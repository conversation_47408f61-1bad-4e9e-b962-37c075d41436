import userEvent from '@testing-library/user-event';

import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import IndexSuggestionImpactBanner, {
  BannerVariant,
} from '@packages/intel/components/performance-advisor/IndexSuggestionImpactBanner';
import { render, screen } from '@packages/react-testing-library';

jest.mock('@packages/common/utils/segmentAnalytics');

describe('@packages/intel/components/performance-advisor/IndexSuggestionImpactBanner', () => {
  const user = userEvent.setup();

  const defaultProps = {
    estimatedImpactBytes: 1234567, // 1.2 MB
    namespace: 'test.db',
    clusterId: 'cluster-123',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders landing page variant with correct text', () => {
    render(<IndexSuggestionImpactBanner {...defaultProps} variant={BannerVariant.LANDING_PAGE} />);

    expect(screen.getByText(/Top Index Suggestion in/)).toBeInTheDocument();
    expect(screen.getByText(/test.db/)).toBeInTheDocument();
    expect(screen.getByText(/Reduce up to/)).toBeInTheDocument();
    expect(screen.getByText(/1.2 MB/)).toBeInTheDocument();

    expect(screen.queryByText(/Expected Impact/)).not.toBeInTheDocument();
  });

  it('renders create indexes variant with correct text', () => {
    render(<IndexSuggestionImpactBanner {...defaultProps} variant={BannerVariant.CREATE_INDEXES} />);

    expect(screen.getByText(/Expected Impact/)).toBeInTheDocument();
    expect(screen.getByText(/Can reduce up to/)).toBeInTheDocument();
    expect(screen.getByText(/1.2 MB/)).toBeInTheDocument();
    expect(screen.getByText(/of disk reads with this index per execution./)).toBeInTheDocument();

    expect(screen.queryByText(/Top Index Suggestion in/)).not.toBeInTheDocument();
  });

  it('renders the expected text when queriesPerHour is provided', () => {
    render(
      <IndexSuggestionImpactBanner {...defaultProps} variant={BannerVariant.CREATE_INDEXES} queriesPerHour={5.4} />
    );

    expect(screen.getByText(/Expected Impact/)).toBeInTheDocument();
    expect(screen.getByText(/Can reduce up to/)).toBeInTheDocument();
    expect(screen.getByText(/1.2 MB/)).toBeInTheDocument();
    expect(screen.getByText(/5.4 queries\/hour with this index per execution./)).toBeInTheDocument();

    expect(screen.queryByText(/Top Index Suggestion in/)).not.toBeInTheDocument();
  });

  it('renders the popover definition when the user hovers over the expected impact text', async () => {
    render(<IndexSuggestionImpactBanner {...defaultProps} variant={BannerVariant.CREATE_INDEXES} />);

    expect(
      screen.queryByText(/Expected impact is calculated from the total data bytes wasted/)
    ).not.toBeInTheDocument();

    await user.hover(screen.getByText(/Expected Impact/));

    expect(
      await screen.findByText(/Expected impact is calculated from the total data bytes wasted/)
    ).toBeInTheDocument();
  });

  it('calls analytics.track with correct arguments for landing page variant', () => {
    render(<IndexSuggestionImpactBanner {...defaultProps} variant={BannerVariant.LANDING_PAGE} />);

    expect(analytics.track).toHaveBeenCalledWith(
      SEGMENT_EVENTS.INDEX_SUGGESTION_IMPACT_BANNER_VIEWED,
      expect.objectContaining({
        context: 'Performance Advisor Landing Page',
        cluster_id: defaultProps.clusterId,
        bytes_wasted: '1.2 MB',
        namespace: defaultProps.namespace,
      })
    );
  });

  it('calls analytics.track with correct arguments for create indexes variant', () => {
    render(<IndexSuggestionImpactBanner {...defaultProps} variant={BannerVariant.CREATE_INDEXES} />);

    expect(analytics.track).toHaveBeenCalledWith(
      SEGMENT_EVENTS.INDEX_SUGGESTION_IMPACT_BANNER_VIEWED,
      expect.objectContaining({
        context: 'Performance Advisor Create Indexes Page',
        cluster_id: defaultProps.clusterId,
        bytes_wasted: '1.2 MB',
        namespace: defaultProps.namespace,
      })
    );
  });
});
