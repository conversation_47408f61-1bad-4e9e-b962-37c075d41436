import { useState } from 'react';

import { css } from '@emotion/react';
import styled from '@emotion/styled';
import Card from '@leafygreen-ui/card';
import { palette } from '@leafygreen-ui/palette';
import { Overline } from '@leafygreen-ui/typography';
import { connect } from 'react-redux';

import { StyledCard as StyledCardType } from '@packages/types/leafygreen-emotion';

import { getActiveGroupId, getActiveOrgId } from '@packages/redux/common/app';
import { hasProjectFeature, isNDSPlan, SettingsState } from '@packages/redux/common/settings';

// analytics
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import {
  CollapsibleArrowIcon,
  CollapsibleContainer,
  CollapsibleHeader,
  CollapsibleHeaderText,
} from '@packages/intel/components/common/Collapsibles';
import IndexSummary from '@packages/intel/components/performance-advisor/shared/IndexSummary';
import InfoTooltip from '@packages/intel/components/performance-advisor/shared/InfoTooltip';
import ShapeCard from '@packages/intel/components/performance-advisor/shared/ShapeCard';
import SuggestedIndexCard from '@packages/intel/components/performance-advisor/shared/SuggestedIndexCard';
import { getNamespace } from '@packages/intel/utils/performanceAdvisorUtils';
import {
  CollectedIndex,
  DeploymentId,
  Feedback,
  IndexSuggestion,
  QueryShapesById,
} from '@packages/project/common/types/indexAdvisor';
import useThemeValue from '@packages/theme/hooks/useThemeValue';

const StyledCard = styled<StyledCardType>(Card)`
  margin-bottom: 20px;
  ${({ color }: { color: string }) => `background-color: ${color};`}};`;

const Header = styled.div({
  fontSize: '14px',
  display: 'flex',
  color: palette.black,
  alignItems: 'center',
  marginBottom: '10px',
  justifyContent: 'space-between',
});

const NamespaceColumnContainer = styled.div({
  display: 'flex',
  flexDirection: 'column',
  gap: '4px',
});

const Namespace = styled.span({
  flex: '1 1 auto',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  color: palette.gray.dark3,
});

const Bold = styled.span({
  fontWeight: 'bold',
});

interface Props {
  minMongoDbVersion?: string;
  filteredCollectedIndexes: Array<CollectedIndex>;
  timespan?: number;
  shapesById: QueryShapesById;
  namespaces: Array<string>;
  index: IndexSuggestion;
  isNDSSharedHost: boolean;
  deploymentId: DeploymentId;
  orgId: string;
  groupId: string;
  hostId: string;
  clusterId?: string;
  onIndexCreation: (isRollingBuild: boolean) => void;
  feedback?: Feedback;
  isAutoIndexingFeatureFlagEnabled: boolean;
  isDismissed?: boolean;
  indexCreationAdviceResponse: any;
  isServerless?: boolean;
  clusterUniqueId?: string;
  allCollections: boolean;
  isCCPA: boolean;

  // @experiment Performance Advisor Impact Clarity | Jira Epic: CLOUDP-318388
  headerBadge?: JSX.Element | null;
  banner?: JSX.Element | null;
  isInImpactClarityTreatment?: boolean;
}

function PerformanceAdvisorCard(props: Props) {
  const [isExistingIndexesExpanded, setExistingIndexesExpanded] = useState(false);
  const [isSampleQueriesExpanded, setSampleQueriesExpanded] = useState(false);

  const {
    filteredCollectedIndexes,
    minMongoDbVersion,
    timespan,
    shapesById,
    namespaces,
    groupId,
    hostId,
    clusterId,
    index,
    deploymentId,
    isNDSSharedHost,
    orgId,
    onIndexCreation,
    feedback,
    isDismissed = false,
    indexCreationAdviceResponse,
    isServerless,
    clusterUniqueId,
    allCollections,
    isCCPA,
    headerBadge = null,
    banner = null,
    isInImpactClarityTreatment = false,
  } = props;

  let existingIndexesInNamespace =
    filteredCollectedIndexes &&
    filteredCollectedIndexes.filter((indexFiltered) => index.namespace === indexFiltered.namespace);

  const onViewExistingIndexes = () => {
    analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
      context: 'Performance Advisor',
      action: 'View Existing Indexes Clicked',
      cluster_id: deploymentId.id,
    });
    if (!!existingIndexesInNamespace.length) {
      setExistingIndexesExpanded(!isExistingIndexesExpanded);
    }
  };

  const onViewSampleQueries = () => {
    analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
      context: 'Performance Advisor',
      action: 'View Sample Query Shapes Clicked',
      cluster_id: deploymentId.id,
    });
    setSampleQueriesExpanded(!isSampleQueriesExpanded);
  };

  const namespace = getNamespace(index.namespace);

  const indexHeader = !!existingIndexesInNamespace.length
    ? `Existing Indexes In This Collection (${existingIndexesInNamespace.length})`
    : `No Existing Indexes In This Collection`;
  const sampleQueriesHeader = `Sample Queries Improved By This Index`;

  const themeValue = useThemeValue();
  const borderColor = themeValue({ light: 'grayLight2', dark: 'grayDark2' });
  const backgroundColor = themeValue({ light: 'grayLight3', dark: 'grayDark4' });

  const SectionDivider = styled.div({
    border: '.5px solid',
    borderColor,
    margin: '12px 0px',
  });

  const ExistingIndexContainer = styled.div({
    padding: '8px 16px',
    backgroundColor,
    border: '1px solid',
    borderColor,
    borderRadius: '3px',
    marginTop: '15px',
  });

  return (
    <StyledCard
      color={isDismissed ? palette.gray.light3 : palette.white}
      data-testid="performance-advisor-card"
      data-dismissed={isDismissed ? true : false}
    >
      {isInImpactClarityTreatment ? (
        <>
          <Header>
            <NamespaceColumnContainer>
              <Overline
                css={css`
                  color: ${palette.gray.dark1};
                `}
              >
                Namespace
              </Overline>
              <Namespace data-testid="performance-advisor-card-namespace">
                {namespace.database}
                <Bold>{namespace.collection}</Bold>
              </Namespace>
            </NamespaceColumnContainer>
            {headerBadge}
          </Header>
          <SectionDivider />
          <div
            css={css`
              margin-bottom: 12px;
            `}
          >
            {banner}
          </div>
        </>
      ) : (
        <Header>
          <Namespace data-testid="performance-advisor-card-namespace">
            {namespace.database}
            <Bold>{namespace.collection}</Bold>
          </Namespace>
        </Header>
      )}
      <SuggestedIndexCard
        index={index}
        minMongoDbVersion={minMongoDbVersion}
        timespan={timespan}
        namespaces={namespaces}
        deploymentId={deploymentId}
        isNDSSharedHost={isNDSSharedHost}
        orgId={orgId}
        groupId={groupId}
        hostId={hostId}
        clusterId={clusterId}
        onIndexCreation={onIndexCreation}
        feedback={feedback}
        indexCreationAdviceResponse={indexCreationAdviceResponse}
        isServerless={isServerless}
        clusterUniqueId={clusterUniqueId}
        existingIndexes={existingIndexesInNamespace}
        isCCPA={isCCPA}
        isInImpactClarityTreatment={isInImpactClarityTreatment}
      />
      <SectionDivider />
      <CollapsibleHeader
        role="button"
        tabIndex={0}
        onClick={onViewExistingIndexes}
        disabled={!existingIndexesInNamespace.length}
        data-testid="toggle-arrow"
      >
        <CollapsibleArrowIcon isExpanded={isExistingIndexesExpanded} />
        <CollapsibleHeaderText>{indexHeader}</CollapsibleHeaderText>
      </CollapsibleHeader>
      <CollapsibleContainer isExpanded={isExistingIndexesExpanded}>
        <ExistingIndexContainer>
          {existingIndexesInNamespace &&
            existingIndexesInNamespace.map((index, i) => {
              return <IndexSummary index={index.index} key={i} />;
            })}
        </ExistingIndexContainer>
      </CollapsibleContainer>
      <SectionDivider />
      <CollapsibleHeader
        role="button"
        tabIndex={0}
        onClick={onViewSampleQueries}
        disabled={false}
        data-testid="toggle-arrow"
      >
        <CollapsibleArrowIcon isExpanded={isSampleQueriesExpanded} />
        <CollapsibleHeaderText>{sampleQueriesHeader}</CollapsibleHeaderText>
        <InfoTooltip>
          Sample of queries that will be improved by creating the index, grouped by query shape.
        </InfoTooltip>
      </CollapsibleHeader>
      <CollapsibleContainer isExpanded={isSampleQueriesExpanded}>
        {index.impact
          .map((shapeId) => shapesById[shapeId])
          .sort((shapeA, shapeB) => {
            return shapeB.inefficiencyScore - shapeA.inefficiencyScore;
          })
          .map(
            (shape, i) =>
              shape && (
                <div key={shape.id} className="performance-advisor-layout-card-container">
                  <ShapeCard
                    shapeIndex={i}
                    operations={shape.operations}
                    count={shape.count}
                    avgMs={shape.avgMs}
                    avgNScanned={shape.avgNScanned}
                    avgNReturned={shape.avgNReturned}
                    avgQueryTargeting={shape.queryTargeting}
                    queryNamespace={shape.namespace}
                    fromCountOrDistinct={shape.fromCountOrDistinct}
                    timespan={timespan}
                    indexNamespace={index.namespace}
                    allCollections={allCollections}
                    queryShapes={shape.queryShapes}
                  />
                </div>
              )
          )}
      </CollapsibleContainer>
    </StyledCard>
  );
}

const mapStateToProps = (state: SettingsState) => ({
  groupId: getActiveGroupId(state),
  isNDSPlan: isNDSPlan(state),
  isAutoIndexingFeatureFlagEnabled: hasProjectFeature(state, 'AUTO_INDEXING'),
  orgId: getActiveOrgId(state),
});

export default connect(mapStateToProps)(PerformanceAdvisorCard);
export { PerformanceAdvisorCard };
