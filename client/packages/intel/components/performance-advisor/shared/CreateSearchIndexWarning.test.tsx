import * as trackingUtil from '@packages/search/atlas-search/tracking';
import CreateSearchIndexWarning from '@packages/intel/components/performance-advisor/shared/CreateSearchIndexWarning';
import { getSearchPageUrl } from '@packages/project/common/util/getSearchPageUrl';
import { fireEvent, render, screen } from '@packages/react-testing-library';
import fakeLocation from '@packages/test-utils/locationMock';

describe('@packages/project/metrics/components/CreateSearchIndexWarning', () => {
  const clickedDataExplorerWarning = jest.spyOn(trackingUtil, 'clickedDataExplorerWarning');
  describe('when rendered', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      render(
        <CreateSearchIndexWarning clusterName="cluster" dbName="db" collName="coll" windowLocation={fakeLocation} />
      );
      fakeLocation.assign = jest.fn();
    });

    it('shows create search index link', () => {
      expect(screen.getByText(/create search index/i)).toBeInTheDocument();
      expect(
        screen.getByRole('img', {
          name: /arrow right icon/i,
        })
      ).toBeInTheDocument();
    });

    it('shows correct warning', () => {
      expect(
        screen.getByRole('img', {
          name: /important with circle icon/i,
        })
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          /adding full-text search to your application\? instead of creating a text index, consider a \. atlas search offers faster performance, more expressive queries, and options to fine-tune relevance\./i
        )
      ).toBeInTheDocument();
    });

    describe('when create search index button is clicked', () => {
      beforeEach(() => {
        fireEvent.click(screen.getByText(/create search index/i));
      });

      it('tracks the click and redirects to search page', () => {
        expect(fakeLocation.assign).toBeCalledWith(getSearchPageUrl('cluster', 'db', 'coll'));
        expect(clickedDataExplorerWarning).toHaveBeenCalled();
      });
    });

    describe('when close button is clicked', () => {
      beforeEach(() => {
        fireEvent.click(screen.getByLabelText(/Close Message/));
      });

      it('hides banner', () => {
        expect(screen.queryByText(/adding full-text search to your application/i)).not.toBeInTheDocument();
      });
    });
  });
});
