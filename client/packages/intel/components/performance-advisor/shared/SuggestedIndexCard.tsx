import { Component } from 'react';

import { css } from '@emotion/react';
import styled from '@emotion/styled';
import Button from '@leafygreen-ui/button';
import InlineDefinition from '@leafygreen-ui/inline-definition';
import { palette } from '@leafygreen-ui/palette';
import { Toast } from '@leafygreen-ui/toast';
import Tooltip from '@leafygreen-ui/tooltip';
import { Body } from '@leafygreen-ui/typography';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'conn... Remove this comment to see the full error message
import { connectBackboneToReact } from 'connect-backbone-to-react';
import parseNamespaceString from 'mongodb-ns';
import _ from 'underscore';

import * as api from '@packages/common/services/api';
import { hoveredSideNavZIndex } from '@packages/common/styles/layoutStyles';
// analytics
import analytics, { SEGMENT_EVENTS, TrackProperties } from '@packages/common/utils/segmentAnalytics';
import CreateIndexModal from '@packages/intel/components/performance-advisor/shared/CreateIndexModal';
import InfoTooltip from '@packages/intel/components/performance-advisor/shared/InfoTooltip';
import SuggestedIndexBox from '@packages/intel/components/performance-advisor/shared/SuggestedIndexBox';
import formatBytes from '@packages/intel/utils/formatBytes';
import { getRoundedMetricPerTimeUnit } from '@packages/intel/utils/performanceAdvisorUtils';
// helpers
import { FREQUENCY } from '@packages/intel/utils/rateFormatters';
import FeedbackTooltip from '@packages/project/common/components/FeedbackTooltip';
import {
  CollectedIndex,
  DeploymentId,
  Feedback,
  IndexSuggestion,
  SubmitFeedbackParams,
} from '@packages/project/common/types/indexAdvisor';

interface Props {
  index: IndexSuggestion;
  minMongoDbVersion?: string;
  timespan?: number;
  deploymentId: DeploymentId;
  isNDSSharedHost: boolean;
  isDataAccess: boolean;
  isPAFeedbackEnabled: boolean;
  isDataExplorerExposed: boolean;
  groupId: string;
  hostId: string;
  clusterId: string;
  namespaces: Array<string>;
  onIndexCreation: (isRollingBuild: boolean) => void;
  currentFullName?: string;
  feedback?: Feedback;
  indexCreationAdviceResponse: any;
  isServerless?: boolean;
  clusterUniqueId?: string;
  existingIndexes?: Array<CollectedIndex>;
  isCCPA: boolean;
  isInImpactClarityTreatment?: boolean;
}

interface State {
  showModal: boolean;
  showThumbsUp: boolean;
  showThumbsDown: boolean;
  isToastOn: boolean;
  submittedFeedback?: Feedback;
  showFeedbackHover?: boolean;
}

const toastStyle = css`
  z-index: ${hoveredSideNavZIndex + 1};
`;

const QueryMetricsHeader = styled(Body)`
  font-weight: 600;
  text-transform: uppercase;
`;

const StyledInlineDefinition = styled(InlineDefinition)`
  text-underline-offset: 4px;
  color: ${palette.gray.dark1};
  text-decoration-color: ${palette.gray.dark1};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 16px;
  margin-top: 8px;
`;

class SuggestedIndexCard extends Component<Props, State> {
  static defaultProps = {
    isDataAccess: false,
    isDataExplorerExposed: false,
    isPAFeedbackEnabled: false,
  };

  state: Readonly<State> = {
    showModal: false,
    showThumbsUp: true,
    showThumbsDown: true,
    isToastOn: false,
    submittedFeedback: this.props.feedback,
  };

  onModalClose = () => {
    this.setState({ showModal: false });
  };

  onClickInfo = () => {
    const { deploymentId } = this.props;
    analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
      context: 'Performance Advisor',
      action: 'Index Info Clicked',
      cluster_id: deploymentId.id,
    });
    this.setState({ showModal: true });
  };

  renderFeedbackButtons = () => {
    const { showThumbsDown, showThumbsUp, submittedFeedback } = this.state;
    const { feedback } = this.props;

    let tooltipFeedback = submittedFeedback;
    let feedbackHelpful;
    if (feedback) {
      tooltipFeedback = feedback;
      feedbackHelpful = feedback.helpful;
    }

    return feedback ? (
      <>
        <FeedbackTooltip
          feedback={tooltipFeedback}
          onSubmitFeedbackClick={this.onSubmitFeedbackClick}
          thumbsUp={feedbackHelpful}
          toggleToastOn={this.toggleToastOn}
          setHoverEnabled={this.setFeedbackHover}
          hoverEnabled={this.state.showFeedbackHover}
        />
      </>
    ) : (
      <>
        {showThumbsUp && (
          <FeedbackTooltip
            feedback={tooltipFeedback}
            onSubmitFeedbackClick={this.onSubmitFeedbackClick}
            thumbsUp
            toggleToastOn={this.toggleToastOn}
            setHoverEnabled={this.setFeedbackHover}
            hoverEnabled={this.state.showFeedbackHover}
          />
        )}
        {showThumbsDown && (
          <FeedbackTooltip
            feedback={tooltipFeedback}
            onSubmitFeedbackClick={this.onSubmitFeedbackClick}
            toggleToastOn={this.toggleToastOn}
            setHoverEnabled={this.setFeedbackHover}
            hoverEnabled={this.state.showFeedbackHover}
          />
        )}
      </>
    );
  };

  renderCreateIndexButton = () => {
    const { isDataAccess, isDataExplorerExposed, existingIndexes } = this.props;
    const maxIndexesReached = existingIndexes && existingIndexes?.length >= 64;

    let button = (
      <Button
        className="suggested-index-card-info-button-enabled"
        variant="primary"
        size="xsmall"
        onClick={this.onClickInfo}
        tabIndex={0}
      >
        Create Index
      </Button>
    );

    let disabledReason = 'You are not authorized to create indexes because';

    if (!isDataAccess && !isDataExplorerExposed) {
      disabledReason += ' Data Explorer is disabled and a Project Owner has not granted data access read permissions.';
    } else if (!isDataAccess) {
      disabledReason += ' a Project Owner has not granted data access read permissions.';
    } else if (!isDataExplorerExposed) {
      disabledReason += ' Data Explorer is disabled.';
    } else if (maxIndexesReached) {
      disabledReason = 'You have reached the maximum number of indexes for this collection (64). ';
    } else {
      disabledReason = '';
    }

    if (!isDataAccess || !isDataExplorerExposed || maxIndexesReached) {
      button = (
        <div className="suggested-index-card-info-button-enabled">
          <Tooltip
            data-testid="create-button-tooltip"
            css={{ maxWidth: '315px' }}
            trigger={
              <div>
                <Button
                  disabled
                  className="suggested-index-card-create-index-button"
                  variant="primary"
                  size="xsmall"
                  tabIndex={0}
                >
                  Create Index
                </Button>
              </div>
            }
            justify="middle"
          >
            <div>
              {maxIndexesReached ? (
                <div className="suggested-index-card-max-indexes">
                  {disabledReason}
                  <a href="https://www.mongodb.com/docs/manual/reference/limits/#:~:text=A%20single%20collection%20can%20have%20no%20more%20than%2064%20indexes.&text=Starting%20in%20version%204.2%2C%20MongoDB%20removes%20the%20Index%20Name%20Length,to%20%224.2%22%20or%20greater">
                    Learn More.
                  </a>
                </div>
              ) : (
                disabledReason
              )}
            </div>
          </Tooltip>
        </div>
      );
    }
    return button;
  };

  toggleToastOn = () => {
    const { isToastOn } = this.state;
    this.setState({ isToastOn: !isToastOn });
  };

  setFeedbackHover = (state?: boolean) => {
    this.setState({ showFeedbackHover: state });
  };

  renderThankYouToast = () => {
    const { isToastOn } = this.state;
    return (
      <Toast
        variant="success"
        css={toastStyle}
        title="Thanks! Feedback sent."
        open={isToastOn}
        onClose={this.toggleToastOn}
      />
    );
  };

  onSubmitFeedbackClick = (helpful: boolean, comment: string) => {
    const { index, currentFullName, groupId, hostId, clusterId, indexCreationAdviceResponse, deploymentId, isCCPA } =
      this.props;

    const saveFeedback = isCCPA ? api.performanceAdvisor.saveFeedback : api.performanceAdvisor.saveFeedbackHostLevel;

    const submitFeedbackParams: SubmitFeedbackParams = {
      groupId,
      hostId,
      clusterId,
      body: {
        namespace: index.namespace,
        suggestedIndex: index.index,
        suggestedIndexId: index.id,
        performanceAdvisorResponse: JSON.stringify(_.omit(indexCreationAdviceResponse, ['feedback'])),
        feedback: { helpful, comment },
      },
    };

    saveFeedback(submitFeedbackParams).then(({ feedbackId, dateSubmitted }) => {
      analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        context: 'Performance Advisor',
        action: 'Feedback provided',
        feedbackId,
        projectId: groupId,
        helpful,
        comment,
        cluster_id: deploymentId.id,
      } as TrackProperties);

      this.setState({
        submittedFeedback: {
          suggestedIndex: index.index,
          helpful,
          comment,
          namespace: index.namespace,
          user: currentFullName || '',
          date: dateSubmitted,
        },
        showThumbsDown: !helpful,
        showThumbsUp: helpful,
      });
    });
  };

  render() {
    const {
      index,
      timespan,
      minMongoDbVersion,
      deploymentId,
      isNDSSharedHost,
      onIndexCreation,
      namespaces,
      groupId,
      hostId,
      isPAFeedbackEnabled,
      isServerless,
      clusterUniqueId,
      isInImpactClarityTreatment,
    } = this.props;

    const {
      count,
      avgMs,
      avgNScanned,
      avgNReturned,
      queryTargeting,
      sortInMemoryCount,
      fromCountOrDistinct,
      avgObjSize,
      namespace,
    } = index;
    const { showModal } = this.state;
    const countPerHour = getRoundedMetricPerTimeUnit({
      count,
      timespanInMs: timespan,
      timeUnit: FREQUENCY.H,
    });
    const inMemorySortPerHour = getRoundedMetricPerTimeUnit({
      count: sortInMemoryCount,
      timespanInMs: timespan,
      timeUnit: FREQUENCY.H,
    });

    let avgObjSizeFormatted;
    if (avgObjSize) {
      avgObjSizeFormatted = formatBytes(avgObjSize);
    } else {
      avgObjSizeFormatted = 'N/A';
    }

    const formattedNamespace = parseNamespaceString(namespace);
    let createIndexModalElement;

    if (showModal) {
      createIndexModalElement = (
        <CreateIndexModal
          id={deploymentId.id}
          idType={deploymentId.type}
          databaseName={formattedNamespace.database}
          collectionName={formattedNamespace.collection}
          groupId={groupId}
          hostId={hostId}
          namespaces={namespaces}
          onClose={this.onModalClose}
          isVisible={showModal}
          isTenantCluster={isNDSSharedHost}
          minMongoDbVersion={minMongoDbVersion}
          index={index.index}
          isPerformanceAdvisor
          onSuccess={onIndexCreation}
          isServerless={isServerless}
          clusterUniqueId={clusterUniqueId}
          // TODO: Pass through clusterDescription in CLOUDP-191791
          clusterDescription={undefined}
          trackContext="Performance Advisor - Suggested Index Card"
        />
      );
    }

    return (
      <div className="suggested-index-card">
        {this.renderThankYouToast()}
        {createIndexModalElement}
        <SuggestedIndexBox index={index.index}>
          <div className="suggested-index-card-actions-padding">
            {isPAFeedbackEnabled && !isServerless && this.renderFeedbackButtons()}
            {this.renderCreateIndexButton()}
          </div>
        </SuggestedIndexBox>
        <div className="suggested-index-card-stats">
          {isInImpactClarityTreatment ? (
            <>
              <QueryMetricsHeader>Current Query Metrics</QueryMetricsHeader>
              <StatsGrid>
                <div>
                  <StyledInlineDefinition definition="The average amount of time it takes to run this query">
                    Avg. Execution Time
                  </StyledInlineDefinition>
                  <div className="suggested-index-card-stats-value">{avgMs} ms</div>
                </div>
                <div>
                  <StyledInlineDefinition definition="The number of times a query has been run">
                    Execution Count
                  </StyledInlineDefinition>
                  <div className="suggested-index-card-stats-value">
                    {countPerHour < 0.01 ? '< 0.01' : `${countPerHour}`}/hour
                  </div>
                </div>
                <div>
                  <StyledInlineDefinition definition="The average ratio of documents scanned to documents returned by this query. A higher ratio indicates higher inefficiency of the query">
                    Avg. Query Targeting
                  </StyledInlineDefinition>
                  <div className="suggested-index-card-stats-value">{fromCountOrDistinct ? 'N/A' : queryTargeting}</div>
                </div>
                {!isServerless && (
                  <div>
                    <StyledInlineDefinition definition="Average size of document that was examined">
                      Avg. Object Size
                    </StyledInlineDefinition>
                    <div className="suggested-index-card-stats-value">{avgObjSizeFormatted}</div>
                  </div>
                )}
                <div>
                  <StyledInlineDefinition definition="Average number of documents that were examined by this query">
                    Avg. Docs Scanned
                  </StyledInlineDefinition>
                  <div className="suggested-index-card-stats-value">{avgNScanned}</div>
                </div>
                <div>
                  <StyledInlineDefinition definition="Average number of documents that were returned by this query">
                    Avg. Docs Returned
                  </StyledInlineDefinition>
                  <div className="suggested-index-card-stats-value">{avgNReturned}</div>
                </div>
                {inMemorySortPerHour >= 0 && (
                  <div>
                    <StyledInlineDefinition definition="Number of operations per hour that need to be sorted in memory">
                      In Memory Sort
                    </StyledInlineDefinition>
                    <div>{inMemorySortPerHour < 0.01 ? '< 0.01' : `${inMemorySortPerHour}`} ops/hr</div>
                  </div>
                )}
              </StatsGrid>
            </>
          ) : (
            <>
              <div className="suggested-index-card-stats-header">Queries Improved by this index</div>
              <div className="suggested-index-card-stats-stat">
                <div className="suggested-index-card-stats-description">Execution Count</div>
                <div className="suggested-index-card-stats-value">
                  {countPerHour < 0.01 ? '< 0.01' : `${countPerHour}`}/hour
                </div>
              </div>
              <div className="suggested-index-card-stats-stat">
                <div className="suggested-index-card-stats-description">Avg. Execution Time</div>
                <div className="suggested-index-card-stats-value">{avgMs} ms</div>
              </div>
              <div className="suggested-index-card-stats-stat">
                <div className="suggested-index-card-stats-description">
                  Avg. Query Targeting
                  <InfoTooltip>
                    The average documents scanned to documents returned ratio of queries improved by the index.
                  </InfoTooltip>
                </div>
                <div className="suggested-index-card-stats-value">{fromCountOrDistinct ? 'N/A' : queryTargeting}</div>
              </div>
              {inMemorySortPerHour >= 0 && (
                <div className="suggested-index-card-stats-stat">
                  <div className="suggested-index-card-stats-description">
                    In Memory Sort
                    <InfoTooltip>
                      Number of operations per hour improved by this index that needed to be sorted in memory.
                    </InfoTooltip>
                  </div>
                  <div className="suggested-index-card-stats-value">
                    {inMemorySortPerHour < 0.01 ? '< 0.01' : `${inMemorySortPerHour}`} ops/hr
                  </div>
                </div>
              )}
              <div className="suggested-index-card-stats-stat">
                <div className="suggested-index-card-stats-description-small">Avg. Docs Scanned</div>
                <div className="suggested-index-card-stats-value">{avgNScanned}</div>
              </div>
              <div className="suggested-index-card-stats-stat">
                <div className="suggested-index-card-stats-description-small">Avg. Docs Returned</div>
                <div className="suggested-index-card-stats-value">{avgNReturned}</div>
              </div>
              {!isServerless && (
                <div className="suggested-index-card-stats-stat-obj">
                  <div className="suggested-index-card-stats-description">Avg. Object Size</div>
                  <div className="suggested-index-card-stats-value">{avgObjSizeFormatted}</div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  }
}

function mapModelsToProps({ settingsModel }: $TSFixMe) {
  return {
    isDataAccess: settingsModel.isDataAccessAdmin(),
    isDataExplorerExposed: settingsModel.isDataExplorerExposed(),
    isPAFeedbackEnabled: settingsModel.isPAFeedbackEnabled(),
    currentFullName: `${settingsModel.get('FIRST_NAME')} ${settingsModel.get('LAST_NAME')}`,
  };
}

export default connectBackboneToReact(mapModelsToProps)(SuggestedIndexCard);
export { SuggestedIndexCard };
