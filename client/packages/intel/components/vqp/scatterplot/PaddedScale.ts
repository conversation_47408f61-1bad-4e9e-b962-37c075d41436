// @ts-expect-error TS(7016): Could not find a declaration file for module 'd3-s... Remove this comment to see the full error message
import { type ScaleContinuousNumeric, scaleLinear, type ScaleTime } from 'd3-scale';

class PaddedScale implements PaddedScale {
  _backingScale: ScaleContinuousNumeric<number, number> | ScaleTime<number, number>;
  _innerDomain: Array<number> | Array<Date>;
  _range: Array<number>;
  _padding: Array<number>;

  constructor(
    backingScale: ScaleContinuousNumeric<number, number> | ScaleTime<number, number> = scaleLinear()
      .domain([0, 1])
      .range([0, 100]),
    padding: Array<number> = [15, 0]
  ) {
    this._backingScale = backingScale;
    this._padding = padding;
    this._innerDomain = this._backingScale.domain();
    this._range = this._backingScale.range();
  }

  rescale(): PaddedScale {
    // backingScale = domain -> padded range
    this._backingScale.domain(this._innerDomain);
    this._backingScale.range([this._range[0] + this._padding[0], this._range[1] - this._padding[1]]);

    // backingScale = padded domain -> non-padded range
    const outerDomain = [this._backingScale.invert(this._range[0]), this._backingScale.invert(this._range[1])];
    this._backingScale.domain(outerDomain);
    this._backingScale.range(this._range);

    return this;
  }

  scale(x: number): number {
    return this._backingScale(x);
  }

  invert(x: number) {
    return this._backingScale.invert(x);
  }

  domain(): Array<number> | Array<Date>;
  domain(value: Array<number> | Array<Date>): PaddedScale;
  domain(value?: Array<number> | Array<Date>): PaddedScale | Array<number> | Array<Date> {
    if (value === undefined) return this._backingScale.domain();

    this._innerDomain = value;
    return this.rescale();
  }

  range(): Array<number>;
  range(value: Array<number>): PaddedScale;
  range(value?: Array<number>): PaddedScale | Array<number> {
    if (value === undefined) return this._backingScale.range();

    this._range = value;
    return this.rescale();
  }

  padding(): Array<number>;
  padding(value: Array<number>): PaddedScale;
  padding(value?: Array<number>): Array<number> | PaddedScale {
    if (value === undefined) return this._padding;

    this._padding = value;
    return this.rescale();
  }

  backingScale(): ScaleContinuousNumeric<number, number> | ScaleTime<number, number>;
  backingScale(scale: ScaleContinuousNumeric<number, number> | ScaleTime<number, number>): PaddedScale;
  backingScale(
    scale?: ScaleContinuousNumeric<number, number> | ScaleTime<number, number>
  ): ScaleContinuousNumeric<number, number> | ScaleTime<number, number> | PaddedScale {
    if (scale === undefined) return this._backingScale;

    this._backingScale = scale;
    return this.rescale();
  }

  clamp(): boolean;
  clamp(x: boolean): PaddedScale;
  clamp(x?: boolean) {
    if (x === undefined) return this._backingScale.clamp();

    this._backingScale.clamp(x);
    return this.rescale();
  }

  copy(): PaddedScale {
    return new PaddedScale(this._backingScale.copy(), this._padding).domain(this._innerDomain).range(this._range);
  }
}

export default PaddedScale;
