import Button from '@leafygreen-ui/button';
import { Spinner } from '@leafygreen-ui/loading-indicator';
import Tooltip from '@leafygreen-ui/tooltip';

import { popoverZIndex } from '@packages/common/styles/layoutStyles';

interface QueryShapeActionButtonProps {
  /** Whether this is a block (true) or unblock (false) action */
  isBlockAction: boolean;
  /** Whether the operation is currently in progress */
  isOperating: boolean;
  /** Whether the user has permission to perform this action */
  hasPermission: boolean;
  /** Click handler for the button */
  onClick: () => void;
  /** Optional data-testid for testing */
  'data-testid'?: string;
}

/**
 * Reusable button component for query shape block/unblock actions
 * Handles permissions, loading states, and conditional styling
 */
const QueryShapeActionButton = ({
  isBlockAction,
  isOperating,
  hasPermission,
  onClick,
  'data-testid': testId,
}: QueryShapeActionButtonProps) => {
  const actionText = isBlockAction ? 'BLOCK' : 'UNBLOCK';
  const operatingText = isBlockAction ? 'BLOCKING' : 'UNBLOCKING';
  const tooltipMessage = `You don't have permission to ${isBlockAction ? 'block' : 'unblock'} query shapes, please reach out to the Project Owner to request the 'Project Data Access Admin' Role.`;

  const button = (
    <Button
      variant={isBlockAction ? 'dangerOutline' : 'default'}
      size="default"
      onClick={onClick}
      disabled={isOperating || !hasPermission}
      data-testid={testId}
    >
      {isOperating ? (
        <>
          <Spinner /> {operatingText} QUERY SHAPE
        </>
      ) : (
        `${actionText} QUERY SHAPE`
      )}
    </Button>
  );

  if (hasPermission) {
    return button;
  }

  return (
    <Tooltip
      align="top"
      justify="middle"
      triggerEvent="hover"
      enabled={true}
      popoverZIndex={popoverZIndex}
      trigger={<div>{button}</div>}
    >
      {tooltipMessage}
    </Tooltip>
  );
};

export default QueryShapeActionButton;
