import mongoDate from '@packages/date';

export enum FREQUENCY {
  MS = 'ms',
  S = 's',
  MINUTE = 'minute',
  H = 'h',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

function getRateAs(count: $TSFixMe, msDuration: number | undefined, frequency: FREQUENCY) {
  const duration = mongoDate.duration(msDuration || 0); // mongoDate handles undefined as 0 anyway, but we might as well make TS happy
  const unitDuration = duration.as(frequency);
  return {
    value: count / unitDuration,
    frequency,
  };
}

function variableRateFormatter(count: $TSFixMe, msDuration: number | undefined) {
  return Object.values(FREQUENCY).reduce((acceptableRate, frequency) => {
    if (acceptableRate) {
      return acceptableRate;
    }

    const currRate = getRateAs(count, msDuration, frequency);
    if (currRate.value >= 1) {
      return currRate;
    }

    // eslint-disable-next-line eqeqeq
    if (frequency === 'year' && acceptableRate == undefined) {
      return currRate;
    }

    return undefined;
  }, undefined);
}

export { getRateAs, variableRateFormatter };
