import { Component } from 'react';

import { Permission, PermissionResult, ResourceType } from '@packages/types/auth/fga';

import { render, screen } from '@packages/react-testing-library';

import { useAuthz } from './useAuthz';
import { withAuthz } from './withAuthz';

jest.mock('./useAuthz', () => {
  return { __esModule: true, useAuthz: jest.fn() };
});
const mockUseAuthz = useAuthz as jest.MockedFunction<typeof useAuthz>;

const requiredPermissions: Array<Permission> = [
  {
    action: 'something.thing1.read',
    resourceType: ResourceType.ORGANIZATION,
    resourceInstanceId: '123',
  },
];

const getPermissionResponse = (
  isAuthorized: boolean
): { permissions: Array<PermissionResult>; isAuthorized: boolean; isLoading: boolean } => ({
  permissions: requiredPermissions.map((permission) => ({ isAuthorized, permission })),
  isAuthorized,
  isLoading: false,
});

// This utility is mainly meant to be used with class componenets that can't
// use functional components. Thus using class component in tests.
class TestComponentClass extends Component<{ isAuthorized: boolean; permissions: Array<PermissionResult> }> {
  render() {
    return (
      <>
        {/* Check isAuthorized: */}
        {this.props.isAuthorized ? (
          <div data-testid="authorized">Authorized</div>
        ) : (
          <div data-testid="unauthorized">Unauthorized</div>
        )}

        {/* Check permissions: */}
        <ul>
          {this.props.permissions.map((permission, idx) => (
            <li data-testid={permission.permission.action} key={idx}>
              {permission.isAuthorized}
            </li>
          ))}
        </ul>
      </>
    );
  }
}

describe('@packages/authz/withAuthz', function () {
  it('should call useAuthz', async function () {
    mockUseAuthz.mockReturnValue(getPermissionResponse(true));
    const AuthzEnhancedComponent = withAuthz(TestComponentClass, requiredPermissions);
    render(<AuthzEnhancedComponent />);
    expect(mockUseAuthz).toHaveBeenCalled();
  });

  it('should add false authz decision to props', function () {
    mockUseAuthz.mockReturnValue(getPermissionResponse(false));
    const AuthzEnhancedComponent = withAuthz(TestComponentClass, requiredPermissions);
    render(<AuthzEnhancedComponent />);
    expect(screen.getByTestId('unauthorized')).toBeInTheDocument();
  });

  it('should add true authz decision to props', function () {
    mockUseAuthz.mockReturnValue(getPermissionResponse(true));
    const AuthzEnhancedComponent = withAuthz(TestComponentClass, requiredPermissions);
    render(<AuthzEnhancedComponent />);
    expect(screen.getByTestId('authorized')).toBeInTheDocument();
  });

  it('should add permissions results to props', function () {
    mockUseAuthz.mockReturnValue(getPermissionResponse(true));
    const AuthzEnhancedComponent = withAuthz(TestComponentClass, requiredPermissions);
    render(<AuthzEnhancedComponent />);
    expect(screen.getByTestId(requiredPermissions[0].action)).toBeInTheDocument();
  });
});
