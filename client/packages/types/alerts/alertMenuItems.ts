import { AlertConfigType } from '@packages/types/alerts/alertConfigs/AlertConfig/AlertConfigType';
import { Scope } from '@packages/types/alerts/Scope';
import { GroupType } from '@packages/types/groups';

export interface MenuItem {
  value: AlertConfigType;
  groupTypes?: Array<GroupType>;
  scopes?: Array<Scope>;
  name?: string;
  active?: boolean;
}

export const globalMenuOptions: Array<MenuItem> = [
  { value: AlertConfigType.HOST },
  { value: AlertConfigType.REPLICA_SET },
  { value: AlertConfigType.CLUSTER },
  { value: AlertConfigType.AGENT, groupTypes: [GroupType.CLOUD] },
  { value: AlertConfigType.BACKUP, groupTypes: [GroupType.CLOUD] },
  { value: AlertConfigType.CPS_BACKUP, groupTypes: [GroupType.NDS] },
  { value: AlertConfigType.BI_CONNECTOR },
  // USER type alerts are all informational, do not show it for global alerts
  { value: AlertConfigType.USER, scopes: [Scope.Group] },
  { value: AlertConfigType.STREAMS, groupTypes: [GroupType.NDS], scopes: [Scope.Group] },
  { value: AlertConfigType.GROUP },
  { value: AlertConfigType.BILLING, groupTypes: [GroupType.CLOUD, GroupType.NDS] },
  { value: AlertConfigType.NDS, groupTypes: [GroupType.NDS], scopes: [Scope.Group, Scope.Global] },
  {
    value: AlertConfigType.NDS_CLOUD_PROVIDER_LIMITS,
    groupTypes: [GroupType.NDS],
    scopes: [Scope.Global],
  },
  {
    value: AlertConfigType.ENCRYPTION_KEY,
    groupTypes: [GroupType.NDS],
    scopes: [Scope.Group, Scope.Global],
  },
  {
    value: AlertConfigType.NDS_X509_USER_AUTHENTICATION,
    groupTypes: [GroupType.NDS],
    scopes: [Scope.Group],
  },
  { value: AlertConfigType.MONGOT, groupTypes: [GroupType.NDS], scopes: [Scope.Global] },
  { value: AlertConfigType.SERVERLESS, groupTypes: [GroupType.NDS], scopes: [Scope.Global] },
  { value: AlertConfigType.FLEX_METRIC, groupTypes: [GroupType.NDS], scopes: [Scope.Global] },
  { value: AlertConfigType.ONLINE_ARCHIVE, groupTypes: [GroupType.NDS], scopes: [Scope.Global] },
  { value: AlertConfigType.CRASH_LOG, groupTypes: [GroupType.NDS], scopes: [Scope.Global] },
  {
    value: AlertConfigType.CORRUPTION_DETECTION,
    groupTypes: [GroupType.NDS],
    scopes: [Scope.Global],
  },
  { value: AlertConfigType.LOG_DOWNLOAD_STARTED_ALERT_CONFIG, groupTypes: [GroupType.CLOUD] },
  {
    value: AlertConfigType.NDS_AUTO_SCALING_AUDIT,
    groupTypes: [GroupType.NDS],
    scopes: [Scope.Group, Scope.Global],
  },
];

export const systemMenuOptions: Array<MenuItem> = [
  {
    value: AlertConfigType.SYSTEM_MAAS,
    name: 'System MaaS',
    scopes: [Scope.System],
  },
];
