import { Alert } from '@packages/types/alerts/alerts';

export enum MongotHostAlertEventType {
  HOST_MONGOT_CRASHING_OOM = 'HOST_MONGOT_CRASHING_OOM',
  HOST_MONGOT_STOP_REPLICATION = 'HOST_MONGOT_STOP_REPLICATION',
  HOST_MONGOT_APPROACHING_STOP_REPLICATION = 'HOST_MONGOT_APPROACHING_STOP_REPLICATION',
  HOST_DISK_SPACE_INSUFFICIENT_FOR_SEARCH_INDEX_REBUILD = 'HOST_DISK_SPACE_INSUFFICIENT_FOR_SEARCH_INDEX_REBUILD',
}

export interface MongotHostAlert extends Alert {
  et: MongotHostAlertEventType;
  ndsClusterName: string;
}
