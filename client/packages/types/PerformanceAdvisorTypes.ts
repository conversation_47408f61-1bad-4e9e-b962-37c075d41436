import { TimeWindow } from './QueryProfilerInterfaces';

export enum AdviceTitle {
  createIndexes = 'Create Indexes',
  dropIndexes = 'Drop Indexes',
  improveSchemas = 'Improve Schemas',
  overview = 'Performance Advisor',
  leverageDataServices = 'Leverage Data Platform',
}

export interface AdviceSummary {
  count?: number;
  suggestion: string;
}

export interface Namespace {
  database: string;
  collection: string;
}

export interface Zoom {
  timeWindow: TimeWindow;
  timeFrame: string; // TODO(CLOUDP-195318): use TimeFrame enum
}
