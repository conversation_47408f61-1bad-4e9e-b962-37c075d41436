export type ProcessType = 'MONGOD_REPLICA' | 'MONGOD_STANDALONE' | 'MONGOS' | 'CONFIG_SERVER';

export type StatusType = 'IN_PROGRESS' | 'WAITING' | 'ERROR' | 'GOAL_STATE' | 'NOT_STARTED';

export interface Step {
  step: string;
  status: string;
  description: string;
}

export interface Move {
  move: string;
  steps: ReadonlyArray<Step>;
}

export interface AutomationStatus {
  name: string;
  cluster: string;
  replicaset: string;
  hostnamePort: string;
  processType: ProcessType;
  timeSinceStatus: number;
  workingOn: string;
  workingOnShort: string;
  statusType: StatusType;
  status: string;
  errorText: string;
  moves: ReadonlyArray<Move>;
}
