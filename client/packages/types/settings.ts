export interface Organization {
  id: string;
  hasVercelIntegration: boolean;
  hasActiveVercelIntegration: boolean;
  isFineGrainedAuthEnabled: boolean;
  isRestrictedByUiAccessList: boolean;
  multiFactorAuthRequired: boolean;
  name: string;
  numOwners: number;
  planType: string;
  roles: Array<string>;
}

export interface SetGroupFlagParams {
  /**
   * ID of the group to have its flag set
   */
  groupId: string;
  /**
   * New value for flag
   */
  isEnabled: boolean;
}
