export interface MemberInfo {
  name: string;
  isDeploying?: boolean;
  metricsUrl?: string;
  isDegraded?: boolean;
  isUnavailable?: boolean;
  isHidden?: boolean;
  isMonitoringPaused?: boolean;
  hostId?: string;
  // typeof Region | string
  region?: any;
  regionPriority?: number;
  replicationSpecId?: string;
  replicaState?: string;
  isWarming?: boolean;
  diskState?: string;
}

export interface ZoneInfo {
  name: string;
  members: Array<MemberInfo>;
  priority: number;
}
