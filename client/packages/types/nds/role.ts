export interface InheritedRole {
  role: string;
  db: string;
  scope: Scope;
}

export interface Resource {
  db: string;
  collection: string;
  applyToAllDbs: boolean;
  applyToAllCollections: boolean;
}

export interface ResourceView {
  db: string;
  collection: string;
  cluster: boolean;
}
export interface Action {
  action: string;
  resources: Array<ResourceView>;
  scope: Scope;
}

export interface CustomRole {
  roleName: string;
  actions: Array<Action>;
  inheritedRoles: Array<InheritedRole>;
  scope: Scope;
  minimumMongoVersion: string | null;
}

export interface BuiltInRole {
  roleName?: string;
  scope?: Scope;
}

export enum Scope {
  DATABASE = 'DATABASE',
  COLLECTION = 'COLLECTION',
  GLOBAL = 'GLOBAL',
  NONE = '',
}

export enum InheritedRoleType {
  USER = 'USER',
  ATLAS = 'ATLAS',
  NONE = 'NONE',
}
