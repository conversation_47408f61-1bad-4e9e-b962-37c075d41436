import { BackingCloudProviderName, CloudProviderName } from './provider';

export enum AWSRegionName {
  US_EAST_1 = 'US_EAST_1',
  US_EAST_2 = 'US_EAST_2',
  US_WEST_1 = 'US_WEST_1',
  US_WEST_2 = 'US_WEST_2',
  CA_CENTRAL_1 = 'CA_CENTRAL_1',
  EU_NORTH_1 = 'EU_NORTH_1',
  EU_WEST_1 = 'EU_WEST_1',
  EU_WEST_2 = 'EU_WEST_2',
  EU_WEST_3 = 'EU_WEST_3',
  EU_CENTRAL_1 = 'EU_CENTRAL_1',
  EU_CENTRAL_2 = 'EU_CENTRAL_2',
  SA_EAST_1 = 'SA_EAST_1',
  AP_EAST_1 = 'AP_EAST_1',
  AP_SOUTHEAST_2 = 'AP_SOUTHEAST_2',
  AP_SOUTHEAST_3 = 'AP_SOUTHEAST_3',
  AP_SOUTHEAST_4 = 'AP_SOUTHEAST_4',
  AP_NORTHEAST_1 = 'AP_NORTHEAST_1',
  AP_NORTHEAST_2 = 'AP_NORTHEAST_2',
  AP_NORTHEAST_3 = 'AP_NORTHEAST_3',
  AP_SOUTHEAST_1 = 'AP_SOUTHEAST_1',
  AP_SOUTH_1 = 'AP_SOUTH_1',
  AP_SOUTH_2 = 'AP_SOUTH_2',
  CN_NORTH_1 = 'CN_NORTH_1',
  CN_NORTHWEST_1 = 'CN_NORTHWEST_1',
  ME_CENTRAL_1 = 'ME_CENTRAL_1',
  ME_SOUTH_1 = 'ME_SOUTH_1',
  AF_SOUTH_1 = 'AF_SOUTH_1',
  EU_SOUTH_1 = 'EU_SOUTH_1',
  EU_SOUTH_2 = 'EU_SOUTH_2',
  IL_CENTRAL_1 = 'IL_CENTRAL_1',
  CA_WEST_1 = 'CA_WEST_1',
  AP_SOUTHEAST_5 = 'AP_SOUTHEAST_5',
  AP_SOUTHEAST_7 = 'AP_SOUTHEAST_7',
  MX_CENTRAL_1 = 'MX_CENTRAL_1',
  US_GOV_WEST_1 = 'US_GOV_WEST_1',
  US_GOV_EAST_1 = 'US_GOV_EAST_1',
}

export enum GCPRegionName {
  EASTERN_US = 'EASTERN_US',
  US_EAST_4 = 'US_EAST_4',
  US_EAST_5 = 'US_EAST_5',
  US_WEST_2 = 'US_WEST_2',
  US_WEST_3 = 'US_WEST_3',
  US_WEST_4 = 'US_WEST_4',
  US_SOUTH_1 = 'US_SOUTH_1',
  CENTRAL_US = 'CENTRAL_US',
  WESTERN_US = 'WESTERN_US',
  NORTH_AMERICA_NORTHEAST_1 = 'NORTH_AMERICA_NORTHEAST_1',
  NORTH_AMERICA_NORTHEAST_2 = 'NORTH_AMERICA_NORTHEAST_2',
  NORTH_AMERICA_SOUTH_1 = 'NORTH_AMERICA_SOUTH_1',
  SOUTH_AMERICA_EAST_1 = 'SOUTH_AMERICA_EAST_1',
  SOUTH_AMERICA_WEST_1 = 'SOUTH_AMERICA_WEST_1',
  EUROPE_CENTRAL_2 = 'EUROPE_CENTRAL_2',
  WESTERN_EUROPE = 'WESTERN_EUROPE',
  EUROPE_NORTH_1 = 'EUROPE_NORTH_1',
  EUROPE_WEST_2 = 'EUROPE_WEST_2',
  EUROPE_WEST_3 = 'EUROPE_WEST_3',
  EUROPE_WEST_4 = 'EUROPE_WEST_4',
  EUROPE_WEST_6 = 'EUROPE_WEST_6',
  EUROPE_WEST_8 = 'EUROPE_WEST_8',
  EUROPE_WEST_9 = 'EUROPE_WEST_9',
  EUROPE_WEST_10 = 'EUROPE_WEST_10',
  EUROPE_WEST_12 = 'EUROPE_WEST_12',
  EUROPE_SOUTHWEST_1 = 'EUROPE_SOUTHWEST_1',
  MIDDLE_EAST_CENTRAL_1 = 'MIDDLE_EAST_CENTRAL_1',
  MIDDLE_EAST_CENTRAL_2 = 'MIDDLE_EAST_CENTRAL_2',
  MIDDLE_EAST_WEST_1 = 'MIDDLE_EAST_WEST_1',
  AFRICA_SOUTH_1 = 'AFRICA_SOUTH_1',
  AUSTRALIA_SOUTHEAST_1 = 'AUSTRALIA_SOUTHEAST_1',
  AUSTRALIA_SOUTHEAST_2 = 'AUSTRALIA_SOUTHEAST_2',
  EASTERN_ASIA_PACIFIC = 'EASTERN_ASIA_PACIFIC',
  NORTHEASTERN_ASIA_PACIFIC = 'NORTHEASTERN_ASIA_PACIFIC',
  ASIA_NORTHEAST_2 = 'ASIA_NORTHEAST_2',
  ASIA_NORTHEAST_3 = 'ASIA_NORTHEAST_3',
  SOUTHEASTERN_ASIA_PACIFIC = 'SOUTHEASTERN_ASIA_PACIFIC',
  ASIA_EAST_2 = 'ASIA_EAST_2',
  ASIA_SOUTHEAST_2 = 'ASIA_SOUTHEAST_2',
  ASIA_SOUTH_1 = 'ASIA_SOUTH_1',
  ASIA_SOUTH_2 = 'ASIA_SOUTH_2',
  EASTERN_US_AW = 'EASTERN_US_AW',
  US_EAST_4_AW = 'US_EAST_4_AW',
  US_EAST_5_AW = 'US_EAST_5_AW',
  US_WEST_2_AW = 'US_WEST_2_AW',
  US_WEST_3_AW = 'US_WEST_3_AW',
  US_WEST_4_AW = 'US_WEST_4_AW',
  US_SOUTH_1_AW = 'US_SOUTH_1_AW',
  CENTRAL_US_AW = 'CENTRAL_US_AW',
  WESTERN_US_AW = 'WESTERN_US_AW',
}

export enum AzureRegionName {
  US_CENTRAL = 'US_CENTRAL',
  US_EAST = 'US_EAST',
  US_EAST_2 = 'US_EAST_2',
  US_NORTH_CENTRAL = 'US_NORTH_CENTRAL',
  US_WEST = 'US_WEST',
  US_SOUTH_CENTRAL = 'US_SOUTH_CENTRAL',
  EUROPE_NORTH = 'EUROPE_NORTH',
  EUROPE_WEST = 'EUROPE_WEST',
  US_WEST_CENTRAL = 'US_WEST_CENTRAL',
  US_WEST_2 = 'US_WEST_2',
  US_WEST_3 = 'US_WEST_3',
  CANADA_EAST = 'CANADA_EAST',
  CANADA_CENTRAL = 'CANADA_CENTRAL',
  BRAZIL_SOUTH = 'BRAZIL_SOUTH',
  BRAZIL_SOUTHEAST = 'BRAZIL_SOUTHEAST',
  AUSTRALIA_EAST = 'AUSTRALIA_EAST',
  AUSTRALIA_SOUTH_EAST = 'AUSTRALIA_SOUTH_EAST',
  AUSTRALIA_CENTRAL = 'AUSTRALIA_CENTRAL',
  AUSTRALIA_CENTRAL_2 = 'AUSTRALIA_CENTRAL_2',
  UAE_NORTH = 'UAE_NORTH',
  GERMANY_WEST_CENTRAL = 'GERMANY_WEST_CENTRAL',
  GERMANY_NORTH = 'GERMANY_NORTH',
  SWITZERLAND_NORTH = 'SWITZERLAND_NORTH',
  SWITZERLAND_WEST = 'SWITZERLAND_WEST',
  SWEDEN_CENTRAL = 'SWEDEN_CENTRAL',
  SWEDEN_SOUTH = 'SWEDEN_SOUTH',
  UK_SOUTH = 'UK_SOUTH',
  UK_WEST = 'UK_WEST',
  INDIA_CENTRAL = 'INDIA_CENTRAL',
  INDIA_WEST = 'INDIA_WEST',
  INDIA_SOUTH = 'INDIA_SOUTH',
  ASIA_EAST = 'ASIA_EAST',
  JAPAN_EAST = 'JAPAN_EAST',
  JAPAN_WEST = 'JAPAN_WEST',
  ASIA_SOUTH_EAST = 'ASIA_SOUTH_EAST',
  KOREA_CENTRAL = 'KOREA_CENTRAL',
  KOREA_SOUTH = 'KOREA_SOUTH',
  FRANCE_CENTRAL = 'FRANCE_CENTRAL',
  FRANCE_SOUTH = 'FRANCE_SOUTH',
  SOUTH_AFRICA_NORTH = 'SOUTH_AFRICA_NORTH',
  SOUTH_AFRICA_WEST = 'SOUTH_AFRICA_WEST',
  NORWAY_EAST = 'NORWAY_EAST',
  NORWAY_WEST = 'NORWAY_WEST',
  UAE_CENTRAL = 'UAE_CENTRAL',
  QATAR_CENTRAL = 'QATAR_CENTRAL',
  POLAND_CENTRAL = 'POLAND_CENTRAL',
  ISRAEL_CENTRAL = 'ISRAEL_CENTRAL',
  ITALY_NORTH = 'ITALY_NORTH',
  SPAIN_CENTRAL = 'SPAIN_CENTRAL',
  MEXICO_CENTRAL = 'MEXICO_CENTRAL',
  NEW_ZEALAND_NORTH = 'NEW_ZEALAND_NORTH',
}

export const RegionNames = {
  ...AWSRegionName,
  ...GCPRegionName,
  ...AzureRegionName,
} as const;

export const Continent = {
  EMEA: 'EMEA',
  EUROPE: 'Europe',
  AMERICAS: 'Americas',
  NORTH_AMERICA: 'North America',
  SOUTH_AMERICA: 'South America',
  APAC: 'APAC',
  ASIA: 'Asia',
  AUSTRALIA: 'Australia',
  MIDDLE_EAST: 'Middle East',
  AFRICA: 'Africa',
} as const;

export type ContinentName = (typeof Continent)[keyof typeof Continent];

export enum RegionNamesNone {
  UNSELECTED = '',
}

export type RegionName = keyof typeof RegionNames | RegionNamesNone.UNSELECTED;

export enum AWSEnv {
  GOV_CLOUD = 'Gov Cloud',
  STANDARD = 'Standard',
}

export enum ComplianceLevel {
  COMMERCIAL = 'COMMERCIAL',
  US_GOV = 'US_GOV',
}

export interface Region {
  awsEnv?: AWSEnv;
  complianceLevel: ComplianceLevel;
  continent: ContinentName;
  isRecommended: boolean;
  key: RegionName;
  latitude?: number;
  location: string;
  longitude?: number;
  name: string;
  provider: BackingCloudProviderName;
  gcpRegionCode?: string;
}

export interface RegionByInstance {
  availableFamilies: Array<string>;
  isBlacklisted: boolean;
  providerName: CloudProviderName;
  regionName: string;
}

export const CANADIAN_REGIONS: Array<RegionName> = [
  RegionNames.CA_CENTRAL_1,
  RegionNames.CANADA_EAST,
  RegionNames.CANADA_CENTRAL,
  RegionNames.NORTH_AMERICA_NORTHEAST_1,
  RegionNames.NORTH_AMERICA_NORTHEAST_2,
];

export const EUROPEAN_CONTINENT_KEYS: ReadonlyArray<ContinentName> = [Continent.EMEA, Continent.EUROPE];

export const AMERICA_CONTINENT_KEYS: ReadonlyArray<ContinentName> = [
  Continent.AMERICAS,
  Continent.SOUTH_AMERICA,
  Continent.NORTH_AMERICA,
];

export const ASIA_CONTINENT_KEYS: ReadonlyArray<ContinentName> = [Continent.ASIA, Continent.APAC];

export const AUSTRALIA_CONTINENT_KEYS: ReadonlyArray<ContinentName> = [Continent.AUSTRALIA];

export type ContinentGrouping = 'Americas' | 'EMEA' | 'APAC';
/**
 * When adding a templated read-only node, we need to be able to tell which general areas of the world already have
 * coverage. For now at least, we're only getting as specific as Americas, EMEA, or APAC, and don't need to be more
 * precise. Also, GCP regions don't have more specific names. This mapping allows us to keep track of which region
 * is in which larger area of the world.
 */
export const normalizedContinentNames: Record<ContinentName, ContinentGrouping> = {
  Americas: 'Americas',
  'North America': 'Americas',
  'South America': 'Americas',
  APAC: 'APAC',
  Australia: 'APAC',
  Asia: 'APAC',
  EMEA: 'EMEA',
  Africa: 'EMEA',
  Europe: 'EMEA',
  'Middle East': 'EMEA',
};

/**
 * These regions are taken from https://cloud.google.com/sustainability/region-carbon, if you notice any discrepancies between here and the list on Google's site, please let Atlas Growth know
 */
export const LOW_CARBON_GCP_REGIONS = [
  'europe-north1', // Finland
  'europe-southwest1', // Madrid
  'europe-west1', // Belgium
  'europe-west2', // London
  'europe-west3', // Frankfurt
  'europe-west4', // Netherlands
  'europe-west6', // Zurich
  'europe-west9', // Paris
  'europe-west10', // Berlin
  'northamerica-northeast1', // Montreal
  'northamerica-northeast2', // Toronto
  'southamerica-east1', // Sao Paulo
  'southamerica-west1', // Santiago
  'us-central1', // Iowa
  'us-south1', // Dallas
  'us-west1', // Oregon
] as const;

export type LowCarbonGcpRegion = (typeof LOW_CARBON_GCP_REGIONS)[number];

/**
 * These regions are taken from https://sustainability.aboutamazon.com/products-services/aws-cloud?energyType=true#carbon-free-energy, if you notice any discrepancies between here and the list on Amazon's site, please let Atlas Growth know.
 */
export const LOW_CARBON_AWS_REGIONS = [
  'us-east-1', // N. Virginia
  'us-gov-east-1', // Gov US-East
  'us-east-2', // Ohio
  'us-west-2', // Oregon
  'us-gov-west-1', // Gov US-West
  'us-west-1', // N. California
  'ca-central-1', // Central Canada
  'ca-west-1', // West Canada
  'eu-west-1', // Ireland
  'eu-central-1', // Frankfurt
  'eu-west-2', // London
  'eu-south-1', // Milan
  'eu-west-3', // Paris
  'eu-north-1', // Stockholm
  'eu-south-2', // Spain
  'eu-central-2', // Zurich
  'ap-south-1', // Mumbai
  'ap-south-2', // Hyderabad
  'ap-northeast-3', // Osaka
  'ap-northeast-1', // Tokyo
  'cn-north-1', // Beijing
  'cn-northwest-1', // Ningxia
] as const;

export type LowCarbonAwsRegion = (typeof LOW_CARBON_AWS_REGIONS)[number];
