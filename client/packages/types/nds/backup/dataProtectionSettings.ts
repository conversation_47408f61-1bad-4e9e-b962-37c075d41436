import { PolicyItem } from './backupJob';

/**
 * Maps to DataProtectionSettingsView.
 */
export interface DataProtectionSettings {
  authorizedEmail: string;
  authorizedUserFirstName: string;
  authorizedUserLastName: string;
  copyProtectionEnabled: boolean;
  encryptionAtRestEnabled: boolean;
  onDemandPolicyItem?: PolicyItem;
  pitEnabled: boolean;
  projectId: string;
  restoreWindowDays: number;
  scheduledPolicyItems: Array<PolicyItem>;
  state: 'ACTIVE' | 'ENABLING' | 'UPDATING' | 'DISABLING';
  updatedDate: Date;
  updatedUser: string;
  markedForDisablementDate?: Date;
}
