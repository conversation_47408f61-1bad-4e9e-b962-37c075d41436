import { CloudProvider } from '@packages/types/nds/provider';
import { RegionName } from '@packages/types/nds/region';

/**
 * A single backup job. Maps to BackupJobView.
 */
export interface BackupJob {
  autoExportEnabled: boolean;
  clusterName: string;
  clusterType: 'REPLICA_SET' | 'SHARDED';
  copySettings: Array<CopySetting>;
  diskBackupState: 'ACTIVE' | 'PAUSED' | 'RETAINING' | 'TERMINATING';
  export?: { frequencyType: BackupFrequencyType; exportBucketId: string };
  extraRetentionSettings?: ExtraRetentionSetting;
  id: string;
  lastOplogTerm?: number;
  lastOplogTs?: { increment: number; time: number };
  lastSnapshotDate: Date | string;
  nextSnapshotDate: Date | string;
  policies: Array<Policy>;
  projectId: string;
  provider: CloudProvider;
  referenceTimeInMins: number;
  restoreWindowDays: number;
  useOrgAndGroupNamesInExportPrefix: boolean;
}

/**
 * A backup copy setting. Maps to CopySettingView.
 */
export interface CopySetting {
  cloudProvider: CloudProvider;
  frequencies: Array<BackupFrequencyType>;
  regionName: RegionName;
  replicationSpecId: string;
  zoneId: string;
  shouldCopyOplogs: boolean;
}

/**
 * Backup extra retention setting. Maps to ExtraRetentionSettingView.
 */
export interface ExtraRetentionSetting {
  frequencyType: BackupFrequencyType;
  retentionDays: number;
}

export type BackupFrequencyType = 'MINUTELY' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | 'ON_DEMAND';

/**
 * Maps to com.xgen.cloud.cps.backupjob._public.ui.PolicyView.
 */
export interface Policy {
  id: string;
  policyItems: Array<PolicyItem>;
}

/**
 * Maps to com.xgen.cloud.cps.backupjob._public.ui.PolicyItemView.
 */
export interface PolicyItem {
  id: string;
  frequencyType: BackupFrequencyType;
  frequencyInterval: number;
  retention: number;
  retentionUnit: BackupRetentionUnit;
}

export type BackupRetentionUnit = 'DAYS' | 'WEEKS' | 'MONTHS' | 'YEARS';
