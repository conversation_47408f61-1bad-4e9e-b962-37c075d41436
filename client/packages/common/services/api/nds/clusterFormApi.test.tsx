// stubbed dependencies
import fetchWrapper from '@packages/common/services/api/fetchWrapper';
// source file
import clusterFormApi from '@packages/common/services/api/nds/clusterFormApi';

jest.mock('@packages/common/services/api/fetchWrapper');
(fetchWrapper as jest.Mock).mockImplementation(() => {
  return Promise.resolve({
    json: () => {
      return Promise.resolve();
    },
  });
});

jest.mock('@packages/common/utils/abTestUtil');

describe('@packages/common/services/api/nds/clusterFormApi', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDefaultTemplates', () => {
    it('calls fetchWrapper once per provider', async () => {
      await clusterFormApi.getDefaultTemplates('testId');
      expect(fetchWrapper).toHaveBeenCalledTimes(3);
    });
  });

  describe('getProviderOptions', () => {
    it('calls fetchWrapper once per provider + FREE + Flex', async () => {
      await clusterFormApi.getProviderOptions('testId');
      expect(fetchWrapper).toHaveBeenCalledTimes(5);
    });
  });
});
