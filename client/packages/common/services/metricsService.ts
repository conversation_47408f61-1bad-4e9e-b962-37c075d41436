import $ from 'jquery';
import _ from 'underscore';

import * as groupIdFromPath from '@packages/common/utils/groupIdFromPath';
import jqueryErrorHandler from '@packages/common/utils/jqueryErrorHandler';

export default {
  loadMetricTypesForHost({
    groupId,
    hostId,
    opcountersChartType,
  }: {
    groupId: string;
    hostId: string;
    opcountersChartType?: string;
  }): PromiseLike<$TSFixMe> {
    const url = `/metrics/v1/groups/${groupId}/hosts/${hostId}`;
    const data = { opcountersChartType };
    return $.ajax({ dataType: 'json', url, data }).then(_.identity, jqueryErrorHandler);
  },

  loadMetricsForHost({
    groupId,
    hostId,
    metricGroup,
    metricId,
    resolution,
    duration,
    bucketed,
    since,
    until,
    aggregator,
    databaseName,
    opcountersChartType,
  }) {
    const data = {
      granularity: resolution || undefined,
      retention: duration,
      bucketed: !!bucketed,
      opcountersChartType,
      since,
      until,
      aggregator,
    };

    // The endpoint does not follow the same pattern for 'dbstats', so we
    // explicitly set it to the correct API path.
    if (metricGroup === 'dbstats') {
      metricGroup = 'databases/storage';
      (data as $TSFixMe).databaseName = databaseName;
    }

    let url = `/metrics/v1/groups/${groupId}/hosts/${hostId}/${metricGroup}`;

    if (metricId) {
      url += `/${metricId}`;
    }

    return Promise.resolve(
      $.ajax({
        data,
        dataType: 'json',
        url,
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  loadHostEvents({ groupId, duration, hostId, since, until }) {
    const url = `/metrics/v1/groups/${groupId}/hosts/${hostId}/annotations`;

    return Promise.resolve(
      $.ajax({
        dataType: 'json',
        url,
        data: { duration, since, until },
      }).then((data) => {
        return data.timestamps.map((annotationStartTime, i) => {
          return {
            start: annotationStartTime,
            type: data.types[i],
          };
        });
      }, jqueryErrorHandler)
    );
  },

  saveSelectedChartList({ metricGroup, selectedCharts }) {
    return Promise.resolve(
      $.ajax({
        url: `/host/hostChartsSelect/${groupIdFromPath.get()}/${metricGroup}`,
        method: 'post',
        dataType: 'json',
        contentType: 'application/json',
        data: JSON.stringify({ selectedCharts }),
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  saveHostEventsEnabled(hostEventsEnabled) {
    return Promise.resolve(
      $.ajax({
        url: `/settings/changeDisplayChartAnnotations/${hostEventsEnabled}`,
        method: 'post',
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  loadZooms(groupId = groupIdFromPath.get()) {
    return Promise.resolve(
      $.ajax({
        url: `/metrics/v1/groups/${groupId}/zooms`,
      }).then((response) => response.zooms, jqueryErrorHandler)
    );
  },

  loadLockPercentForDB({ groupId, hostId, resolution, duration, databaseName, since, until }) {
    const granularity = resolution || undefined;
    const retention = duration;

    return Promise.resolve(
      $.ajax({
        url: `/metrics/v1/groups/${groupId}/hosts/${hostId}/databases/locks`,
        data: {
          granularity,
          retention,
          since,
          until,
          databaseNames: databaseName,
        },
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  saveDefaultChartGranularity(granularity) {
    return Promise.resolve(
      $.ajax({
        type: 'POST',
        url: `/settings/updateDefaultChartGranularity/${granularity}`,
        dataType: 'json',
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  saveDefaultChartZoom(zoom) {
    return Promise.resolve(
      $.ajax({
        type: 'POST',
        url: `/settings/updateDefaultChartZoom/${zoom}`,
        dataType: 'json',
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  loadDefaultMetricIds({ metricGroup, groupId, hostId }) {
    return Promise.resolve(
      $.ajax({
        url: `/v2/host/detail/${metricGroup}/defaultChartIds/${groupId}/${hostId}`,
        dataType: 'json',
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  emailChart(data) {
    return Promise.resolve(
      $.ajax({
        type: 'POST',
        url: `/metrics/v1/groups/${groupIdFromPath.get()}/email`,
        data,
      }).then(_.identity, jqueryErrorHandler)
    );
  },

  getHostPartitions(hostId, groupId = groupIdFromPath.get()) {
    return Promise.resolve(
      $.ajax({
        url: `/host/detail/partitions/${groupId}/${hostId}`,
        dataType: 'json',
      }).then((response) => response.partitions, jqueryErrorHandler)
    );
  },

  openChartExportWindow({
    type,
    groupId,
    hostId,
    format,
    timeZoneId,
    resolution,
    duration,
    getCurrentDb,
    since,
    until,
  }) {
    const url = `/export/v2/charts/${type}/${groupId}/${hostId}/${format}/${encodeURIComponent(timeZoneId)}`;

    const granularity = resolution || undefined;
    const retention = duration;
    const data = { granularity, retention, since, until };

    const db = getCurrentDb();
    if (db) {
      (data as $TSFixMe).hostDbStatsName = db.name;
    }

    const param = Object.keys(data)
      .filter((key) => !!data[key])
      .reduce((memo, key) => {
        memo[key] = data[key];
        return memo;
      }, {});

    window.open(`${url}?${$.param(param)}`, '_blank');
  },

  setDisplayOpcountersOnSeparateCharts(isOnSeparateCharts) {
    return $.ajax({
      type: 'POST',
      url: `/settings/updateSeparateOpcounterCharts/${isOnSeparateCharts}`,
    }).then(_.identity, jqueryErrorHandler);
  },

  loadServerlessMetrics({
    groupId,
    uniqueId,
    metricId,
    resolution,
    duration,
    bucketed,
    since,
    until,
    aggregator,
    opcountersChartType,
  }) {
    const data = {
      granularity: resolution || undefined,
      retention: duration,
      bucketed: !!bucketed,
      opcountersChartType,
      since,
      until,
      aggregator,
    };

    let url = `/metrics/v1/groups/${groupId}/serverless/${uniqueId}`;

    if (metricId) {
      url += `/metrics/${metricId}`;
    }

    return Promise.resolve(
      $.ajax({
        data,
        dataType: 'json',
        url,
      }).then(_.identity, jqueryErrorHandler)
    );
  },
};
