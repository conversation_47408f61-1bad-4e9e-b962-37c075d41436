import SearchDeployment, {
  SearchNode,
  SearchNodes,
  SearchPartitionGroup,
  SearchPartitionGroups,
} from '@packages/common/models/SearchDeployment';

describe('@packages/common/models/SearchDeployment', function () {
  describe('getHostNames', () => {
    it('returns a list of host names', () => {
      const searchPartitionGroup1 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname1', port: 'port1' }),
          new SearchNode({ hostname: 'aaahostname2', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup2 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId2',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'bbbhostname3', port: 'port1' }),
          new SearchNode({ hostname: 'hostname4', port: 'port1' }),
          new SearchNode({ hostname: 'zhostname5', port: 'port1' }),
        ]),
      });

      const searchDeployment = new SearchDeployment({
        partitionGroups: new SearchPartitionGroups([searchPartitionGroup1, searchPartitionGroup2]),
      });

      expect(searchDeployment.getHostNames()).toEqual([
        'hostname1',
        'aaahostname2',
        'bbbhostname3',
        'hostname4',
        'zhostname5',
      ]);
    });
  });
  describe('getOneNodePerPartition', () => {
    it('picks alphabetically first host name from each partition group', () => {
      const searchPartitionGroup1 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname1', port: 'port1' }),
          new SearchNode({ hostname: 'aaahostname2', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup2 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId2',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'bbbhostname3', port: 'port1' }),
          new SearchNode({ hostname: 'hostname4', port: 'port1' }),
          new SearchNode({ hostname: 'zhostname5', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup3 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId2',
        nodes: new SearchNodes([]),
      });

      const searchDeployment = new SearchDeployment({
        partitionGroups: new SearchPartitionGroups([
          searchPartitionGroup1,
          searchPartitionGroup2,
          searchPartitionGroup3,
        ]),
      });

      const result = searchDeployment.getOneNodePerPartition();
      expect(result.length).toEqual(2);
      expect(result[0].getHostname()).toEqual('aaahostname2');
      expect(result[1].getHostname()).toEqual('bbbhostname3');
    });
  });
  describe('getPartitionGroupsByReplicaSetId', () => {
    it('returns a list of partition groups', () => {
      const searchPartitionGroup1 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId1',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname1', port: 'port1' }),
          new SearchNode({ hostname: 'hostname2', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup2 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId2',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname3', port: 'port1' }),
          new SearchNode({ hostname: 'hostname4', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup3 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId1',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname5', port: 'port1' }),
          new SearchNode({ hostname: 'hostname6', port: 'port1' }),
        ]),
      });

      const searchDeployment = new SearchDeployment({
        partitionGroups: new SearchPartitionGroups([
          searchPartitionGroup1,
          searchPartitionGroup2,
          searchPartitionGroup3,
        ]),
      });

      expect(searchDeployment.getPartitionGroupsByReplicaSetId('replicaSetId1')).toEqual([
        searchPartitionGroup1,
        searchPartitionGroup3,
      ]);
      expect(searchDeployment.getPartitionGroupsByReplicaSetId('replicaSetId2')).toEqual([searchPartitionGroup2]);
    });
  });
  describe('getNodesFromPartitionGroups', () => {
    it('returns a list of nodes', () => {
      const searchPartitionGroup1 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId1',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname1', port: 'port1' }),
          new SearchNode({ hostname: 'hostname2', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup2 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId2',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname3', port: 'port1' }),
          new SearchNode({ hostname: 'hostname4', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup3 = new SearchPartitionGroup({
        replicaSetHardwareId: 'replicaSetId1',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname5', port: 'port1' }),
          new SearchNode({ hostname: 'hostname6', port: 'port1' }),
        ]),
      });

      const searchDeployment = new SearchDeployment({
        partitionGroups: new SearchPartitionGroups([
          searchPartitionGroup1,
          searchPartitionGroup2,
          searchPartitionGroup3,
        ]),
      });

      expect(searchDeployment.getNodesFromPartitionGroups('replicaSetId1')).toEqual(
        [searchPartitionGroup1.getNodes(), searchPartitionGroup3.getNodes()].flat()
      );
      expect(searchDeployment.getNodesFromPartitionGroups('replicaSetId2')).toEqual(
        [searchPartitionGroup2.getNodes()].flat()
      );
    });
  });
  describe('getOneNodePerHostCluster', () => {
    it('returns a list of nodes one per host cluster', () => {
      const searchPartitionGroup1 = new SearchPartitionGroup({
        hostClusterId: 'hostClusterId1',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname1', port: 'port1' }),
          new SearchNode({ hostname: 'hostname2', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup2 = new SearchPartitionGroup({
        hostClusterId: 'hostClusterId2',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname3', port: 'port1' }),
          new SearchNode({ hostname: 'hostname4', port: 'port1' }),
        ]),
      });

      const searchPartitionGroup3 = new SearchPartitionGroup({
        hostClusterId: 'hostClusterId3',
        nodes: new SearchNodes([
          new SearchNode({ hostname: 'hostname5', port: 'port1' }),
          new SearchNode({ hostname: 'hostname6', port: 'port1' }),
        ]),
      });

      const searchDeployment = new SearchDeployment({
        partitionGroups: new SearchPartitionGroups([
          searchPartitionGroup1,
          searchPartitionGroup2,
          searchPartitionGroup3,
        ]),
      });

      expect(searchDeployment.getOneNodePerHostCluster()).toEqual([
        searchPartitionGroup1.getNodes().at(0),
        searchPartitionGroup2.getNodes().at(0),
        searchPartitionGroup3.getNodes().at(0),
      ]);
    });
  });
});
