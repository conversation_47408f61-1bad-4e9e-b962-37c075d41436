import Types from '@packages/types/clusterCard';
import { Card } from '@packages/types/deployment/processes';

export function getCards(): Array<Card> {
  return [
    {
      name: 'cluster1',
      isSslEnabled: false,
      isAuthEnabled: false,
      isManaged: true,
      sizeInBytes: 100,
      automationId: 'automationID',
      versions: ['3.2.0'],
      type: Types.ShardedCluster,
      shards: [
        {
          nodes: 3,
          name: 'shard1',
        },
        {
          nodes: 3,
          name: 'shard2',
        },
        {
          nodes: 3,
          name: 'shard3',
        },
      ],
      biConnectors: 0,
      mongoses: 3,
      configServers: 3,
      primaryHostIds: ['a', 'b', 'c'],
      shutdownNodes: 0,
      shutdownModeRollup: 'NONE',
      manualNodes: 1,
      manualModeRollup: 'PARTLY',
      availability: 'AVAILABLE',
      diffSummary: null,
      isContinuousBackupEnabled: false,
      isKmipEnabled: false,
      isShutdown: false,
      manualMode: false,
      monitoringId: 'monitoringaaaa',
      versionOld: false,
      hasStartupWarnings: false,
    },
    {
      name: 'rs1',
      isSslEnabled: false,
      isAuthEnabled: false,
      isManaged: false,
      sizeInBytes: 100,
      automationId: 'automationID2',
      versions: ['3.4.0'],
      type: Types.ReplicaSet,
      biConnectors: 0,
      members: 3,
      primaryHostIds: ['a'],
      shutdownNodes: 3,
      shutdownModeRollup: 'ALL',
      manualNodes: 0,
      manualModeRollup: 'NONE',
      availability: 'AVAILABLE',
      diffSummary: null,
      isContinuousBackupEnabled: false,
      isKmipEnabled: false,
      isShutdown: true,
      manualMode: false,
      monitoringId: 'monitoringbbbb',
      versionOld: false,
      hasStartupWarnings: false,
    },
    {
      name: 'rs2',
      isSslEnabled: false,
      monitoringId: null,
      isManaged: false,
      sizeInBytes: 100,
      automationId: 'automationID3',
      versions: ['3.4.0'],
      type: Types.ReplicaSet,
      biConnectors: 0,
      members: 3,
      primaryHostIds: ['a'],
      shutdownNodes: 1,
      shutdownModeRollup: 'PARTLY',
      manualNodes: 1,
      manualModeRollup: 'PARTLY',
      availability: 'AVAILABLE',
      diffSummary: null,
      isContinuousBackupEnabled: false,
      isKmipEnabled: false,
      isShutdown: false,
      manualMode: false,
      versionOld: false,
      isAuthEnabled: false,
      hasStartupWarnings: false,
    },
  ];
}
