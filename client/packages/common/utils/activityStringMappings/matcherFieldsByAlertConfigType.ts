import { AlertConfigType } from '@packages/types/alerts/alertConfigs/AlertConfig/AlertConfigType';

export const matcherFieldsByAlertConfigType: Partial<Record<AlertConfigType, Record<string, string>>> = {
  [AlertConfigType.HOST]: {
    hostname: 'Hostname',
    port: 'Host Port',
    hostnameAndPort: 'Hostname And Port',
    replicaSetId: 'Replica Set',
  },
  [AlertConfigType.REPLICA_SET]: {
    // If adding a field for NDS, note specific Atlas behavior in ReplicaSetAlertCheck.getMatchObjectForCluster
    replicaSetName: 'Replica Set Name',
    shardName: 'Shard Name',
    clusterName: 'Cluster Name',
  },
  [AlertConfigType.CLUSTER]: {
    clusterName: 'Cluster Name',
  },
  [AlertConfigType.SERVERLESS]: {
    clusterName: 'Serverless Instance Name',
  },
  [AlertConfigType.FLEX_METRIC]: {
    clusterName: 'Flex Cluster Name',
  },
  [AlertConfigType.REALM_METRIC]: {
    appId: 'Application ID',
  },
  [AlertConfigType.STREAMS]: {
    instanceName: 'Stream Processing Instance',
    processorName: 'Stream Processor Name',
  },
  [AlertConfigType.LOG_INGESTION]: {
    ruleId: 'Rule Id',
  },
  [AlertConfigType.CPS_BACKUP]: {
    storageSystem: 'Storage System',
  },
};
