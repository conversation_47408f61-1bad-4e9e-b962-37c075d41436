import chai from 'chai';
import sinonChai from 'sinon-chai';

import {
  getPlanDescription,
  isDeveloperSupportPlan,
  isSalesSoldPlan,
  isSelfServeSupportPlan,
} from '@packages/common/utils/planUtils';

const expect = chai.expect;

chai.use(sinonChai);

describe('getPlanDescription', function () {
  it('returns a string describing the support drawdown minimum and percentage', function () {
    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 40,
        entitlementsDrawdownMinimumCents: 23456,
      })
    )
      .to.have.string('40%')
      .and.to.have.string('$235');
  });

  it('returns a description when only entitlements percentage is set', function () {
    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 20,
        entitlementsDrawdownMinimumCents: 0,
      })
    ).to.equal('Monthly charges will be based on 20% of Atlas service usage.');
  });

  it('returns a description when only entitlements minimum is set', function () {
    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 0,
        entitlementsDrawdownMinimumCents: 100000,
      })
    ).to.equal('Monthly charges will be a minimum of $1,000.');
  });

  it('returns an empty description when only entitlements minimum and percent are 0', function () {
    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: false,
        entitlementsDrawdownPercentage: 0,
        entitlementsDrawdownMinimumCents: 0,
      })
    ).to.equal('');
  });

  it('returns tiered description when tiered pricing is set', function () {
    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        hasTieredSubscriptionPricing: true,
        entitlementsDrawdownPercentage: 50,
        entitlementsDrawdownMinimumCents: 150000,
        planType: 'NDS_PLATINUM',
      })
    ).to.equal(
      'Includes end-to-end support for mission critical workloads and unlimited usage of enterprise features. Monthly charges are based on the greater of $1,500 or a usage-based fee that follows a tiered pricing model. See your invoice for more details.'
    );
  });

  it('returns a string describing the included features', function () {
    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 40,
        entitlementsDrawdownMinimumCents: 23456,
        planType: 'NDS_PRO',
      })
    ).to.have.string('end-to-end support');

    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 40,
        entitlementsDrawdownMinimumCents: 23456,
        planType: 'NDS_ENTERPRISE',
      })
    ).to.have.string('enterprise features');

    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 40,
        entitlementsDrawdownMinimumCents: 23456,
        planType: 'NDS_PLATINUM',
      })
    ).to.have.string('enterprise features');

    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 40,
        entitlementsDrawdownMinimumCents: 23456,
        planType: 'NDS_PLATINUM',
      })
    ).to.have.string('enterprise features');

    expect(
      // @ts-expect-error ts-migrate(2345) FIXME: Argument of type '{ entitlementsDrawdownPercentage... Remove this comment to see the full error message
      getPlanDescription({
        hasAtlasSubscriptionUpliftOrMinimum: true,
        entitlementsDrawdownPercentage: 40,
        entitlementsDrawdownMinimumCents: 23456,
      })
    )
      .to.not.have.string('enterprise features')
      .and.to.not.have.string('MongoDB Compass');
  });
});

describe('isSelfServeSupportPlan', function () {
  it('returns the correct boolean when passed in different plan types', function () {
    expect(isSelfServeSupportPlan('NDS_DEVELOPER')).to.equal(true);
    expect(isSelfServeSupportPlan('NDS_PREMIER')).to.equal(true);
    expect(isSelfServeSupportPlan('NDS_ENTERPRISE')).to.equal(false);
    expect(isSelfServeSupportPlan('NDS_PRO')).to.equal(false);
  });
});

describe('isDeveloperSupportPlan', function () {
  it('returns the correct boolean when passed in different plan types', function () {
    expect(isDeveloperSupportPlan('NDS_DEVELOPER')).to.equal(true);
    expect(isDeveloperSupportPlan('NDS_PREMIER')).to.equal(false);
  });
});

describe('isSalesSoldPlan', function () {
  it(' returns the correct boolean when passed in different plan types', function () {
    expect(isSalesSoldPlan('NDS_DEVELOPER')).to.equal(false);
    expect(isSalesSoldPlan('NDS')).to.equal(false);
    expect(isSalesSoldPlan('NDS_ENTERPRISE')).to.equal(true);
    expect(isSalesSoldPlan('NDS_PLATINUM')).to.equal(true);
    expect(isSalesSoldPlan('NDS_PRO')).to.equal(true);
    expect(isSalesSoldPlan('UNKOWN')).to.equal(false);
  });
});
