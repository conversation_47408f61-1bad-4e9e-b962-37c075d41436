import Deferred from '@packages/common/utils/Deferred';

/**
 * A function that returns a promise that will be resolved after ms
 * milliseconds via setTimeout
 */

const whenTimeout = function (ms) {
  const deferred = new Deferred();
  setTimeout(() => {
    // @ts-expect-error ts-migrate(2554) FIXME: Expected 1 arguments, but got 0.
    deferred.resolve();
  }, ms);
  return deferred;
};

export default whenTimeout;
