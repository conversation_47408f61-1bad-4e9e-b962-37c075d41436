import _ from 'underscore';

import { MongoDBMajorVersion } from '@packages/types/nds/mongoVersion';

export interface MongoDbVersion {
  major: number;
  minor: number;
  update: number;
  tag: string;
}

export const MONGOSYNC_MIN_SUPPORTED_MONGODB60_VERSION = '6.0.13';

/**
 * Parse mongodb version string into:
 * { major: 2, minor:6 }
 */
export function parse(versionString: string): MongoDbVersion {
  const tokens = tokenizeVersionString(versionString);
  const updateVersion: number = parseInt(tokens[2], 10);

  return {
    major: parseInt(tokens[0], 10),
    minor: parseInt(tokens[1], 10),
    update: isNaN(updateVersion) ? 0 : updateVersion,
    tag: tokens.slice(3).join('-'),
  };
}

function tokenizeVersionString(versionString: string): Array<string> {
  const tokens = versionString.split(/[.-]/);
  if (tokens.length < 2) {
    throw new Error(`Unable to parse MongoDB Version: ${versionString}`);
  }
  return tokens;
}

function versionHasMaintenanceSpecified(versionString: string): boolean {
  const tokens = tokenizeVersionString(versionString);
  const updateVersion: number = parseInt(tokens[2], 10);
  return !isNaN(updateVersion);
}

function parseRCVersion(tag: string): number | null {
  const captureGroupName = 'version';
  const capturingRegex = new RegExp(`rc(?<${captureGroupName}>\\d+)`, 'i');
  const versionString = tag.match(capturingRegex)?.groups?.[captureGroupName];
  return versionString === undefined ? null : parseInt(versionString);
}

function compareRCVersions(rc1: number | null, rc2: number | null) {
  if (rc1 === null && rc2 === null) {
    return 0;
  }
  // assume the non-rc version is greater (this may not always be correct and require a future update)
  if (rc1 === null) {
    return 1;
  }
  if (rc2 === null) {
    return -1;
  }
  return rc1 - rc2;
}

export function compare(versionStr1: string, versionStr2: string): number {
  const version1 = parse(versionStr1);
  const version2 = parse(versionStr2);
  if (version1.major !== version2.major) {
    return version1.major - version2.major;
  }
  if (version1.minor !== version2.minor) {
    return version1.minor - version2.minor;
  }
  if (version1.update !== version2.update) {
    return version1.update - version2.update;
  }
  if (version1.tag !== version2.tag) {
    const v1Rc = parseRCVersion(version1.tag);
    const v2Rc = parseRCVersion(version2.tag);
    if (v1Rc !== null || v2Rc !== null) {
      return compareRCVersions(v1Rc, v2Rc);
    }
    return version1.tag.localeCompare(version2.tag);
  }

  return 0;
}

/**
 * Parse mongodb version string only looking for major and minor
 * { major: 2, minor:6 }
 */
export function parsePrefix(versionString: string): { major: number; minor: number } {
  const tokens = versionString.split(/[.-]/);
  if (tokens.length < 2) {
    throw new Error(`Unable to parse MongoDB Version Prefix: ${versionString}`);
  }
  return {
    major: parseInt(tokens[0], 10),
    minor: parseInt(tokens[1], 10),
  };
}

export function comparePrefix(versionStr1: string, versionStr2: string): number {
  const version1 = parsePrefix(versionStr1);
  const version2 = parsePrefix(versionStr2);

  if (version1.major !== version2.major) {
    return version1.major - version2.major;
  }
  if (version1.minor !== version2.minor) {
    return version1.minor - version2.minor;
  }
  return 0;
}

export function isEnterpriseVersion(versionString: string): boolean {
  return /-ent$/.test(versionString);
}

export function isAtLeast3_0(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major >= 3;
}

export function isAtLeast3_2(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major > 3 || (version.major === 3 && version.minor >= 2);
}

export function isAtLeast3_4(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major > 3 || (version.major === 3 && version.minor >= 4);
}

export function isAtLeast3_6(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major > 3 || (version.major === 3 && version.minor >= 6);
}

export function isAtLeast4_0(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major >= 4;
}

export function isAtLeast4_1(versionString: string): boolean {
  if (!versionString) {
    return false;
  }
  const version = parse(versionString);
  return version.major >= 5 || (version.major === 4 && version.minor >= 1);
}

export function isAtLeast4_2(versionString: string): boolean {
  if (!versionString) {
    return false;
  }
  const version = parse(versionString);
  return version.major >= 5 || (version.major === 4 && version.minor >= 2);
}

export function isAtLeast4_4(versionString: string): boolean {
  if (!versionString) {
    return false;
  }
  const version = parse(versionString);
  return version.major >= 5 || (version.major === 4 && version.minor >= 4);
}

export function isAtLeast5_0(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major >= 5;
}

export function isAtLeast6_0(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major >= 6;
}

export function isAtLeast7_0(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major >= 7;
}

export function isAtLeast8_0(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const version = parse(versionString);

  return version.major >= 8;
}

export function supportsSplitHorizon(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  return (
    (isAtLeast4_2(versionString) && compare(versionString, '4.2.0') >= 0) ||
    (isAtLeast4_0(versionString) && compare(versionString, '4.0.11') >= 0) ||
    (isAtLeast3_6(versionString) && compare(versionString, '3.6.14') >= 0)
  );
}

export function supportsFTS(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  return compare(versionString, '4.1.11') >= 0;
}

export function supportsEmbeddedConfig(versionString: string): boolean {
  return isAtLeast8_0(versionString);
}

export function hasServer59071Bug(versionString: string): boolean {
  if (!versionString) {
    return false; // do not disable by default
  }

  return ['5.0.0', '5.0.1', '5.0.2'].includes(versionString);
}

export function supportsRollingKeyRotation(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  return compare(versionString, '4.1.8') >= 0;
}

export function supportsNativeLDAP(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  const isEnterprise = isEnterpriseVersion(versionString);
  const isAtLeast3_3_11 = compare(versionString, '3.3.11') >= 0;

  return isEnterprise && isAtLeast3_3_11;
}

export function supportNamespaceInsights(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  return compare(versionString, '4.4.2') >= 0;
}

export function supportsQueryShapeInsights(version: string) {
  if (!version) return false;
  return compare(version, '8.0.0') >= 0;
}

export function supportServerStatusQuerySortMetrics(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  return compare(versionString, '6.2.0') >= 0;
}

export function getMaxNodesPerReplicaSet(versionString: string): number {
  if (isAtLeast3_0(versionString)) {
    return 50;
  }
  return 12;
}

// Replace a wildcard (3.3.*) with Number.MAX_SAFE_INT (not yet supported by IE)
export function convertWildcardToMax(versionString: string): string {
  return versionString.replace('*', '9007199254740991');
}

export function supportsCollationOptions(versionString: string): boolean {
  if (!versionString) {
    return false;
  }

  return compare(versionString, '3.3.15') >= 0;
}

export function getMinimumMongoDbVersion(versions: Array<string>): string {
  let minimumVersion = versions[0];

  const uniqueVersions = _.uniq(versions);

  if (uniqueVersions.length > 1) {
    uniqueVersions.forEach((currentVersion) => {
      // if minimumVersion is > currentVersion, update minimumVersion to currentVersion
      if (compare(minimumVersion, currentVersion) > 0) {
        minimumVersion = currentVersion;
      }
    });
  }

  return minimumVersion;
}

export function getMaximumMongoDbVersion(versions: Array<string>): string {
  return _.uniq(versions).reduce((max, v) => (compare(v, max) > 0 ? v : max), versions[0]);
}

export function getMajorMinorPrefix(version: string): string {
  const majorMinorMatch = /^(\d+\.\d+)/.exec(version);
  return majorMinorMatch ? majorMinorMatch[1] : '';
}

export function versionCanUpgradeToCD(
  versionString: string,
  defaultCDMongoDBVersionString: string,
  mongoDBMajorVersions: Array<MongoDBMajorVersion>,
  fcvString?: string
) {
  const defaultCDMongoDBVersion = parse(defaultCDMongoDBVersionString);
  const version = parse(versionString);
  const fcv = fcvString ? parse(fcvString) : null;
  if (defaultCDMongoDBVersion.major === version.major) {
    return true;
  }
  if (fcv && defaultCDMongoDBVersion.major - fcv.major > 1) return false;
  if (defaultCDMongoDBVersion.minor === 0) {
    const sortedVersions = mongoDBMajorVersions
      .map((v) => parse(v.name))
      .sort((v1, v2) => {
        if (v1.major !== v2.major) {
          return v1.major - v2.major;
        }
        return v1.minor - v2.minor;
      });
    const defaultCDMajorVersionIndex = sortedVersions.findIndex((v) => v.major === defaultCDMongoDBVersion.major);
    if (defaultCDMajorVersionIndex < 1) {
      return false;
    }
    const secondLatestLTSVersion = sortedVersions[defaultCDMajorVersionIndex - 1];
    return secondLatestLTSVersion.major === version.major && secondLatestLTSVersion.minor === version.minor;
  }

  return false;
}

export function versionCanDowngradeFromCDToLTS(defaultCDMongoDBVersionString: string) {
  return parse(defaultCDMongoDBVersionString).minor === 0;
}

export function isVersionCompatibleWithMongosync(version: number) {
  return version >= 6;
}

export function useMongomirror(
  isMongosyncOlderVersionSupportEnabled: boolean,
  isMongosyncOlderVersionGapSupportEnabled: boolean,
  srcMajorVersion: number,
  destMajorVersion: number,
  isDestSharded: boolean,
  isPushMigration: boolean
) {
  if (isMongosyncOlderVersionSupportEnabled) {
    return false;
  } else if (srcMajorVersion < 6 && destMajorVersion <= 6) {
    if (isMongosyncOlderVersionGapSupportEnabled && destMajorVersion == 6 && isDestSharded && !isPushMigration) {
      return false;
    } else {
      return true;
    }
  } else {
    return false;
  }
}

// supported for 5.0.15+ for major version 5 and 6.0.5+ for major version 6 if maintenance is specified
// if not assume version supported for major version 5+
export function supportsChunkMigrationConcurrencyOptimistic(versionString: string = ''): boolean {
  try {
    return versionHasMaintenanceSpecified(versionString)
      ? compare(versionString, '6.0.5') > 0 ||
          (compare(versionString, '6.0.0') < 0 && compare(versionString, '5.0.15') > 0)
      : compare(versionString, '5.0') >= 0;
  } catch (e) {
    return false;
  }
}
