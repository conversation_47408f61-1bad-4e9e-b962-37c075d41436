export type RecursivePartial<T> = {
  [P in keyof T]?: T[P] extends Array<infer U>
    ? Array<RecursivePartial<U>>
    : T[P] extends object
      ? RecursivePartial<T[P]>
      : T[P] extends infer U | undefined
        ? RecursivePartial<U> | undefined
        : T[P] extends infer U | null
          ? RecursivePartial<U> | null
          : T[P];
};

/**
 * Make some keys `TOptionalKeys` in a type `TBase` be optional
 * @example
 * type Base = {
 *   timestamp: number;
 *   message: string;
 *   exception: Error;
 *   stackTrace: string[];
 * }
 * WithOptional<Base, 'exception' | 'stackTrace'>
 * // is the same as
 * {
 *   timestamp: number;
 *   message: string;
 *   exception?: Error;
 *   stackTrace?: string[];
 * }
 */
export type WithOptional<TBase, TOptionalKeys extends keyof TBase> = Omit<TBase, TOptionalKeys> &
  Pick<Partial<TBase>, TOptionalKeys>;

/**
 * Helper to allow type guard functions to access keys without making any untrue promises about what value they'll get
 */
export type WithUnknownValues<T extends object> = { [K in keyof T]?: unknown };
