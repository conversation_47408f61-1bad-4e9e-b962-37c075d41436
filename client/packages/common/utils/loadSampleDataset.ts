import servicePoller from '@packages/common/utils/servicePoller';
import mongoDate from '@packages/date';

function createLoadSampleDatasetStatusPoller({ loadSampleDatasetStatusCollection, visibilityAPI = document }) {
  const poller = servicePoller.create(() => {
    loadSampleDatasetStatusCollection.fetch();
    if (!loadSampleDatasetStatusCollection.any((model) => model.get('state') === 'WORKING')) {
      poller.stop();
    }
  }, visibilityAPI);
  return poller;
}

function findLoadSampleDatasetStatusForCluster(statuses, clusterDescription) {
  // backbone models will not be passed in from the Data Explorer Empty State
  const clusterCreateDate = mongoDate(clusterDescription.get('createDate'));
  const clusterName = clusterDescription.get('name');

  if (statuses.models) {
    return statuses.find((status) => {
      const createDate = status.get('createDate');
      const loadSampleDatasetCreateDate = mongoDate(createDate);
      return status.get('clusterName') === clusterName && clusterCreateDate.isBefore(loadSampleDatasetCreateDate);
    });
  }
  return statuses.find((status) => {
    const createDate = status.createDate;
    const loadSampleDatasetCreateDate = mongoDate(createDate);
    return status.clusterName === clusterName && clusterCreateDate.isBefore(loadSampleDatasetCreateDate);
  });
}

function isLoadSampleDatasetStatusExpired(status) {
  // check if backbone model or normal object
  const attributes = status.cid ? status.attributes : status;

  if (attributes.state === 'WORKING') {
    return false;
  }

  const threeHours = 3 * 60 * 60 * 1000;
  const completeDate = Date.parse(attributes.completeDate);
  const expireDate = completeDate + threeHours;
  return expireDate < Date.now();
}

function getLoadingSampleDatasetState(status) {
  return status && !isLoadSampleDatasetStatusExpired(status) && status.get('state');
}

function isSampleDatasetLoading(status) {
  return getLoadingSampleDatasetState(status) === 'WORKING';
}

function isSampleDatasetFailed(status) {
  return getLoadingSampleDatasetState(status) === 'FAILED';
}

function isSampleDatasetCompleted(status) {
  return getLoadingSampleDatasetState(status) === 'COMPLETED';
}

// When selectedDatasets exists, it means the user has triggered this sample data loading through the new
// selectively load sample datasets modal.
function hasMultipleSelectedDatasets(status): boolean {
  return status.selectedDatasets && status.selectedDatasets.length > 1;
}

function looksLikeSampleData(dbName) {
  return dbName?.startsWith('sample_');
}

const loadSampleDatasetStatusPollerInterval = 5e3;

export {
  createLoadSampleDatasetStatusPoller,
  findLoadSampleDatasetStatusForCluster,
  isLoadSampleDatasetStatusExpired,
  loadSampleDatasetStatusPollerInterval,
  getLoadingSampleDatasetState,
  isSampleDatasetLoading,
  isSampleDatasetFailed,
  isSampleDatasetCompleted,
  hasMultipleSelectedDatasets,
  looksLikeSampleData,
};
