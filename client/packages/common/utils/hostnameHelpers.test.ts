import chai from 'chai';
import sinon<PERSON><PERSON> from 'sinon-chai';

import { formatHostname, isAtlasClusterHostname, isIPv6 } from '@packages/common/utils/hostnameHelpers';

const expect = chai.expect;

chai.use(sinonChai);

describe('@packages/common/utils/hostnameHelpers', function () {
  it('correctly determines :: is an IPv6 hostname', function () {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    expect(isIPv6('::')).to.be.true;
  });

  it('correctly determines ::1 is an IPv6 hostname', function () {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    expect(isIPv6('::1')).to.be.true;
  });

  it('correctly determines a generic IPv6 hostname', function () {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    expect(isIPv6('2001:db8:85a3:8d3:1319:8a2e:370:7348')).to.be.true;
  });

  it('correctly determines 127.0.0.1 is an IPv4 hostname', function () {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    expect(isIPv6('127.0.0.1')).to.be.false;
  });

  it('formats :: correctly', function () {
    expect(formatHostname('::')).to.equal('[::]');
  });

  it('formats ::1 correctly', function () {
    expect(formatHostname('::1')).to.equal('[::1]');
  });

  it('formats a generic IPv6 hostname format', function () {
    expect(formatHostname('2001:db8:85a3:8d3:1319:8a2e:370:7348')).to.equal('[2001:db8:85a3:8d3:1319:8a2e:370:7348]');
  });

  it('does not change a generic IPv4 hostname', function () {
    expect(formatHostname('127.0.0.1')).to.equal('127.0.0.1');
  });

  it('correctly determines atlas cluster hostnames', function () {
    expect(isAtlasClusterHostname('myhost.mongo.com:27017')).to.be.equal(true);
    expect(isAtlasClusterHostname('myhost.mongodb.net:27017')).to.be.equal(true);
    expect(isAtlasClusterHostname('myhost.mmscloudteam.com:27017')).to.be.equal(true);
    expect(isAtlasClusterHostname('myhost.mangodb.net:27017')).to.be.equal(false);
  });
});
