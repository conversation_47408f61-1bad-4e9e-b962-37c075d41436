import { TrustPolicy } from '@packages/types/nds/utils';

function generateTrustPolicyJSON(awsAccountARN: string, externalId: string): TrustPolicy {
  return {
    Version: '2012-10-17',
    Statement: [
      {
        Effect: 'Allow',
        Principal: {
          AWS: awsAccountARN,
        },
        Action: 'sts:AssumeRole',
        Condition: {
          StringEquals: {
            'sts:ExternalId': externalId,
          },
        },
      },
    ],
  };
}

function generateCreateRoleAWSCLICommand(
  roleName: string,
  policyDocumentName: string,
  maxSessionDurationSeconds: number | null
): string {
  const createRoleCommand = `aws iam create-role \\\n --role-name ${roleName} \\\n --assume-role-policy-document file://${policyDocumentName}`;
  if (maxSessionDurationSeconds) {
    return `${createRoleCommand} \\\n --max-session-duration ${maxSessionDurationSeconds}`;
  } else {
    return createRoleCommand;
  }
}

function generatePutPolicyAWSCLICommand(roleName: string, policyDocumentName: string): string {
  return `aws iam put-role-policy \\\n  --role-name ${roleName} \\\n  --policy-name ${roleName}-policy \\\n  --policy-document file://${policyDocumentName}`;
}

function generatePutPolicyAWSCLICommandWithS3Bucket(
  roleName: string,
  policyDocumentName: string,
  bucketName: string
): string {
  return `aws iam put-role-policy \\\n  --role-name ${roleName} \\\n  --policy-name ${roleName}-${bucketName}-policy \\\n  --policy-document file://${policyDocumentName}`;
}

function generateUpdateRoleCliCommand(roleName: string, maxSessionDurationSeconds: number | undefined): string {
  return `aws iam update-role --role-name ${roleName} --max-session-duration ${maxSessionDurationSeconds}`;
}

// https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_iam-limits.html
// Names of users, groups, roles, policies, instance profiles, and server certificates must be alphanumeric, including
// the following common characters: plus (+), equal (=), comma (,), period (.), at (@), underscore (_), and hyphen (-).
const NAME_PATTERN = /^[a-zA-Z0-9+=,.@_-]+$/;
const ARN_PATTERN =
  /^arn:aws(?:-cn|-us-gov)?:iam::(\d{12}):role(?:\/[a-zA-Z0-9-_+=,.@\/]{0,512})?\/([a-zA-Z0-9-_+=,.@]{1,64})$/;

const validateRoleName = (roleName: string): void => {
  if (!NAME_PATTERN.test(roleName)) {
    throw new Error(
      'The specified role name is invalid. Role names must be alphanumeric and ' +
        "can contain '+=,.@-_' characters. Maximum 64 characters."
    );
  }
};

const isValidRoleARN = (roleARN: string): boolean => ARN_PATTERN.test(roleARN);

const validateRoleARN = (roleARN: string): void => {
  if (!isValidRoleARN(roleARN)) {
    throw new Error('Your Role ARN is invalid.');
  }
};

function roleARNtoAccountId(roleARN: string): string {
  return roleARN.split(':').filter((s) => !!s)[3] || '';
}

function roleARNtoRoleName(roleARN: string): string {
  return roleARN.split('/')[1] || '';
}

export {
  generateTrustPolicyJSON,
  generateCreateRoleAWSCLICommand,
  generatePutPolicyAWSCLICommand,
  generatePutPolicyAWSCLICommandWithS3Bucket,
  generateUpdateRoleCliCommand,
  validateRoleARN,
  isValidRoleARN,
  validateRoleName,
  roleARNtoAccountId,
  roleARNtoRoleName,
};
