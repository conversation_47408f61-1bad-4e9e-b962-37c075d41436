import Backbone from 'backbone';
import _ from 'underscore';

import { UsersCollection } from '@packages/types/nds/userBackbone';

import { NDSIdpType } from '@packages/common/models/NDSOIDCIdentityProvider';
import NDSUser from '@packages/common/models/NDSUser';

export type NDSUserCollectionInterface = UsersCollection;

const NDSUserCollection: {
  new (...args: Array<any>): NDSUserCollectionInterface;
  SORT_FIELD: {
    AUTH_TYPE: 'authType';
    USER: 'user';
    DB: 'db';
  };
} = Backbone.Collection.extend(
  {
    model: NDSUser,

    sortOrder: 1,
    sortField: 'authType',

    getAdminUser() {
      return this.find((user) => user.isAdminUser());
    },

    toggleSortOrder() {
      this.sortOrder = -this.sortOrder;
    },

    sortByField(fieldName) {
      this.sortOrder = 1;
      this.sortField = fieldName;
    },

    _userComparator(a, b, sortOrder = this.sortOrder) {
      // Sort by name
      const aUser = a.getEffectiveUsername();
      const bUser = b.getEffectiveUsername();
      return sortOrder * aUser.localeCompare(bUser);
    },

    _dbComparator(a, b) {
      // Sort by name
      const aDB = a.get('db');
      const bDB = b.get('db');
      const result = this.sortOrder * aDB.localeCompare(bDB);
      if (result !== 0) {
        return result;
      }

      return this._userComparator(a, b);
    },

    _authTypeComparator(a, b) {
      // Sort by authentication mechanism
      const authTypeComparison = b.getAuthTypeWeight() - a.getAuthTypeWeight();
      if (authTypeComparison !== 0) {
        return this.sortOrder * authTypeComparison;
      }

      return this._userComparator(a, b, 1);
    },

    comparator(a, b) {
      switch (this.sortField) {
        case NDSUserCollection.SORT_FIELD.USER:
          return this._userComparator(a, b);
        case NDSUserCollection.SORT_FIELD.DB:
          return this._dbComparator(a, b);
        case NDSUserCollection.SORT_FIELD.AUTH_TYPE:
        default:
          return this._authTypeComparator(a, b);
      }
    },

    getFirstSCRAMUser() {
      return this.findWhere({
        ldapAuthType: NDSUser.LDAP_AUTH_TYPE.NONE,
        awsIAMType: NDSUser.AWS_IAM_TYPE.NONE,
        x509Type: NDSUser.X509_TYPE.NONE,
        oidcAuthType: NDSUser.OIDC_AUTH_TYPE.NONE,
        isEditable: true,
      });
    },

    getNumSCRAMUsers() {
      return this.where({
        ldapAuthType: NDSUser.LDAP_AUTH_TYPE.NONE,
        awsIAMType: NDSUser.AWS_IAM_TYPE.NONE,
        x509Type: NDSUser.X509_TYPE.NONE,
        oidcAuthType: NDSUser.OIDC_AUTH_TYPE.NONE,
        isEditable: true,
      }).length;
    },

    getFirstLDAPUser() {
      return this.findWhere({ ldapAuthType: NDSUser.LDAP_AUTH_TYPE.USER, isEditable: true });
    },

    getFirstLDAPGroup() {
      return this.findWhere({ ldapAuthType: NDSUser.LDAP_AUTH_TYPE.GROUP, isEditable: true });
    },

    getFirstLDAPUserDN() {
      return this.findWhere({ ldapAuthType: NDSUser.LDAP_AUTH_TYPE.USER, isEditable: true, hasUserToDNMapping: false });
    },

    getFirstLDAPMappedUser() {
      return this.findWhere({ ldapAuthType: NDSUser.LDAP_AUTH_TYPE.USER, isEditable: true, hasUserToDNMapping: true });
    },

    getNumLDAPUsers() {
      return this.where({ ldapAuthType: NDSUser.LDAP_AUTH_TYPE.USER, isEditable: true }).length;
    },

    getFirstAWSIAMUser() {
      return this.findWhere({ awsIAMType: NDSUser.AWS_IAM_TYPE.USER, isEditable: true });
    },

    getFirstAWSIAMRole() {
      return this.findWhere({ awsIAMType: NDSUser.AWS_IAM_TYPE.ROLE, isEditable: true });
    },

    getFirstAWSIAMRoleOrUser() {
      return this.getFirstAWSIAMUser() || this.getFirstAWSIAMRole();
    },

    getNumAWSIAMUsers() {
      return (
        this.where({ awsIAMType: NDSUser.AWS_IAM_TYPE.USER, isEditable: true }).length +
        this.where({ awsIAMType: NDSUser.AWS_IAM_TYPE.ROLE, isEditable: true }).length
      );
    },

    getFirstManagedX509User() {
      return this.findWhere({ x509Type: NDSUser.X509_TYPE.MANAGED, isEditable: true });
    },

    getFirstCustomerX509User() {
      return this.findWhere({ x509Type: NDSUser.X509_TYPE.CUSTOMER, isEditable: true });
    },

    getFirstX509User() {
      return this.getFirstManagedX509User() || this.getFirstCustomerX509User();
    },

    getNumX509Users() {
      return (
        this.where({ x509Type: NDSUser.X509_TYPE.MANAGED, isEditable: true }).length +
        this.where({ x509Type: NDSUser.X509_TYPE.CUSTOMER, isEditable: true }).length
      );
    },

    getFirstOIDCWorkforceUser(federationSettings) {
      if (!federationSettings) {
        return null;
      }
      return _.first(
        this.filter(
          (model) => model.get('oidcAuthType') !== NDSUser.OIDC_AUTH_TYPE.NONE && model.get('isEditable')
        ).filter(
          (model) => model.getUserIdp(federationSettings) && model.getUserIdp(federationSettings).isWorkforceIdp()
        )
      );
    },

    getNumOIDCWorkforceUsers(federationSettings) {
      if (!federationSettings) {
        return 0;
      }
      return this.filter(
        (model) => model.get('oidcAuthType') !== NDSUser.OIDC_AUTH_TYPE.NONE && model.get('isEditable')
      ).filter((model) => model.getUserIdp(federationSettings) && model.getUserIdp(federationSettings).isWorkforceIdp())
        .length;
    },

    getFirstOIDCWorkloadUser(federationSettings) {
      if (!federationSettings) {
        return null;
      }
      return _.first(
        this.filter(
          (model) => model.get('oidcAuthType') !== NDSUser.OIDC_AUTH_TYPE.NONE && model.get('isEditable')
        ).filter(
          (model) => model.getUserIdp(federationSettings) && model.getUserIdp(federationSettings).isWorkloadIdp()
        )
      );
    },

    getNumOIDCWorkloadUsers(federationSettings) {
      if (!federationSettings) {
        return 0;
      }
      return this.filter(
        (model) => model.get('oidcAuthType') !== NDSUser.OIDC_AUTH_TYPE.NONE && model.get('isEditable')
      ).filter((model) => model.getUserIdp(federationSettings) && model.getUserIdp(federationSettings).isWorkloadIdp())
        .length;
    },

    hasOIDCUser() {
      return _.any(
        this.filter((model) => model.get('oidcAuthType') !== NDSUser.OIDC_AUTH_TYPE.NONE && model.get('isEditable'))
      );
    },

    hasOIDCUserOfIdpType(federationSettings, idpType: NDSIdpType) {
      switch (idpType) {
        case 'WORKLOAD':
          return this.getNumOIDCWorkloadUsers(federationSettings) > 0;
        case 'WORKFORCE':
          return this.getNumOIDCWorkforceUsers(federationSettings) > 0;
        default:
          return false;
      }
    },

    getEditableUsers() {
      return new NDSUserCollection(this.where({ isEditable: true }));
    },
  },
  {
    SORT_FIELD: {
      AUTH_TYPE: 'authType',
      USER: 'user',
      DB: 'db',
    },
  }
);

export default NDSUserCollection;
