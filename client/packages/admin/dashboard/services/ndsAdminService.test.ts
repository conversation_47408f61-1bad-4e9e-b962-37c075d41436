import chai from 'chai';
import $ from 'jquery';
import sinon from 'sinon';
import sinon<PERSON>hai from 'sinon-chai';

import { NDSAdminFilter } from '@packages/admin/dashboard/models/NDSAdminFilter';
import ndsAdminService from '@packages/admin/dashboard/services/ndsAdminService';
// safeguards
import safeguards from '@packages/test-utils/safeguards';

const expect = chai.expect;

chai.use(sinonChai);

describe('/v2/admin/dashboard/services/ndsAdminService', function () {
  beforeEach(function (this: $TSFixMe) {
    safeguards.disableSafeguards();
    this.sandbox = sinon.createSandbox();
  });

  afterEach(function (this: $TSFixMe) {
    this.sandbox.restore();
    safeguards.enableSafeguards();
  });

  describe('updateClusterThrottlingState', function () {
    const data = { state: 'ALWAYS' };
    const groupId = 'groupId';
    const clusterName = 'clusterName';

    beforeEach(function (this: $TSFixMe) {
      this.ajaxStub = this.sandbox.stub(ndsAdminService, 'updateClusterThrottlingState').returns($.Deferred());
      ndsAdminService.updateClusterThrottlingState(groupId, clusterName, data);
    });

    it('sends a request with the correct parameters', function (this: $TSFixMe) {
      expect(this.ajaxStub).to.have.been.calledWithMatch(groupId, clusterName, data);
    });
  });

  describe('getClusters', function () {
    describe('when called with an empty array', function () {
      beforeEach(function (this: $TSFixMe) {
        this.ajaxStub = this.sandbox.stub($, 'ajax').returns($.Deferred());

        ndsAdminService.getClusters();
      });

      it('sends a request with no query parameters', function (this: $TSFixMe) {
        expect(this.ajaxStub).to.have.been.calledWithMatch({ data: {} });
      });
    });

    describe('when called with multiple filters with mixed paramNames', function () {
      beforeEach(function (this: $TSFixMe) {
        this.ajaxStub = this.sandbox.stub($, 'ajax').returns($.Deferred());

        const filters = [
          new NDSAdminFilter(
            {
              keyName: 'filter1',
              paramName: 'param1',
            },
            { parse: true }
          ),
          new NDSAdminFilter(
            {
              keyName: 'filter2',
              paramName: 'param1',
            },
            { parse: true }
          ),
          new NDSAdminFilter(
            {
              keyName: 'filter3',
              paramName: 'param2',
            },
            { parse: true }
          ),
        ];

        ndsAdminService.getClusters(filters);
      });

      it('sends a request with the correct query parameters', function (this: $TSFixMe) {
        expect(this.ajaxStub).to.have.been.calledWithMatch({
          data: {
            param1: ['filter1', 'filter2'],
            param2: ['filter3'],
          },
        });
      });
    });
  });
});
