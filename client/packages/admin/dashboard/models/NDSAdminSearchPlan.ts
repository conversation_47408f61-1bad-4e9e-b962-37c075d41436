import Backbone from 'backbone';

export default Backbone.Model.extend({
  defaults: {
    type: 'plan',
    _id: null,
    groupId: null,
    result: null,
    shardsDraining: null,
    isClusterOutageSimulationActive: false,
    inferredClusterName: null,
  },

  isInProgress() {
    return this.get('result') === 'IN_PROGRESS';
  },

  isRollback() {
    return this.get('result') === 'ROLLBACK';
  },
});
