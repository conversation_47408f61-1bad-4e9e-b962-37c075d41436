import chai from 'chai';
import sinonChai from 'sinon-chai';

// collections
import NDSAdminSearchLimitsCollection from '@packages/admin/dashboard/models/NDSAdminSearchLimitsCollection';

const expect = chai.expect;

chai.use(sinonChai);

describe('js/backup/models/NDSAdminSearchLimitsCollection', function () {
  describe('when the collection is loaded from a group limits object', function () {
    let collection: $TSFixMe;
    beforeEach(function () {
      collection = NDSAdminSearchLimitsCollection.fromGroupLimits({
        mongodbUsers: { currentLimit: 376 },
        numUserCustomRoles: { currentLimit: 54 },
        numClusters: { currentLimit: 34 },
        numServerlessMTMs: { currentLimit: 100 },
        maxNetworkPermissionEntries: { currentLimit: 231 },
        maxCrossRegionNetworkPermissionEntries: { currentLimit: 232 },
        aCheckSortingforLimitsWithoutDisplayNames: { currentLimit: 76 },
        maxDataLakeTenants: { currentLimit: 100 },
        numBackgroundCustomRoles: { currentLimit: 225 },
        eCheckSortingforLimitsWithoutDisplayNames: { currentLimit: 45 },
        maxValidAtlasGeneratedCerts: { currentLimit: 25 },
        maxNodesPerPrivateLinkRegion: { currentLimit: 30 },
        azurePrivateLinkInboundNATRuleMaximumPort: { currentLimit: 2524 },
        azurePrivateLinkInboundNATRuleMinimumPort: { currentLimit: 1024 },
        azurePrivateLinkMaxNodesPerPrivateLinkRegion: { currentLimit: 150 },
        numPrivateServiceConnectionsPerRegionGroup: { currentLimit: 250 },
        gcpPSCNATSubnetMask: { currentLimit: 27 },
        streamingRestoreIops: { currentLimit: 300 },
        maxManualDownloads: { currentLimit: 500 },
        maxCustomRolesPerUser: { currentLimit: 20 },
        exportIops: { currentLimit: 300 },
        maxOnlineArchivesPerCluster: { currentLimit: 50 },
        maxActiveOnlineArchivesPerCluster: { currentLimit: 20 },
        azureStreamingRestoreIops: { currentLimit: 3000 },
        azureExportIops: { currentLimit: 3000 },
        maxCustomShardKeys: { currentLimit: 40 },
        allowedShardInstanceSizeDifference: { currentLimit: 2 },
      });
    });

    it('then they are sorted alphabetically', function () {
      expect(collection.at(0).get('limitName')).to.equal('aCheckSortingforLimitsWithoutDisplayNames'); // check that limits without display names are sorted properly by field name
      expect(collection.at(1).get('limitName')).to.equal('allowedShardInstanceSizeDifference');
      expect(collection.at(2).get('limitName')).to.equal('azurePrivateLinkInboundNATRuleMaximumPort');
      expect(collection.at(3).get('limitName')).to.equal('azurePrivateLinkInboundNATRuleMinimumPort');
      expect(collection.at(4).get('limitName')).to.equal('azurePrivateLinkMaxNodesPerPrivateLinkRegion');
      expect(collection.at(5).get('limitName')).to.equal('exportIops');
      expect(collection.at(6).get('limitName')).to.equal('streamingRestoreIops');
      expect(collection.at(7).get('limitName')).to.equal('azureExportIops');
      expect(collection.at(8).get('limitName')).to.equal('azureStreamingRestoreIops');
      expect(collection.at(9).get('limitName')).to.equal('maxManualDownloads');
      expect(collection.at(10).get('limitName')).to.equal('numClusters');
      expect(collection.at(11).get('limitName')).to.equal('numBackgroundCustomRoles');
      expect(collection.at(12).get('limitName')).to.equal('numUserCustomRoles');
      expect(collection.at(13).get('limitName')).to.equal('maxDataLakeTenants');
      expect(collection.at(14).get('limitName')).to.equal('eCheckSortingforLimitsWithoutDisplayNames'); // check that limits without display names are sorted properly by field name
      expect(collection.at(15).get('limitName')).to.equal('maxActiveOnlineArchivesPerCluster');
      expect(collection.at(16).get('limitName')).to.equal('maxCrossRegionNetworkPermissionEntries');
      expect(collection.at(17).get('limitName')).to.equal('maxCustomRolesPerUser');
      expect(collection.at(18).get('limitName')).to.equal('maxCustomShardKeys');
      expect(collection.at(19).get('limitName')).to.equal('maxNetworkPermissionEntries');
      expect(collection.at(20).get('limitName')).to.equal('maxNodesPerPrivateLinkRegion');
      expect(collection.at(21).get('limitName')).to.equal('maxOnlineArchivesPerCluster');
      expect(collection.at(22).get('limitName')).to.equal('maxValidAtlasGeneratedCerts');
      expect(collection.at(23).get('limitName')).to.equal('mongodbUsers');
      expect(collection.at(24).get('limitName')).to.equal('numPrivateServiceConnectionsPerRegionGroup');
    });
  });
});
