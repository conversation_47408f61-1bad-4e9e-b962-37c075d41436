// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import { View } from 'backbone.marionette';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'conn... Remove this comment to see the full error message
import { BackboneProvider } from 'connect-backbone-to-react';
import _ from 'underscore';

import PlanExecutorJobPriorityConfigsComponent from '@packages/admin/dashboard/components/PlanExecutorJobPriorityConfigsComponent';
import PlanExecutorJobPrioritySettingsComponent from '@packages/admin/dashboard/components/PlanExecutorJobPrioritySettingsComponent';
import ndsAdminService from '@packages/admin/dashboard/services/ndsAdminService';
import onInitialRenderPromise from '@packages/common/utils/mixins/onInitialRenderPromise';
import removeViewOnDestroy from '@packages/common/utils/mixins/removeViewOnDestroy';
// mixins
import mixInto from '@packages/common/utils/mixInto';
import viewWithReact from '@packages/view-with-react/viewWithReact';

import AddThrottlingItemModal from './AddThrottledItemModal';
import ThrottledItemsView from './ThrottledItemsView';

export default mixInto(View)(removeViewOnDestroy, onInitialRenderPromise, viewWithReact).extend({
  template: require('./ndsBackgroundJobTemplate.hbs'),

  modelEvents: {
    change: 'render',
  },

  events() {
    return {
      'click button[name="enable-background-processing"]': 'enableBackgroundProcessing',
      'click button[name="disable-background-processing"]': 'disableBackgroundProcessing',
      'click button[name="enable-dry-run"]': () => this.setDryRunMode(true),
      'click button[name="disable-dry-run"]': () => this.setDryRunMode(false),
      'click button[name="addWhitelistedGroup"]': '_addWhitelistedGroup',
      'click button[name="addBlacklistedGroup"]': '_addBlacklistedGroup',
      'click button[name="addThrottledCluster"]': '_addThrottledCluster',
      'click button[name="update"]': 'updateProviderSettingsData',
      'change input[name="updatePercent"]': '_changeOfPercent',
      'click button[name="unthrottle-all"]': () => this.updateAllCloudProviderSettingsData(false),
      'click button[name="throttle-all"]': () => this.updateAllCloudProviderSettingsData(true),
    };
  },

  templateContext() {
    return _.extend({}, this.model, {
      planningDisabled: this.model.get('cronsStatus').ndsPlanning === 'DISABLED',
      newPlanExecutionDisabled: this.model.get('planExecutorJobsFiltered') && this.model.get('filterlistRefreshed'),
      noPlansCurrentlyExecuting: this.model.get('activeJobs').length === 0,
      healthChecksDisabled: this.model.get('cronsStatus').ndsInstanceHealthCheck === 'DISABLED',
      externalMaintenanceDisabled: this.model.get('cronsStatus').startNewExternalMaintenanceTasks === 'DISABLED',
      newExternalMaintenanceDisabled:
        this.model.get('externalMaintenanceJobsFiltered') && this.model.get('filterlistRefreshed'),
      newJobGeneratorDisabled: this.model.get('planGeneratorJobsFiltered') && this.model.get('filterlistRefreshed'),
      orphanCleaningDisabled:
        this.model.get('cronsStatus').ndsOrphanCleaningAWS === 'DISABLED' &&
        this.model.get('cronsStatus').ndsOrphanCleaningAzure === 'DISABLED' &&
        this.model.get('cronsStatus').ndsOrphanCleaningGCP === 'DISABLED',
      dryRunModeEnabled: this.dryRunModeEnabled,
      dryRunModeStatus: this.dryRunModeEnabled ? 'ENABLED' : 'DISABLED',
      changeSubmitted: this.changeSubmitted,
      lastChangeFailed: this.lastChangeFailed,
      canViewStatusAndProviderThrottling:
        this.adminSettings.hasGlobalAtlasAdmin() || this.adminSettings.hasGlobalOwner(),
      canViewProjectandClusterThrottling:
        this.adminSettings.hasGlobalAtlasOperator() ||
        this.adminSettings.hasGlobalAtlasAdmin() ||
        this.adminSettings.hasGlobalOwner(),
      aws: this.aws,
      azure: this.azure,
      gcp: this.gcp,
      free: this.free,
      serverless: this.serverless,
      flex: this.flex,
      none: this.none,
    });
  },

  initialize(options: $TSFixMe) {
    this.model = options.backgroundJobStatus;
    this.changeSubmitted = false;
    this.settingsCollection = options.cloudProviderSettingsCollection;
    this.models = this.settingsCollection.models;
    this.dryRunModeEnabled = options.dryRunModeEnabled;
    this.adminSettings = options.adminSettings;
    this._setPercent();

    this.nameToID = {
      aws: 'AWS',
      azure: 'AZURE',
      gcp: 'GCP',
      free: 'FREE',
      serverless: 'SERVERLESS',
      flex: 'FLEX',
      none: 'NONE',
    };

    this.whitelistedGroups = options.whitelistThrottledGroupsCollection;
    this.blacklistedGroups = options.blacklistThrottledGroupsCollection;
    this.throttledClusters = options.throttledClustersCollection;
  },

  findModelObjectByID(objects: $TSFixMe, id: $TSFixMe) {
    const object = _.find(objects, (doc) => {
      // eslint-disable-next-line eqeqeq
      return doc.attributes._id == id;
    });

    return object;
  },

  onRender() {
    this._showWhitelistedGroups();
    this._showBlacklistedGroups();
    this._showThrottledClusters();

    this.registerReactComponent(
      <BackboneProvider>
        <PlanExecutorJobPriorityConfigsComponent />
      </BackboneProvider>,
      '.js-plan-executor-job-priority-configs-container'
    );

    this.registerReactComponent(
      <BackboneProvider>
        <PlanExecutorJobPrioritySettingsComponent />
      </BackboneProvider>,
      '.js-plan-executor-job-priority-settings-container'
    );
  },

  _showWhitelistedGroups() {
    const whitelistedGroupsView = new ThrottledItemsView({
      collection: this.whitelistedGroups,
      type: 'whitelist',
      condition: 'whitelisted from',
      $modalContainer: this.$('.js-modal-container'),
      el: this.$('.js-pinned-whitelist-groups-table'),
    }).render();

    this.trackForRemoval({ whitelistedGroupsView });
  },

  _showBlacklistedGroups() {
    const blacklistedGroupsView = new ThrottledItemsView({
      collection: this.blacklistedGroups,
      type: 'blacklist',
      condition: 'blacklisted into',
      $modalContainer: this.$('.js-modal-container'),
      el: this.$('.js-pinned-blacklist-groups-table'),
    }).render();

    this.trackForRemoval({ blacklistedGroupsView });
  },
  _showThrottledClusters() {
    const throttledClustersView = new ThrottledItemsView({
      collection: this.throttledClusters,
      type: 'cluster',
      condition: 'cluster added',
      $modalContainer: this.$('.js-modal-container'),
      el: this.$('.js-pinned-throttled-clusters-table'),
    }).render();

    this.trackForRemoval({ throttledClustersView });
  },

  enableBackgroundProcessing() {
    this.updateBackgroundProcessing({ state: true });
  },

  disableBackgroundProcessing() {
    this.updateBackgroundProcessing({ state: false });
  },

  setDryRunMode(enabled: $TSFixMe) {
    ndsAdminService
      .setDryRunMode(enabled)
      .then((resp) => (this.dryRunModeEnabled = resp.dryRunModeEnabled))
      // @ts-expect-error TS(2769): No overload matches this call.
      .then(undefined, () => (this.lastChangeFailed = true))
      .then(() => this.render());
  },

  updateBackgroundProcessing({ state }: $TSFixMe) {
    this.changeSubmitted = true;
    ndsAdminService
      .setBackgroundJobsEnabled(state)
      .then(() => {
        this.lastChangeFailed = false;
        return this.model.fetch();
      })
      // @ts-expect-error TS(2769): No overload matches this call.
      .then(undefined, () => (this.lastChangeFailed = true))
      .then(() => this.render());
  },

  updateAllCloudProviderSettingsData(throttled: $TSFixMe) {
    ndsAdminService
      .updateAllCloudProviderSettingsData(throttled)
      .then(() => {
        this.lastChangeFailed = false;
        return this.settingsCollection.fetch();
      })
      .then(
        () => {
          this._setPercent();
          this.render();
          this.$('.js-throttle-infoText').text(`${throttled ? 'Throttled' : 'Unthrottled'} ALL Cloud providers`);
          this.colorInfoText(`throttle`, !throttled);
        },
        () => {
          this.lastChangeFailed = true;
          this.$('.js-throttle-infoText').text('Error trying to update throttling for ALL Cloud provider');
          this.colorInfoText(`throttle`, false);
        }
      );
  },

  updateProviderSettingsData(event: $TSFixMe) {
    const classList = event.target.classList;
    const currentClass = this.findJsClassFromClassList(classList);

    const name = currentClass.split('-')[1];
    const id = this.nameToID[name];
    const throttlingPercent = this.$(`.js-${name}-throttlingPercent`).val();

    const data = {
      _id: id,
      throttlingPercent,
    };

    ndsAdminService.updateCloudProviderSettingsData(data).then(
      () => {
        this.$(`.js-${name}-infoText`).text(`${this.nameToID[name]} data updated!`);
        this.colorInfoText(name, true);
      },
      () => {
        this.$(`.js-${name}-infoText`).text(`Error trying to update ${this.nameToID[name]}.`);
        this.colorInfoText(name, false);
      }
    );
  },

  _addWhitelistedGroup() {
    const addWhitelistGroupModal = new AddThrottlingItemModal({
      whitelistedGroups: this.whitelistedGroups,
      blacklistedGroups: this.blacklistedGroups,
      throttlingType: 'whitelist',
      actionDescription: 'This project will NOT be throttled, regardless of the cloud provider settings above',
    }).render();

    this.$('.js-modal-container').html(addWhitelistGroupModal.$el);
    this.trackForRemoval({ addWhitelistGroupModal });
  },

  _addBlacklistedGroup() {
    const addBlacklistGroupModal = new AddThrottlingItemModal({
      whitelistedGroups: this.whitelistedGroups,
      blacklistedGroups: this.blacklistedGroups,
      throttlingType: 'blacklist',
      actionDescription:
        'This project will FULLY throttled, regardless of the cloud provider settings above. ' +
        'No planner actions will take place for this Project',
    }).render();

    this.$('.js-modal-container').html(addBlacklistGroupModal.$el);
    this.trackForRemoval({ addBlacklistGroupModal });
  },

  _addThrottledCluster() {
    const addThrottledClusterModal = new AddThrottlingItemModal({
      whitelistedGroups: this.whitelistedGroups,
      blacklistedGroups: this.blacklistedGroups,
      throttledClusters: this.throttledClusters,
      throttlingType: 'cluster',
      actionDescription:
        'This cluster will be throttled, regardless of project or cloud providers settings. No planner actions will take place for this cluster',
    }).render();

    this.$('.js-modal-container').html(addThrottledClusterModal.$el);
    this.trackForRemoval({ addThrottledClusterModal });
  },

  colorInfoText(id: $TSFixMe, success: $TSFixMe) {
    if (success === true) {
      this.$(`.js-${id}-infoText`).css('opacity', 1);
      this.$(`.js-${id}-infoText`).removeClass('text-error');
      this.$(`.js-${id}-infoText`).addClass('text-success');
    } else {
      this.$(`.js-${id}-infoText`).css('opacity', 1);
      this.$(`.js-${id}-infoText`).addClass('text-error');
      this.$(`.js-${id}-infoText`).removeClass('text-success');
    }
  },

  _changeOfPercent(event: $TSFixMe) {
    const currentClass = this.findJsClassFromClassList(event.target.classList);
    const id = currentClass.split('-')[1];

    const newPercent = parseInt(event.target.value);
    this.$(`.js-${id}-throttlingPercentText`).text(newPercent);
  },

  _setPercent() {
    this.aws = this.findModelObjectByID(this.models, 'AWS').attributes;
    this.azure = this.findModelObjectByID(this.models, 'AZURE').attributes;
    this.gcp = this.findModelObjectByID(this.models, 'GCP').attributes;
    this.free = this.findModelObjectByID(this.models, 'FREE').attributes;
    this.serverless = this.findModelObjectByID(this.models, 'SERVERLESS').attributes;
    this.flex = this.findModelObjectByID(this.models, 'FLEX').attributes;
    this.none = this.findModelObjectByID(this.models, 'NONE').attributes;
  },

  findJs(str: $TSFixMe) {
    return str.indexOf('js-') > -1;
  },

  findJsClassFromClassList(classList: $TSFixMe) {
    const classes: Array<$TSFixMe> = [];
    _.each(classList, (doc) => {
      classes.push(doc);
    });

    const currentClass = _.find(classes, this.findJs);
    return currentClass;
  },
});
