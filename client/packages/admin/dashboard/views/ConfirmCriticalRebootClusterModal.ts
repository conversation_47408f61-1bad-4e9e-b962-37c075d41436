// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import { View } from 'backbone.marionette';

import * as api from '@packages/common/services/api';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import viewAsModal from '@packages/common/utils/mixins/viewAsModal';
import mixInto from '@packages/common/utils/mixInto';

export default mixInto(View)(viewAsModal).extend({
  template: require('./confirmCriticalRebootClusterModalTemplate.hbs'),
  modifierClassNames: 'view-modal-content-is-small',
  title: 'Critical Reboot Cluster',
  actions: [
    {
      name: 'close',
      cssClass: 'button',
      label: 'Cancel',
    },
    {
      name: 'reboot',
      cssClass: 'button button-is-danger',
      label: 'Reboot',
    },
  ],

  initialize({ groupId, clusterName, hosts, callback }: $TSFixMe) {
    this.groupId = groupId;
    this.clusterName = clusterName;
    this.hosts = hosts;
    this.callback = callback;
  },

  templateContext() {
    return {
      groupId: this.groupId,
      clusterName: this.clusterName,
      hosts: this.hosts,
    };
  },

  events() {
    return {
      'click [name="close"]': this.remove,
      'click [name="reboot"]': '_onClickReboot',
      'submit form': '_onClickReboot',
    };
  },

  onRequestSuccess() {
    this.callback && this.callback();
    this.remove();
  },

  _onClickReboot() {
    api.nds.admin.requestClusterCriticalReboot(this.groupId, this.clusterName).then(
      () => {
        this.onRequestSuccess();
      },
      (err) => {
        this.$('.alert-danger')
          .attr('hidden', false)
          .text(`Error: ${exceptionToMessage(err)}`);
      }
    );
  },
});
