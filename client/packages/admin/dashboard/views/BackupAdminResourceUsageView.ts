// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import { View } from 'backbone.marionette';
import template from 'hbs/backup_admin/backupAdminResourceUsage.hbs';

import Resources from '@packages/legacy/backup-admin/models/Resources';
import ResourceByGroupView from '@packages/legacy/backup-admin/views/ResourceByGroupView';
//  Views
import ResourceView from '@packages/legacy/backup-admin/views/ResourceView';
import ErrorModal from '@packages/legacy/core/views/ErrorModal';

export default View.extend({
  template,

  regions: {
    content: '#backup-admin-resource-usage',
  },

  initialize() {},

  templateContext() {
    return {};
  },

  onRender() {
    const $loadingEl = this.$el.find('#resource-usage-loading');
    const $loadedEl = this.$el.find('#resource-usage-loaded');

    $loadingEl.show();
    $loadedEl.hide();

    const resources = new Resources();
    resources.fetch({
      success: (data: $TSFixMe) => {
        this.$el.find('#resource-by-job').html(new ResourceView({ collection: data.models }).render().el);
        this.$el.find('#resource-by-group').html(new ResourceByGroupView({ collection: data.models }).render().el);

        $loadingEl.hide();
        $loadedEl.show();
      },
      error: (xhr: $TSFixMe) => {
        $loadingEl.hide();
        // @ts-expect-error TS(7009): 'new' expression, whose target lacks a construct s... Remove this comment to see the full error message
        new ErrorModal({ xhr }).render();
      },
    });
  },
});
