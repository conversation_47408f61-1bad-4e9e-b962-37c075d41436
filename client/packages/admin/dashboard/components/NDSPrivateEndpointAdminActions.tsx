import { useReducer } from 'react';

import { css } from '@emotion/react';
import Button from '@leafygreen-ui/button';
import TextInput from '@leafygreen-ui/text-input';
import { produce } from 'immer';

// apis
import { updateGCPPSCNATSubnetRange } from '@packages/common/services/api/nds/adminApi';
import { exceptionToMessage } from '@packages/common/services/errorHelper';

interface Props {
  groupId: string;
  hasGlobalAtlasOperator: boolean;
  gcpPSCCIDR?: string;
}

export const rowStyle = css({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'flex-end',
  gap: '10px',
});

export const rowStyleDisabled = css({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'flex-end',
  gap: '10px',
  opacity: 0.5,
});

enum PrivateEndpointActionType {
  ON_CHANGE_CIDR,
  ON_SUBMIT_CIDR_SUCCESS,
  ON_SUBMIT_CIDR_ERROR,
}

interface PrivateEndpointActionPayload {
  cidr?: string;
  errorMessage?: string;
}

interface PrivateEndpointAction {
  type: PrivateEndpointActionType;
  payload?: PrivateEndpointActionPayload;
}

const initialReducerState: PrivateEndpointActionsReducerState = {
  gcpPSCRegionGroupCidrsRange: '',
  gcpPSCRegionGroupCidrsErrorMessage: '',
  cidrInputState: 'none',
};

interface PrivateEndpointActionsReducerState {
  gcpPSCRegionGroupCidrsRange?: string;
  gcpPSCRegionGroupCidrsErrorMessage?: string;
  cidrInputState?: 'none' | 'valid' | 'error';
}

const privateEndpointActionsReducer = produce(
  (draft: PrivateEndpointActionsReducerState, action: PrivateEndpointAction) => {
    const { type, payload = {} } = action;
    switch (type) {
      case PrivateEndpointActionType.ON_CHANGE_CIDR:
        draft.gcpPSCRegionGroupCidrsRange = payload.cidr;
        break;
      case PrivateEndpointActionType.ON_SUBMIT_CIDR_SUCCESS:
        draft.gcpPSCRegionGroupCidrsErrorMessage = '';
        draft.cidrInputState = 'valid';
        break;
      case PrivateEndpointActionType.ON_SUBMIT_CIDR_ERROR:
        draft.gcpPSCRegionGroupCidrsErrorMessage = payload.errorMessage;
        draft.cidrInputState = 'error';
        break;
      default:
        return draft;
    }
  },
  initialReducerState
);

export default function NDSPrivateEndpointAdminActions(props: Props) {
  const { groupId, gcpPSCCIDR = '', hasGlobalAtlasOperator } = props;

  initialReducerState.gcpPSCRegionGroupCidrsRange = gcpPSCCIDR;

  const [state, dispatch] = useReducer(privateEndpointActionsReducer, initialReducerState);

  const onSubmitCIDR = async () => {
    try {
      const cidr: string = state.gcpPSCRegionGroupCidrsRange === undefined ? '' : state.gcpPSCRegionGroupCidrsRange;
      await updateGCPPSCNATSubnetRange(groupId, cidr);
      dispatch({ type: PrivateEndpointActionType.ON_SUBMIT_CIDR_SUCCESS });
    } catch (e) {
      dispatch({
        type: PrivateEndpointActionType.ON_SUBMIT_CIDR_ERROR,
        payload: { errorMessage: exceptionToMessage(e) },
      });
    }
  };

  return (
    <div css={hasGlobalAtlasOperator ? rowStyle : rowStyleDisabled}>
      <TextInput
        label="GCP Private Service Connect Region Group CIDRs Range"
        description="Set this to use a different range to be divided into GCP PSC region group CIDR blocks in the case of a conflict with peering."
        placeholder="**********/12"
        onChange={({ target: { value } }) => {
          dispatch({ type: PrivateEndpointActionType.ON_CHANGE_CIDR, payload: { cidr: value } });
        }}
        value={state.gcpPSCRegionGroupCidrsRange}
        errorMessage={state.gcpPSCRegionGroupCidrsErrorMessage}
        state={state.cidrInputState}
        disabled={!hasGlobalAtlasOperator}
      />
      <Button onClick={onSubmitCIDR} disabled={!hasGlobalAtlasOperator}>
        Submit
      </Button>
    </div>
  );
}
