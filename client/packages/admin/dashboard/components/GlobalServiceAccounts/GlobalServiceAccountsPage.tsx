import { useCallback, useEffect, useReducer, useState } from 'react';

import { css } from '@emotion/react';
import Button from '@leafygreen-ui/button';
import { BasicEmptyState } from '@leafygreen-ui/empty-state';
import { spacing } from '@leafygreen-ui/tokens';
import { H2 } from '@leafygreen-ui/typography';
import { Helmet } from 'react-helmet';
import { useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom-v5-compat';

import { GlobalServiceAccount, ServiceAccount } from '@packages/types/access';

import * as viewer from '@packages/redux/common/viewer';

import * as api from '@packages/common/services/api';
import * as globalServiceAccountsApi from '@packages/common/services/api/globalServiceAccountsApi';
import ServiceAccountsHeader from '@packages/components/Access/ServiceAccounts/ServiceAccountsHeader';
import { ServiceAccountsTableCard } from '@packages/components/Access/ServiceAccounts/ServiceAccountsTableCard';
import { getSecretFilterExpiryInitValFromQueryParam } from '@packages/components/Access/ServiceAccounts/utils';
import { DeleteServiceAccountModal } from '@packages/components/DeleteModal';
import Image from '@packages/components/Image';
import Loader from '@packages/components/Loader';
import { StyledToast } from '@packages/components/styles/serviceAccounts';

export const DELETE_GLOBAL_SERVICE_ACCOUNT_MODAL_TEST_ID = 'delete-global-service-account-modal';

const styles = {
  link: css`
    display: inline;
  `,
  header: css`
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: ${spacing[400]}px;
  `,
  headerContent: css`
    flex: 1;
  `,
  subtitle: css`
    margin-top: ${spacing[200]}px;
  `,
  documentation: css`
    margin-bottom: ${spacing[200]}px;
  `,
  actions: css`
    display: flex;
    gap: ${spacing[200]}px;
    align-items: center;
  `,
};

interface WikiLinkProps {
  testId: string;
}

function WikiLink({ testId }: WikiLinkProps) {
  return (
    <a
      data-testid={testId}
      href="https://wiki.corp.mongodb.com/spaces/MMS/pages/*********/Global+Service+Accounts"
      target="_blank"
      rel="noopener noreferrer"
      css={styles.link}
    >
      Global Service Accounts wiki
    </a>
  );
}

interface CreateButtonProps {
  onClick: () => void;
  variant?: 'primary';
  size?: 'default';
}

function CreateButton({ onClick, variant = 'primary', size = 'default' }: CreateButtonProps) {
  return (
    <Button variant={variant} size={size} onClick={onClick}>
      Create Global Service Account
    </Button>
  );
}

interface PageHeaderProps {
  allowManagement: boolean;
  onCreateClick: () => void;
  testId: string;
}

function PageHeader({ allowManagement, onCreateClick, testId }: PageHeaderProps) {
  return (
    <div css={styles.header}>
      <div css={styles.headerContent}>
        <H2>Global Service Accounts</H2>
        <div css={styles.subtitle}>
          Global Service Accounts provide programmatic access to the Atlas Administration API with global-level
          permissions.
          <br />
          Consult the <WikiLink testId={testId} /> for full documentation.
        </div>
      </div>
      {allowManagement && (
        <div css={styles.actions}>
          <CreateButton onClick={onCreateClick} />
        </div>
      )}
    </div>
  );
}

interface GlobalServiceAccountsPageState {
  deleteModalIsOpen: boolean;
  serviceAccountToDelete?: GlobalServiceAccount;
  isDeleting: boolean;
  errorToastIsOpen: boolean;
  successToastIsOpen: boolean;
  toastMessage: string;
}

const GlobalServiceAccountsPageInitState: GlobalServiceAccountsPageState = {
  deleteModalIsOpen: false,
  serviceAccountToDelete: undefined,
  isDeleting: false,
  errorToastIsOpen: false,
  successToastIsOpen: false,
  toastMessage: '',
};

export default function GlobalServiceAccountsPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const secretFilterInitVal = getSecretFilterExpiryInitValFromQueryParam(searchParams);
  const [secretFilterExpiryInDays, setSecretFilterExpiryInDays] = useState(secretFilterInitVal);

  const [serviceAccounts, setServiceAccounts] = useState<Array<GlobalServiceAccount>>([]);

  const load = useCallback(async () => {
    try {
      const accounts = await globalServiceAccountsApi.getGlobalServiceAccounts();
      setServiceAccounts(accounts);
    } catch (err) {
      console.error('Failed to load global service accounts:', err);
      throw err; // Re-throw so Loader component can handle the error
    }
  }, []);

  useEffect(() => {
    load();
  }, [load]);

  const hasGlobalReadOnly = useSelector(viewer.isGlobalReadOnly);
  const hasGlobalServiceAccountAdmin = useSelector(viewer.isGlobalServiceAccountAdmin);
  const hasGlobalOwner = useSelector(viewer.isGlobalOwner);
  const allowManagement = hasGlobalServiceAccountAdmin || hasGlobalOwner;

  const [state, setState] = useReducer(
    (prevState: GlobalServiceAccountsPageState, newState: Partial<GlobalServiceAccountsPageState>) => ({
      ...prevState,
      ...newState,
    }),
    GlobalServiceAccountsPageInitState
  );

  const handleCreateClick = () => {
    navigate('/general/globalServiceAccounts/create');
  };

  const handleDeleteClick = (serviceAccount: GlobalServiceAccount) => {
    setState({
      deleteModalIsOpen: true,
      serviceAccountToDelete: serviceAccount,
    });
  };

  const handleDeleteConfirm = async () => {
    if (!state.serviceAccountToDelete) return;

    try {
      setState({ isDeleting: true });
      await api.globalServiceAccounts.deleteGlobalServiceAccount(state.serviceAccountToDelete.clientId);
      await load(); // Refresh the list
      setState({
        deleteModalIsOpen: false,
        serviceAccountToDelete: undefined,
        isDeleting: false,
        successToastIsOpen: true,
        toastMessage: `Global service account "${state.serviceAccountToDelete.name}" has been deleted.`,
      });
    } catch (err) {
      setState({
        errorToastIsOpen: true,
        deleteModalIsOpen: false,
        isDeleting: false,
        toastMessage: 'Failed to delete global service account. Please try again.',
      });
      console.error(err);
    }
  };

  const handleDeleteCancel = () => {
    setState({
      deleteModalIsOpen: false,
      serviceAccountToDelete: undefined,
    });
  };

  return (
    <>
      <Helmet title="Global Service Accounts" />

      <Loader load={load} renderWhenLoaded doNotRenderWhenError hasTopSpacing>
        {serviceAccounts.length === 0 ? (
          <>
            <PageHeader
              allowManagement={allowManagement}
              onCreateClick={handleCreateClick}
              testId="global-service-accounts-header-wiki-link"
            />
            <BasicEmptyState
              title="No global service accounts yet"
              description={
                <>
                  Create a global service account to securely connect your applications to the Atlas Administration API
                  with global-level permissions. <br />
                  Consult the <WikiLink testId="global-service-accounts-empty-state-wiki-link" />.
                </>
              }
              graphic={<Image alt="No global service accounts" src="/static/images/API_Keys_Empty.svg" />}
              primaryButton={allowManagement ? <CreateButton onClick={handleCreateClick} /> : undefined}
            />
          </>
        ) : (
          <>
            <PageHeader
              allowManagement={allowManagement}
              onCreateClick={handleCreateClick}
              testId="global-service-accounts-with-data-wiki-link"
            />
            <ServiceAccountsHeader
              serviceAccounts={serviceAccounts}
              secretFilterExpiryInDays={secretFilterExpiryInDays}
              setSecretFilterExpiryInDays={setSecretFilterExpiryInDays}
            />
            <ServiceAccountsTableCard
              serviceAccounts={serviceAccounts}
              onDeleteIconClick={(serviceAccount: ServiceAccount) => {
                const originalGlobalServiceAccount = serviceAccounts.find(
                  (gsa) => gsa.clientId === serviceAccount.clientId
                );
                if (originalGlobalServiceAccount) {
                  handleDeleteClick(originalGlobalServiceAccount);
                }
              }}
              context="global"
              secretFilterExpiryInDays={secretFilterExpiryInDays}
              allowManagement={allowManagement}
              hasReadPermission={hasGlobalReadOnly}
            />
          </>
        )}
      </Loader>

      <DeleteServiceAccountModal
        testId={DELETE_GLOBAL_SERVICE_ACCOUNT_MODAL_TEST_ID}
        open={state.deleteModalIsOpen}
        onClose={handleDeleteCancel}
        title="Delete Global Service Account"
        deleteLabel="Delete"
        onDelete={handleDeleteConfirm}
        deleteButtonIsDisabled={state.isDeleting}
        warningContent={
          <>
            {state.serviceAccountToDelete?.name} will lose all its data and access. This operation cannot be undone. Any
            applications using this global service account will lose access.
          </>
        }
      >
        Are you sure you want to delete this global service account?
      </DeleteServiceAccountModal>

      <StyledToast
        open={state.errorToastIsOpen}
        variant="warning"
        title="Error"
        description={state.toastMessage}
        onClose={() => setState({ errorToastIsOpen: false })}
      />

      <StyledToast
        open={state.successToastIsOpen}
        variant="success"
        title="Success"
        description={state.toastMessage}
        onClose={() => setState({ successToastIsOpen: false })}
      />
    </>
  );
}
