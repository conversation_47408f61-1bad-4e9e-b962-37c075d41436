// 3rd
import { css } from '@emotion/react';
// components
import Modal from '@leafygreen-ui/modal';

interface Props {
  chartName: string;
  chartInfo: string;
  chartType: string;
  setOpen: (open: boolean) => void;
  isOpen: boolean;
}

export default function BackupChartInfo(props: Props) {
  const { chartName, chartInfo, isOpen, setOpen } = props;
  return (
    <Modal open={isOpen} setOpen={setOpen} css={css('z-index: 100')}>
      <div className="modal-header">
        <h4>Chart Info: {chartName}</h4>
      </div>
      <dl className="metric-info-modal-series">
        <dt className="metric-info-modal-series-name metric-info-modal-series-1">Application Server</dt>
        <dd className="metric-info-modal-series-description">{chartInfo}</dd>
      </dl>
      <div className="modal-body">
        <div>
          <h4>Chart Operations</h4>
          <ul>
            <li>
              Click-and-drag on a chart to zoom to a specific region. Other charts will automatically zoom to the same
              region.
            </li>
            <li>Double-click on a chart to reset zoom level.</li>
          </ul>
        </div>
      </div>
    </Modal>
  );
}
