import { useEffect, useState } from 'react';

import { css } from '@emotion/css';
import { fontFamilies } from '@leafygreen-ui/tokens';
import { Chart, GoogleChartWrapper, GoogleVizEventName, ReactGoogleChartEvent } from 'react-google-charts';

import { GanttRow } from '@packages/admin/dashboard/components/PlanGraph/GanttVisualizationUtil';

import { toChartRowDataArray } from './GanttVisualizationUtil';
import { getMoveStateColor, getMoveStateLightColor, MoveState } from './planVisualizerUtil';

interface GanttGraphProps {
  rows: Array<GanttRow>;
  onRowSelected: (index: number) => void;
}

const COLUMNS = [
  { type: 'string', label: 'Move ID' },
  { type: 'string', label: 'Class' },
  { type: 'string', label: 'State' },
  { type: 'date', label: 'Start Date' },
  { type: 'date', label: 'End Date' },
  { type: 'number', label: 'Duration' },
  { type: 'number', label: 'Percent Complete' },
  { type: 'string', label: 'Dependencies' },
];

const chartStyle = css`
  svg g:last-child * {
    fill: transparent;
    stroke: transparent;
    visibility: hidden;
  }
  svg text:hover {
    cursor: pointer !important;
    filter: brightness(0.6);
  }
  svg path:hover {
    cursor: pointer !important;
  }
`;

export default function GanttGraph({ rows, onRowSelected }: GanttGraphProps) {
  const [options, setOptions] = useState<object>();
  const chartEvents: Array<ReactGoogleChartEvent> = [
    {
      eventName: 'select' as GoogleVizEventName,
      callback: ({ chartWrapper }: { chartWrapper: GoogleChartWrapper | null }) => {
        if (chartWrapper != null) {
          const selection = chartWrapper.getChart().getSelection();
          if (selection?.length) {
            const { row } = chartWrapper.getChart().getSelection()[0];
            onRowSelected(row ?? -1);
          } else {
            onRowSelected(-1);
          }
        }
      },
    },
  ];

  useEffect(() => {
    setOptions({
      height: rows.length * 42 + 60,
      gantt: {
        defaultStartDateMillis: new Date(2015, 3, 28),
        criticalPathEnabled: false,
        arrow: {
          width: 2,
          length: 0,
          radius: 4,
          spaceAfter: 0,
        },
        labelStyle: {
          fontName: fontFamilies.default,
        },
        palette: generatePalette(),
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rows]);

  // @ts-expect-error TS(7006): Parameter 'color' implicitly has an 'any' type.
  const toPalette = (color, lightColor) => ({
    color,
    dark: color,
    light: lightColor,
  });

  const generatePalette = () => {
    const seenStates = new Set<MoveState>();
    const palette: Array<object> = [];
    rows.forEach(({ state }) => {
      if (!seenStates.has(state)) {
        seenStates.add(state);
        palette.push(toPalette(getMoveStateColor(state), getMoveStateLightColor(state)));
      }
    });
    return palette;
  };

  const convertedRows = toChartRowDataArray(rows);
  return (
    <Chart
      chartType="Gantt"
      width="100%"
      height="100%"
      data={[COLUMNS, ...convertedRows]}
      options={options}
      chartEvents={chartEvents}
      className={chartStyle}
    />
  );
}
