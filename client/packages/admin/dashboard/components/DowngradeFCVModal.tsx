import { useEffect, useState } from 'react';

import styled from '@emotion/styled';
import Banner from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import { palette } from '@leafygreen-ui/palette';

import * as api from '@packages/common/services/api';
import { Input } from '@packages/common/styles/adminForm';
import Modal from '@packages/components/Modal';
import { InputErrorMessage, InputLabel } from '@packages/components/styles/common';

const Description = styled.div({
  fontSize: 13,
  fontWeight: 'normal',
  marginTop: '15px',
});

const FormGroup = styled.div({
  marginTop: '10px',
  display: 'flex',
});

const FormGroupLeft = styled.div({
  width: '50%',
});

const FormGroupRight = styled.div({
  width: '50%',
});

const InputDescription = styled.p({
  fontSize: 13,
  fontWeight: 'normal',
  color: palette.gray.base,
});

interface DowngradeFCVModalProps {
  groupId: string;
  clusterName: string;
  onClose: () => void;
  onSuccess: () => void;
}

export default function DowngradeFCVModal({ groupId, clusterName, onClose, onSuccess }: DowngradeFCVModalProps) {
  const [loading, setLoading] = useState(false);

  const [currentVersion, setCurrentVersion] = useState('');
  const [downgradeableVersion, setDowngradeableVersion] = useState('');

  const [generalError, setGeneralError] = useState('');

  const [reason, setReason] = useState('');

  const [link, setLink] = useState('');

  const handleInputChange = (event: $TSFixMe) => {
    setReason(event.target.value);
  };

  useEffect(() => {
    const fetchVersions = async () => {
      try {
        const versions = await api.nds.admin.getDowngradableFeatureCompatibilityVersion(groupId, clusterName);
        const cv = versions.currentVersion;
        const dv = versions.downgradableVersion;
        setCurrentVersion(cv);
        setDowngradeableVersion(dv);
        if (cv === '8.0') {
          setLink(
            'https://wiki.corp.mongodb.com/pages/viewpage.action?spaceKey=MMS&title=Atlas+Admin+Playbook#AtlasAdminPlaybook-DowngradinganAtlasClusterfromMongoDB8.0to7.0'
          );
        } else if (cv === '7.0') {
          setLink(
            'https://wiki.corp.mongodb.com/pages/viewpage.action?spaceKey=MMS&title=Atlas+Admin+Playbook#AtlasAdminPlaybook-DowngradinganAtlasClusterfromMongoDB7.0to6.0'
          );
        } else if (cv === '6.0') {
          setLink(
            'https://wiki.corp.mongodb.com/pages/viewpage.action?spaceKey=MMS&title=Atlas+Admin+Playbook#AtlasAdminPlaybook-DowngradinganAtlasClusterfromMongoDB6.0to5.0'
          );
        } else if (cv === '5.0') {
          setLink(
            'https://wiki.corp.mongodb.com/pages/viewpage.action?spaceKey=MMS&title=Atlas+Admin+Playbook#AtlasAdminPlaybook-DowngradinganAtlasClusterfromMongoDB5.0to4.4'
          );
        }
      } catch (error) {
        setGeneralError('Failed to fetch Feature Compatibility Version');
      }
    };

    fetchVersions();
  }, [groupId, clusterName]);

  const clearLoadingAndSetError = (errorMessage: string) => {
    setGeneralError(errorMessage);
    setLoading(false);
  };

  const onSubmit = async () => {
    setLoading(true);
    setGeneralError('');

    if (!reason) {
      clearLoadingAndSetError('Invalid ticket');
      return;
    }

    try {
      onSuccess();

      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 90);

      await api.nds.admin.fixFeatureCompatibilityVersionForCluster(
        groupId,
        clusterName,
        downgradeableVersion,
        reason,
        expirationDate.getTime()
      );

      onClose();
    } catch (error) {
      clearLoadingAndSetError(error.message);
    }
  };
  return (
    <Modal title={`Feature Compatibility Version Downgrade`} size="medium" onClose={onClose}>
      <>
        <Banner variant="warning">
          This will downgrade the FCV to {downgradeableVersion}. To ensure a safe downgrade, perform the necessary
          feature checks documented
          <a target="_blank" href={link}>
            {' '}
            here{' '}
          </a>
          for {currentVersion} {'>'} {downgradeableVersion} and inform the customer of the procedure to catch any
          potential gotchas with their dataset/index definitions. It is imperative that admins only perform one version
          downgrade at a time and wait for PACPCM to complete before downgrading the binary
        </Banner>
        <Description>
          After downgrading a cluster's FCV, a customer will be able to downgrade their binary via the updateCluster
          endpoint or by an admin pinning a MongoDB version in the fixed version manager
        </Description>
        <hr />
        <FormGroup>
          <FormGroupLeft>
            <InputLabel>Desired Feature Compatibility Version</InputLabel>
            <InputDescription>Set to the cluster's previous MongoDB version</InputDescription>
          </FormGroupLeft>
          <FormGroupRight>
            <Input id="downgradeableFCV" type="text" value={downgradeableVersion} disabled />
          </FormGroupRight>
        </FormGroup>
        <FormGroup>
          <FormGroupLeft>
            <InputLabel>Current Feature Compatibility Version</InputLabel>
            <InputDescription>
              See more {''}
              <a
                target="_blank"
                href={'https://www.mongodb.com/docs/manual/reference/command/setFeatureCompatibilityVersion/'}
              >
                {' '}
                here{' '}
              </a>
            </InputDescription>
          </FormGroupLeft>
          <FormGroupRight>
            <Input id="currentFCV" type="text" value={currentVersion} disabled />
          </FormGroupRight>
        </FormGroup>
        <FormGroup>
          <FormGroupLeft>
            <InputLabel>Reason (JIRA ticket required) </InputLabel>
            <InputDescription>Please specify a valid HELP ticket</InputDescription>
          </FormGroupLeft>
          <FormGroupRight>
            <Input id="reasonFCV" type="text" value={reason} onChange={handleInputChange} placeholder="CLOUDP-123" />
          </FormGroupRight>
        </FormGroup>
      </>

      {generalError && (
        <InputErrorMessage data-test-id="generalError">
          <span className="fa fa-exclamation-triangle" /> {generalError}
        </InputErrorMessage>
      )}
      <Modal.ModalFooter>
        <Button
          data-testid="submitFCVDowngradeRequest"
          variant="primary"
          name="submitChangeConfigType"
          onClick={onSubmit}
          disabled={loading}
        >
          Submit
        </Button>{' '}
        <Button name="cancel" onClick={onClose} disabled={loading}>
          Cancel
        </Button>
      </Modal.ModalFooter>
    </Modal>
  );
}
