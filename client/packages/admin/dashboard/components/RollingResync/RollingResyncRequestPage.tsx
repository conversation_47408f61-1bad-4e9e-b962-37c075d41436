import { useCallback, useEffect, useRef, useState } from 'react';

import { css } from '@emotion/react';
import Banner, { Variant } from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import Checkbox from '@leafygreen-ui/checkbox';
import { Combobox, ComboboxOption } from '@leafygreen-ui/combobox';
import { Option, Select } from '@leafygreen-ui/select';
import TextInput from '@leafygreen-ui/text-input';

import { ClusterDescription } from '@packages/types/nds/clusterDescription';
import { MaintenanceWindow } from '@packages/types/nds/projectMaintenance';
import { InstanceHardware, InstanceHostname, ReplicaSetHardware } from '@packages/types/nds/replicaSetHardware';
import { Optimization } from '@packages/types/nds/rollingResync';

import * as api from '@packages/common/services/api';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import { Label } from '@packages/common/styles/adminForm';
import { marginBottom } from '@packages/common/styles/margins';
import { getMaintenanceWindowUTCString } from '@packages/common/utils/maintenanceWindowUtils';
import DateTimeRangeSelector from '@packages/components/DateTimeRangeSelector';
import Dropdown, { DropdownOption } from '@packages/components/Dropdown';
import Modal from '@packages/components/Modal';
import { InputLabel } from '@packages/components/styles/common';

import ShardNodeSelector from './ShardNodeSelector';

const GROUP_ID_REGEX = /^[a-f0-9]{24}$/;
const FILECOPY_INIT_SYNC_MAX_VERSION = 5;

const REASON_REQUIRED_ERROR_MSG =
  'You must supply a valid and unresolved JIRA ticket ID, SFDC case number, or Intercom conversation ID';

const styles = {
  inputContainerStyle: css`
    padding-bottom: 20px;
    width: 850px;
  `,

  alignRight: css`
    float: right;
  `,

  marginRight: css`
    margin-right: 10px;
  `,
};

interface PublicToAgentSchemeHostnameMapping {
  [key: string]: string | undefined;
}

export default function RollingResyncRequestPage({ onSuccess }: $TSFixMe) {
  //Add Modal
  const [addModalOpened, setAddModalOpened] = useState(false);
  const [groupId, setGroupId] = useState('');
  const [groupIdError, setGroupIdError] = useState('');
  const [groupClusterDescriptions, setGroupClusterDescriptions] = useState<Array<ClusterDescription>>([]);
  const [clusterName, setClusterName] = useState('');
  const [reason, setReason] = useState('');
  const [reasonError, setReasonError] = useState('');

  const [clusterVersion, setClusterVersion] = useState<string | null>(null);

  const [nodesRequestedToResync, setNodesRequestedToResync] = useState<Array<string>>([]);
  const [clusterNodeHostnames, setClusterNodeHostnames] = useState<Array<string>>([]);
  const [preserveNodeOrder, setPreserveNodeOrder] = useState(false);
  const [hideDedicatedCSRS, setHideDedicatedCSRS] = useState<boolean>(true);
  const [replicaHardware, setReplicaHardware] = useState<Array<ReplicaSetHardware>>([]);

  const [syncSourcesToExclude, setSyncSourcesToExclude] = useState<Array<string>>([]);
  const [initialSyncMethod, setInitialSyncMethod] = useState('LOGICAL');
  const [bypassValidations, setBypassValidations] = useState<Array<string>>([]);
  const [maintenanceWindow, setMaintenanceWindow] = useState<MaintenanceWindow | null>(null);
  const [stepDownTiming, setStepDownTiming] = useState('PROJECT_MAINTENANCE_WINDOW');
  const [showStepDownDate, setShowStepDownDate] = useState(false);
  const [customStepDownStartDate, setCustomStepDownStartDate] = useState<Date | null>(null);
  const [customStepDownEndDate, setCustomStepDownEndDate] = useState<Date | null>(null);
  const [bypassOptimizations, setBypassOptimizations] = useState<Array<Optimization>>([
    Optimization.REPLACE_FIRST_SYNC_WITH_OIS,
  ]);
  const [addModalErrorMessage, setAddModalErrorMessage] = useState('');

  const publicHostnameToAgentSchemeHostnameMapping = useRef<PublicToAgentSchemeHostnameMapping>({});

  const addRollingResyncRequest = async (
    groupId: string,
    clusterName: string,
    nodesRequestedToResync: Array<string>,
    preserveNodeOrder: boolean,
    initialSyncMethod: string,
    syncSourcesToExclude: Array<string>,
    bypassValidations: Array<string>,
    stepDownTiming: string,
    customStepDownStartDate: Date | null,
    customStepDownEndDate: Date | null,
    bypassOptimizations: Array<Optimization>,
    reason: string
  ) => {
    setAddModalErrorMessage('');

    // If user chooses to preserve order of nodes, server expects the internal hostnames
    if (preserveNodeOrder) {
      try {
        nodesRequestedToResync = getInternalHostnames(nodesRequestedToResync);
        syncSourcesToExclude = getInternalHostnames(syncSourcesToExclude);
      } catch (error) {
        setAddModalErrorMessage(exceptionToMessage(error));
        return;
      }
    }

    try {
      await api.nds.admin.addRollingResync(
        groupId,
        clusterName,
        nodesRequestedToResync,
        preserveNodeOrder,
        initialSyncMethod,
        syncSourcesToExclude,
        bypassValidations,
        stepDownTiming,
        customStepDownStartDate === null ? null : Date.parse(customStepDownStartDate.toUTCString()),
        customStepDownEndDate === null ? null : Date.parse(customStepDownEndDate.toUTCString()),
        bypassOptimizations,
        reason
      );
      setAddModalOpened(false);
      addModalClose();
      onSuccess();
    } catch (error) {
      setAddModalErrorMessage(exceptionToMessage(error));
    }
  };

  const onGroupIdChange = (groupId: string) => {
    setGroupId(groupId);
    resetGroupAssociatedFields();

    if (!groupId) {
      return;
    }

    if (!GROUP_ID_REGEX.test(groupId)) {
      setGroupIdError('Invalid group ID format');
      return;
    }

    api.nds.clusterDescriptions.getClusterDescriptions(groupId).then(
      (clusterDescriptions) => {
        setGroupClusterDescriptions(clusterDescriptions);
      },
      (err) => {
        setGroupIdError(`Could not look up group ID: ${err.errorCode}`);
      }
    );

    api.nds.maintenanceWindows.getMaintenanceWindow(groupId).then((window) => {
      setMaintenanceWindow(window);
    });
  };

  const onClusterNameSelect = (clusterName: string) => {
    resetShardNodeSelector();

    setClusterName(clusterName);

    const clusterDescription = getClusterDescription(clusterName);
    if (clusterDescription) {
      setClusterVersion(clusterDescription.mongoDBMajorVersion);

      api.nds.admin.getReplicaSetHardware(groupId, clusterName).then((response: Array<ReplicaSetHardware>) => {
        setReplicaHardware(response);
      });
    }
  };

  useEffect(() => {
    const hostnames: Array<string> = [];

    // Process config shards last so config nodes appear last (see CLOUDP-257043)
    const rsHardware = replicaHardware.filter((rs) => rs.containsShardData);
    if (!hideDedicatedCSRS) {
      rsHardware.push(...replicaHardware.filter((rs) => !rs.containsShardData));
    }

    rsHardware.forEach((rs) => {
      const cloudProviderHardware = [...rs.cloudProviderHardware, ...rs.cloudProviderInternalHardware];
      cloudProviderHardware.forEach((node: InstanceHardware) => {
        const publicScheme = node.hostnames?.filter((h: InstanceHostname) => h.scheme === 'PUBLIC')[0];
        const agentScheme = node.hostnames?.filter(
          (h: InstanceHostname) => h.scheme === node.hostnameSchemeForAgents
        )[0];

        if (publicScheme) {
          publicHostnameToAgentSchemeHostnameMapping.current[publicScheme.hostname] = agentScheme?.hostname;
          hostnames.push(publicScheme.hostname);
        }
      });
    });
    setClusterNodeHostnames(hostnames);
  }, [replicaHardware, hideDedicatedCSRS]);

  const onReasonChange = (reason: string) => {
    setReason(reason);

    if (!reason) {
      setReasonError(REASON_REQUIRED_ERROR_MSG);
      return;
    }
    setReasonError('');
  };

  const updateNodesFromShardNodeSelector = useCallback((nodes: Set<string>) => {
    setNodesRequestedToResync(Array.from(nodes));
  }, []);

  const addModalClose = () => {
    setAddModalErrorMessage('');
    setGroupId('');
    setClusterVersion(null);
    setPreserveNodeOrder(false);
    setHideDedicatedCSRS(true);
    setInitialSyncMethod('LOGICAL');
    setBypassValidations([]);
    setStepDownTiming('PROJECT_MAINTENANCE_WINDOW');
    setShowStepDownDate(false);
    setCustomStepDownStartDate(null);
    setCustomStepDownEndDate(null);
    setBypassOptimizations([Optimization.REPLACE_FIRST_SYNC_WITH_OIS]);
    setAddModalOpened(false);

    resetShardNodeSelector();
    resetGroupAssociatedFields();
  };

  const resetShardNodeSelector = () => {
    setReplicaHardware([]);
  };

  const resetGroupAssociatedFields = () => {
    setGroupIdError('');
    setReasonError(REASON_REQUIRED_ERROR_MSG);
    setGroupClusterDescriptions([]);
    setClusterName('');
    setNodesRequestedToResync([]);
    setSyncSourcesToExclude([]);
    setMaintenanceWindow(null);

    resetShardNodeSelector();

    publicHostnameToAgentSchemeHostnameMapping.current = {};
  };

  const onDateTimeRangeSelectionChanged = (event: $TSFixMe): void => {
    const { startDateTime, endDateTime } = event;
    setCustomStepDownStartDate(new Date(startDateTime));
    setCustomStepDownEndDate(new Date(endDateTime));
  };

  const onBypassOptimizationChange = (value: Optimization) => (e: React.ChangeEvent<HTMLInputElement>) => {
    // Checkbox logic is inverted here since Bypass Optimization checkbox labels are phrased positively (e.g.
    // "Use OIS") and server expects the opposite (e.g. "Bypass OIS")
    if (e.target.checked) {
      setBypassOptimizations((prev) => prev.filter((item) => item !== value));
    } else {
      setBypassOptimizations((prev) => [...prev, value]);
    }
  };

  const getClusterDescription = (clusterName: string) => {
    return groupClusterDescriptions?.find((cd) => cd.name === clusterName);
  };

  const getInternalHostnames = (externalHostnames: Array<string>) => {
    return externalHostnames.map((hostname) => {
      const internalHostname = publicHostnameToAgentSchemeHostnameMapping.current[hostname];
      if (!internalHostname) {
        throw new Error(`Unable to retrieve internal hostname for ${hostname}`);
      }
      return internalHostname;
    });
  };

  const bypassValidationsOptions: ReadonlyArray<DropdownOption> = [
    {
      key: 'OPLOG_SIZE',
      text: 'OPLOG_SIZE',
      onClick: () => setBypassValidations(bypassValidations.length === 0 ? ['OPLOG_SIZE'] : []),
    },
  ];

  const stepDownTimingOptions: ReadonlyArray<DropdownOption> = [
    {
      key: 'ANYTIME',
      text: 'anytime',
      onClick: () => {
        setStepDownTiming('ANYTIME');
        setShowStepDownDate(false);
        setCustomStepDownStartDate(null);
        setCustomStepDownEndDate(null);
      },
    },
    {
      key: 'PROJECT_MAINTENANCE_WINDOW',
      text: 'project maintenance window',
      onClick: () => {
        setStepDownTiming('PROJECT_MAINTENANCE_WINDOW');
        setShowStepDownDate(false);
        setCustomStepDownStartDate(null);
        setCustomStepDownEndDate(null);
      },
    },
    {
      key: 'CUSTOM_PRIMARY_STEP_DOWN_WINDOW',
      text: 'custom primary step down window',
      onClick: () => {
        setStepDownTiming('CUSTOM_PRIMARY_STEP_DOWN_WINDOW');
        setShowStepDownDate(true);
      },
    },
  ];

  return (
    <div css={{ display: 'inline' }}>
      <Button variant="primary" name="add" onClick={() => setAddModalOpened(true)} css={marginBottom.Small}>
        Add a Rolling Resync Request
      </Button>

      <Modal open={addModalOpened} onClose={() => addModalClose()}>
        <h2 data-testid="modalHeader">
          <strong>Add a Rolling Resync Request</strong>
        </h2>
        {addModalErrorMessage && <p className="text-error">{addModalErrorMessage}</p>}
        <div css={styles.inputContainerStyle}>
          <TextInput
            css={marginBottom.Small}
            label="Group Id"
            onChange={(event) => onGroupIdChange(event.target.value.trim())}
            errorMessage={groupIdError}
            state={groupIdError === '' ? 'none' : 'error'}
            data-testid="groupIdField"
          />

          <Select
            allowDeselect={false}
            label="Cluster Name"
            onChange={onClusterNameSelect}
            popoverZIndex={2000}
            css={marginBottom.Small}
            disabled={groupId === '' || groupIdError !== ''}
            data-testid="clusterSelectButton"
          >
            {groupClusterDescriptions.map((clusterDescription) => {
              return (
                <Option key={clusterDescription.name} value={clusterDescription.name}>
                  {clusterDescription.name}
                </Option>
              );
            })}
          </Select>

          <TextInput
            css={marginBottom.Small}
            label="Reason for Resync"
            description="Valid support issue ID"
            placeholder="HELP-1234"
            onChange={(event) => onReasonChange(event.target.value.trim())}
            errorMessage={reasonError}
            state={reasonError === '' ? 'none' : 'error'}
            data-testid="reasonField"
          />

          <label>Nodes to Resync</label>

          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Checkbox
              css={marginBottom.XSmall}
              onChange={() => {
                setNodesRequestedToResync([]);
                setPreserveNodeOrder(!preserveNodeOrder);
              }}
              label="Preserve node order"
            />
            <Checkbox
              css={marginBottom.XSmall}
              checked={hideDedicatedCSRS}
              onChange={() => {
                setHideDedicatedCSRS(!hideDedicatedCSRS);
              }}
              label="Hide Dedicated CSRS Hosts"
            />
          </div>

          {!preserveNodeOrder && (
            <div css={{ position: 'relative', marginBottom: '20px' }}>
              <ShardNodeSelector
                replicaHardware={replicaHardware}
                updateNodes={updateNodesFromShardNodeSelector}
                hideDedicatedCSRS={hideDedicatedCSRS}
              />
            </div>
          )}

          {preserveNodeOrder && (
            <Combobox
              aria-label="Nodes to Resync"
              css={marginBottom.Small}
              placeholder="Select nodes..."
              clearable
              multiselect
              chipCharacterLimit={64}
              popoverZIndex={2000}
              disabled={clusterName === ''}
              onChange={setNodesRequestedToResync}
              value={nodesRequestedToResync}
            >
              {clusterNodeHostnames?.map((hostname) => (
                <ComboboxOption displayName={hostname} key={hostname} value={hostname} />
              ))}
            </Combobox>
          )}

          <Combobox
            label="Sync Sources To Exclude"
            css={marginBottom.Small}
            placeholder="Select nodes..."
            clearable
            multiselect
            chipCharacterLimit={64}
            popoverZIndex={2000}
            disabled={clusterName === ''}
            onChange={setSyncSourcesToExclude}
            value={syncSourcesToExclude}
          >
            {clusterNodeHostnames?.map((hostname) => (
              <ComboboxOption displayName={hostname} key={hostname} value={hostname} />
            ))}
          </Combobox>

          <h3>Resync Settings</h3>

          <Select
            allowDeselect={false}
            label="Initial Sync Method (First Node)"
            description="Determines the initial sync method for the first node of the rolling resync. Subsequent nodes will use the optimal strategy as decided by the system. MongoDB versions 5.0 and below are ineligible to use file-copy based initial sync."
            defaultValue={initialSyncMethod}
            onChange={setInitialSyncMethod}
            popoverZIndex={2000}
            css={marginBottom.Small}
            data-testid="initialSyncSelectButton"
          >
            <Option value="LOGICAL">logical</Option>
            <Option
              value="FILECOPY"
              disabled={clusterVersion ? Number(clusterVersion) <= FILECOPY_INIT_SYNC_MAX_VERSION : false}
            >
              file-copy based
            </Option>
            <Option value="ANY">any</Option>
          </Select>

          <Label>Step-Down Timing</Label>
          {maintenanceWindow ? (
            <Banner
              variant={maintenanceWindow.isUserDefined ? Variant.Warning : Variant.Info}
              css={marginBottom.XSmall}
              data-testid="projectMaintenanceBanner"
            >
              {maintenanceWindow.isUserDefined
                ? `This project has a customer-defined maintenance window on ${getMaintenanceWindowUTCString(
                    maintenanceWindow,
                    'UTC'
                  )} UTC`
                : 'This project has no customer-defined maintenance window'}
            </Banner>
          ) : null}

          <Dropdown
            css={css({ display: 'block' }, marginBottom.Small)}
            buttonContent={stepDownTiming}
            options={stepDownTimingOptions}
            withArrow
          ></Dropdown>
          {showStepDownDate && <InputLabel>Custom Step Down Window</InputLabel>}
          {showStepDownDate && (
            <div css={marginBottom.Small}>
              <DateTimeRangeSelector
                onChange={(e: $TSFixMe) => onDateTimeRangeSelectionChanged(e)}
                timeZone="UTC"
                handleMessage={() => {}}
              />
            </div>
          )}

          <h3>Optional Settings</h3>
          <div css={marginBottom.Small}>
            <Label>Optimized Initial Sync</Label>
            <Checkbox
              checked={!bypassOptimizations.includes(Optimization.REPLACE_FIRST_SYNC_WITH_OIS)}
              onChange={onBypassOptimizationChange(Optimization.REPLACE_FIRST_SYNC_WITH_OIS)}
              label="Use OIS for first initial sync (Check for corruption remedation. Leave unchecked for compaction)"
            />
            <Checkbox
              checked={!bypassOptimizations.includes(Optimization.OIS)}
              onChange={onBypassOptimizationChange(Optimization.OIS)}
              label="Use OIS for subsequent initial syncs if supported"
            />
          </div>
          <div>
            <Label>Bypass Validations</Label>
            <p>If OPLOG_SIZE is selected, the minimum oplog size validation will be skipped</p>
            <Dropdown
              css={marginBottom.Small}
              buttonContent={bypassValidations.length === 0 ? 'Select Validations to Bypass' : bypassValidations}
              options={bypassValidationsOptions}
              withArrow
            ></Dropdown>
          </div>
        </div>
        <div css={styles.alignRight}>
          <Button
            css={styles.marginRight}
            onClick={() => {
              addModalClose();
            }}
          >
            {' '}
            Cancel
          </Button>
          <Button
            variant="primary"
            disabled={
              groupId === '' ||
              clusterName === '' ||
              nodesRequestedToResync.length === 0 ||
              initialSyncMethod === '' ||
              stepDownTiming === ''
            }
            onClick={() => {
              addRollingResyncRequest(
                groupId,
                clusterName,
                nodesRequestedToResync,
                preserveNodeOrder,
                initialSyncMethod,
                syncSourcesToExclude,
                bypassValidations,
                stepDownTiming,
                customStepDownStartDate,
                customStepDownEndDate,
                bypassOptimizations,
                reason
              );
            }}
            data-testid="addButton"
          >
            {' '}
            Add
          </Button>
        </div>
      </Modal>
    </div>
  );
}
