import { CreateExposureFormData, FormErrors } from '@packages/types/nds/shadowCluster';

import { JIRA_TICKET_REGEX, OBJECT_ID_REGEX } from './constants';

/**
 * Validates if a string is a valid MongoDB ObjectId
 */
export const isValidObjectId = (id: string): boolean => {
  return OBJECT_ID_REGEX.test(id);
};

/**
 * Validates if a string matches JIRA ticket format
 * Only accepts JIRA ticket format (e.g., PROJ-123)
 */
export const isValidJiraFormat = (jira: string): boolean => {
  return JIRA_TICKET_REGEX.test(jira);
};

/**
 * Validates the entire form and returns an object with field errors
 * Enhanced with proper type checking to prevent runtime errors
 */
export const validateForm = (data: CreateExposureFormData): FormErrors => {
  const errors: FormErrors = {};

  // ObjectId validation with comprehensive type checking
  const objectIdFields: Array<keyof CreateExposureFormData> = ['sourceClusterId', 'sourceGroupId', 'sourceOrgId'];
  objectIdFields.forEach((field) => {
    const value = data[field];
    if (!value) {
      errors[field] = 'This field is required';
    } else if (typeof value !== 'string') {
      errors[field] = 'Must be a valid string';
    } else if (!value.trim()) {
      errors[field] = 'This field is required';
    } else if (!isValidObjectId(value)) {
      errors[field] = 'Must be a valid ObjectId (24 character hex string)';
    }
  });

  // Required string field validation with proper type checking
  const requiredFields: Array<keyof CreateExposureFormData> = [
    'sourceClusterName',
    'sourceGroupName',
    'sourceOrgName',
    'triggerReasonJira',
  ];
  requiredFields.forEach((field) => {
    const value = data[field];
    if (!value) {
      errors[field] = 'This field is required';
    } else if (typeof value !== 'string') {
      errors[field] = 'Must be a valid string';
    } else if (!value.trim()) {
      errors[field] = 'This field is required';
    }
  });

  // JIRA validation - only JIRA ticket format allowed
  const jiraValue = data.triggerReasonJira;
  if (jiraValue && typeof jiraValue === 'string' && jiraValue.trim() && !isValidJiraFormat(jiraValue)) {
    errors.triggerReasonJira = 'Must be a valid JIRA ticket (e.g., PROJ-123)';
  }

  return errors;
};

/**
 * Validates a specific field and returns its error message if any
 */
export const validateField = (
  field: keyof CreateExposureFormData,
  value: string,
  formData: CreateExposureFormData
): string => {
  const tempData = { ...formData, [field]: value };
  const errors = validateForm(tempData);
  return errors[field] || '';
};

/**
 * Checks if the form has any validation errors
 */
export const isFormValid = (data: CreateExposureFormData): boolean => {
  return Object.keys(validateForm(data)).length === 0;
};
