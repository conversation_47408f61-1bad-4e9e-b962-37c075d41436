import { expect } from 'chai';
import { mount } from 'enzyme';
import AceEditor from 'react-ace';
import sinon from 'sinon';

//services
import * as api from '@packages/common/services/api';
import UpdateProcessArgsForm from '@packages/admin/dashboard/components/Search/UpdateProcessArgsForm';
import Tooltip from '@packages/components/Tooltip';
import beforeEachNextCallstackWithUpdateWrapper from '@packages/test-utils/beforeEachNextCallstackWithUpdateWrapper';
import { MockStoreWrappingComponent } from '@packages/test-utils/createMockStore';

const basicProcessArgsJson = {
  shardArgs: {
    setParameter: {
      failIndexKeyTooLong: true,
      notablescan: false,
    },
  },
  configArgs: {
    setParameter: {},
  },
  mongosArgs: {
    setParameter: {},
  },
  mongotArgs: {
    additionalParams: {},
  },
  mongotuneArgs: {},
  replicaSetArgs: {},
  clusterWideArgs: {
    setClusterParameter: {},
  },
  daoVersion: 'V1',
};

function getUpdatedShardArgs() {
  const updatedJson = {
    setParameter: {
      failIndexKeyTooLong: true,
      notablescan: false,
      transactionLifetimeLimitSeconds: 600,
    },
  };

  return JSON.stringify(updatedJson, null, 2);
}

function getUpdatedClusterWideArgs() {
  const updatedJson = {
    setClusterParameter: {
      changeStreamOptions: { preAndPostImages: { expireAfterSeconds: 100 } },
    },
  };

  return JSON.stringify(updatedJson, null, 2);
}

function confirmBasicElements() {
  it('shows seven editors', function (this: $TSFixMe) {
    expect(this.wrapper.find(AceEditor).length).to.equal(7);
  });

  it('shows a tooltip', function (this: $TSFixMe) {
    expect(this.wrapper.find(Tooltip).length).to.equal(1);
  });

  it('shows three docs links', function (this: $TSFixMe) {
    const links = this.wrapper.find('a');
    expect(links.length).to.equal(3);
    expect(links.at(0).text()).to.contain('Configuration File Options');
    expect(links.at(1).text()).to.contain('Available setParameter Server Parameters');
    expect(links.at(2).text()).to.contain('Available setClusterParameter Cluster Parameter');
  });

  it('does not show a result message', function (this: $TSFixMe) {
    expect(this.wrapper.find('.text-is-success').length).to.equal(0);
    expect(this.wrapper.find('.text-is-error').length).to.equal(0);
  });
}

describe('@packages/admin/dashboard/components/Search/UpdateProcessArgsForm', function () {
  beforeEach(function (this: $TSFixMe) {
    this.sandbox = sinon.createSandbox();
  });

  afterEach(function (this: $TSFixMe) {
    this.sandbox.restore();
  });

  describe('when rendered with no process args', function (this: $TSFixMe) {
    this.timeout(10000);
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(<UpdateProcessArgsForm groupId="1" clusterName="myCluster" isEnabled />, {
        wrappingComponent: MockStoreWrappingComponent,
      });
    });

    it('shows basic elements', function () {
      confirmBasicElements();
    });
  });

  describe('when rendered with process args and is editable', function (this: $TSFixMe) {
    this.timeout(10000);
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <UpdateProcessArgsForm groupId="1" clusterName="myCluster" isEnabled processArgs={basicProcessArgsJson} />,
        { wrappingComponent: MockStoreWrappingComponent }
      );
    });

    confirmBasicElements();

    it('shows a clickable update button', function (this: $TSFixMe) {
      const updateButton = this.wrapper.find('Button');
      expect(updateButton.length).to.equal(1);
      expect(updateButton.prop('disabled')).to.equal(false);
    });

    describe('when click on Update button', function () {
      beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
        this.getProcessArgsPromiseStub = this.sandbox
          .stub(api.nds.admin, 'getProcessArgs')
          .returns(basicProcessArgsJson);
        this.wrapper.setState({ shardArgs: getUpdatedShardArgs(), clusterWideArgs: getUpdatedClusterWideArgs() });
        this.wrapper.find('Button').simulate('click');
      });

      it('shows enter reason step', function (this: $TSFixMe) {
        const enterReason = this.wrapper.find('.multi-step-wizard-breadcrumb-is-active');

        expect(enterReason.length).to.equal(1);
        expect(enterReason.text()).to.contain('Enter Reason');
      });

      describe('when enter reason and click on Next', function () {
        beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
          this.wrapper.find('input.wizard-form-field-control').simulate('change', { target: { value: 'Some reason' } });
          this.wrapper.find('button[name="next"]').at(0).simulate('click');
        });

        it('shows confirmation step', function (this: $TSFixMe) {
          const enterReason = this.wrapper.find('.multi-step-wizard-breadcrumb-is-active');

          expect(enterReason.length).to.equal(1);
          expect(enterReason.text()).to.contain('Confirm Update');
        });

        describe('when the confirm button is clicked for the update and success', function () {
          beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
            const updateProcessArgsPromise = Promise.resolve({});
            this.updateProcessArgsStub = this.sandbox
              .stub(api.nds.admin, 'updateProcessArgs')
              .returns(updateProcessArgsPromise);

            this.wrapper.find('button[name="next"]').at(0).simulate('click');
          });

          it('makes a call to update the args', function (this: $TSFixMe) {
            const updatedShardArgs = getUpdatedShardArgs();
            const updatedClusterWideArgs = getUpdatedClusterWideArgs();

            expect(this.updateProcessArgsStub).to.have.been.calledWith('1', 'myCluster', {
              ...basicProcessArgsJson,
              shardArgs: JSON.parse(updatedShardArgs),
              clusterWideArgs: JSON.parse(updatedClusterWideArgs),
              reason: 'Some reason',
            });
          });

          it('shows the success message', function (this: $TSFixMe) {
            const success = this.wrapper.find('.text-is-success');

            expect(success.length).to.equal(1);
            expect(success.text()).to.contain(
              'Process args update submitted. Note that not all args may have been updated.'
            );
            expect(this.wrapper.find('.text-is-error').length).to.equal(0);
          });
        });

        describe('when the confirm button is clicked for the update and fail', function () {
          beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
            const rejection = Promise.reject({
              errorCode: 'SERVER_ERROR',
            });
            this.updateProcessArgsStub = this.sandbox.stub(api.nds.admin, 'updateProcessArgs').returns(rejection);
            this.wrapper.find('button[name="next"]').at(0).simulate('click');
            return rejection.catch(() => this.wrapper.update());
          });

          it('shows an error message', function (this: $TSFixMe) {
            const error = this.wrapper.find('.text-is-error');
            expect(error.length).to.equal(1);
            expect(error.text()).to.contain('Internal server error');
            expect(this.wrapper.find('.text-is-success').length).to.equal(0);
          });
        });
      });
    });
  });

  describe('when rendered with process args and is not editable', function (this: $TSFixMe) {
    this.timeout(10000);
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <UpdateProcessArgsForm
          groupId="1"
          clusterName="myCluster"
          isEnabled={false}
          processArgs={basicProcessArgsJson}
        />,
        { wrappingComponent: MockStoreWrappingComponent }
      );
    });

    confirmBasicElements();

    it('shows an update button', function (this: $TSFixMe) {
      const updateButton = this.wrapper.find('Button');
      expect(updateButton.length).to.equal(1);
      expect(updateButton.prop('disabled')).to.equal(true);
    });
  });

  describe('when rendered with process args and contains deleted args', function (this: $TSFixMe) {
    this.timeout(10000);
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <UpdateProcessArgsForm groupId="1" clusterName="myCluster" isEnabled processArgs={basicProcessArgsJson} />,
        { wrappingComponent: MockStoreWrappingComponent }
      );
      this.wrapper.setState({ hasDeletions: true });
    });

    confirmBasicElements();

    it('shows a clickable update button', function (this: $TSFixMe) {
      const updateButton = this.wrapper.find('Button');
      expect(updateButton.length).to.equal(1);
      expect(updateButton.prop('disabled')).to.equal(false);
    });

    describe('when click on Update button', function () {
      beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
        this.getProcessArgsPromiseStub = this.sandbox
          .stub(api.nds.admin, 'getProcessArgs')
          .returns(basicProcessArgsJson);
        this.wrapper.setState({ shardArgs: getUpdatedShardArgs() });
        this.wrapper.find('Button').simulate('click');
      });

      it('shows enter reason step', function (this: $TSFixMe) {
        const enterReason = this.wrapper.find('.multi-step-wizard-breadcrumb-is-active');

        expect(enterReason.length).to.equal(1);
        expect(enterReason.text()).to.contain('Enter Reason');
      });

      describe('when enter reason and click on Next', function () {
        beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
          this.wrapper.find('input.wizard-form-field-control').simulate('change', { target: { value: 'Some reason' } });
          this.wrapper.find('button[name="next"]').at(0).simulate('click');
        });

        it('shows confirmation step', function (this: $TSFixMe) {
          const enterReason = this.wrapper.find('.multi-step-wizard-breadcrumb-is-active');

          expect(enterReason.length).to.equal(1);
          expect(enterReason.text()).to.contain('Confirm Update');
        });

        describe('when confirmation step is clicked', function () {
          beforeEachNextCallstackWithUpdateWrapper(function (this: $TSFixMe) {
            this.wrapper.find('button[name="next"]').at(0).simulate('click');
          });

          it('shows clickable checkbox', function (this: $TSFixMe) {
            const checkbox = this.wrapper.find('input[type="checkbox"]');
            expect(checkbox.at(0).prop('disabled')).to.equal(false);
          });

          it('updates state when clicked', function (this: $TSFixMe) {
            const checkbox = this.wrapper.find('input[type="checkbox"]');
            checkbox.at(0).simulate('change', { target: { value: true } });
            expect(this.wrapper.instance().state.needsReboot).to.equal(true);
          });
        });
      });
    });
  });
});
