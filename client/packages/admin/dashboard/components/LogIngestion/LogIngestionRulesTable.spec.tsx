import Button from '@leafygreen-ui/button';
import { expect } from 'chai';
import { mount } from 'enzyme';
import sinon from 'sinon';

import LogIngestionRuleModal from '@packages/admin/dashboard/components/LogIngestion/LogIngestionRuleModal';
import LogIngestionRulesTable from '@packages/admin/dashboard/components/LogIngestion/LogIngestionRulesTable';
import LogIngestionRuleStats from '@packages/admin/dashboard/components/LogIngestion/LogIngestionRuleStats';
import {
  emptyStats,
  firstRuleFilter,
  logIngestionRules,
  nonmatchingFilter,
} from '@packages/admin/dashboard/fixtures/logIngestionFixtures';
import { LogIngestionRule } from '@packages/admin/dashboard/types/logIngestion';
import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import api from '@packages/common/services/api/nds/logIngestionApi';
import ConfirmationModal from '@packages/components/ConfirmationModal';
import CopyButton from '@packages/components/CopyButton';
import mongoDate from '@packages/date';

const BUTTON_COLUMN = 6;

const formatTime = (isoDate: $TSFixMe) => {
  return mongoDate(isoDate).tz('UTC').format('MM/DD/YY - hh:mm A');
};

const verifyDisplayedRuleQuery = (displayedQuery: string, rule: LogIngestionRule) => {
  if (rule.format === 'REGEX') {
    expect(displayedQuery).to.equal(rule.query);
  } else {
    // Rule has JSON format
    // Need to round-trip the JSON to compare since displayed query will be formatted
    const ruleJSON = JSON.stringify(JSON.parse(rule.query));
    const displayedJson = JSON.stringify(JSON.parse(displayedQuery));
    expect(displayedJson).to.equal(ruleJSON);
  }
};

describe('@packages/admin/dashboard/components/LogIngestion/LogIngestionRulesTable', function () {
  describe('when rendered with log ingestion rules and no filter', function () {
    beforeEach(function (this: $TSFixMe) {
      this.onRuleChangeStub = sinon.stub();
      this.getRuleStatsStub = sinon.stub(api, 'getRuleStats');
      this.getRuleStatsStub.returns(Promise.resolve(emptyStats));
      this.wrapper = mount(
        <LogIngestionRulesTable
          isGlobalAtlasAdmin={true}
          logIngestionRules={logIngestionRules}
          tzCode="UTC"
          providerOptions={clusterFixtures.getProviderOptions()}
          onRuleChange={this.onRuleChangeStub}
        />
      );
    });

    afterEach(function (this: $TSFixMe) {
      this.getRuleStatsStub.restore();
    });

    it('does not show the rule modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(LogIngestionRuleModal)).to.not.exist;
    });

    it('does not shows the deletion confirmation modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(ConfirmationModal)).to.not.exist;
    });

    it('shows 7 columns', function (this: $TSFixMe) {
      expect(this.wrapper.find('th').length).to.equal(7);
      expect(this.wrapper.find('th').at(0).text()).to.equal('Rule Name');
      expect(this.wrapper.find('th').at(1).text()).to.equal('Rule ID');
      expect(this.wrapper.find('th').at(2).text()).to.equal('Log Type');
      expect(this.wrapper.find('th').at(3).text()).to.equal('Requester');
      expect(this.wrapper.find('th').at(4).text()).to.equal('Created');
      expect(this.wrapper.find('th').at(5).text()).to.equal('Disabled?');
      expect(this.wrapper.find('th').at(BUTTON_COLUMN).text()).to.equal('Actions');
    });

    it(`shows ${logIngestionRules.length} rows with the appropriate values`, function (this: $TSFixMe) {
      const rows = this.wrapper.find('tbody tr');
      expect(rows.length).to.equal(logIngestionRules.length);

      rows.forEach((row: $TSFixMe, i: $TSFixMe) => {
        expect(row.find('td').at(0).text()).to.equal(logIngestionRules[i].name);
        expect(row.find('td').at(1).text()).to.equal(logIngestionRules[i]._id);
        expect(row.find('td').at(2).text()).to.equal(logIngestionRules[i].logType);
        expect(row.find('td').at(3).text()).to.equal(logIngestionRules[i].requester);
        expect(row.find('td').at(4).text()).to.equal(formatTime(logIngestionRules[i].created));

        expect(row.find('td').at(BUTTON_COLUMN).find(Button).length).to.equal(4);
        expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(0).text()).to.equal('Edit');
        expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(1).text()).to.equal('Stats');
        expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(2).text()).to.equal('Disable');
        expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(3).text()).to.equal('Delete');
      });
    });

    it('does not show any rule queries', function (this: $TSFixMe) {
      expect(this.wrapper.find('Query')).to.not.exist;
    });

    describe('when a rule is expanded', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find('Expander').at(0).simulate('click');
      });

      it('shows the query of the rule', function (this: $TSFixMe) {
        const text = this.wrapper.find(`td#query-${logIngestionRules[0]._id}`).text();
        const queryString = text.substring(0, text.indexOf('}') + 1);
        verifyDisplayedRuleQuery(queryString, logIngestionRules[0]);
      });

      it("does not show other rules' queries", function (this: $TSFixMe) {
        expect(this.wrapper.find('Query').length).to.equal(1);
      });

      it('shows a copy button to copy the query of the rule', function (this: $TSFixMe) {
        expect(this.wrapper.find(CopyButton)).to.exist;
        expect(this.wrapper.find(CopyButton).prop('copyableText')).to.equal(logIngestionRules[0].query);
      });

      describe('when another rule is expanded', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.find('Expander').at(1).simulate('click');
        });

        it('hides the query of the first rule', function (this: $TSFixMe) {
          expect(this.wrapper.find(`td#query-${logIngestionRules[0]._id}`)).to.not.exist;
        });

        it('shows the query of the rule', function (this: $TSFixMe) {
          const text = this.wrapper.find(`td#query-${logIngestionRules[1]._id}`).text();
          const queryString = text.substring(0, text.indexOf(')$') + 2);
          verifyDisplayedRuleQuery(queryString, logIngestionRules[1]);
        });

        it("does not show other rules' queries", function (this: $TSFixMe) {
          expect(this.wrapper.find('Query').length).to.equal(1);
        });

        it('shows a copy button to copy the query of the rule', function (this: $TSFixMe) {
          expect(this.wrapper.find(CopyButton)).to.exist;
          expect(this.wrapper.find(CopyButton).prop('copyableText')).to.equal(logIngestionRules[1].query);
        });
      });
    });

    describe('when "Edit" is clicked for a rule', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find('tbody tr').at(0).find(Button).at(0).simulate('click');
      });

      it('shows the rule modal', function (this: $TSFixMe) {
        expect(this.wrapper.find(LogIngestionRuleModal)).to.exist;
      });
    });

    describe('when "Stats" is clicked for a rule', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find('tbody tr').at(0).find(Button).at(1).simulate('click');
        return Promise.resolve().then(() => this.wrapper.update());
      });

      it('shows the rule stats', function (this: $TSFixMe) {
        expect(this.wrapper.find(LogIngestionRuleStats)).to.exist;
      });
    });

    describe('when "Delete" is clicked for a rule', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find('tbody tr').at(0).find(Button).at(3).simulate('click');
      });

      it('shows the confirmation modal', function (this: $TSFixMe) {
        expect(this.wrapper.find(ConfirmationModal)).to.exist;
      });

      describe('and the user confirms the deletion', function () {
        beforeEach(function (this: $TSFixMe) {
          this.deleteRuleStub = sinon.stub(api, 'deleteRule');
          this.deleteRuleStub.returns(
            new Promise<void>((res) => {
              res();
            })
          );

          this.wrapper.find(ConfirmationModal).find(Button).last().simulate('click');
          return Promise.resolve();
        });

        afterEach(function (this: $TSFixMe) {
          this.deleteRuleStub.restore();
        });

        it('makes the call to delete the rule', function (this: $TSFixMe) {
          expect(this.deleteRuleStub).to.have.been.calledWith(logIngestionRules[0]._id);
        });

        it('makes the call to the rule deletion callback', function (this: $TSFixMe) {
          expect(this.onRuleChangeStub).to.have.been.called;
        });
      });
    });
  });

  describe('when rendered with log ingestion rules and a filter matching the first rule', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <LogIngestionRulesTable
          isGlobalAtlasAdmin={true}
          logIngestionRules={logIngestionRules}
          filter={firstRuleFilter}
          tzCode="UTC"
          providerOptions={clusterFixtures.getProviderOptions()}
          onRuleChange={this.onRuleChangeStub}
        />
      );
    });

    it('does not show the rule modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(LogIngestionRuleModal)).to.not.exist;
    });

    it('does not shows the deletion confirmation modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(ConfirmationModal)).to.not.exist;
    });

    it('shows 7 columns', function (this: $TSFixMe) {
      expect(this.wrapper.find('th').length).to.equal(7);
      expect(this.wrapper.find('th').at(0).text()).to.equal('Rule Name');
      expect(this.wrapper.find('th').at(1).text()).to.equal('Rule ID');
      expect(this.wrapper.find('th').at(2).text()).to.equal('Log Type');
      expect(this.wrapper.find('th').at(3).text()).to.equal('Requester');
      expect(this.wrapper.find('th').at(4).text()).to.equal('Created');
      expect(this.wrapper.find('th').at(5).text()).to.equal('Disabled?');
      expect(this.wrapper.find('th').at(BUTTON_COLUMN).text()).to.equal('Actions');
    });

    it(`shows 1 row with the appropriate values`, function (this: $TSFixMe) {
      const rows = this.wrapper.find('tbody tr');
      expect(rows.length).to.equal(1);

      const row = rows.at(0);

      expect(row.find('td').at(0).text()).to.equal(logIngestionRules[0].name);
      expect(row.find('td').at(1).text()).to.equal(logIngestionRules[0]._id);
      expect(row.find('td').at(2).text()).to.equal(logIngestionRules[0].logType);
      expect(row.find('td').at(3).text()).to.equal(logIngestionRules[0].requester);
      expect(row.find('td').at(4).text()).to.equal(formatTime(logIngestionRules[0].created));

      expect(row.find('td').at(BUTTON_COLUMN).find(Button).length).to.equal(4);
      expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(0).text()).to.equal('Edit');
      expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(1).text()).to.equal('Stats');
      expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(2).text()).to.equal('Disable');
      expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(3).text()).to.equal('Delete');
    });
  });

  describe('when rendered with log ingestion rules and a filter not matching any rule', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <LogIngestionRulesTable
          isGlobalAtlasAdmin={true}
          logIngestionRules={logIngestionRules}
          filter={nonmatchingFilter}
          tzCode="UTC"
          providerOptions={clusterFixtures.getProviderOptions()}
          onRuleChange={this.onRuleChangeStub}
        />
      );
    });

    it('does not show the rule modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(LogIngestionRuleModal)).to.not.exist;
    });

    it('does not shows the deletion confirmation modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(ConfirmationModal)).to.not.exist;
    });

    it('shows 7 columns', function (this: $TSFixMe) {
      expect(this.wrapper.find('th').length).to.equal(7);
      expect(this.wrapper.find('th').at(0).text()).to.equal('Rule Name');
      expect(this.wrapper.find('th').at(1).text()).to.equal('Rule ID');
      expect(this.wrapper.find('th').at(2).text()).to.equal('Log Type');
      expect(this.wrapper.find('th').at(3).text()).to.equal('Requester');
      expect(this.wrapper.find('th').at(4).text()).to.equal('Created');
      expect(this.wrapper.find('th').at(5).text()).to.equal('Disabled?');
      expect(this.wrapper.find('th').at(BUTTON_COLUMN).text()).to.equal('Actions');
    });

    it('shows 0 rows', function (this: $TSFixMe) {
      expect(this.wrapper.find('tbody tr')).to.not.exist;
    });

    it('shows the empty text', function (this: $TSFixMe) {
      expect(this.wrapper.find('EmptyText').text()).to.equal('No log ingestion rules match the filters');
    });
  });

  describe('when rendered without log ingestion rules', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <LogIngestionRulesTable
          isGlobalAtlasAdmin={true}
          logIngestionRules={[]}
          tzCode="UTC"
          providerOptions={clusterFixtures.getProviderOptions()}
          onRuleChange={this.onRuleChangeStub}
        />
      );
    });

    it('does not show the rule modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(LogIngestionRuleModal)).to.not.exist;
    });

    it('does not shows the deletion confirmation modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(ConfirmationModal)).to.not.exist;
    });

    it('shows 7 columns', function (this: $TSFixMe) {
      expect(this.wrapper.find('th').length).to.equal(7);
      expect(this.wrapper.find('th').at(0).text()).to.equal('Rule Name');
      expect(this.wrapper.find('th').at(1).text()).to.equal('Rule ID');
      expect(this.wrapper.find('th').at(2).text()).to.equal('Log Type');
      expect(this.wrapper.find('th').at(3).text()).to.equal('Requester');
      expect(this.wrapper.find('th').at(4).text()).to.equal('Created');
      expect(this.wrapper.find('th').at(5).text()).to.equal('Disabled?');
      expect(this.wrapper.find('th').at(BUTTON_COLUMN).text()).to.equal('Actions');
    });

    it('shows 0 rows', function (this: $TSFixMe) {
      expect(this.wrapper.find('tbody tr')).to.not.exist;
    });

    it('shows the empty text', function (this: $TSFixMe) {
      expect(this.wrapper.find('EmptyText').text()).to.equal('No log ingestion rules have been created');
    });
  });

  describe('when rendered with isGlobalAtlasAdmin false', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = mount(
        <LogIngestionRulesTable
          isGlobalAtlasAdmin={false}
          logIngestionRules={logIngestionRules}
          tzCode="UTC"
          providerOptions={clusterFixtures.getProviderOptions()}
          onRuleChange={this.onRuleChangeStub}
        />
      );
    });

    it(`shows ${logIngestionRules.length} rows with only view and stats buttons`, function (this: $TSFixMe) {
      const rows = this.wrapper.find('tbody tr');
      expect(rows.length).to.equal(logIngestionRules.length);

      rows.forEach((row: $TSFixMe, i: $TSFixMe) => {
        expect(row.find('td').at(0).text()).to.equal(logIngestionRules[i].name);
        expect(row.find('td').at(1).text()).to.equal(logIngestionRules[i]._id);
        expect(row.find('td').at(2).text()).to.equal(logIngestionRules[i].logType);
        expect(row.find('td').at(3).text()).to.equal(logIngestionRules[i].requester);
        expect(row.find('td').at(4).text()).to.equal(formatTime(logIngestionRules[i].created));

        expect(row.find('td').at(BUTTON_COLUMN).find(Button).length).to.equal(2);
        expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(0).text()).to.equal('View');
        expect(row.find('td').at(BUTTON_COLUMN).find(Button).at(1).text()).to.equal('Stats');
      });
    });

    it('when "View" is clicked for a rule, shows the rule modal', function (this: $TSFixMe) {
      this.wrapper.find('tbody tr').at(0).find(Button).at(0).simulate('click');
      expect(this.wrapper.find(LogIngestionRuleModal)).to.exist;
    });
  });
});
