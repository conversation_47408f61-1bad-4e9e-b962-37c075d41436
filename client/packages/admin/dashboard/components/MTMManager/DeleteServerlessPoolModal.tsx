import { useState } from 'react';

import { css } from '@emotion/react';
import Button from '@leafygreen-ui/button';

import { exceptionToMessage } from '@packages/common/services/errorHelper';
import Modal from '@packages/components/Modal';
import { InputErrorMessage } from '@packages/components/styles/common';

interface DeleteServerlessPoolModalProps {
  onClose: () => void;
  onConfirm: () => void;
  groupName: string;
  poolId: string;
  deletePool: (poolId: string) => Promise<void>;
}

const styles = {
  marginAround: css`
    margin: 8px;
  `,
  toggleButton: css`
    display: flex;
    flex-direction: row;
    margin-top: 8px;
  `,
  marginRight: css`
    margin-right: 8px;
  `,
};

export default function DeleteServerlessPoolModal({
  onClose,
  onConfirm,
  groupName,
  poolId,
  deletePool,
}: DeleteServerlessPoolModalProps) {
  const [removeError, setRemoveError] = useState('');

  const onCloseHandler = () => {
    setRemoveError('');
    onClose();
  };

  const onClickDelete = async () => {
    try {
      await deletePool(poolId);
      onConfirm();
      onCloseHandler();
    } catch (error) {
      setRemoveError(exceptionToMessage(error));
    }
  };

  const titleContent = `Delete Serverless Pool`;

  return (
    <Modal title={titleContent} size="medium" onClose={onCloseHandler}>
      <div css={styles.marginAround}>
        Are you sure you want to delete the serverless pool <strong>{poolId}</strong> associated with group{' '}
        <strong>{groupName}</strong>? This will delete the Serverless Load Balancing Deployment, Tenant Endpoint, and
        Pre-Allocated records associated with this project.
      </div>
      <Modal.ModalFooter>
        <Button css={styles.marginAround} name="cancel" onClick={onCloseHandler}>
          Cancel
        </Button>
        <Button css={styles.marginAround} variant="danger" name="deleteServerlessPool" onClick={onClickDelete}>
          Delete
        </Button>
      </Modal.ModalFooter>
      {removeError && (
        <InputErrorMessage data-test-id="removeError">
          <span className="fa fa-exclamation-triangle" /> {removeError}
        </InputErrorMessage>
      )}
    </Modal>
  );
}
