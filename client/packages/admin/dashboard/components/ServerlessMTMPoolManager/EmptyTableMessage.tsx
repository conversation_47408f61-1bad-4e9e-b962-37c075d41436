import { css } from '@emotion/react';

const styles = {
  text: css`
    text-align: center;
    font-weight: 200;
    font-size: 15px;
  `,
  container: css`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
  `,
};
// re-use component for consistent styling with serverless profile table
export default function EmptyTableMessage({ children }: $TSFixMe) {
  return (
    <div css={styles.container}>
      <div css={styles.text}>{children}</div>
    </div>
  );
}
