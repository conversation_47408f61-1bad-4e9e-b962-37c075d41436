interface Props {
  openAvailabilityIssues: Array<OpenAvailabilityZoneIssue>;
  openAvailabilityIssuesError?: string;
}

export interface OpenAvailabilityZoneIssue {
  createdAt: string;
  updatedAt: string;
  cloudProvider: string;
  region: string;
  availabilityZone: string;
}

const NDSCloudProviderCapacityOpenAZIssuesTable = ({ openAvailabilityIssues, openAvailabilityIssuesError }: Props) => {
  const getOpenAvailabilityZoneIssuesTable = () => {
    if (!openAvailabilityIssues || openAvailabilityIssues.length === 0) {
      return (
        <tbody>
          <tr>
            <td className="table-empty-container" colSpan={5}>
              <em>There are no cloud provider availability zone issues.</em>
            </td>
          </tr>
        </tbody>
      );
    }
    return (
      <tbody>
        {openAvailabilityIssues.map(
          ({ cloudProvider, region, availabilityZone, createdAt, updatedAt }: OpenAvailabilityZoneIssue) => {
            const key = `${cloudProvider}-${region}-${availabilityZone}-openAvailabilityZoneIssue`;
            return (
              <tr key={key} data-testid={key}>
                <td className="plain-table-cell">{cloudProvider}</td>
                <td className="plain-table-cell">{region}</td>
                <td className="plain-table-cell">{availabilityZone}</td>
                <td className="plain-table-cell">{createdAt}</td>
                <td className="plain-table-cell">{updatedAt}</td>
              </tr>
            );
          }
        )}
      </tbody>
    );
  };
  return (
    <>
      <p className="error" style={{ color: 'red' }}>
        {openAvailabilityIssuesError}
      </p>
      <table className="plain-table">
        <thead>
          <tr className="plain-table-header-row">
            <th className="plain-table-column-header">Cloud Provider</th>
            <th className="plain-table-column-header">Region</th>
            <th className="plain-table-column-header">Availability Zone</th>
            <th className="plain-table-column-header">Created At</th>
            <th className="plain-table-column-header">Updated At</th>
          </tr>
        </thead>
        {getOpenAvailabilityZoneIssuesTable()}
      </table>
    </>
  );
};

export default NDSCloudProviderCapacityOpenAZIssuesTable;
