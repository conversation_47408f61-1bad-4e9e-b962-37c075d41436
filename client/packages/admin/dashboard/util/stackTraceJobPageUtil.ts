import { StackTraceInstance } from '@packages/types/dashboard/stackTraceJobs';

export function getInstanceLabel(instance: StackTraceInstance): string {
  let values = [
    'hostname ' + instance.hostname,
    'hostIdentifier ' + instance.hostIdentifier,
    'runType ' + instance.runType,
  ];
  if (!!instance.runId) {
    values.push('runId ' + instance.runId);
  }
  if (!!instance.mmsRole) {
    values.push('mmsRole ' + instance.mmsRole);
  }
  if (!!instance.instanceId) {
    values.push('instanceId ' + instance.instanceId);
  }
  return values.join(':');
}

export function buildUrlParametersFromObjectKeys(object: any, urlParameters: ReadonlyArray<string>) {
  return urlParameters
    .map((key) => {
      return key + '=' + (!!object[key] ? object[key] : '');
    })
    .join('&');
}

export const ADMIN_STACK_TRACE_PREFIX_URL = 'admin#/general/stackTrace/';
