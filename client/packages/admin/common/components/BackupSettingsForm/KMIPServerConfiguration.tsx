// @ts-expect-error TS(7016): Could not find a declaration file for module 'conn... Remove this comment to see the full error message
import { connectBackboneToReact } from 'connect-backbone-to-react';

import { type WizardInput } from '@packages/types/wizardInput';

import WizardFormFieldInput from '@packages/components/WizardFormField/Input';

import { WizardFormFieldset, WizardFormLegend } from '../FormStyles';

interface Props {
  backupKmipServerCAFile: WizardInput;
  backupKmipServerHost: WizardInput;
  backupKmipServerPort: WizardInput;
  onChange: (key: string, val: string) => void;
  validationErrors: Record<string, string>;
}

const PITRestore = ({
  backupKmipServerCAFile,
  backupKmipServerHost,
  backupKmipServerPort,
  onChange,
  validationErrors,
}: Props) => (
  <WizardFormFieldset>
    <WizardFormLegend>PIT Restore</WizardFormLegend>
    <WizardFormFieldInput
      inputLabel="KMIP Server Host"
      description="Specify the hostname of the KMIP server."
      restartRequired
      example="kmip.example.com"
      inputId="backupKmipServerHost"
      inputValue={backupKmipServerHost.value}
      onInputChange={(value) => onChange('backupKmipServerHost', value)}
      overrides={backupKmipServerHost.overrides}
      error={validationErrors.backupKmipServerHost}
    />
    <WizardFormFieldInput
      inputLabel="KMIP Server Port"
      description="Specify the port of the KMIP server."
      restartRequired
      inputId="backupKmipServerPort"
      inputValue={backupKmipServerPort.value}
      onInputChange={(value) => onChange('backupKmipServerPort', value)}
      overrides={backupKmipServerPort.overrides}
      error={validationErrors.backupKmipServerPort}
    />
    <WizardFormFieldInput
      inputLabel="KMIP Server CA File"
      description="A file containing one or more trusted certificates in PEM format. Required when using the KMIP server."
      restartRequired
      example="/etc/security/ca.pem"
      inputId="backupKmipServerCAFile"
      inputValue={backupKmipServerCAFile.value}
      onInputChange={(value) => onChange('backupKmipServerCAFile', value)}
      overrides={backupKmipServerCAFile.overrides}
      error={validationErrors.backupKmipServerCAFile}
    />
  </WizardFormFieldset>
);

export default connectBackboneToReact(({ model }: $TSFixMe) => ({
  backupKmipServerHost: model.get('backupKmipServerHost'),
  backupKmipServerPort: model.get('backupKmipServerPort'),
  backupKmipServerCAFile: model.get('backupKmipServerCAFile'),
  validationErrors: model.validationError || {},
}))(PITRestore);
