import Backbone from 'backbone';
import _ from 'underscore';

import BackupSettingsForm from './BackupSettingsForm';
import MiscellaneousSettingsForm from './MiscellaneousSettingsForm';
import UserAuthenticationSettingsForm from './UserAuthenticationSettingsForm';
import WebServerAndEmailSettingsForm from './WebServerAndEmailSettingsForm';

const FEATURE_FLAG_VALID_VALUES = new Set(['enabled', 'controlled', 'disabled']);

export interface KeyValuePair {
  key: string;
  value: string | null;
  isSecret?: boolean;
  isHashed: boolean;
  existsOnServer?: boolean;
}

export interface CustomSettingsFormModel extends Backbone.Model<{ pairs: Array<KeyValuePair> }> {
  updateKeyAt(index: number, key: string): void;
  updateValueAt(index: number, value: string): void;
  addEmptyPair(): void;
  removePairAt(index: number): void;
}

const CustomSettingsForm = Backbone.Model.extend(
  {
    validate(attrs: $TSFixMe) {
      const errors: $TSFixMe = {};

      const invalidKeys = this.getInvalidKeysByTab(attrs);
      if (invalidKeys.length) {
        const invalidKeysError = {
          message: `The following keys must be set on the previous forms:`,
          subErrors: [],
        };
        // @ts-expect-error TS(7031): Binding element 'keys' implicitly has an 'any' typ... Remove this comment to see the full error message
        invalidKeys.forEach(([keys, tabName]) => {
          keys.forEach((key: $TSFixMe) => {
            // @ts-expect-error TS(2345): Argument of type 'string' is not assignable to par... Remove this comment to see the full error message
            invalidKeysError.subErrors.push(`${key} can be set on tab '${tabName}'`);
          });
        });

        errors.errors = [invalidKeysError];
      }

      const duplicatedKeys = this.getDuplicatedKeys(attrs);
      if (duplicatedKeys.length) {
        if (errors.errors === undefined) {
          errors.errors = [];
        }
        errors.errors.push({
          message: `The following keys are duplicated: ${duplicatedKeys.join(', ')}`,
        });
      }

      const invalidFeatureFlagValues = this.getInvalidFeatureFlagValues(attrs);
      if (invalidFeatureFlagValues.length) {
        if (errors.errors === undefined) {
          errors.errors = [];
        }
        errors.errors.push({
          message: `The following feature flag keys have invalid values: ${invalidFeatureFlagValues.join(
            ', '
          )}.  Must use enabled, controlled or disabled`,
        });
      }

      if (Object.keys(errors).length > 0) {
        return errors;
      }
    },

    getCustomPropertyKeys(attrs: $TSFixMe) {
      // filter out keys with whitespace so multiple empty fields don't cause errors
      // & filter out keys which have been deleted (holds a value of null).
      return attrs.pairs
        .filter((pair: $TSFixMe) => pair.key.trim() !== '' && pair.value !== null)
        .map((pair: $TSFixMe) => pair.key);
    },

    getInvalidKeysByTab(attrs: $TSFixMe) {
      const invalidKeyTabPairs: Array<$TSFixMe> = [];
      const customKeys = this.getCustomPropertyKeys(attrs);
      const allKeysPerTab = CustomSettingsForm.getKnownKeysByTab(this.tabNames);
      // @ts-expect-error TS(7031): Binding element 'keysForTab' implicitly has an 'an... Remove this comment to see the full error message
      allKeysPerTab.forEach(([keysForTab, tabName]) => {
        const invalidKeys = _.intersection(keysForTab, customKeys);
        if (invalidKeys.length) {
          invalidKeyTabPairs.push([invalidKeys, tabName]);
        }
      });
      return invalidKeyTabPairs;
    },

    getDuplicatedKeys(attrs: $TSFixMe) {
      const customKeys = this.getCustomPropertyKeys(attrs);
      const counts = _.countBy(customKeys);
      return Object.keys(counts).filter((key) => counts[key] > 1);
    },

    getInvalidFeatureFlagValues(attrs: $TSFixMe) {
      return attrs.pairs
        .filter(
          (pair: $TSFixMe) =>
            /^mms\.featureFlag\./.test(pair.key) && pair.value && !FEATURE_FLAG_VALID_VALUES.has(pair.value)
        )
        .map((pair: $TSFixMe) => pair.key);
    },

    toJSON() {
      const json: $TSFixMe = {};
      this.get('pairs').forEach((pair: $TSFixMe) => {
        if (pair.key) {
          if (!json[pair.key]) {
            json[pair.key] = {
              value: pair.value,
              isHashed: pair.isHashed,
            };
          }
        }
      });

      return json;
    },

    initialize() {
      if (!this.get('pairs')) {
        this.set('pairs', []);
      }

      // ensure at least one empty pair
      this.addEmptyPair();
    },

    addEmptyPair() {
      this.set('pairs', [
        ...this.get('pairs'),
        {
          key: '',
          value: '',
          isHashed: false,
        },
      ]);
    },

    updateKeyAt(index: number, key: string) {
      this.updatePairAt(index, { key });
    },

    updateValueAt(index: number, value: string) {
      this.updatePairAt(index, {
        value,
        isHashed: false, // no longer obfuscated
      });
    },

    removePairAt(index: number) {
      // Settings are deleted in mms when set with a value of null
      this.updatePairAt(index, { value: null });
    },

    updatePairAt(index: number, updates: Partial<KeyValuePair>) {
      const pair = this.get('pairs')[index];

      Object.keys(updates).forEach((propertyName) => {
        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
        pair[propertyName] = updates[propertyName];
      });

      this.trigger(`pairChange:${index}`, pair);
    },
  },
  {
    getKnownKeys() {
      return [].concat(
        // @ts-expect-error TS(2769): No overload matches this call.
        _.values(WebServerAndEmailSettingsForm.PROPERTY_MAP),
        _.values(UserAuthenticationSettingsForm.PROPERTY_MAP),
        _.values(BackupSettingsForm.PROPERTY_MAP),
        _.values(MiscellaneousSettingsForm.PROPERTY_MAP)
      );
    },

    getKnownKeysByTab(tabNames: $TSFixMe) {
      return [
        [_.values(WebServerAndEmailSettingsForm.PROPERTY_MAP), tabNames.WEB_SERVER_EMAIL],
        [_.values(UserAuthenticationSettingsForm.PROPERTY_MAP), tabNames.USER_AUTH],
        [_.values(BackupSettingsForm.PROPERTY_MAP), tabNames.BACKUP],
        [_.values(MiscellaneousSettingsForm.PROPERTY_MAP), tabNames.MISCELLANEOUS],
      ];
    },

    fromAppSettings(appSettings: $TSFixMe) {
      // extract any keys we haven't handled from dbProperties
      const unhandledDbProperties = _.omit(appSettings.getDbProperties(), this.getKnownKeys());
      const pairs = Object.keys(unhandledDbProperties).map((key) => {
        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
        const value = unhandledDbProperties[key];
        const isSecret = appSettings.isSecretProperty(key);
        const isHashed = isSecret; // isHashed will be reset when the value is modified
        const existsOnServer = true;
        return { key, value, isSecret, isHashed, existsOnServer };
      });

      return new CustomSettingsForm({ pairs });
    },
  }
);

export default CustomSettingsForm;
