import Backbone from 'backbone';

import * as settingsFormUtils from '@packages/admin/common/util/settingsFormUtils';
import validators from '@packages/admin/common/util/validators';

import AppSettings from './AppSettings';

const WebServerAndEmailSettingsForm = Backbone.Model.extend(
  {
    validate(attrs: $TSFixMe) {
      const errors: $TSFixMe = {};

      // Adapted from https://mathiasbynens.be/demo/url-regex @gruber v2
      const urlRegexString =
        '\\b((?:[a-z][\\w-]+:(?:\\/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+' +
        '[.][a-z]{2,4}\\/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+' +
        '(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))';
      validators.ensureNotBlank(attrs, errors, 'centralUrl');
      validators.ensureMatches(attrs, errors, 'centralUrl', new RegExp(urlRegexString, 'i'), 'Must be a valid url.');
      validators.ensureNotBlank(attrs, errors, 'fromEmailAddr');
      validators.ensureNotBlank(attrs, errors, 'replyToEmailAddr');
      validators.ensureNotBlank(attrs, errors, 'adminEmailAddr');
      validators.ensureNotBlank(attrs, errors, 'emailDaoClass');

      if (attrs.clientCertificateMode && attrs.clientCertificateMode.value) {
        validators.ensureInSet(attrs, errors, 'clientCertificateMode', [
          AppSettings.CLIENT_CERT_MODE_NONE,
          AppSettings.CLIENT_CERT_MODE_AGENTS,
          AppSettings.CLIENT_CERT_MODE_REQUIRED,
        ]);
      }

      if (attrs.emailDaoClass && attrs.emailDaoClass.value) {
        validators.ensureInSet(attrs, errors, 'emailDaoClass', [
          AppSettings.JAVA_EMAIL_DAO,
          AppSettings.AWS_EMAIL_DAO,
          AppSettings.AWS_MAILER,
          AppSettings.SIMPLE_MAILER,
        ]);

        if (
          attrs.emailDaoClass.value === AppSettings.JAVA_EMAIL_DAO ||
          attrs.emailDaoClass.value === AppSettings.SIMPLE_MAILER
        ) {
          validators.ensureNotBlank(attrs, errors, 'mailTransport');
          validators.ensureInSet(attrs, errors, 'mailTransport', ['smtp', 'smtps']);
          validators.ensureNotBlank(attrs, errors, 'mailHostname');
          validators.ensureNotBlank(attrs, errors, 'mailPort');
          validators.ensurePositiveInteger(attrs, errors, 'mailPort');

          validators.ensureInSet(attrs, errors, 'mailTls', ['true', 'false']);
        }

        if (
          attrs.emailDaoClass.value === AppSettings.AWS_EMAIL_DAO ||
          attrs.emailDaoClass.value === AppSettings.AWS_MAILER
        ) {
          validators.ensureNotBlank(attrs, errors, 'awsAccessKey');
          validators.ensureNotBlank(attrs, errors, 'awsSecretKey');
          validators.ensureNotBlank(attrs, errors, 'awsSesEndpoint');
        }
      }

      if (Object.keys(errors).length > 0) {
        validators.ensureDefaultError(errors);
        return errors;
      }
    },

    toJSON() {
      return settingsFormUtils.convertModelKeysToPropertyKeys(
        this.attributes,
        WebServerAndEmailSettingsForm.PROPERTY_MAP
      );
    },
  },
  {
    // map of model keys to property file keys
    PROPERTY_MAP: {
      centralUrl: 'mms.centralUrl',
      httpsPEMKeyFile: 'mms.https.PEMKeyFile',
      httpsPEMKeyFilePassword: 'mms.https.PEMKeyFilePassword',
      clientCertificateMode: 'mms.https.ClientCertificateMode',
      caFile: 'mms.https.CAFile',
      remoteIpHeader: 'mms.remoteIp.header',

      // Email
      fromEmailAddr: 'mms.fromEmailAddr',
      replyToEmailAddr: 'mms.replyToEmailAddr',
      adminEmailAddr: 'mms.adminEmailAddr',
      emailDaoClass: 'mms.emailDaoClass',

      // Email (Java)
      mailTransport: 'mms.mail.transport',
      mailHostname: 'mms.mail.hostname',
      mailPort: 'mms.mail.port',
      mailUsername: 'mms.mail.username',
      mailPassword: 'mms.mail.password',
      mailTls: 'mms.mail.tls',

      // Email (AWS)
      awsAccessKey: 'aws.accesskey',
      awsSecretKey: 'aws.secretkey',
      awsSesEndpoint: 'aws.ses.endpoint',
    },

    fromAppSettings(appSettings: $TSFixMe) {
      const attrs = settingsFormUtils.buildAttributesFromAppSettings(appSettings, this.PROPERTY_MAP);
      return new WebServerAndEmailSettingsForm(attrs);
    },
  }
);

export default WebServerAndEmailSettingsForm;
