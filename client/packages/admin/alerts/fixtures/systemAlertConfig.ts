import { AlertConfig } from '@packages/types/alerts/alertConfigs/AlertConfig';
import { AlertConfigMetricType } from '@packages/types/alerts/alertConfigs/AlertConfig/AlertConfigMetricType';
import { AlertConfigType } from '@packages/types/alerts/alertConfigs/AlertConfig/AlertConfigType';
import { EventType } from '@packages/types/alerts/alertConfigs/AlertConfig/EventType';
import { ThresholdMode } from '@packages/types/alerts/alertConfigs/AlertConfig/Threshold/ThresholdMode';
import { ThresholdOperation } from '@packages/types/alerts/alertConfigs/AlertConfig/Threshold/ThresholdOperation';
import { ThresholdUnit } from '@packages/types/alerts/alertConfigs/AlertConfig/Threshold/ThresholdUnit';
import { NotificationType } from '@packages/types/alerts/alertConfigs/Notification';
import { Scope } from '@packages/types/alerts/Scope';
import { GroupType } from '@packages/types/groups';
import { GroupRole } from '@packages/types/roles';

export const backupBehindConfig: Array<AlertConfig> = [
  {
    _t: AlertConfigType.BACKUP,
    upd: new Date(),
    ac: Scope.System,
    cid: '1',
    tags: [],
    groups: [],
    cre: '2023-09-27T20:16:44Z',
    enabled: true,
    et: EventType.OPLOG_BEHIND,
    forAllGroups: false,
    id: '1',
    notify: [
      {
        delay: 0,
        id: '0',
        interval: 60,
        _t: NotificationType.ADMIN,
      },
    ],
  },
];

export const hostMetricAlertConfig: Array<AlertConfig> = [
  {
    _t: AlertConfigType.HOST_METRIC,
    upd: new Date(),
    tags: [],
    groups: [],
    ac: Scope.System,
    cid: '1',
    cre: '2023-09-27T20:16:44Z',
    enabled: true,
    et: EventType.OUTSIDE_METRIC_THRESHOLD,
    forAllGroups: false,
    groupType: GroupType.NDS,
    mt: {
      metric: AlertConfigMetricType.SEARCH_MAX_NUMBER_OF_LUCENE_DOCS,
      mode: ThresholdMode.AVERAGE,
      op: ThresholdOperation.GREATER_THAN,
      threshold: 1,
      units: ThresholdUnit.BILLION,
    },
    id: '1',
    notify: [
      {
        cid: '2',
        delay: 360,
        email: true,
        gn: 'groupname',
        interval: 1440,
        roles: [GroupRole.GROUP_OWNER],
        _t: NotificationType.GROUP,
      },
    ],
  },
];
