import { AdminAlert, AdminAlertType } from '@packages/types/alerts/adminAlert';

interface TargetProps {
  alert: AdminAlert;
}

export function AdminTarget({ alert }: TargetProps) {
  if (alert.et === AdminAlertType.ADMIN_CLUSTER_LOCK_ACQUIRED) {
    return (
      <span>
        <a href={`/v2/${alert.cid}#/clusters/detail/${alert.clusterName}`}>{alert.clusterName}</a> is locked by{' '}
        {alert.userName} due to {alert.reason}
      </span>
    );
  }

  return (
    <span>
      <a href={`/v2/${alert.cid}#/clusters/detail/${alert.clusterName}`}>{alert.clusterName}</a>
    </span>
  );
}
