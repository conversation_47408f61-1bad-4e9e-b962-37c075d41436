import { Alert } from '@packages/types/alerts/alerts';

// helper
import { systemTargetFactory } from '@packages/admin/alerts/components/Alerts/systemTargetFactory';
import { isOpenAlert } from '@packages/admin/alerts/components/util/alertHelper';
import Dropdown from '@packages/components/Dropdown';
import FormatDateTime from '@packages/components/FormatDateTime';
import SplashView from '@packages/components/SplashView';
import Table from '@packages/components/Table';

interface SystemAlertsTableProps {
  onPrevPageClick: () => void;
  onNextPageClick: () => void;
  onEdit: (alertToModify: Alert) => void;
  data: ReadonlyArray<Alert>;
  firstPage?: boolean;
  lastPage?: boolean;
}

export function SystemAlertsTable({
  onPrevPageClick,
  onNextPageClick,
  firstPage,
  lastPage,
  data,
  onEdit,
}: SystemAlertsTableProps) {
  return (
    <Table
      data={data}
      hasPreviousPage={!firstPage}
      hasNextPage={!lastPage}
      empty={<SplashView hasNoBorder headlineText="There are currently no alerts to show" />}
      rowClassName={(alert: Alert) => (isOpenAlert(alert) ? 'alert-danger' : undefined)}
      serverPagination
      onPrevPageClick={onPrevPageClick}
      onNextPageClick={onNextPageClick}
    >
      <Table.Column header="Description" accessor="description" sortable />
      <Table.Column
        header="Target"
        sortable
        cell={(cellProps: { data: Alert }) => (
          <Table.Cell key="target">{systemTargetFactory(cellProps.data)}</Table.Cell>
        )}
      />
      <Table.Column
        header="Opened"
        sortable
        cell={(cellProps: { data: Alert }) => (
          <Table.Cell key="created">
            <FormatDateTime isoString={new Date(cellProps.data.cre).toISOString()} />
          </Table.Cell>
        )}
      />
      <Table.Column
        header="Last Notified On"
        sortable
        cell={(cellProps: { data: Alert }) => (
          <Table.Cell key="lnd">
            {cellProps.data.lnd && <FormatDateTime isoString={new Date(cellProps.data.lnd).toISOString()} />}
          </Table.Cell>
        )}
      />
      <Table.Column header="Acknowledged By" accessor="acknowledgingUsername" sortable />
      <Table.Column
        header="Actions"
        cell={(cellProps: { data: Alert }) => (
          <Table.Cell key="actions">
            <Dropdown
              options={[
                {
                  text: isOpenAlert(cellProps.data) ? 'Acknowledge' : 'Mark as unacknowledged',
                  onClick: () => {
                    onEdit(cellProps.data);
                  },
                },
              ]}
              placement="left"
              buttonType="button"
              buttonClassName="button-is-xs"
              withEllipsis
              isButton
            />
          </Table.Cell>
        )}
      />
    </Table>
  );
}
