import { FormEvent } from 'react';

import { css } from '@emotion/css';
import styled from '@emotion/styled';
import queryString from 'query-string';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'reac... Remove this comment to see the full error message
import { <PERSON> } from 'react-router-dom';

import { FieldNames } from '@packages/types/auth/field';
import { ReCaptchaAction } from '@packages/types/auth/ReCaptchaAction';
import { Search } from '@packages/types/auth/search';
import { SocialAuthMethods } from '@packages/types/authMethod';
import { SignupMethod } from '@packages/types/registration';

import {
  SocialAuthButtonContainer,
  ThirdPartyIntegrationFormInput,
  ThirdPartyIntegrationPasswordInput,
  ThirdPartyIntegrationRedirectText,
} from '@packages/auth/components/styles/thirdPartyIntegration';
import useOktaSession from '@packages/auth/hooks/useOktaSession';
import useRegistrationAnalytics from '@packages/auth/hooks/useRegistrationAnalytics';
import useRegistrationReducer from '@packages/auth/hooks/useRegistrationReducer';
import { getSignUpSource } from '@packages/auth/utils/analyticsUtils';
import { fieldValidationMap, isUserInputValid, onRegisterUser } from '@packages/auth/utils/registrationUtils';
import { useRequestParams } from '@packages/common/context/RequestParamsContext';
import { ThirdPartyIntegrationLineDivider } from '@packages/components/ThirdPartyIntegration/styles/common';
import ThirdPartyIntegrationLayout from '@packages/components/ThirdPartyIntegration/ThirdPartyIntegrationLayout';
import ThirdPartyIntegrationPage from '@packages/components/ThirdPartyIntegration/ThirdPartyIntegrationPage';

import useReCaptcha from '../hooks/useReCaptcha';
import AuthErrorBanner from './AuthErrorBanner';
import SocialAuthButton from './SocialAuthButton';
import { ErrorMessage } from './styles/form';
import TermsOfServiceCheckbox from './TermsOfServiceCheckbox';

interface Props {
  location?: Pick<Location, 'search'>;
  windowLocation?: Pick<Location, 'assign' | 'href'>;
}

const VercelTermsOfServiceCheckbox = styled(TermsOfServiceCheckbox)`
  margin: 24px 0 0;
  justify-content: left;
  span {
    flex-grow: 0;
    font-size: 13px;
    font-weight: 600;
  }
`;

const VercelPasswordTooltipParagraph = styled.p`
  margin: 0;
  padding: 0;
`;

export default function VercelRegistrationPage({
  location: { search } = { search: '' },
  windowLocation = window.location,
}: Props) {
  const reCaptcha = useReCaptcha(ReCaptchaAction.REGISTER);
  const requestParams = useRequestParams();
  const {
    isGoogleAuthEnabled,
    isGithubAuthEnabled,
    vercelEmail,
    vercelUserId,
    vercelTeamName,
    vercelTeamId,
    siteName,
  } = requestParams;

  const parsedSearchLocation = queryString.parse(search) as Search;
  const {
    vercelEmail: vercelEmailFromClient,
    vercelUserId: vercelUserIdFromClient,
    vercelTeamId: vercelTeamIdFromClient,
    vercelTeamName: vercelTeamnameFromClient,
  } = parsedSearchLocation;

  const clientState = {
    ...parsedSearchLocation,
    n: '/account/vercel/org/selection',
    isVercelIntegration: true,
    vercelEmail: vercelEmail ?? vercelEmailFromClient,
    vercelUserId: vercelUserId ?? vercelUserIdFromClient,
    vercelTeamId: vercelTeamId ?? vercelTeamIdFromClient,
    vercelTeamName: vercelTeamName ?? vercelTeamnameFromClient,
  };

  const [{ formFields, errorMessage, captchaError, errorField, formSubmitted, isSocialSignupDisabled }, dispatch] =
    useRegistrationReducer({
      username: vercelEmail ?? vercelEmailFromClient,
    });

  const { username, password, firstName, lastName, tos } = formFields;

  const reason = clientState?.reason ?? '';
  const authErrorCode = !username ? 'INVALID_VERCEL_ACCESS_CODE' : reason;
  const isSocialAuthEnabled = isGoogleAuthEnabled || isGithubAuthEnabled;
  const analyticsSignupSource = getSignUpSource(undefined, true, false);

  useOktaSession(dispatch);
  useRegistrationAnalytics({ analyticsSignupSource, marketplaceSource: undefined });

  const isSubmitDisabled =
    formSubmitted ||
    !(
      isUserInputValid(FieldNames.USERNAME, username) &&
      isUserInputValid(FieldNames.FIRST_NAME, firstName) &&
      isUserInputValid(FieldNames.LAST_NAME, lastName) &&
      tos &&
      password.length > 0 &&
      errorMessage.length === 0
    );

  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const updatedClientState = {
      ...clientState,
      firstName,
      signupSource: analyticsSignupSource,
      signupMethod: SignupMethod.FORM,
      n: '/account/vercel/confirm', // send the user to confirmation page for Vercel Integration
      vercelEmail: vercelEmail ?? vercelEmailFromClient,
      vercelUserId: vercelUserId ?? vercelUserIdFromClient,
      vercelTeamId: vercelTeamId ?? vercelTeamIdFromClient,
      vercelTeamName: vercelTeamName ?? vercelTeamnameFromClient,
      isNewVercelOrg: 'true',
    };

    const onRegisterUserParams = {
      dispatch,
      clientState: updatedClientState,
      requestParams,
      searchObject: parsedSearchLocation,
      windowLocation,
      registrationFormFields: formFields,
    };

    if (requestParams.reCaptchaRegistrationEnabled) {
      reCaptcha.execute((reCaptchaToken) => {
        onRegisterUser({ ...onRegisterUserParams, reCaptchaToken });
      });
    } else {
      onRegisterUser(onRegisterUserParams);
    }
  };

  return (
    <ThirdPartyIntegrationPage pageTitle="Create Account">
      <ThirdPartyIntegrationLayout
        title="Create Your MongoDB Account"
        isForm
        formProps={{ onSubmit, buttonText: 'Sign up', disabled: isSubmitDisabled }}
      >
        {authErrorCode && (
          <AuthErrorBanner
            className={css({ marginBottom: '24px' })}
            authErrorCode={authErrorCode}
            siteName={siteName}
          />
        )}
        <SocialAuthButtonContainer>
          {isGoogleAuthEnabled && (
            <>
              <SocialAuthButton
                clientState={clientState}
                disabled={isSocialSignupDisabled}
                provider={SocialAuthMethods.GOOGLE}
                location={location}
                data-testid="googleAuthButton"
              />
            </>
          )}
          {isGithubAuthEnabled && (
            <>
              <SocialAuthButton
                clientState={clientState}
                disabled={isSocialSignupDisabled}
                provider={SocialAuthMethods.GITHUB}
                location={location}
                data-testid="githubAuthButton"
              />
            </>
          )}
        </SocialAuthButtonContainer>
        {isSocialAuthEnabled && <ThirdPartyIntegrationLineDivider>or</ThirdPartyIntegrationLineDivider>}
        <ThirdPartyIntegrationFormInput
          fieldName="emailAddress"
          labelName="Email Address"
          autoComplete="username"
          type="email"
          onChange={(e) => dispatch({ type: 'field', payload: { field: FieldNames.USERNAME, value: e.target.value } })}
          value={username}
          regexValidation={fieldValidationMap[FieldNames.USERNAME]}
          hasError={errorField === FieldNames.USERNAME}
          errorMessage={errorMessage}
          tooltipText={
            'This is your email address associated with Vercel. This integration will sign you up for' +
            ' Atlas using the same email address, but you will be able to change it later via the Atlas UI.'
          }
          disabled
        />
        <ThirdPartyIntegrationFormInput
          fieldName={FieldNames.FIRST_NAME}
          labelName="First Name"
          autoComplete="given-name"
          onChange={(e) =>
            dispatch({ type: 'field', payload: { field: FieldNames.FIRST_NAME, value: e.target.value } })
          }
          value={firstName}
          regexValidation={fieldValidationMap[FieldNames.FIRST_NAME]}
          hasError={errorField === FieldNames.FIRST_NAME}
          errorMessage={errorMessage}
        />
        <ThirdPartyIntegrationFormInput
          fieldName={FieldNames.LAST_NAME}
          labelName="Last Name"
          autoComplete="family-name"
          onChange={(e) => dispatch({ type: 'field', payload: { field: FieldNames.LAST_NAME, value: e.target.value } })}
          value={lastName}
          regexValidation={fieldValidationMap[FieldNames.LAST_NAME]}
          hasError={errorField === FieldNames.LAST_NAME}
          errorMessage={errorMessage}
        />
        <ThirdPartyIntegrationPasswordInput
          onChange={(e) => dispatch({ type: 'field', payload: { field: FieldNames.PASSWORD, value: e.target.value } })}
          value={password}
          hasError={errorField === FieldNames.PASSWORD}
          errorMessage={errorMessage}
          forNewUser
          showRequirementsBox={false}
          hasFullWidth
          tooltipText={
            <VercelPasswordTooltipParagraph>
              Your password must:
              <br />
              Contain at least 8 characters
              <br />
              Contain unique characters, numbers, or symbols
              <br />
              Not contain your email address
            </VercelPasswordTooltipParagraph>
          }
        />
        <ThirdPartyIntegrationRedirectText>
          Already have an Atlas account?
          <Link to={`/login/vercel${!!search ? search + '&' : '?'}signedOut=true`}>&nbsp;Log in now</Link>
        </ThirdPartyIntegrationRedirectText>
        <VercelTermsOfServiceCheckbox
          checked={tos}
          onChange={(e) => dispatch({ type: 'field', payload: { field: FieldNames.TOS, value: e.target.checked } })}
        />
        {captchaError && <ErrorMessage data-testid="error-message">{captchaError}</ErrorMessage>}
      </ThirdPartyIntegrationLayout>
    </ThirdPartyIntegrationPage>
  );
}
