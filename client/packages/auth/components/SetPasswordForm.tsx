import { useState } from 'react';

import { css } from '@emotion/react';
import Banner from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import { Toast } from '@leafygreen-ui/toast';
import { H2 } from '@leafygreen-ui/typography';

import { WindowLocation } from '@packages/types/browser';

import * as api from '@packages/common/services/api';
import * as errorHelper from '@packages/common/services/authErrorHelper';
import { ResetFormValues } from '@packages/auth/types/reset';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import usePasswordValidations from '@packages/hooks/usePasswordValidations';

import { mq } from '../../common/styles/utils/mediaQueries';
import FormInput from './FormInput';
import HeaderLogo from './HeaderLogo';
import { bannerStyle } from './styles/banner';
import { Container, header, PasswordInputStyle } from './styles/form';
import { resetButton } from './styles/reset';

interface SetPasswordFormProps {
  username: string;
  setUsername: (value: string) => void;
  tempId: string;
  headerText?: string;
  errorReason?: string;
  email?: string;
  additionalFormParams: Partial<ResetFormValues>;
  windowLocation?: WindowLocation;
}

const headerContainer = css(
  mq({
    width: ['270px', 'calc(100vw - 64px)', '270px', '270px'],
  })
);

export default function SetPasswordForm({
  username,
  setUsername,
  tempId,
  headerText,
  errorReason,
  email,
  additionalFormParams,
  windowLocation = window.location,
}: SetPasswordFormProps) {
  const [newPassword, setNewPassword] = useState('');
  const [newPasswordConfirm, setNewPasswordConfirm] = useState('');
  const [resetErrorCode, setResetErrorCode] = useState('');
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [showToastComponent, setShowToastComponent] = useState(false);
  const { validationsPassed, stateNotifications } = usePasswordValidations(username, newPassword);

  const sendSavePasswordAnalytic = () => {
    analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
      action: 'Save Password',
      context: 'Create a new password',
      username,
    });
  };

  const onFormSubmit = async (e: $TSFixMe) => {
    e.preventDefault();
    setDisableSubmit(true);
    const resetType = 'PASSWORD';

    const formVals: ResetFormValues = {
      username,
      tempId,
      password: newPassword,
      passwordConfirm: newPasswordConfirm,
      resetType,
      ...additionalFormParams,
    };

    if (!validationsPassed) {
      setDisableSubmit(false);
      return;
    }

    // The current okta reset password implementation only handles passwords, not 2FA
    try {
      const { loginRedirect } = await api.auth.resetComplete(formVals);
      setShowToastComponent(true);
      setResetErrorCode('');
      setTimeout(() => {
        setShowToastComponent(false);
        windowLocation.assign(loginRedirect);
      }, 5000);
    } catch ({ errorCode }) {
      setResetErrorCode(errorCode);
      setDisableSubmit(false);
    }
  };

  const getResetErrorMsgFromReason = () => {
    let resetErrorMessage;

    if (errorReason && errorHelper.hasMatchingErrorCode(errorReason)) {
      resetErrorMessage = errorHelper.getResetErrorMessageFromCode(errorReason);
    }
    return resetErrorMessage;
  };

  return (
    <Container onSubmit={onFormSubmit} noValidate method="post">
      <HeaderLogo />
      <H2 css={[header, headerContainer]}>{headerText || 'Reset your password'}</H2>
      <Toast
        data-testid="resetPasswordSuccess"
        variant="success"
        title="Your password has been successfully updated!"
        open={showToastComponent}
      />
      {errorReason && (
        <Banner variant="danger" css={bannerStyle} data-testid="dangerBanner">
          {getResetErrorMsgFromReason()}
        </Banner>
      )}
      {resetErrorCode && (
        <Banner variant="danger" css={bannerStyle} data-testid="dangerBanner">
          {errorHelper.getResetErrorMessageFromCode(resetErrorCode)}
        </Banner>
      )}
      <FormInput
        fieldName="username"
        labelName="Email Address"
        autoComplete="email"
        type="email"
        onChange={(e) => setUsername(e.target.value)}
        value={username}
        disabled={email !== undefined}
      />
      <PasswordInputStyle
        id="newPassword"
        label="New Password"
        autoComplete="new-password"
        onChange={(e) => setNewPassword(e.target.value)}
        value={newPassword}
        disabled={disableSubmit}
        stateNotifications={stateNotifications}
      />
      <PasswordInputStyle
        id="newPasswordConfirm"
        label="Confirm New Password"
        autoComplete="new-password"
        onChange={(e) => setNewPasswordConfirm(e.target.value)}
        value={newPasswordConfirm}
        disabled={disableSubmit}
      />
      <footer>
        <Button
          variant="primary"
          size="default"
          type="submit"
          disabled={username === '' || newPasswordConfirm === '' || disableSubmit || !validationsPassed}
          css={resetButton}
          onClick={sendSavePasswordAnalytic}
        >
          Save Password
        </Button>
      </footer>
    </Container>
  );
}
