import { css } from '@emotion/react';
import Banner, { Variant as BannerVariant } from '@leafygreen-ui/banner';

import * as errorHelper from '@packages/common/services/authErrorHelper';
import intercomService from '@packages/intercom/intercomService';

const textLink = css({
  cursor: 'pointer',
});

const getInvalidUserAuthErrorMessage = () => {
  return (
    <>
      Please provide a valid email address and password. If you continue to have issues logging into your account,
      contact our{' '}
      <a
        role="button"
        onClick={() => {
          intercomService.showIntercom();
        }}
        tabIndex={0}
        css={textLink}
      >
        Support team.
      </a>
    </>
  );
};

export default function AuthErrorBanner({
  authErrorCode,
  marketingUrl,
  siteName,
  className,
}: {
  authErrorCode: string;
  marketingUrl?: string;
  siteName: string;
  className?: string;
}) {
  return (
    <Banner variant={BannerVariant.Danger} data-testid="bannerAuthCodeError" className={className}>
      {authErrorCode === 'INVALID_USER_AUTH' || authErrorCode === 'INVALID_EMAIL_ADDR'
        ? getInvalidUserAuthErrorMessage()
        : errorHelper.getErrorMessageFromCode(authErrorCode, { marketingUrl, siteName })}
    </Banner>
  );
}
