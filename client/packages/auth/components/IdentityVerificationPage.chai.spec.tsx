import userEvent from '@testing-library/user-event';
import { expect } from 'chai';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'history'
import { createBrowserHistory } from 'history';
import { Context } from 'mocha';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'react-router-dom'
import { Router } from 'react-router-dom';
import sinon from 'sinon';

import * as api from '@packages/common/services/api';
import IdentityVerificationPage from '@packages/auth/components/IdentityVerificationPage';
import { RequestParamsProvider } from '@packages/common/context/RequestParamsContext';
import { cleanup, render, screen } from '@packages/react-testing-library';
import fakeLocation from '@packages/test-utils/locationMock';

const EMAIL = '<EMAIL>';
const PASSWORD = 'hello2344';

function identityVerificationPage() {
  return {
    header: screen.queryByRole('heading', { level: 2 }),
    vercelHeader: screen.queryByRole('heading', { level: 3 }),
    description: screen.queryByTestId('identityDescription'),
    linkMyAccounts: screen.queryByRole('button', { name: /link my accounts/i }),
    cancelButton: screen.queryByRole('button', { name: /cancel/i }),
    email: screen.queryByRole('textbox', { name: /email/i }),
    password: screen.queryByPlaceholderText(/password/i),
    confirmButton: screen.queryByRole('button', { name: /confirm/i }),
    banner: screen.queryByRole('alert'),
  };
}

describe('@packages/auth/components/IdentityVerificationPage', function () {
  const user = userEvent.setup();
  beforeEach(function (this: Context) {
    this.history = createBrowserHistory();
    global.window = global.window || {};
    (global.window as any).REQUEST_PARAMS = {};
    this.location = { search: '?signupMethod=google&email=abc%40gmail.com' };
    this.sandbox = sinon.createSandbox();
    this.sandbox.stub(api.admin, 'getUiMsgs').resolves([]);
    this.endOktaSessionStub = this.sandbox.stub();
    this.oktaSessionExistsStub = this.sandbox.stub();
    this.authVerifyStub = this.sandbox.stub(api.auth, 'verify');
    this.cancelAuthVerifyStub = this.sandbox.stub(api.auth, 'cancelLinking');
    this.sandbox.stub(fakeLocation, 'assign');
  });

  afterEach(function (this: Context) {
    (global.window as any).REQUEST_PARAMS = {};
    this.sandbox.restore();
  });

  describe('when rendered with existing Okta session', function () {
    beforeEach(async function (this: Context) {
      this.oktaSessionExistsStub.resolves(true);
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      render(
        <RequestParamsProvider>
          <Router history={this.history}>
            <IdentityVerificationPage
              endOktaSession={this.endOktaSessionStub}
              oktaSessionExists={this.oktaSessionExistsStub}
              windowLocation={fakeLocation}
              location={this.location}
            />
          </Router>
        </RequestParamsProvider>
      );
      await Promise.resolve();
    });

    afterEach(() => {
      cleanup();
    });

    it('shows expected fields', function (this: Context) {
      const page = identityVerificationPage();
      expect(page.header).to.exist;
      expect(page.description).to.exist;
      expect(page.cancelButton).to.exist;
      expect(page.linkMyAccounts).to.exist;
    });

    it('calls endOktaSessionStub once', function (this: Context) {
      expect(this.endOktaSessionStub).to.be.calledOnce;
    });

    describe('when "Cancel, Return to Login" is clicked', function () {
      beforeEach(async function (this: Context) {
        this.cancelAuthVerifyStub.resolves({});
        const page = identityVerificationPage();
        await user.click(page.cancelButton!);
        await Promise.resolve();
      });

      it('calls cancelAuthVerifyStub', function (this: Context) {
        expect(this.cancelAuthVerifyStub).to.be.calledOnce;
      });

      it('calls windowLocation assign with expected values', function () {
        expect(fakeLocation.assign).to.have.been.calledOnce;
        expect(fakeLocation.assign).to.have.been.calledWith('/account/login?signedOut=true');
      });
    });

    describe('when "Link My Accounts" is clicked', function () {
      beforeEach(async function (this: Context) {
        const page = identityVerificationPage();
        await user.click(page.linkMyAccounts!);
        await Promise.resolve();
      });

      it('shows email address and password fields', async function (this: Context) {
        const page = identityVerificationPage();
        expect(page.banner).to.exist;
        expect(page.email).to.exist;
        expect(page.email).to.have.property('value', EMAIL);
        expect(page.email).to.have.property('disabled', true);
        expect(page.password).to.exist;
        expect(page.cancelButton).to.exist;
        expect(page.confirmButton).to.exist;
        expect(page.confirmButton?.getAttribute('aria-disabled')).to.equal('true');
      });

      describe('when "Cancel" is clicked', function () {
        beforeEach(async function (this: Context) {
          this.cancelAuthVerifyStub.resolves({});
          const page = identityVerificationPage();
          await user.click(page.cancelButton!);
          await Promise.resolve();
        });

        it('calls cancelAuthVerifyStub', function (this: Context) {
          expect(this.cancelAuthVerifyStub).to.be.calledOnce;
        });

        it('calls windowLocation assign with expected values', function () {
          expect(fakeLocation.assign).to.have.been.calledOnce;
          expect(fakeLocation.assign).to.have.been.calledWith('/account/login?signedOut=true');
        });
      });

      describe('when password is filled in', function () {
        beforeEach(async function (this: Context) {
          const page = identityVerificationPage();
          await userEvent.type(page.password!, PASSWORD);
        });

        it('enables Confirm button', async function (this: Context) {
          const page = identityVerificationPage();
          expect(page.confirmButton?.getAttribute('aria-disabled')).to.equal('false');
        });

        describe('when Confirm is clicked and the call is not successful', function () {
          beforeEach(async function (this: Context) {
            const page = identityVerificationPage();
            this.authVerifyStub.rejects({ errorCode: 'INVALID_USER_AUTH' });
            await user.click(page.confirmButton!);
            await Promise.resolve();
          });

          it('calls authVerifyStub with expected arguments', function (this: Context) {
            expect(this.authVerifyStub).to.have.been.calledOnce;
            expect(this.authVerifyStub).to.have.been.calledWith({
              username: EMAIL,
              password: PASSWORD,
              reCaptchaToken: undefined,
              clientState: { email: EMAIL, signupMethod: 'google' },
            });
          });

          it('does not call windowLocation assign', function () {
            expect(fakeLocation.assign).to.not.have.been.called;
          });
        });

        describe('when Confirm is clicked and is successful', function () {
          beforeEach(async function (this: Context) {
            const page = identityVerificationPage();
            this.authVerifyStub.resolves({ loginRedirect: 'abc' });
            await user.click(page.confirmButton!);
            await Promise.resolve();
          });

          it('calls authVerifyStub with expected arguments', function (this: Context) {
            expect(this.authVerifyStub).to.have.been.calledOnce;
            expect(this.authVerifyStub).to.have.been.calledWith({
              username: EMAIL,
              password: PASSWORD,
              reCaptchaToken: undefined,
              clientState: { email: EMAIL, signupMethod: 'google' },
            });
          });

          it('calls windowLocation assign', function () {
            expect(fakeLocation.assign).to.have.been.calledOnce;
            expect(fakeLocation.assign).to.have.been.calledWith('abc');
          });
        });
      });
    });
  });

  describe('when rendered with no existing Okta session', function () {
    beforeEach(async function (this: Context) {
      this.oktaSessionExistsStub.resolves(false);
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      render(
        <RequestParamsProvider>
          <Router history={this.history}>
            <IdentityVerificationPage
              endOktaSession={this.endOktaSessionStub}
              oktaSessionExists={this.oktaSessionExistsStub}
              windowLocation={fakeLocation}
            />
          </Router>
        </RequestParamsProvider>
      );
      await Promise.resolve();
    });

    afterEach(() => {
      cleanup();
    });

    it('does not call endOktaSessionStub', function (this: Context) {
      expect(this.endOktaSessionStub).to.not.be.called;
    });
  });

  describe('when rendered with existing Okta session for Vercel integration', function () {
    beforeEach(async function (this: Context) {
      this.oktaSessionExistsStub.resolves(true);
      const locationWithVercel = { search: '?signupMethod=google&email=abc%40gmail.com&isVercelIntegration=true' };
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      render(
        <RequestParamsProvider>
          <Router history={this.history}>
            <IdentityVerificationPage
              endOktaSession={this.endOktaSessionStub}
              oktaSessionExists={this.oktaSessionExistsStub}
              windowLocation={fakeLocation}
              location={locationWithVercel}
            />
          </Router>
        </RequestParamsProvider>
      );
      await Promise.resolve();
    });

    afterEach(() => {
      cleanup();
    });

    it('shows expected fields', function (this: Context) {
      const page = identityVerificationPage();
      expect(page.vercelHeader).to.exist;
      expect(page.description).to.exist;
      expect(page.cancelButton).to.exist;
      expect(page.linkMyAccounts).to.exist;
    });

    it('calls endOktaSessionStub once', function (this: Context) {
      expect(this.endOktaSessionStub).to.be.calledOnce;
    });

    describe('when "Cancel, Return to Login" is clicked', function () {
      beforeEach(async function (this: Context) {
        this.cancelAuthVerifyStub.resolves({});
        const page = identityVerificationPage();
        await user.click(page.cancelButton!);
        await Promise.resolve();
      });

      it('calls cancelAuthVerifyStub', function (this: Context) {
        expect(this.cancelAuthVerifyStub).to.be.calledOnce;
      });

      it('calls windowLocation assign with expected values', function () {
        expect(fakeLocation.assign).to.have.been.calledOnce;
        expect(fakeLocation.assign).to.have.been.calledWith(
          '/account/login/vercel?signedOut=true&signupMethod=google&isVercelIntegration=true'
        );
      });
    });

    describe('when "Link My Accounts" is clicked', function () {
      beforeEach(async function (this: Context) {
        const page = identityVerificationPage();
        await user.click(page.linkMyAccounts!);
        await Promise.resolve();
      });

      describe('when "Cancel" is clicked', function () {
        beforeEach(async function (this: Context) {
          this.cancelAuthVerifyStub.resolves({});
          const page = identityVerificationPage();
          await user.click(page.cancelButton!);
          await Promise.resolve();
        });

        it('calls cancelAuthVerifyStub', function (this: Context) {
          expect(this.cancelAuthVerifyStub).to.be.calledOnce;
        });

        it('calls windowLocation assign with expected values', function () {
          expect(fakeLocation.assign).to.have.been.calledOnce;
          expect(fakeLocation.assign).to.have.been.calledWith(
            '/account/login/vercel?signedOut=true&signupMethod=google&isVercelIntegration=true'
          );
        });
      });
    });
  });
});
