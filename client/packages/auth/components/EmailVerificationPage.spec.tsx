import { expect } from 'chai';
import fetchMock from 'fetch-mock';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'hist... Remove this comment to see the full error message
import { createBrowserHistory } from 'history';
import { Context } from 'mocha';
// @ts-expect-error TS(7016): Could not find a declaration file for module 'reac... Remove this comment to see the full error message
import { Router } from 'react-router-dom';
import sinon from 'sinon';

import * as api from '@packages/common/services/api';
import EmailVerificationPage from '@packages/auth/components/EmailVerificationPage';
// analytics
import analytics, { PAGE_CALLS } from '@packages/common/utils/segmentAnalytics';
import { cleanup, fireEvent, render, RenderResult, waitFor } from '@packages/react-testing-library';
import fakeLocation from '@packages/test-utils/locationMock';

describe('@packages/auth/components/EmailVerificationPage', function () {
  let renderResult: RenderResult;
  beforeEach(function (this: Context) {
    this.history = createBrowserHistory();
    fetchMock.get('/uiMsgs', []);
    this.sandbox = sinon.createSandbox();
    this.unauthedResendVerificationEmailStub = this.sandbox.stub(api.auth, 'unauthedResendVerificationEmail');
    this.oktaSessionExistsStub = this.sandbox.stub();
    this.startOidcStub = this.sandbox.stub(api.auth, 'startOidc');
  });

  afterEach(function (this: Context) {
    fetchMock.restore();
    this.sandbox.restore();
    cleanup();
  });

  describe('when rendered with username and without errorCode', function () {
    beforeEach(function (this: Context) {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderResult = render(
        <Router history={this.history}>
          <EmailVerificationPage location={{ search: '?username=abc' }} />
        </Router>
      );
    });

    it('shows header saying verify your email', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByText('Great, now verify your email')).to.exist;
    });

    it('shows Verify Email logo', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findAllByAltText('Verify Email')).to.exist;
    });

    it('shows text about the email verification', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByTestId('checkInbox')).to.exist;
    });

    it('shows text about check the spam folder', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByTestId('checkSpam')).to.exist;
    });

    it('shows the text about link expired', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByTestId('linkExpired')).to.exist;
    });

    it('shows "Resend verification email" link', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByTestId('resendLink')).to.exist;
    });
  });

  describe('when resend verification email link is clicked', function () {
    beforeEach(async function (this: Context) {
      global.window = global.window || {};
      (global.window as any).REQUEST_PARAMS = {
        accountCentralUrl: 'ACCOUNT_CENTRAL_URL',
      };

      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderResult = render(
        <Router history={this.history}>
          <EmailVerificationPage location={{ search: '?username=abc&emailToken=EXAMPLE_TOKEN' }} />
        </Router>
      );

      // eslint-disable-next-line testing-library/prefer-screen-queries
      fireEvent.click(await renderResult.findByTestId('resendLink'));
    });

    it('calls unauthedResendVerificationEmailStub', function (this: Context) {
      expect(this.unauthedResendVerificationEmailStub).to.have.been.calledOnce;
      expect(this.unauthedResendVerificationEmailStub).to.have.been.calledWith({
        username: 'abc',
        clientState: { username: 'abc', emailToken: 'EXAMPLE_TOKEN' },
        analyticsPageTitle: 'Verify Email',
        emailToken: 'EXAMPLE_TOKEN',
      });
    });

    describe('and unauthedResendVerificationEmail returns a FORBIDDEN response', function () {
      beforeEach(async function (this: Context) {
        this.unauthedResendVerificationEmailStub.rejects({
          errorCode: 'FORBIDDEN',
        });
        // eslint-disable-next-line testing-library/prefer-screen-queries
        fireEvent.click(await renderResult.findByTestId('resendLink'));
      });

      it('prompts the user to re-login', async function () {
        expect(
          // eslint-disable-next-line testing-library/prefer-screen-queries
          await renderResult.findByText('An error occurred while validating your email verification resend request.')
        ).to.exist;
        // eslint-disable-next-line testing-library/prefer-screen-queries
        expect(await renderResult.findByText('Please visit ACCOUNT_CENTRAL_URL and re-login.')).to.exist;
      });
    });

    describe('and unauthedResendVerificationEmail returns rate limit error', function () {
      beforeEach(async function (this: Context) {
        this.unauthedResendVerificationEmailStub.rejects({
          errorCode: 'RATE_LIMITED',
        });
        // eslint-disable-next-line testing-library/prefer-screen-queries
        fireEvent.click(await renderResult.findByTestId('resendLink'));
      });

      it('displays error banner', async function () {
        // eslint-disable-next-line testing-library/prefer-screen-queries
        const errorBanner = await renderResult.findByTestId('resendErrorBanner');
        expect(errorBanner.textContent).to.equal('Too many attempts, please try again later.');
      });
    });
  });

  describe('when rendered without username and errorCode', function () {
    beforeEach(function (this: Context) {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderResult = render(
        <Router history={this.history}>
          <EmailVerificationPage
            location={{ search: '' }}
            windowLocation={fakeLocation}
            oktaSessionExists={this.oktaSessionExistsStub}
          />
        </Router>
      );
    });

    it('shows header saying Email successfully verified', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByText('Email successfully verified!')).to.exist;
    });

    it('shows Email Verified logo', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findAllByAltText('Email Verified')).to.exist;
    });

    it('shows Continue button', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByText('Continue')).to.exist;
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByText('Continue')).to.not.be.disabled;
    });

    describe('when Continue is clicked and oktaSessionExists', function () {
      beforeEach(async function (this: Context) {
        this.oktaSessionExistsStub.resolves(true);
        this.startOidcStub.resolves({ loginRedirect: 'redirect' });
        this.sandbox.stub(fakeLocation, 'assign');

        // eslint-disable-next-line testing-library/prefer-screen-queries
        fireEvent.click(await renderResult.findByText('Continue'));
      });

      it('should call oktaSessionExistsStub', function (this: Context) {
        expect(this.oktaSessionExistsStub).to.be.calledOnce;
      });

      it('should call startOidcStub', function (this: Context) {
        expect(this.startOidcStub).to.be.calledWith({
          clientState: {},
        });
      });

      it('calls windowLocation.assign', async function () {
        await waitFor(() => expect(fakeLocation.assign).to.be.calledOnce);
        expect(fakeLocation.assign).to.have.been.calledWith('redirect');
      });

      it('disables Continue button', async function () {
        // eslint-disable-next-line testing-library/prefer-screen-queries
        expect(await renderResult.findByText('Continue')).to.be.disabled;
      });
    });

    describe('when Continue is clicked and oktaSession does not exist', function () {
      beforeEach(async function (this: Context) {
        this.oktaSessionExistsStub.resolves(false);
        this.sandbox.stub(fakeLocation, 'assign');

        // eslint-disable-next-line testing-library/prefer-screen-queries
        fireEvent.click(await renderResult.findByText('Continue'));
      });

      it('should call oktaSessionExistsStub', function (this: Context) {
        expect(this.oktaSessionExistsStub).to.be.calledOnce;
      });

      it('should not call startOidcStub', function (this: Context) {
        expect(this.startOidcStub).to.not.be.called;
      });

      it('calls windowLocation.assign', async function () {
        await waitFor(() => expect(fakeLocation.assign).to.be.calledOnce);
        expect(fakeLocation.assign).to.have.been.calledWith('/account/login?signedOut=true');
      });

      it('disables Continue button', async function () {
        // eslint-disable-next-line testing-library/prefer-screen-queries
        expect(await renderResult.findByText('Continue')).to.be.disabled;
      });
    });
  });

  describe('when rendered with errorCode', function () {
    beforeEach(function (this: Context) {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderResult = render(
        <Router history={this.history}>
          <EmailVerificationPage
            location={{ search: '?username=abc&errorCode=INVALID_EMAIL_VERIFICATION_TOKEN' }}
            windowLocation={fakeLocation}
            oktaSessionExists={this.oktaSessionExistsStub}
          />
        </Router>
      );
    });

    it('shows warning banner stating that your token is expired', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByTestId('expiredBanner')).to.exist;
    });

    it('shows header saying verify your email', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findByText('Great, now verify your email')).to.exist;
    });

    it('shows Verify Email logo', async function () {
      // eslint-disable-next-line testing-library/prefer-screen-queries
      expect(await renderResult.findAllByAltText('Verify Email')).to.exist;
    });
  });

  describe('when rendered with analyticsEnabled', function () {
    beforeEach(function (this: Context) {
      global.window = global.window || {};
      (global.window as any).REQUEST_PARAMS = {
        analyticsEnabled: true,
      };

      this.segmentIdentifyStub = this.sandbox.stub(analytics, 'identify');
      this.segmentPageStub = this.sandbox.stub(analytics, 'page');

      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderResult = render(
        <Router history={this.history}>
          <EmailVerificationPage
            location={{ search: '?username=abc&errorCode=INVALID_EMAIL_VERIFICATION_TOKEN&uId=test' }}
            windowLocation={fakeLocation}
            oktaSessionExists={this.oktaSessionExistsStub}
          />
        </Router>
      );
    });

    afterEach(function () {
      (global.window as any).REQUEST_PARAMS = {};
    });

    it('calls segment identify with expected arguments', function (this: Context) {
      expect(this.segmentIdentifyStub).to.be.calledWith({
        userId: 'test',
      });
    });

    it('calls segment page with expected arguments', function (this: Context) {
      expect(this.segmentPageStub).to.be.calledWith(PAGE_CALLS.VERIFICATION_EMAIL, {
        userId: 'test',
        resend: true,
      });
    });
  });
});
