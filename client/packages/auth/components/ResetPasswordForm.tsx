import { ReactElement, useState } from 'react';

import Banner from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import { H2 } from '@leafygreen-ui/typography';

import { CloudTeams } from '@packages/types/observability';

import * as api from '@packages/common/services/api';
import * as errorHelper from '@packages/common/services/authErrorHelper';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { sendError } from '@packages/observability';

import FormInput from './FormInput';
import HeaderLogo from './HeaderLogo';
import { bannerStyle } from './styles/banner';
import { Container, header } from './styles/form';
import { HeaderText, resetButton } from './styles/reset';

interface ResetPasswordFormProps {
  username: string;
  setUsername: (value: string) => void;
  headerText?: string;
  successBannerText?: string;
  infoBanner?: ReactElement;
  instructionsText?: string;
  email?: string;
  disableEmail?: boolean;
  submitText?: string;
}

export default function ResetPasswordForm({
  username,
  setUsername,
  headerText,
  instructionsText,
  successBannerText,
  infoBanner,
  email,
  disableEmail,
  submitText,
}: ResetPasswordFormProps) {
  const [showResetSuccess, setResetSuccess] = useState(false);
  const [resetErrorCode, setResetErrorCode] = useState('');
  const [disableSubmit, setDisableSubmit] = useState(false);

  const sendResetPasswordAnalytics = () => {
    analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
      action: 'Reset Password',
      context: 'Reset your password',
      username,
    });
  };

  const onFormSubmit = async (e: $TSFixMe) => {
    e.preventDefault();
    setDisableSubmit(true);

    // don't proceed further if username is not given
    if (!username) {
      return;
    }

    try {
      await api.auth.resetPassword({ username });
      setResetSuccess(true);
    } catch (error) {
      sendError({
        error,
        team: CloudTeams.CoreIam,
      });
      setResetErrorCode(error.errorCode);
      setResetSuccess(false);
      setDisableSubmit(false);
    }
  };

  return (
    <Container onSubmit={onFormSubmit} noValidate method="post">
      <HeaderLogo />
      <H2 css={header}>{headerText || 'Reset your password'}</H2>
      {showResetSuccess ? (
        <>
          <Banner variant="success" css={bannerStyle} data-testid="successBanner">
            <span>{successBannerText || 'Please check your email inbox for a link to complete the reset'}</span>
          </Banner>
        </>
      ) : (
        <>
          {infoBanner}
          <HeaderText>
            {instructionsText ||
              'To reset your password, enter your email below and submit. An email will be sent to you with instructions about how to complete the process.'}
          </HeaderText>
          {resetErrorCode && (
            <Banner variant="danger" css={bannerStyle} data-testid="dangerBanner">
              <span>{errorHelper.getResetErrorMessageFromCode(resetErrorCode)}</span>
            </Banner>
          )}
          <FormInput
            fieldName="username"
            labelName="Email Address"
            autoComplete="email"
            type="email"
            onChange={(e) => setUsername(e.target.value)}
            value={username}
            hasError={resetErrorCode.length > 0}
            disabled={!!email && !!disableEmail}
          />
          <footer>
            <Button
              variant="primary"
              size="default"
              type="submit"
              disabled={username === '' || disableSubmit}
              css={resetButton}
              onClick={sendResetPasswordAnalytics}
            >
              {submitText || 'Reset Password'}
            </Button>
          </footer>
        </>
      )}
    </Container>
  );
}
