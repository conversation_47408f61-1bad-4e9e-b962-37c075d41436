import { palette } from '@leafygreen-ui/palette';
import { color } from '@leafygreen-ui/tokens';

type CssColorVar = `--mdb-${string}`;
type ColorMap = Record<string, CssColorVar>;

/**
 * Creates a map from LG palette colors to CSS variable names,
 * e.g. from [palette.red.light1] to `--mdb-redLight1`.
 *
 * @returns {ColorMap} An object mapping each palette color to its CSS variable name.
 * @example
 * const colorMap: ColorMap = {
 *   [palette.gray.base]: '--mdb-gray',
 *   [palette.gray.dark1]: '--mdb-grayDark1',
 *   // other colors
 * };
 */
function buildColorMap(): ColorMap {
  const colorMap: ColorMap = {};
  for (const color of Object.keys(palette) as Array<keyof typeof palette>) {
    const shades = palette[color];
    if (typeof shades === 'string') {
      colorMap[shades] = `--mdb-${color}`;
    } else {
      for (const shade of Object.keys(shades) as Array<keyof typeof shades>) {
        const colorValue = shades[shade];
        /** e.g. 'dark1' -> 'Dark1' */
        const nameSuffix = shade === 'base' ? '' : shade.charAt(0).toUpperCase() + shade.slice(1);
        colorMap[colorValue] = `--mdb-${color}${nameSuffix}`;
      }
    }
  }
  return colorMap;
}

type ThemeObject = {
  [key: string]: string | ThemeObject;
};

function mapColorsToCssVars(obj: ThemeObject, colorMap: ColorMap): ThemeObject {
  const result: ThemeObject = {};
  for (const key in obj) {
    const val = obj[key];
    if (typeof val === 'string') {
      result[key] = colorMap[val] ? `var(${colorMap[val]})` : val;
    } else if (typeof val === 'object' && val !== null) {
      result[key] = mapColorsToCssVars(val, colorMap);
    }
  }
  return result;
}

const colorMap = buildColorMap();

function createTheme(isDarkMode: boolean) {
  const themeColors = isDarkMode ? color.dark : color.light;
  return mapColorsToCssVars(themeColors, colorMap);
}

export { createTheme };
