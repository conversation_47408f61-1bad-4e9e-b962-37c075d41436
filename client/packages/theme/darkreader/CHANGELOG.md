# @mongodb-js/darkreader

## 1.1.6

### Patch Changes
- Adds opt-out for inline styles and their children using `.darkreader-ignore-child-inline-style`

## 1.1.5

### Patch Changes
- Fixes a bug where <PERSON><PERSON> would run on inject logic and break

## 1.1.4

### Patch Changes
- Ignores Profiler Plot from DarkReader
- Adds opt-out for inline styles using `.darkreader-ignore-inline-style`

## 1.1.3

### Patch Changes
- Cleans up internal Regex to check for mdb/vscode CSS variables

## 1.1.2

### Patch Changes
- Ensure mdb css variables get ignored by DarkReader for all CSS properties

## 1.1.1

### Patch Changes
- Support Dark Mode for Monaco Editor (ignore VSCode CSS Variables, add some other CSS overrides)

## 1.1.0

### Patch Changes
- Adds the `useThemeValue` hook.
- Adds a color mapping exported as `leafygreenColors`, mapping a LG color name to hex.
- Adds peerDep to `@leafygreen-ui/palette`

## 1.0.5

### Patch Changes
Adds new `darkreader-invert` className option to allow <PERSON><PERSON><PERSON><PERSON> to invert a color (great for PNGs, other icons).

## 1.0.4

### Patch Changes
Adds new `darkModeEnabledFromOS` return value to `useDarkReader`. This will evaluate to `true` when OS is preferred and the user has a dark mode OS on their machine (`false` otherwise).

## 1.0.3

### Patch Changes
Bump README

## 1.0.2

### Patch Changes
Re-export ThemePreference from index

## 1.0.1

### Patch Changes
Add Hook useDarkReader - documented in README

## 1.0.0

### Patch Changes
Initial version
