import { ThemePreference } from '@packages/types/theme';

import getPreference from './getPreference';

describe('@packages/theme/utils/getPreference', () => {
  it('should only return LIGHT if the feature is disabled', () => {
    const isFeatureEnabled = false;
    let isCloudManager = false;
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.DARK })).toBe(
      ThemePreference.LIGHT
    );
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.LIGHT })).toBe(
      ThemePreference.LIGHT
    );
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.OS })).toBe(
      ThemePreference.LIGHT
    );

    isCloudManager = true;
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.DARK })).toBe(
      ThemePreference.LIGHT
    );
  });

  it('should only return LIGHT if this CM', () => {
    const isCloudManager = true;
    const isFeatureEnabled = true;

    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.DARK })).toBe(
      ThemePreference.LIGHT
    );
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.LIGHT })).toBe(
      ThemePreference.LIGHT
    );
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.OS })).toBe(
      ThemePreference.LIGHT
    );
  });

  it('should return preference if enabled and not CM', () => {
    const isCloudManager = false;
    const isFeatureEnabled = true;

    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.DARK })).toBe(
      ThemePreference.DARK
    );
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.LIGHT })).toBe(
      ThemePreference.LIGHT
    );
    expect(getPreference({ isFeatureEnabled, isCloudManager, themePreference: ThemePreference.OS })).toBe(
      ThemePreference.OS
    );
  });
});
