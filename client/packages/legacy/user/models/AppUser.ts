import Backbone from 'backbone';

import globalSettings from '@packages/common/constants/globalSettings';
// TODO remove (required in loggedInTemplate.jsp)
import MMS from '@packages/legacy/initializers/initializer';

export default MMS.Models.AppUser = Backbone.Model.extend({
  defaults: {
    username: null,
    isAutomationAdmin: null,
    isBackupAdmin: null,
    isMonitoringAdmin: null,
    isGroupOwner: null,
    isUserAdmin: null,
    isBillingAdmin: null,
    displayChartAnnotations: true,
    separateOpcounterCharts: false,
  },
  // is*Admin methods apply to current group only
  isAutomationAdmin() {
    return this.get('isAutomationAdmin') && globalSettings.Group.roleAvailable('GROUP_AUTOMATION_ADMIN');
  },
  isBackupAdmin() {
    return this.get('isBackupAdmin') && globalSettings.Group.roleAvailable('GROUP_BACKUP_ADMIN');
  },
  isMonitoringAdmin() {
    return this.get('isMonitoringAdmin') && globalSettings.Group.roleAvailable('GROUP_MONITORING_ADMIN');
  },
  isGroupOwner() {
    return this.get('isGroupOwner') && globalSettings.Group.roleAvailable('GROUP_OWNER');
  },
  isUserAdmin() {
    return this.get('isUserAdmin') && globalSettings.Group.roleAvailable('GROUP_USER_ADMIN');
  },
  isBillingAdmin() {
    return this.get('isBillingAdmin') && globalSettings.Group.roleAvailable('GROUP_BILLING_ADMIN');
  },
  hasGlobalReadOnly() {
    return this.get('hasGlobalReadOnly');
  },
  hasGlobalAutomationAdmin() {
    return this.get('hasGlobalAutomationAdmin');
  },
  hasGlobalMonitoringAdmin() {
    return this.get('hasGlobalMonitoringAdmin');
  },
  hasGlobalBackupAdmin() {
    return this.get('hasGlobalBackupAdmin');
  },
  hasGlobalUserAdmin() {
    return this.get('isGlobalUserAdmin');
  },
  hasGlobalBillingAdmin() {
    return this.get('hasGlobalBillingAdmin');
  },
  hasGlobalAtlasOperator() {
    return this.get('hasGlobalAtlasOperator');
  },
  hasGlobalOwner() {
    return this.get('hasGlobalOwner');
  },
});
