load("//client/packages/scripts:client_package.bzl", "client_package")

client_package(
    name = "auto",
    visibility = [
        "//client/packages/admin/dashboard:__subpackages__",
        "//client/packages/handlebars-helpers:__pkg__",
        "//client/packages/legacy/activity:__pkg__",
        "//client/packages/legacy/monitoring:__pkg__",
        "//client/packages/legacy/servers:__pkg__",
        "//client/packages/project/backup:__pkg__",
        "//client/packages/project/common:__pkg__",
        "//client/packages/project/deployment:__pkg__",
        "//client/packages/project/host:__pkg__",
        "//client/packages/project/onboarding:__pkg__",
    ],
    deps = [
        "//:node_modules/ace-builds",
        "//client/hbs",
        "//client/packages/legacy/core",
        "//client/packages/legacy/initializers",
    ],
)
