import Backbone from 'backbone';

// TODO remove (required by backupCodesSetup.jsp)
import MMS from '@packages/legacy/initializers/initializer';

export default MMS.Models.MultiFactorAuth = Backbone.Model.extend({
  url: '/user/mfa',

  getUsername() {
    return this.get('username');
  },

  getAuthenticator() {
    return this.get('authenticator');
  },

  getVoice() {
    return this.get('voice');
  },

  getPhone() {
    return this.get('phone');
  },

  getExtension() {
    return this.get('extension');
  },

  isNew() {
    return false;
  },

  getBackupPhone() {
    return this.get('backupPhone');
  },

  getBackupPhoneExtension() {
    return this.get('backupPhoneExtension');
  },
});
