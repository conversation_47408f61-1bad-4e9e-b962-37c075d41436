// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import ParentModel from 'backbone-parentmodel';
import _ from 'underscore';

import globalSettings from '@packages/common/constants/globalSettings';
// TODO remove (required in loggedInTemplate.jsp)
import MMS from '@packages/legacy/initializers/initializer';

export default MMS.Models.Group = ParentModel.extend({
  defaults: {
    name: null,
    disableDbstats: false,
  },

  roleAvailable(role: $TSFixMe) {
    return _.indexOf(globalSettings.GROUP_AVAILABLE_ROLES, role) >= 0;
  },
});
