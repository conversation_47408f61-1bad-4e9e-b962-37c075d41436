// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import ParentModel from 'backbone-parentmodel';

import Diff from '@packages/legacy/auto/models/Diff';
import ItemDiff from '@packages/legacy/auto/models/ItemDiff';

import MetricThreshold from '../alertConfig/MetricThreshold';

export default ParentModel.extend({
  defaults: {
    _t: null,
    cid: null,
    cre: null,
    et: null,
    hid: undefined,
    hp: undefined,
    ver: undefined,
    rsId: undefined,
    clusterName: undefined,
    clId: undefined,
    shardName: undefined,
    cv: undefined,
    deploymentDiff: undefined,
    clusterDescriptionDiff: undefined,
    un: undefined,
    remoteAddr: undefined,
    userId: undefined,
    ipAddress: undefined,
    source: undefined,
    configState: undefined,
    snapshotId: undefined,
    restoreJobId: undefined,
    clustershotId: undefined,
    syncSource: undefined,
    pointInTime: undefined,
    reason: undefined,
    clusterId: undefined,
    isMmsAdmin: undefined,
    alertConfigEventType: undefined,
    alertConfigType: undefined,
    acid: undefined,
    tag: undefined,
    sourceTargetGroupId: undefined,
    restoreTargetGroupId: undefined,
    restoreTargetName: undefined,
    restoredToTime: undefined,
    newGroupName: undefined,
    feature: undefined,
    auditDescription: undefined,
    queryEngineTenantDiff: undefined,
    ingestionPipelineDiff: undefined,
    genericNDSItemDiff: undefined,
    liveImportOverridesDiff: undefined,
    onlineArchiveDiff: undefined,
    dataProcessingRegionDiff: undefined,
    regionalOutageDiff: undefined,
    rollingResyncDiff: undefined,
    pushBasedLogExportDiff: undefined,
    replicationSpecDiff: undefined,
    clusterUniqueId: undefined,
    azBalancingOverrideDiff: undefined,
  },

  childModels: {
    mt: MetricThreshold,
    deploymentDiff: Diff,
    clusterDescriptionDiff: ItemDiff,
    userSecurityDiff: Diff,
    auditLogDiff: ItemDiff,
    encryptionAtRestDiff: Diff,
    processArgsDiff: ItemDiff,
    queryEngineTenantDiff: Diff,
    ingestionPipelineDiff: Diff,
    cloudProviderAccessDiff: Diff,
    genericNDSItemDiff: Diff,
    osTunedFileOverridesDiff: ItemDiff,
    liveImportOverridesDiff: Diff,
    onlineArchiveDiff: Diff,
    dataProcessingRegionDiff: ItemDiff,
    regionalOutageDiff: Diff,
    rollingResyncDiff: Diff,
    pushBasedLogExportDiff: ItemDiff,
    replicationSpecDiff: Diff,
    azBalancingOverrideDiff: ItemDiff,
  },
});
