import { expect } from 'chai';

import AlertConfigModel from '@packages/legacy/activity/models/alertConfig/AlertConfigModel';
import Matchers from '@packages/legacy/activity/models/alertConfig/Matchers';

describe('@packages/legacy/activity/models/AlertConfigModel', function () {
  describe('when initialized with defaults', function () {
    beforeEach(function (this: $TSFixMe) {
      this.model = new AlertConfigModel();
    });

    it('has a matchers collection', function (this: $TSFixMe) {
      expect(this.model.get('matchers')).to.be.instanceof(Matchers);
    });
  });
});
