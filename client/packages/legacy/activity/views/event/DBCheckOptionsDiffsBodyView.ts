// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import Marion<PERSON> from 'backbone.marionette';
import template from 'hbs/activity/event/db_check_options_diffs.hbs';

export default Marionette.View.extend({
  template,
  templateContext() {
    return {
      status: this.options.status,
      namespacesUpdated: this.options.namespacesUpdated,
      optionsDiffs: this.options.optionsDiffs,
      clusterName: this.options.clusterName,
    };
  },
});
