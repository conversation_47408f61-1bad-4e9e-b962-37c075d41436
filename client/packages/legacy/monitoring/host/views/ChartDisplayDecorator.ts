import _ from 'underscore';

import * as chartIdHelpers from '@packages/legacy/charts/chartIdHelpers';

const ChartDisplayDecorator = function (this: $TSFixMe, model: $TSFixMe) {
  this.model = model;
};

_.extend(ChartDisplayDecorator.prototype, {
  chartTitle() {
    return function (this: $TSFixMe, chartId: $TSFixMe) {
      return this.model.get('displayName') || chartIdHelpers.getTitle(chartId);
    }.bind(this);
  },
  decorate() {
    return {
      chartTitle: this.chartTitle(),
      withStatusDbSelect: this.model.canChartBeFilteredByDB(),
    };
  },
});

export default ChartDisplayDecorator;
