import chai from 'chai';

import * as hostHelper from '@packages/legacy/auto/helpers/hostHelper';
import Process from '@packages/legacy/core/models/deployment/Process';
import ProcessMonitoringState from '@packages/legacy/core/models/deployment/ProcessMonitoringState';
import ReplicaSetConfiguration from '@packages/legacy/core/models/deployment/ReplicaSetConfiguration';

const expect = chai.expect;

describe('auto/helpers/hostHelper.js', function () {
  describe('getHostTypeClass', function () {
    describe('when provided a process', function () {
      beforeEach(function (this: $TSFixMe) {
        this.subject = new Process({
          version: '2.6.3',
          authSchemaVersion: 5,
        });
      });

      describe('that is managed (no ping)', function () {
        beforeEach(function (this: $TSFixMe) {
          this.subject.set('managed', true);
        });

        it('returns standalone', function (this: $TSFixMe) {
          expect(hostHelper.getHostTypeClass(this.subject)).to.equal('standalone');
        });

        describe('that has a stale ping describing a secondary (i.e. process was removed from replica set)', function () {
          beforeEach(function (this: $TSFixMe) {
            this.subject.set(
              'state',
              new ProcessMonitoringState({
                lastPing: 1,
                hostTypes: [8],
                replicaState: 'SECONDARY',
              })
            );
          });

          it('returns standalone', function (this: $TSFixMe) {
            expect(hostHelper.getHostTypeClass(this.subject)).to.equal('standalone');
          });
        });

        describe('that is a mongos', function () {
          beforeEach(function (this: $TSFixMe) {
            this.subject.set('processType', 'mongos');
          });

          it('returns mongos', function (this: $TSFixMe) {
            expect(hostHelper.getHostTypeClass(this.subject)).to.equal('mongos');
          });
        });

        describe('that is a config server', function () {
          beforeEach(function (this: $TSFixMe) {
            this.subject.getArgs().setClusterRole('configsvr');
          });

          it('returns configsvr', function (this: $TSFixMe) {
            expect(hostHelper.getHostTypeClass(this.subject)).to.equal('configsvr');
          });
        });

        describe('that is a replica set member', function () {
          beforeEach(function (this: $TSFixMe) {
            this.subject.getArgs().setReplSetName('rs0');
            this.subject.set('rsConfig', new ReplicaSetConfiguration());
          });

          it('returns nostate', function (this: $TSFixMe) {
            expect(hostHelper.getHostTypeClass(this.subject)).to.equal('nostate');
          });

          describe('that is an arbiter', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('rsConfig').set('arbiterOnly', true);
            });

            it('returns arbiter', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('arbiter');
            });
          });

          describe('that is a delayed', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('rsConfig').set({
                hidden: true,
                slaveDelay: 60,
              });
            });

            it('returns delayed', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('delayed');
            });
          });

          describe('that is a hidden', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('rsConfig').set('hidden', true);
            });

            it('returns hidden-s', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('hidden-s');
            });
          });

          describe('that is a config server', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.getArgs().setClusterRole('configsvr');
            });

            it('returns configsvr', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('configsvr-nostate');
            });
          });
        });
      });

      describe('that is unmanaged (no ping)', function () {
        beforeEach(function (this: $TSFixMe) {
          this.subject.set('managed', false);
          this.subject.set(
            'state',
            new ProcessMonitoringState({
              lastPing: 0,
            })
          );
        });

        it('returns nostate', function (this: $TSFixMe) {
          expect(hostHelper.getHostTypeClass(this.subject)).to.equal('nostate');
        });

        describe('that has a ping', function () {
          beforeEach(function (this: $TSFixMe) {
            this.subject.get('state').set('lastPing', 1);
          });

          describe('that is a config server', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('state').set('hostTypes', [3]);
              this.subject.get('state').set('isConf', true);
            });

            it('returns configsvr', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('configsvr');
            });

            describe('but has ping state DOWN', function () {
              beforeEach(function (this: $TSFixMe) {
                this.subject.get('state').set('replicaState', 'DOWN');
              });

              it('returns down', function (this: $TSFixMe) {
                expect(hostHelper.getHostTypeClass(this.subject)).to.equal('configsvr-down');
              });
            });
          });

          describe('that is a replica set member', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('state').set('replicaState', 'SECONDARY');
            });

            it('returns secondary', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('secondary');
            });

            describe('that is an arbiter', function () {
              beforeEach(function (this: $TSFixMe) {
                this.subject.get('state').set('replicaState', 'ARBITER');
              });

              it('returns arbiter', function (this: $TSFixMe) {
                expect(hostHelper.getHostTypeClass(this.subject)).to.equal('arbiter');
              });
            });

            describe('that is a delayed', function () {
              beforeEach(function (this: $TSFixMe) {
                this.subject.get('state').set({
                  hidden: true,
                  slaveDelay: 60,
                });
              });

              it('returns delayed', function (this: $TSFixMe) {
                expect(hostHelper.getHostTypeClass(this.subject)).to.equal('delayed');
              });

              describe('but has ping state DOWN', function () {
                beforeEach(function (this: $TSFixMe) {
                  this.subject.get('state').set('replicaState', 'DOWN');
                });

                it('returns down', function (this: $TSFixMe) {
                  expect(hostHelper.getHostTypeClass(this.subject)).to.equal('down');
                });
              });
            });

            describe('that is a hidden', function () {
              beforeEach(function (this: $TSFixMe) {
                this.subject.get('state').set('hidden', true);
              });

              it('returns hidden-s', function (this: $TSFixMe) {
                expect(hostHelper.getHostTypeClass(this.subject)).to.equal('hidden-s');
              });
            });
          });

          describe('that has hostType standalone', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('state').set('hostTypes', [1]);
            });

            it('returns recovering', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('standalone');
            });
          });

          describe('that has hostType recovering', function () {
            beforeEach(function (this: $TSFixMe) {
              this.subject.get('state').set('hostTypes', [10]);
            });

            it('returns recovering', function (this: $TSFixMe) {
              expect(hostHelper.getHostTypeClass(this.subject)).to.equal('recovering');
            });
          });
        });
      });
    });
  });
});
