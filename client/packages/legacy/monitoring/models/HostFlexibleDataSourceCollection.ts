import Backbone from 'backbone';
import _ from 'underscore';

import metricsService from '@packages/common/services/metricsService';
import jqueryErrorHandler from '@packages/common/utils/jqueryErrorHandler';
import mongoDate from '@packages/date';
// v1 legacy
import vent from '@packages/legacy/aggregator';
import HostDataSource from '@packages/legacy/charts/models/HostDataSource';
import { getZoomOrWindowRequestProps } from '@packages/legacy/monitoring/helpers/legacyChartHelpers';
import HostDataSourceCollection from '@packages/legacy/monitoring/models/HostDataSourceCollection';
import HostFlexibleEventDataSource from '@packages/legacy/monitoring/models/HostFlexibleEventDataSource';
import flexibleChartService from '@packages/legacy/monitoring/services/flexibleChartService';
import combineDygraphsData from '@packages/legacy/monitoring/utils/combineDygraphsData';
import { LOCK_PERCENT_CHART_ID } from '@packages/legacy/monitoring/utils/idHelpers';
import transformMetricsIntoDygraphsFormat from '@packages/legacy/monitoring/utils/transformMetricsIntoDygraphsFormat';

const HARDWARE_DATA_TYPES = ['munin', 'disk', 'process'];

// This file enables a data source collection for Charts that use the new flexible monitoring data (aka StorageModeV3)
// It is designed to run alongside the existing charts, and as such, uses a kind of debounce in order to batch up requests.
// The legacy chart data requires one request per host per chart. The new only requires one per host. As such, this collection
// manages the job of batching without needing to refactor the existing legacy chart mechanism (which will be sunsetted at a
// later stage once all users have migrated to the new flexible API). -- JJM

export default HostDataSourceCollection.extend({
  model: HostDataSource.extend({
    // v3 (flexible monitoring) fetches should occur once per host (as opposed to classic monitoring which fetch
    // once per host / chart)
    fetchData() {
      return this.collection.fetchData(this);
    },

    // clones need parent collection set
    clone() {
      const cloned = Backbone.Model.prototype.clone.call(this);
      cloned.collection = this.collection;
      return cloned;
    },

    createEventDataSource() {
      const collection = this.collection;

      // The caching prevents sucessive calls for the same annotation data.
      // This is only necessary to support the approach used by the classic monitoring
      // mode prior to its removal - JJM
      const CachedHostFlexibleEventDataSource = HostFlexibleEventDataSource.extend({
        fetchData() {
          if (collection._lastAnnotationFetch) {
            return collection._lastAnnotationFetch;
          }

          collection._lastAnnotationFetch = HostFlexibleEventDataSource.prototype.fetchData.call(this);

          return collection._lastAnnotationFetch;
        },
      });

      return new CachedHostFlexibleEventDataSource({
        settingsModel: this.collection.settingsModel,
        hostId: this.get('hostId'),
        metricsState: this.collection.metricsState,
      });
    },

    getPermalinkUri() {
      let dataType = this.has('dataType') ? this.get('dataType') : this.collection.dataType;
      if (HARDWARE_DATA_TYPES.indexOf(dataType) > -1) {
        dataType = 'hardware';
      }
      return `#/metrics/host/${this.get('hostId')}/${dataType}/${this.get('chartId')}`;
    },

    emailLinkToChart(props: $TSFixMe) {
      return metricsService.emailChart(_.extend({ groupId: this.collection.settingsModel.get('GROUP_ID') }, props));
    },

    loadPartitionInfo() {
      if (this.has('partition')) {
        return this.collection.loadPartitionInfo();
      }
    },
  }),

  initialize(_: $TSFixMe, options: $TSFixMe) {
    this.settingsModel = options.settingsModel;
    this.getCurrentDb = options.getCurrentDb;
    this.replicaSetMode = !!options.replicaSetMode;
    this.metricsState = options.metricsState;

    this.listenTo(vent, 'chart:refreshChart', function (this: $TSFixMe) {
      this._lastAnnotationFetch = undefined;
    });

    HostDataSourceCollection.prototype.initialize.apply(this, arguments);
  },

  url() {
    return `/metrics/v1/groups/${this.settingsModel.get('GROUP_ID')}/hosts/${this.hostId}`;
  },

  // Parse the stacked Object of chartIds into a flat collection
  parse(chartDefs: $TSFixMe) {
    const hasDataTypes = !!this.dataType;
    const possibleDataTypes = this.dataType === 'hardware' ? HARDWARE_DATA_TYPES : [this.dataType];

    return Object.keys(chartDefs)
      .filter((dataType) => {
        return hasDataTypes ? possibleDataTypes.indexOf(dataType) > -1 : true;
      })
      .map((dataType) => {
        return Object.keys(chartDefs[dataType] || {}).map((key) => {
          return {
            hostId: this.hostId,
            chartId: key,
            id: key,
            displayName: chartDefs[dataType][key].label,
            partition: chartDefs[dataType][key].partition, // optional
            dataType,
          };
        });
      })
      .reduce((memo, current) => memo.concat(current), [])
      .sort((chartA, chartB) => {
        return chartA.displayName.toLowerCase() > chartB.displayName.toLowerCase() ? 1 : -1;
      });
  },

  // batch up all chart requests for this hostID into one for flexible (v3) monitoring
  fetchData(model: $TSFixMe) {
    const chartId = model.get('chartId');
    let dataType = model.get('dataType');

    if (HARDWARE_DATA_TYPES.indexOf(dataType) > -1) {
      dataType = 'hardware';
    }

    const props = _.extend(
      {
        groupId: this.settingsModel.get('GROUP_ID'),
        hostId: this.hostId,
        type: dataType,
        resolution: model.get('granularity') || this.settingsModel.get('DEFAULT_CHART_GRANULARITY'),
      },
      getZoomOrWindowRequestProps(this.metricsState, model.get('zoom') || this.settingsModel.get('DEFAULT_CHART_ZOOM'))
    );

    this._lastFetch = this._lastFetch || {};

    // if resolution or duration has changed,
    // or dbstats page and currentDb has changed,
    // or no load for this host in 30 seconds, then start a new fetch
    if (
      this._lastFetch.resolution !== props.resolution ||
      this._lastFetch.duration !== props.duration ||
      this._lastFetch.since !== props.since ||
      this._lastFetch.until !== props.until ||
      (dataType === 'dbstats' && this._lastFetch.currentDb !== this.getCurrentDb()) ||
      mongoDate().isAfter(mongoDate(this._lastFetch.timestamp || 0).add(30, 'seconds'))
    ) {
      this._lastFetch.timestamp = mongoDate().valueOf();

      let loadFunction = 'loadTimelineDataForHost';

      // DBStats require a database to have been selected with each request, and require a different endpoint
      if (dataType === 'dbstats') {
        props.getCurrentDb = this.getCurrentDb;
        this._lastFetch.currentDb = this.getCurrentDb();
        loadFunction = 'loadStatsForDB';
      } else if (this.replicaSetMode) {
        loadFunction = 'loadTimelineDataForHostWithinReplicaSet';
      }

      // load the data
      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
      this._lastFetch.whenLoaded = flexibleChartService[loadFunction](props);

      // set a single legacy handler to notify epoch controls that data has returned with metadata headers (auto gran)
      this._lastFetch.whenLoaded
        .then((response: $TSFixMe) => {
          // trigger metadata for these legacy charts (i.e. not in charts overhauled)
          vent.trigger('legacy.chart.data.resolution', response);
        }, jqueryErrorHandler)
        .then(_.identity, (error: $TSFixMe) => {
          vent.trigger('legacy.chart.data.rejection', error);
        });

      this._lastFetch.resolution = props.resolution;
      this._lastFetch.duration = props.duration;
      this._lastFetch.since = props.since;
      this._lastFetch.until = props.until;
    }

    return this._lastFetch.whenLoaded.then((response: $TSFixMe) => {
      // Support new auto-granularity endpoint that has "meta".
      let data = response.metrics;

      if (!(chartId in data)) {
        // Some chart responses (replica sets and hardware chartsf for instance) are grouped into sections
        // with their own timestamps. This is because hardware charts like CPU are captured separately
        // to regular monitoring data.

        // so massage the content into a form that works for this chart
        const foundGroup = Object.keys(data).filter((chartGroup) => chartId in data[chartGroup])[0];
        if (foundGroup) {
          data = data[foundGroup];
        } else {
          // if the metric doesn't show up for this host, then ignore.
          return {};
        }
      }

      // massage into form that dygrahs likes
      const dygraphData = transformMetricsIntoDygraphsFormat(chartId)(data);

      // we're using the new metrics transformer to massage this data. Lets massage this one more step for the
      // purposes of the legacy charts
      (dygraphData as $TSFixMe).columns = dygraphData.labels;

      // include metadata on request with dygraphData
      (dygraphData as $TSFixMe).meta = response.meta;

      const lockDatabase = this.settingsModel.get('CURRENT_HOST_DB');

      // Note: even though the chartIDs are data-driven, we hardcode LOCK_PERCENT_CHART_ID as we KNOW it allows db filtering.
      // Suboptimal, but unavoidable unless the chart data lists those IDs with this caveat. -- JJM
      if (chartId !== LOCK_PERCENT_CHART_ID || !lockDatabase || lockDatabase === 'effective') {
        // no-op for any but lock percent chart
        return dygraphData;
      }

      // otherwise for lock % chart when a lockDatabase exists, load data for the database
      return flexibleChartService
        .loadLockPercentForDB(
          _.extend(
            {
              databaseName: lockDatabase,
            },
            props
          )
        )
        .then((lockResponse) => {
          const lockDataForDb = lockResponse.metrics.dbPerf;

          const dygraphLockData = transformMetricsIntoDygraphsFormat(chartId)(lockDataForDb);

          const combinedData = combineDygraphsData(dygraphData, dygraphLockData);

          // (ibid)
          (combinedData as $TSFixMe).columns = combinedData.labels;
          (combinedData as $TSFixMe).meta = response.meta;

          return combinedData;
        });
    });
  },

  loadPartitionInfo() {
    if (!this.whenHostPartitionsLoaded) {
      this.whenHostPartitionsLoaded = metricsService.getHostPartitions(this.hostId);
    }

    return this.whenHostPartitionsLoaded;
  },
});
