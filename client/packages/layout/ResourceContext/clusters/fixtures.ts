import { ClusterResources } from '.';

import { ProjectResources } from '../projects';
import { CURRENT_PROJECT_ID } from '../projects/fixtures';
import { type ReturnedCluster } from './fetchClusters';

export const CURRENT_CLUSTER: ReturnedCluster = {
  name: 'My First Cluster',
  metricsId: 'first_cluster',
  isSharded: true,
  isServerless: false,
  isFlex: false,
  hasBackup: false,
};
const OTHER_CLUSTER: ReturnedCluster = {
  name: 'My Second Cluster',
  metricsId: 'second_cluster',
  isSharded: true,
  isServerless: false,
  isFlex: false,
  hasBackup: false,
};
const SERVERLESS_CLUSTER: ReturnedCluster = {
  name: 'My Serverless Cluster',
  metricsId: 'serverless_cluster',
  isSharded: true,
  isServerless: true,
  isFlex: false,
  hasBackup: false,
};
const FLEX_CLUSTER: ReturnedCluster = {
  name: 'My Flex Cluster',
  metricsId: 'flex_cluster',
  isSharded: false,
  isServerless: false,
  isFlex: true,
  hasBackup: false,
};
export const clusters: Array<ReturnedCluster> = [CURRENT_CLUSTER, OTHER_CLUSTER, SERVERLESS_CLUSTER, FLEX_CLUSTER];

export const clustersResourceSegment = {
  allResourcesLink: {
    host: 'dataServices',
    path: `/v2/${CURRENT_PROJECT_ID}#/clusters`,
  },
  availableResources: [
    {
      link: {
        host: 'dataServices',
        path: `/v2/${CURRENT_PROJECT_ID}#/clusters/detail/${CURRENT_CLUSTER.name}`,
      },
      metricsId: CURRENT_CLUSTER.metricsId,
      isSharded: true,
      hasBackup: false,
      resourceId: CURRENT_CLUSTER.name,
      resourceName: CURRENT_CLUSTER.name,
      resourceType: ClusterResources.resourceType,
      parentResourceId: CURRENT_PROJECT_ID,
      parentResourceType: ProjectResources.resourceType,
    },
    {
      link: {
        host: 'dataServices',
        path: `/v2/${CURRENT_PROJECT_ID}#/clusters/detail/${OTHER_CLUSTER.name}`,
      },
      metricsId: OTHER_CLUSTER.metricsId,
      isSharded: true,
      hasBackup: false,
      resourceId: OTHER_CLUSTER.name,
      resourceName: OTHER_CLUSTER.name,
      resourceType: ClusterResources.resourceType,
      parentResourceId: CURRENT_PROJECT_ID,
      parentResourceType: ProjectResources.resourceType,
    },
    {
      link: {
        host: 'dataServices',
        path: `/v2/${CURRENT_PROJECT_ID}#/serverless/detail/${SERVERLESS_CLUSTER.name}`,
      },
      metricsId: SERVERLESS_CLUSTER.metricsId,
      isSharded: true,
      hasBackup: false,
      resourceId: SERVERLESS_CLUSTER.name,
      resourceName: SERVERLESS_CLUSTER.name,
      resourceType: ClusterResources.resourceType,
      parentResourceId: CURRENT_PROJECT_ID,
      parentResourceType: ProjectResources.resourceType,
    },
    {
      link: {
        host: 'dataServices',
        path: `/v2/${CURRENT_PROJECT_ID}#/flex/detail/${FLEX_CLUSTER.name}`,
      },
      metricsId: FLEX_CLUSTER.metricsId,
      isSharded: false,
      hasBackup: false,
      resourceId: FLEX_CLUSTER.name,
      resourceName: FLEX_CLUSTER.name,
      resourceType: ClusterResources.resourceType,
      parentResourceId: CURRENT_PROJECT_ID,
      parentResourceType: ProjectResources.resourceType,
    },
  ],
  currentId: CURRENT_CLUSTER.name,
  resourceType: ClusterResources.resourceType,
  resourceTypeLabel: 'cluster',
};
