import { useEffect } from 'react';

import { ResourceProvider, useResourceContext } from '..';

import globalSettings from '@packages/common/constants/globalSettings';
import { render, screen, waitFor } from '@packages/react-testing-library';

import * as fetchClustersAll from './fetchClusters';
import { CURRENT_ORG_ID } from '../orgs/fixtures';
import { CURRENT_PROJECT_ID } from '../projects/fixtures';
import { clusters, clustersResourceSegment, CURRENT_CLUSTER } from './fixtures';

const clustersMock = jest.spyOn(fetchClustersAll, 'fetchClusters').mockResolvedValue(clusters);
const mockResourcePath = jest.fn();

globalSettings.ORG_ID = CURRENT_ORG_ID;
globalSettings.GROUP_ID = CURRENT_PROJECT_ID;

// For some reason spyOn wouldn't appropriately stub the context
jest.mock('@lg-private/cloud-nav', () => ({
  ...jest.requireActual('@lg-private/cloud-nav'),
  useCloudNavContext: () => ({
    setProjectResourcePath: mockResourcePath,
    setOrganization: jest.fn(),
  }),
}));

const ClusterLayout = () => {
  const { activeCluster, activeOrgId, activeProjectId, setActiveCluster } = useResourceContext();

  useEffect(() => {
    setActiveCluster?.(CURRENT_CLUSTER.name);
  }, [setActiveCluster]);

  return (
    <>
      <h2>{activeCluster?.resourceName}</h2>
      <h2>{activeProjectId}</h2>
      <h2>{activeOrgId}</h2>
    </>
  );
};

const setup = async () => {
  render(
    <ResourceProvider>
      <ClusterLayout />
    </ResourceProvider>
  );

  await waitFor(() => {
    expect(clustersMock).toHaveBeenCalled();
  });
};

describe('@packages/layout/ResourceContext cluster-specific tests', () => {
  it('should update a Resource Path for the active project', async () => {
    await setup();

    screen.getByRole('heading', { name: CURRENT_CLUSTER.name });
    expect(mockResourcePath).toHaveBeenCalledWith([clustersResourceSegment]);
  });

  it('should also update other active resources', async () => {
    await setup();

    screen.getByRole('heading', { name: CURRENT_ORG_ID });
    screen.getByRole('heading', { name: CURRENT_PROJECT_ID });
  });
});
