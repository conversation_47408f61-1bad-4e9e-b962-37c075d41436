import { ResourceType } from '@packages/types/resources';

import { fetchConfigNodes } from '@packages/layout/ResourceContext/fetchClusterNodes';

import { AtlasConfigServerResources } from '../../replicaSets/configServers';
import { AtlasHostResources } from '../baseHosts';

export class AtlasConfigNodeResources extends AtlasHostResources {
  public static get resourceType() {
    return ResourceType.CONFIG_NODE;
  }

  protected static getResourceTypeLabel() {
    return 'node';
  }

  protected static getReplicaSetResourceLoader() {
    return AtlasConfigServerResources;
  }

  protected static fetchSiblings(id: string) {
    return fetchConfigNodes(id);
  }
}
