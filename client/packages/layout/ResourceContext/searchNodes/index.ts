import {
  type HostName as LG<PERSON>ostName,
  type ResourceInterface as LGResource,
  type ResourceSegmentInterface as LGResourceSegment,
} from '@lg-private/cloud-nav';

import { ResourceType } from '@packages/types/resources';

import { ClusterResources } from '../clusters';
import { getCurrentOrgId } from '../getCurrentOrgId';
import { getCurrentProjectId } from '../getCurrentProjectId';
import { getCurrentResourceFromResources } from '../getCurrentResourceFromResources';
import { isClusterSharded } from '../isClusterSharded';
import { OrgResources } from '../orgs';
import { ProjectResources } from '../projects';
import { AtlasShardResources as ShardResources } from '../replicaSets/shards';
import { ResourceTypeLoader } from '../types';
import { fetchSearchNodes, getShardId, ReturnedSearchNode } from './fetchSearchNodes';

export class SearchNodeResources extends ResourceTypeLoader {
  static resourceType = ResourceType.SEARCH_NODE;

  static async fullResourceHierarchy({ id, currentClusterName }: { id: string; currentClusterName: string }) {
    const currentOrgId = getCurrentOrgId();
    const currentProjectId = getCurrentProjectId();
    const isSharded = await isClusterSharded(currentClusterName);
    if (isSharded) {
      const shardId = await getShardId({
        currentClusterName,
        currentProjectId,
        currentNodeId: id,
      });
      return await Promise.all([
        OrgResources.resourceSegment(currentOrgId),
        ProjectResources.resourceSegment(currentProjectId),
        ClusterResources.resourceSegment(currentClusterName, currentProjectId),
        ShardResources.resourceSegment({ replicaSetId: shardId, currentClusterName, currentProjectId }),
        this.resourceSegment({ id, shardId, currentClusterName, currentProjectId }),
      ]);
    }
    return await Promise.all([
      OrgResources.resourceSegment(currentOrgId),
      ProjectResources.resourceSegment(currentProjectId),
      ClusterResources.resourceSegment(currentClusterName, currentProjectId),
      this.resourceSegment({ id, currentClusterName, currentProjectId }),
    ]);
  }

  static async resourceSegment({
    id,
    currentClusterName,
    currentProjectId,
    shardId,
  }: {
    id: string;
    currentClusterName: string;
    currentProjectId: string;
    shardId?: string;
  }) {
    const searchNodes = await this.fetchResources({ currentClusterName, currentProjectId, shardId });
    const currentSearchNode = getCurrentResourceFromResources({
      resources: searchNodes,
      id,
      resourceType: this.resourceType,
    });
    return this.mapToResourceSegment({
      resource: currentSearchNode,
      allResources: searchNodes,
      currentProjectId,
    });
  }

  static async fetchResources({
    currentClusterName,
    currentProjectId,
    shardId,
  }: {
    currentClusterName: string;
    currentProjectId: string;
    shardId?: string;
  }): Promise<Array<LGResource>> {
    const searchNodes = await fetchSearchNodes(currentProjectId, currentClusterName, shardId);
    const searchNodesAsResources = searchNodes.map((node) =>
      this.mapToResourceType({ node, currentClusterName, currentProjectId, shardId })
    );
    return searchNodesAsResources;
  }

  static mapToResourceSegment({
    resource,
    allResources,
    currentProjectId,
  }: {
    resource: LGResource;
    allResources: Array<LGResource>;
    currentProjectId: string;
  }): LGResourceSegment {
    const isSharded = resource.parentResourceType === ShardResources.resourceType;
    const path = isSharded
      ? `/v2/${currentProjectId}#/host/replicaSet/${resource.parentResourceId}`
      : `/v2/${currentProjectId}#/clusters/detail/${resource.parentResourceId}`;
    return {
      resourceType: resource.resourceType,
      resourceTypeLabel: resource.resourceType,
      currentId: resource.resourceId,
      availableResources: allResources,
      allResourcesLink: {
        host: 'dataServices' as LGHostName,
        path,
      },
    };
  }

  static mapToResourceType({
    node,
    currentClusterName,
    currentProjectId,
    shardId,
  }: {
    node: ReturnedSearchNode;
    currentClusterName: string;
    currentProjectId: string;
    shardId?: string;
  }): LGResource {
    const isSharded = !!shardId;
    const parentResourceType = isSharded ? ShardResources.resourceType : ClusterResources.resourceType;
    const parentResourceId = isSharded ? shardId : currentClusterName;
    return {
      resourceType: SearchNodeResources.resourceType,
      resourceId: node.id,
      resourceName: node.name,
      parentResourceType,
      parentResourceId,
      link: {
        host: 'dataServices' as LGHostName,
        path: `/v2/${currentProjectId}#/metrics/search/${currentClusterName}/${node.uri}/status`, // Ask Adam - something is being scraped here
      },
    };
  }
}
