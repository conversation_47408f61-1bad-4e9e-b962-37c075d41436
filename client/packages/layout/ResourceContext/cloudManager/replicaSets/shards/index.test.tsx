import { useEffect } from 'react';

import { CloudManagerShardResources } from '.';

import * as fetchCloudManagerDeploymentsAll from '@packages/layout/ResourceContext/cloudManager/deployments/fetchCloudManagerDeployments';
import * as fetchReplicaSetsAll from '@packages/layout/ResourceContext/cloudManager/replicaSets/fetchReplicaSets';
import * as getResourceHierarchyForReplicaSetAll from '@packages/layout/ResourceContext/getResourceHierarchyForReplicaSet';
import globalSettings from '@packages/common/constants/globalSettings';
import { ResourceProvider, useResourceContext } from '@packages/layout/ResourceContext';
import {
  cloudManagerDeployments,
  cloudManagerDeploymentsResourceSegment,
  CURRENT_CLOUD_MANAGER_DEPLOYMENT,
} from '@packages/layout/ResourceContext/cloudManager/deployments/fixtures';
import { CURRENT_ORG_ID } from '@packages/layout/ResourceContext/orgs/fixtures';
import { CURRENT_PROJECT_ID } from '@packages/layout/ResourceContext/projects/fixtures';
import { render, screen, waitFor } from '@packages/react-testing-library';

import { CURRENT_SHARD, shards, shardsResourceSegment } from './fixtures';

jest.spyOn(fetchCloudManagerDeploymentsAll, 'fetchCloudManagerDeployments').mockResolvedValue(cloudManagerDeployments);
jest
  .spyOn(fetchReplicaSetsAll, 'getCloudManagerDeploymentIdForShard')
  .mockResolvedValue(CURRENT_CLOUD_MANAGER_DEPLOYMENT.id);
jest
  .spyOn(getResourceHierarchyForReplicaSetAll, 'getResourceHierarchyForReplicaSet')
  .mockImplementation((replicaSetId) => CloudManagerShardResources.fullResourceHierarchy({ replicaSetId }));

const mockResourcePath = jest.fn();

globalSettings.ORG_ID = CURRENT_ORG_ID;
globalSettings.GROUP_ID = CURRENT_PROJECT_ID;

// For some reason spyOn wouldn't appropriately stub the context
jest.mock('@lg-private/cloud-nav', () => ({
  ...jest.requireActual('@lg-private/cloud-nav'),
  useCloudNavContext: () => ({ setProjectResourcePath: mockResourcePath, setOrganization: jest.fn() }),
}));

const ShardLayout = () => {
  const { activeShard, activeCloudManagerDeployment, activeOrgId, activeProjectId, setActiveReplicaSet } =
    useResourceContext();

  useEffect(() => {
    setActiveReplicaSet?.(CURRENT_SHARD.id);
  }, [setActiveReplicaSet]);

  return (
    <>
      <h2>{activeShard?.resourceName}</h2>
      <h2>{activeCloudManagerDeployment?.resourceName}</h2>
      <h2>{activeProjectId}</h2>
      <h2>{activeOrgId}</h2>
    </>
  );
};

const setup = async () => {
  const shardsMock = jest.spyOn(fetchReplicaSetsAll, 'fetchShardsByCloudManagerDeployment').mockResolvedValue(shards);

  render(
    <ResourceProvider isCloudManager>
      <ShardLayout />
    </ResourceProvider>
  );

  await waitFor(() => {
    expect(shardsMock).toHaveBeenCalled();
  });
};

describe('@packages/layout/ResourceContext Cloud Manager shard-specific tests', () => {
  it('should update a Resource Path for the active shard', async () => {
    await setup();

    screen.getByRole('heading', { name: CURRENT_SHARD.name });
    expect(mockResourcePath).toHaveBeenCalledWith([cloudManagerDeploymentsResourceSegment, shardsResourceSegment]);
  });

  it('should also update other active resources', async () => {
    await setup();

    screen.getByRole('heading', { name: CURRENT_CLOUD_MANAGER_DEPLOYMENT.name });
    screen.getByRole('heading', { name: CURRENT_ORG_ID });
    screen.getByRole('heading', { name: CURRENT_PROJECT_ID });
  });
});
