import * as fetchDeploymentsModule from './fetchDeployments';
import * as isDedicatedConfigServerModule from './isDedicatedConfigServer';
import { CloudManagerDeploymentResources } from './cloudManager/deployments';
import { CloudManagerClusterNodeResources } from './cloudManager/hosts/clusterNodes';
import { CloudManagerConfigNodeResources } from './cloudManager/hosts/configNodes';
import { CloudManagerMongosResources } from './cloudManager/hosts/mongoses';
import { getResourceHierarchyForHost } from './getResourceHierarchyForHost';
import { AtlasClusterNodeResources } from './hosts/clusterNodes';
import { AtlasConfigNodeResources } from './hosts/configNodes';
import { AtlasMongosResources } from './hosts/mongoses';

function mockIsDedicatedConfigServer(isDedicatedConfigServer: boolean) {
  jest.spyOn(isDedicatedConfigServerModule, 'isDedicatedConfigServer').mockReturnValue(isDedicatedConfigServer);
}

function mockDeployment({
  topLevelItemIds = [],
  mongosIds = [],
}: {
  topLevelItemIds?: Array<string>;
  mongosIds?: Array<string>;
} = {}) {
  jest.spyOn(fetchDeploymentsModule, 'fetchDeployments').mockResolvedValue({
    getItemById: (id: string) => ({
      isTopLevelItem: () => topLevelItemIds.includes(id),
      isMongos: () => mongosIds.includes(id),
      isConfigServer: () => false,
      getClusterId: () => undefined,
    }),
  } as $TSFixMe);
}

describe('setActiveHost', () => {
  it('sets the active cluster node when in Atlas', async () => {
    mockDeployment();
    mockIsDedicatedConfigServer(false);

    const clusterNodeHierarchySpy = jest
      .spyOn(AtlasClusterNodeResources, 'fullResourceHierarchy')
      .mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: false });

    expect(clusterNodeHierarchySpy).toHaveBeenCalledWith({ id: 'someid' });
  });

  it('sets the active mongos when in Atlas', async () => {
    mockDeployment({ mongosIds: ['someid'] });
    mockIsDedicatedConfigServer(false);

    const mongosHierarchySpy = jest.spyOn(AtlasMongosResources, 'fullResourceHierarchy').mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: false });

    expect(mongosHierarchySpy).toHaveBeenCalledWith({ id: 'someid' });
  });

  it('sets the active config node when in Atlas', async () => {
    mockDeployment();
    mockIsDedicatedConfigServer(true);

    const configNodeHierarchySpy = jest.spyOn(AtlasConfigNodeResources, 'fullResourceHierarchy').mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: false });

    expect(configNodeHierarchySpy).toHaveBeenCalledWith({ id: 'someid' });
  });

  it('sets the active Cloud Manager deployment when in Cloud Manager with a top-level replica set', async () => {
    mockDeployment({ topLevelItemIds: ['someid'] });
    mockIsDedicatedConfigServer(false);

    const cloudManagerDeploymentHierarchySpy = jest
      .spyOn(CloudManagerDeploymentResources, 'fullResourceHierarchy')
      .mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: true });

    expect(cloudManagerDeploymentHierarchySpy).toHaveBeenCalledWith('someid');
  });

  it('sets the active cluster node when in Cloud Manager with a non-top-level replica set', async () => {
    mockDeployment({ topLevelItemIds: [] });
    mockIsDedicatedConfigServer(false);

    const cloudManagerClusterNodeHierarchySpy = jest
      .spyOn(CloudManagerClusterNodeResources, 'fullResourceHierarchy')
      .mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: true });

    expect(cloudManagerClusterNodeHierarchySpy).toHaveBeenCalledWith({ id: 'someid' });
  });

  it('sets the active mongos when in Cloud Manager', async () => {
    mockDeployment({ mongosIds: ['someid'] });
    mockIsDedicatedConfigServer(false);

    const mongosHierarchySpy = jest.spyOn(CloudManagerMongosResources, 'fullResourceHierarchy').mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: true });

    expect(mongosHierarchySpy).toHaveBeenCalledWith({ id: 'someid' });
  });

  it('sets the active config node when in Atlas', async () => {
    mockDeployment();
    mockIsDedicatedConfigServer(true);

    const configNodeHierarchySpy = jest
      .spyOn(CloudManagerConfigNodeResources, 'fullResourceHierarchy')
      .mockResolvedValue([]);

    await getResourceHierarchyForHost('someid', { isCloudManager: true });

    expect(configNodeHierarchySpy).toHaveBeenCalledWith({ id: 'someid' });
  });
});
