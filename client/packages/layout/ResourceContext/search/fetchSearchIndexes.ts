import { BaseSearchIndex } from '@packages/types/clusters/atlasSearch/baseSearchIndex';

import { getSearchIndexes } from '../getSearchIndexes';

export interface ReturnedIndex {
  indexID: string;
  name: string;
  collectionName: string;
  database: string;
}

export const fetchSearchIndexes = async ({
  currentProjectId,
  currentClusterName,
  currentCollectionName,
  database,
  filter,
}: {
  currentProjectId: string;
  currentClusterName: string;
  currentCollectionName: string;
  database: string;
  filter: (index: BaseSearchIndex) => boolean;
}): Promise<Array<ReturnedIndex>> => {
  const indexes = await getSearchIndexes({ currentProjectId, currentClusterName });
  const indexesForCollection = indexes.filter(
    (index) => index.collectionName === currentCollectionName && index.database === database && filter(index)
  );
  return indexesForCollection.map(({ indexID, name, collectionName, database }) => ({
    indexID,
    name,
    collectionName,
    database,
  }));
};
