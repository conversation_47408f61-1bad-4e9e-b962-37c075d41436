import { SideNavVariant } from '@lg-private/cloud-nav';

import { CloudLayout } from './CloudLayout';
import { LegacyMongoLayout, type MongoLayoutProps } from './LegacyMongoLayout';
import { useIsInNewNav } from './useIsInNewNav';

// Switch to show the new or old nav from /params
const MongoLayout = <V extends SideNavVariant>(props: MongoLayoutProps<V>) => {
  const isInNewNav = useIsInNewNav(); // This is synchronous
  return isInNewNav ? <CloudLayout {...props} /> : <LegacyMongoLayout {...props} />;
};

export { MongoLayout, MongoLayoutProps };
