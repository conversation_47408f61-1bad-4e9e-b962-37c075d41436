import userEvent from '@testing-library/user-event';

import { UsersPage } from '@packages/access/components/Users/<USER>';
import { render, screen } from '@packages/react-testing-library';
import { getRows } from '@packages/test-utils/tableQueries';

interface TestProps {
  isUserAdmin: boolean;
  isGlobalReadOnly: boolean;
  isLdap: boolean;
  isSaml: boolean;
  currentOrgId: string;
}

interface UserInterface {
  firstName: string;
  lastName: string;
  emailAddress: string;
  username: string;
}

const userFixtures: Array<UserInterface> = [
  {
    firstName: 'User',
    lastName: 'One',
    emailAddress: '<EMAIL>',
    username: '<EMAIL>',
  },
  {
    firstName: 'Admin',
    lastName: 'Two',
    emailAddress: '<EMAIL>',
    username: '<EMAIL>',
  },
];

const centralUrl = 'http://test.mongodb.com';

const orgId = 'oid01';

describe('js/access/components/Users/<USER>', () => {
  const user = userEvent.setup({ skipHover: true });

  const renderComponent = ({
    isUserAdmin = false,
    isGlobalReadOnly = false,
    isLdap = false,
    isSaml = false,
    currentOrgId = '',
  }: Partial<TestProps> = {}) => {
    render(
      <UsersPage
        users={userFixtures}
        fetchGroupUsersAndTeams={jest.fn()}
        fetchFailed={false}
        loading={false}
        numGroupOwners={1}
        onDeleteInvitation={jest.fn()}
        onDeleteUser={jest.fn()}
        onUserRolesChange={jest.fn()}
        centralUrl={centralUrl}
        isUserAdmin={isUserAdmin}
        isGlobalReadOnly={isGlobalReadOnly}
        isLdap={isLdap}
        isSaml={isSaml}
        currentOrgId={currentOrgId}
        loadOrgFederationSettings={jest.fn()}
      />
    );
  };

  const showsTheUserTable = () => {
    it('shows the user table', () => {
      expect(screen.getAllByRole('table')).toHaveLength(1);
    });
    it('shows the default columns', () => {
      const headers = screen.getAllByRole('columnheader');
      expect(headers.length).toBeGreaterThanOrEqual(3);
      expect(headers[0]).toHaveTextContent('Display Name');
      expect(headers[1]).toHaveTextContent('Email Address');
      expect(headers[2]).toHaveTextContent('Project Role');
    });
  };

  const showsAdminColumns = () => {
    it('shows admin columns', () => {
      const headers = screen.getAllByRole('columnheader');
      expect(headers).toHaveLength(6);
      expect(headers[3]).toHaveTextContent('Created');
      expect(headers[4]).toHaveTextContent('Last Login');
      expect(headers[5]).toBeEmptyDOMElement();
    });
  };

  const doesNotShowAdminColumns = () => {
    it('does not show admin columns', () => {
      const headers = screen.getAllByRole('columnheader');
      expect(headers).toHaveLength(3);
    });
  };

  const showsCreatedAndLastLoginColumns = () => {
    it('shows a login column', () => {
      const headers = screen.getAllByRole('columnheader');
      expect(headers).toHaveLength(5);
      expect(headers[3]).toHaveTextContent('Created');
      expect(headers[4]).toHaveTextContent('Last Login');
    });
  };

  const userTypeAndExpectRowsLength = async (userType: string, expectedRowsLength: number) => {
    const input = screen.getByRole('searchbox');
    await user.type(input, userType);
    const rows = getRows();
    expect(rows).toHaveLength(expectedRowsLength);
  };

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('when it renders and user admin', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderComponent();
    });

    showsTheUserTable();

    it('shows the search input', () => {
      expect(screen.getAllByRole('searchbox')).toHaveLength(1);
    });

    it('displays a tip card', () => {
      expect(screen.getByText('Looking to manage access to an organization?'));
    });

    it('does not display a button to navigate to the project access manager', () => {
      expect(screen.queryByRole('link', { name: 'Manage Org Access' })).not.toBeInTheDocument();
    });

    it('shows 2 rows', () => {
      const rows = getRows();
      expect(rows).toHaveLength(2);
    });

    describe('when search input has value', () => {
      it('shows 1 row when search input matches first name of the existing user', () => {
        userTypeAndExpectRowsLength('User', 1);
      });

      it('shows 1 row when search input matches last name of the existing user', () => {
        userTypeAndExpectRowsLength('Two', 1);
      });

      it('shows 1 row when search input matches email of the existing user', () => {
        userTypeAndExpectRowsLength('admin@', 1);
      });

      it('shows 1 row when search input matches username with leading/trailing spaces, tabs and newlines', () => {
        userTypeAndExpectRowsLength('    <EMAIL>    ', 1);
      });

      it('shows 0 rows when search input does not match existing user name', () => {
        userTypeAndExpectRowsLength('unknown@', 0);
      });
    });
  });

  describe('when rendered with isUserAdmin prop is true', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderComponent({ isUserAdmin: true });
    });
    showsTheUserTable();
    showsAdminColumns();
  });

  describe('when rendered with isGlobalReadOnly prop is true', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderComponent({ isGlobalReadOnly: true });
    });
    showsTheUserTable();
    showsCreatedAndLastLoginColumns();
  });

  describe('when rendered with isLdap prop is true', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderComponent({ isLdap: true });
    });
    showsTheUserTable();
    doesNotShowAdminColumns();
  });

  describe('when rendered with isSaml prop is true', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderComponent({ isSaml: true });
    });
    showsTheUserTable();
    doesNotShowAdminColumns();
  });

  it('displays a button to navigate to the project access manager when rendered with currentOrgId', () => {
    renderComponent({ currentOrgId: orgId });
    expect(screen.getByRole('link', { name: 'Manage Org Access' })).toHaveAttribute(
      'href',
      `${centralUrl}/v2#/org/${orgId}/access/users`
    );
  });
});
