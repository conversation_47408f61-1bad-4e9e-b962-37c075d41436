import { useReducer } from 'react';

import Banner from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import Modal from '@leafygreen-ui/modal';
import TextInput from '@leafygreen-ui/text-input';
import { Body, H3 } from '@leafygreen-ui/typography';

import { CloudTeams } from '@packages/types/observability';

import {
  confirmationModalButtonContainerStyle,
  confirmationModalContentStyle,
} from '@packages/authn/components/common/styles';
import fetchWrapper from '@packages/common/services/api/fetchWrapper';
import { sendError } from '@packages/observability';

export interface ClientDeletionModalProps {
  windowLocation?: Pick<Location, 'assign'>;
  open?: boolean;
  setOpen: (open: boolean) => void;
  clientId: string;
}

interface ClientDeletionModalState {
  confirmationInput: string;
  clientDeletionError: string | undefined | null;
  clientDeletionSuccessful: boolean;
}

const initialState: ClientDeletionModalState = {
  confirmationInput: '',
  clientDeletionError: undefined,
  clientDeletionSuccessful: false,
};

interface SetConfirmationInputAction {
  type: 'setConfirmationInput';
  payload: {
    confirmationInput: string;
  };
}

interface SetDeletionSuccessfulAction {
  type: 'setDeletionSuccessful';
}

interface SetDeletionErrorAction {
  type: 'setDeletionError';
  payload: {
    clientDeletionError: string;
  };
}

type ClientDeletionModalAction = SetConfirmationInputAction | SetDeletionSuccessfulAction | SetDeletionErrorAction;

const clientDeletionModalReducer = (
  state: ClientDeletionModalState,
  action: ClientDeletionModalAction
): ClientDeletionModalState => {
  const { type } = action;
  const newState = { ...state };
  switch (type) {
    case 'setConfirmationInput':
      newState.confirmationInput = action.payload.confirmationInput;
      break;
    case 'setDeletionSuccessful':
      newState.clientDeletionError = null;
      newState.clientDeletionSuccessful = true;
      break;
    case 'setDeletionError':
      newState.clientDeletionError = action.payload.clientDeletionError;
      break;
    default:
      break;
  }
  return newState;
};

export const ClientDeletionModal = ({
  windowLocation = window.location,
  open,
  setOpen,
  clientId,
}: ClientDeletionModalProps) => {
  const [state, dispatch] = useReducer(clientDeletionModalReducer, initialState);

  const onSubmit = async () => {
    try {
      await fetchWrapper(`/admin/authn/internalClients/${clientId}`, {
        method: 'DELETE',
      });
      dispatch({
        type: 'setDeletionSuccessful',
      });
      setTimeout(() => {
        windowLocation.assign('/v2/admin#/general/authn');
      }, 3000);
    } catch (error) {
      console.error('Error while submitting client registration', error);
      sendError({ error, team: CloudTeams.CoreIam });
      dispatch({
        type: 'setDeletionError',
        payload: {
          clientDeletionError: `${error.errorCode}: ${error.message}`,
        },
      });
    }
  };

  const isDeleteButtonDisabled = state.confirmationInput !== clientId;

  return (
    <Modal open={open} setOpen={setOpen}>
      <div css={confirmationModalContentStyle}>
        <H3>Confirm deletion of {clientId}</H3>
        <Banner variant="warning">Client deletion is a non-reversible action</Banner>
        <Body>Please type the ID of the client you are attempting to delete below to confirm this deletion.</Body>
        <TextInput
          label="Client ID to be deleted"
          placeholder="mdb_ic_id_xxxxxxxxxxxxxxxxxxxxxxxx"
          value={state.confirmationInput}
          onChange={(e) =>
            dispatch({
              type: 'setConfirmationInput',
              payload: {
                confirmationInput: e.target.value,
              },
            })
          }
        />
        {state.clientDeletionError && <Banner variant="danger">{state.clientDeletionError}</Banner>}
        {state.clientDeletionSuccessful && (
          <Banner variant="success">Client Successfully Deleted! Redirecting you back to the main page...</Banner>
        )}
        <div css={confirmationModalButtonContainerStyle}>
          <Button variant="default" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button variant="danger" type="submit" disabled={isDeleteButtonDisabled} onClick={() => onSubmit()}>
            Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};
