import { AtlasResourcePolicy } from '@packages/types/nds/resourcePolicies/ResourcePolicy';

import * as organization from '@packages/redux/common/organization/index';
import { createReducer } from '@packages/redux/common/reducerUtils';

import resourcePolicyApi from '@packages/common/services/api/nds/resourcePolicyApi';

// actions
const SET_RESOURCE_POLICIES = 'organization/resourcePolicies/SET_RESOURCE_POLICIES';

// reducer
const initialState: { resourcePolicies: Array<AtlasResourcePolicy> | null } = {
  resourcePolicies: null,
};

export default createReducer(initialState, {
  [SET_RESOURCE_POLICIES]: (state, action: { payload: Array<AtlasResourcePolicy> }) => {
    return {
      ...state,
      resourcePolicies: action.payload,
    };
  },
});

// selectors
const getOrgResourcePoliciesData = (state, props: { orgId: string }) => {
  return organization.getOrgData(state, props).resourcePolicies || {};
};

export const getResourcePolicies = (state, props) => getOrgResourcePoliciesData(state, props).resourcePolicies;

// action creators
export const setResourcePolicies = ({ data, orgId }: { data: Array<AtlasResourcePolicy>; orgId: string }) => {
  return {
    type: SET_RESOURCE_POLICIES,
    payload: data,
    meta: { orgId },
  };
};

export const loadResourcePolicies =
  ({ orgId }: { orgId: string }) =>
  (dispatch) => {
    return resourcePolicyApi.getResourcePolicies(orgId).then((response) => {
      dispatch(
        setResourcePolicies({
          data: response,
          orgId,
        })
      );
    });
  };
