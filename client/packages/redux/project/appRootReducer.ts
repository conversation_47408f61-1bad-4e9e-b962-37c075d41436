import { combineSlices } from '@reduxjs/toolkit';
import { Action, ActionFromReducer } from 'redux';
import type { ThunkAction } from 'redux-thunk';

import allClusters from '../common/allClusters';
import app from '../common/app';
import deviceSync from '../common/deviceSync/reducers';
import group from '../common/group';
import { type ReducerPath, registerRootReducer } from '../common/injectReducers';
import invoice from '../common/invoice';
import type { LazyLoadedSlices } from '../common/lazySlices';
import nds from '../common/nds/index';
import processArguments from '../common/processArgumentsSlice';
import purchase from '../common/purchase';
import type { createStore } from '../common/reduxHelpers';
import search from '../common/search/reducer';
import settings from '../common/settings';
import team from '../common/team';
import user from '../common/user';
import viewer from '../common/viewer';
import controlledFeatures from './controlledFeatures';
import deployment from './deployment';
import queryProfiler from './intel/queryProfiler';

const rootReducer = combineSlices({
  allClusters,
  app,
  nds,
  deviceSync,
  deployment,
  group,
  settings,
  viewer,
  controlledFeatures,
  purchase,
  search,
  user,
  invoice,
  team,
  queryProfiler,
  processArguments,
}).withLazyLoadedSlices<LazyLoadedSlices>();

// Register the appRootReducer to be used for injection
const expectedSlices: Set<ReducerPath> = new Set([
  'apiUser',
  'dataAPI',
  'organization',
  'serviceAccount',
  'triggers',
  'uiPreferences',
]);
registerRootReducer('project', rootReducer, expectedSlices);

export default rootReducer;

export type RootState = ReturnType<typeof rootReducer>;
export type RootAction = ActionFromReducer<typeof rootReducer>;
export type RootThunk<ReturnType, A extends Action = RootAction> = ThunkAction<ReturnType, RootState, unknown, A>;
export type RootStore = ReturnType<typeof createStore<RootState, RootAction>>;
export type AppDispatch = RootStore['dispatch'];
