// These top-level matchers enabled the styled buttons across the application

// (internal)
.button-common() {
  text-transform: uppercase;
  line-height: 24px;
}

// primary action button mixin - white text on green background
button.primary {
  .button-common();

  border: 1px solid darken(@green2, 5%);
  color: @pw;
  background: @green2;
  font-weight: bold;

  &:hover:not([disabled]) {
    background-color: darken(@green2, 5%);
    border-color: darken(@green2, 10%);
  }
  &[disabled] {
    opacity: 0.5;
  }
}

// info button mixin - green text
button.info {
  .button-common();

  border: 2px solid @green3;
  color: @green2;
  line-height: 22px; // reduce to counter extra border thickness

  &:hover {
    color: @green1;
    background-color: lighten(@green5, 15%);
    border-color: darken(@green2, 5%);
  }
}

// default button - gray text on white
button.default {
  .button-common();

  color: @gray1;
  border: 1px solid @gray6;

  &:hover {
    background-color: @gray8;
    border-color: @gray5;
  }

  &[disabled] {
    opacity: 0.5;
  }
}

// default button on side panel - near white text on gray
aside button.default {
  color: @gray7;
  background-color: @gray2;
  border-color: @gray3;

  &:hover {
    background-color: @gray3;
    border-color: @gray4;
  }
}

// small button for things like the edit (pencil) icon, black text/icon on white
button.small {
  .button-common();

  background: @pw;
  border: 1px solid @gray6;
  color: @gray1;

  &:hover {
    background: @gray8;
    border-color: @gray5;
  }
}

// styling to
button.btn {
  &.btn-link.btn-is-inline-link {
    padding: 0;
    font-size: inherit;
    font-weight: inherit;
    text-transform: inherit;
    vertical-align: inherit;
  }

  &.btn-is-icon {
    padding: 0;
    background-color: transparent;
    box-shadow: none;
    -webkit-box-shadow: none;

    &:focus,
    &:active,
    &:hover {
      outline: none;
    }
  }
}
