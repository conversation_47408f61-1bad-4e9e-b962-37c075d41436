.diff-modal {
  .modal-dialog {
    width: 520px;
  }
}

#diffModalBody {
  @nesting-margin: 20px;
  @nesting-origin: 76px;
  @icon-margin: 17px;

  padding: 0;

  .nesting {
    border-left: 1px solid @gray6;
    margin-left: @icon-margin;
  }

  .description {
    &-title {
      padding-right: 10px;
    }

    &-body {
      color: @gray4;
      overflow-wrap: anywhere;
    }

    &-label {
      font-weight: bold;
    }
  }

  .safe-add-node {
    &-well {
      border-top: 1px solid @gray6;
      background-color: @gray8;
      margin-left: -17px;
      margin-top: 10px;
      padding: 17px;

      &-intro,
      &-link {
        font-style: italic;
        font-size: 13px;
      }
    }

    &-link {
      font-size: 11px;
    }

    .fa-caret-down,
    .fa-caret-right {
      font-size: 13px;
    }

    .fa-caret-right {
      margin-right: 8px;
    }

    &-step {
      margin-bottom: 7px;
    }
  }

  ul {
    margin-top: 10px;
    margin-bottom: 15px;
    list-style-type: none;
    padding-left: 0;
    display: table;

    li {
      overflow: auto;
      display: table-row;

      > * {
        margin-bottom: 8px;
      }
    }

    li.item-description {
      font-weight: bold;
      font-size: 9px;
      padding: 6px 0 2px 0;
    }

    li.zone-diff {
      display: flex;
      justify-content: space-between;
    }

    span.key {
      min-width: 130px;
      margin-right: 5px;
      display: table-cell;
      font-weight: normal;
      float: left;
      color: @gray4;
    }

    span.shard-name {
      white-space: nowrap;
    }

    span.value,
    span.value-flex {
      display: table-cell;
      word-break: break-word;
      overflow-wrap: break-word;
      text-overflow: initial;
      white-space: normal;
      padding-bottom: 2px;
      height: auto;
      float: right;

      .col-container {
        width: 100%;
        margin-bottom: 10px;

        span {
          &.col-left {
            float: left;
            padding-right: 50px;
            width: 215px;
          }

          &.col {
            display: inline-block;
          }
        }
      }

      span {
        width: 175px;
        display: inline-block;
        font-weight: normal;
        float: left;

        &.old {
          width: 75px;
        }
      }

      .fa-long-arrow-right {
        margin: 0 5px;
        float: left;
      }
    }

    span.value {
      width: 420px;

      &.new {
        padding-left: 80px;
      }

      &.old {
        padding-left: 214px;
      }
    }

    span.new-zone {
      width: 231px;
    }
  }

  .diff {
    list-style-type: none;
    padding: 15px;

    &.no-padding {
      padding: 0;
    }

    i {
      margin-right: 5px;
      font-size: 15px;
    }

    .item-name {
      font-weight: bold;
      word-break: break-all;
      text-overflow: initial;
      white-space: normal;
      padding-left: 4px;
    }

    .diff-title {
      padding: 5px 0 5px 70px;
      position: relative;
      font-style: italic;
      span.label {
        position: absolute;
        left: 0;
        top: 7px;
        height: 15px;
        font-style: normal;

        &.label-restore {
          background-color: #38afe6;
        }
      }
      span.item-name {
        font-style: normal;
        width: 450px;
      }
    }

    .nested-level-1 {
      margin-left: @nesting-origin;
      p,
      .diff-items {
        margin-left: @icon-margin;
      }
      .shard-title {
        margin-left: 10px;
        position: relative;
        font-style: italic;
        padding-top: 10px;

        .item-name {
          font-style: normal;
          font-weight: bold;
        }

        .label {
          float: left;
        }
      }
      .process-title {
        font-weight: bold;
        margin-left: 11px;
        height: 20px;

        > i {
          float: left;
          margin-top: 2px;
          margin-left: -1px;
        }

        .item-name {
          float: left;
          max-width: 600px;
        }
      }
      .process-diff-items {
        margin-left: 130px;
      }
    }

    &:nth-child(even) {
      background-color: @gray8;
    }
  }
}
