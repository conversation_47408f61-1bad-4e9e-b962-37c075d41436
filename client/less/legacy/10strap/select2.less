.theme-value(--mdb-select2-bg-color, @light: var(--mdb-white), @dark: var(--mdb-grayDark4));
.theme-value(--mdb-select2-border-color, @light: var(--mdb-gray), @dark: var(--mdb-gray));
.theme-value(--mdb-select2-focus-box-shadow, @light: var(--mdb-grayLight2), @dark: var(--mdb-grayDark2));

.select2-container--default .select2-selection--multiple.form-control,
.form-control {
  color: @gray0;
  border: 1px solid var(--mdb-select2-border-color);
  background: var(--mdb-select2-bg-color);
  border-radius: 6px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}

.select2-selection.select2-selection--multiple.form-control {
  padding: 0;
  height: initial;
}

input.form-control,
textarea.form-control,
select.form-control,
.select2-container--default.select2-container--open .select2-selection--multiple.form-control {
  &:focus {
    border-color: var(--mdb-select2-border-color);
    outline: 0;
    -webkit-box-shadow: var(--mdb-select2-focus-box-shadow) 0px 0px 0px 3px;
    box-shadow: var(--mdb-select2-focus-box-shadow) 0px 0px 0px 3px;
  }
}
