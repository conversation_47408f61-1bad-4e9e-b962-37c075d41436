"""
Less tools
"""

load("@aspect_rules_js//js:defs.bzl", "js_run_binary")

def less_target(name, srcs):
    dist_dir = "{}_css".format(name)

    js_run_binary(
        name = name,
        tool = "//client/scripts:compile_css",
        srcs = srcs + [
            ":common",
            ":git_version",
            "//:node_modules/@leafygreen-ui/palette",
        ],
        out_dirs = [dist_dir],
        args = [
            "--output",
            "{}/{}".format(native.package_name(), dist_dir),
            "--stable-status-path",
            "../../../$(execpath :git_version)",
            name,
        ],
        mnemonic = "Less",
        execution_requirements = {"no-remote-cache": "1"},
    )
