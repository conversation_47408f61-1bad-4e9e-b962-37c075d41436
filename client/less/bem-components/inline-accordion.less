.inline-accordion {
  background-color: @pw;
  transition: height linear 0.3s;

  &-is-open {
    height: 100%;
  }

  &-is-disabled {
    border-color: #f1f1f1;
    opacity: 0.5;
  }

  &-headline {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 30px;
    color: @gray1;
    margin-bottom: 4px;
    cursor: pointer;
  }

  &-badge {
    font-size: 12px;
    margin-left: 390px;
  }

  &-headline-is-disabled {
    cursor: default;
  }

  &-headline-text {
    font-size: 15px;
    font-weight: 700;
    margin-left: 10px;
    text-transform: uppercase;
    color: @gray4;

    &-is-compact {
      font-weight: 400;
      text-transform: capitalize;
      color: @gray1;
    }

    &-is-bolded {
      text-transform: none;
      color: @gray1;
      margin-left: 0px;
      line-height: 1.75rem;
    }
  }

  &-headline-arrow {
    color: @gray5;
    vertical-align: middle;
    transition: all linear 0.2s;
    transform: rotate(0deg);
    margin-right: 10px;
  }

  &-headline-arrow-is-down {
    transform: rotate(90deg);
  }
}
