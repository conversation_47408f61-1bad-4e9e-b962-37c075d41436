// Empty view for the Deployment page.

@column-width: 230px;
@divider-height: 160px;

.empty-setup-prompt {
  margin: 25px;
  text-align: left;

  &-header {
    font-size: 19px;
  }

  &-column {
    line-height: 21px;
    vertical-align: top;
    display: inline-block;
    width: @column-width;
    margin: 15px 0 70px;
  }

  &-caption {
    font-weight: bold;
  }

  &-description {
    margin: 13px 0;
  }

  &-title {
    font-size: 15px;
    font-weight: medium;
    text-transform: uppercase;
  }

  &-column-link {
    display: block;
    color: @green2;

    &:hover,
    &:active,
    &:focus {
      color: @green2;
      text-decoration: none;
    }

    &-is-disabled,
    &-is-disabled:hover,
    &-is-disabled:active,
    &-is-disabled:focus {
      cursor: not-allowed;
      color: @gray4;
    }
  }

  &-divider {
    position: relative;
    display: inline-block;
    width: 1px;
    margin: 0 75px;
    height: @divider-height;
    border-right: 1px solid @gray6;
    color: @gray5;
  }

  &-or-text {
    position: absolute;
    top: @divider-height / 2 - 10px;
    left: -8px;
    font-size: 11px;
    font-weight: bold;
    background-color: @gray8; // same background color as a bootstrap well
  }

  &-center-image {
    width: 842px;
    height: 518px;
  }

  &-center-text {
    font-size: 15px;
    width: 620px;
    padding-top: 15px;
    padding-bottom: 10px;
  }

  &-sub-text {
    width: 620px;
    font-size: 12px;
    color: @gray4;
  }

  &-divider-title {
    width: 620px;
    font-size: 21px;
    color: @green2;
    padding-bottom: 35px;
  }
}
