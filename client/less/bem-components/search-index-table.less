@import (reference) '../mixins/table.less';

.search-index-table {
  &-header {
    &-cell {
      padding-left: 8px;
    }
  }

  &-cell {
    .table-cell-mixin();
    vertical-align: text-top;
    max-height: none;
    min-height: 64px;
    flex-direction: column;
    justify-content: center;
    align-items: baseline;
    display: flex;

    &-container {
      overflow: visible;
      padding-left: 8px;

      div:first-child {
        max-height: none;
      }
    }

    &-medium {
      min-width: 125px;
    }

    &-large {
      min-width: 180px;
    }

    &-actions {
      flex-direction: row;
      overflow: visible;
      align-items: center;
    }

    &-status-container {
      margin-top: 5px;

      .data-explorer-fts {
        &-status-not-started {
          background-color: @palette__gray--light-3;
          border-color: @palette__gray--light-2;
          color: @palette__gray--dark-2;
        }

        &-status-content {
          min-width: 0px;
        }

        &-node-message {
          min-width: 100px;
        }
      }
    }
  }
}
