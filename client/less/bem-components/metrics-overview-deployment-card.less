.metrics-overview-deployment-card {
  &-header-row {
    padding: 18px 0px 2px 5px;
    display: flex;
    justify-content: space-between;
  }

  &-titles {
    display: flex;
    align-items: center;
  }

  &-hostname {
    display: inline-block;
    margin-right: 26px;
    font-size: 19px;
    font-weight: bold;
  }

  &-status-icon {
    display: inline-block;
    margin-right: 5px;
    font-size: 8px;
  }

  &-type-icon {
    display: inline-block;
    margin-right: 3px;
    font-size: 11px;
    color: @gray4;
  }

  &-type-string {
    display: inline-block;
    margin-right: 26px;
    font-size: 13px;
    font-weight: bold;
    color: @gray4;
  }

  &-version {
    display: inline-block;
    font-size: 13px;
    color: @gray1;
  }

  &-buttons {
    display: inline-block;
  }

  &-button {
    margin-right: 4px;
  }

  &-body {
    display: flex;
    align-items: flex-start;
  }

  &-processes {
    display: table;
    flex: 1 1 auto;
    border-spacing: 0 10px;
    min-width: 400px;
    max-width: 800px;
  }

  &-charts {
    margin: 10px 0 0 20px;
    flex: 3 1 40%;
  }
}
