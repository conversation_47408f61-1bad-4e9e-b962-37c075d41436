@metric-height: 3em;
@metric-after-height: @metric-height / 2;
@value-border-radius: 1.2 * @metric-height;

.theme-value(--mdb-metric-dial-empty-color, @light: @gray2, @dark: var(--mdb-grayDark3));
.theme-value(--mdb-metric-dial-filled-color, @light: @chart8, @dark: var(--mdb-greenDark2));
.theme-value(--mdb-metric-dial-color, @light: @gray4, @dark: var(--mdb-grayLight2));

.metric-dial {
  display: inline-block;
  position: relative;
  width: 2.5 * @metric-height;
  height: @metric-height;
  overflow: hidden;
  margin: 0 0 0 @metric-after-height;

  &:before,
  &:after {
    display: block;
    position: absolute;
    content: '';
  }

  &:before {
    width: 2 * @metric-height;
    height: @metric-height;
    border-radius: @metric-height @metric-height 0 0;
    border-color: var(--mdb-metric-dial-empty-color);
    border-style: solid;
    border-width: 10px 10px 0 10px;
    background: var(--mdb-real-time-bg-color);
  }

  &:after {
    position: absolute;
    bottom: 0;
    left: @metric-after-height / 2;
    width: @metric-height * 1.5;
    height: @metric-after-height * 1.5;
    background: var(--mdb-real-time-bg-color);
    border-radius: @metric-after-height * 1.5 @metric-after-height * 1.5 0 0;
  }

  &-container {
    display: flex;
    text-align: left;
  }

  &-title {
    text-transform: uppercase;
    font-weight: bold;
    color: var(--mdb-metric-dial-color);
    font-size: 13px;
    min-width: 40px;
  }

  &-side {
    width: 80px;
    &-is-min-max {
      width: 120px;
    }
  }

  &-value {
    color: @pw;
    font-size: 19px;
    display: flex;
  }

  &-unit {
    color: var(--mdb-metric-dial-color);
    font-size: 15px;
    line-height: 30px;
    display: inline-block;
    margin-left: 10px;
    white-space: nowrap;
  }

  // manage value by inline rotating between 0 and 180deg
  &-bar {
    position: absolute;
    display: block;
    width: 2 * @metric-height;
    height: @metric-height;
    top: 100%;
    transform-origin: center top;
    border-radius: 0 0 @value-border-radius @value-border-radius;
    border-color: var(--mdb-metric-dial-filled-color);
    border-style: solid;
    border-width: 0 10px 10px 10px;
    background: var(--mdb-real-time-bg-color);
    -webkit-backface-visibility: hidden;

    &-is-animated {
      transition: 0.5s;
    }
  }
}
