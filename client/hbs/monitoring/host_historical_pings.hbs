{{> breadcrumbs href0="#/deployment" text0="Deployment"}}

<div class="js-page-header"></div>

{{createBootstrapDropdownSelect 'pingId' lastPingId lastPingTime pings 'dropdown'}}

<hr />

{{#if canViewPings}}
    <div id="pingViewer" style="position: relative; display: none"></div>
{{else}}
    <div class="empty-view empty-view-has-no-border">
        <div class="empty-view-graphic">
            <i class="fa fa-exclamation-triangle empty-view-icon"></i>
            <svg class="empty-view-shadow empty-view-shadow-is-error">
                <ellipse cx="50%" cy="50%" rx="50%" ry="50%"></ellipse>
            </svg>
        </div>
        <div class="empty-view-text empty-view-text-is-dark">
            This feature requires Monitoring Admin level access.
        </div>
    </div>
{{/if}}
