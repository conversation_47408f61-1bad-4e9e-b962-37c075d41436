<td>
  {{!-- checkbox --}}
</td>
<td>
  <i class="fa {{icon}}"></i>
  {{> activity.admin_only_icon}}
</td>
<td class="event-description">
  {{description}}
    <br/>
    {{#if hp}}
      {{#unless targetLink}}
        {{> activityFeed.normalIcon}}
        {{hp}}
        <br />
      {{/unless}}
    {{/if}}
    {{#if ipAddress}}
      New IP: {{ipAddress}}
      <br/>
    {{/if}}
    {{#if database}}
        <div class="event-cell">Database: {{database}}</div>
    {{/if}}
    {{#if collection}}
        <div class="event-cell">Collection: {{collection}}</div>
    {{/if}}
    {{#if targetLink}}
      {{> activityFeed.normalIcon}}
      <a href="{{targetLink}}">{{targetName}}</a>
      <br/>
    {{/if}}
    {{> audit_user_info}}
</td>
<td>
  <div>
    {{formatDateTimeWithSeconds cre}}
  </div>
  {{#if hasGlobalReadOnly}}
      {{#if sourceDescription}}
        <div class="event-source-description">
          <strong>triggered via</strong> {{sourceDescription}}
        </div>
      {{/if}}
    {{/if}}
</td>
<td>
  {{!-- for when alerts are added to all activity --}}
</td>
<td>
  {{!-- ack button --}}
</td>
