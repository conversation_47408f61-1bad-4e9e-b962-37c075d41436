#!/usr/bin/env bash

set -euo pipefail

page=1
if [ "$#" -eq 1 ]; then
  page="$1"
fi

project_file=".evergreen.yml"

curl_args=(--silent --show-error --fail)
if [ "${GITHUB_USERNAME:-}" != "" ]; then
  curl_args+=(--user "${GITHUB_USERNAME}")
fi

if versions="$(curl "${curl_args[@]}" "https://api.github.com/repos/webpack-contrib/terser-webpack-plugin/tags?page=$page" | jq --raw-output ".[].name" | cut -c 2-)"; then
  for version in $versions; do
    git checkout "$project_file"
    sed -e "s/TERSER_WEBPACK_VERSION/$version/" -i "" "$project_file"
    evergreen patch --uncommitted --project mms --variants code_health --tasks COMPILE_CLIENT_BAZEL \
      --finalize --description "terser version $version" --yes
  done
fi

git checkout "$project_file"
