import DataAPIPage from 'cypress/pages/dataAPI/DataAPIPage';
import TestAPIModal from 'cypress/pages/dataAPI/TestAPIModal';
import DataAPIUsersPage from 'cypress/pages/dataAPI/UsersPage';

const apiKeyName = 'someAPIKey';
const email = '<EMAIL>';

export function UsersTest() {
  it('should display a created API key in the Users table', () => {
    cy.intercept('POST', '/api/admin/v3.0/groups/*/apps/*/api_keys').as('createAPIKey');
    cy.intercept('POST', '/api/admin/v3.0/groups/*/apps/*/services/*/commands/list_namespaces').as('listNamespaces');

    DataAPIPage.testAPIButton().click();

    TestAPIModal.apiKeyNameInput().type(apiKeyName);
    TestAPIModal.apiKeyGenerateButton().should('be.enabled').click();

    cy.wait('@createAPIKey').its('response.statusCode').should('eq', 201);
    cy.wait('@listNamespaces').its('response.statusCode').should('eq', 200);

    TestAPIModal.testAPISnippetsSection().should('be.visible');

    TestAPIModal.closeButton().click();
    TestAPIModal.modal().should('not.exist');

    DataAPIPage.usersTab().click();
    DataAPIUsersPage.userRow().should('have.length', 1);
    DataAPIUsersPage.userRow().should('contain.text', apiKeyName);
  });

  it('should display a created Email/Password user in the Users table', () => {
    cy.intercept('POST', '/api/admin/v3.0/groups/*/apps/*/users').as('createUser');

    DataAPIPage.testAPIButton().click();

    TestAPIModal.webBrowserTab().click();

    TestAPIModal.emailInput().type(email);
    TestAPIModal.autogenPasswordButton().click();
    TestAPIModal.createUserButton().should('be.enabled').click();

    cy.wait('@createUser').its('response.statusCode').should('eq', 201);

    TestAPIModal.autogenPasswordButton().should('not.exist');
    TestAPIModal.createUserButton().should('not.exist');
    TestAPIModal.passwordTooltipButton().scrollIntoView().should('be.visible').click();

    TestAPIModal.closeButton().scrollIntoView().click();
    TestAPIModal.modal().should('not.exist');

    cy.reload();
    DataAPIPage.usersTab().click();
    DataAPIUsersPage.userRow().should('have.length', 2);
    DataAPIUsersPage.userRow().should('contain.text', email);
  });
}
