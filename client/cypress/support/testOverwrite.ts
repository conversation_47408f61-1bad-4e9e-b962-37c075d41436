import { v4 as uuidv4 } from 'uuid';

export type ExperimentFeatureMap = {
  [key in string]: boolean;
};

declare global {
  namespace Cypress {
    interface Chainable {
      abTestAssignments({ assignments, experimentFeatureFlags, alias }: AbTestAssignmentsProps): void;
    }
  }
}

interface TestAssignmentProps {
  testName: string;
  testGroupId: string;
}

interface AbTestAssignmentsProps {
  assignments: Array<TestAssignmentProps>;
  experimentFeatureFlags?: ExperimentFeatureMap;
  alias: string;
}

const generateMockAssignment = ({
  testName,
  testGroupId,
  orgId,
}: {
  testName: string;
  testGroupId: string;
  orgId: string;
}) => {
  return {
    assignmentDate: new Date(),
    entityId: orgId,
    entityType: 'ORG',
    id: uuidv4(),
    tag: 'activation',
    testGroupDatabaseId: uuidv4(),
    testId: uuidv4(),
    testName,
    meta: null,
    testGroupId,
  };
};

Cypress.Commands.add('abTestAssignments', ({ assignments, experimentFeatureFlags, alias }: AbTestAssignmentsProps) => {
  // org param call
  cy.intercept(
    {
      method: 'GET',
      path: '/v2/params/org/*',
    },
    ({ reply }) => {
      reply(({ body }) => {
        body.abTestAssignments = assignments.map((assignment) => {
          return generateMockAssignment({
            testName: assignment.testName,
            testGroupId: assignment.testGroupId,
            orgId: body.appUser.currentOrgId,
          });
        });

        body.experimentFeatureFlags = experimentFeatureFlags;
      });
    }
  ).as(alias);
});
