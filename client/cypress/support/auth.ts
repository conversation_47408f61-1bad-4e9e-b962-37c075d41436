import LoginPage from 'cypress/pages/auth/LoginPage';
import RegistrationPage from 'cypress/pages/auth/RegistrationPage';
import { AppUser, generateSegmentViewer, generateViewer, isProdTest } from 'cypress/support/utils';

import { GroupParams } from '@packages/types/GroupParams';

interface LoginViaUIOptions {
  shouldFail?: boolean;
  failureStatusCode?: number;
}

// TypeScript interface (for helpful autocomplete in editors)
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * This method returns an AppUser's username and password
       * following 3 stages of deriving that information:
       *  1. Passed in via environment variables:
       *    `pnpm cypress:open --env username=<EMAIL>,password=<pass>`
       *  2. Checking if the `persisted-user.json` file exists and using the values from there
       *  3. Generating a new user
       */
      ensureUser: (shouldAlwaysCreateNewUser: boolean) => Chainable<{
        state: 'cli' | 'persisted' | 'new';
        viewer: AppUser;
      }>;
      ensureSegmentUser: () => Chainable<AppUser>;
      addGlobalRolesToCurrentUser: (roles: Array<GlobalRole>) => void;
      removeGlobalRolesFromCurrentUser: (roles: Array<GlobalRole>) => void;
      logout: () => Chainable<void>;
      clearMmsCookies: () => Chainable<void>;
      clearPaypalCookies: () => Chainable<void>;
      registerUser: (viewer: AppUser) => Chainable<void>;
      registerUserViaUI: (viewer: AppUser) => Chainable<void>;
      registerSegmentUserViaUI: () => Chainable<void>;
      registerDeletedUser: (user?: AppUser) => Chainable<void>;
      registerDeletedUserWithCompany: (user?: AppUser) => Chainable<void>;
      /**
       * Login a user using the internal test utils endpoint
       */
      loginViaAPI: (AppUser, RequestOptions?) => Chainable<AppUser>;
      loginViaUI: (
        isDeletedUser: boolean,
        shouldBypassMfa?: boolean,
        user?: AppUser,
        options?: LoginViaUIOptions
      ) => Chainable<AppUser>;
      registerAndLoginNewUserViaAPI: (shouldAlwaysCreateNewUser?: boolean) => Chainable<AppUser>;
      registerAndLoginNewUserViaUI: (shouldAlwaysCreateNewUser?: boolean, shouldBypassMfa?: boolean) => void;
      registerAndLoginSegmentUserViaUI: () => Chainable<AppUser>;
      addVirtualAuthenticator: () => Chainable<string>;
      loginGlobalOwner: (username: string) => Chainable<void>;
    }
    interface ApplicationWindow {
      PARAMS: GroupParams;
    }
  }
}

const paypalUrl = 'https://www.sandbox.paypal.com/';
export const paypalUser: PaypalUser = {
  email: Cypress.env('paypalEmail'),
  password: Cypress.env('paypalPassword'),
};

// This site does not need to be in secrets manager since it does not allow a user any access.
// it is restricted to localhost and will always result in a failed reCAPTCHA assessment.
const reCaptchaFailureTestSiteKey = '6LfWQZoqAAAAAH3JVjFZqoQ9BQr84jczqAVHcyPi';

export const mockEnterpriseReCaptchaRiskyKeyOnLoad = () => {
  // we need to reload recaptcha with the key we want to use otherwise we get a key mismatch error
  // because of using a different key to render recaptcha vs making the token response call
  // see https://github.com/10gen/mms/blob/72f4e1612b2298f8345804a01cc627f1b44c3714/server/src/webapp-mms/WEB-INF/template/home/<USER>
  return Cypress.on('window:load', (win) => {
    if ((win as any).REQUEST_PARAMS) {
      (win as any).REQUEST_PARAMS.reCaptchaKeyId = reCaptchaFailureTestSiteKey;
    }
    cy.get('head').then((head) => {
      const script = document.createElement('script');
      head.append(script);
      const promise = new Cypress.Promise((resolve) => {
        script.onload = resolve;
      });
      script.src = `https://www.recaptcha.net/recaptcha/enterprise.js?onload=reCaptchaOnload&render=${reCaptchaFailureTestSiteKey}`;
      return promise;
    });
  });
};

// While developing an E2E test you may want to re-use an existing user you created
// So that you don't create a new user on every test run.
// If you start cypress via:
//    pnpm cypress:open --env persist-user=true
// Then this code will run and re-use the user from the persistedUserJson file.
const persistUser = Cypress.env('persist-user');
const persistedUserJson = 'cypress/persisted-user.json';

type GlobalRole = 'GLOBAL_BILLING_ADMIN' | 'GLOBAL_OWNER' | 'GLOBAL_ATLAS_ADMIN';

interface RequestOptions {
  retryAttempts?: number;
}

interface CsrfInfo {
  token: string;
  time: number;
}

let csrfInfo: CsrfInfo = { token: '', time: 0 };

let localUser: AppUser;

Cypress.Commands.add('ensureUser', (shouldAlwaysCreateNewUser) => {
  const envUsername = Cypress.env('username');
  const envPassword = Cypress.env('password');

  if (shouldAlwaysCreateNewUser) {
    const viewer = generateViewer();
    localUser = viewer;
    cy.writeFile(persistedUserJson, viewer).then(() => {
      cy.log('ensureUser state: new');
      cy.wrap({ state: 'new', viewer });
    });
    return;
  }

  if (envUsername || envPassword) {
    cy.log('ensureUser state: cli');
    cy.wrap({
      state: 'cli',
      viewer: {
        username: envUsername.toLowerCase(),
        password: envPassword,
        firstname: '',
        lastname: '',
      },
    });
    return;
  }

  cy.log(`persistUser: ${persistUser}`);

  if (!persistUser) {
    cy.log('ensureUser state: new');
    cy.wrap({
      state: 'new',
      viewer: generateViewer(),
    });
    return;
  }

  cy.task<string>('readFileMaybe', persistedUserJson).then((persistedUser?: string) => {
    if (persistedUser) {
      const viewer = JSON.parse(persistedUser);
      if (viewer.username && viewer.password) {
        cy.log('ensureUser state: persisted');
        cy.wrap({ state: 'persisted', viewer });
        localUser = {
          username: viewer.username,
          password: viewer.password,
          firstname: viewer.firstName,
          lastname: viewer.lastName,
        };
      }
    } else {
      const viewer = generateViewer();
      cy.writeFile(persistedUserJson, viewer).then(() => {
        cy.log('ensureUser state: new');
        cy.wrap({ state: 'new', viewer });
      });
    }
  });
});

Cypress.Commands.add('ensureSegmentUser', () => {
  const viewer = generateSegmentViewer();
  localUser = viewer;
  cy.writeFile(persistedUserJson, viewer).then(() => {
    return viewer;
  });
});

Cypress.Commands.add('logout', () => {
  // Cypress hangs onto cookies by default with Cypress 12 and testIsolation off
  // On logout we should clear these
  cy.clearCookies();
  cy.clearLocalStorage();
  cy.visit('/account/login?signedOut=true');
  cy.url().should('include', '/account');
  cy.get('form').should('exist');
});

Cypress.Commands.add('clearMmsCookies', () => {
  global.MMS.cookies.forEach((cookie) => cy.clearCookie(cookie));
});

Cypress.Commands.add('clearPaypalCookies', () => {
  cy.visit(paypalUrl);
  cy.clearAllCookies();
});

Cypress.Commands.add('registerDeletedUser', (user = localUser) => {
  cy.visit('/account/register?signedOut=true');
  cy.get('input[name="emailAddress"]').type(user.username);
  cy.get('input[name="firstName"]').type('Skip');
  cy.get('input[name="lastName"]').type('OFAC');
  cy.get('input[name="password"]').type(user.password);
  cy.get('input[type="checkbox"] + div').click();
  cy.get('button[type="submit"]').should('have.attr', 'aria-disabled', 'false').click();
});

Cypress.Commands.add('registerDeletedUserWithCompany', (user = localUser) => {
  cy.visit('/account/register?signedOut=true');
  cy.get('input[name="emailAddress"]').type(user.username);
  cy.get('input[name="firstName"]').type('Skip');
  cy.get('input[name="lastName"]').type('OFAC');
  cy.get('input[name="password"]').type(user.password);
  cy.get('input[name="company"]').type('MyCompany');
  cy.get('input[type="checkbox"] + div').click();
  cy.get('button[type="submit"]').should('have.attr', 'aria-disabled', 'false').click();
});

Cypress.Commands.add('registerUser', ({ username, password }: AppUser) => {
  cy.visit('/account');

  const userData = {
    username,
    password,
    firstName: 'Skip',
    lastName: 'OFAC',
    newGroup: true,
  };

  localUser = { username, password, firstname: userData.firstName, lastname: userData.lastName };

  // This command relies on a few internal test endpoints
  // which are only available in non-prod environments
  if (!isProdTest()) {
    cy.request({
      method: 'POST',
      url: '/test/utils/user/registerCall',
      body: userData,
      // We've seen registration calls that hit Okta take up to 2 minutes
      timeout: 120000,
    }).then((resp) => {
      expect(resp.status).to.equal(200);
      expect(resp.body.errorCode).to.equal('NONE');
    });

    // Bypass the email verification flow because that flow is tested with Selenium E2E tests
    cy.request({
      method: 'POST',
      url: '/test/utils/user/bypassEmailVerification',
      form: true,
      body: { username },
    }).then((resp) => {
      expect(resp.status).to.equal(200);
      expect(resp.body.errorCode).to.equal('NONE');
    });
  } else {
    cy.log('Skipping cy.registerUser() when test is against prod environment.');
  }
});

Cypress.Commands.add('registerUserViaUI', (viewer: AppUser) => {
  cy.logout();
  RegistrationPage.visit();
  RegistrationPage.register(viewer.username, viewer.password, viewer.firstname, viewer.lastname);
});

Cypress.Commands.add('registerSegmentUserViaUI', () => {
  cy.ensureSegmentUser().then((viewer) => {
    cy.registerUserViaUI(viewer);
  });
});

Cypress.Commands.add('loginViaAPI', (appUser: AppUser, loginOptions?: RequestOptions) => {
  // This command relies on a few internal test endpoints
  // which are only available in non-prod environments
  if (!isProdTest()) {
    cy.intercept('POST', 'https://api.segment.io/v1/p', { success: true });
    cy.intercept('GET', 'https://auth-qa.mongodb.com/api/v1/sessions/me', {});

    // Bypass the Encourage MFA flow because that flow is tested separately
    cy.bypassMfaEncouragementFlow(appUser.username);

    cy.visit('/account');

    let requestAttempts = 0;
    const req = () => {
      return cy
        .request('POST', '/test/utils/user/v1/auth', {
          username: appUser.username,
          password: appUser.password,
        })
        .then((resp) => {
          if (resp.status !== 200 || resp.body.errorCode !== 'NONE') {
            if (loginOptions?.retryAttempts && loginOptions.retryAttempts > requestAttempts) {
              requestAttempts++;
              cy.wait(1000); // wait just to spread out the requests
              return req();
            }
          }

          expect(resp.status).to.equal(200);
          expect(resp.body.errorCode).to.equal('NONE');

          csrfInfo = {
            token: resp.body.csrfToken,
            time: resp.body.csrfTime,
          };

          return appUser;
        })
        .as('viewer');
    };

    return req();
  } else {
    cy.log('Skipping cy.loginViaAPI() when test is against prod environment.');
  }
});

Cypress.Commands.add(
  'loginViaUI',
  (isDeletedUser, shouldBypassMfaEncouragement = true, user = localUser, options?: LoginViaUIOptions) => {
    // Bypass the Encourage MFA flow because that flow is tested separately
    if (shouldBypassMfaEncouragement) {
      cy.bypassMfaEncouragementFlow(user.username);
    }

    cy.visit('/account/login?signedOut=true');
    cy.get('input[name="username"]').type(user.username);
    cy.get('button[name="login"]').should('have.attr', 'aria-disabled', 'false').should('contain', 'Next').click();
    cy.get('input[name="password"]').type(user.password);
    cy.intercept('/account/auth').as('authRoute');
    cy.get('button[name="login"]').should('have.attr', 'aria-disabled', 'false').should('contain', 'Login').click();

    cy.wait('@authRoute')
      .then((interception) => {
        if (isDeletedUser) {
          assert.isNotNull(interception.response);
          expect(interception.response?.statusCode).to.equal(400);
          expect(interception.response?.body).to.have.property('errorCode', 'CANNOT_REGISTER_ACCOUNT_BEING_DELETED');
        } else if (options?.shouldFail) {
          assert.isNotNull(interception.response);
          if (options.failureStatusCode) {
            expect(interception.response?.statusCode).to.equal(options.failureStatusCode);
          }
        } else {
          assert.isNotNull(interception.response);
          expect(interception.response?.statusCode).to.equal(200);
        }

        cy.get('meta[name=csrf-token]').then((csrfElement) => {
          csrfInfo.token = csrfElement.attr('content') || '';

          cy.get('meta[name=csrf-time]').then((timeElement) => {
            csrfInfo.time = parseInt(timeElement.attr('content') || '0', 10);

            return user;
          });
        });
      })
      .wait(1000) // arbitrary wait time since there are time out issues with this call
      .as('viewer');
  }
);

Cypress.Commands.add('registerAndLoginNewUserViaAPI', (shouldAlwaysCreateNewUser = false) => {
  cy.logout()
    .ensureUser(shouldAlwaysCreateNewUser)
    .then(({ state, viewer }) => {
      if (state === 'new') {
        cy.registerUser(viewer);
      }

      return cy.loginViaAPI(viewer);
    });
});

Cypress.Commands.add('registerAndLoginNewUserViaUI', (shouldAlwaysCreateNewUser = false, shouldBypassMfa = true) => {
  cy.logout()
    .ensureUser(shouldAlwaysCreateNewUser)
    .then(({ state, viewer }) => {
      let params;
      if (state === 'new') {
        cy.registerUser(viewer);
      }

      cy.intercept({
        pathname: '/nds/clusters/*',
      }).as('clusters');

      cy.loginViaUI(false, shouldBypassMfa);

      cy.window().then((win) => {
        params = win.PARAMS;
      });
      // first org and project are auto-created on login
      // now try to set the org and project ids for Cypress to use in other steps
      // we should be on the Project "#/clusters" page if everything went right, which has a call to fetch clusters
      cy.wait('@clusters').then(() => {
        global.MMS.lastCreatedProjectId = params.currentGroup.id;
        global.MMS.lastCreatedOrgId = params.currentOrganization.id;
        csrfInfo = {
          token: params.csrfToken,
          time: params.csrfTime,
        };
      });
    });
});

Cypress.Commands.add('registerAndLoginSegmentUserViaUI', () => {
  cy.ensureSegmentUser().then((viewer) => {
    // Email verification is disabled for auth critical analytics tests, so after registration,
    // the user logs in automatically.
    // We need to log out after registration to simulate login through the UI.
    cy.registerUserViaUI(viewer).logout();
    LoginPage.login(viewer.username, viewer.password);
    cy.wrap(viewer).as('viewer');
  });
});

// For internal API calls of certain methods, we need to include CSRF headers
// @ts-expect-error Signature for function is wrong
Cypress.Commands.overwrite('request', (originalFn, methodOrOptions, url, body) => {
  const isOptionsStyle = typeof methodOrOptions === 'object';

  const actualUrl: string = isOptionsStyle ? methodOrOptions.url! : url;
  const actualMethod: string = isOptionsStyle ? methodOrOptions.method! : methodOrOptions;

  const isAffectedMethod = ['POST', 'PUT', 'PATCH', 'DELETE'].includes(actualMethod);

  // we assume that any non-fully-qualified url's are internal API calls
  const isExternalRequest = actualUrl.startsWith('http://') || actualUrl.startsWith('https://');

  if (isExternalRequest || !isAffectedMethod) {
    // @ts-expect-error Expected 1 arg but got 3
    return originalFn(methodOrOptions, url, body);
  }

  const csrfHeaders = {
    'x-csrf-token': csrfInfo.token,
    'x-csrf-time': csrfInfo.time?.toString(),
  };

  if (isOptionsStyle) {
    const options = {
      ...methodOrOptions,
      headers: { ...(methodOrOptions.headers || {}), ...csrfHeaders },
    };
    return originalFn(options);
  }

  return originalFn({
    method: methodOrOptions,
    body,
    url,
    headers: csrfHeaders,
  });
});

Cypress.Commands.add('addGlobalRolesToCurrentUser', (roles: Array<GlobalRole>) => {
  cy.request('POST', '/test/utils/addGlobalRolesToCurrentUser', roles).then((resp) => {
    expect(resp.status).to.equal(200);
  });
});

Cypress.Commands.add('removeGlobalRolesFromCurrentUser', (roles: Array<GlobalRole>) => {
  cy.request('POST', '/test/utils/removeGlobalRolesFromCurrentUser', roles).then((resp) => {
    expect(resp.status).to.equal(200);
  });
});

// This command adds a virtual authenticator for WebAuthn tests
// see https://chromedevtools.github.io/devtools-protocol/tot/WebAuthn/
Cypress.Commands.add('addVirtualAuthenticator', () => {
  Cypress.automation('remote:debugger:protocol', {
    command: 'WebAuthn.enable',
    params: {},
  }).then(async (_) => {
    Cypress.automation('remote:debugger:protocol', {
      command: 'WebAuthn.addVirtualAuthenticator',
      params: {
        options: {
          protocol: 'ctap2',
          transport: 'internal',
          hasResidentKey: true,
          hasUserVerification: true,
          isUserVerified: true,
        },
      },
    }).then((result) => {
      return result.authenticatorId;
    });
  });
});

// The registers and caches an admin user with GLOBAL_OWNER role
// Calling cy.loginGlobalOwner('admin') will use the cached session for the user 'admin'
// We could have many admin sessions if needed (keyed by username), but likely sharing is caring
Cypress.Commands.add('loginGlobalOwner', (username: string) => {
  cy.session(
    username, // each username has a unique session
    () => {
      cy.registerAndLoginNewUserViaAPI(true).addGlobalRolesToCurrentUser(['GLOBAL_OWNER']);
    },
    {
      // validate() {}, - handled by the registerAndLoginNewUserViaAPI command
      //allows admin user to be used across multiple tests instead of creating a new one each time
      cacheAcrossSpecs: true,
    }
  );
});

// adding a no-op export to let TS know this file is a module so that it allows
// us to augment the 'global' scope
export {};
