import ClusterStarterTemplates from '../../pages/starterTemplates/ClusterStarterTemplates';

describe('AI Cluster Assistant', () => {
  before(() => {
    // fetch experiment configs (should be server/conf/experiments-e2e-ai-cluster-assistant.json experiment config file)
    cy.request('POST', '/test/utils/processExperimentUpdates').then((resp) => {
      expect(resp.status).to.equal(200);
    });
    cy.clearMmsCookies();
    cy.registerAndLoginNewUserViaAPI(true);
    cy.createOrg().createProject();
  });

  after(() => {
    cy.deleteProject();
    cy.deleteOrg();
  });

  beforeEach(() => {
    cy.recordMockServerRequests();
  });

  afterEach(() => {
    cy.clearMockServerRequests();
  });

  it('renders the AI Cluster Assistant panel', () => {
    cy.projectNavigate('clusters/starterTemplates');
    cy.viewport(1400, 1000);

    ClusterStarterTemplates.clickAdvancedConfigurationOptions();
    cy.contains('Welcome to the Cluster Assistant', { timeout: 20_000 }).should('be.visible');
  });
});
