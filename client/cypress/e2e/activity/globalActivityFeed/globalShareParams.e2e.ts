import { GlobalRole } from '@packages/types/roles';

import { mockEventTypes, navigateGlobalActivityFeed, publishEventFixtures } from '../helpers';

describe.skip('Global Activity Feed filters', { testIsolation: false }, () => {
  before(() => {
    cy.clearMmsCookies();
    cy.registerAndLoginNewUserViaAPI().addGlobalRolesToCurrentUser([
      GlobalRole.GLOBAL_OWNER,
      GlobalRole.GLOBAL_ATLAS_ADMIN,
    ]);
  });

  before(() => {
    cy.viewport(1400, 1000);
    for (let i = 0; i < 11; i++) {
      mockEventTypes.forEach((eventType) => {
        cy.wrap(publishEventFixtures(eventType));
      });
    }
  });

  beforeEach(() => {
    navigateGlobalActivityFeed();
  });

  describe('when base params values are initialized or changed', () => {
    it('should reflect in the URL', () => {
      cy.reload();
      cy.intercept('POST', `/cloud/activity/admin/auditData`, (req) => {
        if (req.body.nextPageToken != null) {
          req.alias = 'getEventsWithNext';
        } else if (req.body.previousPageToken != null) {
          req.alias = 'getEventsWithPrev';
        } else {
          req.alias = 'getEvents';
        }
      }).as('getEvents');

      cy.wait('@getEvents', { timeout: 10000 }).then((interceptedReq) => {
        cy.url().should('include', `start=${encodeURIComponent(interceptedReq.request.body.startTimestamp)}`);
        cy.url().should('include', `end=${encodeURIComponent(interceptedReq.request.body.endTimestamp)}`);
      });

      cy.get('button[aria-label="Next page"]').click();
      cy.wait('@getEventsWithNext', { timeout: 10000 }).then((interceptedReq) => {
        cy.url().should('include', `next=${encodeURIComponent(interceptedReq.request.body.nextPageToken)}`);
      });

      cy.get('button[aria-label="Previous page"]').click();
      cy.wait('@getEventsWithPrev', { timeout: 12000 }).then((interceptedReq) => {
        cy.url().should('include', `prev=${encodeURIComponent(interceptedReq.request.body.previousPageToken)}`);
      });

      cy.get('[data-testid="leafygreen-ui-select-menubutton"]').click();
      cy.get('li[aria-label="option"][value="75"]').click();
      cy.url().should('include', 'limit=75');
    });
  });

  describe('when a event share URL is created', () => {
    it('should navigate to the same place in the page with the event details', () => {
      cy.reload();
      cy.get('button[aria-label="Share"]', { timeout: 7000 }).click();
      cy.get('[data-testid="copy-url"]').click();

      cy.get('button[aria-label="Next page"]').click();
      cy.get('[data-testid="activity-feed-item"]', { timeout: 7000 }).first().click();
      cy.get('[data-testid="activity-feed-item"]')
        .its('length')
        .then((rowCount) => {
          cy.wrap(rowCount).as('numEntries');
        });

      // Cypress blocks clipboard access so manually changing the url to make it a share link
      cy.get('button[aria-label="Link"]').click();
      cy.url().then((currentUrl) => {
        cy.visit(currentUrl + '&share');
      });
      cy.reload();

      cy.get('@numEntries').then((numEntries) => {
        cy.get('[data-testid="activity-feed-item"]', { timeout: 10000 }).should('have.length', numEntries);
      });
      cy.get('button[aria-label="X"]').click();
      cy.get('button[aria-label="Previous page"]').click();

      cy.get('[data-testid="activity-feed-item"]', { timeout: 10000 }).should('have.length', 50);
    });
  });
});
