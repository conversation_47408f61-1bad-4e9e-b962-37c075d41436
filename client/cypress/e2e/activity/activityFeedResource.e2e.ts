import { AlertConfigType } from '@packages/types/alerts/alertConfigs/AlertConfig/AlertConfigType';
import { EventType } from '@packages/types/alerts/alertConfigs/AlertConfig/EventType';
import { Scope } from '@packages/types/alerts/Scope';
import { GlobalRole } from '@packages/types/roles';

describe('Test Notification E2E', { testIsolation: false }, () => {
  let projectId: string;
  const SERVICE_KEY = '********************************';

  before(() => {
    cy.clearMmsCookies();
    cy.registerAndLoginNewUserViaAPI().addGlobalRolesToCurrentUser([
      GlobalRole.GLOBAL_OWNER,
      GlobalRole.GLOBAL_ATLAS_ADMIN,
    ]);
  });

  beforeEach(() => {
    cy.viewport(1400, 1000);
    cy.createOrg();
    cy.createProject().then((pId) => {
      projectId = pId;
    });
  });

  describe('when testing notification endpoints', () => {
    it('call testNotification with a valid payload returns 200', () => {
      const testNotification = {
        _t: 'PAGER_DUTY',
        delay: 0,
        interval: 5,
        integration: {
          _t: 'PAGER_DUTY',
          serviceKey: SERVICE_KEY,
          region: 'US',
        },
      };

      cy.request({
        method: 'POST',
        url: `/activity/testNotification/${projectId}`,
        body: testNotification,
        failOnStatusCode: false,
      }).then((response) => {
        expect(response.status).to.equal(200);
      });
    });

    it('call testNotification with a redacted service key returns 200', () => {
      cy.projectNavigate('integrations');
      cy.contains('[data-testid="integration-card"]', 'PagerDuty Alerts').find('button').contains('Configure').click();
      cy.get('[data-testid="configurationModalInput"]').type(SERVICE_KEY);
      cy.get('button[name="submit"]').contains('Save').click();

      const testNotification = {
        _t: 'PAGER_DUTY',
        delay: 0,
        interval: 5,
        integration: {
          _t: 'PAGER_DUTY',
          serviceKey: '****************************5678',
          region: 'US',
        },
      };

      cy.request({
        method: 'POST',
        url: `/activity/testNotification/${projectId}`,
        body: testNotification,
        failOnStatusCode: false,
      }).then((response) => {
        expect(response.status).to.equal(200);
      });
    });

    it('call testNotification for an existing notification returns 200', () => {
      const alertConfig = {
        _t: AlertConfigType.HOST,
        ac: Scope.Group,
        et: EventType.HOST_DOWN,
        cid: projectId,
        enabled: true,
        forAllGroups: false,
        matchers: [],
        tags: [],
        groups: [],
        notify: [
          {
            _t: 'PAGER_DUTY',
            delay: 0,
            interval: 5,
            integration: {
              _t: 'PAGER_DUTY',
              serviceKey: '********************************',
              region: 'US',
            },
          },
        ],
      };

      cy.request({
        method: 'POST',
        url: `/activity/alertConfigs/${projectId}`,
        body: alertConfig,
        failOnStatusCode: false,
      }).then((alertConfigResponse) => {
        expect(alertConfigResponse.status).to.equal(200);

        const alertConfigId = alertConfigResponse.body.id;
        const notificationId = alertConfigResponse.body.notify[0].id;
        const testNotification = {
          _t: 'PAGER_DUTY',
          delay: 0,
          interval: 5,
          integration: {
            _t: 'PAGER_DUTY',
            serviceKey: '********************************',
            region: 'US',
          },
        };

        cy.request({
          method: 'POST',
          url: `/activity/testNotification/${projectId}/${alertConfigId}/${notificationId}`,
          body: testNotification,
          failOnStatusCode: false,
        }).then((response) => {
          expect(response.status).to.equal(200);
        });
      });
    });
  });
});
