import FullPagePayment, { ClusterTypes, MockClusterResponse } from 'cypress/pages/FPP/FullPagePayment';
import ClusterStarterTemplates from 'cypress/pages/starterTemplates/ClusterStarterTemplates';

describe('Cluster Starter Templates', { testIsolation: false }, () => {
  before(() => {
    cy.registerAndLoginNewUserViaAPI().addGlobalRolesToCurrentUser(['GLOBAL_BILLING_ADMIN']);
    cy.setNdsPlannerEnabled(false);
    cy.createOrg().createProject();
  });

  after(() => {
    cy.deletePaymentMethods();
    cy.deleteProject();
    cy.deleteOrg();
    cy.setNdsPlannerEnabled(true);
  });

  beforeEach(() => {
    cy.viewport(1400, 1000);
  });

  afterEach(() => {
    cy.deleteClusterDraft('Cluster0', false);
  });

  describe('when a user visits the cluster starter templates page', () => {
    before(() => {
      MockClusterResponse.mockFlexProviderOptions();
      MockClusterResponse.mockFlexTemplateCostEstimate();
      MockClusterResponse.mockFlexTemplateLookup();
      cy.projectNavigate('clusters/starterTemplates');
      cy.wait(10000);
    });

    describe('when a user selects the flex template, changes the name, and then clicks Create', () => {
      before(() => {
        ClusterStarterTemplates.selectFlexCluster();
        ClusterStarterTemplates.changeClusterName('FlexClusterName');
        ClusterStarterTemplates.clickCreateButton();
      });

      it('user should be redirected to the FPP page and see the expected header', () => {
        FullPagePayment.expectTitle('Confirm and Deploy Your Flex Cluster');
      });

      describe('when a user navigates back to cluster starter templates', () => {
        before(() => {
          MockClusterResponse.mockFlexProviderOptions();
          FullPagePayment.clickEditButton();
          cy.wait(10000);
        });
        it('the flex template should be selected', () => {
          cy.get('[data-testid="template-cards-flex"]', { timeout: 10000 }).should('have.attr', 'aria-checked', 'true');
        });
        it('the cluster name should match the changed name', () => {
          cy.get('#cluster-starter-template-name-input', { timeout: 15000 }).should(
            'have.attr',
            'value',
            'FlexClusterName'
          );
        });
        describe('when a user continues to FPP again', () => {
          before(() => {
            MockClusterResponse.mockFlexTemplateCostEstimate();
            MockClusterResponse.mockFlexTemplateLookup();
            ClusterStarterTemplates.clickCreateButton();
          });

          it('user should be redirected to the FPP page and see the expected header', () => {
            FullPagePayment.expectTitle('Confirm and Deploy Your Flex Cluster');
          });
        });
      });
    });

    describe('when a user selects M10, chooses a different region, changes the name, and then clicks Create', () => {
      before(() => {
        cy.projectNavigate('clusters/starterTemplates');
        ClusterStarterTemplates.selectDedicatedCluster();
        ClusterStarterTemplates.selectDifferentRegion('Ohio');
        ClusterStarterTemplates.changeClusterName('DedicatedClusterName');
        ClusterStarterTemplates.clickCreateButton();
      });

      it('user should be redirected to the FPP page and see the expected header', () => {
        FullPagePayment.expectTitle('Finish and Deploy Your Single Region Cluster');
      });

      describe('when a user navigates back to cluster starter templates', () => {
        before(() => {
          FullPagePayment.clickEditButton();
          cy.wait(10000);
        });
        it('the region should match the changed region', () => {
          cy.get('.region-dropdown').contains('Ohio');
        });
        it('the cluster name should match the changed name', () => {
          cy.get('#cluster-starter-template-name-input', { timeout: 15000 }).should(
            'have.attr',
            'value',
            'DedicatedClusterName'
          );
        });
        describe('when a user continues to FPP again', () => {
          before(() => {
            ClusterStarterTemplates.clickCreateButton();
          });

          it('user should be redirected to the FPP page and see the expected header', () => {
            FullPagePayment.expectTitle('Finish and Deploy Your Single Region Cluster');
          });
        });
      });
    });

    describe('when the user has already created a M0 cluster, then selects Flex and clicks create', () => {
      before(() => {
        MockClusterResponse.mockFlexProviderOptions();
        MockClusterResponse.mockFlexTemplateCostEstimate();
        MockClusterResponse.mockFlexTemplateLookup();

        cy.createMockedClusterViaUI({
          shouldSkipFPP: true,
          clusterType: ClusterTypes.Flex,
          hasPaymentMethod: false,
        });
        cy.deleteClusterDraft('Cluster0', false);

        cy.projectNavigate('clusters/starterTemplates');
        cy.wait(10000);

        cy.get('[data-testid="template-cards-flex"]', { timeout: 10000 }).click();
        cy.wait(1000);
        ClusterStarterTemplates.clickCreateButton();
      });

      it('user should be redirected to the FPP page and see the expected header, then should go back to CST and see Flex selected', () => {
        FullPagePayment.expectTitle('Confirm and Deploy Your Flex Cluster');

        FullPagePayment.clickEditButton();
        cy.wait(10000);
        cy.get('[data-testid="template-cards-flex"]', { timeout: 10000 }).should('have.attr', 'aria-checked', 'true');
      });
    });
  });
});
