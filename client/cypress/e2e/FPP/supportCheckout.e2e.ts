describe('Full Page Payment', () => {
  before(() => {
    cy.registerAndLoginNewUserViaAPI().addGlobalRolesToCurrentUser(['GLOBAL_BILLING_ADMIN']);
    cy.createOrg();
    cy.viewport(1400, 1000);
  });

  after(() => {
    cy.deletePaymentMethods();
    cy.deleteOrg();
  });

  describe('When user navigates to the support matrix page and activates a self-serve support plan', () => {
    it('should go through FPP then show the confirmation modal and navigate back to the support page', () => {
      cy.purchaseProSupportViaUI(false);
    });
  });
});
