import { GlobalRole } from '@packages/types/roles';

import { createOrgServiceAccount } from './helpers';
import { createNewOrg, navigateToOrgEditServiceAccountPage } from './navigation';

const NEW_ACCESS_LIST_ENTRY = '127.0.0.3';
const SA_INIT_NAME = 'SA Name before edit';
const SA_INIT_DESC = 'SA Desc before edit';

describe('Edit Service Account Page', { testIsolation: true }, () => {
  beforeEach(() => {
    cy.registerAndLoginNewUserViaAPI().addGlobalRolesToCurrentUser([GlobalRole.GLOBAL_OWNER]);
    createNewOrg();
    cy.intercept('POST', '**/serviceAccounts').as('createServiceAccount');
    cy.intercept('POST', '**/serviceAccounts/*/secrets').as('createSecret');
    cy.intercept('GET', '**/serviceAccounts/*').as('getServiceAccount');
    cy.intercept('GET', '**/serviceAccounts').as('getServiceAccounts');
    createOrgServiceAccount(SA_INIT_NAME, SA_INIT_DESC);
    cy.wait('@createServiceAccount').then(({ response }) => {
      expect(response!.statusCode).to.eq(201);
      expect(response).not.to.be.an('undefined');
      const serviceAccount = response!.body;
      const clientId = serviceAccount.clientId;
      navigateToOrgEditServiceAccountPage(clientId);
    });
    cy.intercept('PATCH', '**/serviceAccounts/*').as('editServiceAccount');
  });

  it('the page fields should be visible and pre-populated', () => {
    cy.contains('Edit Service Account Information');
    cy.contains('Enter Service Account Information');
    cy.contains('Organization Permissions');

    cy.contains('Name');
    cy.contains('Description');
    cy.contains('Client Secret Expiration').should('not.exist');

    cy.contains('Names must not be more than 64 characters.');
    cy.contains('Descriptions must not be more than 250 characters.');

    cy.get('[data-testid="sa-name"]').should('have.value', SA_INIT_NAME);
    cy.get('[data-testid="sa-description"]').should('have.value', SA_INIT_DESC);
    cy.get('[role="option"]').contains('Organization Owner');
  });

  it('navigates to previous page on cancel', () => {
    cy.get('button').contains('Cancel').click();
    cy.url().should('contain', `/v2#/org/${global.MMS.lastCreatedOrgId}/access/serviceAccounts/create`);
  });

  it('enables service account save when all fields are populated', () => {
    cy.get('button').contains('Save and next').parent().should('have.attr', 'aria-disabled', 'false');

    cy.get('input#sa-name').click().focus().clear();

    cy.get('button').contains('Save and next').parent().should('have.attr', 'aria-disabled', 'true');

    cy.get('input#sa-name').click().focus().clear().type('name').should('have.value', 'name');

    cy.get('button').contains('Save and next').parent().should('have.attr', 'aria-disabled', 'false');

    cy.get('input#sa-description').click().focus().clear();

    cy.get('button').contains('Save and next').parent().should('have.attr', 'aria-disabled', 'true');

    cy.get('input#sa-description').click().focus().clear().type('description').should('have.value', 'description');

    cy.get('[role="combobox"]').click();
    cy.get('[role="option"]').contains('Organization Member').click({ force: true });
    cy.get('button').contains('Save and next').parent().should('have.attr', 'aria-disabled', 'false');
  });

  describe('when save service account button is clicked', () => {
    beforeEach(() => {
      cy.get('button').contains('Save and next').click();
    });

    it('updates the service account and shows the service account details', () => {
      cy.wait('@editServiceAccount').then(({ response }) => {
        expect(response!.statusCode).to.eq(200);
        expect(response).not.to.be.an('undefined');
        const serviceAccount = response!.body;
        cy.get('h2').contains('Service Account Information');
        cy.get('label').contains('Client ID');
        cy.get('code').contains(serviceAccount.clientId);
        cy.get('label').contains('Client Secret');
        cy.get('code').contains(serviceAccount.secrets[0].maskedSecretValue);
        cy.get('[role="alert"]').contains(
          'For security purposes, we cannot display a full private key after initial creation.'
        );
        cy.get('button')
          .filter(':contains("Generate new client secret")')
          .should('have.attr', 'aria-disabled', 'false');
        cy.get('h2').contains('API Access List');
        cy.get('button').filter(':contains("Add Access List Entry")').should('have.attr', 'aria-disabled', 'false');
      });
    });

    it('navigates to service accounts page on go to applications click', () => {
      cy.get('button').contains('Cancel').click();
      cy.url().should('contain', `/v2#/org/${global.MMS.lastCreatedOrgId}/access/serviceAccounts`);
    });

    it('when the user creates a new secret, both secrets are visible on the page', () => {
      cy.get('button').contains('Generate new client secret').click();
      cy.contains('Generate a New Client Secret').should('be.visible');
      cy.get('button').contains('Generate New').click();

      cy.wait(['@createSecret', '@getServiceAccount']).then((interceptions) => {
        const createSecretResponse = interceptions[0].response;
        const getServiceAccountResponse = interceptions[1].response;
        expect(createSecretResponse).not.to.be.an('undefined');
        expect(getServiceAccountResponse).not.to.be.an('undefined');
        expect(createSecretResponse!.statusCode).to.eq(201);
        expect(getServiceAccountResponse!.statusCode).to.eq(200);
        const serviceAccount = getServiceAccountResponse!.body;
        cy.wrap(serviceAccount.clientId).as('clientId');
        const newSecret = createSecretResponse!.body;

        cy.contains('New Client Secret Created').should('be.visible');
        cy.get('@clientId').then((clientId) => {
          cy.contains(`${clientId}`).last().should('exist');
        });

        cy.contains(newSecret.secret).should('be.visible');

        cy.get('button').contains('Close').click();
        cy.contains('New Client Secret Created').should('not.exist');

        serviceAccount.secrets.forEach((secret) => {
          cy.contains(secret.maskedSecretValue).should('exist');
        });
        cy.contains(newSecret.secret).should('not.exist');
        cy.get('button').filter(':contains("Generate new client secret")').should('not.exist');
      });
    });

    it('when the user clicks add access list entry and types a correct IP, it is added', () => {
      cy.get('table').should('not.exist');
      cy.get('button').contains('Add Access List Entry').click();

      cy.contains(
        'Creating an API Access List ensures that API calls must originate from IPs given access. Learn more.'
      ).should('be.visible');

      cy.get('input').first().type(NEW_ACCESS_LIST_ENTRY);

      cy.get('button').contains('Save').click();

      cy.contains(
        'Creating an API Access List ensures that API calls must originate from IPs given access. Learn more.'
      ).should('not.exist');

      cy.get('table').should('be.visible');
      cy.get('table th').then((elements) => {
        expect(elements.length).to.equal(5);
        expect(elements.eq(0)).to.contain('Access List Entry');
        expect(elements.eq(1)).to.contain('Last IP Used');
        expect(elements.eq(2)).to.contain('Last Used');
        expect(elements.eq(3)).to.contain('Created On');
        expect(elements.eq(4)).to.contain('');
      });

      cy.get('table tbody tr').then((elements) => {
        expect(elements.length).to.equal(1);
        expect(elements.eq(0).find('td')).to.contain(NEW_ACCESS_LIST_ENTRY);
      });
    });

    it('when the user clicks add access list entry and uses current IP, it is added', () => {
      cy.get('table').should('not.exist');
      cy.get('button').contains('Add Access List Entry').click();

      cy.contains(
        'Creating an API Access List ensures that API calls must originate from IPs given access. Learn more.'
      ).should('be.visible');

      cy.get('button').contains('Use current IP address').click();

      cy.get('button').contains('Save').click();

      cy.contains(
        'Creating an API Access List ensures that API calls must originate from IPs given access. Learn more.'
      ).should('not.exist');

      cy.get('table').should('be.visible');
      cy.get('table th').then((elements) => {
        expect(elements.length).to.equal(5);
        expect(elements.eq(0)).to.contain('Access List Entry');
        expect(elements.eq(1)).to.contain('Last IP Used');
        expect(elements.eq(2)).to.contain('Last Used');
        expect(elements.eq(3)).to.contain('Created On');
        expect(elements.eq(4)).to.contain('');
      });

      cy.get('table tbody tr').then((elements) => {
        expect(elements.length).to.equal(1);
        expect(elements.eq(0).find('td')).to.contain('127.0.0.1');
      });
    });

    it('when the user adds and deletes an access list entry, the table is not visible', () => {
      cy.get('button').contains('Add Access List Entry').click();
      cy.get('input').first().type(NEW_ACCESS_LIST_ENTRY);
      cy.get('button').contains('Save').click();

      cy.get('table tbody tr').eq(0).find('button').click();

      cy.contains('Are you sure you want to remove this IP address from the access list?').should('be.visible');

      cy.get('button').contains('Remove').click();

      cy.contains('Are you sure you want to remove this IP address from the access list?').should('not.exist');
      cy.get('table').should('not.exist');
    });
  });
});
