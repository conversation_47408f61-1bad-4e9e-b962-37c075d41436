// TODO TH CLOUDP-320291: Uncomment this test and remove the skip
// import LoginPage from 'cypress/pages/auth/LoginPage';
// import { SEGMENT_EVENTS, SEGMENT_PAGEVIEWS, USER_TRAITS } from 'cypress/support/criticalAnalytics';
//
// describe.skip('Login page', { tags: ['@critical-analytics'] }, () => {
//   const spyClustersCall = () => {
//     cy.intercept({
//       pathname: '/nds/clusters/*',
//     }).as('clusters');
//   };
//
//   before(() => {
//     cy.recordMockServerRequests();
//   });
//
//   after(() => {
//     cy.clearMockServerRequests();
//   });
//
//   it(`should send a '${SEGMENT_PAGEVIEWS.SIGN_IN}' Segment pageview when user visits the login page`, () => {
//     cy.spyPageviewCall(SEGMENT_PAGEVIEWS.SIGN_IN);
//
//     // The First Time Cloud Login event is tracked on the first login,
//     // the Signed In event is tracked on subsequent logins.
//     cy.registerAndLoginSegmentUserViaUI();
//     cy.waitForSegmentEventsToBeProcessed();
//
//     // At this point of the execution there are already Sign In pageviews
//     // recorded due to the global set-up of E2E tests. We must reset them
//     // to accurately test the visit to the login page.
//     cy.resetRecordedMockServerRequests();
//     LoginPage.visit();
//     cy.wait('@pageview').then(() => {
//       cy.contains('Email Address').should('be.visible').and('not.be.disabled').click();
//     });
//     cy.countMockServerPageviews(SEGMENT_PAGEVIEWS.SIGN_IN).should('equal', 1);
//   });
//
//   describe('when user enters the login information and submits the form', () => {
//     before(() => {
//       spyClustersCall();
//
//       cy.loginViaUI(false, false);
//       cy.waitForSegmentEventsToBeProcessed();
//     });
//
//     it(`should send a '${SEGMENT_EVENTS.SIGNED_IN}' Segment event`, () => {
//       cy.retrieveMockServerRequests({
//         filterCriteria: {
//           body: {
//             event: SEGMENT_EVENTS.SIGNED_IN,
//           },
//         },
//       }).then((res) => {
//         expect(res.count).eq(1);
//         const segmentRequest = res.requests[0].body;
//         expect(segmentRequest).to.exist;
//         expect(segmentRequest!.event).equal(SEGMENT_EVENTS.SIGNED_IN);
//
//         cy.wait('@clusters').then(() => {
//           cy.window().then((window) => {
//             const params = window.PARAMS;
//             expect(segmentRequest!.properties.auid).equal(params.userAuid);
//             expect(segmentRequest!.properties.cloud_user_id).equal(params.appUser.id);
//             expect(segmentRequest!.context.traits[USER_TRAITS.USERNAME]).equal(params.appUser.username);
//             expect(segmentRequest!.properties[USER_TRAITS.ACTIVE_ORG_ID]).equal(params.appUser.currentOrgId);
//           });
//         });
//       });
//     });
//   });
// });
