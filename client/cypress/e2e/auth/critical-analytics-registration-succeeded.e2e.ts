// TODO TH CLOUDP-320291: Uncomment this test and remove the skip
// import RegistrationPage from 'cypress/pages/auth/RegistrationPage';
// import { SEGMENT_EVENTS, SEGMENT_PAGEVIEWS, USER_TRAITS } from 'cypress/support/criticalAnalytics';
//
// describe.skip('Registration page', { tags: ['@critical-analytics'] }, () => {
//   before(() => {
//     cy.recordMockServerRequests();
//   });
//
//   after(() => {
//     cy.clearMockServerRequests();
//   });
//
//   it(`should send a '${SEGMENT_PAGEVIEWS.REGISTRATION}' Segment pageview when user visits registration page`, () => {
//     cy.spyPageviewCall(SEGMENT_PAGEVIEWS.REGISTRATION);
//     RegistrationPage.visit();
//
//     cy.wait('@pageview');
//     cy.countMockServerPageviews(SEGMENT_PAGEVIEWS.REGISTRATION).should('equal', 1);
//   });
//
//   const spyRegistrationCalls = () => {
//     cy.intercept('POST', '/user/registerCall').as('registrationData');
//   };
//
//   describe('when user fills out and submits the form', () => {
//     let params, registrationData;
//
//     before(() => {
//       spyRegistrationCalls();
//
//       cy.registerSegmentUserViaUI();
//       cy.waitForSegmentEventsToBeProcessed();
//
//       cy.wait('@registrationData').then((interception) => {
//         registrationData = interception.request.body;
//       });
//       cy.window().then((window) => {
//         params = window.PARAMS;
//       });
//     });
//
//     it(`should send a '${SEGMENT_EVENTS.REGISTRATION_SUCCEEDED}' Segment event`, () => {
//       cy.retrieveMockServerRequests({
//         filterCriteria: {
//           body: {
//             event: SEGMENT_EVENTS.REGISTRATION_SUCCEEDED,
//           },
//         },
//       }).then((res) => {
//         expect(res.count).eq(1);
//         const segmentRequest = res.requests[0].body;
//         expect(segmentRequest).to.exist;
//         expect(segmentRequest!.event).equal(SEGMENT_EVENTS.REGISTRATION_SUCCEEDED);
//         expect(segmentRequest!.properties.context).equal('Account Registration');
//         expect(segmentRequest!.properties.auid).equal(params.userAuid);
//         expect(segmentRequest!.properties.cloud_user_id).equal(params.appUser.id);
//         expect(segmentRequest!.context.traits[USER_TRAITS.USERNAME]).equal(registrationData.username);
//         expect(segmentRequest!.context.traits[USER_TRAITS.FIRST_NAME]).equal(registrationData.firstName);
//         expect(segmentRequest!.context.traits[USER_TRAITS.LAST_NAME]).equal(registrationData.lastName);
//         expect(segmentRequest!.properties.company).equal(registrationData.company);
//         expect(segmentRequest!.properties.signup_method).equal(registrationData.clientState.signupMethod);
//         expect(segmentRequest!.properties.signup_source).equal(registrationData.clientState.signupSource);
//       });
//     });
//
//     it(`should send a '${SEGMENT_EVENTS.FORM_SUBMITTED}' Segment event`, () => {
//       cy.retrieveMockServerRequests({
//         filterCriteria: {
//           body: {
//             event: SEGMENT_EVENTS.FORM_SUBMITTED,
//           },
//         },
//       }).then((res) => {
//         expect(res.count).eq(1);
//         const segmentRequest = res.requests[0].body;
//         expect(segmentRequest).to.exist;
//         expect(segmentRequest!.event).equal(SEGMENT_EVENTS.FORM_SUBMITTED);
//         expect(segmentRequest!.properties.anonymousId).equal(registrationData.anonymousId);
//         expect(segmentRequest!.properties.email).equal(registrationData.username);
//         expect(segmentRequest!.properties.first_name).equal(registrationData.firstName);
//         expect(segmentRequest!.properties.last_name).equal(registrationData.lastName);
//         expect(segmentRequest!.properties.signup_method).equal(registrationData.clientState.signupMethod);
//         expect(segmentRequest!.properties.signup_source).equal(registrationData.clientState.signupSource);
//         expect(segmentRequest!.properties.elqSiteID).equal(Number(params.eloquaSiteId));
//         expect(segmentRequest!.properties.elqFormName).equal('atlas-registration-1537380126407');
//         expect(segmentRequest!.properties.elqCookieWrite).equal(0);
//         expect(segmentRequest!.properties.send_eloqua).equal(false);
//         expect(segmentRequest!.properties.web_check_businessemail).equal(true);
//         expect(segmentRequest!.properties.email_type).equal('business');
//         expect(segmentRequest!.properties.form_type).equal('registration');
//       });
//     });
//
//     it(`should send a '${SEGMENT_PAGEVIEWS.REGISTRATION_SUCCESS}' Segment pageview`, () => {
//       cy.countMockServerPageviews(SEGMENT_PAGEVIEWS.REGISTRATION_SUCCESS).should('equal', 1);
//     });
//   });
// });
