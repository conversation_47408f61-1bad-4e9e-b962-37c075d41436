import {
  EMERGENCY_PHASE,
  forceConfigServiceSdkToRefreshConfigs,
  setEmergencyPhaseAndSave,
} from 'cypress/pages/admin/ConfigServiceFeatureFlagsPages';
import ClusterDrafts, { defaultClusterName } from 'cypress/pages/clusterDrafts/ClusterDrafts';
import { MockClusterResponse } from 'cypress/pages/FPP/FullPagePayment';

import { GlobalRole } from '@packages/types/roles';

describe('ClusterDraftsSaveButtonCB', { testIsolation: false }, () => {
  before(() => {
    cy.registerAndLoginNewUserViaAPI().addGlobalRolesToCurrentUser([GlobalRole.GLOBAL_OWNER]);
    // in "local" envs, this flag is "disabled", so we enable it for these tests
    setEmergencyPhaseAndSave('mms.featureFlag.resourceTagComponent.clusters', EMERGENCY_PHASE.ENABLED);
    forceConfigServiceSdkToRefreshConfigs();
    cy.createOrg().createProject();
  });

  beforeEach(() => {
    cy.viewport(1400, 1000);
  });

  after(() => {
    cy.deleteProject();
    cy.deleteOrg();
    setEmergencyPhaseAndSave('mms.featureFlag.resourceTagComponent.clusters', EMERGENCY_PHASE.NONE);
    forceConfigServiceSdkToRefreshConfigs();
  });

  describe('when user clicks around an existing cluster in Cluster Builder', () => {
    beforeEach(() => {
      MockClusterResponse.mockExistingClusters('M10');
      MockClusterResponse.mockClusterUsageStats();
      cy.projectNavigate('clusters');
    });

    after(() => {
      cy.deleteClusterDraft(defaultClusterName, true);
    });

    describe('and makes changes they do not want to keep', () => {
      it('should wipe the changes after loading the page', () => {
        ClusterDrafts.makeCBChangesAndReload({
          isAutoSave: false,
          isEdit: true,
          clusterName: defaultClusterName,
        });
        ClusterDrafts.resetClusterDraftAndVerifyChangesInCB({ isEdit: true });
      });
    });

    describe('and makes changes they want to keep', () => {
      it('should load the cluster draft data properly', () => {
        ClusterDrafts.makeCBChangesAndReload({
          isAutoSave: false,
          isEdit: true,
          clusterName: defaultClusterName,
        });
        ClusterDrafts.loadClusterDraftAndVerifyChangesInCB({ isEdit: true });
      });
    });
  });

  describe('when user clicks around an existing cluster in Cluster Builder to add cluster resources tag', () => {
    beforeEach(() => {
      MockClusterResponse.mockExistingClusters('M10');
      MockClusterResponse.mockClusterUsageStats();
      cy.projectNavigate('clusters');
    });

    after(() => {
      cy.deleteClusterDraft(defaultClusterName, true);
    });

    describe('and makes changes they want to keep', () => {
      it('should load the cluster draft data properly', () => {
        ClusterDrafts.makeCBChangesAndReload({
          isAutoSave: false,
          isEdit: true,
          clusterName: defaultClusterName,
          addResourceTag: true,
        });
        ClusterDrafts.loadClusterDraftAndVerifyChangesInCB({ isEdit: true, checkResouceTag: true });
      });
    });
  });

  describe('when user edits a shared cluster', () => {
    beforeEach(() => {
      MockClusterResponse.mockExistingClusters('M0');
      MockClusterResponse.mockClusterUsageStats();
      MockClusterResponse.mockFreeOptions();
      cy.projectNavigate('clusters');
    });

    after(() => {
      cy.deleteClusterDraft(defaultClusterName, true);
    });

    describe('and upgrades to a dedicated tier and saves the draft', () => {
      it('should load the cluster draft when coming back to the page', () => {
        ClusterDrafts.upgradeSharedToDedicatedAndReload({ clusterName: defaultClusterName });
        ClusterDrafts.loadClusterUpgradeDraftAndVerifyChangesInCB();
      });
    });
  });
});
