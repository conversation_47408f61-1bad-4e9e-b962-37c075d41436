.loading {
  font-style: italic;
}

.error {
  color: red;
}

.panel {
  padding: 20px;
}

.resetButton {
  background: #f36800;
  color: white;
  border-radius: 5px;
}

.footer {
  min-height: auto;
  height: 24px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

.footer::before {
  content: 'Experiment changes may not appear until applied';
  width: 75vw;
}

.radioBoxGroup {
  width: 100%;
  flex-wrap: wrap;
}

.radioBox {
  margin-top: 12px;
  font-weight: 400;
}

input[aria-checked='true'] ~ div {
  font-weight: bold;
}
