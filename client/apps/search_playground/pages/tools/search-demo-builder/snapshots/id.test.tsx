import { act, render, screen } from '@testing-library/react';
import { NextRouter, useRouter } from 'next/router';

import { DiscardProgressModal } from '../../../../components/DiscardProgressModal/DiscardProgressModal';
import { SideNavPanel } from '../../../../components/SideNavPanel/SideNavPanel';
import { page } from '../../../../packages/analytics/analytics';
import { ToolName } from '../../../../packages/config/tools.config';
import { FeatureFlagName } from '../../../../packages/feature-flags/feature-flags';
import { DemoBuilder } from '../../../../tools/demo-builder/containers/DemoBuilder/DemoBuilder';
import {
  BASIC_DEMO_BUILDER_CONFIG,
  DemoBuilderConfig,
} from '../../../../tools/demo-builder/packages/demo-builder-config/demo-builder-config';
import DemoBuilderPage, { testId } from './[id].page';

enum mockTestId {
  DemoBuilder = 'DemoBuilder',
  DiscardProgressModal = 'DiscardProgressModal',
  SideNavPanel = 'SideNavPanel',
}

jest.mock('../../../../tools/demo-builder/containers/DemoBuilder/DemoBuilder', () => {
  return {
    DemoBuilder: jest.fn().mockImplementation(() => <div data-testid={mockTestId.DemoBuilder}>Mock DemoBuilder</div>),
  };
});

jest.mock('../../../../components/DiscardProgressModal/DiscardProgressModal', () => {
  return {
    DiscardProgressModal: jest
      .fn()
      .mockImplementation(() => <div data-testid={mockTestId.DiscardProgressModal}>Mock DiscardProgressModal</div>),
  };
});

jest.mock('../../../../components/SideNavPanel/SideNavPanel', () => {
  return {
    SideNavPanel: jest
      .fn()
      .mockImplementation(() => <div data-testid={mockTestId.SideNavPanel}>Mock SideNavPanel</div>),
  };
});

jest.mock('next/router', () => {
  return {
    __esModule: true,
    useRouter: jest.fn(),
  };
});

jest.mock('../../../../packages/analytics/analytics', () => {
  const actualModule = jest.requireActual('../../../../packages/analytics/analytics');
  return {
    __esModule: true,
    ...actualModule,
    page: jest.fn().mockResolvedValue(null),
    initAnalytics: jest.fn().mockResolvedValue(null),
  };
});

const pageMock = page as jest.MockedFunction<typeof page>;
const useRouterMock = useRouter as jest.MockedFunction<typeof useRouter>;
const DiscardProgressModalMock = DiscardProgressModal as jest.MockedFunction<typeof DiscardProgressModal>;
const DemoBuilderMock = DemoBuilder as jest.MockedFunction<typeof DemoBuilder>;
const SideNavPanelMock = SideNavPanel as jest.MockedFunction<typeof SideNavPanel>;

describe('DemoBuilderPage', () => {
  const renderComponent = (pRouter?: NextRouter) => {
    const router = pRouter || ({} as unknown as NextRouter);
    useRouterMock.mockReturnValue(router);
    render(
      <DemoBuilderPage
        featureFlags={{
          [FeatureFlagName.CHATBOT_DEMO_BUILDER_ENABLED_FEATURE_FLAG]: false,
          [FeatureFlagName.CODE_SANDBOX_EXPORT_FEATURE_FLAG]: false,
          [FeatureFlagName.CODE_SANDBOX_QUERY_EXPLAIN_PLANNER_FEATURE_FLAG]: false,
        }}
      />
    );
  };

  it('should render page', () => {
    renderComponent();
    expect(screen.getByTestId(testId.root)).toBeInTheDocument();
  });

  it('should show "Discard progress" modal when user tries to navigate out of Demo builder with updated configuration', () => {
    renderComponent();
    const updatedConfig = {
      ...BASIC_DEMO_BUILDER_CONFIG,
      documents: `[]`,
    };
    updateDemoBuilderConfig(updatedConfig);
    setNavigateToToolName(ToolName.CODE_SANDBOX);
    triggerShouldDisableToolNavigation(ToolName.CODE_SANDBOX, false);
    expect(screen.getByTestId(mockTestId.DiscardProgressModal)).toBeInTheDocument();
  });

  it('should navigate to other tool after user confirmed "Discard progress" modal warning', () => {
    const pushMock = jest.fn();
    const mockRouter = {
      push: pushMock,
    } as unknown as NextRouter;
    renderComponent(mockRouter);
    const updatedConfig = {
      ...BASIC_DEMO_BUILDER_CONFIG,
      documents: `[]`,
    };
    updateDemoBuilderConfig(updatedConfig);
    setNavigateToToolName(ToolName.CODE_SANDBOX);
    triggerShouldDisableToolNavigation(ToolName.CODE_SANDBOX, false);
    confirmDiscardProgressModal();
    expect(pushMock).toHaveBeenCalledWith('/tools/code-sandbox/snapshots/new');
  });

  it('should call analytics "page" event', async () => {
    renderComponent();
    expect(pageMock).toHaveBeenCalledWith('Demo Builder');
  });
});

const updateDemoBuilderConfig = (newConfig: DemoBuilderConfig) => {
  const props = DemoBuilderMock.mock.lastCall![0];
  act(() => {
    props.onConfigUpdate!(newConfig);
  });
};

const triggerShouldDisableToolNavigation = (toolName: ToolName, metaKeyPressed: boolean) => {
  const props = SideNavPanelMock.mock.lastCall![0];
  act(() => {
    props.shouldDisableToolNavigation!(toolName, metaKeyPressed);
  });
};

const confirmDiscardProgressModal = () => {
  const props = DiscardProgressModalMock.mock.lastCall![0];
  act(() => {
    props.onConfirm();
  });
};

const setNavigateToToolName = (toolName: ToolName) => {
  const props = SideNavPanelMock.mock.lastCall![0];
  act(() => {
    props.setNavigateToToolName!(toolName);
  });
};
