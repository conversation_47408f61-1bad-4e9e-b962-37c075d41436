import { render, screen } from '@testing-library/react';

import { testId as sectionTitleTestId } from '../SectionTitle/SectionTitle';
import { EditorHead<PERSON>, EditorHeaderProps } from './EditorHeader';

describe('components/EditorHeader/EditorHeader.tsx', () => {
  const renderComponent = (pProps?: Partial<EditorHeaderProps>) => {
    const props: EditorHeaderProps = {
      title: 'title',
      description: 'description',
      ...pProps,
    };
    render(<EditorHeader {...props} />);
  };

  it('should render title', async () => {
    renderComponent({ title: 'new title' });
    expect(screen.getByTestId(sectionTitleTestId.title)).toHaveTextContent('new title');
  });
});
