import { act, render, screen } from '@testing-library/react';

import { testId as DocumentsTestId } from '../../components/Documents/Documents';
import { fetchSearchResults } from '../../packages/api/api';
import { BASIC_DEMO_BUILDER_CONFIG } from '../../packages/demo-builder-config/demo-builder-config';
import { testId as PreviewTestId } from '../Preview/Preview';
import { DemoBuilder, DemoBuilderProps, testId } from './DemoBuilder';

jest.mock('../../packages/api/api');
const fetchSearchResultsMock = fetchSearchResults as jest.MockedFunction<typeof fetchSearchResults>;

describe('DemoBuilder', () => {
  const renderComponent = async (pProps?: Partial<DemoBuilderProps>) => {
    const props: DemoBuilderProps = {
      initialConfig: BASIC_DEMO_BUILDER_CONFIG,
      ...pProps,
    };
    return await act(async () => {
      render(<DemoBuilder {...props} />);
    });
  };

  it('should render component and all sub-components', () => {
    fetchSearchResultsMock.mockResolvedValue({
      searchResults: [],
      searchIndex: {},
      searchPipeline: '[]',
      facets: [],
    });
    renderComponent();
    expect(screen.getByTestId(testId.root)).toBeInTheDocument();
    expect(screen.getByTestId(PreviewTestId.root)).toBeInTheDocument();
    expect(screen.getByTestId(DocumentsTestId.root)).toBeInTheDocument();
  });
});
