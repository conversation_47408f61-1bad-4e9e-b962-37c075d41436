import { useState } from 'react';

import { useDebounceFunction } from '../../../../../../packages/external/hooks/useDebounceFunction';
import { FetchError } from '../../../../packages/api/fetchWrapper';
import { useBoundCancelableFetchWithState } from '../../../../packages/hooks/useCancelableFetch';
import { fetchAutocompleteResults } from '../../packages/api/api';
import { DemoBuilderConfig } from '../../packages/demo-builder-config/demo-builder-config';
import {
  AutocompleteRequest,
  isAutocompleteRequestValid,
} from '../../packages/preview-search/autocomplete/autocomplete-request';
import { AutocompleteResult } from '../../packages/preview-search/autocomplete/autocomplete-response';

/**
 * Hook to get list of {@link AutocompleteResult} for the provided search query and {@link
 * DemoBuilderConfig}.
 */
export const useAutocompleteSession = (): [
  Array<AutocompleteResult> | undefined,
  string | undefined,
  (searchQuery: string, demoBuilderConfig: DemoBuilderConfig) => void,
  () => void,
] => {
  const [autocompleteResults, setAutocompleteResults] = useState<Array<AutocompleteResult> | undefined>();
  const [autocompletePipeline, setAutocompletePipeline] = useState<string | undefined>();

  // Configure cancelable autocomplete fetch
  const { fetchData: fetchAutocompleteResultsCancelable, cancelFetch: cancelAutocompleteResultsFetch } =
    useBoundCancelableFetchWithState<typeof fetchAutocompleteResults, FetchError>(
      (signal, autocompleteRequest, options) => {
        return fetchAutocompleteResults(autocompleteRequest, { signal, ...options });
      }
    );

  // Fetch autocomplete results
  const runAutocomplete_ = (searchQuery: string, demoBuilderConfig: DemoBuilderConfig) => {
    cancelAutocompleteResultsFetch();

    const autocompleteRequest: AutocompleteRequest = {
      searchQuery,
      demoBuilderConfig,
    };

    if (!isAutocompleteRequestValid(autocompleteRequest)) {
      return;
    }

    fetchAutocompleteResultsCancelable(autocompleteRequest).then((apiSearchResponse) => {
      setAutocompleteResults(apiSearchResponse.results);
      setAutocompletePipeline(apiSearchResponse.pipeline);
    });
  };

  // Debounce autocomplete fetch function call
  const [runAutocomplete, cancelScheduledAutocompleteRun] = useDebounceFunction(runAutocomplete_, 50);

  // Clear autocomplete results in the current session and cancel in flight autocomplete request.
  const finishAutocompleteSession = () => {
    cancelAutocompleteResultsFetch();
    cancelScheduledAutocompleteRun();
    setAutocompleteResults(undefined);
    setAutocompletePipeline(undefined);
  };

  return [autocompleteResults, autocompletePipeline, runAutocomplete, finishAutocompleteSession];
};
