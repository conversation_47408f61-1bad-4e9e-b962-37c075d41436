load("//client/packages/scripts:client_package.bzl", "client_package")

client_package(
    name = "search-pipeline",
    tsconfig_params = {
        "emit_declaration_only": False,
    },
    visibility = [
        "//client/apps/search_playground/packages/analytics:__pkg__",
        "//client/apps/search_playground/tools/demo-builder/components:__pkg__",
        "//client/apps/search_playground/tools/demo-builder/containers:__pkg__",
        "//client/apps/search_playground/tools/demo-builder/packages/preview-search:__pkg__",
    ],
    deps = [
        "//:node_modules/@types/node",
        "//:node_modules/mongodb",
        "//:node_modules/zod",
        "//:node_modules/bson",
        "//client/apps/search_playground/packages/utils",
        "//client/apps/search_playground/packages/document-schema",
        "//client/apps/search_playground/tools/demo-builder/packages/search-index",
        # browser-ready-node-driver is not used in current package but required by jest config.
        "//client/packages/browser-ready-node-driver",
    ],
)
