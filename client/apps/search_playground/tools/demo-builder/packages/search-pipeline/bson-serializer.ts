import { BSONValue } from 'bson';

import { isObject } from '../../../../packages/utils/is-object';

export type SupportedPrimitive = BSONValue | number | string | boolean | null | undefined;
interface SupportedDocument {
  [key: string]: SupportedPrimitive | SupportedDocument;
}
export type SupportedValue = SupportedPrimitive | SupportedDocument | Array<SupportedPrimitive | SupportedDocument>;

/**
 * Converts BSON values to the MongoDB shell string.
 */
export class BsonSerializer {
  constructor(private value: SupportedValue) {}

  getAsString(): string {
    const serializedValue = this.serializeValue(this.value);
    return serializedValue;
  }

  private serializeValue(value: any): string {
    if (Array.isArray(value)) {
      const serializedArrayContent = value.map((arrayItem) => {
        return this.serializeValue(arrayItem);
      });
      return `[${serializedArrayContent.join(', ')}]`;
    }

    if (value instanceof BSONValue) {
      return value.inspect();
    }

    if (isObject(value)) {
      const entries = Object.entries(value).map(([key, value]) => {
        const serializedValue = this.serializeValue(value);
        return `${key}: ${serializedValue}`;
      });
      return `{${entries.join(', ')}}`;
    }

    return JSON.stringify(value);
  }
}
