import { getLogger } from '../../../../../packages/logger/logger';
import { LoggerName } from '../../../../../packages/logger/logger.types';
import { sendError } from '../../../../../packages/sentry/error-tracker';
import { ServiceError, ServiceErrorCode } from '../../../../../packages/service-errors/service-errors';
import { extractLastTwoWords } from '../../../../../packages/utils/string';
import { WorkloadError, WorkloadErrorCode } from '../../../../../packages/workload/workload.errors';
import { AutocompletePipeline } from '../../search-pipeline/autocomplete-pipeline';
import { PreviewSearchWorkloadRegistry } from '../preview-search-workload-registry';
import { AutocompleteRequest, generateAutocompletePipeline, validateAutocompleteRequest } from './autocomplete-request';
import { AutocompleteResponse, AutocompleteResult } from './autocomplete-response';

export enum SearchResultHighlightType {
  hit = 'hit',
  text = 'text',
}

export interface SearchResultHighlightText {
  value: string;
  type: SearchResultHighlightType;
}

export interface SearchResultHighlight {
  score: number;
  path: string;
  texts: Array<SearchResultHighlightText>;
}

/**
 * Represents data structure that mongodb returns for autocomplete search query.
 */
export interface SearchResultHighlights {
  searchResultScore: number;
  highlights: Array<SearchResultHighlight>;
}

const log = getLogger(LoggerName.DEMO_BUILDER_PREVIEW_AUTOCOMPLETE_SEARCH);

/**
 * Core service that returns autocomplete search results based on the provided {@link
 * AutocompleteRequest}.
 */
export class PreviewAutocompleteSearchService {
  static MAX_AUTOCOMPLETE_RESULTS_COUNT = 10;
  constructor(private previewSearchWorkloadRegistry: PreviewSearchWorkloadRegistry) {}

  /**
   * Returns autocomplete suggestions based on the search request.
   */
  async getAutocompleteResults(autocompleteRequest: AutocompleteRequest): Promise<AutocompleteResponse> {
    this.assertAutocompleteRequest(autocompleteRequest);

    try {
      const autocompletePipeline = this.generateAutocompletePipeline(autocompleteRequest);
      const workload = await this.previewSearchWorkloadRegistry.getWorkload(autocompleteRequest.demoBuilderConfig);
      const searchResultHighlights = await workload.runAggregationPipeline<SearchResultHighlights>(
        autocompletePipeline.getAsDocument()
      );
      const autocompleteResults = this.toAutoCompleteResults(searchResultHighlights);

      return {
        pipeline: autocompletePipeline,
        results: autocompleteResults,
      };
    } catch (error) {
      if (error instanceof WorkloadError && error.errorCode === WorkloadErrorCode.DUPLICATE_DOCUMENT_KEYS) {
        throw new ServiceError(ServiceErrorCode.WORKLOAD_CLIENT_EXECUTION_ERROR({ message: error.message }));
      }

      log.error('Cannot get autocomplete results.', error, {
        autocompleteRequest,
      });

      throw error;
    }
  }

  private toAutoCompleteResults(searchResultHighlightsList: Array<SearchResultHighlights>): Array<AutocompleteResult> {
    const flatSearchResultHighlights = searchResultHighlightsList
      .map((searchResultHighlights) => {
        return searchResultHighlights.highlights;
      })
      .flat();

    const autocompleteResults = flatSearchResultHighlights
      .map((searchResultHighlight: SearchResultHighlight) => {
        return this.toAutoCompleteResult(searchResultHighlight);
      })
      .filter((optionalAutocompleteResult) => {
        return !!optionalAutocompleteResult;
      }) as Array<AutocompleteResult>;
    const uniqueAutocompleteResults = this.getUniqueAutocompleteResults(autocompleteResults);
    return uniqueAutocompleteResults.slice(0, PreviewAutocompleteSearchService.MAX_AUTOCOMPLETE_RESULTS_COUNT);
  }

  private toAutoCompleteResult(searchResultHighlight: SearchResultHighlight): AutocompleteResult | undefined {
    if (searchResultHighlight.texts.length === 0) {
      // shouldn't happen in theory as mongot always has to return matched highlight texts.
      log.error('Autocomplete search result has no matched highlight texts');
      sendError({
        error: new Error('Autocomplete search result has no matched highlight texts'),
      });
      return;
    }

    const hitIndex = searchResultHighlight.texts.findIndex((highlightText) => {
      return highlightText.type === SearchResultHighlightType.hit;
    });

    if (hitIndex === -1) {
      // shouldn't happen in theory as mongot always has to return text with the "hit" type.
      log.error('Autocomplete search result has text with the "hit" type.');
      sendError({
        error: new Error('Autocomplete search result has text with the "hit" type.'),
      });
      return;
    }

    if (hitIndex === 0) {
      return {
        beforeMatch: '',
        hitMatch: searchResultHighlight.texts[hitIndex].value,
      };
    }

    const previousText = searchResultHighlight.texts[hitIndex - 1];
    const previousTwoWords = extractLastTwoWords(previousText.value);
    return {
      beforeMatch: previousTwoWords,
      hitMatch: searchResultHighlight.texts[hitIndex].value,
    };
  }

  private getUniqueAutocompleteResults(autocompleteResults: Array<AutocompleteResult>): Array<AutocompleteResult> {
    const uniqueAutocompleteResults: Array<AutocompleteResult> = [];
    const seenAutocompleteResults = new Set<string>();
    autocompleteResults.forEach((autocompleteResult) => {
      const key = autocompleteResult.beforeMatch + autocompleteResult.hitMatch;
      if (seenAutocompleteResults.has(key)) {
        return;
      }
      seenAutocompleteResults.add(key);
      uniqueAutocompleteResults.push(autocompleteResult);
    });
    return uniqueAutocompleteResults;
  }

  private assertAutocompleteRequest(autocompleteRequest: AutocompleteRequest) {
    const error = validateAutocompleteRequest(autocompleteRequest);
    if (error) {
      throw new ServiceError(
        ServiceErrorCode.INVALID_ARGUMENT({
          message: error,
        })
      );
    }
  }

  private generateAutocompletePipeline(autocompleteRequest: AutocompleteRequest): AutocompletePipeline {
    try {
      return generateAutocompletePipeline(autocompleteRequest);
    } catch (error) {
      log.error('Cannot generate autocomplete pipeline.', error);
      sendError({
        error: new Error("'Cannot generate autocomplete pipeline.", { cause: error }),
      });
      throw new ServiceError(
        ServiceErrorCode.INVALID_ARGUMENT({
          message: 'Cannot generate autocomplete pipeline',
        })
      );
    }
  }
}
