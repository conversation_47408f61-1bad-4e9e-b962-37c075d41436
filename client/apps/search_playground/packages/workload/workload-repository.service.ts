import type { Db } from 'mongodb';

import { getLogger } from '../logger/logger';
import { LoggerName } from '../logger/logger.types';
import { CreateWorkload, CreateWorkloadData } from './create-workload';
import { RemoveWorkload } from './remove-workload';
import { Workload } from './workload';
import { WorkloadId } from './workload-id';
import { WorkloadError, WorkloadErrorCode } from './workload.errors';

const log = getLogger(LoggerName.WORKLOAD_REPOSITORY);

/**
 * Manages lifecycles of {@link Workload} instances (create, find, and remove).
 */
export class WorkloadRepositoryService {
  constructor(private database: Db) {}

  /**
   * Creates new {@link Workload}.
   */
  async createWorkload(createWorkloadDataPromise: Promise<CreateWorkloadData>): Promise<Workload> {
    const createWorkload = new CreateWorkload(createWorkloadDataPromise, this.database);
    try {
      await createWorkload.execute();
      const workload = new Workload(createWorkload.workloadId, this.database);
      return workload;
    } catch (error) {
      await new RemoveWorkload(createWorkload.workloadId, this.database).execute();
      throw error;
    }
  }

  /**
   * Returns {@link Workload} instance by workloadId.
   */
  async getWorkloadById(workloadId: string): Promise<Workload> {
    const workloadOptional = await this.findWorkloadById(workloadId);
    if (!workloadOptional) {
      log.error('Workload does not exist', { workloadId });
      throw new WorkloadError(WorkloadErrorCode.WORKLOAD_DOESNT_EXIST, `Workload does not exist`, workloadId);
    }

    return workloadOptional;
  }

  /**
   * Removes {@link Workload} instance by workloadId.
   */
  async removeWorkloadById(workloadId: string): Promise<void> {
    const removeWorkload = new RemoveWorkload(new WorkloadId(workloadId), this.database);
    return removeWorkload.execute();
  }

  /**
   * Finds {@link Workload} instance by workloadId.
   */
  private async findWorkloadById(workloadId: string): Promise<Workload | undefined> {
    const workload = new Workload(new WorkloadId(workloadId), this.database);
    const workloadExist = await workload.hasDataCollection();
    if (!workloadExist) {
      return;
    }
    return workload;
  }
}
