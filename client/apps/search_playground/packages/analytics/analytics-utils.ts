/**
 * @visibleForTesting
 */
export const getHeliotrope = async () => {
  /**
   * We cannot import 'heliotrope' at a top level as the library depends on the browser's "window"
   * object that is absent during server-side rendering. As result, library will be loaded
   * dynamically only on the client.
   * https://nextjs.org/docs/pages/building-your-application/optimizing/lazy-loading#with-external-libraries
   */
  const heliotrope = await import('@mongodb-js/heliotrope');
  return {
    heliotrope: heliotrope.default,
    ENVIRONMENT: heliotrope.ENVIRONMENT,
  };
};
