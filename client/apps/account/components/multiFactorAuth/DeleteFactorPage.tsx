import { useState } from 'react';

import Banner from '@leafygreen-ui/banner';

import { FactorType } from '@packages/types/accountMultiFactorAuth';

import * as api from '@packages/common/services/api';
import { bannerStyles } from '@packages/common/styles/multiFactorAuth';
import { factorMetadata, getUniqueFactors } from '@packages/common/utils/multiFactorAuthUtils';
import Loader from '@packages/components/Loader';
import VerifyFactorCard from '@packages/components/MultiFactorAuth/VerifyFactorCard';
import SplashViewGraphic from '@packages/components/SplashViewGraphic';
import useMfaFactors from '@packages/hooks/useMfaFactors';

import FactorSetupPageLayout from '@apps/account/components/multiFactorAuth/FactorSetupPageLayout';
// common styles
import { LoadingContainer } from '@apps/account/styles/common';
import { accountErrorCodeToMessage } from '@apps/account/utils/AccountErrorHelpers';

interface DeleteFactorPageProps {
  windowLocation?: Pick<Location, 'assign'>;
  match: {
    params: {
      factorId: string;
    };
  };
}

export default function DeleteFactorPage({
  windowLocation = window.location,
  match: {
    params: { factorId },
  },
}: DeleteFactorPageProps) {
  const { factors, isLoading, userFactorsLoadError } = useMfaFactors();
  const [deletionError, setDeletionError] = useState('');

  const onFactorVerificationSuccess = async () => {
    try {
      await api.accountMultiFactorAuth.deleteFactor({ factorId });
      windowLocation.assign('/account/profile/security');
    } catch (error) {
      console.log('An error occured during factor deletion', error);
      setDeletionError(accountErrorCodeToMessage(error.errorCode));
    }
  };

  const loadingComponent = () => {
    if (!isLoading) {
      return null;
    }

    return (
      <LoadingContainer>
        <SplashViewGraphic isLoading />
      </LoadingContainer>
    );
  };

  const userHasNoFactors = !isLoading && !userFactorsLoadError && factors.length === 0;

  const isValidFactor = () => {
    if (factors.length === 0) {
      return true;
    }

    return factors.filter((factor) => factor.id === factorId).length > 0;
  };

  const getPageHeader = () => {
    if (factors.length === 0) {
      return 'Delete Multi-Factor Authentication (MFA)';
    }
    const factorType = factors.find((factor) => factor.id === factorId)?.factorType;
    const { setupName } = factorMetadata[factorType as FactorType];
    return `Delete ${setupName}`;
  };

  const errorMessage = userFactorsLoadError ? accountErrorCodeToMessage(userFactorsLoadError) : deletionError;

  return (
    <Loader load={() => {}} isLoading={isLoading} loadingComponent={loadingComponent} renderWhenLoaded>
      {userHasNoFactors || !isValidFactor() ? (
        // If a user incorrectly navigates to this page with no factors or an invalid factor,
        // just send them back to the security page
        windowLocation.assign('/account/profile/security')
      ) : (
        <FactorSetupPageLayout pageHeader={getPageHeader()} pageHref="">
          {errorMessage && (
            <Banner variant="danger" data-testid="error-banner" css={bannerStyles}>
              {errorMessage}
            </Banner>
          )}
          {factors.length > 0 && (
            <VerifyFactorCard availableFactors={getUniqueFactors(factors)} onSuccess={onFactorVerificationSuccess} />
          )}
        </FactorSetupPageLayout>
      )}
    </Loader>
  );
}
