import { css } from '@emotion/react';
import styled from '@emotion/styled';
import Card from '@leafygreen-ui/card';
import { palette } from '@leafygreen-ui/palette';

import { StyledCard as StyledCardType } from '@packages/types/leafygreen-emotion';

import { mq } from '@packages/common/styles/utils/mediaQueries';
import { commonLinkStyles } from '@packages/components/styles/common';

const accountCardWidth = 450;

export const AccountContainer = styled.section<{ centered: boolean }>(({ centered }) => ({
  display: 'flex',
  width: '100%',
  marginBottom: 30,
  flexDirection: 'column',
  alignItems: centered ? 'center' : 'flex-start',
  justifyContent: 'center',
}));

const commonCardStyles = css({
  display: 'flex',
  flexDirection: 'column',
  width: accountCardWidth,
  maxWidth: '90vw',
  margin: '16px 0',
});

// NOTE: This is explicitly typed as "any" because @leafygreen-ui/card is missing an "export"
// needed for type declaration. (They need to export the "CardProps" interface.)
// When Visual Brand epic is complete, this "export" should be added
// into the LG module and this "any" can be removed
// TODO: https://jira.mongodb.org/browse/CLOUDP-116541
export const AccountCard: any = styled<StyledCardType>(Card)(
  {
    alignItems: 'stretch',
    justifyContent: 'flex-start',
  },
  commonCardStyles
);

export const AccountSubcontainer = styled.div(
  mq({
    maxWidth: '90vw',
    width: accountCardWidth,
    textAlign: ['center', 'center', 'left', 'left'],
  })
);

export const AccountHeader = styled.h3({
  fontWeight: 'bold',
  color: palette.gray.dark3,
  marginTop: '40px',
});

export const AccountSubheader = styled.div({
  fontSize: '14px',
  fontWeight: 'bold',
});

export const RightLink = styled.a(
  {
    float: 'right',
    fontWeight: 'normal',
    marginTop: 10,
  },
  commonLinkStyles({ color: palette.blue.base as string })
);

export const InformativeText = styled.div`
  box-sizing: border-box;
  border: 1px solid ${palette.blue.light2};
  border-radius: 2px;
  margin-top: 16px;
  background-color: ${palette.blue.light3};
  padding: 16px;
  color: ${palette.blue.dark2};
  line-height: 18px;
`;

export const footerButtonStyles = css({ marginTop: 30, alignSelf: 'flex-end' });

export const saveButtonStyles = css({ marginTop: 30, alignSelf: 'flex-end' });

export const cancelButtonStyles = css({ marginTop: 30, alignSelf: 'flex-start', width: '112px' });

export const accountAlertStyles = css({
  fontSize: 12,
  textAlign: 'center',
  borderRadius: 3,
  marginTop: 24,
  width: 450,
  maxWidth: '90vw',
});

export const accountInfoTextStyles = css({
  width: 450,
});

export const bannerStyles = css(accountAlertStyles, {
  textAlign: 'left',
});

export const LoadingContainer = styled.div({
  height: '100vh',
  marginTop: '-60px',
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});
