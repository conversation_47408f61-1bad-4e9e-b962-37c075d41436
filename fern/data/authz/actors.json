[{"service": "authz", "db": "authz", "collection": "actors", "scenarios": ["mms-init-local", "e2e-local"], "primaryKeyFields": ["_id"], "data": [{"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "<EMAIL>"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": []}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mmstestatlasadmin"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_ATLAS_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}, {"policyId": "GLOBAL_OWNER", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "<EMAIL>"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GROUP_OWNER", "scope": {"service": "cloud", "type": "project", "id": "571c390a7cea7e4dbf32b625"}}, {"policyId": "GROUP_OWNER", "scope": {"service": "cloud", "type": "project", "id": "587fcf0d6d47d656616a4da6"}}, {"policyId": "ORG_OWNER", "scope": {"service": "cloud", "type": "organization", "id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.orgs"}, "field": "_id", "criteria": {"name": "<PERSON><PERSON>"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": {"$env": {"lookup": "CORP_USERNAME", "mutators": [{"type": "split-by", "data": {"sequence": "@", "element-at": 0}}]}}}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_ATLAS_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}, {"policyId": "GLOBAL_OWNER", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "<PERSON><PERSON><PERSON><PERSON>"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_METERING_USER", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mongohouse-mms-user"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_ATLAS_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}, {"policyId": "GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}, {"policyId": "GLOBAL_OWNER", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_mms_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_METERING_USER", "scope": {"service": "cloud", "type": "global", "id": "1"}}, {"policyId": "GLOBAL_READ_ONLY", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_event_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_READ_ONLY", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_comment_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_READ_ONLY", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_authz_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_EVENT_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_authn_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_USER_READ_ONLY", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_cukes_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_READ_ONLY", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_configservice_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_EVENT_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}, {"_id": {"$ref": {"ns": {"db": "mmsdbconfig", "collection": "config.users"}, "field": "_id", "criteria": {"un": "mdb_ic_id_paymentsservice_local"}, "mutators": [{"type": "cast-to-type", "data": {"from": "objectid", "to": "string"}}]}}, "policyAssignments": [{"policyId": "GLOBAL_BILLING_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}, {"policyId": "GLOBAL_EVENT_ADMIN", "scope": {"service": "cloud", "type": "global", "id": "1"}}]}]}]