package configservicesdk

import (
	"bufio"
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"math"
	"math/big"
	"math/rand"
	"os"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/aws/retry"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"

	"github.com/10gen/mms/go/configservicesdk/configservicegrpc"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gopkg.in/yaml.v3"
)

const (
	LocalFileNamespace        string = "localFile"
	localConfigServiceVersion string = "config-service-go-sdk-v0.0.0"
	configKeySeparator        string = ":"
	// polling interval to send lastRequestedAtMap to UpdateFeatureFlagMetadataRequest
	featureFlagMetadataPollingIntervalSec uint   = 60
	jsonFileExtension                     string = ".json"
	yamlFileExtension                     string = ".yaml"
	ymlFileExtension string = ".yml"
	propertiesFileExtension               string = ".properties"
	AwsAccessKeyConfigName                string = "local.aws.accessKey"
	AwsSecretKeyConfigName                string = "local.aws.secretKey"
	AwsSecretRegionConfigName             string = "local.aws.secretsRegion"
	AwsDefaultSecretRegion                string = "us-east-1"
	// AwsRetryCount number from AwsManagedSecrets.java
	AwsRetryCount int = 8
)

// module scoped variable to track if the update feature flag metadata request is in-flight.
// doesn't need to be thread safe since only one go coroutine will be modifying it at a time.
var issuingUpdateFeatureFlagMetadataRequest = false

// ConfigServiceRequestOptions is used to pass additional options to the ConfigServiceClient getter methods.
type ConfigServiceRequestOptions struct {
	// EntityId is an ObjectId used to evaluate feature flag
	EntityId *primitive.ObjectID
	// DefaultValue is the value returned by a getter method if a configKey is not found
	DefaultValue interface{}
}

// ConfigServiceClient is the main client for interacting with the Config Service.
type ConfigServiceClient struct {
	Options          ConfigServiceClientOptions
	MonitoringClient ConfigServiceMonitoringClient
	LoggingClient    ConfigServiceLoggingClient

	isStarted                             bool
	authnTokenInserter                    *authnTokenInserter
	localStore                            map[string]*configservicegrpc.Config
	localStoreLock                        sync.RWMutex
	localSecretStore                      map[string]*configservicegrpc.Config
	localSecretStoreLock                  sync.RWMutex
	featureFlagLastRequestedAtMap         map[string]*timestamppb.Timestamp
	featureFlagMetadataPollingIntervalSec uint
}

type ConfigServiceSecretsManagerClient interface {
	GetSecretValue(ctx context.Context, params *secretsmanager.GetSecretValueInput, optFns ...func(*secretsmanager.Options)) (*secretsmanager.GetSecretValueOutput, error)
}

// Feature Flag-related constants
type FeatureFlagInfo struct {
	ID    string
	Name  string
	Scope configservicegrpc.Scope
	Phase configservicegrpc.Phase
}

type DecisiveField string

const (
	FeatureFlagDecisiveFieldPhase             DecisiveField = "phase"
	FeatureFlagDecisiveFieldBlockList         DecisiveField = "blockList"
	FeatureFlagDecisiveFieldAllowList         DecisiveField = "allowList"
	FeatureFlagDecisiveFieldRolloutPercentage DecisiveField = "rolloutPercentage"
	FeatureFlagDecisiveFieldDefaultValue      DecisiveField = "defaultValue"
	// FeatureFlagDecisiveFieldNone : If error occurs during the evaluation of a feature flag, we may
	//still want to return false as the result, in this case the decisive field will be set to "none"
	FeatureFlagDecisiveFieldNone DecisiveField = "none"
)

type FeatureFlagEvaluationData struct {
	EvaluationResult bool
	DecisiveField    DecisiveField
	Phase            configservicegrpc.Phase
}

// maxCrc32HashValue is the pre-calculated value of math.Pow(2, 32) - 1
const maxCrc32HashValue = 4294967295

// NewConfigServiceClient creates a new ConfigServiceClient instance with the provided options.
//
// Parameters:
//   - options (ConfigServiceClientOptions): The configuration options for the ConfigServiceClient.
//
// Returns:
//   - *ConfigServiceClient: A new instance of the ConfigServiceClient.
//   - error: Any error that occurred during the creation of the client.
func NewConfigServiceClient(options ConfigServiceClientOptions) (*ConfigServiceClient, error) {
	validationErr := validateRequiredParameters(options)
	if validationErr != nil {
		return nil, validationErr
	}

	updatedOptions, updatedOptionsErr := cloneAndApplyBounds(options)
	if updatedOptionsErr != nil {
		return nil, updatedOptionsErr
	}

	postProcessingValidationErr := postProcessValidateOptions(updatedOptions)
	if postProcessingValidationErr != nil {
		return nil, postProcessingValidationErr
	}

	registerInternalCountersError := registerInternalMetrics(updatedOptions.MonitoringClient)

	if registerInternalCountersError != nil {
		return nil, registerInternalCountersError
	}

	var localSecretStore map[string]*configservicegrpc.Config

	// If the optional SecretStoreType isn't set, then don't retrieve secrets by setting localSecretStore to nil.
	if updatedOptions.SecretStoreType != nil {
		localSecretStore = map[string]*configservicegrpc.Config{}
	}
	validateSecretStoreOptionsErr := validateSecretStoreOptions(updatedOptions)
	if validateSecretStoreOptionsErr != nil {
		return nil, validateSecretStoreOptionsErr
	}

	return &ConfigServiceClient{
		Options:                               updatedOptions,
		MonitoringClient:                      updatedOptions.MonitoringClient,
		LoggingClient:                         updatedOptions.LoggingClient,
		isStarted:                             false,
		authnTokenInserter:                    NewAuthnTokenInserter(options),
		localStore:                            map[string]*configservicegrpc.Config{},
		localSecretStore:                      localSecretStore,
		featureFlagLastRequestedAtMap:         map[string]*timestamppb.Timestamp{},
		featureFlagMetadataPollingIntervalSec: featureFlagMetadataPollingIntervalSec,
	}, nil
}

// Start is a blocking method that starts up the main poller that refreshes the SDK with latest configs
// from Config Service. However, the poller will only start once the current configs have been
// successfully pulled into the local store at least once. Lastly, another poller is started asynchronously
// that sends feature flag usage data to config service. Note that this method is not thread safe and
// should only be called once from a single SDK instance.
func (c *ConfigServiceClient) Start(ctx context.Context) error {
	if c.isStarted {
		return fmt.Errorf("config service sdk already started")
	}

	defer func() {
		c.isStarted = false
	}()

	c.isStarted = true

	c.startTimer(StartDurationCounterName)

	propertiesErr := c.populateLocalStoreWithApplicationProperties()
	if propertiesErr != nil {
		c.stopTimer(StartDurationCounterName)
		return propertiesErr
	}

	secretsErr := c.populateSecretStore()
	if secretsErr != nil {
		c.stopTimer(StartDurationCounterName)
		c.incrementCounter(StartPopulateSecretStoreErrorsCounterName)
		c.LoggingClient.Error(fmt.Sprintf("Error making initial call to populateSecretStore. Error: %s", secretsErr), localConfigServiceVersion, c.Options.ServiceName)
		return secretsErr
	}

	populateErr := c.PopulateLocalStore()
	for populateErr != nil {
		// log, increment counter, and keep trying until we don't get an error
		c.LoggingClient.Error(fmt.Sprintf("Error making initial call to PopulateLocalStore, logging and retrying. Error: %s", populateErr), localConfigServiceVersion, c.Options.ServiceName)
		c.incrementCounter(StartPopulateLocalStoreErrorsCounterName)
		populateErr = c.PopulateLocalStore()
	}

	c.stopTimer(StartDurationCounterName)

	ffMetadataPollerDuration := time.Second * time.Duration(c.featureFlagMetadataPollingIntervalSec)
	ffMetadataPollerTicker := time.NewTicker(ffMetadataPollerDuration)

	go c.startSendFeatureFlagMetadataPoller(ctx, ffMetadataPollerTicker)

	mainPollerDuration := time.Second * time.Duration(*c.Options.PollingIntervalSec)
	mainPollerTicker := time.NewTicker(mainPollerDuration)

	defer func() {
		mainPollerTicker.Stop()
		ffMetadataPollerTicker.Stop()
	}()

	return c.startPopulateLocalStorePoller(ctx, mainPollerTicker)
}

// PopulateLocalStore synchronously calls the config service and replaces in-memory structure with retrieved config
func (c *ConfigServiceClient) PopulateLocalStore() error {
	var responseErr error
	var response *configservicegrpc.GetConfigsResponse

	c.startTimer(PopulateLocalStoreDurationCounterName)
	defer c.stopTimer(PopulateLocalStoreDurationCounterName)

	for i := uint(0); i < *c.Options.RequestRetryCount; i++ {
		getConfigsResponse, getConfigsResponseError := c.issueGetConfigs()

		if getConfigsResponseError == nil {
			response = getConfigsResponse
			responseErr = nil
			break
		} else {
			responseErr = getConfigsResponseError
		}
	}

	if responseErr != nil {
		errorStatus, ok := status.FromError(responseErr)
		if !ok {
			errorStatus = status.New(codes.Unknown, "An unknown error occurred")
		}
		c.incrementCounter(GetConfigsErrorsCounterName, errorStatus.Code().String())
		return responseErr
	}

	configs := response.GetConfigs()
	postProcessedConfigs, postProcessingErr := c.postProcessConfigServiceData(configs)
	if postProcessingErr != nil {
		return postProcessingErr
	}

	localFileConfigs := map[string]*configservicegrpc.Config{}

	c.localStoreLock.Lock()
	defer c.localStoreLock.Unlock()

	for k, v := range c.localStore {
		if strings.HasPrefix(k, LocalFileNamespace+configKeySeparator) {
			localFileConfigs[k] = v
		}
	}

	clear(c.localStore)

	for k, v := range localFileConfigs {
		c.localStore[k] = v
	}

	for k, v := range postProcessedConfigs {
		c.localStore[k] = v
	}

	return nil
}

// Bool retrieves the boolean value of the configuration with the specified namespace and name.
//
// If the configuration value is not a boolean, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - bool: The boolean value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) Bool(namespace, configName string) (bool, error) {
	return c.BoolWithOptions(namespace, configName, nil)
}

// BoolWithOptions retrieves the boolean value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.EntityId will be used to evaluate the feature flag.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - bool: The boolean value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) BoolWithOptions(namespace, configName string, options *ConfigServiceRequestOptions) (bool, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)
	if configError != nil {
		return false, configError
	}

	valueAsBool, valueIsBool := value.(bool)
	if valueIsBool {
		return valueAsBool, nil
	}

	valueAsString, valueIsString := value.(string)
	if !valueIsString {
		return false, getTypeRepresentationError(configName, value)
	}

	typedValue, typedValueErr := strconv.ParseBool(valueAsString)
	if typedValueErr != nil {
		return false, getTypeError(configName, "bool")
	}
	return typedValue, nil
}

// Date retrieves the time.Time value of the configuration with the specified namespace and name.
//
// If the configuration value is not a valid time.Time, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - time.Time: The time.Time value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) Date(namespace, configName string) (time.Time, error) {
	return c.DateWithOptions(namespace, configName, nil)
}

// DateWithOptions retrieves the time.Time value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.DefaultValue will be returned, if the specified key is not found.
//
// This method will attempt to parse the string value into a time.Time object using the RFC3339
// layout: "2006-01-02T15:04:05Z".
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - time.Time: The time.Time value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) DateWithOptions(namespace, configName string, options *ConfigServiceRequestOptions) (time.Time, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)

	if configError != nil {
		return time.Now(), configError
	}

	valueAsTime, valueIsTime := value.(time.Time)
	if valueIsTime {
		return valueAsTime, nil
	}

	valueAsString, valueIsString := value.(string)
	if !valueIsString {
		return time.Now(), getTypeRepresentationError(configName, value)
	}

	typedValue, typedValueErr := time.Parse(time.RFC3339, valueAsString)
	if typedValueErr != nil {
		return time.Now(), getTypeError(configName, "date")
	}

	return typedValue, nil
}

// Double retrieves the float64 value of the configuration with the specified namespace and name.
//
// If the configuration value is not a valid float64, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - float64: The float64 value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) Double(namespace, configName string) (float64, error) {
	return c.DoubleWithOptions(namespace, configName, nil)
}

// DoubleWithOptions retrieves the float64 value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.DefaultValue will be returned, if the specified key is not found.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - float64: The float64 value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) DoubleWithOptions(namespace, configName string, options *ConfigServiceRequestOptions) (float64, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)

	if configError != nil {
		return float64(0), configError
	}

	valueAsFloat, valueIsFloat := value.(float64)
	if valueIsFloat {
		return valueAsFloat, nil
	}

	valueAsString, valueIsString := value.(string)
	if !valueIsString {
		return float64(0), getTypeRepresentationError(configName, value)
	}

	parsedAsBigFloat, parsedIsBigFloat := new(big.Float).SetString(valueAsString)
	if parsedIsBigFloat {
		parsedAsFloat, _ := parsedAsBigFloat.Float64()
		if canNumberBeRepresented(parsedAsFloat) {
			return parsedAsFloat, nil
		}
	}
	return float64(0), getTypeError(configName, "float/double")
}

// Int retrieves the integer value of the configuration with the specified namespace and name.
//
// This method supports configuration values of type int, int32, and int64. If the configuration
// value is not a valid integer type, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - int: The integer value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) Int(namespace, configName string) (int, error) {
	return c.IntWithOptions(namespace, configName, nil)
}

// IntWithOptions retrieves the integer value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.DefaultValue will be returned, if the specified key is not found.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - int: The integer value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) IntWithOptions(namespace, configName string, options *ConfigServiceRequestOptions) (int, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)

	if configError != nil {
		return 0, configError
	}

	valueAsInt, valueIsInt := checkIfIsAnyIntType(value)
	if valueIsInt {
		return valueAsInt, nil
	}

	valueAsString, valueIsString := value.(string)
	if !valueIsString {
		return 0, getTypeRepresentationError(configName, value)
	}

	parsedAsBigFloat, parsedIsBigFloat := new(big.Float).SetString(valueAsString)
	if parsedIsBigFloat {
		parsedAsFloat, _ := parsedAsBigFloat.Float64()
		if canNumberBeRepresented(parsedAsFloat) {
			return int(math.Floor(parsedAsFloat)), nil
		}
	}
	return 0, getTypeError(configName, "int")
}

// List retrieves the list value of the configuration with the specified namespace and name.
//
// The returned list is of type []interface{}, which means the elements can be of any type.
// If the configuration value is not a valid list, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - []interface{}: The list value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) List(namespace, configName string) ([]interface{}, error) {
	return c.ListWithOptions(namespace, configName, nil)
}

// ListWithOptions retrieves the list value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.DefaultValue will be returned, if the specified key is not found.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - []interface{}: The list value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) ListWithOptions(namespace string, configName string, options *ConfigServiceRequestOptions) ([]interface{}, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)

	if configError != nil {
		return []interface{}{}, configError
	}

	valueAsSlice, valueIsSlice := value.([]interface{})
	if valueIsSlice {
		return valueAsSlice, nil
	}

	valueAsString, valueIsString := value.(string)
	if !valueIsString {
		return []interface{}{}, getTypeRepresentationError(configName, value)
	}

	var typedValue []interface{}
	unmarshallErr := json.Unmarshal([]byte(valueAsString), &typedValue)
	if unmarshallErr != nil {
		if len(valueAsString) == 0 {
			return []interface{}{}, getTypeError(configName, "array")
		}

		toBeReturned := []interface{}{}
		splitByComma := strings.Split(valueAsString, ",")
		for _, s := range splitByComma {
			toBeReturned = append(toBeReturned, strings.TrimSpace(s))
		}
		return toBeReturned, nil
	}
	return typedValue, nil
}

// Long retrieves the int64 value of the configuration with the specified namespace and name.
//
// This method supports configuration values of type int, int32, and int64. If the configuration
// value is not a valid integer type, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - int64: The int64 value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) Long(namespace, configName string) (int64, error) {
	return c.LongWithOptions(namespace, configName, nil)
}

// LongWithOptions retrieves the int64 value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.DefaultValue will be returned, if the specified key is not found.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - int64: The int64 value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) LongWithOptions(namespace, configName string, options *ConfigServiceRequestOptions) (int64, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)
	if configError != nil {
		return 0, configError
	}

	valueAsLong, valueIsLong := checkIfIsAnyInt64Type(value)
	if valueIsLong {
		return valueAsLong, nil
	}

	valueAsString, valueIsString := value.(string)
	if !valueIsString {
		return 0, getTypeRepresentationError(configName, value)
	}

	parsedAsBigFloat, parsedIsBigFloat := new(big.Float).SetString(valueAsString)
	if parsedIsBigFloat {
		parsedAsFloat, _ := parsedAsBigFloat.Float64()
		if canNumberBeRepresented(parsedAsFloat) {
			return int64(math.Floor(parsedAsFloat)), nil
		}
	}
	return 0, getTypeError(configName, "long")
}

// Secret retrieves the Secret value of the configuration with the specified name.
//
// If the specified name does not correspond to an existing Secret entry, this method will return an error.
// If the configuration value does not start with (secret), this method will return an error.
//
// Parameters:
//   - name (string): The name of the configuration key to retrieve.
//
// Returns:
//   - Secret: The represented secret from configuration.
//   - error: Any error that occurred while retrieving or parsing the secret value.
func (c *ConfigServiceClient) Secret(name string) (Secret, error) {
	propertyEntry, propertyEntryErr := c.String(LocalFileNamespace, name)
	if propertyEntryErr != nil {
		return Secret{}, propertyEntryErr
	}

	if !strings.HasPrefix(propertyEntry, SecretPrefix) {
		return Secret{}, fmt.Errorf("config with name: '%s' does not correspond to a secret defined in local property files", name)
	}

	secretName := strings.TrimLeft(propertyEntry, SecretPrefix)
	if len(secretName) == 0 {
		return Secret{}, fmt.Errorf("config with name: '%s' contains empty secret identifier", name)
	}

	secretKey := getKey(LocalFileNamespace, name)

	c.localSecretStoreLock.RLock()
	defer c.localSecretStoreLock.RUnlock()

	secretEntry, isSecretPresent := c.localSecretStore[secretKey]
	if !isSecretPresent {
		return Secret{}, fmt.Errorf("failed to find corresponding secret entry for config with name: '%s'", name)
	}

	secretValue, secretValueErr := c.resolveApplicationPropertyModule(secretEntry)
	if secretValueErr != nil {
		return Secret{}, secretValueErr
	}

	return Secret{value: secretValue}, nil
}

// String retrieves the string value of the configuration with the specified namespace and name.
//
// If the configuration value is not a valid string, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//
// Returns:
//   - string: The string value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) String(namespace, configName string) (string, error) {
	return c.StringWithOptions(namespace, configName, nil)
}

// StringWithOptions retrieves the string value of the configuration with the specified namespace and name.
//
// If ConfigServiceRequestOptions is provided, it will be used in the evaluation.
//
//	e.g. ConfigServiceRequestOptions.DefaultValue will be returned, if the specified key is not found.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - configName (string): The name of the configuration to retrieve.
//   - options (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration.
//
// Returns:
//   - string: The string value of the configuration.
//   - error: Any error that occurred while retrieving or parsing the configuration value.
func (c *ConfigServiceClient) StringWithOptions(namespace, configName string, options *ConfigServiceRequestOptions) (string, error) {
	value, configError := c.getResolvedConfigValueWithOptions(namespace, configName, options)

	if configError != nil {
		return "", configError
	}

	typedValue, ok := value.(string)

	if !ok {
		return typedValue, getTypeError(configName, "string")
	}

	return typedValue, nil
}

// Feature Flag-specific getters

// GetFeatureFlagInfo retrieves the feature flag metadata if the feature flag exists
//
// If the feature flag is not found, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - featureFlagName (string): The name of the feature flag to retrieve.
//
// Returns:
//   - *featureFlagInfo: Pointer reference to a struct containing feature flag info.
//   - error: Any error that occurred while retrieving or processing the feature flag.
func (c *ConfigServiceClient) GetFeatureFlagInfo(namespace string, featureFlagName string) (*FeatureFlagInfo, error) {
	value, configError := c.getConfigValueFromLocalStoreOrError(namespace, featureFlagName)

	if configError != nil {
		return nil, configError
	}

	configModule := value.GetModule().String()
	if configModule != configservicegrpc.ConfigModule.String(configservicegrpc.ConfigModule_CONFIG_MODULE_FEATURE_FLAG) {
		return nil, fmt.Errorf("ConfigServiceClient->GetFeatureFlagInfo->config data with unexpected module: %s", configModule)
	}

	data := value.GetData()
	featureFlagData := &configservicegrpc.FeatureFlagConfigData{}

	if err := data.UnmarshalTo(featureFlagData); err != nil {
		return nil, err
	}

	return &FeatureFlagInfo{
		ID:    value.GetId(),
		Name:  featureFlagName,
		Scope: featureFlagData.GetScope(),
		Phase: featureFlagData.GetPhase(),
	}, nil
}

// GetFeatureFlagEvaluationData returns the evaluated boolean value for a feature flag,
// along with info about how it was determined.
//
// If the feature flag is not found, this method will return an error.
//
// Parameters:
//   - namespace (string): The namespace of the configuration to retrieve.
//   - featureFlagName (string): The name of the feature flag to retrieve.
//   - requestOptions (ConfigServiceRequestOptions): The additional options to use when retrieving the configuration,
//     such as the EntityId. Can be nil.
//
// Returns:
//   - *featureFlagEvaluationData: Pointer reference to a struct containing feature flag evaluation data.
//   - error: Any error that occurred while retrieving or processing the feature flag.
func (c *ConfigServiceClient) GetFeatureFlagEvaluationData(namespace string, featureFlagName string, requestOptions *ConfigServiceRequestOptions) (*FeatureFlagEvaluationData, error) {
	config, configError := c.getConfigValueFromLocalStoreOrError(namespace, featureFlagName)

	// if no config is found
	if configError != nil {
		if requestOptions != nil && requestOptions.DefaultValue != nil {
			defaultValue, ok := requestOptions.DefaultValue.(bool)
			if ok {
				return &FeatureFlagEvaluationData{
					EvaluationResult: defaultValue,
					DecisiveField:    FeatureFlagDecisiveFieldDefaultValue,
					Phase:            configservicegrpc.Phase_PHASE_UNSPECIFIED,
				}, nil
			} else {
				return nil, configError
			}
		}
	}

	evaluationResult, resolveError := c.resolveFeatureFlagConfigModule(config, requestOptions)

	if resolveError != nil {
		return nil, configError
	}

	return evaluationResult, nil
}

// below here are private functions

// api call functions

func (c *ConfigServiceClient) getConfigServiceConnection(ctx context.Context) (*grpc.ClientConn, error) {
	options := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithPerRPCCredentials(c.authnTokenInserter),
	}
	return grpc.DialContext(ctx, c.Options.BaseURL, options...)
}

func (c *ConfigServiceClient) getConfigServiceHandle(ctx context.Context) (configservicegrpc.ConfigServiceClient, error) {
	if c.Options.useMockServiceClient {
		fmt.Println("using mock config service client")
		return c.Options.mockConfigServiceClient, nil
	}

	conn, connErr := c.getConfigServiceConnection(ctx)
	if connErr != nil {
		return nil, connErr
	}

	client := configservicegrpc.NewConfigServiceClient(conn)
	return client, nil
}

func (c *ConfigServiceClient) issueGetConfigs() (*configservicegrpc.GetConfigsResponse, error) {
	ctxWithTimeout, ctxCancel := context.WithTimeout(context.Background(), time.Second*time.Duration(*c.Options.RequestTimeoutSec))
	defer ctxCancel()

	configClient, configClientErr := c.getConfigServiceHandle(ctxWithTimeout)
	if configClientErr != nil {
		return nil, configClientErr
	}

	request := &configservicegrpc.GetConfigsRequest{
		SdkVersion:  localConfigServiceVersion,
		ServiceName: c.Options.ServiceName,
		Namespaces:  c.Options.Namespaces,
	}

	return configClient.GetConfigs(ctxWithTimeout, request)
}

func (c *ConfigServiceClient) issueUpdateFeatureFlagMetadata() error {
	ctxWithTimeout, ctxCancel := context.WithTimeout(context.Background(), time.Second*time.Duration(*c.Options.RequestTimeoutSec))
	defer ctxCancel()

	configClient, configClientErr := c.getConfigServiceHandle(ctxWithTimeout)
	if configClientErr != nil {
		return configClientErr
	}

	request := &configservicegrpc.UpdateFeatureFlagMetadataRequest{
		SdkVersion:       localConfigServiceVersion,
		ServiceName:      c.Options.ServiceName,
		LastRequestedMap: c.featureFlagLastRequestedAtMap,
	}

	_, updateFeatureFlagMetadataErr := configClient.UpdateFeatureFlagMetadata(ctxWithTimeout, request)
	return updateFeatureFlagMetadataErr
}

// poller functions

// startPopulateLocalStorePoller periodically calls PopulateLocalStore
func (c *ConfigServiceClient) startPopulateLocalStorePoller(ctx context.Context, ticker *time.Ticker) error {
	for {
		select {
		case <-ctx.Done():
			// context says we're done
			c.LoggingClient.Info("context Done() fired => terminating startPopulateLocalStorePoller() and resetting sdk state", localConfigServiceVersion, c.Options.ServiceName)
			return nil
		case <-ticker.C:
			// ticker fired => call PopulateLocalStore()
			populateErr := c.PopulateLocalStore()
			if populateErr != nil {
				c.LoggingClient.Error(fmt.Sprintf("encountered error calling PopulateLocalStore: %s", populateErr), localConfigServiceVersion, c.Options.ServiceName)
				return populateErr
			}
		}
	}
}

func (c *ConfigServiceClient) sendFeatureFlagMetadata() {
	defer func() {
		if r := recover(); r != nil {
			c.LoggingClient.Error(fmt.Sprintf("sendFeatureFlagMetadata() recovered from panic: %v", r), localConfigServiceVersion, c.Options.ServiceName)
		}
	}()

	// only attempt to send metadata if the last requested at map is not empty and there is no in-flight request
	if len(c.featureFlagLastRequestedAtMap) != 0 && !issuingUpdateFeatureFlagMetadataRequest {
		issuingUpdateFeatureFlagMetadataRequest = true
		c.LoggingClient.Debug(fmt.Sprintf("issuing update feature flag metadata request with %#v", c.featureFlagLastRequestedAtMap), localConfigServiceVersion, c.Options.ServiceName)
		if responseErr := c.issueUpdateFeatureFlagMetadata(); responseErr == nil {
			c.clearFeatureFlagLastRequestedAtMap()
			c.LoggingClient.Debug(fmt.Sprintf("cleared feature flag metadata map %#v", c.featureFlagLastRequestedAtMap), localConfigServiceVersion, c.Options.ServiceName)
		} else {
			errorStatus, ok := status.FromError(responseErr)
			if !ok {
				errorStatus = status.New(codes.Unknown, "An unknown error occurred")
			}

			c.LoggingClient.Error(fmt.Sprintf("encountered error calling issueUpdateFeatureFlagMetadata: %s", responseErr), localConfigServiceVersion, c.Options.ServiceName)
			c.incrementCounter(UpdateFeatureFlagMetadataCounterName, errorStatus.Code().String())
		}
		issuingUpdateFeatureFlagMetadataRequest = false
	}
}

// startSendFeatureFlagMetadataPoller periodically sends the lastRequestedAtMap to the
// UpdateFeatureFlagMetadata grpc endpoint but only if the map is non-empty and then clears the map
func (c *ConfigServiceClient) startSendFeatureFlagMetadataPoller(ctx context.Context, ticker *time.Ticker) {
	defer func() {
		if r := recover(); r != nil {
			c.LoggingClient.Error(fmt.Sprintf("startSendFeatureFlagMetadataPoller() recovered from panic: %v", r), localConfigServiceVersion, c.Options.ServiceName)
		}
	}()

	for {
		select {
		case <-ctx.Done():
			c.LoggingClient.Info("context Done() fired => terminating startSendFeatureFlagMetadataPoller()", localConfigServiceVersion, c.Options.ServiceName)
			return
		case <-ticker.C:
			c.sendFeatureFlagMetadata()
		}
	}
}

// functions to process config data

// postProcessConfigServiceData is called after GetConfigs() to perform module-specific post-processing on returned config data
func (c *ConfigServiceClient) postProcessConfigServiceData(raw map[string]*configservicegrpc.Config) (map[string]*configservicegrpc.Config, error) {
	updated := map[string]*configservicegrpc.Config{}

	for key, rawConfigData := range raw {
		switch rawConfigData.Module {
		case configservicegrpc.ConfigModule_CONFIG_MODULE_DEMO_MODULE,
			configservicegrpc.ConfigModule_CONFIG_MODULE_APPLICATION_PROPERTY,
			configservicegrpc.ConfigModule_CONFIG_MODULE_FEATURE_FLAG:
			updated[key] = rawConfigData
		case configservicegrpc.ConfigModule_CONFIG_MODULE_UNSPECIFIED:
			// do not persist incomplete config data to local store
			c.LoggingClient.Error(fmt.Sprintf("module not specified for key: '%s'", key), localConfigServiceVersion, c.Options.ServiceName)
		default:
			// do not persist incomplete config data to local store
			c.LoggingClient.Error(fmt.Sprintf("module missing for key: '%s'", key), localConfigServiceVersion, c.Options.ServiceName)
		}
	}
	return updated, nil
}

// functions to look up config data

// used with the typed module-agnostic Getters
func (c *ConfigServiceClient) getResolvedConfigValue(namespace string, configName string) (interface{}, error) {
	return c.getResolvedConfigValueWithOptions(namespace, configName, nil)
}

// used with the typed module-agnostic Getters
func (c *ConfigServiceClient) getResolvedConfigValueWithOptions(namespace string, configName string, options *ConfigServiceRequestOptions) (interface{}, error) {
	validationError := c.validateRequest(namespace, configName)

	if validationError != nil {
		return nil, validationError
	}

	config, isConfigPresent := c.getConfigValueFromLocalStoreOrLocal(namespace, configName)

	if isConfigPresent {
		return c.postProcessResolvedKey(config, options)
	}

	if options != nil && options.DefaultValue != nil {
		return options.DefaultValue, nil
	}

	return nil, c.incrementConfigKeyMissesCounterAndError(getKey(namespace, configName))
}

// used with the typed module-agnostic Getters
func (c *ConfigServiceClient) postProcessResolvedKey(config *configservicegrpc.Config, options *ConfigServiceRequestOptions) (interface{}, error) {
	var configModule = config.GetModule().String()
	var data interface{}
	var resolveError error

	switch configModule {
	case configservicegrpc.ConfigModule.String(configservicegrpc.ConfigModule_CONFIG_MODULE_DEMO_MODULE):
		data, resolveError = c.resolveDemoConfigModule(config)
	case configservicegrpc.ConfigModule.String(configservicegrpc.ConfigModule_CONFIG_MODULE_FEATURE_FLAG):
		var evaluationResult *FeatureFlagEvaluationData
		evaluationResult, resolveError = c.resolveFeatureFlagConfigModule(config, options)
		data = evaluationResult.EvaluationResult
	case configservicegrpc.ConfigModule.String(configservicegrpc.ConfigModule_CONFIG_MODULE_APPLICATION_PROPERTY):
		data, resolveError = c.resolveApplicationPropertyModule(config)
	case configservicegrpc.ConfigModule.String(configservicegrpc.ConfigModule_CONFIG_MODULE_UNSPECIFIED):
	default:
		data = nil
		resolveError = fmt.Errorf("ConfigServiceClient->unknown or unspecified module")
	}

	return data, resolveError
}

// getConfigValueFromLocalStoreOrError returns the corresponding configservicegrpc.Config for
// the specified namespace + name, or returns an error (along with incrementing the ConfigKeyMisses
// Counter). this is intended to be used by feature flag-specific getters, where we do NOT want
// users unintentionally providing a feature flag through local configuration.
func (c *ConfigServiceClient) getConfigValueFromLocalStoreOrError(namespace, configName string) (*configservicegrpc.Config, error) {
	configKey := getKey(namespace, configName)

	c.localStoreLock.RLock()
	defer c.localStoreLock.RUnlock()

	config, isPresent := c.localStore[configKey]
	if !isPresent {
		return nil, c.incrementConfigKeyMissesCounterAndError(configKey)
	}
	return config, nil
}

// getConfigValueFromLocalStoreOrLocal returns a bool instead of an error, and not does increment
// the ConfigKeyMisses Counter. this is intended to be used by module-agnostic getter methods,
// where we want to check localStore and ConfigServiceRequestOptions for a defaultValue before we
// increment the ConfigKeyMisses Counter.
func (c *ConfigServiceClient) getConfigValueFromLocalStoreOrLocal(namespace, configName string) (*configservicegrpc.Config, bool) {
	configKey := getKey(namespace, configName)

	c.localStoreLock.RLock()
	defer c.localStoreLock.RUnlock()

	if config, isPresent := c.localStore[configKey]; isPresent {
		return config, true
	}

	localFileConfigKey := getKey(LocalFileNamespace, configName)
	if localFileConfig, isPresent := c.localStore[localFileConfigKey]; isPresent {
		return localFileConfig, true
	}

	return nil, false
}

// Secrets-specific methods
type ConfigServiceSecretMapping struct {
	Key   string
	Value string
}

func isSecret(value string) bool {
	return strings.HasPrefix(value, SecretPrefix)
}

func (c *ConfigServiceClient) getSecretFromAws(client ConfigServiceSecretsManagerClient, secretName string) (string, error) {
	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	}

	ctxWithTimeout, ctxCancel := context.WithTimeout(context.Background(), time.Second*time.Duration(*c.Options.RequestTimeoutSec))
	defer ctxCancel()

	result, err := client.GetSecretValue(ctxWithTimeout, input)
	if err != nil {
		return "", err
	}
	return aws.ToString(result.SecretString), nil
}

/**
 * Populates the secret store with data. Only runs if the user has provided a secret environment
 * for which to retrieve secret values from. Only runs on "start".
 */
func (c *ConfigServiceClient) populateSecretStore() error {
	if c.localSecretStore == nil {
		return nil
	}

	secretsToRetrieve, secretsToRetrieveErr := c.findSecretsToRetrieve()

	if secretsToRetrieveErr != nil {
		return secretsToRetrieveErr
	}

	c.localSecretStoreLock.Lock()
	defer c.localSecretStoreLock.Unlock()

	switch *c.Options.SecretStoreType {
	case AwsSecretsManager:
		secrets, err := c.getSecretsFromAwsSecretsManager(secretsToRetrieve)
		if err != nil {
			return err
		}
		for k, v := range secrets {
			c.localSecretStore[k] = v
		}
	case Helix:
		message := "helix integration not yet implemented"
		c.LoggingClient.Error(message, localConfigServiceVersion, c.Options.ServiceName)
		return errors.New(message)
	}

	c.LoggingClient.Info(fmt.Sprintf("Secret store successfully updated: %d secrets inserted from %v.", len(c.localSecretStore), *c.Options.SecretStoreType), localConfigServiceVersion, c.Options.ServiceName)
	return nil
}

func (c *ConfigServiceClient) findSecretsToRetrieve() ([]ConfigServiceSecretMapping, error) {
	var secretsToRetrieve []ConfigServiceSecretMapping

	c.localStoreLock.RLock()
	defer c.localStoreLock.RUnlock()

	for configKey, configObj := range c.localStore {
		data := configObj.Data
		appPropConfigDataValue := &configservicegrpc.ApplicationPropertyConfigData{}

		if err := data.UnmarshalTo(appPropConfigDataValue); err != nil {
			c.LoggingClient.Error("unable to unmarshal to ApplicationPropertyConfigData", localConfigServiceVersion, c.Options.ServiceName)
			return nil, err
		}

		value := appPropConfigDataValue.Value

		if isSecret(value) {
			secretName := strings.TrimPrefix(value, SecretPrefix)
			secretsToRetrieve = append(secretsToRetrieve, ConfigServiceSecretMapping{Key: configKey, Value: secretName})
		}
	}

	return secretsToRetrieve, nil
}

func (c *ConfigServiceClient) getSecretsFromAwsSecretsManager(secretsToRetrieve []ConfigServiceSecretMapping) (map[string]*configservicegrpc.Config, error) {
	awsRegions := c.getAwsRegions()
	awsAccess, awsAccessRetrievalError := c.String(LocalFileNamespace, AwsAccessKeyConfigName)
	awsSecret, awsSecretRetrievalError := c.String(LocalFileNamespace, AwsSecretKeyConfigName)

	var missingArgs []string

	if awsAccessRetrievalError != nil {
		missingArgs = append(missingArgs, AwsAccessKeyConfigName)
	}

	if awsSecretRetrievalError != nil {
		missingArgs = append(missingArgs, AwsSecretKeyConfigName)
	}

	if len(missingArgs) > 0 {
		c.LoggingClient.Error("Could not load managed secrets - this environment will not be fully functional. See https://wiki.corp.mongodb.com/display/MMS/Cloud+Secrets+Management", localConfigServiceVersion, c.Options.ServiceName)
		return nil, fmt.Errorf("these properties are missing: %v", missingArgs)
	}

	return c.getAwsSecretsFromClient(awsRegions, &awsAccess, &awsSecret, c.Options.KeyPrefixInSecretStore, secretsToRetrieve)
}

func (c *ConfigServiceClient) getAwsSecretsFromClient(awsRegions []string, awsAccess, awsSecret, secretPrefix *string, secretsToRetrieve []ConfigServiceSecretMapping) (map[string]*configservicegrpc.Config, error) {
	secretsConfigMap := make(map[string]*configservicegrpc.Config)

	// Create a managedSecrets client for each region (replicating functionality from AwsManagedSecrets.java)
	managedSecretsClients, err := c.initializeSecretsManagerClients(awsRegions, awsAccess, awsSecret)
	if err != nil {
		return nil, err
	}

	// Random source for selecting regions
	randSource := rand.NewSource(time.Now().UnixNano())
	random := rand.New(randSource)

	for _, secretMapping := range secretsToRetrieve {
		secretNameInAws := *secretPrefix + secretMapping.Value

		var secretString string
		var propertyConfig *configservicegrpc.Config
		var err error

		// Try to retrieve the secret from a randomly selected region & continue until failure
		regionOrder := random.Perm(len(awsRegions))
		for _, idx := range regionOrder {
			region := awsRegions[idx]
			secretString, err = c.getSecretFromAws(managedSecretsClients[region], secretNameInAws)
			if err == nil {
				break
			}
		}

		if err != nil {
			c.incrementSecretStoreGetSecretErrorsCounter(AwsSecretsManager)
			return nil, fmt.Errorf("failed to retrieve secret %s: %w", secretNameInAws, err)
		}

		propertyConfig, err = makeApplicationPropertyConfig(secretMapping.Key, secretString)
		if err != nil {
			return nil, err
		}
		secretsConfigMap[secretMapping.Key] = propertyConfig
	}

	return secretsConfigMap, nil
}

func (c *ConfigServiceClient) getAwsRegions() []string {
	// If localStore isn't populated, then return the default secret region.
	c.localStoreLock.RLock()
	defer c.localStoreLock.RUnlock()

	if len(c.localStore) != 0 {
		awsRegions, err := c.String(LocalFileNamespace, AwsSecretRegionConfigName)

		if err == nil && awsRegions != "" {
			return strings.Split(awsRegions, ",")
		}
	}

	warningMessage := fmt.Sprintf("AWS regions not specified. Defaulting to '%s'. Make sure to define the regions with the key: '%s'", AwsDefaultSecretRegion, AwsSecretRegionConfigName)
	c.LoggingClient.Warning(warningMessage, localConfigServiceVersion, c.Options.ServiceName)
	return []string{AwsDefaultSecretRegion}
}

func (c *ConfigServiceClient) initializeSecretsManagerClients(awsRegions []string, awsAccess, awsSecret *string) (map[string]ConfigServiceSecretsManagerClient, error) {
	// Only for unit tests
	if c.Options.mockSecretsManagerClients != nil {
		return c.Options.mockSecretsManagerClients, nil
	}

	managedSecretsClients := make(map[string]ConfigServiceSecretsManagerClient)

	var loadDefaultConfigError error
	for _, region := range awsRegions {
		func(region string) {
			ctxWithTimeout, ctxCancel := context.WithTimeout(context.Background(), time.Second*time.Duration(*c.Options.RequestTimeoutSec))
			defer ctxCancel()
			cfg, localDefaultErr := config.LoadDefaultConfig(ctxWithTimeout,
				config.WithRegion(region),
				config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(*awsAccess, *awsSecret, "")),
				config.WithRetryer(func() aws.Retryer {
					// Note: instead of replicating the exponential backoff mechanism from AwsManagedSecrets.java,
					// we're using the built-in retry functionality from the AWS go-SDK.
					return retry.NewStandard(func(o *retry.StandardOptions) {
						o.MaxAttempts = AwsRetryCount
					})
				}),
			)
			if localDefaultErr != nil {
				loadDefaultConfigError = fmt.Errorf("failed to load AWS config for region %s: %w", region, localDefaultErr)
				return
			}
			managedSecretsClients[region] = secretsmanager.NewFromConfig(cfg)
		}(region)
		if loadDefaultConfigError != nil {
			break
		}
	}
	if loadDefaultConfigError != nil {
		return nil, loadDefaultConfigError
	}

	return managedSecretsClients, nil
}

// Application Properties-specific methods

// populateLocalStoreWithApplicationProperties populates the local store with data from local property files. Only runs on "start".
func (c *ConfigServiceClient) populateLocalStoreWithApplicationProperties() error {

	defaultPropertiesErr := c.populateLocalStoreWithDefaultProperties()
	if defaultPropertiesErr != nil {
		return defaultPropertiesErr
	}

	propertiesOverridesErr := c.populateLocalStoreWithPropertyOverrides()
	if propertiesOverridesErr != nil {
		return propertiesOverridesErr
	}

	return nil
}

func (c *ConfigServiceClient) populateLocalStoreWithPropertyOverrides() error {
	if c.Options.PropertyOverridePaths == nil {
		c.LoggingClient.Info("no properties overrides files provided", localConfigServiceVersion, c.Options.ServiceName)
		return nil
	}

	c.localStoreLock.Lock()
	defer c.localStoreLock.Unlock()

	for _, element := range c.Options.PropertyOverridePaths {
		c.LoggingClient.Info(fmt.Sprintf("loading property overrides from file: '%s'", element), localConfigServiceVersion, c.Options.ServiceName)

		propertyOverrides, propertyOverridesErr := c.parseLocalPropertyFile(element)

		// Stop and error out if a local override file doesn't work
		if propertyOverridesErr != nil {
			return propertyOverridesErr
		}

		for k, v := range propertyOverrides {
			// We ignore properties from override files if it was not provided in the default
			// properties file
			_, hasKey := c.localStore[k]
			if hasKey {
				c.LoggingClient.Info(
					fmt.Sprintf("Overriding property '%s' with value from file '%s'",
						k,
						element), localConfigServiceVersion, c.Options.ServiceName)
				c.localStore[k] = v
			} else {
				c.LoggingClient.Warning(
					fmt.Sprintf("Ignoring property '%s' from file '%s' as it doesn't exist in default property file",
						k,
						element), localConfigServiceVersion, c.Options.ServiceName)
			}
		}
	}

	return nil
}

func (c *ConfigServiceClient) populateLocalStoreWithDefaultProperties() error {
	if c.Options.DefaultPropertiesPath == nil {
		c.LoggingClient.Info("no default properties file provided", localConfigServiceVersion, c.Options.ServiceName)
		return nil
	}

	c.LoggingClient.Info(fmt.Sprintf("loading default properties in file: '%s'", *c.Options.DefaultPropertiesPath), localConfigServiceVersion, c.Options.ServiceName)
	defaultProperties, defaultPropertiesErr := c.parseLocalPropertyFile(*c.Options.DefaultPropertiesPath)
	if defaultPropertiesErr != nil {
		return defaultPropertiesErr
	}

	c.localStoreLock.Lock()
	defer c.localStoreLock.Unlock()

	for k, v := range defaultProperties {
		c.localStore[k] = v
	}

	return nil
}

func (c *ConfigServiceClient) parseLocalPropertyFile(path string) (map[string]*configservicegrpc.Config, error) {
	extension := filepath.Ext(path)
	switch extension {
	case jsonFileExtension:
		return c.parseJsonPropertiesFile(path)
	case yamlFileExtension, ymlFileExtension:
		return c.parseYamlPropertiesFile(path)
	case propertiesFileExtension:
		return c.parsePropertiesFile(path)
	default:
		return nil, makeUnknownFileExtensionError(extension)
	}
}

func (c *ConfigServiceClient) parseJsonPropertiesFile(path string) (map[string]*configservicegrpc.Config, error) {
	readFileContents, readFileErr := os.ReadFile(path)
	if readFileErr != nil {
		c.LoggingClient.Error(fmt.Sprintf("encountered error reading json file '%s': %s", path, readFileErr.Error()), localConfigServiceVersion, c.Options.ServiceName)
		return nil, readFileErr
	}

	var input map[string]interface{}
	unmarshalErr := json.Unmarshal(readFileContents, &input)
	if unmarshalErr != nil {
		c.LoggingClient.Error(fmt.Sprintf("encountered error unmarshalling json file '%s': %s", path, unmarshalErr.Error()), localConfigServiceVersion, c.Options.ServiceName)
		return nil, unmarshalErr
	}

	return c.parseUnmarshalledFileDataMap(input, path)
}

func (c *ConfigServiceClient) parseYamlPropertiesFile(path string) (map[string]*configservicegrpc.Config, error) {
	readFileContents, readFileErr := os.ReadFile(path)
	if readFileErr != nil {
		c.LoggingClient.Error(fmt.Sprintf("encountered error reading yaml file '%s': %s", path, readFileErr.Error()), localConfigServiceVersion, c.Options.ServiceName)
		return nil, readFileErr
	}

	var input map[string]interface{}
	unmarshalErr := yaml.Unmarshal(readFileContents, &input)
	if unmarshalErr != nil {
		c.LoggingClient.Error(fmt.Sprintf("encountered error unmarshalling yaml file '%s': %s", path, unmarshalErr.Error()), localConfigServiceVersion, c.Options.ServiceName)
		return nil, unmarshalErr
	}

	return c.parseUnmarshalledFileDataMap(input, path)
}

/**
 * Read .properties files, similar to java.util.Properties functionality.
 * Inspired by https://stackoverflow.com/questions/40022861/parsing-values-from-property-file-in-golang
 */
func (c *ConfigServiceClient) parsePropertiesFile(path string) (map[string]*configservicegrpc.Config, error) {
	readFileStream, readFileErr := os.Open(path)
	if readFileErr != nil {
		c.LoggingClient.Error(fmt.Sprintf("encountered error reading properties file '%s': %s", path, readFileErr.Error()), localConfigServiceVersion, c.Options.ServiceName)
		return nil, readFileErr
	}

	propertiesConfig := map[string]*configservicegrpc.Config{}
	scanner := bufio.NewScanner(readFileStream)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		// skip empty lines and comments
		if len(line) == 0 || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)

		if len(parts) != 2 {
			c.LoggingClient.Error(fmt.Sprintf("invalid property format in file '%s': %s", path, line), localConfigServiceVersion, c.Options.ServiceName)
			return nil, fmt.Errorf("invalid property format: %s", line)
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		if validationErr := c.validateProperty(path, key); validationErr != nil {
			return nil, validationErr
		}

		appPropertyKey := makeApplicationPropertyKey(key)
		configVal, configErr := makeApplicationPropertyConfig(key, value)
		if configErr != nil {
			return nil, configErr
		}
		propertiesConfig[appPropertyKey] = configVal
	}

	if scannerErr := scanner.Err(); scannerErr != nil {
		c.LoggingClient.Error(fmt.Sprintf("error scanning properties file '%s': %s", path, scannerErr.Error()), localConfigServiceVersion, c.Options.ServiceName)
		return nil, scannerErr
	}

	postProcessedProperties, postProcessingErr := c.postProcessConfigServiceData(propertiesConfig)
	if postProcessingErr != nil {
		return nil, postProcessingErr
	}

	return postProcessedProperties, nil
}

func (c *ConfigServiceClient) parseUnmarshalledFileDataMap(input map[string]interface{}, fileName string) (map[string]*configservicegrpc.Config, error) {
	propertiesConfig := map[string]*configservicegrpc.Config{}
	flattened := flattenProperties(input)
	for k, v := range flattened {
		validationErr := c.validateProperty(fileName, k)
		if validationErr != nil {
			return nil, validationErr
		}

		appPropertyKey := makeApplicationPropertyKey(k)
		configVal, configErr := makeApplicationPropertyConfig(k, v)
		if configErr != nil {
			return nil, configErr
		}
		propertiesConfig[appPropertyKey] = configVal
	}

	postProcessedProperties, postProcessingErr := c.postProcessConfigServiceData(propertiesConfig)
	if postProcessingErr != nil {
		return nil, postProcessingErr
	}

	return postProcessedProperties, nil
}

func flattenProperties(input map[string]interface{}) map[string]interface{} {
	output := map[string]interface{}{}
	if input == nil || len(input) == 0 {
		return output
	}

	for key, val := range input {
		switch element := val.(type) {
		case map[string]interface{}:
			nestedMap := flattenProperties(element)
			for nestedKey, nestedValue := range nestedMap {
				output[strings.Join([]string{key, nestedKey}, ".")] = nestedValue
			}
		default:
			output[key] = val
		}
	}

	return output
}

func makeApplicationPropertyKey(name string) string {
	return getKey(LocalFileNamespace, name)
}

func makeApplicationPropertyConfig(name string, input interface{}) (*configservicegrpc.Config, error) {
	inputAsString, inputAsStringErr := getInputAsString(name, input)
	if inputAsStringErr != nil {
		return nil, inputAsStringErr
	}

	data := configservicegrpc.ApplicationPropertyConfigData{
		Value: inputAsString,
	}

	metadata := configservicegrpc.ApplicationPropertyConfigMetadata{
		Source: configservicegrpc.ApplicationPropertySource_APPLICATION_PROPERTY_SOURCE_LOCAL,
	}

	pbData, pbDataErr := anypb.New(&data)
	if pbDataErr != nil {
		return nil, pbDataErr
	}

	pbMetadata, pbMetadataErr := anypb.New(&metadata)
	if pbMetadataErr != nil {
		return nil, pbMetadataErr
	}

	return &configservicegrpc.Config{
		Module:   configservicegrpc.ConfigModule_CONFIG_MODULE_APPLICATION_PROPERTY,
		Data:     pbData,
		Metadata: pbMetadata,
		Name:     name,
	}, nil
}

func getInputAsString(configName string, input interface{}) (string, error) {
	inputAsInterfaceSlice, inputIsInterfaceSlice := input.([]interface{})
	if inputIsInterfaceSlice {
		for _, element := range inputAsInterfaceSlice {
			if _, elementIsMap := element.(map[string]interface{}); elementIsMap {
				return "", fmt.Errorf("issue with key: '%s' => lists containing maps are not currently supported", configName)
			}
		}

		marshalledBytes, marshallErr := json.Marshal(inputAsInterfaceSlice)
		if marshallErr != nil {
			return "", marshallErr
		}
		return string(marshalledBytes), nil
	}
	return fmt.Sprintf("%v", input), nil
}

func (c *ConfigServiceClient) validateProperty(path string, k string) error {
	if strings.ContainsFunc(k, checkForForbiddenChars) {
		message := fmt.Sprintf("encountered key: '%s' in file: '%s' with forbidden characters", k, path)
		c.LoggingClient.Error(message, localConfigServiceVersion, c.Options.ServiceName)
		return errors.New(message)
	}
	return nil
}

func checkForForbiddenChars(r rune) bool {
	return r == ' ' || r == ':'
}

func (c *ConfigServiceClient) resolveApplicationPropertyModule(config *configservicegrpc.Config) (string, error) {
	data := config.GetData()
	m := &configservicegrpc.ApplicationPropertyConfigData{}

	if err := data.UnmarshalTo(m); err != nil {
		return "", err
	}

	return m.GetValue(), nil
}

// Demo Module-specific functions (will be removed)

func (c *ConfigServiceClient) resolveDemoConfigModule(config *configservicegrpc.Config) (interface{}, error) {
	data := config.GetData()
	m := &configservicegrpc.DemoModuleConfigData{}

	if err := data.UnmarshalTo(m); err != nil {
		return nil, err
	}

	return m.GetProp1(), nil
}

// Feature Flag-specific methods

func (c *ConfigServiceClient) resolveFeatureFlagConfigModule(config *configservicegrpc.Config, options *ConfigServiceRequestOptions) (*FeatureFlagEvaluationData, error) {
	configModule := config.GetModule().String()
	if configModule != configservicegrpc.ConfigModule.String(configservicegrpc.ConfigModule_CONFIG_MODULE_FEATURE_FLAG) {
		return nil, fmt.Errorf("ConfigServiceClient->resolveFeatureFlagConfigModule->config data with unexpected module: %s", configModule)
	}

	c.updateFeatureFlagLastRequestedAtMap(config.GetId())

	metadata := config.GetMetadata()
	if metadata != nil {
		featureFlagConfigMetadata := &configservicegrpc.FeatureFlagConfigMetadata{}

		if err := metadata.UnmarshalTo(featureFlagConfigMetadata); err == nil && featureFlagConfigMetadata.DeleteRequestedAt != nil {
			c.incrementSoftDeletedFeatureFlagRequestedCounterAndLogWarning(config.Name, featureFlagConfigMetadata.DeleteRequestedAt.AsTime())
		}
	}

	data := config.GetData()
	featureFlagConfigData := &configservicegrpc.FeatureFlagConfigData{}

	if err := data.UnmarshalTo(featureFlagConfigData); err != nil {
		return nil, err
	}

	ffId := config.GetId()

	var entityId *primitive.ObjectID
	if options != nil && options.EntityId != nil {
		entityId = options.EntityId
	}
	return c.evaluateFeatureFlag(featureFlagConfigData, entityId, ffId)
}

func (c *ConfigServiceClient) evaluateFeatureFlag(ffConfigData *configservicegrpc.FeatureFlagConfigData, entityId *primitive.ObjectID, ffId string) (*FeatureFlagEvaluationData, error) {
	// Check phase first
	// Note: SDK has no knowledge of emergency phase, it is handled by the config service
	switch ffConfigData.GetPhase() {
	case configservicegrpc.Phase_PHASE_DISABLED:
		return &FeatureFlagEvaluationData{
			EvaluationResult: false,
			DecisiveField:    FeatureFlagDecisiveFieldPhase,
			Phase:            configservicegrpc.Phase_PHASE_DISABLED,
		}, nil
	case configservicegrpc.Phase_PHASE_ENABLED:
		return &FeatureFlagEvaluationData{
			EvaluationResult: true,
			DecisiveField:    FeatureFlagDecisiveFieldPhase,
			Phase:            configservicegrpc.Phase_PHASE_ENABLED,
		}, nil
	case configservicegrpc.Phase_PHASE_CONTROLLED:
		return c.evaluateFeatureFlagValueInControlledPhase(ffConfigData, entityId, ffId)
	default:
		return &FeatureFlagEvaluationData{
			EvaluationResult: false,
			DecisiveField:    FeatureFlagDecisiveFieldNone,
			Phase:            configservicegrpc.Phase_PHASE_UNSPECIFIED,
		}, fmt.Errorf("unexpected phase %s", ffConfigData.GetPhase())
	}
}

func (c *ConfigServiceClient) updateFeatureFlagLastRequestedAtMap(ffId string) {
	c.featureFlagLastRequestedAtMap[ffId] = timestamppb.Now()
}

func (c *ConfigServiceClient) clearFeatureFlagLastRequestedAtMap() {
	c.featureFlagLastRequestedAtMap = map[string]*timestamppb.Timestamp{}
}

func (c *ConfigServiceClient) evaluateFeatureFlagValueInControlledPhase(configData *configservicegrpc.FeatureFlagConfigData,
	entityId *primitive.ObjectID, ffId string) (*FeatureFlagEvaluationData, error) {
	if entityId == nil {
		return &FeatureFlagEvaluationData{
			EvaluationResult: false,
			DecisiveField:    FeatureFlagDecisiveFieldRolloutPercentage,
			Phase:            configservicegrpc.Phase_PHASE_CONTROLLED,
		}, nil
	}

	entityIdStr := entityId.Hex()

	if slices.Contains(configData.GetBlockList(), entityIdStr) {
		return &FeatureFlagEvaluationData{
			EvaluationResult: false,
			DecisiveField:    FeatureFlagDecisiveFieldBlockList,
			Phase:            configservicegrpc.Phase_PHASE_CONTROLLED,
		}, nil
	} else if slices.Contains(configData.GetAllowList(), entityIdStr) {
		return &FeatureFlagEvaluationData{
			EvaluationResult: true,
			DecisiveField:    FeatureFlagDecisiveFieldAllowList,
			Phase:            configservicegrpc.Phase_PHASE_CONTROLLED,
		}, nil
	}

	// If EntityId is not in either blockList or allowList, evaluate based on gradual rollout hashing algorithm.
	return c.evaluateEntityInFeatureFlagGradualRollout(configData.GetRolloutPercentage(), entityIdStr, ffId)
}

func (c *ConfigServiceClient) evaluateEntityInFeatureFlagGradualRollout(rolloutPercentage int32, entityId string, ffId string) (*FeatureFlagEvaluationData, error) {
	evaluationResult, evaluationError := c.isHashedIdWithinPercentageForFeatureFlag(entityId, ffId, rolloutPercentage)
	if evaluationError != nil {
		return &FeatureFlagEvaluationData{
			EvaluationResult: false,
			DecisiveField:    FeatureFlagDecisiveFieldNone,
			Phase:            configservicegrpc.Phase_PHASE_CONTROLLED,
		}, evaluationError
	}
	return &FeatureFlagEvaluationData{
		EvaluationResult: evaluationResult,
		DecisiveField:    FeatureFlagDecisiveFieldRolloutPercentage,
		Phase:            configservicegrpc.Phase_PHASE_CONTROLLED,
	}, nil
}

// isHashedIdWithinPercentageForFeatureFlag determines if the crc32 hashed EntityId(ObjectId) falls within a desired percentage for a given feature flag id.
// This is heavily inspired by the MathUtils.isHashedIdWithinPercentage function in mms.
func (c *ConfigServiceClient) isHashedIdWithinPercentageForFeatureFlag(entityIdHexString string, ffId string, percentage int32) (bool, error) {
	if percentage < 0 || percentage > 100 {
		return false, fmt.Errorf("percentage value must be between 0 and 100, value provided: %d", percentage)
	}
	// Check if the input entity id is a valid hexadecimal string
	_, err := hex.DecodeString(entityIdHexString)
	if err != nil {
		return false, fmt.Errorf("invalid hexadecimal string for entity id: %s", entityIdHexString)
	}
	// NOTE: This hash function needs to be consistent with the one used in Config Service Java Sdk.
	checksum := crc32.ChecksumIEEE([]byte(entityIdHexString + ffId))
	thresholdValue := (float64(percentage) / 100) * maxCrc32HashValue

	c.LoggingClient.Debug(fmt.Sprintf("In isHashedIdWithinPercentageForFeatureFlag: entityIdHexString: %s, featureFlagId: %s, checksum: %v, "+
		"threshold: %v, result: %v\n", entityIdHexString, ffId, float64(checksum), thresholdValue, float64(checksum) <= thresholdValue),
		localConfigServiceVersion, c.Options.ServiceName)

	return float64(checksum) <= thresholdValue, nil
}

// general validators and helpers

func (c *ConfigServiceClient) validateRequest(namespace string, configName string) error {
	c.localStoreLock.RLock()
	defer c.localStoreLock.RUnlock()

	if len(c.localStore) == 0 {
		c.incrementCounter(EmptyLocalStoreCounterName)
		return fmt.Errorf("ConfigServiceClient->localStore has not been populated yet. Please call `start` to populate the localStore and try again")
	}

	if len(namespace) == 0 {
		return fmt.Errorf("ConfigServiceClient->namespace cannot be empty or null")
	}

	if len(configName) == 0 {
		return fmt.Errorf("ConfigServiceClient->configName cannot be empty or null")
	}

	return nil
}

func getKey(namespace string, configName string) string {
	return strings.Join([]string{namespace, configName}, configKeySeparator)
}

func getTypeError(configName, t string) error {
	return fmt.Errorf("ConfigServiceClient->requested value for key: '%s' is not of type %s", configName, t)
}

func getTypeRepresentationError(configName string, t interface{}) error {
	return fmt.Errorf("ConfigServiceClient->property with key: '%s' was not represented correctly (encountered: %T)", configName, t)
}

func makeUnknownFileExtensionError(ext string) error {
	return fmt.Errorf("unknown properties file type: '%s'. must be one of: %v", ext, []string{jsonFileExtension, yamlFileExtension, ymlFileExtension, propertiesFileExtension})
}

func checkIfIsAnyIntType(value interface{}) (int, bool) {
	typedValue := 0
	isIntType := false
	switch v := value.(type) {
	case int64:
		typedValue = int(v)
		isIntType = true
	case int32:
		typedValue = int(v)
		isIntType = true
	case int:
		typedValue = v
		isIntType = true
	default:
		// just fall out without updating typedValue or isIntType
	}
	return typedValue, isIntType
}

func checkIfIsAnyInt64Type(value interface{}) (int64, bool) {
	typedValue := int64(0)
	isIntType := false
	switch v := value.(type) {
	case int64:
		typedValue = v
		isIntType = true
	case int32:
		typedValue = int64(v)
		isIntType = true
	case int:
		typedValue = int64(v)
		isIntType = true
	default:
		// just fall out without updating typedValue or isIntType
	}
	return typedValue, isIntType
}

// canNumberBeRepresented returns true if the specified float does not equal +Inf/-Inf
func canNumberBeRepresented(f float64) bool {
	return f != math.Inf(+1) && f != math.Inf(-1)
}

// monitoring

func registerInternalMetrics(monitoringClient ConfigServiceMonitoringClient) error {
	counters := []struct {
		name        string
		description string
		labels      []string
	}{
		{
			name:        GetConfigsErrorsCounterName,
			description: GetConfigsErrorsCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel, statusCodeLabel},
		},
		{
			name:        StartPopulateLocalStoreErrorsCounterName,
			description: StartPopulateLocalStoreErrorsCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
		{
			name:        StartPopulateSecretStoreErrorsCounterName,
			description: StartPopulateSecretStoreErrorsCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
		{
			name:        UpdateFeatureFlagMetadataCounterName,
			description: UpdateFeatureFlagMetadataCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel, statusCodeLabel},
		},
		{
			name:        ConfigKeyMissesCounterName,
			description: ConfigKeyMissesCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
		{
			name:        SoftDeletedFeatureFlagRequestedCounterName,
			description: SoftDeletedFeatureFlagRequestedCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
		{
			name:        EmptyLocalStoreCounterName,
			description: EmptyLocalStoreCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
		{
			name:        SecretStoreGetSecretErrorsCounterName,
			description: SecretStoreGetSecretErrorsCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel, secretStoreLabel},
		},
	}

	timers := []struct {
		name        string
		description string
		labels      []string
	}{
		{
			name:        StartDurationCounterName,
			description: StartDurationCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
		{
			name:        PopulateLocalStoreDurationCounterName,
			description: PopulateLocalStoreDurationCounterDescription,
			labels:      []string{sdkVersionLabel, serviceNameLabel},
		},
	}

	for _, c := range counters {
		if err := monitoringClient.RegisterCounter(c.name, c.description, c.labels...); err != nil {
			return err
		}
	}
	for _, t := range timers {
		if err := monitoringClient.RegisterTimer(t.name, t.description, t.labels...); err != nil {
			return err
		}
	}

	return nil
}

func (c *ConfigServiceClient) incrementCounter(name string, additionalArgs ...string) {
	args := []string{localConfigServiceVersion, c.Options.ServiceName}
	if len(additionalArgs) > 0 {
		args = append(args, additionalArgs...)
	}
	monitoringError := c.MonitoringClient.IncrementCounter(name, args...)
	if monitoringError != nil {
		c.LoggingClient.Error(fmt.Sprintf("ConfigServiceClient->Error incrementing counter '%s': %s", name, monitoringError.Error()), localConfigServiceVersion, c.Options.ServiceName)
	}
}

func (c *ConfigServiceClient) startTimer(name string, additionalLabels ...string) {
	labels := []string{localConfigServiceVersion, c.Options.ServiceName}
	if len(additionalLabels) > 0 {
		labels = append(labels, additionalLabels...)
	}
	monitoringError := c.MonitoringClient.StartTimer(name, labels...)
	if monitoringError != nil {
		c.LoggingClient.Error(fmt.Sprintf("ConfigServiceClient->Error starting timer '%s': %s", name, monitoringError.Error()), localConfigServiceVersion, c.Options.ServiceName)
	}
}

func (c *ConfigServiceClient) stopTimer(name string) {
	monitoringError := c.MonitoringClient.StopTimer(name)
	if monitoringError != nil {
		c.LoggingClient.Error(fmt.Sprintf("ConfigServiceClient->Error stopping timer '%s': %s", name, monitoringError.Error()), localConfigServiceVersion, c.Options.ServiceName)
	}
}

func (c *ConfigServiceClient) incrementConfigKeyMissesCounterAndError(configKey string) error {
	c.incrementCounter(ConfigKeyMissesCounterName)
	return fmt.Errorf("ConfigServiceClient->No config value found for key: %s", configKey)
}

func (c *ConfigServiceClient) incrementSoftDeletedFeatureFlagRequestedCounterAndLogWarning(configKey string, deleteRequestedAt time.Time) {
	c.incrementCounter(SoftDeletedFeatureFlagRequestedCounterName)
	c.LoggingClient.Warning(fmt.Sprintf("requested feature flag '%s' has deleteRequestedAt: '%v'", configKey, deleteRequestedAt), localConfigServiceVersion, c.Options.ServiceName)
}

func (c *ConfigServiceClient) incrementSecretStoreGetSecretErrorsCounter(storeType SecretStoreType) {
	storeTypeStr := string(storeType)
	c.incrementCounter(SecretStoreGetSecretErrorsCounterName, storeTypeStr)
}
