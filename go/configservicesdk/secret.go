package configservicesdk

const (
	maskedValue  = "********"
	SecretPrefix = "(secret)"
)

type Secret struct {
	value string
}

func NewSecret(v string) *Secret {
	return &Secret{value: v}
}

func (s *Secret) String() string {
	return maskedValue
}

func (s *Secret) PlainTextValue() string {
	return s.value
}

type SecretStoreType string

const (
	AwsSecretsManager SecretStoreType = "aws"
	Helix             SecretStoreType = "helix"
	test              SecretStoreType = "test"
)
