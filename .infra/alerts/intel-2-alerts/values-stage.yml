---
# This could ideally not be required and could come dynamically from {{ .Values.additionalLabels.xgen_environment }}

pagerdutyService:
  name: intel-2-non-prod
  fullnameOverride: intel-2-non-prod
rulesTplVals:
  deployment: "(mms-agent-logs)"
  pod: "(mms-agent-logs).*"
  app_agent: "mms-agent-logs"
  app_agent_metrics: "mms-agent-metrics"
  agent_warn_threshold: 5000
  agent_warn_error_zscore_threshold: 5.0
  slow_log_ingestion_threshold: 0
  slow_log_dropped_threshold: 1000
  container: "mms-agent-logs"
  severity_high: "warning"
  severity_low: "warning"
  log_ingestion_sustained_for: 10m
  maintenance_has_run_threshold: 72h

  # query stats
  querystats_maas_low_sev_success_volume: 10
  querystats_maas_high_sev_success_volume: 5
  querystats_maas_low_sev_success_percent: 98.97
  querystats_maas_high_sev_success_percent: 98.95
  querystats_ia_low_sev_success_volume: 10
  querystats_ia_high_sev_success_volume: 5
  querystats_ia_low_sev_success_percent: 98.97
  querystats_ia_high_sev_success_percent: 98.90
  querystats_maas_soft_rate_limit: 35000000
