---
# yamllint disable rule:quoted-strings

pagerdutyService:
  enabled: true
  name: mms-atlas-clusters-ad-non-prod
  fullnameOverride: mms-atlas-clusters-ad-non-prod
  escalationPolicyName: Atlas Clusters Lead
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
  legacyEmailConfig:
    enabled: true
    externalSecretsComponent: core-systems-alerts
    to: <EMAIL>
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: mms_nds_cloud_maintenance_windows_alerting_rules
      rules:
        - alert: (Non-Prod) High z-score in open maintenance windows exceeding 1hr runtime.
          expr: |
            (
              sum(increase(mms_core_alert_statuses_total{xgen_environment=~"dev|qa|staging", event_type="SCHEDULED_MAINTENANCE_DELAYED", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)
              - avg_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment=~"dev|qa|staging", event_type="SCHEDULED_MAINTENANCE_DELAYED", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)[1h:10m])
            )
            / stddev_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment=~"dev|qa|staging", event_type="SCHEDULED_MAINTENANCE_DELAYED", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment) [1h:10m]) > 3
            and stddev_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment=~"dev|qa|staging", event_type="SCHEDULED_MAINTENANCE_DELAYED", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)[1h:10m]) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: A high z-score in open maintenance windows exceeding 1hr runtime.
            description: The z score for maintenances exceeding 1hr runtime. Note a Z-score measures how many standard deviations a specific data point is from the mean of a dataset.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846569/Internal+Maintenance#InternalMaintenance-HighNumberofOpenMaintenanceWindows-AlertRunBook
            grafana_url: 'https://grafana.helix.corp.mongodb.com/d/Fj7aq3USk/atlas-cluster-customer-experience-cx?viewPanel=52&var-datasource=VictoriaMetrics&var-env=dev&var-percentile=0.99&var-endpoint=All&var-time_interval=10m&var-move_name=All&var-step_name=All&var-pathRegex=%2Fapi%2Fatlas%2F(v%5C%5Cd%2B%7Cv1.5)%2Fgroups%2F%5C%5C%7BgroupId%5C%5C%7D%2Fclusters.*&var-region=us-east-1&from=now-7d&to=now'
        - alert: (Non-Prod) High number of open maintenance windows exceeding 1hr runtime.
          expr: |
            (
              sum(increase(mms_core_alert_statuses_total{xgen_environment=~"dev|qa|staging", event_type="SCHEDULED_MAINTENANCE_DELAYED", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment) > 10
            )
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: A high number of open maintenance windows exceeding 1hr runtime.
            description: There is a high number of maintenances exceeding 1hr runtime.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846569/Internal+Maintenance#InternalMaintenance-HighNumberofOpenMaintenanceWindows-AlertRunBook
            grafana_url: 'https://grafana.helix.corp.mongodb.com/d/Fj7aq3USk/atlas-cluster-customer-experience-cx?viewPanel=52&var-datasource=VictoriaMetrics&var-env=dev&var-percentile=0.99&var-endpoint=All&var-time_interval=10m&var-move_name=All&var-step_name=All&var-pathRegex=%2Fapi%2Fatlas%2F(v%5C%5Cd%2B%7Cv1.5)%2Fgroups%2F%5C%5C%7BgroupId%5C%5C%7D%2Fclusters.*&var-region=us-east-1&from=now-7d&to=now'
    - name: mms_nds_data_validation_alerting_rules
      rules:
        - alert: (Non-Prod) High percentage of data validation process failures
          expr: |
            (
              sum(increase(mms_nds_data_validation_process_failed_total{xgen_environment=~"dev|qa"}[1d])) by (xgen_environment)
              /
              sum(increase(mms_nds_data_validation_svc_record_created_total{xgen_environment=~"dev|qa"}[1d])) by (xgen_environment)
            ) > 0.20
            and
            sum(increase(mms_nds_data_validation_svc_record_created_total{xgen_environment=~"dev|qa"}[1d])) by (xgen_environment) >= 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High percentage of data validation process failures
            description: More than 20% of data validations are failing over the last day with at least 10 total data validations.
            grafana_url: 'https://grafana.helix.corp.mongodb.com/d/Af-pJ6MSk/periodic-validation?orgId=20&var-metrics_interval=$__auto&from=now-7d&to=now&timezone=utc&var-datasource=default&var-env=dev&viewPanel=panel-47'
        - alert: (Non-Prod) High percentage of data validation fallbacks to command
          expr: |
            (
              sum(increase(mms_nds_data_validation_fallback_to_command_total{xgen_environment=~"dev|qa"}[1d])) by (xgen_environment)
              /
              sum(increase(mms_nds_data_validation_svc_record_created_total{xgen_environment=~"dev|qa"}[1d])) by (xgen_environment)
            ) > 0.20
            and
            sum(increase(mms_nds_data_validation_svc_record_created_total{xgen_environment=~"dev|qa"}[1d])) by (xgen_environment) >= 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High percentage of data validation fallbacks to command-based validation
            description: More than 20% of data validations are falling back to command-based validation over the last day with at least 10 total data validations.
            grafana_url: 'https://grafana.helix.corp.mongodb.com/d/Af-pJ6MSk/periodic-validation?orgId=20&var-metrics_interval=$__auto&from=now-7d&to=now&timezone=utc&var-datasource=default&var-env=dev&viewPanel=panel-49'
