---
pagerdutyService:
  enabled: true
  escalationPolicyName: "ACE IAM Engineering"
  name: authz-service-client-prod-alerts
  fullnameOverride: authz-service-client-prod-alerts
alertmanagerConfig:
  enabled: true
  route:
    groupBy:
      - alertname
      - severity
    matchers:
      - name: xgen_owner
        value: "IAM Engineers"
      - name: xgen_alerts_deployment
        value: authz-service-client-alerts
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
    xgen_owner: "IAM Engineers"
    xgen_alerts_deployment: authz-service-client-alerts
  groups:
    - name: authz_service_client_alerts_prod
      rules:
        - alert: "Prod/Prod Gov/Internal - Errors being thrown from Authorization RPCs (Client-Side)"
          expr: sum by (xgen_environment) (increase(mms_iam_authz_is_authorized_request_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", error_code!="NOT_FOUND"}[1m])) > 5
          for: 10m
          labels:
            severity: warning
          annotations:
            description: |
              This condition indicates that we are seeing errors in the isAuthorized calls being made from MMS to AuthZ Service in prod environment.
            runbook: "https://wiki.corp.mongodb.com/display/ATLASIAM/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-ErrorsthrownfromisAuthorizedcalls"
            summary: "Client side: Errors being thrown from isAuthorized RPC calls Env:{{ $labels.xgen_environment }}"
