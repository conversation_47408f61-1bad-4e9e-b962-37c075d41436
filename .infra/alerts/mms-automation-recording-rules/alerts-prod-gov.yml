---
vmRule:
  groups:
    - name: mms-agent_automation_log_error_increase_sustained_prod_gov
      rules:
        - record: mms_automation_gov::mms_agent_logs_inserted_total_error_prod_gov::increase1m
          expr: sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment=~"(prod-gov)",level=~"(error|ERROR)",module=~"automation"}[1m])) by (xgen_environment)
        - record: mms_automation_gov::mms_agent_logs_inserted_total_error_prod_gov::increase1m::avg_over_time_5d
          expr: avg_over_time(mms_agent_logs_inserted_total_error_prod_gov::increase1m[5d])
        - record: mms_automation_gov::mms_agent_logs_inserted_total_error_prod_gov::increase1m:stddev_over_time_5d
          expr: stddev_over_time(mms_agent_logs_inserted_total_error_prod_gov::increase1m[5d])
    - name: mms-agent_automation_log_warn_increase_sustained_prod_gov
      rules:
        - record: mms_automation_gov::mms_agent_logs_inserted_total_warn_prod_gov::increase1m
          expr: sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment=~"(prod-gov)",level=~"(warn|WARN)",module=~"automation"}[1m])) by (xgen_environment)
        - record: mms_automation_gov::mms_agent_logs_inserted_total_warn_prod_gov::increase1m::avg_over_time_5d
          expr: avg_over_time(mms_agent_logs_inserted_total_warn_prod_gov::increase1m[5d])
        - record: mms_automation_gov::mms_agent_logs_inserted_total_warn_prod_gov::increase1m:stddev_over_time_5d
          expr: stddev_over_time(mms_agent_logs_inserted_total_warn_prod_gov::increase1m[5d])
    - name: mms-automation-prod-gov-rules-record-rules-no-subquery
      interval: 1m
      rules:
        - record: mms_automation_gov::automation_conf_v1_prod_gov_avg
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="GET",path=~"/agents/api/automation/conf/v1/.*"}[1m]))/ sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="GET",path=~"/agents/api/automation/conf/v1/.*"}[1m]))
        - record: mms_automation_gov::automation_conf_v1_prod_gov_avg_over_time::no_subquery
          expr: avg_over_time(mms_automation_gov::automation_conf_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::automation_conf_v1_prod_gov_stddev::no_subquery
          expr: stddev_over_time(mms_automation_gov::automation_conf_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_jobs_v1_prod_gov_avg
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="GET",path=~"/agents/api/automation/jobs/v1/.*"}[1m]))/ sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="GET",path=~"/agents/api/automation/jobs/v1/.*"}[1m]))
        - record: mms_automation_gov::agents_api_automation_jobs_v1_prod_gov_avg_over_time::no_subquery
          expr: avg_over_time(mms_automation_gov::agents_api_automation_jobs_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_jobs_v1_prod_gov_avg_stddev::no_subquery
          expr: stddev_over_time(mms_automation_gov::agents_api_automation_jobs_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_settings_v1_prod_gov_avg
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="GET",path=~"/agents/api/automation/settings/v1/.*"}[1m]))/ sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="GET",path=~"/agents/api/automation/settings/v1/.*"}[1m]))
        - record: mms_automation_gov::agents_api_automation_settings_v1_prod_gov_avg_over_time::no_subquery
          expr: avg_over_time(mms_automation_gov::agents_api_automation_settings_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_settings_v1_prod_gov_avg_stddev::no_subquery
          expr: stddev_over_time(mms_automation_gov::agents_api_automation_settings_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agentlog_v3_catch_prod_gov_avg
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="POST",path=~"/agentlog/v3/catch/.*"}[1m]))/ sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="POST",path=~"/agentlog/v3/catch/.*"}[1m]))
        - record: mms_automation_gov::agentlog_v3_catch_prod_gov_avg_over_time::no_subquery
          expr: avg_over_time(mms_automation_gov::agentlog_v3_catch_prod_gov_avg[5d])
        - record: mms_automation_gov::agentlog_v3_catch_prod_gov_avg_stddev::no_subquery
          expr: stddev_over_time(mms_automation_gov::agentlog_v3_catch_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_fullstatus_v1_prod_gov_avg
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="POST",path=~"/agents/api/automation/fullstatus/v1/.*"}[1m]))/ sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="POST",path=~"/agents/api/automation/fullstatus/v1/.*"}[1m]))
        - record: mms_automation_gov::agents_api_automation_fullstatus_v1_prod_gov_avg_over_time::no_subquery
          expr: avg_over_time(mms_automation_gov::agents_api_automation_fullstatus_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_fullstatus_v1_prod_gov_avg_stddev::no_subquery
          expr: stddev_over_time(mms_automation_gov::agents_api_automation_fullstatus_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_log_v1_prod_gov_avg
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="POST",path=~"/agents/api/automation/log/v1/.*"}[1m]))/ sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"prod-gov",method="POST",path=~"/agents/api/automation/log/v1/.*"}[1m]))
        - record: mms_automation_gov::agents_api_automation_log_v1_prod_gov_avg_over_time::no_subquery
          expr: avg_over_time(mms_automation_gov::agents_api_automation_log_v1_prod_gov_avg[5d])
        - record: mms_automation_gov::agents_api_automation_log_v1_prod_gov_avg_stddev::no_subquery
          expr: stddev_over_time(mms_automation_gov::agents_api_automation_log_v1_prod_gov_avg[5d])
