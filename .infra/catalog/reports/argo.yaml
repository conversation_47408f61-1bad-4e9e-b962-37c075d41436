---
apiVersion: backstage.io/v1alpha1
kind: Domain
metadata:
  name: reports
  description: Argo Workflows
spec:
  type: argo
  owner: sre-atlas
  argo:
    environments:
      - name: mgmt
        automated_deployments:
          - bucket:
              name: mongodb-mms-build-server
              credentials: mms_artifacts_aws
            environments:
              - name: prod
                apps:
                  - name: atlas-chef-failure-report
                    version_object: origin/master
