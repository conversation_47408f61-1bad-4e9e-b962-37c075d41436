---
apiVersion: backstage.io/v1alpha1
kind: Domain
metadata:
  name: growth-ops
  description: Argo Configuration for Growth operational tools and services
spec:
  type: argo
  owner: 10gen-helix-growth-prod-admin
  argo:
    environments:
      - name: mgmt
        managed_workflows:
          - schedule: "*/10 * * * *"
            asset: "master"
            # SRE-1069 to support this, otherwise all workflows
            # under `.infra/workflows` will be added which doesn't
            # make sense for a monorepo.
            # directory: .infra/workflows/mms
            repository:
              owner: 10gen
              name: mms
        automated_deployments:
          - name: automated-deployments-non-prod
            timeout: 1800
            bucket:
              name: mongodb-mms-build-server
              credentials: mms_artifacts_aws
            environments:
              - name: dev
                apps:
                  - name: mms-atlas-growth-alerts
                    version_object: versions/helm-values-deployments/master/mms-atlas-growth-alerts
              - name: qa
                apps:
                  - name: mms-atlas-growth-alerts
                    version_object: versions/helm-values-deployments/master/mms-atlas-growth-alerts
          - name: automated-deployments-prod
            timeout: 2700
            bucket:
              name: mongodb-mms-build-server
              credentials: mms_artifacts_aws
            environments:
              - name: staging
                apps:
                  - name: mms-atlas-growth-alerts
                    version_object: versions/helm-values-deployments/master/mms-atlas-growth-alerts
              - name: prod
                apps:
                  - name: mms-atlas-growth-alerts
                    version_object: versions/helm-values-deployments/master/mms-atlas-growth-alerts
              - name: prod-gov
                apps:
                  - name: mms-atlas-growth-alerts
                    version_object: versions/helm-values-deployments/master/mms-atlas-growth-alerts
