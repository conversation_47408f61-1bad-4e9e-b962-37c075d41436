---
apiVersion: backstage.io/v1alpha1
kind: Domain
metadata:
  name: communication
  description: Argo Workflows
spec:
  type: argo
  owner: 10gen-helix-communication-prod-admin
  argo:
    environments:
      - name: mgmt
        automated_deployments:
          - bucket:
              name: mongodb-mms-build-server
              credentials: mms_artifacts_aws
            environments:
              - name: dev
                apps:
                  - name: communication-service
                    version_object: origin/master
                  - name: communication-service-resources
                    version_object: origin/master
              - name: dev-gov
                apps:
                  - name: communication-service
                    version_object: origin/master
                  - name: communication-service-resources
                    version_object: origin/master
              - name: qa
                apps:
                  - name: communication-service
                    version_object_pointer: versions/pointers/mms-qa
                  - name: communication-service-resources
                    version_object_pointer: versions/pointers/mms-qa
              - name: qa-gov
                apps:
                  - name: communication-service
                    version_object_pointer: versions/pointers/mms-qa
                  - name: communication-service-resources
                    version_object_pointer: versions/pointers/mms-qa
              - name: staging
                apps:
                  - name: communication-service
                    version_object_pointer: versions/pointers/mms-stage
                  - name: communication-service-resources
                    version_object_pointer: versions/pointers/mms-stage
              - name: prod
                apps:
                  - name: communication-service
                    version_object_pointer: versions/pointers/mms-prod
                  - name: communication-service-resources
                    version_object_pointer: versions/pointers/mms-prod
              - name: prod-gov
                apps:
                  - name: communication-service
                    version_object_pointer: versions/pointers/mms-prod
                  - name: communication-service-resources
                    version_object_pointer: versions/pointers/mms-prod
              - name: internal
                apps:
                  - name: communication-service
                    version_object_pointer: versions/pointers/mms-prod
                  - name: communication-service-resources
                    version_object_pointer: versions/pointers/mms-prod
