---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-payments-alerts
spec:
  deployment:
    slack_channel: cloud
    repository:
      owner: 10gen
      name: mms
    deploy_yaml_path: .infra/deploy.yml
    application_name: mms-payments-alerts
    workload_type: deployment
    argocd_project: payments
    labels:
      app: mms-payments-alerts
      owner: payments
    targets:
      prod:
        namespace: payments-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud
          azure:
            - kube-1-*-azure-cloud
          gcp:
            - kube-1-*-gcp-cloud
  lifecycle: prod
  owner: 10gen-helix-payments-prod-admin
  system: payments
  type: deployment
