---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-atlas-clusters-ad-alerts
spec:
  deployment:
    slack_channel: cloud
    repository:
      owner: 10gen
      name: mms
    deploy_yaml_path: .infra/deploy.yml
    application_name: mms-atlas-clusters-ad-alerts
    workload_type: deployment
    argocd_project: mms
    labels:
      app: mms-atlas-clusters-ad-alerts
      owner: mms
    targets:
      dev:
        namespace: core-systems-dev
        clusters:
          aws:
            - kube-1-*-aws-cloud-dev
      internal:
        namespace: core-systems-internal
        clusters:
          aws:
            - kube-1-*-aws-cloud-internal
      prod:
        namespace: core-systems-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud
      qa:
        namespace: core-systems-qa
        clusters:
          aws:
            - kube-1-*-aws-cloud-dev
      staging:
        namespace: core-systems-staging
        clusters:
          aws:
            - kube-1-*-aws-cloud
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: core-systems
  type: deployment
