---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-cron-jobs
spec:
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: core-systems
  type: deployment
  deployment:
    slack_channel: cloud
    deployment_group: mms-web
    deploy_yaml_path: .infra/deploy.yml
    workload_type: deployment
    argocd_project: mms
    repository:
      owner: 10gen
      name: mms
    labels:
      app: mms-cron-jobs
      owner: mms
    targets:
      dev:
        namespace: core-systems-dev
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_dev
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud-dev
            - kube-1-us-east-2-aws-cloud-dev
      dev-gov:
        namespace: core-systems-dev
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          canonical_role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_dev_gov
        clusters:
          aws:
            - kube-1-us-gov-west-1-aws-cloud-gov-dev
      qa:
        namespace: core-systems-qa
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_qa
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud-dev
            - kube-1-us-east-2-aws-cloud-dev
      qa-gov:
        namespace: core-systems-qa
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          canonical_role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_qa_gov
        clusters:
          aws:
            - kube-1-us-gov-west-1-aws-cloud-gov-dev
      staging:
        namespace: core-systems-staging
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_staging
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud
            - kube-1-us-east-2-aws-cloud
      prod:
        namespace: core-systems-prod
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_prod
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud
            - kube-1-us-east-2-aws-cloud
      prod-gov:
        namespace: core-systems-prod
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          canonical_role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_prod_gov
        clusters:
          aws:
            - kube-1-us-gov-west-1-aws-cloud-gov-prod
      internal:
        namespace: core-systems-internal
        release_name: mms-cron-jobs
        labels:
          role: mms-cron-jobs
          owner: mms
        argocd_profile: argocd_internal
        clusters:
          aws:
            - kube-1-us-east-2-aws-cloud-internal
    application_name: mms-cron-jobs-kube
