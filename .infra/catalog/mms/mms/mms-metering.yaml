---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-metering
spec:
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: mms
  type: deployment
  deployment:
    slack_channel: cloud
    deployment_group: mms-web
    deploy_yaml_path: .infra/deploy.yml
    workload_type: deployment
    argocd_project: mms
    repository:
      owner: 10gen
      name: mms
    labels:
      app: mms-metering
      owner: mms
    targets:
      dev:
        namespace: mms-dev
        release_name: mms-metering
        labels:
          role: metering
          owner: mms
        argocd_profile: argocd_dev
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud-dev
            - kube-1-us-east-2-aws-cloud-dev
      qa:
        namespace: mms-qa
        release_name: mms-metering
        labels:
          role: metering
          owner: mms
        argocd_profile: argocd_qa
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud-dev
            - kube-1-us-east-2-aws-cloud-dev
      staging:
        namespace: mms-staging
        release_name: mms-metering
        labels:
          role: metering
          owner: mms
        argocd_profile: argocd_staging
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud
            - kube-1-us-east-2-aws-cloud
      prod:
        namespace: mms-prod
        release_name: mms-metering
        labels:
          role: metering
          owner: mms
        argocd_profile: argocd_prod
        clusters:
          aws:
            - kube-1-us-east-1-aws-cloud
            - kube-1-us-east-2-aws-cloud
    application_name: mms-metering-kube
