---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-intel-3-alerts
spec:
  deployment:
    slack_channel: cloud
    repository:
      owner: 10gen
      name: mms
    deploy_yaml_path: .infra/deploy.yml
    application_name: mms-intel-3-alerts
    workload_type: deployment
    argocd_project: mms
    labels:
      app: mms-intel-3-alerts
      owner: mms
    targets:
      dev:
        namespace: mms-dev
        clusters:
          aws:
            - kube-1-*-aws-cloud-dev
      dev-gov:
        namespace: mms-dev
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-dev
      internal:
        namespace: mms-internal
        clusters:
          aws:
            - kube-1-*-aws-cloud-internal
      prod:
        namespace: mms-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud
      prod-gov:
        namespace: mms-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-prod
      qa:
        namespace: mms-qa
        clusters:
          aws:
            - kube-1-*-aws-cloud-dev
      qa-gov:
        namespace: mms-qa
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-dev
      staging:
        namespace: mms-staging
        clusters:
          aws:
            - kube-1-*-aws-cloud
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: mms
  type: deployment
