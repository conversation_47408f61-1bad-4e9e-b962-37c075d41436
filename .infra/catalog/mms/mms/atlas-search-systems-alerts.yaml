---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: atlas-search-systems-alerts
spec:
  deployment:
    slack_channel: cloud
    repository:
      owner: 10gen
      name: mms
    deploy_yaml_path: .infra/deploy.yml
    application_name: atlas-search-systems-alerts
    workload_type: deployment
    argocd_project: mms
    labels:
      app: atlas-search-systems-alerts
      owner: mms
    targets:
      dev:
        namespace: mms-dev
        clusters:
          aws:
            - kube-1-*-aws-cloud-dev
          gcp:
            - kube-1-*-gcp-cloud-dev
          azure:
            - kube-1-*-azure-cloud-dev
      internal:
        namespace: mms-internal
        clusters:
          aws:
            - kube-1-*-aws-cloud-internal
      prod:
        namespace: mms-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud
          gcp:
            - kube-1-*-gcp-cloud
          azure:
            - kube-1-*-azure-cloud
      prod-gov:
        namespace: mms-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-prod
      qa:
        namespace: mms-qa
        clusters:
          aws:
            - kube-1-*-aws-cloud-dev
          gcp:
            - kube-1-*-gcp-cloud-dev
          azure:
            - kube-1-*-azure-cloud-dev
      staging:
        namespace: mms-staging
        clusters:
          aws:
            - kube-1-*-aws-cloud
          gcp:
            - kube-1-*-gcp-cloud
          azure:
            - kube-1-*-azure-cloud
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: mms
  type: deployment
