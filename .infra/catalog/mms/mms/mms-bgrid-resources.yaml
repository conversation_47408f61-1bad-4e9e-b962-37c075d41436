---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-bgrid-resources
spec:
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: mms
  type: deployment
  deployment:
    slack_channel: cloud
    deployment_group: mms-resources
    deploy_yaml_path: .infra/resources.yml
    workload_type: platformclaim
    argocd_project: mms
    repository:
      owner: 10gen
      name: mms
    labels:
      app: mms-bgrid-resources
      owner: mms
    targets:
      dev:
        namespace: mms-dev
        release_name: mms-bgrid-resources-dev
        labels:
          app: mms-bgrid-resources-dev
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      qa:
        namespace: mms-qa
        release_name: mms-bgrid-resources-qa
        labels:
          app: mms-bgrid-resources-qa
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      staging:
        namespace: mms-staging
        release_name: mms-bgrid-resources-staging
        labels:
          app: mms-bgrid-resources-staging
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      prod:
        namespace: mms-prod
        release_name: mms-bgrid-resources-prod
        labels:
          app: mms-bgrid-resources-prod
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
