---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-ui-regional-resources
spec:
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: mms-regional
  type: deployment
  deployment:
    slack_channel: cloud
    deployment_group: mms-resources
    deploy_yaml_path: .infra/resources.yml
    workload_type: platformclaim
    argocd_project: mms
    repository:
      owner: 10gen
      name: mms
    labels:
      app: mms-ui-regional-resources
      owner: mms
    targets:
      dev:
        namespace: mms-regional-dev
        release_name: mms-ui-regional-resources-dev
        labels:
          app: mms-ui-regional-resources-dev
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      qa:
        namespace: mms-regional-qa
        release_name: mms-ui-regional-resources-qa
        labels:
          app: mms-ui-regional-resources-qa
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      staging:
        namespace: mms-regional-staging
        release_name: mms-ui-regional-resources-staging
        labels:
          app: mms-ui-regional-resources-staging
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      prod:
        namespace: mms-regional-prod
        release_name: mms-ui-regional-resources-prod
        labels:
          app: mms-ui-regional-resources-prod
          owner: mms
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
