# ⚒️ MMS Deployment Workflows

This directory contains workflow templates related to MMS app deployments.

# How does it work now?

## [wt-deploy-gate-submit-patch]

Input:

1. `asset`: Github commit hash used to compute the parameters to [wt-evergreen-patch-submit]. Evergreen tests will actually run against the githash deployed to `staging`. You should only manually run this after confirming that `asset` has been deployed to all relevant apps (mandatory tests).

Flow:

1. Gets a list of MMS apps configured to run in `staging`
1. Gets s3 content from `s3://cloud-manager-release-artifacts-us-east-1/versions/pointers/mms-stage`
1. Submits a patch to the `mms` Evergreen project and triggers `prod_deploy_gate_push` tests on `asset`
1. FAIL the workflow if the tests failed

Output:

1. `status`: Evergreen patch status
1. `patch`: JSON object representing the Evergreen patch (artifact)

## [wt-deploy-mms-monolith]

This has been moved to the xgen-cloud/argo-workflows repository.

```mermaid
graph LR
  B[post to slack] --> C[validate asset]
  C --> |env!=prod| D[run migrations]
  C --> |env=prod| E[deploy apps]
  D --> E
  E --> |env=staging| F[run deploy gate]
  E --> |env!=staging| G[post to slack]
  F --> G
```

<br/>

Output: `None`

# Development & Testing

To safely make changes to these workflows, follow these testing instructions below. If your changes are dangerous (ymmv), consider committing a modified clone of the workflow and migrating CI/CD env by env to the new workflow by editing [mms.cue].

1. Test Python changes in [argo-workflows] repo: https://github.com/xgen-cloud/argo-workflows/pull/205
1. Push Python changes in [argo-workflows] repo to a Github branch `X`
1. Upload a test version of your workflow template and point it to branch `X` instead of master
1. Submit test workflows in the Argo WF UI in the [argo-workflows-executions-mms-mgmt] execution namespace

[argo-workflows]: https://github.com/xgen-cloud/argo-workflows
[argo-workflows-executions-mms-mgmt]: https://argo-workflows-dev.corp.mongodb.com/cron-workflows/argo-workflows-executions-mms-mgmt
[deploy.yml]: https://github.com/10gen/mms/blob/master/.infra/deploy.yml
[mms.cue]: https://github.com/xgen-cloud/kube-resources/blob/master/cue/argo/mgmt/mms.cue
[slack-notification-config]: https://github.com/xgen-cloud/kube-resources/blob/c1b97681539986e52a26067411a2fbb572981f81/apps/base/argo-workflows-executions/slack-notification-config.yaml#L31-L39
[wt-deploy-gate-submit-patch]: ./wt-validate-deploy-gate-submit-patch.yaml
[wt-deploy-mms-monolith]: https://github.com/xgen-cloud/argo-workflows/blob/master/wfgen/src/wfgen/workflows/wt_deploy_mms_monolith.py
[wt-gvergreen-patch-submit]: https://github.com/xgen-cloud/kube-resources/blob/d1ef212d8857184d37b2de789d192a4512c6a71c/argo/argo-workflows-executions/mgmt/templates/mms-mgmt/wt-deploy-gate-submit-patch.yaml#L113-L121
[.infra/catalog]: https://github.com/10gen/mms/tree/master/.infra/catalog
