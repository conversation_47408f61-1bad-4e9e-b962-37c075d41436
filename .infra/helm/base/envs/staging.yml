---
env:
  INNIT_HEAPDUMPS_BUCKET_PREFIX: javatools-files-staging
  KUBE_AUTHN_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC: authn-service-grpc.core-systems-staging.active.cloud.kube:8080
  KUBE_AUTHN_SERVICE_KUBE_HTTP_STAGING_ACTIVE_CLOUD_10GEN_CC: authn-service-http.core-systems-staging.active.cloud.kube:8081
  KUBE_AUTHN_SERVICE_KUBE_STAGING_GLOBAL_CLOUD_10GEN_CC: authn-service-grpc.core-systems-staging.global.cloud.kube:8080
  KUBE_AUTHN_SERVICE_KUBE_HTTP_STAGING_GLOBAL_CLOUD_10GEN_CC: authn-service-http.core-systems-staging.global.cloud.kube:8081
  KUBE_AUTHZ_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC: authz-service-grpc.core-systems-staging.active.cloud.kube:8080
  KUBE_CONFIG_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: config-service-grpc.growth-staging.us-east-1.aws.cloud.kube:8080
  KUBE_PAYMENTS_SERVICE_KUBE_STAGING_ACTIVE_AWS_CLOUD_STAGING_10GEN_CC: payments-service-grpc.payments-staging.active.cloud.kube:8080
  KUBE_EVENT_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: event-service-grpc.events-staging.active.cloud.kube:8080
  KUBE_EVENTS_API_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: events-api-grpc.events-staging.us-east-1.aws.cloud.kube:8080
  KUBE_EVENTS_API_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC: events-api-grpc.events-staging.active.cloud.kube:8080
  KUBE_MMS_AGENT_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC: mms-api-meshonly-http.mms-staging.active.cloud.kube:8080
  KUBE_API_REGISTRY_SERVICE_KUBE_GRPC_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: api-registry-grpc.api-registry-staging.us-east-1.aws.cloud.kube:8080
  KUBE_API_REGISTRY_SERVICE_KUBE_HTTP_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: api-registry-http.api-registry-staging.us-east-1.aws.cloud.kube:8081
  KUBE_API_REGISTRY_SERVICE_KUBE_GRPC_STAGING_US_EAST_2_AWS_CLOUD_10GEN_CC: api-registry-grpc.api-registry-staging.us-east-2.aws.cloud.kube:8080
  KUBE_API_REGISTRY_SERVICE_KUBE_HTTP_STAGING_US_EAST_2_AWS_CLOUD_10GEN_CC: api-registry-http.api-registry-staging.us-east-2.aws.cloud.kube:8081
  KUBE_CUSTOMER_METRICS_INGESTION_KUBE_HTTP_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: customer-metrics-ingestion-http.customer-metrics-staging.us-east-1.aws.cloud.kube:8081
  KUBE_COMMUNICATION_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC: communication-service-grpc.communication-staging.us-east-1.aws.cloud.kube:8080
  KUBE_SLSBACKUP_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_STAGING_10GEN_CC: slsbackup-grpc.slsbackup-staging.active.cloud-staging.kube:8080

  RESERVED_CODE_CACHE_SIZE: "192m"

authnServiceEnvoyFilter:
  authnServiceNamespace: core-systems-staging

splunk:
  index: mms-staging

consumers:
  mms-staging:
    - "*"

istio:
  # Exclude https, redis, mongo ports from being proxied by istio for perf/tuning reasons.
  podAnnotations:
    traffic.sidecar.istio.io/excludeOutboundPorts: "443,6379,26101,26300,27016,27017,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124"
