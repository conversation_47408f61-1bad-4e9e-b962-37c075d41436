---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud
      topology:
        aws:
          - us-east-1
      roles:
        - name: mms-ingest-staging-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-ingest
          allowAssumeRoleArns:
            - arn:aws:iam::************:role/mms-bgrid-resources-staging/bgrid-snapshot-validation-rw-staging-{{ .Provider }}-{{ .Region }}
            - arn:aws:iam::************:role/event-service-resources-staging/event-service-queue-producer-staging-{{ .Provider }}-{{ .Region }}
