---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  name: mms-nds-migrations
  fullnameOverride: mms-nds-migrations
  escalationPolicyName: Atlas Migrations
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: mongosync live imports
      rules:
        - alert: Too many failed mongosync live migrations
          expr: sum(increase(mms_nds_lm_planner_total{xgen_environment="prod",status="FAILED",migrationToolType="MONGOSYNC"}[15m])) >= 3
          labels:
            severity: warning
          annotations:
            summary: '{{ $value }} mongosync live migration(s) failed.'
            splunk: 'https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3Dmms%20%22PERFORM%20ERROR%22%20AND%20%22com.xgen.svc.nds.liveimport.planner%22&earliest=-24h%40h&latest=now'
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Migration+Alerts+Playbook#AtlasMigrationAlertsPlaybook-TooManyMigrationFailures'
        - alert: Long-running mongosync live migration state -- Commit to Ready for Write
          expr: avg(mms_nds_commit_write_ready_duration_seconds{xgen_environment="prod", migrationToolType="MONGOSYNC"}) > 180
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: 'Mongosync live migrations taking too long from when committing was issued to ready for write'
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Migration+Alerts+Playbook#AtlasMigrationAlertsPlaybook-LongRunningMigrationStages'
        - alert: KubeQuotaHigh for liveimport namespace
          annotations:
            description: 'Namespace {{ $labels.namespace }} is using {{ $value | humanizePercentage }} of its {{ $labels.resource }} quota.'
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/83Oh2msSz/liveimport-kube-metrics?orgId=20&viewPanel=158'
            summary: 'Namespace quota usage is high. Check if workloads are ok and if quota needs to be increased.'
          expr: kube_resourcequota{job="kube-state-metrics", type="used", namespace="liveimport-prod"} / ignoring(instance, job, type) (kube_resourcequota{job="kube-state-metrics", type="hard", namespace="liveimport-prod"} > 0) > 0.8
          for: 10m
          labels:
            severity: warning
        - alert: MongosyncAvailableMemoryLow
          for: 5m
          expr: (100 * (1 - container_memory_usage_bytes{container="mongosync", namespace="liveimport-prod"} / container_spec_memory_limit_bytes{container="mongosync", namespace="liveimport-prod"})) < 30
          annotations:
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            summary: 'Mongosync available memory is low'
            description: 'Mongosync available memory of {{ $labels }} is less than {{$value}} during last 5m'
          labels:
            severity: warning
        - alert: MongomirrorAvailableMemoryLow
          for: 5m
          expr: (100 * (1 - container_memory_usage_bytes{container="mongomirror", namespace="liveimport-prod"} / container_spec_memory_limit_bytes{container="mongomirror", namespace="liveimport-prod"})) < 30
          annotations:
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            summary: 'Mongomirror available memory is low'
            description: 'Mongomirror available memory of {{ $labels }} is less than {{$value}} during last 5m'
          labels:
            severity: warning
        - alert: HighRestartRate
          for: 1m
          expr: increase(kube_pod_container_status_restarts_total{namespace="liveimport-prod"}[3m]) > 2
          annotations:
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/83Oh2msSz/liveimport-kube-metrics?orgId=20&viewPanel=147'
            summary: 'High restart rate detected for {{ $labels.pod }}'
            description: 'Pod {{ $labels.namespace }}/{{ $labels.pod }} has restarted more than 2 times within the last 3 minutes.'
          labels:
            severity: warning
        - alert: PodNotReady
          for: 3m
          expr: count by (pod) (kube_pod_status_ready{namespace="liveimport-prod", condition!="true"} == 1) > 0
          annotations:
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/83Oh2msSz/liveimport-kube-metrics?orgId=20&viewPanel=157'
            summary: 'Pod not ready for more than 3 minutes'
            description: 'Pod {{ $labels.pod }} not ready for more than 3 minutes'
          labels:
            severity: warning
        - alert: Mongosync Last Terminated Reason OOMKilled
          expr: (kube_pod_container_status_last_terminated_reason{namespace="liveimport-prod", container="mongosync", xgen_environment="prod", reason="OOMKilled"}) > 0
          annotations:
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/83Oh2msSz/liveimport-kube-metrics?orgId=20&viewPanel=140'
            summary: 'Mongosync container has been killed due to OOM (Out of memory)'
            description: 'Pod {{ $labels.pod }} OOMKilled'
          labels:
            severity: warning
        - alert: Mongosync Terminated Reason OOMKilled
          expr: (kube_pod_container_status_terminated_reason{namespace="liveimport-prod", container="mongosync", xgen_environment="prod", reason="OOMKilled"}) > 0
          annotations:
            runbook_url: 'https://wiki.corp.mongodb.com/display/KERNEL/LiveImport+Helix+Alerts+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/83Oh2msSz/liveimport-kube-metrics?orgId=20&viewPanel=140'
            summary: 'Mongosync container has been killed due to OOM (Out of memory)'
            description: 'Pod {{ $labels.pod }} OOMKilled'
          labels:
            severity: warning
