---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud-dev
      topology:
        aws:
          - us-east-1
          - us-east-2
      roles:
        - name: mms-jobs-metrics-dev-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-jobs-metrics
          allowGetFederationToken: true
          allowAssumeRoleArns:
            - arn:aws:iam::************:role/event-service-resources-dev/event-service-queue-producer-dev-{{ .Provider }}-{{ .Region }}
            - arn:aws:iam::************:role/cloudcontrol-root
            - arn:aws:iam::************:role/cloudcontrol
