---

aws:
  partition: aws-us-gov
  account: gov-prod-1
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud-gov-prod
      topology:
        aws:
          - us-gov-west-1
      roles:
        - name: mms-jobs-atlas-prioritized-prod-gov-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-jobs-atlas-prioritized
          allowAssumeRoleArns:
            - arn:aws-us-gov:iam::************:role/cloudcontrol-root
            - arn:aws-us-gov:iam::************:role/cloudcontrol
            - arn:aws-us-gov:iam::************:role/event-service-resources-prod/event-service-queue-producer-prod-gov-{{ .Provider }}-{{ .Region }}
