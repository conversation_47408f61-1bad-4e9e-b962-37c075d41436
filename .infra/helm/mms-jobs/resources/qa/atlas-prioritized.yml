---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud-dev
      topology:
        aws:
          - us-east-1
          - us-east-2
      roles:
        - name: mms-jobs-atlas-prioritized-qa-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-jobs-atlas-prioritized
          allowGetFederationToken: true
          allowAssumeRoleArns:
            - arn:aws:iam::************:role/event-service-resources-qa/event-service-queue-producer-qa-{{ .Provider }}-{{ .Region }}
            - arn:aws:iam::************:role/cloudcontrol
            - arn:aws:iam::************:role/cloudcontrol-root
