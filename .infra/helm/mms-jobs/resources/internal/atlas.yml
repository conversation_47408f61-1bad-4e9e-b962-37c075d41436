---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud-internal
      topology:
        aws:
          - us-east-2
      roles:
        - name: mms-jobs-atlas-internal-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-jobs-atlas
          allowAssumeRoleArns:
            - arn:aws:iam::************:role/event-service-resources-internal/event-service-queue-producer-internal-{{ .Provider }}-{{ .Region }}
            - arn:aws:iam::************:role/cloudcontrol-root
