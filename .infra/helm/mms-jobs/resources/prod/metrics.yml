---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud
      topology:
        aws:
          - us-east-1
          - us-east-2
      roles:
        - name: mms-jobs-metrics-prod-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-jobs-metrics
          allowAssumeRoleArns:
#         TODO: add cloudcontrol and cloudcontrol-root if needed https://jira.mongodb.org/browse/SRE-1749
            - arn:aws:iam::************:role/event-service-resources-prod/event-service-queue-producer-prod-{{ .Provider }}-{{ .Region }}
