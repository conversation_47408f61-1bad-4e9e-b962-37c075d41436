---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud
      topology:
        aws:
          - us-east-1
      roles:
        - name: mms-bqproxy-staging-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-bqproxy
          allowAssumeRoleArns:
            - arn:aws:iam::************:role/event-service-resources-staging/event-service-queue-producer-staging-{{ .Provider }}-{{ .Region }}
