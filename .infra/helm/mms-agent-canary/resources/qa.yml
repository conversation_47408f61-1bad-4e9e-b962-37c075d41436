---

aws:
  account: xgen-prod
  iam:
    - kubeClusterTemplate: kube-1-{{ .Region }}-{{ .Provider }}-cloud-qa
      topology:
        aws:
          - us-east-1
      roles:
        - name: mms-agent-canary-qa-{{ .Provider }}-{{ .Region }}
          serviceAccountName: mms-agent-canary
          allowAssumeRoleArns:
            - arn:aws:iam::************:role/cloudcontrol
            - arn:aws:iam::************:role/cloudcontrol-root
