---
image:
  repository: ************.dkr.ecr.us-east-1.amazonaws.com/mms/mms

serviceAccount:
  create: true

xgenAppNameOverride: mms-ui-docker
xgenAppEnvOverride: mms-account

env:
  BASE_PORT: "8080"
  BASE_SSL_PORT: "21443"
  SECRETS_DIR: /run/secrets/10gen.cc/external-secrets/mms
  XGEN_PROMETHEUS_JMX_PORT: "9090"
  XGEN_APP_OVERRIDE: mms-account
  XGEN_APP_DIR: "/tmp/srv/10gen/app"
  OTEL_JAVAAGENT_ENABLED: true
  OTEL_SDK_DISABLED: false

hpa:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  triggers:
    - type: cpu
      metricType: Utilization
      metadata:
        value: "70"

additionalLabels:
  app: mms-account

listeners:
  default:
    appProtocol: http
    port:
      containerPort: 8080
      protocol: TCP

  javatools:
    appProtocol: grpc
    istio:
      disableServiceEntry: true
    port:
      name: javatools
      containerPort: 8887
      protocol: TCP

metricListeners:
  metrics:
    port:
      containerPort: 8130
      protocol: TCP
    podMonitor:
      podMetricsEndpoints:
        - scheme: "http"
          path: "/metrics"
      podTargetLabels:
        - app
        - environment
        - instance_id

  acc-jmx:
    port:
      name: mcs-acc-jmx-0
      containerPort: 9090
      protocol: TCP
    podMonitor:
      podMetricsEndpoints:
        - scheme: "http"
          path: "/metrics"
          metricRelabelConfigs:
            - regex: "^clusterid$"
              action: "labeldrop"
      podTargetLabels:
        - app
        - environment
        - instance_id

istio:
  activeRegion: us-east-1
