---

env:
  # TODO CLOUDP-338574: Remove this in favor of directly using MMS values
  KUBE_OFFICEIPS_SERVICE_DEV_GOV_US_GOV_WEST_1_AWS_CLOUD_GOV_DEV_10GEN_CC: officeips-grpc.officeips-dev.us-gov-west-1.aws.cloud-gov-dev.kube:8080

externalSecrets:
  - name: genkey.json
    path: mms/genkey.json
    file_to_env_var: GENKEY_PATH
    export: false
  - name: properties
    path: mms/properties
    export: false
  - name: toor.ssh.pub
    path: mms/toor.ssh.pub
    export: false
    file_to_env_var: NDS_SSH_TOOR_KEY
  - name: tse.ssh.pub
    path: mms/tse.ssh.pub
    file_to_env_var: NDS_SSH_TSE_KEY
    export: false
  - name: cert.bqproxy-dev
    path: mms/cert.bqproxy-dev
    file_to_env_var: QUERYABLE_CERT
    export: false
  - name: innit_heapdumps_key_id
  - name: innit_heapdumps_secret_key

consumers:
  vm-dev-gov:
    - lb-b-dev
    - lb-h-dev
  javatools-dev:
    - javatools
