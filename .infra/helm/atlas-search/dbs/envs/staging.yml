---
access:
  database:
    oktaGroups:
      10gen-cloud-data-read-metadata:
        roles:
          - databaseName: admin
            roleName: readAnyDatabase
      10gen-cloud-data-root:
        roles:
          - databaseName: admin
            roleName: atlasAdmin
  network:
    allowOfficeIps: true

clusters:
  atlas-search:
    instanceSizeName: M50_NVME
    replicaSet: true
    mongoDBMajorVersion: "7.0"
    replicationSpecs:
      - numShards: 1
        regionConfigs:
          - regionName: US_EAST_1
            providerName: AWS
            priority: 7
            electableSpecs:
              nodeCount: 3
          - regionName: US_EAST_2
            providerName: AWS
            priority: 0
            readOnlySpecs:
              nodeCount: 1
    # The cluster was created manually, so overriding the name to match the existing resource
    nameOverride: ia-stage-atlas-search
