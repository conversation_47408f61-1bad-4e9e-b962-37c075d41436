---
# Who is allowed to consume the service: https://github.com/xgen-cloud/helm-charts/blob/master/charts/xgen-service/values.yaml#L337
env:
  OTEL_JAVAAGENT_ENABLED: true
  OTEL_SDK_DISABLED: false
  GRPC_ADDITIONAL_LISTEN_ADDRESS: "0.0.0.0:8082"

externalSecrets:
  - name: authn_internal_client_secret
  - name: mongo-slsbackup-username
  - name: mongo-slsbackup-password
  - name: sentry_dsn

consumers:
  vm-dev:
    - lb-j-dev
  mms-dev:
    - mms-ui
    - mms-ui-admin
    - mms-api-public
    - javatools
  core-systems-dev:
    - mms-jobs-atlas
    - mms-jobs-atlas-prioritized
    - mms-jobs-common
    - mms-jobs-common-failover
    - mms-cron-jobs

authnServiceEnvoyFilter:
  enabled: false

listeners:
  grpc-8082:
    appProtocol: grpc
    port:
      name: grpc-8082
      containerPort: 8082
      protocol: TCP

istio:
  podAnnotations:
    traffic.sidecar.istio.io/excludeInboundPorts: "8082,10000"

tracing:
  enabled: true
