---
env:
  AZURE_WEB_IDENTITY_TOKEN_ENABLED: false
  GCP_WEB_IDENTITY_TOKEN_ENABLED: false

xgen_owner: mms-atlas-clusters-performance

pagerdutyService:
  enabled: true
  escalationPolicyName: Atlas Clusters Lead

prometheusServiceLevel:
  slos:
    - name: requests-availability
      objective: 99.5
      description: Atlas API SLO POC based on availability for HTTP request responses. Does not include edge
      sli:
        plugin:
          id: "sloth-common/istio/v1/availability"
      alerting:
        labels:
          xgen_owner: mms-atlas-clusters-performance
          xgen_app: mms-agent-regional
        pageAlert:
          disable: true
        ticketAlert:
          disable: true
    - name: requests-p99-latency
      objective: 99
      description: Atlas API SLO based on the 99th percentile of HTTP request responses. Does not include edge
      sli:
        plugin:
          id: "sloth-common/istio/v1/latency"
          options:
            bucket: "250"
      alerting:
        labels:
          xgen_owner: mms-atlas-clusters-performance
          xgen_app: mms-agent-regional
        pageAlert:
          disable: true
        ticketAlert:
          disable: true
    - name: requests-p75-latency
      objective: 75
      description: Atlas API SLO based on the 75th percentile of HTTP request responses. Does not include edge
      sli:
        plugin:
          id: "sloth-common/istio/v1/latency"
          options:
            bucket: "10"
      alerting:
        labels:
          xgen_owner: mms-atlas-clusters-performance
          xgen_app: mms-agent-regional
        pageAlert:
          disable: true
        ticketAlert:
          disable: true
    - name: requests-p50-latency
      objective: 50
      description: Atlas API SLO based on the median of HTTP request responses. Does not include edge
      sli:
        plugin:
          id: "sloth-common/istio/v1/latency"
          options:
            bucket: "5"
      alerting:
        labels:
          xgen_owner: mms-atlas-clusters-performance
          xgen_app: mms-agent-regional
        pageAlert:
          disable: true
        ticketAlert:
          disable: true
