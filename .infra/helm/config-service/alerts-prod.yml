---
# yamllint disable rule:quoted-strings rule:brackets rule:empty-lines
pagerdutyService:
  enabled: true
  fullnameOverride: config-service
  escalationPolicyName: "Atlas Growth On-Call"
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    # CONFIG SERVICE PLATFORM ALERTS
    - name: Config Service general alerts
      rules:
        - alert: "[Config Service General] Number of Running Pods in us-east-1 Region < 20"
          expr: sum(last_over_time(up{xgen_app="config-service",xgen_environment="prod",xgen_region="us-east-1",job="growth-prod/config-service"}[1m])) < 20
          for: 5m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Number of running Config Service pods is less than the desired minimum of 20
            description: "Grafana: http://go/growth-config-service-pods-prod"
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/*********/Growth+Playbook#GrowthPlaybook-NumberofRunningPodsisbelowminimum
            splunk: https://go/splunk-config-service-all-errors
        - alert: "[Config Service General] Argo Workflow Deployment Error"
          expr: sum(argo_workflows_deployment_workflow_execution_status{app="config-service", status!="Succeeded", env="{{ .Values.additionalLabels.xgen_environment }}"}) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.env }}) {{ $labels.alertname }}
            summary: Config Service Argo deployment failed in {{ $labels.env }}
            description: "Playbook: http://go/config-service-argo-error-playbook"
            runbook_url: http://go/config-service-argo-error-playbook
        - alert: "[Config Service General] Auto-scaler approaching maxReplicas"
          expr: kube_horizontalpodautoscaler_status_desired_replicas{xgen_environment="prod", horizontalpodautoscaler=~".*(config-service).*"} / kube_horizontalpodautoscaler_spec_max_replicas{xgen_environment="prod", horizontalpodautoscaler=~".*(config-service).*"} > 0.75
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Number of running Config Service pods is approaching the maximum number of replicas
            description: "Grafana: http://go/growth-config-service-pods-prod | Splunk: https://go/splunk-config-service-all-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/splunk-config-service-all-errors
        - alert: "[Config Service General] Config Service pod is crash looping"
          expr: sum(increase(kube_pod_container_status_waiting_reason{container="config-service",xgen_environment="prod",reason="CrashLoopBackOff"}[1m])) > 0
          for: 5m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Config Service pods are failing to start due to crash looping
            description: "Grafana: http://go/config-service-crash-loop-prod | ArgoCD: http://go/config-service-argocd-logs-prod | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[Config Service General] AuditSvc errors"
          expr: sum(increase(atlasgrowth_config_auditSvc_error_total{xgen_environment="prod"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in creating audit events for the Config Service.
            description: "Grafana: http://go/config-service-audit-logging-err-prod | Splunk: http://go/splunk-atlas-growth-config-auditsvc-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-config-auditsvc-errors
        - alert: "[Config Service General] ConfigAdminClient attach audit info errors"
          expr: sum(increase(mms_configservice_rpc_call_audit_info_error_total{xgen_environment="prod"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in attaching audit info for the Config Service Admin functions.
            description: "Grafana: http://go/config-service-admin-operations-prod | Splunk: http://go/splunk-atlas-growth-configadminclient-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminclient-errors
        - alert: "[Config Service General] Config grpc API: GetConfigs errors"
          expr: sum(increase(atlasgrowth_configService_get_configs_total{xgen_environment="prod", status="failure"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in getting configs for the Config Service SDK.
            description: "Grafana: http://go/config-service-api-operations-prod | Splunk: http://go/splunk-atlas-growth-configserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configserviceimpl-errors
        - alert: "[Config Service General] Config grpc API: GetConfigs errors - Critical"
          expr: sum(increase(atlasgrowth_configService_get_configs_total{xgen_environment="prod", status="failure"}[1m])) > 100
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of errors in getting configs for the Config Service SDK.
            description: "Grafana: http://go/config-service-api-operations-prod | Splunk: http://go/splunk-atlas-growth-configserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configserviceimpl-errors
        - alert: "[Config Service General] Config grpc API: low GetConfigs daily availability"
          expr: sum(increase(rpc_call_latency_seconds_total_count{grpc_service="com.xgen.cloud.services.config.ConfigService",xgen_environment="prod",grpc_method="GetConfigs",grpc_response_status="OK"}[12h]))/sum(increase(rpc_call_latency_seconds_total_count{grpc_service="com.xgen.cloud.services.config.ConfigService",xgen_environment="prod",grpc_method="GetConfigs"}[12h])) < .9995
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low GetConfigs daily availability.
            description: "Grafana: http://go/grafana-config-service-getconfigs-daily-avail | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[Config Service General] Config grpc API: low GetConfigs daily availability - Critical"
          expr: sum(increase(rpc_call_latency_seconds_total_count{grpc_service="com.xgen.cloud.services.config.ConfigService",xgen_environment="prod",grpc_method="GetConfigs",grpc_response_status="OK"}[12h]))/sum(increase(rpc_call_latency_seconds_total_count{grpc_service="com.xgen.cloud.services.config.ConfigService",xgen_environment="prod",grpc_method="GetConfigs"}[12h])) < .99
          for: 10m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Very low GetConfigs daily availability (likely to impact monthly SLO)
            description: "Grafana: http://go/grafana-config-service-getconfigs-daily-avail | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
    - name: Config Service Backing DB Alerts
      rules:
        - alert: "[Backing DB] Opcounters(update) Increased by 100% in 5 Minutes"
          expr: |
            sum(irate(mongodb_opcounters_update{cl_name="ia-prod-config-service"}[5m]))
            /
            sum(irate(mongodb_opcounters_update{cl_name="ia-prod-config-service"}[5m] offset 1h)) > 2
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: "Config Service Backing DB opcounters(update) have increased by more than 100% over the last 5 minutes"
            description: |
              UPDATE Opcounters for Config Service Backing DB have increased significantly.
              Grafana: http://go/ia-prod-config-service-opcounters-update
              Check cluster for details: http://go/ia-prod-config-service
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[Backing DB] Opcounters(query) Increased by 100% in 5 Minutes"
          expr: |
            sum(irate(mongodb_opcounters_query{cl_name="ia-prod-config-service"}[5m]))
            /
            sum(irate(mongodb_opcounters_query{cl_name="ia-prod-config-service"}[5m] offset 1h)) > 2
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: "Config Service Backing DB opcounters(query) have increased by more than 100% over the last 5 minutes"
            description: |
              QUERY Opcounters for Config Service Backing DB have increased significantly.
              Grafana: http://go/ia-prod-config-service-opcounters-query
              Check cluster for details: http://go/ia-prod-config-service
            runbook_url: http://go/growth-on-call-playbook

    - name: Config Service latency alerts
      rules:
        - alert: "[Config Service Latency] Config grpc API: High GetConfigs latency (p.99)"
          # threshold is in ms
          expr: (1000 * histogram_quantile(0.99, sum(rate(rpc_call_latency_seconds_total_bucket{grpc_service="com.xgen.cloud.services.config.ConfigService", xgen_environment="prod", grpc_method="GetConfigs"}[1m])) by (le))) > 80
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: High latency at the 99 percentile for the GetConfigs call of the Config Service SDK.
            description: "Grafana: http://go/grafana-ag-getconfigs-latency-p99 | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[Config Service Latency] Config grpc API: High GetConfigs latency (p.99) - Critical"
          # threshold is in ms
          expr: (1000 * histogram_quantile(0.99, sum(rate(rpc_call_latency_seconds_total_bucket{grpc_service="com.xgen.cloud.services.config.ConfigService", xgen_environment="prod", grpc_method="GetConfigs"}[1m])) by (le))) > 120
          for: 10m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: p99 latency for GetConfigs call higher than SLO.
            description: "Grafana: http://go/grafana-ag-getconfigs-latency-p99 | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[Config Service Latency] Config grpc API: High GetConfigs latency (p.99.9) - Critical"
          # threshold is in ms
          expr: (1000 * histogram_quantile(0.999, sum(rate(rpc_call_latency_seconds_total_bucket{grpc_service="com.xgen.cloud.services.config.ConfigService", xgen_environment="prod", grpc_method="GetConfigs"}[1m])) by (le))) > 250
          for: 10m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: p99.9 latency for GetConfigs call higher than SLO.
            description: "Grafana: http://go/grafana-ag-getconfigs-latency-p999 | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ConfigAdminService gRPC high latency"
          # threshold is in ms
          expr: |
            1000 * histogram_quantile(0.99,
              sum by (xgen_environment, grpc_method, le) (
                increase(rpc_call_latency_seconds_total_bucket{
                  grpc_service="com.xgen.cloud.services.config.ConfigAdminService",
                  xgen_environment="prod",
                }[1h])
              )
            ) > 3500
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{$labels.xgen_environment}}) {{ $labels.alertname }} - {{ $labels.grpc_method }}
            summary: "High p99 latency for gRPC method {{ $labels.grpc_method }}"
            description: "Grafana: http://go/grafana-configadminsvc-latency"
            runbook_url: http://go/config-service-latency-playbook
        - alert: "ConfigAdminService write gRPC high latency (Critical)"
          # threshold is in ms
          expr: |
            1000 * histogram_quantile(0.99,
              sum by (xgen_environment, grpc_method, le) (
                increase(rpc_call_latency_seconds_total_bucket{
                  grpc_service="com.xgen.cloud.services.config.ConfigAdminService",
                  xgen_environment="prod",
                  grpc_method=~"^(Create|Update|BulkUpdate|Delete).*"
                }[1h])
              )
            ) > 4500
          for: 4h
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }} - {{ $labels.grpc_method }}
            summary: "High p99 latency for gRPC method {{ $labels.grpc_method }}"
            description: "Grafana: http://go/grafana-configadminsvc-latency"
            runbook_url: http://go/config-service-latency-playbook
    - name: Config Service Config Namespace alerts
      rules:
        - alert: "[Config Namespaces] Config_Admin grpc API: ListConfigNamespaces errors"
          expr: sum(increase(atlasgrowth_configAdminService_list_config_namespaces_total{xgen_environment="prod", status="failure"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to List Config Namespaces for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-prod | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Config Namespaces] Config_Admin grpc API: GetConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_get_config_namespace_total{xgen_environment="prod", status="failure"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Get Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-prod | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Config Namespaces] Config_Admin grpc API: CreateConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_create_config_namespace_total{xgen_environment="prod", status="failure"}[1m])) > 3
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Create Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-prod | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Config Namespaces] Config_Admin grpc API: DeleteConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_delete_config_namespace_total{xgen_environment="prod", status="failure"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Delete Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-prod | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Config Namespaces] Config_Admin grpc API: UpdateConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_update_config_namespace_total{xgen_environment="prod", status="failure"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Update Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-prod | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
    - name: Config Service Application Property alerts
      rules:
        - alert: "[Application Properties] Config_Admin grpc API: getApplicationProperty errors"
          expr: sum(increase(atlasgrowth_configAdminService_get_application_property_total{status="failure",xgen_environment="prod"}[5m])) > 9
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Get Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props | Splunk: http://go/splunk-atlas-growth-app-prop-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-app-prop-errors
        - alert: "[Application Properties] Config_Admin grpc API: listApplicationProperties errors"
          expr: sum(increase(atlasgrowth_configAdminService_list_application_properties_total{status="failure",xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to List Application Properties for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-admin-app-props/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-oncall/AdminUI:listApplicationPropertyerrors
            splunk: http://go/splunk-atlas-growth-app-prop-errors
        - alert: "[Application Properties] Config_Admin grpc API: createApplicationProperties errors"
          expr: sum(increase(atlasgrowth_configAdminService_create_application_property_total{status="failure",xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Create Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props | Splunk: http://go/splunk-atlas-growth-app-prop-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-app-prop-errors
        - alert: "[Application Properties] Config_Admin grpc API: updateApplicationProperty errors"
          expr: sum(increase(atlasgrowth_configAdminService_update_application_property_total{status="failure",xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Update Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props | Splunk: http://go/splunk-atlas-growth-app-prop-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-app-prop-errors
        - alert: "[Application Properties] Config_Admin grpc API: deleteApplicationProperty errors"
          expr: sum(increase(atlasgrowth_configAdminService_delete_application_property_total{status="failure",xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Delete Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props | Splunk: http://go/splunk-atlas-growth-app-prop-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-app-prop-errors
        - alert: "[Application Properties] Config_Admin grpc API: listApplicationProperties error | Missing CoreConfig Item"
          expr: |
            sum(atlasgrowth_configAdminService_list_application_properties_missing_items{
              xgen_environment="prod"
            }) by (missing_item_id, missing_item_name) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: |
              ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
              Missing ID: {{ $labels.missing_item_id }}
              Missing Name: {{ $labels.missing_item_name }}
            summary: A CoreConfig item is missing from Application Properties.
            description: "Grafana: http://go/grafana-atlas-growth-app-properties/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-oncall/AdminUI:listApplicationPropertyerrors
            splunk: http://go/splunk-atlas-growth-app-prop-errors
        - alert: "[Application Properties] applicationPropertySvc populateGetConfigs error | Missing CoreConfig Item"
          expr: sum(atlasgrowth_applicationPropertySvc_missing_config_item{xgen_environment="prod"}) by (missing_item_id) > 0
          # Sometimes an item may not be found in both collections for a very short period of time during manual DB operations, so we have this 'for' condition
          # If an item isn't found in both coreConfig and applicationProperty collections within the timeframe below, the alert will fire.
          for: 1m
          labels:
            severity: critical
          annotations:
            alert_title_override: |
              ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
              Missing ID: {{ $labels.missing_item_id }}
            summary: CoreConfig item {{ $labels.missing_item_id }} is missing from Application Properties.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-missing-ids/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-appPropSvc-errors
    - name: Config Service Feature Flag Alerts
      rules:
        - alert: "[Feature Flag] Ingestion Cron - Error starting Feature Flag Ingestion Process"
          expr: sum(increase(atlasgrowth_FeatureFlagsIngestionCron_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Ingestion Cron Svc encountered an error when trying to schedule the Ingestion Cron Process
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-ingest-ff-cron-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-ingest-ff-cron-errors
        - alert: "[Feature Flag] Ingestion - Error during Feature Flag Ingestion Process"
          expr: sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Ingestion Process encountered an error
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-ingest-ff-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-ingest-ff-errors
        - alert: "[Feature Flag] Ingestion - Low daily success rate"
          expr: |
            (
              (sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_success_total{xgen_environment="prod"}[12h])))
              /
              (
                sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_success_total{xgen_environment="prod"}[12h]))
                +
                (sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_error_total{xgen_environment="prod"}[12h])) or vector(0))
              )
            ) < .9995
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low feature flag ingestion daily success rate
            description: "Grafana: http://go/config-service-ff-ingest-success-rate | Splunk: http://go/splunk-atlas-growth-ingest-ff-errors"
            runbook_url: http://go/config-service-ff-ingestion-fail-playbook
            splunk: http://go/splunk-atlas-growth-ingest-ff-errors
        - alert: "[Feature Flag] Ingestion - High feature flag ingestion latency from master"
          expr: |
            (
            sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_master_merge_min_sum{xgen_environment="prod"}[10m])) /
            sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_master_merge_min_count{xgen_environment="prod"}[10m]))
            ) > 100
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag took longer than 100 minutes to get ingested into Config Service {{ $labels.xgen_environment }} from master
            description: "Grafana: http://go/config-service-ff-ingestion-latency-master"
            runbook_url: http://go/config-service-ff-ingestion-latency-playbook
        - alert: "Feature Flag Ingestion - High feature flag ingestion latency from S3"
          expr: |
            (
             sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_push_defs_s3_ms_sum{xgen_environment="prod"}[10m])) /
             sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_push_defs_s3_ms_count{xgen_environment="prod"}[10m]))
            ) > 60 * 1000
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag took longer than 60 seconds to get ingested into Config Service from s3 in ({{ $labels.xgen_environment }})
            description: "Grafana: http://go/config-service-ff-ingestion-latency-s3"
            runbook_url: http://go/config-service-ff-ingestion-latency-playbook
        - alert: "[Feature Flag] Deletion Cron - Error starting Feature Flag Deletion Process"
          expr: sum(increase(atlasgrowth_ExpiredFeatureFlagsDeletionCron_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Deletion Cron Svc encountered an error when trying to schedule the Deletion Cron Process
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-delete-ff-cron-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-delete-ff-cron-errors
        - alert: "[Feature Flag] Deletion - Error during Feature Flag Deletion Process"
          expr: sum(increase(atlasgrowth_DeleteExpiredFeatureFlagsPoller_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Deletion Process encountered an error
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-delete-ff-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-delete-ff-errors
        - alert: "[Feature Flag] More than 1 FeatureFlagSyncStatusItem found"
          expr: sum(increase(atlasgrowth_featureFlagSyncStatusSvc_collection_validation_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: More than 1 FeatureFlagSyncStatusItem found in database
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-featureflagsyncstatus-error | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-featureflagsyncstatus-error
        - alert: "[Feature Flag] Decoding - Error converting Feature Flag from database to FeatureFlagItem java class when fetching Feature Flags from the database"
          expr: sum(increase(atlasgrowth_featureFlagSvc_decode_feature_flag_error_total{xgen_environment="prod", status="failure"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Encountered an error trying to convert a Feature Flag from the database to a FeatureFlagItem java class when trying to fetch Feature Flags from the database
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-featureflagsvc-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-featureflagsvc-errors
        - alert: "[Feature Flag] Metadata Reporting - Validation Error updating Feature Flag Metadata `lastRequestedAt` field"
          expr: sum(increase(atlasgrowth_featureFlagSvc_bulk_write_validation_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag updating of `lastRequestedAt` field encountered a validation error, missing name, metadata, or lastRequestedAt date
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-featureflagsvc-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-featureflagsvc-errors
        - alert: "[Feature Flag] featureFlagSvc populateGetConfigs error | Missing CoreConfig Item"
          expr: sum(atlasgrowth_featureFlagSvc_missing_config_item{xgen_environment="prod"}) by (missing_item_id) > 0
          # Sometimes an item may not be found in both collections for a very short period of time during manual DB operations, so we have this 'for' condition
          # If an item isn't found in both coreConfig and featureFlag collections within the timeframe below, the alert will fire.
          for: 1m
          labels:
            severity: critical
          annotations:
            alert_title_override: |
              ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
              Missing ID: {{ $labels.missing_item_id }}
            summary: Feature Flag CoreConfig item {{ $labels.missing_item_id }} is missing.
            description: "Grafana: http://go/configserviceinternalffgrafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-featureflagsvc-errors
        - alert: "[Feature Flag] Config grpc API: Metadata Reporting - Error updating Feature Flag Metadata `lastRequestedAt` field"
          expr: sum(increase(atlasgrowth_configService_update_feature_flag_metadata_total{xgen_environment="prod", status="failure"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag updating of `lastRequestedAt` field encountered an error
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-configserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configserviceimpl-errors
        - alert: "[Feature Flag] Config grpc API: Metadata Reporting - Unacknowledged WriteResult when updating Feature Flag Metadata `lastRequestedAt` field"
          expr: sum(increase(atlasgrowth_configService_update_feature_flag_metadata_unacknowledged_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag updating of `lastRequestedAt` field did not receive and acknowledgement from the database
            description: "Grafana: http://go/configserviceinternalffgrafana | Splunk: http://go/splunk-atlas-growth-configserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configserviceimpl-errors
        - alert: "[Feature Flag] Config_Admin grpc API: List Feature Flags - Error retrieving full list of Feature Flags"
          expr: sum(increase(atlasgrowth_configAdminService_list_feature_flags_total{xgen_environment="prod", status="failure"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Encountered an error when trying to list all Feature Flags
            description: "Grafana: http://go/configserviceadminffgrafana | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Feature Flag] Config_Admin grpc API: Update Feature Flag - Error updating a Feature Flag"
          expr: sum(increase(atlasgrowth_configAdminService_update_feature_flag_total{xgen_environment="prod", status="failure"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Encountered an error when trying to update a Feature Flag
            description: "Grafana: http://go/configserviceadminffgrafana | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Feature Flag] Config_Admin grpc API: Bulk Update Feature Flag Control List - Error bulk updating a Feature Flag's Control List"
          expr: sum(increase(atlasgrowth_configAdminService_bulk_update_feature_flag_control_list_total{xgen_environment="prod", status="failure"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Encountered an error when trying to bulk update a Feature Flag's Control List
            description: "Grafana: http://go/configserviceadminffgrafana | Splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-configadminserviceimpl-errors
        - alert: "[Config Service SDK in MMS] Config Service MMS Feature Flag Evaluation Util Error"
          expr: sum(increase(mms_feature_flag_evaluation_util_evaluation_errors_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: ConfigServiceFeatureFlagEvaluationUtil threw errors within the last 5 minutes. This class uses the Config Service SDK Wrapper to evaluate Feature Flags with respect to an Organization's Plan Type
            description: "Grafana: http://go/featureflagevaluationutil-grafana | Splunk: http://go/featureflagevaluationutil-splunk | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/featureflagevaluationutil-splunk
        - alert: "[Config Service SDK in MMS] FeatureFlagSvc Error Evaluating Config Service Feature Flag"
          expr: sum(increase(atlasgrowth_config_service_feature_flag_evaluation_error_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: FeatureFlagSvc threw an error when attempting to evaluate a Config Service Feature Flag.
            description: "Grafana: http://go/mms-cs-ff-eval-errors-grafana | Splunk: http://go/mms-cs-ffsvc-eval-errors-splunk | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/mms-cs-ffsvc-eval-errors-splunk
        - alert: "[Config Service SDK in MMS] FeatureFlagSvc Error Evaluating Config Service Feature Flag Caused Default Fallback"
          expr: sum(increase(mms_feature_flag_service_ui_fallback_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: FeatureFlagSvc threw an error when attempting to evaluate a Config Service Feature Flag during UI flow and caused default fallback.
            description: "Grafana: http://go/mms-cs-ff-eval-errors-grafana | Splunk: http://go/mms-cs-ffsvc-eval-errors-splunk | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/mms-cs-ffsvc-eval-errors-splunk
        - alert: "[Config Service SDK in MMS] AppSettings Error Evaluating Feature Flag Phase"
          expr: sum(increase(mms_config_service_feature_flag_phase_eval_failure_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: AppSettings threw an error when attempting to evaluate a Feature Flag's phase.
            description: "Grafana: http://go/mms-cs-ff-eval-errors-grafana | Splunk: http://go/mms-cs-ff-phase-eval-errors-splunk | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/mms-cs-ff-phase-eval-errors-splunk
        - alert: "[Config Service SDK in MMS] Request for soft-deleted feature flag exceeding alert threshold period"
          expr: sum by (featureFlag) (increase(config_service_sdk_soft_deleted_feature_flag_requested_total{xgen_environment="prod", xgen_region="us-east-1", serviceName="mms", shouldAlert="true"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Request received for a soft-deleted feature flag {{ $labels.featureFlag }} exceeding alert threshold period in MMS Config Service SDK implementation.
            description: "Grafana: http://go/grafana-config-sdk-soft-delete-ff-request-mms | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
