---
# yamllint disable rule:quoted-strings rule:brackets rule:empty-lines
pagerdutyService:
  enabled: true
  fullnameOverride: config-service
  escalationPolicyName: "Atlas Growth On-Call"
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    # CONFIG SERVICE PLATFORM ALERTS
    - name: GOV - Config Service general alerts
      rules:
        - alert: "[NON-PROD GOV Config Service General] Number of Running Pods in us-west-1 Region < 3"
          expr: sum(last_over_time(up{xgen_app="config-service",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",xgen_region="us-west-1",job="growth-{{ .Values.additionalLabels.xgen_environment }}/config-service"}[15m])) < 3
          for: 30m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Number of running Config Service pods is less than the desired minimum of 3
            description: "Grafana: http://go/growth-config-service-pods-gov-{{ .Values.additionalLabels.xgen_environment }} | Splunk: http://go/splunk-config-service-all-errors-{{ .Values.additionalLabels.xgen_environment }}-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-config-service-all-errors-{{ .Values.additionalLabels.xgen_environment }}-gov
        - alert: "[GOV NON-PROD Config Service General] Auto-scaler approaching maxReplicas"
          expr: kube_horizontalpodautoscaler_status_desired_replicas{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", horizontalpodautoscaler=~".*(config-service).*"} / kube_horizontalpodautoscaler_spec_max_replicas{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", horizontalpodautoscaler=~".*(config-service).*"} > 0.75
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Number of running Config Service pods is approaching the maximum number of replicas
            description: "Grafana: http://go/growth-config-service-pods-gov-{{ .Values.additionalLabels.xgen_environment }} | Splunk: https://go/splunk-config-service-all-errors | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/splunk-config-service-all-errors
        - alert: "[GOV NON-PROD Config Service General] Config Service pod is crash looping"
          expr: sum(increase(kube_pod_container_status_waiting_reason{container="config-service",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",reason="CrashLoopBackOff"}[1m])) > 0
          for: 5m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Config Service pods are failing to start due to crash looping (blocks branch cut and QA release process)
            description: "Grafana: http://go/config-service-crash-loop-non-prod-gov-{{ .Values.additionalLabels.xgen_environment }} | ArgoCD: http://go/config-service-argocd-logs-non-prod-gov-{{ .Values.additionalLabels.xgen_environment }} | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service General] AuditSvc errors"
          expr: sum(increase(atlasgrowth_config_auditSvc_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[20m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in creating audit events for the Config Service.
            description: "Grafana: http://go/config-service-audit-logging-err-gov-dev | Splunk: http://go/splunk-atlas-growth-config-auditsvc-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service General] ConfigAdminClient attach audit info errors"
          expr: sum(increase(mms_configservice_rpc_call_audit_info_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[20m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in attaching audit info for the Config Service Admin functions.
            description: "Grafana: http://go/config-service-admin-operations-gov-dev | Splunk: http://go/splunk-growth-configadminclient-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service General] Config grpc API: GetConfigs errors"
          expr: sum(increase(atlasgrowth_configService_get_configs_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[1m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in getting configs for the Config Service SDK.
            description: "Grafana: http://go/config-service-api-operations-gov-dev | Splunk: http://go/splunk-growth-configserviceimpl-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service General] Config grpc API: low GetConfigs daily availability"
          expr: sum(increase(rpc_call_latency_seconds_total_count{grpc_service="com.xgen.cloud.services.config.ConfigService",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",grpc_method="GetConfigs",grpc_response_status="OK"}[12h]))/sum(increase(rpc_call_latency_seconds_total_count{grpc_service="com.xgen.cloud.services.config.ConfigService",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",grpc_method="GetConfigs"}[12h])) < .9995
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low GetConfigs daily availability.
            description: "Grafana: http://go/grafana-config-svc-getconfigs-daily-avail-gov-dev | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
    - name: GOV - Config Service latency alerts
      rules:
        - alert: "[NON-PROD GOV Config Service Latency] Config grpc API: High GetConfigs latency (p.99)"
          # threshold is in ms
          expr: (1000 * histogram_quantile(0.99, sum(rate(rpc_call_latency_seconds_total_bucket{grpc_service="com.xgen.cloud.services.config.ConfigService", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", grpc_method="GetConfigs"}[1m])) by (le))) > 200
          for: 15m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: p99 latency for GetConfigs call higher than SLO.
            description: "Grafana: http://go/grafana-ag-getconfigs-latency-p99-gov-dev | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ConfigAdminService gRPC high latency"
          # threshold is in ms
          expr: |
            1000 * histogram_quantile(0.99,
              sum by (xgen_environment, grpc_method, le) (
                increase(rpc_call_latency_seconds_total_bucket{
                  grpc_service="com.xgen.cloud.services.config.ConfigAdminService",
                  xgen_environment=~"{{ .Values.additionalLabels.xgen_environment }}",
                }[1h])
              )
            ) > 3500
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{$labels.xgen_environment}}) {{ $labels.alertname }} - {{ $labels.grpc_method }}
            summary: "High p99 latency for gRPC method {{ $labels.grpc_method }}"
            description: "Grafana: http://go/grafana-configadminsvc-latency-gov"
            runbook_url: http://go/config-service-latency-playbook
        - alert: "ConfigAdminService write gRPC high latency (Critical)"
          # threshold is in ms
          expr: |
            1000 * histogram_quantile(0.99,
              sum by (xgen_environment, grpc_method, le) (
                increase(rpc_call_latency_seconds_total_bucket{
                  grpc_service="com.xgen.cloud.services.config.ConfigAdminService",
                  xgen_environment=~"{{ .Values.additionalLabels.xgen_environment }}",
                  grpc_method=~"^(Create|Update|BulkUpdate|Delete).*"
                }[1h])
              )
            ) > 4500
          for: 4h
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }} - {{ $labels.grpc_method }}
            summary: "High p99 latency for gRPC method {{ $labels.grpc_method }}"
            description: "Grafana: http://go/grafana-configadminsvc-latency-gov"
            runbook_url: http://go/config-service-latency-playbook
    - name: GOV - Config Service Config Namespace alerts
      rules:
        - alert: "[NON-PROD GOV Config Namespaces] Config_Admin grpc API: ListConfigNamespaces errors"
          expr: sum(increase(atlasgrowth_configAdminService_list_config_namespaces_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to List Config Namespaces for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-gov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-err-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Namespaces] Config_Admin grpc API: GetConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_get_config_namespace_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Get Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-gov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-err-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Namespaces] Config_Admin grpc API: CreateConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_create_config_namespace_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Create Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-gov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-err-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Namespaces] Config_Admin grpc API: DeleteConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_delete_config_namespace_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Delete Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-gov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-err-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Namespaces] Config_Admin grpc API: UpdateConfigNamespace errors"
          expr: sum(increase(atlasgrowth_configAdminService_update_config_namespace_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Update Config Namespace for the configAdmin grpc API.
            description: "Grafana: http://go/config-service-admin-namespaces-gov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-err-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
    - name: GOV - Config Service Application Property alerts
      rules:
        - alert: "[NON-PROD GOV Application Properties] Config_Admin grpc API: getApplicationProperty errors"
          expr: sum(increase(atlasgrowth_configAdminService_get_application_property_total{status="failure",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 9
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Get Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-gov-dev | Splunk: http://go/splunk-atlas-growth-app-prop-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Application Properties] Config_Admin grpc API: listApplicationProperties errors"
          expr: sum(increase(atlasgrowth_configAdminService_list_application_properties_total{status="failure",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to List Application Properties for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-gov-dev | Splunk: http://go/splunk-atlas-growth-app-prop-errors-dev-gov | http://go/growth-oncall/AdminUI:listApplicationPropertyerrors"
        - alert: "[NON-PROD GOV Application Properties] Config_Admin grpc API: createApplicationProperties errors"
          expr: sum(increase(atlasgrowth_configAdminService_create_application_property_total{status="failure",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Create Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-gov-dev | Splunk: http://go/splunk-atlas-growth-app-prop-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Application Properties] Config_Admin grpc API: updateApplicationProperty errors"
          expr: sum(increase(atlasgrowth_configAdminService_update_application_property_total{status="failure",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors in calls to Update Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-gov-dev | Splunk: http://go/splunk-atlas-growth-app-prop-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Application Properties] Config_Admin grpc API: deleteApplicationProperty errors"
          expr: sum(increase(atlasgrowth_configAdminService_delete_application_property_total{status="failure",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in calls to Delete Application Property for the configAdmin grpc API.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-gov-dev | Splunk: http://go/splunk-atlas-growth-app-prop-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Application Properties] Config_Admin grpc API: listApplicationProperties error | Missing CoreConfig Item"
          expr: |
            sum(atlasgrowth_configAdminService_list_application_properties_missing_items{
            xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"
            }) by (xgen_environment, missing_item_id, missing_item_name) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: |
              ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
              Missing ID: {{ $labels.missing_item_id }}
              Missing Name: {{ $labels.missing_item_name }}
            summary: A CoreConfig item is missing from Application Properties.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-oncall/AdminUI:listApplicationPropertyerrors
            splunk: http://go/splunk-atlas-growth-app-prop-errors-{{ $labels.xgen_environment }}
        - alert: "[NON-PROD GOV  Application Properties] applicationPropertySvc populateGetConfigs error | Missing CoreConfig Item"
          expr: sum(atlasgrowth_applicationPropertySvc_missing_config_item{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}) by (missing_item_id) > 0
          # Sometimes an item may not be found in both collections for a very short period of time during manual DB operations, so we have this 'for' condition
          # If an item isn't found in both coreConfig and applicationProperty collections within the timeframe below, the alert will fire.
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: |
              ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
              Missing ID: {{ $labels.missing_item_id }}
            summary: CoreConfig item {{ $labels.missing_item_id }} is missing from Application Properties.
            description: "Grafana: http://go/grafana-atlas-growth-app-props-missing-ids-{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-appPropSvc-errors-{{ $labels.xgen_environment }}
    - name: GOV - Config Service Feature Flag Alerts
      rules:
        - alert: "[NON-PROD GOV Feature Flag] Ingestion Cron - Error starting Feature Flag Ingestion Process"
          expr: sum(increase(atlasgrowth_FeatureFlagsIngestionCron_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Ingestion Cron Svc encountered an error when trying to schedule the Ingestion Cron Process
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-atlas-growth-ingest-ff-cron-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Ingestion - Error during Feature Flag Ingestion Process"
          expr: sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Ingestion Process encountered an error
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-atlas-growth-ingest-ff-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[Feature Flag] Ingestion - Low daily success rate"
          expr: |
            (
              (sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_success_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[12h])))
              /
              (
                sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_success_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[12h]))
                +
                (sum(increase(atlasgrowth_IngestFeatureFlagsRunnable_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[12h])) or vector(0))
              )
            ) < .9995
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low feature flag ingestion daily success rate
            description: "Grafana: http://go/config-service-ff-ingest-success-rate-gov | Splunk: http://go/splunk-atlas-growth-ingest-ff-errors-gov"
            runbook_url: http://go/config-service-ff-ingestion-fail-playbook
        - alert: "[Feature Flag] Ingestion - High feature flag ingestion latency from master"
          expr: |
            (
            sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_master_merge_min_sum{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) /
            sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_master_merge_min_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m]))
            ) > 100
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag took longer than 100 minutes to get ingested into Config Service {{ $labels.xgen_environment }} from master
            description: "Grafana: http://go/config-service-ff-ingestion-latency-master-gov"
            runbook_url: http://go/config-service-ff-ingestion-latency-playbook
        - alert: "Feature Flag Ingestion - High feature flag ingestion latency from S3"
          expr: |
            (
             sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_push_defs_s3_ms_sum{xgen_environment=~"{{ .Values.additionalLabels.xgen_environment }}"}[10m])) /
             sum by(xgen_environment) (rate(atlasgrowth_IngestFeatureFlagsRunnable_ingestion_latency_from_push_defs_s3_ms_count{xgen_environment=~"{{ .Values.additionalLabels.xgen_environment }}"}[10m]))
            ) > 60 * 1000
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag took longer than 60 seconds to get ingested into Config Service from s3 in ({{ $labels.xgen_environment }})
            description: "Grafana: http://go/config-service-ff-ingestion-latency-s3-gov"
            runbook_url: http://go/config-service-ff-ingestion-latency-playbook
        - alert: "[NON-PROD GOV Feature Flag] Deletion Cron - Error starting Feature Flag Deletion Process"
          expr: sum(increase(atlasgrowth_ExpiredFeatureFlagsDeletionCron_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Deletion Cron Svc encountered an error when trying to schedule the Deletion Cron Process
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-atlas-growth-delete-ff-cron-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Deletion - Error during Feature Flag Deletion Process"
          expr: sum(increase(atlasgrowth_DeleteExpiredFeatureFlagsPoller_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Feature Flag Deletion Process encountered an error
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-atlas-growth-delete-ff-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] More than 1 FeatureFlagSyncStatusItem found"
          expr: sum(increase(atlasgrowth_featureFlagSyncStatusSvc_collection_validation_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: More than 1 FeatureFlagSyncStatusItem found in database
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | http://go/splunk-atlas-growth-ffsyncstatus-error-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Metadata Reporting - Validation Errors updating Feature Flag Metadata `lastRequestedAt` field"
          expr: sum(increase(atlasgrowth_featureFlagSvc_bulk_write_validation_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in validation errors when bulk writing `lastRequestedAt` field (missing name, metadata, or lastRequestedAt date)
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-atlas-growth-featureflagsvc-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] featureFlagSvc populateGetConfigs error | Missing CoreConfig Item"
          expr: sum(atlasgrowth_featureFlagSvc_missing_config_item{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}) by (missing_item_id) > 0
          # Sometimes an item may not be found in both collections for a very short period of time during manual DB operations, so we have this 'for' condition
          # If an item isn't found in both coreConfig and featureFlag collections within the timeframe below, the alert will fire.
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: |
              ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
              Missing ID: {{ $labels.missing_item_id }}
            summary: Feature Flag CoreConfig item {{ $labels.missing_item_id }} is missing.
            description: "Grafana: http://go/configserviceinternalffgrafanagov-{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-featureflagsvc-errors-{{ $labels.xgen_environment }}
        - alert: "[NON-PROD GOV Feature Flag] Config grpc API: Metadata Reporting - Errors updating Feature Flag Metadata `lastRequestedAt` field"
          expr: sum(increase(atlasgrowth_configService_update_feature_flag_metadata_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors when updating `lastRequestedAt` field
            description: "Grafana: http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-growth-configserviceimpl-errors-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Config grpc API: Metadata Reporting - Unacknowledged WriteResults when updating Feature Flag Metadata `lastRequestedAt` field"
          expr: sum(increase(atlasgrowth_configService_update_feature_flag_metadata_unacknowledged_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in db unacknowledged errors when updating `lastRequestedAt` field
            description: "Grafana:  http://go/configserviceinternalffgrafanagov-dev | Splunk: http://go/splunk-growth-configserviceimpl-errors-dev-gov  | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Config_Admin grpc API: List Feature Flags - Errors retrieving full list of Feature Flags"
          expr: sum(increase(atlasgrowth_configAdminService_list_feature_flags_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors when trying to list all Feature Flags
            description: "Grafana: http://go/configserviceadminffgrafanagov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-errors-devgov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Config_Admin grpc API: Update Feature Flag - Errors updating a Feature Flag"
          expr: sum(increase(atlasgrowth_configAdminService_update_feature_flag_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors when trying to update a Feature Flag
            description: "Grafana: http://go/configserviceadminffgrafanagov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-errors-devgov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Config_Admin grpc API: Bulk Update Feature Flag Control List - Errors bulk updating a Feature Flag's Control List"
          expr: sum(increase(atlasgrowth_configAdminService_bulk_update_feature_flag_control_list_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="failure"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Increase in errors when trying to bulk update a Feature Flag's Control List
            description: "Grafana: http://go/configserviceadminffgrafanagov-dev | Splunk: http://go/splunk-growth-configadminserviceimpl-errors-devgov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Feature Flag] Config Service MMS Feature Flag Evaluation Util Error"
          expr: sum(increase(mms_feature_flag_evaluation_util_evaluation_errors_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: ConfigServiceFeatureFlagEvaluationUtil threw errors within the last 5 minutes. This class uses the Config Service SDK Wrapper to evaluate Feature Flags with respect to an Organization's Plan Type
            description: "Grafana: http://go/featureflagevaluationutil-grafana-devgov | Splunk: http://go/featureflagevaluationutil-splunk-devgov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service SDK in MMS] FeatureFlagSvc Error Evaluating Config Service Feature Flag"
          expr: sum(increase(atlasgrowth_config_service_feature_flag_evaluation_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: FeatureFlagSvc threw an error when attempting to evaluate a Config Service Feature Flag.
            description: "Grafana: http://go/mms-cs-ff-eval-errors-grafana-dev-gov | Splunk: http://go/mms-cs-ffsvc-eval-errors-splunk-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service SDK in MMS] FeatureFlagSvc Error Evaluating Config Service Feature Flag Caused Default Fallback"
          expr: sum(increase(mms_feature_flag_service_ui_fallback_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: FeatureFlagSvc threw an error when attempting to evaluate a Config Service Feature Flag during UI flow and caused default fallback.
            description: "Grafana: http://go/mms-cs-ff-eval-errors-grafana-dev-gov | Splunk: http://go/mms-cs-ffsvc-eval-errors-splunk-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service SDK in MMS] AppSettings Error Evaluating Feature Flag Phase"
          expr: sum(increase(mms_config_service_feature_flag_phase_eval_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: AppSettings threw an error when attempting to evaluate a Feature Flag's phase.
            description: "Grafana: http://go/mms-cs-ff-eval-errors-grafana-dev-gov | Splunk: http://go/mms-cs-ff-phase-eval-errors-splunk-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV Config Service SDK in MMS] Request for soft-deleted feature flag exceeding alert threshold period"
          expr: sum by (featureFlag) (increase(config_service_sdk_soft_deleted_feature_flag_requested_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_region="us-west-1", serviceName="mms", shouldAlert="true"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Request received for a soft-deleted feature flag {{ $labels.featureFlag }} exceeding alert threshold period in MMS Config Service SDK implementation.
            description: "Grafana: http://go/graf-config-sdk-soft-delete-ff-request-mms-gov-dev | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
    # MIGRATION RELATED ALERTS
    - name: GOV - MMS Config Service Migration
      rules:
        - alert: "[NON-PROD GOV MMS Migration] Application property mismatch"
          expr: sum(increase(mms_config_service_migration_application_property_mismatch_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Mismatch between AppSetting property and Config Service values.
            description: "Grafana: http://go/grafana-config-migration-mismatch-gov-dev | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD GOV MMS Migration] Errors with application property migration boolean getter"
          expr: sum(increase(mms_config_service_migration_app_settings_get_boolean_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ $labels.xgen_environment }}) {{ $labels.alertname }}
            summary: Non-zero errors in the "isAppPropsMigrationPhaseEnabledInConfigService" call retrieve the application property migration boolean values.
            description: "Grafana: http://go/grafana-config-migration-getter-err-gov-dev | Splunk: http://go/splunk-config-migration-getter-err-dev-gov | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
