package com.xgen.svc.brs.svc;

import static com.xgen.cloud.brs.GridTestConstants.DEFAULT_S3OPLOGSTORE_ID;
import static org.junit.Assert.assertEquals;

import com.mongodb.DBObject;
import com.xgen.cloud.brs.GridTestConstants;
import com.xgen.cloud.brs.GridUnitTestHelpers;
import com.xgen.cloud.brs.core._private.dao.ResourceUsageDao;
import com.xgen.cloud.brs.core._public.model.BackupStatus;
import com.xgen.cloud.brs.core._public.model.OplogStoreIdentifier;
import com.xgen.cloud.brs.core._public.model.SnapshotStoreIdentifier;
import com.xgen.cloud.brs.core._public.model.oplog.OplogSlice;
import com.xgen.cloud.brs.core._public.store.OplogUtil;
import com.xgen.cloud.brs.core._public.store.oplog.OplogStore;
import com.xgen.cloud.brs.daemon._public.grid.BackupDaemonConfiguration;
import com.xgen.cloud.brs.daemon._public.grid.BackupInjector;
import com.xgen.cloud.brs.daemon._public.grid.dao.ClusterStatusDao;
import com.xgen.cloud.common.brs._public.model.OplogStoreType;
import com.xgen.cloud.common.brs._public.model.SnapshotStoreType;
import com.xgen.svc.brs.grid.GridTestHelpers;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class OplogUtilIntTests {

  private static final BackupDaemonConfiguration CONFIG = GridTestHelpers.getConfig();
  private static final BackupInjector BACKUP_INJECTOR = GridTestHelpers.getBackupInjector();
  private static final ObjectId GROUP_ID = ObjectId.get();
  private static final ObjectId JOB_ID = ObjectId.get();
  private static final String RS_ID_S3 = "rs-id-with-s3-oplog";
  private static final String RS_ID_MONGO = "rs-id-with-mongo-oplog";

  private static ResourceUsageDao _resourceUsageDao = BACKUP_INJECTOR.getResourceUsageDao();
  private static ClusterStatusDao _clusterStatusDao = BACKUP_INJECTOR.getClusterStatusDao();

  private BackupStatus _backupStatusS3Oplog;
  private BackupStatus _backupStatusMongoOplog;

  @Before
  public void setup() {
    GridTestHelpers.cleanCollections(CONFIG, BACKUP_INJECTOR);
    GridTestHelpers.ensureDefaultConfigs();
    GridTestHelpers.initStandardS3OplogConfiguration(BACKUP_INJECTOR);

    _backupStatusS3Oplog =
        GridTestHelpers.createReplicaSet(
            BACKUP_INJECTOR,
            GROUP_ID,
            RS_ID_S3,
            SnapshotStoreIdentifier.of(SnapshotStoreType.blockstore, "blockstore"),
            OplogStoreIdentifier.of(
                OplogStoreType.s3OplogStore,
                DEFAULT_S3OPLOGSTORE_ID,
                JOB_ID.toHexString() + ".slices"),
            GridTestConstants.DEFAULT_STORAGE_ENGINE,
            GridTestConstants.DEFAULT_VERSION_STRING,
            GridTestHelpers.getMongoTestOptions());

    _backupStatusMongoOplog =
        GridTestHelpers.createReplicaSet(
            BACKUP_INJECTOR,
            GROUP_ID,
            RS_ID_MONGO,
            SnapshotStoreIdentifier.of(SnapshotStoreType.blockstore, "blockstore"),
            OplogStoreIdentifier.of(OplogStoreType.oplogStore, "oplog1", "backupoplogs.slice"),
            GridTestConstants.DEFAULT_STORAGE_ENGINE,
            GridTestConstants.DEFAULT_VERSION_STRING,
            GridTestHelpers.getMongoTestOptions());
  }

  @Test
  public void updateOplogTTL_MongoStore() throws Exception {
    final Instant now = Instant.now();
    final BSONTimestamp nowTimestamp = new BSONTimestamp((int) now.getEpochSecond(), 1);
    final DBObject jobDocument = _backupStatusMongoOplog.getJobDocument();

    jobDocument.put("headSliceEndDate", new Date(now.toEpochMilli()));
    jobDocument.put("lastOplogPush", nowTimestamp);
    final BackupStatus status = new BackupStatus(jobDocument);

    // Adjust TTL back, such that we expect this routine to lower it.
    final OplogStore oplogStore = BACKUP_INJECTOR.getOplogStore(status);
    assertEquals("sanity", OplogStoreType.oplogStore, oplogStore.getType());

    if (oplogStore.getCurrentTTLSetting() > 0) {
      oplogStore.ensureIndexesTTL(oplogStore.getCurrentTTLSetting() * 2);
    }

    int sliceStartTime = (int) now.minus(Duration.ofHours(33)).getEpochSecond();
    int sliceEndTime = (int) now.minus(Duration.ofHours(32)).getEpochSecond();

    OplogSlice testOplogSlice =
        GridUnitTestHelpers.createRandomOplogSlice(
            GROUP_ID, RS_ID_MONGO, sliceStartTime, sliceEndTime);
    oplogStore.add(testOplogSlice, false);

    int sliceStartTime2 = (int) now.minus(Duration.ofHours(32)).getEpochSecond();
    int sliceEndTime2 = (int) now.minus(Duration.ofHours(30)).getEpochSecond();

    OplogSlice testOplogSlice2 =
        GridUnitTestHelpers.createRandomOplogSlice(
            GROUP_ID, RS_ID_MONGO, sliceStartTime2, sliceEndTime2);
    oplogStore.add(testOplogSlice2, false);
    assertEquals("sanity: expected 2 slices", 2, oplogStore.countSlices(GROUP_ID, RS_ID_MONGO));

    assertEquals(
        "TTL value was calculated as unexpected value",
        111600L,
        OplogUtil.updateOplogTTL(
            status, oplogStore, _resourceUsageDao, _clusterStatusDao, nowTimestamp.getTime()));
    assertEquals(
        "TTL value on index was not changed to expected value",
        111600L,
        oplogStore.getCurrentTTLSetting());
  }

  @Test
  public void updateOplogTTL_S3OplogStore_Noop() throws Exception {
    final Instant now = Instant.now();
    final BSONTimestamp lastOplogPush = new BSONTimestamp((int) now.getEpochSecond(), 0);
    final BSONTimestamp snapshotTimestamp = lastOplogPush;

    final DBObject jobDocument = _backupStatusS3Oplog.getJobDocument();
    jobDocument.put("headSliceEndDate", new Date(now.toEpochMilli()));
    jobDocument.put("lastOplogPush", lastOplogPush);
    final BackupStatus status = new BackupStatus(jobDocument);

    final OplogStore s3OplogStore = BACKUP_INJECTOR.getOplogStore(status);
    assertEquals("sanity", OplogStoreType.s3OplogStore, s3OplogStore.getType());

    assertEquals(
        "S3OplogStore should always return -1 because does not support TTL expiration",
        -1,
        OplogUtil.updateOplogTTL(status, s3OplogStore, null, null, snapshotTimestamp.getTime()));
  }
}
