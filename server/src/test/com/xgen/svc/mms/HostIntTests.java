package com.xgen.svc.mms;

import static com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc.DEPRECATED_MONGODB_VERSIONS_SOURCE_APP_SETTING;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.AUTOMATION_VERSIONS_DIRECTORY;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.AUTOMATION_VERSIONS_SOURCE;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.deployment._public.model.MongoDbVersion;
import com.xgen.cloud.deployment._public.model.VersionManifest;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class HostIntTests extends JUnit5BaseSvcTest {

  @Inject private AutomationMongoDbVersionSvc _svc;

  @Inject private AppSettings _settings;

  private final String basePath = "./test_mongodb_releases/";

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _settings.setProp(
        AUTOMATION_VERSIONS_SOURCE.value,
        DEPRECATED_MONGODB_VERSIONS_SOURCE_APP_SETTING,
        AppSettings.SettingType.MEMORY);
    _settings.setProp(
        AUTOMATION_VERSIONS_DIRECTORY.value, basePath, AppSettings.SettingType.MEMORY);
    _svc.autoUpdateDefaultVersions();
    _svc.invalidateVersionManifestCache();
  }

  @Test
  public void testGetMaxConnsWithAtlasVersionsDefault() {
    testGetMaxConnsWithVersionDefault(_svc.getAtlasVersionManifest());
  }

  @Test
  public void testGetMaxConnsWithCmVersionsDefault() {
    testGetMaxConnsWithVersionDefault(_svc.getCmVersionManifest());
  }

  private void testGetMaxConnsWithVersionDefault(final VersionManifest manifest) {
    final List<MongoDbVersion> versions = manifest.getDefaultVersions();
    assertFalse(
        versions.isEmpty(), "Expected to find at least some versions, was: " + versions.size());

    for (final MongoDbVersion version : versions) {
      final Host host = new Host();
      host.setVersion(version.getName());
      try {
        assertNotNull(host.getMaxConnsWithVersionDefault());
      } catch (IllegalArgumentException exc) {
        fail(
            "Please update Host::getMaxConnsWithVersionDefault for new MongoDB version '"
                + version.getVersion()
                + "'");
      }
    }
  }
}
