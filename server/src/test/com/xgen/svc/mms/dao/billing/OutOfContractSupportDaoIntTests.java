package com.xgen.svc.mms.dao.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mongodb.client.result.UpdateResult;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.OOCSRecord;
import com.xgen.svc.mms.model.billing.OutOfContractSupport;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class OutOfContractSupportDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private OutOfContractSupportDao oocsDao;

  @Test
  public void testFindByOrgId() {
    ObjectId orgId = new ObjectId();
    OutOfContractSupport oocs =
        OutOfContractSupport.builder()
            .id(new ObjectId())
            .orgId(orgId)
            .activationCode("activationCode2")
            .effectiveDate(DateTimeUtils.dateOf(LocalDate.of(2024, 11, 1)))
            .oocsRecords(new ArrayList<>())
            .build();

    oocsDao.insertReplicaSafe(oocs);
    List<OutOfContractSupport> findResult = oocsDao.findByOrgId(orgId);

    assertFalse(findResult.isEmpty());
    assertEquals(orgId, findResult.get(0).getOrgId());
  }

  @Test
  public void testFindOOCSRecordsWithEmptyCalculationDate() {
    OutOfContractSupport oocs =
        OutOfContractSupport.builder()
            .id(new ObjectId())
            .orgId(new ObjectId())
            .activationCode("activationCode3")
            .effectiveDate(DateTimeUtils.dateOf(LocalDate.of(2024, 11, 1)))
            .oocsRecords(new ArrayList<>())
            .build();

    oocsDao.insertReplicaSafe(oocs);
    List<OutOfContractSupport> findResult = oocsDao.findOOCSRecordsWithEmptyCalculationDate();

    assertFalse(findResult.isEmpty());
    assertNotNull(findResult.get(0).getId());
  }

  @Test
  public void testFindByOrgIdAndEffectiveDateRange() {
    ObjectId orgId = new ObjectId();
    OutOfContractSupport oocs1 =
        OutOfContractSupport.builder()
            .orgId(orgId)
            .activationCode("activationCode3")
            .effectiveDate(new GregorianCalendar(2024, Calendar.NOVEMBER, 1).getTime())
            .build();

    OutOfContractSupport oocs2 =
        OutOfContractSupport.builder()
            .orgId(orgId)
            .activationCode("activationCode4")
            .effectiveDate(new GregorianCalendar(2024, Calendar.NOVEMBER, 30).getTime())
            .build();

    OutOfContractSupport oocs3 =
        OutOfContractSupport.builder()
            .orgId(orgId)
            .activationCode("activationCode5")
            .effectiveDate(new GregorianCalendar(2024, Calendar.DECEMBER, 10).getTime())
            .build();

    oocsDao.insertReplicaSafe(oocs1);
    oocsDao.insertReplicaSafe(oocs2);
    oocsDao.insertReplicaSafe(oocs3);

    Date startDate = new GregorianCalendar(2024, Calendar.OCTOBER, 1).getTime();
    Date endDate = new GregorianCalendar(2024, Calendar.DECEMBER, 1).getTime();

    // Retrieve by orgId and date range
    List<OutOfContractSupport> results =
        oocsDao.findByOrgIdAndEffectiveDateRange(orgId, startDate, endDate);

    assertEquals(2, results.size());
  }

  @Test
  public void testUpdateCalculationDateAndCharges() {
    ObjectId orgId = new ObjectId();
    List<OOCSRecord> oocsRecords = new ArrayList<>();
    oocsRecords.add(
        OOCSRecord.builder()
            .oocsRecordId("testOocsId")
            .oocsStartDate(new GregorianCalendar(2024, Calendar.DECEMBER, 1).getTime())
            .oocsEndDate(new GregorianCalendar(2024, Calendar.DECEMBER, 30).getTime())
            .build());

    OutOfContractSupport oocs =
        OutOfContractSupport.builder()
            .orgId(orgId)
            .activationCode("activationCode4")
            .effectiveDate(new GregorianCalendar(2024, Calendar.NOVEMBER, 1).getTime())
            .oocsRecords(oocsRecords)
            .build();

    oocsDao.insertReplicaSafe(oocs);

    List<OutOfContractSupport> findResult = oocsDao.findByOrgId(orgId);
    OOCSRecord oocsRecord = findResult.get(0).getOocsRecords().get(0);

    assertNull(oocs.getCalculationDate());
    assertNull(oocsRecord.getTotalPriceCents());

    OOCSRecord oocsRecordToUpdate =
        OOCSRecord.builder()
            .oocsRecordId(oocsRecord.getOocsRecordId())
            .oocsStartDate(oocsRecord.getOocsStartDate())
            .oocsEndDate(oocsRecord.getOocsEndDate())
            .totalPriceCents(500L)
            .build();

    oocsDao.updateCalculationDateAndCharges(findResult.get(0), Arrays.asList(oocsRecordToUpdate));

    List<OutOfContractSupport> updatedOocs = oocsDao.findByOrgId(orgId);
    assertEquals(500L, (long) updatedOocs.get(0).getOocsRecords().get(0).getTotalPriceCents());
    assertNotNull(updatedOocs.get(0).getCalculationDate());
  }

  @Test
  public void testUpdateOOCSRecordPrice() {
    ObjectId oocsItemId = new ObjectId();
    List<OOCSRecord> oocsRecords = new ArrayList<>();
    oocsRecords.add(
        OOCSRecord.builder()
            .oocsRecordId("record1")
            .oocsStartDate(new Date())
            .oocsEndDate(new Date())
            .totalPriceCents(1000L)
            .build());

    OutOfContractSupport oocs =
        OutOfContractSupport.builder()
            .id(oocsItemId)
            .orgId(new ObjectId())
            .activationCode("activationCode6")
            .effectiveDate(new Date())
            .oocsRecords(oocsRecords)
            .build();

    oocsDao.insertReplicaSafe(oocs);

    UpdateResult result = oocsDao.updateOOCSRecordPrice(oocsItemId, "record1", 5000L);
    assertEquals(1, result.getMatchedCount());
    assertEquals(1, result.getModifiedCount());

    OutOfContractSupport updatedOocs = oocsDao.findByOocsId(oocsItemId);
    assertNotNull(updatedOocs);

    OOCSRecord updatedRecord =
        updatedOocs.getOocsRecords().stream()
            .filter(record -> "record1".equals(record.getOocsRecordId()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("OOCS Record not found for ID: record1"));

    assertEquals(5000L, updatedRecord.getTotalPriceCents().longValue());
  }
}
