package com.xgen.svc.mms.dao;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.core.dao.base.BaseDao;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class ChartsTenantDaoIntTests extends BaseSvcTest {
  private static final String CONNECTION_NAME = "chartsmetadata";
  private static final String DB_NAME = "metadata";

  private static final String ITEMS_COLLECTION = "items";
  private static final String DATA_SOURCES_COLLECTION = "datasources";

  @Inject private ChartsTenantDao _chartsTenantDao;
  @Inject private MongoSvc _mongoSvc;
  private BaseDao chartsDataSourcesDao;
  private BaseDao chartsItemsDao;

  @Before
  public void setUp() {
    chartsDataSourcesDao =
        new BaseDao(_mongoSvc, CONNECTION_NAME, DB_NAME, DATA_SOURCES_COLLECTION) {};
    chartsItemsDao = new BaseDao(_mongoSvc, CONNECTION_NAME, DB_NAME, ITEMS_COLLECTION) {};
  }

  @Test
  public void testUpdateTenant() {
    final BasicDBObject oldTenant = new BasicDBObject();
    oldTenant.append("_id", "tenant");
    oldTenant.append("chartsAppVersion", "1.0.0");
    oldTenant.append("foo", 1);
    _chartsTenantDao.insertReplicaSafe(oldTenant);
    final BasicDBObject newTenant = new BasicDBObject();
    newTenant.append("_id", "tenant");
    newTenant.append("chartsAppVersion", "1.0.1");
    newTenant.append("bar", 1);
    _chartsTenantDao.updateTenant(newTenant);
    final BasicDBObject updatedTenant =
        _chartsTenantDao.findOne(new BasicDBObject("_id", "tenant"));
    assertEquals("1.0.1", updatedTenant.getString("chartsAppVersion"));
    assertEquals(1, updatedTenant.getInt("foo"));
    assertFalse(updatedTenant.containsField("bar"));
  }

  @Test
  public void testIsUnusedTenant_sampleData() {
    final String tenantId = new ObjectId().toHexString();
    final BasicDBObject oldTenant = new BasicDBObject();
    final Instant createdAt = Instant.now().minus(Duration.ofDays(31));
    oldTenant.append("_id", tenantId);
    oldTenant.append("chartsAppVersion", "1.0.0");
    oldTenant.append("foo", 1);
    oldTenant.append("createdAt", createdAt);
    _chartsTenantDao.insertReplicaSafe(oldTenant);

    // Was the tenant created more than 30 days ago?
    final Instant thirtyDaysAgo = Instant.now().minus(Duration.ofDays(30));
    assertTrue(_chartsTenantDao.isUnusedChartsTenant(tenantId, thirtyDaysAgo));

    // Does the tenant have zero non-sample data sources? (from metadata.datasources )
    final BasicDBObject sampleDatasource = new BasicDBObject();
    sampleDatasource.append("_id", new ObjectId());
    sampleDatasource.append("tenantId", tenantId);
    sampleDatasource.append("sampleDatasource", true);
    chartsDataSourcesDao.insertReplicaSafe(sampleDatasource);

    assertTrue(_chartsTenantDao.isUnusedChartsTenant(tenantId, thirtyDaysAgo));
    assertFalse(
        _chartsTenantDao.isUnusedChartsTenant(tenantId, createdAt.minus(Duration.ofSeconds(1))));

    // Does the tenant have zero charts? (from metadata.items )
    final BasicDBObject item = new BasicDBObject();
    item.append("_id", new ObjectId());
    item.append("tenantId", tenantId);
    chartsItemsDao.insertReplicaSafe(item);
    assertFalse(_chartsTenantDao.isUnusedChartsTenant(tenantId, thirtyDaysAgo));
  }

  @Test
  public void testIsUnusedTenant_NonSampleData() {
    final String tenantId = new ObjectId().toHexString();
    final BasicDBObject oldTenant = new BasicDBObject();
    final Instant createdAt = Instant.now().minus(Duration.ofDays(31));
    final Instant thirtyDaysAgo = Instant.now().minus(Duration.ofDays(30));
    oldTenant.append("_id", tenantId);
    oldTenant.append("chartsAppVersion", "1.0.0");
    oldTenant.append("foo", 1);
    oldTenant.append("createdAt", createdAt);
    _chartsTenantDao.insertReplicaSafe(oldTenant);

    final BasicDBObject datasource = new BasicDBObject();
    datasource.append("_id", new ObjectId());
    datasource.append("tenantId", tenantId);
    datasource.append("sampleDatasource", false);
    chartsDataSourcesDao.insertReplicaSafe(datasource);

    final BasicDBObject sampleDatasource = new BasicDBObject();
    sampleDatasource.append("_id", new ObjectId());
    sampleDatasource.append("tenantId", tenantId);
    sampleDatasource.append("sampleDatasource", true);
    chartsDataSourcesDao.insertReplicaSafe(sampleDatasource);

    assertFalse(_chartsTenantDao.isUnusedChartsTenant(tenantId, thirtyDaysAgo));
  }
}
