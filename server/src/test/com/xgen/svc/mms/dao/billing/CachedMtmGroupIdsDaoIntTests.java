package com.xgen.svc.mms.dao.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.CachedMtmGroupId;
import jakarta.inject.Inject;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class CachedMtmGroupIdsDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private CachedMtmGroupIdsDao dao;

  @Test
  public void testSave() {
    Set<CachedMtmGroupId> groups =
        Set.of(
            new CachedMtmGroupId(new ObjectId()),
            new CachedMtmGroupId(new ObjectId()),
            new CachedMtmGroupId(new ObjectId()));

    dao.save(groups);
    assertEquals(
        groups.stream().map(CachedMtmGroupId::getMtmGroupId).collect(Collectors.toSet()),
        dao.getAllGroupIds());
  }
}
