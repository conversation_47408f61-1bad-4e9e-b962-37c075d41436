package com.xgen.svc.mms.dao.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.OrgPreferences;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class OrgPreferencesDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private OrgPreferencesDao orgPreferencesDao;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPreferencesDao/orgPreferences.json.ftl",
        null,
        OrgPreferences.DB_NAME,
        OrgPreferences.COLLECTION_NAME);
  }

  @Test
  public void findOrgPreferencesByOrgId_success() {
    ObjectId orgId = oid(101);
    assertNotNull(orgPreferencesDao.findOrgPreferencesByOrgId(orgId).get());

    ObjectId orgIdNonExistent = oid(201);
    assertFalse(orgPreferencesDao.findOrgPreferencesByOrgId(orgIdNonExistent).isPresent());
  }

  @Test
  public void updateInvoiceEmailRecipients_success() {
    ObjectId orgIdNonExistent = oid(233);
    // upserts document with update if query is not matched
    OrgPreferences orgPreferences =
        orgPreferencesDao.updateInvoiceEmailRecipients(orgIdNonExistent, false).get();
    assertNotNull(orgPreferences);
    assertEquals(orgPreferences.getOrgId(), orgIdNonExistent);
    assertFalse(orgPreferences.getShouldSendInvoiceOnlyToBillingEmail());

    // updates existing document with correct preferences
    ObjectId orgIdExists = oid(101);
    OrgPreferences orgPreferencesExists =
        orgPreferencesDao.findOrgPreferencesByOrgId(orgIdExists).get();
    assertFalse(orgPreferencesExists.getShouldSendInvoiceOnlyToBillingEmail());
    OrgPreferences orgPreferences2 =
        orgPreferencesDao.updateInvoiceEmailRecipients(orgIdExists, true).get();
    assertTrue(orgPreferences2.getShouldSendInvoiceOnlyToBillingEmail());
  }
}
