package com.xgen.svc.mms.api.res.atlas._private;

import static org.junit.Assert.assertEquals;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDnsPinClusterNamePrefixDao;
import com.xgen.cloud.nds.project._private.dao.NdsGroupDnsPinDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionDnsPinClusterNamePrefix;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class ApiPrivateAtlasDnsPinResourceIntTests extends ApiBaseResourceTest {

  @Inject private UserDao _userDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  @Inject
  private ClusterDescriptionDnsPinClusterNamePrefixDao
      _clusterDescriptionDnsPinClusterNamePrefixDao;

  @Inject private GroupDao _groupDao;
  @Inject private NdsGroupDnsPinDao _ndsGroupDnsPinDao;

  private static final String TEST_CLUSTER_NAME = "regularcluster";

  @Test
  public void testGlobalPermissionRequired() {
    // Non-global user (no additional setup needed)

    final JSONObject resp =
        doDigestJsonGet(
            "/api/private/nds/dnsPinToGroupMappings/anyvalue",
            HttpStatus.SC_UNAUTHORIZED,
            JOHN_DOE_USERNAME,
            JOHN_DOE_API_KEY);

    assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.getInt(ApiError.ERROR_FIELD));
    assertEquals("USER_UNAUTHORIZED", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGlobalReadOnlyPermission() {
    assignGlobalRoleToUser(JOHN_DOE_USERNAME, Role.GLOBAL_READ_ONLY);

    final JSONObject resp =
        doDigestJsonGet(
            "/api/private/nds/dnsPinToGroupMappings/anyvalue",
            HttpStatus.SC_NOT_FOUND,
            JOHN_DOE_USERNAME,
            JOHN_DOE_API_KEY);

    assertEquals("RESOURCE_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetClusterPinToGroupMapping() {
    assignGlobalRoleToUser(JOHN_DOE_USERNAME, Role.GLOBAL_OWNER);

    // Create organization and group directly
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _groupDao.save(group);

    // Create and save cluster description
    final ClusterDescription clusterDescription =
        createAndSaveClusterDescription(group, TEST_CLUSTER_NAME);

    // Create and save DNS pin prefix
    final ClusterDescriptionDnsPinClusterNamePrefix dnsPinPrefix =
        new ClusterDescriptionDnsPinClusterNamePrefix(clusterDescription);
    _clusterDescriptionDnsPinClusterNamePrefixDao.create(dnsPinPrefix);

    // Create and save NDS group
    NDSGroup ndsGroup = createAndSaveNDSGroup(dnsPinPrefix.getGroupId(), dnsPinPrefix.getDnsPin());

    final JSONObject resp =
        doDigestJsonGet(
            "/api/private/nds/dnsPinToGroupMappings/"
                + dnsPinPrefix.getDnsPin()
                + "?clusterNamePrefix="
                + TEST_CLUSTER_NAME,
            HttpStatus.SC_OK,
            JOHN_DOE_USERNAME,
            JOHN_DOE_API_KEY);

    assertEquals(ndsGroup.getGroupId().toString(), resp.getString("groupId"));
    assertEquals(org.getId().toString(), resp.getString("orgId"));
  }

  @Test
  public void testGetGroupPinToGroupMapping() {
    // Arrange
    assignGlobalRoleToUser(JOHN_DOE_USERNAME, Role.GLOBAL_OWNER);

    // Create organization and group directly
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _groupDao.save(group);

    // Create and save NDS group
    NDSGroup ndsGroup = createAndSaveNDSGroup(group.getId(), null);

    // Create DNS pin mapping
    _ndsGroupDnsPinDao.create(ndsGroup.getDNSPin(), group.getId());

    final JSONObject resp =
        doDigestJsonGet(
            "/api/private/nds/dnsPinToGroupMappings/" + ndsGroup.getDNSPin(),
            HttpStatus.SC_OK,
            JOHN_DOE_USERNAME,
            JOHN_DOE_API_KEY);

    assertEquals(ndsGroup.getGroupId().toString(), resp.getString("groupId"));
    assertEquals(org.getId().toString(), resp.getString("orgId"));
  }

  // Helper method to assign a global role to a user
  private void assignGlobalRoleToUser(String username, Role role) {
    final AppUser user = _userDao.findByUsername(username);
    user.assignRole(RoleAssignment.forGlobal(role));
    _userDao.update(user);
  }

  // Helper method to create and save a cluster description
  private ClusterDescription createAndSaveClusterDescription(Group group, String clusterName) {
    final ClusterDescription clusterDescription =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(group.getId(), clusterName))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);
    return clusterDescription;
  }

  // Helper method to create and save an NDS group
  private NDSGroup createAndSaveNDSGroup(ObjectId groupId, String dnsPin) {
    NDSGroup ndsGroup = NDSModelTestFactory.getFreeMockedGroup(groupId);
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);
    return ndsGroup;
  }
}
