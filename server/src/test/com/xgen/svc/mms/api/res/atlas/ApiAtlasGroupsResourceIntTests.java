package com.xgen.svc.mms.api.res.atlas;

import static com.xgen.svc.common.TestDataUtils.populateCollectionFromJsonFtlFile;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.TeamRoleAssignment;
import com.xgen.cloud.access.role._public.view.ApiRoleAssignmentView;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.groupcreation._public.svc.GroupCreationSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.common.view._public.base.Link;
import com.xgen.cloud.common.view._public.base.LinkRelView;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.Limits.Defaults;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.LimitTestFactory;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeModelTestFactory;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeState;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.OverrunPolicy;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.streams._private.dao.StreamsTenantDao;
import com.xgen.cloud.organization._private.view.OrgCreationForm;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._private.svc.UserSvcOkta;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.mhouse.services.billinglimits.v1.Models;
import com.xgen.mhouse.services.billinglimits.v1.Models.DataScanningLimitStatus;
import com.xgen.mhouse.services.billinglimits.v1.Models.LimitSpan;
import com.xgen.mhouse.services.billinglimits.v1.Models.UsageLimit;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.view.ApiAppUserView;
import com.xgen.svc.mms.api.view.ApiLimitView.FieldDefs;
import com.xgen.svc.mms.api.view.ApiLimitView.ProjectLimitName;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGroupView;
import com.xgen.svc.mms.api.view.atlas.ApiGovAtlasRegionUsageRestrictionsView;
import com.xgen.svc.mms.api.view.atlas.dataLake.ApiAtlasDataFederationQueryLimitView;
import com.xgen.svc.mms.api.view.atlas.dataLake.ApiAtlasDataFederationTenantQueryLimitView;
import com.xgen.svc.mms.api.view.atlas.privateLink.ApiAtlasCreateEndpointServiceRequestView;
import com.xgen.svc.mms.model.ChartsConfig;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.svc.NDSOrgSvc;
import com.xgen.svc.mms.svc.TeamSvc;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.core.UriBuilder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;
import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URIBuilder;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class ApiAtlasGroupsResourceIntTests extends ApiBaseResourceTest {

  private static final String BASE_URL = "api/atlas/v1.0/groups";

  private static final String EXPECTED_REALTIME_SETTING_KEY = "isRealtimePerformancePanelEnabled";
  private static final String EXPECTED_DATA_EXPLORER_SETTING_KEY = "isDataExplorerEnabled";
  private static final String EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY =
      "isDataExplorerGenAIFeaturesEnabled";
  private static final String EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY =
      "isDataExplorerGenAISampleDocumentPassingEnabled";
  private static final String EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY =
      "isPerformanceAdvisorEnabled";
  private static final String EXPECTED_SCHEMA_ADVISOR_KEY = "isSchemaAdvisorEnabled";
  private static final String EXPECTED_DB_STATS_SETTING_KEY =
      "isCollectDatabaseSpecificsStatisticsEnabled";
  private static final String EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY =
      "isExtendedStorageSizesEnabled";
  private static final String GROUP_NAME = "name";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private OrganizationSvc _organizationSvc;
  @Inject private NDSOrgSvc _ndsOrgSvc;
  @Inject private GroupSvc _groupSvc;
  @Inject private GroupCreationSvc _groupCreationSvc;
  @Inject private UserSvc _userSvc;
  @Inject private TeamSvc _teamSvc;
  @Inject private UserDao _userDao;
  @Inject private NDSDataLakePublicSvc _ndsDataLakePublicSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private AppSettings _appSettings;
  @Inject private GCPOrganizationDao _gcpOrganizationDao;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  private static final String FAKE_API_KEY = "IAMAFAKEAPIKEYANDABADHACK3R0";

  private JSONObject addGroup(final String pOrgId, final String pGroupName) {
    return addGroup(
        pOrgId,
        pGroupName,
        HttpStatus.SC_CREATED,
        false,
        ADMIN_USERNAME,
        null,
        ADMIN_API_KEY,
        null,
        null,
        null);
  }

  private JSONObject addGroup(
      final String pOrgId,
      final String pGroupName,
      final int pExpectedHttpStatusCode,
      final boolean pEnvelope,
      final String pUsername,
      final String pProjectOwnerId,
      final String pApiKey,
      final Boolean pUseCNRegionsOnly,
      final RegionUsageRestrictions pRegionUsageRestrictions,
      final Boolean pWithDefaultAlertsSettings) {
    final JSONObject jsonRequest = new JSONObject();
    jsonRequest.put(ApiAtlasGroupView.NAME_FIELD, pGroupName);

    if (pOrgId != null) {
      jsonRequest.put(ApiAtlasGroupView.ORG_ID_FIELD, pOrgId);
    }

    if (pUseCNRegionsOnly != null) {
      jsonRequest.put(ApiAtlasGroupView.USE_CN_REGIONS_ONLY, pUseCNRegionsOnly);
    }

    if (pRegionUsageRestrictions != null) {
      jsonRequest.put(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS, pRegionUsageRestrictions.name());
    }

    if (pWithDefaultAlertsSettings != null) {
      jsonRequest.put(ApiAtlasGroupView.WITH_DEFAULT_ALERTS_SETTINGS, pWithDefaultAlertsSettings);
    }
    final UriBuilder uri = UriBuilder.fromUri(BASE_URL);
    if (pEnvelope) {
      uri.queryParam("envelope", true);
    }
    if (pProjectOwnerId != null) {
      uri.queryParam("projectOwnerId", pProjectOwnerId);
    }
    return doDigestJsonPost(
        uri.toString(), jsonRequest, pExpectedHttpStatusCode, pUsername, pApiKey);
  }

  private JSONObject testAddGroup(
      final String pGroupName,
      final String pOrgId,
      final int pExpectedHttpStatusCode,
      final ApiErrorCode pApiErrorCode,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey,
      final Boolean pUseCNRegionsOnly,
      final boolean pAtlasCNRegionsOnlyEnabled) {
    return testAddGroup(
        pGroupName,
        pOrgId,
        pExpectedHttpStatusCode,
        pApiErrorCode,
        pEnvelope,
        pUsername,
        null,
        pApiKey,
        pUseCNRegionsOnly,
        pAtlasCNRegionsOnlyEnabled,
        null,
        true);
  }

  private JSONObject testAddGroup(
      final String pGroupName,
      final String pOrgId,
      final int pExpectedHttpStatusCode,
      final ApiErrorCode pApiErrorCode,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey,
      final RegionUsageRestrictions pRegionUsageRestrictions) {
    return testAddGroup(
        pGroupName,
        pOrgId,
        pExpectedHttpStatusCode,
        pApiErrorCode,
        pEnvelope,
        pUsername,
        null,
        pApiKey,
        null,
        false,
        pRegionUsageRestrictions,
        true);
  }

  private JSONObject testAddGroup(
      final String pGroupName,
      final String pOrgId,
      final int pExpectedHttpStatusCode,
      final ApiErrorCode pApiErrorCode,
      final boolean pEnvelope,
      final String pUsername,
      final String pProjectOwnerId,
      final String pApiKey,
      final Boolean pUseCNRegionsOnly,
      final boolean pAtlasCNRegionsOnlyEnabled,
      final RegionUsageRestrictions pRegionUsageRestrictions,
      final Boolean pWithDefaultAlertsSettings) {
    final JSONObject jsonResponse =
        addGroup(
            pOrgId,
            pGroupName,
            pExpectedHttpStatusCode,
            pEnvelope,
            pUsername,
            pProjectOwnerId,
            pApiKey,
            pUseCNRegionsOnly,
            pRegionUsageRestrictions,
            pWithDefaultAlertsSettings);

    assertGroupJsonResponse(
        pGroupName,
        jsonResponse,
        pExpectedHttpStatusCode,
        0,
        pApiErrorCode,
        false,
        pUseCNRegionsOnly != null ? pUseCNRegionsOnly : false,
        pAtlasCNRegionsOnlyEnabled,
        pEnvelope,
        false,
        pWithDefaultAlertsSettings);

    // check that a corresponding NDS group has been created if successful
    if (pExpectedHttpStatusCode == HttpStatus.SC_CREATED) {
      final Optional<NDSGroup> ndsGroup =
          _ndsGroupSvc.find(
              new ObjectId(
                  pEnvelope
                      ? jsonResponse
                          .getJSONObject(ApiResponseBuilder.CONTENT_FIELD)
                          .getString(ApiAtlasGroupView.ID_FIELD)
                      : jsonResponse.getString(ApiAtlasGroupView.ID_FIELD)));
      assertTrue(ndsGroup.isPresent());
      assertEquals(
          pUseCNRegionsOnly != null ? pUseCNRegionsOnly : false, ndsGroup.get().useCNRegionsOnly());
    }

    // if contained in an envelope, return the content node
    if (pEnvelope && pExpectedHttpStatusCode != HttpStatus.SC_UNAUTHORIZED) {
      return jsonResponse.getJSONObject(ApiResponseBuilder.CONTENT_FIELD);
    }

    if (pProjectOwnerId != null) {
      final AppUser appUser = _userSvc.findById(new ObjectId(pProjectOwnerId));
      assertTrue(
          appUser
              .getGroupIds()
              .contains(new ObjectId(jsonResponse.getString(ApiAtlasGroupView.ID_FIELD))));
      assertEquals(
          appUser.getRoleInGroup(new ObjectId(jsonResponse.getString(ApiAtlasGroupView.ID_FIELD))),
          Role.GROUP_OWNER);
    }

    return jsonResponse;
  }

  private void assertGetAllGroupsJsonResponse(
      final JSONObject pAllGroupsJsonResponse,
      final int pExpectedHttpStatusCode,
      final int pExpectedGroupCount,
      final int pExpectedClusterCount,
      final Integer pItemsPerPage,
      final ApiErrorCode pApiErrorCode,
      final String pErrorDetailContains,
      final boolean pUseCNRegionsOnly,
      final boolean pAtlasCNRegionsOnlyEnabled,
      final boolean pEnvelope)
      throws JSONException {
    if (pEnvelope) {
      assertEquals(
          pExpectedHttpStatusCode, pAllGroupsJsonResponse.getInt(ApiResponseBuilder.STATUS_FIELD));
      final JSONObject contentJson =
          pAllGroupsJsonResponse.getJSONObject(ApiResponseBuilder.CONTENT_FIELD);

      if (pApiErrorCode != null) {
        assertEquals(pApiErrorCode.getStatus(), contentJson.getInt(ApiError.ERROR_FIELD));
        assertEquals(pApiErrorCode.name(), contentJson.getString(ApiError.ERROR_CODE_FIELD));
        return;
      }

      assertGetAllGroupsJsonResponse(
          contentJson,
          pExpectedHttpStatusCode,
          pExpectedGroupCount,
          pExpectedClusterCount,
          pItemsPerPage,
          pApiErrorCode,
          pErrorDetailContains,
          pUseCNRegionsOnly,
          pAtlasCNRegionsOnlyEnabled,
          false);

      return;
    }

    if (pApiErrorCode != null) {
      assertEquals(pExpectedHttpStatusCode, pApiErrorCode.getStatus());
      assertEquals(pApiErrorCode.getStatus(), pAllGroupsJsonResponse.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          pApiErrorCode.name(), pAllGroupsJsonResponse.getString(ApiError.ERROR_CODE_FIELD));

      if (pErrorDetailContains != null) {
        assertTrue(
            pAllGroupsJsonResponse.getString(ApiError.DETAIL_FIELD).contains(pErrorDetailContains));
      }

      return;
    }
    final int totalResultsCount = pAllGroupsJsonResponse.getInt(ApiListView.TOTAL_COUNT_FIELD);

    assertEquals(pAllGroupsJsonResponse.toString(2), pExpectedGroupCount, totalResultsCount);

    final JSONArray resultsJson = pAllGroupsJsonResponse.getJSONArray(ApiListView.RESULTS_FIELD);

    // The pagination service returns 1 page when 0 results are specified
    final int expectedResultsCount;
    if (pItemsPerPage != null) {
      final int itemsPerPage = pItemsPerPage == 0 ? 1 : pItemsPerPage;
      expectedResultsCount =
          pExpectedGroupCount == 0 || itemsPerPage > pExpectedGroupCount
              ? pExpectedGroupCount
              : itemsPerPage;
    } else {
      expectedResultsCount = pExpectedGroupCount;
    }
    assertEquals(expectedResultsCount, resultsJson.length());

    IntStream.range(0, resultsJson.length())
        .forEach(
            i -> {
              try {
                final JSONObject groupJson = resultsJson.getJSONObject(i);
                assertGroupJsonResponse(
                    groupJson.getString(ApiAtlasGroupView.NAME_FIELD),
                    groupJson,
                    pExpectedHttpStatusCode,
                    pExpectedClusterCount,
                    pApiErrorCode,
                    false,
                    pUseCNRegionsOnly,
                    pAtlasCNRegionsOnlyEnabled,
                    false,
                    true,
                    true);
              } catch (final Exception e) {
                throw new RuntimeException(e);
              }
            });
  }

  private void assertGroupJsonResponse(
      final String pGroupName,
      final JSONObject pGroupJsonResponse,
      final int pExpectedHttpStatusCode,
      final int pExpectedClusterCount,
      final ApiErrorCode pApiErrorCode,
      final boolean pIncludeBetaFeatures,
      final boolean pUseCNRegionsOnly,
      final boolean pAtlasCNRegionsOnlyEnabled,
      final boolean pEnvelope,
      final boolean pSelfOnly,
      final boolean pWithDefaultAlertsSettings)
      throws JSONException {
    if (pExpectedHttpStatusCode == HttpStatus.SC_UNAUTHORIZED) {
      return;
    }

    if (pEnvelope) {
      final JSONObject contentJson =
          pGroupJsonResponse.getJSONObject(ApiResponseBuilder.CONTENT_FIELD);
      assertEquals(
          pExpectedHttpStatusCode, pGroupJsonResponse.getInt(ApiResponseBuilder.STATUS_FIELD));

      if (pApiErrorCode != null) {
        assertEquals(pApiErrorCode.getStatus(), contentJson.getInt(ApiError.ERROR_FIELD));
        assertEquals(pApiErrorCode.name(), contentJson.getString(ApiError.ERROR_CODE_FIELD));
        return;
      }

      assertGroupJsonResponse(
          pGroupName,
          contentJson,
          pExpectedHttpStatusCode,
          pExpectedClusterCount,
          null,
          false,
          pUseCNRegionsOnly,
          pAtlasCNRegionsOnlyEnabled,
          false,
          pSelfOnly,
          pWithDefaultAlertsSettings);

      return;
    }

    if (pApiErrorCode != null) {
      assertEquals(pExpectedHttpStatusCode, pApiErrorCode.getStatus());
      assertEquals(pApiErrorCode.getStatus(), pGroupJsonResponse.getInt(ApiError.ERROR_FIELD));
      assertEquals(pApiErrorCode.name(), pGroupJsonResponse.getString(ApiError.ERROR_CODE_FIELD));

      return;
    }

    // Verify the contents
    pGroupJsonResponse.getString(ApiAtlasGroupView.ID_FIELD);
    pGroupJsonResponse.getString(ApiAtlasGroupView.ORG_ID_FIELD);
    assertEquals(pGroupName, pGroupJsonResponse.getString(ApiAtlasGroupView.NAME_FIELD));
    pGroupJsonResponse.getString(ApiAtlasGroupView.CREATED_FIELD);
    assertEquals(
        pWithDefaultAlertsSettings,
        pGroupJsonResponse.getBoolean(ApiAtlasGroupView.WITH_DEFAULT_ALERTS_SETTINGS));
    assertEquals(
        pExpectedClusterCount, pGroupJsonResponse.getLong(ApiAtlasGroupView.CLUSTER_COUNT_FIELD));

    final List<String> expectedLinkRels =
        pSelfOnly
            ? Collections.singletonList(LinkRelView.SELF.getRel())
            : Arrays.asList(
                LinkRelView.SELF.getRel(),
                LinkRelView.CLUSTERS.getRel(),
                LinkRelView.CONTAINERS.getRel(),
                LinkRelView.DATABASE_USERS.getRel(),
                LinkRelView.PEERS.getRel(),
                LinkRelView.PROCESSES.getRel(),
                LinkRelView.WHITELIST.getRel());

    final JSONArray linksJson = pGroupJsonResponse.getJSONArray(ApiAtlasGroupView.LINKS_FIELD);
    IntStream.range(0, linksJson.length())
        .forEach(
            i -> {
              try {
                final JSONObject linkJson = linksJson.getJSONObject(i);
                assertTrue(expectedLinkRels.contains(linkJson.getString(Link.REL_FIELD)));
              } catch (final JSONException e) {
                throw new RuntimeException(e);
              }
            });

    if (pIncludeBetaFeatures) {
      assertNotNull(pGroupJsonResponse.getJSONArray(ApiAtlasGroupView.BETA_FEATURES_FIELD));
      final JSONArray featureFlags =
          pGroupJsonResponse.getJSONArray(ApiAtlasGroupView.BETA_FEATURES_FIELD);
      assertFalse(featureFlags.isEmpty());
      assertNotNull(FeatureFlag.valueOf(featureFlags.getString(0)));
    }

    if (pAtlasCNRegionsOnlyEnabled) {
      try {
        assertEquals(
            pUseCNRegionsOnly,
            pGroupJsonResponse.getBoolean(ApiAtlasGroupView.USE_CN_REGIONS_ONLY));
      } catch (final JSONException pE) {
        fail("Did not expect JSONException");
      }
    } else {
      try {
        pGroupJsonResponse.getBoolean(ApiAtlasGroupView.USE_CN_REGIONS_ONLY);
        fail("Expected JSONException");
      } catch (final JSONException pE) {
        assertEquals("JSONObject[\"useCNRegionsOnly\"] not found.", pE.getMessage());
      }
    }
  }

  private String getGroupName(final String pBaseName, final int pIndex, final boolean pEnvelope) {
    return String.format("%s%s-%s", pBaseName, pEnvelope ? "-e" : "", pIndex);
  }

  private void testDeleteGroupUsersSetUp() throws Exception {
    populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
  }

  private void testDeleteGroupSetUp() throws Exception {

    // Needed to make sure that GroupSvcCloud gets injected and the right validation logic for
    // group deletion runs
    _appSettings.setProp("mms.userSvcClass", UserSvcOkta.class.getSimpleName(), SettingType.MEMORY);

    populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groupsForAtlas.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/ChartsConfigDao/chartsConfig.json.ftl",
        null,
        ChartsConfig.DB_NAME,
        ChartsConfig.COLLECTION_NAME);

    // Load Atlas resources
    populateCollectionFromJsonFtlFile(
        "mms/res/atlas/ApiAtlasGroupsResourceIntTests/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    populateCollectionFromJsonFtlFile(
        "mms/res/atlas/ApiAtlasGroupsResourceIntTests/dataLakes.json.ftl",
        null,
        NDSDataLakeTenantDao.DB_NAME,
        NDSDataLakeTenantDao.COLLECTION_NAME);
    populateCollectionFromJsonFtlFile(
        "mms/res/atlas/ApiAtlasGroupsResourceIntTests/ndsGroups.json.ftl",
        null,
        NDSGroupDao.DB,
        NDSGroupDao.COLLECTION);

    populateCollectionFromJsonFtlFile(
        "mms/dao/streams/streamsTenant.json.ftl",
        null,
        StreamsTenantDao.DB_NAME,
        StreamsTenantDao.COLLECTION_NAME);
  }

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _dataLakeTestUtils.setUp();
  }

  @After
  public void teardown() {
    _dataLakeTestUtils.teardown();
    _appSettings.clearMemory();
  }

  @Test
  public void testAddGroup() throws Exception {
    testAddGroup(true);
    testAddGroup(false);
  }

  @Test
  public void testCreateGroupRegionUsageRestrictions_Commercial() {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final JSONObject jsonRequest =
        new JSONObject()
            .put(ApiAtlasGroupView.NAME_FIELD, "newGroup")
            .put(ApiAtlasGroupView.ORG_ID_FIELD, org.getId())
            .put(
                ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS,
                ApiGovAtlasRegionUsageRestrictionsView.COMMERCIAL_FEDRAMP_REGIONS_ONLY.name());

    final JSONObject fedrampCommercialResponse =
        doDigestJsonPost(
            BASE_URL, jsonRequest, HttpStatus.SC_BAD_REQUEST, ADMIN_USERNAME, ADMIN_API_KEY);
    assertEquals(
        NDSErrorCode.INVALID_REGION_RESTRICTION.name(),
        fedrampCommercialResponse.getString("errorCode"));

    jsonRequest.put(
        ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS,
        ApiGovAtlasRegionUsageRestrictionsView.GOV_REGIONS_ONLY.name());

    final JSONObject govCloudOnlyResponse =
        doDigestJsonPost(
            BASE_URL, jsonRequest, HttpStatus.SC_BAD_REQUEST, ADMIN_USERNAME, ADMIN_API_KEY);
    assertEquals(
        NDSErrorCode.INVALID_REGION_RESTRICTION.name(),
        govCloudOnlyResponse.getString("errorCode"));

    jsonRequest.put(
        ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS,
        ApiGovAtlasRegionUsageRestrictionsView.NONE.name());

    final JSONObject noneResponse =
        doDigestJsonPost(
            BASE_URL, jsonRequest, HttpStatus.SC_CREATED, ADMIN_USERNAME, ADMIN_API_KEY);

    assertFalse(noneResponse.has(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS));

    jsonRequest
        .put(ApiAtlasGroupView.NAME_FIELD, "noValue")
        .remove(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS);

    final JSONObject noValueResponse =
        doDigestJsonPost(
            BASE_URL, jsonRequest, HttpStatus.SC_CREATED, ADMIN_USERNAME, ADMIN_API_KEY);

    assertFalse(noValueResponse.has(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS));
  }

  @Test
  public void testCreateGroupRegionUsageRestrictions_GovCloud() {
    try {
      _appSettings.setProp("nds.gov.us.enabled", "true", SettingType.MEMORY);

      final Organization org = MmsFactory.createOrganizationWithNDSPlan();
      final JSONObject jsonRequest =
          new JSONObject()
              .put(ApiAtlasGroupView.NAME_FIELD, "none")
              .put(ApiAtlasGroupView.ORG_ID_FIELD, org.getId())
              .put(
                  ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS,
                  ApiGovAtlasRegionUsageRestrictionsView.NONE.name());

      final JSONObject noneResponse =
          doDigestJsonPost(
              BASE_URL, jsonRequest, HttpStatus.SC_CREATED, ADMIN_USERNAME, ADMIN_API_KEY);

      assertEquals(
          ApiGovAtlasRegionUsageRestrictionsView.COMMERCIAL_FEDRAMP_REGIONS_ONLY.name(),
          noneResponse.getString(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS));

      jsonRequest
          .put(ApiAtlasGroupView.NAME_FIELD, "commercialFedRAMP")
          .put(
              ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS,
              ApiGovAtlasRegionUsageRestrictionsView.COMMERCIAL_FEDRAMP_REGIONS_ONLY.name());

      final JSONObject fedrampCommercialResponse =
          doDigestJsonPost(
              BASE_URL, jsonRequest, HttpStatus.SC_CREATED, ADMIN_USERNAME, ADMIN_API_KEY);

      assertEquals(
          ApiGovAtlasRegionUsageRestrictionsView.COMMERCIAL_FEDRAMP_REGIONS_ONLY.name(),
          fedrampCommercialResponse.getString(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS));

      jsonRequest
          .put(ApiAtlasGroupView.NAME_FIELD, "govRegions")
          .put(
              ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS,
              ApiGovAtlasRegionUsageRestrictionsView.GOV_REGIONS_ONLY.name());

      final JSONObject govCloudOnlyResponse =
          doDigestJsonPost(
              BASE_URL, jsonRequest, HttpStatus.SC_CREATED, ADMIN_USERNAME, ADMIN_API_KEY);
      assertEquals(
          ApiGovAtlasRegionUsageRestrictionsView.GOV_REGIONS_ONLY.name(),
          govCloudOnlyResponse.getString(ApiAtlasGroupView.REGION_USAGE_RESTRICTIONS));
    } finally {
      _appSettings.clearMemory();
    }
  }

  @Test
  public void testAddGroup_throwsException_whenGroupIsNull() {
    final JSONObject response =
        HttpUtils.getInstance()
            .post()
            .path(BASE_URL)
            .digestAuth(ADMIN_USERNAME, ADMIN_API_KEY)
            .returnType(JSONObject.class)
            .data(null)
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .send();
    assertNotNull(response);
    assertEquals(ApiErrorCode.VALIDATION_ERROR.getStatus(), response.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.VALIDATION_ERROR.name(), response.getString(ApiError.ERROR_CODE_FIELD));
  }

  private void testAddGroup(final boolean pEnvelope) throws Exception {
    FeatureFlagIntTestUtil.disableFeatureGlobally(FeatureFlag.ATLAS_CN_REGIONS_ONLY);
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    // Unauthorized (Invalid Credentials)
    testAddGroup(
        getGroupName("group-unauthorized", 0, pEnvelope),
        null,
        ApiErrorCode.USER_UNAUTHORIZED.getStatus(),
        ApiErrorCode.USER_UNAUTHORIZED,
        pEnvelope,
        ADMIN_USERNAME,
        FAKE_API_KEY,
        null,
        false);

    // No Org
    final JSONObject groupNoOrgJsonResponse =
        testAddGroup(
            getGroupName("group-no-org", 0, pEnvelope),
            null,
            HttpStatus.SC_CREATED,
            null,
            false,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            null,
            false);

    // With Org
    final String orgId = groupNoOrgJsonResponse.getString(ApiAtlasGroupView.ORG_ID_FIELD);
    final JSONObject groupWithOrgJsonResponse =
        testAddGroup(
            getGroupName("group-with-org", 0, pEnvelope),
            orgId,
            HttpStatus.SC_CREATED,
            null,
            pEnvelope,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            null,
            false);

    // Duplicate name
    testAddGroup(
        groupWithOrgJsonResponse.getString(ApiAtlasGroupView.NAME_FIELD),
        orgId,
        ApiErrorCode.GROUP_ALREADY_EXISTS.getStatus(),
        ApiErrorCode.GROUP_ALREADY_EXISTS,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        null,
        false);

    // Org does not exist
    testAddGroup(
        getGroupName("group-for-non-existent-org", 0, pEnvelope),
        new ObjectId().toString(),
        ApiErrorCode.ORG_NOT_FOUND.getStatus(),
        ApiErrorCode.ORG_NOT_FOUND,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        null,
        false);

    // Invalid attribute ("useCNRegionsOnly")
    testAddGroup(
        getGroupName("group-with-invalid-attribute", 0, pEnvelope),
        orgId,
        ApiErrorCode.INVALID_ATTRIBUTE.getStatus(),
        ApiErrorCode.INVALID_ATTRIBUTE,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        false,
        false);
    testAddGroup(
        getGroupName("group-with-invalid-attribute", 0, pEnvelope),
        orgId,
        ApiErrorCode.INVALID_ATTRIBUTE.getStatus(),
        ApiErrorCode.INVALID_ATTRIBUTE,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        true,
        false);

    // RegionUsageRestriction is NONE
    testAddGroup(
        getGroupName("group-with-region-usage-restriction-none", 0, pEnvelope),
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        RegionUsageRestrictions.NONE);

    // Invalid attribute ("regionUsageRestrictions")
    testAddGroup(
        getGroupName("group-with-invalid-region-restriction", 0, pEnvelope),
        orgId,
        ApiErrorCode.INVALID_REGION_RESTRICTION.getStatus(),
        ApiErrorCode.INVALID_REGION_RESTRICTION,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        RegionUsageRestrictions.GOV_REGIONS_ONLY);
    testAddGroup(
        getGroupName("group-with-invalid-region-restriction", 0, pEnvelope),
        orgId,
        ApiErrorCode.INVALID_REGION_RESTRICTION.getStatus(),
        ApiErrorCode.INVALID_REGION_RESTRICTION,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        RegionUsageRestrictions.COMMERCIAL_FEDRAMP_REGIONS_ONLY);

    // Non-Atlas Org
    final AppUser adminUser = _userSvc.findByUsername(ADMIN_USERNAME);
    final Organization nonAtlasOrg =
        _ndsOrgSvc.createOrganization(
            "test-cloud-org",
            GroupType.CLOUD,
            adminUser,
            new Date(),
            AuditInfoHelpers.fromInternal());

    testAddGroup(
        getGroupName("group-for-non-atlas-org", 0, pEnvelope),
        nonAtlasOrg.getId().toString(),
        ApiErrorCode.NOT_ATLAS_ORG.getStatus(),
        ApiErrorCode.NOT_ATLAS_ORG,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        null,
        false);

    // Org over group limit
    try {
      final String propName = ConfigLimitSvc.Fields.MAX_GROUPS_PER_ORG.getPropName();
      final int defaultMaxGroupsPerOrg = 1;
      _appSettings.setProp(
          propName, Integer.toString(defaultMaxGroupsPerOrg), AppSettings.SettingType.MEMORY);
      testAddGroup(
          getGroupName("group-for-org-over-limit", 0, pEnvelope),
          orgId,
          ApiErrorCode.MAX_GROUPS_PER_ORG_EXCEEDED.getStatus(),
          ApiErrorCode.MAX_GROUPS_PER_ORG_EXCEEDED,
          pEnvelope,
          ADMIN_USERNAME,
          ADMIN_API_KEY,
          null,
          false);
    } finally {
      _appSettings.clearMemory();
    }

    // User does not have access to the specified org
    testAddGroup(
        getGroupName("group-no-user-access", 0, pEnvelope),
        orgId,
        ApiErrorCode.NOT_ORG_GROUP_CREATOR.getStatus(),
        ApiErrorCode.NOT_ORG_GROUP_CREATOR,
        pEnvelope,
        JOHN_DOE_USERNAME,
        JOHN_DOE_API_KEY,
        null,
        false);

    // API User without group creator cannot create a group
    testAddGroup(
        "group-api-not-group-creator",
        orgId,
        ApiErrorCode.NOT_ORG_GROUP_CREATOR.getStatus(),
        ApiErrorCode.NOT_ORG_GROUP_CREATOR,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        API_ONLY_USER_API_KEY,
        null,
        false);

    // Add the API user to the organization as a group creator
    final AppUser apiBackingUser = _userSvc.findByUsername(API_ONLY_USER_USERNAME);
    final Organization targetOrganization = _organizationSvc.findById(new ObjectId(orgId));
    final Set<Role> rolesToAdd = Set.of(Role.ORG_GROUP_CREATOR);
    final Set<Role> rolesToRemove = Collections.emptySet();
    final AuditInfo auditInfo = AuditInfoHelpers.fromInternal();
    _userSvc.updateUserRoleInOrganization(
        apiBackingUser, targetOrganization.getId(), rolesToAdd, rolesToRemove, false, auditInfo);

    // API user cannot create a group without an org
    testAddGroup(
        "group-api-no-org",
        null,
        ApiErrorCode.API_KEY_CREATED_GROUPS_MUST_HAVE_ORG.getStatus(),
        ApiErrorCode.API_KEY_CREATED_GROUPS_MUST_HAVE_ORG,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        API_ONLY_USER_API_KEY,
        null,
        false);

    // API user can create a group with an existing org
    testAddGroup(
        "group-api-with-org",
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        API_ONLY_USER_API_KEY,
        null,
        false);

    final AppUser newOrgOwner =
        MmsFactory.createUserWithRoleInOrganization(
            targetOrganization, "<EMAIL>", Role.ORG_OWNER);

    // Add group with a project owner
    testAddGroup(
        "group-api-with-org-and-project-owner",
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        newOrgOwner.getId().toString(),
        API_ONLY_USER_API_KEY,
        null,
        false,
        null,
        true);

    // Add group without default alert configs
    testAddGroup(
        "group-api-without-default-alert-configs",
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        newOrgOwner.getId().toString(),
        API_ONLY_USER_API_KEY,
        null,
        false,
        null,
        false);

    // Add group with default alert configs
    testAddGroup(
        "group-api-with-default-alert-configs",
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        newOrgOwner.getId().toString(),
        API_ONLY_USER_API_KEY,
        null,
        false,
        null,
        true);

    testAddGroup(
        "group-api-default-alert-configs",
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        newOrgOwner.getId().toString(),
        API_ONLY_USER_API_KEY,
        null,
        false,
        null,
        true);
    // Test when NDS_SERVERLESS_FEATURE_ENABLED is true
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        SettingType.MEMORY);

    testAddGroup(
        getGroupName("group-with-serverless-instances-metering", 0, pEnvelope),
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        null,
        false);

    // Test when ATLAS_CN_REGIONS_ONLY feature flag is enabled
    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_CN_REGIONS_ONLY);

    // MongoDB Atlas in China Terms of Service not accepted
    testAddGroup(
        getGroupName("group-with-ToS-not-accepted-1", 0, pEnvelope),
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        false,
        true);
    testAddGroup(
        getGroupName("group-with-ToS-not-accepted-2", 0, pEnvelope),
        orgId,
        ApiErrorCode.CANNOT_CREATE_CN_REGIONS_ONLY_GROUP.getStatus(),
        ApiErrorCode.CANNOT_CREATE_CN_REGIONS_ONLY_GROUP,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        true,
        true);
    testAddGroup(
        getGroupName("group-with-ToS-not-accepted-3", 0, pEnvelope),
        orgId,
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        null,
        true);

    // MongoDB Atlas in China Terms of Service accepted
    final Organization org = MmsFactory.createOrganizationWithNDSPlan(true);

    testAddGroup(
        getGroupName("group-with-ToS-accepted-1", 0, pEnvelope),
        org.getId().toString(),
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        false,
        true);
    testAddGroup(
        getGroupName("group-with-ToS-accepted-2", 0, pEnvelope),
        org.getId().toString(),
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        true,
        true);
    testAddGroup(
        getGroupName("group-with-ToS-accepted-3", 0, pEnvelope),
        org.getId().toString(),
        HttpStatus.SC_CREATED,
        null,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        null,
        true);
  }

  private JSONObject getGroup(
      final String pGroupId,
      final int pExpectedHttpStatusCode,
      final boolean pIncludeBetaFeatures,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey) {
    return doDigestJsonGet(
        String.format(
            "%s/%s?%s%s",
            BASE_URL,
            pGroupId,
            pEnvelope ? "envelope=true&" : "",
            pIncludeBetaFeatures ? "includeBetaFeatures=true" : ""),
        pExpectedHttpStatusCode,
        pUsername,
        pApiKey);
  }

  private void testGetGroup(
      final String pGroupId,
      final String pExpectedGroupName,
      final int pExpectedClusterCount,
      final int pExpectedHttpStatusCode,
      final ApiErrorCode pApiErrorCode,
      final boolean pIncludeBetaFeatures,
      final boolean pUseCNRegionsOnly,
      final boolean pAtlasCNRegionsOnlyEnabled,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey) {
    testGetGroup(
        pGroupId,
        pExpectedGroupName,
        pExpectedClusterCount,
        pExpectedHttpStatusCode,
        pApiErrorCode,
        pApiErrorCode,
        pIncludeBetaFeatures,
        pUseCNRegionsOnly,
        pAtlasCNRegionsOnlyEnabled,
        pEnvelope,
        pUsername,
        pApiKey);
  }

  private void testGetGroup(
      final String pGroupId,
      final String pExpectedGroupName,
      final int pExpectedClusterCount,
      final int pExpectedHttpStatusCode,
      final ApiErrorCode pApiErrorCode,
      final ApiErrorCode pApiErrorCodeByName,
      final boolean pIncludeBetaFeatures,
      final boolean pUseCNRegionsOnly,
      final boolean pAtlasCNRegionsOnlyEnabled,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey) {
    final JSONObject jsonResponseById =
        getGroup(
            pGroupId, pExpectedHttpStatusCode, pIncludeBetaFeatures, pEnvelope, pUsername, pApiKey);
    assertGroupJsonResponse(
        pExpectedGroupName,
        jsonResponseById,
        pExpectedHttpStatusCode,
        pExpectedClusterCount,
        pApiErrorCode,
        pIncludeBetaFeatures,
        pUseCNRegionsOnly,
        pAtlasCNRegionsOnlyEnabled,
        pEnvelope,
        false,
        true);

    final JSONObject jsonResponseByName =
        getGroupByName(pExpectedGroupName, pExpectedHttpStatusCode, pEnvelope, pUsername, pApiKey);
    assertGroupJsonResponse(
        pExpectedGroupName,
        jsonResponseByName,
        pExpectedHttpStatusCode,
        pExpectedClusterCount,
        pApiErrorCodeByName,
        pIncludeBetaFeatures,
        pUseCNRegionsOnly,
        pAtlasCNRegionsOnlyEnabled,
        pEnvelope,
        false,
        true);
  }

  @Test
  public void testGetGroupByNamePrefixYieldsNoResults() throws Exception {

    populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groupsForAtlas.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    populateCollectionFromJsonFtlFile(
        "mms/res/atlas/ApiAtlasGroupsResourceIntTests/ndsGroups.json.ftl",
        null,
        NDSGroupDao.DB,
        NDSGroupDao.COLLECTION);
    populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);

    // Test a group name prefix - there should be no groups named "Group" in the above json file
    getGroupByName("Group", HttpStatus.SC_NOT_FOUND, false, ADMIN_USERNAME, ADMIN_API_KEY);

    // Test exact name match should still yield results
    final JSONObject jsonResponseByName =
        getGroupByName(
            URLEncoder.encode("Group 100", StandardCharsets.UTF_8).replace("+", "%20"),
            HttpStatus.SC_OK,
            false,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(jsonResponseByName.getString(ApiAtlasGroupView.NAME_FIELD), "Group 100");
  }

  @Test
  public void testGetGroup() throws Exception {
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/user/UserDao/usersWithCharts.json.ftl",
        null,
        AppUser.DB_NAME,
        AppUser.COLLECTION_NAME);

    testGetGroup(true, true);
    testGetGroup(false, false);
  }

  private void testGetGroup(final boolean pIncludeBetaFeatures, final boolean pEnvelope)
      throws Exception {
    FeatureFlagIntTestUtil.disableFeatureGlobally(FeatureFlag.ATLAS_CN_REGIONS_ONLY);

    // Unauthorized (Invalid Credentials)
    testGetGroup(
        new ObjectId().toString(),
        getGroupName("invalid-credentials-group", 0, pEnvelope),
        0,
        ApiErrorCode.USER_UNAUTHORIZED.getStatus(),
        ApiErrorCode.USER_UNAUTHORIZED,
        pIncludeBetaFeatures,
        false,
        false,
        pEnvelope,
        ADMIN_USERNAME,
        FAKE_API_KEY);

    // Non-existent group Id
    testGetGroup(
        new ObjectId().toString(),
        getGroupName("non-existent-group", 0, pEnvelope),
        0,
        HttpServletResponse.SC_NOT_FOUND,
        ApiErrorCode.GROUP_NOT_FOUND,
        ApiErrorCode.GROUP_NAME_NOT_FOUND,
        pIncludeBetaFeatures,
        false,
        false,
        false,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    final AppUser apiBackingUser = _userSvc.findByUsername(API_ONLY_USER_USERNAME);
    Organization newOrg = addOrg("org", apiBackingUser);

    // Valid group (with and without clusters)
    final String validGroupName = getGroupName("valid-group", 0, pEnvelope);
    final JSONObject groupJson = addGroup(newOrg.getId().toString(), validGroupName);

    final ObjectId validGroupId = new ObjectId(groupJson.getString(ApiAtlasGroupView.ID_FIELD));
    final NDSGroup group = _ndsGroupSvc.find(validGroupId).get();
    _ndsGroupSvc.addCloudContainer(
        group, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final int numClusters = 5;
    for (int clusterIndex = 0; clusterIndex <= numClusters; clusterIndex++) {
      if (clusterIndex > 0) {
        _clusterDescriptionDao.save(
            new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(
                        validGroupId, String.format("test-cluster-%s", clusterIndex))
                    .append("dnsPin", group.getDNSPin())));
      }

      testGetGroup(
          validGroupId.toString(),
          validGroupName,
          clusterIndex,
          HttpStatus.SC_OK,
          null,
          pIncludeBetaFeatures,
          false,
          false,
          pEnvelope,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
    }

    // API User with Org roles should be able to access the group info
    final ObjectId orgId = new ObjectId(groupJson.getString(ApiAtlasGroupView.ORG_ID_FIELD));
    final Organization targetOrganization = _organizationSvc.findById(orgId);
    final Set<Role> rolesToAdd = Set.of(Role.ORG_OWNER);
    final Set<Role> rolesToRemove = Collections.emptySet();
    final AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    _userSvc.updateUserRoleInOrganization(
        apiBackingUser, targetOrganization.getId(), rolesToAdd, rolesToRemove, false, auditInfo);

    testGetGroup(
        validGroupId.toString(),
        validGroupName,
        5,
        HttpStatus.SC_OK,
        null,
        pIncludeBetaFeatures,
        false,
        false,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        API_ONLY_USER_API_KEY);

    // Global Charts Admin
    testGetGroup(
        validGroupId.toString(),
        validGroupName,
        5,
        HttpStatus.SC_OK,
        null,
        pIncludeBetaFeatures,
        false,
        false,
        pEnvelope,
        CHARTS_USER_NAME,
        CHARTS_API_KEY);

    // Unauthorized access
    testGetGroup(
        validGroupId.toString(),
        validGroupName,
        numClusters,
        ApiErrorCode.USER_UNAUTHORIZED.getStatus(),
        ApiErrorCode.USER_UNAUTHORIZED,
        pIncludeBetaFeatures,
        false,
        false,
        pEnvelope,
        JOHN_DOE_USERNAME,
        JOHN_DOE_API_KEY);

    // Non-Atlas group
    final ObjectId nonAtlasGroupId = new ObjectId();
    final String nonAtlasGroupName = getGroupName("non-atlas-group", 0, pEnvelope);
    _groupCreationSvc.addGroup(
        nonAtlasGroupId,
        _organizationSvc.findById(
            new ObjectId((groupJson.getString(ApiAtlasGroupView.ORG_ID_FIELD)))),
        ADMIN_USER_ID,
        nonAtlasGroupName,
        false,
        Collections.emptyList(),
        AuditInfoHelpers.fromSystem(),
        false);

    testGetGroup(
        nonAtlasGroupId.toString(),
        nonAtlasGroupName,
        0,
        ApiErrorCode.NOT_ATLAS_GROUP.getStatus(),
        ApiErrorCode.NOT_ATLAS_GROUP,
        pIncludeBetaFeatures,
        false,
        false,
        pEnvelope,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_CN_REGIONS_ONLY);

    // Test when ATLAS_CN_REGIONS_ONLY feature flag is enabled
    testGetGroup(
        validGroupId.toString(),
        validGroupName,
        5,
        HttpStatus.SC_OK,
        null,
        pIncludeBetaFeatures,
        false,
        true,
        pEnvelope,
        API_ONLY_USER_USERNAME,
        API_ONLY_USER_API_KEY);
  }

  private Organization addOrg(String org, AppUser apiBackingUser) throws SvcException {
    return _ndsOrgSvc.createOrganizationFromForm(
        new OrgCreationForm(org, GroupType.NDS, false, null), apiBackingUser, null);
  }

  private JSONObject getGroupByName(
      final String pGroupName,
      final int pExpectedHttpStatusCode,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey) {
    return doDigestJsonGet(
        String.format("%s/byName/%s%s", BASE_URL, pGroupName, pEnvelope ? "?envelope=true" : ""),
        pExpectedHttpStatusCode,
        pUsername,
        pApiKey);
  }

  private JSONObject getAllGroups(
      final int pExpectedHttpStatusCode,
      @Nullable final Integer pItemsPerPage,
      final boolean pEnvelope,
      final String pUsername,
      final String pApiKey)
      throws Exception {
    final URIBuilder uriBuilder =
        new URIBuilder(BASE_URL).addParameter("envelope", String.valueOf(pEnvelope));
    if (pItemsPerPage != null) {
      uriBuilder.addParameter("itemsPerPage", String.valueOf(pItemsPerPage));
    }

    // Basic test
    return doDigestJsonGet(
        uriBuilder.build().toString(), pExpectedHttpStatusCode, pUsername, pApiKey);
  }

  @Test
  public void testGetAllGroups() throws Exception {
    testGetAllGroups(true);
    testGetAllGroups(false);
  }

  private void testGetAllGroups(final boolean pEnvelope) throws Exception {
    FeatureFlagIntTestUtil.disableFeatureGlobally(FeatureFlag.ATLAS_CN_REGIONS_ONLY);

    // Unauthorized (Invalid Credentials)
    getAllGroups(HttpStatus.SC_UNAUTHORIZED, null, pEnvelope, ADMIN_USERNAME, FAKE_API_KEY);

    // Setup app user references
    final AppUser adminUser = _userSvc.findByUsername(ADMIN_USERNAME);
    final AppUser janeDoeUser = _userSvc.findByUsername(JANE_DOE_USERNAME);
    final AppUser johnDoeUser = _userSvc.findByUsername(JOHN_DOE_USERNAME);

    // No groups returned (user has global read)
    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, ADMIN_USERNAME, ADMIN_API_KEY),
        HttpStatus.SC_OK,
        0,
        0,
        null,
        null,
        null,
        false,
        false,
        pEnvelope);

    // Add a non-NDS group and ensure the counts stay consistent
    final Organization cloudOrg =
        _ndsOrgSvc.createOrganization(
            getGroupName("test-cloud-org", 0, pEnvelope),
            GroupType.CLOUD,
            adminUser,
            new Date(),
            AuditInfoHelpers.fromInternal());

    _groupCreationSvc.findOrCreate(
        new ObjectId(),
        cloudOrg,
        getGroupName("test-cg-group", 0, pEnvelope),
        null,
        List.of(),
        false,
        null,
        false,
        true,
        null);

    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, ADMIN_USERNAME, ADMIN_API_KEY),
        HttpStatus.SC_OK,
        0,
        0,
        null,
        null,
        null,
        false,
        false,
        pEnvelope);

    // User with global read all permissions
    final int numGroups = 5;
    final int numClustersPerGroup = 3;
    final int numGroupsSubset = numGroups - 2;
    final List<ObjectId> generatedGroupIds = new ArrayList<>();
    final List<ClusterDescriptionId> generatedClusterDescriptionIds = new ArrayList<>();
    for (int groupIndex = 1; groupIndex <= numGroups; groupIndex++) {
      // Create a group
      final String groupName = getGroupName("test-ag-group", groupIndex, pEnvelope);
      final JSONObject groupJson = addGroup(null, groupName);
      final Group group =
          _groupSvc.findById(new ObjectId(groupJson.getString(ApiAtlasGroupView.ID_FIELD)));
      final Organization org =
          _organizationSvc.findById(
              new ObjectId(groupJson.getString(ApiAtlasGroupView.ORG_ID_FIELD)));
      generatedGroupIds.add(group.getId());

      // Grant a user access to a subset of orgs/groups
      if (groupIndex <= numGroupsSubset) {
        _userSvc.addUserToOrganization(
            JANE_DOE_USERNAME,
            org.getId(),
            Collections.singletonList(Role.ORG_READ_ONLY),
            MmsFactory.createAuditInfoWithAppUser());

        _groupSvc.updateUserRole(
            janeDoeUser,
            group,
            new HashSet<>(Collections.singletonList(Role.GROUP_READ_ONLY)),
            Collections.emptySet(),
            AuditInfoHelpers.fromSystem());
      }

      // Add a cloud provider
      _ndsGroupSvc.addCloudContainer(
          _ndsGroupSvc.find(group.getId()).get(),
          new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
    }

    // Test with and without clusters
    for (int clusterIndex = 0; clusterIndex <= numClustersPerGroup; clusterIndex++) {
      for (int groupIndex = 0; groupIndex < generatedGroupIds.size(); groupIndex++) {
        final ObjectId groupId = generatedGroupIds.get(groupIndex);
        if (clusterIndex > 0) {
          final String clusterName =
              String.format("test-ag-cluster-%s-%s", groupIndex, clusterIndex);
          final NDSGroup ndsGroup = _ndsGroupSvc.find(groupId).get();
          _clusterDescriptionDao.save(
              new ClusterDescription(
                  NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName)
                      .append("dnsPin", ndsGroup.getDNSPin())));

          generatedClusterDescriptionIds.add(new ClusterDescriptionId(clusterName, groupId));
        }
      }

      assertGetAllGroupsJsonResponse(
          getAllGroups(HttpStatus.SC_OK, null, pEnvelope, ADMIN_USERNAME, ADMIN_API_KEY),
          HttpStatus.SC_OK,
          numGroups,
          clusterIndex,
          null,
          null,
          null,
          false,
          false,
          pEnvelope);
    }

    // Pagination
    for (int itemsPerPage = 0; itemsPerPage <= numGroups; itemsPerPage++) {
      assertGetAllGroupsJsonResponse(
          getAllGroups(HttpStatus.SC_OK, itemsPerPage, pEnvelope, ADMIN_USERNAME, ADMIN_API_KEY),
          HttpStatus.SC_OK,
          numGroups,
          numClustersPerGroup,
          itemsPerPage,
          null,
          null,
          false,
          false,
          pEnvelope);
    }

    // User with group read, but not access to the groups (not creator)
    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, JOHN_DOE_USERNAME, JOHN_DOE_API_KEY),
        HttpStatus.SC_OK,
        0,
        0,
        null,
        null,
        null,
        false,
        false,
        pEnvelope);

    // User with group read, and access to a subset of the groups (not creator)
    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, JANE_DOE_USERNAME, JANE_DOE_API_KEY),
        HttpStatus.SC_OK,
        numGroupsSubset,
        numClustersPerGroup,
        null,
        null,
        null,
        false,
        false,
        pEnvelope);

    // Collection contains a group in an invalid state
    final Organization ndsInvalidStateOrg =
        _ndsOrgSvc.createOrganization(
            getGroupName("test-invalid-nds-org", 0, pEnvelope),
            GroupType.NDS,
            adminUser,
            new Date(),
            AuditInfoHelpers.fromInternal());

    final Group ndsInvalidStateGroup =
        _groupCreationSvc.findOrCreate(
            new ObjectId(),
            ndsInvalidStateOrg,
            getGroupName("test-invalid-nds-group", 0, pEnvelope),
            null,
            List.of(),
            false,
            null,
            false,
            true,
            null);
    generatedGroupIds.add(ndsInvalidStateGroup.getId());

    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, ADMIN_USERNAME, ADMIN_API_KEY),
        HttpStatus.SC_OK,
        6,
        numClustersPerGroup,
        5,
        null,
        null,
        false,
        false,
        pEnvelope);

    // Setup for groups with teams assigned to them, as well as groups
    final int numGroupsTeams = 5;
    final int numClustersPerGroupTeams = 1;
    final int numGroupsSubsetTeams = numGroupsTeams - 2;
    final List<ObjectId> generatedGroupIdsTeams = new ArrayList<>();
    final List<ClusterDescriptionId> generatedClusterDescriptionIdsTeams = new ArrayList<>();
    for (int groupIndex = 1; groupIndex <= numGroupsTeams; groupIndex++) {
      // Create a group
      final String groupName = getGroupName("testTeams-ag-group", groupIndex, pEnvelope);
      final JSONObject groupJson = addGroup(null, groupName);
      final Group group =
          _groupSvc.findById(new ObjectId(groupJson.getString(ApiAtlasGroupView.ID_FIELD)));
      final Organization org =
          _organizationSvc.findById(
              new ObjectId(groupJson.getString(ApiAtlasGroupView.ORG_ID_FIELD)));
      generatedGroupIdsTeams.add(group.getId());

      // Grant a user access to all of orgs/groups, some through roles some through teams
      if (groupIndex <= numGroupsSubsetTeams) {
        _userSvc.addUserToOrganization(
            JOHN_DOE_USERNAME,
            org.getId(),
            Collections.singletonList(Role.ORG_MEMBER),
            MmsFactory.createAuditInfoWithAppUser());

        final Team team =
            _teamSvc.create(
                "newTeam" + groupIndex,
                Collections.singleton(JOHN_DOE_USERNAME),
                org,
                AuditInfoHelpers.fromSystem());

        _groupSvc.addTeams(
            org.getId(),
            group,
            Collections.singletonList(new TeamRoleAssignment(team.getId(), Role.GROUP_READ_ONLY)),
            AuditInfoHelpers.fromSystem());
      } else if (groupIndex < numGroupsTeams) {
        _userSvc.addUserToOrganization(
            JOHN_DOE_USERNAME,
            org.getId(),
            Collections.singletonList(Role.ORG_MEMBER),
            MmsFactory.createAuditInfoWithAppUser());

        _groupSvc.updateUserRole(
            johnDoeUser,
            group,
            new HashSet<>(Collections.singletonList(Role.GROUP_READ_ONLY)),
            Collections.emptySet(),
            AuditInfoHelpers.fromSystem());
      } else {
        // Add a second group to the Org the user is ORG_READ_ONLY in and make sure they can see
        // both
        final String groupName2 = getGroupName("testTeams-ag-group", groupIndex + 1, pEnvelope);
        _userSvc.addUserToOrganization(
            JOHN_DOE_USERNAME,
            org.getId(),
            Collections.singletonList(Role.ORG_READ_ONLY),
            MmsFactory.createAuditInfoWithAppUser());

        final JSONObject groupJson2 = addGroup(org.getId().toString(), groupName2);
        final Group group2 =
            _groupSvc.findById(new ObjectId(groupJson2.getString(ApiAtlasGroupView.ID_FIELD)));
        generatedGroupIdsTeams.add(group2.getId());
        _ndsGroupSvc.addCloudContainer(
            _ndsGroupSvc.find(group2.getId()).get(),
            new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
      }

      // Add a cloud provider
      _ndsGroupSvc.addCloudContainer(
          _ndsGroupSvc.find(group.getId()).get(),
          new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
    }

    // Test with and without clusters
    for (int clusterIndex = 0; clusterIndex <= numClustersPerGroupTeams; clusterIndex++) {
      for (int groupIndex = 0; groupIndex < generatedGroupIdsTeams.size(); groupIndex++) {
        final ObjectId groupId = generatedGroupIdsTeams.get(groupIndex);
        if (clusterIndex > 0) {
          final String clusterName =
              String.format("test-ag-cluster-%s-%s", groupIndex, clusterIndex);
          final NDSGroup ndsGroup = _ndsGroupSvc.find(groupId).get();
          _clusterDescriptionDao.save(
              new ClusterDescription(
                  NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName)
                      .append("dnsPin", ndsGroup.getDNSPin())));

          generatedClusterDescriptionIdsTeams.add(new ClusterDescriptionId(clusterName, groupId));
        }
      }
    }

    // Make sure John Doe can now see all groups he is a part of thanks to be assigned to them via
    // Teams
    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, JOHN_DOE_USERNAME, JOHN_DOE_API_KEY),
        HttpStatus.SC_OK,
        // Add an additional 1 because there are two groups in the org the user is ORG_READ_ONLY in
        numGroupsTeams + 1,
        numClustersPerGroupTeams,
        null,
        null,
        null,
        false,
        false,
        pEnvelope);

    // Test when ATLAS_CN_REGIONS_ONLY feature flag is enabled
    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_CN_REGIONS_ONLY);

    assertGetAllGroupsJsonResponse(
        getAllGroups(HttpStatus.SC_OK, null, pEnvelope, JOHN_DOE_USERNAME, JOHN_DOE_API_KEY),
        HttpStatus.SC_OK,
        numGroupsTeams + 1,
        numClustersPerGroupTeams,
        null,
        null,
        null,
        false,
        true,
        pEnvelope);

    // Delete the clusters and groups so they do not interfere with other tests
    generatedClusterDescriptionIds.forEach(
        cId -> _clusterDescriptionDao.remove(cId.getGroupId(), cId.getClusterName()));
    generatedGroupIds.forEach(gId -> _groupSvc.delete(gId));

    generatedClusterDescriptionIdsTeams.forEach(
        cId -> _clusterDescriptionDao.remove(cId.getGroupId(), cId.getClusterName()));
    generatedGroupIdsTeams.forEach(gId -> _groupSvc.delete(gId));
  }

  @Test
  public void testDeleteGroupDoesNotExist() throws Exception {
    testDeleteGroupSetUp();
    // Test deleting a group that doesn't exist
    final JSONObject resp1 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(1000),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals("GROUP_NOT_FOUND", resp1.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveBackups() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active backups cannot be deleted
    final JSONObject resp2 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(100),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals("CANNOT_CLOSE_GROUP_ACTIVE_BACKUP", resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveClusters() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active clusters cannot be deleted
    final JSONObject resp3 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(116),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp3.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_CLUSTERS", resp3.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveDatalake() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active data lakes cannot be deleted
    final JSONObject resp4 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(117),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp4.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_DATA_LAKES", resp4.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActivePrivateEndpointServices() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active private endpoint services cannot be deleted
    final JSONObject resp5 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(118),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp5.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_PRIVATE_ENDPOINT_SERVICES",
        resp5.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActivePeers() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active peering connections
    final JSONObject resp6 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(119),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp6.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_PEERING_CONNECTIONS",
        resp6.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveChartsApp() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active charts apps cannot be deleted
    final JSONObject resp7 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(187),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp7.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_CHARTS_APP", resp7.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveDataFederationPrivateEndpoints() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active Atlas Data Federation private endpoints cannot be deleted
    final JSONObject resp8 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(120),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp8.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_DATA_FEDERATION_PRIVATE_ENDPOINTS",
        resp8.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveStreamsResources() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active Streams resources cannot be deleted
    final JSONObject resp8 =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(122),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp8.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_ACTIVE_STREAMS_RESOURCE", resp8.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithActiveEARPrivateEndpoints() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with active EAR private endpoints cannot be deleted
    final JSONObject resp =
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(124),
            HttpStatus.SC_CONFLICT,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CLOSE_GROUP_EAR_PRIVATE_ENDPOINT", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupWithFailedInvoiceOrg_succeeds() throws Exception {
    testDeleteGroupSetUp();

    // Test that a group in an organization with a failed invoice can be deleted
    doDigestJsonDelete(
        "api/atlas/v1.0/groups/" + oid(115), HttpStatus.SC_ACCEPTED, ADMIN_USERNAME, ADMIN_API_KEY);
  }

  @Test
  public void testDeleteGroupWithNoActiveBackups_succeeds() throws Exception {
    testDeleteGroupSetUp();
    // Test that a group with NO active backups can be deleted
    doDigestJsonDelete(
        "api/atlas/v1.0/groups/" + oid(101), HttpStatus.SC_ACCEPTED, ADMIN_USERNAME, ADMIN_API_KEY);
  }

  @Test
  public void testGetGroupUsers() throws Exception {
    populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/user/UserDao/usersWithCharts.json.ftl",
        null,
        AppUser.DB_NAME,
        AppUser.COLLECTION_NAME);

    // Basic test
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/users",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(4, resp1.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results1 = resp1.getJSONArray(ApiListView.RESULTS_FIELD);
    assertNotNull(results1);
    assertEquals(4, results1.length());
    assertTrue(hasUsers(results1, 6, 4, 13, 2));

    // Test with a group that doesn't exist
    final JSONObject resp2 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(1000) + "/users",
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals("GROUP_NOT_FOUND", resp2.getString(ApiError.ERROR_CODE_FIELD));

    // Charts Admin
    final JSONObject resp3 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/users",
            HttpStatus.SC_OK,
            CHARTS_USER_NAME,
            CHARTS_API_KEY);
    assertEquals(4, resp3.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results2 = resp3.getJSONArray(ApiListView.RESULTS_FIELD);
    assertNotNull(results2);
    assertEquals(4, results2.length());
    assertTrue(hasUsers(results2, 6, 4, 13, 2));
  }

  @Test
  public void testGetGroupUsersWhenUserIsOrgOwner_returnsThatUser() throws Exception {
    // GIVEN
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    final Organization organization2 = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(organization);
    final String username = "<EMAIL>";
    MmsFactory.createUser(username);

    _userSvc.addUserToOrganization(
        username,
        organization.getId(),
        Collections.singletonList(Role.ORG_OWNER),
        MmsFactory.createAuditInfoWithAppUser());
    _userSvc.addUserToOrganization(
        username,
        organization2.getId(),
        Collections.singletonList(Role.ORG_OWNER),
        MmsFactory.createAuditInfoWithAppUser());

    // WHEN
    final JSONObject resp =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + group.getId() + "/users?includeOrgUsers=true",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    // THEN
    assertEquals(1, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results = resp.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(1, results.length());
  }

  @Test
  public void testGetGroupUsersWhenUserIsOrgOwnerInAnotherOrg_doesNotReturnThatUser()
      throws Exception {
    // GIVEN
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    final Organization organization2 = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(organization);
    final String username = "<EMAIL>";
    MmsFactory.createUser(username);

    _userSvc.addUserToOrganization(
        username,
        organization.getId(),
        Collections.singletonList(Role.ORG_MEMBER),
        MmsFactory.createAuditInfoWithAppUser());
    _userSvc.addUserToOrganization(
        username,
        organization2.getId(),
        Collections.singletonList(Role.ORG_OWNER),
        MmsFactory.createAuditInfoWithAppUser());

    // WHEN
    final JSONObject resp =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + group.getId() + "/users?includeOrgUsers=true",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    // THEN
    assertEquals(0, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results = resp.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(0, results.length());
  }

  @Test
  public void testDeleteGroupUsers() throws Exception {
    testDeleteGroupUsersSetUp();
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/public/v1.0/groups/" + oid(102) + "/users",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray results1 = resp1.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(3, results1.length());
    assertTrue(hasUsers(results1, 4, 6, 3));

    // Test deleting a user from a group
    doDigestDelete(
        "api/public/v1.0/groups/" + oid(102) + "/users/" + oid(6),
        HttpStatus.SC_NO_CONTENT,
        ADMIN_USERNAME,
        ADMIN_API_KEY);
    final JSONObject resp2 =
        doDigestJsonGet(
            "api/public/v1.0/groups/" + oid(102) + "/users",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray results2 = resp2.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(2, results2.length());
    assertTrue(hasUsers(results2, 4, 3));
  }

  @Test
  public void testDeleteGroupUsersLastOwner() throws Exception {
    testDeleteGroupUsersSetUp();
    // Test deleting the last GROUP_OWNER from group oid(102)
    doDigestJsonDelete(
        "api/public/v1.0/groups/" + oid(102) + "/users/" + oid(3),
        HttpStatus.SC_NO_CONTENT,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    final JSONObject resp =
        doDigestJsonGet(
            "api/public/v1.0/groups/" + oid(102) + "/users",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray results = resp.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(2, results.length());
    assertTrue(hasUsers(results, 4, 6));
  }

  @Test
  public void testDeleteGroupUsersInvalidUser() throws Exception {
    testDeleteGroupUsersSetUp();
    // Test deleting a user that doesn't exist
    final JSONObject resp =
        doDigestJsonDelete(
            "api/public/v1.0/groups/" + oid(102) + "/users/" + oid(1000),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
    assertEquals("USER_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupUsersInvalidGroup() throws Exception {
    testDeleteGroupUsersSetUp();
    final JSONObject resp =
        doDigestJsonDelete(
            "api/public/v1.0/groups/" + oid(102) + "/users/" + oid(1),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
    assertEquals("USER_NOT_IN_GROUP", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testDeleteGroupUsersGroupDoesntExist() throws Exception {
    testDeleteGroupUsersSetUp();
    // Test deleting a user from a group that doesn't exist
    final JSONObject resp =
        doDigestJsonDelete(
            "api/public/v1.0/groups/" + oid(1000) + "/users/" + oid(1),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
    assertEquals("GROUP_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetGroupUsers_withFlattenRoles() {

    // Users setup
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(organization);
    final String userWithImpliedGroupUsername = "userWithImpliedGroup";
    final String userWithImpliedOrgUsername = "userWithImpliedOrg";
    final String userWithImpliedGroupAndOrgUsername = "userWithImpliedGroupAndOrg";
    final AppUser userWithImpliedGroup = MmsFactory.createUser(userWithImpliedGroupUsername);
    final AppUser userWithImpliedOrg = MmsFactory.createUser(userWithImpliedOrgUsername);
    final AppUser userWithImpliedGroupAndOrg =
        MmsFactory.createUser(userWithImpliedGroupAndOrgUsername);

    // Roles setup
    final Set<Role> impliedGroupRoles = Set.of(Role.GROUP_MONITORING_ADMIN);
    final Set<Role> impliedGroupRolesFlattened =
        Set.of(Role.GROUP_MONITORING_ADMIN, Role.GROUP_READ_ONLY);
    final Set<Role> impliedOrgRoles = Set.of(Role.ORG_BILLING_ADMIN);
    final Set<Role> impliedOrgRolesFlattened =
        Set.of(
            Role.ORG_MEMBER,
            Role.ORG_BILLING_ADMIN,
            Role.GROUP_BILLING_ADMIN,
            Role.ORG_BILLING_READ_ONLY);
    final Set<Role> impliedGroupAndOrgRolesFlattened = new HashSet<>();
    impliedGroupAndOrgRolesFlattened.addAll(impliedGroupRolesFlattened);
    impliedGroupAndOrgRolesFlattened.addAll(impliedOrgRolesFlattened);

    // Users with implied roles
    _userDao.addGroupIdAndAssignRoles(
        userWithImpliedGroup.getId(), group.getId(), impliedGroupRoles);
    _userDao.addOrgIdAndAssignRoles(
        userWithImpliedOrg.getId(), organization.getId(), impliedOrgRoles);
    _userDao.addGroupIdAndAssignRoles(
        userWithImpliedOrg.getId(), group.getId(), Collections.emptySet());
    _userDao.addGroupIdAndAssignRoles(
        userWithImpliedGroupAndOrg.getId(), group.getId(), impliedGroupRoles);
    _userDao.addOrgIdAndAssignRoles(
        userWithImpliedGroupAndOrg.getId(), organization.getId(), impliedOrgRoles);

    final JSONObject flattenedResponse =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + group.getId().toString() + "/users?flattenRoles=true",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray flattenedUsers = flattenedResponse.getJSONArray(ApiListView.RESULTS_FIELD);

    final JSONObject jsonUserWithImpliedGroup =
        findUserByUsername(flattenedUsers, userWithImpliedGroupUsername);
    assertNotNull(jsonUserWithImpliedGroup);
    assertTrue(hasFlattenedRoles(jsonUserWithImpliedGroup, impliedGroupRolesFlattened));

    final JSONObject jsonUserWithImpliedOrg =
        findUserByUsername(flattenedUsers, userWithImpliedOrgUsername);
    assertNotNull(jsonUserWithImpliedOrg);
    assertTrue(hasFlattenedRoles(jsonUserWithImpliedOrg, impliedOrgRolesFlattened));

    final JSONObject jsonUserWithImpliedOrgAndGroup =
        findUserByUsername(flattenedUsers, userWithImpliedGroupAndOrgUsername);
    assertNotNull(jsonUserWithImpliedOrgAndGroup);
    assertTrue(hasFlattenedRoles(jsonUserWithImpliedOrgAndGroup, impliedGroupAndOrgRolesFlattened));

    final JSONObject unflattenedResponse =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + group.getId().toString() + "/users",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray unflattenedUsers = unflattenedResponse.getJSONArray(ApiListView.RESULTS_FIELD);

    final JSONObject jsonUser1 = findUserByUsername(unflattenedUsers, userWithImpliedGroupUsername);
    assertNotNull(jsonUser1);
    assertFalse(jsonUser1.has(ApiAppUserView.FLATTENED_ROLES_FIELD));

    final JSONObject jsonUser2 = findUserByUsername(unflattenedUsers, userWithImpliedOrgUsername);
    assertNotNull(jsonUser2);
    assertFalse(jsonUser2.has(ApiAppUserView.FLATTENED_ROLES_FIELD));

    final JSONObject jsonUser3 =
        findUserByUsername(unflattenedUsers, userWithImpliedGroupAndOrgUsername);
    assertNotNull(jsonUser3);
    assertFalse(jsonUser3.has(ApiAppUserView.FLATTENED_ROLES_FIELD));
  }

  @Test
  public void testGetGroupUsers_withIncludeOrgUsers() throws Exception {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final AppUser groupUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);
    final AppUser orgUser =
        MmsFactory.createUserWithRoleInOrganization(
            org, "org-user" + getUniquifier() + "@example.com", Role.ORG_OWNER);

    final JSONObject response =
        HttpUtils.getInstance()
            .get()
            .path("api/atlas/v1.0/groups/%s/users?includeOrgUsers=true", group.getId())
            .digestAuth(ADMIN_USERNAME, ADMIN_API_KEY)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    final JSONArray responseUsers = response.getJSONArray(ApiListView.RESULTS_FIELD);
    final Set<String> responseUsernames = new HashSet<>();
    for (int i = 0; i < responseUsers.length(); i++) {
      final JSONObject user = responseUsers.getJSONObject(i);
      responseUsernames.add(user.getString(ApiAppUserView.USERNAME_FIELD));
    }

    assertEquals(2, responseUsernames.size());
    assertEquals(Set.of(groupUser.getUsername(), orgUser.getUsername()), responseUsernames);
  }

  @Test
  public void testGetGroupSettings_defaults() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final JSONObject response =
        HttpUtils.getInstance()
            .get()
            .path("api/atlas/v1.0/groups/%s/settings", group.getId())
            .digestAuth(ADMIN_USERNAME, ADMIN_API_KEY)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertTrue(response.getBoolean(EXPECTED_REALTIME_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_DATA_EXPLORER_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY));
    assertFalse(
        response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_SCHEMA_ADVISOR_KEY));
    assertTrue(response.getBoolean(EXPECTED_DB_STATS_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY));
  }

  @Test
  public void testPatchGroupSettings_emptyUpdate() throws Exception {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final JSONObject fullUpdate = new JSONObject();

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s/settings", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertTrue(response.getBoolean(EXPECTED_REALTIME_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_DATA_EXPLORER_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY));
    assertFalse(
        response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_SCHEMA_ADVISOR_KEY));
    assertTrue(response.getBoolean(EXPECTED_DB_STATS_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY));
  }

  @Test
  public void testPatchGroupSettings_fullUpdate_allDisabled() throws Exception {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final JSONObject fullUpdate = new JSONObject();
    fullUpdate.put(EXPECTED_REALTIME_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_DATA_EXPLORER_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_SCHEMA_ADVISOR_KEY, false);
    fullUpdate.put(EXPECTED_DB_STATS_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY, false);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s/settings", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertFalse(response.getBoolean(EXPECTED_REALTIME_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_DATA_EXPLORER_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY));
    assertFalse(
        response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_SCHEMA_ADVISOR_KEY));
    assertFalse(response.getBoolean(EXPECTED_DB_STATS_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY));

    // assert that getGroupSettings also reflects the updated state
    final JSONObject getGroupSettingsResponse =
        HttpUtils.getInstance()
            .get()
            .path("api/atlas/v1.0/groups/%s/settings", group.getId())
            .digestAuth(ADMIN_USERNAME, ADMIN_API_KEY)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();
    assertNotNull(getGroupSettingsResponse);
    assertFalse(getGroupSettingsResponse.getBoolean(EXPECTED_REALTIME_SETTING_KEY));
    assertFalse(getGroupSettingsResponse.getBoolean(EXPECTED_DATA_EXPLORER_SETTING_KEY));
    assertFalse(
        getGroupSettingsResponse.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY));
    assertFalse(
        getGroupSettingsResponse.getBoolean(
            EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY));
    assertFalse(getGroupSettingsResponse.getBoolean(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY));
    assertFalse(getGroupSettingsResponse.getBoolean(EXPECTED_SCHEMA_ADVISOR_KEY));
    assertFalse(getGroupSettingsResponse.getBoolean(EXPECTED_DB_STATS_SETTING_KEY));
    assertFalse(getGroupSettingsResponse.getBoolean(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY));
  }

  @Test
  public void testPatchGroupSettings_fullUpdate_mixed() throws Exception {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final JSONObject fullUpdate = new JSONObject();
    fullUpdate.put(EXPECTED_REALTIME_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_DATA_EXPLORER_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_SCHEMA_ADVISOR_KEY, true);
    fullUpdate.put(EXPECTED_DB_STATS_SETTING_KEY, true);
    fullUpdate.put(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY, true);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s/settings", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertFalse(response.getBoolean(EXPECTED_REALTIME_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_DATA_EXPLORER_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY));
    assertFalse(
        response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_SCHEMA_ADVISOR_KEY));
    assertTrue(response.getBoolean(EXPECTED_DB_STATS_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY));
  }

  @Test
  public void testPatchGroupSettings_partialUpdate() throws Exception {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final JSONObject fullUpdate = new JSONObject();
    fullUpdate.put(EXPECTED_REALTIME_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY, false);
    fullUpdate.put(EXPECTED_SCHEMA_ADVISOR_KEY, false);
    fullUpdate.put(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY, true);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s/settings", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertFalse(response.getBoolean(EXPECTED_REALTIME_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_DATA_EXPLORER_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_FEATURES_SETTING_KEY));
    assertFalse(
        response.getBoolean(EXPECTED_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_PERFORMANCE_ADVISOR_SETTING_KEY));
    assertFalse(response.getBoolean(EXPECTED_SCHEMA_ADVISOR_KEY));
    assertTrue(response.getBoolean(EXPECTED_DB_STATS_SETTING_KEY));
    assertTrue(response.getBoolean(EXPECTED_EXTENDED_STORAGE_SIZES_SETTING_KEY));
  }

  @Test
  public void testPatchGroupName_withValidGroup() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final String updatedGroupName = "cluster20";

    final JSONObject fullUpdate = new JSONObject();
    fullUpdate.put(GROUP_NAME, updatedGroupName);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONObject.class)
            .send();
    final JSONObject jsonResponseById =
        getGroup(
            group.getId().toString(),
            HttpStatus.SC_OK,
            false,
            false,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());

    assertNotNull(response);
    assertGroupJsonResponse(
        updatedGroupName,
        jsonResponseById,
        HttpStatus.SC_OK,
        group.getShardCount(),
        null,
        false,
        false,
        false,
        false,
        false,
        true);
  }

  @Test
  public void testPatchGroupName_withInvalidGroupId() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final String newGroupName = "cluster20";

    final JSONObject fullUpdate = new JSONObject();
    fullUpdate.put(GROUP_NAME, newGroupName);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/null")
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_NOT_FOUND)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertEquals(ApiErrorCode.INVALID_GROUP_ID.toString(), response.get("errorCode"));
  }

  @Test
  public void testPatchGroup_withUndefinedGroupName() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final JSONObject fullUpdate = new JSONObject();

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertEquals(ApiErrorCode.INVALID_PROJECT_UPDATE.toString(), response.get("errorCode"));
  }

  @Test
  public void testPatchGroup_withEmptyGroupName() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final String newGroupName = "";

    final JSONObject fullUpdate = new JSONObject();
    fullUpdate.put(GROUP_NAME, newGroupName);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .data(fullUpdate)
            .path("api/atlas/v1.0/groups/%s", group.getId())
            .digestAuth(groupOwner.getUsername(), groupOwnerApiKey.getKey())
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(response);
    assertEquals(ApiErrorCode.INVALID_PROJECT_UPDATE.toString(), response.get("errorCode"));
  }

  @Test
  public void testSetUserManagedLimit() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());
    final String limitsUrl = String.format("api/atlas/v1.0/groups/%s/limits", group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final AppUser groupUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_ADMIN);

    final UserApiKey groupUserApiKey =
        MmsFactory.generateUserApiKey(groupUser.getId(), "Group User API Key");

    // verify limits starts with default value
    assertEquals(
        Defaults.MONGODB_USERS,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MONGODB_USERS.getLimitField().getValue()));
    assertEquals(
        Defaults.NUM_CLUSTERS,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.NUM_CLUSTERS.getLimitField().getValue()));
    assertEquals(
        Defaults.NUM_SERVERLESS_MTMS,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitField().getValue()));
    assertEquals(
        Defaults.NUM_USER_CUSTOM_ROLES,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitField().getValue()));
    assertEquals(
        Defaults.MAX_NETWORK_PERMISSION_ENTRIES,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitField().getValue()));
    assertEquals(
        Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup,
            ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES
                .getLimitField()
                .getValue()));
    assertEquals(
        Defaults.MAX_NODES_PER_PRIVATELINK_REGION,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup,
            ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitField().getValue()));

    // unauthorized
    doDigestJsonPatch(
        limitsUrl + "/" + ProjectLimitName.MONGODB_USERS.getLimitName(),
        new JSONObject()
            .put(FieldDefs.NAME, ProjectLimitName.MONGODB_USERS.getLimitName())
            .put(FieldDefs.VALUE, 200),
        HttpStatus.SC_UNAUTHORIZED,
        groupUser.getUsername(),
        groupUserApiKey.getKey());

    // limit not found
    doDigestJsonPatch(
        limitsUrl + "/badLimit",
        new JSONObject().put(FieldDefs.NAME, "badLimit").put(FieldDefs.VALUE, 200),
        HttpStatus.SC_NOT_FOUND,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // invalid limit increase
    doDigestJsonPatch(
        limitsUrl + "/" + ProjectLimitName.MONGODB_USERS.getLimitName(),
        new JSONObject()
            .put(FieldDefs.NAME, ProjectLimitName.MONGODB_USERS.getLimitName())
            .put(FieldDefs.VALUE, 1000),
        HttpStatus.SC_BAD_REQUEST,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // success - MONGODB_USERS
    final JSONObject mongodbUsersRequest = new JSONObject();
    mongodbUsersRequest.put(FieldDefs.NAME, "GROUP_READ_ONLY");
    JSONObject mongodbUsers =
        doDigestJsonPatch(
            limitsUrl + "/" + ProjectLimitName.MONGODB_USERS.getLimitName(),
            new JSONObject()
                .put(FieldDefs.NAME, ProjectLimitName.MONGODB_USERS.getLimitName())
                .put(FieldDefs.VALUE, 200),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(mongodbUsers.get(FieldDefs.NAME), ProjectLimitName.MONGODB_USERS.getLimitName());
    assertEquals(mongodbUsers.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(mongodbUsers.get(FieldDefs.VALUE), 200);
    assertEquals(mongodbUsers.get(FieldDefs.DEFAULT_LIMIT), Defaults.MONGODB_USERS);
    assertEquals(
        mongodbUsers.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MONGODB_USERS.getLimitField().getMaximum());
    assertEquals(
        200,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MONGODB_USERS.getLimitField().getValue()));

    // success - NUM_CLUSTERS
    JSONObject numClusters =
        doDigestJsonPatch(
            limitsUrl + "/" + ProjectLimitName.NUM_CLUSTERS.getLimitName(),
            new JSONObject()
                .put(FieldDefs.NAME, ProjectLimitName.NUM_CLUSTERS.getLimitName())
                .put(FieldDefs.VALUE, 70),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(numClusters.get(FieldDefs.NAME), ProjectLimitName.NUM_CLUSTERS.getLimitName());
    assertEquals(numClusters.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(numClusters.get(FieldDefs.VALUE), 70);
    assertEquals(numClusters.get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_CLUSTERS);
    assertEquals(
        numClusters.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_CLUSTERS.getLimitField().getMaximum());
    assertEquals(
        70,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.NUM_CLUSTERS.getLimitField().getValue()));

    // success - NUM_SERVERLESS_MTMS
    JSONObject numServerlessMTMs =
        doDigestJsonPatch(
            limitsUrl + "/" + ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitName(),
            new JSONObject()
                .put(FieldDefs.NAME, ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitName())
                .put(FieldDefs.VALUE, 70),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        numServerlessMTMs.get(FieldDefs.NAME), ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitName());
    assertEquals(numServerlessMTMs.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(numServerlessMTMs.get(FieldDefs.VALUE), 70);
    assertEquals(numServerlessMTMs.get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_SERVERLESS_MTMS);
    assertEquals(
        numServerlessMTMs.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitField().getMaximum());
    assertEquals(
        70,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitField().getValue()));

    // success - NUM_USER_CUSTOM_ROLES
    JSONObject numUserCustomRoles =
        doDigestJsonPatch(
            limitsUrl + "/" + ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitName(),
            new JSONObject()
                .put(FieldDefs.NAME, ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitName())
                .put(FieldDefs.VALUE, 1000),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(numUserCustomRoles.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(numUserCustomRoles.get(FieldDefs.VALUE), 1000);
    assertEquals(numUserCustomRoles.get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_USER_CUSTOM_ROLES);
    assertEquals(
        numUserCustomRoles.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitField().getMaximum());
    assertEquals(
        1000,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitField().getValue()));

    // success - MAX_NETWORK_PERMISSION_ENTRIES
    final long newMaxNetworkPermissionsEntries = Defaults.MAX_NETWORK_PERMISSION_ENTRIES + 50L;
    JSONObject maxNetworkPermissionEntries =
        doDigestJsonPatch(
            limitsUrl + "/" + ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitName(),
            new JSONObject()
                .put(FieldDefs.NAME, ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitName())
                .put(FieldDefs.VALUE, newMaxNetworkPermissionsEntries),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        maxNetworkPermissionEntries.get(FieldDefs.NAME),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitName());
    assertEquals(0, maxNetworkPermissionEntries.get(FieldDefs.CURRENT_USAGE));
    assertEquals(
        newMaxNetworkPermissionsEntries, maxNetworkPermissionEntries.getLong(FieldDefs.VALUE));
    assertEquals(
        Defaults.MAX_NETWORK_PERMISSION_ENTRIES,
        maxNetworkPermissionEntries.get(FieldDefs.DEFAULT_LIMIT));
    assertEquals(
        maxNetworkPermissionEntries.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitField().getMaximum());
    assertEquals(
        newMaxNetworkPermissionsEntries,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitField().getValue()));

    /**
     * invalid limit increase - total number of network permissions exceed {@link
     * Limits#TOTAL_NETWORK_PERMISSION_ENTRIES}
     */
    doDigestJsonPatch(
        limitsUrl
            + "/"
            + ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName(),
        new JSONObject()
            .put(
                FieldDefs.NAME,
                ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName())
            .put(FieldDefs.VALUE, 200),
        HttpStatus.SC_BAD_REQUEST,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());
    assertEquals(
        Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup,
            ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES
                .getLimitField()
                .getValue()));

    // success - MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES
    final long newMaxCrossRegionPermissionEntries =
        Limits.TOTAL_NETWORK_PERMISSION_ENTRIES - newMaxNetworkPermissionsEntries;
    assertTrue(
        newMaxCrossRegionPermissionEntries > Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES);
    JSONObject maxCrossRegionNetworkPermissionEntries =
        doDigestJsonPatch(
            limitsUrl
                + "/"
                + ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName(),
            new JSONObject()
                .put(
                    FieldDefs.NAME,
                    ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName())
                .put(FieldDefs.VALUE, newMaxCrossRegionPermissionEntries),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        maxCrossRegionNetworkPermissionEntries.get(FieldDefs.NAME),
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName());
    assertEquals(0, maxCrossRegionNetworkPermissionEntries.get(FieldDefs.CURRENT_USAGE));
    assertEquals(
        newMaxCrossRegionPermissionEntries,
        maxCrossRegionNetworkPermissionEntries.getLong(FieldDefs.VALUE));
    assertEquals(
        Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES,
        maxCrossRegionNetworkPermissionEntries.get(FieldDefs.DEFAULT_LIMIT));
    assertEquals(
        maxCrossRegionNetworkPermissionEntries.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitField().getMaximum());
    assertEquals(
        newMaxCrossRegionPermissionEntries,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup,
            ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES
                .getLimitField()
                .getValue()));

    // success - MAX_NODES_PER_PRIVATELINK_REGION
    JSONObject maxNodesPerPrivatelinkRegion =
        doDigestJsonPatch(
            limitsUrl + "/" + ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitName(),
            new JSONObject()
                .put(
                    FieldDefs.NAME,
                    ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitName())
                .put(FieldDefs.VALUE, 50),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        maxNodesPerPrivatelinkRegion.get(FieldDefs.NAME),
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitName());
    assertEquals(maxNodesPerPrivatelinkRegion.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(maxNodesPerPrivatelinkRegion.get(FieldDefs.VALUE), 50);
    assertEquals(
        maxNodesPerPrivatelinkRegion.get(FieldDefs.DEFAULT_LIMIT),
        Defaults.MAX_NODES_PER_PRIVATELINK_REGION);
    assertEquals(
        maxNodesPerPrivatelinkRegion.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitField().getMaximum());
    assertEquals(
        50,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup,
            ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitField().getValue()));

    // success - GCP PSC fields
    final String pscNumNodesLimitName =
        ProjectLimitName.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP.getLimitName();
    final Integer pscNewNumPSCsPerRGLimit = 13;
    {
      final JSONObject setNumPSC =
          doDigestJsonPatch(
              limitsUrl + "/" + pscNumNodesLimitName,
              new JSONObject()
                  .put(FieldDefs.NAME, pscNumNodesLimitName)
                  .put(FieldDefs.VALUE, pscNewNumPSCsPerRGLimit),
              HttpStatus.SC_OK,
              groupOwner.getUsername(),
              groupOwnerApiKey.getKey());
      assertEquals(setNumPSC.get(FieldDefs.VALUE), pscNewNumPSCsPerRGLimit);
      assertEquals(setNumPSC.get(FieldDefs.NAME), pscNumNodesLimitName);
    }

    final String pscSubnetMaskLimitName = ProjectLimitName.GCP_PSC_NAT_SUBNET_MASK.getLimitName();
    final Integer newPSCSubnetMask = Limits.FieldDefs.GCP_PSC_NAT_SUBNET_MASK.getMaximum();
    {
      final JSONObject setSubnetMask =
          doDigestJsonPatch(
              limitsUrl + "/" + pscSubnetMaskLimitName,
              new JSONObject()
                  .put(FieldDefs.NAME, pscSubnetMaskLimitName)
                  .put(FieldDefs.VALUE, newPSCSubnetMask),
              HttpStatus.SC_OK,
              groupOwner.getUsername(),
              groupOwnerApiKey.getKey());
      assertEquals(setSubnetMask.get(FieldDefs.VALUE), newPSCSubnetMask);
      assertEquals(setSubnetMask.get(FieldDefs.NAME), pscSubnetMaskLimitName);
    }

    // Above limit - get error
    {
      doDigestJsonPatch(
          limitsUrl + "/" + pscSubnetMaskLimitName,
          new JSONObject()
              .put(FieldDefs.VALUE, Limits.FieldDefs.GCP_PSC_NAT_SUBNET_MASK.getMaximum() + 1),
          HttpStatus.SC_BAD_REQUEST,
          groupOwner.getUsername(),
          groupOwnerApiKey.getKey());
      doDigestJsonPatch(
          limitsUrl + "/" + pscNumNodesLimitName,
          new JSONObject()
              .put(FieldDefs.NAME, pscNumNodesLimitName)
              .put(
                  FieldDefs.VALUE,
                  Limits.FieldDefs.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP.getMaximum()
                      + 1),
          HttpStatus.SC_BAD_REQUEST,
          groupOwner.getUsername(),
          groupOwnerApiKey.getKey());
    }
    // Below limit - get error
    doDigestJsonPatch(
        limitsUrl + "/" + pscSubnetMaskLimitName,
        new JSONObject().put(FieldDefs.VALUE, Limits.GCP_PSC_NAT_SUBNET_MASK_LOWER_LIMIT - 1),
        HttpStatus.SC_BAD_REQUEST,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // Create PSC - limit updates should now be blocked
    final GCPOrganization gcpOrganization =
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization());
    _gcpOrganizationDao.save(gcpOrganization);
    paymentMethodStubber.stubPaymentMethod(org.getId(), true);
    final String pscUrl =
        String.format("api/atlas/v1.0/groups/%s/privateEndpoint/endpointService", group.getId());
    final JSONObject createPSCReq =
        new JSONObject()
            .put(
                ApiAtlasCreateEndpointServiceRequestView.FieldDefs.PROVIDER_NAME,
                CloudProvider.GCP.name())
            .put(
                ApiAtlasCreateEndpointServiceRequestView.FieldDefs.REGION,
                GCPRegionName.CENTRAL_US.getName());
    doDigestJsonPost(
        pscUrl,
        createPSCReq,
        HttpStatus.SC_CREATED,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());
    doDigestJsonPatch(
        limitsUrl + "/" + pscSubnetMaskLimitName,
        new JSONObject()
            .put(FieldDefs.NAME, pscSubnetMaskLimitName)
            .put(FieldDefs.VALUE, newPSCSubnetMask),
        HttpStatus.SC_BAD_REQUEST,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    doDigestJsonPatch(
        limitsUrl + "/" + pscNumNodesLimitName,
        new JSONObject()
            .put(FieldDefs.NAME, pscNumNodesLimitName)
            .put(FieldDefs.VALUE, pscNewNumPSCsPerRGLimit),
        HttpStatus.SC_BAD_REQUEST,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());
  }

  @Test
  public void testGetUserManagedLimit() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());
    final String limitsUrl = String.format("api/atlas/v1.0/groups/%s/limits", group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final AppUser groupUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_ADMIN);

    MmsFactory.generateUserApiKey(groupUser.getId(), "Group User API Key");

    final AppUser globalAtlasAdminUser = MmsFactory.createGlobalAtlasAdminUser(group);

    // success - MONGODB_USERS
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.MONGODB_USERS.getLimitField().getValue(),
        300L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject mongodbUsers =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.MONGODB_USERS.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(mongodbUsers.get(FieldDefs.NAME), ProjectLimitName.MONGODB_USERS.getLimitName());
    assertEquals(mongodbUsers.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(mongodbUsers.get(FieldDefs.VALUE), 300);
    assertEquals(mongodbUsers.get(FieldDefs.DEFAULT_LIMIT), Defaults.MONGODB_USERS);
    assertEquals(
        mongodbUsers.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MONGODB_USERS.getLimitField().getMaximum());

    // success - MONGODB_USERS - test admin override limit
    final long limitSetByAdmin = ProjectLimitName.MONGODB_USERS.getLimitField().getMaximum() + 1;
    _ndsGroupSvc.setLimit(
        group.getId(),
        ProjectLimitName.MONGODB_USERS.getLimitField().getValue(),
        limitSetByAdmin,
        MmsFactory.createAuditInfoFromUiCall(globalAtlasAdminUser, true, "foo"),
        globalAtlasAdminUser);
    JSONObject mongodbUsers2 =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.MONGODB_USERS.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(mongodbUsers2.getLong(FieldDefs.MAXIMUM_LIMIT), limitSetByAdmin);

    // success - NUM_CLUSTERS
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.NUM_CLUSTERS.getLimitField().getValue(),
        80L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject numClusters =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.NUM_CLUSTERS.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(numClusters.get(FieldDefs.NAME), ProjectLimitName.NUM_CLUSTERS.getLimitName());
    assertEquals(numClusters.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(numClusters.get(FieldDefs.VALUE), 80);
    assertEquals(numClusters.get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_CLUSTERS);
    assertEquals(
        numClusters.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_CLUSTERS.getLimitField().getMaximum());

    // success - NUM_SERVERLESS_MTMS
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitField().getValue(),
        80L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject numServerlessMTMs =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        numServerlessMTMs.get(FieldDefs.NAME), ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitName());
    assertEquals(numServerlessMTMs.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(numServerlessMTMs.get(FieldDefs.VALUE), 80);
    assertEquals(numServerlessMTMs.get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_SERVERLESS_MTMS);
    assertEquals(
        numServerlessMTMs.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitField().getMaximum());

    // success - NUM_USER_CUSTOM_ROLES
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitField().getValue(),
        1000L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject numUserCustomRoles =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        numUserCustomRoles.get(FieldDefs.NAME),
        ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitName());
    assertEquals(numUserCustomRoles.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(numUserCustomRoles.get(FieldDefs.VALUE), 1000);
    assertEquals(numUserCustomRoles.get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_USER_CUSTOM_ROLES);
    assertEquals(
        numUserCustomRoles.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitField().getMaximum());

    // success - MAX_NETWORK_PERMISSION_ENTRIES
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitField().getValue(),
        50L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject maxNetworkPermissionEntries =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        maxNetworkPermissionEntries.get(FieldDefs.NAME),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitName());
    assertEquals(maxNetworkPermissionEntries.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(maxNetworkPermissionEntries.get(FieldDefs.VALUE), 50);
    assertEquals(
        maxNetworkPermissionEntries.get(FieldDefs.DEFAULT_LIMIT),
        Defaults.MAX_NETWORK_PERMISSION_ENTRIES);
    assertEquals(
        maxNetworkPermissionEntries.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitField().getMaximum());

    // success - MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitField().getValue(),
        190L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject maxCrossRegionNetworkPermissionEntries =
        doDigestJsonGet(
            limitsUrl
                + "/"
                + ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        maxCrossRegionNetworkPermissionEntries.get(FieldDefs.NAME),
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName());
    assertEquals(maxCrossRegionNetworkPermissionEntries.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(maxCrossRegionNetworkPermissionEntries.get(FieldDefs.VALUE), 190);
    assertEquals(
        maxCrossRegionNetworkPermissionEntries.get(FieldDefs.DEFAULT_LIMIT),
        Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES);
    assertEquals(
        maxCrossRegionNetworkPermissionEntries.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitField().getMaximum());

    // success - MAX_NODES_PER_PRIVATELINK_REGION
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitField().getValue(),
        50L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    JSONObject maxNodesPerPrivatelinkRegion =
        doDigestJsonGet(
            limitsUrl + "/" + ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitName(),
            HttpStatus.SC_OK,
            groupOwner.getUsername(),
            groupOwnerApiKey.getKey());
    assertEquals(
        maxNodesPerPrivatelinkRegion.get(FieldDefs.NAME),
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitName());
    assertEquals(maxNodesPerPrivatelinkRegion.get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(maxNodesPerPrivatelinkRegion.get(FieldDefs.VALUE), 50);
    assertEquals(
        maxNodesPerPrivatelinkRegion.get(FieldDefs.DEFAULT_LIMIT),
        Defaults.MAX_NODES_PER_PRIVATELINK_REGION);
    assertEquals(
        maxNodesPerPrivatelinkRegion.get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitField().getMaximum());
  }

  @Test
  public void testGetAllUserManagedLimits() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    _ndsGroupSvc.ensureGroup(group.getId());
    final String limitsUrl = String.format("api/atlas/v1.0/groups/%s/limits", group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    MmsFactory.createUserWithRoleInGroup(
        group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_ADMIN);

    final JSONArray results =
        doDigestJsonArrayGet(
            limitsUrl, HttpStatus.SC_OK, groupOwner.getUsername(), groupOwnerApiKey.getKey());

    assertEquals(Limits.USER_MANAGED_LIMITS_VALUES.size(), results.length());

    assertEquals(
        results.getJSONObject(0).get(FieldDefs.NAME),
        ProjectLimitName.MONGODB_USERS.getLimitName());
    assertEquals(results.getJSONObject(0).get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(results.getJSONObject(0).get(FieldDefs.VALUE), Defaults.MONGODB_USERS);
    assertEquals(results.getJSONObject(0).get(FieldDefs.DEFAULT_LIMIT), Defaults.MONGODB_USERS);
    assertEquals(
        results.getJSONObject(0).get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MONGODB_USERS.getLimitField().getMaximum());

    assertEquals(
        results.getJSONObject(1).get(FieldDefs.NAME), ProjectLimitName.NUM_CLUSTERS.getLimitName());
    assertEquals(results.getJSONObject(1).get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(results.getJSONObject(1).get(FieldDefs.VALUE), Defaults.NUM_CLUSTERS);
    assertEquals(results.getJSONObject(1).get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_CLUSTERS);
    assertEquals(
        results.getJSONObject(1).get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_CLUSTERS.getLimitField().getMaximum());

    assertEquals(
        results.getJSONObject(2).get(FieldDefs.NAME),
        ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitName());
    assertEquals(results.getJSONObject(2).get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(results.getJSONObject(2).get(FieldDefs.VALUE), Defaults.NUM_SERVERLESS_MTMS);
    assertEquals(
        results.getJSONObject(2).get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_SERVERLESS_MTMS);
    assertEquals(
        results.getJSONObject(2).get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_SERVERLESS_MTMS.getLimitField().getMaximum());

    assertEquals(
        results.getJSONObject(3).get(FieldDefs.NAME),
        ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitName());
    assertEquals(results.getJSONObject(3).get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(results.getJSONObject(3).get(FieldDefs.VALUE), Defaults.NUM_USER_CUSTOM_ROLES);
    assertEquals(
        results.getJSONObject(3).get(FieldDefs.DEFAULT_LIMIT), Defaults.NUM_USER_CUSTOM_ROLES);
    assertEquals(
        results.getJSONObject(3).get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.NUM_USER_CUSTOM_ROLES.getLimitField().getMaximum());

    assertEquals(
        results.getJSONObject(4).get(FieldDefs.NAME),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitName());
    assertEquals(results.getJSONObject(4).get(FieldDefs.CURRENT_USAGE), 0);
    assertEquals(
        results.getJSONObject(4).get(FieldDefs.VALUE), Defaults.MAX_NETWORK_PERMISSION_ENTRIES);
    assertEquals(
        results.getJSONObject(4).get(FieldDefs.DEFAULT_LIMIT),
        Defaults.MAX_NETWORK_PERMISSION_ENTRIES);
    assertEquals(
        results.getJSONObject(4).get(FieldDefs.MAXIMUM_LIMIT),
        ProjectLimitName.MAX_NETWORK_PERMISSION_ENTRIES.getLimitField().getMaximum());

    assertLimit(
        results.getJSONObject(5),
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitName(),
        Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES,
        0,
        Defaults.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES,
        ProjectLimitName.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getLimitField().getMaximum());

    final JSONObject maxNodesPerPrivatelinkRegion = results.getJSONObject(6);
    assertLimit(
        maxNodesPerPrivatelinkRegion,
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitName(),
        Defaults.MAX_NODES_PER_PRIVATELINK_REGION,
        0,
        Defaults.MAX_NODES_PER_PRIVATELINK_REGION,
        ProjectLimitName.MAX_NODES_PER_PRIVATELINK_REGION.getLimitField().getMaximum());
    final JSONObject pscPerRegion = results.getJSONObject(7);
    // Group creation populates the limit dynamically based off config
    final int expectedVal =
        _appSettings.getIntProp(
            Fields.NDS_GCP_PSC_REGION_GROUP_DEFAULT_SIZE.value,
            Defaults.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP);
    assertLimit(
        pscPerRegion,
        ProjectLimitName.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP.getLimitName(),
        expectedVal,
        null,
        Defaults.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP,
        ProjectLimitName.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP
            .getLimitField()
            .getMaximum());
    final JSONObject pscSubnetMask = results.getJSONObject(8);
    assertLimit(
        pscSubnetMask,
        ProjectLimitName.GCP_PSC_NAT_SUBNET_MASK.getLimitName(),
        Defaults.GCP_PSC_NAT_SUBNET_MASK,
        null,
        Defaults.GCP_PSC_NAT_SUBNET_MASK,
        ProjectLimitName.GCP_PSC_NAT_SUBNET_MASK.getLimitField().getMaximum());
  }

  private void assertLimit(
      final JSONObject pLimitDoc,
      final String pName,
      final Integer pValue,
      final Integer pCurrentUsage,
      final Integer pDefault,
      final Integer pMaximum) {
    assertEquals(pName, pLimitDoc.get(FieldDefs.NAME));
    assertEquals(pValue, pLimitDoc.get(FieldDefs.VALUE));
    if (pCurrentUsage != null) {
      assertEquals(pCurrentUsage, pLimitDoc.get(FieldDefs.CURRENT_USAGE));
    } else {
      assertFalse(pLimitDoc.has(FieldDefs.CURRENT_USAGE));
    }
    assertEquals(pDefault, pLimitDoc.get(FieldDefs.DEFAULT_LIMIT));
    assertEquals(pMaximum, pLimitDoc.get(FieldDefs.MAXIMUM_LIMIT));
  }

  @Test
  public void testDeleteLimit() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());
    final String limitsUrl = String.format("api/atlas/v1.0/groups/%s/limits", group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final AppUser groupUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_ADMIN);

    final UserApiKey groupUserApiKey =
        MmsFactory.generateUserApiKey(groupUser.getId(), "Group User API Key");

    final String limitName = ProjectLimitName.MONGODB_USERS.getLimitName();

    // verify limit starts with default value
    assertEquals(
        100,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MONGODB_USERS.getLimitField().getValue()));
    // set up a non-default limit
    _ndsGroupSvc.setUserManagedLimit(
        group.getId(),
        ProjectLimitName.MONGODB_USERS.getLimitField().getValue(),
        300L,
        AuditInfoHelpers.fromSystem(),
        groupOwner);
    assertEquals(
        300,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MONGODB_USERS.getLimitField().getValue()));

    // unauthorized
    doDigestJsonDelete(
        limitsUrl + "/" + limitName,
        HttpStatus.SC_UNAUTHORIZED,
        groupUser.getUsername(),
        groupUserApiKey.getKey());

    // limit not found
    doDigestJsonDelete(
        limitsUrl + "/badLimit",
        HttpStatus.SC_NOT_FOUND,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // success
    doDigestJsonDelete(
        limitsUrl + "/" + limitName,
        HttpStatus.SC_NO_CONTENT,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // verify limit is back to default
    assertEquals(
        100,
        _ndsGroupSvc.getCurrentUserManagedLimit(
            ndsGroup, ProjectLimitName.MONGODB_USERS.getLimitField().getValue()));
  }

  @Test
  public void testSetDataFederationLimit() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final String limitsUrl =
        String.format("api/atlas/v1.0/groups/%s/limits/dataFederation.", group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final AppUser groupUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_ADMIN);

    final UserApiKey groupUserApiKey =
        MmsFactory.generateUserApiKey(groupUser.getId(), "Group User API Key");

    final ApiAtlasDataFederationQueryLimitView.LimitName limitName =
        ApiAtlasDataFederationQueryLimitView.LimitName.BYTES_PROCESSED_DAILY;

    final long usageLimitsInBytes = (long) Units.convert(118.0, Units.GIGABYTES, Units.BYTES);
    final JSONObject usageLimitViewJSON =
        getApiAtlasQueryLimitViewJSON(usageLimitsInBytes, OverrunPolicy.BLOCK_AND_KILL.name());

    // invalid permission
    {
      final JSONObject response =
          doDigestJsonPatch(
              limitsUrl + limitName.getLimitName(),
              usageLimitViewJSON,
              HttpStatus.SC_UNAUTHORIZED,
              groupUser.getUsername(),
              groupUserApiKey.getKey());
      assertEquals("Unauthorized", response.getString(ApiError.REASON_FIELD));
      assertEquals(
          ApiErrorCode.USER_UNAUTHORIZED.name(), response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // invalid limit span
    {
      final JSONObject response =
          doDigestJsonPatch(
              limitsUrl + "badLimitName",
              usageLimitViewJSON,
              HttpStatus.SC_NOT_FOUND,
              groupOwner.getUsername(),
              groupOwnerApiKey.getKey());
      assertEquals("Not Found", response.getString(ApiError.REASON_FIELD));
      assertEquals(
          ApiErrorCode.LIMIT_NOT_FOUND.name(), response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // invalid overrun policy
    {
      final JSONObject usageLimitViewJSON2 =
          getApiAtlasQueryLimitViewJSON(usageLimitsInBytes, "badOverrunPolicy");
      final JSONObject response =
          doDigestJsonPatch(
              limitsUrl + limitName.getLimitName(),
              usageLimitViewJSON2,
              HttpStatus.SC_BAD_REQUEST,
              groupOwner.getUsername(),
              groupOwnerApiKey.getKey());
      assertEquals("Bad Request", response.getString(ApiError.REASON_FIELD));
      assertEquals(
          ApiErrorCode.INVALID_ENUM_VALUE.name(), response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // success
    {
      final JSONObject response =
          doDigestJsonPatch(
              limitsUrl + limitName.getLimitName(),
              usageLimitViewJSON,
              HttpStatus.SC_OK,
              groupOwner.getUsername(),
              groupOwnerApiKey.getKey());
      assertEquals(usageLimitsInBytes, response.getLong(FieldDefs.VALUE));
      assertEquals(
          OverrunPolicy.BLOCK_AND_KILL.name(),
          response.getString(ApiAtlasDataFederationQueryLimitView.FieldDefs.OVERRUN_POLICY));
      assertFalse(response.has(ApiAtlasDataFederationTenantQueryLimitView.FieldDefs.TENANT_NAME));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getUsageLimit(group.getId().toHexString(), LimitSpan.LIMIT_SPAN_DAILY.name())
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(), LimitSpan.LIMIT_SPAN_DAILY);
      assertEquals(actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(), usageLimitsInBytes);
    }

    // success - update
    {
      final Long limitBytes = (long) Units.convert(120.0, Units.GIGABYTES, Units.BYTES);
      _dataLakeTestUtils.setUsageLimit(
          _ndsDataLakePublicSvc.toUsageLimit(
              DataFederationUsageLimitView.builder()
                  .groupId(group.getId())
                  .limitSpan(limitName.getLimitSpan())
                  .limitInBytes(limitBytes)
                  .overrunPolicy(OverrunPolicy.BLOCK)
                  .build()));
      // verify limit is set
      assertEquals(
          limitBytes,
          _ndsDataLakePublicSvc
              .getProjectUsageLimit(group.getId(), limitName.getLimitSpan())
              .flatMap(DataFederationUsageLimitView::getLimitInBytes)
              .orElseThrow());
      final JSONObject response =
          doDigestJsonPatch(
              limitsUrl + limitName.getLimitName(),
              usageLimitViewJSON,
              HttpStatus.SC_OK,
              groupOwner.getUsername(),
              groupOwnerApiKey.getKey());
      assertEquals(usageLimitsInBytes, response.getLong(FieldDefs.VALUE));
      assertEquals(
          OverrunPolicy.BLOCK_AND_KILL.name(),
          response.getString(ApiAtlasDataFederationQueryLimitView.FieldDefs.OVERRUN_POLICY));
      assertFalse(response.has(ApiAtlasDataFederationTenantQueryLimitView.FieldDefs.TENANT_NAME));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getUsageLimit(group.getId().toHexString(), LimitSpan.LIMIT_SPAN_DAILY.name())
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(), LimitSpan.LIMIT_SPAN_DAILY);
      assertEquals(actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(), usageLimitsInBytes);
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getOverrunPolicy(),
          Models.OverrunPolicy.OVERRUN_POLICY_BLOCK_AND_KILL);
    }
  }

  @Test
  public void testDeleteDataFederationLimit() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final String limitsUrl =
        String.format("api/atlas/v1.0/groups/%s/limits/dataFederation.", group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    final AppUser groupUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-user" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_ADMIN);

    final UserApiKey groupUserApiKey =
        MmsFactory.generateUserApiKey(groupUser.getId(), "Group User API Key");

    final ApiAtlasDataFederationQueryLimitView.LimitName limitName =
        ApiAtlasDataFederationQueryLimitView.LimitName.BYTES_PROCESSED_DAILY;

    // verify limit doesn't exist
    assertTrue(
        _ndsDataLakePublicSvc
            .getProjectUsageLimit(group.getId(), limitName.getLimitSpan())
            .isEmpty());
    // set up a limit
    final Long limitBytes = (long) Units.convert(1, Units.GIGABYTES, Units.BYTES);
    _dataLakeTestUtils.setUsageLimit(
        _ndsDataLakePublicSvc.toUsageLimit(
            DataFederationUsageLimitView.builder()
                .groupId(group.getId())
                .limitSpan(limitName.getLimitSpan())
                .limitInBytes(limitBytes)
                .overrunPolicy(OverrunPolicy.BLOCK)
                .build()));
    // verify limit is set
    assertEquals(
        limitBytes,
        _ndsDataLakePublicSvc
            .getProjectUsageLimit(group.getId(), limitName.getLimitSpan())
            .flatMap(DataFederationUsageLimitView::getLimitInBytes)
            .orElseThrow());

    // unauthorized
    doDigestJsonDelete(
        limitsUrl + limitName.getLimitName(),
        HttpStatus.SC_UNAUTHORIZED,
        groupUser.getUsername(),
        groupUserApiKey.getKey());

    // limit not found
    doDigestJsonDelete(
        limitsUrl + "badLimit",
        HttpStatus.SC_NOT_FOUND,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // success
    doDigestJsonDelete(
        limitsUrl + limitName.getLimitName(),
        HttpStatus.SC_NO_CONTENT,
        groupOwner.getUsername(),
        groupOwnerApiKey.getKey());

    // verify limit is gone
    assertTrue(
        _ndsDataLakePublicSvc
            .getProjectUsageLimit(group.getId(), limitName.getLimitSpan())
            .isEmpty());
  }

  @Test
  public void testGetAllQueryLimits_GroupOwner() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);
    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");
    testGetAllQueryLimits(ndsGroup, groupOwner.getUsername(), groupOwnerApiKey.getKey());
  }

  @Test
  public void testGetAllQueryLimits_GroupReadOnly() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());
    MmsFactory.createUserWithRoleInGroup(
        group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final AppUser groupReadOnlyUser =
        MmsFactory.createUserWithRoleInGroup(
            group, "read-only" + getUniquifier() + "@example.com", Role.GROUP_READ_ONLY);
    final UserApiKey groupReadOnlyApiKey =
        MmsFactory.generateUserApiKey(groupReadOnlyUser.getId(), "Group Read-Only API Key");
    testGetAllQueryLimits(ndsGroup, groupReadOnlyUser.getUsername(), groupReadOnlyApiKey.getKey());
  }

  @Test
  public void testGetAllQueryLimits_GroupDataAccessReadOnly() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());
    MmsFactory.createUserWithRoleInGroup(
        group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final AppUser groupReadOnlyUser =
        MmsFactory.createUserWithRoleInGroup(
            group,
            "read-only" + getUniquifier() + "@example.com",
            Role.GROUP_DATA_ACCESS_READ_ONLY);
    final UserApiKey groupReadOnlyApiKey =
        MmsFactory.generateUserApiKey(groupReadOnlyUser.getId(), "Group Read-Only API Key");
    testGetAllQueryLimits(ndsGroup, groupReadOnlyUser.getUsername(), groupReadOnlyApiKey.getKey());
  }

  private void testGetAllQueryLimits(
      final NDSGroup pNdsGroup, final String pUsername, final String pApiKey) throws SvcException {
    final ObjectId groupId = pNdsGroup.getGroupId();
    final String limitsUrl =
        String.format("api/atlas/v1.0/groups/%s/dataFederationLimits", groupId);

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(0, resp.length());
    }

    _dataLakeTestUtils.setUsageLimit(LimitTestFactory.getTenantUsageLimit());
    _dataLakeTestUtils.setUsageLimit(LimitTestFactory.getProjectUsageLimit());

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(0, resp.length());
    }

    final UsageLimit projectLimit = LimitTestFactory.getProjectUsageLimit(groupId);
    _dataLakeTestUtils.setUsageLimit(projectLimit);

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(1, resp.length());
      final JSONObject obj = resp.getJSONObject(0);
      assertEquals("dataFederation.bytesProcessed.query", obj.getString("name"));
      assertEquals(projectLimit.getPerQueryScanningLimit().getLimitBytes(), obj.getLong("value"));
      assertEquals(0, obj.getLong("currentUsage"));
      assertFalse(obj.has("defaultLimit"));
      assertFalse(obj.has("maximumLimit"));
      assertFalse(obj.has("overrunPolicy"));
      assertTrue(obj.has("lastModifiedDate"));
      assertFalse(obj.has("tenantName"));
    }

    final String tenantName = "limited";
    final NDSDataLakeTenant tenant =
        NDSDataLakeModelTestFactory.getDataLakeTenant(pNdsGroup, tenantName);
    _dataLakeTestUtils.saveTenant(tenant);
    final UsageLimit tenantLimit =
        LimitTestFactory.getTenantUsageLimit(groupId, tenant.getTenantId());
    _dataLakeTestUtils.setUsageLimit(tenantLimit);

    final String tenantName2 = "limited2";
    final NDSDataLakeTenant tenant2 =
        NDSDataLakeModelTestFactory.getDataLakeTenant(pNdsGroup, tenantName2).toBuilder()
            .state(NDSDataLakeState.DELETED)
            .build();
    _dataLakeTestUtils.saveTenant(tenant2);
    final UsageLimit tenantLimit2 =
        LimitTestFactory.getTenantUsageLimit(groupId, tenant.getTenantId());
    _dataLakeTestUtils.setUsageLimit(tenantLimit2);

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(2, resp.length());
      final List<JSONObject> limits =
          StreamSupport.stream(resp.spliterator(), false)
              .map(obj -> (JSONObject) obj)
              .sorted(Comparator.comparing((JSONObject obj) -> obj.getString("name")))
              .toList();

      JSONObject limit1 = limits.get(0);
      assertEquals("bytesProcessed.query", limit1.getString("name"));
      assertEquals(
          projectLimit.getPerQueryScanningLimit().getLimitBytes(), limit1.getLong("value"));
      assertEquals(0, limit1.getLong("currentUsage"));
      assertFalse(limit1.has("defaultLimit"));
      assertFalse(limit1.has("maximumLimit"));
      assertFalse(limit1.has("overrunPolicy"));
      assertTrue(limit1.has("lastModifiedDate"));
      assertEquals("limited", limit1.getString("tenantName"));

      JSONObject limit2 = limits.get(1);
      assertEquals("dataFederation.bytesProcessed.query", limit2.getString("name"));
      assertEquals(tenantLimit.getPerQueryScanningLimit().getLimitBytes(), limit2.getLong("value"));
      assertEquals(0, limit2.getLong("currentUsage"));
      assertFalse(limit2.has("defaultLimit"));
      assertFalse(limit2.has("maximumLimit"));
      assertFalse(limit2.has("overrunPolicy"));
      assertTrue(limit2.has("lastModifiedDate"));
      assertFalse(limit2.has("tenantName"));
    }
  }

  @Test
  public void testGetGroupLimits_GroupOwner() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);
    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    testGetGroupLimits(ndsGroup, groupOwner.getUsername(), groupOwnerApiKey.getKey());
  }

  @Test
  public void testGetGroupLimits_GroupDataAccessReadOnly() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());

    MmsFactory.createUserWithRoleInGroup(
        group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);

    final AppUser groupReadOnlyUser =
        MmsFactory.createUserWithRoleInGroup(
            group,
            "read-only" + getUniquifier() + "@example.com",
            Role.GROUP_DATA_ACCESS_READ_ONLY);
    final UserApiKey groupReadOnlyApiKey =
        MmsFactory.generateUserApiKey(groupReadOnlyUser.getId(), "Group Read-Only API Key");

    testGetGroupLimits(ndsGroup, groupReadOnlyUser.getUsername(), groupReadOnlyApiKey.getKey());
  }

  private void testGetGroupLimits(
      final NDSGroup pNdsGroup, final String pUsername, final String pApiKey) throws SvcException {
    final ObjectId groupId = pNdsGroup.getGroupId();
    final String limitsUrl = String.format("api/atlas/v1.0/groups/%s/limits", groupId);

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(Limits.USER_MANAGED_LIMITS_VALUES.size(), resp.length());
      final Set<String> limitNames = new HashSet<>();
      for (int i = 0; i < Limits.USER_MANAGED_LIMITS_VALUES.size(); i++) {
        limitNames.add(resp.getJSONObject(i).getString("name"));
      }
      assertEquals(
          Set.of(
              "atlas.project.security.databaseAccess.users",
              "atlas.project.deployment.clusters",
              "atlas.project.deployment.serverlessMTMs",
              "atlas.project.security.databaseAccess.customRoles",
              "atlas.project.security.networkAccess.entries",
              "atlas.project.security.networkAccess.crossRegionEntries",
              "atlas.project.deployment.nodesPerPrivateLinkRegion",
              "atlas.project.deployment.privateServiceConnectionsPerRegionGroup",
              "atlas.project.deployment.privateServiceConnectionsSubnetMask",
              "atlas.project.deployment.salesSoldM0s"),
          limitNames);
    }

    _dataLakeTestUtils.setUsageLimit(LimitTestFactory.getTenantUsageLimit());
    _dataLakeTestUtils.setUsageLimit(LimitTestFactory.getProjectUsageLimit());

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(Limits.USER_MANAGED_LIMITS_VALUES.size(), resp.length());
    }

    final String tenantName = "limited";
    final NDSDataLakeTenant tenant =
        NDSDataLakeModelTestFactory.getDataLakeTenant(pNdsGroup, tenantName);
    _dataLakeTestUtils.saveTenant(tenant);
    final UsageLimit tenantLimit =
        LimitTestFactory.getTenantUsageLimit(groupId, tenant.getTenantId());
    _dataLakeTestUtils.setUsageLimit(tenantLimit);

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(Limits.USER_MANAGED_LIMITS_VALUES.size(), resp.length());
    }

    final UsageLimit projectLimit = LimitTestFactory.getProjectUsageLimit(groupId);
    _dataLakeTestUtils.setUsageLimit(projectLimit);

    {
      final JSONArray resp = doDigestJsonArrayGet(limitsUrl, HttpStatus.SC_OK, pUsername, pApiKey);
      assertEquals(Limits.USER_MANAGED_LIMITS_VALUES.size() + 1, resp.length());
      final JSONObject obj = resp.getJSONObject(Limits.USER_MANAGED_LIMITS_VALUES.size());
      assertEquals("dataFederation.bytesProcessed.query", obj.getString("name"));
      assertEquals(projectLimit.getPerQueryScanningLimit().getLimitBytes(), obj.getLong("value"));
      assertEquals(0, obj.getLong("currentUsage"));
      assertFalse(obj.has("defaultLimit"));
      assertFalse(obj.has("maximumLimit"));
      assertFalse(obj.has("overrunPolicy"));
      assertTrue(obj.has("lastModifiedDate"));
      assertFalse(obj.has("tenantName"));
    }
  }

  @Test
  public void testGetOneDataFederationLimit_GroupOwner() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(group.getId());

    final AppUser groupOwner =
        MmsFactory.createUserWithRoleInGroup(
            group, "group-owner" + getUniquifier() + "@example.com", Role.GROUP_OWNER);
    final UserApiKey groupOwnerApiKey =
        MmsFactory.generateUserApiKey(groupOwner.getId(), "Group Owner API Key");

    testGetOneDataFederationLimit(ndsGroup, groupOwner.getUsername(), groupOwnerApiKey.getKey());
  }

  private void testGetOneDataFederationLimit(
      final NDSGroup pNdsGroup, final String pUsername, final String pApiKey) throws SvcException {
    final ObjectId groupId = pNdsGroup.getGroupId();
    final String limitsBaseUrl =
        String.format("api/atlas/v1.0/groups/%s/limits/dataFederation.bytesProcessed.", groupId);

    doDigestJsonGet(limitsBaseUrl + "monthly", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "weekly", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "daily", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "query", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);

    _dataLakeTestUtils.setUsageLimit(LimitTestFactory.getTenantUsageLimit());
    _dataLakeTestUtils.setUsageLimit(LimitTestFactory.getProjectUsageLimit());

    final String tenantName = "limited";
    final NDSDataLakeTenant tenant =
        NDSDataLakeModelTestFactory.getDataLakeTenant(pNdsGroup, tenantName);
    _dataLakeTestUtils.saveTenant(tenant);
    final UsageLimit tenantLimit =
        LimitTestFactory.getTenantUsageLimit(groupId, tenant.getTenantId());
    _dataLakeTestUtils.setUsageLimit(tenantLimit);

    doDigestJsonGet(limitsBaseUrl + "monthly", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "weekly", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "daily", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "query", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);

    final UsageLimit projectLimit = LimitTestFactory.getProjectUsageLimit(groupId);
    _dataLakeTestUtils.setUsageLimit(projectLimit);

    doDigestJsonGet(limitsBaseUrl + "monthly", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "weekly", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    doDigestJsonGet(limitsBaseUrl + "daily", HttpStatus.SC_NOT_FOUND, pUsername, pApiKey);
    JSONObject resp =
        doDigestJsonGet(limitsBaseUrl + "query", HttpStatus.SC_OK, pUsername, pApiKey);

    assertEquals("dataFederation.bytesProcessed.query", resp.getString("name"));
    assertEquals(projectLimit.getPerQueryScanningLimit().getLimitBytes(), resp.getLong("value"));
    assertEquals(0, resp.getLong("currentUsage"));
    assertFalse(resp.has("defaultLimit"));
    assertFalse(resp.has("maximumLimit"));
    assertFalse(resp.has("overrunPolicy"));
    assertTrue(resp.has("lastModifiedDate"));
    assertFalse(resp.has("tenantName"));
  }

  private static boolean hasUsers(final JSONArray pArray, final int... pUserIds)
      throws JSONException {
    // Check that the JSON array contains the specified users in the specified order.
    int i = 0;
    for (final int userId : pUserIds) {
      boolean found = false;
      for (; i < pArray.length(); i++) {
        final JSONObject item = pArray.getJSONObject(i);
        if (item.getString(ApiAppUserView.ID_FIELD).equals(oid(userId).toString())) {
          found = true;
          i = 0;
          break;
        }
      }
      if (!found) {
        return false;
      }
    }
    return true;
  }

  private static JSONObject findUserByUsername(final JSONArray pArray, final String username)
      throws JSONException {
    int i = 0;
    for (; i < pArray.length(); i++) {
      final JSONObject item = pArray.getJSONObject(i);
      if (item.getString(ApiAppUserView.USERNAME_FIELD).equals(username.toLowerCase())) {
        return item;
      }
    }
    return null;
  }

  private static boolean hasFlattenedRoles(final JSONObject pUser, final Set<Role> pRoles) {
    final JSONArray flattenedRoles = pUser.getJSONArray(ApiAppUserView.FLATTENED_ROLES_FIELD);
    final Set<Role> foundRoles = new HashSet<>();
    for (int i = 0; i < flattenedRoles.length(); i++) {
      final JSONObject roleAssignment = flattenedRoles.getJSONObject(i);
      foundRoles.add(
          Role.fromString(roleAssignment.getString(ApiRoleAssignmentView.ROLE_NAME_FIELD)));
    }
    return pRoles.equals(foundRoles);
  }

  private JSONObject getApiAtlasQueryLimitViewJSON(
      final long pLimitInBytes, final String pOverrunPolicy) {
    return new JSONObject()
        .put(FieldDefs.VALUE, pLimitInBytes)
        .put(ApiAtlasDataFederationQueryLimitView.FieldDefs.OVERRUN_POLICY, pOverrunPolicy);
  }
}
