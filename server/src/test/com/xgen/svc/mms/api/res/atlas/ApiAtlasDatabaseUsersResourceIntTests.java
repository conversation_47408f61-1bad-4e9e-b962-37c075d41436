package com.xgen.svc.mms.api.res.atlas;

import static com.google.common.net.UrlEscapers.urlPathSegmentEscaper;
import static com.xgen.svc.core.LinksAssertions.assertLinkEquals;
import static com.xgen.svc.mms.api.view.atlas.ApiAtlasDatabaseUserView.LABELS_FIELD;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jackson._public.ObjectMapperProvider;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.common.view._public.base.LinkRelView;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.AWSIAMType;
import com.xgen.cloud.nds.common._public.model.LDAPAuthType;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.NDSDBUserAction;
import com.xgen.cloud.nds.common._public.model.OIDCAuthType;
import com.xgen.cloud.nds.common._public.model.X509Type;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.usersecurity.NDSUserCertDao;
import com.xgen.cloud.nds.project._public.model.NDSCustomDBRole;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSUserScope;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserCert;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserToDNMapping;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView.NDSDBRoleView;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.cloud.user._public.svc.UserApiKeySvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasDatabaseUserView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasDatabaseUserView.ApiAtlasDatabaseUserViewBuilder;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasNDSLDAPView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasNDSLabelView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasRoleView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasUserCertView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasUserScopeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasUserSecurityView;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class ApiAtlasDatabaseUsersResourceIntTests extends ApiBaseResourceTest {

  private GroupDao _groupDao;
  private NDSUserCertDao _ndsUserCertDao;
  private NDSGroupDao _ndsGroupDao;
  private AppSettings _appSettings;
  private UserApiKeySvc _userApiKeySvc;

  private static final String CLUSTER_NAME = "cluster";
  private static final String DATA_LAKE_NAME = "dataLake";

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    _groupDao = AppConfig.getInstance(GroupDao.class);
    _ndsUserCertDao = AppConfig.getInstance(NDSUserCertDao.class);
    _ndsGroupDao = AppConfig.getInstance(NDSGroupDao.class);
    _appSettings = AppConfig.getInstance(AppSettings.class);
    _userApiKeySvc = AppConfig.getInstance(UserApiKeySvc.class);
    _appSettings.setProp("nds.gov.us.enabled", "false", AppSettings.SettingType.MEMORY);
  }

  @After
  public void tearDown() {
    _appSettings.setProp("nds.gov.us.enabled", "false", AppSettings.SettingType.MEMORY);
  }

  @Test
  public void testGetUsers() throws Exception {
    // Basic test to get all users
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(9, resp1.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results1 = resp1.getJSONArray(ApiListView.RESULTS_FIELD);

    final JSONObject user1 = results1.getJSONObject(0);
    assertEquals(oid(118).toString(), user1.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user1.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "admin", "admin"));
    assertEquals("NONE", user1.getString("ldapAuthType"));
    final JSONArray roles1 = user1.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles1);
    assertEquals(1, roles1.length());
    assertTrue(hasRole(roles1, "admin", "readWriteAnyDatabase"));
    final JSONArray scopes1 = user1.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
    assertNotNull(scopes1);
    assertEquals(0, scopes1.length());
    assertEquals("NONE", user1.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user1.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
    // Should not be exposed to customers
    assertFalse(user1.has("isEditable"));

    final JSONObject user2 = results1.getJSONObject(1);
    assertEquals(oid(118).toString(), user2.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user2.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "admin", "johndoe"));
    assertEquals("NONE", user2.getString("ldapAuthType"));
    final JSONArray roles2 = user2.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles2);
    assertEquals(1, roles2.length());
    assertTrue(hasRole(roles2, "db", "read"));
    assertEquals("NONE", user2.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user2.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    final JSONObject user3 = results1.getJSONObject(2);
    assertEquals(oid(118).toString(), user3.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user3.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "admin", "janedoe"));
    assertEquals("NONE", user3.getString("ldapAuthType"));
    final JSONArray roles3 = user3.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles3);
    assertEquals(1, roles3.length());
    assertTrue(hasRole(roles3, "db2", "read"));
    assertEquals("NONE", user3.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user3.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    final JSONObject user4 = results1.getJSONObject(3);
    assertEquals(oid(118).toString(), user4.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user4.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "$external", "CN=user,DC=com"));
    assertEquals("USER", user4.getString("ldapAuthType"));
    final JSONArray roles4 = user4.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles4);
    assertEquals(1, roles4.length());
    assertTrue(hasRole(roles4, "admin", "readWriteAnyDatabase"));
    assertEquals("NONE", user4.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user4.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    // LDAP group
    final JSONObject user5 = results1.getJSONObject(4);
    assertEquals(oid(118).toString(), user5.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user5.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "admin", "CN=group,DC=com"));
    assertEquals("GROUP", user5.getString("ldapAuthType"));
    final JSONArray roles5 = user5.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles5);
    assertEquals(1, roles5.length());
    assertTrue(hasRole(roles5, "admin", "readWriteAnyDatabase"));
    assertEquals("NONE", user5.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user5.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    // X509 managed
    final JSONObject user6 = results1.getJSONObject(5);
    assertEquals(oid(118).toString(), user6.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user6.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "$external", "myX509"));
    assertEquals("NONE", user6.getString("ldapAuthType"));
    final JSONArray roles6 = user6.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles6);
    assertEquals(1, roles6.length());
    assertTrue(hasRole(roles6, "admin", "readWriteAnyDatabase"));
    assertEquals("MANAGED", user6.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user6.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    // AWS IAM role
    final JSONObject user7 = results1.getJSONObject(6);
    assertEquals(oid(118).toString(), user7.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user7.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "$external", "arn:aws:iam::012345678910:role/exampleRole0"));
    assertEquals("NONE", user7.getString("ldapAuthType"));
    final JSONArray roles7 = user7.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles6);
    assertEquals(1, roles7.length());
    assertTrue(hasRole(roles6, "admin", "readWriteAnyDatabase"));
    assertEquals("NONE", user7.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("ROLE", user7.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    // OIDC Auth Type
    final JSONObject user8 = results1.getJSONObject(7);
    assertEquals(oid(118).toString(), user8.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user8.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "admin", "123/10gen"));
    assertEquals("NONE", user8.getString("ldapAuthType"));
    assertEquals("IDP_GROUP", user8.getString("oidcAuthType"));
    final JSONArray roles8 = user8.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles8);
    assertEquals(1, roles8.length());
    assertTrue(hasRole(roles8, "db", "atlasAdmin"));
    assertEquals("NONE", user8.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user8.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    final JSONObject user9 = results1.getJSONObject(8);
    assertEquals(oid(118).toString(), user9.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user9.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results1, "admin", "123/everyone"));
    assertEquals("NONE", user9.getString("ldapAuthType"));
    assertEquals("IDP_GROUP", user9.getString("oidcAuthType"));
    final JSONArray roles9 = user9.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles9);
    assertEquals(1, roles9.length());
    assertTrue(hasRole(roles9, "db", "read"));
    assertEquals("NONE", user9.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user9.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));

    // Test with group that doesn't exist
    final JSONObject resp2 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(18) + "/databaseUsers",
            HttpStatus.SC_NOT_FOUND,
            JOHN_DOE_USERNAME,
            JOHN_DOE_API_KEY);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals("GROUP_NOT_FOUND", resp2.getString(ApiError.ERROR_CODE_FIELD));

    // Test with group that has scoped users
    final JSONObject resp3 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(184) + "/databaseUsers",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(1, resp3.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results3 = resp3.getJSONArray(ApiListView.RESULTS_FIELD);

    final JSONObject user0 = results3.getJSONObject(0);
    assertEquals(oid(184).toString(), user0.getString(ApiAtlasDatabaseUserView.GROUP_ID_FIELD));
    assertFalse(user0.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(hasUser(results3, "admin", "johndoe"));
    assertEquals("NONE", user0.getString("ldapAuthType"));
    final JSONArray roles0 = user0.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
    assertNotNull(roles0);
    assertEquals(1, roles0.length());
    final JSONArray scopes8 = user0.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
    assertNotNull(scopes8);
    assertEquals(2, scopes8.length());
    assertTrue(hasScope(scopes8, CLUSTER_NAME, "CLUSTER"));
    assertTrue(hasScope(scopes8, DATA_LAKE_NAME, "DATA_LAKE"));
    assertEquals("NONE", user0.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
    assertEquals("NONE", user0.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
  }

  @Test
  public void testGetUsersByDatabase() throws Exception {
    // Test to get all users part of admin database
    final String dbReq1 = "admin";
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + dbReq1,
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(6, resp1.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results1 = resp1.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(
        "admin", (results1.getJSONObject(0)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "johndoe", (results1.getJSONObject(1)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "janedoe", (results1.getJSONObject(2)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "CN=group,DC=com",
        (results1.getJSONObject(3)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "123/10gen",
        (results1.getJSONObject(4)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "123/everyone",
        (results1.getJSONObject(5)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));

    // Test to get scoped user part of admin database, group 184
    final JSONObject resp2 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(184) + "/databaseUsers/" + dbReq1,
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(1, resp2.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results2 = resp2.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(
        "johndoe", (results2.getJSONObject(0)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));

    // Test to get all users part of $external database
    final String dbReq2 = "$external";
    final JSONObject resp3 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + dbReq2,
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(3, resp3.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray results3 = resp3.getJSONArray(ApiListView.RESULTS_FIELD);
    assertEquals(
        "CN=user,DC=com",
        (results3.getJSONObject(0)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "myX509", (results3.getJSONObject(1)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(
        "arn:aws:iam::012345678910:role/exampleRole0",
        (results3.getJSONObject(2)).getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));

    // Test to get users part of a database that does not exist
    final String dbReq3 = "deebee";
    final JSONObject resp4 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + dbReq3,
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(0, resp4.getInt(ApiListView.TOTAL_COUNT_FIELD));
  }

  @Test
  public void testGetUserByName() {
    // Basic test
    {
      final String req = "admin/johndoe";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("johndoe", resp.getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", resp.getString(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertLinkEquals(
          resp, LinkRelView.SELF, "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req);
    }

    // Test where the user with the given username and database doesn't exist
    {
      final String req = "deebee/joshdoe"; // Non-existent database and non-existent user
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test database exists, but user in this database doesn't
    {
      final String req = "admin/joshdoe";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test user exists, but in a different database
    {
      final String req = "deebee/johndoe";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test getting a user from a group that doesn't exist
    {
      final String req = "admin/johndoe";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(18) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("GROUP_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test LDAP users, X509 users, AWS IAM users exist in the correct database

    // LDAP user, $external database
    {
      final String req = "$external/CN=user,DC=com";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("CN=user,DC=com", resp.getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("$external", resp.getString(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertLinkEquals(
          resp, LinkRelView.SELF, "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req);
    }

    // LDAP user, admin database
    {
      final String req = "admin/CN=user,DC=com";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // LDAP group, admin database
    {
      final String req = "admin/CN=group,DC=com";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("CN=group,DC=com", resp.getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", resp.getString(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertLinkEquals(
          resp, LinkRelView.SELF, "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req);
    }

    // LDAP group, $external database
    {
      final String req = "$external/CN=group,DC=com";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // X509 user, $external database
    {
      final String req = "$external/myX509";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("myX509", resp.getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("$external", resp.getString(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertLinkEquals(
          resp, LinkRelView.SELF, "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req);
    }

    // X509 user, admin database
    {
      final String req = "admin/CN=myX509,DC=com";
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // AWS IAM user, $external database
    {
      final String username = "arn:aws:iam::012345678910:role/exampleRole0";
      final String req = "$external/" + urlPathSegmentEscaper().escape(username);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(
          "arn:aws:iam::012345678910:role/exampleRole0",
          resp.getString(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("$external", resp.getString(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertLinkEquals(
          resp, LinkRelView.SELF, "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req);
    }

    // AWS IAM user, admin database
    {
      final String username = "arn:aws:iam::012345678910:role/exampleRole0";
      final String req = "admin/" + urlPathSegmentEscaper().escape(username);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/" + req,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testGetValidUserCerts() throws Exception {
    // constants for cert validity window
    final Date yesterday = new Date(System.currentTimeMillis() - Duration.ofDays(1).toMillis());
    final Date tomorrow = new Date(System.currentTimeMillis() + Duration.ofDays(1).toMillis());

    {
      // User exists, but has no certs
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3Djohndoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(0, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
    }

    final NDSUserCert dummyCert =
        new NDSUserCert(
            1,
            oid(118),
            String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "johndoe"),
            yesterday,
            tomorrow,
            null,
            1);
    _ndsUserCertDao.insertReplicaSafe(dummyCert);

    {
      // User exists and has a valid cert
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3Djohndoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(1, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
      final JSONArray results = resp.getJSONArray(ApiListView.RESULTS_FIELD);
      assertEquals(
          String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "johndoe"),
          (results.getJSONObject(0)).getString(ApiAtlasUserCertView.SUBJECT_FIELD));
    }

    {
      // User exists and has a valid cert and queried without X500 format
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/johndoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(1, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
      final JSONArray results = resp.getJSONArray(ApiListView.RESULTS_FIELD);
      assertEquals(
          String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "johndoe"),
          (results.getJSONObject(0)).getString(ApiAtlasUserCertView.SUBJECT_FIELD));
    }

    {
      // User exists, but has a revoked cert that would otherwise be valid
      _ndsUserCertDao.revokeCert(dummyCert.getGroupId(), dummyCert.getId());
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3D=johndoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(0, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
    }

    {
      // User exists, but has an old cert
      final NDSUserCert dummyExpiredCert =
          new NDSUserCert(
              0,
              oid(118),
              String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "johndoe"),
              yesterday,
              yesterday,
              null,
              0);
      _ndsUserCertDao.insertReplicaSafe(dummyExpiredCert);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN=%3Djohndoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(0, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
    }

    {
      // User doesn't exist and has no certs
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3Djoshdoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(0, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
    }

    {
      // User doesn't exist (perhaps deleted) but still has certs
      final NDSUserCert dummyCertForDeletedUser =
          new NDSUserCert(
              2,
              oid(118),
              String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "joshdoe"),
              new Date(System.currentTimeMillis() - Duration.ofDays(1).toMillis()),
              new Date(System.currentTimeMillis() + Duration.ofDays(1).toMillis()),
              null,
              2);
      _ndsUserCertDao.insertReplicaSafe(dummyCertForDeletedUser);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3Djoshdoe/certs",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(1, resp.getInt(ApiListView.TOTAL_COUNT_FIELD));
      final JSONArray respInner = resp.getJSONArray(ApiListView.RESULTS_FIELD);
      assertEquals(
          String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "joshdoe"),
          (respInner.getJSONObject(0)).getString(ApiAtlasUserCertView.SUBJECT_FIELD));
    }
  }

  @Test
  public void testGenerateCert() {
    // No expiration specified - default to 3 months
    doDigestPostJsonStr(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3DmyX509/certs",
        new JSONObject(),
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Valid expiration specified
    final JSONObject validExpirationParam = new JSONObject();
    validExpirationParam.put(ApiAtlasUserCertView.MONTHS_UNTIL_EXPIRATION_FIELD, 16);
    doDigestPostJsonStr(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3DmyX509/certs",
        validExpirationParam,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    final JSONObject certsForUser =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3DmyX509/certs",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(2, certsForUser.getInt(ApiListView.TOTAL_COUNT_FIELD));
    final JSONArray certsResult = certsForUser.getJSONArray(ApiListView.RESULTS_FIELD);
    final JSONObject firstCert = (certsResult.getJSONObject(0));
    final JSONObject secondCert = (certsResult.getJSONObject(1));
    assertEquals(
        String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "myX509"),
        firstCert.getString(ApiAtlasUserCertView.SUBJECT_FIELD));
    assertFalse(firstCert.has(ApiAtlasUserCertView.REVOKED_AT_FIELD));

    assertEquals(
        String.format(NDSDefaults.X509_USER_SUBJECT_TEMPLATE, "myX509"),
        secondCert.getString(ApiAtlasUserCertView.SUBJECT_FIELD));
    assertFalse(secondCert.has(ApiAtlasUserCertView.REVOKED_AT_FIELD));

    // Expiration can't be more than 24 months
    final JSONObject invalidExpirationParam = new JSONObject();
    invalidExpirationParam.put(ApiAtlasUserCertView.MONTHS_UNTIL_EXPIRATION_FIELD, 26);
    final JSONObject invalidExpiraionParamResp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3DmyX509/certs",
            invalidExpirationParam,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, invalidExpiraionParamResp.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CERT_EXPIRY_CANNOT_EXCEED_TWO_YEARS",
        invalidExpiraionParamResp.getString(ApiError.ERROR_CODE_FIELD));

    // Can't generate cert if advanced mode X.509 is enabled (group 131)
    final JSONObject customerX509Param = new JSONObject();
    customerX509Param.put(ApiAtlasUserCertView.MONTHS_UNTIL_EXPIRATION_FIELD, 15);
    final JSONObject customerX509ParamResp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers/CN%3DmyX509/certs",
            customerX509Param,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, customerX509ParamResp.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_GENERATE_CERT_IF_ADVANCED_X509",
        customerX509ParamResp.getString(ApiError.ERROR_CODE_FIELD));

    // Can't generate cert for non-X.509 user
    final JSONObject nonX509Param = new JSONObject();
    nonX509Param.put(ApiAtlasUserCertView.MONTHS_UNTIL_EXPIRATION_FIELD, 15);
    final JSONObject nonX509ParamResp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3Duser,DC%3Dcom/certs",
            nonX509Param,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, nonX509ParamResp.getInt(ApiError.ERROR_FIELD));
    assertEquals("USER_NOT_MANAGED_X509", nonX509ParamResp.getString(ApiError.ERROR_CODE_FIELD));

    // Can't generate cert if limit has been reached for user in group
    final JSONObject limitReachedResp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/CN%3DmyX509/certs",
            new JSONObject(),
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, limitReachedResp.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.ATLAS_GENERATED_CERTS_LIMIT_EXCEEDED.name(),
        limitReachedResp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testAddUserWithInvalidPassword() {
    final byte[] passwordBytes = {1, 'a', 'b'};
    final JSONObject user =
        createNewUserJson("admin", "nickdoe", new String(passwordBytes, StandardCharsets.UTF_8));
    final JSONObject resp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
    assertEquals("INVALID_PASSWORD", resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testAddDeleteUser_admin() {
    testAddDeleteUser(ADMIN_USERNAME, ADMIN_API_KEY);
  }

  @Test
  public void testCreateAnyDbUser() {
    final String username = "anyDbUsername";
    final String dbName = "admin";
    final String roleDbName = "";
    final JSONObject user =
        createNewUserJson(dbName, username, "testUserPassword", LDAPAuthType.NONE, roleDbName);
    final JSONObject resp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user,
            HttpStatus.SC_CREATED,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(username, resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
    assertEquals(dbName, resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
    assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
    assertTrue(
        hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), roleDbName, "read"));
  }

  @Test
  public void testAddDeleteUser_streamProcessingOwner() {
    testAddDeleteUser(STREAMS_USER_NAME, STREAMS_API_KEY);
  }

  @Test
  public void testAddDeleteUser_databaseAccessAdmin() throws SvcException {
    final AppUser user =
        MmsFactory.createUserWithRoleInGroup(
            _groupDao.findById(oid(118)),
            String.format("<EMAIL>", getUniquifier()),
            Role.GROUP_DATABASE_ACCESS_ADMIN);

    final UserApiKey key = _userApiKeySvc.add(user.getId(), "test");
    testAddDeleteUser_SCRAM(user.getUsername(), key.getKey());
  }

  public void testAddDeleteUser(final String pUsername, final String pApiKey) {
    // ------
    // SCRAM
    // ------

    testAddDeleteUser_SCRAM(pUsername, pApiKey);

    // ----
    // LDAP
    // ----

    // Test adding an LDAP USER user
    {
      final JSONObject user =
          createNewUserJson("$external", "CN=user1,DC=org", null, LDAPAuthType.USER, "admin");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("CN=user1,DC=org", resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      assertEquals("USER", resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
    }

    // Test adding a user with conflicting auth mechanisms (LDAP USER and SCRAM)
    {
      final JSONObject user =
          createNewUserJson("$external", "CN=bad,DC=org", null, LDAPAuthType.USER, "admin")
              .put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "ALGYr8UiYGPdqJ9");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, LDAP).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding a LDAP USER user with distinguished name subject to LDAP injection
    {
      final JSONObject user =
          createNewUserJson("$external", "CN=\";user#\",DC=org", null, LDAPAuthType.USER, "admin");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_LDAP_NAME", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding an LDAP USER user with an invalid distinguished name
    {
      final JSONObject user =
          createNewUserJson("$external", "undignified", null, LDAPAuthType.USER, "admin");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_LDAP_NAME", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    if (pUsername.equals(ADMIN_USERNAME)) {
      // Test adding an LDAP USER user with userToDNMapping in place
      {
        final JSONObject userSecurity =
            new JSONObject()
                .put(
                    ApiAtlasUserSecurityView.LDAP_FIELD,
                    new JSONObject()
                        .put(
                            ApiAtlasNDSLDAPView.USER_TO_DN_MAPPING_FIELD,
                            new JSONArray()
                                .put(
                                    new JSONObject()
                                        .put(NDSUserToDNMapping.MATCH_FIELD, "(.*)")
                                        .put(
                                            NDSUserToDNMapping.LDAP_QUERY_FIELD,
                                            "ou=engineering,dc=example,dc=com??one?(user={0})"))));
        doDigestJsonPatch(
            String.format("api/atlas/v1.0/groups/%s/userSecurity", oid(141)),
            userSecurity,
            HttpStatus.SC_ACCEPTED,
            pUsername,
            pApiKey);

        final JSONObject user =
            createNewUserJson("$external", "whosdignifiednow", null, LDAPAuthType.USER, "admin");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(141) + "/databaseUsers",
                user,
                HttpStatus.SC_CREATED,
                pUsername,
                pApiKey);
        assertEquals("whosdignifiednow", resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
        assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
        assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
        assertEquals("USER", resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      }

      // Test adding an LDAP USER user of type USER to the admin database
      {
        final JSONObject user =
            createNewUserJson("admin", "CN=bad,DC=org", null, LDAPAuthType.USER, "admin");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                user,
                HttpStatus.SC_BAD_REQUEST,
                pUsername,
                pApiKey);
        assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
        assertEquals("DATABASE_NAME_INVALID_EXTERNAL", resp.getString(ApiError.ERROR_CODE_FIELD));
      }

      // Test adding an LDAP GROUP user
      {
        final JSONObject user =
            createNewUserJson("admin", "CN=group1,DC=org", null, LDAPAuthType.GROUP, "admin");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                user,
                HttpStatus.SC_CREATED,
                pUsername,
                pApiKey);
        assertEquals("CN=group1,DC=org", resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
        assertEquals("admin", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
        assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
        assertEquals("GROUP", resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
        assertTrue(
            hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
      }

      // Test adding a user with conflicting auth mechanisms (LDAP GROUP and SCRAM)
      {
        final JSONObject user =
            createNewUserJson(
                    "admin", "CN=badGroup,DC=org", "ALGYr8UiYGPdqJ9", LDAPAuthType.GROUP, "admin")
                .put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "ALGYr8UiYGPdqJ9");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                user,
                HttpStatus.SC_BAD_REQUEST,
                pUsername,
                pApiKey);
        assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
        assertEquals(
            ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
            resp.getString(ApiError.ERROR_CODE_FIELD));
        assertEquals(
            "Invalid authentication settings. Users cannot be configured for multiple"
                + " authentication mechanisms (SCRAM, LDAP).",
            resp.getString(ApiError.DETAIL_FIELD));
      }

      // Test adding an LDAP GROUP user with an invalid distinguished name
      {
        final JSONObject user =
            createNewUserJson("admin", "undignified", null, LDAPAuthType.GROUP, "admin");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                user,
                HttpStatus.SC_BAD_REQUEST,
                pUsername,
                pApiKey);
        assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
        assertEquals("ATLAS_INVALID_LDAP_NAME", resp.getString(ApiError.ERROR_CODE_FIELD));
      }

      // Test adding an LDAP GROUP user to the $external database
      {
        final JSONObject user =
            createNewUserJson("$external", "CN=badGroup,DC=org", null, LDAPAuthType.GROUP, "admin");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                user,
                HttpStatus.SC_BAD_REQUEST,
                pUsername,
                pApiKey);
        assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
        assertEquals("DATABASE_NAME_INVALID_ADMIN", resp.getString(ApiError.ERROR_CODE_FIELD));
      }

      // Test deleting LDAP USER user
      {
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com",
            HttpStatus.SC_NO_CONTENT,
            pUsername,
            pApiKey);
        final JSONObject resp =
            doDigestJsonGet(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                HttpStatus.SC_OK,
                pUsername,
                pApiKey);
        assertFalse(
            hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "$external", "CN=user,DC=com"));
      }

      // Test delete LDAP GROUP user
      {
        doDigestJsonDelete(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com",
            HttpStatus.SC_NO_CONTENT,
            pUsername,
            pApiKey);
        final JSONObject resp =
            doDigestJsonGet(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                HttpStatus.SC_OK,
                pUsername,
                pApiKey);
        assertFalse(
            hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "admin", "CN=group,DC=com"));
      }
    }
    // ----
    // X509
    // ----

    // NOTE: using group 118 for managedX509 tests. Group 131 has customerX509 enabled.

    // Test adding a X509 MANAGED user
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "certificateLover",
              null,
              LDAPAuthType.NONE.name(),
              null,
              X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertTrue(resp.has(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals("certificateLover", resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      assertEquals("NONE", resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
    }

    // Test adding a X509 MANAGED user with too many OIDs
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "CN=user,O=extra",
              null,
              LDAPAuthType.NONE.name(),
              null,
              X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_MANAGED_X509_NAME_INVALID", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a X509 MANAGED user with an invalid username
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "certs,CAs,CRLsOhMy!",
              null,
              LDAPAuthType.NONE.name(),
              null,
              X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_X509_NAME", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a X509 CUSTOMER user with an invalid DN
    {
      final JSONObject user =
          createNewX509User(
              "$external", "undignified", null, LDAPAuthType.NONE.name(), null, X509Type.CUSTOMER);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_X509_NAME", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a X509 CUSTOMER user with a DN that contains metacharacters
    {
      final JSONObject user =
          createNewX509User(
              "$external", "CN=user#15", null, LDAPAuthType.NONE.name(), null, X509Type.CUSTOMER);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_DN_METACHARACTERS", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a valid X509 CUSTOMER user
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "CN=user,O=Atlas",
              null,
              LDAPAuthType.NONE.name(),
              null,
              X509Type.CUSTOMER);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertTrue(resp.has(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals("CN=user,O=Atlas", resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      assertEquals("NONE", resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
    }

    // Test adding a temporary X509 MANAGED user
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "CN=temporaryCert",
              null,
              LDAPAuthType.NONE.name(),
              new Date(),
              X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "ATLAS_X509_USER_CANNOT_BE_TEMPORARY", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a user with X509 and SCRAM
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "CN=x509SCRAM",
              "soMuchSecurity",
              LDAPAuthType.NONE.name(),
              null,
              X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, X509).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding a user with X509 and LDAP
    {
      final JSONObject user =
          createNewX509User(
              "$external",
              "CN=x509AndLDAP",
              null,
              LDAPAuthType.USER.name(),
              null,
              X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (LDAP, X509).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding an X509 MANAGED user to the admin database
    {
      final JSONObject user =
          createNewX509User(
              "admin", "CN=x509Admin", null, LDAPAuthType.NONE.name(), null, X509Type.MANAGED);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_INVALID_EXTERNAL", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test deleting X509 MANAGED user
    {
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/certificateLover",
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertFalse(
          hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "$external", "certificateLover"));
    }

    // Test deleting X509 CUSTOMER user
    {
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers/$external/CN=user,O=Atlas",
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertFalse(
          hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "$external", "CN=user,O=Atlas"));
    }

    // -------
    // AWS IAM
    // -------

    final String awsIAMUserFmtStr = "arn:aws:iam::012345678910:user/%s";
    final String awsIAMRoleFmtStr = "arn:aws:iam::012345678910:role/%s";

    // Test adding an AWS IAM USER user
    {
      final String username = String.format(awsIAMUserFmtStr, "i-am-user");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.USER, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertEquals(username, resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals(
          LDAPAuthType.NONE.name(), resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertEquals(X509Type.NONE.name(), resp.get(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals(AWSIAMType.USER.name(), resp.get(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
    }

    // Test adding an AWS IAM ROLE user
    {
      final String username = String.format(awsIAMRoleFmtStr, "i-am-role");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.ROLE, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertEquals(username, resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals(
          LDAPAuthType.NONE.name(), resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertEquals(X509Type.NONE.name(), resp.get(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals(AWSIAMType.ROLE.name(), resp.get(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
    }

    // Test adding an AWS IAM USER with an invalid name
    {
      final String username = "arn-t-you-sad-i-am-invalid";
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.USER, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AWS_IAM_ARN.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding an AWS IAM ROLE with an invalid name
    {
      final String username = "absent-for-role-call";
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.ROLE, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AWS_IAM_ARN.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding an AWS IAM USER with a role name
    {
      final String username = String.format(awsIAMRoleFmtStr, "i-should-be-a-user");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.USER, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AWS_IAM_ARN.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding an AWS IAM ROLE with a user name
    {
      final String username = String.format(awsIAMUserFmtStr, "i-should-be-a-role");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.ROLE, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AWS_IAM_ARN.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding an AWS IAM USER using admin auth database
    {
      final String username = String.format(awsIAMUserFmtStr, "i-am-the-administrative-state");
      final JSONObject user =
          createNewAWSIAMUser(
              "admin", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.USER, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.DATABASE_NAME_INVALID_EXTERNAL.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding an AWS IAM ROLE using admin auth database
    {
      final String username = String.format(awsIAMRoleFmtStr, "we-arn-t-the-administrative-state");
      final JSONObject user =
          createNewAWSIAMUser(
              "admin", username, null, LDAPAuthType.NONE, X509Type.NONE, AWSIAMType.ROLE, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.DATABASE_NAME_INVALID_EXTERNAL.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a temporary AWS IAM USER user
    {
      final String username = String.format(awsIAMUserFmtStr, "temp-or-are-we");
      final Date deleteAfter = Date.from(Instant.now().plus(Duration.ofDays(1)));
      final JSONObject user =
          createNewAWSIAMUser(
              "$external",
              username,
              null,
              LDAPAuthType.NONE,
              X509Type.NONE,
              AWSIAMType.USER,
              deleteAfter);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertEquals(username, resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals(
          LDAPAuthType.NONE.name(), resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertEquals(X509Type.NONE.name(), resp.get(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals(AWSIAMType.USER.name(), resp.get(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
      assertTrue(resp.has(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD));
      assertEquals(
          TimeUtils.toISOString(deleteAfter),
          resp.getString(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD));
    }

    // Test adding a temporary AWS IAM ROLE user
    {
      final String username = String.format(awsIAMRoleFmtStr, "temp-or-are-ye");
      final Date deleteAfter = Date.from(Instant.now().plus(Duration.ofDays(1)));
      final JSONObject user =
          createNewAWSIAMUser(
              "$external",
              username,
              null,
              LDAPAuthType.NONE,
              X509Type.NONE,
              AWSIAMType.ROLE,
              deleteAfter);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("$external", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertEquals(username, resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals(
          LDAPAuthType.NONE.name(), resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertEquals(X509Type.NONE.name(), resp.get(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals(AWSIAMType.ROLE.name(), resp.get(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertTrue(hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "admin", "read"));
      assertTrue(resp.has(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD));
      assertEquals(
          TimeUtils.toISOString(deleteAfter),
          resp.getString(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD));
    }

    // Test adding a user with SCRAM and AWS IAM
    {
      final String username = String.format(awsIAMUserFmtStr, "scram-bled-auth-types");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external",
              username,
              "forget-about-it",
              LDAPAuthType.NONE,
              X509Type.NONE,
              AWSIAMType.USER,
              null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, MONGODB_AWS).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding a user with LDAP and AWS IAM
    {
      final String username = String.format(awsIAMUserFmtStr, "ldap-per-dan");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external", username, null, LDAPAuthType.USER, X509Type.NONE, AWSIAMType.USER, null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (LDAP, MONGODB_AWS).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding a user with X509 and AWS IAM
    {
      final String username = String.format(awsIAMUserFmtStr, "formula-x509-all-purpose");
      final JSONObject user =
          createNewAWSIAMUser(
              "$external",
              username,
              null,
              LDAPAuthType.NONE,
              X509Type.MANAGED,
              AWSIAMType.USER,
              null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      // TODO: this should be ATLAS_INVALID_AUTH_SETTINGS (CLOUDP-61416)
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_DN_METACHARACTERS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));

      // TODO: this should be "Invalid authentication settings. Users cannot be configured for
      //       multiple authentication mechanisms (X509, MONGODB_AWS)." (CLOUDP-61416)
      assertEquals(
          String.format(
              "The distinguished name specified in the username field, %s, contains illegal"
                  + " metacharacters.",
              username),
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test deleting an AWS IAM USER user
    {
      final String username = String.format(awsIAMUserFmtStr, "i-am-user");
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/$external/"
              + urlPathSegmentEscaper().escape(username),
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertFalse(hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "$external", username));
    }

    // Test deleting an AWS IAM ROLE userz
    {
      final String username = String.format(awsIAMRoleFmtStr, "i-am-role");
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/$external/"
              + urlPathSegmentEscaper().escape(username),
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertFalse(hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "$external", username));
    }

    // ----
    // OIDC
    // ----

    // Test adding an OIDC group
    {
      final JSONObject group = createNewOIDCGroup("admin", "curly/howard", null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              group,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("curly/howard", resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", resp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(resp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      assertEquals("NONE", resp.get(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD));
      assertEquals("NONE", resp.get(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertEquals("NONE", resp.get(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertEquals("IDP_GROUP", resp.get(ApiAtlasDatabaseUserView.OIDC_AUTH_TYPE_FIELD));

      final JSONObject getResp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(hasUser(getResp.getJSONArray(ApiListView.RESULTS_FIELD), "admin", "curly/howard"));
    }

    // Test adding an OIDC group with invalid username
    {
      final JSONObject group = createNewOIDCGroup("admin", "shemp", null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              group,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_FEDERATED_AUTH_USERNAME.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "The username shemp is invalid for Federated Authentication users. These usernames must"
              + " take the form of an identity provider name, followed by a slash, followed by an"
              + " identity provider group name.",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding a user with conflicting auth mechanisms (OIDC group and SCRAM)
    {
      final JSONObject conflictingUserGroup =
          createNewOIDCGroup("admin", "moe/howard", "supersecretpassword");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              conflictingUserGroup,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, OIDC).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding a group with conflicting auth mechanisms (OIDC and LDAP)
    {
      final JSONObject group = createNewOIDCGroup("admin", "joe/besser", null);
      group.put(NDSDBUserView.FieldDefs.LDAP_AUTH_TYPE, "GROUP");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              group,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (LDAP, OIDC).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test adding an OIDC group user to the wrong database
    {
      final JSONObject user = createNewOIDCGroup("$external", "larry/fine", null);
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_INVALID_ADMIN", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test deleting OIDC group with OIDC setting enabled
    {
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/curly%2Fhoward",
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertFalse(hasUser(resp.getJSONArray(ApiListView.RESULTS_FIELD), "admin", "curly/howard"));
    }

    // -------------------
    // database user limit
    // -------------------

    // Test adding SCRAM users up to the group users limit
    {
      final NDSGroup group = _ndsGroupDao.find(oid(118)).get();
      for (int i = group.getDatabaseUsers().size(); i < Limits.Defaults.MONGODB_USERS; i++) {
        final JSONObject user = createNewUserJson("admin", "newuser" + i, "T0pSecr8!");
        final JSONObject resp =
            doDigestJsonPost(
                "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
                user,
                HttpStatus.SC_CREATED,
                pUsername,
                pApiKey);
        assertEquals("newuser" + i, resp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      }
    }

    // Test adding a SCRAM user once the group users limit has been reached
    {
      final JSONObject user = createNewUserJson("admin", "wontwork", "T0pSecr8!");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_FORBIDDEN,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_FORBIDDEN, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("GROUP_USERS_LIMIT_EXCEEDED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  private void testAddDeleteUser_SCRAM(final String pUsername, final String pApiKey) {
    // Test adding a SCRAM user with database level permissions
    {
      final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!");
      final JSONObject resp1 =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("nickdoe", resp1.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", resp1.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(resp1.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      final JSONArray roles = resp1.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
      assertTrue(hasRole(roles, "admin", "read"));
    }

    // Test adding a SCRAM user with collection level permissions
    {
      final JSONObject collectionUser =
          createUserWithCollectionPermissions("admin", "piauser", "T0pSecr8!");
      final JSONObject collectionResp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              collectionUser,
              HttpStatus.SC_CREATED,
              pUsername,
              pApiKey);
      assertEquals("piauser", collectionResp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", collectionResp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(collectionResp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      final JSONArray collectionRoles =
          collectionResp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
      assertTrue(hasRole(collectionRoles, "admin", "collection", "read"));
    }

    // Test adding a non-editable SCRAM user
    {
      final JSONObject user = createNewUserJson("admin", "nonedit", "T0pSecr8!");
      user.put("isEditable", false);
      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
          user,
          HttpStatus.SC_CREATED,
          pUsername,
          pApiKey);
      // Should not be able to delete them
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nonedit",
          HttpStatus.SC_FORBIDDEN,
          pUsername,
          pApiKey);
      // But can delete them with override
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/admin/nonedit?overrideEditable=true",
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
    }

    // Test adding a SCRAM user with a missing database name field
    {
      final JSONObject user =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "jakedoe");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("MISSING_ATTRIBUTE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user with an empty database name field
    {
      final JSONObject user =
          new JSONObject()
              .put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "jakedoe")
              .put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user to a group that doesn't exist
    {
      final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(18) + "/databaseUsers",
              user,
              HttpStatus.SC_NOT_FOUND,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("GROUP_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user without appropriate access
    {
      final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_UNAUTHORIZED,
              JOHN_DOE_USERNAME,
              JOHN_DOE_API_KEY);
      assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USER_CANNOT_ACCESS_GROUP", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user with duplicate roles
    {
      final JSONObject user =
          createNewUserJson("admin", "dupeAdmin", "D0ubl3T@k3:)")
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray(
                      Arrays.asList(
                          createRoleJson("admin", "read"), createRoleJson("admin", "read"))));
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          NDSErrorCode.DUPLICATE_DATABASE_ROLES.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
      assertTrue(resp.getString(ApiError.DETAIL_FIELD).contains("read"));
    }

    // Test adding a SCRAM user with a role that has an invalid collection name
    {
      final JSONObject user =
          createNewUserJson("admin", "user1", "D0ubl3T@k3:)")
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray(
                      Collections.singletonList(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "$someCollection"))));
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.INVALID_COLLECTION_NAME.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user with a common password
    {
      final JSONObject user = createNewUserJson("admin", "ezpass", "password");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("COMMON_PASSWORD", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user on a non-admin database
    {
      final JSONObject user = createNewUserJson("mydb", "ezpass", "T0pSecr8!");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_INVALID_ADMIN", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test deleting SCRAM user
    {
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
          HttpStatus.SC_NO_CONTENT,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      final JSONArray delResults = resp.getJSONArray(ApiListView.RESULTS_FIELD);
      assertFalse(hasUser(delResults, "admin", "nickdoe"));
    }
  }

  @Test
  public void testAddUserSCRAMInGovEnvironment() {
    _appSettings.setProp("nds.gov.us.enabled", "true", AppSettings.SettingType.MEMORY);
    // Test adding a SCRAM user with database level permissions
    {
      final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!ExtraLong");
      final JSONObject resp1 =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_CREATED,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("nickdoe", resp1.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", resp1.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(resp1.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      final JSONArray roles = resp1.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
      assertTrue(hasRole(roles, "admin", "read"));
    }

    // Test adding a SCRAM user with collection level permissions
    {
      final JSONObject collectionUser =
          createUserWithCollectionPermissions("admin", "piauser", "T0pSecr8!ExtraLong");
      final JSONObject collectionResp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              collectionUser,
              HttpStatus.SC_CREATED,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("piauser", collectionResp.get(ApiAtlasDatabaseUserView.USERNAME_FIELD));
      assertEquals("admin", collectionResp.get(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD));
      assertFalse(collectionResp.has(ApiAtlasDatabaseUserView.PASSWORD_FIELD));
      final JSONArray collectionRoles =
          collectionResp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
      assertTrue(hasRole(collectionRoles, "admin", "collection", "read"));
    }

    // Test adding a non-editable SCRAM user
    {
      final JSONObject user = createNewUserJson("admin", "nonedit", "T0pSecr8!ExtraLong");
      user.put("isEditable", false);
      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
          user,
          HttpStatus.SC_CREATED,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
      // Should not be able to delete them
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nonedit",
          HttpStatus.SC_FORBIDDEN,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
      // But can delete them with override
      doDigestJsonDelete(
          "api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/admin/nonedit?overrideEditable=true",
          HttpStatus.SC_NO_CONTENT,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
    }

    // Test adding a SCRAM user with a missing database name field
    {
      final JSONObject user =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "jakedoe");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("MISSING_ATTRIBUTE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user with an empty database name field
    {
      final JSONObject user =
          new JSONObject()
              .put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "jakedoe")
              .put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user to a group that doesn't exist
    {
      final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!ExtraLong");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(18) + "/databaseUsers",
              user,
              HttpStatus.SC_NOT_FOUND,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("GROUP_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user without appropriate access
    {
      final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!ExtraLong");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_UNAUTHORIZED,
              JOHN_DOE_USERNAME,
              JOHN_DOE_API_KEY);
      assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USER_CANNOT_ACCESS_GROUP", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user with duplicate roles
    {
      final JSONObject user =
          createNewUserJson("admin", "dupeAdmin", "D0ubl3T@k3:)ExtraLong")
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray(
                      Arrays.asList(
                          createRoleJson("admin", "read"), createRoleJson("admin", "read"))));
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          NDSErrorCode.DUPLICATE_DATABASE_ROLES.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
      assertTrue(resp.getString(ApiError.DETAIL_FIELD).contains("read"));
    }

    // Test adding a SCRAM user with a role that has an invalid collection name
    {
      final JSONObject user =
          createNewUserJson("admin", "user1", "D0ubl3T@k3:)ExtraLong")
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray(
                      Collections.singletonList(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "$someCollection"))));
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.INVALID_COLLECTION_NAME.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user with a common password
    {
      final JSONObject user = createNewUserJson("admin", "ezpass", "password");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("COMMON_PASSWORD", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test adding a SCRAM user on a non-admin database
    {
      final JSONObject user = createNewUserJson("mydb", "ezpass", "T0pSecr8!ExtraLong");
      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              user,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_INVALID_ADMIN", resp.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testAddUserGroupCustomRole() throws Exception {
    final ObjectId groupId = oid(118);
    final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!");
    final JSONObject role =
        user.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD).getJSONObject(0);
    final String customRoleName = "testRO";
    role.put(ApiAtlasRoleView.ROLE_NAME_FIELD, customRoleName);
    role.put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "admin");

    // Role doesn't exist, should fail
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
        user,
        HttpStatus.SC_BAD_REQUEST,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Role exists, successfully create user with custom role
    final NDSCustomDBRole.NDSDBAction ndsdbAction =
        new NDSCustomDBRole.NDSDBAction(
            NDSDBUserAction.FIND.getAction(),
            Collections.singletonList(
                new NDSCustomDBRole.NDSDBAction.NDSDBResource("test", "test")));
    final NDSCustomDBRole ndsCustomDBRole =
        new NDSCustomDBRole(
            customRoleName, Collections.singletonList(ndsdbAction), Collections.emptySet());
    _ndsGroupDao.saveCustomDBRoles(groupId, Collections.singletonList(ndsCustomDBRole));
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
        user,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);
    final JSONObject userResponse =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/nickdoe",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray roles = userResponse.getJSONArray("roles");
    assertEquals(1, roles.length());
    final JSONObject customRole = roles.getJSONObject(0);
    assertEquals(customRoleName, customRole.getString("roleName"));
    assertEquals("admin", customRole.getString("databaseName"));
  }

  private JSONObject createRoleJson(final String pDatabaseName, final String pRoleName)
      throws JSONException {
    final JSONObject roleJson = new JSONObject();
    roleJson.put(ApiAtlasRoleView.DATABASE_NAME_FIELD, pDatabaseName);
    roleJson.put(ApiAtlasRoleView.ROLE_NAME_FIELD, pRoleName);

    return roleJson;
  }

  @Test
  public void testAddUserWithScopes() {
    final ObjectId groupId = oid(118);

    // Add SCRAM user with empty scope field initialized
    final JSONObject user0 = createNewUserJson("admin", "scopeduser", "T0pSecr8!");
    user0.put(ApiAtlasDatabaseUserView.SCOPES_FIELD, new JSONArray());

    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
        user0,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    final JSONObject resp0 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    final JSONArray scopes0 = resp0.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
    assertNotNull(scopes0);
    assertEquals(0, scopes0.length());

    // Add SCRAM with no scope field initialized, still initializes scopes field
    final JSONObject user1 = createNewUserJson("admin", "nulluser", "T0pSecr8!");

    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
        user1,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    final JSONObject resp1 =
        doDigestJsonGet(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/nulluser",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    final JSONArray scopes1 = resp1.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
    assertNotNull(scopes1);
    assertEquals(0, scopes1.length());

    // Add SCRAM user with data lake and cluster
    final JSONObject user2 = createNewUserJson("admin", "twoscopes", "T0pSecr8!");
    user2.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        new JSONArray(
            Arrays.asList(
                createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()),
                createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));

    final JSONObject resp2 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
            user2,
            HttpStatus.SC_CREATED,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    final JSONArray scopes2 = resp2.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
    assertEquals(2, scopes2.length());
    assertTrue(hasScope(scopes2, CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()));
    assertTrue(hasScope(scopes2, DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()));

    // Add SCRAM user scoped to shared tier
    final JSONObject sharedTierUser = createNewUserJson("admin", "sharedTier", "T0pSecr8!");
    sharedTierUser.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name())));
    final JSONObject sharedTierUserResp =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            sharedTierUser,
            HttpStatus.SC_CREATED,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    final JSONArray scopes3 =
        sharedTierUserResp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
    assertEquals(1, scopes3.length());
    assertTrue(hasScope(scopes3, "free1", NDSUserScope.Type.CLUSTER.name()));

    // Tests for validation error messages when adding new SCRAM user with scope
    // Empty scope name
    final JSONObject user3 = createNewUserJson("admin", "emptyScopeName", "T0pSecr8!");
    user3.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        new JSONArray(List.of(createScopeJson("", NDSUserScope.Type.CLUSTER.name()))));

    final JSONObject resp3 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
            user3,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(HttpStatus.SC_BAD_REQUEST, resp3.get(ApiError.ERROR_FIELD));
    assertEquals("INVALID_ATTRIBUTE", resp3.getString(ApiError.ERROR_CODE_FIELD));

    // Null scope name
    final JSONObject user4 = createNewUserJson("admin", "invalidscope", "T0pSecr8!");
    user4.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        new JSONArray(List.of(createScopeJson(null, NDSUserScope.Type.CLUSTER.name()))));

    final JSONObject resp4 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
            user4,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(HttpStatus.SC_BAD_REQUEST, resp4.get(ApiError.ERROR_FIELD));
    assertEquals("INVALID_ATTRIBUTE", resp4.getString(ApiError.ERROR_CODE_FIELD));

    // Null scope type
    final JSONObject user5 = createNewUserJson("admin", "invalidscope2", "T0pSecr8!");
    user5.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        new JSONArray(List.of(createScopeJson(CLUSTER_NAME, null))));

    final JSONObject resp5 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
            user5,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(HttpStatus.SC_BAD_REQUEST, resp5.get(ApiError.ERROR_FIELD));
    assertEquals("INVALID_ATTRIBUTE", resp5.getString(ApiError.ERROR_CODE_FIELD));

    // Invalid scope name characters
    final JSONObject user6 = createNewUserJson("admin", "invalidscope3", "T0pSecr8!");
    user6.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        new JSONArray(
            List.of(createScopeJson("<invalid\\_scope>", NDSUserScope.Type.CLUSTER.name()))));

    final JSONObject resp6 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
            user6,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(HttpStatus.SC_BAD_REQUEST, resp6.get(ApiError.ERROR_FIELD));
    assertEquals("ATLAS_USER_SCOPE_INVALID_NAME", resp6.getString(ApiError.ERROR_CODE_FIELD));

    // Duplicate scope
    final JSONObject user7 = createNewUserJson("admin", "dupescope", "T0pSecr8!");
    user7.put(
        ApiAtlasDatabaseUserView.SCOPES_FIELD,
        new JSONArray(
            List.of(
                createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()),
                createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()))));

    final JSONObject resp7 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
            user7,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);

    assertEquals(HttpStatus.SC_BAD_REQUEST, resp7.get(ApiError.ERROR_FIELD));
    assertEquals("ATLAS_DUPLICATE_SCOPES", resp7.getString(ApiError.ERROR_CODE_FIELD));

    // Tests for non-SCRAM user scope validations
    // Add LDAPUser user scoped to shared tier cluster, should fail
    {
      final JSONObject ldapUser =
          createNewUserJson("$external", "CN=user1,DC=org", null, LDAPAuthType.USER, "admin")
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              ldapUser,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals(
          NDSErrorCode.UNSUPPORTED_AUTH_TYPE_FOR_SHARED_TIER.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }
    // Add aws iam user to shared tier should fail since version < 4.4
    {
      final JSONObject awsIAMUser =
          createNewAWSIAMUser(
                  "$external",
                  "arn:aws:iam::012345678910:user/testUser",
                  null,
                  LDAPAuthType.NONE,
                  X509Type.NONE,
                  AWSIAMType.USER,
                  null)
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              awsIAMUser,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals(
          NDSErrorCode.UNSUPPORTED_AWS_IAM_USER_FOR_SCOPED_CLUSTER.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Add X.509 user scoped to data lake, should succeed
    {
      final JSONObject dataLakeX509User =
          createNewX509User(
              "$external",
              "CN=user,O=Atlas",
              null,
              LDAPAuthType.NONE.name(),
              null,
              X509Type.CUSTOMER);

      dataLakeX509User.put(
          ApiAtlasDatabaseUserView.SCOPES_FIELD,
          new JSONArray(
              List.of(createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));

      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              dataLakeX509User,
              HttpStatus.SC_CREATED,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scopes = resp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(1, scopes.length());
      assertTrue(hasScope(scopes, DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()));
    }

    // Add scoped OIDC group, should fail since version < 7.0
    {
      final JSONObject oidcGroup = createNewOIDCGroup("admin", "lanky/kong", null);
      oidcGroup.put(
          ApiAtlasDatabaseUserView.SCOPES_FIELD,
          new JSONArray(List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              oidcGroup,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(
          NDSErrorCode.UNSUPPORTED_OIDC_GROUP_FOR_SCOPED_CLUSTER.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Add scoped OIDC group, should succeed with version >= 7.0
    {
      final JSONObject oidcGroup = createNewOIDCGroup("admin", "lanky/kong", null);
      oidcGroup.put(
          ApiAtlasDatabaseUserView.SCOPES_FIELD,
          new JSONArray(List.of(createScopeJson("rs-70", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject resp =
          doDigestJsonPost(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
              oidcGroup,
              HttpStatus.SC_CREATED,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scopes = resp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(1, scopes.length());
      assertTrue(hasScope(scopes, "rs-70", NDSUserScope.Type.CLUSTER.name()));
    }
  }

  @Test
  public void testPatchUserWithScopes() {
    final ObjectId groupId = oid(118);

    // Initialize SCRAM user
    {
      final JSONObject user0 =
          createNewUserJson("admin", "scopeduser", "T0pSecr8!")
              .put(ApiAtlasDatabaseUserView.SCOPES_FIELD, new JSONArray());

      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + groupId + "/databaseUsers",
          user0,
          HttpStatus.SC_CREATED,
          ADMIN_USERNAME,
          ADMIN_API_KEY);

      final JSONObject response =
          doDigestJsonGet(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scopes = response.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(0, scopes.length());
    }

    // Tests for updating existing SCRAM user with scopes
    // Delete existing scopes
    {
      final JSONObject userUpdateScopesPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.SCOPES_FIELD, new JSONArray());

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/scopeduser",
              userUpdateScopesPatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scopes1 = response.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(0, scopes1.length());
      assertLinkEquals(
          response,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/scopeduser");
    }

    // Add cluster and data lake scopes
    {
      final JSONObject clusterDataLakePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()),
                          createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/scopeduser",
              clusterDataLakePatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scopes2 = response.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(2, scopes2.length());
      assertLinkEquals(
          response,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/scopeduser");
    }

    // Add shared tier cluster
    {
      final JSONObject scramSharedTier =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject scramSharedTierResp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/scopeduser",
              scramSharedTier,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scramSharedTierScopes =
          scramSharedTierResp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(1, scramSharedTierScopes.length());
      assertLinkEquals(
          scramSharedTierResp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/scopeduser");
    }

    // Test validation error messages
    // Scope with empty name, should fail
    {
      final JSONObject emptyScopeNamePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(List.of(createScopeJson("", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
              emptyScopeNamePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Scope with null name, should fail
    {
      final JSONObject nullScopeNamePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(List.of(createScopeJson(null, NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
              nullScopeNamePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Scope with null type, should fail
    {
      final JSONObject nullScopeTypePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(List.of(createScopeJson(CLUSTER_NAME, null))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
              nullScopeTypePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Invalid scope name, should fail
    {
      final JSONObject invalidScopeNamePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson("<invalid\\_scope>", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
              invalidScopeNamePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_USER_SCOPE_INVALID_NAME", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Duplicate scopes, should fail
    {
      final JSONObject duplicateScopePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()),
                          createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/admin/scopeduser",
              duplicateScopePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_DUPLICATE_SCOPES", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Tests for non-SCRAM user scope validations
    final String ldapUsername = "CN=bad,DC=org";
    final JSONObject newLDAPUser =
        createNewUserJson("$external", ldapUsername, null, LDAPAuthType.USER, "admin");

    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        newLDAPUser,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Add shared tier cluster to LDAP user, should fail
    {
      final JSONObject sharedTierPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/$external/" + ldapUsername,
              sharedTierPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals(
          "UNSUPPORTED_AUTH_TYPE_FOR_SHARED_TIER", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Add data lake to LDAP user, should fail
    {
      final JSONObject dataLakePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));
      final JSONObject response =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + groupId + "/databaseUsers/$external/" + ldapUsername,
              dataLakePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      assertEquals(HttpStatus.SC_BAD_REQUEST, response.get(ApiError.ERROR_FIELD));
      assertEquals(
          "UNSUPPORTED_AUTH_TYPE_FOR_DATA_LAKE", response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Initialize OIDC group
    final JSONObject newOIDCGroup = createNewOIDCGroup("admin", "tiny/kong", null);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        newOIDCGroup,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Add cluster scope to OIDC group, should fail since version < 7.0
    {
      final JSONObject scopePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(createScopeJson("free1", NDSUserScope.Type.CLUSTER.name()))));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/tiny%2Fkong",
              scopePatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(
          NDSErrorCode.UNSUPPORTED_OIDC_GROUP_FOR_SCOPED_CLUSTER.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Add cluster and data lake scopes to OIDC group, should succeed with version >= 7.0
    {
      final JSONObject scopePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson("rs-70", NDSUserScope.Type.CLUSTER.name()),
                          createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/tiny%2Fkong",
              scopePatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);

      final JSONArray scopes = resp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(2, scopes.length());
      assertTrue(hasScope(scopes, "rs-70", NDSUserScope.Type.CLUSTER.name()));
      assertTrue(hasScope(scopes, DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()));
    }
  }

  private JSONObject createScopeJson(final String pScopeName, final String pScopeType)
      throws JSONException {
    final JSONObject scopeJson = new JSONObject();
    scopeJson.put(ApiAtlasUserScopeView.NAME_FIELD, pScopeName);
    scopeJson.put(ApiAtlasUserScopeView.TYPE_FIELD, pScopeType);
    return scopeJson;
  }

  @Test
  public void testPatchUser_Admin() {
    testPatchUser(ADMIN_USERNAME, ADMIN_API_KEY);
  }

  @Test
  public void testPatchUser_StreamProcessingAdmin() {
    testPatchUser_SCRAM(STREAMS_USER_NAME, STREAMS_API_KEY);
  }

  @Test
  public void testPatchUser_DatabaseAccessAdmin() throws SvcException {
    final AppUser user =
        MmsFactory.createUserWithRoleInGroup(
            _groupDao.findById(oid(118)),
            String.format("<EMAIL>", getUniquifier()),
            Role.GROUP_DATABASE_ACCESS_ADMIN);
    final UserApiKey key = _userApiKeySvc.add(user.getId(), "test");

    testPatchUser_SCRAM(user.getUsername(), key.getKey());
  }

  private void testPatchUser(final String pUsername, final String pApiKey) {
    // ----
    // SCRAM
    // ----

    testAddDeleteUser_SCRAM(pUsername, pApiKey);

    // ----
    // LDAP
    // ----

    // Test updating LDAP user
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com",
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com");
    }

    // Test updating LDAP user auth type fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, "NONE");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating LDAP user to SCRAM fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "pwd");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, LDAP).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating LDAP username fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "undignified");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating LDAP user auth database fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "admin");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/CN=user,DC=com",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating LDAP GROUP user
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com",
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com");
    }

    // Test updating LDAP GROUP user auth type fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, "NONE");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating LDAP GROUP user to SCRAM fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "pwd");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, LDAP).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating LDAP GROUP user username fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "undignified");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating LDAP GROUP user auth database fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "$external");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/CN=group,DC=com",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // ----
    // X509
    // ----

    // Add initial X509 MANAGED user
    final JSONObject x509User =
        createNewX509User(
            "$external", "x509User", null, LDAPAuthType.NONE.name(), null, X509Type.MANAGED);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        x509User,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);
    // Add customer X.509 user to group 131 (which has customer X.509 enabled)
    final JSONObject customerX509User =
        createNewX509User(
            "$external",
            "CN=userToUpdate,O=Atlas",
            null,
            LDAPAuthType.NONE.name(),
            null,
            X509Type.CUSTOMER);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(131) + "/databaseUsers",
        customerX509User,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Test updating X509 MANAGED user to NONE auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.X509_TYPE_FIELD, X509Type.NONE.name());
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_CONFLICT,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating X509 MANAGED user to be temporary
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD,
                  TimeUtils.toISOString(new Date()));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "PERMANENT_ENTITY_CANNOT_BE_MADE_TEMPORARY", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating X509 MANAGED user to SCRAM
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "moreSecurity");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, X509).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating X509 MANAGED user to have LDAP USER auth type
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, LDAPAuthType.USER.name());
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_CONFLICT,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating X509 MANAGED user auth database
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "admin");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_CONFLICT,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating X509 CUSTOMER user username
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "CN=updatedName,O=Atlas");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(131)
                  + "/databaseUsers/$external/CN=userToUpdate,O=Atlas",
              userPatch,
              HttpStatus.SC_CONFLICT,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating X509 MANAGED user username
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "newX509Username");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_CONFLICT,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating X509 MANAGED user roles
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User",
              userPatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals("MANAGED", resp.getString(ApiAtlasDatabaseUserView.X509_TYPE_FIELD));
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/$external/x509User");
    }

    // -------
    // AWS IAM
    // -------
    final String awsIAMUserFmtStr = "arn:aws:iam::012345678910:user/%s";
    final String awsIAMRoleFmtStr = "arn:aws:iam::012345678910:role/%s";

    // Add initial AWS IAM USER user
    final String awsIAMUserUsername = String.format(awsIAMUserFmtStr, "i-am-user");
    final JSONObject awsIAMUserUser =
        createNewAWSIAMUser(
            "$external",
            awsIAMUserUsername,
            null,
            LDAPAuthType.NONE,
            X509Type.NONE,
            AWSIAMType.USER,
            null);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        awsIAMUserUser,
        HttpStatus.SC_CREATED,
        pUsername,
        pApiKey);

    // Add initial AWS IAM USER user
    final String awsIAMRoleUsername = String.format(awsIAMRoleFmtStr, "i-am-role");
    final JSONObject awsIAMRoleUser =
        createNewAWSIAMUser(
            "$external",
            awsIAMRoleUsername,
            null,
            LDAPAuthType.NONE,
            X509Type.NONE,
            AWSIAMType.ROLE,
            null);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        awsIAMRoleUser,
        HttpStatus.SC_CREATED,
        pUsername,
        pApiKey);

    // Test updating AWS IAM USER user to NONE auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD, AWSIAMType.NONE);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM ROLE user to NONE auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD, AWSIAMType.NONE);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMRoleUsername),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM USER user to be temporary
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD,
                  TimeUtils.toISOString(new Date()));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "PERMANENT_ENTITY_CANNOT_BE_MADE_TEMPORARY", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM ROLE user to be temporary
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD,
                  TimeUtils.toISOString(new Date()));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMRoleUsername),
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "PERMANENT_ENTITY_CANNOT_BE_MADE_TEMPORARY", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM USER user to SCRAM
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "foo");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, MONGODB_AWS).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating AWS IAM ROLE user to SCRAM
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "foo");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMRoleUsername),
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, MONGODB_AWS).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating AWS IAM USER user to have LDAP auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, LDAPAuthType.USER);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM ROLE user to have LDAP auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, LDAPAuthType.USER);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMRoleUsername),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM USER user to have X509 auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.X509_TYPE_FIELD, X509Type.CUSTOMER);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM ROLE user to have X509 auth type
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.X509_TYPE_FIELD, X509Type.CUSTOMER);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + URLEncoder.encode(awsIAMRoleUsername, StandardCharsets.UTF_8),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("AUTH_TYPE_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM USER user auth database
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "admin");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + URLEncoder.encode(awsIAMUserUsername, StandardCharsets.UTF_8),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM ROLE user auth database
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "admin");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + URLEncoder.encode(awsIAMRoleUsername, StandardCharsets.UTF_8),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM USER user username
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.USERNAME_FIELD,
                  String.format(awsIAMUserFmtStr, "i-am-user-updated"));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + URLEncoder.encode(awsIAMUserUsername, StandardCharsets.UTF_8),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM ROLE user username
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.USERNAME_FIELD,
                  String.format(awsIAMUserFmtStr, "i-am-role-updated"));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + URLEncoder.encode(awsIAMRoleUsername, StandardCharsets.UTF_8),
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating AWS IAM USER user roles
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertEquals(
          AWSIAMType.USER.name(), resp.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/$external/"
              + urlPathSegmentEscaper().escape(awsIAMUserUsername));
    }

    // Test updating AWS IAM ROLE user roles
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMRoleUsername),
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertEquals(
          AWSIAMType.ROLE.name(), resp.getString(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD));
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/$external/"
              + urlPathSegmentEscaper().escape(awsIAMRoleUsername));
    }

    // Test AWS IAM USER user adding cluster and data lake scopes
    {
      final JSONObject clusterDataLakePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()),
                          createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMUserUsername),
              clusterDataLakePatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      final JSONArray scopes2 = resp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(2, scopes2.length());
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/$external/"
              + urlPathSegmentEscaper().escape(awsIAMUserUsername));
    }

    // Test AWS IAM ROLE user adding cluster and data lake scopes
    {
      final JSONObject clusterDataLakePatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.SCOPES_FIELD,
                  new JSONArray(
                      List.of(
                          createScopeJson(CLUSTER_NAME, NDSUserScope.Type.CLUSTER.name()),
                          createScopeJson(DATA_LAKE_NAME, NDSUserScope.Type.DATA_LAKE.name()))));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/"
                  + oid(118)
                  + "/databaseUsers/$external/"
                  + urlPathSegmentEscaper().escape(awsIAMRoleUsername),
              clusterDataLakePatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      final JSONArray scopes2 = resp.getJSONArray(ApiAtlasDatabaseUserView.SCOPES_FIELD);
      assertEquals(2, scopes2.length());
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/$external/"
              + urlPathSegmentEscaper().escape(awsIAMRoleUsername));
    }

    // ----
    // OIDC
    // ----
    final JSONObject oidcGroup = createNewOIDCGroup("admin", "bobhoskins/ismario", null);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        oidcGroup,
        HttpStatus.SC_CREATED,
        pUsername,
        pApiKey);

    // Test updating OIDC group
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario",
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario");
    }

    // Test updating OIDC group auth type fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.OIDC_AUTH_TYPE_FIELD, "NONE");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.AUTH_TYPE_CANNOT_BE_CHANGED.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating OIDC group to SCRAM fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "pwd");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.ATLAS_INVALID_AUTH_SETTINGS.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Invalid authentication settings. Users cannot be configured for multiple authentication"
              + " mechanisms (SCRAM, OIDC).",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating OIDC group to LDAP fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, "USER");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.AUTH_TYPE_CANNOT_BE_CHANGED.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          "Cannot modify the authentication type of an existing database user.",
          resp.getString(ApiError.DETAIL_FIELD));
    }

    // Test updating OIDC group username fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.USERNAME_FIELD, "not/chrispratt");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals(
          "DATABASE_USERNAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating OIDC group auth database fails
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, "$external");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/bobhoskins%2Fismario",
              userPatch,
              HttpStatus.SC_CONFLICT,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_CONFLICT, resp.get(ApiError.ERROR_FIELD));
      assertEquals("DATABASE_NAME_CANNOT_BE_CHANGED", resp.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  private void testPatchUser_SCRAM(final String pUsername, final String pApiKey) {
    final JSONObject userUpdateRolesForDatabasePatch =
        new JSONObject()
            .put(
                ApiAtlasDatabaseUserView.ROLES_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                            .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));

    // Test updating a non-existent user
    {
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/janetdoe",
              userUpdateRolesForDatabasePatch,
              HttpStatus.SC_NOT_FOUND,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USERNAME_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating a user in a non-existent group
    {
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(18) + "/databaseUsers/admin/nickdoe",
              userUpdateRolesForDatabasePatch,
              HttpStatus.SC_NOT_FOUND,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("GROUP_NOT_FOUND", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user roles with database level permissions
    {
      final JSONObject userPost = createNewUserJson("admin", "nickdoe", "T0pSecr8!");
      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
          userPost,
          HttpStatus.SC_CREATED,
          pUsername,
          pApiKey);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userUpdateRolesForDatabasePatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      final JSONArray roles = resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
      assertTrue(hasRole(roles, "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating SCRAM user roles with collection-level permissions
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "collection")));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(
          hasRole(
              resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD),
              "db",
              "collection",
              "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating SCRAM user collection-level permission with an empty string is treated as a
    // database level role
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "")));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating SCRAM user password
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "newpassword");

      final JSONObject respPassword =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_OK,
              pUsername,
              pApiKey);
      assertTrue(
          hasRole(
              respPassword.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          respPassword,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating non-editable SCRAM user
    {
      final JSONObject user = createNewUserJson("admin", "nonedit", "T0pSecr8!");
      user.put("isEditable", false);
      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
          user,
          HttpStatus.SC_CREATED,
          pUsername,
          pApiKey);
      // Should not be able to update them
      doDigestJsonPatch(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nonedit",
          user,
          HttpStatus.SC_FORBIDDEN,
          pUsername,
          pApiKey);
      // But can update them with override
      doDigestJsonPatch(
          "api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/admin/nonedit?overrideEditable=true",
          user,
          HttpStatus.SC_OK,
          pUsername,
          pApiKey);
    }

    // Test updating SCRAM user without appropriate permissions
    {
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userUpdateRolesForDatabasePatch,
              HttpStatus.SC_UNAUTHORIZED,
              JOHN_DOE_USERNAME,
              JOHN_DOE_API_KEY);
      assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USER_CANNOT_ACCESS_GROUP", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user with a common password
    {
      final JSONObject userPatch =
          new JSONObject(
                  userUpdateRolesForDatabasePatch,
                  JSONObject.getNames(userUpdateRolesForDatabasePatch))
              .put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "password");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("COMMON_PASSWORD", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user adding duplicate roles
    {
      final JSONObject userPatch =
          new JSONObject(
              userUpdateRolesForDatabasePatch,
              JSONObject.getNames(userUpdateRolesForDatabasePatch));
      userPatch.put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "P@ssM@sterz$:/");
      userPatch.put(
          ApiAtlasDatabaseUserView.ROLES_FIELD,
          new JSONArray(
              Arrays.asList(createRoleJson("admin", "read"), createRoleJson("admin", "read"))));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          NDSErrorCode.DUPLICATE_DATABASE_ROLES.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
      assertTrue(resp.getString(ApiError.DETAIL_FIELD).contains("read"));
    }

    // Test updating SCRAM user with a nonexistent role
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "bad")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_ROLE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user with a role on non-existent db
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "bad")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_ROLE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user with a role that does not have collection level permission
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "admin")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "clusterMonitor")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "db")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              pUsername,
              pApiKey);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testPatchUserWithInvalidPassword() {
    // Create the test user.
    final JSONObject userPost = createNewUserJson("admin", "nickdoe", "T0pSecr8!ExtraLong");
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        userPost,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);
    final JSONObject userPatchData = new JSONObject();
    final byte[] invalidPasswordBytes = {1, 'a', 'b'};
    userPatchData.put(
        ApiAtlasDatabaseUserView.PASSWORD_FIELD,
        new String(invalidPasswordBytes, StandardCharsets.UTF_8));

    final JSONObject invalidPasswordResp =
        doDigestJsonPatch(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
            userPatchData,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, invalidPasswordResp.getInt(ApiError.ERROR_FIELD));
    assertEquals("INVALID_PASSWORD", invalidPasswordResp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testPatchUserWithRestrictedRole() throws Exception {
    // Create the test user.
    final JSONObject user = createNewUserJson("admin", "nickdoe", "T0pSecr8!");
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        user,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Try patching a user with a restricted role.
    final JSONArray badUserPatchRoles = new JSONArray();
    final JSONObject badUserPatchRole = new JSONObject();
    badUserPatchRole.put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "admin");
    badUserPatchRole.put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite");
    badUserPatchRole.put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "someCollection");
    badUserPatchRoles.put(badUserPatchRole);
    final JSONObject userPatchData = new JSONObject();
    userPatchData.put(ApiAtlasDatabaseUserView.ROLES_FIELD, badUserPatchRoles);

    final JSONObject restrictedRoleResp =
        doDigestJsonPatch(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
            userPatchData,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, restrictedRoleResp.get(ApiError.ERROR_FIELD));
    assertEquals("ATLAS_RESTRICTED_ROLE", restrictedRoleResp.getString(ApiError.ERROR_CODE_FIELD));

    // Try patching a user with a role with a restricted collection.
    final JSONArray badUserPatchRoles2 = new JSONArray();
    final JSONObject badUserPatchRole2 = new JSONObject();
    badUserPatchRole2.put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db");
    badUserPatchRole2.put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite");
    badUserPatchRole2.put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "system.users");
    badUserPatchRoles2.put(badUserPatchRole2);
    final JSONObject userPatchData2 = new JSONObject();
    userPatchData2.put(ApiAtlasDatabaseUserView.ROLES_FIELD, badUserPatchRoles2);

    final JSONObject restrictedCollectionResp =
        doDigestJsonPatch(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
            userPatchData2,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, restrictedCollectionResp.get(ApiError.ERROR_FIELD));
    assertEquals(
        "ATLAS_RESTRICTED_COLLECTION",
        restrictedCollectionResp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testPatchUserSCRAMUpdatesInGovEnvironment() {
    _appSettings.setProp("nds.gov.us.enabled", "true", AppSettings.SettingType.MEMORY);
    final JSONObject userUpdateRolesForDatabasePatch =
        new JSONObject()
            .put(
                ApiAtlasDatabaseUserView.ROLES_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                            .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")));

    // ------
    // SCRAM
    // ------

    // Test updating SCRAM user roles with database level permissions
    {
      final JSONObject userPost = createNewUserJson("admin", "nickdoe", "T0pSecr8!ExtraLong");
      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
          userPost,
          HttpStatus.SC_CREATED,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userUpdateRolesForDatabasePatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      final JSONArray roles = resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD);
      assertTrue(hasRole(roles, "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating SCRAM user roles with collection-level permissions
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "collection")));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertTrue(
          hasRole(
              resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD),
              "db",
              "collection",
              "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating SCRAM user collection-level permission with an empty string is treated as a
    // database level role
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "readWrite")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "")));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertTrue(
          hasRole(resp.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          resp,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating SCRAM user password
    {
      final JSONObject userPatch =
          new JSONObject().put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "newpasswordExtraLong");

      final JSONObject respPassword =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertTrue(
          hasRole(
              respPassword.getJSONArray(ApiAtlasDatabaseUserView.ROLES_FIELD), "db", "readWrite"));
      assertLinkEquals(
          respPassword,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe");
    }

    // Test updating non-editable SCRAM user
    {
      final JSONObject user = createNewUserJson("admin", "nonedit", "T0pSecr8!ExtraLong");
      user.put("isEditable", false);
      doDigestJsonPost(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
          user,
          HttpStatus.SC_CREATED,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
      // Should not be able to update them
      doDigestJsonPatch(
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nonedit",
          user,
          HttpStatus.SC_FORBIDDEN,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
      // But can update them with override
      doDigestJsonPatch(
          "api/atlas/v1.0/groups/"
              + oid(118)
              + "/databaseUsers/admin/nonedit?overrideEditable=true",
          user,
          HttpStatus.SC_OK,
          ADMIN_USERNAME,
          ADMIN_API_KEY);
    }

    // Test updating SCRAM user without appropriate permissions
    {
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userUpdateRolesForDatabasePatch,
              HttpStatus.SC_UNAUTHORIZED,
              JOHN_DOE_USERNAME,
              JOHN_DOE_API_KEY);
      assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("USER_CANNOT_ACCESS_GROUP", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user with a common password
    {
      final JSONObject userPatch =
          new JSONObject(
                  userUpdateRolesForDatabasePatch,
                  JSONObject.getNames(userUpdateRolesForDatabasePatch))
              .put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "password");
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals("COMMON_PASSWORD", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user adding duplicate roles
    {
      final JSONObject userPatch =
          new JSONObject(
              userUpdateRolesForDatabasePatch,
              JSONObject.getNames(userUpdateRolesForDatabasePatch));
      userPatch.put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, "P@ssM@sterz$:/ExtraLong");
      userPatch.put(
          ApiAtlasDatabaseUserView.ROLES_FIELD,
          new JSONArray(
              Arrays.asList(createRoleJson("admin", "read"), createRoleJson("admin", "read"))));
      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          NDSErrorCode.DUPLICATE_DATABASE_ROLES.name(), resp.getString(ApiError.ERROR_CODE_FIELD));
      assertTrue(resp.getString(ApiError.DETAIL_FIELD).contains("read"));
    }

    // Test updating SCRAM user with a nonexistent role
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "bad")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_ROLE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user with a role on non-existent db
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "db")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "bad")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals("ATLAS_INVALID_ROLE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Test updating SCRAM user with a role that does not have collection level permission
    {
      final JSONObject userPatch =
          new JSONObject()
              .put(
                  ApiAtlasDatabaseUserView.ROLES_FIELD,
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, "admin")
                              .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "clusterMonitor")
                              .put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "db")));

      final JSONObject resp =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/nickdoe",
              userPatch,
              HttpStatus.SC_BAD_REQUEST,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.get(ApiError.ERROR_FIELD));
      assertEquals("INVALID_ATTRIBUTE", resp.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  private JSONObject userToJson(final ApiAtlasDatabaseUserView pView)
      throws JsonProcessingException {
    final ObjectMapper objectMapper = new ObjectMapperProvider(Set.of()).get();
    return new JSONObject(
        objectMapper.writerFor(ApiAtlasDatabaseUserView.class).writeValueAsString(pView));
  }

  @Test
  public void testPatch_forUserWithLabels_noLabelsIncluded_doesNotUpdate() throws Exception {
    final var adminDb = "admin";
    final var username = "username0";
    final var gid = oid(118).toHexString();
    final List<ApiAtlasNDSLabelView> startingLabels =
        List.of(new ApiAtlasNDSLabelView("Label1", "value1"));
    final ApiAtlasDatabaseUserView startingUserView;
    {
      final var password = "aslkdjf890736498password";
      final ApiAtlasDatabaseUserViewBuilder builder = new ApiAtlasDatabaseUserView().toBuilder();
      startingUserView =
          builder
              .username(username)
              .databaseName(adminDb)
              .password(password)
              .groupId(gid)
              .roles(List.of(new ApiAtlasRoleView(new NDSDBRoleView("admin", "read"))))
              .labels(startingLabels)
              .build();
    }

    // Initial user creation - only check labels get set
    final JSONObject userCreateResponse =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + gid + "/databaseUsers",
            userToJson(startingUserView),
            HttpStatus.SC_CREATED,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final ObjectMapper objectMapper = new ObjectMapperProvider(Set.of()).get();
    final ObjectReader userReader = objectMapper.readerFor(ApiAtlasDatabaseUserView.class);

    final ApiAtlasDatabaseUserView userParsed =
        userReader.readValue(userCreateResponse.toString(), ApiAtlasDatabaseUserView.class);
    assertEquals(startingLabels, userParsed.getLabels());

    final Function<JSONObject, JSONObject> patchUser =
        (final JSONObject pPayload) ->
            doDigestJsonPatch(
                "api/atlas/v1.0/groups/" + gid + "/databaseUsers/admin/" + username,
                pPayload,
                HttpStatus.SC_OK,
                ADMIN_USERNAME,
                ADMIN_API_KEY);
    final var emptyUserForPatchBuilder = new ApiAtlasDatabaseUserView();

    // Patch the password - no expected change to the user
    final JSONObject passwordPatch =
        userToJson(emptyUserForPatchBuilder.toBuilder().password("alskdjflkasjdlakjsg").build());
    final JSONObject passwordChange_patch_response = patchUser.apply(passwordPatch);
    final ApiAtlasDatabaseUserView password_patch_user =
        userReader.readValue(
            passwordChange_patch_response.toString(), ApiAtlasDatabaseUserView.class);
    assertEquals(password_patch_user.getLabels(), startingLabels);
    // check that we haven't had other side effect modifications
    assertEquals(userParsed, password_patch_user);

    // clear out the labels explicitly - should work
    final ApiAtlasDatabaseUserView patchSetLinkEmpty =
        emptyUserForPatchBuilder.toBuilder().labels(List.of()).build();
    final JSONObject patchResponse = patchUser.apply(userToJson(patchSetLinkEmpty));
    final var patchedUserParsed =
        userReader.readValue(patchResponse.toString(), ApiAtlasDatabaseUserView.class);
    assertEquals(patchedUserParsed.getLabels(), List.of());

    // Add labels and verify added
    final JSONObject patchSetLink =
        userToJson(emptyUserForPatchBuilder.toBuilder().labels(startingLabels).build());
    final JSONObject patchResponse_addLinks = patchUser.apply(patchSetLink);

    final ApiAtlasDatabaseUserView patchedUser_linksAdded_Parsed =
        userReader.readValue(patchResponse_addLinks.toString(), ApiAtlasDatabaseUserView.class);
    assertEquals(patchedUser_linksAdded_Parsed.getLabels(), startingLabels);

    // Test explicitly specifying a null value
    final JSONObject setLabelsToNullPatch = new JSONObject().put(LABELS_FIELD, JSONObject.NULL);
    final JSONObject setLabelsToNullPatch_response = patchUser.apply(setLabelsToNullPatch);
    final ApiAtlasDatabaseUserView nullPatched_user =
        userReader.readValue(
            setLabelsToNullPatch_response.toString(), ApiAtlasDatabaseUserView.class);
    assertEquals(nullPatched_user.getLabels(), List.of());
  }

  @Test
  public void testDeleteAfterDate() throws Exception {
    final Date oneWeek = new Date(System.currentTimeMillis() + Duration.ofDays(7).toMillis());
    final Date twoWeeks = new Date(System.currentTimeMillis() + Duration.ofDays(14).toMillis());
    final Date yesterday = new Date(System.currentTimeMillis() - Duration.ofDays(1).toMillis());

    // Create a test temporary user
    final JSONObject user =
        createNewUserJson("admin", "temporaryHarry", "imJustStoppingBy!", oneWeek);
    doDigestJsonPost(
        "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
        user,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY);

    // Cannot update to be in the past
    final JSONObject yesterdayPatch = new JSONObject();
    yesterdayPatch.put(
        ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD, TimeUtils.toISOString(yesterday));
    final JSONObject yesterdayResponse =
        doDigestJsonPatch(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry",
            yesterdayPatch,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals("EXPIRATION_DATE_IN_PAST", yesterdayResponse.getString(ApiError.ERROR_CODE_FIELD));

    // Cannot update to be too far in the future
    final JSONObject twoWeeksPatch = new JSONObject();
    twoWeeksPatch.put(
        ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD, TimeUtils.toISOString(twoWeeks));
    final JSONObject twoWeeksResponse =
        doDigestJsonPatch(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry",
            twoWeeksPatch,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(
        "EXPIRATION_DATE_EXCEEDS_MAX", twoWeeksResponse.getString(ApiError.ERROR_CODE_FIELD));

    // Patching with nothing in the field causes no change
    {
      final String originalValue = user.getString(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD);
      user.remove(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD);
      final JSONObject patchResult =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry",
              user,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertEquals(
          originalValue, patchResult.getString(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD));
      assertLinkEquals(
          patchResult,
          LinkRelView.SELF,
          "/api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry");
    }

    // Sending null clears the field and makes user permanent
    {
      user.put(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD, JSONObject.NULL);
      final JSONObject patchResult =
          doDigestJsonPatch(
              "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry",
              user,
              HttpStatus.SC_OK,
              ADMIN_USERNAME,
              ADMIN_API_KEY);
      assertFalse(patchResult.has(ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD));
      assertLinkEquals(
          patchResult,
          LinkRelView.SELF,
          "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry");
    }

    // Cannot update permanent user to be temporary
    final JSONObject permanentPatch = new JSONObject();
    permanentPatch.put(
        ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD, TimeUtils.toISOString(oneWeek));
    final JSONObject permanentResponse =
        doDigestJsonPatch(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers/admin/temporaryHarry",
            permanentPatch,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(
        "PERMANENT_ENTITY_CANNOT_BE_MADE_TEMPORARY",
        permanentResponse.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testCreateUserLabels() {
    // Valid label
    final JSONArray validLabel = createNDSLabelsJsonArray("foo", "bar");
    final JSONObject user1 = createNewUserJsonWithLabels("admin", "user1", "T0pSecr8!", validLabel);
    final JSONObject resp1 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user1,
            HttpStatus.SC_CREATED,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray labels = resp1.getJSONArray(ApiAtlasDatabaseUserView.LABELS_FIELD);
    assertTrue(hasLabel(labels, "foo", "bar"));

    // Null label
    final JSONArray nullLabel = createNDSLabelsJsonArray(null, "bar");
    final JSONObject user2 = createNewUserJsonWithLabels("admin", "user2", "T0pSecr8!", nullLabel);
    final JSONObject resp2 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user2,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp2.getString(ApiError.ERROR_CODE_FIELD));

    // Label with value exceeding 255 characters
    final JSONArray tooLongLabel = createNDSLabelsJsonArray("foo", StringUtils.repeat("i", 256));
    final JSONObject user3 =
        createNewUserJsonWithLabels("admin", "user3", "T0pSecr8!", tooLongLabel);
    final JSONObject resp3 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user3,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp3.getInt(ApiError.ERROR_FIELD));
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp3.getString(ApiError.ERROR_CODE_FIELD));

    // Label with missing field
    final JSONArray missingValue = new JSONArray().put(new JSONObject().put("key", "foo"));
    final JSONObject user4 =
        createNewUserJsonWithLabels("admin", "user4", "T0pSecr8!", missingValue);
    final JSONObject resp4 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user4,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp4.getInt(ApiError.ERROR_FIELD));
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp4.getString(ApiError.ERROR_CODE_FIELD));

    // Empty array
    final JSONObject user5 =
        createNewUserJsonWithLabels("admin", "user5", "T0pSecr8!", new JSONArray());
    final JSONObject resp5 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user5,
            HttpStatus.SC_CREATED,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertTrue(resp5.getJSONArray(ApiAtlasDatabaseUserView.LABELS_FIELD).isEmpty());

    // Label with extra field
    final JSONArray extraFieldLabel =
        new JSONArray()
            .put(new JSONObject().put("key", "foo").put("value", "bar").put("extra", "field"));
    final JSONObject user6 =
        createNewUserJsonWithLabels("admin", "user6", "T0pSecr8!", extraFieldLabel);
    final JSONObject resp6 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user6,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp6.getInt(ApiError.ERROR_FIELD));
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp6.getString(ApiError.ERROR_CODE_FIELD));

    // Label with empty object
    final JSONObject user7 =
        createNewUserJsonWithLabels(
            "admin", "user7", "T0pSecr8!", new JSONArray().put(new JSONObject()));
    final JSONObject resp7 =
        doDigestJsonPost(
            "api/atlas/v1.0/groups/" + oid(118) + "/databaseUsers",
            user7,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp7.getInt(ApiError.ERROR_FIELD));
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp7.getString(ApiError.ERROR_CODE_FIELD));
  }

  private static JSONObject createNewUserJson(
      final String pDatabaseName,
      final String pUsername,
      final String pPassword,
      final Date pDeleteAfterDate)
      throws JSONException {
    return createNewUserJson(
        pDatabaseName,
        pUsername,
        pPassword,
        LDAPAuthType.NONE.name(),
        pDatabaseName,
        pDeleteAfterDate,
        X509Type.NONE.name(),
        null);
  }

  private static JSONObject createNewUserJson(
      final String pDatabaseName, final String pUsername, final String pPassword)
      throws JSONException {
    return createNewUserJson(pDatabaseName, pUsername, pPassword, null);
  }

  private static JSONObject createNewUserJson(
      final String pAuthenticationDatabaseName,
      final String pUsername,
      final String pPassword,
      final LDAPAuthType pLDAPAuthType,
      final String pRoleDatabaseName)
      throws JSONException {
    return createNewUserJson(
        pAuthenticationDatabaseName,
        pUsername,
        pPassword,
        pLDAPAuthType.name(),
        pRoleDatabaseName,
        null,
        X509Type.NONE.name(),
        null);
  }

  private static JSONObject createNewX509User(
      final String pAuthenticationDatabaseName,
      final String pUsername,
      final String pPassword,
      final String pLDAPAuthType,
      final Date pDeleteAfterDate,
      final X509Type pX509Type) {
    return createNewUserJson(
        pAuthenticationDatabaseName,
        pUsername,
        pPassword,
        pLDAPAuthType,
        "admin",
        pDeleteAfterDate,
        pX509Type.name(),
        null);
  }

  private static JSONObject createNewAWSIAMUser(
      final String pAuthenticationDatabaseName,
      final String pUsername,
      final String pPassword,
      final LDAPAuthType pLDAPAuthType,
      final X509Type pX509Type,
      final AWSIAMType pAWSIAMType,
      final Date pDeleteAfterDate) {
    final JSONObject user =
        createNewUserJson(
            pAuthenticationDatabaseName,
            pUsername,
            null,
            pLDAPAuthType.name(),
            pX509Type.name(),
            pAWSIAMType.name(),
            OIDCAuthType.NONE.name(),
            createRolesJSONArray("admin"),
            pDeleteAfterDate,
            null);
    if (pPassword != null) {
      user.put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, pPassword);
    }
    return user;
  }

  private static JSONObject createNewOIDCGroup(
      final String pAuthenticationDatabaseName, final String pUsername, final String pPassword) {
    return createNewUserJson(
        pAuthenticationDatabaseName,
        pUsername,
        pPassword,
        LDAPAuthType.NONE.name(),
        X509Type.NONE.name(),
        AWSIAMType.NONE.name(),
        OIDCAuthType.IDP_GROUP.name(),
        createRolesJSONArray("admin"),
        null,
        null);
  }

  private static JSONObject createNewUserJsonWithLabels(
      final String pDatabaseName,
      final String pUsername,
      final String pPassword,
      final JSONArray pLabels) {
    return createNewUserJson(
        pDatabaseName,
        pUsername,
        pPassword,
        LDAPAuthType.NONE.name(),
        pDatabaseName,
        null,
        X509Type.NONE.name(),
        pLabels);
  }

  private static JSONObject createNewUserJson(
      final String pAuthenticationDatabaseName,
      final String pUsername,
      final String pPassword,
      final String pLDAPAuthType,
      final String pRoleDatabaseName,
      final Date pDeleteAfterDate,
      final String pX509Type,
      final JSONArray pLabels) {
    return createNewUserJson(
        pAuthenticationDatabaseName,
        pUsername,
        pPassword,
        pLDAPAuthType,
        pX509Type,
        AWSIAMType.NONE.name(),
        OIDCAuthType.NONE.name(),
        createRolesJSONArray(pRoleDatabaseName),
        pDeleteAfterDate,
        pLabels);
  }

  public static JSONArray createRolesJSONArray(final String pRoleDatabaseName) {
    final JSONObject role =
        new JSONObject()
            .put(ApiAtlasRoleView.DATABASE_NAME_FIELD, pRoleDatabaseName)
            .put(ApiAtlasRoleView.ROLE_NAME_FIELD, "read");
    return new JSONArray().put(role);
  }

  private static JSONObject createNewUserJson(
      final String pAuthenticationDatabaseName,
      final String pUsername,
      final String pPassword,
      final String pLDAPAuthType,
      final String pX509Type,
      final String pAWSIAMType,
      final String pOIDCAuthType,
      final JSONArray pRoles,
      final Date pDeleteAfterDate,
      final JSONArray pLabels)
      throws JSONException {
    final JSONObject user = new JSONObject();
    user.put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, pAuthenticationDatabaseName);
    user.put(ApiAtlasDatabaseUserView.USERNAME_FIELD, pUsername);
    if (LDAPAuthType.NONE.name().equals(pLDAPAuthType)) {
      user.put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, pPassword);
    }
    user.put(ApiAtlasDatabaseUserView.LDAP_AUTH_TYPE_FIELD, pLDAPAuthType);
    user.put(ApiAtlasDatabaseUserView.X509_TYPE_FIELD, pX509Type);
    user.put(ApiAtlasDatabaseUserView.AWS_IAM_TYPE_FIELD, pAWSIAMType);
    user.put(ApiAtlasDatabaseUserView.OIDC_AUTH_TYPE_FIELD, pOIDCAuthType);
    user.put(ApiAtlasDatabaseUserView.ROLES_FIELD, pRoles);
    user.put(
        ApiAtlasDatabaseUserView.DELETE_AFTER_DATE_FIELD, TimeUtils.toISOString(pDeleteAfterDate));
    if (pLabels != null) {
      user.put(ApiAtlasDatabaseUserView.LABELS_FIELD, pLabels);
    }
    return user;
  }

  private static JSONObject createUserWithCollectionPermissions(
      final String pDatabaseName, final String pUsername, final String pPassword)
      throws JSONException {
    final JSONObject user = new JSONObject();
    user.put(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD, pDatabaseName);
    user.put(ApiAtlasDatabaseUserView.USERNAME_FIELD, pUsername);
    user.put(ApiAtlasDatabaseUserView.PASSWORD_FIELD, pPassword);
    final JSONArray roles = new JSONArray();
    final JSONObject role = new JSONObject();
    role.put(ApiAtlasRoleView.DATABASE_NAME_FIELD, pDatabaseName);
    role.put(ApiAtlasRoleView.ROLE_NAME_FIELD, "read");
    role.put(ApiAtlasRoleView.COLLECTION_NAME_FIELD, "collection");
    roles.put(role);
    user.put(ApiAtlasDatabaseUserView.ROLES_FIELD, roles);
    return user;
  }

  private JSONArray createNDSLabelsJsonArray(final String pKey, final String pValue) {
    return new JSONArray()
        .put(
            new JSONObject()
                .put(ApiAtlasNDSLabelView.KEY, pKey)
                .put(ApiAtlasNDSLabelView.VALUE, pValue));
  }

  private static boolean hasUser(
      final JSONArray pArray, final String pDatabaseName, final String pUsername)
      throws JSONException {
    for (int i = 0; i < pArray.length(); i++) {
      final JSONObject item = pArray.getJSONObject(i);
      if (item.getString(ApiAtlasDatabaseUserView.USERNAME_FIELD).equals(pUsername)
          && item.getString(ApiAtlasDatabaseUserView.DATABASE_NAME_FIELD).equals(pDatabaseName)) {
        return true;
      }
    }
    return false;
  }

  private static boolean hasRole(
      final JSONArray pArray, final String pDatabaseName, final String pRoleName)
      throws JSONException {
    for (int i = 0; i < pArray.length(); i++) {
      final JSONObject item = pArray.getJSONObject(i);
      if (item.getString(ApiAtlasRoleView.DATABASE_NAME_FIELD).equals(pDatabaseName)
          && item.getString(ApiAtlasRoleView.ROLE_NAME_FIELD).equals(pRoleName)
          && (!item.has(ApiAtlasRoleView.COLLECTION_NAME_FIELD)
              || item.getString(ApiAtlasRoleView.COLLECTION_NAME_FIELD) == null)) {
        return true;
      }
    }
    return false;
  }

  private static boolean hasLabel(final JSONArray pArray, final String pKey, final String pValue)
      throws JSONException {
    for (int i = 0; i < pArray.length(); i++) {
      final JSONObject item = pArray.getJSONObject(i);
      if (item.getString(ApiAtlasNDSLabelView.KEY).equals(pKey)
          && item.getString(ApiAtlasNDSLabelView.VALUE).equals(pValue)) {
        return true;
      }
    }
    return false;
  }

  private static boolean hasRole(
      final JSONArray pArray,
      final String pDatabaseName,
      final String pCollectionName,
      final String pRoleName)
      throws JSONException {
    for (int i = 0; i < pArray.length(); i++) {
      final JSONObject item = pArray.getJSONObject(i);
      if (item.getString(ApiAtlasRoleView.DATABASE_NAME_FIELD).equals(pDatabaseName)
          && item.getString(ApiAtlasRoleView.ROLE_NAME_FIELD).equals(pRoleName)
          && item.getString(ApiAtlasRoleView.COLLECTION_NAME_FIELD).equals(pCollectionName)) {
        return true;
      }
    }
    return false;
  }

  private static boolean hasScope(
      final JSONArray pArray, final String pScopedResourceName, final String pScopedResourceType)
      throws JSONException {
    for (int i = 0; i < pArray.length(); i++) {
      final JSONObject item = pArray.getJSONObject(i);
      if (item.getString(ApiAtlasUserScopeView.NAME_FIELD).equals(pScopedResourceName)
          && item.getString(ApiAtlasUserScopeView.TYPE_FIELD).equals(pScopedResourceType)) {
        return true;
      }
    }
    return false;
  }
}
