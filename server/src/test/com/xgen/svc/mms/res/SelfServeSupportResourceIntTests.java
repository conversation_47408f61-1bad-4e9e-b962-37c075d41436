package com.xgen.svc.mms.res;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Status;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseResourceTest;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.dao.billing.SelfServeProductDao;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlanType;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.util.billing.NumberFormatter;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import jakarta.inject.Inject;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class SelfServeSupportResourceIntTests extends BaseResourceTest {

  @Inject private OrganizationFactory _organizationFactory;
  @Inject private InvoiceFactory _invoiceFactory;
  @Inject private SelfServeProductDao _selfServeProductDao;

  private static final long DIVISIBLE_MONTHLY_CHARGE_CENTS = 37758000;
  // Happens with reactivation on May 1
  private static final long MAXIMUM_POSSIBLE_SUPPORT_CHARGE =
      (DIVISIBLE_MONTHLY_CHARGE_CENTS * 5) / 5;
  // Happens with reactivation on Jan 31 or Aug 31
  private static final long MINIMUM_POSSIBLE_SUPPORT_CHARGE =
      (DIVISIBLE_MONTHLY_CHARGE_CENTS * 2 + DIVISIBLE_MONTHLY_CHARGE_CENTS / 30 * 29) / 5;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _selfServeProductDao.syncDocuments();
  }

  @Test
  public void getPlanDetails_ReactivatedAfter90Days_Charges90DaysBackFillCharge() {
    final Date now = new Date();
    final Date startOfDay = DateUtils.truncate(now, Calendar.DATE);
    final Date nextDay = DateUtils.addDays(startOfDay, 1);
    final Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    final Date minus1Months = DateUtils.addMonths(startOfMonth, -1);
    final Date minus2Months = DateUtils.addMonths(startOfMonth, -2);
    final Date minus3Months = DateUtils.addMonths(startOfMonth, -3);
    final Date minus4Months = DateUtils.addMonths(startOfMonth, -4);

    final Date startOfSupportPeriod =
        DateUtils.addDays(DateUtils.truncate(now, Calendar.DATE), -90);
    final long monthlyUsageCents = DIVISIBLE_MONTHLY_CHARGE_CENTS;

    // Developer has uplift charges of 20%
    final long monthlyUpliftCents = monthlyUsageCents / 5;

    // Set up organization
    final Organization org = setupReactivationOrg(PrepaidPlanType.NDS_DEVELOPER, now);
    addPreviousMonthInvoicesToReactivationOrg(
        org, startOfMonth, nextDay, monthlyUsageCents, Status.PENDING);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus1Months, startOfMonth, monthlyUsageCents, Status.CLOSED);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus2Months, minus1Months, monthlyUsageCents, Status.CLOSED);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus3Months, minus2Months, monthlyUsageCents, Status.CLOSED);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus4Months, minus3Months, monthlyUsageCents, Status.CLOSED);

    final AppUser orgMember =
        MmsFactory.createUserWithRoleInOrganization(org, "<EMAIL>", Role.ORG_MEMBER);
    final JSONObject developerResponse =
        doAuthedJsonGet(
            orgMember,
            "/selfServe/reactivationDetails/org/" + org.getId() + "?productType=ATLAS_DEVELOPER");

    // All charges in current month happened before current time, so we count it as a full month
    final long numberOfFullMonthCharges = numberOfFullMonthsInPast90Days(now) + 1;
    final long expectedSupportChargesForFirstMonth =
        getExpectedSupportChargesForPartialEndOfMonth(monthlyUpliftCents, startOfSupportPeriod);
    final long expectedSupportCharges =
        numberOfFullMonthCharges * monthlyUpliftCents + expectedSupportChargesForFirstMonth;

    // Sanity check the logic
    assertTrue(expectedSupportCharges >= MINIMUM_POSSIBLE_SUPPORT_CHARGE);
    assertTrue(expectedSupportCharges <= MAXIMUM_POSSIBLE_SUPPORT_CHARGE);

    final String expectedChargesString = NumberFormatter.formatUsd(expectedSupportCharges);

    assertEquals(expectedChargesString, developerResponse.getString("estimatedCharges"));
  }

  @Test
  public void
      getPlanDetails_ReactivatedAfter90Days_MinimumUpliftOneMonth_Charges90DaysBackFillCharge() {
    final Date now = new Date();
    final Date startOfDay = DateUtils.truncate(now, Calendar.DATE);
    final Date nextDay = DateUtils.addDays(startOfDay, 1);
    final Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    final Date minus1Months = DateUtils.addMonths(startOfMonth, -1);
    final Date minus2Months = DateUtils.addMonths(startOfMonth, -2);
    final Date minus3Months = DateUtils.addMonths(startOfMonth, -3);
    final Date minus4Months = DateUtils.addMonths(startOfMonth, -4);

    final Date startOfSupportPeriod =
        DateUtils.addDays(DateUtils.truncate(now, Calendar.DATE), -90);
    final long monthlyUsageCents = DIVISIBLE_MONTHLY_CHARGE_CENTS;
    // Developer has uplift charges of 20%
    final long monthlyUpliftCents = monthlyUsageCents / 5;
    final long monthlyMinimumCharge = 4900;

    final Organization org = setupReactivationOrg(PrepaidPlanType.NDS_DEVELOPER, now);
    addPreviousMonthInvoicesToReactivationOrg(
        org, startOfMonth, nextDay, monthlyUsageCents, Status.PENDING);
    // This month will charge the exact monthly minimum
    addPreviousMonthInvoicesToReactivationOrg(org, minus1Months, startOfMonth, 0, Status.CLOSED);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus2Months, minus1Months, monthlyUsageCents, Status.CLOSED);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus3Months, minus2Months, monthlyUsageCents, Status.CLOSED);
    addPreviousMonthInvoicesToReactivationOrg(
        org, minus4Months, minus3Months, monthlyUsageCents, Status.CLOSED);

    final AppUser orgMember =
        MmsFactory.createUserWithRoleInOrganization(org, "<EMAIL>", Role.ORG_MEMBER);
    final JSONObject developerResponse =
        doAuthedJsonGet(
            orgMember,
            "/selfServe/reactivationDetails/org/" + org.getId() + "?productType=ATLAS_DEVELOPER");

    // current month and previous full months, minus one of minimum support
    final long numberOfFullMonthCharges = numberOfFullMonthsInPast90Days(now);
    final long expectedSupportCharges =
        getExpectedSupportChargesForPartialEndOfMonth(monthlyUpliftCents, startOfSupportPeriod);
    final String expectedChargesString =
        NumberFormatter.formatUsd(
            numberOfFullMonthCharges * monthlyUpliftCents
                + expectedSupportCharges
                + monthlyMinimumCharge);

    assertEquals(expectedChargesString, developerResponse.getString("estimatedCharges"));
  }

  /**
   * Calculates the portion of support charge expected on invoice if plan starts mid-month. This
   * function assumes that charges for all days are the same, which is the case when invoice is
   * created using {@link InvoiceFactory}.
   *
   * <p>To prevent rounding issues, it is recommended to use support charges that are evenly
   * divisible by the days in the month. This prevents the days at the end of the month having one
   * more cent of usage than previous days, which can cause rounding issues. Usage of $377,580 will
   * always be split evenly throughout the month. ($13,020 if you don't worry about leap years)
   *
   * @param totalMonthSupportCharge total amount of support charge expected if charged for the
   *     entire month
   * @param startOfSupportPeriod start day of the support plan and support charges
   * @return expected support charge in cents
   */
  long getExpectedSupportChargesForPartialEndOfMonth(
      final long totalMonthSupportCharge, final Date startOfSupportPeriod) {
    final Date startOfMonth = DateUtils.truncate(startOfSupportPeriod, Calendar.MONTH);
    final Date endOfMonth = DateUtils.addMonths(startOfMonth, 1);

    final long daysInMonth = dateRangeInUnits(startOfMonth, endOfMonth, TimeUnit.DAYS);
    final long daysOfSupport = dateRangeInUnits(startOfSupportPeriod, endOfMonth, TimeUnit.DAYS);

    return totalMonthSupportCharge * daysOfSupport / daysInMonth;
  }

  long dateRangeInUnits(final Date startDate, final Date endDate, final TimeUnit timeUnit) {
    final long rangeInMillis = endDate.getTime() - startDate.getTime();
    return timeUnit.convert(rangeInMillis, TimeUnit.MILLISECONDS);
  }

  void addPreviousMonthInvoicesToReactivationOrg(
      final Organization organization,
      final Date startDate,
      final Date endDate,
      final long usageAmount,
      final Status status) {
    _invoiceFactory.createInvoiceWithPayment(
        startDate,
        endDate,
        organization.getId(),
        0,
        usageAmount,
        0,
        status, // i == 0 ? Status.PENDING : Status.CLOSED,
        Payment.Status.CREATED,
        null);
  }

  long numberOfFullMonthsInPast90Days(final Date currentDay) {
    final YearMonth startOfCurrentMonth = YearMonth.from(DateTimeUtils.localDateOf(currentDay));
    final YearMonth startOf90DaysAgoMonth =
        YearMonth.from(DateTimeUtils.localDateOf(currentDay).minusDays(90));
    return ChronoUnit.MONTHS.between(startOf90DaysAgoMonth, startOfCurrentMonth) - 1;
  }

  Organization setupReactivationOrg(final PrepaidPlanType planType, final Date now) {
    final Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    final Date minus5Months = DateUtils.addMonths(startOfMonth, -5);
    final ObjectId orgId = ObjectId.get();

    final PrepaidPlan prepaidPlan =
        new PrepaidPlan.Builder(planType, List.of())
            .startDate(minus5Months)
            .endDate(minus5Months)
            .build();

    final OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(orgId)
            .planType(PlanType.NDS)
            .startDate(minus5Months)
            .activated(minus5Months)
            .build();

    Organization.Builder orgBuilder =
        new Organization.Builder()
            .id(orgId)
            .created(minus5Months)
            .name("SelfServeSupportResourceIntTest org")
            .groupType(GroupType.NDS);

    return _organizationFactory.createAtlasOrgWithPlan(orgBuilder, plan, List.of(prepaidPlan));
  }
}
