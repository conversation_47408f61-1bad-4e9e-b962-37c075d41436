package com.xgen.svc.mms.res.filter;

import static com.xgen.svc.mms.util.http.HttpUtils.gzip;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.logcollection.LogCollectionRequest;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import jakarta.ws.rs.core.HttpHeaders;
import java.io.IOException;
import java.util.Map;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.util.EntityUtils;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;

/**
 * Integration tests for gzip decompression filter. Uses log collection endpoint for submitting log
 * request.
 */
public class GzipDecompressFilterIntTests extends BaseResourceTest {

  private final ObjectMapper _objectMapper = CustomJacksonJsonProvider.createObjectMapper();
  private Group _cloudGroup;
  private AppUser _cloudGlobalMonitoringUser;

  @Before
  public void setUp() throws Exception {
    super.setUp();

    _cloudGroup = MmsFactory.createGroupWithStandardPlan();
    _cloudGlobalMonitoringUser =
        MmsFactory.createUser(
            _cloudGroup, "<EMAIL>", Sets.newHashSet(Role.GLOBAL_MONITORING_ADMIN));
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "automation/logcollection/automation_config_cloud_dev_2_shard_cluster.json.ftl",
        Map.of("group_id", _cloudGroup.getId().toHexString(), "config_id", new ObjectId()),
        AutomationConfig.DB_NAME,
        AutomationConfig.COLLECTION_NAME);
  }

  @Test
  public void gzipCorrectDataOk() throws Exception {
    // Submitting log collection request (POST with json gzipped data)
    final LogCollectionRequest logRequest =
        LogCollectionRequest.builder()
            .types(new LogCollectionType[] {LogCollectionType.MONGODB})
            .sizeInBytes(50_000)
            .build();
    final String stringJson = _objectMapper.writeValueAsString(logRequest);
    final byte[] gzippedContent = gzip(stringJson);

    submitLogCollectionRequest(gzippedContent, HttpStatus.SC_ACCEPTED);
  }

  @Test
  public void gzipIncorrectHttp400() throws Exception {
    // Some random set of bytes
    final byte[] gzippedContent = {0x35, 0x7A, 0x2F, 0x12, 0x5B, 0x73, 0x4C};

    final String responseBody =
        submitLogCollectionRequest(gzippedContent, HttpStatus.SC_BAD_REQUEST);
    assertThat(
        new JSONObject(responseBody).getString("errorCode"),
        equalTo(CommonErrorCode.GZIP_INCORRECT_ERROR.name()));
  }

  @Test
  public void gzipCorrectDataBroken() throws IOException, JSONException {
    final byte[] gzippedContent = gzip("hello");

    final String responseBody =
        submitLogCollectionRequest(gzippedContent, HttpStatus.SC_BAD_REQUEST);
    assertThat(
        new JSONObject(responseBody).getString("errorCode"),
        equalTo(CommonErrorCode.MALFORMED_JSON.name()));
  }

  private String submitLogCollectionRequest(
      final byte[] pGzippedContent, final int pHttpStatusExpected) throws IOException {
    final String path = "/log/collection/request/" + _cloudGroup.getId() + "/process/SyncStore_1";
    final String url = getResourceUrl(appendAuthToPath(_cloudGlobalMonitoringUser, path));

    final HttpPost request = new HttpPost(url);
    request.addHeader(HttpHeaders.CONTENT_ENCODING, "gzip");
    request.setEntity(new ByteArrayEntity(pGzippedContent, ContentType.APPLICATION_JSON));

    try (final CloseableHttpResponse response =
        HttpUtils.getInstance().getClient().execute(request)) {
      assertThat(response.getStatusLine().getStatusCode(), equalTo(pHttpStatusExpected));
      return EntityUtils.toString(response.getEntity());
    }
  }
}
