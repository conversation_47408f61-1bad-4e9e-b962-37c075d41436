package com.xgen.svc.mms.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.dao.ChartsConfigDao;
import com.xgen.svc.mms.model.ChartsConfig;
import com.xgen.svc.mms.model.ChartsConfig.Status;
import jakarta.inject.Inject;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class ChartsResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private ChartsConfigDao _chartsConfigDao;
  @Inject private AppSettings _appSettings;
  @Inject private UserDao _userDao;

  private Group _group;
  private Organization _organization;
  private AppUser _orgUser;
  private AppUser _groupUser;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _appSettings.setProp(
        "charts.centralUrl", "http://test.charts.mongodb.com", AppSettings.SettingType.MEMORY);
    _organization = MmsFactory.createOrganizationWithNDSPlan("chartsOrg");
    _orgUser = MmsFactory.createUserInOrg(_organization, "<EMAIL>");
    _group = MmsFactory.createGroup(_organization);
    _groupUser = MmsFactory.createUser(_group);
    // add active charts app to group
    final ChartsConfig chartsConfig = new ChartsConfig(_group.getId(), "test");
    _chartsConfigDao.save(chartsConfig);
    _chartsConfigDao.setStitchAppIds(_group.getId(), new ObjectId(), "testClientAppId");
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, _organization, FeatureFlag.CHARTS);
  }

  @Test
  public void testGetChartsConfig() {
    final ChartsConfig expected = _chartsConfigDao.findByGroupId(_group.getId());
    assertNotNull(expected);
    final JSONObject config = doAuthedJsonGet(_groupUser, "/charts/config/" + _group.getId());
    assertEquals(expected.getStatus().name(), config.getString("status"));
    assertEquals(expected.getStitchAppName(), config.getString("stitchAppName"));
    assertEquals(expected.getStitchAppId().toHexString(), config.getString("stitchAppId"));
    assertEquals(expected.getStitchClientAppId(), config.getString("stitchClientAppId"));
    assertFalse(config.has("lastException"));
    assertFalse(config.has("lastStackTrace"));
  }

  @Test
  public void testGetChartsConfig_activationFailed() {
    final Group failedGroup = MmsFactory.createGroup(_organization);
    final AppUser user = MmsFactory.createUser(failedGroup);
    final ChartsConfig chartsConfig = new ChartsConfig(failedGroup.getId(), "failed");
    _chartsConfigDao.save(chartsConfig);
    final ObjectId failedAppId = new ObjectId();
    _chartsConfigDao.setStitchAppIds(failedGroup.getId(), failedAppId, "failedClientAppId");
    _chartsConfigDao.markFailure(
        failedGroup.getId(), Status.ACTIVATION_FAILED, "BadThingHappened", "BadStackTrace");
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, _organization, FeatureFlag.CHARTS);

    final JSONObject config = doAuthedJsonGet(user, "/charts/config/" + failedGroup.getId());
    assertEquals(chartsConfig.getStitchAppName(), config.getString("stitchAppName"));
    assertEquals(failedAppId.toHexString(), config.getString("stitchAppId"));
    assertEquals("failedClientAppId", config.getString("stitchClientAppId"));
    assertEquals(Status.ACTIVATION_FAILED.name(), config.getString("status"));
    assertFalse(config.has("lastException"));
    assertFalse(config.has("lastStackTrace"));
  }

  @Test
  public void testChartsRedirect_noCharts() {
    final Group noChartsGroup = MmsFactory.createGroup(_organization, "noCharts");
    FeatureFlagIntTestUtil.enableFeatureForEntity(noChartsGroup, _organization, FeatureFlag.CHARTS);
    final AppUser user = MmsFactory.createUser(noChartsGroup, "<EMAIL>");

    // no params
    {
      final String redirectUrl = doAuthedGetRedirectUrl(user, "/charts/");
      assertEquals("/v2/" + noChartsGroup.getId() + "#/charts", redirectUrl);
    }

    // with database
    {
      final String redirectUrl =
          doAuthedGetRedirectUrl(user, "/charts?sourceType=cluster&name=cluster&database=db");
      assertEquals(
          "/v2/" + noChartsGroup.getId() + "#/charts?database=db&sourceType=cluster&name=cluster",
          redirectUrl);
    }

    // with collection
    {
      final String redirectUrl =
          doAuthedGetRedirectUrl(
              user, "/charts?sourceType=cluster&name=cluster&database=db&collection=coll");
      assertEquals(
          "/v2/"
              + noChartsGroup.getId()
              + "#/charts?database=db&sourceType=cluster&name=cluster&collection=coll",
          redirectUrl);
    }
  }

  @Test
  public void testChartsRedirect() {
    // group user
    {
      final String redirectUrl = doAuthedGetTempRedirectUrl(_groupUser, "/charts");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with database
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(
              _groupUser, "/charts?sourceType=cluster&name=cluster&database=db");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with collection
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(
              _groupUser, "/charts?sourceType=cluster&name=cluster&database=db&collection=coll");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with embedding
    {
      final String redirectUrl = doAuthedGetTempRedirectUrl(_groupUser, "/charts?embedding=true");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }
  }

  @Test
  public void testChartsRedirect_activationOptimizationFeature() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.CHARTS_ACTIVATION_OPTIMIZATION);

    // group user
    {
      final String redirectUrl = doAuthedGetTempRedirectUrl(_groupUser, "/charts");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with database
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(
              _groupUser, "/charts?sourceType=cluster&name=cluster&database=db");
      assertEquals(
          "http://test.charts.mongodb.com/testClientAppId/configure/data-source?database=db&sourceType=cluster&name=cluster",
          redirectUrl);
    }

    // with collection
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(
              _groupUser, "/charts?sourceType=cluster&name=cluster&database=db&collection=coll");
      assertEquals(
          "http://test.charts.mongodb.com/testClientAppId/configure/data-source?database=db&sourceType=cluster&name=cluster&collection=coll",
          redirectUrl);
    }

    // with embedding
    {
      final String redirectUrl = doAuthedGetTempRedirectUrl(_groupUser, "/charts?embedding=true");
      assertEquals("http://test.charts.mongodb.com/testClientAppId/atlas/embedding", redirectUrl);
    }
  }

  @Test
  public void testChartsRedirectByStitchClientAppId() {
    // stitchClientAppId
    chartsRedirectByStitchClientAppId("testClientAppId");
    // Falls back to Group Id
    chartsRedirectByStitchClientAppId(_group.getId().toString());
  }

  public void chartsRedirectByStitchClientAppId(final String id) {
    // group user
    {
      final String redirectUrl = doAuthedGetTempRedirectUrl(_groupUser, "/charts/" + id);
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // org user
    {
      final String redirectUrl = doAuthedGetTempRedirectUrl(_orgUser, "/charts/" + id);
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with database
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(
              _groupUser, "/charts/" + id + "?sourceType=cluster&name=cluster&database=db");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with collection
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(
              _groupUser,
              "/charts/" + id + "?sourceType=cluster&name=cluster&database=db&collection=coll");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }

    // with embedding
    {
      final String redirectUrl =
          doAuthedGetTempRedirectUrl(_groupUser, "/charts/" + id + "?embedding=true");
      assertEquals("http://test.charts.mongodb.com/testClientAppId", redirectUrl);
    }
  }

  @Test
  public void testChartsRedirectByGroupId_invalidGroup() {
    doAuthedGetBytes(_orgUser, "/charts/" + new ObjectId(), HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void testChartsRedirectByGroupId_wrongOrg() {
    final Group other = MmsFactory.createGroupWithNDSPlan("otherGroup");
    doAuthedGetBytes(_orgUser, "/charts/" + other.getId(), HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void testChartsRedirectByGroupId_noChartsConfig() {
    final Group noCharts = MmsFactory.createGroup(_organization, "noCharts");
    FeatureFlagIntTestUtil.enableFeatureForEntity(noCharts, _organization, FeatureFlag.CHARTS);
    final String redirectUrl = doAuthedGetRedirectUrl(_orgUser, "/charts/" + noCharts.getId());
    assertEquals("/v2/" + noCharts.getId() + "#/charts", redirectUrl);
  }

  @Test
  public void testActivateCharts() throws SvcException {
    // group data user forbidden
    final AppUser dataUser =
        MmsFactory.createUserWithRoleInGroup(
            _group, "data-" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_READ_ONLY);
    doAuthedJsonPost(dataUser, "/charts/activate/" + _group.getId(), null, HttpStatus.SC_FORBIDDEN);

    // group owner ok
    doAuthedJsonPost(_groupUser, "/charts/activate/" + _group.getId(), null, HttpStatus.SC_OK);

    // global owner ok
    final AppUser globalOwner =
        MmsFactory.createGlobalOwnerAdminUser(MmsFactory.createOrganizationWithNDSPlan("admin"));
    doAuthedJsonPost(globalOwner, "/charts/activate/" + _group.getId(), null, HttpStatus.SC_OK);
  }

  @Test
  public void testActivateCharts_activationOptimization() throws SvcException {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.CHARTS_ACTIVATION_OPTIMIZATION);

    // group data user ok
    final AppUser dataUser =
        MmsFactory.createUserWithRoleInGroup(
            _group, "data-" + getUniquifier() + "@example.com", Role.GROUP_DATA_ACCESS_READ_ONLY);
    doAuthedJsonPost(dataUser, "/charts/activate/" + _group.getId(), null, HttpStatus.SC_OK);
  }

  @Test
  public void testAdminGetTenants() {
    // existing tenant
    final ChartsConfig initial = _chartsConfigDao.findByGroupId(_group.getId());

    // add a running tenant
    final ChartsConfig running = new ChartsConfig(new ObjectId(), "runningTenant");
    _chartsConfigDao.save(running);
    _chartsConfigDao.setStatus(running.getGroupId(), Status.RUNNING);
    _chartsConfigDao.setVersion(running.getGroupId(), "testVersion");

    // add a failed tenant
    final ChartsConfig failed = new ChartsConfig(new ObjectId(), "failedTenant");
    _chartsConfigDao.save(failed);
    _chartsConfigDao.markFailure(
        failed.getGroupId(), Status.ACTIVATION_FAILED, "lastException", "lastStacktrace");
    _chartsConfigDao.setVersion(failed.getGroupId(), "testVersion");

    // add charts admin user
    final AppUser chartsAdmin =
        MmsFactory.createUser(_group, "<EMAIL>", Set.of(Role.GLOBAL_CHARTS_ADMIN));

    // forbidden for non-admin
    doAuthedJsonGet(_groupUser, "/charts/admin/tenants", HttpStatus.SC_FORBIDDEN);

    // charts admin can retrieve all tenants
    {
      final JSONObject tenants = doAuthedJsonGet(chartsAdmin, "/charts/admin/tenants");
      assertEquals(3, tenants.getInt("count"));

      assertEquals(
          Stream.of(initial, running, failed)
              .map(ChartsConfig::getGroupId)
              .map(ObjectId::toHexString)
              .collect(Collectors.toUnmodifiableSet()),
          convertJSONArrayToJSONObjectList(tenants.getJSONArray("tenantRecords")).stream()
              .map(t -> t.getString("groupId"))
              .collect(Collectors.toUnmodifiableSet()));
    }

    // filter by status
    {
      final JSONObject tenants =
          doAuthedJsonGet(chartsAdmin, "/charts/admin/tenants?status=ACTIVATION_FAILED");
      assertEquals(1, tenants.getInt("count"));
      final JSONObject failedTenant = (JSONObject) tenants.getJSONArray("tenantRecords").get(0);
      assertEquals(failed.getGroupId().toHexString(), failedTenant.getString("groupId"));
      assertEquals(Status.ACTIVATION_FAILED.name(), failedTenant.getString("status"));
      // admin view contains exception info
      assertEquals("lastException", failedTenant.getString("lastException"));
      assertEquals("lastStacktrace", failedTenant.getString("lastStackTrace"));
    }

    // filter by version
    {
      final JSONObject tenants =
          doAuthedJsonGet(chartsAdmin, "/charts/admin/tenants?version=testVersion");
      assertEquals(2, tenants.getInt("count"));
      assertEquals(
          Stream.of(running, failed)
              .map(ChartsConfig::getGroupId)
              .map(ObjectId::toHexString)
              .collect(Collectors.toUnmodifiableSet()),
          convertJSONArrayToJSONObjectList(tenants.getJSONArray("tenantRecords")).stream()
              .map(t -> t.getString("groupId"))
              .collect(Collectors.toUnmodifiableSet()));
    }

    // filter by both status and version
    {
      final JSONObject tenants =
          doAuthedJsonGet(
              chartsAdmin, "/charts/admin/tenants?status=ACTIVATION_FAILED&version=testVersion");
      assertEquals(1, tenants.getInt("count"));
      final JSONObject failedTenant = (JSONObject) tenants.getJSONArray("tenantRecords").get(0);
      assertEquals(failed.getGroupId().toHexString(), failedTenant.getString("groupId"));
      assertEquals(Status.ACTIVATION_FAILED.name(), failedTenant.getString("status"));
    }
  }
}
