package com.xgen.svc.mms.res;

import static com.xgen.cloud.group._public.model.GroupAddress.ADDRESS_LINE_ONE_FIELD;
import static com.xgen.cloud.group._public.model.GroupAddress.ADDRESS_LINE_TWO_FIELD;
import static com.xgen.cloud.group._public.model.GroupAddress.CITY_FIELD;
import static com.xgen.cloud.group._public.model.GroupAddress.COUNTRY_FIELD;
import static com.xgen.cloud.group._public.model.GroupAddress.STATE_FIELD;
import static com.xgen.svc.mms.res.view.LinkBraintreeCustomerView.BILLING_ADDRESS_FIELD;
import static com.xgen.svc.mms.res.view.LinkBraintreeCustomerView.PAYMENT_METHOD_NONCE_FIELD;
import static com.xgen.svc.mms.res.view.LinkBraintreeCustomerView.TYPE_FIELD;
import static com.xgen.svc.mms.svc.billing.BraintreeSvc.PAYPAL_ACCOUNT_TYPE;
import static com.xgen.svc.mms.svc.billing.BraintreeSvcTestUtils.FAKE_PAYPAL_EMAIL_ADDRESS;
import static com.xgen.svc.mms.svc.billing.BraintreeSvcTestUtils.FAKE_PAYPAL_NONCE;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.group._public.model.BillingAddress;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._private.svc.provider.UserSvcProvider;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseResourceTest;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.model.billing.BraintreeCustomer;
import com.xgen.svc.mms.svc.billing.BraintreeCustomerSvc;
import com.xgen.svc.mms.svc.billing.BraintreeSvc;
import com.xgen.svc.mms.svc.billing.BraintreeSvcTestUtils;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public final class BillingResourceThirdPartyTests extends BaseResourceTest {

  private static final String CREDIT_CARD_ACCOUNT_TYPE = "CreditCard";

  @Inject private BraintreeSvcTestUtils _braintreeSvcTestUtils;
  @Inject private BraintreeCustomerSvc _braintreeCustomerSvc;
  @Inject private BraintreeSvc _braintreeSvc;
  @Inject private UserSvcProvider _userSvcProvider;
  @Inject private UserDao _userDao;
  private UserSvc _userSvc;

  private Organization _organization;
  private AppUser _user;
  private ObjectId _nonExistingCustomerId;
  private Organization _organizationWithoutExistingCustomer;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _organization = MmsFactory.createOrganizationWithStandardPlan();
    _user =
        MmsFactory.createUserWithRoleInOrganization(
            _organization, "<EMAIL>", Role.ORG_OWNER);
    _organizationWithoutExistingCustomer = MmsFactory.createOrganizationWithNDSPlan();
    _nonExistingCustomerId = _organizationWithoutExistingCustomer.getId();

    _userSvc = _userSvcProvider.getUserSvcDB();
    _userSvc.addUserToOrganization(
        _user,
        _organizationWithoutExistingCustomer.getId(),
        List.of(Role.ORG_OWNER),
        MmsFactory.createAuditInfoWithAppUser());
    _braintreeSvcTestUtils.setUpVault(_organization.getId(), _nonExistingCustomerId);
  }

  @After
  public void tearDown() {
    _braintreeSvcTestUtils.tearDownVault();
  }

  @Test
  public void getClientToken_existingCustomer() {
    final String orgIdStr = _organization.getId().toString();
    final JSONObject result =
        doAuthedJsonGet(_user, "/billing/braintreeClientToken/" + orgIdStr, HttpStatus.SC_OK);

    assertThat(result.get("environment"), is("sandbox"));
    assertThat(result.get("clientToken"), is(notNullValue()));
    final JSONObject customer = result.getJSONObject("customer");
    assertThat(customer.get("orgId"), is(orgIdStr));
    assertThat(customer.get("paymentMethodToken"), is(notNullValue()));
    assertThat(customer.get("payPalEmailAddress"), is(notNullValue()));
  }

  @Test
  public void getClientToken_nonExistingCustomer() {
    final String orgIdStr = _nonExistingCustomerId.toString();
    final JSONObject result =
        doAuthedJsonGet(_user, "/billing/braintreeClientToken/" + orgIdStr, HttpStatus.SC_OK);

    assertThat(result.get("environment"), is("sandbox"));
    assertThat(result.get("clientToken"), is(notNullValue()));
    assertThat(result.has("customer"), is(false));
  }

  @Test
  public void linkBraintreeCustomer_wrongPaymentMethodType() {
    final String orgIdStr = _organization.getId().toString();
    final JSONObject linkBraintreeCustomerView =
        new JSONObject()
            .put(PAYMENT_METHOD_NONCE_FIELD, FAKE_PAYPAL_NONCE)
            .put(TYPE_FIELD, CREDIT_CARD_ACCOUNT_TYPE);
    final JSONObject response =
        doAuthedJsonPost(
            _user,
            "/billing/braintreeCustomer/" + orgIdStr,
            linkBraintreeCustomerView,
            HttpStatus.SC_BAD_REQUEST);
    assertThat(response.get("errorCode"), is("UNSUPPORTED_BRAINTREE_ACCOUNT_TYPE"));
  }

  @Test
  public void linkBraintreeCustomer_existingCustomer() {
    final String orgIdStr = _organization.getId().toString();
    final JSONObject linkBraintreeCustomerView = createBraintreeCustomerView();
    doAuthedJsonPost(
        _user,
        "/billing/braintreeCustomer/" + orgIdStr,
        linkBraintreeCustomerView,
        HttpStatus.SC_OK);

    final Optional<BraintreeCustomer> braintreeCustomer =
        _braintreeCustomerSvc.findByOrgId(_organization.getId());
    assertThat(braintreeCustomer.isPresent(), is(true));
    assertThat(braintreeCustomer.get().getOrgId(), is(_organization.getId()));
    assertThat(braintreeCustomer.get().getPaymentMethodToken(), is(notNullValue()));
    assertThat(braintreeCustomer.get().getPayPalEmailAddress(), is(FAKE_PAYPAL_EMAIL_ADDRESS));
  }

  @Test
  public void linkBraintreeCustomer_nonExistingCustomer() {
    final String orgIdStr = _nonExistingCustomerId.toString();
    final JSONObject linkBraintreeCustomerView = createBraintreeCustomerView();
    doAuthedJsonPost(
        _user,
        "/billing/braintreeCustomer/" + orgIdStr,
        linkBraintreeCustomerView,
        HttpStatus.SC_OK);
    final Optional<BraintreeCustomer> braintreeCustomer =
        _braintreeCustomerSvc.findByOrgId(_nonExistingCustomerId);
    assertThat(braintreeCustomer.isPresent(), is(true));
    assertThat(braintreeCustomer.get().getOrgId(), is(_nonExistingCustomerId));
    assertThat(braintreeCustomer.get().getPaymentMethodToken(), is(notNullValue()));
    assertThat(braintreeCustomer.get().getPayPalEmailAddress(), is(FAKE_PAYPAL_EMAIL_ADDRESS));
  }

  @Test
  public void linkBraintreeCustomer_creatingCustomerFailsDueToExistingCustomer() {
    final String orgIdStr = _nonExistingCustomerId.toString();
    _braintreeSvcTestUtils.createCustomer(_nonExistingCustomerId);
    final JSONObject linkBraintreeCustomerView = createBraintreeCustomerView();
    doAuthedJsonPost(
        _user,
        "/billing/braintreeCustomer/" + orgIdStr,
        linkBraintreeCustomerView,
        HttpStatus.SC_OK);
    final Optional<BraintreeCustomer> braintreeCustomer =
        _braintreeCustomerSvc.findByOrgId(_nonExistingCustomerId);
    assertThat(braintreeCustomer.isPresent(), is(true));
    assertThat(braintreeCustomer.get().getOrgId(), is(_nonExistingCustomerId));
    assertThat(braintreeCustomer.get().getPaymentMethodToken(), is(notNullValue()));
    assertThat(braintreeCustomer.get().getPayPalEmailAddress(), is(FAKE_PAYPAL_EMAIL_ADDRESS));
  }

  @Test
  public void braintreeDashboard_nonGlobalBillingAdmin() throws Exception {
    final String orgIdStr = _organization.getId().toString();

    doAuthedGetBytes(_user, "/billing/braintreeDashboard/" + orgIdStr, HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void braintreeDashboard_existingCustomer() {
    addGlobalBillingAdminRole();
    final String orgIdStr = _organization.getId().toString();
    final String redirectUrl =
        doAuthedGetTempRedirectUrl(_user, "/billing/braintreeDashboard/" + orgIdStr);
    assertThat(redirectUrl, is(_braintreeSvc.getCustomerPage(_organization.getId())));
  }

  @Test
  public void braintreeDashboard_nonExistingCustomer() {
    addGlobalBillingAdminRole();
    final String orgIdStr = _nonExistingCustomerId.toString();
    doAuthedHtmlGet(_user, "/billing/braintreeDashboard/" + orgIdStr, HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void getBraintreeCustomer_existingCustomer() {
    final String orgIdStr = _organization.getId().toString();
    final JSONObject result = doAuthedJsonGet(_user, "/billing/braintreeCustomer/" + orgIdStr);
    assertThat(result.get("orgId"), is(orgIdStr));
    assertThat(result.get("paymentMethodToken"), is(notNullValue()));
    assertThat(result.get("payPalEmailAddress"), is(notNullValue()));
  }

  @Test
  public void getBraintreeCustomer_nonExistingCustomer() {
    final String orgIdStr = _nonExistingCustomerId.toString();

    doAuthedGetReturnCloseableResponseTreatMeWithCare(
        _user, "/billing/braintreeCustomer/" + orgIdStr, HttpStatus.SC_NO_CONTENT);
  }

  private JSONObject createBraintreeCustomerView() {
    final BillingAddress billingAddress = _braintreeSvcTestUtils.getBillingAddress();
    return new JSONObject()
        .put(PAYMENT_METHOD_NONCE_FIELD, FAKE_PAYPAL_NONCE)
        .put(TYPE_FIELD, PAYPAL_ACCOUNT_TYPE)
        .put(
            BILLING_ADDRESS_FIELD,
            new JSONObject()
                .put(ADDRESS_LINE_ONE_FIELD, billingAddress.getAddressLineOne())
                .put(ADDRESS_LINE_TWO_FIELD, billingAddress.getAddressLineTwo())
                .put(CITY_FIELD, billingAddress.getCity())
                .put(STATE_FIELD, billingAddress.getState())
                .put(COUNTRY_FIELD, billingAddress.getCountry()));
  }

  private void addGlobalBillingAdminRole() {
    _user.assignRole(RoleAssignment.forGlobal(Role.GLOBAL_BILLING_ADMIN));
    _userDao.update(_user);
  }
}
