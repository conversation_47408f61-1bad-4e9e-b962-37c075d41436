package com.xgen.svc.mms.res.filter;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.authn._public.model.PartnerIdentityType;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelRole;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelContact;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanId;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserApiKeySvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.api.res.ApiJUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

/**
 * Integration tests for PartnerApiCallFilter. Tests that the filter correctly validates partner API
 * calls and enforces the @PartnerApiCall annotation requirements.
 */
@ExtendWith(GuiceTestExtension.class)
public class PartnerApiCallFilterIntTests extends ApiJUnit5BaseResourceTest {

  @Inject private AppSettings appSettings;
  @Inject private UserApiKeySvc userApiKeySvc;
  @Inject private VercelNativeInstallationsDaoSvc vercelInstallationsSvc;
  @Inject private VercelInstallationSvc billingInstallationsSvc;

  // Test endpoints - using actual Vercel Native endpoints that have @PartnerApiCall annotations
  private static final String VERCEL_INSTALLATION_BASE_PATH =
      "/api/private/vercelnative/v1/installations";
  private static final String TEST_INSTALLATION_ID = "42424242424242424242";
  private static final String FAKE_INSTALLATION_ID = "fake-id";
  private static final String FAKE_ACCESS_TOKEN = "fake-token";
  private static final String FAKE_ENCRYPTION_KEY =
      "32d0d9d5a364607645559305723a0d25f03092bfdbde8a5b199914183bb828d8";

  // Endpoints with different @PartnerApiCall configurations
  private static final String PUT_INSTALLATION_PATH =
      VERCEL_INSTALLATION_BASE_PATH + "/" + TEST_INSTALLATION_ID; // HUMAN_USER only
  private static final String GET_INSTALLATION_PATH =
      VERCEL_INSTALLATION_BASE_PATH + "/" + TEST_INSTALLATION_ID; // SYSTEM_USER only
  private static final String POST_RESOURCES_PATH =
      VERCEL_INSTALLATION_BASE_PATH + "/" + TEST_INSTALLATION_ID + "/resources"; // HUMAN_USER only

  // Non-partner API endpoint for testing regular users - will be set dynamically
  private String regularApiPath;

  private Organization testOrganization;
  private AppUser regularUser;
  private String regularUserToken;
  private String partnerHumanToken;
  private String partnerSystemToken;
  private VercelNativeInstallation testInstallation;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    // Create test organization and group
    testOrganization = MmsFactory.createOrganizationWithNDSPlan();

    // Set up regular API path using the group ID
    regularApiPath = "/api/atlas/v1.0/orgs/" + testOrganization.getId();

    // Create regular (non-partner) user
    regularUser =
        MmsFactory.createApiUserWithRoleInOrganization(
            "regular-user", testOrganization, Role.ORG_OWNER);
    regularUserToken = userApiKeySvc.findByUserId(regularUser.getId()).get(0).getKey();

    // Create partner API users with different identity types
    partnerHumanToken =
        createPartnerUserAndGetToken(
            testOrganization, Role.ORG_OWNER, PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER);
    partnerSystemToken =
        createPartnerUserAndGetToken(
            testOrganization, Role.ORG_OWNER, PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER);

    // Create test Vercel installation with atlas resources
    AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(testOrganization.getId())
            .installedProducts(List.of())
            .build();

    // Create VercelData with installation ID
    VercelData vercelData =
        VercelData.builder()
            .installationId(TEST_INSTALLATION_ID)
            .accessToken(FAKE_ACCESS_TOKEN, FAKE_ENCRYPTION_KEY)
            .teamName("test-team")
            .installationUrl("https://vercel.com/test")
            .contact(VercelContact.builder().name("Test User").email("<EMAIL>").build())
            .acceptedPolicies(Map.of("privacy", "accepted"))
            .build();

    testInstallation =
        VercelNativeInstallation.builder()
            .vercelData(vercelData)
            .atlasResources(atlasResources)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    vercelInstallationsSvc.upsertInstallationDb(TEST_INSTALLATION_ID, testInstallation);
  }

  @AfterEach
  public void tearDown() {
    appSettings.clearMemory();
  }

  /**
   * Helper method to create a partner API user with the specified role and identity type, and
   * return the authentication token.
   */
  private String createPartnerUserAndGetToken(
      Organization org, Role role, PartnerIdentityType identityType) {
    AppUser partnerUser =
        MmsFactory.createPartnerApiUserWithRoleInOrganization(
            "partner-user-" + UUID.randomUUID(), org, role);
    return getAuthnBackedPartnerToken(partnerUser, identityType);
  }

  private void initBillingInstallation() {
    // Not consistently needed for all endpoints, so don't always setup
    VercelInstallation billingInstallation =
        VercelInstallation.builder()
            .installationId(TEST_INSTALLATION_ID)
            .orgId(testOrganization.getId())
            .accessTokenEncrypted(FAKE_ACCESS_TOKEN, FAKE_ENCRYPTION_KEY)
            .tokenType("Bearer")
            .billingPlanId(VercelBillingPlanId.FREE)
            .installationUrl("https://vercel.com/test")
            .build();
    billingInstallationsSvc.upsert(billingInstallation);
  }

  // Tests for endpoints that require HUMAN_USER identity type only

  @Test
  public void testPutInstallation_withHumanUserToken_shouldSucceed() {
    // PUT /installations/{id} requires VERCEL_NATIVE_HUMAN_USER;
    initBillingInstallation();

    // Should get past the filter (may fail later due to missing data, but filter should pass)
    HttpUtils.getInstance()
        .put()
        .path(PUT_INSTALLATION_PATH)
        .data(getPutRequestBody())
        .initiatorAuth(partnerHumanToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                VercelRole.ADMIN,
                TEST_INSTALLATION_ID))
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();
  }

  @Test
  public void testPutInstallation_withSystemUserToken_shouldFail() {
    // PUT /installations/{id} requires VERCEL_NATIVE_HUMAN_USER, but we're using SYSTEM_USER
    initBillingInstallation();

    HttpUtils.getInstance()
        .put()
        .path(PUT_INSTALLATION_PATH)
        .data(getPutRequestBody())
        .initiatorAuth(partnerSystemToken)
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testPutInstallation_withRegularUser_shouldFail() {
    // PUT /installations/{id} requires partner API user, but we're using regular user
    initBillingInstallation();

    doDigestJsonPut(
        PUT_INSTALLATION_PATH,
        getPutRequestBody(),
        HttpStatus.SC_FORBIDDEN,
        regularUser.getUsername(),
        regularUserToken);
  }

  // Tests for endpoints that require SYSTEM_USER identity type only

  @Test
  public void testGetInstallation_withSystemUserToken_shouldSucceed() {
    // GET /installations/{id} requires VERCEL_NATIVE_SYSTEM_USER
    initBillingInstallation();

    HttpUtils.getInstance()
        .get()
        .path(GET_INSTALLATION_PATH)
        .initiatorAuth(partnerSystemToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER,
                VercelRole.ADMIN,
                TEST_INSTALLATION_ID))
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();
  }

  @Test
  public void testGetInstallation_withHumanUserToken_shouldFail() {
    // GET /installations/{id} requires VERCEL_NATIVE_SYSTEM_USER, but we're using HUMAN_USER
    initBillingInstallation();

    HttpUtils.getInstance()
        .get()
        .path(GET_INSTALLATION_PATH)
        .initiatorAuth(partnerHumanToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                VercelRole.ADMIN,
                TEST_INSTALLATION_ID))
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testGetInstallation_withRegularUser_shouldFail() {
    // GET /installations/{id} requires partner API user, but we're using regular user
    initBillingInstallation();

    doDigestJsonGet(
        GET_INSTALLATION_PATH,
        HttpStatus.SC_FORBIDDEN,
        regularUser.getUsername(),
        regularUserToken);
  }

  // Tests for endpoints that allow multiple identity types

  @Test
  public void testDeleteInstallation_withHumanUserToken_shouldSucceed() {
    // DELETE /installations/{id} allows both HUMAN_USER and SYSTEM_USER

    // Should get past the filter (may fail later due to missing data, but filter should pass)
    HttpUtils.getInstance()
        .delete()
        .path(VERCEL_INSTALLATION_BASE_PATH + "/" + FAKE_INSTALLATION_ID) // use fake id to get 404
        .initiatorAuth(partnerHumanToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                VercelRole.ADMIN,
                FAKE_INSTALLATION_ID))
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_NOT_FOUND)
        .send();
  }

  @Test
  public void testDeleteInstallation_withSystemUserToken_shouldSucceed() {
    // DELETE /installations/{id} allows both HUMAN_USER and SYSTEM_USER

    // Should get past the filter (may fail later due to missing data, but filter should pass)
    HttpUtils.getInstance()
        .delete()
        .path(VERCEL_INSTALLATION_BASE_PATH + "/" + FAKE_INSTALLATION_ID) // use fake id to get 404
        .initiatorAuth(partnerSystemToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER,
                VercelRole.ADMIN,
                FAKE_INSTALLATION_ID))
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_NOT_FOUND)
        .send();
  }

  @Test
  public void testDeleteInstallation_withRegularUser_shouldFail() {
    // DELETE /installations/{id} requires partner API user, but we're using regular user

    doDigestJsonDelete(
        VERCEL_INSTALLATION_BASE_PATH + "/fake-id",
        HttpStatus.SC_FORBIDDEN,
        regularUser.getUsername(),
        regularUserToken);
  }

  // Tests for resource provisioning endpoint

  @Test
  public void testProvisionResource_withHumanUserToken_shouldSucceed() {
    // POST /installations/{id}/resources requires VERCEL_NATIVE_HUMAN_USER
    JSONObject requestBody =
        new JSONObject().put("resourceType", "CLUSTER").put("name", "test-cluster");

    // Should get past the filter (may fail later due to missing data, but filter should pass)
    HttpUtils.getInstance()
        .post()
        .path(POST_RESOURCES_PATH)
        .data(requestBody)
        .initiatorAuth(partnerHumanToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                VercelRole.ADMIN,
                TEST_INSTALLATION_ID))
        .returnType(JSONObject.class)
        // Should fail with 400 because we're not actually creating a cluster, so just test that
        // we got past the filter
        .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
        .send();
  }

  @Test
  public void testProvisionResource_withSystemUserToken_shouldFail() {
    // POST /installations/{id}/resources requires VERCEL_NATIVE_HUMAN_USER, but we're using
    // SYSTEM_USER
    JSONObject requestBody =
        new JSONObject().put("resourceType", "CLUSTER").put("name", "test-cluster");

    HttpUtils.getInstance()
        .post()
        .path(POST_RESOURCES_PATH)
        .data(requestBody)
        .initiatorAuth(partnerSystemToken)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER,
                VercelRole.ADMIN,
                TEST_INSTALLATION_ID))
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testProvisionResource_withRegularUser_shouldFail() {
    // POST /installations/{id}/resources requires partner API user, but we're using regular user
    JSONObject requestBody =
        new JSONObject().put("resourceType", "CLUSTER").put("name", "test-cluster");

    doDigestJsonPost(
        POST_RESOURCES_PATH,
        requestBody,
        HttpStatus.SC_FORBIDDEN,
        regularUser.getUsername(),
        regularUserToken);
  }

  // Tests for regular API endpoints (should reject partner users)

  @Test
  public void testRegularApiEndpoint_withRegularUser_shouldSucceed() {
    // Regular API endpoints should work with regular users
    JSONObject response =
        doDigestJsonGet(
            regularApiPath, HttpStatus.SC_OK, regularUser.getUsername(), regularUserToken);
    assertNotNull(response);
  }

  @Test
  public void testRegularApiEndpoint_withPartnerHumanUser_shouldFail() {
    // Regular API endpoints should reject partner users (bidirectional enforcement)
    HttpUtils.getInstance()
        .get()
        .path(regularApiPath)
        .initiatorAuth(partnerHumanToken)
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testRegularApiEndpoint_withPartnerSystemUser_shouldFail() {
    // Regular API endpoints should reject partner users (bidirectional enforcement)
    HttpUtils.getInstance()
        .get()
        .path(regularApiPath)
        .initiatorAuth(partnerSystemToken)
        .returnType(JSONObject.class)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  // Test filter disabled scenario

  @Test
  public void testFilterDisabled_partnerUserCanAccessRegularEndpoint() {
    // Disable the filter
    appSettings.setProp("iam.partnerApiCallFilter.enabled", "false", SettingType.MEMORY);

    // When filter is disabled, partner users should be able to access regular endpoints
    JSONObject response =
        HttpUtils.getInstance()
            .get()
            .path(regularApiPath)
            .initiatorAuth(partnerHumanToken)
            .returnType(JSONObject.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();
    assertNotNull(response);

    // Re-enable for other tests
    appSettings.setProp("iam.partnerApiCallFilter.enabled", "true", SettingType.MEMORY);
  }

  private JSONObject getPutRequestBody() {
    return new JSONObject()
        .put("account", new JSONObject().put("name", "test"))
        .put("credentials", new JSONObject().put("accessToken", "test"))
        .put("acceptedPolicies", new JSONObject().put("privacy", "accepted"));
  }
}
