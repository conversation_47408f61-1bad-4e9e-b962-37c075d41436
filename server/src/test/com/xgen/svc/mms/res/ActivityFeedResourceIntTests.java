package com.xgen.svc.mms.res;

import static com.xgen.cloud.appconfig._public.config.AppConfig.getInstance;
import static com.xgen.svc.common.TestDataUtils.getParamsWithEncryptedIntegrationKeys;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._private.dao.AlertConfigHistoryDao;
import com.xgen.cloud.activity._public.model.alert.Alert;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfig;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfigHistory;
import com.xgen.cloud.activity._public.model.alert.config.HostAlertConfig;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.alerts.alert._public.model.MaintenanceWindowConfig;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.constants._public.model.communication.PagerdutyRegion;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.common.resource._public.model.ResourceId.ResourceType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.activity.GroupAlert;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.integration._public.model.PagerdutyIntegration;
import com.xgen.cloud.integration._public.model.credentials.PagerdutyCredentials;
import com.xgen.cloud.integration._public.model.helpers.IntegrationUtils;
import com.xgen.cloud.monitoring.agent._public.model.alert.AgentAlertConfig;
import com.xgen.cloud.monitoring.agent._public.model.alert.AgentAlertConfig.Builder;
import com.xgen.cloud.monitoring.agent._public.model.event.AgentEvent.Type;
import com.xgen.cloud.notification._public.model.Notification;
import com.xgen.cloud.notification._public.model.PagerDutyNotification;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient;
import com.xgen.cloud.payments.standalone.common._public.gateway.PaymentMethodGateway;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseResourceTestCommon;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public final class ActivityFeedResourceIntTests extends BaseResourceTestCommon {
  public static final String ID_FIELD = "id";
  private static final String SERVICE_KEY = "********************************";
  private static final String REDACTED_SERVICE_KEY =
      IntegrationUtils.getRedactedString(SERVICE_KEY);
  private static final String INVALID_SERVICE_KEY = "12342";
  @Inject NDSClusterSvc _clusterSvc;
  @Inject AutomationMongoDbVersionSvc _versionSvc;
  @Inject private AlertConfigHistoryDao _alertConfigHistoryDao;
  private GroupSvc _groupSvc;
  private Organization _organization;
  private Group _group;
  private Team _team;
  private AppUser _user;
  private AppUser _monitoringAdminUser;
  private Alert _alert;
  private AlertConfig _alertConfig;
  private PaymentMethodClient paymentMethodClient;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _clusterSvc.start();
    _groupSvc = MmsFactory.getGroupSvc();
    _organization = MmsFactory.createOrganizationWithStandardPlan();
    _group = MmsFactory.createGroup(_organization, "Test");
    _team = MmsFactory.createTeam(_organization, "teamone");

    final GroupAlert.Builder alertBuilder = new GroupAlert.Builder();
    alertBuilder.id(new ObjectId());
    alertBuilder.groupId(_group.getId());
    alertBuilder.orgId(_organization.getId());
    _alert = alertBuilder.build();

    final ObjectId alertConfigId = new ObjectId();
    final AgentAlertConfig.Builder alertConfigBuilder =
        new AgentAlertConfig.Builder(Type.MONITORING_AGENT_DOWN, alertConfigId);
    alertConfigBuilder.orgId(_organization.getId());
    alertConfigBuilder.groupId(_group.getId());
    _alertConfig = alertConfigBuilder.build();

    _user = MmsFactory.createUser(_group);
    _monitoringAdminUser = MmsFactory.createMonitoringAdminUser(_group);

    MmsFactory.addUserToTeam(_user, _team);
    MmsFactory.addTeamToGroup(_team, _group);
    MmsFactory.addUserWithRoleInOrganization(_organization, _user, Role.ORG_OWNER);

    final Map<String, Object> params =
        new HashMap<>(Map.of("groupId", String.valueOf(_group.getId()), "hostId", "abc123"));
    params.putAll(getParamsWithEncryptedIntegrationKeys());
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/event/EventDao/events.json.ftl", params, Event.DB_NAME, Event.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/alert/AlertDao/alerts_for_resource.json.ftl",
        params,
        Alert.DB_NAME,
        Alert.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/alert/config/AlertConfigDao/alertConfigsWithEncryptedIntegrationKeys.json.ftl",
        params,
        AlertConfig.DB_NAME,
        AlertConfig.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/window/MaintenanceWindowConfigDao/windowConfigs.json.ftl",
        params,
        MaintenanceWindowConfig.DB_NAME,
        MaintenanceWindowConfig.COLLECTION_NAME);

    paymentMethodClient = spy(getInstance(PaymentMethodClient.class));
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(PaymentMethodGateway.class),
        "paymentMethodClient",
        paymentMethodClient);
    stubPaymentMethod(_organization.getId());
  }

  @Test
  @Ignore(
      "Skipping this test for now because cross-service int tests are not supported. Communication"
          + " Service.")
  public void testNotification() {
    final JSONObject notification = getPagerDutyNotification(SERVICE_KEY);
    doAuthedJsonPost(
        _user, "/activity/testNotification/" + _group.getId(), notification, HttpStatus.SC_OK);
  }

  @Test
  @Ignore(
      "Skipping this test for now because cross-service int tests are not supported. Communication"
          + " Service.")
  public void testNotification_redactedNotification() {
    final JSONObject redactedNotification = getPagerDutyNotification(REDACTED_SERVICE_KEY);
    _groupSvc.setDefaultIntegration(
        _group,
        new PagerdutyIntegration(
            new ObjectId(),
            new ResourceId(ResourceType.PROJECT, _group.getId()),
            true,
            new PagerdutyCredentials(SERVICE_KEY, PagerdutyRegion.US),
            Instant.now(),
            Instant.now()));
    doAuthedJsonPost(
        _user,
        "/activity/testNotification/" + _group.getId(),
        redactedNotification,
        HttpStatus.SC_OK);
  }

  @Test
  @Ignore(
      "Skipping this test for now because cross-service int tests are not supported. Communication"
          + " Service.")
  public void testExistingNotification() {
    final JSONObject alertConfig =
        getAlertConfigWithNotifications(List.of(getPagerDutyNotification(SERVICE_KEY)));
    final JSONObject createAlertConfigResponse = createAlertConfigAndGetResponse(alertConfig);
    final String alertConfigId = createAlertConfigResponse.getString("id");
    final JSONObject notification =
        createAlertConfigResponse.getJSONArray(AlertConfig.NOTIFY_FIELD).getJSONObject(0);
    final String notificationId = notification.getString("id");
    assertNotNull(notificationId);

    doAuthedJsonPost(
        _user,
        String.format(
            "/activity/testNotification/%s/%s/%s", _group.getId(), alertConfigId, notificationId),
        getPagerDutyNotification(SERVICE_KEY),
        HttpStatus.SC_OK);
  }

  @Test
  @Ignore(
      "Skipping this test for now because cross-service int tests are not supported. Communication"
          + " Service.")
  public void testExistingNotification_redactedNotification() {
    final JSONObject alertConfig =
        getAlertConfigWithNotifications(List.of(getPagerDutyNotification(SERVICE_KEY)));
    final JSONObject createAlertConfigResponse = createAlertConfigAndGetResponse(alertConfig);
    final String alertConfigId = createAlertConfigResponse.getString("id");
    final JSONObject notification =
        createAlertConfigResponse.getJSONArray(AlertConfig.NOTIFY_FIELD).getJSONObject(0);
    final String notificationId = notification.getString("id");
    assertNotNull(notificationId);

    doAuthedJsonPost(
        _user,
        String.format(
            "/activity/testNotification/%s/%s/%s", _group.getId(), alertConfigId, notificationId),
        getPagerDutyNotification(REDACTED_SERVICE_KEY),
        HttpStatus.SC_OK);
  }

  @Test
  public void testNotification_incorrectServiceKey_throws400() {
    final JSONObject redactedNotification = getPagerDutyNotification(INVALID_SERVICE_KEY);
    _groupSvc.setDefaultIntegration(
        _group,
        new PagerdutyIntegration(
            new ObjectId(),
            new ResourceId(ResourceType.PROJECT, _group.getId()),
            true,
            new PagerdutyCredentials(SERVICE_KEY, PagerdutyRegion.US),
            Instant.now(),
            Instant.now()));
    doAuthedJsonPost(
        _user,
        "/activity/testNotification/" + _group.getId(),
        redactedNotification,
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Ignore(
      "Skipping this test for now because cross-service int tests are not supported. Communication"
          + " Service.")
  public void testNotification_unredactedServiceKey_worksWhenRegionIsSwitched() {
    final JSONObject redactedNotification =
        getPagerDutyNotification(SERVICE_KEY, PagerdutyRegion.EU);
    _groupSvc.setDefaultIntegration(
        _group,
        new PagerdutyIntegration(
            new ObjectId(),
            new ResourceId(ResourceType.PROJECT, _group.getId()),
            true,
            new PagerdutyCredentials(SERVICE_KEY, PagerdutyRegion.US),
            Instant.now(),
            Instant.now()));
    doAuthedJsonPost(
        _user,
        "/activity/testNotification/" + _group.getId(),
        redactedNotification,
        HttpStatus.SC_OK);
  }

  @Test
  @Ignore(
      "Skipping this test for now because cross-service int tests are not supported. Communication"
          + " Service.")
  public void testNotification_redactedServiceKey_worksWhenRegionIsSwitched() {
    final JSONObject redactedNotification =
        getPagerDutyNotification(REDACTED_SERVICE_KEY, PagerdutyRegion.EU);
    _groupSvc.setDefaultIntegration(
        _group,
        new PagerdutyIntegration(
            new ObjectId(),
            new ResourceId(ResourceType.PROJECT, _group.getId()),
            true,
            new PagerdutyCredentials(SERVICE_KEY, PagerdutyRegion.US),
            Instant.now(),
            Instant.now()));
    doAuthedJsonPost(
        _user,
        "/activity/testNotification/" + _group.getId(),
        redactedNotification,
        HttpStatus.SC_OK);
  }

  @Test
  public void testExistingNotification_incorrectServiceKey_throws400() {
    final JSONObject alertConfig =
        getAlertConfigWithNotifications(List.of(getPagerDutyNotification(SERVICE_KEY)));
    final JSONObject createAlertConfigResponse = createAlertConfigAndGetResponse(alertConfig);
    final String alertConfigId = createAlertConfigResponse.getString("id");
    final JSONObject notification =
        createAlertConfigResponse.getJSONArray(AlertConfig.NOTIFY_FIELD).getJSONObject(0);
    final String notificationId = notification.getString("id");
    assertNotNull(notificationId);

    doAuthedJsonPost(
        _user,
        String.format(
            "/activity/testNotification/%s/%s/%s", _group.getId(), alertConfigId, notificationId),
        getPagerDutyNotification(INVALID_SERVICE_KEY),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testGetAlerts() {
    buildAlertConfigHistory(_alertConfig);
    JSONArray resp =
        doAuthedJsonArrayGet(
            _user, String.format("/activity/alertConfigs/%s", _group.getId()), HttpStatus.SC_OK);

    assertEquals(16, resp.length());
  }

  @Test
  public void testGetAlertConfigHistory() {
    buildAlertConfigHistory(_alertConfig);
    var resp =
        doAuthedJsonGet(
            _user,
            String.format(
                "/activity/alertConfigs/history/%s/%s", _group.getId(), _alertConfig.getId()),
            HttpStatus.SC_OK);

    assertEquals(_alertConfig.getId().toString(), resp.get("acid"));
  }

  @Test
  public void testGetAlertConfigHistory_unauthorized() {
    final AlertConfig secondAlertConfig =
        new Builder(Type.MONITORING_AGENT_DOWN, ObjectId.get())
            .groupId(ObjectId.get())
            .orgId(ObjectId.get())
            .build();

    buildAlertConfigHistory(secondAlertConfig);
    doAuthedJsonGet(
        _user,
        String.format(
            "/activity/alertConfigs/history/%s/%s", _group.getId(), secondAlertConfig.getId()),
        HttpStatus.SC_UNAUTHORIZED);
  }

  private JSONObject createAlertConfigAndGetResponse(final JSONObject alertConfig) {
    final JSONObject createAlertConfigResponse =
        doAuthedJsonPost(_user, "/activity/alertConfigs/" + _group.getId(), alertConfig);
    assertNotNull(createAlertConfigResponse.get("id"));
    return createAlertConfigResponse;
  }

  private JSONObject getAlertConfigWithNotifications(final List<JSONObject> notifications) {
    final JSONObject alertConfig = new JSONObject();
    alertConfig.put(AlertConfig.TYPE_FIELD, HostAlertConfig.TYPE_NAME);
    alertConfig.put(AlertConfig.GROUP_ID_FIELD, _group.getId().toString());
    alertConfig.put(AlertConfig.ENABLED_FIELD, "true");
    alertConfig.put(AlertConfig.EVENT_TYPE_FIELD, HostEvent.Type.HOST_DOWN.name());
    final JSONArray matchers = new JSONArray();
    alertConfig.put(AlertConfig.MATCHERS_FIELD, matchers);
    alertConfig.put(AlertConfig.NOTIFY_FIELD, notifications);
    return alertConfig;
  }

  private JSONObject getPagerDutyNotification(final String serviceKey) {
    return getPagerDutyNotification(serviceKey, PagerdutyRegion.US);
  }

  private JSONObject getPagerDutyNotification(
      final String serviceKey, final PagerdutyRegion region) {
    final JSONObject notification = new JSONObject();
    notification.put(Notification.TYPE_FIELD, PagerDutyNotification.TYPE);
    notification.put(Notification.DELAY_FIELD, 0);
    final JSONObject integration = new JSONObject();
    integration.put("_t", "PAGER_DUTY");
    integration.put(PagerdutyCredentials.SERVICE_KEY_FIELD, serviceKey);
    integration.put(PagerdutyCredentials.REGION_FIELD, region);
    notification.put(Notification.INTEGRATION_FIELD, integration);
    return notification;
  }

  private void buildAlertConfigHistory(final AlertConfig alertConfig) {
    final AlertConfigHistory.Builder historyBuilder = new AlertConfigHistory.Builder();
    final AlertConfigHistory history =
        historyBuilder
            .groupId(alertConfig.getGroupId())
            .alertConfigId(alertConfig.getId())
            .deleted(false)
            .build();
    _alertConfigHistoryDao.save(history);
  }

  private void stubPaymentMethod(final ObjectId orgId) {
    doReturn(
            Optional.of(
                PaymentMethod.builder()
                    .type(PaymentMethodType.CREDIT_CARD)
                    .orgId(orgId)
                    .billingAccount(BillingAccount.MONGODB_INC)
                    .status(PaymentMethod.Status.ACTIVE)
                    .build()))
        .when(paymentMethodClient)
        .getActivePaymentMethod(eq(orgId), anyBoolean());
  }
}
