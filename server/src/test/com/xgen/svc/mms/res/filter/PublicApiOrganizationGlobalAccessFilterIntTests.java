package com.xgen.svc.mms.res.filter;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.svc.NDSOrgSvc;
import java.util.Date;
import java.util.Set;
import org.apache.http.HttpStatus;
import org.junit.Before;
import org.junit.Test;

public class PublicApiOrganizationGlobalAccessFilterIntTests extends ApiBaseResourceTest {

  private OrganizationSvc organizationSvc;
  private UserDao userDao;
  private Organization restrictedOrg;
  private Organization unrestrictedOrg;

  private static final String PATH_UNRESTRICTED =
      "/api/atlas/v1.0/fake/customerOrgGlobalRestriction/unrestricted/%s";
  private static final String PATH_RESTRICTED =
      "/api/atlas/v1.0/fake/customerOrgGlobalRestriction/restricted/%s";

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    userDao = AppConfig.getInstance(UserDao.class);
    organizationSvc = AppConfig.getInstance(OrganizationSvc.class);
    final NDSOrgSvc NDSOrgSvc = AppConfig.getInstance(NDSOrgSvc.class);

    restrictedOrg =
        NDSOrgSvc.createOrganization(
            "Restricted Org",
            GroupType.NDS,
            userDao.findById(API_ONLY_USER_ID),
            new Date(),
            AuditInfoHelpers.fromInternal());
    organizationSvc.setRestrictEmployeeAccess(
        restrictedOrg.getId(), true, AuditInfoHelpers.fromInternal());
    userDao.addOrgRoleAssignments(
        API_ONLY_USER_USERNAME, restrictedOrg.getId(), Set.of(Role.ORG_OWNER));

    unrestrictedOrg =
        NDSOrgSvc.createOrganization(
            "Unrestricted Org",
            GroupType.NDS,
            userDao.findById(API_ONLY_USER_ID),
            new Date(),
            AuditInfoHelpers.fromInternal());
  }

  @Test
  public void testUnrestrictedEndpoint_restrictedOrg_allowsGlobalAccess() {
    getHttpUtils()
        .get()
        .path(PATH_UNRESTRICTED, restrictedOrg.getId())
        .digestAuth(API_ONLY_USER_USERNAME, API_ONLY_USER_API_KEY)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    getHttpUtils()
        .get()
        .path(PATH_UNRESTRICTED, restrictedOrg.getId())
        .digestAuth(API_ONLY_GLOBAL_USER_USERNAME, API_ONLY_GLOBAL_USER_API_KEY)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();
  }

  @Test
  public void testRestrictedEndpoint_restrictedOrg_deniesGlobalAccess() {
    getHttpUtils()
        .get()
        .path(PATH_RESTRICTED, restrictedOrg.getId())
        .digestAuth(API_ONLY_USER_USERNAME, API_ONLY_USER_API_KEY)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    getHttpUtils()
        .get()
        .path(PATH_RESTRICTED, restrictedOrg.getId())
        .digestAuth(API_ONLY_GLOBAL_USER_USERNAME, API_ONLY_GLOBAL_USER_API_KEY)
        .expectedApiErrorCode(
            HttpStatus.SC_FORBIDDEN, ApiErrorCode.ORG_ACCESS_REQUIRES_CUSTOMER_GRANT)
        .send();
  }

  @Test
  public void testRestrictedEndpoint_unrestrictedOrg_allowsGlobalAccess() {
    getHttpUtils()
        .get()
        .path(PATH_RESTRICTED, unrestrictedOrg.getId())
        .digestAuth(API_ONLY_USER_USERNAME, API_ONLY_USER_API_KEY)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    getHttpUtils()
        .get()
        .path(PATH_RESTRICTED, unrestrictedOrg.getId())
        .digestAuth(API_ONLY_GLOBAL_USER_USERNAME, API_ONLY_GLOBAL_USER_API_KEY)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();
  }
}
