package com.xgen.svc.mms.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.mongodb.DBObject;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.Host.DataSource;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.model.performanceadvisor.SlowQueryLogIngestionMetadata;
import jakarta.inject.Inject;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class SlowQueryLogIngestionMetadataSvcIntTests extends BaseSvcTest {

  @Inject private SlowQueryLogIngestionMetadataSvc _metadataSvc;
  @Inject private HostDao _hostDao;
  @Inject private HostSvc _hostSvc;

  private Group _group;
  private HostCluster _dedicatedCluster;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    final Organization _org = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_org);
    _dedicatedCluster =
        createReplSet(_group.getId(), "dedicatedreplset", DataSource.MONITORING_AGENT);
    createReplSet(_group.getId(), "sharedreplset", DataSource.PROXY);
    final Group otherGroup = MmsFactory.createGroup(_org);
    createReplSet(otherGroup.getId(), "dedicatedreplset", DataSource.MONITORING_AGENT);
    createReplSet(otherGroup.getId(), "sharedreplset", DataSource.PROXY);
  }

  @Test
  public void testInsertMetadatasForGroup() {
    // test
    _metadataSvc.insertMetadatasForGroup(_group.getId(), true);

    // verify
    final List<SlowQueryLogIngestionMetadata> metadatas = _metadataSvc.findAllMetadatas();
    assertEquals(2, metadatas.size());
    assertEquals(2, _dedicatedCluster.getHosts().size());
    _dedicatedCluster
        .getHosts()
        .forEach(
            host ->
                assertTrue(
                    metadatas.stream()
                        .anyMatch(
                            metadata ->
                                metadata.getGroupId().equals(host.getGroupId())
                                    && metadata
                                        .getHostnameAndPort()
                                        .equals(host.getHostnameAndPort()))));
  }

  private HostCluster createReplSet(
      final ObjectId pGroupId, final String pName, final DataSource pDataSource) {
    final HostCluster replSet = MmsFactory.createReplicaSet_V_4_2(pGroupId, pName, false);
    final List<Host> hosts = _hostSvc.findHostsByIds(replSet, false);
    hosts.forEach(host -> host.setDataSource(pDataSource));
    final List<DBObject> dbObjects = _hostDao.toMorphiaDBObjects(hosts);
    dbObjects.forEach(dbObject -> _hostDao.saveReplicaSafe(dbObject));
    replSet.setHosts(hosts);
    return replSet;
  }
}
