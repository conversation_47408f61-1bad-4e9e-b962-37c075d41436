package com.xgen.svc.mms.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.activity._public.model.alert.config.AlertConfig;
import com.xgen.cloud.alerts.alert._private.dao.AlertConfigDao;
import com.xgen.cloud.alerts.notify._private.dao.ExternalEventTrackDao;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.email._public.svc.EmailSvc;
import com.xgen.cloud.email._public.svc.template.HandlebarsTemplateSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.agent._public.model.alert.AgentAlertConfig;
import com.xgen.cloud.monitoring.agent._public.model.event.AgentEvent;
import com.xgen.cloud.notification._public.model.SlackNotification;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.svc.ping.NewRelicMessagingSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class ExternalFailureTrackerSvcIntTests extends BaseSvcTest {
  private static final ObjectId GROUP_ID = oid(103);
  public static final String NEW_RELIC_TO_INCREMENT_ID =
      NewRelicMessagingSvc.EXTERNAL_FAILURE_TRACKER_ID_PREFIX + GROUP_ID.toString();

  @Inject private ExternalEventTrackDao _externalFailureTrackDao;
  @Inject private MockExternalFailureTrackerSvc _mockExternalFailureTrackerSvc;
  @Inject private GroupDao _groupDao;
  @Inject private AlertConfigDao _alertConfigDao;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
  }

  @Test
  public void testNewRelicEmails() throws Exception {
    Date now = new Date();
    final Duration twoDays = Duration.ofDays(2);

    _groupDao.setDefaultNewRelicSettings(GROUP_ID, "test1", "test2", "test3", "test4");

    now = DateUtils.addMinutes(now, -5);
    _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    _mockExternalFailureTrackerSvc.warnAboutNewRelicFailures();
    _mockExternalFailureTrackerSvc.disableNewRelicFailures();
    assertEquals(false, _mockExternalFailureTrackerSvc.wasNewRelicWarningEmailSent());
    assertEquals(false, _mockExternalFailureTrackerSvc.wasNewRelicDisabledEmailSent());
    _externalFailureTrackDao.clear(NEW_RELIC_TO_INCREMENT_ID);

    now = DateUtils.addHours(now, -1);
    _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    for (int i = 0; i < 30; i++) {
      _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    }

    Thread.sleep(100); // UNACKNOWLEDGED increment writes
    _mockExternalFailureTrackerSvc.warnAboutNewRelicFailures();
    _mockExternalFailureTrackerSvc.disableNewRelicFailures();
    assertEquals(true, _mockExternalFailureTrackerSvc.wasNewRelicWarningEmailSent());
    assertEquals(false, _mockExternalFailureTrackerSvc.wasNewRelicDisabledEmailSent());
    _externalFailureTrackDao.clear(NEW_RELIC_TO_INCREMENT_ID);

    now = DateUtils.addHours(now, -10);
    _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    for (int i = 0; i < 30 * 10; i++) {
      _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    }

    Thread.sleep(100); // UNACKNOWLEDGED increment writes
    _mockExternalFailureTrackerSvc.warnAboutNewRelicFailures();
    _mockExternalFailureTrackerSvc.disableNewRelicFailures();

    assertEquals(true, _mockExternalFailureTrackerSvc.wasNewRelicWarningEmailSent());
    assertEquals(false, _mockExternalFailureTrackerSvc.wasNewRelicDisabledEmailSent());
    _mockExternalFailureTrackerSvc.warnAboutNewRelicFailures();
    _mockExternalFailureTrackerSvc.disableNewRelicFailures();
    assertEquals(false, _mockExternalFailureTrackerSvc.wasNewRelicWarningEmailSent());
    assertEquals(false, _mockExternalFailureTrackerSvc.wasNewRelicDisabledEmailSent());
    _externalFailureTrackDao.clear(NEW_RELIC_TO_INCREMENT_ID);

    Group group = _groupDao.findById(GROUP_ID);
    assertTrue(group.hasNewRelicIntegration());

    now = DateUtils.addHours(now, -13);
    _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    for (int i = 0; i < 30 * 24; i++) {
      _externalFailureTrackDao.increment(NEW_RELIC_TO_INCREMENT_ID, now, twoDays);
    }

    Thread.sleep(100); // UNACKNOWLEDGED increment writes
    _mockExternalFailureTrackerSvc.warnAboutNewRelicFailures();
    _mockExternalFailureTrackerSvc.disableNewRelicFailures();
    assertEquals(true, _mockExternalFailureTrackerSvc.wasNewRelicWarningEmailSent());
    assertEquals(true, _mockExternalFailureTrackerSvc.wasNewRelicDisabledEmailSent());
    assertEquals(false, _externalFailureTrackDao.containsId(NEW_RELIC_TO_INCREMENT_ID));

    group = _groupDao.findById(GROUP_ID);
    assertFalse(group.hasNewRelicIntegration());
  }

  @Test
  public void testAlertNotificationEmails() throws Exception {
    Date now = new Date();
    final Duration twoDays = Duration.ofDays(2);

    final AgentAlertConfig.Builder alertBuilder =
        new AgentAlertConfig.Builder(AgentEvent.Type.MONITORING_AGENT_DOWN, ObjectId.get());

    alertBuilder.groupId(GROUP_ID);
    final SlackNotification notification =
        new SlackNotification("slackApiToken", "slackChannelName", 1, 2);
    final String trackId = notification.getExternalFailureTrackerId();
    alertBuilder.notification(notification);
    final AgentAlertConfig config = alertBuilder.build();
    _alertConfigDao.save(config);

    now = DateUtils.addMinutes(now, -5);
    _externalFailureTrackDao.increment(trackId, now, twoDays);
    _mockExternalFailureTrackerSvc.warnAboutAlertNotificationFailures();
    _mockExternalFailureTrackerSvc.disableAlertNotificationFailures();
    assertFalse(_mockExternalFailureTrackerSvc.wasAlertsWarningEmailSent());
    assertFalse(_mockExternalFailureTrackerSvc.wasAlertsDisabledEmailSent());
    _externalFailureTrackDao.clear(trackId);

    now = DateUtils.addHours(now, -1);
    _externalFailureTrackDao.increment(trackId, now, twoDays);
    for (int i = 0; i < 30; i++) {
      _externalFailureTrackDao.increment(trackId, now, twoDays);
    }

    Thread.sleep(100); // UNACKNOWLEDGED increment writes
    _mockExternalFailureTrackerSvc.warnAboutAlertNotificationFailures();
    _mockExternalFailureTrackerSvc.disableAlertNotificationFailures();
    assertTrue(_mockExternalFailureTrackerSvc.wasAlertsWarningEmailSent());
    assertFalse(_mockExternalFailureTrackerSvc.wasAlertsDisabledEmailSent());
    _externalFailureTrackDao.clear(trackId);

    now = DateUtils.addHours(now, -10);
    _externalFailureTrackDao.increment(trackId, now, twoDays);
    for (int i = 0; i < 30 * 10; i++) {
      _externalFailureTrackDao.increment(trackId, now, twoDays);
    }

    Thread.sleep(100); // UNACKNOWLEDGED increment writes
    _mockExternalFailureTrackerSvc.warnAboutAlertNotificationFailures();
    _mockExternalFailureTrackerSvc.disableAlertNotificationFailures();
    assertTrue(_mockExternalFailureTrackerSvc.wasAlertsWarningEmailSent());
    assertFalse(_mockExternalFailureTrackerSvc.wasAlertsDisabledEmailSent());
    _mockExternalFailureTrackerSvc.warnAboutAlertNotificationFailures();
    _mockExternalFailureTrackerSvc.disableAlertNotificationFailures();
    assertFalse(_mockExternalFailureTrackerSvc.wasAlertsWarningEmailSent());
    assertFalse(_mockExternalFailureTrackerSvc.wasAlertsDisabledEmailSent());
    _externalFailureTrackDao.clear(trackId);

    List<AlertConfig> configs = _alertConfigDao.findByGroupId(GROUP_ID);
    assertEquals(1, configs.size());
    AlertConfig alertConfig = configs.iterator().next();
    assertEquals(1, alertConfig.getNotifications().size());
    assertFalse(alertConfig.isDisabled());

    now = DateUtils.addHours(now, -13);
    _externalFailureTrackDao.increment(trackId, now, twoDays);
    for (int i = 0; i < 30 * 24; i++) {
      _externalFailureTrackDao.increment(trackId, now, twoDays);
    }

    Thread.sleep(100); // UNACKNOWLEDGED increment writes
    _mockExternalFailureTrackerSvc.warnAboutAlertNotificationFailures();
    _mockExternalFailureTrackerSvc.disableAlertNotificationFailures();
    assertTrue(_mockExternalFailureTrackerSvc.wasAlertsWarningEmailSent());
    assertTrue(_mockExternalFailureTrackerSvc.wasAlertsDisabledEmailSent());
    assertFalse(_externalFailureTrackDao.containsId(trackId));

    configs = _alertConfigDao.findByGroupId(GROUP_ID);
    assertEquals(1, configs.size());
    alertConfig = configs.iterator().next();
    assertTrue(alertConfig.getNotifications().isEmpty());
    assertTrue(alertConfig.isDisabled());
  }

  private static class MockExternalFailureTrackerSvc extends ExternalFailureTrackSvc {
    private boolean _newRelicWarningEmailSent = false;
    private boolean _newRelicDisabledEmailSent = false;
    private boolean _alertsWarningEmailSent = false;
    private boolean _alertsDisabledEmailSent = false;

    @Inject
    public MockExternalFailureTrackerSvc(
        final AppSettings pAppSettings,
        final EmailSvc pEmailSvc,
        final HandlebarsTemplateSvc pHandlebarsTemplateSvc,
        final GroupSvc pGroupSvc,
        final AlertNotificationExternalFailureSpecifics pAlertNotificationExternalFailureSpecifics,
        final NewRelicExternalFailureSpecifics pNewRelicExternalFailureSpecifics) {
      super(
          pAppSettings,
          pEmailSvc,
          pHandlebarsTemplateSvc,
          pGroupSvc,
          pAlertNotificationExternalFailureSpecifics,
          pNewRelicExternalFailureSpecifics);
    }

    @Override
    public void warnAboutNewRelicFailures() {
      _newRelicWarningEmailSent = false;
      super.warnAboutNewRelicFailures();
    }

    @Override
    public void disableNewRelicFailures() {
      _newRelicDisabledEmailSent = false;
      super.disableNewRelicFailures();
    }

    @Override
    public void warnAboutAlertNotificationFailures() {
      _alertsWarningEmailSent = false;
      super.warnAboutAlertNotificationFailures();
    }

    @Override
    public void disableAlertNotificationFailures() {
      _alertsDisabledEmailSent = false;
      super.disableAlertNotificationFailures();
    }

    @Override
    void sendEmail(
        final ObjectId pGroupId,
        final boolean isWarning,
        final ExternalFailureSpecifics pExternalFailureSpecifics)
        throws Exception {
      if (pExternalFailureSpecifics instanceof NewRelicExternalFailureSpecifics) {
        if (isWarning) {
          _newRelicWarningEmailSent = true;
        } else {
          _newRelicDisabledEmailSent = true;
        }
      } else if (pExternalFailureSpecifics instanceof AlertNotificationExternalFailureSpecifics) {
        if (isWarning) {
          _alertsWarningEmailSent = true;
        } else {
          _alertsDisabledEmailSent = true;
        }
      }
    }

    private boolean wasNewRelicWarningEmailSent() {
      return _newRelicWarningEmailSent;
    }

    private boolean wasNewRelicDisabledEmailSent() {
      return _newRelicDisabledEmailSent;
    }

    private boolean wasAlertsWarningEmailSent() {
      return _alertsWarningEmailSent;
    }

    private boolean wasAlertsDisabledEmailSent() {
      return _alertsDisabledEmailSent;
    }
  }
}
