package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.process.orchestration._private.svc.schedule.BillingSchedulerImpl;
import com.xgen.cloud.billingplatform.process.orchestration._public.model.lastbilldate.LastBillDateForOrg;
import com.xgen.cloud.billingplatform.process.orchestration._public.svc.lastbilldate.LastBillDateForOrgSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.server.utils.testFactories.MeterUsageTestFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.PayingOrg;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.LineItemFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class RebillAfterSuccessfulPaymentSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private RebillAfterSuccessfulPaymentsSvc rebillSvc;

  @Inject private AppSettings appSettings;

  @Inject private OrganizationFactory organizationFactory;
  @Inject private InvoiceSvc invoiceSvc;
  @Inject private LineItemDao lineItemDao;
  @Inject private LastBillDateForOrgSvc lastBillDateForOrgSvc;
  @Inject private CreditFactory creditFactory;
  @Inject private JobQueueTestUtils jobQueueTestUtils;
  @Inject private InvoiceDao invoiceDao;
  @Inject private LineItemFactory lineItemFactory;

  @Test
  public void rebillOrganizationAsync() throws Exception {
    Date startDate = TimeUtils.fromISOString("2023-09-01");
    Date endDate = DateUtils.addDays(startDate, 5);
    Organization parentOrg = setupOrgForTest(startDate, endDate);
    Organization linkedOrg1 = setupOrgForTest(startDate, endDate);
    Organization linkedOrg2 = setupOrgForTest(startDate, endDate);
    Organization linkedOrg3 = setupOrgForTest(startDate, endDate);
    Invoice parentInvoice = invoiceDao.findPendingMonthlyByOrgId(parentOrg.getId());
    lineItemFactory.generateCreditLineItem(parentInvoice, 4000L, startDate, ObjectId.get(), false);

    save(
        PayingOrg.builder()
            .payingOrgId(parentOrg.getId())
            .linkedOrgIds(List.of(linkedOrg1.getId(), linkedOrg2.getId(), linkedOrg3.getId()))
            .build());

    appSettings.setProperty(BillingSchedulerImpl.DAILY_BILLING_AD_HOC_BATCH_SIZE_PROP, 1);

    for (Organization org : List.of(linkedOrg1, linkedOrg2, linkedOrg3, parentOrg)) {
      Invoice invoice = invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());
      assertEquals(0, lineItemDao.getCreditOnlyLineItems(invoice.getId()).size());
      assertNull(lastBillDateForOrgSvc.findByOrgId(org.getId()));
    }

    rebillSvc.rebillOrganization(parentOrg, DateUtils.addHours(endDate, 10));

    jobQueueTestUtils.processAllJobsInQueue();

    for (Organization org : List.of(linkedOrg1, linkedOrg2, linkedOrg3, parentOrg)) {
      Invoice invoice = invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());

      List<LineItem> creditLineItems = lineItemDao.getCreditOnlyLineItems(invoice.getId());
      assertEquals(5, creditLineItems.size());
      LastBillDateForOrg lastBillDateForOrg = lastBillDateForOrgSvc.findByOrgId(org.getId());
      assertEquals(DateUtils.addDays(endDate, -1), lastBillDateForOrg.getLastBillDateForCredit());
      assertEquals(DateUtils.addDays(endDate, -1), lastBillDateForOrg.getLastBillDate());

      long usageCharges =
          lineItemDao.calcNonSupportCharges(
              List.of(invoice.getId()), invoice.getStartDate(), invoice.getEndDate());
      assertEquals(5590, usageCharges);
    }

    appSettings.setProperty(BillingSchedulerImpl.DAILY_BILLING_AD_HOC_BATCH_SIZE_PROP, null);
  }

  private Organization setupOrgForTest(Date startDate, Date endDate) {
    Organization organization = organizationFactory.createAtlasOrganization(startDate);
    Group group = organizationFactory.createNdsGroup(organization, startDate);

    Date current = new Date(startDate.getTime());
    while (current.before(endDate)) {
      Date currentEnd = DateUtils.addDays(current, 1);
      MeterUsage usage =
          MeterUsageTestFactory.createRawAzureSnapshotMeterUsage(
              current,
              group.getId(),
              "clusterName",
              1000,
              DateUtils.addHours(currentEnd, 1),
              currentEnd);

      save(usage);

      current = currentEnd;
    }

    creditFactory.createPrepaidCredit(organization.getId(), startDate, endDate, 1000000L, false);

    return organization;
  }
}
