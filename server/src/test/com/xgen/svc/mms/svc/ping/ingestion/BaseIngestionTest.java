package com.xgen.svc.mms.svc.ping.ingestion;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.common.db.legacy._public.cursor.CustomMorphiaObjectFactory;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.util._public.encoding.PooledEncodingUtils;
import com.xgen.cloud.common.util._public.json.JsonUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.topology._private.dao.CanonicalHostDao;
import com.xgen.cloud.monitoring.topology._private.dao.HostClusterDao;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.monitoring.topology._private.dao.HostDeletedCacheDao;
import com.xgen.cloud.monitoring.topology._public.model.CanonicalHost;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.cloud.monitoring.topology._public.model.ping.PingUtils;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.svc.ping.PingRequest;
import com.xgen.svc.mms.svc.ping.PingRequestFactory;
import dev.morphia.Datastore;
import dev.morphia.Morphia;
import dev.morphia.mapping.Mapper;
import dev.morphia.mapping.MapperOptions;
import jakarta.inject.Inject;
import java.io.IOException;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.zip.DataFormatException;
import org.apache.commons.lang.StringUtils;
import org.bson.BSONObject;
import org.bson.BasicBSONObject;
import org.bson.types.BasicBSONList;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.json.JSONObject;

public class BaseIngestionTest extends BaseSvcTest {

  static final BasicDBObject CANONICAL_HOST_SORT_ORDER =
      new BasicDBObject(CanonicalHost.UNIQUE_HASH_FIELD, 1);

  static final Comparator<Host> HOST_COMPARATOR =
      (o1, o2) -> o1.getHostnameAndPort().compareToIgnoreCase(o2.getHostnameAndPort());

  /**
   * Provides deterministic iteration order for HostClusters. Does not include Cluster name or
   * clusterId in ordering as the logic/iteration order when assigning cluster names isn't
   * well-defined. Therefore can only compare based on consistent cluster values.
   */
  static final Comparator<HostCluster> HOST_CLUSTER_COMPARATOR =
      (o1, o2) -> {
        int cmp = o1.getTypeCode() - o2.getTypeCode();
        if (cmp == 0) {
          cmp = Boolean.compare(o1.isActive(), o2.isActive());
        }
        if (cmp == 0) {
          cmp =
              String.CASE_INSENSITIVE_ORDER.compare(
                  String.valueOf(o1.getParentClusterId()), String.valueOf(o2.getParentClusterId()));
        }
        if (cmp == 0) {
          cmp =
              new TreeSet<>(o1.getReplicaSetIds())
                  .toString()
                  .compareTo(new TreeSet<>(o2.getReplicaSetIds()).toString());
        }
        if (cmp == 0) {
          cmp =
              new TreeSet<>(o1.getShardIds())
                  .toString()
                  .compareTo(new TreeSet<>(o2.getShardIds()).toString());
        }
        if (cmp == 0) {
          cmp =
              new TreeSet<>(o1.getHostIds())
                  .toString()
                  .compareTo(new TreeSet<>(o2.getHostIds()).toString());
        }
        return cmp;
      };

  /** Provides deterministic iteration order for NodeCluster model objects. */
  static final Comparator<NodeCluster> NODE_CLUSTER_COMPARATOR =
      (o1, o2) -> {
        int cmp = String.valueOf(o1.getId()).compareTo(String.valueOf(o2.getId()));
        if (cmp == 0) {
          cmp =
              String.valueOf(o1.getParentClusterId())
                  .compareTo(String.valueOf(o2.getParentClusterId()));
        }
        if (cmp == 0) {
          cmp =
              String.valueOf(new TreeSet<>(o1.getReplicaSetIds()))
                  .compareTo(String.valueOf(new TreeSet<>(o2.getReplicaSetIds())));
        }
        if (cmp == 0) {
          cmp =
              String.valueOf(new TreeSet<>(o1.getShardIds()))
                  .compareTo(String.valueOf(new TreeSet<>(o2.getShardIds())));
        }
        if (cmp == 0) {
          cmp =
              String.valueOf(new TreeSet<>(o1.getClusterTypes()))
                  .compareTo(String.valueOf(new TreeSet<>(o1.getClusterTypes())));
        }
        if (cmp == 0) {
          final Set<String> nodePreferredHostnameAndPorts1 = new TreeSet<>();
          for (final Node n : o1.getNodes()) {
            nodePreferredHostnameAndPorts1.add(n.getPreferredHostnameAndPort());
          }
          final Set<String> nodePreferredHostnameAndPorts2 = new TreeSet<>();
          for (final Node n : o2.getNodes()) {
            nodePreferredHostnameAndPorts2.add(n.getPreferredHostnameAndPort());
          }
          cmp =
              String.valueOf(nodePreferredHostnameAndPorts1)
                  .compareTo(String.valueOf(nodePreferredHostnameAndPorts2));
        }
        if (cmp == 0) {
          cmp = o1.getPossiblyTheSameClusters().size() - o2.getPossiblyTheSameClusters().size();
        }
        return cmp;
      };

  @Inject PingRequestFactory _pingRequestFactory;

  @Inject GroupSvc _groupSvc;

  @Inject CanonicalHostDao _canonicalHostDao;

  @Inject HostDao _hostDao;

  @Inject HostClusterDao _hostClusterDao;

  @Inject HostDeletedCacheDao _hostDeletedCacheDao;

  protected final Morphia _morphia;

  @SuppressWarnings("rawtypes")
  public BaseIngestionTest() {
    _morphia =
        new Morphia(
            new Mapper(MapperOptions.legacy().build()),
            new HashSet<Class>(Arrays.asList(Host.class, HostCluster.class)));
    final MapperOptions mapperOptions =
        MapperOptions.legacy()
            .objectFactory(new CustomMorphiaObjectFactory(Collections.emptySet()))
            .storeEmpties(true)
            .build();
    _morphia.getMapper().setOptions(mapperOptions);
  }

  protected Morphia getMorphia() {
    return _morphia;
  }

  protected void populateIngestionCollectionsFromJsonFiles(
      final ObjectId pGroupId,
      final String pExistingCanonicalHostsFilename,
      final String pExistingHostClustersFilename,
      final String pExistingHostsFilename,
      final String pExistingDeleteHostsFilename)
      throws Exception {
    final Group group = newTestGroup(pGroupId);

    populateIngestionCollectionsFromJsonFiles(
        group,
        pExistingCanonicalHostsFilename,
        pExistingHostClustersFilename,
        pExistingHostsFilename,
        pExistingDeleteHostsFilename);
  }

  protected void populateIngestionCollectionsFromJsonFiles(
      final Group pGroup,
      final String pExistingCanonicalHostsFilename,
      final String pExistingHostClustersFilename,
      final String pExistingHostsFilename,
      final String pExistingDeleteHostsFilename)
      throws Exception {
    _groupSvc.save(pGroup);

    if (pExistingCanonicalHostsFilename != null) {
      TestDataUtils.populateCollectionFromJsonFile(
          pExistingCanonicalHostsFilename, CanonicalHost.DB_NAME, CanonicalHost.COLLECTION_NAME);
    }

    if (pExistingHostClustersFilename != null) {
      TestDataUtils.populateCollectionFromJsonFile(
          pExistingHostClustersFilename, HostCluster.DB_NAME, HostCluster.COLLECTION_NAME);
    }

    if (pExistingHostsFilename != null) {
      TestDataUtils.populateCollectionFromJsonFile(
          pExistingHostsFilename, HostDao.DB_NAME, HostDao.COLLECTION_NAME);
    }

    if (pExistingDeleteHostsFilename != null) {
      if (pExistingDeleteHostsFilename.endsWith(".ftl")) {
        TestDataUtils.populateCollectionFromJsonFtlFile(
            pExistingDeleteHostsFilename, null, "mmsdbpings", "config.hostDelectedCache");
      } else {
        TestDataUtils.populateCollectionFromJsonFile(
            pExistingDeleteHostsFilename, "mmsdbpings", "config.hostDelectedCache");
      }
    }

    _hostDeletedCacheDao.invalidateAll();
  }

  protected Organization newTestOrganization() {
    return new Organization.Builder().name("Test").build();
  }

  protected Group newTestGroup(final ObjectId pGroupId) {
    final Group group = new Group();
    group.setId(pGroupId);
    group.setName("test");
    group.setDiscoveryIncludesFQDN(true);
    return group;
  }

  protected PingRequest newTestPingRequest(final ObjectId pGroupId, final BasicBSONObject pPing) {
    return newTestPingRequest(newTestOrganization(), newTestGroup(pGroupId), pPing);
  }

  protected PingRequest newTestPingWithTimeRequest(
      final ObjectId pGroupId, final BasicBSONObject pPing, final long pTime) {
    return newTestPingWithTimeRequest(newTestOrganization(), newTestGroup(pGroupId), pPing, pTime);
  }

  protected PingRequest newTestPingRequest(
      final Organization pOrganization, final Group pGroup, final BasicBSONObject pPing) {
    // JM: taking local time from a host as wall clock time ping was received.
    final Calendar mockWallClockTimePingReceived =
        extractLatestHostLocalTime(PingUtils.extractHosts(pPing));
    TimeUtils2.addSeconds(mockWallClockTimePingReceived, 60);

    return _pingRequestFactory.newPingRequest(
        null, pPing, pOrganization, pGroup, mockWallClockTimePingReceived, "127.0.0.1");
  }

  protected PingRequest newTestPingWithTimeRequest(
      final Organization pOrganization,
      final Group pGroup,
      final BasicBSONObject pPing,
      final long time) {
    final Calendar someTime = Calendar.getInstance();
    someTime.setTimeInMillis(time);
    return _pingRequestFactory.newPingRequest(
        null, pPing, pOrganization, pGroup, someTime, "127.0.0.1");
  }

  protected List<NodeCluster> findOrderedExpectedNodeClustersfromJsonFile(
      final ObjectId pGroupId, final String pFilename) throws Exception {
    final List<BasicDBObject> nodeClusterBsons = loadBsonObjectsFromJsonFile(pFilename);

    final List<NodeCluster> nodeClusters = new ArrayList<>(nodeClusterBsons.size());
    for (final BasicDBObject obj : nodeClusterBsons) {
      nodeClusters.add(NodeCluster.fromDbObj(pGroupId, obj));
    }

    nodeClusters.sort(NODE_CLUSTER_COMPARATOR);
    return nodeClusters;
  }

  protected List<BasicDBObject> findExpectedCanonicalHostsfromJsonFile(final String pFilename)
      throws Exception {
    final List<BasicDBObject> canonicalHosts = loadBsonObjectsFromJsonFile(pFilename);
    canonicalHosts.sort(Comparator.comparing(o -> o.getString(CanonicalHost.UNIQUE_HASH_FIELD)));
    return canonicalHosts;
  }

  protected List<Host> findOrderedExpectedHostsfromFtlJsonFile(
      final String pFilename, final Map<String, Object> pParams) {
    final List<Host> hosts = new ArrayList<>();

    final Datastore datastore = _hostDao.getMongoSvc().getMorphiaDs(_hostDao.getDbName());
    final List<BasicDBObject> hostBsons = loadBsonObjectsFromFtlJsonFile(pFilename, pParams);
    for (final BasicDBObject hostBson : hostBsons) {
      hosts.add(getMorphia().fromDBObject(datastore, Host.class, hostBson));
    }

    hosts.sort(HOST_COMPARATOR);
    return hosts;
  }

  protected List<HostCluster> findOrderedExpectedHostClustersfromJsonFile(final String pFilename)
      throws Exception {
    final List<BasicDBObject> hostClusterBsons = loadBsonObjectsFromJsonFile(pFilename);
    return orderAndConvertBsonToHostClusters(hostClusterBsons);
  }

  protected List<HostCluster> findOrderedExpectedHostClustersfromFtlJsonFile(
      final String pFilename, final Map<String, Object> pFtlParams) {
    final List<BasicDBObject> hostClusterBsons =
        loadBsonObjectsFromFtlJsonFile(pFilename, pFtlParams);
    return orderAndConvertBsonToHostClusters(hostClusterBsons);
  }

  private List<HostCluster> orderAndConvertBsonToHostClusters(
      final List<BasicDBObject> pHostClusterBsons) {
    final Datastore datastore =
        _hostClusterDao.getMongoSvc().getMorphiaDs(_hostClusterDao.getDbName());
    final List<HostCluster> hostClusters = new ArrayList<>();
    for (final BasicDBObject hostClusterBson : pHostClusterBsons) {
      hostClusters.add(getMorphia().fromDBObject(datastore, HostCluster.class, hostClusterBson));
    }

    hostClusters.sort(HOST_CLUSTER_COMPARATOR);
    return hostClusters;
  }

  protected List<HostCluster> findOrderedActualHostClustersFromDB(final ObjectId pGroupId) {
    final List<HostCluster> hostClusters = _hostClusterDao.findByGroupId(pGroupId);

    hostClusters.sort(HOST_CLUSTER_COMPARATOR);
    return hostClusters;
  }

  protected List<BasicDBObject> findOrderedActualCanonicalHostsFromDB(final ObjectId pGroupId) {
    return DbUtils.toList(
        _canonicalHostDao
            .getDbCollection()
            .find(new BasicDBObject(CanonicalHost.GROUP_ID_FIELD, pGroupId))
            .sort(CANONICAL_HOST_SORT_ORDER));
  }

  protected List<Host> findOrderedActualHostsFromDB(final ObjectId pGroupId) {
    final List<Host> hosts = _hostDao.findByGroupIds(Collections.singletonList(pGroupId));

    hosts.sort(HOST_COMPARATOR);
    return hosts;
  }

  static Calendar extractLatestHostLocalTime(final BasicBSONObject pHosts) {
    Date latest = new Date(0);
    for (final BasicBSONObject pHost : PingUtils.extractHostList(pHosts)) {
      final BasicBSONObject serverStatus = PingUtils.extractServerStatus(pHost);
      if (serverStatus != null && serverStatus.containsField(Ping.LOCAL_TIME.field)) {
        final Date localTime = serverStatus.getDate(Ping.LOCAL_TIME.field);
        if (localTime.after(latest)) {
          latest = localTime;
        }
      }
    }

    if (latest.getTime() == 0L) {
      throw new IllegalStateException("Could not determine any local time from ping fixture");
    }

    return TimeUtils2.cal(latest.getTime());
  }

  protected static void assertNodesEqual(final Node pExpected, final Node pActual)
      throws IOException, DataFormatException {
    final List<Difference> differences = findDifferences(pExpected, pActual, true);
    if (!differences.isEmpty()) {
      fail("Node differences detected for node " + pExpected.getFirstHostId() + '\n' + differences);
    }
  }

  @SuppressWarnings("rawtypes")
  protected static void assertNodeClustersEqual(
      final List<NodeCluster> pExpectedNodeClusters, final List<NodeCluster> pActualNodeClusters) {
    assertEquals(
        "regression: wrong node cluster obj count",
        pExpectedNodeClusters.size(),
        pActualNodeClusters.size());
    for (int i = 0; i < pExpectedNodeClusters.size(); i++) {
      final NodeCluster expected = pExpectedNodeClusters.get(i);
      final NodeCluster actual = pActualNodeClusters.get(i);
      assertEquals("regression: node cluster mismatch: (" + i + ")", expected, actual);
    }
  }

  @SuppressWarnings("rawtypes")
  protected static void assertCanonicalHostsEqual(
      final List<BasicDBObject> pExpectedCanonicalHosts,
      final List<BasicDBObject> pActualCanonicalHosts) {
    assertEquals(
        "regression: wrong canonical host obj count",
        pExpectedCanonicalHosts.size(),
        pActualCanonicalHosts.size());
    for (int i = 0; i < pExpectedCanonicalHosts.size(); i++) {
      final Map expected = pExpectedCanonicalHosts.get(i).toMap();
      final Map actual = pActualCanonicalHosts.get(i).toMap();
      assertEquals("regression: canonical host mismatch: (" + i + ")", expected, actual);
    }
  }

  protected void assertHostsEqual(final List<Host> pExpectedHosts, final List<Host> pActualHosts)
      throws JSONException {
    assertEquals("regression: wrong Host count", pExpectedHosts.size(), pActualHosts.size());
    for (int i = 0; i < pExpectedHosts.size(); i++) {
      final Host expectedHost = pExpectedHosts.get(i);
      final Host actualHost = pActualHosts.get(i);

      assertNull(
          "regression: host mismatch: ("
              + i
              + ") "
              + "EXPECT:\n"
              + toPrettyJson(expectedHost)
              + '\n'
              + "ACTUAL:\n"
              + toPrettyJson(actualHost),
          expectedHost.deepEquals(actualHost));
    }
  }

  protected void assertHostClustersEqual(
      final List<HostCluster> pExpectedHostClusters, final List<HostCluster> pActualHostClusters) {
    assertEquals(
        "regression: wrong HostCluster count",
        pExpectedHostClusters.size(),
        pActualHostClusters.size());
    for (int i = 0; i < pExpectedHostClusters.size(); i++) {
      final HostCluster expectedHostCluster = pExpectedHostClusters.get(i);
      final HostCluster actualHostCluster = pActualHostClusters.get(i);

      assertNull(
          "regression: HostCluster mismatch: ("
              + i
              + ") "
              + "EXPECT:\n"
              + toPrettyJson(expectedHostCluster)
              + '\n'
              + "ACTUAL:\n"
              + toPrettyJson(actualHostCluster),
          expectedHostCluster.deepEquals(actualHostCluster));

      if (expectedHostCluster.isReplicaSet()) {
        final String debugStr =
            "EXPECT:\n"
                + toPrettyJson(expectedHostCluster)
                + '\n'
                + "ACTUAL:\n"
                + toPrettyJson(actualHostCluster);

        if (expectedHostCluster.getReplicaSets() == null) {
          assertNull(actualHostCluster.getReplicaSets());
        } else {
          assertEquals(
              "sanity: 1 replica set: (" + i + ") " + debugStr,
              1,
              expectedHostCluster.getReplicaSets().size());
          final ReplicaSet expectedReplicaSet =
              expectedHostCluster.getReplicaSets().iterator().next();
          final ReplicaSet actualReplicaSet = actualHostCluster.getReplicaSets().iterator().next();
          assertTrue(
              "regression: ReplicaSet mismatch: (" + i + ") " + debugStr,
              expectedReplicaSet.configurationEqual(actualReplicaSet));
        }
      }
    }
  }

  protected void assertNewHostClustersEqual(
      final List<HostCluster> expectedHostClusters, final List<HostCluster> actualHostClusters)
      throws JSONException {
    assertEquals(
        "regression: wrong new HostCluster count",
        expectedHostClusters.size(),
        actualHostClusters.size());

    for (int i = 0; i < expectedHostClusters.size(); i++) {
      final HostCluster expectedHostCluster = expectedHostClusters.get(i);
      final HostCluster actualHostCluster = actualHostClusters.get(i);

      // Assert "updated" field is within reasonable tolerance. Field is defined in JSON as "now"
      // using FTL function.
      final Date expectedUpdated = expectedHostCluster.getUpdated();
      final Date actualUpdated = actualHostCluster.getUpdated();
      assertTrue(
          "updated timestamp was not set on cluster ("
              + i
              + "). expected:"
              + expectedUpdated
              + " actual:"
              + actualUpdated,
          com.xgen.cloud.common.util._public.time.TimeUtils.withinMinutes(
              expectedUpdated.getTime(), actualUpdated.getTime(), 3));

      // Assert "heartbeat" field is within reasonable tolerance. Field is defined in JSON as "now"
      // using FTL function.
      final Date expectedHeartbeat = expectedHostCluster.getHeartbeat();
      final Date actualHeartbeat = actualHostCluster.getHeartbeat();
      assertTrue(
          "heartbeat timestamp was not set on cluster ("
              + i
              + "). expected:"
              + expectedHeartbeat
              + " actual:"
              + actualHeartbeat,
          com.xgen.cloud.common.util._public.time.TimeUtils.withinMinutes(
              expectedHeartbeat.getTime(), actualHeartbeat.getTime(), 3));

      // Assert "created" field is within reasonable tolerance. Field is defined in JSON as "now"
      // using FTL function.
      final Date expectedCreated = expectedHostCluster.getCreated();
      final Date actualCreated = actualHostCluster.getCreated();
      assertTrue(
          "created timestamp was not set on cluster ("
              + i
              + "). expected:"
              + expectedCreated
              + " actual:"
              + actualCreated,
          com.xgen.cloud.common.util._public.time.TimeUtils.withinMinutes(
              expectedCreated.getTime(), actualCreated.getTime(), 3));

      // All Date assertions have checked out, so clear them for the deepEquals check
      expectedHostCluster.setUpdated(null);
      actualHostCluster.setUpdated(null);
      expectedHostCluster.setHeartbeat(null);
      actualHostCluster.setHeartbeat(null);
      expectedHostCluster.setCreated(null);
      actualHostCluster.setCreated(null);

      // New clusters with non-deterministic object Ids. Not making any attempt to derive,
      // so clear them for the deepEquals check.
      expectedHostCluster.setId(null);
      actualHostCluster.setId(null);
      expectedHostCluster.setClusterId(null);
      actualHostCluster.setClusterId(null);

      // New clusters have auto-incrementing name assigned based on iteration order
      // of DiscoveryRequest._nodeClusters, which is undefined. Therefore not asserting names
      // without being JVM version dependent.
      expectedHostCluster.setName(null);
      actualHostCluster.setName(null);

      assertNull(
          "regression: host cluster mismatch: ("
              + i
              + ") "
              + "EXPECT:\n"
              + toPrettyJson(expectedHostCluster)
              + '\n'
              + "ACTUAL:\n"
              + toPrettyJson(actualHostCluster),
          expectedHostCluster.deepEquals(actualHostCluster));
    }
  }

  /** Helper for returning a human-readable difference between expected and actual field values. */
  @SuppressWarnings("rawtypes")
  protected static List<Difference> findDifferences(
      final Node pExpected, final Node pActual, final boolean pPeers)
      throws IOException, DataFormatException {
    final List<Difference> differences = new ArrayList<>();

    if (!Objects.equals(pExpected.getGroupId(), pActual.getGroupId())) {
      differences.add(new Difference("groupId", pExpected.getGroupId(), pActual.getGroupId()));
    }
    if (!Objects.equals(pExpected.getIsSelfId(), pActual.getIsSelfId())) {
      differences.add(new Difference("isSelfId", pExpected.getIsSelfId(), pActual.getIsSelfId()));
    }
    if (!Objects.equals(
        pExpected.getPreferredHostnameAndPort(), pActual.getPreferredHostnameAndPort())) {
      differences.add(
          new Difference(
              "preferredHostnameAndPort",
              pExpected.getPreferredHostnameAndPort(),
              pActual.getPreferredHostnameAndPort()));
    }
    if (!Objects.equals(pExpected.getPreferredHostId(), pActual.getPreferredHostId())) {
      differences.add(
          new Difference(
              "preferredHostId", pExpected.getPreferredHostId(), pActual.getPreferredHostId()));
    }
    if (!Objects.equals(pExpected.getHostnamesAndPorts(), pActual.getHostnamesAndPorts())) {
      differences.add(
          new Difference(
              "hostnamesAndPorts",
              pExpected.getHostnamesAndPorts(),
              pActual.getHostnamesAndPorts()));
    }
    if (!Arrays.equals(pExpected._allHostIds, pActual._allHostIds)) {
      differences.add(
          new Difference(
              "allHostIds",
              Arrays.toString(pExpected._allHostIds),
              Arrays.toString(pActual._allHostIds)));
    }
    if (!Objects.equals(pExpected.getShardIds(), pActual.getShardIds())) {
      differences.add(new Difference("shardIds", pExpected.getShardIds(), pActual.getShardIds()));
    }
    if (!Objects.equals(pExpected.getReplicaSetIds(), pActual.getReplicaSetIds())) {
      differences.add(
          new Difference(
              "replicaSetIds", pExpected.getReplicaSetIds(), pActual.getReplicaSetIds()));
    }
    if (!Objects.equals(pExpected.getLastHeartbeats(), pActual.getLastHeartbeats())) {
      differences.add(
          new Difference(
              "lastHeartbeats", pExpected.getLastHeartbeats(), pActual.getLastHeartbeats()));
    }
    if (!Objects.equals(pExpected.getReplicaHealth(), pActual.getReplicaHealth())) {
      differences.add(
          new Difference(
              "replicaHealth", pExpected.getReplicaHealth(), pActual.getReplicaHealth()));
    }
    if (!Objects.equals(pExpected.getHostTypes(), pActual.getHostTypes())) {
      differences.add(
          new Difference("hostTypes", pExpected.getHostTypes(), pActual.getHostTypes()));
    }
    if (!Objects.equals(pExpected.getReplicaMemberStates(), pActual.getReplicaMemberStates())) {
      differences.add(
          new Difference(
              "replicaStates",
              pExpected.getReplicaMemberStates(),
              pActual.getReplicaMemberStates()));
    }
    if (!Objects.equals(pExpected.getVersion(), pActual.getVersion())) {
      differences.add(new Difference("version", pExpected.getVersion(), pActual.getVersion()));
    }
    if (!Objects.equals(pExpected.getGitVersion(), pActual.getGitVersion())) {
      differences.add(
          new Difference("gitVersion", pExpected.getGitVersion(), pActual.getGitVersion()));
    }
    if (!Objects.equals(pExpected.getModules(), pActual.getModules())) {
      differences.add(new Difference("modules", pExpected.getModules(), pActual.getModules()));
    }
    if (!Objects.equals(pExpected.getSysInfo(), pActual.getSysInfo())) {
      differences.add(new Difference("sysInfo", pExpected.getSysInfo(), pActual.getSysInfo()));
    }
    if (!Objects.equals(pExpected.getBits(), pActual.getBits())) {
      differences.add(new Difference("bits", pExpected.getBits(), pActual.getBits()));
    }
    if (!Objects.equals(pExpected.getUptime(), pActual.getUptime())) {
      differences.add(new Difference("uptime", pExpected.getUptime(), pActual.getUptime()));
    }
    if (!Objects.equals(pExpected.getLastUptime(), pActual.getLastUptime())) {
      differences.add(
          new Difference("lastUptime", pExpected.getLastUptime(), pActual.getLastUptime()));
    }
    if (!Objects.equals(pExpected.getLocalTime(), pActual.getLocalTime())) {
      differences.add(
          new Difference("localTime", pExpected.getLocalTime(), pActual.getLocalTime()));
    }
    if (!Objects.equals(pExpected.getJournalingEnabled(), pActual.getJournalingEnabled())) {
      differences.add(
          new Difference(
              "journalingEnabled",
              pExpected.getJournalingEnabled(),
              pActual.getJournalingEnabled()));
    }
    if (!Objects.equals(pExpected.getStartupWarnings(), pActual.getStartupWarnings())) {
      differences.add(
          new Difference(
              "startupWarnings", pExpected.getStartupWarnings(), pActual.getStartupWarnings()));
    }
    if (!Objects.equals(pExpected.getLowUlimit(), pActual.getLowUlimit())) {
      differences.add(
          new Difference("lowUlimit", pExpected.getLowUlimit(), pActual.getLowUlimit()));
    }
    if (!Objects.equals(pExpected.getLastMongosPing(), pActual.getLastMongosPing())) {
      differences.add(
          new Difference(
              "lastMongosPing",
              pExpected.getLastMongosPing() == null
                  ? null
                  : pExpected.getLastMongosPing().getTime(),
              pActual.getLastMongosPing() == null ? null : pActual.getLastMongosPing().getTime()));
    }
    if (!Objects.equals(
        pExpected.getSSLServerCertificateExpirationDate(),
        pActual.getSSLServerCertificateExpirationDate())) {
      differences.add(
          new Difference(
              "SSLServerCertificationExpirationDate",
              pExpected.getSSLServerCertificateExpirationDate(),
              pActual.getSSLServerCertificateExpirationDate()));
    }

    final Map expectedCmdLineOpts =
        pExpected.getCompressedCmdLineOpts() != null
            ? PooledEncodingUtils.decompressBsonObj(pExpected.getCompressedCmdLineOpts()).toMap()
            : null;
    final Map actualCmdLineOpts =
        pActual.getCompressedCmdLineOpts() != null
            ? PooledEncodingUtils.decompressBsonObj(pActual.getCompressedCmdLineOpts()).toMap()
            : null;
    if (!Objects.equals(expectedCmdLineOpts, actualCmdLineOpts)) {
      differences.add(
          new Difference("compressedCmdLineOpts", expectedCmdLineOpts, actualCmdLineOpts));
    }

    if (!Objects.equals(pExpected.getIsColocatedConfigSvr(), pActual.getIsColocatedConfigSvr())) {
      differences.add(
          new Difference(
              "isColocatedConfigSvr",
              pExpected.getIsColocatedConfigSvr(),
              pActual.getIsColocatedConfigSvr()));
    }

    if (pPeers) {
      final Set<Node> expectedPeers = pExpected.getPeers();
      final Set<Node> actualPeers = pActual.getPeers();
      for (final Node expected : expectedPeers) {
        for (final Node actual : actualPeers) {
          if (expected.equals(actual)) {
            differences.addAll(findDifferences(expected, actual, false));
            break;
          }
        }
        if (!actualPeers.contains(expected)) {
          differences.add(
              new Difference(
                  "no matching peer for node " + expected.getFirstHostId(), expected, null));
        }
      }
      if (expectedPeers.size() != actualPeers.size()) {
        differences.add(
            new Difference(
                "mismatched number of peers. expected: "
                    + pExpected.getFirstHostId()
                    + " "
                    + expectedPeers.size()
                    + ", actual: "
                    + pActual.getFirstHostId()
                    + " "
                    + actualPeers.size(),
                null,
                null));
      }
    }

    return differences;
  }

  static class Difference {

    final String _message;
    final Object _expected;
    final Object _actual;

    Difference(final String pMessage, final Object pExpected, final Object pActual) {
      _message = pMessage;
      _expected = pExpected;
      _actual = pActual;
    }

    @Override
    public String toString() {
      return "NodeDifference{"
          + "\n_key='"
          + _message
          + '\''
          + ", \n_expected="
          + _expected
          + ", \n_actual="
          + _actual
          + '}';
    }
  }

  protected String toPrettyJson(final Object pObject) throws JSONException {
    return toPrettyJson(getMorphia().toDBObject(pObject));
  }

  protected String toPrettyJson(final DBObject pBson) throws JSONException {
    return new JSONObject(JsonUtils.serializeStrict(pBson)).toString(2);
  }

  @SuppressWarnings("unchecked")
  protected void assertCanonicalizedBSONObjectEquals(
      final BSONPath pStack, final BSONObject pExpected, final BSONObject pActual) {
    for (final String key : pExpected.keySet()) {
      pStack.onKey(key);

      final Object expected = pExpected.get(key);
      final Object actual = pActual.get(key);
      assertBSONValue(pStack, expected, actual);

      pStack.pop();
    }

    assertEquals(
        "mismatched object sizes: path=" + pStack,
        pExpected.keySet().size(),
        pActual.keySet().size());
  }

  private void assertBSONArrayEquals(
      final BSONPath pStack, final Object pExpected, final Object pActual) {
    final int length = Array.getLength(pExpected);
    assertEquals("mismatched array sizes: path=" + pStack, length, Array.getLength(pActual));

    for (int i = 0; i < length; i++) {
      pStack.onArray(i);

      final Object expected = Array.get(pExpected, i);
      final Object actual = Array.get(pActual, i);
      assertBSONValue(pStack, expected, actual);

      pStack.pop();
    }
  }

  @SuppressWarnings("unchecked")
  private void assertBSONListEquals(
      final BSONPath pStack, final List<Object> pExpected, final List<Object> pActual) {
    assertEquals("mismatched array sizes: path=" + pStack, pExpected.size(), pActual.size());

    for (int i = 0; i < pExpected.size(); i++) {
      pStack.onList(i);

      final Object expected = pExpected.get(i);
      final Object actual = pActual.get(i);
      assertBSONValue(pStack, expected, actual);

      pStack.pop();
    }
  }

  @SuppressWarnings("unchecked")
  private void assertBSONMapEquals(
      final BSONPath pStack,
      final Map<String, Object> pExpected,
      final Map<String, Object> pActual) {
    for (final String key : pExpected.keySet()) {
      pStack.onKey(key);

      final Object expected = pExpected.get(key);
      final Object actual = pActual.get(key);

      assertBSONValue(pStack, expected, actual);

      pStack.pop();
    }

    assertEquals(
        "mismatched object sizes: path=" + pStack,
        pExpected.keySet().size(),
        pActual.keySet().size());
  }

  @SuppressWarnings("unchecked")
  protected void assertBSONValue(
      final BSONPath pStack, final Object pExpected, final Object pActual) {
    assertEquals(
        "class mismatch: path="
            + pStack
            + " expectedValue:"
            + pExpected
            + " actualValue:"
            + pActual,
        getClass(pExpected),
        getClass(pActual));

    if (pExpected instanceof BSONObject) {
      assertCanonicalizedBSONObjectEquals(pStack, (BSONObject) pExpected, (BSONObject) pActual);
    } else if (pExpected instanceof List) {
      assertBSONListEquals(pStack, (List) pExpected, (List) pActual);
    } else if (pExpected instanceof Map) {
      assertBSONMapEquals(pStack, (Map<String, Object>) pExpected, (Map<String, Object>) pActual);
    } else if (pExpected != null && pExpected.getClass().isArray()) {
      assertBSONArrayEquals(pStack, pExpected, pActual);
    } else {
      assertEquals(
          "value mismatch: path=" + pStack + " class:" + getClass(pExpected), pExpected, pActual);
    }
  }

  protected static Class<?> getClass(final Object o) {
    return o == null ? null : o.getClass();
  }

  /**
   * Helper class for tracking traversal of BSONObject during comparison. toString() produces
   * human-readable path from root to a field. E.g., "data.[2].members.[1].foo.bar"
   */
  protected static class BSONPath {

    private final LinkedList<String> _stack;

    protected BSONPath() {
      _stack = new LinkedList<>();
    }

    void onKey(final String pKey) {
      _stack.addLast(pKey);
    }

    void onList(final int pIndex) {
      _stack.addLast("(" + pIndex + ")");
    }

    void onArray(final int pIndex) {
      _stack.addLast("[" + pIndex + "]");
    }

    String pop() {
      return _stack.removeLast();
    }

    @Override
    public boolean equals(final Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }

      final BSONPath bsonPath = (BSONPath) o;

      return _stack.equals(bsonPath._stack);
    }

    @Override
    public int hashCode() {
      return _stack.hashCode();
    }

    @Override
    public String toString() {
      return StringUtils.join(_stack, '.');
    }
  }

  // JM: from 2.12.2 driver
  @SuppressWarnings("unchecked")
  private static Object canonicalize(final Object from) {
    if (from instanceof BSONObject && !(from instanceof BasicBSONList)) {
      return canonicalizeBSONObject((BSONObject) from);
    } else if (from instanceof List) {
      return canonicalizeList((List<Object>) from);
    } else if (from instanceof Map) {
      return canonicalizeMap((Map<String, Object>) from);
    } else {
      return from;
    }
  }

  private static Map<String, Object> canonicalizeMap(final Map<String, Object> from) {
    final Map<String, Object> canonicalized = new LinkedHashMap<String, Object>(from.size());
    final TreeSet<String> keysInOrder = new TreeSet<String>(from.keySet());
    for (final String key : keysInOrder) {
      final Object val = from.get(key);
      canonicalized.put(key, canonicalize(val));
    }
    return canonicalized;
  }

  private static BasicBSONObject canonicalizeBSONObject(final BSONObject from) {
    final BasicBSONObject canonicalized = new BasicBSONObject();
    final TreeSet<String> keysInOrder = new TreeSet<String>(from.keySet());
    for (final String key : keysInOrder) {
      final Object val = from.get(key);
      canonicalized.put(key, canonicalize(val));
    }
    return canonicalized;
  }

  private static List<Object> canonicalizeList(final List<Object> list) {
    final List<Object> canonicalized = new ArrayList<Object>(list.size());
    for (final Object cur : list) {
      canonicalized.add(canonicalize(cur));
    }
    return canonicalized;
  }
}
