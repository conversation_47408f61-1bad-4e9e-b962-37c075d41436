package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.reset;

import com.google.common.collect.Sets;
import com.stripe.Stripe;
import com.stripe.model.Charge;
import com.stripe.model.Customer;
import com.stripe.model.PaymentIntent;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.EventSvc;
import com.xgen.cloud.billing._public.svc.ICodeActivationSvc;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus.Type;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.grpc._public.client.PaymentProcessingClient;
import com.xgen.cloud.payments.standalone.common._public.gateway.PaymentMethodGateway;
import com.xgen.cloud.payments.stripe._public.client.StripeInterface;
import com.xgen.cloud.services.core.inprocess.RunWithStandaloneServices;
import com.xgen.cloud.services.core.inprocess.RunWithStandaloneServicesTestExtension;
import com.xgen.cloud.services.core.inprocess.StandaloneServiceRunner.AvailableService;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CouponDao;
import com.xgen.svc.mms.dao.billing.DiscountDao;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.dao.billing.PaymentMethodDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.Coupon;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.Discount;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.PaymentIntentStatus;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.PaymentMethodFactory;
import configservicesdk.com.xgen.devtools.configservicesdk.Secret;
import jakarta.inject.Inject;
import java.text.SimpleDateFormat;
import java.time.Clock;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith({RunWithStandaloneServicesTestExtension.class})
@RunWithStandaloneServices({AvailableService.PAYMENT_METHOD_SERVICE})
public class PaymentMethodSvcThirdPartyTests extends JUnit5BaseSvcTest {

  private static final String LOCALHOST = "127.0.0.1";

  @Inject private PlanSvc _planSvc;

  @Inject private PaymentMethodSvc _paymentMethodSvc;

  @Inject private PaymentMethodDao _paymentMethodDao;

  @Inject private DiscountDao _discountDao;

  @Inject private CouponDao _couponDao;

  @Inject private EventSvc eventSvc;

  @Inject private OrganizationDao _organizationDao;

  @Inject private StripeInterface _stripeInterface;

  @Inject private CreditSvc _creditSvc;

  @Inject private SalesforceProductCodeDao _salesforceProductCodeDao;

  @Inject private BraintreeSvc _braintreeSvc;

  @Inject private PaymentSvc _paymentSvc;

  @Inject private InvoiceDao _invoiceDao;

  @Inject private AccountantSvc _accountantSvc;

  @Inject private Clock clock;

  @Inject private InvoiceFactory _invoiceFactory;
  @Inject private PaymentMethodFactory _paymentMethodFactory;
  @Inject private OrganizationFactory _organizationFactory;
  @Inject private OrganizationSvc organizationSvc;
  @Inject private PaymentProcessingClient paymentProcessingClient;
  @Inject private PaymentDao paymentDao;

  @Inject private AddPaymentMethodOrchestratorSvc addPaymentMethodOrchestratorSvc;
  @Inject private ICodeActivationSvc codeActivationSvc;
  @Inject private PaymentMethodGateway paymentMethodGateway;

  private Group _group;
  private Organization _organization;
  private AppUser _user;
  private AppUser _globalUser;
  private Secret _stripePubKey;
  private AuditInfo _auditInfo;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    reset(paymentProcessingClient);

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/InvoiceDao/invoices.json.ftl",
        null,
        Invoice.DB_NAME,
        Invoice.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/LineItemDao/lineItems.json.ftl",
        null,
        LineItem.DB_NAME,
        LineItem.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/PaymentMethodDao/paymentMethods.json.ftl",
        null,
        PaymentMethod.DB_NAME,
        PaymentMethod.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/CouponDao/coupons.json.ftl", null, Coupon.DB_NAME, Coupon.COLLECTION_NAME);

    _salesforceProductCodeDao.syncDocuments();

    _organization = MmsFactory.createOrganizationWithStandardPlan();
    _group = MmsFactory.createGroup(_organization);
    _user = MmsFactory.createUser(_group);
    _globalUser = MmsFactory.createReadOnlyAdminUser(_group);
    _stripePubKey = _stripeInterface.getPublishableApiKey(BillingAccount.MONGODB_INC);

    Stripe.apiKey = _stripeInterface.getApiKey(BillingAccount.MONGODB_INC).toPlainTextValue();

    _auditInfo = MmsFactory.createAuditInfoFromUiCall(_user, false, LOCALHOST);
  }

  @Test
  public void testCreatePaymentMethodStandard() throws Exception {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.YEAR, 1);
    int month = cal.get(Calendar.MONTH) + 1;
    int year = cal.get(Calendar.YEAR);
    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    assertNotNull(paymentMethod);

    Customer customer = _stripeInterface.getCustomer(null, paymentMethod.getStripeCustomerId());
    List<com.stripe.model.PaymentMethod> stripePaymentMethods =
        _stripeInterface.getCustomerCardPaymentMethods(
            paymentMethod.getBillingAccount(), customer.getId());

    assertEquals(1, stripePaymentMethods.size());
    assertEquals(paymentMethod.getStripePaymentMethodId(), stripePaymentMethods.get(0).getId());
    assertEquals(paymentMethod.getStripePaymentMethodId(), stripePaymentMethod.getId());

    com.stripe.model.PaymentMethod.Card card = stripePaymentMethods.get(0).getCard();

    assertEquals("4242", card.getLast4());
    assertEquals(Long.valueOf(month), card.getExpMonth());
    assertEquals(Long.valueOf(year), card.getExpYear());
    assertEquals(StripeTestUtils.NAME, stripePaymentMethod.getBillingDetails().getName());

    assertEquals(card.getLast4(), paymentMethod.getCardLast4());
    assertEquals(StripeTestUtils.NAME, paymentMethod.getNameOnCard());
    assertEquals(stripePaymentMethod.getId(), paymentMethod.getStripePaymentMethodId());

    assertEquals(null, paymentMethod.getBillingAddress().getAddressLineTwo());

    assertEquals(2, eventSvc.findAll(3).size());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void testCreatePaymentMethodNDS() throws Exception {
    Date now = new Date();
    Organization org = _organizationFactory.createAtlasOrganizationWithPlan(now, PlanType.NDS);
    Group group = _organizationFactory.createNdsGroup(org, now);
    AppUser user = MmsFactory.createUser(group);

    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.YEAR, 1);
    int month = cal.get(Calendar.MONTH) + 1;
    int year = cal.get(Calendar.YEAR);

    String coupon = "GetAtlas"; // purposefully change the casing
    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year);

    AuditInfo auditInfo = MmsFactory.createAuditInfoFromUiCall(user, false, LOCALHOST);

    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        org,
        user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        coupon,
        null,
        auditInfo,
        clock,
        null);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(org.getId(), false).orElse(null);
    assertNotNull(paymentMethod);
    Customer customer = _stripeInterface.getCustomer(null, paymentMethod.getStripeCustomerId());

    List<com.stripe.model.PaymentMethod> stripePaymentMethods =
        _stripeInterface.getCustomerCardPaymentMethods(
            paymentMethod.getBillingAccount(), customer.getId());

    assertEquals(1, stripePaymentMethods.size());
    assertEquals(paymentMethod.getStripePaymentMethodId(), stripePaymentMethods.get(0).getId());
    assertEquals(paymentMethod.getStripePaymentMethodId(), stripePaymentMethod.getId());

    com.stripe.model.PaymentMethod.Card card = stripePaymentMethods.get(0).getCard();

    assertEquals("4242", card.getLast4());
    assertEquals(Long.valueOf(month), card.getExpMonth());
    assertEquals(Long.valueOf(year), card.getExpYear());
    assertEquals(StripeTestUtils.NAME, stripePaymentMethod.getBillingDetails().getName());

    assertEquals(card.getLast4(), paymentMethod.getCardLast4());
    assertEquals(StripeTestUtils.NAME, paymentMethod.getNameOnCard());
    assertEquals(stripePaymentMethod.getId(), paymentMethod.getStripePaymentMethodId());

    assertNull(paymentMethod.getBillingAddress().getAddressLineTwo());

    // TODO: we need to add events in next ticket.
    // two events because a credit was issued
    // assertEquals(2, eventSvc.findAll(2).size());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());

    Date pDate = new Date();
    List<Credit> credits = _creditSvc.findUnappliedCreditsByOrgId(group.getOrgId(), pDate);
    assertEquals(1, credits.size());
    assertEquals(10000L, credits.get(0).getAmountCents()); // GETATLAS has a value of 10000
    assertEquals("GETATLAS", credits.get(0).getNote());
  }

  @Test
  public void testCreatePaymentMethodAndApplyCoupon() throws Exception {
    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey);

    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);
    codeActivationSvc.applyCode(_organization, _user, "COUPON1", clock, _auditInfo, true);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    assertNotNull(paymentMethod);

    List<Discount> discounts = _discountDao.findActiveByOrgId(_organization.getId(), new Date());
    assertEquals(1, discounts.size());

    Discount discount = discounts.get(0);
    Calendar discountEndDate =
        TimeUtils.cal((DateUtils.truncate(Date.from(clock.instant()), Calendar.DATE)).getTime());
    TimeUtils.addDays(discountEndDate, 30);
    List<Event> events = eventSvc.findAll(4);
    assertEquals(3, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(0.25, event.getDiscountPercent(), 0.00000001);
    assertEquals(
        Collections.unmodifiableSet(Sets.newHashSet(SKU.MMS_STANDARD, SKU.MMS_BACKUP_STORAGE)),
        event.getDiscountSkus());
    assertEquals(discount.getEndDate(), event.getDiscountEndDate());
    assertEquals(discountEndDate.getTime(), event.getDiscountEndDate());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void testCreatePaymentMethodWithDollarOffCoupon() throws Exception {
    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        "DOLLAR_OFF_COUPON_ONE",
        null,
        _auditInfo,
        clock,
        null);
    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    assertNotNull(paymentMethod);

    List<Credit> credits = _creditSvc.findAvailableByOrgId(paymentMethod.getOrgId());
    assertEquals(1, credits.size());
    assertEquals(10025L, credits.get(0).getAmountCents());
    assertEquals("DOLLAR_OFF_COUPON_ONE", credits.get(0).getNote());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void testApplyingOldSchoolPrePaidCoupon() throws Exception {
    codeActivationSvc.applyCode(
        _organization, _user, "OLD_SCHOOL_PREPAID_COUPON", clock, _auditInfo, true);

    List<Discount> discounts = _discountDao.findActiveByOrgId(_organization.getId(), new Date());
    assertEquals(1, discounts.size());
    Discount discount = discounts.get(0);
    Calendar discountEndDate =
        TimeUtils.cal((DateUtils.truncate(Date.from(clock.instant()), Calendar.DATE)).getTime());
    TimeUtils.addDays(discountEndDate, 365);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(1.00, event.getDiscountPercent(), 0.00000001);
    assertEquals(
        Collections.unmodifiableSet(Sets.newHashSet(SKU.MMS_STANDARD, SKU.MMS_BACKUP_STORAGE)),
        event.getDiscountSkus());
    assertEquals(discount.getEndDate(), event.getDiscountEndDate());
    assertEquals(discountEndDate.getTime(), event.getDiscountEndDate());
  }

  @Test
  public void testApplyingRegularCouponWithNoStripePaymentMethodId() throws Exception {
    codeActivationSvc.applyCode(_organization, _user, "UNLIMITED_COUPON", clock, _auditInfo, true);

    List<Discount> discounts = _discountDao.findActiveByOrgId(_organization.getId(), new Date());
    assertEquals(1, discounts.size());
    Discount discount = discounts.get(0);
    Calendar discountEndDate =
        TimeUtils.cal((DateUtils.truncate(Date.from(clock.instant()), Calendar.DATE)).getTime());
    TimeUtils.addDays(discountEndDate, 1);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(0.05, event.getDiscountPercent(), 0.00000001);
    assertEquals(
        Collections.unmodifiableSet(Sets.newHashSet(SKU.MMS_PREMIUM)), event.getDiscountSkus());
    assertEquals(discount.getEndDate(), event.getDiscountEndDate());
    assertEquals(discountEndDate.getTime(), event.getDiscountEndDate());
  }

  /**
   * This test depends on a seed Opportunity in the Salesforce Sandbox, with activation code
   * 369****************. https://mongodb--stage.cs10.my.salesforce.com/006A000000SI02V This
   * Opportunity in the Sandbox is a copy of a real Opportunity in Salesforce production. Therefore,
   * whenever the Sandbox is refreshed, it should be recreated without any need for further
   * intervention. If this becomes problematic, we can find a different way to test this.
   */
  @Test
  @Disabled
  public void testCreatePaymentMethodWithPrePaidActivationCode() throws Exception {
    assertTrue(eventSvc.findAll(1).isEmpty());
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        null,
        _organization,
        _user,
        null,
        null,
        false,
        null,
        "369****************",
        null,
        _auditInfo,
        clock,
        null);

    List<Discount> discounts = _discountDao.findActiveByOrgId(_organization.getId(), new Date());
    assertEquals(0, discounts.size());

    _organization = _organizationDao.findById(_organization.getId());
    // Since this group only has prepaid backup, it should be on a FREE_TIER plan.
    OrgPlan plan = _planSvc.getCurrentPlan(_organization.getId());
    assertEquals(PlanType.FREE_TIER, plan.getPlanType());

    // The account should *not* have been upgraded, so ensure there's no event for it
    assertFalse(
        eventSvc.findAll(99).stream()
            .anyMatch(e -> Objects.equals(e.getEventType(), BillingEvent.Type.ACCOUNT_UPGRADED)));

    List<PrepaidPlan> prepaidPlans =
        _planSvc.getOrgPrepaidPlan(_organization.getId()).getPrepaidPlans();
    assertEquals(1, prepaidPlans.size());
    PrepaidPlan prepaidPlan = prepaidPlans.get(0);
    assertEquals("369****************", prepaidPlan.getActivationCode());
    assertEquals(Arrays.asList(SKU.MMS_BACKUP_STORAGE), prepaidPlan.getSkus());
  }

  @Test
  @Disabled
  public void
      testCreatePaymentMethodWithPrePaidActivationCodeAndQuarterlyLineItemsForStandardGroup()
          throws Exception {
    _group = MmsFactory.createGroupWithStandardPlan();
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        null,
        _organization,
        _user,
        null,
        null,
        false,
        null,
        "*****************",
        null,
        _auditInfo,
        clock,
        null);
    _organization = _organizationDao.findById(_organization.getId());
    OrgPlan plan = _planSvc.getCurrentPlan(_organization.getId());
    List<PrepaidPlan> prepaidPlans =
        _planSvc.getOrgPrepaidPlan(_organization.getId()).getPrepaidPlans();

    // Sort the created plans, for easier equality comparisons
    Collections.sort(prepaidPlans, Comparator.comparing(PrepaidPlan::getStartDate));

    // One for each quarter
    assertEquals(4, prepaidPlans.size());

    SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd");

    PrepaidPlan prepaidPlan = prepaidPlans.get(0);
    assertEquals("*****************", prepaidPlan.getActivationCode());
    assertEquals(Arrays.asList(SKU.MMS_BACKUP_STORAGE), prepaidPlan.getSkus());
    assertEquals(df.parse("2014/10/30"), prepaidPlan.getStartDate());
    assertEquals(df.parse("2015/01/30"), prepaidPlan.getEndDate());
    assertEquals(10000, prepaidPlan.getQuantity());

    prepaidPlan = prepaidPlans.get(1);
    assertEquals("*****************", prepaidPlan.getActivationCode());
    assertEquals(Arrays.asList(SKU.MMS_BACKUP_STORAGE), prepaidPlan.getSkus());
    assertEquals(df.parse("2015/01/30"), prepaidPlan.getStartDate());
    assertEquals(df.parse("2015/04/30"), prepaidPlan.getEndDate());
    assertEquals(10000, prepaidPlan.getQuantity());

    prepaidPlan = prepaidPlans.get(2);
    assertEquals("*****************", prepaidPlan.getActivationCode());
    assertEquals(Arrays.asList(SKU.MMS_BACKUP_STORAGE), prepaidPlan.getSkus());
    assertEquals(df.parse("2015/04/30"), prepaidPlan.getStartDate());
    assertEquals(df.parse("2015/07/30"), prepaidPlan.getEndDate());
    assertEquals(10000, prepaidPlan.getQuantity());

    prepaidPlan = prepaidPlans.get(3);
    assertEquals("*****************", prepaidPlan.getActivationCode());
    assertEquals(Arrays.asList(SKU.MMS_BACKUP_STORAGE), prepaidPlan.getSkus());
    assertEquals(df.parse("2015/07/30"), prepaidPlan.getStartDate());
    assertEquals(df.parse("2015/10/30"), prepaidPlan.getEndDate());
    assertEquals(10000, prepaidPlan.getQuantity());
  }

  @Test
  public void testCreatePaymentMethodWithInternalFreeActivationCode() throws Exception {
    try {
      addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
          null,
          _organization,
          _user,
          null,
          null,
          false,
          null,
          PrepaidPlan.INTERNAL_FREE_ACTIVATION_CODE,
          null,
          _auditInfo,
          clock,
          null);
      fail("Activation codes cannot be entered through the payment method modal");
    } catch (SvcException e) {
      assertEquals(BillingErrorCode.CREDIT_CARD_OR_COUPON_CODE_REQUIRED, e.getErrorCode());
    }
  }

  @Test
  public void testCreatePaymentMethodWithPercentOffCouponCode() throws Exception {
    try {
      addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
          null,
          _organization,
          _user,
          null,
          null,
          false,
          null,
          "COUPON1",
          null,
          _auditInfo,
          clock,
          null);
      fail("Percent off coupon codes cannot be entered through the payment method modal");
    } catch (SvcException e) {
      assertEquals(
          BillingErrorCode.PERCENT_OFF_COUPON_CODE_NOT_PERMITTED_THOUGH_PAYMENT_METHOD,
          e.getErrorCode());
    }
  }

  @Test
  public void testApplyCouponOrActivationCodeWithInternalFreeActivationCode() throws Exception {
    AuditInfo auditInfoForGlobalUser =
        MmsFactory.createAuditInfoFromUiCall(_globalUser, true, LOCALHOST);

    codeActivationSvc.applyCode(
        _organization,
        _globalUser,
        PrepaidPlan.INTERNAL_FREE_ACTIVATION_CODE,
        clock,
        auditInfoForGlobalUser,
        true);

    _organization = _organizationDao.findById(_organization.getId());
    List<PrepaidPlan> prepaidPlans =
        _planSvc.getOrgPrepaidPlan(_organization.getId()).getPrepaidPlans();
    assertEquals(1, prepaidPlans.size());
    PrepaidPlan prepaidPlan = prepaidPlans.get(0);
    assertEquals(PrepaidPlan.INTERNAL_FREE_ACTIVATION_CODE, prepaidPlan.getActivationCode());
    assertNull(prepaidPlan.getSkus());
    assertNotNull(prepaidPlan.getStartDate());
    assertEquals(PrepaidPlan.END_OF_TIME, prepaidPlan.getEndDate());
    assertEquals(PrepaidPlan.UNLIMITED_QUANTITY, prepaidPlan.getQuantity());
  }

  @Test
  public void testUpdatePaymentMethodStandard() throws Exception {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.YEAR, 1);
    int month = cal.get(Calendar.MONTH) + 1;
    int year = cal.get(Calendar.YEAR);
    cal.add(Calendar.YEAR, 1);
    int year2 = cal.get(Calendar.YEAR);

    com.stripe.model.PaymentMethod createdStripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        createdStripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    createdStripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year2);

    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        createdStripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    Customer customer = _stripeInterface.getCustomer(null, paymentMethod.getStripeCustomerId());

    List<com.stripe.model.PaymentMethod> foundStripePaymentMethods =
        _stripeInterface.getCustomerCardPaymentMethods(
            paymentMethod.getBillingAccount(), customer.getId());

    assertEquals(1, foundStripePaymentMethods.size());
    assertEquals(
        paymentMethod.getStripePaymentMethodId(), foundStripePaymentMethods.get(0).getId());
    assertEquals(paymentMethod.getStripePaymentMethodId(), createdStripePaymentMethod.getId());

    com.stripe.model.PaymentMethod.Card card = foundStripePaymentMethods.get(0).getCard();

    assertEquals("4242", card.getLast4());
    assertEquals(Long.valueOf(month), card.getExpMonth());
    assertEquals(Long.valueOf(year2), card.getExpYear());
    assertEquals(StripeTestUtils.NAME, createdStripePaymentMethod.getBillingDetails().getName());

    assertEquals(card.getLast4(), paymentMethod.getCardLast4());
    assertEquals(StripeTestUtils.NAME, paymentMethod.getNameOnCard());
    assertEquals(createdStripePaymentMethod.getId(), paymentMethod.getStripePaymentMethodId());

    assertEquals(3, eventSvc.findAll(5).size());

    com.stripe.model.PaymentMethod badStripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(
            _stripePubKey, StripeTestUtils.CARD_NUMBER_CHARGE_DECLINED, month, year2);

    try {
      addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
          BillingAccount.MONGODB_INC,
          _organization,
          _user,
          badStripePaymentMethod.getId(),
          null,
          false,
          null,
          null,
          null,
          _auditInfo,
          clock,
          null);
      fail();
    } catch (Exception e) {
      // expected
    }

    PaymentMethod paymentMethod2 =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);

    List<com.stripe.model.PaymentMethod> foundStripePaymentMethods2 =
        _stripeInterface.getCustomerCardPaymentMethods(
            paymentMethod.getBillingAccount(), customer.getId());

    assertEquals(
        foundStripePaymentMethods2.get(0).getId(), paymentMethod2.getStripePaymentMethodId());
    assertEquals(
        paymentMethod.getStripePaymentMethodId(), paymentMethod2.getStripePaymentMethodId());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void
      testUpdatePaymentMethodSamePaymentMethodAsActivePaymentMethod_existingPaymentMethodIsNotDetached()
          throws Exception {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.YEAR, 1);
    int month = cal.get(Calendar.MONTH) + 1;
    int year = cal.get(Calendar.YEAR);

    com.stripe.model.PaymentMethod createdStripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        createdStripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        null,
        _organization,
        _user,
        createdStripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    Customer customer = _stripeInterface.getCustomer(null, paymentMethod.getStripeCustomerId());

    List<com.stripe.model.PaymentMethod> foundStripePaymentMethods =
        _stripeInterface.getCustomerCardPaymentMethods(
            paymentMethod.getBillingAccount(), customer.getId());

    // this would fail if the existing payment method was detached.
    assertEquals(1, foundStripePaymentMethods.size());
    assertEquals(
        paymentMethod.getStripePaymentMethodId(), foundStripePaymentMethods.get(0).getId());
    assertEquals(paymentMethod.getStripePaymentMethodId(), createdStripePaymentMethod.getId());

    com.stripe.model.PaymentMethod.Card card = foundStripePaymentMethods.get(0).getCard();

    assertEquals("4242", card.getLast4());
    assertEquals(Long.valueOf(month), card.getExpMonth());
    assertEquals(Long.valueOf(year), card.getExpYear());
    assertEquals(StripeTestUtils.NAME, createdStripePaymentMethod.getBillingDetails().getName());

    assertEquals(card.getLast4(), paymentMethod.getCardLast4());
    assertEquals(StripeTestUtils.NAME, paymentMethod.getNameOnCard());
    assertEquals(createdStripePaymentMethod.getId(), paymentMethod.getStripePaymentMethodId());

    assertEquals(2, eventSvc.findAll(5).size());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void testUpdatePaymentMethodWithCoupon() throws Exception {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.YEAR, 1);
    int month = cal.get(Calendar.MONTH) + 1;
    int year = cal.get(Calendar.YEAR);
    cal.add(Calendar.YEAR, 1);
    int year2 = cal.get(Calendar.YEAR);

    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey, month, year2);

    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        "DOLLAR_OFF_COUPON_ONE",
        null,
        _auditInfo,
        clock,
        null);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    assertNotNull(paymentMethod);

    List<Credit> credits = _creditSvc.findAvailableByOrgId(paymentMethod.getOrgId());
    assertEquals(1, credits.size());
    assertEquals(10025L, credits.get(0).getAmountCents());
    assertEquals("DOLLAR_OFF_COUPON_ONE", credits.get(0).getNote());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void testUpdatePaymentMethodWithCouponButNoStripePaymentMethodId() throws Exception {
    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        null,
        _organization,
        _user,
        null,
        null,
        false,
        null,
        "DOLLAR_OFF_COUPON_ONE",
        null,
        _auditInfo,
        clock,
        null);

    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(_organization.getId(), false).orElse(null);
    assertNotNull(paymentMethod);

    List<Credit> credits = _creditSvc.findAvailableByOrgId(paymentMethod.getOrgId());
    assertEquals(1, credits.size());
    assertEquals(10025L, credits.get(0).getAmountCents());
    assertEquals("DOLLAR_OFF_COUPON_ONE", credits.get(0).getNote());

    _stripeInterface.deleteCustomer(null, paymentMethod.getStripeCustomerId());
  }

  @Test
  public void testUpdatePaymentMethodForSuspendedOrganization() throws Exception {
    organizationSvc.updateOrgPaymentStatus(
        _organization, OrgPaymentStatus.suspended(new Date(), false, null));
    _organization = _organizationDao.findById(_organization.getId());

    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    Organization unlockedOrg = _organizationDao.findById(_organization.getId());
    assertEquals(OrgPaymentStatus.Type.OK, unlockedOrg.getPaymentStatus().getStatus());
  }

  @Test
  public void
      testUpdatePaymentMethodForSuspendedOrganization_dunningRecoveryDisabled_staysInSuspended()
          throws Exception {
    organizationSvc.updateOrgPaymentStatus(
        _organization, OrgPaymentStatus.suspended(new Date(), true, null));
    _organization = _organizationDao.findById(_organization.getId());

    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey);
    addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
        BillingAccount.MONGODB_INC,
        _organization,
        _user,
        stripePaymentMethod.getId(),
        null,
        false,
        null,
        null,
        null,
        _auditInfo,
        clock,
        null);

    Organization stillSuspendedOrg = _organizationDao.findById(_organization.getId());
    assertEquals(Type.SUSPENDED, stillSuspendedOrg.getPaymentStatus().getStatus());
  }

  @Test
  public void testValidateCardChargeability() throws Exception {
    BillingAccount billingAccount = BillingAccount.MONGODB_INC;

    com.stripe.model.PaymentMethod stripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(_stripePubKey);

    long cents = 100;

    Customer customer =
        _stripeInterface.createStripeCustomer(billingAccount, "test", stripePaymentMethod.getId());

    _paymentMethodSvc.validateCardChargeability(
        _organization, billingAccount, null, customer.getId(), stripePaymentMethod.getId());

    List<PaymentIntent> recentPaymentIntents =
        _stripeInterface.getRecentPaymentIntents(billingAccount, customer.getId(), 50);

    PaymentIntent paymentIntent =
        recentPaymentIntents.stream()
            .filter(pi -> stripePaymentMethod.getId().equals(pi.getPaymentMethod()))
            .findFirst()
            .get();

    assertEquals(PaymentIntentStatus.CANCELED, paymentIntent.getStatus());
    assertEquals(cents, paymentIntent.getAmount().intValue());

    Charge charge = paymentIntent.getLatestChargeObject();

    assertTrue(charge.getRefunded());
    assertEquals(cents, charge.getAmountRefunded().intValue());

    com.stripe.model.PaymentMethod badStripePaymentMethod =
        StripeTestUtils.getStripeCardPaymentMethodForTesting(
            _stripePubKey, StripeTestUtils.CARD_NUMBER_CHARGE_DECLINED, 11, 50);

    try {
      _paymentMethodSvc.validateCardChargeability(
          _organization, billingAccount, null, customer.getId(), badStripePaymentMethod.getId());
      fail();
    } catch (Exception e) {
      // expected
    }

    recentPaymentIntents =
        _stripeInterface.getRecentPaymentIntents(billingAccount, customer.getId(), 50);

    assertTrue(
        recentPaymentIntents.stream()
            .noneMatch(pi -> badStripePaymentMethod.getId().equals(pi.getPaymentMethod())));

    // should pass with a payment requiring auth
    _paymentMethodSvc.validateCardChargeability(
        _organization,
        billingAccount,
        null,
        customer.getId(),
        StripeTestUtils.PAYMENT_METHOD_CARD_AUTH_REQUIRED);
  }
}
