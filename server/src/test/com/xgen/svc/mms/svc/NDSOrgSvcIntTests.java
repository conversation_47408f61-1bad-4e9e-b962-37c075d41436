package com.xgen.svc.mms.svc;

import static org.hamcrest.CoreMatchers.instanceOf;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billing._public.svc.IInvoiceSvc;
import com.xgen.cloud.billing._public.svc.IPlanSvc;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.group._public.view.GroupInfoView;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._private.view.OrgCreationForm;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.activity.OrgAudit;
import com.xgen.cloud.organization._public.model.activity.OrgEvent.Type;
import com.xgen.cloud.organization._public.view.OrgInfoView;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.svc.common.OrgErrorCode;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class NDSOrgSvcIntTests extends BaseSvcTest {
  @Inject private NDSOrgSvc _ndsOrgSvc;

  @Inject private OrganizationDao _organizationDao;

  @Inject private UserDao _userDao;

  @Inject private IPlanSvc _planSvc;

  @Inject private IInvoiceSvc _invoiceSvc;

  @Inject private AppSettings _appSettings;

  @Inject private AuditSvc _auditSvc;

  @Inject private AuthzSvc _authzSvc;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/user/UserDao/users.json.ftl",
        getParamMap(),
        AppUser.DB_NAME,
        AppUser.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, NDSGroupDao.DB, NDSGroupDao.COLLECTION);
  }

  @Test
  public void testCreateOrganization() throws Exception {
    final AppUser user = _userDao.findById(oid(1));
    final Date now = new Date();
    final Organization org =
        _ndsOrgSvc.createOrganization(
            "Test", GroupType.CLOUD, user, now, AuditInfoHelpers.fromInternal());
    assertNotNull(org.getId());
    assertEquals(oid(1), org.getCreator());
    assertEquals("Test", org.getName());
    assertEquals("test", org.getNameLowercase());
    assertEquals(Collections.singletonList("test"), org.getNameTokens());
    assertTrue(org.getGenAiFeaturesEnabled());
    assertFalse(org.isSkipDefaultAlertsSettingsEnabled());

    final OrgPlan plan = _planSvc.getCurrentPlan(org.getId());
    assertNotNull(plan);
    assertEquals(PlanType.FREE_TIER, plan.getPlanType());

    assertNotNull(plan.getFreeUntil());
    assertNull(org.getDeleted());
    final AppUser updatedUser = _userDao.findById(oid(1));
    assertTrue(updatedUser.hasOrgId(org.getId()));
    assertTrue(_authzSvc.hasOrgRole(updatedUser, Role.ORG_OWNER, org.getId()));
    final Organization retrievedOrg = _organizationDao.findById(org.getId());
    assertEquals("Test", retrievedOrg.getName());
    final Invoice orgPendingInvoice = _invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());
    assertNotNull(orgPendingInvoice);
    assertFalse(org.getRestrictEmployeeAccess());

    final Organization ndsOrg =
        _ndsOrgSvc.createOrganization(
            "Test", GroupType.NDS, user, now, AuditInfoHelpers.fromInternal());
    final OrgPlan ndsPlan = _planSvc.getCurrentPlan(ndsOrg.getId());
    assertEquals(PlanType.NDS, ndsPlan.getPlanType());
    assertNull(ndsPlan.getFreeUntil());
    final Date expectedPlanStartDate = DateUtils.truncate(now, Calendar.MONTH);
    assertEquals(expectedPlanStartDate, ndsPlan.getStartDate());

    final Invoice ndsPendingInvoice = _invoiceSvc.getPendingMonthlyInvoiceByOrgId(ndsOrg.getId());
    assertNotNull(ndsPendingInvoice);
    assertFalse(ndsOrg.getRestrictEmployeeAccess());

    _appSettings.setProp(
        Fields.ALLOW_OPS_MANAGER_ORGS.value, Boolean.TRUE.toString(), SettingType.MEMORY);

    final Organization onPremOrg =
        _ndsOrgSvc.createOrganization(
            "Test", GroupType.ONPREM, user, now, AuditInfoHelpers.fromInternal());
    final OrgPlan onPremPlan = _planSvc.getCurrentPlan(onPremOrg.getId());
    assertEquals(PlanType.ONPREM, onPremPlan.getPlanType());
    assertNull(onPremPlan.getFreeUntil());
    final Invoice onPremPendingInvoice =
        _invoiceSvc.getPendingMonthlyInvoiceByOrgId(onPremOrg.getId());
    assertNotNull(onPremPendingInvoice);
    assertFalse(onPremOrg.getRestrictEmployeeAccess());

    try {
      _ndsOrgSvc.createOrganization(
          "", GroupType.CLOUD, user, now, AuditInfoHelpers.fromInternal());
      fail("Empty organization name should cause exception!");
    } catch (final SvcException e) {
      assertEquals(OrgErrorCode.INVALID_ORG_NAME, e.getErrorCode());
    }
  }

  @Test
  public void testCreateOrganization_withGlobalApiUser() throws Exception {
    final Date now = new Date();
    final AppUser globalApiKeyUser = _userDao.findById(oid(12));
    final Organization orgCreatedByGlobalApiKey =
        _ndsOrgSvc.createOrganization(
            "Test", GroupType.CLOUD, globalApiKeyUser, now, AuditInfoHelpers.fromInternal());
    // The org is successfully created
    assertNotNull(orgCreatedByGlobalApiKey.getId());
    assertEquals(oid(12), orgCreatedByGlobalApiKey.getCreator());

    // The global API key user should not be added to the org
    final AppUser updatedGlobalApiKeyUser = _userDao.findById(oid(12));
    assertFalse(updatedGlobalApiKeyUser.hasOrgId(orgCreatedByGlobalApiKey.getId()));
    assertFalse(
        _authzSvc.hasOrgRole(
            updatedGlobalApiKeyUser, Role.ORG_OWNER, orgCreatedByGlobalApiKey.getId()));
  }

  @Test
  public void testCreateOmOrganizationWhenNotAllowed() throws Exception {
    final AppUser user = _userDao.findById(oid(1));
    final SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                _ndsOrgSvc.createOrganization(
                    "OM org", GroupType.ONPREM, user, new Date(), AuditInfoHelpers.fromInternal()));
    assertEquals(OrgErrorCode.OPS_MANAGER_ORGS_DISALLOWED, exception.getErrorCode());
  }

  @Test
  public void testCreateOrganizationFromForm() throws Exception {
    final AppUser user = _userDao.findById(oid(1));
    final OrgCreationForm form = new OrgCreationForm("Test", GroupType.ONPREM, false, null);

    _appSettings.setProp(
        Fields.ALLOW_OPS_MANAGER_ORGS.value, Boolean.TRUE.toString(), SettingType.MEMORY);

    final Organization onPremOrg =
        _ndsOrgSvc.createOrganizationFromForm(form, user, AuditInfoHelpers.fromInternal());
    final OrgPlan onPremPlan = _planSvc.getCurrentPlan(onPremOrg.getId());
    assertEquals(PlanType.ONPREM, onPremPlan.getPlanType());
    assertNull(onPremPlan.getFreeUntil());
    final Invoice onPremPendingInvoice =
        _invoiceSvc.getPendingMonthlyInvoiceByOrgId(onPremOrg.getId());
    assertNotNull(onPremPendingInvoice);
    assertFalse(onPremOrg.getRestrictEmployeeAccess());
  }

  @Test
  public void testCreateOrganizationFromForm_setsSecurityContact() throws Exception {
    final AppUser user = _userDao.findById(oid(1));
    final String securityContact = "<EMAIL>";
    final OrgCreationForm form = new OrgCreationForm("Test", GroupType.NDS, false, securityContact);

    final Organization org =
        _ndsOrgSvc.createOrganizationFromForm(form, user, AuditInfoHelpers.fromInternal());
    assertEquals(securityContact, org.getSecurityContact());

    final List<Event> securityContactEvents =
        _auditSvc.findAll(
            1, Collections.singletonList(Type.SECURITY_CONTACT_MODIFIED.name()), null, null);
    assertEquals(1, securityContactEvents.size());
    assertThat(securityContactEvents.get(0), instanceOf(OrgAudit.class));
    final OrgAudit securityContactEvent = (OrgAudit) securityContactEvents.get(0);
    assertNull(securityContactEvent.getOldSecurityContact());
    assertEquals(securityContact, securityContactEvent.getNewSecurityContact());
  }

  @Test
  public void testRenameOrganization() throws Exception {
    final AppUser appUser = _userDao.findById(oid(1));
    final AuditInfo auditInfo =
        MmsFactory.createAuditInfoFromUiCall(
            appUser, _authzSvc.hasAnyGlobalRole(appUser), "testRemoteAddr");
    final Date now = new Date();
    _ndsOrgSvc.renameOrganization(oid(200), "Renamed", now, auditInfo);
    final Organization org = _organizationDao.findById(oid(200));
    assertEquals(now, org.getUpdated());
    assertEquals("Renamed", org.getName());
    assertEquals("renamed", org.getNameLowercase());
    assertEquals(Collections.singletonList("renamed"), org.getNameTokens());

    try {
      _ndsOrgSvc.renameOrganization(oid(200), "", now, auditInfo);
      fail("Empty organization name should cause exception!");
    } catch (final SvcException e) {
      assertEquals(OrgErrorCode.INVALID_ORG_NAME, e.getErrorCode());
    }
  }

  @Test
  public void testGetOrgsForNonGlobalUser() {
    final AppUser sevenOrganizationUser = _userDao.findById(oid(1));

    final List<OrgInfoView> sevenOrgs =
        _ndsOrgSvc.getOrgsForNonGlobalUser(
            sevenOrganizationUser, 40, false, false, false, RegionUsageRestrictions.NONE);
    assertEquals(8, sevenOrgs.size());

    final Set<ObjectId> sevenOrgIds =
        sevenOrgs.stream().map(OrgInfoView::getOrgId).collect(Collectors.toSet());
    assertEquals(
        Set.of(oid(200), oid(201), oid(202), oid(218), oid(225), oid(230), oid(231), oid(517)),
        sevenOrgIds);

    // Returns all groups that a user has access to either as ORG_READ_ONLY through an org,
    // as GROUP_READ_ONLY directly through group, or through a team
    final AppUser user6 = _userDao.findById(oid(6));
    final List<OrgInfoView> teamOrgs =
        _ndsOrgSvc.getOrgsForNonGlobalUser(
            user6, 40, false, false, false, RegionUsageRestrictions.NONE);
    Stream.of(oid(200), oid(202), oid(218))
        .forEach(
            orgId -> {
              assertTrue(teamOrgs.stream().anyMatch(orgInfo -> orgInfo.getOrgId().equals(orgId)));
            });
    assertEquals(
        Set.of(oid(100), oid(102), oid(126), oid(143), oid(118), oid(184), oid(188)),
        teamOrgs.stream()
            .flatMap(info -> info.getGroups().stream().map(GroupInfoView::getGroupId))
            .collect(Collectors.toSet()));
  }

  @Test
  public void testCreateOrganization_withSkipDefaultAlertsSettingsEnabled() throws Exception {
    final AppUser user = _userDao.findById(oid(1));
    final Date now = new Date();

    Organization org =
        _ndsOrgSvc.createOrganization(
            "Test", GroupType.CLOUD, user, now, true, false, AuditInfoHelpers.fromInternal());
    assertNotNull(org.getId());

    org = _organizationDao.findById(org.getId());

    assertEquals(oid(1), org.getCreator());
    assertEquals("Test", org.getName());
    assertEquals("test", org.getNameLowercase());
    assertEquals(Collections.singletonList("test"), org.getNameTokens());
    assertTrue(org.getGenAiFeaturesEnabled());
    assertTrue(org.isSkipDefaultAlertsSettingsEnabled());
  }
}
