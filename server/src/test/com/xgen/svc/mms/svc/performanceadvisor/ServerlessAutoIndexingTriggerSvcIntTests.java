package com.xgen.svc.mms.svc.performanceadvisor;

import static com.xgen.svc.common.MmsFactory.createGroupOwnerUser;
import static com.xgen.svc.common.MmsFactory.createGroupWithPremiumPlan;
import static com.xgen.svc.common.MmsFactory.createReplicaSet_V_2_4;
import static java.time.Duration.ofSeconds;
import static java.time.Instant.now;
import static java.util.Collections.singletonList;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._private.dao.JobsProcessorDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.performanceadvisor.SlowQueryLogEntry;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.AutoIndexingState;
import com.xgen.svc.mms.svc.ServerlessAutoIndexingSvc;
import com.xgen.svc.mms.svc.SlowQueryLogSvc;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ServerlessAutoIndexingTriggerSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private AppSettings _settings;
  @Inject private HostSvc _hostSvc;

  @Inject private SlowQueryLogSvc _slowQueryLogSvc;

  @Inject private ServerlessAutoIndexingTriggerSvc _serverlessAutoIndexingTriggerSvc;

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private JobsProcessorDao _jobsProcessorDao;

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private ServerlessAutoIndexingSvc _serverlessAutoIndexingSvc;

  @Inject private OrganizationSvc _organizationSvc;

  @Test
  void testScheduleServerlessAutoIndexingJobs() throws Exception {
    _settings.setProp(
        "mms.monitoring.performanceadvisor.serverless.autoindexing.cronJobEnabled",
        "true",
        AppSettings.SettingType.MEMORY);
    final Instant now = now();

    // MTM1: [tenant4, tenant1, tenant5]
    // MTM2: [tenant2, tenant6]
    // MTM3: [tenant3]
    // tenant: 4 -> 2 -> 3 -> 1 -> 6 -> 5
    final var params1 = setupForMultiTenantTests("1", true, null, null, null);
    final var params2 = setupForMultiTenantTests("2", true, null, null, null);
    final var params3 = setupForMultiTenantTests("3", true, null, null, null);
    final var params4 =
        setupForMultiTenantTests(
            "4", false, params1._mtmGroup, params1._mtmHostCluster, "mtmCluster1");
    final var params5 =
        setupForMultiTenantTests(
            "5", false, params1._mtmGroup, params1._mtmHostCluster, "mtmCluster1");
    final var params6 =
        setupForMultiTenantTests(
            "6", false, params2._mtmGroup, params2._mtmHostCluster, "mtmCluster2");
    final var params7 = setupForMultiTenantTests("7", true, null, null, null);

    final List<SlowQueryLogEntry> logEntries = new ArrayList<>();
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params1._tenantClusterDescription.getUniqueId(), now.minus(ofSeconds(60)), 60));
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params2._tenantClusterDescription.getUniqueId(), now.minus(ofSeconds(60)), 58));
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params3._tenantClusterDescription.getUniqueId(), now.minus(ofSeconds(60)), 55));
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params4._tenantClusterDescription.getUniqueId(), now.minus(ofSeconds(180)), 70));
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params5._tenantClusterDescription.getUniqueId(), now.minus(ofSeconds(360)), 52));
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params6._tenantClusterDescription.getUniqueId(), now.minus(ofSeconds(360)), 54));
    logEntries.addAll(
        generateSlowQueryLogEntry(
            params7._tenantClusterDescription.getUniqueId(),
            now.minus(ofSeconds(60 * 60 + 1)), // out of bound
            60));

    _slowQueryLogSvc.getDao().insertIa(logEntries);

    _serverlessAutoIndexingSvc.createConfigForCluster(
        params1._group.getId(),
        params1._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        params2._group.getId(),
        params2._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        params3._group.getId(),
        params3._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        params4._group.getId(),
        params4._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        params5._group.getId(),
        params5._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        params6._group.getId(),
        params6._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        params7._group.getId(),
        params7._tenantClusterDescription.getUniqueId(),
        AutoIndexingState.ENABLED);

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params1._group, params1._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params2._group, params2._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params3._group, params3._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params4._group, params4._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params5._group, params5._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params6._group, params6._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        params7._group, params7._org, FeatureFlag.SERVERLESS_AUTO_INDEXING);

    _serverlessAutoIndexingTriggerSvc.scheduleServerlessAutoIndexingJobs();

    final var jobs = _jobsProcessorDao.findAllAsList();

    Assertions.assertEquals(6, jobs.size());

    jobs.sort(Comparator.comparingLong(o -> (Long) o.get("scheduledFor")));

    for (int i = 1; i < 6; i++) {
      final long interval =
          (Long) jobs.get(i).get("scheduledFor") - (Long) jobs.get(i - 1).get("scheduledFor");
      Assertions.assertEquals(600000, interval); // each job should be 10 mins apart
    }

    assertJobClusterUniqueId(jobs.get(0), params4._tenantClusterDescription.getUniqueId());
    assertJobClusterUniqueId(jobs.get(1), params2._tenantClusterDescription.getUniqueId());
    assertJobClusterUniqueId(jobs.get(2), params3._tenantClusterDescription.getUniqueId());
    assertJobClusterUniqueId(jobs.get(3), params1._tenantClusterDescription.getUniqueId());
    assertJobClusterUniqueId(jobs.get(4), params6._tenantClusterDescription.getUniqueId());
    assertJobClusterUniqueId(jobs.get(5), params5._tenantClusterDescription.getUniqueId());
  }

  public void assertJobClusterUniqueId(final DBObject pJob, final ObjectId pClusterUniqueId) {
    final BasicDBObject params = (BasicDBObject) pJob.get("params");
    final ObjectId clusterUniqueId = (ObjectId) params.get("clusterUniqueId");
    Assertions.assertEquals(pClusterUniqueId, clusterUniqueId);
  }

  private static class MultiTenantParams {

    private final Group _group;
    private final Group _mtmGroup;
    private final Organization _org;
    private final AppUser _user;
    private final HostCluster _mtmHostCluster;
    private final ClusterDescription _tenantClusterDescription;

    private MultiTenantParams(
        final Group pGroup,
        final Group pMtmGroup,
        final Organization pOrg,
        final AppUser pAppUser,
        final HostCluster pMtmHostCluster,
        final ClusterDescription pTenantClusterDescription) {
      _group = pGroup;
      _mtmGroup = pMtmGroup;
      _org = pOrg;
      _user = pAppUser;
      _mtmHostCluster = pMtmHostCluster;
      _tenantClusterDescription = pTenantClusterDescription;
    }
  }

  private MultiTenantParams setupForMultiTenantTests(
      final String pSuffix,
      final Boolean pCreateMTM,
      final Group pMTMGroup,
      final HostCluster pMTMHostCluster,
      final String pBackingMTMClusterName) {
    final String tenantClusterName = "cluster" + pSuffix;
    final String backingMtmClusterName =
        pCreateMTM ? "mtmCluster" + pSuffix : pBackingMTMClusterName;
    final ObjectId replSpecId = new ObjectId();
    final ObjectId backupReplSpecId = new ObjectId();
    final Group mtmGroup;
    final HostCluster mtmHostCluster;

    final Group group = createGroupWithPremiumPlan();
    final Organization org = _organizationSvc.findById(group.getOrgId());
    if (pCreateMTM) {
      mtmGroup = createGroupWithPremiumPlan();
    } else {
      mtmGroup = pMTMGroup;
    }
    final AppUser user = createGroupOwnerUser(group);

    // there will always only be 2 hosts in the list getting sorted and only one will be primary, so
    // we just want the primary to come first in the list
    final Comparator<Host> hostComparator =
        (o1, o2) -> o1.getIsPrimary() ? -1 : o2.getIsPrimary() ? 1 : 0;

    final List<String> tenantHostnames;
    final ClusterDescription tenantClusterDescription;

    tenantClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getDefaultServerlessClusterDescription(
                group.getId(), tenantClusterName));
    _clusterDescriptionDao.save(tenantClusterDescription.toDBObject(), WriteConcern.ACKNOWLEDGED);
    final String[] uriHostnames = tenantClusterDescription.getMongoDBUriHosts();
    tenantHostnames =
        Arrays.asList(uriHostnames).stream()
            .map(HostUtils::extractHostname)
            .collect(Collectors.toUnmodifiableList());

    final BasicDBObject tenantReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(tenantClusterName, group.getId(), 0);
    _replicaSetHardwareDao.create(
        tenantReplicaSetHardwareId,
        ReplicaSetHardwareModelTestFactory.getRsIdForNonConfigShard(tenantClusterName, 0),
        true,
        false,
        replSpecId);

    final InstanceHostname.HostnameScheme hostnameSchemeForAgents =
        InstanceHostname.HostnameScheme.LEGACY;
    tenantHostnames.forEach(
        hn -> {
          final ObjectId instanceId =
              _replicaSetHardwareDao.addInstance(
                  tenantReplicaSetHardwareId, CloudProvider.SERVERLESS, false, 0);
          final Hostnames hostnames = new Hostnames(hn);
          _replicaSetHardwareDao.setInstanceField(
              tenantReplicaSetHardwareId,
              instanceId,
              false,
              InstanceHardware.FieldDefs.HOSTNAMES,
              hostnames.toDBList());
          _replicaSetHardwareDao.setInstanceField(
              tenantReplicaSetHardwareId,
              instanceId,
              false,
              InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
              hostnameSchemeForAgents.name());
        });

    if (pCreateMTM) {
      final BasicDBObject mtmReplicaSetHardwareId =
          ReplicaSetHardware.createNonConfigReplicaSetId(
              backingMtmClusterName, mtmGroup.getId(), 0);
      _replicaSetHardwareDao.create(
          mtmReplicaSetHardwareId,
          ReplicaSetHardwareModelTestFactory.getRsIdForNonConfigShard(backingMtmClusterName, 0),
          true,
          false,
          backupReplSpecId);

      mtmHostCluster = createReplicaSet_V_2_4(mtmGroup, backingMtmClusterName + "-shard-0", false);

      final List<Host> backingHosts = _hostSvc.findHostsByIds(mtmHostCluster, false);

      backingHosts.sort(hostComparator);

      backingHosts.forEach(
          h -> {
            final ObjectId instanceId =
                _replicaSetHardwareDao.addInstance(
                    mtmReplicaSetHardwareId, CloudProvider.AWS, false, 0);
            final Hostnames hostnames = new Hostnames(h.getName());
            _replicaSetHardwareDao.setInstanceField(
                mtmReplicaSetHardwareId,
                instanceId,
                false,
                InstanceHardware.FieldDefs.HOSTNAMES,
                hostnames.toDBList());
            _replicaSetHardwareDao.setInstanceField(
                mtmReplicaSetHardwareId,
                instanceId,
                false,
                InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                hostnameSchemeForAgents.name());
          });

      _clusterDescriptionDao.save(
          NDSModelTestFactory.getAWSClusterDescription(mtmGroup.getId(), backingMtmClusterName),
          WriteConcern.ACKNOWLEDGED);
    } else {
      mtmHostCluster = pMTMHostCluster;
    }

    _ndsGroupSvc.create(group.getId(), new NDSManagedX509(), false);
    final BasicDBObject containerDBObject = NDSModelTestFactory.getServerlessContainer();
    containerDBObject.put("id", group.getId());
    containerDBObject.put("tenantClusterName", tenantClusterName);
    final BasicDBObject clusterIdDBObject = (BasicDBObject) containerDBObject.get("clusterId");
    clusterIdDBObject.put("groupId", mtmGroup.getId());
    clusterIdDBObject.put("clusterName", backingMtmClusterName);
    TenantCloudProviderContainer container =
        new ServerlessCloudProviderContainer(containerDBObject);
    _ndsGroupDao.addCloudContainer(group.getId(), container);

    return new MultiTenantParams(
        group, mtmGroup, org, user, mtmHostCluster, tenantClusterDescription);
  }

  private List<SlowQueryLogEntry> generateSlowQueryLogEntry(
      final ObjectId pClusterUniqueId, final Instant pTimestamp, final int pN) {
    final List<SlowQueryLogEntry> entries = new ArrayList<>();
    for (int i = 0; i < pN; i++) {
      entries.add(generateSlowQueryLogEntry(pClusterUniqueId, null, pTimestamp));
    }
    return entries;
  }

  private SlowQueryLogEntry generateSlowQueryLogEntry(
      final ObjectId pClusterUniqueId, final ObjectId pGroupId, final Instant pTimestamp) {
    // no visible constructor for SlowQueryLogEntry, so just build it from a DBObject...
    BasicDBObject dbObject = new BasicDBObject();

    dbObject.put(SlowQueryLogEntry.CLUSTER_UNIQUE_ID_FIELD, pClusterUniqueId);
    dbObject.put(SlowQueryLogEntry.RECEIVED_TIMESTAMP_FIELD, Date.from(pTimestamp));
    dbObject.put(SlowQueryLogEntry.ADJUSTED_TIMESTAMP_FIELD, Date.from(pTimestamp));
    dbObject.put(SlowQueryLogEntry.INSTANCE_TYPE_FIELD, "SERVERLESS");

    dbObject.put(SlowQueryLogEntry.GROUP_ID_FIELD, pGroupId != null ? pGroupId : ObjectId.get());

    // the rest is fluff
    dbObject.put(SlowQueryLogEntry.ID_FIELD, new ObjectId());
    dbObject.put(SlowQueryLogEntry.VERSION_FIELD, 1);
    dbObject.put(SlowQueryLogEntry.ENCODING_FIELD, singletonList("DESede"));
    dbObject.put(SlowQueryLogEntry.NAMESPACE_FIELD, pClusterUniqueId + "_mytestdb.mycoll");
    dbObject.put(SlowQueryLogEntry.IS_USABLE_FIELD, true);
    dbObject.put(SlowQueryLogEntry.DATA_FIELD, "bogusplaceholderdata");
    dbObject.put(SlowQueryLogEntry.RANDOM_FIELD, ThreadLocalRandom.current().nextDouble());

    return SlowQueryLogEntry.fromDBObject(dbObject);
  }
}
