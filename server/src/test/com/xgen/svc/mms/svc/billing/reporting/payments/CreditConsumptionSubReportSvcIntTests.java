package com.xgen.svc.mms.svc.billing.reporting.payments;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.model._public.email.TemplateMap;
import com.xgen.cloud.email._public.svc.template.HandlebarsTemplateSvc;
import com.xgen.cloud.payments.reports._public.model.ReportTemplate;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.LineItemFactory;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class CreditConsumptionSubReportSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private HandlebarsTemplateSvc templateSvc;

  @Inject private CreditConsumptionSubReportSvc creditsSubReportSvc;

  @Inject private LineItemFactory lineItemFactory;

  @Inject private CreditFactory creditFactory;

  @Test
  public void testCreditConsumptionReport() throws Exception {
    ObjectId orgId = new ObjectId();
    LocalDate localNow = LocalDate.of(2022, 1, 1);
    Date now = Date.from(localNow.atStartOfDay().toInstant(ZoneOffset.UTC));

    Date startOfToday = DateUtils.truncate(now, Calendar.DATE);
    Date startOfYesterday = DateUtils.addDays(startOfToday, -1);
    Date twoDaysAgo = DateUtils.addDays(startOfToday, -2);
    Date threeDaysAgo = DateUtils.addDays(startOfToday, -3);
    Date fourDaysAgo = DateUtils.addDays(startOfToday, -4);
    Date fiveDaysAgo = DateUtils.addDays(startOfToday, -5);
    Date sixDaysAgo = DateUtils.addDays(startOfToday, -6);
    Date sevenDaysAgo = DateUtils.addDays(startOfToday, -7);
    Date eightDaysAgo = DateUtils.addDays(startOfToday, -8);
    Date oneYearAgo = DateUtils.addYears(startOfToday, -1);
    Date oneYearFromNow = DateUtils.addYears(startOfToday, 1);

    ObjectId invoiceId = new ObjectId();
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();
    long yesterdayAmountCents = 1000;
    long twoDaysAgoAmountCents = 100;
    long threeDaysAgoAmountCents = 400;
    long fourDaysAgoAmountCents = 800;
    long fiveDaysAgoAmountCents = 80;
    long sixDaysAgoAmountCents = 40;
    long sevenDaysAgoAmountCents = 20;
    long eightDaysAgoAmountCents = 10000;

    Credit genericCredit =
        creditFactory.createGenericCredit(orgId, oneYearAgo, oneYearFromNow, 400000, true);
    Credit promoCredit =
        creditFactory.createPromoCredit(orgId, oneYearAgo, oneYearFromNow, 400000, true);
    Credit prepaidCredit1 =
        creditFactory.createPrepaidCredit(orgId, oneYearAgo, oneYearFromNow, 400000, true);
    Credit prepaidCredit2 =
        creditFactory.createPrepaidCredit(orgId, oneYearAgo, oneYearFromNow, 400000, true);

    // 2 line items from same generic credit on the same day
    lineItemFactory.createCreditLineItem(
        invoice, yesterdayAmountCents / 4, startOfYesterday, genericCredit.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (yesterdayAmountCents / 4) * 3, startOfYesterday, genericCredit.getId(), false);

    // 2 line items from different credits on the same day
    lineItemFactory.createCreditLineItem(
        invoice, twoDaysAgoAmountCents / 4, twoDaysAgo, genericCredit.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (twoDaysAgoAmountCents / 4) * 3, twoDaysAgo, promoCredit.getId(), false);

    // 4 line items from different prepaid credits credits on the same day
    lineItemFactory.createCreditLineItem(
        invoice, threeDaysAgoAmountCents / 4, threeDaysAgo, prepaidCredit1.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (threeDaysAgoAmountCents / 4), threeDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (threeDaysAgoAmountCents / 4), threeDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (threeDaysAgoAmountCents / 4), threeDaysAgo, prepaidCredit2.getId(), false);

    // 4 line items from different prepaid credits and generic credits on the same day
    lineItemFactory.createCreditLineItem(
        invoice, fourDaysAgoAmountCents / 4, fourDaysAgo, prepaidCredit1.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (fourDaysAgoAmountCents / 4), fourDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (fourDaysAgoAmountCents / 4), fourDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (fourDaysAgoAmountCents / 4), fourDaysAgo, genericCredit.getId(), false);

    // line items from each credit
    lineItemFactory.createCreditLineItem(
        invoice, fiveDaysAgoAmountCents / 4, fiveDaysAgo, prepaidCredit1.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (fiveDaysAgoAmountCents / 4), fiveDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (fiveDaysAgoAmountCents / 4), fiveDaysAgo, promoCredit.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (fiveDaysAgoAmountCents / 4), fiveDaysAgo, genericCredit.getId(), false);

    // line items from each credit
    lineItemFactory.createCreditLineItem(
        invoice, sixDaysAgoAmountCents / 4, sixDaysAgo, prepaidCredit1.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (sixDaysAgoAmountCents / 4), sixDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (sixDaysAgoAmountCents / 4), sixDaysAgo, promoCredit.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (sixDaysAgoAmountCents / 4), sixDaysAgo, genericCredit.getId(), false);

    // line items from each credit
    lineItemFactory.createCreditLineItem(
        invoice, sevenDaysAgoAmountCents / 4, sevenDaysAgo, prepaidCredit1.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (sevenDaysAgoAmountCents / 4), sevenDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (sevenDaysAgoAmountCents / 4), sevenDaysAgo, promoCredit.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (sevenDaysAgoAmountCents / 4), sevenDaysAgo, genericCredit.getId(), false);

    // line items from each credit
    lineItemFactory.createCreditLineItem(
        invoice, eightDaysAgoAmountCents / 4, eightDaysAgo, prepaidCredit1.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (eightDaysAgoAmountCents / 4), eightDaysAgo, prepaidCredit2.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (eightDaysAgoAmountCents / 4), eightDaysAgo, promoCredit.getId(), false);
    lineItemFactory.createCreditLineItem(
        invoice, (eightDaysAgoAmountCents / 4), eightDaysAgo, genericCredit.getId(), false);

    TemplateMap templateMap = creditsSubReportSvc.generate(now);
    String output = render(ReportTemplate.CREDIT_CONSUMPTION_SUB_REPORT, templateMap);

    // todo: refactor to parse and verify html output with jsoup
    assertThat(
        output,
        equalTo(
            "<h3>Credit Consumption Over 7 Days</h3>\n"
                + "<p>\n"
                + "This report shows credits consumed by type over the past 7 days. For each day"
                + " and credit type, we show two numbers: the distinct number of credits and the"
                + " total amount of credit consumed.\n"
                + "</p>\n"
                + "<table border=\"1\" cellpadding=\"10\" style=\"border-collapse:collapse;\">\n"
                + "    <tr>\n"
                + "      <th><p>Credit Type</p></th>\n"
                + "      \n"
                + "       <th>2021-12-31<br></th>\n"
                + "      \n"
                + "       <th>2021-12-30<br></th>\n"
                + "      \n"
                + "       <th>2021-12-29<br></th>\n"
                + "      \n"
                + "       <th>2021-12-28<br></th>\n"
                + "      \n"
                + "       <th>2021-12-27<br></th>\n"
                + "      \n"
                + "       <th>2021-12-26<br></th>\n"
                + "      \n"
                + "       <th>2021-12-25<br></th>\n"
                + "      \n"
                + "\n"
                + "    </tr>\n"
                + "    \n"
                + "   <tr>\n"
                + "      <td><p>GCP_FLEX_COMMIT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GCP_BILLING_ACCOUNT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>VERCEL_SELF_SERVE</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AZURE_FLEX_COMMIT_ROLLOVER</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AZURE_FLEX_COMMIT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>PREPAID_NDS</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        2 / -$4.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        2 / -$6.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        2 / -$0.40<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        2 / -$0.20<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        2 / -$0.10<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>DIRECT_FLEX_COMMIT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AWS_FLEX_COMMIT_ROLLOVER</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>ROLLOVER</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>PROMO</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        1 / -$0.75<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$0.20<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$0.10<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$0.05<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AWS_MONTHLY_COMMITMENT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GCP_MONTHLY_COMMITMENT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>DIRECT_FLEX_COMMIT_ROLLOVER</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AZURE_MONTHLY_COMMITMENT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AZURE_SELF_SERVE</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GCP_SELF_SERVE</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>MONTHLY_COMMITMENT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GCP_PREPAID</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GCP_MONTHLY_COMMITMENT_ROLLOVER</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AWS_SELF_SERVE</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GENERIC</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$10.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        1 / -$0.25<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$2.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$0.20<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$0.10<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        bgcolor=\"#ffc1b4\"\n"
                + "      >\n"
                + "        1 / -$0.05<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AWS_PREPAID</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AWS_FLEX_COMMIT</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>AZURE_PREPAID</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "   <tr>\n"
                + "      <td><p>GCP_FLEX_COMMIT_ROLLOVER</p></td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "      <td\n"
                + "        \n"
                + "        \n"
                + "      >\n"
                + "        0 / $0.00<br>\n"
                + "      </td>\n"
                + "      \n"
                + "   </tr>\n"
                + "   \n"
                + "</table>\n"));
    verify(
        templateMap,
        CreditType.GENERIC,
        List.of(
            -yesterdayAmountCents,
            -twoDaysAgoAmountCents / 4,
            0L,
            -(fourDaysAgoAmountCents / 4),
            -(fiveDaysAgoAmountCents / 4),
            -(sixDaysAgoAmountCents / 4),
            -sevenDaysAgoAmountCents / 4),
        List.of(1L, 1L, 0L, 1L, 1L, 1L, 1L),
        List.of(39.0, 0.0, 1.0, 9.0, 1.0, 1.0, 0.998));
    verify(
        templateMap,
        CreditType.PROMO,
        List.of(
            0L,
            -(twoDaysAgoAmountCents / 4) * 3,
            0L,
            0L,
            -(fiveDaysAgoAmountCents / 4),
            -(sixDaysAgoAmountCents / 4),
            -sevenDaysAgoAmountCents / 4),
        List.of(0L, 1L, 0L, 0L, 1L, 1L, 1L),
        List.of(1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.998));
    verify(
        templateMap,
        CreditType.PREPAID_NDS,
        List.of(
            0L,
            0L,
            -threeDaysAgoAmountCents,
            -(fourDaysAgoAmountCents / 4) * 3,
            -(fiveDaysAgoAmountCents / 4) * 2,
            -(sixDaysAgoAmountCents / 4) * 2,
            -(sevenDaysAgoAmountCents / 4) * 2),
        List.of(0L, 0L, 2L, 2L, 2L, 2L, 2L),
        List.of(0.0, 1.0, 0.3333333333333333, 14.0, 1.0, 1.0, 0.998));

    verify(
        templateMap,
        CreditType.ROLLOVER,
        List.of(0L, 0L, 0L, 0L, 0L, 0L, 0L),
        List.of(0L, 0L, 0L, 0L, 0L, 0L, 0L),
        List.of(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0));
  }

  @SuppressWarnings("unchecked")
  private void verify(
      TemplateMap pTemplateMap,
      CreditType pCreditType,
      List<Long> expectedAmounts,
      List<Long> expectedCounts,
      List<Double> expectedPercentChange) {
    List<Map<String, Number>> cells =
        (List<Map<String, Number>>)
            ((Map<String, Object>) pTemplateMap.getData().get("data")).get(pCreditType.name());

    assertEquals(expectedAmounts.size(), cells.size());
    assertEquals(expectedCounts.size(), cells.size());
    assertEquals(expectedPercentChange.size(), cells.size());

    List<Long> observedAmounts =
        cells.stream().map(cell -> cell.get("amount").longValue()).collect(Collectors.toList());
    assertThat(observedAmounts, equalTo(expectedAmounts));

    List<Long> observedCounts =
        cells.stream().map(cell -> cell.get("count").longValue()).collect(Collectors.toList());
    assertThat(observedCounts, equalTo(expectedCounts));

    List<Double> observedPctChange =
        cells.stream()
            .map(cell -> cell.get("pctChange").doubleValue())
            .collect(Collectors.toList());
    assertThat(observedPctChange, equalTo(expectedPercentChange));
  }

  private String render(ReportTemplate template, TemplateMap templateMap) {
    return templateSvc.render(template, templateMap, null);
  }
}
