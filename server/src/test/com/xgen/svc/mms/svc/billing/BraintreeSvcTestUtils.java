package com.xgen.svc.mms.svc.billing;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import com.braintreegateway.BraintreeGateway;
import com.braintreegateway.Customer;
import com.braintreegateway.CustomerRequest;
import com.braintreegateway.PaymentMethod;
import com.braintreegateway.PaymentMethodRequest;
import com.braintreegateway.Result;
import com.braintreegateway.ValidationError;
import com.braintreegateway.exceptions.NotFoundException;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.group._public.model.BillingAddress;
import com.xgen.svc.mms.model.billing.BraintreeCustomer;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Optional;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BraintreeSvcTestUtils {
  private static final Logger LOG = LoggerFactory.getLogger(BraintreeSvcTestUtils.class);

  // These constants are defined by Braintree.
  public static final String FAKE_PAYPAL_NONCE = "fake-paypal-billing-agreement-nonce";
  public static final String FAKE_PAYPAL_EMAIL_ADDRESS = "<EMAIL>";
  private static final String US_ADDRESS_LINE1 = "1633 Broadway";
  private static final String US_ADDRESS_LINE2 = "38th Floor";
  private static final String US_ADDRESS_CITY = "New York";
  private static final String US_ADDRESS_STATE = "New York";
  private static final String US_ADDRESS_ZIP_CODE = "10019";
  private static final String US_ADDRESS_COUNTRY = "US";

  private final BraintreeGateway _braintreeGateway;
  private final SimpleBraintreeCustomerDao braintreeCustomerDao;
  private ObjectId _existingCustomerId;
  private ObjectId _nonExistingCustomerId;
  private String _paymentMethodToken;
  private BillingAddress _billingAddress;

  @Inject
  public BraintreeSvcTestUtils(
      BraintreeGateway braintreeGateway, SimpleBraintreeCustomerDao braintreeCustomerDao) {
    this._braintreeGateway = braintreeGateway;
    this.braintreeCustomerDao = braintreeCustomerDao;
  }

  public void setUpVault(ObjectId pExistingCustomerId, ObjectId pNonExistingCustomerId) {
    braintreeCustomerDao.deleteManyMajority(new Document());

    deleteCustomerIfExist(pExistingCustomerId);
    deleteCustomerIfExist(pNonExistingCustomerId);
    createCustomer(pExistingCustomerId);
    _existingCustomerId = pExistingCustomerId;
    _nonExistingCustomerId = pNonExistingCustomerId;

    _billingAddress = createBillingAddress();
    _paymentMethodToken = createPaymentMethod(pExistingCustomerId);

    braintreeCustomerDao.insertOneReplicaSafe(
        BraintreeCustomer.builder()
            .orgId(pExistingCustomerId)
            .paymentMethodToken(_paymentMethodToken)
            .payPalEmailAddress(FAKE_PAYPAL_EMAIL_ADDRESS)
            .build());
  }

  public void tearDownVault() {
    if (_existingCustomerId != null) {
      deleteCustomerIfExist(_existingCustomerId);
    }
    if (_nonExistingCustomerId != null) {
      deleteCustomerIfExist(_nonExistingCustomerId);
    }
  }

  public String getPaymentMethodToken() {
    return _paymentMethodToken;
  }

  public void createCustomer(ObjectId pOrgId) {
    CustomerRequest customerRequest = new CustomerRequest().id(pOrgId.toString());
    Result<Customer> customerResult = _braintreeGateway.customer().create(customerRequest);
    if (!customerResult.isSuccess()) {
      LOG.error(
          "Failed creating a customer: orgId={} message={}", pOrgId, customerResult.getMessage());
      for (ValidationError error : customerResult.getErrors().getAllDeepValidationErrors()) {
        LOG.error(
            "Failed validating new customer: orgId={} code={} message={} attribute={}",
            pOrgId,
            error.getCode(),
            error.getMessage(),
            error.getAttribute());
      }
    }
    assertThat(customerResult.isSuccess(), is(true));
    assertThat(customerResult.getTarget().getId(), is(pOrgId.toString()));
  }

  String createPaymentMethod(ObjectId pOrgId) {
    PaymentMethodRequest paymentMethodRequest =
        new PaymentMethodRequest()
            .customerId(pOrgId.toString())
            .paymentMethodNonce(FAKE_PAYPAL_NONCE);
    Result<? extends PaymentMethod> paymentMethodResult =
        _braintreeGateway.paymentMethod().create(paymentMethodRequest);
    assertThat(paymentMethodResult.getMessage(), paymentMethodResult.isSuccess(), is(true));
    assertThat(paymentMethodResult.getTarget().getCustomerId(), is(pOrgId.toString()));
    assertThat(paymentMethodResult.getTarget().getToken(), is(notNullValue()));
    return paymentMethodResult.getTarget().getToken();
  }

  void deleteCustomer(ObjectId pOrgId) {
    _braintreeGateway.customer().delete(pOrgId.toString());
  }

  void deleteCustomerIfExist(ObjectId pOrgId) {
    try {
      deleteCustomer(pOrgId);
    } catch (NotFoundException pE) {
      // Ignore if such a customer does not exist.
    }
  }

  Optional<Customer> findCustomer(ObjectId pOrgId) {
    try {
      return Optional.of(_braintreeGateway.customer().find(pOrgId.toString()));
    } catch (NotFoundException pE) {
      return Optional.empty();
    }
  }

  public static BillingAddress createBillingAddress() {
    return new BillingAddress.Builder()
        .addressLineOne(US_ADDRESS_LINE1)
        .addressLineTwo(US_ADDRESS_LINE2)
        .city(US_ADDRESS_CITY)
        .state(US_ADDRESS_STATE)
        .country(US_ADDRESS_COUNTRY)
        .zip(US_ADDRESS_ZIP_CODE)
        .build();
  }

  public ObjectId getExistingCustomerId() {
    return _existingCustomerId;
  }

  public ObjectId getNonExistingCustomerId() {
    return _nonExistingCustomerId;
  }

  public BillingAddress getBillingAddress() {
    return _billingAddress;
  }

  /** Simple dao to be available for MMS and standalone tests. */
  @Singleton
  public static class SimpleBraintreeCustomerDao extends BaseDao<BraintreeCustomer> {
    @Inject
    public SimpleBraintreeCustomerDao(MongoClientContainer container, CodecRegistry codecRegistry) {
      super(container, BraintreeCustomer.DB_NAME, BraintreeCustomer.COLLECTION_NAME, codecRegistry);
    }
  }
}
