package com.xgen.svc.mms.svc.billing.audit.auditors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditError;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class InvoicePaymentAuditorIntTests extends JUnit5BaseSvcTest {

  @Inject private InvoicePaymentAuditor invoicePaymentAuditor;

  @Inject private InvoiceFactory invoiceFactory;

  @Inject private InvoiceDao invoiceDao;

  @Inject private PaymentDao paymentDao;

  @Test
  public void auditPass_nonZeroPaidAmounts() {
    int amountPaid = 100000;
    Invoice invoice =
        new Invoice.Builder()
            .status(Invoice.Status.PAID)
            .amountBilledCents(2 * amountPaid)
            .amountPaidCents(2 * amountPaid)
            .build();
    invoiceDao.save(invoice);
    Payment payment =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Payment.Status.PAID)
            .amountBilledCents(amountPaid)
            .amountPaidCents(amountPaid)
            .build();
    paymentDao.save(payment);

    Payment payment_mc =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Status.INVOICED)
            .paymentMethod(PaymentMethodType.MONTHLY_COMMITMENT)
            .amountBilledCents(amountPaid)
            .amountPaidCents(amountPaid)
            .build();
    paymentDao.save(payment_mc);

    Optional<AuditFailureDetail> result = invoicePaymentAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_forgivenPayment_zeroPaidAmounts() {
    ObjectId orgId = new ObjectId();
    Date startDate = TimeUtils.fromISOString("2021-11-01");
    Date endDate = TimeUtils.fromISOString("2021-11-30");
    Pair<Invoice, Payment> pair =
        invoiceFactory.createInvoiceWithPayment(
            startDate,
            endDate,
            orgId,
            0,
            100000,
            0,
            Invoice.Status.CLOSED,
            Payment.Status.FORGIVEN,
            null);
    assertEquals(0, pair.getLeft().getAmountPaidCents());
    assertEquals(0, pair.getRight().getAmountPaidCents());
    Optional<AuditFailureDetail> result = invoicePaymentAuditor.audit(pair.getLeft());
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_cancelledAndSuccessfulPayments() {
    int amountPaid = 100000;
    Invoice invoice =
        new Invoice.Builder()
            .status(Invoice.Status.PAID)
            .amountBilledCents(amountPaid)
            .amountPaidCents(amountPaid)
            .build();
    invoiceDao.save(invoice);
    Payment successfulPayment =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Payment.Status.PAID)
            .amountBilledCents(80000)
            .amountPaidCents(80000)
            .build();
    Payment successfulPayment2 =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Payment.Status.PAID)
            .amountBilledCents(20000)
            .amountPaidCents(20000)
            .build();
    Payment failedPayment =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Payment.Status.FAILED)
            .amountBilledCents(amountPaid)
            .amountPaidCents(amountPaid)
            .build();
    paymentDao.save(successfulPayment);
    paymentDao.save(successfulPayment2);
    paymentDao.save(failedPayment);
    Optional<AuditFailureDetail> result = invoicePaymentAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_paidAndInvoicedPayments() {
    int amountPaid = 100000;
    Invoice invoice =
        new Invoice.Builder()
            .status(Invoice.Status.PAID)
            .amountBilledCents(amountPaid)
            .amountPaidCents(amountPaid)
            .build();
    invoiceDao.save(invoice);
    Payment successfulPayment =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Payment.Status.PAID)
            .amountBilledCents(80000)
            .amountPaidCents(80000)
            .build();
    Payment successfulPayment2 =
        new Payment.Builder()
            .invoiceId(invoice.getId())
            .status(Payment.Status.INVOICED)
            .amountBilledCents(20000)
            .amountPaidCents(20000)
            .build();

    paymentDao.save(successfulPayment);
    paymentDao.save(successfulPayment2);
    Optional<AuditFailureDetail> result = invoicePaymentAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditFail() {
    int amountPaid = 100000;
    ObjectId orgId = ObjectId.get();
    Invoice invoice =
        new Invoice.Builder()
            .orgId(orgId)
            .status(Invoice.Status.PAID)
            .amountBilledCents(amountPaid)
            .amountPaidCents(amountPaid)
            .build();
    invoiceDao.save(invoice);
    int paymentPaidAmount = amountPaid - 1;
    Payment payment =
        new Payment.Builder()
            .status(Payment.Status.PAID)
            .invoiceId(invoice.getId())
            .amountBilledCents(paymentPaidAmount)
            .amountPaidCents(paymentPaidAmount)
            .build();
    paymentDao.save(payment);
    Optional<AuditFailureDetail> result = invoicePaymentAuditor.audit(invoice);
    assertTrue(result.isPresent());
    AuditFailureDetail auditFailureDetail = result.get();
    assertEquals(invoicePaymentAuditor.getAuditorName(), auditFailureDetail.getAuditorName());
    assertEquals(invoice.getId(), auditFailureDetail.getAuditContext().getInvoiceId());
    assertEquals(orgId, auditFailureDetail.getAuditContext().getOrgId());
    List<BillingAuditError> errorCodes = auditFailureDetail.getBillingAuditErrors();
    assertEquals(1, errorCodes.size());
    assertEquals(
        BillingAuditErrorCode.INVOICE_PAID_AMOUNT_DOES_NOT_EQUAL_PAID_AMOUNT,
        errorCodes.get(0).getErrorCode());
    String expectedErrorMsg =
        String.format(
            BillingAuditErrorCode.INVOICE_PAID_AMOUNT_DOES_NOT_EQUAL_PAID_AMOUNT.getTemplate(),
            amountPaid,
            paymentPaidAmount);
    assertEquals(expectedErrorMsg, errorCodes.get(0).getErrorMessage());
  }
}
