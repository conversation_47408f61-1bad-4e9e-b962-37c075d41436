package com.xgen.svc.mms.svc.billing.monitoring.impl;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.ActiveInvoiceMetricAggregator.BILLING_ACTIVE_INVOICES_COUNT_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.svc.billing.monitoring.BillingMetricFailureException;
import io.prometheus.client.CollectorRegistry;
import jakarta.inject.Inject;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ActiveInvoiceMetricAggregatorIntTests extends JUnit5BaseSvcTest {

  private ActiveInvoiceMetricAggregator activeInvoiceMetricAggregator;

  @Inject private LineItemDao lineItemDao;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    activeInvoiceMetricAggregator = new ActiveInvoiceMetricAggregator(lineItemDao, clock);
  }

  @Test
  void testEmitMetrics() {
    mockClockTime(Instant.parse("2025-04-01T00:00:00.00Z"));
    // create line items for 3/31
    createMockLineItemForInvoiceIdsForDay(ObjectId.get(), dateOf(LocalDate.of(2025, 3, 31)));
    createMockLineItemForInvoiceIdsForDay(ObjectId.get(), dateOf(LocalDate.of(2025, 3, 31)));
    createMockLineItemForInvoiceIdsForDay(ObjectId.get(), dateOf(LocalDate.of(2025, 3, 31)));
    // create line items for 4/1
    createMockLineItemForInvoiceIdsForDay(ObjectId.get(), dateOf(LocalDate.of(2025, 4, 1)));
    createMockLineItemForInvoiceIdsForDay(ObjectId.get(), dateOf(LocalDate.of(2025, 4, 1)));

    try {
      activeInvoiceMetricAggregator.emitMetrics();
    } catch (BillingMetricFailureException e) {
      fail("No exception should be thrown when emitting metrics");
    }

    double gaugeValue =
        CollectorRegistry.defaultRegistry.getSampleValue(BILLING_ACTIVE_INVOICES_COUNT_NAME);
    assertEquals(3, gaugeValue, 0);

    mockClockTime(Instant.parse("2025-04-02T00:00:00.00Z"));

    try {
      activeInvoiceMetricAggregator.emitMetrics();
    } catch (BillingMetricFailureException e) {
      fail("No exception should be thrown when emitting metrics");
    }

    gaugeValue =
        CollectorRegistry.defaultRegistry.getSampleValue(BILLING_ACTIVE_INVOICES_COUNT_NAME);
    assertEquals(2, gaugeValue, 0);
  }

  private void createMockLineItemForInvoiceIdsForDay(ObjectId invoiceId, Date date) {
    lineItemDao.save(
        new LineItem.Builder()
            .invoiceId(invoiceId)
            .startDate(date)
            .sku(SKU.NDS_AWS_INSTANCE_M10)
            .totalPriceCents(100L)
            .build());
  }
}
