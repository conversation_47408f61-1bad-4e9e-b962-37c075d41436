package com.xgen.svc.mms.svc.marketing;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.sforce.soap.enterprise.sobject.Lead;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.mms.dao.billing.SalesforceCachedIdDao;
import com.xgen.svc.mms.model.billing.SalesforceCachedId;
import jakarta.inject.Inject;
import java.util.Calendar;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SalesforceLeadSyncSvcSfdcTests extends BaseSfdcTest {

  private static final Logger LOG = LoggerFactory.getLogger(SalesforceLeadSyncSvcSfdcTests.class);

  @Inject private SalesforceLeadSyncSvc _salesforceLeadSyncSvc;

  @Inject private MarketingIntegrationSvc _marketingIntegrationSvc;

  @Inject private SalesforceCachedIdDao _salesforceCachedIdDao;

  @Inject private UserDao _userDao;
  @Inject private AppSettings appSettings;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    createOrgGroupAndUser();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
  }

  @Test
  public void testCreateLeadNew() throws Exception {
    // Create Lead
    String leadId = _salesforceLeadSyncSvc.ensureLead(_user, false, null, true);
    _user = _userDao.findByUsername(_user.getUsername());
    assertEquals(leadId, _user.getSalesforceLeadId());
    LOG.warn("Created Lead with id=" + leadId);
  }

  @Test
  public void testCreateAndUpdateLeadNewCached() throws Exception {
    SalesforceIdCache salesforceIdCache =
        new SalesforceIdCache(_marketingIntegrationSvc, _salesforceCachedIdDao, appSettings);
    // Create Lead
    String leadId = _salesforceLeadSyncSvc.ensureLead(_user, false, salesforceIdCache, true);
    _user = _userDao.findByUsername(_user.getUsername());
    assertEquals(leadId, _user.getSalesforceLeadId());
    LOG.warn("Created Lead with id=" + leadId);
    Lead lead = _salesforceApiSvc.getLeadWithModifiedDate(_user.getSalesforceLeadId());
    Calendar firstModifiedDate = lead.getLastModifiedDate();

    // Now test update with cache
    SalesforceCachedId cachedLeadId = new SalesforceCachedId.Builder().setLeadId(leadId).build();
    salesforceIdCache.getSalesforceCachedIdDao().findOrCreate(cachedLeadId, true);

    // update Lead. It should not push an update to salesforce
    String leadId2ndTime = _salesforceLeadSyncSvc.ensureLead(_user, false, salesforceIdCache, true);
    assertEquals(leadId, leadId2ndTime);
    _user = _userDao.findByUsername(_user.getUsername());
    assertEquals(leadId, _user.getSalesforceLeadId());
    lead = _salesforceApiSvc.getLeadWithModifiedDate(_user.getSalesforceLeadId());
    Calendar secondModifiedDate = lead.getLastModifiedDate();
    assertTrue(secondModifiedDate.compareTo(firstModifiedDate) == 0);

    // Test with cache but skip update, verify it doesn't cause an update
    String leadId3rdTime = _salesforceLeadSyncSvc.ensureLead(_user, true, salesforceIdCache, true);
    assertEquals(leadId2ndTime, leadId3rdTime);
    _user = _userDao.findByUsername(_user.getUsername());
    assertEquals(leadId, _user.getSalesforceLeadId());
    lead = _salesforceApiSvc.getLeadWithModifiedDate(_user.getSalesforceLeadId());
    Calendar thirdModifiedDate = lead.getLastModifiedDate();
    assertEquals(secondModifiedDate, thirdModifiedDate);
  }

  @Test
  public void testUpdateLeadNullCache() throws Exception {
    // Create Lead
    String leadId = _salesforceLeadSyncSvc.ensureLead(_user, false, null, true);
    _user = _userDao.findByUsername(_user.getUsername());

    // Update Lead
    String newLeadId = _salesforceLeadSyncSvc.ensureLead(_user, false, null, true);
    _user = _userDao.findByUsername(_user.getUsername());
    assertEquals(newLeadId, _user.getSalesforceLeadId());
  }

  @Test
  public void testUpdateLeadNullCacheSkipUpdate() throws Exception {
    // Create Lead
    String leadId = _salesforceLeadSyncSvc.ensureLead(_user, true, null, true);
    _user = _userDao.findByUsername(_user.getUsername());

    // Update Lead
    String newLeadId = _salesforceLeadSyncSvc.ensureLead(_user, true, null, true);
    _user = _userDao.findByUsername(_user.getUsername());
    assertEquals(newLeadId, _user.getSalesforceLeadId());
  }

  @Test
  public void testLeadNotCreated() throws Exception {
    // Should not create Lead
    String leadId = _salesforceLeadSyncSvc.ensureLead(_user, false, null, false);
    _user = _userDao.findByUsername(_user.getUsername());
    assertNull(leadId);
    assertNull(_user.getSalesforceLeadId());
  }

  @Test
  public void syncLead_missingLastName() throws Exception {
    _user.setLastName(null);
    _userDao.save(_user);
    assertNull(_user.getLastName());
    _salesforceLeadSyncSvc.trySyncLead(_user, true, null, true);
    _user = _userDao.findByUsername(_user.getUsername());
    assertNull(
        "Last name was set to [not provided] before syncing but should not have been "
            + "saved in the db",
        _user.getLastName());
    // try syncing again
    _salesforceLeadSyncSvc.trySyncLead(_user, true, null, true);
    _user = _userDao.findByUsername(_user.getUsername());
    assertNull(
        "Last name was set to [not provided] before the second syncing but should not have been "
            + "saved in the db",
        _user.getLastName());
  }
}
