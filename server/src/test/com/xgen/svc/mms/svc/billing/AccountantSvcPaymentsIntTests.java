package com.xgen.svc.mms.svc.billing;

import static com.xgen.cloud.common.jobqueue._public.svc.BaseObjectJobHandler.OBJECT_IDS_FIELD;
import static com.xgen.svc.mms.svc.billing.ActivationCodeRetroactiveApplicationJobHandler.ACTIVATED_DATE;
import static com.xgen.svc.mms.svc.billing.ActivationCodeRetroactiveApplicationJobHandler.ACTIVATION_CODE;
import static com.xgen.svc.mms.svc.billing.ActivationCodeRetroactiveApplicationJobHandler.AUDIT_INFO;
import static com.xgen.svc.mms.svc.billing.ActivationCodeRetroactiveApplicationJobHandler.CREDIT_PULLED_FORWARD;
import static com.xgen.svc.mms.svc.billing.ActivationCodeRetroactiveApplicationJobHandler.ORGANIZATION;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.plan._public.model.SubscriptionPricingModel;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.process.orchestration._private.svc.lastbilldate.LastBillDateSvcImpl;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfoUtils;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.partners.usage.aws._public.svc.AwsApiSvc;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.grpc._public.client.RefundQueryingClient;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.dao.billing.OrgPrepaidPlanDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.dao.billing.SubscriptionUsageDaoV2;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.ApplyActivationCodeRequest;
import com.xgen.svc.mms.model.billing.Coupon;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.Discount;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.model.billing.SalesforceOpportunity;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.svc.mms.util.billing.PartnerMarketplaceUtils;
import com.xgen.svc.mms.util.billing.scenarios.SalesSoldScenarios;
import com.xgen.svc.mms.util.billing.testFactories.AwsSalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.GcpSalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory.SalesforceProduct;
import com.xgen.svc.mms.util.billing.testFactories.UsageFactory;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class AccountantSvcPaymentsIntTests extends JUnit5BaseSvcTest {

  @Inject private AccountantSvc _accountantSvc;
  @Inject private InvoiceDao _invoiceDao;
  @Inject private InvoiceSvc _invoiceSvc;
  @Inject private LastBillDateSvcImpl _lastBillDateSvc;
  @Inject private PaymentSvc _paymentSvc;
  @Inject private LineItemDao _lineItemDao;
  @Inject private PaymentDao _paymentDao;
  @Inject private SalesSoldScenarios _salesSoldScenarios;
  @Inject private OrganizationFactory _organizationFactory;
  @Inject private OrganizationDao _organizationDao;
  @Inject private CreditDao _creditDao;
  @Inject private SalesforceProductCodeDao _salesforceProductCodeDao;
  @Inject private SalesforceOpportunityFactory salesforceOpportunityFactory;
  @Inject private SalesSoldDealActivationSvc _salesSoldDealActivationSvc;
  @Inject private UsageFactory _usageFactory;
  @Inject private CreditFactory _creditFactory;
  @Inject private RebillSvc _rebillSvc;
  @Inject private OrgPrepaidPlanDao _orgPrepaidPlanDao;
  @Inject private AwsSalesforceOpportunityFactory awsSalesforceOpportunityFactory;
  @Inject private GcpSalesforceOpportunityFactory gcpSalesforceOpportunityFactory;

  @Inject
  private ActivationCodeRetroactiveApplicationJobHandler
      activationCodeRetroactiveApplicationJobHandler;

  @Inject private JobQueueTestUtils jobQueueTestUtils;

  @Inject private AwsApiSvc awsApiSvc;
  @Inject private RefundQueryingClient refundQueryingClient;
  @Inject PaymentMethodStubber paymentMethodStubber;

  @Override
  public void setUp() throws Exception {
    super.setUp();

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/CouponDao/coupons.json.ftl", null, Coupon.DB_NAME, Coupon.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/DiscountDao/discounts.json.ftl",
        null,
        Discount.DB_NAME,
        Discount.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/InvoiceDao/invoices.json.ftl",
        null,
        Invoice.DB_NAME,
        Invoice.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/PaymentDao/payments.json.ftl",
        null,
        Payment.DB_NAME,
        Payment.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/SubscriptionUsageDao/subscriptionUsage.json.ftl",
        null,
        SubscriptionUsageDaoV2.DB_NAME,
        SubscriptionUsageDaoV2.COLLECTION_NAME);

    List<ObjectId> groupIds = IntStream.range(0, 500).mapToObj(JUnit5BaseSvcTest::oid).toList();

    Date end = DateUtils.addDays(nowDate(), 1);
    Date start = DateUtils.addYears(end, -1);
    UsageFactory.generateMeterUsageFromExistingSubscriptionUsage(start, end, groupIds);

    _lineItemDao.getCollection().drop();
    _salesforceProductCodeDao.syncDocuments();
    doNothing().when(awsApiSvc).ensureValidEntitlements(anyString(), anyString());

    doReturn(List.of()).when(refundQueryingClient).getRefundsByInvoiceId(any());
  }

  @Test
  public void testProcessOrganizationsApplyCredits() {
    Credit credit1 =
        new Credit.Builder("tiny")
            .id(ObjectId.get())
            .orgId(oid(200))
            .amountCents(2000L)
            .created(DateUtils.addDays(nowDate(), -1))
            .startDate(new GregorianCalendar(1970, Calendar.JANUARY, 1).getTime())
            .endDate(new GregorianCalendar(9999, Calendar.DECEMBER, 31).getTime())
            .build();
    _creditDao.save(credit1);
    _creditDao.applyCredit(credit1.getId(), 1900L);
    Credit credit2 =
        new Credit.Builder("tiny")
            .id(ObjectId.get())
            .orgId(oid(200))
            .amountCents(4000L)
            .created(nowDate())
            .startDate(new GregorianCalendar(1970, Calendar.JANUARY, 1).getTime())
            .endDate(new GregorianCalendar(9999, Calendar.DECEMBER, 31).getTime())
            .build();
    _creditDao.save(credit2);

    // Test that the accountant generates the correct line items, one of each type per day.
    Date todayAfterSla = InvoiceTestUtils.getDailyBillingStartTime(nowDate());
    Set<ObjectId> orgIds = Set.of(oid(200));
    _accountantSvc.processOrganizations(orgIds, todayAfterSla);

    // Verify that line items were created for each used credit
    Invoice pendingInvoice = _invoiceDao.findPendingMonthlyByOrgId(oid(200));

    List<LineItem> credits =
        _lineItemDao.findByInvoiceId(pendingInvoice.getId()).stream()
            .filter(l -> l.getSku().equals(SKU.CREDIT))
            .collect(Collectors.toList());
    assertEquals(2, credits.size());
    assertEquals(-100L, credits.get(0).getTotalPriceCents());
    assertEquals(-116L, credits.get(1).getTotalPriceCents());

    // Verify that the credits were used and have the expected amount remaining
    assertEquals(0L, _creditDao.findById(credit1.getId()).getAmountRemainingCents());
    assertEquals(3884L, _creditDao.findById(credit2.getId()).getAmountRemainingCents());
  }

  @Test
  public void testProcessOrganizationsApplyPrepaidCredits() {

    Date midnight = DateUtils.truncate(nowDate(), Calendar.DAY_OF_MONTH);
    Date todayAt2am = InvoiceTestUtils.getDailyBillingStartTime(midnight);

    // this credit is out of range.
    // this simulates situation when accountant sends date range of [dayCurrent - dayTomorrow]
    // and the credit startDate is [dayTomorrow - to some date in future]
    // this credit should not be applied.
    Credit credit1 =
        new Credit.Builder("tiny")
            .id(ObjectId.get())
            .orgId(oid(200))
            .amountCents(2000L)
            .type(CreditType.PREPAID_NDS)
            .startDate(midnight)
            .endDate(DateUtils.addDays(midnight, 5))
            .created(DateUtils.addDays(nowDate(), -1))
            .build();
    _creditDao.save(credit1);

    Credit credit2 =
        new Credit.Builder("tiny")
            .id(ObjectId.get())
            .orgId(oid(200))
            .amountCents(4000L)
            .created(nowDate())
            .type(CreditType.PREPAID_NDS)
            .startDate(DateUtils.addDays(midnight, -4))
            .endDate(DateUtils.addDays(midnight, 10))
            .build();
    _creditDao.save(credit2);

    // Test that the accountant generates the correct credit line items, one of each type per day.
    Set<ObjectId> orgIds = Set.of(oid(200));
    paymentMethodStubber.stubPaymentMethods(orgIds);
    _accountantSvc.processOrganizations(orgIds, todayAt2am);

    // Invoice - oid(5) was associated with org - oid(200)
    List<LineItem> creditLineItems1 =
        _lineItemDao.findByInvoiceId(oid(5)).stream()
            .filter(l -> l.getSku().equals(SKU.CREDIT))
            .collect(Collectors.toList());

    assertEquals(1, creditLineItems1.size());
    assertEquals(-216L, creditLineItems1.get(0).getTotalPriceCents());

    // credit1 was not applied
    assertEquals(2000L, _creditDao.findById(credit1.getId()).getAmountRemainingCents());
    assertEquals(3784L, _creditDao.findById(credit2.getId()).getAmountRemainingCents());
  }

  @Test
  public void testProcessOrganizationWithZeroDollarElasticCredit() throws Exception {
    Date beginningOfMonth = DateUtils.truncate(new Date(2023 - 1900, 9, 1), Calendar.MONTH);
    testProcessOrganizationWithZeroDollarElasticCredit(beginningOfMonth);
  }

  void testProcessOrganizationWithZeroDollarElasticCredit(Date beginningOfMonth) throws Exception {
    Date midMonth =
        InvoiceTestUtils.getDailyBillingStartTime(DateUtils.addDays(beginningOfMonth, 15));

    Organization org =
        _salesSoldScenarios.createOrgWithUsageAndZeroDollarElasticCredit(beginningOfMonth);
    List<Credit> orgCredits = _creditDao.findByOrgId(org.getId());

    Optional<Credit> creditWith20Discount =
        orgCredits.stream()
            .filter(
                (credit) ->
                    credit.getSalesforceOpportunityLineItem().getDiscountPercent().equals(20D))
            .findFirst();

    assertFalse(creditWith20Discount.isEmpty());

    _accountantSvc.processOrganizations(List.of(org.getId()), midMonth);

    List<Invoice> orgInvoices =
        _invoiceSvc.findPendingMonthlyInvoicesByOrgIds(List.of(org.getId()));
    assertEquals(1, orgInvoices.size());

    List<Payment> orgMidMonthPayments =
        _paymentSvc.getPaymentsByInvoiceId(orgInvoices.get(0).getId(), false);
    assertEquals(0, orgMidMonthPayments.size());

    Date closeDate = InvoiceTestUtils.getDailyBillingStartTime(orgInvoices.get(0).getEndDate());

    _accountantSvc.processOrganizations(List.of(org.getId()), closeDate);

    List<Payment> orgEndOfMonthPayments =
        _paymentSvc.getPaymentsByInvoiceId(orgInvoices.get(0).getId(), false);
    assertEquals(1, orgEndOfMonthPayments.size());

    Payment payment = orgEndOfMonthPayments.get(0);
    assertEquals(creditWith20Discount.get().getId(), payment.getCreditId());
    assertEquals(PaymentMethodType.INVOICE, payment.getPaymentMethod());
  }

  @Test
  public void testCloseInvoice_overlappingElasticDeals() {
    Date beginningOfMonth = new Date(2023 - 1900, 9, 1);
    testCloseInvoice_overlappingElasticDeals(beginningOfMonth);
  }

  private void testCloseInvoice_overlappingElasticDeals(Date beginningOfMonth) {
    Date middleOfMonth = DateUtils.addDays(beginningOfMonth, 15);
    Date startOfNextMonth = DateUtils.addMonths(beginningOfMonth, 1);
    Date oneYearLater = DateUtils.addYears(middleOfMonth, 11);

    long daysInMonth =
        ChronoUnit.DAYS.between(beginningOfMonth.toInstant(), startOfNextMonth.toInstant());

    Organization org = _organizationFactory.createAtlasOrganization(beginningOfMonth, true);
    Group grp = _organizationFactory.createNdsGroup(org, beginningOfMonth);

    _creditFactory.createGcpMarketplaceMonthlyCommitment(
        org.getId(), middleOfMonth, oneYearLater, 1200_00, false);
    _creditFactory.createPrepaidCredit(org.getId(), beginningOfMonth, startOfNextMonth, 0, true);

    long totalUsageCents =
        _usageFactory.generateHourlyAWSSubscriptionUsage(
            new ObjectId(),
            grp.getId(),
            AWSRegionName.US_EAST_1,
            AWSNDSInstanceSize.M50,
            1,
            beginningOfMonth,
            startOfNextMonth,
            false);

    long usagePerDay = totalUsageCents / daysInMonth;

    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addHours(startOfNextMonth, 4));
    List<Invoice> invoices = _invoiceDao.findByOrgId(org.getId(), false);

    assertThat(invoices.size(), equalTo(1));

    List<Payment> payments = _paymentDao.findByInvoiceId(invoices.get(0).getId(), true);
    assertThat(payments.size(), equalTo(2));

    assertThat(
        payments.get(0).getPaymentMethod(), equalTo(PaymentMethodType.GCP_MARKETPLACE_INVOICE));
    assertThat(payments.get(0).getAmountBilledCents(), equalTo(usagePerDay * (daysInMonth - 15)));
    assertThat(payments.get(1).getPaymentMethod(), equalTo(PaymentMethodType.INVOICE));
    assertThat(payments.get(1).getAmountBilledCents(), equalTo(usagePerDay * 15));
  }

  @Test
  public void
      testApplyCreditsWithPartnerCreditOverlappingDirectMCCredit_partnerCreditNotInElasticStatus_mcCreditStarts1MonhAfterPartnerCreditStarts_partnerPrioritizedUntilItReachesElastic_thenMcCreditDrawnDown_thenPartnerCreditElasticallyDrawnDown()
          throws Exception {
    // tests the scenario where two credits overlap, while the one less preferred by
    // getCreditComparator(), which is within commitment, has carry over credit available, and the
    // other credit has an
    // amountRemaining
    // month 1, only the partner credit is active so it is prioritized
    // month 2, both credits active with amount remaining > 0, so the one with the earlier end date
    // is prioritized (partner)
    // month 3, both credits active with amount remaining > 0, MC credit has been billed a minimum
    // so mcCredit.carryOver is > 0. Partner credit is still prioritized.
    // month 4. partner credit reaches amount remaining = 0, so mc credit is prioritized for
    // remainder of the invoice.
    // month 5. this month's usage surpasses the amount remaining on the mc credit, so the mc credit
    // is drawn down to 0, before returning to the partner credit because the partner credit is
    // selected as the active elastic credit due to later opp closed date.
    Date invoiceStart = DateUtils.truncate(nowDate(), Calendar.MONTH);
    Date month2Start = DateUtils.addMonths(invoiceStart, 1);
    Date month3Start = DateUtils.addMonths(invoiceStart, 2);
    Date month4Start = DateUtils.addMonths(invoiceStart, 3);
    Date month5Start = DateUtils.addMonths(invoiceStart, 5);
    Date month6Start = DateUtils.addMonths(invoiceStart, 6);
    Date month13Start = DateUtils.addMonths(invoiceStart, 12);
    Date month14Start = DateUtils.addMonths(invoiceStart, 13);
    Date partnerPrepaidStart = invoiceStart;
    Date directMcStart = month2Start;
    Date partnerPrepaidEnd = month13Start;
    Date directMcEnd = month14Start;
    long directMcAmount = 120000L;
    long commitmentMinimum = directMcAmount / 12;
    long entitlementsMinimum = 500 + commitmentMinimum;
    long partnerPrepaidAmount = entitlementsMinimum * 3 + 100;
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    SalesforceOpportunity directMcOpp =
        salesforceOpportunityFactory.insertAtlasProMonthlyCommitmentOpportunity(
            directMcStart, DateUtils.addDays(directMcEnd, -1), directMcAmount, true);
    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    SalesforceOpportunity partnerPrepaidOpp =
        awsSalesforceOpportunityFactory.insertOpportunity(
            SalesforceProduct.AWS_MARKETPLACE_PRO_PREPAID_CREDIT,
            partnerPrepaidStart,
            partnerPrepaidEnd,
            partnerPrepaidAmount / 100.0,
            true,
            1.0);

    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, partnerPrepaidOpp.getLicenseKey(), invoiceStart, null, auditInfo, true));

    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(), entitlementsMinimum, invoiceStart, true);

    // sanity check credits were inserted properly
    Credit partnerCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.AWS_PREPAID)
            .findFirst()
            .get();
    assertEquals(partnerPrepaidAmount, partnerCredit.getAmountCents());
    assertEquals(partnerPrepaidStart, partnerCredit.getStartDate());
    assertEquals(partnerPrepaidEnd, partnerCredit.getEndDate());

    // process all of month 1
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month2Start, 3));

    // activate the direct MC deal
    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, directMcOpp.getLicenseKey(), month2Start, null, auditInfo, true));
    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(), entitlementsMinimum, invoiceStart, true);

    // sanity check credit dates and amounts
    Credit directMcCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.MONTHLY_COMMITMENT)
            .findFirst()
            .get();
    assertEquals(directMcAmount, directMcCredit.getAmountCents());
    assertEquals(directMcStart, directMcCredit.getStartDate());
    assertEquals(directMcEnd, directMcCredit.getEndDate());

    // during month 1 mc is within_commitment w/o carryover, and prepaid is within_commitment, so
    // prepaid is prioritized
    // assert that the prepaid marketplace credit was used to cover month 1
    Invoice month1Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), invoiceStart);
    long partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month1Invoice.getId());
    long mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month1Invoice.getId());
    long invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month1Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());
    assertTrue(partnerCreditApplied > 0);
    assertEquals(partnerCreditApplied, invoiceTotalExcludingCredits);
    assertEquals(entitlementsMinimum, partnerCreditApplied);
    assertEquals(partnerCredit.getAmountUsedCents(), partnerCreditApplied);
    assertEquals(0, partnerCredit.getTotalBilledCents());
    assertEquals(0, mcCreditApplied);
    assertEquals(0, directMcCredit.getTotalBilledCents());
    assertEquals(0, directMcCredit.getAmountUsedCents());

    // process all of month 2
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month3Start, 3));

    Invoice month2Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month2Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month2Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month2Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month2Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());
    long expectedPartnerCreditTotalUsage = entitlementsMinimum * 2;
    assertTrue(partnerCreditApplied > 0);
    assertEquals(partnerCreditApplied, invoiceTotalExcludingCredits);
    assertEquals(entitlementsMinimum, partnerCreditApplied);
    assertEquals(expectedPartnerCreditTotalUsage, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());
    assertEquals(0, mcCreditApplied);
    assertEquals(commitmentMinimum, directMcCredit.getTotalBilledCents());
    assertEquals(0, directMcCredit.getAmountUsedCents());

    // process all of month 3
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month4Start, 3));

    Invoice month3Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month3Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month3Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month3Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month3Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    // entitlementsMinimum - commitmentMinimum = 500
    expectedPartnerCreditTotalUsage = entitlementsMinimum * 3;
    assertEquals(invoiceTotalExcludingCredits, partnerCreditApplied);
    assertEquals(entitlementsMinimum, partnerCreditApplied);
    assertTrue(partnerCreditApplied > 0);
    assertEquals(expectedPartnerCreditTotalUsage, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());
    long expectedMcCreditTotalUsage = 0;
    long expectedMcCreditTotalBilled = commitmentMinimum * 2;
    assertEquals(expectedMcCreditTotalUsage, mcCreditApplied);
    assertEquals(expectedMcCreditTotalBilled, directMcCredit.getTotalBilledCents());
    assertEquals(expectedMcCreditTotalUsage, directMcCredit.getAmountUsedCents());

    // process all of month 4
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month5Start, 3));

    Invoice month4Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month4Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month4Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month4Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month4Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();
    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    expectedPartnerCreditTotalUsage = entitlementsMinimum * 3 + 100;
    assertEquals(invoiceTotalExcludingCredits, partnerCreditApplied + mcCreditApplied);
    assertEquals(entitlementsMinimum, partnerCreditApplied + mcCreditApplied);
    assertEquals(100, partnerCreditApplied);
    assertEquals(expectedPartnerCreditTotalUsage, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getAmountRemainingCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());
    expectedMcCreditTotalUsage = entitlementsMinimum - 100;
    expectedMcCreditTotalBilled = commitmentMinimum * 3;
    assertEquals(expectedMcCreditTotalUsage, mcCreditApplied);
    assertEquals(expectedMcCreditTotalBilled, directMcCredit.getTotalBilledCents());
    assertEquals(expectedMcCreditTotalUsage, directMcCredit.getAmountUsedCents());

    // process all of month 5 (update entitlements minimum to drain mc credit + some more, so we can
    // verify that extra amount is applied to the partner credit)
    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(),
        directMcCredit.getAmountRemainingCents() + 900,
        month6Start,
        true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(),
        directMcCredit.getAmountRemainingCents() + 900,
        month6Start,
        true);
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month6Start, 3));
    Invoice month5Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month5Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month5Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month5Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month5Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();
    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    // during month 5 mc credit is fully exhausted, so we switch to the active elastic credit.
    expectedPartnerCreditTotalUsage = entitlementsMinimum * 3 + 100 + 900;
    assertEquals(expectedPartnerCreditTotalUsage, partnerCredit.getAmountUsedCents());
    assertEquals(invoiceTotalExcludingCredits, partnerCreditApplied + mcCreditApplied);
    assertEquals(900, partnerCreditApplied);
    assertEquals(-900, partnerCredit.getAmountRemainingCents());
    assertEquals(900, partnerCredit.getTotalBilledCents());
    expectedMcCreditTotalUsage = directMcAmount;
    expectedMcCreditTotalBilled = directMcAmount;
    assertEquals(expectedMcCreditTotalBilled, directMcCredit.getTotalBilledCents());
    assertEquals(expectedMcCreditTotalUsage, directMcCredit.getAmountUsedCents());
  }

  @Test
  public void
      testApplyCreditsWithPartnerCreditOverlappingDirectMCCredit_partnerCreditNotInElasticStatus_partnerCreditStarts1MonthAfterMcCreditStarts_mcCreditPrioritizedUntilItReachesElastic_thenPartnerCreditDrawnDown_thenPartnerCreditElasticallyDrawnDown()
          throws Exception {
    // tests the scenario where two credits overlap, while the one more preferred by
    // getCreditComparator() has carry over credit available, and the other credit has an
    // amountRemaining
    // month 1, only the mc credit is active so it is prioritized
    // month 2, both credits active with amount remaining > 0, so the one with the earlier end date
    // is prioritized (mc)
    // month 3, both credits active with amount remaining > 0, MC credit has been billed a minimum
    // so mcCredit.carryOver is > 0. mc credit is still prioritized.
    // month 4. mc credit reaches amount remaining = 0, so partner credit is prioritized for
    // remainder of the invoice.
    // month 5. this month's usage surpasses the amount remaining on the partner credit, so the
    // partner credit is drawn down to 0, the partner credit continues to be drawn down because its
    // the active elastic credit with the latest closed opp.
    Date pNow = DateUtils.setMonths(nowDate(), Calendar.MAY);
    Date invoiceStart = DateUtils.truncate(pNow, Calendar.MONTH);
    Date month2Start = DateUtils.addMonths(invoiceStart, 1);
    Date month3Start = DateUtils.addMonths(invoiceStart, 2);
    Date month4Start = DateUtils.addMonths(invoiceStart, 3);
    Date month5Start = DateUtils.addMonths(invoiceStart, 5);
    Date month6Start = DateUtils.addMonths(invoiceStart, 6);
    Date month13Start = DateUtils.addMonths(invoiceStart, 12);
    Date month14Start = DateUtils.addMonths(invoiceStart, 13);
    Date partnerPrepaidStart = month2Start;
    Date directMcStart = invoiceStart;
    Date partnerPrepaidEnd = month14Start;
    Date directMcEnd = month13Start;
    long directMcAmount = 120000L;
    long commitmentMinimum = directMcAmount / 12;
    long entitlementsMinimum = 500 + commitmentMinimum;
    long partnerPrepaidAmount = entitlementsMinimum * 3 + 100;
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    SalesforceOpportunity directMcOpp =
        salesforceOpportunityFactory.insertAtlasProMonthlyCommitmentOpportunity(
            directMcStart, DateUtils.addDays(directMcEnd, -1), directMcAmount, true);

    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, directMcOpp.getLicenseKey(), invoiceStart, null, auditInfo, true));

    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(), entitlementsMinimum, invoiceStart, true);

    // sanity check credit dates and amounts
    Credit directMcCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.MONTHLY_COMMITMENT)
            .findFirst()
            .get();
    ObjectId directMcCreditId = directMcCredit.getId();
    assertEquals(directMcAmount, directMcCredit.getAmountCents());
    assertEquals(directMcStart, directMcCredit.getStartDate());
    assertEquals(directMcEnd, directMcCredit.getEndDate());

    // process all of month 1
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month2Start, 3));

    SalesforceOpportunity partnerPrepaidOpp =
        awsSalesforceOpportunityFactory.insertOpportunity(
            SalesforceProduct.AWS_MARKETPLACE_PRO_PREPAID_CREDIT,
            partnerPrepaidStart,
            partnerPrepaidEnd,
            partnerPrepaidAmount / 100.0,
            true,
            1.0);

    // activate the partner pp deal
    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, partnerPrepaidOpp.getLicenseKey(), month2Start, null, auditInfo, true));
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(), entitlementsMinimum, invoiceStart, true);

    // sanity check credits were inserted properly
    Credit partnerCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.AWS_PREPAID)
            .findFirst()
            .get();
    ObjectId partnerCreditId = partnerCredit.getId();
    assertEquals(partnerPrepaidAmount, partnerCredit.getAmountCents());
    assertEquals(partnerPrepaidStart, partnerCredit.getStartDate());
    assertEquals(partnerPrepaidEnd, partnerCredit.getEndDate());

    Invoice month1Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), invoiceStart);
    long partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month1Invoice.getId());
    long mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month1Invoice.getId());
    long invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month1Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());
    assertTrue(mcCreditApplied > 0);
    assertEquals(mcCreditApplied, invoiceTotalExcludingCredits);
    assertEquals(entitlementsMinimum, mcCreditApplied);
    assertEquals(directMcCredit.getAmountUsedCents(), mcCreditApplied);
    assertEquals(mcCreditApplied, directMcCredit.getTotalBilledCents());
    assertEquals(0, partnerCreditApplied);
    assertEquals(0, partnerCredit.getTotalBilledCents());
    assertEquals(0, partnerCredit.getAmountUsedCents());

    // update entitlements minimum to less than the commit minimum, so carry over credit is created.
    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(), commitmentMinimum - 800, month2Start, true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(), commitmentMinimum - 800, month2Start, true);

    // process all of month 2
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month3Start, 3));

    Invoice month2Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month2Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month2Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month2Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month2Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertTrue(mcCreditApplied > 0);
    assertEquals(commitmentMinimum - 800, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, mcCreditApplied);
    assertEquals(commitmentMinimum * 2 + 500, directMcCredit.getTotalBilledCents());
    assertEquals(
        entitlementsMinimum + commitmentMinimum - 800, directMcCredit.getAmountUsedCents());
    assertEquals(800, directMcCredit.getCarryOverCents());
    assertEquals(0, partnerCreditApplied);
    assertEquals(0, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    // process all of month 3
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month4Start, 3));

    Invoice month3Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month3Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month3Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month3Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month3Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertTrue(mcCreditApplied > 0);
    assertEquals(commitmentMinimum - 800, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, mcCreditApplied);
    assertEquals(commitmentMinimum * 3 + 500, directMcCredit.getTotalBilledCents());
    assertEquals(
        entitlementsMinimum + (commitmentMinimum - 800) * 2, directMcCredit.getAmountUsedCents());
    assertEquals(800 * 2, directMcCredit.getCarryOverCents());
    assertEquals(0, partnerCreditApplied);
    assertEquals(0, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(),
        directMcCredit.getAmountRemainingCents() + 300,
        month2Start,
        true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(),
        directMcCredit.getAmountRemainingCents() + 300,
        month2Start,
        true);

    // process all of month 4
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month5Start, 3));

    Invoice month4Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month4Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month4Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month4Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month4Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    long expectedMcCreditApplied = directMcCredit.getAmountRemainingCents();
    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertTrue(mcCreditApplied > 0);
    assertEquals(expectedMcCreditApplied, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, mcCreditApplied + 300);
    assertEquals(directMcCredit.getAmountUsedCents(), directMcCredit.getTotalBilledCents());
    assertEquals(directMcAmount, directMcCredit.getAmountUsedCents());
    assertEquals(0, directMcCredit.getCarryOverCents());
    assertEquals(0, directMcCredit.getAmountRemainingCents());
    assertEquals(300, partnerCreditApplied);
    assertEquals(300, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(),
        partnerCredit.getAmountRemainingCents() + 1700,
        month2Start,
        true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(),
        partnerCredit.getAmountRemainingCents() + 1700,
        month2Start,
        true);

    // process all of month 5
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month6Start, 3));

    Invoice month5Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month5Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month5Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month5Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month5Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    long expectedPartnerCreditApplied = partnerCredit.getAmountRemainingCents() + 1700;
    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertEquals(0, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, partnerCreditApplied);
    assertEquals(directMcCredit.getAmountUsedCents(), directMcCredit.getTotalBilledCents());
    assertEquals(directMcAmount, directMcCredit.getAmountUsedCents());
    assertEquals(0, directMcCredit.getCarryOverCents());
    assertEquals(0, partnerCredit.getCarryOverCents());
    assertEquals(expectedPartnerCreditApplied, partnerCreditApplied);
    assertEquals(partnerCredit.getAmountCents() + 1700, partnerCredit.getAmountUsedCents());
    assertEquals(1700, partnerCredit.getTotalBilledCents());

    List<Payment> allPayments = _paymentDao.findByOrgId(org.getId());

    List<Payment> partnerCreditPayments =
        allPayments.stream()
            .filter((p) -> p.getCreditId().equals(partnerCreditId))
            .collect(Collectors.toList());
    assertEquals(1, partnerCreditPayments.size());

    List<Payment> directMcPayments =
        allPayments.stream()
            .filter((p) -> p.getCreditId().equals(directMcCreditId))
            .collect(Collectors.toList());
    assertEquals(4, directMcPayments.size());

    List<Payment> directMcPaymentsWithin =
        allPayments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(directMcCreditId)
                        && p.getPaymentMethod() == PaymentMethodType.MONTHLY_COMMITMENT)
            .collect(Collectors.toList());
    assertEquals(4, directMcPaymentsWithin.size());

    long totalDirectMcWithinBilled =
        directMcPaymentsWithin.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalDirectMcWithinPaid =
        directMcPaymentsWithin.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalDirectMcWithinSubtotal =
        directMcPaymentsWithin.stream().mapToLong(Payment::getAmountBilledCents).sum();

    assertEquals(directMcAmount, totalDirectMcWithinBilled);
    assertEquals(directMcAmount, totalDirectMcWithinPaid);
    assertEquals(directMcAmount, totalDirectMcWithinSubtotal);

    List<Payment> directMcPaymentsElastic =
        allPayments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(directMcCreditId)
                        && p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .collect(Collectors.toList());
    assertEquals(0, directMcPaymentsElastic.size());

    List<Payment> partnerElasticPayments =
        allPayments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(partnerCreditId)
                        && p.getPaymentMethod()
                            == PaymentMethodType.AWS_MARKETPLACE_ELASTIC_INVOICE)
            .collect(Collectors.toList());
    assertEquals(1, partnerElasticPayments.size());
    long totalPartnerElasticBilled =
        partnerElasticPayments.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalPartnerElasticPaid =
        partnerElasticPayments.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalPartnerElasticSubtotal =
        partnerElasticPayments.stream().mapToLong(Payment::getAmountBilledCents).sum();

    assertEquals(1700, totalPartnerElasticBilled);
    assertEquals(1700, totalPartnerElasticPaid);
    assertEquals(1700, totalPartnerElasticSubtotal);
  }

  @Test
  public void
      testApplyCreditsWithPartnerCreditOverlappingDirectMCCredit_partnerCreditNotInElasticStatus_partnerCreditStarts1MonthAfterMcCreditStarts_mcCreditPrioritizedAfterItReachesElastic_thenPartnerCreditDrawnDown_thenDirectMcCreditIsChosenAsElasticCredit()
          throws Exception {
    Date invoiceStart = DateUtils.setYears(DateUtils.truncate(nowDate(), Calendar.MONTH), 2024);
    testApplyCreditsWithPartnerCreditOverlappingDirectMCCredit_partnerCreditNotInElasticStatus_partnerCreditStarts1MonthAfterMcCreditStarts_mcCreditPrioritizedAfterItReachesElastic_thenPartnerCreditDrawnDown_thenDirectMcCreditIsChosenAsElasticCredit(
        invoiceStart);
  }

  private void
      testApplyCreditsWithPartnerCreditOverlappingDirectMCCredit_partnerCreditNotInElasticStatus_partnerCreditStarts1MonthAfterMcCreditStarts_mcCreditPrioritizedAfterItReachesElastic_thenPartnerCreditDrawnDown_thenDirectMcCreditIsChosenAsElasticCredit(
          Date invoiceStart) throws Exception {
    // tests the scenario where two credits overlap, while the one more preferred by
    // getCreditComparator() has carry over credit available, and the other credit has an
    // amountRemaining
    // month 1, only the mc credit is active so it is prioritized
    // month 2, both credits active with amount remaining > 0, so the one with the earlier end date
    // is prioritized (mc)
    // month 3, both credits active with amount remaining > 0, MC credit has been billed a minimum
    // so mcCredit.carryOver is > 0. mc credit is still prioritized.
    // month 4. mc credit reaches amount remaining = 0, so partner credit is prioritized for
    // remainder of the invoice.
    // month 5. this month's usage surpasses the amount remaining on the partner credit, so the
    // partner credit is drawn down to 0, the direct mc credit is then prioritized because it is the
    // latest closed opp.

    Date month2Start = DateUtils.addMonths(invoiceStart, 1);
    Date month3Start = DateUtils.addMonths(invoiceStart, 2);
    Date month4Start = DateUtils.addMonths(invoiceStart, 3);
    Date month5Start = DateUtils.addMonths(invoiceStart, 5);
    Date month6Start = DateUtils.addMonths(invoiceStart, 6);
    Date month13Start = DateUtils.addMonths(invoiceStart, 12);
    Date month14Start = DateUtils.addMonths(invoiceStart, 13);
    Date partnerPrepaidStart = month2Start;
    Date directMcStart = invoiceStart;
    Date partnerPrepaidEnd = month14Start;
    Date directMcEnd = month13Start;
    long directMcAmount = 120000L;
    long commitmentMinimum = directMcAmount / 12;
    long entitlementsMinimum = 500 + commitmentMinimum;
    long partnerPrepaidAmount = entitlementsMinimum * 3 + 100;
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    SalesforceOpportunity partnerPrepaidOpp =
        awsSalesforceOpportunityFactory.insertOpportunity(
            SalesforceProduct.AWS_MARKETPLACE_PRO_PREPAID_CREDIT,
            partnerPrepaidStart,
            partnerPrepaidEnd,
            partnerPrepaidAmount / 100.0,
            true,
            1.0);

    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    SalesforceOpportunity directMcOpp =
        salesforceOpportunityFactory.insertAtlasProMonthlyCommitmentOpportunity(
            directMcStart, directMcEnd, directMcAmount, true);

    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, directMcOpp.getLicenseKey(), invoiceStart, null, auditInfo, true));

    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(), entitlementsMinimum, invoiceStart, true);

    // sanity check credit dates and amounts
    Credit directMcCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.MONTHLY_COMMITMENT)
            .findFirst()
            .get();
    ObjectId directMcCreditId = directMcCredit.getId();
    assertEquals(directMcAmount, directMcCredit.getAmountCents());
    assertEquals(directMcStart, directMcCredit.getStartDate());
    assertEquals(directMcEnd, directMcCredit.getEndDate());

    // process all of month 1
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month2Start, 3));

    // activate the partner pp deal
    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, partnerPrepaidOpp.getLicenseKey(), month2Start, null, auditInfo, true));
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(), entitlementsMinimum, invoiceStart, true);

    // sanity check credits were inserted properly
    Credit partnerCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.AWS_PREPAID)
            .findFirst()
            .get();
    ObjectId partnerCreditId = partnerCredit.getId();
    assertEquals(partnerPrepaidAmount, partnerCredit.getAmountCents());
    assertEquals(partnerPrepaidStart, partnerCredit.getStartDate());
    assertEquals(partnerPrepaidEnd, partnerCredit.getEndDate());

    Invoice month1Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), invoiceStart);
    long partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month1Invoice.getId());
    long mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month1Invoice.getId());
    long invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month1Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());
    assertTrue(mcCreditApplied > 0);
    assertEquals(mcCreditApplied, invoiceTotalExcludingCredits);
    assertEquals(entitlementsMinimum, mcCreditApplied);
    assertEquals(directMcCredit.getAmountUsedCents(), mcCreditApplied);
    assertEquals(mcCreditApplied, directMcCredit.getTotalBilledCents());
    assertEquals(0, partnerCreditApplied);
    assertEquals(0, partnerCredit.getTotalBilledCents());
    assertEquals(0, partnerCredit.getAmountUsedCents());

    // update entitlements minimum to less than the commit minimum, so carry over credit is created.
    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(), commitmentMinimum - 800, month2Start, true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(), commitmentMinimum - 800, month2Start, true);

    // process all of month 2
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month3Start, 3));

    Invoice month2Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month2Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month2Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month2Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month2Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertTrue(mcCreditApplied > 0);
    assertEquals(commitmentMinimum - 800, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, mcCreditApplied);
    assertEquals(commitmentMinimum * 2 + 500, directMcCredit.getTotalBilledCents());
    assertEquals(
        entitlementsMinimum + commitmentMinimum - 800, directMcCredit.getAmountUsedCents());
    assertEquals(800, directMcCredit.getCarryOverCents());
    assertEquals(0, partnerCreditApplied);
    assertEquals(0, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    // process all of month 3
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month4Start, 3));

    Invoice month3Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month3Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month3Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month3Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month3Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertTrue(mcCreditApplied > 0);
    assertEquals(commitmentMinimum - 800, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, mcCreditApplied);
    assertEquals(commitmentMinimum * 3 + 500, directMcCredit.getTotalBilledCents());
    assertEquals(
        entitlementsMinimum + (commitmentMinimum - 800) * 2, directMcCredit.getAmountUsedCents());
    assertEquals(800 * 2, directMcCredit.getCarryOverCents());
    assertEquals(0, partnerCreditApplied);
    assertEquals(0, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(),
        directMcCredit.getAmountRemainingCents() + 300,
        month2Start,
        true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(),
        directMcCredit.getAmountRemainingCents() + 300,
        month2Start,
        true);

    // process all of month 4
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month5Start, 3));

    Invoice month4Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month4Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month4Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month4Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month4Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    long expectedMcCreditApplied = directMcCredit.getAmountRemainingCents();
    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertTrue(mcCreditApplied > 0);
    assertEquals(expectedMcCreditApplied, mcCreditApplied);
    assertEquals(invoiceTotalExcludingCredits, mcCreditApplied + 300);
    assertEquals(directMcCredit.getAmountUsedCents(), directMcCredit.getTotalBilledCents());
    assertEquals(directMcAmount, directMcCredit.getAmountUsedCents());
    assertEquals(0, directMcCredit.getCarryOverCents());
    assertEquals(0, directMcCredit.getAmountRemainingCents());
    assertEquals(300, partnerCreditApplied);
    assertEquals(300, partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    updatePlanEntitlementsMinimum(
        directMcOpp.getLicenseKey(),
        partnerCredit.getAmountRemainingCents() + 1700,
        month2Start,
        true);
    updatePlanEntitlementsMinimum(
        partnerPrepaidOpp.getLicenseKey(),
        partnerCredit.getAmountRemainingCents() + 1700,
        month2Start,
        true);

    // process all of month 5
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month6Start, 3));

    Invoice month5Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month5Start);
    partnerCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(partnerCredit.getId()), month5Invoice.getId());
    mcCreditApplied =
        -_lineItemDao.getTotalPriceCentsByCreditIdsAndInvoiceId(
            List.of(directMcCredit.getId()), month5Invoice.getId());
    invoiceTotalExcludingCredits =
        _lineItemDao.getNonCreditLineItems(month5Invoice.getId()).stream()
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    long expectedPartnerCreditApplied = partnerCredit.getAmountRemainingCents();
    partnerCredit = _creditDao.findById(partnerCredit.getId());
    directMcCredit = _creditDao.findById(directMcCredit.getId());

    assertEquals(1700, mcCreditApplied);
    assertEquals(directMcAmount + 1700, directMcCredit.getAmountUsedCents());
    assertEquals(invoiceTotalExcludingCredits - 1700, partnerCreditApplied);
    assertEquals(directMcCredit.getAmountUsedCents(), directMcCredit.getTotalBilledCents());

    assertEquals(0, directMcCredit.getCarryOverCents());
    assertEquals(0, partnerCredit.getCarryOverCents());
    assertEquals(expectedPartnerCreditApplied, partnerCreditApplied);
    assertEquals(partnerCredit.getAmountCents(), partnerCredit.getAmountUsedCents());
    assertEquals(0, partnerCredit.getTotalBilledCents());

    List<Payment> allPayments = _paymentDao.findByOrgId(org.getId());

    List<Payment> partnerCreditPayments =
        allPayments.stream()
            .filter((p) -> p.getCreditId().equals(partnerCreditId))
            .collect(Collectors.toList());
    assertEquals(0, partnerCreditPayments.size());

    List<Payment> directMcPayments =
        allPayments.stream()
            .filter((p) -> p.getCreditId().equals(directMcCreditId))
            .collect(Collectors.toList());
    assertEquals(5, directMcPayments.size());

    List<Payment> directMcPaymentsWithin =
        allPayments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(directMcCreditId)
                        && p.getPaymentMethod() == PaymentMethodType.MONTHLY_COMMITMENT)
            .collect(Collectors.toList());
    assertEquals(4, directMcPaymentsWithin.size());
    long totalDirectMcWithinBilled =
        directMcPaymentsWithin.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalDirectMcWithinPaid =
        directMcPaymentsWithin.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalDirectMcWithinSubtotal =
        directMcPaymentsWithin.stream().mapToLong(Payment::getAmountBilledCents).sum();

    assertEquals(directMcAmount, totalDirectMcWithinBilled);
    assertEquals(directMcAmount, totalDirectMcWithinPaid);
    assertEquals(directMcAmount, totalDirectMcWithinSubtotal);

    List<Payment> directMcPaymentsElastic =
        allPayments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(directMcCreditId)
                        && p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .collect(Collectors.toList());
    assertEquals(1, directMcPaymentsElastic.size());
    long totalDirectMcElasticBilled =
        directMcPaymentsElastic.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalDirectMcElasticPaid =
        directMcPaymentsElastic.stream().mapToLong(Payment::getAmountBilledCents).sum();
    long totalDirectMcElasticSubtotal =
        directMcPaymentsElastic.stream().mapToLong(Payment::getAmountBilledCents).sum();

    assertEquals(1700, totalDirectMcElasticBilled);
    assertEquals(1700, totalDirectMcElasticPaid);
    assertEquals(1700, totalDirectMcElasticSubtotal);
  }

  @Test
  public void
      testApplyGCPCreditWithOverlappingGCPCredit_oldGCPCreditIsWithinCommitment_oldGcpCreditUsedToCoverUsageUpUntilItsEndDate()
          throws Exception {
    // gcp credit activated. [month 1 / 20, month 2 / 20]
    // 2/5 gcp credit activated. [month 2 / 1, month 4 / 1]
    // old gcp credit continues to be prioritized because it is still within commitment up to its
    // end date.
    Date pNow = DateUtils.setMonths(nowDate(), Calendar.MAY);
    Date invoiceStart = DateUtils.truncate(pNow, Calendar.MONTH);
    Date month2Start = DateUtils.addMonths(invoiceStart, 1);
    Date month3Start = DateUtils.addMonths(invoiceStart, 2);
    Date month4Start = DateUtils.addMonths(invoiceStart, 3);
    Date oldGcpStart = DateUtils.addDays(invoiceStart, 20);
    Date oldGcpEnd = DateUtils.addDays(month2Start, 20);
    Date newGcpStart = month2Start;
    Date newGcpEnd = month4Start;
    long oldGcpAmount = 10000;
    long newGcpAmount = 40000;
    long entitlements = 1100;

    SalesforceOpportunity oldGcpOpp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            oldGcpStart, oldGcpEnd, oldGcpAmount, false);

    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);

    applyGcpMonthCommitmentV1ActivationCode(org, oldGcpOpp, invoiceStart);
    updatePlanEntitlementsMinimum(oldGcpOpp.getLicenseKey(), entitlements, invoiceStart, true);

    // sanity check credits were inserted properly
    Credit oldGcpCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.GCP_BILLING_ACCOUNT)
            .findFirst()
            .get();
    ObjectId oldGcpCreditId = oldGcpCredit.getId();
    assertEquals(oldGcpAmount, oldGcpCredit.getAmountCents());
    assertEquals(oldGcpStart, oldGcpCredit.getStartDate());
    assertEquals(oldGcpEnd, oldGcpCredit.getEndDate());

    // process all of month 1
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month2Start, 3));

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    Date creditProrateDate = DateUtils.addDays(oldGcpStart, 1);
    long daysInMonth = TimeUtils2.daysPerMonth(TimeUtils2.getCalendar(creditProrateDate));
    long daysCoveredByCredit =
        PartnerMarketplaceUtils.getDaysCoveredByCredit(oldGcpCredit, creditProrateDate);
    long minimumDueForMonth1 =
        PartnerMarketplaceUtils.getProratedMonthlyMinimumCents(
            daysCoveredByCredit, oldGcpCredit, creditProrateDate);
    long expectedAmountUsedForMonth1 =
        (long) (entitlements * ((double) daysCoveredByCredit / daysInMonth));

    assertTrue(expectedAmountUsedForMonth1 > 0);
    assertTrue(oldGcpCredit.getTotalBilledCents() > oldGcpCredit.getAmountUsedCents());
    assertTrue(oldGcpCredit.getCarryOverCents() > 0);
    assertEquals(
        oldGcpCredit.getCarryOverCents(),
        oldGcpCredit.getTotalBilledCents() - oldGcpCredit.getAmountUsedCents());
    assertEquals(expectedAmountUsedForMonth1, oldGcpCredit.getAmountUsedCents());
    assertEquals(minimumDueForMonth1, oldGcpCredit.getTotalBilledCents());

    // process the first 5 days of month 2
    Date month2Day5BillTime =
        DateUtils.addHours(
            InvoiceTestUtils.getDailyBillingStartTime(DateUtils.addDays(month2Start, 4)), 1);
    _accountantSvc.processOrganizations(List.of(org.getId()), month2Day5BillTime);

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    long amountUsedBeforeNewActivation = oldGcpCredit.getAmountUsedCents();
    long amountBilledBeforeNewActivation = oldGcpCredit.getTotalBilledCents();
    long carryOverBeforeNewActivation = oldGcpCredit.getCarryOverCents();

    SalesforceOpportunity newGcpOpp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            newGcpStart, newGcpEnd, newGcpAmount, false);

    applyGcpMonthCommitmentV1ActivationCode(org, newGcpOpp, month2Day5BillTime);
    updatePlanEntitlementsMinimum(
        newGcpOpp.getLicenseKey(), entitlements, month2Day5BillTime, false);

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    assertEquals(amountUsedBeforeNewActivation, oldGcpCredit.getAmountUsedCents());
    assertEquals(amountBilledBeforeNewActivation, oldGcpCredit.getTotalBilledCents());
    assertEquals(carryOverBeforeNewActivation, oldGcpCredit.getCarryOverCents());

    // process the rest of month 2
    _accountantSvc.processOrganizations(
        List.of(org.getId()),
        DateUtils.addHours(InvoiceTestUtils.getDailyBillingStartTime(month3Start), 1));

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    long daysInMonth2 = TimeUtils2.daysPerMonth(TimeUtils2.getCalendar(month2Day5BillTime));
    long daysCoveredByCreditMonth2 =
        PartnerMarketplaceUtils.getDaysCoveredByCredit(oldGcpCredit, month2Day5BillTime);
    long expectedAmountUsedMonth2 =
        (long) (entitlements * ((double) daysCoveredByCreditMonth2 / daysInMonth2));

    assertTrue(expectedAmountUsedMonth2 > 0);
    assertTrue(oldGcpCredit.getTotalBilledCents() > oldGcpCredit.getAmountUsedCents());
    assertTrue(oldGcpCredit.getCarryOverCents() > 0);
    assertTrue(oldGcpAmount > minimumDueForMonth1);
    assertEquals(
        oldGcpCredit.getCarryOverCents(),
        oldGcpCredit.getTotalBilledCents() - oldGcpCredit.getAmountUsedCents());
    assertEquals(
        expectedAmountUsedMonth2 + expectedAmountUsedForMonth1, oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpAmount, oldGcpCredit.getTotalBilledCents());

    Credit newGcpCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getActivationCode().equals(newGcpOpp.getLicenseKey()))
            .findFirst()
            .get();

    ObjectId newGcpCreditId = newGcpCredit.getId();
    long expectedAmountUsedMonth2New = entitlements - expectedAmountUsedMonth2;
    long minimumDueForMonth2New = newGcpAmount / 2;
    assertTrue(expectedAmountUsedMonth2New > 0);
    assertTrue(expectedAmountUsedMonth2New < expectedAmountUsedMonth2);
    assertTrue(newGcpCredit.getCarryOverCents() > 0);
    assertEquals(
        newGcpCredit.getCarryOverCents(),
        newGcpCredit.getTotalBilledCents() - newGcpCredit.getAmountUsedCents());
    assertEquals(expectedAmountUsedMonth2New, newGcpCredit.getAmountUsedCents());
    assertEquals(minimumDueForMonth2New, newGcpCredit.getTotalBilledCents());

    Invoice month2Invoice =
        _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month2Day5BillTime);
    verifyCreditDateRange(
        oldGcpCredit, month2Invoice, month2Start, DateUtils.addDays(month2Start, 20));
    verifyCreditDateRange(
        newGcpCredit, month2Invoice, DateUtils.addDays(month2Start, 20), month3Start);

    // verify payments on month 1 and 2.
    Invoice month1Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), invoiceStart);

    List<Payment> month1Payments = _paymentDao.findByInvoiceId(month1Invoice.getId(), true);
    assertEquals(1, month1Payments.size());
    Payment month1WithinPayment =
        month1Payments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.GCP_MARKETPLACE_INVOICE)
            .findFirst()
            .get();

    Date expectedLastWithinCommitmentDay = DateUtils.addDays(month2Start, -1);
    verifyPaymentDateRange(month1WithinPayment, oldGcpStart, expectedLastWithinCommitmentDay);

    assertEquals(minimumDueForMonth1, month1WithinPayment.getAmountBilledCents());
    assertEquals(0, month1WithinPayment.getAmountPaidCents());
    assertEquals(expectedAmountUsedForMonth1, month1WithinPayment.getSubtotalCents());
    assertEquals(oldGcpCredit.getId(), month1WithinPayment.getCreditId());

    List<Payment> month2Payments = _paymentDao.findByInvoiceId(month2Invoice.getId(), true);
    assertEquals(2, month2Payments.size());
    Payment month2OldPayment =
        month2Payments.stream()
            .filter((p) -> p.getCreditId().equals(oldGcpCreditId))
            .findFirst()
            .get();
    Payment month2NewCreditPayment =
        month2Payments.stream()
            .filter((p) -> p.getCreditId().equals(newGcpCreditId))
            .findFirst()
            .get();

    Date oldMaxStart = DateUtils.addDays(month2Start, 19);
    Date newMaxStart = DateUtils.addDays(month3Start, -1);
    verifyPaymentDateRange(month2OldPayment, month2Start, oldMaxStart);
    verifyPaymentDateRange(month2NewCreditPayment, DateUtils.addDays(oldMaxStart, 1), newMaxStart);

    assertTrue(oldGcpAmount - minimumDueForMonth1 > 0);
    assertEquals(oldGcpAmount - minimumDueForMonth1, month2OldPayment.getAmountBilledCents());
    assertEquals(0, month2OldPayment.getAmountPaidCents());
    assertEquals(expectedAmountUsedMonth2, month2OldPayment.getSubtotalCents());
    assertEquals(oldGcpCredit.getId(), month2OldPayment.getCreditId());
    assertEquals(PaymentMethodType.GCP_MARKETPLACE_INVOICE, month2OldPayment.getPaymentMethod());

    assertEquals(minimumDueForMonth2New, month2NewCreditPayment.getAmountBilledCents());
    assertEquals(0, month2NewCreditPayment.getAmountPaidCents());
    assertEquals(expectedAmountUsedMonth2New, month2NewCreditPayment.getSubtotalCents());
    assertEquals(newGcpCredit.getId(), month2NewCreditPayment.getCreditId());
    assertEquals(
        PaymentMethodType.GCP_MARKETPLACE_INVOICE, month2NewCreditPayment.getPaymentMethod());
  }

  @Test
  public void
      testApplyGCPCreditWithOverlappingGCPCredit_newOverlappingGcpCreditAppliedAfterOldGcpCreditGoesOverCommitment_oldGCPCreditReappliedToInvoiceUpToDateItWasAppliedUpToPriorToActivationOfNewGCPCredit()
          throws Exception {
    // this test  simulates the scenario where we would have double reported usage, prior to the bug
    // fix ticket attached to this commit.
    // gcp credit activated. [month 1 / 20, month 2 / 20]
    // old gcp credit drawn down to negative in month 1
    // month 2/5 gcp credit activated. [month 2 / 1, month 4 / 1]
    // old gcp drawn down up until the 5th prior to and during rebill (prior to bug fix, rebill
    // resulted in new credit being prioritized for days 1-5), then the new gcp credit is
    // drawn down because it has an amount remaining.
    Date pNow = DateUtils.setMonths(nowDate(), Calendar.MAY);
    Date invoiceStart = DateUtils.truncate(pNow, Calendar.MONTH);
    Date month2Start = DateUtils.addMonths(invoiceStart, 1);
    Date month3Start = DateUtils.addMonths(invoiceStart, 2);
    Date month4Start = DateUtils.addMonths(invoiceStart, 3);
    Date oldGcpStart = DateUtils.addDays(invoiceStart, 20);
    Date oldGcpEnd = DateUtils.addDays(month2Start, 20);
    Date newGcpStart = month2Start;
    Date newGcpEnd = month4Start;
    long oldGcpAmount = 100000;
    long newGcpAmount = 1000000; // (monthly min) < entitlements which will be charged = 390000
    long entitlements = 1100000;
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    SalesforceOpportunity oldGcpOpp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            oldGcpStart, oldGcpEnd, oldGcpAmount, false);

    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);
    applyGcpMonthCommitmentV1ActivationCode(org, oldGcpOpp, invoiceStart);

    updatePlanEntitlementsMinimum(oldGcpOpp.getLicenseKey(), entitlements, invoiceStart, true);

    // sanity check credits were inserted properly
    Credit oldGcpCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.GCP_BILLING_ACCOUNT)
            .findFirst()
            .get();
    assertEquals(oldGcpAmount, oldGcpCredit.getAmountCents());
    assertEquals(oldGcpStart, oldGcpCredit.getStartDate());
    assertEquals(oldGcpEnd, oldGcpCredit.getEndDate());

    // process all of month 1
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month2Start, 3));
    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    Date creditProrateDate = DateUtils.addDays(oldGcpStart, 1);
    long daysInMonth = TimeUtils2.daysPerMonth(TimeUtils2.getCalendar(creditProrateDate));
    long daysCoveredByCredit =
        PartnerMarketplaceUtils.getDaysCoveredByCredit(oldGcpCredit, creditProrateDate);

    long expectedAmountUsedForMonth1 =
        (long) (entitlements * ((double) daysCoveredByCredit / daysInMonth));

    assertTrue(expectedAmountUsedForMonth1 > 0);
    assertEquals(oldGcpCredit.getCarryOverCents(), 0);
    assertTrue(oldGcpCredit.getAmountRemainingCents() < 0);
    assertTrue(oldGcpCredit.getAmountRemainingCents() < 0);
    assertTrue(oldGcpCredit.getAmountUsedCents() > 0);
    assertTrue(oldGcpCredit.getTotalBilledCents() > 0);
    assertEquals(expectedAmountUsedForMonth1, oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getTotalBilledCents(), oldGcpCredit.getAmountUsedCents());

    // process the first 4 days of month 2
    Date month2Day5BillTime =
        DateUtils.addHours(
            InvoiceTestUtils.getDailyBillingStartTime(DateUtils.addDays(month2Start, 4)), 1);
    _accountantSvc.processOrganizations(List.of(org.getId()), month2Day5BillTime);

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());

    SalesforceOpportunity newGcpOpp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            newGcpStart, newGcpEnd, newGcpAmount, false);

    applyGcpMonthCommitmentV1ActivationCode(org, newGcpOpp, month2Day5BillTime);
    updatePlanEntitlementsMinimum(
        newGcpOpp.getLicenseKey(), entitlements, month2Day5BillTime, false);

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    // this assertion failed prior to bug fix, because old GCP credit is not reapplied after rebill.
    assertEquals(expectedAmountUsedForMonth1, oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getAmountUsedCents(), oldGcpCredit.getTotalBilledCents());
    assertEquals(0, oldGcpCredit.getCarryOverCents());

    // process the rest of month 2
    _accountantSvc.processOrganizations(
        List.of(org.getId()),
        DateUtils.addHours(InvoiceTestUtils.getDailyBillingStartTime(month3Start), 1));

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    assertEquals(expectedAmountUsedForMonth1, oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getAmountUsedCents(), oldGcpCredit.getTotalBilledCents());
    assertEquals(0, oldGcpCredit.getCarryOverCents());

    long daysInMonth2 = TimeUtils2.daysPerMonth(TimeUtils2.getCalendar(month2Day5BillTime));
    // because 5 the new credit which has an amount remaining should be prioritized after
    // the old credit is re-applied up to day 5.
    long daysCoveredByCreditMonth2 = 4;
    long expectedAmountUsedMonth2 =
        (long) (entitlements * ((double) daysCoveredByCreditMonth2 / daysInMonth2));

    assertTrue(expectedAmountUsedMonth2 > 0);
    assertTrue(oldGcpCredit.getAmountRemainingCents() < 0);
    assertEquals(0, oldGcpCredit.getCarryOverCents());

    Credit newGcpCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getActivationCode().equals(newGcpOpp.getLicenseKey()))
            .findFirst()
            .get();
    ObjectId newGcpCreditId = newGcpCredit.getId();

    long minimumDueForMonth2New = newGcpAmount / 2;
    assertTrue(newGcpCredit.getAmountRemainingCents() < 0);
    assertTrue(newGcpCredit.getAmountUsedCents() > minimumDueForMonth2New);
    assertEquals(0, newGcpCredit.getCarryOverCents());
    assertTrue(entitlements > expectedAmountUsedMonth2);
    assertEquals(entitlements, newGcpCredit.getAmountUsedCents());
    assertEquals(newGcpCredit.getAmountUsedCents(), newGcpCredit.getTotalBilledCents());

    Invoice month2Invoice =
        _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month2Day5BillTime);

    verifyCreditDateRange(newGcpCredit, month2Invoice, month2Start, month3Start);

    // verify payments on month 1 and 2.
    Invoice month1Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), invoiceStart);

    List<Payment> month1Payments = _paymentDao.findByInvoiceId(month1Invoice.getId(), true);
    assertEquals(2, month1Payments.size());
    Payment month1WithinPayment =
        month1Payments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.GCP_MARKETPLACE_INVOICE)
            .findFirst()
            .get();
    Payment month1ElasticPayment =
        month1Payments.stream()
            .filter(
                (p) -> p.getPaymentMethod() == PaymentMethodType.GCP_MARKETPLACE_ELASTIC_INVOICE)
            .findFirst()
            .get();

    // entitlements min * x/31 - oldGcpCredit.amount > 0. min(x) = 3, so expect 3 days of witin
    // commitment line items
    Date expectedLastWithinCommitmentDay = DateUtils.addDays(oldGcpStart, 2);
    Date elasticMaxStart = DateUtils.addDays(month2Start, -1);
    verifyPaymentDateRange(month1WithinPayment, oldGcpStart, expectedLastWithinCommitmentDay);
    verifyPaymentDateRange(month1ElasticPayment, expectedLastWithinCommitmentDay, elasticMaxStart);

    assertEquals(month1WithinPayment.getAmountBilledCents(), oldGcpCredit.getAmountCents());
    assertEquals(month1WithinPayment.getAmountPaidCents(), 0);
    assertEquals(month1WithinPayment.getCreditId(), oldGcpCredit.getId());

    long expectedElasticUsage = expectedAmountUsedForMonth1 - oldGcpCredit.getAmountCents();
    assertTrue(expectedElasticUsage > 0);
    assertEquals(month1ElasticPayment.getAmountBilledCents(), expectedElasticUsage);
    assertEquals(month1ElasticPayment.getAmountPaidCents(), 0);
    assertEquals(month1ElasticPayment.getCreditId(), oldGcpCredit.getId());

    List<Payment> month2Payments = _paymentDao.findByInvoiceId(month2Invoice.getId(), true);
    assertEquals(2, month2Payments.size());

    Payment month2WithinPayment =
        month2Payments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(newGcpCreditId)
                        && p.getPaymentMethod() == PaymentMethodType.GCP_MARKETPLACE_INVOICE)
            .findFirst()
            .get();

    Date newMaxStart = DateUtils.addDays(month3Start, -3);
    verifyPaymentDateRange(month2WithinPayment, month2Start, newMaxStart);

    assertEquals(newGcpCredit.getAmountCents(), month2WithinPayment.getAmountBilledCents());
    assertEquals(newGcpCredit.getId(), month2WithinPayment.getCreditId());
    assertEquals(PaymentMethodType.GCP_MARKETPLACE_INVOICE, month2WithinPayment.getPaymentMethod());
  }

  @Test
  public void
      testMCCreditNotOverdrawnWhenAmountBilledGreaterThanAmountCentsWithCarryOverWhenCreditAppliedBothCreditsAreDrawndown_afterRollout()
          throws Exception {
    Date pNow = DateUtils.setYears(DateUtils.setMonths(nowDate(), Calendar.MAY), 2024);
    testMCCreditNotOverdrawnWhenAmountBilledGreaterThanAmountCentsWithCarryOverWhenCreditAppliedBothCreditsAreDrawndown(
        pNow);
  }

  private void
      testMCCreditNotOverdrawnWhenAmountBilledGreaterThanAmountCentsWithCarryOverWhenCreditAppliedBothCreditsAreDrawndown(
          Date pNow) throws Exception {
    Date invoiceStart = DateUtils.truncate(pNow, Calendar.MONTH);
    Date creditStart = DateUtils.truncate(pNow, Calendar.MONTH);
    Date creditEnd = DateUtils.addDays(DateUtils.addYears(creditStart, 1), -1);

    long quantity = 100;
    long rolloverQuantity = 20;

    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    SalesforceOpportunity opp =
        salesforceOpportunityFactory
            .insertAtlasPlanWithEntitlementsSalesforceOpportunityWithRollover(
                SalesforceProduct.ATLAS_PRO_MONTHLY_COMMITMENT.getProductCode(),
                creditStart,
                creditEnd,
                quantity,
                creditStart,
                DateUtils.addYears(creditEnd, 1),
                rolloverQuantity,
                true);

    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    _salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, opp.getLicenseKey(), DateUtils.addHours(invoiceStart, 2), null, auditInfo, true));
    updatePlanEntitlementsMinimum(opp.getLicenseKey(), 0, invoiceStart, true);

    Credit rolloverCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.ROLLOVER)
            .findFirst()
            .get();

    Credit mcCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.MONTHLY_COMMITMENT)
            .findFirst()
            .get();

    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 1));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 2));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 3));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 4));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 5));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 6));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 7));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 8));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 9));
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 10));
    mcCredit = _creditDao.findById(mcCredit.getId());
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 11));
    mcCredit = _creditDao.findById(mcCredit.getId());
    assertEquals(mcCredit.getTotalBilledCents(), mcCredit.getAmountCents());
    updatePlanEntitlementsMinimum(opp.getLicenseKey(), 2000000, invoiceStart, true);
    rolloverCredit = _creditDao.findById(rolloverCredit.getId());
    // Rollover credit gets billed one month minimum
    assertEquals(1000, rolloverCredit.getTotalBilledCents());
    _accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addMonths(DateUtils.addHours(invoiceStart, 3), 12));
    mcCredit = _creditDao.findById(mcCredit.getId());
    rolloverCredit = _creditDao.findById(rolloverCredit.getId());
    // MC Credit has been fully billed
    assertEquals(-1988000L, mcCredit.getAmountRemainingCents());
    assertEquals(mcCredit.getTotalBilledCents(), mcCredit.getAmountUsedCents());

    // Rollover credit gets billed for one more month minimum, the 2000 stands for this month and
    // past month's minimum
    assertEquals(2000, rolloverCredit.getTotalBilledCents());
    Invoice invoiceForLastMonth =
        _invoiceDao.findByOrgId(org.getId(), false).stream()
            .filter((i) -> i.getStartDate().equals(DateUtils.addMonths(invoiceStart, 11)))
            .findFirst()
            .get();
    List<Payment> payments = _paymentDao.findByInvoiceId(invoiceForLastMonth.getId(), true);
    assertEquals(2, payments.size());
    Payment withinCommitPayment =
        payments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.MONTHLY_COMMITMENT)
            .findFirst()
            .get();
    Payment elasticPayment =
        payments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .findFirst()
            .get();
    // Payment is 1000 which attests to the monthly minimum amount that was billed to the rollover
    assertEquals(1000, withinCommitPayment.getAmountBilledCents());
    assertEquals(12000, withinCommitPayment.getSubtotalCents());
    assertEquals(mcCredit.getId(), withinCommitPayment.getCreditId());
    // Created elastic payment with the exceeded usage
    assertEquals(1988000, elasticPayment.getAmountBilledCents());
    assertEquals(mcCredit.getId(), elasticPayment.getCreditId());
  }

  @Test
  public void
      testApplyGCPCreditWithOverlappingGCPCredit_newOverlappingGcpCreditAppliedAfterOldGcpCreditGoesOverCommitment_oldGcpCreditGoesOverInMonthOfNewGcpCreditActivation_oldGCPCreditReappliedToInvoiceUpToDateItWasAppliedUpToPriorToActivationOfNewGCPCredit()
          throws Exception {
    // this test  simulates the scenario where we would have double reported usage, prior to the bug
    // fix ticket attached to this commit.
    // gcp credit activated. [month 1 / 20, month 2 / 20]
    // old gcp credit drawn down to negative in month 2
    // month 2/5 gcp credit activated. [month 2 / 1, month 4 / 1]
    // old gcp drawn down up until the 5th prior to and during rebill (prior to bug fix, rebill
    // resulted in new credit being prioritized for days 1-5), then the new gcp credit is
    // drawn down because it has an amount remaining.
    Date pNow = DateUtils.setMonths(nowDate(), Calendar.MAY);
    Date invoiceStart = DateUtils.truncate(pNow, Calendar.MONTH);
    Date month2Start = DateUtils.addMonths(invoiceStart, 1);
    Date month3Start = DateUtils.addMonths(invoiceStart, 2);
    Date month4Start = DateUtils.addMonths(invoiceStart, 3);
    Date oldGcpStart = DateUtils.addDays(invoiceStart, 20);
    Date oldGcpEnd = DateUtils.addDays(month2Start, 20);
    Date newGcpStart = month2Start;
    Date newGcpEnd = month4Start;
    long oldGcpAmount = 461291L;
    long newGcpAmount = 1000000; // (monthly min) < entitlements which will be charged = 390000
    long entitlements = 1100000;

    SalesforceOpportunity oldGcpOpp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            oldGcpStart, oldGcpEnd, oldGcpAmount, false);

    Organization org = _organizationFactory.createAtlasOrganization(invoiceStart);

    applyGcpMonthCommitmentV1ActivationCode(org, oldGcpOpp, invoiceStart);
    updatePlanEntitlementsMinimum(oldGcpOpp.getLicenseKey(), entitlements, invoiceStart, true);

    // sanity check credits were inserted properly
    Credit oldGcpCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getType() == CreditType.GCP_BILLING_ACCOUNT)
            .findFirst()
            .get();
    ObjectId oldGcpCreditId = oldGcpCredit.getId();
    assertEquals(oldGcpAmount, oldGcpCredit.getAmountCents());
    assertEquals(oldGcpStart, oldGcpCredit.getStartDate());
    assertEquals(oldGcpEnd, oldGcpCredit.getEndDate());

    // process all of month 1
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(month2Start, 3));
    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    Date creditProrateDate = DateUtils.addDays(oldGcpStart, 1);
    long daysInMonth = TimeUtils2.daysPerMonth(TimeUtils2.getCalendar(creditProrateDate));
    long daysCoveredByCredit =
        PartnerMarketplaceUtils.getDaysCoveredByCredit(oldGcpCredit, creditProrateDate);

    long expectedAmountUsedForMonth1 =
        (long) (entitlements * ((double) daysCoveredByCredit / daysInMonth));

    assertTrue(expectedAmountUsedForMonth1 > 0);
    assertEquals(oldGcpCredit.getCarryOverCents(), 0);
    assertTrue(oldGcpCredit.getAmountRemainingCents() > 0);
    assertTrue(oldGcpCredit.getAmountUsedCents() > 0);
    assertTrue(oldGcpCredit.getTotalBilledCents() > 0);
    assertEquals(expectedAmountUsedForMonth1, oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getTotalBilledCents(), oldGcpCredit.getAmountUsedCents());

    // process the first 4 days of month 2
    Date month2Day5BillTime =
        DateUtils.addHours(
            InvoiceTestUtils.getDailyBillingStartTime(DateUtils.addDays(month2Start, 4)), 1);
    _accountantSvc.processOrganizations(List.of(org.getId()), month2Day5BillTime);

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    long amountUsedBeforeNewActivation = oldGcpCredit.getAmountUsedCents();
    long amountBilledBeforeNewActivation = oldGcpCredit.getTotalBilledCents();
    long carryOverBeforeNewActivation = oldGcpCredit.getCarryOverCents();
    assertTrue(oldGcpCredit.getAmountRemainingCents() < 0);
    assertEquals(oldGcpCredit.getTotalBilledCents(), oldGcpCredit.getAmountUsedCents());

    SalesforceOpportunity newGcpOpp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            newGcpStart, newGcpEnd, newGcpAmount, false);

    applyGcpMonthCommitmentV1ActivationCode(org, newGcpOpp, month2Day5BillTime);
    updatePlanEntitlementsMinimum(
        newGcpOpp.getLicenseKey(), entitlements, month2Day5BillTime, false);

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    // the old GCP credit is not re-applied because there is a new credit that should take
    // precedence
    // since it has a positive amount to draw down from
    assertEquals(oldGcpCredit.getAmountCents(), oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getAmountCents(), oldGcpCredit.getTotalBilledCents());
    assertEquals(0L, oldGcpCredit.getCarryOverCents());

    // process the rest of month 2
    _accountantSvc.processOrganizations(
        List.of(org.getId()),
        DateUtils.addHours(InvoiceTestUtils.getDailyBillingStartTime(month3Start), 1));

    oldGcpCredit = _creditDao.findById(oldGcpCredit.getId());
    assertEquals(oldGcpCredit.getAmountCents(), oldGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getAmountCents(), oldGcpCredit.getTotalBilledCents());
    assertEquals(0L, oldGcpCredit.getCarryOverCents());

    long daysInMonth2 = TimeUtils2.daysPerMonth(TimeUtils2.getCalendar(month2Day5BillTime));
    // because 5 the new credit which has an amount remaining should be prioritized after
    // the old credit is re-applied up to day 5.
    long daysCoveredByCreditMonth2 = 4;
    long expectedAmountUsedMonth2 =
        (long) (entitlements * ((double) daysCoveredByCreditMonth2 / daysInMonth2));

    assertTrue(expectedAmountUsedMonth2 > 0);
    assertEquals(0, oldGcpCredit.getAmountRemainingCents());
    assertEquals(oldGcpCredit.getAmountUsedCents(), oldGcpCredit.getTotalBilledCents());

    Credit newGcpCredit =
        _creditDao.findByOrgId(org.getId()).stream()
            .filter((c) -> c.getActivationCode().equals(newGcpOpp.getLicenseKey()))
            .findFirst()
            .get();
    ObjectId newGcpCreditId = newGcpCredit.getId();

    long expectedAmountUsedMonth2New = entitlements - expectedAmountUsedMonth2;
    long minimumDueForMonth2New = newGcpAmount / 2;
    assertTrue(expectedAmountUsedMonth2New > 0);
    assertTrue(newGcpCredit.getAmountRemainingCents() < 0);
    assertTrue(newGcpCredit.getAmountUsedCents() > minimumDueForMonth2New);
    assertEquals(0, newGcpCredit.getCarryOverCents());
    assertTrue(expectedAmountUsedMonth2New > expectedAmountUsedMonth2);
    assertEquals(newGcpCredit.getAmountUsedCents(), newGcpCredit.getTotalBilledCents());

    Invoice month2Invoice =
        _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), month2Day5BillTime);

    verifyCreditDateRange(
        oldGcpCredit, month2Invoice, month2Start, DateUtils.addDays(month2Start, 2));
    verifyCreditDateRange(
        newGcpCredit, month2Invoice, DateUtils.addDays(month2Start, 1), month3Start);

    // verify payments on month 1 and 2.
    Invoice month1Invoice = _invoiceDao.findMonthlyInvoiceByOrgIdForDate(org.getId(), invoiceStart);

    List<Payment> month1Payments = _paymentDao.findByInvoiceId(month1Invoice.getId(), true);
    List<Payment> month2Payments = _paymentDao.findByInvoiceId(month2Invoice.getId(), true);
    assertEquals(1, month1Payments.size());
    assertEquals(3, month2Payments.size());
    Payment month1WithinPayment =
        month1Payments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.GCP_MARKETPLACE_INVOICE)
            .findFirst()
            .get();

    Date expectedMaxStart = DateUtils.addDays(month2Start, -1);
    verifyPaymentDateRange(month1WithinPayment, oldGcpStart, expectedMaxStart);

    assertEquals(month1WithinPayment.getAmountBilledCents(), expectedAmountUsedForMonth1);
    assertEquals(month1WithinPayment.getAmountPaidCents(), 0);
    assertEquals(month1WithinPayment.getCreditId(), oldGcpCredit.getId());

    Payment month2OldPayment =
        month2Payments.stream()
            .filter(
                (p) ->
                    p.getCreditId().equals(oldGcpCreditId)
                        && p.getPaymentMethod() == PaymentMethodType.GCP_MARKETPLACE_INVOICE)
            .findFirst()
            .get();
    Payment month2NewCreditPayment =
        month2Payments.stream()
            .filter((p) -> p.getCreditId().equals(newGcpCreditId))
            .findFirst()
            .get();

    Date minStartDatePayment1 = month2Start;
    Date maxStartDatePayment1 = DateUtils.addDays(month2Start, 1);
    Date minStartDatePayment2 = DateUtils.addDays(month2Start, 1);
    Date maxStartDatePayment2 = DateUtils.addDays(month2Start, 3);
    Date minStartDatePayment3 = DateUtils.addDays(month2Start, 1);
    Date maxStartDatePayment3 = DateUtils.addDays(month3Start, -1);
    verifyPaymentDateRange(month2OldPayment, minStartDatePayment1, maxStartDatePayment1);
    verifyPaymentDateRange(month2NewCreditPayment, minStartDatePayment3, maxStartDatePayment3);

    assertTrue(month2OldPayment.getAmountBilledCents() > 0);
    assertTrue(month2NewCreditPayment.getAmountBilledCents() > 0);
    assertEquals(
        entitlements, month2OldPayment.getAmountBilledCents() + newGcpCredit.getAmountUsedCents());
    assertEquals(oldGcpCredit.getId(), month2OldPayment.getCreditId());
    assertEquals(PaymentMethodType.GCP_MARKETPLACE_INVOICE, month2OldPayment.getPaymentMethod());

    assertEquals(newGcpCredit.getId(), month2NewCreditPayment.getCreditId());
    assertEquals(
        PaymentMethodType.GCP_MARKETPLACE_INVOICE, month2NewCreditPayment.getPaymentMethod());
  }

  @Test
  public void applyCreditsOnlyAfterPayment() {
    // this is the case where customer exhausts credits and then a payment is created. Then there
    // are lagging line items that are billed on the days that the payment previously covered. We do
    // not want to mess with credits or line items that are covered by payment. So we should just
    // ignore them and bill "around" them

    // Day 1 - 15.75 this should be covered with $15.25 of credit
    // Day 2 - 15.75 partial credit coverage - payment should be created to cover 4.25
    // Day 3 - 15.75 no credits to cover

    Date now = nowDate();
    Date startOfMonth = DateTimeUtils.dateOf(LocalDate.now(clock).withDayOfMonth(1));
    Date secondOfMonth = DateUtils.addDays(startOfMonth, 1);
    Date thirdOfMonth = DateUtils.addDays(secondOfMonth, 1);
    Organization org = MmsFactory.createOrganizationWithNDSPlan(startOfMonth);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    _organizationDao.setNDSMaxOutstandingBillCents(org.getId(), 100);

    Credit credit =
        new Credit.Builder()
            .created(now)
            .type(CreditType.PREPAID_NDS)
            .activationCode("test")
            .startDate(DateUtils.addMonths(now, -2))
            .endDate(DateUtils.addMonths(now, 2))
            .orgId(org.getId())
            .amountCents(2000)
            .build();

    ObjectId creditId = _creditDao.save(credit);
    Group group = MmsFactory.createGroup(org);
    Group group2 = MmsFactory.createGroup(org);
    _usageFactory.generateHourlyAWSSubscriptionUsage(
        ObjectId.get(),
        group.getId(),
        AWSRegionName.US_EAST_2,
        AWSNDSInstanceSize.M50,
        1,
        startOfMonth,
        thirdOfMonth,
        false);
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(thirdOfMonth, 2));
    // first day covered fully by credits, second day we create a payment
    Payment payment = _paymentDao.findByOrgId(org.getId()).get(0);
    assertEquals(1148, payment.getAmountBilledCents());

    ObjectId paymentId = payment.getId();
    Invoice invoice = _invoiceDao.findPendingMonthlyByOrgId(org.getId());
    List<LineItem> creditLineItems = _lineItemDao.getCreditOnlyLineItems(invoice.getId());
    assertEquals(2, creditLineItems.size());

    LineItem creditLineItem1 = creditLineItems.get(0);
    assertEquals(startOfMonth, creditLineItem1.getStartDate());
    assertEquals(-1574, creditLineItem1.getTotalPriceCents());

    LineItem creditLineItem2 = creditLineItems.get(1);
    assertEquals(secondOfMonth, creditLineItem2.getStartDate());
    assertEquals(-426, creditLineItem2.getTotalPriceCents());

    // simulate late usage by generating more usage on first and second of month and rollback LBDs
    _lastBillDateSvc.updateLastBillDate(group2.getId(), DateUtils.addDays(startOfMonth, -1));
    _usageFactory.generateHourlyAWSSubscriptionUsage(
        ObjectId.get(),
        group2.getId(),
        AWSRegionName.US_EAST_2,
        AWSNDSInstanceSize.M50,
        1,
        startOfMonth,
        thirdOfMonth,
        false);

    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addHours(thirdOfMonth, 2));

    List<LineItem> creditLineItemsAfterLateUsage =
        _lineItemDao.getCreditOnlyLineItems(invoice.getId());
    assertEquals(2, creditLineItemsAfterLateUsage.size());
    // credit line items should not have changed
    assertEquals(creditLineItem1, creditLineItemsAfterLateUsage.get(0));
    assertEquals(creditLineItem2, creditLineItemsAfterLateUsage.get(1));

    List<Payment> paymentsAfterLateUsage = _paymentDao.findByOrgId(org.getId());
    assertEquals(2, paymentsAfterLateUsage.size());
    // payments are descending by created date
    Payment samePaymentFromBefore =
        paymentsAfterLateUsage.stream()
            .filter(paymentsAfter -> paymentsAfter.getId().equals(paymentId))
            .findFirst()
            .get();
    assertEquals(payment.getId(), samePaymentFromBefore.getId());
    assertEquals(payment.getAmountBilledCents(), samePaymentFromBefore.getAmountBilledCents());
    assertEquals(payment.getInvoiceId(), samePaymentFromBefore.getInvoiceId());
    assertEquals(payment.getCreated(), samePaymentFromBefore.getCreated());

    Payment paymentForLateUsage =
        paymentsAfterLateUsage.stream()
            .filter(paymentsAfter -> !paymentsAfter.getId().equals(paymentId))
            .findFirst()
            .get();
    // 1574 (daily charges per group) * 4 (2 groups 2 days) - 2000 (credit) - 1148(previous payment)
    // =3148
    assertEquals(3148, paymentForLateUsage.getAmountBilledCents());
  }

  private void verifyPaymentDateRange(
      Payment pPayment, Date pMinStartDate, Date pMaxStartDateForPayment) {
    Date minStartDateForPayment = _lineItemDao.findById(pPayment.getMinLineItemId()).getStartDate();
    Date maxStartDateForPayment = _lineItemDao.findById(pPayment.getMaxLineItemId()).getStartDate();

    assertEquals(pMinStartDate, minStartDateForPayment);
    assertEquals(pMaxStartDateForPayment, maxStartDateForPayment);
  }

  private void verifyCreditDateRange(
      Credit pCredit, Invoice pInvoice, Date pMinStartDate, Date pMaxEndDate) {
    Date minLineItemStartDateForOldCredit =
        _lineItemDao.findByInvoiceId(pInvoice.getId()).stream()
            .filter((l) -> pCredit.getId().equals(l.getCreditId()))
            .min(Comparator.comparing(LineItem::getStartDate))
            .get()
            .getStartDate();
    Date maxLineItemEndDateForOldCredit =
        _lineItemDao.findByInvoiceId(pInvoice.getId()).stream()
            .filter((l) -> pCredit.getId().equals(l.getCreditId()))
            .max(Comparator.comparing(LineItem::getEndDate))
            .get()
            .getEndDate();

    assertEquals(pMaxEndDate, maxLineItemEndDateForOldCredit);
    assertEquals(pMinStartDate, minLineItemStartDateForOldCredit);
  }

  /** No longer supported for default activation */
  private void applyGcpMonthCommitmentV1ActivationCode(
      Organization org, SalesforceOpportunity opp, Date activatedDate) throws Exception {
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    Credit credit =
        _creditFactory.createGcpMarketplaceMonthlyCommitmentV1(org.getId(), opp, activatedDate);

    BasicDBObject params = new BasicDBObject();
    params.put(CREDIT_PULLED_FORWARD, false);
    params.put(ORGANIZATION, org.getId());
    params.put(OBJECT_IDS_FIELD, List.of(credit.getId()));
    params.put(ACTIVATED_DATE, activatedDate);
    params.put(ACTIVATION_CODE, opp.getLicenseKey());
    params.put(AUDIT_INFO, AuditInfoUtils.serialize(auditInfo));

    activationCodeRetroactiveApplicationJobHandler.doHandleWork(
        List.of(credit.getId()), params, null);

    // Wait for rebill
    jobQueueTestUtils.processAllJobsInQueue();
  }

  private void updatePlanEntitlementsMinimum(
      String pActivationCode, long pNewMinimum, Date pNow, boolean pSkipRebill) throws Exception {
    List<OrgPrepaidPlan> orgPrepaidPlans =
        _orgPrepaidPlanDao.findOrgPrepaidPlansByActivationCode(pActivationCode);
    assertEquals(1, orgPrepaidPlans.size());
    OrgPrepaidPlan orgPrepaidPlan = orgPrepaidPlans.get(0);
    List<PrepaidPlan> plansToUpdate =
        orgPrepaidPlan.getPrepaidPlans().stream()
            .map(
                (p) -> {
                  if (p.getActivationCode().equals(pActivationCode)) {
                    return new PrepaidPlan.Builder(p)
                        .subscriptionPricingModel(
                            SubscriptionPricingModel.tiered(
                                pNewMinimum, p.getSubscriptionPricingModel().getPricingTiers()))
                        .build();
                  } else {
                    return p;
                  }
                })
            .collect(Collectors.toList());
    _orgPrepaidPlanDao.updateByOrgId(orgPrepaidPlan.getOrgId(), plansToUpdate);
    if (!pSkipRebill) {
      _rebillSvc.rebillOrganization(
          _organizationDao.findById(orgPrepaidPlan.getOrgId()),
          pNow,
          AuditInfoHelpers.fromSystem());
    }
  }
}
