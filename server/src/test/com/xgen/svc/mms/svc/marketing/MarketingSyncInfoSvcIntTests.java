package com.xgen.svc.mms.svc.marketing;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.marketing.AggregatedMarketingSyncInfo;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.Date;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

public class MarketingSyncInfoSvcIntTests extends BaseSvcTest {

  @Inject private MarketingSyncInfoSvc _marketingSyncInfoSvc;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
  }

  // TODO: as part of CLOUDP-102706 we are ignoring this test but should remove
  @Test
  @Ignore
  public void testGetAccountMarketingSyncInfo() {
    AggregatedMarketingSyncInfo info =
        getMarketingSyncInfoSvc()
            .getAccountMarketingSyncInfo(Arrays.asList(oid(218), oid(226)), new Date());
    assertEquals(1, info.getNDSFreeClusterCount());
    assertNotNull(info.getNDSFreeClusterFirstCreated());
    AggregatedMarketingSyncInfo info2 =
        getMarketingSyncInfoSvc()
            .getAccountMarketingSyncInfo(Arrays.asList(oid(201), oid(202), oid(203)), new Date());
    assertEquals(0, info2.getNDSFreeClusterCount());
    assertNull(info2.getNDSFreeClusterFirstCreated());
  }

  protected MarketingSyncInfoSvc getMarketingSyncInfoSvc() {
    return _marketingSyncInfoSvc;
  }
}
