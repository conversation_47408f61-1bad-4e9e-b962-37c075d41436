package com.xgen.svc.mms.svc.billing.pricing;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.units._public.model.SKUUnits;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.usagedimensions.AwsImportedUsageDimensions;
import com.xgen.module.metering.common.view.MeterUsageAggregateView;
import com.xgen.module.metering.server.dao.MeterUsageDao;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.OrgBillContext;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.svc.billing.pricing.svc.PricingStrategySupplementalUsageSvc;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class PricingStrategySupplementalUsageSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private MeterUsageDao meterUsageDao;
  @Inject private PricingStrategySupplementalUsageSvc pricingStrategySupplementalUsageSvc;

  @Test
  public void getUsageDateGroupAdditionUsage_billDateAfterUsageDate() {
    MeterId meterId = MeterId.NDS_AWS_DATA_TRANSFER_INTERNET;
    Group group = new Group();
    group.setId(new ObjectId());
    Date usageDate = DateTimeUtils.dateOf(LocalDate.of(2023, 1, 1));
    Date billDate = DateTimeUtils.dateOf(LocalDate.of(2023, 1, 2));

    List<MeterUsageAggregateView> meterUsage =
        List.of(
            new MeterUsageAggregateView.Builder(
                    new MeterUsage.Builder(AppEnv.TEST)
                        .meterId(meterId)
                        .groupId(group.getId())
                        .startTime(usageDate)
                        .endTime(DateUtils.addDays(usageDate, 1))
                        .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 2, 0, 0)))
                        .usageDimensions(
                            new AwsImportedUsageDimensions.Builder()
                                .awsRegionName(AWSRegionName.AF_SOUTH_1)
                                .cloudProviderReportDate(usageDate)
                                .clusterName("testCluster1")
                                .clusterUniqueId(new ObjectId())
                                .rsId("testRSID1")
                                .build())
                        .quantity(10)
                        .unit(SKUUnits.GIGABYTES)
                        .build())
                .build());

    OrgBillContext orgBillContext =
        new OrgBillContext(
            new Organization.Builder().name("unit test").build(),
            group,
            new Invoice.Builder().build(),
            billDate,
            DateUtils.addDays(billDate, 1),
            new OrgPlan.Builder().build(),
            new OrgPrepaidPlan.Builder().build());

    meterUsageDao.saveUsages(
        List.of(
            // Before SLA window
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(usageDate)
                .endTime(DateUtils.addDays(usageDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 1, 23, 59)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(usageDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // In SLA window
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(usageDate)
                .endTime(DateUtils.addDays(usageDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 2, 1, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(usageDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // In daily billing window
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(usageDate)
                .endTime(DateUtils.addDays(usageDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 2, 5, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(usageDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build()));

    List<MeterUsage> supplementalMeterUsage =
        pricingStrategySupplementalUsageSvc.getUsageDateGroupAdditionUsage(
            meterUsage, orgBillContext);

    assertEquals(2, supplementalMeterUsage.size());
  }

  @Test
  public void getUsageDateGroupAdditionUsage_billDateEqualUsageDate() {
    MeterId meterId = MeterId.NDS_AWS_DATA_TRANSFER_INTERNET;
    Group group = new Group();
    group.setId(new ObjectId());
    Date usageDate = DateTimeUtils.dateOf(LocalDate.of(2023, 1, 1));
    Date billDate = usageDate;

    List<MeterUsageAggregateView> meterUsage =
        List.of(
            new MeterUsageAggregateView.Builder(
                    new MeterUsage.Builder(AppEnv.TEST)
                        .meterId(meterId)
                        .groupId(group.getId())
                        .startTime(usageDate)
                        .endTime(DateUtils.addDays(usageDate, 1))
                        .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 1, 0, 0)))
                        .usageDimensions(
                            new AwsImportedUsageDimensions.Builder()
                                .awsRegionName(AWSRegionName.AF_SOUTH_1)
                                .cloudProviderReportDate(usageDate)
                                .clusterName("testCluster1")
                                .clusterUniqueId(new ObjectId())
                                .rsId("testRSID1")
                                .build())
                        .quantity(10)
                        .unit(SKUUnits.GIGABYTES)
                        .build())
                .build());

    OrgBillContext orgBillContext =
        new OrgBillContext(
            new Organization.Builder().name("unit test").build(),
            group,
            new Invoice.Builder().build(),
            billDate,
            DateUtils.addDays(billDate, 1),
            new OrgPlan.Builder().build(),
            new OrgPrepaidPlan.Builder().build());

    meterUsageDao.saveUsages(
        List.of(
            // In SLA window
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(usageDate)
                .endTime(DateUtils.addDays(usageDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 1, 1, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(usageDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // previous day
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(usageDate)
                .endTime(DateUtils.addDays(usageDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2022, 12, 31, 5, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(usageDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build()));

    List<MeterUsage> supplementalMeterUsage =
        pricingStrategySupplementalUsageSvc.getUsageDateGroupAdditionUsage(
            meterUsage, orgBillContext);

    assertEquals(1, supplementalMeterUsage.size());
  }

  @Test
  public void getUsageMonthGroupAdditionUsage_billDateWithinUsageMonth() {
    MeterId meterId = MeterId.NDS_AWS_DATA_TRANSFER_INTERNET;
    Group group = new Group();
    group.setId(new ObjectId());
    Date billDate = DateTimeUtils.dateOf(LocalDate.of(2023, 1, 7));

    List<MeterUsageAggregateView> meterUsage =
        List.of(
            new MeterUsageAggregateView.Builder(
                    new MeterUsage.Builder(AppEnv.TEST)
                        .meterId(meterId)
                        .groupId(group.getId())
                        .startTime(billDate)
                        .endTime(DateUtils.addDays(billDate, 1))
                        .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 2, 0, 0)))
                        .usageDimensions(
                            new AwsImportedUsageDimensions.Builder()
                                .awsRegionName(AWSRegionName.AF_SOUTH_1)
                                .cloudProviderReportDate(billDate)
                                .clusterName("testCluster1")
                                .clusterUniqueId(new ObjectId())
                                .rsId("testRSID1")
                                .build())
                        .quantity(10)
                        .unit(SKUUnits.GIGABYTES)
                        .build())
                .build());

    OrgBillContext orgBillContext =
        new OrgBillContext(
            new Organization.Builder().name("unit test").build(),
            group,
            new Invoice.Builder().build(),
            billDate,
            DateUtils.addDays(billDate, 1),
            new OrgPlan.Builder().build(),
            new OrgPrepaidPlan.Builder().build());

    meterUsageDao.saveUsages(
        List.of(
            // Previous Day usage
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(DateUtils.addDays(billDate, -1))
                .endTime(billDate)
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 6, 5, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(billDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // Yesterday usage date in SLA
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(DateUtils.addDays(billDate, -1))
                .endTime(billDate)
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 7, 1, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(billDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // Today usage date in SLA
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(billDate)
                .endTime(DateUtils.addDays(billDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 1, 7, 1, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(billDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build()));

    List<MeterUsage> supplementalMeterUsage =
        pricingStrategySupplementalUsageSvc.getUsageMonthGroupAdditionUsage(
            meterUsage, orgBillContext);

    assertEquals(2, supplementalMeterUsage.size());
  }

  @Test
  public void getUsageMonthGroupAdditionUsage_billDateAfterUsageMonth() {
    MeterId meterId = MeterId.NDS_AWS_DATA_TRANSFER_INTERNET;
    Group group = new Group();
    group.setId(new ObjectId());
    YearMonth usageMonth = YearMonth.of(2023, 1);
    Date billDate = DateTimeUtils.dateOf(LocalDate.of(2023, 2, 7));

    List<MeterUsageAggregateView> meterUsage =
        List.of(
            new MeterUsageAggregateView.Builder(
                    new MeterUsage.Builder(AppEnv.TEST)
                        .meterId(meterId)
                        .groupId(group.getId())
                        .startTime(DateTimeUtils.dateOf(usageMonth.atDay(1)))
                        .endTime(DateTimeUtils.dateOf(usageMonth.atDay(2)))
                        .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 2, 7, 0, 0)))
                        .usageDimensions(
                            new AwsImportedUsageDimensions.Builder()
                                .awsRegionName(AWSRegionName.AF_SOUTH_1)
                                .cloudProviderReportDate(billDate)
                                .clusterName("testCluster1")
                                .clusterUniqueId(new ObjectId())
                                .rsId("testRSID1")
                                .build())
                        .quantity(10)
                        .unit(SKUUnits.GIGABYTES)
                        .build())
                .build());

    OrgBillContext orgBillContext =
        new OrgBillContext(
            new Organization.Builder().name("unit test").build(),
            group,
            new Invoice.Builder().build(),
            billDate,
            DateUtils.addDays(billDate, 1),
            new OrgPlan.Builder().build(),
            new OrgPrepaidPlan.Builder().build());

    meterUsageDao.saveUsages(
        List.of(
            // Reported Previous Day
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(DateTimeUtils.dateOf(usageMonth.atDay(1)))
                .endTime(DateTimeUtils.dateOf(usageMonth.atDay(2)))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 2, 6, 5, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(billDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // Yesterday usage date in SLA
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(DateTimeUtils.dateOf(usageMonth.atDay(2)))
                .endTime(DateTimeUtils.dateOf(usageMonth.atDay(3)))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 2, 7, 1, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(billDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build(),
            // next month usage in SLA
            new MeterUsage.Builder(AppEnv.TEST)
                .meterId(meterId)
                .groupId(group.getId())
                .startTime(billDate)
                .endTime(DateUtils.addDays(billDate, 1))
                .reportedTime(DateTimeUtils.dateOf(LocalDateTime.of(2023, 2, 7, 1, 0)))
                .usageDimensions(
                    new AwsImportedUsageDimensions.Builder()
                        .awsRegionName(AWSRegionName.AF_SOUTH_1)
                        .cloudProviderReportDate(billDate)
                        .clusterName("testCluster2")
                        .clusterUniqueId(new ObjectId())
                        .rsId("testRSID2")
                        .build())
                .quantity(10)
                .unit(SKUUnits.GIGABYTES)
                .build()));

    List<MeterUsage> supplementalMeterUsage =
        pricingStrategySupplementalUsageSvc.getUsageMonthGroupAdditionUsage(
            meterUsage, orgBillContext);

    assertEquals(2, supplementalMeterUsage.size());
  }
}
