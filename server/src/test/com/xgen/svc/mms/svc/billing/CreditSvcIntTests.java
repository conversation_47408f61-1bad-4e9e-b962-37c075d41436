package com.xgen.svc.mms.svc.billing;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static com.xgen.svc.mms.model.billing.CreditType.AZURE_MONTHLY_COMMITMENT;
import static com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory.SalesforceProduct.AWS_MARKETPLACE_CREDIT;
import static com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory.SalesforceProduct.AWS_MARKETPLACE_MONTHLY_COMMITMENT_CREDIT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.activity._public.svc.event.EventSvc;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.payments.credit._public.model.CreditFieldsUpdateRequest;
import com.xgen.cloud.payments.grpc._public.client.RefundQueryingClient;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.PayingOrgDao;
import com.xgen.svc.mms.dao.billing.SalesforceOpportunityDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.ApplyActivationCodeRequest;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.GCPMarketplaceEntitlement;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.model.billing.SalesforceOpportunity;
import com.xgen.svc.mms.model.billing.SalesforceOpportunityLineItem;
import com.xgen.svc.mms.model.billing.ServiceCreditRevenueReason;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.svc.mms.util.billing.scenarios.SelfServeScenarios;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.FlexCommitCreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.FlexCommitCreditFactory.FlexCommitCreditRequest;
import com.xgen.svc.mms.util.billing.testFactories.GcpMarketplaceEntitlementFactory;
import com.xgen.svc.mms.util.billing.testFactories.GcpSalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.UsageFactory;
import jakarta.inject.Inject;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CreditSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private CreditSvc creditSvc;
  @Inject private CreditDao creditDao;
  @Inject private CreditFactory creditFactory;
  @Inject private EventSvc eventSvc;
  @Inject private SalesforceOpportunityDao salesforceOpportunityDao;
  @Inject private GcpMarketplaceEntitlementFactory gcpMarketplaceEntitlementFactory;
  @Inject private GcpSalesforceOpportunityFactory gcpSalesforceOpportunityFactory;
  @Inject private OrganizationFactory organizationFactory;
  @Inject private AccountantSvc accountantSvc;
  @Inject private SalesSoldDealActivationSvc salesSoldDealActivationSvc;
  @Inject private SalesforceOpportunityFactory salesforceOpportunityFactory;
  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;
  @Inject private FlexCommitCreditFactory flexCommitCreditFactory;
  @Inject private FeatureFlagSvc featureFlagSvc;
  @Inject private OrganizationDao organizationDao;
  @Inject private PayingOrgDao payingOrgDao;
  @Inject private SelfServeScenarios selfServeScenarios;
  @Inject private PaymentSvc paymentSvc;
  @Inject private InvoiceSvc invoiceSvc;
  @Inject private InvoiceDao invoiceDao;
  @Inject private JobQueueTestUtils jobQueueTestUtils;
  @Inject private RefundQueryingClient refundQueryingClient;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  private final String USERNAME = "phillip.quiza";
  private final String LOCALHOST = "localhost";

  private AppUser user;
  private AuditInfo auditInfo;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();

    user = new AppUser();
    user.setUsername(USERNAME);

    auditInfo = MmsFactory.createAuditInfoFromUiCall(user, false, LOCALHOST);

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/CreditDao/credits.json.ftl", null, Credit.DB_NAME, Credit.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/InvoiceDao/invoices.json.ftl",
        null,
        Invoice.DB_NAME,
        Invoice.COLLECTION_NAME);
    salesforceProductCodeDao.syncDocuments();
  }

  @Test
  public void testHasGcpCreditsWithUnbilledCommitment() {
    // validate hasGcpCreditsWithUnbilledCommitment returns true when orgId has an active gcp
    // credit
    // has a portion of its total term commitment that has not yet been billed.

    // 1. month 1 day 1 - create org
    // 2. month 1 day 3 - activate prepaid gcp marketplace opp with 100k commitment
    // 3. generate usage from month 1 day 1 to month 2 day 1 = < 100k
    // 4. run daily billing up to month 2 day 3
    // 5. generate usage from month 2 day 1 to month 3 day 1 = > 100k usage
    // 6. run daily billing up to month 3 day 3

    Date now = new Date();
    Date month1 = DateUtils.truncate(now, Calendar.MONTH);
    Date monthNegative1 = DateUtils.addMonths(month1, -1);
    Date month2 = DateUtils.addMonths(month1, 1);
    Date month3 = DateUtils.addMonths(month1, 2);
    Date month12 = DateUtils.addMonths(month1, 11);
    Date month1Plus3 = DateUtils.addDays(month1, 3);
    Date month2Plus3 = DateUtils.addDays(month2, 3);
    Date month3Plus3 = DateUtils.addDays(month3, 3);
    Date month12Plus3 = DateUtils.addDays(month12, 3);

    // 1. month 1 day 1 - create org
    Organization org = organizationFactory.createAtlasOrganization(month1);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Group group = organizationFactory.createNdsGroup(org, month1);
    long totalEntitlementCents = ********;
    SalesforceOpportunity opp =
        gcpSalesforceOpportunityFactory.insertProMonthlyCommitmentOpportunity(
            month1, month12, totalEntitlementCents, false);

    assertFalse(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), month2));

    accountantSvc.processOrganizations(List.of(org.getId()), month1Plus3);
    assertFalse(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), month2));

    // 2. month 1 - create legacy gcp marketplace credit with 100k commitment
    creditFactory.createGcpMarketplaceMonthlyCommitment(
        org.getId(), month1, month12, totalEntitlementCents, false);
    assertTrue(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), month2));

    // verify that passing a dates outside expiration returns false
    assertFalse(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), month12Plus3));
    assertFalse(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), monthNegative1));

    // 3. generate usage from month 1 day 1 to month 2 day 1 = < 100k
    UsageFactory.generateHourlyAWSSubscriptionUsage(
        new ObjectId(),
        group.getId(),
        AWSRegionName.US_EAST_1,
        AWSNDSInstanceSize.R200,
        1,
        month1,
        month2);

    // 4. run daily billing up to month 2 day 3
    accountantSvc.processOrganizations(List.of(org.getId()), month2Plus3);
    assertTrue(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), month2));

    // 5. generate usage from month 2 day 1 to month 3 day 1 = > 100k usage
    UsageFactory.generateHourlyAWSSubscriptionUsage(
        new ObjectId(),
        group.getId(),
        AWSRegionName.US_EAST_1,
        AWSNDSInstanceSize.R200,
        150, // generate a lot of usage to be  sure it covers the total commitment
        month2,
        month3);

    // 6. run daily billing up to month 3 day 3
    accountantSvc.processOrganizations(List.of(org.getId()), month3Plus3);
    assertFalse(creditSvc.hasGcpCreditsWithUnbilledCommitment(org.getId(), month2));
  }

  @Test
  public void test_hasActiveGcpFlatFeeMonthlyCommitment() {
    Date now = new Date();
    Date thisMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(thisMonth, 1);

    Organization org = organizationFactory.createAtlasOrganization(now);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Credit gcpFlatFeeMonthlyCommitment =
        creditFactory.createGcpMarketplaceMonthlyCommitment(
            org.getId(), thisMonth, nextMonth, 1000L, true);

    // should return true if the credit is active method
    assertThat(creditSvc.hasActiveGcpFlatFeeMonthlyCommitment(org.getId(), now), is(true));

    creditDao.applyCredit(gcpFlatFeeMonthlyCommitment.getId(), 2000L);

    // should return true even if the commitment is complete
    assertThat(creditSvc.hasActiveGcpFlatFeeMonthlyCommitment(org.getId(), now), is(true));

    creditDao.delete(gcpFlatFeeMonthlyCommitment.getId());

    Organization org2 = organizationFactory.createAtlasOrganization(now);
    Credit gcpUsageBasedMonthlyCommitment =
        creditFactory.createGcpMarketplaceMonthlyCommitment(
            org2.getId(), thisMonth, nextMonth, 1000L, false);

    // should NOT return true if there is a non-flatfee credit
    assertThat(creditSvc.hasActiveGcpFlatFeeMonthlyCommitment(org2.getId(), now), is(false));
  }

  @Test
  public void testHasAwsCreditsWithUnbilledCommitment() throws SvcException {
    Date now = new Date();
    Date thisMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(thisMonth, 1);

    Organization org = organizationFactory.createAtlasOrganization(now);
    paymentMethodStubber.stubPaymentMethod(org.getId());

    Credit.Builder awsCreditBuilder =
        new Credit.Builder()
            .activationCode("activationCode")
            .amountCents(1000L)
            .amountRemainingCents(1000L)
            .startDate(thisMonth)
            .endDate(nextMonth)
            .orgId(org.getId());

    Credit awsPrepaidCredit =
        awsCreditBuilder
            .type(CreditType.AWS_PREPAID)
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder()
                    .productCode(AWS_MARKETPLACE_CREDIT.getProductCode())
                    .build())
            .build();
    creditSvc.issueSalesSoldAtlasCredit(awsPrepaidCredit, AuditInfoHelpers.fromSystem());

    assertThat(creditSvc.hasAwsCreditsWithUnbilledCommitment(org.getId(), now), is(false));

    Credit awsMonthlyCommitCredit =
        awsCreditBuilder
            .type(CreditType.AWS_MONTHLY_COMMITMENT)
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder()
                    .productCode(AWS_MARKETPLACE_MONTHLY_COMMITMENT_CREDIT.getProductCode())
                    .build())
            .build();
    creditSvc.issueSalesSoldAtlasCredit(awsMonthlyCommitCredit, AuditInfoHelpers.fromSystem());

    assertThat(creditSvc.hasAwsCreditsWithUnbilledCommitment(org.getId(), now), is(true));
  }

  @Test
  public void testHasMonthlyCommitmentCreditsWithUnbilledCommitment() throws Exception {
    // validate hasMonthlyCommitmentCreditsWithUnbilledCommitment returns true when orgId has an
    // active MC
    // credit
    // has a portion of its total term commitment that has not yet been billed.

    // 1. month 1 day 1 - create org
    // 2. month 1 day 3 - activate  monthly commit opp with 100k commitment
    // 3. generate usage from month 1 day 1 to month 2 day 1 = < 100k
    // 4. run daily billing up to month 2 day 3
    // 5. generate usage from month 2 day 1 to month 3 day 1 = > 100k usage
    // 6. run daily billing up to month 3 day 3

    Date now = new Date();
    Date month1 = DateUtils.truncate(now, Calendar.MONTH);
    Date monthNegative1 = DateUtils.addMonths(month1, -1);
    Date month2 = DateUtils.addMonths(month1, 1);
    Date month3 = DateUtils.addMonths(month1, 2);
    Date month12 = DateUtils.addMonths(month1, 12);
    Date month13 = DateUtils.addMonths(month1, 13);
    Date month1Plus3 = DateUtils.addDays(month1, 3);
    Date month2Plus3 = DateUtils.addDays(month2, 3);
    Date month3Plus3 = DateUtils.addDays(month3, 3);
    Date month13Plus3 = DateUtils.addDays(month13, 3);

    // 1. month 1 day 1 - create org
    Organization org = organizationFactory.createAtlasOrganization(month1);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Group group = organizationFactory.createNdsGroup(org, month1);
    long totalEntitlementCents = ********;
    SalesforceOpportunity opp =
        salesforceOpportunityFactory.insertAtlasProMonthlyCommitmentOpportunity(
            month1, month12, totalEntitlementCents, false);
    doReturn(List.of()).when(refundQueryingClient).getRefundsByInvoiceId(any());

    assertFalse(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(org.getId(), month2));

    accountantSvc.processOrganizations(List.of(org.getId()), month1Plus3);
    assertFalse(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(org.getId(), month2));

    // 2. month 1 day 3 - activate  monthly commit opp with 100k commitment
    salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org, opp.getLicenseKey(), month1Plus3, null, AuditInfoHelpers.fromSystem(), true));
    assertTrue(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(org.getId(), month2));

    // verify that passing a dates outside expiration returns false
    assertFalse(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(
            org.getId(), month13Plus3));
    assertFalse(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(
            org.getId(), monthNegative1));

    // 3. generate usage from month 1 day 1 to month 2 day 1 = < 100k
    UsageFactory.generateHourlyAWSSubscriptionUsage(
        new ObjectId(),
        group.getId(),
        AWSRegionName.US_EAST_1,
        AWSNDSInstanceSize.R200,
        1,
        month1,
        month2);

    // 4. run daily billing up to month 2 day 3
    accountantSvc.processOrganizations(List.of(org.getId()), month2Plus3);
    assertTrue(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(org.getId(), month2));

    // 5. generate usage from month 2 day 1 to month 3 day 1 = > 100k usage
    UsageFactory.generateHourlyAWSSubscriptionUsage(
        new ObjectId(),
        group.getId(),
        AWSRegionName.US_EAST_1,
        AWSNDSInstanceSize.R200,
        150, // generate a lot of usage to be  sure it covers the total commitment
        month2,
        month3);

    // 6. run daily billing up to month 3 day 3
    accountantSvc.processOrganizations(List.of(org.getId()), month3Plus3);
    assertFalse(
        creditSvc.hasDirectMonthlyCommitmentCreditsWithUnbilledCommitment(org.getId(), month2));
  }

  @Test
  public void testIssueGenericCredit() throws SvcException {
    AppUser user = new AppUser();
    user.setUsername("phillip.quiza");
    Credit.Builder builder =
        new Credit.Builder("We owe you money")
            .id(ObjectId.get())
            .orgId(oid(201))
            .created(new Date())
            .reason("reason")
            .issuer("phillip.quiza")
            .type(CreditType.GENERIC)
            .revenueReason(ServiceCreditRevenueReason.OTHER)
            .startDate(new Date())
            .endDate(new Date());
    builder.amountCents(2000);
    Credit credit = builder.build();

    assertGenericCredit(credit);
  }

  @Test
  public void testIssueNDSCreditWithMissingActivationCode() {
    Credit.Builder builder =
        new Credit.Builder("We owe you money")
            .id(ObjectId.get())
            .orgId(oid(201))
            .created(new Date())
            .reason("reason")
            .issuer(USERNAME)
            .type(CreditType.PREPAID_NDS);
    builder.amountCents(2000);
    Credit credit = builder.build();
    assertThrows(
        IllegalStateException.class, () -> creditSvc.issueSalesSoldAtlasCredit(credit, auditInfo));
  }

  private void assertGenericCredit(Credit credit) throws SvcException {
    int initalCreditCount = creditSvc.findAllList().size();
    creditSvc.issueServiceCreditRetroactively(credit, auditInfo);
    ObjectId id = credit.getId();
    assertNotNull(id);
    Credit rCredit = creditSvc.findById(id);
    assertEquals(id, rCredit.getId());
    assertEquals(oid(201), rCredit.getOrgId());
    assertEquals("reason", rCredit.getReason());
    assertEquals(USERNAME, rCredit.getIssuer());
    assertEquals(1, creditSvc.findAllList().size() - initalCreditCount);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit e = (BillingAudit) events.get(0);
    assertEquals(2000, e.getCreditAmountCents());

    Credit.Builder builder1 =
        new Credit.Builder("We owe you money")
            .orgId(oid(204))
            .created(new Date())
            .reason("reason")
            .issuer(USERNAME)
            .amountCents(100)
            .amountRemainingCents(50)
            .startDate(new Date())
            .endDate(new Date());
    Credit credit1 = builder1.build();

    try {
      creditSvc.issueServiceCreditRetroactively(credit1, auditInfo);
      fail();
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INVALID_CREDIT_AMOUNT, ex.getErrorCode());
    }
    assertEquals(1, creditSvc.findAllList().size() - initalCreditCount);
  }

  @Test
  public void testIssueGenericCreditEvent() throws SvcException {
    AppUser user = new AppUser();
    user.setUsername("phillip.quiza");
    Credit.Builder builder =
        new Credit.Builder("We owe you money")
            .orgId(oid(209))
            .created(new Date())
            .reason("reason")
            .issuer("phillip.quiza")
            .amountCents(2000)
            .type(CreditType.GENERIC)
            .startDate(new Date())
            .revenueReason(ServiceCreditRevenueReason.PRODUCT_ISSUE)
            .endDate(new Date());
    Credit credit = builder.build();
    assertGenericCreditAudit(credit);
  }

  @Test
  public void testIssuePrepaidNDSEvent() throws SvcException {
    Credit.Builder builder =
        new Credit.Builder("We owe you money")
            .orgId(oid(209))
            .created(new Date())
            .reason("reason")
            .issuer(USERNAME)
            .amountCents(2000)
            .type(CreditType.PREPAID_NDS)
            .activationCode("1234");
    Credit credit = builder.build();

    creditSvc.issueSalesSoldAtlasCredit(credit, auditInfo);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    assertEquals(BillingEvent.Type.CREDIT_ISSUED, events.get(0).getEventType());
  }

  @Test
  public void testIssueGcpMarketplaceCreditEvent() throws SvcException {
    long amountCents = 2000;
    ObjectId orgId = oid(209);
    Credit.Builder builder =
        new Credit.Builder()
            .orgId(orgId)
            .created(new Date())
            .issuer(USERNAME)
            .amountCents(amountCents)
            .type(CreditType.GCP_BILLING_ACCOUNT)
            .activationCode("1234");
    Credit credit = builder.build();
    creditSvc.issueSalesSoldAtlasCredit(credit, auditInfo);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(BillingEvent.Type.GCP_BILLING_ACCOUNT_CREDIT_ISSUED, event.getEventType());
    assertEquals(amountCents, event.getCreditAmountCents());
    assertEquals(orgId, event.getOrgId());
  }

  @Test
  public void testIssueAwsMarketplaceCreditEvent() throws SvcException {
    long amountCents = 2000;
    Organization org = organizationFactory.createAtlasOrganization(new Date());
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Credit.Builder builder =
        new Credit.Builder()
            .orgId(org.getId())
            .created(new Date())
            .amountCents(amountCents)
            .type(CreditType.AWS_PREPAID)
            .activationCode("activationCode");
    Credit credit = builder.build();
    creditSvc.issueSalesSoldAtlasCredit(credit, auditInfo);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(BillingEvent.Type.AWS_BILLING_ACCOUNT_CREDIT_ISSUED, event.getEventType());
    assertEquals(amountCents, event.getCreditAmountCents());
    assertEquals(org.getId(), event.getOrgId());
  }

  @Test
  public void testIssueAzureMarketplaceCreditEvent() throws SvcException {
    Organization org = organizationFactory.createAtlasOrganization();
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Credit credit =
        new Credit.Builder()
            .orgId(org.getId())
            .created(new Date())
            .amountCents(2000)
            .type(CreditType.AZURE_PREPAID)
            .activationCode("activationCode")
            .build();
    creditSvc.issueSalesSoldAtlasCredit(credit, auditInfo);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(BillingEvent.Type.AZURE_BILLING_ACCOUNT_CREDIT_ISSUED, event.getEventType());
    assertEquals(credit.getAmountCents(), event.getCreditAmountCents());
    assertEquals(org.getId(), event.getOrgId());
  }

  @Test
  public void testSalesSoldHasSalesSoldCreditAvailable() {
    ObjectId orgId = ObjectId.get();
    DateTime now = new DateTime();

    Credit promoCredit =
        new Credit.Builder("We owe you money")
            .orgId(orgId)
            .created(now.minusDays(5).toDate())
            .startDate(now.minusDays(5).toDate())
            .endDate(now.plusDays(5).toDate())
            .reason("reason")
            .issuer("test.user")
            .amountCents(2000)
            .amountRemainingCents(2000)
            .type(CreditType.PROMO)
            .activationCode("1234")
            .build();
    creditDao.save(promoCredit);
    assertFalse(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));

    Credit prepaidCredit =
        new Credit.Builder("We owe you money")
            .orgId(orgId)
            .created(now.minusDays(5).toDate())
            .startDate(now.minusDays(5).toDate())
            .endDate(now.plusDays(5).toDate())
            .reason("reason")
            .issuer("test.user")
            .amountCents(2000)
            .amountRemainingCents(2000)
            .type(CreditType.PREPAID_NDS)
            .activationCode("1234")
            .build();
    creditDao.save(prepaidCredit);
    assertTrue(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));
    creditDao.delete(prepaidCredit.getId());

    Credit oldPrepaidCredit =
        new Credit.Builder("We owe you money")
            .orgId(orgId)
            .created(now.minusDays(20).toDate())
            .startDate(now.minusDays(20).toDate())
            .endDate(now.minusDays(15).toDate())
            .reason("reason")
            .issuer("test.user")
            .amountCents(2000)
            .amountRemainingCents(2000)
            .type(CreditType.PREPAID_NDS)
            .activationCode("1234")
            .build();
    creditDao.save(oldPrepaidCredit);
    assertFalse(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));
    creditDao.delete(oldPrepaidCredit.getId());

    Credit futurePrepaidCredit =
        new Credit.Builder("We owe you money")
            .orgId(orgId)
            .created(now.toDate())
            .startDate(now.plusDays(1).toDate())
            .endDate(now.plusDays(10).toDate())
            .reason("reason")
            .issuer("test.user")
            .amountCents(2000)
            .amountRemainingCents(2000)
            .type(CreditType.PREPAID_NDS)
            .activationCode("1234")
            .build();
    creditDao.save(futurePrepaidCredit);
    assertFalse(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));
    creditDao.delete(futurePrepaidCredit.getId());

    Credit monthlyCommitmentCredit =
        new Credit.Builder(prepaidCredit).type(CreditType.MONTHLY_COMMITMENT).build();
    creditDao.save(monthlyCommitmentCredit);
    assertTrue(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));
    creditDao.delete(monthlyCommitmentCredit.getId());

    Credit gcpMarketplaceCredit =
        new Credit.Builder(prepaidCredit).type(CreditType.GCP_BILLING_ACCOUNT).build();
    creditDao.save(gcpMarketplaceCredit);
    assertTrue(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));
    creditDao.delete(gcpMarketplaceCredit.getId());

    Credit rolloverCredit = new Credit.Builder(prepaidCredit).type(CreditType.ROLLOVER).build();
    creditDao.save(rolloverCredit);
    assertTrue(
        creditSvc.hasSalesSoldCreditAvailable(orgId, now.minusDays(10).toDate(), now.toDate()));
  }

  @Test
  public void testFindCreditsByOrgId_activeOnly() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date prevMonth = DateUtils.addMonths(today, -1);
    Date yesterday = DateUtils.addDays(today, -1);
    Date nextMonth = DateUtils.addMonths(today, 1);
    Date nextYear = DateUtils.addYears(today, 1);
    String activationCode = "activationCode";

    Organization org = organizationFactory.createAtlasOrganization(now);
    paymentMethodStubber.stubPaymentMethod(org.getId());

    SalesforceOpportunityLineItem pastElastic =
        SalesforceOpportunityLineItem.builder()
            .startDate(prevMonth)
            .endDate(yesterday)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem currentElastic =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(nextMonth)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem futureElastic =
        SalesforceOpportunityLineItem.builder()
            .startDate(nextMonth)
            .endDate(nextYear)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity pastSalesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(pastElastic))
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(nextMonth)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(currentElastic))
            .build();

    SalesforceOpportunity futureSalesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(futureElastic))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(pastSalesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(futureSalesforceOpportunity);

    Credit pastElasticCredit =
        new Credit.Builder()
            .id(new ObjectId())
            .note("past elastic credit")
            .startDate(pastElastic.getStartDate())
            .endDate(pastElastic.getEndDate())
            .elasticInvoicing(true)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(pastElastic)
            .amountCents((long) (1 * currentElastic.getQuantity()))
            .amountRemainingCents(0)
            .activationCode(activationCode)
            .build();

    Credit currentElasticCredit =
        new Credit.Builder()
            .id(new ObjectId())
            .note("current elastic credit")
            .startDate(currentElastic.getStartDate())
            .endDate(currentElastic.getEndDate())
            .elasticInvoicing(true)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(currentElastic)
            .amountCents((long) (1 * currentElastic.getQuantity()))
            .amountRemainingCents(0)
            .activationCode(activationCode)
            .build();

    Credit currentElasticPartnerCredit =
        new Credit.Builder()
            .id(new ObjectId())
            .note("GCP Credit")
            .startDate(currentElastic.getStartDate())
            .endDate(currentElastic.getEndDate())
            .elasticInvoicing(true)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(currentElastic)
            .amountCents((long) (1 * currentElastic.getQuantity()))
            .amountRemainingCents(-123456789)
            .activationCode(activationCode)
            .build();

    Credit currentNonElasticCredit =
        new Credit.Builder()
            .note("current non-elastic credit")
            .created(yesterday)
            .id(new ObjectId())
            .startDate(yesterday)
            .endDate(nextMonth)
            .orgId(org.getId())
            .amountCents((long) (1 * currentElastic.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit futureElasticCredit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(futureElastic.getStartDate())
            .endDate(futureElastic.getEndDate())
            .elasticInvoicing(true)
            .note("future elastic credit")
            .orgId(org.getId())
            .salesforceOpportunityLineItem(futureElastic)
            .amountRemainingCents(0)
            .amountCents((long) (1 * futureElastic.getQuantity()))
            .activationCode(activationCode)
            .build();

    creditDao.save(pastElasticCredit);
    creditDao.save(futureElasticCredit);
    creditDao.save(currentElasticCredit);
    creditDao.save(currentNonElasticCredit);
    creditDao.save(currentElasticPartnerCredit);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId(), true);
    assertThat(credits.get(0).getId(), equalTo(currentNonElasticCredit.getId()));
    assertThat(credits.get(1).getId(), equalTo(currentElasticCredit.getId()));
    assertThat(credits.get(2).getId(), equalTo(currentElasticPartnerCredit.getId()));
    assertThat(credits.get(3).getId(), equalTo(futureElasticCredit.getId()));
  }

  @Test
  public void testUpdateNewSalesforceOpportunityLineItem() {
    LocalDate earlier = LocalDate.of(2022, 1, 1);
    LocalDate later = LocalDate.of(2022, 1, 31);
    long creditAmount = 100L;

    ObjectId orgId = new ObjectId();
    String updateNote = "HELP-1010";

    Credit activeCredit =
        creditFactory.createPrepaidCredit(
            orgId, DateTimeUtils.dateOf(earlier), DateTimeUtils.dateOf(later), creditAmount, false);

    SalesforceOpportunityLineItem currSFOLI = activeCredit.getSalesforceOpportunityLineItem();

    SalesforceOpportunity sfOpp =
        salesforceOpportunityFactory.insertAtlasProOpportunity(
            DateTimeUtils.dateOf(earlier), DateTimeUtils.dateOf(later), 0, true);

    SalesforceOpportunityLineItem newSfoli = sfOpp.getLineItems().get(0);

    try {
      creditSvc.updateNewSalesforceOpportunityLineItem(
          currSFOLI.getId(), newSfoli.getId(), updateNote);
    } catch (SvcException ignored) {
    }

    Credit updatedCredit = creditSvc.findById(activeCredit.getId());
    assertEquals(updatedCredit.getSalesforceOpportunityLineItem(), newSfoli);
  }

  @Test
  public void testModifySalesforceOpportunityLineItemSafeFields() {
    // Unsafe Salesforce opportunity line item fields to update
    Date startDate = dateOf(LocalDate.of(2023, 8, 1));
    Date endDate = dateOf(LocalDate.of(2023, 8, 31));
    Double quantity = 100.0;
    String productCode = "CLD_ATL_CRE";
    Double termMonths = 12.0;
    boolean isEligibleForTieredPricing = false;

    Date updatedStartDate = dateOf(LocalDate.of(2023, 9, 1));
    Date updatedEndDate = dateOf(LocalDate.of(2023, 9, 30));
    Double updatedQuantity = 200.0;
    String updatedProductCode = "CLD_ATL_ENT";
    Double updatedTermMonths = 24.0;
    boolean updatedIsEligibleForTieredPricing = true;

    // Safe Salesforce opportunity line item field to update
    String sfoliId = "sfoliId";
    String currency = "USD";
    Double unitPrice = 0.88;
    Double discountPercent = 0.0;
    Boolean elasticInvoicing = null;

    String updatedSfoliId = "updatedSfoliId";
    String updatedCurrency = "CNY";
    Double updatedUnitPrice = 0.99;
    Double updatedDiscountPercent = 2.0;
    Boolean updatedElasticInvoicing = true;

    // GIVEN a credit with a Salesforce opportunity line item
    SalesforceOpportunityLineItem originalSfoli =
        SalesforceOpportunityLineItem.builder()
            .id(sfoliId)
            .startDate(startDate)
            .endDate(endDate)
            .quantity(quantity)
            .termMonths(termMonths)
            .productCode(productCode)
            .eligibleForTieredPricing(isEligibleForTieredPricing)
            .currency(currency)
            .unitPrice(unitPrice)
            .discountPercent(discountPercent)
            .elasticInvoicing(elasticInvoicing)
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder().lineItems(Collections.singletonList(originalSfoli)).build();
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);

    Organization org = organizationFactory.createAtlasOrganization(startDate);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(originalSfoli.getStartDate())
            .endDate(originalSfoli.getEndDate())
            .elasticInvoicing(true)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(originalSfoli)
            .amountCents((long) (1 * originalSfoli.getQuantity()))
            .amountRemainingCents(0)
            .activationCode("mockActivationCode")
            .build();
    creditDao.save(credit);

    // GIVEN a new a Salesforce opportunity line item to be updated
    SalesforceOpportunityLineItem newSfoli =
        SalesforceOpportunityLineItem.builder()
            .id(updatedSfoliId)
            .startDate(updatedStartDate)
            .endDate(updatedEndDate)
            .quantity(updatedQuantity)
            .termMonths(updatedTermMonths)
            .productCode(updatedProductCode)
            .eligibleForTieredPricing(updatedIsEligibleForTieredPricing)
            .currency(updatedCurrency)
            .unitPrice(updatedUnitPrice)
            .discountPercent(updatedDiscountPercent)
            .elasticInvoicing(updatedElasticInvoicing)
            .build();

    // WHEN modify the safe fields for Salesforce opportunity line item
    creditSvc.modifySalesforceOpportunityLineItemSafeFields(credit.getId(), newSfoli);

    // THEN only safe fields being updated
    Credit updatedCredit = creditSvc.findById(credit.getId());
    assertNotNull(updatedCredit.getSalesforceOpportunityLineItem());
    SalesforceOpportunityLineItem updatedSfoli = updatedCredit.getSalesforceOpportunityLineItem();
    assertEquals(updatedSfoli.getId(), updatedSfoliId);
    assertEquals(updatedSfoli.getCurrency(), updatedCurrency);
    assertEquals(updatedSfoli.getUnitPrice(), updatedUnitPrice);
    assertEquals(updatedSfoli.getDiscountPercent(), updatedDiscountPercent);

    // THEN unsafe fields will not be updated
    assertEquals(updatedSfoli.getStartDate(), startDate);
    assertEquals(updatedSfoli.getEndDate(), endDate);
    assertEquals(updatedSfoli.getQuantity(), quantity);
    assertEquals(updatedSfoli.getProductCode(), productCode);
    assertEquals(updatedSfoli.getTermMonths(), termMonths);
    assertEquals(updatedSfoli.isEligibleForTieredPricing(), isEligibleForTieredPricing);

    creditSvc.modifySalesforceOpportunityLineItemSafeFields(updatedCredit.getId(), newSfoli);
    updatedCredit = creditSvc.findById(updatedCredit.getId());
    assertNotNull(updatedCredit.getSalesforceOpportunityLineItem());

    updatedSfoli = updatedCredit.getSalesforceOpportunityLineItem();
    assertEquals(updatedSfoli.isElasticInvoicing(), updatedElasticInvoicing);
  }

  @Test
  public void testUpdateIncompatibleTypeGCP() {
    ObjectId orgId = new ObjectId();
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date nextMonth = DateUtils.addMonths(today, 1);

    Credit gcpCredit =
        creditFactory.createGcpMarketplaceZeroDollarElasticCredit(orgId, now, nextMonth);
    CreditType creditType = gcpCredit.getType();

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(),
          CreditFieldsUpdateRequest.builder().type(CreditType.GCP_MONTHLY_COMMITMENT).build(),
          auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit.getType(), creditType);
  }

  @Test
  public void testUpdateCompatibleTypeGCP() {
    ObjectId orgId = new ObjectId();
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date nextMonth = DateUtils.addMonths(today, 1);

    Credit gcpCredit =
        creditFactory.createGcpMarketplaceZeroDollarElasticCredit(orgId, now, nextMonth);
    CreditType creditType = gcpCredit.getType();

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(),
          CreditFieldsUpdateRequest.builder().type(CreditType.GCP_BILLING_ACCOUNT).build(),
          auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit.getType(), creditType);
  }

  @Test
  public void testUpdateGCPEntitlementIdIncompatibleType() {
    ObjectId orgId = new ObjectId();
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date nextMonth = DateUtils.addMonths(today, 1);

    String accountId = "gcpAccountttId";
    String entId = "entitelmentId";
    GCPMarketplaceEntitlement createdEnt =
        gcpMarketplaceEntitlementFactory.createMonthlyCommitmentEntitlement(
            accountId, true, entId, null);

    Credit gcpCredit =
        creditFactory.createGcpMarketplaceZeroDollarElasticCredit(orgId, now, nextMonth);

    String oldGcpMarketplaceEntitlementId = gcpCredit.getGcpMarketplaceEntitlementId();

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(),
          CreditFieldsUpdateRequest.builder()
              .gcpMarketplaceEntitlementId(createdEnt.getEntitlementId())
              .build(),
          auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit.getGcpMarketplaceEntitlementId(), oldGcpMarketplaceEntitlementId);

    createdEnt =
        gcpMarketplaceEntitlementFactory.createMonthlyCommitmentEntitlement(
            accountId, false, entId, null);

    gcpCredit = creditFactory.createGcpMarketplaceMonthlyCommitment(orgId, now, nextMonth, 0, true);

    oldGcpMarketplaceEntitlementId = gcpCredit.getGcpMarketplaceEntitlementId();

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(),
          CreditFieldsUpdateRequest.builder()
              .gcpMarketplaceEntitlementId(createdEnt.getEntitlementId())
              .build(),
          auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit.getGcpMarketplaceEntitlementId(), oldGcpMarketplaceEntitlementId);
  }

  @Test
  public void testUpdateGCPEntitlementIdCompatibleType() {
    ObjectId orgId = new ObjectId();
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date nextMonth = DateUtils.addMonths(today, 1);

    String accountId = "gcpAccountttId";
    String entId = "entitelmentId";
    GCPMarketplaceEntitlement createdEnt =
        gcpMarketplaceEntitlementFactory.createMonthlyCommitmentEntitlement(
            accountId, false, entId, null);
    String newGcpMarketplaceEntitlementId = createdEnt.getEntitlementId();

    Credit gcpCredit =
        creditFactory.createGcpMarketplaceZeroDollarElasticCredit(orgId, now, nextMonth);

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(),
          CreditFieldsUpdateRequest.builder()
              .gcpMarketplaceEntitlementId(newGcpMarketplaceEntitlementId)
              .build(),
          auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit.getGcpMarketplaceEntitlementId(), newGcpMarketplaceEntitlementId);
    assertEquals(gcpCredit.getStartDate(), updatedCredit.getStartDate());

    gcpMarketplaceEntitlementFactory.createMonthlyCommitmentEntitlement(
        accountId, true, entId, null);
    newGcpMarketplaceEntitlementId = createdEnt.getEntitlementId();

    gcpCredit = creditFactory.createGcpMarketplaceMonthlyCommitment(orgId, now, nextMonth, 0, true);

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(),
          CreditFieldsUpdateRequest.builder()
              .gcpMarketplaceEntitlementId(newGcpMarketplaceEntitlementId)
              .build(),
          auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit.getGcpMarketplaceEntitlementId(), newGcpMarketplaceEntitlementId);
    assertEquals(gcpCredit.getStartDate(), updatedCredit.getStartDate());
  }

  @Test
  public void testModifyCreditFields() throws SvcException {
    ObjectId orgId = new ObjectId();
    LocalDate earlier = LocalDate.of(2022, 1, 1);
    LocalDate later = LocalDate.of(2022, 1, 31);
    long creditAmount = 100L;
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date nextMonth = DateUtils.addMonths(today, 1);

    UUID currUUID = UUID.randomUUID();
    long amtCents = 99L;
    long amtRemainingCents = 98L;
    long totalBilledCents = 1L;

    Credit activeCredit =
        creditFactory.createPrepaidCredit(
            orgId, DateTimeUtils.dateOf(earlier), DateTimeUtils.dateOf(later), creditAmount, false);
    creditSvc.modifyCreditFields(
        activeCredit.getId(),
        CreditFieldsUpdateRequest.builder()
            .startDate(today)
            .endDate(nextMonth)
            .isElasticInvoicing(true)
            .type(CreditType.PREPAID_NDS)
            .amountCents(amtCents)
            .amountRemainingCents(amtRemainingCents)
            .totalBilledCents(totalBilledCents)
            .awsCustomerId("customerID")
            .awsProductCode("productCodeExample")
            .gcpMarketplaceEntitlementId(currUUID.toString())
            .azureSubscriptionId(currUUID)
            .evergreen(true)
            .updateNote("HELP-1010")
            .build(),
        auditInfo);
    Credit updatedCredit = creditSvc.findById(activeCredit.getId());

    assertEquals(updatedCredit.getStartDate(), today);
    assertEquals(updatedCredit.getEndDate(), nextMonth);
    assertTrue(updatedCredit.isElasticInvoicing());
    assertEquals(updatedCredit.getType(), CreditType.PREPAID_NDS);
    assertEquals(updatedCredit.getAmountCents(), amtCents);
    assertEquals(updatedCredit.getAmountRemainingCents(), amtRemainingCents);
    assertEquals(updatedCredit.getTotalBilledCents(), totalBilledCents);
    assertEquals(updatedCredit.getAwsMarketplaceCustomerId(), "customerID");
    assertEquals(updatedCredit.getAwsMarketplaceProductCode(), "productCodeExample");
    assertEquals(updatedCredit.getAzureExternalSubscriptionId(), currUUID);
    assertEquals(updatedCredit.getGcpMarketplaceEntitlementId(), currUUID.toString());
    assertTrue(updatedCredit.isEvergreen());

    // verify that all corresponding events have been emitted
    List<BillingEvent.Type> billingEventTypes =
        List.of(
            BillingEvent.Type.CREDIT_START_DATE_MODIFIED,
            BillingEvent.Type.CREDIT_END_DATE_MODIFIED,
            BillingEvent.Type.CREDIT_ELASTIC_INVOICING_MODIFIED,
            BillingEvent.Type.CREDIT_TYPE_MODIFIED,
            BillingEvent.Type.CREDIT_AMOUNT_CENTS_MODIFIED,
            BillingEvent.Type.CREDIT_AMOUNT_REMAINING_CENTS_MODIFIED,
            BillingEvent.Type.CREDIT_TOTAL_BILLED_CENTS_MODIFIED,
            BillingEvent.Type.CREDIT_AWS_CUSTOMER_ID_MODIFIED,
            BillingEvent.Type.CREDIT_AWS_PRODUCT_CODE_MODIFIED,
            BillingEvent.Type.CREDIT_AZURE_SUBSCRIPTION_ID_MODIFIED,
            BillingEvent.Type.CREDIT_GCP_MARKETPLACE_ENTITLEMENT_ID_MODIFIED,
            BillingEvent.Type.EVERGREEN_PRIORITY_MODIFIED);
    List<Event> eventsCreated =
        eventSvc.findAllForOrganization(
            orgId,
            0,
            billingEventTypes.stream().map(Enum::name).toList(),
            null,
            null,
            Collections.emptyList(),
            false,
            false,
            null,
            null);
    List<EventType> eventTypesCreated = eventsCreated.stream().map(Event::getEventType).toList();
    assertThat(billingEventTypes, containsInAnyOrder(eventTypesCreated.toArray()));
  }

  @Test
  public void testModifyCreditAllNulls() {
    ObjectId orgId = new ObjectId();
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date nextMonth = DateUtils.addMonths(today, 1);

    Credit gcpCredit =
        creditFactory.createGcpMarketplaceZeroDollarElasticCredit(orgId, now, nextMonth);

    try {
      creditSvc.modifyCreditFields(
          gcpCredit.getId(), CreditFieldsUpdateRequest.builder().build(), auditInfo);
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.INCOMPATIBLE_PLAN_BILLING_MODEL, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(gcpCredit.getId());
    assertEquals(updatedCredit, gcpCredit);
  }

  @Test
  public void testHasMonthlyCommitmentCredit() {
    boolean hasMonthlyCommitmentCredit = creditSvc.hasDirectMonthlyCommitmentCredit(oid(2001));
    assertThat(hasMonthlyCommitmentCredit, equalTo(true));
    hasMonthlyCommitmentCredit = creditSvc.hasDirectMonthlyCommitmentCredit(oid(2002));
    assertThat(hasMonthlyCommitmentCredit, equalTo(true));
    hasMonthlyCommitmentCredit = creditSvc.hasDirectMonthlyCommitmentCredit(oid(17));
    assertThat(hasMonthlyCommitmentCredit, equalTo(false));
  }

  @Test
  public void testMoveCreditUsageToNewCredit() {
    ObjectId oldCreditId = new ObjectId();
    ObjectId newCreditId = new ObjectId();
    Credit oldCredit =
        new Credit.Builder().amountCents(1000).amountRemainingCents(600).id(oldCreditId).build();

    Credit newCredit =
        new Credit.Builder().amountCents(1200).amountRemainingCents(1200).id(newCreditId).build();

    creditDao.save(oldCredit);
    creditDao.save(newCredit);

    creditSvc.moveCreditUsageToNewCredit(oldCredit, newCredit, 350);

    Credit oldCreditUpdated = creditDao.findById(oldCreditId);
    Credit newCreditUpdated = creditDao.findById(newCreditId);

    assertEquals(950, oldCreditUpdated.getAmountRemainingCents());
    assertEquals(850, newCreditUpdated.getAmountRemainingCents());
    assertEquals(oldCreditUpdated.getAmountCents(), oldCredit.getAmountCents());
    assertEquals(newCreditUpdated.getAmountCents(), newCredit.getAmountCents());
  }

  @Test
  public void testUpdateCreditEndDate() {
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();
    ObjectId creditId = new ObjectId();
    Date startDate = TimeUtils.fromISOString("2021-01-01");
    Date endDate = TimeUtils.fromISOString("2022-01-01");
    Date inclusiveEndDate = TimeUtils.fromISOString("2022-01-01");
    SalesforceOpportunityLineItem salesforceOpportunityLineItem =
        SalesforceOpportunityLineItem.builder()
            .startDate(startDate)
            .endDate(inclusiveEndDate)
            .build();
    Credit credit =
        new Credit.Builder()
            .amountCents(1000)
            .amountRemainingCents(600)
            .id(creditId)
            .startDate(startDate)
            .endDate(endDate)
            .salesforceOpportunityLineItem(salesforceOpportunityLineItem)
            .build();
    assertEquals(startDate, credit.getStartDate());
    assertEquals(endDate, credit.getEndDate());
    assertEquals(startDate, credit.getSalesforceOpportunityLineItem().getStartDate());
    assertEquals(inclusiveEndDate, credit.getSalesforceOpportunityLineItem().getEndDate());
    creditDao.save(credit);

    Date newEndDate = TimeUtils.fromISOString("2022-01-01");
    creditSvc.modifyCreditEndDate(creditId, newEndDate, auditInfo);

    Credit updatedCredit = creditDao.findById(creditId);
    assertEquals(startDate, updatedCredit.getStartDate());
    assertEquals(newEndDate, updatedCredit.getEndDate());
    assertEquals(startDate, updatedCredit.getSalesforceOpportunityLineItem().getStartDate());
    assertEquals(endDate, updatedCredit.getSalesforceOpportunityLineItem().getEndDate());
  }

  @Test
  public void testFindActiveElasticCredit() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date tomorrow = DateUtils.addDays(today, 1);
    String activationCode = "activationCode";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    creditDao.save(credit);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(credit.getId(), foundCredit.getId());
  }

  @Test
  public void testFindActiveElasticCredit_multipleOpps_chosenByClosedDate() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date tomorrow = DateUtils.addDays(today, 1);
    String activationCode = "activationCode";
    String activationCode2 = "activationCode2";
    String activationCode3 = "activationCode3";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity2 =
        SalesforceOpportunity.builder()
            .closeDate(today)
            .licenseKey(activationCode2)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity3 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode3)
            .lineItems(Collections.singletonList(li))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity3);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity2);

    Credit credit3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit credit2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode2)
            .build();

    creditDao.save(credit);
    creditDao.save(credit3);
    creditDao.save(credit2);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(credit3.getId(), foundCredit.getId());
  }

  @Test
  public void
      testFindActiveElasticCredit_multipleOpps_MultipleLineItems_chosenByClosedDateAndEarliestEndDate() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date tomorrow = DateUtils.addDays(today, 1);
    Date dayAfterTomorrow = DateUtils.addDays(today, 2);
    Date threeDaysFromNow = DateUtils.addDays(today, 3);
    String activationCode = "activationCode";
    String activationCode2 = "activationCode2";
    String activationCode3 = "activationCode3";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(threeDaysFromNow)
            .quantity(1D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li2 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(dayAfterTomorrow)
            .quantity(3D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li3 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(2D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity2 =
        SalesforceOpportunity.builder()
            .closeDate(today)
            .licenseKey(activationCode2)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity3 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode3)
            .lineItems(Arrays.asList(li, li3, li2))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity3);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity2);

    Credit creditOpp3Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit credit2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode2)
            .build();

    creditDao.save(credit);
    creditDao.save(creditOpp3Li1);
    creditDao.save(creditOpp3Li3);
    creditDao.save(creditOpp3Li2);
    creditDao.save(credit2);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(creditOpp3Li3.getId(), foundCredit.getId());
  }

  @Test
  public void
      testFindActiveElasticCredit_multipleOpps_MultipleLineItems_chosenByClosedDateAndEarliestEndDateAndEarliestStartDate() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date dayBeforeYesterday = DateUtils.addDays(today, -2);
    Date threeDaysAgo = DateUtils.addDays(today, -3);
    Date tomorrow = DateUtils.addDays(today, 1);
    Date dayAfterTomorrow = DateUtils.addDays(today, 2);
    Date threeDaysFromNow = DateUtils.addDays(today, 3);
    String activationCode = "activationCode";
    String activationCode2 = "activationCode2";
    String activationCode3 = "activationCode3";
    String activationCode4 = "activationCode4";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(threeDaysFromNow)
            .quantity(12D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li2 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(dayAfterTomorrow)
            .quantity(19D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li3 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(13D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li4 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(12D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li5 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(13D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li6 =
        SalesforceOpportunityLineItem.builder()
            .startDate(today)
            .endDate(tomorrow)
            .quantity(15D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li7 =
        SalesforceOpportunityLineItem.builder()
            .startDate(threeDaysAgo)
            .endDate(tomorrow)
            .quantity(14D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity2 =
        SalesforceOpportunity.builder()
            .closeDate(today)
            .licenseKey(activationCode2)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity3 =
        SalesforceOpportunity.builder()
            .closeDate(dayAfterTomorrow)
            .licenseKey(activationCode3)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6))
            .build();

    SalesforceOpportunity salesforceOpportunity4 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode4)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6, li7))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity3);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity4);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity2);

    Credit creditOpp4Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li7 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li7)
            .amountCents((long) (1 * li7.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp3Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit credit2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode2)
            .build();

    creditDao.save(credit);
    creditDao.save(creditOpp3Li1);
    creditDao.save(creditOpp3Li3);
    creditDao.save(creditOpp3Li2);
    creditDao.save(creditOpp3Li4);
    creditDao.save(creditOpp3Li5);
    creditDao.save(creditOpp3Li6);
    creditDao.save(creditOpp4Li1);
    creditDao.save(creditOpp4Li3);
    creditDao.save(creditOpp4Li2);
    creditDao.save(creditOpp4Li4);
    creditDao.save(creditOpp4Li5);
    creditDao.save(creditOpp4Li6);
    creditDao.save(creditOpp4Li7);
    creditDao.save(credit2);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(creditOpp3Li5.getId(), foundCredit.getId());
  }

  @Test
  public void
      testFindActiveElasticCredit_multipleOppsSameCloseDate_MultipleLineItems_chosenByClosedDateAndEarliestEndDateAndEarliestStartDate() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date dayBeforeYesterday = DateUtils.addDays(today, -2);
    Date threeDaysAgo = DateUtils.addDays(today, -3);
    Date tomorrow = DateUtils.addDays(today, 1);
    Date dayAfterTomorrow = DateUtils.addDays(today, 2);
    Date threeDaysFromNow = DateUtils.addDays(today, 3);
    String activationCode = "activationCode";
    String activationCode2 = "activationCode2";
    String activationCode3 = "activationCode3";
    String activationCode4 = "activationCode4";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(threeDaysFromNow)
            .quantity(12D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li2 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(dayAfterTomorrow)
            .quantity(30D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li3 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(44D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li4 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li5 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(55D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li6 =
        SalesforceOpportunityLineItem.builder()
            .startDate(today)
            .endDate(tomorrow)
            .quantity(9D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li7 =
        SalesforceOpportunityLineItem.builder()
            .startDate(threeDaysAgo)
            .endDate(tomorrow)
            .quantity(12D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity2 =
        SalesforceOpportunity.builder()
            .closeDate(today)
            .licenseKey(activationCode2)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity3 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode3)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6))
            .build();

    SalesforceOpportunity salesforceOpportunity4 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode4)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6, li7))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity3);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity4);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity2);

    Credit creditOpp4Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li7 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li7.getStartDate())
            .endDate(li7.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li7)
            .amountCents((long) (1 * li7.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp3Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit credit2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode2)
            .build();

    creditDao.save(credit);
    creditDao.save(creditOpp3Li1);
    creditDao.save(creditOpp3Li3);
    creditDao.save(creditOpp3Li2);
    creditDao.save(creditOpp3Li4);
    creditDao.save(creditOpp3Li5);
    creditDao.save(creditOpp3Li6);
    creditDao.save(creditOpp4Li1);
    creditDao.save(creditOpp4Li3);
    creditDao.save(creditOpp4Li7);
    creditDao.save(creditOpp4Li2);
    creditDao.save(creditOpp4Li4);
    creditDao.save(creditOpp4Li5);
    creditDao.save(creditOpp4Li6);
    creditDao.save(credit2);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(creditOpp4Li7.getId(), foundCredit.getId());
  }

  @Test
  public void
      testFindActiveElasticCredit_multipleOpps_MultipleLineItems_chosenByClosedDateAndEarliestEndDateAndEarliestStartDateAndGreatestQuantity() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date dayBeforeYesterday = DateUtils.addDays(today, -2);
    Date threeDaysAgo = DateUtils.addDays(today, -3);
    Date tomorrow = DateUtils.addDays(today, 1);
    Date dayAfterTomorrow = DateUtils.addDays(today, 2);
    Date threeDaysFromNow = DateUtils.addDays(today, 3);
    String activationCode = "activationCode";
    String activationCode2 = "activationCode2";
    String activationCode3 = "activationCode3";
    String activationCode4 = "activationCode4";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(threeDaysFromNow)
            .quantity(12D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li2 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(dayAfterTomorrow)
            .quantity(9D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li3 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(44D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li4 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(105D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li5 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(99D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li6 =
        SalesforceOpportunityLineItem.builder()
            .startDate(today)
            .endDate(tomorrow)
            .quantity(43D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li7 =
        SalesforceOpportunityLineItem.builder()
            .startDate(threeDaysAgo)
            .endDate(tomorrow)
            .quantity(105D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li8 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(106D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity2 =
        SalesforceOpportunity.builder()
            .closeDate(today)
            .licenseKey(activationCode2)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity3 =
        SalesforceOpportunity.builder()
            .closeDate(dayAfterTomorrow)
            .licenseKey(activationCode3)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6, li8))
            .build();

    SalesforceOpportunity salesforceOpportunity4 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode4)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6, li7))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity3);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity4);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity2);

    Credit creditOpp4Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li7 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li7)
            .amountCents((long) (1 * li7.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp3Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li8 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li8.getStartDate())
            .endDate(li8.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li8)
            .amountCents((long) (1 * li8.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit credit2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode2)
            .build();

    creditDao.save(credit);
    creditDao.save(creditOpp3Li1);
    creditDao.save(creditOpp3Li3);
    creditDao.save(creditOpp3Li2);
    creditDao.save(creditOpp3Li4);
    creditDao.save(creditOpp3Li5);
    creditDao.save(creditOpp3Li8);
    creditDao.save(creditOpp3Li6);
    creditDao.save(creditOpp4Li1);
    creditDao.save(creditOpp4Li3);
    creditDao.save(creditOpp4Li2);
    creditDao.save(creditOpp4Li4);
    creditDao.save(creditOpp4Li5);
    creditDao.save(creditOpp4Li6);
    creditDao.save(creditOpp4Li7);
    creditDao.save(credit2);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(creditOpp3Li8.getId(), foundCredit.getId());
  }

  @Test
  public void
      testFindActiveElasticCredit_multipleOpps_MultipleLineItems_chosenByClosedDateAndEarliestEndDateAndEarliestStartDateAndGreatestQuantityAndDiscountPercent() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date dayBeforeYesterday = DateUtils.addDays(today, -2);
    Date threeDaysAgo = DateUtils.addDays(today, -3);
    Date tomorrow = DateUtils.addDays(today, 1);
    Date dayAfterTomorrow = DateUtils.addDays(today, 2);
    Date threeDaysFromNow = DateUtils.addDays(today, 3);
    String activationCode = "activationCode";
    String activationCode2 = "activationCode2";
    String activationCode3 = "activationCode3";
    String activationCode4 = "activationCode4";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(threeDaysFromNow)
            .quantity(12D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li2 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(dayAfterTomorrow)
            .quantity(9D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li3 =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(44D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li4 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(105D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li5 =
        SalesforceOpportunityLineItem.builder()
            .startDate(dayBeforeYesterday)
            .endDate(tomorrow)
            .quantity(99D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li6 =
        SalesforceOpportunityLineItem.builder()
            .startDate(today)
            .endDate(tomorrow)
            .quantity(43D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunityLineItem li7 =
        SalesforceOpportunityLineItem.builder()
            .startDate(threeDaysAgo)
            .endDate(tomorrow)
            .quantity(105D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity2 =
        SalesforceOpportunity.builder()
            .closeDate(today)
            .licenseKey(activationCode2)
            .lineItems(Collections.singletonList(li))
            .build();

    SalesforceOpportunity salesforceOpportunity3 =
        SalesforceOpportunity.builder()
            .closeDate(dayAfterTomorrow)
            .licenseKey(activationCode3)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6))
            .build();

    SalesforceOpportunity salesforceOpportunity4 =
        SalesforceOpportunity.builder()
            .closeDate(tomorrow)
            .licenseKey(activationCode4)
            .lineItems(Arrays.asList(li, li3, li2, li4, li5, li6, li7))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity3);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity4);
    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity2);

    Credit creditOpp4Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp4Li7 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li7)
            .amountCents((long) (1 * li7.getQuantity()))
            .activationCode(activationCode4)
            .build();

    Credit creditOpp3Li1 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li3 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li3.getStartDate())
            .endDate(li3.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li3)
            .amountCents((long) (1 * li3.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li2.getStartDate())
            .endDate(li2.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li2)
            .amountCents((long) (1 * li2.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li4 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li4.getStartDate())
            .endDate(li4.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li4)
            .amountCents((long) (1 * li4.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li5 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li5.getStartDate())
            .endDate(li5.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li5)
            .amountCents((long) (1 * li5.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit creditOpp3Li6 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li6.getStartDate())
            .endDate(li6.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li6)
            .amountCents((long) (1 * li6.getQuantity()))
            .activationCode(activationCode3)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    Credit credit2 =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode2)
            .build();

    creditDao.save(credit);
    creditDao.save(creditOpp3Li1);
    creditDao.save(creditOpp3Li3);
    creditDao.save(creditOpp3Li2);
    creditDao.save(creditOpp3Li4);
    creditDao.save(creditOpp3Li5);
    creditDao.save(creditOpp3Li6);
    creditDao.save(creditOpp4Li1);
    creditDao.save(creditOpp4Li3);
    creditDao.save(creditOpp4Li2);
    creditDao.save(creditOpp4Li4);
    creditDao.save(creditOpp4Li5);
    creditDao.save(creditOpp4Li6);
    creditDao.save(creditOpp4Li7);
    creditDao.save(credit2);

    Credit foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now).get();

    assertEquals(creditOpp3Li4.getId(), foundCredit.getId());
  }

  @Test
  public void testFindActiveElasticCredit_missingOpp_exceptionThrown() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date tomorrow = DateUtils.addDays(today, 1);
    String activationCode = "activationCode";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(100D)
            .discountPercent(0D)
            .productCode("CLD_ATL_CRE")
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .elasticInvoicing(true)
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    creditDao.save(credit);

    try {
      creditSvc.findActiveElasticCredit(orgId, now, now).get();
      fail("illegal state exception should have been thrown due to missing opp");
    } catch (Exception e) {
      assertTrue(e instanceof IllegalStateException);
    }
  }

  @Test
  public void testFindActiveElasticCredit_noElasticCredit_noneFound() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date tomorrow = DateUtils.addDays(today, 1);
    String activationCode = "activationCode";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(100D)
            .discountPercent(0D)
            .build();

    SalesforceOpportunity salesforceOpportunity =
        SalesforceOpportunity.builder()
            .closeDate(yesterday)
            .licenseKey(activationCode)
            .lineItems(Collections.singletonList(li))
            .build();

    salesforceOpportunityDao.insertReplicaSafe(salesforceOpportunity);

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    creditDao.save(credit);

    Optional<Credit> foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now);

    assertTrue(foundCredit.isEmpty());
  }

  @Test
  public void testFindActiveElasticCredit_noElasticCreditAndNoOpp_noneFound() {
    Date now = new Date();
    Date today = DateUtils.truncate(now, Calendar.DATE);
    Date yesterday = DateUtils.addDays(today, -1);
    Date tomorrow = DateUtils.addDays(today, 1);
    String activationCode = "activationCode";
    ObjectId orgId = new ObjectId();

    SalesforceOpportunityLineItem li =
        SalesforceOpportunityLineItem.builder()
            .startDate(yesterday)
            .endDate(tomorrow)
            .quantity(100D)
            .discountPercent(0D)
            .build();

    Credit credit =
        new Credit.Builder()
            .id(new ObjectId())
            .startDate(li.getStartDate())
            .endDate(li.getEndDate())
            .orgId(orgId)
            .salesforceOpportunityLineItem(li)
            .amountCents((long) (1 * li.getQuantity()))
            .activationCode(activationCode)
            .build();

    creditDao.save(credit);

    Optional<Credit> foundCredit = creditSvc.findActiveElasticCredit(orgId, now, now);

    assertTrue(foundCredit.isEmpty());
  }

  @Test
  public void testUpdateAmountSuccess_positiveInc() throws SvcException {
    ObjectId creditId = new ObjectId();
    long amount = 1000L;
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(new Date())
            .endDate(new Date())
            .created(new Date())
            .orgId(new ObjectId())
            .amountCents(amount)
            .amountRemainingCents(amount)
            .build();
    creditDao.save(credit);
    int numUpdated = creditSvc.modifyCreditAmount(creditId, 100, AuditInfoHelpers.fromInternal());
    assertEquals(1, numUpdated);
    Credit updatedCredit = creditDao.findById(creditId);
    assertEquals(1100L, updatedCredit.getAmountRemainingCents());
    assertEquals(1100L, updatedCredit.getAmountCents());
  }

  @Test
  public void testUpdateAmountSuccess_negativeInc() throws SvcException {
    ObjectId creditId = new ObjectId();
    long amount = 1000L;
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(new Date())
            .endDate(new Date())
            .created(new Date())
            .orgId(new ObjectId())
            .amountCents(amount)
            .amountRemainingCents(amount)
            .build();
    creditDao.save(credit);
    int numUpdated = creditSvc.modifyCreditAmount(creditId, -100, AuditInfoHelpers.fromInternal());
    assertEquals(1, numUpdated);
    Credit updatedCredit = creditDao.findById(creditId);
    assertEquals(900L, updatedCredit.getAmountRemainingCents());
    assertEquals(900L, updatedCredit.getAmountCents());
  }

  @Test
  public void testDirectFlexCreditsUnbilledValidation() {
    ObjectId PAYMENT_VIEW_ORG_ID = new ObjectId();
    Date lastWeek = new DateTime().minusWeeks(1).toDate();
    List<Credit> flexCreditsWithRollover =
        flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
            FlexCommitCreditRequest.builder()
                .organizationId(PAYMENT_VIEW_ORG_ID)
                .flexStartDate(lastWeek)
                .amountPerFlex(********)
                .amountPerRollover(4000000)
                .build());
    ObjectId orgId = flexCreditsWithRollover.get(0).getOrgId();

    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));

    // Bill all credits
    flexCreditsWithRollover.forEach(
        credit -> creditDao.updateCreditTotalBilledCents(credit.getId(), credit.getAmountCents()));

    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
  }

  @Test
  public void testGCPFlexCreditsUnbilledValidation() {
    ObjectId PAYMENT_VIEW_ORG_ID = new ObjectId();
    Date lastWeek = new DateTime().minusWeeks(1).toDate();
    List<Credit> flexCreditsWithRollover =
        flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
            FlexCommitCreditRequest.builder()
                .organizationId(PAYMENT_VIEW_ORG_ID)
                .creditType(CreditType.GCP_FLEX_COMMIT)
                .flexStartDate(lastWeek)
                .amountPerFlex(********)
                .amountPerRollover(4000000)
                .build());
    ObjectId orgId = flexCreditsWithRollover.get(0).getOrgId();

    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));

    // Bill all credits
    flexCreditsWithRollover.forEach(
        credit -> creditDao.updateCreditTotalBilledCents(credit.getId(), credit.getAmountCents()));

    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
  }

  @Test
  public void testAWSFlexCreditsUnbilledValidation() {
    ObjectId PAYMENT_VIEW_ORG_ID = new ObjectId();
    Date lastWeek = new DateTime().minusWeeks(1).toDate();
    List<Credit> flexCreditsWithRollover =
        flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
            FlexCommitCreditRequest.builder()
                .organizationId(PAYMENT_VIEW_ORG_ID)
                .creditType(CreditType.AWS_FLEX_COMMIT)
                .flexStartDate(lastWeek)
                .amountPerFlex(********)
                .amountPerRollover(4000000)
                .build());
    ObjectId orgId = flexCreditsWithRollover.get(0).getOrgId();

    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));

    // Bill all credits
    flexCreditsWithRollover.forEach(
        credit -> creditDao.updateCreditTotalBilledCents(credit.getId(), credit.getAmountCents()));

    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
  }

  @Test
  public void testAzureFlexCreditsUnbilledValidation() {
    ObjectId PAYMENT_VIEW_ORG_ID = new ObjectId();
    Date lastWeek = new DateTime().minusWeeks(1).toDate();
    List<Credit> flexCreditsWithRollover =
        flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
            FlexCommitCreditRequest.builder()
                .organizationId(PAYMENT_VIEW_ORG_ID)
                .creditType(CreditType.AZURE_FLEX_COMMIT)
                .flexStartDate(lastWeek)
                .amountPerFlex(********)
                .amountPerRollover(4000000)
                .build());
    ObjectId orgId = flexCreditsWithRollover.get(0).getOrgId();

    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertTrue(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));

    // Bill all credits
    flexCreditsWithRollover.forEach(
        credit -> creditDao.updateCreditTotalBilledCents(credit.getId(), credit.getAmountCents()));

    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
    assertFalse(creditSvc.hasFlexCreditsWithUnbilledCommitment(orgId, new Date()));
  }

  @Test
  public void test_findActiveCreditsDuringInvoice() {

    Date startDate = DateTimeUtils.dateOf(LocalDate.of(2023, 7, 1));
    Date endDate = DateTimeUtils.dateOf(LocalDate.of(2023, 8, 1));

    ObjectId payingOrgId = new ObjectId();

    ObjectId linkedOrgId = new ObjectId();
    Credit credit1 =
        new Credit.Builder()
            .id(new ObjectId())
            .orgId(payingOrgId)
            .startDate(startDate)
            .endDate(endDate)
            .type(CreditType.MONTHLY_COMMITMENT)
            .build();

    saveAll(List.of(credit1));

    payingOrgDao.upsertPayingOrg(payingOrgId, List.of(linkedOrgId), Instant.now());
    Invoice invoice =
        new Invoice.Builder().startDate(startDate).endDate(endDate).orgId(linkedOrgId).build();

    List<Credit> credits =
        creditSvc.findActiveCreditsDuringPendingInvoice(
            invoice.getOrgId(),
            invoice.getStartDate(),
            invoice.getEndDate(),
            Set.of(CreditType.MONTHLY_COMMITMENT));

    assertThat(credits.size(), equalTo(1));
    assertThat(credits.get(0).getId(), equalTo(credit1.getId()));
  }

  @Test
  public void testUpdateAmountFail_invalidInc() {
    ObjectId creditId = new ObjectId();
    long amount = 1000L;
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(new Date())
            .endDate(new Date())
            .created(new Date())
            .orgId(new ObjectId())
            .amountCents(amount)
            .amountRemainingCents(amount)
            .build();
    creditDao.save(credit);
    try {
      creditSvc.modifyCreditAmount(creditId, -2000, AuditInfoHelpers.fromInternal());
    } catch (SvcException ignored) {
    }
    Credit updatedCredit = creditDao.findById(creditId);
    assertEquals(1000L, updatedCredit.getAmountRemainingCents());
    assertEquals(1000L, updatedCredit.getAmountCents());
  }

  @Test
  public void testPullForwardCredit() throws SvcException {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date threeMonthsFromNow = DateUtils.addMonths(startOfMonth, 3);
    Date expectedStartDate = DateUtils.addMonths(threeMonthsFromNow, -1);
    ObjectId creditId = new ObjectId();
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(threeMonthsFromNow)
            .type(CreditType.PREPAID_NDS)
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(threeMonthsFromNow).build())
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .startDate(startOfMonth)
            .endDate(startOfNextMonth)
            .status(Invoice.Status.PENDING)
            .build();
    invoiceDao.save(invoice);

    creditDao.save(credit);
    creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    Credit updatedCredit = creditSvc.findById(creditId);

    assertEquals(updatedCredit.getStartDate(), expectedStartDate);
  }

  @Test
  public void testPullForwardCredit_prepaidNonElasticMarketplace() throws SvcException {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date threeMonthsFromNow = DateUtils.addMonths(startOfMonth, 3);
    Date expectedStartDate = DateUtils.addMonths(threeMonthsFromNow, -1);
    ObjectId creditId = new ObjectId();
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(threeMonthsFromNow)
            .type(CreditType.AWS_PREPAID)
            .elasticInvoicing(false)
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(threeMonthsFromNow).build())
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .startDate(startOfMonth)
            .endDate(startOfNextMonth)
            .status(Invoice.Status.PENDING)
            .build();
    invoiceDao.save(invoice);

    creditDao.save(credit);
    creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    Credit updatedCredit = creditSvc.findById(creditId);

    assertEquals(updatedCredit.getStartDate(), expectedStartDate);
  }

  @Test
  public void testPullForwardMarktplaceElasticCredit_DoesNotPullForward() {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date threeMonthsFromNow = DateUtils.addMonths(startOfMonth, 3);
    ObjectId creditId = new ObjectId();
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(threeMonthsFromNow)
            .type(CreditType.AWS_PREPAID)
            .elasticInvoicing(true)
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(threeMonthsFromNow).build())
            .build();
    creditDao.save(credit);
    try {
      creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.CANNOT_PULL_FORWARD_CREDIT, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(creditId);
    assertEquals(updatedCredit.getStartDate(), threeMonthsFromNow);
  }

  @Test
  public void testPullForwardCredit_SameMonth_DoesNotPullForward() {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date threeMonthsFromNow = DateUtils.addMonths(startOfMonth, 3);
    ObjectId creditId = new ObjectId();
    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(startOfMonth)
            .type(CreditType.PREPAID_NDS)
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(startOfMonth).build())
            .build();
    creditDao.save(credit);
    try {
      creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    } catch (SvcException ex) {
      assertEquals(BillingErrorCode.CANNOT_PULL_FORWARD_CREDIT, ex.getErrorCode());
    }

    Credit updatedCredit = creditSvc.findById(creditId);
    assertEquals(updatedCredit.getStartDate(), startOfMonth);
  }

  @Test
  public void testPullForwardCredit_RebillsWithSuccessfulPayment() throws Exception {
    // start date is next month and it pulls forward to this month so rebills

    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date nextYear = DateUtils.addYears(startOfNextMonth, 1);
    ObjectId creditId = new ObjectId();
    Organization org =
        organizationFactory.createAtlasOrganization(
            DateUtils.truncate(Date.from(clock.instant()), Calendar.MONTH));
    paymentMethodStubber.stubPaymentMethod(org.getId());
    org = selfServeScenarios.createOrgWithMOB(org);

    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(startOfNextMonth)
            .endDate(nextYear)
            .elasticInvoicing(true)
            .amountCents(0)
            .type(CreditType.PREPAID_NDS)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(startOfNextMonth).build())
            .build();
    creditDao.save(credit);

    List<Invoice> invoice = invoiceSvc.findPendingMonthlyInvoicesByOrgIds(List.of(org.getId()));

    List<Payment> payments = paymentSvc.getPaymentsByInvoiceId(invoice.get(0).getId(), true);
    assertEquals(payments.size(), 1);
    paymentSvc.updatePaymentStatus(
        payments.get(0).getId(), Status.PAID, AuditInfoHelpers.fromInternal(), "note");

    creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    assertEquals(invoice.size(), 1);

    jobQueueTestUtils.processAllJobsInQueue();
    List<Payment> updatedPayments = paymentSvc.getPaymentsByInvoiceId(invoice.get(0).getId(), true);

    assertEquals(updatedPayments.size(), 1);
    assertEquals(updatedPayments.get(0).getStatus(), Payment.Status.PAID);
    Credit updatedCredit = creditSvc.findById(creditId);
    assertEquals(updatedCredit.getStartDate(), startOfMonth);
  }

  @Test
  public void testPullForwardCredit_Rebills() throws Exception {
    // start date is next month and it pulls forward to this month so rebills

    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    ObjectId creditId = new ObjectId();
    Organization org =
        organizationFactory.createAtlasOrganization(
            DateUtils.truncate(Date.from(clock.instant()), Calendar.MONTH));
    paymentMethodStubber.stubPaymentMethod(org.getId());
    org = selfServeScenarios.createOrgWithMOB(org);

    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(startOfNextMonth)
            .type(CreditType.PREPAID_NDS)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(startOfNextMonth).build())
            .build();
    creditDao.save(credit);

    List<Invoice> invoice = invoiceSvc.findPendingMonthlyInvoicesByOrgIds(List.of(org.getId()));

    creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    assertEquals(invoice.size(), 1);

    jobQueueTestUtils.processAllJobsInQueue();
    List<Payment> updatedPayments = paymentSvc.getPaymentsByInvoiceId(invoice.get(0).getId(), true);

    assertEquals(updatedPayments.size(), 2);
    assertTrue(updatedPayments.stream().anyMatch(p -> p.getStatus().equals(Status.CREATED)));
    assertTrue(updatedPayments.stream().anyMatch(p -> p.getStatus().equals(Status.CANCELLED)));
    Credit updatedCredit = creditSvc.findById(creditId);
    assertEquals(updatedCredit.getStartDate(), startOfMonth);
  }

  @Test
  public void testPullForwardOldCredit_Rebills() throws Exception {
    // start date is next month and it pulls forward to this month so rebills

    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    ObjectId creditId = new ObjectId();
    Organization org =
        organizationFactory.createAtlasOrganization(
            DateUtils.truncate(Date.from(clock.instant()), Calendar.MONTH));
    paymentMethodStubber.stubPaymentMethod(org.getId());
    org = selfServeScenarios.createOrgWithMOB(org);

    Credit credit =
        new Credit.Builder()
            .id(creditId)
            .startDate(startOfNextMonth)
            .type(CreditType.PREPAID_NDS)
            .orgId(org.getId())
            .salesforceOpportunityLineItem(
                SalesforceOpportunityLineItem.builder().startDate(startOfNextMonth).build())
            .build();
    creditDao.save(credit);

    List<Invoice> invoice = invoiceSvc.findPendingMonthlyInvoicesByOrgIds(List.of(org.getId()));

    creditSvc.pullForwardCredit(creditId, now, AuditInfoHelpers.fromSystem());
    assertEquals(invoice.size(), 1);

    jobQueueTestUtils.processAllJobsInQueue();
    List<Payment> updatedPayments = paymentSvc.getPaymentsByInvoiceId(invoice.get(0).getId(), true);

    assertEquals(updatedPayments.size(), 2);
    assertTrue(updatedPayments.stream().anyMatch(p -> p.getStatus().equals(Status.CREATED)));
    assertTrue(updatedPayments.stream().anyMatch(p -> p.getStatus().equals(Status.CANCELLED)));
    Credit updatedCredit = creditSvc.findById(creditId);
    assertEquals(updatedCredit.getStartDate(), startOfMonth);
  }

  @Test
  public void testFindByGcpEntitlementId() {
    Credit found =
        Credit.builder().id(new ObjectId()).gcpMarketplaceEntitlementId("entitlementId").build();

    Credit found2 =
        Credit.builder().id(new ObjectId()).gcpMarketplaceEntitlementId("entitlementId").build();

    Credit notFound1 =
        Credit.builder().id(new ObjectId()).gcpMarketplaceEntitlementId("entitlementId2").build();

    Credit notFound2 =
        Credit.builder().id(new ObjectId()).gcpMarketplaceEntitlementId("entitlementId2").build();

    creditDao.save(notFound1);
    creditDao.save(notFound2);
    creditDao.save(found);
    creditDao.save(found2);

    assertThat(
        creditSvc.findByGcpEntitlementId("entitlementId").stream()
            .map(Credit::getId)
            .collect(Collectors.toList()),
        containsInAnyOrder(found.getId(), found2.getId()));
  }

  @Test
  public void testFindByAzureSubscriptionId() {
    UUID uuid1 = UUID.randomUUID();
    UUID uuid2 = UUID.randomUUID();
    Credit found = Credit.builder().id(new ObjectId()).azureExternalSubscriptionId(uuid1).build();

    Credit found2 = Credit.builder().id(new ObjectId()).azureExternalSubscriptionId(uuid1).build();

    Credit notFound1 =
        Credit.builder().id(new ObjectId()).azureExternalSubscriptionId(uuid2).build();

    Credit notFound2 =
        Credit.builder().id(new ObjectId()).azureExternalSubscriptionId(uuid2).build();

    creditDao.save(notFound1);
    creditDao.save(notFound2);
    creditDao.save(found);
    creditDao.save(found2);

    assertThat(
        creditSvc.findByAzureSubscriptionId(uuid1).stream()
            .map(Credit::getId)
            .collect(Collectors.toList()),
        containsInAnyOrder(found.getId(), found2.getId()));
  }

  @Test
  public void testIsSalesSoldOnDate() {
    LocalDate now = LocalDate.now().minusMonths(1).withDayOfMonth(15);
    LocalDate startDate = LocalDate.now().minusMonths(1).withDayOfMonth(1);
    LocalDate endDate = startDate.plusYears(1);
    ObjectId orgId = new ObjectId();

    Credit credit =
        Credit.builder()
            .startDate(dateOf(startDate))
            .endDate(dateOf(endDate))
            .amountCents(100)
            .type(CreditType.PREPAID_NDS)
            .elasticInvoicing(false)
            .orgId(orgId)
            .build();

    creditDao.save(credit);

    assertTrue(creditSvc.isSalesSoldOnDate(orgId, now));
    assertTrue(creditSvc.isSalesSoldOnDate(orgId, startDate));
    assertTrue(creditSvc.isSalesSoldOnDate(orgId, endDate.minusDays(1)));
    assertFalse(creditSvc.isSalesSoldOnDate(orgId, endDate));
    assertFalse(creditSvc.isSalesSoldOnDate(orgId, now.plusYears(1)));
    assertFalse(creditSvc.isSalesSoldOnDate(orgId, startDate.minusDays(1)));
    assertFalse(creditSvc.isSalesSoldOnDate(new ObjectId(), now));
  }

  @Test
  public void testHasAvailableCreditInRange() {
    Date today = new Date();
    Date tomorrow = DateUtils.addDays(today, 1);

    assertTrue(
        creditSvc.hasAvailableCreditInRange(
            oid(308), today, tomorrow, Set.of(AZURE_MONTHLY_COMMITMENT)));
    assertFalse(
        creditSvc.hasAvailableCreditInRange(
            oid(308), today, today, Set.of(AZURE_MONTHLY_COMMITMENT)));
  }

  private void assertGenericCreditAudit(Credit credit) throws SvcException {
    creditSvc.issueServiceCreditRetroactively(credit, auditInfo);
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit e = (BillingAudit) events.get(0);
    assertEquals(2000, e.getCreditAmountCents());
    assertNull(e.getInvoiceId());
    assertNull(e.getInvoiceEndDate());
  }
}
