package com.xgen.svc.mms.svc.explorer;

import static com.xgen.cloud.common.explorer._public.model.DataExplorerOpType.CREATE_ROLLING_INDEX;
import static com.xgen.cloud.common.explorer._public.model.DataExplorerOpType.LIST_INDEX_STATS;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.svc.mms.util.UnitTestUtils.l;
import static com.xgen.svc.mms.util.UnitTestUtils.o;
import static java.time.temporal.ChronoUnit.HOURS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._private.dao.AgentJobsProcessorDao;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.dao.explorer.DataExplorerResponseDao;
import com.xgen.svc.mms.model.explorer.Parameters;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import org.bson.BasicBSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class CreateRollingIndexJobRequestIntTests extends JobRequestIntTest {

  @Inject private AgentJobsProcessorDao _agentJobsProcessorDao;
  @Inject private DataExplorerResponseDao _dataExplorerResponseDao;
  @Inject private AppSettings _settings;

  private Group _group;
  private Organization _org;
  private AppUser _groupOwner;

  private static final String DEFAULT_KEYS = "{ \"a\" : 1, \"b\" : -1 }";

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithPremiumPlan();
    _org = _organizationSvc.findById(_group.getOrgId());
    _groupOwner = MmsFactory.createGroupOwnerUser(_group);

    MmsFactory.createAutomationAgentAuditEntry(_group, "hostname", "4.8.2.2491", new Date());
  }

  @After
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    DataExplorerTestResponseInserter.killAll();
    _agentJobsProcessorDao.deleteByGroup(_group.getId());
  }

  @Test
  public void testSubmitAndRetrieveDataExplorerRequest_simpleCreateRollingIndex() throws Exception {
    final HostCluster hostCluster = MmsFactory.createReplicaSet_V_2_4(_group, "replSet", true);
    final String options = "{ \"background\" : true }";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, hostCluster, null)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    final boolean disableConcurrentRollingIndexes =
        isFeatureFlagEnabled(
            FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);

    final CreateRollingIndexJobRequest createRollingIndexJobRequest =
        new CreateRollingIndexJobRequest(
            disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);

    try {
      createRollingIndexJobRequest.submitJob();
      fail("Submitting Create Rolling Index job should result in UnsupportedOperationException");
    } catch (final SvcException pEx) {
      assertEquals(UnsupportedOperationException.class, pEx.getCause().getClass());
    }
  }

  @Test
  public void testSubmit_existingIndexValidation_noExistingIndexes() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_2(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null));

    final String options = "{\"name\": \"bloop\"}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
    } catch (final SvcException pEx) {
      fail("No matching existing indexes, so validation should pass: " + pEx.getMessage());
    }
  }

  @Test
  public void testSubmit_existingIndexValidation_existingIndexes_nameExists() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse("db.collection", "bloop", o("x", 1), null)); // same name; different keys

    final String options = "{\"name\": \"bloop\"}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail("Matching existing index present, so validation should fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void testSubmit_existingIndexValidation_existingIndexes_nameMatchesDefaultName() {
    // setup
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, "myhost.com", 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection", "a_1_b_-1", o("x", 1), null)); // same name; different keys

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options("")
            .build();

    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail("Matching existing index present, so validation should fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void testSubmit_existingIndexValidation_existingIndexes_keysExist_4_2() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_2(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection", "bloop", o("a", 1, "b", -1), null)); // different name; same keys

    final String options = "{\"name\": \"blarp\"}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail("Matching existing index present, so validation should fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void testSubmit_existingIndexValidation_existingIndexes_keysExist_4_9() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection", "bloop", o("a", 1, "b", -1), null)); // different name; same keys

    final String options = "{\"name\": \"blarp\"}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test

    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail("Matching existing index present, so validation should fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void
      testSubmit_existingIndexValidation_existingIndexes_keysExistWithNoPartialFilterExpression_4_9() {
    // setup
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, "myhost.com", 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection",
            "bloop",
            o("a", 1, "b", -1),
            null)); // different name; same keys, no filter expression

    final String options =
        "{\"name\": \"blarp\", \"partialFilterExpression\" : { \"b\": { \"$lt\": 2 }}}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
    } catch (final SvcException pEx) {
      fail(
          "Existing index has the same keys but no partialFilterExpression which is"
              + " allowed in 4.9+, so validation should pass: "
              + pEx.getMessage());
    }
  }

  @Test
  public void
      testSubmit_existingIndexValidation_existingIndexes_keysExistWithNoPartialFilterExpression_4_2() {
    // setup
    final String hostId = MmsFactory.createNewHost_V_4_2(_group, "myhost.com", 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection", "bloop", o("a", 1, "b", -1), null)); // different name; same keys

    final String options =
        "{\"name\": \"blarp\", \"partialFilterExpression\" : { \"b\": { \"$lt\": 2 }}}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail(
          "New indexes have the same keys and a partialFilterExpression which is not"
              + " allowed below 4.9+, so validation should fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void
      testSubmit_existingIndexValidation_existingIndexes_keysExistWithSamePartialFilterExpression_4_9() {
    // setup
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, "myhost.com", 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection",
            "bloop",
            o("a", 1, "b", -1),
            o("b", o("$lt", 2)))); // different name; same keys and partialFilterExpression

    final String options =
        "{\"name\": \"blarp\", \"partialFilterExpression\" : { \"b\": { \"$lt\": 2 }}}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail(
          "Existing index has the same keys and partialFilterExpression, so validation should"
              + " fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void
      testSubmit_existingIndexValidation_existingIndexes_keysExistWithDifferentPartialFilterExpression_4_9() {
    // setup
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, "myhost.com", 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection",
            "bloop",
            o("a", 1, "b", -1),
            o("b", o("$lt", 2)))); // different name and partialFilterExpression; same keys

    final String options =
        "{\"name\": \"blarp\", \"partialFilterExpression\" : { \"b\": { \"$gt\": 4 }}}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .requestType(CREATE_ROLLING_INDEX)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
    } catch (final SvcException pEx) {
      fail(
          "Existing index has the same keys but different partialFilterExpression which is"
              + " allowed in 4.9+, so validation should pass: "
              + pEx.getMessage());
    }
  }

  @Test
  public void
      testSubmit_existingIndexValidation_existingIndexes_keysExistWithSamePartialFilterExpression_4_9_2() {
    // setup
    final String hostId = MmsFactory.createNewHost_V_4_9(_group, "myhost.com", 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null),
        indexStatsResponse(
            "db.collection",
            "bloop",
            o("a", 1, "b", -1),
            o("b", o("$lt", 2)).append("a", o("$gt", 4))));

    final String options =
        "{\"name\": \"blarp\", \"partialFilterExpression\" : { \"a\": { \"$gt\": 4 }, \"b\": {"
            + " \"$lt\": 2 } }}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys(DEFAULT_KEYS)
            .options(options)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail(
          "Existing index has the same keys and the same partialFilterExpression which is"
              + " not allowed in 4.9+, so validation should fail. ");
    } catch (final SvcException pEx) {
    }
  }

  @Test
  public void testSubmit_compoundHashedIndex_4_2() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_2(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null));

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys("{ \"a\" : 1, \"b\" :\"hashed\" }")
            .requestType(CREATE_ROLLING_INDEX)
            .options("{\"name\": \"blarp\"}")
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail(
          "Compound hashed indexes are not allowed in mongod version lower than 4.4, so validation"
              + " should fail");
    } catch (final SvcException pEx) {
      // pass
    }
  }

  @Test
  public void testSubmit_compoundHashedIndex_4_4() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_4(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null));

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys("{ \"a\" : 1, \"b\" :\"hashed\" }")
            .requestType(CREATE_ROLLING_INDEX)
            .options("{\"name\": \"blarp\"}")
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
    } catch (final SvcException pEx) {
      fail(
          "Compound hashed indexes are allowed in mongod version lower than 4.4, so validation"
              + " should pass");
    }
  }

  @Test
  public void testSubmit_compoundHashedIndex_multipleHashedKeys_4_4() {

    // setup
    final String hostname = "myhost.com";
    final String hostId = MmsFactory.createNewHost_V_4_4(_group, hostname, 27017);
    final Host host = _hostSvc.findHostById(hostId, _group.getId());

    // just the _id index
    DataExplorerTestResponseInserter.start(
        _agentJobsProcessorDao,
        _dataExplorerResponseDao,
        _group.getId(),
        null,
        host,
        "db.collection",
        LIST_INDEX_STATS,
        indexStatsResponse("db.collection", "_id_", o("_id", 1), null));

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, null, host)
            .keys("{ \"a\" : 1, \"b\" :\"hashed\" , \"c\" :\"hashed\" }")
            .options("{\"name\": \"blarp\"}")
            .requestType(CREATE_ROLLING_INDEX)
            .build();

    // test
    try {
      final boolean disableConcurrentRollingIndexes =
          isFeatureFlagEnabled(
              FeatureFlag.DISABLE_CONCURRENT_ROLLING_INDEXES, _settings, null, parameters._group);
      new CreateRollingIndexJobRequest(
          disableConcurrentRollingIndexes, _dataExplorerSvc, parameters);
      fail(
          "Compound hashed indexes can't have more than one hashed field , so validation should"
              + " fail");
    } catch (final SvcException pEx) {
    }
  }

  private static BasicBSONObject indexStatsResponse(
      final String pNs,
      final String pIndexName,
      final BasicBSONObject pKey,
      final BasicBSONObject pPartialFilterExpression) {
    final BasicBSONObject indexStatsResponse =
        o("ns", pNs)
            .append("key", pKey)
            .append(
                "indexAccesses",
                l(o("ops", 10L, "since", Date.from(Instant.now().minus(7, HOURS)))))
            .append("indexSize", 32)
            .append("name", pIndexName);
    if (pPartialFilterExpression != null) {
      indexStatsResponse.append("partialFilterExpression", pPartialFilterExpression);
    }
    return indexStatsResponse;
  }
}
