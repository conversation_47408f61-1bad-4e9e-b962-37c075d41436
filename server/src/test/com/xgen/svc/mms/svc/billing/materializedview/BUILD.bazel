load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/svc/mms/dao/billing",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "//server/src/test/com/xgen/persistence",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/mms/svc/billing/utils",
        "@maven//:junit_junit",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
