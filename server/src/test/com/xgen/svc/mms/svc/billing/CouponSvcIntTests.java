package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.activity._public.svc.event.EventSvc;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.DiscountDao;
import com.xgen.svc.mms.model.billing.Coupon;
import com.xgen.svc.mms.model.billing.Discount;
import com.xgen.svc.mms.model.billing.DollarOffCoupon;
import com.xgen.svc.mms.model.billing.PercentOffCoupon;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CouponSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private AuditSvc _auditSvc;

  @Inject private CouponSvc _couponSvc;

  @Inject private DiscountDao _discountDao;

  @Inject private EventSvc eventSvc;

  private AppUser user;
  private AuditInfo auditInfo;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();

    user = new AppUser();
    user.setUsername("baby.pear");
    auditInfo = MmsFactory.createAuditInfoFromUiCall(user, false, "localhost");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/CouponDao/coupons.json.ftl", null, Coupon.DB_NAME, Coupon.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/DiscountDao/discounts.json.ftl",
        null,
        Discount.DB_NAME,
        Discount.COLLECTION_NAME);
  }

  @Test
  @Disabled
  public void testIsValidPercentOffCoupon() {
    assertFalse(getCouponSvc().isValidPercentOffCoupon(oid(200), "BAD_COUPON_CODE"));
    assertFalse(getCouponSvc().isValidPercentOffCoupon(oid(200), "COUPON2"));
    assertFalse(getCouponSvc().isValidPercentOffCoupon(oid(200), "COUPON3"));
    assertTrue(getCouponSvc().isValidPercentOffCoupon(oid(200), "UNLIMITED_COUPON"));
  }

  @Test
  public void testApplyPercentOffCoupon() {
    PercentOffCoupon unlimitedPercentOffCoupon =
        getCouponSvc().findPercentOffCouponByCode("UNLIMITED_COUPON");

    assertEquals(
        BillingErrorCode.INVALID_COUPON_CODE,
        getCouponSvc()
            .applyPercentOffCoupon(
                oid(200), getCouponSvc().findPercentOffCouponByCode("BAD_COUPON_CODE"), auditInfo)
            .getErrorCode());
    assertEquals(
        BillingErrorCode.MAX_COUPON_APPLICATIONS_EXCEEDED,
        getCouponSvc()
            .applyPercentOffCoupon(
                oid(200), getCouponSvc().findPercentOffCouponByCode("COUPON2"), auditInfo)
            .getErrorCode());
    assertEquals(
        BillingErrorCode.COUPON_ALREADY_USED,
        getCouponSvc()
            .applyPercentOffCoupon(
                oid(202), getCouponSvc().findPercentOffCouponByCode("COUPON2"), auditInfo)
            .getErrorCode());
    assertEquals(
        BillingErrorCode.COUPON_CONFLICT,
        getCouponSvc()
            .applyPercentOffCoupon(
                oid(202), getCouponSvc().findPercentOffCouponByCode("COUPON3"), auditInfo)
            .getErrorCode());
    assertEquals(
        CommonErrorCode.NONE,
        getCouponSvc()
            .applyPercentOffCoupon(
                oid(202), getCouponSvc().findPercentOffCouponByCode("UNLIMITED_COUPON"), auditInfo)
            .getErrorCode());

    List<Discount> discounts = getDiscountDao().findActiveByOrgId(oid(202), new Date());
    Discount discount =
        discounts.stream().filter(d -> Objects.equals(d.getCouponId(), oid(4))).findFirst().get();
    List<Event> events = eventSvc.findAll(2);
    assertEquals(1, events.size());
    BillingAudit event = (BillingAudit) events.get(0);
    assertEquals(0.05, event.getDiscountPercent(), 0.00000001);
    assertEquals(Collections.singleton(SKU.MMS_PREMIUM), event.getDiscountSkus());
    assertEquals(discount.getEndDate(), event.getDiscountEndDate());
    assertEquals(
        CouponSvc.getEndDate(CouponSvc.getTodaysDate(), unlimitedPercentOffCoupon),
        event.getDiscountEndDate());
    // Test that a coupon with the maxApplicationsPerOrg set to 0 can be applied an unlimited number
    // of times.
    assertNotEquals(
        BillingErrorCode.MAX_COUPON_APPLICATIONS_EXCEEDED,
        getCouponSvc()
            .applyPercentOffCoupon(
                oid(102),
                getCouponSvc().findPercentOffCouponByCode("UNLIMITED_COUPON"),
                auditInfo));
  }

  @Test
  public void testGetActivePercentOffCouponsByOrgId() {
    Date startDate = new Date();
    Date endDate = DateUtils.addDays(startDate, 30);
    List<PercentOffCoupon> coupons1 =
        getCouponSvc().getActivePercentOffCouponsByOrgId(oid(200), startDate, endDate);
    assertEquals(1, coupons1.size());
    assertEquals(oid(2), coupons1.get(0).getId());
    List<PercentOffCoupon> coupons2 =
        getCouponSvc().getActivePercentOffCouponsByOrgId(oid(201), startDate, endDate);
    assertEquals(0, coupons2.size());
    Date oneYearAgo = DateUtils.addYears(startDate, -1);
    List<PercentOffCoupon> coupons3 =
        getCouponSvc().getActivePercentOffCouponsByOrgId(oid(201), oneYearAgo, startDate);
    assertEquals(1, coupons3.size());
    assertEquals(oid(1), coupons3.get(0).getId());
  }

  @Test
  public void testGetDollarOffCouponsByLowercaseSearchString() {
    String searchTxt = "getatlas";
    String sortBy = DollarOffCoupon.START_DATE_FIELD;
    boolean isAscending = false;
    int startIndex = 0;
    int numberOfPromos = 10;
    List<DollarOffCoupon> coupons =
        getCouponSvc()
            .findActiveDollarOffCoupons(searchTxt, sortBy, isAscending, startIndex, numberOfPromos);
    assertEquals(1, coupons.size());
    DollarOffCoupon dollarOffCoupon = coupons.get(0);
    assertEquals(dollarOffCoupon.getDollarOffAmountInCents(), 10000L);
  }

  protected CouponSvc getCouponSvc() {
    return _couponSvc;
  }

  protected DiscountDao getDiscountDao() {
    return _discountDao;
  }
}
