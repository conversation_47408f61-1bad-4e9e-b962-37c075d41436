package com.xgen.svc.mms.svc.billing.regressiontests.testcases;

import static com.xgen.svc.mms.svc.billing.regressiontests.model.testcases.enums.RegressionTestFeatureTypes.DAILY_BILLING_LINE_ITEMS;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xgen.svc.mms.svc.billing.jsoncomparator.model.DocumentDiscrepancy;
import com.xgen.svc.mms.svc.billing.regressiontests.helper.RegressionFeatureTestsRunner;
import java.util.List;

public class DailyBillingLineItemRegressionTests extends RegressionFeatureTestsRunner {
  public static List<String> testCases() {
    return getTestCasesFromFeatureType(DAILY_BILLING_LINE_ITEMS);
  }

  @Override
  protected String generateFailureReport(List<DocumentDiscrepancy> documentDiscrepancies)
      throws JsonProcessingException {
    StringBuilder errMessageBuilder =
        new StringBuilder(super.generateFailureReport(documentDiscrepancies));
    errMessageBuilder.append(
        "\n Please refer the following wiki page on how to resolve test failure:\n");
    errMessageBuilder.append(
        "https://wiki.corp.mongodb.com/spaces/MMS/pages/347080002/Daily+Billing+Line+Item+Regression+Tests#DailyBillingLineItemRegressionTests-HowtoResolveTestFailure");
    return errMessageBuilder.toString();
  }
}
