package com.xgen.svc.mms.svc.metrics;

import static com.xgen.svc.mms.util.serverless.ServerlessMetricTestHelpers.createServerlessClusterMeasurementSequence;
import static java.time.temporal.ChronoUnit.HOURS;
import static java.time.temporal.ChronoUnit.MILLIS;
import static java.time.temporal.ChronoUnit.MINUTES;
import static java.time.temporal.ChronoUnit.SECONDS;
import static java.util.Collections.singletonList;

import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.metrics._private.dao.serverless.ServerlessClusterMeasurementDao;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.BillingAnomaly;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.BillingAnomaly.Anomaly;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessClusterMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessHost;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessInstanceResourceConsumption;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.util.serverless.ServerlessMetricTestHelpers;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ServerlessBillingMetricsSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private ServerlessClusterMeasurementDao _serverlessClusterMeasurementDao;

  @Inject private ServerlessBillingMetricsSvc _serverlessBillingMetricsSvc;

  private final Instant RECEIVED_TIME = Instant.now();
  private final Instant SAMPLE_TIME = Instant.now();
  private final ObjectId UNIQUE_ID = ObjectId.get();
  private final ObjectId UNIQUE_ID_2 = ObjectId.get();
  private final String HOSTNAME = "hostname";

  private Organization _organization;
  private Group _group;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _organization = MmsFactory.createOrganizationWithStandardPlan("Test");
    _group = MmsFactory.createGroup(_organization, "cus_0001");
    _serverlessClusterMeasurementDao = AppConfig.getInstance(ServerlessClusterMeasurementDao.class);
  }

  @Test
  void findOldestHourMetricsTimestampWhenNoMeasurements() {
    final Instant lastMetricInstant =
        _serverlessBillingMetricsSvc.getOldestHourlyMetricTimestampForServerlessInstance(
            UNIQUE_ID, SAMPLE_TIME.minus(50, HOURS));

    Assertions.assertNull(lastMetricInstant);

    final Instant minuteMetricInstant =
        _serverlessBillingMetricsSvc.getOldestHourlyMetricTimestampForServerlessInstance(
            UNIQUE_ID, SAMPLE_TIME.minus(50, HOURS));
    Assertions.assertNull(minuteMetricInstant);
  }

  @Test
  void findOldestHourMetricsTimestamp() {
    final List<ServerlessClusterMeasurement> metrics =
        IntStream.range(0, 25)
            .boxed()
            .map(
                num ->
                    List.of(
                        ServerlessMetricTestHelpers.newTestServerlessClusterMeasurement(
                            UNIQUE_ID,
                            Date.from(SAMPLE_TIME.minus(num, HOURS)),
                            Date.from(RECEIVED_TIME.minus(num, HOURS)),
                            singletonList(
                                Pair.of(
                                    HOSTNAME,
                                    new ServerlessMetricTestHelpers.HostParams(
                                        2L, 2L, ServerlessHost.ROLE.PRIMARY)))),
                        ServerlessMetricTestHelpers.newTestServerlessClusterMeasurement(
                            UNIQUE_ID_2,
                            Date.from(SAMPLE_TIME.minus(num * 2, HOURS)),
                            Date.from(RECEIVED_TIME.minus(num * 2, HOURS)),
                            singletonList(
                                Pair.of(
                                    HOSTNAME,
                                    new ServerlessMetricTestHelpers.HostParams(
                                        4L, 4L, ServerlessHost.ROLE.PRIMARY))))))
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

    _serverlessClusterMeasurementDao.insert(metrics, Retention.HOUR_DATA_RETENTION);

    final Instant lastMetricInstant =
        _serverlessBillingMetricsSvc.getOldestHourlyMetricTimestampForServerlessInstance(
            UNIQUE_ID, SAMPLE_TIME.minus(40, HOURS));

    Assertions.assertEquals(SAMPLE_TIME.minus(24, HOURS).truncatedTo(MILLIS), lastMetricInstant);

    // Metrics exist up to 50 hours ago, only retrieves timestamp from within time range
    final Instant lastMetricInstant2 =
        _serverlessBillingMetricsSvc.getOldestHourlyMetricTimestampForServerlessInstance(
            UNIQUE_ID_2, SAMPLE_TIME.minus(40, HOURS));
    Assertions.assertEquals(SAMPLE_TIME.minus(40, HOURS).truncatedTo(MILLIS), lastMetricInstant2);
  }

  @Test
  void findOldestMinuteMetricsTimestamp() {
    final List<ServerlessClusterMeasurement> metrics =
        IntStream.range(0, 61)
            .boxed()
            .map(
                num ->
                    List.of(
                        ServerlessMetricTestHelpers.newTestServerlessClusterMeasurement(
                            UNIQUE_ID,
                            Date.from(SAMPLE_TIME.minus(num, MINUTES)),
                            Date.from(RECEIVED_TIME.minus(num, MINUTES)),
                            singletonList(
                                Pair.of(
                                    HOSTNAME,
                                    new ServerlessMetricTestHelpers.HostParams(
                                        2L, 2L, ServerlessHost.ROLE.PRIMARY)))),
                        ServerlessMetricTestHelpers.newTestServerlessClusterMeasurement(
                            UNIQUE_ID_2,
                            Date.from(SAMPLE_TIME.minus(num * 2, MINUTES)),
                            Date.from(RECEIVED_TIME.minus(num * 2, MINUTES)),
                            singletonList(
                                Pair.of(
                                    HOSTNAME,
                                    new ServerlessMetricTestHelpers.HostParams(
                                        4L, 4L, ServerlessHost.ROLE.PRIMARY))))))
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

    _serverlessClusterMeasurementDao.insert(metrics, Retention.MINUTE_DATA_RETENTION);

    final Instant lastMetricInstant =
        _serverlessBillingMetricsSvc.getOldestMinuteMetricTimestampForServerlessInstance(
            UNIQUE_ID, SAMPLE_TIME.minus(120, MINUTES));

    Assertions.assertEquals(SAMPLE_TIME.minus(60, MINUTES).truncatedTo(MILLIS), lastMetricInstant);

    final Instant lastMetricInstant2 =
        _serverlessBillingMetricsSvc.getOldestMinuteMetricTimestampForServerlessInstance(
            UNIQUE_ID_2, SAMPLE_TIME.minus(120, MINUTES));
    Assertions.assertEquals(
        SAMPLE_TIME.minus(120, MINUTES).truncatedTo(MILLIS), lastMetricInstant2);
  }

  // case 0 happy path: collect all metrics + extra at the beginning + no migration
  @Test
  void testConsumeMeasurements_happyPath() {
    final int NUM_METRICS = 65;
    final Instant until = SAMPLE_TIME.plus(NUM_METRICS, MINUTES).minus(30, SECONDS);

    // generate 65 metrics
    final List<ServerlessClusterMeasurement> measurements =
        createServerlessClusterMeasurementSequence(
            0, 1, 0, 65, SAMPLE_TIME, 3, 0, _group.getId(), UNIQUE_ID, false);

    // insert measurements
    _serverlessClusterMeasurementDao.insert(measurements, Retention.MINUTE_DATA_RETENTION);

    // query and consume
    final ServerlessInstanceResourceConsumption consumption =
        _serverlessBillingMetricsSvc.getResourceConsumptionForServerlessInstance(
            UNIQUE_ID, SAMPLE_TIME.plus(5, MINUTES).minus(30, SECONDS), until);

    // assert no anomalies && gauge + counter vals
    Assertions.assertEquals(0, consumption.getBillingAnomalies().size());
    // 60 + (60 * 2) + (60 * 3) = 360
    Assertions.assertEquals((Long) 360L, consumption.getIndexEntryBytesWritten());
    // 3 * ( 5 + 6 + ... + 64) / 60 = 101.80
    Assertions.assertTrue(consumption.getIndexSize() == 103.5);
    // ((5 + 6 + ... + 64)/60 + (10 + 12 + ... + 128)/60 + (15 + 18 +... + 192)/60)/3 = 69.0
    Assertions.assertTrue(consumption.getOpenConnections() == 69.0);
  }

  @Test
  void testConsumeMeasurements_maxOpsPerSec() {
    final Instant since = SAMPLE_TIME.truncatedTo(HOURS).minus(1, HOURS);
    final Instant until = since.plus(1, HOURS);

    // create metrics up to one minute before the start of the hour
    final List<ServerlessClusterMeasurement> previousHourMeasurements =
        createServerlessClusterMeasurementSequence(
            0, 500, 10, 2, since.minus(2, MINUTES), 3, 3, _group.getId(), UNIQUE_ID, false);
    // create metrics up to the end of the hour
    final List<ServerlessClusterMeasurement> currentHourMeasurements =
        createServerlessClusterMeasurementSequence(
            0, 100, 510, 60, since, 3, 3, _group.getId(), UNIQUE_ID, false);
    // create metrics for the next hour
    final List<ServerlessClusterMeasurement> nextHourMeasurements =
        createServerlessClusterMeasurementSequence(
            60, 100, 60 * 500 + 510, 60, since, 3, 3, _group.getId(), UNIQUE_ID, false);

    // insert measurements
    _serverlessClusterMeasurementDao.insert(
        previousHourMeasurements, Retention.MINUTE_DATA_RETENTION);
    _serverlessClusterMeasurementDao.insert(
        currentHourMeasurements, Retention.MINUTE_DATA_RETENTION);
    _serverlessClusterMeasurementDao.insert(nextHourMeasurements, Retention.MINUTE_DATA_RETENTION);

    // query and consume
    final Optional<Double> maxOpsPerSecForCluster =
        _serverlessBillingMetricsSvc.getOpsPerMinuteForCluster(UNIQUE_ID, since, until).stream()
            .max(Double::compareTo);

    Assertions.assertTrue(maxOpsPerSecForCluster.isPresent(), "no metrics found for cluster");

    final double maxOps = maxOpsPerSecForCluster.orElseThrow();

    Assertions.assertEquals(60.0, maxOps, 0.01);
  }

  // case 2 missing data at the start and end: collect metrics after since + no migration + stop
  @Test
  void testConsumeMeasurements_missingDataAtStartAndEnd() {
    final int NUM_METRICS = 10;
    final Instant until = SAMPLE_TIME.plus(20, MINUTES);
    final List<ServerlessClusterMeasurement> measurements =
        createServerlessClusterMeasurementSequence(
            0,
            1,
            0,
            NUM_METRICS,
            SAMPLE_TIME.plus(5, MINUTES),
            3,
            0,
            _group.getId(),
            UNIQUE_ID,
            false);

    // insert measurements
    _serverlessClusterMeasurementDao.insert(measurements, Retention.MINUTE_DATA_RETENTION);

    // query and consume
    final ServerlessInstanceResourceConsumption consumption =
        _serverlessBillingMetricsSvc.getResourceConsumptionForServerlessInstance(
            UNIQUE_ID, SAMPLE_TIME, until);

    Assertions.assertEquals(15, consumption.getBillingAnomalies().size());

    final Map<Anomaly, Integer> anomalyCounts = getAnomalyCounts(consumption);

    Assertions.assertEquals((Integer) 1, anomalyCounts.get(Anomaly.NO_PREVIOUS_DATA));
    Assertions.assertEquals((Integer) 1, anomalyCounts.get(Anomaly.DATA_BEGINS_MID_RANGE));
    Assertions.assertEquals((Integer) 1, anomalyCounts.get(Anomaly.DATA_ENDS_EARLY));
    Assertions.assertEquals(
        (Integer) 3,
        anomalyCounts.get(Anomaly.PROXY_DATA_BEGINS_MID_RANGE),
        "Should have gotten 1 anomaly per host");
    Assertions.assertEquals(
        (Integer) 3,
        anomalyCounts.get(Anomaly.SERVER_DATA_BEGINS_MID_RANGE),
        "Should have gotten 1 anomaly per host");
    Assertions.assertEquals(
        (Integer) 3,
        anomalyCounts.get(Anomaly.PROXY_HOST_DATA_ENDS_EARLY),
        "Should have gotten 1 anomaly per host");
    Assertions.assertEquals(
        (Integer) 3,
        anomalyCounts.get(Anomaly.SERVER_HOST_DATA_ENDS_EARLY),
        "Should have gotten 1 anomaly per host");
  }

  @Test
  void testConsumeMeasurements_onlyOneMetricBeforeSince() {
    final int NUM_METRICS = 1;
    final List<ServerlessClusterMeasurement> measurements =
        createServerlessClusterMeasurementSequence(
            0, 1, 0, NUM_METRICS, SAMPLE_TIME, 3, 0, _group.getId(), UNIQUE_ID, false);

    final Instant since = SAMPLE_TIME.plus(3, MINUTES);
    final Instant until = SAMPLE_TIME.plus(10, MINUTES);

    // insert measurements
    _serverlessClusterMeasurementDao.insert(measurements, Retention.MINUTE_DATA_RETENTION);

    // query and consume
    final ServerlessInstanceResourceConsumption consumption =
        _serverlessBillingMetricsSvc.getResourceConsumptionForServerlessInstance(
            UNIQUE_ID, since, until);

    Assertions.assertEquals(1, consumption.getBillingAnomalies().size());
    Assertions.assertEquals(Anomaly.NO_DATA, consumption.getBillingAnomalies().get(0).getAnomaly());
    Assertions.assertNull(consumption.getDocumentBytesWritten());
    Assertions.assertEquals((Double) 0.0, consumption.getIndexSize());
  }

  @Test
  void testConsumeMeasurements_onlyOneMetricAfterSince() {
    final int NUM_METRICS = 1;
    final List<ServerlessClusterMeasurement> measurements =
        createServerlessClusterMeasurementSequence(
            0, 1, 0, NUM_METRICS, SAMPLE_TIME, 3, 0, _group.getId(), UNIQUE_ID, false);

    final Instant since = SAMPLE_TIME.minus(3, MINUTES);
    final Instant until = SAMPLE_TIME.plus(10, MINUTES);

    // insert measurements
    _serverlessClusterMeasurementDao.insert(measurements, Retention.MINUTE_DATA_RETENTION);

    // query and consume
    final ServerlessInstanceResourceConsumption consumption =
        _serverlessBillingMetricsSvc.getResourceConsumptionForServerlessInstance(
            UNIQUE_ID, since, until);

    final Map<Anomaly, Integer> anomalyCounts = getAnomalyCounts(consumption);

    Assertions.assertEquals(5, anomalyCounts.keySet().size());
    Assertions.assertEquals((Integer) 1, anomalyCounts.get(Anomaly.NO_PREVIOUS_DATA));
    Assertions.assertEquals((Integer) 1, anomalyCounts.get(Anomaly.DATA_ENDS_EARLY));
    Assertions.assertEquals((Integer) 1, anomalyCounts.get(Anomaly.DATA_BEGINS_MID_RANGE));
    Assertions.assertEquals(
        (Integer) 3,
        anomalyCounts.get(Anomaly.PROXY_DATA_BEGINS_MID_RANGE),
        "Should have gotten 1 anomaly per host");
    Assertions.assertEquals(
        (Integer) 3,
        anomalyCounts.get(Anomaly.SERVER_DATA_BEGINS_MID_RANGE),
        "Should have gotten 1 anomaly per host");
  }

  private Map<Anomaly, Integer> getAnomalyCounts(
      final ServerlessInstanceResourceConsumption pConsumption) {
    final Map<Anomaly, Integer> anomalyCounts = new HashMap<>();
    for (final BillingAnomaly anomaly : pConsumption.getBillingAnomalies()) {
      final int count = anomalyCounts.getOrDefault(anomaly.getAnomaly(), 0);
      anomalyCounts.put(anomaly.getAnomaly(), count + 1);
    }
    return anomalyCounts;
  }
}
