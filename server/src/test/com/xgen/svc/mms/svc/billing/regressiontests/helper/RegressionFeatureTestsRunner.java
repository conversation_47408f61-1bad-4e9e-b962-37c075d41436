package com.xgen.svc.mms.svc.billing.regressiontests.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.inject.Injector;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.svc.billing.jsoncomparator.model.DocumentDiscrepancy;
import com.xgen.svc.mms.svc.billing.regressiontests.model.BaseTestCase;
import com.xgen.svc.mms.svc.billing.regressiontests.model.testcases.enums.RegressionTestFeatureTypes;
import jakarta.inject.Inject;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

/**
 * The abstract class for running all test cases defined under a {@link RegressionTestFeatureTypes}.
 * Extends this class and implement the {@link #testCases()} method to specify the feature the test
 * cases under which you wish to run regression tests against by calling the {@link
 * #getTestCasesFromFeatureType(RegressionTestFeatureTypes)} method with the feature type.
 */
public abstract class RegressionFeatureTestsRunner extends JUnit5BaseSvcTest {
  @Inject private Injector injector;
  private Path tempDir;
  private String testDataFileLocation;

  private static RegressionTestFeatureTypes regressionTestFeatureTypes;

  /**
   * Implement this method to specify the feature the regression tests should test against using the
   * {@link #getTestCasesFromFeatureType(RegressionTestFeatureTypes)} method
   *
   * @return the testing feature type
   */
  public static List<String> testCases() {

    return Collections.emptyList();
  }

  protected static List<String> getTestCasesFromFeatureType(
      RegressionTestFeatureTypes featureTypes) {
    regressionTestFeatureTypes = featureTypes;
    return regressionTestFeatureTypes.getTestCaseConfigs().keySet().stream()
        .collect(Collectors.toList());
  }

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    tempDir = Files.createTempDirectory(this.getClass().getSimpleName());
  }

  @AfterEach
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    Files.deleteIfExists(Path.of(testDataFileLocation));
    Files.deleteIfExists(tempDir);
  }

  @ParameterizedTest
  @MethodSource("testCases")
  public void testBillingSuite(String testName) throws Exception {

    BaseTestCase testCase = regressionTestFeatureTypes.injectTestCaseByName(injector, testName);

    testDataFileLocation = testCase.generateTestDataToFile(tempDir.toString());
    List<DocumentDiscrepancy> documentDiscrepancies =
        testCase.compareOutput(
            regressionTestFeatureTypes.getDefaultExpectedOutputFileLocation(), tempDir.toString());
    Assertions.assertTrue(
        documentDiscrepancies == null || documentDiscrepancies.isEmpty(),
        generateFailureReport(documentDiscrepancies));
  }

  protected String generateFailureReport(List<DocumentDiscrepancy> documentDiscrepancies)
      throws JsonProcessingException {
    StringBuilder errMessageBuilder = new StringBuilder("Following discrepancies detected: \n");
    ObjectWriter objWriter = new ObjectMapper().writer().withDefaultPrettyPrinter();
    errMessageBuilder.append(objWriter.writeValueAsString(documentDiscrepancies));
    return errMessageBuilder.toString();
  }
}
