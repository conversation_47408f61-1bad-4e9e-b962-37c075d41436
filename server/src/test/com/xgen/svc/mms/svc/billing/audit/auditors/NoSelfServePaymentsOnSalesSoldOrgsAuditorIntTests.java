package com.xgen.svc.mms.svc.billing.audit.auditors;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class NoSelfServePaymentsOnSalesSoldOrgsAuditorIntTests extends JUnit5BaseSvcTest {

  @Inject NoSelfServePaymentsOnSalesSoldOrgsAuditor noSelfServePaymentsOnSalesSoldOrgsAuditor;

  @Test
  public void testAuditFail() {
    LocalDateTime now = LocalDateTime.now();
    ObjectId orgId = new ObjectId();
    Payment payment =
        Payment.builder()
            .id(new ObjectId())
            .orgId(orgId)
            .paymentMethod(PaymentMethodType.CREDIT_CARD)
            .amountBilledCents(4L)
            .status(Status.CREATED)
            .created(dateOf(now.plusHours(1)))
            .build();
    Date startDate = dateOf(now.withDayOfMonth(1).minusMonths(2));

    Date endDate = dateOf(now.toLocalDate().plusDays(1)); // ended today
    Credit salesSoldCredit =
        Credit.builder()
            .type(CreditType.PREPAID_NDS)
            .startDate(startDate)
            .endDate(endDate)
            .amountRemainingCents(10L)
            .id(new ObjectId())
            .orgId(orgId)
            .build();
    save(salesSoldCredit);
    save(payment);

    assertTrue(noSelfServePaymentsOnSalesSoldOrgsAuditor.getIdsToAudit().contains(payment.getId()));
    Optional<AuditFailureDetail> failureOp =
        noSelfServePaymentsOnSalesSoldOrgsAuditor.audit(payment);
    assertTrue(failureOp.isPresent());
    AuditFailureDetail failureDetail = failureOp.get();
    assertEquals(
        BillingAuditErrorCode.SALES_SOLD_ORG_HAS_SELF_SERVE_PAYMENT,
        failureDetail.getBillingAuditErrors().get(0).getErrorCode());
  }

  @Test
  public void testAuditSuccess_expiredSalesSoldCredit() {
    LocalDateTime now = LocalDateTime.now();
    ObjectId orgId = new ObjectId();
    Payment payment =
        Payment.builder()
            .id(new ObjectId())
            .orgId(orgId)
            .paymentMethod(PaymentMethodType.CREDIT_CARD)
            .amountBilledCents(4L)
            .status(Status.CREATED)
            .created(dateOf(now.plusHours(1)))
            .build();
    Date startDate = dateOf(now.withDayOfMonth(1).minusMonths(2));

    Date endDate = dateOf(now.toLocalDate().plusDays(-20)); // ended today
    Credit salesSoldCredit =
        Credit.builder()
            .type(CreditType.PREPAID_NDS)
            .startDate(startDate)
            .endDate(endDate)
            .amountRemainingCents(10L)
            .id(new ObjectId())
            .orgId(orgId)
            .build();
    save(salesSoldCredit);
    save(payment);

    assertTrue(noSelfServePaymentsOnSalesSoldOrgsAuditor.getIdsToAudit().contains(payment.getId()));
    Optional<AuditFailureDetail> failureOp =
        noSelfServePaymentsOnSalesSoldOrgsAuditor.audit(payment);
    assertFalse(failureOp.isPresent());
  }

  @Test
  public void testAuditSuccess_noSalesSoldCredit() {
    LocalDateTime now = LocalDateTime.now();
    ObjectId orgId = new ObjectId();
    Payment payment =
        Payment.builder()
            .id(new ObjectId())
            .orgId(orgId)
            .paymentMethod(PaymentMethodType.CREDIT_CARD)
            .amountBilledCents(4L)
            .status(Status.CREATED)
            .created(dateOf(now.plusHours(1)))
            .build();
    save(payment);

    assertTrue(noSelfServePaymentsOnSalesSoldOrgsAuditor.getIdsToAudit().contains(payment.getId()));
    Optional<AuditFailureDetail> failureOp =
        noSelfServePaymentsOnSalesSoldOrgsAuditor.audit(payment);
    assertFalse(failureOp.isPresent());
  }
}
