package com.xgen.svc.mms.svc.explorer;

import static com.xgen.cloud.common.explorer._public.model.DataExplorerOpType.UPDATE_DOCUMENT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.model.explorer.Parameters;
import java.util.Date;
import org.junit.Before;
import org.junit.Test;

public class UpdateDocumentJobRequestIntTests extends JobRequestIntTest {
  private Group _group;
  private Organization _org;
  private AppUser _groupOwner;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithPremiumPlan();
    _org = _organizationSvc.findById(_group.getOrgId());
    _groupOwner = MmsFactory.createGroupOwnerUser(_group);

    MmsFactory.createAutomationAgentAuditEntry(_group, "hostname", "4.8.2.2491", new Date());
  }

  @Test
  public void testSubmitAndRetrieveDataExplorerRequest_simpleUpdateDocument() throws Exception {
    final HostCluster hostCluster = MmsFactory.createReplicaSet_V_2_4(_group, "replSet", true);
    final String updatedDoc = "{ \"_id\": \"blah\", \"a\" : {\"$numberInt\": \"1\"} }";
    final String options = "{\"writeConcern\": {\"w\": 1}, \"multi\": false}";

    final Parameters parameters =
        getBaseParameters(_group, _org, _groupOwner, hostCluster, null)
            .updatedDocument(updatedDoc)
            .options(options)
            .build();

    final UpdateDocumentJobRequest updateDocumentJobRequest =
        new UpdateDocumentJobRequest(_dataExplorerSvc, parameters);

    final BasicDBObject op =
        testGetAgentJob(
            updateDocumentJobRequest.submitJob(),
            null,
            hostCluster.getClusterId(),
            null,
            null, // testing against cluster
            _group);

    assertEquals(UPDATE_DOCUMENT.getName(), op.get("type"));
    assertNotNull(op.get("updatedDoc"));
    assertEquals(options, op.get("options"));

    _agentJobsProcessorDao.deleteByGroup(_group.getId());
  }
}
