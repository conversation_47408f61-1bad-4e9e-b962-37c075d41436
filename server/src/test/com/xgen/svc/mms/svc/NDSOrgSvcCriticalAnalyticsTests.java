package com.xgen.svc.mms.svc;

import static org.junit.Assert.assertEquals;

import com.xgen.cloud.criticalanalytics.CriticalAnalyticsFactory;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.svc.core.BaseSvcTest;
import jakarta.inject.Inject;
import org.junit.Before;
import org.junit.Test;

public class NDSOrgSvcCriticalAnalyticsTests extends BaseSvcTest {
  @Inject private CriticalAnalyticsFactory criticalAnalyticsFactory;
  @Inject private OrganizationSvc organizationSvc;

  @Before
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testCreateOrganizationInDatabase() throws Exception {
    final Organization org = criticalAnalyticsFactory.createOrganization();
    final Organization orgInDb = organizationSvc.findById(org.getId());

    assertEquals(org.getCreated(), orgInDb.getCreated());
    assertEquals(org.getCreator(), orgInDb.getCreator());
    assertEquals(org.getName(), orgInDb.getName());
    assertEquals(org.getGroupType(), orgInDb.getGroupType());
  }
}
