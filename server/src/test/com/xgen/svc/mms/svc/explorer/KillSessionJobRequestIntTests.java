package com.xgen.svc.mms.svc.explorer;

import static com.xgen.cloud.common.explorer._public.model.DataExplorerOpType.KILL_SESSION;
import static com.xgen.svc.common.MmsFactory.createGroupOwnerUser;
import static com.xgen.svc.common.MmsFactory.createGroupWithPremiumPlan;
import static com.xgen.svc.common.MmsFactory.createStandalone;
import static org.junit.Assert.assertEquals;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.model.explorer.Parameters;
import java.util.Date;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.junit.Before;
import org.junit.Test;

public class KillSessionJobRequestIntTests extends JobRequestIntTest {

  @Before
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testSubmitAndRetrieveDataExplorerRequest_simpleKillSession() throws Exception {

    // setup

    final Group group = createGroupWithPremiumPlan();
    final AppUser user = createGroupOwnerUser(group);
    final String hostId = createStandalone(group, "hostname", 27017);
    MmsFactory.createAutomationAgentAuditEntry(group, "hostname", "5.6.0.5506", new Date());

    final String query = "{\"lsid\": {\"id\": \"correct id\"}}";
    // TODO will cause issues
    final AuditInfo auditInfo = null;

    // Test with a host

    final BasicDBObject op =
        testKillSessionRequestAndResponse(user, group, query, auditInfo, null, hostId, null, null);

    assertEquals(KILL_SESSION.getName(), op.get("type"));
    assertEquals(query, op.get("query"));

    _agentJobsProcessorDao.deleteByGroup(group.getId());
  }

  private BasicDBObject testKillSessionRequestAndResponse(
      final AppUser pUser,
      final Group pGroup,
      final String pQuery,
      final AuditInfo auditInfo,
      final ObjectId pBackingGroupId,
      final String pHostId,
      final ObjectId pClusterId,
      final String pBackingHostname)
      throws JSONException, SvcException {

    // submit request
    final Organization org = _organizationSvc.findById(pGroup.getOrgId());
    final Host host = pHostId != null ? _hostSvc.findHostById(pHostId, pGroup.getId()) : null;
    final HostCluster hostCluster =
        pClusterId != null
            ? _hostClusterLifecycleSvc.findHostClusterByClusterId(pGroup.getId(), pClusterId, false)
            : null;

    final Parameters parameters =
        getBaseParameters(pGroup, org, pUser, hostCluster, host)
            .query(pQuery)
            .requestType(KILL_SESSION)
            .auditInfo(auditInfo)
            .build();

    final KillSessionJobRequest killSessionJobRequest =
        new KillSessionJobRequest(_dataExplorerSvc, parameters);

    return testGetAgentJob(
        killSessionJobRequest.submitJob(),
        pBackingGroupId,
        pClusterId,
        pBackingHostname,
        host,
        pGroup);
  }
}
