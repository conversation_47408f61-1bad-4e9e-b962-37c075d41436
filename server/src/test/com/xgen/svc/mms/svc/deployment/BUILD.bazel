load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = False,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:junit_junit",
    ],
)
