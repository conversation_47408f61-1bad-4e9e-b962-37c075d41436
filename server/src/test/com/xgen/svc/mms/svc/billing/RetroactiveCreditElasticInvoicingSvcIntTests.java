package com.xgen.svc.mms.svc.billing;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;

import com.xgen.cloud.billing._public.svc.IAccountantSvc;
import com.xgen.cloud.billing._public.svc.marketing.exception.MarketingIntegrationException;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Status;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.ApplyActivationCodeRequest;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.LineItemData;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.SalesforceOpportunity;
import com.xgen.svc.mms.model.billing.TaxResult;
import com.xgen.svc.mms.svc.billing.RetroactiveCreditSvc.PaymentsRefundMode;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.svc.mms.util.billing.testFactories.CreateInvoiceParams;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.PaymentFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.UsageFactory;
import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

public class RetroactiveCreditElasticInvoicingSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private RetroactiveCreditForElasticInvoicingSvc _retroactiveCreditForElasticInvoicingSvc;

  @Inject private PaymentDao _paymentDao;

  @Inject private InvoiceSvc invoiceSvc;

  @Inject private CreditDao _creditDao;
  @Inject private LineItemDao lineItemDao;

  @Inject private OrganizationFactory _organizationFactory;

  @Inject private InvoiceFactory _invoiceFactory;

  @Inject private CreditFactory _creditFactory;

  @Inject private SalesforceOpportunityFactory salesforceOpportunityFactory;

  @Inject private UsageFactory usageFactory;

  @Inject private IAccountantSvc accountantSvc;

  @Inject private SalesSoldDealActivationSvc salesSoldDealActivationSvc;

  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;

  @Inject private PaymentFactory paymentFactory;
  @Inject private JobQueueTestUtils jobQueueTestUtils;
  @Inject private RefundProcessingClient refundProcessingClient;
  @Inject private PaymentMethodStubber paymentMethodStubber;
  private Organization org;
  private Invoice fourMonthsAgoInvoice;
  private Invoice threeMonthsAgoInvoice;
  private Invoice twoMonthsAgoInvoice;
  private Invoice previousMonthInvoice;
  private Invoice currentMonthInvoice;

  @BeforeEach
  public void setup() throws Exception {
    salesforceProductCodeDao.syncDocuments();

    doAnswer(
            invocation -> {
              ObjectId paymentId = invocation.getArgument(0);
              _paymentDao.setPaymentStatus(paymentId, Payment.Status.REFUNDED, new Date());
              return RefundPaymentResult.success();
            })
        .when(refundProcessingClient)
        .fullRefundPaymentWithoutForgivingCharges(any(), any(), any(), any());
  }

  private Organization setupScenario(
      PaymentMethodType pPaidPaymentMethodType,
      boolean pHasFailedPayment,
      PaymentMethodType pFailedPaymentMethodType) {
    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus1Month = DateUtils.addMonths(startOfMonth, -1);
    Date minus2Months = DateUtils.addMonths(startOfMonth, -2);
    Date minus3Months = DateUtils.addMonths(startOfMonth, -3);
    Date minus4Months = DateUtils.addMonths(startOfMonth, -4);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    org = _organizationFactory.createAtlasOrganization(minus5Months, false);
    paymentMethodStubber.stubPaymentMethod(org.getId());

    fourMonthsAgoInvoice =
        _invoiceFactory
            .createInvoiceWithPayment(
                minus4Months,
                minus3Months,
                org.getId(),
                0,
                25000,
                0,
                Status.CLOSED,
                Payment.Status.FAILED,
                null)
            .getLeft();

    Pair<Invoice, Payment> invoicePaymentPair1 =
        _invoiceFactory.createInvoiceWithPayment(
            minus3Months,
            minus2Months,
            org.getId(),
            // if the payment method type is credit-consumption based we need to cover the
            // entire payment range with credit
            pFailedPaymentMethodType.isSalesSold() ? 25200 : 0,
            25100,
            100,
            pHasFailedPayment ? Status.CHARGE_FAILED : Status.FORGIVEN,
            pHasFailedPayment ? Payment.Status.FAILED : Payment.Status.FORGIVEN,
            new PaymentMethod.Builder().type(pFailedPaymentMethodType).build());
    threeMonthsAgoInvoice = invoicePaymentPair1.getLeft();

    Pair<Invoice, Payment> invoicePaymentPair =
        _invoiceFactory.createInvoiceWithPayment(
            minus2Months,
            minus1Month,
            org.getId(),
            // if the payment method type is credit-consumption based we need to cover the
            // entire payment range with credit
            pPaidPaymentMethodType == PaymentMethodType.AWS_MARKETPLACE_SELF_SERVE
                    || pPaidPaymentMethodType.isSalesSold()
                ? 25700
                : 0,
            25600,
            100,
            Status.PAID,
            Payment.Status.PAID,
            new PaymentMethod.Builder().type(pPaidPaymentMethodType).build());
    twoMonthsAgoInvoice = invoicePaymentPair.getLeft();

    previousMonthInvoice =
        _invoiceFactory
            .createInvoiceWithPayment(
                minus1Month,
                startOfMonth,
                org.getId(),
                0,
                24210,
                100,
                Status.CLOSED,
                Payment.Status.CREATED,
                null)
            .getLeft();

    currentMonthInvoice =
        _invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .hasPartialPayment(true)
                .startDate(startOfMonth)
                .endDate(nextMonth)
                .organizationId(org.getId())
                .usageAmountCents(200L)
                .invoiceStatus(Status.PENDING)
                .build());

    return org;
  }

  @Test
  public void
      testMultiplePayments_refundsAllExceptAmountThatCantBeCovered_paymentThatCantBeCoveredInMiddle()
          throws SvcException {
    Date startOfMonth = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -1);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Organization org = _organizationFactory.createAtlasOrganization(startOfMonth, true);
    Invoice invoice = invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());
    paymentMethodStubber.stubPaymentMethod(org.getId());
    _invoiceFactory.applyUsageToInvoice(
        invoice,
        startOfMonth,
        nextMonth,
        8000,
        0,
        Optional.empty(),
        true,
        Payment.Status.FAILED,
        new Date());
    Payment payment1 = _paymentDao.findByInvoiceId(invoice.getId(), true).get(0);

    Payment payment2 =
        paymentFactory.createPayment(
            invoice,
            DateUtils.addDays(new Date(), 1),
            TaxResult.NO_TAX,
            0,
            payment1.getMaxLineItemId(),
            PaymentMethodType.CREDIT_CARD,
            Payment.Status.PAID);

    invoiceSvc.updateInvoiceStatus(invoice.getId(), Status.CLOSED);

    Credit credit =
        _creditFactory.createPrepaidCredit(
            org.getId(), startOfMonth, DateUtils.addYears(startOfMonth, 1), 2000, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    Payment payment1Updated = _paymentDao.findById(payment1.getId());
    Payment payment2Updated = _paymentDao.findById(payment2.getId());

    assertEquals(Payment.Status.PAID, payment2Updated.getStatus());
    assertEquals(Payment.Status.CANCELLED, payment1Updated.getStatus());

    List<Payment> invoicePayments = _paymentDao.findByInvoiceId(invoice.getId(), true);
    assertEquals(3, invoicePayments.size());

    Payment elasticPayment =
        invoicePayments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .findFirst()
            .get();
    Payment selfServePaymentCancelled =
        invoicePayments.stream()
            .filter((p) -> p.getStatus() == Payment.Status.CANCELLED)
            .findFirst()
            .get();

    Payment selfServePaymentPaid =
        invoicePayments.stream()
            .filter(
                (p) ->
                    p.getPaymentMethod() == PaymentMethodType.CREDIT_CARD
                        && p.getStatus() == Payment.Status.PAID)
            .findFirst()
            .get();

    long totalSubtotal = lineItemDao.getInvoiceSubtotal(invoice.getId(), null);

    long expectedElasticPayment =
        totalSubtotal - payment2.getAmountBilledCents() - credit.getAmountCents();
    assertEquals(Payment.Status.CREATED, elasticPayment.getStatus());

    assertEquals(elasticPayment.getAmountBilledCents(), expectedElasticPayment);
    assertTrue(elasticPayment.getAmountBilledCents() > 0);
    assertTrue(elasticPayment.getAmountBilledCents() < totalSubtotal);
  }

  @Test
  public void testMultiplePayments() throws SvcException {
    Date startOfMonth = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -1);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Organization org = _organizationFactory.createAtlasOrganization(startOfMonth, true);
    Invoice invoice = invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());
    paymentMethodStubber.stubPaymentMethod(org.getId());

    _invoiceFactory.applyUsageToInvoice(
        invoice,
        startOfMonth,
        nextMonth,
        8000,
        0,
        Optional.empty(),
        true,
        Payment.Status.FAILED,
        new Date());
    Payment payment1 = _paymentDao.findByInvoiceId(invoice.getId(), true).get(0);
    List<Payment> payments = _paymentDao.findByInvoiceId(invoice.getId(), true);
  }

  @Test
  public void testApplyCreditsRetroactivelyWithEnoughCreditToRefund() throws Exception {
    setupScenario(PaymentMethodType.CREDIT_CARD, false, PaymentMethodType.CREDIT_CARD);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    Credit credit =
        _creditFactory.createMonthlyCommitmentCredit(
            org.getId(), minus5Months, nextMonth, 27600, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    List<Payment> fourMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(fourMonthsAgoInvoice.getId(), true);
    List<Payment> threeMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(threeMonthsAgoInvoice.getId(), true);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);
    List<Payment> previousInvoicePayments =
        _paymentDao.findByInvoiceId(previousMonthInvoice.getId(), true);
    previousInvoicePayments.sort(Comparator.comparing(Payment::getId));
    List<Payment> currentInvoicePayments =
        _paymentDao.findByInvoiceId(currentMonthInvoice.getId(), true);

    assertThat(fourMonthsAgoInvoicePayments.size(), is(1));
    assertThat(fourMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FAILED));
    // payment shouldnt be touched because it is outside the 3 month window

    assertThat(threeMonthsAgoInvoicePayments.size(), is(1));
    assertThat(threeMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FORGIVEN));

    // PAID -> REFUNDED (refunded because there was enough credit to cover)
    assertThat(twoMonthsAgoInvoicePayments.size(), is(1));
    assertThat(twoMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.REFUNDED));

    // CREATED -> CANCELLED unpaid payment cancelled
    assertThat(previousInvoicePayments.size(), is(2));
    assertThat(previousInvoicePayments.get(0).getStatus(), is(Payment.Status.CANCELLED));
    // usage beyond credit amount invoiced elastically
    assertThat(previousInvoicePayments.get(1).getPaymentMethod(), is(PaymentMethodType.INVOICE));
    assertThat(previousInvoicePayments.get(1).getAmountBilledCents(), is(22410l));

    assertThat(currentInvoicePayments.size(), is(1));
    assertThat(
        currentInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CREATED)); // pending invoice isnt touched

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertThat(updatedCredit.getAmountRemainingCents(), is(-22410l));
  }

  @Test
  public void testApplyCreditsRetroactivelyWithoutEnoughCreditToRefund() throws Exception {
    setupScenario(PaymentMethodType.CREDIT_CARD, false, PaymentMethodType.CREDIT_CARD);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    Credit credit =
        _creditFactory.createMonthlyCommitmentCredit(
            org.getId(), minus5Months, nextMonth, 24210, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    List<Payment> fourMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(fourMonthsAgoInvoice.getId(), true);
    List<Payment> threeMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(threeMonthsAgoInvoice.getId(), true);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);
    List<Payment> previousMonthInvoicePayments =
        _paymentDao.findByInvoiceId(previousMonthInvoice.getId(), true);
    previousMonthInvoicePayments.sort(Comparator.comparing(Payment::getId));
    List<Payment> currentMonthInvoicePayments =
        _paymentDao.findByInvoiceId(currentMonthInvoice.getId(), true);

    assertThat(fourMonthsAgoInvoicePayments.size(), is(1));
    assertThat(fourMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FAILED));
    // payment shouldnt be touched because it is outside the 3 month window

    assertThat(threeMonthsAgoInvoicePayments.size(), is(1));
    assertThat(threeMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FORGIVEN));

    assertThat(twoMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        twoMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.PAID)); // not refunded (not enough credit)

    // ran out of credits while covering CREATED payment. CREATED payment marked as cancelled
    assertThat(previousMonthInvoicePayments.size(), is(2));
    assertThat(previousMonthInvoicePayments.get(0).getStatus(), is(Payment.Status.CANCELLED));
    // elastic payment issued for remaining uncovered usage
    assertThat(
        previousMonthInvoicePayments.get(1).getPaymentMethod(), is(PaymentMethodType.INVOICE));
    assertThat(previousMonthInvoicePayments.get(1).getAmountBilledCents(), is(100l));

    assertThat(currentMonthInvoicePayments.size(), is(1));
    assertThat(
        currentMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CREATED)); // pending invoice isnt touched

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertThat(updatedCredit.getAmountRemainingCents(), is(-100l));
  }

  @Test
  public void testApplyCreditsRetroactivelyWithFailedPayment() throws Exception {
    setupScenario(PaymentMethodType.CREDIT_CARD, true, PaymentMethodType.CREDIT_CARD);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    Credit credit =
        _creditFactory.createMonthlyCommitmentCredit(
            org.getId(), minus5Months, nextMonth, 25200 + 25700 + 24310 + 100, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    List<Payment> fourMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(fourMonthsAgoInvoice.getId(), true);
    List<Payment> threeMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(threeMonthsAgoInvoice.getId(), true);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);
    List<Payment> previousMonthInvoicePayments =
        _paymentDao.findByInvoiceId(previousMonthInvoice.getId(), true);
    List<Payment> currentMonthInvoicePayments =
        _paymentDao.findByInvoiceId(currentMonthInvoice.getId(), true);

    assertThat(fourMonthsAgoInvoicePayments.size(), is(1));
    assertThat(fourMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FAILED));
    // payment shouldnt be touched because it is outside the 3 month window

    assertThat(threeMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        threeMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // FAILED -> CANCELLED (unpaid payment cancelled)

    assertThat(twoMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        twoMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.REFUNDED)); // PAID -> REFUNDED

    assertThat(previousMonthInvoicePayments.size(), is(1));
    assertThat(
        previousMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // CREATED -> CANCELLED (covered with credit).

    assertThat(currentMonthInvoicePayments.size(), is(1));
    assertThat(
        currentMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CREATED)); // pending invoice isnt touched

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertThat(updatedCredit.getAmountRemainingCents(), is(100l));
  }

  @Test
  public void testApplyCreditsRetroactively_doesNotRefundAwsSelfServePayment() throws Exception {
    setupScenario(PaymentMethodType.AWS_MARKETPLACE_SELF_SERVE, true, PaymentMethodType.PAYPAL);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    Credit credit =
        _creditFactory.createMonthlyCommitmentCredit(
            org.getId(), minus5Months, nextMonth, 25200 + 25700 + 24310 + 100, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    List<Payment> fourMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(fourMonthsAgoInvoice.getId(), true);
    List<Payment> threeMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(threeMonthsAgoInvoice.getId(), true);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);
    List<Payment> previousMonthInvoicePayments =
        _paymentDao.findByInvoiceId(previousMonthInvoice.getId(), true);
    List<Payment> currentMonthInvoicePayments =
        _paymentDao.findByInvoiceId(currentMonthInvoice.getId(), true);

    assertThat(fourMonthsAgoInvoicePayments.size(), is(1));
    assertThat(fourMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FAILED));
    // payment shouldnt be touched because it is outside the 3 month window

    assertThat(threeMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        threeMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // failed PAYPAL payment should be canceled

    assertThat(twoMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        twoMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.PAID)); // PAID payment should not be touched

    assertThat(previousMonthInvoicePayments.size(), is(1));
    assertThat(
        previousMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // CREATED -> CANCELLED (covered with credit).

    assertThat(currentMonthInvoicePayments.size(), is(1));
    assertThat(
        currentMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CREATED)); // pending invoice isnt touched

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertThat(
        updatedCredit.getAmountUsedCents(),
        is(
            threeMonthsAgoInvoicePayments.get(0).getAmountBilledCents()
                + previousMonthInvoicePayments.get(0).getAmountBilledCents()));
  }

  @Test
  public void testApplyCreditsRetroactively_doesNotRefundOrCancelSalesSoldPayment()
      throws Exception {
    setupScenario(
        PaymentMethodType.MONTHLY_COMMITMENT, true, PaymentMethodType.AWS_MARKETPLACE_INVOICE);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    Credit credit =
        _creditFactory.createMonthlyCommitmentCredit(
            org.getId(), minus5Months, nextMonth, 25200 + 25700 + 24310 + 100, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    List<Payment> fourMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(fourMonthsAgoInvoice.getId(), true);
    List<Payment> threeMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(threeMonthsAgoInvoice.getId(), true);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);
    List<Payment> previousMonthInvoicePayments =
        _paymentDao.findByInvoiceId(previousMonthInvoice.getId(), true);
    List<Payment> currentMonthInvoicePayments =
        _paymentDao.findByInvoiceId(currentMonthInvoice.getId(), true);

    assertThat(fourMonthsAgoInvoicePayments.size(), is(1));
    assertThat(fourMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FAILED));
    // payment shouldnt be touched because it is outside the 3-month window

    assertThat(threeMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        threeMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.FAILED)); // failed payment should not be canceled

    assertThat(twoMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        twoMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.PAID)); // PAID payment should not be touched

    assertThat(previousMonthInvoicePayments.size(), is(1));
    assertThat(
        previousMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // CREATED -> CANCELLED (covered with credit).

    assertThat(currentMonthInvoicePayments.size(), is(1));
    assertThat(
        currentMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CREATED)); // pending invoice isn't touched

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertThat(updatedCredit.getAmountRemainingCents(), is(51000L));
  }

  @Test
  public void testApplyCreditsRetroactivelyWithZeroDollarGCPCredit() throws Exception {
    setupScenario(PaymentMethodType.CREDIT_CARD, true, PaymentMethodType.CREDIT_CARD);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus5Months = DateUtils.addMonths(startOfMonth, -5);

    Credit credit =
        _creditFactory.createGcpMarketplaceZeroDollarElasticCredit(
            org.getId(), minus5Months, nextMonth);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), new Date(), true, AuditInfoHelpers.fromSystem());

    List<Payment> fourMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(fourMonthsAgoInvoice.getId(), true);
    List<Payment> threeMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(threeMonthsAgoInvoice.getId(), true);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);
    List<Payment> previousMonthInvoicePayments =
        _paymentDao.findByInvoiceId(previousMonthInvoice.getId(), true);
    previousMonthInvoicePayments.sort(Comparator.comparing(Payment::getId));
    List<Payment> currentMonthInvoicePayments =
        _paymentDao.findByInvoiceId(currentMonthInvoice.getId(), true);

    assertThat(fourMonthsAgoInvoicePayments.size(), is(1));
    assertThat(fourMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.FAILED));
    // payment shouldnt be touched because it is outside the 3 month window

    assertThat(threeMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        threeMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // FAILED -> CANCELLED (unpaid payment cancelled)

    assertThat(twoMonthsAgoInvoicePayments.size(), is(1));
    assertThat(
        twoMonthsAgoInvoicePayments.get(0).getStatus(),
        is(Payment.Status.PAID)); // PAID -> should not be touched - $0 credit

    assertThat(previousMonthInvoicePayments.size(), is(1));
    assertThat(
        previousMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CANCELLED)); // CREATED -> CANCELLED (covered with credit).

    assertThat(currentMonthInvoicePayments.size(), is(1));
    assertThat(
        currentMonthInvoicePayments.get(0).getStatus(),
        is(Payment.Status.CREATED)); // pending invoice isnt touched
  }

  @Test
  public void testApplyCreditsForClosedInvoiceWithGCPpayment() throws Exception {
    //    setupScenario(PaymentMethodType.AWS_MARKETPLACE_SELF_SERVE, false,
    // PaymentMethodType.AWS_MARKETPLACE_SELF_SERVE);

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus1Month = DateUtils.addMonths(startOfMonth, -1);
    Date minus2Months = DateUtils.addMonths(startOfMonth, -2);
    org = _organizationFactory.createAtlasOrganization(minus2Months, false);
    paymentMethodStubber.stubPaymentMethod(org.getId());

    Credit credit =
        _creditFactory.createGcpMarketplaceZeroDollarElasticCredit(
            org.getId(), minus2Months, nextMonth);
    AuditInfo auditInfo = mock(AuditInfo.class);

    twoMonthsAgoInvoice =
        _invoiceFactory
            .createInvoiceWithPayment(
                minus2Months,
                minus1Month,
                org.getId(),
                25700,
                25600,
                100,
                Status.CLOSED,
                Payment.Status.CREATED,
                new PaymentMethod.Builder()
                    .type(PaymentMethodType.AWS_MARKETPLACE_SELF_SERVE)
                    .build())
            .getLeft();

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsForClosedInvoice(
        twoMonthsAgoInvoice, List.of(credit.getId()), PaymentsRefundMode.NONE, auditInfo);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);

    assertThat(twoMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.CREATED));
  }

  @Test
  public void
      testApplyCreditsForClosedInvoiceWithProcessingPayment_paymentTransitionedIntoPendingReversal()
          throws Exception {

    Date startOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus1Month = DateUtils.addMonths(startOfMonth, -1);
    Date minus2Months = DateUtils.addMonths(startOfMonth, -2);
    org = _organizationFactory.createAtlasOrganization(minus2Months, false);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Credit credit =
        _creditFactory.createGcpMarketplaceZeroDollarElasticCredit(
            org.getId(), minus2Months, nextMonth);
    AuditInfo auditInfo = mock(AuditInfo.class);

    twoMonthsAgoInvoice =
        _invoiceFactory
            .createInvoiceWithPayment(
                minus2Months,
                minus1Month,
                org.getId(),
                25700,
                25600,
                100,
                Status.CLOSED,
                Payment.Status.PROCESSING,
                new PaymentMethod.Builder().type(PaymentMethodType.CREDIT_CARD).build())
            .getLeft();

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsForClosedInvoice(
        twoMonthsAgoInvoice, List.of(credit.getId()), PaymentsRefundMode.NONE, auditInfo);
    List<Payment> twoMonthsAgoInvoicePayments =
        _paymentDao.findByInvoiceId(twoMonthsAgoInvoice.getId(), true);

    assertThat(twoMonthsAgoInvoicePayments.get(0).getStatus(), is(Payment.Status.PENDING_REVERSAL));
  }

  @Test
  @Disabled
  public void
      testApplyCreditsForClosedInvoice_zeroDollarDollarDirectElastic_hasFailedPayments_postNegativeDirect()
          throws SvcException, MarketingIntegrationException {
    LocalDateTime now = LocalDateTime.now().withYear(2024).withMonth(10).withDayOfMonth(15);

    mockClockTime(now);

    Invoice retroInvoice =
        testApplyCreditsForClosedInvoice_zeroDollarDollarDirectElastic_hasFailedPayments(now);

    long invoiceTotal = lineItemDao.getInvoiceAmountBilledCents(retroInvoice.getId(), null);
    assertEquals(0, invoiceTotal);
  }

  public Invoice testApplyCreditsForClosedInvoice_zeroDollarDollarDirectElastic_hasFailedPayments(
      LocalDateTime now) throws SvcException, MarketingIntegrationException {
    LocalDateTime startOfThisMonth = now.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
    LocalDateTime startOf1MonthAgo = startOfThisMonth.minusMonths(1);

    Organization org = _organizationFactory.createAtlasOrganization(dateOf(startOf1MonthAgo), true);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Group group = _organizationFactory.createNdsGroup(org, dateOf(startOf1MonthAgo));
    long usageCents =
        usageFactory.generateHourlyAWSSubscriptionUsage(
            new ObjectId(),
            group.getId(),
            AWSRegionName.AP_EAST_1,
            AWSNDSInstanceSize.M60,
            1,
            dateOf(startOf1MonthAgo),
            dateOf(startOf1MonthAgo.plusDays(14)),
            true);

    // bill to the 15th of 1 month ago
    LocalDateTime billToDate = startOf1MonthAgo.plusDays(15).withHour(5);
    accountantSvc.processOrganizations(List.of(org.getId()), dateOf(billToDate));

    Invoice oneMonthAgoInvoice =
        invoiceSvc.findMonthlyByOrgIdForDate(org.getId(), dateOf(billToDate));
    // close the pending invoice from 1 month ago, this should generate a self serve payment.
    billToDate = startOf1MonthAgo.plusMonths(1).plusDays(2).withHour(5);
    accountantSvc.processOrganizations(List.of(org.getId()), dateOf(billToDate));

    // validate the invoice was closed with 1 self serve payment on it.
    oneMonthAgoInvoice = invoiceSvc.findById(oneMonthAgoInvoice.getId());
    assertEquals(Status.CLOSED, oneMonthAgoInvoice.getStatus());

    List<Payment> payments = _paymentDao.findByInvoiceId(oneMonthAgoInvoice.getId(), true);
    assertEquals(1, payments.size());
    Payment oneMonthAgoPayment1 = payments.get(0);
    assertTrue(oneMonthAgoInvoice.getSubtotalCents() > 0);
    assertEquals(oneMonthAgoInvoice.getSubtotalCents(), oneMonthAgoPayment1.getAmountBilledCents());
    _paymentDao.setPaymentStatus(
        oneMonthAgoPayment1.getId(), Payment.Status.FAILED, dateOf(billToDate));

    // Setup a zero dollar elastic opportunity and activate it.
    SalesforceOpportunity opp =
        salesforceOpportunityFactory.insertPrepaidOpportunity(
            null, dateOf(startOf1MonthAgo), dateOf(startOf1MonthAgo.plusYears(1)), false, 0, true);

    salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org,
            opp.getLicenseKey(),
            dateOf(billToDate),
            null,
            AuditInfoHelpers.fromSystem(),
            true));
    Credit credit = _creditDao.findByActivationCode(opp.getLicenseKey()).get(0);

    // verify payment was cancelled and elastic invoice payment was created
    List<Payment> postRetroPayments = _paymentDao.findByInvoiceId(oneMonthAgoInvoice.getId(), true);
    assertEquals(2, postRetroPayments.size());
    Payment oneMonthAgoPayment1RetroSelfServe =
        postRetroPayments.stream()
            .filter(p -> p.getPaymentMethod() == PaymentMethodType.CREDIT_CARD)
            .findFirst()
            .get();
    assertEquals(usageCents, oneMonthAgoPayment1RetroSelfServe.getAmountBilledCents());
    assertEquals(Payment.Status.CANCELLED, oneMonthAgoPayment1RetroSelfServe.getStatus());

    Payment oneMonthAgoPayment1RetroElastic =
        postRetroPayments.stream()
            .filter(p -> p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .findFirst()
            .get();
    assertEquals(usageCents, oneMonthAgoPayment1RetroElastic.getAmountBilledCents());
    assertEquals(Payment.Status.CREATED, oneMonthAgoPayment1RetroElastic.getStatus());
    assertEquals(credit.getId(), oneMonthAgoPayment1RetroElastic.getCreditId());

    return oneMonthAgoInvoice;
  }

  @Test
  public void
      testApplyCreditsForClosedInvoice_zeroDollarDollarDirectElastic_hasFailedPaymentsAndInvoicedPayment_postNegativeRollout()
          throws SvcException, MarketingIntegrationException {
    // this test validates that we do not create a duplicate payment for an invoice that already
    // contains an elastic payment on it.
    LocalDateTime now = LocalDateTime.now().withYear(2024).withMonth(8).withDayOfMonth(15);
    Invoice retroInvoice =
        testApplyCreditsForClosedInvoice_zeroDollarDollarDirectElastic_hasInvoicedPayment(now);

    long invoiceTotal = lineItemDao.getInvoiceAmountBilledCents(retroInvoice.getId(), null);
    assertEquals(0, invoiceTotal);
  }

  public Invoice testApplyCreditsForClosedInvoice_zeroDollarDollarDirectElastic_hasInvoicedPayment(
      LocalDateTime now) throws SvcException, MarketingIntegrationException {
    LocalDateTime startOfThisMonth = now.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
    LocalDateTime startOf1MonthAgo = startOfThisMonth.minusMonths(1);

    Organization org = _organizationFactory.createAtlasOrganization(dateOf(startOf1MonthAgo), true);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Group group = _organizationFactory.createNdsGroup(org, dateOf(startOfThisMonth));
    long usageCents =
        usageFactory.generateHourlyAWSSubscriptionUsage(
            new ObjectId(),
            group.getId(),
            AWSRegionName.AP_EAST_1,
            AWSNDSInstanceSize.M60,
            1,
            dateOf(startOfThisMonth),
            dateOf(startOfThisMonth.plusDays(14)),
            true);
    // Setup a zero dollar elastic opportunity and activate it.
    SalesforceOpportunity opp =
        salesforceOpportunityFactory.insertPrepaidOpportunity(
            null, dateOf(startOfThisMonth), dateOf(startOfThisMonth.plusYears(1)), false, 0, true);

    // bill to the 15th of this month
    LocalDateTime billToDate = startOfThisMonth.plusDays(15).withHour(5);
    accountantSvc.processOrganizations(List.of(org.getId()), dateOf(billToDate));

    // apply a 0$ elastic credit on the date  we billed to above
    salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org,
            opp.getLicenseKey(),
            dateOf(billToDate),
            null,
            AuditInfoHelpers.fromSystem(),
            true));
    // Trigger rebill
    jobQueueTestUtils.processAllJobsInQueue();
    Credit creditOpp1 = _creditDao.findByActivationCode(opp.getLicenseKey()).get(0);

    Invoice thisMonthInvoice =
        invoiceSvc.findMonthlyByOrgIdForDate(org.getId(), dateOf(billToDate));

    // close the pending invoice, this should generate a direct elastic payment.
    billToDate = startOfThisMonth.plusMonths(1).plusDays(2).withHour(5);
    accountantSvc.processOrganizations(List.of(org.getId()), dateOf(billToDate));

    // validate the invoice was closed with 1 self serve payment on it.
    thisMonthInvoice = invoiceSvc.findById(thisMonthInvoice.getId());
    assertEquals(Status.CLOSED, thisMonthInvoice.getStatus());

    List<Payment> payments = _paymentDao.findByInvoiceId(thisMonthInvoice.getId(), true);
    assertEquals(1, payments.size());
    Payment thisMonthInvoicePayment = payments.get(0);
    assertTrue(thisMonthInvoice.getSubtotalCents() > 0);
    assertEquals(PaymentMethodType.INVOICE, thisMonthInvoicePayment.getPaymentMethod());
    assertEquals(
        thisMonthInvoice.getSubtotalCents(), thisMonthInvoicePayment.getAmountBilledCents());

    // create a new opp, that overlaps with the first, and apply it in the 2nd month
    SalesforceOpportunity opp2 =
        salesforceOpportunityFactory.insertPrepaidOpportunity(
            null, dateOf(startOfThisMonth), dateOf(startOfThisMonth.plusYears(1)), false, 0, true);

    salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org,
            opp2.getLicenseKey(),
            dateOf(billToDate),
            null,
            AuditInfoHelpers.fromSystem(),
            true));
    Credit creditOpp2 = _creditDao.findByActivationCode(opp2.getLicenseKey()).get(0);

    // verify the INVOICED payment from opp1 was not touched
    List<Payment> postRetroPayments = _paymentDao.findByInvoiceId(thisMonthInvoice.getId(), true);
    assertEquals(1, postRetroPayments.size());
    Payment opp1InvoicePayment = postRetroPayments.get(0);
    assertEquals(usageCents, opp1InvoicePayment.getAmountBilledCents());
    assertEquals(Payment.Status.CREATED, opp1InvoicePayment.getStatus());
    assertEquals(creditOpp1.getId(), opp1InvoicePayment.getCreditId());
    assertEquals(PaymentMethodType.INVOICE, opp1InvoicePayment.getPaymentMethod());
    assertEquals(thisMonthInvoicePayment.getId(), opp1InvoicePayment.getId());

    // validate only credit1 has line item data on the closed invoice.
    List<LineItemData> lineItemData =
        lineItemDao.getElasticCreditUsageOnInvoice(
            opp1InvoicePayment.getInvoiceId(), Set.of(creditOpp1.getId(), creditOpp2.getId()));

    assertEquals(1, lineItemData.size());
    assertEquals(lineItemData.get(0).getCreditId(), creditOpp1.getId());
    assertEquals(lineItemData.get(0).getSubtotalCents(), opp1InvoicePayment.getAmountBilledCents());
    assertEquals(lineItemData.get(0).getSubtotalCents(), opp1InvoicePayment.getSubtotalCents());

    return thisMonthInvoice;
  }

  @Test
  public void invoiceWithLaggingUsage_laggingUsageIsSkipped() throws SvcException {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date minus1Month = DateUtils.addMonths(startOfMonth, -1);

    org = _organizationFactory.createAtlasOrganization(minus1Month, true);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Invoice invoice =
        _invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(minus1Month)
                .endDate(startOfMonth)
                .organizationId(org.getId())
                .usageAmountCents(1000L)
                .hasLaggingUsage(true)
                .invoiceStatus(Status.CLOSED)
                .build());
    Payment payment =
        _invoiceFactory.createPaymentForInvoice(
            invoice, Payment.Status.CREATED, null, TaxResult.NO_TAX);

    Credit credit =
        _creditFactory.createPrepaidCredit(org.getId(), minus1Month, startOfNextMonth, 0L, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit), now, true, AuditInfoHelpers.fromSystem());

    long laggingUsage = lineItemDao.computeTotalBeforeDate(invoice.getId(), minus1Month, false);
    assertThat(laggingUsage, greaterThan(0L));

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertThat(updatedCredit.getAmountUsedCents(), is(1000L - laggingUsage));

    Payment updatedPayment = _paymentDao.findById(payment.getId());
    assertThat(updatedPayment.getStatus(), is(Payment.Status.CANCELLED));
  }

  @Test
  public void testApplyCreditsForClosedInvoice_forceRefund_singleCredit() throws SvcException {
    Date startOfMonth = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -1);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Organization org = _organizationFactory.createAtlasOrganization(startOfMonth, true);
    Invoice invoice = invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());
    paymentMethodStubber.stubPaymentMethod(org.getId());

    _invoiceFactory.applyUsageToInvoice(
        invoice,
        startOfMonth,
        nextMonth,
        8000,
        0,
        Optional.empty(),
        true,
        Payment.Status.FAILED,
        new Date());
    Payment payment1 = _paymentDao.findByInvoiceId(invoice.getId(), true).get(0);

    Payment payment2 =
        paymentFactory.createPayment(
            invoice,
            DateUtils.addDays(new Date(), 1),
            TaxResult.NO_TAX,
            0,
            payment1.getMaxLineItemId(),
            PaymentMethodType.CREDIT_CARD,
            Payment.Status.PAID);

    invoiceSvc.updateInvoiceStatus(invoice.getId(), Status.CLOSED);
    Invoice updatedInvoice = invoiceSvc.getInvoice(invoice.getId());

    Credit credit =
        _creditFactory.createPrepaidCredit(
            org.getId(), startOfMonth, DateUtils.addYears(startOfMonth, 1), 0, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit),
        List.of(updatedInvoice),
        PaymentsRefundMode.ALL,
        AuditInfoHelpers.fromSystem());

    Payment updatedPayment1 = _paymentDao.findById(payment1.getId());
    Payment updatedPayment2 = _paymentDao.findById(payment2.getId());

    assertEquals(Payment.Status.REFUNDED, updatedPayment2.getStatus());
    assertEquals(Payment.Status.CANCELLED, updatedPayment1.getStatus());

    List<Payment> invoicePayments = _paymentDao.findByInvoiceId(invoice.getId(), true);
    assertEquals(3, invoicePayments.size());

    Payment elasticPayment =
        invoicePayments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .findFirst()
            .orElseThrow();

    assertEquals(Payment.Status.CREATED, elasticPayment.getStatus());
    assertEquals(8000L, elasticPayment.getAmountBilledCents());

    Credit updatedCredit = _creditDao.findById(credit.getId());
    assertEquals(0L, updatedCredit.getAmountCents());
    assertEquals(8000L, updatedCredit.getTotalBilledCents());
    assertEquals(-8000L, updatedCredit.getAmountRemainingCents());
  }

  @Test
  public void testApplyCreditsForClosedInvoice_forceRefund_twoCredits() throws SvcException {
    Date startOfMonth = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -1);
    Date nextMonth = DateUtils.addMonths(startOfMonth, 1);
    Organization org = _organizationFactory.createAtlasOrganization(startOfMonth, true);
    Invoice invoice = invoiceSvc.getPendingMonthlyInvoiceByOrgId(org.getId());
    paymentMethodStubber.stubPaymentMethod(org.getId());

    _invoiceFactory.applyUsageToInvoice(
        invoice,
        startOfMonth,
        nextMonth,
        8000,
        0,
        Optional.empty(),
        true,
        Payment.Status.FAILED,
        new Date());
    Payment payment1 = _paymentDao.findByInvoiceId(invoice.getId(), true).get(0);

    Payment payment2 =
        paymentFactory.createPayment(
            invoice,
            DateUtils.addDays(new Date(), 1),
            TaxResult.NO_TAX,
            0,
            payment1.getMaxLineItemId(),
            PaymentMethodType.CREDIT_CARD,
            Payment.Status.PAID);

    invoiceSvc.updateInvoiceStatus(invoice.getId(), Status.CLOSED);
    Invoice updatedInvoice = invoiceSvc.getInvoice(invoice.getId());

    Credit credit1 =
        _creditFactory.createPrepaidCredit(
            org.getId(), startOfMonth, DateUtils.addYears(startOfMonth, 1), 0, true);
    Credit credit2 =
        _creditFactory.createPrepaidCredit(
            org.getId(), startOfMonth, DateUtils.addYears(startOfMonth, 2), 0, true);

    _retroactiveCreditForElasticInvoicingSvc.applyCreditsRetroactively(
        List.of(credit1, credit2),
        List.of(updatedInvoice),
        PaymentsRefundMode.ALL,
        AuditInfoHelpers.fromSystem());

    Payment updatedPayment1 = _paymentDao.findById(payment1.getId());
    Payment updatedPayment2 = _paymentDao.findById(payment2.getId());

    // force refund skipped
    assertEquals(Payment.Status.REFUNDED, updatedPayment2.getStatus());
    assertEquals(Payment.Status.CANCELLED, updatedPayment1.getStatus());

    List<Payment> invoicePayments = _paymentDao.findByInvoiceId(invoice.getId(), true);
    assertEquals(3, invoicePayments.size());

    Payment elasticPayment =
        invoicePayments.stream()
            .filter((p) -> p.getPaymentMethod() == PaymentMethodType.INVOICE)
            .findFirst()
            .orElseThrow();

    assertEquals(Payment.Status.CREATED, elasticPayment.getStatus());
    assertEquals(8000L, elasticPayment.getAmountBilledCents());

    Credit updatedCredit1 = _creditDao.findById(credit1.getId());
    assertEquals(0L, updatedCredit1.getAmountCents());
    assertEquals(0L, updatedCredit1.getTotalBilledCents());
    assertEquals(0L, updatedCredit1.getAmountRemainingCents());

    Credit updatedCredit2 = _creditDao.findById(credit2.getId());
    assertEquals(0L, updatedCredit2.getAmountCents());
    assertEquals(8000L, updatedCredit2.getTotalBilledCents());
    assertEquals(-8000L, updatedCredit2.getAmountRemainingCents());
  }
}
