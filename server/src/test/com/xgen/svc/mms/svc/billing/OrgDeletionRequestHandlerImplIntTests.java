package com.xgen.svc.mms.svc.billing;

import static com.xgen.cloud.billingplatform.process.invoicelocking._public.context.LockedInvoiceContextInterceptor.LOCKED_INVOICE_CONTEXT_KEY;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.process.invoicelocking._public.svc.LockedInvoiceSvc;
import com.xgen.cloud.billingplatform.process.invoicelocking._public.svc.LockedInvoiceSvc.LockParams;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.partners.usage.report._public.model.AzureUsageReport;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport.Status;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.standalone.partners.azure.constant._public.model.AzureDimension;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.PayingOrgDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.PaymentMethodMandate;
import com.xgen.svc.mms.svc.common.OrgErrorCode;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.FlexCommitCreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.FlexCommitCreditFactory.FlexCommitCreditRequest;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.PartnerUsageReportFactory;
import com.xgen.svc.mms.util.billing.testFactories.PaymentFactory;
import com.xgen.svc.mms.util.billing.testFactories.PaymentMethodFactory;
import io.grpc.Context;
import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class OrgDeletionRequestHandlerImplIntTests extends JUnit5BaseSvcTest {

  @Inject OrgDeletionRequestHandlerImpl orgDeletionRequestHandlerImpl;
  @Inject OrganizationFactory organizationFactory;
  @Inject InvoiceDao invoiceDao;
  @Inject CreditFactory creditFactory;
  @Inject CreditDao creditDao;
  @Inject PayingOrgDao payingOrgDao;
  @Inject PaymentFactory paymentFactory;
  @Inject PaymentDao paymentDao;
  @Inject PartnerUsageReportFactory usageReportFactory;
  @Inject PaymentMethodFactory paymentMethodFactory;
  @Inject PaymentMethodSvc paymentMethodSvc;
  @Inject FlexCommitCreditFactory flexCommitCreditFactory;
  @Inject LockedInvoiceSvc lockedInvoiceSvc;
  @Inject PaymentMethodStubber paymentMethodStubber;
  @Inject VercelInstallationSvc vercelInstallationSvc;

  @Test
  public void test_validationSucceeds() throws SvcException {
    Date now = new Date();
    Organization org = organizationFactory.createAtlasOrganization(now);

    orgDeletionRequestHandlerImpl.validate(org, now);
  }

  @Test
  public void test_validation_requires_unlocked_invoice() {
    Date now = new Date();
    Organization org = organizationFactory.createAtlasOrganization(now);

    Invoice invoice = invoiceDao.findPendingMonthlyByOrgId(org.getId());

    // Lock invoice in one context
    lockedInvoiceSvc.withLockedInvoice(
        invoice.getId(),
        new LockParams("test"),
        ignored -> {
          // Run validation within another context
          Context ctx = Context.current().withValue(LOCKED_INVOICE_CONTEXT_KEY, null);
          ctx.run(
              () -> {
                SvcException exception =
                    assertThrows(
                        SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
                assertThat(
                    exception.getErrorCode(), equalTo(OrgErrorCode.ORG_INVOICE_LOCKED_ON_DELETE));
              });
        });
  }

  @Test
  public void test_validation_requires_completed_gcp_commitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Date nextYear = DateUtils.addMonths(beginningOfMonth, 12);
    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    creditFactory.createGcpMarketplaceMonthlyCommitment(
        org.getId(), beginningOfMonth, nextYear, 120000L, false);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(), equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_GCP_COMMITMENT));
  }

  @Test
  public void test_validation_requires_completed_aws_commitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Date nextYear = DateUtils.addMonths(beginningOfMonth, 12);
    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    creditFactory.createAwsMarketplaceMonthlyCommitmentCredit(
        org.getId(), beginningOfMonth, nextYear, 120000L);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(), equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_AWS_COMMITMENT));
  }

  @Test
  public void testValidationRequiresCompletedFlexAwsCommitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
        FlexCommitCreditRequest.builder()
            .organizationId(org.getId())
            .flexStartDate(beginningOfMonth)
            .amountPerFlex(10000)
            .amountPerRollover(6000)
            .creditType(CreditType.AWS_FLEX_COMMIT)
            .build());

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));

    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_FLEX_COMMITMENT));
  }

  @Test
  public void testValidationRequiresCompletedFlexGcpCommitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
        FlexCommitCreditRequest.builder()
            .organizationId(org.getId())
            .flexStartDate(beginningOfMonth)
            .amountPerFlex(10000)
            .amountPerRollover(6000)
            .creditType(CreditType.GCP_FLEX_COMMIT)
            .build());

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));

    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_FLEX_COMMITMENT));
  }

  @Test
  public void testValidationRequiresCompletedFlexAzureCommitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
        FlexCommitCreditRequest.builder()
            .organizationId(org.getId())
            .flexStartDate(beginningOfMonth)
            .amountPerFlex(10000)
            .amountPerRollover(6000)
            .creditType(CreditType.AZURE_FLEX_COMMIT)
            .build());

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));

    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_FLEX_COMMITMENT));
  }

  @Test
  public void testValidationRequiresCompletedFlexDirectCommitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    flexCommitCreditFactory.createTwoYearFlexCommitCreditWithRollover(
        FlexCommitCreditRequest.builder()
            .organizationId(org.getId())
            .flexStartDate(beginningOfMonth)
            .amountPerFlex(10000)
            .amountPerRollover(6000)
            .build());

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));

    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_FLEX_COMMITMENT));
  }

  @Test
  public void test_validation_requires_inactive_gcp_flat_fee_commitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Date nextYear = DateUtils.addMonths(beginningOfMonth, 12);
    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    Credit credit =
        creditFactory.createGcpMarketplaceMonthlyCommitment(
            org.getId(), beginningOfMonth, nextYear, 120000L, true);
    creditDao.applyCredit(credit.getId(), 220000L);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_WITH_ACTIVE_GCP_MONTHLY_COMMITMENT));
  }

  @Test
  public void test_validation_requires_completed_direct_monthly_commitment() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Date nextYear = DateUtils.addMonths(beginningOfMonth, 12);
    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    creditFactory.createMonthlyCommitmentCredit(
        org.getId(), beginningOfMonth, nextYear, 120000L, true);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_REMAINING_MONTHLY_COMMITMENT));
  }

  @Test
  public void test_validation_requires_no_linked_orgs() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization payingOrg = organizationFactory.createAtlasOrganization(beginningOfMonth);
    Organization linkedOrg = organizationFactory.createAtlasOrganization(beginningOfMonth);

    payingOrgDao.upsertPayingOrg(payingOrg.getId(), List.of(linkedOrg.getId()), now.toInstant());

    SvcException exception =
        assertThrows(
            SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(payingOrg, now));
    assertThat(exception.getErrorCode(), equalTo(OrgErrorCode.ORG_HAS_LINKED_ORGS));
  }

  @Test
  public void test_validation_requires_no_failed_payments() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    paymentFactory.createPayment(
        null, org.getId(), 100L, Payment.Status.FAILED, BillingAccount.MONGODB_INC);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(exception.getErrorCode(), equalTo(OrgErrorCode.CANNOT_DELETE_ORG_FAILED_PAYMENTS));
  }

  @Test
  public void test_validation_requires_no_processing_payments() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    paymentFactory.createPayment(
        null, org.getId(), 100L, Payment.Status.PROCESSING, BillingAccount.MONGODB_INC);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_PROCESSING_OR_PENDING_REVERSAL_PAYMENTS));
  }

  @Test
  public void test_validation_requires_no_active_mandates() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    String stripeMandateId = "stripeMandateId";
    PaymentMethodMandate.Status mandateStatus = PaymentMethodMandate.Status.ACTIVE;
    PaymentMethodMandate paymentMethodMandate =
        new PaymentMethodMandate.Builder().id(stripeMandateId).status(mandateStatus).build();
    BillingAccount billingAccount = BillingAccount.MONGODB_LTD;
    String stripeCustomerId = "stripeCustomerId";
    String stripePaymentMethodId = "stripePaymentMethodId";

    PaymentMethod paymentMethod =
        paymentMethodFactory.createFakeCreditCardPaymentMethod(
            org.getId(),
            billingAccount,
            stripeCustomerId,
            stripePaymentMethodId,
            paymentMethodMandate);
    paymentMethodStubber.stubPaymentMethod(paymentMethod, org.getId());

    Payment payment =
        paymentFactory.createPayment(
            PaymentMethodType.CREDIT_CARD,
            org.getId(),
            100L,
            Payment.Status.CREATED,
            BillingAccount.MONGODB_INC);

    paymentDao.updatePaymentMethodId(
        payment.getId(), paymentMethod.getId(), paymentMethod.getBillingAccount());
    // FIX THIS
    // paymentMethodSvc.replaceActivePaymentMethodByOrgId(paymentMethod.getId(), org.getId());

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(exception.getErrorCode(), equalTo(OrgErrorCode.UNPAID_PAYMENTS_WITH_ACTIVE_MANDATE));
  }

  @Test
  public void test_validation_requires_no_pending_reversal_payments() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    paymentFactory.createPayment(
        null, org.getId(), 100L, Payment.Status.PENDING_REVERSAL, BillingAccount.MONGODB_INC);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_PROCESSING_OR_PENDING_REVERSAL_PAYMENTS));
  }

  @Test
  public void test_validation_requires_no_failed_aws_usagereports() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    usageReportFactory.createAwsUsageReport(
        org,
        "productCode",
        "customerId",
        "dimension",
        new Date(),
        new Date(),
        new Date(),
        1,
        1,
        Status.NEW,
        new ObjectId(),
        true);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(), equalTo(OrgErrorCode.CANNOT_DELETE_ORG_UNREPORTED_AWS_USAGE));
  }

  @Test
  public void test_validation_requires_no_failed_azure_usagereports() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    usageReportFactory.createAzureUsageReport(
        AzureUsageReport.builder()
            .orgId(org.getId())
            .creditId(new ObjectId())
            .azureExternalSubscriptionId(null)
            .azureUsageEventId(null)
            .azureDimension(AzureDimension.OVERAGE)
            .azurePlanId("plan")
            .created(now)
            .startDate(now)
            .endDate(now)
            .billedAmountCents(100L)
            .reportedAmountCents(100L)
            .status(Status.NEW)
            .build());

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(), equalTo(OrgErrorCode.CANNOT_DELETE_ORG_UNREPORTED_AZURE_USAGE));
  }

  @Test
  public void test_validation_requires_no_failed_gcp_usagereports() {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    usageReportFactory.createGcpUsageReport(
        org.getId(),
        new ObjectId(),
        "accountId",
        "entitlementId",
        "usageReportingId",
        "metric",
        new Date(),
        new Date(),
        new Date(),
        1,
        1,
        true,
        Status.NEW);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(), equalTo(OrgErrorCode.CANNOT_DELETE_ORG_UNREPORTED_GCP_USAGE));
  }

  @Test
  public void test_perform_finalizes_pending_invoice() throws SvcException {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);
    paymentMethodStubber.stubPaymentMethod(org.getId());
    Invoice invoice = invoiceDao.findPendingMonthlyByOrgId(org.getId());
    assertThat(invoice.getStatus(), equalTo(Invoice.Status.PENDING));

    orgDeletionRequestHandlerImpl.perform(org, now);

    Invoice updatedInvoice = invoiceDao.findById(invoice.getId());
    assertThat(updatedInvoice.getStatus(), equalTo(Invoice.Status.FREE));
  }

  @Test
  public void test_validation_requires_no_active_vercel_installation() throws SvcException {
    Date now = new Date();
    Date beginningOfMonth = DateUtils.truncate(now, Calendar.DAY_OF_MONTH);

    Organization org = organizationFactory.createAtlasOrganization(beginningOfMonth);

    // Create an active Vercel installation for the organization
    VercelInstallation vercelInstallation =
        VercelInstallation.builder()
            .installationId("test-installation-id")
            .orgId(org.getId())
            .accessToken("test-access-token")
            .tokenType("Bearer")
            .installationUrl("https://test.vercel.app")
            .build();

    vercelInstallationSvc.upsert(vercelInstallation);

    SvcException exception =
        assertThrows(SvcException.class, () -> orgDeletionRequestHandlerImpl.validate(org, now));
    assertThat(
        exception.getErrorCode(),
        equalTo(OrgErrorCode.CANNOT_DELETE_ORG_ACTIVE_VERCEL_INSTALLATION));
    assertThat(exception.getMessage(), containsString(org.getName()));

    vercelInstallationSvc.upsert(
        vercelInstallation.toBuilder().deletedAt(LocalDateTime.now()).build());

    orgDeletionRequestHandlerImpl.validate(org, now);
  }
}
