/* (C) Copyright 2012, MongoDB, Inc. */

package com.xgen.svc.mms.svc.ping.ingestion;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._public.model.event.HostRestartCounter;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.group._public.model.PreferredHostname;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStorageConfig;
import com.xgen.cloud.monitoring.topology._private.dao.CanonicalHostDao;
import com.xgen.cloud.monitoring.topology._public.model.ClusterType;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.cloud.monitoring.topology._public.model.ping.PingUtils;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.mms.dao.event.counter.HostRestartCounterDao;
import com.xgen.svc.mms.svc.ping.PingRequest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.bson.BasicBSONObject;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public final class IngestionSvcJsonIntTests extends BaseIngestionTest {

  @Inject DiscoverySvc _discoverySvc;

  @Inject CanonicalHostDao _canonicalHostDao;

  @Inject HostRestartCounterDao _hostRestartCounterDao;

  @Inject AppSettings _settings;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    FeatureFlagIntTestUtil.disableFeatureGlobally(FeatureFlag.CANONICAL_HOSTS_TTL_WRITES_ENABLED);
    FeatureFlagIntTestUtil.disableFeatureGlobally(
        FeatureFlag.CANONICAL_HOSTS_TTL_FILTERED_READS_ENABLED);
  }

  @Test
  public final void processNodesTest_rsidIsNotLostBecauseOfBlankHost() throws Exception {
    // Monarc Gaming Labs
    final Organization org = newTestOrganization();
    final ObjectId groupId = new ObjectId("547602b1e4b0b30ceddbc82d");
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/547602b1e4b0b30ceddbc82d/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/547602b1e4b0b30ceddbc82d/HostClusters.json",
        "mms/ingestion/IngestionSvc/547602b1e4b0b30ceddbc82d/Hosts.json",
        "mms/ingestion/IngestionSvc/547602b1e4b0b30ceddbc82d/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/547602b1e4b0b30ceddbc82d/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    assertEquals(
        "socialcasino_0",
        _hostDao.findById("e7a8bc04d9055566c68a228c7f0982cd", groupId).getReplicaSetId());
    assertEquals(
        "socialcasino_1",
        _hostDao.findById("1333435a4e89a9d4dafae4d74dfbc457", groupId).getReplicaSetId());
  }

  @Test
  public final void processNodesTest_whiteSpaceInShardString() throws Exception {
    // timon
    final ObjectId groupId = new ObjectId("53ce11b938bd3b23ead4ada7");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/53ce11b938bd3b23ead4ada7/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/53ce11b938bd3b23ead4ada7/HostClusters.json",
        "mms/ingestion/IngestionSvc/53ce11b938bd3b23ead4ada7/Hosts.json",
        "mms/ingestion/IngestionSvc/53ce11b938bd3b23ead4ada7/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/53ce11b938bd3b23ead4ada7/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<BasicDBObject> badAliases =
        _canonicalHostDao.findByGroupId(groupId).stream()
            .filter(o -> o.getString("hid").startsWith("true"))
            .collect(Collectors.toList());
    assertEquals(0, badAliases.size());
  }

  @Test
  public final void processNodesTest_serviceSourceNullNodeSignaturePeer() throws Exception {
    // Servicesource
    final ObjectId groupId = new ObjectId("4ee2874fb6f8d3480b72c790");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/4ee2874fb6f8d3480b72c790/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4ee2874fb6f8d3480b72c790/HostClusters.json",
        "mms/ingestion/IngestionSvc/4ee2874fb6f8d3480b72c790/Hosts.json",
        "mms/ingestion/IngestionSvc/4ee2874fb6f8d3480b72c790/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4ee2874fb6f8d3480b72c790/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertEquals(11, actualHostClusters.size());

    final Map<ClusterType, List<HostCluster>> byClusterType =
        actualHostClusters.stream().collect(Collectors.groupingBy(HostCluster::getType));
    assertEquals(5, byClusterType.get(ClusterType.REPLICA_SET).size());
    assertEquals(4, byClusterType.get(ClusterType.SHARDED).size());
    assertEquals(2, byClusterType.get(ClusterType.SHARDED_REPLICA_SET).size());
    assertNull(byClusterType.get(ClusterType.MASTER_SLAVE));
  }

  @Test
  public final void processNodesTest_shardsDiscoveredBeforeMongoSAndConfigs() throws Exception {
    // vnfo3live
    final ObjectId groupId = new ObjectId("51d5049da8e0e6aa50be5701");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/51d5049da8e0e6aa50be5701/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/51d5049da8e0e6aa50be5701/HostClusters.json",
        "mms/ingestion/IngestionSvc/51d5049da8e0e6aa50be5701/Hosts.json",
        "mms/ingestion/IngestionSvc/51d5049da8e0e6aa50be5701/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/51d5049da8e0e6aa50be5701/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromFtlJsonFile(groupId);
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertNewHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest_kixeye() throws Exception {
    final ObjectId groupId = new ObjectId("4e9e68f7ae6429bfa40fcc81");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/HostClusters.json",
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/Hosts.json",
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(groupId);
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest_kixeye_WithPreferredHostnames() throws Exception {
    final Organization org = newTestOrganization();
    final ObjectId groupId = new ObjectId("4e9e68f7ae6429bfa40fcc81");
    final Group group = newTestGroup(groupId);
    group.addPreferredHostname(new PreferredHostname(".dc.i.kixeye.com", true, false));
    group.addPreferredHostname(new PreferredHostname(".sjc.i.kixeye.com", true, false));

    populateIngestionCollectionsFromJsonFiles(
        group,
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/HostClusters.json",
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/Hosts.json",
        "mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4e9e68f7ae6429bfa40fcc81/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(group.getId());
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(group.getId());
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest_plt_prod() throws Exception {
    final ObjectId groupId = new ObjectId("522f7cf47ec5df2d7b03cf17");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/522f7cf47ec5df2d7b03cf17/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/522f7cf47ec5df2d7b03cf17/HostClusters.json",
        "mms/ingestion/IngestionSvc/522f7cf47ec5df2d7b03cf17/Hosts.json",
        "mms/ingestion/IngestionSvc/522f7cf47ec5df2d7b03cf17/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/522f7cf47ec5df2d7b03cf17/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    final DiscoveryRequest discoveryRequest = _discoverySvc.processAll(pingRequest);
    assertEquals(0, discoveryRequest.getNodeClusters().size());

    final List<HostCluster> clusters = findOrderedActualHostClustersFromDB(groupId);
    assertEquals(0, clusters.size());

    final Node standalone = discoveryRequest.findNode("ip-172-31-6-75:27017");
    assertEquals(1, standalone.getHostTypes().size());
    assertFalse(standalone.hasReplicaSetId());

    final Node repl = discoveryRequest.findNode("ip-172-31-2-95:27017");
    assertEquals(
        0,
        repl.getHostTypes()
            .size()); // JM: odd, but will be set as STANDALONE by periodic ReviewHostTypesJob
    assertEquals(1, repl.getReplicaSetIds().size());
    assertEquals("CumulusReplica", repl.getReplicaSetIds().iterator().next());
  }

  @Test
  public final void processNodesTest_EOS_PEP_ROLES() throws Exception {
    final Organization org = newTestOrganization();
    final ObjectId groupId = new ObjectId("51cc672b7fe227e9f18dd97f");
    final Group group = newTestGroup(groupId);
    group.setPreferredHostnames(Arrays.asList(new PreferredHostname("^5[04]\\..*", false, true)));

    populateIngestionCollectionsFromJsonFiles(
        group,
        "mms/ingestion/IngestionSvc/51cc672b7fe227e9f18dd97f/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/51cc672b7fe227e9f18dd97f/HostClusters.json",
        "mms/ingestion/IngestionSvc/51cc672b7fe227e9f18dd97f/Hosts.json",
        "mms/ingestion/IngestionSvc/51cc672b7fe227e9f18dd97f/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/51cc672b7fe227e9f18dd97f/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(group.getId());
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(group.getId());
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);

    // customers expects primary hostname to be the IP address: **************
    final String mongosPreferredIpAddress = "**************";
    final String primaryHostId =
        _canonicalHostDao.findPrimaryHostId(group.getId(), "ROUTER2us-east-1a", 27017);
    assertEquals(
        HostUtils.assembleHostId(group.getId(), mongosPreferredIpAddress, 27017), primaryHostId);

    assertNull(
        "incorrect reversed entry exists",
        _canonicalHostDao.findPrimaryHostId(group.getId(), mongosPreferredIpAddress, 27017));
  }

  @Test
  public final void processNodesTest_thereq() throws Exception {
    final ObjectId groupId = new ObjectId("5180232f7fe227e9f1880756");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/5180232f7fe227e9f1880756/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/5180232f7fe227e9f1880756/HostClusters.json",
        "mms/ingestion/IngestionSvc/5180232f7fe227e9f1880756/Hosts.json",
        "mms/ingestion/IngestionSvc/5180232f7fe227e9f1880756/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/5180232f7fe227e9f1880756/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(groupId);
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest7() throws Exception {
    final ObjectId groupId = new ObjectId("517967c37fe227e9f1880073");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/517967c37fe227e9f1880073/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/517967c37fe227e9f1880073/HostClusters.json",
        "mms/ingestion/IngestionSvc/517967c37fe227e9f1880073/Hosts.json",
        "mms/ingestion/IngestionSvc/517967c37fe227e9f1880073/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/517967c37fe227e9f1880073/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromFtlJsonFile(groupId);
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertNewHostClustersEqual(expectedHostClusters, actualHostClusters);
    assertEquals(6, actualHostClusters.size());
  }

  @Test
  public final void processNodesTest4() throws Exception {
    final ObjectId groupId = new ObjectId("4dac4dcd95b6506eefca2e88");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/4dac4dcd95b6506eefca2e88/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4dac4dcd95b6506eefca2e88/HostClusters.json",
        "mms/ingestion/IngestionSvc/4dac4dcd95b6506eefca2e88/Hosts.json",
        "mms/ingestion/IngestionSvc/4dac4dcd95b6506eefca2e88/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4dac4dcd95b6506eefca2e88/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(groupId);
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest3() throws Exception {
    final ObjectId groupId = new ObjectId("50758439699f047de062dbfe");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/50758439699f047de062dbfe/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/50758439699f047de062dbfe/HostClusters.json",
        "mms/ingestion/IngestionSvc/50758439699f047de062dbfe/Hosts.json",
        "mms/ingestion/IngestionSvc/50758439699f047de062dbfe/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/50758439699f047de062dbfe/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    assertEquals(0, findOrderedActualHostClustersFromDB(groupId).size());
  }

  @Test
  public final void processNodesTest2() throws Exception {
    final ObjectId groupId = new ObjectId("50c4ac0a7fe227e9f181c6de");
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(groupId);

    populateIngestionCollectionsFromJsonFiles(
        groupId,
        "mms/ingestion/IngestionSvc/50c4ac0a7fe227e9f181c6de/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/50c4ac0a7fe227e9f181c6de/HostClusters.json",
        "mms/ingestion/IngestionSvc/50c4ac0a7fe227e9f181c6de/Hosts.json",
        "mms/ingestion/IngestionSvc/50c4ac0a7fe227e9f181c6de/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/50c4ac0a7fe227e9f181c6de/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(groupId);
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(groupId);
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest1() throws Exception {
    final Organization org = newTestOrganization();
    final ObjectId groupId = new ObjectId("4d8b65ee95b6506eefca2d15");
    final Group group = newTestGroup(groupId);
    group.addPreferredHostname(new PreferredHostname("^DBM.*$", false, true));

    populateIngestionCollectionsFromJsonFiles(
        group,
        "mms/ingestion/IngestionSvc/4d8b65ee95b6506eefca2d15/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4d8b65ee95b6506eefca2d15/HostClusters.json",
        "mms/ingestion/IngestionSvc/4d8b65ee95b6506eefca2d15/Hosts.json",
        "mms/ingestion/IngestionSvc/4d8b65ee95b6506eefca2d15/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4d8b65ee95b6506eefca2d15/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromFtlJsonFile(group.getId());
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(group.getId());
    assertNewHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public final void processNodesTest0() throws Exception {
    final Organization org = newTestOrganization();
    final ObjectId groupId = new ObjectId("4d3874ade528c81a1fcdd511");
    final Group group = newTestGroup(groupId);
    group.addPreferredHostname(new PreferredHostname(".*cbi.*", false, true));
    group.addPreferredHostname(new PreferredHostname("10gen.cc*", true, false));

    populateIngestionCollectionsFromJsonFiles(
        group,
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/HostClusters.json",
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/Hosts.json",
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromJsonFile(group.getId());
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(group.getId());
    assertHostClustersEqual(expectedHostClusters, actualHostClusters);
  }

  @Test
  public void pingRestartCounter() throws Exception {
    final String hostAndPort = "cbi8.ny.10gen.cc:27800";
    final ObjectId groupId = new ObjectId("4d3874ade528c81a1fcdd511");
    final Group group = newTestGroup(groupId);
    group.addPreferredHostname(new PreferredHostname(".*cbi.*", false, true));
    group.addPreferredHostname(new PreferredHostname("10gen.cc*", true, false));

    populateIngestionCollectionsFromJsonFiles(
        group,
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/HostClusters.json",
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/Hosts.json",
        "mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4d3874ade528c81a1fcdd511/Ping.json");

    final BasicBSONObject singleHost =
        (BasicBSONObject) PingUtils.extractHosts(ping).get(hostAndPort);
    final BasicBSONObject serverStatus = PingUtils.extractServerStatus(singleHost);
    serverStatus.put(Ping.UPTIME.field, 3);

    final PingRequest pingRequest =
        newTestPingWithTimeRequest(groupId, ping, Instant.now().toEpochMilli());

    pingRequest.getGroup().getGroupStorageConfig().setMode(GroupStorageConfig.Mode.V3);

    _discoverySvc.processAll(pingRequest);

    Thread.sleep(2000);

    final String hostId = HostUtils.extractHostId(groupId, hostAndPort);
    HostRestartCounter counter = _hostRestartCounterDao.findByHostId(hostId);
    assertEquals(Integer.valueOf(1), counter.getRestartCounter());

    final PingRequest pingRequest2 =
        newTestPingWithTimeRequest(groupId, ping, Instant.now().toEpochMilli());

    pingRequest2.getGroup().getGroupStorageConfig().setMode(GroupStorageConfig.Mode.V3);
    _discoverySvc.processAll(pingRequest2);

    Thread.sleep(2000);
    /* Will still be 1 because createRestartEvent will not create a new event unless at least 2 minutes has passed
     * since the last restart event.
     */
    counter = _hostRestartCounterDao.findByHostId(hostId);
    assertEquals(Integer.valueOf(1), counter.getRestartCounter());
  }

  @Test
  public final void processNodesTest_recordedfuture() throws Exception {
    final Organization org = newTestOrganization();
    final Group group = newTestGroup(new ObjectId("4e7084c114587819403c9756"));

    populateIngestionCollectionsFromJsonFiles(
        group,
        "mms/ingestion/IngestionSvc/4e7084c114587819403c9756/CanonicalHosts.json",
        "mms/ingestion/IngestionSvc/4e7084c114587819403c9756/HostClusters.json",
        "mms/ingestion/IngestionSvc/4e7084c114587819403c9756/Hosts.json",
        "mms/ingestion/IngestionSvc/4e7084c114587819403c9756/DeletedHosts.json.ftl");

    final BasicBSONObject ping =
        loadBsonObjectFromJsonFile("mms/ingestion/IngestionSvc/4e7084c114587819403c9756/Ping.json");
    final PingRequest pingRequest = newTestPingRequest(org, group, ping);
    _discoverySvc.processAll(pingRequest);

    final List<HostCluster> expectedHostClusters =
        findOrderedExpectedHostClustersfromFtlJsonFile(group.getId());
    final List<HostCluster> actualHostClusters = findOrderedActualHostClustersFromDB(group.getId());
    assertNewHostClustersEqual(expectedHostClusters, actualHostClusters);

    assertEquals(13, actualHostClusters.size());
  }

  protected List<HostCluster> findOrderedExpectedHostClustersfromJsonFile(final ObjectId pGroupId)
      throws Exception {
    return findOrderedExpectedHostClustersfromJsonFile(
        "mms/ingestion/IngestionSvc/" + pGroupId + "/expected/HostClusters-" + pGroupId + ".json");
  }

  protected List<HostCluster> findOrderedExpectedHostClustersfromFtlJsonFile(
      final ObjectId pGroupId) throws Exception {
    final String ftlTemplate =
        "mms/ingestion/IngestionSvc/"
            + pGroupId
            + "/expected/HostClusters-"
            + pGroupId
            + ".json.ftl";
    return findOrderedExpectedHostClustersfromFtlJsonFile(ftlTemplate, null);
  }
}
