load("//server/src/test:rules.bzl", "library_package", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(
        ["*IntTests.java"],
    ),
    deps = [
        ":commonUtil",
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/billingplatform/model/jobqueue",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_private/dao",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/billing/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/module/metering/client/svc/api_2023_05_03",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/svc/mms/dao/billing",
        "//server/src/main/com/xgen/svc/mms/svc/atlasbilling",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_quartz_scheduler_quartz",
    ],
)

library_package(
    name = "commonUtil",
    srcs = ["SubscriptionUsageTestUtil.java"],
    deps = [
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/svc/mms/dao/billing",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
    ],
)
