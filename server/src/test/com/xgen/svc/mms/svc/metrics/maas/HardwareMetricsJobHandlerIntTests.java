package com.xgen.svc.mms.svc.metrics.maas;

import static io.opentelemetry.proto.metrics.v1.MetricsData.parseFrom;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpServer;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.metrics._private.dao.backfill.CustomerMetricsBackfillCheckpointDao;
import com.xgen.cloud.monitoring.metrics._private.dao.backfill.CustomerMetricsBackfillCheckpointDao.Checkpoint;
import com.xgen.cloud.monitoring.metrics._private.dao.backfill.CustomerMetricsBackfillCheckpointDao.CheckpointStatus;
import com.xgen.cloud.monitoring.metrics._private.dao.rrd.HostMeasurementDao;
import com.xgen.cloud.monitoring.metrics._public.model.DiskMeasurement;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import io.opentelemetry.proto.metrics.v1.Metric;
import io.opentelemetry.proto.metrics.v1.MetricsData;
import io.opentelemetry.proto.metrics.v1.ResourceMetrics;
import io.opentelemetry.proto.metrics.v1.ScopeMetrics;
import jakarta.inject.Inject;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class HardwareMetricsJobHandlerIntTests extends JUnit5BaseSvcTest {
  @Inject private AppSettings appSettings;
  @Inject private HardwareMetricsBackfillSvc hardwareMetricsBackfillSvc;
  @Inject private CustomerMetricsBackfillCheckpointDao checkpointDao;
  @Inject private HostMeasurementDao hostMeasurementDao;
  @Inject private JobQueueTestUtils jobQueueTestUtils;
  @Inject private HardwareMetricsBackfillJobHandler jobHandler;
  private HttpServer httpServer;
  private byte[] lastReceivedPayload;

  private static final int MAAS_LOCAL_PORT = 32001;

  private long startEpochMillis;
  private long endEpochMillis;
  private ObjectId groupId;
  private final List<String> hostIds = new ArrayList<>();

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    final Group group = MmsFactory.createGroupWithStandardPlan();
    groupId = group.getId();
    final HostCluster replicaSet = MmsFactory.createReplicaSet_V_5_0(groupId, "testReplicaSet");
    final HostCluster cluster = MmsFactory.createCluster("Cluster0", List.of(replicaSet));
    hostIds.addAll(cluster.getHostIds());

    // Have to set these after the cluster is created so that the hosts are active within the job
    startEpochMillis = Instant.now().minus(Duration.ofDays(300)).toEpochMilli();
    endEpochMillis = Instant.now().toEpochMilli();

    appSettings.setProp(
        HardwareMetricsBackfillSvc.BACKFILL_HARDWARE_ENABLED_PROPERTY_NAME,
        "true",
        SettingType.MEMORY);
    appSettings.setProp(
        HardwareMetricsBackfillSvc.BACKFILL_HARDWARE_START_EPOCH_MILLIS_PROPERTY_NAME,
        String.valueOf(startEpochMillis),
        SettingType.MEMORY);
    appSettings.setProp(
        HardwareMetricsBackfillSvc.BACKFILL_HARDWARE_END_EPOCH_MILLIS_PROPERTY_NAME,
        String.valueOf(endEpochMillis),
        SettingType.MEMORY);

    startHttpServer();
  }

  @AfterEach
  public void tearDown() throws Exception {
    super.tearDown();
    httpServer.stop(0);
  }

  @Test
  public void testHardwareMetricsBackfill() throws Exception {
    assertTrue(hardwareMetricsBackfillSvc.isMetricBackfillEnabled());

    // Add metrics for each host in the group to DB
    final long sampleTime = startEpochMillis + 1000;
    for (final String hostId : hostIds) {
      final List<DiskMeasurement> diskMeasurements =
          List.of(
              new DiskMeasurement(
                  DiskMeasurement.id(groupId, hostId, sampleTime),
                  new Date(sampleTime),
                  new Date(),
                  0,
                  new BasicDBObject(
                      "p", new BasicDBObject(Map.of("su", new BasicDBObject("su", 6))))));
      hostMeasurementDao.insertDiskMeasurements(diskMeasurements, Retention.DAY_DATA_RETENTION);
    }

    // Submit the backfill batch job for the group
    hardwareMetricsBackfillSvc.scheduleMetricBackfillJobs(List.of(groupId));
    jobQueueTestUtils.processAllJobsInQueue();

    Thread.sleep(10_000); // Wait for the job to complete

    // Assert that the payload was received in the correct format by the HTTP server
    assertNotNull(lastReceivedPayload);
    final MetricsData parsedPayload = parseFrom(lastReceivedPayload);

    // Group ID and host ID are correct
    assertEquals(1, parsedPayload.getResourceMetricsCount());
    final ResourceMetrics resourceMetrics = parsedPayload.getResourceMetrics(0);
    assertEquals(
        groupId.toString(),
        resourceMetrics.getResource().getAttributes(0).getValue().getStringValue());
    assertTrue(
        hostIds.contains(
            resourceMetrics.getResource().getAttributes(1).getValue().getStringValue()));
    assertEquals(1, resourceMetrics.getScopeMetricsCount());

    // Metric data is correct (including timestamp)
    final ScopeMetrics scopeMetrics = parsedPayload.getResourceMetrics(0).getScopeMetrics(0);
    assertEquals(1, scopeMetrics.getMetricsCount());
    final Metric metric = scopeMetrics.getMetrics(0);
    assertEquals("hardware_disk_metrics_disk_space_used_bytes", metric.getName());
    assertEquals(sampleTime, metric.getGauge().getDataPoints(0).getTimeUnixNano() / 1_000_000);
    assertEquals(6, metric.getGauge().getDataPoints(0).getAsDouble());

    // Assert the checkpoint is marked as complete for each host
    for (final String hostId : hostIds) {
      final Checkpoint checkpoint = checkpointDao.findCheckpointByHostId(hostId);
      assertNotNull(checkpoint);
      assertEquals(hostId, checkpoint.getId());
      assertEquals(groupId.toString(), checkpoint.getGroupId());
      assertEquals(CheckpointStatus.COMPLETE, checkpoint.getStatus());
    }
  }

  private void startHttpServer() throws IOException {
    httpServer = HttpServer.create(new InetSocketAddress("localhost", MAAS_LOCAL_PORT), 0);
    final String path = "/metrics/v1/otel/bulk/backfill/" + jobHandler.getUsecaseId();
    httpServer.createContext(path, this::otelBulkBackfillHandler);
    httpServer.start();
  }

  private void otelBulkBackfillHandler(final HttpExchange exchange) throws IOException {
    LOG.info("Received HTTP request for metrics backfill");
    if ("POST".equals(exchange.getRequestMethod())) {
      try (final InputStream is = exchange.getRequestBody()) {
        lastReceivedPayload = is.readAllBytes();
      }
      exchange.sendResponseHeaders(200, 0);
    } else {
      LOG.error(
          "Unexpected HTTP request for metrics backfill, method: {}", exchange.getRequestMethod());
      exchange.sendResponseHeaders(405, 0);
    }
  }
}
