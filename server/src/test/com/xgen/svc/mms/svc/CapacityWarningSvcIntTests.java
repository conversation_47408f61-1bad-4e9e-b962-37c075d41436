package com.xgen.svc.mms.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStorageConfig;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.model.CapacityWarning;
import jakarta.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.Before;
import org.junit.Test;

public class CapacityWarningSvcIntTests extends BaseSvcTest {

  @Inject private CapacityWarningSvc _capacityWarningSvc;

  @Inject private GroupDao _groupDao;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/host/HostDao/hosts.json.ftl", null, Host.DB_NAME, Host.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ReplicaSetHardwareDao/replicaSetHardware.json.ftl",
        null,
        "nds",
        "config.nds.replicaSetHardware");

    MmsFactory.ensureDefaultRetentionPolicies();
    final Group group = _groupDao.findById(oid(118));
    group.setGroupStorageConfig(
        new GroupStorageConfig(GroupStorageConfig.Mode.V3, MongoSvcUtils.oid(1)));
    _groupDao.save(group);
  }

  @Test
  public void testGetCapacityWarnings_NoData() {
    // The call to getCapacityWarnings can return more result than just the "clusterForAlert" since
    // the content of the fixture files are constantly changing. To make this test more stable, we
    // are filtering only for the cluster we are interested in testing
    final List<CapacityWarning> warnings = getCapacityWarningSvc().getCapacityWarnings(oid(118));
    final List<CapacityWarning> clusterForAlertWarnings =
        warnings.stream()
            .filter(w -> w.getClusterName().equals("clusterForAlert"))
            .collect(Collectors.toList());

    assertEquals(2, clusterForAlertWarnings.size());

    clusterForAlertWarnings.stream()
        .filter(w -> w.getStatisticType().equals(CapacityWarning.StatisticType.STORAGE))
        .forEach(
            w -> {
              assertEquals(CapacityWarning.AlertLevel.INFO, w.getAlertLevel());
              assertEquals(64, (int) w.getMaxValue());
              assertTrue(Double.isNaN(w.getCurrentValue()));
            });
    clusterForAlertWarnings.stream()
        .filter(w -> w.getStatisticType().equals(CapacityWarning.StatisticType.CONNECTIONS))
        .forEach(
            w -> {
              assertEquals(CapacityWarning.AlertLevel.INFO, w.getAlertLevel());
              assertEquals(3000, (int) w.getMaxValue());
              assertTrue(Double.isNaN(w.getCurrentValue()));
            });
  }

  protected CapacityWarningSvc getCapacityWarningSvc() {
    return _capacityWarningSvc;
  }
}
