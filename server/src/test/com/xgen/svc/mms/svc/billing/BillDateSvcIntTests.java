package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Status;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.process.orchestration._private.dao.lastbilldate.LastBillDateForOrgDao;
import com.xgen.cloud.billingplatform.process.orchestration._public.model.lastbilldate.LastBillDateForOrg;
import com.xgen.cloud.billingplatform.process.orchestration._public.svc.lastbilldate.LastBillDateSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.PayingOrg;
import com.xgen.svc.mms.util.billing.testFactories.CreateInvoiceParams;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class BillDateSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private BillDateSvc billDateSvc;
  @Inject private InvoiceFactory invoiceFactory;
  @Inject private LastBillDateSvc lastBillDateSvc;
  @Inject private LastBillDateForOrgDao lastBillDateForOrgDao;
  @Inject private AppSettings appSettings;

  @Test
  public void determineFirstBillDate_firstTimeBillingOrg() {
    LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
    LocalDate endOfMonth = startOfMonth.plusMonths(1);
    Organization org = MmsFactory.createOrganizationWithNDSPlan(DateTimeUtils.dateOf(startOfMonth));
    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(DateTimeUtils.dateOf(startOfMonth))
                .endDate(DateTimeUtils.dateOf(endOfMonth))
                .organizationId(org.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Status.PENDING)
                .build());
    Date result = billDateSvc.determineFirstBillDate(org, invoice, List.of(invoice.getOrgId()));
    assertEquals(
        DateTimeUtils.dateOf(startOfMonth),
        result,
        "First bill date should be the first day of month");
  }

  @Test
  public void determineFirstBillDate_allDatesSet() {
    LocalDate startOfMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
    LocalDate fifthOfMonth = LocalDate.now().minusMonths(1).withDayOfMonth(5);

    LocalDate endOfMonth = startOfMonth.plusMonths(1);
    Organization org = MmsFactory.createOrganizationWithNDSPlan(DateTimeUtils.dateOf(startOfMonth));
    Group group1 = MmsFactory.createGroup(org);
    Group group2 = MmsFactory.createGroup(org);
    // no last bill date
    Group group3 = MmsFactory.createGroup(org);

    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(DateTimeUtils.dateOf(startOfMonth))
                .endDate(DateTimeUtils.dateOf(endOfMonth))
                .organizationId(org.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Status.PENDING)
                .build());
    lastBillDateForOrgDao.updateLastBillDate(org.getId(), DateTimeUtils.dateOf(startOfMonth));
    lastBillDateSvc.updateLastBillDate(group1.getId(), DateTimeUtils.dateOf(startOfMonth));
    lastBillDateSvc.updateLastBillDate(group2.getId(), DateTimeUtils.dateOf(fifthOfMonth));

    lastBillDateSvc.updateLastBillDateForSKUs(
        group1.getId(),
        SKU.getBillableSkusUsingLastBillDateBySku(
            appSettings.getAppEnv().isProdOrProdGovOrInternal()),
        DateTimeUtils.dateOf(startOfMonth));

    lastBillDateSvc.updateLastBillDateForSKUs(
        group2.getId(),
        SKU.getBillableSkusUsingLastBillDateBySku(
            appSettings.getAppEnv().isProdOrProdGovOrInternal()),
        DateTimeUtils.dateOf(fifthOfMonth));

    Date result = billDateSvc.determineFirstBillDate(org, invoice, List.of(invoice.getOrgId()));
    Date expectedDate = DateTimeUtils.dateOf(startOfMonth.withDayOfMonth(2));
    assertEquals(expectedDate, result, "First bill date should be the second day of month");
  }

  @Test
  public void determineFirstBillDate_orgLbdBeforeGroup() {
    LocalDate startOfMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
    LocalDate fifthOfMonth = LocalDate.now().minusMonths(1).withDayOfMonth(5);
    LocalDate seventhfMonth = LocalDate.now().minusMonths(1).withDayOfMonth(7);

    LocalDate endOfMonth = startOfMonth.plusMonths(1);
    Organization org = MmsFactory.createOrganizationWithNDSPlan(DateTimeUtils.dateOf(startOfMonth));
    Group group = MmsFactory.createGroup(org);

    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(DateTimeUtils.dateOf(startOfMonth))
                .endDate(DateTimeUtils.dateOf(endOfMonth))
                .organizationId(org.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Status.PENDING)
                .build());
    lastBillDateForOrgDao.updateLastBillDate(org.getId(), DateTimeUtils.dateOf(fifthOfMonth));
    lastBillDateSvc.updateLastBillDate(group.getId(), DateTimeUtils.dateOf(seventhfMonth));

    lastBillDateSvc.updateLastBillDateForSKUs(
        group.getId(),
        SKU.getBillableSkusUsingLastBillDateBySku(
            appSettings.getAppEnv().isProdOrProdGovOrInternal()),
        DateTimeUtils.dateOf(fifthOfMonth));

    Date result = billDateSvc.determineFirstBillDate(org, invoice, List.of(org.getId()));
    Date expectedDate = DateTimeUtils.dateOf(fifthOfMonth.plusDays(1));
    assertEquals(expectedDate, result, "First bill date should be org LBD + 1 day");
  }

  @Test
  public void multithreadUpdateCreditLbd() throws ExecutionException, InterruptedException {
    Date lastBillDate = DateTimeUtils.dateOf(LocalDate.parse("2023-06-05"));
    ObjectId orgId = new ObjectId();
    LastBillDateForOrg lastBillDateForOrg =
        new LastBillDateForOrg(new ObjectId(), orgId, lastBillDate, lastBillDate);
    save(lastBillDateForOrg);
    ExecutorService executorService = Executors.newFixedThreadPool(5);
    Date newBillDate1 = DateTimeUtils.dateOf(LocalDate.parse("2023-06-03"));
    Date newBillDate2 = DateTimeUtils.dateOf(LocalDate.parse("2023-06-02"));
    Date newBillDate3 = DateTimeUtils.dateOf(LocalDate.parse("2023-06-01"));
    Date newBillDate4 = DateTimeUtils.dateOf(LocalDate.parse("2023-05-31"));
    Date newBillDate5 = DateTimeUtils.dateOf(LocalDate.parse("2023-05-30"));
    List<Future<?>> futures = new ArrayList<>();
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate1)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate2)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate3)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate4)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate5)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate1)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate2)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate3)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate4)));
    futures.add(
        executorService.submit(() -> billDateSvc.checkCreditApplicationDate(orgId, newBillDate5)));
    for (Future<?> future : futures) {
      future.get();
    }
    LastBillDateForOrg lastBillDateForOrgAfterUpdate = lastBillDateForOrgDao.findByOrgId(orgId);
    Date expectedLastBillDate = DateUtils.addDays(newBillDate5, -1);
    assertEquals(
        expectedLastBillDate,
        lastBillDateForOrgAfterUpdate.getLastBillDateForCredit(),
        "Earliest credit bill date should be 2023-05-29, since the earliest bill date is"
            + " 2023-05-30");
  }

  @Test
  public void determineFirstBillDate_crossOrg() {
    Date apr1 = TimeUtils.fromISOString("2023-04-01");
    Date apr3 = TimeUtils.fromISOString("2023-04-03");
    Date apr4 = TimeUtils.fromISOString("2023-04-04");
    Date apr5 = TimeUtils.fromISOString("2023-04-05");

    Date endOfMonth = TimeUtils.fromISOString("2023-05-01");
    Organization payingOrg = MmsFactory.createOrganizationWithNDSPlan(apr1);

    Organization linkedOrg = MmsFactory.createOrganizationWithNDSPlan(apr1);

    Group payingGroup = MmsFactory.createGroup(payingOrg);
    Group linkedGroup = MmsFactory.createGroup(linkedOrg);

    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(apr1)
                .endDate(endOfMonth)
                .organizationId(linkedOrg.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Status.PENDING)
                .build());
    lastBillDateForOrgDao.updateLastBillDate(payingOrg.getId(), apr5);
    lastBillDateForOrgDao.updateCreditLastBillDate(payingOrg.getId(), apr5);
    lastBillDateForOrgDao.updateLastBillDate(linkedOrg.getId(), apr5);
    lastBillDateForOrgDao.updateCreditLastBillDate(linkedOrg.getId(), apr4);

    lastBillDateSvc.updateLastBillDate(payingGroup.getId(), apr4);
    lastBillDateSvc.updateLastBillDate(linkedGroup.getId(), apr3);

    lastBillDateSvc.updateLastBillDateForSKUs(
        payingGroup.getId(),
        SKU.getBillableSkusUsingLastBillDateBySku(
            appSettings.getAppEnv().isProdOrProdGovOrInternal()),
        apr4);

    lastBillDateSvc.updateLastBillDateForSKUs(
        linkedGroup.getId(),
        SKU.getBillableSkusUsingLastBillDateBySku(
            appSettings.getAppEnv().isProdOrProdGovOrInternal()),
        apr3);

    save(
        PayingOrg.builder()
            .payingOrgId(payingOrg.getId())
            .linkedOrgIds(List.of(linkedOrg.getId()))
            .build());

    assertEquals(
        apr4,
        billDateSvc.determineFirstBillDate(
            payingOrg, invoice, List.of(payingOrg.getId(), linkedOrg.getId())),
        "Earliest group bill date (including linked orgs) should be the second day of month");
  }
}
