package com.xgen.svc.mms.svc;

import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MONITORING_REALM_ENABLED;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MONITORING_REALM_REQUEST_THREADS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.REALM_METRICS_NUM_REQUEST_PAGES_1_MIN;
import static com.xgen.svc.mms.svc.RealmMetricCollectionSvc.STARTED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.group._public.svc.CacheableGroupSvc;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.common._public.model.retention.RetentionPolicy;
import com.xgen.cloud.monitoring.metrics._private.dao.realm.RealmMetricStateDao;
import com.xgen.cloud.monitoring.metrics._private.dao.rrd.RetentionPolicyDao;
import com.xgen.cloud.monitoring.metrics._public.model.RealmMetricState;
import com.xgen.cloud.monitoring.metrics._public.model.RealmRegionMetrics;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.svc.RealmMetricCollectionSvc.RealmMetricRequestHandler;
import com.xgen.svc.mms.svc.ping.PingIngestionSvc;
import com.xgen.svc.mms.svc.ping.RealmPingSourceFactory;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import org.bson.Document;
import org.junit.Before;
import org.junit.Test;

public class RealmMetricCollectionSvcIntTests extends BaseSvcTest {
  @Inject private PingIngestionSvc _pingIngestionSvc;
  @Inject private RealmPingSourceFactory _realmPingSourceFactory;
  @Inject private RetentionPolicyDao _retentionPolicyDao;
  @Inject private RealmMetricStateDao _realmMetricStateDao;
  @Inject private CacheableGroupSvc _cacheableGroupSvc;
  @Inject private AppSettings _appSettings;

  private static final Duration REQUEST_GRANULARITY = Duration.ofMinutes(1);
  private static final long REQUEST_GRANULARITY_MILLIS = REQUEST_GRANULARITY.toMillis();

  @Before
  @Override
  public void setUp() {
    final SortedSet<Retention> policy =
        new TreeSet<>(List.of(Retention.of(REQUEST_GRANULARITY, Duration.ofHours(48))));
    if (_retentionPolicyDao.findByPlanType(PlanType.NDS) != null) {
      _retentionPolicyDao.removeRetentionPolicy(_retentionPolicyDao.findByPlanType(PlanType.NDS));
    }
    _retentionPolicyDao.addRetentionPolicy(
        new RetentionPolicy(
            Set.of(PlanType.NDS), policy, RetentionPolicy.RetentionPolicyType.REALM));
    _realmMetricStateDao.deleteManyMajority(new Document());
  }

  @Test
  public void TestCollectionDisabledNoThreads() {
    _appSettings.setProp(MONITORING_REALM_ENABLED.value, "false", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "0", AppSettings.SettingType.MEMORY);
    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    assertNull(svc.getSvcStatus());
    svc.stop();
  }

  @Test
  public void TestCollectionEnabledNoThreads() {
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "0", AppSettings.SettingType.MEMORY);
    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    assertNull(svc.getSvcStatus());
    svc.stop();
  }

  @Test
  public void TestCollectionDisabledOneThread() {
    _appSettings.setProp(MONITORING_REALM_ENABLED.value, "false", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "1", AppSettings.SettingType.MEMORY);
    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    assertNull(svc.getSvcStatus());
    svc.stop();
  }

  @Test
  public void TestCollectionEnabledOneThread() {
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "1", AppSettings.SettingType.MEMORY);
    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    assertEquals(STARTED, svc.getSvcStatus());
    svc.stop();
  }

  @Test
  public void TestCollectionEnabledNThreads() {
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "4", AppSettings.SettingType.MEMORY);
    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    assertEquals(STARTED, svc.getSvcStatus());
    assertEquals(4, svc.getCorePoolSize());
    svc.stop();
  }

  @Test
  public void TestSvcIncreaseInStateDocs() {
    final String newNumberOfPages = "64";
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "1", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        REALM_METRICS_NUM_REQUEST_PAGES_1_MIN.value,
        newNumberOfPages,
        AppSettings.SettingType.MEMORY);

    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    final RealmMetricState state =
        new RealmMetricState(
            0, Date.from(Instant.now().minus(REQUEST_GRANULARITY)), REQUEST_GRANULARITY_MILLIS);
    _realmMetricStateDao.insertReplicaSafe(state);

    final RealmMetricCollectionSvc.RealmMetricRequestHandler handler =
        svc.new RealmMetricRequestHandler();

    final Thread handleRequest = new Thread(handler);
    handleRequest.start();
    try {
      // Give time for Handler to run at least once
      Thread.sleep(10_000);
    } catch (InterruptedException e1) {
      throw new RuntimeException(e1);
    }

    // stop the requesting thread from busy-waiting
    handleRequest.interrupt();

    assertEquals(
        Long.parseLong(newNumberOfPages),
        _realmMetricStateDao.countByRequestGranularity(REQUEST_GRANULARITY_MILLIS));
    svc.stop();
  }

  @Test
  public void TestSvcDecreaseInStateDocs() {
    _appSettings.setProp(MONITORING_REALM_ENABLED.value, "true", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "1", AppSettings.SettingType.MEMORY);
    final String newNumberOfPages = "1";
    _appSettings.setProp(
        REALM_METRICS_NUM_REQUEST_PAGES_1_MIN.value,
        newNumberOfPages,
        AppSettings.SettingType.MEMORY);

    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    final RealmMetricState state =
        new RealmMetricState(
            0, Date.from(Instant.now().minus(REQUEST_GRANULARITY)), REQUEST_GRANULARITY_MILLIS);
    final RealmMetricState removableState =
        new RealmMetricState(
            100, Date.from(Instant.now().minus(REQUEST_GRANULARITY)), REQUEST_GRANULARITY_MILLIS);
    _realmMetricStateDao.insertReplicaSafe(state);
    _realmMetricStateDao.insertReplicaSafe(removableState);

    final RealmMetricCollectionSvc.RealmMetricRequestHandler handler =
        svc.new RealmMetricRequestHandler();

    final Thread handleRequest = new Thread(handler);
    handleRequest.start();
    try {
      // Give time for Handler to run at least once
      Thread.sleep(10_000);
    } catch (InterruptedException e1) {
      throw new RuntimeException(e1);
    }

    // stop the requesting thread from busy-waiting
    handleRequest.interrupt();

    assertEquals(
        Long.parseLong(newNumberOfPages),
        _realmMetricStateDao.countByRequestGranularity(REQUEST_GRANULARITY_MILLIS));
    assertEquals(
        "Assert that the number of pages is respected",
        Integer.valueOf(0),
        _realmMetricStateDao.findAll().get(0).getPageNumber());
    svc.stop();
  }

  @Test
  public void TestSvcUnchagedStateDocs() {
    _appSettings.setProp(MONITORING_REALM_ENABLED.value, "true", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        MONITORING_REALM_REQUEST_THREADS.value, "1", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        REALM_METRICS_NUM_REQUEST_PAGES_1_MIN.value, "64", AppSettings.SettingType.MEMORY);

    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    final RealmMetricState state =
        new RealmMetricState(
            1, Date.from(Instant.now().minus(REQUEST_GRANULARITY)), REQUEST_GRANULARITY_MILLIS);
    _realmMetricStateDao.insertReplicaSafe(state);

    final RealmMetricCollectionSvc.RealmMetricRequestHandler handler =
        svc.new RealmMetricRequestHandler();

    final Thread handleRequest = new Thread(handler);
    handleRequest.start();
    try {
      // Give time for Handler to run at least once
      Thread.sleep(10_000);
    } catch (InterruptedException e1) {
      throw new RuntimeException(e1);
    }

    // stop the requesting thread from busy-waiting
    handleRequest.interrupt();

    assertEquals(
        "Expected number of RealmMetricStates to be unchanged, as there is no Realm Metric state"
            + " doc associated with page 0",
        1,
        _realmMetricStateDao.countByRequestGranularity(REQUEST_GRANULARITY_MILLIS));
    svc.stop();
  }

  @Test
  public void testFindFirstDuplicateRegions() {
    final RealmMetricCollectionSvc svc =
        new RealmMetricCollectionSvc(
            _pingIngestionSvc,
            _realmPingSourceFactory,
            _realmMetricStateDao,
            _retentionPolicyDao,
            _cacheableGroupSvc,
            _appSettings);

    final RealmMetricRequestHandler _handler = svc.new RealmMetricRequestHandler();
    final RealmRegionMetrics metric1 = new RealmRegionMetrics();
    final RealmRegionMetrics metric2 = new RealmRegionMetrics();
    final RealmRegionMetrics metric3 = new RealmRegionMetrics();

    final List<RealmRegionMetrics> regionMetricsList = List.of(metric1, metric2, metric3);
    metric1.setRegion("us-east-1");
    metric2.setRegion("us-east-2");
    metric3.setRegion("us-east-3");

    assertTrue(_handler.findFirstDuplicateRegion(regionMetricsList).isEmpty());

    metric2.setRegion("us-east-1");
    final Optional<String> result = _handler.findFirstDuplicateRegion(regionMetricsList);
    assertFalse(result.isEmpty());
    assertEquals(result.get(), "us-east-1");
  }
}
