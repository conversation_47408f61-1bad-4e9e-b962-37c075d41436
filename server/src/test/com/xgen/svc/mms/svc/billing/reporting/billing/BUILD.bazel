load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/email",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/module/metering/common/utils",
        "//server/src/main/com/xgen/svc/mms/dao/marketing",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "//server/src/main/com/xgen/svc/mms/util/billing/scenarios",
        "//server/src/main/com/xgen/svc/mms/util/billing/testFactories",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
