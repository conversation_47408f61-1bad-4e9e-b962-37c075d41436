package com.xgen.svc.mms.svc;

import static com.xgen.svc.mms.svc.ChartsSvc.getChartsUsername;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._private.dao.UserApiKeyDao;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.dao.ChartsConfigDao;
import com.xgen.svc.mms.model.ChartsConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ChartsSvcIntTests extends JUnit5BaseResourceTest {

  @Inject private ChartsSvc _chartsSvc;
  @Inject private UserSvc _userSvc;
  @Inject private UserApiKeyDao _userApiKeyDao;
  @Inject private ChartsConfigDao _configDao;
  @Inject private UserDao _userDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private AuthzSvc authzSvc;

  @Test
  public void testCreateChartsUser() throws Exception {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    getChartsSvc().createChartsUser(org.getId(), group.getId());

    final List<AppUser> users = getUserSvc().findLocalUsersByGroupId(group.getId());
    assertEquals(1, users.size());
    final AppUser user = users.get(0);
    assertEquals(getChartsUsername(group.getId()), user.getUsername());
    assertEquals(1, user.getGroupIds().size());
    assertEquals(1, user.getOrgIds().size());
    assertEquals(2, user.getRoles().size());
    assertTrue(authzSvc.hasOrgRole(user, Role.ORG_MEMBER, org.getId()));
    assertTrue(authzSvc.hasGroupRole(user, Role.GROUP_CHARTS_ADMIN, group));
    assertEquals(1, getUserApiKeyDao().countByUserId(user.getId()));

    // Call it a second time to ensure that it's idempotent.
    getChartsSvc().createChartsUser(org.getId(), group.getId());
    assertEquals(1, getUserSvc().findLocalUsersByGroupId(group.getId()).size());
    assertEquals(1, getUserApiKeyDao().countByUserId(user.getId()));
  }

  @Test
  public void testGetCountByVersionAndGetCountByStatus() {
    // arrange
    ObjectId groupId = oid(1);

    ChartsConfig config = new ChartsConfig(groupId, "s1");
    ObjectId groupId2 = oid(2);
    ChartsConfig config2 = new ChartsConfig(groupId2, "s2");

    getConfigDao().save(config);
    getConfigDao().save(config2);
    getConfigDao().setVersion(groupId, "0.0.0");
    getConfigDao().setVersion(groupId2, "0.1.0");

    // act
    List<DBObject> countByVersion = getChartsSvc().getCountByVersion();
    List<DBObject> countByStatus = getChartsSvc().getCountByStatus();

    // assert
    assertEquals(2, countByVersion.size());
    assertEquals(1, countByStatus.size());

    assertEquals("0.0.0", countByVersion.get(0).get("version"));
    assertEquals(1, countByVersion.get(0).get("count"));

    assertEquals("0.1.0", countByVersion.get(1).get("version"));
    assertEquals(1, countByVersion.get(1).get("count"));

    assertEquals("ACTIVATING", countByStatus.get(0).get("status"));
    assertEquals(2, countByStatus.get(0).get("count"));
  }

  @Test
  public void testHasChartsApp() {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    assertFalse(_chartsSvc.hasChartsApp(group.getId()));

    final ChartsConfig config = new ChartsConfig(group.getId(), "s1");
    getConfigDao().save(config);

    assertTrue(_chartsSvc.hasChartsApp(group.getId()));

    // ACTIVATION_FAILED -> no charts app
    getConfigDao().setStatus(group.getId(), ChartsConfig.Status.ACTIVATION_FAILED);
    assertFalse(_chartsSvc.hasChartsApp(group.getId()));
  }

  @Test
  public void testHasActivatedChartsApp() {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    assertFalse(_chartsSvc.hasActivatedChartsApp(group.getId()));

    final ChartsConfig config = new ChartsConfig(group.getId(), "s1");
    getConfigDao().save(config);

    // ACTIVATION_FAILED -> no charts app
    getConfigDao().setStatus(group.getId(), ChartsConfig.Status.ACTIVATION_FAILED);
    assertFalse(_chartsSvc.hasActivatedChartsApp(group.getId()));
    // ACTIVATING -> MongoNav / CloudNav should still use the non activated URL.
    getConfigDao().setStatus(group.getId(), ChartsConfig.Status.ACTIVATING);
    assertFalse(_chartsSvc.hasActivatedChartsApp(group.getId()));

    getConfigDao().setStatus(group.getId(), ChartsConfig.Status.RUNNING);
    assertTrue(_chartsSvc.hasActivatedChartsApp(group.getId()));
  }

  @Test
  public void testUpdateCurrentProjectOrOrg() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group1 = MmsFactory.createGroup(org);
    final Group group2 = MmsFactory.createGroup(org);
    final AppUser appUser = MmsFactory.createUser();

    MmsFactory.createUserWithRoleInGroup(group1, appUser.getPrimaryEmail(), Role.GROUP_OWNER);
    MmsFactory.createUserWithRoleInGroup(group2, appUser.getPrimaryEmail(), Role.GROUP_OWNER);

    final AppUser multiGroupUser = _userDao.findByUsername(appUser.getPrimaryEmail());

    assertEquals(multiGroupUser.getCurrentGroupId(), group2.getId());

    _chartsSvc.updateCurrentProjectOrOrg(multiGroupUser, group1.getId(), org.getId());

    final AppUser updatedUser = _userDao.findByUsername(appUser.getPrimaryEmail());
    assertNotNull(updatedUser);
    assertEquals(updatedUser.getCurrentGroupId(), group1.getId());
  }

  @Test
  public void testUpdateCurrentProjectOrOrg_noProjectAccess() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group1 = MmsFactory.createGroup(org);
    final Group group2 = MmsFactory.createGroup(org);
    final AppUser appUser = MmsFactory.createUser();

    MmsFactory.createUserWithRoleInGroup(group1, appUser.getPrimaryEmail(), Role.GROUP_OWNER);

    final AppUser onlyGroup1User = _userDao.findByUsername(appUser.getPrimaryEmail());

    assertEquals(onlyGroup1User.getCurrentGroupId(), group1.getId());

    _chartsSvc.updateCurrentProjectOrOrg(onlyGroup1User, group2.getId(), org.getId());

    final AppUser updatedUser = _userDao.findByUsername(appUser.getPrimaryEmail());
    assertNotNull(updatedUser);
    // Did not update group, does not have roles.
    assertEquals(updatedUser.getCurrentGroupId(), group1.getId());
  }

  @Test
  public void testUpdateCurrentProjectOrOrg_OrgAndProject() throws SvcException {
    final Organization org1 = MmsFactory.createOrganizationWithNDSPlan();
    final Organization org2 = MmsFactory.createOrganizationWithNDSPlan();
    final Group group1 = MmsFactory.createGroup(org1);
    final Group group2 = MmsFactory.createGroup(org2);
    final AppUser appUser = MmsFactory.createUser();

    MmsFactory.createUserWithRoleInGroup(group1, appUser.getPrimaryEmail(), Role.GROUP_OWNER);
    MmsFactory.createUserWithRoleInGroup(group2, appUser.getPrimaryEmail(), Role.GROUP_OWNER);

    _userDao.addOrgId(appUser.getId(), org1.getId(), Role.ORG_MEMBER);
    _userDao.addOrgId(appUser.getId(), org2.getId(), Role.ORG_MEMBER);
    _userDao.setCurrentOrgId(appUser.getId(), org2.getId());

    final AppUser multiOrgUser = _userDao.findByUsername(appUser.getPrimaryEmail());

    assertEquals(multiOrgUser.getCurrentGroupId(), group2.getId());
    assertEquals(multiOrgUser.getCurrentOrgId(), org2.getId());

    _chartsSvc.updateCurrentProjectOrOrg(multiOrgUser, group1.getId(), org1.getId());

    final AppUser updatedUser = _userDao.findByUsername(appUser.getPrimaryEmail());
    assertNotNull(updatedUser);
    // Updated Org & Group
    assertEquals(updatedUser.getCurrentGroupId(), group1.getId());
    assertEquals(updatedUser.getCurrentOrgId(), org1.getId());
  }

  @Test
  public void testGetChartsProjectClusterDescriptions() {
    ObjectId groupId = oid(1);

    ClusterDescription freeCluster =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new NDSModelTestFactory.TestFreeClusterDescriptionConfig().setGroupId(groupId)));

    final BasicDBObject paidClusterDescriptionDeletingConfig =
        NDSModelTestFactory.getAWSClusterDescription();
    paidClusterDescriptionDeletingConfig.put(ClusterDescription.FieldDefs.DELETE_REQUESTED, true);
    paidClusterDescriptionDeletingConfig.put(ClusterDescription.FieldDefs.GROUP_ID, groupId);
    final ClusterDescription paidClusterDeleting =
        new ClusterDescription(paidClusterDescriptionDeletingConfig);

    final BasicDBObject paidClusterDescriptionDeletedConfig =
        NDSModelTestFactory.getAWSClusterDescription();
    paidClusterDescriptionDeletedConfig.put(
        ClusterDescription.FieldDefs.STATE, ClusterDescription.State.DELETED);
    paidClusterDescriptionDeletedConfig.put(ClusterDescription.FieldDefs.GROUP_ID, groupId);

    final ClusterDescription paidClusterDeleted =
        new ClusterDescription(paidClusterDescriptionDeletedConfig);

    _clusterDescriptionDao.save(freeCluster);
    _clusterDescriptionDao.save(paidClusterDeleted);
    _clusterDescriptionDao.save(paidClusterDeleting);

    final Collection<ClusterDescription> chartsClusterDescriptions =
        _chartsSvc.getChartsProjectClusterDescriptions(groupId);
    assertEquals(1, chartsClusterDescriptions.size());
    final Optional<ClusterDescription> clusterDescriptionOptional =
        chartsClusterDescriptions.stream()
            .filter(
                clusterDescription ->
                    clusterDescription.getUniqueId().equals(freeCluster.getUniqueId()))
            .findFirst();
    assertTrue(clusterDescriptionOptional.isPresent());
  }

  private ChartsSvc getChartsSvc() {
    return _chartsSvc;
  }

  private UserSvc getUserSvc() {
    return _userSvc;
  }

  private UserApiKeyDao getUserApiKeyDao() {
    return _userApiKeyDao;
  }

  private ChartsConfigDao getConfigDao() {
    return _configDao;
  }
}
