load("//server/src/test:rules.bzl", "library_package", "test_package")

library_package(
    name = "event",
    deps = [
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
    ],
)

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = False,
    deps = [
        ":event",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/atm/activity",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/externalanalytics",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:junit_junit",
    ],
)
