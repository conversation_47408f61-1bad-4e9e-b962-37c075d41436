load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/svc/mms/dao/agent",
        "//server/src/main/com/xgen/svc/mms/model/agent",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
