package com.xgen.svc.mms.svc.billing.audit.auditors;

import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_ENTERPRISE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.xgen.cloud.billing._public.svc.marketing.exception.MarketingIntegrationException;
import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditError;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode;
import com.xgen.cloud.billingplatform.crossorg._public.svc.OrgLinkingSvc;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.dao.billing.OrgPlanDao;
import com.xgen.svc.mms.dao.billing.SelfServeProductDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlan.Builder;
import com.xgen.svc.mms.model.billing.PrepaidPlanType;
import com.xgen.svc.mms.model.billing.SelfServeProduct.Type;
import com.xgen.svc.mms.svc.billing.AccountantSvc;
import com.xgen.svc.mms.svc.billing.BillingIntTestSetupHelper;
import com.xgen.svc.mms.svc.billing.InvoiceTestUtils;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import com.xgen.svc.mms.svc.billing.PlanSvc;
import com.xgen.svc.mms.svc.billing.plans.selfServe.SelfServePlanProvider;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory.SalesforceProduct;
import com.xgen.svc.mms.util.billing.testFactories.UsageFactory;
import jakarta.inject.Inject;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SupportUpliftAuditorIntTests extends JUnit5BaseSvcTest {

  // total usage is 5301.36
  //
  private static final Date INVOICE_END_DATE = DateTimeUtils.dateOf(LocalDate.parse("2021-12-01"));
  private static final Date INVOICE_START_DATE =
      DateTimeUtils.dateOf(LocalDate.parse("2021-11-01"));
  private static final Date TEST_ACTIVATED_DATE = DateUtils.addHours(INVOICE_START_DATE, -6);
  private static final Date DAILY_BILLING_DATE =
      InvoiceTestUtils.getDailyBillingStartTime(
          DateTimeUtils.dateOf(LocalDate.parse("2021-11-30")));
  // total usage over the course of a 30 day month is $5301.36
  // for single plan 5301.36 * 20% = 1060.26
  // If we run daily bill for 29 days total usage will be 5124.65, single plan 1024.91
  private static final long EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN = 102491L;
  // for two plans for first plan 14 days = 2,473.968 plan 1 has a 20% uplift
  // for second plan which is 15 days = 2,650.5 plan 2 has a 70% uplift
  // 2,473.968 * 20% + $2,650.68 * 70% = 2350.2696 , there is a bit of rounding error, the actual
  // value is 2350.23
  private static final long EXPECTED_UPLIFT_AMOUNT_TWO_PLAN = 235023L;
  // in cross org the uplift is 3x what the single plan uplift is because there are 3 orgs
  private static final long EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN_CROSS_ORG = 307473L;
  // Calculating charges using NDS_ENTITLEMENTS vs auditor which checks non support SKUs * uplift
  // percentage gives out difference of few cents based on where we round off amounts.
  private static final long EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN_CROSS_ORG_ROUNDING_ERROR = 307475L;
  // in cross org the uplift is 3x what the single plan uplift is because there are 3 orgs
  private static final long EXPECTED_UPLIFT_AMOUNT_TWO_PLAN_CROSS_ORG = 705069L;
  private static final long EXPECTED_UPLIFT_AMOUNT_TWO_PLAN_CROSS_ORG_ROUNDING_ERROR = 705072L;
  // total usage over the course of a 30 day month is $5301.36
  // first tier is up to 25k so all usage fits in first tier 5301.36 * 70% = 3710.95
  // If we run daily bill for 29 days total usage will be 5124.65, uplift is 3587.21
  private static final long EXPECTED_UPLIFT_AMOUNT_TIERED_PLAN = 358721L;
  // in cross org the uplift is 2x what the single tiered plan uplift is
  // (EXPECTED_UPLIFT_AMOUNT_TIERED_PLAN=358721) because there are 2 orgs
  private static final long EXPECTED_UPLIFT_AMOUNT_SINGLE_TIERED_PLAN_CROSS_ORG = 717442L;
  private static final long DEVELOPER_MINIMUM = 4900L;
  private static final long PRO_MINIMUM = 79900L;
  // Pro minimum charge per day for $799 comes out to be 26.63. On some days we charge 26.64.
  private static final long PRO_MINIMUM_29DAYS = 77236L;
  @Inject private SupportUpliftAuditor supportUpliftAuditor;
  @Inject private LineItemDao lineItemDao;
  @Inject private InvoiceDao invoiceDao;

  @Inject
  private com.xgen.cloud.billingplatform.process.invoicelocking._private.dao.InvoiceDao
      invoiceLockingDao;

  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;
  @Inject private AccountantSvc accountantSvc;
  @Inject private UsageFactory usageFactory;
  @Inject private PayingOrgSvc payingOrgSvc;
  @Inject private PlanSvc planSvc;
  @Inject private OrganizationFactory organizationFactory;
  @Inject private CreditFactory creditFactory;
  @Inject private OrganizationDao organizationDao;
  @Inject private OrgLinkingSvc orgLinkingSvc;
  @Inject private BillingIntTestSetupHelper billingIntTestSetupHelper;
  @Inject private OrgPlanDao orgPlanDao;
  @Inject private SelfServePlanProvider selfServePlanProvider;
  @Inject private SelfServeProductDao selfServeProductDao;
  @Inject private PaymentMethodStubber paymentMethodStubber;
  private Organization org;
  private Group group;
  private Organization linkedOrg1;
  private Organization linkedOrg2;
  private Group linkedOrg1Group;
  private Group linkedOrg2Group;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    org = organizationFactory.createAtlasOrganization(INVOICE_START_DATE);
    linkedOrg1 = organizationFactory.createAtlasOrganization(INVOICE_START_DATE);
    linkedOrg2 = organizationFactory.createAtlasOrganization(INVOICE_START_DATE);
    group = organizationFactory.createNdsGroup(org, INVOICE_START_DATE);
    linkedOrg1Group = organizationFactory.createNdsGroup(linkedOrg1, INVOICE_START_DATE);
    linkedOrg2Group = organizationFactory.createNdsGroup(linkedOrg2, INVOICE_START_DATE);
    salesforceProductCodeDao.syncDocuments();
    selfServeProductDao.syncDocuments();
    supportUpliftAuditor = spy(supportUpliftAuditor);
  }

  @Test
  public void auditPass_singleFlatPlan()
      throws SvcException, MarketingIntegrationException, IOException {
    generateUsageAndSingleFlatPlan();
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singleSelfServeFlatPlan()
      throws SvcException, MarketingIntegrationException {
    generateUsageAndSingleSelfServeFlatPlan();
    when(supportUpliftAuditor.now()).thenReturn(TEST_ACTIVATED_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singleTieredPlan()
      throws SvcException, MarketingIntegrationException, IOException {
    generateUsageAndSingleTieredPlan();
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_TIERED_PLAN, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_twoTieredPlansWithSamePricing()
      throws SvcException, MarketingIntegrationException, IOException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    Date midMonthPlanChange = DateUtils.addDays(INVOICE_START_DATE, 10);

    billingIntTestSetupHelper.createAndActivateTieredPlanOpportunity(
        org,
        SalesforceProduct.ATLAS_ENTERPRISE_AZURE,
        false,
        10000000L,
        INVOICE_START_DATE,
        midMonthPlanChange,
        INVOICE_START_DATE);

    accountantSvc.processOrganizations(
        List.of(org.getId()), DateUtils.addHours(midMonthPlanChange, 2));
    billingIntTestSetupHelper.createAndActivateTieredPlanOpportunity(
        org,
        SalesforceProduct.ATLAS_ENTERPRISE_AZURE,
        false,
        10000000L,
        midMonthPlanChange,
        INVOICE_END_DATE,
        midMonthPlanChange);
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_TIERED_PLAN, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
    List<OrgPlan> enterprisePlans =
        orgPlanDao.findAllByOrgId(org.getId()).stream()
            .filter(orgPlan -> orgPlan.getPlanType() == NDS_ENTERPRISE)
            .collect(Collectors.toList());
    assertEquals(2, enterprisePlans.size());
    assertTrue(
        enterprisePlans.stream()
            .anyMatch(
                plan ->
                    plan.getStartDate().equals(INVOICE_START_DATE)
                        && plan.getEndDate().equals(midMonthPlanChange)));
    assertTrue(
        enterprisePlans.stream()
            .anyMatch(
                plan ->
                    plan.getStartDate().equals(midMonthPlanChange)
                        && plan.getEndDate().equals(INVOICE_END_DATE)));
  }

  @Test
  public void auditPass_crossOrg_tieredPlan()
      throws SvcException, MarketingIntegrationException, IOException {
    generateUsageAndSingleTieredPlanCrossOrg();

    orgLinkingSvc.forceLinkOrgsSyncAndRebillSync(
        org.getId(),
        List.of(linkedOrg1.getId()),
        AuditInfoHelpers.fromInternal(),
        DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Invoice linkedOrg1Invoice = invoiceDao.findByOrgId(linkedOrg1.getId(), true).get(0);

    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId()), SKU.NDS_ENTITLEMENTS);

    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_TIERED_PLAN_CROSS_ORG, supportAmount);

    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_deletedOrgSkipAudit() {
    organizationDao.markDeleted(org.getId(), new Date());
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singlePlan_planStartsAndEndOutsideInvoiceRange()
      throws SvcException, MarketingIntegrationException, IOException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    billingIntTestSetupHelper.createAndActivateOpportunity(
        org,
        SalesforceOpportunityFactory.SalesforceProduct.ATLAS_PRO,
        false,
        10000000L,
        DateUtils.addMonths(INVOICE_START_DATE, -1),
        DateUtils.addMonths(INVOICE_END_DATE, 1),
        DateUtils.addMonths(INVOICE_START_DATE, -1));
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singlePlan_noUsage()
      throws SvcException, MarketingIntegrationException, IOException {
    billingIntTestSetupHelper.createAndActivateOpportunity(
        org,
        SalesforceOpportunityFactory.SalesforceProduct.ATLAS_PRO,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    // there is no usage, so only the minimum should be on invoice
    assertEquals(PRO_MINIMUM_29DAYS, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singlePlan_noUsage_midMonth()
      throws SvcException, MarketingIntegrationException, IOException {
    billingIntTestSetupHelper.createAndActivateOpportunity(
        org,
        SalesforceOpportunityFactory.SalesforceProduct.ATLAS_PRO,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
    Date midMonthBillDate =
        InvoiceTestUtils.getDailyBillingStartTime(
            DateTimeUtils.dateOf(LocalDate.parse("2021-11-15")));
    accountantSvc.processOrganizations(List.of(org.getId()), midMonthBillDate);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    // there is no usage, so only the minimum should be on invoice
    assertEquals(37286L, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singlePlan_noEndDate() throws SvcException, IOException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    Credit credit =
        creditFactory.createPrepaidCredit(
            org.getId(), INVOICE_START_DATE, INVOICE_END_DATE, 1000000L, false);

    List<PrepaidPlan> prepaidPlans =
        List.of(
            new Builder(PrepaidPlanType.NDS_PRO, SKU.PREMIUM_NDS_UNCHARGED_SKUS)
                .startDate(INVOICE_START_DATE)
                .flatSubscriptionPricingModel(DEVELOPER_MINIMUM, 20)
                .creditId(credit.getId())
                .build());
    OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(org.getId())
            .planType(NDS_ENTERPRISE)
            .startDate(INVOICE_START_DATE)
            .flatSubscriptionPricingModel(0, 20L)
            .build();
    planSvc.initPlan(org.getId(), plan, prepaidPlans);
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    // there is no usage, so only the minimum should be on invoice
    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_singlePlan_withBackfillCharge()
      throws SvcException, MarketingIntegrationException, IOException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    billingIntTestSetupHelper.createAndActivateOpportunity(
        org,
        SalesforceOpportunityFactory.SalesforceProduct.ATLAS_PRO,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    // there is no usage, so only the minimum should be on invoice
    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN, supportAmount);
    createSupportLineItemForAmount(invoice.getId(), true);
    supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    // with a backfill charge, we know the sum of support line items is greater than the minimum now
    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN + 79900L, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditFail_singlePlan()
      throws SvcException, MarketingIntegrationException, IOException {
    generateUsageAndSingleFlatPlan();
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN, supportAmount);

    // delete support line items so we can force a failure
    deleteSupportLineItemsForInvoice(invoice);
    supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(0, supportAmount);

    // add back support line item for minimum
    createSupportLineItemForAmount(invoice.getId(), false);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isPresent());
    assertFailureResult(result.get(), invoice, EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN, PRO_MINIMUM);
  }

  @Test
  public void auditPass_multiplePlans() throws Exception {
    generateUsageAndTwoPlans();
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_TWO_PLAN, supportAmount);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditFail_multiplePlans() throws Exception {
    generateUsageAndTwoPlans();
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);
    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(EXPECTED_UPLIFT_AMOUNT_TWO_PLAN, supportAmount);

    // delete support line items so we can force a failure
    deleteSupportLineItemsForInvoice(invoice);
    supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId()), SKU.NDS_ENTITLEMENTS);
    assertEquals(0, supportAmount);

    // add back support line item for minimum
    createSupportLineItemForAmount(invoice.getId(), false);
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isPresent());
    assertFailureResult(result.get(), invoice, EXPECTED_UPLIFT_AMOUNT_TWO_PLAN, PRO_MINIMUM);
  }

  @Test
  public void auditPass_crossOrg_singlePlan() throws Exception {
    generateUsageAndSinglePlanCrossOrg();

    orgLinkingSvc.forceLinkOrgsSyncAndRebillSync(
        org.getId(),
        List.of(linkedOrg1.getId(), linkedOrg2.getId()),
        AuditInfoHelpers.fromInternal(),
        DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Invoice linkedOrg1Invoice = invoiceDao.findByOrgId(linkedOrg1.getId(), true).get(0);
    Invoice linkedOrg2invoice = invoiceDao.findByOrgId(linkedOrg2.getId(), true).get(0);

    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId(), linkedOrg2invoice.getId()),
            SKU.NDS_ENTITLEMENTS);

    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN_CROSS_ORG, supportAmount);

    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditPass_crossOrg_multiplePlans() throws Exception {
    generateUsageAndTwoPlansCrossOrg();
    orgLinkingSvc.forceLinkOrgsSyncAndRebillSync(
        org.getId(),
        List.of(linkedOrg1.getId(), linkedOrg2.getId()),
        AuditInfoHelpers.fromInternal(),
        DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Invoice linkedOrg1Invoice = invoiceDao.findByOrgId(linkedOrg1.getId(), true).get(0);
    Invoice linkedOrg2invoice = invoiceDao.findByOrgId(linkedOrg2.getId(), true).get(0);

    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId(), linkedOrg2invoice.getId()),
            SKU.NDS_ENTITLEMENTS);

    assertEquals(EXPECTED_UPLIFT_AMOUNT_TWO_PLAN_CROSS_ORG, supportAmount);

    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditFail_crossOrg_singlePlan() throws Exception {
    generateUsageAndSinglePlanCrossOrg();

    orgLinkingSvc.forceLinkOrgsSyncAndRebillSync(
        org.getId(),
        List.of(linkedOrg1.getId(), linkedOrg2.getId()),
        AuditInfoHelpers.fromInternal(),
        DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Invoice linkedOrg1Invoice = invoiceDao.findByOrgId(linkedOrg1.getId(), true).get(0);
    Invoice linkedOrg2invoice = invoiceDao.findByOrgId(linkedOrg2.getId(), true).get(0);

    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId(), linkedOrg2invoice.getId()),
            SKU.NDS_ENTITLEMENTS);

    assertEquals(EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN_CROSS_ORG, supportAmount);

    deleteSupportLineItemsForInvoice(invoice);
    deleteSupportLineItemsForInvoice(linkedOrg1Invoice);
    deleteSupportLineItemsForInvoice(linkedOrg2invoice);

    supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId(), linkedOrg2invoice.getId()),
            SKU.NDS_ENTITLEMENTS);

    // verify we actually deleted all the support line items
    assertEquals(0, supportAmount);

    // add back support line item for minimum
    createSupportLineItemForAmount(invoice.getId(), false);

    // theres a slight difference (2 cents) in the line item sum and the expected amount due to
    // rounding
    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isPresent());
    assertFailureResult(
        result.get(),
        invoice,
        EXPECTED_UPLIFT_AMOUNT_SINGLE_PLAN_CROSS_ORG_ROUNDING_ERROR,
        PRO_MINIMUM);
  }

  @Test
  public void auditFail_crossOrg_multiplePlans() throws Exception {
    generateUsageAndTwoPlansCrossOrg();
    orgLinkingSvc.forceLinkOrgsSyncAndRebillSync(
        org.getId(),
        List.of(linkedOrg1.getId(), linkedOrg2.getId()),
        AuditInfoHelpers.fromInternal(),
        DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    Invoice linkedOrg1Invoice = invoiceDao.findByOrgId(linkedOrg1.getId(), true).get(0);
    Invoice linkedOrg2invoice = invoiceDao.findByOrgId(linkedOrg2.getId(), true).get(0);

    long supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId(), linkedOrg2invoice.getId()),
            SKU.NDS_ENTITLEMENTS);

    assertEquals(EXPECTED_UPLIFT_AMOUNT_TWO_PLAN_CROSS_ORG, supportAmount);

    deleteSupportLineItemsForInvoice(invoice);
    deleteSupportLineItemsForInvoice(linkedOrg1Invoice);
    deleteSupportLineItemsForInvoice(linkedOrg2invoice);

    supportAmount =
        lineItemDao.getAggregateCentsForInvoicesWithSku(
            List.of(invoice.getId(), linkedOrg1Invoice.getId(), linkedOrg2invoice.getId()),
            SKU.NDS_ENTITLEMENTS);

    // verify we actually deleted all the support line items
    assertEquals(0, supportAmount);

    // add back support line item for minimum
    createSupportLineItemForAmount(invoice.getId(), false);

    Optional<AuditFailureDetail> result = supportUpliftAuditor.audit(invoice);
    assertTrue(result.isPresent());
    // theres a slight difference (3 cents) in the line item sum and the expected amount due to
    // rounding
    assertFailureResult(
        result.get(),
        invoice,
        EXPECTED_UPLIFT_AMOUNT_TWO_PLAN_CROSS_ORG_ROUNDING_ERROR,
        PRO_MINIMUM);

    deleteSupportLineItemsForInvoice(invoice);
    LineItem lineItem =
        new LineItem.Builder()
            .startDate(INVOICE_START_DATE)
            .sku(SKU.NDS_ENTITLEMENTS)
            .endDate(DateTimeUtils.dateOf(LocalDate.parse("2021-11-29")))
            .invoiceId(invoice.getId())
            .totalPriceCents(705072)
            .isBackfillCharge(false)
            .build();
    lineItemDao.save(lineItem);

    Optional<AuditFailureDetail> result1 = supportUpliftAuditor.audit(invoice);
    assertTrue(result1.isPresent());
    // 3 errors, one for each project
    assertEquals(3, result1.get().getBillingAuditErrors().size());
    List<BillingAuditError> errors = result1.get().getBillingAuditErrors();
    assertTrue(errors.get(0).getErrorMessage().contains("projectId"));
  }

  @Test
  public void getIdsToAudit_lockedInvoicePresent_LockedInvoiceGetsFilteredOut() {
    accountantSvc.processOrganizations(List.of(org.getId()), DAILY_BILLING_DATE);

    Invoice invoice = invoiceDao.findByOrgId(org.getId(), true).get(0);
    invoiceLockingDao.tryLockInvoice(invoice.getId(), "test", new Date());
    assertEquals(0, supportUpliftAuditor.getIdsToAudit().size());
  }

  private void generateUsageAndSingleFlatPlan() throws SvcException, MarketingIntegrationException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    billingIntTestSetupHelper.createAndActivateOpportunity(
        org,
        SalesforceOpportunityFactory.SalesforceProduct.ATLAS_PRO,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
  }

  private void generateUsageAndSingleSelfServeFlatPlan() throws SvcException {
    paymentMethodStubber.stubPaymentMethod(org.getId());

    selfServePlanProvider
        .get(Type.ATLAS_DEVELOPER)
        .activate(org.getId(), TEST_ACTIVATED_DATE, AuditInfoHelpers.fromSystem());
  }

  private void generateUsageAndSingleTieredPlan()
      throws SvcException, MarketingIntegrationException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    billingIntTestSetupHelper.createAndActivateTieredPlanOpportunity(
        org,
        SalesforceProduct.ATLAS_ENTERPRISE,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
  }

  private void generateUsageAndSinglePlanCrossOrg()
      throws SvcException, MarketingIntegrationException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), linkedOrg1Group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), linkedOrg2Group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    billingIntTestSetupHelper.createAndActivateOpportunity(
        org,
        SalesforceOpportunityFactory.SalesforceProduct.ATLAS_PRO,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
  }

  private void generateUsageAndSingleTieredPlanCrossOrg()
      throws SvcException, MarketingIntegrationException {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), linkedOrg1Group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    billingIntTestSetupHelper.createAndActivateTieredPlanOpportunity(
        org,
        SalesforceProduct.ATLAS_ENTERPRISE,
        false,
        10000000L,
        INVOICE_START_DATE,
        INVOICE_END_DATE,
        INVOICE_START_DATE);
  }

  private void generateUsageAndTwoPlans() {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    Date plan1EndDate = DateUtils.addDays(INVOICE_START_DATE, 14);

    Credit credit1 =
        creditFactory.createPrepaidCredit(
            org.getId(), INVOICE_START_DATE, plan1EndDate, 1000000L, false);

    Credit credit2 =
        creditFactory.createPrepaidCredit(
            org.getId(), plan1EndDate, INVOICE_END_DATE, 10000000L, false);

    List<PrepaidPlan> prepaidPlans =
        List.of(
            new Builder(PrepaidPlanType.NDS_PRO, SKU.PREMIUM_NDS_UNCHARGED_SKUS)
                .startDate(INVOICE_START_DATE)
                .endDate(plan1EndDate)
                .flatSubscriptionPricingModel(DEVELOPER_MINIMUM, 20)
                .creditId(credit1.getId())
                .build(),
            new Builder(PrepaidPlanType.NDS_ENTERPRISE, SKU.PREMIUM_NDS_UNCHARGED_SKUS)
                .startDate(plan1EndDate)
                .endDate(INVOICE_END_DATE)
                .flatSubscriptionPricingModel(PRO_MINIMUM, 70)
                .creditId(credit2.getId())
                .build());
    OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(org.getId())
            .planType(NDS_ENTERPRISE)
            .startDate(INVOICE_START_DATE)
            .endDate(INVOICE_END_DATE)
            .flatSubscriptionPricingModel(0, 20L)
            .build();
    planSvc.initPlan(org.getId(), plan, prepaidPlans);
  }

  private void generateUsageAndTwoPlansCrossOrg() {
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), linkedOrg1Group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);
    usageFactory.generateHourlyAwsR400Usage(
        ObjectId.get(), linkedOrg2Group.getId(), 1, INVOICE_START_DATE, INVOICE_END_DATE);

    Date plan1EndDate = DateUtils.addDays(INVOICE_START_DATE, 14);

    Credit credit1 =
        creditFactory.createPrepaidCredit(
            org.getId(), INVOICE_START_DATE, plan1EndDate, 1000000L, false);

    Credit credit2 =
        creditFactory.createPrepaidCredit(
            org.getId(), plan1EndDate, INVOICE_END_DATE, 10000000L, false);

    List<PrepaidPlan> prepaidPlans =
        List.of(
            new Builder(PrepaidPlanType.NDS_PRO, SKU.PREMIUM_NDS_UNCHARGED_SKUS)
                .startDate(INVOICE_START_DATE)
                .endDate(plan1EndDate)
                .flatSubscriptionPricingModel(DEVELOPER_MINIMUM, 20)
                .creditId(credit1.getId())
                .build(),
            new Builder(PrepaidPlanType.NDS_ENTERPRISE, SKU.PREMIUM_NDS_UNCHARGED_SKUS)
                .startDate(plan1EndDate)
                .endDate(INVOICE_END_DATE)
                .flatSubscriptionPricingModel(PRO_MINIMUM, 70)
                .creditId(credit2.getId())
                .build());
    OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(org.getId())
            .planType(NDS_ENTERPRISE)
            .startDate(INVOICE_START_DATE)
            .endDate(INVOICE_END_DATE)
            .flatSubscriptionPricingModel(0, 20L)
            .build();
    planSvc.initPlan(org.getId(), plan, prepaidPlans);
  }

  private void deleteSupportLineItemsForInvoice(Invoice invoice) {
    List<LineItem> supportLineItems =
        lineItemDao.findSupportLineItemByInvoiceSinceStartDate(
            invoice.getId(), invoice.getStartDate());
    supportLineItems.forEach(lineItem -> lineItemDao.delete(lineItem.getId()));
  }

  private void assertFailureResult(
      AuditFailureDetail auditFailureDetail,
      Invoice invoice,
      long expectedAmount,
      long actualAmount) {
    assertEquals(supportUpliftAuditor.getAuditorName(), auditFailureDetail.getAuditorName());
    assertEquals(invoice.getId(), auditFailureDetail.getAuditContext().getInvoiceId());
    assertEquals(invoice.getOrgId(), auditFailureDetail.getAuditContext().getOrgId());
    List<BillingAuditError> billingAuditErrors = auditFailureDetail.getBillingAuditErrors();
    assertEquals(1, billingAuditErrors.size());
    assertEquals(
        BillingAuditErrorCode.SUPPORT_CHARGES_DO_NOT_MATCH,
        billingAuditErrors.get(0).getErrorCode());
    BillingAuditError expectedBillingAuditError =
        new BillingAuditError.Builder(BillingAuditErrorCode.SUPPORT_CHARGES_DO_NOT_MATCH)
            .addArg("expectedSupportCharges", expectedAmount)
            .addArg("actualSupportCharges", actualAmount)
            .build();
    assertEquals(
        expectedBillingAuditError.getErrorMessage(), billingAuditErrors.get(0).getErrorMessage());
  }

  private void createSupportLineItemForAmount(ObjectId invoiceId, boolean isBackfillCharge) {
    LineItem lineItem =
        new LineItem.Builder()
            .startDate(INVOICE_START_DATE)
            .sku(SKU.NDS_ENTITLEMENTS)
            .endDate(DateTimeUtils.dateOf(LocalDate.parse("2021-11-02")))
            .invoiceId(invoiceId)
            .totalPriceCents(PRO_MINIMUM)
            .isBackfillCharge(isBackfillCharge)
            .build();
    lineItemDao.save(lineItem);
  }
}
