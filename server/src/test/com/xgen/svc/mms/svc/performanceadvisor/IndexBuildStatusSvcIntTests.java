package com.xgen.svc.mms.svc.performanceadvisor;

import static com.xgen.svc.common.MmsFactory.createGroupWithPremiumPlan;
import static com.xgen.svc.common.MmsFactory.createReplicaSet_V_2_4;
import static com.xgen.svc.common.MmsFactory.createStandalone;
import static java.time.temporal.ChronoUnit.DAYS;

import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._private.dao.AgentJobsProcessorDao;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob.Command.Type;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob.Status;
import com.xgen.cloud.common.jobqueue._public.model.AgentJobHandler;
import com.xgen.cloud.cron._private.dao.CronStateDao;
import com.xgen.cloud.explorer.activity._public.audit.IndexBuildAudit;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.explorer.BuildingIndexDao;
import com.xgen.svc.mms.dao.explorer.DataExplorerResponseDao;
import com.xgen.svc.mms.model.explorer.BuildingIndex;
import com.xgen.svc.mms.util.PerfomanceAdvisorResourceIntTestHelper.AutomationJobMockResponseRunner;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.BasicBSONObject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class IndexBuildStatusSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private IndexBuildStatusSvc _indexBuildStatusSvc;
  @Inject private BuildingIndexDao _buildingIndexDao;
  @Inject private HostSvc _hostSvc;
  @Inject private AgentJobsProcessorDao _jobsProcessorDao;
  @Inject private DataExplorerResponseDao _dataExplorerResponseDao;
  @Inject private AuditSvc _auditSvc;

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private CronStateDao _cronStateDao;

  private static final String DB = "smol";
  private static final String COLL = "pupper";
  private static final String NAMESPACE = DB + "." + COLL;
  private static final String REPL_SET_NAME_1 = "replicaSet1";
  private static final String VERSION = "4.8.2.2491";
  private static final String INDEX_NAME = "idxName_autocreated";
  private static final String HOSTNAME = "intelMac";
  private static final String NDS_CLUSTER_NAME = "atlasCluster0";

  private Organization _org;
  private Group _group;
  private Organization _atlasOrg;
  private Group _atlasGroup;
  private HostCluster _hostCluster;
  private Host _host;
  private String _standaloneHostId;
  private ClusterDescription _clusterDescription;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _org = MmsFactory.createOrganizationWithPremiumPlan();
    _group = MmsFactory.createGroup(_org);

    _atlasOrg = MmsFactory.createOrganizationWithNDSPlan();
    _atlasGroup = MmsFactory.createGroup(_atlasOrg);

    _hostCluster = MmsFactory.createReplicaSet_V_4_4(_group.getId(), REPL_SET_NAME_1);
    MmsFactory.createAutomationAgentAuditEntry(_group, REPL_SET_NAME_1, VERSION, new Date());
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _org, FeatureFlag.PA_BUGS_AND_SUSTAINABILITY);

    _hostSvc.loadHostClusterHosts(_hostCluster);
    _host = _hostCluster.getPrimaryHosts().get(0);

    _standaloneHostId = createStandalone(_group, HOSTNAME, 47017);
    MmsFactory.createAutomationAgentAuditEntry(_group, HOSTNAME, "4.8.2.2491", new Date());

    _clusterDescription =
        NDSModelTestFactory.getClusterDescription(_group.getId(), CloudProvider.AWS)
            .copy()
            .setIsMTM(false)
            .setName(NDS_CLUSTER_NAME)
            .build();

    // manually disabling the buildingIndexingBuildProgress cron job to avoid the cron job checking
    // for index progress
    // and failing occasionally.
    _cronStateDao.setManuallyDisabled("buildingIndexingBuildProgress", true);
    _cronStateDao.setManuallyDisabled("serverlessAutoIndexingBuildProgress", true);
  }

  @Test
  void checkInProgressIndexesFailed() throws Exception {
    final AgentJob agentJob =
        new AgentJob(
            ObjectId.get(),
            _group.getId(),
            new ObjectId(),
            "localhost",
            Type.DataExplorerOp,
            AgentJobHandler.class,
            new BasicDBObject(),
            Date.from(Instant.now().plus(2, DAYS)));

    agentJob.updateStatus(Status.FAILED, Optional.of("Mistake"));
    final ObjectId agentJobId = agentJob.getId();
    _jobsProcessorDao.putJob(agentJob);

    final BuildingIndex index =
        createIndex(_group.getId(), _hostCluster.getClusterId(), null, agentJobId, NAMESPACE);
    _buildingIndexDao.insertMajority(index);
    List<BuildingIndex> inProgressBuildingIndexes =
        _buildingIndexDao.findInProgressBuildingIndexes();
    Assertions.assertEquals(1, inProgressBuildingIndexes.size());
    _indexBuildStatusSvc.checkIndexingBuildProgress();
    final BuildingIndex failedIndex = _buildingIndexDao.findById(index.getId()).get();
    Assertions.assertEquals(BuildingIndex.Status.FAILED, failedIndex.getStatus());
    Assertions.assertEquals("UNKNOWN", failedIndex.getFailedReason());

    final List<Event> failedAudit =
        _auditSvc.findAll(
            10,
            Collections.singletonList(
                com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent.Type
                    .INDEX_FAILED_INDEX_BUILD
                    .toString()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));
    Assertions.assertEquals(1, failedAudit.size());
    final IndexBuildAudit audit = (IndexBuildAudit) failedAudit.get(0);
    Assertions.assertEquals(INDEX_NAME, audit.getIndexName());
    Assertions.assertEquals(_hostCluster.getClusterName(), audit.getClusterName());
    Assertions.assertEquals(_group.getId(), audit.getGroupId());
    Assertions.assertNull(audit.getHostname());
  }

  @Test
  void checkInProgressIndexesSucceeding() throws Exception {
    final BuildingIndex index = createAgentJobsAndIndexes(INDEX_NAME);
    List<BuildingIndex> inProgressBuildingIndexes =
        _buildingIndexDao.findInProgressBuildingIndexes();
    Assertions.assertEquals(1, inProgressBuildingIndexes.size());

    final AutomationJobMockResponseRunner indexStatsJobRunner =
        new AutomationJobMockResponseRunner(
            _dataExplorerResponseDao,
            _jobsProcessorDao,
            _host.getId(),
            Map.of(NAMESPACE, List.of(new BasicBSONObject("name", INDEX_NAME))),
            Map.of(),
            Map.of(),
            List.of(NAMESPACE),
            false,
            null,
            Set.of());
    indexStatsJobRunner.start();
    Thread.sleep(2500);
    _indexBuildStatusSvc.checkIndexingBuildProgress();
    final BuildingIndex completedIndex = _buildingIndexDao.findById(index.getId()).get();
    indexStatsJobRunner.stopThread();

    Assertions.assertEquals(BuildingIndex.Status.COMPLETED, completedIndex.getStatus());
    Assertions.assertNull(completedIndex.getFailedReason());

    final List<Event> successAudit =
        _auditSvc.findAll(
            10,
            Collections.singletonList(
                com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent.Type
                    .INDEX_SUCCESS_INDEX_BUILD
                    .toString()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));
    Assertions.assertEquals(1, successAudit.size());
    final IndexBuildAudit audit = (IndexBuildAudit) successAudit.get(0);
    Assertions.assertEquals(INDEX_NAME, audit.getIndexName());
    Assertions.assertEquals(_hostCluster.getClusterName(), audit.getClusterName());
    Assertions.assertEquals(_group.getId(), audit.getGroupId());
    Assertions.assertNull(audit.getHostname());
  }

  @Test
  void checkInProgressIndexesManySucceeding() throws Exception {
    final BuildingIndex index1 = createAgentJobsAndIndexes(INDEX_NAME + "_1");
    final BuildingIndex index2 = createAgentJobsAndIndexes(INDEX_NAME + "_2");
    final BuildingIndex index3 = createAgentJobsAndIndexes(INDEX_NAME + "_3");

    List<BuildingIndex> inProgressBuildingIndexes =
        _buildingIndexDao.findInProgressBuildingIndexes();
    Assertions.assertEquals(3, inProgressBuildingIndexes.size());

    final AutomationJobMockResponseRunner indexStatsJobRunner =
        new AutomationJobMockResponseRunner(
            _dataExplorerResponseDao,
            _jobsProcessorDao,
            _host.getId(),
            Map.of(
                NAMESPACE,
                List.of(
                    new BasicBSONObject("name", INDEX_NAME + "_1"),
                    new BasicBSONObject("name", INDEX_NAME + "_2"),
                    new BasicBSONObject("name", INDEX_NAME + "_3"))),
            Map.of(),
            Map.of(),
            List.of(NAMESPACE),
            false,
            null,
            Set.of());
    indexStatsJobRunner.start();
    Thread.sleep(2500);
    _indexBuildStatusSvc.checkIndexingBuildProgress();
    indexStatsJobRunner.stopThread();

    final BuildingIndex completedIndex1 = _buildingIndexDao.findById(index1.getId()).get();
    Assertions.assertEquals(BuildingIndex.Status.COMPLETED, completedIndex1.getStatus());
    Assertions.assertNull(completedIndex1.getFailedReason());

    final BuildingIndex completedIndex2 = _buildingIndexDao.findById(index2.getId()).get();
    Assertions.assertEquals(BuildingIndex.Status.COMPLETED, completedIndex2.getStatus());
    Assertions.assertNull(completedIndex2.getFailedReason());

    final BuildingIndex completedIndex3 = _buildingIndexDao.findById(index3.getId()).get();
    Assertions.assertEquals(BuildingIndex.Status.COMPLETED, completedIndex3.getStatus());
    Assertions.assertNull(completedIndex3.getFailedReason());

    final List<Event> successAudit =
        _auditSvc.findAll(
            10,
            Collections.singletonList(
                com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent.Type
                    .INDEX_SUCCESS_INDEX_BUILD
                    .toString()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));
    Assertions.assertEquals(3, successAudit.size());
  }

  private BuildingIndex createAgentJobsAndIndexes(final String pIndexName) {
    final AgentJob createIndexJob = createAgentJobForCreateIndex(_group, _host, _hostCluster);
    final ObjectId createIndexJobId = createIndexJob.getId();
    _jobsProcessorDao.putJob(createIndexJob);

    final BuildingIndex index =
        createIndex(
            _group.getId(),
            _hostCluster.getClusterId(),
            null,
            createIndexJobId,
            NAMESPACE,
            pIndexName);
    _buildingIndexDao.insertMajority(index);
    return index;
  }

  @Test
  void checkInProgressStandaloneSuccess() throws Exception {
    final AgentJob createIndexJob = createAgentJobForStandalone(_group, HOSTNAME);
    createIndexJob.updateStatus(Status.COMPLETED, Optional.of("Mistake"));
    final ObjectId createIndexJobId = createIndexJob.getId();
    _jobsProcessorDao.putJob(createIndexJob);

    final AgentJob agentJob = createAgentJobForListIndexStats(_group, HOSTNAME, null);
    _jobsProcessorDao.putJob(agentJob);

    final BuildingIndex index =
        createIndex(_group.getId(), null, _standaloneHostId, createIndexJobId, NAMESPACE);
    _buildingIndexDao.insertMajority(index);

    final AutomationJobMockResponseRunner indexStatsJobRunner =
        new AutomationJobMockResponseRunner(
            _dataExplorerResponseDao,
            _jobsProcessorDao,
            _standaloneHostId,
            Map.of(NAMESPACE, List.of(new BasicBSONObject("name", INDEX_NAME))),
            Map.of(),
            Map.of(),
            List.of(NAMESPACE),
            false,
            null,
            Set.of());
    indexStatsJobRunner.start();
    Thread.sleep(2500);

    _indexBuildStatusSvc.checkIndexingBuildProgress();
    final BuildingIndex successIndex = _buildingIndexDao.findById(index.getId()).get();
    indexStatsJobRunner.stopThread();

    Assertions.assertEquals(BuildingIndex.Status.COMPLETED, successIndex.getStatus());

    final List<Event> successAudit =
        _auditSvc.findAll(
            10,
            Collections.singletonList(
                com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent.Type
                    .INDEX_SUCCESS_INDEX_BUILD
                    .toString()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));
    Assertions.assertEquals(1, successAudit.size());
    final IndexBuildAudit audit = (IndexBuildAudit) successAudit.get(0);
    Assertions.assertEquals(INDEX_NAME, audit.getIndexName());
    Assertions.assertEquals("intelMac:47017", audit.getHostname());
    Assertions.assertEquals(_group.getId(), audit.getGroupId());
    Assertions.assertNull(audit.getClusterName());
  }

  @Test
  void checkInProgressStandaloneFailed() throws Exception {
    final AgentJob createIndexJob = createAgentJobForStandalone(_group, HOSTNAME);
    createIndexJob.updateStatus(Status.FAILED, Optional.of("Mistake"));
    final ObjectId createIndexJobId = createIndexJob.getId();
    _jobsProcessorDao.putJob(createIndexJob);

    final BuildingIndex index =
        createIndex(_group.getId(), null, _standaloneHostId, createIndexJobId, NAMESPACE);
    _buildingIndexDao.insertMajority(index);
    _indexBuildStatusSvc.checkIndexingBuildProgress();
    final BuildingIndex failedIndex = _buildingIndexDao.findById(index.getId()).get();
    Assertions.assertEquals(BuildingIndex.Status.FAILED, failedIndex.getStatus());
    Assertions.assertEquals("UNKNOWN", failedIndex.getFailedReason());

    final List<Event> failedAudit =
        _auditSvc.findAll(
            10,
            Collections.singletonList(
                com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent.Type
                    .INDEX_FAILED_INDEX_BUILD
                    .toString()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));
    Assertions.assertEquals(1, failedAudit.size());
    final IndexBuildAudit audit = (IndexBuildAudit) failedAudit.get(0);
    Assertions.assertEquals(INDEX_NAME, audit.getIndexName());
    Assertions.assertEquals("intelMac:47017", audit.getHostname());
    Assertions.assertEquals(_group.getId(), audit.getGroupId());
    Assertions.assertNull(audit.getClusterName());
  }

  @Test
  void checkInProgressIndexesFailedNoMatchingItem() throws Exception {
    final BuildingIndex index = createAgentJobsAndIndexes(INDEX_NAME);
    List<BuildingIndex> inProgressBuildingIndexes =
        _buildingIndexDao.findInProgressBuildingIndexes();
    Assertions.assertEquals(1, inProgressBuildingIndexes.size());

    final AutomationJobMockResponseRunner indexStatsJobRunner =
        new AutomationJobMockResponseRunner(
            _dataExplorerResponseDao,
            _jobsProcessorDao,
            _host.getId(),
            Map.of(NAMESPACE, List.of(new BasicBSONObject("name", "OTHER_INDEX"))),
            Map.of(),
            Map.of(),
            List.of(NAMESPACE),
            false,
            null,
            Set.of());
    indexStatsJobRunner.start();
    Thread.sleep(2500);
    _indexBuildStatusSvc.checkIndexingBuildProgress();
    final BuildingIndex failedIndex = _buildingIndexDao.findById(index.getId()).get();
    indexStatsJobRunner.stopThread();

    Assertions.assertEquals(BuildingIndex.Status.FAILED, failedIndex.getStatus());
    Assertions.assertEquals(
        "Index does not exist in listIndexStats response", failedIndex.getFailedReason());

    final List<Event> failedAudit =
        _auditSvc.findAll(
            10,
            Collections.singletonList(
                com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent.Type
                    .INDEX_FAILED_INDEX_BUILD
                    .toString()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));
    Assertions.assertEquals(0, failedAudit.size());
  }

  @Test
  void checkInProgressIndexesSucceedingForServerless() throws Exception {
    final MultiTenantParams setup = setupForMultiTenantTests();
    final Group group = setup._group;
    final Group mtmGroup = setup._mtmGroup;
    final HostCluster mtmHostCluster = setup._mtmHostCluster;
    final ClusterDescription tenantClusterDescription = setup._tenantClusterDescription;
    final Host _host = _hostSvc.findHostsByIds(mtmHostCluster, false).get(0);

    _clusterDescriptionDao.save(tenantClusterDescription);
    _hostSvc.loadHostClusterHosts(mtmHostCluster);
    MmsFactory.createAutomationAgentAuditEntry(mtmGroup, "hostname", "5.8.2.5551", new Date());

    final AgentJob createIndexJob = createAgentJobForCreateIndex(group, _host, mtmHostCluster);
    final ObjectId createIndexJobId = createIndexJob.getId();
    _jobsProcessorDao.putJob(createIndexJob);

    final BuildingIndex index =
        createIndexForServerless(
            group.getId(), tenantClusterDescription.getUniqueId(), createIndexJobId, NAMESPACE);
    _buildingIndexDao.insertMajority(index);
    List<BuildingIndex> inProgressBuildingIndexes =
        _buildingIndexDao.findInProgressBuildingIndexes();
    Assertions.assertEquals(inProgressBuildingIndexes.size(), 1);

    final AutomationJobMockResponseRunner indexStatsJobRunner =
        new AutomationJobMockResponseRunner(
            _dataExplorerResponseDao,
            _jobsProcessorDao,
            _host.getId(),
            Map.of(NAMESPACE, List.of(new BasicBSONObject("name", INDEX_NAME))),
            Map.of(),
            Map.of(),
            List.of(NAMESPACE),
            false,
            null,
            Set.of());
    indexStatsJobRunner.start();
    Thread.sleep(2500);
    _indexBuildStatusSvc.checkIndexingBuildProgress();
    final BuildingIndex completedIndex = _buildingIndexDao.findById(index.getId()).get();
    indexStatsJobRunner.stopThread();

    Assertions.assertEquals(BuildingIndex.Status.COMPLETED, completedIndex.getStatus());
    Assertions.assertNull(completedIndex.getFailedReason());
  }

  private AgentJob createAgentJobForListIndexStats(
      final Group pGroup, final String pHostname, final HostCluster pHostCluster) {
    final AgentJob agentJob =
        new AgentJob(
            ObjectId.get(),
            pGroup.getId(),
            new ObjectId(),
            pHostname,
            Type.DataExplorerOp,
            AgentJobHandler.class,
            new BasicDBObject(),
            Date.from(Instant.now().plus(2, DAYS)));
    agentJob.addParameter(
        "op",
        new BasicDBObject("db", DB).append("collection", COLL).append("type", "listIndexStats"));

    if (pHostCluster != null) {
      agentJob.addParameter("clusterId", pHostCluster.getClusterId());
    }

    agentJob.addParameter("hostname", "localhost");
    agentJob.addParameter("port", 27017);
    return agentJob;
  }

  private AgentJob createAgentJobForCreateIndex(
      final Group pGroup, final Host pHost, final HostCluster pHostCluster) {
    final AgentJob agentJob =
        new AgentJob(
            ObjectId.get(),
            pGroup.getId(),
            new ObjectId(),
            pHost.getName(),
            Type.DataExplorerOp,
            AgentJobHandler.class,
            new BasicDBObject(),
            Date.from(Instant.now().plus(2, DAYS)));
    agentJob.addParameter(
        "op", new BasicDBObject("db", DB).append("collection", COLL).append("type", "createIndex"));
    agentJob.addParameter("clusterId", pHostCluster.getClusterId());
    agentJob.addParameter("hostname", "localhost");
    agentJob.addParameter("port", 27017);
    return agentJob;
  }

  private AgentJob createAgentJobForStandalone(final Group pGroup, final String pHostname) {
    final AgentJob agentJob =
        new AgentJob(
            ObjectId.get(),
            pGroup.getId(),
            new ObjectId(),
            pHostname,
            Type.DataExplorerOp,
            AgentJobHandler.class,
            new BasicDBObject(),
            Date.from(Instant.now().plus(2, DAYS)));
    agentJob.addParameter(
        "op", new BasicDBObject("db", DB).append("collection", COLL).append("type", "createIndex"));
    agentJob.addParameter("hostname", pHostname);
    agentJob.addParameter("port", 27017);
    return agentJob;
  }

  final BuildingIndex createIndex(
      final ObjectId pGroupId,
      final ObjectId pClusterId,
      final String pHostId,
      final ObjectId pAgentJobId,
      final String pNamespace) {
    return createIndex(pGroupId, pClusterId, pHostId, pAgentJobId, pNamespace, INDEX_NAME);
  }

  private BuildingIndex createIndex(
      final ObjectId pGroupId,
      final ObjectId pClusterId,
      final String pHostId,
      final ObjectId pAgentJobId,
      final String pNamespace,
      final String pIndexName) {
    return new BuildingIndex(
        pGroupId,
        pClusterId,
        pHostId,
        null,
        pAgentJobId,
        null,
        null,
        null,
        pNamespace,
        pIndexName,
        false,
        null,
        BuildingIndex.Status.IN_PROGRESS);
  }

  private BuildingIndex createIndexForServerless(
      final ObjectId pGroupId,
      final ObjectId pClusterId,
      final ObjectId pAgentJobId,
      final String pNamespace) {
    return new BuildingIndex(
        pGroupId,
        null,
        null,
        pClusterId,
        pAgentJobId,
        null,
        null,
        null,
        pNamespace,
        INDEX_NAME,
        false,
        null,
        BuildingIndex.Status.IN_PROGRESS);
  }

  private static class MultiTenantParams {

    private final Group _group;
    private final Group _mtmGroup;
    private final HostCluster _mtmHostCluster;
    private final ClusterDescription _tenantClusterDescription;

    private MultiTenantParams(
        final Group pGroup,
        final Group pMtmGroup,
        final HostCluster pMtmHostCluster,
        final ClusterDescription pTenantClusterDescription) {
      _group = pGroup;
      _mtmGroup = pMtmGroup;
      _mtmHostCluster = pMtmHostCluster;
      _tenantClusterDescription = pTenantClusterDescription;
    }
  }

  // Serverless Setup
  private MultiTenantParams setupForMultiTenantTests() throws Exception {
    final String tenantClusterName = "cluster";
    final String backingMtmClusterName = "mtmCluster";
    final ObjectId replSpecId = new ObjectId();
    final ObjectId backupReplSpecId = new ObjectId();

    final Group group = createGroupWithPremiumPlan();
    final Group mtmGroup = createGroupWithPremiumPlan();

    // there will always only be 2 hosts in the list getting sorted and only one will be primary, so
    // we just want the primary to come first in the list
    final Comparator<Host> hostComparator =
        (o1, o2) -> o1.getIsPrimary() ? -1 : o2.getIsPrimary() ? 1 : 0;

    final List<String> tenantHostnames;
    final ClusterDescription tenantClusterDescription;
    tenantClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getDefaultServerlessClusterDescription(
                group.getId(), tenantClusterName));
    _clusterDescriptionDao.save(tenantClusterDescription.toDBObject(), WriteConcern.ACKNOWLEDGED);
    final String[] uriHostnames = tenantClusterDescription.getMongoDBUriHosts();
    tenantHostnames =
        Arrays.asList(uriHostnames).stream()
            .map(HostUtils::extractHostname)
            .collect(Collectors.toUnmodifiableList());

    final BasicDBObject tenantReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(tenantClusterName, group.getId(), 0);
    _replicaSetHardwareDao.create(
        tenantReplicaSetHardwareId,
        ReplicaSetHardwareModelTestFactory.getRsIdForNonConfigShard(tenantClusterName, 0),
        true,
        false,
        replSpecId);

    final InstanceHostname.HostnameScheme hostnameSchemeForAgents =
        InstanceHostname.HostnameScheme.LEGACY;
    tenantHostnames.forEach(
        hn -> {
          final ObjectId instanceId =
              _replicaSetHardwareDao.addInstance(
                  tenantReplicaSetHardwareId, CloudProvider.SERVERLESS, false, 0);
          final Hostnames hostnames = new Hostnames(hn);
          _replicaSetHardwareDao.setInstanceField(
              tenantReplicaSetHardwareId,
              instanceId,
              false,
              InstanceHardware.FieldDefs.HOSTNAMES,
              hostnames.toDBList());
          _replicaSetHardwareDao.setInstanceField(
              tenantReplicaSetHardwareId,
              instanceId,
              false,
              InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
              hostnameSchemeForAgents.name());
        });

    final BasicDBObject mtmReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(backingMtmClusterName, mtmGroup.getId(), 0);
    _replicaSetHardwareDao.create(
        mtmReplicaSetHardwareId,
        ReplicaSetHardwareModelTestFactory.getRsIdForNonConfigShard(backingMtmClusterName, 0),
        true,
        false,
        backupReplSpecId);

    final HostCluster mtmHostCluster =
        createReplicaSet_V_2_4(mtmGroup, backingMtmClusterName + "-shard-0", false);

    final List<Host> backingHosts = _hostSvc.findHostsByIds(mtmHostCluster, false);

    backingHosts.sort(hostComparator);

    backingHosts.forEach(
        h -> {
          final ObjectId instanceId =
              _replicaSetHardwareDao.addInstance(
                  mtmReplicaSetHardwareId, CloudProvider.AWS, false, 0);
          final Hostnames hostnames = new Hostnames(h.getName());
          _replicaSetHardwareDao.setInstanceField(
              mtmReplicaSetHardwareId,
              instanceId,
              false,
              InstanceHardware.FieldDefs.HOSTNAMES,
              hostnames.toDBList());
          _replicaSetHardwareDao.setInstanceField(
              mtmReplicaSetHardwareId,
              instanceId,
              false,
              InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
              hostnameSchemeForAgents.name());
        });

    _clusterDescriptionDao.save(
        NDSModelTestFactory.getAWSClusterDescription(mtmGroup.getId(), backingMtmClusterName),
        WriteConcern.ACKNOWLEDGED);

    _ndsGroupSvc.create(group.getId(), new NDSManagedX509(), false);
    final BasicDBObject containerDBObject = NDSModelTestFactory.getServerlessContainer();
    containerDBObject.put("id", group.getId());
    containerDBObject.put("tenantClusterName", tenantClusterName);
    final BasicDBObject clusterIdDBObject = (BasicDBObject) containerDBObject.get("clusterId");
    clusterIdDBObject.put("groupId", mtmGroup.getId());
    clusterIdDBObject.put("clusterName", backingMtmClusterName);
    TenantCloudProviderContainer container =
        new ServerlessCloudProviderContainer(containerDBObject);
    _ndsGroupDao.addCloudContainer(group.getId(), container);
    return new MultiTenantParams(group, mtmGroup, mtmHostCluster, tenantClusterDescription);
  }
}
