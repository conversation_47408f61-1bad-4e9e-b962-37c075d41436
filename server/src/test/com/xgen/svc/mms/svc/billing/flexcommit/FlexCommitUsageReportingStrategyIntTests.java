package com.xgen.svc.mms.svc.billing.flexcommit;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billing._public.svc.marketing.exception.MarketingIntegrationException;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.partners.usage.report._public.dao.PartnerUsageReportDao;
import com.xgen.cloud.partners.usage.report._public.model.AWSUsageReport;
import com.xgen.cloud.partners.usage.report._public.model.AzureUsageReport;
import com.xgen.cloud.partners.usage.report._public.model.AzureUsageReport.Builder;
import com.xgen.cloud.partners.usage.report._public.model.GCPUsageReport;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport.PartnerUsageReportBuilder;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport.Status;
import com.xgen.cloud.partners.usage.strategy._public.svc.FlexCommitUsageReportingStrategy;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.svc.billing.CreditSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class FlexCommitUsageReportingStrategyIntTests extends FlexCommitBaseTest {
  @Inject PartnerUsageReportDao partnerUsageReportDao;
  @Inject CreditDao creditDao;
  @Inject FlexCommitUsageReportingStrategy flexCommitUsageReportingStrategy;
  @Inject CreditSvc creditSvc;
  static final String DIMENSION_NAME = "Overage";

  @Test
  public void testAWSUsageReportStrategyScenarios()
      throws SvcException, MarketingIntegrationException {
    CreditType flexType = CreditType.AWS_FLEX_COMMIT;
    CreditType rolloverType = CreditType.AWS_FLEX_COMMIT_ROLLOVER;
    String productCode = "CLD_ATL_PRO_ESLA60_FC_AWS_CU";

    testAllScenarios(flexType, rolloverType, productCode);
  }

  @Test
  public void testGCPUsageReportStrategyScenarios()
      throws SvcException, MarketingIntegrationException {
    CreditType flexType = CreditType.GCP_FLEX_COMMIT;
    CreditType rolloverType = CreditType.GCP_FLEX_COMMIT_ROLLOVER;
    String productCode = "CLD_ATL_PRO_GCP_FC_CU_AB";

    testAllScenarios(flexType, rolloverType, productCode);
  }

  @Test
  public void testAzureUsageReportStrategyScenarios()
      throws SvcException, MarketingIntegrationException {
    CreditType flexType = CreditType.AZURE_FLEX_COMMIT;
    CreditType rolloverType = CreditType.AZURE_FLEX_COMMIT_ROLLOVER;
    String productCode = "CLD_ATL_PRO_ESLA60_AZR_FC_CU";

    testAllScenarios(flexType, rolloverType, productCode);
  }

  private void testAllScenarios(CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    testSingleUsageReportStrategyTestGeneric(flexType, rolloverType, productCode);
    testFlexAndRolloverUsageReportStrategyTest(flexType, rolloverType, productCode);
    testSingleUsagePartRolloverReportStrategyTest(flexType, rolloverType, productCode);
    testAllCreditsUsageReportStrategyTest(flexType, rolloverType, productCode);
    testUsesLastFlexUsageReportStrategyTest(flexType, rolloverType, productCode);
    testShortfallFlexCreditUsageReportStrategyTest(flexType, rolloverType, productCode);
    testShortfallRolloverCreditUsageReportStrategyTest(flexType, rolloverType, productCode);
    shortfallPartRolloverCreditUsageReportStrategyTest(flexType, rolloverType, productCode);
    onlyElasticUsageReportElasticStrategyTest(flexType, rolloverType, productCode);
    withinCommitAndElasticUsageReportElasticStrategyTest(flexType, rolloverType, productCode);
    allWithinCommitAndElasticUsageReportElasticStrategyTest(flexType, rolloverType, productCode);
  }

  private void testSingleUsageReportStrategyTestGeneric(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {

    // GIVEN: we accrued 2000 cents of additional usage
    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, 1000000, 4000, flexType, rolloverType, true, productCode);
    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // Simulate that we drew down 2000 cents that day
    long additionalUsageCents = 2000;
    creditDao.updateCreditAmountRemainingCents(
        outdatedCredits.get(0).getId(),
        outdatedCredits.get(0).getAmountRemainingCents() - additionalUsageCents);

    List<Credit> updatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    Credit updatedFlexCredit = updatedCredits.get(0);
    assertEquals(updatedFlexCredit.getType(), flexType);
    assertEquals(
        updatedFlexCredit.getAmountRemainingCents(),
        outdatedCredits.get(0).getAmountRemainingCents() - 2000);

    // WHEN: reports are generated
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(updatedFlexCredit, NOW);
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(NOW, 1), -1);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);

    // THEN: report's billed and reported cents are equal to the 2000 cents
    assertEquals(report.getBilledAmountCents(), additionalUsageCents);
    assertEquals(report.getReportedAmountCents(), additionalUsageCents);
    genericUsageReportAssertions(report, NOW, usageReportEndDate, updatedFlexCredit.getId(), false);
    partnerSpecificAssertions(report, updatedFlexCredit);
  }

  private void testFlexAndRolloverUsageReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {

    // GIVEN
    long amountCentsFlex = 1000000;
    long amountCentsRollover = 4000000;
    long usedRolloverCents = 5000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());
    long supportChargesToDate = outdatedCredits.get(0).getTotalBilledCents();

    // WHEN: first flex credit is exhausted and its corresponding rollover credit is partially
    // exhausted
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(0).getId(), 0);
    creditDao.updateCreditAmountRemainingCents(
        outdatedCredits.get(1).getId(), amountCentsRollover - usedRolloverCents);

    List<Credit> updatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    Credit updatedFlexCredit = updatedCredits.get(0);
    assertEquals(updatedFlexCredit.getType(), flexType);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(updatedFlexCredit, NOW);

    // THEN: for the flex credit, all the remaining available cents were reported
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(NOW, 1), -1);
    PartnerUsageReport report = reports.get(0);

    assertEquals(reports.size(), 1);
    assertEquals(
        report.getBilledAmountCents(),
        updatedFlexCredit.getAmountUsedCents() - supportChargesToDate);
    assertEquals(
        report.getReportedAmountCents(),
        Math.round((float) (updatedFlexCredit.getAmountUsedCents() - supportChargesToDate) / 100)
            * 100);
    genericUsageReportAssertions(report, NOW, usageReportEndDate, updatedFlexCredit.getId(), false);
    partnerSpecificAssertions(report, updatedFlexCredit);

    // For the rollover credits
    Credit rolloverCredit = updatedCredits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    reports = createReportsFromCredit(rolloverCredit, NOW);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), usedRolloverCents);
    assertEquals(report.getReportedAmountCents(), usedRolloverCents);
    genericUsageReportAssertions(report, NOW, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);
  }

  private void testSingleUsagePartRolloverReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    // GIVEN
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(NOW, 1), -1);
    long amountCentsFlex = 1000000;
    long amountCentsRollover = 4000;
    long usedRolloverCents = 2000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // WHEN: first flex credit is fully exhausted with usage and rollover credit uses 2000 cents
    createUsageForCredit(outdatedCredits.get(0), amountCentsFlex);
    creditDao.updateCreditAmountRemainingCents(
        outdatedCredits.get(1).getId(),
        outdatedCredits.get(1).getAmountRemainingCents() - usedRolloverCents);

    List<Credit> updatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // THEN: there is no reported usage for flex credit
    Credit updatedFlexCredit = updatedCredits.get(0);
    assertEquals(updatedFlexCredit.getType(), flexType);
    assertEquals(updatedFlexCredit.getTotalBilledCents(), amountCentsFlex);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(updatedFlexCredit, NOW);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    assertEquals(reports.size(), 1);
    genericUsageReportAssertions(report, NOW, usageReportEndDate, updatedFlexCredit.getId(), false);
    partnerSpecificAssertions(report, updatedFlexCredit);

    // THEN: there is 2000 cents usage on the rollover credit
    Credit rolloverCredit = updatedCredits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    reports = createReportsFromCredit(rolloverCredit, NOW);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), usedRolloverCents);
    assertEquals(report.getReportedAmountCents(), usedRolloverCents);
    assertEquals(reports.size(), 1);
    genericUsageReportAssertions(report, NOW, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);
  }

  private void testAllCreditsUsageReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    // GIVEN
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(NOW, 1), -1);

    long amountCentsFlex = 300000;
    long amountCentsRollover = 4000;
    long secondFlexUsageCents = 5000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());
    long supportChargesToDate = outdatedCredits.get(0).getTotalBilledCents();

    // WHEN: first flex and rollover credits are exhausted and second flex credit has 5000 cents
    // used
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(0).getId(), 0);
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(1).getId(), 0);
    creditDao.updateCreditAmountRemainingCents(
        outdatedCredits.get(2).getId(),
        outdatedCredits.get(2).getAmountCents() - secondFlexUsageCents);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());

    // THEN
    // exhausted flex credit should have all of the usage except support charges reported
    Credit flexCredit = credits.get(0);
    assertEquals(flexCredit.getType(), flexType);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit, NOW);
    PartnerUsageReport exhaustedFlexCreditReport = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(
        exhaustedFlexCreditReport.getBilledAmountCents(),
        flexCredit.getAmountCents() - supportChargesToDate);
    assertEquals(
        exhaustedFlexCreditReport.getReportedAmountCents(),
        Math.round(
                (float) (flexCredit.getAmountUsedCents() - flexCredit.getTotalBilledCents()) / 100)
            * 100);
    genericUsageReportAssertions(
        exhaustedFlexCreditReport, NOW, usageReportEndDate, flexCredit.getId(), false);
    partnerSpecificAssertions(exhaustedFlexCreditReport, flexCredit);

    // Rollover credit is fully exhausted per report
    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    reports = createReportsFromCredit(rolloverCredit, NOW);
    PartnerUsageReport rolloverReport = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(rolloverReport.getBilledAmountCents(), amountCentsRollover);
    assertEquals(rolloverReport.getReportedAmountCents(), amountCentsRollover);
    genericUsageReportAssertions(
        rolloverReport, NOW, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(rolloverReport, rolloverCredit);

    Credit secondFlexCredit = credits.get(2);
    assertEquals(secondFlexCredit.getType(), flexType);
    reports = createReportsFromCredit(secondFlexCredit, NOW);
    PartnerUsageReport secondFlexUsageReport = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(secondFlexUsageReport.getBilledAmountCents(), secondFlexUsageCents);
    assertEquals(secondFlexUsageReport.getReportedAmountCents(), secondFlexUsageCents);
    genericUsageReportAssertions(
        secondFlexUsageReport, NOW, usageReportEndDate, secondFlexCredit.getId(), false);
    partnerSpecificAssertions(secondFlexUsageReport, secondFlexCredit);
  }

  private void testUsesLastFlexUsageReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    Date billDate = NOW;
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);
    long amountCentsFlex = 300000;
    long amountCentsRollover = 4000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // Previously exhausted credits and using part of last flex
    createUsageForCredit(outdatedCredits.get(0), amountCentsFlex);
    createUsageForCredit(outdatedCredits.get(1), amountCentsRollover);
    creditDao.updateCreditAmountRemainingCents(
        outdatedCredits.get(2).getId(), outdatedCredits.get(2).getAmountCents() - 5000);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());

    Credit flexCredit = credits.get(0);
    assertEquals(flexCredit.getType(), flexType);
    assertEquals(flexCredit.getTotalBilledCents(), amountCentsFlex);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit, billDate);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit.getId(), false);
    partnerSpecificAssertions(report, flexCredit);

    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    assertEquals(rolloverCredit.getTotalBilledCents(), amountCentsRollover);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    Credit secondFlexCredit = credits.get(2);
    assertEquals(secondFlexCredit.getType(), flexType);
    assertEquals(
        secondFlexCredit.getAmountRemainingCents(), outdatedCredits.get(2).getAmountCents() - 5000);
    reports = createReportsFromCredit(secondFlexCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), secondFlexCredit.getAmountUsedCents());
    assertEquals(report.getReportedAmountCents(), secondFlexCredit.getAmountUsedCents());
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, secondFlexCredit.getId(), false);
    partnerSpecificAssertions(report, secondFlexCredit);
  }

  private void testShortfallFlexCreditUsageReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {

    // GIVEN
    long amountCentsFlex = 300000;
    long amountCentsRollover = 4000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());
    long supportChargesToDate = credits.get(0).getTotalBilledCents();

    // WHEN: we reach the end of first flex credit
    Date billDate = DateTimeUtils.dateOf(credits.get(0).getBillByDate().minusDays(1));
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);

    Credit flexCredit = credits.get(0);
    assertEquals(flexCredit.getType(), flexType);

    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit, billDate);
    PartnerUsageReport report = reports.get(0);

    // THEN: we report all of the unused flex credit for the flex credit 1.
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), flexCredit.getAmountCents() - supportChargesToDate);

    // We also report the unused credit rounded to the nearest dollar
    assertEquals(
        report.getReportedAmountCents(),
        Math.round((float) (flexCredit.getAmountCents() - supportChargesToDate) / 100) * 100);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit.getId(), false);
    partnerSpecificAssertions(report, flexCredit);

    // There was no usage hence there was no rollover usage reported
    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), rolloverCredit.getAmountCents());
    assertEquals(report.getReportedAmountCents(), rolloverCredit.getAmountCents());
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    // There was no usage hence there was also no usage reported against flex credit 2
    Credit flexCredit2 = credits.get(2);
    assertEquals(flexCredit2.getType(), flexType);
    reports = createReportsFromCredit(flexCredit2, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), false);
    partnerSpecificAssertions(report, flexCredit2);
  }

  private void testShortfallRolloverCreditUsageReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    // GIVEN
    long amountCentsFlex = 300000;
    long amountCentsRollover = 4000;
    long previouslyReportedCents = 10000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());
    long supportChargesToDate = outdatedCredits.get(0).getTotalBilledCents();

    Date billDate = DateTimeUtils.dateOf(outdatedCredits.get(0).getBillByDate().minusDays(1));
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);

    // WHEN: 10000 cents of usage is applied against flex credit 1 after first flex credit's
    // billByDate
    createUsageForCredit(outdatedCredits.get(0), previouslyReportedCents);

    // THEN: shortfall payment is charged on both first flex credit and rollover
    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());
    Credit flexCredit = credits.get(0);
    assertEquals(flexCredit.getType(), flexType);
    assertEquals(flexCredit.getTotalBilledCents(), previouslyReportedCents);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit, billDate);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(
        report.getBilledAmountCents(), flexCredit.getAmountCents() - previouslyReportedCents);
    assertEquals(
        report.getReportedAmountCents(),
        Math.round(
                (float)
                        (flexCredit.getAmountCents()
                            - previouslyReportedCents
                            - supportChargesToDate)
                    / 100)
            * 100);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit.getId(), false);
    partnerSpecificAssertions(report, flexCredit);

    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), amountCentsRollover);
    assertEquals(report.getReportedAmountCents(), amountCentsRollover);
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    // no usage is reported against the second flex credit
    Credit flexCredit2 = credits.get(2);
    assertEquals(flexCredit2.getType(), flexType);
    reports = createReportsFromCredit(flexCredit2, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), false);
    partnerSpecificAssertions(report, flexCredit2);
  }

  private void shortfallPartRolloverCreditUsageReportStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    // GIVEN
    long amountCentsFlex = 300000;
    long amountCentsRollover = 40000;
    long usedCentsRollover = 1000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    // WHEN: flex credit 1 is exhausted and rollover credit is partially used after
    // billByDate has passed
    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());
    Date billDate = DateTimeUtils.dateOf(outdatedCredits.get(0).getBillByDate().minusDays(1));
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);

    createUsageForCredit(outdatedCredits.get(0), amountCentsFlex);
    createUsageForCredit(outdatedCredits.get(1), usedCentsRollover);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());

    // THEN: there is no shortfall charge for flex credit 1 (because it is exhausted)
    Credit flexCredit = credits.get(0);
    assertEquals(flexCredit.getType(), flexType);
    assertEquals(flexCredit.getTotalBilledCents(), amountCentsFlex);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit, billDate);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit.getId(), false);
    partnerSpecificAssertions(report, flexCredit);

    // All the unused rollover amount cents is charged as shortfall charge.
    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    assertEquals(rolloverCredit.getTotalBilledCents(), usedCentsRollover);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), amountCentsRollover - usedCentsRollover);
    assertEquals(report.getReportedAmountCents(), amountCentsRollover - usedCentsRollover);
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    // No usage is reported against flex credit 2
    Credit flexCredit2 = credits.get(2);
    assertEquals(flexCredit2.getType(), flexType);
    assertEquals(flexCredit2.getTotalBilledCents(), 0);
    reports = createReportsFromCredit(flexCredit2, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), false);
    partnerSpecificAssertions(report, flexCredit2);
  }

  private void onlyElasticUsageReportElasticStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    Date billDate = NOW;
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);
    long amountCentsFlex = 1000000;
    long amountCentsRollover = 4000;
    long usedElastic = 5000;
    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // GIVEN: flex credit 1, rollover credit, and flex credit 2 are exhausted AND flex credit 2 has
    // elastic usage
    createUsageForCredit(outdatedCredits.get(0), amountCentsFlex);
    createUsageForCredit(outdatedCredits.get(1), amountCentsRollover);
    createUsageForCredit(outdatedCredits.get(2), amountCentsFlex);
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(2).getId(), -usedElastic);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());

    Credit flexCredit1 = credits.get(0);
    assertEquals(flexCredit1.getType(), flexType);
    assertEquals(flexCredit1.getTotalBilledCents(), amountCentsFlex);

    // WHEN: usage reports are generated before credit 1's billByDate
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit1, billDate);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);

    // THEN: there is no additional report against credit 1
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit1.getId(), false);
    partnerSpecificAssertions(report, flexCredit1);

    // There is no additional report against rollover credit
    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    assertEquals(rolloverCredit.getTotalBilledCents(), amountCentsRollover);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    // There are 2 reports for flex credit 2, second report containing elastic usage
    Credit flexCredit2 = credits.get(2);
    assertEquals(flexCredit2.getType(), flexType);
    assertEquals(flexCredit2.getTotalBilledCents(), amountCentsFlex);
    reports = createReportsFromCredit(flexCredit2, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), usedElastic);
    assertEquals(report.getReportedAmountCents(), usedElastic);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), true);
    partnerSpecificAssertions(report, flexCredit2);
  }

  private void withinCommitAndElasticUsageReportElasticStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {
    Date billDate = NOW;
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);
    long amountCentsFlex = 1000000;
    long amountCentsRollover = 4000;
    long usedElastic = 5000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // GIVEN: flex 1 and rollover credits are exhausted, flex credit 2 has elastic usage
    createUsageForCredit(outdatedCredits.get(0), amountCentsFlex);
    createUsageForCredit(outdatedCredits.get(1), amountCentsRollover);
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(2).getId(), -usedElastic);

    List<Credit> credits = creditSvc.findCreditsByOrgId(org.getId());

    Credit flexCredit1 = credits.get(0);
    assertEquals(flexCredit1.getType(), flexType);
    assertEquals(flexCredit1.getTotalBilledCents(), amountCentsFlex);

    // WHEN: usage reports are generated
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit1, billDate);
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);

    // THEN: there is no additional report against credit 1
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit1.getId(), false);
    partnerSpecificAssertions(report, flexCredit1);

    // There is no additional report against rollover credit
    Credit rolloverCredit = credits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    assertEquals(rolloverCredit.getTotalBilledCents(), amountCentsRollover);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), 0);
    assertEquals(report.getReportedAmountCents(), 0);
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    // There are 2 reports for flex credit 2, first report reporting all flex usage
    Credit flexCredit2 = credits.get(2);
    assertEquals(flexCredit2.getType(), flexType);
    assertEquals(flexCredit2.getAmountRemainingCents(), -usedElastic);
    reports = createReportsFromCredit(flexCredit2, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 2);
    assertEquals(report.getBilledAmountCents(), amountCentsFlex);
    assertEquals(report.getReportedAmountCents(), amountCentsFlex);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), false);
    partnerSpecificAssertions(report, flexCredit2);

    // second report reporting all the elastic usage
    report = reports.get(1);
    assertEquals(report.getBilledAmountCents(), usedElastic);
    assertEquals(report.getReportedAmountCents(), usedElastic);
    assertTrue(report.getIsOverage());
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), true);
    partnerSpecificAssertions(report, flexCredit2);
  }

  private void allWithinCommitAndElasticUsageReportElasticStrategyTest(
      CreditType flexType, CreditType rolloverType, String productCode)
      throws SvcException, MarketingIntegrationException {

    Date billDate = NOW;
    Date usageReportEndDate = DateUtils.addSeconds(DateUtils.addDays(billDate, 1), -1);
    long amountCentsFlex = 1000000;
    long amountCentsRollover = 4000;
    long usedElastic = 5000;

    Organization org =
        activateFlexCommitDealAndAssertCredits(
            2, amountCentsFlex, amountCentsRollover, flexType, rolloverType, true, productCode);

    List<Credit> outdatedCredits = creditSvc.findCreditsByOrgId(org.getId());
    long supportChargesToDate = outdatedCredits.get(0).getTotalBilledCents();

    // GIVEN: flex credit 1, rollover credit, and flex credit 2 are exhausted (and there's elastic
    // usage on flex credit 2) AND unit price for credit 1 has changed
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(0).getId(), 0);
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(1).getId(), 0);
    creditDao.updateCreditAmountRemainingCents(outdatedCredits.get(2).getId(), -5000);
    creditDao.modifySalesforceOpportunityUnitPrice(outdatedCredits.get(0).getId(), 1.1);

    List<Credit> updatedCredits = creditSvc.findCreditsByOrgId(org.getId());

    // WHEN: usage reports are created before credits' billByDate
    Credit flexCredit = updatedCredits.get(0);
    assertEquals(flexCredit.getType(), flexType);
    assertEquals(flexCredit.getAmountRemainingCents(), 0);
    List<? extends PartnerUsageReport> reports = createReportsFromCredit(flexCredit, billDate);

    // THEN: credit 1 is fully exhausted
    PartnerUsageReport report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), amountCentsFlex - supportChargesToDate);
    assertEquals(
        report.getReportedAmountCents(),
        Math.round((amountCentsFlex * 1.1 - supportChargesToDate) / 100) * 100);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit.getId(), false);
    partnerSpecificAssertions(report, flexCredit);

    // Rollover credit is fully exhausted
    Credit rolloverCredit = updatedCredits.get(1);
    assertEquals(rolloverCredit.getType(), rolloverType);
    assertEquals(rolloverCredit.getAmountRemainingCents(), 0);
    reports = createReportsFromCredit(rolloverCredit, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 1);
    assertEquals(report.getBilledAmountCents(), amountCentsRollover);
    assertEquals(report.getReportedAmountCents(), amountCentsRollover);
    genericUsageReportAssertions(
        report, billDate, usageReportEndDate, rolloverCredit.getId(), false);
    partnerSpecificAssertions(report, rolloverCredit);

    // Flex credit 2 has within commitment and elastic reports
    Credit flexCredit2 = updatedCredits.get(2);
    assertEquals(flexCredit2.getType(), flexType);
    assertEquals(flexCredit2.getAmountRemainingCents(), -5000);
    reports = createReportsFromCredit(flexCredit2, billDate);
    report = reports.get(0);
    assertEquals(reports.size(), 2);
    assertEquals(report.getBilledAmountCents(), amountCentsFlex);
    assertEquals(report.getReportedAmountCents(), amountCentsFlex);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), false);
    partnerSpecificAssertions(report, flexCredit2);
    report = reports.get(1);
    assertEquals(report.getBilledAmountCents(), usedElastic);
    assertEquals(report.getReportedAmountCents(), usedElastic);
    genericUsageReportAssertions(report, billDate, usageReportEndDate, flexCredit2.getId(), true);
    partnerSpecificAssertions(report, flexCredit2);
  }

  private void genericUsageReportAssertions(
      PartnerUsageReport report,
      Date billDate,
      Date usageReportEndDate,
      ObjectId creditId,
      Boolean isOverage) {
    assertThat(report.getStartDate(), equalTo(billDate));
    assertThat(report.getEndDate(), equalTo(usageReportEndDate));
    assertThat(report.getReportedAtDate(), equalTo(null));
    assertThat(report.getCreditId(), equalTo(creditId));
    assertThat(report.getStatus(), equalTo(Status.NEW));
    assertEquals(report.getIsOverage(), isOverage);
  }

  private void partnerSpecificAssertions(PartnerUsageReport report, Credit credit) {
    if (credit.isAwsMarketplaceCredit()) {
      AWSUsageReport awsReport = (AWSUsageReport) report;
      assertEquals(awsReport.getAwsCustomerId(), credit.getAwsMarketplaceCustomerId());
      assertEquals(awsReport.getAwsDimension(), DIMENSION_NAME);
      assertEquals(awsReport.getAwsProductCode(), credit.getAwsMarketplaceProductCode());
    } else if (credit.isGcpMarketplaceCredit()) {
      GCPUsageReport gcpReport = (GCPUsageReport) report;
      assertEquals(gcpReport.getGcpEntitlementId(), credit.getGcpMarketplaceEntitlementId());
    } else if (credit.isAzureMarketplaceCredit()) {
      AzureUsageReport azureReport = (AzureUsageReport) report;
      assertEquals(
          azureReport.getAzureExternalSubscriptionId(), credit.getAzureExternalSubscriptionId());
      assertEquals(azureReport.getAzureDimension(), DIMENSION_NAME);
    }
  }

  private List<? extends PartnerUsageReport> createReportsFromCredit(Credit credit, Date billDate) {
    Invoice invoice = new Invoice.Builder().build();
    PartnerUsageReportBuilder<?> baseBuilder;

    CreditType type = credit.getType();
    if (type == CreditType.AWS_FLEX_COMMIT || type == CreditType.AWS_FLEX_COMMIT_ROLLOVER) {
      baseBuilder =
          new AWSUsageReport.Builder()
              .awsDimension(DIMENSION_NAME)
              .awsCustomerId(credit.getAwsMarketplaceCustomerId())
              .awsProductCode(credit.getAwsMarketplaceProductCode());
    } else if (type == CreditType.GCP_FLEX_COMMIT || type == CreditType.GCP_FLEX_COMMIT_ROLLOVER) {
      baseBuilder =
          new GCPUsageReport.Builder().gcpEntitlementId(credit.getGcpMarketplaceEntitlementId());
    } else if (type == CreditType.AZURE_FLEX_COMMIT
        || type == CreditType.AZURE_FLEX_COMMIT_ROLLOVER) {
      baseBuilder =
          new AzureUsageReport.Builder()
              .azureDimension(DIMENSION_NAME)
              .azureExternalSubscriptionId(credit.getAzureExternalSubscriptionId());
    } else {
      throw new IllegalStateException("Unexpected value: " + credit.getType());
    }

    return flexCommitUsageReportingStrategy.createUsageReports(
        baseBuilder, credit, invoice, credit.getOrgId(), billDate);
  }

  private void createUsageForCredit(Credit credit, long usedCents) {
    Builder builder =
        new Builder()
            .azureExternalSubscriptionId(credit.getAzureExternalSubscriptionId())
            .billedAmountCents(usedCents)
            .reportedAmountCents(usedCents)
            .startDate(credit.getStartDate())
            .creditId(credit.getId())
            .orgId(credit.getOrgId());

    AzureUsageReport flexCreditReport =
        builder
            .creditId(credit.getId())
            .startDate(credit.getStartDate())
            .billedAmountCents(usedCents)
            .reportedAmountCents(usedCents)
            .status(Status.SUCCESSFUL)
            .build();
    partnerUsageReportDao.save(flexCreditReport);
    creditDao.updateCreditTotalBilledCents(credit.getId(), usedCents);
    creditDao.updateCreditAmountRemainingCents(credit.getId(), credit.getAmountCents() - usedCents);
  }
}
