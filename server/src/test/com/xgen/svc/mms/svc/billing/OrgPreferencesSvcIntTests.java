package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.OrgPreferences;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class OrgPreferencesSvcIntTests extends JUnit5BaseSvcTest {
  @Inject private OrgPreferencesSvc orgPreferencesSvc;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPreferencesDao/orgPreferences.json.ftl",
        null,
        OrgPreferences.DB_NAME,
        OrgPreferences.COLLECTION_NAME);
  }

  @Test
  public void updateInvoiceEmailPreferences_success() {
    // OrgPreference exists, verify that it was updated with true preference
    ObjectId orgId = oid(101);
    OrgPreferences initialOrgPreferences = orgPreferencesSvc.getOrgPreferences(orgId);
    assertNotNull(initialOrgPreferences);
    assertFalse(initialOrgPreferences.getShouldSendInvoiceOnlyToBillingEmail());
    OrgPreferences orgPreferencesUpdated =
        orgPreferencesSvc.updateInvoiceEmailRecipients(orgId, true);
    assertEquals(orgPreferencesUpdated.getOrgId(), orgId);
    assertTrue(orgPreferencesUpdated.getShouldSendInvoiceOnlyToBillingEmail());

    // If OrgPreference non-existent, then will create one with default false preference
    ObjectId orgId2 = oid(201);
    assertNull(orgPreferencesSvc.getOrgPreferences(orgId2));
    OrgPreferences orgPreferencesUpdated2 =
        orgPreferencesSvc.updateInvoiceEmailRecipients(orgId2, false);
    assertNotNull(orgPreferencesUpdated2);
    assertEquals(orgPreferencesUpdated2.getOrgId(), orgId2);
    assertFalse(orgPreferencesUpdated2.getShouldSendInvoiceOnlyToBillingEmail());
  }
}
