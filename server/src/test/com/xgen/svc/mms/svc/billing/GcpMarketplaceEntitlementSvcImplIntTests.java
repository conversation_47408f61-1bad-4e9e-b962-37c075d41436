package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.cloudcommerceprocurement.v1.CloudCommercePartnerProcurementService;
import com.google.cloudcommerceprocurement.v1.model.Entitlement;
import com.google.servicecontrol.v1.ServiceControl;
import com.xgen.cloud.billing._public.svc.GcpMarketplaceEntitlementSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.GcpMarketplaceEntitlementDao;
import com.xgen.svc.mms.model.billing.GCPMarketplaceEntitlement;
import com.xgen.svc.mms.model.billing.GCPMarketplaceEntitlementState;
import com.xgen.svc.mms.util.billing.testFactories.GcpMarketplaceEntitlementFactory;
import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GcpMarketplaceEntitlementSvcImplIntTests extends JUnit5BaseSvcTest {

  private GcpMarketplaceEntitlementSvc gcpMarketplaceEntitlementSvc;

  @Mock private GcpMarketplaceClient gcpMarketplaceClient;

  @Inject private GcpMarketplaceEntitlementDao gcpMarketplaceEntitlementDao;
  @Inject private AppSettings appSettings;
  @Inject private GcpMarketplaceEntitlementFactory gcpMarketplaceEntitlementFactory;

  @BeforeEach
  public void setup() {
    gcpMarketplaceEntitlementSvc =
        new GcpMarketplaceEntitlementSvcImpl(
            gcpMarketplaceClient, gcpMarketplaceEntitlementDao, appSettings);
  }

  /**
   * We currently vendor our own versions of the cloud commerce procurement and the service control
   * library. This test attempts to instantiate clients for these packages to ensure there is no
   * runtime failure. Each package will ensure that a compatible version of google-api-client is
   * used.
   */
  @Test
  public void testPackageLoading() throws ClassNotFoundException {
    // Force the static blocks for these classes to run where the verification occurs.
    Class.forName(ServiceControl.class.getName());
    Class.forName(CloudCommercePartnerProcurementService.class.getName());
  }

  @Test
  public void testFindSelfServeEntitlementByAccountId() {
    String accountId = "gcpAccountttIdd";
    String entId = "entitelmentdId";
    GCPMarketplaceEntitlement gcpMarketplaceEntitlement =
        gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);
    assertNull(gcpMarketplaceEntitlement);

    gcpMarketplaceEntitlementFactory.createSelfServeEntitlement(
        accountId, entId, GCPMarketplaceEntitlementState.ENTITLEMENT_SUSPENDED);

    gcpMarketplaceEntitlement =
        gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);
    assertEquals(entId, gcpMarketplaceEntitlement.getEntitlementId());
    assertEquals(accountId, gcpMarketplaceEntitlement.getAccountId());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_SUSPENDED, gcpMarketplaceEntitlement.getState());
    assertNotNull(gcpMarketplaceEntitlement.getUsageReportingId());
    assertNotNull(gcpMarketplaceEntitlement.getEntitlementUpdatedTime());
    assertNotNull(gcpMarketplaceEntitlement.getProductExternalName());
    assertNotNull(gcpMarketplaceEntitlement.getId());
  }

  @Test
  public void testFindSelfServeEntitlementByAccountId_nullState() {
    String accountId = "gcpAccountttId";
    String entId = "entitelmentId";
    GCPMarketplaceEntitlement ent =
        gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);
    assertNull(ent);

    gcpMarketplaceEntitlementFactory.createSelfServeEntitlement(accountId, entId, null);

    ent = gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);

    assertEquals(entId, ent.getEntitlementId());
    assertEquals(accountId, ent.getAccountId());
    assertEquals(
        appSettings.getStrProp(GcpMarketplaceEntitlementSvcImpl.SELF_SERVE_PRODUCT_NAME_CONFIG),
        ent.getProductExternalName());
    assertNotNull(ent.getProductExternalName());
    assertNotNull(ent.getUsageReportingId());
    assertNotNull(ent.getEntitlementUpdatedTime());
    assertNotNull(ent.getId());
    assertNull(ent.getState());
  }

  @Test
  public void testFindOrCreateGcpSelfServeEntitlementByAccountId() {
    String accountId = "accountId";
    GCPMarketplaceEntitlement createdEnt =
        gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);

    assertNull(createdEnt);

    // verify the first call results in a new entitlement being created
    createdEnt =
        gcpMarketplaceEntitlementSvc.findOrCreateGcpSelfServeEntitlementByAccountId(accountId);
    assertNotNull(createdEnt.getId());
    assertEquals(accountId, createdEnt.getAccountId());
    assertEquals(
        appSettings.getStrProp(GcpMarketplaceEntitlementSvcImpl.SELF_SERVE_PRODUCT_NAME_CONFIG),
        createdEnt.getProductExternalName());
    assertNotNull(createdEnt.getProductExternalName());
    assertNull(createdEnt.getState());
    assertNull(createdEnt.getEntitlementUpdatedTime());
    assertNull(createdEnt.getUsageReportingId());
    assertNull(createdEnt.getEntitlementId());

    // verify a follow up call with the same accountId retrieves the existing entitlement, and
    // inserts nothing.
    GCPMarketplaceEntitlement foundEnt =
        gcpMarketplaceEntitlementSvc.findOrCreateGcpSelfServeEntitlementByAccountId(accountId);
    assertNotNull(foundEnt.getId());
    assertEquals(foundEnt.getId(), createdEnt.getId());
    assertEquals(foundEnt.getAccountId(), createdEnt.getAccountId());
    assertEquals(foundEnt.getProductExternalName(), createdEnt.getProductExternalName());
    assertNull(foundEnt.getState());
    assertNull(foundEnt.getEntitlementUpdatedTime());
    assertNull(foundEnt.getUsageReportingId());
    assertNull(foundEnt.getEntitlementId());

    // verify a call with a new account, while there are existing entitlements, does not modify any
    // existing entitlements, and creates a new entitlement for the new account
    String accountId2 = "accountId2";
    GCPMarketplaceEntitlement createdEnt2 =
        gcpMarketplaceEntitlementSvc.findOrCreateGcpSelfServeEntitlementByAccountId(accountId2);
    foundEnt =
        gcpMarketplaceEntitlementSvc.findOrCreateGcpSelfServeEntitlementByAccountId(accountId);
    assertNotNull(foundEnt.getId());
    assertEquals(foundEnt.getId(), createdEnt.getId());
    assertEquals(foundEnt.getAccountId(), createdEnt.getAccountId());
    assertEquals(foundEnt.getProductExternalName(), createdEnt.getProductExternalName());
    assertNull(foundEnt.getState());
    assertNull(foundEnt.getEntitlementUpdatedTime());
    assertNull(foundEnt.getUsageReportingId());
    assertNull(foundEnt.getEntitlementId());

    assertNotNull(createdEnt2.getId());
    assertEquals(accountId2, createdEnt2.getAccountId());
    assertEquals(
        appSettings.getStrProp(GcpMarketplaceEntitlementSvcImpl.SELF_SERVE_PRODUCT_NAME_CONFIG),
        createdEnt2.getProductExternalName());
    assertNotNull(createdEnt2.getProductExternalName());
    assertNull(createdEnt2.getState());
    assertNull(createdEnt2.getEntitlementUpdatedTime());
    assertNull(createdEnt2.getUsageReportingId());
    assertNull(createdEnt2.getEntitlementId());
    assertNotEquals(createdEnt2.getId(), foundEnt.getId());
  }

  @Test
  public void testFindOrCreateGcpSelfServeEntitlementByAccountId_inactiveSubscription() {
    String accountId = "accountId";
    GCPMarketplaceEntitlement ent1 =
        save(
            GCPMarketplaceEntitlement.builder()
                .accountId(accountId)
                .entitlementId("prevEntitlement")
                .state(GCPMarketplaceEntitlementState.ENTITLEMENT_CANCELLED)
                .build());

    // verify a new entitlement created
    GCPMarketplaceEntitlement ent2 =
        gcpMarketplaceEntitlementSvc.findOrCreateGcpSelfServeEntitlementByAccountId(accountId);
    assertNotEquals(ent1.getId(), ent2.getId());
    assertNotNull(ent2.getId());
    assertEquals(accountId, ent2.getAccountId());
    assertEquals(
        appSettings.getStrProp(GcpMarketplaceEntitlementSvcImpl.SELF_SERVE_PRODUCT_NAME_CONFIG),
        ent2.getProductExternalName());
    assertNotNull(ent2.getProductExternalName());
    assertNull(ent2.getState());
    assertNull(ent2.getEntitlementUpdatedTime());
    assertNull(ent2.getUsageReportingId());
    assertNull(ent2.getEntitlementId());
  }

  @Test
  public void testFindById() {
    Optional<GCPMarketplaceEntitlement> notFoundEnt =
        gcpMarketplaceEntitlementSvc.findById(new ObjectId());
    assertFalse(notFoundEnt.isPresent());

    String accountId = "gcpAccountttIdd";
    String entId = "entitelmentdId";
    GCPMarketplaceEntitlement gcpMarketplaceEntitlement =
        gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);
    assertNull(gcpMarketplaceEntitlement);

    gcpMarketplaceEntitlementFactory.createSelfServeEntitlement(
        accountId, entId, GCPMarketplaceEntitlementState.ENTITLEMENT_SUSPENDED);

    gcpMarketplaceEntitlement =
        gcpMarketplaceEntitlementSvc.findSelfServeEntitlementByAccountId(accountId);
    assertEquals(entId, gcpMarketplaceEntitlement.getEntitlementId());
    assertEquals(accountId, gcpMarketplaceEntitlement.getAccountId());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_SUSPENDED, gcpMarketplaceEntitlement.getState());
    assertNotNull(gcpMarketplaceEntitlement.getUsageReportingId());
    assertNotNull(gcpMarketplaceEntitlement.getEntitlementUpdatedTime());
    assertNotNull(gcpMarketplaceEntitlement.getProductExternalName());
    assertNotNull(gcpMarketplaceEntitlement.getId());

    GCPMarketplaceEntitlement foundEnt =
        gcpMarketplaceEntitlementSvc.findById(gcpMarketplaceEntitlement.getId()).get();
    assertEquals(foundEnt.getId(), gcpMarketplaceEntitlement.getId());
    assertEquals(foundEnt.getAccountId(), gcpMarketplaceEntitlement.getAccountId());
    assertEquals(foundEnt.getEntitlementId(), gcpMarketplaceEntitlement.getEntitlementId());
    assertEquals(foundEnt.getUsageReportingId(), gcpMarketplaceEntitlement.getUsageReportingId());
    assertEquals(foundEnt.getState(), gcpMarketplaceEntitlement.getState());
    assertEquals(
        foundEnt.getEntitlementUpdatedTime(),
        gcpMarketplaceEntitlement.getEntitlementUpdatedTime());
    assertEquals(
        foundEnt.getProductExternalName(), gcpMarketplaceEntitlement.getProductExternalName());
  }

  @Test
  public void testApproveEntitlement() {
    String entitlementId = "entttid";
    gcpMarketplaceEntitlementSvc.approveEntitlement(entitlementId);

    verify(gcpMarketplaceClient, times(1)).approveEntitlement(any(), eq(entitlementId));
  }

  @Test
  public void testSyncEntitlement() {
    Entitlement entitlement = mock(Entitlement.class);

    String usageReportingId = "usageReportingId";
    String usageReportingName = "usageReporting:" + usageReportingId;
    String accountId = "accountId3322";
    String accountName = "accounts/" + accountId;
    String plan = "plan";
    String quoteExternalName = "quoteExternalName";
    String productExternalName = "productExternalName3232";
    String offer = "offer";
    String newPendingOffer = "newPendingOffer";
    String newPendingPlan = "newPendingPlan";
    String state = GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_CANCELLATION.name();
    LocalDateTime subscriptionEndTime = LocalDateTime.now().minus(4, ChronoUnit.HOURS);
    LocalDateTime offerEndTime = LocalDateTime.now().minus(2, ChronoUnit.HOURS);
    LocalDateTime entitlementUpdateTime = LocalDateTime.now();
    String messageToUser = "messageToUser";
    String offerDuration = "durationOffer";
    String newPendingOfferDuration = "newPendingOfferDuration";

    ObjectId gcpPubSubMessageId = new ObjectId();
    String entitlementId = "ennnntttiiiitleentId";
    when(entitlement.getUsageReportingId()).thenReturn(usageReportingName);
    when(entitlement.getAccount()).thenReturn(accountName);
    when(entitlement.getPlan()).thenReturn(plan);
    when(entitlement.getQuoteExternalName()).thenReturn(quoteExternalName);
    when(entitlement.getProductExternalName()).thenReturn(productExternalName);
    when(entitlement.getOffer()).thenReturn(offer);
    when(entitlement.getNewPendingOffer()).thenReturn(newPendingOffer);
    when(entitlement.getNewPendingPlan()).thenReturn(newPendingPlan);
    when(entitlement.getState()).thenReturn(state);
    when(entitlement.getSubscriptionEndTime())
        .thenReturn(subscriptionEndTime.toInstant(ZoneOffset.UTC).toString());
    when(entitlement.getOfferEndTime())
        .thenReturn(offerEndTime.toInstant(ZoneOffset.UTC).toString());
    when(entitlement.getUpdateTime())
        .thenReturn(entitlementUpdateTime.toInstant(ZoneOffset.UTC).toString());
    when(entitlement.getMessageToUser()).thenReturn(messageToUser);
    when(entitlement.getOfferDuration()).thenReturn(offerDuration);
    when(entitlement.getNewPendingOfferDuration()).thenReturn(newPendingOfferDuration);

    GCPMarketplaceEntitlement gcpMarketplaceEntitlement =
        gcpMarketplaceEntitlementSvc.syncGcpMarketplaceEntitlement(
            entitlementId, entitlement, gcpPubSubMessageId);

    assertEquals(usageReportingId, gcpMarketplaceEntitlement.getUsageReportingId());
    assertEquals(accountId, gcpMarketplaceEntitlement.getAccountId());
    assertEquals(plan, gcpMarketplaceEntitlement.getPlanId());
    assertEquals(quoteExternalName, gcpMarketplaceEntitlement.getQuoteExternalName());
    assertEquals(productExternalName, gcpMarketplaceEntitlement.getProductExternalName());
    assertEquals(offer, gcpMarketplaceEntitlement.getOffer());
    assertEquals(newPendingOffer, gcpMarketplaceEntitlement.getNewPendingOffer());
    assertEquals(newPendingPlan, gcpMarketplaceEntitlement.getNewPendingPlan());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_CANCELLATION,
        gcpMarketplaceEntitlement.getState());
    assertEquals(
        subscriptionEndTime.getHour(),
        gcpMarketplaceEntitlement.getSubscriptionEndTime().getHour());
    assertEquals(
        entitlementUpdateTime.getHour(),
        gcpMarketplaceEntitlement.getEntitlementUpdatedTime().getHour());
    assertEquals(offerEndTime.getHour(), gcpMarketplaceEntitlement.getOfferEndTime().getHour());
    assertEquals(messageToUser, gcpMarketplaceEntitlement.getMessageToUser());
    assertEquals(offerDuration, gcpMarketplaceEntitlement.getOfferDuration());
    assertEquals(newPendingOfferDuration, gcpMarketplaceEntitlement.getNewPendingOfferDuration());
    assertEquals(gcpPubSubMessageId, gcpMarketplaceEntitlement.getGcpPubSubNotificationId());
    assertEquals(entitlementId, gcpMarketplaceEntitlement.getEntitlementId());

    when(entitlement.getState())
        .thenReturn(GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_PLAN_CHANGE_APPROVAL.name());
    GCPMarketplaceEntitlement gcpMarketplaceEntitlementUpdated =
        gcpMarketplaceEntitlementSvc.syncGcpMarketplaceEntitlement(
            entitlementId, entitlement, gcpPubSubMessageId);

    assertEquals(usageReportingId, gcpMarketplaceEntitlementUpdated.getUsageReportingId());
    assertEquals(accountId, gcpMarketplaceEntitlementUpdated.getAccountId());
    assertEquals(plan, gcpMarketplaceEntitlementUpdated.getPlanId());
    assertEquals(quoteExternalName, gcpMarketplaceEntitlementUpdated.getQuoteExternalName());
    assertEquals(productExternalName, gcpMarketplaceEntitlementUpdated.getProductExternalName());
    assertEquals(offer, gcpMarketplaceEntitlementUpdated.getOffer());
    assertEquals(newPendingOffer, gcpMarketplaceEntitlementUpdated.getNewPendingOffer());
    assertEquals(newPendingPlan, gcpMarketplaceEntitlementUpdated.getNewPendingPlan());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_PLAN_CHANGE_APPROVAL,
        gcpMarketplaceEntitlementUpdated.getState());
    assertEquals(
        subscriptionEndTime.getHour(),
        gcpMarketplaceEntitlementUpdated.getSubscriptionEndTime().getHour());
    assertEquals(
        entitlementUpdateTime.getHour(),
        gcpMarketplaceEntitlementUpdated.getEntitlementUpdatedTime().getHour());
    assertEquals(
        offerEndTime.getHour(), gcpMarketplaceEntitlementUpdated.getOfferEndTime().getHour());
    assertEquals(messageToUser, gcpMarketplaceEntitlementUpdated.getMessageToUser());
    assertEquals(offerDuration, gcpMarketplaceEntitlementUpdated.getOfferDuration());
    assertEquals(
        newPendingOfferDuration, gcpMarketplaceEntitlementUpdated.getNewPendingOfferDuration());
    assertEquals(gcpPubSubMessageId, gcpMarketplaceEntitlementUpdated.getGcpPubSubNotificationId());
    assertEquals(entitlementId, gcpMarketplaceEntitlementUpdated.getEntitlementId());

    List<GCPMarketplaceEntitlement> entitlementsByAccount = getEntitlementsByAccount(accountId);
    assertEquals(1, entitlementsByAccount.size());

    String newProductName = "newProductName";
    String newEntitlementId = "newEntitlementId";
    Entitlement newEntitlement = mock(Entitlement.class);
    when(newEntitlement.getUsageReportingId()).thenReturn(usageReportingName);
    when(newEntitlement.getAccount()).thenReturn(accountName);
    when(newEntitlement.getPlan()).thenReturn(plan);
    when(newEntitlement.getProductExternalName()).thenReturn(newProductName);
    when(newEntitlement.getState())
        .thenReturn(GCPMarketplaceEntitlementState.ENTITLEMENT_ACTIVATION_REQUESTED.name());
    when(newEntitlement.getUpdateTime())
        .thenReturn(entitlementUpdateTime.toInstant(ZoneOffset.UTC).toString());

    GCPMarketplaceEntitlement gcpMarketplaceNewEntitlementNewProduct =
        gcpMarketplaceEntitlementSvc.syncGcpMarketplaceEntitlement(
            newEntitlementId, newEntitlement, null);

    assertNull(gcpMarketplaceNewEntitlementNewProduct.getSubscriptionEndTime());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getQuoteExternalName());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getOfferEndTime());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getOffer());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getOfferDuration());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getNewPendingOffer());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getNewPendingOfferDuration());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getNewPendingPlan());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getMessageToUser());
    assertNull(gcpMarketplaceNewEntitlementNewProduct.getGcpPubSubNotificationId());
    assertEquals(accountId, gcpMarketplaceNewEntitlementNewProduct.getAccountId());
    assertEquals(newProductName, gcpMarketplaceNewEntitlementNewProduct.getProductExternalName());
    assertEquals(plan, gcpMarketplaceNewEntitlementNewProduct.getPlanId());
    assertEquals(newEntitlementId, gcpMarketplaceNewEntitlementNewProduct.getEntitlementId());
    assertEquals(usageReportingId, gcpMarketplaceNewEntitlementNewProduct.getUsageReportingId());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_ACTIVATION_REQUESTED,
        gcpMarketplaceNewEntitlementNewProduct.getState());

    String newAccountId2 = "newAccountId2";
    String newAccountName2 = "newAccountId/" + newAccountId2;
    String newUsageReportingId2 = "newUsageReportingId2";
    String newUsageReportingName2 = "newUsageReportingId:" + newUsageReportingId2;
    String newEntitlementId2 = "newEntitlementId2";
    Entitlement newEntitlement2 = mock(Entitlement.class);
    when(newEntitlement2.getUsageReportingId()).thenReturn(newUsageReportingName2);
    when(newEntitlement2.getAccount()).thenReturn(newAccountName2);
    when(newEntitlement2.getPlan()).thenReturn(plan);
    when(newEntitlement2.getProductExternalName()).thenReturn(newProductName);
    when(newEntitlement2.getState())
        .thenReturn(GCPMarketplaceEntitlementState.ENTITLEMENT_ACTIVATION_REQUESTED.name());
    when(newEntitlement2.getUpdateTime())
        .thenReturn(entitlementUpdateTime.toInstant(ZoneOffset.UTC).toString());

    GCPMarketplaceEntitlement gcpMarketplaceEntitlementNewAccount =
        gcpMarketplaceEntitlementSvc.syncGcpMarketplaceEntitlement(
            newEntitlementId2, newEntitlement2, null);

    assertNull(gcpMarketplaceEntitlementNewAccount.getSubscriptionEndTime());
    assertNull(gcpMarketplaceEntitlementNewAccount.getQuoteExternalName());
    assertNull(gcpMarketplaceEntitlementNewAccount.getOfferEndTime());
    assertNull(gcpMarketplaceEntitlementNewAccount.getOffer());
    assertNull(gcpMarketplaceEntitlementNewAccount.getOfferDuration());
    assertNull(gcpMarketplaceEntitlementNewAccount.getNewPendingOffer());
    assertNull(gcpMarketplaceEntitlementNewAccount.getNewPendingOfferDuration());
    assertNull(gcpMarketplaceEntitlementNewAccount.getNewPendingPlan());
    assertNull(gcpMarketplaceEntitlementNewAccount.getMessageToUser());
    assertEquals(newAccountId2, gcpMarketplaceEntitlementNewAccount.getAccountId());
    assertEquals(newProductName, gcpMarketplaceEntitlementNewAccount.getProductExternalName());
    assertEquals(plan, gcpMarketplaceEntitlementNewAccount.getPlanId());
    assertEquals(newEntitlementId2, gcpMarketplaceEntitlementNewAccount.getEntitlementId());
    assertEquals(newEntitlementId2, gcpMarketplaceEntitlementNewAccount.getEntitlementId());
    assertEquals(
        entitlementUpdateTime.getHour(),
        gcpMarketplaceEntitlementNewAccount.getEntitlementUpdatedTime().getHour());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_ACTIVATION_REQUESTED,
        gcpMarketplaceEntitlementNewAccount.getState());

    List<GCPMarketplaceEntitlement> entitlementsByAccountAfterNewProduct =
        getEntitlementsByAccount(accountId);
    assertEquals(2, entitlementsByAccountAfterNewProduct.size());

    List<GCPMarketplaceEntitlement> entitlementsByAccountAfterNewAccount =
        getEntitlementsByAccount(newAccountId2);
    assertEquals(1, entitlementsByAccountAfterNewAccount.size());

    List<GCPMarketplaceEntitlement> entitlementsByAccountAndProduct1 =
        getEntitlementsByAccountAndProductExternalName(accountId, productExternalName);
    assertEquals(1, entitlementsByAccountAndProduct1.size());

    List<GCPMarketplaceEntitlement> entitlementsByAccountAndProduct2 =
        getEntitlementsByAccountAndProductExternalName(accountId, newProductName);
    assertEquals(1, entitlementsByAccountAndProduct2.size());

    List<GCPMarketplaceEntitlement> entitlementsByAccountAndProduct3 =
        getEntitlementsByAccountAndProductExternalName(newAccountId2, newProductName);
    assertEquals(1, entitlementsByAccountAndProduct3.size());
  }

  @Test
  public void testSyncEntitlement_selfServe() {
    Entitlement entitlement = mock(Entitlement.class);

    String usageReportingId = "usageReportingId";
    String usageReportingName = "usageReporting:" + usageReportingId;
    String accountId = "accountId3322";
    String accountName = "accounts/" + accountId;
    String plan = "plan";
    String quoteExternalName = "quoteExternalName";
    String productExternalName =
        appSettings.getStrProp(GcpMarketplaceEntitlementSvcImpl.SELF_SERVE_PRODUCT_NAME_CONFIG);
    String offer = "offer";
    String newPendingOffer = "newPendingOffer";
    String newPendingPlan = "newPendingPlan";
    String state = GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_CANCELLATION.name();
    LocalDateTime entitlementUpdateTime = LocalDateTime.now();
    LocalDateTime subscriptionEndTime = entitlementUpdateTime.minusHours(4);
    LocalDateTime offerEndTime = entitlementUpdateTime.minusHours(2);
    String messageToUser = "messageToUser";
    String offerDuration = "durationOffer";
    String newPendingOfferDuration = "newPendingOfferDuration";

    // A historical entitlement that should be updated
    GCPMarketplaceEntitlement prevCancelledEnt =
        save(
            GCPMarketplaceEntitlement.builder()
                .accountId(accountId)
                .state(GCPMarketplaceEntitlementState.ENTITLEMENT_CANCELLED)
                .entitlementId("prevEntitlementId")
                .productExternalName(productExternalName)
                .build());

    ObjectId gcpPubSubMessageId = new ObjectId();
    String entitlementId = "newEntitlementId";
    when(entitlement.getUsageReportingId()).thenReturn(usageReportingName);
    when(entitlement.getAccount()).thenReturn(accountName);
    when(entitlement.getPlan()).thenReturn(plan);
    when(entitlement.getQuoteExternalName()).thenReturn(quoteExternalName);
    when(entitlement.getProductExternalName()).thenReturn(productExternalName);
    when(entitlement.getOffer()).thenReturn(offer);
    when(entitlement.getNewPendingOffer()).thenReturn(newPendingOffer);
    when(entitlement.getNewPendingPlan()).thenReturn(newPendingPlan);
    when(entitlement.getState()).thenReturn(state);
    when(entitlement.getSubscriptionEndTime())
        .thenReturn(subscriptionEndTime.toInstant(ZoneOffset.UTC).toString());
    when(entitlement.getOfferEndTime())
        .thenReturn(offerEndTime.toInstant(ZoneOffset.UTC).toString());
    when(entitlement.getUpdateTime())
        .thenReturn(entitlementUpdateTime.toInstant(ZoneOffset.UTC).toString());
    when(entitlement.getMessageToUser()).thenReturn(messageToUser);
    when(entitlement.getOfferDuration()).thenReturn(offerDuration);
    when(entitlement.getNewPendingOfferDuration()).thenReturn(newPendingOfferDuration);

    // A new entitlement should be created
    GCPMarketplaceEntitlement createdEnt =
        gcpMarketplaceEntitlementSvc.syncGcpMarketplaceEntitlement(
            entitlementId, entitlement, gcpPubSubMessageId);

    assertNotEquals(prevCancelledEnt.getId(), createdEnt.getId());
    assertEquals(usageReportingId, createdEnt.getUsageReportingId());
    assertEquals(accountId, createdEnt.getAccountId());
    assertEquals(plan, createdEnt.getPlanId());
    assertEquals(quoteExternalName, createdEnt.getQuoteExternalName());
    assertEquals(productExternalName, createdEnt.getProductExternalName());
    assertEquals(offer, createdEnt.getOffer());
    assertEquals(newPendingOffer, createdEnt.getNewPendingOffer());
    assertEquals(newPendingPlan, createdEnt.getNewPendingPlan());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_CANCELLATION, createdEnt.getState());
    assertEquals(subscriptionEndTime.getHour(), createdEnt.getSubscriptionEndTime().getHour());
    assertEquals(entitlementUpdateTime.getHour(), createdEnt.getEntitlementUpdatedTime().getHour());
    assertEquals(offerEndTime.getHour(), createdEnt.getOfferEndTime().getHour());
    assertEquals(messageToUser, createdEnt.getMessageToUser());
    assertEquals(offerDuration, createdEnt.getOfferDuration());
    assertEquals(newPendingOfferDuration, createdEnt.getNewPendingOfferDuration());
    assertEquals(gcpPubSubMessageId, createdEnt.getGcpPubSubNotificationId());
    assertEquals(entitlementId, createdEnt.getEntitlementId());

    when(entitlement.getState())
        .thenReturn(GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_PLAN_CHANGE_APPROVAL.name());
    // An existing entitlement should be updated
    GCPMarketplaceEntitlement updatedEnt =
        gcpMarketplaceEntitlementSvc.syncGcpMarketplaceEntitlement(
            entitlementId, entitlement, gcpPubSubMessageId);

    assertEquals(createdEnt.getId(), updatedEnt.getId());
    assertEquals(usageReportingId, updatedEnt.getUsageReportingId());
    assertEquals(accountId, updatedEnt.getAccountId());
    assertEquals(plan, updatedEnt.getPlanId());
    assertEquals(quoteExternalName, updatedEnt.getQuoteExternalName());
    assertEquals(productExternalName, updatedEnt.getProductExternalName());
    assertEquals(offer, updatedEnt.getOffer());
    assertEquals(newPendingOffer, updatedEnt.getNewPendingOffer());
    assertEquals(newPendingPlan, updatedEnt.getNewPendingPlan());
    assertEquals(
        GCPMarketplaceEntitlementState.ENTITLEMENT_PENDING_PLAN_CHANGE_APPROVAL,
        updatedEnt.getState());
    assertEquals(subscriptionEndTime.getHour(), updatedEnt.getSubscriptionEndTime().getHour());
    assertEquals(entitlementUpdateTime.getHour(), updatedEnt.getEntitlementUpdatedTime().getHour());
    assertEquals(offerEndTime.getHour(), updatedEnt.getOfferEndTime().getHour());
    assertEquals(messageToUser, updatedEnt.getMessageToUser());
    assertEquals(offerDuration, updatedEnt.getOfferDuration());
    assertEquals(newPendingOfferDuration, updatedEnt.getNewPendingOfferDuration());
    assertEquals(gcpPubSubMessageId, updatedEnt.getGcpPubSubNotificationId());
    assertEquals(entitlementId, updatedEnt.getEntitlementId());

    List<GCPMarketplaceEntitlement> entitlementsByAccount = getEntitlementsByAccount(accountId);
    assertEquals(2, entitlementsByAccount.size());

    List<GCPMarketplaceEntitlement> entitlementsByAccountAndProduct =
        getEntitlementsByAccountAndProductExternalName(accountId, productExternalName);
    assertEquals(2, entitlementsByAccountAndProduct.size());
  }

  private List<GCPMarketplaceEntitlement> getEntitlementsByAccount(String gcpAccountId) {
    return gcpMarketplaceEntitlementDao.findAll().stream()
        .filter((e) -> e.getAccountId().equals(gcpAccountId))
        .collect(Collectors.toList());
  }

  private List<GCPMarketplaceEntitlement> getEntitlementsByAccountAndProductExternalName(
      String gcpAccountId, String productExternalName) {
    return gcpMarketplaceEntitlementDao.findAll().stream()
        .filter(
            (e) ->
                e.getAccountId().equals(gcpAccountId)
                    && e.getProductExternalName().equals(productExternalName))
        .collect(Collectors.toList());
  }
}
