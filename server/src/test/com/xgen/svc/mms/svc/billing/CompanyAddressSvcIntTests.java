package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.activity._public.svc.event.EventSvc;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.CompanyAddress;
import com.xgen.cloud.group._public.model.CompanyAddress.Status;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.payments.salestax.validation._public.svc.AddressValidationSvc;
import com.xgen.cloud.payments.salestax.validation._public.svc.TaxIdValidationSvc;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CompanyAddressDao;
import com.xgen.svc.mms.dao.billing.PayingOrgDao;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.util.billing.testFactories.CompanyAddressFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CompanyAddressSvcIntTests extends JUnit5BaseSvcTest {

  private CompanyAddressSvc _companyAddressSvc;
  private OFACCheckSvc _mockOFACCheckSvc;
  @Inject private OrganizationDao _organizationDao;
  @Inject private EventSvc eventSvc;
  @Inject private CompanyAddressDao _companyAddressDao;
  @Inject private PayingOrgSvc _payingOrgSvc;
  @Inject private AuditSvc _auditSvc;
  @Inject private AuthzSvc _authzSvc;
  @Inject private TaxIdValidationSvc taxIdValidationSvc;

  @Inject private PayingOrgDao payingOrgDao;

  @Inject private OrganizationFactory organizationFactory;

  @Inject private CompanyAddressFactory companyAddressFactory;
  @Inject private AddressValidationSvc addressValidationSvc;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/CompanyAddressDao/companyAddresses.json.ftl",
        null,
        CompanyAddress.DB_NAME,
        CompanyAddress.COLLECTION_NAME);

    // Mock out call to third party OFACCheck service. Found to be flaky 3/2022.
    _mockOFACCheckSvc = mock(OFACCheckSvc.class);
    doNothing().when(_mockOFACCheckSvc).checkCompanyAddress(any(), any());
    _companyAddressSvc =
        new CompanyAddressSvc(
            _organizationDao,
            _companyAddressDao,
            _payingOrgSvc,
            _auditSvc,
            _mockOFACCheckSvc,
            _authzSvc,
            taxIdValidationSvc,
            addressValidationSvc);
  }

  @Test
  public void testSetCompanyAddress() throws SvcException {
    String newCompanyName = "The fun company :)";
    ObjectId orgId = oid(201);
    Organization org = _organizationDao.findById(orgId);
    AuditInfo newAuditInfo = AuditInfoHelpers.fromInternal();
    CompanyAddress initialCompanyAddress = _companyAddressSvc.findActiveByOrgId(orgId, true);
    Date currentDate = new Date();
    // Null out the actual id and change company name to create a new address
    CompanyAddress.Builder newCompanyAddressBuilder =
        new CompanyAddress.Builder(initialCompanyAddress);
    newCompanyAddressBuilder.id(null);
    newCompanyAddressBuilder.companyName(newCompanyName);
    newCompanyAddressBuilder.build();
    CompanyAddress newCompanyAddress = newCompanyAddressBuilder.build();
    _companyAddressSvc.setCompanyAddress(org, newCompanyAddress, currentDate, newAuditInfo, null);
    // Make sure that the active company address is the address that was just saved
    CompanyAddress newActiveCompanyAddress = _companyAddressSvc.findActiveByOrgId(orgId, true);

    verify(_mockOFACCheckSvc, times(1)).checkCompanyAddress(org, newCompanyAddress);
    assertEquals(newCompanyName, newActiveCompanyAddress.getCompanyName());
    assertEquals(orgId, newActiveCompanyAddress.getOrgId());
    assertEquals(CompanyAddress.Status.ACTIVE, newActiveCompanyAddress.getStatus());
    assertEquals(currentDate, newActiveCompanyAddress.getCreated());
    assertNull(newActiveCompanyAddress.getDeactivated());

    // Make sure that audit event was saved
    List<Event> events = eventSvc.findAll(5);
    assertEquals(1, events.size());

    // Make sure that the old address object is now inactive
    CompanyAddress deactivatedAddress = _companyAddressSvc.findById(initialCompanyAddress.getId());
    assertEquals(CompanyAddress.Status.INACTIVE, deactivatedAddress.getStatus());
    assertEquals(currentDate, deactivatedAddress.getDeactivated());
  }

  @Test
  public void testSetCompanyAddress_onLinkedOrg() throws SvcException {

    // GIVEN
    Date now = new Date();

    Organization payingOrg = organizationFactory.createAtlasOrganization();
    Organization linkedOrg = organizationFactory.createAtlasOrganization();

    payingOrgDao.upsertPayingOrg(payingOrg.getId(), List.of(linkedOrg.getId()), now.toInstant());

    AuditInfo newAuditInfo = AuditInfoHelpers.fromInternal();

    CompanyAddress payingOrgCompanyAddress =
        companyAddressFactory.createUSAddress(payingOrg.getId());

    CompanyAddress linkedOrgCompanyAddress =
        companyAddressFactory.createEUAddress(linkedOrg.getId(), false);

    CompanyAddress newCompanyAddress =
        new CompanyAddress.Builder(linkedOrgCompanyAddress)
            .id(new ObjectId())
            .companyName("new company name")
            .build();

    // WHEN

    _companyAddressSvc.setCompanyAddress(linkedOrg, newCompanyAddress, now, newAuditInfo, null);

    // THEN

    // Make sure that the active company address is the address that was just saved
    CompanyAddress newActiveCompanyAddress =
        _companyAddressDao.findActiveByOrgId(linkedOrg.getId());

    verify(_mockOFACCheckSvc, times(1)).checkCompanyAddress(linkedOrg, newCompanyAddress);
    assertEquals(newCompanyAddress.getCompanyName(), newActiveCompanyAddress.getCompanyName());
    assertEquals(linkedOrg.getId(), newActiveCompanyAddress.getOrgId());
    assertEquals(CompanyAddress.Status.ACTIVE, newActiveCompanyAddress.getStatus());
    assertEquals(now, newActiveCompanyAddress.getCreated());
    assertNull(newActiveCompanyAddress.getDeactivated());

    // Make sure that audit event was saved
    List<Event> events = eventSvc.findAll(5);
    assertEquals(1, events.size());

    // Make sure that the old address object is now inactive
    CompanyAddress deactivatedAddress =
        _companyAddressSvc.findById(linkedOrgCompanyAddress.getId());
    assertEquals(CompanyAddress.Status.INACTIVE, deactivatedAddress.getStatus());
    assertEquals(now, deactivatedAddress.getDeactivated());

    // Make sure that the paying org's address is unaffected
    CompanyAddress payingOrgCompanyAddress_updated =
        _companyAddressDao.findById(payingOrgCompanyAddress.getId());
    assertEquals(Status.ACTIVE, payingOrgCompanyAddress_updated.getStatus());
  }

  @Test
  public void testSetCompanyAddressFirstTime() throws SvcException {
    String newCompanyName = "The fun company :)";
    ObjectId orgId = oid(201);
    ObjectId anotherOrgId = oid(203);
    Organization anotherOrg = _organizationDao.findById(anotherOrgId);
    AuditInfo newAuditInfo = AuditInfoHelpers.fromInternal();
    CompanyAddress templateCompanyAddress = _companyAddressSvc.findActiveByOrgId(orgId, true);
    // Null out the actual id and change company name to create a new address
    CompanyAddress.Builder newCompanyAddressBuilder =
        new CompanyAddress.Builder(templateCompanyAddress);
    newCompanyAddressBuilder.id(null);
    newCompanyAddressBuilder.orgId(oid(203));
    newCompanyAddressBuilder.companyName(newCompanyName);
    CompanyAddress newCompanyAddress = newCompanyAddressBuilder.build();
    _companyAddressSvc.setCompanyAddress(
        anotherOrg, newCompanyAddress, new Date(), newAuditInfo, null);
    // Make sure that the active company address is the address that was just saved
    CompanyAddress newActiveCompanyAddress =
        _companyAddressSvc.findActiveByOrgId(anotherOrgId, true);

    verify(_mockOFACCheckSvc, times(1)).checkCompanyAddress(anotherOrg, newCompanyAddress);
    assertEquals(newCompanyName, newActiveCompanyAddress.getCompanyName());
    assertEquals(anotherOrgId, newActiveCompanyAddress.getOrgId());
    assertEquals(CompanyAddress.Status.ACTIVE, newActiveCompanyAddress.getStatus());

    // Make sure audit event was saved
    List<Event> events = eventSvc.findAll(5);
    assertEquals(1, events.size());
  }

  @Test
  public void test_findActiveByOrgId() {
    Organization payingOrg = organizationFactory.createAtlasOrganization();
    CompanyAddress payingOrgCompanyAddress =
        companyAddressFactory.createUSAddress(payingOrg.getId());

    Organization linkedOrg = organizationFactory.createAtlasOrganization();
    CompanyAddress linkedOrgCompanyAddress =
        companyAddressFactory.createUSAddress(linkedOrg.getId());

    payingOrgDao.upsertPayingOrg(
        payingOrg.getId(), List.of(linkedOrg.getId()), new Date().toInstant());

    CompanyAddress companyAddress = _companyAddressSvc.findActiveByOrgId(linkedOrg.getId(), true);
    assertEquals(companyAddress.getId(), payingOrgCompanyAddress.getId());

    CompanyAddress companyAddress2 = _companyAddressSvc.findActiveByOrgId(linkedOrg.getId(), false);
    assertEquals(companyAddress2.getId(), linkedOrgCompanyAddress.getId());
  }

  @Test
  public void testFindActiveByOrgIds() {
    ObjectId orgId1 = ObjectId.get(); // paying org 1 with address
    ObjectId orgId2 = ObjectId.get(); // paying org 2 without address
    ObjectId orgId3 = ObjectId.get(); // linked org to orgId1 without address
    ObjectId orgId4 = ObjectId.get(); // linked org to orgId2 with address
    ObjectId orgId5 = ObjectId.get(); // solo org

    payingOrgDao.upsertPayingOrg(orgId1, List.of(orgId3), Instant.now());
    payingOrgDao.upsertPayingOrg(orgId2, List.of(orgId4), Instant.now());

    CompanyAddress org1Address =
        save(new CompanyAddress.Builder().orgId(orgId1).status(Status.ACTIVE).build());
    CompanyAddress org4Address =
        save(new CompanyAddress.Builder().orgId(orgId4).status(Status.ACTIVE).build());
    CompanyAddress org5Address =
        save(new CompanyAddress.Builder().orgId(orgId5).status(Status.ACTIVE).build());

    Assertions.assertThat(_companyAddressSvc.findActiveByOrgIds(List.of(orgId5), true))
        .hasSize(1)
        .containsEntry(orgId5, org5Address);

    Assertions.assertThat(
            _companyAddressSvc.findActiveByOrgIds(List.of(orgId1, orgId2, orgId3, orgId4), true))
        .hasSize(4)
        .containsEntry(orgId1, org1Address)
        .containsEntry(orgId2, null)
        .containsEntry(orgId3, org1Address)
        .containsEntry(orgId4, null);

    Assertions.assertThat(
            _companyAddressSvc.findActiveByOrgIds(
                List.of(orgId1, orgId2, orgId3, orgId4, orgId5), false))
        .hasSize(5)
        .containsEntry(orgId1, org1Address)
        .containsEntry(orgId2, null)
        .containsEntry(orgId3, null)
        .containsEntry(orgId4, org4Address)
        .containsEntry(orgId5, org5Address);
  }
}
