load("//server/src/test:rules.bzl", "library_package", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/atm/agentjobs",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/common/appsettings/_public/svc",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/explorer",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_private/dao",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_public/model",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/cron/_private/dao",
        "//server/src/main/com/xgen/cloud/deployment/_public/model",
        "//server/src/main/com/xgen/cloud/explorer/activity",
        "//server/src/main/com/xgen/cloud/externalanalytics/_public/svc",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/common/_public/model",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle/_public/svc",
        "//server/src/main/com/xgen/cloud/monitoring/logs/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/metrics/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/performanceadvisor",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/main/com/xgen/svc/mms/dao/explorer",
        "//server/src/main/com/xgen/svc/mms/dao/performanceadvisor",
        "//server/src/main/com/xgen/svc/mms/dao/performanceadvisor/autoindexing",
        "//server/src/main/com/xgen/svc/mms/model/explorer",
        "//server/src/main/com/xgen/svc/mms/model/performanceadvisor",
        "//server/src/test/com/xgen/cloud/performanceadvisor/util",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/mms/svc/explorer",
        "//server/src/test/com/xgen/svc/mms/svc/performanceadvisor:utils",
        "//server/src/test/com/xgen/svc/mms/util",
        "//server/src/test/com/xgen/svc/nds/serverless/svc:NDSServerlessTestUtils",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)

library_package(
    name = "utils",
    visibility = [
        "//server/src/test:__subpackages__",
    ],
    deps = [
        "//server/src/unit/com/xgen/svc/mms/util",
    ],
)
