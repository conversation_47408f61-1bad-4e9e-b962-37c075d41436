package com.xgen.svc.mms.svc.billing.audit.auditors;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billing._public.svc.IAccountantSvc;
import com.xgen.cloud.billing._public.svc.marketing.exception.MarketingIntegrationException;
import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.constants._public.model.partners.PartnerType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.ApplyActivationCodeRequest;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.SalesforceOpportunity;
import com.xgen.svc.mms.svc.billing.LineItemSvc;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ElasticCreditUsageAuditorIntTests extends JUnit5BaseSvcTest {
  @Inject private CreditFactory creditFactory;

  @Inject private OrganizationFactory organizationFactory;

  @Inject private CreditDao creditDao;

  @Inject private LineItemSvc lineItemSvc;

  @Inject private IAccountantSvc accountantSvc;

  @Inject private SalesSoldDealActivationSvc salesSoldDealActivationSvc;

  @Inject private ElasticCreditUsageAuditor elasticCreditUsageAuditor;

  @Inject private SalesforceOpportunityFactory salesforceOpportunityFactory;

  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;

  @BeforeEach
  public void setup() {
    salesforceProductCodeDao.syncDocuments();
  }

  @Test
  public void testAuditSuccess() throws SvcException, MarketingIntegrationException {
    LocalDate now = LocalDate.now().withDayOfMonth(1).minusMonths(1);
    LocalDate activationDate = now.withDayOfMonth(25);
    LocalDate startDateCredit = now.minusMonths(2);
    LocalDate endDateCredit = now.plusMonths(10);
    long creditAmountCents = 10000;

    // WHEN a credit is activated on an org and drawn down into the negative
    Organization org = organizationFactory.createAtlasOrganization(dateOf(now.plusDays(2)), true);
    SalesforceOpportunity opp =
        salesforceOpportunityFactory.insertPrepaidOpportunity(
            PartnerType.GCP,
            dateOf(startDateCredit),
            dateOf(endDateCredit),
            true,
            creditAmountCents,
            true);
    salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org,
            opp.getLicenseKey(),
            dateOf(activationDate),
            null,
            AuditInfoHelpers.fromSystem(),
            true));
    accountantSvc.processOrganizations(List.of(org.getId()), dateOf(activationDate));

    // ASSERT the elastic credit usage matches the credit's elastic line item's total
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    long elasticCreditUsage = credit.getTotalElasticUsageCents();
    long elasticLineItemTotal = lineItemSvc.getElasticCreditUsageByCreditId(credit.getId());
    assertTrue(elasticCreditUsage > 0);
    assertEquals(elasticCreditUsage, -elasticLineItemTotal);

    // ASSERT audit passes
    Optional<AuditFailureDetail> auditResult = elasticCreditUsageAuditor.audit(credit);
    assertEquals(Optional.empty(), auditResult);

    // ASSERT that this credit would be included in the list of credits to audit.
    assertTrue(elasticCreditUsageAuditor.getIdsToAudit().contains(credit.getId()));
  }

  @Test
  public void testAuditFail() throws SvcException, MarketingIntegrationException {
    LocalDate now = LocalDate.now().withDayOfMonth(1).minusMonths(1);
    LocalDate activationDate = now.withDayOfMonth(25);
    LocalDate startDateCredit = now.minusMonths(2);
    LocalDate endDateCredit = now.plusMonths(10);
    long creditAmountCents = 10000;

    // WHEN a credit is activated on an org and drawn down into the negative
    Organization org = organizationFactory.createAtlasOrganization(dateOf(now.plusDays(2)), true);
    SalesforceOpportunity opp =
        salesforceOpportunityFactory.insertPrepaidOpportunity(
            PartnerType.GCP,
            dateOf(startDateCredit),
            dateOf(endDateCredit),
            true,
            creditAmountCents,
            true);
    salesSoldDealActivationSvc.applyActivationCodeSyncAndRebillSync(
        new ApplyActivationCodeRequest(
            org,
            opp.getLicenseKey(),
            dateOf(activationDate),
            null,
            AuditInfoHelpers.fromSystem(),
            true));
    accountantSvc.processOrganizations(List.of(org.getId()), dateOf(activationDate));

    // ASSERT the elastic credit usage matches the credit's elastic line item's total
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    long elasticCreditUsage = credit.getTotalElasticUsageCents();
    long elasticLineItemTotal = lineItemSvc.getElasticCreditUsageByCreditId(credit.getId());
    assertTrue(elasticCreditUsage > 0);
    assertEquals(elasticCreditUsage, -elasticLineItemTotal);

    // ASSERT that this credit would be included in the list of credits to audit.
    Optional<AuditFailureDetail> auditResult = elasticCreditUsageAuditor.audit(credit);
    assertEquals(Optional.empty(), auditResult);

    // THEN update credit amount remaining to force audit to fail.
    creditDao.updateCreditAmountRemainingCents(credit.getId(), elasticLineItemTotal + 12);

    Optional<AuditFailureDetail> auditResultFail =
        elasticCreditUsageAuditor.audit(creditDao.findById(credit.getId()));

    // ASSERT audit fails
    assertTrue(auditResultFail.isPresent());

    assertEquals(
        BillingAuditErrorCode.TOTAL_ELASTIC_LINE_ITEMS_NOT_EQUAL_CREDIT_ELASTIC_USAGE,
        auditResultFail.get().getBillingAuditErrors().get(0).getErrorCode());

    // ASSERT that this credit would be included in the list of credits to audit.
    assertTrue(elasticCreditUsageAuditor.getIdsToAudit().contains(credit.getId()));
  }

  @Test
  public void testGetIdsToAudit() throws SvcException, MarketingIntegrationException {
    LocalDate now = LocalDate.now().withDayOfMonth(1).minusMonths(1);
    LocalDate startDateCredit = now.minusMonths(2);
    LocalDate endDateCredit = now.plusMonths(10);
    long creditAmountCents = 10000;

    // WHEN a credit is activated on an org and drawn down into the negative
    Organization org = organizationFactory.createAtlasOrganization(dateOf(now.plusDays(2)), true);
    Credit validCredit =
        creditFactory.createGcpPrepaidCredit(
            org.getId(), dateOf(startDateCredit), dateOf(endDateCredit), creditAmountCents, true);

    Credit validPartnerSalesSoldCredit =
        creditFactory.createGcpPrepaidCredit(
            org.getId(), dateOf(startDateCredit), dateOf(endDateCredit), creditAmountCents, true);

    Credit validPartnerSelfServeCredit =
        creditFactory.createGcpSelfServeCredit(
            org.getId(), dateOf(startDateCredit), dateOf(endDateCredit));

    Credit recentlyExpiredCredit =
        creditFactory.createGcpPrepaidCredit(
            org.getId(),
            dateOf(now.minusMonths(13)),
            dateOf(now.minusMonths(1)),
            creditAmountCents,
            true);

    Credit veryOldExpiredCredit =
        creditFactory.createGcpPrepaidCredit(
            org.getId(),
            dateOf(now.minusMonths(20)),
            dateOf(now.minusMonths(8)),
            creditAmountCents,
            true);

    Set<ObjectId> idsToAudit = new HashSet<>(elasticCreditUsageAuditor.getIdsToAudit());

    assertTrue(idsToAudit.contains(validCredit.getId()));
    assertTrue(idsToAudit.contains(recentlyExpiredCredit.getId()));
    assertTrue(idsToAudit.contains(validPartnerSalesSoldCredit.getId()));
    assertTrue(idsToAudit.contains(validPartnerSelfServeCredit.getId()));
    assertFalse(idsToAudit.contains(veryOldExpiredCredit.getId()));
  }
}
