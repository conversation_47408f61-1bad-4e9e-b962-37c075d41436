package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Status;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.units._public.model.SKUUnits;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeState;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.pricing._public.svc.SkuPricingSvc;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.getter.AWSRegionNameGetter;
import com.xgen.module.metering.common.model.getter.AzureRegionNameGetter;
import com.xgen.module.metering.common.model.getter.GcpRegionNameGetter;
import com.xgen.module.metering.common.model.usagedimensions.AtlasPremiumSkusUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.AwsSearchInstanceUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.AwsUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.AzureSearchInstanceUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.AzureUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.GcpBaseUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.GcpDataTransferUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.GcpSearchInstanceUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.GcpSnapshotUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.GcpUsageDimensions;
import com.xgen.module.metering.common.view.MeterUsageAggregateView;
import com.xgen.module.metering.server.utils.testFactories.MeterUsageTestFactory;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.LineItem.Builder;
import com.xgen.svc.mms.model.billing.MeterIdActivationRules;
import com.xgen.svc.mms.model.billing.OrgBillContext;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.svc.billing.pricing.AWSRegionPricing;
import com.xgen.svc.mms.svc.billing.pricing.AdditionalPricingInfo;
import com.xgen.svc.mms.svc.billing.pricing.AdvancedSecurityPricingStrategy;
import com.xgen.svc.mms.svc.billing.pricing.AzureRegionPricing;
import com.xgen.svc.mms.svc.billing.pricing.BiConnectorPricingStrategy;
import com.xgen.svc.mms.svc.billing.pricing.EnterpriseAuditingPricingStrategy;
import com.xgen.svc.mms.svc.billing.pricing.GCPBasePricing;
import com.xgen.svc.mms.svc.billing.pricing.GCPDataTransferPricing;
import com.xgen.svc.mms.svc.billing.pricing.GCPRegionPricing;
import com.xgen.svc.mms.svc.billing.pricing.PricingStrategy;
import com.xgen.svc.mms.svc.billing.pricing.UnitPricing;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class MeterBillerIntTests extends JUnit5BaseSvcTest {
  private static final Date JAN1 = DateTimeUtils.dateOf(LocalDate.parse("2022-01-01"));
  private static final Date JAN2 = DateTimeUtils.dateOf(LocalDate.parse("2022-01-02"));
  private static final Date JAN3 = DateTimeUtils.dateOf(LocalDate.parse("2022-01-03"));
  private static final ObjectId TENANT_ID = ObjectId.get();
  private static final String TENANT_NAME = ObjectId.get().toString();

  private static final ObjectId CLUSTER_UNIQUE_ID = ObjectId.get();

  private static final String CLUSTER_NAME = ObjectId.get().toString();

  private Organization org;
  private Group group;
  private Invoice invoice;

  @Inject private MeterBiller meterBiller;
  @Inject private SkuPricingSvc skuPricingSvc;
  @Inject private NDSDataLakeTenantDao tenantDao;
  @Inject private ClusterDescriptionDao clusterDescriptionDao;
  @Inject private EnterpriseAuditingPricingStrategy enterpriseAuditingPricingStrategy;
  @Inject private AdvancedSecurityPricingStrategy advancedSecurityPricingStrategy;
  @Inject private BiConnectorPricingStrategy biConnectorPricingStrategy;
  @Inject private AppSettings appSettings;

  @BeforeEach
  public void setup() {
    org = MmsFactory.createOrganizationWithNDSPlan();
    group = MmsFactory.createGroup(org);
    invoice =
        new Invoice.Builder().orgId(org.getId()).startDate(JAN1).status(Status.PENDING).build();
    meterBiller = spy(meterBiller);
    when(meterBiller.getNow()).thenReturn(JAN3);
  }

  @Test
  public void billDaily_gcpServerlessRegional() {
    Stream.of(
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_WPU,
                MeterId.NDS_GCP_SERVERLESS_WPU,
                GCPRegionName.EASTERN_US),
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM,
                MeterId.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM,
                GCPRegionName.EASTERN_US),
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
                MeterId.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
                GCPRegionName.EASTERN_US),
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET,
                MeterId.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET,
                GCPRegionName.EASTERN_US),
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL,
                MeterId.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL,
                GCPRegionName.EASTERN_US),
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER,
                MeterId.NDS_GCP_SERVERLESS_DATA_TRANSFER,
                GCPRegionName.EASTERN_US),
            gcpRegionalMeterUsageParams(
                SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW,
                MeterId.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW,
                GCPRegionName.EASTERN_US))
        .forEach(this::doTestBillDaily);
  }

  @Test
  public void billDaily_awsServerlessRegional() {
    Stream.of(
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_WPU,
                MeterId.NDS_AWS_SERVERLESS_WPU,
                AWSRegionName.US_EAST_1),
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM,
                MeterId.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM,
                AWSRegionName.US_EAST_1),
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
                MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
                AWSRegionName.US_EAST_1),
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET,
                MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET,
                AWSRegionName.US_EAST_1),
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL,
                MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL,
                AWSRegionName.US_EAST_1),
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER,
                MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
                AWSRegionName.US_EAST_1),
            awsRegionalMeterUsageParams(
                SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW,
                MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW,
                AWSRegionName.US_EAST_1))
        .forEach(this::doTestBillDaily);
  }

  @Test
  public void billDaily_azureServerlessRegional() {
    Stream.of(
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_WPU,
                MeterId.NDS_AZURE_SERVERLESS_WPU,
                AzureRegionName.US_EAST),
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM,
                MeterId.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM,
                AzureRegionName.US_EAST),
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
                MeterId.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
                AzureRegionName.US_EAST),
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET,
                MeterId.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET,
                AzureRegionName.US_EAST),
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL,
                MeterId.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL,
                AzureRegionName.US_EAST),
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER,
                MeterId.NDS_AZURE_SERVERLESS_DATA_TRANSFER,
                AzureRegionName.US_EAST),
            azureRegionalMeterUsageParams(
                SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW,
                MeterId.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW,
                AzureRegionName.US_EAST))
        .forEach(this::doTestBillDaily);
  }

  @Test
  public void billDaily_gcpDataTransfer() {
    Stream.of(
            gcpDataTransferParams(
                SKU.NDS_GCP_DATA_TRANSFER_INTERNET, MeterId.NDS_GCP_DATA_TRANSFER_INTERNET),
            gcpDataTransferParams(
                SKU.NDS_GCP_DATA_TRANSFER_INTER_REGION, MeterId.NDS_GCP_DATA_TRANSFER_INTER_REGION),
            gcpDataTransferParams(
                SKU.NDS_GCP_DATA_TRANSFER_GOOGLE, MeterId.NDS_GCP_DATA_TRANSFER_GOOGLE),
            gcpDataTransferParams(
                SKU.DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION,
                MeterId.DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION),
            baseDimensionsParams(
                SKU.NDS_GCP_DATA_TRANSFER_INTER_CONNECT,
                MeterId.NDS_GCP_DATA_TRANSFER_INTER_CONNECT),
            clusterOnlyDimensionsParams(
                SKU.NDS_GCP_DATA_TRANSFER_INTER_ZONE, MeterId.NDS_GCP_DATA_TRANSFER_INTER_ZONE))
        .forEach(this::doTestBillDaily);
  }

  @Test
  public void billDaily_dataLake_aws() {
    NDSDataLakeTenant tenant =
        new NDSDataLakeTenant.NDSDataLakeTenantBuilder()
            .tenantId(TENANT_ID)
            .id(new NDSDataLakeTenantId(group.getId(), TENANT_NAME))
            .state(NDSDataLakeState.ACTIVE)
            .build();

    tenantDao.saveTenant(tenant);

    doTestBillDaily(dataLakeAwsTenantIdParams(SKU.DATA_LAKE_AWS_DATA_SCANNED));

    List<SKU> hostRegionUsageSkus =
        List.of(
            SKU.DATA_LAKE_AWS_DATA_RETURNED_SAME_REGION,
            SKU.DATA_LAKE_AWS_DATA_RETURNED_DIFFERENT_REGION,
            SKU.DATA_LAKE_AWS_DATA_RETURNED_INTERNET);

    hostRegionUsageSkus.forEach(sku -> doTestBillDaily(dataLakeAwsHostRegionParams(sku)));

    List<SKU> awsDataRegionUsageSkus =
        List.of(
            SKU.DATA_LAKE_AWS_DATA_TRANSFERRED_FROM_DIFFERENT_REGION,
            SKU.NDS_AWS_DATA_LAKE_STORAGE_ACCESS,
            SKU.NDS_AWS_OBJECT_STORAGE_ACCESS,
            SKU.NDS_AWS_OBJECT_STORAGE_SEEK);

    awsDataRegionUsageSkus.forEach(sku -> doTestBillDaily(dataLakeAwsDataRegionParams(sku)));
  }

  @Test
  public void billDaily_dataLake_azure() {
    NDSDataLakeTenant tenant =
        new NDSDataLakeTenant.NDSDataLakeTenantBuilder()
            .tenantId(TENANT_ID)
            .id(new NDSDataLakeTenantId(group.getId(), TENANT_NAME))
            .state(NDSDataLakeState.ACTIVE)
            .build();

    tenantDao.saveTenant(tenant);

    doTestBillDaily(dataLakeAzureTenantIdParams(SKU.DATA_FEDERATION_AZURE_DATA_SCANNED));

    List<SKU> hostRegionUsageSkus =
        List.of(
            SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_REGION,
            SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_CONTINENT,
            SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_DIFFERENT_CONTINENT,
            SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_INTERNET);

    hostRegionUsageSkus.forEach(sku -> doTestBillDaily(dataLakeAzureHostRegionParams(sku)));

    List<SKU> azureDataRegionUsageSkus =
        List.of(SKU.NDS_AZURE_DATA_LAKE_STORAGE_ACCESS, SKU.NDS_AZURE_OBJECT_STORAGE_ACCESS);

    azureDataRegionUsageSkus.forEach(sku -> doTestBillDaily(dataLakeAzureDataRegionParams(sku)));
  }

  @Test
  public void billDaily_dataLake_gcp() {
    NDSDataLakeTenant tenant =
        new NDSDataLakeTenant.NDSDataLakeTenantBuilder()
            .tenantId(TENANT_ID)
            .id(new NDSDataLakeTenantId(group.getId(), TENANT_NAME))
            .state(NDSDataLakeState.ACTIVE)
            .build();

    tenantDao.saveTenant(tenant);

    doTestBillDaily(dataLakeGcpTenantIdParams(SKU.DATA_FEDERATION_GCP_DATA_SCANNED));

    List<SKU> hostRegionUsageSkus = List.of(SKU.DATA_FEDERATION_GCP_DATA_RETURNED_INTERNET);

    hostRegionUsageSkus.forEach(sku -> doTestBillDaily(dataLakeGcpHostRegionParams(sku)));

    List<SKU> gcpDataRegionUsageSkus =
        List.of(SKU.NDS_GCP_DATA_LAKE_STORAGE_ACCESS, SKU.NDS_GCP_OBJECT_STORAGE_ACCESS);

    gcpDataRegionUsageSkus.forEach(sku -> doTestBillDaily(dataLakeGcpDataRegionParams(sku)));
  }

  @Test
  public void billDaily_gcpSnapshotCopyDisk() {
    doTestBillDaily(gcpSnapshotParams(SKU.GCP_SNAPSHOT_COPY_DISK));
  }

  @Test
  public void billDaily_awsSearchInstanceSKUs() {
    List<SKU> searchInstanceSKUs =
        List.of(
            SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME,
            SKU.NDS_AWS_SEARCH_INSTANCE_S30_COMPUTE_NVME,
            SKU.NDS_AWS_SEARCH_INSTANCE_S40_COMPUTE_NVME,
            SKU.NDS_AWS_SEARCH_INSTANCE_S50_COMPUTE_NVME,
            SKU.NDS_AWS_SEARCH_INSTANCE_S60_COMPUTE_NVME);

    for (SKU sku : searchInstanceSKUs) {
      doTestBillDaily(awsSearchInstanceParams(sku));
    }
  }

  @Test
  public void billDaily_gcpSearchInstanceSKUs() {
    List<SKU> searchInstanceSKUs =
        List.of(
            SKU.NDS_GCP_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD,
            SKU.NDS_GCP_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD,
            SKU.NDS_GCP_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD,
            SKU.NDS_GCP_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD,
            SKU.NDS_GCP_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD,
            SKU.NDS_GCP_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD,
            SKU.NDS_GCP_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD);

    for (SKU sku : searchInstanceSKUs) {
      doTestBillDaily(gcpSearchInstanceParams(sku));
    }
  }

  @Test
  public void billDaily_azureSearchInstanceSKUs() {
    List<SKU> searchInstanceSKUs =
        List.of(
            SKU.NDS_AZURE_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD,
            SKU.NDS_AZURE_SEARCH_INSTANCE_S135_MEMORY_LOCALSSD);

    for (SKU sku : searchInstanceSKUs) {
      doTestBillDaily(azureSearchInstanceParams(sku));
    }
  }

  @Test
  public void billDaily_advancedSecurity() {
    SKU sku = SKU.NDS_ADVANCED_SECURITY;

    // TODO: remove when meter id is activated
    save(
        MeterIdActivationRules.withDefaultRule(
            Set.of(SKU.NDS_ADVANCED_SECURITY), LocalDate.parse("2023-08-01")));

    BillDailyParams params =
        premiumUpchargeDimensionsParams(
            sku, MeterId.NDS_ADVANCED_SECURITY, advancedSecurityPricingStrategy);
    List<LineItem> generatedLineItems = doTestBillDaily(params);
    assertEquals(8, generatedLineItems.get(0).getTotalPriceCents());
    assertEquals(0.0837472602739726, generatedLineItems.get(0).getUnitPriceDollars(), 0.0);
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2099-01-01")));
  }

  @Test
  public void billDaily_biConnector() {
    SKU sku = SKU.NDS_BI_CONNECTOR;
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2023-08-01")));

    BillDailyParams params =
        premiumUpchargeDimensionsParams(sku, MeterId.NDS_BI_CONNECTOR, biConnectorPricingStrategy);
    List<LineItem> generatedLineItems = doTestBillDaily(params);
    assertEquals(1138L, generatedLineItems.get(0).getTotalPriceCents());
    assertEquals(11.38, generatedLineItems.get(0).getUnitPriceDollars(), 0.0);
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2099-01-01")));
  }

  @Test
  public void billDaily_biConnector_noClusterDescription() {
    SKU sku = SKU.NDS_BI_CONNECTOR;
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2023-08-01")));

    MeterUsage meterUsage =
        new MeterUsage.Builder(AppEnv.TEST)
            .id(ObjectId.get())
            .startTime(JAN1)
            .endTime(JAN2)
            .reportedTime(JAN2)
            .meterId(MeterId.NDS_BI_CONNECTOR)
            .unit(SKUUnits.HOURS)
            .quantity(1)
            .groupId(group.getId())
            .usageDimensions(
                AtlasPremiumSkusUsageDimensions.premiumAws(
                    ObjectId.get(), "test-cluster", AWSRegionName.EU_CENTRAL_1))
            .build();

    BillDailyParams params = new BillDailyParams(sku, List.of(meterUsage), List.of());
    List<LineItem> generatedLineItems = doTestBillDaily(params);
    assertEquals(0, generatedLineItems.size());
  }

  @Test
  public void billDaily_enterpriseAuditing() {
    SKU sku = SKU.NDS_ENTERPRISE_AUDITING;
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2023-08-01")));

    BillDailyParams params =
        premiumUpchargeDimensionsParams(
            sku, MeterId.NDS_ENTERPRISE_AUDITING, enterpriseAuditingPricingStrategy);
    List<LineItem> generatedLineItems = doTestBillDaily(params);
    assertEquals(6, generatedLineItems.get(0).getTotalPriceCents());
    assertEquals(0.05583150684931507, generatedLineItems.get(0).getUnitPriceDollars(), 0.0);
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2099-01-01")));
  }

  @Test
  public void billDaily_enterpriseAuditing_noClusterDescription() {
    SKU sku = SKU.NDS_ENTERPRISE_AUDITING;
    // TODO: remove when meter id is activated
    save(MeterIdActivationRules.withDefaultRule(Set.of(sku), LocalDate.parse("2023-08-01")));

    BillDailyParams params =
        premiumUpchargeDimensionsParams(
            sku, MeterId.NDS_ENTERPRISE_AUDITING, enterpriseAuditingPricingStrategy);

    MeterUsage meterUsageWithoutClusterDescription =
        new MeterUsage.Builder(AppEnv.TEST)
            .id(ObjectId.get())
            .startTime(JAN1)
            .endTime(JAN2)
            .reportedTime(JAN2)
            .meterId(MeterId.NDS_BI_CONNECTOR)
            .unit(SKUUnits.HOURS)
            .quantity(1)
            .groupId(group.getId())
            .usageDimensions(
                AtlasPremiumSkusUsageDimensions.premiumAws(
                    ObjectId.get(), "test-cluster", AWSRegionName.EU_CENTRAL_1))
            .build();

    List<MeterUsage> meterUsages = new ArrayList<>(params.meterUsages);
    meterUsages.add(meterUsageWithoutClusterDescription);
    params = new BillDailyParams(sku, meterUsages, params.expectedLineItems);

    List<LineItem> generatedLineItems = doTestBillDaily(params);
    assertEquals(
        1, generatedLineItems.size(), "One line item with cluster description should be billed");
    assertEquals(6, generatedLineItems.get(0).getTotalPriceCents());
    assertEquals(0.05583150684931507, generatedLineItems.get(0).getUnitPriceDollars(), 0.0);
  }

  // Tests MeterBiller#billDaily - passes BillDailyParams#billDailyParams as an arg and expects
  // BillDailyParams#expectedLineItems result
  private List<LineItem> doTestBillDaily(BillDailyParams billDailyParams) {
    for (MeterUsage meterUsage : billDailyParams.meterUsages) {
      save(meterUsage);
    }

    OrgBillContext orgBillContext = getOrgBillContext(invoice);
    BillerContext context =
        new BillerContext.Builder(orgBillContext).sku(billDailyParams.sku).build();

    List<LineItem> actualLineItems = meterBiller.billDaily(context).collect(Collectors.toList());

    assertEquals(billDailyParams.expectedLineItems.size(), actualLineItems.size());
    String testFailureMsg =
        String.format(
            "Line items for sku %s should be generated from meter usage as expected",
            billDailyParams.sku);
    assertEquals(
        Set.copyOf(billDailyParams.expectedLineItems),
        new HashSet<>(actualLineItems),
        testFailureMsg);
    return actualLineItems;
  }

  private OrgBillContext getOrgBillContext(Invoice invoice) {
    return new OrgBillContext(
        org,
        group,
        invoice,
        JAN1,
        JAN2,
        new OrgPlan.Builder().orgId(org.getId()).build(),
        new OrgPrepaidPlan.Builder().build());
  }

  private BillDailyParams premiumUpchargeDimensionsParams(
      SKU sku, MeterId meterId, PricingStrategy pricingStrategy) {
    clusterDescriptionDao.save(
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                group.getId(), String.format("test-cluster-%s", 0))));

    List<ClusterDescription> clusterDescriptions = clusterDescriptionDao.findByGroup(group.getId());
    ClusterDescription clusterDescription = clusterDescriptions.get(0);

    MeterUsage meterUsage =
        new MeterUsage.Builder(AppEnv.TEST)
            .id(ObjectId.get())
            .startTime(JAN1)
            .endTime(JAN2)
            .reportedTime(JAN2)
            .meterId(meterId)
            .unit(SKUUnits.HOURS)
            .quantity(1)
            .groupId(group.getId())
            .usageDimensions(
                AtlasPremiumSkusUsageDimensions.premiumAws(
                    clusterDescription.getUniqueId(), "test-cluster", AWSRegionName.EU_CENTRAL_1))
            .build();

    double rate =
        pricingStrategy.getUnitPriceDollars(
            sku,
            skuPricingSvc.getPricing(sku, JAN2),
            new MeterUsageAggregateView.Builder(meterUsage).build(),
            new AdditionalPricingInfo(invoice, JAN1),
            appSettings);
    LineItem lineItem =
        makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), rate)
            .clusterUniqueId(clusterDescription.getUniqueId())
            .awsRegionName(AWSRegionName.EU_CENTRAL_1)
            .clusterName("test-cluster")
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams baseDimensionsParams(SKU sku, MeterId meterId) {
    when(meterBiller.getMeterId(sku)).thenReturn(meterId);

    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomGcpBaseMeterUsage(
            meterId, group.getId(), JAN1, JAN2, CLUSTER_UNIQUE_ID, CLUSTER_NAME);

    GcpBaseUsageDimensions usageDimensions =
        (GcpBaseUsageDimensions) meterUsage.getUsageDimensions();

    double price =
        new GCPBasePricing()
            .getUnitPriceDollars(
                sku,
                skuPricingSvc.getPricing(sku, JAN2),
                new MeterUsageAggregateView.Builder(meterUsage).build(),
                new AdditionalPricingInfo(invoice, JAN1),
                appSettings);
    LineItem lineItem =
        makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), price)
            .gcpBase(usageDimensions.getGcpBase())
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterName(CLUSTER_NAME)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams clusterOnlyDimensionsParams(SKU sku, MeterId meterId) {
    when(meterBiller.getMeterId(sku)).thenReturn(meterId);

    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomClusterOnlyDimensionsMeterUsage(
            meterId, group.getId(), JAN1, JAN2, CLUSTER_UNIQUE_ID, CLUSTER_NAME);

    double price =
        new UnitPricing()
            .getUnitPriceDollars(
                sku,
                skuPricingSvc.getPricing(sku, JAN2),
                new MeterUsageAggregateView.Builder(meterUsage).build(),
                new AdditionalPricingInfo(invoice, JAN1),
                appSettings);
    LineItem lineItem =
        makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), price)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterName(CLUSTER_NAME)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams gcpDataTransferParams(SKU sku, MeterId meterId) {
    when(meterBiller.getMeterId(sku)).thenReturn(meterId);
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomGcpDataTransferMeterUsage(
            meterId, group.getId(), JAN1, JAN2, CLUSTER_UNIQUE_ID, CLUSTER_NAME);

    GcpDataTransferUsageDimensions usageDimensions =
        (GcpDataTransferUsageDimensions) meterUsage.getUsageDimensions();

    MeterUsageAggregateView aggView = new MeterUsageAggregateView.Builder(meterUsage).build();

    LineItem lineItem =
        makeLineItemFromSingleUsage(
                sku,
                meterUsage,
                org.getId(),
                new GCPDataTransferPricing()
                    .getUnitPriceDollars(
                        sku,
                        skuPricingSvc.getPricing(sku, JAN2),
                        aggView,
                        new AdditionalPricingInfo(invoice, JAN1),
                        appSettings))
            .gcpDest(usageDimensions.getGcpDest())
            .gcpSource(usageDimensions.getGcpSource())
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterName(CLUSTER_NAME)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams gcpRegionalMeterUsageParams(
      SKU sku, MeterId meterId, GCPRegionName regionName) {
    when(meterBiller.getMeterId(sku)).thenReturn(meterId);
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomGcpMeterUsage(
            meterId, group.getId(), regionName, JAN1, JAN2);

    MeterUsageAggregateView aggView = new MeterUsageAggregateView.Builder(meterUsage).build();

    GcpUsageDimensions usageDimensions = (GcpUsageDimensions) meterUsage.getUsageDimensions();
    LineItem lineItem =
        makeLineItemFromSingleUsage(
                sku,
                meterUsage,
                org.getId(),
                new GCPRegionPricing()
                    .getUnitPriceDollars(
                        sku,
                        skuPricingSvc.getPricing(sku, JAN2),
                        aggView,
                        new AdditionalPricingInfo(invoice, JAN1),
                        appSettings))
            .gcpRegionName(regionName)
            .clusterName(usageDimensions.getClusterName())
            .clusterUniqueId(usageDimensions.getClusterUniqueId())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams awsRegionalMeterUsageParams(
      SKU sku, MeterId meterId, AWSRegionName regionName) {
    when(meterBiller.getMeterId(sku)).thenReturn(meterId);
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomAwsMeterUsage(
            meterId, group.getId(), regionName, "Test Cluster 1", JAN1, JAN2);

    MeterUsageAggregateView aggView = new MeterUsageAggregateView.Builder(meterUsage).build();

    AwsUsageDimensions usageDimensions = (AwsUsageDimensions) meterUsage.getUsageDimensions();
    LineItem lineItem =
        makeLineItemFromSingleUsage(
                sku,
                meterUsage,
                org.getId(),
                new AWSRegionPricing()
                    .getUnitPriceDollars(
                        sku,
                        skuPricingSvc.getPricing(sku, JAN2),
                        aggView,
                        new AdditionalPricingInfo(invoice, JAN1),
                        appSettings))
            .awsRegionName(regionName)
            .clusterName(usageDimensions.getClusterName())
            .clusterUniqueId(usageDimensions.getClusterUniqueId())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams azureRegionalMeterUsageParams(
      SKU sku, MeterId meterId, AzureRegionName regionName) {
    when(meterBiller.getMeterId(sku)).thenReturn(meterId);
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomAzureMeterUsage(
            meterId, group.getId(), regionName, "Test Cluster 1", JAN1, JAN2);

    MeterUsageAggregateView aggView = new MeterUsageAggregateView.Builder(meterUsage).build();
    AzureUsageDimensions usageDimensions = (AzureUsageDimensions) meterUsage.getUsageDimensions();

    LineItem lineItem =
        makeLineItemFromSingleUsage(
                sku,
                meterUsage,
                org.getId(),
                new AzureRegionPricing()
                    .getUnitPriceDollars(
                        sku,
                        skuPricingSvc.getPricing(sku, JAN2),
                        aggView,
                        new AdditionalPricingInfo(invoice, JAN1),
                        appSettings))
            .azureRegionName(regionName)
            .clusterName(usageDimensions.getClusterName())
            .clusterUniqueId(usageDimensions.getClusterUniqueId())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private LineItem.Builder buildDataLakeLineItem(
      SKU sku, MeterUsage meterUsage, PricingStrategy pricingStrategy) {
    double price =
        pricingStrategy.getUnitPriceDollars(
            sku,
            skuPricingSvc.getPricing(sku, JAN2),
            new MeterUsageAggregateView.Builder(meterUsage).build(),
            new AdditionalPricingInfo(invoice, JAN1),
            appSettings);
    return makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), price)
        .dataLakeTenantName(TENANT_NAME)
        .dataLakeTenantId(TENANT_ID);
  }

  private BillDailyParams dataLakeAwsTenantIdParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeTenantOnlyMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    LineItem lineItem =
        buildDataLakeLineItem(SKU.DATA_LAKE_AWS_DATA_SCANNED, meterUsage, new UnitPricing())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeAzureTenantIdParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeTenantOnlyMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    LineItem lineItem =
        buildDataLakeLineItem(SKU.DATA_FEDERATION_AZURE_DATA_SCANNED, meterUsage, new UnitPricing())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeGcpTenantIdParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeTenantOnlyMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    LineItem lineItem =
        buildDataLakeLineItem(SKU.DATA_FEDERATION_GCP_DATA_SCANNED, meterUsage, new UnitPricing())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams gcpSnapshotParams(SKU sku) {
    ObjectId clusterId = ObjectId.get();
    GCPRegionName regionName = GCPRegionName.EASTERN_US;
    String clusterName = ObjectId.get().toString();

    MeterUsage meterUsage =
        MeterUsageTestFactory.createMeterUsage(
            sku.getInfo().getMeterId(),
            group.getId(),
            JAN1,
            JAN2,
            1,
            new GcpSnapshotUsageDimensions.Builder()
                .clusterName(clusterName)
                .clusterUniqueId(clusterId)
                .snapshotId(ObjectId.get())
                .gcpRegionName(regionName)
                .build());

    double price =
        new GCPRegionPricing()
            .getUnitPriceDollars(
                sku,
                skuPricingSvc.getPricing(sku, JAN2),
                new MeterUsageAggregateView.Builder(meterUsage).build(),
                new AdditionalPricingInfo(invoice, JAN1),
                appSettings);

    LineItem lineItem =
        makeLineItemFromSingleUsage(SKU.GCP_SNAPSHOT_COPY_DISK, meterUsage, org.getId(), price)
            .clusterUniqueId(clusterId)
            .gcpRegionName(regionName)
            .clusterName(clusterName)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams awsSearchInstanceParams(SKU sku) {
    ObjectId clusterId = ObjectId.get();
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    String clusterName = ObjectId.get().toString();

    MeterUsage meterUsage =
        MeterUsageTestFactory.createMeterUsage(
            sku.getInfo().getMeterId(),
            group.getId(),
            JAN1,
            JAN2,
            1,
            new AwsSearchInstanceUsageDimensions.Builder()
                .clusterName(clusterName)
                .clusterUniqueId(clusterId)
                .awsRegionName(regionName)
                .build());

    double price =
        new AWSRegionPricing()
            .getUnitPriceDollars(
                sku,
                skuPricingSvc.getPricing(sku, JAN2),
                new MeterUsageAggregateView.Builder(meterUsage).build(),
                new AdditionalPricingInfo(invoice, JAN1),
                appSettings);

    LineItem lineItem =
        makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), price)
            .clusterUniqueId(clusterId)
            .awsRegionName(regionName)
            .clusterName(clusterName)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams gcpSearchInstanceParams(SKU sku) {
    ObjectId clusterId = ObjectId.get();
    GCPRegionName regionName = GCPRegionName.CENTRAL_US;
    String clusterName = ObjectId.get().toString();

    MeterUsage meterUsage =
        MeterUsageTestFactory.createMeterUsage(
            sku.getInfo().getMeterId(),
            group.getId(),
            JAN1,
            JAN2,
            1,
            new GcpSearchInstanceUsageDimensions.Builder()
                .clusterName(clusterName)
                .clusterUniqueId(clusterId)
                .gcpRegionName(regionName)
                .build());

    double price =
        new GCPRegionPricing()
            .getUnitPriceDollars(
                sku,
                skuPricingSvc.getPricing(sku, JAN2),
                new MeterUsageAggregateView.Builder(meterUsage).build(),
                new AdditionalPricingInfo(invoice, JAN1),
                appSettings);

    LineItem lineItem =
        makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), price)
            .clusterUniqueId(clusterId)
            .gcpRegionName(regionName)
            .clusterName(clusterName)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams azureSearchInstanceParams(SKU sku) {
    ObjectId clusterId = ObjectId.get();
    AzureRegionName regionName = AzureRegionName.AUSTRALIA_CENTRAL;
    String clusterName = ObjectId.get().toString();

    MeterUsage meterUsage =
        MeterUsageTestFactory.createMeterUsage(
            sku.getInfo().getMeterId(),
            group.getId(),
            JAN1,
            JAN2,
            1,
            new AzureSearchInstanceUsageDimensions.Builder()
                .clusterName(clusterName)
                .clusterUniqueId(clusterId)
                .azureRegionName(regionName)
                .build());

    double price =
        new AzureRegionPricing()
            .getUnitPriceDollars(
                sku,
                skuPricingSvc.getPricing(sku, JAN2),
                new MeterUsageAggregateView.Builder(meterUsage).build(),
                new AdditionalPricingInfo(invoice, JAN1),
                appSettings);

    LineItem lineItem =
        makeLineItemFromSingleUsage(sku, meterUsage, org.getId(), price)
            .clusterUniqueId(clusterId)
            .azureRegionName(regionName)
            .clusterName(clusterName)
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeAwsDataRegionParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeAwsDataRegionMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    AWSRegionNameGetter awsRegionNameGetter = (AWSRegionNameGetter) meterUsage.getUsageDimensions();

    LineItem lineItem =
        buildDataLakeLineItem(sku, meterUsage, new AWSRegionPricing())
            .awsRegionName(awsRegionNameGetter.getAwsRegionName())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeAzureDataRegionParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeAzureDataRegionMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    AzureRegionNameGetter azureRegionNameGetter =
        (AzureRegionNameGetter) meterUsage.getUsageDimensions();

    LineItem lineItem =
        buildDataLakeLineItem(sku, meterUsage, new AzureRegionPricing())
            .azureRegionName(azureRegionNameGetter.getAzureRegionName())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeGcpDataRegionParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeGcpDataRegionMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    GcpRegionNameGetter gcpRegionNameGetter = (GcpRegionNameGetter) meterUsage.getUsageDimensions();

    LineItem lineItem =
        buildDataLakeLineItem(sku, meterUsage, new GCPRegionPricing())
            .gcpRegionName(gcpRegionNameGetter.getGcpRegionName())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeAwsHostRegionParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeAwsHostRegionMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    AWSRegionNameGetter awsRegionNameGetter = (AWSRegionNameGetter) meterUsage.getUsageDimensions();

    LineItem lineItem =
        buildDataLakeLineItem(sku, meterUsage, new AWSRegionPricing())
            .awsRegionName(awsRegionNameGetter.getAwsRegionName())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeAzureHostRegionParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeAzureHostRegionMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    AzureRegionNameGetter azureRegionNameGetter =
        (AzureRegionNameGetter) meterUsage.getUsageDimensions();

    LineItem lineItem =
        buildDataLakeLineItem(sku, meterUsage, new AzureRegionPricing())
            .azureRegionName(azureRegionNameGetter.getAzureRegionName())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private BillDailyParams dataLakeGcpHostRegionParams(SKU sku) {
    MeterUsage meterUsage =
        MeterUsageTestFactory.createRandomDataLakeGcpHostRegionMeterUsage(
            sku.getInfo().getMeterId(), group.getId(), JAN1, JAN2, TENANT_ID);

    GcpRegionNameGetter gcpRegionNameGetter = (GcpRegionNameGetter) meterUsage.getUsageDimensions();

    LineItem lineItem =
        buildDataLakeLineItem(sku, meterUsage, new GCPRegionPricing())
            .gcpRegionName(gcpRegionNameGetter.getGcpRegionName())
            .build();

    return new BillDailyParams(sku, List.of(meterUsage), List.of(lineItem));
  }

  private Builder makeLineItemFromSingleUsage(
      SKU sku, MeterUsage meterUsage, ObjectId orgId, double price) {
    return new LineItem.Builder()
        .orgId(orgId)
        .groupId(meterUsage.getGroupId())
        .startDate(meterUsage.getStartTime())
        .endDate(meterUsage.getEndTime())
        .quantity(meterUsage.getQuantity())
        .unitPriceDollars(price)
        .invoiceId(invoice.getId())
        .meterUsageIds(List.of(meterUsage.getId()))
        .created(JAN3)
        .usageDate(JAN1)
        .sku(sku);
  }

  /**
   * Helper class to hold parameters for testing {@link MeterBiller#billDaily} - meter usages passed
   * in as an argument and expected generated stream of line items.
   */
  private static class BillDailyParams {
    private final SKU sku;
    private final Collection<MeterUsage> meterUsages;
    private final Collection<LineItem> expectedLineItems;

    private BillDailyParams(
        SKU sku, Collection<MeterUsage> meterUsages, Collection<LineItem> expectedLineItems) {
      this.sku = sku;
      this.meterUsages = meterUsages;
      this.expectedLineItems = expectedLineItems;
    }
  }
}
