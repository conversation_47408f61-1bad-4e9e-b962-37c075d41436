load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/svc/mms/svc/billing/audit",
        "//server/src/main/com/xgen/svc/mms/svc/billing/cron",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
