package com.xgen.svc.mms.svc.billing;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.BillingAddress;
import com.xgen.cloud.group._public.model.CompanyAddress;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus.Type;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.activity.OrgAudit;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.OFACHitTrackDao;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.svc.billing.ofac.OFACClient;
import com.xgen.svc.mms.svc.billing.ofac.OFACClient.OFACCheckResult;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.Date;
import java.util.List;
import org.json.JSONArray;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;

public class OFACCheckSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private OFACCheckSvc _ofacCheckSvc;
  @Inject private OrganizationFactory organizationFactory;
  @Inject private OFACHitTrackDao ofacHitTrackDao;
  @Inject private OrganizationDao organizationDao;
  @Inject private AuditSvc auditSvc;

  @Inject
  @Named("opensanctionsClient")
  private OFACClient ofacClient;

  @BeforeEach
  public void setUp() {
    reset(ofacClient);
  }

  @Test
  public void testCheckIndividual() throws SvcException {
    Organization org = organizationFactory.createAtlasOrganization(new Date());

    when(ofacClient.checkIndividual("Osama bin Laden", ""))
        .thenReturn(Mono.just(new OFACCheckResult(new JSONArray(List.of("OBL!")))));
    when(ofacClient.checkIndividual("Osama bin Laden", "SA"))
        .thenReturn(Mono.just(new OFACCheckResult(new JSONArray(List.of("OBL!")))));

    PaymentMethod binLadenPaymentMethod =
        new PaymentMethod.Builder()
            .nameOnCard("Osama bin Laden")
            .type(PaymentMethodType.CREDIT_CARD)
            .billingAddress(new BillingAddress.Builder().country("").build())
            .build();
    // Osama bin Laden is a terrorist
    // checking no country as input, no country returned by OFAC check, should return a hit.
    assertThrows(
        SvcException.class, () -> getOFACCheckSvc().checkPaymentMethod(org, binLadenPaymentMethod));

    PaymentMethod binLadenPaymentMethodWithCountry =
        new PaymentMethod.Builder()
            .nameOnCard("Osama bin Laden")
            .type(PaymentMethodType.CREDIT_CARD)
            .billingAddress(new BillingAddress.Builder().country("SA").build())
            .build();
    // Osama bin Laden is a terrorist
    // checking country entered as input, and no country is returned by OFAC check. should
    // hit.
    assertThrows(
        SvcException.class,
        () -> getOFACCheckSvc().checkPaymentMethod(org, binLadenPaymentMethodWithCountry));

    PaymentMethod johnDoePaymentMethod =
        new PaymentMethod.Builder()
            .nameOnCard("John Doe")
            .type(PaymentMethodType.CREDIT_CARD)
            .billingAddress(new BillingAddress.Builder().country("US").build())
            .build();

    // John Doe is not a terrorist.
    _ofacCheckSvc.checkPaymentMethod(org, johnDoePaymentMethod);
  }

  @Test
  public void testCheckCompanyAddress_companyName() throws SvcException {
    when(ofacClient.checkEntity("Al Qaeda"))
        .thenReturn(Mono.just(new OFACCheckResult(new JSONArray(List.of("Al Qaeda!")))));

    Organization org = organizationFactory.createAtlasOrganization(new Date());
    CompanyAddress alQaedaAddr =
        new CompanyAddress.Builder().country("IQ").companyName("Al Qaeda").build();

    // Al Qaeda is a terrorist organization.
    assertThrows(SvcException.class, () -> _ofacCheckSvc.checkCompanyAddress(org, alQaedaAddr));

    when(ofacClient.checkEntity("MongoDB"))
        .thenReturn(Mono.just(new OFACCheckResult(new JSONArray())));

    // MongoDB, Inc. is not a terrorist organization.
    CompanyAddress mongo =
        new CompanyAddress.Builder().country("US").companyName("MongoDB").build();

    _ofacCheckSvc.checkCompanyAddress(org, mongo);
  }

  @Test
  public void testCheckPaymentMethod_nonEmbargoedCountry() throws SvcException {
    Organization org = organizationFactory.createAtlasOrganization(new Date());
    PaymentMethod belarusPaymentMethod =
        new PaymentMethod.Builder()
            .nameOnCard("name")
            .billingAddress(new BillingAddress.Builder().country("US").build())
            .type(PaymentMethodType.PAYPAL)
            .build();

    _ofacCheckSvc.checkPaymentMethod(org, belarusPaymentMethod);

    Organization updatedOrg = organizationDao.findById(org.getId());
    assertThat(updatedOrg.getPaymentStatus().getStatus(), equalTo(Type.OK));
  }

  @Test
  public void testCheckPaymentMethod_belarusAddress() {
    Organization org = organizationFactory.createAtlasOrganization(new Date());
    PaymentMethod belarusPaymentMethod =
        new PaymentMethod.Builder()
            .nameOnCard("name")
            .billingAddress(new BillingAddress.Builder().country("BY").build())
            .build();

    assertThrows(
        SvcException.class, () -> _ofacCheckSvc.checkPaymentMethod(org, belarusPaymentMethod));
    assertOrgEmbargoConfirmed(org);
  }

  @Test
  public void testCheckPaymentMethod_russianAddress() {
    Organization org = organizationFactory.createAtlasOrganization(new Date());
    PaymentMethod russianPaymentMethod =
        new PaymentMethod.Builder()
            .nameOnCard("name")
            .billingAddress(new BillingAddress.Builder().country("RU").build())
            .build();

    assertThrows(
        SvcException.class, () -> _ofacCheckSvc.checkPaymentMethod(org, russianPaymentMethod));
    assertOrgEmbargoConfirmed(org);
  }

  @Test
  public void testCheckCompanyAddress_nonEmbargoedCountry() throws SvcException {
    Organization org = organizationFactory.createAtlasOrganization(new Date());

    CompanyAddress belarusAddress =
        new CompanyAddress.Builder().country("US").companyName(null).build();

    _ofacCheckSvc.checkCompanyAddress(org, belarusAddress);

    Organization updatedOrg = organizationDao.findById(org.getId());
    assertThat(updatedOrg.getPaymentStatus().getStatus(), equalTo(Type.OK));
  }

  @Test
  public void testCheckCompanyAddress_belarusAddress() {
    Organization org = organizationFactory.createAtlasOrganization(new Date());
    CompanyAddress belarusAddress = new CompanyAddress.Builder().country("BY").build();

    assertThrows(SvcException.class, () -> _ofacCheckSvc.checkCompanyAddress(org, belarusAddress));
    assertOrgEmbargoConfirmed(org);
  }

  @Test
  public void testCheckCompanyAddress_russianAddress() throws SvcException {
    Organization org = organizationFactory.createAtlasOrganization(new Date());
    CompanyAddress russianAddress = new CompanyAddress.Builder().country("RU").build();

    assertThrows(SvcException.class, () -> _ofacCheckSvc.checkCompanyAddress(org, russianAddress));
    assertOrgEmbargoConfirmed(org);
  }

  private void assertOrgEmbargoConfirmed(Organization org) {

    Organization updatedOrg = organizationDao.findById(org.getId());
    assertThat(updatedOrg.getPaymentStatus().getStatus(), equalTo(Type.EMBARGO_CONFIRMED));

    List<Event> ofacHitAuditEvents =
        auditSvc.findByEventTypeForOrganization(
            org.getId(), OrgAudit.Type.ORG_COMPANY_NAME_OFAC_HIT);
    List<Event> ofacEmbargoConfirmedEvents =
        auditSvc.findByEventTypeForOrganization(org.getId(), OrgAudit.Type.ORG_EMBARGO_CONFIRMED);

    assertThat(ofacHitAuditEvents.size(), equalTo(1));
    assertThat(ofacEmbargoConfirmedEvents.size(), equalTo(1));
  }

  protected OFACCheckSvc getOFACCheckSvc() {
    return _ofacCheckSvc;
  }
}
