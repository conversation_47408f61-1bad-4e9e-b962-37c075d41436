package com.xgen.svc.mms.svc.billing.audit.auditors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport.Status;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.PartnerUsageReportFactory;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class PartnerCreditUsageReportAuditorIntTests extends JUnit5BaseSvcTest {

  @Inject private CreditFactory creditFactory;

  @Inject private CreditDao creditDao;

  @Inject private PartnerUsageReportFactory partnerUsageReportFactory;

  @Inject private OrganizationFactory organizationFactory;

  @Inject private PartnerCreditUsageReportAuditor partnerCreditUsageReportAuditor;

  @Test
  public void testAuditSuccess() {

    Date startDate = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date endDate = DateUtils.addYears(startDate, 1);
    Organization org = organizationFactory.createAtlasOrganization(startDate);
    ObjectId orgId = org.getId();
    Credit credit = creditFactory.createAwsSelfServeCredit(orgId, startDate, endDate);

    // audit PASSES when credit has no usage reports generated and 0 amount billed
    Optional<AuditFailureDetail> result = partnerCreditUsageReportAuditor.audit(credit);
    assertTrue(result.isEmpty());

    // audit PASSES when credit has some usage reports generated and matching amount billed
    long billedAmountCents = 4000;
    createUsageReport(org, credit, billedAmountCents, startDate);
    createUsageReport(org, credit, billedAmountCents, startDate);
    creditDao.setTotalBilledCents(credit.getId(), billedAmountCents * 2);
    Credit updatedCredit = creditDao.findById(credit.getId());
    result = partnerCreditUsageReportAuditor.audit(updatedCredit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void testAuditFailure() {

    Date startDate = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date endDate = DateUtils.addYears(startDate, 1);
    Organization org = organizationFactory.createAtlasOrganization(startDate);
    ObjectId orgId = org.getId();
    Credit credit = creditFactory.createAwsSelfServeCredit(orgId, startDate, endDate);
    creditDao.setTotalBilledCents(credit.getId(), 200);
    Credit updatedCredit = creditDao.findById(credit.getId());
    // audit FAILS when credit has no usage reports generated and 0 amount billed
    Optional<AuditFailureDetail> result = partnerCreditUsageReportAuditor.audit(updatedCredit);
    assertFalse(result.isEmpty());
    assertEquals(result.get().getAuditContext().getCreditId(), credit.getId());
    assertEquals(result.get().getAuditContext().getOrgId(), credit.getOrgId());
    assertEquals(result.get().getAuditContext().getAuditEntityKey(), credit.getId().toString());
    assertEquals(
        result.get().getBillingAuditErrors().get(0).getArgs().get("creditAmountBilled"), "200");
    assertEquals(
        result.get().getBillingAuditErrors().get(0).getArgs().get("usageReportsAmountBilled"), "0");

    // audit FAILS when credit has some usage reports generated and matching amount billed
    long billedAmountCents = 4000;
    createUsageReport(org, credit, billedAmountCents, startDate);
    createUsageReport(org, credit, billedAmountCents, startDate);
    creditDao.setTotalBilledCents(credit.getId(), billedAmountCents * 2 + 10);
    updatedCredit = creditDao.findById(credit.getId());
    result = partnerCreditUsageReportAuditor.audit(updatedCredit);
    assertFalse(result.isEmpty());
    assertEquals(result.get().getAuditContext().getCreditId(), credit.getId());
    assertEquals(result.get().getAuditContext().getOrgId(), credit.getOrgId());
    assertEquals(result.get().getAuditContext().getAuditEntityKey(), credit.getId().toString());
    assertEquals(
        result.get().getBillingAuditErrors().get(0).getArgs().get("creditAmountBilled"), "8010");
    assertEquals(
        result.get().getBillingAuditErrors().get(0).getArgs().get("usageReportsAmountBilled"),
        "8000");
  }

  @Test
  public void testAuditSuccess_gcpMcRollover() {
    ObjectId creditId =
        creditDao.save(
            Credit.builder()
                .type(CreditType.GCP_MONTHLY_COMMITMENT_ROLLOVER)
                .startDate(TimeUtils2.fromISOString("2024-01-01T00:00:00Z"))
                .endDate(TimeUtils2.fromISOString("2026-01-01T00:00:00Z"))
                .amountCents(12_000_00)
                .build());
    Credit credit = creditDao.findById(creditId);

    // audit PASSES when credit has no usage reports generated and 0 amount billed
    Optional<AuditFailureDetail> result = partnerCreditUsageReportAuditor.audit(credit);
    assertThat(result).isEmpty();
  }

  @Test
  public void testAuditFailure_gcpMcRollover() {
    ObjectId creditId =
        creditDao.save(
            Credit.builder()
                .orgId(new ObjectId())
                .type(CreditType.GCP_MONTHLY_COMMITMENT_ROLLOVER)
                .startDate(TimeUtils2.fromISOString("2024-01-01T00:00:00Z"))
                .endDate(TimeUtils2.fromISOString("2026-01-01T00:00:00Z"))
                .amountCents(12_000_00)
                .totalBilledCents(100_00)
                .build());
    Credit credit = creditDao.findById(creditId);

    // audit FAILS when credit has non-zero totalBilledCents
    Optional<AuditFailureDetail> result = partnerCreditUsageReportAuditor.audit(credit);
    assertThat(result).isNotEmpty();
    assertThat(result.get().getAuditContext().getCreditId()).isEqualTo(credit.getId());
    assertThat(result.get().getAuditContext().getOrgId()).isEqualTo(credit.getOrgId());
    assertThat(result.get().getAuditContext().getAuditEntityKey())
        .isEqualTo(credit.getId().toString());
    assertThat(result.get().getBillingAuditErrors().get(0).getErrorCode())
        .isEqualTo(BillingAuditErrorCode.GCP_MC_V2_ROLLOVER_HAS_USAGE_REPORTS);
    assertThat(result.get().getBillingAuditErrors().get(0).getArgs())
        .containsExactly(
            entry("creditAmountBilled", "10000"), entry("usageReportsAmountBilled", "0"));

    partnerUsageReportFactory.createGcpUsageReport(
        credit.getOrgId(),
        credit.getId(),
        "AccountId",
        "EntitlementId",
        "UsageReportingId",
        "Metric",
        new Date(),
        credit.getStartDate(),
        DateUtils.addDays(credit.getStartDate(), 1),
        100_00,
        100_00,
        false,
        Status.SUCCESSFUL);

    // audit FAILS when credit even if totalBilledCents is equal to reported cents
    result = partnerCreditUsageReportAuditor.audit(credit);
    assertThat(result).isNotEmpty();
    assertThat(result.get().getAuditContext().getCreditId()).isEqualTo(credit.getId());
    assertThat(result.get().getAuditContext().getOrgId()).isEqualTo(credit.getOrgId());
    assertThat(result.get().getAuditContext().getAuditEntityKey())
        .isEqualTo(credit.getId().toString());
    assertThat(result.get().getBillingAuditErrors().get(0).getErrorCode())
        .isEqualTo(BillingAuditErrorCode.GCP_MC_V2_ROLLOVER_HAS_USAGE_REPORTS);
    assertThat(result.get().getBillingAuditErrors().get(0).getArgs())
        .containsExactly(
            entry("creditAmountBilled", "10000"), entry("usageReportsAmountBilled", "10000"));
  }

  private PartnerUsageReport createUsageReport(
      Organization org, Credit credit, long billedAmountCents, Date startDate) {
    return partnerUsageReportFactory.createAwsUsageReport(
        org,
        "productCode",
        "customerId",
        "dimension",
        DateUtils.addDays(startDate, 1),
        DateUtils.addDays(startDate, 1),
        DateUtils.addDays(startDate, 2),
        billedAmountCents,
        billedAmountCents,
        Status.NEW,
        credit.getId(),
        true);
  }
}
