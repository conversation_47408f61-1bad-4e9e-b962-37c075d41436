load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku/_public/model",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/svc/billing/jsoncomparator",
        "//server/src/main/com/xgen/svc/mms/svc/billing/regressiontests",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
