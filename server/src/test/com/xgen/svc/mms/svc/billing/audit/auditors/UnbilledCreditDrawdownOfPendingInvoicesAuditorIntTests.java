package com.xgen.svc.mms.svc.billing.audit.auditors;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static com.xgen.cloud.common.util._public.time.DateTimeUtils.localDateOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditError;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode;
import com.xgen.cloud.billingplatform.crossorg._public.svc.OrgLinkingSvc;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.svc.billing.AccountantSvc;
import com.xgen.svc.mms.util.billing.scenarios.SalesSoldScenarios;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory.FlexCommitRequest;
import com.xgen.svc.mms.util.billing.testFactories.SalesforceOpportunityFactory.FlexScenarioType;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;

public class UnbilledCreditDrawdownOfPendingInvoicesAuditorIntTests extends JUnit5BaseSvcTest {
  @Inject private InvoiceDao invoiceDao;
  @Inject private CreditDao creditDao;

  @Inject
  private UnbilledCreditDrawdownOnPendingInvoicesAuditor
      unbilledCreditDrawdownOnPendingInvoicesAuditor;

  @Inject private SalesSoldScenarios salesSoldScenarios;
  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;
  @Inject private LineItemDao lineItemDao;
  @Inject private OrganizationFactory organizationFactory;
  @Inject private AccountantSvc accountantSvc;
  @Inject private OrgLinkingSvc orgLinkingSvc;

  Date beginningOfMonth = dateOf(LocalDate.now().minusMonths(1).withDayOfMonth(1));
  Date midMonth = DateUtils.addDays(beginningOfMonth, 15);
  Date afterOneMonth = DateUtils.addMonths(beginningOfMonth, 1);
  Date midSecondMonth = DateUtils.addDays(afterOneMonth, 15);
  Date threeMonthsFromNow = DateUtils.addMonths(beginningOfMonth, 3);

  private AutoCloseable mocks;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    mocks = MockitoAnnotations.openMocks(this);
    salesforceProductCodeDao.syncDocuments();
  }

  @AfterEach
  public void tearDown() throws Exception {
    mocks.close();
  }

  @Test
  public void auditSuccessPrepaidZeroDollarPrepaidFirstMonth() throws Exception {
    Organization org =
        salesSoldScenarios.createOrgWithProPrepaidOpportunity(
            beginningOfMonth, threeMonthsFromNow, 0);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditSuccessPrepaidZeroDollarPrepaidFirstMonthTurnsElasticMidmonth()
      throws Exception {
    Organization org =
        salesSoldScenarios.createOrgWithProPrepaidOpportunity(
            beginningOfMonth, threeMonthsFromNow, 30000);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditSuccessPrepaidZeroDollarPrepaidSecondMonth() throws Exception {
    Organization org =
        salesSoldScenarios.createOrgWithProPrepaidOpportunity(
            beginningOfMonth, threeMonthsFromNow, 0);
    accountantSvc.processOrganizations(List.of(org.getId()), midSecondMonth);
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditSuccessElasticPaymentFlexCommit() throws Exception {
    FlexCommitRequest flexCommitRequest =
        new FlexCommitRequest(FlexScenarioType.DIRECT, 20000, 0, 1, localDateOf(beginningOfMonth));

    Organization org = salesSoldScenarios.activateFlexCommitDeal(flexCommitRequest);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditSuccessElasticPaymentZeroDollarMonthlyCommitTurnsElasticMidmonth()
      throws Exception {
    Organization org =
        salesSoldScenarios.createOrgWithProMonthlyCommit(
            beginningOfMonth, threeMonthsFromNow, 30000);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditSuccessCrossOrgPrepaid() throws Exception {
    Organization payingOrg =
        salesSoldScenarios.createOrgWithProPrepaidOpportunity(
            beginningOfMonth, threeMonthsFromNow, 0);
    salesSoldScenarios.generateUsageForOrg(payingOrg, beginningOfMonth, midMonth);

    Organization linkedOrg =
        organizationFactory.createAtlasOrganization(beginningOfMonth, "org with MOB");
    salesSoldScenarios.generateUsageForOrg(linkedOrg, beginningOfMonth, midMonth);

    orgLinkingSvc.linkToPayingOrg(
        payingOrg.getId(), List.of(linkedOrg.getId()), AuditInfoHelpers.fromSystem());

    accountantSvc.processOrganizations(List.of(payingOrg.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(payingOrg.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditSuccessCrossOrgMonthlyCommit() throws Exception {
    Organization payingOrg =
        salesSoldScenarios.createOrgWithProMonthlyCommit(beginningOfMonth, threeMonthsFromNow, 0);
    salesSoldScenarios.generateUsageForOrg(payingOrg, beginningOfMonth, midMonth);

    Organization linkedOrg =
        organizationFactory.createAtlasOrganization(beginningOfMonth, "org with MOB");
    salesSoldScenarios.generateUsageForOrg(linkedOrg, beginningOfMonth, midMonth);

    orgLinkingSvc.linkToPayingOrg(
        payingOrg.getId(), List.of(linkedOrg.getId()), AuditInfoHelpers.fromSystem());

    accountantSvc.processOrganizations(List.of(payingOrg.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(payingOrg.getId()).get(0);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    assertTrue(result.isEmpty());
  }

  @Test
  public void auditFailurePrepaidZeroDollarPrepaid() throws Exception {
    Organization org =
        salesSoldScenarios.createOrgWithProPrepaidOpportunity(
            beginningOfMonth, threeMonthsFromNow, 0);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Credit editedCredit =
        Credit.builder(credit).id(credit.getId()).amountRemainingCents(-100000).build();
    long supportChargesUpToMidMonth =
        lineItemDao.getInvoiceSubtotal(
            invoiceDao.findPendingMonthlyByOrgId(org.getId()).getId(), null);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(editedCredit);
    assertTrue(result.isPresent());
    validateError(result.get(), editedCredit, supportChargesUpToMidMonth);
  }

  @Test
  public void auditFailurePrepaidZeroDollarMonthlyCommit() throws Exception {
    Organization org =
        salesSoldScenarios.createOrgWithProMonthlyCommit(beginningOfMonth, threeMonthsFromNow, 0);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);
    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    Credit editedCredit =
        Credit.builder(credit).id(credit.getId()).amountRemainingCents(-100000).build();
    long supportChargesUpToMidMonth =
        lineItemDao.getInvoiceSubtotal(
            invoiceDao.findPendingMonthlyByOrgId(org.getId()).getId(), null);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(editedCredit);
    assertTrue(result.isPresent());
    validateError(result.get(), editedCredit, supportChargesUpToMidMonth);
  }

  @Test
  public void auditFailureElasticPaymentFlexCommit() throws Exception {
    // GIVEN
    FlexCommitRequest flexCommitRequest =
        new FlexCommitRequest(FlexScenarioType.DIRECT, 20000, 0, 1, localDateOf(beginningOfMonth));
    Organization org = salesSoldScenarios.activateFlexCommitDeal(flexCommitRequest);
    accountantSvc.processOrganizations(List.of(org.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(org.getId()).get(0);
    // WHEN: we have more elastic usage charged against the credit then there should be
    Credit editedCredit =
        Credit.builder(credit).id(credit.getId()).amountRemainingCents(-100000).build();
    long supportChargesUpToMidMonth =
        lineItemDao.getInvoiceSubtotal(
            invoiceDao.findPendingMonthlyByOrgId(org.getId()).getId(), null);
    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(editedCredit);

    // THEN: we get an auditor alert on a pending invoice
    assertTrue(result.isPresent());
    validateError(result.get(), editedCredit, supportChargesUpToMidMonth);
  }

  @Test
  public void auditFailureCrossOrgPrepaid() throws Exception {
    Organization payingOrg =
        salesSoldScenarios.createOrgWithProPrepaidOpportunity(
            beginningOfMonth, threeMonthsFromNow, 0);
    salesSoldScenarios.generateUsageForOrg(payingOrg, beginningOfMonth, midMonth);

    Organization linkedOrg =
        organizationFactory.createAtlasOrganization(beginningOfMonth, "org with MOB");
    salesSoldScenarios.generateUsageForOrg(linkedOrg, beginningOfMonth, midMonth);

    orgLinkingSvc.linkToPayingOrg(
        payingOrg.getId(), List.of(linkedOrg.getId()), AuditInfoHelpers.fromSystem());

    accountantSvc.processOrganizations(List.of(payingOrg.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(payingOrg.getId()).get(0);
    Invoice linkedOrgInvoice = invoiceDao.findPendingMonthlyByOrgId(linkedOrg.getId());

    lineItemDao.updateLineItemByCreditIdAndInvoiceId(
        credit.getId(), linkedOrgInvoice.getId(), 10000);

    List<Invoice> invoices =
        List.of(
            invoiceDao.findPendingMonthlyByOrgId(payingOrg.getId()),
            invoiceDao.findPendingMonthlyByOrgId(linkedOrg.getId()));
    long elasticCreditDrawdownOnPendingInvoices =
        -invoices.stream()
            .mapToLong(
                i ->
                    lineItemDao.getElasticCreditUsageByCreditIdAndInvoiceId(
                        credit.getId(), i.getId(), true))
            .sum();

    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    validateError(result.get(), credit, elasticCreditDrawdownOnPendingInvoices);
  }

  @Test
  public void auditFailureCrossOrgMonthlyCommit() throws Exception {
    Organization payingOrg =
        salesSoldScenarios.createOrgWithProMonthlyCommit(beginningOfMonth, threeMonthsFromNow, 0);
    salesSoldScenarios.generateUsageForOrg(payingOrg, beginningOfMonth, midMonth);

    Organization linkedOrg =
        organizationFactory.createAtlasOrganization(beginningOfMonth, "org with MOB");
    salesSoldScenarios.generateUsageForOrg(linkedOrg, beginningOfMonth, midMonth);

    orgLinkingSvc.linkToPayingOrg(
        payingOrg.getId(), List.of(linkedOrg.getId()), AuditInfoHelpers.fromSystem());

    accountantSvc.processOrganizations(List.of(payingOrg.getId()), midMonth);

    Credit credit = creditDao.findByOrgId(payingOrg.getId()).get(0);
    Invoice linkedOrgInvoice = invoiceDao.findPendingMonthlyByOrgId(linkedOrg.getId());

    lineItemDao.updateLineItemByCreditIdAndInvoiceId(
        credit.getId(), linkedOrgInvoice.getId(), 10000);

    List<Invoice> invoices =
        List.of(
            invoiceDao.findPendingMonthlyByOrgId(payingOrg.getId()),
            invoiceDao.findPendingMonthlyByOrgId(linkedOrg.getId()));
    long elasticCreditDrawdownOnPendingInvoices =
        -invoices.stream()
            .mapToLong(
                i ->
                    lineItemDao.getElasticCreditUsageByCreditIdAndInvoiceId(
                        credit.getId(), i.getId(), true))
            .sum();

    Optional<AuditFailureDetail> result =
        unbilledCreditDrawdownOnPendingInvoicesAuditor.audit(credit);
    validateError(result.get(), credit, elasticCreditDrawdownOnPendingInvoices);
  }

  @Test
  public void testGetIdsToAudit() {
    Date fiveMonthsAgo = dateOf(LocalDate.now().withDayOfMonth(1).minusMonths(5));
    Date thisMonth = dateOf(LocalDate.now().withDayOfMonth(1));
    Date inFiveMonths = dateOf(LocalDate.now().withDayOfMonth(1).plusMonths(5));

    Credit elasticPrepaidCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.PREPAID_NDS)
            .endDate(thisMonth)
            .totalBilledCents(0)
            .amountCents(0)
            .amountRemainingCents(-1000)
            .build();
    Credit nonElasticPrepaidCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.PREPAID_NDS)
            .endDate(thisMonth)
            .totalBilledCents(0)
            .amountCents(1000)
            .amountRemainingCents(100)
            .build();
    Credit newlyElasticPrepaidCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.PREPAID_NDS)
            .endDate(thisMonth)
            .totalBilledCents(0)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    creditDao.save(elasticPrepaidCredit);
    creditDao.save(nonElasticPrepaidCredit);
    creditDao.save(newlyElasticPrepaidCredit);

    Credit elasticMonthlyCommitmentCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.MONTHLY_COMMITMENT)
            .endDate(thisMonth)
            .totalBilledCents(10000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    Credit nonElasticMCCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.MONTHLY_COMMITMENT)
            .endDate(thisMonth)
            .totalBilledCents(0)
            .amountCents(1000)
            .amountRemainingCents(100)
            .build();
    Credit newlyElasticMCCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.MONTHLY_COMMITMENT)
            .endDate(thisMonth)
            .totalBilledCents(9000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    creditDao.save(elasticMonthlyCommitmentCredit);
    creditDao.save(nonElasticMCCredit);
    creditDao.save(newlyElasticMCCredit);

    Credit elasticFlexCommitmentCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.DIRECT_FLEX_COMMIT)
            .endDate(thisMonth)
            .totalBilledCents(10000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    Credit nonElasticFlexCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.DIRECT_FLEX_COMMIT)
            .endDate(thisMonth)
            .totalBilledCents(0)
            .amountCents(1000)
            .amountRemainingCents(100)
            .build();
    Credit newlyElasticFlexCredit =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.DIRECT_FLEX_COMMIT)
            .endDate(thisMonth)
            .totalBilledCents(9000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    creditDao.save(elasticFlexCommitmentCredit);
    creditDao.save(nonElasticFlexCredit);
    creditDao.save(newlyElasticFlexCredit);

    Credit elasticMonthlyCommitmentCreditThisMonth =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.MONTHLY_COMMITMENT)
            .endDate(thisMonth)
            .totalBilledCents(9000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    Credit elasticMonthlyCommitmentCreditFiveMonthsAgo =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.MONTHLY_COMMITMENT)
            .startDate(fiveMonthsAgo)
            .totalBilledCents(9000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    Credit elasticMonthlyCommitmentCreditPlusFiveMonths =
        Credit.builder()
            .id(new ObjectId())
            .type(CreditType.MONTHLY_COMMITMENT)
            .endDate(inFiveMonths)
            .totalBilledCents(9000)
            .amountCents(10000)
            .amountRemainingCents(-1000)
            .build();
    creditDao.save(elasticMonthlyCommitmentCreditThisMonth);
    creditDao.save(elasticMonthlyCommitmentCreditFiveMonthsAgo);
    creditDao.save(elasticMonthlyCommitmentCreditPlusFiveMonths);

    List<ObjectId> creditIds = unbilledCreditDrawdownOnPendingInvoicesAuditor.getIdsToAudit();
    assertEquals(creditIds.size(), 8);
    assertTrue(creditIds.contains(elasticPrepaidCredit.getId()));
    assertTrue(creditIds.contains(newlyElasticPrepaidCredit.getId()));
    assertTrue(creditIds.contains(elasticMonthlyCommitmentCredit.getId()));
    assertTrue(creditIds.contains(newlyElasticMCCredit.getId()));
    assertTrue(creditIds.contains(elasticFlexCommitmentCredit.getId()));
    assertTrue(creditIds.contains(newlyElasticFlexCredit.getId()));
    assertTrue(creditIds.contains(elasticMonthlyCommitmentCreditThisMonth.getId()));
    assertTrue(creditIds.contains(elasticMonthlyCommitmentCreditPlusFiveMonths.getId()));
  }

  private void validateError(
      AuditFailureDetail auditFailureDetail, Credit credit, long creditDrawdownOnPendingInvoices) {
    assertEquals(
        unbilledCreditDrawdownOnPendingInvoicesAuditor.getAuditorName(),
        auditFailureDetail.getAuditorName());
    List<BillingAuditError> errorCodes = auditFailureDetail.getBillingAuditErrors();

    assertEquals(1, errorCodes.size());
    assertEquals(
        BillingAuditErrorCode.CREDIT_DRAWDOWN_ON_PENDING_INVOICES_DOESNT_MATCH_CREDIT,
        errorCodes.get(0).getErrorCode());

    String expectedErrorMsg =
        String.format(
            BillingAuditErrorCode.CREDIT_DRAWDOWN_ON_PENDING_INVOICES_DOESNT_MATCH_CREDIT
                .getTemplate(),
            credit.getId(),
            credit.getType(),
            credit.getAmountUsedCents(),
            credit.getTotalBilledCents(),
            credit.getAmountRemainingCents(),
            creditDrawdownOnPendingInvoices);

    assertEquals(expectedErrorMsg, errorCodes.get(0).getErrorMessage());
  }
}
