package com.xgen.svc.mms.svc.ping;

import com.xgen.cloud.monitoring.metrics._public.model.TotalStorageCacheEntry;
import com.xgen.cloud.monitoring.metrics._public.model.TotalStorageCacheEntry.DBStatsEntry;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.monitoring.topology._public.model.CanonicalHostUtils;
import com.xgen.cloud.monitoring.topology._public.model.CanonicalHostUtils.CanonicalMappings;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.ping.DBStatsCacheDao;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.bson.BasicBSONObject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class TotalStorageCacheSvcIntTests extends JUnit5BaseSvcTest {

  private static final ObjectId GROUP_ID = oid(1);
  private static final String HOSTNAME = "hostname";
  private static final int PORT1 = 27017;
  private static final int PORT2 = 27018;

  private static final String HOST_ID1 = HostUtils.getHostId(GROUP_ID, HOSTNAME, PORT1);
  private static final String HOST_ID2 = HostUtils.getHostId(GROUP_ID, HOSTNAME, PORT2);

  @Inject private TotalStorageCacheSvc _totalStorageCacheSvc;

  @Inject private DBStatsCacheDao _dbStatsCacheDao;

  @Inject private HostDao _hostDao;

  @Test
  public void ingestDBStats() {
    final BasicBSONObject host1 = newHostPingBson(HOSTNAME, PORT1, "foo", "bar");
    final BasicBSONObject host2 = newHostPingBson(HOSTNAME, PORT2, "baz");
    final CanonicalMappings mappings =
        CanonicalHostUtils.initMappings(GROUP_ID, Collections.emptyList());

    _totalStorageCacheSvc.doDbStatsCalculations(
        GROUP_ID, mappings, new Date(), Arrays.asList(host1, host2), 10);

    final TotalStorageCacheEntry entry1 = _dbStatsCacheDao.findById(GROUP_ID, HOST_ID1);
    Assertions.assertNotNull(entry1, "Failed to find TotalStorageCacheEntry for " + HOST_ID1);
    Assertions.assertEquals(206821L, entry1.getDbStorageTotal().longValue());
    Assertions.assertEquals(213121L, entry1.getDbDataSizeTotal().longValue());
    Assertions.assertEquals(202001L, entry1.getDbDataSizeTotalWoSystem().longValue());
    Assertions.assertEquals(206121L, entry1.getDbIndexSizeTotal().longValue());
    Assertions.assertEquals(2, entry1.getTotalDatabases().intValue());
    Assertions.assertEquals(24, entry1.getTotalCollections().intValue());
    Assertions.assertEquals(32, entry1.getTotalViews().intValue());
    Assertions.assertEquals(36, entry1.getTotalIndexes().intValue());

    final List<DBStatsEntry> topEntries1 = entry1.getTopEntries();
    Assertions.assertEquals(
        7, topEntries1.size(), "Unexpected number of topEntries: " + topEntries1);

    Assertions.assertEquals(
        new DBStatsEntry("bar", 200102L),
        topEntries1.get(0),
        "Expected entries sorted by size descending: " + topEntries1);
    Assertions.assertEquals(
        new DBStatsEntry("foo", 200100L),
        topEntries1.get(1),
        "Expected entries sorted by size descending: " + topEntries1);
    Assertions.assertEquals(
        new DBStatsEntry("system", 2948L),
        topEntries1.get(2),
        "Expected entries sorted by size descending: " + topEntries1);
    Assertions.assertEquals(
        new DBStatsEntry("config", 2748L),
        topEntries1.get(3),
        "Expected entries sorted by size descending: " + topEntries1);
    Assertions.assertEquals(
        new DBStatsEntry("local", 2548L),
        topEntries1.get(4),
        "Expected entries sorted by size descending: " + topEntries1);
    Assertions.assertEquals(
        new DBStatsEntry("admin", 2348L),
        topEntries1.get(5),
        "Expected entries sorted by size descending: " + topEntries1);
    Assertions.assertEquals(
        new DBStatsEntry("*", 2148L),
        topEntries1.get(6),
        "Expected entries sorted by size descending: " + topEntries1);

    final TotalStorageCacheEntry entry2 = _dbStatsCacheDao.findById(GROUP_ID, HOST_ID2);
    Assertions.assertNotNull(entry2, "Failed to find TotalStorageCacheEntry for " + HOST_ID2);
    Assertions.assertEquals(106720L, entry2.getDbStorageTotal().longValue());
    Assertions.assertEquals(112120L, entry2.getDbDataSizeTotal().longValue());
    Assertions.assertEquals(101000L, entry2.getDbDataSizeTotalWoSystem().longValue());
    Assertions.assertEquals(106120L, entry2.getDbIndexSizeTotal().longValue());
    Assertions.assertEquals(1, entry2.getTotalDatabases().intValue());
    Assertions.assertEquals(12, entry2.getTotalCollections().intValue());
    Assertions.assertEquals(16, entry2.getTotalViews().intValue());
    Assertions.assertEquals(18, entry2.getTotalIndexes().intValue());

    final List<DBStatsEntry> topEntries2 = entry2.getTopEntries();
    Assertions.assertEquals(
        6, topEntries2.size(), "Unexpected number of topEntries: " + topEntries2);

    Assertions.assertEquals(
        new DBStatsEntry("baz", 200100L),
        topEntries2.get(0),
        "Expected entries sorted by size descending: " + topEntries2);
    Assertions.assertEquals(
        new DBStatsEntry("system", 2948L),
        topEntries2.get(1),
        "Expected entries sorted by size descending: " + topEntries2);
    Assertions.assertEquals(
        new DBStatsEntry("config", 2748L),
        topEntries2.get(2),
        "Expected entries sorted by size descending: " + topEntries2);
    Assertions.assertEquals(
        new DBStatsEntry("local", 2548L),
        topEntries2.get(3),
        "Expected entries sorted by size descending: " + topEntries2);
    Assertions.assertEquals(
        new DBStatsEntry("admin", 2348L),
        topEntries2.get(4),
        "Expected entries sorted by size descending: " + topEntries2);
    Assertions.assertEquals(
        new DBStatsEntry("*", 2148L),
        topEntries2.get(5),
        "Expected entries sorted by size descending: " + topEntries2);
  }

  @Test
  public void ingestDBStats_TopEntriesIsLimited() {
    final BasicBSONObject host1 = newHostPingBson(HOSTNAME, PORT1, "foo", "bar", "baz");
    final CanonicalMappings mappings =
        CanonicalHostUtils.initMappings(GROUP_ID, Collections.emptyList());
    final int maxTopEntries = 2;

    // Ingest with 3 user databases but a maxTopEntries of only 2
    _totalStorageCacheSvc.doDbStatsCalculations(
        GROUP_ID, mappings, new Date(), Collections.singletonList(host1), maxTopEntries);

    final TotalStorageCacheEntry entry = _dbStatsCacheDao.findById(GROUP_ID, HOST_ID1);
    Assertions.assertNotNull(entry, "Failed to find TotalStorageCacheEntry for " + HOST_ID1);
    Assertions.assertEquals(306923L, entry.getDbStorageTotal().longValue());
    Assertions.assertEquals(314123L, entry.getDbDataSizeTotal().longValue());
    Assertions.assertEquals(303003L, entry.getDbDataSizeTotalWoSystem().longValue());
    Assertions.assertEquals(306123L, entry.getDbIndexSizeTotal().longValue());
    Assertions.assertEquals(3, entry.getTotalDatabases().intValue());
    Assertions.assertEquals(36, entry.getTotalCollections().intValue());
    Assertions.assertEquals(48, entry.getTotalViews().intValue());
    Assertions.assertEquals(54, entry.getTotalIndexes().intValue());

    // Verify that the top 2 entries were stored.
    final List<DBStatsEntry> topEntries = entry.getTopEntries();
    Assertions.assertEquals(
        2, topEntries.size(), "Expected topEntries to be limited to 2: " + topEntries);

    Assertions.assertEquals(
        "baz",
        topEntries.get(0).getDatabaseName(),
        "Expected entries sorted by size descending: " + topEntries);
    Assertions.assertEquals(
        200104L,
        topEntries.get(0).getTotalSizeBytes(),
        "Expected entries sorted by size descending: " + topEntries);

    Assertions.assertEquals(
        "bar",
        topEntries.get(1).getDatabaseName(),
        "Expected entries sorted by size descending: " + topEntries);
    Assertions.assertEquals(
        200102L,
        topEntries.get(1).getTotalSizeBytes(),
        "Expected entries sorted by size descending: " + topEntries);
  }

  @Test
  public void ingestDBStats_HostStats() {
    _hostDao.create(HOSTNAME, PORT1, GROUP_ID, Collections.emptyMap());

    final BasicBSONObject host1 = newHostPingBson(HOSTNAME, PORT1, "foo");
    final CanonicalMappings mappings =
        CanonicalHostUtils.initMappings(GROUP_ID, Collections.emptyList());
    final int maxTopEntries = 2;

    // Ingest with 3 user databases but a maxTopEntries of only 2
    _totalStorageCacheSvc.doDbStatsCalculations(
        GROUP_ID, mappings, new Date(), Collections.singletonList(host1), maxTopEntries);

    final Host host = _hostDao.findById(HOST_ID1, GROUP_ID);
    Assertions.assertNotNull(host, "Failed to find host: " + HOST_ID1);

    Assertions.assertEquals(112120L, host.getLastDataSize().longValue());
    Assertions.assertEquals(101000L, host.getLastDataSizeWithoutLocal().longValue());
    Assertions.assertEquals(106120L, host.getLastIndexSize().longValue());
    Assertions.assertEquals(100000L, host.getLastIndexSizeWithoutLocal().longValue());
    Assertions.assertEquals(1, host.getNonSystemDbCount().intValue());
    Assertions.assertFalse(
        host.isDbStatsCacheStale(), "Expected Host's DB Stats stale field to be reset");
  }

  static BasicBSONObject newHostPingBson(
      final String pHostname, int pPort, final String... pDatabaseNames) {

    final BasicBSONObject databasesSection = newDatabasesSection();
    for (int i = 0; i < pDatabaseNames.length; i++) {
      final String databaseName = pDatabaseNames[i];
      databasesSection.put(databaseName, newDBStatsResponse(100_000L + i));
    }

    return new BasicBSONObject()
        .append(Ping.HOST.field, pHostname)
        .append(Ping.PORT.field, pPort)
        .append(Ping.DATABASES.field, databasesSection);
  }

  private static BasicBSONObject newDatabasesSection() {
    return new BasicBSONObject()
        .append("*", newDBStatsResponse(1024L))
        .append("admin", newDBStatsResponse(1124L))
        .append("local", newDBStatsResponse(1224L))
        .append("config", newDBStatsResponse(1324L))
        .append("system", newDBStatsResponse(1424L));
  }

  private static BasicBSONObject newDBStatsResponse(final long pSize) {
    final BasicBSONObject stats = new BasicBSONObject();
    stats.put("collections", 12L);
    stats.put("views", 16L);
    stats.put("objects", 12345L);
    stats.put("avgObjSize", 123.45);
    stats.put("dataSize", pSize + 1000);
    stats.put("storageSize", pSize + 100);
    stats.put("numExtents", 1234L);
    stats.put("indexes", 18L);
    stats.put("indexSize", pSize);
    stats.put("fileSize", 12345L);
    stats.put("fsUsedSize", 102345L);
    stats.put("fsTotalSize", 1002345L);
    return stats;
  }
}
