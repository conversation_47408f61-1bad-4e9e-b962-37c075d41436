package com.xgen.svc.mms.svc.billing.monitoring.impl;

import static com.xgen.svc.mms.svc.billing.monitoring.impl.LastBillDateMetricsAggregator.LAST_BILL_DATE_BY_SKU_COUNT_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LastBillDateMetricsAggregator.LAST_BILL_DATE_COUNT_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LastBillDateMetricsAggregator.NOW_MINUS_LAST_BILL_DATE_PROM_LABEL;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LastBillDateMetricsAggregator.SKU_PROM_LABEL;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.process.orchestration._public.model.lastbilldate.LastBillDate;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import io.prometheus.client.CollectorRegistry;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class LastBillDateBillingMetricsAggregatorIntTests extends JUnit5BaseSvcTest {
  @Inject private LastBillDateMetricsAggregator _aggregator;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "/mms/dao/billing/LastBillDateDao/lastBillDates.json.ftl",
        null,
        LastBillDate.DB_NAME,
        LastBillDate.COLLECTION_NAME);
  }

  @Test
  public void testEmitMetrics() {
    _aggregator.emitMetrics();
    verifyLastBillDateCountGauge(2, 3);
    verifyLastBillDateCountGauge(3, 4);
    verifyLastBillDateCountGauge(6, 4);
    verifyLastBillDateBySkuCountGauge(2, SKU.STITCH_DATA_DOWNLOADED, 2);
    verifyLastBillDateBySkuCountGauge(3, SKU.NDS_AWS_DATA_TRANSFER_INTERNET, 3);
  }

  void verifyLastBillDateCountGauge(int pNowMinusLastBillDate, long pExpectedCount) {
    double gaugeValue =
        CollectorRegistry.defaultRegistry.getSampleValue(
            LAST_BILL_DATE_COUNT_NAME,
            new String[] {NOW_MINUS_LAST_BILL_DATE_PROM_LABEL},
            new String[] {String.valueOf(pNowMinusLastBillDate)});
    assertEquals((double) pExpectedCount, gaugeValue, 0);
  }

  void verifyLastBillDateBySkuCountGauge(int pNowMinusLastBillDate, SKU pSku, long pExpectedCount) {
    double gaugeValue =
        CollectorRegistry.defaultRegistry.getSampleValue(
            LAST_BILL_DATE_BY_SKU_COUNT_NAME,
            new String[] {NOW_MINUS_LAST_BILL_DATE_PROM_LABEL, SKU_PROM_LABEL},
            new String[] {String.valueOf(pNowMinusLastBillDate), pSku.name()});
    assertEquals((double) pExpectedCount, gaugeValue, 0);
  }
}
