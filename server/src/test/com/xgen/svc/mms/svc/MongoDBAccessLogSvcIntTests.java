package com.xgen.svc.mms.svc;

import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.HOURS;
import static org.junit.Assert.assertEquals;

import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseResourceTest;
import com.xgen.svc.mms.model.performanceadvisor.MongoDBAccessLogEntry;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import org.junit.Before;
import org.junit.Test;

public class MongoDBAccessLogSvcIntTests extends BaseResourceTest {
  private static final Instant TIMESTAMP_BEFORE_FIRST_LOG_LINE =
      now().minus(2, DAYS); // must be less than 1mo ago (max Retention)

  private static final Date LOG_LINE1_TIMESTAMP =
      Date.from(TIMESTAMP_BEFORE_FIRST_LOG_LINE.plus(20, HOURS));
  private static final Date LOG_LINE2_TIMESTAMP =
      Date.from(TIMESTAMP_BEFORE_FIRST_LOG_LINE.plus(5, HOURS));
  private static final Date LOG_LINE3_TIMESTAMP =
      Date.from(TIMESTAMP_BEFORE_FIRST_LOG_LINE.minus(5, HOURS));
  private static final Date LOG_LINE4_TIMESTAMP =
      Date.from(TIMESTAMP_BEFORE_FIRST_LOG_LINE.minus(4, DAYS));

  private static final SimpleDateFormat TIMESTAMP_FORMAT =
      new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");

  private static final String LOG_LINE1_TIMESTAMP_STR =
      TIMESTAMP_FORMAT.format(LOG_LINE1_TIMESTAMP);
  private static final String LOG_LINE2_TIMESTAMP_STR =
      TIMESTAMP_FORMAT.format(LOG_LINE2_TIMESTAMP);
  private static final String LOG_LINE3_TIMESTAMP_STR =
      TIMESTAMP_FORMAT.format(LOG_LINE3_TIMESTAMP);
  private static final String LOG_LINE4_TIMESTAMP_STR =
      TIMESTAMP_FORMAT.format(LOG_LINE4_TIMESTAMP);

  private static final String SAMPLE_LOG_LINE1 =
      LOG_LINE1_TIMESTAMP_STR
          + " I ACCESS   [conn42] SASL SCRAM-SHA-1 authentication failed for bruce on admin from"
          + " client 127.0.0.1:56149 ; UserNotFound: Could not find user bruce@admin";
  private static final String SAMPLE_LOG_LINE2 =
      LOG_LINE2_TIMESTAMP_STR
          + " I ACCESS   [conn47] Successfully authenticated as principal bruce on admin from"
          + " client 127.0.0.1:56343";
  private static final String SAMPLE_LOG_LINE3 =
      LOG_LINE3_TIMESTAMP_STR
          + " I ACCESS   [conn47] Successfully authenticated as principal Alex on admin from"
          + " client 127.0.0.1:56343";
  private static final String SAMPLE_LOG_LINE4 =
      LOG_LINE4_TIMESTAMP_STR
          + " I ACCESS   [conn42] SASL SCRAM-SHA-1 authentication failed for bruce on admin from"
          + " client 127.0.0.1:56149 ; UserNotFound: Could not find user bruce@admin";

  private OrganizationDao _organizationDao;
  private MongoDBAccessLogSvc _mongoDbAccessLogsSvc;
  private final String AGENT_HOSTNAME = "myFreeCluster-shard-00-00-12345.mongodb.net";
  private final String CLUSTER_NAME = "myFreeCluster";

  private List<MongoDBAccessLogEntry> _entries;

  private Group _group;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _organizationDao = AppConfig.getInstance(OrganizationDao.class);
    _group = MmsFactory.createGroupWithStandardPlan();

    _mongoDbAccessLogsSvc = AppConfig.getInstance(MongoDBAccessLogSvc.class);

    final Organization org = _organizationDao.findById(_group.getOrgId());
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, org, FeatureFlag.MONGODB_ACCESS_HISTORY);

    final MongoDBAccessLogEntry entry1 =
        MongoDBAccessLogEntry.fromLogMessage(
            SAMPLE_LOG_LINE1,
            _group.getId(),
            CLUSTER_NAME,
            LOG_LINE1_TIMESTAMP,
            AGENT_HOSTNAME,
            UUID.randomUUID().toString(),
            LOG_LINE1_TIMESTAMP,
            2390);

    final MongoDBAccessLogEntry entry2 =
        MongoDBAccessLogEntry.fromLogMessage(
            SAMPLE_LOG_LINE2,
            _group.getId(),
            CLUSTER_NAME,
            LOG_LINE2_TIMESTAMP,
            AGENT_HOSTNAME,
            UUID.randomUUID().toString(),
            LOG_LINE2_TIMESTAMP,
            2390);

    final MongoDBAccessLogEntry entry3 =
        MongoDBAccessLogEntry.fromLogMessage(
            SAMPLE_LOG_LINE3,
            _group.getId(),
            CLUSTER_NAME,
            LOG_LINE3_TIMESTAMP,
            AGENT_HOSTNAME,
            UUID.randomUUID().toString(),
            LOG_LINE3_TIMESTAMP,
            2390);

    final MongoDBAccessLogEntry entry4 =
        MongoDBAccessLogEntry.fromLogMessage(
            SAMPLE_LOG_LINE4,
            _group.getId(),
            CLUSTER_NAME,
            LOG_LINE4_TIMESTAMP,
            AGENT_HOSTNAME,
            UUID.randomUUID().toString(),
            LOG_LINE4_TIMESTAMP,
            2390);

    _entries = new ArrayList<>(List.of(entry1, entry2, entry3, entry4));

    _mongoDbAccessLogsSvc.insert(_group.getId(), AGENT_HOSTNAME, _entries);

    TimeUnit.SECONDS.sleep(5);
  }

  @Test
  public void testFindEntriesByClusterNameOnPrem() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByClusterName(_group, CLUSTER_NAME);
    assertEquals(_entries, logEntries);
  }

  @Test
  public void testFindEntriesByClusterNameIA() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByClusterNameInIA(_group.getId().toString(), CLUSTER_NAME);
    assertEquals(_entries, logEntries);
  }

  @Test
  public void testFindEntriesByClusterNameSkipLimit() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByClusterName(_group, CLUSTER_NAME, 1, 2);

    assertEquals(logEntries, _entries.subList(1, _entries.size() - 1));
    assertEquals(2, logEntries.size());
  }

  @Test
  public void testFindEntriesByClusterNameSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByClusterNameSinceUntil(
            _group,
            CLUSTER_NAME,
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries, logEntries);
  }

  @Test
  public void testFindEntriesByHostnameSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByHostnameSinceUntil(
            _group,
            CLUSTER_NAME,
            AGENT_HOSTNAME,
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries, logEntries);
  }

  @Test
  public void testFindEntriesByHostnameAuthResultSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByHostnameAuthResultSinceUntil(
            _group,
            CLUSTER_NAME,
            AGENT_HOSTNAME,
            false,
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries.get(0), logEntries.get(0));
  }

  @Test
  public void testFindEntriesByAuthResultIpAddressSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByAuthResultIpAddressSinceUntil(
            _group,
            CLUSTER_NAME,
            false,
            "127.0.0.1",
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries.get(0), logEntries.get(0));
  }

  @Test
  public void testFindEntriesByAuthResultSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByAuthResultSinceUntil(
            _group,
            CLUSTER_NAME,
            false,
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries.get(0), logEntries.get(0));
  }

  @Test
  public void testFindEntriesByIpAddressSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByIpAddressSinceUntil(
            _group,
            CLUSTER_NAME,
            "127.0.0.1",
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries, logEntries);
  }

  @Test
  public void testFindEntriesByHostnameAuthResultIpAddressSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByHostnameAuthResultIpAddressSinceUntil(
            _group,
            CLUSTER_NAME,
            AGENT_HOSTNAME,
            false,
            "127.0.0.1",
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries.get(0), logEntries.get(0));
  }

  @Test
  public void testFindEntriesByHostnameIpAddressSinceUntil() throws SvcException {
    final List<MongoDBAccessLogEntry> logEntries =
        _mongoDbAccessLogsSvc.findEntriesByHostnameIpAddressSinceUntil(
            _group,
            CLUSTER_NAME,
            AGENT_HOSTNAME,
            "127.0.0.1",
            LOG_LINE4_TIMESTAMP.toInstant().minus(1, DAYS),
            LOG_LINE1_TIMESTAMP.toInstant().plus(1, DAYS),
            0,
            10);
    assertEquals(_entries.get(0), logEntries.get(0));
  }
}
