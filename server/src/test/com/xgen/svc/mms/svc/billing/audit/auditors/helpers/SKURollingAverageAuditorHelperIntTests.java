package com.xgen.svc.mms.svc.billing.audit.auditors.helpers;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.persistence.Persistence;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.LineItemSummary;
import com.xgen.svc.mms.model.billing.SKUAuditingLineItemMetrics;
import com.xgen.svc.mms.svc.billing.audit.auditors.SKURollingAverageAuditor.DateRange;
import com.xgen.svc.mms.svc.billing.audit.auditors.helpers.SKURollingAverageAuditorHelper.AggregatingField;
import com.xgen.svc.mms.svc.billing.reporting.billing.LineItemsTable.LineItemSummarySkuKey;
import com.xgen.svc.mms.svc.billing.utils.LineItemTestUtils;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.time.DateUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SKURollingAverageAuditorHelperIntTests extends JUnit5BaseSvcTest {
  @Inject SKURollingAverageAuditorHelper helper;
  @Inject Persistence persistence;
  Date inputStartDate = TimeUtils.fromISOString("2024-09-20T06:30:00");
  Date expectedStartDate = DateUtils.truncate(inputStartDate, Calendar.DATE);
  SKU expectedTieredSku = SKU.NDS_AWS_PIT_RESTORE_STORAGE;
  int lineItemCount = 2;
  int unitQuantity = 2;
  int unitTotalPriceCents = 3;
  int numOfTier = 2;
  int numOfUsageDays = 3;
  List<Date> allUsageDates =
      IntStream.range(0, numOfUsageDays)
          .mapToObj(i -> DateUtils.addDays(TimeUtils.fromISOString("2024-09-19"), i))
          .collect(Collectors.toList());

  @Test
  public void getCurrentSummaryAggregateByUsageDate_success() {
    // GIVEN
    List<LineItemSummary> expectedLineItemSummaries =
        LineItemTestUtils.createLineItemSummaries(
            expectedStartDate,
            allUsageDates,
            expectedTieredSku,
            numOfTier,
            lineItemCount,
            unitQuantity,
            unitTotalPriceCents);
    saveAll(expectedLineItemSummaries);
    Map<Date, Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics>> expectedResult =
        new HashMap<>();
    expectedLineItemSummaries.forEach(
        summary -> {
          expectedResult.computeIfAbsent(summary.getUsageDate(), k -> new HashMap<>());
          expectedResult
              .get(summary.getUsageDate())
              .put(
                  new LineItemSummarySkuKey(summary.getSku(), summary.getTier()),
                  new SKUAuditingLineItemMetrics.Builder(
                          summary.getSku(),
                          summary.getTier(),
                          summary.getQuantity(),
                          summary.getTotalPriceCents(),
                          summary.getLineItemCount())
                      .build());
        });

    // WHEN
    Map<Date, Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics>> result =
        helper.getCurrentSummaryAggregate(
            inputStartDate, expectedTieredSku, AggregatingField.USAGE_DATE);

    // THEN
    assertEquals(result.size(), numOfUsageDays);
    Assertions.assertThat(result).containsExactlyInAnyOrderEntriesOf(expectedResult);
  }

  @Test
  public void getCurrentSummaryAggregateByUsageDate_noSummary() {
    // WHEN
    Map<Date, Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics>> actualResult =
        helper.getCurrentSummaryAggregate(
            inputStartDate, expectedTieredSku, AggregatingField.USAGE_DATE);
    // THEN
    assertEquals(actualResult.size(), 0);
  }

  @Test
  public void getCurrentSummaryAggregateByBillDate_success() {
    // GIVEN
    List<LineItemSummary> expectedLineItemSummaries =
        LineItemTestUtils.createLineItemSummaries(
            expectedStartDate,
            allUsageDates,
            expectedTieredSku,
            numOfTier,
            lineItemCount,
            unitQuantity,
            unitTotalPriceCents);
    saveAll(expectedLineItemSummaries);
    Map<Date, Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics>> expectedResult =
        new HashMap<>();
    expectedLineItemSummaries.forEach(
        summary -> {
          expectedResult.computeIfAbsent(summary.getStartDate(), k -> new HashMap<>());
          LineItemSummarySkuKey skuKey =
              new LineItemSummarySkuKey(summary.getSku(), summary.getTier());
          expectedResult
              .get(summary.getStartDate())
              .computeIfAbsent(
                  skuKey,
                  k ->
                      new SKUAuditingLineItemMetrics.Builder(
                              summary.getSku(), summary.getTier(), 0, 0, 0)
                          .build());
          expectedResult
              .get(summary.getStartDate())
              .get(skuKey)
              .sumCountQuantityAndPriceFromLineItemSummary(summary);
        });

    // WHEN
    Map<Date, Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics>> result =
        helper.getCurrentSummaryAggregate(
            inputStartDate, expectedTieredSku, AggregatingField.BILL_DATE);

    // THEN
    assertEquals(result.size(), 1);
    Assertions.assertThat(result).containsExactlyInAnyOrderEntriesOf(expectedResult);
  }

  @Test
  public void getRollingMeanSummaryAggregateByUsageDate_success() {
    // GIVEN
    int bufferedDay = 1;
    int queriedRange = 2;
    int numOfUsageDays = 4;
    Date startUsageDate = DateUtils.addDays(expectedStartDate, -1);
    List<Date> usageDates =
        IntStream.range(0, numOfUsageDays)
            .mapToObj(i -> DateUtils.addDays(startUsageDate, i))
            .collect(Collectors.toList());
    DateRange queryRange =
        new DateRange(expectedStartDate, DateUtils.addDays(expectedStartDate, queriedRange));
    Date currTime = DateUtils.addDays(queryRange.endDateExcl(), bufferedDay + 1);
    Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics> expectedResult = new HashMap<>();
    saveLineItemSummarySvcAndCreateLineItemMetricsMap(
        queriedRange + bufferedDay,
        queryRange,
        expectedStartDate,
        usageDates,
        expectedTieredSku,
        numOfTier,
        lineItemCount,
        unitQuantity,
        AggregatingField.USAGE_DATE,
        expectedResult);
    expectedResult
        .keySet()
        .forEach(
            skuKey -> {
              SKUAuditingLineItemMetrics cur = expectedResult.get(skuKey);
              expectedResult.put(
                  skuKey,
                  new SKUAuditingLineItemMetrics.Builder(
                          cur.getSku(),
                          cur.getTier(),
                          cur.getQuantity() / queriedRange,
                          cur.getTotalPriceCents() / queriedRange,
                          cur.getLineItemCount() / queriedRange)
                      .build());
            });
    // WHEN
    Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics> result =
        helper.getRollingMeanSummary(
            queryRange, expectedTieredSku, bufferedDay, currTime, AggregatingField.USAGE_DATE);
    // Then
    assertEquals(result.size(), numOfTier);
    Assertions.assertThat(result).containsExactlyInAnyOrderEntriesOf(expectedResult);
  }

  @Test
  public void getRollingMeanSummaryAggregateByBillDate_success() {
    // GIVEN
    int bufferedDay = 1;
    int numOfBillDates = 5;
    int numOfUsageDays = 4;
    Date startUsageDate = DateUtils.addDays(expectedStartDate, -1);
    List<Date> usageDates =
        IntStream.range(0, numOfUsageDays)
            .mapToObj(i -> DateUtils.addDays(startUsageDate, i))
            .collect(Collectors.toList());
    DateRange queryRange =
        new DateRange(expectedStartDate, DateUtils.addDays(expectedStartDate, numOfBillDates));
    Date currTime = DateUtils.addDays(queryRange.endDateExcl(), bufferedDay + 1);
    Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics> expectedResult = new HashMap<>();
    saveLineItemSummarySvcAndCreateLineItemMetricsMap(
        numOfBillDates + bufferedDay,
        queryRange,
        expectedStartDate,
        usageDates,
        expectedTieredSku,
        numOfTier,
        lineItemCount,
        unitQuantity,
        AggregatingField.BILL_DATE,
        expectedResult);
    expectedResult
        .keySet()
        .forEach(
            skuKey -> {
              SKUAuditingLineItemMetrics cur = expectedResult.get(skuKey);
              expectedResult.put(
                  skuKey,
                  new SKUAuditingLineItemMetrics.Builder(
                          cur.getSku(),
                          cur.getTier(),
                          cur.getQuantity() / numOfBillDates,
                          cur.getTotalPriceCents() / numOfBillDates,
                          cur.getLineItemCount() / numOfBillDates)
                      .build());
            });
    // WHEN
    Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics> result =
        helper.getRollingMeanSummary(
            queryRange, expectedTieredSku, bufferedDay, currTime, AggregatingField.BILL_DATE);
    // Then
    assertEquals(result.size(), numOfTier);
    Assertions.assertThat(result).containsExactlyInAnyOrderEntriesOf(expectedResult);
  }

  private void saveLineItemSummarySvcAndCreateLineItemMetricsMap(
      int numOfDays,
      DateRange queryRange,
      Date startDate,
      List<Date> usageDates,
      SKU sku,
      int numOfTier,
      int lineItemCount,
      int unitQuantity,
      AggregatingField aggregatingField,
      Map<LineItemSummarySkuKey, SKUAuditingLineItemMetrics> expectedResult) {
    IntStream.range(0, numOfDays)
        .forEach(
            i -> {
              List<LineItemSummary> summaries =
                  LineItemTestUtils.createLineItemSummaries(
                      DateUtils.addDays(startDate, i),
                      usageDates,
                      sku,
                      numOfTier,
                      lineItemCount,
                      unitQuantity,
                      unitTotalPriceCents);
              saveAll(summaries);
              summaries.stream()
                  .filter(
                      lineItemSummary -> {
                        if (aggregatingField == AggregatingField.USAGE_DATE) {
                          return !lineItemSummary.getUsageDate().before(queryRange.fromDateIncl())
                              && lineItemSummary.getUsageDate().before(queryRange.endDateExcl());
                        } else {
                          return !lineItemSummary.getStartDate().before(queryRange.fromDateIncl())
                              && lineItemSummary.getStartDate().before(queryRange.endDateExcl());
                        }
                      })
                  .forEach(
                      lineItemSummary -> {
                        LineItemSummarySkuKey skuKey =
                            new LineItemSummarySkuKey(
                                lineItemSummary.getSku(), lineItemSummary.getTier());
                        expectedResult.computeIfAbsent(
                            skuKey,
                            k ->
                                new SKUAuditingLineItemMetrics.Builder(
                                        lineItemSummary.getSku(),
                                        lineItemSummary.getTier(),
                                        0,
                                        0,
                                        0)
                                    .build());
                        expectedResult
                            .get(skuKey)
                            .sumCountQuantityAndPriceFromLineItemSummary(lineItemSummary);
                      });
            });
  }
}
