package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.billingplatform.support._public.model.AtlasEntitlementsCostEstimate;
import com.xgen.svc.core.FixtureUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.SelfServeProductDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import jakarta.inject.Inject;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AtlasEntitlementsEstimateSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private AtlasEntitlementsEstimateSvc atlasEntitlementsEstimateSvc;
  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;
  @Inject private SelfServeProductDao selfServeProductDao;

  private static final String EXPECTED_JSON =
      "mms/billing/support/atlas_entitlements_estimate.json";

  private static final String EXPECTED_JSON_GOV =
      "mms/billing/support/atlas_entitlements_estimate_gov.json";

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    salesforceProductCodeDao.syncDocuments();
    selfServeProductDao.syncDocuments();
  }

  @Test
  public void estimateEntitlements() throws JsonProcessingException {
    doTest(EXPECTED_JSON, false, 6);
  }

  @Test
  public void estimateEntitlements_gov() throws JsonProcessingException {
    doTest(EXPECTED_JSON_GOV, true, 2);
  }

  private void doTest(String fileName, boolean isGov, int expectedEntryCount)
      throws JsonProcessingException {
    List<Long> costs = List.of(1_000_00L, 100_000_00L);
    List<List<AtlasEntitlementsCostEstimate>> result =
        atlasEntitlementsEstimateSvc.estimateEntitlements(List.of(1_000_00L, 100_000_00L), isGov);

    ObjectMapper mapper = new ObjectMapper();
    String json = mapper.writeValueAsString(result);

    String expectedJson =
        FixtureUtils.getStringOfFtlTemplate(fileName, null).replace("\n", "").replace(" ", "");

    assertEquals(
        expectedEntryCount * costs.size(),
        StringUtils.countMatches(expectedJson, "subscriptionType"));
    assertEquals(expectedJson, json);
  }
}
