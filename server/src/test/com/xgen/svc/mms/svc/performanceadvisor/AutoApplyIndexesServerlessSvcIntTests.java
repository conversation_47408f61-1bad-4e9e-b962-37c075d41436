package com.xgen.svc.mms.svc.performanceadvisor;

import static com.mongodb.assertions.Assertions.fail;
import static com.xgen.cloud.common.util._public.util.BaseHostUtils.extractHostname;
import static com.xgen.cloud.common.util._public.util.DriverUtils.reverseObjectId;
import static com.xgen.svc.mms.model.explorer.JobSource.SERVERLESS_AUTO_INDEXING;
import static com.xgen.svc.mms.util.SlowQueryTestLogs.LOG_LINES_STRUCTURED_EXTENDED;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.explorer._public.model.DataExplorerOpType;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._private.dao.AgentJobsProcessorDao;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob.Command.Type;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob.Status;
import com.xgen.cloud.common.jobqueue._public.model.BaseJob;
import com.xgen.cloud.common.jobqueue._public.svc.AgentJobsProcessorSvc;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.activity._public.event.AutoIndexingEvent;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessAutoIndexingAudit;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.explorer.jobs.GetDataExplorerOpJob;
import com.xgen.svc.mms.dao.explorer.DataExplorerResponseDao;
import com.xgen.svc.mms.dao.performanceadvisor.autoindexing.InProgressIndexesJobCacheDao;
import com.xgen.svc.mms.model.performanceadvisor.ExcludedIndex;
import com.xgen.svc.mms.model.performanceadvisor.IndexKeyPattern;
import com.xgen.svc.mms.model.performanceadvisor.SlowQueryLogEntry;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.AutoIndexingIndexStatus;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.AutoIndexingState;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.InProgressIndexesJobCache;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.ServerlessAutoIndexingIndex;
import com.xgen.svc.mms.model.performanceadvisor.profiler.SuggestedIndexField.SortDirection;
import com.xgen.svc.mms.svc.ServerlessAutoIndexingSvc;
import com.xgen.svc.mms.svc.SlowQueryLogSvc;
import com.xgen.svc.mms.svc.explorer.DataExplorerSvc;
import com.xgen.svc.mms.svc.explorer.IndexBuildNotificationSvc;
import com.xgen.svc.mms.svc.host.LegacyHostSvc;
import com.xgen.svc.mms.util.PerfomanceAdvisorResourceIntTestHelper.AutomationJobMockResponseRunner;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.serverless.svc.NDSServerlessTestUtils;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

public class AutoApplyIndexesServerlessSvcIntTests extends JUnit5BaseSvcTest {
  private static final String CLUSTER_NAME = "myCluster";

  @Inject private HostSvc _hostSvc;
  @Inject private AppSettings _appSettings;
  @Inject private AutoApplyIndexesServerlessSvc _autoApplyIndexesServerlessSvc;
  @Inject private InProgressIndexesJobCacheDao _inProgressIndexesJobCacheDao;
  @Inject private DataExplorerResponseDao _dataExplorerResponseDao;
  @Inject private AgentJobsProcessorDao _agentJobsProcessorDao;
  @Inject private AgentJobsProcessorSvc _agentJobsProcessorSvc;
  @Inject private DataExplorerSvc _dataExplorerSvc;
  @Inject private HostClusterLifecycleSvc _hostClusterLifecycleSvc;
  @Inject private ServerlessAutoIndexingSvc _serverlessAutoIndexingSvc;
  @Inject private CreateIndexSuggestionsSvc _createIndexSuggestionsSvc;
  @Inject private SegmentEventSvc _segmentEventSvc;
  @Inject private NDSLookupSvc _ndsLookupSvc;
  @Inject private IndexBuildNotificationSvc _indexBuildNotificationSvc;
  @Inject private SlowQueryLogSvc _slowQueryLogSvc;
  @Inject private NDSServerlessTestUtils _ndsServerlessTestUtils;
  @Inject private LegacyHostSvc _legacyHostSvc;
  @Inject private AuditSvc _auditSvc;
  private Organization _org;
  private Group _group;
  private Group _mtmGroup;
  private HostCluster _hostCluster;
  private Host _host;
  private ClusterDescription _clusterDescription;
  private ClusterDescription _mtmClusterDescription;
  private ReplicaSetHardware _tenantHardware;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _org = MmsFactory.createOrganizationWithPremiumPlan();
    _mtmGroup = MmsFactory.createGroup(_org);
    _group = MmsFactory.createGroup(_org);
    _autoApplyIndexesServerlessSvc =
        new AutoApplyIndexesServerlessSvc(
            _appSettings,
            _inProgressIndexesJobCacheDao,
            _dataExplorerSvc,
            _hostClusterLifecycleSvc,
            _serverlessAutoIndexingSvc,
            _createIndexSuggestionsSvc,
            _segmentEventSvc,
            _ndsLookupSvc,
            _indexBuildNotificationSvc,
            _agentJobsProcessorSvc);
    _mtmClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_mtmGroup.getId(), CLUSTER_NAME));
    _clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getDefaultServerlessClusterDescription(
                _group.getId(), "serverless"));
    _ndsServerlessTestUtils.createServerlessTenantAndMTMCluster(
        _mtmClusterDescription, List.of(_clusterDescription), null, null);
    _ndsServerlessTestUtils.createHardwareAndHostsFromClusterDescriptionHostnames(
        _mtmClusterDescription);
    _ndsServerlessTestUtils.createHardwareFromClusterDescriptionHostnames(_clusterDescription);

    _hostCluster =
        _hostClusterLifecycleSvc.findHostClustersByGroupId(_mtmGroup.getId(), true).get(0);
    _host = _hostCluster.getPrimaryHosts().get(0);
    MmsFactory.createAutomationAgentAuditEntry(_mtmGroup, "hostname", "5.6.0.5512", new Date());

    _tenantHardware =
        _legacyHostSvc.findReplicaSetHardwareForHostname(
            _clusterDescription.getFirstMongoDBUriHost(), _clusterDescription.getGroupId());
  }

  @Test
  void testInProgressIndexBuildsValidCacheNoBuilds() throws Exception {
    final var cachedInProgressIndex = new InProgressIndexesJobCache();
    cachedInProgressIndex.setNumIndexes(0);
    cachedInProgressIndex.setRcid(reverseObjectId(_mtmGroup.getId()));
    cachedInProgressIndex.setNdsClusterName(CLUSTER_NAME);
    cachedInProgressIndex.setCompleted(true);
    _inProgressIndexesJobCacheDao.insert(cachedInProgressIndex);

    Assertions.assertTrue(
        _autoApplyIndexesServerlessSvc.checkInProgressIndexBuilds(
            _org, _mtmGroup, _hostCluster.getClusterId(), _mtmClusterDescription),
        "allow if cached job is complete and has no builds");
  }

  @Test
  void testInProgressIndexBuildsValidCacheTooManyBuilds() throws Exception {
    final var cacheEntry = new InProgressIndexesJobCache();
    cacheEntry.setNumIndexes(3);
    cacheEntry.setRcid(reverseObjectId(_mtmGroup.getId()));
    cacheEntry.setNdsClusterName(CLUSTER_NAME);
    cacheEntry.setCompleted(true);

    _inProgressIndexesJobCacheDao.insert(cacheEntry);

    Assertions.assertFalse(
        _autoApplyIndexesServerlessSvc.checkInProgressIndexBuilds(
            _org, _mtmGroup, _hostCluster.getClusterId(), _mtmClusterDescription),
        "do not allow if cached job has too many builds in progress");
  }

  @Test
  void testInProgressIndexBuildsSubmitsIndexJobWhenCacheEmpty() {
    Assertions.assertTrue(
        _inProgressIndexesJobCacheDao
            .findByGroupIdAndClusterName(_mtmGroup.getId(), CLUSTER_NAME)
            .isEmpty(),
        "cache starts empty");

    final var indexStatsJobRunner =
        new AutomationJobMockResponseRunner(
            _dataExplorerResponseDao,
            _agentJobsProcessorDao,
            _host.getId(),
            Map.of(),
            Map.of(),
            Collections.emptyMap(),
            Collections.emptyList(),
            false,
            null,
            Set.of());
    indexStatsJobRunner.start();
    Assertions.assertTrue(
        _autoApplyIndexesServerlessSvc.checkInProgressIndexBuilds(
            _org, _mtmGroup, _hostCluster.getClusterId(), _mtmClusterDescription),
        "allow if cache is empty, and index build job returns no builds");
    indexStatsJobRunner.stopThread();

    final var cache =
        _inProgressIndexesJobCacheDao
            .findByGroupIdAndClusterName(_mtmGroup.getId(), CLUSTER_NAME)
            .get();
    Assertions.assertEquals(
        cache.getNumIndexes(), Integer.valueOf(0), "cache has correct numIndexes");
    Assertions.assertTrue(cache.getCompleted(), "cache marked completed");
    Assertions.assertFalse(cache.getFailed(), "cache not marked failed");

    Assertions.assertTrue(
        _autoApplyIndexesServerlessSvc.checkInProgressIndexBuilds(
            _org, _mtmGroup, _hostCluster.getClusterId(), _mtmClusterDescription),
        "reads cached result if immediately checking again");
  }

  @Test
  void testInProgressIndexBuildsSucceedsWhenIncompleteButFinishesLater() throws Exception {
    final var cacheEntry = new InProgressIndexesJobCache();
    cacheEntry.setNumIndexes(3);
    cacheEntry.setRcid(reverseObjectId(_mtmGroup.getId()));
    cacheEntry.setNdsClusterName(CLUSTER_NAME);
    cacheEntry.setCompleted(false);
    cacheEntry.setFailed(false);
    _inProgressIndexesJobCacheDao.insert(cacheEntry);
    new java.util.Timer()
        .schedule(
            new java.util.TimerTask() {
              @Override
              public void run() {
                _inProgressIndexesJobCacheDao.setCompleted(_mtmGroup.getId(), CLUSTER_NAME, 0);
              }
            },
            7000);

    Assertions.assertTrue(
        _autoApplyIndexesServerlessSvc.checkInProgressIndexBuilds(
            _org, _mtmGroup, _hostCluster.getClusterId(), _mtmClusterDescription),
        "allow succeed if cache job incomplete, but completes 7 seconds later");
  }

  @Test
  void testInProgressIndexBuildsFailsWhenJobIncompleteAndDoesNotFinish() throws Exception {
    final var cacheEntry = new InProgressIndexesJobCache();
    cacheEntry.setNumIndexes(3);
    cacheEntry.setRcid(reverseObjectId(_mtmGroup.getId()));
    cacheEntry.setNdsClusterName(CLUSTER_NAME);
    cacheEntry.setCompleted(false);
    cacheEntry.setFailed(false);
    _inProgressIndexesJobCacheDao.insert(cacheEntry);

    Assertions.assertFalse(
        _autoApplyIndexesServerlessSvc.checkInProgressIndexBuilds(
            _org, _mtmGroup, _hostCluster.getClusterId(), _mtmClusterDescription),
        "fail if cache job incomplete, and doesn't complete within 15 seconds");
  }

  @Test
  void testInProgressAutoIndexingJobsFailsIfJobInProgress() {
    var agentJob = makeDataExplorerCreateIndexJob();
    agentJob.updateStatus(Status.NEW, Optional.empty());
    _agentJobsProcessorSvc.putJob(agentJob, false);

    var autoIndexJob = makeAutoIndexJob(agentJob.getId());
    _serverlessAutoIndexingSvc.insertServerlessAutoIndexingIndex(autoIndexJob);

    Assertions.assertFalse(
        _autoApplyIndexesServerlessSvc.checkInProgressAutoIndexingJobs(
            _mtmGroup.getId(),
            _mtmClusterDescription.getName(),
            _clusterDescription.getUniqueId(),
            _clusterDescription.getGroupId()),
        "fails if auto-indexing job in progress, agent job status != complete");
  }

  @Test
  void testInProgressAutoIndexingJobsSucceedsIfJobComplete() {
    var agentJob = makeDataExplorerCreateIndexJob();
    agentJob.updateStatus(Status.COMPLETED, Optional.empty());
    _agentJobsProcessorSvc.putJob(agentJob, false);

    var autoIndexJob = makeAutoIndexJob(agentJob.getId());
    _serverlessAutoIndexingSvc.insertServerlessAutoIndexingIndex(autoIndexJob);

    Assertions.assertTrue(
        _autoApplyIndexesServerlessSvc.checkInProgressAutoIndexingJobs(
            _mtmGroup.getId(),
            _mtmClusterDescription.getName(),
            _clusterDescription.getUniqueId(),
            _clusterDescription.getGroupId()),
        "succeeds if auto-indexing job in progress, agent job status = complete");
  }

  @Disabled("Temporarily disabled due to flakiness, see CLOUDP-318294")
  @Test
  void testHandleServerlessAutoIndexing() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        _group.getId(), _clusterDescription.getUniqueId(), AutoIndexingState.ENABLED);

    _appSettings.setProp(
        "mms.monitoring.performanceadvisor.serverless.minlogspershape",
        "2",
        AppSettings.SettingType.MEMORY);

    _appSettings.setProp(
        "mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed",
        "100",
        AppSettings.SettingType.MEMORY);

    setUpServerlessSlowLogs();

    try {
      _autoApplyIndexesServerlessSvc.handleServerlessAutoIndexing(
          _clusterDescription.getUniqueId(),
          _group,
          _org,
          List.of(),
          _mtmGroup.getId(),
          _mtmClusterDescription.getName());
    } catch (final Exception pE) {
      fail();
    }

    final var jobs = _agentJobsProcessorDao.findAllAgentJobs();
    Assertions.assertEquals(2, jobs.size());
    jobs.sort(Comparator.comparingLong(BaseJob::getCreated));

    assertAgentJob(jobs.get(0), "listIndexStats", false);
    assertAgentJob(jobs.get(1), "createIndex", true);

    final var autoIndexingIndexes =
        _serverlessAutoIndexingSvc.findServerlessAutoIndexingIndexesByClusterUniqueIdNamespaces(
            _clusterDescription.getUniqueId(), List.of("test.foo"));
    Assertions.assertEquals(1, autoIndexingIndexes.size());

    final var autoIndexingIndex = autoIndexingIndexes.get(0);
    Assertions.assertEquals(_group.getId(), autoIndexingIndex.getProjectId());
    Assertions.assertEquals(
        _clusterDescription.getUniqueId(), autoIndexingIndex.getClusterUniqueId());
    Assertions.assertEquals(
        jobs.get(1).getId(), autoIndexingIndex.getAgentJobId()); // createIndex job id
    Assertions.assertEquals("test.foo", autoIndexingIndex.getNamespace());
    Assertions.assertEquals("b_1_autocreated", autoIndexingIndex.getIndexName());
    Assertions.assertEquals(
        List.of(new IndexKeyPattern("b", SortDirection.ASCENDING)),
        autoIndexingIndex.getKeyPattern());
    Assertions.assertEquals(AutoIndexingIndexStatus.IN_PROGRESS, autoIndexingIndex.getStatus());
    Assertions.assertEquals(
        _mtmClusterDescription.getName(), autoIndexingIndex.getMtmClusterName());
    Assertions.assertEquals(
        _mtmClusterDescription.getGroupId(), autoIndexingIndex.getMtmProjectId());
    Assertions.assertNull(autoIndexingIndex.getCompleted());

    List<Event> events =
        _auditSvc.findByEventTypeMostRecentFirst(
            AutoIndexingEvent.Type.AUTO_INDEXING_STARTED_INDEX_BUILD);
    Assertions.assertEquals(1, events.size());
    final ServerlessAutoIndexingAudit serverlessAutoIndexingAudit =
        (ServerlessAutoIndexingAudit) events.get(0);

    Assertions.assertEquals(_group.getId(), serverlessAutoIndexingAudit.getGroupId());
    Assertions.assertEquals(
        _clusterDescription.getName(), serverlessAutoIndexingAudit.getInstanceName());
    Assertions.assertEquals("test", serverlessAutoIndexingAudit.getDb());
    Assertions.assertEquals("foo", serverlessAutoIndexingAudit.getCollection());
  }

  @Test
  void testHandleServerlessAutoIndexing_disabled() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _org, FeatureFlag.SERVERLESS_AUTO_INDEXING);
    _serverlessAutoIndexingSvc.createConfigForCluster(
        _group.getId(), _clusterDescription.getUniqueId(), AutoIndexingState.DISABLED);

    _appSettings.setProp(
        "mms.monitoring.performanceadvisor.serverless.minlogspershape",
        "2",
        AppSettings.SettingType.MEMORY);

    _appSettings.setProp(
        "mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed",
        "100",
        AppSettings.SettingType.MEMORY);

    setUpServerlessSlowLogs();

    try {
      _autoApplyIndexesServerlessSvc.handleServerlessAutoIndexing(
          _clusterDescription.getUniqueId(),
          _group,
          _org,
          List.of(),
          _mtmGroup.getId(),
          _mtmClusterDescription.getName());
    } catch (final Exception pE) {
      fail();
    }

    // no agent job, auto index or audit event
    final var jobs = _agentJobsProcessorDao.findAllAgentJobs();
    Assertions.assertEquals(0, jobs.size());

    final var autoIndexingIndexes =
        _serverlessAutoIndexingSvc.findServerlessAutoIndexingIndexesByClusterUniqueIdNamespaces(
            _clusterDescription.getUniqueId(), List.of("test.foo"));
    Assertions.assertEquals(0, autoIndexingIndexes.size());

    List<Event> events =
        _auditSvc.findByEventTypeMostRecentFirst(
            AutoIndexingEvent.Type.AUTO_INDEXING_STARTED_INDEX_BUILD);
    Assertions.assertEquals(0, events.size());
  }

  @Test
  void testHandleServerlessAutoIndexing_excludedIndex() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _org, FeatureFlag.SERVERLESS_AUTO_INDEXING);

    final ExcludedIndex excludedIndex1 =
        new ExcludedIndex(
            "test.foo", "b_1", List.of((new IndexKeyPattern("b", SortDirection.ANY))));
    final ExcludedIndex excludedIndex2 =
        new ExcludedIndex(
            "test.foo", "a_1", List.of((new IndexKeyPattern("a", SortDirection.ANY))));
    _serverlessAutoIndexingSvc.createConfigForCluster(
        _group.getId(),
        _clusterDescription.getUniqueId(),
        List.of(excludedIndex1, excludedIndex2),
        AutoIndexingState.ENABLED);

    _appSettings.setProp(
        "mms.monitoring.performanceadvisor.serverless.minlogspershape",
        "2",
        AppSettings.SettingType.MEMORY);

    setUpServerlessSlowLogs();

    try {
      _autoApplyIndexesServerlessSvc.handleServerlessAutoIndexing(
          _clusterDescription.getUniqueId(),
          _group,
          _org,
          List.of(),
          _mtmGroup.getId(),
          _mtmClusterDescription.getName());
    } catch (final Exception pE) {
      fail();
    }

    final var jobs = _agentJobsProcessorDao.findAllAgentJobs();
    Assertions.assertEquals(0, jobs.size());

    final var autoIndexingIndexes =
        _serverlessAutoIndexingSvc.findServerlessAutoIndexingIndexesByClusterUniqueIdNamespaces(
            _clusterDescription.getUniqueId(), List.of("test.foo"));
    Assertions.assertEquals(0, autoIndexingIndexes.size());

    List<Event> events =
        _auditSvc.findByEventTypeMostRecentFirst(
            AutoIndexingEvent.Type.AUTO_INDEXING_STARTED_INDEX_BUILD);
    Assertions.assertEquals(0, events.size());
  }

  private void setUpServerlessSlowLogs() throws Exception {
    // insert tenant specific slow queries
    final long skew = 2;
    final long now = System.currentTimeMillis();
    final List<SlowQueryLogEntry> entries = new ArrayList<>();
    for (String logLine : LOG_LINES_STRUCTURED_EXTENDED) {
      logLine =
          logLine.replaceAll("collection_placeholder", _clusterDescription.getUniqueId() + "_test");
      final SlowQueryLogEntry entry =
          SlowQueryLogEntry.fromLogMessage(
              logLine,
              new Date(now),
              new Date(now - skew),
              1,
              _group.getId(),
              _host.getFullHostname(),
              _host.getPort(),
              "5.0.0",
              null,
              true,
              "SERVERLESS");
      entries.add(entry);
    }

    // add first shape with low impact twice to hit log minimum
    final SlowQueryLogEntry entryLowImpact =
        SlowQueryLogEntry.fromLogMessage(
            LOG_LINES_STRUCTURED_EXTENDED
                .get(0)
                .replaceAll("collection_placeholder", _clusterDescription.getUniqueId() + "_test"),
            new Date(now),
            new Date(now - skew),
            1,
            _group.getId(),
            _host.getFullHostname(),
            _host.getPort(),
            "5.0.0",
            null,
            true,
            "SERVERLESS");
    entries.add(entryLowImpact);

    // add second shape with high impact twice to hit log minimum
    final SlowQueryLogEntry entryHighImpact =
        SlowQueryLogEntry.fromLogMessage(
            LOG_LINES_STRUCTURED_EXTENDED
                .get(1)
                .replaceAll("collection_placeholder", _clusterDescription.getUniqueId() + "_test"),
            new Date(now),
            new Date(now - skew),
            1,
            _group.getId(),
            _host.getFullHostname(),
            _host.getPort(),
            "5.0.0",
            null,
            true,
            "SERVERLESS");
    entries.add(entryHighImpact);

    _slowQueryLogSvc.insertNonMTMHoldingGroup(_group.getId(), _host.getFullHostname(), entries);
    Thread.sleep(5_000);
  }

  private void assertAgentJob(
      final AgentJob pJob, final String pOpType, final Boolean pWasAutoCreated) {
    Assertions.assertEquals(_mtmGroup.getId(), pJob.getGroupId());
    Assertions.assertEquals(_host.getName(), pJob.getHostname());
    Assertions.assertEquals(Type.DataExplorerOp, pJob.getCommand());

    final BasicDBObject params = pJob.getParameters();
    Assertions.assertEquals(_host.getName(), params.getString("hostname"));
    Assertions.assertEquals(_host.getPort().intValue(), params.getInt("port"));
    Assertions.assertEquals(
        extractHostname(
            _tenantHardware
                .getAllHardware()
                .findFirst()
                .orElseThrow()
                .getHostnameForAgents()
                .get()),
        params.getString("sniHostname"));
    Assertions.assertEquals(NDSDefaults.MONGOD_PUBLIC_PORT, params.getInt("sniPort"));
    Assertions.assertEquals(_group.getId(), params.getObjectId("sniGroupId"));
    Assertions.assertEquals(pWasAutoCreated, params.getBoolean("wasAutoCreated"));

    final BasicDBObject op = (BasicDBObject) pJob.getParameters().get("op");
    Assertions.assertEquals(pOpType, op.get("type"));
    Assertions.assertEquals(SERVERLESS_AUTO_INDEXING.name(), op.get("source"));
  }

  private AgentJob makeDataExplorerCreateIndexJob() {
    return GetDataExplorerOpJob.create(
        new ObjectId(),
        _group.getId(),
        new ObjectId(),
        "host",
        new BasicDBObject(
            "op", new BasicDBObject("type", DataExplorerOpType.CREATE_INDEX.getName())),
        null);
  }

  private ServerlessAutoIndexingIndex makeAutoIndexJob(final ObjectId pAgentJobId) {
    final List<IndexKeyPattern> keyPattern =
        List.of(new IndexKeyPattern("indexField", SortDirection.ANY));
    return new ServerlessAutoIndexingIndex(
        _group.getId(),
        _clusterDescription.getUniqueId(),
        pAgentJobId,
        new Date(),
        "namespace",
        "indexName",
        keyPattern,
        AutoIndexingIndexStatus.IN_PROGRESS,
        new Date(),
        new Date(),
        Boolean.FALSE,
        "",
        _mtmClusterDescription.getGroupId(),
        _mtmClusterDescription.getName(),
        0.0,
        new Date(),
        null);
  }
}
