package com.xgen.svc.mms.svc.billing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.jobqueue._private.dao.BatchJobDao;
import com.xgen.cloud.common.jobqueue._public.model.BaseJob;
import com.xgen.cloud.common.jobqueue._public.model.BatchJob;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.billing._private.dao.SubscriptionUsageBatchStatusDao;
import com.xgen.cloud.nds.billing._public.model.NDSInstanceUsage;
import com.xgen.cloud.nds.billing._public.model.NDSStorageUsage;
import com.xgen.cloud.nds.billing._public.model.SubscriptionUsage;
import com.xgen.cloud.nds.billing._public.model.SubscriptionUsageBatchStatus;
import com.xgen.cloud.nds.billing._public.model.SubscriptionUsageBatchStatus.Status;
import com.xgen.cloud.nds.billing._public.svc.SubscriptionUsageBatchStatusHandler;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SubscriptionUsageSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private GroupDao _groupDao;
  @Inject private BatchJobDao _batchJobDao;

  @Inject private SubscriptionUsageSvc _subscriptionUsageSvc;
  @Inject private SubscriptionUsageBatchStatusDao _subscriptionUsageBatchStatusDao;
  @Inject private JobQueueTestUtils jobQueueTestUtils;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/host/HostDao/hosts.json.ftl", null, Host.DB_NAME, Host.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/svc/billing/SubscriptionUsageSvcIntTests/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
  }

  @Test
  public void testScheduleSubscriptionUsageBatchStatusOnCompleteJob() {
    _subscriptionUsageSvc.calcAllSubscriptionUsage();
    BatchJob subscriptionUsageJob = _batchJobDao.getLastBatchJob("SubscriptionUsage");
    assertEquals(
        SubscriptionUsageBatchStatusHandler.class.getName(),
        subscriptionUsageJob.getParams().get(BaseJob.ON_COMPLETE_PARAM_KEY));

    jobQueueTestUtils.processAllJobsInQueue();

    assertNotNull(_batchJobDao.getLastBatchJob("SubscriptionUsage").getOnCompleteFinishedAt());

    SubscriptionUsageBatchStatus status =
        _subscriptionUsageBatchStatusDao.findByBatchJobId(subscriptionUsageJob.getId()).get();

    assertEquals(Status.COMPLETED, status.getStatus());
    assertEquals(subscriptionUsageJob.getParams().get("startTime"), status.getUsageStartTime());
  }

  @Test
  public void testCalcAllSubscriptionUsage() throws Exception {

    // Normally this is triggered by the job processor, but the job processor is off in the int test
    // environment. Instead, we just invoke the handler directly.
    SubscriptionUsageBatchJobHandler handler =
        new SubscriptionUsageBatchJobHandler(null, _groupDao, _subscriptionUsageSvc);
    List<ObjectId> groupIds = _groupDao.findAllActiveGroupIds();
    handler.doHandleWork(groupIds, new BasicDBObject(), null);

    SubscriptionUsage usage = getSubscriptionUsageSvc().findMostRecentByGroupId(oid(103));
    assertNotNull(usage);
    assertEquals(oid(103), usage.getGroupId());
    assertNotNull(usage.getCreated());
    assertEquals(3, usage.getBillableHosts());
    assertEquals(usage.getNDSInstanceUsage(), List.of());

    Date startTime = usage.getStartTime();
    Date endTime = usage.getEndTime();
    assertNotNull(startTime);
    assertNotNull(endTime);
    assertEquals(0, DateUtils.getFragmentInMilliseconds(startTime, Calendar.HOUR_OF_DAY));
    assertEquals(0, DateUtils.getFragmentInMilliseconds(endTime, Calendar.HOUR_OF_DAY));
    assertEquals(endTime, DateUtils.addHours(startTime, 1));

    SubscriptionUsage subUsageForGroup101WithEmptyUsage =
        getSubscriptionUsageSvc().findMostRecentByGroupId(oid(101));
    assertNull(subUsageForGroup101WithEmptyUsage);

    SubscriptionUsage subUsageForGroup143WithEmptyUsage =
        getSubscriptionUsageSvc().findMostRecentByGroupId(oid(143));
    assertNull(subUsageForGroup143WithEmptyUsage);

    SubscriptionUsage ndsUsage = getSubscriptionUsageSvc().findMostRecentByGroupId(oid(118));
    assertNotNull(ndsUsage);
    assertEquals(oid(118), ndsUsage.getGroupId());

    List<NDSInstanceUsage> instanceUsageList = ndsUsage.getNDSInstanceUsage();
    assertEquals(21, instanceUsageList.size());

    for (NDSInstanceUsage instanceUsage : instanceUsageList) {
      // We allow only a cloud provider for a group but here we have a mix of cloud providers.
      // It is still possible, if rare, to have a mix of cloud providers: You delete all clusters in
      // a cloud provider and then create a new cluster in another cloud provider. Then both cloud
      // providers can show up in the same invoice.

      switch (instanceUsage.getCloudProvider()) {
        case AWS:
          switch (instanceUsage.getAWSInstanceSize()) {
            case M10:
              // Replica set
              assertEquals(oid(16), instanceUsage.getClusterUniqueId());
              assertEquals("rs2", instanceUsage.getClusterName());
              assertEquals(AWSRegionName.US_EAST_2, instanceUsage.getAWSRegionName());
              assertEquals(5, instanceUsage.getInstanceCount());
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M30:
              // Replica set
              assertEquals(oid(1), instanceUsage.getClusterUniqueId());
              assertEquals("rs1", instanceUsage.getClusterName());
              assertEquals(AWSRegionName.US_EAST_1, instanceUsage.getAWSRegionName());
              assertEquals(5, instanceUsage.getInstanceCount());
              assertTrue(instanceUsage.isBiConnectorEnabled());
              break;
            case M40:
              // Sharded cluster with a read-only member of each shard in EU
              if (instanceUsage.getAWSRegionName().equals(AWSRegionName.US_WEST_2)) {
                assertEquals(oid(2), instanceUsage.getClusterUniqueId());
                assertEquals("sh1", instanceUsage.getClusterName());
                assertEquals(9, instanceUsage.getInstanceCount());
              } else if (instanceUsage.getAWSRegionName().equals(AWSRegionName.EU_WEST_1)) {
                assertEquals(oid(2), instanceUsage.getClusterUniqueId());
                assertEquals("sh1", instanceUsage.getClusterName());
                assertEquals(3, instanceUsage.getInstanceCount());
              } else {
                assertEquals(oid(13), instanceUsage.getClusterUniqueId());
                assertEquals("rs3", instanceUsage.getClusterName());
                assertEquals(AWSRegionName.US_WEST_1, instanceUsage.getAWSRegionName());
                assertEquals(9, instanceUsage.getInstanceCount());
              }
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M20:
              // Config servers for sharded cluster
              if (instanceUsage.getClusterUniqueId().equals(oid(2))) {
                assertEquals(oid(2), instanceUsage.getClusterUniqueId());
                assertEquals("sh1", instanceUsage.getClusterName());
                assertEquals(AWSRegionName.US_WEST_2, instanceUsage.getAWSRegionName());
                assertEquals(3, instanceUsage.getInstanceCount());
              } else {
                assertEquals(oid(13), instanceUsage.getClusterUniqueId());
                assertEquals("rs3", instanceUsage.getClusterName());
                assertEquals(AWSRegionName.US_WEST_1, instanceUsage.getAWSRegionName());
                assertEquals(3, instanceUsage.getInstanceCount());
              }
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M40_NVME:
              assertEquals(oid(1), instanceUsage.getClusterUniqueId());
              assertEquals("rsNvme", instanceUsage.getClusterName());
              assertEquals(AWSRegionName.EU_WEST_3, instanceUsage.getAWSRegionName());
              assertEquals(4, instanceUsage.getInstanceCount());
              assertTrue(instanceUsage.isBiConnectorEnabled());
              assertEquals(
                  3000, instanceUsage.getAWSInstanceSize().getBackupDiskIOPS().get().intValue());
              assertEquals(
                  400, instanceUsage.getAWSInstanceSize().getBackupDiskSizeGB().get().intValue());
              break;
            case M50:
              // asymmetric cluster - analytics nodes
              assertEquals(oid(3), instanceUsage.getClusterUniqueId());
              assertEquals("rs4", instanceUsage.getClusterName());
              assertEquals(AWSRegionName.EU_WEST_2, instanceUsage.getAWSRegionName());
              assertEquals(1, instanceUsage.getInstanceCount());
              break;
            case M60:
              // asymmetric cluster - electable nodes
              assertEquals(oid(3), instanceUsage.getClusterUniqueId());
              assertEquals("rs4", instanceUsage.getClusterName());
              assertEquals(AWSRegionName.EU_WEST_2, instanceUsage.getAWSRegionName());
              assertEquals(3, instanceUsage.getInstanceCount());
              break;
            case M80:
              // config shard cluster
              assertEquals(oid(18), instanceUsage.getClusterUniqueId());
              assertEquals("config-shard-1", instanceUsage.getClusterName());
              assertEquals(AWSRegionName.EU_CENTRAL_1, instanceUsage.getAWSRegionName());
              assertEquals(9, instanceUsage.getInstanceCount());
              break;
            default:
              fail(instanceUsage.getAWSInstanceSize().name());
              break;
          }
          break;
        case GCP:
          switch (instanceUsage.getGCPInstanceSize()) {
            case M10:
              // Replica set
              assertEquals(oid(5), instanceUsage.getClusterUniqueId());
              assertEquals("gcp1", instanceUsage.getClusterName());
              assertEquals(GCPRegionName.EASTERN_US, instanceUsage.getGCPRegionName());
              assertEquals(3, instanceUsage.getInstanceCount());
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M30:
              // Sharded cluster with a read-only member of each shard in WESTERN_EUROPE
              if (instanceUsage.getGCPRegionName().equals(GCPRegionName.CENTRAL_US)) {
                assertEquals(oid(12), instanceUsage.getClusterUniqueId());
                assertEquals("gcp_sh1", instanceUsage.getClusterName());
                assertEquals(12, instanceUsage.getInstanceCount());
              } else if (instanceUsage.getGCPRegionName().equals(GCPRegionName.WESTERN_EUROPE)) {
                assertEquals(oid(12), instanceUsage.getClusterUniqueId());
                assertEquals("gcp_sh1", instanceUsage.getClusterName());
                assertEquals(3, instanceUsage.getInstanceCount());
              } else {
                fail(instanceUsage.getGCPRegionName().name());
              }
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M20:
              // Config servers for sharded cluster
              assertEquals(oid(12), instanceUsage.getClusterUniqueId());
              assertEquals("gcp_sh1", instanceUsage.getClusterName());
              assertEquals(GCPRegionName.CENTRAL_US, instanceUsage.getGCPRegionName());
              assertEquals(3, instanceUsage.getInstanceCount());
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
          }
          break;
        case AZURE:
          switch (instanceUsage.getAzureInstanceSize()) {
            case M20:
              // Replica set
              assertEquals(oid(10), instanceUsage.getClusterUniqueId());
              assertEquals("azure", instanceUsage.getClusterName());
              assertEquals(AzureRegionName.US_EAST_2, instanceUsage.getAzureRegionName());
              assertEquals(3, instanceUsage.getInstanceCount());
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M50:
              // Sharded cluster with a read-only member of each shard in US_CENTRAL
              if (instanceUsage.getAzureRegionName().equals(AzureRegionName.US_EAST)) {
                assertEquals(oid(11), instanceUsage.getClusterUniqueId());
                assertEquals("azure_sh1", instanceUsage.getClusterName());
                assertEquals(9, instanceUsage.getInstanceCount());
              } else if (instanceUsage.getAzureRegionName().equals(AzureRegionName.US_CENTRAL)) {
                assertEquals(oid(11), instanceUsage.getClusterUniqueId());
                assertEquals("azure_sh1", instanceUsage.getClusterName());
                assertEquals(3, instanceUsage.getInstanceCount());
              } else {
                fail(instanceUsage.getAzureRegionName().name());
              }
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
            case M40:
              // Config servers for sharded cluster
              assertEquals(oid(11), instanceUsage.getClusterUniqueId());
              assertEquals("azure_sh1", instanceUsage.getClusterName());
              assertEquals(AzureRegionName.US_EAST, instanceUsage.getAzureRegionName());
              assertEquals(3, instanceUsage.getInstanceCount());
              assertFalse(instanceUsage.isBiConnectorEnabled());
              break;
          }
          break;
        case FREE:
          assertEquals(FreeInstanceSize.M5, instanceUsage.getFreeInstanceSize());
          assertNull(instanceUsage.getServerlessInstanceSize());
          assertEquals(oid(9), instanceUsage.getClusterUniqueId());
          assertEquals("free1", instanceUsage.getClusterName());
          assertEquals(AWSRegionName.US_EAST_1, instanceUsage.getAWSRegionName());
          assertNull(instanceUsage.getAzureRegionName());
          assertNull(instanceUsage.getGCPRegionName());
          assertEquals(3, instanceUsage.getInstanceCount());
          assertFalse(instanceUsage.isBiConnectorEnabled());
          break;
        case SERVERLESS:
          assertEquals(
              ServerlessInstanceSize.SERVERLESS_V2, instanceUsage.getServerlessInstanceSize());
          assertNull(instanceUsage.getFreeInstanceSize());
          assertEquals(oid(19), instanceUsage.getClusterUniqueId());
          assertEquals("serverless1", instanceUsage.getClusterName());
          assertEquals(AWSRegionName.US_EAST_1, instanceUsage.getAWSRegionName());
          assertNull(instanceUsage.getAzureRegionName());
          assertNull(instanceUsage.getGCPRegionName());
          assertEquals(3, instanceUsage.getInstanceCount());
          assertFalse(instanceUsage.isBiConnectorEnabled());
          break;
        default:
          fail(instanceUsage.getCloudProvider().name());
          break;
      }
      assertFalse(instanceUsage.isAdvancedSecurityEnabled());
      assertFalse(instanceUsage.isEnterpriseAuditingEnabled());
    }

    List<NDSStorageUsage> storageUsageList = ndsUsage.getNDSStorageUsage();
    assertEquals(14, storageUsageList.size());
    for (NDSStorageUsage storageUsage : storageUsageList) {
      switch (storageUsage.getCloudProvider()) {
        case AWS:
          switch (storageUsage.getAWSRegionName()) {
            case US_EAST_1:
              assertEquals(oid(1), storageUsage.getClusterUniqueId());
              assertEquals("rs1", storageUsage.getClusterName());
              assertEquals(VolumeType.Io1, storageUsage.getAWSVolumeType());
              assertEquals(320, storageUsage.getDiskSizeGB());
              assertEquals(5000, storageUsage.getDiskIOPS());
              break;
            case US_EAST_2:
              assertEquals(oid(16), storageUsage.getClusterUniqueId());
              assertEquals("rs2", storageUsage.getClusterName());
              assertEquals(VolumeType.Io1, storageUsage.getAWSVolumeType());
              assertEquals(320, storageUsage.getDiskSizeGB());
              assertEquals(5000, storageUsage.getDiskIOPS());
              break;
            case US_WEST_2:
              assertEquals(oid(2), storageUsage.getClusterUniqueId());
              assertEquals("sh1", storageUsage.getClusterName());
              assertEquals(VolumeType.Gp2, storageUsage.getAWSVolumeType());
              assertEquals(9216, storageUsage.getDiskSizeGB());
              assertEquals(10500, storageUsage.getDiskIOPS());
              break;
            case EU_WEST_1:
              assertEquals(oid(2), storageUsage.getClusterUniqueId());
              assertEquals("sh1", storageUsage.getClusterName());
              assertEquals(VolumeType.Gp2, storageUsage.getAWSVolumeType());
              assertEquals(3072, storageUsage.getDiskSizeGB());
              assertEquals(3000, storageUsage.getDiskIOPS());
              break;
            case US_WEST_1:
              assertEquals(oid(13), storageUsage.getClusterUniqueId());
              assertEquals("rs3", storageUsage.getClusterName());
              assertEquals(VolumeType.Gp2, storageUsage.getAWSVolumeType());
              assertEquals(6330, storageUsage.getDiskSizeGB());
              assertEquals(9300, storageUsage.getDiskIOPS());
              break;
            case EU_WEST_2:
              // asymmetric cluster with different instance sizes but same disk size and IOPS
              assertEquals(oid(3), storageUsage.getClusterUniqueId());
              assertEquals("rs4", storageUsage.getClusterName());
              assertEquals(VolumeType.Io1, storageUsage.getAWSVolumeType());
              assertEquals(256, storageUsage.getDiskSizeGB());
              assertEquals(4000, storageUsage.getDiskIOPS());
              break;
            case EU_WEST_3:
              assertEquals(oid(1), storageUsage.getClusterUniqueId());
              assertEquals("rsNvme", storageUsage.getClusterName());
              assertEquals(VolumeType.Io1, storageUsage.getAWSVolumeType());
              assertEquals(400, storageUsage.getDiskSizeGB());
              assertEquals(3000, storageUsage.getDiskIOPS());
              break;
            case EU_CENTRAL_1:
              assertEquals(oid(18), storageUsage.getClusterUniqueId());
              assertEquals("config-shard-1", storageUsage.getClusterName());
              assertEquals(VolumeType.Gp2, storageUsage.getAWSVolumeType());
              assertEquals(6300, storageUsage.getDiskSizeGB());
              assertEquals(1000, storageUsage.getDiskIOPS());
              break;
            default:
              fail(storageUsage.getAWSRegionName().name());
              break;
          }
          break;
        case AZURE:
          switch (storageUsage.getAzureRegionName()) {
            case US_EAST_2:
              assertEquals(oid(10), storageUsage.getClusterUniqueId());
              assertEquals("azure", storageUsage.getClusterName());
              assertEquals(384, storageUsage.getDiskSizeGB());
              assertFalse(storageUsage.hasDiskIOPS());
              assertEquals(0, storageUsage.getDiskIOPS());
              break;
            case US_EAST:
              assertEquals(oid(11), storageUsage.getClusterUniqueId());
              assertEquals("azure_sh1", storageUsage.getClusterName());
              if (storageUsage.getAzureDiskType().equals(AzureDiskType.P20)) {
                assertEquals(2304, storageUsage.getDiskSizeGB());
              }
              if (storageUsage.getAzureDiskType().equals(AzureDiskType.P10)) {
                assertEquals(384, storageUsage.getDiskSizeGB());
              }
              assertFalse(storageUsage.hasDiskIOPS());
              assertEquals(0, storageUsage.getDiskIOPS());
              break;
            case US_CENTRAL:
              assertEquals(oid(11), storageUsage.getClusterUniqueId());
              assertEquals("azure_sh1", storageUsage.getClusterName());
              assertEquals(768, storageUsage.getDiskSizeGB());
              assertFalse(storageUsage.hasDiskIOPS());
              assertEquals(0, storageUsage.getDiskIOPS());
              break;
            case US_WEST_CENTRAL:
              // Included IOPS = 4420 = (3500 + (200 - 16) * 5)
              // Electable included IOPS = 13260 = 4420 * 3
              // Analytic included IOPS = 4420 = 4420 * 1
              // Electable IOPS = 15000 = 5000 * 3
              // Analytic IOPS = 6000 = 6000 * 1
              // Billable IOPS = 3320 = (15000 - 13260) + (6000 - 4420)
              // Billable disk = 800 = (200 * 3) + (200 * 1)
              assertEquals(oid(20), storageUsage.getClusterUniqueId());
              assertEquals("pv2", storageUsage.getClusterName());
              assertEquals(800, storageUsage.getDiskSizeGB());
              assertEquals(3320, storageUsage.getDiskIOPS());
              assertTrue(storageUsage.hasDiskIOPS());
              break;
            default:
              fail(storageUsage.getAzureRegionName().name());
              break;
          }
          break;
        case GCP:
          switch (storageUsage.getGCPRegionName()) {
            case CENTRAL_US:
              assertEquals(oid(12), storageUsage.getClusterUniqueId());
              assertEquals("gcp_sh1", storageUsage.getClusterName());
              assertEquals(900, storageUsage.getDiskSizeGB());
              break;
            case WESTERN_EUROPE:
              assertEquals(oid(12), storageUsage.getClusterUniqueId());
              assertEquals("gcp_sh1", storageUsage.getClusterName());
              assertEquals(300, storageUsage.getDiskSizeGB());
              break;
            default:
              fail(storageUsage.getGCPRegionName().name());
              break;
          }
          break;
        default:
          fail(storageUsage.getCloudProvider().name());
          break;
      }
    }

    // Test that the storage free tier doesn't get applied to provisioned storage.
    SubscriptionUsage provisionedUsage =
        getSubscriptionUsageSvc().findMostRecentByGroupId(oid(126));
    assertNotNull(provisionedUsage);
    assertEquals(oid(126), provisionedUsage.getGroupId());

    List<NDSStorageUsage> provisionedStorageUsageList = provisionedUsage.getNDSStorageUsage();
    assertEquals(1, provisionedStorageUsageList.size());
    NDSStorageUsage provisionedStorageUsage = provisionedStorageUsageList.get(0);
    assertEquals(CloudProvider.AWS, provisionedStorageUsage.getCloudProvider());
    assertEquals(VolumeType.Io1, provisionedStorageUsage.getAWSVolumeType());
    assertEquals(180, provisionedStorageUsage.getDiskSizeGB());
    assertEquals(3000, provisionedStorageUsage.getDiskIOPS());

    // Test that the property is set if LDAP is enabled.
    SubscriptionUsage ldapUsage = getSubscriptionUsageSvc().findMostRecentByGroupId(oid(142));
    assertNotNull(ldapUsage);
    List<NDSInstanceUsage> ldapInstanceUsageList = ldapUsage.getNDSInstanceUsage();
    for (NDSInstanceUsage instanceUsage : ldapInstanceUsageList) {
      assertTrue(instanceUsage.isAdvancedSecurityEnabled());
      assertTrue(instanceUsage.isEnterpriseAuditingEnabled());
    }
  }

  @Test
  public void testNotCalculateSubcriptionUsageForFlexClusters() throws Exception {
    ObjectId flexGroupId = oid(519);

    SubscriptionUsageBatchJobHandler handler =
        new SubscriptionUsageBatchJobHandler(null, _groupDao, _subscriptionUsageSvc);

    List<ObjectId> groupIds = _groupDao.findAllActiveGroupIds();
    assertTrue(
        groupIds.contains(flexGroupId), "did not contain expected group with id " + flexGroupId);

    handler.doHandleWork(groupIds, new BasicDBObject(), null);
    Optional<SubscriptionUsage> provisionedUsage =
        Optional.ofNullable(getSubscriptionUsageSvc().findMostRecentByGroupId(flexGroupId));

    assertTrue(
        provisionedUsage.isEmpty(), "USS subscription usage was created but should not have been");
  }

  @Test
  public void testCalcNDSTotalCapacity() {
    assertEquals(
        32030, getSubscriptionUsageSvc().calcNDSTotalCapacity(_groupDao.findById(oid(118))));
    assertEquals(180, getSubscriptionUsageSvc().calcNDSTotalCapacity(_groupDao.findById(oid(126))));
  }

  @Test
  public void testGetBillableHosts() {
    Set<String> hosts = getSubscriptionUsageSvc().getBillableHosts(oid(103));
    assertEquals(Set.of("localhost", "v_3_4_0.example.com", "something.example.com"), hosts);
  }

  protected SubscriptionUsageSvc getSubscriptionUsageSvc() {
    return _subscriptionUsageSvc;
  }
}
