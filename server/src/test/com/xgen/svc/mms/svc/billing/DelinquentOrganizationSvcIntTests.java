package com.xgen.svc.mms.svc.billing;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static com.xgen.cloud.common.util._public.time.DateTimeUtils.localDateOf;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.OrgFinancialProtection;
import com.xgen.cloud.organization._public.model.OrgFinancialProtectionStatus;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus.Type;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.activity.OrgAudit;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.netsuite.common._public.models.NetsuiteInvoiceDetails;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.PayingOrgDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.FailedChargeAttempts;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.billing.PayingOrg;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlanType;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.util.billing.testFactories.CreateInvoiceParams;
import com.xgen.svc.mms.util.billing.testFactories.CreditFactory;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import com.xgen.svc.mms.util.billing.testFactories.PayingOrgFactory;
import com.xgen.svc.mms.util.billing.testFactories.PaymentFactory;
import jakarta.inject.Inject;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DelinquentOrganizationSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private OrganizationDao _organizationDao;

  @Inject private PaymentDao _paymentDao;

  @Inject private PayingOrgDao _payingOrgDao;

  @Inject private AuditSvc _auditSvc;

  @Inject private OrganizationSvc _organizationSvc;

  @Inject private DelinquentOrganizationSvc _delinquentOrganizationSvc;

  @Inject private PlanSvc _planSvc;

  @Inject private AccountantSvc _accountantSvc;

  @Inject private CreditFactory _creditFactory;

  @Inject private OrganizationFactory _orgFactory;

  @Inject private InvoiceFactory invoiceFactory;

  @Inject private PayingOrgFactory payingOrgFactory;

  @Inject private PaymentFactory paymentFactory;

  @Inject private OrganizationFactory organizationFactory;

  @Inject private BusinessDayChecker businessDayChecker;
  @Inject private JobQueueTestUtils jobQueueTestUtils;

  @Inject private CreditDao creditDao;

  @Mock private AppSettings appSettings;

  @Inject private FeatureFlagSvc featureFlagSvc;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/PaymentDao/payments.json.ftl",
        null,
        Payment.DB_NAME,
        Payment.COLLECTION_NAME);

    doReturn(true)
        .when(appSettings)
        .isFeatureFlagInEnabledState(FeatureFlag.SALES_SOLD_WARNING_ORG_PAYMENT_STATUS);
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testActivateDelinquentOrgAndLinkedOrgs() {
    Date now = new Date();
    Date dueDate = Date.from(now.toInstant().minus(PaymentSvc.SUSPENDED_DELINQUENCY_DURATION));
    List<Payment> failedPayments = _paymentDao.findFailedPaymentsByOrgId(oid(205));
    assertEquals(1, failedPayments.size());
    Payment failedPayment = failedPayments.get(0);
    assertTrue(failedPayment.getPaymentDueDate().before(now));

    AuditInfo acct = AuditInfoHelpers.fromAccountant();
    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        _organizationDao.findById(oid(205)), now, acct);

    List<Payment> updatedFailedPayments = _paymentDao.findFailedPaymentsByOrgId(oid(205));
    assertEquals(1, updatedFailedPayments.size());
    Payment updatedFailedPayment = updatedFailedPayments.get(0);
    assertEquals(failedPayment.getId(), updatedFailedPayment.getId());
    assertEquals(Payment.Status.FAILED, updatedFailedPayment.getStatus());
    assertEquals(dueDate, updatedFailedPayment.getPaymentDueDate());

    Organization org = _organizationDao.findById(oid(205));
    assertEquals(OrgPaymentStatus.Type.WARNING, org.getPaymentStatus().getStatus());

    List<Event> activatedAuditEvents =
        _auditSvc.findByDate(OrgAudit.Type.ORG_TEMPORARILY_ACTIVATED, now);
    assertEquals(1, activatedAuditEvents.size());
    OrgAudit activatedEvent = (OrgAudit) activatedAuditEvents.get(0);
    assertEquals(OrgAudit.Type.ORG_TEMPORARILY_ACTIVATED, activatedEvent.getEventType());
    assertEquals(acct.getEventSource(), activatedEvent.getSource());
    assertEquals(oid(205), activatedEvent.getOrgId());
    assertEquals(org.getName(), activatedEvent.getOrgName());
    assertEquals(now, activatedEvent.getSuspensionDate());

    List<Event> suspensionDateAuditEvents =
        _auditSvc.findByDate(OrgAudit.Type.ORG_SUSPENSION_DATE_CHANGED, now);
    assertEquals(1, suspensionDateAuditEvents.size());
    OrgAudit suspensionDateEvent = (OrgAudit) suspensionDateAuditEvents.get(0);
    assertEquals(OrgAudit.Type.ORG_SUSPENSION_DATE_CHANGED, suspensionDateEvent.getEventType());
    assertEquals(acct.getEventSource(), suspensionDateEvent.getSource());
    assertEquals(oid(205), suspensionDateEvent.getOrgId());
    assertEquals(org.getName(), suspensionDateEvent.getOrgName());
    assertEquals(now, suspensionDateEvent.getSuspensionDate());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testActivateDelinquentOrgAndLinkedOrgs_unsuspendsLinkedOrgs() {
    Date now = new Date();

    Organization linkedOrg = organizationFactory.createAtlasOrganization();
    _organizationDao.updateOrgPaymentStatus(
        linkedOrg.getId(), OrgPaymentStatus.suspended(now, false, LocalDate.now()));
    _payingOrgDao.upsertPayingOrg(oid(205), List.of(linkedOrg.getId()), Instant.now());
    assertEquals(
        Type.SUSPENDED,
        _organizationDao.findById(linkedOrg.getId()).getPaymentStatus().getStatus());

    List<Payment> failedPayments = _paymentDao.findFailedPaymentsByOrgId(oid(205));
    assertEquals(1, failedPayments.size());
    Payment failedPayment = failedPayments.get(0);
    assertTrue(failedPayment.getPaymentDueDate().before(now));

    AuditInfo acct = AuditInfoHelpers.fromAccountant();
    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        _organizationDao.findById(oid(205)), now, acct);

    Organization org = _organizationDao.findById(oid(205));
    assertEquals(OrgPaymentStatus.Type.WARNING, org.getPaymentStatus().getStatus());

    Organization linkedOrgAfter = _organizationDao.findById(linkedOrg.getId());
    assertNotEquals(Type.SUSPENDED, linkedOrgAfter.getPaymentStatus().getStatus());
  }

  @Test
  public void testSetStatusForChangedOrganization() {
    Date now = new Date();
    OrgPaymentStatus orgPaymentStatus = OrgPaymentStatus.ok(null);
    Organization suspendedOrg =
        new Organization.Builder()
            .id(new ObjectId())
            .name("abc")
            .paymentStatus(orgPaymentStatus)
            .build();
    _organizationDao.save(suspendedOrg);
    Date suspendedDate =
        DateUtils.addDays(now, (int) -PaymentSvc.SUSPENDED_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForSuspendedOrg =
        new Payment.Builder()
            .orgId(suspendedOrg.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(suspendedDate)
            .updated(suspendedDate)
            .build();
    _paymentDao.save(paymentForSuspendedOrg);

    Organization lockedOutOrg = _organizationDao.findById(oid(323));

    Date lockedDate =
        DateUtils.addDays(now, (int) -PaymentSvc.LOCKED_OUT_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForLockedOutOrg =
        new Payment.Builder()
            .orgId(lockedOutOrg.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(lockedDate)
            .updated(lockedDate)
            .build();
    _paymentDao.save(paymentForLockedOutOrg);

    Organization warningOrg =
        new Organization.Builder()
            .id(new ObjectId())
            .name("ghi")
            .paymentStatus(orgPaymentStatus)
            .build();
    _organizationDao.save(warningOrg);
    Date warningDate =
        DateUtils.addDays(now, (int) -PaymentSvc.SUSPENDED_DELINQUENCY_DURATION.toDays());
    Payment paymentForWarningOrg =
        new Payment.Builder()
            .orgId(warningOrg.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(warningDate)
            .updated(warningDate)
            .build();
    _paymentDao.save(paymentForWarningOrg);

    Organization okOrg =
        new Organization.Builder()
            .id(new ObjectId())
            .name("jkl")
            .paymentStatus(orgPaymentStatus)
            .build();
    _organizationDao.save(okOrg);

    Payment paymentForAdminSuspendedOrg =
        new Payment.Builder()
            .orgId(suspendedOrg.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(suspendedDate)
            .updated(suspendedDate)
            .build();
    _paymentDao.save(paymentForAdminSuspendedOrg);

    Organization okOrgProcessing =
        new Organization.Builder()
            .id(new ObjectId())
            .name("jkl")
            .paymentStatus(orgPaymentStatus)
            .build();
    Payment processingPayment =
        new Payment.Builder()
            .id(new ObjectId())
            .orgId(okOrgProcessing.getId())
            .amountBilledCents(1000L)
            .status(Status.PROCESSING)
            .paymentDueDate(new Date())
            .build();
    _organizationDao.save(okOrgProcessing);
    _paymentDao.save(processingPayment);

    _delinquentOrganizationSvc.updatePaymentStatusForOrganizations(
        List.of(
            suspendedOrg.getId(),
            lockedOutOrg.getId(),
            warningOrg.getId(),
            okOrg.getId(),
            okOrgProcessing.getId()),
        now);

    List<Event> suspendedAuditEvents = _auditSvc.findByDate(OrgAudit.Type.ORG_SUSPENDED, now);
    List<Event> lockedAuditEvents = _auditSvc.findByDate(OrgAudit.Type.ORG_LOCKED, now);

    // Test newly SUSPENDED org if business days
    if (businessDayChecker.isBusinessDay(now)) {
      assertEquals(
          OrgPaymentStatus.Type.SUSPENDED,
          _organizationSvc.findById(suspendedOrg.getId()).getPaymentStatus().getStatus());
      assertEquals(1, suspendedAuditEvents.size());
    }

    // Test newly LOCKED org
    assertEquals(
        OrgPaymentStatus.Type.LOCKED,
        _organizationSvc.findById(lockedOutOrg.getId()).getPaymentStatus().getStatus());
    assertEquals(1, lockedAuditEvents.size());

    // Test newly WARNING org
    assertEquals(
        OrgPaymentStatus.Type.WARNING,
        _organizationSvc.findById(warningOrg.getId()).getPaymentStatus().getStatus());

    // Test OK org
    assertEquals(
        OrgPaymentStatus.Type.OK,
        _organizationSvc.findById(okOrg.getId()).getPaymentStatus().getStatus());

    // Test OK org with Processing payment
    assertEquals(
        OrgPaymentStatus.Type.OK,
        _organizationSvc.findById(okOrgProcessing.getId()).getPaymentStatus().getStatus());

    saveFailedChargeAttempts(processingPayment.getId(), 1, now);
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(okOrgProcessing, now);

    // Test OK org with Processing payment that had previously failed
    assertEquals(
        Type.WARNING,
        _organizationSvc.findById(okOrgProcessing.getId()).getPaymentStatus().getStatus());
  }

  @Test
  public void testUpdatePaymentStatusForOrgUnderFinancialProtection() {
    Date now = new Date();
    OrgPaymentStatus paymentStatusOK = OrgPaymentStatus.ok(null);

    OrgFinancialProtection financialProtectionStatusBankruptcy =
        OrgFinancialProtection.builder()
            .status(OrgFinancialProtectionStatus.BANKRUPTCY)
            .since(LocalDateTime.now())
            .build();

    // GIVEN an org under financial protection and payment status is OK
    Organization okOrg =
        new Organization.Builder()
            .id(new ObjectId())
            .name("okOrg")
            .paymentStatus(paymentStatusOK)
            .financialProtectionStatus(financialProtectionStatusBankruptcy)
            .build();
    _organizationDao.save(okOrg);

    // GIVEN an org under financial protection and about to enter Warning payment status
    Organization orgShouldBeWarnedButUnderFinancialProtection =
        new Organization.Builder()
            .id(new ObjectId())
            .name("orgShouldBeWarnedButUnderFinancialProtection")
            .paymentStatus(paymentStatusOK)
            .financialProtectionStatus(financialProtectionStatusBankruptcy)
            .build();
    _organizationDao.save(orgShouldBeWarnedButUnderFinancialProtection);

    Date warningDate =
        DateUtils.addDays(now, (int) -PaymentSvc.SUSPENDED_DELINQUENCY_DURATION.toDays());
    Payment paymentForWarningOrg =
        new Payment.Builder()
            .orgId(orgShouldBeWarnedButUnderFinancialProtection.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(warningDate)
            .updated(warningDate)
            .build();
    _paymentDao.save(paymentForWarningOrg);

    // GIVEN an org under financial protection and about to be suspended
    Organization orgShouldBeSuspendedButUnderFinancialProtection =
        new Organization.Builder()
            .id(new ObjectId())
            .name("orgShouldBeSuspendedButUnderFinancialProtection")
            .paymentStatus(paymentStatusOK)
            .financialProtectionStatus(financialProtectionStatusBankruptcy)
            .build();
    _organizationDao.save(orgShouldBeSuspendedButUnderFinancialProtection);

    Date suspendedDate =
        DateUtils.addDays(now, (int) -PaymentSvc.SUSPENDED_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForSuspendedOrg =
        new Payment.Builder()
            .orgId(orgShouldBeSuspendedButUnderFinancialProtection.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(suspendedDate)
            .updated(suspendedDate)
            .build();
    _paymentDao.save(paymentForSuspendedOrg);

    // GIVEN an org should be locked but it's under financial protection
    Organization orgShouldBeLockedButUnderFinancialProtection =
        new Organization.Builder()
            .id(new ObjectId())
            .name("orgShouldBeLockedButUnderFinancialProtection")
            .paymentStatus(paymentStatusOK)
            .financialProtectionStatus(financialProtectionStatusBankruptcy)
            .build();
    _organizationDao.save(orgShouldBeLockedButUnderFinancialProtection);

    Date lockedDate =
        DateUtils.addDays(now, (int) -PaymentSvc.LOCKED_OUT_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForLockedOutOrg =
        new Payment.Builder()
            .orgId(orgShouldBeLockedButUnderFinancialProtection.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(lockedDate)
            .updated(lockedDate)
            .build();
    _paymentDao.save(paymentForLockedOutOrg);

    // WHEN
    _delinquentOrganizationSvc.updatePaymentStatusForOrganizations(
        List.of(
            okOrg.getId(),
            orgShouldBeWarnedButUnderFinancialProtection.getId(),
            orgShouldBeSuspendedButUnderFinancialProtection.getId()),
        now);

    List<Event> suspendedAuditEvents = _auditSvc.findByDate(OrgAudit.Type.ORG_SUSPENDED, now);
    List<Event> lockedAuditEvents = _auditSvc.findByDate(OrgAudit.Type.ORG_LOCKED, now);

    // THEN org under financial protection remains OK status if no failed payments
    assertEquals(
        OrgPaymentStatus.Type.OK,
        _organizationSvc.findById(okOrg.getId()).getPaymentStatus().getStatus());

    // THEN org under financial protection should not be warned even if there are failed payments
    assertEquals(
        OrgPaymentStatus.Type.OK,
        _organizationSvc
            .findById(orgShouldBeWarnedButUnderFinancialProtection.getId())
            .getPaymentStatus()
            .getStatus());

    // THEN org under financial protection should not be suspended even if there are failed payments
    if (businessDayChecker.isBusinessDay(now)) {
      assertEquals(
          OrgPaymentStatus.Type.OK,
          _organizationSvc
              .findById(orgShouldBeSuspendedButUnderFinancialProtection.getId())
              .getPaymentStatus()
              .getStatus());
      assertEquals(0, suspendedAuditEvents.size());
    }

    // THEN org under financial protection should not be locked even if there are failed payments
    assertEquals(
        OrgPaymentStatus.Type.OK,
        _organizationSvc
            .findById(orgShouldBeLockedButUnderFinancialProtection.getId())
            .getPaymentStatus()
            .getStatus());
    assertEquals(0, lockedAuditEvents.size());
  }

  @Test
  public void testDetermineOrgPaymentStatus_saleSoldWarning() {
    LocalDate mockNow = LocalDate.parse("2024-08-01");
    Date mockNowDate = dateOf(mockNow);
    Date dueDateNotOverdue = dateOf(LocalDate.parse("2024-07-15"));
    Date dueDateOverdueBy30Days = dateOf(LocalDate.parse("2024-07-01"));
    Date date20240101 = TimeUtils.fromISOString("2024-01-01");
    Date date20250101 = TimeUtils.fromISOString("2025-01-01");

    Organization org = organizationFactory.createAtlasOrganization();
    ObjectId orgId = org.getId();

    assertEquals(
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, mockNowDate).getStatus(),
        Type.OK);

    ObjectId directPrepaidCreditId = new ObjectId();
    Credit directPrepaidCredit =
        Credit.builder()
            .id(directPrepaidCreditId)
            .orgId(orgId)
            .startDate(date20240101)
            .endDate(date20250101)
            .elasticInvoicing(true)
            .amountCents(10000)
            .amountRemainingCents(0)
            .type(CreditType.PREPAID_NDS)
            .build();
    creditDao.save(directPrepaidCredit);

    NetsuiteInvoiceDetails mockNetsuiteInvoiceDetails =
        NetsuiteInvoiceDetails.builder()
            .taxTotal(0.0)
            .rate(1.0)
            .unitPrice(1.0)
            .exchangeRate(1.0)
            .total(58460.31)
            .currency("USD")
            .tranId("IIR483295")
            .customerInternalId("676208")
            .amountPaid(0.0)
            .dueDate(date20240101)
            .build();

    // GIVEN a direct payment not overdue yet
    paymentFactory.createPaymentWithBuilder(
        Payment.builder()
            .orgId(orgId)
            .paymentMethod(PaymentMethodType.INVOICE)
            .amountPaidCents(0L)
            .status(Status.INVOICED)
            .paymentDueDate(dueDateNotOverdue)
            .creditId(directPrepaidCreditId)
            .netsuiteInvoiceInternalId("16700001")
            .netsuiteInvoiceDetails(mockNetsuiteInvoiceDetails));
    // WHEN - THEN
    assertEquals(
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, mockNowDate).getStatus(),
        Type.OK);

    // GIVEN a mp payment not eligible for dunning notification
    paymentFactory.createPaymentWithBuilder(
        Payment.builder()
            .orgId(orgId)
            .paymentMethod(PaymentMethodType.AWS_MARKETPLACE_INVOICE)
            .amountPaidCents(0L)
            .status(Status.INVOICED)
            .paymentDueDate(dueDateOverdueBy30Days)
            .creditId(directPrepaidCreditId)
            .netsuiteInvoiceInternalId("16700002")
            .netsuiteInvoiceDetails(mockNetsuiteInvoiceDetails));
    // WHEN - THEN
    assertEquals(
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, mockNowDate).getStatus(),
        Type.OK);

    // GIVEN a payment that is eligible
    Payment unpaidPayment =
        paymentFactory.createPaymentWithBuilder(
            Payment.builder()
                .orgId(orgId)
                .paymentMethod(PaymentMethodType.INVOICE)
                .amountPaidCents(0L)
                .status(Status.INVOICED)
                .paymentDueDate(dueDateOverdueBy30Days)
                .creditId(directPrepaidCreditId)
                .netsuiteInvoiceInternalId("16700003")
                .netsuiteInvoiceDetails(mockNetsuiteInvoiceDetails));

    // WHEN - THEN
    assertEquals(
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, mockNowDate).getStatus(),
        Type.SALES_SOLD_WARNING);

    // GIVEN a weekend as mock now
    LocalDate weekend = LocalDate.parse("2024-08-04");

    // WHEN - THEN expect org payment status is still OK; Because OK -> SALES_SOLD_WARNING is not
    // allowed on weekend
    assertEquals(
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, dateOf(weekend)).getStatus(),
        Type.OK);

    // WHEN - update the org payment status
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, mockNowDate);

    // THEN expect the org payment status should be updated to SALES_SOLD_WARNING
    Organization updatedOrg = _organizationDao.findById(orgId);
    assertEquals(updatedOrg.getPaymentStatus().getStatus(), Type.SALES_SOLD_WARNING);

    // GIVEN - update the payment to paid
    _paymentDao.setPaymentStatus(unpaidPayment.getId(), Status.PAID, mockNowDate);

    // WHEN
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(updatedOrg, dateOf(weekend));

    // THEN expect the org payment status to return to OK; Because SALES_SOLD_WARNING -> OK is
    // allowed on weekend
    updatedOrg = _organizationDao.findById(orgId);
    assertEquals(updatedOrg.getPaymentStatus().getStatus(), Type.OK);
  }

  @Test
  public void reactivateOrganizationIfPaid() throws IOException {
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/InvoiceDao/invoices.json.ftl",
        null,
        Invoice.DB_NAME,
        Invoice.COLLECTION_NAME);
    Organization org = _organizationDao.findById(oid(205));
    // Pass in an arbitrary audit info
    _delinquentOrganizationSvc.reactivateOrganizationIfPaid(org, AuditInfoHelpers.fromSystem());
    Organization orgWithFailedPayment = _organizationDao.findById(oid(205));
    assertEquals(
        OrgPaymentStatus.Type.SUSPENDED, orgWithFailedPayment.getPaymentStatus().getStatus());

    // Forgive payment belonging to invoice with status CLOSED. Should not reactivate yet
    _paymentDao.forgivePayment(oid(9), new Date());
    Organization orgWithForgivenPaymentInClosedInvoice = _organizationDao.findById(oid(205));
    assertEquals(
        OrgPaymentStatus.Type.SUSPENDED,
        orgWithForgivenPaymentInClosedInvoice.getPaymentStatus().getStatus());

    // Forgive payment belonging to invoice with status CHARGE_FAILED. Should reactivate now
    _paymentDao.forgivePayment(oid(3), new Date());
    // Pass in an arbitrary audit info
    _delinquentOrganizationSvc.reactivateOrganizationIfPaid(org, AuditInfoHelpers.fromSystem());
    Organization orgWithForgivenPayment = _organizationDao.findById(oid(205));
    assertEquals(OrgPaymentStatus.Type.OK, orgWithForgivenPayment.getPaymentStatus().getStatus());
  }

  @Test
  public void demotePlanForLockedOrganization_ndsPro() throws SvcException, IOException {
    Date now = new Date();
    Date orgCreationDate = DateUtils.addDays(now, -455);
    Date currentPlanStartDate = DateUtils.addDays(now, -90);
    Date currentPlanEndDate = DateUtils.addDays(now, 275);

    PrepaidPlan pastPrepaidDeveloperPlan =
        new PrepaidPlan.Builder(PrepaidPlanType.NDS_DEVELOPER, List.of())
            .startDate(orgCreationDate)
            .endDate(currentPlanStartDate)
            .build();

    PrepaidPlan currentPrepaidDeveloperPlan =
        new PrepaidPlan.Builder(PrepaidPlanType.NDS_PRO, List.of())
            .startDate(currentPlanStartDate)
            .endDate(currentPlanEndDate)
            .build();

    ObjectId orgId = new ObjectId();
    OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(orgId)
            .planType(PlanType.NDS_PRO)
            .startDate(currentPlanStartDate)
            .endDate(currentPlanEndDate)
            .build();

    OrgPaymentStatus orgPaymentStatus = OrgPaymentStatus.ok(DateUtils.addDays(now, -30));
    Organization org =
        new Organization.Builder()
            .id(orgId)
            .created(DateUtils.addDays(now, -455))
            .name("locked nds org")
            .paymentStatus(orgPaymentStatus)
            .build();
    OrgPrepaidPlan newPrepaidPlan =
        new OrgPrepaidPlan.Builder()
            .orgId(orgId)
            .prepaidPlans(List.of(pastPrepaidDeveloperPlan, currentPrepaidDeveloperPlan))
            .build();

    _planSvc.initPlan(org.getId(), plan, newPrepaidPlan.getPrepaidPlans());
    _organizationDao.save(org);

    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addDays(now, 1));

    assertEquals(
        PlanType.NDS_PRO,
        _planSvc.getCurrentPlan(org.getId(), DateUtils.addDays(now, 1)).getPlanType());

    Date lockedDate =
        DateUtils.addDays(now, (int) -PaymentSvc.LOCKED_OUT_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForLockedOutOrg =
        new Payment.Builder()
            .orgId(org.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(lockedDate)
            .updated(lockedDate)
            .build();
    _paymentDao.save(paymentForLockedOutOrg);

    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, now);

    assertEquals(
        OrgPaymentStatus.Type.LOCKED,
        _organizationSvc.findById(org.getId()).getPaymentStatus().getStatus());

    assertEquals(
        PlanType.NDS_PRO,
        _planSvc.getCurrentPlan(org.getId(), now).getPlanType(),
        "Plan today should still be NDS_PRO");

    // Plan starting from tomorrow was demoted to NDS
    for (int i = 1; i < 3; i++) {
      assertEquals(
          PlanType.NDS,
          _planSvc.getCurrentPlan(org.getId(), DateUtils.addDays(now, i)).getPlanType());
    }

    // run daily billing again and make sure plan was not upgraded back
    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addDays(now, 2));
    assertEquals(
        PlanType.NDS_PRO,
        _planSvc.getCurrentPlan(org.getId(), now).getPlanType(),
        "Plan today is still NDS_PRO");

    // Plan starting from tomorrow is still NDS
    for (int i = 1; i < 3; i++) {
      OrgPlan planForDate = _planSvc.getCurrentPlan(org.getId(), DateUtils.addDays(now, i));
      assertEquals(PlanType.NDS, planForDate.getPlanType(), "Plan should be demoted to NDS");
      assertEquals(
          DateUtils.addDays(TimeUtils.getStartOfDayAsDate(now), 1),
          planForDate.getStartDate(),
          "Plan start date should be set for tomorrow start of day");
    }
  }

  @Test
  public void demotePlanForLockedOrganization_endOfMonth_ndsPro() {
    doTestPlanDemotionAtEndOfMonth(PlanType.NDS_PRO, PrepaidPlanType.NDS_PRO);
  }

  @Test
  public void demotePlanForLockedOrganization_endOfMonth_ndsDeveloper() {
    doTestPlanDemotionAtEndOfMonth(PlanType.NDS_DEVELOPER, PrepaidPlanType.NDS_DEVELOPER);
  }

  public void doTestPlanDemotionAtEndOfMonth(PlanType planType, PrepaidPlanType prepaidPlanType) {
    Date nov29 = TimeUtils.fromISOString("2021-11-29");
    Date nov30 = TimeUtils.fromISOString("2021-11-30");
    Date dec1 = TimeUtils.fromISOString("2021-12-01");

    Date orgCreationDate = DateUtils.addDays(nov29, -455);
    Date currentPlanEndDate = DateUtils.addDays(nov29, 275);

    PrepaidPlan prepaidPlan =
        new PrepaidPlan.Builder(prepaidPlanType, List.of()).startDate(orgCreationDate).build();

    ObjectId orgId = ObjectId.get();

    OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(orgId)
            .planType(planType)
            .startDate(orgCreationDate)
            .endDate(currentPlanEndDate)
            .build();

    Organization.Builder orgBuilder =
        new Organization.Builder()
            .id(orgId)
            .created(orgCreationDate)
            .name("locked nds org")
            .paymentStatus(OrgPaymentStatus.ok(DateUtils.addDays(nov29, -30)))
            .groupType(GroupType.NDS);

    Organization org = _orgFactory.createAtlasOrgWithPlan(orgBuilder, plan, List.of(prepaidPlan));
    _orgFactory.createNdsGroup(org, orgCreationDate);

    /**
     * credit needed so that sales-sold plans are not downgraded back by {@link
     * AccountantSvc#updatePlan()}, see {@link PlanSvc#getActivePrepaidPlan()} method that returns
     * null plan if credit is not found.
     */
    if (prepaidPlanType.isSalesSold()) {
      _creditFactory.createPrepaidCredit(
          org.getId(), orgCreationDate, currentPlanEndDate, 1000, true);
    }

    _accountantSvc.processOrganizations(List.of(org.getId()), nov29);

    // mock daily scheduleOrgPaymentStatusUpdate job is run to update org payment status
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, nov29);

    Function<Date, PlanType> getPlanTypeForDate =
        (Date date) -> _planSvc.getCurrentPlan(org.getId(), date).getPlanType();

    assertEquals(planType, getPlanTypeForDate.apply(nov29), "Existing plan is NDS Developer");

    Date lockedDate =
        DateUtils.addDays(nov29, (int) -PaymentSvc.LOCKED_OUT_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForLockedOutOrg =
        new Payment.Builder()
            .orgId(org.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(lockedDate)
            .updated(lockedDate)
            .build();
    _paymentDao.save(paymentForLockedOutOrg);

    /**
     * kick off daily billing - this tests actually test functionality in {@link
     * DelinquentOrganizationSvc#demotePlanForLockedOrganization} method, however we are also
     * testing that {@link AccountantSvc#updatePlan} doesn't undo the plan demotion.
     */
    _accountantSvc.processOrganizations(List.of(org.getId()), nov30);
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, nov30);

    assertEquals(
        OrgPaymentStatus.Type.LOCKED,
        _organizationSvc.findById(org.getId()).getPaymentStatus().getStatus(),
        "Organization status was automatically updated to LOCKED");

    assertEquals(
        planType, getPlanTypeForDate.apply(nov29), "Plan for yesterday remained NDS_DEVELOPER");

    assertEquals(
        planType, getPlanTypeForDate.apply(nov30), "Plan for today remained NDS_DEVELOPER");

    for (int i = 1; i < 6; i++) {
      assertEquals(
          PlanType.NDS,
          getPlanTypeForDate.apply(DateUtils.addDays(nov30, i)),
          "Plan starting from tomorrow was demoted to NDS");
    }

    // kick off daily billy again to ensure the old plan is not being brought back from
    // prepaidPlan collection
    _accountantSvc.processOrganizations(List.of(org.getId()), dec1);
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, dec1);

    assertEquals(
        OrgPaymentStatus.Type.LOCKED,
        _organizationSvc.findById(org.getId()).getPaymentStatus().getStatus(),
        "Organization remained locked after invoice is closed on Dec 1");

    assertEquals(
        planType, getPlanTypeForDate.apply(nov29), "Plan for yesterday remained NDS_DEVELOPER");

    assertEquals(
        planType, getPlanTypeForDate.apply(nov30), "Plan for today remained NDS_DEVELOPER");

    Date expectedNewPlanStartDate = TimeUtils.getStartOfDayAsDate(dec1);

    assertEquals(
        expectedNewPlanStartDate,
        _planSvc.getCurrentPlan(org.getId(), nov30).getEndDate(),
        "Current plan end date is set for tomorrow");

    assertEquals(
        expectedNewPlanStartDate,
        _planSvc.getCurrentPlan(org.getId(), dec1).getStartDate(),
        "New plan start date is set for tomorrow");

    for (int i = 1; i < 6; i++) {
      Date currentDate = DateUtils.addDays(nov30, i);
      assertEquals(
          PlanType.NDS,
          getPlanTypeForDate.apply(currentDate),
          "Plan demotion is persistent after the next run of the daily billing");
    }
  }

  @Test
  public void demotePlanForLockedOrganization_cloudPremium() throws SvcException, IOException {
    Date now = new Date();
    Date currentPlanStartDate = DateUtils.addDays(now, -90);
    Date currentPlanEndDate = DateUtils.addDays(now, 275);
    Date orgCreationDate = DateUtils.addDays(now, -455);

    PrepaidPlan pastPrepaidPremiumPlan =
        new PrepaidPlan.Builder(PrepaidPlanType.TRIAL_PERIOD, List.of())
            .startDate(orgCreationDate)
            .endDate(currentPlanStartDate)
            .build();
    PrepaidPlan currentPrepaidPremiumPlan =
        new PrepaidPlan.Builder(PrepaidPlanType.PREPAID_BUNDLED, List.of())
            .startDate(currentPlanStartDate)
            .endDate(currentPlanEndDate)
            .build();

    ObjectId orgId = new ObjectId();
    OrgPlan plan =
        new OrgPlan.Builder()
            .orgId(orgId)
            .planType(PlanType.PREMIUM)
            .startDate(currentPlanStartDate)
            .endDate(currentPlanEndDate)
            .build();

    OrgPaymentStatus orgPaymentStatus = OrgPaymentStatus.ok(DateUtils.addDays(now, -30));
    Organization org =
        new Organization.Builder()
            .id(orgId)
            .created(orgCreationDate)
            .name("locked cloud org")
            .paymentStatus(orgPaymentStatus)
            .build();
    _planSvc.initPlan(
        org.getId(), plan, List.of(pastPrepaidPremiumPlan, currentPrepaidPremiumPlan));
    _organizationDao.save(org);

    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addDays(now, 1));

    assertEquals(
        PlanType.PREMIUM,
        _planSvc.getCurrentPlan(org.getId(), DateUtils.addDays(now, 1)).getPlanType());

    Date lockedDate =
        DateUtils.addDays(now, (int) -PaymentSvc.LOCKED_OUT_DELINQUENCY_DURATION.toDays() - 1);
    Payment paymentForLockedOutOrg =
        new Payment.Builder()
            .orgId(org.getId())
            .amountBilledCents(2000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(lockedDate)
            .updated(lockedDate)
            .build();
    _paymentDao.save(paymentForLockedOutOrg);

    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, now);

    assertEquals(
        OrgPaymentStatus.Type.LOCKED,
        _organizationSvc.findById(org.getId()).getPaymentStatus().getStatus());

    assertEquals(
        PlanType.FREE_TIER, _planSvc.getCurrentPlan(org.getId(), new Date()).getPlanType());

    _accountantSvc.processOrganizations(List.of(org.getId()), DateUtils.addDays(now, 2));

    assertEquals(
        PlanType.FREE_TIER,
        _planSvc.getCurrentPlan(org.getId(), DateUtils.addDays(now, 2)).getPlanType());

    assertEquals(
        PlanType.FREE_TIER,
        _planSvc.getCurrentPlan(org.getId(), DateUtils.addDays(now, 3)).getPlanType());
  }

  @Test
  public void reactivateOrganizationIfPaid_failedPayment_closedInvoiceStatus() {
    Date invoiceStartDate = TimeUtils2.fromISOString("2021-11-01T00:00:00Z");
    Date invoiceEndDate = TimeUtils2.fromISOString("2021-11-30T00:00:00Z");

    Organization org = _organizationDao.findById(oid(238));
    Date suspendedDate =
        DateUtils.addDays(
            invoiceStartDate, (int) -PaymentSvc.SUSPENDED_DELINQUENCY_DURATION.toDays() - 1);
    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(invoiceStartDate)
                .endDate(invoiceEndDate)
                .organizationId(org.getId())
                .creditAmountCents(100)
                .usageAmountCents(100)
                .supportAmountCents(0)
                .invoiceStatus(Invoice.Status.CLOSED)
                .build());
    Payment paymentForSuspendedOrg =
        new Payment.Builder()
            .orgId(org.getId())
            .amountBilledCents(1000L)
            .status(Payment.Status.FAILED)
            .paymentDueDate(suspendedDate)
            .updated(suspendedDate)
            .invoiceId(invoice.getId()) // invoice with CLOSED status
            .build();
    // Save a failed payment
    _paymentDao.save(paymentForSuspendedOrg);

    // Update org payment status to Suspended
    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, invoiceStartDate);
    Organization orgWithFailedPayment = _organizationDao.findById(oid(238));
    // verify org payment status is suspended
    assertEquals(
        OrgPaymentStatus.Type.SUSPENDED, orgWithFailedPayment.getPaymentStatus().getStatus());
    // Should reactivate because payment belongs to invoice with status closed.
    _delinquentOrganizationSvc.reactivateOrganizationIfPaid(
        orgWithFailedPayment, AuditInfoHelpers.fromSystem());
    Organization orgShouldBeReactivated = _organizationDao.findById(oid(238));
    // Verify status is OK again.
    assertEquals(OrgPaymentStatus.Type.OK, orgShouldBeReactivated.getPaymentStatus().getStatus());
  }

  @Test
  public void testUpdatePaymentStatusForOrganization_updatesStatusForLinkedOrgs() {
    Date now = new Date();
    // Skip test if not business days, as org suspension only happen on business days
    if (!businessDayChecker.isBusinessDay(now)) {
      return;
    }
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date suspensionDate = DelinquentOrganizationSvc.getSuspendedDate(now);
    Date dayBeforeSuspensionDate = DateUtils.addDays(suspensionDate, -1);
    Date lockedDate = DelinquentOrganizationSvc.getLockedOutDate(now);
    Date dayBeforeLockedDate = DateUtils.addDays(lockedDate, -1);

    PayingOrg payingOrg = payingOrgFactory.createPayingOrgWithLinkedOrgs(2);
    Organization payingOrgOrg = _organizationSvc.findById(payingOrg.getPayingOrgId());
    Organization linkedOrg1 = _organizationSvc.findById(payingOrg.getLinkedOrgIds().get(0));
    Organization linkedOrg2 = _organizationSvc.findById(payingOrg.getLinkedOrgIds().get(1));

    Invoice payingOrgInvoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(startOfMonth)
                .endDate(startOfNextMonth)
                .organizationId(payingOrgOrg.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Invoice.Status.CLOSED)
                .build());
    Invoice linkedOrg1Invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(startOfMonth)
                .endDate(startOfNextMonth)
                .organizationId(linkedOrg1.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Invoice.Status.CLOSED)
                .build());
    Invoice linkedOrg2Invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(startOfMonth)
                .endDate(startOfNextMonth)
                .organizationId(linkedOrg2.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Invoice.Status.CLOSED)
                .build());

    // Create a PAID paying org payment
    paymentFactory.createPayment(
        payingOrgInvoice,
        PaymentMethodType.CREDIT_CARD,
        100,
        Status.PAID,
        BillingAccount.MONGODB_INC,
        null,
        suspensionDate);
    _paymentDao.updateDueDateForOrganization(payingOrgOrg.getId(), suspensionDate);

    // Create FAILED linked org payment due day before suspension date
    // This would cause all the orgs to become SUSPENDED
    Payment linkedOrg1Payment =
        paymentFactory.createPayment(
            linkedOrg1Invoice,
            PaymentMethodType.CREDIT_CARD,
            200,
            Status.FAILED,
            BillingAccount.MONGODB_INC,
            null,
            dayBeforeSuspensionDate);
    _paymentDao.updateDueDateForOrganization(linkedOrg1.getId(), dayBeforeSuspensionDate);

    // Create FAILED linked org payment due day before suspension date
    // This will cause all the orgs to become LOCKED since this payment is older than SUSPENDED
    Payment linkedOrg2Payment =
        paymentFactory.createPayment(
            linkedOrg2Invoice,
            PaymentMethodType.CREDIT_CARD,
            300,
            Status.FAILED,
            BillingAccount.MONGODB_INC,
            null,
            dayBeforeLockedDate);
    _paymentDao.updateDueDateForOrganization(linkedOrg2.getId(), dayBeforeLockedDate);

    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(payingOrgOrg, now);

    Organization updatedPayingOrg = _organizationSvc.findById(payingOrgOrg.getId());
    Organization updatedLinkedOrg1 = _organizationSvc.findById(linkedOrg1.getId());
    Organization updatedLinkedOrg2 = _organizationSvc.findById(linkedOrg2.getId());

    assertThat(updatedPayingOrg.getPaymentStatus().getStatus(), is(Type.LOCKED));
    assertThat(updatedLinkedOrg1.getPaymentStatus().getStatus(), is(Type.LOCKED));
    assertThat(updatedLinkedOrg2.getPaymentStatus().getStatus(), is(Type.LOCKED));

    // Now we will mark the failed locking payment as PAID
    // This should now use the other failed payment to mark the orgs as SUSPENDED
    _paymentDao.setPaymentStatus(linkedOrg2Payment.getId(), Status.PAID, now);

    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(updatedPayingOrg, now);

    updatedPayingOrg = _organizationSvc.findById(payingOrgOrg.getId());
    updatedLinkedOrg1 = _organizationSvc.findById(linkedOrg1.getId());
    updatedLinkedOrg2 = _organizationSvc.findById(linkedOrg2.getId());

    assertThat(updatedPayingOrg.getPaymentStatus().getStatus(), is(Type.SUSPENDED));
    assertThat(updatedLinkedOrg1.getPaymentStatus().getStatus(), is(Type.SUSPENDED));
    assertThat(updatedLinkedOrg2.getPaymentStatus().getStatus(), is(Type.SUSPENDED));

    // Now we will mark the failed suspended payment as PAID
    // There should now be no more FAILED payments, org statuses should be OK
    _paymentDao.setPaymentStatus(linkedOrg1Payment.getId(), Status.PAID, now);

    _delinquentOrganizationSvc.updatePaymentStatusForOrganization(updatedPayingOrg, now);

    updatedPayingOrg = _organizationSvc.findById(payingOrgOrg.getId());
    updatedLinkedOrg1 = _organizationSvc.findById(linkedOrg1.getId());
    updatedLinkedOrg2 = _organizationSvc.findById(linkedOrg2.getId());

    assertThat(updatedPayingOrg.getPaymentStatus().getStatus(), is(Type.OK));
    assertThat(updatedLinkedOrg1.getPaymentStatus().getStatus(), is(Type.OK));
    assertThat(updatedLinkedOrg2.getPaymentStatus().getStatus(), is(Type.OK));
  }

  @Test
  public void testUpdateDueDateForOrganization() {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date suspensionDate = DelinquentOrganizationSvc.getSuspendedDate(now);

    Organization organization = organizationFactory.createAtlasOrganization(now);
    Organization organization2 = organizationFactory.createAtlasOrganization(now);
    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(startOfMonth)
                .endDate(startOfNextMonth)
                .organizationId(organization.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Invoice.Status.CLOSED)
                .build());

    // set up payments on a different org, to be tested in second half of test.
    Payment processingPaymentFailed2 =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.PROCESSING)
            .paymentDueDate(suspensionDate)
            .orgId(organization2.getId())
            .invoiceId(new ObjectId())
            .failedChargeAttempts(
                FailedChargeAttempts.builder().lastAttemptedCharge(now).attempts(1).build())
            .build();

    Payment failedPayment2 =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.FAILED)
            .paymentDueDate(suspensionDate)
            .orgId(organization2.getId())
            .invoiceId(new ObjectId())
            .build();

    Payment paymentPaid2 =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.PAID)
            .paymentDueDate(suspensionDate)
            .orgId(organization2.getId())
            .invoiceId(new ObjectId())
            .build();

    Payment processingPayment2 =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.PROCESSING)
            .paymentDueDate(suspensionDate)
            .orgId(organization2.getId())
            .invoiceId(new ObjectId())
            .build();

    _paymentDao.save(processingPaymentFailed2);
    _paymentDao.save(processingPayment2);
    _paymentDao.save(failedPayment2);
    _paymentDao.save(paymentPaid2);

    Date newSuspensionDate = DateUtils.addYears(now, 1);
    Date newDueDate =
        Date.from(newSuspensionDate.toInstant().minus(PaymentSvc.SUSPENDED_DELINQUENCY_DURATION));
    // Create a PAID paying org payment
    Payment paymentPaid =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.PAID)
            .paymentDueDate(suspensionDate)
            .orgId(organization.getId())
            .invoiceId(invoice.getId())
            .build();

    _paymentDao.save(paymentPaid);

    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization, newSuspensionDate, AuditInfoHelpers.fromSystem());

    Payment updatedPaymentPaid = _paymentDao.findById(paymentPaid.getId());

    assertEquals(suspensionDate, updatedPaymentPaid.getPaymentDueDate());

    // Create a FAILED paying org payment
    Payment failedPayment =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.FAILED)
            .paymentDueDate(suspensionDate)
            .orgId(organization.getId())
            .invoiceId(invoice.getId())
            .build();

    _paymentDao.save(failedPayment);

    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization, newSuspensionDate, AuditInfoHelpers.fromSystem());

    Payment updatedPaymentFailed = _paymentDao.findById(failedPayment.getId());
    updatedPaymentPaid = _paymentDao.findById(paymentPaid.getId());

    assertEquals(suspensionDate, updatedPaymentPaid.getPaymentDueDate());
    assertEquals(newDueDate, updatedPaymentFailed.getPaymentDueDate());

    // Create a PROCESSING paying org payment
    Payment processingPayment =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.PROCESSING)
            .paymentDueDate(suspensionDate)
            .orgId(organization.getId())
            .invoiceId(invoice.getId())
            .build();

    _paymentDao.save(processingPayment);

    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization, newSuspensionDate, AuditInfoHelpers.fromSystem());

    Payment updatedPaymentProcessing = _paymentDao.findById(processingPayment.getId());
    updatedPaymentFailed = _paymentDao.findById(failedPayment.getId());
    updatedPaymentPaid = _paymentDao.findById(paymentPaid.getId());

    assertEquals(suspensionDate, updatedPaymentProcessing.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentPaid.getPaymentDueDate());
    assertEquals(newDueDate, updatedPaymentFailed.getPaymentDueDate());

    // Create a PROCESSING paying org payment with failed attempt
    Payment processingPaymentFailed =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.PROCESSING)
            .paymentDueDate(suspensionDate)
            .orgId(organization.getId())
            .invoiceId(invoice.getId())
            .failedChargeAttempts(
                FailedChargeAttempts.builder().attempts(1).lastAttemptedCharge(now).build())
            .build();

    _paymentDao.save(processingPaymentFailed);

    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization, newSuspensionDate, AuditInfoHelpers.fromSystem());

    Payment updatedProcessingPaymentFailed = _paymentDao.findById(processingPaymentFailed.getId());
    updatedPaymentProcessing = _paymentDao.findById(processingPayment.getId());
    updatedPaymentFailed = _paymentDao.findById(failedPayment.getId());
    updatedPaymentPaid = _paymentDao.findById(paymentPaid.getId());

    // verify org 1's payments were all updated
    assertEquals(newDueDate, updatedProcessingPaymentFailed.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentProcessing.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentPaid.getPaymentDueDate());
    assertEquals(newDueDate, updatedPaymentFailed.getPaymentDueDate());

    Payment updatedProcessingPaymentFailed2 =
        _paymentDao.findById(processingPaymentFailed2.getId());
    Payment updatedPaymentProcessing2 = _paymentDao.findById(processingPayment2.getId());
    Payment updatedPaymentFailed2 = _paymentDao.findById(failedPayment2.getId());
    Payment updatedPaymentPaid2 = _paymentDao.findById(paymentPaid2.getId());

    // verify org 2's payments were not touched
    assertEquals(suspensionDate, updatedProcessingPaymentFailed2.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentProcessing2.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentPaid2.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentFailed2.getPaymentDueDate());

    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization2, newSuspensionDate, AuditInfoHelpers.fromSystem());

    // verify org 2's payments were all updated
    updatedProcessingPaymentFailed2 = _paymentDao.findById(processingPaymentFailed2.getId());
    updatedPaymentProcessing2 = _paymentDao.findById(processingPayment2.getId());
    updatedPaymentFailed2 = _paymentDao.findById(failedPayment2.getId());
    updatedPaymentPaid2 = _paymentDao.findById(paymentPaid2.getId());

    assertEquals(newDueDate, updatedProcessingPaymentFailed2.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentProcessing2.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentPaid2.getPaymentDueDate());
    assertEquals(newDueDate, updatedPaymentFailed2.getPaymentDueDate());

    // verify org 1's payments haven't changed
    updatedProcessingPaymentFailed = _paymentDao.findById(processingPaymentFailed.getId());
    updatedPaymentProcessing = _paymentDao.findById(processingPayment.getId());
    updatedPaymentFailed = _paymentDao.findById(failedPayment.getId());
    updatedPaymentPaid = _paymentDao.findById(paymentPaid.getId());

    assertEquals(newDueDate, updatedProcessingPaymentFailed.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentProcessing.getPaymentDueDate());
    assertEquals(suspensionDate, updatedPaymentPaid.getPaymentDueDate());
    assertEquals(newDueDate, updatedPaymentFailed.getPaymentDueDate());
  }

  @Test
  public void testUpdateDueDateForOrganization_sameSuspensionDate() {
    Date now = new Date();
    Date startOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date startOfNextMonth = DateUtils.addMonths(startOfMonth, 1);
    Date suspensionDate = DelinquentOrganizationSvc.getSuspendedDate(now);

    Organization organization = organizationFactory.createAtlasOrganization(now);
    Invoice invoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(startOfMonth)
                .endDate(startOfNextMonth)
                .organizationId(organization.getId())
                .creditAmountCents(0)
                .usageAmountCents(0)
                .supportAmountCents(0)
                .invoiceStatus(Invoice.Status.CLOSED)
                .build());

    Date newSuspensionDate = DateUtils.addYears(now, 1);
    Date newDueDate =
        Date.from(newSuspensionDate.toInstant().minus(PaymentSvc.SUSPENDED_DELINQUENCY_DURATION));

    Payment payment =
        new Payment.Builder()
            .id(new ObjectId())
            .status(Status.FAILED)
            .paymentDueDate(suspensionDate)
            .orgId(organization.getId())
            .invoiceId(invoice.getId())
            .build();

    _paymentDao.save(payment);

    // When: change suspension date
    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization, newSuspensionDate, AuditInfoHelpers.fromSystem());

    // Then: dueDate was updated and audit was created
    Payment updatedPayment = _paymentDao.findById(payment.getId());
    assertEquals(newDueDate, updatedPayment.getPaymentDueDate());

    List<Event> events = _auditSvc.findByDate(OrgAudit.Type.ORG_SUSPENSION_DATE_CHANGED, now);
    assertEquals(1, events.size());
    OrgAudit audit = (OrgAudit) events.get(0);
    assertEquals(newSuspensionDate, audit.getSuspensionDate());

    // When: change suspension date with the same date
    _delinquentOrganizationSvc.activateDelinquentOrgAndLinkedOrgs(
        organization, newSuspensionDate, AuditInfoHelpers.fromSystem());

    // Then: audit was not created
    updatedPayment = _paymentDao.findById(payment.getId());
    assertEquals(newDueDate, updatedPayment.getPaymentDueDate());

    events = _auditSvc.findByDate(OrgAudit.Type.ORG_SUSPENSION_DATE_CHANGED, now);
    assertEquals(1, events.size());
  }

  @Test
  public void test_adminSuspend() {

    Organization org = organizationFactory.createDeadAtlasOrganization(new Date());
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    LocalDate now = LocalDate.now();

    assertThat(_delinquentOrganizationSvc.adminSuspend(org, auditInfo, now), is(true));

    Organization updated = _organizationDao.findById(org.getId());
    assertThat(updated.getPaymentStatus().getStatus(), is(Type.SUSPENDED));
    assertThat(updated.getPaymentStatus().isAdminSuspended(), is(true));
  }

  @Test
  public void test_adminLock() {

    Organization org = organizationFactory.createDeadAtlasOrganization(new Date());
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    LocalDate now = LocalDate.now();

    assertThat(_delinquentOrganizationSvc.adminLock(org, auditInfo, now), is(true));

    Organization updated = _organizationDao.findById(org.getId());
    assertThat(updated.getPaymentStatus().getStatus(), is(Type.LOCKED));
    assertThat(updated.getPaymentStatus().isAdminLocked(), is(true));
  }

  @Test
  public void testScheduleOrgPaymentStatusUpdateBatchJob() {
    Date now = new Date();
    Date suspensionDate = DelinquentOrganizationSvc.getSuspendedDate(now);
    Date dayBeforeSuspensionDate = DateUtils.addDays(suspensionDate, -1);
    Date lockedDate = DelinquentOrganizationSvc.getLockedOutDate(now);
    Date dayBeforeLockedDate = DateUtils.addDays(lockedDate, -1);

    // GIVEN a mock org with a FAILED payment older than 30 days,
    // which should cause the org status moving from WARNING to SUSPENDED.
    Organization orgWillBeSuspended = _orgFactory.createWarningOrganization(now);
    paymentFactory.createPaymentWithPaymentDueDate(
        PaymentMethodType.CREDIT_CARD,
        orgWillBeSuspended.getId(),
        100,
        Status.FAILED,
        BillingAccount.MONGODB_INC,
        dayBeforeSuspensionDate);

    // GIVEN a mock org with a FAILED payment older than 60 days,
    // which should cause the org to be locked
    Organization orgWillBeLocked = _orgFactory.createAtlasOrganization(now);
    paymentFactory.createPaymentWithPaymentDueDate(
        PaymentMethodType.CREDIT_CARD,
        orgWillBeLocked.getId(),
        100,
        Status.FAILED,
        BillingAccount.MONGODB_INC,
        dayBeforeLockedDate);

    // GIVEN a mock org in suspended status but without any failed payment now,
    // which should change the org to be ok
    Organization orgWillBeOk = _orgFactory.createSuspendedOrganization(now);

    // GIVEN an embargoed org with a FAILED payment older than 60 days
    Organization embargoedOrgWithFailedPayments = _orgFactory.createEmbargoedOrganization(now);
    paymentFactory.createPaymentWithPaymentDueDate(
        PaymentMethodType.CREDIT_CARD,
        embargoedOrgWithFailedPayments.getId(),
        1500,
        Status.FAILED,
        BillingAccount.MONGODB_INC,
        dayBeforeLockedDate);

    // GIVEN a deleted org with a FAILED payment older than 60 days
    Organization orgDeleted =
        new Organization.Builder()
            .id(new ObjectId())
            .name("orgDeleted")
            .paymentStatus(OrgPaymentStatus.ok(now))
            .deleted(now)
            .build();
    _organizationDao.save(orgDeleted);
    paymentFactory.createPaymentWithPaymentDueDate(
        PaymentMethodType.CREDIT_CARD,
        orgDeleted.getId(),
        2000,
        Status.FAILED,
        BillingAccount.MONGODB_INC,
        dayBeforeLockedDate);

    // WHEN schedule the batch job to update org payment status
    _delinquentOrganizationSvc.scheduleOrgPaymentStatusUpdate();
    jobQueueTestUtils.processAllJobsInQueue();

    // THEN expected the org payment status are updated accordingly,
    // notification emails are sent to delinquent orgs
    Organization updatedOrgWillBeSuspended = _organizationSvc.findById(orgWillBeSuspended.getId());
    if (businessDayChecker.isBusinessDay(now)) { // org suspension only happen on business days
      assertThat(updatedOrgWillBeSuspended.getPaymentStatus().getStatus(), is(Type.SUSPENDED));
      assertTrue(
          DateUtils.isSameDay(
              updatedOrgWillBeSuspended.getPaymentStatus().getLastContacted(), now));
    } else {
      assertThat(updatedOrgWillBeSuspended.getPaymentStatus().getStatus(), is(Type.WARNING));
      assertNull(updatedOrgWillBeSuspended.getPaymentStatus().getLastContacted());
    }

    Organization updatedOrgWillBeLocked = _organizationSvc.findById(orgWillBeLocked.getId());
    assertThat(updatedOrgWillBeLocked.getPaymentStatus().getStatus(), is(Type.LOCKED));
    assertTrue(
        DateUtils.isSameDay(updatedOrgWillBeLocked.getPaymentStatus().getLastContacted(), now));

    Organization updatedOrgWillBeOk = _organizationSvc.findById(orgWillBeOk.getId());
    assertThat(updatedOrgWillBeOk.getPaymentStatus().getStatus(), is(Type.OK));
    assertNull(updatedOrgWillBeOk.getPaymentStatus().getLastContacted());

    // THEN verify the org payment status of embargoed org remains EMBARGOED
    Organization updatedEmbargoedOrg =
        _organizationSvc.findById(embargoedOrgWithFailedPayments.getId());
    assertThat(updatedEmbargoedOrg.getPaymentStatus().getStatus(), is(Type.EMBARGOED));
    assertNull(updatedEmbargoedOrg.getPaymentStatus().getLastContacted());

    // THEN verify the org payment status of deleted org remains unchanged
    Organization updatedDeletedOrg = _organizationSvc.findById(orgDeleted.getId());
    assertThat(
        updatedDeletedOrg.getPaymentStatus().getStatus(),
        is(orgDeleted.getPaymentStatus().getStatus()));
    assertNull(updatedDeletedOrg.getPaymentStatus().getLastContacted());
  }

  /*
   ORG activated grace period in WARNING, 5 days before it went to SUSPENDED
   ORG still in grace period, should be in WARNING
  */
  @Test
  public void testDeterminePaymentStatus_initialStateWarning_CurrentlyInGracePeriod() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date thirtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 30);
    Date fiveDaysBeforeSuspensionDate = DateUtils.addDays(thirtyDaysAfterFirstOfMonth, -5);
    Date twoDaysAfterGracePeriodActivated = DateUtils.addDays(fiveDaysBeforeSuspensionDate, 2);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeSuspensionDate,
            null,
            false,
            localDateOf(fiveDaysBeforeSuspensionDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, twoDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.WARNING);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(fiveDaysBeforeSuspensionDate));
  }

  /*
   ORG activated grace period in WARNING, 5 days before it went to SUSPENDED
   ORG has been in grace period for 8 days, should be in WARNING
  */
  @Test
  public void testDeterminePaymentStatus_initialStateWarning_CurrentlyInTheMiddleOfGracePeriod() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date thirtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 30);
    Date fiveDaysBeforeSuspensionDate = DateUtils.addDays(thirtyDaysAfterFirstOfMonth, -5);
    Date eightDaysAfterGracePeriodActivated = DateUtils.addDays(fiveDaysBeforeSuspensionDate, 8);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeSuspensionDate,
            null,
            false,
            localDateOf(fiveDaysBeforeSuspensionDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, eightDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.WARNING);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(fiveDaysBeforeSuspensionDate));
  }

  /*
   ORG activated grace period in WARNING, 5 days before it went to SUSPENDED
   Grace period is over but only three days have passed, so it should stay in its current state
   of WARNING
  */
  @Test
  public void
      testDeterminePaymentStatus_withGracePeriod_gracePeriodEndedButBacktoInitialStatus_Warning() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date thirtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 30);
    Date fiveDaysBeforeSuspensionDate = DateUtils.addDays(thirtyDaysAfterFirstOfMonth, -5);
    Date seventeenDaysAfterGracePeriodActivated =
        DateUtils.addDays(fiveDaysBeforeSuspensionDate, 17);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeSuspensionDate,
            null,
            false,
            localDateOf(fiveDaysBeforeSuspensionDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, seventeenDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.WARNING);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(fiveDaysBeforeSuspensionDate));
  }

  /*
   ORG activated grace period in WARNING, 5 days before it went to SUSPENDED
   Grace period is over and more than 5 days after have passed so org should be SUSPENDED
  */
  @Test
  public void testDeterminePaymentStatus_withGracePeriod_gracePeriodEnded_ShouldBeSuspended() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date thirtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 30);
    Date fiveDaysBeforeSuspensionDate = DateUtils.addDays(thirtyDaysAfterFirstOfMonth, -5);
    Date twentyDaysAfterGracePeriodActivated = DateUtils.addDays(fiveDaysBeforeSuspensionDate, 20);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeSuspensionDate,
            null,
            false,
            localDateOf(fiveDaysBeforeSuspensionDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, twentyDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.SUSPENDED);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(fiveDaysBeforeSuspensionDate));
  }

  /*
    ORG activated grace period in SUSPENDED 5 days before it went to LOCKED
    It's been 3 days since grace period started, so it's still be in grace period,
    org should be in WARNING
  */
  @Test
  public void testDeterminePaymentStatusSuspended_CurrentlyInGracePeriod() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date sixtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 30);
    Date fiveDaysBeforeLockedDate = DateUtils.addDays(sixtyDaysAfterFirstOfMonth, -5);
    Date threeDaysAfterGracePeriodActivated = DateUtils.addDays(fiveDaysBeforeLockedDate, 3);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeLockedDate,
            null,
            false,
            localDateOf(fiveDaysBeforeLockedDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, threeDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.WARNING);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(), localDateOf(fiveDaysBeforeLockedDate));
  }

  /*
   ORG activated grace period in SUSPENDED 5 days before it went to LOCKED
   Grace period ended but less than 5 days have passed since the grace period ended, so it should
   go back to it's previous state of SUSPENDED
  */
  @Test
  public void testDeterminePaymentStatusSuspended_TimeAfterGracePeriodNotOver_stillInWarning() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date sixtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 60);
    Date fiveDaysBeforeLockedDate = DateUtils.addDays(sixtyDaysAfterFirstOfMonth, -5);
    Date sixteenDaysAfterGracePeriodActivated = DateUtils.addDays(fiveDaysBeforeLockedDate, 16);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeLockedDate,
            null,
            false,
            localDateOf(fiveDaysBeforeLockedDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, sixteenDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.SUSPENDED);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(), localDateOf(fiveDaysBeforeLockedDate));
  }

  /*
   ORG activated grace period in SUSPENDED 5 days before it went to LOCKED
   It's been six days since grace period ended so it should change to the next state
   which is LOCKED
  */
  @Test
  public void testDeterminePaymentStatusSuspended_TimeAfterGracePeriodOver_ChangedToLocked() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date sixtyDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 60);
    Date fiveDaysBeforeSuspensionDate = DateUtils.addDays(sixtyDaysAfterFirstOfMonth, -5);
    Date twentyDaysAfterGracePeriodActivated = DateUtils.addDays(fiveDaysBeforeSuspensionDate, 20);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            fiveDaysBeforeSuspensionDate,
            null,
            false,
            localDateOf(fiveDaysBeforeSuspensionDate));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, twentyDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.LOCKED);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(fiveDaysBeforeSuspensionDate));
  }

  /*
   ORG activated grace period in LOCKED
   Grace period is active so should be in WARNING
  */
  @Test
  public void testDeterminePaymentStatusLocked_stillInGracePeriod() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date sixtyFiveDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 65);
    Date oneDayAfterGracePeriodActivated = DateUtils.addDays(sixtyFiveDaysAfterFirstOfMonth, 1);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.LOCKED,
            sixtyFiveDaysAfterFirstOfMonth,
            null,
            false,
            localDateOf(sixtyFiveDaysAfterFirstOfMonth));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(org, oneDayAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.WARNING);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(sixtyFiveDaysAfterFirstOfMonth));
  }

  /*
     ORG activated grace period in LOCKED
     It's been 1 days since grace period ended so it should return back to its previous state
     of LOCKED
  */
  @Test
  public void testDeterminePaymentStatusLockedAfterGracePeriodEnded() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date sixtyFiveDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 65);
    Date fifteenDaysAfterGracePeriodActivated =
        DateUtils.addDays(sixtyFiveDaysAfterFirstOfMonth, 20);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            sixtyFiveDaysAfterFirstOfMonth,
            null,
            false,
            localDateOf(sixtyFiveDaysAfterFirstOfMonth));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment failedPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Payment.Status.FAILED)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(failedPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, fifteenDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.LOCKED);
    assertEquals(
        updatedPaymentStatus.getGracePeriodActivatedDate(),
        localDateOf(sixtyFiveDaysAfterFirstOfMonth));
  }

  /*
     ORG activated grace period in LOCKED
     Payment has been successful so org's status should be OK and gracePeriod unset
  */
  @Test
  public void testSuccessfulPayment_unsetsGracePeriod() {
    ObjectId orgId = new ObjectId();
    Date now = new Date();
    Date firstOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date fifteenDaysBeforeFirstOfMonth = DateUtils.addDays(firstOfMonth, -15);
    Date sixtyFiveDaysAfterFirstOfMonth = DateUtils.addDays(firstOfMonth, 65);
    Date fifteenDaysAfterGracePeriodActivated =
        DateUtils.addDays(sixtyFiveDaysAfterFirstOfMonth, 20);

    OrgPaymentStatus paymentStatus =
        new OrgPaymentStatus(
            Type.WARNING,
            sixtyFiveDaysAfterFirstOfMonth,
            null,
            false,
            localDateOf(sixtyFiveDaysAfterFirstOfMonth));

    Organization org =
        new Organization.Builder().id(orgId).name("Test").paymentStatus(paymentStatus).build();
    _organizationDao.save(org);

    Payment succesfulPayment =
        new Payment.Builder()
            .orgId(org.getId())
            .status(Status.PAID)
            .paymentDueDate(firstOfMonth)
            .created(fifteenDaysBeforeFirstOfMonth)
            .build();
    _paymentDao.save(succesfulPayment);

    OrgPaymentStatus updatedPaymentStatus =
        _delinquentOrganizationSvc.determineOrgPaymentStatus(
            org, fifteenDaysAfterGracePeriodActivated);
    assertEquals(updatedPaymentStatus.getStatus(), Type.OK);
    assertNull(updatedPaymentStatus.getGracePeriodActivatedDate());
  }

  private void saveFailedChargeAttempts(
      ObjectId paymentId, int attemptsCount, Date lastFailedCharge) {
    Payment payment = _paymentDao.findById(paymentId);
    save(
        payment.toBuilder()
            .failedChargeAttempts(
                FailedChargeAttempts.builder()
                    .lastAttemptedCharge(lastFailedCharge)
                    .attempts(attemptsCount)
                    .build())
            .build());
  }
}
