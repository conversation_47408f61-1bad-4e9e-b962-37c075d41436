package com.xgen.svc.mms.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSPrivateLinkConnectionDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkConnection;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSTenantEndpoint;
import com.xgen.cloud.nds.azure._private.dao.AzurePrivateLinkConnectionDao;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateEndpoint;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateLinkConnection;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpoint;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectRegionGroupDao;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectionDao;
import com.xgen.cloud.nds.gcp._public.model.GCPPrivateServiceConnection;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectRegionGroup;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionUpdatesDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterProvisionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkEndpointIdEntry;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkSettings;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.spothealthcheck.util.NDSClusterSpotHealthCheckTestUtil;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.svc.billing.DelinquentOrganizationSvc;
import com.xgen.svc.mms.util.billing.testFactories.PaymentFactory;
import com.xgen.svc.nds.CloudProviderContainerTestUtils;
import com.xgen.svc.nds.aws.svc.AWSTenantEndpointSvc;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestServerlessClusterDescriptionConfig;
import com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class AdminOrgSvcIntTests extends BaseSvcTest {

  @Inject private AdminOrgSvc adminOrgSvc;
  @Inject private NDSGroupSvc ndsGroupSvc;
  @Inject private AWSTenantEndpointSvc awsTenantEndpointSvc;
  @Inject private NDSTenantEndpointSvc ndsTenantEndpointSvc;
  @Inject private DelinquentOrganizationSvc delinquentOrganizationSvc;
  @Inject private OrganizationDao organizationDao;
  @Inject private NDSGroupDao ndsGroupDao;
  @Inject private ClusterDescriptionDao clusterDescriptionDao;
  @Inject private ClusterDescriptionUpdatesDao clusterDescriptionUpdatesDao;
  @Inject private AWSPrivateLinkConnectionDao awsPrivateLinkDao;
  @Inject private AzurePrivateLinkConnectionDao azurePrivateLinkDao;
  @Inject private GCPPrivateServiceConnectRegionGroupDao gcpPrivateServiceConnectRegionGroupDao;
  @Inject private GCPPrivateServiceConnectionDao gcpPrivateServiceConnectionDao;
  @Inject private PaymentFactory paymentFactory;
  @Inject private NDSClusterSpotHealthCheckTestUtil healthCheckTestUtils;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, NDSGroupDao.DB, NDSGroupDao.COLLECTION);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/AWSAccountDao/awsAccounts.json.ftl", null, "nds", "config.nds.awsAccounts");
  }

  @Test
  public void testRemoveNetworkAccessForEligibleSuspendedOrgs() {
    final Date rightBeforeNetworkAccessRemoval = new Date();
    /** GIVEN an Org 265 with Group 161 and Group 162 eligible for network removal */
    assertNull(
        organizationDao
            .findById(oid(265))
            .getAutoRestrictAccessInfo()
            .getLastIpWhitelistAutoDeleteDate());
    assertEquals(
        2,
        ndsGroupDao.find(oid(161)).get().getNetworkPermissionList().getNetworkPermissions().size());
    assertEquals(
        1,
        ndsGroupDao.find(oid(162)).get().getNetworkPermissionList().getNetworkPermissions().size());

    /** GIVEN an Org 262 not eligible for network access removal */
    assertEquals(
        1,
        ndsGroupDao.find(oid(165)).get().getNetworkPermissionList().getNetworkPermissions().size());

    // WHEN remove network access for eligible orgs
    adminOrgSvc.removeNetworkAccessForEligibleSuspendedOrgs(List.of(oid(262), oid(265)));

    /**
     * THEN expect Org 265 with Group 161 and Group 162 will have their network access list being
     * removed.
     */
    assertTrue(
        organizationDao
            .findById(oid(265))
            .getAutoRestrictAccessInfo()
            .getLastIpWhitelistAutoDeleteDate()
            .after(rightBeforeNetworkAccessRemoval));
    assertEquals(
        0,
        ndsGroupDao.find(oid(161)).get().getNetworkPermissionList().getNetworkPermissions().size());
    assertEquals(
        0,
        ndsGroupDao.find(oid(162)).get().getNetworkPermissionList().getNetworkPermissions().size());

    /** THEN expect Org 262 with Group 165 will not have its network access list being removed. */
    assertFalse(
        organizationDao
            .findById(oid(262))
            .getAutoRestrictAccessInfo()
            .getLastIpWhitelistAutoDeleteDate()
            .after(rightBeforeNetworkAccessRemoval));
    assertEquals(
        1,
        ndsGroupDao.find(oid(165)).get().getNetworkPermissionList().getNetworkPermissions().size());
  }

  private void setupPauseReadyCluster(final ObjectId pGroupId, final String pClusterName)
      throws SvcException {
    final Group group = MmsFactory.createGroupWithNDSPlan(pGroupId.toString(), pGroupId);
    ndsGroupSvc.ensureGroup(pGroupId);
    ndsGroupDao.addCloudContainer(
        pGroupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
    clusterDescriptionDao.setState(pGroupId, pClusterName, State.IDLE);
    healthCheckTestUtils.setupHealthyCluster(ndsGroupSvc.find(pGroupId).get(), pClusterName, group);
  }

  @Test
  public void testPausePaidClustersForDelinquentOrgs() throws SvcException {
    final Date rightBeforePaidClustersPauseDate = new Date();
    setupPauseReadyCluster(oid(163), "lockedOrgRs1");
    setupPauseReadyCluster(oid(163), "lockedOrgRs2");
    setupPauseReadyCluster(oid(164), "lockedOrgRs3");

    paymentMethodStubber.stubPaymentMethod(oid(163));
    paymentMethodStubber.stubPaymentMethod(oid(164));
    paymentMethodStubber.stubPaymentMethod(oid(166));
    paymentMethodStubber.stubPaymentMethod(oid(269));
    paymentMethodStubber.stubPaymentMethod(oid(272));
    paymentMethodStubber.stubPaymentMethod(oid(273));
    paymentMethodStubber.stubPaymentMethod(oid(274));
    paymentMethodStubber.stubPaymentMethod(oid(275));

    adminOrgSvc.pausePaidClustersForDelinquentOrgs();
    assertTrue(clusterDescriptionUpdatesDao.findByName(oid(163), "lockedOrgRs1").get().isPaused());
    assertTrue(clusterDescriptionUpdatesDao.findByName(oid(163), "lockedOrgRs2").get().isPaused());
    assertTrue(clusterDescriptionUpdatesDao.findByName(oid(164), "lockedOrgRs3").get().isPaused());

    assertFalse(clusterDescriptionUpdatesDao.findByName(oid(166), "lockedOrgRs4").isPresent());

    assertTrue(
        organizationDao
            .findById(oid(275))
            .getAutoRestrictAccessInfo()
            .getLastPaidClustersAutoPauseDate()
            .after(rightBeforePaidClustersPauseDate));

    assertFalse(
        organizationDao
            .findById(oid(271))
            .getAutoRestrictAccessInfo()
            .getLastPaidClustersAutoPauseDate()
            .after(rightBeforePaidClustersPauseDate));
  }

  @Test
  public void testDeletePrivateEndpoints() throws SvcException {
    // Set up a mock group in a locked org, with NDS plan and private endpoints
    final Group group = setUpGroupWithPrivateEndpoints();
    final ObjectId groupId = group.getId();
    final Organization updatedOrg = organizationDao.findById(group.getOrgId());
    final AppUser user = new AppUser();
    final AuditInfo auditInfo =
        MmsFactory.createAuditInfoFromUiCall(user, false, "remoteAddress.com");

    // WHEN request to terminate all private endpoints for a given org
    adminOrgSvc.deletePrivateEndpoints(updatedOrg, auditInfo);

    final NDSGroup ndsGroup = ndsGroupSvc.ensureGroup(groupId);
    final List<CloudProviderContainer> cloudProviderContainers =
        ndsGroup.getCloudProviderContainers();
    assertEquals(6, cloudProviderContainers.size());

    // THEN expect each private endpoint connecting to Dedicated Clusters is deleted requested
    cloudProviderContainers.forEach(
        cloudProviderContainer -> {
          final List<? extends DedicatedEndpointService> endpointServices =
              cloudProviderContainer.getEndpointServices();
          if (cloudProviderContainer.getCloudProvider().equals(CloudProvider.GCP)) {
            boolean hasAnyEndpointNotDeleted =
                endpointServices.stream()
                    .map(GCPPrivateServiceConnectRegionGroup.class::cast)
                    .flatMap(endpointService -> endpointService.getGCPEndpointGroups().stream())
                    .anyMatch(endpointGroup -> !endpointGroup.isDeleteRequested());
            assertFalse(hasAnyEndpointNotDeleted);
          } else {
            endpointServices.forEach(
                endpointService ->
                    endpointService
                        .getEndpoints()
                        .forEach(endpoint -> assertTrue(endpoint.isDeleteRequested())));
          }
        });

    // THEN expect each private endpoint connecting to Serverless Instances deleted requested
    final List<? extends TenantEndpoint> tenantEndpoints =
        ndsTenantEndpointSvc.getEndpointsForGroupByCloudProvider(
            ndsGroup.getGroupId(), CloudProvider.AWS);
    assertTrue(tenantEndpoints.stream().allMatch(TenantEndpoint::isDeleteRequested));

    // THEN expect each private endpoint connecting to Federated Database Instance / Online Archive
    // is deleted
    assertTrue(ndsGroup.getPrivateNetworkSettings().getEndpointIds().isEmpty());
  }

  private Group setUpGroupWithPrivateEndpoints() throws SvcException {
    // Set up a mock group with NDS Plan
    final Group group = MmsFactory.createGroupWithNDSPlan();
    final ObjectId groupId = group.getId();
    ndsGroupSvc.ensureGroup(groupId);

    // Set up an org with the mock group with a FAILED payment older than 60 days,
    // which should cause the org to be locked
    final ObjectId orgId = group.getOrgId();
    final Organization org = organizationDao.findById(orgId);
    final Date now = new Date();
    final Date lockedDate = DelinquentOrganizationSvc.getLockedOutDate(now);
    final Date dayBeforeLockedDate = DateUtils.addDays(lockedDate, -1);
    paymentFactory.createPaymentWithPaymentDueDate(
        PaymentMethodType.CREDIT_CARD,
        org.getId(),
        100,
        Status.FAILED,
        BillingAccount.MONGODB_INC,
        dayBeforeLockedDate);
    delinquentOrganizationSvc.updatePaymentStatusForOrganization(org, now);

    // Set up different Cloud Provider containers in the mock group
    final Pair<ObjectId, List<ObjectId>> pair =
        CloudProviderContainerTestUtils.setupGroupWithMultipleContainers(
            ndsGroupDao,
            groupId,
            List.of(
                AWSRegionName.US_EAST_1,
                AWSRegionName.EU_CENTRAL_1,
                AzureRegionName.US_EAST,
                AzureRegionName.US_EAST_2,
                GCPRegionName.NORTH_AMERICA_NORTHEAST_1));
    final List<ObjectId> cloudProvidersContainerIds = pair.getRight();

    // Set up private endpoints connecting Dedicated Clusters
    setUpAWSPrivateLinkConnections(groupId, cloudProvidersContainerIds);
    setUpAzurePrivateLinkConnections(groupId, cloudProvidersContainerIds);
    setUpGCPPrivateServiceConnections(groupId, cloudProvidersContainerIds);

    // Set up private endpoints connecting to Serverless Instances
    setUpTenantEndpoint(groupId);

    // Set up private endpoints connecting to Federated Database Instance / Online Archive
    ndsGroupDao.updatePrivateNetworkSettings(
        group.getId(),
        new NDSPrivateNetworkSettings(
            Set.of(
                new NDSPrivateNetworkEndpointIdEntry.Builder()
                    .endpointId("vpce-0c591c2c0310482bf")
                    .type(NDSPrivateNetworkEndpointIdEntry.Type.DATA_LAKE)
                    .provider(CloudProvider.AWS)
                    .comment("test endpoint")
                    .build()),
            null),
        new Date());

    return group;
  }

  private void setUpAWSPrivateLinkConnections(
      final ObjectId groupId, final List<ObjectId> cloudProvidersContainerIds) {
    final ObjectId connection1Id = oid(111);
    final List<AWSPrivateLinkConnection> awsPrivateLinkConnections =
        List.of(
            new AWSPrivateLinkConnection(
                connection1Id,
                DedicatedEndpointService.Status.AVAILABLE,
                "endpointServiceError0",
                false,
                null,
                List.of(
                    new AWSPrivateLinkInterfaceEndpoint(
                        "endpointId0",
                        "privateHostname0",
                        AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                        "endpointError0",
                        false,
                        0)),
                "endpointServiceName0",
                "endpointServiceId0",
                "arn:aws:elasticloadbalancing:us-east-2:123456789012:targetgroup/my-targets/0000000000000000"),
            new AWSPrivateLinkConnection(
                ObjectId.get(),
                DedicatedEndpointService.Status.INITIATING,
                "endpointServiceError1",
                false,
                null,
                List.of(),
                "endpointServiceName1",
                "endpointServiceId1",
                "arn:aws:elasticloadbalancing:us-east-2:123456789012:targetgroup/my-targets/0000000000000001"));

    // add endpoint service to AWS containers containerIds[0,1]
    IntStream.range(0, 2)
        .forEach(
            i ->
                awsPrivateLinkDao.initializePrivateEndpointService(
                    awsPrivateLinkConnections.get(i), groupId, cloudProvidersContainerIds.get(i)));
  }

  public void setUpAzurePrivateLinkConnections(
      final ObjectId groupId, final List<ObjectId> cloudProvidersContainerIds) {
    final ObjectId connection1Id = oid(222);
    final List<AzurePrivateLinkConnection> azurePrivateLinkConnections =
        List.of(
            new AzurePrivateLinkConnection(
                connection1Id,
                "resourceId0",
                "serviceName0",
                "balancerName0",
                List.of(
                    new AzurePrivateEndpoint(
                        "resourceId0",
                        "ipAddr",
                        "hostname",
                        "mongodbHostname",
                        "connectionName",
                        AzurePrivateEndpoint.Status.AVAILABLE,
                        "error0",
                        false,
                        0)),
                DedicatedEndpointService.Status.AVAILABLE,
                "errorMessage0",
                false,
                null),
            new AzurePrivateLinkConnection(
                ObjectId.get(),
                "resourceId1",
                "serviceName1",
                "balancerName1",
                List.of(
                    new AzurePrivateEndpoint(
                        "resourceId1",
                        "ipAddr",
                        "hostname",
                        "mongodbHostname",
                        "connectionName",
                        AzurePrivateEndpoint.Status.AVAILABLE,
                        "error1",
                        true,
                        1)),
                DedicatedEndpointService.Status.AVAILABLE,
                "errorMessage1",
                true,
                null));

    // add endpoint service to azure containers containerIds[2,3]
    IntStream.range(0, 2)
        .forEach(
            i ->
                azurePrivateLinkDao.initializePrivateEndpointService(
                    azurePrivateLinkConnections.get(i),
                    groupId,
                    cloudProvidersContainerIds.get(i + 2)));
  }

  private void setUpGCPPrivateServiceConnections(
      final ObjectId groupId, final List<ObjectId> cloudProvidersContainerIds) {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPSCRegionGroupWithActiveEndpointGroup(
            50, GCPRegionName.US_WEST_2, "testGcpPscEndpointGroup");

    // add endpoint service to GPC containers containerIds[4]
    gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, groupId, cloudProvidersContainerIds.get(4));

    final List<GCPPrivateServiceConnection> privateServiceConnections =
        NDSModelTestFactory.getGCPPrivateServiceConnections(groupId, regionGroup);
    privateServiceConnections.forEach(
        psc -> gcpPrivateServiceConnectionDao.insertPrivateServiceConnection(psc));
  }

  private void setUpTenantEndpoint(final ObjectId groupId) throws SvcException {
    final String SERVERLESS_INSTANCE_NAME = "serverless";
    final String MTM_CLUSTER_NAME = "mtmCluster";
    final ObjectId MTM_GROUP_ID = oid(777);
    final ObjectId TENANT_ENDPOINT_SERVICE_ID = oid(1111);

    ClusterDescription serverlessInstance =
        createServerlessInstance(groupId, SERVERLESS_INSTANCE_NAME);
    clusterDescriptionDao.save(serverlessInstance);

    final ServerlessCloudProviderContainer instanceContainer =
        new ServerlessCloudProviderContainer(
            NDSModelTestFactory.getServerlessContainer(
                MTM_GROUP_ID, MTM_CLUSTER_NAME, SERVERLESS_INSTANCE_NAME));
    ndsGroupSvc.addCloudContainer(ndsGroupSvc.ensureGroup(groupId), instanceContainer);

    final AWSTenantEndpoint instanceEndpoint =
        TenantPrivateNetworkingModelTestFactory.getAvailableAWSTenantEndpoint(
                serverlessInstance.getGroupId(),
                SERVERLESS_INSTANCE_NAME,
                groupId,
                TENANT_ENDPOINT_SERVICE_ID)
            .toBuilder()
            .id(oid(11))
            .build();
    awsTenantEndpointSvc.createEndpoint(instanceEndpoint);
  }

  private ClusterDescription createServerlessInstance(final ObjectId groupId, final String name) {
    final String dnsPin =
        RandomStringUtils.randomAlphanumeric(NDSDefaults.NUM_DNS_PIN_CHARACTERS).toLowerCase();
    final TestServerlessClusterDescriptionConfig serverlessClusterDescriptionConfig =
        new NDSModelTestFactory.TestServerlessClusterDescriptionConfig()
            .setGroupId(groupId)
            .setClusterProvisionType(ClusterProvisionType.FAST)
            .setDnsPin(dnsPin)
            .setClusterName(name);
    final ClusterDescription serverlessInstance =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                serverlessClusterDescriptionConfig));
    return serverlessInstance;
  }
}
