package com.xgen.svc.mms.svc.billing.monitoring.impl;

import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.CURRENT_MONTH_COUNT_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.CURRENT_MONTH_PRICE_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.CURRENT_MONTH_QUANTITY_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.PAST_DAY_COUNT_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.PAST_DAY_PRICE_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.PAST_DAY_QUANTITY_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.SKU_PROM_LABEL;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.LineItemsBillingMetricsAggregator.SKU_SERVICE_PROM_LABEL;
import static java.util.stream.Collectors.groupingBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.svc.billing.monitoring.BillingMetricFailureException;
import io.prometheus.client.CollectorRegistry;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class LineItemsBillingMetricsAggregatorIntTests extends JUnit5BaseSvcTest {

  @Inject private LineItemsBillingMetricsAggregator aggregator;

  @Inject private LineItemDao _lineItemDao;

  @Test
  @SuppressWarnings("deprecation")
  public void testEmitMetrics() throws BillingMetricFailureException {
    ObjectId pInvoiceId = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date dayBeforeFirstDayOfMonth = DateUtils.addDays(firstDayOfMonth, -1);
    Date twoDaysBeforeFirstDayOfMonth = DateUtils.addDays(firstDayOfMonth, -2);
    Date yesterday = DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), -1);

    int lineItemsOfEachTypeToCreate = 3;
    for (int i = 0; i < lineItemsOfEachTypeToCreate; i++) {
      LineItem lineItemAws1 =
          new LineItem.Builder()
              .totalPriceCents(10)
              .quantity(10)
              .sku(SKU.NDS_AWS_INSTANCE_R400)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemAws2 =
          new LineItem.Builder()
              .totalPriceCents(20)
              .quantity(100)
              .sku(SKU.NDS_AWS_INSTANCE_R400)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemAws3 =
          new LineItem.Builder()
              .totalPriceCents(30)
              .quantity(1000)
              .sku(SKU.NDS_AWS_INSTANCE_R60)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemAws4 =
          new LineItem.Builder()
              .totalPriceCents(4000)
              .quantity(2000)
              .sku(SKU.NDS_AWS_INSTANCE_R60)
              .startDate(dayBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemAws5 =
          new LineItem.Builder()
              .totalPriceCents(5000)
              .quantity(3000)
              .sku(SKU.NDS_AWS_INSTANCE_R60)
              .startDate(twoDaysBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemAws6 =
          new LineItem.Builder()
              .totalPriceCents(5000)
              .quantity(3000)
              .sku(SKU.NDS_AWS_INSTANCE_R60)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      // data transfer skus
      LineItem lineItemDataTransfer1 =
          new LineItem.Builder()
              .totalPriceCents(100)
              .quantity(100)
              .sku(SKU.REALM_APP_DATA_TRANSFER)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemDataTransfer2 =
          new LineItem.Builder()
              .totalPriceCents(200)
              .quantity(1000)
              .sku(SKU.REALM_APP_DATA_TRANSFER)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemDataTransfer3 =
          new LineItem.Builder()
              .totalPriceCents(300)
              .quantity(10000)
              .sku(SKU.NDS_AZURE_DATA_TRANSFER)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemDataTransfer4 =
          new LineItem.Builder()
              .totalPriceCents(40000)
              .quantity(20000)
              .sku(SKU.NDS_AZURE_DATA_TRANSFER)
              .startDate(dayBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemDataTransfer5 =
          new LineItem.Builder()
              .totalPriceCents(50000)
              .quantity(30000)
              .sku(SKU.NDS_AZURE_DATA_TRANSFER)
              .startDate(twoDaysBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemDataTransfer6 =
          new LineItem.Builder()
              .totalPriceCents(50000)
              .quantity(30000)
              .sku(SKU.NDS_AZURE_DATA_TRANSFER)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      // backup skus
      LineItem lineItemBackup1 =
          new LineItem.Builder()
              .totalPriceCents(1000)
              .quantity(1000)
              .sku(SKU.CLASSIC_BACKUP_OPLOG)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemBackup2 =
          new LineItem.Builder()
              .totalPriceCents(2000)
              .quantity(4567)
              .sku(SKU.CLASSIC_BACKUP_OPLOG)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemBackup3 =
          new LineItem.Builder()
              .totalPriceCents(6573)
              .quantity(34672)
              .sku(SKU.MMS_BACKUP_STORAGE)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemBackup4 =
          new LineItem.Builder()
              .totalPriceCents(7543)
              .quantity(7453)
              .sku(SKU.MMS_BACKUP_STORAGE)
              .startDate(dayBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemBackup5 =
          new LineItem.Builder()
              .totalPriceCents(4256)
              .quantity(5424)
              .sku(SKU.MMS_BACKUP_STORAGE)
              .startDate(twoDaysBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemBackup6 =
          new LineItem.Builder()
              .totalPriceCents(6481)
              .quantity(9623)
              .sku(SKU.MMS_BACKUP_STORAGE)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      // support skus
      LineItem lineItemSupport1 =
          new LineItem.Builder()
              .totalPriceCents(10000)
              .quantity(10000)
              .sku(SKU.NDS_ENTITLEMENTS)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemSupport2 =
          new LineItem.Builder()
              .totalPriceCents(20000)
              .quantity(34562)
              .sku(SKU.NDS_ENTITLEMENTS)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemSupport3 =
          new LineItem.Builder()
              .totalPriceCents(30000)
              .quantity(5346)
              .sku(SKU.NDS_FREE_SUPPORT)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemSupport4 =
          new LineItem.Builder()
              .totalPriceCents(412651)
              .quantity(2542)
              .sku(SKU.NDS_FREE_SUPPORT)
              .startDate(dayBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemSupport5 =
          new LineItem.Builder()
              .totalPriceCents(58392)
              .quantity(30561)
              .sku(SKU.NDS_FREE_SUPPORT)
              .startDate(twoDaysBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemSupport6 =
          new LineItem.Builder()
              .totalPriceCents(5031)
              .quantity(30031)
              .sku(SKU.NDS_FREE_SUPPORT)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemSupport7 =
          new LineItem.Builder()
              .totalPriceCents(5031)
              .quantity(30031)
              .sku(SKU.NDS_FREE_SUPPORT)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      // premium feature skus
      LineItem lineItemPrem1 =
          new LineItem.Builder()
              .totalPriceCents(10313)
              .quantity(10023)
              .sku(SKU.NDS_BI_CONNECTOR)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemPrem2 =
          new LineItem.Builder()
              .totalPriceCents(20011)
              .quantity(10002)
              .sku(SKU.NDS_BI_CONNECTOR)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemPrem3 =
          new LineItem.Builder()
              .totalPriceCents(30051)
              .quantity(543)
              .sku(SKU.NDS_ADVANCED_SECURITY)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemPrem4 =
          new LineItem.Builder()
              .totalPriceCents(4123)
              .quantity(2123)
              .sku(SKU.NDS_ADVANCED_SECURITY)
              .startDate(dayBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemPrem5 =
          new LineItem.Builder()
              .totalPriceCents(502300)
              .quantity(30021)
              .sku(SKU.NDS_ADVANCED_SECURITY)
              .startDate(twoDaysBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemPrem6 =
          new LineItem.Builder()
              .totalPriceCents(5311)
              .quantity(30400)
              .sku(SKU.NDS_ADVANCED_SECURITY)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      // credit skus
      LineItem lineItemCredit1 =
          new LineItem.Builder()
              .totalPriceCents(-123)
              .quantity(1)
              .sku(SKU.CREDIT)
              .startDate(firstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemCredit2 =
          new LineItem.Builder()
              .totalPriceCents(-150)
              .quantity(2)
              .sku(SKU.CREDIT)
              .startDate(dayBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemCredit3 =
          new LineItem.Builder()
              .totalPriceCents(-191)
              .quantity(3)
              .sku(SKU.CREDIT)
              .startDate(twoDaysBeforeFirstDayOfMonth)
              .invoiceId(pInvoiceId)
              .build();

      LineItem lineItemCredit4 =
          new LineItem.Builder()
              .totalPriceCents(-190)
              .quantity(3)
              .sku(SKU.CREDIT)
              .startDate(yesterday)
              .invoiceId(pInvoiceId)
              .build();

      _lineItemDao.save(lineItemAws1);
      _lineItemDao.save(lineItemAws2);
      _lineItemDao.save(lineItemAws3);
      _lineItemDao.save(lineItemAws4);
      _lineItemDao.save(lineItemAws5);
      _lineItemDao.save(lineItemAws6);
      _lineItemDao.save(lineItemBackup1);
      _lineItemDao.save(lineItemBackup2);
      _lineItemDao.save(lineItemBackup3);
      _lineItemDao.save(lineItemBackup4);
      _lineItemDao.save(lineItemBackup5);
      _lineItemDao.save(lineItemBackup6);
      _lineItemDao.save(lineItemPrem1);
      _lineItemDao.save(lineItemPrem2);
      _lineItemDao.save(lineItemPrem3);
      _lineItemDao.save(lineItemPrem4);
      _lineItemDao.save(lineItemPrem5);
      _lineItemDao.save(lineItemPrem6);
      _lineItemDao.save(lineItemSupport1);
      _lineItemDao.save(lineItemSupport2);
      _lineItemDao.save(lineItemSupport3);
      _lineItemDao.save(lineItemSupport4);
      _lineItemDao.save(lineItemSupport5);
      _lineItemDao.save(lineItemSupport6);
      _lineItemDao.save(lineItemSupport7);
      _lineItemDao.save(lineItemDataTransfer1);
      _lineItemDao.save(lineItemDataTransfer2);
      _lineItemDao.save(lineItemDataTransfer3);
      _lineItemDao.save(lineItemDataTransfer4);
      _lineItemDao.save(lineItemDataTransfer5);
      _lineItemDao.save(lineItemDataTransfer6);
      _lineItemDao.save(lineItemCredit1);
      _lineItemDao.save(lineItemCredit2);
      _lineItemDao.save(lineItemCredit3);
      _lineItemDao.save(lineItemCredit4);
    }

    aggregator.emitMetrics();
    verifyCurrentMonthMetrics(pInvoiceId);
    verifyPreviousDayMetrics(pInvoiceId);
  }

  @Test
  public void testFirstOfMonth() throws BillingMetricFailureException {
    Date today = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date tomorrow = DateUtils.addDays(today, 1);
    Date yesterday = DateUtils.addDays(today, -1);
    Date dayBeforeYesterday = DateUtils.addDays(yesterday, -1);

    LineItem lineItemCreatedDayBeforeTheFirst =
        new LineItem.Builder()
            .totalPriceCents(10)
            .quantity(20)
            .sku(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6)
            .startDate(dayBeforeYesterday)
            .endDate(yesterday)
            .build();
    _lineItemDao.save(lineItemCreatedDayBeforeTheFirst);

    aggregator.emitMetrics(yesterday);

    verifyCurrentMonth(10, 20, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6);
    verifyPreviousDay(10, 20, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6);

    LineItem lineItemCreatedOnFirst =
        new LineItem.Builder()
            .totalPriceCents(100)
            .quantity(200)
            .sku(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10)
            .startDate(yesterday)
            .endDate(today)
            .build();
    _lineItemDao.save(lineItemCreatedOnFirst);

    aggregator.emitMetrics(today);

    verifyCurrentMonth(0, 0, 0, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6);
    verifyPreviousDay(0, 0, 0, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6);

    verifyCurrentMonth(100, 200, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10);
    verifyPreviousDay(100, 200, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10);

    LineItem lineItemCreatedOnDayAfterFirst =
        new LineItem.Builder()
            .totalPriceCents(1000)
            .quantity(2000)
            .sku(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P15)
            .startDate(today)
            .endDate(tomorrow)
            .build();
    _lineItemDao.save(lineItemCreatedOnDayAfterFirst);

    aggregator.emitMetrics(tomorrow);

    verifyCurrentMonth(0, 0, 0, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6);
    verifyPreviousDay(0, 0, 0, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6);

    verifyCurrentMonth(100, 200, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10);
    verifyPreviousDay(0, 0, 0, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10);

    verifyCurrentMonth(1000, 2000, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P15);
    verifyPreviousDay(1000, 2000, 1, SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P15);
  }

  private void verifyCurrentMonthMetrics(ObjectId pInvoiceId) {
    Date firstDayOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date dayBeforeFirstOfMonth = DateUtils.addDays(firstDayOfMonth, -1);
    List<LineItem> allLineItems = _lineItemDao.findByInvoiceId(pInvoiceId);

    Map<SKU, Long> currentMonthExpectedPrices =
        allLineItems.stream()
            .filter((l) -> !l.getStartDate().before(dayBeforeFirstOfMonth))
            .collect(
                groupingBy(LineItem::getSku, Collectors.summingLong(LineItem::getTotalPriceCents)));

    Map<SKU, Double> currentMonthExpectedValuesQuantity =
        allLineItems.stream()
            .filter((l) -> !l.getStartDate().before(dayBeforeFirstOfMonth))
            .collect(groupingBy(LineItem::getSku, Collectors.summingDouble(LineItem::getQuantity)));

    Map<SKU, Long> currentMonthExpectedCounts =
        allLineItems.stream()
            .filter((l) -> !l.getStartDate().before(dayBeforeFirstOfMonth))
            .collect(groupingBy(LineItem::getSku, Collectors.counting()));

    long numSkusChecked = 0;
    for (SKU sku : SKU.values()) {
      double expectedPrice = 0;
      double expectedCount = 0;
      double expectedQuantity = 0;
      if (currentMonthExpectedCounts.containsKey(sku)) {
        numSkusChecked++;
        assertTrue(currentMonthExpectedCounts.get(sku) > 0);
        assertTrue(currentMonthExpectedPrices.get(sku) != 0);
        assertTrue(currentMonthExpectedValuesQuantity.get(sku) > 0);
        expectedPrice = currentMonthExpectedPrices.get(sku);
        expectedCount = currentMonthExpectedCounts.get(sku);
        expectedQuantity = currentMonthExpectedValuesQuantity.get(sku);
      }
      String skuService = sku.getInfo().getSkuService().name();
      assertEquals(expectedPrice, getValue(CURRENT_MONTH_PRICE_NAME, skuService, sku.name()), 0);
      assertEquals(
          expectedQuantity, getValue(CURRENT_MONTH_QUANTITY_NAME, skuService, sku.name()), 0);
      assertEquals(expectedCount, getValue(CURRENT_MONTH_COUNT_NAME, skuService, sku.name()), 0);
    }
    assertTrue(numSkusChecked > 0);
  }

  private void verifyPreviousDayMetrics(ObjectId pInvoiceId) {
    Date yesterday = DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), -1);
    Date today = DateUtils.truncate(new Date(), Calendar.DATE);
    List<LineItem> allLineItems = _lineItemDao.findByInvoiceId(pInvoiceId);

    Map<SKU, Long> previousDayPrices =
        allLineItems.stream()
            .filter((l) -> !l.getStartDate().before(yesterday) && l.getStartDate().before(today))
            .collect(
                groupingBy(LineItem::getSku, Collectors.summingLong(LineItem::getTotalPriceCents)));

    Map<SKU, Double> previousDayQuants =
        allLineItems.stream()
            .filter((l) -> !l.getStartDate().before(yesterday) && l.getStartDate().before(today))
            .collect(groupingBy(LineItem::getSku, Collectors.summingDouble(LineItem::getQuantity)));

    Map<SKU, Long> previousDayCounts =
        allLineItems.stream()
            .filter((l) -> !l.getStartDate().before(yesterday) && l.getStartDate().before(today))
            .collect(groupingBy(LineItem::getSku, Collectors.counting()));

    long numSkusChecked = 0;
    for (SKU sku : SKU.values()) {
      double expectedPrice = 0;
      double expectedCount = 0;
      double expectedQuantity = 0;
      if (previousDayCounts.containsKey(sku)) {
        numSkusChecked++;
        assertTrue(previousDayCounts.get(sku) > 0);
        assertTrue(previousDayPrices.get(sku) != 0);
        assertTrue(previousDayQuants.get(sku) > 0);
        expectedPrice = (double) previousDayPrices.get(sku);
        expectedCount = previousDayCounts.get(sku);
        expectedQuantity = previousDayQuants.get(sku);
      }
      String skuService = sku.getInfo().getSkuService().name();
      assertEquals(expectedPrice, getValue(PAST_DAY_PRICE_NAME, skuService, sku.name()), 0);
      assertEquals(expectedQuantity, getValue(PAST_DAY_QUANTITY_NAME, skuService, sku.name()), 0);
      assertEquals(expectedCount, getValue(PAST_DAY_COUNT_NAME, skuService, sku.name()), 0);
    }
    assertTrue(numSkusChecked > 0);
  }

  private void verifyCurrentMonth(
      double pExpectedPrice, double pExpectedQuantity, double pExpectedCount, SKU pSku) {
    assertEquals(
        pExpectedPrice,
        getValue(CURRENT_MONTH_PRICE_NAME, pSku.getInfo().getSkuService().name(), pSku.name()),
        0);
    assertEquals(
        pExpectedQuantity,
        getValue(CURRENT_MONTH_QUANTITY_NAME, pSku.getInfo().getSkuService().name(), pSku.name()),
        0);
    assertEquals(
        pExpectedCount,
        getValue(CURRENT_MONTH_COUNT_NAME, pSku.getInfo().getSkuService().name(), pSku.name()),
        0);
  }

  private void verifyPreviousDay(
      double pExpectedPrice, double pExpectedQuantity, double pExpectedCount, SKU pSku) {
    assertEquals(
        pExpectedPrice,
        getValue(PAST_DAY_PRICE_NAME, pSku.getInfo().getSkuService().name(), pSku.name()),
        0);
    assertEquals(
        pExpectedQuantity,
        getValue(PAST_DAY_QUANTITY_NAME, pSku.getInfo().getSkuService().name(), pSku.name()),
        0);
    assertEquals(
        pExpectedCount,
        getValue(PAST_DAY_COUNT_NAME, pSku.getInfo().getSkuService().name(), pSku.name()),
        0);
  }

  private Double getValue(String name, String skuService, String sku) {
    return CollectorRegistry.defaultRegistry.getSampleValue(
        name,
        new String[] {SKU_SERVICE_PROM_LABEL, SKU_PROM_LABEL},
        new String[] {skuService, sku});
  }
}
