package com.xgen.svc.mms.svc.billing.monitoring.impl;

import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_DEVELOPER;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_ENTERPRISE;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_MLAB_PRO_1HR;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_MLAB_PRO_2HR;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_PLATINUM;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_PREMIER;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_PRO;
import static com.xgen.cloud.billingplatform.model.plan._public.model.PlanType.NDS_SELF_SERVE_PRO;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.PlansBillingMetricsAggregator.ACTIVE_SALES_SOLD_COUNT_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.PlansBillingMetricsAggregator.ACTIVE_SELF_SERVE_COUNT_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.PlansBillingMetricsAggregator.BILLED_SUPPORT_SALES_SOLD_CURRENT_MONTH_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.PlansBillingMetricsAggregator.BILLED_SUPPORT_SELF_SERVE_CURRENT_MONTH_NAME;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.PlansBillingMetricsAggregator.PROM_LABEL_ELASTIC;
import static com.xgen.svc.mms.svc.billing.monitoring.impl.PlansBillingMetricsAggregator.PROM_LABEL_PLAN_TYPE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.common.collect.ImmutableList;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Status;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Type;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.dao.billing.OrgPlanDao;
import com.xgen.svc.mms.dao.billing.PayingOrgDao;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlanType;
import com.xgen.svc.mms.svc.billing.monitoring.BillingMetricFailureException;
import io.prometheus.client.CollectorRegistry;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class PlansBillingMetricsAggregatorIntTests extends JUnit5BaseSvcTest {

  @Inject private LineItemDao _lineItemDao;

  @Inject private OrgPlanDao _orgPlanDao;

  @Inject private InvoiceDao _invoiceDao;

  @Inject private PayingOrgDao _payingOrgDao;

  @Inject private PlansBillingMetricsAggregator _aggregator;

  @Test
  public void testEmitMetrics() throws BillingMetricFailureException {
    ObjectId invoiceId = new ObjectId();
    ObjectId invoiceId2 = new ObjectId();
    ObjectId invoiceId3 = new ObjectId();
    Date now = new Date();
    ObjectId orgId = new ObjectId();
    ObjectId orgId2 = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date secondDayOfMonth = DateUtils.addDays(firstDayOfMonth, 1);
    Date thirdDayOfMonth = DateUtils.addDays(firstDayOfMonth, 2);
    Date firstDayOfPreviousMonth = DateUtils.addMonths(firstDayOfMonth, -1);
    Date secondDayOfPreviousMonth = DateUtils.addDays(firstDayOfPreviousMonth, 1);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);

    LineItem lineItem =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem2 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem3 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem4 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem5 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem6 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId2)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem7 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1000)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    LineItem lineItem8 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1002)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    LineItem lineItem9 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1005)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .id(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice2 =
        new Invoice.Builder()
            .id(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId2)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice3 =
        new Invoice.Builder()
            .id(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(firstDayOfMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    OrgPlan prepaidPlan =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_1HR)
            .startDate(firstDayOfMonth)
            .orgId(orgId)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .build();

    OrgPlan prepaidPlan2 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_1HR)
            .startDate(firstDayOfPreviousMonth)
            .orgId(orgId2)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .build();

    _orgPlanDao.insertReplicaSafe(prepaidPlan);
    _orgPlanDao.insertReplicaSafe(prepaidPlan2);
    List<LineItem> lineItems =
        Arrays.asList(
            lineItem, lineItem2, lineItem3, lineItem4, lineItem5, lineItem6, lineItem7, lineItem8,
            lineItem9);
    lineItems.forEach((l) -> _lineItemDao.save(l));
    _invoiceDao.save(invoice);
    _invoiceDao.save(invoice2);
    _invoiceDao.save(invoice3);

    _aggregator.emitMetrics();

    verifyAmountBilledGauge(prepaidPlan, lineItems);
    verifyAmountBilledGauge(prepaidPlan2, lineItems);
    verifyCountActiveGauge(true, false, NDS_MLAB_PRO_1HR, 2.0);
  }

  @Test
  public void testEmitMetrics_linkedOrgs_linkedOrgsCorrectlyCalculateGauge() {
    ObjectId invoiceId = new ObjectId();
    ObjectId invoiceId2 = new ObjectId();
    ObjectId invoiceId3 = new ObjectId();
    ObjectId invoiceId4 = new ObjectId();
    Date now = new Date();
    ObjectId payingOrg = new ObjectId();
    ObjectId linkedOrgId1 = new ObjectId();
    ObjectId linkedOrgId2 = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date secondDayOfMonth = DateUtils.addDays(firstDayOfMonth, 1);
    Date thirdDayOfMonth = DateUtils.addDays(firstDayOfMonth, 2);
    Date firstDayOfPreviousMonth = DateUtils.addMonths(firstDayOfMonth, -1);
    Date secondDayOfPreviousMonth = DateUtils.addDays(firstDayOfPreviousMonth, 1);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);

    List<LineItem> lineItems =
        ImmutableList.<LineItem>builder()
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(100)
                    .invoiceId(invoiceId)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(102)
                    .invoiceId(invoiceId)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(105)
                    .invoiceId(invoiceId)
                    .startDate(secondDayOfMonth)
                    .endDate(thirdDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(100)
                    .invoiceId(invoiceId2)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(102)
                    .invoiceId(invoiceId2)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(105)
                    .invoiceId(invoiceId2)
                    .startDate(secondDayOfMonth)
                    .endDate(thirdDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(110)
                    .invoiceId(invoiceId4)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(1000)
                    .invoiceId(invoiceId3)
                    .startDate(firstDayOfPreviousMonth)
                    .endDate(secondDayOfPreviousMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(1002)
                    .invoiceId(invoiceId3)
                    .startDate(firstDayOfPreviousMonth)
                    .endDate(secondDayOfPreviousMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(1005)
                    .invoiceId(invoiceId3)
                    .startDate(firstDayOfPreviousMonth)
                    .endDate(secondDayOfPreviousMonth)
                    .build())
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .id(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(payingOrg)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice2 =
        new Invoice.Builder()
            .id(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(linkedOrgId1)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice3 =
        new Invoice.Builder()
            .id(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(firstDayOfMonth)
            .orgId(payingOrg)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice4 =
        new Invoice.Builder()
            .id(invoiceId4)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(linkedOrgId2)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    OrgPlan prepaidPlanPayingOrgCurrMonth =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_1HR)
            .startDate(firstDayOfMonth)
            .orgId(payingOrg)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .build();

    OrgPlan prepaidPlanLinkedOrg1 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_2HR)
            .startDate(firstDayOfMonth)
            .orgId(linkedOrgId1)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .build();

    OrgPlan prepaidPlanLinkedOrg2 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_PREMIER)
            .startDate(firstDayOfMonth)
            .orgId(linkedOrgId2)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .build();

    OrgPlan prepaidPlanPayingOrgPastMonth =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_1HR)
            .startDate(firstDayOfPreviousMonth)
            .orgId(payingOrg)
            .endDate(firstDayOfMonth)
            .activated(new Date())
            .build();

    _orgPlanDao.insertReplicaSafe(prepaidPlanPayingOrgCurrMonth);
    _orgPlanDao.insertReplicaSafe(prepaidPlanPayingOrgPastMonth);
    _orgPlanDao.insertReplicaSafe(prepaidPlanLinkedOrg1);
    _orgPlanDao.insertReplicaSafe(prepaidPlanLinkedOrg2);
    lineItems.forEach((l) -> _lineItemDao.save(l));
    _invoiceDao.save(invoice);
    _invoiceDao.save(invoice2);
    _invoiceDao.save(invoice3);
    _invoiceDao.save(invoice4);

    _payingOrgDao.upsertPayingOrg(payingOrg, List.of(linkedOrgId1, linkedOrgId2), Instant.now());

    _aggregator.emitMetrics();

    verifyAmountBilledGauge(true, false, NDS_MLAB_PRO_1HR, 100 + 102 + 105 + 100 + 102 + 105 + 110);
    verifyAmountBilledGauge(true, false, NDS_MLAB_PRO_2HR, 0);
    verifyAmountBilledGauge(true, false, NDS_PREMIER, 0);

    verifyCountActiveGauge(true, false, NDS_MLAB_PRO_1HR, 1.0);
  }

  @Test
  public void testEmitMetrics_partialMonthPlans_correctlyCalculateGauge() {
    ObjectId invoiceId = new ObjectId();
    Date now = new Date();
    ObjectId orgId = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date secondDayOfMonth = DateUtils.addDays(firstDayOfMonth, 1);
    Date thirdDayOfMonth = DateUtils.addDays(firstDayOfMonth, 2);
    Date fourthDayOfMonth = DateUtils.addDays(firstDayOfMonth, 3);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);

    List<LineItem> lineItems =
        ImmutableList.<LineItem>builder()
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(100)
                    .invoiceId(invoiceId)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(102)
                    .invoiceId(invoiceId)
                    .startDate(firstDayOfMonth)
                    .endDate(secondDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(105)
                    .invoiceId(invoiceId)
                    .startDate(secondDayOfMonth)
                    .endDate(thirdDayOfMonth)
                    .build())
            .add(
                new LineItem.Builder()
                    .sku(SKU.NDS_ENTITLEMENTS)
                    .totalPriceCents(110)
                    .invoiceId(invoiceId)
                    .startDate(thirdDayOfMonth)
                    .endDate(fourthDayOfMonth)
                    .build())
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .id(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    OrgPlan prepaidPlan1 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_1HR)
            .startDate(firstDayOfMonth)
            .orgId(orgId)
            .endDate(thirdDayOfMonth)
            .activated(new Date())
            .build();

    OrgPlan prepaidPlan2 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_MLAB_PRO_2HR)
            .startDate(thirdDayOfMonth)
            .orgId(orgId)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .build();

    _orgPlanDao.insertReplicaSafe(prepaidPlan1);
    _orgPlanDao.insertReplicaSafe(prepaidPlan2);

    lineItems.forEach((l) -> _lineItemDao.save(l));
    _invoiceDao.save(invoice);

    _aggregator.emitMetrics();

    verifyAmountBilledGauge(true, false, NDS_MLAB_PRO_1HR, 100 + 102 + 105);
    verifyAmountBilledGauge(true, false, NDS_MLAB_PRO_2HR, 110);

    // Unfortunately, this logic depends on the current date so I need to make this test conditional
    //  to see which plan would be active on the current date.
    if (now.before(thirdDayOfMonth)) {
      verifyCountActiveGauge(true, false, NDS_MLAB_PRO_1HR, 1.0);
      verifyCountActiveGauge(true, false, NDS_MLAB_PRO_2HR, 0.0);
    } else {
      verifyCountActiveGauge(true, false, NDS_MLAB_PRO_1HR, 0.0);
      verifyCountActiveGauge(true, false, NDS_MLAB_PRO_2HR, 1.0);
    }
  }

  @Test
  public void testEmitMetrics_multiplePrepaidPlanTypes() throws BillingMetricFailureException {
    ObjectId invoiceId = new ObjectId();
    ObjectId invoiceId2 = new ObjectId();
    ObjectId invoiceId3 = new ObjectId();
    Date now = new Date();
    ObjectId orgId = new ObjectId();
    ObjectId orgId2 = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date secondDayOfMonth = DateUtils.addDays(firstDayOfMonth, 1);
    Date thirdDayOfMonth = DateUtils.addDays(firstDayOfMonth, 2);
    Date firstDayOfPreviousMonth = DateUtils.addMonths(firstDayOfMonth, -1);
    Date secondDayOfPreviousMonth = DateUtils.addDays(firstDayOfPreviousMonth, 1);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);

    LineItem lineItem =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(101)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem2 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem3 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem3again =
        new LineItem.Builder()
            .sku(SKU.NDS_AWS_INSTANCE_R60)
            .totalPriceCents(400)
            .invoiceId(invoiceId)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem4 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem5 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem6 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId2)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem6again =
        new LineItem.Builder()
            .sku(SKU.NDS_AZURE_DATA_TRANSFER)
            .totalPriceCents(10335)
            .invoiceId(invoiceId2)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem7 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1000)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    LineItem lineItem8 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1002)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    LineItem lineItem9 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1005)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .id(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice2 =
        new Invoice.Builder()
            .id(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId2)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice3 =
        new Invoice.Builder()
            .id(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(firstDayOfMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    OrgPlan orgPlan =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_PREMIER)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .orgId(orgId)
            .build();

    OrgPlan orgPlan2 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .planType(NDS_PRO)
            .startDate(firstDayOfPreviousMonth)
            .endDate(firstDayOfMonth)
            .orgId(orgId2)
            .activated(firstDayOfPreviousMonth)
            .build();

    OrgPlan orgPlan3 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(new Date())
            .planType(NDS_MLAB_PRO_2HR)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId2)
            .activated(new Date())
            .build();

    _orgPlanDao.insertReplicaSafe(orgPlan);
    _orgPlanDao.insertReplicaSafe(orgPlan2);
    _orgPlanDao.insertReplicaSafe(orgPlan3);
    List<LineItem> lineItems =
        Arrays.asList(
            lineItem,
            lineItem2,
            lineItem3,
            lineItem3again,
            lineItem4,
            lineItem5,
            lineItem6,
            lineItem6again,
            lineItem7,
            lineItem8,
            lineItem9);
    lineItems.forEach((l) -> _lineItemDao.save(l));
    _invoiceDao.save(invoice);
    _invoiceDao.save(invoice2);
    _invoiceDao.save(invoice3);

    _aggregator.emitMetrics();

    verifyAmountBilledGauge(true, false, NDS_PREMIER, 308.0);
    verifyAmountBilledGauge(false, false, NDS_PREMIER, 0);
    verifyAmountBilledGauge(false, true, NDS_PREMIER, 0);
    verifyAmountBilledGauge(true, true, NDS_PREMIER, 0);
    verifyAmountBilledGauge(true, true, NDS_MLAB_PRO_2HR, 307.0);
    verifyAmountBilledGauge(true, false, NDS_MLAB_PRO_2HR, 0);
    verifyAmountBilledGauge(false, false, NDS_MLAB_PRO_2HR, 0);
    verifyAmountBilledGauge(false, true, NDS_MLAB_PRO_2HR, 0);

    verifyCountActiveGauge(true, false, NDS_PREMIER, 1.0);
    verifyCountActiveGauge(true, true, NDS_PREMIER, 0);
    verifyCountActiveGauge(false, false, NDS_PREMIER, 0);
    verifyCountActiveGauge(false, true, NDS_PREMIER, 0);
    verifyCountActiveGauge(true, true, NDS_MLAB_PRO_2HR, 1.0);
    verifyCountActiveGauge(true, false, NDS_MLAB_PRO_2HR, 0);
    verifyCountActiveGauge(false, false, NDS_MLAB_PRO_2HR, 0);
    verifyCountActiveGauge(false, true, NDS_MLAB_PRO_2HR, 0);

    verifyAllOtherGaugesNotIncremented(Arrays.asList(NDS_MLAB_PRO_2HR, NDS_PREMIER));
  }

  @Test
  public void testEmitMetrics_multiplePrepaidPlanTypes_salesSoldAndSelfServe()
      throws BillingMetricFailureException {
    ObjectId invoiceId = new ObjectId();
    ObjectId invoiceId2 = new ObjectId();
    ObjectId invoiceId3 = new ObjectId();
    Date now = new Date();
    ObjectId orgId = new ObjectId();
    ObjectId orgId2 = new ObjectId();
    ObjectId orgId3 = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date secondDayOfMonth = DateUtils.addDays(firstDayOfMonth, 1);
    Date thirdDayOfMonth = DateUtils.addDays(firstDayOfMonth, 2);
    Date firstDayOfPreviousMonth = DateUtils.addMonths(firstDayOfMonth, -1);
    Date secondDayOfPreviousMonth = DateUtils.addDays(firstDayOfPreviousMonth, 1);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);
    Date todayTruncatedToDay = DateUtils.truncate(now, Calendar.DATE);

    LineItem lineItem =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(101)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem2 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem3 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem4 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem5 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem6 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId2)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem7 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1000)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    LineItem lineItem8 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1002)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    LineItem lineItem9 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(1005)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(secondDayOfPreviousMonth)
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .id(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice2 =
        new Invoice.Builder()
            .id(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId2)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice3 =
        new Invoice.Builder()
            .id(invoiceId3)
            .startDate(firstDayOfPreviousMonth)
            .endDate(firstDayOfMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    OrgPlan orgPlan =
        new OrgPlan.Builder()
            .planType(NDS_DEVELOPER)
            .elasticInvoicingActivationDate(null)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .activated(new Date())
            .orgId(orgId)
            .build();

    OrgPlan orgPlan2 =
        new OrgPlan.Builder()
            .planType(NDS_PRO)
            .elasticInvoicingActivationDate(null)
            .startDate(firstDayOfPreviousMonth)
            .endDate(firstDayOfMonth)
            .activated(new Date())
            .orgId(orgId2)
            .build();

    OrgPlan orgPlan3 =
        new OrgPlan.Builder()
            .planType(NDS_PLATINUM)
            .elasticInvoicingActivationDate(new Date())
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId2)
            .activated(firstDayOfNextMonth)
            .build();

    OrgPlan orgPlan4 =
        new OrgPlan.Builder()
            .planType(null)
            .elasticInvoicingActivationDate(new Date())
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(null)
            .activated(firstDayOfNextMonth)
            .build();

    _orgPlanDao.insertReplicaSafe(orgPlan);
    _orgPlanDao.insertReplicaSafe(orgPlan2);
    _orgPlanDao.insertReplicaSafe(orgPlan3);
    _orgPlanDao.insertReplicaSafe(orgPlan4);
    List<LineItem> lineItems =
        Arrays.asList(
            lineItem, lineItem2, lineItem3, lineItem4, lineItem5, lineItem6, lineItem7, lineItem8,
            lineItem9);
    lineItems.forEach((l) -> _lineItemDao.save(l));
    _invoiceDao.save(invoice);
    _invoiceDao.save(invoice2);
    _invoiceDao.save(invoice3);

    _aggregator.emitMetrics();

    verifyAmountBilledGauge(true, false, NDS_DEVELOPER, 308.0);
    verifyAmountBilledGauge(false, false, NDS_DEVELOPER, 0);
    verifyAmountBilledGauge(false, true, NDS_DEVELOPER, 0);
    verifyAmountBilledGauge(true, true, NDS_DEVELOPER, 0);
    verifyAmountBilledGauge(false, true, NDS_PLATINUM, 307.0);
    verifyAmountBilledGauge(true, false, NDS_PLATINUM, 0);
    verifyAmountBilledGauge(false, false, NDS_PLATINUM, 0);
    verifyAmountBilledGauge(true, true, NDS_PLATINUM, 0);

    verifyCountActiveGauge(true, false, NDS_DEVELOPER, 1.0);
    verifyCountActiveGauge(true, true, NDS_DEVELOPER, 0);
    verifyCountActiveGauge(false, false, NDS_DEVELOPER, 0);
    verifyCountActiveGauge(false, true, NDS_DEVELOPER, 0);
    verifyCountActiveGauge(false, true, NDS_PLATINUM, 1.0);
    verifyCountActiveGauge(true, false, NDS_PLATINUM, 0);
    verifyCountActiveGauge(false, false, NDS_PLATINUM, 0);
    verifyCountActiveGauge(true, true, NDS_PLATINUM, 0);

    verifyAllOtherGaugesNotIncremented(Arrays.asList(NDS_DEVELOPER, NDS_PLATINUM));
  }

  @Test
  public void testEmitMetrics_multiplePrepaidPlanTypes_elasticNonElastic()
      throws BillingMetricFailureException {
    ObjectId invoiceId = new ObjectId();
    ObjectId invoiceId2 = new ObjectId();
    ObjectId invoiceId3 = new ObjectId();
    ObjectId invoiceId4 = new ObjectId();
    Date now = new Date();
    ObjectId orgId = new ObjectId();
    ObjectId orgId2 = new ObjectId();
    ObjectId orgId3 = new ObjectId();
    ObjectId orgId4 = new ObjectId();
    Date firstDayOfMonth = DateUtils.truncate(now, Calendar.MONTH);
    Date secondDayOfMonth = DateUtils.addDays(firstDayOfMonth, 1);
    Date thirdDayOfMonth = DateUtils.addDays(firstDayOfMonth, 2);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);

    LineItem lineItem =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(101)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem2 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem3 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem4 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem5 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(102)
            .invoiceId(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem6 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(105)
            .invoiceId(invoiceId2)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem10 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(10000)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem11 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(10002)
            .invoiceId(invoiceId3)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem12 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(10005)
            .invoiceId(invoiceId3)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    LineItem lineItem13 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100001)
            .invoiceId(invoiceId4)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem14 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100002)
            .invoiceId(invoiceId4)
            .startDate(firstDayOfMonth)
            .endDate(secondDayOfMonth)
            .build();

    LineItem lineItem15 =
        new LineItem.Builder()
            .sku(SKU.NDS_ENTITLEMENTS)
            .totalPriceCents(100005)
            .invoiceId(invoiceId4)
            .startDate(secondDayOfMonth)
            .endDate(thirdDayOfMonth)
            .build();

    Invoice invoice =
        new Invoice.Builder()
            .id(invoiceId)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice2 =
        new Invoice.Builder()
            .id(invoiceId2)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId2)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice3 =
        new Invoice.Builder()
            .id(invoiceId3)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId3)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    Invoice invoice4 =
        new Invoice.Builder()
            .id(invoiceId4)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .orgId(orgId4)
            .type(Type.MONTHLY)
            .status(Status.PENDING)
            .build();

    //    PrepaidPlan prepaidPlan =
    //        new PrepaidPlan.Builder(PrepaidPlanType.NDS_SELF_SERVE_PRO, List.of())
    //            .elasticInvoicing(false)
    //            .startDate(firstDayOfMonth)
    //            .endDate(firstDayOfNextMonth)
    //            .build();
    //
    //    PrepaidPlan prepaidPlan2 =
    //        new PrepaidPlan.Builder(PrepaidPlanType.NDS_SELF_SERVE_PRO, List.of())
    //            .elasticInvoicing(true)
    //            .startDate(firstDayOfMonth)
    //            .endDate(firstDayOfNextMonth)
    //            .build();
    //
    //    PrepaidPlan prepaidPlan3 =
    //        new PrepaidPlan.Builder(PrepaidPlanType.NDS_ENTERPRISE, List.of())
    //            .elasticInvoicing(true)
    //            .startDate(firstDayOfMonth)
    //            .endDate(firstDayOfNextMonth)
    //            .build();
    //
    //    PrepaidPlan prepaidPlan4 =
    //        new PrepaidPlan.Builder(PrepaidPlanType.NDS_ENTERPRISE, List.of())
    //            .elasticInvoicing(false)
    //            .startDate(firstDayOfMonth)
    //            .endDate(firstDayOfNextMonth)
    //            .build();

    OrgPlan orgPlan =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .activated(new Date())
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .planType(NDS_SELF_SERVE_PRO)
            .orgId(orgId)
            .build();

    OrgPlan orgPlan2 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(new Date())
            .activated(new Date())
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .planType(NDS_SELF_SERVE_PRO)
            .orgId(orgId2)
            .build();

    OrgPlan orgPlan3 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(new Date())
            .activated(new Date())
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .planType(NDS_ENTERPRISE)
            .orgId(orgId3)
            .build();

    OrgPlan orgPlan4 =
        new OrgPlan.Builder()
            .elasticInvoicingActivationDate(null)
            .startDate(firstDayOfMonth)
            .endDate(firstDayOfNextMonth)
            .planType(NDS_ENTERPRISE)
            .orgId(orgId4)
            .activated(new Date())
            .build();

    _orgPlanDao.insertReplicaSafe(orgPlan);
    _orgPlanDao.insertReplicaSafe(orgPlan2);
    _orgPlanDao.insertReplicaSafe(orgPlan3);
    _orgPlanDao.insertReplicaSafe(orgPlan4);
    List<LineItem> lineItems =
        Arrays.asList(
            lineItem,
            lineItem2,
            lineItem3,
            lineItem4,
            lineItem5,
            lineItem6,
            lineItem10,
            lineItem11,
            lineItem12,
            lineItem13,
            lineItem14,
            lineItem15);
    lineItems.forEach((l) -> _lineItemDao.save(l));
    _invoiceDao.save(invoice);
    _invoiceDao.save(invoice2);
    _invoiceDao.save(invoice3);
    _invoiceDao.save(invoice4);

    _aggregator.emitMetrics();

    verifyAmountBilledGauge(true, false, NDS_SELF_SERVE_PRO, 308.0);
    verifyAmountBilledGauge(true, true, NDS_SELF_SERVE_PRO, 307.0);
    verifyAmountBilledGauge(false, false, NDS_SELF_SERVE_PRO, 0);
    verifyAmountBilledGauge(false, true, NDS_SELF_SERVE_PRO, 0);
    verifyAmountBilledGauge(false, true, NDS_ENTERPRISE, 30007.0);
    verifyAmountBilledGauge(false, false, NDS_ENTERPRISE, 300008.0);
    verifyAmountBilledGauge(true, false, NDS_ENTERPRISE, 0);
    verifyAmountBilledGauge(true, true, NDS_ENTERPRISE, 0);

    verifyCountActiveGauge(true, false, NDS_SELF_SERVE_PRO, 1.0);
    verifyCountActiveGauge(true, true, NDS_SELF_SERVE_PRO, 1.0);
    verifyCountActiveGauge(false, false, NDS_SELF_SERVE_PRO, 0);
    verifyCountActiveGauge(false, true, NDS_SELF_SERVE_PRO, 0);
    verifyCountActiveGauge(false, true, NDS_ENTERPRISE, 1.0);
    verifyCountActiveGauge(false, false, NDS_ENTERPRISE, 1.0);
    verifyCountActiveGauge(true, false, NDS_ENTERPRISE, 0);
    verifyCountActiveGauge(true, true, NDS_ENTERPRISE, 0);

    verifyAllOtherGaugesNotIncremented(Arrays.asList(NDS_SELF_SERVE_PRO, NDS_ENTERPRISE));
  }

  private void verifyCountActiveGauge(
      boolean isSelfServe, Boolean isElastic, PlanType planType, double expectedValue) {
    String[] labels = new String[] {PROM_LABEL_PLAN_TYPE, PROM_LABEL_ELASTIC};
    String[] values = new String[] {planType.name(), isElastic.toString()};
    String name = isSelfServe ? ACTIVE_SELF_SERVE_COUNT_NAME : ACTIVE_SALES_SOLD_COUNT_NAME;
    Double gaugeValue = CollectorRegistry.defaultRegistry.getSampleValue(name, labels, values);
    if (gaugeValue == null) {
      gaugeValue = 0.0;
    }

    assertEquals(expectedValue, gaugeValue, 0);
  }

  private void verifyCountGaugeNotSet(boolean isSelfServe, Boolean isElastic, PlanType planType) {
    String[] labels = new String[] {PROM_LABEL_PLAN_TYPE, PROM_LABEL_ELASTIC};
    String[] values = new String[] {planType.name(), isElastic.toString()};
    String name = isSelfServe ? ACTIVE_SELF_SERVE_COUNT_NAME : ACTIVE_SALES_SOLD_COUNT_NAME;
    assertNull(CollectorRegistry.defaultRegistry.getSampleValue(name, labels, values));
  }

  private void verifyAmountBilledGauge(
      boolean isSelfServe, Boolean isElastic, PlanType planType, double expectedValue) {
    String[] labels = new String[] {PROM_LABEL_PLAN_TYPE, PROM_LABEL_ELASTIC};
    String[] values = new String[] {planType.name(), isElastic.toString()};
    String name =
        isSelfServe
            ? BILLED_SUPPORT_SELF_SERVE_CURRENT_MONTH_NAME
            : BILLED_SUPPORT_SALES_SOLD_CURRENT_MONTH_NAME;
    Double gaugeValue = CollectorRegistry.defaultRegistry.getSampleValue(name, labels, values);
    if (gaugeValue == null) {
      gaugeValue = 0.0;
    }
    assertEquals(expectedValue, gaugeValue, 0);
  }

  private void verifyAmountBilledGauge(OrgPlan pPrepaidPlan, List<LineItem> lineItems) {
    Date firstDayOfMonth = DateUtils.truncate(new Date(), Calendar.MONTH);
    Date firstDayOfNextMonth = DateUtils.addMonths(firstDayOfMonth, 1);
    boolean selfServe =
        PrepaidPlanType.getSelfServePlanTypes().contains(pPrepaidPlan.getPlanType());
    boolean elastic = pPrepaidPlan.getElasticInvoicingActivationDate() != null;
    PlanType planType = pPrepaidPlan.getPlanType();
    long expectedValue =
        lineItems.stream()
            .filter((l) -> l.getSku() == SKU.NDS_ENTITLEMENTS) // get all support line items
            .filter(
                (l) ->
                    !l.getStartDate().before(firstDayOfMonth)
                        && !l.getEndDate()
                            .after(firstDayOfNextMonth)) // filter out line items from other months
            .filter(
                (l) ->
                    !l.getStartDate().before(pPrepaidPlan.getStartDate())
                        && (!l.getEndDate().after(pPrepaidPlan.getEndDate())
                            || pPrepaidPlan.getEndDate()
                                == null)) // filter out line items that were not active during this
            // prepaid plan's active period
            .mapToLong(LineItem::getTotalPriceCents)
            .sum();

    assertTrue(expectedValue > 0);
    verifyAmountBilledGauge(selfServe, elastic, planType, expectedValue);
  }

  private void verifyAllOtherGaugesNotIncremented(List<PlanType> planTypes) {
    for (PlanType planType : PlanType.values()) {
      if (!planTypes.contains(planType)) {
        verifyAmountBilledGauge(true, true, planType, 0);
        verifyAmountBilledGauge(true, false, planType, 0);
        verifyAmountBilledGauge(false, true, planType, 0);
        verifyAmountBilledGauge(false, false, planType, 0);
        verifyCountActiveGauge(true, true, planType, 0);
        verifyCountActiveGauge(true, false, planType, 0);
        verifyCountActiveGauge(false, true, planType, 0);
        verifyCountActiveGauge(false, false, planType, 0);
      }
    }
  }
}
