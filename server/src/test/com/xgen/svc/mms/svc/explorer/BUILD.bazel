load("//server/src/test:rules.bzl", "library_package", "test_package")

library_package(
    name = "explorer",
    visibility = ["//server/src/test:__subpackages__"],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/appconfig/_public/config",
        "//server/src/main/com/xgen/cloud/atm/agentjobs",
        "//server/src/main/com/xgen/cloud/common/explorer",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_private/dao",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_public/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/dao/explorer",
        "//server/src/main/com/xgen/svc/mms/model/explorer",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:junit_junit",
    ],
)

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = False,
    deps = [
        ":explorer",
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/common/appsettings/_public/svc",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/explorer",
        "//server/src/main/com/xgen/cloud/common/featureFlag/_public/model",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_private/dao",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_public/model",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/explorer/activity",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/dao/explorer",
        "//server/src/main/com/xgen/svc/mms/model/explorer",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/unit/com/xgen/svc/mms/util",
        "@maven//:junit_junit",
    ],
)
