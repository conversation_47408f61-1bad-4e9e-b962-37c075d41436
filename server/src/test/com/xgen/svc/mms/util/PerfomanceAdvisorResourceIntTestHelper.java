package com.xgen.svc.mms.util;

import static com.xgen.cloud.performanceadvisor.util.PerformanceAdvisorTestUtils.waitForFunctionToEqual;
import static com.xgen.svc.common.TestDataUtils.populateCollectionFromJsonFtlFile;
import static com.xgen.svc.mms.svc.performanceadvisor.schema.CallToAction.Type.REDUCE_LOOKUP_OPS;
import static com.xgen.svc.mms.svc.performanceadvisor.schema.CallToAction.Type.REDUCE_NUMBER_OF_NAMESPACES;
import static com.xgen.svc.mms.svc.performanceadvisor.schema.CallToAction.Type.REMOVE_UNNECESSARY_INDEXES;
import static com.xgen.svc.mms.util.UnitTestUtils.o;
import static java.time.Duration.ofMinutes;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.DAYS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.common.jobqueue._private.dao.AgentJobsProcessorDao;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob.Command.Type;
import com.xgen.cloud.common.model._public.annotation.GenEncryptMetadata;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.util._public.compression.GZipCompressionUtils;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.logs._private.dao.SlowQueryLogDao;
import com.xgen.cloud.monitoring.metrics._private.dao.CollStatsLatencyNamespaceStatsDao;
import com.xgen.cloud.monitoring.metrics._public.model.ClusterView;
import com.xgen.cloud.monitoring.metrics._public.model.CollStatsLatencyNamespaceRankStats;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.performanceadvisor._private.dao.CollectionStatsDao;
import com.xgen.cloud.performanceadvisor._private.dao.ShardingConfigDao;
import com.xgen.cloud.performanceadvisor._public.model.CollectionStats;
import com.xgen.cloud.performanceadvisor._public.model.ShardingConfig;
import com.xgen.cloud.performanceadvisor._public.model.ShardingConfig.Config;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.dao.explorer.DataExplorerOpResponse;
import com.xgen.svc.mms.dao.explorer.DataExplorerResponseDao;
import com.xgen.svc.mms.model.performanceadvisor.PerformanceAdvisorResponseMetadata;
import com.xgen.svc.mms.model.performanceadvisor.SlowQueryLogEntry;
import com.xgen.svc.mms.res.view.performanceadvisor.CreateIndexesResponseView;
import com.xgen.svc.mms.res.view.performanceadvisor.PublicRecommendationItemView;
import com.xgen.svc.mms.res.view.performanceadvisor.PublicSchemaAdvisorView;
import com.xgen.svc.mms.res.view.performanceadvisor.SchemaAdvisorAffectedNamespaceView;
import com.xgen.svc.mms.svc.explorer.DataExplorerEncodingUtils;
import com.xgen.svc.mms.svc.performanceadvisor.schema.AntiPatternAccumulator;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import org.bson.BasicBSONObject;
import org.bson.types.BSONTimestamp;
import org.bson.types.BasicBSONList;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;

public class PerfomanceAdvisorResourceIntTestHelper {
  public static final String NAMESPACE = "test.foo";
  public static final String NAMESPACE_2 = "test.goo";
  public static final String NAMESPACE_TIMESERIES = "time.time";
  public static final Date NOW = new Date();
  public static final long LEFT_BOUNDARY = NOW.getTime() - NOW.getTime() % 600000 - 60 * 60 * 1000;
  public static final Date TIMESTAMP_START = new Date(LEFT_BOUNDARY + 3 * 60 * 1000);

  public static final SimpleDateFormat TIMESTAMP_FORMAT =
      new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");

  public static BasicBSONObject getIndex(
      final BasicDBObject pKey, final String pName, final String pNs) {
    return getIndex(pKey, pName, pNs, 0, 0);
  }

  public static BasicBSONObject getIndexWithSpecialProperties(
      final BasicDBObject pKey,
      final String pName,
      final String pNs,
      final Map<String, Object> pIndexProperties) {
    final BasicBSONObject baseObject = getIndex(pKey, pName, pNs, 0, 0);
    pIndexProperties.keySet().forEach(prop -> baseObject.append(prop, pIndexProperties.get(prop)));
    return baseObject;
  }

  public static BasicBSONObject getIndex(
      final BasicDBObject pKey,
      final String pName,
      final String pNs,
      final int pOps,
      final int pDaysSince) {
    return new BasicBSONObject("v", 2)
        .append("key", pKey)
        .append("name", pName)
        .append("ns", pNs)
        .append("indexSize", 20480)
        .append(
            "indexAccesses",
            new BasicBSONObject[] {
              new BasicDBObject("ops", pOps)
                  .append("since", Date.from(Instant.now().minus(pDaysSince, DAYS)))
            });
  }

  public static String getEncryptedPerformanceAdvisorResponseString() throws Exception {

    final CreateIndexesResponseView performanceAdvisorResponse =
        new CreateIndexesResponseView(
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            List.of("ns1", "ns2", "ns3"),
            1,
            new PerformanceAdvisorResponseMetadata(),
            null,
            null,
            null,
            Collections.emptyList());

    final String performanceAdvisorResponseString =
        new ObjectMapper().writeValueAsString(performanceAdvisorResponse);
    final byte[] StrCompressed = GZipCompressionUtils.gzipStr(performanceAdvisorResponseString);
    final String StrEncodedAndCompressed = Base64.getEncoder().encodeToString(StrCompressed);
    @GenEncryptMetadata(
        DB = "feedback",
        Collection = "performance-advisor",
        Field = "performanceAdvisorResponse")
    final String encryptedPerformanceAdvisorFeedbackIdResponse =
        EncryptionUtils.genEncryptStr(StrEncodedAndCompressed);

    return encryptedPerformanceAdvisorFeedbackIdResponse;
  }

  public static AutomationJobMockResponseRunner setUpCCPASchemaAdvice(
      final HostCluster pHostCluster,
      final Group pGroup,
      final DataExplorerResponseDao pDataExplorerResponseDao,
      final AgentJobsProcessorDao pAgentJobsProcessorDao,
      final AppUser pUser,
      final UserDao pUserDao,
      final SlowQueryLogDao pSlowQueryLogDao,
      final String pHostname)
      throws Exception {
    BasicBSONObject index1 =
        PerfomanceAdvisorResourceIntTestHelper.getIndexWithSpecialProperties(
            new BasicDBObject("_id", 1), "_id_", NAMESPACE, Map.of());
    BasicBSONObject index2 =
        PerfomanceAdvisorResourceIntTestHelper.getIndexWithSpecialProperties(
            new BasicDBObject("uniqueAndTtlFluff", 1),
            "uniqueAndTtlFluff",
            NAMESPACE,
            Map.of("unique", true, "expireAfterSeconds", 600));
    BasicBSONObject timeseriesIndex1 =
        PerfomanceAdvisorResourceIntTestHelper.getIndexWithSpecialProperties(
            new BasicDBObject("_id", 1), "_id_", NAMESPACE_TIMESERIES, Map.of());
    BasicBSONObject timeseriesIndex2 =
        PerfomanceAdvisorResourceIntTestHelper.getIndexWithSpecialProperties(
            new BasicDBObject("uniqueAndTtlFluff", 1),
            "uniqueAndTtlFluff",
            NAMESPACE_TIMESERIES,
            Map.of("unique", true, "expireAfterSeconds", 600));
    List<BasicBSONObject> indexes = new ArrayList<>();
    List<BasicBSONObject> timeseriesIndexes = new ArrayList<>();
    indexes.add(index1);
    indexes.add(index2);
    timeseriesIndexes.add(timeseriesIndex1);
    timeseriesIndexes.add(timeseriesIndex2);
    for (int i = 0; i < 30; i++) {
      BasicBSONObject index =
          PerfomanceAdvisorResourceIntTestHelper.getIndex(
              new BasicDBObject("singleShardUnusedFluff" + i, 1),
              "singleShardUnusedFluff" + i,
              NAMESPACE,
              0,
              10);
      indexes.add(index);
    }
    final Map<String, List<BasicBSONObject>> nsToIndex = new HashMap<>();
    nsToIndex.put(NAMESPACE, indexes);
    nsToIndex.put(NAMESPACE_TIMESERIES, timeseriesIndexes);

    List<String> shards = pHostCluster.getShardIds().stream().toList();

    Map<String, String> hostNameToHostId = new HashMap<>();
    Map<String, Map<String, List<BasicBSONObject>>> hostNameToNsToIndex = new HashMap<>();

    // Populate data from shard 1
    for (Host host : pHostCluster.getHostsByShardId(shards.get(0))) {
      hostNameToHostId.put(host.getName(), host.getId());
      hostNameToNsToIndex.put(host.getName(), new HashMap<>(nsToIndex));
    }

    // Populate data from shard 2
    for (Host host : pHostCluster.getHostsByShardId(shards.get(1))) {
      hostNameToHostId.put(host.getName(), host.getId());
      hostNameToNsToIndex.put(host.getName(), new HashMap<>(nsToIndex));
    }

    // Populate mongos
    for (Host host : pHostCluster.getMongosHosts()) {
      hostNameToHostId.put(host.getName(), host.getId());
      hostNameToNsToIndex.put(host.getName(), new HashMap<>(nsToIndex));
    }

    // Populate top namespaces
    // Same processId across documents indicates that all docs are from single start-up period
    final String dummyProcessId = "d57aba50";

    // Get composed _id to set in backing documents for first and second timestamp for Host and
    // primary ClusterView
    final String composedIdCluster =
        CollStatsLatencyNamespaceRankStats.getComposedId(
            pGroup.getId(),
            pHostCluster.getClusterId().toString(),
            ClusterView.PRIMARY,
            Date.from(TIMESTAMP_START.toInstant().plusSeconds(5 * 60)).getTime(), // xx:08:00,
            dummyProcessId);

    final Map<String, Object> ftlParamsForNamespaceStats =
        Map.of(
            "composedIdCluster",
            composedIdCluster,
            "expirationDate",
            TIMESTAMP_START.toInstant().plus(7, DAYS).toEpochMilli());

    // Populate namespaceStats collection so we generate deltas when ranking documents
    populateCollectionFromJsonFtlFile(
        "mms/svc/ping/CollStatsLatencyMetrics/namespaceStatsDocsForHottestNamespaces.json.ftl",
        ftlParamsForNamespaceStats,
        CollStatsLatencyNamespaceStatsDao.DB_NAME,
        CollStatsLatencyNamespaceStatsDao.COLLECTION_NAME);

    final var mockDataExplorerJobRunner =
        new AutomationJobMockResponseRunner(
            pDataExplorerResponseDao,
            pAgentJobsProcessorDao,
            // hostId is (as far as i can tell) thrown away from the response of
            // Index Stats Job, so we can just set the response to any random hostId:
            pHostCluster.getMongosHosts().get(0).getId(),
            nsToIndex,
            hostNameToNsToIndex,
            hostNameToHostId,
            List.of(NAMESPACE, NAMESPACE_TIMESERIES),
            true,
            pHostCluster.getClusterId(),
            Set.of(NAMESPACE_TIMESERIES));
    mockDataExplorerJobRunner.start();

    pUser.assignRole((RoleAssignment.forGroup(Role.GROUP_READ_ONLY, pGroup.getId())));
    pUserDao.update(pUser);

    // Generate mock data
    List<SlowQueryLogEntry> slowQueries = new ArrayList<>();
    final int LOG_THRESHOLD = 200;
    final String lookupLog =
        " I COMMAND  [conn466] command test.foo appName: \"MongoDB"
            + " Shell\" command: aggregate { aggregate: \"woofer\", pipeline: [ { $match: {"
            + " breedName: \"wzsHF\" } }, { $lookup: { from: \"doggo\", localField: \"breedId\","
            + " foreignField: \"breedId\", as: \"doggos\" } } ], cursor: {}, $db: \"smol\" }"
            + " planSummary: COLLSCAN keysExamined:100 docsExamined:200 cursorExhausted:1"
            + " numYields:7 nreturned:1 reslen:84 locks:{ Global: { acquireCount: { r: 22 } },"
            + " Database: { acquireCount: { r: 1 } }, Collection: { acquireCount: { r: 10 } } }"
            + " protocol:op_command 2ms";
    final Date timestamp1 = Date.from(now().minus(ofMinutes(23)));
    for (int i = 0; i < LOG_THRESHOLD; i++) {
      SlowQueryLogEntry slowLog = generateEntry(lookupLog, pGroup, timestamp1, null, pHostname);
      slowQueries.add(slowLog);
    }

    // Add mock slow logs to the database
    pSlowQueryLogDao.insertIa(slowQueries);
    waitForFunctionToEqual(
        LOG_THRESHOLD,
        () ->
            pSlowQueryLogDao
                .findAbbrvEntriesForTesting(
                    Set.of(hostNameToHostId.get(pHostname)),
                    new HashSet<>(),
                    new HashSet<>(),
                    now().minus(1, DAYS),
                    now(),
                    false)
                .size(),
        "Asserting slow logs");

    MmsFactory.createAutomationAgentAuditEntry(
        pGroup,
        pHostname,
        AgentVersion.MIN_LISTINDEXSTATS_MULTIPLE_NAMESPACES_VERSION.toString(),
        NOW);

    return mockDataExplorerJobRunner;
  }

  public static SlowQueryLogEntry generateEntry(
      final String pLogLine,
      final Group pGroup,
      final Date pTimestamp,
      final String pReplicaState,
      final String pHostname)
      throws Exception {
    final String timestampStr = TIMESTAMP_FORMAT.format(pTimestamp);
    final String logLine = timestampStr + pLogLine;

    return SlowQueryLogEntry.fromLogMessage(
        logLine,
        pTimestamp,
        pTimestamp,
        1,
        pGroup.getId(),
        pHostname,
        27017,
        "3.6.0",
        null,
        true,
        null,
        pReplicaState != null ? Optional.of(pReplicaState) : Optional.empty());
  }

  public static void assertCCPASchemaAdvicePublicApiResponse(
      final AtomicReference<JSONObject> pResp) {
    assertNotNull(pResp);

    final JSONArray recommendations =
        pResp.get().getJSONArray(PublicSchemaAdvisorView.RECOMMENDATIONS_FIELD);
    assertEquals(recommendations.length(), 3);
    for (int i = 0; i < recommendations.length(); i++) {
      JSONObject cta = recommendations.getJSONObject(i);
      JSONObject affectedNs =
          cta.getJSONArray(PublicRecommendationItemView.AFFECTED_NAMESPACES_FIELD).getJSONObject(0);
      String ctaType = cta.getString(PublicRecommendationItemView.RECOMMENDATION_FIELD);
      if (ctaType.equals(REDUCE_NUMBER_OF_NAMESPACES.name())) {
        assertEquals(
            affectedNs.get(SchemaAdvisorAffectedNamespaceView.NAMESPACE_FIELD), JSONObject.NULL);
        assertEquals(
            cta.get(PublicRecommendationItemView.DESCRIPTION_FIELD),
            PublicRecommendationItemView.getDescription(REDUCE_NUMBER_OF_NAMESPACES.name()));
      } else if (ctaType.equals(REMOVE_UNNECESSARY_INDEXES.name())) {
        assertEquals(
            affectedNs.getString(SchemaAdvisorAffectedNamespaceView.NAMESPACE_FIELD), NAMESPACE);
        assertEquals(
            cta.get(PublicRecommendationItemView.DESCRIPTION_FIELD),
            PublicRecommendationItemView.getDescription(REMOVE_UNNECESSARY_INDEXES.name()));
      } else if (ctaType.equals(REDUCE_LOOKUP_OPS.name())) {
        assertEquals(
            affectedNs.getString(SchemaAdvisorAffectedNamespaceView.NAMESPACE_FIELD), NAMESPACE);
        assertEquals(
            cta.get(PublicRecommendationItemView.DESCRIPTION_FIELD),
            PublicRecommendationItemView.getDescription(REDUCE_LOOKUP_OPS.name()));
      } else {
        throw new AssertionError("Unexpected callToAction: " + cta.getString("callToAction"));
      }
    }
  }

  public static void assertCCPASchemaAdviceResponse(
      final AtomicReference<JSONObject> pResp, final HostCluster pShardedCluster) {
    assertNotNull(pResp);
    assertEquals(pShardedCluster.getClusterId().toString(), pResp.get().getString("clusterId"));

    final JSONArray ctas = pResp.get().getJSONArray("callToActions");
    for (int i = 0; i < ctas.length(); i++) {
      JSONObject cta = ctas.getJSONObject(i);
      if (cta.get("callToAction").equals(REDUCE_NUMBER_OF_NAMESPACES.name())) {
        assertTrue(cta.getBoolean("hasTriggered"));
        assertTrue(cta.getJSONArray("affectedItems").getJSONObject(0).getBoolean("hasTriggered"));
      } else if (cta.get("callToAction").equals(REMOVE_UNNECESSARY_INDEXES.name())) {
        assertTrue(cta.getBoolean("hasTriggered"));
        assertEquals(cta.getJSONArray("affectedItems").getJSONObject(0).get("ns"), NAMESPACE);
        assertTrue(cta.getJSONArray("affectedItems").getJSONObject(0).getBoolean("hasTriggered"));
      } else if (cta.get("callToAction").equals(REDUCE_LOOKUP_OPS.name())) {
        assertTrue(cta.getBoolean("hasTriggered"));
        assertEquals(cta.getJSONArray("affectedItems").getJSONObject(0).get("ns"), NAMESPACE);
        assertTrue(cta.getJSONArray("affectedItems").getJSONObject(0).getBoolean("hasTriggered"));
      } else {
        throw new AssertionError("Unexpected callToAction: " + cta.getString("callToAction"));
      }
    }
  }

  public static class ShardingJobMockResponseRunner extends Thread {
    private final AgentJobsProcessorDao _agentJobsProcessorDao;
    private final ShardingConfigDao _shardingConfigDao;
    private final ObjectId _clusterId;
    private final ObjectId _groupId;
    private final List<Config> _configs;
    private volatile boolean stop = false;

    public ShardingJobMockResponseRunner(
        final AgentJobsProcessorDao pAgentJobsProcessorDao,
        final ShardingConfigDao pShardingConfigDao,
        final ObjectId pClusterId,
        final ObjectId pGroupId,
        final List<Config> pConfigs) {
      _agentJobsProcessorDao = pAgentJobsProcessorDao;
      _shardingConfigDao = pShardingConfigDao;
      _clusterId = pClusterId;
      _groupId = pGroupId;
      _configs = pConfigs;
    }

    @Override
    public void run() {
      try {
        while (!stop) {
          final List<DBObject> jobs = _agentJobsProcessorDao.findAll().toArray();
          for (final DBObject job : jobs) {
            final String command = (String) job.get("command");
            if (!command.equals(Type.ShardingConfig.toString())) {
              continue;
            }

            final ObjectId requestId = (ObjectId) job.get("_id");
            final String status = (String) job.get("status");
            // don't repeat insertion if the request has already completed
            if (!status.equals("COMPLETED")) {
              ShardingConfig response =
                  new ShardingConfig(
                      _clusterId.toString(),
                      _groupId,
                      requestId,
                      Date.from(Instant.now()),
                      _configs);
              _shardingConfigDao.insert(response);
              waitForFunctionToEqual(
                  1L, () -> _shardingConfigDao.countAll(), "Asserting shard configs");
              completeJob(requestId);
            }
          }
          Thread.sleep(1_000);
        }

      } catch (final InterruptedException e) {
        System.out.println("Thread got interrupted. Aborting.");
      }
    }

    private void completeJob(final ObjectId pRequestId) {
      final AgentJob agentJob = _agentJobsProcessorDao.findById(pRequestId);
      agentJob.updateStatus(AgentJob.Status.COMPLETED, Optional.empty());
      _agentJobsProcessorDao.putJob(agentJob);
    }
  }

  /**
   * Thread to replace automation agent job responses. Checks if there is a new DATA_EXPLORER_OP
   * (listIndexStats) agent job that needs to be processed, parses the listIndexStats job and
   * inserts mock index data for the appropriate namespace.
   */
  public static class AutomationJobMockResponseRunner extends Thread {

    private static final int SLEEP_MS = 1_000;
    private volatile boolean stop = false;

    private final DataExplorerResponseDao _dataExplorerResponseDao;
    private final AgentJobsProcessorDao _agentJobsProcessorDao;
    private final String _hostId;
    private final Map<String, Map<String, List<BasicBSONObject>>> _hostNameToNsToIndex;
    private final Map<String, String> _hostNameToHostId;
    private final Map<String, List<BasicBSONObject>> _nsToIndex;
    private final List<String> _topNamespaces;
    private final boolean _listIndexStatsHasMultipleNamespaces;
    private final ObjectId _clusterId;
    private final Set<String> _timeSeriesNamespaces;

    public AutomationJobMockResponseRunner(
        final DataExplorerResponseDao pDataExplorerResponseDao,
        final AgentJobsProcessorDao pAgentJobsProcessorDao,
        final String pHostId,
        final Map<String, List<BasicBSONObject>> pNsToIndex,
        final Map<String, Map<String, List<BasicBSONObject>>> pHostNameToNsToIndex,
        final Map<String, String> pHostNameToHostId,
        final List<String> pTopNamespaces,
        final boolean pListIndexStatsHasMultipleNamespaces,
        final ObjectId pClusterId,
        final Set<String> pTimeSeriesNamespaces) {
      _dataExplorerResponseDao = pDataExplorerResponseDao;
      _agentJobsProcessorDao = pAgentJobsProcessorDao;
      _hostId = pHostId;
      _hostNameToNsToIndex = pHostNameToNsToIndex;
      _hostNameToHostId = pHostNameToHostId;
      _nsToIndex = pNsToIndex;
      _topNamespaces = pTopNamespaces;
      _listIndexStatsHasMultipleNamespaces = pListIndexStatsHasMultipleNamespaces;
      _clusterId = pClusterId;
      _timeSeriesNamespaces = pTimeSeriesNamespaces;
    }

    @Override
    public void run() {
      try {
        while (!stop) {
          final List<DBObject> jobs = _agentJobsProcessorDao.findAll().toArray();
          for (final DBObject job : jobs) {
            final String command = (String) job.get("command");
            if (!command.equals(Type.DataExplorerOp.toString())) {
              continue;
            }
            final ObjectId requestId = (ObjectId) job.get("_id");
            final DBObject params = (DBObject) job.get("params");
            final DBObject op = (DBObject) params.get("op");
            final String db = (String) op.get("db");
            final String collection = (String) op.get("collection");
            final String status = (String) job.get("status");
            final String type = (String) op.get("type");
            final ObjectId clusterId = (ObjectId) params.get("clusterId");
            final ObjectId sniGroupId = (ObjectId) params.get("sniGroupId");
            final ObjectId groupId =
                sniGroupId == null ? (ObjectId) job.get("groupId") : sniGroupId;
            final String hostname = (String) params.get("hostname");

            // don't repeat insertion if the request has already completed
            if (!status.equals("COMPLETED")) {
              switch (type) {
                case "listIndexStats":
                  if (!_listIndexStatsHasMultipleNamespaces) {
                    createListIndexStatsResponse(requestId, groupId, clusterId, db, collection);
                  } else {
                    createListIndexStatsWithMultipleNamespacesResponse(
                        requestId, hostname, groupId);
                  }
                  break;
                case "collStats":
                  createCollStatsResponse(requestId, groupId, db, collection, hostname);
                  break;
                case "top":
                  createTopResponse(requestId, groupId);
                  break;
                case "listNamespaces":
                  createListNamespacesResponse(requestId, groupId, clusterId);
                case "listAllInProgressIndexes":
                  createListAllInProgressIndexesResponse(requestId, groupId, clusterId);
                  break;
                case "find":
                  completeJob(requestId, false);
                  break;
                case "createIndex":
                  createIndex(requestId);
                  break;
                default:
                  throw new RuntimeException("Invalid type " + type);
              }
            }
          }
          Thread.sleep(SLEEP_MS);
        }

      } catch (final InterruptedException e) {
        System.out.println("Thread got interrupted. Aborting.");
      }
    }

    public void createListIndexStatsResponse(
        final ObjectId requestId,
        final ObjectId groupId,
        final ObjectId clusterId,
        final String db,
        final String collection) {
      try {
        final String key = String.format("%s.%s", db, collection);
        final List<BasicBSONObject> obj = _nsToIndex.getOrDefault(key, Collections.emptyList());
        for (final BasicBSONObject o : obj) {
          final String raw = DataExplorerEncodingUtils.encodeToRaw(o);
          final DataExplorerOpResponse response =
              new DataExplorerOpResponse(requestId, raw, groupId, _hostId, clusterId, null, null);
          _dataExplorerResponseDao.insert(response);
          completeJob(requestId, true);
        }
        completeJob(requestId, false);
      } catch (final Exception e) {
        System.out.println(
            "Failed because ListIndexStats job creation threw an exception " + e.getMessage());
      }
    }

    public void createListIndexStatsWithMultipleNamespacesResponse(
        final ObjectId requestId, final String hostName, final ObjectId groupId) {
      try {
        var nsToIndex = _hostNameToNsToIndex.get(hostName);
        for (String ns : nsToIndex.keySet()) {
          BasicBSONObject nsBson = new BasicBSONObject();
          nsBson.append("key", ns);
          nsBson.append("value", nsToIndex.getOrDefault(ns, Collections.emptyList()));
          final String raw = DataExplorerEncodingUtils.encodeToRaw(nsBson);
          final DataExplorerOpResponse response =
              new DataExplorerOpResponse(
                  requestId, raw, groupId, _hostNameToHostId.get(hostName), null, null, null);
          _dataExplorerResponseDao.insert(response);
        }
        completeJob(requestId, false);
      } catch (final Exception e) {
        System.out.println(
            "Failed because ListIndexStats job creation threw an exception " + e.getMessage());
      }
    }

    private void createCollStatsResponse(
        final ObjectId requestId,
        final ObjectId groupId,
        final String db,
        final String collection,
        final String hostname) {
      try {
        final String ns = String.format("%s.%s", db, collection);
        final BasicBSONObject obj = new BasicBSONObject();
        obj.append("ns", ns);
        obj.append("size", 42000);
        obj.append("count", 1000);
        obj.append("avgObjSize", 42);
        obj.append("storageSize", 49152);
        obj.append("capped", false);
        obj.append("wiredTiger", new BasicBSONObject());
        obj.append("nindexes", 1);
        obj.append("indexDetails", new BasicBSONObject());
        obj.append("indexBuilds", new BasicBSONList());
        obj.append("totalIndexSize", 45056);
        obj.append("indexSizes", new BasicBSONObject());
        obj.append("scaleFactor", 1);
        obj.append("ok", 1.0d);
        obj.append("$clusterTime", new BasicBSONObject());
        obj.append("operationTime", new BSONTimestamp());
        if (_timeSeriesNamespaces.contains(ns)) {
          obj.append("timeseries", o());
        }
        final String raw = DataExplorerEncodingUtils.encodeToRaw(obj);
        final DataExplorerOpResponse response =
            new DataExplorerOpResponse(
                requestId,
                raw,
                groupId,
                _hostNameToHostId.getOrDefault(hostname, _hostId),
                _clusterId,
                null,
                null);
        _dataExplorerResponseDao.insert(response);
        completeJob(requestId, false);
      } catch (final Exception e) {
        System.out.println(
            "Failed because CollStats job response creation threw an exception " + e.getMessage());
      }
    }

    private void createTopResponse(final ObjectId requestId, final ObjectId groupId) {
      try {
        final BasicBSONObject obj = new BasicBSONObject();
        final BasicBSONObject totalsLimited = new BasicBSONObject();
        for (final String ns : _topNamespaces) {
          final BasicDBObject statistics = new BasicDBObject("time", 1).append("count", 1);
          final BasicDBObject statisticsBson =
              new BasicDBObject("total", statistics)
                  .append("commands", statistics)
                  .append("getmore", statistics)
                  .append("insert", statistics)
                  .append("queries", statistics)
                  .append("readLock", statistics)
                  .append("remove", statistics)
                  .append("update", statistics)
                  .append("writeLock", statistics);

          totalsLimited.append(ns, statisticsBson);
        }
        obj.append("totalsLimited", totalsLimited);
        obj.append("limit", 20);
        obj.append("nsCount", _topNamespaces.size());
        final String raw = DataExplorerEncodingUtils.encodeToRaw(obj);
        final DataExplorerOpResponse response =
            new DataExplorerOpResponse(requestId, raw, groupId, _hostId, null, null, null);
        _dataExplorerResponseDao.insert(response);
        completeJob(requestId, false);
      } catch (final Exception e) {
        System.out.println(
            "Failed because Top job response creation threw an exception " + e.getMessage());
      }
    }

    private void createListNamespacesResponse(
        final ObjectId requestId, final ObjectId groupId, final ObjectId clusterId) {
      try {
        final BasicBSONObject obj = new BasicBSONObject();
        final BasicBSONList testColls = new BasicBSONList();
        for (int i = 0; i < AntiPatternAccumulator.NUMBER_OF_NAMESPACES_THRESHOLD; i++) {
          testColls.add("foo" + i);
        }
        obj.append("test", testColls);
        final BasicBSONList dbColls = new BasicBSONList();
        dbColls.add("foo");
        obj.append("db", dbColls);
        final String raw = DataExplorerEncodingUtils.encodeToRaw(obj);
        final DataExplorerOpResponse response =
            new DataExplorerOpResponse(requestId, raw, groupId, _hostId, clusterId, null, null);
        _dataExplorerResponseDao.insert(response);
        completeJob(requestId, false);
      } catch (final Exception e) {
        System.out.println(
            "Failed because listNamespaces job response creation threw an exception "
                + e.getMessage());
      }
    }

    private void createListAllInProgressIndexesResponse(
        final ObjectId requestId, final ObjectId groupId, final ObjectId clusterId) {
      try {
        final BasicBSONList inProgressIndexes = new BasicBSONList();
        if (groupId.equals(new ObjectId("000000000000000000000000"))) {
          inProgressIndexes.add(createInProgressIndex());
        }
        final BasicBSONObject obj = new BasicDBObject();
        obj.append("inprog", inProgressIndexes);

        final String raw = DataExplorerEncodingUtils.encodeToRaw(obj);
        final DataExplorerOpResponse response =
            new DataExplorerOpResponse(requestId, raw, groupId, _hostId, clusterId, null, null);
        _dataExplorerResponseDao.insert(response);
        completeJob(requestId, false);
      } catch (final Exception e) {
        System.out.println(
            "Failed because Top job response creation threw an exception " + e.getMessage());
      }
    }

    public BasicBSONObject createInProgressIndex() {
      final String ns = "test.foo";
      final BasicBSONObject obj = new BasicBSONObject();
      obj.append("ns", ns);
      obj.append("type", "op");
      obj.append("opid", 974096);
      obj.append(
          "lsid",
          new BasicBSONObject()
              .append("id", UUID.fromString("c54501fa-950e-450c-d1c7-6ac4f8645bb8"))
              .append("uid", "WPHYA876yIw8EY/UqOcbQ3DoC80IClV+Q3d4H3pEmRo=".getBytes()));
      obj.append("secs_running", 0L);
      obj.append("microsecs_running", 372893L);
      obj.append("op", "command");
      obj.append("msg", "Index build in progress");
      obj.append("progress", new BasicBSONObject().append("done", 66084).append("total", 100296));
      return obj;
    }

    // Mock completed createIndex op.
    public void createIndex(final ObjectId pRequestId) {
      completeJob(pRequestId, false);
    }

    public void stopThread() {
      stop = true;
    }

    private void completeJob(final ObjectId pRequestId, final boolean hasMore) {
      final AgentJob agentJob = _agentJobsProcessorDao.findById(pRequestId);
      agentJob.updateStatus(AgentJob.Status.COMPLETED, Optional.empty());
      ((BasicDBObject) agentJob.getParameters().get("op")).put("hasMore", hasMore);
      _agentJobsProcessorDao.putJob(agentJob);
    }
  }

  /**
   * Thread to replace automation agent job responses. Checks if there is a new CollStatsCollection
   * agent job that needs to be processed, parses the job and inserts mock collStats data for the
   * appropriate namespaces.
   */
  public static class CollStatsCollectionJobMockResponseRunner extends Thread {
    private static final int SLEEP_MS = 1_000;
    private volatile boolean stop = false;

    private final AgentJobsProcessorDao _agentJobsProcessorDao;
    private final CollectionStatsDao _collectionStatsDao;
    private final Set<String> _targetedHostIds;

    public CollStatsCollectionJobMockResponseRunner(
        final AgentJobsProcessorDao pAgentJobsProcessorDao,
        final CollectionStatsDao pCollectionStatsDao,
        final Set<String> pTargetedHostIds) {
      _agentJobsProcessorDao = pAgentJobsProcessorDao;
      _collectionStatsDao = pCollectionStatsDao;
      _targetedHostIds = pTargetedHostIds;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void run() {
      try {
        while (!stop) {
          final List<DBObject> jobs = _agentJobsProcessorDao.findAll().toArray();
          for (final DBObject job : jobs) {
            final String command = (String) job.get("command");
            if (!command.equals("COLLECT_COLLSTATS")) {
              continue;
            }
            final ObjectId requestId = (ObjectId) job.get("_id");
            final ObjectId groupId = (ObjectId) job.get("groupId");
            final DBObject params = (DBObject) job.get("params");
            final String status = (String) job.get("status");
            final List<String> namespaces = (List<String>) params.get("namespaces");
            final String jobHostId = (String) params.get("hostId");

            // don't repeat insertion if the request has already completed
            if (!status.equals("COMPLETED") && _targetedHostIds.contains(jobHostId)) {
              createCollectCollStatsResponse(requestId, groupId, namespaces, jobHostId);
            }
          }
          Thread.sleep(SLEEP_MS);
        }

      } catch (final InterruptedException e) {
        System.out.println("Thread got interrupted. Aborting.");
      }
    }

    private void createCollectCollStatsResponse(
        final ObjectId requestId,
        final ObjectId groupId,
        final List<String> namespaces,
        final String jobHostId) {
      try {
        final List<CollectionStats> responses = new ArrayList<>();

        for (final String ns : namespaces) {
          final CollectionStats collStatsItem =
              new CollectionStats(groupId, jobHostId, ns, 42.0, 1000L, new Date());
          responses.add(collStatsItem);
        }
        _collectionStatsDao.upsertAll(responses);
        waitForFunctionToEqual(
            namespaces.size(),
            () -> _collectionStatsDao.findByHostNamespaces(jobHostId, namespaces).size(),
            "Asserting collection stats");
        final AgentJob agentJob = _agentJobsProcessorDao.findById(requestId);
        agentJob.updateStatus(AgentJob.Status.COMPLETED, Optional.empty());
        _agentJobsProcessorDao.putJob(agentJob);
      } catch (final Exception e) {
        System.out.println(
            "Failed because CollStats job response creation threw an exception " + e.getMessage());
      }
    }

    public void stopThread() {
      stop = true;
    }
  }
}
