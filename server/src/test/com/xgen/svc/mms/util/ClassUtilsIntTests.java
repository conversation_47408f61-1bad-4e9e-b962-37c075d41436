package com.xgen.svc.mms.util;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.xgen.cloud.common.model._public.annotation.MethodCallPromTimed;
import com.xgen.cloud.common.util._public.util.ClassUtils;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ClassUtilsIntTests {
  @Inject Helper injectedHelper;

  @Test
  public void getGuiceEnhancedClassParent_returnsOriginal_whenNotGuiceEnhanced() {
    final var notEnhanced = "Not enhanced";
    final var result = ClassUtils.getGuiceEnhancedClassParent(notEnhanced.getClass());
    assertThat(result, is(equalTo(notEnhanced.getClass())));
  }

  @Test
  public void getGuiceEnhancedClassParent_returnsParent_whenGuiceEnhanced() {
    final var result = ClassUtils.getGuiceEnhancedClassParent(injectedHelper.getClass());
    assertThat(result, is(equalTo(Helper.class)));
  }

  @Singleton
  public static class Helper {
    // Annotation is being used so that Guice creates the subclass
    @MethodCallPromTimed(name = "mms.test.classUtils.aop")
    public void run() {}
  }
}
