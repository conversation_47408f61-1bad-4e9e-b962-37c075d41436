package com.xgen.svc.mms.util;

import static com.xgen.svc.mms.util.DiscoverySDLock.COLLECTION_NAME;
import static com.xgen.svc.mms.util.DiscoverySDLock.DB_NAME;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.driverwrappers._public.common.DbMaintenanceException;
import com.xgen.cloud.common.driverwrappers._public.common.MongoClientRuntimeSettingsCache;
import com.xgen.cloud.common.util._public.util.SDLock;
import com.xgen.cloud.common.util._public.util.SDLock.BeaterOptions;
import com.xgen.cloud.common.util._public.util.SDLock.HeartbeatingLock;
import com.xgen.cloud.common.util._public.util.SDLock.ResourceException;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.time.Duration;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class DiscoverySDLockIntTests extends JUnit5BaseSvcTest {

  @Inject MongoSvc _mongoSvc;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testBlockUnblockWritesDbLevel() throws ResourceException {

    MongoClientRuntimeSettingsCache.setWritesEnabled(DB_NAME, false);

    DiscoverySDLock sdLock = new DiscoverySDLock(_mongoSvc);
    HeartbeatingLock lock = sdLock.getLock("test-resource-discovery-sdlock-db-level");
    final BeaterOptions options = new BeaterOptions();
    options.lockTimeoutThresholdMs = Duration.ofMinutes(4).toMillis();
    options.sleepBetweenUpdatesMs = Duration.ofSeconds(20).toMillis();

    boolean exceptionThrown = false;

    try {
      lock.tryAcquire(options);
    } catch (DbMaintenanceException e) {
      exceptionThrown = true;
    }
    assertTrue(exceptionThrown);
    MongoClientRuntimeSettingsCache.setWritesEnabled(DB_NAME, true);
    SDLock.AcquireResult acquireResult = lock.tryAcquire(options);
    assertTrue(acquireResult.locked);
  }

  @Test
  public void testBlockUnblockWritesCollectionLevel() throws ResourceException {

    MongoClientRuntimeSettingsCache.setWritesEnabled(DB_NAME, COLLECTION_NAME, false);

    DiscoverySDLock sdLock = new DiscoverySDLock(_mongoSvc);
    HeartbeatingLock lock = sdLock.getLock("test-resource-discovery-sdlock-collection-level");
    final BeaterOptions options = new BeaterOptions();
    options.lockTimeoutThresholdMs = Duration.ofMinutes(4).toMillis();
    options.sleepBetweenUpdatesMs = Duration.ofSeconds(20).toMillis();

    boolean exceptionThrown = false;

    try {
      lock.tryAcquire(options);
    } catch (DbMaintenanceException e) {
      exceptionThrown = true;
    }
    assertTrue(exceptionThrown);
    MongoClientRuntimeSettingsCache.setWritesEnabled(DB_NAME, COLLECTION_NAME, true);
    SDLock.AcquireResult acquireResult = lock.tryAcquire(options);
    assertTrue(acquireResult.locked);
  }

  @AfterEach
  public void tearDown() throws Exception {
    super.tearDown();
  }
}
