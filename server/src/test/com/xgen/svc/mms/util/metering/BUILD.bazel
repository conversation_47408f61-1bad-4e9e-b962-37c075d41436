load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = False,
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/invoice",
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/module/metering/server/utils/testFactories",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/util/metering",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:junit_junit",
    ],
)
