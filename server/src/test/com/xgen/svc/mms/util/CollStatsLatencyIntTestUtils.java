package com.xgen.svc.mms.util;

import static com.xgen.svc.common.TestDataUtils.populateCollectionFromJsonFtlFile;
import static com.xgen.svc.mms.api.view.ApiCollStatsLatencyNamespaceMetricsView.METRICS_FIELD;
import static com.xgen.svc.mms.api.view.ApiMeasurementView.DATAPOINTS_FIELD;
import static com.xgen.svc.mms.api.view.ApiMeasurementView.NAME_FIELD;
import static com.xgen.svc.mms.api.view.ApiMeasurementView.UNITS_FIELD;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.metrics._private.dao.CollStatsLatencyNamespaceStatsDao;
import com.xgen.cloud.monitoring.metrics._public.model.AxisUnit;
import com.xgen.cloud.monitoring.metrics._public.model.ClusterView;
import com.xgen.cloud.monitoring.metrics._public.model.CollStatsLatencyNamespaceMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.CollStatsLatencyNamespaceRankStats;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.svc.core.BaseTestCommon;
import com.xgen.svc.mms.api.model.ApiCollStatsLatencyNamespaceMeasurement;
import com.xgen.svc.mms.api.view.ApiAxisUnitsView;
import com.xgen.svc.mms.api.view.ApiMeasurementsCollStatsLatencyHostView;
import jakarta.ws.rs.core.UriBuilder;
import java.text.SimpleDateFormat;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;

public class CollStatsLatencyIntTestUtils extends BaseTestCommon {
  // FTL test file variables
  private static final String BASE_FIELD = "base";
  private static final String INCREMENT_FIELD = "increment";
  private static final String LATENT_OP_COUNT_FIELD = "latentOpCount";

  public static String TEST_DB_NAME = "test";
  public static String TEST_COLLECTION_NAME = "matrices";

  // Constants for pinning endpoints
  public static final int TEST_CACHE_REFRESH_INTERVAL_SECONDS = 1;
  public static final String TEST_PINNED_COLLECTION_1 = "one.collection";
  public static final String TEST_PINNED_COLLECTION_2 = "another.collection";
  public static final String TEST_PINNED_COLLECTION_3 = "third.collection";
  public static final String TEST_PINNED_COLLECTION_4 = "fourth.collection";
  public static final String SYSTEM_COLLECTION_NAME = "system.collection";

  // Constants for measurement endpoints
  public static final boolean DEFAULT_ENVELOPE_STRING = true;
  public static final String DEFAULT_GRANULARITY_STRING =
      Retention.COLLECTION_LEVEL_QUERY_LATENCY_TEN_MINUTE_RETENTION.getSamplePeriod().toString();
  public static final String EMPTY_STRING = "";

  public static final String PERIOD_FIELD = "period";
  public static final String ENVELOPE_FIELD = "envelope";
  public static final String CONTENT_FIELD = "content";
  public static final String METRIC_NAME_FIELD = "metricName";

  public static final int EXPECTED_NUMBER_OF_DATA_POINTS = 2;

  // Constants for ranking endpoints
  // Expected ranked namespaces (ordered) based on populating backing db with
  // namespaceStatsDocs.json.ftl mock data
  public static List<String> EXPECTED_RANKED_NAMESPACES_HOST =
      List.of(
          "Namespace1",
          "Namespace2",
          "Namespace3",
          "sample_mflix.movies",
          "sample_airbnb.listingsAndReviews",
          "sample_restaurants.neighborhoods",
          "sample_training.zips",
          "sample_restaurants.restaurants",
          "sample_training.grades",
          "sample_mflix.embedded_movies",
          "sample_training.routes",
          "sample_mflix.comments",
          "sample_geospatial.shipwrecks",
          "sample_training.companies",
          "sample_mflix.theaters",
          "sample_mflix.users",
          "sample_analytics.transactions",
          "sample_mflix.sessions",
          "sample_training.trips",
          "sample_training.posts");
  public static List<String> EXPECTED_RANKED_NAMESPACES_CLUSTER =
      List.of(
          "Namespace1",
          "Namespace2",
          "Namespace3",
          "sample_restaurants.restaurants",
          "sample_mflix.sessions",
          "sample_training.zips",
          "sample_training.inspections",
          "sample_training.grades",
          "sample_mflix.embedded_movies",
          "sample_training.routes",
          "sample_mflix.comments",
          "sample_geospatial.shipwrecks",
          "sample_training.companies",
          "sample_mflix.theaters",
          "sample_weatherdata.data",
          "sample_mflix.users",
          "sample_analytics.transactions",
          "sample_mflix.movies",
          "sample_airbnb.listingsAndReviews",
          "sample_training.trips");

  /* Constructs and inserts collStats Host measurements with given params at each timestamp in pTimestamps with
  latency metric values starting at pBase and linearly increasing by factor of pIncrement. Add non-zero
  pNumLatentReadOpCount to generate very latent read operations. */
  public static List<CollStatsLatencyNamespaceMeasurement> constructCollStatsHostMeasurements(
      final ObjectId pGroupId,
      final String pHostIdentifier,
      final String pDbName,
      final String pCollection,
      final List<Date> pTimestamps,
      final String pProcessId,
      final int pIncrement,
      final int pBase,
      final int pNumLatentReadOpCount) {
    final List<CollStatsLatencyNamespaceMeasurement> constructedMeasurements = new ArrayList<>();

    final Map<String, Object> hostPingParams = new HashMap<>();
    hostPingParams.put(BASE_FIELD, pBase); // Base stays the same for all Measurements

    for (int i = 0; i < pTimestamps.size(); i++) {
      final Date currentTimestamp = pTimestamps.get(i);
      // Construct measurement at timestamp
      final String hostPingId =
          CollStatsLatencyNamespaceMeasurement.id(
              pGroupId,
              pHostIdentifier,
              ClusterView.INDIVIDUAL_PROCESS,
              DbUtils.getNamespace(pDbName, pCollection),
              currentTimestamp.getTime(),
              pProcessId);

      hostPingParams.put(Ping.ID.field, hostPingId);
      hostPingParams.put(Ping.LOCAL_TIME.field, formatJSONDate(currentTimestamp));
      hostPingParams.put(INCREMENT_FIELD, i * pIncrement);
      hostPingParams.put(LATENT_OP_COUNT_FIELD, i * pNumLatentReadOpCount);

      final BasicDBObject pingHost1_1 =
          loadBsonObjectFromFtlJsonFile(
              "mms/svc/ping/CollStatsLatencyMetrics/validMeasurementWithParams.json.ftl",
              hostPingParams);

      final CollStatsLatencyNamespaceMeasurement measurement =
          new CollStatsLatencyNamespaceMeasurement(currentTimestamp, currentTimestamp, pingHost1_1);

      constructedMeasurements.add(measurement);
    }

    return constructedMeasurements;
  }

  /* Constructs and inserts collStats cluster measurements with given params at each timestamp in pTimestamps with constantly increasing latency metrics to generate standard data series */
  public static List<CollStatsLatencyNamespaceMeasurement> constructCollStatsClusterMeasurements(
      final ObjectId pGroupId,
      final String pClusterIdentifier,
      final ClusterView pClusterView,
      final String pDbName,
      final String pCollectionName,
      final List<Date> pTimestamps) {
    final List<CollStatsLatencyNamespaceMeasurement> constructedMeasurements = new ArrayList<>();

    // Same processId for all measurements (eg. no restarts during this time)
    final String clusterProcessId = new ObjectId().toString();

    for (int i = 0; i < pTimestamps.size(); i++) {
      final Date currentTimestamp = pTimestamps.get(i);

      // Construct cluster measurement at timestamp
      final String clusterFirstPingId =
          CollStatsLatencyNamespaceMeasurement.id(
              pGroupId,
              pClusterIdentifier,
              pClusterView,
              DbUtils.getNamespace(pDbName, pCollectionName),
              currentTimestamp.getTime(),
              clusterProcessId);
      final Map<String, Object> clusterFirstPingParams = new HashMap<>();
      clusterFirstPingParams.put(Ping.ID.field, clusterFirstPingId);
      clusterFirstPingParams.put(Ping.LOCAL_TIME.field, formatJSONDate(currentTimestamp));
      clusterFirstPingParams.put(BASE_FIELD, 0);
      clusterFirstPingParams.put(INCREMENT_FIELD, i + 1);
      clusterFirstPingParams.put(LATENT_OP_COUNT_FIELD, 0); // No latency spikes

      final BasicDBObject cluster1Ping =
          loadBsonObjectFromFtlJsonFile(
              "mms/svc/ping/CollStatsLatencyMetrics/validMeasurementWithParams.json.ftl",
              clusterFirstPingParams);

      final CollStatsLatencyNamespaceMeasurement measurement =
          new CollStatsLatencyNamespaceMeasurement(
              currentTimestamp, currentTimestamp, cluster1Ping);

      constructedMeasurements.add(measurement);
    }

    return constructedMeasurements;
  }

  public static JSONObject createNamespacesRequestBody(final List<String> pNamespaces) {
    final JSONArray namespacesArray = new JSONArray();
    namespacesArray.putAll(pNamespaces);
    final JSONObject namespacesRequestBody = new JSONObject();
    namespacesRequestBody.put("namespaces", namespacesArray);
    return namespacesRequestBody;
  }

  public static UriBuilder addMetricsQueryParamsToUriBuilder(
      final UriBuilder pUriBuilder, final List<String> pMetrics) {

    for (final String metric : pMetrics) {
      pUriBuilder.queryParam(METRICS_FIELD, metric);
    }

    return pUriBuilder;
  }

  /* Populates the namespacestats collection for single Host and primary in shard for two timestamps to generate ranking deltas */
  public static void populateNamespaceStatsCollection(
      final ObjectId pGroupId,
      final String pHostIdentifier,
      final String pPrimaryShardIdentifier,
      final Date pFirstTimestamp,
      final Date pSecondTimestamp)
      throws Exception {
    // Same processId across documents indicates that all docs are from single start-up period
    final String dummyProcessId = "d57aba50";

    // Get composed _id to set in backing documents for first and second timestamp for Host and
    // primary ClusterView
    final String composedIdForHost1_1 =
        CollStatsLatencyNamespaceRankStats.getComposedId(
            pGroupId,
            pHostIdentifier,
            ClusterView.INDIVIDUAL_PROCESS,
            pFirstTimestamp.getTime(),
            dummyProcessId);
    final String composedIdForHost1_2 =
        CollStatsLatencyNamespaceRankStats.getComposedId(
            pGroupId,
            pHostIdentifier,
            ClusterView.INDIVIDUAL_PROCESS,
            pSecondTimestamp.getTime(),
            dummyProcessId);

    final String composedIdForPrimary_1 =
        CollStatsLatencyNamespaceRankStats.getComposedId(
            pGroupId,
            pPrimaryShardIdentifier,
            ClusterView.PRIMARY,
            pFirstTimestamp.getTime(),
            dummyProcessId);
    final String composedIdForPrimary_2 =
        CollStatsLatencyNamespaceRankStats.getComposedId(
            pGroupId,
            pPrimaryShardIdentifier,
            ClusterView.PRIMARY,
            pSecondTimestamp.getTime(),
            dummyProcessId);

    final Map<String, Object> ftlParamsForNamespaceStats =
        Map.of(
            "composedIdHost1_1",
            composedIdForHost1_1,
            "composedIdHost1_2",
            composedIdForHost1_2,
            "composedIdPrimary1",
            composedIdForPrimary_1,
            "composedIdPrimary2",
            composedIdForPrimary_2,
            "expirationDate",
            pFirstTimestamp.toInstant().plus(7, ChronoUnit.DAYS).toEpochMilli());

    populateCollectionFromJsonFtlFile(
        "mms/svc/ping/CollStatsLatencyMetrics/namespaceStatsDocs.json.ftl",
        ftlParamsForNamespaceStats,
        CollStatsLatencyNamespaceStatsDao.DB_NAME,
        CollStatsLatencyNamespaceStatsDao.COLLECTION_NAME);
  }

  /**
   * This helper method inserts CollStats Measurements at every timestamp listed in pTimestamps,
   * with the last Measurements (number defined by pNumLatentMeasurements) having a high
   * microseconds bucket in the reads Histogram with the op count specified. The purpose of this
   * method is to mock CollStats Measurement docs with query latency spikes for a Host / Cluster.
   *
   * @param pHostIdentifier Host identifier for the Measurements
   * @param pDbName Database name for the Measurements
   * @param pCollectionName Collection name for the Measurements
   * @param pNumLatentMeasurements Number of Measurements to insert that have high read latency
   * @param pCountOfLatentReadOps Count of read operations with high latency within the latent
   *     Measurement docs
   * @param pTimestamps List of Dates at which to mock and insert Measurements for
   */
  public static List<CollStatsLatencyNamespaceMeasurement>
      constructCollStatsMeasurementsWithReadLatencySpike(
          final ObjectId pGroupId,
          final String pHostIdentifier,
          final String pDbName,
          final String pCollectionName,
          final int pNumLatentMeasurements,
          final int pCountOfLatentReadOps,
          final List<Date> pTimestamps) {
    if (pNumLatentMeasurements >= pTimestamps.size()) {
      throw new IllegalArgumentException(
          "Number of latent measurements (pNumLatentMeasurements) must be less than total number of"
              + " measurements (pTimestamps).");
    }

    final String testProcessId = new ObjectId().toString();
    final List<Date> timestampsForNormalMeasurements =
        pTimestamps.subList(0, pTimestamps.size() - pNumLatentMeasurements);
    // The final Measurements will have very latent reads
    final List<Date> timestampsForSpike =
        pTimestamps.subList(pTimestamps.size() - pNumLatentMeasurements, pTimestamps.size());

    final int incrementValueForSmallerBucket = 1;
    final List<CollStatsLatencyNamespaceMeasurement> normalHostMeasurements =
        constructCollStatsHostMeasurements(
            pGroupId,
            pHostIdentifier,
            pDbName,
            pCollectionName,
            timestampsForNormalMeasurements,
            testProcessId,
            incrementValueForSmallerBucket,
            0,
            0);

    final List<CollStatsLatencyNamespaceMeasurement> latentHostMeasurements =
        constructCollStatsHostMeasurements(
            pGroupId,
            pHostIdentifier,
            pDbName,
            pCollectionName,
            timestampsForSpike,
            testProcessId,
            0, // Don't increment smaller-valued buckets
            timestampsForNormalMeasurements.size()
                * incrementValueForSmallerBucket, // Add base to ensure Histogram counters are
            // increasing from prev Measurements
            pCountOfLatentReadOps); // Count of read operations to add to larger bucket

    return Stream.of(normalHostMeasurements, latentHostMeasurements)
        .flatMap(Collection::stream)
        .collect(Collectors.toList());
  }

  public static void validateCommonFieldsForCollStatsHostAndClusterViews(
      final JSONObject pContentMap,
      final String pExpectedGroupId,
      final String pExpectedDbName,
      final String pExpectedCollectionName,
      final String pExpectedGranularity,
      final String pExpectedStart,
      final String pExpectedEnd) {
    assertEquals(
        pExpectedGroupId,
        pContentMap.getString(ApiMeasurementsCollStatsLatencyHostView.GROUP_ID_FIELD));
    assertEquals(
        pExpectedDbName,
        pContentMap.getString(ApiMeasurementsCollStatsLatencyHostView.DATABASE_NAME_FIELD));
    assertEquals(
        pExpectedCollectionName,
        pContentMap.getString(ApiMeasurementsCollStatsLatencyHostView.COLLECTION_NAME_FIELD));
    assertEquals(
        pExpectedGranularity,
        pContentMap.getString(ApiMeasurementsCollStatsLatencyHostView.GRANULARITY_FIELD));
    assertEquals(
        pExpectedStart, pContentMap.getString(ApiMeasurementsCollStatsLatencyHostView.START_FIELD));
    assertEquals(
        pExpectedEnd, pContentMap.getString(ApiMeasurementsCollStatsLatencyHostView.END_FIELD));
  }

  public static void validateApiMeasurements(
      final JSONArray pMeasurements,
      final List<ApiCollStatsLatencyNamespaceMeasurement> pMetrics,
      final int pExpectedNumberOfDataPoints) {
    assertEquals(pMetrics.size(), pMeasurements.length());

    for (int index = 0; index < pMeasurements.length(); index++) {
      final JSONObject measurement = pMeasurements.getJSONObject(index);
      final ApiCollStatsLatencyNamespaceMeasurement metric = pMetrics.get(index);
      validateApiMeasurement(
          measurement, metric.toString(), metric.getAxisUnit(), pExpectedNumberOfDataPoints);
    }
  }

  protected static void validateApiMeasurement(
      final JSONObject pApiMeasurement,
      final String pExpectedMetricName,
      final AxisUnit pExpectedUnit,
      final int pExpectedNumberOfDataPoints) {
    assertEquals(
        "Unexpected metric name in response", pExpectedMetricName, pApiMeasurement.get(NAME_FIELD));

    final JSONArray dataPoints = pApiMeasurement.getJSONArray(DATAPOINTS_FIELD);
    for (int index = 0; index < pExpectedNumberOfDataPoints; index++) {
      final JSONObject dataPoint = dataPoints.getJSONObject(index);
      final String valueField = "value";
      final String value = String.valueOf(dataPoint.get(valueField));

      // The first data point should always be null (reset).
      if (index == 0) {
        assertEquals("null", value);
        continue;
      }

      // Assume no counter resets on subsequent data points
      assertNotEquals(
          String.format(
              "Metric (%s) returned unexpected data point value", pApiMeasurement.get(NAME_FIELD)),
          "null",
          value);
    }

    assertEquals(
        String.format("Metric %s returned unexpected unit.", pApiMeasurement.get(NAME_FIELD)),
        ApiAxisUnitsView.valueOf(pExpectedUnit).name(),
        pApiMeasurement.getString(UNITS_FIELD));
  }

  private static String formatJSONDate(final Date pDate) {
    final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
    return formatter.format(pDate);
  }
}
