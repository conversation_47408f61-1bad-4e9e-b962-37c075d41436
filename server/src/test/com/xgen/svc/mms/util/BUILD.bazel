load("//server/src/test:rules.bzl", "library_package", "test_package")

library_package(
    name = "util",
    visibility = [
        "//server/src/features:__subpackages__",
        "//server/src/test:__subpackages__",
        "//server/src/unit:__subpackages__",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/access/rolecheck/_private/svc",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/authz",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_private/dao",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/common",
        "//server/src/main/com/xgen/cloud/monitoring/logs/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/metrics/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/performanceadvisor",
        "//server/src/main/com/xgen/cloud/performanceadvisor/_private/dao",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/main/com/xgen/svc/mms/dao/explorer",
        "//server/src/main/com/xgen/svc/mms/model/performanceadvisor",
        "//server/src/test/com/xgen/cloud/performanceadvisor/util",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/svc/core",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:junit_junit",  # only used by AuthzSvcResourceClassTest which itself should be phased out
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:software_amazon_awssdk_sdk_core",
    ],
)

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    data = ["//server/src/webapp-mms"],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/brs/autodownloader",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/aws",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/partners/aws",
        "//server/src/main/com/xgen/cloud/util",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/util/billing",
        "//server/src/main/com/xgen/svc/mms/util/billing/testFactories",
        "//server/src/test/com/xgen/svc/brs/grid",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/testlib/junit5/extensions/guicetest",
        "//third_party:driverwrappers",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
