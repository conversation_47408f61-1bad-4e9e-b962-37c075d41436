package com.xgen.svc.mms.util;

import static com.google.common.collect.Lists.newArrayList;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SlowQueryTestLogs {

  public static final SimpleDateFormat TIMESTAMP_FORMAT =
      new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");

  private static final SimpleDateFormat STRUCTURED_TIMESTAMP_FORMAT =
      new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX");

  private static final String SHAPE_A_1_PERCENT_UTIL_37500_MS_LINE0 =
      " I WRITE    [conn1] update test.foo query: { a: \"adfdsdfsd\" } update: { $set: { a:"
          + " \"asdf\" } } keysExamined:0 docsExamined:10000 nMatched:100 nModified:0 keyUpdates:0"
          + " writeConflicts:0 numYields:78 locks:{ Global: { acquireCount: { r: 79, w: 79 } },"
          + " Database: { acquireCount: { w: 79 } }, Collection: { acquireCount: { w: 79 } } }"
          + " 37500ms";
  private static final String SHAPE_A_1_PERCENT_UTIL_37500_MS_LINE1 =
      " I COMMAND  [conn2] command test.foo appName: \"MongoDB Shell\" command: find { find:"
          + " \"foo\", filter: { a: \"asdf\" } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 cursorExhausted:1 numYields:78 nreturned:100 reslen:81 locks:{"
          + " Global: { acquireCount: { r: 158 } }, Database: { acquireCount: { r: 79 } },"
          + " Collection: { acquireCount: { r: 79 } } } protocol:op_command 37500ms";
  private static final String SHAPE_AB_39_PERCENT_UTIL_37500_MS_LINE0 =
      " I COMMAND  [conn2] command test.foo appName: \"MongoDB Shell\" command: find { find:"
          + " \"foo\", filter: { a: \"asdf\" }, sort: { b: 1.0 } } planSummary: COLLSCAN"
          + " keysExamined:0 docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78"
          + " nreturned:3900 reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: {"
          + " acquireCount: { r: 79 } }, Collection: { acquireCount: { r: 79 } } }"
          + " protocol:op_command 37500ms";
  private static final String SHAPE_C_85_PERCENT_UTIL_150000_MS_LINE =
      " I COMMAND  [conn2] command test.foo appName: \"MongoDB Shell\" command: find { find:"
          + " \"foo\", filter: { c: \"asdf\" } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 cursorExhausted:1 numYields:78 nreturned:8500 reslen:81 locks:{"
          + " Global: { acquireCount: { r: 158 } }, Database: { acquireCount: { r: 79 } },"
          + " Collection: { acquireCount: { r: 79 } } } protocol:op_command 150000ms";
  private static final String SHAPE_AB_39_PERCENT_UTIL_37500_MS_LINE1 =
      " I COMMAND  [conn2] command test.foo appName: \"MongoDB Shell\" command: find { find:"
          + " \"foo\", filter: { a: \"asdf\" }, sort: { b: 1.0 } } planSummary: COLLSCAN"
          + " keysExamined:0 docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78"
          + " nreturned:3900 reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: {"
          + " acquireCount: { r: 79 } }, Collection: { acquireCount: { r: 79 } } }"
          + " protocol:op_command 37500ms";
  private static final String SHAPE_X_95_PERCENT_UTIL_150000_MS_LINE =
      " I COMMAND  [conn2] command db.c appName: \"MongoDB Shell\" command: find { find: \"c\","
          + " filter: { x: 1.0 } } planSummary: COLLSCAN keysExamined:0 docsExamined:10000"
          + " hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:9500 reslen:81 locks:{"
          + " Global: { acquireCount: { r: 158 } }, Database: { acquireCount: { r: 79 } },"
          + " Collection: { acquireCount: { r: 79 } } } protocol:op_command 150000ms";
  private static final String COVERED_BY_INDEX_LINE =
      " I COMMAND  [conn2] command test.foo appName: \"MongoDB Shell\" command: find { find:"
          + " \"foo\", filter: { grant: 1 } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:9500"
          + " reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: { acquireCount: {"
          + " r: 79 } }, Collection: { acquireCount: { r: 79 } } } protocol:op_command 150000ms";
  private static final String IGNORE_LOG_LINE6 =
      " I COMMAND  [conn2] command test.skip appName: \"MongoDB Shell\" command: find { find:"
          + " \"skip\", filter: { z: 1 } } planSummary: COLLSCAN keysExamined:0 docsExamined:10"
          + " hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:10 reslen:81 locks:{ Global:"
          + " { acquireCount: { r: 158 } }, Database: { acquireCount: { r: 79 } }, Collection: {"
          + " acquireCount: { r: 79 } } } protocol:op_command 1ms";
  private static final String IGNORE_LOG_LINE7 =
      " I COMMAND  [conn2] command db.c appName: \"MongoDB Shell\" command: find { find: \"c\","
          + " filter: { tooshort: 1 } } planSummary: COLLSCAN keysExamined:0 docsExamined:501"
          + " cursorExhausted:1 numYields:78 nreturned:1 reslen:81 locks:{ Global: { acquireCount:"
          + " { r: 158 } }, Database: { acquireCount: { r: 79 } }, Collection: { acquireCount: {"
          + " r: 79 } } } protocol:op_command 500ms";

  public static final String SAMPLE_LOG_LINE_AGG1 =
      " I COMMAND  [conn466] command test.other appName: \"MongoDB Shell\" command: aggregate {"
          + " aggregate: \"other\", pipeline: [ { $lookup: {from: \"fromColl\", localField:"
          + " \"locField\", foreignField: \"forField\", as: \"asField\"} } ], cursor: {}, $db:"
          + " \"test\" } planSummary: COLLSCAN cursorid:7157860660900964075 keysExamined:501"
          + " docsExamined:1 numYields:782 nreturned:1 reslen:10276 locks:{ Global: {"
          + " acquireCount: { r: 1570 } }, Database: { acquireCount: { r: 785 } }, Collection: {"
          + " acquireCount: { r: 785 } } } protocol:op_command 10000ms";

  // system namespaces - should be ignored
  private static final String DROP_SAMPLE_LOG_LINE_SYSTEM_COLL1 =
      " I COMMAND  [conn2] command local.system.replset appName: \"MongoDB Shell\" command: find {"
          + " find: \"c\", filter: { x: 1 } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:1"
          + " reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: { acquireCount: {"
          + " r: 79 } }, Collection: { acquireCount: { r: 79 } } } protocol:op_command 7ms";
  private static final String DROP_SAMPLE_LOG_LINE_SYSTEM_COLL2 =
      " I COMMAND  [conn2] command local.clustermanager appName: \"MongoDB Shell\" command: find {"
          + " find: \"c\", filter: { x: 1 } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:1"
          + " reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: { acquireCount: {"
          + " r: 79 } }, Collection: { acquireCount: { r: 79 } } } protocol:op_command 7ms";
  private static final String DROP_SAMPLE_LOG_LINE_SYSTEM_COLL3 =
      " I COMMAND  [conn2] command admin.system.roles appName: \"MongoDB Shell\" command: find {"
          + " find: \"c\", filter: { x: 1 } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:1"
          + " reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: { acquireCount: {"
          + " r: 79 } }, Collection: { acquireCount: { r: 79 } } } protocol:op_command 7ms";
  private static final String DROP_SAMPLE_LOG_LINE_SYSTEM_COLL4 =
      " I COMMAND  [conn2] command admin.system.users appName: \"MongoDB Shell\" command: find {"
          + " find: \"c\", filter: { x: 1 } } planSummary: COLLSCAN keysExamined:0"
          + " docsExamined:10000 hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:1"
          + " reslen:81 locks:{ Global: { acquireCount: { r: 158 } }, Database: { acquireCount: {"
          + " r: 79 } }, Collection: { acquireCount: { r: 79 } } } protocol:op_command 7ms";
  private static final String DROP_SAMPLE_LOG_LINE_SYSTEM_COLL5 =
      " I COMMAND  [conn2] command admin.$cmd appName: \"MongoDB Shell\" command: find { find:"
          + " \"c\", filter: { x: 1 } } planSummary: COLLSCAN keysExamined:0 docsExamined:10000"
          + " hasSortStage:1 cursorExhausted:1 numYields:78 nreturned:1 reslen:81 locks:{ Global:"
          + " { acquireCount: { r: 158 } }, Database: { acquireCount: { r: 79 } }, Collection: {"
          + " acquireCount: { r: 79 } } } protocol:op_command 7ms";

  // for indexes generated from queries including negation operators
  private static final String SAMPLE_LOG_LINE_NEGATION0 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {a: { $ne: 4 } }}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 100000ms";

  private static final String SAMPLE_LOG_LINE_NEGATION1 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: { b: { $nin: [ 5,"
          + " 10 ] }  }}keysExamined:0 docsExamined:2000 nMatched:1 200000ms";

  private static final String SAMPLE_LOG_LINE_NEGATION2 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: { c: { $not: { $gt:"
          + " 3 } } }}keysExamined:0 docsExamined:2000 nMatched:1 300000ms";

  private static final String SAMPLE_LOG_LINE_SKIP_NRETURNED_ZERO =
      " I  COMMAND  [conn2769] command smol.smol appName: \"MongoDB Shell\" command: find { find:"
          + " \"smol\", filter: { a: 1.0 }, skip: 1000.0, lsid: { id:"
          + " UUID(\"fe02a0a0-2647-4350-a7c6-62fc6c1c82e9\") }, $db: \"smol\" } planSummary:"
          + " COLLSCAN keysExamined:0 docsExamined:10000 cursorExhausted:1 numYields:0 nreturned:0"
          + " queryHash:4B53BE76 planCacheKey:4B53BE76 reslen:98 locks:{"
          + " ReplicationStateTransition: { acquireCount: { w: 1 } }, Global: { acquireCount: { r:"
          + " 1 } }, Database: { acquireCount: { r: 1 } }, Collection: { acquireCount: { r: 1 } },"
          + " Mutex: { acquireCount: { r: 1 } } } storage:{ data: { bytesRead: 76,"
          + " timeReadingMicros: 254 } } protocol:op_msg 100000ms";

  private static final String SAMPLE_LOG_LINE_SKIP_NRETURNED_NON_ZERO =
      " I  COMMAND  [conn2769] command smol.smol appName: \"MongoDB Shell\" command: find { find:"
          + " \"smol\", filter: { b: 1.0 }, skip: 1000.0, lsid: { id:"
          + " UUID(\"fe02a0a0-2647-4350-a7c6-62fc6c1c82e9\") }, $db: \"smol\" } planSummary:"
          + " COLLSCAN keysExamined:0 docsExamined:10000 cursorExhausted:1 numYields:0 nreturned:1"
          + " queryHash:4B53BE76 planCacheKey:4B53BE76 reslen:98 locks:{"
          + " ReplicationStateTransition: { acquireCount: { w: 1 } }, Global: { acquireCount: { r:"
          + " 1 } }, Database: { acquireCount: { r: 1 } }, Collection: { acquireCount: { r: 1 } },"
          + " Mutex: { acquireCount: { r: 1 } } } storage:{ data: { bytesRead: 76,"
          + " timeReadingMicros: 254 } } protocol:op_msg 100000ms";

  // for testing > 20 possible indexes
  private static final String SAMPLE_LOG_LINE_EXTENDED0 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {a: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 100000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED1 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {b: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 200000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED2 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {c: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 300000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED3 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {d: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 400000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED4 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {e: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 500000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED5 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {f: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 600000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED6 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {g: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 700000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED7 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {h: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 800000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED8 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {i: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 900000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED9 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {j: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 100000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED10 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {k: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 110000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED11 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {l: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 120000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED12 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {m: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 130000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED13 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {n: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 140000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED14 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {o: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 150000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED15 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {p: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 160000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED16 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {q: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 170000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED17 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {r: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 180000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED18 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {s: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 190000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED19 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {t: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 200000ms";
  private static final String SAMPLE_LOG_LINE_EXTENDED20 =
      " I COMMAND [conn1] command test.foo command: find { find: \"c\", filter: {u: 1}}"
          + "keysExamined:0 docsExamined:2000 nMatched:1 210000ms";
  private static final String SAMPLE_LOG_LINE_COUNT =
      " I COMMAND  [conn438] command smol.smol appName: \"MongoDB Shell\" command: count { count:"
          + " \"smol\", query: { a: 1.0, b: \"a\" }, fields: {}, lsid: { id:"
          + " UUID(\"200fc478-8a76-4d9f-8b55-0761db1df9d3\") }, $clusterTime: { clusterTime:"
          + " Timestamp(1659031814, 1), signature: { hash: BinData(0,"
          + " 0000000000000000000000000000000000000000), keyId: 0 } }, $db: \"smol\" } planSummary:"
          + " COLLSCAN { a: 1 } keysExamined:0 docsExamined:2000 numYields:0 reslen:170 locks:{"
          + " Global: { acquireCount: { r: 1 } }, Database: { acquireCount: { r: 1 } }, Collection:"
          + " { acquireCount: { r: 1 } } } protocol:op_msg 0ms";

  private static final String SAMPLE_LOG_LINE_DISTINCT =
      " I  COMMAND  [conn741] command smol.smol appName: \"MongoDB Shell\" command: distinct {"
          + " distinct: \"smol\", key: \"_id\", query: { a: ObjectId('53c994b05b78bace99013a02') },"
          + " lsid: { id: UUID(\"d4872d19-d35c-4054-8753-85a66e437cd0\") }, $db: \"smol\" }"
          + " planSummary: COLLSCAN { _id: 1 } keysExamined:0 docsExamined:1000 numYields:0"
          + " reslen:201 locks:{ ReplicationStateTransition: { acquireCount: { w: 1 } }, Global: {"
          + " acquireCount: { r: 1 } }, Database: { acquireCount: { r: 1 } }, Collection: {"
          + " acquireCount: { r: 1 } }, Mutex: { acquireCount: { r: 1 } } } storage:{}"
          + " protocol:op_msg 0ms";

  private static final String SAMPLE_LOG_LINE_EXTENDED_ID_KEY =
      " I  COMMAND  [conn2769] command smol.smol appName: \"MongoDB Shell\" command: find { find:"
          + " \"smol\", filter: { a: ObjectId('53c994b05b78bace99013a02'), status: { $in: [ \"b\","
          + " \"c\" ] } }, sort: { _id: 1.0 }, lsid: { id:"
          + " UUID(\"fe02a0a0-2647-4350-a7c6-62fc6c1c82e9\") }, $db: \"smol\" } planSummary: IXSCAN"
          + " { _id: 1 } keysExamined:2000 docsExamined:2000 cursorExhausted:1 numYields:0"
          + " nreturned:0 queryHash:C5673234 planCacheKey:C5673234 reslen:98 locks:{"
          + " ReplicationStateTransition: { acquireCount: { w: 1 } }, Global: { acquireCount: { r:"
          + " 1 } }, Database: { acquireCount: { r: 1 } }, Collection: { acquireCount: { r: 1 } },"
          + " Mutex: { acquireCount: { r: 1 } } } storage:{ data: { bytesRead: 109,"
          + " timeReadingMicros: 11 }, timeWaitingMicros: { schemaLock: 2 } } protocol:op_msg"
          + " 500000ms";

  // sample structured logs with views
  private static final String SAMPLE_STRUCTURED_VIEW_LOG1 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn37527\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.helloView\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"helloView\",\"filter\":{\"a\":1},\"lsid\":{\"id\":{\"$uuid\":\"a6c5b04d-2096-4024-90d5-43c137739ce5\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.helloView\",\"dependencyChain\":[\"helloView\",\"hello\"],\"resolvedPipeline\":[]}],\"keysExamined\":0,\"docsExamined\":2000,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":0,\"queryHash\":\"4B53BE76\",\"planCacheKey\":\"943FC59B\",\"reslen\":103,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":4}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":4}}},\"storage\":{},\"remote\":\"127.0.0.1:54553\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VIEW_LOG2 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn37527\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.helloView\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"helloView\",\"filter\":{\"a\":1},\"lsid\":{\"id\":{\"$uuid\":\"a6c5b04d-2096-4024-90d5-43c137739ce5\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.helloView\",\"dependencyChain\":[\"helloView\",\"hello\"],\"resolvedPipeline\":[]}],\"keysExamined\":0,\"docsExamined\":2000,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":0,\"queryHash\":\"4B53BE76\",\"planCacheKey\":\"943FC59B\",\"reslen\":103,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":4}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":4}}},\"storage\":{},\"remote\":\"127.0.0.1:54553\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VIEW_LOG3 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn23055\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.timeseries\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"timeseries\",\"filter\":{\"a\":9999},\"lsid\":{\"id\":{\"$uuid\":\"ed083a25-0d3d-4fd5-a8e1-5abcd94f7846\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.timeseries\",\"dependencyChain\":[\"timeseries\",\"system.buckets.timeseries\"],\"resolvedPipeline\":[{\"$_internalUnpackBucket\":{\"timeField\":\"timeField\",\"bucketMaxSpanSeconds\":3600,\"exclude\":[]}}]}],\"keysExamined\":0,\"docsExamined\":2000,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":3,\"queryHash\":\"2128D8E3\",\"planCacheKey\":\"2AB3ADF6\",\"reslen\":461,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":5}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":5}}},\"storage\":{},\"remote\":\"127.0.0.1:49643\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VIEW_LOG4 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn23055\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.timeseries\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"timeseries\",\"filter\":{\"c\":0},\"sort\":{\"a\":1},\"lsid\":{\"id\":{\"$uuid\":\"ed083a25-0d3d-4fd5-a8e1-5abcd94f7846\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.timeseries\",\"dependencyChain\":[\"timeseries\",\"system.buckets.timeseries\"],\"resolvedPipeline\":[{\"$_internalUnpackBucket\":{\"timeField\":\"timeField\",\"bucketMaxSpanSeconds\":3600,\"exclude\":[]}}]}],\"keysExamined\":0,\"docsExamined\":2000,\"hasSortStage\":true,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":1,\"queryHash\":\"2D057D3C\",\"planCacheKey\":\"2FFB5A6A\",\"reslen\":104,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":5}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":5}}},\"storage\":{},\"remote\":\"127.0.0.1:49643\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VIEW_LOG5 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn23055\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.timeseries\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"timeseries\",\"filter\":{\"e\":{\"$binary\":{\"base64\":\"1235\",\"subType\":\"0\"}}},\"lsid\":{\"id\":{\"$uuid\":\"ed083a25-0d3d-4fd5-a8e1-5abcd94f7846\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.timeseries\",\"dependencyChain\":[\"timeseries\",\"system.buckets.timeseries\"],\"resolvedPipeline\":[{\"$_internalUnpackBucket\":{\"timeField\":\"timeField\",\"bucketMaxSpanSeconds\":3600,\"exclude\":[]}}]}],\"keysExamined\":0,\"docsExamined\":2000,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":1,\"queryHash\":\"E107827D\",\"planCacheKey\":\"615C35CB\",\"reslen\":104,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":5}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":5}}},\"storage\":{},\"remote\":\"127.0.0.1:49643\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VIEW_LOG6 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn23055\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.timeseries\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"timeseries\",\"filter\":{\"a\":9999},\"lsid\":{\"id\":{\"$uuid\":\"ed083a25-0d3d-4fd5-a8e1-5abcd94f7846\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.timeseries\",\"dependencyChain\":[\"timeseries\",\"system.buckets.timeseries\"],\"resolvedPipeline\":[{\"$_internalUnpackBucket\":{\"timeField\":\"timeField\",\"bucketMaxSpanSeconds\":3600,\"exclude\":[]}}]}],\"keysExamined\":0,\"docsExamined\":2000,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":1,\"queryHash\":\"2128D8E3\",\"planCacheKey\":\"2AB3ADF6\",\"reslen\":461,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":5}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":5}}},\"storage\":{},\"remote\":\"127.0.0.1:49643\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VIEW_LOG7 =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn23055\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"test.timeseries\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"timeseries\",\"filter\":{\"c\":0},\"sort\":{\"a\":1},\"lsid\":{\"id\":{\"$uuid\":\"ed083a25-0d3d-4fd5-a8e1-5abcd94f7846\"}},\"$db\":\"test\"},\"planSummary\":\"COLLSCAN\",\"resolvedViews\":[{\"viewNamespace\":\"test.timeseries\",\"dependencyChain\":[\"timeseries\",\"system.buckets.timeseries\"],\"resolvedPipeline\":[{\"$_internalUnpackBucket\":{\"timeField\":\"timeField\",\"bucketMaxSpanSeconds\":3600,\"exclude\":[]}}]}],\"keysExamined\":0,\"docsExamined\":2000,\"hasSortStage\":true,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":1,\"queryHash\":\"2D057D3C\",\"planCacheKey\":\"2FFB5A6A\",\"reslen\":104,\"locks\":{\"ParallelBatchWriterMode\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":5}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":5}}},\"storage\":{},\"remote\":\"127.0.0.1:49643\",\"protocol\":\"op_msg\",\"durationMillis\":210000}}";

  // sample structured logs with "truncated"
  private static final String SAMPLE_STRUCTURED_TRUNCATED_LOG =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn864\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"smol.foo\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"foo\",\"filter\":{\"b\":1,\"c\":{\"$in\":[1,2,3,4,5,6,7,8,9,10]}}},\"planSummary\":\"COLLSCAN\",\"planningTimeMicros\":661,\"keysExamined\":0,\"docsExamined\":100000,\"fromPlanCache\":true,\"nBatches\":1,\"cursorExhausted\":true,\"numYields\":100,\"nreturned\":0,\"queryHash\":\"980E1314\",\"planCacheKey\":\"F89250A7\",\"queryFramework\":\"sbe\",\"reslen\":97,\"locks\":{\"FeatureCompatibilityVersion\":{\"acquireCount\":{\"r\":101}},\"Global\":{\"acquireCount\":{\"r\":101}}},\"storage\":{},\"remote\":\"127.0.0.1:61415\",\"protocol\":\"op_msg\",\"durationMillis\":40},\"truncated\":{\"command\":{\"filter\":{\"c\":{\"$in\":{\"2261\":{\"type\":\"double\",\"size\":8}}}}}},\"size\":{\"command\":41012}}";

  private static final String SAMPLE_STRUCTURED_LOG =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,  "
          + " \"ctx\":\"conn208\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"ns\":\"smol.floof\",\"appName\":\"MongoDB"
          + " Shell\",\"command\":{\"find\":\"floof\",\"filter\":{\"binaryList\":{\"$in\":[{\"$binary\":{\"base64\":\"abcd\",\"subType\":\"0\"}},{\"$binary\":{\"base64\":\"ef01\",\"subType\":\"0\"}}]}},\"lsid\":{\"id\":{\"$uuid\":\"08d3e9d2-dae8-4960-87a2-d728bdfe34e5\"}},\"$clusterTime\":{\"clusterTime\":{\"$timestamp\":{\"t\":1712948870,\"i\":4009}},\"signature\":{\"hash\":{\"$binary\":{\"base64\":\"AAAAAAAAAAAAAAAAAAAAAAAAAAA=\",\"subType\":\"0\"}},\"keyId\":0}},\"$db\":\"smol\"},\"planSummary\":\"COLLSCAN\",\"keysExamined\":0,\"docsExamined\":2000,\"cursorExhausted\":true,\"numYields\":0,\"nreturned\":0,\"queryHash\":\"F9CA6F39\",\"planCacheKey\":\"F9CA6F39\",\"reslen\":394,\"locks\":{\"FeatureCompatibilityVersion\":{\"acquireCount\":{\"r\":1}},\"ReplicationStateTransition\":{\"acquireCount\":{\"w\":1}},\"Global\":{\"acquireCount\":{\"r\":1}},\"Database\":{\"acquireCount\":{\"r\":1}},\"Collection\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":1}}},\"storage\":{},\"protocol\":\"op_msg\",\"durationMillis\":210000}}";
  private static final String SAMPLE_STRUCTURED_VERSION_8_LOG =
      "{\"t\":{\"$date\":\"%s\"},\"s\":\"I\",  \"c\":\"COMMAND\",  \"id\":51803,   \"svc\":\"S\","
          + " \"ctx\":\"conn34\",\"msg\":\"Slow"
          + " query\",\"attr\":{\"type\":\"command\",\"isFromUserConnection\":true,\"ns\":\"smol.floof\",\"collectionType\":\"normal\",\"appName\":\"MongoDB"
          + " Shell\",\"queryShapeHash\":\"DAC80BEA8EAB892425FF5D9D83A5CA577CC5143930B58E8CE1C4011DC32CD937\",\"command\":{\"find\":\"floof\",\"filter\":{\"z\":10},\"lsid\":{\"id\":{\"$uuid\":\"183a7ce1-f388-4993-a36e-cb9cf18aadac\"}},\"$clusterTime\":{\"clusterTime\":{\"$timestamp\":{\"t\":1713364459,\"i\":22}},\"signature\":{\"hash\":{\"$binary\":{\"base64\":\"AAAAAAAAAAAAAAAAAAAAAAAAAAA=\",\"subType\":\"0\"}},\"keyId\":0}},\"$db\":\"smol\"},\"planSummary\":\"COLLSCAN\",\"planningTimeMicros\":9,\"cursorid\":4668460187490744486,\"keysExamined\":0,\"docsExamined\":6101,\"nBatches\":1,\"numYields\":0,\"nreturned\":101,\"queryHash\":\"D87DA668\",\"planCacheKey\":\"2939A298\",\"queryFramework\":\"classic\",\"reslen\":6576,\"locks\":{\"Global\":{\"acquireCount\":{\"r\":1}},\"Mutex\":{\"acquireCount\":{\"r\":2}}},\"storage\":{},\"remote\":\"127.0.0.1:54849\",\"protocol\":\"op_msg\",\"workingMillis\":210000,\"durationMillis\":210000,\"queues\":{\"execution\":{\"admissions\":2,\"totalTimeQueuedMicros\":0},\"ingress\":{\"admissions\":1,\"totalTimeQueuedMicros\":0}}}}";
  private static final int LOG_LINES_LIMIT = 24;
  private static final int LOG_LINES_COUNT = 10;
  private static final int LOG_LINES_EXTENDED_COUNT = 21;
  private static final int LOG_LINES_NEGATION_COUNT = 3;
  private static final int LOG_LINES_SKIP_COUNT = 2;
  private static final int LOG_LINES_STRUCTURED_EXTENDED_COUNT = 20;
  public static final List<String> LOG_LINES = new ArrayList<>(LOG_LINES_COUNT);
  public static final List<String> LOG_LINES_EXTENDED = new ArrayList<>(LOG_LINES_EXTENDED_COUNT);
  public static final List<String> LOG_LINES_NEGATION = new ArrayList<>(LOG_LINES_NEGATION_COUNT);
  public static final List<String> LOG_LINES_SKIP = new ArrayList<>(LOG_LINES_SKIP_COUNT);
  public static final List<String> LOG_LINES_DROP = new ArrayList<>();
  public static final List<String> LOG_LINES_WITH_COUNT = new ArrayList<>(1);
  public static final List<String> LOG_LINES_WITH_DISTINCT = new ArrayList<>(1);
  public static final List<String> LOG_LINES_STRUCTURED = new ArrayList<>(1);
  public static final List<String> LOG_LINES_EXTENDED_WITH_ID_KEY = new ArrayList<>(1);
  public static final List<String> LOG_LINES_STRUCTURED_EXTENDED =
      new ArrayList<>(LOG_LINES_STRUCTURED_EXTENDED_COUNT);
  public static final List<String> LOG_LINES_STRUCTURED_VIEWS = new ArrayList<>();
  public static final List<String> LOG_LINES_STRUCTURED_TRUNCATED = new ArrayList<>(1);

  static {
    final List<String> logLines =
        newArrayList(
            SHAPE_A_1_PERCENT_UTIL_37500_MS_LINE0,
            SHAPE_A_1_PERCENT_UTIL_37500_MS_LINE1,
            SHAPE_AB_39_PERCENT_UTIL_37500_MS_LINE0,
            SHAPE_C_85_PERCENT_UTIL_150000_MS_LINE,
            SHAPE_AB_39_PERCENT_UTIL_37500_MS_LINE1,
            SHAPE_X_95_PERCENT_UTIL_150000_MS_LINE);

    final List<String> logLinesExtended =
        newArrayList(
            SAMPLE_LOG_LINE_EXTENDED0,
            SAMPLE_LOG_LINE_EXTENDED1,
            SAMPLE_LOG_LINE_EXTENDED2,
            SAMPLE_LOG_LINE_EXTENDED3,
            SAMPLE_LOG_LINE_EXTENDED4,
            SAMPLE_LOG_LINE_EXTENDED5,
            SAMPLE_LOG_LINE_EXTENDED6,
            SAMPLE_LOG_LINE_EXTENDED7,
            SAMPLE_LOG_LINE_EXTENDED8,
            SAMPLE_LOG_LINE_EXTENDED9,
            SAMPLE_LOG_LINE_EXTENDED10,
            SAMPLE_LOG_LINE_EXTENDED11,
            SAMPLE_LOG_LINE_EXTENDED12,
            SAMPLE_LOG_LINE_EXTENDED13,
            SAMPLE_LOG_LINE_EXTENDED14,
            SAMPLE_LOG_LINE_EXTENDED15,
            SAMPLE_LOG_LINE_EXTENDED16,
            SAMPLE_LOG_LINE_EXTENDED17,
            SAMPLE_LOG_LINE_EXTENDED18,
            SAMPLE_LOG_LINE_EXTENDED19,
            SAMPLE_LOG_LINE_EXTENDED20);

    final List<String> logLinesNegation =
        newArrayList(
            SAMPLE_LOG_LINE_NEGATION0, SAMPLE_LOG_LINE_NEGATION1, SAMPLE_LOG_LINE_NEGATION2);

    final List<String> logLinesSkip =
        newArrayList(SAMPLE_LOG_LINE_SKIP_NRETURNED_NON_ZERO, SAMPLE_LOG_LINE_SKIP_NRETURNED_ZERO);

    final List<String> logLinesStructuredViews =
        newArrayList(
            SAMPLE_STRUCTURED_VIEW_LOG1,
            SAMPLE_STRUCTURED_VIEW_LOG2,
            SAMPLE_STRUCTURED_VIEW_LOG3,
            SAMPLE_STRUCTURED_VIEW_LOG4,
            SAMPLE_STRUCTURED_VIEW_LOG5,
            SAMPLE_STRUCTURED_VIEW_LOG6,
            SAMPLE_STRUCTURED_VIEW_LOG7);
    final long start = System.currentTimeMillis();

    int i = 0;
    for (; i < LOG_LINES_LIMIT - 1; i++) {
      // Adds timestamps going back LOG_LINES_COUNT - i milliseconds for the ith log line (max is
      // LOG_LINES_COUNT millis ago)

      String logLine = logLines.get(i % (logLines.size()));
      final long timestamp = start + i;
      final String timestampStr = TIMESTAMP_FORMAT.format(new Date(timestamp));
      LOG_LINES.add(timestampStr + logLine);
    }
    // sanity check collected indexes dedupe process
    LOG_LINES.add(TIMESTAMP_FORMAT.format(new Date(start)) + COVERED_BY_INDEX_LINE);
    // Set last timestamp far back enough so time span includes all host measurements
    LOG_LINES.add(
        TIMESTAMP_FORMAT.format(new Date(start - 60 * 60 * 1000))
            + logLines.get(i % logLines.size()));

    // Log lines to be dropped
    List<String> logsToDrop =
        List.of(
            DROP_SAMPLE_LOG_LINE_SYSTEM_COLL1,
            DROP_SAMPLE_LOG_LINE_SYSTEM_COLL2,
            DROP_SAMPLE_LOG_LINE_SYSTEM_COLL3,
            DROP_SAMPLE_LOG_LINE_SYSTEM_COLL4,
            DROP_SAMPLE_LOG_LINE_SYSTEM_COLL5);

    for (int j = 0; j < logsToDrop.size(); j++) {
      final String log =
          TIMESTAMP_FORMAT.format(new Date(start - logsToDrop.size() + j)) + logsToDrop.get(j);
      LOG_LINES.add(log);
      LOG_LINES_DROP.add(log);
    }

    for (int k = 0; k < LOG_LINES_EXTENDED_COUNT; k++) {
      // Adds timestamps going back LOG_LINES_EXTENDED_COUNT - i milliseconds for the ith log line
      // (max is LOG_LINES_EXTENDED_COUNT millis ago)

      final long timestamp = start - LOG_LINES_EXTENDED_COUNT - k;
      LOG_LINES_EXTENDED.add(
          TIMESTAMP_FORMAT.format(new Date(timestamp)) + logLinesExtended.get(k));
    }

    for (int k = 0; k < LOG_LINES_NEGATION_COUNT; k++) {
      // Adds timestamps going back LOG_LINES_EXTENDED_COUNT - i milliseconds for the ith log line
      // (max is LOG_LINES_EXTENDED_COUNT millis ago)

      final long timestamp = start - LOG_LINES_NEGATION_COUNT - k;
      LOG_LINES_NEGATION.add(
          TIMESTAMP_FORMAT.format(new Date(timestamp)) + logLinesNegation.get(k));
    }

    for (int k = 0; k < LOG_LINES_SKIP_COUNT; k++) {
      // Adds timestamps going back LOG_LINES_SKIP_COUNT - i milliseconds for the ith log line
      // (max is LOG_LINES_SKIP_COUNT millis ago)

      final long timestamp = start - LOG_LINES_SKIP_COUNT - k;
      LOG_LINES_SKIP.add(TIMESTAMP_FORMAT.format(new Date(timestamp)) + logLinesSkip.get(k));
    }

    LOG_LINES_WITH_COUNT.add(TIMESTAMP_FORMAT.format(new Date(start - 1)) + SAMPLE_LOG_LINE_COUNT);

    LOG_LINES_WITH_DISTINCT.add(
        TIMESTAMP_FORMAT.format(new Date(start - 1)) + SAMPLE_LOG_LINE_DISTINCT);

    LOG_LINES_STRUCTURED.add(
        String.format(SAMPLE_STRUCTURED_LOG, TIMESTAMP_FORMAT.format(new Date(start - 2))));

    LOG_LINES_STRUCTURED.add(
        String.format(
            SAMPLE_STRUCTURED_VERSION_8_LOG, TIMESTAMP_FORMAT.format(new Date(start - 1))));

    LOG_LINES_EXTENDED_WITH_ID_KEY.add(
        TIMESTAMP_FORMAT.format(new Date(start - 1)) + SAMPLE_LOG_LINE_EXTENDED_ID_KEY);

    for (int k = 0; k < logLinesStructuredViews.size(); k++) {
      // Adds timestamps going back logLinesStructuredViews.size() - i milliseconds for the ith log
      // line
      // (max is logLinesStructuredViews.size() millis ago)
      final long timestamp = start - logLinesStructuredViews.size() - k;
      LOG_LINES_STRUCTURED_VIEWS.add(
          String.format(
              logLinesStructuredViews.get(k), TIMESTAMP_FORMAT.format(new Date(timestamp))));
    }

    LOG_LINES_STRUCTURED_TRUNCATED.add(
        String.format(
            SAMPLE_STRUCTURED_TRUNCATED_LOG, TIMESTAMP_FORMAT.format(new Date(start - 1))));

    for (int k = 0; k < LOG_LINES_STRUCTURED_EXTENDED_COUNT; k++) {
      final String sampleLogLine =
          "{\n"
              + "  \"t\": { \"$date\": \"%s\" },\n"
              + "  \"s\": \"I\",\n"
              + "  \"c\": \"COMMAND\",\n"
              + "  \"id\": 51803,\n"
              + "  \"ctx\": \"conn3\",\n"
              + "  \"msg\": \"Slow query\",\n"
              + "  \"attr\": {\n"
              + "    \"type\": \"command\",\n"
              + "    \"ns\": \"collection_placeholder.foo\",\n"
              + "    \"appName\": \"MongoDB Shell\",\n"
              + "    \"command\": {\n"
              + "      \"find\": \"c\",\n"
              + "      \"filter\": { \"%s\": 1 },\n"
              + "      \"lsid\": { \"id\": { \"$uuid\": \"92505d32-9ccf-4db0-8dc7-3724878aeb90\" }"
              + " },\n"
              + "      \"$db\": \"collection_placeholder\"\n"
              + "    },\n"
              + "    \"keysExamined\": 0,\n"
              + "    \"docsExamined\": 2000,\n"
              + "    \"fromMultiPlanner\": true,\n"
              + "    \"cursorExhausted\": true,\n"
              + "    \"numYields\": 0,\n"
              + "    \"nreturned\": 1,\n"
              + "    \"queryHash\": \"4D5D5EFF\",\n"
              + "    \"planCacheKey\": \"FA14BDC1\",\n"
              + "    \"reslen\": 115,\n"
              + "    \"locks\": {\n"
              + "      \"Global\": { \"acquireCount\": { \"r\": 3 } },\n"
              + "      \"Mutex\": { \"acquireCount\": { \"r\": 3 } }\n"
              + "    },\n"
              + "    \"storage\": { \"data\": { \"bytesRead\": 108, \"timeReadingMicros\": 4 }"
              + " },\n"
              + "    \"operationMetrics\": {\n"
              + "      \"docBytesRead\": %s,\n"
              + "      \"docUnitsRead\": 1,\n"
              + "      \"cpuNanos\": 1\n"
              + "    },\n"
              + "    \"remote\": \"127.0.0.1:60769\",\n"
              + "    \"protocol\": \"op_msg\",\n"
              + "    \"durationMillis\": 500000\n"
              + "  }\n"
              + "}\n";
      // Adds timestamps going back logLinesStructuredViews.size() - i milliseconds for the ith log
      // line
      // (max is logLinesStructuredViews.size() millis ago)
      final long timestamp = start - logLinesStructuredViews.size() - k;
      if (k == 0) {
        // for first entry, have low docBytesRead
        LOG_LINES_STRUCTURED_EXTENDED.add(
            String.format(
                sampleLogLine,
                STRUCTURED_TIMESTAMP_FORMAT.format(new Date(timestamp)),
                (char) ('a' + k),
                "116"));
      } else {
        LOG_LINES_STRUCTURED_EXTENDED.add(
            String.format(
                sampleLogLine,
                STRUCTURED_TIMESTAMP_FORMAT.format(new Date(timestamp)),
                (char) ('a' + k),
                "1116"));
      }
    }

    // Lines below should be disregarded
    LOG_LINES.add(TIMESTAMP_FORMAT.format(new Date(start - 1)) + IGNORE_LOG_LINE6);
    LOG_LINES.add(TIMESTAMP_FORMAT.format(new Date(start - 1)) + IGNORE_LOG_LINE7);
  }
}
