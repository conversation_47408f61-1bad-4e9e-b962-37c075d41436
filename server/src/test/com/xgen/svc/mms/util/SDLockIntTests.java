package com.xgen.svc.mms.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.DBObject;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCollection;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.util._public.util.SDLock;
import com.xgen.svc.common.TestDataUtils;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SDLockIntTests {

  MongoClient _client;
  DBCollection _lockColl;

  @BeforeEach
  public void setUp() throws Exception {
    _client = new MongoClient("localhost", TestDataUtils.TEST_MONGO_PORT);
    _lockColl = getTestLocksCollection(_client);
    _lockColl.drop();
  }

  @AfterEach
  public void tearDown() {
    _client.close();
  }

  @Test
  public void testHeartbeatLockAcquire() throws Exception {
    SDLock distLock = new SDLock(_lockColl);
    SDLock.HeartbeatingLock lock = distLock.getLock("resource");
    SDLock.AcquireResult result =
        lock.acquire(new SDLock.LockAcquireOptions(), new SDLock.BeaterOptions());
    assertTrue(result.locked);

    SDLock.HeartbeatingLock competingLock = distLock.getLock("resource");
    final SDLock.LockAcquireOptions quickAcquire = new SDLock.LockAcquireOptions();
    quickAcquire.timeToTryMs = 1000;
    SDLock.AcquireResult failedResult =
        competingLock.acquire(quickAcquire, new SDLock.BeaterOptions());
    assertFalse(failedResult.locked);
    assertTrue(lock.release().released);
  }

  @Test
  public void testHeartbeatLockTryAcquire() throws Exception {
    SDLock distLock = new SDLock(_lockColl);
    SDLock.HeartbeatingLock lock = distLock.getLock("resource");
    assertTrue(lock.tryAcquire(new SDLock.BeaterOptions()).locked);

    // tryAcquire on lock that's already taken - should fail
    SDLock.HeartbeatingLock competingLock = distLock.getLock("resource");
    assertFalse(competingLock.tryAcquire(new SDLock.BeaterOptions()).locked);

    // release original lock
    assertTrue(lock.release().released);

    // tryAcquire again now that lock's released
    assertTrue(competingLock.tryAcquire(new SDLock.BeaterOptions()).locked);
    assertTrue(competingLock.release().released);
  }

  @Test
  public void testHeartbeatLockMetadata() throws Exception {
    final SDLock sdLock = new SDLock(_lockColl);
    final SDLock.HeartbeatingLock lockWithoutMetadata = sdLock.getLock("resourceWithoutMetadata");
    assertTrue(lockWithoutMetadata.tryAcquire(new SDLock.BeaterOptions()).locked);
    final List<DBObject> locks0 = _lockColl.find().toArray();
    assertEquals(1, locks0.size());
    assertTrue(locks0.get(0).containsField("owned"));
    final DBObject locks0Owned = (DBObject) locks0.get(0).get("owned");
    assertEquals(2, locks0Owned.keySet().size());
    assertTrue(locks0Owned.containsField("lockId"));
    assertTrue(locks0Owned.containsField("timeoutMs"));

    final SDLock.HeartbeatingLock lockWithMetadata =
        sdLock.getLock("resourceWithMetadata", () -> Map.of("myCustomMetadata", "isTheBest!"));
    assertTrue(lockWithMetadata.tryAcquire(new SDLock.BeaterOptions()).locked);
    final List<DBObject> locks1 = _lockColl.find().toArray();
    assertEquals(2, locks1.size());
    assertEquals(locks0.get(0), locks1.get(0));
    assertTrue(locks1.get(1).containsField("owned"));
    final DBObject locks1Owned = (DBObject) locks1.get(1).get("owned");
    assertEquals(3, locks1Owned.keySet().size());
    assertTrue(locks1Owned.containsField("lockId"));
    assertTrue(locks1Owned.containsField("timeoutMs"));
    assertTrue(locks1Owned.containsField("myCustomMetadata"));
  }

  @Test
  public void testKillingLock() throws Exception {
    SDLock distLock = new SDLock(_lockColl);

    SDLock.KillingLock killingLock = distLock.getKillingLock("resource");
    SDLock.KillingOptions options = new SDLock.KillingOptions();
    options.lockTimeoutThresholdMs = TimeUnit.SECONDS.toMillis(2);
    options.sleepBetweenUpdatesMs = TimeUnit.MINUTES.toMillis(5);
    options.lockTimeRemainingBeforeKillMs = TimeUnit.SECONDS.toMillis(1);
    options.timeBetweenPollsMs = 100;
    final TestKillable killable =
        new TestKillable(TimeUnit.SECONDS.toMillis(1), TimeUnit.SECONDS.toMillis(2));

    assertTrue(killingLock.acquire(new SDLock.LockAcquireOptions(), options, killable).locked);
    try {
      Thread.sleep(TimeUnit.SECONDS.toMillis(2));
      assertTrue(killable._killCalled, "killable callback not called");
    } finally {
      killingLock.release();
    }
  }

  private static DBCollection getTestLocksCollection(final MongoClient pClient) {
    return DbUtils.getDB(pClient, "dist_lock").getCollection("locks");
  }

  static class TestKillable implements SDLock.Killable {
    final long _start = System.currentTimeMillis();
    final long _minVal;
    final long _maxVal;
    volatile boolean _killCalled;

    TestKillable(long minVal, long maxVal) {
      _minVal = minVal;
      _maxVal = maxVal;
    }

    public void kill() {
      _killCalled = true;
      final long timeTilKilledMs = System.currentTimeMillis() - _start;
      assertTrue(
          timeTilKilledMs >= _minVal && timeTilKilledMs <= _maxVal,
          "Bad kill: " + timeTilKilledMs + " min:" + _minVal + " max:" + _maxVal);
    }
  }
}
