package com.xgen.svc.mms.util;

import static com.xgen.svc.mms.util.AuthzSvcResourceTestUtils.methodNamesWithAuthActions;

import com.google.common.reflect.TypeToken;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AuthzSvcJUnit5ResourceClassTest<T> extends AuthzSvcJUnit5ResourceTest {
  // To run just this test in subclass add:
  // @Test @Override public void testEndpointsWithFga() { super.testEndpointsWithFga(); }

  private final Logger LOG = LoggerFactory.getLogger(getClass());

  @Test
  @Override
  @Disabled(
      "Removed @Auth annotations from MMS endpoints as part of Milestone 1D of Action Primitives."
          + " Enable test again when the @Auth annotations are added back.")
  public void testEndpointsWithFga() {
    final Class<?> clazz = new TypeToken<T>(getClass()) {}.getRawType();
    final List<String> methodNames = getEndpointMethodNames(clazz);
    runAuthzEndpointChecks(clazz, methodNames);
  }

  public void testEndpointsWithFgaExcluding(final Set<String> excludedMethodNames) {
    final Class<?> clazz = new TypeToken<T>(getClass()) {}.getRawType();

    final List<String> allMethodNames = getEndpointMethodNames(clazz);
    final List<String> methodNames =
        allMethodNames.stream()
            .filter(f -> !excludedMethodNames.contains(f))
            .collect(Collectors.toList());

    runAuthzEndpointChecks(clazz, methodNames);
  }

  private List<String> getEndpointMethodNames(final Class<?> clazz) {
    final List<String> methodNames = methodNamesWithAuthActions(clazz);
    if (methodNames.isEmpty()) {
      throw new IllegalStateException("No @Auth annotated endpoints found for: " + clazz);
    }
    return methodNames;
  }

  private void runAuthzEndpointChecks(final Class<?> clazz, final List<String> methodNames) {
    for (final String methodName : methodNames) {
      LOG.info("Running Authz @Auth endpoint check for: " + clazz + "::" + methodName);
      runAuthzAuthedEndpoint(clazz, methodName);
    }
  }
}
