package com.xgen.svc.mms.util.billing.scenarios.generic;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.jobqueue.JobQueueTestUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient;
import com.xgen.cloud.payments.grpc._public.client.RefundQueryingClient;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.CreditDao;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.dao.billing.PaymentMethodDao;
import com.xgen.svc.mms.dao.marketing.SalesforceProductCodeDao;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.ElasticBillingMethod;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.DailyBillingRunMode;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.Scenario;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioCreditCardPaymentMethod;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioExecutionConfig;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioInvoice;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioLinkedOrg;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioOpportunity;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioOpportunityCredit;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioOpportunityCreditRollover;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GenericBillingScenarioIntTests extends JUnit5BaseSvcTest {
  private static final Date STATIC_NOW = TimeUtils2.fromISOString("2024-10-05T10:23:10Z");
  @Inject RefundQueryingClient refundQueryingClient;
  @Inject private CreditDao creditDao;
  @Inject private InvoiceDao invoiceDao;
  @Inject private PaymentMethodDao paymentMethodDao;
  @Inject private PaymentDao paymentDao;
  @Inject private SalesforceProductCodeDao salesforceProductCodeDao;
  @Inject private GenericBillingScenario genericBillingScenario;
  @Inject private LineItemDao lineItemDao;
  @Inject private PayingOrgSvc payingOrgSvc;
  @Inject private OrganizationSvc organizationSvc;
  @Inject private JobQueueTestUtils jobQueueTestUtils;
  @Inject private PaymentMethodClient paymentMethodClient;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    salesforceProductCodeDao.syncDocuments();
    mockClockTime(STATIC_NOW.toInstant());

    doReturn(List.of()).when(refundQueryingClient).getRefundsByInvoiceId(any());
    doAnswer(
            invocation -> {
              ObjectId orgId = invocation.getArgument(0);
              // Ignore the useEffectivePayingOrg parameter and just pass the orgId
              PaymentMethod paymentMethod = paymentMethodDao.findActiveByOrgId(orgId);
              return java.util.Optional.ofNullable(paymentMethod);
            })
        .when(paymentMethodClient)
        .getActivePaymentMethod(any(ObjectId.class), any(Boolean.class));
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getMostRecentBeforeOrEqualToDate(any(), any());
  }

  @Test
  public void testGenerate_prepaidCreditWithRollover() {
    Scenario scenario =
        Scenario.builder()
            .orgName("some name")
            .opportunities(
                List.of(
                    ScenarioOpportunity.builder()
                        .currency("USD")
                        .elasticBillingMethod(ElasticBillingMethod.ContractRate)
                        .applyOn(LocalDate.of(2024, 9, 1))
                        .credits(
                            List.of(
                                ScenarioOpportunityCredit.builder()
                                    .creditType(CreditType.PREPAID_NDS)
                                    .startDate(LocalDate.of(2024, 8, 1))
                                    .endDate(LocalDate.of(2025, 8, 1))
                                    .amountCents(100000)
                                    .rollover(
                                        ScenarioOpportunityCreditRollover.builder()
                                            .amountCents(1000000)
                                            .build())
                                    .build()))
                        .build()))
            .invoices(
                List.of(
                    new ScenarioInvoice(1000),
                    new ScenarioInvoice(2000),
                    new ScenarioInvoice(3000)))
            .build();

    Organization org = genericBillingScenario.generate(scenario);

    List<Invoice> invoices =
        invoiceDao.findByOrgId(org.getId(), true).stream()
            .sorted(Comparator.comparing(Invoice::getStartDate))
            .toList();

    assertThat(lineItemDao.getInvoiceCreditsTotal(invoices.get(0).getId())).isEqualTo(-31 * 3000);
    assertThat(lineItemDao.getInvoiceCreditsTotal(invoices.get(1).getId())).isEqualTo(-30 * 2000);
    assertThat(lineItemDao.getInvoiceCreditsTotal(invoices.get(2).getId())).isEqualTo(-4 * 1000);

    List<Credit> credits =
        creditDao.findByOrgId(org.getId()).stream()
            .sorted(Comparator.comparing(Credit::getEndDate))
            .toList();
    Credit mainCredit = credits.get(0);
    Credit rolloverCredit = credits.get(1);

    assertThat(org.getName()).isEqualTo(scenario.orgName());
    assertThat(mainCredit.getType()).isEqualTo(CreditType.PREPAID_NDS);
    assertThat(mainCredit.getStartDate()).isEqualTo(dateOf(LocalDate.of(2024, 8, 1)));
    assertThat(mainCredit.getEndDate()).isEqualTo(dateOf(LocalDate.of(2025, 8, 1)));
    assertThat(rolloverCredit.getType()).isEqualTo(CreditType.PREPAID_NDS);
    assertThat(rolloverCredit.isRollover()).isTrue();
    assertThat(rolloverCredit.getStartDate()).isEqualTo(dateOf(LocalDate.of(2024, 8, 1)));
    assertThat(rolloverCredit.getEndDate()).isEqualTo(dateOf(LocalDate.of(2026, 8, 1)));

    assertThat(mainCredit.getAmountCents()).isEqualTo(100000);
    assertThat(mainCredit.getAmountRemainingCents()).isEqualTo(0);
    assertThat(rolloverCredit.getAmountCents()).isEqualTo(1000000);
    assertThat(rolloverCredit.getAmountRemainingCents()).isEqualTo(943000L);
  }

  @Test
  public void testGenerate_selfServe_dailyBillingInDailyMode_MOB() {
    Scenario scenario =
        Scenario.builder()
            .orgName("some name")
            .invoices(List.of(new ScenarioInvoice(1000), new ScenarioInvoice(2000)))
            .maxOutstandingBillCents(2000L * 15)
            .selfServePaymentMethod(
                ScenarioCreditCardPaymentMethod.builder()
                    .billingAccount(BillingAccount.MONGODB_INC)
                    .build())
            .executionConfig(
                ScenarioExecutionConfig.builder()
                    .dailyBillingRunMode(DailyBillingRunMode.DAILY)
                    .build())
            .build();

    Organization org = genericBillingScenario.generate(scenario);

    List<Invoice> invoices =
        invoiceDao.findByOrgId(org.getId(), true).stream()
            .sorted(Comparator.comparing(Invoice::getStartDate))
            .toList();
    long numOfDaysInInvoice =
        LocalDate.ofInstant(clock.instant(), ZoneOffset.UTC).minusMonths(1).lengthOfMonth();

    assertThat(invoices.get(0).getAmountBilledCents()).isEqualTo(numOfDaysInInvoice * 2000);
    assertThat(org.getName()).isEqualTo(scenario.orgName());

    PaymentMethod paymentMethod = paymentMethodDao.findActiveByOrgId(org.getId());
    assertThat(paymentMethod.getType()).isEqualTo(PaymentMethodType.CREDIT_CARD);
    assertThat(paymentMethod.getStatus()).isEqualTo(PaymentMethod.Status.ACTIVE);

    List<Payment> payments =
        paymentDao.findByOrgId(org.getId()).stream()
            .sorted(Comparator.comparing(Payment::getCreated))
            .toList();
    // dailyBilling is running in DAILY mode so mid month payment will be created right when it
    // exceeds MOB
    assertThat(payments.get(0).getAmountBilledCents()).isEqualTo(16 * 2000);
    // this payment is created on the invoice closure
    assertThat(payments.get(1).getAmountBilledCents()).isEqualTo(2000 * (numOfDaysInInvoice - 16));
  }

  @Test
  public void testGenerate_crossOrg_awsElastic() {
    Scenario scenario =
        Scenario.builder()
            .orgName("some name")
            .opportunities(
                List.of(
                    ScenarioOpportunity.builder()
                        .currency("USD")
                        .elasticBillingMethod(ElasticBillingMethod.ContractRate)
                        .applyOn(LocalDate.of(2024, 10, 1))
                        .credits(
                            List.of(
                                ScenarioOpportunityCredit.builder()
                                    .creditType(CreditType.AWS_PREPAID)
                                    .startDate(LocalDate.of(2024, 10, 1))
                                    .endDate(LocalDate.of(2025, 10, 1))
                                    .amountCents(0)
                                    .elasticInvoicing(true)
                                    .build()))
                        .build()))
            .invoices(List.of(new ScenarioInvoice(1000)))
            .linkedOrgs(
                List.of(
                    ScenarioLinkedOrg.builder()
                        .orgName("linked1")
                        .invoices(List.of(new ScenarioInvoice(2000)))
                        .build(),
                    ScenarioLinkedOrg.builder()
                        .orgName("linked2")
                        .invoices(List.of(new ScenarioInvoice(3000)))
                        .build()))
            .build();

    Organization org = genericBillingScenario.generate(scenario);
    // Orgs linking is async
    jobQueueTestUtils.processAllJobsInQueue();

    List<Invoice> payingOrgInvoices = invoiceDao.findByOrgId(org.getId(), true);
    assertThat(payingOrgInvoices).hasSize(1);
    assertThat(lineItemDao.getInvoiceCreditsTotal(payingOrgInvoices.get(0).getId()))
        .isEqualTo(-4 * 1000);

    List<ObjectId> linkedOrgIds =
        payingOrgSvc.findByPayingOrgId(org.getId()).orElseThrow().getLinkedOrgIds();
    List<Organization> linkedOrgs = organizationSvc.findByIds(linkedOrgIds);
    assertThat(linkedOrgs).hasSize(2);

    Organization linkedOrg1 =
        linkedOrgs.stream().filter(o -> o.getName().equals("linked1")).findFirst().orElseThrow();
    Organization linkedOrg2 =
        linkedOrgs.stream().filter(o -> o.getName().equals("linked2")).findFirst().orElseThrow();

    List<Invoice> invoices1 = invoiceDao.findByOrgId(linkedOrg1.getId(), true);
    assertThat(invoices1).hasSize(1);
    assertThat(lineItemDao.getInvoiceCreditsTotal(invoices1.get(0).getId())).isEqualTo(-4 * 2000);

    List<Invoice> invoices2 = invoiceDao.findByOrgId(linkedOrg2.getId(), true);
    assertThat(invoices2).hasSize(1);
    assertThat(lineItemDao.getInvoiceCreditsTotal(invoices2.get(0).getId())).isEqualTo(-4 * 3000);

    List<Credit> credits = creditDao.findByOrgId(org.getId());
    assertThat(credits).hasSize(1);
    assertThat(credits.get(0).getType()).isEqualTo(CreditType.AWS_PREPAID);
    assertThat(credits.get(0).getAmountCents()).isEqualTo(0);
    assertThat(credits.get(0).getAmountRemainingCents()).isEqualTo(-24000);
  }
}
