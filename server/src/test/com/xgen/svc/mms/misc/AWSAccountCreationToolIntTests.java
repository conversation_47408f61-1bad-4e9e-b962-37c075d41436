package com.xgen.svc.mms.misc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.amazonaws.services.ec2.model.AvailabilityZone;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAvailabilityZone;
import com.xgen.cloud.nds.aws._public.model.AWSAvailabilityZone.Status;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

public class AWSAccountCreationToolIntTests extends JUnit5BaseSvcTest {
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private AWSApiSvc _awsApiSvc;

  @Override
  public void setUp() throws Exception {
    super.setUp();
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void test_generateAWSAccountSafe(boolean isForDataPlaneNodes) {
    final AWSAccountCreationTool tool =
        new AWSAccountCreationTool(_awsAccountDao, createMockAwsApiSvc());

    // The tool assumes there was an AWS Account to begin with.
    final AWSAccount sourceAwsAccount =
        NDSModelTestFactory.getFullyAvailableAWSAccount4Regions(
                new ObjectId(), "cloud-prod-1", false)
            .toBuilder()
            .setForDataPlaneNodes(isForDataPlaneNodes)
            .build();

    // Constrain ap-northeast-1d
    final AWSAvailabilityZone constrainedAz =
        sourceAwsAccount.getRegions().get(1).getAvailabilityZones().get(2).toBuilder()
            .status(Status.CONSTRAINED)
            .build();
    sourceAwsAccount.getRegions().get(1).getAvailabilityZones().set(2, constrainedAz);

    _awsAccountDao.save(sourceAwsAccount);

    // No valid source account name to copy from means nothing was generated
    {
      assertTrue(
          tool.generateAWSAccountSafe(
                  new AWSAccountCreationTool.ToolOptions.Builder()
                      .sourceName("fake-source-name")
                      .targetName("cloud-prod-2")
                      .arnRole("mockArnRole")
                      .build())
              .isEmpty());
    }

    // noop without commit should return but not persist the new account
    {
      final AWSAccount generatedAwsAccount =
          tool.generateAWSAccountSafe(
                  (new AWSAccountCreationTool.ToolOptions.Builder())
                      .sourceName(sourceAwsAccount.getName())
                      .targetName("cloud-prod-2")
                      .arnRole("mockArnRole")
                      .build())
              .get();

      assertEquals("cloud-prod-2", generatedAwsAccount.getName());
      assertEquals("mockArnRole", generatedAwsAccount.getAssumeRoleARN().get());
      assertEquals(2000L, generatedAwsAccount.getCapacityRemaining());
      assertFalse(generatedAwsAccount.getAssignmentEnabled());
      assertEquals(isForDataPlaneNodes, generatedAwsAccount.isForDataPlaneNodes());

      validateRegionsAndAvailabilityZones(generatedAwsAccount);

      // awsAccount is cleaned up if commit=false
      assertTrue(_awsAccountDao.findByName(generatedAwsAccount.getName()).isEmpty());
    }

    // commit should persist the new account
    {
      final AWSAccount generatedAwsAccount =
          tool.generateAWSAccountSafe(
                  (new AWSAccountCreationTool.ToolOptions.Builder())
                      .sourceName(sourceAwsAccount.getName())
                      .targetName("cloud-prod-2")
                      .arnRole("mockArnRole")
                      .commit(true)
                      .build())
              .get();

      assertEquals("cloud-prod-2", generatedAwsAccount.getName());
      assertEquals("mockArnRole", generatedAwsAccount.getAssumeRoleARN().get());
      assertEquals(2000L, generatedAwsAccount.getCapacityRemaining());
      assertFalse(generatedAwsAccount.getAssignmentEnabled());
      assertEquals(isForDataPlaneNodes, generatedAwsAccount.isForDataPlaneNodes());

      validateRegionsAndAvailabilityZones(generatedAwsAccount);

      // Confirm it is persisted
      assertTrue(_awsAccountDao.findByName(generatedAwsAccount.getName()).isPresent());
    }

    // commit should return empty if account name already exists
    {
      assertTrue(
          tool.generateAWSAccountSafe(
                  (new AWSAccountCreationTool.ToolOptions.Builder())
                      .sourceName(sourceAwsAccount.getName())
                      .targetName("cloud-prod-2")
                      .arnRole("mockArnRole")
                      .commit(true)
                      .build())
              .isEmpty());
    }
  }

  private void validateRegionsAndAvailabilityZones(final AWSAccount pAWSAccount) {
    assertTrue(pAWSAccount.getRegions().size() > 0);
    pAWSAccount
        .getRegions()
        .forEach(
            region -> {
              assertTrue(region.getGroupIds().isEmpty());

              switch (region.getName()) {
                case US_EAST_2:
                  // Directly matches what's in the source
                  assertEquals(region.getAvailabilityZones().size(), 3);
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(0), "us-east-2a", "use2-az1");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(1), "us-east-2b", "use2-az2");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(2), "us-east-2c", "use2-az3");
                  break;
                case AP_NORTHEAST_1:
                  // If availability zone order differs from the API it should still respect source
                  // account az order
                  assertEquals(region.getAvailabilityZones().size(), 3);
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(0), "ap-northeast-1a", "apne1-az2");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(1), "ap-northeast-1c", "apne1-az4");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(2),
                      "ap-northeast-1d",
                      "apne1-az1",
                      Status.CONSTRAINED);
                  break;
                case AP_NORTHEAST_2:
                  // Only 2 AZs
                  assertEquals(region.getAvailabilityZones().size(), 2);
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(0), "ap-northeast-2a", "apne2-az1");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(1), "ap-northeast-2c", "apne2-az2");
                  break;
                case EU_WEST_1:
                  // AZs that don't exist in new account should not be added, and new ones should be
                  // added in their place
                  assertEquals(region.getAvailabilityZones().size(), 3);
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(0), "eu-west-1a", "euw1-az1");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(1), "eu-west-1d", "euw1-az4");
                  assertAvailabilityZone(
                      region.getAvailabilityZones().get(2), "eu-west-1e", "euw1-az5");
                  break;
                default:
                  throw new IllegalStateException(
                      "Additional unaccounted regions found from the test models");
              }
            });
  }

  private void assertAvailabilityZone(
      final AWSAvailabilityZone pAvailabilityZone,
      final String pZoneName,
      final String pZoneId,
      final AWSAvailabilityZone.Status pStatus) {
    assertEquals(pAvailabilityZone.getName(), pZoneName);
    assertEquals(pAvailabilityZone.getZoneId().toString(), pZoneId);
    assertEquals(pAvailabilityZone.getStatus(), pStatus);
  }

  private void assertAvailabilityZone(
      final AWSAvailabilityZone pAvailabilityZone, final String pZoneName, final String pZoneId) {
    assertAvailabilityZone(pAvailabilityZone, pZoneName, pZoneId, Status.AVAILABLE);
  }

  private AWSApiSvc createMockAwsApiSvc() {
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);

    // These regions were pulled from
    // NDSModelTestFactor::getFullyAvailableAWSAccount4RegionsWithCreds
    doReturn(
            List.of(
                createAvailabilityZone(AWSRegionName.US_EAST_2, "us-east-2a", "use2-az1"),
                createAvailabilityZone(AWSRegionName.US_EAST_2, "us-east-2b", "use2-az2"),
                createAvailabilityZone(AWSRegionName.US_EAST_2, "us-east-2c", "use2-az3")))
        .when(awsApiSvc)
        .findAvailabilityZones(any(), eq(AWSRegionName.US_EAST_2), any());
    doReturn(
            List.of(
                createAvailabilityZone(
                    AWSRegionName.AP_NORTHEAST_1, "ap-northeast-1d", "apne1-az1"),
                createAvailabilityZone(
                    AWSRegionName.AP_NORTHEAST_1, "ap-northeast-1a", "apne1-az2"),
                createAvailabilityZone(
                    AWSRegionName.AP_NORTHEAST_1, "ap-northeast-1c", "apne1-az4")))
        .when(awsApiSvc)
        .findAvailabilityZones(any(), eq(AWSRegionName.AP_NORTHEAST_1), any());
    doReturn(
            List.of(
                createAvailabilityZone(
                    AWSRegionName.AP_NORTHEAST_2, "ap-northeast-2a", "apne2-az1"),
                createAvailabilityZone(
                    AWSRegionName.AP_NORTHEAST_2, "ap-northeast-2c", "apne2-az2")))
        .when(awsApiSvc)
        .findAvailabilityZones(any(), eq(AWSRegionName.AP_NORTHEAST_2), any());
    doReturn(
            List.of(
                createAvailabilityZone(AWSRegionName.EU_WEST_1, "eu-west-1a", "euw1-az1"),
                createAvailabilityZone(AWSRegionName.EU_WEST_1, "eu-west-1d", "euw1-az4"),
                createAvailabilityZone(AWSRegionName.EU_WEST_1, "eu-west-1e", "euw1-az5")))
        .when(awsApiSvc)
        .findAvailabilityZones(any(), eq(AWSRegionName.EU_WEST_1), any());

    return awsApiSvc;
  }

  private AvailabilityZone createAvailabilityZone(
      final AWSRegionName pRegionName, final String pZoneName, final String pZoneId) {
    final AvailabilityZone availabilityZone = new AvailabilityZone();

    availabilityZone.setRegionName(pRegionName.name());
    availabilityZone.setZoneName(pZoneName);
    availabilityZone.setZoneId(pZoneId);

    return availabilityZone;
  }
}
