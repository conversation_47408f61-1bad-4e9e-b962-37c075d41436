package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.amazonaws.services.ec2.model.CapacityReservation;
import com.amazonaws.services.ec2.model.Filter;
import com.amazonaws.services.ec2.model.ModifyCapacityReservationRequest;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSRegion;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.common._public.model.ComplianceLevel;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.misc.AWSCapacityReservationTool.AWSApiNames;
import com.xgen.svc.mms.misc.AWSCapacityReservationTool.Action;
import com.xgen.svc.mms.misc.AWSCapacityReservationTool.CapacityReservationWrapper;
import com.xgen.svc.mms.misc.AWSCapacityReservationTool.EndDateType;
import com.xgen.svc.mms.misc.AWSCapacityReservationTool.ToolOptions;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

public class AWSCapacityReservationToolIntTests extends BaseSvcTest {

  @Inject private AWSAccountDao _awsAccountDao;

  private AWSApiSvc _awsApiSvc;

  private ObjectId accountId1;
  private ObjectId accountId2;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _awsApiSvc = mock(AWSApiSvc.class);

    accountId1 = new ObjectId();
    accountId2 = new ObjectId();
    final List<AWSAccount> awsAccounts =
        List.of(
            AWSAccount.builder()
                .setId(accountId1)
                .setName("cloud-prod-1")
                .setRegions(
                    List.of(
                        AWSRegion.builder()
                            .name(AWSRegionName.US_EAST_1)
                            .availabilityZones(
                                List.of(
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_EAST_1, 0),
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_EAST_1, 1)))
                            .build(),
                        AWSRegion.builder()
                            .name(AWSRegionName.US_WEST_1)
                            .availabilityZones(
                                List.of(
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_WEST_1, 0),
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_WEST_1, 1)))
                            .build()))
                .build(),
            AWSAccount.builder()
                .setId(accountId2)
                .setName("cloud-prod-2")
                .setRegions(
                    List.of(
                        AWSRegion.builder()
                            .name(AWSRegionName.US_EAST_1)
                            .availabilityZones(
                                List.of(
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_EAST_1, 0),
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_EAST_1, 1)))
                            .build(),
                        AWSRegion.builder()
                            .name(AWSRegionName.US_WEST_1)
                            .availabilityZones(
                                List.of(
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_WEST_1, 0),
                                    NDSModelTestFactory.getMockAWSAvailabilityZone(
                                        AWSRegionName.US_WEST_1, 1)))
                            .build()))
                .build());
    awsAccounts.forEach(_awsAccountDao::save);
    doReturn(Collections.emptyList())
        .when(_awsApiSvc)
        .getCapacityReservations(any(), any(), any(), any());
  }

  @Test
  public void test_capacityReservation_filtersTest() throws ParseException {
    final AWSCapacityReservationTool tool =
        new AWSCapacityReservationTool(_awsAccountDao, _awsApiSvc);
    // states filter
    {
      reset(_awsApiSvc);
      tool.executeOnCapacityReservations(
          new ToolOptions.Builder()
              .complianceLevel(ComplianceLevel.COMMERCIAL)
              .action(Action.DESCRIBE)
              .states(List.of("pending", "active"))
              .build());
      validateGetCapacityReservation(
          List.of(new Filter(AWSApiNames.STATE, List.of("pending", "active"))));
    }

    // reservation type filter
    {
      reset(_awsApiSvc);
      tool.executeOnCapacityReservations(
          new ToolOptions.Builder()
              .reservationType("targeted")
              .complianceLevel(ComplianceLevel.COMMERCIAL)
              .action(Action.DESCRIBE)
              .build());
      validateGetCapacityReservation(
          List.of(new Filter(AWSApiNames.RESERVATION_TYPE, List.of("targeted"))));
    }

    // start date filter
    {
      reset(_awsApiSvc);
      tool.executeOnCapacityReservations(
          new ToolOptions.Builder()
              .complianceLevel(ComplianceLevel.COMMERCIAL)
              .startDateFilter("2024-04-16")
              .action(Action.DESCRIBE)
              .build());
      validateGetCapacityReservation(
          List.of(new Filter(AWSApiNames.START_DATE, List.of("2024-04-16"))));
    }

    // end date filter
    {
      reset(_awsApiSvc);
      tool.executeOnCapacityReservations(
          new ToolOptions.Builder()
              .complianceLevel(ComplianceLevel.COMMERCIAL)
              .endDateFilter("2024-04-17")
              .action(Action.DESCRIBE)
              .build());
      validateGetCapacityReservation(
          List.of(new Filter(AWSApiNames.END_DATE, List.of("2024-04-17"))));
    }
  }

  private void validateGetCapacityReservation(final List<Filter> pFilters) {
    verify(_awsApiSvc, times(1))
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_EAST_1), eq(pFilters), any());
    verify(_awsApiSvc, times(1))
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_WEST_1), eq(pFilters), any());
    verify(_awsApiSvc, times(1))
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_EAST_1), eq(pFilters), any());
    verify(_awsApiSvc, times(1))
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_WEST_1), eq(pFilters), any());
  }

  private CapacityReservation buildCapacityReservation(final String pReservationId) {
    final CapacityReservation capacityReservation = new CapacityReservation();
    capacityReservation.setCapacityReservationId(pReservationId);
    return capacityReservation;
  }

  @Test
  public void test_describeCapacityReservation() throws ParseException {
    doReturn(List.of(buildCapacityReservation("r1")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r2")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_WEST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r3")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r4")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_WEST_1), any(), any());

    final AWSCapacityReservationTool tool =
        spy(new AWSCapacityReservationTool(_awsAccountDao, _awsApiSvc));
    tool.executeOnCapacityReservations(
        new ToolOptions.Builder()
            .complianceLevel(ComplianceLevel.COMMERCIAL)
            .action(Action.DESCRIBE)
            .build());
    final ArgumentCaptor<List<CapacityReservationWrapper>> printCapacityCaptor =
        ArgumentCaptor.forClass(List.class);

    verify(tool).printCapacityReservations(printCapacityCaptor.capture());
    final List<CapacityReservationWrapper> argument = printCapacityCaptor.getValue();
    assertEquals(4, argument.size());
    assertEquals("r1", argument.get(0).capacityReservation.getCapacityReservationId());
    assertEquals("r2", argument.get(1).capacityReservation.getCapacityReservationId());
    assertEquals("r3", argument.get(2).capacityReservation.getCapacityReservationId());
    assertEquals("r4", argument.get(3).capacityReservation.getCapacityReservationId());

    verify(_awsApiSvc, never()).cancelCapacityReservation(any(), any(), any(), any());
    verify(_awsApiSvc, never()).modifyCapacityReservation(any(), any(), any(), any());
  }

  @Test
  public void test_acceptCapacityReservation() throws ParseException {
    doReturn(List.of(buildCapacityReservation("r1")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r2")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_WEST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r3")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r4")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_WEST_1), any(), any());

    final AWSCapacityReservationTool tool =
        new AWSCapacityReservationTool(_awsAccountDao, _awsApiSvc);
    tool.executeOnCapacityReservations(
        new ToolOptions.Builder()
            .complianceLevel(ComplianceLevel.COMMERCIAL)
            .action(Action.ACCEPT)
            .build());

    final ArgumentCaptor<ObjectId> accountIdCaptor = ArgumentCaptor.forClass(ObjectId.class);
    final ArgumentCaptor<AWSRegionName> regionCaptor = ArgumentCaptor.forClass(AWSRegionName.class);
    final ArgumentCaptor<ModifyCapacityReservationRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyCapacityReservationRequest.class);

    verify(_awsApiSvc, times(4))
        .modifyCapacityReservation(
            accountIdCaptor.capture(), regionCaptor.capture(), requestCaptor.capture(), any());
    assertEquals(4, requestCaptor.getAllValues().size());

    assertEquals(accountId1, accountIdCaptor.getAllValues().get(0));
    assertEquals(accountId1, accountIdCaptor.getAllValues().get(1));
    assertEquals(accountId2, accountIdCaptor.getAllValues().get(2));
    assertEquals(accountId2, accountIdCaptor.getAllValues().get(3));

    assertEquals(AWSRegionName.US_EAST_1, regionCaptor.getAllValues().get(0));
    assertEquals(AWSRegionName.US_WEST_1, regionCaptor.getAllValues().get(1));
    assertEquals(AWSRegionName.US_EAST_1, regionCaptor.getAllValues().get(2));
    assertEquals(AWSRegionName.US_WEST_1, regionCaptor.getAllValues().get(3));

    assertEquals("r1", requestCaptor.getAllValues().get(0).getCapacityReservationId());
    assertEquals("r2", requestCaptor.getAllValues().get(1).getCapacityReservationId());
    assertEquals("r3", requestCaptor.getAllValues().get(2).getCapacityReservationId());
    assertEquals("r4", requestCaptor.getAllValues().get(3).getCapacityReservationId());

    verify(_awsApiSvc, never()).cancelCapacityReservation(any(), any(), any(), any());
  }

  @Test
  public void test_acceptCapacityReservation_withEndDate() throws ParseException {
    doReturn(List.of(buildCapacityReservation("r1")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r2")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r3")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_WEST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r4")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_WEST_1), any(), any());

    final AWSCapacityReservationTool tool =
        new AWSCapacityReservationTool(_awsAccountDao, _awsApiSvc);

    // Invalid Date
    assertThrows(
        ParseException.class,
        () -> new ToolOptions.Builder().action(Action.ACCEPT).endDate("123/456").build());

    // Valid Date
    Date endDate = new GregorianCalendar(3000, Calendar.JANUARY, 1).getTime();
    tool.executeOnCapacityReservations(
        new ToolOptions.Builder()
            .complianceLevel(ComplianceLevel.COMMERCIAL)
            .action(Action.ACCEPT)
            .regions(List.of(AWSRegionName.US_EAST_1))
            .endDate(endDate.toString())
            .build());

    // Unlimited Reservation
    tool.executeOnCapacityReservations(
        new ToolOptions.Builder()
            .complianceLevel(ComplianceLevel.COMMERCIAL)
            .action(Action.ACCEPT)
            .regions(List.of(AWSRegionName.US_WEST_1))
            .endDate("unlimited")
            .build());

    final ArgumentCaptor<ObjectId> accountIdCaptor = ArgumentCaptor.forClass(ObjectId.class);
    final ArgumentCaptor<AWSRegionName> regionCaptor = ArgumentCaptor.forClass(AWSRegionName.class);
    final ArgumentCaptor<ModifyCapacityReservationRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyCapacityReservationRequest.class);

    verify(_awsApiSvc, times(4))
        .modifyCapacityReservation(
            accountIdCaptor.capture(), regionCaptor.capture(), requestCaptor.capture(), any());
    assertEquals(4, requestCaptor.getAllValues().size());

    // Reservations with an end date
    assertEquals(accountId1, accountIdCaptor.getAllValues().get(0));
    assertEquals(accountId2, accountIdCaptor.getAllValues().get(1));

    assertEquals(AWSRegionName.US_EAST_1, regionCaptor.getAllValues().get(0));
    assertEquals(AWSRegionName.US_EAST_1, regionCaptor.getAllValues().get(1));

    assertEquals("r1", requestCaptor.getAllValues().get(0).getCapacityReservationId());
    assertEquals("r2", requestCaptor.getAllValues().get(1).getCapacityReservationId());

    assertEquals(endDate, requestCaptor.getAllValues().get(0).getEndDate());
    assertEquals(endDate, requestCaptor.getAllValues().get(1).getEndDate());

    assertEquals(
        EndDateType.LIMITED_RESERVATION, requestCaptor.getAllValues().get(0).getEndDateType());
    assertEquals(
        EndDateType.LIMITED_RESERVATION, requestCaptor.getAllValues().get(1).getEndDateType());

    // Reservations with no end date
    assertEquals(accountId1, accountIdCaptor.getAllValues().get(2));
    assertEquals(accountId2, accountIdCaptor.getAllValues().get(3));

    assertEquals(AWSRegionName.US_WEST_1, regionCaptor.getAllValues().get(2));
    assertEquals(AWSRegionName.US_WEST_1, regionCaptor.getAllValues().get(3));

    assertEquals("r3", requestCaptor.getAllValues().get(2).getCapacityReservationId());
    assertEquals("r4", requestCaptor.getAllValues().get(3).getCapacityReservationId());

    assertEquals(
        EndDateType.UNLIMITED_RESERVATION, requestCaptor.getAllValues().get(2).getEndDateType());
    assertEquals(
        EndDateType.UNLIMITED_RESERVATION, requestCaptor.getAllValues().get(3).getEndDateType());

    verify(_awsApiSvc, never()).cancelCapacityReservation(any(), any(), any(), any());
  }

  @Test
  public void test_cancelCapacityReservation() throws ParseException {
    doReturn(List.of(buildCapacityReservation("r1")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r2")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId1), eq(AWSRegionName.US_WEST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r3")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_EAST_1), any(), any());
    doReturn(List.of(buildCapacityReservation("r4")))
        .when(_awsApiSvc)
        .getCapacityReservations(eq(accountId2), eq(AWSRegionName.US_WEST_1), any(), any());

    final AWSCapacityReservationTool tool =
        new AWSCapacityReservationTool(_awsAccountDao, _awsApiSvc);
    tool.executeOnCapacityReservations(
        new ToolOptions.Builder()
            .complianceLevel(ComplianceLevel.COMMERCIAL)
            .action(Action.CANCEL)
            .build());

    final ArgumentCaptor<ObjectId> accountIdCaptor = ArgumentCaptor.forClass(ObjectId.class);
    final ArgumentCaptor<AWSRegionName> regionCaptor = ArgumentCaptor.forClass(AWSRegionName.class);
    final ArgumentCaptor<String> reservationIdCaptor = ArgumentCaptor.forClass(String.class);

    verify(_awsApiSvc, times(4))
        .cancelCapacityReservation(
            accountIdCaptor.capture(),
            regionCaptor.capture(),
            reservationIdCaptor.capture(),
            any());
    assertEquals(4, reservationIdCaptor.getAllValues().size());

    assertEquals(accountId1, accountIdCaptor.getAllValues().get(0));
    assertEquals(accountId1, accountIdCaptor.getAllValues().get(1));
    assertEquals(accountId2, accountIdCaptor.getAllValues().get(2));
    assertEquals(accountId2, accountIdCaptor.getAllValues().get(3));

    assertEquals(AWSRegionName.US_EAST_1, regionCaptor.getAllValues().get(0));
    assertEquals(AWSRegionName.US_WEST_1, regionCaptor.getAllValues().get(1));
    assertEquals(AWSRegionName.US_EAST_1, regionCaptor.getAllValues().get(2));
    assertEquals(AWSRegionName.US_WEST_1, regionCaptor.getAllValues().get(3));

    assertEquals("r1", reservationIdCaptor.getAllValues().get(0));
    assertEquals("r2", reservationIdCaptor.getAllValues().get(1));
    assertEquals("r3", reservationIdCaptor.getAllValues().get(2));
    assertEquals("r4", reservationIdCaptor.getAllValues().get(3));

    verify(_awsApiSvc, never()).modifyCapacityReservation(any(), any(), any(), any());
  }
}
