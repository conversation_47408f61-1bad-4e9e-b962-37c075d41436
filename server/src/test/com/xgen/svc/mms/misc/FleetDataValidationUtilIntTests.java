package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.util._public.util.classpath.ClasspathUtils;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.datavalidation._public.model.DataValidationArguments;
import com.xgen.cloud.nds.datavalidation._public.model.DataValidationRecord;
import com.xgen.cloud.nds.datavalidation._public.model.DataValidationRun;
import com.xgen.cloud.nds.datavalidation._public.svc.DataValidationResultsSvc;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class FleetDataValidationUtilIntTests extends BaseSvcTest {
  @Inject private ReplicaSetHardwareDao _hardwareDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private DataValidationResultsSvc _dataValidationResultsSvc;

  @Inject private FleetDataValidationUtil _util;

  private ObjectId groupId;
  private String clusterName;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    // Hardcoded to match fleet_data_validation.csv file
    groupId = new ObjectId("61b27428bd56a193664dd743");
    clusterName = "cluster0";
    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName)
                .append(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION, "4.0"));
    _clusterDescriptionDao.save(cluster);

    // Hardcoded pin to match fleet_data_validation.csv
    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), cluster, "leayw");

    _hardwareDao.saveReplicaSafe(hardwareDoc);
  }

  @Test
  public void testMain_WithPercentage() {
    final String[] args = {"--majorVersion", "4.0", "--percentage", "100"};
    _util.run(args);

    final ReplicaSetHardware hardware =
        new ReplicaSetHardware((BasicDBObject) _hardwareDao.findAllAsList().get(0));

    hardware
        .getHardware()
        .forEach(
            i ->
                assertTrue(
                    _dataValidationResultsSvc
                        .findByTargetInstanceId(i.getInstanceId())
                        .isPresent()));
  }

  @Test
  public void testMain_WithCustomDate() {
    final SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
    final Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DAY_OF_YEAR, 1);
    final String[] args = {
      "--majorVersion",
      "4.0",
      "--percentage",
      "100",
      "--createdBefore",
      format.format(cal.getTime())
    };
    _util.run(args);

    final ReplicaSetHardware hardware =
        new ReplicaSetHardware((BasicDBObject) _hardwareDao.findAllAsList().get(0));

    hardware
        .getHardware()
        .forEach(
            i ->
                assertTrue(
                    _dataValidationResultsSvc
                        .findByTargetInstanceId(i.getInstanceId())
                        .isPresent()));
  }

  @Test
  public void testMain_WithCustomProvider() {
    final String[] args = {
      "--majorVersion", "4.0", "--percentage", "100", "--cloudProvider", "AZURE"
    };
    _util.run(args);

    assertTrue(_dataValidationResultsSvc.findAll().isEmpty());
  }

  @Test
  public void testMain_WithNumCluster() {
    final String[] args = {"--majorVersion", "4.0", "--numCluster", "1"};
    _util.run(args);

    final ReplicaSetHardware hardware =
        new ReplicaSetHardware((BasicDBObject) _hardwareDao.findAllAsList().get(0));

    hardware
        .getHardware()
        .forEach(
            i ->
                assertTrue(
                    _dataValidationResultsSvc
                        .findByTargetInstanceId(i.getInstanceId())
                        .isPresent()));
  }

  @Test
  public void testMain_WithNumInstances() {
    final String[] args = {"--majorVersion", "4.0", "--percentage", "100", "--numInstances", "1"};
    _util.run(args);

    assertEquals(1, _dataValidationResultsSvc.findAll().size());
  }

  @Test
  public void testRun_DateOutOfBound() {
    _util.runWithPercentage(
        "4.0",
        100,
        new Date(0L),
        FleetDataValidationUtil.DEFAULT_PROVIDERS,
        Optional.empty(),
        DataValidationArguments.noArgs());
    assertTrue(_dataValidationResultsSvc.findAll().isEmpty());
  }

  @Test
  public void testRun_NonMatchingVersion() {
    _util.runWithPercentage(
        "3.6",
        100,
        new Date(),
        FleetDataValidationUtil.DEFAULT_PROVIDERS,
        Optional.empty(),
        DataValidationArguments.noArgs());
    assertTrue(_dataValidationResultsSvc.findAll().isEmpty());
  }

  @Test
  public void testRun_Subset() {
    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(groupId, "cluster1")
                .append(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION, "4.0"));
    _clusterDescriptionDao.save(cluster);

    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, new ObjectId(), cluster);

    _hardwareDao.saveReplicaSafe(hardwareDoc);
    _util.runWithPercentage(
        "4.0",
        50,
        DateUtils.addMinutes(new Date(), 1),
        FleetDataValidationUtil.DEFAULT_PROVIDERS,
        Optional.empty(),
        DataValidationArguments.noArgs());

    final ReplicaSetHardware cluster0Hardware =
        _hardwareDao.findByCluster(groupId, "cluster0").get(0);
    final ReplicaSetHardware cluster1Hardware =
        _hardwareDao.findByCluster(groupId, "cluster1").get(0);

    // Records are only created for cluster0 and not cluster1
    cluster0Hardware
        .getHardware()
        .forEach(
            i ->
                assertTrue(
                    _dataValidationResultsSvc
                        .findByTargetInstanceId(i.getInstanceId())
                        .isPresent()));
    cluster1Hardware
        .getHardware()
        .forEach(
            i ->
                assertTrue(
                    _dataValidationResultsSvc.findByTargetInstanceId(i.getInstanceId()).isEmpty()));
  }

  @Test
  public void testRun_RetainHistoricalRecords() {
    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(groupId, "cluster1")
                .append(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION, "4.0"));
    _clusterDescriptionDao.save(cluster);

    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, new ObjectId(), cluster);

    _hardwareDao.saveReplicaSafe(hardwareDoc);
    _util.runWithPercentage(
        "4.0",
        100,
        DateUtils.addMinutes(new Date(), 1),
        FleetDataValidationUtil.DEFAULT_PROVIDERS,
        Optional.empty(),
        DataValidationArguments.noArgs());

    final ReplicaSetHardware cluster0Hardware =
        _hardwareDao.findByCluster(groupId, "cluster0").get(0);
    final ReplicaSetHardware cluster1Hardware =
        _hardwareDao.findByCluster(groupId, "cluster1").get(0);

    // Records are created for both clusters
    final List<InstanceHardware> instances =
        Stream.concat(
                cluster0Hardware.getHardware().stream(), cluster1Hardware.getHardware().stream())
            .collect(Collectors.toList());

    final List<DataValidationRecord> records = new ArrayList<>();
    instances.forEach(
        i -> {
          final Optional<DataValidationRecord> record =
              _dataValidationResultsSvc.findByTargetInstanceId(i.getInstanceId());
          assertTrue(record.isPresent());
          records.add(record.get());
        });

    records.forEach(
        r -> {
          _dataValidationResultsSvc.setRecordStatus(r.getId(), DataValidationRun.Status.WORKING);
        });

    // Run utility again - since there are existing WORKING records - should not create any more
    // records
    _util.runWithPercentage(
        "4.0",
        100,
        DateUtils.addMinutes(new Date(), 1),
        FleetDataValidationUtil.DEFAULT_PROVIDERS,
        Optional.empty(),
        DataValidationArguments.noArgs());
    assertEquals(instances.size(), _dataValidationResultsSvc.findAll().size());

    records.forEach(
        r -> {
          _dataValidationResultsSvc.setRecordStatus(r.getId(), DataValidationRun.Status.COMPLETED);
        });

    // Run utility again - historical records are retained since existing records are COMPLETED
    _util.runWithPercentage(
        "4.0",
        100,
        DateUtils.addMinutes(new Date(), 1),
        FleetDataValidationUtil.DEFAULT_PROVIDERS,
        Optional.empty(),
        DataValidationArguments.noArgs());
    assertEquals(instances.size() * 2, _dataValidationResultsSvc.findAll().size());
  }

  @Test
  public void testMain_WithCsvFile() throws IOException {
    final InputStream inputStream = ClasspathUtils.openResourceUrl("csv/fleet_data_validation.csv");
    final String outputFilePath = "/tmp/fleet_data_validation.csv";
    final File targetFile = new File(outputFilePath);
    FileUtils.copyInputStreamToFile(inputStream, targetFile);
    final String[] args = {"--majorVersion", "4.0", "--csvFile", outputFilePath, "-d"};
    _util.run(args);

    final ReplicaSetHardware hardware =
        new ReplicaSetHardware((BasicDBObject) _hardwareDao.findAllAsList().get(0));

    hardware
        .getHardware()
        .forEach(
            i ->
                assertTrue(
                    _dataValidationResultsSvc
                        .findByTargetInstanceId(i.getInstanceId())
                        .isPresent()));
  }

  @Test
  public void testMain_WithCsvFile_testFilters() throws IOException {
    final InputStream inputStream = ClasspathUtils.openResourceUrl("csv/fleet_data_validation.csv");
    final String outputFilePath = "/tmp/fleet_data_validation.csv";
    final File targetFile = new File(outputFilePath);
    FileUtils.copyInputStreamToFile(inputStream, targetFile);

    final String[] majorVersionArgs = {"--majorVersion", "3.6", "--csvFile", outputFilePath, "-d"};
    _util.run(majorVersionArgs);
    assertEquals(_dataValidationResultsSvc.countAll(), 0);

    final String[] createDateArgs = {
      "--createdBefore", "01/01/2000", "--majorVersion", "4.0", "--csvFile", outputFilePath, "-d"
    };
    _util.run(createDateArgs);
    assertEquals(_dataValidationResultsSvc.countAll(), 0);

    final String[] providerArgs = {
      "--cloudProvider", "AZURE", "--majorVersion", "4.0", "--csvFile", outputFilePath, "-d"
    };
    _util.run(providerArgs);
    assertEquals(_dataValidationResultsSvc.countAll(), 0);
  }
}
