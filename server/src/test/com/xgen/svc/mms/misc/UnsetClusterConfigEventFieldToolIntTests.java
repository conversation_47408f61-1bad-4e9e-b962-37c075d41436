package com.xgen.svc.mms.misc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.common.util.concurrent.RateLimiter;
import com.mongodb.BasicDBObject;
import com.mongodb.Cursor;
import com.mongodb.DBObject;
import com.xgen.cloud.activity._private.dao.EventDao;
import com.xgen.cloud.atm.activity._public.audit.AutomationConfigAudit;
import com.xgen.cloud.atm.activity._public.event.AutomationConfigEvent;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class UnsetClusterConfigEventFieldToolIntTests extends JUnit5BaseSvcTest {
  private UnsetClusterConfigEventFieldTool tool;

  @Inject private MongoSvc mongoSvc;
  @Inject private AppSettings appSettings;
  private EventDao eventDao;

  private RateLimiter rateLimiter = Mockito.mock(RateLimiter.class);

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    doReturn(100.0).when(rateLimiter).acquire(anyInt());
    doReturn(100.0).when(rateLimiter).getRate();
    tool = new UnsetClusterConfigEventFieldTool(mongoSvc, appSettings, "mmsdbevents");
    eventDao = tool.eventDao;
  }

  @Test
  public void testMigrationUnsetsEvents() throws Exception {
    LocalDate now = LocalDate.now();
    createAutomationConfigEvents(5, now);
    createAutomationConfigEvents(5, now.minusDays(399));
    LocalDate fourHundredDaysAgo = now.minusDays(400);
    createAutomationConfigEvents(5, fourHundredDaysAgo);

    assertEquals(15, eventDao.findAll().size());
    long unsetEvents =
        tool.unsetClusterConfig(now.minusDays(399), null, rateLimiter, 100, 100, false, false);

    assertEquals(10, unsetEvents);
    assertEquals(15, eventDao.findAll().size());

    List<DBObject> events = eventDao.findAll().toArray();
    Date unsetTargetDate =
        Date.from(now.minusDays(399).atStartOfDay(ZoneId.systemDefault()).toInstant());

    // Cant use eventDao accessors here because we pre-parse clusterConfig out of the event models
    // :(
    for (DBObject object : events) {
      Date created = (Date) object.get(AutomationConfigAudit.CREATED_AT_FIELD);
      Object clusterConfig = object.get(AutomationConfigAudit.CLUSTER_CONFIG_FIELD);
      if (created.after(unsetTargetDate)) {
        assertNotNull(clusterConfig);
      } else {
        assertNull(clusterConfig);
      }
    }
    verify(rateLimiter, times(1)).acquire(anyInt());
    verify(rateLimiter, times(1)).getRate();
  }

  @Test
  public void testMigrationSkipsStartId() throws Exception {
    LocalDate now = LocalDate.now();
    LocalDate fourHundredDaysAgo = now.minusDays(400);
    createAutomationConfigEvents(1, fourHundredDaysAgo.minusDays(1));
    createAutomationConfigEvents(4, fourHundredDaysAgo);
    createAutomationConfigEvents(1, LocalDate.now());

    List<DBObject> events = eventDao.findAll().stream().toList();
    Date startDate = (Date) events.get(1).get("cre");

    long dryRunUnsetEvents =
        tool.unsetClusterConfig(now.minusDays(399), startDate, rateLimiter, 100, 100, true, false);
    assertEquals(dryRunUnsetEvents, 4);

    long unsetEvents =
        tool.unsetClusterConfig(now.minusDays(399), startDate, rateLimiter, 100, 100, false, false);

    assertEquals(unsetEvents, 4);

    Cursor cursor = eventDao.findAll();
    DBObject firstEvent = cursor.next();
    assertNotNull(firstEvent.get(AutomationConfigAudit.CLUSTER_CONFIG_FIELD));

    for (int i = 0; i < 4; i++) {
      assertNull(cursor.next().get(AutomationConfigAudit.CLUSTER_CONFIG_FIELD));
    }

    verify(rateLimiter, times(1)).acquire(anyInt());
    verify(rateLimiter, times(2)).getRate();
  }

  @Test
  public void testMigrationFailsWhenDateIsTooEarly() throws Exception {
    LocalDate now = LocalDate.now();
    createAutomationConfigEvents(5, now);
    LocalDate fourHundredDaysAgo = now.minusDays(400);
    createAutomationConfigEvents(5, fourHundredDaysAgo);

    try {
      tool.unsetClusterConfig(now.minusDays(300), null, rateLimiter, 100, 100, false, false);
      fail("Should throw");
    } catch (RuntimeException e) {
    }

    List<DBObject> events = eventDao.findAll().toArray();
    for (DBObject object : events) {
      Object clusterConfig = object.get(AutomationConfigAudit.CLUSTER_CONFIG_FIELD);
      assertNotNull(clusterConfig);
    }
    verify(rateLimiter, times(1)).getRate();
  }

  @Test
  public void testForceAllowsEarlyDates() throws Exception {
    LocalDate now = LocalDate.now();
    LocalDate oneDayInTheFuture = now.plusDays(1);
    createAutomationConfigEvents(5, now);

    long unsetEventCount =
        tool.unsetClusterConfig(oneDayInTheFuture, null, rateLimiter, 100, 100, false, true);
    assertEquals(5, unsetEventCount);

    List<DBObject> events = eventDao.findAll().toArray();
    for (DBObject object : events) {
      Object clusterConfig = object.get(AutomationConfigAudit.CLUSTER_CONFIG_FIELD);
      assertNull(clusterConfig);
    }
  }

  @Test
  public void testDryRunSkipsMigration() throws Exception {
    LocalDate fourHundredDaysAgo = LocalDate.now().minusDays(400);
    createAutomationConfigEvents(5, fourHundredDaysAgo);

    long unsetEventCount =
        tool.unsetClusterConfig(
            LocalDate.now().minusDays(389), null, rateLimiter, 100, 100, true, false);
    assertEquals(5, unsetEventCount);

    List<DBObject> events = eventDao.findAll().toArray();
    for (DBObject object : events) {
      Object clusterConfig = object.get(AutomationConfigAudit.CLUSTER_CONFIG_FIELD);
      assertNotNull(clusterConfig);
    }
    verify(rateLimiter, times(1)).getRate();
  }

  private void createAutomationConfigEvents(int count, LocalDate localDate) {
    Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    for (int i = 0; i < count; i++) {
      AutomationConfigAudit.Builder builder =
          new AutomationConfigAudit.Builder(
              AutomationConfigEvent.Type.AUTOMATION_CONFIG_PUBLISHED_AUDIT);
      builder.createdAt(date);
      builder.clusterConfig(new AutomationConfig());
      ObjectId eventId = eventDao.save(builder.build());

      // Have to do a little workaround since we no longer write clusterConfig to automation audits
      // in the
      // mms db.
      eventDao.updateOne(
          new BasicDBObject("_id", eventId),
          new BasicDBObject("$set", new BasicDBObject("clusterConfig", "abc123")));
    }
  }
}
