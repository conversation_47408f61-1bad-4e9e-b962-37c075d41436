package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.google.api.services.compute.model.ServiceAttachment;
import com.google.api.services.compute.model.ServiceAttachmentConnectedEndpoint;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectRegionGroupDao;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectionDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPPrivateServiceConnection;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPConsumerForwardingRule;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectEndpointGroup;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectEndpointGroup.Status;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectRegionGroup;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.misc.GCPUpdateIncorrectPSCServiceAttachmentForwardingRulePairingMetadataTool.GCPEndpointGroupInfo;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class GCPUpdateIncorrectPSCServiceAttachmentForwardingRulePairingMetadataToolIntTests
    extends BaseSvcTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private GCPPrivateServiceConnectionDao _gcpPrivateServiceConnectionDao;
  @Inject private GCPPrivateServiceConnectRegionGroupDao _regionGroupDao;

  @Mock private GCPApiSvc _gcpApiSvc;
  private GCPUpdateIncorrectPSCServiceAttachmentForwardingRulePairingMetadataTool _tool;
  private Map<String, ObjectId> _ndsGroupMap;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    MockitoAnnotations.initMocks(this);
    _ndsGroupMap = new HashMap<>();
    seedGroupsForTest();
    _tool =
        new GCPUpdateIncorrectPSCServiceAttachmentForwardingRulePairingMetadataTool(
            _ndsGroupDao, _gcpPrivateServiceConnectionDao, _gcpApiSvc, _regionGroupDao);
  }

  @Test
  public void testFindEndpointGroupsToUpdate() {
    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate = _tool.findEndpointGroupsToUpdate();
    assertEquals(6, endpointGroupsToUpdate.size());
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getEndpointGroupName().equals("group1_rg1_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getEndpointGroupName().equals("group1_rg2_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getEndpointGroupName().equals("group2_rg1_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getEndpointGroupName().equals("group4_rg1_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getEndpointGroupName().equals("group5_rg1_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getEndpointGroupName().equals("group5_rg1_eg2_broken")));
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_twoRegionGroups_bothBroken() {
    final ObjectId projectId = _ndsGroupMap.get("group1");

    assertFalse(areForwardingRulesCorrectForProject(projectId));

    final List<GCPPrivateServiceConnection> privateServiceConnectionsBefore =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    final Set<ObjectId> distinctForwardingRuleIdsBefore =
        privateServiceConnectionsBefore.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertTrue(privateServiceConnectionsBefore.size() > distinctForwardingRuleIdsBefore.size());

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);

    assertEquals(2, endpointGroupsToUpdate.size());
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getProjectId().equals(projectId)
                        && pEndpointGroupInfo
                            .getEndpointGroupName()
                            .equals("group1_rg1_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                pEndpointGroupInfo ->
                    pEndpointGroupInfo.getProjectId().equals(projectId)
                        && pEndpointGroupInfo
                            .getEndpointGroupName()
                            .equals("group1_rg2_eg1_broken")));

    endpointGroupsToUpdate.forEach(
        endpointGroupInfo -> _tool.updatePairingMetadata(endpointGroupInfo));

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdateAfterRePair =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdateAfterRePair.size());

    assertTrue(areForwardingRulesCorrectForProject(projectId));

    final List<GCPPrivateServiceConnection> privateServiceConnectionsAfter =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    assertTrue(
        privateServiceConnectionsAfter.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().size() == 1));
    final Set<ObjectId> distinctForwardingRuleIdsAfter =
        privateServiceConnectionsAfter.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertEquals(privateServiceConnectionsAfter.size(), distinctForwardingRuleIdsAfter.size());
    final NDSGroup groupAfter = _ndsGroupDao.find(projectId).get();
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_twoRegionGroups_oneBroken() {
    final ObjectId projectId = _ndsGroupMap.get("group2");

    assertFalse(areForwardingRulesCorrectForProject(projectId));

    final List<GCPPrivateServiceConnection> privateServiceConnectionsBefore =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    final Set<ObjectId> distinctForwardingRuleIdsBefore =
        privateServiceConnectionsBefore.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertTrue(privateServiceConnectionsBefore.size() > distinctForwardingRuleIdsBefore.size());

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(1, endpointGroupsToUpdate.size());
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                endpointGroupInfo ->
                    endpointGroupInfo.getProjectId().equals(projectId)
                        && endpointGroupInfo
                            .getEndpointGroupName()
                            .equals("group2_rg1_eg1_broken")));

    endpointGroupsToUpdate.forEach(
        endpointGroupInfo -> _tool.updatePairingMetadata(endpointGroupInfo));

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdateAfterRePair =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdateAfterRePair.size());

    assertTrue(areForwardingRulesCorrectForProject(projectId));

    final List<GCPPrivateServiceConnection> privateServiceConnectionsAfter =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    assertTrue(
        privateServiceConnectionsAfter.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().size() == 1));
    final Set<ObjectId> distinctForwardingRuleIdsAfter =
        privateServiceConnectionsAfter.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertEquals(privateServiceConnectionsAfter.size(), distinctForwardingRuleIdsAfter.size());
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_twoEndpointGroupsOneRegion_bothBroken() {
    final ObjectId projectId = _ndsGroupMap.get("group5");

    assertFalse(areForwardingRulesCorrectForProject(projectId));

    final List<GCPPrivateServiceConnection> privateServiceConnectionsBefore =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    final Set<ObjectId> distinctForwardingRuleIdsBefore =
        privateServiceConnectionsBefore.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertTrue(privateServiceConnectionsBefore.size() > distinctForwardingRuleIdsBefore.size());

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(2, endpointGroupsToUpdate.size());
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                endpointGroupInfo ->
                    endpointGroupInfo.getProjectId().equals(projectId)
                        && endpointGroupInfo
                            .getEndpointGroupName()
                            .equals("group5_rg1_eg1_broken")));
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                endpointGroupInfo ->
                    endpointGroupInfo.getProjectId().equals(projectId)
                        && endpointGroupInfo
                            .getEndpointGroupName()
                            .equals("group5_rg1_eg2_broken")));

    endpointGroupsToUpdate.forEach(
        endpointGroupInfo -> _tool.updatePairingMetadata(endpointGroupInfo));

    assertTrue(areForwardingRulesCorrectForProject(projectId));

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdateAfterRePair =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdateAfterRePair.size());
    final List<GCPPrivateServiceConnection> privateServiceConnectionsAfter =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    assertTrue(
        privateServiceConnectionsAfter.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().size() == 2));
    final Set<ObjectId> distinctForwardingRuleIdsAfter =
        privateServiceConnectionsAfter.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertEquals(privateServiceConnectionsAfter.size() * 2, distinctForwardingRuleIdsAfter.size());
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_twoEndpointGroupsOneRegion_bothOk() {
    final ObjectId projectId = _ndsGroupMap.get("group3");

    assertTrue(areForwardingRulesCorrectForProject(projectId));

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdate.size());
    final List<GCPPrivateServiceConnection> privateServiceConnections =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    assertTrue(
        privateServiceConnections.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().size() == 2));
    final Set<ObjectId> distinctForwardingRuleIds =
        privateServiceConnections.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertEquals(privateServiceConnections.size() * 2, distinctForwardingRuleIds.size());
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_twoRegionGroups_oneNotSetUpOtherBroken() {
    final ObjectId projectId = _ndsGroupMap.get("group4");
    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);

    assertFalse(areForwardingRulesCorrectForProject(projectId));

    assertEquals(1, endpointGroupsToUpdate.size());
    assertTrue(
        endpointGroupsToUpdate.stream()
            .anyMatch(
                endpointGroupInfo ->
                    endpointGroupInfo.getProjectId().equals(projectId)
                        && endpointGroupInfo
                            .getEndpointGroupName()
                            .equals("group4_rg1_eg1_broken")));

    endpointGroupsToUpdate.forEach(
        endpointGroupInfo -> _tool.updatePairingMetadata(endpointGroupInfo));

    final List<GCPEndpointGroupInfo> endpointGroupsToUpdateAfterRePair =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdateAfterRePair.size());
    final List<GCPPrivateServiceConnection> privateServiceConnections =
        _gcpPrivateServiceConnectionDao
            .findAllPrivateServiceConnectionsByProjectId(projectId)
            .stream()
            .filter(psc -> psc.getRegionName().equals(GCPRegionName.NORTH_AMERICA_NORTHEAST_1))
            .collect(Collectors.toList());
    assertTrue(
        privateServiceConnections.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().size() == 1));
    final List<GCPPrivateServiceConnection> privateServiceConnectionsEastAsia =
        _gcpPrivateServiceConnectionDao
            .findAllPrivateServiceConnectionsByProjectId(projectId)
            .stream()
            .filter(psc -> psc.getRegionName().equals(GCPRegionName.ASIA_EAST_2))
            .collect(Collectors.toList());
    assertTrue(
        privateServiceConnectionsEastAsia.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().isEmpty()));

    assertTrue(areForwardingRulesCorrectForProject(projectId));

    final Set<ObjectId> distinctForwardingRuleIdsAfter =
        privateServiceConnections.stream()
            .flatMap(
                pGCPPrivateServiceConnection ->
                    pGCPPrivateServiceConnection.getConsumerForwardingRuleIds().stream())
            .collect(Collectors.toSet());

    assertEquals(privateServiceConnections.size(), distinctForwardingRuleIdsAfter.size());
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_noRegionGroups() {
    final ObjectId projectId = _ndsGroupMap.get("groupWithNoRegionGroups");
    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdate.size());
  }

  @Test
  public void testFindEndpointGroupsToUpdateForNDSGroup_groupWithNoEndpointGroups() {
    final ObjectId projectId = _ndsGroupMap.get("groupWithNoEndpointGroups");
    final List<GCPEndpointGroupInfo> endpointGroupsToUpdate =
        _tool.findEndpointGroupsToUpdateForNDSGroup(projectId);
    assertEquals(0, endpointGroupsToUpdate.size());
    final List<GCPPrivateServiceConnection> privateServiceConnections =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(projectId);
    assertTrue(
        privateServiceConnections.stream()
            .allMatch(
                pPrivateServiceConnection ->
                    pPrivateServiceConnection.getConsumerForwardingRuleIds().isEmpty()));
  }

  private void seedGroupsForTest() {
    final NDSGroup group1 =
        seedNDSGroup(
            "group1",
            Map.of(
                GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
                List.of("group1_rg1_eg1_broken"),
                GCPRegionName.ASIA_EAST_2,
                List.of("group1_rg2_eg1_broken")),
            Map.of("group1_rg1_eg1_broken", true, "group1_rg2_eg1_broken", true));
    final NDSGroup group2 =
        seedNDSGroup(
            "group2",
            Map.of(
                GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
                List.of("group2_rg1_eg1_broken"),
                GCPRegionName.ASIA_EAST_2,
                List.of("group2_rg2_eg1_ok")),
            Map.of("group2_rg1_eg1_broken", true, "group2_rg2_eg1_ok", false));
    final NDSGroup group3 =
        seedNDSGroup(
            "group3",
            Map.of(
                GCPRegionName.ASIA_NORTHEAST_2, List.of("group3_rg1_eg1_ok", "group3_rg1_eg2_ok")),
            Map.of("group3_rg1_eg1_ok", false, "group3_rg1_eg2_ok", false));
    final NDSGroup group4 =
        seedNDSGroup(
            "group4",
            Map.of(
                GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
                List.of("group4_rg1_eg1_broken"),
                GCPRegionName.ASIA_EAST_2,
                List.of()),
            Map.of("group4_rg1_eg1_broken", true));
    final NDSGroup group5 =
        seedNDSGroup(
            "group5",
            Map.of(
                GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
                List.of("group5_rg1_eg1_broken", "group5_rg1_eg2_broken")),
            Map.of("group5_rg1_eg1_broken", true, "group5_rg1_eg2_broken", true));
    final NDSGroup groupWithNoRegionGroups =
        seedNDSGroup("groupWithNoRegionGroups", Map.of(), Map.of());
    final NDSGroup groupWithNoEndpointGroups =
        seedNDSGroup(
            "groupWithNoEndpointGroups", Map.of(GCPRegionName.EUROPE_NORTH_1, List.of()), Map.of());

    _ndsGroupMap.put("group1", group1.getGroupId());
    _ndsGroupMap.put("group2", group2.getGroupId());
    _ndsGroupMap.put("group3", group3.getGroupId());
    _ndsGroupMap.put("group4", group4.getGroupId());
    _ndsGroupMap.put("group5", group5.getGroupId());
    _ndsGroupMap.put("groupWithNoRegionGroups", groupWithNoRegionGroups.getGroupId());
    _ndsGroupMap.put("groupWithNoEndpointGroups", groupWithNoEndpointGroups.getGroupId());
  }

  private NDSGroup seedNDSGroup(
      final String pGroupName,
      final Map<GCPRegionName, List<String>> regionGroupToEndpointGroupsMap,
      final Map<String, Boolean> endpointGroupNameToNeedsFixingMap) {
    final Organization org =
        MmsFactory.createOrganizationWithNDSPlan(String.format("org-%s", pGroupName));
    final Group group = MmsFactory.createGroup(org, pGroupName);

    final ObjectId projectId = group.getId();
    final String atlasGCPProjectId = "p-aksdjfkasdf";

    _ndsGroupSvc.create(projectId, new NDSManagedX509(), false);
    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(
            projectId, new GCPCloudProviderContainer(NDSModelTestFactory.getGCPContainer()));

    final int numPSCs = 25;

    for (Entry<GCPRegionName, List<String>> entry : regionGroupToEndpointGroupsMap.entrySet()) {
      GCPRegionName regionName = entry.getKey();
      List<String> endpointGroupNames = entry.getValue();
      final List<GCPPrivateServiceConnectEndpointGroup> endpointGroups = new ArrayList<>();

      for (int i = 0; i < endpointGroupNames.size(); i++) {
        final String endpointGroupName = endpointGroupNames.get(i);
        final List<GCPConsumerForwardingRule> forwardingRules =
            NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
                null, numPSCs, endpointGroupName);
        final GCPPrivateServiceConnectEndpointGroup endpointGroup =
            new GCPPrivateServiceConnectEndpointGroup(
                new ObjectId(),
                endpointGroupName,
                "gcpProjectName",
                false,
                null,
                Status.AVAILABLE,
                forwardingRules,
                i);
        endpointGroups.add(endpointGroup);
      }

      final GCPPrivateServiceConnectRegionGroup regionGroup =
          new GCPPrivateServiceConnectRegionGroup(
              new ObjectId(),
              GCPPrivateServiceConnectRegionGroup.Status.AVAILABLE,
              null,
              false,
              null,
              regionName,
              endpointGroups,
              "11.0.0.0/8",
              numPSCs);

      _regionGroupDao.insertRegionGroup(projectId, containerId, regionGroup);

      final List<GCPPrivateServiceConnection> privateServiceConnections =
          NDSModelTestFactory.getGCPPrivateServiceConnections(projectId, regionGroup);
      _gcpPrivateServiceConnectionDao.insertPrivateServiceConnections(privateServiceConnections);

      for (int i = 0; i < privateServiceConnections.size(); i++) {
        final ServiceAttachment serviceAttachment = new ServiceAttachment();
        final List<ServiceAttachmentConnectedEndpoint> endpoints = new ArrayList<>();
        for (final GCPPrivateServiceConnectEndpointGroup endpointGroup : endpointGroups) {
          final ServiceAttachmentConnectedEndpoint endpoint =
              new ServiceAttachmentConnectedEndpoint();
          endpoint.setEndpoint(
              String.format(
                  "projects/%s/regions/%s/forwardingRules/%s",
                  endpointGroup.getCustomerGCPProjectId(),
                  regionGroup.getRegionName().get().getValue(),
                  endpointGroup.getForwardingRules().get(i).getForwardingRuleName()));
          endpoints.add(endpoint);
        }
        serviceAttachment.setConnectedEndpoints(endpoints);
        doReturn(serviceAttachment)
            .when(_gcpApiSvc)
            .getServiceAttachment(
                any(),
                any(),
                any(),
                any(),
                eq(privateServiceConnections.get(i).getServiceAttachmentName()));
      }

      for (final GCPPrivateServiceConnectEndpointGroup endpointGroup : endpointGroups) {
        final boolean getBrokenPairs =
            endpointGroupNameToNeedsFixingMap.get(endpointGroup.getEndpointGroupName());
        final List<Pair<GCPPrivateServiceConnection, GCPConsumerForwardingRule>> pairs =
            getServiceAttachmentForwardingRulePairsForTest(
                projectId,
                regionGroup,
                endpointGroup,
                new ObjectId(),
                atlasGCPProjectId,
                getBrokenPairs);

        pairs.forEach(
            pair -> {
              final GCPPrivateServiceConnection gcpPrivateServiceConnection = pair.getLeft();
              final GCPConsumerForwardingRule gcpConsumerForwardingRule = pair.getRight();
              _gcpPrivateServiceConnectionDao.setForwardingRuleOnPrivateServiceConnection(
                  projectId,
                  regionGroup.getId(),
                  gcpPrivateServiceConnection.getId(),
                  gcpConsumerForwardingRule.getId());

              _regionGroupDao.setPrivateServiceConnectionIdOnForwardingRule(
                  projectId,
                  containerId,
                  regionGroup.getId(),
                  endpointGroup.getEndpointGroupName(),
                  gcpConsumerForwardingRule.getId(),
                  gcpPrivateServiceConnection.getId());
            });
      }
    }

    return _ndsGroupDao.find(projectId).orElseThrow();
  }

  private List<Pair<GCPPrivateServiceConnection, GCPConsumerForwardingRule>>
      getServiceAttachmentForwardingRulePairsForTest(
          final ObjectId pProjectId,
          final GCPPrivateServiceConnectRegionGroup pRegionGroup,
          final GCPPrivateServiceConnectEndpointGroup pEndpointGroup,
          final ObjectId pAtlasGCPOrganizationId,
          final String pAtlasGCPProjectId,
          final boolean pGetBrokenPairs) {
    final List<GCPPrivateServiceConnection> privateServiceConnections =
        _gcpPrivateServiceConnectionDao.findPrivateServiceConnectionsForRegionGroup(
            pProjectId, pRegionGroup.getId());
    final List<Pair<GCPPrivateServiceConnection, GCPConsumerForwardingRule>> pairs =
        new ArrayList<>();
    for (final GCPPrivateServiceConnection privateServiceConnection : privateServiceConnections) {
      final ServiceAttachment serviceAttachment =
          _gcpApiSvc.getServiceAttachment(
              pAtlasGCPOrganizationId,
              pAtlasGCPProjectId,
              pRegionGroup.getRegionName().get(),
              LOG,
              privateServiceConnection.getServiceAttachmentName());

      final List<GCPConsumerForwardingRule> atlasForwardingRules =
          pEndpointGroup.getForwardingRules();
      final List<ServiceAttachmentConnectedEndpoint> gcpForwardingRules =
          Optional.ofNullable(serviceAttachment.getConnectedEndpoints())
              .orElse(Collections.emptyList());

      final Optional<GCPConsumerForwardingRule> forwardingRuleForPrivateServiceConnection;
      if (pGetBrokenPairs) {
        forwardingRuleForPrivateServiceConnection =
            atlasForwardingRules.stream()
                .filter(
                    atlasForwardingRule ->
                        gcpForwardingRules.stream()
                            .map(ServiceAttachmentConnectedEndpoint::getEndpoint)
                            .anyMatch(
                                gcpForwardingRule ->
                                    gcpForwardingRule != null
                                        && gcpForwardingRule.contains(
                                            atlasForwardingRule.getForwardingRuleName())))
                .findFirst();
      } else {
        forwardingRuleForPrivateServiceConnection =
            atlasForwardingRules.stream()
                .filter(
                    atlasForwardingRule ->
                        gcpForwardingRules.stream()
                            .map(ServiceAttachmentConnectedEndpoint::getEndpoint)
                            .anyMatch(
                                gcpForwardingRule ->
                                    gcpForwardingRule != null
                                        && gcpForwardingRule
                                            .substring(gcpForwardingRule.lastIndexOf("/") + 1)
                                            .equals(atlasForwardingRule.getForwardingRuleName())))
                .findFirst();
      }

      pairs.add(
          Pair.of(
              privateServiceConnection, forwardingRuleForPrivateServiceConnection.orElse(null)));
    }

    return pairs;
  }

  private boolean areForwardingRulesCorrectForProject(final ObjectId pProjectId) {
    final NDSGroup group = _ndsGroupDao.find(pProjectId).get();
    final List<GCPPrivateServiceConnectEndpointGroup> endpointGroups =
        group
            .getCloudProviderContainerByType(CloudProvider.GCP)
            .map(GCPCloudProviderContainer.class::cast)
            .get()
            .getPscRegionGroups()
            .stream()
            .flatMap(rg -> rg.getGCPEndpointGroups().stream())
            .collect(Collectors.toList());
    for (final GCPPrivateServiceConnectEndpointGroup endpointGroup : endpointGroups) {
      final Set<ObjectId> distinctForwardingRuleIds =
          endpointGroup.getForwardingRules().stream()
              .map(GCPConsumerForwardingRule::getPrivateServiceConnectionId)
              .collect(Collectors.toSet());
      if (distinctForwardingRuleIds.size() < endpointGroup.getForwardingRules().size()) {
        return false;
      }
    }
    return true;
  }
}
