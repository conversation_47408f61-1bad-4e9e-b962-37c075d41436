package com.xgen.svc.mms.misc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.amazonaws.services.ec2.model.Instance;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSCloudProviderContainerDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.dns._public.model.DNSChangeItem;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.misc.BackfillGCPHostedZoneDNSRecordsForGovAWSClusters.DNSSubmission;
import com.xgen.svc.mms.misc.BackfillGCPHostedZoneDNSRecordsForGovAWSClusters.OptionResult;
import com.xgen.svc.nds.aws.dns.DNSChangeQueueDao;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class BackfillGCPHostedZoneDNSRecordsForGovAWSClustersIntTests extends JUnit5BaseSvcTest {
  @Inject private AppSettings _appSettings;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private AWSCloudProviderContainerDao _containerDao;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private DNSChangeQueueDao _dnsQueueDao;

  ObjectId _accountId;

  static final Instant INITIAL_REF = Instant.EPOCH.plus(Duration.ofDays(365));
  static final int NUM_CLUSTERS = 3;

  @Override
  public void setUp() throws Exception {
    super.setUp();
    final AWSAccount dnsAccount = AWSAccount.builder().setForDNS(true).build();
    _accountId = dnsAccount.getId();
    _awsAccountDao.save(dnsAccount);
  }

  @Test
  public void testApplicableClusterDescriptions_groupId() {
    final ObjectId groupId = ObjectId.get();
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);

    setupClusterDescriptions(groupId, INITIAL_REF);
    final BackfillGCPHostedZoneDNSRecordsForGovAWSClusters backfillTool =
        new BackfillGCPHostedZoneDNSRecordsForGovAWSClusters(
            _ndsClusterSvc,
            _replicaSetHardwareSvc,
            _clusterDescriptionDao,
            awsApiSvc,
            _awsAccountDao,
            _dnsQueueDao,
            _appSettings);

    final OptionResult optionResult = OptionResult.builder().groupId(groupId).build();
    final Set<ObjectId> expectedApplicable =
        _clusterDescriptionDao.findByGroup(groupId).stream()
            .map(ClusterDescription::getUniqueId)
            .collect(Collectors.toSet());
    final Set<ObjectId> actualApplicable =
        backfillTool.getApplicableAWSClusterDescriptions(optionResult).stream()
            .map(ClusterDescription::getUniqueId)
            .collect(Collectors.toSet());
    assertEquals(expectedApplicable, actualApplicable);
  }

  @Test
  public void testApplicableClusterDescriptions_activeClusters() {
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);

    final List<Pair<ObjectId, List<ClusterDescription>>> results =
        IntStream.range(1, 3)
            .mapToObj(
                i -> {
                  final ObjectId groupId = ObjectId.get();
                  final Instant start = INITIAL_REF.plus(Duration.ofDays(365L * i));
                  final Pair<ObjectId, List<ClusterDescription>> containerClusters =
                      setupClusterDescriptions(groupId, start);
                  final List<ClusterDescription> clusterDescriptions = containerClusters.getRight();
                  return Pair.of(groupId, clusterDescriptions);
                })
            .toList();

    final BackfillGCPHostedZoneDNSRecordsForGovAWSClusters backfillTool =
        new BackfillGCPHostedZoneDNSRecordsForGovAWSClusters(
            _ndsClusterSvc,
            _replicaSetHardwareSvc,
            _clusterDescriptionDao,
            awsApiSvc,
            _awsAccountDao,
            _dnsQueueDao,
            _appSettings);

    // Test - all active clusters
    final List<ClusterDescription> allClusterIds = new ArrayList<>();
    allClusterIds.addAll(results.get(0).getRight());
    allClusterIds.addAll(results.get(1).getRight());
    assertEquals(
        allClusterIds.stream().map(ClusterDescription::getUniqueId).toList(),
        backfillTool.getApplicableAWSClusterDescriptions(OptionResult.builder().build()).stream()
            .map(ClusterDescription::getUniqueId)
            .toList());

    // Test - starting clusterId
    final List<ClusterDescription> newerClusters = new ArrayList<>(results.get(1).getRight());
    // processed only half of the clusters, restart at (group1, cluster0)
    final ObjectId startingClusterId = results.get(1).getRight().get(0).getUniqueId();
    assertEquals(
        newerClusters.stream().map(ClusterDescription::getUniqueId).toList(),
        backfillTool
            .getApplicableAWSClusterDescriptions(
                OptionResult.builder().startingClusterId(startingClusterId).build())
            .stream()
            .map(ClusterDescription::getUniqueId)
            .toList());
  }

  @Test
  public void testDetermineDNSSubmissionForCluster() {
    final ObjectId groupId = ObjectId.get();

    // setup private ip lookup
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final int numHardwares = 3;
    final List<Pair<String, String>> instancesData =
        IntStream.range(0, numHardwares)
            .mapToObj(i -> Pair.of("ec2InstanceId" + i, "privateIp" + i))
            .toList();

    // mock out aws api
    mockInstancesForAWS(awsApiSvc, instancesData);

    final BackfillGCPHostedZoneDNSRecordsForGovAWSClusters backfillTool =
        new BackfillGCPHostedZoneDNSRecordsForGovAWSClusters(
            _ndsClusterSvc,
            _replicaSetHardwareSvc,
            _clusterDescriptionDao,
            awsApiSvc,
            _awsAccountDao,
            _dnsQueueDao,
            _appSettings);

    final int numClusters = 1;
    final int expectedDNSRecordsPerHardware = 3;

    final Pair<ObjectId, List<ClusterDescription>> containerClusters =
        setupClusterDescriptions(
            groupId, INITIAL_REF, numClusters, instancesData.stream().map(Pair::getLeft).toList());
    final List<ClusterDescription> clusterDescriptions = containerClusters.getRight();

    // test tool first run dns change items not submitted
    final List<DNSSubmission> dnsSumissionsPreWrite =
        backfillTool.determineDNSSubmissionForCluster(
            clusterDescriptions.get(0), CloudProvider.AWS, false);
    assertEquals(numHardwares * expectedDNSRecordsPerHardware, dnsSumissionsPreWrite.size());

    // submit changes to queue
    for (final DNSSubmission dnsSubmission : dnsSumissionsPreWrite) {
      final DNSChangeItem dnsChangeItem = dnsSubmission.getChangeItem();
      _dnsQueueDao.saveNewItem(dnsChangeItem);
    }

    // skip flag off second submission no effect
    final List<DNSSubmission> dnsSumissionsPostWriteSkipOff =
        backfillTool.determineDNSSubmissionForCluster(
            clusterDescriptions.get(0), CloudProvider.AWS, false);
    assertEquals(0, dnsSumissionsPostWriteSkipOff.size());

    // skip flag on second submission unchanged
    final List<DNSSubmission> dnsSumissionsPostWriteSkipOn =
        backfillTool.determineDNSSubmissionForCluster(
            clusterDescriptions.get(0), CloudProvider.AWS, true);
    assertEquals(numHardwares * expectedDNSRecordsPerHardware, dnsSumissionsPostWriteSkipOn.size());
  }

  @Test
  public void testProcessSingleClusterDescriptionBatch() {
    final ObjectId groupId = ObjectId.get();

    // setup private ip lookup
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final int numHardwares = 3;
    final List<Pair<String, String>> instancesData =
        IntStream.range(0, numHardwares)
            .mapToObj(i -> Pair.of("ec2InstanceId" + i, "privateIp" + i))
            .toList();

    // mock out aws api
    mockInstancesForAWS(awsApiSvc, instancesData);

    final BackfillGCPHostedZoneDNSRecordsForGovAWSClusters backfillTool =
        new BackfillGCPHostedZoneDNSRecordsForGovAWSClusters(
            _ndsClusterSvc,
            _replicaSetHardwareSvc,
            _clusterDescriptionDao,
            awsApiSvc,
            _awsAccountDao,
            _dnsQueueDao,
            _appSettings);
    final int numClusters = 1;
    final ObjectId ec2AwsAccountId = ObjectId.get();
    final Pair<ObjectId, List<ClusterDescription>> containerClusters =
        setupClusterDescriptions(
            groupId,
            ec2AwsAccountId,
            INITIAL_REF,
            numClusters,
            instancesData.stream().map(Pair::getLeft).toList());
    final List<ClusterDescription> clusterDescriptions = containerClusters.getRight();
    final ObjectId containerId = containerClusters.getLeft();

    final List<Pair<ClusterDescription, List<DNSSubmission>>> result =
        backfillTool.processSingleClusterDescriptionBatch(clusterDescriptions, 1, false, false);

    // expected 3 dns submissions per node
    assertEquals(numClusters, result.size());

    // verify private ip lookup
    for (final Pair<String, String> p : instancesData) {
      verify(awsApiSvc, times(1))
          .findEC2Instance(eq(ec2AwsAccountId), any(), any(), eq(p.getLeft()));
    }

    // verify dns change items group dns items by instance hardware id
    final Map<ObjectId, List<DNSSubmission>> groupedDNSSubmissions =
        result.stream()
            .map(Pair::getRight)
            .flatMap(Collection::stream)
            .collect(Collectors.groupingBy(DNSSubmission::getInstanceHardwareId));

    for (final Map.Entry<ObjectId, List<DNSSubmission>> entry : groupedDNSSubmissions.entrySet()) {
      final List<DNSSubmission> submissions = entry.getValue();
      assertEquals(3, submissions.size());
    }
  }

  @Test
  public void testProcessAllClusterDescriptionBatches() {
    final ObjectId groupId = ObjectId.get();

    // setup private ip lookup
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final int numHardwares = 3;
    final List<Pair<String, String>> instancesData =
        IntStream.range(0, numHardwares)
            .mapToObj(i -> Pair.of("ec2InstanceId" + i, "privateIp" + i))
            .toList();

    // mock out aws api
    mockInstancesForAWS(awsApiSvc, instancesData);

    final BackfillGCPHostedZoneDNSRecordsForGovAWSClusters backfillTool =
        new BackfillGCPHostedZoneDNSRecordsForGovAWSClusters(
            _ndsClusterSvc,
            _replicaSetHardwareSvc,
            _clusterDescriptionDao,
            awsApiSvc,
            _awsAccountDao,
            _dnsQueueDao,
            _appSettings);

    final int numClusters = 3;
    setupClusterDescriptions(
        groupId, INITIAL_REF, numClusters, instancesData.stream().map(Pair::getLeft).toList());
    // run with
    backfillTool.processAllClusterDescriptionBatches(
        OptionResult.builder().dryRun(false).build(), 2, 1);
  }

  private void mockInstancesForAWS(
      final AWSApiSvc pAWSApiSvc, final List<Pair<String, String>> pEC2Pairs) {
    for (final Pair<String, String> pair : pEC2Pairs) {
      final Instance instance = new Instance();
      instance.setPrivateIpAddress(pair.getRight());
      doReturn(instance).when(pAWSApiSvc).findEC2Instance(any(), any(), any(), eq(pair.getLeft()));
    }
  }

  private Pair<ObjectId, List<ClusterDescription>> setupClusterDescriptions(
      final ObjectId pGroupId, final Instant pTimeReference) {
    return setupClusterDescriptions(pGroupId, pTimeReference, NUM_CLUSTERS);
  }

  private Pair<ObjectId, List<ClusterDescription>> setupClusterDescriptions(
      final ObjectId pGroupId, final Instant pTimeReference, final int pNumClusters) {
    return setupClusterDescriptions(
        pGroupId,
        pTimeReference,
        pNumClusters,
        IntStream.range(0, 3).mapToObj(i -> "i-2134").toList());
  }

  // return pair of containerId and clusters within that container
  private Pair<ObjectId, List<ClusterDescription>> setupClusterDescriptions(
      final ObjectId pGroupId,
      final Instant pTimeReference,
      final int pNumClusters,
      final List<String> pInstanceHardwareData) {
    return setupClusterDescriptions(
        pGroupId, ObjectId.get(), pTimeReference, pNumClusters, pInstanceHardwareData);
  }

  // return pair of containerId and clusters within that container
  private Pair<ObjectId, List<ClusterDescription>> setupClusterDescriptions(
      final ObjectId pGroupId,
      final ObjectId awsAccountId,
      final Instant pTimeReference,
      final int pNumClusters,
      final List<String> pInstanceHardwareData) {
    try {
      _ndsGroupSvc.ensureGroup(pGroupId);
    } catch (final SvcException pE) {
      throw new RuntimeException(pE);
    }

    final ObjectId containerId =
        _containerDao
            .addAWSCloudContainer(
                pGroupId,
                new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer(awsAccountId)))
            .orElseThrow();

    final List<ObjectId> idsDerivedFromDate =
        IntStream.range(0, pNumClusters)
            .mapToObj(i -> pTimeReference.plus(Duration.ofDays(i)))
            .map(Date::from)
            .map(ObjectId::new)
            .toList();
    final List<ClusterDescription> clusterDescriptions =
        idsDerivedFromDate.stream()
            .map(
                uniqueId -> {
                  final String identifier = uniqueId.toHexString().substring(20, 24);
                  return NDSModelTestFactory.getAWSClusterDescription(
                          pGroupId, "Cluster" + identifier)
                      .append(FieldDefs.DNS_PIN, identifier)
                      .append(FieldDefs.UNIQUE_ID, uniqueId);
                })
            .map(ClusterDescription::new)
            .toList();

    clusterDescriptions.forEach(
        cd -> {
          _clusterDescriptionDao.save(cd);
          final BasicDBObject replicaSetHardware =
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, containerId, cd);

          // overwrite ec2instanceId for lookup
          final BasicDBList instancehardware =
              (BasicDBList) replicaSetHardware.get("cloudProviderHardware");
          assertEquals(instancehardware.size(), pInstanceHardwareData.size());
          for (int i = 0; i < instancehardware.size(); i++) {
            final BasicDBObject hardware = (BasicDBObject) instancehardware.get(i);
            hardware.append("ec2InstanceId", pInstanceHardwareData.get(i));
          }

          _replicaSetHardwareDao.saveReplicaSafe(replicaSetHardware);
        });

    return Pair.of(containerId, clusterDescriptions);
  }
}
