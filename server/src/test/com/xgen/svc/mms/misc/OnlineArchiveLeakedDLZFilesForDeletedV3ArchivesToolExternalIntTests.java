package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.HeadBucketRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.waiters.WaiterParameters;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.aws._public.clients.AWSClientsFactory;
import com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.AWSAccountBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureStorageAccountDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureStorageAccount;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveHistoryDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveRunDao;
import com.xgen.cloud.nds.onlinearchive._public.model.DataLandingZoneConfig;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveHistory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun;
import com.xgen.svc.core.BaseSvcTest;
import jakarta.inject.Inject;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesToolExternalIntTests
    extends BaseSvcTest {

  private static final Logger LOG =
      LoggerFactory.getLogger(
          OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesToolExternalIntTests.class);

  @Inject private AppSettings _appSettings;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private OnlineArchiveRunDao _onlineArchiveRunDao;
  @Inject private OnlineArchiveHistoryDao _onlineArchiveHistoryDao;
  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private AzureApiSvc _azureApiSvc;
  @Inject private AzureStorageAccountDao _azureStorageAccountDao;

  private ObjectId _groupId;
  private AWSClientsFactory _clientsFactory;
  private AWSRegionName _awsRegion;
  private AzureRegionName _azureRegion;
  private AzureStorageAccount _azureAccount;
  private String _testContainer;

  private static final String AWS_ACCESS_KEY = "local.aws.accessKey";
  private static final String AWS_SECRET_KEY = "local.aws.secretKey";
  private static final String TEST_BUCKET = "oa-dlz-manifest-file-backfill-test";
  private static final String TEST_CONTAINER_PREFIX = "deleteleakedfilestest";
  private static final String AZURE_STORAGE_ACCOUNT_NAME = "mdbdlzoaeaus2l";
  private static final String AZURE_STORAGE_ACCOUNT_KEY = "azure.oa.dlz.storageAccountKey";
  private static final String SAS_CONTAINER_PERMISSION_STRING = "carwld";

  @Before
  public void setup() throws Exception {
    super.setUp();
    _groupId = ObjectId.get();
    _awsRegion = AWSRegionName.US_EAST_1;
    _azureRegion = AzureRegionName.US_EAST_2;

    final String awsAccessKey = _appSettings.getStrProp(AWS_ACCESS_KEY);
    final String awsSecretKey = _appSettings.getStrProp(AWS_SECRET_KEY);
    final AWSAccount awsAccount =
        new AWSAccountBuilder()
            .setAccessKey(awsAccessKey)
            .setSecretKey(awsSecretKey)
            .setForOnlineArchiveDataLandingZone(true)
            .build();
    _awsAccountDao.save(awsAccount);

    _clientsFactory = new AWSClientsFactory(_appSettings.getNDSGovUSEnabled());
    final AmazonS3 s3Client = getS3Client();
    s3Client
        .waiters()
        .bucketExists()
        .run(new WaiterParameters<>(new HeadBucketRequest(TEST_BUCKET)));

    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(new ObjectId(), _azureRegion);
    _azureAccount =
        AzureStorageAccount.builder()
            .azureSubscriptionId(azureContainer.getAzureSubscriptionId())
            .regionName(_azureRegion)
            .storageAccountName(AZURE_STORAGE_ACCOUNT_NAME)
            .storageAccountKey(_appSettings.getStrProp(AZURE_STORAGE_ACCOUNT_KEY))
            .created(new Date())
            .lastUpdated(new Date())
            .build();
    _azureStorageAccountDao.save(_azureAccount);
    _testContainer = TEST_CONTAINER_PREFIX + new ObjectId().toHexString();
  }

  /**
   * archive1 - in AWS - deleted archive - files exist in DLZ - should set the dlzCleanUpEndDate
   *
   * <p>archive2 - in AWS - deleted archive - no files in DLZ
   *
   * <p>archive3 - in AWS - archive is not deleted.
   *
   * <p>archive4 - in AWS - archive is not deleted.
   *
   * <p>archive5 - in AZURE - deleted archive - files exist in DLZ - should set the
   * dlzCleanUpEndDate
   */
  @Test
  public void testDeleteLeakedDLZFilesForLeakedArchives() {
    final OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesTool tool =
        new OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesTool(
            _onlineArchiveDao,
            _onlineArchiveHistoryDao,
            _onlineArchiveRunDao,
            _awsApiSvc,
            _azureApiSvc,
            _awsAccountDao,
            _azureStorageAccountDao);

    final ObjectId archiveId1 = oid(1);
    final ObjectId archiveId2 = oid(2);
    final ObjectId archiveId3 = oid(3);
    final ObjectId archiveId4 = oid(4);
    final ObjectId archiveId5 = oid(5);

    final DataProcessRegion awsDataProcessRegion =
        new DataProcessRegion(CloudProvider.AWS.name(), _awsRegion.getName());
    final DataProcessRegion azureDataProcessRegion =
        new DataProcessRegion(CloudProvider.AZURE.name(), _azureRegion.getName());

    // Create archives, dlz files for these archives should not be cleaned up.
    createOA(_groupId, "Cluster0", archiveId3, OnlineArchive.State.ACTIVE, awsDataProcessRegion);
    createOA(_groupId, "Cluster0", archiveId4, OnlineArchive.State.ACTIVE, awsDataProcessRegion);

    // Create runs
    final String jobId1 = UUID.randomUUID().toString();
    createOARun(archiveId1, jobId1, 3, awsDataProcessRegion, null);

    final String jobId2 = UUID.randomUUID().toString();
    createOARun(archiveId2, jobId2, 3, awsDataProcessRegion, null);

    final String jobId3 = UUID.randomUUID().toString();
    createOARun(archiveId3, jobId3, 3, awsDataProcessRegion, null);

    final String jobId4 = UUID.randomUUID().toString();
    createOARun(archiveId4, jobId4, 3, awsDataProcessRegion, null);

    final String jobId5 = UUID.randomUUID().toString();
    createOARun(
        archiveId5,
        jobId5,
        3,
        azureDataProcessRegion,
        new DataLandingZoneConfig.AzureDataLandingZoneConfig(
            _azureRegion, _testContainer, AZURE_STORAGE_ACCOUNT_NAME));

    // Create run histories
    createOAHistory(archiveId1, 4);
    createOAHistory(archiveId2, 3);
    createOAHistory(archiveId3, 3);
    createOAHistory(archiveId4, 3);
    createOAHistory(archiveId5, 2);

    // Save files in S3.
    final AmazonS3 s3Client = getS3Client();
    createFilesInAWSDLZ(archiveId1, s3Client);
    createFilesInAWSDLZ(archiveId3, s3Client);
    createFilesInAWSDLZ(archiveId4, s3Client);

    // Save files in Azure.
    final NDSAzureTempCredentialsView storageAccountToken =
        _azureApiSvc.generateStorageAccountSasToken(
            _azureAccount.getStorageAccountName(),
            _appSettings.getStrProp(AZURE_STORAGE_ACCOUNT_KEY),
            "cd");
    _azureApiSvc.createBlobContainerIfNotExists(
        _testContainer, storageAccountToken.getSasUri(), storageAccountToken.getSasToken(), LOG);
    final NDSAzureTempCredentialsView storageContainerToken =
        _azureApiSvc.generateStorageContainerSasToken(
            _azureAccount.getStorageAccountName(),
            _azureAccount.getUnencryptedStorageAccountKey(),
            _testContainer,
            SAS_CONTAINER_PERMISSION_STRING);
    createFilesInAzure(archiveId5, storageContainerToken);

    try {
      tool.setOptions(
          new String[] {
            String.format("--startDate=%s", "08/10/2024"), String.format("--outputFile=%s", "file"),
          });

      final Map<ObjectId, Long> deletedArchivesSuccessfullyDeletedFilesInDLZ = new HashMap<>();
      final Set<ObjectId> deletedArchivesWithNoFilesInDLZ = new HashSet<>();
      final Map<ObjectId, String> deletedArchivesWithSomeErrorDeletingFilesInDLZ = new HashMap<>();

      final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      startDate.set(Calendar.HOUR_OF_DAY, 0);
      startDate.set(Calendar.MINUTE, 0);
      startDate.set(Calendar.SECOND, 0);
      startDate.set(Calendar.MILLISECOND, 0);

      final File outputFile = new File("testfile");
      try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
        tool.deleteLeakedDLZFilesForDeletedArchives(
            1000,
            DateUtils.addDays(startDate.getTime(), -4),
            new Date(),
            deletedArchivesSuccessfullyDeletedFilesInDLZ,
            deletedArchivesWithNoFilesInDLZ,
            deletedArchivesWithSomeErrorDeletingFilesInDLZ,
            writer);
      }

      // archive1 (aws) had files in dlz which are now deleted, also its dlz cleanup date is set.
      // archive5 (azure) had files in dlz which are now deleted, also its dlz cleanup date is set.
      assertEquals(deletedArchivesSuccessfullyDeletedFilesInDLZ.size(), 2);
      assertTrue(deletedArchivesSuccessfullyDeletedFilesInDLZ.containsKey(archiveId1));
      assertNotNull(_onlineArchiveRunDao.findByJobId(jobId1).get().getDlzCleanupEndDate());
      assertTrue(deletedArchivesSuccessfullyDeletedFilesInDLZ.containsKey(archiveId5));
      assertNotNull(_onlineArchiveRunDao.findByJobId(jobId5).get().getDlzCleanupEndDate());

      // archive2 (aws) did not have files in dlz, also its dlz cleanup date is not set.
      assertEquals(deletedArchivesWithNoFilesInDLZ.size(), 1);
      assertTrue(deletedArchivesWithNoFilesInDLZ.contains(archiveId2));
      assertNull(_onlineArchiveRunDao.findByJobId(jobId2).get().getDlzCleanupEndDate());

      assertTrue(deletedArchivesWithSomeErrorDeletingFilesInDLZ.isEmpty());

      // archive 3 and 4 exists, thus its cleanup date is unaffected.
      assertNull(_onlineArchiveRunDao.findByJobId(jobId3).get().getDlzCleanupEndDate());
      assertNull(_onlineArchiveRunDao.findByJobId(jobId4).get().getDlzCleanupEndDate());
    } catch (final Exception e) {
      fail();
    } finally {
      // clean up resources.
      s3Client.deleteObject(TEST_BUCKET, String.format("%s/%s", _groupId, archiveId1));
      s3Client.deleteObject(TEST_BUCKET, String.format("%s/%s", _groupId, archiveId2));
      s3Client.deleteObject(TEST_BUCKET, String.format("%s/%s", _groupId, archiveId3));
      s3Client.deleteObject(TEST_BUCKET, String.format("%s/%s", _groupId, archiveId4));

      _azureApiSvc.deleteBlobContainerIfExists(
          _testContainer, storageAccountToken.getSasUri(), storageAccountToken.getSasToken(), LOG);
    }
  }

  private void createOA(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pArchiveId,
      final OnlineArchive.State pState,
      final DataProcessRegion pDataProcessRegion) {
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("string", "field1", 0),
            new PartitionField("string", "field2", 1),
            new PartitionField("date", "dateField", 2));

    _onlineArchiveDao.create(
        new OnlineArchive.Builder()
            .setArchiveId(pArchiveId)
            .setClusterId(pGroupId, pClusterName)
            .setDbName("mydb")
            .setCollName("mycoll")
            .setPartitionFields(partitionFields)
            .setCriteria(
                new OnlineArchive.DateCriteria(
                    "dateField", 5, OnlineArchive.DateCriteria.DateFormat.ISODATE))
            .setState(pState)
            .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
            .setCollectionUUID(UUID.randomUUID())
            .setDataSetName("dataset")
            .setDataProcessRegion(pDataProcessRegion)
            .setDataLandingZoneBucketName(TEST_BUCKET)
            .build());
  }

  private void createOARun(
      final ObjectId pArchiveId,
      final String pJobId,
      final int pDateOffset,
      final DataProcessRegion pDataProcessRegion,
      final DataLandingZoneConfig dlzConfig) {
    final Instant now = Instant.now();
    final Date startDate = Date.from(now.minus(Duration.ofDays(pDateOffset)));
    final Date endDate = Date.from(now.minus(Duration.ofDays(pDateOffset - 1)));

    _onlineArchiveRunDao.save(
        new OnlineArchiveRun.Builder()
            .id(new ObjectId())
            .archiveId(pArchiveId)
            .groupId(_groupId)
            .clusterName("Cluster0")
            .jobId(pJobId)
            .startDate(startDate)
            .endDate(endDate)
            .errorMessage(null)
            .numFrsJobErrors(0)
            .state(OnlineArchiveRun.ArchiveRunState.COMPLETED)
            .onlineArchiveVersion(OnlineArchiveVersion.V3)
            .dataLandingZoneBucketName(TEST_BUCKET)
            .dataProcessRegion(pDataProcessRegion)
            .dataLandingZoneConfig(dlzConfig)
            .dlzCleanupEndDate(null)
            .hasDlzCleanupError(false)
            .dlzCleanupErrorMessage(null)
            .build());
  }

  private void createOAHistory(final ObjectId pArchiveId, final int pDateOffset) {

    final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
    startDate.set(Calendar.HOUR_OF_DAY, 0);
    startDate.set(Calendar.MINUTE, 0);
    startDate.set(Calendar.SECOND, 0);
    startDate.set(Calendar.MILLISECOND, 0);

    final OnlineArchiveHistory history =
        new OnlineArchiveHistory.Builder()
            .id(new ObjectId())
            .archiveId(pArchiveId)
            .startDate(DateUtils.addDays(startDate.getTime(), -pDateOffset))
            .clusterId(_groupId, "Cluster0")
            .frsJobPollingElapsedTimeMs(10)
            .build();
    _onlineArchiveHistoryDao.create(history);
  }

  private void createFilesInAWSDLZ(final ObjectId pArchiveId, final AmazonS3 pS3Client) {
    final String file = "some random data file";
    final InputStream stream = new ByteArrayInputStream(file.getBytes(StandardCharsets.UTF_8));
    final PutObjectRequest request =
        new PutObjectRequest(
            TEST_BUCKET,
            String.format("%s/%s", _groupId, pArchiveId),
            stream,
            new ObjectMetadata());
    pS3Client.putObject(request);
  }

  private void createFilesInAzure(
      final ObjectId pArchiveId, final NDSAzureTempCredentialsView pStorageContainerToken) {
    final InputStream stream =
        new ByteArrayInputStream("some random data file".getBytes(StandardCharsets.UTF_8));
    _azureApiSvc.uploadBlob(
        _testContainer,
        pStorageContainerToken.getSasUri(),
        pStorageContainerToken.getSasToken(),
        String.format("%s/%s", _groupId, pArchiveId),
        stream,
        LOG);
  }

  private AmazonS3 getS3Client() {
    final String awsAccessKey = _appSettings.getStrProp(AWS_ACCESS_KEY);
    final String awsSecretKey = _appSettings.getStrProp(AWS_SECRET_KEY);
    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(awsAccessKey, awsSecretKey, null, null);
    return _clientsFactory.getS3Client(provider, _awsRegion.getValue());
  }
}
