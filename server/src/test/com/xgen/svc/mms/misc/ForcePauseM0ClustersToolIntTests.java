package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.free._private.dao.FreeTenantClusterDescriptionDao;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.dao.pausefreetiermonitoring.NDSM0ClusterActivityDao;
import com.xgen.svc.mms.misc.ForcePauseM0ClustersTool.Status;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.io.File;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class ForcePauseM0ClustersToolIntTests extends BaseSvcTest {

  private File _groupIdJsonFile;
  private File _csvOutputFile;
  private List<ObjectId> _groupIds;
  private Map<ObjectId, ObjectId> _clusterIdToGroupIdMapping;

  private List<ObjectId> _alreadyNotifiedClusterUniqueIds;
  private List<ObjectId> _alreadyPausedClusterUniqueIds;
  private List<ObjectId> _excludedFromPauseClusterUniqueIds;

  @Inject private NDSGroupDao _ndsGroupDao;

  @Inject private GroupSvc _groupSvc;

  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  @Inject private FreeTenantClusterDescriptionDao _freeTenantClusterDescriptionDao;

  @Inject private NDSM0ClusterActivityDao _ndsM0ClusterActivityDao;

  private ForcePauseM0ClustersTool _forcePauseM0ClustersTool;

  @Before
  public void setUp() throws Exception {
    super.setUp();

    _groupIdJsonFile = File.createTempFile("groups", "json");
    _groupIdJsonFile.deleteOnExit();

    _csvOutputFile = File.createTempFile("results", "csv");
    _csvOutputFile.delete();

    final List<Group> groups =
        IntStream.range(0, 10)
            .mapToObj(
                i -> {
                  final Organization organization =
                      MmsFactory.createOrganizationWithNDSPlan(
                          String.format("Org-%s", i), new Date());

                  return IntStream.range(0, 15)
                      .mapToObj(
                          j -> {
                            try {
                              final Group group =
                                  MmsFactory.createGroup(
                                      organization, String.format("Group-%s", j));
                              _ndsGroupSvc.ensureGroup(group.getId());

                              return group;
                            } catch (final SvcException pE) {
                              throw new RuntimeException(pE);
                            }
                          });
                })
            .flatMap(s -> s)
            .collect(Collectors.toList());

    final JSONArray groupIdJsonArray = new JSONArray();
    groups.stream()
        .map(g -> new JSONObject().put("id", g.getId()).put("name", g.getName()))
        .forEach(groupIdJsonArray::put);

    _groupIds = groups.stream().map(Group::getId).collect(Collectors.toList());

    // create the JSON file
    try (final FileWriter fileWriter = new FileWriter(_groupIdJsonFile)) {
      fileWriter.write(groupIdJsonArray.toString());
    }

    // randomly assign M0 clusters to each group and save them in the DB
    final Random random = new Random();
    while (_clusterIdToGroupIdMapping == null || _clusterIdToGroupIdMapping.isEmpty()) {
      _clusterIdToGroupIdMapping =
          groups.stream()
              .filter(g -> random.nextBoolean())
              .map(
                  g -> {
                    final ClusterDescription clusterDescription =
                        new ClusterDescription(
                            NDSModelTestFactory.getFreeClusterDescription(
                                new TestFreeClusterDescriptionConfig()
                                    .setClusterName("Cluster0")
                                    .setGroupId(g.getId())
                                    .setDnsPin(g.getId().toHexString())
                                    .setBackingProvider(CloudProvider.AWS)
                                    .setInstanceSize(FreeInstanceSize.M0)));

                    _clusterDescriptionDao.save(clusterDescription);

                    return Map.entry(
                        clusterDescription.getUniqueId(), clusterDescription.getGroupId());
                  })
              .collect(
                  Collectors.toUnmodifiableMap(entry -> entry.getKey(), entry -> entry.getValue()));
    }

    // randomly set some percentage as already notified about pause
    while (_alreadyNotifiedClusterUniqueIds == null || _alreadyNotifiedClusterUniqueIds.isEmpty()) {
      _alreadyNotifiedClusterUniqueIds =
          _clusterIdToGroupIdMapping.entrySet().stream()
              .filter(c -> random.nextInt(100) <= 10)
              .peek(
                  cId ->
                      _freeTenantClusterDescriptionDao
                          .updateFreeTenantClusterUserNotifiedAboutPauseDate(
                              cId.getValue(), cId.getKey(), new Date()))
              .map(entry -> entry.getKey())
              .collect(Collectors.toList());
    }

    // randomly set some percentage as already paused
    while (_alreadyPausedClusterUniqueIds == null || _alreadyPausedClusterUniqueIds.isEmpty()) {
      _alreadyPausedClusterUniqueIds =
          _clusterIdToGroupIdMapping.entrySet().stream()
              .filter(cId -> !_alreadyNotifiedClusterUniqueIds.contains(cId.getKey()))
              .filter(c -> random.nextInt(100) <= 7)
              .peek(
                  cId ->
                      _freeTenantClusterDescriptionDao
                          .updateFreeTenantClusterUserNotifiedAboutPauseDate(
                              cId.getValue(), cId.getKey(), DateUtils.addDays(new Date(), -8)))
              .peek(
                  cId -> {
                    final ClusterDescription clusterDescription =
                        _clusterDescriptionDao.findByUniqueId(null, cId.getKey()).orElseThrow();
                    final ClusterDescription.Builder<?, ClusterDescription>
                        clusterDescriptionBuilder = clusterDescription.copy();
                    clusterDescriptionBuilder.setIsPaused(true);

                    final FreeTenantProviderOptions freeTenantProviderOptions =
                        (FreeTenantProviderOptions)
                            clusterDescription.getFreeTenantProviderOptions();
                    clusterDescriptionBuilder.setFreeTenantProviderOptions(
                        freeTenantProviderOptions
                            .copy()
                            .setNdsAccessRevokedDate(new Date())
                            .build());

                    _clusterDescriptionDao.save(clusterDescriptionBuilder.build());
                  })
              .map(entry -> entry.getKey())
              .collect(Collectors.toList());
    }

    // randomly set some percentage as excluded from pause
    while (_excludedFromPauseClusterUniqueIds == null
        || _excludedFromPauseClusterUniqueIds.isEmpty()) {
      _excludedFromPauseClusterUniqueIds =
          _clusterIdToGroupIdMapping.entrySet().stream()
              .filter(c -> !_alreadyPausedClusterUniqueIds.contains(c.getKey()))
              .filter(c -> !_alreadyNotifiedClusterUniqueIds.contains(c.getKey()))
              .filter(c -> random.nextInt(100) <= 5)
              .peek(
                  cId ->
                      _freeTenantClusterDescriptionDao
                          .updateFreeTenantClusterExcludeFromAutomaticPause(
                              cId.getValue(), cId.getKey()))
              .map(entry -> entry.getKey())
              .collect(Collectors.toList());
    }

    _forcePauseM0ClustersTool =
        new ForcePauseM0ClustersTool(
            _ndsGroupDao,
            _clusterDescriptionDao,
            _freeTenantClusterDescriptionDao,
            _ndsM0ClusterActivityDao);
  }

  @After
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    Optional.ofNullable(_groupIdJsonFile).filter(File::exists).ifPresent(File::delete);
    Optional.ofNullable(_csvOutputFile).filter(File::exists).ifPresent(File::delete);
  }

  private void testTool(final int pLimit) throws Exception {
    final long numClustersPausedBeforeRun =
        _clusterIdToGroupIdMapping.entrySet().stream()
            .map(entry -> _clusterDescriptionDao.findByUniqueId(entry.getValue(), entry.getKey()))
            .flatMap(Optional::stream)
            .map(ClusterDescription::getFreeTenantProviderOptions)
            .map(FreeTenantProviderOptions.class::cast)
            .map(FreeTenantProviderOptions::getUserNotifiedAboutPauseDate)
            .filter(Objects::nonNull)
            .count();

    assertTrue(numClustersPausedBeforeRun > 0);
    assertEquals(
        numClustersPausedBeforeRun,
        _alreadyNotifiedClusterUniqueIds.size() + _alreadyPausedClusterUniqueIds.size());

    _forcePauseM0ClustersTool.run(_groupIdJsonFile.getPath(), 2l, pLimit, _csvOutputFile.getPath());

    final List<ClusterDescription> updatedClusterDescriptions =
        _clusterIdToGroupIdMapping.entrySet().stream()
            .map(entry -> _clusterDescriptionDao.findByUniqueId(entry.getValue(), entry.getKey()))
            .flatMap(Optional::stream)
            .collect(Collectors.toList());

    final List<ClusterDescription> clustersWithPauseNotifications =
        updatedClusterDescriptions.stream()
            .filter(
                cd ->
                    Objects.nonNull(
                        ((FreeTenantProviderOptions) cd.getFreeTenantProviderOptions())
                            .getUserNotifiedAboutPauseDate()))
            .collect(Collectors.toList());

    final int expectedNotifiedAboutPauseCount;
    if (pLimit == 0
        || pLimit
            > _clusterIdToGroupIdMapping.size()
                - _alreadyNotifiedClusterUniqueIds.size()
                - _alreadyPausedClusterUniqueIds.size()
                - _excludedFromPauseClusterUniqueIds.size()) {
      expectedNotifiedAboutPauseCount =
          _clusterIdToGroupIdMapping.size() - _excludedFromPauseClusterUniqueIds.size();
    } else {
      expectedNotifiedAboutPauseCount =
          pLimit + _alreadyNotifiedClusterUniqueIds.size() + _alreadyPausedClusterUniqueIds.size();
    }

    assertEquals(clustersWithPauseNotifications.size(), expectedNotifiedAboutPauseCount);

    // ensure all of the newly set notification dates are greater than 7 days in the past (note that
    // already paused cluster descriptions are assumed to have been set less than 7 days ago since
    // we set them to new Date() when setting up the test
    final Date sevenDaysAgoDate = DateUtils.addDays(new Date(), -7);
    final long numClusterDescriptionsReadyToPauseByDuration =
        clustersWithPauseNotifications.stream()
            .map(ClusterDescription::getFreeTenantProviderOptions)
            .map(FreeTenantProviderOptions.class::cast)
            .map(FreeTenantProviderOptions::getUserNotifiedAboutPauseDate)
            .filter(Objects::nonNull)
            .filter(sevenDaysAgoDate::after)
            .count();
    assertEquals(
        expectedNotifiedAboutPauseCount - _alreadyNotifiedClusterUniqueIds.size(),
        numClusterDescriptionsReadyToPauseByDuration);

    // confirm CSV results
    assertTrue(_csvOutputFile.exists());
    final List<String> csvRows = Files.readAllLines(Paths.get(_csvOutputFile.toURI()));

    // verify the total number of expected rows (+1 to include the header)
    final int numGroupsWithoutTenants = _groupIds.size() - _clusterIdToGroupIdMapping.size();
    final int expectedTotalRows =
        (pLimit > 0 & pLimit < _clusterIdToGroupIdMapping.size()
                ? numGroupsWithoutTenants
                    + _alreadyNotifiedClusterUniqueIds.size()
                    + _alreadyPausedClusterUniqueIds.size()
                    + _excludedFromPauseClusterUniqueIds.size()
                    + pLimit
                : _groupIds.size())
            + 1;
    assertEquals(expectedTotalRows, csvRows.size());

    // verify the header
    assertEquals(ForcePauseM0ClustersTool.CSV_HEADER_LINE, csvRows.get(0));

    // verify the field count for each row by delimiter (expected number of fields - 1)
    csvRows.forEach(r -> assertEquals(5, StringUtils.countMatches(r, ",")));

    // verify number M0_PAUSE_ALREADY_REQUESTED
    assertEquals(
        _alreadyNotifiedClusterUniqueIds.size(),
        getCountWithStatus(csvRows, Status.M0_PAUSE_ALREADY_REQUESTED));

    // verify number M0_ALREADY_PAUSED
    assertEquals(
        _alreadyPausedClusterUniqueIds.size(),
        getCountWithStatus(csvRows, Status.M0_ALREADY_PAUSED));

    // verify number M0_EXCLUDED_FROM_PAUSE_DUE_TO_FAILURE
    assertEquals(
        _excludedFromPauseClusterUniqueIds.size(),
        getCountWithStatus(csvRows, Status.M0_EXCLUDED_FROM_PAUSE_DUE_TO_FAILURE));

    // verify number SUCCESS
    assertEquals(
        expectedNotifiedAboutPauseCount
            - _alreadyNotifiedClusterUniqueIds.size()
            - _alreadyPausedClusterUniqueIds.size(),
        getCountWithStatus(csvRows, Status.SUCCESS));

    // verify number M0_CLUSTER_NOT_FOUND_FOR_GROUP
    assertEquals(
        _groupIds.size() - _clusterIdToGroupIdMapping.size(),
        getCountWithStatus(csvRows, Status.M0_CLUSTER_NOT_FOUND_FOR_GROUP));
  }

  private long getCountWithStatus(
      final List<String> pCSVRows, final ForcePauseM0ClustersTool.Status pStatus) {
    return pCSVRows.stream().filter(r -> r.contains(pStatus.name())).count();
  }

  @Test
  public void testTool_NoLimit() throws Exception {
    testTool(0);
  }

  @Test
  public void testTool_WithLimitLessThanPossiblePauses() throws Exception {
    testTool(3);
  }

  @Test
  public void testTool_WithLimitGreaterThanPossiblePauses() throws Exception {
    testTool(99);
  }
}
