package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.LimitSpan;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.OverrunPolicy;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDataLakeConfigDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveDataLakeConfig;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveDataLakeConfigSvc;
import jakarta.inject.Inject;
import java.util.Objects;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class OnlineArchiveDefaultQueryLimitBackfillToolIntTests extends BaseSvcTest {

  @Inject private OnlineArchiveDataLakeConfigSvc _onlineArchiveDataLakeConfigSvc;
  @Inject private NDSDataLakeTenantSvc _ndsDataLakeTenantSvc;
  @Inject private NDSDataLakePublicSvc _ndsDataLakePublicSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private DataLakeAdminApiClient _dataLakeAdminApiClient;
  @Inject private GroupDao _groupDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private OnlineArchiveDataLakeConfigDao _onlineArchiveDataLakeConfigDao;

  @Before
  public void setup() throws Exception {
    super.setUp();

    // Set up a group and cluster with 2 online archives whose metadata has resolved,
    // i.e. archives with existing data sources in the storage config
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveDefaultQueryLimitBackfillToolIntTests/groups.json.ftl",
        null,
        "mmsdbconfig",
        "config.customers");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveDefaultQueryLimitBackfillToolIntTests/ndsGroups.json.ftl",
        null,
        "nds",
        "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveDefaultQueryLimitBackfillToolIntTests/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveDefaultQueryLimitBackfillToolIntTests/onlineArchiveDataLakeConfigs.json.ftl",
        null,
        "nds",
        "config.nds.onlineArchiveDataLakeConfigs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveDefaultQueryLimitBackfillToolIntTests/queryEngineTenants.json.ftl",
        null,
        "nds",
        "config.nds.queryEngineTenants");

    // start mock admin API
    _dataLakeTestUtils.setUp();
  }

  @After
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testProcessDataLakeConfigs_dryRun() throws Exception {
    verifyQueryLimitsForConfig(oid(3), "Cluster0", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster2", false, false);
    verifyQueryLimitsForConfig(oid(3), "ClusterWithStateDeleted", false, false);

    final OnlineArchiveDefaultQueryLimitBackfillTool tool =
        new OnlineArchiveDefaultQueryLimitBackfillTool(
            _clusterDescriptionDao,
            _groupDao,
            _onlineArchiveDataLakeConfigDao,
            _ndsDataLakeTenantSvc,
            _ndsDataLakePublicSvc,
            _dataLakeAdminApiClient);

    tool.setOptions(new String[] {"--dryRun"});
    tool.processDataLakeConfigs();

    verifyQueryLimitsForConfig(oid(3), "Cluster0", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster2", false, false);
    verifyQueryLimitsForConfig(oid(3), "ClusterWithStateDeleted", false, false);
  }

  @Test
  public void testProcessDataLakeConfigs_limit() throws Exception {
    verifyQueryLimitsForConfig(oid(3), "Cluster0", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", false, true);

    final OnlineArchiveDefaultQueryLimitBackfillTool tool =
        new OnlineArchiveDefaultQueryLimitBackfillTool(
            _clusterDescriptionDao,
            _groupDao,
            _onlineArchiveDataLakeConfigDao,
            _ndsDataLakeTenantSvc,
            _ndsDataLakePublicSvc,
            _dataLakeAdminApiClient);

    tool.setOptions(new String[] {"--limit=1"});
    final OnlineArchiveDefaultQueryLimitBackfillTool.ProcessResult result1 =
        tool.processDataLakeConfigs();
    assertEquals(1, result1.getTotalProcessed());
    assertEquals(1, result1.getTotalUpdated());
    assertEquals(0, result1.getTotalUnchanged());
    assertEquals(0, result1.getTotalErrored());

    verifyQueryLimitsForConfig(oid(3), "Cluster0", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", false, true);

    // process again, this should skip Cluster0 and process Cluster1
    final OnlineArchiveDefaultQueryLimitBackfillTool.ProcessResult result2 =
        tool.processDataLakeConfigs();
    assertEquals(2, result2.getTotalProcessed());
    assertEquals(1, result2.getTotalUpdated());
    assertEquals(1, result2.getTotalUnchanged());
    assertEquals(0, result2.getTotalErrored());

    verifyQueryLimitsForConfig(oid(3), "Cluster0", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster2", false, false);
    verifyQueryLimitsForConfig(oid(3), "ClusterWithStateDeleted", false, false);
  }

  @Test
  public void testProcessDataLakeConfigs() throws Exception {
    verifyQueryLimitsForConfig(oid(3), "Cluster0", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", false, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster2", false, false);
    verifyQueryLimitsForConfig(oid(3), "ClusterWithStateDeleted", false, false);

    final OnlineArchiveDefaultQueryLimitBackfillTool tool =
        new OnlineArchiveDefaultQueryLimitBackfillTool(
            _clusterDescriptionDao,
            _groupDao,
            _onlineArchiveDataLakeConfigDao,
            _ndsDataLakeTenantSvc,
            _ndsDataLakePublicSvc,
            _dataLakeAdminApiClient);

    final OnlineArchiveDefaultQueryLimitBackfillTool.ProcessResult result1 =
        tool.processDataLakeConfigs();
    assertEquals(7, result1.getTotalProcessed());
    assertEquals(4, result1.getTotalUpdated());
    assertEquals(3, result1.getTotalUnchanged());
    assertEquals(0, result1.getTotalErrored());

    verifyQueryLimitsForConfig(oid(3), "Cluster0", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster2", true, false);
    verifyQueryLimitsForConfig(oid(3), "ClusterWithStateDeleted", true, false);

    // Verify idempotency
    final OnlineArchiveDefaultQueryLimitBackfillTool.ProcessResult result2 =
        tool.processDataLakeConfigs();

    verifyQueryLimitsForConfig(oid(3), "Cluster0", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster1", true, true);
    verifyQueryLimitsForConfig(oid(3), "Cluster2", true, false);
    verifyQueryLimitsForConfig(oid(3), "ClusterWithStateDeleted", true, false);
    assertEquals(7, result2.getTotalProcessed());
    assertEquals(0, result2.getTotalUpdated());
    assertEquals(7, result2.getTotalUnchanged());
    assertEquals(0, result2.getTotalErrored());
  }

  @Test
  public void testProcessDataLakeConfig_validationFailed() throws Exception {
    final OnlineArchiveDefaultQueryLimitBackfillTool tool =
        new OnlineArchiveDefaultQueryLimitBackfillTool(
            _clusterDescriptionDao,
            _groupDao,
            _onlineArchiveDataLakeConfigDao,
            _ndsDataLakeTenantSvc,
            _ndsDataLakePublicSvc,
            _dataLakeAdminApiClient);
    verifyProcessDataLakeConfigSkipped(tool, oid(1234), "GroupDoesNotExist");
    verifyProcessDataLakeConfigSkipped(tool, oid(17), "ClusterWithInactiveGroup");
    verifyProcessDataLakeConfigSkipped(tool, oid(3), "ClusterDoesNotExist");
  }

  public void verifyProcessDataLakeConfigSkipped(
      OnlineArchiveDefaultQueryLimitBackfillTool pTool,
      final ObjectId pGroupId,
      final String pClusterName)
      throws Exception {
    final OnlineArchiveDataLakeConfig onlineArchiveDataLakeConfig =
        _onlineArchiveDataLakeConfigSvc
            .getOnlineArchiveDataLakeConfig(pGroupId, pClusterName)
            .orElseThrow(
                () -> new IllegalStateException("Cannot find OnlineArchiveDataLakeConfig"));

    final boolean updated = pTool.processDataLakeConfig(onlineArchiveDataLakeConfig);
    assertFalse(updated);
  }

  private void verifyQueryLimitsForConfig(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pPostMigration,
      final boolean pBeforeQueryLimitRelease)
      throws SvcException {
    final OnlineArchiveDataLakeConfig onlineArchiveDataLakeConfig =
        _onlineArchiveDataLakeConfigSvc
            .getOnlineArchiveDataLakeConfig(pGroupId, pClusterName)
            .orElseThrow(
                () -> new IllegalStateException("Cannot find OnlineArchiveDataLakeConfig"));

    final NDSDataLakeTenant.NDSDataLakeTenantId federatedTenantId =
        onlineArchiveDataLakeConfig.getDataLakeTenantId();

    final NDSDataLakeTenant tenant =
        _ndsDataLakeTenantSvc
            .findByDataLakeTenantId(federatedTenantId)
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME,
                        federatedTenantId.getGroupId(),
                        federatedTenantId.getName()));
    final Optional<DataFederationUsageLimitView> usageLimitView =
        _ndsDataLakePublicSvc.getTenantUsageLimits(tenant).stream()
            .filter(
                limit ->
                    Objects.equals(limit.getLimitSpan().orElse(null), LimitSpan.MONTHLY)
                        && Objects.equals(
                            limit.getOverrunPolicy().orElse(null), OverrunPolicy.BLOCK))
            .findFirst();
    if (pPostMigration) {
      assertTrue(usageLimitView.isPresent());
      assertEquals(
          Optional.of(
              pBeforeQueryLimitRelease
                  ? OnlineArchiveDefaultQueryLimitBackfillTool
                      .DEFAULT_MONTHLY_LIMIT_BEFORE_QUERY_LIMIT_RELEASE
                  : OnlineArchiveDefaultQueryLimitBackfillTool
                      .DEFAULT_MONTHLY_LIMIT_AFTER_QUERY_LIMIT_RELEASE),
          usageLimitView.get().getLimitInBytes());
    } else {
      assertTrue(usageLimitView.isEmpty());
    }
  }
}
