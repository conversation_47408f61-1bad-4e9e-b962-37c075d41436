package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.amazonaws.services.ec2.model.DescribeInstancesResult;
import com.amazonaws.services.ec2.model.Instance;
import com.amazonaws.services.ec2.model.InstanceState;
import com.amazonaws.services.ec2.model.InstanceStateName;
import com.amazonaws.services.ec2.model.Reservation;
import com.amazonaws.services.ec2.model.Tag;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSAccountSvc;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.cloudprovider._public.svc.PartitionGroupSvc;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class AWSHostnameBackfillToolIntTests extends JUnit5BaseSvcTest {
  @Inject private AppSettings _appSettings;
  private AWSAccountSvc _awsAccountSvc;
  private AWSApiSvc _awsApiSvc;
  private ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  private NDSClusterSvc _ndsClusterSvc;
  private PartitionGroupSvc _partitionGroupSvc;
  private ClusterDescription clusterDescription;
  private final String ACCOUNT_NAME = "cloud-prod-2";

  @Override
  public void setUp() throws Exception {
    super.setUp();
    initializeMockSvcs();
  }

  @Test
  public void test_findInstancesInAllAccounts_toolOptionsProjectId() throws IOException {
    {
      // If toolOptions accountName doesn't match
      final AWSHostnameBackfillTool tool =
          new AWSHostnameBackfillTool(
              _appSettings,
              _awsAccountSvc,
              _awsApiSvc,
              _replicaSetHardwareSvc,
              _ndsClusterSvc,
              _partitionGroupSvc);
      tool.setToolOptions(
          AWSHostnameBackfillTool.ToolOptions.builder().accountName("cloud-prod-XYZ").build());
      tool.processInstancesInAllAccounts();
      verify(_awsApiSvc, never()).findEC2InstancesAndApply(any(), any(), anyInt(), any(), any());
    }
    clearInvocations(_awsApiSvc);

    {
      // If toolOptions accountName matches
      final AWSHostnameBackfillTool tool =
          new AWSHostnameBackfillTool(
              _appSettings,
              _awsAccountSvc,
              _awsApiSvc,
              _replicaSetHardwareSvc,
              _ndsClusterSvc,
              _partitionGroupSvc);
      tool.setToolOptions(
          AWSHostnameBackfillTool.ToolOptions.builder().accountName(ACCOUNT_NAME).build());
      tool.processInstancesInAllAccounts();
      verify(_awsApiSvc, times(1)).findEC2InstancesAndApply(any(), any(), anyInt(), any(), any());
    }
    clearInvocations(_awsApiSvc);

    {
      // If toolOptions does not specify an accountName
      final AWSHostnameBackfillTool tool =
          new AWSHostnameBackfillTool(
              _appSettings,
              _awsAccountSvc,
              _awsApiSvc,
              _replicaSetHardwareSvc,
              _ndsClusterSvc,
              _partitionGroupSvc);
      tool.setToolOptions(AWSHostnameBackfillTool.ToolOptions.builder().build());
      tool.processInstancesInAllAccounts();
      verify(_awsApiSvc, times(1)).findEC2InstancesAndApply(any(), any(), anyInt(), any(), any());
    }
  }

  @Test
  public void test_findInstancesInAllAccounts_instanceChecks() throws IOException {
    final AWSHostnameBackfillTool tool =
        new AWSHostnameBackfillTool(
            _appSettings,
            _awsAccountSvc,
            _awsApiSvc,
            _replicaSetHardwareSvc,
            _ndsClusterSvc,
            _partitionGroupSvc);
    tool.setToolOptions(AWSHostnameBackfillTool.ToolOptions.builder().build());
    tool.processInstancesInAllAccounts();

    verify(_ndsClusterSvc, times(1))
        .getActiveClusterDescriptionsByUniqueIds(eq(List.of(clusterDescription.getUniqueId())));
    verify(_replicaSetHardwareSvc, times(1)).getReplicaSetHardware(any(), any());
    verify(_partitionGroupSvc, times(1)).findAllByNDSGroupId(any());

    final ArgumentCaptor<List<Tag>> captorSource = ArgumentCaptor.forClass(List.class);
    verify(_awsApiSvc, times(2)).tagResources(any(), any(), any(), any(), captorSource.capture());
    final List<List<Tag>> capturedInstances = captorSource.getAllValues();
    assertEquals(
        "atlas-exg720-shard-00-02.eeb5t.mmscloudteam.com",
        capturedInstances.get(0).get(1).getValue());
    assertEquals(
        "super-duper-d-shard-00-search-7ra55g.eeb5t.mmscloudteam.com",
        capturedInstances.get(1).get(1).getValue());

    // Test with dryRun on
    clearInvocations(_awsApiSvc);
    tool.setToolOptions(AWSHostnameBackfillTool.ToolOptions.builder().dryRun(true).build());
    tool.processInstancesInAllAccounts();

    verify(_awsApiSvc, never()).tagResources(any(), any(), any(), any(), captorSource.capture());
  }

  private void initializeMockSvcs() throws IOException {
    clusterDescription = new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    _awsAccountSvc = mock(AWSAccountSvc.class);
    _awsApiSvc = mock(AWSApiSvc.class);
    _ndsClusterSvc = mock(NDSClusterSvc.class);
    _replicaSetHardwareSvc = mock(ReplicaSetHardwareSvc.class);
    _partitionGroupSvc = mock(PartitionGroupSvc.class);

    final Instance instanceWithHostname = mock(Instance.class);
    doReturn("i-instanceWithHostname").when(instanceWithHostname).getInstanceId();
    doReturn(
            List.of(
                new Tag(NDSDefaults.INSTANCE_HOSTNAME_TAG_NAME, "fake-hostname-00-00.mongodb.net")))
        .when(instanceWithHostname)
        .getTags();
    doReturn(new InstanceState().withName(InstanceStateName.Running))
        .when(instanceWithHostname)
        .getState();

    final Instance instanceWithoutUniqueId = mock(Instance.class);
    doReturn("i-instanceWithoutUniqueId").when(instanceWithoutUniqueId).getInstanceId();
    doReturn(List.of()).when(instanceWithoutUniqueId).getTags();
    doReturn(new InstanceState().withName(InstanceStateName.Running))
        .when(instanceWithoutUniqueId)
        .getState();

    final Instance replicaSetInstanceWithUniqueId = mock(Instance.class);
    doReturn("i-replicaSetInstanceWithUniqueId")
        .when(replicaSetInstanceWithUniqueId)
        .getInstanceId();
    doReturn(
            new ArrayList<>(
                List.of(
                    new Tag(
                        NDSDefaults.CLUSTER_UNIQUE_ID_TAG_NAME,
                        clusterDescription.getUniqueId().toString()))))
        .when(replicaSetInstanceWithUniqueId)
        .getTags();
    doReturn(new InstanceState().withName(InstanceStateName.Running))
        .when(replicaSetInstanceWithUniqueId)
        .getState();

    final Instance searchInstanceWithUniqueId = mock(Instance.class);
    doReturn("i-searchInstanceWithUniqueId").when(searchInstanceWithUniqueId).getInstanceId();
    doReturn(
            new ArrayList<>(
                List.of(
                    new Tag(
                        NDSDefaults.GROUP_ID_TAG_NAME,
                        clusterDescription.getGroupId().toString()))))
        .when(searchInstanceWithUniqueId)
        .getTags();
    doReturn(new InstanceState().withName(InstanceStateName.Running))
        .when(searchInstanceWithUniqueId)
        .getState();

    final List<Instance> instances =
        List.of(
            instanceWithHostname,
            instanceWithoutUniqueId,
            replicaSetInstanceWithUniqueId,
            searchInstanceWithUniqueId);
    final DescribeInstancesResult result = mock(DescribeInstancesResult.class);
    final Reservation reservation = mock(Reservation.class);
    doReturn(instances).when(reservation).getInstances();
    doReturn(List.of(reservation)).when(result).getReservations();
    doAnswer(
            invocation -> {
              final Consumer consumer = invocation.getArgument(4);
              consumer.accept(result);
              return null;
            })
        .when(_awsApiSvc)
        .findEC2InstancesAndApply(any(), any(), anyInt(), any(), any());

    doReturn(Map.of(clusterDescription.getUniqueId(), Optional.of(clusterDescription)))
        .when(_ndsClusterSvc)
        .getActiveClusterDescriptionsByUniqueIds(anyCollection());

    final AWSAccount awsAccount = mock(AWSAccount.class);
    doReturn(new ObjectId()).when(awsAccount).getId();
    doReturn(ACCOUNT_NAME).when(awsAccount).getName();
    doReturn(List.of(AWSRegionName.US_EAST_1)).when(awsAccount).getRegionNames();
    doReturn(List.of(awsAccount)).when(_awsAccountSvc).findAllAccounts();

    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);
    final AWSInstanceHardware awsInstanceHardware = mock(AWSInstanceHardware.class);
    doReturn(Optional.of("atlas-exg720-shard-00-02.eeb5t.mmscloudteam.com"))
        .when(awsInstanceHardware)
        .getHostnameForAgents();
    doReturn(CloudProvider.AWS).when(awsInstanceHardware).getCloudProvider();
    doReturn(Optional.of(replicaSetInstanceWithUniqueId.getInstanceId()))
        .when(awsInstanceHardware)
        .getEC2InstanceId();
    doReturn(Stream.of(awsInstanceHardware)).when(replicaSetHardware).getAllHardware();
    doReturn(List.of(replicaSetHardware))
        .when(_replicaSetHardwareSvc)
        .getReplicaSetHardware(any(), any());

    final PartitionGroup partitionGroup = mock(PartitionGroup.class);
    final AWSInstanceHardware awsSearchInstanceHardware = mock(AWSInstanceHardware.class);
    doReturn(Optional.of("super-duper-d-shard-00-search-7ra55g.eeb5t.mmscloudteam.com"))
        .when(awsSearchInstanceHardware)
        .getHostnameForAgents();
    doReturn(CloudProvider.AWS).when(awsSearchInstanceHardware).getCloudProvider();
    doReturn(Optional.of(searchInstanceWithUniqueId.getInstanceId()))
        .when(awsSearchInstanceHardware)
        .getEC2InstanceId();
    final SearchInstance awsSearchInstance = mock(SearchInstance.class);
    doReturn(awsSearchInstanceHardware).when(awsSearchInstance).getInstanceHardware();
    doReturn(List.of(awsSearchInstance)).when(partitionGroup).getInstances();
    doReturn(List.of(partitionGroup)).when(_partitionGroupSvc).findAllByNDSGroupId(any());
  }
}
