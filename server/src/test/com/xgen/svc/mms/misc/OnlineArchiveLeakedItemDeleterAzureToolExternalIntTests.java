package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.azure._private.dao.AzureStorageAccountDao;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureStorageAccount;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.svc.core.BaseSvcTest;
import jakarta.inject.Inject;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Date;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OnlineArchiveLeakedItemDeleterAzureToolExternalIntTests extends BaseSvcTest {

  private static final Logger LOG =
      LoggerFactory.getLogger(OnlineArchiveLeakedItemDeleterAzureToolExternalIntTests.class);

  // create, delete only permissions for account-level SAS
  private static final String ACCOUNT_SAS_PERMISSION_STRING = "cd";

  @Inject private AppSettings _appSettings;
  @Inject private OnlineArchiveDao _onlineArchiveDao;

  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private AzureApiSvc _azureApiSvc;
  @Inject private AzureStorageAccountDao _azureStorageAccountDao;

  private AzureRegionName _azureRegion;
  private AzureStorageAccount _azureAccount;

  private static final String AZURE_STORAGE_ACCOUNT_NAME = "mdbdlzoaeaus2l";
  private static final String AZURE_STORAGE_ACCOUNT_KEY = "azure.oa.dlz.storageAccountKey";
  private static final String SAS_CONTAINER_PERMISSION_STRING = "carwld";

  private static final String AZURE_CONTAINER_NAME_FORMAT = "%s-%s";

  @Before
  public void setup() throws Exception {
    super.setUp();
    _azureRegion = AzureRegionName.US_EAST_2;

    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(new ObjectId(), _azureRegion);
    _azureAccount =
        AzureStorageAccount.builder()
            .azureSubscriptionId(azureContainer.getAzureSubscriptionId())
            .regionName(_azureRegion)
            .storageAccountName(AZURE_STORAGE_ACCOUNT_NAME)
            .storageAccountKey(_appSettings.getStrProp(AZURE_STORAGE_ACCOUNT_KEY))
            .created(new Date())
            .lastUpdated(new Date())
            .build();
    _azureStorageAccountDao.save(_azureAccount);
  }

  @Test
  public void testCreateContainerWithBlobs_deletedArchive() throws ParseException, IOException {

    // archive does not exist in database.
    final ObjectId groupId1 = ObjectId.get();
    final ObjectId archiveId1 = ObjectId.get();

    final String container = String.format(AZURE_CONTAINER_NAME_FORMAT, groupId1, archiveId1);
    final String blobPath1 = String.format("%s/data/%s/1", groupId1, archiveId1);
    final String blobPath2 = String.format("%s/data/%s/2", groupId1, archiveId1);

    final NDSAzureTempCredentialsView accountCredentials =
        _azureApiSvc.generateStorageAccountSasToken(
            _azureAccount.getStorageAccountName(),
            _azureAccount.getUnencryptedStorageAccountKey(),
            ACCOUNT_SAS_PERMISSION_STRING);

    try {
      _azureApiSvc.createBlobContainerIfNotExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);

      NDSAzureTempCredentialsView azureTempCredentialsView =
          _azureApiSvc.generateStorageContainerSasToken(
              _azureAccount.getStorageAccountName(),
              _azureAccount.getUnencryptedStorageAccountKey(),
              container,
              SAS_CONTAINER_PERMISSION_STRING);

      final String archiveFile1 = "archivefile1";
      final InputStream archiveFileStream1 =
          new ByteArrayInputStream(archiveFile1.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath1,
          archiveFileStream1,
          LOG);

      final String archiveFile2 = "archivefile2";
      final InputStream archiveFileStream2 =
          new ByteArrayInputStream(archiveFile2.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath2,
          archiveFileStream2,
          LOG);

      final OnlineArchiveLeakedItemDeleterAzureTool tool =
          new OnlineArchiveLeakedItemDeleterAzureTool(
              null,
              _azureSubscriptionDao,
              _azureStorageAccountDao,
              _azureApiSvc,
              _onlineArchiveDao);
      tool.setOptions(new String[] {});

      final File outputFile = new File("testfile");
      final OnlineArchiveLeakedItemDeleterAzureTool.ContainerMetaData containerMetaData;
      try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
        containerMetaData =
            tool.deleteLeakedBlobsInContainer(
                container, _azureAccount, writer, DateUtils.addDays(new Date(), 1));
      }

      // Verify that the container and blobs are deleted
      assertFalse(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath1,
              LOG));

      assertFalse(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath2,
              LOG));

      assertEquals(2, containerMetaData.deletedCount);
      assertEquals(0, containerMetaData.failedCount);
      assertTrue(containerMetaData.containerDeleted);
    } finally {
      _azureApiSvc.deleteBlobContainerIfExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);
    }
  }

  @Test
  public void testCreateContainerWithBlobs_deletedArchive_maxLeakedItemsToDelete()
      throws ParseException, IOException {

    final ObjectId groupId1 = ObjectId.get();
    final ObjectId archiveId1 = ObjectId.get();

    final String container = String.format(AZURE_CONTAINER_NAME_FORMAT, groupId1, archiveId1);
    final String blobPath1 = String.format("%s/data/%s/1", groupId1, archiveId1);
    final String blobPath2 = String.format("%s/data/%s/2", groupId1, archiveId1);

    final NDSAzureTempCredentialsView accountCredentials =
        _azureApiSvc.generateStorageAccountSasToken(
            _azureAccount.getStorageAccountName(),
            _azureAccount.getUnencryptedStorageAccountKey(),
            ACCOUNT_SAS_PERMISSION_STRING);

    try {
      _azureApiSvc.createBlobContainerIfNotExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);

      NDSAzureTempCredentialsView azureTempCredentialsView =
          _azureApiSvc.generateStorageContainerSasToken(
              _azureAccount.getStorageAccountName(),
              _azureAccount.getUnencryptedStorageAccountKey(),
              container,
              SAS_CONTAINER_PERMISSION_STRING);

      final String archiveFile1 = "archivefile1";
      final InputStream archiveFileStream1 =
          new ByteArrayInputStream(archiveFile1.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath1,
          archiveFileStream1,
          LOG);

      final String archiveFile2 = "archivefile2";
      final InputStream archiveFileStream2 =
          new ByteArrayInputStream(archiveFile2.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath2,
          archiveFileStream2,
          LOG);

      final OnlineArchiveLeakedItemDeleterAzureTool tool =
          new OnlineArchiveLeakedItemDeleterAzureTool(
              null,
              _azureSubscriptionDao,
              _azureStorageAccountDao,
              _azureApiSvc,
              _onlineArchiveDao);
      tool.setOptions(new String[] {"--maxLeakedItemsToDelete", "1"});

      final File outputFile = new File("testfile");
      final OnlineArchiveLeakedItemDeleterAzureTool.ContainerMetaData containerMetaData;
      try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
        containerMetaData =
            tool.deleteLeakedBlobsInContainer(
                container, _azureAccount, writer, DateUtils.addDays(new Date(), 1));
      }

      // Verify that only 1 blob is deleted.
      assertFalse(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath1,
              LOG));

      assertTrue(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath2,
              LOG));

      assertEquals(1, containerMetaData.deletedCount);
      assertEquals(0, containerMetaData.failedCount);
      assertFalse(containerMetaData.containerDeleted);
    } finally {
      _azureApiSvc.deleteBlobContainerIfExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);
    }
  }

  @Test
  public void testCreateContainerWithBlobs_deletedArchive_dryRun()
      throws ParseException, IOException {

    final ObjectId groupId1 = ObjectId.get();
    final ObjectId archiveId1 = ObjectId.get();

    final String container = String.format(AZURE_CONTAINER_NAME_FORMAT, groupId1, archiveId1);
    final String blobPath1 = String.format("%s/data/%s/1", groupId1, archiveId1);
    final String blobPath2 = String.format("%s/data/%s/2", groupId1, archiveId1);

    final NDSAzureTempCredentialsView accountCredentials =
        _azureApiSvc.generateStorageAccountSasToken(
            _azureAccount.getStorageAccountName(),
            _azureAccount.getUnencryptedStorageAccountKey(),
            ACCOUNT_SAS_PERMISSION_STRING);

    try {
      _azureApiSvc.createBlobContainerIfNotExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);

      NDSAzureTempCredentialsView azureTempCredentialsView =
          _azureApiSvc.generateStorageContainerSasToken(
              _azureAccount.getStorageAccountName(),
              _azureAccount.getUnencryptedStorageAccountKey(),
              container,
              SAS_CONTAINER_PERMISSION_STRING);

      final String archiveFile1 = "archivefile1";
      final InputStream archiveFileStream1 =
          new ByteArrayInputStream(archiveFile1.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath1,
          archiveFileStream1,
          LOG);

      final String archiveFile2 = "archivefile2";
      final InputStream archiveFileStream2 =
          new ByteArrayInputStream(archiveFile2.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath2,
          archiveFileStream2,
          LOG);

      final OnlineArchiveLeakedItemDeleterAzureTool tool =
          new OnlineArchiveLeakedItemDeleterAzureTool(
              null,
              _azureSubscriptionDao,
              _azureStorageAccountDao,
              _azureApiSvc,
              _onlineArchiveDao);
      tool.setOptions(new String[] {"--dryRun"});

      final File outputFile = new File("testfile");
      final OnlineArchiveLeakedItemDeleterAzureTool.ContainerMetaData containerMetaData;
      try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
        containerMetaData =
            tool.deleteLeakedBlobsInContainer(
                container, _azureAccount, writer, DateUtils.addDays(new Date(), 1));
      }

      // Verify blobs were not deleted since dry run is set.
      assertTrue(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath1,
              LOG));

      assertTrue(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath2,
              LOG));

      assertEquals(2, containerMetaData.deletedCount);
      assertEquals(0, containerMetaData.failedCount);
      assertFalse(containerMetaData.containerDeleted);

    } finally {
      _azureApiSvc.deleteBlobContainerIfExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);
    }
  }

  @Test
  public void testCreateContainerWithBlobs_deletedArchive_testingThresholdDate()
      throws ParseException, IOException {

    final ObjectId groupId1 = ObjectId.get();
    final ObjectId archiveId1 = ObjectId.get();

    final String container = String.format(AZURE_CONTAINER_NAME_FORMAT, groupId1, archiveId1);
    final String blobPath1 = String.format("%s/data/%s/1", groupId1, archiveId1);
    final String blobPath2 = String.format("%s/data/%s/2", groupId1, archiveId1);

    final NDSAzureTempCredentialsView accountCredentials =
        _azureApiSvc.generateStorageAccountSasToken(
            _azureAccount.getStorageAccountName(),
            _azureAccount.getUnencryptedStorageAccountKey(),
            ACCOUNT_SAS_PERMISSION_STRING);

    try {
      _azureApiSvc.createBlobContainerIfNotExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);

      NDSAzureTempCredentialsView azureTempCredentialsView =
          _azureApiSvc.generateStorageContainerSasToken(
              _azureAccount.getStorageAccountName(),
              _azureAccount.getUnencryptedStorageAccountKey(),
              container,
              SAS_CONTAINER_PERMISSION_STRING);

      final String archiveFile1 = "archivefile1";
      final InputStream archiveFileStream1 =
          new ByteArrayInputStream(archiveFile1.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath1,
          archiveFileStream1,
          LOG);

      final String archiveFile2 = "archivefile2";
      final InputStream archiveFileStream2 =
          new ByteArrayInputStream(archiveFile2.getBytes(StandardCharsets.UTF_8));

      // upload mocked files to DLZ
      _azureApiSvc.uploadBlob(
          container,
          azureTempCredentialsView.getSasUri(),
          azureTempCredentialsView.getSasToken(),
          blobPath2,
          archiveFileStream2,
          LOG);

      final OnlineArchiveLeakedItemDeleterAzureTool tool =
          new OnlineArchiveLeakedItemDeleterAzureTool(
              null,
              _azureSubscriptionDao,
              _azureStorageAccountDao,
              _azureApiSvc,
              _onlineArchiveDao);

      final File outputFile = new File("testfile");
      final OnlineArchiveLeakedItemDeleterAzureTool.ContainerMetaData containerMetaData;
      try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
        containerMetaData =
            tool.deleteLeakedBlobsInContainer(
                container,
                _azureAccount,
                writer,
                OnlineArchiveLeakedItemDeleterAzureTool.THRESHOLD_DATE);
      }

      assertTrue(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath1,
              LOG));

      assertTrue(
          _azureApiSvc.deleteBlob(
              container,
              azureTempCredentialsView.getSasUri(),
              azureTempCredentialsView.getSasToken(),
              blobPath2,
              LOG));

      assertEquals(0, containerMetaData.deletedCount);
      assertEquals(0, containerMetaData.failedCount);
      assertFalse(containerMetaData.containerDeleted);

    } finally {
      _azureApiSvc.deleteBlobContainerIfExists(
          container, accountCredentials.getSasUri(), accountCredentials.getSasToken(), LOG);
    }
  }
}
