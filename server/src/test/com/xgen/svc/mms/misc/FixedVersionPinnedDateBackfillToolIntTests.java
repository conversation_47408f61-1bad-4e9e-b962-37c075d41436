package com.xgen.svc.mms.misc;

import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.VersionAudit.Type.AGENT_VERSION_FIXED;
import static com.xgen.cloud.nds.activity._public.event.audit.VersionAudit.Type.CLUSTER_FCV_FIXED;
import static com.xgen.cloud.nds.activity._public.event.audit.VersionAudit.Type.CLUSTER_VERSION_FIXED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._private.dao.EventDao;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSBaseAudit;
import com.xgen.cloud.nds.activity._public.event.audit.VersionAudit;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class FixedVersionPinnedDateBackfillToolIntTests extends BaseSvcTest {
  private static final Date EXPIRATION_DATE =
      new Date(Instant.parse("3020-01-01T00:00:00Z").toEpochMilli());

  @Inject private FixedVersionPinnedDateBackfillTool _fixedVersionPinnedDateBackfillTool;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private GroupSvc _groupSvc;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private EventDao _eventDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSUISvc _ndsUISVC;

  @Before
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testDryRun_doesNotSetPinnedDate() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());

    { // check for MongoDB versions
      final ClusterDescription generatedClusterDescription =
          generateClusterDescriptionWithFixedMongoDBVersion(pinnedDate, true, false);

      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
          true, CLUSTER_VERSION_FIXED);

      final Optional<ClusterDescription> updatedClusterDescription =
          _clusterDescriptionDao.findByName(
              generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
      assertNull(updatedClusterDescription.get().getFixedMongoDBVersion().get().getPinnedDate());
    }
    { // check for FCV versions
      final ClusterDescription generatedClusterDescription =
          generateClusterDescriptionWithFixedMongoDBFCVVersion(pinnedDate, true, false);

      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
          true, CLUSTER_FCV_FIXED);

      final Optional<ClusterDescription> updatedClusterDescription =
          _clusterDescriptionDao.findByName(
              generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
      assertNull(
          updatedClusterDescription
              .get()
              .getFixedFeatureCompatibilityVersion()
              .get()
              .getPinnedDate());
    }
    { // check for CPU Architecture versions
      final ClusterDescription generatedClusterDescription =
          generateClusterDescriptionWithFixedCPUArchVersion(pinnedDate, true, false);

      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
          true, CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED);

      final Optional<ClusterDescription> updatedClusterDescription =
          _clusterDescriptionDao.findByName(
              generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
      assertNull(updatedClusterDescription.get().getFixedCpuArch().get().getPinnedDate());
    }
    { // check for fixed agent versions
      final ObjectId groupId = new ObjectId();
      final FixedAgentVersion fixedAgentVersion =
          new FixedAgentVersion(
              AgentType.MONGOT, "8.67.5.309", null, "Jenny asked me to.", null, null, null);
      addFixedAgentVersionToGroup(groupId, fixedAgentVersion, pinnedDate, true, false, false);

      assertNull(
          _ndsGroupSvc
              .find(groupId)
              .get()
              .getFixedAgentVersion(AgentType.MONGOT)
              .get()
              .getPinnedDate());
    }
  }

  @Test
  public void testBackfillMongoDBVersions() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedMongoDBVersion(pinnedDate, true, false);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_VERSION_FIXED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedMongoDBVersion().isPresent());
    assertEquals(
        pinnedDate, updatedClusterDescription.get().getFixedMongoDBVersion().get().getPinnedDate());
  }

  @Test
  public void testBackfillMongoDBVersions_doesNotOverwritePinnedDates() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedMongoDBVersion(pinnedDate, false, false);

    final Date preUpdatePinnedDate =
        generatedClusterDescription.getFixedMongoDBVersion().get().getPinnedDate();

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_VERSION_FIXED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedMongoDBVersion().isPresent());
    assertEquals(
        preUpdatePinnedDate,
        updatedClusterDescription.get().getFixedMongoDBVersion().get().getPinnedDate());
  }

  @Test
  public void testBackfillMongoDBVersions_noMatchingEvent() throws Exception {
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedMongoDBVersion(new Date(), true, true);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_VERSION_FIXED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedMongoDBVersion().isPresent());
    assertNull(updatedClusterDescription.get().getFixedMongoDBVersion().get().getPinnedDate());
  }

  @Test
  public void testBackfillMongoDBFCVVersions() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedMongoDBFCVVersion(pinnedDate, true, false);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_FCV_FIXED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedFeatureCompatibilityVersion().isPresent());
    assertEquals(
        pinnedDate,
        updatedClusterDescription
            .get()
            .getFixedFeatureCompatibilityVersion()
            .get()
            .getPinnedDate());
  }

  @Test
  public void testBackfillMongoDBFCVVersions_doesNotOverwritePinnedDates() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedMongoDBFCVVersion(pinnedDate, false, false);

    final Date preUpdatePinnedDate =
        generatedClusterDescription.getFixedFeatureCompatibilityVersion().get().getPinnedDate();

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_FCV_FIXED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedFeatureCompatibilityVersion().isPresent());
    assertEquals(
        preUpdatePinnedDate,
        updatedClusterDescription
            .get()
            .getFixedFeatureCompatibilityVersion()
            .get()
            .getPinnedDate());
  }

  @Test
  public void testBackfillMongoDBFCVVersions_noMatchingEvent() throws Exception {
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedMongoDBFCVVersion(new Date(), true, true);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_FCV_FIXED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedFeatureCompatibilityVersion().isPresent());
    assertNull(
        updatedClusterDescription
            .get()
            .getFixedFeatureCompatibilityVersion()
            .get()
            .getPinnedDate());
  }

  @Test
  public void testBackfillCPUArchVersions() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedCPUArchVersion(pinnedDate, true, false);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedCpuArch().isPresent());
    assertEquals(
        pinnedDate, updatedClusterDescription.get().getFixedCpuArch().get().getPinnedDate());
  }

  @Test
  public void testBackfillCPUArchVersions_doesNotOverwritePinnedDates() throws Exception {
    final Date pinnedDate = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedCPUArchVersion(pinnedDate, false, false);

    final Date preUpdatePinnedDate =
        generatedClusterDescription.getFixedCpuArch().get().getPinnedDate();

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedCpuArch().isPresent());
    assertEquals(
        preUpdatePinnedDate,
        updatedClusterDescription.get().getFixedCpuArch().get().getPinnedDate());
  }

  @Test
  public void getFixedCpuArch_noMatchingEvent() throws Exception {
    ClusterDescription generatedClusterDescription =
        generateClusterDescriptionWithFixedCPUArchVersion(new Date(), true, true);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            generatedClusterDescription.getGroupId(), generatedClusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertTrue(updatedClusterDescription.get().getFixedCpuArch().isPresent());
    assertNull(updatedClusterDescription.get().getFixedCpuArch().get().getPinnedDate());
  }

  @Test
  public void testBackfillFixedAgentVersions() throws Exception {
    final ObjectId groupId = new ObjectId();

    final Date pinnedDate1 = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    final FixedAgentVersion fixedAgentVersion1 =
        new FixedAgentVersion(
            AgentType.MONGOT, "8.67.5.309", null, "Jenny asked me to.", null, null, null);

    final Date pinnedDate2 = new Date(Instant.parse("2023-01-02T00:00:00Z").toEpochMilli());
    final FixedAgentVersion fixedAgentVersion2 =
        new FixedAgentVersion(
            AgentType.AUTOMATION,
            "10.31.0",
            "https://boo.hoo",
            "Ghost in the machine.",
            null,
            null,
            null);

    addFixedAgentVersionToGroup(groupId, fixedAgentVersion1, pinnedDate1, true, false, false);
    addFixedAgentVersionToGroup(groupId, fixedAgentVersion2, pinnedDate2, true, false, false);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, AGENT_VERSION_FIXED);

    final Optional<NDSGroup> groupOpt = _ndsGroupSvc.find(groupId);
    assertTrue(groupOpt.isPresent());
    final NDSGroup updatedGroup = groupOpt.get();

    final FixedAgentVersion updatedFixedAgentVersion1 =
        updatedGroup.getFixedAgentVersion(fixedAgentVersion1.getAgentType()).get();
    assertEquals(pinnedDate1, updatedFixedAgentVersion1.getPinnedDate());
    final FixedAgentVersion updatedFixedAgentVersion2 =
        updatedGroup.getFixedAgentVersion(fixedAgentVersion2.getAgentType()).get();
    assertEquals(pinnedDate2, updatedFixedAgentVersion2.getPinnedDate());
  }

  @Test
  public void testBackfillFixedAgentVersions_doesNotOverwritePinnedDates() throws Exception {
    final ObjectId groupId = new ObjectId();

    final Date pinnedDate1 = new Date(Instant.parse("2023-01-01T00:00:00Z").toEpochMilli());
    final FixedAgentVersion fixedAgentVersion1 =
        new FixedAgentVersion(
            AgentType.MONGOT, "8.67.5.309", null, "Jenny asked me to.", null, null, null);

    final Date pinnedDate2 = new Date(Instant.parse("2023-01-02T00:00:00Z").toEpochMilli());
    final FixedAgentVersion fixedAgentVersion2 =
        new FixedAgentVersion(
            AgentType.AUTOMATION,
            "10.31.0",
            "https://boo.hoo",
            "Ghost in the machine.",
            null,
            null,
            null);

    addFixedAgentVersionToGroup(groupId, fixedAgentVersion1, pinnedDate1, false, false, false);
    addFixedAgentVersionToGroup(groupId, fixedAgentVersion2, pinnedDate2, false, false, false);

    final NDSGroup preUpdateGroup = _ndsGroupSvc.find(groupId).get();
    final Date originalPinnedDate1 =
        preUpdateGroup
            .getFixedAgentVersion(fixedAgentVersion1.getAgentType())
            .get()
            .getPinnedDate();
    final Date originalPinnedDate2 =
        preUpdateGroup
            .getFixedAgentVersion(fixedAgentVersion2.getAgentType())
            .get()
            .getPinnedDate();

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, AGENT_VERSION_FIXED);

    final Optional<NDSGroup> groupOpt = _ndsGroupSvc.find(groupId);
    assertTrue(groupOpt.isPresent());
    final NDSGroup updatedGroup = groupOpt.get();

    final FixedAgentVersion updatedFixedAgentVersion1 =
        updatedGroup.getFixedAgentVersion(fixedAgentVersion1.getAgentType()).get();
    assertEquals(originalPinnedDate1, updatedFixedAgentVersion1.getPinnedDate());

    final FixedAgentVersion updatedFixedAgentVersion2 =
        updatedGroup.getFixedAgentVersion(fixedAgentVersion2.getAgentType()).get();
    assertEquals(originalPinnedDate2, updatedFixedAgentVersion2.getPinnedDate());
  }

  @Test
  public void testBackfillFixedAgentVersions_noMatchingEvent() throws Exception {
    final ObjectId groupId = new ObjectId();

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(
            AgentType.MONGOT, "8.67.5.309", null, "Jenny asked me to.", null, null, null);

    addFixedAgentVersionToGroup(groupId, fixedAgentVersion, new Date(), true, true, false);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, AGENT_VERSION_FIXED);

    final Optional<NDSGroup> groupOpt = _ndsGroupSvc.find(groupId);
    assertTrue(groupOpt.isPresent());
    final NDSGroup updatedGroup = groupOpt.get();

    final FixedAgentVersion updatedFixedAgentVersion =
        updatedGroup.getFixedAgentVersion(fixedAgentVersion.getAgentType()).get();
    assertNull(updatedFixedAgentVersion.getPinnedDate());
  }

  @Test
  public void testBackfillFixedAgentVersions_noEventFoundByQuery() throws Exception {
    final ObjectId groupId = new ObjectId();

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(
            AgentType.MONGOT, "8.67.5.309", null, "Jenny asked me to.", null, null, null);

    addFixedAgentVersionToGroup(groupId, fixedAgentVersion, new Date(), true, false, true);

    _fixedVersionPinnedDateBackfillTool.updatePinnedDateForFixedVersionType(
        false, AGENT_VERSION_FIXED);

    final Optional<NDSGroup> groupOpt = _ndsGroupSvc.find(groupId);
    assertTrue(groupOpt.isPresent());
    final NDSGroup updatedGroup = groupOpt.get();

    final FixedAgentVersion updatedFixedAgentVersion =
        updatedGroup.getFixedAgentVersion(fixedAgentVersion.getAgentType()).get();
    assertNull(updatedFixedAgentVersion.getPinnedDate());
  }

  private ClusterDescription generateClusterDescriptionWithFixedMongoDBVersion(
      final Date pPinnedDate,
      final boolean pSetVersionDateToNull,
      final boolean pChangeEventTargetVersion)
      throws SvcException {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);

    _ndsClusterSvc.fixClusterMongoDBVersion(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        VersionUtils.FIVE_ZERO_ZERO.getVersion(),
        "",
        AuditInfoHelpers.fromSystem());

    // set the date on the event to pPinnedDate
    final BasicDBObject query =
        new BasicDBObject()
            .append(Event.GROUP_ID_FIELD, clusterDescription.getGroupId())
            .append("clusterName", clusterDescription.getName())
            .append(Event.EVENT_TYPE_FIELD, CLUSTER_VERSION_FIXED);
    final BasicDBObject update =
        new BasicDBObject()
            .append(BaseDao.SET, new BasicDBObject(Event.CREATED_AT_FIELD, pPinnedDate));

    _eventDao.updateOne(query, update);

    if (pChangeEventTargetVersion) {
      final BasicDBObject updateTargetVersion =
          new BasicDBObject()
              .append(BaseDao.SET, new BasicDBObject(VersionAudit.TARGET_VERSION_FIELD, "foo"));
      _eventDao.updateOne(query, updateTargetVersion);
    }

    // change pinned date on fixed version to null
    if (pSetVersionDateToNull) {
      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForCluster(
          clusterDescription.getGroupId(),
          clusterDescription.getName(),
          FieldDefs.FIXED_MONGODB_VERSION,
          null);
    }

    // ensure cluster description is saved properly (with pinned date set to null)
    final Optional<ClusterDescription> updatedClusterDescriptionOpt =
        _clusterDescriptionDao.findByName(
            clusterDescription.getGroupId(), clusterDescription.getName());
    assertTrue(updatedClusterDescriptionOpt.isPresent());
    final ClusterDescription updatedClusterDescription = updatedClusterDescriptionOpt.get();
    assertTrue(updatedClusterDescription.getFixedMongoDBVersion().isPresent());

    if (pSetVersionDateToNull) {
      assertNull(updatedClusterDescription.getFixedMongoDBVersion().get().getPinnedDate());
    } else {
      assertNotNull(updatedClusterDescription.getFixedMongoDBVersion().get().getPinnedDate());
    }

    return updatedClusterDescription;
  }

  private ClusterDescription generateClusterDescriptionWithFixedMongoDBFCVVersion(
      final Date pPinnedDate,
      final boolean pSetVersionDateToNull,
      final boolean pChangeEventTargetVersion)
      throws SvcException {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);

    final ObjectId pGroupId = clusterDescription.getGroupId();
    final String pClusterName = clusterDescription.getName();
    final String pVersion = clusterDescription.getMongoDBMajorVersion();
    _ndsClusterSvc.fixClusterFeatureCompatibilityVersion(
        pGroupId,
        pClusterName,
        pVersion,
        "",
        AuditInfoHelpers.fromSystem(),
        EXPIRATION_DATE,
        new Date(),
        null);

    // set the date on the event to pPinnedDate
    final BasicDBObject query =
        new BasicDBObject()
            .append(Event.GROUP_ID_FIELD, clusterDescription.getGroupId())
            .append("clusterName", clusterDescription.getName())
            .append(Event.EVENT_TYPE_FIELD, CLUSTER_FCV_FIXED);
    final BasicDBObject update =
        new BasicDBObject()
            .append(BaseDao.SET, new BasicDBObject(Event.CREATED_AT_FIELD, pPinnedDate));

    _eventDao.updateOne(query, update);

    if (pChangeEventTargetVersion) {
      final BasicDBObject updateTargetVersion =
          new BasicDBObject()
              .append(BaseDao.SET, new BasicDBObject(VersionAudit.TARGET_VERSION_FIELD, "foo"));
      _eventDao.updateOne(query, updateTargetVersion);
    }

    // change pinned date on fixed version to null
    if (pSetVersionDateToNull) {
      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForCluster(
          clusterDescription.getGroupId(),
          clusterDescription.getName(),
          FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION,
          null);
    }

    // ensure cluster description is saved properly
    final Optional<ClusterDescription> updatedClusterDescriptionOpt =
        _clusterDescriptionDao.findByName(
            clusterDescription.getGroupId(), clusterDescription.getName());
    assertTrue(updatedClusterDescriptionOpt.isPresent());
    final ClusterDescription updatedClusterDescription = updatedClusterDescriptionOpt.get();
    assertTrue(updatedClusterDescription.getFixedFeatureCompatibilityVersion().isPresent());

    if (pSetVersionDateToNull) {
      assertNull(
          updatedClusterDescription.getFixedFeatureCompatibilityVersion().get().getPinnedDate());
    } else {
      assertNotNull(
          updatedClusterDescription.getFixedFeatureCompatibilityVersion().get().getPinnedDate());
    }

    return updatedClusterDescription;
  }

  private ClusterDescription generateClusterDescriptionWithFixedCPUArchVersion(
      final Date pPinnedDate,
      final boolean pSetFixedVersionDateToNull,
      final boolean pChangeEventTargetVersion)
      throws SvcException {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);

    final Group group = new Group();
    group.setId(clusterDescription.getGroupId());
    _groupSvc.save(group);

    _ndsClusterSvc.fixCpuArchForCluster(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        CpuArchitecture.ARM64,
        AuditInfoHelpers.fromSystem(),
        "",
        EXPIRATION_DATE);

    // set the date on the event to pPinnedDate
    final BasicDBObject query =
        new BasicDBObject()
            .append(Event.GROUP_ID_FIELD, clusterDescription.getGroupId())
            .append("clusterName", clusterDescription.getName())
            .append(Event.EVENT_TYPE_FIELD, CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED);
    final BasicDBObject update =
        new BasicDBObject()
            .append(BaseDao.SET, new BasicDBObject(Event.CREATED_AT_FIELD, pPinnedDate));

    _eventDao.updateOne(query, update);

    if (pChangeEventTargetVersion) {
      final List<AuditDescription> newAuditDescription =
          List.of(
              new AuditDescription(
                  "The Cluster's Preferred CPU Architecture",
                  String.format("Pinned to %s", CpuArchitecture.X86_64)));

      final BasicDBObject updateTargetVersion =
          new BasicDBObject()
              .append(
                  BaseDao.SET,
                  new BasicDBObject(
                      NDSBaseAudit.FieldDefs.AUDIT_DESCRIPTION_FIELD, newAuditDescription));
      _eventDao.updateOne(query, updateTargetVersion);
    }

    // change pinned date on fixed version to null
    if (pSetFixedVersionDateToNull) {
      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForCluster(
          clusterDescription.getGroupId(),
          clusterDescription.getName(),
          FieldDefs.FIXED_CPU_ARCH,
          null);
    }

    // ensure cluster description is saved properly
    final Optional<ClusterDescription> updatedClusterDescriptionOpt =
        _clusterDescriptionDao.findByName(
            clusterDescription.getGroupId(), clusterDescription.getName());
    assertTrue(updatedClusterDescriptionOpt.isPresent());
    final ClusterDescription updatedClusterDescription = updatedClusterDescriptionOpt.get();
    assertTrue(updatedClusterDescription.getFixedCpuArch().isPresent());

    final FixedVersion fixedVersion = updatedClusterDescription.getFixedCpuArch().get();

    if (pSetFixedVersionDateToNull) {
      assertNull(fixedVersion.getPinnedDate());
    } else {
      assertNotNull(fixedVersion.getPinnedDate());
    }

    return updatedClusterDescription;
  }

  private void addFixedAgentVersionToGroup(
      final ObjectId pGroupId,
      final FixedAgentVersion pFixedAgentVersion,
      final Date pPinnedDate,
      final boolean pSetFixedVersionDateToNull,
      final boolean pChangeEventTargetVersion,
      final boolean pChangeEventAgentVersion)
      throws SvcException {
    final NDSGroup group = _ndsGroupSvc.ensureGroup(pGroupId);

    _ndsUISVC.fixAgentVersion(
        group.getGroupId(),
        pFixedAgentVersion.getAgentType(),
        pFixedAgentVersion.getVersion(),
        "foo",
        "foo",
        EXPIRATION_DATE,
        AuditInfoHelpers.fromSystem());

    final BasicDBObject query =
        new BasicDBObject()
            .append(Event.GROUP_ID_FIELD, pGroupId)
            .append(Event.EVENT_TYPE_FIELD, AGENT_VERSION_FIXED)
            .append(VersionAudit.AGENT_TYPE_FIELD, pFixedAgentVersion.getAgentType());
    final BasicDBObject update =
        new BasicDBObject()
            .append(BaseDao.SET, new BasicDBObject(Event.CREATED_AT_FIELD, pPinnedDate));

    _eventDao.updateOne(query, update);

    if (pChangeEventTargetVersion) {
      final BasicDBObject updateTargetVersion =
          new BasicDBObject()
              .append(BaseDao.SET, new BasicDBObject(VersionAudit.TARGET_VERSION_FIELD, "foo"));
      _eventDao.updateOne(query, updateTargetVersion);
    }

    if (pChangeEventAgentVersion) {
      final BasicDBObject updateAgentVersion =
          new BasicDBObject()
              .append(BaseDao.SET, new BasicDBObject(VersionAudit.AGENT_TYPE_FIELD, "foo"));
      _eventDao.updateOne(query, updateAgentVersion);
    }

    // set the pinned date to null
    if (pSetFixedVersionDateToNull) {
      _fixedVersionPinnedDateBackfillTool.updatePinnedDateForGroup(
          group.getGroupId(), pFixedAgentVersion.getAgentType(), null);
    }

    // ensure pinned date is set to null
    final NDSGroup updatedGroup = _ndsGroupSvc.find(group.getGroupId()).get();
    Optional<FixedAgentVersion> finalFixedAgentVersion =
        updatedGroup.getFixedAgentVersion(pFixedAgentVersion.getAgentType());
    assertTrue(finalFixedAgentVersion.isPresent());
    if (pSetFixedVersionDateToNull) {
      assertNull(finalFixedAgentVersion.get().getPinnedDate());
    } else {
      assertNotNull(finalFixedAgentVersion.get().getPinnedDate());
    }
  }
}
