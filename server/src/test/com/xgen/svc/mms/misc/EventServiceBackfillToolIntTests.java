package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.activity._private.dao.EventDao;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.model.event.HostEvent.Type;
import com.xgen.cloud.eventcommitter._private.dao.EventTypeMigrationStatusDao;
import com.xgen.cloud.eventcommitter._public.model.EventTypeMigrationStatus;
import com.xgen.svc.core.BaseSvcTest;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class EventServiceBackfillToolIntTests extends BaseSvcTest {
  @Inject EventServiceBackfillTool eventServiceBackfillTool;
  @Inject EventTypeMigrationStatusDao eventTypeMigrationStatusDao;
  @Inject EventDao oldEventDao;
  @Inject com.xgen.cloud.services.event._private.dao.EventDao newEventDao;

  final Integer NUM_TEST_EVENTS = 100;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    oldEventDao.insert(generateTestHostEvents());
    eventServiceBackfillTool.initializeOldEventMorphiaDs(30);
  }

  @Test
  public void testDryRun_doesNotWriteEvents() throws Exception {
    eventServiceBackfillTool.migrateEventType(Type.HOST_RESTARTED, false, 10, 20, 0, 30);

    assertEquals(newEventDao.getCollection().estimatedDocumentCount(), 0L);
  }

  @Test
  public void testMigrateEventType() throws Exception {
    eventServiceBackfillTool.migrateEventType(Type.HOST_RESTARTED, true, 10, 20, 0, 30);

    // Verify that all documents were migrated
    assertEquals(NUM_TEST_EVENTS.intValue(), newEventDao.getCollection().estimatedDocumentCount());
    for (int i = 0; i < NUM_TEST_EVENTS; i++) {
      assertTrue(newEventDao.find(oid(i)).isPresent());
    }

    // Verify that all the lastMigrated document includes the created date of the last migrated
    // event type
    final Date lastEventCreatedDate =
        Date.from(newEventDao.find(oid(NUM_TEST_EVENTS - 1)).get().getCreatedDate());

    final EventTypeMigrationStatus status =
        eventTypeMigrationStatusDao.findByEventType(Type.HOST_RESTARTED.toString()).get();

    assertEquals(Type.HOST_RESTARTED.toString(), status.getEventType());
    assertEquals(lastEventCreatedDate, status.getLastMigratedCreationDate());
    assertEquals((long) NUM_TEST_EVENTS, status.getNumberOfEventsMigrated());
  }

  @Test
  public void testMigrateEventType_ignoresDuplicateEvents() throws Exception {
    eventServiceBackfillTool.migrateEventType(Type.HOST_RESTARTED, true, 10, 20, 0, 30);

    assertEquals(newEventDao.getCollection().estimatedDocumentCount(), (long) NUM_TEST_EVENTS);

    // Reset the last run date to allow for duplicate writes and re-run migration
    eventTypeMigrationStatusDao.updateLastMigratedCreationDate(
        Type.HOST_RESTARTED.toString(), new Date(0), 0);
    eventServiceBackfillTool.migrateEventType(Type.HOST_RESTARTED, true, 10, 20, 0, 30);

    assertEquals(newEventDao.getCollection().estimatedDocumentCount(), (long) NUM_TEST_EVENTS);

    for (int i = 0; i < NUM_TEST_EVENTS; i++) {
      assertTrue(newEventDao.find(oid(i)).isPresent());
    }

    final EventTypeMigrationStatus status =
        eventTypeMigrationStatusDao.findByEventType(Type.HOST_RESTARTED.toString()).get();
    assertEquals((long) NUM_TEST_EVENTS, status.getNumberOfEventsMigrated());
  }

  private List<Event> generateTestHostEvents() {
    List<Event> testEvents = new ArrayList<>();

    for (int i = 0; i < NUM_TEST_EVENTS; i++) {
      final HostEvent.Builder builder = new HostEvent.Builder(HostEvent.Type.HOST_RESTARTED, null);
      builder.id(oid(i));
      builder.createdAt(new Date(i * 1000));
      builder.groupId(ObjectId.get());
      builder.hostId(ObjectId.get().toString());
      builder.hostnameAndPort("hostname::port");
      builder.userAlias("userAlias");
      builder.replicaSetId("replicaSetId");
      builder.hostTypeIds(List.of(0));
      final HostEvent event = builder.build();

      testEvents.add(event);
    }

    return testEvents;
  }
}
