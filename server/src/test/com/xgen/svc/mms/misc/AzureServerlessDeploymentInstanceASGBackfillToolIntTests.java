package com.xgen.svc.mms.misc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.azure.resourcemanager.network.fluent.models.NetworkInterfaceInner;
import com.azure.resourcemanager.network.models.ApplicationSecurityGroup;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._private.dao.AzureInstanceHardwareDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware.AzurePublicIPSKUType;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.AzureEnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class AzureServerlessDeploymentInstanceASGBackfillToolIntTests extends BaseSvcTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private AzureInstanceHardwareDao _azureInstanceHardwareDao;
  @Inject private AppSettings _appSettings;

  private AzureApiSvc _azureApiSvc;

  private AzureServerlessDeploymentInstanceASGBackfillTool _tool;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _azureApiSvc = mock(AzureApiSvc.class);

    _tool =
        new AzureServerlessDeploymentInstanceASGBackfillTool(
            _ndsGroupDao,
            _azureApiSvc,
            _serverlessLoadBalancingDeploymentDao,
            _replicaSetHardwareDao);
  }

  @Test
  public void testRunTool_nonMTMAzureGroup() throws InterruptedException {
    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer(AzureRegionName.US_EAST_2));

    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.addCloudContainer(groupId, azureContainer);

    _tool.run(groupId);

    verify(_azureApiSvc, times(0))
        .findApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());
    verify(_azureApiSvc, times(0))
        .createApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());
    verify(_azureApiSvc, times(0))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());
  }

  @Test
  public void testRunTool_nonAzureServerlessMTMGroups() throws InterruptedException {
    final ServerlessLoadBalancingDeployment awsDeployment =
        ServerlessDeploymentModelTestFactory.getAWSServerlessLoadBalancingDeployment(
            AWSRegionName.US_EAST_1);
    final ObjectId awsServerlessMTMGroupId = awsDeployment.getGroupId();

    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer(AWSRegionName.US_EAST_1));

    _ndsGroupSvc.create(
        awsServerlessMTMGroupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        awsServerlessMTMGroupId,
        EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(awsServerlessMTMGroupId);
    _ndsGroupDao.addCloudContainer(awsServerlessMTMGroupId, awsContainer);

    _tool.run(awsServerlessMTMGroupId);

    verify(_azureApiSvc, times(0))
        .findApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());
    verify(_azureApiSvc, times(0))
        .createApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());
    verify(_azureApiSvc, times(0))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());
  }

  @Test
  public void testRunTool_unprovisionedServerlessDeployment() throws InterruptedException {
    final ServerlessLoadBalancingDeployment unprovisionedDeployment =
        ServerlessDeploymentModelTestFactory.getUnProvisionedAzureServerlessLoadBalancingDeployment(
            AzureRegionName.US_EAST_2);
    final ObjectId serverlessMTMGroupId = unprovisionedDeployment.getGroupId();

    _ndsGroupSvc.create(
        serverlessMTMGroupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        serverlessMTMGroupId,
        EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(serverlessMTMGroupId);

    _tool.run(serverlessMTMGroupId);

    verify(_azureApiSvc, times(0))
        .findApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());
    verify(_azureApiSvc, times(0))
        .createApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());
    verify(_azureApiSvc, times(0))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());
  }

  @Test
  public void testRunTool_azureServerlessMTMGroup_existingApplicationSecurityGroup()
      throws InterruptedException {
    final ObjectId azureServerlessMTMGroupId = new ObjectId();

    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer(AzureRegionName.US_EAST_2));

    _ndsGroupSvc.create(
        azureServerlessMTMGroupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        azureServerlessMTMGroupId,
        EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(azureServerlessMTMGroupId);
    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(azureServerlessMTMGroupId, azureContainer);

    final ServerlessLoadBalancingDeployment azureDeployment =
        setupAzureDeployment(azureServerlessMTMGroupId, containerId);

    final String networkInterfaceName = "interfaceName";
    setupAzureMTMReplicaSet(azureServerlessMTMGroupId, containerId, networkInterfaceName);

    final ApplicationSecurityGroup applicationSecurityGroup = mock(ApplicationSecurityGroup.class);
    doReturn(applicationSecurityGroup)
        .when(_azureApiSvc)
        .findApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());

    doReturn(mock(NetworkInterfaceInner.class))
        .when(_azureApiSvc)
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());

    _tool.run(null);

    verify(_azureApiSvc, times(0))
        .createApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());

    verify(_azureApiSvc, times(1))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());

    verify(_azureApiSvc, times(3))
        .removeExistingApplicationSecurityGroupFromNetworkInterface(
            any(), anyString(), any(), anyString(), any());

    verify(_azureApiSvc, times(1))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), eq(networkInterfaceName), any());

    azureDeployment.getEnvoyInstances().stream()
        .map(AzureEnvoyInstance.class::cast)
        .forEach(
            instance ->
                verify(_azureApiSvc, times(1))
                    .removeExistingApplicationSecurityGroupFromNetworkInterface(
                        any(), anyString(), any(), eq(instance.getNetworkInterfaceName()), any()));
  }

  @Test
  public void testRunTool_azureServerlessMTMGroup_noExistingApplicationSecurityGroup()
      throws InterruptedException {
    final ObjectId azureServerlessMTMGroupId = new ObjectId();

    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer(AzureRegionName.US_EAST_2));

    _ndsGroupSvc.create(
        azureServerlessMTMGroupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        azureServerlessMTMGroupId,
        EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(azureServerlessMTMGroupId);
    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(azureServerlessMTMGroupId, azureContainer);

    final ServerlessLoadBalancingDeployment azureDeployment =
        setupAzureDeployment(azureServerlessMTMGroupId, containerId);

    final String networkInterfaceName = "interfaceName";
    setupAzureMTMReplicaSet(azureServerlessMTMGroupId, containerId, networkInterfaceName);

    doReturn(null)
        .when(_azureApiSvc)
        .findApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());

    final ApplicationSecurityGroup applicationSecurityGroup = mock(ApplicationSecurityGroup.class);
    doReturn(applicationSecurityGroup)
        .when(_azureApiSvc)
        .createApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());

    doReturn(mock(NetworkInterfaceInner.class))
        .when(_azureApiSvc)
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());

    _tool.run(null);

    verify(_azureApiSvc, times(1))
        .createApplicationSecurityGroup(any(), anyString(), any(), anyString(), any());

    verify(_azureApiSvc, times(1))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), anyString(), any());

    verify(_azureApiSvc, times(1))
        .attachApplicationSecurityGroupToNetworkInterface(
            any(), anyString(), any(), eq(networkInterfaceName), any());

    azureDeployment.getEnvoyInstances().stream()
        .map(AzureEnvoyInstance.class::cast)
        .forEach(
            instance ->
                verify(_azureApiSvc, times(1))
                    .removeExistingApplicationSecurityGroupFromNetworkInterface(
                        any(), anyString(), any(), eq(instance.getNetworkInterfaceName()), any()));
  }

  private void setupAzureMTMReplicaSet(
      final ObjectId pGroupId, final ObjectId pContainerId, final String pNetworkInterfaceName) {

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("cluster0", pGroupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("atlas-1234", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AZURE, false, 0);
    _azureInstanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        AzureNDSInstanceSize.M10.name(),
        AzureInstanceFamily.STANDARD_DSV2.name(),
        CloudProviderRegistry.getByCloudProvider(CloudProvider.AZURE)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, AzureInstanceFamily.STANDARD_DSV2),
        AzureDiskType.P2.name(),
        AzureDiskType.P2.name(),
        null,
        null,
        null,
        null,
        null,
        "vm1234",
        "osDisk1234",
        "dataDisk.1234",
        "dataDeviceName1234",
        "publicIp1234",
        pNetworkInterfaceName,
        100,
        null,
        null,
        new Hostnames(),
        HostnameScheme.INTERNAL,
        "********",
        AzurePublicIPSKUType.STANDARD,
        pContainerId,
        null,
        "GTS");
  }

  private ServerlessLoadBalancingDeployment setupAzureDeployment(
      final ObjectId pGroupId, final ObjectId pContainerId) {
    final ServerlessLoadBalancingDeployment azureDeployment =
        ServerlessDeploymentModelTestFactory.getAzureServerlessLoadBalancingDeployment(
            AzureRegionName.US_EAST_2, pGroupId, pContainerId);

    _serverlessLoadBalancingDeploymentDao.save(azureDeployment);

    return azureDeployment;
  }
}
