package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.free._private.dao.NDSSharedMTMProfileDao;
import com.xgen.cloud.nds.free._public.model.NDSSharedMTMProfile;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class AtlasSharedTierVersionUpgradeToolIntTests extends BaseSvcTest {

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSSharedMTMProfileDao _ndsSharedMTMProfileDao;
  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  private final List<NDSSharedMTMProfile> _sharedMTMProfiles =
      NDSModelTestFactory.getNDSMTMProfilesForTest().stream().limit(5).toList();

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _ndsGroupSvc = mock(NDSGroupSvc.class);
    _clusterDescriptionDao = mock(ClusterDescriptionDao.class);
    _ndsSharedMTMProfileDao.insertManyMajority(_sharedMTMProfiles);
  }

  private static class CommandLineOptionsBuilder {
    private Optional<String> _newFullVersion;
    private Optional<String> _originalMajorVersion;

    public CommandLineOptionsBuilder(
        final String pNewFullVersion, final String pOriginalMajorVersion) {
      _newFullVersion = Optional.of(pNewFullVersion);
      _originalMajorVersion = Optional.of(pOriginalMajorVersion);
    }

    public String[] build() {
      final List<String> optionsList = new ArrayList<>();
      if (_newFullVersion.isPresent()) {
        optionsList.add("--newFullVersion");
        optionsList.add(_newFullVersion.get());
      }

      if (_originalMajorVersion.isPresent()) {
        optionsList.add("--originalMajorVersion");
        optionsList.add(_originalMajorVersion.get());
      }
      return optionsList.toArray(new String[0]);
    }
  }

  private Set<ObjectId> createGroupIds(final int count) {
    final Set<ObjectId> groupIds = new HashSet<>();
    for (int i = 0; i < count; i++) {
      groupIds.add(new ObjectId());
    }
    return groupIds;
  }

  private List<SharedMTMCluster> createSharedMTMClusters(
      Set<ObjectId> pGroupIds, String pMajorVersion) {

    return pGroupIds.stream()
        .map(
            groupId -> {
              final SharedMTMCluster sharedMTMCluster =
                  new SharedMTMCluster(
                      NDSModelTestFactory.getSharedMTMCluster(
                          new ObjectId(), "test_" + groupId, pMajorVersion));
              _mtmClusterDao.insert(sharedMTMCluster);
              return sharedMTMCluster;
            })
        .collect(Collectors.toList());
  }

  private List<ClusterDescription> createClusterDescription(
      Set<ObjectId> pGroupIds, CloudProvider pCloudProvider) {
    return pGroupIds.stream()
        .map(
            groupId -> {
              final ClusterDescription clusterDescription =
                  NDSModelTestFactory.getClusterDescription(groupId, pCloudProvider);
              _clusterDescriptionDao.save(clusterDescription);
              return clusterDescription;
            })
        .collect(Collectors.toList());
  }

  @Test
  public void testVersionUpgrade_InvalidInput() {
    // assert invalid input for newFullVersion
    try {
      AtlasSharedTierVersionUpgradeTool.main(new CommandLineOptionsBuilder("a", "1").build());
      fail("Should throw illegal argument exception for invalid versions");
    } catch (final IllegalArgumentException e) {
      assertTrue(
          "should throw invalid input for newFullVersion",
          e.getMessage().contains("Invalid input for newFullVersion"));
    }

    // assert invalid input for originalMajorVersion
    try {
      AtlasSharedTierVersionUpgradeTool.main(
          new CommandLineOptionsBuilder("7.0.5", "test").build());
      fail("Should throw illegal argument exception for invalid versions");
    } catch (final IllegalArgumentException e) {
      assertTrue(
          "should throw invalid input for originalMajorVersion",
          e.getMessage().contains("Invalid input for originalMajorVersion"));
    }
  }

  @Test
  public void testVersionUpgrade() {
    final VersionUtils.Version originalVersion = VersionUtils.parse("5.0");
    final VersionUtils.Version newMajorVersion =
        VersionUtils.parse(NDSModelTestFactory.TEST_FREE_MONGODB_MAJOR_VERSION);
    final VersionUtils.Version newFullVersion =
        VersionUtils.parse(NDSModelTestFactory.TEST_FREE_MONGODB_VERSION);

    final Set<ObjectId> groupIds = createGroupIds(3);

    createSharedMTMClusters(groupIds, originalVersion.getVersion());
    createClusterDescription(groupIds, CloudProvider.FREE);

    doReturn(new ArrayList<>(groupIds)).when(_ndsGroupSvc).getFreeMTMGroupIds();

    // setup tool
    final AtlasSharedTierVersionUpgradeTool tool =
        new AtlasSharedTierVersionUpgradeTool(
            _ndsGroupSvc,
            _ndsSharedMTMProfileDao,
            _mtmClusterDao,
            _ndsGroupDao,
            _clusterDescriptionDao);

    // run the tool
    tool.updateAtlasSharedTierVersion(originalVersion, newMajorVersion, newFullVersion);

    // assert shared mtm profiles version upgrade
    assertTrue(
        _ndsSharedMTMProfileDao.findMTMProfiles().stream()
            .allMatch(p -> p.getMongoDBMajorVersion().equals(newMajorVersion.getVersion())));

    // assert mtm cluster version upgrade
    groupIds.forEach(
        groupId -> {
          _mtmClusterDao
              .findClustersByGroupId(groupId)
              .forEach(
                  mtmCluster ->
                      assertEquals(
                          mtmCluster.getMongoDBMajorVersion(),
                          NDSModelTestFactory.TEST_FREE_MONGODB_MAJOR_VERSION));
        });

    // assert cluster description version update
    groupIds.forEach(
        groupId -> {
          _clusterDescriptionDao
              .findByGroup(groupId)
              .forEach(
                  cd -> {
                    assertEquals(
                        cd.getMongoDBMajorVersion(),
                        NDSModelTestFactory.TEST_FREE_MONGODB_MAJOR_VERSION);
                    assertEquals(
                        cd.getMongoDBVersion().getVersion(),
                        NDSModelTestFactory.TEST_FREE_MONGODB_VERSION);
                  });
        });
  }
}
