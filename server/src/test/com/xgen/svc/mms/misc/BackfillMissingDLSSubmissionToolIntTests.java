package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.common.util._public.util.classpath.ClasspathUtils;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.onlinearchive._private.dao.BackfillMissingDLSJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveRunDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveSubmittedUploadFilesJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.OnlineArchiveV3MigrationDao;
import com.xgen.cloud.nds.onlinearchive._public.model.ArchiveRunStats;
import com.xgen.cloud.nds.onlinearchive._public.model.BackfillMissingDLSJob;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.State;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun.ArchiveRunState;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun.Builder;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveSubmittedUploadFilesJob;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.BackfillStats;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Phase;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Source;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Target;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveBackfillMissingDLSJobHandler;
import jakarta.inject.Inject;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.apache.commons.io.FileUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.supercsv.io.CsvListReader;
import org.supercsv.prefs.CsvPreference;

public class BackfillMissingDLSSubmissionToolIntTests extends BaseSvcTest {

  @Inject private OnlineArchiveRunDao _onlineArchiveRunDao;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private OnlineArchiveV3MigrationDao _onlineArchiveV3MigrationDao;
  @Inject private BackfillMissingDLSJobDao _backfillMissingDLSJobDao;
  @Inject private OnlineArchiveSubmittedUploadFilesJobDao _onlineArchiveSubmittedUploadFilesJobDao;
  @Inject private JobsProcessorSvc _jobsProcessorSvc;

  private ObjectId _groupId;
  private String _clusterName;
  private File _csvOutputFile;

  @Before
  public void setup() throws Exception {
    super.setUp();

    _groupId = ObjectId.get();
    _clusterName = "cluster1";
  }

  @Test
  public void testSubmitBackfillJobs() throws IOException, ParseException {
    final BackfillMissingDLSSubmissionTool tool =
        new BackfillMissingDLSSubmissionTool(
            _onlineArchiveRunDao,
            _onlineArchiveDao,
            _onlineArchiveV3MigrationDao,
            _backfillMissingDLSJobDao,
            _onlineArchiveSubmittedUploadFilesJobDao,
            _jobsProcessorSvc);

    final ObjectId archiveId = oid(1);
    final ObjectId archiveIdDeleted = oid(3);
    final ObjectId archiveIdWithActiveMigration = oid(4);
    final OnlineArchive archive = getOnlineArchive(_groupId, _clusterName, archiveId, State.ACTIVE);
    _onlineArchiveDao.create(archive);
    _onlineArchiveV3MigrationDao.save(
        getMigration(OnlineArchiveV3Migration.State.SUCCEEDED, archiveId));
    final ArchiveRunStats emptyStats =
        new ArchiveRunStats().copy().numFiles(0).archiveQueryElapsedTimeMs(0L).build();
    _onlineArchiveRunDao.save(getOnlineArchiveRun(archiveId, "successfulJob", emptyStats));

    _onlineArchiveDao.create(
        getOnlineArchive(_groupId, _clusterName, archiveIdDeleted, State.DELETED));

    _onlineArchiveDao.create(
        getOnlineArchive(_groupId, _clusterName, archiveIdWithActiveMigration, State.ACTIVE));
    _onlineArchiveV3MigrationDao.save(
        getMigration(OnlineArchiveV3Migration.State.IN_PROGRESS, archiveIdWithActiveMigration));

    final ArchiveRunStats fullStats =
        new ArchiveRunStats().copy().numFiles(1).archiveQueryElapsedTimeMs(1L).build();
    _onlineArchiveRunDao.save(getOnlineArchiveRun(archiveId, "archiveRunStatsNotEmpty", fullStats));

    _onlineArchiveRunDao.save(getOnlineArchiveRun(archiveId, "dlsJobAlreadyUploaded", emptyStats));
    _onlineArchiveSubmittedUploadFilesJobDao.save(getUploadFilesJob("dlsJobAlreadyUploaded"));

    _csvOutputFile = File.createTempFile("/tmp/results", ".csv");
    _csvOutputFile.deleteOnExit();

    final String csvFilePath = "/tmp/backfillMissingDLSSubmissionTestInputFile.csv";
    final File targetFile = new File(csvFilePath);
    FileUtils.copyInputStreamToFile(
        ClasspathUtils.openResourceUrl("csv/backfillMissingDLSSubmissionTestInputFile.csv"),
        targetFile);

    tool.setOptions(
        new String[] {
          String.format("--inputFile=%s", csvFilePath),
          String.format("--outputCsvFile=%s", _csvOutputFile.getAbsolutePath())
        });
    tool.submitBackfillJobs();

    // verify all errors were written to output file
    final HashMap<String, String> errorMap = new HashMap<>();
    errorMap.put("successfulJob", "null");
    errorMap.put("archiveDoesNotExist", "archive does not exist");
    errorMap.put("archiveDeleted", "archive is deleted");
    errorMap.put("hasActiveMigration", "archive has active migration");
    errorMap.put("archiveRunDoesNotExist", "archive run does not exist");
    errorMap.put("archiveRunStatsNotEmpty", "archive run stats was not empty");
    errorMap.put("dlsJobAlreadyUploaded", "DLS job was already uploaded to DLS");
    final CsvListReader csvListReader =
        new CsvListReader(
            new FileReader(_csvOutputFile.getAbsolutePath()), CsvPreference.STANDARD_PREFERENCE);
    csvListReader.read(null, null, null);
    while (true) {
      final List<Object> csvRow = csvListReader.read(null, null, null);
      if (csvRow == null) {
        break; // EOF
      }
      final String dlsJobId = (String) csvRow.get(1);
      final String validationError = (String) csvRow.get(2);

      assertEquals(dlsJobId, errorMap.get(dlsJobId), validationError);
    }

    // verify one job has been successfully submitted
    assertEquals(1, _backfillMissingDLSJobDao.findAll().size());
    final List<BackfillMissingDLSJob> backfillMissingDLSJobs =
        _backfillMissingDLSJobDao.findByJobId("successfulJob");
    assertEquals(1, backfillMissingDLSJobs.size());

    final BackfillMissingDLSJob backfillMissingDLSJob = backfillMissingDLSJobs.get(0);
    assertNotNull(backfillMissingDLSJob.getId());
    assertEquals("successfulJob", backfillMissingDLSJob.getDlsJobId());
    assertEquals(archiveId, backfillMissingDLSJob.getArchiveId());
    assertEquals(_groupId, backfillMissingDLSJob.getGroupId());
    assertEquals("dataset", backfillMissingDLSJob.getDatasetName());
    assertEquals(archive.getDataProcessRegion(), backfillMissingDLSJob.getDataProcessRegion());
    assertEquals(
        archive.getDataLandingZoneBucketName(),
        backfillMissingDLSJob.getDataLandingZoneBucketName());
    assertEquals(
        archive.getDataLandingZoneConfig(), backfillMissingDLSJob.getDataLandingZoneConfig());
    assertEquals(BackfillMissingDLSJob.State.QUEUED, backfillMissingDLSJob.getState());
    assertEquals(1, backfillMissingDLSJob.getWaitForDLSUploadIntervalMinutes());

    final List<Job> jobs =
        _jobsProcessorSvc.findAllJobsForHandler(OnlineArchiveBackfillMissingDLSJobHandler.class);
    assertEquals(1, jobs.size());
  }

  private OnlineArchive getOnlineArchive(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pArchiveId,
      final OnlineArchive.State pState) {
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("string", "field1", 0),
            new PartitionField("string", "field2", 1),
            new PartitionField("date", "dateField", 2));

    return new OnlineArchive.Builder()
        .setArchiveId(pArchiveId)
        .setClusterId(pGroupId, pClusterName)
        .setDbName("mydb")
        .setCollName("mycoll")
        .setPartitionFields(partitionFields)
        .setCriteria(
            new OnlineArchive.DateCriteria(
                "dateField", 5, OnlineArchive.DateCriteria.DateFormat.ISODATE))
        .setState(pState)
        .setOnlineArchiveVersion(OnlineArchiveVersion.V1)
        .setCollectionUUID(UUID.randomUUID())
        .setDataSetName("dataset")
        .setDataProcessRegion(new DataProcessRegion("AWS", "US_EAST_1"))
        .setDataLandingZoneBucketName("dlzBucket")
        .build();
  }

  private OnlineArchiveRun getOnlineArchiveRun(
      final ObjectId pArchiveId, final String pJobId, final ArchiveRunStats pStats) {
    final Instant now = Instant.now();
    final String dlzBucketName = "online-archive-test-bucket";
    final Date startDate = Date.from(now.minus(Duration.ofHours(1)));
    final Date endDate = Date.from(now.minus(Duration.ofHours(2)));

    return new Builder()
        .id(new ObjectId())
        .archiveId(pArchiveId)
        .groupId(new ObjectId())
        .clusterName("cluster1")
        .jobId(pJobId)
        .startDate(startDate)
        .endDate(endDate)
        .errorMessage(null)
        .numFrsJobErrors(0)
        .state(ArchiveRunState.COMPLETED)
        .stats(pStats)
        .onlineArchiveVersion(OnlineArchiveVersion.V3)
        .dataLandingZoneBucketName(dlzBucketName)
        .dataProcessRegion(
            DataProcessRegion.builder()
                .cloudProvider(CloudProvider.AWS.getChefProvider())
                .region(AWSRegionName.US_EAST_1.getValue())
                .build())
        .dlzCleanupEndDate(null)
        .hasDlzCleanupError(false)
        .dlzCleanupErrorMessage(null)
        .build();
  }

  private OnlineArchiveV3Migration getMigration(
      final OnlineArchiveV3Migration.State pState, final ObjectId pArchiveId) {
    final Date date = new Date();
    final Source source = new Source(OnlineArchiveVersion.V1, "US_EAST_1", "bucket", "somePath");
    final DataProcessRegion dpr = new DataProcessRegion("AWS", "US_EAST_1");
    final Target target =
        new Target(
            dpr,
            "dlzBucket",
            "cutoverDSName",
            "cutoverPartitionset",
            "cutoverPath",
            "backfillDSName",
            "backfillPartitionset");
    final List<String> overrideShardKeys = List.of("primaryKey", "secondaryKey");
    return new OnlineArchiveV3Migration(
        ObjectId.get(),
        date,
        pArchiveId,
        oid(3),
        "cluster",
        oid(4),
        pState,
        date,
        date,
        false,
        null,
        null,
        source,
        target,
        Phase.PHASE_1_CUTOVER_NEW_DATA,
        date,
        date,
        null,
        null,
        null,
        overrideShardKeys,
        false,
        false,
        new BackfillStats.Builder()
            .setArchiveScanElapsedTimeMs(1)
            .setTotalScannedNumArchiveFiles(2)
            .setTotalScannedArchiveFileSize(3)
            .setNumGeneratedUploadJobs(4)
            .setDatasetNumDocuments(5)
            .setDatasetDataSize(6)
            .setTotalElapsedTimeMs(7)
            .build(),
        false);
  }

  public OnlineArchiveSubmittedUploadFilesJob getUploadFilesJob(final String pJobId) {
    return new OnlineArchiveSubmittedUploadFilesJob.Builder()
        .id(new ObjectId())
        .jobId(pJobId)
        .manifestProvider("aws")
        .manifestBucket("bucket")
        .manifestPath("path")
        .build();
  }
}
