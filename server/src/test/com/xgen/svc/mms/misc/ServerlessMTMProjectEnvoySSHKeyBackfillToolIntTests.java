package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.serverless._public.model.ServerlessEnvoySSHKeys;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.serverless._public.svc.ServerlessEnvoySSHKeysSvc;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class ServerlessMTMProjectEnvoySSHKeyBackfillToolIntTests extends BaseSvcTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  @Inject private ServerlessEnvoySSHKeysSvc _serverlessEnvoySSHKeysSvc;
  private ServerlessMTMProjectEnvoySSHKeyBackfillTool _tool;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _tool =
        new ServerlessMTMProjectEnvoySSHKeyBackfillTool(_ndsGroupDao, _serverlessEnvoySSHKeysSvc);
  }

  @Test
  public void testRunTool_noServerlessDeployment() throws InterruptedException {
    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer());

    final ObjectId groupId = ObjectId.get();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.addCloudContainer(groupId, azureContainer);

    _tool.run(groupId);

    assertTrue(_serverlessEnvoySSHKeysSvc.getSshKeys(groupId).isEmpty());
  }

  @Test
  public void testRunTool_unprovisionedServerlessDeployment() throws InterruptedException {
    final ServerlessLoadBalancingDeployment unprovisionedDeployment =
        ServerlessDeploymentModelTestFactory.getUnProvisionedGCPServerlessLoadBalancingDeployment(
            GCPRegionName.CENTRAL_US);
    final ObjectId groupId = unprovisionedDeployment.getGroupId();

    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        groupId, EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(groupId);
    _serverlessLoadBalancingDeploymentDao.save(unprovisionedDeployment);

    assertTrue(_serverlessEnvoySSHKeysSvc.getSshKeys(groupId).isEmpty());

    _tool.run(groupId);

    assertTrue(_serverlessEnvoySSHKeysSvc.getSshKeys(groupId).isPresent());
  }

  @Test
  public void testRunTool_provisionedServerlessDeployment() throws InterruptedException {
    final ServerlessLoadBalancingDeployment provisionedDeployment =
        ServerlessDeploymentModelTestFactory.getAzureServerlessLoadBalancingDeployment(
            AzureRegionName.US_EAST_2);
    final ObjectId groupId = provisionedDeployment.getGroupId();

    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        groupId, EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(groupId);
    _serverlessLoadBalancingDeploymentDao.save(provisionedDeployment);

    assertTrue(_serverlessEnvoySSHKeysSvc.getSshKeys(groupId).isEmpty());

    _tool.run(groupId);

    assertTrue(_serverlessEnvoySSHKeysSvc.getSshKeys(groupId).isPresent());
  }

  @Test
  public void testRunTool_provisionedServerlessDeployment_existingSSHKey()
      throws InterruptedException {
    final ServerlessLoadBalancingDeployment provisionedDeployment =
        ServerlessDeploymentModelTestFactory.getAzureServerlessLoadBalancingDeployment(
            AzureRegionName.US_EAST_2);
    final ObjectId groupId = provisionedDeployment.getGroupId();

    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false, RegionUsageRestrictions.NONE);
    _ndsGroupDao.setMTMPassword(
        groupId, EncryptionUtils.genEncryptStr(EncryptionUtils.randomAlphanumeric(24)));
    _ndsGroupDao.setServerlessMTMHolder(groupId);
    _serverlessLoadBalancingDeploymentDao.save(provisionedDeployment);
    _serverlessEnvoySSHKeysSvc.createEnvoySshKeys(groupId);

    final Optional<ServerlessEnvoySSHKeys> sshKeys = _serverlessEnvoySSHKeysSvc.getSshKeys(groupId);
    assertTrue(sshKeys.isPresent());

    _tool.run(groupId);

    assertTrue(_serverlessEnvoySSHKeysSvc.getSshKeys(groupId).isPresent());
    assertEquals(sshKeys.get(), _serverlessEnvoySSHKeysSvc.getSshKeys(groupId).get());
  }
}
