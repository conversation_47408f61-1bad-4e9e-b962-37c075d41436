load("//server/src/test:rules.bzl", "library_package", "test_package")

library_package(
    name = "misc",
    visibility = ["//server/src/features/com/xgen/svc/cukes/steps/deployment:__pkg__"],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role/_public/model",
        "//server/src/main/com/xgen/cloud/authz/core/_public/client",
        "//server/src/main/com/xgen/cloud/authz/core/_public/utils",
        "//server/src/main/com/xgen/cloud/authz/core/_public/view/ui",
        "//server/src/main/com/xgen/cloud/authz/shared/_public/exceptions",
        "//server/src/main/com/xgen/cloud/brs/web/_private/res",
        "//server/src/main/com/xgen/cloud/common/authz/_public/view",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/guice",
        "//server/src/main/com/xgen/cloud/common/requestparams",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/organization/_public/model",
        "//server/src/main/com/xgen/cloud/payments/credit",
        "//server/src/main/com/xgen/cloud/services/authz/_public/proto/authzv2:java_grpc",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/unit/com/xgen/svc/mms",
        "@maven//:com_google_inject_guice",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:junit_junit",
        "@maven//:org_mockito_mockito_core",
    ],
)

test_package(
    name = "TestLibrary",
    srcs = glob(
        ["*IntTests.java"],
        exclude = ["*ExternalIntTests.java"],
    ),
    deny_warnings = False,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/role/_public/model",
        "//server/src/main/com/xgen/cloud/access/usergroups",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/activity/_private/dao",
        "//server/src/main/com/xgen/cloud/atm/activity",
        "//server/src/main/com/xgen/cloud/authz/core",
        "//server/src/main/com/xgen/cloud/authz/resource",
        "//server/src/main/com/xgen/cloud/authz/shared",
        "//server/src/main/com/xgen/cloud/authz/sync",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/authz",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/jobqueue/_public/svc",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/streamex",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/deployment/_public/model",
        "//server/src/main/com/xgen/cloud/eventcommitter",
        "//server/src/main/com/xgen/cloud/eventcommitter/_private/dao",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/group/_public/model",
        "//server/src/main/com/xgen/cloud/group/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/billing/_public/model",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datalake/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/datavalidation",
        "//server/src/main/com/xgen/cloud/nds/datavalidation/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/module",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/settings",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/replicasethardware",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/organization/_public/model",
        "//server/src/main/com/xgen/cloud/organization/_public/svc",
        "//server/src/main/com/xgen/cloud/payments/credit",
        "//server/src/main/com/xgen/cloud/resourcetags",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider",
        "//server/src/main/com/xgen/cloud/services/authz",
        "//server/src/main/com/xgen/cloud/services/common/proto",
        "//server/src/main/com/xgen/cloud/services/event/_private/dao",
        "//server/src/main/com/xgen/cloud/team",
        "//server/src/main/com/xgen/cloud/team/_private/dao",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/main/com/xgen/cloud/user/_private/svc",
        "//server/src/main/com/xgen/cloud/usergroupsync",
        "//server/src/main/com/xgen/cloud/usergroupsync/_private/dao",
        "//server/src/main/com/xgen/cloud/usersandinvites",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/main/com/xgen/svc/mms/dao/billing",
        "//server/src/main/com/xgen/svc/mms/dao/pausefreetiermonitoring",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/main/com/xgen/svc/mms/svc/atlasbilling",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/nds/svc/adl:utils",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//third_party:driverwrappers",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:com_amazonaws_aws_java_sdk_elasticloadbalancingv2",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_azure_azure_core",
        "@maven//:com_azure_resourcemanager_azure_resourcemanager_compute",
        "@maven//:com_azure_resourcemanager_azure_resourcemanager_network",
        "@maven//:com_google_apis_google_api_services_cloudresourcemanager",
        "@maven//:com_google_apis_google_api_services_compute",
        "@maven//:com_google_inject_guice",
        "@maven//:junit_junit",
        "@maven//:net_sf_supercsv_super_csv",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)

test_package(
    name = "AzureExternalTestLibrary",
    srcs = [
        "AzureCapacityReservationOrphanedItemCorrectionToolExternalIntTests.java",
        "AzureInstanceHardwareBackfillPhysicalZoneIdsToolExternalIntTests.java",
        "AzureSubscriptionBackfillPhysicalZoneIdsToolExternalIntTests.java",
        "AzureSubscriptionCreationToolExternalIntTests.java",
    ],
    deny_warnings = False,
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = [
        "external",
        "no-sandbox",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/test/com/xgen/svc/nds/azure",
        "@maven//:com_azure_resourcemanager_azure_resourcemanager_compute",
        "@maven//:com_azure_resourcemanager_azure_resourcemanager_resources",
        "@maven//:junit_junit",
        "@maven//:org_assertj_assertj_core",
    ],
)

test_package(
    name = "onlineArchiveDLZManifestFileBackfillToolExternalTestLibrary",
    srcs =
        [
            "OnlineArchiveDLZManifestFileBackfillToolExternalIntTests.java",
            "OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesToolExternalIntTests.java",
            "OnlineArchiveLeakedItemDeleterAWSToolExternalIntTests.java",
            "OnlineArchiveLeakedItemDeleterAzureToolExternalIntTests.java",
        ],
    deny_warnings = False,
    tags = ["external"],
    deps = [
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/aws",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/eventcommitter",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datavalidation",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/module",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/payments/credit",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)

test_package(
    name = "onlineArchiveDeleteUnusedInvalidUUIDJobManifestsToolExternalTestLibrary",
    srcs = ["OnlineArchiveDeleteUnusedInvalidUUIDJobManifestsToolExternalIntTests.java"],
    deny_warnings = False,
    tags = ["external"],
    deps = [
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/aws",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:junit_junit",
    ],
)

test_package(
    name = "awsIpamPoolToolExternalTestLibrary",
    srcs = ["AwsIpamPoolToolExternalIntTests.java"],
    deny_warnings = False,
    tags = [
        "external",
        "no-sandbox",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/svc",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
