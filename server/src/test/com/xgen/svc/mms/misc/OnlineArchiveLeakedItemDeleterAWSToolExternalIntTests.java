package com.xgen.svc.mms.misc;

import static com.xgen.svc.mms.misc.OnlineArchiveLeakedItemDeleterAWSTool.DEFAULT_DATE_OFFSET_DAYS;
import static com.xgen.svc.mms.misc.OnlineArchiveLeakedItemDeleterAWSTool.DEFAULT_INVENTORY_PREFIX;
import static com.xgen.svc.mms.misc.OnlineArchiveLeakedItemDeleterAWSTool.ONLINE_ARCHIVE_INVENTORY_CONFIG_ID;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.HeadBucketRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.waiters.WaiterParameters;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.aws._public.clients.AWSClientsFactory;
import com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil;
import com.xgen.cloud.common.model._public.date.DateFormat;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.AWSAccountBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.State;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.misc.OnlineArchiveLeakedItemDeleterAWSTool.InventoryMetaData;
import jakarta.inject.Inject;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.zip.GZIPOutputStream;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class OnlineArchiveLeakedItemDeleterAWSToolExternalIntTests extends BaseSvcTest {

  @Inject private AppSettings _appSettings;

  private ObjectId _groupId;
  private AWSClientsFactory _clientsFactory;
  private AWSRegionName _awsRegion;

  @Inject private OnlineArchiveDao _onlineArchiveDao;

  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private AWSAccountDao _awsAccountDao;

  private static final String AWS_ACCESS_KEY = "local.aws.accessKey";
  private static final String AWS_SECRET_KEY = "local.aws.secretKey";
  private static final String TEST_BUCKET = "oa-dlz-manifest-file-backfill-test";

  @Before
  public void setup() throws Exception {
    super.setUp();
    _groupId = ObjectId.get();
    _awsRegion = AWSRegionName.US_EAST_1;

    final String awsAccessKey = _appSettings.getStrProp(AWS_ACCESS_KEY);
    final String awsSecretKey = _appSettings.getStrProp(AWS_SECRET_KEY);
    final AWSAccount awsAccount =
        new AWSAccountBuilder()
            .setAccessKey(awsAccessKey)
            .setSecretKey(awsSecretKey)
            .setForOnlineArchiveDataLandingZone(true)
            .build();
    _awsAccountDao.save(awsAccount);

    _clientsFactory = new AWSClientsFactory(_appSettings.getNDSGovUSEnabled());
    final AmazonS3 s3Client = getS3Client();
    s3Client
        .waiters()
        .bucketExists()
        .run(new WaiterParameters<>(new HeadBucketRequest(TEST_BUCKET)));
  }

  @After
  public void teardown() throws Exception {
    final AmazonS3 s3Client = getS3Client();

    // List and delete specific files created during the tests
    ListObjectsV2Request listObjectsRequest =
        new ListObjectsV2Request().withBucketName(TEST_BUCKET);
    ListObjectsV2Result result;
    do {
      result = s3Client.listObjectsV2(listObjectsRequest);
      for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
        String key = objectSummary.getKey();
        if (key.equals("archive/inventory/file1.csv.gz")
            || key.equals("archive/inventory/file2.csv.gz")
            || key.endsWith("_1.bson.snappy")
            || key.endsWith("_2.bson.snappy")) {
          s3Client.deleteObject(TEST_BUCKET, key);
        }
      }
      listObjectsRequest.setContinuationToken(result.getNextContinuationToken());
    } while (result.isTruncated());
  }

  @Test
  public void testProcessInventoryMetaDataList_DryRun() throws Exception {
    final OnlineArchiveLeakedItemDeleterAWSTool tool =
        new OnlineArchiveLeakedItemDeleterAWSTool(
            _appSettings, null, _awsAccountDao, _awsApiSvc, _onlineArchiveDao);

    tool.setOptions(new String[] {"--dryRun"});

    final Date targetDate = DateUtils.addDays(new Date(), DEFAULT_DATE_OFFSET_DAYS);
    final String dateString =
        new SimpleDateFormat(DateFormat.ISO_1.getFormatDay()).format(targetDate);

    final String manifestKey =
        String.join(
            "/",
            DEFAULT_INVENTORY_PREFIX,
            TEST_BUCKET,
            ONLINE_ARCHIVE_INVENTORY_CONFIG_ID,
            dateString);
    String inventoryFile1Key = "archive/inventory/file1.csv.gz";
    String inventoryFile2Key = "archive/inventory/file2.csv.gz";

    createManifestFile(getS3Client(), manifestKey, inventoryFile1Key, inventoryFile2Key);

    // Archive 1 is not in database. This file should be deleted unless dry run is set.
    final ObjectId groupId1 = new ObjectId();
    final ObjectId archiveId1 = new ObjectId();
    final ObjectId jobId1 = new ObjectId();
    String leakedItemInArchive1 =
        String.format("%s/%s/data/%s/%s_1.bson.snappy", groupId1, archiveId1, jobId1, jobId1);

    // Archive 2 is in database, but in purged state. This file should be deleted unless dry run is
    // set.
    final ObjectId groupId2 = new ObjectId();
    final ObjectId archiveId2 = new ObjectId();
    final ObjectId jobId2 = new ObjectId();
    insertOnlineArchive(groupId2, archiveId2, "test-cluster-1", OnlineArchive.State.PURGED);
    String leakedItemInArchive2 =
        String.format("%s/%s/data/%s/%s_2.bson.snappy", groupId2, archiveId2, jobId2, jobId2);

    // Archive 3 is in database, in active state. This file should not be deleted.
    final ObjectId groupId3 = new ObjectId();
    final ObjectId archiveId3 = new ObjectId();
    final ObjectId jobId3 = new ObjectId();
    insertOnlineArchive(groupId3, archiveId3, "test-cluster-1", State.ACTIVE);
    String leakedItemInArchive3 =
        String.format("%s/%s/data/%s/%s_1.bson.snappy", groupId3, archiveId3, jobId3, jobId3);

    // Archive 4 is in database, in deleted state. This file should not be deleted.
    final ObjectId groupId4 = new ObjectId();
    final ObjectId archiveId4 = new ObjectId();
    final ObjectId jobId4 = new ObjectId();
    insertOnlineArchive(groupId4, archiveId4, "test-cluster-1", State.DELETED);
    String leakedItemInArchive4 =
        String.format("%s/%s/data/%s/%s_2.bson.snappy", groupId4, archiveId4, jobId4, jobId4);

    createInventory(inventoryFile1Key, getS3Client(), leakedItemInArchive1, leakedItemInArchive2);
    createInventory(inventoryFile2Key, getS3Client(), leakedItemInArchive3, leakedItemInArchive4);

    putObjectInS3(getS3Client(), leakedItemInArchive1);
    putObjectInS3(getS3Client(), leakedItemInArchive2);
    putObjectInS3(getS3Client(), leakedItemInArchive3);
    putObjectInS3(getS3Client(), leakedItemInArchive4);

    InventoryMetaData inventoryMetaData =
        new InventoryMetaData(AWSRegionName.US_EAST_1, TEST_BUCKET, manifestKey);

    final File outputFile = new File("testfile");
    try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
      tool.processInventoryMetaDataList(List.of(inventoryMetaData), writer);
    }

    final AmazonS3 s3Client = getS3Client();

    // Verify no files are deleted, since this is a dry run
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive1));
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive2));
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive3));
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive4));
  }

  @Test
  public void testProcessInventoryMetaDataList() throws Exception {
    final OnlineArchiveLeakedItemDeleterAWSTool tool =
        new OnlineArchiveLeakedItemDeleterAWSTool(
            _appSettings, null, _awsAccountDao, _awsApiSvc, _onlineArchiveDao);

    String[] args = {
      "--outputFile", "testOutput.txt",
    };

    tool.setOptions(args);

    final Date targetDate = DateUtils.addDays(new Date(), DEFAULT_DATE_OFFSET_DAYS);
    final String dateString =
        new SimpleDateFormat(DateFormat.ISO_1.getFormatDay()).format(targetDate);
    final String manifestKey =
        String.join(
            "/",
            DEFAULT_INVENTORY_PREFIX,
            TEST_BUCKET,
            ONLINE_ARCHIVE_INVENTORY_CONFIG_ID,
            dateString);

    String inventoryFile1Key = "archive/inventory/file1.csv.gz";
    String inventoryFile2Key = "archive/inventory/file2.csv.gz";

    createManifestFile(getS3Client(), manifestKey, inventoryFile1Key, inventoryFile2Key);

    // Archive 1 is not in database. This file should be deleted.
    final ObjectId groupId1 = new ObjectId();
    final ObjectId archiveId1 = new ObjectId();
    final ObjectId jobId1 = new ObjectId();
    String leakedItemInArchive1 =
        String.format("%s/%s/data/%s/%s_1.bson.snappy", groupId1, archiveId1, jobId1, jobId1);

    // Archive 2 is in database, but in purged state. This file should be deleted.
    final ObjectId groupId2 = new ObjectId();
    final ObjectId archiveId2 = new ObjectId();
    final ObjectId jobId2 = new ObjectId();
    insertOnlineArchive(groupId2, archiveId2, "test-cluster-1", OnlineArchive.State.PURGED);
    String leakedItemInArchive2 =
        String.format("%s/%s/data/%s/%s_2.bson.snappy", groupId2, archiveId2, jobId2, jobId2);

    // Archive 3 is in database, in active state. This file should not be deleted.
    final ObjectId groupId3 = new ObjectId();
    final ObjectId archiveId3 = new ObjectId();
    final ObjectId jobId3 = new ObjectId();
    insertOnlineArchive(groupId3, archiveId3, "test-cluster-1", State.ACTIVE);
    String leakedItemInArchive3 =
        String.format("%s/%s/data/%s/%s_1.bson.snappy", groupId3, archiveId3, jobId3, jobId3);

    // Archive 4 is in database, in deleted state. This file should not be deleted.
    final ObjectId groupId4 = new ObjectId();
    final ObjectId archiveId4 = new ObjectId();
    final ObjectId jobId4 = new ObjectId();
    insertOnlineArchive(groupId4, archiveId4, "test-cluster-1", State.DELETED);
    String leakedItemInArchive4 =
        String.format("%s/%s/data/%s/%s_2.bson.snappy", groupId4, archiveId4, jobId4, jobId4);

    createInventory(inventoryFile1Key, getS3Client(), leakedItemInArchive1, leakedItemInArchive2);
    createInventory(inventoryFile2Key, getS3Client(), leakedItemInArchive3, leakedItemInArchive4);

    putObjectInS3(getS3Client(), leakedItemInArchive1);
    putObjectInS3(getS3Client(), leakedItemInArchive2);
    putObjectInS3(getS3Client(), leakedItemInArchive3);
    putObjectInS3(getS3Client(), leakedItemInArchive4);

    InventoryMetaData inventoryMetaData =
        new InventoryMetaData(AWSRegionName.US_EAST_1, TEST_BUCKET, manifestKey);

    final File outputFile = new File("testfile");
    try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
      tool.processInventoryMetaDataList(List.of(inventoryMetaData), writer);
    }

    final AmazonS3 s3Client = getS3Client();

    // Verify leakedItemInArchive1 and leakedItemInArchive2 are deleted as expected
    assertTrue(doesNotExist(s3Client, TEST_BUCKET, leakedItemInArchive1));
    assertTrue(doesNotExist(s3Client, TEST_BUCKET, leakedItemInArchive2));

    // Verify leakedItemInArchive3 and leakedItemInArchive4 are not deleted as expected
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive3));
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive4));
  }

  @Test
  public void testProcessInventoryMetaDataList_maxFilesToDeleteSetToOne() throws Exception {
    final OnlineArchiveLeakedItemDeleterAWSTool tool =
        new OnlineArchiveLeakedItemDeleterAWSTool(
            _appSettings, null, _awsAccountDao, _awsApiSvc, _onlineArchiveDao);

    String[] args = {
      "--maxLeakedItemsToDelete", "1",
    };

    tool.setOptions(args);

    final Date targetDate = DateUtils.addDays(new Date(), DEFAULT_DATE_OFFSET_DAYS);
    final String dateString =
        new SimpleDateFormat(DateFormat.ISO_1.getFormatDay()).format(targetDate);
    final String manifestKey =
        String.join(
            "/",
            DEFAULT_INVENTORY_PREFIX,
            TEST_BUCKET,
            ONLINE_ARCHIVE_INVENTORY_CONFIG_ID,
            dateString);

    String inventoryFile1Key = "archive/inventory/file1.csv.gz";
    String inventoryFile2Key = "archive/inventory/file2.csv.gz";

    createManifestFile(getS3Client(), manifestKey, inventoryFile1Key, inventoryFile2Key);

    final ObjectId groupId1 = new ObjectId();
    final ObjectId archiveId1 = new ObjectId();
    final ObjectId jobId1 = new ObjectId();
    String leakedItemInArchive1 =
        String.format("%s/%s/data/%s/%s_1.bson.snappy", groupId1, archiveId1, jobId1, jobId1);

    final ObjectId groupId2 = new ObjectId();
    final ObjectId archiveId2 = new ObjectId();
    final ObjectId jobId2 = new ObjectId();
    insertOnlineArchive(groupId2, archiveId2, "test-cluster-1", OnlineArchive.State.PURGED);
    String leakedItemInArchive2 =
        String.format("%s/%s/data/%s/%s_2.bson.snappy", groupId2, archiveId2, jobId2, jobId2);

    final ObjectId groupId3 = new ObjectId();
    final ObjectId archiveId3 = new ObjectId();
    final ObjectId jobId3 = new ObjectId();
    insertOnlineArchive(groupId3, archiveId3, "test-cluster-1", State.ACTIVE);
    String leakedItemInArchive3 =
        String.format("%s/%s/data/%s/%s_1.bson.snappy", groupId3, archiveId3, jobId3, jobId3);

    final ObjectId groupId4 = new ObjectId();
    final ObjectId archiveId4 = new ObjectId();
    final ObjectId jobId4 = new ObjectId();
    insertOnlineArchive(groupId4, archiveId4, "test-cluster-1", State.DELETED);
    String leakedItemInArchive4 =
        String.format("%s/%s/data/%s/%s_2.bson.snappy", groupId4, archiveId4, jobId4, jobId4);

    createInventory(inventoryFile1Key, getS3Client(), leakedItemInArchive1, leakedItemInArchive2);
    createInventory(inventoryFile2Key, getS3Client(), leakedItemInArchive3, leakedItemInArchive4);

    putObjectInS3(getS3Client(), leakedItemInArchive1);
    putObjectInS3(getS3Client(), leakedItemInArchive2);
    putObjectInS3(getS3Client(), leakedItemInArchive3);
    putObjectInS3(getS3Client(), leakedItemInArchive4);

    InventoryMetaData inventoryMetaData =
        new InventoryMetaData(AWSRegionName.US_EAST_1, TEST_BUCKET, manifestKey);

    final File outputFile = new File("testfile");
    try (final BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
      tool.processInventoryMetaDataList(List.of(inventoryMetaData), writer);
    }

    final AmazonS3 s3Client = getS3Client();

    // Verify only one file is deleted, since maxFilesToDelete is set to 1
    assertTrue(doesNotExist(s3Client, TEST_BUCKET, leakedItemInArchive1));

    // Verify leakedItemInArchive2 is not deleted since maxFilesToDelete is set to 1
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive2));

    // Verify leakedItemInArchive3 and leakedItemInArchive4 are not deleted
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive3));
    assertNotNull(s3Client.getObjectMetadata(TEST_BUCKET, leakedItemInArchive4));
  }

  private boolean doesNotExist(AmazonS3 s3Client, String bucket, String key) {
    try {
      s3Client.getObjectMetadata(bucket, key);
      return false;
    } catch (com.amazonaws.services.s3.model.AmazonS3Exception e) {
      if (e.getStatusCode() == 404) {
        return true;
      }
      throw e;
    }
  }

  private void insertOnlineArchive(
      final ObjectId pGroupId,
      final ObjectId pArchiveId,
      final String pClusterName,
      final OnlineArchive.State state) {
    final List<PartitionField> partitionFields = List.of();

    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder()
            .setArchiveId(ObjectId.get())
            .setClusterId(pGroupId, pClusterName)
            .setArchiveId(pArchiveId)
            .setCriteria(
                new OnlineArchive.DateCriteria(
                    "dateField", 5, OnlineArchive.DateCriteria.DateFormat.ISODATE))
            .setState(state)
            .setPartitionFields(partitionFields)
            .build();

    _onlineArchiveDao.create(onlineArchive);
  }

  private String createManifestFile(
      final AmazonS3 pS3Client,
      final String pManifestKey,
      final String pInventoryFile1Key,
      final String pInventoryFile2Key) {
    long inventoryFile1Size = 12345L; // Example size in bytes
    long inventoryFile2Size = 67890L; // Example size in bytes

    String manifestJsonContent =
        String.format(
            "{\n"
                + "  \"files\": [\n"
                + "    {\n"
                + "      \"key\": \"%s\",\n"
                + "      \"size\": %d\n"
                + "    },\n"
                + "    {\n"
                + "      \"key\": \"%s\",\n"
                + "      \"size\": %d\n"
                + "    }\n"
                + "  ]\n"
                + "}",
            pInventoryFile1Key, inventoryFile1Size, pInventoryFile2Key, inventoryFile2Size);

    final InputStream stream =
        new ByteArrayInputStream(manifestJsonContent.getBytes(StandardCharsets.UTF_8));
    final PutObjectRequest request =
        new PutObjectRequest(
            TEST_BUCKET, String.format(pManifestKey), stream, new ObjectMetadata());
    pS3Client.putObject(request);

    return pManifestKey;
  }

  private void createInventory(
      final String pInventoryFile,
      final AmazonS3 pS3Client,
      final String pS3Key1,
      final String pS3Key2)
      throws Exception {
    final String file =
        String.format(
            "archive,%s,1048576,2025-05-26T15:00:00.000Z\n"
                + "archive,%s,524288,2025-05-26T15:30:00.000Z",
            pS3Key1, pS3Key2);

    // GZIP content
    byte[] gzippedBytes;
    try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
        GZIPOutputStream gzipOS = new GZIPOutputStream(baos)) {
      gzipOS.write(file.getBytes(StandardCharsets.UTF_8));
      gzipOS.finish();
      gzippedBytes = baos.toByteArray();
    }

    final InputStream stream = new ByteArrayInputStream(gzippedBytes);
    final ObjectMetadata metadata = new ObjectMetadata();
    metadata.setContentType("application/gzip");
    metadata.setContentLength(gzippedBytes.length);

    final PutObjectRequest request =
        new PutObjectRequest(TEST_BUCKET, pInventoryFile, stream, metadata);
    pS3Client.putObject(request);
  }

  private void putObjectInS3(final AmazonS3 pS3Client, final String s3Key) {
    final String fileContent = "Sample content for S3 object";
    final InputStream stream =
        new ByteArrayInputStream(fileContent.getBytes(StandardCharsets.UTF_8));
    final ObjectMetadata metadata = new ObjectMetadata();
    metadata.setContentType("text/plain");
    metadata.setContentLength(fileContent.getBytes(StandardCharsets.UTF_8).length);

    final PutObjectRequest request = new PutObjectRequest(TEST_BUCKET, s3Key, stream, metadata);
    pS3Client.putObject(request);
  }

  private AmazonS3 getS3Client() {
    final String awsAccessKey = _appSettings.getStrProp(AWS_ACCESS_KEY);
    final String awsSecretKey = _appSettings.getStrProp(AWS_SECRET_KEY);
    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(awsAccessKey, awsSecretKey, null, null);
    return _clientsFactory.getS3Client(provider, _awsRegion.getValue());
  }
}
