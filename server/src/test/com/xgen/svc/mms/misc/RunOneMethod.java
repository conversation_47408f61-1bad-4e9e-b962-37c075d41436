package com.xgen.svc.mms.misc;

import static org.mockito.Mockito.when;

import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.xgen.cloud.brs.web._private.res.v2.BackupResource;
import com.xgen.cloud.common.guice._public.extensions.closeable.CloseableModule;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.misc.util.MinimalModule;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.core.Response;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// ant misc -Dclass=RunOneMethod -Dapp.id=mms -Dserver.env=local -Darg0=GROUPID -Darg1=USERNAME
public class RunOneMethod {

  private final Injector _injector;
  private static final Logger LOG = LoggerFactory.getLogger(RunOneMethod.class);

  @Inject
  public RunOneMethod() throws Exception {
    _injector = Guice.createInjector(new CloseableModule(), MODULE);
  }

  private void runMethod(final String groupId, final String userName) throws Exception {
    BackupResource backupResource = _injector.getInstance(BackupResource.class);
    GroupDao groupDao = _injector.getInstance(GroupDao.class);
    UserDao userAdo = _injector.getInstance(UserDao.class);
    Group group = groupDao.findById(groupId);
    AppUser user = userAdo.findByUsername(userName);

    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
    RequestParams params = Mockito.mock(RequestParams.class);

    when(request.getAttribute(BaseResource.REQUEST_PARAMS)).thenReturn(params);
    when(params.getCurrentGroup()).thenReturn(group);
    when(params.getAppUser()).thenReturn(user);

    Response methodResponse = backupResource.dashboardPage(request, response, groupId);
    LOG.info("{}", methodResponse.getEntity());
  }

  public static void main(String[] args) throws Exception {

    for (String arg : args) {
      LOG.info(arg);
    }

    RunOneMethod methodRunner = new RunOneMethod();
    methodRunner.runMethod(args[0], args[1]);
  }

  static final AbstractModule MODULE = new MinimalModule();
}
