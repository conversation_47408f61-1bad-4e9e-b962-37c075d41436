package com.xgen.svc.mms.misc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.xgen.cloud.authz.core._public.client.UserGroupClient;
import com.xgen.cloud.authz.core._public.model.PolicyAssignment;
import com.xgen.cloud.authz.core._public.model.UserGroup;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.resourcetags._public.model.ResourceId;
import com.xgen.cloud.team._private.dao.TeamDaoT;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.team._public.svc.CloudTeamSvc;
import com.xgen.cloud.usergroupsync._public.sync.UserGroupSyncSvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.misc.authzconsistency.ReadWriteBatchResults;
import com.xgen.svc.mms.misc.authzconsistency.ResourceConsistencyArgs;
import com.xgen.svc.mms.misc.authzconsistency.ResourceConsistencyArgs.ArgumentsBuilder;
import com.xgen.svc.mms.misc.authzconsistency.TeamConsistency;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class TeamConsistencyIntTests extends JUnit5BaseSvcTest {

  @Inject private CloudTeamSvc teamSvc;
  @Inject private TeamDaoT teamDao;
  @Inject private OrganizationDao organizationDao;
  @Mock private UserGroupClient userGroupClient;
  @Mock private UserGroupSyncSvc userGroupSyncSvc;

  private TeamConsistency teamConsistency;
  private Team team1;
  private Team team2;
  private Team team3;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    teamConsistency =
        new TeamConsistency(userGroupClient, userGroupSyncSvc, teamSvc, organizationDao);
    Organization org1 = MmsFactory.createOrganizationWithNDSPlan();
    Group group1 = MmsFactory.createGroup(org1);
    team1 = MmsFactory.createTeam(org1, "team1");
    MmsFactory.addTeamToGroup(team1, group1);
    team2 = MmsFactory.createTeam(org1, "team2");
    MmsFactory.addTeamToGroup(team2, group1);
    Organization org2 = MmsFactory.createOrganizationWithNDSPlan();
    team3 = MmsFactory.createTeam(org2, "team3");
  }

  @Test
  public void testCheckAllTeams_userGroupIdNull() {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroups(true).batchSize(100).build();
    mockUserGroupClient(Set.of(team1, team2));
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(1, results.missing().size());
    assertTrue(results.missing().contains(team3));
  }

  @Test
  public void testCheckAllTeams_userGroupIdNotInAuthz() {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroups(true).batchSize(100).build();
    mockUserGroupClient(Set.of(team1, team2));
    teamDao.setUserGroup(team3.getId(), ObjectId.get(), new Date());
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(1, results.missing().size());
    assertTrue(results.missing().stream().anyMatch(team -> team.getId().equals(team3.getId())));
  }

  @Test
  public void testCheckAllTeams_allUserGroupsExist() {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroups(true).batchSize(100).build();
    mockUserGroupClient(Set.of(team1, team2, team3));
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(0, results.missing().size());
    assertEquals(0, results.outOfSyncIds().size());
  }

  @Test
  public void testCheckAllTeams_policies_allUserGroupsExistAndMatch() {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroupsPolicies(true).batchSize(100).build();
    mockUserGroupClient(Set.of(team1, team2, team3));
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(0, results.missing().size());
  }

  @Test
  public void testCheckAllTeamsPolicies_allUserGroupsExistButWrongPolicies() {
    Set<Team> teamsInAuthz = Set.of(team1, team2, team3);
    Set<UserGroup> userGroups = new HashSet<>();
    teamsInAuthz.forEach(
        team -> {
          ObjectId userGroupId = new ObjectId();
          teamDao.setUserGroup(team.getId(), userGroupId, new Date());
          userGroups.add(
              new UserGroup(
                  userGroupId,
                  team.getOrgId(),
                  team.getName(),
                  Set.of(
                      new PolicyAssignment(
                          "policy", new ResourceId("service", "type", ObjectId.get(), null))),
                  0));
        });
    doReturn(userGroups).when(userGroupClient).getUserGroupsByIds(anySet());
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroupsPolicies(true).batchSize(100).build();
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(0, results.missing().size());
    assertEquals(3, results.outOfSyncIds().size());
  }

  @Test
  public void testCheckAllTeamsPolicies_allUserGroupsExistButOneWrongPolicy() {
    Set<Team> teamsInAuthz = Set.of(team1, team2);
    Set<UserGroup> userGroups = new HashSet<>();
    teamsInAuthz.forEach(
        team -> {
          ObjectId userGroupId = new ObjectId();
          teamDao.setUserGroup(team.getId(), userGroupId, new Date());
          userGroups.add(
              new UserGroup(
                  userGroupId,
                  team.getOrgId(),
                  team.getName(),
                  userGroupSyncSvc.getTeamPolicyAssignments(team.getId()),
                  0));
        });
    ObjectId userGroupId = new ObjectId();
    teamDao.setUserGroup(team3.getId(), userGroupId, new Date());
    userGroups.add(
        new UserGroup(
            userGroupId,
            team3.getOrgId(),
            team3.getName(),
            Set.of(
                new PolicyAssignment(
                    "policy", new ResourceId("service", "type", ObjectId.get(), null))),
            0));
    doReturn(userGroups).when(userGroupClient).getUserGroupsByIds(anySet());
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroupsPolicies(true).batchSize(100).build();
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(0, results.missing().size());
    assertEquals(1, results.outOfSyncIds().size());
  }

  @Test
  public void testCheckAllTeamsPolicies_allUserGroupsExistButWrongPolicies_performWrites()
      throws SvcException {
    Set<Team> teamsInAuthz = Set.of(team1, team2, team3);
    Set<UserGroup> userGroups = new HashSet<>();
    teamsInAuthz.forEach(
        team -> {
          ObjectId userGroupId = new ObjectId();
          teamDao.setUserGroup(team.getId(), userGroupId, new Date());
          userGroups.add(
              new UserGroup(
                  userGroupId,
                  team.getOrgId(),
                  team.getName(),
                  Set.of(
                      new PolicyAssignment(
                          "policy", new ResourceId("service", "type", ObjectId.get(), null))),
                  0));
        });
    doReturn(userGroups).when(userGroupClient).getUserGroupsByIds(anySet());
    ResourceConsistencyArgs args =
        new ArgumentsBuilder()
            .checkUserGroupsPolicies(true)
            .performWrites(true)
            .batchSize(100)
            .build();
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(0, results.missing().size());
    assertEquals(0, results.outOfSyncIds().size());
    assertEquals(3, results.createdIds().size());
    verify(userGroupSyncSvc, times(3)).syncCreateOrUpdateToUserGroup(any(), any());
  }

  @Test
  public void testCheckAllTeamsPolicies_allUserGroupsExistButOneWrongPolicy_performWrites()
      throws SvcException {
    Set<Team> teamsInAuthz = Set.of(team1, team2);
    Set<UserGroup> userGroups = new HashSet<>();
    teamsInAuthz.forEach(
        team -> {
          ObjectId userGroupId = new ObjectId();
          teamDao.setUserGroup(team.getId(), userGroupId, new Date());
          userGroups.add(
              new UserGroup(
                  userGroupId,
                  team.getOrgId(),
                  team.getName(),
                  userGroupSyncSvc.getTeamPolicyAssignments(team.getId()),
                  0));
        });
    ObjectId userGroupId = new ObjectId();
    teamDao.setUserGroup(team3.getId(), userGroupId, new Date());
    userGroups.add(
        new UserGroup(
            userGroupId,
            team3.getOrgId(),
            team3.getName(),
            Set.of(
                new PolicyAssignment(
                    "policy", new ResourceId("service", "type", ObjectId.get(), null))),
            0));
    doReturn(userGroups).when(userGroupClient).getUserGroupsByIds(anySet());
    ResourceConsistencyArgs args =
        new ArgumentsBuilder()
            .checkUserGroupsPolicies(true)
            .performWrites(true)
            .batchSize(100)
            .build();
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(0, results.missing().size());
    assertEquals(0, results.outOfSyncIds().size());
    assertEquals(1, results.createdIds().size());
    verify(userGroupSyncSvc, times(1)).syncCreateOrUpdateToUserGroup(any(), any());
  }

  @Test
  public void testCheckAllTeams_noUserGroupExists() {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroups(true).batchSize(100).build();
    teamDao.setUserGroup(team1.getId(), ObjectId.get(), new Date());
    teamDao.setUserGroup(team2.getId(), ObjectId.get(), new Date());
    teamDao.setUserGroup(team3.getId(), ObjectId.get(), new Date());
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    assertEquals(3, results.missing().size());
  }

  @Test
  public void testCheckAllTeams_noUserGroupExists_performWrites() throws SvcException {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder().checkUserGroups(true).performWrites(true).batchSize(100).build();
    teamDao.setUserGroup(team1.getId(), ObjectId.get(), new Date());
    teamDao.setUserGroup(team2.getId(), ObjectId.get(), new Date());
    teamDao.setUserGroup(team3.getId(), ObjectId.get(), new Date());
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    verify(userGroupSyncSvc, times(3)).syncCreateOrUpdateToUserGroup(any(), any());
    assertEquals(3, results.createdIds().size());
    assertTrue(
        results
            .createdIds()
            .containsAll(
                Set.of(
                    team1.getId().toHexString(),
                    team2.getId().toHexString(),
                    team3.getId().toHexString())));
  }

  @Test
  public void testCheckAllTeams_userGroupDifferentPolicies_performWrites() throws SvcException {
    ResourceConsistencyArgs args =
        new ArgumentsBuilder()
            .checkUserGroupsPolicies(true)
            .performWrites(true)
            .batchSize(100)
            .build();
    teamDao.setUserGroup(team1.getId(), ObjectId.get(), new Date());
    teamDao.setUserGroup(team2.getId(), ObjectId.get(), new Date());
    teamDao.setUserGroup(team3.getId(), ObjectId.get(), new Date());
    ReadWriteBatchResults<Team> results = teamConsistency.checkAllTeams(args);
    verify(userGroupSyncSvc, times(3)).syncCreateOrUpdateToUserGroup(any(), any());
    assertEquals(3, results.createdIds().size());
    assertTrue(
        results
            .createdIds()
            .containsAll(
                Set.of(
                    team1.getId().toHexString(),
                    team2.getId().toHexString(),
                    team3.getId().toHexString())));
  }

  private void mockUserGroupClient(Set<Team> teamsInAuthz) {
    Set<UserGroup> userGroups = new HashSet<>();
    teamsInAuthz.forEach(
        team -> {
          ObjectId userGroupId = new ObjectId();
          teamDao.setUserGroup(team.getId(), userGroupId, new Date());
          userGroups.add(
              new UserGroup(
                  userGroupId,
                  team.getOrgId(),
                  team.getName(),
                  userGroupSyncSvc.getTeamPolicyAssignments(team.getId()),
                  0));
        });
    doReturn(userGroups).when(userGroupClient).getUserGroupsByIds(anySet());
  }
}
