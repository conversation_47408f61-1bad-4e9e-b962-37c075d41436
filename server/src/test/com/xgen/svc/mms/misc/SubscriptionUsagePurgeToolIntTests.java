package com.xgen.svc.mms.misc;

import static org.assertj.core.api.Assertions.assertThat;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.nds.billing._public.model.SubscriptionUsage;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.SubscriptionUsageDaoV2;
import com.xgen.svc.mms.misc.SubscriptionUsagePurgeTool.ToolOptions;
import com.xgen.svc.mms.svc.atlasbilling.SubscriptionUsagePurgeSvc.SubscriptionUsagePurgeResult;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

public class SubscriptionUsagePurgeToolIntTests extends JUnit5BaseSvcTest {
  private static final Instant NOW = Instant.now();
  private static final ObjectId GROUP_ID = new ObjectId();
  private static final Duration MIN_EXPIRATION_DURATION = Duration.ofDays(180);
  @Inject private SubscriptionUsageDaoV2 _subscriptionUsageDao;
  @Inject private SubscriptionUsagePurgeTool _subscriptionUsagePurgeTool;
  @Inject private AppSettings _appSettings;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _appSettings.setProp(
        Fields.NDS_BILLING_PURGE_MIN_EXPIRATION_DURATION_DAYS.value,
        String.valueOf(MIN_EXPIRATION_DURATION.toDays()),
        SettingType.MEMORY);
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testExecute(boolean pIsDryRun) {
    final List<Date> dates =
        Stream.of(4, 3, 2, 1)
            .map(
                hourOffset ->
                    Date.from(
                        NOW.minus(hourOffset, ChronoUnit.HOURS).minus(MIN_EXPIRATION_DURATION)))
            .toList();
    final int hoursToDelete = 3;
    final Date expireDate = dates.get(hoursToDelete);
    final int numDocumentsPerDate = 250;

    createSubscriptionUsagesFromHours(dates, numDocumentsPerDate);

    final ToolOptions options =
        new ToolOptions(pIsDryRun, false, 15, 3, 5, 5_000, 25, 10, expireDate);

    final SubscriptionUsagePurgeResult results =
        _subscriptionUsagePurgeTool.execute(Date.from(NOW), options);

    assertThat(results.totalDocumentsDeleted())
        .isEqualTo(numDocumentsPerDate * hoursToDelete * (pIsDryRun ? 0 : 1));
    assertThat(results.totalDocumentsExpired()).isEqualTo(numDocumentsPerDate * 3);

    // Ensure documents after date were not deleted
    assertThat(
            _subscriptionUsageDao
                .findInTimeRangeByGroupIds(List.of(GROUP_ID), dates.get(3), Date.from(NOW))
                .size())
        .isEqualTo(numDocumentsPerDate);
  }

  @Test
  public void testExecute_foundMoreHoursThanMaxHours() {
    final List<Date> dates =
        Stream.of(4, 3, 2, 1)
            .map(
                hourOffset ->
                    Date.from(
                        NOW.minus(hourOffset, ChronoUnit.HOURS).minus(MIN_EXPIRATION_DURATION)))
            .toList();
    final int hoursToDelete = 3;
    final Date expireDate = dates.get(hoursToDelete);
    final int numDocumentsPerDate = 250;

    createSubscriptionUsagesFromHours(dates, numDocumentsPerDate);

    final ToolOptions options = new ToolOptions(false, false, 15, 5, 1, 5_000, 25, 10, expireDate);
    final SubscriptionUsagePurgeResult result =
        _subscriptionUsagePurgeTool.execute(Date.from(NOW), options);
    assertThat(SubscriptionUsagePurgeResult.PURGE_SKIPPED_RESULT).isEqualTo(result);

    // Ensure no documents were deleted
    assertThat(
            _subscriptionUsageDao
                .findInTimeRangeByGroupIds(List.of(GROUP_ID), dates.get(0), Date.from(NOW))
                .size())
        .isEqualTo(numDocumentsPerDate * dates.size());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testExecute_withLimit(final boolean pIsDryRun) {
    final List<Date> dates =
        Stream.of(4, 3, 2, 1)
            .map(
                hourOffset ->
                    Date.from(
                        NOW.minus(hourOffset, ChronoUnit.HOURS).minus(MIN_EXPIRATION_DURATION)))
            .toList();
    final Date expireDate = dates.get(3);
    final int numDocumentsPerDate = 250;

    createSubscriptionUsagesFromHours(dates, numDocumentsPerDate);

    final int limit = 100;
    final ToolOptions options =
        new ToolOptions(pIsDryRun, false, 15, 3, 5, limit, 25, 10, expireDate);

    final SubscriptionUsagePurgeResult results =
        _subscriptionUsagePurgeTool.execute(Date.from(NOW), options);

    assertThat(results.totalDocumentsDeleted()).isEqualTo(limit * (pIsDryRun ? 0 : 1));
    assertThat(results.totalDocumentsExpired()).isEqualTo(numDocumentsPerDate * 3);

    // Ensure documents after date were not deleted
    assertThat(
            _subscriptionUsageDao
                .findInTimeRangeByGroupIds(List.of(GROUP_ID), dates.get(3), Date.from(NOW))
                .size())
        .isEqualTo(numDocumentsPerDate);
  }

  private void createSubscriptionUsagesFromHours(
      final List<Date> pDates, final int numDocumentsPerDate) {
    pDates.forEach(
        pDate ->
            IntStream.range(0, numDocumentsPerDate)
                .forEach(
                    i -> _subscriptionUsageDao.save(createSubscriptionUsageWithStartTime(pDate))));
  }

  private static SubscriptionUsage createSubscriptionUsageWithStartTime(final Date startTime) {
    return new SubscriptionUsage.Builder()
        .groupId(GROUP_ID)
        .startTime(startTime)
        .endTime(Date.from(NOW.minus(MIN_EXPIRATION_DURATION)))
        .build();
  }
}
