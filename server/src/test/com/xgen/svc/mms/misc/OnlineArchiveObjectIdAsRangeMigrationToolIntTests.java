package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStoreProvider.ProviderValues;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDataLakeConfigDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveDataLakeConfig;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Collection;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.DataSource;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Database;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Store;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveDataLakeConfigSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Stream;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class OnlineArchiveObjectIdAsRangeMigrationToolIntTests extends BaseSvcTest {

  @Inject private OnlineArchiveSvc _onlineArchiveSvc;
  @Inject private OnlineArchiveDataLakeConfigSvc _onlineArchiveDataLakeConfigSvc;
  @Inject private NDSDataLakeTenantSvc _ndsDataLakeTenantSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private OnlineArchiveDataLakeConfigDao _onlineArchiveDataLakeConfigDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  private static final Pattern OBJECTID_DISCRETE_PATTERN_LEGACY =
      Pattern.compile("ObjectID\\(\"\\{.+ objectid\\}\"\\)");
  private static final Pattern OBJECTID_RANGE_PATTERN =
      Pattern.compile("\\{min\\(.+\\) objectid\\}-\\{max\\(.+\\) objectid\\}");
  private static final Pattern OBJECTID_DISCRETE_PATTERN_CURRENT =
      Pattern.compile("\\{.+ objectid\\}");

  @Before
  public void setup() throws Exception {
    super.setUp();

    // Set up a group and cluster with 2 online archives whose metadata has resolved,
    // i.e. archives with existing data sources in the storage config
    // One archive has an ObjectID partition field and will be migrated,
    // the other archive does not and will not be migrated.
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveObjectIdAsRangeMigrationToolIntTests/groups.json.ftl",
        null,
        "mmsdbconfig",
        "config.customers");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveObjectIdAsRangeMigrationToolIntTests/ndsGroups.json.ftl",
        null,
        "nds",
        "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveObjectIdAsRangeMigrationToolIntTests/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveObjectIdAsRangeMigrationToolIntTests/onlineArchiveDataLakeConfigs.json.ftl",
        null,
        "nds",
        "config.nds.onlineArchiveDataLakeConfigs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveObjectIdAsRangeMigrationToolIntTests/onlineArchives.json.ftl",
        null,
        "nds",
        "config.nds.onlineArchives");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/OnlineArchiveObjectIdAsRangeMigrationToolIntTests/queryEngineTenants.json.ftl",
        null,
        "nds",
        "config.nds.queryEngineTenants");

    // start mock admin API
    _dataLakeTestUtils.setUp();
    // seed with existing tenant storage configs
    seedTestStorage();
  }

  @After
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testProcessOnlineArchives() throws Exception {
    verifyStorageConfigDataSources(oid(8), false);
    verifyStorageConfigDataSources(oid(9), false);

    final OnlineArchiveObjectIdAsRangeMigrationTool tool =
        new OnlineArchiveObjectIdAsRangeMigrationTool(
            _onlineArchiveDao,
            _onlineArchiveSvc,
            _onlineArchiveDataLakeConfigDao,
            _clusterDescriptionDao);

    assertEquals(1, tool.getOnlineArchives().size());

    tool.processOnlineArchives();

    verifyStorageConfigDataSources(oid(8), true);
    verifyStorageConfigDataSources(oid(9), true);

    // Verify idempotency

    tool.processOnlineArchives();

    verifyStorageConfigDataSources(oid(8), true);
    verifyStorageConfigDataSources(oid(9), true);
  }

  private void verifyStorageConfigDataSources(
      final ObjectId pArchiveId, final boolean pPostMigration) throws Exception {
    final OnlineArchive onlineArchive = _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);
    final OnlineArchiveDataLakeConfig onlineArchiveDataLakeConfig =
        _onlineArchiveDataLakeConfigSvc
            .getOnlineArchiveDataLakeConfig(
                onlineArchive.getGroupId(), onlineArchive.getClusterName())
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        "Cannot find OnlineArchiveDataLakeConfig of OnlineArchive"));

    Stream.of(
            onlineArchiveDataLakeConfig.getDataLakeTenantId(),
            onlineArchiveDataLakeConfig.getArchiveOnlyDataLakeTenantId())
        .forEach(
            tenantId -> {
              final NDSDataLakeStorageV1View storage;
              try {
                final NDSDataLakeTenant tenant =
                    _ndsDataLakeTenantSvc
                        .findByDataLakeTenantId(tenantId)
                        .orElseThrow(
                            () ->
                                new SvcException(
                                    NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME,
                                    tenantId.getGroupId(),
                                    tenantId.getName()));
                storage = _ndsDataLakeTenantSvc.getStorageConfig(tenant);
              } catch (final SvcException e) {
                throw new IllegalStateException(e);
              }

              final List<Document> s3dataSources =
                  storage
                      .getCollectionDataSources(
                          onlineArchive.getDbName(), onlineArchive.getCollName())
                      .stream()
                      .filter(_dataLakeTestUtils::isOnlineArchiveS3DataSource)
                      .toList();

              final boolean archiveHasObjectIdPartitionField =
                  onlineArchive.getPartitionFields().stream()
                      .anyMatch(
                          partitionField ->
                              partitionField.getFieldType().equals(Optional.of("objectId")));

              if (archiveHasObjectIdPartitionField) {
                if (pPostMigration) {
                  // 3 data sources
                  assertEquals(1, s3dataSources.size());

                  final String sourcePath = s3dataSources.get(0).getString(DataSource.PATH);

                  // data source with ObjectID as range in path
                  assertFalse(OBJECTID_DISCRETE_PATTERN_LEGACY.matcher(sourcePath).find());
                  assertTrue(OBJECTID_DISCRETE_PATTERN_CURRENT.matcher(sourcePath).find());
                  assertTrue(OBJECTID_RANGE_PATTERN.matcher(sourcePath).find());
                } else {
                  // 1 data source with discrete ObjectID in path
                  assertEquals(1, s3dataSources.size());

                  final String sourcePath = s3dataSources.get(0).getString(DataSource.PATH);

                  assertTrue(OBJECTID_DISCRETE_PATTERN_LEGACY.matcher(sourcePath).find());
                }
              } else {
                // 1 data source without ObjectID in path
                assertEquals(1, s3dataSources.size());

                final String sourcePath = s3dataSources.get(0).getString(DataSource.PATH);

                assertFalse(OBJECTID_DISCRETE_PATTERN_LEGACY.matcher(sourcePath).find());
                assertFalse(OBJECTID_RANGE_PATTERN.matcher(sourcePath).find());
              }
            });
  }

  private void seedTestStorage() throws DataLakeAdminApiException {
    final Document storageConfig = new Document();
    storageConfig.append(
        StorageConfigFieldDefs.DATABASES,
        List.of(
            new Document(Database.NAME, "myFirstDatabase")
                .append(
                    Database.COLLECTIONS,
                    List.of(
                        new Document(Collection.NAME, "myFirstCollection")
                            .append(
                                Collection.DATA_SOURCES,
                                List.of(
                                    new Document(
                                            DataSource.STORE_NAME,
                                            OnlineArchiveDataLakeConfigSvc
                                                .ONLINE_ARCHIVE_TENANT_S3_STORE_NAME)
                                        .append(DataSource.OMIT_ATTRIBUTES, true)
                                        .append(
                                            DataSource.PATH,
                                            "/myFirstDatabase/9ebb0a76-5a79-473b-a81f-c39b2263cffb/000000000000000000000008/{min(dateField)"
                                                + " epoch_millis}-{max(dateField)"
                                                + " epoch_millis}/ObjectID(\"{_id"
                                                + " objectid}\")/{field1 string}"),
                                    new Document(
                                            DataSource.STORE_NAME,
                                            OnlineArchiveDataLakeConfigSvc
                                                .ONLINE_ARCHIVE_TENANT_ATLAS_STORE_NAME)
                                        .append(DataSource.DATABASE, "myFirstDatabase")
                                        .append(DataSource.COLLECTION, "myFirstCollection"))),
                        new Document(Collection.NAME, "mySecondCollection")
                            .append(
                                Collection.DATA_SOURCES,
                                List.of(
                                    new Document(
                                            DataSource.STORE_NAME,
                                            OnlineArchiveDataLakeConfigSvc
                                                .ONLINE_ARCHIVE_TENANT_S3_STORE_NAME)
                                        .append(DataSource.OMIT_ATTRIBUTES, true)
                                        .append(
                                            DataSource.PATH,
                                            "/myFirstDatabase/d89ffc23-3e76-4e7b-99a8-060bfc4a932d/000000000000000000000009/{min(dateField)"
                                                + " epoch_millis}-{max(dateField)"
                                                + " epoch_millis}/{field1 string}/{min(field2)"
                                                + " int}-{max(field2) int}"),
                                    new Document(
                                            DataSource.STORE_NAME,
                                            OnlineArchiveDataLakeConfigSvc
                                                .ONLINE_ARCHIVE_TENANT_ATLAS_STORE_NAME)
                                        .append(DataSource.DATABASE, "myFirstDatabase")
                                        .append(DataSource.COLLECTION, "mySecondCollection")))))));
    storageConfig.append(
        StorageConfigFieldDefs.STORES,
        List.of(
            new Document(Store.PROVIDER, ProviderValues.S3)
                .append(Store.BUCKET, "atlas-online-archive-local-us-east-1")
                .append(Store.PREFIX, "/000000000000000000000003/000000000000000000000006")
                .append(Store.REGION, AWSRegionName.US_EAST_1.getName())
                .append(Store.REPLACEMENT_DELIMITER, "-")
                .append(Store.DOCUMENT_COUNT_METADATA_KEY, "x-mdb-document-count")
                .append(
                    Store.NAME, OnlineArchiveDataLakeConfigSvc.ONLINE_ARCHIVE_TENANT_S3_STORE_NAME),
            new Document(Store.PROVIDER, ProviderValues.ATLAS)
                .append(Store.CLUSTER_NAME, "Cluster0")
                .append(Store.PROJECT_ID, oid(3))
                .append(
                    Store.NAME,
                    OnlineArchiveDataLakeConfigSvc.ONLINE_ARCHIVE_TENANT_ATLAS_STORE_NAME)));

    final NDSDataLakeStorageV1View storage = new NDSDataLakeStorageV1View(storageConfig);

    for (final NDSDataLakeTenant tenant : _ndsDataLakeTenantSvc.findTenantsByGroupId(oid(3))) {
      _ndsDataLakeTenantSvc.updateStorageConfig(tenant, storage, AuditInfoHelpers.fromSystem());
    }
  }
}
