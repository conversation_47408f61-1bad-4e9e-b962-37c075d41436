package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;

import com.mongodb.DuplicateKeyException;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZone;
import com.xgen.cloud.nds.azure._public.model.AzureRegion;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.svc.nds.azure.AzureExternalIntTest;
import jakarta.inject.Inject;
import org.junit.Test;

public class AzureSubscriptionCreationToolExternalIntTests extends AzureExternalIntTest {
  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private AzureApiSvc _azureApiSvc;

  @Override
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void test_generateAzureSubscription() {
    final AzureSubscriptionCreationTool tool =
        new AzureSubscriptionCreationTool(_azureSubscriptionDao, _azureApiSvc);

    final String newSubscriptionId = getAppSettings().getStrProp(NDS_AZURE_TESTING_SUBSCRIPTION_ID);

    // The tool assumes there was an Azure Subscription to begin with.
    final AzureSubscription sourceAzureSubscription = getAzureSubscription();

    // No valid source account name to copy from means nothing was generated
    {
      assertThrows(
          IllegalStateException.class,
          () ->
              tool.generateAzureSubscription(
                      new AzureSubscriptionCreationTool.ToolOptions.Builder()
                          .sourceName("fake-source-name")
                          .targetName("cloud-prod-2")
                          .subscriptionId(newSubscriptionId)
                          .build())
                  .isEmpty());
    }

    // noop without commit should return but not persist the new account
    {
      final AzureSubscription generatedAzureSubscription =
          tool.generateAzureSubscription(
                  (new AzureSubscriptionCreationTool.ToolOptions.Builder())
                      .sourceName(sourceAzureSubscription.getName())
                      .targetName("cloud-prod-2")
                      .subscriptionId(newSubscriptionId)
                      .build())
              .orElseThrow();

      assertEquals("cloud-prod-2", generatedAzureSubscription.getName());
      assertEquals(newSubscriptionId, generatedAzureSubscription.getSubscriptionId());
      assertEquals(800L, generatedAzureSubscription.getCapacityRemaining());
      assertFalse(generatedAzureSubscription.getAssignmentEnabled());

      assertTrue(_azureSubscriptionDao.findByName(generatedAzureSubscription.getName()).isEmpty());
    }

    // commit should persist the new account
    {
      final AzureSubscription generatedAzureSubscription =
          tool.generateAzureSubscription(
                  (new AzureSubscriptionCreationTool.ToolOptions.Builder())
                      .sourceName(sourceAzureSubscription.getName())
                      .targetName("cloud-prod-2")
                      .subscriptionId(newSubscriptionId)
                      .commit(true)
                      .build())
              .get();

      assertEquals("cloud-prod-2", generatedAzureSubscription.getName());
      assertEquals(newSubscriptionId, generatedAzureSubscription.getSubscriptionId());
      assertEquals(800L, generatedAzureSubscription.getCapacityRemaining());
      assertFalse(generatedAzureSubscription.getAssignmentEnabled());

      validateRegionsAndAvailabilityZones(sourceAzureSubscription, generatedAzureSubscription);

      // Confirm it is persisted
      assertTrue(
          _azureSubscriptionDao.findByName(generatedAzureSubscription.getName()).isPresent());
    }

    // commit should throw exception if account name already exists
    {
      assertThrows(
          DuplicateKeyException.class,
          () ->
              tool.generateAzureSubscription(
                      (new AzureSubscriptionCreationTool.ToolOptions.Builder())
                          .sourceName(sourceAzureSubscription.getName())
                          .targetName("cloud-prod-2")
                          .subscriptionId(newSubscriptionId)
                          .commit(true)
                          .build())
                  .isEmpty());
    }
  }

  private void validateRegionsAndAvailabilityZones(
      final AzureSubscription pSourceAzureSubscription,
      final AzureSubscription pTargetAzureSubscription) {
    assertFalse(pTargetAzureSubscription.getRegions().isEmpty());
    assertEquals(
        pSourceAzureSubscription.getRegions().size(), pTargetAzureSubscription.getRegions().size());

    for (int i = 0; i < pTargetAzureSubscription.getRegions().size(); i++) {
      final AzureRegion sourceRegion = pSourceAzureSubscription.getRegions().get(i);
      final AzureRegion targetRegion = pTargetAzureSubscription.getRegions().get(i);

      assertTrue(targetRegion.getGroupIds().isEmpty());
      assertEquals(sourceRegion.getRegion(), targetRegion.getRegion());
      assertEquals(
          sourceRegion.getAvailabilityZones().size(), targetRegion.getAvailabilityZones().size());
      for (int j = 0; j < sourceRegion.getAvailabilityZones().size(); j++) {
        validateAvailabilityZone(
            sourceRegion.getAvailabilityZones().get(j), targetRegion.getAvailabilityZones().get(j));
      }
    }
  }

  private void validateAvailabilityZone(
      final AzureAvailabilityZone pSourceAz, final AzureAvailabilityZone pTargetAz) {
    assertEquals(pSourceAz.getName(), pTargetAz.getName());
    assertEquals(
        pSourceAz.getConstrainedFamilies().size(), pTargetAz.getConstrainedFamilies().size());

    for (int i = 0; i < pSourceAz.getConstrainedFamilies().size(); i++) {
      assertEquals(
          pSourceAz.getConstrainedFamilies().get(i), pTargetAz.getConstrainedFamilies().get(i));
    }
  }
}
