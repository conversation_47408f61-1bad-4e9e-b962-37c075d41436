package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.mms.misc.SuspendOrganizationsTool.Status;
import com.xgen.svc.mms.svc.billing.DelinquentOrganizationSvc;
import jakarta.inject.Inject;
import java.io.File;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class SuspendOrganizationsToolIntTests extends BaseSvcTest {

  private File _organizationsJSONFile;
  private File _csvOutputFile;
  private List<ObjectId> _orgIds;
  private List<ObjectId> _alreadyLockedOrgIds;
  private List<ObjectId> _notFoundOrgIds;

  @Inject private DelinquentOrganizationSvc _delinquentOrganizationSvc;
  @Inject private OrganizationSvc _organizationSvc;

  @Before
  public void setUp() throws Exception {
    super.setUp();

    _alreadyLockedOrgIds = new ArrayList<>();
    _notFoundOrgIds = new ArrayList<>();

    _organizationsJSONFile = File.createTempFile("organizations", "json");
    _organizationsJSONFile.deleteOnExit();

    _csvOutputFile = File.createTempFile("results", "csv");
    _csvOutputFile.delete();

    final List<Organization> organizations =
        IntStream.range(0, 10)
            .mapToObj(
                i ->
                    MmsFactory.createOrganizationWithNDSPlan(
                        String.format("Org-%s", i), new Date()))
            .collect(Collectors.toList());

    final JSONArray organizationJSONArray = new JSONArray();
    organizations.stream()
        .map(org -> new JSONObject().put("id", org.getId()).put("name", org.getName()))
        .forEach(organizationJSONArray::put);

    _notFoundOrgIds.add(ObjectId.get());

    _notFoundOrgIds.forEach(
        username ->
            organizationJSONArray.put(
                new JSONObject().put("id", ObjectId.get()).put("name", "FakeOrg")));

    _orgIds =
        Stream.concat(organizations.stream().map(Organization::getId), _notFoundOrgIds.stream())
            .collect(Collectors.toList());

    // create the JSON file
    try (final FileWriter fileWriter = new FileWriter(_organizationsJSONFile)) {
      fileWriter.write(organizationJSONArray.toString());
    }

    // set two organizations as already locked
    IntStream.range(2, 4)
        .mapToObj(organizations::get)
        .forEach(
            org -> {
              getDelinquentOrganizationSvc()
                  .suspend(org, AuditInfoHelpers.fromSystem(), LocalDate.now());

              _alreadyLockedOrgIds.add(org.getId());
            });
  }

  @After
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    Optional.ofNullable(_organizationsJSONFile).filter(File::exists).ifPresent(File::delete);
    Optional.ofNullable(_csvOutputFile).filter(File::exists).ifPresent(File::delete);
  }

  private void testTool(final int pLimit) throws Exception {
    final SuspendOrganizationsTool suspendOrganizationsTool =
        new SuspendOrganizationsTool(_delinquentOrganizationSvc, _organizationSvc);

    suspendOrganizationsTool.run(
        _organizationsJSONFile.getPath(), pLimit, _csvOutputFile.getPath());

    final int expectedSuspendCount =
        (pLimit == 0 || pLimit > _orgIds.size()
            ? _orgIds.size() - _notFoundOrgIds.size()
            : pLimit + _alreadyLockedOrgIds.size());

    // confirm CSV results
    assertTrue(_csvOutputFile.exists());
    final List<String> csvRows = Files.readAllLines(Paths.get(_csvOutputFile.toURI()));

    // verify the total number of expected rows (+1 to include the header)
    final int expectedTotalRows =
        (pLimit > 0 & pLimit < _orgIds.size()
                ? pLimit + _alreadyLockedOrgIds.size() + _notFoundOrgIds.size()
                : _orgIds.size())
            + 1;

    assertEquals(expectedTotalRows, csvRows.size());

    // verify the header
    assertEquals(SuspendOrganizationsTool.CSV_HEADER_LINE, csvRows.get(0));

    // verify the field count for each row by delimiter (expected number of fields - 1)
    csvRows.forEach(r -> assertEquals(4, StringUtils.countMatches(r, ",")));

    // verify number USER_NOT_FOUND
    assertEquals(
        _notFoundOrgIds.size(), getCountWithStatus(csvRows, Status.ORGANIZATION_NOT_FOUND));

    // verify number USER_ALREADY_LOCKED
    assertEquals(
        _alreadyLockedOrgIds.size(),
        getCountWithStatus(csvRows, Status.ORGANIZATION_ALREADY_SUSPENDED));

    // verify number SUCCESS
    assertEquals(
        expectedSuspendCount - _alreadyLockedOrgIds.size(),
        getCountWithStatus(csvRows, Status.SUCCESS));

    // Expected number of locked orgs
    assertEquals(
        expectedSuspendCount,
        _orgIds.stream()
            .filter(u -> !_notFoundOrgIds.contains(u))
            .map(getOrganizationSvc()::findById)
            .map(Organization::isPaymentStatusSuspended)
            .filter(Boolean::booleanValue)
            .count());

    // Expected number of admin locked orgs
    assertEquals(
        expectedSuspendCount - _alreadyLockedOrgIds.size(),
        _orgIds.stream()
            .filter(u -> !_notFoundOrgIds.contains(u))
            .map(getOrganizationSvc()::findById)
            .map(
                o ->
                    o.isPaymentStatusSuspended()
                        && o.getPaymentStatus().getDisableDunningRecovery())
            .filter(Boolean::booleanValue)
            .count());
  }

  private long getCountWithStatus(final List<String> pCSVRows, final Status pStatus) {
    return pCSVRows.stream().filter(r -> r.contains(pStatus.name())).count();
  }

  @Test
  public void testTool_NoLimit() throws Exception {
    testTool(0);
  }

  @Test
  public void testTool_WithLimitLessThanPossiblePauses() throws Exception {
    testTool(3);
  }

  @Test
  public void testTool_WithLimitGreaterThanPossiblePauses() throws Exception {
    testTool(99);
  }

  private DelinquentOrganizationSvc getDelinquentOrganizationSvc() {
    return _delinquentOrganizationSvc;
  }

  private OrganizationSvc getOrganizationSvc() {
    return _organizationSvc;
  }
}
