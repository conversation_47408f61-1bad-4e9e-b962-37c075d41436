package com.xgen.svc.mms.misc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessDataLakeFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.featureid.NDSCloudProviderAccessFeatureUsageDataLakeFeatureId;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseSvcTest;
import jakarta.inject.Inject;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class CreateCloudProviderAccessRolesToolIntTests extends BaseSvcTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private NDSDataLakeTenantDao _dataLakeTenantDao;
  @Inject private AppSettings _appSettings;

  private CreateCloudProviderAccessRolesTool _createCloudProviderAccessRolesTool;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/CreateCloudProviderAccessRolesToolIntTests/ndsGroups.json.ftl",
        null,
        "nds",
        "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/CreateCloudProviderAccessRolesToolIntTests/awsAccounts.json.ftl",
        null,
        "nds",
        "config.nds.awsAccounts");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/misc/CreateCloudProviderAccessRolesToolIntTests/dataLakeTenants.json.ftl",
        null,
        "nds",
        "config.nds.queryEngineTenants");

    _createCloudProviderAccessRolesTool =
        new CreateCloudProviderAccessRolesTool(
            _ndsGroupDao, _awsAccountDao, _dataLakeTenantDao, _appSettings);
  }

  @Test
  public void testMigrationToolLimit1() throws FileNotFoundException {
    final String csvFileName =
        String.format("/tmp/migrated-datalake-tenant-details-%s.csv", Instant.now().toString());

    _createCloudProviderAccessRolesTool
        .getAppSettings()
        .setProp(
            "queryengine.aws.assumeRoleUserARN",
            "arn:aws:iam::************:user/queryengine",
            AppSettings.SettingType.MEMORY);

    final List<AWSAccount> awsAccountBeforeMigration =
        _createCloudProviderAccessRolesTool.getAwsAccountDao().findAllAsBasicList().stream()
            .map(AWSAccount::new)
            .collect(Collectors.toList());
    assertEquals(3, awsAccountBeforeMigration.size());
    final List<NDSGroup> ndsGroupsBeforeMigration =
        _createCloudProviderAccessRolesTool.getNdsGroupDao().findAllAsBasicList().stream()
            .map(NDSGroup::new)
            .collect(Collectors.toList());
    assertEquals(4, ndsGroupsBeforeMigration.size());
    final List<NDSDataLakeTenant> dataLakeTenantsBeforeMigration =
        _createCloudProviderAccessRolesTool.getDataLakeTenantDao().findAll();
    assertEquals(11, dataLakeTenantsBeforeMigration.size());

    _createCloudProviderAccessRolesTool.run(1, csvFileName);

    final List<AWSAccount> awsAccountAfterMigration =
        _createCloudProviderAccessRolesTool.getAwsAccountDao().findAllAsBasicList().stream()
            .map(AWSAccount::new)
            .collect(Collectors.toList());
    assertEquals(3, awsAccountAfterMigration.size());
    final List<NDSGroup> ndsGroupsAfterMigration =
        _createCloudProviderAccessRolesTool.getNdsGroupDao().findAllAsBasicList().stream()
            .map(NDSGroup::new)
            .collect(Collectors.toList());
    assertEquals(4, ndsGroupsAfterMigration.size());
    final List<NDSDataLakeTenant> dataLakeTenantsAfterMigration =
        _createCloudProviderAccessRolesTool.getDataLakeTenantDao().findAll();
    assertEquals(11, dataLakeTenantsAfterMigration.size());

    final AWSAccount awsAccountUsed = awsAccountAfterMigration.get(1);

    // Data Lake tenant10 - do not migrate (state: UNVERIFIED)
    assertEquals(
        ndsGroupsBeforeMigration.get(0).getCloudProviderAccess(),
        ndsGroupsAfterMigration.get(0).getCloudProviderAccess());
    assertEquals(dataLakeTenantsBeforeMigration.get(0), dataLakeTenantsAfterMigration.get(0));

    // Data Lake tenant20 - create role and add feature usage
    // ensure tenant unchanged, except cloudproviderConfig.aws.roleId (tested further down)
    final NDSDataLakeTenant tenant20 = dataLakeTenantsAfterMigration.get(1);
    assertTenantsEqualExceptRoleId(dataLakeTenantsBeforeMigration.get(1), tenant20);

    final NDSGroup group2 = ndsGroupsAfterMigration.get(1);
    final List<NDSCloudProviderAccessAWSIAMRole> group2Roles =
        group2.getCloudProviderAccess().getAwsIamRoles();
    assertEquals(2, group2Roles.size());

    // ensure group2role1 remains unchanged (except featureUsages, modified when migrating tenant30)
    final NDSCloudProviderAccessAWSIAMRole group2Role1 = group2Roles.get(0);
    assertEquals(
        ndsGroupsBeforeMigration
            .get(1)
            .getCloudProviderAccess()
            .getAwsIamRoles()
            .get(0)
            .copy()
            .featureUsages(null)
            .build(),
        group2Role1.copy().featureUsages(null).build());

    // ensure group2Role2 created correctly
    final NDSCloudProviderAccessAWSIAMRole group2Role2 = group2Roles.get(1);
    assertEquals(
        tenant20.getCloudProviderConfig().getAws().getIamAssumedRoleARN(),
        group2Role2.getIamAssumedRoleArn());
    assertEquals(
        tenant20.getCloudProviderConfig().getAws().getExternalId(),
        group2Role2.getAtlasAssumedRoleExternalId());
    assertEquals(awsAccountUsed.getRootARN(), group2Role2.getAtlasAWSAccountArn());
    assertEquals(awsAccountUsed.getId(), group2Role2.getAtlasAWSAccountId());
    assertEquals(1, group2Role2.getFeatureUsages().size());
    assertEquals(
        new NDSCloudProviderAccessDataLakeFeatureUsage(
            new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                group2.getGroupId(), tenant20.getName())),
        group2Role2.getFeatureUsages().get(0));

    // ensure cloud provider config of tenant20 gets updated with roleId of group2role2
    assertNotNull(tenant20.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(group2Role2.getRoleId(), tenant20.getCloudProviderConfig().getAws().getRoleId());

    // do not migrate remaining data lake tenants (due to limit=1)
    assertEquals(dataLakeTenantsBeforeMigration.get(2), dataLakeTenantsAfterMigration.get(2));
    assertEquals(dataLakeTenantsBeforeMigration.get(3), dataLakeTenantsAfterMigration.get(3));
    assertEquals(dataLakeTenantsBeforeMigration.get(4), dataLakeTenantsAfterMigration.get(4));
    assertEquals(dataLakeTenantsBeforeMigration.get(5), dataLakeTenantsAfterMigration.get(5));
    assertEquals(dataLakeTenantsBeforeMigration.get(6), dataLakeTenantsAfterMigration.get(6));
    assertEquals(dataLakeTenantsBeforeMigration.get(7), dataLakeTenantsAfterMigration.get(7));
    assertEquals(dataLakeTenantsBeforeMigration.get(8), dataLakeTenantsAfterMigration.get(8));
    assertEquals(dataLakeTenantsBeforeMigration.get(9), dataLakeTenantsAfterMigration.get(9));
    assertEquals(dataLakeTenantsBeforeMigration.get(10), dataLakeTenantsAfterMigration.get(10));

    // do not migrate remaining groups
    assertEquals(
        ndsGroupsBeforeMigration.get(2).getCloudProviderAccess(),
        ndsGroupsAfterMigration.get(2).getCloudProviderAccess());
    assertEquals(
        ndsGroupsBeforeMigration.get(3).getCloudProviderAccess(),
        ndsGroupsAfterMigration.get(3).getCloudProviderAccess());
  }

  @Test
  public void testMigrationTool() throws IOException {
    final String csvFileName =
        String.format("/tmp/migrated-datalake-tenant-details-%s.csv", Instant.now().toString());

    _createCloudProviderAccessRolesTool
        .getAppSettings()
        .setProp(
            "queryengine.aws.assumeRoleUserARN",
            "arn:aws:iam::************:user/queryengine",
            AppSettings.SettingType.MEMORY);

    final List<AWSAccount> awsAccountBeforeMigration =
        _createCloudProviderAccessRolesTool.getAwsAccountDao().findAllAsBasicList().stream()
            .map(AWSAccount::new)
            .collect(Collectors.toList());
    assertEquals(3, awsAccountBeforeMigration.size());
    final List<NDSGroup> ndsGroupsBeforeMigration =
        _createCloudProviderAccessRolesTool.getNdsGroupDao().findAllAsBasicList().stream()
            .map(NDSGroup::new)
            .collect(Collectors.toList());
    assertEquals(4, ndsGroupsBeforeMigration.size());
    final List<NDSDataLakeTenant> dataLakeTenantsBeforeMigration =
        _createCloudProviderAccessRolesTool.getDataLakeTenantDao().findAll();
    assertEquals(11, dataLakeTenantsBeforeMigration.size());

    _createCloudProviderAccessRolesTool.run(100, csvFileName);

    final List<AWSAccount> awsAccountAfterMigration =
        _createCloudProviderAccessRolesTool.getAwsAccountDao().findAllAsBasicList().stream()
            .map(AWSAccount::new)
            .collect(Collectors.toList());
    assertEquals(3, awsAccountAfterMigration.size());
    final List<NDSGroup> ndsGroupsAfterMigration =
        _createCloudProviderAccessRolesTool.getNdsGroupDao().findAllAsBasicList().stream()
            .map(NDSGroup::new)
            .collect(Collectors.toList());
    assertEquals(4, ndsGroupsAfterMigration.size());
    final List<NDSDataLakeTenant> dataLakeTenantsAfterMigration =
        _createCloudProviderAccessRolesTool.getDataLakeTenantDao().findAll();
    assertEquals(11, dataLakeTenantsAfterMigration.size());

    final AWSAccount awsAccountUsed = awsAccountAfterMigration.get(1);

    // Data Lake tenant10 - do not migrate (state: UNVERIFIED)
    assertEquals(
        ndsGroupsBeforeMigration.get(0).getCloudProviderAccess(),
        ndsGroupsAfterMigration.get(0).getCloudProviderAccess());
    assertEquals(dataLakeTenantsBeforeMigration.get(0), dataLakeTenantsAfterMigration.get(0));

    // Data Lake tenant20 - create role and add feature usage
    // ensure tenant unchanged, except cloudproviderConfig.aws.roleId (tested further down)
    final NDSDataLakeTenant tenant20 = dataLakeTenantsAfterMigration.get(1);
    assertTenantsEqualExceptRoleId(dataLakeTenantsBeforeMigration.get(1), tenant20);

    final NDSGroup group2 = ndsGroupsAfterMigration.get(1);
    final List<NDSCloudProviderAccessAWSIAMRole> group2Roles =
        group2.getCloudProviderAccess().getAwsIamRoles();
    assertEquals(2, group2Roles.size());

    // ensure group2role1 remains unchanged (except featureUsages, modified when migrating tenant30)
    final NDSCloudProviderAccessAWSIAMRole group2Role1 = group2Roles.get(0);
    assertEquals(
        ndsGroupsBeforeMigration
            .get(1)
            .getCloudProviderAccess()
            .getAwsIamRoles()
            .get(0)
            .copy()
            .featureUsages(null)
            .build(),
        group2Role1.copy().featureUsages(null).build());

    // ensure group2Role2 created correctly
    final NDSCloudProviderAccessAWSIAMRole group2Role2 = group2Roles.get(1);
    assertEquals(
        tenant20.getCloudProviderConfig().getAws().getIamAssumedRoleARN(),
        group2Role2.getIamAssumedRoleArn());
    assertEquals(
        tenant20.getCloudProviderConfig().getAws().getExternalId(),
        group2Role2.getAtlasAssumedRoleExternalId());
    assertEquals(awsAccountUsed.getRootARN(), group2Role2.getAtlasAWSAccountArn());
    assertEquals(awsAccountUsed.getId(), group2Role2.getAtlasAWSAccountId());
    assertEquals(1, group2Role2.getFeatureUsages().size());
    assertEquals(
        new NDSCloudProviderAccessDataLakeFeatureUsage(
            new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                group2.getGroupId(), tenant20.getName())),
        group2Role2.getFeatureUsages().get(0));

    // ensure cloud provider config of tenant20 gets updated with roleId of group2role2
    assertNotNull(tenant20.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(group2Role2.getRoleId(), tenant20.getCloudProviderConfig().getAws().getRoleId());

    // Data Lake tenant30 - use existing role and add feature usage
    final NDSDataLakeTenant tenant30 = dataLakeTenantsAfterMigration.get(2);

    // ensure group2role1 contains feature usage for tenant30
    assertEquals(
        new NDSCloudProviderAccessDataLakeFeatureUsage(
            new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                group2.getGroupId(), tenant30.getName())),
        group2Role1.getFeatureUsages().get(0));

    // ensure cloud provider config of tenant30 gets updated with roleId of group2role1
    assertNotNull(tenant30.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(group2Role1.getRoleId(), tenant30.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(
        group2Role1.getAtlasAssumedRoleExternalId(),
        tenant30.getCloudProviderConfig().getAws().getExternalId());

    // Data Lake tenant40 - do not migrate (roleId not null)
    assertEquals(
        ndsGroupsBeforeMigration.get(2).getCloudProviderAccess(),
        ndsGroupsAfterMigration.get(2).getCloudProviderAccess());
    assertEquals(dataLakeTenantsBeforeMigration.get(3), dataLakeTenantsAfterMigration.get(3));

    // Data Lake tenant50 - do not migrate (state: DELETED)
    assertEquals(dataLakeTenantsBeforeMigration.get(4), dataLakeTenantsAfterMigration.get(4));

    // Data Lake tenant60 - do not migrate (dateLakeType: ONLINE_ARCHIVE)
    assertEquals(dataLakeTenantsBeforeMigration.get(5), dataLakeTenantsAfterMigration.get(5));

    // tenant70 and tenant80 share iamAssumedRoleARN, have different externalIds
    // and are missing roleId
    // Data Lake tenant70 - create role using tenant70 externalId, add feature usage
    final NDSDataLakeTenant tenant70BeforeMigration = dataLakeTenantsBeforeMigration.get(6);
    final NDSDataLakeTenant tenant70 = dataLakeTenantsAfterMigration.get(6);
    final NDSGroup group4BeforeMigration = ndsGroupsBeforeMigration.get(3);
    final NDSGroup group4 = ndsGroupsAfterMigration.get(3);
    final List<NDSCloudProviderAccessAWSIAMRole> group4Roles =
        group4.getCloudProviderAccess().getAwsIamRoles();

    // ensure existing roles remain unchanged
    assertEquals(
        group4BeforeMigration.getCloudProviderAccess().getAwsIamRoles().get(0), group4Roles.get(0));
    assertEquals(
        group4BeforeMigration.getCloudProviderAccess().getAwsIamRoles().get(1), group4Roles.get(1));

    // ensure new role uses iamAssumedRoleArn and externalId of tenant70
    assertEquals(3, group4Roles.size());
    final NDSCloudProviderAccessAWSIAMRole group4Role3 =
        group4.getCloudProviderAccess().getAwsIamRoles().get(2);
    assertEquals(
        tenant70.getCloudProviderConfig().getAws().getIamAssumedRoleARN(),
        group4Role3.getIamAssumedRoleArn());
    assertEquals(
        tenant70BeforeMigration.getCloudProviderConfig().getAws().getExternalId(),
        group4Role3.getAtlasAssumedRoleExternalId());
    assertEquals(awsAccountUsed.getRootARN(), group4Role3.getAtlasAWSAccountArn());
    assertEquals(awsAccountUsed.getId(), group4Role3.getAtlasAWSAccountId());
    // the other feature usage comes from tenant80 (tested further down)
    assertEquals(2, group4Role3.getFeatureUsages().size());
    assertEquals(
        new NDSCloudProviderAccessDataLakeFeatureUsage(
            new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                group4.getGroupId(), tenant70.getName())),
        group4Role3.getFeatureUsages().get(0));

    // ensure cloud provider config of tenant70 gets updated with roleId of group4Role3
    assertNotNull(tenant70.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(group4Role3.getRoleId(), tenant70.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(
        tenant70BeforeMigration.getCloudProviderConfig().getAws().getExternalId(),
        tenant70.getCloudProviderConfig().getAws().getExternalId());

    // Data Lake tenant80 - create role using tenant70 externalId, add feature usage
    final NDSDataLakeTenant tenant80 = dataLakeTenantsAfterMigration.get(7);

    // ensure feature usages of group4Role3 contain tenant80
    assertEquals(
        new NDSCloudProviderAccessDataLakeFeatureUsage(
            new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                group4.getGroupId(), tenant80.getName())),
        group4Role3.getFeatureUsages().get(1));

    // ensure cloud provider config of tenant80 gets updated with roleId and externalId of
    // group4Role3
    assertNotNull(tenant80.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(group4Role3.getRoleId(), tenant80.getCloudProviderConfig().getAws().getRoleId());
    assertEquals(
        group4Role3.getAtlasAssumedRoleExternalId(),
        tenant80.getCloudProviderConfig().getAws().getExternalId());

    // Data Lake tenant90 - do not migrate (missing externalId)
    assertEquals(dataLakeTenantsBeforeMigration.get(8), dataLakeTenantsAfterMigration.get(8));

    // Data Lake tenant100 - do not migrate (missing iamAssumedRoleARN)
    assertEquals(dataLakeTenantsBeforeMigration.get(9), dataLakeTenantsAfterMigration.get(9));

    // Data Lake tenant110 - do not migrate (missing iamAssumedRoleARN and externalId)
    assertEquals(dataLakeTenantsBeforeMigration.get(10), dataLakeTenantsAfterMigration.get(10));

    // ensure CSV file contents populated successfully
    assertCSVFilePopulated(csvFileName, 4);
  }

  private void assertCSVFilePopulated(final String pCSVFileName, final int pTenantsUpdated)
      throws IOException {
    // ISODateStr,groupIdStr,tenantNameStr,iamAssumedRoleARN,externalId,roleIdStr,roleAssociationType,status
    final List<String> csvRows = Files.readAllLines(Paths.get(pCSVFileName));
    assertEquals(pTenantsUpdated + 1, csvRows.size());
    // test header
    assertEquals(
        "dateProcessed,groupId,tenantName,iamAssumedRoleARN,externalId,associatedRoleId,roleAssociationType,status",
        csvRows.get(0));
    // test remaining rows
    for (final String csvRow : csvRows.subList(1, csvRows.size() - 1)) {
      final List<String> rowItems = Arrays.asList(csvRow.split(","));
      assertEquals(8, rowItems.size());
      // ensure output generated within the last 10 minutes
      assertTrue(
          OffsetDateTime.parse(rowItems.get(0)).plusMinutes(10).toInstant().isAfter(Instant.now()));
      // check ObjectId valid
      assertTrue(ObjectId.isValid(rowItems.get(1)));
      // check tenantId can be reconstructed
      try {
        NDSDataLakeTenantId.builder()
            .groupId(new ObjectId(rowItems.get(1)))
            .name(rowItems.get(2))
            .build()
            .validate();
      } catch (final SvcException pE) {
        fail();
      }
      // check iamAssumedRoleArn is valid
      assertFalse(rowItems.get(3).isEmpty());
      // check externalId is valid
      assertTrue(ValidationUtils.VALID_EXTERNAL_ID_PATTERN.matcher(rowItems.get(4)).matches());
      // check roleId is valid
      assertTrue(ObjectId.isValid(rowItems.get(5)));
      // check roleExistedBeforeMigration valid
      assertTrue(rowItems.get(6).equals("EXISTING_ROLE") || rowItems.get(6).equals("NEW_ROLE"));
      // check all tenants migrated successfully
      assertEquals("SUCCESS", rowItems.get(7));
    }
  }

  private void assertTenantsEqualExceptRoleId(
      final NDSDataLakeTenant pOriginalTenant, final NDSDataLakeTenant pModifiedTenant) {
    final NDSDataLakeTenant pOriginalTenantSansRoleId =
        pOriginalTenant.toBuilder()
            .cloudProviderConfig(
                pOriginalTenant.getCloudProviderConfig().toBuilder()
                    .aws(
                        pOriginalTenant.getCloudProviderConfig().getAws().toBuilder()
                            .roleId(null)
                            .build())
                    .build())
            .build();
    final NDSDataLakeTenant pModifiedTenantSansRoleId =
        pModifiedTenant.toBuilder()
            .cloudProviderConfig(
                pModifiedTenant.getCloudProviderConfig().toBuilder()
                    .aws(
                        pModifiedTenant.getCloudProviderConfig().getAws().toBuilder()
                            .roleId(null)
                            .build())
                    .build())
            .build();
    assertEquals(pOriginalTenantSansRoleId, pModifiedTenantSansRoleId);
  }
}
