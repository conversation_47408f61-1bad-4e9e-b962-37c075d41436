package com.xgen.svc.mms.misc;

import static com.xgen.cloud.nds.azure._public.model.AzureOrphanedItem.Type.CAPACITY_RESERVATION;
import static org.assertj.core.api.Assertions.assertThat;

import com.azure.resourcemanager.compute.fluent.models.CapacityReservationGroupInner;
import com.azure.resourcemanager.compute.fluent.models.CapacityReservationInner;
import com.azure.resourcemanager.resources.fluentcore.arm.AvailabilityZoneId;
import com.azure.resourcemanager.resources.models.ResourceGroup;
import com.xgen.cloud.nds.azure._public.model.AzureOrphanedItem;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.svc.nds.azure.AzureExternalIntTest;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AzureCapacityReservationOrphanedItemCorrectionToolExternalIntTests
    extends AzureExternalIntTest {
  private static final Logger LOG =
      LoggerFactory.getLogger(
          AzureCapacityReservationOrphanedItemCorrectionToolExternalIntTests.class);
  private static final String RESOURCE_NAME = "backfillToolTestGroup";

  @Inject private AzureApiSvc azureApiSvc;
  @Inject private NDSOrphanedItemDao ndsOrphanedItemDao;

  @Test(timeout = 1_000 * 60 * 30) // 30 minutes
  public void testTool() throws InterruptedException {
    final ResourceGroup resourceGroup =
        azureApiSvc.createResourceGroup(
            getAzureSubscription().getId(),
            AzureRegionName.US_EAST_2,
            RESOURCE_NAME,
            Map.of("xgen:created-by", "AzureCapacityReservationOrphanedItemCorrectionToolIntTests"),
            LOG);

    final CapacityReservationGroupInner capacityReservationGroup =
        azureApiSvc.createCapacityReservationGroup(
            getAzureSubscription().getId(),
            resourceGroup.name(),
            RESOURCE_NAME,
            AzureRegionName.US_EAST_2,
            List.of(AvailabilityZoneId.ZONE_1.getValue()),
            Map.of("xgen:created-by", "AzureCapacityReservationOrphanedItemCorrectionToolIntTests"),
            LOG);

    final CapacityReservationInner capacityReservation =
        azureApiSvc.createCapacityReservation(
            getAzureSubscription().getId(),
            resourceGroup.name(),
            capacityReservationGroup.name(),
            RESOURCE_NAME,
            AzureRegionName.US_EAST_2,
            "Standard_B2s",
            1,
            AvailabilityZoneId.ZONE_1.getValue(),
            Map.of("xgen:created-by", "AzureCapacityReservationOrphanedItemCorrectionToolIntTests"),
            LOG);

    final AzureOrphanedItem orphanedReservation =
        new AzureOrphanedItem.Builder()
            .setAzureSubscriptionId(getAzureSubscription().getId())
            .setResourceGroupName(resourceGroup.name())
            .setType(CAPACITY_RESERVATION)
            .setId(RESOURCE_NAME)
            .build();

    ndsOrphanedItemDao.add(orphanedReservation);

    final List<NDSOrphanedItem> preExecution =
        ndsOrphanedItemDao.findByCloudProviderAndType(CloudProvider.AZURE, CAPACITY_RESERVATION);
    assertThat(preExecution).hasSize(1);
    assertThat((AzureOrphanedItem) preExecution.get(0))
        .matches(
            azureOrphanedItem ->
                azureOrphanedItem.getAzureSubscriptionId().equals(getAzureSubscription().getId()))
        .matches(
            azureOrphanedItem ->
                azureOrphanedItem.getResourceGroupName().equals(resourceGroup.name()))
        .matches(
            azureOrphanedItem -> azureOrphanedItem.getCapacityReservationGroupName().isEmpty());

    final AzureCapacityReservationOrphanedItemCorrectionTool tool =
        new AzureCapacityReservationOrphanedItemCorrectionTool(azureApiSvc, ndsOrphanedItemDao);
    tool.execute(true);

    final List<NDSOrphanedItem> postExecution =
        ndsOrphanedItemDao.findByCloudProviderAndType(CloudProvider.AZURE, CAPACITY_RESERVATION);
    assertThat(postExecution).hasSize(1);
    assertThat((AzureOrphanedItem) postExecution.get(0))
        .matches(
            azureOrphanedItem ->
                azureOrphanedItem.getAzureSubscriptionId().equals(getAzureSubscription().getId()))
        .matches(
            azureOrphanedItem ->
                azureOrphanedItem.getResourceGroupName().equals(resourceGroup.name()))
        .matches(
            azureOrphanedItem -> azureOrphanedItem.getCapacityReservationGroupName().isPresent());

    azureApiSvc.deleteCapacityReservation(
        getAzureSubscription().getId(),
        resourceGroup.name(),
        capacityReservationGroup.name(),
        capacityReservation.name(),
        LOG);

    azureApiSvc.deleteCapacityReservationGroup(
        getAzureSubscription().getId(), resourceGroup.name(), capacityReservationGroup.name(), LOG);

    azureApiSvc.deleteResourceGroup(getAzureSubscription().getId(), resourceGroup.name(), LOG);
  }
}
