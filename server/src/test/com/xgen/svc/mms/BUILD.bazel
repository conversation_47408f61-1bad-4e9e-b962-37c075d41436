load("//server/src/test:rules.bzl", "library_package", "test_package")

library_package(
    name = "mms",
    visibility = ["//server/src/test:__subpackages__"],
    deps = [
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
    ],
)

test_package(
    name = "TestLibrary",
    srcs = glob(
        ["*IntTests.java"],
    ),
    deps = [
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/test/com/xgen/svc/core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
