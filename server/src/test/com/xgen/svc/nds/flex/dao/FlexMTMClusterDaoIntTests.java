package com.xgen.svc.nds.flex.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.FieldDefs;
import com.xgen.cloud.nds.flex._private.dao.FlexMTMClusterDao;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexMTMCluster;
import com.xgen.cloud.nds.flex._public.model.FlexTenantClusterConfiguration;
import com.xgen.cloud.nds.free._private.dao.SharedMTMClusterDao;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class FlexMTMClusterDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private FlexMTMClusterDao _flexMTMClusterDao;
  @Inject private SharedMTMClusterDao _sharedMTMClusterDao;
  @Inject private ServerlessMTMClusterDao _serverlessMTMClusterDao;
  @Inject private MTMClusterDao _mtmClusterDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _flexMTMClusterDao.ensureIndexes();
  }

  @Test
  public void testFindAllFlexMTMClusters() {
    final HashSet<FlexMTMCluster> flexMTMClusters = new HashSet<>();
    for (int i = 0; i < 10; i++) {
      final String mtmName = String.format("flex-mtm-%s", i);
      final FlexMTMCluster cluster =
          new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster(mtmName, new ObjectId()));
      _flexMTMClusterDao.insert(cluster);
      flexMTMClusters.add(cluster);
    }
    // inserting a non Flex MTM
    final SharedMTMCluster sharedMTMCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());
    _sharedMTMClusterDao.insert(sharedMTMCluster);

    final List<FlexMTMCluster> foundFlexMTMClusters = _flexMTMClusterDao.findAllFlexMTMClusters();
    assertEquals(10, foundFlexMTMClusters.size());
    assertTrue(foundFlexMTMClusters.containsAll(flexMTMClusters));
    assertFalse(foundFlexMTMClusters.contains(sharedMTMCluster));
  }

  @Test
  public void testFindFlexMTMClustersWithFilter_withSkipAndLimit() {
    for (int i = 0; i < 10; i++) {
      final FlexMTMCluster mtm =
          new FlexMTMCluster(
              NDSModelTestFactory.getFlexMTMCluster(
                  String.format("flex-mtm-%s", i), new ObjectId()));
      _flexMTMClusterDao.insert(mtm);
    }
    final Map<String, String> filters = new HashMap<>();

    // Skip none
    assertEquals(10, _flexMTMClusterDao.findFlexMTMClustersWithFilter(filters, 0, 10).size());
    // limit greater than size
    assertEquals(10, _flexMTMClusterDao.findFlexMTMClustersWithFilter(filters, 0, 20).size());
    // limit less than size
    assertEquals(5, _flexMTMClusterDao.findFlexMTMClustersWithFilter(filters, 0, 5).size());
    // skip a value > 0
    assertEquals(5, _flexMTMClusterDao.findFlexMTMClustersWithFilter(filters, 5, 10).size());
    assertEquals(5, _flexMTMClusterDao.findFlexMTMClustersWithFilter(filters, 5, 20).size());
  }

  @Test
  public void testFindFlexMTMClustersWithFilter() {
    final FlexMTMCluster mtmAws =
        new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster("flex-mtm-aws", new ObjectId()));
    _flexMTMClusterDao.insert(mtmAws);

    final FlexMTMCluster mtmAzure =
        new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster("flex-mtm-azure", new ObjectId()))
            .copy()
            .setProviderName(CloudProvider.AZURE)
            .setRegion(AzureRegionName.US_EAST_2)
            .build();
    _flexMTMClusterDao.insert(mtmAzure);

    final FlexMTMCluster mtmAzure2 =
        new FlexMTMCluster(
                NDSModelTestFactory.getFlexMTMCluster("flex-mtm-azure-1", new ObjectId()))
            .copy()
            .setProviderName(CloudProvider.AZURE)
            .setRegion(AzureRegionName.US_EAST_2)
            .build();
    _flexMTMClusterDao.insert(mtmAzure2);

    final SharedMTMCluster sharedMTMCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());
    _sharedMTMClusterDao.insert(sharedMTMCluster);

    // Testing cloud provider filter
    assertEquals(
        1,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("cloudProvider", "AWS"), 0, 5)
            .size());
    assertTrue(
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("cloudProvider", "AWS"), 0, 5)
            .contains(mtmAws));
    assertFalse(
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("cloudProvider", "AWS"), 0, 5)
            .contains(sharedMTMCluster));

    // testing region name filter
    assertEquals(
        2,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("regionName", "US_EAST_2"), 0, 5)
            .size());
    assertTrue(
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("regionName", "US_EAST_2"), 0, 5)
            .containsAll(List.of(mtmAzure, mtmAzure2)));
    // testing mtm name search value
    assertEquals(
        1,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("searchValue", "flex-mtm-aws"), 0, 5)
            .size());
    assertEquals(
        mtmAws,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("searchValue", "flex-mtm-aws"), 0, 5)
            .get(0));
    // testing group id search value
    assertEquals(
        1,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(
                Map.of("searchValue", mtmAzure.getGroupId().toString()), 0, 5)
            .size());
    assertEquals(
        mtmAzure,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(
                Map.of("searchValue", mtmAzure.getGroupId().toString()), 0, 5)
            .get(0));
    // testing invalid search value
    assertEquals(
        0,
        _flexMTMClusterDao
            .findFlexMTMClustersWithFilter(Map.of("searchValue", "foo"), 0, 5)
            .size());
  }

  @Test
  public void testFindFlexMTMClusterByGroupIdAndName() {
    // testing non Flex MTM
    {
      final SharedMTMCluster sharedMTMCluster =
          new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());
      _sharedMTMClusterDao.insert(sharedMTMCluster);
      assertTrue(
          _flexMTMClusterDao
              .findFlexMTMClusterByGroupIdAndName(
                  sharedMTMCluster.getGroupId(), sharedMTMCluster.getName())
              .isEmpty());
    }
    // testing out Flex MTM
    {
      final FlexMTMCluster cluster =
          new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster("flex-mtm-1", new ObjectId()));
      _flexMTMClusterDao.insert(cluster);

      assertEquals(
          cluster,
          _flexMTMClusterDao
              .findFlexMTMClusterByGroupIdAndName(cluster.getGroupId(), cluster.getName())
              .orElseThrow());
    }
  }

  @Test
  public void testFindDistinctFlexClusterConfigurations() {
    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster("shared-mtm"));
    _flexMTMClusterDao.insert(sharedCluster);

    // test no Flex MTMs
    {
      final Set<FlexTenantClusterConfiguration> flexTenantClusterConfigurations =
          _flexMTMClusterDao.findDistinctFlexClusterConfigurations();
      assertTrue(flexTenantClusterConfigurations.isEmpty());
    }

    // Note: These MTMs are considered the same configuration based on factors such as instance
    // size, region and version.
    final FlexMTMCluster flexMTMCluster1 =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                "flex-mtm-us-east-1-1",
                new ObjectId(),
                FlexInstanceSize.FLEX,
                AWSRegionName.US_EAST_1,
                NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION));
    _flexMTMClusterDao.insert(flexMTMCluster1);
    final FlexMTMCluster flexMTMCluster2 =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                "flex-mtm-us-east-1-2",
                new ObjectId(),
                FlexInstanceSize.FLEX,
                AWSRegionName.US_EAST_1,
                NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION));
    _flexMTMClusterDao.insert(flexMTMCluster2);

    // Asset configurations are equal
    assertEquals(flexMTMCluster1.getRegion(), flexMTMCluster2.getRegion());
    assertEquals(flexMTMCluster1.getTenantInstanceSize(), flexMTMCluster2.getTenantInstanceSize());
    assertEquals(
        flexMTMCluster1.getMongoDBMajorVersion(), flexMTMCluster2.getMongoDBMajorVersion());

    // This MTM differs from the above MTMs by region and is considered a different configuration.
    final FlexMTMCluster flexMTMCluster3 =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                "flex-mtm-us-east-2-3",
                new ObjectId(),
                FlexInstanceSize.FLEX,
                AWSRegionName.US_EAST_2,
                NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION));
    _flexMTMClusterDao.insert(flexMTMCluster3);

    // Asset configurations are not equal
    assertNotEquals(flexMTMCluster1.getRegion(), flexMTMCluster3.getRegion());
    assertEquals(flexMTMCluster1.getTenantInstanceSize(), flexMTMCluster3.getTenantInstanceSize());
    assertEquals(
        flexMTMCluster1.getMongoDBMajorVersion(), flexMTMCluster3.getMongoDBMajorVersion());

    // test Flex MTMs present
    {
      final Set<FlexTenantClusterConfiguration> flexTenantClusterConfigurations =
          _flexMTMClusterDao.findDistinctFlexClusterConfigurations();
      assertEquals(2, flexTenantClusterConfigurations.size());

      final FlexTenantClusterConfiguration flexTenantClusterConfiguration1 =
          flexTenantClusterConfigurations.stream()
              .filter(c -> c.getRegionName().equals(flexMTMCluster1.getRegion()))
              .findFirst()
              .orElseThrow();
      assertEquals(flexMTMCluster1.getRegion(), flexTenantClusterConfiguration1.getRegionName());
      assertEquals(
          flexMTMCluster1.getTenantInstanceSize(),
          flexTenantClusterConfiguration1.getTenantInstanceSize());
      assertEquals(
          flexMTMCluster1.getMongoDBMajorVersion(),
          flexTenantClusterConfiguration1.getMongoDBMajorVersion());

      final FlexTenantClusterConfiguration flexTenantClusterConfiguration2 =
          flexTenantClusterConfigurations.stream()
              .filter(c -> c.getRegionName().equals(flexMTMCluster3.getRegion()))
              .findFirst()
              .orElseThrow();
      assertEquals(flexMTMCluster3.getRegion(), flexTenantClusterConfiguration2.getRegionName());
      assertEquals(
          flexMTMCluster3.getTenantInstanceSize(),
          flexTenantClusterConfiguration2.getTenantInstanceSize());
      assertEquals(
          flexMTMCluster3.getMongoDBMajorVersion(),
          flexTenantClusterConfiguration2.getMongoDBMajorVersion());
    }

    _flexMTMClusterDao.deleteCluster(
        sharedCluster.getGroupId(), sharedCluster.getName(), sharedCluster.getMTMClusterType());
    _flexMTMClusterDao.deleteCluster(
        flexMTMCluster1.getGroupId(),
        flexMTMCluster1.getName(),
        flexMTMCluster1.getMTMClusterType());
    _flexMTMClusterDao.deleteCluster(
        flexMTMCluster2.getGroupId(),
        flexMTMCluster2.getName(),
        flexMTMCluster2.getMTMClusterType());
    _flexMTMClusterDao.deleteCluster(
        flexMTMCluster3.getGroupId(),
        flexMTMCluster3.getName(),
        flexMTMCluster3.getMTMClusterType());

    // test no MTMs
    {
      final Set<FlexTenantClusterConfiguration> FlexTenantClusterConfigurations =
          _flexMTMClusterDao.findDistinctFlexClusterConfigurations();
      assertTrue(FlexTenantClusterConfigurations.isEmpty());
    }
  }

  @Test
  public void testUpdateFlexMTMCluster() {
    final FlexMTMCluster flexMTMCluster =
        new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster("flex-mtm", new ObjectId()));
    _flexMTMClusterDao.insert(flexMTMCluster);

    assertEquals(1, _flexMTMClusterDao.findAllFlexMTMClusters().size());

    _flexMTMClusterDao.updateCluster(
        flexMTMCluster.getGroupId(),
        flexMTMCluster.getName(),
        flexMTMCluster,
        !flexMTMCluster.isAssignmentEnabled(),
        flexMTMCluster.getMaxCapacity() + 10,
        flexMTMCluster.getCapacityRemaining() + 10,
        FlexInstanceSize.FLEX,
        Collections.emptyList());
    final FlexMTMCluster updatedFlexMTMCluster =
        _flexMTMClusterDao
            .findFlexMTMClusterByGroupIdAndName(
                flexMTMCluster.getGroupId(), flexMTMCluster.getName())
            .orElseThrow();
    assertEquals(
        !flexMTMCluster.isAssignmentEnabled(), updatedFlexMTMCluster.isAssignmentEnabled());
    assertEquals(flexMTMCluster.getMaxCapacity() + 10, updatedFlexMTMCluster.getMaxCapacity());
    assertEquals(
        flexMTMCluster.getCapacityRemaining() + 10, updatedFlexMTMCluster.getCapacityRemaining());
  }

  @Test
  public void testCountFlexMTMClusters() {
    final ObjectId groupId = new ObjectId();
    for (int i = 0; i < 5; i++) {
      final FlexMTMCluster mtm =
          new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster("flex-mtm-" + i, groupId))
              .copy()
              .setRegion(AWSRegionName.US_EAST_2)
              .build();
      _flexMTMClusterDao.insert(mtm);
    }
    for (int i = 0; i < 3; i++) {
      final FlexMTMCluster mtm =
          new FlexMTMCluster(
                  NDSModelTestFactory.getFlexMTMCluster("flex-mtm-azure-" + i, new ObjectId()))
              .copy()
              .setProviderName(CloudProvider.AZURE)
              .setRegion(AzureRegionName.US_EAST_2)
              .build();
      _flexMTMClusterDao.insert(mtm);
    }

    for (int i = 0; i < 2; i++) {
      final FlexMTMCluster mtm =
          new FlexMTMCluster(
                  NDSModelTestFactory.getFlexMTMCluster("flex-mtm-azure-" + i, new ObjectId()))
              .copy()
              .setProviderName(CloudProvider.AZURE)
              .setRegion(AzureRegionName.US_WEST_2)
              .build();
      _flexMTMClusterDao.insert(mtm);
    }

    // no filters
    assertEquals(10L, _flexMTMClusterDao.countFlexClusters(Map.of()));
    // cloud provider filter
    assertEquals(5L, _flexMTMClusterDao.countFlexClusters(Map.of("cloudProvider", "AWS")));
    // region name filter
    assertEquals(2L, _flexMTMClusterDao.countFlexClusters(Map.of("regionName", "US_WEST_2")));
    // group name filter
    assertEquals(
        5L, _flexMTMClusterDao.countFlexClusters(Map.of("searchValue", groupId.toString())));
    // invalid cluster name filter
    assertEquals(0L, _flexMTMClusterDao.countFlexClusters(Map.of("searchValue", "flex-mtm-azure")));
    // full cluster name filter
    assertEquals(1L, _flexMTMClusterDao.countFlexClusters(Map.of("searchValue", "flex-mtm-0")));
  }

  @Test
  public void testFindFlexClusterWithAvailableCapacity() {
    // Test no flex mtm clusters
    _mtmClusterDao.insert(new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster()));

    assertTrue(
        _flexMTMClusterDao
            .findFlexClusterWithAvailableCapacity(
                CloudProvider.AWS,
                AWSRegionName.US_EAST_1,
                FlexInstanceSize.FLEX,
                Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
                new ObjectId())
            .isEmpty());

    // Test no available mtm cluster capacity
    final BasicDBObject flexMTMCluster1DBObj = NDSModelTestFactory.getFlexMTMCluster();
    flexMTMCluster1DBObj.put(FieldDefs.CAPACITY_REMAINING, 0);
    final FlexMTMCluster flexMTMCluster1 = new FlexMTMCluster(flexMTMCluster1DBObj);
    _mtmClusterDao.insert(flexMTMCluster1);

    assertTrue(
        _flexMTMClusterDao
            .findFlexClusterWithAvailableCapacity(
                CloudProvider.AWS,
                AWSRegionName.US_EAST_1,
                FlexInstanceSize.FLEX,
                Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
                new ObjectId())
            .isEmpty());

    // Test find flex mtm cluster with available capacity
    final FlexMTMCluster flexMTMCluster2 =
        new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster());
    _mtmClusterDao.insert(flexMTMCluster2);

    assertTrue(
        _flexMTMClusterDao
            .findFlexClusterWithAvailableCapacity(
                CloudProvider.AWS,
                AWSRegionName.US_EAST_1,
                FlexInstanceSize.FLEX,
                Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
                new ObjectId())
            .isPresent());

    // Test find flex mtm cluster sorted by capacity remaining
    final BasicDBObject flexMTMCluster3DBObj = NDSModelTestFactory.getFlexMTMCluster();
    flexMTMCluster3DBObj.put(FieldDefs.CAPACITY_REMAINING, 998);
    final FlexMTMCluster flexMTMCluster3 = new FlexMTMCluster(flexMTMCluster3DBObj);
    _mtmClusterDao.insert(flexMTMCluster3);

    final Optional<FlexMTMCluster> flexMTMClusterFound =
        _flexMTMClusterDao.findFlexClusterWithAvailableCapacity(
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            FlexInstanceSize.FLEX,
            Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
            new ObjectId());
    assertTrue(flexMTMClusterFound.isPresent());
    assertEquals(998, flexMTMClusterFound.orElseThrow().getCapacityRemaining());
  }

  @Test
  public void testFindFlexClusterWithAvailableCapacity_IsolationGroupIds() {
    // cluster with no isolationGroupIds
    {
      final FlexMTMCluster flexMTMCluster =
          new FlexMTMCluster(NDSModelTestFactory.getFlexMTMCluster());
      _mtmClusterDao.insert(flexMTMCluster);

      assertTrue(
          _flexMTMClusterDao
              .findFlexClusterWithAvailableCapacity(
                  CloudProvider.AWS,
                  AWSRegionName.US_EAST_1,
                  FlexInstanceSize.FLEX,
                  Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
                  new ObjectId())
              .isPresent());
    }

    // cluster with one matching group id in isolationGroupIds and capacity > 0
    {
      final ObjectId isolationGroupId0 = new ObjectId();
      final ObjectId isolationGroupId1 = new ObjectId();
      final BasicDBList isolationGroupIds = new BasicDBList();
      isolationGroupIds.addAll(List.of(isolationGroupId0, isolationGroupId1));

      final FlexMTMCluster flexMTMCluster =
          new FlexMTMCluster(
              NDSModelTestFactory.getFlexMTMCluster()
                  .append(MTMCluster.FieldDefs.ISOLATION_GROUP_IDS, isolationGroupIds));
      _mtmClusterDao.insert(flexMTMCluster);

      final Optional<FlexMTMCluster> flexMTMClusterWithAvailableCapacity =
          _flexMTMClusterDao.findFlexClusterWithAvailableCapacity(
              CloudProvider.AWS,
              AWSRegionName.US_EAST_1,
              FlexInstanceSize.FLEX,
              Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
              isolationGroupId1);
      assertEquals(
          flexMTMCluster.getName(), flexMTMClusterWithAvailableCapacity.orElseThrow().getName());
    }

    // cluster with mismatching isolationGroupIds - return first cluster with no isolationGroupIds
    {
      final ObjectId isolationGroupId0 = new ObjectId();
      final ObjectId isolationGroupId1 = new ObjectId();
      final BasicDBList isolationGroupIds = new BasicDBList();
      isolationGroupIds.addAll(List.of(isolationGroupId0, isolationGroupId1));

      final FlexMTMCluster flexMTMCluster =
          new FlexMTMCluster(
              NDSModelTestFactory.getFlexMTMCluster()
                  .append(MTMCluster.FieldDefs.ISOLATION_GROUP_IDS, isolationGroupIds));
      _mtmClusterDao.insert(flexMTMCluster);

      final Optional<FlexMTMCluster> flexMTMClusterWithAvailableCapacity =
          _flexMTMClusterDao.findFlexClusterWithAvailableCapacity(
              CloudProvider.AWS,
              AWSRegionName.US_EAST_1,
              FlexInstanceSize.FLEX,
              Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
              new ObjectId());
      assertTrue(
          flexMTMClusterWithAvailableCapacity.orElseThrow().getIsolationGroupIds().isEmpty());
    }

    // cluster with one matching group id in isolationGroupIds and capacity = 0
    {
      final ObjectId isolationGroupId0 = new ObjectId();
      final ObjectId isolationGroupId1 = new ObjectId();
      final BasicDBList isolationGroupIds = new BasicDBList();
      isolationGroupIds.addAll(List.of(isolationGroupId0, isolationGroupId1));

      final FlexMTMCluster flexMTMCluster =
          new FlexMTMCluster(
              NDSModelTestFactory.getFlexMTMCluster()
                  .append(MTMCluster.FieldDefs.ISOLATION_GROUP_IDS, isolationGroupIds)
                  .append(MTMCluster.FieldDefs.CAPACITY_REMAINING, 0));
      _mtmClusterDao.insert(flexMTMCluster);

      final Optional<FlexMTMCluster> flexMTMClusterWithAvailableCapacity =
          _flexMTMClusterDao.findFlexClusterWithAvailableCapacity(
              CloudProvider.AWS,
              AWSRegionName.US_EAST_1,
              FlexInstanceSize.FLEX,
              Optional.of(NDSModelTestFactory.TEST_FLEX_MONGODB_MAJOR_VERSION),
              isolationGroupId1);
      assertTrue(flexMTMClusterWithAvailableCapacity.isEmpty());
    }
  }

  @Test
  public void testFindFlexMTMs() {
    final ClusterDescriptionId sharedClusterDescriptionId1 =
        new ClusterDescriptionId("shared-mtm-1", oid(1));
    final ClusterDescriptionId sharedClusterDescriptionId2 =
        new ClusterDescriptionId("shared-mtm-2", oid(1));
    final ClusterDescriptionId sharedClusterDescriptionId3 =
        new ClusterDescriptionId("shared-mtm-3", oid(2));

    final ClusterDescriptionId serverlessClusterDescriptionId1 =
        new ClusterDescriptionId("serverless-mtm-1", oid(3));
    final ClusterDescriptionId serverlessClusterDescriptionId2 =
        new ClusterDescriptionId("serverless-mtm-2", oid(3));
    final ClusterDescriptionId serverlessClusterDescriptionId3 =
        new ClusterDescriptionId("serverless-mtm-3", oid(4));

    final ClusterDescriptionId flexClusterDescriptionId1 =
        new ClusterDescriptionId("flex-mtm-1", oid(5));
    final ClusterDescriptionId flexClusterDescriptionId2 =
        new ClusterDescriptionId("flex-mtm-2", oid(5));
    final ClusterDescriptionId flexClusterDescriptionId3 =
        new ClusterDescriptionId("flex-mtm-3", oid(6));

    Stream.of(sharedClusterDescriptionId1, sharedClusterDescriptionId2, sharedClusterDescriptionId3)
        .map(
            cid ->
                new SharedMTMCluster(
                    NDSModelTestFactory.getSharedMTMCluster(
                        cid.getGroupId(), cid.getClusterName())))
        .forEach(sharedMTMCluster -> _sharedMTMClusterDao.insert(sharedMTMCluster));

    Stream.of(
            serverlessClusterDescriptionId1,
            serverlessClusterDescriptionId2,
            serverlessClusterDescriptionId3)
        .map(
            cid ->
                new ServerlessMTMCluster(
                    NDSModelTestFactory.getServerlessMTMCluster(
                        cid.getGroupId(), cid.getClusterName())))
        .forEach(serverlessMTMCluster -> _serverlessMTMClusterDao.insert(serverlessMTMCluster));

    final FlexMTMCluster flexMTMCluster1 =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                flexClusterDescriptionId1.getClusterName(),
                flexClusterDescriptionId1.getGroupId()));
    final FlexMTMCluster flexMTMCluster2 =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                flexClusterDescriptionId2.getClusterName(),
                flexClusterDescriptionId2.getGroupId()));
    final FlexMTMCluster flexMTMCluster3 =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                flexClusterDescriptionId3.getClusterName(),
                flexClusterDescriptionId3.getGroupId()));
    Stream.of(flexMTMCluster1, flexMTMCluster2, flexMTMCluster3)
        .forEach(mtmCluster -> _flexMTMClusterDao.insert(mtmCluster));

    // Test empty list of cluster description IDs
    assertEquals(Collections.emptyList(), _flexMTMClusterDao.findFlexMTMs(Collections.emptyList()));

    // Test lists of flex-only cluster description IDs
    assertEquals(
        List.of(flexMTMCluster1),
        _flexMTMClusterDao.findFlexMTMs(List.of(flexClusterDescriptionId1)));
    assertEquals(
        List.of(flexMTMCluster2),
        _flexMTMClusterDao.findFlexMTMs(List.of(flexClusterDescriptionId2)));
    assertEquals(
        List.of(flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(List.of(flexClusterDescriptionId3)));
    assertEquals(
        List.of(flexMTMCluster1, flexMTMCluster2),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(flexClusterDescriptionId1, flexClusterDescriptionId2)));
    assertEquals(
        List.of(flexMTMCluster1, flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(flexClusterDescriptionId1, flexClusterDescriptionId3)));
    assertEquals(
        List.of(flexMTMCluster2, flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(flexClusterDescriptionId2, flexClusterDescriptionId3)));
    assertEquals(
        List.of(flexMTMCluster1, flexMTMCluster2, flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                flexClusterDescriptionId1, flexClusterDescriptionId2, flexClusterDescriptionId3)));

    // Test lists of shared, serverless, and flex cluster description IDs
    assertEquals(
        List.of(flexMTMCluster1),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1)));
    assertEquals(
        List.of(flexMTMCluster2),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId2)));
    assertEquals(
        List.of(flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(flexMTMCluster1, flexMTMCluster2),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2)));
    assertEquals(
        List.of(flexMTMCluster1, flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(flexMTMCluster2, flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(flexMTMCluster1, flexMTMCluster2, flexMTMCluster3),
        _flexMTMClusterDao.findFlexMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
  }
}
