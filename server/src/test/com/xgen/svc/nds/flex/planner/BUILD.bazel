load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/appconfig/_public/config",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/serverless/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/nds/tenant/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade/_private/dao",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/nds/tenant/planner",
        "//server/src/unit/com/xgen/cloud/common/util/_public/util:PojoTestUtils",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//server/src/unit/com/xgen/svc/nds/serverless/model:serverlessTestFactory",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
