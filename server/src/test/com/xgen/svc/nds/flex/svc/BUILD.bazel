load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/organization/_public/model",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/test/com/xgen/cloud/services/payments/modules/paymentMethod/common",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
