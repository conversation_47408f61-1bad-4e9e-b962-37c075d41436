package com.xgen.svc.nds.azure.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.nds.azure._private.dao.AzureCloudProviderContainerDao;
import com.xgen.cloud.nds.azure._private.dao.AzurePeerNetworkDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer.FieldDefs;
import com.xgen.cloud.nds.azure._public.model.AzurePeerNetwork;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubnet;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AzureCloudProviderContainerDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private NDSGroupDao _groupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private AzureCloudProviderContainerDao _containerDao;

  @Inject private AzurePeerNetworkDao _peerDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
  }

  @Test
  public void testGetAzureContainersForGroup() {
    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    final AzureCloudProviderContainer container1 =
        new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer());
    _groupDao.addCloudContainer(groupId, container1);

    final AzureCloudProviderContainer container2 =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer(AzureRegionName.ASIA_EAST));
    _groupDao.addCloudContainer(groupId, container2);

    List<AzureCloudProviderContainer> containers =
        _containerDao.getAzureContainersForGroup(groupId);
    assertEquals(containers.size(), 2);
  }

  @Test
  public void testDeleteUnusedContainer() {
    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    final AzureCloudProviderContainer container =
        new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer());
    final ObjectId containerId = _groupDao.addCloudContainer(groupId, container);

    // Group has 1 container
    assertEquals(1, _groupDao.find(groupId).get().getCloudProviderContainers().size());

    _containerDao.deleteUnusedContainer(groupId, containerId, container.getLastUpdate());
    // Group still has 1 container because container is provisioned
    assertEquals(1, _groupDao.find(groupId).get().getCloudProviderContainers().size());

    _containerDao.unsetProvisionedFields(groupId, containerId);

    final ObjectId peerId =
        _peerDao.addAzurePeerNetwork(groupId, new AzurePeerNetwork(containerId, "", "", "", ""));
    _containerDao.deleteUnusedContainer(groupId, containerId, container.getLastUpdate());

    // Group still has 1 container because container has a peer
    assertEquals(1, _groupDao.find(groupId).get().getCloudProviderContainers().size());

    _peerDao.deleteContainerPeer(groupId, containerId, peerId);
    _containerDao.deleteUnusedContainer(groupId, containerId, new Date(0));

    // Container still exists because last update did not match
    assertEquals(1, _groupDao.find(groupId).get().getCloudProviderContainers().size());

    _containerDao.deleteUnusedContainer(
        groupId,
        containerId,
        _groupDao.find(groupId).get().getCloudProviderContainers().get(0).getLastUpdate());

    // Container is deleted
    assertEquals(0, _groupDao.find(groupId).get().getCloudProviderContainers().size());
  }

  @Test
  public void testAddCloudContainer() {

    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    final AzureCloudProviderContainer container =
        new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer());
    final ObjectId containerId = _groupDao.addCloudContainer(groupId, container);

    _containerDao.setProvisionedFields(
        groupId,
        containerId,
        "resource-group-name",
        "virtual-network-id",
        new AzureSubnet("subnet-name"),
        "network-security-group-name");

    NDSGroup group = _groupDao.find(groupId).get();
    AzureCloudProviderContainer dbContainer =
        (AzureCloudProviderContainer) group.getCloudProviderContainers().get(0);

    assertEquals(container.getAzureSubscriptionId(), dbContainer.getAzureSubscriptionId());
    assertEquals("resource-group-name", dbContainer.getResourceGroupName().get());
    assertEquals("virtual-network-id", dbContainer.getVirtualNetworkName().get());
    assertEquals("subnet-name", dbContainer.getSubnets().get(0).getSubnetName());
    assertEquals("network-security-group-name", dbContainer.getNetworkSecurityGroupName().get());
    assertFalse(dbContainer.getNetworkPermissionListLastUpdated().isPresent());

    final Date now = new Date();
    _groupDao.setCloudContainerNetworkPermissionListLastUpdated(groupId, containerId, now);

    group = _groupDao.find(groupId).get();
    dbContainer = (AzureCloudProviderContainer) group.getCloudProviderContainers().get(0);

    assertEquals(now, dbContainer.getNetworkPermissionListLastUpdated().get());

    _containerDao.unsetProvisionedFields(groupId, containerId);

    group = _groupDao.find(groupId).get();
    dbContainer = (AzureCloudProviderContainer) group.getCloudProviderContainers().get(0);

    assertFalse(dbContainer.isProvisioned());
    assertFalse(dbContainer.getResourceGroupName().isPresent());
    assertFalse(dbContainer.getVirtualNetworkName().isPresent());
    assertTrue(dbContainer.getSubnets().isEmpty());
    assertFalse(dbContainer.getNetworkSecurityGroupName().isPresent());
    assertFalse(dbContainer.getNetworkPermissionListLastUpdated().isPresent());
  }

  @Test
  public void testUnsetProvisionedFieldsWithLastUpdateDate() {
    final ObjectId groupId1 = new ObjectId();
    _ndsGroupSvc.create(groupId1, new NDSManagedX509(), false);

    final Date cloudProviderContainerDate = new Date();
    final AzureCloudProviderContainer providerContainer =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer()
                .append(
                    CloudProviderContainer.FieldDefs.LAST_UPDATE_DATE, cloudProviderContainerDate));
    final ObjectId containerId = _groupDao.addCloudContainer(groupId1, providerContainer);

    final Optional<NDSGroup> maybeNdsGroup = _groupDao.find(groupId1);
    assertTrue(maybeNdsGroup.isPresent());
    assertEquals(1, maybeNdsGroup.get().getCloudProviderContainers().size());
    assertEquals(
        cloudProviderContainerDate,
        maybeNdsGroup.get().getCloudProviderContainers().get(0).getLastUpdate());

    // Act
    final Date customUnprovisionedDate =
        new Date(new Date().getTime() - TimeUnit.HOURS.toMillis(1));
    _containerDao.unsetProvisionedFields(groupId1, containerId, customUnprovisionedDate);

    final Optional<NDSGroup> maybeUpdatedNdsGroup = _groupDao.find(groupId1);
    assertTrue(maybeUpdatedNdsGroup.isPresent());
    assertEquals(
        customUnprovisionedDate,
        maybeUpdatedNdsGroup.get().getCloudProviderContainers().get(0).getLastUpdate());
  }

  @Test
  public void testFindContainersUnprovisionedSince() {
    final ObjectId groupId1 = new ObjectId();
    _ndsGroupSvc.create(groupId1, new NDSManagedX509(), false);
    final ObjectId groupId2 = new ObjectId();
    _ndsGroupSvc.create(groupId2, new NDSManagedX509(), false);

    // Empty collection should return no results
    assertEquals(
        0, _containerDao.findContainersUnprovisionedSince(new Date(Long.MAX_VALUE)).size());

    final Date provisionedContainer1Date = new Date();
    final AzureCloudProviderContainer providerContainer1 =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer()
                .append(
                    CloudProviderContainer.FieldDefs.LAST_UPDATE_DATE, provisionedContainer1Date));
    final ObjectId provisionedContainer1Id =
        _groupDao.addCloudContainer(groupId1, providerContainer1);

    // A provisioned container should never be returned
    assertEquals(
        0, _containerDao.findContainersUnprovisionedSince(new Date(Long.MAX_VALUE)).size());

    final Date unprovisionedContainer1Date = new Date();
    final AzureCloudProviderContainer unprovisionedContainer1 =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getUnprovisionedAzureContainer()
                .append(
                    CloudProviderContainer.FieldDefs.LAST_UPDATE_DATE,
                    unprovisionedContainer1Date));
    final ObjectId unprovisionedContainer1Id =
        _groupDao.addCloudContainer(groupId1, unprovisionedContainer1);

    // A unprovisioned container not returned if not before supplied date
    assertEquals(
        0, _containerDao.findContainersUnprovisionedSince(unprovisionedContainer1Date).size());

    // A unprovisioned container is returned if it is after the supplied date
    final List<Pair<ObjectId, CloudProviderContainer>> result =
        _containerDao.findContainersUnprovisionedSince(
            new Date(unprovisionedContainer1Date.getTime() + 1));
    assertEquals(1, result.size());
    assertEquals(groupId1, result.get(0).getLeft());
    assertEquals(unprovisionedContainer1Id, result.get(0).getRight().getId());

    final Date unprovisionedContainer2Date = new Date();
    final AzureCloudProviderContainer unprovisionedContainer2 =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getUnprovisionedAzureContainer()
                .append(
                    CloudProviderContainer.FieldDefs.LAST_UPDATE_DATE,
                    unprovisionedContainer2Date));
    final ObjectId unprovisionedContainer2Id =
        _groupDao.addCloudContainer(groupId2, unprovisionedContainer2);

    // Multiple results returned for different groups
    final List<Pair<ObjectId, CloudProviderContainer>> result2 =
        _containerDao.findContainersUnprovisionedSince(
            new Date(unprovisionedContainer2Date.getTime() + 1));
    assertEquals(2, result2.size());

    final Date unprovisionedContainer3Date = new Date();
    final AzureCloudProviderContainer unprovisionedContainer3 =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getUnprovisionedAzureContainer()
                .append(
                    CloudProviderContainer.FieldDefs.LAST_UPDATE_DATE, unprovisionedContainer2Date)
                .append(FieldDefs.VNET_NAME, "LiterallyAnything"));
    final ObjectId unprovisionedContainer3Id =
        _groupDao.addCloudContainer(groupId2, unprovisionedContainer3);

    // No result for the container with a vnet name, even if it is unprovisioned for some time
    final List<Pair<ObjectId, CloudProviderContainer>> result3 =
        _containerDao.findContainersUnprovisionedSince(
            new Date(unprovisionedContainer3Date.getTime() + 1));
    assertEquals(2, result3.size());
  }

  @Test
  public void testAddAzureCloudContainer() {
    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    final ObjectId containerId =
        _containerDao
            .addAzureCloudContainer(
                groupId, new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer()))
            .get();

    final BasicDBObject doc = (BasicDBObject) _groupDao.getDbCollection().findOne();
    assertEquals(1, ((BasicDBList) doc.get("cloudProviderContainers")).size());

    final BasicDBObject container =
        (BasicDBObject) ((BasicDBList) doc.get("cloudProviderContainers")).get(0);
    assertEquals(CloudProvider.AZURE.name(), container.get("providerName"));
    assertEquals(containerId, container.get("id"));

    _containerDao.addAzureCloudContainer(
        groupId,
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer(AzureRegionName.US_WEST)));
    final BasicDBObject doc2 = (BasicDBObject) _groupDao.getDbCollection().findOne();
    assertEquals(2, ((BasicDBList) doc2.get("cloudProviderContainers")).size());

    assertFalse(
        _containerDao
            .addAzureCloudContainer(
                groupId, new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer()))
            .isPresent());

    _containerDao.addAzureCloudContainer(
        groupId,
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer(AzureRegionName.EUROPE_NORTH)));

    assertFalse(
        _containerDao
            .addAzureCloudContainer(
                groupId,
                new AzureCloudProviderContainer(
                    NDSModelTestFactory.getAzureContainer(AzureRegionName.US_WEST)))
            .isPresent());

    final BasicDBObject updatedDoc = (BasicDBObject) _groupDao.getDbCollection().findOne();
    final BasicDBList containers = (BasicDBList) updatedDoc.get("cloudProviderContainers");
    final int numUSWestContainers =
        containers.stream()
            .filter(
                containerElem ->
                    ((BasicDBObject) containerElem).getString("region").equals("US_WEST"))
            .collect(Collectors.toList())
            .size();

    assertEquals(1, numUSWestContainers);
  }
}
