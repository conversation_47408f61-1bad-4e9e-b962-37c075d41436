package com.xgen.svc.nds.azure.dao;

import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.DUPLICATE_KEY_ERROR_CODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.MongoWriteException;
import com.xgen.cloud.nds.azure._private.dao.privatelink.AzurePrivateLinkConnectionInboundNATRuleDao;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateLinkConnectionInboundNATRule;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.PrivateLinkConnectionRule.Usage;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AzurePrivateLinkConnectionInboundNATRuleDaoIntTests extends JUnit5BaseSvcTest {

  @Inject
  private AzurePrivateLinkConnectionInboundNATRuleDao _azurePrivateLinkConnectionInboundNATRuleDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _azurePrivateLinkConnectionInboundNATRuleDao.ensureIndexes();
  }

  @Test
  public void testReservePrivateLinkConnectionRule() {
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            ObjectId.get(),
            ObjectId.get(),
            1024,
            "pl-0.mongodb.net",
            new Date(42),
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.HIDDEN_SECONDARY_NODE);

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);
    final Optional<AzurePrivateLinkConnectionInboundNATRule> savedRule =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-0.mongodb.net");
    assertTrue(savedRule.isPresent());

    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getGroupId(), savedRule.get().getGroupId());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getInstanceId(), savedRule.get().getInstanceId());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getReservedPortNumber(),
        savedRule.get().getReservedPortNumber());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getInstanceHostname(),
        savedRule.get().getInstanceHostname());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getDeletedDate(),
        savedRule.get().getDeletedDate());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getNdsProcessPort(),
        savedRule.get().getNdsProcessPort());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getPrivateLinkConnectionId(),
        savedRule.get().getPrivateLinkConnectionId());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule0.getInboundNATRuleName(),
        savedRule.get().getInboundNATRuleName());
    assertEquals(azurePrivateLinkConnectionInboundNATRule0.getUsage(), savedRule.get().getUsage());

    try {
      _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
          azurePrivateLinkConnectionInboundNATRule0);
    } catch (final MongoWriteException pE) {
      assertEquals(DUPLICATE_KEY_ERROR_CODE.intValue(), pE.getCode());
    }
  }

  @Test
  public void testFindConnectionRuleForBiConnectorInstance() {

    final ObjectId groupId = ObjectId.get();
    final ObjectId instanceId = ObjectId.get();
    final AzurePrivateLinkConnectionInboundNATRule biRule =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            instanceId,
            1024,
            "pl-0-bi.mongodb.net",
            null,
            NDSDefaults.MONGOSQLD_PUBLIC_PORT,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.BI_CONNECTOR);

    final AzurePrivateLinkConnectionInboundNATRule mongodRule =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            instanceId,
            1025,
            "pl-0.mongodb.net",
            null,
            NDSDefaults.MONGOD_PUBLIC_PORT,
            ObjectId.get(),
            "pl-1-inbound-nat-rule",
            Usage.VISIBLE_NODE);

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        biRule);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        mongodRule);

    final List<AzurePrivateLinkConnectionInboundNATRule> biRules =
        _azurePrivateLinkConnectionInboundNATRuleDao.findConnectionRuleForBiConnectorInstance(
            instanceId);
    assertEquals(1, biRules.size());
    assertEquals(biRule, biRules.get(0));
  }

  @Test
  public void testFindPrivateLinkConnectionRuleFromHostname() {
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            ObjectId.get(),
            ObjectId.get(),
            1024,
            "pl-0.mongodb.net",
            new Date(42),
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.HIDDEN_SECONDARY_NODE);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule1 =
        new AzurePrivateLinkConnectionInboundNATRule(
            ObjectId.get(),
            ObjectId.get(),
            1025,
            "pl-1.mongodb.net",
            new Date(42),
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.HIDDEN_SECONDARY_NODE);

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule1);

    final Optional<AzurePrivateLinkConnectionInboundNATRule> rule0 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-0.mongodb.net");
    assertTrue(rule0.isPresent());
    assertEquals(1024, rule0.get().getReservedPortNumber().intValue());

    final Optional<AzurePrivateLinkConnectionInboundNATRule> rule1 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-1.mongodb.net");
    assertTrue(rule1.isPresent());
    assertEquals(1025, rule1.get().getReservedPortNumber().intValue());
  }

  @Test
  public void testFindPrivateLinkConnectionRulesFromInstanceId() {
    final ObjectId instanceId = ObjectId.get();
    final ObjectId secondInstanceId = ObjectId.get();
    final ObjectId groupId = ObjectId.get();
    final ObjectId privateEndpointServiceId = ObjectId.get();
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            instanceId,
            1024,
            "node-0.mongodb.net",
            new Date(42),
            27017,
            privateEndpointServiceId,
            "pl-0-inbound-nat-rule",
            Usage.HIDDEN_SECONDARY_NODE);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule1 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            instanceId,
            1025,
            "node-1.mongodb.net",
            new Date(42),
            27017,
            privateEndpointServiceId,
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule2 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            instanceId,
            1026,
            "node-2.mongodb.net",
            new Date(42),
            27017,
            privateEndpointServiceId,
            "pl-0-inbound-nat-rule",
            Usage.BI_CONNECTOR);
    final AzurePrivateLinkConnectionInboundNATRule
        azurePrivateLinkConnectionInboundNATRule3_differentInstanceId =
            new AzurePrivateLinkConnectionInboundNATRule(
                groupId,
                secondInstanceId,
                1027,
                "node-3.mongodb.net",
                new Date(42),
                27017,
                privateEndpointServiceId,
                "pl-0-inbound-nat-rule",
                Usage.BI_CONNECTOR);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule4 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            instanceId,
            1028,
            "node-4.mongodb.net",
            new Date(42),
            27017,
            privateEndpointServiceId,
            "pl-0-inbound-nat-rule",
            Usage.SNAPSHOT_DOWNLOAD);

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule1);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule2);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule3_differentInstanceId);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule4);

    final List<AzurePrivateLinkConnectionInboundNATRule> expectedInstanceRulesFound =
        List.of(
            azurePrivateLinkConnectionInboundNATRule0,
            azurePrivateLinkConnectionInboundNATRule1,
            azurePrivateLinkConnectionInboundNATRule2,
            azurePrivateLinkConnectionInboundNATRule4);
    final List<AzurePrivateLinkConnectionInboundNATRule> actualInstanceRules =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findProvisionedSingleTargetPrivateLinkConnectionRulesFromInstanceId(instanceId);
    assertFalse(actualInstanceRules.isEmpty());
    assertEquals(expectedInstanceRulesFound.size(), actualInstanceRules.size());
    expectedInstanceRulesFound.forEach(
        expectedRule -> {
          final Optional<AzurePrivateLinkConnectionInboundNATRule> foundRule =
              actualInstanceRules.stream()
                  .filter(
                      actualRule ->
                          actualRule
                              .getReservedPortNumber()
                              .equals(expectedRule.getReservedPortNumber()))
                  .findFirst();
          assertTrue(foundRule.isPresent());
        });
    final Optional<AzurePrivateLinkConnectionInboundNATRule> notFoundRule =
        actualInstanceRules.stream()
            .filter(
                actualRule ->
                    actualRule
                        .getReservedPortNumber()
                        .equals(
                            azurePrivateLinkConnectionInboundNATRule3_differentInstanceId
                                .getReservedPortNumber()))
            .findFirst();
    assertTrue(notFoundRule.isEmpty());

    final List<AzurePrivateLinkConnectionInboundNATRule> ruleForSecondInstance =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findProvisionedSingleTargetPrivateLinkConnectionRulesFromInstanceId(secondInstanceId);
    assertEquals(1, ruleForSecondInstance.size());
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule3_differentInstanceId.getReservedPortNumber(),
        ruleForSecondInstance.get(0).getReservedPortNumber());
  }

  @Test
  public void testGetHighestPortForGroup() {
    final ObjectId groupId = ObjectId.get();
    final int minimumPort = 42;

    // returns minimum port if no existing rules
    assertEquals(
        42,
        _azurePrivateLinkConnectionInboundNATRuleDao.getHighestPortForGroup(groupId, minimumPort));

    // returns minimum port if existing rule under minimum port number (not realistic in practice)
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            22,
            "pl-0.mongodb.net",
            new Date(42),
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE));
    assertEquals(
        42,
        _azurePrivateLinkConnectionInboundNATRuleDao.getHighestPortForGroup(groupId, minimumPort));

    // returns existing rule port if greater than minimum
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            66,
            "pl-1.mongodb.net",
            new Date(42),
            27017,
            ObjectId.get(),
            "pl-1-inbound-nat-rule",
            Usage.VISIBLE_NODE));
    assertEquals(
        66,
        _azurePrivateLinkConnectionInboundNATRuleDao.getHighestPortForGroup(groupId, minimumPort));
  }

  @Test
  public void testGetProvisionedInboundNATRulesForGroup() {
    final ObjectId groupId = ObjectId.get();
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            1024,
            "pl-0.mongodb.net",
            new Date(42),
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule1 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            1025,
            "pl-1.mongodb.net",
            new Date(42),
            27017,
            null,
            null,
            Usage.VISIBLE_NODE);

    // No rules -> empty list
    assertTrue(
        _azurePrivateLinkConnectionInboundNATRuleDao
            .getProvisionedSingleTargetConnectionRulesForGroup(groupId)
            .isEmpty());

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule1);

    // One provisioned rule
    assertEquals(
        List.of(azurePrivateLinkConnectionInboundNATRule0),
        _azurePrivateLinkConnectionInboundNATRuleDao
            .getProvisionedSingleTargetConnectionRulesForGroup(groupId));
  }

  @Test
  public void testGetNewestDeletedInboundNATRule() {
    final ObjectId groupId = ObjectId.get();
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            1024,
            "pl-0.mongodb.net",
            new Date(42),
            null,
            null,
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule1 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            1025,
            "pl-1.mongodb.net",
            new Date(22),
            27017,
            null,
            null,
            Usage.VISIBLE_NODE);

    // No rules - no oldest deleted
    assertTrue(
        _azurePrivateLinkConnectionInboundNATRuleDao
            .getNewestSingleTargetDeletedPrivateLinkConnectionRule(groupId)
            .isEmpty());

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule1);

    // Oldest deleted should be earliest date
    assertEquals(
        azurePrivateLinkConnectionInboundNATRule1,
        _azurePrivateLinkConnectionInboundNATRuleDao
            .getNewestSingleTargetDeletedPrivateLinkConnectionRule(groupId)
            .get());
  }

  @Test
  public void testReclaimUnusedReservation() {
    final ObjectId groupId = ObjectId.get();
    final String oldHostname = "pl-0.mongodb.net";
    final int portNumber = 1024;
    final String newHostname = "pl-1.mongodb.net";
    final ObjectId newInstanceId = ObjectId.get();
    final Usage usage = Usage.BI_CONNECTOR;
    final AzurePrivateLinkConnectionInboundNATRule oldRule =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            portNumber,
            oldHostname,
            new Date(42),
            27017,
            null,
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    final AzurePrivateLinkConnectionInboundNATRule newRule =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            newInstanceId,
            portNumber,
            newHostname,
            new Date(42),
            27017,
            null,
            "pl-0-inbound-nat-rule",
            usage);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        oldRule);

    // Cannot reclaim reservation for incorrect hostname
    assertFalse(
        _azurePrivateLinkConnectionInboundNATRuleDao.reclaimUnusedReservation(
            groupId, "incorrect.hostname", portNumber, newHostname, newInstanceId, usage));

    // Can reclaim reservation for correct parameters
    assertTrue(
        _azurePrivateLinkConnectionInboundNATRuleDao.reclaimUnusedReservation(
            groupId, oldHostname, portNumber, newHostname, newInstanceId, usage));

    final AzurePrivateLinkConnectionInboundNATRule reclaimedRule =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname(newHostname)
            .get();
    assertEquals(newRule, reclaimedRule);
  }

  @Test
  public void testSetInboundNATRuleUnprovisioned() {
    final ObjectId groupId = ObjectId.get();
    final String hostname = "pl-0.mongodb.net";
    final int port = 1024;
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            port,
            hostname,
            null,
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);

    // Check provisioned fields set correctly
    final AzurePrivateLinkConnectionInboundNATRule provisionedRule =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname(hostname)
            .get();
    assertNotNull(provisionedRule.getPrivateLinkConnectionId());
    assertNotNull(provisionedRule.getInboundNATRuleName());
    assertNull(provisionedRule.getDeletedDate());

    _azurePrivateLinkConnectionInboundNATRuleDao
        .setSingleTargetPrivateLinkConnectionRuleUnprovisioned(groupId, port);

    // Check provisioned fields unset correctly
    final AzurePrivateLinkConnectionInboundNATRule unprovisionedRule =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname(hostname)
            .get();
    assertNull(unprovisionedRule.getPrivateLinkConnectionId());
    assertNull(unprovisionedRule.getInboundNATRuleName());
    assertNotNull(unprovisionedRule.getDeletedDate());
  }

  @Test
  public void testSetInboundNATRulesUnprovisioned() {
    final ObjectId groupId = ObjectId.get();
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            1024,
            "pl-0.mongodb.net",
            null,
            27017,
            ObjectId.get(),
            "pl-0-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule1 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            1025,
            "pl-1.mongodb.net",
            null,
            27017,
            ObjectId.get(),
            "pl-1-inbound-nat-rule",
            Usage.VISIBLE_NODE);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);
    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule1);

    // Check provisioned fields set correctly
    final AzurePrivateLinkConnectionInboundNATRule provisionedRule0 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-0.mongodb.net")
            .get();
    assertNotNull(provisionedRule0.getPrivateLinkConnectionId());
    assertNotNull(provisionedRule0.getInboundNATRuleName());
    assertNull(provisionedRule0.getDeletedDate());
    final AzurePrivateLinkConnectionInboundNATRule provisionedRule1 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-1.mongodb.net")
            .get();
    assertNotNull(provisionedRule1.getPrivateLinkConnectionId());
    assertNotNull(provisionedRule1.getInboundNATRuleName());
    assertNull(provisionedRule1.getDeletedDate());

    _azurePrivateLinkConnectionInboundNATRuleDao.setSingleTargetConnectionRulesUnprovisioned(
        List.of(
            azurePrivateLinkConnectionInboundNATRule0, azurePrivateLinkConnectionInboundNATRule1));

    // Check provisioned fields unset correctly
    final AzurePrivateLinkConnectionInboundNATRule unprovisionedRule0 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-0.mongodb.net")
            .get();
    assertNull(unprovisionedRule0.getPrivateLinkConnectionId());
    assertNull(unprovisionedRule0.getInboundNATRuleName());
    assertNotNull(unprovisionedRule0.getDeletedDate());
    final AzurePrivateLinkConnectionInboundNATRule unprovisionedRule1 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-1.mongodb.net")
            .get();
    assertNull(unprovisionedRule1.getPrivateLinkConnectionId());
    assertNull(unprovisionedRule1.getInboundNATRuleName());
    assertNotNull(unprovisionedRule1.getDeletedDate());
  }

  @Test
  public void testSetInboundNATRuleProvisionedFields() {
    final ObjectId groupId = ObjectId.get();
    final int port = 1024;
    final ObjectId privateLinkConnectionId = ObjectId.get();
    final String inboundNATRuleName = "pl-0-inbound-nat-rule";
    final int ndsProcessPort = 27107;
    final AzurePrivateLinkConnectionInboundNATRule azurePrivateLinkConnectionInboundNATRule0 =
        new AzurePrivateLinkConnectionInboundNATRule(
            groupId,
            ObjectId.get(),
            port,
            "pl-0.mongodb.net",
            null,
            null,
            null,
            null,
            Usage.VISIBLE_NODE);

    _azurePrivateLinkConnectionInboundNATRuleDao.reserveSingleTargetPrivateLinkConnectionRule(
        azurePrivateLinkConnectionInboundNATRule0);

    // Check provisioned fields unset
    final AzurePrivateLinkConnectionInboundNATRule unprovisionedRule0 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-0.mongodb.net")
            .get();
    assertNull(unprovisionedRule0.getPrivateLinkConnectionId());
    assertNull(unprovisionedRule0.getInboundNATRuleName());
    assertNull(unprovisionedRule0.getNdsProcessPort());

    assertTrue(
        _azurePrivateLinkConnectionInboundNATRuleDao.setInboundNATRuleProvisionedFields(
            groupId, port, privateLinkConnectionId, inboundNATRuleName, ndsProcessPort));

    // Check provisioned fields set correctly
    final AzurePrivateLinkConnectionInboundNATRule provisionedRule0 =
        _azurePrivateLinkConnectionInboundNATRuleDao
            .findSingleTargetPrivateLinkConnectionRuleFromHostname("pl-0.mongodb.net")
            .get();
    assertEquals(privateLinkConnectionId, provisionedRule0.getPrivateLinkConnectionId());
    assertEquals(inboundNATRuleName, provisionedRule0.getInboundNATRuleName());
    assertEquals(ndsProcessPort, provisionedRule0.getNdsProcessPort().intValue());
  }
}
