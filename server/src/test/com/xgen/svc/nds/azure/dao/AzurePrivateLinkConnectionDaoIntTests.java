package com.xgen.svc.nds.azure.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.azure._private.dao.AzurePrivateLinkConnectionDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateEndpoint;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateLinkConnection;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.CloudProviderContainerTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AzurePrivateLinkConnectionDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AzurePrivateLinkConnectionDao _azurePrivateLinkConnectionDao;

  private ObjectId _groupId;
  private ObjectId _containerId;

  private AzurePrivateLinkConnection _connection;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();

    _ndsGroupDao.ensureIndexes();
    final Pair<ObjectId, ObjectId> pair =
        CloudProviderContainerTestUtils.setupGroupWithSingleAzureContainer(
            _ndsGroupDao, _ndsGroupSvc);
    _groupId = pair.getLeft();
    _containerId = pair.getRight();

    _connection =
        new AzurePrivateLinkConnection(
            new ObjectId(),
            "resourceId",
            "serviceName",
            "balancerName",
            List.of(),
            AzurePrivateLinkConnection.Status.INITIATING,
            "error message",
            true,
            new Date());
  }

  private List<AzurePrivateLinkConnection> fetchAzurePrivateLinkConnections() {
    return _ndsGroupDao
        .find(_groupId)
        .get()
        .getCloudProviderContainer(_containerId)
        .get()
        .getEndpointServices()
        .stream()
        .map(AzurePrivateLinkConnection.class::cast)
        .collect(Collectors.toList());
  }

  @Test
  public void testSetPrivateLinkServiceProvisionedFields() {
    final AzurePrivateLinkConnection azurePrivateLinkConnection = new AzurePrivateLinkConnection();
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        azurePrivateLinkConnection, _groupId, _containerId);

    final AzurePrivateLinkConnection fetchedUnprovisionedPrivateLink =
        fetchAzurePrivateLinkConnections().get(0);

    assertEquals(Status.INITIATING, fetchedUnprovisionedPrivateLink.getStatus());
    assertNull(fetchedUnprovisionedPrivateLink.getLoadBalancerName());
    assertNull(fetchedUnprovisionedPrivateLink.getPrivateLinkServiceName());
    assertNull(fetchedUnprovisionedPrivateLink.getPrivateLinkServiceResourceId());

    final String privateLinkServiceName = "service1";
    final String privateLinkResourceId = "resource1";
    final String loadBalancerName = "loadBalancer1";

    _azurePrivateLinkConnectionDao.setPrivateLinkServiceProvisionedFields(
        _groupId, _containerId, privateLinkServiceName, privateLinkResourceId, loadBalancerName);

    final AzurePrivateLinkConnection fetchedProvisionedPrivateLink =
        fetchAzurePrivateLinkConnections().get(0);

    assertEquals(Status.AVAILABLE, fetchedProvisionedPrivateLink.getStatus());
    assertEquals(privateLinkServiceName, fetchedProvisionedPrivateLink.getPrivateLinkServiceName());
    assertEquals(
        privateLinkResourceId, fetchedProvisionedPrivateLink.getPrivateLinkServiceResourceId());
    assertEquals(loadBalancerName, fetchedProvisionedPrivateLink.getLoadBalancerName());
  }

  @Test
  public void testInitializePrivateLink() {
    final AzurePrivateLinkConnection privateLink = new AzurePrivateLinkConnection();
    assertEquals(
        0,
        _ndsGroupDao.find(_groupId).orElseThrow().getCloudProviderContainers().stream()
            .map(AzureCloudProviderContainer.class::cast)
            .flatMap(c -> c.getEndpointService().stream())
            .count());
    // success
    assertTrue(
        _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
            privateLink, _groupId, _containerId));
    // fail second time
    assertFalse(
        _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
            privateLink, _groupId, _containerId));
    // assert service has been added
    assertEquals(
        1,
        _ndsGroupDao.find(_groupId).orElseThrow().getCloudProviderContainers().stream()
            .map(AzureCloudProviderContainer.class::cast)
            .flatMap(c -> c.getEndpointService().stream())
            .count());
  }

  @Test
  public void testRemovePrivateLink() {
    final AzurePrivateLinkConnection privateLink = new AzurePrivateLinkConnection();
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        privateLink, _groupId, _containerId);

    assertTrue(_azurePrivateLinkConnectionDao.removePrivateLink(_groupId, _containerId));
    assertTrue(fetchAzurePrivateLinkConnections().isEmpty());

    final AzurePrivateLinkConnection privateLinkCantDelete =
        NDSModelTestFactory.getAzurePrivateLinkConnection();
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        privateLinkCantDelete, _groupId, _containerId);

    assertFalse(_azurePrivateLinkConnectionDao.removePrivateLink(_groupId, _containerId));
    assertFalse(fetchAzurePrivateLinkConnections().isEmpty());
  }

  @Test
  public void testSetNeedsUpdateAfter() {
    // container does not exist - return false
    assertFalse(
        _azurePrivateLinkConnectionDao.setNeedsUpdateAfter(
            _groupId, new ObjectId(), _connection.getId(), new Date()));

    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer()));

    // container exists, no private link connections - return false
    assertFalse(
        _azurePrivateLinkConnectionDao.setNeedsUpdateAfter(
            _groupId, containerId, _connection.getId(), new Date()));

    // container exists, private link connection exists - return true
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, containerId);

    final Date needsUpdateAfterDate = new Date();
    assertTrue(
        _azurePrivateLinkConnectionDao.setNeedsUpdateAfter(
            _groupId, containerId, null, needsUpdateAfterDate));

    final NDSGroup groupWithPrivateLink = _ndsGroupDao.find(_groupId).orElseThrow();
    final Optional<Date> privateLinkConnectionWithNeedsUpdateDate =
        groupWithPrivateLink
            .getCloudProviderContainer(containerId)
            .map(AzureCloudProviderContainer.class::cast)
            .flatMap(
                azureCloudProviderContainer -> azureCloudProviderContainer.getEndpointService())
            .flatMap(AzurePrivateLinkConnection::getNeedsUpdateAfter);

    assertTrue(privateLinkConnectionWithNeedsUpdateDate.isPresent());
    assertEquals(needsUpdateAfterDate, privateLinkConnectionWithNeedsUpdateDate.get());
  }

  @Test
  public void testUnsetNeedsUpdateAfter() {
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, _containerId);

    final Date needsUpdateAfterDate = new Date();
    assertTrue(
        _azurePrivateLinkConnectionDao.setNeedsUpdateAfter(
            _groupId, _containerId, null, needsUpdateAfterDate));

    // incorrect date will not unset
    assertFalse(
        _azurePrivateLinkConnectionDao.unsetNeedsUpdateAfter(
            _groupId, _containerId, null, new Date(0)));

    // correct date will unset
    assertTrue(
        _azurePrivateLinkConnectionDao.unsetNeedsUpdateAfter(
            _groupId, _containerId, null, needsUpdateAfterDate));
  }

  @Test
  public void testTryRequestDeletePrivateLink_NoEndpointsExist() {
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, _containerId);

    assertTrue(
        _azurePrivateLinkConnectionDao.tryRequestDeleteEndpointService(
            _groupId, _containerId, null));
  }

  @Test
  public void testTryRequestDeletePrivateLink_EndpointExist() {
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, _containerId);

    _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
        NDSModelTestFactory.getInitiatingAzurePrivateEndpoint(), _groupId, _containerId);

    assertFalse(
        _azurePrivateLinkConnectionDao.tryRequestDeleteEndpointService(
            _groupId, _containerId, null));
  }

  private Optional<AzurePrivateLinkConnection> findConnection() {
    return _ndsGroupDao
        .find(_groupId)
        .flatMap(g -> g.getCloudProviderContainer(_containerId))
        .map(AzureCloudProviderContainer.class::cast)
        .flatMap(azureCloudProviderContainer -> azureCloudProviderContainer.getEndpointService());
  }

  @Test
  public void testAddPrivateEndpointToExistingPrivateLink() {
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, _containerId);

    // test default has no endpoints
    {
      final AzurePrivateLinkConnection connection = findConnection().orElseThrow();
      assertEquals(0, connection.getEndpoints().size());
    }

    // test add endpoint
    final AzurePrivateEndpoint endpoint = new AzurePrivateEndpoint("id", 0, "127.0.0.1");
    assertTrue(
        _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
            endpoint, _groupId, _containerId));
    {
      final AzurePrivateLinkConnection connection = findConnection().orElseThrow();
      assertEquals(1, connection.getEndpoints().size());
      assertEquals(endpoint, connection.getEndpoints().get(0));
    }

    // Adding a new endpoint with the same ID and index fails
    assertFalse(
        _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
            endpoint, _groupId, _containerId));
    {
      final AzurePrivateLinkConnection connection = findConnection().orElseThrow();
      assertEquals(1, connection.getEndpoints().size());
      assertEquals(endpoint, connection.getEndpoints().get(0));
    }

    // Adding a new endpoint with a different ID but the same index fails
    assertFalse(
        _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
            new AzurePrivateEndpoint("differentid", 0, "127.0.0.1"), _groupId, _containerId));
    {
      final AzurePrivateLinkConnection connection = findConnection().orElseThrow();
      assertEquals(1, connection.getEndpoints().size());
      assertEquals(endpoint, connection.getEndpoints().get(0));
    }

    // Adding a new endpoint with a different index but the same ID fails
    assertFalse(
        _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
            new AzurePrivateEndpoint("id", 1, "127.0.0.1"), _groupId, _containerId));
    {
      final AzurePrivateLinkConnection connection = findConnection().orElseThrow();
      assertEquals(1, connection.getEndpoints().size());
      assertEquals(endpoint, connection.getEndpoints().get(0));
    }

    // Adding a new endpoint with a different index and different ID succeeds
    final AzurePrivateEndpoint validEndpoint =
        new AzurePrivateEndpoint("differentid", 1, "127.0.0.1");
    assertTrue(
        _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
            validEndpoint, _groupId, _containerId));
    {
      final AzurePrivateLinkConnection connection = findConnection().orElseThrow();
      assertEquals(2, connection.getEndpoints().size());
      assertEquals(endpoint, connection.getEndpoints().get(0));
      assertEquals(validEndpoint, connection.getEndpoints().get(1));
    }
  }

  @Test
  public void testSetEndpointDeleteRequested() {
    final AzurePrivateLinkConnection plc = NDSModelTestFactory.getAzurePrivateLinkConnection();

    final String interfaceEndpointId = plc.getEndpoints().get(0).getEndpointId();
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(plc, _groupId, _containerId);

    assertTrue(
        _azurePrivateLinkConnectionDao.setEndpointDeleteRequested(
            _groupId, _containerId, interfaceEndpointId));

    final DedicatedEndpointService retrievedPrivateLink = fetchAzurePrivateLinkConnections().get(0);

    assertTrue(retrievedPrivateLink.getEndpoints().get(0).isDeleteRequested());
  }

  @Test
  public void testRemoveEndpointService() {
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, _containerId);

    final AzurePrivateEndpoint endpoint = NDSModelTestFactory.getInitiatingAzurePrivateEndpoint();
    _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
        endpoint, _groupId, _containerId);

    final List<AzurePrivateEndpoint> endpointsFromDB = getEndpointsFromDB(_containerId);
    assertEquals(1, endpointsFromDB.size());

    _azurePrivateLinkConnectionDao.removeEndpoint(
        _groupId, _containerId, endpointsFromDB.get(0).getEndpointId());

    final List<AzurePrivateEndpoint> endpointsFromDBAfterRemove = getEndpointsFromDB(_containerId);
    assertEquals(0, endpointsFromDBAfterRemove.size());
  }

  @Test
  public void testSetPrivateEndpointAvailable() {
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
        _connection, _groupId, _containerId);

    final String resourceId = "resource1";
    final String privateIPAddress = "***********";

    final AzurePrivateEndpoint endpoint = new AzurePrivateEndpoint(resourceId, 0, privateIPAddress);
    _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
        endpoint, _groupId, _containerId);

    final AzurePrivateEndpoint newEndpoint = getEndpointsFromDB(_containerId).get(0);
    assertTrue(newEndpoint.getPrivateEndpointMongoDBHostname().isEmpty());
    assertTrue(newEndpoint.getPrivateEndpointProviderHostname().isEmpty());
    assertEquals(AzurePrivateEndpoint.Status.INITIATING, endpoint.getStatus());
    assertEquals(resourceId, newEndpoint.getEndpointId());
    assertEquals(privateIPAddress, newEndpoint.getPrivateEndpointIPAddress());

    final String mongodbHostname = "azurePrivateLink.com";
    final String providerHostname = "azurePrivateLink.azure.com";
    final String connectionName = "connection-privatelink";
    _azurePrivateLinkConnectionDao.setPrivateEndpointAvailable(
        _groupId, _containerId, "resource1", providerHostname, mongodbHostname, connectionName);

    final AzurePrivateEndpoint availableEndpoint = getEndpointsFromDB(_containerId).get(0);
    assertEquals(mongodbHostname, availableEndpoint.getPrivateEndpointMongoDBHostname().get());
    assertEquals(providerHostname, availableEndpoint.getPrivateEndpointProviderHostname().get());
    assertEquals(connectionName, availableEndpoint.getPrivateEndpointConnectionName());
    assertEquals(AzurePrivateEndpoint.Status.AVAILABLE, availableEndpoint.getStatus());
  }

  @Test
  public void testSetEndpointErrorMessage() {
    final AzurePrivateLinkConnection plc = NDSModelTestFactory.getAzurePrivateLinkConnection();

    final String endpointId = plc.getEndpoints().get(0).getEndpointId();
    final String errorMessage = "testErrorMessage";
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(plc, _groupId, _containerId);

    assertTrue(
        _azurePrivateLinkConnectionDao.setEndpointErrorMessage(
            _groupId, _containerId, endpointId, errorMessage));

    final DedicatedEndpointService retrievedPrivateLink = fetchAzurePrivateLinkConnections().get(0);

    assertEquals(
        errorMessage, retrievedPrivateLink.getEndpoints().get(0).getOptionalErrorMessage().get());
  }

  @Test
  public void testResetFailedEndpoint() {
    final AzurePrivateLinkConnection plc = NDSModelTestFactory.getAzurePrivateLinkConnection();
    final AzurePrivateEndpoint originalEndpoint = plc.getEndpoints().get(0);
    final String endpointId = originalEndpoint.getEndpointId();
    final String privateIPAddress0 = originalEndpoint.getPrivateEndpointIPAddress();
    _azurePrivateLinkConnectionDao.initializePrivateEndpointService(plc, _groupId, _containerId);

    // If endpoint is not in FAILED status, should not update the endpoint ID
    final String newEndpointId = "someFakeId";
    final String privateIPAddress1 = "***********";
    _azurePrivateLinkConnectionDao.resetFailedPrivateEndpoint(
        _groupId, _containerId, endpointId, newEndpointId, privateIPAddress1);
    {
      final DedicatedEndpointService expectedFailUpdate = fetchAzurePrivateLinkConnections().get(0);
      final AzurePrivateEndpoint endpoint =
          (AzurePrivateEndpoint) expectedFailUpdate.getEndpoints().get(0);
      assertEquals(endpointId, endpoint.getEndpointId());
      assertEquals(privateIPAddress0, endpoint.getPrivateEndpointIPAddress());
    }

    // If endpoint is in FAILED status, should update the endpoint ID with new ID, update the
    // privateIPAddress to the new privateIPAddress, set status to INITIATING, and clear error
    // message
    _azurePrivateLinkConnectionDao.setEndpointStatus(
        _groupId, _containerId, endpointId, AzurePrivateEndpoint.Status.FAILED);
    _azurePrivateLinkConnectionDao.setEndpointErrorMessage(
        _groupId, _containerId, endpointId, "some fake error message");
    _azurePrivateLinkConnectionDao.resetFailedPrivateEndpoint(
        _groupId, _containerId, endpointId, newEndpointId, privateIPAddress1);
    {
      final AzurePrivateLinkConnection endpointService = fetchAzurePrivateLinkConnections().get(0);
      final List<AzurePrivateEndpoint> endpoints = endpointService.getEndpoints();
      // should replace existing endpoint
      assertEquals(1, endpoints.size());
      final AzurePrivateEndpoint endpoint = endpoints.get(0);
      assertEquals(newEndpointId, endpoint.getEndpointId());
      assertEquals(AzurePrivateEndpoint.Status.INITIATING, endpoint.getStatus());
      assertEquals(Optional.empty(), endpoint.getOptionalErrorMessage());
      assertEquals(privateIPAddress1, endpoint.getPrivateEndpointIPAddress());
    }

    // If endpoint is in FAILED status, and resource id is the same and you update the same endpoint
    // with a new private IP address
    final String privateIPAddress2 = "***********";
    _azurePrivateLinkConnectionDao.setEndpointStatus(
        _groupId, _containerId, newEndpointId, AzurePrivateEndpoint.Status.FAILED);
    _azurePrivateLinkConnectionDao.setEndpointErrorMessage(
        _groupId, _containerId, newEndpointId, "some fake error message");
    _azurePrivateLinkConnectionDao.resetFailedPrivateEndpoint(
        _groupId, _containerId, newEndpointId, newEndpointId, privateIPAddress2);
    {
      final AzurePrivateLinkConnection endpointService = fetchAzurePrivateLinkConnections().get(0);
      final List<AzurePrivateEndpoint> endpoints = endpointService.getEndpoints();
      // should replace existing endpoint
      assertEquals(1, endpoints.size());
      final AzurePrivateEndpoint endpoint = endpoints.get(0);
      assertEquals(newEndpointId, endpoint.getEndpointId());
      assertEquals(AzurePrivateEndpoint.Status.INITIATING, endpoint.getStatus());
      assertEquals(Optional.empty(), endpoint.getOptionalErrorMessage());
      assertEquals(privateIPAddress2, endpoint.getPrivateEndpointIPAddress());
    }
  }

  @Test
  public void testUpdatePrivateLinkFields() throws Exception {
    final Date beforeInit =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .orElseThrow()
            .getLastUpdate();
    assertNull(beforeInit);

    final AzurePrivateLinkConnection plc = NDSModelTestFactory.getAzurePrivateLinkConnection();
    // initializePrivateEndpointService calls updatePrivateLinkFields which updates the last update
    // date
    assertTrue(
        _azurePrivateLinkConnectionDao.initializePrivateEndpointService(
            plc, _groupId, _containerId));
    final Date afterInit =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .orElseThrow()
            .getLastUpdate();
    assertNotEquals(afterInit, beforeInit);

    Thread.sleep(100);

    // setNeedsUpdateAfter calls updatePrivateLinkFields which updates the last update date
    assertTrue(
        _azurePrivateLinkConnectionDao.setNeedsUpdateAfter(
            _groupId, _containerId, null, new Date()));
    final Date afterSetNeedsUpdate =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .orElseThrow()
            .getLastUpdate();
    assertTrue(afterSetNeedsUpdate.after(afterInit));

    Thread.sleep(100);

    // setEndpointDeleteRequested calls updatePrivateLinkFields which updates the last update date
    final String interfaceEndpointId = plc.getEndpoints().get(0).getEndpointId();
    assertTrue(
        _azurePrivateLinkConnectionDao.setEndpointDeleteRequested(
            _groupId, _containerId, interfaceEndpointId));
    final Date afterEndpointSetDeleted =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .orElseThrow()
            .getLastUpdate();
    assertTrue(afterEndpointSetDeleted.after(afterSetNeedsUpdate));

    Thread.sleep(100);

    // addPrivateEndpointToExistingPrivateLink calls updatePrivateLinkFields which updates the last
    // update date, and also mixes in PUSH operators
    assertTrue(
        _azurePrivateLinkConnectionDao.addPrivateEndpointToExistingPrivateLink(
            NDSModelTestFactory.getInitiatingAzurePrivateEndpoint(), _groupId, _containerId));
    final Date afterPrivateEndpointAddedToExistingPrivateLink =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .orElseThrow()
            .getLastUpdate();
    assertTrue(afterPrivateEndpointAddedToExistingPrivateLink.after(afterEndpointSetDeleted));
  }

  private List<AzurePrivateEndpoint> getEndpointsFromDB(ObjectId containerId) {
    return _ndsGroupDao
        .find(_groupId)
        .flatMap(g -> g.getCloudProviderContainer(containerId))
        .map(AzureCloudProviderContainer.class::cast)
        .flatMap(azureCloudProviderContainer -> azureCloudProviderContainer.getEndpointService())
        .map(AzurePrivateLinkConnection::getEndpoints)
        .orElseThrow();
  }
}
