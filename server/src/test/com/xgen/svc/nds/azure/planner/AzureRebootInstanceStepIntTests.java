package com.xgen.svc.nds.azure.planner;

import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.chef._private.dao.ChefServerStatusDao;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.azure.AzureExternalIntTest;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Collections;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AzureRebootInstanceStepIntTests extends AzureExternalIntTest {

  @Inject private ChefServerStatusDao _chefServerStatusDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private AgentJobSvc _agentJobSvc;
  @Inject private AppSettings _appSettings;

  @Test(timeout = 50 * 60 * 1000L)
  public void testAzureRebootInstanceStep() throws Exception {
    // In order to properly reboot machine, we need a automation config to have been published.
    // However, we don't need to launch 3 nodes in order to run this test successfully.
    getClusterDescriptionDao()
        .save(
            getClusterDescription()
                .copy()
                .setReplicationSpecList(
                    getReplicationSpecs(
                        getClusterDescription().getReplicationSpecsWithShardData().get(0).getId(),
                        1,
                        1))
                .mixin(ClusterDescriptionBuilderTestMixin::new)
                .updateAllHardware(
                    new AzureHardwareSpec.Builder()
                        .setDiskType(AzureDiskType.P10)
                        .setInstanceSize(AzureNDSInstanceSize.M10)
                        .setInstanceFamilyAndOS(
                            AzureInstanceFamily.STANDARD_B,
                            CloudProviderRegistry.getByCloudProvider(CloudProvider.AZURE)
                                .getInstanceHardwareProvider()
                                .getDefaultOs(_appSettings, AzureInstanceFamily.STANDARD_B)))
                .build());

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final String clusterName = getClusterDescription().getName();
    final NDSPlanContext ndsPlanContext = (NDSPlanContext) plan.getPlanContext();

    final Move provisionContainerMove =
        AzureProvisionContainerMove.factoryCreate(
            plan.getPlanContext(), getTags(), getContainer().getId());
    plan.addMove(provisionContainerMove);

    final Move ensureNetworkPermissionsAppliedMove =
        AzureEnsureNetworkPermissionsAppliedMove.factoryCreate(
            plan.getPlanContext(), getTags(), getContainer().getId());
    ensureNetworkPermissionsAppliedMove.addPredecessor(provisionContainerMove);
    plan.addMove(ensureNetworkPermissionsAppliedMove);

    final ProcessAutomationConfigPerClusterMove configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);

    final ObjectId instanceId = getInstanceIds().get(0);

    final Move provisionMachineMove =
        AzureProvisionMachineMove.factoryCreate(
            ndsPlanContext, getTags(), clusterName, instanceId, false);
    plan.addMove(provisionMachineMove);
    provisionMachineMove.addPredecessor(provisionContainerMove);
    configMove.addPredecessor(provisionMachineMove);

    getPlanDao().save(plan);

    try {
      waitForPlanPerformSuccessWithWhitelistSync(plan);

      // Wait for agent ping so that we have a primary
      Thread.sleep(Duration.ofMinutes(1).toMillis());

      final ReplicaSetHardware replicaSetHardware =
          getHardwareDao()
              .findReplicaSetHardwareForInstance(
                  getNDSGroup().getGroupId(), clusterName, instanceId);

      final AzureInstanceHardware instanceHardware =
          (AzureInstanceHardware) getInstanceHardware(replicaSetHardware, instanceId);

      final AzureRebootInstanceStep rebootInstanceStep =
          new AzureRebootInstanceStep(
              ndsPlanContext,
              new Step.State(plan.getId(), plan.getMoves().get(0).getId(), 6, getPlanDao()),
              getContainer(),
              instanceHardware,
              clusterName,
              getAzureApiSvc(),
              _chefServerStatusDao,
              _replicaSetHardwareDao,
              _agentJobSvc);

      testResetInstanceStepInternal(getNDSGroup(), clusterName, instanceId, rebootInstanceStep);
    } finally {
      performUnconditionally(() -> waitForPlanRollbackSuccess(plan));
    }
  }
}
