package com.xgen.svc.nds.azure.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.spy;

import com.azure.resourcemanager.compute.models.Snapshot;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.azure._public.model.AzureAdminBackupSnapshot;
import com.xgen.cloud.nds.azure._public.model.AzureOrphanedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.azure.AzureExternalIntTest;
import com.xgen.svc.nds.azure.planner.snapshot.AbstractAzureCreateManagedDataDiskSnapshotStep.SnapshotType;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AzureAdminBackupSnapshotMoveIntTests extends AzureExternalIntTest {

  @Test(timeout = 30 * 60 * 1000L)
  public void testAzureAdminBackupSnapshotMove() throws InterruptedException, SvcException {
    final Plan setupPlan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccessWithWhitelistSync(setupPlan);
      testPerform();
    } finally {
      waitForPlanRollbackSuccess(setupPlan);
    }
  }

  private void testPerform() throws InterruptedException, SvcException {
    final Plan createAdminSnapshotPlan = new Plan(getGroupId(), getNdsPlanContextFactory());
    final String clusterName = getClusterDescription().getName();
    final ObjectId instanceId = getInstanceIds().get(0);
    final String hostname = getHostname(getClusterDescription(), getInstanceIds());
    final AppUser globalAtlasOperatorUser =
        MmsFactory.createApiUserWithGlobalRole(
            "globalAtlasOperatorUser", Role.GLOBAL_ATLAS_OPERATOR);

    try {
      final NDSAdminJob ndsAdminJob =
          getNDSAdminJobDao()
              .create(
                  createAdminSnapshotPlan.getId(),
                  getGroupId(),
                  clusterName,
                  hostname,
                  NDSAdminJob.Type.TAKE_ADMIN_BACKUP_SNAPSHOT);

      getNDSAdminBackupSnapshotSvc()
          .insertAdminBackupSnapshot(
              getGroupId(),
              getClusterDescription().getName(),
              getInstanceHardware(getGroupId(), clusterName, instanceId),
              hostname,
              globalAtlasOperatorUser,
              "comment");

      final Optional<AdminBackupSnapshot> adminBackupSnapshotOpt =
          getNDSAdminBackupSnapshotSvc().findPendingSnapshot(getGroupId(), clusterName, instanceId);
      assertTrue(adminBackupSnapshotOpt.isPresent());
      final AzureAdminBackupSnapshot azureAdminBackupSnapshot =
          (AzureAdminBackupSnapshot) adminBackupSnapshotOpt.get();

      final AzureAdminBackupSnapshotMove adminBackupSnapshotMove =
          spy(
              AzureAdminBackupSnapshotMove.factoryCreate(
                  createAdminSnapshotPlan.getPlanContext(),
                  getClusterDescription().getName(),
                  instanceId,
                  ndsAdminJob.getId()));
      createAdminSnapshotPlan.addMove(adminBackupSnapshotMove);
      getPlanDao().save(createAdminSnapshotPlan);

      Result<?> result = adminBackupSnapshotMove.perform();
      final Optional<NDSAdminJob> ndsAdminJobJustAfterPerform =
          getNDSAdminJobDao().find(ndsAdminJob.getId());
      assertTrue(ndsAdminJobJustAfterPerform.isPresent());

      if (result.getStatus().isInProgress()) {
        assertEquals(NDSAdminJob.Status.WORKING, ndsAdminJobJustAfterPerform.get().getStatus());
      }

      while (!result.getStatus().isDone()) {
        assertFalse(result.getStatus().isFailed());
        Thread.sleep(Duration.ofSeconds(5).toMillis());
        result = adminBackupSnapshotMove.perform();
      }

      final Optional<NDSAdminJob> ndsAdminJobAfterPerform =
          getNDSAdminJobDao().find(ndsAdminJob.getId());
      assertTrue(ndsAdminJobAfterPerform.isPresent());
      assertEquals(NDSAdminJob.Status.COMPLETE, ndsAdminJobAfterPerform.get().getStatus());

      final List<AdminBackupSnapshot> snapshots =
          getNDSAdminBackupSnapshotSvc()
              .findNonDeletedSnapshotsForCluster(getGroupId(), clusterName);
      assertEquals(1, snapshots.size());

      final AzureAdminBackupSnapshot azureResult = (AzureAdminBackupSnapshot) snapshots.get(0);
      assertEquals(azureAdminBackupSnapshot.getId(), azureResult.getId());
      assertEquals(AdminBackupSnapshot.Status.ACTIVE, azureResult.getStatus());
      final Snapshot snapshot =
          getAzureApiSvc()
              .findSnapshot(
                  getAzureSubscription().getId(),
                  getContainer().getResourceGroupName().get(),
                  azureResult.getDiskSnapshotName(),
                  getLogger());
      assertEquals(
          SnapshotType.EMERGENCY_BACKUP.getValue(),
          snapshot.tags().get(NDSDefaults.SNAPSHOT_TYPE.toLowerCase()));

      final Optional<NDSOrphanedItem> orphanedItemOpt =
          getOrphanedItemDao()
              .findByIdAndCloudProviderAndType(
                  azureResult.getCloudProviderSnapshotId(),
                  azureResult.getCloudProvider(),
                  AzureOrphanedItem.Type.EMERGENCY_SNAPSHOT);
      assertTrue(orphanedItemOpt.isPresent());
      assertEquals(azureResult.getCloudProviderSnapshotId(), orphanedItemOpt.get().getId());
    } finally {
      waitForPlanRollbackSuccess(createAdminSnapshotPlan);
      final Optional<AdminBackupSnapshot> snapshotAfterRollback =
          getNDSAdminBackupSnapshotSvc()
              .findNonDeletedSnapshot(getGroupId(), clusterName, instanceId);

      assertTrue(snapshotAfterRollback.isPresent());
      assertEquals(AdminBackupSnapshot.Status.FAILED, snapshotAfterRollback.get().getStatus());
    }
  }
}
