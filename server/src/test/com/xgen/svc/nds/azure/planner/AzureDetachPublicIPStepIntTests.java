package com.xgen.svc.nds.azure.planner;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.azure._private.dao.AzureCloudProviderContainerDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.nds.azure.AzureExternalIntTest;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Collections;
import org.junit.Test;

public class AzureDetachPublicIPStepIntTests extends AzureExternalIntTest {

  @Inject private AzureCloudProviderContainerDao _cloudProviderContainerDao;
  @Inject private GroupDao _groupDao;

  private void testAzureDetachPublicIPStepInternal(
      final AzureDetachPublicIPStep pStep,
      final String pNetworkInterfaceName,
      final String pPublicIPAddressName) {

    waitForStepPerformDone(pStep);

    retryAssertion(
        Duration.ofMinutes(1),
        () -> verifyPublicIPDetached(pNetworkInterfaceName, pPublicIPAddressName));

    // Ensure rollback attached public IP back
    waitForStepRollbackDone(pStep);

    retryAssertion(
        Duration.ofMinutes(1),
        () -> verifyPublicIPAttached(pNetworkInterfaceName, pPublicIPAddressName));
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAzureDetachPublicIPStep() {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final Move dummyProvisionContainerMove =
        AzureProvisionContainerMove.factoryCreate(
            plan.getPlanContext(), getTags(), getContainer().getId());
    final Move dummyProvisionMachineMove =
        AzureProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            false);

    plan.addMove(dummyProvisionContainerMove);
    plan.addMove(dummyProvisionMachineMove);

    // Need to save plan so we can get state prepared
    getPlanDao().save(plan);

    final NDSPlanContext planContext = (NDSPlanContext) plan.getPlanContext();

    final AzureProvisionContainerMove provisionContainerMove =
        new AzureProvisionContainerMove(
            dummyProvisionContainerMove.getId(),
            Collections.emptySet(),
            Collections.emptySet(),
            plan.getPlanContext(),
            new Move.State(plan.getId(), dummyProvisionContainerMove.getId(), getPlanDao()),
            getTags(),
            getContainer().getId(),
            _cloudProviderContainerDao,
            getNDSGroupDao(),
            getAzureApiSvc(),
            getOrphanedItemSvc(),
            getOrphanedItemDao(),
            _groupDao);

    waitForMovePerformDone(provisionContainerMove);

    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getNDSGroup().getGroupId(),
                getClusterDescription().getName(),
                getInstanceIds().get(0));

    final String hostname =
        DNSRecordUtil.getLegacyDNSRecordForInstance(
            getNDSGroupDao().find(getNDSGroup().getGroupId()).get(),
            replicaSetHardware,
            getClusterDescription(),
            getInstanceIds().get(0),
            NDSSettings.getInstanceDomainName(
                getAppSettings(),
                CloudProvider.AZURE,
                getClusterDescription().getHostnameSubdomainLevel()));

    final AzureCreatePublicIPAddressStep createPublicIPAddressStep =
        AzureCreatePublicIPAddressStep.createPublicIPAddressStepForInstance(
            planContext,
            dummyProvisionMachineMove
                .getState()
                .forStep(AzureProvisionMachineMove.StepNumber.CREATE_PUBLIC_IP_ADDRESS),
            getContainer(),
            getClusterDescription(),
            hostname,
            InstanceHostname.HostnameScheme.LEGACY,
            getAzureApiSvc(),
            getOrphanedItemDao(),
            null,
            _groupDao,
            ResourceOperationTagType.NONE);

    waitForStepPerformDone(createPublicIPAddressStep);

    final AzureCreateNetworkInterfaceStep createNetworkInterfaceStep =
        new AzureCreateNetworkInterfaceStep(
            planContext,
            dummyProvisionMachineMove
                .getState()
                .forStep(AzureProvisionMachineMove.StepNumber.CREATE_NETWORK_INTERFACE),
            getContainer(),
            getClusterDescription(),
            createPublicIPAddressStep.performInternal().getData().getResourceName(),
            hostname,
            InstanceHostname.HostnameScheme.LEGACY,
            getAzureApiSvc(),
            getOrphanedItemDao(),
            _groupDao,
            ResourceOperationTagType.NONE,
            false);

    waitForStepPerformDone(createNetworkInterfaceStep);

    final AzureDetachPublicIPStep step =
        new AzureDetachPublicIPStep(
            planContext,
            new State(plan.getId(), plan.getMoves().get(0).getId(), 2, getPlanDao()),
            getContainer(),
            createNetworkInterfaceStep.performInternal().getData().getResourceName(),
            createPublicIPAddressStep.performInternal().getData().getResourceName(),
            getAzureApiSvc());

    testAzureDetachPublicIPStepInternal(
        step,
        createNetworkInterfaceStep.performInternal().getData().getResourceName(),
        createPublicIPAddressStep.performInternal().getData().getResourceName());
  }
}
