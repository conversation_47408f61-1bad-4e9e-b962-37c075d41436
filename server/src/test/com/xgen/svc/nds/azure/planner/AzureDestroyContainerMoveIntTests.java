package com.xgen.svc.nds.azure.planner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.azure.resourcemanager.network.models.Network;
import com.azure.resourcemanager.network.models.NetworkSecurityGroup;
import com.azure.resourcemanager.resources.models.ResourceGroup;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.azure.AzureExternalIntTest;
import org.junit.Test;

public class AzureDestroyContainerMoveIntTests extends AzureExternalIntTest {

  private void testAzureDestroyContainerMoveInternal(final AzureDestroyContainerMove pMove) {

    final NDSGroup groupBeforePerform = getNDSGroupDao().find(getGroup().getId()).get();
    final AzureCloudProviderContainer originalContainer =
        (AzureCloudProviderContainer)
            groupBeforePerform.getCloudProviderContainer(getContainer().getId()).get();

    waitForMovePerformDone(pMove);

    final NDSGroup groupAfterPerform = getNDSGroupDao().find(getGroup().getId()).get();
    final AzureCloudProviderContainer container =
        (AzureCloudProviderContainer)
            groupAfterPerform.getCloudProviderContainer(getContainer().getId()).get();

    assertFalse(container.getResourceGroupName().isPresent());
    assertFalse(container.getVirtualNetworkName().isPresent());
    assertTrue(container.getSubnets().isEmpty());

    // The security group should still be around at this stage
    final NetworkSecurityGroup nsg =
        getAzureApiSvc()
            .findNetworkSecurityGroup(
                originalContainer.getAzureSubscriptionId(),
                originalContainer.getResourceGroupName().get(),
                originalContainer.getNetworkSecurityGroupName().get(),
                getLogger());
    assertNotNull(nsg);

    final Network vnet =
        getAzureApiSvc()
            .findVirtualNetwork(
                originalContainer.getAzureSubscriptionId(),
                originalContainer.getResourceGroupName().get(),
                originalContainer.getVirtualNetworkName().get(),
                getLogger());
    assertNull(vnet);

    // ensure resource group is orphaned
    final String resourceGroupName = originalContainer.getResourceGroupName().get();
    final ResourceGroup rsg =
        getAzureApiSvc()
            .findResourceGroup(
                originalContainer.getAzureSubscriptionId(), resourceGroupName, getLogger());
    assertNotNull(rsg);

    assertNotNull(getOrphanedItemById(resourceGroupName));

    cleanOrphanedItems();

    verifySecurityGroupDeleted(
        resourceGroupName, originalContainer.getNetworkSecurityGroupName().get());
    verifyResourceGroupDeleted(resourceGroupName);
    verifyCIDRNotInAuthRestrictions(
        getAutomationConfigSvc().findPublishedOrEmpty(getGroup().getId()),
        container.getAtlasCidr());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAzureDestroyContainerMove() throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final AzureProvisionContainerMove provisionContainerMove =
        AzureProvisionContainerMove.factoryCreate(
            plan.getPlanContext(), getTags(), getContainer().getId());

    plan.addMove(provisionContainerMove);

    final AzureDestroyContainerMove destroyContainerMove =
        AzureDestroyContainerMove.factoryCreate(plan.getPlanContext(), getContainer().getId());

    plan.addMove(destroyContainerMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    testAzureDestroyContainerMoveInternal(destroyContainerMove);
  }
}
