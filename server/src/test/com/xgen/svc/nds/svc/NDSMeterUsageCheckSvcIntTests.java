package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mongodb.BasicDBList;
import com.xgen.cloud.common.jobqueue._private.dao.BatchJobDao;
import com.xgen.cloud.common.jobqueue._private.dao.JobsProcessorDao;
import com.xgen.cloud.common.jobqueue._public.model.BatchJob;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.BaseObjectJobHandler;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.meteringChecks.NDSMeterUsageCheckBatchJobHandler;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class NDSMeterUsageCheckSvcIntTests extends JUnit5BaseSvcTest {
  @Inject private BatchJobDao _batchJobDao;
  @Inject private JobsProcessorDao _jobsProcessorDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSMeterUsageCheckSvc _svc;

  @Test
  public void testScheduleMeterUsageChecks() {
    // create a group with no date to check billing - should not be picked up
    final ObjectId groupHasNoNextBillingMeterCheckDate = new ObjectId();
    _ndsGroupSvc.create(
        groupHasNoNextBillingMeterCheckDate,
        new NDSManagedX509(),
        false,
        RegionUsageRestrictions.NONE,
        false,
        NDSDefaults.generateDnsPinPerGroup());

    // create an eligible group with a job ID already on the object
    // we are currently picking up groups with job IDs
    final ObjectId groupHasJobAlready = new ObjectId();
    _ndsGroupSvc.create(
        groupHasJobAlready,
        new NDSManagedX509(),
        false,
        RegionUsageRestrictions.NONE,
        true,
        NDSDefaults.generateDnsPinPerGroup());

    assertNull(_batchJobDao.getLastBatchJob(NDSMeterUsageCheckSvc.BATCH_NAME));

    // 1 eligible group and 2 non-eligible group
    {
      final ObjectId eligibleGroupId = new ObjectId();
      _ndsGroupSvc.create(
          eligibleGroupId,
          new NDSManagedX509(),
          false,
          RegionUsageRestrictions.NONE,
          true,
          NDSDefaults.generateDnsPinPerGroup());
      _svc.scheduleMeterUsageChecks();
      final BatchJob mostRecentBatchJob =
          _batchJobDao.getLastBatchJob(NDSMeterUsageCheckSvc.BATCH_NAME);
      assertNotNull(mostRecentBatchJob);
      assertEquals(NDSMeterUsageCheckSvc.BATCH_NAME, mostRecentBatchJob.getBatchName());
      assertEquals(1, _batchJobDao.findAll().toArray().size());
      final List<Job> jobs =
          _jobsProcessorDao.findAllJobsForHandler(NDSMeterUsageCheckBatchJobHandler.class);
      assertEquals(1, jobs.size());
      assertEquals(
          Set.of(groupHasJobAlready, eligibleGroupId),
          Optional.of(jobs.get(0).getParameters().get(BaseObjectJobHandler.OBJECT_IDS_FIELD))
              .map(BasicDBList.class::cast)
              .map(List::stream)
              .map(s -> s.map(ObjectId.class::cast).collect(Collectors.toSet()))
              .get());
    }
  }
}
