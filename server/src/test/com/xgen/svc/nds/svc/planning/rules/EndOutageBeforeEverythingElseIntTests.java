package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Test;

/*
 * These are actually unit tests. No external services or DB access is required.
 * However, in order to instantiate a move, <PERSON><PERSON><PERSON> has to be up and running.
 */
public class EndOutageBeforeEverythingElseIntTests extends DependencyRuleBaseTests {

  @Test
  public void testApply3NodesWFMH() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, new ObjectId(), clusterDescription));
    final ClusterOutageSimulation clusterOutageSimulation = mock(ClusterOutageSimulation.class);
    doReturn(new ObjectId()).when(clusterOutageSimulation).getId();

    final Host host0 = mock(Host.class);
    doReturn(false).when(host0).getIsPrimary();
    doReturn(false).when(host0).getIsSecondary();
    final Host host1 = mock(Host.class);
    doReturn(true).when(host1).getIsPrimary();
    doReturn(false).when(host1).getIsSecondary();
    final Host host2 = mock(Host.class);
    doReturn(false).when(host2).getIsPrimary();
    doReturn(true).when(host2).getIsSecondary();

    final List<PlannedAction> actions = new ArrayList<>();
    final List<PlannedAction> wfhmActions = new ArrayList<>();
    wfhmActions.add(
        _actionFactory.forWaitForHealthyMachine(
            replicaSetHardware, replicaSetHardware.getHardware().get(0)));
    wfhmActions.add(
        _actionFactory.forWaitForHealthyMachine(
            replicaSetHardware, replicaSetHardware.getHardware().get(1)));
    wfhmActions.add(
        _actionFactory.forWaitForHealthyMachine(
            replicaSetHardware, replicaSetHardware.getHardware().get(2)));
    final PlannedAction endOutageAction =
        _actionFactory.forEndClusterOutageSimulation(clusterDescription, clusterOutageSimulation);

    actions.addAll(wfhmActions);
    actions.add(endOutageAction);

    new EndOutageBeforeEverythingElse().apply(actions);

    // End simulation should come before WFMH moves
    assertEquals(endOutageAction.getSuccessors().size(), 3);
    wfhmActions.stream()
        .forEach(
            wfhmAction -> {
              assertTrue(
                  endOutageAction.getSuccessors().contains(wfhmAction.getLastMove().getId()));
              assertTrue(wfhmAction.getSuccessors().isEmpty());
            });
    assertTrue(endOutageAction.getPredecessors().isEmpty());
  }

  @Test
  public void testApplyNoWFMH() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterOutageSimulation clusterOutageSimulation = mock(ClusterOutageSimulation.class);
    doReturn(new ObjectId()).when(clusterOutageSimulation).getId();

    final List<PlannedAction> actions = new ArrayList<>();
    final PlannedAction endOutageAction =
        _actionFactory.forEndClusterOutageSimulation(clusterDescription, clusterOutageSimulation);
    actions.add(endOutageAction);

    new EndOutageBeforeEverythingElse().apply(actions);

    assertTrue(endOutageAction.getPredecessors().isEmpty());
    assertTrue(endOutageAction.getSuccessors().isEmpty());
  }
}
