package com.xgen.svc.nds.svc.onlinearchive;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class OnlineArchiveSvcAzureIntTests extends OnlineArchiveSvcIntTests {
  @Test
  public void testUpdateCriteria() throws SvcException {
    testUpdateCriteria(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testResolveCollectionUUID() throws SvcException {
    testResolveCollectionUUID(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testResolveFieldTypes() throws SvcException {
    testResolveFieldTypes(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testTuneArchiveFrequency() throws SvcException {
    testTuneArchiveFrequency(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testCalculateDeviationRate() throws SvcException {
    testCalculateDeviationRate(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testUpdateOnlineArchiveDataExpiration() throws SvcException {
    testUpdateOnlineArchiveDataExpirationV3(_group, CloudProvider.AZURE);
  }

  @Test
  public void testSendDataExpirationEmail() {
    _onlineArchiveTestUtils.createCluster(_groupId, CloudProvider.AZURE);

    _onlineArchiveSvc.sendDataExpirationEmail(
        _group,
        OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME,
        "dbName",
        "collName",
        8,
        OnlineArchiveVersion.V3,
        ObjectId.get());
    verifyEmailsSent(
        _group, 1, OnlineArchiveVersion.V3, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);
  }

  @Test
  public void testGetArchiveRunInfo() throws SvcException {
    testGetArchiveRunInfo(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testValidateV3AzureArchiveRegion() {
    final ClusterDescription azureClusterDescription =
        NDSModelTestFactory.getClusterDescription(
            _groupId, "testClusterAZURE", CloudProvider.AZURE);
    _clusterDescriptionDao.save(azureClusterDescription);
    final OnlineArchive azureOnlineArchive =
        _onlineArchiveTestUtils
            .getOnlineArchive(_groupId, CloudProvider.AZURE, OnlineArchiveVersion.V3)
            .copy()
            .setDataProcessRegion(null)
            .build();
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(_groupId, "testClusterAWS", CloudProvider.AWS);
    _clusterDescriptionDao.save(awsClusterDescription);

    // empty data process region
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(azureClusterDescription, azureOnlineArchive);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("Data Process Region"));
    }

    final OnlineArchive onlineArchive1 =
        azureOnlineArchive
            .copy()
            .setDataProcessRegion(new OnlineArchive.DataProcessRegion("GCP", "some-region"))
            .build();

    // cloud provider not supported
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(azureClusterDescription, onlineArchive1);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_STORAGE_CLOUD_PROVIDER_NOT_SUPPORTED, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("GCP"));
    }

    final OnlineArchive onlineArchive2 =
        azureOnlineArchive
            .copy()
            .setDataProcessRegion(new OnlineArchive.DataProcessRegion("azure", "some-region"))
            .build();

    // wrong cloud provider case
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(azureClusterDescription, onlineArchive2);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("Data Process Region - Cloud Provider"));
    }

    // azure cluster region is supported in DLZ and does not match archive region
    final OnlineArchive onlineArchive3 =
        azureOnlineArchive
            .copy()
            .setDataProcessRegion(new OnlineArchive.DataProcessRegion("AZURE", "US_EAST_2"))
            .build();

    // cluster provider does not match archive provider
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(awsClusterDescription, onlineArchive3);
    } catch (final SvcException pE) {
      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_DATA_PROCESS_REGION_CLUSTER_CLOUD_PROVIDER_MISMATCH,
          pE.getErrorCode());
    }

    // cluster region is supported in DLZ and does not match archive region
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(azureClusterDescription, onlineArchive3);
    } catch (final SvcException pE) {
      fail();
    }

    final OnlineArchive onlineArchive4 =
        azureOnlineArchive
            .copy()
            .setDataProcessRegion(new OnlineArchive.DataProcessRegion("AZURE", "US_EAST_100"))
            .build();

    // region not valid
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(azureClusterDescription, onlineArchive4);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("Data Process Region - Region"));
    }

    final OnlineArchive onlineArchive5 =
        azureOnlineArchive
            .copy()
            .setDataProcessRegion(new OnlineArchive.DataProcessRegion("AZURE", "ASIA_EAST"))
            .build();

    // region not supported by DLS
    try {
      _onlineArchiveSvc.validateV3DataProcessRegion(azureClusterDescription, onlineArchive5);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_STORAGE_REGION_NOT_SUPPORTED, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("ASIA_EAST"));
    }
  }

  @Test
  public void testEnsureV3DataProcessingRegion() {
    testEnsureV3DataProcessingRegion(_organization, _group, CloudProvider.AZURE);
  }

  @Test
  public void testCreate() throws SvcException {
    _onlineArchiveTestUtils.createCluster(_groupId, CloudProvider.AZURE);

    {
      // azure online archive feature flag enabled
      try {
        final OnlineArchive azureOnlineArchive =
            new OnlineArchive.Builder(
                    _onlineArchiveTestUtils
                        .getOnlineArchive(
                            _groupId, CloudProvider.AZURE, OnlineArchive.OnlineArchiveVersion.V3)
                        .toDBObject())
                .setArchiveId(ObjectId.get())
                .setDbName("v3db")
                .setCollName("v3coll")
                .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
                .setDataProcessRegion(new OnlineArchive.DataProcessRegion("AZURE", "US_EAST_2"))
                .build();
        _onlineArchiveSvc.create(azureOnlineArchive, AuditInfoHelpers.fromSystem());
      } catch (final SvcException pE) {
        fail();
      }
    }

    {
      // empty partition field
      try {
        final OnlineArchive onlineArchive =
            new OnlineArchive.Builder(
                    _onlineArchiveTestUtils
                        .getOnlineArchive(
                            _groupId, CloudProvider.AZURE, OnlineArchive.OnlineArchiveVersion.V3)
                        .toDBObject())
                .setArchiveId(ObjectId.get())
                .setDbName("v3db")
                .setCollName("v3coll2")
                .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
                .setDataProcessRegion(new OnlineArchive.DataProcessRegion("AZURE", "US_EAST_2"))
                .setPartitionFields(List.of(new PartitionField("dateField", 0)))
                .build();
        _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
      } catch (final SvcException pE) {
        fail(pE.getMessage());
      }

      // fail creating V1 AWS archive since azure archives exist
      {
        try {
          _onlineArchiveSvc.create(
              _onlineArchiveTestUtils.getOnlineArchive(
                  _groupId, CloudProvider.AWS, OnlineArchiveVersion.V1),
              AuditInfoHelpers.fromSystem());
          fail();
        } catch (final SvcException pE) {
          assertEquals(
              NDSErrorCode.ONLINE_ARCHIVE_CROSS_CLOUD_PROVIDER_NOT_SUPPORTED, pE.getErrorCode());
        }
      }
    }

    final List<OnlineArchive> onlineArchives =
        _onlineArchiveSvc.getArchivesForCluster(
            _groupId, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);
    assertEquals(2, onlineArchives.size());
    assertEquals(1, onlineArchives.get(1).getPartitionFields().size());
    assertEquals("dateField", onlineArchives.get(1).getPartitionFields().get(0).getFieldName());
  }

  @Test
  public void testCreateAndActivate() throws SvcException {
    testCreateAndActivateV3(_group, CloudProvider.AZURE);
  }

  @Test
  public void testCreateAndActivateV3_ThrowDataLakeException() throws SvcException {
    testCreateAndActivateV3_ThrowDataLakeException(_group, CloudProvider.AZURE);
  }

  @Test
  public void testMarkForPaused() throws SvcException {
    testMarkForPaused(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testSetPauseRequested() throws SvcException {
    testSetPauseRequested(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testSetPauseCompleted() throws SvcException {
    testSetPauseCompleted(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testPauseAndActivate() throws SvcException {
    testPauseAndActivateV3(_group, CloudProvider.AZURE);
  }

  @Test
  public void testCreateOnlineArchiveLimit_ResumeFromPausing() {
    testUpdateOnlineArchiveLimitResumeFrom(
        CloudProvider.AZURE, _group, OnlineArchiveVersion.V3, OnlineArchive.State.PAUSING);
  }

  @Test
  public void testCreateOnlineArchiveLimit_ResumeFromPaused() {
    testUpdateOnlineArchiveLimitResumeFrom(
        CloudProvider.AZURE, _group, OnlineArchiveVersion.V3, OnlineArchive.State.PAUSED);
  }

  @Test
  public void testResumeWithDuplicateCollectionUUID() throws SvcException {
    testResumeWithDuplicateCollectionUUID(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testMarkForDeletion() throws SvcException {
    testMarkForDeletion(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }

  @Test
  public void testMarkArchivesForDeletion() throws SvcException {
    testMarkArchivesForDeletion(_group, CloudProvider.AZURE, OnlineArchiveVersion.V3);
  }
}
