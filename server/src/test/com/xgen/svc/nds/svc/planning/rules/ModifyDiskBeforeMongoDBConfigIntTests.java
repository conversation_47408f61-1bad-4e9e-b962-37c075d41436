package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Test;

/*
 * These are actually unit tests. No external services or DB access is required.
 * However, in order to instantiate a move, Guice has to be up and running.
 */
public class ModifyDiskBeforeMongoDBConfigIntTests extends DependencyRuleBaseTests {

  @Test
  public void testApplyNoMongoDBConfig() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, container.getId(), clusterDescription));
    final NDSGroup ndsGroup = NDSModelTestFactory.getAWSMockedGroup();
    doReturn(Optional.of(container)).when(ndsGroup).getCloudProviderContainer(any());

    final Host host = new Host();
    final InstanceHardware instance = spy(replicaSetHardware.getHardware().get(0));
    final RegionName desiredRegion = instance.getRegion(Optional.of(container));
    // increase disk - modify disk
    doReturn(false).when(instance).doesShardDiskMatch(any(), anyDouble(), any());
    doReturn(true).when(instance).canModifyVolume(anyDouble(), any(), anyBoolean());
    doReturn(true).when(instance).doesShardHardwareSizeMatch(any(), any());
    doReturn(container.getId()).when(instance).getCloudContainerId();

    final List<PlannedAction> modifyDiskActions =
        _actionFactory.forUpdateMachine(
            ndsGroup,
            clusterDescription,
            replicaSetHardware,
            instance,
            desiredRegion,
            container,
            host,
            null,
            Map.of(),
            LOG,
            Map.of(),
            null,
            false,
            false,
            Map.of(),
            false);

    new ModifyDiskBeforeMongoDBConfig().apply(modifyDiskActions);

    modifyDiskActions.forEach(m -> assertTrue(m.getPredecessors().isEmpty()));
    modifyDiskActions.forEach(m -> assertTrue(m.getSuccessors().isEmpty()));
  }

  @Test
  public void testApplyWithMongoDBConfig() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, container.getId(), clusterDescription));
    final NDSGroup ndsGroup = NDSModelTestFactory.getAWSMockedGroup();
    doReturn(Optional.of(container)).when(ndsGroup).getCloudProviderContainer(any());

    final Host host = new Host();
    final AWSInstanceHardware instance =
        (AWSInstanceHardware) spy(replicaSetHardware.getHardware().get(0));
    final RegionName desiredRegion = instance.getRegion(Optional.of(container));
    // increase disk - modify disk
    doReturn(false).when(instance).doesShardDiskMatch(any(), anyDouble(), any());
    doReturn(true).when(instance).canModifyVolume(anyDouble(), any(), anyBoolean());
    doReturn(true).when(instance).doesShardHardwareSizeMatch(any(), any());
    doReturn(container.getId()).when(instance).getCloudContainerId();

    final List<PlannedAction> modifyDiskActions =
        _actionFactory.forUpdateMachine(
            ndsGroup,
            clusterDescription,
            replicaSetHardware,
            instance,
            desiredRegion,
            container,
            host,
            null,
            Map.of(),
            LOG,
            Map.of(),
            null,
            false,
            false,
            Map.of(),
            false);

    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final PlannedAction automationConfigAction =
        _actionFactory.forSyncMongoDBConfigForCluster(
            cd, Collections.emptyList(), false, false, false);

    final List<PlannedAction> actions = new ArrayList<>(modifyDiskActions);
    actions.add(automationConfigAction);

    new ModifyDiskBeforeMongoDBConfig().apply(actions);

    // modify disk should come before the automation config
    modifyDiskActions.stream()
        .forEach(
            m -> {
              assertEquals(1, m.getSuccessors().size());
              assertTrue(m.getSuccessors().contains(automationConfigAction.getLastMove().getId()));
              assertTrue(m.getPredecessors().isEmpty());
            });

    assertTrue(automationConfigAction.getSuccessors().isEmpty());
  }

  @Test
  public void testApplyNoDiskModification() {
    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final PlannedAction automationConfigAction =
        _actionFactory.forSyncMongoDBConfigForCluster(
            cd, Collections.emptyList(), false, false, false);
    new ModifyDiskBeforeMongoDBConfig().apply(Collections.singletonList(automationConfigAction));

    assertTrue(automationConfigAction.getSuccessors().isEmpty());
    assertTrue(automationConfigAction.getPredecessors().isEmpty());
  }
}
