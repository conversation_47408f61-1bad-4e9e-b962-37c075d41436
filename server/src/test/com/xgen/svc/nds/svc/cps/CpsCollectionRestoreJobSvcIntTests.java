package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.constants._public.model.user.UserApiKeyType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreJob;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class CpsCollectionRestoreJobSvcIntTests extends JUnit5BaseSvcTest {
  private static final ObjectId SOURCE_SNAPSHOT_ID = new ObjectId();
  private static final ObjectId SOURCE_PROJECT_ID = oid(100);
  private static final ObjectId SOURCE_CLUSTER_UNIQUE_ID = new ObjectId();
  public static final ObjectId TARGET_PROJECT_ID = ObjectId.get();
  public static final String TARGET_CLUSTER_NAME = "targetClusterName";
  public static final ObjectId TARGET_CLUSTER_UNIQUE_ID = ObjectId.get();
  public static final String TARGET_MDB_VERSION = "8.0.0";
  public static final String TARGET_FCV = "7.3";

  @Inject private CpsCollectionRestoreJobSvc underTest;

  @Test
  public void testCreateAndFindCollectionRestoreJob() throws SvcException {
    final ObjectId jobId1 = createCollectionRestoreJob(null);
    final ObjectId jobId2 = createCollectionRestoreJob(new BSONTimestamp(100000000, 3));

    final CollectionRestoreJob job1 = underTest.findById(jobId1).orElseThrow();
    assertEquals(jobId1, job1.getId());
    assertEquals(
        getCollectionRestoreRequest(), job1.getCollectionRestore().getCollectionRestoreRequest());

    final List<CollectionRestoreJob> jobs =
        underTest.findBySourceCluster(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID);
    assertEquals(2, jobs.size());
    assertEquals(
        Set.of(jobId1, jobId2),
        jobs.stream().map(SystemClusterJob::getId).collect(Collectors.toSet()));
  }

  private ObjectId createCollectionRestoreJob(@Nullable BSONTimestamp sourcePitTime)
      throws SvcException {
    final Organization org = new Organization.Builder().name("").id(new ObjectId()).build();
    final AppUser user = new AppUser();
    final BackupSnapshot snapshot = createSnapshot();

    final AuditInfo auditInfo = new AuditInfo("", "", UserApiKeyType.INTERNAL, EventSource.MMS);
    final CollectionRestoreRequest restoreRequest = getCollectionRestoreRequest();

    return underTest.create(org, user, snapshot, sourcePitTime, restoreRequest, auditInfo);
  }

  private CollectionRestoreRequest getCollectionRestoreRequest() {
    return new CollectionRestoreRequest(
        TARGET_PROJECT_ID,
        TARGET_CLUSTER_NAME,
        TARGET_CLUSTER_UNIQUE_ID,
        TARGET_MDB_VERSION,
        TARGET_FCV,
        CollectionRestoreRequest.IndexRestoreOption.RESTORE_ALL_INDEXES,
        CollectionRestoreRequest.RollbackStrategy.ROLLBACK_ALL_COLLECTIONS,
        CollectionRestoreRequest.WriteStrategy.OVERWRITE_IF_EXISTS,
        List.of(new CollectionRestoreRequest.RestoreName("sourceDb1", "targetDb1")),
        List.of(new CollectionRestoreRequest.RestoreName("db3.coll1", "db3.new-coll1")),
        "");
  }

  private BackupSnapshot createSnapshot() {
    final BackupSnapshot snapshot = mock(BackupSnapshot.class);
    when(snapshot.getId()).thenReturn(SOURCE_SNAPSHOT_ID);
    when(snapshot.getProjectId()).thenReturn(SOURCE_PROJECT_ID);
    when(snapshot.getClusterUniqueId()).thenReturn(SOURCE_CLUSTER_UNIQUE_ID);
    return snapshot;
  }
}
