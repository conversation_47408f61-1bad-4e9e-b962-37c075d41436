package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction.ActionType;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Test;

public class EnvoyInstanceReplacementsUpgradeDowngradeMustBeRollingIntTests
    extends DependencyRuleBaseTests {

  @Test
  public void testApplyUpdateOnly() {
    final List<PlannedAction> actions = new ArrayList<>();
    actions.add(
        _actionFactory.forUpdateServerlessEnvoyInstance(
            new ObjectId(), new ObjectId(), new ObjectId(), CloudProvider.AWS));
    actions.add(
        _actionFactory.forUpdateServerlessEnvoyInstance(
            new ObjectId(), new ObjectId(), new ObjectId(), CloudProvider.AWS));
    actions.add(
        _actionFactory.forUpdateServerlessEnvoyInstance(
            new ObjectId(), new ObjectId(), new ObjectId(), CloudProvider.AWS));

    new EnvoyInstanceReplacementsUpgradeDowngradeMustBeRolling().apply(actions);

    final PlannedAction action0 = actions.get(0);
    final PlannedAction action1 = actions.get(1);
    final PlannedAction action2 = actions.get(2);

    assertTrue(action0.getPredecessors().isEmpty());
    assertEquals(1, action0.getSuccessors().size());
    assertTrue(action0.getSuccessors().contains(action1.getFirstMove().getId()));
    assertEquals(1, action1.getPredecessors().size());
    assertEquals(1, action1.getSuccessors().size());
    assertTrue(action1.getSuccessors().contains(action2.getFirstMove().getId()));
    assertEquals(1, action2.getPredecessors().size());
    assertTrue(action2.getSuccessors().isEmpty());
  }

  @Test
  public void testApplyUpdateAndWaitForHealthy() {
    final List<PlannedAction> actions = new ArrayList<>();
    actions.add(
        _actionFactory.forUpdateServerlessEnvoyInstance(
            new ObjectId(), new ObjectId(), new ObjectId(), CloudProvider.AWS));
    actions.add(
        _actionFactory.forWaitForHealthyEnvoyInstance(
            new ObjectId(), CloudProvider.AWS, new ObjectId()));
    actions.add(
        _actionFactory.forUpdateServerlessEnvoyInstance(
            new ObjectId(), new ObjectId(), new ObjectId(), CloudProvider.AWS));

    new EnvoyInstanceReplacementsUpgradeDowngradeMustBeRolling().apply(actions);

    final PlannedAction action0 = actions.get(0);
    final PlannedAction action1 = actions.get(1);
    final PlannedAction action2 = actions.get(2);

    // WAIT_HEALTHY action has no predecessors
    assertEquals(ActionType.WAIT_HEALTHY, action1.getActionType());
    assertTrue(action1.getPredecessors().isEmpty());
    assertEquals(1, action1.getSuccessors().size());
    assertTrue(action1.getSuccessors().contains(action0.getFirstMove().getId()));
    assertEquals(ActionType.UPDATE, action0.getActionType());
    assertEquals(1, action0.getPredecessors().size());
    assertEquals(1, action0.getSuccessors().size());
    assertTrue(action0.getSuccessors().contains(action2.getFirstMove().getId()));
    assertEquals(ActionType.UPDATE, action2.getActionType());
    assertEquals(1, action2.getPredecessors().size());
    assertTrue(action2.getSuccessors().isEmpty());
  }
}
