package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.List;
import org.junit.Test;

public class SyncClusterPrivateEndpointBeforeEnsureConnectivityIntTests
    extends DependencyRuleBaseTests {

  @Test
  public void apply() {
    final ClusterDescription clusterDescription0 =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription clusterDescription1 =
        NDSModelTestFactory.getClusterDescription(
            clusterDescription0.getGroupId(), "otherClusterName", CloudProvider.AWS);
    final ClusterDescription clusterDescription2 =
        NDSModelTestFactory.getClusterDescription(
            clusterDescription0.getGroupId(), "thirdClusterName", CloudProvider.AWS);

    final PlannedAction ensureConnectivityAction0 =
        _actionFactory.forEnsureConnectivityForClusterTopologyUpdate(
            _ndsPlanContext, clusterDescription0.getName());
    final PlannedAction ensureConnectivityAction2 =
        _actionFactory.forEnsureConnectivityForClusterTopologyUpdate(
            _ndsPlanContext, clusterDescription2.getName());

    final PlannedAction syncAction0 =
        _actionFactory.forSyncClusterPrivateEndpointConnection(
            clusterDescription0, clusterDescription0.getOnlyCloudProvider().orElseThrow());
    final PlannedAction syncAction1 =
        _actionFactory.forSyncClusterPrivateEndpointConnection(
            clusterDescription1, clusterDescription1.getOnlyCloudProvider().orElseThrow());

    final List<PlannedAction> actions =
        List.of(ensureConnectivityAction0, ensureConnectivityAction2, syncAction0, syncAction1);

    new SyncClusterPrivateEndpointBeforeEnsureConnectivity().apply(actions);

    assertTrue(
        ensureConnectivityAction0.getPredecessors().contains(syncAction0.getFirstMove().getId()));
    assertTrue(ensureConnectivityAction0.getSuccessors().isEmpty());
    assertTrue(syncAction0.getPredecessors().isEmpty());
    assertTrue(
        syncAction0.getSuccessors().contains(ensureConnectivityAction0.getFirstMove().getId()));
    assertTrue(syncAction1.getSuccessors().isEmpty());
    assertTrue(syncAction1.getPredecessors().isEmpty());
    assertTrue(ensureConnectivityAction2.getSuccessors().isEmpty());
    assertTrue(ensureConnectivityAction2.getPredecessors().isEmpty());
  }
}
