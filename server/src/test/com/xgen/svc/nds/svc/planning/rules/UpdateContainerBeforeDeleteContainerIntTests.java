package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.Test;

/*
 * These are actually unit tests. No external services or DB access is required.
 * However, in order to instantiate a move, Guice has to be up and running.
 */
public class UpdateContainerBeforeDeleteContainerIntTests extends DependencyRuleBaseTests {

  @Test
  public void testUpdateContainerWithoutDeleteContainer() {
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final PlannedAction updateContainerAction =
        _actionFactory.forUpdateWhitelist(AWS_TAGS, container);

    new UpdateContainerBeforeDeleteContainer()
        .apply(Collections.singletonList(updateContainerAction));

    assertTrue(updateContainerAction.getPredecessors().isEmpty());
    assertTrue(updateContainerAction.getSuccessors().isEmpty());
  }

  @Test
  public void testDeleteContainerWithoutUpdates() {
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final PlannedAction destroyContainerAction = _actionFactory.forDestroyContainer(container);

    new UpdateContainerBeforeDeleteContainer()
        .apply(Collections.singletonList(destroyContainerAction));

    assertTrue(destroyContainerAction.getPredecessors().isEmpty());
    assertTrue(destroyContainerAction.getSuccessors().isEmpty());
  }

  @Test
  public void testDeleteContainerWithUpdates() {
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final PlannedAction destroyContainerAction = _actionFactory.forDestroyContainer(container);
    final PlannedAction updateContainerAction =
        _actionFactory.forUpdateWhitelist(Map.of(), container);

    final List<PlannedAction> actions = new ArrayList<>();
    actions.add(destroyContainerAction);
    actions.add(updateContainerAction);

    new UpdateContainerBeforeDeleteContainer().apply(actions);

    assertTrue(updateContainerAction.getPredecessors().isEmpty());
    assertEquals(1, updateContainerAction.getSuccessors().size());
    assertTrue(
        updateContainerAction
            .getSuccessors()
            .contains(destroyContainerAction.getFirstMove().getId()));
    assertTrue(destroyContainerAction.getSuccessors().isEmpty());
  }

  @Test
  public void testDeleteContainerUnrelatedMachines() {
    final AWSCloudProviderContainer container1 =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final PlannedAction destroyContainerAction = _actionFactory.forDestroyContainer(container1);
    final AWSCloudProviderContainer container2 =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final PlannedAction updateContainerAction =
        _actionFactory.forUpdateWhitelist(Map.of(), container2);

    final List<PlannedAction> actions = new ArrayList<>();
    actions.add(destroyContainerAction);
    actions.add(updateContainerAction);

    new UpdateContainerBeforeDeleteContainer().apply(actions);

    assertTrue(updateContainerAction.getPredecessors().isEmpty());
    assertTrue(updateContainerAction.getSuccessors().isEmpty());
    assertTrue(destroyContainerAction.getPredecessors().isEmpty());
    assertTrue(destroyContainerAction.getSuccessors().isEmpty());
  }
}
