package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.amazonaws.services.securitytoken.model.Credentials;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.chef.CloudChefConfDao;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings.Properties;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.PushBasedLogExportBuilder;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.State;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.streams._public.model.VPCProxyInstanceDescription;
import com.xgen.cloud.streams._public.svc.VPCProxyInstanceDescriptionSvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.serverless.svc.EnvoyInstanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class TemporaryCredentialSvcIntTests extends JUnit5BaseSvcTest {
  private static final String FQDN_VAL = "localhost";
  private static final String SEARCH_FQDN_VAL = "atlas-shard-00-search-abcd123.mmscloudteam.com";
  private static final String ENVOY_INSTANCE_HOSTNAME = "instance-00-00.sniproxy.mmscloudteam.com";
  private static final String VPC_PROXY_INSTANCE_HOSTNAME = "streams-proxy-00-00.mmscloudteam.com";
  private static final ObjectId GROUP_ID_VAL = new ObjectId();
  private static final ObjectId CN_REGIONS_GROUP_ID_VAL = new ObjectId();
  private static final String CLUSTER_NAME = "cluster";
  private static final String ASSUME_ROLE_ARN = "arn:aws:iam::123456789012:role/roleName";

  @Mock private SearchInstanceSvc _searchInstanceSvc;
  @Mock private AWSApiSvc _awsApiSvc;
  @Mock private EnvoyInstanceSvc _envoyInstanceSvc;
  @Mock private VPCProxyInstanceDescriptionSvc _vpcProxyInstanceDescriptionSvc;
  @Mock private CloudChefConfDao _cloudChefConfDao;
  @Mock private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;
  @Mock private NDSPushBasedLogExportEmailSvc _ndsPushBasedLogExportEmailSvc;

  @Inject private ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AppSettings _appSettings;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  private TemporaryCredentialSvc _temporaryCredentialSvc;
  private InstanceHardware _instanceHardware;

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    MockitoAnnotations.openMocks(this);

    Group group = MmsFactory.createGroupWithNDSPlan("group", GROUP_ID_VAL);
    _ndsGroupSvc.ensureGroup(group.getId());

    Group govcloudGroup = MmsFactory.createGroupWithNDSPlan("govGroup", CN_REGIONS_GROUP_ID_VAL);
    _ndsGroupSvc.ensureGroup(govcloudGroup.getId(), true, RegionUsageRestrictions.NONE, false);

    final AWSAccount commercialLogsAccount = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(
        commercialLogsAccount.toBuilder()
            .setForLogs(true)
            .setAssumeRoleARN(ASSUME_ROLE_ARN)
            .build());

    final AWSAccount cnRegionsAccount = new AWSAccount(AWSModelTestFactory.getAWSChinaAccount());
    _awsAccountDao.save(cnRegionsAccount.toBuilder().setForLogs(true).build());

    _appSettings.setProp(
        Properties.PROVISIONING_LOG_UPLOAD_ENABLED,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        Properties.PROVISIONING_LOG_UPLOAD_BUCKET, "ohmybucket", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(Properties.ANOMALIES_BUCKET, "ohmybucket", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        Properties.PROVISIONING_LOG_UPLOAD_CRED_DURATION_MINUTES,
        "120",
        AppSettings.SettingType.MEMORY);

    _instanceHardware =
        InstanceHardware.getHardware(
            InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                FQDN_VAL,
                "public-hostname0",
                "mesh-hostname0",
                "private-hostname0",
                new ObjectId(),
                0,
                AWSNDSInstanceSize.M10,
                false));

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), CLUSTER_NAME));
    _clusterDescriptionDao.save(clusterDescription);

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(
            clusterDescription.getName(), clusterDescription.getGroupId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(
            clusterDescription.getDeploymentClusterName(), 0),
        true,
        false,
        ObjectId.get());

    _replicaSetHardwareDao.setCloudProviderHardware(
        replicaSetHardwareId, new BasicDBList(), List.of(_instanceHardware), false);

    _temporaryCredentialSvc =
        new TemporaryCredentialSvc(
            _appSettings,
            _ndsGroupDao,
            _awsAccountDao,
            _awsApiSvc,
            _envoyInstanceSvc,
            _vpcProxyInstanceDescriptionSvc,
            _replicaSetHardwareSvc,
            _searchInstanceSvc,
            _cloudChefConfDao,
            _ndsCloudProviderAccessSvc,
            _ndsPushBasedLogExportEmailSvc);
  }

  private Credentials setupTempCreds() {
    Credentials credsMock = mock(Credentials.class);
    doReturn(credsMock)
        .when(_awsApiSvc)
        .getTemporarySessionCredentials((AWSAccount) any(), any(), any(), any(), any(), any());

    return credsMock;
  }

  private boolean compareAWSTempCredentialsWithCredentials(
      final NDSAWSTempCredentials awsCreds, final Credentials creds) {
    return awsCreds.getAccessKey().equals(creds.getAccessKeyId())
        && awsCreds.getAccessSecret().equals(creds.getSecretAccessKey())
        && awsCreds.getSessionToken().equals(creds.getSessionToken())
        && awsCreds.getExpirationDate().equals(creds.getExpiration());
  }

  @Test
  void testGetTempSRECredentials_searchInstanceNotFound_throwsInvalidHostName() {
    doReturn(Optional.empty())
        .when(_searchInstanceSvc)
        .findSearchInstanceByHostname(eq(SEARCH_FQDN_VAL));

    SvcException exc =
        assertThrows(
            SvcException.class,
            () ->
                _temporaryCredentialSvc.getTempCredentials(
                    SEARCH_FQDN_VAL,
                    GROUP_ID_VAL.toString(),
                    _instanceHardware.getPublicIP().get(),
                    TemporaryCredentialType.SRE_CREDENTIALS),
            "Expected INVALID_HOST_NAME error when search instance is not found");

    assertEquals(NDSErrorCode.INVALID_HOST_NAME, exc.getErrorCode());
  }

  @Test
  void testGetTempSRECredentials_emptyPublicIp_throwsUnexpectedOrigin() {
    SvcException exc =
        assertThrows(
            SvcException.class,
            () ->
                _temporaryCredentialSvc.getTempCredentials(
                    FQDN_VAL, GROUP_ID_VAL.toString(), "", TemporaryCredentialType.SRE_CREDENTIALS),
            "Expected UNEXPECTED_ORIGIN error when public IP is empty");

    assertEquals(NDSErrorCode.UNEXPECTED_ORIGIN, exc.getErrorCode());
  }

  @Test
  void testGetTempSRECredentials_unexpectedOriginIp_throwsUnexpectedOrigin() {
    SvcException exc =
        assertThrows(
            SvcException.class,
            () ->
                _temporaryCredentialSvc.getTempCredentials(
                    FQDN_VAL,
                    GROUP_ID_VAL.toString(),
                    "***********",
                    TemporaryCredentialType.SRE_CREDENTIALS),
            "Expected UNEXPECTED_ORIGIN error when origin IP is unexpected");

    assertEquals(NDSErrorCode.UNEXPECTED_ORIGIN, exc.getErrorCode());
  }

  @Test
  void testGetTempSRECredentials_envoyInstanceNotFound_throwsInvalidHostName() {
    doReturn(Optional.empty())
        .when(_envoyInstanceSvc)
        .findEnvoyInstanceFromHostname(eq(ENVOY_INSTANCE_HOSTNAME));

    SvcException exc =
        assertThrows(
            SvcException.class,
            () ->
                _temporaryCredentialSvc.getTempCredentials(
                    ENVOY_INSTANCE_HOSTNAME,
                    GROUP_ID_VAL.toString(),
                    _instanceHardware.getPublicIP().get(),
                    TemporaryCredentialType.SRE_CREDENTIALS),
            "Expected INVALID_HOST_NAME error when envoy instance is not found");

    assertEquals(NDSErrorCode.INVALID_HOST_NAME, exc.getErrorCode());
  }

  @Test
  void
      testGetTempCredentials_ProvisioningLogUpload_missingLogUploadBucket_throwsNonexistentBucket() {
    _appSettings.setProp(
        Properties.PROVISIONING_LOG_UPLOAD_BUCKET, null, AppSettings.SettingType.MEMORY);

    SvcException exc =
        assertThrows(
            SvcException.class,
            () ->
                _temporaryCredentialSvc.getTempCredentials(
                    FQDN_VAL,
                    GROUP_ID_VAL.toString(),
                    _instanceHardware.getPublicIP().get(),
                    TemporaryCredentialType.PROVISIONING_LOG_UPLOADS),
            "Expected NONEXISTENT_BUCKET error when log upload bucket is missing");

    String expectedMessage =
        NDSErrorCode.NONEXISTENT_BUCKET.formatMessage(Properties.PROVISIONING_LOG_UPLOAD_BUCKET);
    assertEquals(expectedMessage, exc.getMessage());
  }

  @ParameterizedTest(
      name = "Test testGetTempCredentials_usesFederationToken: Credential Type = {0}")
  @EnumSource(
      value = TemporaryCredentialType.class,
      names = {"SRE_CREDENTIALS", "PROVISIONING_LOG_UPLOADS"})
  void testGetTempCredentials_usesFederationToken(TemporaryCredentialType credentialName)
      throws SvcException {
    final Credentials credsMock = setupTempCreds();

    Credentials credentials =
        _temporaryCredentialSvc.getTempCredentials(
            FQDN_VAL,
            GROUP_ID_VAL.toString(),
            _instanceHardware.getPublicIP().orElseThrow(),
            credentialName);

    assertEquals(credsMock, credentials);

    verify(_awsApiSvc, times(1))
        .getTemporarySessionCredentials(
            (AWSAccount) any(), any(), anyString(), any(), any(), any());
  }

  @Test
  void testGetTempSRECredentials_missingReplicaSetHardware_throwsInvalidHostName() {
    _replicaSetHardwareDao.deleteAllHardwareOnCluster(GROUP_ID_VAL, CLUSTER_NAME);
    final SvcException exc =
        assertThrows(
            SvcException.class,
            () ->
                _temporaryCredentialSvc.getTempCredentials(
                    FQDN_VAL,
                    GROUP_ID_VAL.toString(),
                    _instanceHardware.getPublicIP().get(),
                    TemporaryCredentialType.SRE_CREDENTIALS),
            "not found non-search instance throws");
    assertEquals(NDSErrorCode.INVALID_HOST_NAME, exc.getErrorCode());
  }

  @ParameterizedTest(name = "Test testGetTempCredentials_cloudChefConfFound: Credential Type = {0}")
  @EnumSource(
      value = TemporaryCredentialType.class,
      names = {"SRE_CREDENTIALS", "PROVISIONING_LOG_UPLOADS"})
  void testGetTempCredentials_cloudChefConfFound(TemporaryCredentialType credentialName)
      throws Exception {
    if (credentialName.equals(TemporaryCredentialType.SRE_CREDENTIALS)) {
      CloudChefConf confMock = mock(CloudChefConf.class);
      doReturn(_instanceHardware.getPublicIP().get()).when(confMock).getIp();
      doReturn(confMock).when(_cloudChefConfDao).findCompleteByFqdn(any());
    }
    Credentials credsMock = setupTempCreds();

    Credentials creds =
        _temporaryCredentialSvc.getTempCredentials(
            FQDN_VAL,
            GROUP_ID_VAL.toString(),
            _instanceHardware.getPublicIP().get(),
            credentialName);
    assertEquals(credsMock, creds, "Credentials should match");
  }

  @ParameterizedTest(name = "Test testGetTempCredentials_envoyInstance: Credential Type = {0}")
  @EnumSource(
      value = TemporaryCredentialType.class,
      names = {"SRE_CREDENTIALS", "PROVISIONING_LOG_UPLOADS"})
  void testGetTempCredentials_envoyInstance(TemporaryCredentialType credentialName)
      throws Exception {

    if (credentialName.equals(TemporaryCredentialType.SRE_CREDENTIALS)) {
      EnvoyInstance envoyInstance = mock(EnvoyInstance.class);
      doReturn("***********").when(envoyInstance).getPublicIpAddress();
      doReturn(Optional.of(envoyInstance))
          .when(_envoyInstanceSvc)
          .findEnvoyInstanceFromHostname(ENVOY_INSTANCE_HOSTNAME);
    }

    Credentials credsMock = setupTempCreds();
    Credentials creds =
        _temporaryCredentialSvc.getTempCredentials(
            ENVOY_INSTANCE_HOSTNAME, GROUP_ID_VAL.toString(), "***********", credentialName);
    assertEquals(credsMock, creds, "Credentials should match");
  }

  @Test
  void testGetSRETempCredentials_cachesCredentials() throws SvcException, InterruptedException {
    EnvoyInstance envoyInstance = mock(EnvoyInstance.class);
    doReturn("***********").when(envoyInstance).getPublicIpAddress();
    doReturn(Optional.of(envoyInstance))
        .when(_envoyInstanceSvc)
        .findEnvoyInstanceFromHostname(ENVOY_INSTANCE_HOSTNAME);

    setupTempCreds();
    Credentials credsPreCache =
        _temporaryCredentialSvc.getTempCredentials(
            ENVOY_INSTANCE_HOSTNAME,
            GROUP_ID_VAL.toString(),
            "***********",
            TemporaryCredentialType.SRE_CREDENTIALS);
    verify(_awsApiSvc, times(1))
        .getTemporarySessionCredentials((AWSAccount) any(), any(), any(), any(), any(), any());

    Credentials credsPostCache =
        _temporaryCredentialSvc.getTempCredentials(
            ENVOY_INSTANCE_HOSTNAME,
            GROUP_ID_VAL.toString(),
            "***********",
            TemporaryCredentialType.SRE_CREDENTIALS);
    verify(_awsApiSvc, times(1))
        .getTemporarySessionCredentials((AWSAccount) any(), any(), any(), any(), any(), any());

    assertEquals(credsPreCache, credsPostCache, "uses cached credentials");

    setupTempCreds(); // Reset the mock to return new credentials.
    _temporaryCredentialSvc.invalidateSRETempCredentialCache(); // Clear cache.

    Credentials credsPostExpiry =
        _temporaryCredentialSvc.getTempCredentials(
            ENVOY_INSTANCE_HOSTNAME,
            GROUP_ID_VAL.toString(),
            "***********",
            TemporaryCredentialType.SRE_CREDENTIALS);
    verify(_awsApiSvc, times(2))
        .getTemporarySessionCredentials((AWSAccount) any(), any(), any(), any(), any(), any());

    assertNotEquals(
        credsPostCache, credsPostExpiry, "uses different credentials after cache expires");

    setupTempCreds(); // Reset the mock to return new credentials.

    // Different accounts are cached separately.
    Credentials credsCNAccount =
        _temporaryCredentialSvc.getTempCredentials(
            ENVOY_INSTANCE_HOSTNAME,
            CN_REGIONS_GROUP_ID_VAL.toString(),
            "***********",
            TemporaryCredentialType.SRE_CREDENTIALS);
    verify(_awsApiSvc, times(3))
        .getTemporarySessionCredentials((AWSAccount) any(), any(), any(), any(), any(), any());

    assertNotEquals(credsPreCache, credsCNAccount, "uses different credentials");
  }

  @ParameterizedTest(name = "Test testGetTempCredentials_vpcProxyInstance: Credential Type = {0}")
  @EnumSource(
      value = TemporaryCredentialType.class,
      names = {"SRE_CREDENTIALS", "PROVISIONING_LOG_UPLOADS"})
  void testGetTempCredentials_vpcProxyInstance(TemporaryCredentialType credentialName)
      throws Exception {
    if (credentialName.equals(TemporaryCredentialType.SRE_CREDENTIALS)) {
      VPCProxyInstanceDescription instanceDesc = mock(VPCProxyInstanceDescription.class);
      doReturn(_instanceHardware.getPublicIP().get()).when(instanceDesc).getPublicIp();
      doReturn(Optional.of(instanceDesc))
          .when(_vpcProxyInstanceDescriptionSvc)
          .findByHostname(VPC_PROXY_INSTANCE_HOSTNAME);
    }
    Credentials credsMock = setupTempCreds();
    Credentials creds =
        _temporaryCredentialSvc.getTempCredentials(
            VPC_PROXY_INSTANCE_HOSTNAME,
            GROUP_ID_VAL.toString(),
            _instanceHardware.getPublicIP().get(),
            credentialName);
    assertEquals(credsMock, creds, "Credentials should match");
  }

  @ParameterizedTest(name = "Test testGetTempCredentials_searchInstance: Credential Type = {0}")
  @EnumSource(
      value = TemporaryCredentialType.class,
      names = {"SRE_CREDENTIALS", "PROVISIONING_LOG_UPLOADS"})
  void testGetTempCredentials_searchInstance(TemporaryCredentialType credentialName)
      throws Exception {
    if (credentialName.equals(TemporaryCredentialType.SRE_CREDENTIALS)) {
      doReturn(Optional.of(_instanceHardware))
          .when(_searchInstanceSvc)
          .findSearchInstanceByHostname(SEARCH_FQDN_VAL);
    }
    Credentials credsMock = setupTempCreds();

    Credentials creds =
        _temporaryCredentialSvc.getTempCredentials(
            SEARCH_FQDN_VAL,
            GROUP_ID_VAL.toString(),
            _instanceHardware.getPublicIP().get(),
            credentialName);
    assertEquals(credsMock, creds, "Credentials should match");
  }

  @ParameterizedTest(name = "Test testGetTempCredentials_replicasetInstance: Credential Type = {0}")
  @EnumSource(
      value = TemporaryCredentialType.class,
      names = {"SRE_CREDENTIALS", "PROVISIONING_LOG_UPLOADS"})
  void testGetTempCredentials_replicasetInstance(TemporaryCredentialType credentialName)
      throws Exception {
    Credentials credsMock = setupTempCreds();
    Credentials creds =
        _temporaryCredentialSvc.getTempCredentials(
            FQDN_VAL,
            GROUP_ID_VAL.toString(),
            _instanceHardware.getPublicIP().get(),
            credentialName);
    assertEquals(credsMock, creds, "Credentials should match");
  }

  @Test
  void testGetTempCredentials_pushBasedLogExport_validCachedCredential() throws Exception {
    // Cache credentials with an expiration time in the future
    final Date expirationDate = new Date(System.currentTimeMillis() + (60 * 60 * 1000));
    final NDSAWSTempCredentials cachedAWSCreds =
        new NDSAWSTempCredentials("accessKey", "accessSecret", "sessionToken", expirationDate);
    final PushBasedLogExport pbleConfig =
        new PushBasedLogExportBuilder().state(State.ACTIVE).tempCredentials(cachedAWSCreds).build();
    _ndsGroupDao.setPushBasedLogExportFields(GROUP_ID_VAL, pbleConfig);

    final Credentials returnedTempCreds =
        _temporaryCredentialSvc.getTempCredentials(
            "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT);

    assertTrue(
        compareAWSTempCredentialsWithCredentials(cachedAWSCreds, returnedTempCreds),
        "Returned credentials should be cached credentials");
  }

  @Test
  void testGetTempCredentials_pushBasedLogExport_invalidCachedCredentialGeneratesNewCreds()
      throws Exception {
    // Cache credentials with an expiration time in the past
    final Date pastExpirationDate = new Date(System.currentTimeMillis() - 1000);
    final NDSAWSTempCredentials cachedAWSCreds =
        new NDSAWSTempCredentials(
            "cachedAccessKey", "cachedAccessSecret", "cachedSessionToken", pastExpirationDate);
    final PushBasedLogExport pbleConfig =
        new PushBasedLogExportBuilder().state(State.ACTIVE).tempCredentials(cachedAWSCreds).build();
    _ndsGroupDao.setPushBasedLogExportFields(GROUP_ID_VAL, pbleConfig);

    final NDSAWSTempCredentials newAWSCreds =
        new NDSAWSTempCredentials(
            "newAccessKey",
            "newAccessSecret",
            "newSessionToken",
            new Date(new Date().getTime() + 1000000));

    doReturn(newAWSCreds)
        .when(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(eq(GROUP_ID_VAL), any(), any());

    final Credentials returnedTempCreds =
        _temporaryCredentialSvc.getTempCredentials(
            "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT);

    assertTrue(
        compareAWSTempCredentialsWithCredentials(newAWSCreds, returnedTempCreds),
        "Returned credentials should be cached credentials");

    // Ensure that the returned value was cached in the group DAO
    final Optional<NDSGroup> ndsGroup = _ndsGroupDao.find(GROUP_ID_VAL);
    assertTrue(ndsGroup.isPresent());
    assertTrue(ndsGroup.get().getPushBasedLogExport().getTempCredentials().isPresent());
    compareAWSTempCredentialsWithCredentials(
        (NDSAWSTempCredentials) ndsGroup.get().getPushBasedLogExport().getTempCredentials().get(),
        returnedTempCreds);
  }

  @Test
  void testGetTempCredentials_pushBasedLogExport_noExistingCredentialGeneratesNewCreds()
      throws Exception {
    // No existing credentials in the group document

    final NDSAWSTempCredentials newAWSCreds =
        new NDSAWSTempCredentials(
            "newAccessKey",
            "newAccessSecret",
            "newSessionToken",
            new Date(new Date().getTime() + 1000000));

    doReturn(newAWSCreds)
        .when(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(eq(GROUP_ID_VAL), any(), any());

    final Credentials returnedTempCreds =
        _temporaryCredentialSvc.getTempCredentials(
            "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT);

    assertTrue(
        compareAWSTempCredentialsWithCredentials(newAWSCreds, returnedTempCreds),
        "Returned credentials should be cached credentials");

    // Ensure that the returned value was cached in the group DAO
    final Optional<NDSGroup> ndsGroup = _ndsGroupDao.find(GROUP_ID_VAL);
    assertTrue(ndsGroup.isPresent());
    assertTrue(ndsGroup.get().getPushBasedLogExport().getTempCredentials().isPresent());
    compareAWSTempCredentialsWithCredentials(
        (NDSAWSTempCredentials) ndsGroup.get().getPushBasedLogExport().getTempCredentials().get(),
        returnedTempCreds);
  }

  @ParameterizedTest(
      name =
          "Test testGetTempCredentials_pushBasedLogExport_failureToIssueCredsSendsEmail: Error Code"
              + " = {0}")
  @EnumSource(
      value = NDSErrorCode.class,
      names = {"IAM_ROLE_INVALID_SESSION_DURATION", "CANNOT_ASSUME_ROLE"})
  void testGetTempCredentials_pushBasedLogExport_failureToIssueCredsSendsEmail(
      final NDSErrorCode errCode) throws Exception {
    // No existing credentials, so we should try to issue new ones, and should email the user
    // when we fail to do so
    doThrow(new SvcException(errCode))
        .when(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(eq(GROUP_ID_VAL), any(), any());

    assertThrows(
        SvcException.class,
        () ->
            _temporaryCredentialSvc.getTempCredentials(
                "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT));

    verify(_ndsPushBasedLogExportEmailSvc, times(1))
        .submitPushBasedLogExportRoleValidationFailedEmail(any(), any(), any());
  }

  @Test
  void testGetTempCredentials_pushBasedLogExport_maxTempCredentialsDurationOverride()
      throws Exception {
    // No existing credentials in the group document

    final NDSAWSTempCredentials newAWSCreds =
        new NDSAWSTempCredentials(
            "newAccessKey",
            "newAccessSecret",
            "newSessionToken",
            new Date(new Date().getTime() + 1000000));

    doReturn(newAWSCreds)
        .when(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(eq(GROUP_ID_VAL), any(), any());

    _temporaryCredentialSvc.getTempCredentials(
        "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT);
    // credentials were requested with default duration of 1 hour
    verify(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(), any(), eq(Duration.ofHours(1)));

    // set max PBLE credentials duration
    _appSettings.setProp(
        TemporaryCredentialSvc.PBLE_MAX_CREDENTIAL_SESSION_HOURS_CONFIG,
        "12",
        AppSettings.SettingType.MEMORY);

    _temporaryCredentialSvc.getTempCredentials(
        "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT);
    // credentials were requested with duration of 12 hours
    verify(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(), any(), eq(Duration.ofHours(12)));

    // set max PBLE credentials duration to something invalid
    _appSettings.setProp(
        TemporaryCredentialSvc.PBLE_MAX_CREDENTIAL_SESSION_HOURS_CONFIG,
        "x",
        AppSettings.SettingType.MEMORY);

    // should not cause any problems
    _temporaryCredentialSvc.getTempCredentials(
        "", GROUP_ID_VAL.toString(), "", TemporaryCredentialType.PUSH_BASED_LOG_EXPORT);

    // credentials were requested with default duration of 1 hours again
    verify(_ndsCloudProviderAccessSvc, times(2))
        .getAWSAssumeRoleTempCredentials(any(), any(), eq(Duration.ofHours(1)));
  }

  @Test
  void testGetPBLETempCredentialDuration() throws SvcException {
    final NDSGroup testGroup = new NDSGroup(NDSModelTestFactory.getNDSGroupAllFields());
    final int tempCredentialSessionHourLimit =
        (int) TemporaryCredentialSvc.PBLE_DEFAULT_CREDENTIAL_DURATION.toHours();

    // With property set, uses property
    _appSettings.setProp(
        TemporaryCredentialSvc.PBLE_MAX_CREDENTIAL_SESSION_HOURS_CONFIG,
        String.valueOf(tempCredentialSessionHourLimit + 1),
        AppSettings.SettingType.MEMORY);
    assertEquals(
        Duration.ofHours(tempCredentialSessionHourLimit + 1),
        _temporaryCredentialSvc.getPBLETempCredentialDuration(testGroup));

    // If property is not set, use default.
    _appSettings.clearProperty(TemporaryCredentialSvc.PBLE_MAX_CREDENTIAL_SESSION_HOURS_CONFIG);
    assertEquals(
        Duration.ofHours(tempCredentialSessionHourLimit),
        _temporaryCredentialSvc.getPBLETempCredentialDuration(testGroup));
  }
}
