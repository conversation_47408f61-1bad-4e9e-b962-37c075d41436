package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNotNull;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.google.auth.oauth2.AccessToken;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._private.dao.EventDao;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.util._public.json.JsonOptional;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.LimitTestFactory;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAWSCloudProviderConfig;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeCloudProviderConfig;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeGCPCloudProviderConfig;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeState;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeDataProcessRegion;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView;
import com.xgen.cloud.nds.datalake._public.model.ui.NDSDataLakeDataProcessRegionView;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessDataLakeFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.featureid.NDSCloudProviderAccessFeatureUsageDataLakeFeatureId;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.api.view.atlas._private.dataLake.ApiPrivateAtlasGCPBucketAccessRequestView;
import com.xgen.svc.nds.model.ui.NDSGCPTempCredentialsView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAzureServicePrincipalView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAWSCloudProviderConfigView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAzureCloudProviderConfigView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCloudProviderConfigView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeMetricsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeS3BucketView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStateView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantView;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSDataLakeSvcIntTests extends JUnit5BaseSvcTest {
  static final String REMOTE_ADDR = "192.168.0.1";
  static final String DATA_LAKE_ASSUME_ROLE_IAM_USER = "some:user:arn:/some.username";
  static final String DATA_LAKE_ASSUME_ROLE_ACCESS_KEY = "AREYOUTHEKEYMASTER";
  static final String DATA_LAKE_ASSUME_ROLE_ACCESS_SECRET = "THESECRETCODEIS12345";
  static final String TEST_IAM_ASSUMED_ROLE_ARN = "arn:aws:iam::111111111111:role/role-with-me";
  static final String TEST_IAM_ASSUMED_ROLE_ARN_2 = "arn:aws:iam::************:role/role-with-me";
  static final String TEST_IAM_ROOT_ARN = "arn:aws:iam::************:root";

  static final String TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID = "externalId";
  static final ObjectId TEST_ROLE_ID = new ObjectId();
  static final ObjectId TEST_ROLE_ID_2 = new ObjectId();

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSDataLakePrivateSvc _dataLakePrivateSvc;
  @Inject private NDSDataLakePublicSvc _dataLakePublicSvc;
  @Inject private NDSDataLakeTenantDao _tenantDao;
  @Inject private EventDao eventDao;
  @Inject private AppSettings _appSettings;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;

  private Organization _org;
  private Group _group;
  private AppUser _user;
  private NDSGroup _ndsGroup;
  private AWSAccount _awsAccount;
  private AzureSubscription _azureSubscription;

  private String _gcpAccessTokenValue;
  private Date _gcpAccessTokenExpiration;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _dataLakeTestUtils.setUp();
    _org = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_org);
    _user = MmsFactory.createUser(_group);
    _ndsGroup = _ndsGroupSvc.ensureGroup(_group.getId());
    _awsAccount = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(_awsAccount);

    _azureSubscription =
        new AzureSubscription(
            new ObjectId(),
            "local",
            new Date(),
            new Date(),
            "clientId",
            "tenantId",
            "subscriptionId",
            "secret",
            null,
            UUID.randomUUID().toString(),
            "secret",
            SupportedAzureEnvironment.AZURE,
            Collections.emptyList(),
            Collections.emptyList(),
            true,
            false,
            false,
            false,
            true);
    _azureSubscriptionDao.save(_azureSubscription);

    final NDSCloudProviderAccessAWSIAMRole role0 =
        new NDSCloudProviderAccessAWSIAMRole(
            TEST_ROLE_ID,
            TEST_IAM_ASSUMED_ROLE_ARN,
            List.of(),
            _awsAccount.getId(),
            _awsAccount.getRootARN(),
            TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(
        _ndsGroup, role0, AuditInfoHelpers.fromSystem());

    final NDSCloudProviderAccessAWSIAMRole role1 =
        new NDSCloudProviderAccessAWSIAMRole(
            TEST_ROLE_ID_2,
            TEST_IAM_ASSUMED_ROLE_ARN_2,
            List.of(),
            _awsAccount.getId(),
            _awsAccount.getRootARN(),
            TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(
        _ndsGroup, role1, AuditInfoHelpers.fromSystem());

    _appSettings.setProp(
        NDSDataLakeTenantSvc.DATA_LAKE_ASSUME_ROLE_IAM_USER_ARN_PROP,
        DATA_LAKE_ASSUME_ROLE_IAM_USER,
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        NDSDataLakeTenantSvc.DATA_LAKE_ASSUME_ROLE_ACCESS_KEY_PROP,
        DATA_LAKE_ASSUME_ROLE_ACCESS_KEY,
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        NDSDataLakeTenantSvc.DATA_LAKE_ASSUME_ROLE_ACCESS_SECRET_PROP,
        DATA_LAKE_ASSUME_ROLE_ACCESS_SECRET,
        AppSettings.SettingType.MEMORY);

    mockAWSApiSvc();
    mockAzureApiSvc();
    mockGCPApiSvc();
  }

  @AfterEach
  public void teardown() {
    _tenantDao.getCollection().deleteMany(new Document());
    eventDao.getDbCollection().remove(new BasicDBObject());
    _dataLakeTestUtils.teardown();
  }

  private void testCreateDataLakeTenant(final boolean pWithDataProcessRegion) throws Exception {
    final String tenantName = "foo";
    final NDSDataLakeTenantView.NDSDataLakeTenantViewBuilder builder =
        NDSDataLakeTenantView.builder()
            .name(tenantName)
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfigView(
                    new NDSDataLakeAWSCloudProviderConfigView(
                        TEST_IAM_ROOT_ARN,
                        TEST_IAM_ASSUMED_ROLE_ARN,
                        TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
                        new NDSDataLakeS3BucketView("bucket", AWSRegionName.US_EAST_2),
                        TEST_ROLE_ID)));

    if (pWithDataProcessRegion) {
      builder.dataProcessRegion(
          JsonOptional.of(
              new NDSDataLakeDataProcessRegionView(
                  CloudProvider.AWS, AWSRegionName.US_EAST_1.name())));
    }

    final NDSDataLakeTenantView tenantView = builder.build();

    assertEquals(0, _tenantDao.getCollection().countDocuments());

    AuditInfo auditInfo = MmsFactory.createAuditInfoFromPublicApi(_user, false, REMOTE_ADDR);

    _dataLakePublicSvc.createTenant(_group, tenantView, auditInfo);

    // ensure tenant config was saved
    assertEquals(1, _tenantDao.getCollection().countDocuments());

    final NDSDataLakeTenant savedTenant = _tenantDao.getCollection().find().first();

    // ensure UI service updates the group id
    assertEquals(_ndsGroup.getGroupId(), savedTenant.getGroupId());

    // ensure data processing region looks as expected
    if (pWithDataProcessRegion) {
      assertEquals(CloudProvider.AWS, savedTenant.getDataProcessRegion().getCloudProvider());
      assertEquals(AWSRegionName.US_EAST_1.name(), savedTenant.getDataProcessRegion().getRegion());
    } else {
      assertNull(savedTenant.getDataProcessRegion());
    }

    // ensure audit event was saved
    assertEquals(
        1,
        eventDao
            .findByEventTypeForGroup(_ndsGroup.getGroupId(), Type.FEDERATED_DATABASE_CREATED)
            .size());
  }

  @Test
  public void testCreateDataLakeTenant() throws Exception {
    testCreateDataLakeTenant(false);
  }

  @Test
  public void testCreateDataLakeTenantWithDataProcessingRegion() throws Exception {
    testCreateDataLakeTenant(true);
  }

  @Test
  public void testCreateDataLakeTenantWithDataProcessingRegionWithFeatureFlag() throws Exception {
    testCreateDataLakeTenant(true);
  }

  @Test
  public void testFindTenantsByGroupId() throws Exception {
    for (final String name : List.of("foo", "bar", "baz")) {
      _dataLakeTestUtils.saveTenant(
          NDSDataLakeTenant.builder()
              .id(new NDSDataLakeTenantId(_ndsGroup.getGroupId(), name))
              .tenantId(ObjectId.get())
              .domainLabelName(name)
              .state(NDSDataLakeState.UNVERIFIED)
              .cloudProviderConfig(
                  new NDSDataLakeCloudProviderConfig(
                      new NDSDataLakeAWSCloudProviderConfig(
                          TEST_IAM_ASSUMED_ROLE_ARN,
                          TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
                          null,
                          null)))
              .build());
    }

    // Test excluding the external ID and IAM User ARN
    final List<NDSDataLakeTenantView> tenantViews =
        _dataLakePublicSvc.findTenantsByGroup(_ndsGroup);
    tenantViews.forEach(
        tenantView -> {
          assertEquals(_ndsGroup.getGroupId(), tenantView.getGroupId());
          assertEquals(
              _awsAccount.getRootARN(),
              tenantView.getCloudProviderConfig().getAws().getIamUserARN());
          assertEquals(
              TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
              tenantView.getCloudProviderConfig().getAws().getExternalId());
        });
  }

  @Test
  public void testFindByGroupIdAndName() throws Exception {
    final NDSDataLakeTenant tenant =
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenantId(_ndsGroup.getGroupId(), "foo"))
            .state(NDSDataLakeState.UNVERIFIED)
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfig(
                    new NDSDataLakeAWSCloudProviderConfig(
                        TEST_IAM_ASSUMED_ROLE_ARN, TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID, null, null)))
            .tenantId(new ObjectId())
            .build();
    _tenantDao.saveTenant(tenant);

    final NDSDataLakeTenantView tenantView = _dataLakePublicSvc.findByGroupAndName(_group, "foo");
    assertEquals(_ndsGroup.getGroupId(), tenantView.getGroupId());
    assertEquals("foo", tenantView.getName());
    assertEquals(
        _awsAccount.getRootARN(), tenantView.getCloudProviderConfig().getAws().getIamUserARN());
    assertEquals(
        TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
        tenantView.getCloudProviderConfig().getAws().getExternalId());
    assertTrue(((NDSDataLakeStorageV1View) tenantView.getStorage()).getDatabases().isEmpty());
    assertTrue(((NDSDataLakeStorageV1View) tenantView.getStorage()).getStores().isEmpty());
  }

  @Test
  public void testFindByTenantId() throws Exception {
    final NDSDataLakeTenant tenant =
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenantId(_ndsGroup.getGroupId(), "foo"))
            .tenantId(new ObjectId())
            .state(NDSDataLakeState.UNVERIFIED)
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfig(
                    new NDSDataLakeAWSCloudProviderConfig(
                        TEST_IAM_ASSUMED_ROLE_ARN, TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID, null, null)))
            .build();
    _dataLakeTestUtils.saveTenant(tenant);
    final ObjectId tenantId = _dataLakePublicSvc.findByGroupAndName(_group, "foo").getTenantId();

    final NDSDataLakeTenantView tenantView =
        _dataLakePrivateSvc.findByTenantIdWithoutStorage(tenantId);
    assertEquals(_ndsGroup.getGroupId(), tenantView.getGroupId());
    assertEquals("foo", tenantView.getName());
    assertEquals(
        _awsAccount.getRootARN(), tenantView.getCloudProviderConfig().getAws().getIamUserARN());
    assertEquals(
        TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
        tenantView.getCloudProviderConfig().getAws().getExternalId());
  }

  @Test
  public void testUpdateAwsTenant() throws Exception {
    final String tenantName = "awsTenant";
    final NDSDataLakeTenantView.NDSDataLakeTenantViewBuilder builder =
        NDSDataLakeTenantView.builder()
            .name(tenantName)
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfigView(
                    new NDSDataLakeAWSCloudProviderConfigView(
                        TEST_IAM_ROOT_ARN,
                        TEST_IAM_ASSUMED_ROLE_ARN,
                        TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID,
                        new NDSDataLakeS3BucketView("bucket", AWSRegionName.US_EAST_2),
                        TEST_ROLE_ID)));
    _dataLakePublicSvc.createTenant(_group, builder.build(), AuditInfoHelpers.fromSystem());
    final NDSDataLakeTenantView usEast2Tenant =
        _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
    assertEquals(
        AWSRegionName.US_EAST_2.name(),
        usEast2Tenant
            .getCloudProviderConfig()
            .getAws()
            .getTestS3Bucket()
            .getRegion()
            .get()
            .getName());

    // Data process region update - Success
    {
      final NDSDataLakeDataProcessRegion dataProcessRegion =
          new NDSDataLakeDataProcessRegion(CloudProvider.AWS, AWSRegionName.US_EAST_1.getName());
      final NDSDataLakeTenantView updateDataProcessRegionView =
          NDSDataLakeTenantView.builder()
              .dataProcessRegion(
                  JsonOptional.of(new NDSDataLakeDataProcessRegionView(dataProcessRegion)))
              .build();

      _dataLakePublicSvc.updateTenant(
          _group, tenantName, updateDataProcessRegionView, AuditInfoHelpers.fromSystem());
      final NDSDataLakeTenantView tenantWithDataProcessRegion =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
      assertEquals(
          updateDataProcessRegionView.getDataProcessRegion(),
          tenantWithDataProcessRegion.getDataProcessRegion());
    }

    // Update IAM role, validation fails (all validation failure scenarios covered in the unit
    // test since we have to mock AWSApiSvc regardless) - Fail
    {
      final NDSDataLakeAWSCloudProviderConfigView awsCloudProviderConfigView =
          NDSDataLakeAWSCloudProviderConfigView.builder().roleId(new ObjectId()).build();
      final NDSDataLakeCloudProviderConfigView cloudProviderConfigView =
          NDSDataLakeCloudProviderConfigView.builder().aws(awsCloudProviderConfigView).build();
      final NDSDataLakeTenantView updateTenantView =
          NDSDataLakeTenantView.builder().cloudProviderConfig(cloudProviderConfigView).build();
      try {
        _dataLakePublicSvc.updateTenant(
            _group, tenantName, updateTenantView, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, pE.getErrorCode());
      }
    }

    // Update IAM role, validation passes - Success
    {
      final NDSDataLakeS3BucketView ndsDataLakeTestS3BucketView =
          new NDSDataLakeS3BucketView("bucket-o-json", null);
      final NDSDataLakeAWSCloudProviderConfigView awsCloudProviderConfigView =
          NDSDataLakeAWSCloudProviderConfigView.builder()
              .roleId(TEST_ROLE_ID_2)
              .iamAssumedRoleARN(TEST_IAM_ASSUMED_ROLE_ARN_2)
              .externalId(TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID)
              .testS3Bucket(ndsDataLakeTestS3BucketView)
              .build();
      final NDSDataLakeCloudProviderConfigView cloudProviderConfigView =
          NDSDataLakeCloudProviderConfigView.builder().aws(awsCloudProviderConfigView).build();
      final NDSDataLakeTenantView updateTenantView =
          NDSDataLakeTenantView.builder().cloudProviderConfig(cloudProviderConfigView).build();
      _dataLakePublicSvc.updateTenant(
          _group, tenantName, updateTenantView, AuditInfoHelpers.fromSystem());
      final NDSDataLakeTenantView tenantWithUpdatedIAMRole =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
      assertEquals(
          awsCloudProviderConfigView.getRoleId(),
          tenantWithUpdatedIAMRole.getCloudProviderConfig().getAws().getRoleId());
      assertEquals(
          awsCloudProviderConfigView.getIamAssumedRoleARN(),
          tenantWithUpdatedIAMRole.getCloudProviderConfig().getAws().getIamAssumedRoleARN());
      assertEquals(
          awsCloudProviderConfigView.getExternalId(),
          tenantWithUpdatedIAMRole.getCloudProviderConfig().getAws().getExternalId());
      assertEquals(
          ndsDataLakeTestS3BucketView.toBuilder().region(AWSRegionName.US_EAST_2).build(),
          tenantWithUpdatedIAMRole.getCloudProviderConfig().getAws().getTestS3Bucket());
      assertEquals(NDSDataLakeStateView.ACTIVE, tenantWithUpdatedIAMRole.getState());
    }

    // Update data process region on a tenant that has an IAM role - Success
    {
      final NDSDataLakeTenantView existingTenant =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);

      final NDSDataLakeDataProcessRegionView dataProcessRegionView =
          NDSDataLakeDataProcessRegionView.builder()
              .region(AWSRegionName.EU_WEST_1.getName())
              .cloudProvider(CloudProvider.AWS)
              .build();
      final NDSDataLakeTenantView updateTenantView =
          NDSDataLakeTenantView.builder()
              .dataProcessRegion(JsonOptional.of(dataProcessRegionView))
              .build();
      _dataLakePublicSvc.updateTenant(
          _group, tenantName, updateTenantView, AuditInfoHelpers.fromSystem());

      final NDSDataLakeTenantView updatedTenant =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
      assertEquals(existingTenant.getCloudProviderConfig(), updatedTenant.getCloudProviderConfig());
      assertEquals(dataProcessRegionView, updatedTenant.getDataProcessRegion().get());
    }
  }

  @Test
  public void testUpdateAzureTenant() throws Exception {
    final String tenantName = "azureTenant";

    final String tenantId = UUID.randomUUID().toString();
    final String servicePrincipalId = UUID.randomUUID().toString();
    final String atlasAppId = _azureSubscription.getMultiTenantAppId().orElse(null);

    final NDSCloudProviderAccessAzureServicePrincipalView sp1 =
        new NDSCloudProviderAccessAzureServicePrincipalView(
            null, servicePrincipalId, List.of(), atlasAppId, tenantId, new Date(), new Date());
    final ObjectId roleId1 =
        _ndsCloudProviderAccessSvc
            .addAzureServicePrincipalToCloudProviderAccess(
                _group, sp1, AuditInfoHelpers.fromSystem())
            .getId();

    final NDSCloudProviderAccessAzureServicePrincipalView sp2 =
        new NDSCloudProviderAccessAzureServicePrincipalView(
            null,
            UUID.randomUUID().toString(),
            List.of(),
            atlasAppId,
            UUID.randomUUID().toString(),
            new Date(),
            new Date());
    final ObjectId roleId2 =
        _ndsCloudProviderAccessSvc
            .addAzureServicePrincipalToCloudProviderAccess(
                _group, sp2, AuditInfoHelpers.fromSystem())
            .getId();

    final NDSDataLakeTenantView.NDSDataLakeTenantViewBuilder builder =
        NDSDataLakeTenantView.builder()
            .name(tenantName)
            .dataProcessRegion(
                JsonOptional.of(
                    NDSDataLakeDataProcessRegionView.builder()
                        .cloudProvider(CloudProvider.AZURE)
                        .build()))
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfigView(
                    new NDSDataLakeAzureCloudProviderConfigView(
                        servicePrincipalId, tenantId, atlasAppId, roleId1)));
    _dataLakePublicSvc.createTenant(_group, builder.build(), AuditInfoHelpers.fromSystem());
    final NDSDataLakeTenantView azureTenant =
        _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
    assertNotNull(azureTenant.getCloudProviderConfig().getAzure().getRoleId());
    assertEquals(tenantId, azureTenant.getCloudProviderConfig().getAzure().getTenantId());
    assertEquals(
        servicePrincipalId,
        azureTenant.getCloudProviderConfig().getAzure().getServicePrincipalId());
    assertEquals(atlasAppId, azureTenant.getCloudProviderConfig().getAzure().getAtlasAppId());

    assertNull(azureTenant.getCloudProviderConfig().getAws());

    // Data process region update - Success
    {
      final NDSDataLakeDataProcessRegion dataProcessRegion =
          new NDSDataLakeDataProcessRegion(
              CloudProvider.AZURE, AzureRegionName.US_EAST_2.getName());
      final NDSDataLakeTenantView updateDataProcessRegionView =
          NDSDataLakeTenantView.builder()
              .dataProcessRegion(
                  JsonOptional.of(new NDSDataLakeDataProcessRegionView(dataProcessRegion)))
              .build();

      _dataLakePublicSvc.updateTenant(
          _group, tenantName, updateDataProcessRegionView, AuditInfoHelpers.fromSystem());
      final NDSDataLakeTenantView tenantWithDataProcessRegion =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
      assertEquals(
          updateDataProcessRegionView.getDataProcessRegion(),
          tenantWithDataProcessRegion.getDataProcessRegion());
    }

    // Update service principal, validation fails (validation failure scenarios
    // covered in the unit test since we have to mock AzureApiSvc regardless) - Fail
    {
      final NDSDataLakeAzureCloudProviderConfigView azureCloudProviderConfigView =
          NDSDataLakeAzureCloudProviderConfigView.builder().roleId(new ObjectId()).build();
      final NDSDataLakeCloudProviderConfigView cloudProviderConfigView =
          NDSDataLakeCloudProviderConfigView.builder().azure(azureCloudProviderConfigView).build();
      final NDSDataLakeTenantView updateTenantView =
          NDSDataLakeTenantView.builder().cloudProviderConfig(cloudProviderConfigView).build();
      try {
        _dataLakePublicSvc.updateTenant(
            _group, tenantName, updateTenantView, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(
            NDSErrorCode.CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_NOT_FOUND,
            pE.getErrorCode());
      }
    }

    // Update Service Principal, validation passes - Success
    {
      final NDSDataLakeAzureCloudProviderConfigView azureCloudProviderConfigView =
          NDSDataLakeAzureCloudProviderConfigView.builder().roleId(roleId2).build();
      final NDSDataLakeCloudProviderConfigView cloudProviderConfigView =
          NDSDataLakeCloudProviderConfigView.builder().azure(azureCloudProviderConfigView).build();
      final NDSDataLakeTenantView updateTenantView =
          NDSDataLakeTenantView.builder().cloudProviderConfig(cloudProviderConfigView).build();
      _dataLakePublicSvc.updateTenant(
          _group, tenantName, updateTenantView, AuditInfoHelpers.fromSystem());
      final NDSDataLakeTenantView tenantWithUpdatedServicePrincipal =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
      assertEquals(
          azureCloudProviderConfigView.getRoleId(),
          tenantWithUpdatedServicePrincipal.getCloudProviderConfig().getAzure().getRoleId());
      assertEquals(
          sp2.getTenantId(),
          tenantWithUpdatedServicePrincipal.getCloudProviderConfig().getAzure().getTenantId());
      assertEquals(
          sp2.getServicePrincipalId(),
          tenantWithUpdatedServicePrincipal
              .getCloudProviderConfig()
              .getAzure()
              .getServicePrincipalId());
      assertEquals(
          atlasAppId,
          tenantWithUpdatedServicePrincipal.getCloudProviderConfig().getAzure().getAtlasAppId());
      assertEquals(NDSDataLakeStateView.ACTIVE, tenantWithUpdatedServicePrincipal.getState());
    }

    // Update data process region on a tenant that has a service principal - Success
    {
      final NDSDataLakeTenantView existingTenant =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);

      final NDSDataLakeDataProcessRegionView dataProcessRegionView =
          NDSDataLakeDataProcessRegionView.builder()
              .region(AzureRegionName.US_EAST_2.getName())
              .cloudProvider(CloudProvider.AZURE)
              .build();
      final NDSDataLakeTenantView updateTenantView =
          NDSDataLakeTenantView.builder()
              .dataProcessRegion(JsonOptional.of(dataProcessRegionView))
              .build();
      _dataLakePublicSvc.updateTenant(
          _group, tenantName, updateTenantView, AuditInfoHelpers.fromSystem());

      final NDSDataLakeTenantView updatedTenant =
          _dataLakePublicSvc.findByGroupAndName(_group, tenantName);
      assertEquals(existingTenant.getCloudProviderConfig(), updatedTenant.getCloudProviderConfig());
      assertEquals(dataProcessRegionView, updatedTenant.getDataProcessRegion().get());
    }
  }

  @Test
  public void testGetDataLakeMetrics() throws SvcException {
    // create tenant
    final String tenantName = "elevennants";
    final ObjectId tenantId = ObjectId.get();
    final ObjectId groupId = _ndsGroup.getGroupId();

    // add dummy metrics
    _dataLakeTestUtils.setTenantMetrics(
        tenantId.toHexString(),
        NDSDataLakeMetricsView.builder()
            .avgExecutionTime(100.0)
            .totalSuccessfulQueries(7L)
            .totalFailedQueries(3L)
            .totalDataScanned(7L)
            .totalDataReturned(15L)
            .build());

    _dataLakeTestUtils.saveTenant(
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenantId(groupId, tenantName))
            .tenantId(tenantId)
            .domainLabelName(tenantName)
            .state(NDSDataLakeState.UNVERIFIED)
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfig(
                    new NDSDataLakeAWSCloudProviderConfig(
                        TEST_IAM_ASSUMED_ROLE_ARN, TEST_IAM_ASSUMED_ROLE_EXTERNAL_ID, null, null)))
            .build());

    final NDSDataLakeMetricsView metrics =
        _dataLakePublicSvc.getMetricsByGroupIdAndNameForCurrentBillingCycle(groupId, tenantName);

    assertEquals(7L, (long) metrics.getTotalSuccessfulQueries());
    assertEquals(3L, (long) metrics.getTotalFailedQueries());
    assertEquals(7L, (long) metrics.getTotalDataScanned());
    assertEquals(15L, (long) metrics.getTotalDataReturned());
    assertEquals(100.0, metrics.getAvgExecutionTime(), 0.01);
  }

  @Test
  public void testGetDataLakeMetricsNotFound() {
    final String tenantName = "elevennants";
    final ObjectId groupId = _ndsGroup.getGroupId();
    try {
      _dataLakePublicSvc.getMetricsByGroupIdAndNameForCurrentBillingCycle(groupId, tenantName);
      fail("Expected not found error did not occur");
    } catch (final SvcException e) {
      assertEquals(e.getErrorCode(), NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME);
    }
  }

  @Test
  public void testSetClientConfigConfirmed() throws Exception {
    final String name = "DR-FUNKENSTEIN";

    _dataLakePublicSvc.createTenant(
        _group,
        buildTenantView().toBuilder()
            .name(name)
            .state(NDSDataLakeStateView.ACTIVE)
            .clientConfigConfirmed(false)
            .build(),
        AuditInfoHelpers.fromSystem());

    final NDSDataLakeTenantView tenantView = _dataLakePublicSvc.findByGroupAndName(_group, name);
    assertNotNull(tenantView.getClientConfigConfirmed());
    assertFalse(tenantView.getClientConfigConfirmed());

    final NDSDataLakeTenantView updatedTenantView =
        _dataLakePublicSvc.setClientConfigConfirmed(tenantView);
    assertNotNull(tenantView.getClientConfigConfirmed());
    assertTrue(updatedTenantView.getClientConfigConfirmed());

    final NDSDataLakeTenantView updatedTenantViewFromDB =
        _dataLakePrivateSvc.findByTenantIdWithoutStorage(updatedTenantView.getTenantId());
    assertNotNull(updatedTenantViewFromDB.getClientConfigConfirmed());
    assertTrue(updatedTenantViewFromDB.getClientConfigConfirmed());
  }

  @Test
  public void testDeleteByGroupIdAndName() throws SvcException {
    final Date now = Date.from(Instant.now());
    final ObjectId tenantId = new ObjectId();
    final String dataLakeName = "LakeGeorge";
    final ObjectId roleId = new ObjectId();

    final NDSDataLakeTenant tenant =
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenantId(_ndsGroup.getGroupId(), dataLakeName))
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfig(
                    new NDSDataLakeAWSCloudProviderConfig("roleARN", "externalID", null, roleId)))
            .state(NDSDataLakeState.ACTIVE)
            .tenantId(tenantId)
            .createdDate(now)
            .lastUpdatedDate(now)
            .build();
    _dataLakeTestUtils.saveTenant(tenant);

    // Delete Tenant with feature usage and Role in Cloud Provider Access - Ensure feature usage is
    // removed
    final NDSCloudProviderAccessDataLakeFeatureUsage featureUsage =
        new NDSCloudProviderAccessDataLakeFeatureUsage(
            new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                tenant.getGroupId(), tenant.getName()));
    final NDSCloudProviderAccessAWSIAMRole role =
        new NDSCloudProviderAccessAWSIAMRole(
            roleId,
            "assumedRoleARN",
            List.of(featureUsage),
            new ObjectId(),
            "accountARN",
            "externalId",
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(
        _ndsGroup, role, AuditInfoHelpers.fromSystem());
    _dataLakePublicSvc.deleteByGroupAndName(_group, dataLakeName, AuditInfoHelpers.fromSystem());

    final NDSGroup ndsGroupWithUpdatedCPA = _ndsGroupSvc.find(_ndsGroup.getGroupId()).orElseThrow();
    final NDSCloudProviderAccessAWSIAMRole awsIamRole =
        ndsGroupWithUpdatedCPA
            .getCloudProviderAccess()
            .getNDSCloudProviderAccessAWSIAMRoleById(roleId)
            .orElseThrow();
    assertEquals(List.of(), awsIamRole.getFeatureUsages());

    final NDSDataLakeTenant updatedTenant = _tenantDao.findByTenantId(tenantId).orElseThrow();
    assertEquals(NDSDataLakeState.DELETED, updatedTenant.getState());
  }

  private NDSDataLakeTenantView buildTenantView() {
    return NDSDataLakeTenantView.builder()
        .state(NDSDataLakeStateView.ACTIVE)
        .cloudProviderConfig(
            new NDSDataLakeCloudProviderConfigView(
                NDSDataLakeAWSCloudProviderConfigView.builder()
                    .roleId(TEST_ROLE_ID)
                    .testS3Bucket(
                        new NDSDataLakeS3BucketView("dont-kick-me", AWSRegionName.AP_NORTHEAST_1))
                    .build()))
        .build();
  }

  private AWSApiSvc mockAWSApiSvc() throws IllegalAccessException, NoSuchFieldException {
    final AWSApiSvc apiSvc = mock(AWSApiSvc.class);
    final Credentials tempCredentials = mock(Credentials.class);
    doReturn("THEK3Y2SUCCEZZIZZZM333:)").when(tempCredentials).getAccessKeyId();
    doReturn("SHhIAmAS3CR3tDONTT3LLANY1;-)").when(tempCredentials).getSecretAccessKey();
    doReturn("SEzz10nT0kK3n!!").when(tempCredentials).getSessionToken();
    doReturn(tempCredentials)
        .when(apiSvc)
        .assumeRole(
            any(AWSCredentialsProvider.class), any(), isNotNull(), any(), any(), any(), any());
    doReturn(tempCredentials)
        .when(apiSvc)
        .assumeRole(
            any(ObjectId.class), any(), any(), any(), isNotNull(), any(), any(), any(), any());
    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(apiSvc)
        .assumeRole(any(AWSCredentialsProvider.class), any(), isNull(), any(), any(), any(), any());
    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(apiSvc)
        .assumeRole(any(ObjectId.class), any(), any(), isNull(), any(), any(), any(), any(), any());

    doReturn(List.of(mock(S3ObjectSummary.class)))
        .when(apiSvc)
        .listObjects(any(), any(), any(), anyBoolean(), any());
    doReturn(mock(ObjectMetadata.class))
        .when(apiSvc)
        .getObjectMetadata((AWSCredentials) any(), any(), any(), anyBoolean(), any());
    doReturn(Optional.of(AWSRegionName.US_EAST_2))
        .when(apiSvc)
        .getS3BucketRegion(any(), anyString(), anyBoolean(), any());

    ClassModifier.modifyInstanceValue(_ndsCloudProviderAccessSvc, "_awsApiSvc", apiSvc);
    ClassModifier.modifyInstanceValue(_dataLakePrivateSvc, "_awsApiSvc", apiSvc);
    ClassModifier.modifyInstanceValue(_dataLakePublicSvc, "_awsApiSvc", apiSvc);

    return apiSvc;
  }

  private AzureApiSvc mockAzureApiSvc() throws NoSuchFieldException, IllegalAccessException {
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);

    final NDSAzureTempCredentialsView tempCredentialsView =
        new NDSAzureTempCredentialsView(
            "https://testaccount.blob.core.windows.net/testcontainer", "sasToken", new Date());
    doReturn(tempCredentialsView)
        .when(azureApiSvc)
        .generateStorageContainerSasTokenForCloudProviderAccess(any(), any(), any(), any(), any());
    doReturn(List.of())
        .when(azureApiSvc)
        .listBlobs(eq(tempCredentialsView), eq("testcontainer"), eq(""), any());
    doNothing()
        .when(azureApiSvc)
        .authenticateServicePrincipalForCloudProviderAccess(any(), any(), any());

    ClassModifier.modifyInstanceValue(_ndsCloudProviderAccessSvc, "_azureApiSvc", azureApiSvc);
    ClassModifier.modifyInstanceValue(_dataLakePrivateSvc, "_azureApiSvc", azureApiSvc);
    ClassModifier.modifyInstanceValue(_dataLakePublicSvc, "_azureApiSvc", azureApiSvc);

    return azureApiSvc;
  }

  private GCPApiSvc mockGCPApiSvc() throws NoSuchFieldException, IllegalAccessException {
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);

    _gcpAccessTokenExpiration = new Date();
    _gcpAccessTokenValue = UUID.randomUUID().toString();

    final AccessToken accessToken =
        new AccessToken(_gcpAccessTokenValue, _gcpAccessTokenExpiration);
    doReturn(accessToken).when(gcpApiSvc).getAccessTokenForCloudStorageAccess(any(), any());

    ClassModifier.modifyInstanceValue(_ndsCloudProviderAccessSvc, "_gcpApiSvc", gcpApiSvc);
    ClassModifier.modifyInstanceValue(_dataLakePrivateSvc, "_gcpApiSvc", gcpApiSvc);

    return gcpApiSvc;
  }

  @Test
  public void testSetUsageLimit() throws SvcException {
    // project level usage limit
    {
      final DataFederationUsageLimitView dataFederationUsageLimitView =
          LimitTestFactory.getProjectUsageLimitView();
      final DataFederationUsageLimitView currentUsageLimitView =
          dataFederationUsageLimitView.toBuilder()
              .lastModifiedDate(new Date())
              .limitInBytes((long) Units.convert(101.0, Units.GIGABYTES, Units.BYTES))
              .build();
      _dataLakeTestUtils.setUsageLimit(
          dataFederationUsageLimitView
              .toUpdatedUsageLimitView(currentUsageLimitView)
              .toUsageLimit(null));
      final List<DataFederationUsageLimitView> dataFederationUsageLimitViews =
          _dataLakePublicSvc.getUsageLimits(dataFederationUsageLimitView.getGroupId());

      assertEquals(dataFederationUsageLimitViews.size(), 1);
      final DataFederationUsageLimitView responseDataFederationUsageLimitView =
          dataFederationUsageLimitViews.get(0);
      assertEquals(
          responseDataFederationUsageLimitView.getGroupId(),
          dataFederationUsageLimitView.getGroupId());
      assertEquals(
          responseDataFederationUsageLimitView.getTenantName(),
          dataFederationUsageLimitView.getTenantName());
      assertEquals(
          responseDataFederationUsageLimitView.getLimitInBytes(),
          dataFederationUsageLimitView.getLimitInBytes());
      assertEquals(
          responseDataFederationUsageLimitView.getLimitSpan(),
          dataFederationUsageLimitView.getLimitSpan());
      assertEquals(
          responseDataFederationUsageLimitView.getOverrunPolicy(),
          dataFederationUsageLimitView.getOverrunPolicy());
      assertNotEquals(
          responseDataFederationUsageLimitView.getLastModifiedDate(),
          dataFederationUsageLimitView.getLastModifiedDate());
    }
  }

  @Test
  public void testGetGCPTempCredentials() throws SvcException {
    // create tenant
    final String tenantName = "elevennants";
    final ObjectId tenantId = ObjectId.get();
    final ObjectId groupId = _ndsGroup.getGroupId();

    final String gcpServiceAccount = "<EMAIL>";
    final ObjectId roleId = ObjectId.get();

    _dataLakeTestUtils.saveTenant(
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenantId(groupId, tenantName))
            .tenantId(tenantId)
            .domainLabelName(tenantName)
            .state(NDSDataLakeState.ACTIVE)
            .cloudProviderConfig(
                new NDSDataLakeCloudProviderConfig(
                    new NDSDataLakeGCPCloudProviderConfig(gcpServiceAccount, roleId)))
            .build());

    // This result relies on mocks made in in mockGCPApiSvc().
    var result =
        _dataLakePrivateSvc.getGCPTempCredentials(
            tenantId, new ApiPrivateAtlasGCPBucketAccessRequestView(false));
    assertInstanceOf(NDSGCPTempCredentialsView.class, result);

    var gcpResult = (NDSGCPTempCredentialsView) result;
    assertEquals(_gcpAccessTokenValue, gcpResult.getTokenValue());
    assertEquals(_gcpAccessTokenExpiration, gcpResult.getExpirationDate());
  }
}
