package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.Test;

public class ProvisionServerlessNATGatewayBeforeEnvoyIntTests extends DependencyRuleBaseTests {

  @Test
  public void testApply() {
    final ObjectId containerId = new ObjectId();
    final ObjectId deploymentId = new ObjectId();

    // No dependencies when the sole move
    {
      final PlannedAction provisionEnvoy =
          _actionFactory.forCreateServerlessEnvoyInstance(
              new ObjectId(), containerId, new ObjectId(), CloudProvider.GCP);

      new ProvisionServerlessNATGatewayBeforeEnvoy().apply(List.of(provisionEnvoy));

      assertEquals(Set.of(), provisionEnvoy.getPredecessors());
    }

    // provision depends on both
    {
      final PlannedAction provisionNATGateway =
          _actionFactory.forCreateServerlessNATGateway(
              containerId, CloudProvider.GCP, deploymentId);
      final PlannedAction provisionEnvoy =
          _actionFactory.forCreateServerlessEnvoyInstance(
              new ObjectId(), containerId, deploymentId, CloudProvider.GCP);

      new ProvisionServerlessNATGatewayBeforeEnvoy()
          .apply(List.of(provisionNATGateway, provisionEnvoy));

      assertEquals(
          Set.of(provisionNATGateway.getLastMove().getId()), provisionEnvoy.getPredecessors());
    }
  }
}
