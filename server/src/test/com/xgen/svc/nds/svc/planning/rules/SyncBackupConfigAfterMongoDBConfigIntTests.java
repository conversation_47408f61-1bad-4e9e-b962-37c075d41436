package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Test;

public class SyncBackupConfigAfterMongoDBConfigIntTests extends DependencyRuleBaseTests {

  @Test
  public void testApplyNoMongoDBConfig() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);

    final PlannedAction backupAction = _actionFactory.forSyncBackupConfig(clusterDescription);
    new SyncBackupConfigAfterMongoDBConfig().apply(Collections.singletonList(backupAction));

    assertTrue(backupAction.getPredecessors().isEmpty());
    assertTrue(backupAction.getSuccessors().isEmpty());
  }

  @Test
  public void testApplyWithMongoDBConfig() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);

    final PlannedAction backupAction = _actionFactory.forSyncBackupConfig(clusterDescription);
    final PlannedAction automationConfigAction =
        _actionFactory.forSyncMongoDBConfigForCluster(
            clusterDescription, Collections.emptyList(), false, false, false);

    final List<PlannedAction> actions = new ArrayList<>();
    actions.add(backupAction);
    actions.add(automationConfigAction);

    new SyncBackupConfigAfterMongoDBConfig().apply(actions);
    assertEquals(1, backupAction.getPredecessors().size());
    assertTrue(
        backupAction.getPredecessors().contains(automationConfigAction.getMoves()[0].getId()));
    assertTrue(automationConfigAction.getPredecessors().isEmpty());
  }
}
