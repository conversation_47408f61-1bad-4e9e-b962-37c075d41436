package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.List;
import org.junit.Test;

public class TransitionConfigServerBeforeSyncClusterPrivateEndpointIntTests
    extends DependencyRuleBaseTests {

  @Test
  public void apply() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final PlannedAction transitionConfigServerAction =
        _actionFactory.forTransitionConfigServerMove(
            clusterDescription, ConfigServerType.DEDICATED);
    final PlannedAction syncAction1 =
        _actionFactory.forSyncClusterPrivateEndpointConnection(
            clusterDescription, clusterDescription.getOnlyCloudProvider().orElseThrow());
    final PlannedAction syncAction2 =
        _actionFactory.forSyncClusterPrivateEndpointConnection(
            clusterDescription, CloudProvider.GCP);

    final List<PlannedAction> actions =
        List.of(transitionConfigServerAction, syncAction1, syncAction2);

    new TransitionConfigServerBeforeSyncClusterPrivateEndpoint().apply(actions);

    assertTrue(
        syncAction1
            .getPredecessors()
            .contains(transitionConfigServerAction.getFirstMove().getId()));
    assertTrue(
        syncAction2
            .getPredecessors()
            .contains(transitionConfigServerAction.getFirstMove().getId()));
    assertTrue(syncAction1.getSuccessors().isEmpty());
    assertTrue(syncAction2.getSuccessors().isEmpty());
    assertTrue(transitionConfigServerAction.getPredecessors().isEmpty());
    assertTrue(
        transitionConfigServerAction.getSuccessors().contains(syncAction1.getFirstMove().getId()));
    assertTrue(
        transitionConfigServerAction.getSuccessors().contains(syncAction2.getFirstMove().getId()));
  }
}
