package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.PushBasedLogExportBuilder;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.State;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessPushBasedLogExportFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.featureid.NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class PushBasedLogExportSvcIntTests extends JUnit5NDSBaseTest {
  private AppUser _groupOwner;
  private Group _group;
  private ObjectId _groupId;
  private Organization _organization;
  private UserApiKey _adminApiKey;
  private PushBasedLogExportSvc _pushBasedLogExportSvc;
  private AuditInfo _auditInfo;
  private NDSCloudProviderAccessSvc _spyNdsCloudProviderAccessSvc;

  @Inject private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private SegmentEventSvc _segmentEventSvc;
  @Inject private AppSettings _appSettings;
  @Inject private GroupSvc _groupSvc;
  @Inject private TemporaryCredentialSvc _temporaryCredentialSvc;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization);
    _groupId = _group.getId();
    _groupOwner =
        MmsFactory.createUser(_group, "<EMAIL>", Set.of(Role.GROUP_OWNER));
    _adminApiKey = MmsFactory.generateUserApiKey(_groupOwner.getId(), "api key");
    _ndsGroupSvc.create(_groupId, new NDSManagedX509(), false);
    _spyNdsCloudProviderAccessSvc = spy(_ndsCloudProviderAccessSvc);
    mockAwsApiSvc();
    mockAwsIamRoles();

    _pushBasedLogExportSvc =
        spy(
            new PushBasedLogExportSvc(
                _spyNdsCloudProviderAccessSvc,
                _ndsGroupSvc,
                _segmentEventSvc,
                _appSettings,
                _groupSvc,
                _temporaryCredentialSvc));
    _auditInfo = AuditInfoHelpers.fromSystem();
  }

  @Test
  public void testCreatePushBasedLogConfigurationActive() throws SvcException {
    doNothing()
        .when(_pushBasedLogExportSvc)
        .validateBucketAndIamRole(any(), anyString(), anyString(), any());
    _pushBasedLogExportSvc.createPushBasedLogConfiguration(
        _auditInfo, _group.getId(), "testBucketName", "testPrefixPath/", new ObjectId(0, 0));

    // Valid request from user with permissions results in updates to DB
    final PushBasedLogExport config = _ndsGroupSvc.getPushBasedLogExport(_groupId);
    assertEquals(State.ACTIVE, config.getState());
    assertEquals("testBucketName", config.getBucketName());
    assertEquals("testPrefixPath", config.getPrefixPath());
    assertEquals(new ObjectId(0, 0), config.getIamRoleId());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _groupId, "testBucketName"))),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());

    assertTrue(_ndsGroupSvc.find(_groupId).get().getPlanASAP());
  }

  @Test
  public void testCreatePushBasedLogConfigurationNoGroup() throws SvcException {
    assertThrows(
        SvcException.class,
        () ->
            _pushBasedLogExportSvc.createPushBasedLogConfiguration(
                _auditInfo,
                MmsFactory.createGroup(_organization, "DeadGroup").getId(),
                "testBucketName",
                "testPrefixPath",
                new ObjectId(0, 0)));
    verify(_pushBasedLogExportSvc, times(0))
        .writeConfig(any(), any(), any(), any(), any(), any(), any());
  }

  @Test
  public void testCreatePushBasedLogConfiguration_invalidSessionDuration() throws SvcException {
    mockAwsApiSvc(CommonErrorCode.INVALID_PARAMETER);
    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () ->
                _pushBasedLogExportSvc.createPushBasedLogConfiguration(
                    _auditInfo, _groupId, "testBucketName", "testPrefixPath", new ObjectId(0, 0)));
    assertEquals(NDSErrorCode.IAM_ROLE_INVALID_SESSION_DURATION, thrown.getErrorCode());
    verify(_pushBasedLogExportSvc).updateLogExportState(eq(_groupId), eq(State.ASSUME_ROLE_FAILED));
    verify(_pushBasedLogExportSvc, never())
        .writeConfig(any(), any(), any(), any(), any(), any(), any());
  }

  @Test
  public void testGetPushBasedLogExportConfiguration() throws SvcException {
    final PushBasedLogExport initialConfig =
        _pushBasedLogExportSvc.getPushBasedLogConfiguration(_groupId);

    // Correctly fetches default config (empty on creation)
    assertEquals(new PushBasedLogExport(), initialConfig);

    // Correctly fetches updated PushBasedLogExport configuration
    final PushBasedLogExport customConfig =
        new PushBasedLogExport.PushBasedLogExportBuilder()
            .bucketName("testBucketName")
            .iamRoleId(new ObjectId(0, 0))
            .state(State.ACTIVE)
            .prefixPath("prefix/path")
            .lastUpdateDate(new Date())
            .exportInterval(300)
            .createDate(new Date())
            .needsChefConfigUpdateAfter(new Date())
            .tempCredentials(NDSModelTestFactory.getPBLETempCredentialsForTest())
            .enabledMongot(false)
            .enabledMongotLastUpdateDate(new Date())
            .build();
    final boolean modified =
        _ndsGroupSvc.setPushBasedLogExportFields(_groupId, customConfig, _auditInfo);
    assertTrue(modified);
    final PushBasedLogExport modifiedConfig =
        _pushBasedLogExportSvc.getPushBasedLogConfiguration(_groupId);
    assertEquals(customConfig, modifiedConfig);
  }

  @Test
  public void testUpdatePushBasedLogExportSkipsErrorCheck() throws SvcException {
    final PushBasedLogExport config =
        new PushBasedLogExportBuilder()
            .bucketName("testBucketName")
            .prefixPath("testPrefixPath")
            .iamRoleId(new ObjectId(0, 0))
            .build();
    {
      doThrow(SvcException.class).when(_pushBasedLogExportSvc).getRoleArn(any(), any());
      assertThrows(
          SvcException.class,
          () ->
              _pushBasedLogExportSvc.createPushBasedLogConfiguration(
                  _auditInfo,
                  _groupId,
                  config.getBucketName(),
                  config.getPrefixPath(),
                  config.getIamRoleId()));
    }
    {
      doReturn("arn:aws:iam::************:role/buttered-role-arn-1")
          .when(_pushBasedLogExportSvc)
          .getRoleArn(any(), any());
      _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
          _auditInfo,
          _groupId,
          config.getBucketName(),
          config.getPrefixPath(),
          config.getIamRoleId());
      final PushBasedLogExport configFromFailed = _ndsGroupSvc.getPushBasedLogExport(_groupId);
      assertEquals(State.ACTIVE, configFromFailed.getState());
      assertEquals(config.getBucketName(), configFromFailed.getBucketName());
      assertEquals(config.getPrefixPath(), configFromFailed.getPrefixPath());
      assertEquals(config.getIamRoleId(), configFromFailed.getIamRoleId());
      assertEquals(
          List.of(
              new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                  new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                      _groupId, config.getBucketName()))),
          _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
              .filter(role -> role.getRoleId().equals(config.getIamRoleId()))
              .findFirst()
              .get()
              .getFeatureUsages());
    }
  }

  @Test
  public void testUpdatePushBasedLogExportConfiguration() throws SvcException {
    doNothing()
        .when(_pushBasedLogExportSvc)
        .validateBucketAndIamRole(any(), anyString(), anyString(), any());

    // Cannot update feature that is in an UNCONFIGURED state
    final SvcException notEnabledException =
        assertThrows(
            SvcException.class,
            () ->
                _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
                    _auditInfo, _groupId, null, null, null));
    assertEquals(
        NDSErrorCode.PUSH_BASED_LOG_EXPORT_NOT_ENABLED, notEnabledException.getErrorCode());

    _pushBasedLogExportSvc.createPushBasedLogConfiguration(
        _auditInfo, _groupId, "testBucketName", "testPrefixPath", new ObjectId(0, 0));
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _groupId, "testBucketName"))),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());

    // Update is identical to existing configuration -- no changes made
    final SvcException identicalConfiguration1 =
        assertThrows(
            SvcException.class,
            () ->
                _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
                    _auditInfo, _groupId, null, null, null));
    assertEquals(
        NDSErrorCode.PUSH_BASED_LOG_EXPORT_UPDATE_IDENTICAL_TO_EXISTING_CONFIG,
        identicalConfiguration1.getErrorCode());
    final SvcException identicalConfiguration2 =
        assertThrows(
            SvcException.class,
            () ->
                _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
                    _auditInfo, _groupId, "testBucketName", "testPrefixPath", new ObjectId(0, 0)));
    assertEquals(
        NDSErrorCode.PUSH_BASED_LOG_EXPORT_UPDATE_IDENTICAL_TO_EXISTING_CONFIG,
        identicalConfiguration2.getErrorCode());
    final SvcException identicalConfiguration3 =
        assertThrows(
            SvcException.class,
            () ->
                _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
                    _auditInfo, _groupId, "testBucketName", "testPrefixPath/", new ObjectId(0, 0)));
    assertEquals(
        NDSErrorCode.PUSH_BASED_LOG_EXPORT_UPDATE_IDENTICAL_TO_EXISTING_CONFIG,
        identicalConfiguration3.getErrorCode());

    // Full update
    _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
        _auditInfo, _groupId, "updatedBucketName", "updatedPrefixPath", new ObjectId(1, 1));
    final PushBasedLogExport config = _ndsGroupSvc.getPushBasedLogExport(_groupId);
    assertEquals(State.ACTIVE, config.getState());
    assertEquals("updatedBucketName", config.getBucketName());
    assertEquals("updatedPrefixPath", config.getPrefixPath());
    assertEquals(new ObjectId(1, 1), config.getIamRoleId());
    assertEquals(
        Collections.emptyList(),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _groupId, "updatedBucketName"))),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(1, 1)))
            .findFirst()
            .get()
            .getFeatureUsages());

    // Partial update
    _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
        _auditInfo, _groupId, null, null, new ObjectId(0, 0));
    final PushBasedLogExport config2 = _ndsGroupSvc.getPushBasedLogExport(_groupId);
    assertEquals(State.ACTIVE, config2.getState());
    assertEquals("updatedBucketName", config2.getBucketName());
    assertEquals("updatedPrefixPath", config2.getPrefixPath());
    assertEquals(new ObjectId(0, 0), config2.getIamRoleId());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _groupId, "updatedBucketName"))),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertEquals(
        Collections.emptyList(),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(1, 1)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertTrue(_ndsGroupSvc.find(_groupId).get().getPlanASAP());
  }

  @Test
  public void testUpdatePushBasedLogConfiguration_invalidSessionDuration() throws SvcException {
    mockAwsApiSvc(CommonErrorCode.INVALID_PARAMETER);
    _pushBasedLogExportSvc.savePushBasedLogExportDocument(
        _groupId, "bucketName", "prefix/path", new ObjectId(), _auditInfo);

    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () ->
                _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
                    _auditInfo, _groupId, "testBucketName", "testPrefixPath", new ObjectId(0, 0)));
    assertEquals(NDSErrorCode.IAM_ROLE_INVALID_SESSION_DURATION, thrown.getErrorCode());
    verify(_pushBasedLogExportSvc, never())
        .updateLogExportState(eq(_groupId), eq(State.ASSUME_ROLE_FAILED));
    verify(_pushBasedLogExportSvc, never())
        .writeConfig(any(), any(), any(), any(), any(), any(), any());
  }

  @Test
  public void testDeletePushBasedLogConfiguration() throws SvcException {
    assertThrows(
        SvcException.class,
        () -> _pushBasedLogExportSvc.deletePushBasedLogConfiguration(_auditInfo, new ObjectId()));
    assertThrows(
        SvcException.class,
        () -> _pushBasedLogExportSvc.deletePushBasedLogConfiguration(_auditInfo, _group.getId()));

    doNothing()
        .when(_pushBasedLogExportSvc)
        .validateBucketAndIamRole(any(), anyString(), anyString(), any());
    _pushBasedLogExportSvc.createPushBasedLogConfiguration(
        _auditInfo, _group.getId(), "testBucketName", "testPrefixPath", new ObjectId(0, 0));

    final PushBasedLogExport preDeletion = _ndsGroupSvc.getPushBasedLogExport(_groupId);
    assertEquals(State.ACTIVE, preDeletion.getState());
    assertEquals("testBucketName", preDeletion.getBucketName());
    assertEquals("testPrefixPath", preDeletion.getPrefixPath());
    assertEquals(new ObjectId(0, 0), preDeletion.getIamRoleId());

    _pushBasedLogExportSvc.deletePushBasedLogConfiguration(_auditInfo, _group.getId());
    final PushBasedLogExport postDeletion = _ndsGroupSvc.getPushBasedLogExport(_groupId);

    assertEquals(
        PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS, postDeletion.getExportInterval());
    assertEquals(State.UNCONFIGURED, postDeletion.getState());
    assertNull(postDeletion.getBucketName());
    assertNull(postDeletion.getBucketRegion());
    assertNull(postDeletion.getPrefixPath());
    assertNull(postDeletion.getIamRoleId());
    assertNull(postDeletion.getCreateDate());
    assertNotNull(postDeletion.getLastUpdateDate());

    assertEquals(
        Collections.emptyList(),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertTrue(_ndsGroupSvc.find(_groupId).get().getPlanASAP());
  }

  @Test
  public void testWriteConfig() throws SvcException {
    doNothing()
        .when(_pushBasedLogExportSvc)
        .validateBucketAndIamRole(any(), anyString(), anyString(), any());
    _pushBasedLogExportSvc.createPushBasedLogConfiguration(
        _auditInfo, _groupId, "activeBucketName", "activePrefixPath", new ObjectId(0, 0));
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _groupId, "activeBucketName"))),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertEquals(
        Collections.emptyList(),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(1, 1)))
            .findFirst()
            .get()
            .getFeatureUsages());

    final NDSAWSTempCredentials pbleTempCredentialsForTest =
        NDSModelTestFactory.getPBLETempCredentialsForTest();

    _pushBasedLogExportSvc.writeConfig(
        _auditInfo,
        _groupId,
        "newBucketName",
        "us-east-1",
        "newPrefixPath",
        new ObjectId(1, 1),
        pbleTempCredentialsForTest);
    final PushBasedLogExport updatedConfig = _ndsGroupSvc.getPushBasedLogExport(_groupId);
    assertEquals(State.ACTIVE, updatedConfig.getState());
    assertEquals("newBucketName", updatedConfig.getBucketName());
    assertEquals("newPrefixPath", updatedConfig.getPrefixPath());
    assertEquals(new ObjectId(1, 1), updatedConfig.getIamRoleId());
    assertEquals(
        Collections.emptyList(),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _groupId, "newBucketName"))),
        _ndsGroupSvc.find(_groupId).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(1, 1)))
            .findFirst()
            .get()
            .getFeatureUsages());
    assertEquals(pbleTempCredentialsForTest, updatedConfig.getTempCredentials().get());
  }

  private AWSApiSvc mockAwsApiSvc() {
    try {
      return mockAwsApiSvc(null);
    } catch (Exception e) {
      throw new IllegalStateException("Exception during mocking AWS Api Service");
    }
  }

  private AWSApiSvc mockAwsApiSvc(final CommonErrorCode pErrorCode) {
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);

    doReturn(List.of())
        .when(awsApiSvc)
        .listObjects(any(), anyString(), anyString(), anyInt(), anyBoolean(), any());
    doNothing()
        .when(awsApiSvc)
        .uploadObjectToS3(
            any(AWSCredentials.class),
            anyString(),
            anyString(),
            any(),
            any(),
            any(),
            anyBoolean(),
            any());
    doReturn(Optional.of(AWSRegionName.US_EAST_1))
        .when(awsApiSvc)
        .getS3BucketRegion(any(), anyString(), anyBoolean(), any());

    final Credentials credentials = mock(Credentials.class);
    doReturn("accessKeyId").when(credentials).getAccessKeyId();
    doReturn("secretAccessKey").when(credentials).getSecretAccessKey();
    doReturn("sessionToken").when(credentials).getSessionToken();
    doReturn(new Date()).when(credentials).getExpiration();

    if (pErrorCode == null) {
      doReturn(credentials)
          .when(awsApiSvc)
          .assumeRole(any(ObjectId.class), any(), any(), any(), any(), any(), any(), any(), any());
    } else if (pErrorCode == CommonErrorCode.INVALID_PARAMETER) {
      doThrow(
              new AWSApiException(
                  "requested DurationSeconds exceeds the MaxSessionDuration",
                  CommonErrorCode.INVALID_PARAMETER,
                  "ValidationError"))
          .when(awsApiSvc)
          .assumeRole(
              any(ObjectId.class),
              any(),
              anyString(),
              anyString(),
              anyString(),
              any(),
              any(),
              any(),
              any());
    }

    doReturn(awsApiSvc).when(_spyNdsCloudProviderAccessSvc).getAWSApiSvc();
    return awsApiSvc;
  }

  private void mockAwsIamRoles() {
    final NDSCloudProviderAccessAWSIAMRole role1 =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(new ObjectId(0, 0))
            .iamAssumedRoleArn("arn:aws:iam::************:role/buttered-role-arn-1")
            .atlasAssumedRoleExternalId(UUID.randomUUID().toString())
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(oid(0))
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    final NDSCloudProviderAccessAWSIAMRole role2 =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(new ObjectId(1, 1))
            .iamAssumedRoleArn("arn:aws:iam::************:role/buttered-role-arn-2")
            .atlasAssumedRoleExternalId(UUID.randomUUID().toString())
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(oid(1))
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    AppConfig.getInstance(NDSGroupDao.class)
        .addAwsIamRoleToCloudProviderAccess(_group.getId(), role1);
    AppConfig.getInstance(NDSGroupDao.class)
        .addAwsIamRoleToCloudProviderAccess(_group.getId(), role2);
  }
}
