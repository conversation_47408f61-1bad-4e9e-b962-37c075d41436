package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppShutdownException;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCollection;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.cps.core._public.config.CpsAppSettings;
import com.xgen.cloud.cps.pit._private.dao.CpsRegionalMetadataStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsRegionalMetadataStoreConfig;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.nds.dao.CpsRegionalMetadataStoreMongoSvc;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.util.Map.Entry;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CpsOplogAbnormalitiesCheckerSvcIntTests extends JUnit5NDSBaseTest {

  @Inject private AppSettings _appSettings;

  @Inject private CpsAppSettings _cpsAppSettings;

  @Inject private CpsRegionalMetadataStoreConfigDao _cpsRegionalMetadataStoreConfigDao;

  @Inject private CpsRegionalMetadataStoreMongoSvc _cpsRegionalMetadataStoreMongoSvc;

  private CpsOplogAbnormalitiesCheckerSvc _cpsOplogAbnormalitiesCheckerSvc;

  private static final ObjectId GROUP_ID = new ObjectId("5fea7af66578c75f801dff07");
  private static final ObjectId CLUSTER_UNIQUE_ID = new ObjectId("5fea7a0ff5fd7b7a2979d667");
  private static final String DATABASE_NAME = GROUP_ID.toHexString() + "_cps";
  private static final String COLLECTION_NAME = CLUSTER_UNIQUE_ID.toHexString() + "_cluster-name";

  private static final String STORE_ID = "s3oplog_storeid";
  private static final String BRS_STORE_ID = "oplog_storeid";

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    // Clean start
    _cpsRegionalMetadataStoreMongoSvc.close();

    _cpsRegionalMetadataStoreConfigDao.insertForTests(
        new CpsRegionalMetadataStoreConfig(
            STORE_ID,
            BRS_STORE_ID,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            true,
            false,
            false,
            null,
            "fake_deployment_id",
            0));

    _cpsOplogAbnormalitiesCheckerSvc =
        spy(
            new CpsOplogAbnormalitiesCheckerSvc(
                _appSettings, _cpsAppSettings, _cpsRegionalMetadataStoreMongoSvc));
  }

  @AfterEach
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    // Prevent test leaking MongoClients
    _cpsRegionalMetadataStoreMongoSvc.close();
  }

  @Test
  public void checkOplogAbnormalities_nonCpsDatabaseNameOrCollectionName()
      throws InterruptedException, AppShutdownException {
    final Set<Entry<String, MongoClient>> allEntries =
        _cpsRegionalMetadataStoreMongoSvc.getAllEntries();
    assertEquals(1, allEntries.size(), "sanity: " + allEntries);

    final Entry<String, MongoClient> entry = allEntries.iterator().next();
    final String oplogStoreId = entry.getKey();
    final MongoClient client = entry.getValue();

    _cpsOplogAbnormalitiesCheckerSvc.checkAbnormalitiesForOplogStoreId(oplogStoreId, client);

    DBCollection collection = DbUtils.getDB(client, "non-metadata").getCollection("collection");
    verify(_cpsOplogAbnormalitiesCheckerSvc, never())
        .checkAbnormalitiesForCollection(oplogStoreId, collection.getDB(), collection.getName());
  }

  @Test
  public void checkOplogAbnormalities_indexesAsExpected()
      throws InterruptedException, AppShutdownException {
    final Set<Entry<String, MongoClient>> allEntries =
        _cpsRegionalMetadataStoreMongoSvc.getAllEntries();
    assertEquals(1, allEntries.size(), "sanity: " + allEntries);

    final Entry<String, MongoClient> entry = allEntries.iterator().next();
    final String oplogStoreId = entry.getKey();
    final MongoClient client = entry.getValue();

    // create all indexes for the collection
    final CpsOplogSliceMetadataDaoProxy dao =
        CpsOplogSliceMetadataDaoProxy.get(
            _cpsRegionalMetadataStoreMongoSvc,
            STORE_ID,
            DATABASE_NAME,
            WriteConcern.JOURNALED,
            COLLECTION_NAME);
    dao.ensureIndexes();

    DBCollection collection = DbUtils.getDB(client, DATABASE_NAME).getCollection(COLLECTION_NAME);
    final Set<String> indexSignatures =
        _cpsOplogAbnormalitiesCheckerSvc.getIndexSignatures(collection);

    assertFalse(
        _cpsOplogAbnormalitiesCheckerSvc.hasMissingRequiredIndex(
            indexSignatures, DATABASE_NAME, COLLECTION_NAME, oplogStoreId));
    assertFalse(
        _cpsOplogAbnormalitiesCheckerSvc.hasUnexpectedIndex(
            indexSignatures, DATABASE_NAME, COLLECTION_NAME, oplogStoreId));

    _cpsOplogAbnormalitiesCheckerSvc.checkAbnormalitiesForOplogStoreId(oplogStoreId, client);

    verify(_cpsOplogAbnormalitiesCheckerSvc, never())
        .remediateMissingIndexes(DATABASE_NAME, COLLECTION_NAME, oplogStoreId);
  }

  @Test
  public void checkOplogAbnormalities_unexpectedIndex()
      throws InterruptedException, AppShutdownException {
    final Set<Entry<String, MongoClient>> allEntries =
        _cpsRegionalMetadataStoreMongoSvc.getAllEntries();
    assertEquals(1, allEntries.size(), "sanity: " + allEntries);

    final Entry<String, MongoClient> entry = allEntries.iterator().next();
    final String oplogStoreId = entry.getKey();
    final MongoClient client = entry.getValue();

    // create all indexes for the collection
    final CpsOplogSliceMetadataDaoProxy dao =
        CpsOplogSliceMetadataDaoProxy.get(
            _cpsRegionalMetadataStoreMongoSvc,
            STORE_ID,
            DATABASE_NAME,
            WriteConcern.JOURNALED,
            COLLECTION_NAME);
    dao.ensureIndexes();

    DBCollection collection = DbUtils.getDB(client, DATABASE_NAME).getCollection(COLLECTION_NAME);

    // add an extra index
    collection.createIndex(new BasicDBObject("clusterName", 1));

    final Set<String> indexSignatures =
        _cpsOplogAbnormalitiesCheckerSvc.getIndexSignatures(collection);

    assertFalse(
        _cpsOplogAbnormalitiesCheckerSvc.hasMissingRequiredIndex(
            indexSignatures, DATABASE_NAME, COLLECTION_NAME, oplogStoreId));
    assertTrue(
        _cpsOplogAbnormalitiesCheckerSvc.hasUnexpectedIndex(
            indexSignatures, DATABASE_NAME, COLLECTION_NAME, oplogStoreId));

    _cpsOplogAbnormalitiesCheckerSvc.checkAbnormalitiesForOplogStoreId(oplogStoreId, client);

    verify(_cpsOplogAbnormalitiesCheckerSvc, never())
        .remediateMissingIndexes(DATABASE_NAME, COLLECTION_NAME, oplogStoreId);
  }

  @Test
  public void checkOplogAbnormalities_missingIndexes()
      throws InterruptedException, AppShutdownException {
    final Set<Entry<String, MongoClient>> allEntries =
        _cpsRegionalMetadataStoreMongoSvc.getAllEntries();
    assertEquals(1, allEntries.size(), "sanity: " + allEntries);

    final Entry<String, MongoClient> entry = allEntries.iterator().next();
    final String oplogStoreId = entry.getKey();
    final MongoClient client = entry.getValue();

    DBCollection collection = DbUtils.getDB(client, DATABASE_NAME).getCollection(COLLECTION_NAME);
    // make sure the collection has no indexes
    collection.dropIndexes();
    final Set<String> indexSignatures =
        _cpsOplogAbnormalitiesCheckerSvc.getIndexSignatures(collection);

    assertTrue(
        _cpsOplogAbnormalitiesCheckerSvc.hasMissingRequiredIndex(
            indexSignatures, DATABASE_NAME, COLLECTION_NAME, oplogStoreId));
    assertFalse(
        _cpsOplogAbnormalitiesCheckerSvc.hasUnexpectedIndex(
            indexSignatures, DATABASE_NAME, COLLECTION_NAME, oplogStoreId));

    _cpsOplogAbnormalitiesCheckerSvc.checkAbnormalitiesForOplogStoreId(oplogStoreId, client);

    verify(_cpsOplogAbnormalitiesCheckerSvc)
        .remediateMissingIndexes(DATABASE_NAME, COLLECTION_NAME, oplogStoreId);
  }
}
