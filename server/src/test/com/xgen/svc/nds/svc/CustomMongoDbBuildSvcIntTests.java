package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.system._public.model.PlatformConstants;
import com.xgen.cloud.common.system._public.model.PlatformConstants.InternalArchitectures;
import com.xgen.cloud.deployment._public.model.MongoDbBuild;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.CustomMongoDbBuildDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CustomMongoDbBuildSvcIntTests extends JUnit5BaseSvcTest {
  @Inject private CustomMongoDbBuildDao _customMongoDbBuildDao;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  // Test subject
  @Inject private CustomMongoDbBuildSvc _customMongoDbBuildSvc;

  private final String TRUE_NAME_FORMAT = "4.0.%d-test";
  private final String URL_FORMAT = "s3://host.with.the.most.v%d";
  private final String GIT_VERSION_FORMAT = "23498723%d";
  private final int NUM_BUILDS = 3;

  @BeforeEach
  public void setup() throws Exception {

    super.setUp();
    try {
      _ndsClusterSvc.start();
    } catch (IllegalStateException e) {
      // Ignore IllegalStateException because CONTINUOUS_DELIVERY is not set.
    }

    // Seed collection with custom builds
    IntStream.range(0, NUM_BUILDS)
        .forEach(
            i -> {
              final MongoDbBuild.Builder builder = new MongoDbBuild.Builder();
              builder.trueName(String.format(TRUE_NAME_FORMAT, i));
              builder.url(String.format(URL_FORMAT, i));
              builder.platform(PlatformConstants.Platforms.linux);
              builder.architecture(InternalArchitectures.AMD64.getName());
              builder.gitVersion(String.format(GIT_VERSION_FORMAT, i));
              _customMongoDbBuildDao.save(builder.build());
            });

    // Mark a cluster as using one of these builds
    final BasicDBObject descDoc = NDSModelTestFactory.getGCPClusterDescription();
    descDoc.append(
        ClusterDescription.FieldDefs.MONGODB_VERSION, String.format(TRUE_NAME_FORMAT, 0));

    _clusterDescriptionDao.save(new ClusterDescription(descDoc));
  }

  @Test
  public void testGetCustomBuilds() throws Exception {
    final List<MongoDbBuild> versions = _customMongoDbBuildDao.findAllVersions();
    assertEquals(NUM_BUILDS, versions.size());
    IntStream.range(0, NUM_BUILDS)
        .forEach(
            i -> {
              final MongoDbBuild buildFromList = versions.get(i);

              final List<MongoDbBuild> buildsByTrueName =
                  _customMongoDbBuildSvc.getCustomBuild(buildFromList.getTrueName());
              assertFalse(buildsByTrueName.isEmpty());

              final MongoDbBuild buildByTrueName = buildsByTrueName.get(0);
              assertEquals(buildByTrueName.getTrueName(), buildFromList.getTrueName());
              assertEquals(buildByTrueName.getUrl(), buildFromList.getUrl());
              assertEquals(buildByTrueName.getPlatform(), buildFromList.getPlatform());
              assertEquals(buildByTrueName.getGitVersion(), buildFromList.getGitVersion());
            });
  }

  @Test
  public void testAddCustomBuild() throws Exception {
    // When add invalid build
    final MongoDbBuild.Builder builder = new MongoDbBuild.Builder();
    builder.trueName("invalidBuild");
    builder.url(String.format(URL_FORMAT, NUM_BUILDS));
    builder.platform(PlatformConstants.Platforms.linux);
    builder.gitVersion(String.format(GIT_VERSION_FORMAT, NUM_BUILDS));

    // Cannot add to collection because does not pass validation
    try {
      _customMongoDbBuildSvc.addCustomBuild(builder.build());
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_MONGODB_CUSTOM_BUILD_NAME, e.getErrorCode());
    }

    // When add valid build with conflicting name
    builder.trueName(String.format(TRUE_NAME_FORMAT, NUM_BUILDS - 1));
    _customMongoDbBuildDao.save(builder.build());

    // Adding the build fails for encountering an automation validation error
    try {
      _customMongoDbBuildSvc.addCustomBuild(builder.build());
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_MONGODB_VERSION_CONFIG, e.getErrorCode());
    }

    // The build cannot be added because the svc will find a duplicate build
    builder.architecture(PlatformConstants.InternalArchitectures.AMD64.getName());
    try {
      _customMongoDbBuildSvc.addCustomBuild(builder.build());
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.DUPLICATE_MONGODB_BUILD_NAME, e.getErrorCode());
    }

    // When add valid build with a different architecture
    builder.architecture(PlatformConstants.InternalArchitectures.AARCH64.getName());

    final MongoDbBuild validBuildDiffArch = builder.build();
    // Passes validation, finds no duplicate build, so is saved to DAO
    try {
      _customMongoDbBuildSvc.addCustomBuild(validBuildDiffArch);
    } catch (Exception e) {
      fail();
    }

    // Afterward, build is in the collection
    final Optional<MongoDbBuild> savedBuildDiffArch =
        _customMongoDbBuildDao.findByTrueName(validBuildDiffArch.getTrueName()).stream()
            .filter(
                mdbBuild ->
                    mdbBuild
                        .getArchitecture()
                        .equals(PlatformConstants.InternalArchitectures.AARCH64.getName()))
            .findFirst();
    assertTrue(savedBuildDiffArch.isPresent());
    assertEquals(validBuildDiffArch.getTrueName(), savedBuildDiffArch.get().getTrueName());
    assertEquals(validBuildDiffArch.getGitVersion(), savedBuildDiffArch.get().getGitVersion());
    assertEquals(validBuildDiffArch.getPlatform(), savedBuildDiffArch.get().getPlatform());
    assertEquals(validBuildDiffArch.getArchitecture(), savedBuildDiffArch.get().getArchitecture());
    assertEquals(validBuildDiffArch.getUrl(), savedBuildDiffArch.get().getUrl());

    // When add valid build with unique name
    builder.trueName(String.format(TRUE_NAME_FORMAT, NUM_BUILDS));
    final MongoDbBuild validBuildUniqueName = builder.build();

    // Passes validation, finds no duplicate build, so is saved to DAO
    try {
      _customMongoDbBuildSvc.addCustomBuild(validBuildUniqueName);
    } catch (Exception e) {
      fail();
    }

    // Afterward, build is in the collection
    final Optional<MongoDbBuild> savedBuildUniqueName =
        _customMongoDbBuildDao.findByTrueName(validBuildUniqueName.getTrueName()).stream()
            .findFirst();
    assertTrue(savedBuildUniqueName.isPresent());
    assertEquals(validBuildUniqueName.getTrueName(), savedBuildUniqueName.get().getTrueName());
    assertEquals(validBuildUniqueName.getGitVersion(), savedBuildUniqueName.get().getGitVersion());
    assertEquals(validBuildUniqueName.getPlatform(), savedBuildUniqueName.get().getPlatform());
    assertEquals(validBuildUniqueName.getUrl(), savedBuildUniqueName.get().getUrl());
  }

  @Test
  public void testDeleteCustomBuild() throws Exception {
    // Delete build that does not exist
    try {
      _customMongoDbBuildSvc.deleteCustomBuilds("doesnotexist");
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.MONGODB_BUILD_DOES_NOT_EXIST, e.getErrorCode());
    }

    // Test cannot delete build that is being used by a cluster
    final String buildInUse = String.format(TRUE_NAME_FORMAT, 0);
    final Optional<MongoDbBuild> savedBuild =
        _customMongoDbBuildDao.findByTrueName(buildInUse).stream().findFirst();
    assertTrue(savedBuild.isPresent());

    // Check that we will find cluster that is using custom build
    assertTrue(
        _ndsClusterSvc
            .findClusterDescriptionsByBuildVersion(savedBuild.get().getTrueName())
            .hasNext());
    assertFalse(
        _ndsClusterSvc
            .findPendingClusterDescriptionsByBuildVersion(savedBuild.get().getTrueName())
            .hasNext());

    // Cannot delete build that is in use
    try {
      _customMongoDbBuildSvc.deleteCustomBuilds(buildInUse);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.MONGODB_BUILD_IN_USE, e.getErrorCode());
    }

    // Can delete build that is valid and not in use
    final String buildToDelete = String.format(TRUE_NAME_FORMAT, 1);
    final Optional<MongoDbBuild> savedBuildToDelete =
        _customMongoDbBuildDao.findByTrueName(buildToDelete).stream().findFirst();

    assertTrue(savedBuildToDelete.isPresent());
    assertFalse(
        _ndsClusterSvc
            .findClusterDescriptionsByBuildVersion(savedBuildToDelete.get().getTrueName())
            .hasNext());
    assertFalse(
        _ndsClusterSvc
            .findPendingClusterDescriptionsByBuildVersion(savedBuildToDelete.get().getTrueName())
            .hasNext());
    try {
      _customMongoDbBuildSvc.deleteCustomBuilds(savedBuildToDelete.get().getTrueName());
    } catch (Exception e) {
      fail();
    }

    // No longer in collection
    assertTrue(_customMongoDbBuildDao.findByTrueName(buildToDelete).isEmpty());
  }
}
