package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.svc.nds.aws.planner.AwsSwapIpMove;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.List;
import java.util.Map;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.Test;

public class FlagLastAwsSwapIpMoveIntTests extends DependencyRuleBaseTests {
  @Test
  public void testApply() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, new ObjectId(), clusterDescription));
    assertEquals(3, replicaSetHardware.getHardware().size());

    final Host host = new Host();
    host.setName("some host name");

    final var rule = new FlagLastAwsSwapIpMove();

    // AwsSwapIpMove in last action should get flagged as the last move
    {
      List<PlannedAction> swapIpActions = new java.util.ArrayList<>(List.of());
      replicaSetHardware
          .getHardware()
          .forEach(
              hardware ->
                  swapIpActions.add(
                      _actionFactory.forAwsSwapIpMove(
                          Action.SWAP_FUTURE_IP,
                          clusterDescription.getDeploymentClusterName(),
                          hardware.getInstanceId(),
                          replicaSetHardware,
                          hardware,
                          host,
                          Map.of(),
                          new BSONTimestamp(12, 0))));

      // The AwsSwapIpMove and WaitForMachineHealthyMove in the last action should be the last moves
      assertEquals(3, swapIpActions.size());
      swapIpActions.get(0).getMoves()[1].addPredecessor(swapIpActions.get(0).getMoves()[0]);
      swapIpActions.get(1).getMoves()[0].addPredecessor(swapIpActions.get(0).getMoves()[1]);
      swapIpActions.get(1).getMoves()[1].addPredecessor(swapIpActions.get(1).getMoves()[0]);
      swapIpActions.get(2).getMoves()[0].addPredecessor(swapIpActions.get(1).getMoves()[1]);
      swapIpActions.get(2).getMoves()[1].addPredecessor(swapIpActions.get(2).getMoves()[0]);

      rule.apply(swapIpActions);

      final var firstSwapIpMove = (AwsSwapIpMove) swapIpActions.get(0).getMoves()[0];
      assertFalse(firstSwapIpMove.getIsLastSwapIpMove());

      final var secondSwapIpMove = (AwsSwapIpMove) swapIpActions.get(1).getMoves()[0];
      assertFalse(secondSwapIpMove.getIsLastSwapIpMove());

      final var lastSwapIpMove = (AwsSwapIpMove) swapIpActions.get(2).getMoves()[0];
      assertTrue(lastSwapIpMove.getIsLastSwapIpMove());
    }

    // AwsSwapIpMove in first action should get flagged as the last move
    {
      List<PlannedAction> swapIpActions = new java.util.ArrayList<>(List.of());
      replicaSetHardware
          .getHardware()
          .forEach(
              hardware ->
                  swapIpActions.add(
                      _actionFactory.forAwsSwapIpMove(
                          Action.SWAP_FUTURE_IP,
                          clusterDescription.getDeploymentClusterName(),
                          hardware.getInstanceId(),
                          replicaSetHardware,
                          hardware,
                          host,
                          Map.of(),
                          new BSONTimestamp(12, 0))));

      // The AwsSwapIpMove and WaitForMachineHealthyMove in the first action should be the last
      // moves
      assertEquals(3, swapIpActions.size());
      swapIpActions.get(1).getMoves()[1].addPredecessor(swapIpActions.get(1).getMoves()[0]);
      swapIpActions.get(2).getMoves()[0].addPredecessor(swapIpActions.get(1).getMoves()[1]);
      swapIpActions.get(2).getMoves()[1].addPredecessor(swapIpActions.get(2).getMoves()[0]);
      swapIpActions.get(0).getMoves()[0].addPredecessor(swapIpActions.get(2).getMoves()[1]);
      swapIpActions.get(0).getMoves()[1].addPredecessor(swapIpActions.get(0).getMoves()[0]);

      rule.apply(swapIpActions);

      final var firstSwapIpMove = (AwsSwapIpMove) swapIpActions.get(0).getMoves()[0];
      assertTrue(firstSwapIpMove.getIsLastSwapIpMove());

      final var secondSwapIpMove = (AwsSwapIpMove) swapIpActions.get(1).getMoves()[0];
      assertFalse(secondSwapIpMove.getIsLastSwapIpMove());

      final var lastSwapIpMove = (AwsSwapIpMove) swapIpActions.get(2).getMoves()[0];
      assertFalse(lastSwapIpMove.getIsLastSwapIpMove());
    }

    // AwsSwapIpMove in second action should get flagged as the last move
    {
      List<PlannedAction> swapIpActions = new java.util.ArrayList<>(List.of());
      replicaSetHardware
          .getHardware()
          .forEach(
              hardware ->
                  swapIpActions.add(
                      _actionFactory.forAwsSwapIpMove(
                          Action.SWAP_FUTURE_IP,
                          clusterDescription.getDeploymentClusterName(),
                          hardware.getInstanceId(),
                          replicaSetHardware,
                          hardware,
                          host,
                          Map.of(),
                          new BSONTimestamp(12, 0))));

      // The AwsSwapIpMove and WaitForMachineHealthyMove in the first action should be the last
      // moves
      assertEquals(3, swapIpActions.size());
      swapIpActions.get(2).getMoves()[1].addPredecessor(swapIpActions.get(2).getMoves()[0]);
      swapIpActions.get(0).getMoves()[0].addPredecessor(swapIpActions.get(2).getMoves()[1]);
      swapIpActions.get(0).getMoves()[1].addPredecessor(swapIpActions.get(0).getMoves()[0]);
      swapIpActions.get(1).getMoves()[0].addPredecessor(swapIpActions.get(0).getMoves()[1]);
      swapIpActions.get(1).getMoves()[1].addPredecessor(swapIpActions.get(1).getMoves()[0]);

      rule.apply(swapIpActions);

      final var firstSwapIpMove = (AwsSwapIpMove) swapIpActions.get(0).getMoves()[0];
      assertFalse(firstSwapIpMove.getIsLastSwapIpMove());

      final var secondSwapIpMove = (AwsSwapIpMove) swapIpActions.get(1).getMoves()[0];
      assertTrue(secondSwapIpMove.getIsLastSwapIpMove());

      final var lastSwapIpMove = (AwsSwapIpMove) swapIpActions.get(2).getMoves()[0];
      assertFalse(lastSwapIpMove.getIsLastSwapIpMove());
    }

    // Single AwsSwapIpMove get flagged as the last move
    {
      List<PlannedAction> swapIpActions = new java.util.ArrayList<>(List.of());

      swapIpActions.add(
          _actionFactory.forAwsSwapIpMove(
              Action.SWAP_FUTURE_IP,
              clusterDescription.getDeploymentClusterName(),
              replicaSetHardware.getHardware().get(0).getInstanceId(),
              replicaSetHardware,
              replicaSetHardware.getHardware().get(0),
              host,
              Map.of(),
              new BSONTimestamp(12, 0)));

      // The AwsSwapIpMove and WaitForMachineHealthyMove in the first action should be the last
      // moves
      assertEquals(1, swapIpActions.size());
      swapIpActions.get(0).getMoves()[1].addPredecessor(swapIpActions.get(0).getMoves()[0]);

      rule.apply(swapIpActions);

      final var firstSwapIpMove = (AwsSwapIpMove) swapIpActions.get(0).getMoves()[0];
      assertTrue(firstSwapIpMove.getIsLastSwapIpMove());
    }
  }
}
