package com.xgen.svc.nds.svc.planning;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.activity._private.dao.EventDao;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._private.dao.HostClusterDao;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.admin.NDSAdminJobDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionFactory;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus.State;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSHostModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob.Type;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.decoupled.cloudprovider._private.dao.PartitionGroupDao;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchPhysicalModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.aws.AWSSearchInstanceSize;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSAdminJobSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSAdminJobSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private EventDao _eventDao;
  @Inject private NDSAdminJobSvc _ndsAdminJobSvc;
  @Inject private NDSAdminJobDao _ndsAdminJobDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private PlanDao _planDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private HostClusterDao _hostClusterDao;
  @Inject private HostDao _hostDao;
  @Inject private PartitionGroupDao _partitionGroupDao;

  private NDSGroup _ndsGroup;
  private Group _group;
  private AppUser _globalAtlasAdmin;
  private AuditInfo _auditInfo;

  private static final String COMMENT = "comment";

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(organization, "AdminJobsTestser001");
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    _ndsGroupDao.addCloudContainer(
        _group.getId(), new AWSCloudProviderContainer(new ObjectId(), AWSRegionName.US_EAST_1));
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    _globalAtlasAdmin = new AppUser();
    _globalAtlasAdmin.setRoleAssignment(RoleAssignment.forGlobal(Role.GLOBAL_ATLAS_ADMIN), true);
    _auditInfo = MmsFactory.createAuditInfoFromUiCall(_globalAtlasAdmin, true, "127.0.0.1");
  }

  @Test
  public void testSubmitManageMongotuneProcessesAdminJob_Validations() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ObjectId groupId = _ndsGroup.getGroupId();

    // Test basic validations
    {
      final ClusterDescription clusterDescriptionNoMongotune =
          ClusterDescriptionFactory.get(
              NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster0"));
      _clusterDescriptionDao.save(clusterDescriptionNoMongotune);
      // Group not found - Fail
      try {
        _ndsAdminJobSvc.submitMongotuneBinaryAdminJob(
            new ObjectId(),
            clusterDescriptionNoMongotune,
            State.ENABLE,
            AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.INVALID_GROUP_ID, pE.getErrorCode());
      }

      // Mongotune status is empty - Fail
      try {
        _ndsAdminJobSvc.submitMongotuneBinaryAdminJob(
            groupId, clusterDescriptionNoMongotune, State.ENABLE, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
        assertTrue(pE.getMessage().contains("Mongotune status not found"));
      }
    }

    {
      ClusterDescription clusterDescriptionWithMongotune =
          ClusterDescriptionFactory.get(
              NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster1"));
      clusterDescriptionWithMongotune =
          clusterDescriptionWithMongotune
              .copy()
              .setMongotuneStatus(new MongotuneStatus(State.ENABLE, "1.0.0", Map.of()))
              .build();
      _clusterDescriptionDao.save(clusterDescriptionWithMongotune);

      // mongotune is already in goal state
      try {
        _ndsAdminJobSvc.submitMongotuneBinaryAdminJob(
            groupId, clusterDescriptionWithMongotune, State.ENABLE, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
        assertTrue(pE.getMessage().contains("Mongotune goal state on cluster description matches"));
      }

      // admin job already in progress
      final NDSAdminJob preExistingAdminJob =
          _ndsAdminJobDao.create(
              new ObjectId(),
              groupId,
              clusterDescriptionWithMongotune.getName(),
              null,
              Type.ENABLE_MONGOTUNE);
      try {
        _ndsAdminJobSvc.submitMongotuneBinaryAdminJob(
            groupId, clusterDescriptionWithMongotune, State.ENABLE, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.ADMIN_JOB_OF_TYPE_IN_PROGRESS_FOR_CLUSTER, pE.getErrorCode());
      }
    }
  }

  @Test
  public void testSubmitManageMongotuneProcessesAdminJob_Success() throws SvcException {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ObjectId groupId = _ndsGroup.getGroupId();
    ClusterDescription clusterDescriptionWithMongotuneEnabled =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster1"));
    clusterDescriptionWithMongotuneEnabled =
        clusterDescriptionWithMongotuneEnabled
            .copy()
            .setMongotuneStatus(new MongotuneStatus(State.ENABLE, "1.0.0", Map.of()))
            .build();
    _clusterDescriptionDao.save(clusterDescriptionWithMongotuneEnabled);

    _ndsAdminJobSvc.submitMongotuneBinaryAdminJob(
        groupId, clusterDescriptionWithMongotuneEnabled, State.DISABLE, _auditInfo);

    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByClusterTypeAndStatus(
            groupId,
            clusterDescriptionWithMongotuneEnabled.getName(),
            Type.DISABLE_MONGOTUNE,
            NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    assertEquals(
        1, _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.MONGOTUNE_DISABLED).size());

    // Clean up jobs
    _ndsAdminJobDao.remove(new BasicDBObject("_id", workingAdminJobsAfterSubmit.get(0).getId()));
    _planDao.remove(new BasicDBObject("_id", plansAfterSubmit.get(0).get("_id")));
  }

  @Test
  public void testSubmitMongotunePolicyAdminJob_Validations() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ObjectId groupId = _ndsGroup.getGroupId();

    // Test basic validations
    {
      final ClusterDescription clusterDescriptionNoMongotune =
          ClusterDescriptionFactory.get(
              NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster0"));
      _clusterDescriptionDao.save(clusterDescriptionNoMongotune);
      // Group not found - Fail
      try {
        _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
            new ObjectId(),
            clusterDescriptionNoMongotune,
            PolicyType.DISK_WRITE_BLOCKING,
            true,
            AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.INVALID_GROUP_ID, pE.getErrorCode());
      }

      // Mongotune status is empty - Fail
      try {
        _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
            groupId,
            clusterDescriptionNoMongotune,
            PolicyType.DISK_WRITE_BLOCKING,
            true,
            AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
        assertTrue(pE.getMessage().contains("Mongotune status not found"));
      }
    }

    {
      // Create cluster with mongotune status and policy already in goal state
      final DiskWriteBlockPolicyStatus enabledPolicy =
          DiskWriteBlockPolicyStatus.builder()
              .setEnabled(true)
              .setEligible(true)
              .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
              .build();
      ClusterDescription clusterDescriptionWithMongotune =
          ClusterDescriptionFactory.get(
              NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster1"));
      clusterDescriptionWithMongotune =
          clusterDescriptionWithMongotune
              .copy()
              .setMongotuneStatus(
                  new MongotuneStatus(
                      State.ENABLE, "1.0.0", Map.of(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy)))
              .build();
      _clusterDescriptionDao.save(clusterDescriptionWithMongotune);

      // Policy is already in goal state (enabled) - Fail
      try {
        _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
            groupId,
            clusterDescriptionWithMongotune,
            PolicyType.DISK_WRITE_BLOCKING,
            true,
            AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
        assertTrue(pE.getMessage().contains("goal state on cluster description matches"));
      }

      // Admin job already in progress for policy
      _ndsAdminJobDao.create(
          new ObjectId(),
          groupId,
          clusterDescriptionWithMongotune.getName(),
          null,
          Type.ENABLE_MONGOTUNE_POLICY);
      try {
        _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
            groupId,
            clusterDescriptionWithMongotune,
            PolicyType.DISK_WRITE_BLOCKING,
            true,
            AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.ADMIN_JOB_OF_TYPE_IN_PROGRESS_FOR_CLUSTER, pE.getErrorCode());
      }
    }
  }

  @Test
  public void testSubmitMongotunePolicyAdminJob_Success() throws SvcException {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ObjectId groupId = _ndsGroup.getGroupId();

    // Create cluster with mongotune enabled and policy disabled (so we can enable it)
    final DiskWriteBlockPolicyStatus disabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(false)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();
    ClusterDescription clusterDescriptionWithMongotuneEnabled =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster1"));
    clusterDescriptionWithMongotuneEnabled =
        clusterDescriptionWithMongotuneEnabled
            .copy()
            .setMongotuneStatus(
                new MongotuneStatus(
                    State.ENABLE, "1.0.0", Map.of(PolicyType.DISK_WRITE_BLOCKING, disabledPolicy)))
            .build();
    _clusterDescriptionDao.save(clusterDescriptionWithMongotuneEnabled);

    // Submit job to enable the policy
    _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
        groupId,
        clusterDescriptionWithMongotuneEnabled,
        PolicyType.DISK_WRITE_BLOCKING,
        true,
        _auditInfo);

    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByClusterTypeAndStatus(
            groupId,
            clusterDescriptionWithMongotuneEnabled.getName(),
            Type.ENABLE_MONGOTUNE_POLICY,
            NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    assertEquals(
        1,
        _eventDao
            .findByEventTypeForGroup(groupId, NDSAudit.Type.MONGOTUNE_POLICY_ENABLED_BY_ADMIN)
            .size());

    // Clean up jobs
    _ndsAdminJobDao.remove(new BasicDBObject("_id", workingAdminJobsAfterSubmit.get(0).getId()));
    _planDao.remove(new BasicDBObject("_id", plansAfterSubmit.get(0).get("_id")));
  }

  @Test
  public void testSubmitMongotunePolicyAdminJob_DisablePolicy_Success() throws SvcException {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ObjectId groupId = _ndsGroup.getGroupId();

    // Create cluster with mongotune enabled and policy enabled (so we can disable it)
    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();
    ClusterDescription clusterDescriptionWithMongotuneEnabled =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster2"));
    clusterDescriptionWithMongotuneEnabled =
        clusterDescriptionWithMongotuneEnabled
            .copy()
            .setMongotuneStatus(
                new MongotuneStatus(
                    State.ENABLE, "1.0.0", Map.of(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy)))
            .build();
    _clusterDescriptionDao.save(clusterDescriptionWithMongotuneEnabled);

    // Submit job to disable the policy
    _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
        groupId,
        clusterDescriptionWithMongotuneEnabled,
        PolicyType.DISK_WRITE_BLOCKING,
        false,
        _auditInfo);

    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByClusterTypeAndStatus(
            groupId,
            clusterDescriptionWithMongotuneEnabled.getName(),
            Type.DISABLE_MONGOTUNE_POLICY,
            NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    assertEquals(
        1,
        _eventDao
            .findByEventTypeForGroup(groupId, NDSAudit.Type.MONGOTUNE_POLICY_DISABLED_BY_ADMIN)
            .size());

    // Clean up jobs
    _ndsAdminJobDao.remove(new BasicDBObject("_id", workingAdminJobsAfterSubmit.get(0).getId()));
    _planDao.remove(new BasicDBObject("_id", plansAfterSubmit.get(0).get("_id")));
  }

  @Test
  public void testSubmitUnsetUserWriteBlockModelAgentJobAdminJobAndPlan() throws SvcException {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ObjectId groupId = _ndsGroup.getGroupId();

    ClusterDescription clusterDescription =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, "Cluster2"));
    _clusterDescriptionDao.save(clusterDescription);

    // Submit job to disable the policy
    _ndsAdminJobSvc.submitUnsetUserWriteBlockModelAgentJobAdminJobAndPlan(
        clusterDescription, _auditInfo);

    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByClusterTypeAndStatus(
            groupId,
            clusterDescription.getName(),
            Type.UNSET_USER_WRITE_BLOCK_MODE,
            NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    assertEquals(
        1,
        _eventDao
            .findByEventTypeForGroup(groupId, NDSAudit.Type.UNSET_USER_WRITE_BLOCK_MODE)
            .size());

    // Clean up jobs
    _ndsAdminJobDao.remove(new BasicDBObject("_id", workingAdminJobsAfterSubmit.get(0).getId()));
    _planDao.remove(new BasicDBObject("_id", plansAfterSubmit.get(0).get("_id")));
  }

  @Test
  public void testSubmitAdminJob_Resync() throws Exception {
    final ObjectId groupId = _ndsGroup.getGroupId();
    final String clusterName = "GoOnAndResynMyInstance";
    final NDSAdminJob.Type type = NDSAdminJob.Type.RESYNC;

    final ClusterDescription clusterDescription =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName));
    _clusterDescriptionDao.save(clusterDescription);

    final BasicDBObject replicaSetHardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, _ndsGroup.getCloudProviderContainers().get(0).getId(), clusterDescription);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(replicaSetHardwareDoc);
    _replicaSetHardwareDao.saveReplicaSafe(replicaSetHardwareDoc);

    final String primaryHost = replicaSetHardware.getHardware().get(0).getHostnameForAgents().get();
    final String secondaryHost =
        replicaSetHardware.getHardware().get(1).getHostnameForAgents().get();

    // Job already in process for host - Fail
    final NDSAdminJob preExistingAdminJob =
        _ndsAdminJobDao.create(new ObjectId(), groupId, clusterName, secondaryHost, type);
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, secondaryHost, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.ADMIN_JOB_OF_TYPE_IN_PROGRSSS, pE.getErrorCode());
    }
    _ndsAdminJobDao.updateJobStatus(preExistingAdminJob.getId(), NDSAdminJob.Status.COMPLETE, null);

    // No Primary, not requested by GLOBAL_ATLAS_ADMIN - Fail
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, secondaryHost, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("Only a Global Atlas Admin"));
      assertTrue(pE.getMessage().contains("without a primary host"));
    }

    assertEquals(
        0,
        _eventDao
            .findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED)
            .size());

    // No Secondary, not requested by GLOBAL_ATLAS_ADMIN - Fail
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, secondaryHost, type, _auditInfo, _globalAtlasAdmin, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("No secondaries in the replica set"));
    }

    assertEquals(
        0,
        _eventDao
            .findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED)
            .size());

    // setup 1 secondary and 2 recovering
    final Cluster cluster = Cluster.getCluster(clusterDescription, List.of(replicaSetHardware));
    final List<HostCluster> hostClusters =
        NDSHostModelTestFactory.getHostClusters(_ndsGroup.getGroupId(), cluster, true);
    hostClusters.forEach(
        hc -> {
          _hostClusterDao.save(hc);
          hc.getHosts()
              .forEach(
                  h -> {
                    h.setHostTypes(
                        List.of(
                            (h.getName().equals(secondaryHost)
                                    ? HostType.REPLICA_SECONDARY
                                    : HostType.RECOVERING)
                                .getCode()));
                    _hostDao.save(
                        NDSHostModelTestFactory.convertHostToDbObject(h, groupId),
                        WriteConcern.ACKNOWLEDGED);
                  });
        });

    // One secondary, 2 recovering, request to resync the secondary by GLOBAL_ATLAS_ADMIN - Fail
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, secondaryHost, type, _auditInfo, _globalAtlasAdmin, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode(), pE.getMessage());
      assertTrue(pE.getMessage().contains("is the only secondary in the replica set"));
    }

    assertEquals(
        0,
        _eventDao
            .findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED)
            .size());

    final Calendar timeOfUpdateToSecondaries = Calendar.getInstance();
    timeOfUpdateToSecondaries.setTimeInMillis(Instant.now().toEpochMilli());

    // Set nodes into secondary
    hostClusters.forEach(
        hc ->
            hc.getHosts()
                .forEach(
                    h -> {
                      h.setHostTypes(List.of(HostType.REPLICA_SECONDARY.getCode()));
                      _hostDao.save(
                          NDSHostModelTestFactory.convertHostToDbObject(h, groupId),
                          WriteConcern.ACKNOWLEDGED);
                    }));

    // No Primary, but secondary available and was requested by GLOBAL_ATLAS_ADMIN - should succeed
    {
      final List<Event> eventsBeforeSubmit =
          _eventDao.findByEventTypeForGroup(
              groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED);
      assertEquals(0, eventsBeforeSubmit.size());

      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, secondaryHost, type, _auditInfo, _globalAtlasAdmin, COMMENT);

      final List<NDSAdminJob> workingAdminJobsAfterSubmit =
          _ndsAdminJobDao.findByHostnameTypeAndStatus(
              secondaryHost, type, NDSAdminJob.Status.WORKING);
      assertEquals(1, workingAdminJobsAfterSubmit.size());

      final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
      assertEquals(1, plansAfterSubmit.size());

      final List<Event> eventsAfterSubmit =
          _eventDao.findByEventTypeForGroup(
              groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED);
      assertEquals(1, eventsAfterSubmit.size());

      // Remove the jobs we just submitted so further tests can happen
      _ndsAdminJobDao.remove(new BasicDBObject("_id", workingAdminJobsAfterSubmit.get(0).getId()));
      _planDao.remove(new BasicDBObject("_id", plansAfterSubmit.get(0).get("_id")));
      _eventDao.remove(new BasicDBObject("_id", eventsAfterSubmit.get(0).getId()));
    }

    // Set node into primary
    hostClusters.forEach(
        hc ->
            hc.getHosts()
                .forEach(
                    h -> {
                      if (h.getName().equals(primaryHost)) {
                        h.setHostTypes(List.of(HostType.REPLICA_PRIMARY.getCode()));
                        _hostDao.save(
                            NDSHostModelTestFactory.convertHostToDbObject(h, groupId),
                            WriteConcern.ACKNOWLEDGED);
                      }
                    }));

    // Try to resync a primary
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, primaryHost, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.UNSUPPORTED, pE.getErrorCode());
      assertTrue(pE.getMessage().contains("Can not resync the primary host itself"));
    }

    final List<Event> eventsBeforeSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED);
    assertEquals(0, eventsBeforeSubmit.size());

    // Successful Job Submission
    _ndsAdminJobSvc.submitAdminJob(
        groupId, clusterName, secondaryHost, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(
            secondaryHost, type, NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    final List<Event> eventsAfterSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_RESYNC_REQUESTED);
    assertEquals(1, eventsAfterSubmit.size());
  }

  @Test
  public void testSubmitAdminJob_Stop_Start() throws Exception {
    final ObjectId groupId = _ndsGroup.getGroupId();
    final String clusterName = "GoOnAndFlipMyInstance";
    final NDSAdminJob.Type type = NDSAdminJob.Type.STOP_START_VM;

    final ClusterDescription clusterDescription =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName));

    final BasicDBObject replicaSetHardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, _ndsGroup.getCloudProviderContainers().get(0).getId(), clusterDescription);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(replicaSetHardwareDoc);
    final String hostname = replicaSetHardware.getHardware().get(0).getHostnameForAgents().get();

    // Group not found - Fail
    try {
      _ndsAdminJobSvc.submitAdminJob(
          new ObjectId(),
          clusterName,
          hostname,
          NDSAdminJob.Type.STOP_START_VM,
          AuditInfoHelpers.fromSystem(),
          null,
          COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_GROUP_ID, pE.getErrorCode());
    }

    // Job already in process for host - Fail
    final NDSAdminJob preExistingAdminJob =
        _ndsAdminJobDao.create(new ObjectId(), groupId, clusterName, hostname, type);
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, hostname, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.ADMIN_JOB_OF_TYPE_IN_PROGRSSS, pE.getErrorCode());
    }

    // ReplicaSet hardware not found - Fail
    _ndsAdminJobDao.updateJobStatus(preExistingAdminJob.getId(), NDSAdminJob.Status.COMPLETE, null);
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, hostname, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_CLUSTER, pE.getErrorCode());
    }

    // ReplicaSetHardware exists, no instance matching on hostname - Fail
    _replicaSetHardwareDao.saveReplicaSafe(replicaSetHardwareDoc);
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId,
          clusterName,
          "somehostnamethat.does.not.exist",
          type,
          AuditInfoHelpers.fromSystem(),
          null,
          COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_HOST_NAME, pE.getErrorCode());
    }

    // Instance hardware present with a matching hostname, cluster description not found (should
    // never really happen) - Fail
    try {
      _ndsAdminJobSvc.submitAdminJob(
          groupId, clusterName, hostname, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_CLUSTER, pE.getErrorCode());
    }

    final List<NDSAdminJob> workingAdminJobsBeforeSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(hostname, type, NDSAdminJob.Status.WORKING);
    assertEquals(0, workingAdminJobsBeforeSubmit.size());

    final List<BasicDBObject> plansBeforeSubmit = _planDao.findByGroupId(groupId);
    assertEquals(0, plansBeforeSubmit.size());

    final List<Event> eventsBeforeSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_STOP_START);
    assertEquals(0, eventsBeforeSubmit.size());

    // Cluster description exists - Success
    _clusterDescriptionDao.save(clusterDescription);
    _ndsAdminJobSvc.submitAdminJob(
        groupId, clusterName, hostname, type, AuditInfoHelpers.fromSystem(), null, COMMENT);
    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(hostname, type, NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    final List<Event> eventsAfterSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.CLUSTER_INSTANCE_STOP_START);
    assertEquals(1, eventsAfterSubmit.size());
  }

  @Test
  public void testSubmitUpdateBumperFileAdminJob_UpdateBumperFiles() throws Exception {
    final ObjectId groupId = _ndsGroup.getGroupId();
    final String clusterName = "cluster0";
    final NDSAdminJob.Type type = Type.UPDATE_BUMPER_FILES;

    final ClusterDescription clusterDescription =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName));

    final BasicDBObject replicaSetHardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, _ndsGroup.getCloudProviderContainers().get(0).getId(), clusterDescription);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(replicaSetHardwareDoc);
    final String hostname0 = replicaSetHardware.getHardware().get(0).getHostnameForAgents().get();
    final String hostname1 = replicaSetHardware.getHardware().get(1).getHostnameForAgents().get();

    // Group not found - Fail
    try {
      _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
          new ObjectId(), clusterName, hostname0, 2, AuditInfoHelpers.fromSystem());
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_GROUP_ID, pE.getErrorCode());
    }

    // Job already in process for cluster - Fail
    final NDSAdminJob preExistingAdminJob =
        _ndsAdminJobDao.create(new ObjectId(), groupId, clusterName, hostname1, type);
    try {
      _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
          groupId, clusterName, hostname0, 2, AuditInfoHelpers.fromSystem());
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.ADMIN_JOB_OF_TYPE_IN_PROGRESS_FOR_CLUSTER, pE.getErrorCode());
    }

    // ReplicaSet hardware not found - Fail
    _ndsAdminJobDao.updateJobStatus(preExistingAdminJob.getId(), NDSAdminJob.Status.COMPLETE, null);
    try {
      _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
          groupId, clusterName, hostname0, 2, AuditInfoHelpers.fromSystem());
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
    }

    // ReplicaSetHardware exists, no instance matching on hostname - Fail
    _replicaSetHardwareDao.saveReplicaSafe(replicaSetHardwareDoc);
    try {
      _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
          groupId,
          clusterName,
          "somehostnamethat.does.not.exist",
          2,
          AuditInfoHelpers.fromSystem());
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
    }

    final List<NDSAdminJob> workingAdminJobsBeforeSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(hostname0, type, NDSAdminJob.Status.WORKING);
    assertEquals(0, workingAdminJobsBeforeSubmit.size());

    final List<BasicDBObject> plansBeforeSubmit = _planDao.findByGroupId(groupId);
    assertEquals(0, plansBeforeSubmit.size());

    final List<Event> eventsBeforeSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.UPDATE_BUMPER_FILES);
    assertEquals(0, eventsBeforeSubmit.size());

    // Cluster description exists - Success
    _clusterDescriptionDao.save(clusterDescription);
    _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
        groupId, clusterName, hostname0, 2, AuditInfoHelpers.fromSystem());
    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(hostname0, type, NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    final List<Event> eventsAfterSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.UPDATE_BUMPER_FILES);
    assertEquals(1, eventsAfterSubmit.size());
    assertTrue(eventsAfterSubmit.get(0).isHidden());
  }

  @Test
  public void testSubmitUpdateBumperFileAdminJob_UpdateBumperFiles_SearchInstance()
      throws Exception {
    final ObjectId groupId = _ndsGroup.getGroupId();
    final String clusterName = "cluster0";
    final NDSAdminJob.Type type = Type.UPDATE_BUMPER_FILES;

    final ClusterDescription clusterDescription =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName));
    _clusterDescriptionDao.save(clusterDescription);
    final String hostname = "a1-shard-121-search-abc1.jkyr.mesh.xyz";

    /// pre-conditions
    final List<NDSAdminJob> workingAdminJobsBeforeSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(hostname, type, NDSAdminJob.Status.WORKING);
    assertEquals(0, workingAdminJobsBeforeSubmit.size());
    final List<BasicDBObject> plansBeforeSubmit = _planDao.findByGroupId(groupId);
    assertEquals(0, plansBeforeSubmit.size());
    final List<Event> eventsBeforeSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.UPDATE_BUMPER_FILES);
    assertEquals(0, eventsBeforeSubmit.size());

    // Search node not found - Fail
    try {
      _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
          groupId, clusterName, hostname, 2, AuditInfoHelpers.fromSystem());
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
    }

    // add search node - Success
    Hostnames hostnames = new Hostnames(hostname);
    SearchInstance searchInstance =
        SearchPhysicalModelTestFactory.getProvisionedSearchInstance(
            new ObjectId(),
            0,
            new Date(),
            AWSSearchInstanceSize.S20_HIGHCPU_NVME,
            Optional.of(hostnames),
            Optional.empty());
    PartitionGroup partitionGroup =
        SearchPhysicalModelTestFactory.getPartitionGroup(
            new ObjectId(),
            _ndsGroup.getGroupId(),
            new ObjectId(),
            new ObjectId(),
            List.of(searchInstance));
    _partitionGroupDao.create(partitionGroup);

    _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
        groupId, clusterName, hostname, 2, AuditInfoHelpers.fromSystem());

    // validate job, plan, and audit were created
    final List<NDSAdminJob> workingAdminJobsAfterSubmit =
        _ndsAdminJobDao.findByHostnameTypeAndStatus(hostname, type, NDSAdminJob.Status.WORKING);
    assertEquals(1, workingAdminJobsAfterSubmit.size());

    final List<BasicDBObject> plansAfterSubmit = _planDao.findByGroupId(groupId);
    assertEquals(1, plansAfterSubmit.size());

    final List<Event> eventsAfterSubmit =
        _eventDao.findByEventTypeForGroup(groupId, NDSAudit.Type.UPDATE_BUMPER_FILES);
    assertEquals(1, eventsAfterSubmit.size());
    assertTrue(eventsAfterSubmit.get(0).isHidden());
  }
}
