package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

import com.amazonaws.services.ec2.model.VolumeType;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.inttestimpls.SimpleContextFactory;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Plan.PlanResult;
import com.xgen.module.common.planner.model.Result.PlanFailureCode;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CpsSnapshotStatusCleanupSvcIntTests extends JUnit5BaseSvcTest {
  private static final ObjectId GROUP_ID = oid(118);
  private static final ObjectId CLUSTER_UNIQUE_ID = oid(1);
  private static final ObjectId POLICY_ITEM_ID = oid(234);
  private static final String CLUSTER_NAME = "rs1";
  @Inject private CpsSnapshotStatusCleanupSvc _cpsSnapshotStatusCleanupSvc;
  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private PlanDao _planDao;
  @Inject private SimpleContextFactory _simpleContextFactory;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/host/HostClusterDao/hostClusters.json.ftl",
        null,
        HostCluster.DB_NAME,
        HostCluster.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/host/HostDao/hosts.json.ftl", null, Host.DB_NAME, Host.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, NDSGroupDao.DB, NDSGroupDao.COLLECTION);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupJobDao/jobs.json.ftl", null, "nds", "config.nds.backup.jobs");
  }

  @Test
  public void testCleanupSnapshotStatusCleanup() {
    final Plan planInProgress =
        new Plan(GROUP_ID, (gid, pid) -> _simpleContextFactory.create(gid, pid));
    final Plan planFailed =
        new Plan(GROUP_ID, (gid, pid) -> _simpleContextFactory.create(gid, pid));
    final Plan planAbandoned =
        new Plan(GROUP_ID, (gid, pid) -> _simpleContextFactory.create(gid, pid));
    final Plan planCompleted =
        new Plan(GROUP_ID, (gid, pid) -> _simpleContextFactory.create(gid, pid));

    _planDao.save(planInProgress);
    _planDao.save(planFailed);
    _planDao.save(planAbandoned);
    _planDao.save(planCompleted);

    _planDao.updateResult(planInProgress.getId(), PlanResult.IN_PROGRESS, PlanFailureCode.NONE);
    _planDao.abandonPlan(planAbandoned.getId());
    _planDao.updateResult(planFailed.getId(), PlanResult.FAILED, PlanFailureCode.NOT_FOUND);
    _planDao.updateResult(planCompleted.getId(), PlanResult.SUCCESS, PlanFailureCode.NONE);

    final Date now = new Date();
    final Date oneYearAgo = DateUtils.addDays(now, -366);
    final Date oneDayAgo = DateUtils.addDays(now, -1);
    final Date twoHoursAgo = DateUtils.addHours(now, -2);
    final Date threeHoursAgo = DateUtils.addHours(now, -3);
    final Date fourHoursAgo = DateUtils.addHours(now, -4);
    final Date fiveHoursAgo = DateUtils.addHours(now, -5);
    final Date oneMinuteAgo = DateUtils.addMinutes(now, -10);

    // in progress snapshot but outside the cleanup window, won't update status
    final SnapshotUpdate snapshot1yearFailed =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(oneYearAgo), planFailed.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot1yearFailed);

    final SnapshotUpdate snapshot1dayInProgress =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(oneDayAgo), planInProgress.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot1dayInProgress);

    final SnapshotUpdate snapshot5hourInProgress =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(fiveHoursAgo), planInProgress.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot5hourInProgress);

    final SnapshotUpdate snapshot4hourFailed =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(fourHoursAgo), planFailed.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot4hourFailed);

    final SnapshotUpdate snapshot3hourCompleted =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(threeHoursAgo), planCompleted.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot3hourCompleted);

    final SnapshotUpdate snapshot2hourAbandoned =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(twoHoursAgo), planAbandoned.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot2hourAbandoned);

    // snapshot within the grace period, will not update the status
    final SnapshotUpdate snapshot1minuteCompleted =
        buildDefaultSnapshot(DriverUtils.newObjectIdFromDate(oneMinuteAgo), planCompleted.getId());
    _backupSnapshotDao.addBackupSnapshot(snapshot1minuteCompleted);

    _cpsSnapshotStatusCleanupSvc.cleanUpSnapshotStatus();

    _backupSnapshotDao
        .findById(snapshot1yearFailed.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.IN_PROGRESS, snapshot.getSnapshotStatus()),
            () -> fail("snapshot1yearFailed not found"));
    _backupSnapshotDao
        .findById(snapshot1dayInProgress.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.IN_PROGRESS, snapshot.getSnapshotStatus()),
            () -> fail("snapshot1dayInProgress not found"));
    _backupSnapshotDao
        .findById(snapshot5hourInProgress.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.IN_PROGRESS, snapshot.getSnapshotStatus()),
            () -> fail("snapshot5hourInProgress not found"));
    _backupSnapshotDao
        .findById(snapshot4hourFailed.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.FAILED, snapshot.getSnapshotStatus()),
            () -> fail("snapshot4hourFailed not found"));
    _backupSnapshotDao
        .findById(snapshot3hourCompleted.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.COMPLETED, snapshot.getSnapshotStatus()),
            () -> fail("snapshot3hourCompleted not found"));
    _backupSnapshotDao
        .findById(snapshot2hourAbandoned.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.FAILED, snapshot.getSnapshotStatus()),
            () -> fail("snapshot2hourAbandoned not found"));
    _backupSnapshotDao
        .findById(snapshot1minuteCompleted.getId())
        .ifPresentOrElse(
            snapshot -> assertEquals(Status.IN_PROGRESS, snapshot.getSnapshotStatus()),
            () -> fail("snapshot1minuteCompleted not found"));
  }

  private SnapshotUpdate buildDefaultSnapshot(final ObjectId snapshotId, final ObjectId planId) {

    final SnapshotUpdate.AwsSnapshotFieldBuilder awsBuilder =
        new SnapshotUpdate.AwsSnapshotFieldBuilder();
    final ObjectId awsAccountId = new ObjectId();
    final ObjectId awsContainerId = new ObjectId();
    final String ebsVolumeId = "ebsVolumeId-1";
    final String ebsSnapshotDesc = AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId);
    final SnapshotUpdate snapshotUpdate = new SnapshotUpdate();
    snapshotUpdate
        .setId(snapshotId)
        .setProjectId(GROUP_ID)
        .setShard(false)
        .setPlanId(planId)
        .setClusterName(CLUSTER_NAME)
        .setClusterUniqueId(CLUSTER_UNIQUE_ID)
        .setStatus(Status.IN_PROGRESS)
        .setCloudProviders(List.of(CloudProvider.AWS))
        .setType(BackupSnapshot.Type.SCHEDULED)
        .setAwsSnapshotField(
            awsBuilder
                .withEbsSnapshotDescription(ebsSnapshotDesc)
                .withAWSAccountId(awsAccountId)
                .withAWSContainerId(awsContainerId)
                .withAWSSubnetId("awsSubnetId-1")
                .withEbsVolumeId(ebsVolumeId)
                .withEbsVolumeType(VolumeType.Io1.name())
                .withIsEbsVolumeEncrypted(true)
                .withEbsVolumeSize(AWSNDSInstanceSize.M30.getDefaultDiskSizeGB())
                .withEbsDiskIOPS(AWSNDSInstanceSize.M30.getMaxEBSStandardIOPS())
                .withRegionName(AWSRegionName.US_EAST_1.getName()))
        .setPolicyItemIds(List.of(POLICY_ITEM_ID))
        .setFrequencyType(BackupFrequencyType.DAILY)
        .setOverrideRetentionPolicy(false)
        .setBackupRetentionUnit(BackupRetentionUnit.DAYS);
    return snapshotUpdate;
  }
}
