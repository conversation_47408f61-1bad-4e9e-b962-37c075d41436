package com.xgen.svc.nds.svc.cps;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobQueueDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.ClusterType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.DiskBackupState;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.pit._private.dao.CpsRegionalMetadataStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsOplogSlice;
import com.xgen.cloud.cps.pit._public.model.CpsRegionalMetadataStoreConfig;
import com.xgen.cloud.cps.pit._public.model.OplogMigration;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.nds.dao.CpsRegionalMetadataStoreMongoSvc;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CpsOplogExpirationSvcIntTests extends JUnit5NDSBaseTest {

  @Inject private BackupJobDao _backupJobDao;
  @Inject private BackupJobQueueDao _backupJobQueueDao;
  @Inject private BackupSnapshotDao _backupSnapshotDao;

  @Inject private CpsSvc _cpsSvc;
  @Inject private CpsPitSvc _cpsPitSvc;
  @Inject private CpsPolicySvc _cpsPolicySvc;
  @Inject private CpsOplogExpirationSvc _cpsOplogExpirationSvc;
  private CpsOplogExpirationSvc _spiedCpsOplogExpirationSvc;

  private static final AWSRegionName DEFAULT_REGION = AWSRegionName.US_EAST_1;
  private static final AWSRegionName REGION_2 = AWSRegionName.US_WEST_1;

  private static final String COMPRESSOR = "snappy";
  private static final String MONGOD_VERSION = "3.4.0";
  private static final int DEFAULT_COUNT = 2;
  private static final int DEFAULT_PROCESSED_SIZE = 12345;
  private static final int DEFAULT_RAW_SIZE = 67890;
  private static final CloudProvider DEFAULT_PROVIDER = CloudProvider.AWS;
  private static final ObjectId GROUP_ID = new ObjectId("5c38fdc7df72661fe212e5f4");
  private static final String STORE_ID = "s3oplog_storeid";
  private static final String BRS_STORE_ID = "oplog_storeid";

  private static final String CLUSTER_NAME = "MyCluster";
  private static final ObjectId CLUSTER_UNIQUE_ID = new ObjectId();
  private static final String RS_ID = "MyCluster-shard-01";
  private static final Date SCHEDULED_CREATION_DATE =
      new Date(System.currentTimeMillis() - Duration.ofMinutes(1).toMillis());
  private static final Date SCHEDULED_DELETION_DATE =
      new Date(System.currentTimeMillis() + Duration.ofDays(3).toMillis());
  private static final Date SNAPSHOT_INITIATION_DATE =
      new Date(System.currentTimeMillis() - Duration.ofMinutes(1).toMillis());
  private static final long USED_SPACE = 1L << 30; // 1GB
  private static final BackupSnapshotEncryptionCredentials AWS_ENCRYPT_CREDENTIALS =
      new AWSBackupSnapshotEncryptionCredentials(null, null, null, null, null, false);

  @Inject private CpsRegionalMetadataStoreConfigDao _cpsRegionalMetadataStoreConfigDao;

  @Inject private CpsRegionalMetadataStoreMongoSvc _cpsRegionalMetadataStoreMongoSvc;

  private CpsOplogSliceMetadataDaoProxy _oplogSliceMetadataDao;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    // Clean start
    _cpsRegionalMetadataStoreMongoSvc.close();

    _cpsRegionalMetadataStoreConfigDao.insertForTests(
        new CpsRegionalMetadataStoreConfig(
            STORE_ID,
            BRS_STORE_ID,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            true,
            false,
            false,
            null,
            "fake_deployment_id",
            0));

    _oplogSliceMetadataDao =
        spy(
            CpsOplogSliceMetadataDaoProxy.get(
                _cpsRegionalMetadataStoreMongoSvc,
                STORE_ID,
                GROUP_ID.toHexString() + "_oplog_metadata_test",
                WriteConcern.JOURNALED,
                "coll_name"));

    // clear if it exists
    _oplogSliceMetadataDao.getDbCollection().drop();

    final CpsOplogStoreFactory cpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(cpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), any()))
        .thenReturn(_oplogSliceMetadataDao);
    when(cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(any(BackupJob.class), any()))
        .thenReturn(_oplogSliceMetadataDao);
    when(cpsOplogStoreFactory.getCpsOplogMigrationStorageRegionSliceDaoProxy(
            any(BackupJob.class), any()))
        .thenReturn(_oplogSliceMetadataDao);

    CpsPitSvc spiedCpsPitSvc = spy(_cpsPitSvc);
    doReturn(cpsOplogStoreFactory).when(spiedCpsPitSvc).getCpsOplogStoreFactory();

    CpsSvc spiedCpsSvc = spy(_cpsSvc);
    doReturn(spiedCpsPitSvc).when(spiedCpsSvc).getCpsPitSvc();

    _spiedCpsOplogExpirationSvc = spy(_cpsOplogExpirationSvc);
    doReturn(spiedCpsSvc).when(_spiedCpsOplogExpirationSvc).getCpsSvc();
    doReturn(cpsOplogStoreFactory).when(_spiedCpsOplogExpirationSvc).getCpsOplogStoreFactory();
  }

  @AfterEach
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    // Prevent test leaking MongoClients
    _cpsRegionalMetadataStoreMongoSvc.close();
  }

  @Test
  public void testExpireSlicesInner1() {
    final int pitWindowDays = 2;
    final ObjectId savedJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            GROUP_ID,
            CLUSTER_NAME,
            CLUSTER_UNIQUE_ID,
            BackupJob.ClusterType.REPLICA_SET,
            pitWindowDays,
            false,
            false);

    final Map<String, PitSetting> rsIdToPitSettings = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(new PitStorage(STORE_ID, "blobstore-dummy", REGION_2.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings.put(RS_ID, pitSetting);

    final BackupJob pitDisabledJob = _backupJobDao.find(savedJobId).get();

    _backupJobDao.enablePitIfDisabled(pitDisabledJob.getId());
    _backupJobDao.updatePitSettings(
        pitDisabledJob.getId(), rsIdToPitSettings, pitDisabledJob.getVersion() + 1);

    final BackupJob backupJob = _backupJobDao.find(savedJobId).get();
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017 00:00
    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1498953600, 1))); // 7/2/2017 00:00
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1)));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867202, 1))
                .setEndTimestamp(new BSONTimestamp(1498867205, 1)));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498953500, 1)));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498953500, 1))
                .setEndTimestamp(new BSONTimestamp(1498953610, 1)));
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Case 1 Setup:

    snap1 - not PIT restoreable (cuz out of restore window)
    slice1 -
    slice2 -
    slice3 -
    snap2 - PIT restoreable
    slice4 -

    Expected: invalidate slice[1-3]

    */
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-03T06:34:56Z"),
        backupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);
    List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        new HashSet<>(Arrays.asList(slice1.getId(), slice2.getId(), slice3.getId())),
        invalidSliceIds);

    _oplogSliceMetadataDao.remove(new BasicDBObject());
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Case 2 Setup:

    snap1 - not PIT restoreable (cuz out of restore window)
    slice1
    slice2
    slice3
    snap2 - not PIT restoreable (cuz out of restore window)
    slice4

    Expected: invalidate slice[1-4]

    */
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-08-23T06:34:56Z"),
        backupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);
    invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    invalidSliceIds = invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        new HashSet<>(
            Arrays.asList(slice1.getId(), slice2.getId(), slice3.getId(), slice4.getId())),
        invalidSliceIds);

    _oplogSliceMetadataDao.remove(new BasicDBObject());
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Case 3 Setup:

    snap1 - PIT restoreable
    slice1
    slice2
    slice3
    snap2 - PIT restoreable
    slice4

    Expected: nothing to invalidate

    */
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-02T01:34:56Z"),
        backupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);
    invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    invalidSliceIds = invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(0, invalidSliceIds.size());
  }

  @Test
  public void testexpireSlicesInner2() {
    final int pitWindowDays = 2;
    final ObjectId savedJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            GROUP_ID,
            CLUSTER_NAME,
            CLUSTER_UNIQUE_ID,
            BackupJob.ClusterType.REPLICA_SET,
            pitWindowDays,
            false,
            false);

    final Map<String, PitSetting> rsIdToPitSettings = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(new PitStorage(STORE_ID, "blobstore-dummy", REGION_2.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings.put(RS_ID, pitSetting);

    final BackupJob pitDisabledJob = _backupJobDao.find(savedJobId).get();

    _backupJobDao.enablePitIfDisabled(pitDisabledJob.getId());
    _backupJobDao.updatePitSettings(
        pitDisabledJob.getId(), rsIdToPitSettings, pitDisabledJob.getVersion());

    final BackupJob backupJob = _backupJobDao.find(savedJobId).get();
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017
    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1498953600, 1))); // 7/2/2017
    final ObjectId snap3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1499040000, 1))); // 7/3/2017
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867300, 1)));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867300, 1))
                .setEndTimestamp(new BSONTimestamp(1498867400, 1)));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867400, 1))
                .setEndTimestamp(new BSONTimestamp(1498953600, 1)));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498953600, 1))
                .setEndTimestamp(new BSONTimestamp(1499212800, 1))); // 7/5/2017
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Case 1 Setup:

    snap1 - not PIT restoreable (cuz out of restore window)
    slice1
    slice2
    slice3 - (end timestamp intersects with snap2 pitsentinel optime)
    snap2 - PIT restoreable
    slice4
    snap3 - PIT restoreable

    Expected: invalidate slice[1-2]

    */
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-03T06:34:56Z"),
        backupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);
    List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        new HashSet<>(Arrays.asList(slice1.getId(), slice2.getId())), invalidSliceIds);

    _oplogSliceMetadataDao.remove(new BasicDBObject());
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Case 2 Setup:

    snap1 - not PIT restoreable (cuz out of restore window)
    slice1
    slice2
    slice3 - (end timestamp intersects with snap2 pitsentinel optime)
    snap2 - not PIT restoreable (cuz out of restore window)
    slice4
    snap3 - PIT restoreable

    Expected: invalidate slice[1-3]. We don't invalidate slice4 because it's end date is not before "now"

    */
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-04T06:34:56Z"),
        backupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);
    invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    invalidSliceIds = invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        new HashSet<>(Arrays.asList(slice1.getId(), slice2.getId(), slice3.getId())),
        invalidSliceIds);
  }

  @Test
  public void testexpireSlicesInner3() {
    final int pitWindowDays = 3;

    final ObjectId savedJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            GROUP_ID,
            CLUSTER_NAME,
            CLUSTER_UNIQUE_ID,
            BackupJob.ClusterType.REPLICA_SET,
            pitWindowDays,
            false,
            false);

    final Map<String, PitSetting> rsIdToPitSettings = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(new PitStorage(STORE_ID, "blobstore-dummy", REGION_2.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings.put(RS_ID, pitSetting);

    final BackupJob pitDisabledJob = _backupJobDao.find(savedJobId).get();

    _backupJobDao.enablePitIfDisabled(pitDisabledJob.getId());
    _backupJobDao.updatePitSettings(
        pitDisabledJob.getId(), rsIdToPitSettings, pitDisabledJob.getVersion() + 1);

    final BackupJob backupJob = _backupJobDao.find(savedJobId).get();
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(true)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017
    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1498953600, 1))); // 7/2/2017
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1)));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867202, 1))
                .setEndTimestamp(new BSONTimestamp(1498867300, 1)));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867300, 1))
                .setEndTimestamp(new BSONTimestamp(1498867400, 1)));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867400, 1))
                .setEndTimestamp(new BSONTimestamp(1498953700, 1)));
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Setup: This can happen if user reduces restore window in the middle of tail. Even though slice[1-3] are valid,
    we don't want to create "holes" in our oplog slice window

    snap1 - not PIT restoreable (within restore window, but purged)
    slice1
    slice2
    slice3
    snap2 - PIT restoreable
    slice4

    Expected: invalidate slice[1-3]

    */
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-03T06:34:56Z"),
        backupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);
    final List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        new HashSet<>(Arrays.asList(slice1.getId(), slice2.getId(), slice3.getId())),
        invalidSliceIds);
  }

  @Test
  public void testexpireSlicesInnerWithExtendedOplogWindow() {
    final double pitWindowDays = 2;

    final PolicyItem dailyPolicyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.DAILY, 1, Duration.ZERO, BackupRetentionUnit.DAYS);
    final PolicyItem weeklyPolicyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.WEEKLY, 1, Duration.ZERO, BackupRetentionUnit.DAYS);
    final PolicyItem hourlyPolicyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.HOURLY, 6, Duration.ZERO, BackupRetentionUnit.DAYS);
    final Policy backupPolicy =
        new Policy(
            new ObjectId(), Arrays.asList(hourlyPolicyItem, dailyPolicyItem, weeklyPolicyItem));
    final Map<String, PitSetting> rsIdToPitSettings = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder().regionName(AWSRegionName.US_EAST_1.getValue()).build();
    rsIdToPitSettings.put(RS_ID, pitSetting);

    final BackupJob backupJob =
        BackupJob.builder()
            .id(oid(20))
            .projectId(GROUP_ID)
            .clusterName(CLUSTER_NAME)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterType(ClusterType.REPLICA_SET)
            .restoreWindowDays(pitWindowDays)
            .pitEnabled(true)
            .policies(Arrays.asList(backupPolicy))
            .diskBackupState(DiskBackupState.ACTIVE)
            .pitSettings(rsIdToPitSettings)
            .build();
    _backupJobDao.saveMajority(BackupJobDao.toDbObj(backupJob));

    final ObjectId snapshot1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1675987200, 1))); // 2/10/2023 00:00
    final ObjectId snapshot2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1676008800, 1))); // 2/10/2023 06:00

    // 02/09/2023 23:57 - 02/09/2023 23:59
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1675987020, 1))
                .setEndTimestamp(new BSONTimestamp(1675987140, 1)));

    // 02/09/2023 23:59 - 02/10/2023 00:01
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1675987140, 1))
                .setEndTimestamp(new BSONTimestamp(1675987260, 1)));

    // 02/10/2023 00:01 - 02/10/2023 00:03
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1675987260, 1))
                .setEndTimestamp(new BSONTimestamp(1675987380, 1)));

    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);

    BackupJob savedBackupJob = _backupJobDao.find(oid(20)).get();

    // With 2 days of PIT window, at 02/12/2023 03:00, slices before 02/10/2023 03:00 should be
    // considered to purge. However since there's a snapshot at 02/10/2023 00:00 and another one at
    // 02/10/2023 06:00, we should keep all slices after 02/10/2023 00:00 to guarantee the full 2
    // days PIT restore window
    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2023-02-12T03:00:00Z"),
        savedBackupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);

    final List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(new HashSet<>(Arrays.asList(slice1.getId())), invalidSliceIds);
  }

  @Test
  public void testExpireSlicesForBackupLock() {
    final int pitWindowDays = 3;

    final ObjectId savedJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            GROUP_ID,
            CLUSTER_NAME,
            CLUSTER_UNIQUE_ID,
            BackupJob.ClusterType.REPLICA_SET,
            pitWindowDays,
            false,
            true);

    final Map<String, PitSetting> rsIdToPitSettings = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(new PitStorage(STORE_ID, "blobstore-dummy", REGION_2.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings.put(RS_ID, pitSetting);

    BackupJob backupJob = _backupJobDao.find(savedJobId).get();

    _backupJobDao.enablePitIfDisabled(backupJob.getId());
    _backupJobDao.updatePitSettings(
        backupJob.getId(), rsIdToPitSettings, backupJob.getVersion() + 1);

    final BackupJob spyBackupJob = spy(backupJob);
    doReturn(true).when(spyBackupJob).shouldRetainBackups();
    doReturn(true).when(spyBackupJob).isRetaining();

    /*
    Case 1: Backup Job has RETAINING state. No active snapshots exist, all slices are invalidated
    */
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867222, 1))
                .setEndTimestamp(new BSONTimestamp(1498867333, 1)));

    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(**********, 1)));

    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);

    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-03T06:34:56Z"),
        spyBackupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);

    List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());

    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(Set.of(slice1.getId(), slice2.getId()), invalidSliceIds);

    /*
    Case 2: Backup Job has RETAINING state. Expire slice[3~5] which is earlier than the oldest nonResilient snapshot
    snap1 Expected: slice[3~5] invalidated in this case + slice[1~2] invalidated in case 1
    */
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(true)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017

    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1498953600, 1))); // 7/2/2017

    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867100, 1))
                .setEndTimestamp(new BSONTimestamp(1498867105, 1)));

    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867111, 1))
                .setEndTimestamp(new BSONTimestamp(1498867115, 1)));
    final CpsOplogSlice slice5 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498867120, 1))
                .setEndTimestamp(new BSONTimestamp(1498867125, 1)));
    final CpsOplogSlice slice6 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setStartTimestamp(new BSONTimestamp(1498953600, 1))
                .setEndTimestamp(new BSONTimestamp(1498953800, 1)));

    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);
    _oplogSliceMetadataDao.addMetadata(slice5);
    _oplogSliceMetadataDao.addMetadata(slice6);

    _spiedCpsOplogExpirationSvc.expireSlicesInner(
        TimeUtils.fromISOString("2017-07-03T06:34:56Z"),
        spyBackupJob,
        RS_ID,
        _oplogSliceMetadataDao,
        pitWindowDays);

    invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, DEFAULT_REGION.getValue());

    invalidSliceIds = invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        Set.of(slice1.getId(), slice2.getId(), slice3.getId(), slice4.getId(), slice5.getId()),
        invalidSliceIds);
  }

  @Test
  public void testexpireSlices_ActiveAndQueuedBackupJob() {
    final int pitWindowDaysActive = 3;
    final int pitWindowDaysInQueue = 1;
    final PitStorage ps1 =
        new PitStorage(STORE_ID, "blobstore-dummy", AWSRegionName.US_WEST_1.getValue());
    final PitStorage ps1_copy =
        new PitStorage(STORE_ID, "blobstore-dummy", AWSRegionName.US_WEST_1.getValue());
    final PitStorage ps2 =
        new PitStorage(STORE_ID, "blobstore-dummy", AWSRegionName.US_EAST_2.getValue());

    final PolicyItem policyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.DAILY, 1, Duration.ZERO, BackupRetentionUnit.DAYS);
    final Policy basicPolicy = new Policy(new ObjectId(), Arrays.asList(policyItem));
    final Map<String, PitSetting> rsIdToPitSettings1 = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(new OplogMigration(List.of(ps1), new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings1.put(RS_ID, pitSetting);

    final BackupJob backupJob1 =
        BackupJob.builder()
            .id(oid(10))
            .projectId(GROUP_ID)
            .clusterName(CLUSTER_NAME)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterType(ClusterType.REPLICA_SET)
            .restoreWindowDays(pitWindowDaysActive)
            .pitEnabled(true)
            .policies(Arrays.asList(basicPolicy))
            .diskBackupState(DiskBackupState.ACTIVE)
            .pitSettings(rsIdToPitSettings1)
            .build();

    _backupJobDao.saveMajority(BackupJobDao.toDbObj(backupJob1));

    final Map<String, PitSetting> rsIdToPitSettings2 = new HashMap<>();

    final PitSetting pitSetting2 =
        PitSetting.builder()
            .oplogMigration(new OplogMigration(List.of(ps2), new Date()))
            .regionalMetadataStoreConfigId(STORE_ID)
            .blobStoreConfigId("blobstore-dummy")
            .regionName(AWSRegionName.US_WEST_1.getValue())
            .build();

    rsIdToPitSettings2.put(RS_ID, pitSetting2);

    BackupJob backupJob2 =
        BackupJob.builder()
            .id(oid(20))
            .projectId(GROUP_ID)
            .clusterName(CLUSTER_NAME)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterType(ClusterType.REPLICA_SET)
            .restoreWindowDays(pitWindowDaysInQueue)
            .pitEnabled(true)
            .policies(Arrays.asList(basicPolicy))
            .diskBackupState(DiskBackupState.ACTIVE)
            .pitSettings(rsIdToPitSettings2)
            .build();

    _backupJobQueueDao.saveMajority(BackupJobDao.toDbObj(backupJob2));

    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setId(oid(100))
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017
    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setId(oid(200))
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1498953600, 1))); // 7/2/2017
    final CpsOplogSlice slice0 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(0))
                .setStartTimestamp(new BSONTimestamp(1498867160, 1))
                .setEndTimestamp(new BSONTimestamp(1498867180, 1))
                .setRegionName(AWSRegionName.US_EAST_2)
                .setStorageRegionName(AWSRegionName.US_EAST_2));
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(1))
                .setStartTimestamp(new BSONTimestamp(1498867180, 1))
                .setEndTimestamp(new BSONTimestamp(**********, 1))
                .setRegionName(AWSRegionName.US_EAST_2)
                .setStorageRegionName(AWSRegionName.US_EAST_2));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(2))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_WEST_1)
                .setStorageRegionName(AWSRegionName.US_WEST_1));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(3))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(4))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_2)
                .setStorageRegionName(AWSRegionName.US_EAST_2));
    final CpsOplogSlice slice5 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(5))
                .setStartTimestamp(new BSONTimestamp(1498867202, 1))
                .setEndTimestamp(new BSONTimestamp(1498867204, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice6 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(6))
                .setStartTimestamp(new BSONTimestamp(1498867204, 1))
                .setEndTimestamp(new BSONTimestamp(1498867300, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice7 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(7))
                .setStartTimestamp(new BSONTimestamp(1498867300, 1))
                .setEndTimestamp(new BSONTimestamp(1498867400, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice8 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(8))
                .setStartTimestamp(new BSONTimestamp(1498867400, 1))
                .setEndTimestamp(new BSONTimestamp(1498953700, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    _oplogSliceMetadataDao.addMetadata(slice0);
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);
    _oplogSliceMetadataDao.addMetadata(slice5);
    _oplogSliceMetadataDao.addMetadata(slice6);
    _oplogSliceMetadataDao.addMetadata(slice7);
    _oplogSliceMetadataDao.addMetadata(slice8);

    /*-

    Setup:

    slice0 US_EAST_2
    slice1 US_EAST_2
    snap1 - PIT restoreable (within restore window)
    slice2 US_WEST_1
    slice3 US_EAST_1
    slice4 US_EAST_2
    slice5 US_EAST_1
    slice6 US_EAST_1
    slice7 US_EAST_1
    snap2 - PIT restoreable
    slice8 US_EAST_1

    active backupJob
    - pit window 3 days
    - regions primary: US_EAST_1 and migration: US_WEST_1

    queued backupJob
    - pit window 1 day
    - regions primary: US_WEST_1 and migration: US_EAST_2

    Expected:
      invalidate slice[0] - because it's there's no snapshot before it
      keep slice[1-2,4] - even though these slices belong to regions of backup job queue, with restore window of 1 day,
        we use the active job's restore window instead
      keep slice[3,5-8] - we keep due to active job's restore window
    */

    doReturn(TimeUtils.fromISOString("2017-07-03T06:34:56Z"))
        .when(_spiedCpsOplogExpirationSvc)
        .getNow();

    _spiedCpsOplogExpirationSvc.expireOplogSlices();

    final List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, AWSRegionName.US_EAST_2.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(new HashSet<>(Arrays.asList(slice0.getId())), invalidSliceIds);
    Assert.assertEquals(
        0,
        _oplogSliceMetadataDao
            .getInvalidUnpurgedSlices(RS_ID, CloudProvider.AWS, AWSRegionName.US_WEST_1.getValue())
            .size());
    Assert.assertEquals(
        0,
        _oplogSliceMetadataDao
            .getInvalidUnpurgedSlices(RS_ID, CloudProvider.AWS, AWSRegionName.US_EAST_1.getValue())
            .size());

    // Verify expireSlicesInnerForRegion in queued backupJob only get called once since ps1_copy
    // has a match in the active backup job and will be skipped
    verify(_spiedCpsOplogExpirationSvc, times(2))
        .expireSlicesInner(
            any(Date.class),
            any(BackupJob.class),
            anyString(),
            any(CpsOplogSliceMetadataDaoProxy.class),
            anyDouble());
  }

  /** Here we test for the case where oplog migration slice is behind the primary slices */
  @Test
  public void testexpireSlices_ActiveBackupJob() {
    final int pitWindowDaysActive = 3;

    final PolicyItem policyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.DAILY, 1, Duration.ZERO, BackupRetentionUnit.DAYS);
    final Policy basicPolicy = new Policy(new ObjectId(), Arrays.asList(policyItem));
    final Map<String, PitSetting> rsIdToPitSettings1 = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(
                        new PitStorage(
                            STORE_ID, "blobstore-dummy", AWSRegionName.US_WEST_1.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings1.put(RS_ID, pitSetting);

    final BackupJob backupJob1 =
        BackupJob.builder()
            .id(oid(10))
            .projectId(GROUP_ID)
            .clusterName(CLUSTER_NAME)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterType(ClusterType.REPLICA_SET)
            .restoreWindowDays(pitWindowDaysActive)
            .pitEnabled(true)
            .policies(Arrays.asList(basicPolicy))
            .diskBackupState(DiskBackupState.ACTIVE)
            .pitSettings(rsIdToPitSettings1)
            .build();

    _backupJobDao.saveMajority(BackupJobDao.toDbObj(backupJob1));

    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setId(oid(100))
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017
    final CpsOplogSlice slice0 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(0))
                .setStartTimestamp(new BSONTimestamp(1498780100, 1))
                .setEndTimestamp(new BSONTimestamp(1498780200, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(1))
                .setStartTimestamp(new BSONTimestamp(1498780200, 1))
                .setEndTimestamp(new BSONTimestamp(**********, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(2))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(3))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(4))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice5 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(5))
                .setStartTimestamp(new BSONTimestamp(1498867202, 1))
                .setEndTimestamp(new BSONTimestamp(1498867204, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    _oplogSliceMetadataDao.addMetadata(slice0);
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);
    _oplogSliceMetadataDao.addMetadata(slice5);

    /*-

    Setup:

    slice0 US_EAST_1
    slice1 US_EAST_1
    snap1 - PIT restoreable for US_EAST_1, also within restore window
    slice2 US_EAST_1
    slice3 US_EAST_1
    slice4 US_WEST_1
    slice5 US_EAST_1

    active backupJob
    - pit window 3 days
    - regions primary: US_EAST_1 and migration: US_WEST_1

    Expected:
      invalidate slice[0] - because it's there's no snapshot before it
      keep slice[1-3,5] - keep because there's snapshot to use with
      keep slice[4] - this is a migration slice but don't expire since it is still within restore window
    */

    doReturn(TimeUtils.fromISOString("2017-07-03T06:34:56Z"))
        .when(_spiedCpsOplogExpirationSvc)
        .getNow();

    _spiedCpsOplogExpirationSvc.expireOplogSlices();

    final List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, AWSRegionName.US_EAST_1.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(new HashSet<>(Arrays.asList(slice0.getId())), invalidSliceIds);
    Assert.assertEquals(
        0,
        _oplogSliceMetadataDao
            .getInvalidUnpurgedSlices(RS_ID, CloudProvider.AWS, AWSRegionName.US_WEST_1.getValue())
            .size());
  }

  /**
   * Here we test for the case where we have to expire some slices on both oplog migration and
   * primary region
   */
  @Test
  public void testexpireSlices_ActiveBackupJob_ExpireBoth() {
    final int pitWindowDaysActive = 3;

    final PolicyItem policyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.DAILY, 1, Duration.ZERO, BackupRetentionUnit.DAYS);
    final Policy basicPolicy = new Policy(new ObjectId(), Arrays.asList(policyItem));
    final Map<String, PitSetting> rsIdToPitSettings1 = new HashMap<>();

    final PitSetting pitSetting =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(
                        new PitStorage(
                            STORE_ID, "blobstore-dummy", AWSRegionName.US_WEST_1.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_EAST_1.getValue())
            .build();

    rsIdToPitSettings1.put(RS_ID, pitSetting);

    final BackupJob backupJob1 =
        BackupJob.builder()
            .id(oid(10))
            .projectId(GROUP_ID)
            .clusterName(CLUSTER_NAME)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterType(ClusterType.REPLICA_SET)
            .restoreWindowDays(pitWindowDaysActive)
            .pitEnabled(true)
            .policies(Arrays.asList(basicPolicy))
            .diskBackupState(DiskBackupState.ACTIVE)
            .pitSettings(rsIdToPitSettings1)
            .build();

    _backupJobDao.saveMajority(BackupJobDao.toDbObj(backupJob1));

    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setId(oid(100))
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017
    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setId(oid(200))
                .setType(BackupSnapshot.Type.ON_DEMAND)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(1498953600, 1))); // 7/2/2017
    final CpsOplogSlice slice0 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(0))
                .setStartTimestamp(new BSONTimestamp(1498780100, 1))
                .setEndTimestamp(new BSONTimestamp(1498780200, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(1))
                .setStartTimestamp(new BSONTimestamp(1498780200, 1))
                .setEndTimestamp(new BSONTimestamp(**********, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(2))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(3))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_WEST_1)
                .setStorageRegionName(AWSRegionName.US_WEST_1));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(4))
                .setStartTimestamp(new BSONTimestamp(1498867202, 1))
                .setEndTimestamp(new BSONTimestamp(1498953700, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice5 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(5))
                .setStartTimestamp(new BSONTimestamp(1498867202, 1))
                .setEndTimestamp(new BSONTimestamp(1498953700, 1))
                .setRegionName(AWSRegionName.US_WEST_1)
                .setStorageRegionName(AWSRegionName.US_WEST_1));
    final CpsOplogSlice slice6 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(6))
                .setStartTimestamp(new BSONTimestamp(1498953700, 1))
                .setEndTimestamp(new BSONTimestamp(1498954000, 1))
                .setRegionName(AWSRegionName.US_EAST_1)
                .setStorageRegionName(AWSRegionName.US_EAST_1));
    final CpsOplogSlice slice7 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(7))
                .setStartTimestamp(new BSONTimestamp(1498953700, 1))
                .setEndTimestamp(new BSONTimestamp(1498954000, 1))
                .setRegionName(AWSRegionName.US_WEST_1)
                .setStorageRegionName(AWSRegionName.US_WEST_1));

    _oplogSliceMetadataDao.addMetadata(slice0);
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);
    _oplogSliceMetadataDao.addMetadata(slice5);
    _oplogSliceMetadataDao.addMetadata(slice6);
    _oplogSliceMetadataDao.addMetadata(slice7);
    /*-

    Setup:

    slice0 US_EAST_1
    slice1 US_EAST_1 - overlaps with snap1
    snap1 - PIT restoreable for US_EAST_1 only
    slice2 US_EAST_1
    slice3 US_WEST_1
    slice4 US_EAST_1 - overlaps with snap2
    slice5 US_WEST_1 - overlaps with snap2
    snap2 - PIT restoreable for US_EAST_1 and US_WEST1
    slice6 US_EAST_1
    slice7 US_WEST_1

    backupJob
    - pit window 3 days
    - regions primary: US_EAST_1 and migration: US_WEST_1

    Expected:
      invalidate slice[0] - because there's no restoreable snapshot before it
      keep slice[4] - even though there's no restoreable snapshot for US_WEST_1, we keep it because all of the slices
           belong to the same rsId, and for this rsId, the earliest restoreable snapshot is snaps1
      keep slice[1-3,5-7] - because there's pit restoreable snapshot
    */

    doReturn(TimeUtils.fromISOString("2017-07-03T06:34:56Z"))
        .when(_spiedCpsOplogExpirationSvc)
        .getNow();

    _spiedCpsOplogExpirationSvc.expireOplogSlices();

    List<CpsOplogSlice> invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, AWSRegionName.US_EAST_1.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(new HashSet<>(Arrays.asList(slice0.getId())), invalidSliceIds);

    invalidSlices =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, AWSRegionName.US_WEST_1.getValue());
    invalidSliceIds = invalidSlices.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(0, invalidSliceIds.size());
  }

  @Test
  public void testexpireSlices_QueuedBackupJobOnly() {
    final int pitWindowDaysInQueue = 3;

    final PolicyItem policyItem =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.DAILY, 1, Duration.ZERO, BackupRetentionUnit.DAYS);
    final Policy basicPolicy = new Policy(new ObjectId(), Arrays.asList(policyItem));

    final Map<String, PitSetting> rsIdToPitSettings2 = new HashMap<>();

    final PitSetting pitSetting2 =
        PitSetting.builder()
            .oplogMigration(
                new OplogMigration(
                    List.of(
                        new PitStorage(
                            STORE_ID, "blobstore-dummy", AWSRegionName.US_EAST_2.getValue())),
                    new Date()))
            .regionName(AWSRegionName.US_WEST_1.getValue())
            .build();

    rsIdToPitSettings2.put(RS_ID, pitSetting2);

    final BackupJob backupJob2 =
        BackupJob.builder()
            .id(oid(20))
            .projectId(GROUP_ID)
            .clusterName(CLUSTER_NAME)
            .clusterUniqueId(CLUSTER_UNIQUE_ID)
            .clusterType(ClusterType.REPLICA_SET)
            .restoreWindowDays(pitWindowDaysInQueue)
            .pitEnabled(true)
            .policies(Arrays.asList(basicPolicy))
            .diskBackupState(DiskBackupState.ACTIVE)
            .pitSettings(rsIdToPitSettings2)
            .build();

    _backupJobQueueDao.saveMajority(BackupJobDao.toDbObj(backupJob2));

    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setId(oid(100))
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setDeleted(false)
                .setPurged(false)
                .setPitSentinelOptime(new BSONTimestamp(**********, 1))); // 7/1/2017
    final CpsOplogSlice slice1 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(1))
                .setStartTimestamp(new BSONTimestamp(1498867180, 1))
                .setEndTimestamp(new BSONTimestamp(**********, 1))
                .setRegionName(AWSRegionName.US_EAST_2)
                .setStorageRegionName(AWSRegionName.US_EAST_2));
    final CpsOplogSlice slice2 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(2))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_WEST_1)
                .setStorageRegionName(AWSRegionName.US_WEST_1));
    final CpsOplogSlice slice3 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(3))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_2)
                .setStorageRegionName(AWSRegionName.US_EAST_2));
    final CpsOplogSlice slice4 =
        new CpsOplogSlice(
            buildDefaultSlice()
                .setId(oid(4))
                .setStartTimestamp(new BSONTimestamp(**********, 1))
                .setEndTimestamp(new BSONTimestamp(1498867202, 1))
                .setRegionName(AWSRegionName.US_EAST_2)
                .setStorageRegionName(AWSRegionName.US_EAST_2));
    _oplogSliceMetadataDao.addMetadata(slice1);
    _oplogSliceMetadataDao.addMetadata(slice2);
    _oplogSliceMetadataDao.addMetadata(slice3);
    _oplogSliceMetadataDao.addMetadata(slice4);

    /*-

    Setup:

    slice1 US_EAST_2
    snap1 - PIT restoreable (within restore window)
    slice2 US_WEST_1
    slice3 US_EAST_2
    slice4 US_EAST_2

    queued backupJob
    - pit window 3 days
    - regions primary: US_WEST_1 and migration: US_EAST_2

    Expected:
      invalidate all slices, since there's no active backup job for the same cluster
    */

    doReturn(TimeUtils.fromISOString("2017-07-03T06:34:56Z"))
        .when(_spiedCpsOplogExpirationSvc)
        .getNow();

    _spiedCpsOplogExpirationSvc.expireOplogSlices();

    final List<CpsOplogSlice> invalidSlicesUsEast2 =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, AWSRegionName.US_EAST_2.getValue());
    Set<ObjectId> invalidSliceIds =
        invalidSlicesUsEast2.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(
        new HashSet<>(Arrays.asList(slice1.getId(), slice3.getId(), slice4.getId())),
        invalidSliceIds);
    final List<CpsOplogSlice> invalidSlicesUsWest1 =
        _oplogSliceMetadataDao.getInvalidUnpurgedSlices(
            RS_ID, CloudProvider.AWS, AWSRegionName.US_WEST_1.getValue());
    invalidSliceIds =
        invalidSlicesUsWest1.stream().map(CpsOplogSlice::getId).collect(Collectors.toSet());
    Assert.assertEquals(new HashSet<>(Arrays.asList(slice2.getId())), invalidSliceIds);
  }

  private CpsOplogSlice.Builder buildDefaultSlice() {
    return new CpsOplogSlice.Builder()
        .setId(ObjectId.get())
        .setGroupId(GROUP_ID)
        .setClusterUniqueId(CLUSTER_UNIQUE_ID)
        .setClusterName(CLUSTER_NAME)
        .setReplicaSetId(RS_ID)
        .setRegionName(DEFAULT_REGION)
        .setNumDocs(DEFAULT_COUNT)
        .setProcessedSize(DEFAULT_PROCESSED_SIZE)
        .setCloudProvider(DEFAULT_PROVIDER)
        .setRawSize(DEFAULT_RAW_SIZE)
        .setMongodVersion(MONGOD_VERSION)
        .setCompressor(COMPRESSOR)
        .setPurged(false)
        .setValid(true);
  }

  private ObjectId addAwsSnapshot(SnapshotUpdate update) {
    final SnapshotUpdate.AwsSnapshotFieldBuilder awsBuilder =
        new SnapshotUpdate.AwsSnapshotFieldBuilder();
    final String awsRegionName = AWSRegionName.US_EAST_1.getName();
    final ObjectId awsAccountId = new ObjectId();
    final ObjectId awsContainerId = new ObjectId();
    final String awsSubnetId = "awsSubnetId-1";
    final String ebsVolumeId = "ebsVolumeId-1";
    final String ebsSnapshotDesc = AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId);
    final String ebsVolumeType = VolumeType.Io1.name();
    final boolean ebsVolumeEncrypted = true;
    final Integer ebsVolumeSize = AWSNDSInstanceSize.M30.getDefaultDiskSizeGB();
    final Integer ebsDiskIOPS = AWSNDSInstanceSize.M30.getMaxEBSStandardIOPS();

    final Date completionDate = new Date();
    return _backupSnapshotDao.addBackupSnapshot(
        update
            .setId(new ObjectId())
            .setProjectId(GROUP_ID)
            .setClusterName(CLUSTER_NAME)
            .setDeploymentClusterName(CLUSTER_NAME)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
            .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
            .setRsId(RS_ID)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(USED_SPACE)
            .setCloudProviders(Arrays.asList(CloudProvider.AWS))
            .setAwsSnapshotField(
                awsBuilder
                    .withEbsSnapshotDescription(ebsSnapshotDesc)
                    .withAWSAccountId(awsAccountId)
                    .withAWSContainerId(awsContainerId)
                    .withAWSSubnetId(awsSubnetId)
                    .withEbsVolumeId(ebsVolumeId)
                    .withEbsVolumeType(ebsVolumeType)
                    .withIsEbsVolumeEncrypted(ebsVolumeEncrypted)
                    .withEbsVolumeSize(ebsVolumeSize)
                    .withEbsDiskIOPS(ebsDiskIOPS)
                    .withRegionName(awsRegionName))
            .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(completionDate));
  }
}
