package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.CpsPolicyUtils;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.PlanningType;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.planner.BackupSnapshotUtils;
import jakarta.inject.Inject;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * The completed date of the snapshots could be several minutes away from the scheduled creation
 * date. That's because we run the simulation in 1 hour increments, instead of 1 minute increments.
 */
public class CpsSnapshotEngineIntTests extends JUnit5BaseSvcTest {

  private Group _group;
  private ObjectId _groupId;
  private static final Logger LOG = LoggerFactory.getLogger(CpsSnapshotEngineIntTests.class);

  @Inject private CpsPolicySvc _cpsPolicySvc;

  @Inject private CpsPitSvc _cpsPitSvc;

  @Inject private CpsSvc _cpsSvc;

  @Inject private BackupSnapshotDao _backupSnapshotDao;

  @Inject private BackupJobDao _backupJobDao;

  @Inject private CpsGcSvc _cpsGcSvc;

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  private static DBObject toDBObject(BackupJob job) {
    return BackupJobDao.toDbObj(job);
  }

  @BeforeEach
  public void setup() throws Exception {
    _group = MmsFactory.createGroupWithNDSPlan("test group");
    _groupId = _group.getId();
    final AWSApiSvc dummyAwsApiSvc = mock(AWSApiSvc.class);
    // NOTE: this is the only mocking done in this Int Test
    doNothing().when(dummyAwsApiSvc).deleteEbsSnapshot(any(), any(), any(), any());
    _cpsGcSvc.setAwsApiSvc(dummyAwsApiSvc);
  }

  /** This only saves the cluster metadata, it doesn't create the actual cluster */
  private ClusterDescription saveAndGetDiskBackupEnabledCluster() {
    return saveAndGetDiskBackupEnabledCluster(ClusterDescription.State.IDLE, false);
  }

  private ClusterDescription saveAndGetDiskBackupEnabledCluster(
      final ClusterDescription.State pState, final boolean pIsDeleteRequested) {
    final BasicDBObject dbObject =
        NDSModelTestFactory.getAWSClusterDescription(_groupId, "cluster0");
    dbObject.append("diskBackupEnabled", true);
    if (pIsDeleteRequested) {
      dbObject.append("deleteRequested", true);
    }
    final ClusterDescription clusterDescription = new ClusterDescription(dbObject);
    _clusterDescriptionDao.save(clusterDescription);

    if (pState != ClusterDescription.State.IDLE) {
      _clusterDescriptionDao.setState(
          clusterDescription.getGroupId(), clusterDescription.getName(), pState);
    }

    return _clusterDescriptionDao
        .findByUniqueId(clusterDescription.getGroupId(), clusterDescription.getUniqueId())
        .get();
  }

  private void runCpsSim(
      final BackupJob pBackupJob,
      final ClusterDescription pClusterDescription,
      final Date pStartDate,
      final Date pEndDate) {
    final CpsSnapshotEngine cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .cpsPitSvc(_cpsPitSvc)
            .cpsSvc(_cpsSvc)
            .backupSnapshotDao(_backupSnapshotDao)
            .logger(LOG)
            .build();

    final CPSSimulator cpsSimulator =
        new CPSSimulator(
            _cpsGcSvc,
            cpsSnapshotEngine,
            _groupId,
            pClusterDescription,
            pStartDate,
            pEndDate,
            pBackupJob.getId());
    cpsSimulator.run();
  }

  private String snapshotsToStr(final List<BackupSnapshot> pCompletedSnapshots) {
    final StringBuffer sb = new StringBuffer("\nSnapshots:\n");
    pCompletedSnapshots.stream()
        .forEach(
            s -> {
              sb.append(
                  String.format(
                      "id=%s schedcre=%s compl=%s del=%s freq=%s policyItems=%s\n",
                      s.getId(),
                      s.getScheduledCreationDate(),
                      s.getSnapshotCompletionDate(),
                      s.getScheduledDeletionDate(),
                      s.getFrequencyType(),
                      s.getPolicyItems()));
            });
    return sb.toString();
  }

  @Test
  public void testHourlyOnly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.HOURLY,
                Duration.ofDays(1),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-02T14:35:56Z"));

    final ObjectId expectedPolicyItemId =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(24, completedSnapshots.size(), completedSnapshotsView);
    for (final BackupSnapshot backupSnapshot : completedSnapshots) {
      assertEquals(
          BackupFrequencyType.HOURLY, backupSnapshot.getFrequencyType(), completedSnapshotsView);
      assertEquals(
          BackupRetentionUnit.DAYS, backupSnapshot.getRetentionUnit(), completedSnapshotsView);
      assertEquals(
          expectedPolicyItemId, backupSnapshot.getPolicyItems().get(0), completedSnapshotsView);
    }
  }

  @Test
  public void testHourlyOnly2() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:56Z");
    final int referenceTimeInMins = 780; // 13:00
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                12,
                BackupFrequencyType.HOURLY,
                Duration.ofDays(3),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-02T14:35:56Z"));

    final List<String> expectedSchedCreateList =
        Arrays.asList(
            "Fri Feb 01 00:00:56 GMT 2019",
            "Fri Feb 01 13:00:56 GMT 2019",
            "Sat Feb 02 01:00:56 GMT 2019",
            "Sat Feb 02 13:00:56 GMT 2019");
    final ObjectId expectedPolicyItemId =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(4, completedSnapshots.size(), completedSnapshotsView);
    for (final BackupSnapshot backupSnapshot : completedSnapshots) {
      assertEquals(
          BackupFrequencyType.HOURLY, backupSnapshot.getFrequencyType(), completedSnapshotsView);
      assertEquals(
          BackupRetentionUnit.DAYS, backupSnapshot.getRetentionUnit(), completedSnapshotsView);
      assertEquals(
          expectedPolicyItemId, backupSnapshot.getPolicyItems().get(0), completedSnapshotsView);
    }

    SimpleDateFormat dateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy");
    dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
    final List<String> completedSnapshotCreDates =
        completedSnapshots.stream()
            .sorted(Comparator.comparing(BackupSnapshot::getScheduledCreationDate))
            .map(s -> s.getScheduledCreationDate())
            .map(d -> dateFormat.format(d))
            .collect(Collectors.toList());
    assertEquals(expectedSchedCreateList, completedSnapshotCreDates, completedSnapshotsView);
  }

  @Test
  public void testDailyOnly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.DAILY,
                Duration.ofDays(3),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-07T14:34:57Z"));

    final ObjectId expectedPolicyItemId =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(3, completedSnapshots.size(), completedSnapshotsView);
    for (final BackupSnapshot backupSnapshot : completedSnapshots) {
      assertEquals(
          BackupFrequencyType.DAILY, backupSnapshot.getFrequencyType(), completedSnapshotsView);
      assertEquals(
          BackupRetentionUnit.DAYS, backupSnapshot.getRetentionUnit(), completedSnapshotsView);
      assertEquals(
          expectedPolicyItemId, backupSnapshot.getPolicyItems().get(0), completedSnapshotsView);
    }
  }

  @Test
  public void testWeeklyOnly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                6,
                BackupFrequencyType.WEEKLY,
                Duration.ofDays(30),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-27T14:34:57Z"));

    final ObjectId expectedPolicyItemId =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(5, completedSnapshots.size(), completedSnapshotsView);
    for (final BackupSnapshot backupSnapshot : completedSnapshots) {
      assertEquals(
          BackupFrequencyType.WEEKLY, backupSnapshot.getFrequencyType(), completedSnapshotsView);
      assertEquals(
          BackupRetentionUnit.DAYS, backupSnapshot.getRetentionUnit(), completedSnapshotsView);
      assertEquals(
          expectedPolicyItemId, backupSnapshot.getPolicyItems().get(0), completedSnapshotsView);
    }
  }

  @Test
  public void testMonthlyOnly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.MONTHLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2020-02-02T14:34:57Z"));

    final ObjectId expectedPolicyItemId =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(13, completedSnapshots.size(), completedSnapshotsView);
    for (final BackupSnapshot backupSnapshot : completedSnapshots) {
      assertEquals(
          BackupFrequencyType.MONTHLY, backupSnapshot.getFrequencyType(), completedSnapshotsView);
      assertEquals(
          BackupRetentionUnit.DAYS, backupSnapshot.getRetentionUnit(), completedSnapshotsView);
      assertEquals(
          expectedPolicyItemId, backupSnapshot.getPolicyItems().get(0), completedSnapshotsView);
    }
  }

  @Test
  public void testYearlyOnly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.YEARLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2020-02-02T14:34:57Z"));

    final ObjectId expectedPolicyItemId =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(2, completedSnapshots.size(), completedSnapshotsView);
    for (final BackupSnapshot backupSnapshot : completedSnapshots) {
      assertEquals(
          BackupFrequencyType.YEARLY, backupSnapshot.getFrequencyType(), completedSnapshotsView);
      assertEquals(
          BackupRetentionUnit.DAYS, backupSnapshot.getRetentionUnit(), completedSnapshotsView);
      assertEquals(
          expectedPolicyItemId, backupSnapshot.getPolicyItems().get(0), completedSnapshotsView);
    }
  }

  @Test
  public void testCombination() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.DAILY,
                Duration.ofDays(7),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.WEEKLY,
                Duration.ofDays(14),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.MONTHLY,
                Duration.ofDays(62),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.YEARLY,
                Duration.ofDays(372),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-05-02T14:34:57Z"));

    final int expectedDaily = 5;
    final int expectedWeekly = 2;
    final int expectedMonthly = 2;
    final int expectedYearly = 1;

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    assertEquals(
        expectedDaily + expectedWeekly + expectedMonthly + expectedYearly,
        completedSnapshots.size(),
        snapshotsToStr(completedSnapshots));
  }

  /** Check for coinciding monthly and yearly */
  @Test
  public void testYearlyAndMonthly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.MONTHLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                2,
                BackupFrequencyType.YEARLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-06-03T14:34:57Z"));

    final int expected1stSnapshot = 1;
    // 3 instead of 4 since it coincides with yearly frequency on 2/01
    final int expectedMonthly = 3;
    final int expectedYearly = 1;

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    assertEquals(
        expected1stSnapshot + expectedMonthly + expectedYearly,
        completedSnapshots.size(),
        snapshotsToStr(completedSnapshots));
  }

  /** Check for coinciding montlhy and weekly */
  @Test
  public void testMultiWeeklyAndMonthly() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.WEEKLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                7,
                BackupFrequencyType.WEEKLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                15,
                BackupFrequencyType.MONTHLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                CpsPolicyUtils.FREQUENCY_INTERVAL_LAST_DAY_OF_MONTH,
                BackupFrequencyType.MONTHLY,
                Duration.ofDays(400),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-04-02T14:34:57Z"));

    final int expected1stSnapshot = 1;
    final int expectedWeekly1 = 9;
    final int expectedWeekly2 =
        8; // this is only 8 because 2019-03-31 got combined with last day monthly
    final int expectedMonthly1 = 2;
    final int expectedMonthly2 = 2;

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    assertEquals(
        expected1stSnapshot
            + expectedWeekly1
            + expectedWeekly2
            + expectedMonthly1
            + expectedMonthly2,
        completedSnapshots.size(),
        snapshotsToStr(completedSnapshots));
  }

  @Test
  public void testDailyWeeklyCoincide() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.DAILY,
                Duration.ofDays(7),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.WEEKLY,
                Duration.ofDays(7),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-01T13:34:57Z"));

    final int expected1stSnapshot = 1;

    final ObjectId expectedPolicyItemId1 =
        backupJob.getPolicies().get(0).getPolicyItems().get(0).getId();
    final ObjectId expectedPolicyItemId2 =
        backupJob.getPolicies().get(0).getPolicyItems().get(1).getId();
    final Set<ObjectId> expectedPolicyIds =
        Arrays.asList(expectedPolicyItemId1, expectedPolicyItemId2).stream()
            .collect(Collectors.toSet());

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);
    assertEquals(expected1stSnapshot, completedSnapshots.size(), completedSnapshotsView);
    assertEquals(
        expectedPolicyIds,
        completedSnapshots.get(0).getPolicyItems().stream().collect(Collectors.toSet()),
        completedSnapshotsView);
  }

  @Test
  public void testDefaultPolicy() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                6,
                BackupFrequencyType.HOURLY,
                Duration.ofDays(2),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.DAILY,
                Duration.ofDays(7),
                BackupRetentionUnit.DAYS),
            getPolicyItem(
                nextSnapshotDate,
                6,
                BackupFrequencyType.WEEKLY,
                Duration.ofDays(4 * 7),
                BackupRetentionUnit.WEEKS),
            getPolicyItem(
                nextSnapshotDate,
                CpsPolicyUtils.FREQUENCY_INTERVAL_LAST_DAY_OF_MONTH,
                BackupFrequencyType.MONTHLY,
                Duration.ofDays(12 * 31),
                BackupRetentionUnit.MONTHS),
            getPolicyItem(
                nextSnapshotDate,
                3,
                BackupFrequencyType.YEARLY,
                Duration.ofDays(12 * 31),
                BackupRetentionUnit.YEARS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:00Z"),
        TimeUtils2.fromISOString("2020-04-02T14:34:57Z"));

    final List<String> expectedSchedCreateList =
        Arrays.asList(
            "Sun Mar 31 00:00:00 GMT 2019",
            "Tue Apr 30 00:00:00 GMT 2019",
            "Fri May 31 00:00:00 GMT 2019",
            "Sun Jun 30 00:00:00 GMT 2019",
            "Wed Jul 31 00:00:00 GMT 2019",
            "Sat Aug 31 00:00:00 GMT 2019",
            "Mon Sep 30 00:00:00 GMT 2019",
            "Thu Oct 31 00:00:00 GMT 2019",
            "Sat Nov 30 00:00:00 GMT 2019",
            "Tue Dec 31 00:00:00 GMT 2019",
            "Fri Jan 31 00:00:00 GMT 2020",
            "Sat Feb 29 00:00:00 GMT 2020",
            "Sun Mar 01 00:00:00 GMT 2020",
            "Sat Mar 07 00:00:00 GMT 2020",
            "Sat Mar 14 00:00:00 GMT 2020",
            "Sat Mar 21 00:00:00 GMT 2020",
            "Fri Mar 27 00:00:00 GMT 2020",
            "Sat Mar 28 00:00:00 GMT 2020",
            "Sun Mar 29 00:00:00 GMT 2020",
            "Mon Mar 30 00:00:00 GMT 2020",
            "Tue Mar 31 00:00:00 GMT 2020",
            "Tue Mar 31 18:00:00 GMT 2020",
            "Wed Apr 01 00:00:00 GMT 2020",
            "Wed Apr 01 06:00:00 GMT 2020",
            "Wed Apr 01 12:00:00 GMT 2020",
            "Wed Apr 01 18:00:00 GMT 2020",
            "Thu Apr 02 00:00:00 GMT 2020",
            "Thu Apr 02 06:00:00 GMT 2020",
            "Thu Apr 02 12:00:00 GMT 2020");

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    final String completedSnapshotsView = snapshotsToStr(completedSnapshots);

    DateFormat dateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy");
    dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
    final List<String> completedSnapshotCreDates =
        completedSnapshots.stream()
            .sorted(Comparator.comparing(BackupSnapshot::getScheduledCreationDate))
            .map(s -> s.getScheduledCreationDate())
            .map(d -> dateFormat.format(d))
            .collect(Collectors.toList());
    assertEquals(expectedSchedCreateList, completedSnapshotCreDates, completedSnapshotsView);
  }

  @Test
  public void testDeletedCluster() throws SvcException {
    final ClusterDescription clusterDescription =
        saveAndGetDiskBackupEnabledCluster(ClusterDescription.State.DELETED, false);
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.HOURLY,
                Duration.ofDays(1),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-01T15:35:56Z"));

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    assertEquals(0, completedSnapshots.size());
  }

  @Test
  public void testDeleteRequestedCluster() throws SvcException {
    final ClusterDescription clusterDescription =
        saveAndGetDiskBackupEnabledCluster(ClusterDescription.State.IDLE, true);
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.HOURLY,
                Duration.ofDays(1),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    runCpsSim(
        backupJob,
        clusterDescription,
        TimeUtils2.fromISOString("2019-02-01T12:34:56Z"),
        TimeUtils2.fromISOString("2019-02-01T15:35:56Z"));

    final List<BackupSnapshot> completedSnapshots =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            _groupId, clusterDescription.getUniqueId(), false, true);
    assertEquals(0, completedSnapshots.size());
  }

  @Test
  public void testShouldCopySnapshotsNowReplset() {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final CpsSnapshotEngine cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .cpsPitSvc(_cpsPitSvc)
            .cpsSvc(_cpsSvc)
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .backupSnapshotDao(_backupSnapshotDao)
            .logger(LOG)
            .build();

    assertFalse(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    final ObjectId copyId1 = addQueuedCopySnapshot(clusterDescription, new ObjectId(), false);

    assertTrue(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    _backupSnapshotDao.setQueuedSnapshotToInProgress(copyId1, new ObjectId());

    assertFalse(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    final ObjectId copyId2 = addQueuedCopySnapshot(clusterDescription, new ObjectId(), false);
    assertFalse(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    _backupSnapshotDao.markCompleted(copyId1, new Date(), new Date(), BackupRetentionUnit.MONTHS);
    assertTrue(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));
  }

  @Test
  public void testShouldCopySnapshotsNowSharded() {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final CpsSnapshotEngine cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .cpsPitSvc(_cpsPitSvc)
            .cpsSvc(_cpsSvc)
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .backupSnapshotDao(_backupSnapshotDao)
            .logger(LOG)
            .build();

    assertFalse(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    final ObjectId copyId1 = addQueuedCopySnapshot(clusterDescription, new ObjectId(), true);
    final ObjectId copyId2 = addQueuedCopySnapshot(clusterDescription, new ObjectId(), true);
    assertTrue(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    _backupSnapshotDao.setQueuedSnapshotToInProgress(copyId1, new ObjectId());
    _backupSnapshotDao.setQueuedSnapshotToInProgress(copyId2, new ObjectId());

    assertFalse(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    final ObjectId copyId3 = addQueuedCopySnapshot(clusterDescription, new ObjectId(), false);
    final ObjectId copyId4 = addQueuedCopySnapshot(clusterDescription, new ObjectId(), false);

    assertFalse(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));

    _backupSnapshotDao.markCompleted(copyId1, new Date(), new Date(), BackupRetentionUnit.MONTHS);
    _backupSnapshotDao.markCompleted(copyId2, new Date(), new Date(), BackupRetentionUnit.MONTHS);
    assertTrue(cpsSnapshotEngine.shouldCopySnapshotNow(clusterDescription));
  }

  @Test
  public void testMultipleQueuedSnapshots() throws SvcException {
    final ClusterDescription clusterDescription = saveAndGetDiskBackupEnabledCluster();
    final Date nextSnapshotDate = TimeUtils2.fromISOString("2019-02-01T00:00:00Z");
    final int referenceTimeInMins = 0;
    final BackupJob backupJob =
        getBackupJob(
            referenceTimeInMins,
            nextSnapshotDate,
            clusterDescription.getUniqueId(),
            getPolicyItem(
                nextSnapshotDate,
                1,
                BackupFrequencyType.HOURLY,
                Duration.ofDays(2),
                BackupRetentionUnit.DAYS));
    CpsPolicySvc.validatePolicies(clusterDescription, null, backupJob.getPolicies());
    _backupJobDao.saveReplicaSafe(toDBObject(backupJob));

    final CpsSnapshotEngine cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .cpsPitSvc(_cpsPitSvc)
            .cpsSvc(_cpsSvc)
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .backupSnapshotDao(_backupSnapshotDao)
            .logger(LOG)
            .build();

    Date sameTime = new Date();

    for (int i = 0; i < 10; i++) {
      cpsSnapshotEngine.tryQueue(sameTime, _group, backupJob, clusterDescription);
    }

    final List<BackupSnapshot> queuedScheduledBackupSnapshots1 =
        _backupSnapshotDao.findActiveQueuedByType(
            clusterDescription.getGroupId(),
            backupJob.getClusterUniqueId(),
            BackupSnapshot.Type.SCHEDULED);

    assertEquals(1, queuedScheduledBackupSnapshots1.size());

    addResilientSnapshot(clusterDescription);

    final List<BackupSnapshot> queuedResilientBackupSnapshots1 =
        _backupSnapshotDao.findActiveQueuedByType(
            clusterDescription.getGroupId(),
            backupJob.getClusterUniqueId(),
            BackupSnapshot.Type.FALLBACK);

    assertEquals(1, queuedResilientBackupSnapshots1.size());
    cpsSnapshotEngine.tryQueue(sameTime, _group, backupJob, clusterDescription);

    final List<BackupSnapshot> queuedScheduledBackupSnapshots2 =
        _backupSnapshotDao.findActiveQueuedByType(
            clusterDescription.getGroupId(),
            backupJob.getClusterUniqueId(),
            BackupSnapshot.Type.SCHEDULED);

    assertEquals(1, queuedScheduledBackupSnapshots2.size());

    final List<BackupSnapshot> queuedResilientBackupSnapshots2 =
        _backupSnapshotDao.findActiveQueuedByType(
            clusterDescription.getGroupId(),
            backupJob.getClusterUniqueId(),
            BackupSnapshot.Type.FALLBACK);

    assertEquals(0, queuedResilientBackupSnapshots2.size());
  }

  private void addResilientSnapshot(final ClusterDescription cd) {
    final SnapshotUpdate update = new SnapshotUpdate();
    _backupSnapshotDao.addBackupSnapshot(
        update
            .setId(new ObjectId())
            .setProjectId(cd.getGroupId())
            .setClusterUniqueId(cd.getUniqueId())
            .setPurged(false)
            .setDeleted(false)
            .setType(BackupSnapshot.Type.FALLBACK)
            .setStatus(BackupSnapshot.Status.QUEUED)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(0L)
            .setCloudProviders(Arrays.asList(CloudProvider.AWS))
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setAwsSnapshotField(new SnapshotUpdate.AwsSnapshotFieldBuilder()));
  }

  private ObjectId addQueuedCopySnapshot(
      final ClusterDescription cd, final ObjectId parentSnasphotId, final boolean isSharded) {
    final SnapshotUpdate update = new SnapshotUpdate();
    final ObjectId id = new ObjectId();
    _backupSnapshotDao.addBackupSnapshot(
        update
            .setId(id)
            .setProjectId(cd.getGroupId())
            .setClusterUniqueId(cd.getUniqueId())
            .setPurged(false)
            .setDeleted(false)
            .setShard(isSharded)
            .setType(BackupSnapshot.Type.COPY)
            .setStatus(BackupSnapshot.Status.QUEUED)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(199L)
            .setCloudProviders(Arrays.asList(CloudProvider.AWS))
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setParentSnapshotId(parentSnasphotId)
            .setAwsSnapshotField(new SnapshotUpdate.AwsSnapshotFieldBuilder()));
    return id;
  }

  private BasicDBObject getPolicyItem(
      final Date pNextSnapshotDate,
      final int pFrequencyInterval,
      final BackupFrequencyType pBackupFrequencyType,
      final Duration pRetention,
      final BackupRetentionUnit pBackupRetentionUnit) {
    return new BasicDBObject()
        .append(PolicyItem.FieldDefs.ID, new ObjectId())
        .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, pFrequencyInterval)
        .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, pBackupFrequencyType.name())
        .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, pNextSnapshotDate)
        .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, pRetention.toMillis())
        .append(PolicyItem.FieldDefs.RETENTION_UNIT, pBackupRetentionUnit.name());
  }

  private BackupJob getBackupJob(
      final int referenceTimeInMins,
      final Date nextSnapshotDate,
      final ObjectId clusterUniqueId,
      final BasicDBObject... items) {
    final List<BasicDBObject> itemList = Arrays.asList(items);
    final List<Policy> policies =
        List.of(
            new Policy(
                new BasicDBObject(Policy.FieldDefs.POLICY_ITEMS, itemList)
                    .append(Policy.FieldDefs.ID, new ObjectId())));

    return NDSModelTestFactory.getBackupJobBuilder()
        .projectId(_groupId)
        .policies(policies)
        .referenceTimeInMins(referenceTimeInMins)
        .nextSnapshotDate(nextSnapshotDate)
        .clusterUniqueId(clusterUniqueId)
        .build();
  }

  class CPSSimulator {

    final CpsGcSvc _cpsGcSvc;
    CpsSnapshotEngine _cpsSnapshotEngine;
    final BackupSnapshotDao _backupSnapshotDao;
    final ObjectId _groupId;
    final ClusterDescription _clusterDescription;
    final Date _startDate;
    final Date _endDate;
    final InstanceHardware _primaryInstance;
    private final ObjectId _jobId;

    CPSSimulator(
        final CpsGcSvc pCpsGcSvc,
        final CpsSnapshotEngine pCpsSnapshotEngine,
        final ObjectId pGroupId,
        final ClusterDescription pClusterDescription,
        final Date pStartDate,
        final Date pEndDate,
        final ObjectId jobId) {
      _cpsGcSvc = pCpsGcSvc;
      _cpsSnapshotEngine = pCpsSnapshotEngine;
      _backupSnapshotDao = pCpsSnapshotEngine.getBackupSnapshotDao();
      _groupId = pGroupId;
      _clusterDescription = pClusterDescription;
      _startDate = pStartDate;
      _endDate = pEndDate;
      _primaryInstance =
          new AWSInstanceHardware(
              InstanceHardware.getEmptyHardware(CloudProvider.AWS, new ObjectId(), new Date(), 0));
      _jobId = jobId;
    }

    private void simulateSnapshotMove(final BackupJob job, final Date completedDate) {
      final List<BackupSnapshot> snapshotList =
          _backupSnapshotDao.findActionableNonCopyByCluster(
              _groupId, _clusterDescription.getUniqueId());
      for (final BackupSnapshot snapshot : snapshotList) {
        final Pair<Date, BackupRetentionUnit> deletionDateAndRetention =
            BackupSnapshotUtils.getScheduledDeletionDateBackup(job, snapshot, completedDate);

        _backupSnapshotDao.markCompleted(
            snapshot.getId(),
            deletionDateAndRetention.getLeft(),
            completedDate,
            deletionDateAndRetention.getRight());
        final SnapshotUpdate update = new SnapshotUpdate();
        _backupSnapshotDao.updateBackupSnapshot(
            snapshot.getId(), update.setCloudProviders(Arrays.asList(CloudProvider.AWS)));
      }
    }

    private void runInternal(final Date now) {
      _cpsGcSvc.cleanSnapshotsInternal(now);

      final BackupJob updatedBackupJob = _backupJobDao.find(_jobId).get();
      _cpsSnapshotEngine = _cpsSnapshotEngine.toBuilder().build();

      _cpsSnapshotEngine.tryQueue(now, _group, updatedBackupJob, _clusterDescription);

      if (_cpsSnapshotEngine.shouldTakeSnapshotNow(
          _group, _clusterDescription, PlanningType.CONCURRENT)) {
        simulateSnapshotMove(updatedBackupJob, now);
      }
    }

    public void run() {
      _backupSnapshotDao.remove(new BasicDBObject());

      Date now = _startDate;

      while (now.before(_endDate)) {
        runInternal(now);
        now = DateUtils.addHours(now, 1);
      }
      runInternal(_endDate);
    }
  }
}
