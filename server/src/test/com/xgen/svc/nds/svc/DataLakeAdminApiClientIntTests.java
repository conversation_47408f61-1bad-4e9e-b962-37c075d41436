package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

public class DataLakeAdminApiClientIntTests extends JUnit5BaseSvcTest {

  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private DataLakeAdminApiClient _adminApiClient;

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testCanReconnect() throws IOException, DataLakeAdminApiException {
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant =
        NDSDataLakeTenant.builder()
            .tenantId(tenantId)
            .id(new NDSDataLakeTenantId(groupId, "tenantname"))
            .build();
    try {
      _adminApiClient.findStorageConfig(tenant).orElseThrow();
      fail("Should not be able to fetch a config if API is down");
    } catch (DataLakeAdminApiException pE) {
      // ignore
    }
    _dataLakeTestUtils.setUp();

    final NDSDataLakeStorageV1View storageConfig =
        NDSDataLakeStorageV1View.builder()
            .config(new Document().append("stores", List.of()).append("databases", List.of()))
            .build();
    _adminApiClient.updateStorageConfig(tenant, storageConfig, new Date(), "int-test", null);
    assertEquals(storageConfig, _adminApiClient.findStorageConfig(tenant).orElseThrow());
  }
}
