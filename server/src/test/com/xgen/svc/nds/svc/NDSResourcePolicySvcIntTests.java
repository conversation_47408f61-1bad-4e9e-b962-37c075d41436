package com.xgen.svc.nds.svc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse.Decision;
import com.xgen.cloud.nds.resourcepolicy._public.model.Policy;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.nds.resourcepolicy.util.AtlasResourcePolicyTestUtil;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class NDSResourcePolicySvcIntTests extends JUnit5BaseSvcTest {
  private static final ObjectId ORG_ID = new ObjectId();

  @Inject private NDSResourcePolicySvc _ndsResourcePolicySvc;
  @Inject private AtlasResourcePolicySvc _atlasResourcePolicySvc;
  @Inject private GroupDao _groupDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private AuditSvc _auditSvc;
  @Inject private InformationalAlertSvc _informationalAlertSvc;
  @Inject private NDSClusterSvc _ndsClusterSvc;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);
  }

  @Test
  public void testIsProjectRequestAuthorized_noResourcePolicies() {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final AtlasResourcePolicySvc spyAtlasResourcePolicySvc = Mockito.spy(_atlasResourcePolicySvc);
    final NDSResourcePolicySvc svc =
        new NDSResourcePolicySvc(
            spyAtlasResourcePolicySvc, _auditSvc, _informationalAlertSvc, _ndsClusterSvc);

    final AtlasResourcePolicyAuthResponse response =
        svc.isProjectIpAccessListRequestAuthorized(
            group, NDSNetworkPermission.getPermissionsFromStrings("0.0.0.0/0"), null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
    Mockito.verify(spyAtlasResourcePolicySvc, times(0)).isAuthorized(any(), any(), any());
  }

  @Test
  public void testIsCreateClusterRequestAuthorized_noResourcePolicies() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final AtlasResourcePolicySvc spyAtlasResourcePolicySvc = Mockito.spy(_atlasResourcePolicySvc);
    final NDSResourcePolicySvc svc =
        new NDSResourcePolicySvc(
            spyAtlasResourcePolicySvc, _auditSvc, _informationalAlertSvc, _ndsClusterSvc);

    final ClusterDescriptionView awsCluster =
        new ClusterDescriptionView(
            new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(group.getId(), "cluster0")));

    final AtlasResourcePolicyAuthResponse response =
        svc.isCreateClusterRequestAuthorized(
            group,
            awsCluster,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
    Mockito.verify(spyAtlasResourcePolicySvc, times(0)).isAuthorized(any(), any(), any());
  }

  @Test
  public void testIsUpdateClusterRequestAuthorized_noResourcePolicies() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final AtlasResourcePolicySvc spyAtlasResourcePolicySvc = Mockito.spy(_atlasResourcePolicySvc);
    final NDSResourcePolicySvc svc =
        new NDSResourcePolicySvc(
            spyAtlasResourcePolicySvc, _auditSvc, _informationalAlertSvc, _ndsClusterSvc);

    final ClusterDescriptionView awsCluster =
        new ClusterDescriptionView(
            new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(group.getId(), "cluster0")));

    final AtlasResourcePolicyAuthResponse response =
        svc.isUpdateClusterRequestAuthorized(
            group,
            awsCluster.getUniqueId(),
            awsCluster,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
    Mockito.verify(spyAtlasResourcePolicySvc, times(0)).isAuthorized(any(), any(), any());
  }

  @Test
  public void testIsProjectRequestAuthorized_deny() {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"project.ipAccessList.modify",
                resource
              ) when {
               context.project.ipAccessList.contains(ip("0.0.0.0/0"))
              };
            """);

    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isProjectIpAccessListRequestAuthorized(
            group, NDSNetworkPermission.getPermissionsFromStrings("0.0.0.0/0"), null);
    assertThat(response.decision()).isEqualTo(Decision.Deny);

    final List<Event> createdEvent =
        _auditSvc.findByEventTypeForGroup(group.getId(), Type.RESOURCE_POLICY_VIOLATED);
    assertThat(createdEvent).hasSize(1);
  }

  @Test
  public void testIsProjectRequestAuthorized_allow() {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"project.ipAccessList.modify",
                resource
              ) when {
               context.project.ipAccessList.contains(ip("0.0.0.0/0"))
              };
            """);

    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isProjectIpAccessListRequestAuthorized(
            group, NDSNetworkPermission.getPermissionsFromStrings("*******"), null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
  }

  @Test
  public void testIsCreateClusterRequestAuthorized_deny() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final ClusterDescriptionView awsCluster =
        new ClusterDescriptionView(
            new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(group.getId(), "cluster0")));

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
              ) when {
               context.cluster.regions.contains(ResourcePolicy::Region::"aws:us-east-1")
              };
            """);

    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group,
            awsCluster,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Deny);
  }

  @Test
  public void testIsCreateClusterRequestAuthorized_allow() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final ClusterDescriptionView gcpCluster =
        new ClusterDescriptionView(
            new ClusterDescription(
                NDSModelTestFactory.getGCPClusterDescription(group.getId(), "cluster0")));

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
              ) when {
               context.cluster.regions.contains(ResourcePolicy::Region::"aws:us-east-1")
              };
            """);

    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group,
            gcpCluster,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
  }

  @Test
  public void testIsCreateClusterRequestAuthorizedWithClusterLimit_allow() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final ClusterDescriptionView gcpCluster =
        new ClusterDescriptionView(
            new ClusterDescription(
                NDSModelTestFactory.getGCPClusterDescription(group.getId(), "cluster0")));

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
              )
              when { context.project.clustersInProject > 1 };
            """);

    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group,
            gcpCluster,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
  }

  @Test
  public void testIsCreateClusterRequestAuthorizedWithClusterLimit_deny() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final ClusterDescriptionView gcpCluster =
        new ClusterDescriptionView(
            new ClusterDescription(
                NDSModelTestFactory.getGCPClusterDescription(group.getId(), "cluster0")));

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
              )
              when { context.project.clustersInProject > 0 };
            """);

    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group,
            gcpCluster,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Deny);
  }

  @Test
  public void testIsUpdateClusterRequestAuthorized_deny() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final ClusterDescription awsCluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), "cluster0"));
    _clusterDescriptionDao.save(awsCluster);

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
              ) when {
               context.cluster.regions.contains(ResourcePolicy::Region::"aws:us-east-2")
              };
            """);
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final ReplicationSpec replicationSpec = awsCluster.getReplicationSpecsWithShardData().get(0);
    final ClusterDescription awsClusterInvalid =
        awsCluster
            .copy()
            .setReplicationSpecList(
                List.of(
                    NDSModelTestFactory.getAWSReplicationSpec(
                        replicationSpec.getId(),
                        replicationSpec.getZoneId(),
                        replicationSpec.getZoneName(),
                        replicationSpec.getNumShards(),
                        replicationSpec.getTotalNodes(),
                        List.of(AWSRegionName.US_EAST_2))))
            .build();

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group,
            new ClusterDescriptionView(awsClusterInvalid),
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Deny);

    final List<Event> createdEvent =
        _auditSvc.findByEventTypeForGroup(group.getId(), Type.RESOURCE_POLICY_VIOLATED);
    assertThat(createdEvent).hasSize(1);
  }

  @Test
  public void testIsUpdateClusterRequestAuthorized_allow() throws SvcException {
    final Group group = new Group();
    group.setOrgId(ORG_ID);
    group.setName("project");
    _groupDao.save(group);

    final ClusterDescription awsCluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), "cluster0"));
    _clusterDescriptionDao.save(awsCluster);

    Policy policy =
        new Policy(
            new ObjectId(),
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
              ) when {
               context.cluster.regions.contains(ResourcePolicy::Region::"aws:us-east-2")
              };
            """);
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), ORG_ID).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final ReplicationSpec replicationSpec = awsCluster.getReplicationSpecsWithShardData().get(0);
    final ClusterDescription awsClusterInvalid =
        awsCluster
            .copy()
            .setReplicationSpecList(
                List.of(
                    NDSModelTestFactory.getAWSReplicationSpec(
                        replicationSpec.getId(),
                        replicationSpec.getZoneId(),
                        replicationSpec.getZoneName(),
                        replicationSpec.getNumShards(),
                        replicationSpec.getTotalNodes(),
                        List.of(AWSRegionName.US_WEST_1))))
            .build();

    final AtlasResourcePolicyAuthResponse response =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group,
            new ClusterDescriptionView(awsClusterInvalid),
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            null);
    assertThat(response.decision()).isEqualTo(Decision.Allow);
  }
}
