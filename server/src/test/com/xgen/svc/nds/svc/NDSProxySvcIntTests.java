package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._private.dao.HostClusterDao;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.common._public.model.ProxyVersion;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ProxyConfig;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMPoolDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessFreeMigrationStatusDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessUpgradeToDedicatedStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus.ServerlessFreeMigrationState;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.ProxyAccountsView;
import com.xgen.svc.nds.model.ui.ProxyAccountsView.ProxyAccountView;
import com.xgen.svc.nds.model.ui.ProxyAccountsView.ProxyAccountView.Action;
import com.xgen.svc.nds.model.ui.ProxyAccountsView.ProxyAccountView.LimitsView;
import com.xgen.svc.nds.model.ui.ProxyConfigView;
import com.xgen.svc.nds.serverless.dao.NDSServerlessMockedMetricsDao;
import com.xgen.svc.nds.serverless.dao.ServerlessCloudProviderContainerDao;
import com.xgen.svc.nds.serverless.svc.NDSServerlessLoadSvc;
import com.xgen.svc.nds.serverless.util.ServerlessTestUtil;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.tenant.util.TenantTestUtil;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSProxySvcIntTests extends JUnit5NDSBaseTest {
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ReplicaSetHardwareDao _hardwareDao;
  @Inject private NDSProxySvc _ndsProxySvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private ServerlessMTMPoolDao _serverlessMTMPoolDao;
  @Inject private AWSInstanceHardwareDao _awsInstanceHardwareDao;
  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private ServerlessMTMClusterDao _serverlessMTMClusterDao;
  @Inject private HostClusterDao _hostClusterDao;
  @Inject private HostDao _hostDao;
  @Inject private ReplicaSetHardwareSvc _hardwareSvc;
  @Inject private NDSClusterSvc _clusterSvc;
  @Inject private NDSServerlessMockedMetricsDao _ndsServerlessMockedMetricsDao;
  @Inject private NDSServerlessLoadSvc _ndsServerlessLoadSvc;
  @Inject private ServerlessCloudProviderContainerDao _serverlessContainerDao;
  @Inject private TenantInstanceHardwareSvc _tenantInstanceHardwareSvc;
  @Inject private OrganizationSvc _organizationSvc;
  @Inject private ServerlessFreeMigrationStatusDao _serverlessFreeMigrationStatusDao;
  @Inject private ServerlessUpgradeToDedicatedStatusDao _serverlessUpgradeToDedicatedStatusDao;
  private Organization _serverlessMTMOrganization;
  private AppUser _serverlessMTMUser;
  private Group _serverlessMTMGroup;
  private NDSGroup _serverlessMTMNdsGroup;
  private ClusterDescription _serverlessMTMCluster;

  private ClusterDescription _otherServerlessMTMCluster;

  private ClusterDescription _serverlessCluster;
  private List<InstanceHardware> _backingHardwares;
  private List<String> _backingURIHostCIDRs;
  private Pair<String, ProxyVersion> _outdatedVersion;
  private ProxyConfigView _proxyConfigView;

  private final String MTM_LEGACY_HOSTNAME = "some.host.cc";
  private final long PREVIOUS_VERSION = 0;
  private final long TARGET_VERSION = 1;
  private final String PRIVATE_CIDR = "*************/21";
  private final String PUBLIC_IP = "303.0.808.0/32";
  private final String SERVERLESS_CLUSTER_NAME = "serverless";
  private final String OTHER_SERVERLESS_MTM_CLUSTER_NAME = "otherServerlessMTMCluster";

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();

    TenantTestUtil.makeAWSAccount(_awsAccountDao, "test", "test");
    TenantTestUtil.findOrMakeAWSDNSAccount(_awsAccountDao, "test", "test");

    final ServerlessTestUtil.SetupTenantMTMGroupResults resultsServerlessMTM =
        ServerlessTestUtil.setupServerlessMTMGroupWithProvisionedMTMCluster(
            _ndsGroupDao,
            _ndsGroupSvc,
            _awsAccountDao,
            _clusterDescriptionDao,
            _hardwareDao,
            _awsInstanceHardwareDao,
            _mtmClusterDao,
            _hostClusterDao,
            _hostDao,
            _hardwareSvc,
            _clusterSvc,
            _serverlessMTMPoolDao,
            _ndsServerlessMockedMetricsDao);
    _ndsServerlessLoadSvc.updateServerlessMTMLoadDocuments();

    _serverlessMTMOrganization =
        _organizationSvc.findById(resultsServerlessMTM.getMtmGroup().getOrgId());
    _serverlessMTMUser = resultsServerlessMTM.getMtmUser();
    _serverlessMTMGroup = resultsServerlessMTM.getMtmGroup();
    _serverlessMTMNdsGroup = resultsServerlessMTM.getNdsGroup();
    _serverlessMTMCluster = resultsServerlessMTM.getMtmCluster();

    final ServerlessTestUtil.SetupTenantGroupResults resultsServerless =
        ServerlessTestUtil.setupServerlessGroup(
            _ndsGroupDao,
            _ndsGroupSvc,
            _serverlessContainerDao,
            _clusterDescriptionDao,
            _hardwareDao,
            _hostClusterDao,
            _hostDao,
            _tenantInstanceHardwareSvc,
            _serverlessMTMClusterDao,
            SERVERLESS_CLUSTER_NAME,
            true);
    _serverlessCluster = resultsServerless.getTenantCluster();
    final ObjectId linkedContainerId = resultsServerless.getTenantContainerId();

    // add the tenant's container id to the ServerlessMTMCluster
    _serverlessMTMClusterDao.decrementCapacityForCluster(
        _serverlessCluster.getGroupId(),
        _serverlessCluster.getName(),
        MTMClusterType.SERVERLESS,
        linkedContainerId);

    _serverlessMTMClusterDao.incrementTargetProxyVersionForTenants(
        _serverlessMTMGroup.getId(),
        _serverlessMTMCluster.getName(),
        MTMClusterType.SERVERLESS,
        List.of(_serverlessCluster.getUniqueId().toString()),
        List.of(linkedContainerId));

    final ServerlessTestUtil.SetupTenantMTMGroupResults resultsOtherServerlessMTM =
        ServerlessTestUtil.setupProvisionedMTMClusterWithGroupCreationResults(
            _ndsGroupDao,
            _clusterDescriptionDao,
            _hardwareDao,
            _awsInstanceHardwareDao,
            _mtmClusterDao,
            _hostClusterDao,
            _hostDao,
            _hardwareSvc,
            _clusterSvc,
            _serverlessMTMPoolDao,
            _ndsServerlessMockedMetricsDao,
            CloudProvider.SERVERLESS,
            resultsServerlessMTM,
            OTHER_SERVERLESS_MTM_CLUSTER_NAME,
            CloudProvider.NONE);
    _otherServerlessMTMCluster = resultsOtherServerlessMTM.getMtmCluster();

    final HashMap<String, Long> versionMap = new HashMap<>();
    versionMap.put(MTM_LEGACY_HOSTNAME, PREVIOUS_VERSION);
    final ProxyVersion version = new ProxyVersion(TARGET_VERSION, versionMap);

    _backingHardwares =
        _hardwareDao
            .findByCluster(_serverlessMTMCluster.getGroupId(), _serverlessMTMCluster.getName())
            .stream()
            .flatMap(ReplicaSetHardware::getAllHardware)
            .collect(Collectors.toList());
    _backingURIHostCIDRs = List.of(PRIVATE_CIDR, PUBLIC_IP);
    _outdatedVersion = Pair.of(_serverlessCluster.getUniqueId().toString(), version);
    _proxyConfigView =
        new ProxyConfigView(new ProxyConfig("test", "1", 0L, Map.of(), false, "", false));
  }

  @Test
  public void testBuildAccountView() throws SvcException {
    final Optional<ProxyAccountView> accountView =
        _ndsProxySvc.buildAccountView(
            null,
            _outdatedVersion,
            _serverlessMTMCluster,
            _backingHardwares,
            _backingURIHostCIDRs,
            _proxyConfigView,
            _ndsGroupDao.find(_serverlessMTMGroup.getId()).orElseThrow());

    assertTrue(accountView.isPresent());
  }

  @Test
  public void testBuildAccountView_ServerlessToDedicated_InProgress() throws Exception {
    final ServerlessMTMCluster backingMTM =
        _serverlessMTMClusterDao
            .findServerlessCluster(
                _serverlessMTMCluster.getGroupId(), _serverlessMTMCluster.getName())
            .orElseThrow();

    final BasicDBObject statusObj =
        new ServerlessUpgradeToDedicatedStatus(
                _serverlessCluster.getName(),
                _serverlessCluster.getGroupId(),
                backingMTM.getName(),
                backingMTM.getGroupId(),
                _serverlessCluster.getUniqueId(),
                new Date(),
                AWSRegionName.US_EAST_1,
                _serverlessCluster,
                new ObjectId())
            .toDBObject();
    statusObj.put(
        ServerlessUpgradeToDedicatedStatus.FieldDefs.MIGRATION_HOSTNAME, "migrationHostname.net");

    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        new ServerlessUpgradeToDedicatedStatus(statusObj);
    _serverlessUpgradeToDedicatedStatusDao.save(serverlessUpgradeToDedicatedStatus);

    final Optional<ProxyAccountView> proxyAccountViewOpt =
        _ndsProxySvc.buildAccountView(
            null,
            Pair.of(_serverlessCluster.getUniqueId().toString(), new ProxyVersion(2, Map.of())),
            _serverlessMTMCluster,
            _backingHardwares,
            _backingURIHostCIDRs,
            _proxyConfigView,
            _ndsGroupDao.find(_serverlessMTMGroup.getId()).orElseThrow());

    assertTrue(proxyAccountViewOpt.isPresent());

    final ProxyAccountView proxyAccountsView = proxyAccountViewOpt.get();
    assertEquals(
        _serverlessCluster.getUniqueId().toString() + "_", proxyAccountsView.getDbPrefix());

    // assert that one of the hostnames is the migration hostname
    assertTrue(
        proxyAccountsView.getHosts().stream()
            .anyMatch(hostView -> hostView.getSni().contains("migrationHostname.net")));
  }

  @Test
  public void testValidateProxyAccountViewsForServerless() {
    final ServerlessMTMCluster backingMTM =
        _serverlessMTMClusterDao
            .findServerlessCluster(
                _serverlessMTMCluster.getGroupId(), _serverlessMTMCluster.getName())
            .orElseThrow();

    final ClusterDescription tenantClusterDescription =
        _clusterDescriptionDao
            .findByName(_serverlessCluster.getGroupId(), _serverlessCluster.getName())
            .orElseThrow();

    final ProxyAccountView tenantProxyAccountView =
        new ProxyAccountView(
            1L,
            Action.REMOVE,
            tenantClusterDescription.getGroupId(),
            NDSDefaults.getReplicaSetNameForUnshardedCluster(tenantClusterDescription),
            NDSProxySvc.getAccountPrefix(tenantClusterDescription.getUniqueId().toString()),
            List.of(),
            List.of(),
            LimitsView.getLimitsFromClusterDescription(tenantClusterDescription, false),
            Map.of(),
            List.of(),
            null,
            null,
            null,
            null,
            null,
            tenantClusterDescription.getHostnameSchemeForAgents().orElse(null),
            tenantClusterDescription.getName(),
            false,
            ProxyAccountView.FlexTier.NONE.getProxyTierName(),
            false);

    final ProxyAccountsView proxyAccountsView =
        new ProxyAccountsView(List.of(tenantProxyAccountView), false);

    // should throw since tenant is being removed, but is still active
    assertThrows(
        IllegalStateException.class,
        () -> _ndsProxySvc.validateProxyAccountViewsForServerless(proxyAccountsView, backingMTM));

    // tenant is marked as deleted
    {
      _clusterDescriptionDao.markDeleted(
          tenantClusterDescription.getGroupId(), tenantClusterDescription.getName(), new Date());

      try {
        _ndsProxySvc.validateProxyAccountViewsForServerless(proxyAccountsView, backingMTM);
      } catch (final Exception pE) {
        fail();
      }
    }
  }

  @Test
  public void testValidateProxyAccountViewsForServerless_NoLongerServerless() {
    final ServerlessMTMCluster backingMTM =
        _serverlessMTMClusterDao
            .findServerlessCluster(
                _serverlessMTMCluster.getGroupId(), _serverlessMTMCluster.getName())
            .orElseThrow();

    // Free cluster
    final ClusterDescription clusterDescription_free =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription());
    _clusterDescriptionDao.save(clusterDescription_free);

    final ProxyAccountView tenantProxyAccountView_free =
        new ProxyAccountView(
            1L,
            Action.REMOVE,
            clusterDescription_free.getGroupId(),
            NDSDefaults.getReplicaSetNameForUnshardedCluster(clusterDescription_free),
            NDSProxySvc.getAccountPrefix(clusterDescription_free.getUniqueId().toString()),
            List.of(),
            List.of(),
            LimitsView.getLimitsFromClusterDescription(clusterDescription_free, false),
            Map.of(),
            List.of(),
            null,
            null,
            null,
            null,
            null,
            clusterDescription_free.getHostnameSchemeForAgents().orElse(null),
            clusterDescription_free.getName(),
            false,
            ProxyAccountView.FlexTier.NONE.getProxyTierName(),
            false);

    final ProxyAccountsView proxyAccountsView_free =
        new ProxyAccountsView(List.of(tenantProxyAccountView_free), false);

    try {
      _ndsProxySvc.validateProxyAccountViewsForServerless(proxyAccountsView_free, backingMTM);
    } catch (final Exception pE) {
      fail();
    }

    // Dedicated cluster
    final ClusterDescription clusterDescription_M30 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription_M30);

    final ProxyAccountView tenantProxyAccountView_M30 =
        new ProxyAccountView(
            1L,
            Action.REMOVE,
            clusterDescription_M30.getGroupId(),
            NDSDefaults.getReplicaSetNameForUnshardedCluster(clusterDescription_M30),
            NDSProxySvc.getAccountPrefix(clusterDescription_M30.getUniqueId().toString()),
            List.of(),
            List.of(),
            // This field needs to be some form of tenant so using the free limits for now
            // The code will never get to the point where it sets this to dedicated or free as well
            // since the tenant unique id will no longer exist
            LimitsView.getLimitsFromClusterDescription(clusterDescription_free, false),
            Map.of(),
            List.of(),
            null,
            null,
            null,
            null,
            null,
            clusterDescription_M30.getHostnameSchemeForAgents().orElse(null),
            clusterDescription_M30.getName(),
            false,
            ProxyAccountView.FlexTier.NONE.getProxyTierName(),
            false);

    final ProxyAccountsView proxyAccountsView_M30 =
        new ProxyAccountsView(List.of(tenantProxyAccountView_M30), false);

    try {
      _ndsProxySvc.validateProxyAccountViewsForServerless(proxyAccountsView_M30, backingMTM);
    } catch (final Exception pE) {
      fail();
    }
  }

  @Test
  public void testBuildAccountView_ServerlessToFree() throws Exception {
    final ServerlessMTMCluster backingMTM =
        _serverlessMTMClusterDao
            .findServerlessCluster(
                _serverlessMTMCluster.getGroupId(), _serverlessMTMCluster.getName())
            .orElseThrow();

    // The old serverless information will be stored in the migration document
    final ClusterDescription tenantClusterDescription =
        _clusterDescriptionDao
            .findByName(_serverlessCluster.getGroupId(), _serverlessCluster.getName())
            .orElseThrow();

    // Serverless information replaced with free cluster
    _clusterDescriptionDao.remove(_serverlessCluster.getGroupId(), _serverlessCluster.getName());

    final ClusterDescription clusterDescription_free =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription())
            .copy()
            .setGroupId(tenantClusterDescription.getGroupId())
            .setName(tenantClusterDescription.getName())
            .build();
    _clusterDescriptionDao.save(clusterDescription_free);

    final ServerlessFreeMigrationStatus serverlessFreeMigrationStatus =
        new ServerlessFreeMigrationStatus(
            tenantClusterDescription.getGroupId(),
            tenantClusterDescription.getName(),
            tenantClusterDescription.getUniqueId(),
            backingMTM.getGroupId(),
            backingMTM.getName(),
            ServerlessFreeMigrationState.COMPLETED);
    _serverlessFreeMigrationStatusDao.save(serverlessFreeMigrationStatus);

    final Optional<ProxyAccountView> proxyAccountViewOpt =
        _ndsProxySvc.buildAccountView(
            null,
            Pair.of(
                tenantClusterDescription.getUniqueId().toString(), new ProxyVersion(2, Map.of())),
            _serverlessMTMCluster,
            List.of(),
            List.of(),
            null,
            null);

    assertTrue(proxyAccountViewOpt.isPresent());

    final ProxyAccountView proxyAccountsView = proxyAccountViewOpt.get();
    assertEquals(Action.REMOVE, proxyAccountsView.getAction());
    assertEquals(
        tenantClusterDescription.getUniqueId().toString() + "_", proxyAccountsView.getDbPrefix());
    assertTrue(proxyAccountsView.getIsTenantUpgradeInProgress());
  }

  @Test
  public void testBuildAccountView_ServerlessToDedicated() throws Exception {
    final ServerlessMTMCluster backingMTM =
        _serverlessMTMClusterDao
            .findServerlessCluster(
                _serverlessMTMCluster.getGroupId(), _serverlessMTMCluster.getName())
            .orElseThrow();

    // The old serverless information will be stored in the migration document
    final ClusterDescription tenantClusterDescription =
        _clusterDescriptionDao
            .findByName(_serverlessCluster.getGroupId(), _serverlessCluster.getName())
            .orElseThrow();

    // Serverless information replaced with M30 cluster
    _clusterDescriptionDao.remove(_serverlessCluster.getGroupId(), _serverlessCluster.getName());

    final ClusterDescription clusterDescription_M30 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setGroupId(tenantClusterDescription.getGroupId())
            .setName(tenantClusterDescription.getName())
            .build();
    _clusterDescriptionDao.save(clusterDescription_M30);

    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        new ServerlessUpgradeToDedicatedStatus(
            tenantClusterDescription.getName(),
            tenantClusterDescription.getGroupId(),
            backingMTM.getName(),
            backingMTM.getGroupId(),
            tenantClusterDescription.getUniqueId(),
            new Date(),
            AWSRegionName.US_EAST_1,
            tenantClusterDescription,
            clusterDescription_M30.getUniqueId());

    _serverlessUpgradeToDedicatedStatusDao.save(serverlessUpgradeToDedicatedStatus);
    _serverlessUpgradeToDedicatedStatusDao.setComplete(
        tenantClusterDescription.getGroupId(), serverlessUpgradeToDedicatedStatus.getId());

    final Optional<ProxyAccountView> proxyAccountViewOpt =
        _ndsProxySvc.buildAccountView(
            null,
            Pair.of(
                tenantClusterDescription.getUniqueId().toString(), new ProxyVersion(2, Map.of())),
            _serverlessMTMCluster,
            List.of(),
            List.of(),
            null,
            null);

    assertTrue(proxyAccountViewOpt.isPresent());

    final ProxyAccountView proxyAccountsView = proxyAccountViewOpt.get();
    assertEquals(Action.REMOVE, proxyAccountsView.getAction());
    assertEquals(
        tenantClusterDescription.getUniqueId().toString() + "_", proxyAccountsView.getDbPrefix());
    assertTrue(proxyAccountsView.getIsTenantUpgradeInProgress());
  }
}
