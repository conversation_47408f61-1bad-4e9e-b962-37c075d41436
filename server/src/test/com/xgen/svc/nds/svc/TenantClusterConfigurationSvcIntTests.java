package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.flex._private.dao.FlexMTMClusterDao;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexTenantClusterConfiguration;
import com.xgen.cloud.nds.free._private.dao.SharedMTMClusterDao;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.SharedTenantClusterConfiguration;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantClusterConfiguration;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.Set;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class TenantClusterConfigurationSvcIntTests extends JUnit5NDSBaseTest {

  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private SharedMTMClusterDao _sharedMTMClusterDao;
  @Inject private ServerlessMTMClusterDao _serverlessMTMClusterDao;
  @Inject private FlexMTMClusterDao _flexMTMClusterDao;
  @Inject private TenantClusterConfigurationSvc _tenantClusterConfigurationSvc;

  private ObjectId _isolatedGroupId;
  private ObjectId _unisolatedGroupId;

  private static Set<FreeInstanceSize> FREE_INSTANCE_SIZES =
      Set.of(FreeInstanceSize.M0, FreeInstanceSize.M2, FreeInstanceSize.M5);
  private static Set<ServerlessInstanceSize> SERVERLESS_INSTANCE_SIZES =
      Set.of(ServerlessInstanceSize.SERVERLESS_V2);
  private static Set<FlexInstanceSize> FLEX_INSTANCE_SIZES = Set.of(FlexInstanceSize.FLEX);

  private static Set<RegionName> FOUR_ZERO_REGIONS = Set.of(AWSRegionName.US_EAST_1);
  private static Set<RegionName> FOUR_TWO_REGIONS =
      Set.of(
          AWSRegionName.US_EAST_1,
          AWSRegionName.AP_SOUTH_1,
          GCPRegionName.WESTERN_EUROPE,
          AzureRegionName.US_EAST_2,
          AWSRegionName.AF_SOUTH_1);
  private static Set<RegionName> FOUR_FOUR_REGIONS = Set.of(AWSRegionName.US_WEST_1);

  private static Pair<String, String> FOUR_ZERO_VERSION_NAME = Pair.of("4.0", "fourZero");
  private static Pair<String, String> FOUR_TWO_VERSION_NAME = Pair.of("4.2", "fourTwo");
  private static Pair<String, String> FOUR_FOUR_VERSION_NAME = Pair.of("4.4", "fourFour");

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _mtmClusterDao.ensureIndexes();
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    final Group isolatedGroup = MmsFactory.createGroup(organization);
    final Group unisolatedGroup = MmsFactory.createGroup(organization);

    _isolatedGroupId = isolatedGroup.getId();
    _unisolatedGroupId = unisolatedGroup.getId();

    setUpMTMClusters();
  }

  private void setUpMTMClusters() {
    IntStream.range(0, 15)
        .forEach(
            i -> {
              FREE_INSTANCE_SIZES.forEach(
                  instanceSize -> {
                    FOUR_ZERO_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getSharedMTMCluster(
                                  getMTMClusterName(
                                      instanceSize, region, FOUR_ZERO_VERSION_NAME.getRight(), i),
                                  instanceSize,
                                  region,
                                  FOUR_ZERO_VERSION_NAME.getLeft()));
                        });
                    FOUR_TWO_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getSharedMTMCluster(
                                  getMTMClusterName(
                                      instanceSize, region, FOUR_TWO_VERSION_NAME.getRight(), i),
                                  instanceSize,
                                  region,
                                  FOUR_TWO_VERSION_NAME.getLeft()));
                        });
                    FOUR_FOUR_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getSharedMTMCluster(
                                  getMTMClusterName(
                                      instanceSize,
                                      region,
                                      FOUR_FOUR_VERSION_NAME.getRight(),
                                      i,
                                      true),
                                  instanceSize,
                                  region,
                                  FOUR_FOUR_VERSION_NAME.getLeft(),
                                  Set.of(_isolatedGroupId)));
                        });
                  });
              SERVERLESS_INSTANCE_SIZES.forEach(
                  instanceSize -> {
                    FOUR_ZERO_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getServerlessMTMCluster(
                                  getMTMClusterName(
                                      instanceSize, region, FOUR_ZERO_VERSION_NAME.getRight(), i),
                                  instanceSize,
                                  region,
                                  FOUR_ZERO_VERSION_NAME.getLeft()));
                        });
                    FOUR_TWO_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getServerlessMTMCluster(
                                  getMTMClusterName(
                                      instanceSize, region, FOUR_TWO_VERSION_NAME.getRight(), i),
                                  instanceSize,
                                  region,
                                  FOUR_TWO_VERSION_NAME.getLeft()));
                        });
                    FOUR_FOUR_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getServerlessMTMCluster(
                                  getMTMClusterName(
                                      instanceSize,
                                      region,
                                      FOUR_FOUR_VERSION_NAME.getRight(),
                                      i,
                                      true),
                                  instanceSize,
                                  region,
                                  FOUR_FOUR_VERSION_NAME.getLeft(),
                                  Set.of(_isolatedGroupId),
                                  new Date(),
                                  Date.from(Instant.now().minus(Duration.ofMinutes(10)))));
                        });
                  });

              FLEX_INSTANCE_SIZES.forEach(
                  instanceSize -> {
                    FOUR_ZERO_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getFlexMTMCluster(
                                  getMTMClusterName(
                                      instanceSize, region, FOUR_ZERO_VERSION_NAME.getRight(), i),
                                  instanceSize,
                                  region,
                                  FOUR_ZERO_VERSION_NAME.getLeft()));
                        });
                    FOUR_TWO_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getFlexMTMCluster(
                                  getMTMClusterName(
                                      instanceSize, region, FOUR_TWO_VERSION_NAME.getRight(), i),
                                  instanceSize,
                                  region,
                                  FOUR_TWO_VERSION_NAME.getLeft()));
                        });
                    FOUR_FOUR_REGIONS.forEach(
                        region -> {
                          _mtmClusterDao.saveReplicaSafe(
                              NDSModelTestFactory.getFlexMTMCluster(
                                  getMTMClusterName(
                                      instanceSize,
                                      region,
                                      FOUR_FOUR_VERSION_NAME.getRight(),
                                      i,
                                      true),
                                  instanceSize,
                                  region,
                                  FOUR_FOUR_VERSION_NAME.getLeft(),
                                  Set.of(_isolatedGroupId)));
                        });
                  });
            });
  }

  @Test
  public void testGetAllAvailableSharedTenantClusterConfigurations_NoMTMs() {
    _mtmClusterDao.remove(new BasicDBObject());
    _tenantClusterConfigurationSvc.clearCache();
    assertEquals(
        0,
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_unisolatedGroupId)
            .size());
    assertEquals(
        0,
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_isolatedGroupId)
            .size());
  }

  @Test
  public void testGetAllAvailableSharedTenantClusterConfigurations() {
    assertEquals(525, _mtmClusterDao.findAllClusters().size());
    assertEquals(315, _sharedMTMClusterDao.findAllSharedClusters().size());
    _tenantClusterConfigurationSvc.clearCache();

    // not isolated
    assertEquals(
        18,
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_unisolatedGroupId)
            .size());
    assertTrue(
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_unisolatedGroupId)
            .contains(
                new SharedTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    FreeInstanceSize.M2,
                    FOUR_TWO_VERSION_NAME.getLeft())));
    assertFalse(
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_unisolatedGroupId)
            .contains(
                new SharedTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    FreeInstanceSize.M2,
                    FOUR_ZERO_VERSION_NAME.getLeft())));

    // isolated
    assertEquals(
        3,
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_isolatedGroupId)
            .size());
    assertTrue(
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_isolatedGroupId)
            .contains(
                new SharedTenantClusterConfiguration(
                    AWSRegionName.US_WEST_1,
                    FreeInstanceSize.M2,
                    FOUR_FOUR_VERSION_NAME.getLeft())));
    assertFalse(
        _tenantClusterConfigurationSvc
            .getAllAvailableSharedTenantClusterConfigurations(_isolatedGroupId)
            .contains(
                new SharedTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    FreeInstanceSize.M2,
                    FOUR_TWO_VERSION_NAME.getLeft())));

    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testGetAllAvailableServerlessTenantClusterConfigurations_NoMTMs() {
    _mtmClusterDao.remove(new BasicDBObject());
    _tenantClusterConfigurationSvc.clearCache();
    assertEquals(
        0,
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_unisolatedGroupId)
            .size());
    assertEquals(
        0,
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_isolatedGroupId)
            .size());
  }

  @Test
  public void testGetAllAvailableServerlessTenantClusterConfigurations() {
    assertEquals(525, _mtmClusterDao.findAllClusters().size());
    assertEquals(105, _serverlessMTMClusterDao.findAllServerlessClusters().size());
    _tenantClusterConfigurationSvc.clearCache();

    // not isolated
    assertEquals(
        6,
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_unisolatedGroupId)
            .size());
    assertTrue(
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_unisolatedGroupId)
            .contains(
                new ServerlessTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    ServerlessInstanceSize.SERVERLESS_V2,
                    FOUR_TWO_VERSION_NAME.getLeft())));
    assertFalse(
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_unisolatedGroupId)
            .contains(
                new ServerlessTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    ServerlessInstanceSize.SERVERLESS_V2,
                    FOUR_ZERO_VERSION_NAME.getLeft())));

    // isolated
    assertEquals(
        1,
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_isolatedGroupId)
            .size());
    assertTrue(
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_isolatedGroupId)
            .contains(
                new ServerlessTenantClusterConfiguration(
                    AWSRegionName.US_WEST_1,
                    ServerlessInstanceSize.SERVERLESS_V2,
                    FOUR_FOUR_VERSION_NAME.getLeft())));
    assertFalse(
        _tenantClusterConfigurationSvc
            .getAllAvailableServerlessTenantClusterConfigurations(_isolatedGroupId)
            .contains(
                new ServerlessTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    ServerlessInstanceSize.SERVERLESS_V2,
                    FOUR_TWO_VERSION_NAME.getLeft())));

    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testGetAllAvailableFlexTenantClusterConfigurations_NoMTMs() {
    _mtmClusterDao.remove(new BasicDBObject());
    _tenantClusterConfigurationSvc.clearCache();
    assertEquals(
        0,
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_unisolatedGroupId)
            .size());
    assertEquals(
        0,
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_isolatedGroupId)
            .size());
  }

  @Test
  public void testGetAllAvailableFlexTenantClusterConfigurations() {
    assertEquals(525, _mtmClusterDao.findAllClusters().size());
    assertEquals(105, _flexMTMClusterDao.findAllFlexMTMClusters().size());
    _tenantClusterConfigurationSvc.clearCache();

    // not isolated
    assertEquals(
        6,
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_unisolatedGroupId)
            .size());
    assertTrue(
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_unisolatedGroupId)
            .contains(
                new FlexTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    FlexInstanceSize.FLEX,
                    FOUR_TWO_VERSION_NAME.getLeft())));
    assertFalse(
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_unisolatedGroupId)
            .contains(
                new FlexTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    FlexInstanceSize.FLEX,
                    FOUR_ZERO_VERSION_NAME.getLeft())));

    // isolated
    assertEquals(
        1,
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_isolatedGroupId)
            .size());
    assertTrue(
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_isolatedGroupId)
            .contains(
                new FlexTenantClusterConfiguration(
                    AWSRegionName.US_WEST_1,
                    FlexInstanceSize.FLEX,
                    FOUR_FOUR_VERSION_NAME.getLeft())));
    assertFalse(
        _tenantClusterConfigurationSvc
            .getAllAvailableFlexTenantClusterConfigurations(_isolatedGroupId)
            .contains(
                new FlexTenantClusterConfiguration(
                    GCPRegionName.WESTERN_EUROPE,
                    FlexInstanceSize.FLEX,
                    FOUR_TWO_VERSION_NAME.getLeft())));

    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testGetAllAvailableSharedMTMRegionViewsForAdmin() {
    _tenantClusterConfigurationSvc.clearCache();
    assertEquals(
        6, _tenantClusterConfigurationSvc.getAllAvailableSharedMTMRegionViewsForAdmin().size());
    assertTrue(
        _tenantClusterConfigurationSvc.getAllAvailableSharedMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_EAST_1.getValue())));
    assertTrue(
        _tenantClusterConfigurationSvc.getAllAvailableSharedMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_WEST_1.getValue())));
    assertFalse(
        _tenantClusterConfigurationSvc.getAllAvailableSharedMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_WEST_2.getValue())));
    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testGetAllAvailableServerlessMTMRegionViewsForAdmin() {
    _tenantClusterConfigurationSvc.clearCache();
    assertEquals(
        6, _tenantClusterConfigurationSvc.getAllAvailableServerlessMTMRegionViewsForAdmin().size());
    assertTrue(
        _tenantClusterConfigurationSvc.getAllAvailableServerlessMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_EAST_1.getValue())));
    assertTrue(
        _tenantClusterConfigurationSvc.getAllAvailableServerlessMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_WEST_1.getValue())));
    assertFalse(
        _tenantClusterConfigurationSvc.getAllAvailableServerlessMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_WEST_2.getValue())));
    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testGetAllAvailableFlexMTMRegionViewsForAdmin() {
    _tenantClusterConfigurationSvc.clearCache();
    assertEquals(
        6, _tenantClusterConfigurationSvc.getAllAvailableFlexMTMRegionViewsForAdmin().size());
    assertTrue(
        _tenantClusterConfigurationSvc.getAllAvailableFlexMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_EAST_1.getValue())));
    assertTrue(
        _tenantClusterConfigurationSvc.getAllAvailableFlexMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_WEST_1.getValue())));
    assertFalse(
        _tenantClusterConfigurationSvc.getAllAvailableFlexMTMRegionViewsForAdmin().stream()
            .map(RegionView::getName)
            .anyMatch(regionName -> regionName.equals(AWSRegionName.US_WEST_2.getValue())));
    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testIsSharedTenantClusterConfigurationAvailable() {
    _tenantClusterConfigurationSvc.clearCache();

    // not isolated
    assertTrue(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            GCPRegionName.WESTERN_EUROPE,
            FreeInstanceSize.M5,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            GCPRegionName.ASIA_EAST_2,
            FreeInstanceSize.M5,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            AzureRegionName.EUROPE_NORTH,
            FreeInstanceSize.M5,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            AWSRegionName.US_WEST_1,
            FreeInstanceSize.M5,
            FOUR_FOUR_VERSION_NAME.getLeft()));

    // isolated
    assertTrue(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            AWSRegionName.US_WEST_1,
            FreeInstanceSize.M5,
            FOUR_FOUR_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            GCPRegionName.WESTERN_EUROPE,
            FreeInstanceSize.M5,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            GCPRegionName.ASIA_EAST_2,
            FreeInstanceSize.M5,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isSharedTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            AzureRegionName.EUROPE_NORTH,
            FreeInstanceSize.M5,
            FOUR_TWO_VERSION_NAME.getLeft()));

    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testIsServerlessTenantClusterConfigurationAvailable() {
    _tenantClusterConfigurationSvc.clearCache();

    // not isolated
    assertTrue(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            GCPRegionName.WESTERN_EUROPE,
            ServerlessInstanceSize.SERVERLESS_V2));
    assertFalse(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _unisolatedGroupId, GCPRegionName.ASIA_EAST_2, ServerlessInstanceSize.SERVERLESS_V2));
    assertFalse(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            AzureRegionName.EUROPE_NORTH,
            ServerlessInstanceSize.SERVERLESS_V2));
    assertFalse(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _unisolatedGroupId, AWSRegionName.US_WEST_1, ServerlessInstanceSize.SERVERLESS_V2));

    // isolated
    assertTrue(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _isolatedGroupId, AWSRegionName.US_WEST_1, ServerlessInstanceSize.SERVERLESS_V2));
    assertFalse(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _isolatedGroupId, GCPRegionName.WESTERN_EUROPE, ServerlessInstanceSize.SERVERLESS_V2));
    assertFalse(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _isolatedGroupId, GCPRegionName.ASIA_EAST_2, ServerlessInstanceSize.SERVERLESS_V2));
    assertFalse(
        _tenantClusterConfigurationSvc.isServerlessTenantClusterConfigurationAvailable(
            _isolatedGroupId, AzureRegionName.EUROPE_NORTH, ServerlessInstanceSize.SERVERLESS_V2));

    _tenantClusterConfigurationSvc.clearCache();
  }

  @Test
  public void testIsFlexTenantClusterConfigurationAvailable() {
    _tenantClusterConfigurationSvc.clearCache();

    // not isolated
    assertTrue(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            GCPRegionName.WESTERN_EUROPE,
            FlexInstanceSize.FLEX,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            GCPRegionName.ASIA_EAST_2,
            FlexInstanceSize.FLEX,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            AzureRegionName.EUROPE_NORTH,
            FlexInstanceSize.FLEX,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _unisolatedGroupId,
            AWSRegionName.US_WEST_1,
            FlexInstanceSize.FLEX,
            FOUR_FOUR_VERSION_NAME.getLeft()));

    // isolated
    assertTrue(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            AWSRegionName.US_WEST_1,
            FlexInstanceSize.FLEX,
            FOUR_FOUR_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            GCPRegionName.WESTERN_EUROPE,
            FlexInstanceSize.FLEX,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            GCPRegionName.ASIA_EAST_2,
            FlexInstanceSize.FLEX,
            FOUR_TWO_VERSION_NAME.getLeft()));
    assertFalse(
        _tenantClusterConfigurationSvc.isFlexTenantClusterConfigurationAvailable(
            _isolatedGroupId,
            AzureRegionName.EUROPE_NORTH,
            FlexInstanceSize.FLEX,
            FOUR_TWO_VERSION_NAME.getLeft()));

    _tenantClusterConfigurationSvc.clearCache();
  }

  private static String getMTMClusterName(
      final InstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoVersionName,
      final int pIndex,
      final boolean pIsIsolated) {
    return String.format(
        "%s_%s_%s_%d%s",
        pInstanceSize.name(),
        pRegionName.getName(),
        pMongoVersionName,
        pIndex,
        pIsIsolated ? "_isolated" : "");
  }

  private static String getMTMClusterName(
      final InstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoVersionName,
      final int pIndex) {
    return getMTMClusterName(pInstanceSize, pRegionName, pMongoVersionName, pIndex, false);
  }
}
