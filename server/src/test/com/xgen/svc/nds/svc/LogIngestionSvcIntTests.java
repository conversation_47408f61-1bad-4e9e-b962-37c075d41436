package com.xgen.svc.nds.svc;

import static java.time.Instant.now;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.FieldDefs;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.ReplicaSetType;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.logingestion._private.dao.LogIngestionLogsDao;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry.Pagination;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry.SourceMetadata;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry.SourceMetadata.Process;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntryMatchResults;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.LogIngestionRulesDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.LogIngestionRule;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class LogIngestionSvcIntTests extends JUnit5BaseSvcTest {
  private static final String CLUSTER_NAME = "Cluster";
  private static final String ANALYTICS_HOSTNAME = "myhost.analytics";
  private static final String BASE_HOSTNAME = "myhost.base";

  private static final int TEST_PAGINATION_ELEMENTS_LIMIT = 1;

  @Inject private AppSettings _appSettings;
  @Inject private LogIngestionRulesDao _logIngestionRulesDao;
  @Inject private LogIngestionLogsDao _logIngestionLogsDao;
  @Inject private LogIngestionSvc _logIngestionSvc;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  private AppUser _requester;
  private Organization _organization;
  private Group _group;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _requester = MmsFactory.createUser("first.last");
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization);
    _ndsGroupSvc.ensureGroup(_group.getId());
    createAsymmetricClusterAndReplicaSetHardware(_group.getId(), CLUSTER_NAME);

    _appSettings.setProp(
        "mms.nds.logingestion.api.maxresults",
        String.format("%d", TEST_PAGINATION_ELEMENTS_LIMIT),
        SettingType.MEMORY);
  }

  @AfterEach
  public void tearDown() {
    _appSettings.clearMemory();
  }

  @Test
  public void testCreateRule() throws SvcException {
    final LogIngestionRule logIngestionRule =
        getLogIngestionRule("Rule1", _requester.getUsername());

    _logIngestionSvc.createRule(logIngestionRule);
    assertTrue(_logIngestionRulesDao.findByName(logIngestionRule.getName()).isPresent());

    try {
      _logIngestionSvc.createRule(logIngestionRule);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.RULE_ALREADY_EXISTS, pE.getErrorCode());
    }

    try {
      final LogIngestionRule logIngestionRuleBadRequester =
          getLogIngestionRule("Rule2", "bad.requester");
      _logIngestionSvc.createRule(logIngestionRuleBadRequester);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.USERNAME_NOT_FOUND, pE.getErrorCode());
    }
  }

  @Test
  public void testGetRules() {
    final LogIngestionRule logIngestionRule1 =
        getLogIngestionRule("Rule1", _requester.getUsername());
    final LogIngestionRule logIngestionRule2 =
        getLogIngestionRule("Rule2", _requester.getUsername());

    assertEquals(0, _logIngestionSvc.getRules().size());
    _logIngestionRulesDao.create(logIngestionRule1);
    _logIngestionRulesDao.create(logIngestionRule2);
    assertEquals(2, _logIngestionSvc.getRules().size());
  }

  @Test
  public void testRemoveRule() throws SvcException {
    final LogIngestionRule logIngestionRule = getLogIngestionRule("Rule", _requester.getUsername());

    _logIngestionRulesDao.create(logIngestionRule);

    _logIngestionSvc.removeRule(logIngestionRule.getId());
    assertFalse(_logIngestionRulesDao.findByName(logIngestionRule.getName()).isPresent());

    try {
      _logIngestionSvc.removeRule(logIngestionRule.getId());
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.RULE_DOES_NOT_EXIST, pE.getErrorCode());
    }
  }

  @Test
  public void testReplaceRule() throws SvcException {
    final LogIngestionRule logIngestionRule = getLogIngestionRule("Rule", _requester.getUsername());

    _logIngestionRulesDao.create(logIngestionRule);

    final LogIngestionRule newRule =
        getLogIngestionRule("NewRule", _requester.getUsername(), logIngestionRule.getId());
    _logIngestionSvc.replaceRule(newRule);

    assertTrue(_logIngestionRulesDao.findByName(newRule.getName()).isPresent());
    assertFalse(_logIngestionRulesDao.findByName(logIngestionRule.getName()).isPresent());

    try {
      final LogIngestionRule bogusRule = getLogIngestionRule("Bogus", _requester.getUsername());
      _logIngestionSvc.replaceRule(bogusRule);
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.RULE_DOES_NOT_EXIST, pE.getErrorCode());
    }
  }

  @Test
  public void testInsertLogEntries_excludeOutsideRetentionWindow() throws Exception {
    final LogIngestionSvc svc = spy(_logIngestionSvc);
    final SourceMetadata metadata = new SourceMetadata(getSourceMetadataDBObject());
    doReturn(metadata).when(svc).getSourceMetadata(anyString(), any());

    final LogIngestionRule logIngestionRule = getLogIngestionRule("Rule", _requester.getUsername());

    svc.createRule(logIngestionRule);

    final ObjectId groupId = ObjectId.get();

    final Date now = new Date();
    final Duration retention = Duration.ofDays(7);
    final Date justInsideRetentionWindow =
        Date.from(now().minus(retention).plus(1, ChronoUnit.DAYS));
    final Date outsideRetentionWindow = Date.from(now().minus(retention).minus(1, ChronoUnit.DAYS));

    final LogIngestionLogEntry entry1 =
        getLogIngestionLogEntry(logIngestionRule.getId(), now, 75800);
    final LogIngestionLogEntry entry2 =
        getLogIngestionLogEntry(logIngestionRule.getId(), justInsideRetentionWindow, 75800);
    final LogIngestionLogEntry entry3 =
        getLogIngestionLogEntry(logIngestionRule.getId(), outsideRetentionWindow, 75800);

    // Insert log entries
    // Sleep to allow batched writer to do the writes
    svc.insertLogEntries(
        groupId, logIngestionRule.getId(), List.of(entry1, entry2, entry3), "myhost.local");
    Thread.sleep(3_000);

    final List<LogIngestionLogEntry> writtenEntries = _logIngestionLogsDao.findAll();

    // entry3 is excluded due to being outside the retention window
    assertEquals(2, writtenEntries.size());
    assertTrue(writtenEntries.stream().anyMatch(entry -> entry.getId().equals(entry1.getId())));
    assertTrue(writtenEntries.stream().anyMatch(entry -> entry.getId().equals(entry2.getId())));
  }

  @Test
  public void testGetSourceMetaData() throws SvcException {
    final SourceMetadata analyticsMetadata =
        _logIngestionSvc.getSourceMetadata(ANALYTICS_HOSTNAME, _group.getId());
    assertEquals(AWSNDSInstanceSize.M10, analyticsMetadata.getInstanceSize());
    // Verify that clusterType is populated (should be REPLICASET for regular clusters)
    assertNotNull(analyticsMetadata.getClusterType());
    assertEquals(ClusterType.REPLICASET, analyticsMetadata.getClusterType());

    final SourceMetadata baseMetadata =
        _logIngestionSvc.getSourceMetadata(BASE_HOSTNAME + "0", _group.getId());
    assertEquals(AWSNDSInstanceSize.M30, baseMetadata.getInstanceSize());
    // Verify that clusterType is populated (should be REPLICASET for regular clusters)
    assertNotNull(baseMetadata.getClusterType());
    assertEquals(ClusterType.REPLICASET, baseMetadata.getClusterType());
  }

  @Test
  public void testGetSourceMetadata_ClusterTypeInApiResponse() throws SvcException {
    // This test verifies that the clusterType field is properly populated in the SourceMetadata
    // returned by getSourceMetadata method

    final SourceMetadata sourceMetadata =
        _logIngestionSvc.getSourceMetadata(BASE_HOSTNAME + "0", _group.getId());

    // Verify that the SourceMetadata object has the clusterType field populated
    // For regular clusters, it should be REPLICASET
    assertNotNull(sourceMetadata.getClusterType());
    assertEquals(ClusterType.REPLICASET, sourceMetadata.getClusterType());
  }

  @Test
  public void testFindMatchesForRuleIdsSinceOptionalTimestamp() {
    // This is overridden in test setup and is key to our pagination testing
    assertEquals(1, _appSettings.getIntProp("mms.nds.logingestion.api.maxresults"));

    // setup test data: 5 rule ids, each with logs over 5 days
    final SourceMetadata source =
        new SourceMetadata(
            new ObjectId(),
            new ReplicaSetHardware(
                ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareEmptyHardware(0)),
            List.of(Process.MONGOD),
            "",
            CloudProvider.AWS,
            AWSNDSInstanceSize.M10,
            AWSRegionName.US_EAST_1,
            Version.fromString(NDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION),
            new ObjectId(),
            "test-cluster",
            ClusterType.REPLICASET);

    // Three rule ids, two will be matched
    final BasicDBObject projection = new BasicDBObject().append("foo", 1).append("bar", 2);

    final Date date0 = new Date();
    final Date date1 = DateUtils.addHours(date0, 1);
    final Date date2 = DateUtils.addHours(date0, 2);
    final Date dateBeforeAll = DateUtils.addHours(date0, -1);
    final Date dateAfterAll = DateUtils.addHours(date2, 1);

    final LogIngestionLogEntry firstRuleMatch =
        new LogIngestionLogEntry(oid(105), oid(1), projection, source, date0, 1L);
    final LogIngestionLogEntry secondRuleMatch =
        new LogIngestionLogEntry(oid(205), oid(2), projection, source, date1, 1L);
    final LogIngestionLogEntry thirdRuleMatch =
        new LogIngestionLogEntry(oid(305), oid(3), projection, source, date2, 1L);

    _logIngestionLogsDao.insertManyMajority(
        List.of(firstRuleMatch, secondRuleMatch, thirdRuleMatch));

    // find the first of two matches by date
    final LogIngestionLogEntryMatchResults firstByDate =
        _logIngestionSvc.findMatchesForRuleIdsSinceOptionalTimestamp(
            List.of(oid(1), oid(2)), dateBeforeAll, null);
    assertEquals(List.of(firstRuleMatch), firstByDate.logEntries());
    assertTrue(firstByDate.hasNextPage());

    // find the first of two matches by date
    final LogIngestionLogEntryMatchResults secondByDate =
        _logIngestionSvc.findMatchesForRuleIdsSinceOptionalTimestamp(
            List.of(oid(1), oid(2)),
            dateBeforeAll,
            new Pagination(
                firstByDate.logEntries().get(0).getLogTimestamp(),
                firstByDate.logEntries().get(0).getId()));
    assertEquals(List.of(secondRuleMatch), secondByDate.logEntries());
    assertFalse(secondByDate.hasNextPage());

    // Find something from the future - no results
    final LogIngestionLogEntryMatchResults fromTheFuture =
        _logIngestionSvc.findMatchesForRuleIdsSinceOptionalTimestamp(
            List.of(oid(1), oid(2)), dateAfterAll, null);
    assertEquals(List.of(), fromTheFuture.logEntries());
    assertFalse(fromTheFuture.hasNextPage());
  }

  private static Date date(String pDate) {
    return Date.from(Instant.parse(pDate));
  }

  private LogIngestionRule getLogIngestionRule(final String pName, final String pRequester) {
    return getLogIngestionRule(pName, pRequester, ObjectId.get());
  }

  private LogIngestionRule getLogIngestionRule(
      final String pName, final String pRequester, final ObjectId id) {
    return new LogIngestionRule.Builder()
        .setId(id)
        .setName(pName)
        .setRequester(pRequester)
        .setLogType(LogIngestionRule.LogType.MONGOD)
        .setSourceClusters(
            new LogIngestionRule.SourceClusters.Builder()
                .setClusterTiers(List.of("M10"))
                .setMongoDBVersions(List.of("4.4.0"))
                .build())
        .setFormat(LogIngestionRule.Format.REGEX)
        .setQuery("^(regex)$")
        .setProjection("$1")
        .setResultsPerHour(10)
        .build();
  }

  private LogIngestionLogEntry getLogIngestionLogEntry(
      final ObjectId pRuleId, final Date pLogTimestamp, final int pElapsedTime) {
    return new LogIngestionLogEntry(
        new BasicDBObject()
            .append(LogIngestionLogEntry.FieldDefs.ID, ObjectId.get())
            .append(LogIngestionLogEntry.FieldDefs.RULE_ID, pRuleId)
            .append(
                LogIngestionLogEntry.FieldDefs.PROJECTION,
                new BasicDBObject("string", "the quick brown fox"))
            .append(LogIngestionLogEntry.FieldDefs.SOURCE_METADATA, getSourceMetadataDBObject())
            .append(LogIngestionLogEntry.FieldDefs.LOG_TIMESTAMP, pLogTimestamp)
            .append(LogIngestionLogEntry.FieldDefs.ELAPSED_TIME, pElapsedTime));
  }

  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  private BasicDBObject getSourceMetadataDBObject() {
    return new BasicDBObject()
        .append(SourceMetadata.FieldDefs.CLUSTER_UNIQUE_ID, ObjectId.get())
        .append(SourceMetadata.FieldDefs.REPLICA_SET_TYPE, ReplicaSetType.SHARD.name())
        .append(
            SourceMetadata.FieldDefs.NODE_PROCESSES,
            List.of(SourceMetadata.Process.MONGOD, SourceMetadata.Process.MONGOS).stream()
                .map(process -> process.name())
                .collect(DbUtils.toBasicDBList()))
        .append(SourceMetadata.FieldDefs.NODE_HOSTNAME, "myhost.local")
        .append(SourceMetadata.FieldDefs.CLOUD_PROVIDER, CloudProvider.AWS.name())
        .append(SourceMetadata.FieldDefs.INSTANCE_SIZE, AWSNDSInstanceSize.M30.name())
        .append(SourceMetadata.FieldDefs.REGION, AWSRegionName.US_EAST_1.getName())
        .append(SourceMetadata.FieldDefs.MONGODB_VERSION, VersionUtils.Version.fromString("4.2.0"))
        .append(SourceMetadata.FieldDefs.CLUSTER_TYPE, ClusterType.REPLICASET.name());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  private void createAsymmetricClusterAndReplicaSetHardware(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription(pGroupId, pClusterName, true);
    final ClusterDescription symmetricClusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final HardwareSpec.Builder analyticsHardwareSpec =
        symmetricClusterDescription
            .getOnlyHardwareSpec(NodeType.ANALYTICS)
            .orElseThrow()
            .copy()
            .setInstanceSize(AWSNDSInstanceSize.M10);
    final ClusterDescription asymmetricClusterDescription =
        symmetricClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(analyticsHardwareSpec, NodeTypeFamily.ANALYTICS)
            .build();
    _clusterDescriptionDao.save(asymmetricClusterDescription);

    final ObjectId dataBearingRSHID =
        asymmetricClusterDescription.getReplicationSpecsWithShardData().get(0).getId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(CLUSTER_NAME, _group.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(CLUSTER_NAME, 0),
        true,
        false,
        dataBearingRSHID);
    final List<InstanceHardware> instanceHardwares = new ArrayList<>();
    IntStream.range(0, 4)
        .forEach(
            index -> {
              final BasicDBObject ihDBObject =
                  InstanceHardware.getEmptyHardware(
                      CloudProvider.AWS, new ObjectId(), new Date(), index);
              if (index == 3) {
                ihDBObject.put(FieldDefs.INSTANCE_SIZE, AWSNDSInstanceSize.M10);
                ihDBObject.put(FieldDefs.HOSTNAMES, new Hostnames(ANALYTICS_HOSTNAME).toDBList());
              } else {
                ihDBObject.put(FieldDefs.INSTANCE_SIZE, AWSNDSInstanceSize.M30);
                ihDBObject.put(
                    FieldDefs.HOSTNAMES, new Hostnames(BASE_HOSTNAME + index).toDBList());
              }
              instanceHardwares.add(new AWSInstanceHardware(ihDBObject));
            });
    _replicaSetHardwareDao.setCloudProviderHardware(
        replicaSetHardwareId, new BasicDBList(), instanceHardwares, false);
  }
}
