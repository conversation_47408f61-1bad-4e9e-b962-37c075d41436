package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue._public.svc.JobHandlerSvc;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.metering._private.dao.GCPDailyBillingChargeDao;
import com.xgen.cloud.nds.metering._public.model.GCPDailyBillingCharge;
import com.xgen.cloud.nds.metering._public.model.GCPDailyBillingCharge.GCPDailyBillingSKU;
import com.xgen.cloud.nds.metering._public.model.GCPDailyBillingCharge.GCPDailyBillingSKUUnit;
import com.xgen.cloud.nds.project._private.dao.NDSDailyBillingJobDao;
import com.xgen.module.metering.client.svc.IMeterReportSvc;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class GCPDailyBillingIntTests extends JUnit5BaseSvcTest {

  @Inject private JobHandlerSvc _jobHandlerSvc;
  @Inject private GCPDailyBillingChargeDao _gcpDailyBillingChargeDao;
  @Inject private MeterUsage.Builder.Factory _meterUsageFactory;
  @Inject private NDSDailyBillingJobDao _dailyBillingJobDao;
  private NDSBigQueryBillingSvc _bigQueryBillingSvc;
  private GCPDailyMeterUsageCheckJobHandler _usageCheckJobHandler;
  private GCPDailyUsageSubmissionJobHandler _submissionJobHandler;
  private GCPDailyMeterUsageCheckAuditJobHandler _auditJobHandler;
  private Date _usageDate;
  private GCPDailyBillingSKU _sku;
  private ObjectId _groupId;
  private GCPRegionName _region;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _bigQueryBillingSvc = mock(NDSBigQueryBillingSvc.class);
    final IMeterReportSvc _meterReportSvc = mock(IMeterReportSvc.class);
    _usageCheckJobHandler =
        new GCPDailyMeterUsageCheckJobHandler(
            _bigQueryBillingSvc, _gcpDailyBillingChargeDao, _dailyBillingJobDao, _jobHandlerSvc);
    _submissionJobHandler =
        new GCPDailyUsageSubmissionJobHandler(
            _jobHandlerSvc, _meterReportSvc, _meterUsageFactory, _gcpDailyBillingChargeDao);
    _auditJobHandler =
        new GCPDailyMeterUsageCheckAuditJobHandler(
            _jobHandlerSvc, _bigQueryBillingSvc, _gcpDailyBillingChargeDao);
    _usageDate = DateUtils.truncate(new Date(), Calendar.DATE);
    _groupId = new ObjectId();
    _region = GCPRegionName.ASIA_EAST_2;
  }

  private BasicDBObject getJobParams() {
    return new BasicDBObject()
        .append(NDSDailyMeterUsageCheckSvc.USAGE_DATE, _usageDate)
        .append(NDSDailyMeterUsageCheckSvc.SKU_TYPE, _sku);
  }

  private double convertBillingUnitsToChargeUnits(final double pAmount) {
    if (_sku == GCPDailyBillingSKU.GCP_PRIVATE_ENDPOINT_CAPACITY_USAGE) {
      return Units.convert(pAmount, Units.GIGABYTES, Units.BYTES);
    } else if (_sku == GCPDailyBillingSKU.GCP_PRIVATE_ENDPOINT_USAGE) {
      return Units.convert(pAmount, Units.HOURS, Units.SECONDS);
    }
    throw new IllegalArgumentException(String.format("Invalid sku %s", _sku));
  }

  private GCPDailyBillingSKUUnit getSKUUnit() {
    if (_sku == GCPDailyBillingSKU.GCP_PRIVATE_ENDPOINT_CAPACITY_USAGE) {
      return GCPDailyBillingSKUUnit.BYTES;
    } else if (_sku == GCPDailyBillingSKU.GCP_PRIVATE_ENDPOINT_USAGE) {
      return GCPDailyBillingSKUUnit.SECONDS;
    }
    throw new IllegalArgumentException(String.format("Invalid sku %s", _sku));
  }

  private void runUsageCheckJobAndFindCharge(final double pUsageAmountBillingUnits) {
    final double usageAmount = convertBillingUnitsToChargeUnits(pUsageAmountBillingUnits);
    final GCPDailyBillingCharge charge =
        new GCPDailyBillingCharge.Builder()
            .usageDate(_usageDate)
            .skuType(_sku)
            .usageAmount(usageAmount)
            .usageUnit(getSKUUnit())
            .id(new ObjectId())
            .dataCollectionTime(new Date())
            .groupId(_groupId)
            .region(_region)
            .build();
    when(_bigQueryBillingSvc.getDailyBillingCharges(_sku, _usageDate))
        .thenReturn(Stream.of(charge));

    _usageCheckJobHandler.handleWork(getJobParams(), new ObjectId());
    assertTrue(_gcpDailyBillingChargeDao.find(charge.getId()).isPresent());
  }

  private void runSubmissionJob() {
    _submissionJobHandler.handleWork(new BasicDBObject(), new ObjectId());

    // No more charges need to be submitted
    final List<GCPDailyBillingCharge> chargesToSubmit =
        _gcpDailyBillingChargeDao.findUnsubmittedCharges(1).collect(Collectors.toList());
    assertEquals(chargesToSubmit.size(), 0);
  }

  private void runAuditJobAndFindUsage(
      final double pNewUsageAmountBillingUnits, final double pExpectedDiscrepancyBillingUnits) {
    final double usageAmount = convertBillingUnitsToChargeUnits(pNewUsageAmountBillingUnits);
    final double expectedDiscrepancy =
        convertBillingUnitsToChargeUnits(pExpectedDiscrepancyBillingUnits);
    final GCPDailyBillingCharge charge =
        new GCPDailyBillingCharge.Builder()
            .usageDate(_usageDate)
            .skuType(_sku)
            .usageAmount(usageAmount)
            .usageUnit(getSKUUnit())
            .id(new ObjectId())
            .dataCollectionTime(new Date())
            .groupId(_groupId)
            .region(_region)
            .build();
    when(_bigQueryBillingSvc.getDailyBillingCharges(_sku, _usageDate))
        .thenReturn(Stream.of(charge));

    final List<GCPDailyBillingCharge> unsubmittedChargesBeforeAudit =
        _gcpDailyBillingChargeDao.findUnsubmittedCharges(1).collect(Collectors.toList());
    assertTrue(unsubmittedChargesBeforeAudit.isEmpty());

    _auditJobHandler.handleWork(getJobParams(), new ObjectId());
    final List<GCPDailyBillingCharge> newChargesFromAudit =
        _gcpDailyBillingChargeDao.findUnsubmittedCharges(1).collect(Collectors.toList());

    if (pExpectedDiscrepancyBillingUnits <= 0) {
      assertEquals(0, newChargesFromAudit.size());
    } else {
      assertEquals(1, newChargesFromAudit.size());
      final GCPDailyBillingCharge newChargeFromAudit = newChargesFromAudit.get(0);
      assertEquals((Double) expectedDiscrepancy, newChargeFromAudit.getUsageAmount());
      assertEquals(_sku, newChargeFromAudit.getSkuType());
      assertEquals(getSKUUnit(), newChargeFromAudit.getUsageUnit());
    }
  }

  private void assertNoUnsubmittedCharges() {
    assertEquals(_gcpDailyBillingChargeDao.findUnsubmittedCharges(1).count(), 0);
  }

  @Test
  public void testGCPPSCBillingRemediationFlow() {
    _sku = GCPDailyBillingSKU.GCP_PRIVATE_ENDPOINT_CAPACITY_USAGE;

    // GCP reports charge of 10 GB
    runUsageCheckJobAndFindCharge(10);

    // We store and submit charge of 10 GB
    runSubmissionJob();

    // During auditing, GCP reports charge of 15 GB
    runAuditJobAndFindUsage(15, 5);

    // We store and submit charge of 5 GB
    runSubmissionJob();

    // During 2nd round of auditing, GCP reports charge of 25 GB
    runAuditJobAndFindUsage(25, 10);

    // We store and submit change of 10 GB
    runSubmissionJob();

    // During 3rd round of auditing, we match the value exactly
    runAuditJobAndFindUsage(25, 0);

    // No unsubmitted charges exist
    assertEquals(_gcpDailyBillingChargeDao.findUnsubmittedCharges(1).count(), 0);

    // During 4th round of auditing, determine we overcharged
    runAuditJobAndFindUsage(15, -10);

    // No unsubmitted charges exist
    assertNoUnsubmittedCharges();
  }

  @Test
  public void testGCPPSCBillingRemediationFlow_privateEndpointUsage() {
    _sku = GCPDailyBillingSKU.GCP_PRIVATE_ENDPOINT_USAGE;

    // GCP reports charge of 12 hours
    runUsageCheckJobAndFindCharge(12);

    // We store and submit charge
    runSubmissionJob();

    // During auditing, GCP reports charge of 15 hours
    runAuditJobAndFindUsage(15, 3);

    // We store and submit charge
    runSubmissionJob();

    // During 2nd round of auditing, GCP reports charge of 25 hours
    runAuditJobAndFindUsage(25, 10);

    // We store and submit change
    runSubmissionJob();

    // During 3rd round of auditing, we match the value exactly
    runAuditJobAndFindUsage(25, 0);

    // No unsubmitted charges exist
    assertEquals(_gcpDailyBillingChargeDao.findUnsubmittedCharges(1).count(), 0);

    // During 4th round of auditing, determine we overcharged
    runAuditJobAndFindUsage(15, -10);

    // No unsubmitted charges exist
    assertNoUnsubmittedCharges();
  }
}
