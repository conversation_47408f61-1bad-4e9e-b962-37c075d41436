package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.Test;

public class NewMachinesBeforeEnsureConnectivityIntTests extends DependencyRuleBaseTests {

  @Test
  public void apply() {
    final ClusterDescription clusterDescription0 =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ReplicaSetHardware replicaSetHardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, new ObjectId(), clusterDescription0));

    final ClusterDescription clusterDescription1 =
        NDSModelTestFactory.getClusterDescription(
            clusterDescription0.getGroupId(), "otherClusterName", CloudProvider.AWS);
    final ReplicaSetHardware replicaSetHardware1 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, new ObjectId(), clusterDescription1));

    final ClusterDescription clusterDescription2 =
        NDSModelTestFactory.getClusterDescription(
            clusterDescription0.getGroupId(), "thirdClusterName", CloudProvider.AWS);

    final PlannedAction createMachineAction0 =
        _actionFactory.forCreateMachine(
            replicaSetHardware0,
            replicaSetHardware0.getAllHardware().findFirst().orElseThrow(),
            new ObjectId(),
            Map.of());

    final PlannedAction createMachineAction1 =
        _actionFactory.forCreateMachine(
            replicaSetHardware1,
            replicaSetHardware1.getAllHardware().findFirst().orElseThrow(),
            new ObjectId(),
            Map.of());

    final PlannedAction ensureConnectivityAction0 =
        _actionFactory.forEnsureConnectivityForClusterTopologyUpdate(
            _ndsPlanContext, clusterDescription0.getName());
    final PlannedAction ensureConnectivityAction2 =
        _actionFactory.forEnsureConnectivityForClusterTopologyUpdate(
            _ndsPlanContext, clusterDescription2.getName());

    final List<PlannedAction> actions =
        List.of(
            ensureConnectivityAction0,
            ensureConnectivityAction2,
            createMachineAction0,
            createMachineAction1);
    new NewMachinesBeforeEnsureConnectivity().apply(actions);

    assertTrue(
        ensureConnectivityAction0
            .getPredecessors()
            .contains(createMachineAction0.getLastMove().getId()));
    assertTrue(ensureConnectivityAction0.getSuccessors().isEmpty());
    assertTrue(createMachineAction0.getPredecessors().isEmpty());
    assertTrue(
        createMachineAction0
            .getSuccessors()
            .contains(ensureConnectivityAction0.getFirstMove().getId()));
    assertTrue(createMachineAction1.getSuccessors().isEmpty());
    assertTrue(createMachineAction1.getPredecessors().isEmpty());
    assertTrue(ensureConnectivityAction2.getSuccessors().isEmpty());
    assertTrue(ensureConnectivityAction2.getPredecessors().isEmpty());
  }
}
