package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.nds.planner.CopySnapshotMove;
import com.xgen.svc.nds.planner.PostCopySnapshotMove;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.Test;

public class DoCopySnapshotMovesBeforePostCopySnapshotIntTests extends DependencyRuleBaseTests {

  @Test
  public void testApply() {
    final List<PlannedAction> actions = new ArrayList<>();
    final ClusterDescription cd = mock(ClusterDescription.class);
    doReturn(Set.of(CloudProvider.AWS)).when(cd).getCloudProviders();
    doReturn(new ObjectId()).when(cd).getUniqueId();

    actions.add(_actionFactory.forCopySnapshot("test", new ObjectId(), cd));
    actions.add(_actionFactory.forCopySnapshot("test", new ObjectId(), cd));
    actions.add(_actionFactory.forCopySnapshot("test", new ObjectId(), cd));
    actions.add(_actionFactory.forPostCopySnapshot("test", new ArrayList<>(), cd));

    new DoCopySnapshotMovesBeforePostCopySnapshot().apply(actions);

    final List<PlannedAction> copyActions =
        actions.stream()
            .filter(action -> action.getFirstMove() instanceof CopySnapshotMove)
            .toList();

    final List<PlannedAction> postCopyActions =
        actions.stream()
            .filter(action -> action.getFirstMove() instanceof PostCopySnapshotMove)
            .toList();

    assertTrue(copyActions.stream().allMatch(a -> a.getPredecessors().isEmpty()));
    postCopyActions.forEach(
        copyAction -> {
          assertEquals(
              copyActions.stream().map(a -> a.getFirstMove().getId()).collect(Collectors.toSet()),
              copyAction.getPredecessors());
        });
  }
}
