package com.xgen.svc.nds.svc.planning.rules;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Test;

public class ModifyDiskBeforeUpdateClusterMongotuneConfigIntTests extends DependencyRuleBaseTests {

  @Test
  public void testApplyModifyDiskNoUpdateMongotuneConfig() {
    List<PlannedAction> modifyDiskActions = getModifyDiskActions(getClusterDescription());
    new ModifyDiskBeforeUpdateClusterMongotuneConfig().apply(modifyDiskActions);
    modifyDiskActions.forEach(m -> assertTrue(m.getPredecessors().isEmpty()));
    modifyDiskActions.forEach(m -> assertTrue(m.getSuccessors().isEmpty()));
  }

  @Test
  public void testApplyModifyDiskApplyUpdateMongotuneConfig() {
    ClusterDescription clusterDescription = getClusterDescription();
    PlannedAction modifyDiskAction = getModifyDiskActions(clusterDescription).get(0);
    PlannedAction needsUpdateClusterMongotuneConfigAction =
        _actionFactory.forUpdateClusterMongotuneConfig(clusterDescription);

    final List<PlannedAction> actions = new ArrayList<>();
    actions.add(modifyDiskAction);
    actions.add(needsUpdateClusterMongotuneConfigAction);

    new ModifyDiskBeforeUpdateClusterMongotuneConfig().apply(actions);

    // modify disk should be predecessor of update cluster mongotune config
    assertEquals(1, modifyDiskAction.getSuccessors().size());
    assertTrue(
        modifyDiskAction
            .getSuccessors()
            .contains(needsUpdateClusterMongotuneConfigAction.getFirstMove().getId()));
    assertTrue(modifyDiskAction.getPredecessors().isEmpty());
    assertTrue(needsUpdateClusterMongotuneConfigAction.getSuccessors().isEmpty());
  }

  ClusterDescription getClusterDescription() {
    return NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
  }

  List<PlannedAction> getModifyDiskActions(final ClusterDescription clusterDescription) {
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, container.getId(), clusterDescription));
    final NDSGroup ndsGroup = NDSModelTestFactory.getAWSMockedGroup();
    doReturn(Optional.of(container)).when(ndsGroup).getCloudProviderContainer(any());

    final Host host = new Host();
    final InstanceHardware instance = spy(replicaSetHardware.getHardware().get(0));
    final RegionName desiredRegion = instance.getRegion(Optional.of(container));
    // increase disk - modify disk
    doReturn(false).when(instance).doesShardDiskMatch(any(), anyDouble(), any());
    doReturn(true).when(instance).canModifyVolume(anyDouble(), any(), anyBoolean());
    doReturn(true).when(instance).doesShardHardwareSizeMatch(any(), any());
    doReturn(container.getId()).when(instance).getCloudContainerId();

    return _actionFactory.forUpdateMachine(
        ndsGroup,
        clusterDescription,
        replicaSetHardware,
        instance,
        desiredRegion,
        container,
        host,
        null,
        Map.of(),
        LOG,
        Map.of(),
        null,
        false,
        false,
        Map.of(),
        false);
  }
}
