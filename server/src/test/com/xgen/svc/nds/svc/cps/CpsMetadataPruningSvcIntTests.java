package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.amazonaws.services.ec2.model.VolumeType;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CpsMetadataPruningSvcIntTests extends JUnit5NDSBaseTest {

  private static final Logger LOG = LoggerFactory.getLogger(CpsMetadataPruningSvcIntTests.class);

  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private CpsMetadataPruningSvc _cpsMetadataPruningSvc;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  @Disabled
  public void testPruneExpiredMetadata() {
    final Date now = new Date();
    final Date oneYearAgo = DateUtils.addDays(now, -366);
    final Date twoYear = DateUtils.addDays(now, -730);
    final Date twoYearAgo = DateUtils.addDays(now, -731);
    final Date threeYearAgo = DateUtils.addDays(now, -365 * 3);

    final ObjectId id1 = DriverUtils.newObjectIdFromDate(oneYearAgo);
    final ObjectId id2 = DriverUtils.newObjectIdFromDate(twoYear);
    final ObjectId id3 = DriverUtils.newObjectIdFromDate(twoYearAgo);
    final ObjectId id4 = DriverUtils.newObjectIdFromDate(threeYearAgo);

    _backupSnapshotDao.addBackupSnapshot(
        buildDefaultSnapshot().setId(id1).setPurged(true).setPurgedDate(oneYearAgo));

    _backupSnapshotDao.addBackupSnapshot(
        buildDefaultSnapshot().setId(id2).setPurged(true).setPurgedDate(twoYear));

    _backupSnapshotDao.addBackupSnapshot(
        buildDefaultSnapshot().setId(id3).setPurged(true).setPurgedDate(twoYearAgo));

    _backupSnapshotDao.addBackupSnapshot(
        buildDefaultSnapshot().setId(id4).setPurged(true).setPurgedDate(threeYearAgo));

    _cpsMetadataPruningSvc.pruneExpiredMetadata();
    assertTrue(_backupSnapshotDao.findById(id1).isPresent());
    assertTrue(_backupSnapshotDao.findById(id2).isPresent());
    assertTrue(_backupSnapshotDao.findById(id3).isEmpty());
    assertTrue(_backupSnapshotDao.findById(id4).isEmpty());
  }

  private SnapshotUpdate buildDefaultSnapshot() {
    final SnapshotUpdate.AwsSnapshotFieldBuilder awsBuilder =
        new SnapshotUpdate.AwsSnapshotFieldBuilder();
    final ObjectId awsAccountId = new ObjectId();
    final ObjectId awsContainerId = new ObjectId();
    final String ebsVolumeId = "ebsVolumeId-1";
    final String ebsSnapshotDesc = AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId);
    final SnapshotUpdate snapshotUpdate = new SnapshotUpdate();
    snapshotUpdate
        .setId(new ObjectId())
        .setProjectId(ObjectId.get())
        .setShard(false)
        .setStatus(BackupSnapshot.Status.COMPLETED)
        .setCloudProviders(List.of(CloudProvider.AWS))
        .setType(BackupSnapshot.Type.SCHEDULED)
        .setAwsSnapshotField(
            awsBuilder
                .withEbsSnapshotDescription(ebsSnapshotDesc)
                .withAWSAccountId(awsAccountId)
                .withAWSContainerId(awsContainerId)
                .withAWSSubnetId("awsSubnetId-1")
                .withEbsVolumeId(ebsVolumeId)
                .withEbsVolumeType(VolumeType.Io1.name())
                .withIsEbsVolumeEncrypted(true)
                .withEbsVolumeSize(AWSNDSInstanceSize.M30.getDefaultDiskSizeGB())
                .withEbsDiskIOPS(AWSNDSInstanceSize.M30.getMaxEBSStandardIOPS())
                .withRegionName(AWSRegionName.US_EAST_1.getName()))
        .setFrequencyType(BackupFrequencyType.DAILY)
        .setOverrideRetentionPolicy(false)
        .setBackupRetentionUnit(BackupRetentionUnit.DAYS);
    return snapshotUpdate;
  }
}
