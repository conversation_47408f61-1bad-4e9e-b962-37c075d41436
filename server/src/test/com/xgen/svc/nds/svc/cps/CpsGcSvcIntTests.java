package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.internal.verification.VerificationModeFactory.times;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppShutdownException;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._private.dao.CpsBackupCursorFileListsDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.AwsSnapshotFieldBuilder;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.AzureSnapshotFieldBuilder;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.GcpSnapshotFieldBuilder;
import com.xgen.cloud.cps.restore._public.svc.CpsSnapshotSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CpsGcSvcIntTests extends JUnit5NDSBaseTest {

  private static final Logger LOG = LoggerFactory.getLogger(CpsGcSvcIntTests.class);

  @Inject private CpsPolicySvc _backupPolicySvc;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private AppSettings _appSettings;
  @Inject private BackupJobDao _backupJobDao;
  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private CpsSvc _cpsSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private CpsBackupCursorFileListsDao _fileListsDao;
  @Inject private NDSGroupDao _theNdsGroupDao;
  @Inject private CpsPolicySvc _cpsPolicySvc;
  @Inject private BackupRestoreJobDao _backupRestoreJobDao;

  private final AWSApiSvc _awsApiSvc = mock(AWSApiSvc.class);
  private final AzureApiSvc _azureApiSvc = mock(AzureApiSvc.class);
  private final GCPApiSvc _gcpApiSvc = mock(GCPApiSvc.class);
  private final CpsCollectionMetadataBackupSvc _cpsCollectionMetadataBackupSvc =
      mock(CpsCollectionMetadataBackupSvc.class);

  private CpsGcSvc _cpsGcSvc;
  private CpsSnapshotSvc _cpsSnapshotSvc;

  private Group _mmsGroup;
  private NDSGroup _ndsGroup;
  private ClusterDescription _azureClusterDescription;
  private ClusterDescription _awsClusterDescription;
  private ClusterDescription _gcpClusterDescription;
  private AWSCloudProviderContainer _awsCloudProviderContainer;

  private ObjectId _awsBackupJobId;
  private ObjectId _azureBackupJobId;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    doNothing().when(_azureApiSvc).deleteSnapshot(any(), any(), any(), any());
    doReturn(false).when(_azureApiSvc).deleteSnapshotWithVerification(any(), any(), any(), any());
    doNothing().when(_awsApiSvc).deleteEbsSnapshot(any(), any(), any(), any());
    setupClusterAndGroup();
    _cpsGcSvc =
        spy(
            new CpsGcSvc(
                _appSettings,
                _awsApiSvc,
                _azureApiSvc,
                _gcpApiSvc,
                _cpsSvc,
                _fileListsDao,
                _cpsSnapshotSvc,
                _backupRestoreJobDao,
                _cpsCollectionMetadataBackupSvc));
  }

  private void setupClusterAndGroup() throws Exception {
    _mmsGroup = MmsFactory.createGroupWithNDSPlan();
    _ndsGroup = _ndsGroupSvc.ensureGroup(_mmsGroup.getId());

    _awsCloudProviderContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    _theNdsGroupDao.addCloudContainer(_mmsGroup.getId(), _awsCloudProviderContainer);

    _theNdsGroupDao.addCloudContainer(
        _mmsGroup.getId(),
        new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer()));

    _azureClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(_mmsGroup.getId(), "azureCluster"));

    _awsClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_mmsGroup.getId(), "awsCluster"));

    _gcpClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getGCPClusterDescription(_mmsGroup.getId(), "gcpCluster"));

    _clusterDescriptionDao.save(_azureClusterDescription);
    _clusterDescriptionDao.save(_awsClusterDescription);

    // Add backup jobs
    _azureBackupJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            _ndsGroup.getGroupId(),
            _azureClusterDescription.getName(),
            _azureClusterDescription.getUniqueId(),
            BackupJob.ClusterType.REPLICA_SET,
            2,
            false,
            false);

    _awsBackupJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            _ndsGroup.getGroupId(),
            _awsClusterDescription.getName(),
            _awsClusterDescription.getUniqueId(),
            BackupJob.ClusterType.REPLICA_SET,
            2,
            false,
            false);
  }

  @Test
  public void testCleanSnapshotsAws_normal() throws Exception {
    final ClusterDescription clusterDescription = _awsClusterDescription;
    final PolicyItem policyItem1 =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    final BackupJob backupJob = _backupJobDao.find(_awsBackupJobId).get();
    final Policy oldPolicy = backupJob.getPolicies().get(0);

    final Policy newBackupPolicy = new Policy(oldPolicy.getId(), Arrays.asList(policyItem1));

    // change backup policy to retain only minimum of 3 snapshots in total
    _backupPolicySvc.updateBackupPolicy(
        clusterDescription.getGroupId(),
        clusterDescription.getUniqueId(),
        0,
        2,
        null,
        Arrays.asList(newBackupPolicy),
        new Date(),
        false);

    // 4 Aws backup snapshots with one expired
    for (int i = 0; i < 3; i++) {
      addAwsBackupSnapshot(
          clusterDescription, String.format("awsSnapshot-%s", i), new Date(), null, "RsId", false);
    }
    final ObjectId expiredId =
        addAwsBackupSnapshot(
            clusterDescription,
            "awsSnapshot-expired",
            new Date(),
            new Date(System.currentTimeMillis() - Duration.ofMinutes(2).toMillis()),
            "RsId",
            false);

    final List<BackupSnapshot> allSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(4, allSnapshots.size());

    // test cleanup
    final List<BackupSnapshot> snapshotsToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, new Date());
    assertEquals(1, snapshotsToPurge.size());

    doNothing().when(_awsApiSvc).deleteEbsSnapshot(any(), any(), any(), any());
    final Date expireBeforeDate = new Date();
    cleanSnapshotsByProvider(CloudProvider.AWS, expireBeforeDate);
    verify(_cpsGcSvc, times(1)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(3, activeSnapshots.size());

    final Optional<BackupSnapshot> purged = _backupSnapshotDao.findById(expiredId);
    assertTrue(purged.isPresent(), "Snapshot " + expiredId + " should have been purged");
    assertTrue(purged.get().getPurged());
  }

  @Test
  public void testCleanSnapshotsAzure_normal() throws Exception {
    final PolicyItem policyItem1 =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    final BackupJob backupJob = _backupJobDao.find(_azureBackupJobId).get();
    final Policy oldPolicy = backupJob.getPolicies().get(0);

    final Policy newBackupPolicy = new Policy(oldPolicy.getId(), Arrays.asList(policyItem1));

    // change backup policy to retain only minimum of 3 snapshots in total
    _backupPolicySvc.updateBackupPolicy(
        _azureClusterDescription.getGroupId(),
        _azureClusterDescription.getUniqueId(),
        0,
        2,
        null,
        Arrays.asList(newBackupPolicy),
        new Date(),
        false);

    // 4 Azure backup snapshots with one expired
    for (int i = 0; i < 3; i++) {
      addAzureBackupSnapshot(
          _azureClusterDescription,
          String.format("azSnapshot-%s", i),
          null,
          BackupSnapshot.Type.SCHEDULED);
    }
    final ObjectId expiredId =
        addAzureBackupSnapshot(
            _azureClusterDescription,
            "azSnapshot-expired",
            new Date(System.currentTimeMillis() - Duration.ofMinutes(2).toMillis()),
            BackupSnapshot.Type.SCHEDULED);

    final List<BackupSnapshot> allSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            _azureClusterDescription.getGroupId(), _azureClusterDescription.getUniqueId(), false);
    assertEquals(4, allSnapshots.size());

    final List<BackupSnapshot> snapshotsToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AZURE, new Date());
    assertEquals(1, snapshotsToPurge.size());

    doReturn(false).when(_azureApiSvc).deleteSnapshotWithVerification(any(), any(), any(), any());

    // test cleanup
    final Date expireBeforeDate = new Date();
    cleanSnapshotsByProvider(CloudProvider.AZURE, expireBeforeDate);
    verify(_cpsGcSvc, times(1)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            _azureClusterDescription.getGroupId(), _azureClusterDescription.getUniqueId(), false);
    assertEquals(4, activeSnapshots.size());

    // Azure does not mark snapshot as purged the first time, since the call is asynchronous,
    // it will mark snapshot as purged when it is no longer found on second call
    doReturn(true).when(_azureApiSvc).deleteSnapshotWithVerification(any(), any(), any(), any());
    cleanSnapshotsByProvider(CloudProvider.AZURE, expireBeforeDate);
    verify(_cpsGcSvc, times(2)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots2 =
        _backupSnapshotDao.findCompletedByCluster(
            _azureClusterDescription.getGroupId(), _azureClusterDescription.getUniqueId(), false);
    assertEquals(3, activeSnapshots2.size());

    final Optional<BackupSnapshot> purged = _backupSnapshotDao.findById(expiredId);
    assertTrue(purged.isPresent());
    assertTrue(purged.get().getPurged());
  }

  private String snapshotsToStr(final List<BackupSnapshot> pCompletedSnapshots) {
    final StringBuffer sb = new StringBuffer("\nSnapshots:\n");
    pCompletedSnapshots.stream()
        .forEach(
            s -> {
              sb.append(
                  String.format(
                      "id=%s schedcre=%s compl=%s del=%s\n",
                      s.getId(),
                      s.getScheduledCreationDate(),
                      s.getSnapshotCompletionDate(),
                      s.getScheduledDeletionDate()));
            });
    return sb.toString();
  }

  @Test
  public void testCleanSnapshotsAws_expired() throws Exception {
    final ClusterDescription clusterDescription = _awsClusterDescription;
    final Date expireIn2Hour =
        new Date(System.currentTimeMillis() + Duration.ofHours(2).toMillis());
    final Date expireIn4Hour =
        new Date(System.currentTimeMillis() + Duration.ofHours(4).toMillis());
    final Date expired2HourAgo =
        new Date(System.currentTimeMillis() - Duration.ofHours(2).toMillis());
    final Date expired4HourAgo =
        new Date(System.currentTimeMillis() - Duration.ofHours(4).toMillis());

    final Date now = new Date();
    final Date later = DateUtils.addHours(now, 1);

    // 4 Aws backup snapshots with two expired
    final ObjectId activeOneId =
        addAwsBackupSnapshot(
            later,
            clusterDescription,
            "awsSnapshot-active1",
            new Date(),
            expireIn2Hour,
            "RsId",
            false,
            Collections.emptyList(),
            null);
    final ObjectId activeTwoId =
        addAwsBackupSnapshot(
            later,
            clusterDescription,
            "awsSnapshot-active2",
            new Date(),
            expireIn4Hour,
            "RsId",
            false,
            Collections.emptyList(),
            null);
    final ObjectId expired2 =
        addAwsBackupSnapshot(
            now,
            clusterDescription,
            "awsSnapshot-expired2",
            new Date(),
            expired4HourAgo,
            "RsId",
            false,
            Collections.emptyList(),
            null);
    final ObjectId expired1 =
        addAwsBackupSnapshot(
            later,
            clusterDescription,
            "awsSnapshot-expired1",
            new Date(),
            expired2HourAgo,
            "RsId",
            false,
            Collections.emptyList(),
            null);

    final List<BackupSnapshot> allSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            _awsClusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(4, allSnapshots.size());

    LOG.debug("all snapshots before cleanup: {}", snapshotsToStr(allSnapshots));

    // test cleanup
    doThrow(new AWSApiException(CommonErrorCode.NOT_FOUND))
        .when(_awsApiSvc)
        .deleteEbsSnapshot(any(), any(), any(), any());
    cleanSnapshotsByProvider(CloudProvider.AWS, new Date());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            _awsClusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(2, activeSnapshots.size());

    Optional<BackupSnapshot> extended = _backupSnapshotDao.findById(expired1);
    assertTrue(extended.get().getPurged());
  }

  @Test
  public void testCleanSnapshotsAws_AllDeleted() throws Exception {
    final ClusterDescription clusterDescription = _awsClusterDescription;
    // 3 Aws backup snapshots and all deleted
    final Date expiredDate = new Date();
    for (int i = 0; i < 3; i++) {
      addAwsBackupSnapshot(
          clusterDescription,
          String.format("awsSnapshot-%s", i),
          new Date(),
          expiredDate,
          "RsId",
          false);
    }
    _backupSnapshotDao
        .findCompletedByCluster(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), false)
        .forEach(
            sn -> _backupSnapshotDao.markSnapshotsDeleted(Collections.singletonList(sn.getId())));

    // test cleanup
    cleanSnapshotsByProvider(CloudProvider.AWS, new Date());
    verify(_cpsGcSvc, times(3)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(0, activeSnapshots.size());
  }

  @Test
  public void testCleanSnapshotsAzure_AllDeleted() throws Exception {
    // 3 Azure backup snapshots and all deleted
    final Date expiredDate = new Date();
    for (int i = 0; i < 3; i++) {
      addAzureBackupSnapshot(
          _azureClusterDescription,
          String.format("azSnapshot-%s", i),
          expiredDate,
          BackupSnapshot.Type.SCHEDULED);
    }
    _backupSnapshotDao
        .findCompletedByCluster(
            _azureClusterDescription.getGroupId(), _azureClusterDescription.getUniqueId(), false)
        .forEach(
            sn -> _backupSnapshotDao.markSnapshotsDeleted(Collections.singletonList(sn.getId())));

    // test cleanup
    cleanSnapshotsByProvider(CloudProvider.AZURE, new Date());
    verify(_cpsGcSvc, times(3)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            _azureClusterDescription.getGroupId(), _azureClusterDescription.getUniqueId(), false);
    assertEquals(0, activeSnapshots.size());
  }

  @Test
  public void testCleanSnapshots_NothingToCleanup() throws Exception {
    // Edge case test: nothing to clean
    cleanSnapshotsByProvider(CloudProvider.AZURE, new Date());
    cleanSnapshotsByProvider(CloudProvider.AWS, new Date());
  }

  @Test
  public void testCleanSnapshotsBackupPolicyChanged() throws Exception {
    final ClusterDescription clusterDescription = _awsClusterDescription;
    final Date create2DaysAgo =
        new Date(System.currentTimeMillis() - Duration.ofDays(2).toMillis());
    final Date expireIn1Day = new Date(System.currentTimeMillis() + Duration.ofDays(1).toMillis());
    final Date create1DayAgo = new Date(System.currentTimeMillis() - Duration.ofDays(1).toMillis());
    final Date expireIn2Days = new Date(System.currentTimeMillis() + Duration.ofDays(2).toMillis());
    final Date create0DaysAgo = new Date(System.currentTimeMillis());
    final Date expireIn3Days = new Date(System.currentTimeMillis() + Duration.ofDays(3).toMillis());

    final Duration newRetention = Duration.ofDays(2);
    final PolicyItem policyItem1 =
        new PolicyItem(
            new ObjectId(), BackupFrequencyType.DAILY, 1, newRetention, BackupRetentionUnit.DAYS);

    // 2 active AWS snapshots, one is expired, will be purged after backup policy change
    final ObjectId activeToBePurgedId =
        addAwsBackupSnapshot(
            create2DaysAgo,
            clusterDescription,
            "awsSnapshot-active1",
            create2DaysAgo,
            expireIn1Day,
            "RsId",
            false,
            Arrays.asList(policyItem1),
            policyItem1.getId());
    final ObjectId activeOneId =
        addAwsBackupSnapshot(
            create1DayAgo,
            clusterDescription,
            "awsSnapshot-active2",
            create1DayAgo,
            expireIn2Days,
            "RsId",
            false,
            Arrays.asList(policyItem1),
            policyItem1.getId());
    final ObjectId activeTwoId =
        addAwsBackupSnapshot(
            create0DaysAgo,
            clusterDescription,
            "awsSnapshot-active3",
            create0DaysAgo,
            expireIn3Days,
            "RsId",
            false,
            Arrays.asList(policyItem1),
            policyItem1.getId());

    // properly set completion date to be same as initiationdate
    _backupSnapshotDao.markCompleted(
        activeToBePurgedId, expireIn1Day, create2DaysAgo, BackupRetentionUnit.DAYS);
    _backupSnapshotDao.markCompleted(
        activeOneId, expireIn2Days, create1DayAgo, BackupRetentionUnit.DAYS);
    _backupSnapshotDao.markCompleted(
        activeTwoId, expireIn3Days, create0DaysAgo, BackupRetentionUnit.DAYS);

    final List<BackupSnapshot> allSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(3, allSnapshots.size());

    final BackupJob backupJob = _backupJobDao.find(_awsBackupJobId).get();
    final Policy oldPolicy = backupJob.getPolicies().get(0);

    final Policy newBackupPolicy = new Policy(oldPolicy.getId(), Arrays.asList(policyItem1));

    // change backup policy to retain 2 snapshots for daily snapshots
    _backupPolicySvc.updateBackupPolicy(
        clusterDescription.getGroupId(),
        clusterDescription.getUniqueId(),
        0,
        2,
        null,
        Arrays.asList(newBackupPolicy),
        new Date(),
        true);

    final Optional<BackupSnapshot> toBePurged = _backupSnapshotDao.findById(activeToBePurgedId);
    assertFalse(toBePurged.get().getPurged());

    // test cleanup
    doThrow(new AWSApiException(CommonErrorCode.NOT_FOUND))
        .when(_awsApiSvc)
        .deleteEbsSnapshot(any(), any(), any(), any());
    cleanSnapshotsByProvider(CloudProvider.AWS, new Date());
    verify(_cpsGcSvc, times(1)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), false);
    assertEquals(2, activeSnapshots.size());

    final Optional<BackupSnapshot> purged = _backupSnapshotDao.findById(activeToBePurgedId);
    assertTrue(purged.get().getPurged());
  }

  @Test
  public void testCleanShardedClusterSnapshots() throws Exception {
    final ClusterDescription clusterDescription = _awsClusterDescription;
    final PolicyItem policyItem1 =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    final BackupJob backupJob = _backupJobDao.find(_awsBackupJobId).get();
    final Policy oldPolicy = backupJob.getPolicies().get(0);

    final Policy newBackupPolicy = new Policy(oldPolicy.getId(), Arrays.asList(policyItem1));

    // change backup policy to retain only minimum of 3 snapshots in total
    _backupPolicySvc.updateBackupPolicy(
        clusterDescription.getGroupId(),
        clusterDescription.getUniqueId(),
        0,
        2,
        null,
        Arrays.asList(newBackupPolicy),
        new Date(),
        false);

    // 1 expired sharded snapshot which children snapshots
    final Date scheduledDeletionDate =
        new Date(System.currentTimeMillis() - Duration.ofMinutes(2).toMillis());
    final ObjectId shard0 =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1"),
            new Date(),
            scheduledDeletionDate,
            _awsClusterDescription.getName() + "-shard-0",
            true);
    final ObjectId shard1 =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1"),
            new Date(),
            scheduledDeletionDate,
            _awsClusterDescription.getName() + "-shard-1",
            true);
    final ObjectId config0 =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1"),
            new Date(),
            scheduledDeletionDate,
            _awsClusterDescription.getName() + "-config-0",
            true);

    final Map<ObjectId, String> members = new HashMap<>();
    members.put(shard0, _awsClusterDescription.getName() + "-shard-0");
    members.put(shard1, _awsClusterDescription.getName() + "-shard-1");
    members.put(config0, _awsClusterDescription.getName() + "-config-0");
    final ObjectId shardedClusterSnapshotId = new ObjectId();
    addShardedClusterSnapshot(
        shardedClusterSnapshotId,
        _awsClusterDescription,
        new Date(),
        scheduledDeletionDate,
        CloudProvider.AWS,
        members);

    final List<BackupSnapshot> snapshotsShardedStillInProgress =
        _backupSnapshotDao.findCompletedByCluster(
            _awsClusterDescription.getGroupId(), _awsClusterDescription.getUniqueId(), false);
    assertEquals(3, snapshotsShardedStillInProgress.size());

    _backupSnapshotDao.markCompleted(
        shardedClusterSnapshotId, scheduledDeletionDate, new Date(), BackupRetentionUnit.DAYS);
    final List<BackupSnapshot> snapshotsShardedCompleted =
        _backupSnapshotDao.findCompletedByCluster(
            _awsClusterDescription.getGroupId(), _awsClusterDescription.getUniqueId(), false);
    assertEquals(4, snapshotsShardedCompleted.size());

    final List<BackupSnapshot> snapshotsToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, new Date());
    assertEquals(1, snapshotsToPurge.size());
    // test cleanup

    // set retention to 1
    final PolicyItem policyItem2 =
        new PolicyItem(
            policyItem1.getId(),
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS);

    final Policy newBackupPolicy2 = new Policy(oldPolicy.getId(), Arrays.asList(policyItem2));

    // change backup policy to retain only minimum of 3 snapshots in total
    _backupPolicySvc.updateBackupPolicy(
        clusterDescription.getGroupId(),
        clusterDescription.getUniqueId(),
        0,
        2,
        null,
        Arrays.asList(newBackupPolicy2),
        new Date(),
        false);

    doThrow(new AWSApiException(CommonErrorCode.NOT_FOUND))
        .when(_awsApiSvc)
        .deleteEbsSnapshot(any(), any(), any(), any());
    cleanSnapshotsByProvider(CloudProvider.AWS, new Date());
    verify(_cpsGcSvc, times(1)).deleteSnapshot(any());

    final List<BackupSnapshot> activeSnapshots =
        _backupSnapshotDao.findCompletedByCluster(
            _awsClusterDescription.getGroupId(), _awsClusterDescription.getUniqueId(), false);
    assertEquals(0, activeSnapshots.size());

    // Sharded cluster snapshot's root snapshot will only be marked as deleted in the 1st pass
    final Optional<BackupSnapshot> purged = _backupSnapshotDao.findById(shardedClusterSnapshotId);
    assertTrue(purged.isPresent());
    assertTrue(purged.get().getDeleted());
    assertFalse(purged.get().getPurged());

    final Optional<BackupSnapshot> purged2 = _backupSnapshotDao.findById(shard0);
    assertTrue(purged2.isPresent());
    assertTrue(purged2.get().getPurged());

    final Optional<BackupSnapshot> purged3 = _backupSnapshotDao.findById(shard1);
    assertTrue(purged3.isPresent());
    assertTrue(purged3.get().getPurged());

    final Optional<BackupSnapshot> purged4 = _backupSnapshotDao.findById(config0);
    assertTrue(purged4.isPresent());
    assertTrue(purged4.get().getPurged());

    // Sharded cluster snapshot's root snapshot will be marked as purged in the 2nd run
    cleanSnapshotsByProvider(CloudProvider.AWS, new Date());
    final Optional<BackupSnapshot> purged5 = _backupSnapshotDao.findById(shardedClusterSnapshotId);
    assertTrue(purged5.isPresent());
    assertTrue(purged5.get().getPurged());
  }

  @Test
  public void testCleanSnapshotsInternal_no_provider() {
    final List<CloudProvider> cloudProviders = List.of(CloudProvider.AWS, CloudProvider.NONE);
    final Date now = new Date();
    final Date scheduledDeletionDateInThePast =
        new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis());

    final Date scheduledDeletionDateInTheFuture =
        new Date(System.currentTimeMillis() + Duration.ofMinutes(20).toMillis());

    final ObjectId activeToBePurgedId =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1"),
            now,
            scheduledDeletionDateInThePast,
            "rsId",
            false);

    final ObjectId activeNotPurged =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-2"),
            now,
            scheduledDeletionDateInTheFuture,
            "rsId",
            false);

    final ObjectId shard0_no_provider =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1-no-provider"),
            now,
            scheduledDeletionDateInTheFuture,
            _awsClusterDescription.getName() + "-shard-0",
            true);

    _backupSnapshotDao.updateBackupSnapshot(
        shard0_no_provider, new SnapshotUpdate().setCloudProviders(List.of()));

    final ObjectId shard1 =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1"),
            now,
            scheduledDeletionDateInThePast,
            _awsClusterDescription.getName() + "-shard-1",
            true);

    final ObjectId config0 =
        addAwsBackupSnapshot(
            _awsClusterDescription,
            String.format("awsSnapshot-1"),
            now,
            scheduledDeletionDateInThePast,
            _awsClusterDescription.getName() + "-config-0",
            true);

    final Map<ObjectId, String> members = new HashMap<>();
    members.put(shard0_no_provider, _awsClusterDescription.getName() + "-shard-0");
    members.put(shard1, _awsClusterDescription.getName() + "-shard-1");
    members.put(config0, _awsClusterDescription.getName() + "-config-0");
    final ObjectId shardedClusterSnapshotId = new ObjectId();

    addShardedClusterSnapshot(
        shardedClusterSnapshotId,
        _awsClusterDescription,
        now,
        scheduledDeletionDateInThePast,
        null,
        members);

    _backupSnapshotDao.updateBackupSnapshot(
        shardedClusterSnapshotId, new SnapshotUpdate().setStatus(Status.FAILED));

    int expired =
        cloudProviders.stream()
            .map(provider -> _backupSnapshotDao.findSnapshotsToPurge(provider, new Date()).size())
            .reduce(0, Integer::sum);
    assertEquals(2, expired);

    doNothing().when(_awsApiSvc).deleteEbsSnapshot(any(), any(), any(), any());

    // Run the garbage collection
    _cpsGcSvc.cleanSnapshotsInternal(new Date());
    expired =
        cloudProviders.stream()
            .map(provider -> _backupSnapshotDao.findSnapshotsToPurge(provider, new Date()).size())
            .reduce(0, Integer::sum);
    assertEquals(1, expired);
    assertTrue(_backupSnapshotDao.findById(activeToBePurgedId).get().getPurged());
    assertTrue(_backupSnapshotDao.findById(shard0_no_provider).get().getPurged());
    assertTrue(_backupSnapshotDao.findById(shard1).get().getPurged());
    assertTrue(_backupSnapshotDao.findById(config0).get().getPurged());
    assertFalse(_backupSnapshotDao.findById(activeNotPurged).get().getPurged());
    assertFalse(_backupSnapshotDao.findById(shardedClusterSnapshotId).get().getPurged());

    // The 2nd pass will delete the root sharded snapshot
    _cpsGcSvc.cleanSnapshotsInternal(new Date());
    // Validate that all the snapshots were deleted.
    expired =
        cloudProviders.stream()
            .map(provider -> _backupSnapshotDao.findSnapshotsToPurge(provider, new Date()).size())
            .reduce(0, Integer::sum);
    assertEquals(0, expired);
    assertTrue(_backupSnapshotDao.findById(shardedClusterSnapshotId).get().getPurged());
  }

  /**
   * This tests that we delete _all_ provider's snapshots using the cleanSnapshotsInternal
   * method/entrypoint that's called from the cron job. We create 4 snapshots for each provider and
   * wait on all of them to be deleted.
   */
  @Test
  public void testCleanSnapshotsInternal_AllDeleted() {
    List<ClusterDescription> descriptions =
        List.of(_awsClusterDescription, _azureClusterDescription, _gcpClusterDescription);

    // Create 4 snapshots per provider.
    final Date expiredDate = new Date();
    for (final ClusterDescription description : descriptions) {
      for (int i = 0; i < 4; i++) {
        switch (description.getOnlyCloudProvider().get()) {
          case AWS ->
              addAwsBackupSnapshot(
                  description,
                  String.format("awsSnapshot-%s", i),
                  new Date(),
                  expiredDate,
                  "RsId",
                  false);
          case AZURE ->
              addAzureBackupSnapshot(
                  description,
                  String.format("azureSnapshot-%s", i),
                  new Date(),
                  BackupSnapshot.Type.SCHEDULED);
          case GCP ->
              addGcpBackupSnapshot(
                  description, String.format("gcpSnapshot-%s", i), new Date(), expiredDate);
        }
      }

      // Loop through and mark all the snapshots we just made as deleted.
      _backupSnapshotDao
          .findCompletedByCluster(description.getGroupId(), description.getUniqueId(), false)
          .forEach(sn -> _backupSnapshotDao.markSnapshotsDeleted(List.of(sn.getId())));
    }

    int expired =
        descriptions.stream()
            .map(
                d ->
                    _backupSnapshotDao
                        .findSnapshotsToPurge(d.getOnlyCloudProvider().get(), new Date())
                        .size())
            .reduce(0, Integer::sum);
    assertEquals(12, expired);

    // To trigger a purge, we need to mock these to return an "it was already deleted" exception.
    // Because of this mocking, we're really only testing the second execution of the cron job.
    // We delete the cloud provider snapshot on the first execution, then mark the snapshot
    // as purged in our DB on the second execution.
    doNothing().when(_awsApiSvc).deleteEbsSnapshot(any(), any(), any(), any());
    doReturn(true).when(_azureApiSvc).deleteSnapshotWithVerification(any(), any(), any(), any());
    doThrow(new GCPApiException(CommonErrorCode.NOT_FOUND))
        .when(_gcpApiSvc)
        .deleteSnapshot(any(), any(), any(), any());

    // Run the garbage collection.
    _cpsGcSvc.cleanSnapshotsInternal(new Date());

    // Validate that all the snapshots were deleted.
    expired =
        descriptions.stream()
            .map(
                d ->
                    _backupSnapshotDao
                        .findSnapshotsToPurge(d.getOnlyCloudProvider().get(), new Date())
                        .size())
            .reduce(0, Integer::sum);
    assertEquals(0, expired);
  }

  void cleanSnapshotsByProvider(final CloudProvider pProvider, final Date pExpiresBefore)
      throws AppShutdownException {
    final ExecutorService executor = Executors.newSingleThreadExecutor();
    try {
      _cpsGcSvc.cleanSnapshotsByProvider(executor, pProvider, pExpiresBefore);
    } finally {
      CpsOplogUtil.closeExecutor(LOG, executor, 1, TimeUnit.MINUTES);
    }
  }

  public ObjectId addAzureBackupSnapshot(
      final ClusterDescription pClusterDescription,
      final String pSnapshotName,
      final Date pScheduledDeletionDate,
      final BackupSnapshot.Type pSnapshotType) {
    return addAzureBackupSnapshot(
        new Date(), pClusterDescription, pSnapshotName, pScheduledDeletionDate, pSnapshotType);
  }

  public ObjectId addAzureBackupSnapshot(
      final Date pNow,
      final ClusterDescription pClusterDescription,
      final String pSnapshotName,
      final Date pScheduledDeletionDate,
      final BackupSnapshot.Type pSnapshotType) {
    final ObjectId subscriptionId = new ObjectId();
    final String resourceGroup = "resource-group";
    final Date scheduledCreationDate = new Date(pNow.getTime() - Duration.ofMinutes(1).toMillis());
    final Date snapshotInitiationDate =
        new Date(System.currentTimeMillis() - Duration.ofMinutes(1).toMillis());
    final Date scheduledDeletionDate =
        pScheduledDeletionDate != null
            ? pScheduledDeletionDate
            : new Date(System.currentTimeMillis() + Duration.ofDays(30).toMillis());
    final long usedDiskSpace = 1 << 30; // 1GB
    final ObjectId snapshotId = new ObjectId();
    final BackupSnapshotEncryptionCredentials pEncryptionCredentials =
        new AWSBackupSnapshotEncryptionCredentials(null, null, null, null, null, false);

    final Date completionDate = new Date();
    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(snapshotId)
            .setProjectId(pClusterDescription.getGroupId())
            .setClusterName(pClusterDescription.getName())
            .setDeploymentClusterName(pClusterDescription.getDeploymentClusterName())
            .setRsId("rsId")
            .setClusterUniqueId(pClusterDescription.getUniqueId())
            .setScheduledCreationDate(scheduledCreationDate)
            .setSnapshotInitiationDate(snapshotInitiationDate)
            .setScheduledDeletionDate(scheduledDeletionDate)
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(VersionUtils.FOUR_ZERO_ZERO)
            .setUsedDiskSpace(usedDiskSpace)
            .setCloudProviders(Arrays.asList(CloudProvider.AZURE))
            .setAzureSnapshotField(
                new AzureSnapshotFieldBuilder()
                    .withSnapshotName(pSnapshotName)
                    .withDiskType(AzureDiskType.P10.name())
                    .withSubscriptionId(subscriptionId)
                    .withResourceGroup(resourceGroup)
                    .withRegionName(AzureRegionName.US_EAST.name()))
            .setEncryptionDetails(pEncryptionCredentials)
            .setStatus(Status.COMPLETED)
            .setType(pSnapshotType)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setPolicyItemIds(Collections.emptyList())
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(completionDate));
  }

  public ObjectId addGcpBackupSnapshot(
      final ClusterDescription pClusterDescription,
      final String pSnapshotName,
      final Date pNow,
      final Date pScheduledDeletionDate) {
    final Date scheduledCreationDate = new Date(pNow.getTime() - Duration.ofMinutes(1).toMillis());
    final Date snapshotInitiationDate =
        new Date(System.currentTimeMillis() - Duration.ofMinutes(1).toMillis());
    final Date scheduledDeletionDate =
        pScheduledDeletionDate != null
            ? pScheduledDeletionDate
            : new Date(System.currentTimeMillis() + Duration.ofDays(30).toMillis());
    final long usedDiskSpace = 1 << 30; // 1GB
    final ObjectId snapshotId = new ObjectId();

    final Date completionDate = new Date();
    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(snapshotId)
            .setProjectId(pClusterDescription.getGroupId())
            .setClusterName(pClusterDescription.getName())
            .setDeploymentClusterName(pClusterDescription.getDeploymentClusterName())
            .setRsId("rsId")
            .setClusterUniqueId(pClusterDescription.getUniqueId())
            .setScheduledCreationDate(scheduledCreationDate)
            .setSnapshotInitiationDate(snapshotInitiationDate)
            .setScheduledDeletionDate(scheduledDeletionDate)
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(VersionUtils.FOUR_ZERO_ZERO)
            .setUsedDiskSpace(usedDiskSpace)
            .setCloudProviders(List.of(CloudProvider.GCP))
            .setType(Type.SCHEDULED)
            .setGcpSnapshotField(
                new GcpSnapshotFieldBuilder()
                    .withGcpOrgId(new ObjectId())
                    .withGcpProjectId(new ObjectId().toString())
                    .withSnapshotName(pSnapshotName)
                    .withRegionName(GCPRegionName.CENTRAL_US.name()))
            .setStatus(Status.COMPLETED)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setPolicyItemIds(Collections.emptyList())
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(completionDate));
  }

  public ObjectId addAwsBackupSnapshot(
      final ClusterDescription pClusterDescription,
      final String pSnapshotId,
      final Date psnapshotInitiationDate,
      final Date pScheduledDeletionDate,
      final String pRsId,
      final boolean pIsShard) {
    return addAwsBackupSnapshot(
        new Date(),
        pClusterDescription,
        pSnapshotId,
        psnapshotInitiationDate,
        pScheduledDeletionDate,
        pRsId,
        pIsShard,
        Collections.emptyList(),
        null);
  }

  public ObjectId addAwsBackupSnapshot(
      final Date pScheduledCreationDate,
      final ClusterDescription pClusterDescription,
      final String pSnapshotId,
      final Date psnapshotInitiationDate,
      final Date pScheduledDeletionDate,
      final String pRsId,
      final boolean pIsShard,
      final List<PolicyItem> pPolicyItemList,
      final ObjectId pMainPolicyItemId) {
    final long usedDiskSpace = 1 << 30; // 1GB
    final BackupSnapshotEncryptionCredentials pEncryptionCredentials =
        new AWSBackupSnapshotEncryptionCredentials(null, null, null, null, null, false);

    final ObjectId pBackupSnapshotId = new ObjectId();
    final Date completionDate = new Date();
    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(pBackupSnapshotId)
            .setProjectId(pClusterDescription.getGroupId())
            .setClusterName(pClusterDescription.getName())
            .setDeploymentClusterName(pClusterDescription.getDeploymentClusterName())
            .setRsId(pRsId)
            .setClusterUniqueId(pClusterDescription.getUniqueId())
            .setScheduledCreationDate(pScheduledCreationDate)
            .setSnapshotInitiationDate(psnapshotInitiationDate)
            .setScheduledDeletionDate(pScheduledDeletionDate)
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(VersionUtils.FOUR_ZERO_ZERO)
            .setUsedDiskSpace(usedDiskSpace)
            .setCloudProviders(Arrays.asList(CloudProvider.AWS))
            .setAwsSnapshotField(
                new AwsSnapshotFieldBuilder()
                    .withEbsSnapshotId(pSnapshotId)
                    .withEbsSnapshotDescription("snapshot description")
                    .withAWSAccountId(_awsCloudProviderContainer.getAWSAccountId())
                    .withAWSContainerId(_awsCloudProviderContainer.getId())
                    .withAWSSubnetId(_awsCloudProviderContainer.getSubnets()[0].getSubnetId())
                    .withEbsVolumeId("vol-asdf")
                    .withEbsVolumeType("io1")
                    .withIsEbsVolumeEncrypted(true)
                    .withEbsVolumeSize(100)
                    .withEbsDiskIOPS(200)
                    .withRegionName(_awsCloudProviderContainer.getRegion().getName()))
            .setEncryptionDetails(pEncryptionCredentials)
            .setShard(pIsShard)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setPolicyItemIds(
                pPolicyItemList.stream().map(PolicyItem::getId).collect(Collectors.toList()))
            .setMainPolicyItemId(pMainPolicyItemId)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(completionDate));
  }

  private void addShardedClusterSnapshot(
      final ObjectId clusterSnapshotId,
      final ClusterDescription pClusterDescription,
      final Date psnapshotInitiationDate,
      final Date pScheduledDeletionDate,
      final CloudProvider cloudProvider,
      final Map<ObjectId, String> members) {
    _backupSnapshotDao.addShardedClusterBackupSnapshot(
        clusterSnapshotId,
        pClusterDescription.getGroupId(),
        pClusterDescription.getName(),
        pClusterDescription.getDeploymentClusterName(),
        pClusterDescription.getUniqueId(),
        new Date(),
        new Date(),
        psnapshotInitiationDate,
        VersionUtils.FOUR_ZERO_ZERO,
        VersionUtils.FOUR_ZERO_ZERO,
        0L,
        cloudProvider != null ? Arrays.asList(cloudProvider) : Collections.emptyList(),
        new AWSBackupSnapshotEncryptionCredentials(null, null, null, null, null, false),
        members,
        BackupSnapshot.Type.SCHEDULED,
        null,
        BackupSnapshot.Status.QUEUED,
        null,
        BackupFrequencyType.DAILY,
        Collections.emptyList(),
        null,
        BackupRetentionUnit.DAYS,
        ConfigServerType.DEDICATED,
        BackupSnapshot.PlanningType.BLOCKING,
        null,
        null,
        false);
  }
}
