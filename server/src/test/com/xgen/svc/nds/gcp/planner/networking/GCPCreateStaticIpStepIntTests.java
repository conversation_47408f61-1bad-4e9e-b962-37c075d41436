package com.xgen.svc.nds.gcp.planner.networking;

import static org.junit.Assert.assertEquals;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.GCPReserveProjectStep;
import com.xgen.svc.nds.gcp.planner.GCPStep;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

public class GCPCreateStaticIpStepIntTests extends GCPExternalIntTest {
  @Inject private GroupDao _groupDao;

  private void testGCPCreateStepInternal(final GCPStep<?> pStep) {
    waitForStepPerformDone(pStep);
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPCreateStaticIpStep() {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final GCPReserveProjectStep gcpCreateProjectStep =
        new GCPReserveProjectStep(
            ((NDSPlanContext) plan.getPlanContext()),
            new State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                1,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            getGCPProjectDao(),
            getGCPProjectCreationSvc(),
            getOrphanedItemDao(),
            getNDSOrphanedItemSvc(),
            _groupDao);
    try {
      waitForStepPerformDone(gcpCreateProjectStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(gcpCreateProjectStep);
      throw t;
    }
    final String projectId = gcpCreateProjectStep.perform().getData().getProjectId();

    getNDSGroupDao()
        .setCloudContainerFields(
            getNDSGroup().getGroupId(),
            getContainer().getId(),
            Collections.singletonList(
                Pair.of(GCPCloudProviderContainer.FieldDefs.PROJECT_ID, projectId)));

    final String hostname =
        DNSRecordUtil.getLegacyDNSRecordForInstance(
            getNDSGroup(),
            getHardwareDao()
                .findReplicaSetHardwareForInstance(
                    getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0)),
            getClusterDescription(),
            getInstanceIds().get(0),
            NDSSettings.getInstanceDomainName(
                getAppSettings(),
                CloudProvider.GCP,
                getClusterDescription().getHostnameSubdomainLevel()));

    final GCPCreateStaticIpStep step =
        new GCPCreateStaticIpStep(
            (NDSPlanContext) plan.getPlanContext(),
            new State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                2,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            GCPZoneName.US_EAST1_B,
            hostname,
            InstanceHostname.HostnameScheme.LEGACY,
            getGCPApiSvc(),
            getOrphanedItemDao(),
            new Date());

    try {
      testGCPCreateStepInternal(step);
    } finally {
      performUnconditionally(() -> waitForStepRollbackDone(step));
      performUnconditionally(() -> waitForStepRollbackDone(gcpCreateProjectStep));
    }
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPCreateStaticIpStepAndOrphanOnRollback() {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final GCPReserveProjectStep gcpCreateProjectStep =
        new GCPReserveProjectStep(
            ((NDSPlanContext) plan.getPlanContext()),
            new State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                1,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            getGCPProjectDao(),
            getGCPProjectCreationSvc(),
            getOrphanedItemDao(),
            getNDSOrphanedItemSvc(),
            _groupDao);
    try {
      waitForStepPerformDone(gcpCreateProjectStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(gcpCreateProjectStep);
      throw t;
    }
    final String projectId = gcpCreateProjectStep.perform().getData().getProjectId();

    getNDSGroupDao()
        .setCloudContainerFields(
            getNDSGroup().getGroupId(),
            getContainer().getId(),
            Collections.singletonList(
                Pair.of(GCPCloudProviderContainer.FieldDefs.PROJECT_ID, projectId)));

    final String hostname =
        DNSRecordUtil.getLegacyDNSRecordForInstance(
            getNDSGroup(),
            getHardwareDao()
                .findReplicaSetHardwareForInstance(
                    getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0)),
            getClusterDescription(),
            getInstanceIds().get(0),
            NDSSettings.getInstanceDomainName(
                getAppSettings(),
                CloudProvider.GCP,
                getClusterDescription().getHostnameSubdomainLevel()));

    final GCPCreateStaticIpStep step =
        new GCPCreateStaticIpStep(
            (NDSPlanContext) plan.getPlanContext(),
            new State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                2,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            GCPZoneName.US_EAST1_B,
            hostname,
            InstanceHostname.HostnameScheme.LEGACY,
            getGCPApiSvc(),
            getOrphanedItemDao(),
            new Date());

    try {
      boolean failed = false;
      // Progress as far as requesting IP creation, then rollback.
      // This ensures the IP is orphaned and not leaked, even when a rollback happens before IP
      // provisioning completes
      do {
        if (step.perform().getStatus().isFailed()) {
          failed = true;
        }
      } while (!(step.ipCreateRequested() || failed));
    } finally {
      performUnconditionally(() -> waitForStepRollbackDone(step));

      final List<NDSOrphanedItem> orphanedItems =
          getOrphanedItemDao().findGcpOrphansForGcpProject(projectId);

      assertEquals(1, orphanedItems.size());
      assertEquals(step.getIpName().get(), orphanedItems.get(0).getId());

      performUnconditionally(() -> waitForStepRollbackDone(gcpCreateProjectStep));
    }
  }
}
