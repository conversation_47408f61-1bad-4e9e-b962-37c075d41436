package com.xgen.svc.nds.gcp.planner.networking;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.GCPReserveProjectStep;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import org.junit.Test;

public class GCPCreateNetworkStepIntTests extends GCPExternalIntTest {

  @Inject private GroupDao _groupDao;

  private void testGCPCreateNetworkStepInternal(final GCPCreateNetworkStep pStep) {
    waitForStepPerformDone(pStep);
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPCreateNetworkStep() {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final GCPReserveProjectStep gcpCreateProjectStep =
        new GCPReserveProjectStep(
            ((NDSPlanContext) plan.getPlanContext()),
            new State(plan.getId(), plan.getMoves().get(0).getId(), 1, getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            getGCPProjectDao(),
            getGCPProjectCreationSvc(),
            getOrphanedItemDao(),
            getNDSOrphanedItemSvc(),
            _groupDao);

    try {
      waitForStepPerformDone(gcpCreateProjectStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(gcpCreateProjectStep);
      throw t;
    }
    final String projectId = gcpCreateProjectStep.perform().getData().getProjectId();

    final GCPCreateNetworkStep step =
        new GCPCreateNetworkStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(plan.getId(), plan.getMoves().get(0).getId(), 2, getPlanDao()),
            getContainer(),
            projectId,
            getGCPApiSvc(),
            getNDSOrphanedItemSvc());

    try {
      testGCPCreateNetworkStepInternal(step);
    } finally {
      performUnconditionally(() -> waitForStepRollbackDone(step));
      performUnconditionally(() -> waitForStepRollbackDone(gcpCreateProjectStep));
    }
  }
}
