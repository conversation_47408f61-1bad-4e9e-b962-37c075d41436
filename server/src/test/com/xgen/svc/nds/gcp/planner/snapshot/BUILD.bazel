load("//server/src/test:rules.bzl", "test_package")

# NDS GCP CPS Integration Tests
test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = ["external"],
    runtime_deps = [
        "//server/src/main/com/xgen/svc/atm/res",
        "//server/src/main/com/xgen/svc/atm/res/api",
        "//server/src/main/com/xgen/svc/mms/res",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/main/com/xgen/svc/nds/security/res",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/dao",
        "//server/src/test/com/xgen/svc/nds/gcp",
        "@maven//:com_google_apis_google_api_services_compute",
        "@maven//:junit_junit",
        "@maven//:org_mockito_mockito_core",
    ],
)
