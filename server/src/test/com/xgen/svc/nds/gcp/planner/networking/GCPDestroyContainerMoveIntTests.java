package com.xgen.svc.nds.gcp.planner.networking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import org.junit.Test;

public class GCPDestroyContainerMoveIntTests extends GCPExternalIntTest {
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private GroupDao _groupDao;

  public void testGCPDestroyContainerMoveInternal(
      final GCPDestroyContainerMove pDummyDestroyContainerMove) throws InterruptedException {

    NDSGroup group = getNDSGroupDao().find(getGroup().getId()).get();
    GCPCloudProviderContainer container =
        (GCPCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

    final GCPCloudProviderContainer originalContainer = container;

    Result<NoData> result = pDummyDestroyContainerMove.perform();
    while (!result.getStatus().isDone()) {

      assertNotFailed(pDummyDestroyContainerMove.getClass().getSimpleName(), result);
      assertTrue(container.getNetworkName().isPresent());

      Thread.sleep(Duration.ofSeconds(5).toMillis());

      result = pDummyDestroyContainerMove.perform();
      group = getNDSGroupDao().find(getGroup().getId()).get();
      container =
          (GCPCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();
    }

    group = getNDSGroupDao().find(getGroup().getId()).get();
    container =
        (GCPCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

    assertFalse(container.getGcpProjectId().isPresent());
    assertFalse(container.getNetworkName().isPresent());
    assertEquals(0, container.getSubnets().size());

    verifySubnetDeleted(
        originalContainer.getGcpProjectId().get(), originalContainer.getSubnets().get(0));
    verifyNetworkDeleted(
        originalContainer.getGcpProjectId().get(), originalContainer.getNetworkName().get());
    verifyProjectOrphaned(originalContainer.getGcpProjectId().get());
    verifyCIDRNotInAuthRestrictions(
        getAutomationConfigSvc().findPublishedOrEmpty(getGroup().getId()),
        container.getAtlasCidr());

    assertTrue(pDummyDestroyContainerMove.rollback().getStatus().isDone());
    cleanOrphanedItems();
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPDestroyContainerMove() throws InterruptedException {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            new ArrayList<>(GCPNDSDefaults.AVAILABLE_REGIONS)
                .subList(0, GCPNDSDefaults.INTERNAL_MAX_REGIONS_PER_CONTAINER));
    final GCPDestroyContainerMove dummyDestroyContainerMove =
        GCPDestroyContainerMove.factoryCreate(plan.getPlanContext(), getContainer().getId());
    plan.addMove(provisionContainerMove);
    plan.addMove(dummyDestroyContainerMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      testGCPDestroyContainerMoveInternal(dummyDestroyContainerMove);
    } catch (final Throwable t) {
      getContainer().getSubnets().forEach(this::cleanupSubnet);
      getContainer().getNetworkName().ifPresent(this::cleanupNetwork);
      throw t;
    }
  }
}
