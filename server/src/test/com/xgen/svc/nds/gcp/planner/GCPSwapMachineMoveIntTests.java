package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.google.api.services.compute.model.Instance;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import jakarta.inject.Inject;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class GCPSwapMachineMoveIntTests extends GCPExternalIntTest {

  private ObjectId _instanceId;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    getAppSettings()
        .setProp(
            NDSSettings.Properties.KEEP_ORPHANED_IP_HOURS, "1", AppSettings.SettingType.MEMORY);
  }

  private void testGCPSwapMachineMoveInternal(final GCPSwapMachineMove pMove)
      throws InterruptedException {

    final GCPInstanceHardware originalHardware =
        (GCPInstanceHardware)
            getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);

    prepareInstanceForOSSwap(
        getReplicaSetHardwareForInstance(getClusterDescription().getName(), _instanceId).getId(),
        _instanceId,
        originalHardware.isNVMe());

    Result<NoData> result;
    do {
      result = pMove.perform();
      assertNotFailed(pMove.getClass().getSimpleName(), result);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);
      assertTrue(instanceHardware.getStaticIpName().isPresent());
      assertTrue(instanceHardware.getGCPInstanceName().isPresent());
      assertTrue(instanceHardware.getDataDeviceName().isPresent());
      assertTrue(instanceHardware.getDiskName().isPresent());
      assertTrue(instanceHardware.getHostnames().getLegacyHostname().isPresent());
      assertFalse(instanceHardware.needsForceReplacement());
      assertEquals(originalHardware.getMemberIndex(), instanceHardware.getMemberIndex());
      assertEquals(originalHardware.getStaticIpName(), instanceHardware.getStaticIpName());
      assertEquals(originalHardware.getDiskName(), instanceHardware.getDiskName());
      assertEquals(originalHardware.getHostnames(), instanceHardware.getHostnames());

      if (result.getStatus().isDone()) {
        assertEquals(originalHardware.getGCPInstanceName(), instanceHardware.getGCPInstanceName());
        assertFalse(instanceHardware.needsOSSwap());
        verifyDNSUpdated(instanceHardware);
      } else {
        if (pMove.isHardwareMetadataUpdated()) {
          assertFalse(instanceHardware.needsOSSwap());
        } else {
          assertTrue(instanceHardware.needsOSSwap());
        }
      }
      Thread.sleep(Duration.ofSeconds(5).toMillis());
    } while (!result.getStatus().isDone());

    waitForMoveRollbackDone(pMove);

    final GCPInstanceHardware instanceHardware =
        (GCPInstanceHardware)
            getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);
    assertTrue(instanceHardware.needsForceReplacement());
    assertFalse(instanceHardware.needsOSSwap());
    assertTrue(instanceHardware.getGCPInstanceName().isPresent());
  }

  private void verifyDNSUpdated(final GCPInstanceHardware instanceHardware) {
    final GCPCloudProviderContainer gcpContainer =
        (GCPCloudProviderContainer)
            getNDSGroup().getCloudProviderContainerByType(CloudProvider.GCP).get();

    // public, private, and
    final Optional<String> hostnameForAgents =
        instanceHardware
            .getHostnames()
            .getHostnameOfScheme(getClusterDescription().getHostnameSchemeForAgents().get());
    final Optional<String> publicHostname = instanceHardware.getHostnames().getPublicHostname();
    final Optional<String> privateHostname = instanceHardware.getHostnames().getPrivateHostname();
    assertNotEquals("hostnameForAgents should not be empty", Optional.empty(), hostnameForAgents);
    assertNotEquals("public hostname should not be empty", Optional.empty(), publicHostname);
    assertNotEquals("private hostname should not be empty", Optional.empty(), privateHostname);

    // We wrap this in a retry because our DNS zone cache values are set to 60 seconds
    retryAssertion(
        Duration.ofMinutes(2),
        () -> {
          try {
            assertEquals(
                "hostnameForAgents should be the instance public IP in the public hosted zone",
                instanceHardware.getPublicIP().get(),
                InetAddress.getByName(hostnameForAgents.get()).getHostAddress());
          } catch (UnknownHostException pE) {
            throw new AssertionError(
                String.format("hostname %s does not exist", hostnameForAgents));
          }
          try {
            assertEquals(
                "public hostname should be the instance public IP in the public hosted zone",
                instanceHardware.getPublicIP().get(),
                InetAddress.getByName(publicHostname.get()).getHostAddress());
          } catch (UnknownHostException pE) {
            throw new AssertionError(String.format("hostname %s does not exist", publicHostname));
          }
          try {
            final Instance instance =
                getGCPApiSvc()
                    .getInstance(
                        gcpContainer.getGcpOrganizationId(),
                        gcpContainer.getGcpProjectId().get(),
                        instanceHardware.getZoneName().get(),
                        getLogger(),
                        instanceHardware.getGCPInstanceName().get());
            final String freshPrivateIp = instance.getNetworkInterfaces().get(0).getNetworkIP();
            assertEquals(
                "private hostname should be the instance private IP in the public hosted zone",
                freshPrivateIp,
                InetAddress.getByName(privateHostname.get()).getHostAddress());
          } catch (UnknownHostException pE) {
            throw new AssertionError(String.format("hostname %s does not exist", privateHostname));
          }
        });
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testGCPSwapMachineMoveRollback_ForceReplace() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    _instanceId = findInstanceIdWithNonZeroMemberIndex();

    final GCPProvisionMachineMove provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(_instanceId),
            getClusterDescription().getName(),
            _instanceId,
            false);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    final Plan swapPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPSwapMachineMove swapMove =
        GCPSwapMachineMove.factoryCreate(
            swapPlan.getPlanContext(), getClusterDescription().getName(), _instanceId);
    swapPlan.addMove(swapMove);
    getPlanDao().save(swapPlan);

    try {
      GCPInstanceHardware originalHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);

      prepareInstanceForOSSwap(
          getReplicaSetHardwareForInstance(getClusterDescription().getName(), _instanceId).getId(),
          _instanceId,
          originalHardware.isNVMe());

      // Fail on wait for automation agent, the very last step in perform
      final GCPSwapMachineMove spySwapMove = spy(swapMove);

      doReturn(Result.failed()).when(spySwapMove).waitForAutomationAgentStart(any(), any());

      originalHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);
      Result<Result.NoData> result;
      do {
        result = spySwapMove.perform();
        Thread.sleep(Duration.ofSeconds(5).toMillis());
      } while (!result.getStatus().isFailed());

      waitForMoveRollbackDone(spySwapMove);
      GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);

      // Verify instance has new hardware with need force replacement set
      assertEquals(originalHardware.getGCPInstanceName(), instanceHardware.getGCPInstanceName());
      assertEquals(originalHardware.getStaticIpName(), instanceHardware.getStaticIpName());
      assertEquals(originalHardware.getDiskName(), instanceHardware.getDiskName());
      assertTrue(instanceHardware.needsForceReplacement());
    } catch (final Throwable t) {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), _instanceId);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware) replicaSetHardware.getById(_instanceId).get();
      if (instanceHardware.isProvisioned()) {
        cleanupGCPInstance(instanceHardware);
        cleanupPersistentDisk(instanceHardware);
        cleanupStaticIp(instanceHardware);
        cleanSRVRecords(getNDSGroup(), getClusterDescription().getName());
      }
      throw t;
    }

    // Cleanup
    waitForMoveRollbackDone(provisionMachineMove);
    waitForMoveRollbackDone(provisionContainerMove);
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testGCPSwapMachineMoveRollback_RevertToOldHardware() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    _instanceId = findInstanceIdWithNonZeroMemberIndex();

    final GCPProvisionMachineMove provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(_instanceId),
            getClusterDescription().getName(),
            _instanceId,
            false);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    final Plan swapPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPSwapMachineMove swapMove =
        GCPSwapMachineMove.factoryCreate(
            swapPlan.getPlanContext(), getClusterDescription().getName(), _instanceId);
    swapPlan.addMove(swapMove);
    getPlanDao().save(swapPlan);

    try {
      GCPInstanceHardware originalHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);
      prepareInstanceForOSSwap(
          getReplicaSetHardwareForInstance(getClusterDescription().getName(), _instanceId).getId(),
          _instanceId,
          originalHardware.isNVMe());

      // Fail on delete instance
      final GCPSwapMachineMove spySwapMove = spy(swapMove);
      doReturn(Result.failed()).when(spySwapMove).deleteInstance(any(), any(), any());

      Result<NoData> result;
      do {
        result = spySwapMove.perform();
        Thread.sleep(Duration.ofSeconds(5).toMillis());
      } while (!result.getStatus().isFailed());

      waitForMoveRollbackDone(spySwapMove);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);

      // verify instance is reverted to same hardware
      assertEquals(originalHardware.getGCPInstanceName(), instanceHardware.getGCPInstanceName());
      assertEquals(originalHardware.getStaticIpName(), instanceHardware.getStaticIpName());
      assertEquals(originalHardware.getDiskName(), instanceHardware.getDiskName());
      assertFalse(instanceHardware.needsForceReplacement());

    } catch (final Throwable t) {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), _instanceId);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware) replicaSetHardware.getById(_instanceId).get();
      if (instanceHardware.isProvisioned()) {
        cleanupGCPInstance(instanceHardware);
        cleanupPersistentDisk(instanceHardware);
        cleanupStaticIp(instanceHardware);
        cleanSRVRecords(getNDSGroup(), getClusterDescription().getName());
      }
      throw t;
    }

    // Cleanup
    waitForMoveRollbackDone(provisionMachineMove);
    waitForMoveRollbackDone(provisionContainerMove);
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testGCPSwapMachineMoveRollback_BeforeNewInstanceProvisioned()
      throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    _instanceId = findInstanceIdWithNonZeroMemberIndex();

    final GCPProvisionMachineMove provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(_instanceId),
            getClusterDescription().getName(),
            _instanceId,
            false);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    final Plan swapPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPSwapMachineMove swapMove =
        GCPSwapMachineMove.factoryCreate(
            swapPlan.getPlanContext(), getClusterDescription().getName(), _instanceId);
    swapPlan.addMove(swapMove);
    getPlanDao().save(swapPlan);

    try {
      GCPInstanceHardware originalHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);
      prepareInstanceForOSSwap(
          getReplicaSetHardwareForInstance(getClusterDescription().getName(), _instanceId).getId(),
          _instanceId,
          originalHardware.isNVMe());

      // Fail on create new instance
      final GCPSwapMachineMove spySwapMove = spy(swapMove);
      doReturn(Result.failed())
          .when(spySwapMove)
          .createOSSwapInstance(
              any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean());

      Result<NoData> result;
      do {
        result = spySwapMove.perform();
        Thread.sleep(Duration.ofSeconds(5).toMillis());
      } while (!result.getStatus().isFailed());

      waitForMoveRollbackDone(spySwapMove);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware)
              getInstanceHardware(getGroupId(), getClusterDescription().getName(), _instanceId);

      // If the move fails to create a new instance, we will have neither the old instance nor a new
      // one. In this case, we force-replace the instance
      assertEquals(originalHardware.getGCPInstanceName(), instanceHardware.getGCPInstanceName());
      assertEquals(originalHardware.getStaticIpName(), instanceHardware.getStaticIpName());
      assertEquals(originalHardware.getDiskName(), instanceHardware.getDiskName());
      assertTrue(instanceHardware.needsForceReplacement());
    } catch (final Throwable t) {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), _instanceId);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware) replicaSetHardware.getById(_instanceId).get();
      if (instanceHardware.isProvisioned()) {
        cleanupGCPInstance(instanceHardware);
        cleanupPersistentDisk(instanceHardware);
        cleanupStaticIp(instanceHardware);
        cleanSRVRecords(getNDSGroup(), getClusterDescription().getName());
      }
      throw t;
    }

    // Cleanup
    waitForMoveRollbackDone(provisionMachineMove);
    waitForMoveRollbackDone(provisionContainerMove);
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testGCPSwapMachineMove() throws InterruptedException {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    _instanceId = findInstanceIdWithNonZeroMemberIndex();

    final GCPProvisionMachineMove provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(_instanceId),
            getClusterDescription().getName(),
            _instanceId,
            false);
    final GCPSwapMachineMove dummySwapOSMove =
        GCPSwapMachineMove.factoryCreate(
            plan.getPlanContext(), getClusterDescription().getName(), _instanceId);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    plan.addMove(dummySwapOSMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    try {
      testGCPSwapMachineMoveInternal(dummySwapOSMove);
    } catch (final Throwable t) {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), _instanceId);
      final GCPInstanceHardware instanceHardware =
          (GCPInstanceHardware) replicaSetHardware.getById(_instanceId).get();
      if (instanceHardware.isProvisioned()) {
        cleanupGCPInstance(instanceHardware);
        cleanupPersistentDisk(instanceHardware);
        cleanupStaticIp(instanceHardware);
        cleanSRVRecords(getNDSGroup(), getClusterDescription().getName());
      }
      throw t;
    }
  }

  private ObjectId findInstanceIdWithNonZeroMemberIndex() {
    // Find a hardware instance with memberIndex > 0
    return getInstanceIds().stream()
        .filter(
            instanceId ->
                getInstanceHardware(getGroupId(), getClusterDescription().getName(), instanceId)
                        .getMemberIndex()
                    > 0)
        .findFirst()
        .get();
  }

  private void prepareInstanceForOSSwap(
      final BasicDBObject pReplicaSetHardwareId,
      final ObjectId pInstanceId,
      final boolean pIsInternalInstance) {
    _replicaSetHardwareDao.setInstanceOSSwap(
        pReplicaSetHardwareId, pInstanceId, pIsInternalInstance, true);

    _replicaSetHardwareDao.setAction(
        pReplicaSetHardwareId, pInstanceId, pIsInternalInstance, Action.OS_SWAP);

    _replicaSetHardwareDao.clearInstanceForceReplacement(
        pReplicaSetHardwareId, pInstanceId, pIsInternalInstance);
  }
}
