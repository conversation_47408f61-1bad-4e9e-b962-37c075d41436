package com.xgen.svc.nds.gcp;

import static com.xgen.svc.nds.gcp.GCPTestConstants.GCP_COMMON_SECRETS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.amazonaws.services.route53.model.RRType;
import com.google.api.services.compute.model.Address;
import com.google.inject.Injector;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.brs.daemon._public.grid.BackupInjector;
import com.xgen.cloud.brs.daemon._public.grid.SeedBlockstoreConfigs;
import com.xgen.cloud.brs.daemon._public.grid.svc.BackupSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.capacity._public.svc.GCPCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.CloudProviderSettingsDao;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderSettings;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._private.dao.GCPPeerVpcDao;
import com.xgen.cloud.nds.gcp._private.dao.GCPProjectDao;
import com.xgen.cloud.nds.gcp._public.model.GCPAtlasSubnet;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSSettings;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem;
import com.xgen.cloud.nds.gcp._public.model.GCPRegion;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPZone;
import com.xgen.cloud.nds.gcp._public.model.GCPZone.Status;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPAutoScaling;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.gcp._public.svc.GCPCMEKSvc;
import com.xgen.cloud.nds.planning.common._public.util.NDSMoveTags;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.ClusterMaintenanceDecisions;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.NDSLogDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.admin.NDSAdminJobDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBUser;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.nds.NDSLocalSecretsManager;
import com.xgen.svc.nds.gcp.planner.GCPProvisionMachineMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPEnsureNetworkPermissionsAppliedMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.gcp.svc.GCPCloudProviderContainerSvc;
import com.xgen.svc.nds.gcp.svc.GCPProjectCreationSvc;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.planner.BasePlannerIntTest;
import com.xgen.svc.nds.planner.EnsureConnectivityForTopologyChangeMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import com.xgen.svc.nds.security.svc.NDSACMESvc;
import com.xgen.svc.nds.svc.CloudChefConfSvc;
import com.xgen.svc.nds.svc.NDSAdminBackupSnapshotSvc;
import com.xgen.svc.nds.svc.NDSOrphanedItemSvc;
import com.xgen.svc.nds.svc.NDSRemoteImageSvc;
import com.xgen.svc.nds.svc.planning.NDSPlanningSvc;
import com.xgen.svc.nds.svc.planning.NDSPlanningSvc.AutomationConfigContext;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GCPExternalIntTest extends BasePlannerIntTest {
  private static final String NDS_AWS_TESTING_ACCESS_KEY = "local.aws.accessKey";
  private static final String NDS_AWS_TESTING_SECRET_KEY = "local.aws.secretKey";
  private static final Logger LOG = LoggerFactory.getLogger(GCPExternalIntTest.class);
  // CleanItems may have deleted things fast enough the entire
  // project is already deleted, so we may be unauthorised
  public static final Set<ErrorCode> DELETED_PROJECT_ITEM_ERRORS =
      Set.of(
          CommonErrorCode.NO_AUTHORIZATION,
          CommonErrorCode.NOT_FOUND,
          GCPErrorCode.ACCESS_NOT_CONFIGURED);
  private Group _group;
  private AppUser _user;
  private NDSDBUser _dbuser;
  private ObjectId _containerId;
  private List<ObjectId> _instanceIds;
  private ClusterDescription _clusterDescription;
  @Inject private AppSettings _appSettings;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private GCPOrganizationDao _gcpOrganizationDao;
  @Inject private GCPProjectDao _gcpProjectDao;
  @Inject private GCPProjectCreationSvc _gcpProjectSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private GCPApiSvc _gcpApiSvc;
  @Inject private NDSPlanContext.Factory _ndsPlanContextFactory;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSOrphanedItemDao _orphanedItemDao;
  @Inject private NDSOrphanedItemSvc _ndsOrphanedItemSvc;
  @Inject private CloudProviderSettingsDao _cloudProviderSettingsDao;
  @Inject private AutomationMongoDbVersionSvc _mongoDbVersionSvc;

  @Inject private NDSClusterSvc _clusterSvc;
  @Inject private NDSPlanningSvc _planningSvc;
  @Inject private NDSOrphanedItemSvc _orphanedItemSvc;
  @Inject private BackupSvc _backupSvc;
  @Inject private GroupSvc _groupSvc;
  @Inject private NDSPlanningSvc _ndsPlanningSvc;
  @Inject private GCPCloudProviderContainerSvc _gcpCloudProviderContainerSvc;
  @Inject private NDSAdminJobDao _ndsAdminJobDao;
  @Inject private GCPPeerVpcDao _gcpPeerVpcDao;
  @Inject private NDSLogDao _ndsLogDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private NDSACMESvc _ndsACMESvc;
  @Inject private NDSLocalSecretsManager _ndsLocalSecretsManager;
  @Inject private CloudChefConfSvc _cloudChefConfSvc;
  @Inject private NDSRemoteImageSvc _remoteImageSvc;
  @Inject private GroupDao _groupDao;
  @Inject private NDSAdminBackupSnapshotSvc _ndsAdminBackupSnapshotSvc;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private Injector _injector;
  @Inject private GCPCMEKSvc _gcpCmekSvc;
  @Inject private GCPCapacityDenylistSvc _gcpCapacityDenylistSvc;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createBackupAdminUser(_group);
    _mongoDbVersionSvc.autoUpdateDefaultVersions();
    _clusterSvc.start();
    final GCPOrganization gcpOrganization = makeGCPOrganization();
    _ndsGroupSvc.ensureGroup(_group.getId());
    final GCPCloudProviderContainer container =
        new GCPCloudProviderContainer(gcpOrganization.getId(), NDSDefaults.ATLAS_CIDR);
    _containerId = getNDSGroupDao().addCloudContainer(_group.getId(), container);
    _clusterDescription = getDefaultClusterDescription();
    _clusterDescriptionDao.save(_clusterDescription);
    _ndsPlanningSvc.refreshClusterHardwareActions(
        _ndsGroupDao.find(_group.getId()).get(),
        getGroup(),
        Collections.emptyList(),
        0L,
        LoggerFactory.getLogger("foo"),
        Cluster.getCluster(_clusterDescription, Collections.emptyList()),
        List.of(),
        new AutomationConfigContext(_automationConfigSvc, _group.getId()),
        new ClusterMaintenanceDecisions.Builder());
    refreshInstanceIds(getClusterDescription());
    _dbuser =
        NDSDBUser.builder()
            .username("admin")
            .database("admin")
            .roles(
                Collections.singletonList(
                    _ndsGroupSvc.getNDSDBRoleSvc().factoryCreate("admin", "root")))
            .scopes(Collections.emptyList())
            .labels(Collections.emptyList())
            .isEditable(true)
            .scramSha1Auth(NDSDBUserView.getScramSha1Auth("admin", "admin", 10000))
            .scramSha256Auth(NDSDBUserView.getScramSha256Auth("admin", "admin", 15000))
            .build();
    _ndsGroupDao.addDatabaseUser(_group.getId(), _dbuser);
    // Force an automation config move
    _clusterDescriptionDao.setNeedsPublishWithRestartForCluster(
        _group.getId(), _clusterDescription.getName(), new Date());
    _ndsGroupDao.setNeedsPublishForGroup(_group.getId(), new Date());
    final SeedBlockstoreConfigs configs =
        new SeedBlockstoreConfigs(new BackupInjector(_injector, _appSettings));
    configs.ensureDefaultConfigs(null);
    ensureUsersForGroup(_group.getId(), getNdsPlanContextFactory());
    _cloudProviderSettingsDao.save(new CloudProviderSettings(CloudProvider.GCP, 100));
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    cleanSRVRecords(getNDSGroup(), getClusterDescription().getName());
    cleanOrphanedItems();
    super.tearDown();
  }

  private GCPOrganization makeGCPOrganization() throws Exception {
    final String gcpClientCertificate;
    final String gcpBillingAccountId;
    final String gcpFolderId;
    final JSONObject secrets = _ndsLocalSecretsManager.getAWSSecret(GCP_COMMON_SECRETS);
    try {
      gcpClientCertificate = secrets.getString("clientCertificateFile");
    } catch (final Exception e) {
      throw new RuntimeException(
          String.format(
              "Could not load client certificate file (%s)",
              GCPTestConstants.GCP_CLIENT_CERTIFICATE_FILE),
          e);
    }
    try {
      gcpBillingAccountId = secrets.getString("billingAccountId");
    } catch (final Exception e) {
      throw new RuntimeException("Could not load gcp billing account id", e);
    }
    try {
      gcpFolderId = _appSettings.getStrProp(GCPTestConstants.GCP_FOLDER_ID);
    } catch (final Exception e) {
      throw new RuntimeException("Could not load gcp folder id", e);
    }
    final GCPOrganization organization =
        new GCPOrganization("local", 100, gcpFolderId, gcpClientCertificate, gcpBillingAccountId);
    _gcpOrganizationDao.save(organization);
    final ObjectId organizationId = organization.getId();
    final GCPRegion gcpRegion = new GCPRegion(GCPRegionName.EASTERN_US);
    gcpRegion
        .getZones()
        .addAll(
            Arrays.stream(GCPZoneName.values())
                .filter(z -> z.getRegion().equals(gcpRegion.getName()))
                .map(z -> new GCPZone(GCPZoneName.valueOf(z.name()), Status.AVAILABLE))
                .collect(Collectors.toList()));
    _gcpOrganizationDao.addRegion(organizationId, gcpRegion);
    return _gcpOrganizationDao.find(organizationId).get();
  }

  protected List<ReplicationSpec> getDefaultReplicationSpecs() {
    return getReplicationSpecs(1, 3);
  }

  protected ClusterDescription getDefaultClusterDescription() {
    final Date now = new Date();
    final InstanceFamily instanceFamily =
        GCPNDSInstanceSize.M10.getInstanceFamilies().keySet().iterator().next();
    return new ClusterDescription.Builder<>()
        .setDiskSizeGB(50)
        .setName("cluster1")
        .setClusterNamePrefix("cluster1")
        .setDnsPin(getNDSGroup().getDNSPin())
        .setUniqueId(new ObjectId())
        .setClusterType(ClusterDescription.ClusterType.REPLICASET)
        .setInternalClusterRole(InternalClusterRole.NONE)
        .setReplicationSpecList(getDefaultReplicationSpecs())
        .mixin(ClusterDescriptionBuilderTestMixin::new)
        .updateAllHardware(
            new GCPHardwareSpec.Builder()
                .setInstanceSize(GCPNDSInstanceSize.M10)
                .setInstanceFamilyAndOS(
                    instanceFamily,
                    CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
                        .getInstanceHardwareProvider()
                        .getDefaultOs(_appSettings, instanceFamily)))
        .setGroupId(_group.getId())
        .setCreateDate(now)
        .setLastUpdateDate(now)
        .setMongoDBVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion())
        .setMongoDBMajorVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getMajorVersionString())
        .setMongoUriHosts(new String[0])
        .setPrivateMongoUriHosts(new String[0])
        .setMongoUriLastUpdateDate(now)
        .setState(ClusterDescription.State.WORKING)
        .setRestoreJobIds(Collections.emptyList())
        .setAutoScalingForProvider(
            CloudProvider.GCP,
            new GCPAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
            NodeTypeFamily.BASE)
        .setBiConnector(
            new ClusterDescription.BiConnector(NDSModelTestFactory.getDefaultBiConnector()))
        .setGeoSharding(new GeoSharding(NDSModelTestFactory.getDefaultGeoSharding()))
        .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
        .setHostnameSchemeForAgents(InstanceHostname.HostnameScheme.LEGACY)
        .setHostnameSubdomainLevel(InstanceHostname.SubdomainLevel.GCP)
        .setDeploymentClusterName("cluster1")
        .setDiskBackupEnabled(true)
        .build();
  }

  protected List<ReplicationSpec> getReplicationSpecs(final int pNumShards, final int pNumNodes) {
    return getReplicationSpecs(new ObjectId(), pNumShards, pNumNodes);
  }

  protected List<ReplicationSpec> getReplicationSpecs(
      final ObjectId pId, final int pNumShards, final int pNumNodes) {
    return Collections.singletonList(
        new ReplicationSpec(
            pId,
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            pNumShards,
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    GCPRegionName.EASTERN_US, 1, pNumNodes, 0, 0, 0))));
  }

  protected void refreshInstanceIds(final ClusterDescription pClusterDescription) {
    final Cluster cluster =
        Cluster.getCluster(
            pClusterDescription,
            getHardwareDao()
                .findByCluster(getNDSGroup().getGroupId(), pClusterDescription.getName()));
    _instanceIds =
        cluster.getLiveReplicaSets().stream()
            .flatMap(h -> h.getHardware().stream())
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toList());
  }

  public void cleanOrphanedItems() {
    // Set all orphaned items as ready to be deleted to avoid cases where orphaned items keep days
    // is set too far into the future
    final long cleanStartTimeNano = System.nanoTime();

    _orphanedItemDao.updateAllMajority(
        new BasicDBObject(),
        new BasicDBObject(
            BaseDao.SET,
            new BasicDBObject()
                .append(NDSOrphanedItem.FieldDefs.KEEP_UNTIL_DATE, new Date(0L))
                .append(
                    NDSOrphanedItem.FieldDefs.EXPIRATION_DATE,
                    DateUtils.addSeconds(new Date(), 15))));
    // Some resources take time to clean up and the orphan service won't
    // delete the project until they're gone.
    while (!_orphanedItemSvc.findAllOrphanedItems().isEmpty()) {
      _orphanedItemSvc.cleanItems();

      try {
        TimeUnit.SECONDS.sleep(10);
      } catch (final InterruptedException e) {
        break;
      }

      final long currentTimeNano = System.nanoTime();
      final long elapsedTimeNano = currentTimeNano - cleanStartTimeNano;

      if (elapsedTimeNano > Duration.ofMinutes(10).toNanos()) {
        fail(
            String.format(
                "Failed to clean some orphaned items: %s",
                _orphanedItemSvc.findAllOrphanedItems()));
      }
    }
  }

  protected GCPOrphanedItem getOrphanedItemById(final String pId) {
    final BasicDBObject doc =
        _orphanedItemDao.findOne(new BasicDBObject(OrphanedItem.FieldDefs.ID, pId));
    return doc == null ? null : new GCPOrphanedItem(doc);
  }

  protected Plan setupPlanWithContainerAndSingleInstance() {
    return setupPlanWithContainerAndSingleInstance(
        getClusterDescription().getRegionNames().stream()
            .map(region -> (GCPRegionName) region)
            .collect(Collectors.toList()));
  }

  protected Plan setupPlanWithContainerAndSingleInstance(final List<GCPRegionName> regions) {
    final ClusterDescription initialClusterDescription =
        getClusterDescription()
            .copy()
            .setReplicationSpecList(
                getReplicationSpecs(
                    getClusterDescription().getReplicationSpecsWithShardData().get(0).getId(),
                    1,
                    1))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder()
                    .setInstanceSize(GCPNDSInstanceSize.M10)
                    .setInstanceFamilyAndOS(
                        GCPInstanceFamily.G1,
                        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
                            .getInstanceHardwareProvider()
                            .getDefaultOs(_appSettings, GCPInstanceFamily.G1)))
            .setDiskSizeGB(20)
            .build();
    getClusterDescriptionDao().save(initialClusterDescription);
    final ObjectId instanceId = getInstanceIds().get(0);
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(), Collections.emptyMap(), getContainer().getId(), regions);
    plan.addMove(provisionContainerMove);
    final Move ensureNetworkPermissionsAppliedMove =
        GCPEnsureNetworkPermissionsAppliedMove.factoryCreate(
            plan.getPlanContext(), getTags(instanceId), getContainer().getId());
    plan.addMove(ensureNetworkPermissionsAppliedMove);
    final ProcessAutomationConfigPerClusterMove configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            initialClusterDescription.getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);

    final Move provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(instanceId),
            initialClusterDescription.getName(),
            instanceId,
            false);
    plan.addMove(provisionMachineMove);
    ensureNetworkPermissionsAppliedMove.addPredecessor(provisionContainerMove);
    provisionMachineMove.addPredecessor(provisionContainerMove);
    configMove.addPredecessor(provisionMachineMove);

    final EnsureConnectivityForTopologyChangeMove ecftcm =
        EnsureConnectivityForTopologyChangeMove.factoryCreate(
            plan.getPlanContext(), getClusterDescription().getName());
    plan.addMove(ecftcm);
    ecftcm.addPredecessor(configMove);

    getPlanDao().save(plan);
    return plan;
  }

  protected Plan setupPlanWithContainerAndInstances() {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    plan.addMove(provisionContainerMove);

    final Move ensureNetworkPermissionsAppliedMove =
        GCPEnsureNetworkPermissionsAppliedMove.factoryCreate(
            plan.getPlanContext(), getTags(getInstanceIds().get(0)), getContainer().getId());
    plan.addMove(ensureNetworkPermissionsAppliedMove);
    ensureNetworkPermissionsAppliedMove.addPredecessor(provisionContainerMove);

    final ProcessAutomationConfigPerClusterMove configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);

    final List<ObjectId> instanceIds = getInstanceIds();
    instanceIds.stream()
        .map(
            id ->
                GCPProvisionMachineMove.factoryCreate(
                    plan.getPlanContext(),
                    getTags(id),
                    getClusterDescription().getName(),
                    id,
                    false))
        .peek(plan::addMove)
        .peek(m -> m.addPredecessor(ensureNetworkPermissionsAppliedMove))
        .forEach(configMove::addPredecessor);

    final EnsureConnectivityForTopologyChangeMove ecftcm =
        EnsureConnectivityForTopologyChangeMove.factoryCreate(
            plan.getPlanContext(), getClusterDescription().getName());
    plan.addMove(ecftcm);
    ecftcm.addPredecessor(configMove);

    getPlanDao().save(plan);
    return plan;
  }

  protected void verifyResourceDeletedViaException(
      final Runnable pRunnable, final Set<ErrorCode> pExpectedErrorCodes) {
    verifyResourceDeletedViaException(pRunnable, Duration.ofMinutes(3), pExpectedErrorCodes);
  }

  protected void verifyResourceDeletedViaException(
      final Runnable pRunnable, Duration pTimeout, final Set<ErrorCode> pExpectedErrorCodes) {
    try {
      if (!waitForCondition(
          pTimeout,
          () -> {
            pRunnable.run();
            return false;
          })) {
        fail(
            "Timed out waiting for resource to be deleted, waited for "
                + pTimeout.getSeconds()
                + " seconds");
      }
    } catch (final GCPApiException e) {
      assertTrue(pExpectedErrorCodes.contains(e.getErrorCode()));
    }
  }

  protected void verifyProjectDeleted(final String pProjectId) {
    verifyResourceDeletedViaStatus(
        () ->
            getGCPApiSvc()
                .getProject(getContainer().getGcpOrganizationId(), LOG, pProjectId)
                .getState()
                .equals("DELETE_REQUESTED"));
  }

  protected void verifyProjectDeleted(final ObjectId pOrganizationId, final String pProjectId) {
    verifyResourceDeletedViaStatus(
        () ->
            getGCPApiSvc()
                .getProject(pOrganizationId, LOG, pProjectId)
                .getState()
                .equals("DELETE_REQUESTED"));
  }

  protected void verifySnapshotDeleted(
      final ObjectId pOrganizationId, final String pProjectId, final String pSnapshotName) {
    verifyResourceDeletedViaException(
        () -> getGCPApiSvc().findSnapshot(pOrganizationId, pProjectId, pSnapshotName, LOG),
        DELETED_PROJECT_ITEM_ERRORS);
  }

  protected void verifyFirewallRuleDeleted(
      final String pProjectId, final String pFirewallRuleName) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getFirewall(
                    getContainer().getGcpOrganizationId(), pProjectId, LOG, pFirewallRuleName),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected void verifyNetworkDeleted(final String pProjectId, final String pNetworkName) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getNetwork(getContainer().getGcpOrganizationId(), pProjectId, LOG, pNetworkName),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected void cleanupNetwork(final String pNetworkName) {
    performUnconditionally(
        () ->
            getGCPApiSvc()
                .deleteNetwork(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    LOG,
                    pNetworkName));
  }

  protected void verifySubnetDeleted(final String pProjectId, final GCPAtlasSubnet pSubnet) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getSubnet(
                    getContainer().getGcpOrganizationId(),
                    pProjectId,
                    pSubnet.getRegion(),
                    LOG,
                    pSubnet.getSubnetName()),
        DELETED_PROJECT_ITEM_ERRORS);
  }

  protected void cleanupSubnet(final GCPAtlasSubnet pSubnet) {
    performUnconditionally(
        () ->
            getGCPApiSvc()
                .deleteSubnet(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pSubnet.getRegion(),
                    LOG,
                    pSubnet.getSubnetName()));
  }

  protected void cleanupGCPInstance(final GCPInstanceHardware pGCPInstanceHardware) {
    performUnconditionally(
        () ->
            getGCPApiSvc()
                .deleteInstance(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pGCPInstanceHardware.getZoneName().get(),
                    LOG,
                    pGCPInstanceHardware.getGCPInstanceName().get()));
  }

  protected void verifyGCPInstanceDeleted(final GCPInstanceHardware pGCPInstanceHardware) {
    verifyGCPInstanceDeleted(
        getContainer().getGcpOrganizationId(),
        getContainer().getGcpProjectId().get(),
        pGCPInstanceHardware.getZoneName().get(),
        pGCPInstanceHardware.getGCPInstanceName().get());
  }

  protected void verifyGCPInstanceDeleted(
      final ObjectId pOrgId,
      final String pProjectId,
      final GCPZoneName pZoneName,
      final String pInstanceName) {
    verifyResourceDeletedViaException(
        () -> getGCPApiSvc().getInstance(pOrgId, pProjectId, pZoneName, LOG, pInstanceName),
        Duration.ofMinutes(6),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected String getInstanceStatus(final GCPInstanceHardware pGCPInstanceHardware) {
    return getGCPApiSvc()
        .getInstance(
            getContainer().getGcpOrganizationId(),
            getContainer().getGcpProjectId().get(),
            pGCPInstanceHardware.getZoneName().get(),
            LOG,
            pGCPInstanceHardware.getGCPInstanceName().get())
        .getStatus();
  }

  protected void verifyPublicDNSRecordDeleted(final String pDNSRecord) {
    verifyDNSRecordDeleted(
        pDNSRecord,
        RRType.A,
        NDSSettings.getRoute53PublicHostedZonesForClusterHost(
            getAppSettings(),
            CloudProvider.GCP,
            getClusterDescription().getHostnameSubdomainLevel()));
  }

  protected void verifyPrivateDNSRecordDeleted(final String pDNSRecord) {
    verifyDNSRecordDeleted(
        pDNSRecord, RRType.A, GCPNDSSettings.getRoute53PrivateHostedZoneId(getAppSettings()));
    verifyDNSRecordDeleted(
        pDNSRecord,
        RRType.A,
        GCPNDSSettings.getRoute53MongoDBLevelPrivateHostedZoneId(getAppSettings()));
  }

  protected void cleanupStaticIp(final GCPInstanceHardware pInstanceHardware) {
    performUnconditionally(
        () ->
            getGCPApiSvc()
                .deleteStaticIp(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pInstanceHardware.getZoneName().get().getRegion(),
                    LOG,
                    pInstanceHardware.getStaticIpName().get()));
  }

  protected void verifyStaticIpDeleted(final GCPInstanceHardware pInstanceHardware) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getStaticIp(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pInstanceHardware.getZoneName().get().getRegion(),
                    LOG,
                    pInstanceHardware.getStaticIpName().get()),
        DELETED_PROJECT_ITEM_ERRORS);
  }

  protected void verifyStaticIpAttached(
      final GCPRegionName pRegionName, final String pStaticIpName) {
    final Address address =
        getGCPApiSvc()
            .getStaticIp(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pRegionName,
                LOG,
                pStaticIpName);
    assertEquals("IN_USE", address.getStatus());
  }

  protected void verifyStaticIpDetached(
      final GCPRegionName pRegionName, final String pStaticIpName) {
    final Address address =
        getGCPApiSvc()
            .getStaticIp(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pRegionName,
                LOG,
                pStaticIpName);
    assertEquals("RESERVED", address.getStatus());
  }

  protected void cleanupPersistentDisk(final GCPInstanceHardware pInstanceHardware) {
    performUnconditionally(
        () ->
            getGCPApiSvc()
                .deleteDisk(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pInstanceHardware.getZoneName().get(),
                    LOG,
                    pInstanceHardware.getDiskName().get()));
  }

  protected void verifyPersistentDiskDeleted(final GCPInstanceHardware pInstanceHardware) {
    verifyPersistentDiskDeleted(
        getContainer().getGcpOrganizationId(),
        getContainer().getGcpProjectId().get(),
        pInstanceHardware.getZoneName().get(),
        pInstanceHardware.getDiskName().get());
  }

  protected void verifyPersistentDiskDeleted(
      final ObjectId pOrgId,
      final String pProjectId,
      final GCPZoneName pZoneName,
      final String pDiskName) {
    verifyResourceDeletedViaException(
        () -> getGCPApiSvc().getDisk(pOrgId, pProjectId, pZoneName, LOG, pDiskName),
        DELETED_PROJECT_ITEM_ERRORS);
  }

  protected void cleanupProject(final String pGcpProjectId) {
    performUnconditionally(
        () ->
            getGCPApiSvc()
                .deleteProject(getContainer().getGcpOrganizationId(), pGcpProjectId, LOG));
  }

  @Override
  protected void ensureNetworkPermissionsApplied() {
    final Plan ensureNetworkPermissionsPlan =
        new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final Move ensureNetworkPermissionsAppliedMove =
        GCPEnsureNetworkPermissionsAppliedMove.factoryCreate(
            ensureNetworkPermissionsPlan.getPlanContext(),
            getTags(getInstanceIds().get(0)),
            getContainer().getId());
    ensureNetworkPermissionsPlan.addMove(ensureNetworkPermissionsAppliedMove);
    getPlanDao().save(ensureNetworkPermissionsPlan);
    waitForPlanPerformSuccess(ensureNetworkPermissionsPlan);
  }

  protected void verifyStaticIpOrphaned(final GCPInstanceHardware pInstanceHardware) {
    assertNotNull(getOrphanedItemById(pInstanceHardware.getStaticIpName().get()));
  }

  protected void verifyPersistentDiskOrphaned(
      final GCPInstanceHardware pInstanceHardware,
      final ObjectId pClusterUniqueId,
      final String pHostname) {
    final NDSOrphanedItem item =
        pInstanceHardware.getDiskName().map(this::getOrphanedItemById).orElse(null);
    assertNotNull(item);
    assertEquals(Optional.ofNullable(pClusterUniqueId), item.getClusterUniqueId());
    assertEquals(Optional.ofNullable(pHostname), item.getHostname());
  }

  protected void verifyProjectOrphaned(final String pGcpProjectid) {
    performUnconditionally(() -> getOrphanedItemById(pGcpProjectid));
  }

  protected void verifyResourceLabels(final Map<String, String> pLabels) {
    assertNotNull(pLabels);
    assertFalse(pLabels.isEmpty());
    final List<String> expectedLabels =
        Arrays.asList(
            "clustername",
            "clusteruniqueid",
            "containerid",
            "createdate",
            "environment",
            "groupid",
            "originhostnameid",
            "groupname");
    if (!StringUtils.isEmpty(System.getProperty("version.id"))) {
      expectedLabels.add("versionid");
    }
    if (!StringUtils.isEmpty(System.getProperty("task.name"))) {
      expectedLabels.add("taskname");
    }

    expectedLabels.forEach(label -> assertTrue(pLabels.containsKey(label)));
  }

  protected void verifyInternalForwardingRuleLabels(final Map<String, String> pLabels) {
    assertNotNull(pLabels);
    assertFalse(pLabels.isEmpty());
    final List<String> expectedLabels =
        Arrays.asList(
            "containerid", "createdate", "environment", "groupid", "originhostnameid", "groupname");

    expectedLabels.forEach(label -> assertTrue(pLabels.containsKey(label)));
  }

  protected void verifyHealthCheckDeleted(
      final String pHealthCheckName, final GCPRegionName pRegionName) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getHealthCheck(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pRegionName,
                    LOG,
                    pHealthCheckName),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected void verifyBackendServiceDeleted(
      final String pBackendServiceName, final GCPRegionName pRegionName) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getBackendService(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pRegionName,
                    LOG,
                    pBackendServiceName),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected void verifyInternalForwardingRuleDeleted(
      final String pInternalForwardingRuleName, final GCPRegionName pRegionName) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getForwardingRule(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pRegionName,
                    LOG,
                    pInternalForwardingRuleName),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected void verifyServiceAttachmentDeleted(
      final String pServiceAttachmentName, final GCPRegionName pRegionName) {
    verifyResourceDeletedViaException(
        () ->
            getGCPApiSvc()
                .getServiceAttachment(
                    getContainer().getGcpOrganizationId(),
                    getContainer().getGcpProjectId().get(),
                    pRegionName,
                    LOG,
                    pServiceAttachmentName),
        Set.of(CommonErrorCode.NOT_FOUND));
  }

  protected Group getGroup() {
    return _groupSvc.findById(_group.getId());
  }

  protected GCPCloudProviderContainerSvc getGCPCloudProviderContainerSvc() {
    return _gcpCloudProviderContainerSvc;
  }

  @Override
  protected NDSGroup getNDSGroup() {
    return _ndsGroupDao.find(_group.getId()).get();
  }

  protected ObjectId getGroupId() {
    return _group.getId();
  }

  protected GCPCloudProviderContainer getContainer() {
    final NDSGroup group = _ndsGroupDao.find(_group.getId()).get();
    return (GCPCloudProviderContainer) group.getCloudProviderContainer(_containerId).get();
  }

  protected GCPOrganization getGCPOrganization(final ObjectId pOrganizationId) {
    return getGCPOrganizationDao()
        .find(pOrganizationId)
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format("Failed to find GCP Organization by id (%s)", pOrganizationId)));
  }

  protected List<ObjectId> getInstanceIds() {
    return _instanceIds;
  }

  protected ClusterDescription getClusterDescription() {
    return _clusterDescriptionDao
        .findByName(getNDSGroup().getGroupId(), _clusterDescription.getName())
        .get();
  }

  public ReplicaSetHardware getReplicaSetHardwareForInstance(
      final String pClusterName, final ObjectId pInstanceId) {
    return getHardwareDao()
        .findReplicaSetHardwareForInstance(getGroupId(), pClusterName, pInstanceId);
  }

  protected InstanceHardware getInstanceHardwareForPrimary() {
    final ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(
            getClusterDescription().getName(), getInstanceIds().get(0));
    final String primaryHostName =
        getHostSvc()
            .getPrimaryForReplSet(replicaSetHardware.getGroupId(), replicaSetHardware.getRsId())
            .getName();
    return replicaSetHardware.getHardware().stream()
        .filter(
            instanceHardware ->
                instanceHardware.getHostnames().stream()
                    .map(InstanceHostname::getHostname)
                    .collect(Collectors.toList())
                    .contains(primaryHostName))
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalStateException(
                    String.format(
                        "Could not find instance hardware for primary. Expected host name: %s,"
                            + " found instance hardware hostnames: %s.",
                        primaryHostName,
                        replicaSetHardware.getHardware().stream()
                            .map(InstanceHardware::getHostnames)
                            .flatMap(Collection::stream)
                            .map(InstanceHostname::getHostname)
                            .collect(Collectors.joining(", ")))));
  }

  protected ClusterDescriptionDao getClusterDescriptionDao() {
    return _clusterDescriptionDao;
  }

  protected GCPApiSvc getGCPApiSvc() {
    return _gcpApiSvc;
  }

  protected NDSPlanContext.Factory getNdsPlanContextFactory() {
    return _ndsPlanContextFactory;
  }

  protected NDSGroupDao getNDSGroupDao() {
    return _ndsGroupDao;
  }

  protected NDSLogDao getNDSLogDao() {
    return _ndsLogDao;
  }

  protected NDSGroupSvc getNDSGroupSvc() {
    return _ndsGroupSvc;
  }

  protected NDSClusterSvc getClusterSvc() {
    return _clusterSvc;
  }

  protected GCPCapacityDenylistSvc getGCPCapacityDenylistSvc() {
    return _gcpCapacityDenylistSvc;
  }

  protected NDSAdminJobDao getNDSAdminJobDao() {
    return _ndsAdminJobDao;
  }

  protected GCPCloudProviderContainerSvc getCloudProviderContainerSvc() {
    return _gcpCloudProviderContainerSvc;
  }

  protected GCPOrganizationDao getGCPOrganizationDao() {
    return _gcpOrganizationDao;
  }

  protected AppUser getUser() {
    return _user;
  }

  protected GCPPeerVpcDao getGCPPeerVpcDao() {
    return _gcpPeerVpcDao;
  }

  protected AutomationConfigPublishingSvc getAutomationConfigSvc() {
    return _automationConfigSvc;
  }

  protected CloudChefConfSvc getCloudChefConfSvc() {
    return _cloudChefConfSvc;
  }

  protected NDSRemoteImageSvc getRemoteImageSvc() {
    return _remoteImageSvc;
  }

  protected NDSOrphanedItemDao getOrphanedItemDao() {
    return _orphanedItemDao;
  }

  protected GCPProjectDao getGCPProjectDao() {
    return _gcpProjectDao;
  }

  protected GCPProjectCreationSvc getGCPProjectCreationSvc() {
    return _gcpProjectSvc;
  }

  protected NDSOrphanedItemSvc getNDSOrphanedItemSvc() {
    return _ndsOrphanedItemSvc;
  }

  protected GroupDao getGroupDao() {
    return _groupDao;
  }

  protected NDSAdminBackupSnapshotSvc getNDSAdminBackupSnapshotSvc() {
    return _ndsAdminBackupSnapshotSvc;
  }

  protected ReplicaSetHardwareDao getReplicaSetHardwareDao() {
    return _replicaSetHardwareDao;
  }

  protected FeatureFlagSvc getFeatureFlagSvc() {
    return _featureFlagSvc;
  }

  protected Logger getLogger() {
    return LOG;
  }

  protected Map<String, String> getTags(final ObjectId pInstanceId) {
    final RegionName regionName =
        getReplicaSetHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroupId(), getClusterDescription().getName(), pInstanceId)
            .getInstanceIdToRegionNameMap(
                getClusterDescription()
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs())
            .get(pInstanceId);
    return NDSMoveTags.builder()
        .setCloudProvider(CloudProvider.GCP)
        .setRegionName(regionName)
        .build();
  }

  public NDSACMESvc getNdsACMESvc() {
    return this._ndsACMESvc;
  }

  protected GCPCMEKSvc getGcpCmekSvc() {
    return _gcpCmekSvc;
  }
}
