load("//server/src/test:rules.bzl", "test_package")

# NDS GCP Integration Tests For Networking Related Resources
test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = [
        "external",
        "no-sandbox",
    ],
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/atm/res/api",
        "//server/src/main/com/xgen/svc/mms/res",
        "//server/src/main/com/xgen/svc/mms/res/admin",
        "//server/src/main/com/xgen/svc/mms/res/common",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/main/com/xgen/svc/nds/security/res",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/hostname",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/nds/gcp",
        "@maven//:com_amazonaws_aws_java_sdk_route53",
        "@maven//:com_google_apis_google_api_services_compute",
        "@maven//:junit_junit",
        "@maven//:org_mockito_mockito_core",
    ],
)
