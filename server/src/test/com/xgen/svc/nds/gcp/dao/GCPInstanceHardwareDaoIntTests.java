package com.xgen.svc.nds.gcp.dao;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.gcp._private.dao.GCPInstanceHardwareDao;
import com.xgen.cloud.nds.gcp._public.model.GCPDiskType;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware.FieldDefs;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class GCPInstanceHardwareDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private GCPInstanceHardwareDao _instanceHardwareDao;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private AppSettings _appSettings;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testUnsetProvisionedFields_ChangeProvider() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final Hostnames hostnames = new Hostnames("host0.mms.com");
    final String acmeProvider = "LETS_ENCRYPT_V2";

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(),
        GCPNDSInstanceSize.M30,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        "data0",
        "/dev/b",
        ndsGroup.getCloudProviderContainers().get(0).getId(),
        null,
        null,
        GCPDiskType.HYPERDISK_EXTREME,
        null,
        acmeProvider);

    _instanceHardwareDao.unsetProvisionedFields(
        replicaSetHardwareId, instanceId, false, CloudProvider.AWS);

    final ReplicaSetHardware hardwareAfter =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();

    final InstanceHardware hardware = hardwareAfter.getAllHardware().findFirst().orElseThrow();
    assertEquals(CloudProvider.AWS, hardware.getCloudProvider());
    assertFalse(hardware.isProvisioned());

    _instanceHardwareDao.unsetProvisionedFieldsChangeProvider(
        hardwareAfter.getId(), instanceId, false);

    final BasicDBObject replicaSetHardwareDoc =
        _replicaSetHardwareDao.findOne(
            new BasicDBObject(ReplicaSetHardware.FieldDefs.ID, replicaSetHardwareId));
    final BasicDBList cloudProviderHardware =
        (BasicDBList) replicaSetHardwareDoc.get("cloudProviderHardware");

    for (int i = 0; i < cloudProviderHardware.size(); i++) {
      final BasicDBObject hardwareDoc = (BasicDBObject) cloudProviderHardware.get(i);
      assertFalse(hardwareDoc.containsField(FieldDefs.INSTANCE_NAME));
      assertFalse(hardwareDoc.containsField(FieldDefs.ZONE_NAME));
      assertFalse(hardwareDoc.containsField(FieldDefs.DISK_NAME));
      assertFalse(hardwareDoc.containsField(FieldDefs.STATIC_IP_NAME));
      assertFalse(hardwareDoc.containsField(FieldDefs.LOAD_BALANCER_IP));
      assertFalse(hardwareDoc.containsField(FieldDefs.DATA_DEVICE_NAME));
      assertFalse(hardwareDoc.containsField(FieldDefs.DISK_TYPE));
      assertFalse(hardwareDoc.containsField(InstanceHardware.FieldDefs.ACME_PROVIDER));
    }
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testUnsetProvisionedFields() {
    final ObjectId groupId = new ObjectId();

    final String clusterName = "foo";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    final String replicaSetName = ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId, replicaSetName, true, false, new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);
    _replicaSetHardwareDao.setInstanceOSSwap(replicaSetHardwareId, instanceId, false, true);

    final Hostnames hostnames =
        new Hostnames(InstanceHostname.internal("internal"), InstanceHostname.public_("public"));
    final HostnameScheme hostnameScheme = HostnameScheme.INTERNAL;
    final String publicIP = "***********";
    final int diskSizeGB = 123;
    final GCPNDSInstanceSize instanceSize = GCPNDSInstanceSize.M10;
    final GCPInstanceFamily instanceFamily = GCPInstanceFamily.N1;
    final OS os = OS.AL2;
    final String instanceName = "instance-name";
    final String staticIpName = "static-ip-name";
    final GCPZoneName zoneName = GCPZoneName.ASIA_EAST1_A;
    final String diskName = "disk-name";
    final String dataDeviceName = "data-device-name";
    final ObjectId containerId = new ObjectId();
    final String loadBalancerIp = "load-balancer-ip";
    final String appliedOSPolicyVersion = "1234";
    final String acmeProvider = "LETS_ENCRYPT_V2";

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        hostnameScheme,
        publicIP,
        diskSizeGB,
        instanceSize,
        instanceFamily,
        os,
        instanceName,
        staticIpName,
        zoneName,
        diskName,
        dataDeviceName,
        containerId,
        loadBalancerIp,
        appliedOSPolicyVersion,
        GCPDiskType.HYPERDISK_BALANCED,
        null,
        acmeProvider);

    _instanceHardwareDao.setInstanceForceServerRestart(replicaSetHardwareId, instanceId, false);

    final ReplicaSetHardware replicaSetHardwareAfterProvision =
        _replicaSetHardwareDao.findByCluster(groupId, clusterName).get(0);
    final GCPInstanceHardware hardwareAfterProvision =
        (GCPInstanceHardware) replicaSetHardwareAfterProvision.getHardware().get(0);

    assertEquals(hostnames, hardwareAfterProvision.getHostnames());
    assertEquals(hostnameScheme, hardwareAfterProvision.getHostnameSchemeForAgents());
    assertEquals(Optional.of(publicIP), hardwareAfterProvision.getPublicIP());
    assertEquals(Optional.of(diskSizeGB), hardwareAfterProvision.getDiskSizeGB());
    assertEquals(Optional.of(instanceSize.name()), hardwareAfterProvision.getInstanceSize());
    assertEquals(Optional.of(instanceFamily.name()), hardwareAfterProvision.getInstanceFamily());
    assertEquals(Optional.of(os), hardwareAfterProvision.getOS());
    assertEquals(Optional.of(instanceName), hardwareAfterProvision.getGCPInstanceName());
    assertEquals(Optional.of(staticIpName), hardwareAfterProvision.getStaticIpName());
    assertEquals(Optional.of(zoneName), hardwareAfterProvision.getZoneName());
    assertEquals(Optional.of(diskName), hardwareAfterProvision.getDiskName());
    assertEquals(Optional.of(dataDeviceName), hardwareAfterProvision.getDataDeviceName());
    assertEquals(containerId, hardwareAfterProvision.getCloudContainerId());
    assertEquals(Optional.of(loadBalancerIp), hardwareAfterProvision.getLoadBalancerIp());
    assertTrue(hardwareAfterProvision.needsForceServerRestart());
    assertEquals(Optional.of(acmeProvider), hardwareAfterProvision.getACMEProvider());

    _instanceHardwareDao.unsetProvisionedFields(
        replicaSetHardwareId, instanceId, false, CloudProvider.GCP);

    final ReplicaSetHardware replicaSetHardwareAfterDeprovision =
        _replicaSetHardwareDao.findByCluster(groupId, clusterName).get(0);
    final GCPInstanceHardware hardwareAfterDeprovision =
        (GCPInstanceHardware) replicaSetHardwareAfterDeprovision.getHardware().get(0);

    assertEquals(new Hostnames(), hardwareAfterDeprovision.getHostnames());
    assertNull(hardwareAfterDeprovision.getHostnameSchemeForAgents());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getPublicIP());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getDiskSizeGB());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getInstanceSize());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getInstanceFamily());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getOS());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getGCPInstanceName());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getStaticIpName());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getZoneName());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getDiskName());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getDataDeviceName());
    assertNull(hardwareAfterDeprovision.getCloudContainerId());
    assertEquals(Optional.empty(), hardwareAfterDeprovision.getLoadBalancerIp());
    assertEquals(GCPDiskType.PD_SSD, hardwareAfterDeprovision.getGcpDiskType());
    assertNull(hardwareAfterDeprovision.toDBObject().get(FieldDefs.DISK_TYPE));
    assertFalse(hardwareAfterDeprovision.needsForceServerRestart());
    assertFalse(hardwareAfterDeprovision.getACMEProvider().isPresent());
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testSetFields() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final ReplicaSetHardware hardwareBefore =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceBefore =
        (GCPInstanceHardware) hardwareBefore.getHardware().get(0);
    assertFalse(instanceBefore.isProvisioned());
    assertEquals(InstanceHardware.Action.NONE, instanceBefore.getAction());
    assertFalse(instanceBefore.needsForceReplacement());
    assertFalse(instanceBefore.needsOSSwap());
    assertFalse(instanceBefore.isPaused());
    assertFalse(instanceBefore.getLastInstanceSizeModifyDate().isPresent());
    assertFalse(instanceBefore.getACMEProvider().isPresent());
    final Hostnames hostnames = new Hostnames("host0.mms.com");
    final String acmeProvider = "LETS_ENCRYPT_V2";

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(),
        GCPNDSInstanceSize.M30,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        "data0",
        "/dev/b",
        ndsGroup.getCloudProviderContainers().get(0).getId(),
        "***********",
        "1234",
        null,
        null,
        acmeProvider);

    final ReplicaSetHardware hardwareAfter =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceAfter =
        (GCPInstanceHardware) hardwareAfter.getHardware().get(0);

    assertEquals(
        ndsGroup.getCloudProviderContainers().get(0).getId(), instanceAfter.getCloudContainerId());
    assertEquals("host0.mms.com", instanceAfter.getHostnameForAgents().get());
    assertEquals("*******", instanceAfter.getPublicIP().get());
    assertEquals(
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(), instanceAfter.getDiskSizeGB().get(), 0.01);
    assertTrue(instanceAfter.isProvisioned());
    assertFalse(instanceAfter.needsForceReplacement());
    assertFalse(instanceAfter.needsForceServerRestart());
    assertFalse(instanceAfter.needsOSSwap());
    assertThat(instanceAfter.getLastInstanceSizeModifyDate().get(), greaterThan(now));
    assertEquals(GCPNDSInstanceSize.M30.name(), instanceAfter.getInstanceSize().get());
    assertEquals(GCPInstanceFamily.N2.name(), instanceAfter.getInstanceFamily().get());
    assertEquals("vm0", instanceAfter.getGCPInstanceName().get());
    assertEquals("ip0", instanceAfter.getStaticIpName().get());
    assertEquals(GCPZoneName.US_EAST1_B, instanceAfter.getZoneName().get());
    assertEquals("data0", instanceAfter.getDiskName().get());
    assertEquals("/dev/b", instanceAfter.getDataDeviceName().get());
    assertEquals("***********", instanceAfter.getLoadBalancerIp().get());
    assertEquals(GCPDiskType.PD_SSD, instanceAfter.getGcpDiskType());
    assertEquals(acmeProvider, instanceAfter.getACMEProvider().get());
    assertEquals(Optional.empty(), instanceAfter.getDiskIOPS());
  }

  @Test
  public void testUpdateAvailabilityZoneUsingATransaction() {
    final ObjectId groupId = new ObjectId();
    final ReplicaSetHardware.ReplicaSetHardwareIds rsIds =
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(groupId, "foo", "foo", List.of())
            .next();
    _replicaSetHardwareDao.create(rsIds.id(), rsIds.rsId(), true, false, new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(rsIds.id(), CloudProvider.GCP, false, 0);

    { // check pre-conditions
      final ReplicaSetHardware hardwareBefore =
          _replicaSetHardwareDao.findById(rsIds.id()).orElseThrow();
      final GCPInstanceHardware instanceBefore =
          (GCPInstanceHardware) hardwareBefore.getHardware().get(0);
      assertEquals(Optional.empty(), instanceBefore.getZoneName());
    }

    // perform update
    _instanceHardwareDao.updateAvailabilityZoneUsingATransaction(
        rsIds.id(), (rsh) -> GCPZoneName.US_EAST1_B, instanceId);

    { // check post-conditions
      final ReplicaSetHardware hardwareAfter =
          _replicaSetHardwareDao.findById(rsIds.id()).orElseThrow();
      final GCPInstanceHardware instanceAfter =
          (GCPInstanceHardware) hardwareAfter.getHardware().get(0);
      assertEquals(Optional.of(GCPZoneName.US_EAST1_B), instanceAfter.getZoneName());
    }
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testSetInstanceOSSwap() {
    final ObjectId groupId = new ObjectId();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    final String replicaSetName = ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId, replicaSetName, true, false, new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);
    _replicaSetHardwareDao.setInstanceOSSwap(replicaSetHardwareId, instanceId, false, true);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final ReplicaSetHardware hardwareBefore =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceBefore =
        (GCPInstanceHardware) hardwareBefore.getHardware().get(0);
    assertFalse(instanceBefore.isProvisioned());
    assertEquals(InstanceHardware.Action.NONE, instanceBefore.getAction());
    assertTrue(instanceBefore.needsOSSwap());
    final Hostnames hostnames = new Hostnames("hostname");

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(),
        GCPNDSInstanceSize.M30,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        "data0",
        "/dev/b",
        ndsGroup.getCloudProviderContainers().get(0).getId(),
        "***********",
        "1234",
        null,
        null,
        null);

    final ReplicaSetHardware hardwareAfter =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceAfter =
        (GCPInstanceHardware) hardwareAfter.getHardware().get(0);
    assertFalse(instanceAfter.needsOSSwap());
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testSetAppliedOSPolicyVersion() {
    final ObjectId groupId = new ObjectId();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    final String replicaSetName = ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId, replicaSetName, true, false, new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final ReplicaSetHardware hardwareBefore =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceBefore =
        (GCPInstanceHardware) hardwareBefore.getHardware().get(0);
    assertFalse(instanceBefore.isProvisioned());
    assertEquals(InstanceHardware.Action.NONE, instanceBefore.getAction());

    assertTrue(instanceBefore.getAppliedOSPolicyVersion().isEmpty());

    final Hostnames hostnames = new Hostnames("hostname");

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(),
        GCPNDSInstanceSize.M30,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        "data0",
        "/dev/b",
        ndsGroup.getCloudProviderContainers().get(0).getId(),
        "***********",
        "1234",
        null,
        null,
        null);
    final String appliedOSPolicyVersion = "4";

    _instanceHardwareDao.setAppliedOSPolicyVersion(
        replicaSetHardwareId, instanceId, false, appliedOSPolicyVersion);

    final ReplicaSetHardware hardwareAfter =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceAfter =
        (GCPInstanceHardware) hardwareAfter.getHardware().get(0);
    assertTrue(instanceAfter.getAppliedOSPolicyVersion().isPresent());
    assertTrue(instanceAfter.getAppliedOSPolicyVersion().get().equals(appliedOSPolicyVersion));
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testSetLoadBalancerIp() {
    final ObjectId groupId = new ObjectId();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final ReplicaSetHardware hardwareBefore =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceBefore =
        (GCPInstanceHardware) hardwareBefore.getHardware().get(0);
    assertTrue(instanceBefore.getLoadBalancerIp().isEmpty());
    assertTrue(instanceBefore.getConfigNeedsUpdateAfter().isEmpty());

    _instanceHardwareDao.setLoadBalancerIp(replicaSetHardwareId, instanceId, "*******", false);

    final ReplicaSetHardware lbIpUpdatedNoConfigUpdate =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceAfter =
        (GCPInstanceHardware) lbIpUpdatedNoConfigUpdate.getHardware().get(0);

    assertEquals("*******", instanceAfter.getLoadBalancerIp().orElse(null));
    assertTrue(instanceAfter.getConfigNeedsUpdateAfter().isEmpty());

    _instanceHardwareDao.setLoadBalancerIp(replicaSetHardwareId, instanceId, "*******", true);

    final ReplicaSetHardware lbIpUpdatedConfigUpdate =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceConfigUpdated =
        (GCPInstanceHardware) lbIpUpdatedConfigUpdate.getHardware().get(0);

    assertEquals("*******", instanceConfigUpdated.getLoadBalancerIp().orElse(null));
    assertTrue(
        instanceConfigUpdated
            .getConfigNeedsUpdateAfter()
            .map(date -> date.before(new Date()))
            .orElse(false));
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testUpdateInstanceSize() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final ReplicaSetHardware hardwareBefore =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceBefore =
        (GCPInstanceHardware) hardwareBefore.getHardware().get(0);
    assertFalse(instanceBefore.isProvisioned());
    assertFalse(instanceBefore.getLastInstanceSizeModifyDate().isPresent());
    final Hostnames hostnames = new Hostnames("host0.mms.com");

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(),
        GCPNDSInstanceSize.M30,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        "data0",
        "/dev/b",
        ndsGroup.getCloudProviderContainers().get(0).getId(),
        null,
        "1234",
        null,
        null,
        null);

    final ReplicaSetHardware hardwareAfterProvision =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceAfterProvision =
        (GCPInstanceHardware) hardwareAfterProvision.getHardware().get(0);

    assertTrue(instanceAfterProvision.isProvisioned());
    assertThat(instanceAfterProvision.getLastInstanceSizeModifyDate().get(), greaterThan(now));

    final Date provisionedNow = instanceAfterProvision.getLastInstanceSizeModifyDate().get();

    _instanceHardwareDao.updateInstanceSize(
        replicaSetHardwareId,
        instanceId,
        false,
        GCPNDSInstanceSize.M40.name(),
        GCPInstanceFamily.N2.getName());

    final ReplicaSetHardware hardwareAfterInstanceSizeUpdate =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final GCPInstanceHardware instanceAfterInstanceSizeUpdate =
        (GCPInstanceHardware) hardwareAfterInstanceSizeUpdate.getHardware().get(0);

    assertTrue(instanceAfterInstanceSizeUpdate.isProvisioned());
    assertEquals(
        GCPNDSInstanceSize.M40.name(), instanceAfterInstanceSizeUpdate.getInstanceSize().get());
    assertEquals(
        GCPInstanceFamily.N2.name(), instanceAfterInstanceSizeUpdate.getInstanceFamily().get());
    assertThat(
        instanceAfterInstanceSizeUpdate.getLastInstanceSizeModifyDate().get(),
        greaterThan(provisionedNow));
  }

  @Test
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testGetActionCounts() {
    final ObjectId groupId0 = new ObjectId();
    final String clusterName = "foo";
    final BasicDBObject fooReplicaSetHardware0Id =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId0, 0);
    _replicaSetHardwareDao.create(
        fooReplicaSetHardware0Id,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId0 =
        _replicaSetHardwareDao.addInstance(fooReplicaSetHardware0Id, CloudProvider.GCP, false, 0);
    final ObjectId instanceId1 =
        _replicaSetHardwareDao.addInstance(fooReplicaSetHardware0Id, CloudProvider.GCP, false, 1);

    final Hostnames hostnames0 = new Hostnames("host0.mms.com");
    final Hostnames hostnames1 = new Hostnames("host1.mms.com");

    _instanceHardwareDao.setProvisionedFields(
        fooReplicaSetHardware0Id,
        instanceId0,
        false,
        hostnames0,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSDefaults.INSTANCE_SIZE.getDefaultDiskSizeGB(),
        GCPNDSDefaults.INSTANCE_SIZE,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        "data0",
        "/dev/b",
        oid(0),
        null,
        "1234",
        null,
        null,
        null);
    _replicaSetHardwareDao.setAction(
        fooReplicaSetHardware0Id, instanceId0, false, InstanceHardware.Action.NONE);

    _instanceHardwareDao.setProvisionedFields(
        fooReplicaSetHardware0Id,
        instanceId1,
        false,
        hostnames1,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSDefaults.INSTANCE_SIZE.getDefaultDiskSizeGB(),
        GCPNDSDefaults.INSTANCE_SIZE,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm1",
        "ip1",
        GCPZoneName.US_EAST1_C,
        "data1",
        "/dev/b",
        oid(0),
        null,
        "1234",
        null,
        null,
        null);
    _replicaSetHardwareDao.setAction(
        fooReplicaSetHardware0Id, instanceId1, false, InstanceHardware.Action.HEAL_REPAIR);

    final ObjectId groupId1 = new ObjectId();

    final BasicDBObject fooReplicaSetHardware1Id =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId1, 1);
    _replicaSetHardwareDao.create(
        fooReplicaSetHardware1Id,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 1),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId3 =
        _replicaSetHardwareDao.addInstance(fooReplicaSetHardware1Id, CloudProvider.GCP, false, 0);
    final ObjectId instanceId4 =
        _replicaSetHardwareDao.addInstance(fooReplicaSetHardware1Id, CloudProvider.GCP, false, 1);

    final Hostnames hostnames3 = new Hostnames("host3.mms.com");
    final Hostnames hostnames4 = new Hostnames("host4.mms.com");

    _instanceHardwareDao.setProvisionedFields(
        fooReplicaSetHardware1Id,
        instanceId3,
        false,
        hostnames3,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSDefaults.INSTANCE_SIZE.getDefaultDiskSizeGB(),
        GCPNDSDefaults.INSTANCE_SIZE,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm2",
        "ip2",
        GCPZoneName.US_EAST1_D,
        "data2",
        "/dev/b",
        oid(0),
        null,
        "1234",
        null,
        null,
        null);
    _replicaSetHardwareDao.setAction(
        fooReplicaSetHardware1Id, instanceId3, false, InstanceHardware.Action.HEAL_RESYNC);

    _instanceHardwareDao.setProvisionedFields(
        fooReplicaSetHardware1Id,
        instanceId4,
        false,
        hostnames4,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSDefaults.INSTANCE_SIZE.getDefaultDiskSizeGB(),
        GCPNDSDefaults.INSTANCE_SIZE,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm3",
        "ip3",
        GCPZoneName.US_EAST4_A,
        "data3",
        "/dev/b",
        oid(0),
        null,
        "1234",
        null,
        null,
        null);
    _replicaSetHardwareDao.setAction(
        fooReplicaSetHardware1Id, instanceId4, false, InstanceHardware.Action.NONE);

    final ObjectId groupId2 = new ObjectId();

    final BasicDBObject intReplicaSetHardware0Id =
        ReplicaSetHardware.createNonConfigReplicaSetId("int", groupId2, 0);
    _replicaSetHardwareDao.create(
        intReplicaSetHardware0Id,
        ReplicaSetHardware.getNonConfigReplicaSetName("int", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId5 =
        _replicaSetHardwareDao.addInstance(intReplicaSetHardware0Id, CloudProvider.GCP, false, 0);
    final ObjectId instanceId6 =
        _replicaSetHardwareDao.addInstance(intReplicaSetHardware0Id, CloudProvider.GCP, true, 1);

    final Hostnames hostnames5 = new Hostnames("host5.mms.com");
    final Hostnames hostnames6 = new Hostnames("host6.mms.com");

    _instanceHardwareDao.setProvisionedFields(
        intReplicaSetHardware0Id,
        instanceId5,
        false,
        hostnames5,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSDefaults.INSTANCE_SIZE.getDefaultDiskSizeGB(),
        GCPNDSDefaults.INSTANCE_SIZE,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm4",
        "ip4",
        GCPZoneName.US_EAST4_B,
        "data4",
        "/dev/b",
        oid(0),
        null,
        "1234",
        null,
        null,
        null);
    _replicaSetHardwareDao.setAction(
        intReplicaSetHardware0Id, instanceId5, false, InstanceHardware.Action.HEAL_CANNOT_REPAIR);

    _instanceHardwareDao.setProvisionedFields(
        intReplicaSetHardware0Id,
        instanceId6,
        true,
        hostnames6,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSDefaults.INSTANCE_SIZE.getDefaultDiskSizeGB(),
        GCPNDSDefaults.INSTANCE_SIZE,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm5",
        "ip5",
        GCPZoneName.US_EAST4_C,
        "data5",
        "/dev/b",
        oid(0),
        null,
        "1234",
        null,
        null,
        null);
    _replicaSetHardwareDao.setAction(
        intReplicaSetHardware0Id, instanceId6, true, InstanceHardware.Action.HEAL_POWER_CYCLE);

    Map<Action, Integer> counts =
        _instanceHardwareDao.getActionCounts(GCPZoneName.US_EAST1_B.name());
    assertEquals(1, counts.size());
    assertTrue(counts.containsKey(InstanceHardware.Action.NONE));
    assertEquals(1, counts.get(InstanceHardware.Action.NONE).intValue());

    counts = _instanceHardwareDao.getActionCounts(GCPZoneName.US_EAST1_C.name());
    assertEquals(1, counts.size());
    assertTrue(counts.containsKey(InstanceHardware.Action.HEAL_REPAIR));
    assertEquals(1, counts.get(InstanceHardware.Action.HEAL_REPAIR).intValue());

    counts = _instanceHardwareDao.getActionCounts(GCPZoneName.US_EAST1_D.name());
    assertEquals(1, counts.size());
    assertTrue(counts.containsKey(InstanceHardware.Action.HEAL_RESYNC));
    assertEquals(1, counts.get(InstanceHardware.Action.HEAL_RESYNC).intValue());

    counts = _instanceHardwareDao.getActionCounts(GCPZoneName.US_EAST4_A.name());
    assertEquals(1, counts.size());
    assertTrue(counts.containsKey(InstanceHardware.Action.NONE));
    assertEquals(1, counts.get(InstanceHardware.Action.NONE).intValue());

    counts = _instanceHardwareDao.getActionCounts(GCPZoneName.US_EAST4_B.name());
    assertEquals(1, counts.size());
    assertTrue(counts.containsKey(InstanceHardware.Action.HEAL_CANNOT_REPAIR));
    assertEquals(1, counts.get(InstanceHardware.Action.HEAL_CANNOT_REPAIR).intValue());

    counts = _instanceHardwareDao.getActionCounts(GCPZoneName.US_EAST4_C.name());
    assertEquals(1, counts.size());
    assertTrue(counts.containsKey(InstanceHardware.Action.HEAL_POWER_CYCLE));
    assertEquals(1, counts.get(InstanceHardware.Action.HEAL_POWER_CYCLE).intValue());

    counts = _instanceHardwareDao.getActionCounts(GCPZoneName.EUROPE_WEST1_B.name());
    assertEquals(0, counts.size());
  }

  private static Stream<Arguments> testCreateOrReplaceInstanceDisk_withHyperdisk_params() {
    return Stream.of(
        Arguments.of(GCPDiskType.HYPERDISK_BALANCED, 10_000),
        Arguments.of(GCPDiskType.HYPERDISK_EXTREME, null));
  }

  @ParameterizedTest
  @MethodSource("testCreateOrReplaceInstanceDisk_withHyperdisk_params")
  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testCreateOrReplaceInstanceDisk_withHyperdisk(
      final GCPDiskType pGCPDiskType, @Nullable final Integer pDiskIops) {
    // boilerplate setup
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.GCP, false, 0);

    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    final Hostnames hostnames = new Hostnames("host0.mms.com");
    final var diskName = "data0";

    _instanceHardwareDao.setProvisionedFields(
        replicaSetHardwareId,
        instanceId,
        false,
        hostnames,
        InstanceHostname.HostnameScheme.LEGACY,
        "*******",
        GCPNDSInstanceSize.M30.getDefaultDiskSizeGB(),
        GCPNDSInstanceSize.M30,
        GCPInstanceFamily.N2,
        CloudProviderRegistry.getByCloudProvider(CloudProvider.GCP)
            .getInstanceHardwareProvider()
            .getDefaultOs(_appSettings, GCPInstanceFamily.N2),
        "vm0",
        "ip0",
        GCPZoneName.US_EAST1_B,
        diskName,
        "/dev/b",
        ndsGroup.getCloudProviderContainers().get(0).getId(),
        null,
        null,
        pGCPDiskType,
        pDiskIops,
        null);

    final Supplier<GCPInstanceHardware> getHw =
        () -> {
          final ReplicaSetHardware rsHw =
              _replicaSetHardwareDao.findByCluster(groupId, "foo").get(0);
          return (GCPInstanceHardware) rsHw.getHardware().get(0);
        };

    GCPInstanceHardware instanceHw = getHw.get();
    assertEquals(pGCPDiskType, instanceHw.getGcpDiskType());
    assertEquals(pDiskIops, instanceHw.getDiskIOPS().orElse(null));

    final Date nowish = new Date();
    _instanceHardwareDao.updateInstanceDisk(
        replicaSetHardwareId,
        instanceId,
        false,
        diskName,
        300,
        "/dev/c",
        pGCPDiskType,
        pDiskIops,
        nowish);

    instanceHw = getHw.get();
    assertEquals(pGCPDiskType, instanceHw.getGcpDiskType());
    assertEquals(pDiskIops, instanceHw.getDiskIOPS().orElse(null));
    assertEquals(nowish, instanceHw.getLastDiskModifyDate().orElseThrow());

    // replace hyperdisk - eg blocked modification
    _instanceHardwareDao.createOrReplaceInstanceDisk(
        replicaSetHardwareId, instanceId, false, diskName, 400, "/dev/c", pGCPDiskType, pDiskIops);
    instanceHw = getHw.get();
    assertEquals(pGCPDiskType, instanceHw.getGcpDiskType());
    assertEquals(pDiskIops, instanceHw.getDiskIOPS().orElse(null));
    assertEquals(Optional.empty(), instanceHw.getLastDiskModifyDate());

    // update disk in place
    _instanceHardwareDao.updateInstanceDisk(
        replicaSetHardwareId,
        instanceId,
        false,
        diskName,
        401,
        "/dev/c",
        pGCPDiskType,
        pDiskIops,
        nowish);
    instanceHw = getHw.get();
    assertEquals(pGCPDiskType, instanceHw.getGcpDiskType());
    assertEquals(pDiskIops, instanceHw.getDiskIOPS().orElse(null));
    assertEquals(nowish, instanceHw.getLastDiskModifyDate().orElseThrow());

    // change disk type - implicit SSD
    _instanceHardwareDao.createOrReplaceInstanceDisk(
        replicaSetHardwareId, instanceId, false, diskName, 200, "/dev/c", null, null);
    instanceHw = getHw.get();
    assertEquals(GCPDiskType.PD_SSD, instanceHw.getGcpDiskType());
    assertTrue(instanceHw.getDiskIOPS().isEmpty());
    assertEquals(Optional.empty(), instanceHw.getLastDiskModifyDate());

    // Back to hyperdisk
    _instanceHardwareDao.createOrReplaceInstanceDisk(
        replicaSetHardwareId, instanceId, false, diskName, 300, "/dev/c", pGCPDiskType, pDiskIops);

    instanceHw = getHw.get();
    assertEquals(pGCPDiskType, instanceHw.getGcpDiskType());
    assertEquals(pDiskIops, instanceHw.getDiskIOPS().orElse(null));

    // change disk type - explicit SSD
    _instanceHardwareDao.createOrReplaceInstanceDisk(
        replicaSetHardwareId, instanceId, false, diskName, 200, "/dev/c", GCPDiskType.PD_SSD, null);
    assertEquals(GCPDiskType.PD_SSD, getHw.get().getGcpDiskType());
    // implementation detail - we null out this field for consistency and avoiding any migration
    assertNull(getHw.get().toDBObject().get(GCPInstanceHardware.FieldDefs.DISK_TYPE));
    assertFalse(getHw.get().toDBObject().containsKey(GCPInstanceHardware.FieldDefs.DISK_TYPE));
    assertFalse(getHw.get().toDBObject().containsKey(GCPInstanceHardware.FieldDefs.DISK_IOPS));

    // Back to hyperdisk - test overwriting in provision fcn
    final BiConsumer<GCPDiskType, Integer> applySetProvisionedFields =
        (final GCPDiskType pType, @Nullable final Integer pIops) -> {
          final ObjectId containerId = new ObjectId();
          _instanceHardwareDao.setProvisionedFields(
              replicaSetHardwareId,
              instanceId,
              false,
              new Hostnames("abc"),
              HostnameScheme.INTERNAL,
              "*******",
              123,
              GCPNDSInstanceSize.M250,
              GCPInstanceFamily.N2,
              OS.AL2,
              "intanceName",
              "staticip",
              GCPZoneName.US_EAST1_B,
              "diskNameHere",
              "dataDeviceName",
              containerId,
              null,
              null,
              pType,
              pIops,
              null);
        };
    applySetProvisionedFields.accept(pGCPDiskType, pDiskIops);
    instanceHw = getHw.get();
    assertEquals(pGCPDiskType, instanceHw.getGcpDiskType());
    assertEquals(pDiskIops, instanceHw.getDiskIOPS().orElse(null));

    // and back to SSD
    applySetProvisionedFields.accept(GCPDiskType.PD_SSD, null);
    instanceHw = getHw.get();
    assertEquals(GCPDiskType.PD_SSD, instanceHw.getGcpDiskType());
    // implementation detail - we null out this field for consistency and avoiding any migration
    assertNull(instanceHw.toDBObject().get(GCPInstanceHardware.FieldDefs.DISK_TYPE));
    assertFalse(instanceHw.toDBObject().containsKey(GCPInstanceHardware.FieldDefs.DISK_TYPE));
    assertNull(instanceHw.toDBObject().get(GCPInstanceHardware.FieldDefs.DISK_IOPS));
    assertFalse(instanceHw.toDBObject().containsKey(GCPInstanceHardware.FieldDefs.DISK_IOPS));
  }
}
