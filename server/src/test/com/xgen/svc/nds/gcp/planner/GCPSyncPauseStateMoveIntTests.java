package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;

import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import java.time.Duration;
import java.util.Collections;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.Test;

public class GCPSyncPauseStateMoveIntTests extends GCPExternalIntTest {

  private void testGCPSyncPauseStateInternal(final ObjectId pInstanceId, final boolean pIsPaused) {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

    final GCPSyncPauseStateMove syncPauseStateMove =
        GCPSyncPauseStateMove.factoryCreate(
            plan.getPlanContext(), getClusterDescription().getName(), pInstanceId);

    plan.addMove(syncPauseStateMove);
    getPlanDao().save(plan);

    final GCPInstanceHardware originalHardware =
        (GCPInstanceHardware)
            getInstanceHardware(getGroup().getId(), getClusterDescription().getName(), pInstanceId);

    assertEquals(originalHardware.getAction(), syncPauseStateMove.getMoveTriggeringAction());

    waitForMovePerformDone(syncPauseStateMove);

    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), pInstanceId);
    final InstanceHardware instanceHardware = getInstanceHardware(replicaSetHardware, pInstanceId);

    assertEquals(pIsPaused, instanceHardware.isPaused());
  }

  @Test(timeout = 25 * 60 * 1000L)
  public void testGCPSyncPauseStateMove() throws Exception {

    // In order to properly pause/resume machine, we need a automation config to have been
    // published.
    // However, we don't need to launch 3 nodes in order to run this test successfully.
    final ReplicationSpec existingSpec =
        getClusterDescription().getReplicationSpecsWithShardData().get(0);

    getClusterDescriptionDao()
        .save(
            getClusterDescription()
                .copy()
                .setReplicationSpecList(getReplicationSpecs(existingSpec.getId(), 1, 1))
                .build());

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

    final ObjectId instanceId = getInstanceIds().get(0);
    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    final Move configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);
    final Move provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            getTags(instanceId),
            getClusterDescription().getName(),
            instanceId,
            false);

    plan.addMove(provisionContainerMove);
    plan.addMove(configMove);
    plan.addMove(provisionMachineMove);
    provisionMachineMove.addPredecessor(provisionContainerMove);
    configMove.addPredecessor(provisionMachineMove);

    getPlanDao().save(plan);

    try {
      // Wait for the cluster creation
      waitForPlanPerformSuccess(plan);

      // Wait for agent ping so that we have a primary
      Thread.sleep(Duration.ofMinutes(1).toMillis());

      // Save 'true' to the isPaused property
      getClusterDescriptionDao().save(getClusterDescription().copy().setIsPaused(true).build());

      // Test the pausing of an instance
      testGCPSyncPauseStateInternal(instanceId, true);

      // Save 'false' to the isPaused property
      getClusterDescriptionDao().save(getClusterDescription().copy().setIsPaused(false).build());

      // Test the resuming of an instance
      testGCPSyncPauseStateInternal(instanceId, false);
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }
}
