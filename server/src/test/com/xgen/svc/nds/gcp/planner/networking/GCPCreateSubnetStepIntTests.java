package com.xgen.svc.nds.gcp.planner.networking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.google.api.services.compute.model.Operation;
import com.google.api.services.compute.model.Subnetwork;
import com.mongodb.BasicDBList;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.ComplianceLevel;
import com.xgen.cloud.nds.gcp._public.model.GCPAtlasSubnet;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem;
import com.xgen.cloud.nds.gcp._public.model.GCPPeerVpc;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc.OperationStatus;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.GCPReserveProjectStep;
import com.xgen.svc.nds.gcp.planner.GCPStep;
import com.xgen.svc.nds.gcp.svc.GCPUriUtil;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.Test;

public class GCPCreateSubnetStepIntTests extends GCPExternalIntTest {
  private int _stepIndex = 1;

  private List<Step<?>> setupProject(final Plan pPlan) {
    final GCPReserveProjectStep gcpCreateProjectStep =
        new GCPReserveProjectStep(
            ((NDSPlanContext) pPlan.getPlanContext()),
            new State(pPlan.getId(), pPlan.getMoves().get(0).getId(), _stepIndex++, getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            getGCPProjectDao(),
            getGCPProjectCreationSvc(),
            getOrphanedItemDao(),
            getNDSOrphanedItemSvc(),
            getGroupDao());
    try {
      waitForStepPerformDone(gcpCreateProjectStep);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForStepRollbackDone(gcpCreateProjectStep));
      throw t;
    }
    final String projectId = gcpCreateProjectStep.perform().getData().getProjectId();

    // Need a network first
    final GCPCreateNetworkStep createNetworkStep =
        new GCPCreateNetworkStep(
            ((NDSPlanContext) pPlan.getPlanContext()),
            new State(pPlan.getId(), pPlan.getMoves().get(0).getId(), _stepIndex++, getPlanDao()),
            getContainer(),
            projectId,
            getGCPApiSvc(),
            getNDSOrphanedItemSvc());

    try {
      waitForStepPerformDone(createNetworkStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createNetworkStep);
      waitForStepRollbackDone(gcpCreateProjectStep);
      throw t;
    }

    getNDSGroupDao()
        .setCloudContainerFields(
            getGroupId(),
            getContainer().getId(),
            List.of(
                Pair.of(GCPCloudProviderContainer.FieldDefs.PROJECT_ID, projectId),
                Pair.of(
                    GCPCloudProviderContainer.FieldDefs.NETWORK_NAME,
                    createNetworkStep.perform().getData().getNetworkName())));
    return List.of(createNetworkStep, gcpCreateProjectStep);
  }

  private void checkCloudProviderSubnets(
      final Map<GCPRegionName, Integer> pExpectedSubnetsPerRegion) {
    final GCPCloudProviderContainer container = getContainer();
    List<Subnetwork> subnetworks =
        getGCPApiSvc()
            .listSubnets(
                container.getGcpOrganizationId(), container.getGcpProjectId().get(), getLogger())
            .stream()
            .filter(
                subnetwork ->
                    NetUtils.areOverlappingIpv4CidrBlocks(
                        subnetwork.getIpCidrRange(), getContainer().getAtlasCidr()))
            .collect(Collectors.toList());
    for (final Map.Entry<GCPRegionName, Integer> entry : pExpectedSubnetsPerRegion.entrySet()) {
      assertEquals(
          (int) entry.getValue(),
          subnetworks.stream()
              .filter(
                  subnetwork ->
                      entry
                          .getKey()
                          .equals(
                              GCPUriUtil.findRegionForSubnet(subnetwork, ComplianceLevel.COMMERCIAL)
                                  .orElseThrow()))
              .count());
    }
  }

  private void orphanSubnets(final List<GCPAtlasSubnet> pSubnets) {
    for (final GCPAtlasSubnet subnet : pSubnets) {
      getNDSOrphanedItemSvc()
          .orphanResourceAndIgnoreDuplicate(
              new GCPOrphanedItem.Builder()
                  .setGcpOrganizationId(getContainer().getGcpOrganizationId())
                  .setProjectName(getContainer().getGcpProjectId().get())
                  .setDurationUntilExpire(GCPOrphanedItem.Expirations.SUBNET)
                  .setId(subnet.getSubnetName())
                  .setType(GCPOrphanedItem.Type.SUBNET)
                  .setRegionName(subnet.getRegion())
                  .build(),
              getLogger());
    }
    assertEquals(pSubnets.size(), getNDSOrphanedItemSvc().findAllOrphanedItems().size());
  }

  private GCPCreateSubnetStep getCreateSubnetStep(
      final Plan pPlan, final List<GCPRegionName> regions, final int pStepIndex) {
    final GCPCloudProviderContainer container = getContainer();
    return new GCPCreateSubnetStep(
        (NDSPlanContext) pPlan.getPlanContext(),
        new Step.State(pPlan.getId(), pPlan.getMoves().get(0).getId(), pStepIndex, getPlanDao()),
        container,
        GCPOrganization.builder()
            .id(container.getGcpOrganizationId())
            .complianceLevel(ComplianceLevel.COMMERCIAL)
            .build(),
        container.getGcpProjectId().get(),
        container.getNetworkName().get(),
        regions,
        getGCPApiSvc(),
        getNDSOrphanedItemSvc());
  }

  private void testGCPCreateSubnetStepInternal(final Plan pPlan) throws SvcException {
    final List<GCPRegionName> regions = List.of(GCPRegionName.EASTERN_US, GCPRegionName.CENTRAL_US);

    // start with 0 subnets
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 0, GCPRegionName.CENTRAL_US, 0));

    final GCPCreateSubnetStep createSubnetsStep1 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info(("create new subnets in fresh GCP project"));
      waitForStepPerformDone(createSubnetsStep1);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 1, GCPRegionName.CENTRAL_US, 1));
    } catch (final Throwable t) {
      waitForStepRollbackDone(createSubnetsStep1);
      throw t;
    }

    final List<GCPAtlasSubnet> leakedSubnets = getContainer().getSubnets();
    getLogger()
        .info(
            "Leak subnets: {}",
            leakedSubnets.stream().map(GCPAtlasSubnet::getSubnetName).collect(Collectors.toList()));
    getGCPCloudProviderContainerSvc()
        .updateSubnets(getGroupId(), getContainer().getId(), Collections.emptyList());

    final GCPCreateSubnetStep createSubnetsStep2 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info("Create new subnets over leaked ones");
      waitForStepPerformDone(createSubnetsStep2);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 2, GCPRegionName.CENTRAL_US, 2));
    } catch (final Throwable t) {
      waitForStepRollbackDone(createSubnetsStep2);
      getGCPCloudProviderContainerSvc()
          .updateSubnets(getGroupId(), getContainer().getId(), leakedSubnets);
      waitForStepRollbackDone(createSubnetsStep1);
      throw t;
    }

    getLogger().info("Rollback and delete these new subnets");
    waitForStepRollbackDone(createSubnetsStep2);
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 1, GCPRegionName.CENTRAL_US, 1));

    // test if orphaned subnets will be reused
    final GCPCreateSubnetStep createSubnetsStep3 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info("Add the leaked subnets as orphaned items to be resurrected");
      orphanSubnets(leakedSubnets);
      getLogger().info("Resurrect these subnets in a new CreateSubnetStep");
      waitForStepPerformDone(createSubnetsStep3);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 1, GCPRegionName.CENTRAL_US, 1));
      assertEquals(0, getNDSOrphanedItemSvc().findAllOrphanedItems().size());
    } catch (final Throwable t) {
      getGCPCloudProviderContainerSvc()
          .updateSubnets(getGroupId(), getContainer().getId(), leakedSubnets);
      waitForStepRollbackDone(createSubnetsStep1);
    }

    getLogger().info("Rollback and delete the resurrected subnets");
    waitForStepRollbackDone(createSubnetsStep3);
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 0, GCPRegionName.CENTRAL_US, 0));

    getLogger().info("Re-add the leaked subnets as orphaned items without backing subnets");
    orphanSubnets(leakedSubnets);
    final GCPCreateSubnetStep createSubnetsStep4 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info("Re-create these subnets with the same CIDR and name");
      waitForStepPerformDone(createSubnetsStep4);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 1, GCPRegionName.CENTRAL_US, 1));
      assertEquals(0, getNDSOrphanedItemSvc().findAllOrphanedItems().size());
      // ensure that orphaned subnets are re-created with the same name
      for (final GCPAtlasSubnet subnet : getContainer().getSubnets()) {
        assertTrue(
            leakedSubnets.stream()
                .anyMatch(
                    leakedSubnet -> leakedSubnet.getSubnetName().equals(subnet.getSubnetName())));
      }
    } catch (final Throwable t) {
      waitForStepRollbackDone(createSubnetsStep4);
    }
    getLogger().info("Rollback and delete subnets");
    waitForStepRollbackDone(createSubnetsStep4);
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 0, GCPRegionName.CENTRAL_US, 0));
  }

  private void testGCPCreateSubnetStepInternal_gov(final Plan pPlan) throws SvcException {
    final List<GCPRegionName> regions = List.of(GCPRegionName.EASTERN_US_AW);

    // start with 0 subnets
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 0));

    final GCPCreateSubnetStep createSubnetsStep1 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info(("create new subnets in fresh GCP project"));
      waitForStepPerformDone(createSubnetsStep1);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 1));
    } catch (final Throwable t) {
      waitForStepRollbackDone(createSubnetsStep1);
      throw t;
    }

    final List<GCPAtlasSubnet> leakedSubnets = getContainer().getSubnets();
    getLogger()
        .info(
            "Leak subnets: {}",
            leakedSubnets.stream().map(GCPAtlasSubnet::getSubnetName).collect(Collectors.toList()));
    getGCPCloudProviderContainerSvc()
        .updateSubnets(getGroupId(), getContainer().getId(), Collections.emptyList());

    final GCPCreateSubnetStep createSubnetsStep2 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info("Create new subnets over leaked ones");
      waitForStepPerformDone(createSubnetsStep2);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 2));
    } catch (final Throwable t) {
      waitForStepRollbackDone(createSubnetsStep2);
      getGCPCloudProviderContainerSvc()
          .updateSubnets(getGroupId(), getContainer().getId(), leakedSubnets);
      waitForStepRollbackDone(createSubnetsStep1);
      throw t;
    }

    getLogger().info("Rollback and delete these new subnets");
    waitForStepRollbackDone(createSubnetsStep2);
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 1));

    // test if orphaned subnets will be reused
    final GCPCreateSubnetStep createSubnetsStep3 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info("Add the leaked subnets as orphaned items to be resurrected");
      orphanSubnets(leakedSubnets);
      getLogger().info("Resurrect these subnets in a new CreateSubnetStep");
      waitForStepPerformDone(createSubnetsStep3);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 1));
      assertEquals(0, getNDSOrphanedItemSvc().findAllOrphanedItems().size());
    } catch (final Throwable t) {
      getGCPCloudProviderContainerSvc()
          .updateSubnets(getGroupId(), getContainer().getId(), leakedSubnets);
      waitForStepRollbackDone(createSubnetsStep1);
    }

    getLogger().info("Rollback and delete the resurrected subnets");
    waitForStepRollbackDone(createSubnetsStep3);
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 0));

    getLogger().info("Re-add the leaked subnets as orphaned items without backing subnets");
    orphanSubnets(leakedSubnets);
    final GCPCreateSubnetStep createSubnetsStep4 =
        getCreateSubnetStep(pPlan, regions, _stepIndex++);
    try {
      getLogger().info("Re-create these subnets with the same CIDR and name");
      waitForStepPerformDone(createSubnetsStep4);
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 1));
      assertEquals(0, getNDSOrphanedItemSvc().findAllOrphanedItems().size());
      // ensure that orphaned subnets are re-created with the same name
      for (final GCPAtlasSubnet subnet : getContainer().getSubnets()) {
        assertTrue(
            leakedSubnets.stream()
                .anyMatch(
                    leakedSubnet -> leakedSubnet.getSubnetName().equals(subnet.getSubnetName())));
      }
    } catch (final Throwable t) {
      waitForStepRollbackDone(createSubnetsStep4);
    }
    getLogger().info("Rollback and delete subnets");
    waitForStepRollbackDone(createSubnetsStep4);
    checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US_AW, 0));
  }

  private Pair<NDSGroup, Plan> createExternalNetwork() throws SvcException {
    final Group externalGroup = MmsFactory.createGroupWithNDSPlan();
    final ObjectId externalGroupId = externalGroup.getId();
    MmsFactory.createBackupAdminUser(externalGroup);
    getNDSGroupSvc().ensureGroup(externalGroup.getId());

    final GCPCloudProviderContainer externalContainer =
        new GCPCloudProviderContainer(getContainer().getGcpOrganizationId(), null);
    final ObjectId externalContainerId =
        getNDSGroupDao().addCloudContainer(externalGroup.getId(), externalContainer);

    final Plan containerCreationPlan = new Plan(externalGroup.getId(), getNdsPlanContextFactory());
    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            containerCreationPlan.getPlanContext(),
            Collections.emptyMap(),
            externalContainerId,
            List.of(GCPRegionName.EASTERN_US));
    containerCreationPlan.addMove(provisionContainerMove);
    getPlanDao().save(containerCreationPlan);

    try {
      getLogger().info("Provisioning container to be used as external network");
      waitForPlanPerformSuccess(containerCreationPlan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(containerCreationPlan);
      throw t;
    }

    final NDSGroup ndsGroup = getNDSGroupDao().find(externalGroupId).get();
    return Pair.of(ndsGroup, containerCreationPlan);
  }

  private void createCustomRoute(final NDSGroup pNDSGroup, final String pCIDR) {
    getLogger().info("Create custom route on external network to {}", pCIDR);
    final GCPCloudProviderContainer container =
        (GCPCloudProviderContainer)
            pNDSGroup.getCloudProviderContainerByType(CloudProvider.GCP).get();
    final Operation operation =
        getGCPApiSvc()
            .createNetworkRoute(
                container.getGcpOrganizationId(),
                container.getGcpProjectId().get(),
                container.getNetworkName().get(),
                GCPStep.generateResourceName("rt", container.getId().toHexString()),
                pCIDR,
                0L,
                String.format(
                    "projects/%s/global/gateways/default-internet-gateway",
                    container.getGcpProjectId().get()),
                getLogger());
    assertTrue(
        waitForCondition(
            Duration.ofMinutes(2L),
            () ->
                getGCPApiSvc()
                    .checkGlobalOperationStatus(
                        container.getGcpOrganizationId(),
                        container.getGcpProjectId().get(),
                        getLogger(),
                        operation.getName())
                    .equals(OperationStatus.DONE)));
  }

  private List<Plan> peerToExternalNetwork(final NDSGroup pExternalGroup) {
    final GCPCloudProviderContainer externalContainer =
        (GCPCloudProviderContainer)
            pExternalGroup.getCloudProviderContainerByType(CloudProvider.GCP).get();

    final GCPPeerVpc peerToExternal =
        new GCPPeerVpc(
            getContainer().getId(),
            externalContainer.getNetworkName().get(),
            externalContainer.getGcpProjectId().get());

    final GCPPeerVpc peerFromExternal =
        new GCPPeerVpc(
            externalContainer.getId(),
            getContainer().getNetworkName().get(),
            getContainer().getGcpProjectId().get());

    final Plan peerToExternalPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final Plan peerFromExternalPlan =
        new Plan(pExternalGroup.getGroupId(), getNdsPlanContextFactory());

    final GCPSyncPeeringConnectionMove peerToExternalMove =
        GCPSyncPeeringConnectionMove.factoryCreate(
            peerToExternalPlan.getPlanContext(), getContainer().getId(), peerToExternal.getId());
    getGCPPeerVpcDao().addGcpPeerVpc(getGroup().getId(), peerToExternal);

    final GCPSyncPeeringConnectionMove peerFromExternalMove =
        GCPSyncPeeringConnectionMove.factoryCreate(
            peerFromExternalPlan.getPlanContext(),
            externalContainer.getId(),
            peerFromExternal.getId());
    getGCPPeerVpcDao().addGcpPeerVpc(pExternalGroup.getGroupId(), peerFromExternal);

    peerToExternalPlan.addMove(peerToExternalMove);
    peerFromExternalPlan.addMove(peerFromExternalMove);
    getPlanDao().save(peerToExternalPlan);
    getPlanDao().save(peerFromExternalPlan);

    try {
      getLogger().info("Peering to external network");
      waitForPlanPerformSuccess(peerToExternalPlan);
      getLogger().info("Peering from external network");
      waitForPlanPerformSuccess(peerFromExternalPlan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(peerToExternalPlan);
      waitForPlanRollbackSuccess(peerFromExternalPlan);
      throw t;
    }

    return List.of(peerToExternalPlan, peerFromExternalPlan);
  }

  private List<Plan> createExternalNetworkAndPeer() throws SvcException {
    final List<Plan> plansToRollback = new ArrayList<>();
    try {
      final Pair<NDSGroup, Plan> groupAndPlan = createExternalNetwork();
      final NDSGroup externalNetworkGroup = groupAndPlan.getLeft();
      plansToRollback.add(groupAndPlan.getRight());

      createCustomRoute(externalNetworkGroup, "192.168.240.0/21");

      final List<Plan> peerPlans = peerToExternalNetwork(externalNetworkGroup);
      plansToRollback.addAll(peerPlans);

    } catch (final Throwable t) {
      for (final Plan planToRollback : plansToRollback) {
        waitForPlanRollbackSuccess(planToRollback);
      }
      throw t;
    }
    return plansToRollback;
  }

  /**
   * This test ensures that GCPCreateSubnetStep correctly parses the error returned by GCP when we
   * try to provision a subnet over overlapping peered network resources. If this test begins
   * failing because GCP has updated the error message, prod projects will be effected immediately.
   * A P2 ticket should be filed and the error handling should be updated and back-ported.
   *
   * @param pPlan
   * @throws SvcException
   */
  private void testGCPCreateSubnetStepInternal_withConflictingPeers(final Plan pPlan)
      throws SvcException {
    final List<Plan> plansToRollback = createExternalNetworkAndPeer();
    try {
      // start with 0 subnets
      checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 0));

      final GCPCreateSubnetStep createSubnetStep =
          getCreateSubnetStep(pPlan, List.of(GCPRegionName.EASTERN_US), _stepIndex++);
      try {
        getLogger().info("Attempt to create a subnet around a conflicting peer");

        // ensure that the step first tries and fails to put the subnet at the top of the Atlas cidr
        assertTrue(
            waitForCondition(
                Duration.ofMinutes(2L),
                () -> {
                  createSubnetStep.perform();
                  final List<String> failedCIDRs =
                      createSubnetStep
                          .getStateValue("failedSubnetCIDRs")
                          .map(BasicDBList.class::cast)
                          .orElse(new BasicDBList())
                          .stream()
                          .map(String.class::cast)
                          .collect(Collectors.toList());
                  assertTrue(failedCIDRs.size() <= 1);
                  return failedCIDRs.contains("192.168.248.0/21");
                }));

        // ensure that the step then tries and fails to put the subnet in the next subnet block
        assertTrue(
            waitForCondition(
                Duration.ofMinutes(2L),
                () -> {
                  createSubnetStep.perform();
                  final List<String> failedCIDRs =
                      createSubnetStep
                          .getStateValue("failedSubnetCIDRs")
                          .map(BasicDBList.class::cast)
                          .orElse(new BasicDBList())
                          .stream()
                          .map(String.class::cast)
                          .collect(Collectors.toList());
                  assertTrue(failedCIDRs.size() <= 2);
                  return failedCIDRs.contains("192.168.240.0/21");
                }));

        // ensure step succeeds after placing the subnet in an un-overlapped cidr block
        waitForStepPerformDone(createSubnetStep);
        final List<GCPAtlasSubnet> subnets = createSubnetStep.perform().getData().getSubnets();
        assertEquals(1, subnets.size());
        assertEquals("192.168.232.0/21", subnets.get(0).getCidrBlock());
        checkCloudProviderSubnets(Map.of(GCPRegionName.EASTERN_US, 1));
      } finally {
        getLogger().info("Tear down test subnet");
        waitForStepRollbackDone(createSubnetStep);
      }
    } finally {
      getLogger().info("Tear down test peered network");
      for (final Plan planToRollback : plansToRollback) {
        waitForPlanRollbackSuccess(planToRollback);
      }
    }
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPCreateSubnetStep() throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final List<Step<?>> setupSteps = setupProject(plan);

    try {
      testGCPCreateSubnetStepInternal(plan);
      testGCPCreateSubnetStepInternal_withConflictingPeers(plan);
    } finally {
      getLogger().info("Tear down test resources");
      for (final Step<?> setupStep : setupSteps)
        performUnconditionally(() -> waitForStepRollbackDone(setupStep));
    }
  }
}
