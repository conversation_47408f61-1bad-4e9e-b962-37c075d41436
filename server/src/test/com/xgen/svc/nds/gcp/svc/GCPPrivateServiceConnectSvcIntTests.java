package com.xgen.svc.nds.gcp.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.Limits.FieldDefs;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectRegionGroupDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPConsumerForwardingRule;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectEndpointGroup;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectEndpointGroup.Status;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectRegionGroup;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class GCPPrivateServiceConnectSvcIntTests extends JUnit5BaseSvcTest {
  private Group _group;
  private ObjectId _containerId;
  private AppUser _user;
  private AuditInfo _auditInfo;

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private GCPPrivateServiceConnectRegionGroupDao _regionGroupDao;
  @Inject private GCPPrivateServiceConnectSvc _gcpPrivateServiceConnectSvc;
  @Inject private GCPOrganizationDao _gcpOrganizationDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = new AppUser();
    _auditInfo = MmsFactory.createAuditInfoFromUiCall(_user, false, "remoteAddress.com");
    _ndsGroupSvc.ensureGroup(_group.getId());
    _containerId =
        _ndsGroupDao.addCloudContainer(
            _group.getId(), new GCPCloudProviderContainer(NDSModelTestFactory.getGCPContainer()));
    final GCPOrganization gcpOrganization =
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization());
    _gcpOrganizationDao.save(gcpOrganization);
  }

  @Test
  public void testAddPrivateServiceConnectRegionGroup_sameRegion() {
    try {
      final GCPPrivateServiceConnectRegionGroup regionGroup1 =
          _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
              _ndsGroupSvc.find(_group.getId()).orElseThrow(),
              GCPRegionName.values()[0],
              _auditInfo);
    } catch (final SvcException pE) {
      fail();
    }

    try {
      final GCPPrivateServiceConnectRegionGroup regionGroup1 =
          _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
              _ndsGroupSvc.find(_group.getId()).orElseThrow(),
              GCPRegionName.values()[0],
              _auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(
          pE.getErrorCode(), NDSErrorCode.PRIVATE_ENDPOINT_SERVICE_ALREADY_EXISTS_FOR_REGION);
      assertTrue(pE.getMessageParams().contains(CloudProvider.GCP.getDescription()));
    }
  }

  @Test
  public void testAddPrivateServiceConnectRegionGroup_runOutOfRegions() throws SvcException {
    _ndsGroupSvc.setLimit(
        _group.getId(),
        FieldDefs.NUM_PRIVATE_SERVICE_CONNECTIONS_PER_REGION_GROUP.value,
        50L,
        _auditInfo,
        _user);
    _ndsGroupSvc.setLimit(
        _group.getId(), FieldDefs.GCP_PSC_NAT_SUBNET_MASK.value, 20L, _auditInfo, _user);

    final GCPPrivateServiceConnectRegionGroup regionGroup1 =
        _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
            _ndsGroupSvc.find(_group.getId()).orElseThrow(), GCPRegionName.values()[0], _auditInfo);
    assertEquals(14, NetUtils.getCidrMask(regionGroup1.getNatCIDRBlock().get()));

    final GCPPrivateServiceConnectRegionGroup regionGroup2 =
        _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
            _ndsGroupSvc.find(_group.getId()).orElseThrow(), GCPRegionName.values()[1], _auditInfo);
    assertEquals(14, NetUtils.getCidrMask(regionGroup2.getNatCIDRBlock().get()));
    assertFalse(
        NetUtils.areOverlappingIpv4CidrBlocks(
            regionGroup1.getNatCIDRBlock().get(), regionGroup2.getNatCIDRBlock().get()));

    final GCPPrivateServiceConnectRegionGroup regionGroup3 =
        _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
            _ndsGroupSvc.find(_group.getId()).orElseThrow(), GCPRegionName.values()[2], _auditInfo);
    assertEquals(14, NetUtils.getCidrMask(regionGroup3.getNatCIDRBlock().get()));
    assertFalse(
        NetUtils.areOverlappingIpv4CidrBlocks(
            regionGroup2.getNatCIDRBlock().get(), regionGroup3.getNatCIDRBlock().get()));

    final GCPPrivateServiceConnectRegionGroup regionGroup4 =
        _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
            _ndsGroupSvc.find(_group.getId()).orElseThrow(), GCPRegionName.values()[3], _auditInfo);
    assertEquals(14, NetUtils.getCidrMask(regionGroup4.getNatCIDRBlock().get()));
    assertFalse(
        NetUtils.areOverlappingIpv4CidrBlocks(
            regionGroup3.getNatCIDRBlock().get(), regionGroup4.getNatCIDRBlock().get()));

    try {
      _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
          _ndsGroupSvc.find(_group.getId()).orElseThrow(), GCPRegionName.values()[4], _auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(
          NDSErrorCode.INSUFFICIENT_FREE_ADDRESS_SPACE_FOR_ADDITIONAL_PSC_REGION,
          pE.getErrorCode());
    }

    // if we delete a region group, we can now re-use the CIDR block for a new region
    _regionGroupDao.deleteRegionGroup(_group.getId(), _containerId, regionGroup3.getId());

    final GCPPrivateServiceConnectRegionGroup regionGroup5 =
        _gcpPrivateServiceConnectSvc.addPrivateServiceConnectRegionGroup(
            _ndsGroupSvc.find(_group.getId()).orElseThrow(), GCPRegionName.values()[4], _auditInfo);
    assertEquals(14, NetUtils.getCidrMask(regionGroup3.getNatCIDRBlock().get()));
    assertEquals(regionGroup3.getNatCIDRBlock().get(), regionGroup5.getNatCIDRBlock().get());
    assertFalse(
        NetUtils.areOverlappingIpv4CidrBlocks(
            regionGroup5.getNatCIDRBlock().get(), regionGroup4.getNatCIDRBlock().get()));
  }

  private List<GCPPrivateServiceConnectRegionGroup> fetchPrivateServiceConnectRegionGroups() {
    return _ndsGroupDao
        .find(_group.getId())
        .get()
        .getCloudProviderContainer(_containerId)
        .get()
        .getEndpointServices()
        .stream()
        .map(GCPPrivateServiceConnectRegionGroup.class::cast)
        .collect(Collectors.toList());
  }

  @Test
  public void testPatchEndpointGroup() throws SvcException {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();

    final List<GCPConsumerForwardingRule> forwardingRules1 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpoint-group-name-1");
    final List<GCPConsumerForwardingRule> forwardingRules2 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpoint-group-name-2");

    final GCPPrivateServiceConnectEndpointGroup endpointGroup1 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpoint-group-name-1", "gcpProjectId1", forwardingRules1, 0)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();
    final GCPPrivateServiceConnectEndpointGroup endpointGroup2 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpoint-group-name-2", "gcpProjectId2", forwardingRules2, 1)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();

    _regionGroupDao.initializePrivateEndpointService(regionGroup, _group.getId(), _containerId);
    _regionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _group.getId(), _containerId, regionGroup.getId(), endpointGroup1);
    _regionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _group.getId(), _containerId, regionGroup.getId(), endpointGroup2);
    final GCPPrivateServiceConnectRegionGroup regionGroupWithEndpointGroups =
        fetchPrivateServiceConnectRegionGroups().get(0);

    final HashMap<String, String> forwardingRuleMap = new LinkedHashMap<>();
    IntStream.range(0, regionGroup.getSize().get())
        .forEach(
            i -> {
              forwardingRuleMap.put(
                  String.format("endpoint-group-name-2-%d", i), String.format("10.0.0.%d", i));
            });

    // patching an available endpoint does nothing
    {
      _gcpPrivateServiceConnectSvc.patchEndpointGroup(
          _group.getId(),
          _containerId,
          regionGroupWithEndpointGroups,
          endpointGroup2.getId(),
          "new-name",
          "correctedProjectId",
          forwardingRuleMap,
          _auditInfo);
      final NDSGroup groupAfter = _ndsGroupDao.find(_group.getId()).get();
      final GCPPrivateServiceConnectRegionGroup regionGroupAfter =
          ((GCPCloudProviderContainer) groupAfter.getCloudProviderContainer(_containerId).get())
              .getPscRegionGroups().stream()
                  .filter(rg -> rg.getId().equals(regionGroup.getId()))
                  .findFirst()
                  .get();
      assertEquals(Status.AVAILABLE, regionGroupAfter.getGCPEndpointGroups().get(0).getStatus());
      assertEquals(Status.AVAILABLE, regionGroupAfter.getGCPEndpointGroups().get(1).getStatus());
      assertEquals(
          "endpoint-group-name-2",
          regionGroupAfter.getGCPEndpointGroups().get(1).getEndpointGroupName());
      assertTrue(regionGroupAfter.getNeedsUpdateAfter().isEmpty());
      assertFalse(groupAfter.getPlanASAP());
    }

    // patching a failed endpoint into a duplicate of another fails
    {
      try {
        final HashMap<String, String> forwardingRuleMapDuplicateOfAnother = new LinkedHashMap<>();
        IntStream.range(0, regionGroup.getSize().get())
            .forEach(
                i -> {
                  forwardingRuleMapDuplicateOfAnother.put(
                      String.format("endpoint-group-name-1-%d", i), String.format("10.0.0.%d", i));
                });
        _gcpPrivateServiceConnectSvc.patchEndpointGroup(
            _group.getId(),
            _containerId,
            regionGroupWithEndpointGroups,
            endpointGroup2.getId(),
            "endpoint-group-name-1",
            "gcpProjectId1",
            forwardingRuleMapDuplicateOfAnother,
            _auditInfo);
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.PRIVATE_ENDPOINT_GROUP_ALREADY_EXISTS, pE.getErrorCode());
      }
      final NDSGroup groupAfter = _ndsGroupDao.find(_group.getId()).get();
      final GCPPrivateServiceConnectRegionGroup regionGroupAfter =
          ((GCPCloudProviderContainer) groupAfter.getCloudProviderContainer(_containerId).get())
              .getPscRegionGroups().stream()
                  .filter(rg -> rg.getId().equals(regionGroup.getId()))
                  .findFirst()
                  .get();
      assertEquals(Status.AVAILABLE, regionGroupAfter.getGCPEndpointGroups().get(0).getStatus());
      assertEquals(Status.AVAILABLE, regionGroupAfter.getGCPEndpointGroups().get(1).getStatus());
      assertEquals(
          "endpoint-group-name-2",
          regionGroupAfter.getGCPEndpointGroups().get(1).getEndpointGroupName());
      assertTrue(regionGroupAfter.getNeedsUpdateAfter().isEmpty());
      assertFalse(groupAfter.getPlanASAP());
    }
  }

  @Test
  public void testPatchEndpointGroup_onlyProjectId() throws SvcException {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();

    final List<GCPConsumerForwardingRule> forwardingRules1 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpoint-group-name-1");
    final List<GCPConsumerForwardingRule> forwardingRules2 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpoint-group-name-2");

    final GCPPrivateServiceConnectEndpointGroup endpointGroup1 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpoint-group-name-1", "gcpProjectId1", forwardingRules1, 0)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();
    final GCPPrivateServiceConnectEndpointGroup endpointGroup2 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpoint-group-name-2", "gcpProjectId2", forwardingRules2, 1)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();

    _regionGroupDao.initializePrivateEndpointService(regionGroup, _group.getId(), _containerId);
    _regionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _group.getId(), _containerId, regionGroup.getId(), endpointGroup1);
    _regionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _group.getId(), _containerId, regionGroup.getId(), endpointGroup2);

    _regionGroupDao.setEndpointGroupErrorMessage(
        _group.getId(),
        _containerId,
        regionGroup.getId(),
        endpointGroup2.getEndpointGroupName(),
        "nope");

    final GCPPrivateServiceConnectRegionGroup regionGroupWithFailedEndpointGroup =
        fetchPrivateServiceConnectRegionGroups().get(0);

    _gcpPrivateServiceConnectSvc.patchEndpointGroup(
        _group.getId(),
        _containerId,
        regionGroupWithFailedEndpointGroup,
        endpointGroup2.getId(),
        null,
        "gcpProjectIdFixed",
        Map.of(),
        _auditInfo);
    final NDSGroup groupAfter = _ndsGroupDao.find(_group.getId()).get();
    final GCPPrivateServiceConnectRegionGroup regionGroupAfter =
        ((GCPCloudProviderContainer) groupAfter.getCloudProviderContainer(_containerId).get())
            .getPscRegionGroups().stream()
                .filter(rg -> rg.getId().equals(regionGroup.getId()))
                .findFirst()
                .get();
    assertEquals(Status.INITIATING, regionGroupAfter.getGCPEndpointGroups().get(1).getStatus());
    assertEquals(
        "endpoint-group-name-2",
        regionGroupAfter.getGCPEndpointGroups().get(1).getEndpointGroupName());
    assertTrue(regionGroupAfter.getGCPEndpointGroups().get(1).getErrorMessage().isEmpty());
    assertEquals(
        "gcpProjectIdFixed",
        regionGroupAfter.getGCPEndpointGroups().get(1).getCustomerGCPProjectId());
    assertEquals(50, regionGroupAfter.getGCPEndpointGroups().get(1).getForwardingRules().size());
    assertTrue(regionGroupAfter.getNeedsUpdateAfter().isPresent());
    assertTrue(groupAfter.getPlanASAP());
  }

  @Test
  public void testPatchEndpointGroup_onlyForwardingRules() throws SvcException {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();

    final List<GCPConsumerForwardingRule> forwardingRules1 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpoint-group-name-1");
    final List<GCPConsumerForwardingRule> forwardingRules2 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpoint-group-name-2");

    final GCPPrivateServiceConnectEndpointGroup endpointGroup1 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpoint-group-name-1", "gcpProjectId1", forwardingRules1, 0)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();
    final GCPPrivateServiceConnectEndpointGroup endpointGroup2 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpoint-group-name-2", "gcpProjectId2", forwardingRules2, 1)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();

    _regionGroupDao.initializePrivateEndpointService(regionGroup, _group.getId(), _containerId);
    _regionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _group.getId(), _containerId, regionGroup.getId(), endpointGroup1);
    _regionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _group.getId(), _containerId, regionGroup.getId(), endpointGroup2);

    _regionGroupDao.setEndpointGroupErrorMessage(
        _group.getId(),
        _containerId,
        regionGroup.getId(),
        endpointGroup2.getEndpointGroupName(),
        "nope");

    final GCPPrivateServiceConnectRegionGroup regionGroupWithFailedEndpointGroup =
        fetchPrivateServiceConnectRegionGroups().get(0);

    final HashMap<String, String> forwardingRuleMap = new LinkedHashMap<>();
    IntStream.range(0, regionGroup.getSize().get())
        .forEach(
            i -> {
              forwardingRuleMap.put(
                  String.format("fixed-rule-name-%d", i), String.format("10.0.0.%d", i));
            });

    _gcpPrivateServiceConnectSvc.patchEndpointGroup(
        _group.getId(),
        _containerId,
        regionGroupWithFailedEndpointGroup,
        endpointGroup2.getId(),
        null,
        null,
        forwardingRuleMap,
        _auditInfo);
    final NDSGroup groupAfter = _ndsGroupDao.find(_group.getId()).get();
    final GCPPrivateServiceConnectRegionGroup regionGroupAfter =
        ((GCPCloudProviderContainer) groupAfter.getCloudProviderContainer(_containerId).get())
            .getPscRegionGroups().stream()
                .filter(rg -> rg.getId().equals(regionGroup.getId()))
                .findFirst()
                .get();
    assertEquals(Status.INITIATING, regionGroupAfter.getGCPEndpointGroups().get(1).getStatus());
    assertEquals(
        "endpoint-group-name-2",
        regionGroupAfter.getGCPEndpointGroups().get(1).getEndpointGroupName());
    assertTrue(regionGroupAfter.getGCPEndpointGroups().get(1).getErrorMessage().isEmpty());
    assertEquals(
        "gcpProjectId2", regionGroupAfter.getGCPEndpointGroups().get(1).getCustomerGCPProjectId());
    assertEquals(50, regionGroupAfter.getGCPEndpointGroups().get(1).getForwardingRules().size());
    assertEquals(
        "fixed-rule-name-5",
        regionGroupAfter
            .getGCPEndpointGroups()
            .get(1)
            .getForwardingRules()
            .get(5)
            .getForwardingRuleName());
    assertTrue(regionGroupAfter.getNeedsUpdateAfter().isPresent());
    assertTrue(groupAfter.getPlanASAP());
  }
}
