package com.xgen.svc.nds.gcp.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.fail;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.Job.Status;
import com.xgen.cloud.common.jobqueue._public.model.JobHandler;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.SysProp;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.GcpSnapshotFieldBuilder;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.LeakedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanedItem;
import com.xgen.cloud.nds.gcp._private.dao.GCPCloudProviderContainerDao;
import com.xgen.cloud.nds.gcp._private.dao.GCPInstanceHardwareDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPLeakedItem;
import com.xgen.cloud.nds.gcp._public.model.GCPLeakedItem.Type;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem.Builder;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem.Expirations;
import com.xgen.cloud.nds.leakeditem._private.dao.LeakedItemDao;
import com.xgen.cloud.nds.leakeditem._public.model.LeakedItemCleanupState;
import com.xgen.cloud.nds.leakeditem._public.model.LeakedItemProcessorMode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPCreateSnapshotStep.SnapshotType;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.RandomStringUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GCPCloudProviderItemProcessorSvcIntTests extends GCPExternalIntTest {

  @Inject private GCPCloudProviderItemProcessorSvc _svc;

  @Inject private GCPInstanceHardwareDao _instanceHardwareDao;

  @Inject private LeakedItemDao _leakedItemDao;

  @Inject private NDSOrphanedItemDao _orphanedItemDao;

  @Inject private GCPCloudProviderContainerDao _containerDao;

  @Inject BackupSnapshotDao _backupSnapshotDao;

  @Inject ClusterDescriptionDao _clusterDescriptionDao;

  private Plan _plan;
  private GCPCloudProviderContainer _cachedContainer;
  private boolean _planNeedsRollback;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_LEAKED_ITEMS_PROCESSOR_MINIMUM_ITEM_LIFE_SECONDS.value,
            "0",
            AppSettings.SettingType.MEMORY);
    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value,
            LeakedItemProcessorMode.PAUSED.name(),
            AppSettings.SettingType.MEMORY);
    getAppSettings()
        .setProp(
            Fields.NDS_GCP_LEAKED_ITEM_DETECTION_MODE.value,
            LeakedItemProcessorMode.PAUSED.name(),
            AppSettings.SettingType.MEMORY);
  }

  private void setupPlan() {
    _plan = setupPlanWithContainerAndSingleInstance();
    _planNeedsRollback = true;
    try {
      waitForPlanPerformSuccess(_plan);
      _cachedContainer = getContainer();
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(_plan);
      throw t;
    }
  }

  @Test
  public void testCorrectNumberOfCleanupJobsCreated() {
    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value,
            LeakedItemProcessorMode.JOB_HANDLER.name(),
            AppSettings.SettingType.MEMORY);
    _svc.runCleanupOnly();

    final List<Job> jobsCreated =
        _svc.getJobsProcessorSvc().findAllJobsForHandler(GCPLeakedItemCleanupJobHandler.class);
    assertEquals(1, jobsCreated.size());

    final Job job = jobsCreated.get(0);

    final BasicDBObject params = job.getParameters();
    final ObjectId id = params.getObjectId(LeakedItemCleanupState.FieldDefs.ID);
    final LeakedItemCleanupState state = _svc.getLeakedItemCleanupStateSvc().findById(id);

    assertEquals(CloudProvider.GCP, state.getCloudProvider());
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    if (_cachedContainer != null) {
      _containerDao.setProvisionedFields(
          getGroup().getId(),
          _cachedContainer.getId(),
          _cachedContainer.getGcpProjectId().get(),
          _cachedContainer.getNetworkName().get(),
          _cachedContainer.getSubnets());
    }
    if (_planNeedsRollback) {
      waitForPlanRollbackSuccess(_plan);
    }
    super.tearDown();
  }

  @Test(timeout = 150 * 60 * 1000)
  public void testFullLifeCycleWithCron() {
    setupPlan();
    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value,
            LeakedItemProcessorMode.CRON.name(),
            AppSettings.SettingType.MEMORY);
    getAppSettings()
        .setProp(
            Fields.NDS_GCP_LEAKED_ITEM_DETECTION_MODE.value,
            LeakedItemProcessorMode.CRON.name(),
            AppSettings.SettingType.MEMORY);

    final NDSGroup group = getNDSGroup();
    final ClusterDescription cluster = getClusterDescription();
    final GCPInstanceHardware instanceHardware =
        (GCPInstanceHardware)
            getInstanceHardware(group.getGroupId(), cluster.getName(), getInstanceIds().get(0));
    final ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(cluster.getName(), instanceHardware.getInstanceId());
    final GCPCloudProviderContainer container = getContainer();

    final Logger logger = LoggerFactory.getLogger(GCPCloudProviderItemProcessorSvcIntTests.class);

    Map<String, String> tags = new HashMap<>();
    tags.put("snapshottype", SnapshotType.BACKUP.getValue());
    tags.put(NDSDefaults.CLUSTER_NAME_TAG_NAME.toLowerCase(), cluster.getName());
    tags.put(
        NDSDefaults.CLUSTER_UNIQUE_ID_TAG_NAME.toLowerCase(), cluster.getUniqueId().toString());
    tags.put(NDSDefaults.GROUP_ID_TAG_NAME.toLowerCase(), getGroup().getId().toString());

    final String snapshotName =
        String.format(
            "%s-%s-%s",
            "ds",
            container.getGcpProjectId().get(),
            RandomStringUtils.randomAlphanumeric(8).toLowerCase());

    getGCPApiSvc()
        .createSnapshot(
            container.getGcpOrganizationId(),
            container.getGcpProjectId().get(),
            instanceHardware.getZoneName().get(),
            logger,
            instanceHardware.getDiskName().get(),
            snapshotName,
            tags);

    final ObjectId snapshotId = new ObjectId();

    final SnapshotUpdate snapshotUpdate =
        new SnapshotUpdate()
            .setId(snapshotId)
            .setProjectId(getGroup().getId())
            .setClusterName(cluster.getName())
            .setDeploymentClusterName(cluster.getName())
            .setClusterUniqueId(cluster.getUniqueId())
            .setDescription("testing")
            .setScheduledCreationDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
            .setScheduledDeletionDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
            .setSnapshotInitiationDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(VersionUtils.Version.fromString("3.6.3"))
            .setUsedDiskSpace(0L)
            .setCloudProviders(Arrays.asList(CloudProvider.GCP))
            .setEncryptionDetails(null)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setRequestingUser(null)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setPolicyItemIds(Collections.emptyList())
            .setMainPolicyItemId(null)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(new Date())
            .setGcpSnapshotField(
                new GcpSnapshotFieldBuilder()
                    .withSnapshotName(snapshotName)
                    .withSourceDiskName(instanceHardware.getDiskName().get())
                    .withGcpOrgId(container.getGcpOrganizationId())
                    .withGcpProjectId(container.getGcpProjectId().get())
                    .withRegionName(instanceHardware.getZoneName().get().getRegion().toString())
                    .withSnapshotDiskSizeGb(instanceHardware.getDiskSizeGB().get()));

    _backupSnapshotDao.addBackupSnapshot(snapshotUpdate);

    final GCPOrphanedItem instance =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setZoneName(instanceHardware.getZoneName().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.INSTANCE)
            .setId(instanceHardware.getGCPInstanceName().get())
            .setRegionName(instanceHardware.getZoneName().get().getRegion())
            .setType(Type.INSTANCE)
            .build();

    final GCPOrphanedItem disk =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setZoneName(instanceHardware.getZoneName().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.PERSISTENT_DISK)
            .setId(instanceHardware.getDiskName().get())
            .setRegionName(instanceHardware.getZoneName().get().getRegion())
            .setType(Type.PERSISTENT_DISK)
            .build();

    final GCPOrphanedItem subnet =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.SUBNET)
            .setId(container.getSubnets().get(0).getSubnetName())
            .setRegionName(container.getSubnets().get(0).getRegion())
            .setType(Type.SUBNET)
            .build();

    final GCPOrphanedItem project =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.PROJECT)
            .setId(container.getGcpProjectId().get())
            .setType(Type.PROJECT)
            .build();

    _svc.findNewLeakedItems();

    final Set<String> createdIds =
        Set.of(instance.getId(), disk.getId(), subnet.getId(), project.getId(), snapshotName);

    {
      final List<GCPLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Since metadata still exists for the created resources, they should not be considered leaked
      assertEquals(List.of(), leakedItems);
    }

    _orphanedItemDao.add(instance);
    _orphanedItemDao.add(disk);
    _orphanedItemDao.add(subnet);
    _orphanedItemDao.add(project);

    // Remove metadata for instance
    _instanceHardwareDao.unsetProvisionedFields(
        replicaSetHardware.getId(), instanceHardware.getInstanceId(), false, CloudProvider.GCP);
    _containerDao.unsetProvisionedFields(group.getGroupId(), container.getId());

    _svc.findNewLeakedItems();
    {
      final List<GCPLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exists but items are in the orphan queue and have not expired
      assertEquals(List.of(), leakedItems);
    }

    _orphanedItemDao.expireItem(instance.getId(), CloudProvider.GCP, Type.INSTANCE);
    _orphanedItemDao.expireItem(disk.getId(), CloudProvider.GCP, Type.PERSISTENT_DISK);
    _orphanedItemDao.expireItem(subnet.getId(), CloudProvider.GCP, Type.SUBNET);
    _orphanedItemDao.expireItem(project.getId(), CloudProvider.GCP, Type.PROJECT);
    _backupSnapshotDao.remove(new BasicDBObject());

    _svc.findNewLeakedItems();
    {
      final Set<String> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .map(OrphanedItem::getId)
              .filter(createdIds::contains)
              .collect(Collectors.toSet());

      // Metadata does not exist and the items in the orphan queue have expired, but the cluster
      // is not idle, so cluster level resources will be marked as in use
      assertEquals(Set.of(subnet.getId(), project.getId(), snapshotName), leakedItems);
    }

    _clusterDescriptionDao.setState(group.getGroupId(), cluster.getName(), State.IDLE);

    _svc.findNewLeakedItems();
    {
      final Set<String> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .map(OrphanedItem::getId)
              .filter(createdIds::contains)
              .collect(Collectors.toSet());

      // Metadata does not exists, items in the orphan queue are expired, the cluster is idle, and
      // the snapshot is no longer in the database.
      assertEquals(createdIds, leakedItems);
    }

    _svc.deleteApprovedItems();
    {
      final List<GCPLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .toList();

      // Item still exist because it has not been marked for deletion
      final Set<String> leakedItemIds =
          leakedItems.stream().map(OrphanedItem::getId).collect(Collectors.toSet());
      assertEquals(createdIds, leakedItemIds);
      leakedItems.forEach(i -> assertEquals(Optional.empty(), i.getDeletedDate()));
    }

    _leakedItemDao.bulkApproveForDeletion(
        List.of(instance.getId()), CloudProvider.GCP, Type.INSTANCE);
    _leakedItemDao.bulkApproveForDeletion(
        List.of(disk.getId()), CloudProvider.GCP, Type.PERSISTENT_DISK);
    _leakedItemDao.bulkApproveForDeletion(List.of(subnet.getId()), CloudProvider.GCP, Type.SUBNET);
    _leakedItemDao.bulkApproveForDeletion(
        List.of(project.getId()), CloudProvider.GCP, Type.PROJECT);
    _leakedItemDao.bulkApproveForDeletion(List.of(snapshotName), CloudProvider.GCP, Type.SNAPSHOT);

    // First call will submit the delete request
    _svc.deleteApprovedItems();
    verifyGCPInstanceDeleted(
        instance.getGcpOrganizationId(),
        instance.getProjectName(),
        instance.getZoneName(),
        instance.getId());

    // Second call will confirm that the delete request succeeded and set the date
    _svc.deleteApprovedItems();
    final LeakedItem leakedInstance =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, instance.getId(), Type.INSTANCE)
            .get();
    assertNotEquals(Optional.empty(), leakedInstance.getDeletedDate());
    // Disk should also be deleted
    verifyPersistentDiskDeleted(
        disk.getGcpOrganizationId(), disk.getProjectName(), disk.getZoneName(), disk.getId());
    verifySnapshotDeleted(project.getGcpOrganizationId(), project.getId(), snapshotName);
    final LeakedItem leakedSnapshot =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, snapshotName, Type.SNAPSHOT)
            .get();
    assertNotEquals(Optional.empty(), leakedSnapshot.getDeletedDate());

    // Third call will confirm that the data disk is deleted, set its deleted date, and delete the
    // project
    _svc.deleteApprovedItems();
    final LeakedItem leakedDisk =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, disk.getId(), Type.PERSISTENT_DISK)
            .get();
    assertNotEquals(Optional.empty(), leakedDisk.getDeletedDate());
    verifyProjectDeleted(project.getGcpOrganizationId(), project.getId());

    final LeakedItem leakedSubnet =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, subnet.getId(), Type.SUBNET)
            .get();
    assertNotEquals(Optional.empty(), leakedSubnet.getDeletedDate());

    final LeakedItem leakedProject =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, project.getId(), Type.PROJECT)
            .get();
    assertNotEquals(Optional.empty(), leakedProject.getDeletedDate());
    _planNeedsRollback = false;

    // Unset all the dates on the leaked items to test that they get removed on pre-processing
    _leakedItemDao.unsetDeleteDataForTest(instance.getId(), CloudProvider.GCP, Type.INSTANCE);
    _leakedItemDao.unsetDeleteDataForTest(disk.getId(), CloudProvider.GCP, Type.PERSISTENT_DISK);
    _leakedItemDao.unsetDeleteDataForTest(subnet.getId(), CloudProvider.GCP, Type.SUBNET);
    _leakedItemDao.unsetDeleteDataForTest(project.getId(), CloudProvider.GCP, Type.PROJECT);
    _leakedItemDao.unsetDeleteDataForTest(snapshotName, CloudProvider.GCP, Type.SNAPSHOT);

    retryAssertion(
        Duration.ofMinutes(2),
        () -> {
          _svc.removeItemsIfNotNeeded();
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, instance.getId(), Type.INSTANCE));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, disk.getId(), Type.PERSISTENT_DISK));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, subnet.getId(), Type.SUBNET));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, project.getId(), Type.PROJECT));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, snapshotName, Type.SNAPSHOT));
        });

    // Cleaning up the orphan items from the dao.  removeItemsIfNotNeeded already verifies
    //  that the resources are there or not so there's no need to orphan them again
    _orphanedItemDao.remove(instance);
    _orphanedItemDao.remove(disk);
    _orphanedItemDao.remove(subnet);
    _orphanedItemDao.remove(project);
  }

  private void waitForJobToComplete(Class<? extends JobHandler> pJobHandlerClass)
      throws InterruptedException {
    List<Job> jobs;
    do {
      jobs = _svc.getJobsProcessorSvc().findAllJobsForHandler(pJobHandlerClass);

      // if any jobs have failed, test has failed

      final List<Job> failedJobs =
          jobs.stream().filter(j -> Status.FAILED_STATUSES.contains(j.getStatus())).toList();

      if (!failedJobs.isEmpty()) {
        fail("Failed jobs: " + failedJobs);
      }

      // brief sleep to avoid busy waiting
      Thread.sleep(1000);
      // if there are any incomplete jobs, continue
    } while (jobs.stream().anyMatch(j -> !j.getStatus().equals(Job.Status.COMPLETED)));
  }

  @Test(timeout = 150 * 60 * 1000L)
  public void testFullLifeCycleWithJobHandler() throws InterruptedException {
    SysProp.set(SysProp.Property.JOB_PROCESSOR_ENABLED, "true");
    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value,
            LeakedItemProcessorMode.JOB_HANDLER.name(),
            AppSettings.SettingType.MEMORY);
    getAppSettings()
        .setProp(
            Fields.NDS_GCP_LEAKED_ITEM_DETECTION_MODE.value,
            LeakedItemProcessorMode.JOB_HANDLER.name(),
            AppSettings.SettingType.MEMORY);

    setupPlan();

    final NDSGroup group = getNDSGroup();
    final ClusterDescription cluster = getClusterDescription();
    final GCPInstanceHardware instanceHardware =
        (GCPInstanceHardware)
            getInstanceHardware(group.getGroupId(), cluster.getName(), getInstanceIds().get(0));
    final ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(cluster.getName(), instanceHardware.getInstanceId());
    final GCPCloudProviderContainer container = getContainer();
    final Logger logger = LoggerFactory.getLogger(GCPCloudProviderItemProcessorSvcIntTests.class);

    Map<String, String> tags = new HashMap<>();
    tags.put("snapshottype", SnapshotType.BACKUP.getValue());
    tags.put(NDSDefaults.CLUSTER_NAME_TAG_NAME.toLowerCase(), cluster.getName());
    tags.put(
        NDSDefaults.CLUSTER_UNIQUE_ID_TAG_NAME.toLowerCase(), cluster.getUniqueId().toString());
    tags.put(NDSDefaults.GROUP_ID_TAG_NAME.toLowerCase(), getGroup().getId().toString());

    final String snapshotName =
        String.format(
            "%s-%s-%s",
            "ds",
            container.getGcpProjectId().get(),
            RandomStringUtils.randomAlphanumeric(8).toLowerCase());

    getGCPApiSvc()
        .createSnapshot(
            container.getGcpOrganizationId(),
            container.getGcpProjectId().get(),
            instanceHardware.getZoneName().get(),
            logger,
            instanceHardware.getDiskName().get(),
            snapshotName,
            tags);

    // create metadata for resources with old creation and deletion dates
    final ObjectId snapshotId = new ObjectId();

    final SnapshotUpdate snapshotUpdate =
        new SnapshotUpdate()
            .setId(snapshotId)
            .setProjectId(getGroup().getId())
            .setClusterName(cluster.getName())
            .setDeploymentClusterName(cluster.getName())
            .setClusterUniqueId(cluster.getUniqueId())
            .setDescription("testing")
            .setScheduledCreationDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
            .setScheduledDeletionDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
            .setSnapshotInitiationDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(VersionUtils.Version.fromString("3.6.3"))
            .setUsedDiskSpace(0L)
            .setCloudProviders(Arrays.asList(CloudProvider.GCP))
            .setEncryptionDetails(null)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setRequestingUser(null)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setPolicyItemIds(Collections.emptyList())
            .setMainPolicyItemId(null)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(new Date())
            .setGcpSnapshotField(
                new GcpSnapshotFieldBuilder()
                    .withSnapshotName(snapshotName)
                    .withSourceDiskName(instanceHardware.getDiskName().get())
                    .withGcpOrgId(container.getGcpOrganizationId())
                    .withGcpProjectId(container.getGcpProjectId().get())
                    .withRegionName(instanceHardware.getZoneName().get().getRegion().toString())
                    .withSnapshotDiskSizeGb(instanceHardware.getDiskSizeGB().get()));

    _backupSnapshotDao.addBackupSnapshot(snapshotUpdate);

    final GCPOrphanedItem instance =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setZoneName(instanceHardware.getZoneName().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.INSTANCE)
            .setId(instanceHardware.getGCPInstanceName().get())
            .setRegionName(instanceHardware.getZoneName().get().getRegion())
            .setType(Type.INSTANCE)
            .build();

    final GCPOrphanedItem disk =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setZoneName(instanceHardware.getZoneName().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.PERSISTENT_DISK)
            .setId(instanceHardware.getDiskName().get())
            .setRegionName(instanceHardware.getZoneName().get().getRegion())
            .setType(Type.PERSISTENT_DISK)
            .build();

    final GCPOrphanedItem subnet =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.SUBNET)
            .setId(container.getSubnets().get(0).getSubnetName())
            .setRegionName(container.getSubnets().get(0).getRegion())
            .setType(Type.SUBNET)
            .build();

    final GCPOrphanedItem project =
        new Builder()
            .setGcpOrganizationId(container.getGcpOrganizationId())
            .setProjectName(container.getGcpProjectId().get())
            .setDurationToKeep(Duration.ofMinutes(10))
            .setDurationUntilExpire(Expirations.PROJECT)
            .setId(container.getGcpProjectId().get())
            .setType(Type.PROJECT)
            .build();

    getAppSettings()
        .setProp(
            Fields.NDS_GCP_LEAKED_ITEM_DETECTION_MODE.value,
            LeakedItemProcessorMode.JOB_HANDLER.name(),
            AppSettings.SettingType.MEMORY);
    _svc.runDetection();
    waitForJobToComplete(GCPLeakedItemDetectionJobHandler.class);

    final Set<String> createdIds =
        Set.of(instance.getId(), disk.getId(), subnet.getId(), project.getId(), snapshotName);

    {
      final List<GCPLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Since metadata still exists for the created resources, they should not be considered leaked
      assertEquals(List.of(), leakedItems);
    }

    _orphanedItemDao.add(instance);
    _orphanedItemDao.add(disk);
    _orphanedItemDao.add(subnet);
    _orphanedItemDao.add(project);

    // Remove metadata for instance
    _instanceHardwareDao.unsetProvisionedFields(
        replicaSetHardware.getId(), instanceHardware.getInstanceId(), false, CloudProvider.GCP);
    _containerDao.unsetProvisionedFields(group.getGroupId(), container.getId());

    _svc.runDetection();
    waitForJobToComplete(GCPLeakedItemDetectionJobHandler.class);

    {
      final List<GCPLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exists but items are in the orphan queue and have not expired
      assertEquals(List.of(), leakedItems);
    }

    _orphanedItemDao.expireItem(instance.getId(), CloudProvider.GCP, Type.INSTANCE);
    _orphanedItemDao.expireItem(disk.getId(), CloudProvider.GCP, Type.PERSISTENT_DISK);
    _orphanedItemDao.expireItem(subnet.getId(), CloudProvider.GCP, Type.SUBNET);
    _orphanedItemDao.expireItem(project.getId(), CloudProvider.GCP, Type.PROJECT);
    _backupSnapshotDao.remove(new BasicDBObject());

    _svc.runDetection();
    waitForJobToComplete(GCPLeakedItemDetectionJobHandler.class);

    {
      final Set<String> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .map(OrphanedItem::getId)
              .filter(createdIds::contains)
              .collect(Collectors.toSet());

      // Metadata does not exist and the items in the orphan queue have expired, but the cluster
      // is not idle, so cluster level resources will be marked as in use
      assertEquals(Set.of(subnet.getId(), project.getId(), snapshotName), leakedItems);
    }

    _clusterDescriptionDao.setState(group.getGroupId(), cluster.getName(), State.IDLE);
    _svc.runDetection();
    waitForJobToComplete(GCPLeakedItemDetectionJobHandler.class);

    {
      final Set<String> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .map(OrphanedItem::getId)
              .filter(createdIds::contains)
              .collect(Collectors.toSet());

      // Metadata does not exists, items in the orphan queue are expired, the cluster is idle, and
      // the snapshot is no longer in the database.
      assertEquals(createdIds, leakedItems);
    }

    _svc.runDetection();
    waitForJobToComplete(GCPLeakedItemDetectionJobHandler.class);

    {
      final List<GCPLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.GCP)
              .map(GCPLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .toList();

      // Item still exist because it has not been marked for deletion
      assertEquals(
          createdIds, leakedItems.stream().map(OrphanedItem::getId).collect(Collectors.toSet()));
      leakedItems.forEach(i -> assertEquals(Optional.empty(), i.getDeletedDate()));
    }

    getAppSettings()
        .setProp(
            Fields.NDS_GCP_LEAKED_ITEM_DETECTION_MODE.value,
            LeakedItemProcessorMode.PAUSED.name(),
            AppSettings.SettingType.MEMORY);
    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value,
            LeakedItemProcessorMode.JOB_HANDLER.name(),
            AppSettings.SettingType.MEMORY);

    _leakedItemDao.bulkApproveForDeletion(
        List.of(instance.getId()), CloudProvider.GCP, Type.INSTANCE);
    _leakedItemDao.bulkApproveForDeletion(
        List.of(disk.getId()), CloudProvider.GCP, Type.PERSISTENT_DISK);
    _leakedItemDao.bulkApproveForDeletion(List.of(subnet.getId()), CloudProvider.GCP, Type.SUBNET);
    _leakedItemDao.bulkApproveForDeletion(
        List.of(project.getId()), CloudProvider.GCP, Type.PROJECT);
    _leakedItemDao.bulkApproveForDeletion(List.of(snapshotName), CloudProvider.GCP, Type.SNAPSHOT);

    // First call will submit the delete request
    _svc.runCleanupOnly();
    waitForJobToComplete(GCPLeakedItemCleanupJobHandler.class);
    verifyGCPInstanceDeleted(
        instance.getGcpOrganizationId(),
        instance.getProjectName(),
        instance.getZoneName(),
        instance.getId());

    // Second call will confirm that the delete request succeeded and set the date
    _svc.runCleanupOnly();
    waitForJobToComplete(GCPLeakedItemCleanupJobHandler.class);

    final LeakedItem leakedInstance =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, instance.getId(), Type.INSTANCE)
            .get();
    assertNotEquals(Optional.empty(), leakedInstance.getDeletedDate());
    // Disk should also be deleted
    verifyPersistentDiskDeleted(
        disk.getGcpOrganizationId(), disk.getProjectName(), disk.getZoneName(), disk.getId());
    verifySnapshotDeleted(project.getGcpOrganizationId(), project.getId(), snapshotName);
    final LeakedItem leakedSnapshot =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, snapshotName, Type.SNAPSHOT)
            .get();
    assertNotEquals(Optional.empty(), leakedSnapshot.getDeletedDate());

    // Third call will confirm that the data disk is deleted, set its deleted date, and delete the
    // project
    _svc.runCleanupOnly();
    waitForJobToComplete(GCPLeakedItemCleanupJobHandler.class);

    final LeakedItem leakedDisk =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, disk.getId(), Type.PERSISTENT_DISK)
            .get();
    assertNotEquals(Optional.empty(), leakedDisk.getDeletedDate());
    verifyProjectDeleted(project.getGcpOrganizationId(), project.getId());

    final LeakedItem leakedSubnet =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, subnet.getId(), Type.SUBNET)
            .get();
    assertNotEquals(Optional.empty(), leakedSubnet.getDeletedDate());

    final LeakedItem leakedProject =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.GCP, project.getId(), Type.PROJECT)
            .get();
    assertNotEquals(Optional.empty(), leakedProject.getDeletedDate());
    _planNeedsRollback = false;

    // Unset all the dates on the leaked items to test that they get removed on pre-processing
    _leakedItemDao.unsetDeleteDataForTest(instance.getId(), CloudProvider.GCP, Type.INSTANCE);
    _leakedItemDao.unsetDeleteDataForTest(disk.getId(), CloudProvider.GCP, Type.PERSISTENT_DISK);
    _leakedItemDao.unsetDeleteDataForTest(subnet.getId(), CloudProvider.GCP, Type.SUBNET);
    _leakedItemDao.unsetDeleteDataForTest(project.getId(), CloudProvider.GCP, Type.PROJECT);
    _leakedItemDao.unsetDeleteDataForTest(snapshotName, CloudProvider.GCP, Type.SNAPSHOT);

    retryAssertion(
        Duration.ofMinutes(2),
        () -> {
          _svc.runCleanupOnly();
          try {
            waitForJobToComplete(GCPLeakedItemCleanupJobHandler.class);
          } catch (InterruptedException e) {
            fail(e.getMessage());
          }

          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, instance.getId(), Type.INSTANCE));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, disk.getId(), Type.PERSISTENT_DISK));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, subnet.getId(), Type.SUBNET));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, project.getId(), Type.PROJECT));
          assertEquals(
              Optional.empty(),
              _leakedItemDao.findByCloudProviderAndId(
                  CloudProvider.GCP, snapshotName, Type.SNAPSHOT));
        });

    // Cleaning up the orphan items from the dao.  removeItemsIfNotNeeded already verifies
    //  that the resources are there or not so there's no need to orphan them again
    _orphanedItemDao.remove(instance);
    _orphanedItemDao.remove(disk);
    _orphanedItemDao.remove(subnet);
    _orphanedItemDao.remove(project);
  }
}
