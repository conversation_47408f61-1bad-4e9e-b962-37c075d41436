package com.xgen.svc.nds.gcp.planner;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.stream.Collectors;
import org.junit.Test;

public class GCPCreateOSSwapInstanceStepIntTests extends GCPExternalIntTest {

  @Inject private GroupDao _groupDao;

  private void testGCPCreateStepInternal(final GCPStep<?> pStep) {
    waitForStepPerformDone(pStep);
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPCreateOSSwapInstanceStep() {

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final ClusterDescription gcpClusterDescription = getClusterDescription();
    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            gcpClusterDescription.getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    plan.addMove(provisionContainerMove);
    getPlanDao().save(plan);

    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, getContainer().getId(), gcpClusterDescription));

    waitForMovePerformDone(provisionContainerMove);

    final GCPInstanceHardware gcpInstanceHardware =
        (GCPInstanceHardware) replicaSetHardware.getHardware().get(0);

    final String hostname =
        DNSRecordUtil.getLegacyDNSRecordForInstance(
            getNDSGroup(),
            getHardwareDao()
                .findReplicaSetHardwareForInstance(
                    getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0)),
            getClusterDescription(),
            getInstanceIds().get(0),
            NDSSettings.getInstanceDomainName(
                getAppSettings(),
                CloudProvider.GCP,
                getClusterDescription().getHostnameSubdomainLevel()));

    final GCPCreateOSSwapInstanceStep step =
        new GCPCreateOSSwapInstanceStep(
            ((NDSPlanContext) plan.getPlanContext()),
            new State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                2,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            gcpClusterDescription,
            replicaSetHardware,
            gcpInstanceHardware,
            hostname,
            gcpInstanceHardware.getGCPInstanceName().get(),
            GCPZoneName.US_EAST1_B,
            "127.0.0.1", // this fake ip is to satisfy cloud conf config metadata only
            GCPNDSDefaults.SCSI_INTERFACE_DATA_DEVICE_PREFIX + GCPNDSDefaults.DATA_DEVICE_RANGE_MIN,
            false,
            InstanceHostname.HostnameScheme.LEGACY,
            null,
            false,
            getGCPApiSvc(),
            getCloudChefConfSvc(),
            getRemoteImageSvc(),
            getOrphanedItemDao(),
            _groupDao,
            getGCPCapacityDenylistSvc());

    try {
      testGCPCreateStepInternal(step);
    } catch (final Exception e) {
      e.printStackTrace();
    } finally {
      performUnconditionally(() -> waitForStepRollbackDone(step));
      performUnconditionally(() -> waitForMoveRollbackDone(provisionContainerMove));
    }
  }
}
