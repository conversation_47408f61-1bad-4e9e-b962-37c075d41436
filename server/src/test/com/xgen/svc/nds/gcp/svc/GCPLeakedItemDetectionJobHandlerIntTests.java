package com.xgen.svc.nds.gcp.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobHandlerSvc;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.nds.leakeditem._public.model.GCPLeakedItemDetectionState;
import com.xgen.cloud.nds.leakeditem._public.model.LeakedItemDetectionState.FieldDefs;
import com.xgen.cloud.nds.leakeditem._public.svc.LeakedItemDetectionStateSvc;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import jakarta.inject.Inject;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class GCPLeakedItemDetectionJobHandlerIntTests extends GCPExternalIntTest {
  @Inject private GCPCloudProviderItemProcessorSvc _gcpCloudProviderItemProcessorSvc;
  @Inject private JobsProcessorSvc _jobsProcessorSvc;
  @Inject private JobHandlerSvc _jobHandlerSvc;
  @Inject private LeakedItemDetectionStateSvc _leakedItemDetectionStateSvc;
  @Inject private GCPLeakedItemDetectionJobHandler _gcpLeakedItemDetectionJobHandler;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    super.tearDown();
  }

  @Test(timeout = 15 * 60 * 1000)
  public void testJobCompletes() {
    List<Job> jobs = _gcpCloudProviderItemProcessorSvc.generateLeakedItemDetectionJobs();

    assertNotNull(jobs);
    final Job job = jobs.get(0);
    _jobsProcessorSvc.submitJob(jobs.get(0));

    final ObjectId stateId = job.getParameters().getObjectId(FieldDefs.ID);
    GCPLeakedItemDetectionState state =
        (GCPLeakedItemDetectionState) _leakedItemDetectionStateSvc.findById(stateId).get();

    while (state.getCurrentProjectIndex() < state.getProjects().size()) {
      _gcpLeakedItemDetectionJobHandler.handleWork(job.getParameters(), job.getId());

      state = (GCPLeakedItemDetectionState) _leakedItemDetectionStateSvc.findById(stateId).get();
    }

    assertEquals(state.getCurrentProjectIndex(), state.getProjects().size());
    final Job completedJob = _jobHandlerSvc.getJob(job.getId());
    assertEquals(completedJob.getStatus(), Job.Status.COMPLETED);
  }
}
