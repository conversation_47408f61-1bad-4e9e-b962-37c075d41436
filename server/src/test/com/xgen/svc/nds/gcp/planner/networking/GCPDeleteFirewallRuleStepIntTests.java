package com.xgen.svc.nds.gcp.planner.networking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.google.api.services.compute.model.Firewall;
import com.google.api.services.compute.model.Firewall.Allowed;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermissionList;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.GCPReserveProjectStep;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

public class GCPDeleteFirewallRuleStepIntTests extends GCPExternalIntTest {
  @Inject private GroupDao _groupDao;

  public void testGCPDeleteFirewallRuleStepInternal(final Plan pPlan) {

    final NDSPlanContext context = (NDSPlanContext) pPlan.getPlanContext();
    final GCPOrganization gcpOrganization =
        getGCPOrganization(getContainer().getGcpOrganizationId());

    final Firewall firewall = new Firewall();
    firewall.setName(RandomStringUtils.randomAlphabetic(15).toLowerCase());

    // Deleting a non-existent firewall rule should return done.
    final GCPDeleteFirewallRuleStep step =
        new GCPDeleteFirewallRuleStep(
            context,
            new Step.State(
                pPlan.getId(),
                pPlan.getMoves().get(0).getId(),
                1,
                pPlan.getPlanContext().getPlanDao()),
            getContainer(),
            getContainer().getGcpProjectId().get(),
            firewall.getName(),
            getGCPApiSvc());
    assertTrue(step.perform().getStatus().isDone());

    final NDSNetworkPermissionList listOrig =
        new NDSNetworkPermissionList(
            Set.of(
                new NDSNetworkPermission("123.123.123.123/32"),
                new NDSNetworkPermission("10.0.0.0/16"),
                new NDSNetworkPermission("0.0.0.0/0")));
    final List<String> sourceRanges =
        listOrig.getNetworkPermissions().stream()
            .map(NDSNetworkPermission::getValue)
            .collect(Collectors.toList());
    getNDSGroupDao()
        .updateNetworkPermissionList(
            getNDSGroup().getGroupId(),
            getNDSGroup().getNetworkPermissionList().getLastUpdated(),
            listOrig);

    firewall.setNetwork("/global/networks/" + getContainer().getNetworkName().get());
    firewall.setAllowed(
        Collections.singletonList(
            new Allowed().setIPProtocol("tcp").setPorts(Collections.singletonList("80"))));
    firewall.setSourceRanges(sourceRanges);

    // Now ensure firewall rule
    final GCPEnsureFirewallRuleStep step1 =
        new GCPEnsureFirewallRuleStep(
            context,
            new Step.State(
                pPlan.getId(),
                pPlan.getMoves().get(0).getId(),
                2,
                pPlan.getPlanContext().getPlanDao()),
            getContainer(),
            getContainer().getGcpProjectId().get(),
            firewall.getName(),
            firewall,
            getGCPApiSvc(),
            getNDSGroupDao());
    waitForStepPerformDone(step1);

    // Now successfully delete firewall rule
    final GCPDeleteFirewallRuleStep step2 =
        new GCPDeleteFirewallRuleStep(
            context,
            new Step.State(
                pPlan.getId(),
                pPlan.getMoves().get(0).getId(),
                3,
                pPlan.getPlanContext().getPlanDao()),
            getContainer(),
            getContainer().getGcpProjectId().get(),
            firewall.getName(),
            getGCPApiSvc());
    waitForStepPerformDone(step2);

    try {
      getGCPApiSvc()
          .getFirewall(
              gcpOrganization.getId(),
              getContainer().getGcpProjectId().get(),
              pPlan.getPlanContext().getLogger(),
              firewall.getName());
      fail();
    } catch (final GCPApiException e) {
      assertEquals(CommonErrorCode.NOT_FOUND, e.getErrorCode());
    }
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPDeleteFirewallRuleStep() {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final GCPReserveProjectStep gcpCreateProjectStep =
        new GCPReserveProjectStep(
            ((NDSPlanContext) plan.getPlanContext()),
            new State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                1,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            getGCPProjectDao(),
            getGCPProjectCreationSvc(),
            getOrphanedItemDao(),
            getNDSOrphanedItemSvc(),
            _groupDao);
    try {
      waitForStepPerformDone(gcpCreateProjectStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(gcpCreateProjectStep);
      throw t;
    }
    final String projectId = gcpCreateProjectStep.perform().getData().getProjectId();

    final GCPCreateNetworkStep gcpCreateNetworkStep =
        new GCPCreateNetworkStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                2,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            projectId,
            getGCPApiSvc(),
            getNDSOrphanedItemSvc());
    try {
      waitForStepPerformDone(gcpCreateNetworkStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(gcpCreateNetworkStep);
      throw t;
    }
    final String networkName = gcpCreateNetworkStep.perform().getData().getNetworkName();

    getNDSGroupDao()
        .setCloudContainerFields(
            getNDSGroup().getGroupId(),
            getContainer().getId(),
            Arrays.asList(
                Pair.of(GCPCloudProviderContainer.FieldDefs.PROJECT_ID, projectId),
                Pair.of(GCPCloudProviderContainer.FieldDefs.NETWORK_NAME, networkName)));

    try {
      testGCPDeleteFirewallRuleStepInternal(plan);
    } finally {
      waitForStepRollbackDone(gcpCreateProjectStep);
    }
  }
}
