package com.xgen.svc.nds.gcp.planner.networking;

import static org.junit.Assert.assertTrue;

import com.amazonaws.services.route53.model.RRType;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.gcp._public.model.GCPDiskConfiguration;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.gcp._public.util.GCPInstanceUtil;
import com.xgen.cloud.nds.hostname._public.svc.NDSHostnameSvc;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.InstanceHostnameData;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.GCPCreateInstanceStep;
import com.xgen.svc.nds.gcp.planner.GCPCreatePersistentDiskStep;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.SyncDNSRecordStep;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Test;

public class GCPAttachPublicIPStepIntTests extends GCPExternalIntTest {
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private GroupDao _groupDao;

  private void testGCPAttachPublicIPStepInternal(
      final GCPAttachPublicIPStep pStep,
      final GCPRegionName pRegionName,
      final String pStaticIpName)
      throws InterruptedException {

    // Attach public ip
    waitForStepPerformDone(pStep);

    // Make sure the public ip is attached
    verifyStaticIpAttached(pRegionName, pStaticIpName);

    waitForStepRollbackDone(pStep);

    // Make sure the public ip is detached after rollback
    verifyStaticIpDetached(pRegionName, pStaticIpName);

    // Make sure rolling back does nothing
    assertTrue(pStep.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testGCPAttachPublicIPStep() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            List.of(GCPRegionName.CENTRAL_US));
    plan.addMove(provisionContainerMove);
    plan.addMove(new DummyMove());
    plan.addMove(new DummyMove());
    plan.addMove(new DummyMove());
    plan.addMove(new DummyMove());
    plan.addMove(new DummyMove());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    final NDSPlanContext planContext = (NDSPlanContext) plan.getPlanContext();

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
            getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));

    final Triple<Hostnames, String, HostnameScheme> desiredHostnamesForInstance =
        NDSHostnameSvc.getDesiredHostnamesForInstance(
            InstanceHostnameData.forInstance(
                getNDSGroup(),
                replicaSetHardware,
                getClusterDescription(),
                replicaSetHardware.getById(getInstanceIds().get(0)).get().getInstanceId(),
                NDSSettings.getInstanceDomainName(
                    planContext.getAppSettings(),
                    CloudProvider.GCP,
                    getClusterDescription().getHostnameSubdomainLevel())),
            false,
            false);

    final GCPCreateStaticIpStep createStaticIpStep =
        new GCPCreateStaticIpStep(
            planContext,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(1).getId(),
                0,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            GCPZoneName.US_CENTRAL1_A,
            desiredHostnamesForInstance.getMiddle(),
            desiredHostnamesForInstance.getRight(),
            getGCPApiSvc(),
            getOrphanedItemDao(),
            new Date());

    try {
      waitForStepPerformDone(createStaticIpStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createStaticIpStep);
      throw t;
    }
    final GCPCreateStaticIpStep.Data createIpData = createStaticIpStep.perform().getData();

    final List<SyncDNSRecordStep> createDNSRecordStep =
        performAndWaitForDNSSync(
            planContext,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(2).getId(),
                1,
                plan.getPlanContext().getPlanDao()),
            NDSSettings.getRoute53PublicHostedZonesForClusterHost(
                plan.getPlanContext().getAppSettings(),
                CloudProvider.GCP,
                getClusterDescription().getHostnameSubdomainLevel()),
            RRType.A,
            DNSRecordUtil.getLegacyDNSRecordForInstance(
                InstanceHostnameData.forInstance(
                    getNDSGroup(),
                    replicaSetHardware,
                    getClusterDescription(),
                    getInstanceIds().get(0),
                    NDSSettings.getInstanceDomainName(
                        planContext.getAppSettings(),
                        CloudProvider.GCP,
                        getClusterDescription().getHostnameSubdomainLevel()))),
            createIpData.getIpAddress());

    final GCPCreatePersistentDiskStep createDiskStep =
        new GCPCreatePersistentDiskStep(
            planContext,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(3).getId(),
                2,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            getClusterDescription(),
            GCPZoneName.US_CENTRAL1_A,
            desiredHostnamesForInstance.getMiddle(),
            getClusterDescription().getHostnameSchemeForAgents().get(),
            getGCPApiSvc(),
            getOrphanedItemDao(),
            _groupDao,
            new GCPDiskConfiguration(
                new GCPInstanceUtil()
                    .getDiskType(
                        getClusterDescription(), replicaSetHardware, getInstanceIds().get(0)),
                (long) getClusterDescription().getDiskSizeGB()));

    try {
      waitForStepPerformDone(createDiskStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createDiskStep);
      throw t;
    }

    final GCPCreatePersistentDiskStep.Data createDiskData = createDiskStep.perform().getData();

    final GCPCreateInstanceStep createInstanceStep =
        new GCPCreateInstanceStep(
            planContext,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(4).getId(),
                3,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            getClusterDescription(),
            replicaSetHardware,
            replicaSetHardware.getHardware().get(0),
            desiredHostnamesForInstance.getMiddle(),
            "publicHostname",
            GCPZoneName.US_CENTRAL1_A,
            createIpData.getIpAddress(),
            createDiskData.getDiskLink(),
            createDiskData.getDiskName(),
            false,
            getClusterDescription().getHostnameSchemeForAgents().get(),
            null,
            false,
            getGCPApiSvc(),
            getCloudChefConfSvc(),
            getRemoteImageSvc(),
            getOrphanedItemDao(),
            _groupDao,
            getGCPCapacityDenylistSvc());

    try {
      waitForStepPerformDone(createInstanceStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createInstanceStep);
      throw t;
    }

    final GCPCreateInstanceStep.Data createInstanceData = createInstanceStep.perform().getData();

    final GCPDetachPublicIPStep detachPublicIPStep =
        new GCPDetachPublicIPStep(
            planContext,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(5).getId(),
                4,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            createIpData.getIpAddress(),
            createInstanceData.getInstanceName(),
            GCPZoneName.US_CENTRAL1_A,
            getGCPApiSvc());
    try {
      waitForStepPerformDone(detachPublicIPStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(detachPublicIPStep);
      throw t;
    }

    final GCPAttachPublicIPStep step =
        new GCPAttachPublicIPStep(
            planContext,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(6).getId(),
                5,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            createIpData.getIpAddress(),
            createInstanceData.getInstanceName(),
            GCPZoneName.US_CENTRAL1_A,
            getGCPApiSvc());

    try {
      testGCPAttachPublicIPStepInternal(
          step, GCPZoneName.US_CENTRAL1_A.getRegion(), createIpData.getIpName());
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForStepRollbackDone(step));
      throw t;
    } finally {
      waitForStepRollbackDone(createInstanceStep);
      waitForStepRollbackDone(createDiskStep);
      createDNSRecordStep.forEach(this::waitForStepRollbackDone);
      waitForStepRollbackDone(createStaticIpStep);
      waitForMoveRollbackDone(provisionContainerMove);
    }
  }
}
