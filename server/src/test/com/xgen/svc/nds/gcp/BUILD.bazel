load("//server/src/test:rules.bzl", "library_package")

library_package(
    name = "gcp",
    visibility = ["//server/src/test:__subpackages__"],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/brs/web",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/capacity/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/planning/common",
        "//server/src/main/com/xgen/cloud/nds/planning/summary/_public/model",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao/admin",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/planner",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:com_amazonaws_aws_java_sdk_route53",
        "@maven//:com_google_apis_google_api_services_compute",
        "@maven//:com_google_inject_guice",
        "@maven//:junit_junit",
    ],
)
