package com.xgen.svc.nds.gcp.planner;

import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.networking.GCPDeleteFirewallRuleStep;
import com.xgen.svc.nds.gcp.planner.networking.GCPEnsureNetworkPermissionsAppliedMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import java.time.Duration;
import java.util.Collections;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.Test;

public class GCPRestartServerMoveIntTests extends GCPExternalIntTest {

  @Test(timeout = 60 * 60 * 1000L)
  public void testGCPRestartServerMove() throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final ObjectId anyInstanceId = getInstanceIds().get(0);

    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    plan.addMove(provisionContainerMove);

    final Move ensureNetworkPermissionsAppliedMove =
        GCPEnsureNetworkPermissionsAppliedMove.factoryCreate(
            plan.getPlanContext(), getTags(anyInstanceId), getContainer().getId());
    plan.addMove(ensureNetworkPermissionsAppliedMove);
    ensureNetworkPermissionsAppliedMove.addPredecessor(provisionContainerMove);

    final ProcessAutomationConfigPerClusterMove configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);

    getInstanceIds().stream()
        .map(
            id ->
                GCPProvisionMachineMove.factoryCreate(
                    plan.getPlanContext(),
                    getTags(id),
                    getClusterDescription().getName(),
                    id,
                    false))
        .forEach(
            m -> {
              plan.addMove(m);
              m.addPredecessor(provisionContainerMove);
              configMove.addPredecessor(m);
            });

    getPlanDao().save(plan);

    try {
      waitForPlanPerformSuccess(plan);

      // Wait for agent ping so that we have a primary
      Thread.sleep(Duration.ofMinutes(1).toMillis());

      // Find the primary
      final ObjectId instanceId = getInstanceHardwareForPrimary().getInstanceId();

      final Plan restartMongoServerPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

      final GCPRestartServerMove restartMongoServerMove =
          GCPRestartServerMove.factoryCreate(
              restartMongoServerPlan.getPlanContext(),
              getClusterDescription().getName(),
              instanceId);

      restartMongoServerPlan.addMove(restartMongoServerMove);
      getPlanDao().save(restartMongoServerPlan);

      testRestartServerInternal(
          getNDSGroup(), getClusterDescription().getName(), instanceId, restartMongoServerMove);
    } finally {
      // "rollback" the GCPEnsureNetworkPermissionsAppliedMove
      waitForStepPerformDone(
          new GCPDeleteFirewallRuleStep(
              (NDSPlanContext) plan.getPlanContext(),
              new Step.State(
                  plan.getId(),
                  plan.getMoves().get(2).getId(),
                  0,
                  plan.getPlanContext().getPlanDao()),
              getContainer(),
              getContainer().getGcpProjectId().get(),
              String.format(
                  "%s-%s",
                  GCPNDSDefaults.MONGO_FIREWALL_RULE_PREFIX, getContainer().getId().toString()),
              getGCPApiSvc()));

      waitForStepPerformDone(
          new GCPDeleteFirewallRuleStep(
              (NDSPlanContext) plan.getPlanContext(),
              new Step.State(
                  plan.getId(),
                  plan.getMoves().get(2).getId(),
                  1,
                  plan.getPlanContext().getPlanDao()),
              getContainer(),
              getContainer().getGcpProjectId().get(),
              String.format(
                  "%s-%s",
                  GCPNDSDefaults.ACCESSLIST_FIREWALL_RULE_PREFIX,
                  getContainer().getId().toString()),
              getGCPApiSvc()));

      waitForPlanRollbackSuccess(plan);
    }
  }
}
