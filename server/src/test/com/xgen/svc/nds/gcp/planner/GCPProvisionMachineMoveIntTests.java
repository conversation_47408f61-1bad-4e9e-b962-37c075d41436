package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

import com.google.api.services.compute.model.Address;
import com.google.api.services.compute.model.Disk;
import com.google.api.services.compute.model.Instance;
import com.xgen.cloud.agent._public.svc.AgentApiKeySvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.svc.GCPCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPPhysicalZoneId;
import com.xgen.cloud.nds.gcp._public.model.GCPRegion;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPZone;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.security._public.util.ACMEUtils;
import com.xgen.cloud.security._public.model.ACMEOrder;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.networking.GCPDestroyContainerMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.svc.NDSRemoteImageSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.shredzone.acme4j.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GCPProvisionMachineMoveIntTests extends GCPExternalIntTest {
  private static final Logger LOG = LoggerFactory.getLogger(GCPProvisionMachineMoveIntTests.class);

  @Inject private AgentApiKeySvc _agentApiKeySvc;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private NDSRemoteImageSvc _remoteImageSvc;
  @Inject private GCPCapacityDenylistSvc _gcpCapacityDenylistSvc;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    getAppSettings()
        .setProp(
            NDSSettings.Properties.KEEP_ORPHANED_IP_HOURS, "1", AppSettings.SettingType.MEMORY);
  }

  private void verifyACMEOrder(final GCPProvisionMachineMove pMove, final String pInstanceName)
      throws Exception {
    final List<String> domains =
        ACMEUtils.getACMEDomains(
            getNDSGroup(),
            pInstanceName,
            getClusterDescription().getHostnameSchemeForAgents().get(),
            NDSSettings.getInstanceDomainName(
                getAppSettings(), CloudProvider.GCP, SubdomainLevel.GCP),
            getClusterDescription().getDnsPin());
    final ACMEOrder acmeOrder = getNdsACMESvc().getOrder(getAcmeProvider(), domains);
    acmeOrder.rebind(getNdsACMESvc().getLogin(getAcmeProvider()));
    assertTrue(
        ACMEUtils.getAuthorizationsWithDns01Challenges(acmeOrder).stream()
            .allMatch(ac -> ac.getLeft().getStatus().equals(Status.VALID)));
  }

  private void testGCPProvisionMachineMoveInternal(
      final GCPProvisionMachineMove pMove,
      final boolean pIsCapacityAware,
      final GCPZoneName pUnconstrainedZone)
      throws Exception {
    Result<NoData> result = pMove.perform();
    while (!result.getStatus().isDone()) {

      assertNotFailed(pMove.getClass().getSimpleName(), result);

      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
      final GCPInstanceHardware hardware =
          (GCPInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).get();

      assertFalse(hardware.getInstanceSize().isPresent());
      assertFalse(hardware.getDiskSizeGB().isPresent());
      assertFalse(hardware.getHostnameForAgents().isPresent());
      assertFalse(hardware.getStaticIpName().isPresent());
      assertFalse(hardware.isProvisioned());
      assertFalse(hardware.getLastInstanceSizeModifyDate().isPresent());
      assertFalse(hardware.getAppliedOSPolicyVersion().isPresent());

      Thread.sleep(Duration.ofSeconds(5).toMillis());

      result = pMove.perform();
    }

    assertAgentKeysExistForInstance(_agentApiKeySvc, getInstanceIds(), getGroup(), true);

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
            getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final GCPInstanceHardware hardware =
        (GCPInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).get();

    assertTrue(hardware.getInstanceSize().isPresent());
    assertTrue(hardware.getDiskSizeGB().isPresent());
    assertTrue(hardware.getHostnameForAgents().isPresent());
    assertTrue(hardware.getStaticIpName().isPresent());
    assertTrue(hardware.getPublicIP().isPresent());
    assertTrue(hardware.isProvisioned());
    assertTrue(hardware.getLastInstanceSizeModifyDate().isPresent());
    assertTrue(hardware.getAppliedOSPolicyVersion().isPresent());
    assertFalse(
        "should not have acmeProvider field given newDedicatedClusterAllowMixedCerts flag is false",
        hardware.getACMEProvider().isPresent());

    final Address address =
        getGCPApiSvc()
            .getStaticIp(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                hardware.getZoneName().get().getRegion(),
                pMove.getContext().getLogger(),
                hardware.getStaticIpName().get());

    final Disk disk =
        getGCPApiSvc()
            .getDisk(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                hardware.getZoneName().get(),
                pMove.getContext().getLogger(),
                hardware.getDiskName().get());

    final Instance instance =
        getGCPApiSvc()
            .getInstance(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                hardware.getZoneName().get(),
                pMove.getContext().getLogger(),
                hardware.getGCPInstanceName().get());

    final ClusterDescription clusterDescription = getClusterDescription();
    final String dnsRecord =
        DNSRecordUtil.getLegacyDNSRecordForInstance(
            getNDSGroup(),
            _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
                getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0)),
            clusterDescription,
            getInstanceIds().get(0),
            NDSSettings.getInstanceDomainName(
                getAppSettings(),
                clusterDescription.getLegacyProvider(),
                clusterDescription.getHostnameSubdomainLevel()));

    assertEquals(address.getName(), hardware.getStaticIpName().get());
    assertEquals(address.getAddress(), hardware.getPublicIP().get());
    assertEquals(disk.getName(), hardware.getDiskName().get());
    assertEquals(disk.getSizeGb(), Long.valueOf(hardware.getDiskSizeGB().get()));
    assertEquals(instance.getName(), hardware.getGCPInstanceName().get());
    final String[] zoneUrlSplit = instance.getZone().split("/");
    final String zoneName = zoneUrlSplit[zoneUrlSplit.length - 1];
    assertEquals(zoneName, hardware.getZoneName().get().getValue());
    assertNotEquals(pUnconstrainedZone, getGCPRegion().getZones().get(0).getName());
    if (pIsCapacityAware) {
      assertEquals(pUnconstrainedZone, hardware.getZoneName().get());
    } else {
      assertEquals(getGCPRegion().getZones().get(0).getName(), hardware.getZoneName().get());
    }
    assertEquals(dnsRecord, hardware.getHostnameForAgents().get());
    final String imageVersion = _remoteImageSvc.getImageVersion(getGroupId());
    assertEquals(imageVersion, hardware.getAppliedOSPolicyVersion().get());

    verifyResourceLabels(disk.getLabels());
    verifyResourceLabels(instance.getLabels());
    verifyACMEOrder(pMove, instance.getName());

    waitForMoveRollbackDone(pMove);

    verifyPrivateDNSRecordDeleted(dnsRecord);
    verifyPublicDNSRecordDeleted(dnsRecord);
    verifyStaticIpOrphaned(hardware);
    verifyPersistentDiskOrphaned(hardware, null, null);
    verifyGCPInstanceDeleted(hardware);
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublished(getNDSGroup().getGroupId());
    verifyPublicIPAndCIDRsInAuthRestrictions(
        automationConfig, hardware.getPublicIP().get(), getContainer().getAtlasCidr());
  }

  private GCPRegion getGCPRegion() {
    final RegionConfig regionConfig =
        getClusterDescription()
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigForProvider(CloudProvider.GCP);
    return getGCPOrganization(getContainer().getGcpOrganizationId()).getRegions().stream()
        .filter(region -> region.getName().equals(regionConfig.getRegionName()))
        .findFirst()
        .orElseThrow();
  }

  private GCPZoneName registerCapacityDenylistOverrides() {
    final RegionConfig regionConfig =
        getClusterDescription()
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigForProvider(CloudProvider.GCP);
    final GCPHardwareSpec hardwareSpec = (GCPHardwareSpec) regionConfig.getElectableSpecs();
    final List<GCPZone> zones = getGCPRegion().getZones();
    for (final GCPZone zone : zones.subList(0, 2)) {
      _gcpCapacityDenylistSvc.registerCapacityOverride(
          hardwareSpec.getInstanceFamily(),
          hardwareSpec.getInstanceSize(),
          (GCPRegionName) regionConfig.getRegionName(),
          new GCPPhysicalZoneId(zone.getName()),
          CapacityDenyListEntry.Status.CAPACITY_UNAVAILABLE_OVERRIDE,
          Optional.empty());
    }
    return zones.get(2).getName();
  }

  private void testGCPProvisionMachineMove(final boolean pCapacityAware) throws Exception {
    FeatureFlagIntTestUtil.setFeatureFlagToControlled(FeatureFlag.GCP_CAPACITY_AWARE_AZ_SELECTION);
    if (pCapacityAware) {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          getGroup(), null, FeatureFlag.GCP_CAPACITY_AWARE_AZ_SELECTION);
    } else {
      FeatureFlagIntTestUtil.disableFeatureForEntity(
          getGroup(), null, FeatureFlag.GCP_CAPACITY_AWARE_AZ_SELECTION);
    }

    final GCPZoneName unconstrainedZone = registerCapacityDenylistOverrides();
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final Map<String, String> tags = getTags(getInstanceIds().get(0));

    final GCPProvisionContainerMove provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            getClusterDescription().getRegionNames().stream()
                .map(region -> (GCPRegionName) region)
                .collect(Collectors.toList()));
    final GCPProvisionMachineMove provisionMachineMove =
        GCPProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            tags,
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            false);

    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);

    // Need to save plan so we can get state prepared
    getPlanDao().save(plan);

    // Verify that cloud provider and region are added into move tags
    final Optional<Plan> savedPlan = getPlanDao().find(plan.getId());
    assertTrue(savedPlan.isPresent());
    assertEquals(tags, savedPlan.get().getMoveById(provisionMachineMove.getId()).get().getTags());

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      testGCPProvisionMachineMoveInternal(provisionMachineMove, pCapacityAware, unconstrainedZone);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForMoveRollbackDone(provisionMachineMove));
      throw t;
    } finally {
      waitForMoveRollbackDone(provisionContainerMove);
    }
  }

  @Test(timeout = 25 * 60 * 1000L)
  public void testGCPProvisionMachineMove_CapacityUnaware() throws Exception {
    testGCPProvisionMachineMove(false);
  }

  @Test(timeout = 25 * 60 * 1000L)
  public void testGCPProvisionMachineMove_CapacityAware() throws Exception {
    testGCPProvisionMachineMove(true);
  }

  @Test(timeout = 40 * 60 * 1000L)
  public void testRecreateMachine_RetainIPs() {
    testRecreateMachine(true);
  }

  @Test(timeout = 40 * 60 * 1000L)
  public void testRecreateMachine_DoNotRetainIPs() {
    testRecreateMachine(false);
  }

  private void testRecreateMachine(final boolean pTestRetainIPs) {
    if (pTestRetainIPs) {
      final ClusterDescription clusterDescription =
          getClusterDescription().copy().setCreateDate(DateUtils.addDays(new Date(), -60)).build();
      getClusterDescriptionDao().save(clusterDescription);
    }

    final Plan provisionPlan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccess(provisionPlan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(provisionPlan);
      throw t;
    }

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
            getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final GCPInstanceHardware instanceHardware =
        (GCPInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).get();

    final String originalStaticIpName = instanceHardware.getStaticIpName().get();
    final String originalIpAddress = instanceHardware.getPublicIP().get();
    final ObjectId instanceId = getInstanceIds().get(0);

    final Plan recreatePlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

    final Move destroyMachineMove =
        GCPDestroyMachineMove.factoryCreate(
            recreatePlan.getPlanContext(),
            getTags(instanceId),
            getClusterDescription().getName(),
            instanceId,
            false);
    recreatePlan.addMove(destroyMachineMove);

    final Move provisionMachineMove2 =
        GCPProvisionMachineMove.factoryCreate(
            recreatePlan.getPlanContext(),
            getTags(getInstanceIds().get(0)),
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            false);
    provisionMachineMove2.addPredecessor(destroyMachineMove);
    recreatePlan.addMove(provisionMachineMove2);

    getPlanDao().save(recreatePlan);

    try {
      waitForPlanPerformSuccess(recreatePlan);
      final ReplicaSetHardware recreatedHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
      final GCPInstanceHardware recreatedInstance =
          (GCPInstanceHardware) recreatedHardware.getById(getInstanceIds().get(0)).get();

      if (pTestRetainIPs) {
        // Make sure the machine that was re-created using the old public ip
        assertEquals(originalIpAddress, recreatedInstance.getPublicIP().get());
        assertEquals(originalStaticIpName, recreatedInstance.getStaticIpName().get());
      } else {
        // Make sure the machine that was re-created use a new public ip
        assertNotEquals(originalIpAddress, recreatedInstance.getPublicIP().get());
        assertNotEquals(originalStaticIpName, recreatedInstance.getStaticIpName().get());
      }
    } catch (final Exception e) {
      LOG.error(e.getMessage(), e);
    } finally {
      waitForPlanRollbackSuccessNoReset(recreatePlan);

      // clean up container and hardware if needed
      final ReplicaSetHardware orphanedHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
      final GCPInstanceHardware instanceHardwareToClean =
          (GCPInstanceHardware) orphanedHardware.getById(getInstanceIds().get(0)).get();

      final Plan cleanupClusterPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

      if (instanceHardwareToClean.isProvisioned()) {
        final Move cleanupMachineMove =
            GCPDestroyMachineMove.factoryCreate(
                cleanupClusterPlan.getPlanContext(),
                getTags(instanceId),
                getClusterDescription().getName(),
                instanceId,
                false);
        cleanupClusterPlan.addMove(cleanupMachineMove);
      }

      getPlanDao().save(cleanupClusterPlan);
      waitForPlanPerformSuccess(cleanupClusterPlan);

      final GCPCloudProviderContainer container = getContainer();
      if (container.isProvisioned()) {
        final Plan cleanupContainerPlan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
        final Move cleanupContainerMove =
            GCPDestroyContainerMove.factoryCreate(
                cleanupContainerPlan.getPlanContext(), container.getId());
        cleanupContainerPlan.addMove(cleanupContainerMove);
        getPlanDao().save(cleanupContainerPlan);
        waitForPlanPerformSuccess(cleanupContainerPlan);
      }
    }
  }
}
