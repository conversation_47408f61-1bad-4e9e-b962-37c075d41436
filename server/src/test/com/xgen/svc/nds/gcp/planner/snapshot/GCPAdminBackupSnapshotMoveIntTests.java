package com.xgen.svc.nds.gcp.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.gcp._public.model.GCPAdminBackupSnapshot;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob.Type;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class GCPAdminBackupSnapshotMoveIntTests extends GCPExternalIntTest {
  @Before
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testGCPAdminBackupSnapshotMove() throws InterruptedException, SvcException {
    final Plan setupPlan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccess(setupPlan);
      testPerform();
    } finally {
      waitForPlanRollbackSuccess(setupPlan);
    }
  }

  private void testPerform() throws InterruptedException, SvcException {
    final Plan createAdminSnapshotPlan = new Plan(getGroupId(), getNdsPlanContextFactory());
    final ClusterDescription clusterDescription = getClusterDescription();
    final ObjectId instanceId = getInstanceIds().get(0);
    final String hostname = getHostname(clusterDescription, getInstanceIds());

    final AppUser globalAtlasOperatorUser =
        MmsFactory.createApiUserWithGlobalRole(
            "globalAtlasOperatorUser", Role.GLOBAL_ATLAS_OPERATOR);
    try {
      final NDSAdminJob ndsAdminJob =
          getNDSAdminJobDao()
              .create(
                  createAdminSnapshotPlan.getId(),
                  getGroupId(),
                  clusterDescription.getName(),
                  hostname,
                  Type.TAKE_ADMIN_BACKUP_SNAPSHOT);

      getNDSAdminBackupSnapshotSvc()
          .insertAdminBackupSnapshot(
              getGroupId(),
              getClusterDescription().getName(),
              getInstanceHardware(getGroupId(), clusterDescription.getName(), instanceId),
              hostname,
              globalAtlasOperatorUser,
              "comment");

      final Optional<AdminBackupSnapshot> adminBackupSnapshotOpt =
          getNDSAdminBackupSnapshotSvc()
              .findPendingSnapshot(getGroupId(), clusterDescription.getName(), instanceId);
      assertTrue(adminBackupSnapshotOpt.isPresent());
      final GCPAdminBackupSnapshot gcpAdminBackupSnapshot =
          (GCPAdminBackupSnapshot) adminBackupSnapshotOpt.get();

      final GCPAdminBackupSnapshotMove adminBackupSnapshotMove =
          spy(
              GCPAdminBackupSnapshotMove.factoryCreate(
                  createAdminSnapshotPlan.getPlanContext(),
                  clusterDescription.getName(),
                  instanceId,
                  ndsAdminJob.getId()));
      createAdminSnapshotPlan.addMove(adminBackupSnapshotMove);
      getPlanDao().save(createAdminSnapshotPlan);

      Result<?> result = adminBackupSnapshotMove.perform();
      final Optional<NDSAdminJob> ndsAdminJobJustAfterPerform =
          getNDSAdminJobDao().find(ndsAdminJob.getId());
      assertTrue(ndsAdminJobJustAfterPerform.isPresent());
      assertEquals(NDSAdminJob.Status.WORKING, ndsAdminJobJustAfterPerform.get().getStatus());

      while (!result.getStatus().isDone()) {
        assertFalse(result.getStatus().isFailed());
        Thread.sleep(Duration.ofSeconds(5).toMillis());
        result = adminBackupSnapshotMove.perform();
      }

      final Optional<NDSAdminJob> ndsAdminJobAfterPerform =
          getNDSAdminJobDao().find(ndsAdminJob.getId());
      assertTrue(ndsAdminJobAfterPerform.isPresent());
      assertEquals(NDSAdminJob.Status.COMPLETE, ndsAdminJobAfterPerform.get().getStatus());

      final List<AdminBackupSnapshot> snapshots =
          getNDSAdminBackupSnapshotSvc()
              .findNonDeletedSnapshotsForCluster(getGroupId(), clusterDescription.getName());
      assertEquals(1, snapshots.size());

      final GCPAdminBackupSnapshot gcpResult = (GCPAdminBackupSnapshot) snapshots.get(0);
      assertEquals(gcpAdminBackupSnapshot.getId(), gcpResult.getId());
      assertEquals(Status.ACTIVE, gcpResult.getStatus());

      final Optional<NDSOrphanedItem> orphanedItemOpt =
          getOrphanedItemDao()
              .findByIdAndCloudProviderAndType(
                  gcpResult.getCloudProviderSnapshotId(),
                  gcpResult.getCloudProvider(),
                  GCPOrphanedItem.Type.EMERGENCY_SNAPSHOT);
      assertTrue(orphanedItemOpt.isPresent());
      assertEquals(gcpResult.getCloudProviderSnapshotId(), orphanedItemOpt.get().getId());
    } finally {
      waitForPlanRollbackSuccess(createAdminSnapshotPlan);
      final Optional<AdminBackupSnapshot> snapshotAfterRollback =
          getNDSAdminBackupSnapshotSvc()
              .findNonDeletedSnapshot(getGroupId(), clusterDescription.getName(), instanceId);
      assertTrue(snapshotAfterRollback.isPresent());
      assertEquals(Status.FAILED, snapshotAfterRollback.get().getStatus());
    }
  }
}
