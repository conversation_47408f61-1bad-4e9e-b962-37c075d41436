package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Test;

public class GCPAdminRestartServerMoveIntTests extends GCPExternalIntTest {

  @Test(timeout = 55 * 60 * 1000L)
  public void testGCPAdminRestartServerMove() {
    final Plan plan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccess(plan);
      testGCPAdminRestartServerInternal();
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }

  private void testGCPAdminRestartServerInternal() {
    final ClusterDescription clusterDescription = getClusterDescription();

    final Plan plan = new Plan(getGroupId(), getNdsPlanContextFactory());

    final NDSAdminJob ndsAdminJob =
        getNDSAdminJobDao()
            .create(
                plan.getId(),
                clusterDescription.getGroupId(),
                clusterDescription.getName(),
                getHostname(getClusterDescription(), getInstanceIds()),
                NDSAdminJob.Type.RESTART_HOST);

    final ObjectId instanceId = getInstanceIds().get(0);
    final GCPAdminRestartServerMove adminRestartServerMove =
        spy(
            GCPAdminRestartServerMove.factoryCreate(
                plan.getPlanContext(),
                getClusterDescription().getName(),
                instanceId,
                ndsAdminJob.getId()));
    plan.addMove(adminRestartServerMove);
    getPlanDao().save(plan);

    final GCPInstanceHardware gcpInstanceHardware =
        Cluster.getCluster(
                getClusterDescription(),
                getHardwareDao()
                    .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName()))
            .getLiveReplicaSets()
            .stream()
            .flatMap(replicaSetHardware -> replicaSetHardware.getHardware().stream())
            .filter(instanceHardware -> instanceHardware.getInstanceId().equals(instanceId))
            .findFirst()
            .map(GCPInstanceHardware.class::cast)
            .get();

    assertEquals(GCPApiSvc.OperationStatus.RUNNING, getInstanceStatus(gcpInstanceHardware));
    final Optional<NDSAdminJob> ndsAdminJobBeforePerform =
        getNDSAdminJobDao().find(ndsAdminJob.getId());

    assertTrue(ndsAdminJobBeforePerform.isPresent());
    assertEquals(NDSAdminJob.Status.WORKING, ndsAdminJobBeforePerform.get().getStatus());

    waitForMovePerformDone(adminRestartServerMove);

    final Optional<NDSAdminJob> ndsAdminJobAfterPerform =
        getNDSAdminJobDao().find(ndsAdminJob.getId());
    assertTrue(ndsAdminJobAfterPerform.isPresent());
    assertEquals(NDSAdminJob.Status.COMPLETE, ndsAdminJobAfterPerform.get().getStatus());

    assertEquals(GCPApiSvc.OperationStatus.RUNNING, getInstanceStatus(gcpInstanceHardware));
  }
}
