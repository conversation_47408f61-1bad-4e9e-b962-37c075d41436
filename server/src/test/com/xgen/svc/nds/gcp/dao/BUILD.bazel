load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/metering",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
    ],
)
