package com.xgen.svc.nds.gcp.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.spy;

import com.google.api.services.compute.model.Snapshot;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.GCPBackupSnapshot;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.CpsTestUtils;
import com.xgen.svc.nds.dao.BackupSnapshotDaoForTestExt;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.planner.BackupSnapshotUtils;
import com.xgen.svc.nds.planner.snapshot.CpsReplSetSnapshotMove;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GCPCreateBackupSnapshotMoveIntTests extends GCPExternalIntTest {

  private static final Logger LOG =
      LoggerFactory.getLogger(GCPCreateBackupSnapshotMoveIntTests.class);

  @Inject private BackupSnapshotDaoForTestExt _backupSnapshotDaoForTestExt;
  @Inject private BackupJobDao _backupJobDao;
  @Inject private CpsPolicySvc _cpsPolicySvc;

  private static int _purgedSnapshotsExpected = 0;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _purgedSnapshotsExpected = 0;
  }

  @Test(timeout = 45 * 60 * 1000L)
  public void testGCPCreateBackupSnapshotMove() throws Exception {
    final Plan plan = setupPlanWithContainerAndInstances();

    try {
      waitForPlanPerformSuccess(plan);
      final CpsReplSetSnapshotMove firstSnapshotMove = prepareMoveForSnapshotCreate();

      final ClusterDescription clusterDescription = getClusterDescription();
      final ObjectId backupJobId =
          _cpsPolicySvc.createBackupJobForTesting(
              clusterDescription.getGroupId(),
              clusterDescription.getName(),
              clusterDescription.getUniqueId(),
              BackupJob.ClusterType.REPLICA_SET,
              2,
              false,
              false);

      waitforMonitoringData();

      // add an GCP Snapshot to be picked up
      final BackupJob backupJob = _backupJobDao.find(backupJobId).get();
      BackupSnapshotUtils.queueScheduledSnapshot(
          new Date(),
          getClusterDescription(),
          backupJob,
          _backupSnapshotDaoForTestExt,
          false,
          false);

      waitForMovePerformDone(firstSnapshotMove);

      assertTrue(backupJob.getDiskBackupState() == BackupJob.DiskBackupState.ACTIVE);

      final boolean deleted = false;
      final List<BackupSnapshot> afterfirstSnapshots =
          _backupSnapshotDaoForTestExt.findCompletedByCluster(
              getClusterDescription().getGroupId(), getClusterDescription().getUniqueId(), deleted);
      assertEquals(1, afterfirstSnapshots.size());

      final GCPBackupSnapshot gcpBackupSnapshot = (GCPBackupSnapshot) afterfirstSnapshots.get(0);
      final Snapshot firstSnapshot =
          findgcpSnapshot(gcpBackupSnapshot.getName(), firstSnapshotMove.getContext().getLogger());

      assertEquals(gcpBackupSnapshot.getName(), firstSnapshot.getName());
      CpsTestUtils.verifyCompletedBackupSnapshotMetadata(backupJob, gcpBackupSnapshot);

      LOG.info("Passed the test case. Now going to test rollback");

      testMoveRollback(firstSnapshotMove);
    } catch (SvcException e) {
      e.printStackTrace();
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }

  private Snapshot findgcpSnapshot(final String pSnapshotName, final Logger pLogger) {
    return getGCPApiSvc()
        .findSnapshot(
            getContainer().getGcpOrganizationId(),
            getContainer().getGcpProjectId().get(),
            pSnapshotName,
            pLogger);
  }

  protected void testMoveRollback(final CpsReplSetSnapshotMove pMove) {
    waitForMoveRollbackDone(pMove);
    _purgedSnapshotsExpected++;

    final List<BackupSnapshot> snapshotsPurged =
        _backupSnapshotDaoForTestExt.findPurgedByCluster(
            pMove.getContext().getGroupId(), getClusterDescription().getName());

    assertEquals(
        "There should be a purged snapshot in db because of the move rollback",
        _purgedSnapshotsExpected,
        snapshotsPurged.size());
  }

  protected CpsReplSetSnapshotMove prepareMoveForSnapshotCreate() {
    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final CpsReplSetSnapshotMove snapshotMove =
        spy(
            CpsReplSetSnapshotMove.factoryCreate(
                plan.getPlanContext(), getClusterDescription().getName()));

    plan.addMove(snapshotMove);
    getPlanDao().save(plan);
    return snapshotMove;
  }
}
