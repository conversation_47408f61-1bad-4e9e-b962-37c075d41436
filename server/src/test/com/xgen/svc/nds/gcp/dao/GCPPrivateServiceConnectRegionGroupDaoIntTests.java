package com.xgen.svc.nds.gcp.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectRegionGroupDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPConsumerForwardingRule;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectEndpointGroup;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectRegionGroup;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroup.FieldDefs;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.CloudProviderContainerTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class GCPPrivateServiceConnectRegionGroupDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private GCPPrivateServiceConnectRegionGroupDao _gcpPrivateServiceConnectRegionGroupDao;

  private ObjectId _groupId;
  private ObjectId _containerId;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _ndsGroupDao.ensureIndexes();
    final Pair<ObjectId, ObjectId> pair =
        CloudProviderContainerTestUtils.setupGroupWithSingleGCPContainer(
            _ndsGroupDao, _ndsGroupSvc);
    _groupId = pair.getLeft();
    _containerId = pair.getRight();
  }

  @Test
  public void testInitializePrivateEndpointService() {
    final GCPPrivateServiceConnectRegionGroup regionGroup_centralUS =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    {
      assertTrue(
          _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
              regionGroup_centralUS, _groupId, _containerId));

      final BasicDBObject doc = (BasicDBObject) _ndsGroupDao.getDbCollection().findOne();
      final BasicDBObject container =
          (BasicDBObject) ((BasicDBList) doc.get(FieldDefs.CLOUD_PROVIDER_CONTAINERS)).get(0);
      final BasicDBList regionGroups =
          (BasicDBList) container.get(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS);
      final BasicDBObject regionGroup = (BasicDBObject) regionGroups.get(0);
      assertNotNull(regionGroup);
      assertEquals(regionGroup_centralUS, new GCPPrivateServiceConnectRegionGroup(regionGroup));
    }

    // fails if a private endpoint service already exists for this region, but NAT CIDR is different
    final GCPPrivateServiceConnectRegionGroup regionGroup_centralUS_diffCidr =
        new GCPPrivateServiceConnectRegionGroup(
            NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup()
                .toDBObject()
                .append("natCIDRBlock", NDSModelTestFactory.getGCPPSCRegionGroupCIDRBlock(30, 60)));
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup_centralUS_diffCidr, _groupId, _containerId));

    // fails if a private endpoint service with same NAT CIDR already exists
    final GCPPrivateServiceConnectRegionGroup regionGroup_usWest2_sameNAT =
        new GCPPrivateServiceConnectRegionGroup(
            NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup(
                    GCPRegionName.NORTH_AMERICA_NORTHEAST_1, Status.AVAILABLE)
                .toDBObject()
                .append("natCIDRBlock", regionGroup_centralUS.getNatCIDRBlock().get()));
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup_usWest2_sameNAT, _groupId, _containerId));

    // can initialize another endpoint service in diff region and diff CIDR
    final GCPPrivateServiceConnectRegionGroup regionGroup_northamericaNortheast1 =
        new GCPPrivateServiceConnectRegionGroup(
            NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup(
                    GCPRegionName.NORTH_AMERICA_NORTHEAST_1, Status.AVAILABLE)
                .toDBObject()
                .append("natCIDRBlock", NDSModelTestFactory.getGCPPSCRegionGroupCIDRBlock(30, 60)));
    {
      assertTrue(
          _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
              regionGroup_northamericaNortheast1, _groupId, _containerId));
      final BasicDBObject doc = (BasicDBObject) _ndsGroupDao.getDbCollection().findOne();
      final BasicDBObject container =
          (BasicDBObject) ((BasicDBList) doc.get(FieldDefs.CLOUD_PROVIDER_CONTAINERS)).get(0);
      final BasicDBList regionGroups =
          (BasicDBList) container.get(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS);
      final BasicDBObject regionGroup = (BasicDBObject) regionGroups.get(1);
      assertNotNull(regionGroup);
      assertEquals(
          regionGroup_northamericaNortheast1, new GCPPrivateServiceConnectRegionGroup(regionGroup));
    }
  }

  @Test
  public void testSetNeedsUpdateAfter() {
    // container doesn't exist
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, ObjectId.get(), ObjectId.get(), new Date()));

    // container no private service connections yet
    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new GCPCloudProviderContainer(NDSModelTestFactory.getGCPContainer()));
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, containerId, ObjectId.get(), new Date()));

    // container has one private service connection
    final GCPPrivateServiceConnectRegionGroup regionGroup1 =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup(
            GCPRegionName.CENTRAL_US, Status.AVAILABLE);
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup1, _groupId, containerId);
    final Date needsUpdateAfterDate1 = new Date();
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, containerId, regionGroup1.getId(), needsUpdateAfterDate1));
    {
      final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
      final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
          group
              .getCloudProviderContainer(containerId)
              .map(GCPCloudProviderContainer.class::cast)
              .map(GCPCloudProviderContainer::getEndpointServices)
              .orElseThrow();
      final GCPPrivateServiceConnectRegionGroup foundRegionGroup1 =
          regionGroups.stream()
              .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
              .findFirst()
              .get();
      assertEquals(needsUpdateAfterDate1, foundRegionGroup1.getNeedsUpdateAfter().get());
    }

    // container has multiple private service connections
    final GCPPrivateServiceConnectRegionGroup regionGroup2 =
        new GCPPrivateServiceConnectRegionGroup(
            NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup(
                    GCPRegionName.NORTH_AMERICA_NORTHEAST_1, Status.AVAILABLE)
                .toDBObject()
                .append("natCIDRBlock", NDSModelTestFactory.getGCPPSCRegionGroupCIDRBlock(30, 60)));
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup2, _groupId, containerId);

    final Date needsUpdateAfterDate2 = new Date();
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, containerId, regionGroup2.getId(), needsUpdateAfterDate2));

    {
      final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
      final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
          group
              .getCloudProviderContainer(containerId)
              .map(GCPCloudProviderContainer.class::cast)
              .map(GCPCloudProviderContainer::getEndpointServices)
              .orElseThrow();
      final GCPPrivateServiceConnectRegionGroup foundRegionGroup2 =
          regionGroups.stream()
              .filter(
                  rg -> rg.getRegionName().get().equals(GCPRegionName.NORTH_AMERICA_NORTHEAST_1))
              .findFirst()
              .get();
      assertEquals(needsUpdateAfterDate2, foundRegionGroup2.getNeedsUpdateAfter().get());
    }

    // set a new date
    final Date needsUpdateAfterDate3 = new Date();
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, containerId, regionGroup1.getId(), needsUpdateAfterDate3));
    {
      final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
      final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
          group
              .getCloudProviderContainer(containerId)
              .map(GCPCloudProviderContainer.class::cast)
              .map(GCPCloudProviderContainer::getEndpointServices)
              .orElseThrow();
      final GCPPrivateServiceConnectRegionGroup foundRegionGroup3 =
          regionGroups.stream()
              .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
              .findFirst()
              .get();
      assertEquals(needsUpdateAfterDate3, foundRegionGroup3.getNeedsUpdateAfter().get());
    }
  }

  @Test
  public void testTryRequestDeletePrivateLink() {
    // container doesn't exist
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.tryRequestDeleteEndpointService(
            _groupId, ObjectId.get(), ObjectId.get()));

    // container no private service connections yet
    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new GCPCloudProviderContainer(NDSModelTestFactory.getGCPContainer()));

    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.tryRequestDeleteEndpointService(
            _groupId, containerId, ObjectId.get()));

    final GCPPrivateServiceConnectRegionGroup regionGroup1 =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup(
            GCPRegionName.CENTRAL_US,
            Status.AVAILABLE,
            false,
            List.of(
                NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                    "endpointGroup",
                    "project",
                    List.of(
                        NDSModelTestFactory.getGCPConsumerForwardingRule(
                            new ObjectId(), "fr", 0, "1.1.1.1")),
                    0)),
            1);
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup1, _groupId, containerId));

    // Cannot delete a region group that still has endpoint groups
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.tryRequestDeleteEndpointService(
            _groupId, containerId, regionGroup1.getId()));

    _gcpPrivateServiceConnectRegionGroupDao.deleteEndpointGroup(
        _groupId, containerId, regionGroup1.getId(), "endpointGroup");
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.tryRequestDeleteEndpointService(
            _groupId, containerId, regionGroup1.getId()));
  }

  @Test
  public void testUnsetNeedsUpdateAfter() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);

    final Date needsUpdateAfterDate = new Date();
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, _containerId, regionGroup.getId(), needsUpdateAfterDate));

    // incorrect date will not unset
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao.unsetNeedsUpdateAfter(
            _groupId, _containerId, regionGroup.getId(), new Date(0)));

    // correct date will unset
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.unsetNeedsUpdateAfter(
            _groupId, _containerId, regionGroup.getId(), needsUpdateAfterDate));

    final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
        group
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroup =
        regionGroups.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    assertTrue(foundRegionGroup.getNeedsUpdateAfter().isEmpty());
  }

  @Test
  public void testAddPrivateEndpointGroupToExistingPrivateEndpointService() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 3, "testForwardingRule");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "endpointGroupName", "gcpProjectId", forwardingRules, 0);

    // container doesn't exist
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, ObjectId.get(), ObjectId.get(), endpointGroup));

    // no private service connections yet
    assertFalse(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, _containerId, ObjectId.get(), endpointGroup));

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup, _groupId, _containerId));
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, _containerId, regionGroup.getId(), endpointGroup));

    final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
        group
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectEndpointGroup foundEndpointGroup =
        regionGroups.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get()
            .getGCPEndpointGroups()
            .stream()
            .findFirst()
            .get();

    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.INITIATING, foundEndpointGroup.getStatus());
    assertEquals(endpointGroup, foundEndpointGroup);
  }

  @Test
  public void testDeleteEndpointGroup() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 3, "testForwardingRule");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "endpointGroupName", "gcpProjectId", forwardingRules, 0);

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup, _groupId, _containerId));
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, _containerId, regionGroup.getId(), endpointGroup));

    final NDSGroup groupAfterCreate = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfterCreate =
        groupAfterCreate
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectEndpointGroup foundEndpointGroupAfterCreate =
        regionGroupsAfterCreate.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get()
            .getGCPEndpointGroups()
            .stream()
            .findFirst()
            .get();

    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.INITIATING,
        foundEndpointGroupAfterCreate.getStatus());
    assertEquals(endpointGroup, foundEndpointGroupAfterCreate);

    _gcpPrivateServiceConnectRegionGroupDao.deleteEndpointGroup(
        _groupId, _containerId, regionGroup.getId(), endpointGroup.getEndpointGroupName());

    final NDSGroup groupAfterDelete = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfterDelete =
        groupAfterDelete
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();

    final Optional<GCPPrivateServiceConnectEndpointGroup> foundEndpointGroupAfterDelete =
        regionGroupsAfterDelete.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get()
            .getGCPEndpointGroups()
            .stream()
            .findFirst();

    assertTrue(foundEndpointGroupAfterDelete.isEmpty());
  }

  @Test
  public void testMarkRegionGroupProvisioningCompleted() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);

    final Date needsUpdateAfterDate = new Date();
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setNeedsUpdateAfter(
            _groupId, _containerId, regionGroup.getId(), needsUpdateAfterDate));

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.markRegionGroupProvisioningCompleted(
            _groupId, _containerId, regionGroup.getId(), needsUpdateAfterDate));

    final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
        group
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroup =
        regionGroups.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();

    assertEquals(Status.AVAILABLE, foundRegionGroup.getStatus());
    assertTrue(foundRegionGroup.getNeedsUpdateAfter().isEmpty());
  }

  @Test
  public void testSetRegionGroupErrorMessage() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setRegionGroupErrorMessage(
            _groupId, _containerId, regionGroup.getId(), "nope"));

    final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
        group
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroup =
        regionGroups.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();

    assertEquals(Status.FAILED, foundRegionGroup.getStatus());
    assertTrue(foundRegionGroup.getErrorMessage().isPresent());
    assertEquals("nope", foundRegionGroup.getErrorMessage().get());
  }

  @Test
  public void testSetPrivateEndpointServiceStatus() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setPrivateEndpointServiceStatus(
            _groupId, _containerId, regionGroup.getId(), Status.FAILED));

    final NDSGroup group = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroups =
        group
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroup =
        regionGroups.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();

    assertEquals(Status.FAILED, foundRegionGroup.getStatus());
  }

  private List<GCPPrivateServiceConnectRegionGroup> fetchPrivateServiceConnectRegionGroups() {
    return _ndsGroupDao
        .find(_groupId)
        .get()
        .getCloudProviderContainer(_containerId)
        .get()
        .getEndpointServices()
        .stream()
        .map(GCPPrivateServiceConnectRegionGroup.class::cast)
        .collect(Collectors.toList());
  }

  @Test
  public void testRemovePrivateEndpointGroupFromExistingPrivateEndpointService() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "testForwardingRule");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "endpointGroupName", "gcpProjectId", forwardingRules, 0);

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup, _groupId, _containerId));
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, _containerId, regionGroup.getId(), endpointGroup));

    final GCPPrivateServiceConnectRegionGroup fetchedRegionGroup1 =
        fetchPrivateServiceConnectRegionGroups().get(0);

    assertFalse(fetchedRegionGroup1.getGCPEndpointGroups().get(0).isDeleteRequested());

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.setPrivateEndpointGroupDeleteRequested(
            _groupId, _containerId, regionGroup.getId(), endpointGroup.getEndpointGroupName()));

    final GCPPrivateServiceConnectRegionGroup fetchedRegionGroup2 =
        fetchPrivateServiceConnectRegionGroups().get(0);

    assertTrue(fetchedRegionGroup2.getGCPEndpointGroups().get(0).isDeleteRequested());
  }

  @Test
  public void testResetFailedPrivateEndpointGroup() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();

    final List<GCPConsumerForwardingRule> forwardingRules1 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpointGroupName1");
    final List<GCPConsumerForwardingRule> forwardingRules2 =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "endpointGroupName2");

    final GCPPrivateServiceConnectEndpointGroup endpointGroup1 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpointGroupName1", "gcpProjectId1", forwardingRules1, 0)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();
    final GCPPrivateServiceConnectEndpointGroup endpointGroup2 =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
                "endpointGroupName2", "gcpProjectId2", forwardingRules2, 1)
            .copy()
            .setStatus(GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE)
            .build();

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
            regionGroup, _groupId, _containerId));

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, _containerId, regionGroup.getId(), endpointGroup1));
    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao
            .addPrivateEndpointGroupToExistingPrivateEndpointService(
                _groupId, _containerId, regionGroup.getId(), endpointGroup2));

    _gcpPrivateServiceConnectRegionGroupDao.setEndpointGroupErrorMessage(
        _groupId, _containerId, regionGroup.getId(), endpointGroup2.getEndpointGroupName(), "nope");

    final GCPPrivateServiceConnectRegionGroup regionGroupWithEndpointGroupError =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .orElseThrow()
            .getPscRegionGroups()
            .get(0);

    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE,
        regionGroupWithEndpointGroupError.getGCPEndpointGroups().get(0).getStatus());
    assertTrue(
        regionGroupWithEndpointGroupError
            .getGCPEndpointGroups()
            .get(0)
            .getErrorMessage()
            .isEmpty());
    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.FAILED,
        regionGroupWithEndpointGroupError.getGCPEndpointGroups().get(1).getStatus());
    assertEquals(
        "nope",
        regionGroupWithEndpointGroupError.getGCPEndpointGroups().get(1).getErrorMessage().get());

    final List<GCPConsumerForwardingRule> forwardingRules2Fixed =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            regionGroup.getId(), 50, "newEndpointGroupName2");

    _gcpPrivateServiceConnectRegionGroupDao.resetFailedPrivateEndpointGroup(
        _groupId,
        _containerId,
        regionGroup.getId(),
        endpointGroup2.getId(),
        "newEndpointGroupName2",
        "gcpProjectId2",
        forwardingRules2Fixed);

    // calling this on an AVAILABLE endpoint group should do nothing
    _gcpPrivateServiceConnectRegionGroupDao.resetFailedPrivateEndpointGroup(
        _groupId,
        _containerId,
        regionGroup.getId(),
        endpointGroup1.getId(),
        "newEndpointGroupName2",
        "gcpProjectId2",
        forwardingRules2Fixed);

    final GCPPrivateServiceConnectRegionGroup regionGroupAfterPatch =
        _ndsGroupDao
            .find(_groupId)
            .orElseThrow()
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .orElseThrow()
            .getPscRegionGroups()
            .get(0);

    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE,
        regionGroupAfterPatch.getGCPEndpointGroups().get(0).getStatus());
    assertEquals(
        "gcpProjectId1",
        regionGroupAfterPatch.getGCPEndpointGroups().get(0).getCustomerGCPProjectId());
    assertTrue(regionGroupAfterPatch.getGCPEndpointGroups().get(0).getErrorMessage().isEmpty());
    final GCPPrivateServiceConnectEndpointGroup patchedEndpointGroup =
        regionGroupAfterPatch.getGCPEndpointGroups().get(1);
    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.INITIATING, patchedEndpointGroup.getStatus());
    assertTrue(patchedEndpointGroup.getErrorMessage().isEmpty());
    assertEquals("newEndpointGroupName2", patchedEndpointGroup.getEndpointGroupName());
    assertEquals("gcpProjectId2", patchedEndpointGroup.getCustomerGCPProjectId());
    assertEquals(1, patchedEndpointGroup.getIndex());
    assertEquals(50, patchedEndpointGroup.getForwardingRules().size());
    assertEquals(
        "newEndpointGroupName2-0",
        patchedEndpointGroup.getForwardingRules().get(0).getEndpointId());
  }

  @Test
  public void testUpdateEndpointGroupStatus() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            ObjectId.get(), 50, "testForwardingRule");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "melodrama-endpoints", "melodrama", forwardingRules, 0);
    _gcpPrivateServiceConnectRegionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _groupId, _containerId, regionGroup.getId(), endpointGroup);

    final NDSGroup groupBefore = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsBefore =
        groupBefore
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupBefore =
        regionGroupsBefore.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.INITIATING,
        foundRegionGroupBefore.getGCPEndpointGroups().get(0).getStatus());

    _gcpPrivateServiceConnectRegionGroupDao.updateEndpointGroupStatus(
        _groupId,
        _containerId,
        regionGroup.getId(),
        "melodrama-endpoints",
        GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE);

    final NDSGroup groupAfter = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfter =
        groupAfter
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupAfter =
        regionGroupsAfter.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE,
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getStatus());
  }

  @Test
  public void testSetEndpointGroupErrorMessage() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            ObjectId.get(), 50, "testForwardingRule");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "melodrama-endpoints", "melodrama", forwardingRules, 0);
    _gcpPrivateServiceConnectRegionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _groupId, _containerId, regionGroup.getId(), endpointGroup);

    _gcpPrivateServiceConnectRegionGroupDao.setEndpointGroupErrorMessage(
        _groupId,
        _containerId,
        regionGroup.getId(),
        "melodrama-endpoints",
        "it's just a supercut of us");

    final NDSGroup groupAfter = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfter =
        groupAfter
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupAfter =
        regionGroupsAfter.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    assertEquals(
        GCPPrivateServiceConnectEndpointGroup.Status.FAILED,
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getStatus());
    assertTrue(foundRegionGroupAfter.getGCPEndpointGroups().get(0).getErrorMessage().isPresent());
    assertEquals(
        "it's just a supercut of us",
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getErrorMessage().get());
  }

  @Test
  public void testSetForwardingRuleAvailable() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            ObjectId.get(), 50, "melodrama-endpoints");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "melodrama-endpoints", "melodrama", forwardingRules, 0);
    _gcpPrivateServiceConnectRegionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _groupId, _containerId, regionGroup.getId(), endpointGroup);

    final NDSGroup groupBefore = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsBefore =
        groupBefore
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupBefore =
        regionGroupsBefore.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    assertTrue(
        foundRegionGroupBefore.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .noneMatch(fr -> fr.getPrivateEndpointGCPProviderHostname() != null));

    final boolean result =
        _gcpPrivateServiceConnectRegionGroupDao.setForwardingRuleAvailable(
            _groupId,
            _containerId,
            regionGroup.getId(),
            "melodrama-endpoints",
            "melodrama-endpoints-2",
            "the-louvre.gcp.mongodb.net",
            "the-louvre.mongodb.net");

    assertTrue(result);

    final NDSGroup groupAfter = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfter =
        groupAfter
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupAfter =
        regionGroupsAfter.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    final Optional<GCPConsumerForwardingRule> foundForwardingRule =
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .filter(fr -> fr.getEndpointId().equals("melodrama-endpoints-2"))
            .findFirst();

    assertTrue(foundForwardingRule.isPresent());
    assertEquals(
        "the-louvre.mongodb.net", foundForwardingRule.get().getPrivateEndpointMongoDBHostname());
    assertEquals(
        "the-louvre.gcp.mongodb.net",
        foundForwardingRule.get().getPrivateEndpointGCPProviderHostname());
    assertEquals(GCPConsumerForwardingRule.Status.AVAILABLE, foundForwardingRule.get().getStatus());

    final Optional<GCPConsumerForwardingRule> foundUnchangedForwardingRule =
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .filter(fr -> fr.getEndpointId().equals("melodrama-endpoints-3"))
            .findFirst();

    assertTrue(foundUnchangedForwardingRule.isPresent());
    assertNull(foundUnchangedForwardingRule.get().getPrivateEndpointMongoDBHostname());
    assertNull(foundUnchangedForwardingRule.get().getPrivateEndpointGCPProviderHostname());
    assertEquals(
        GCPConsumerForwardingRule.Status.INITIATING,
        foundUnchangedForwardingRule.get().getStatus());
  }

  @Test
  public void testSetForwardingRuleStatus() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            ObjectId.get(), 50, "melodrama-endpoints");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "melodrama-endpoints", "melodrama", forwardingRules, 0);
    _gcpPrivateServiceConnectRegionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _groupId, _containerId, regionGroup.getId(), endpointGroup);

    final NDSGroup groupBefore = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsBefore =
        groupBefore
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupBefore =
        regionGroupsBefore.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    assertTrue(
        foundRegionGroupBefore.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .noneMatch(fr -> fr.getPrivateEndpointGCPProviderHostname() != null));

    final boolean result =
        _gcpPrivateServiceConnectRegionGroupDao.setForwardingRuleStatus(
            _groupId,
            _containerId,
            regionGroup.getId(),
            "melodrama-endpoints",
            "melodrama-endpoints-2",
            GCPConsumerForwardingRule.Status.DELETED);

    assertTrue(result);

    final NDSGroup groupAfter = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfter =
        groupAfter
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupAfter =
        regionGroupsAfter.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    final Optional<GCPConsumerForwardingRule> foundForwardingRule =
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .filter(fr -> fr.getEndpointId().equals("melodrama-endpoints-2"))
            .findFirst();

    assertTrue(foundForwardingRule.isPresent());
    assertNull(foundForwardingRule.get().getPrivateEndpointMongoDBHostname());
    assertNull(foundForwardingRule.get().getPrivateEndpointGCPProviderHostname());
    assertEquals(GCPConsumerForwardingRule.Status.DELETED, foundForwardingRule.get().getStatus());

    final Optional<GCPConsumerForwardingRule> foundUnchangedForwardingRule =
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .filter(fr -> fr.getEndpointId().equals("melodrama-endpoints-3"))
            .findFirst();

    assertTrue(foundUnchangedForwardingRule.isPresent());
    assertNull(foundUnchangedForwardingRule.get().getPrivateEndpointMongoDBHostname());
    assertNull(foundUnchangedForwardingRule.get().getPrivateEndpointGCPProviderHostname());
    assertEquals(
        GCPConsumerForwardingRule.Status.INITIATING,
        foundUnchangedForwardingRule.get().getStatus());
  }

  @Test
  public void testSetPrivateServiceConnectionIdOnForwardingRule() {
    final GCPPrivateServiceConnectRegionGroup regionGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();
    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup, _groupId, _containerId);
    final List<GCPConsumerForwardingRule> forwardingRules =
        NDSModelTestFactory.getGCPConsumerForwardingRulesForPrivateServiceConnection(
            ObjectId.get(), 50, "melodrama-endpoints");
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        NDSModelTestFactory.getGCPPrivateServiceConnectEndpointGroup(
            "melodrama-endpoints", "melodrama", forwardingRules, 0);
    _gcpPrivateServiceConnectRegionGroupDao.addPrivateEndpointGroupToExistingPrivateEndpointService(
        _groupId, _containerId, regionGroup.getId(), endpointGroup);

    final NDSGroup groupBefore = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsBefore =
        groupBefore
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupBefore =
        regionGroupsBefore.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();

    final ObjectId pscId = new ObjectId();

    assertTrue(
        foundRegionGroupBefore.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .noneMatch(fr -> fr.getPrivateServiceConnectionId() == pscId));

    final boolean result =
        _gcpPrivateServiceConnectRegionGroupDao.setPrivateServiceConnectionIdOnForwardingRule(
            _groupId,
            _containerId,
            regionGroup.getId(),
            "melodrama-endpoints",
            forwardingRules.get(2).getId(),
            pscId);

    assertTrue(result);

    final NDSGroup groupAfter = _ndsGroupDao.find(_groupId).orElseThrow();
    final List<GCPPrivateServiceConnectRegionGroup> regionGroupsAfter =
        groupAfter
            .getCloudProviderContainer(_containerId)
            .map(GCPCloudProviderContainer.class::cast)
            .map(GCPCloudProviderContainer::getEndpointServices)
            .orElseThrow();
    final GCPPrivateServiceConnectRegionGroup foundRegionGroupAfter =
        regionGroupsAfter.stream()
            .filter(rg -> rg.getRegionName().get().equals(GCPRegionName.CENTRAL_US))
            .findFirst()
            .get();
    final Optional<GCPConsumerForwardingRule> foundForwardingRule =
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .filter(fr -> fr.getId().equals(forwardingRules.get(2).getId()))
            .findFirst();

    assertTrue(foundForwardingRule.isPresent());
    assertEquals(pscId, foundForwardingRule.get().getPrivateServiceConnectionId());

    final Optional<GCPConsumerForwardingRule> foundUnchangedForwardingRule =
        foundRegionGroupAfter.getGCPEndpointGroups().get(0).getForwardingRules().stream()
            .filter(fr -> fr.getEndpointId().equals("melodrama-endpoints-3"))
            .findFirst();

    assertTrue(foundUnchangedForwardingRule.isPresent());
    assertNotEquals(pscId, foundUnchangedForwardingRule.get().getPrivateServiceConnectionId());
  }

  @Test
  public void testDeleteRegionGroup() {
    final GCPPrivateServiceConnectRegionGroup regionGroup_centralUS =
        NDSModelTestFactory.getGCPPrivateServiceConnectRegionGroup();

    _gcpPrivateServiceConnectRegionGroupDao.initializePrivateEndpointService(
        regionGroup_centralUS, _groupId, _containerId);

    final BasicDBObject docBefore = (BasicDBObject) _ndsGroupDao.getDbCollection().findOne();
    final BasicDBObject containerBefore =
        (BasicDBObject) ((BasicDBList) docBefore.get(FieldDefs.CLOUD_PROVIDER_CONTAINERS)).get(0);
    final BasicDBList regionGroupsBefore =
        (BasicDBList) containerBefore.get(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS);
    assertEquals(1, regionGroupsBefore.size());
    final BasicDBObject regionGroupBefore = (BasicDBObject) regionGroupsBefore.get(0);
    final ObjectId endpointServiceId =
        new ObjectId(regionGroupBefore.getString(DedicatedEndpointService.FieldDefs.ID));

    assertNotNull(regionGroupBefore);
    assertEquals(regionGroup_centralUS, new GCPPrivateServiceConnectRegionGroup(regionGroupBefore));

    assertTrue(
        _gcpPrivateServiceConnectRegionGroupDao.deleteRegionGroup(
            _groupId, _containerId, endpointServiceId));

    final BasicDBObject docAfter = (BasicDBObject) _ndsGroupDao.getDbCollection().findOne();
    final BasicDBObject containerAfter =
        (BasicDBObject) ((BasicDBList) docAfter.get(FieldDefs.CLOUD_PROVIDER_CONTAINERS)).get(0);
    final BasicDBList regionGroupsAfter =
        (BasicDBList) containerAfter.get(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS);
    assertTrue(regionGroupsAfter.isEmpty());
  }
}
