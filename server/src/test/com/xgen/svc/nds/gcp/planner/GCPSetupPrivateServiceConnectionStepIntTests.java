package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.google.api.services.compute.model.BackendService;
import com.google.api.services.compute.model.ForwardingRule;
import com.google.api.services.compute.model.HealthCheck;
import com.google.api.services.compute.model.ServiceAttachment;
import com.google.api.services.compute.model.Subnetwork;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectionDao;
import com.xgen.cloud.nds.gcp._public.model.GCPAtlasSubnet;
import com.xgen.cloud.nds.gcp._public.model.GCPNATSubnet;
import com.xgen.cloud.nds.gcp._public.model.GCPPrivateServiceConnection;
import com.xgen.cloud.nds.gcp._public.model.GCPPrivateServiceConnection.Status;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.gcp.planner.networking.GCPDeletePrivateServiceConnectionStep;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPSetupPrivateServiceConnectionStep;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GCPSetupPrivateServiceConnectionStepIntTests extends GCPExternalIntTest {

  private static final Logger LOGGER =
      LoggerFactory.getLogger(GCPSetupPrivateServiceConnectionStepIntTests.class);

  @Inject GCPPrivateServiceConnectionDao _gcpPrivateServiceConnectionDao;

  @Test
  public void test() {
    final ObjectId groupId = getGroupId();
    final ObjectId containerId = getContainer().getId();
    final Plan plan = new Plan(groupId, getNdsPlanContextFactory());

    final Move provisionContainerMove =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            containerId,
            List.of(GCPRegionName.NORTH_AMERICA_NORTHEAST_1));
    plan.addMove(provisionContainerMove);
    getPlanDao().save(plan);
    waitForMovePerformDone(provisionContainerMove);

    final GCPPrivateServiceConnection privateServiceConnection =
        NDSModelTestFactory.getGCPPrivateServiceConnectionWithStatus(
            groupId,
            ObjectId.get(),
            GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
            5,
            Status.INITIATING,
            50);

    final GCPSetupPrivateServiceConnectionStep step =
        new GCPSetupPrivateServiceConnectionStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(plan.getId(), plan.getMoves().get(0).getId(), 0, getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            privateServiceConnection,
            getGroupDao(),
            _gcpPrivateServiceConnectionDao);

    waitForStepPerformDone(step);

    final Result<GCPSetupPrivateServiceConnectionStep.Data> result = step.perform();

    verifyNATSubnet(result.getData().getNatSubnetName(), privateServiceConnection.getNatSubnet());
    verifyHealthCheck(
        result.getData().getHealthCheckName(), privateServiceConnection.getRegionName());
    verifyBackendService(
        result.getData().getBackendServiceName(),
        result.getData().getHealthCheckName(),
        privateServiceConnection.getRegionName());
    verifyInternalForwardingRule(
        result.getData().getInternalForwardingRuleName(),
        result.getData().getBackendServiceName(),
        privateServiceConnection.getRegionName());
    verifyServiceAttachment(
        result.getData().getServiceAttachmentName(),
        result.getData().getNatSubnetName(),
        result.getData().getInternalForwardingRuleName(),
        privateServiceConnection.getRegionName());

    // cleanup
    final GCPDeletePrivateServiceConnectionStep cleanupStep =
        new GCPDeletePrivateServiceConnectionStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(plan.getId(), plan.getMoves().get(0).getId(), 0, getPlanDao()),
            getContainer(),
            getGCPApiSvc(),
            privateServiceConnection.getServiceAttachmentName(),
            privateServiceConnection.getInternalLoadBalancer().getInternalForwardingRuleName(),
            privateServiceConnection.getInternalLoadBalancer().getBackendServiceName(),
            privateServiceConnection.getInternalLoadBalancer().getHealthCheckName(),
            privateServiceConnection.getNatSubnet().getSubnetName().orElse(null),
            privateServiceConnection.getRegionName());

    waitForStepPerformDone(cleanupStep);

    final Result<NoData> cleanupResult = cleanupStep.perform();

    assertTrue(cleanupResult.getStatus().isDone());

    verifyHealthCheckDeleted(
        result.getData().getHealthCheckName(), privateServiceConnection.getRegionName());
    verifyBackendServiceDeleted(
        result.getData().getBackendServiceName(), privateServiceConnection.getRegionName());
    verifyInternalForwardingRuleDeleted(
        result.getData().getInternalForwardingRuleName(), privateServiceConnection.getRegionName());
    verifyServiceAttachmentDeleted(
        result.getData().getServiceAttachmentName(), privateServiceConnection.getRegionName());
  }

  private void verifyNATSubnet(final String pSubnetName, final GCPNATSubnet pGCPNATSubnet) {
    final Subnetwork subnetwork =
        getGCPApiSvc()
            .getSubnet(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pGCPNATSubnet.getRegion(),
                LOGGER,
                pSubnetName);

    assertNotNull(subnetwork);
    assertEquals(pSubnetName, subnetwork.getName());
    assertEquals(pGCPNATSubnet.getCidrBlock(), subnetwork.getIpCidrRange());
    assertEquals("PRIVATE_SERVICE_CONNECT", subnetwork.getPurpose());
  }

  private void verifyHealthCheck(final String pHealthCheckName, final GCPRegionName pRegionName) {
    final HealthCheck healthCheck =
        getGCPApiSvc()
            .getHealthCheck(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pRegionName,
                LOGGER,
                pHealthCheckName);

    assertNotNull(healthCheck);
    assertEquals(pHealthCheckName, healthCheck.getName());
    assertEquals("TCP", healthCheck.getType());
    assertNotNull(healthCheck.getTcpHealthCheck());
    assertEquals((Integer) 27017, healthCheck.getTcpHealthCheck().getPort());
    assertEquals("mongod", healthCheck.getTcpHealthCheck().getPortName());
  }

  private void verifyBackendService(
      final String pBackendServiceName,
      final String pHealthCheckName,
      final GCPRegionName pRegionName) {
    final BackendService backendService =
        getGCPApiSvc()
            .getBackendService(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pRegionName,
                LOGGER,
                pBackendServiceName);

    assertNotNull(backendService);
    assertEquals(pBackendServiceName, backendService.getName());
    assertEquals("INTERNAL", backendService.getLoadBalancingScheme());
    assertEquals("TCP", backendService.getProtocol());
    assertTrue(
        backendService.getHealthChecks().stream().anyMatch(hc -> hc.contains(pHealthCheckName)));
  }

  private void verifyInternalForwardingRule(
      final String pInternalForwardingRuleName,
      final String pBackendServiceName,
      final GCPRegionName pRegionName) {
    final ForwardingRule forwardingRule =
        getGCPApiSvc()
            .getForwardingRule(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pRegionName,
                LOGGER,
                pInternalForwardingRuleName);

    final GCPAtlasSubnet atlasSubnet =
        getContainer().getSubnets().stream()
            .filter(sn -> sn.getRegion().equals(pRegionName))
            .findFirst()
            .get();

    assertNotNull(forwardingRule);
    assertEquals(pInternalForwardingRuleName, forwardingRule.getName());
    assertEquals("INTERNAL", forwardingRule.getLoadBalancingScheme());
    assertTrue(forwardingRule.getNetwork().contains(getContainer().getNetworkName().get()));
    assertTrue(forwardingRule.getSubnetwork().contains(atlasSubnet.getSubnetName()));
    assertEquals("TCP", forwardingRule.getIPProtocol());
    assertEquals(3, forwardingRule.getPorts().size());
    assertTrue(forwardingRule.getBackendService().contains(pBackendServiceName));
    verifyInternalForwardingRuleLabels(forwardingRule.getLabels());
  }

  private void verifyServiceAttachment(
      final String pServiceAttachmentName,
      final String pNATSubnetName,
      final String pInternalForwardingRuleName,
      final GCPRegionName pRegionName) {
    final ServiceAttachment serviceAttachment =
        getGCPApiSvc()
            .getServiceAttachment(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pRegionName,
                LOGGER,
                pServiceAttachmentName);

    assertNotNull(serviceAttachment);
    assertEquals(pServiceAttachmentName, serviceAttachment.getName());
    assertEquals("ACCEPT_MANUAL", serviceAttachment.getConnectionPreference());
    assertNull(serviceAttachment.getConsumerAcceptLists());
    assertTrue(
        serviceAttachment.getNatSubnets().stream().anyMatch(sn -> sn.contains(pNATSubnetName)));
    assertTrue(serviceAttachment.getTargetService().contains(pInternalForwardingRuleName));
    assertTrue(serviceAttachment.getRegion().contains(pRegionName.getValue()));
  }
}
