package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.google.api.services.compute.model.AttachedDisk;
import com.google.api.services.compute.model.Instance;
import com.xgen.cloud.agent._public.svc.AgentApiKeySvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPOrphanedItem;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import com.xgen.svc.nds.svc.CloudChefConfSvc;
import com.xgen.svc.nds.svc.NDSInstanceAgentApiKeysSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.slf4j.Logger;

public class GCPReplaceDiskMoveIntTests extends GCPExternalIntTest {

  private static final int MEDIUM_SIZE = 15;
  private static final int SMALLER_SIZE = 10;

  @Inject private AgentApiKeySvc _agentApiKeySvc;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private CloudChefConfSvc _cloudChefConfSvc;
  @Inject private NDSOrphanedItemDao _ndsOrphanedItemDao;

  @Inject private NDSInstanceAgentApiKeysSvc _ndsInstanceAgentApiKeysSvc;

  @Test(timeout = 60 * 60 * 1000L)
  public void testGCPReplaceDiskSizeMove() throws InterruptedException {
    final Plan plan = setupPlanWithContainerAndSingleInstance();

    final Pair<ClusterDescription, GCPReplaceDiskMove> replaceDiskMoveTest =
        prepareClusterAndMoveForTest(MEDIUM_SIZE);
    final Pair<ClusterDescription, GCPReplaceDiskMove> rollbackReplaceDiskTest =
        prepareClusterAndMoveForTest(SMALLER_SIZE);

    try {
      waitForPlanPerformSuccessWithWhitelistSync(plan);

      // Test shrink disk size
      getClusterDescriptionDao().save(replaceDiskMoveTest.getLeft());
      testGCPUpdateDiskSizeInternal(replaceDiskMoveTest.getRight());

      doReturn(Result.failed()).when(rollbackReplaceDiskTest.getRight()).updateUserData(any());
      getClusterDescriptionDao().save(rollbackReplaceDiskTest.getLeft());
      testGCPReplaceDiskSizeRollback(rollbackReplaceDiskTest.getRight());
    } finally {
      waitForMoveRollbackDone(replaceDiskMoveTest.getRight());
      waitForPlanRollbackSuccess(plan);
    }
  }

  private Instance getGCPInstance(
      final GCPInstanceHardware pInstanceHardware,
      final String pInstanceName,
      final Logger pLogger) {
    return getGCPApiSvc()
        .getInstance(
            getContainer().getGcpOrganizationId(),
            getContainer().getGcpProjectId().get(),
            pInstanceHardware.getZoneName().get(),
            pLogger,
            pInstanceName);
  }

  private GCPInstanceHardware getInstanceHardware(final GCPReplaceDiskMove pMove) {
    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
            getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final GCPInstanceHardware hardware =
        (GCPInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).get();
    return hardware;
  }

  private Pair<ClusterDescription, GCPReplaceDiskMove> prepareClusterAndMoveForTest(
      final int pDiskSize) {
    final ClusterDescription cluster =
        getClusterDescription().copy().setDiskSizeGB(pDiskSize).build();

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

    final GCPReplaceDiskMove move =
        spy(
            GCPReplaceDiskMove.factoryCreate(
                plan.getPlanContext(), cluster.getName(), getInstanceIds().get(0)));

    plan.addMove(move);
    getPlanDao().save(plan);
    return Pair.of(cluster, move);
  }

  private void testGCPReplaceDiskSizeRollback(final GCPReplaceDiskMove pMove)
      throws InterruptedException {
    waitForMoveToFailAndRollback(pMove);

    final GCPInstanceHardware hardware = getInstanceHardware(pMove);

    final Optional<String> cloudChefConfId =
        pMove.getState().getStringValue(GCPReplaceDiskMove.StateFields.CLOUD_CHEF_CONF_ID);
    cloudChefConfId.ifPresent(
        id -> assertEquals(new Date(0L), _cloudChefConfSvc.findById(id).get().getValidDatetime()));

    assertEquals(MEDIUM_SIZE, hardware.getDiskSizeGB().get().intValue());
    assertTrue(getInstanceHardware(pMove).needsForceReplacement());
    final String newDiskName = pMove.getNewDiskName();
    assertTrue(
        _ndsOrphanedItemDao
            .findByIdAndCloudProviderAndType(
                newDiskName, CloudProvider.GCP, GCPOrphanedItem.Type.PERSISTENT_DISK)
            .isPresent());
  }

  private void waitForMoveToFailAndRollback(final Move pMove) throws InterruptedException {
    Result<?> result = pMove.perform();

    while (!result.getStatus().isFailed()) {
      Thread.sleep(Duration.ofSeconds(5).toMillis());
      result = pMove.perform();
    }

    waitForMoveRollbackDone(pMove);
  }

  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  public void testGCPUpdateDiskSizeInternal(final GCPReplaceDiskMove pMove) {
    final GCPInstanceHardware oldHardware = getInstanceHardware(pMove);
    // Key before resize
    final AgentApiKey initialKey =
        getApiKeyForInstance(_ndsInstanceAgentApiKeysSvc, _agentApiKeySvc, getInstanceIds());
    assertNotNull(initialKey);

    waitForMovePerformDone(pMove);

    // Key after resize
    final AgentApiKey finalKey =
        getApiKeyForInstance(_ndsInstanceAgentApiKeysSvc, _agentApiKeySvc, getInstanceIds());
    assertNotNull(finalKey);
    assertNotEquals(initialKey, finalKey);

    final GCPInstanceHardware hardware = getInstanceHardware(pMove);

    final Instance gpcInstance =
        getGCPInstance(hardware, hardware.getGCPInstanceName().get(), pMove.getLogger());

    final AttachedDisk gcpDisk = gpcInstance.getDisks().get(1);
    assertNotNull(gcpDisk);
    // Make sure the disk and the instance hardware match the cluster description
    assertEquals((int) getClusterDescription().getDiskSizeGB(), hardware.getDiskSizeGB().get(), 0);
    assertEquals(
        GCPNDSDefaults.SCSI_INTERFACE_DATA_DEVICE_PREFIX + gcpDisk.getDeviceName(),
        hardware.getDataDeviceName().get());

    final String oldDiskName = oldHardware.getDiskName().get();
    assertTrue(
        _ndsOrphanedItemDao
            .findByIdAndCloudProviderAndType(
                oldDiskName, CloudProvider.GCP, GCPOrphanedItem.Type.PERSISTENT_DISK)
            .isPresent());
  }
}
