package com.xgen.svc.nds.gcp.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import java.time.Duration;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GCPStopStartInstanceMoveIntTests extends GCPExternalIntTest {
  private static final Logger LOG = LoggerFactory.getLogger(GCPStopStartInstanceMoveIntTests.class);

  @Test(timeout = 100 * 60 * 1000L)
  public void testGCPStopStartInstanceMove() throws Exception {
    final Plan plan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccess(plan);
      testGCPStopStartInstanceInternal();
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }

  private void testGCPStopStartInstanceInternal() throws InterruptedException {
    final ClusterDescription clusterDescription = getClusterDescription();

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());

    final NDSAdminJob ndsAdminJob =
        getNDSAdminJobDao()
            .create(
                plan.getId(),
                clusterDescription.getGroupId(),
                clusterDescription.getName(),
                getHostname(getClusterDescription(), getInstanceIds()),
                NDSAdminJob.Type.STOP_START_VM);

    final ObjectId instanceId = getInstanceIds().get(0);
    final GCPStopStartInstanceMove move =
        spy(
            GCPStopStartInstanceMove.factoryCreate(
                plan.getPlanContext(),
                clusterDescription.getName(),
                instanceId,
                ndsAdminJob.getId()));

    plan.addMove(move);
    getPlanDao().save(plan);

    final GCPInstanceHardware gcpInstanceHardware =
        Cluster.getCluster(
                getClusterDescription(),
                getHardwareDao()
                    .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName()))
            .getLiveReplicaSets()
            .stream()
            .flatMap(rsh -> rsh.getHardware().stream())
            .filter(ih -> ih.getInstanceId().equals(instanceId))
            .findFirst()
            .map(GCPInstanceHardware.class::cast)
            .get();

    assertEquals(GCPApiSvc.OperationStatus.RUNNING, getInstanceStatus(gcpInstanceHardware));
    Result<?> result = move.perform();
    final Optional<NDSAdminJob> ndsAdminJobBeforePerform =
        getNDSAdminJobDao().find(ndsAdminJob.getId());
    assertTrue(ndsAdminJobBeforePerform.isPresent());
    assertEquals(NDSAdminJob.Status.WORKING, ndsAdminJobBeforePerform.get().getStatus());

    while (!result.getStatus().isDone()) {
      if (result.getStatus().isFailed()) {
        final String error =
            String.format(
                "Failed waiting for move %s to complete, status=%s, failureCode=%s, message=%s",
                move.getClass().getSimpleName(),
                result.getStatus(),
                result.getPlanFailureCode(),
                result.getMessage());
        LOG.error(error);
        fail(error);
      }
      Thread.sleep(Duration.ofSeconds(5).toMillis());
      result = move.perform();
    }

    final Optional<NDSAdminJob> ndsAdminJobAfterPerform =
        getNDSAdminJobDao().find(ndsAdminJob.getId());
    assertTrue(ndsAdminJobAfterPerform.isPresent());
    assertEquals(NDSAdminJob.Status.COMPLETE, ndsAdminJobAfterPerform.get().getStatus());

    assertEquals(GCPApiSvc.OperationStatus.RUNNING, getInstanceStatus(gcpInstanceHardware));
  }
}
