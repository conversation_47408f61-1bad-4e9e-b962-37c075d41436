package com.xgen.svc.nds.gcp.planner.networking;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.google.api.services.compute.model.Firewall;
import com.google.api.services.compute.model.Network;
import com.google.api.services.compute.model.Subnetwork;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.gcp.GCPExternalIntTest;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import org.junit.Test;

public class GCPProvisionContainerMoveIntTests extends GCPExternalIntTest {

  public void testGCPProvisionContainerMoveInternal(
      final GCPProvisionContainerMove pMove, final List<GCPRegionName> pRegions)
      throws InterruptedException {

    Result<NoData> result = pMove.perform();

    // The move should not fail but it may be done or in progress
    assertNotFailed(pMove.getClass().getSimpleName(), result);

    NDSGroup group = getNDSGroupDao().find(getGroup().getId()).get();
    GCPCloudProviderContainer container =
        (GCPCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

    while (!result.getStatus().isDone()) {

      assertNotFailed(pMove.getClass().getSimpleName(), result);

      assertFalse(container.getNetworkName().isPresent());
      assertEquals(0, container.getSubnets().size());
      assertFalse(container.isProvisioned());

      result = pMove.perform();
      group = getNDSGroupDao().find(getGroup().getId()).get();
      container =
          (GCPCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

      Thread.sleep(Duration.ofSeconds(5).toMillis());
    }

    assertTrue(container.getNetworkName().isPresent());
    assertEquals(pRegions.get(0), container.getSubnets().get(0).getRegion());
    assertTrue(container.isProvisioned());

    final Network network =
        getGCPApiSvc()
            .getNetwork(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pMove.getContext().getLogger(),
                container.getNetworkName().get());
    assertEquals(network.getName(), container.getNetworkName().get());

    final Subnetwork subnet =
        getGCPApiSvc()
            .getSubnet(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                container.getSubnets().get(0).getRegion(),
                pMove.getContext().getLogger(),
                container.getSubnets().get(0).getSubnetName());
    assertEquals(subnet.getName(), container.getSubnets().get(0).getSubnetName());

    final List<Firewall> firewallRules =
        getGCPApiSvc()
            .listFirewallRulesOnNetwork(
                getContainer().getGcpOrganizationId(),
                getContainer().getGcpProjectId().get(),
                pMove.getContext().getLogger(),
                container.getNetworkName().get());

    assertEquals(1, firewallRules.size());
    assertTrue(firewallRules.stream().anyMatch(fr -> fr.getName().contains("icmp")));

    final GCPCloudProviderContainer cachedContainer = getContainer();
    waitForMoveRollbackDone(pMove);

    group = getNDSGroupDao().find(getGroup().getId()).get();
    container =
        (GCPCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();
    assertFalse(container.getNetworkName().isPresent());
    verifyNetworkDeleted(
        cachedContainer.getGcpProjectId().get(), cachedContainer.getNetworkName().get());
    assertEquals(0, container.getSubnets().size());

    verifySubnetDeleted(
        cachedContainer.getGcpProjectId().get(), cachedContainer.getSubnets().get(0));
    assertFalse(container.isProvisioned());
    verifyProjectOrphaned(cachedContainer.getGcpProjectId().get());
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testGCPProvisionContainerMove() throws InterruptedException {
    final List<GCPRegionName> regionsNeedingSubnets = List.of(GCPRegionName.US_WEST_2);

    final Plan plan = new Plan(getGroup().getId(), getNdsPlanContextFactory());
    final GCPProvisionContainerMove move =
        GCPProvisionContainerMove.factoryCreate(
            plan.getPlanContext(),
            Collections.emptyMap(),
            getContainer().getId(),
            regionsNeedingSubnets);

    plan.addMove(move);

    // Need to save plan so we can get state prepared
    getPlanDao().save(plan);

    try {
      testGCPProvisionContainerMoveInternal(move, regionsNeedingSubnets);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForMoveRollbackDone(move));
      throw t;
    }
  }
}
