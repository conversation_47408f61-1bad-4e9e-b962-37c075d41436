package com.xgen.svc.nds.gcp.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.jobqueue._private.dao.JobsProcessorDao;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.leakeditem._public.model.LeakedItemCleanupState;
import com.xgen.cloud.nds.leakeditem._public.svc.LeakedItemCleanupStateSvc;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class GCPLeakedItemCleanupJobHandlerIntTests extends JUnit5BaseSvcTest {
  @Inject private LeakedItemCleanupStateSvc _leakedItemCleanupStateSvc;
  @Inject private GCPLeakedItemCleanupJobHandler _gcpLeakedItemCleanupJobHandler;
  @Inject private GCPCloudProviderItemProcessorSvc _gcpCloudProviderItemProcessorSvc;
  @Inject private JobsProcessorSvc _jobsProcessorSvc;
  @Inject private JobsProcessorDao _jobsProcessorDao;

  @Test
  public void testJobCompletes() {

    final Job job =
        _gcpCloudProviderItemProcessorSvc.generateLeakedItemCleanupJob(CloudProvider.GCP);
    assertNotNull(job);
    final String expectedWorker = GCPLeakedItemCleanupJobHandler.class.toString().split(" ")[1];
    assertTrue(job.getWorker().equals(expectedWorker));

    LOG.info("{}", job.getId());
    _jobsProcessorSvc.submitJob(job);

    final ObjectId stateId = job.getParameters().getObjectId(LeakedItemCleanupState.FieldDefs.ID);

    // Run three times for three stages
    _gcpLeakedItemCleanupJobHandler.handleWork(job.getParameters(), job.getId());
    assertEquals(
        _leakedItemCleanupStateSvc.findById(stateId).getStage(),
        LeakedItemCleanupState.LeakedItemCleanupStage.DELETE_APPROVED_ITEMS);
    _gcpLeakedItemCleanupJobHandler.handleWork(job.getParameters(), job.getId());
    assertEquals(
        _leakedItemCleanupStateSvc.findById(stateId).getStage(),
        LeakedItemCleanupState.LeakedItemCleanupStage.FIND_UNACKNOWLEDGED_AND_FAILED_ITEMS);
    _gcpLeakedItemCleanupJobHandler.handleWork(job.getParameters(), job.getId());
    assertEquals(
        _leakedItemCleanupStateSvc.findById(stateId).getStage(),
        LeakedItemCleanupState.LeakedItemCleanupStage.DONE);

    final Job finishedJob = _jobsProcessorDao.getJob(job.getId());
    assertEquals(Job.Status.COMPLETED, finishedJob.getStatus());
  }
}
