package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.cps.billing._private.dao.CpsOplogUsageSummaryDao;
import com.xgen.cloud.cps.billing._public.model.CpsOplogUsageSummary;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CpsOplogUsageSummaryDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private CpsOplogUsageSummaryDao _cpsOplogUsageSummaryDao;
  private static final String clusterName1 = "clusterName1";
  private static final ObjectId clusterUniqueId1 = new ObjectId();
  private static final ObjectId projectId1 = new ObjectId();
  private static final RegionName regionName1 = AWSRegionName.US_EAST_1;
  private static final CloudProvider cloudProvider1 = CloudProvider.AWS;
  private static final double usageInGBDays1 = 100;
  private static final Date usageDate1 = TimeUtils.fromISOString("2023-03-21");

  private static final String clusterName2 = "clusterName2";
  private static final ObjectId clusterUniqueId2 = new ObjectId();
  private static final ObjectId projectId2 = new ObjectId();
  private static final RegionName regionName2 = AWSRegionName.US_WEST_1;
  private static final CloudProvider cloudProvider2 = CloudProvider.AWS;
  private static final double usageInGBDays2 = 200;
  private static final Date usageDate2 = TimeUtils.fromISOString("2023-03-21");

  private static final String clusterName3 = "clusterName3";
  private static final ObjectId clusterUniqueId3 = new ObjectId();
  private static final ObjectId projectId3 = new ObjectId();
  private static final RegionName regionName3 = AWSRegionName.US_EAST_1;
  private static final CloudProvider cloudProvider3 = CloudProvider.AWS;
  private static final double usageInGBDays3 = 300;
  private static final Date usageDate3 = TimeUtils.fromISOString("2023-03-23");

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    // clear if it exists
    _cpsOplogUsageSummaryDao.getDbCollection().drop();
    // ensure indexes exist after drop
    _cpsOplogUsageSummaryDao.ensureIndexes();

    List<CpsOplogUsageSummary> cpsOplogUsageSummaryList = new ArrayList<>();
    CpsOplogUsageSummary oplog1 =
        new CpsOplogUsageSummary(
            clusterName1,
            clusterUniqueId1,
            projectId1,
            regionName1,
            cloudProvider1,
            usageInGBDays1,
            usageDate1);
    CpsOplogUsageSummary oplog2 =
        new CpsOplogUsageSummary(
            clusterName2,
            clusterUniqueId2,
            projectId2,
            regionName2,
            cloudProvider2,
            usageInGBDays2,
            usageDate2);
    CpsOplogUsageSummary oplog3 =
        new CpsOplogUsageSummary(
            clusterName3,
            clusterUniqueId3,
            projectId3,
            regionName3,
            cloudProvider3,
            usageInGBDays3,
            usageDate3);
    cpsOplogUsageSummaryList.add(oplog1);
    cpsOplogUsageSummaryList.add(oplog2);
    cpsOplogUsageSummaryList.add(oplog3);

    // addUsageBatch is tested implicitly
    _cpsOplogUsageSummaryDao.addUsageBatch(cpsOplogUsageSummaryList);

    // we drop the TTL index to prevent the test data from being deleted before the test finishes
    _cpsOplogUsageSummaryDao.getDbCollection().dropIndexes();
  }

  @Test
  public void testAddUsageBatch() {
    final Date usageDate = TimeUtils.fromISOString("2023-03-24");

    final String clusterName4 = "clusterName4";
    final ObjectId clusterUniqueId4 = new ObjectId();
    final ObjectId projectId4 = new ObjectId();
    final RegionName regionName4 = AWSRegionName.US_EAST_1;
    final CloudProvider cloudProvider4 = CloudProvider.AWS;
    final double usageInGBDays4 = 400;

    final String clusterName5 = "clusterName5";
    final ObjectId clusterUniqueId5 = new ObjectId();
    final ObjectId projectId5 = new ObjectId();
    final RegionName regionName5 = AWSRegionName.US_EAST_1;
    final CloudProvider cloudProvider5 = CloudProvider.AWS;
    final double usageInGBDays5 = 500;

    List<CpsOplogUsageSummary> cpsOplogUsageSummaryList =
        Arrays.asList(
            new CpsOplogUsageSummary(
                clusterName4,
                clusterUniqueId4,
                projectId4,
                regionName4,
                cloudProvider4,
                usageInGBDays4,
                usageDate),
            new CpsOplogUsageSummary(
                clusterName5,
                clusterUniqueId5,
                projectId5,
                regionName5,
                cloudProvider5,
                usageInGBDays5,
                usageDate));

    _cpsOplogUsageSummaryDao.addUsageBatch(cpsOplogUsageSummaryList);

    try (final DBCursor cursor = _cpsOplogUsageSummaryDao.getAllUsageByDate(usageDate)) {
      assertEquals(cpsOplogUsageSummaryList.size(), cursor.toArray().size());
    }
  }

  @Test
  public void testGetAllUsageByDate() {
    try (final DBCursor cursor = _cpsOplogUsageSummaryDao.getAllUsageByDate(usageDate1)) {
      assertEquals(2, cursor.toArray().size());
    }

    try (final DBCursor cursor = _cpsOplogUsageSummaryDao.getAllUsageByDate(usageDate3)) {
      assertEquals(1, cursor.toArray().size());
    }
  }

  @Test
  public void testGetPitClusterInfo() {
    List<ObjectId> groupIds = Arrays.asList(projectId1, projectId2, projectId3, new ObjectId());
    try (final DBCursor cursor =
        _cpsOplogUsageSummaryDao.getPitClusterInfo(CloudProvider.AWS, usageDate1, groupIds)) {
      assertEquals(2, cursor.toArray().size());
    }
  }
}
