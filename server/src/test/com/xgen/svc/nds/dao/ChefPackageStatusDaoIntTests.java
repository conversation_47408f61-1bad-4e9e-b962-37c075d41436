package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.project._private.dao.ChefPackageStatusDao;
import com.xgen.cloud.nds.project._public.model.ChefPackageStatus;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;

public class ChefPackageStatusDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private ChefPackageStatusDao _chefPackageStatusDao;

  @Test
  public void testCreateAndFind() {

    List<String> packages = Arrays.asList("a", "b", "c", "d");
    String packageHash = _chefPackageStatusDao.createIfNotExists(packages);

    Optional<ChefPackageStatus> packageStatus = _chefPackageStatusDao.findByHash(packageHash);
    assertTrue(packageStatus.isPresent());
  }

  @Test
  public void testHashGeneration() {

    List<String> alphabetizedPackages = Arrays.asList("a", "b", "c", "d");
    String alphabetizedPackagesHash = _chefPackageStatusDao.createIfNotExists(alphabetizedPackages);

    List<String> randomizedPackages = Arrays.asList("c", "b", "d", "a");
    String randomizedPackageHash = _chefPackageStatusDao.createIfNotExists(randomizedPackages);

    assertEquals(alphabetizedPackagesHash, randomizedPackageHash);
    assertEquals(1, _chefPackageStatusDao.getDbCollection().count());
  }
}
