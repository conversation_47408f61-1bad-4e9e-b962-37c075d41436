package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.driverwrappers._public.legacy.BulkWriteOperation;
import com.xgen.cloud.common.function._public.function.FunctionsEx;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardwareHealth;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.ForceStopStartVMSource;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.FieldDefs;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.RebootRequestedBy;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareHealth;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.NDSProcessType;
import com.xgen.cloud.nds.common._public.model.ReplicaSetType;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSHostModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ReplicaSetHardwareDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private AppSettings _appSettings;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _replicaSetHardwareDao.ensureIndexes();
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testCreate() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0),
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        specId);
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 1),
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 1),
        true,
        false,
        specId);
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createConfigReplicaSetId("foo", groupId),
        String.format("%s-config-0", "foo"),
        false,
        true,
        specId);

    assertNotNull(
        _replicaSetHardwareDao
            .getDbCollection()
            .findOne(
                new BasicDBObject(
                    "_id",
                    new BasicDBObject()
                        .append("clusterName", "foo")
                        .append("groupId", groupId)
                        .append("type", ReplicaSetType.SHARD.name())
                        .append("index", 0))));

    assertNotNull(
        _replicaSetHardwareDao
            .getDbCollection()
            .findOne(
                new BasicDBObject(
                    "_id",
                    new BasicDBObject()
                        .append("clusterName", "foo")
                        .append("groupId", groupId)
                        .append("type", ReplicaSetType.SHARD.name())
                        .append("index", 1))));

    assertNotNull(
        _replicaSetHardwareDao
            .getDbCollection()
            .findOne(
                new BasicDBObject(
                    "_id",
                    new BasicDBObject()
                        .append("clusterName", "foo")
                        .append("groupId", groupId)
                        .append("type", ReplicaSetType.CONFIG.name())
                        .append("index", 0))));

    // Make sure unique id is working
    try {
      _replicaSetHardwareDao.create(
          ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0),
          ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
          true,
          false,
          specId);
      fail();
    } catch (Exception e) {
    }
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindById() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    final BasicDBObject replicaSetHardware0Id =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    final BasicDBObject replicaSetHardware1Id =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 1);
    _replicaSetHardwareDao.create(
        replicaSetHardware0Id,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        specId);
    _replicaSetHardwareDao.create(
        replicaSetHardware1Id,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 1),
        true,
        false,
        specId);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    final Date now = new Date(System.currentTimeMillis() - Duration.ofSeconds(5).toMillis());
    final ReplicaSetHardware hardware =
        _replicaSetHardwareDao.findById(replicaSetHardware0Id).get();
    assertNotNull(hardware);
    assertEquals("foo", hardware.getClusterName());
    assertEquals(groupId, hardware.getGroupId());
    assertEquals(ReplicaSetType.SHARD, hardware.getType());
    assertEquals(0, hardware.getIndex());
    assertTrue(hardware.getLastUpdated().after(now));

    final ReplicaSetHardware hardware2 =
        _replicaSetHardwareDao.findById(replicaSetHardware1Id).get();
    assertNotNull(hardware);
    assertEquals("foo", hardware2.getClusterName());
    assertEquals(groupId, hardware.getGroupId());
    assertEquals(ReplicaSetType.SHARD, hardware2.getType());
    assertEquals(1, hardware2.getIndex());
    assertTrue(hardware2.getLastUpdated().after(now));

    assertFalse(
        _replicaSetHardwareDao
            .findById(ReplicaSetHardware.createNonConfigReplicaSetId("bar", groupId, 0))
            .isPresent());
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindByCluster() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0),
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        specId);
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 1),
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 1),
        true,
        false,
        specId);
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createConfigReplicaSetId("foo", groupId),
        String.format("%s-config-0", "foo"),
        false,
        true,
        specId);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    final List<ReplicaSetHardware> hardware =
        _replicaSetHardwareDao.findByCluster(g.getGroupId(), "foo");
    assertNotNull(hardware);
    assertEquals(3, hardware.size());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testAddInstance() {
    final ObjectId groupId = new ObjectId();

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    assertNotNull(instanceId);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    final ReplicaSetHardware hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(1, hardware.getHardware().size());
    assertEquals(instanceId, hardware.getHardware().get(0).getInstanceId());

    final ObjectId instanceId2 =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 1);
    final ReplicaSetHardware hardware2 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(2, hardware2.getHardware().size());
    assertEquals(instanceId, hardware2.getHardware().get(0).getInstanceId());
    assertEquals(0, hardware2.getHardware().get(0).getMemberIndex());
    assertEquals(instanceId2, hardware2.getHardware().get(1).getInstanceId());
    assertEquals(1, hardware2.getHardware().get(1).getMemberIndex());
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindByInstanceHostname() {
    final String hostname = "myhostname";
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());

    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    assertNotNull(instanceId);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    final ReplicaSetHardware hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(1, hardware.getHardware().size());
    assertEquals(instanceId, hardware.getHardware().get(0).getInstanceId());

    assertFalse(
        _replicaSetHardwareDao.findClusterByInstanceHostname(hostname, groupId).isPresent());

    final Hostnames hostnames = new Hostnames(hostname);
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        hostnames.toDBList());
    final ClusterDescriptionId clusterDescriptionId =
        _replicaSetHardwareDao.findClusterByInstanceHostname(hostname, groupId).orElse(null);
    assertNotNull(clusterDescriptionId);
    assertEquals(clusterName, clusterDescriptionId.getClusterName());
    assertEquals(groupId, clusterDescriptionId.getGroupId());
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindReplicaSetHardwareForInstance() {
    final String hostname = "myhostname";
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final int index = 0;

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        new ObjectId());

    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    assertNotNull(instanceId);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    final ReplicaSetHardware hardware =
        _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
            g.getGroupId(), clusterName, instanceId);
    assertEquals(1, hardware.getHardware().size());
    assertEquals(instanceId, hardware.getHardware().get(0).getInstanceId());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAction() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    ReplicaSetHardware hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(InstanceHardware.Action.NONE, hardware.getHardware().get(0).getAction());

    _replicaSetHardwareDao.setAction(
        replicaSetHardwareId, instanceId, false, InstanceHardware.Action.CREATE);
    hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(InstanceHardware.Action.CREATE, hardware.getHardware().get(0).getAction());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndClearInstanceForceReplacement() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    ReplicaSetHardware hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();

    assertEquals(1, hardware.getHardware().size());
    assertFalse(hardware.getHardware().get(0).needsForceReplacement());

    // set forceReplacement for the instance
    _replicaSetHardwareDao.setInstanceForceReplacement(hardware.getId(), instanceId, false);

    hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertTrue(hardware.getHardware().get(0).needsForceReplacement());

    // clear forceReplacement for the instance
    _replicaSetHardwareDao.clearInstanceForceReplacement(hardware.getId(), instanceId, false);

    hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertFalse(hardware.getHardware().get(0).needsForceReplacement());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSafeSetHostnames() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final InstanceHostname legacyHostname =
        new InstanceHostname(InstanceHostname.HostnameScheme.LEGACY, "abc1");
    final InstanceHostname publicHostname =
        new InstanceHostname(InstanceHostname.HostnameScheme.LEGACY, "xyz2");
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    ReplicaSetHardware hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(0, hardware.getHardware().get(0).getHostnames().size());

    final Hostnames newHostnames1 = new Hostnames(List.of(legacyHostname));
    _replicaSetHardwareDao.safeSetHostnames(
        replicaSetHardwareId, instanceId, false, null, newHostnames1);
    hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertHostnamesEquals(newHostnames1, hardware.getHardware().get(0).getHostnames());

    final Hostnames newHostnames2 = new Hostnames(List.of(legacyHostname, publicHostname));
    _replicaSetHardwareDao.safeSetHostnames(
        replicaSetHardwareId, instanceId, false, newHostnames1, newHostnames2);
    hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertHostnamesEquals(newHostnames2, hardware.getHardware().get(0).getHostnames());

    _replicaSetHardwareDao.safeSetHostnames(
        replicaSetHardwareId, instanceId, false, newHostnames1, newHostnames1);
    hardware = _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    assertEquals(2, hardware.getHardware().get(0).getHostnames().size()); // test for no update
  }

  private void assertHostnamesEquals(
      final Hostnames pCorrectHostnames, final Hostnames pHostnames) {
    final Comparator<InstanceHostname> instanceHostnameComparator =
        (ih1, ih2) -> ih1.getHostname().compareTo(ih2.getHostname());
    final List<InstanceHostname> sortedCorrectHostnames =
        pCorrectHostnames.stream().sorted(instanceHostnameComparator).collect(Collectors.toList());
    final List<InstanceHostname> sortedHostnames =
        pHostnames.stream().sorted(instanceHostnameComparator).collect(Collectors.toList());

    assertEquals(sortedCorrectHostnames.size(), sortedHostnames.size());
    IntStream.range(0, sortedHostnames.size())
        .forEach(
            i -> {
              assertEquals(
                  sortedCorrectHostnames.get(i).getHostname(),
                  sortedHostnames.get(i).getHostname());
              assertEquals(
                  sortedCorrectHostnames.get(i).getScheme(), sortedHostnames.get(i).getScheme());
            });
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testUpdateHealth() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final ReplicaSetHardware hardware1 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();

    final AWSInstanceHardwareHealth initialHealth =
        (AWSInstanceHardwareHealth) hardware1.getHardware().get(0).getHealth();
    assertFalse(initialHealth.getCollectedFor().isPresent());
    assertFalse(initialHealth.getLastAutomationAgentAudit().isPresent());
    assertFalse(initialHealth.getLastMonitoringProcessPing().isPresent());
    assertFalse(initialHealth.getLastICMPPing().isPresent());
    assertFalse(initialHealth.getLastInstanceState().isPresent());
    assertFalse(initialHealth.getLastInstanceStatus().isPresent());
    assertFalse(initialHealth.getLastSystemStatus().isPresent());
    assertFalse(initialHealth.getLastVolumeStatus().isPresent());

    final AWSInstanceHardwareHealth partialHealth =
        (AWSInstanceHardwareHealth)
            new AWSInstanceHardwareHealth.Builder()
                .setLastInstanceState(new InstanceHardwareHealth.HealthItem(new Date(), "good"))
                .setCollectedForDate(new Date())
                .setLastAutomationAuditDate(new Date())
                .setLastMonitoringProcessPingDate(new Date())
                .build();

    final BulkWriteOperation bw = _replicaSetHardwareDao.getBulkWriteOperation();
    _replicaSetHardwareDao.appendHealthUpdate(
        bw, replicaSetHardwareId, instanceId, false, partialHealth);
    bw.execute(WriteConcern.ACKNOWLEDGED);

    final ReplicaSetHardware hardware2 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();

    final AWSInstanceHardwareHealth savedHealth =
        (AWSInstanceHardwareHealth) hardware2.getHardware().get(0).getHealth();
    assertTrue(savedHealth.getCollectedFor().isPresent());
    assertTrue(savedHealth.getLastAutomationAgentAudit().isPresent());
    assertTrue(savedHealth.getLastMonitoringProcessPing().isPresent());
    assertFalse(savedHealth.getLastICMPPing().isPresent());
    assertTrue(savedHealth.getLastInstanceState().isPresent());
    assertFalse(savedHealth.getLastInstanceStatus().isPresent());
    assertFalse(savedHealth.getLastSystemStatus().isPresent());
    assertFalse(savedHealth.getLastVolumeStatus().isPresent());

    assertEquals(partialHealth.getCollectedFor().get(), savedHealth.getCollectedFor().get());
    assertEquals(
        partialHealth.getLastAutomationAgentAudit().get(),
        savedHealth.getLastAutomationAgentAudit().get());
    assertEquals(
        partialHealth.getLastMonitoringProcessPing().get(),
        savedHealth.getLastMonitoringProcessPing().get());
    assertEquals(
        partialHealth.getLastInstanceState().get().getInitiallySeen(),
        savedHealth.getLastInstanceState().get().getInitiallySeen());
    assertEquals(
        partialHealth.getLastInstanceState().get().getValue(),
        savedHealth.getLastInstanceState().get().getValue());

    // Collected for mismatch should still save
    final AWSInstanceHardwareHealth partialHealth2 =
        (AWSInstanceHardwareHealth)
            new AWSInstanceHardwareHealth.Builder()
                .setLastInstanceState(new InstanceHardwareHealth.HealthItem(new Date(), "bad"))
                .setCollectedForDate(new Date())
                .setLastAutomationAuditDate(new Date())
                .setLastMonitoringProcessPingDate(new Date())
                .build();

    final BulkWriteOperation bw2 = _replicaSetHardwareDao.getBulkWriteOperation();
    _replicaSetHardwareDao.appendHealthUpdate(
        bw2, replicaSetHardwareId, instanceId, false, partialHealth2);
    bw2.execute(WriteConcern.ACKNOWLEDGED);

    final ReplicaSetHardware hardware3 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();

    // Make sure we did not update anything
    final AWSInstanceHardwareHealth savedHealth2 =
        (AWSInstanceHardwareHealth) hardware3.getHardware().get(0).getHealth();
    assertEquals(
        hardware2.getHardware().get(0).getLastUpdated(),
        hardware3.getHardware().get(0).getLastUpdated());
    assertTrue(savedHealth2.getCollectedFor().isPresent());
    assertTrue(savedHealth2.getLastAutomationAgentAudit().isPresent());
    assertTrue(savedHealth2.getLastMonitoringProcessPing().isPresent());
    assertFalse(savedHealth2.getLastICMPPing().isPresent());
    assertTrue(savedHealth2.getLastInstanceState().isPresent());
    assertFalse(savedHealth2.getLastInstanceStatus().isPresent());
    assertFalse(savedHealth2.getLastSystemStatus().isPresent());
    assertFalse(savedHealth2.getLastVolumeStatus().isPresent());

    assertEquals(partialHealth2.getCollectedFor().get(), savedHealth2.getCollectedFor().get());
    assertEquals(
        partialHealth2.getLastAutomationAgentAudit().get(),
        savedHealth2.getLastAutomationAgentAudit().get());
    assertEquals(
        partialHealth2.getLastMonitoringProcessPing().get(),
        savedHealth2.getLastMonitoringProcessPing().get());
    assertEquals(
        partialHealth2.getLastInstanceState().get().getInitiallySeen(),
        savedHealth2.getLastInstanceState().get().getInitiallySeen());
    assertEquals(
        partialHealth2.getLastInstanceState().get().getValue(),
        savedHealth2.getLastInstanceState().get().getValue());
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindAllWithActiveInstances() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    final ObjectId instanceId1 =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 1);
    final ObjectId instanceId3 =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 2);

    assertEquals(0, _replicaSetHardwareDao.findAllWithActiveInstances(g.getGroupId()).count());

    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId, instanceId1, false, InstanceHardware.FieldDefs.PROVISIONED, true);

    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId, instanceId3, false, InstanceHardware.FieldDefs.PROVISIONED, true);

    assertEquals(1, _replicaSetHardwareDao.findAllWithActiveInstances(g.getGroupId()).count());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testDeleteAllHardwareOnCluster() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";

    final BasicDBObject fooReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        fooReplicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final BasicDBObject barReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("bar", groupId, 0);
    _replicaSetHardwareDao.create(
        barReplicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("bar", 0),
        true,
        false,
        new ObjectId());
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);

    _replicaSetHardwareDao.addInstance(fooReplicaSetHardwareId, CloudProvider.AWS, false, 0);
    _replicaSetHardwareDao.addInstance(fooReplicaSetHardwareId, CloudProvider.AWS, false, 1);
    _replicaSetHardwareDao.addInstance(fooReplicaSetHardwareId, CloudProvider.AWS, false, 2);

    _replicaSetHardwareDao.addInstance(barReplicaSetHardwareId, CloudProvider.AWS, false, 0);
    _replicaSetHardwareDao.addInstance(barReplicaSetHardwareId, CloudProvider.AWS, false, 1);
    _replicaSetHardwareDao.addInstance(barReplicaSetHardwareId, CloudProvider.AWS, false, 2);

    assertEquals(
        _replicaSetHardwareDao.findByCluster(g.getGroupId(), "foo").stream()
            .flatMap(r -> r.getHardware().stream())
            .count(),
        3);

    _replicaSetHardwareDao.deleteAllHardwareOnCluster(groupId, clusterName);

    assertEquals(
        _replicaSetHardwareDao.findByCluster(g.getGroupId(), "foo").stream()
            .flatMap(r -> r.getHardware().stream())
            .count(),
        0);
    assertEquals(
        _replicaSetHardwareDao.findByCluster(g.getGroupId(), "bar").stream()
            .flatMap(r -> r.getHardware().stream())
            .count(),
        3);
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetInstanceForceServerRestart() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    _replicaSetHardwareDao.setInstanceForceServerRestart(replicaSetHardwareId, instanceId, false);

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);

    assertTrue(hardware.needsForceServerRestart());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetInstanceOSSwap() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject fooReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        fooReplicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(fooReplicaSetHardwareId, CloudProvider.AWS, false, 0);

    final ReplicaSetHardware replicaSetHardwareOriginalState =
        _replicaSetHardwareDao.findById(fooReplicaSetHardwareId).get();
    final InstanceHardware hardwareOriginalState =
        replicaSetHardwareOriginalState.getHardware().get(0);

    assertTrue(!hardwareOriginalState.needsOSSwap());

    final Date newDate_M30_M5_foo = new Date();
    _replicaSetHardwareDao.setInstanceOSSwap(
        fooReplicaSetHardwareId,
        instanceId,
        false,
        true,
        Optional.of(Pair.of("instanceSize", "M30")),
        Optional.of(Pair.of("lastInstanceSizeModifyDate", newDate_M30_M5_foo)),
        Optional.of(Pair.of("instanceFamily", "M5")),
        Optional.of(
            Pair.of(
                "os",
                CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                    .getInstanceHardwareProvider()
                    .getDefaultOs(_appSettings, AWSInstanceFamily.M5))),
        Optional.of(Pair.of("diskDeviceName", "/dev/xvdc")));

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findById(fooReplicaSetHardwareId).get();
    final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);

    assertTrue(hardware.needsOSSwap());
    assertEquals("M30", hardware.toDBObject().getString("instanceSize"));
    assertEquals("M5", hardware.toDBObject().getString("instanceFamily"));
    assertEquals("/dev/xvdc", hardware.toDBObject().getString("diskDeviceName"));
    assertEquals(newDate_M30_M5_foo, hardware.toDBObject().getDate("lastInstanceSizeModifyDate"));

    final Date newDate_M30_M6G_foo = new Date();
    _replicaSetHardwareDao.setInstanceOSSwap(
        fooReplicaSetHardwareId,
        instanceId,
        false,
        false,
        Optional.of(Pair.of("instanceSize", "M30")),
        Optional.of(Pair.of("lastInstanceSizeModifyDate", newDate_M30_M6G_foo)),
        Optional.of(Pair.of("instanceFamily", "M6G")),
        Optional.of(
            Pair.of(
                "os",
                CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                    .getInstanceHardwareProvider()
                    .getDefaultOs(_appSettings, AWSInstanceFamily.M6G))),
        Optional.of(Pair.of("diskDeviceName", "/dev/xvdb")));

    final ReplicaSetHardware replicaSetHardwareSetToFalse =
        _replicaSetHardwareDao.findById(fooReplicaSetHardwareId).get();
    final InstanceHardware hardwareSetToFalse = replicaSetHardwareSetToFalse.getHardware().get(0);
    assertFalse(hardwareSetToFalse.needsOSSwap());
    assertEquals("M30", hardwareSetToFalse.toDBObject().getString("instanceSize"));
    assertEquals("M6G", hardwareSetToFalse.toDBObject().getString("instanceFamily"));
    assertEquals("/dev/xvdb", hardwareSetToFalse.toDBObject().getString("diskDeviceName"));
    assertEquals(
        newDate_M30_M6G_foo, hardwareSetToFalse.toDBObject().getDate("lastInstanceSizeModifyDate"));

    // testing update without updating the instanceFamily and deviceName
    _replicaSetHardwareDao.setInstanceOSSwap(fooReplicaSetHardwareId, instanceId, false, true);
    final ReplicaSetHardware replicaSetHardwareSetToTrueWithoutInstanceFamilyUpdated =
        _replicaSetHardwareDao.findById(fooReplicaSetHardwareId).get();
    final InstanceHardware hardwareSetToTrueWithoutInstanceFamilyUpdated =
        replicaSetHardwareSetToTrueWithoutInstanceFamilyUpdated.getHardware().get(0);
    assertTrue(hardwareSetToTrueWithoutInstanceFamilyUpdated.needsOSSwap());
    assertEquals(
        "M6G",
        hardwareSetToTrueWithoutInstanceFamilyUpdated.toDBObject().getString("instanceFamily"));
    assertEquals(
        "/dev/xvdb",
        hardwareSetToTrueWithoutInstanceFamilyUpdated.toDBObject().getString("diskDeviceName"));

    // testing on internal instance
    final String clusterNameInternal = "bar";
    final ObjectId replicationSpecIdInternal = new ObjectId();
    final BasicDBObject barReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterNameInternal, groupId, index);
    _replicaSetHardwareDao.create(
        barReplicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterNameInternal, index),
        true,
        false,
        replicationSpecIdInternal);
    final ObjectId instanceIdInternal =
        _replicaSetHardwareDao.addInstance(barReplicaSetHardwareId, CloudProvider.AWS, true, 0);

    final ReplicaSetHardware replicaSetInternalHardwareOriginalState =
        _replicaSetHardwareDao.findById(barReplicaSetHardwareId).get();
    InstanceHardware internalHardwareOriginalState =
        replicaSetInternalHardwareOriginalState.getInternalHardware().get(0);

    assertTrue(!internalHardwareOriginalState.needsOSSwap());

    final Date newDate_M30_M5_bar = new Date();
    _replicaSetHardwareDao.setInstanceOSSwap(
        barReplicaSetHardwareId,
        instanceIdInternal,
        true,
        true,
        Optional.of(Pair.of("instanceSize", "M30")),
        Optional.of(Pair.of("lastInstanceSizeModifyDate", newDate_M30_M5_bar)),
        Optional.of(Pair.of("instanceFamily", "M5")),
        Optional.of(
            Pair.of(
                "os",
                CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                    .getInstanceHardwareProvider()
                    .getDefaultOs(_appSettings, AWSInstanceFamily.M5))),
        Optional.of(Pair.of("diskDeviceName", "/dev/xvdb")));

    final ReplicaSetHardware replicaSetInternalHardware =
        _replicaSetHardwareDao.findById(barReplicaSetHardwareId).get();
    final InstanceHardware internalHardware =
        replicaSetInternalHardware.getInternalHardware().get(0);

    assertTrue(internalHardware.needsOSSwap());
    assertEquals("M30", internalHardware.toDBObject().getString("instanceSize"));
    assertEquals("M5", internalHardware.toDBObject().getString("instanceFamily"));
    assertEquals("/dev/xvdb", internalHardware.toDBObject().getString("diskDeviceName"));
    assertEquals(
        newDate_M30_M5_bar, internalHardware.toDBObject().getDate("lastInstanceSizeModifyDate"));

    final Date newDate_M30_M6G_bar = new Date();
    _replicaSetHardwareDao.setInstanceOSSwap(
        barReplicaSetHardwareId,
        instanceIdInternal,
        true,
        false,
        Optional.of(Pair.of("instanceSize", "M30")),
        Optional.of(Pair.of("lastInstanceSizeModifyDate", newDate_M30_M6G_bar)),
        Optional.of(Pair.of("instanceFamily", "M6G")),
        Optional.of(
            Pair.of(
                "os",
                CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                    .getInstanceHardwareProvider()
                    .getDefaultOs(_appSettings, AWSInstanceFamily.M6G))),
        Optional.of(Pair.of("diskDeviceName", "/dev/xvdb")));

    final ReplicaSetHardware replicaSetInternalHardwareSetToFalse =
        _replicaSetHardwareDao.findById(barReplicaSetHardwareId).get();
    final InstanceHardware internalHardwareSetToFalse =
        replicaSetInternalHardwareSetToFalse.getInternalHardware().get(0);

    assertFalse(internalHardwareSetToFalse.needsOSSwap());
    assertEquals("M30", internalHardwareSetToFalse.toDBObject().getString("instanceSize"));
    assertEquals("M6G", internalHardwareSetToFalse.toDBObject().getString("instanceFamily"));
    assertEquals("/dev/xvdb", internalHardwareSetToFalse.toDBObject().getString("diskDeviceName"));
    assertEquals(
        newDate_M30_M6G_bar,
        internalHardwareSetToFalse.toDBObject().getDate("lastInstanceSizeModifyDate"));
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetInstanceRequestServerReboot() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    _replicaSetHardwareDao.setInstanceRequestServerReboot(
        replicaSetHardwareId,
        instanceId,
        false,
        new Date(),
        false,
        "hash1",
        RebootRequestedBy.CHEF);

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);

    assertTrue(hardware.getRebootRequestedDate().isPresent());
    assertFalse(hardware.needsCriticalReboot());
    assertEquals("hash1", hardware.getRebootRequestedChefCommitHash().get());

    // Test new date is not set if the previous request was not processed
    _replicaSetHardwareDao.setInstanceRequestServerReboot(
        replicaSetHardwareId, instanceId, false, new Date(), false, null, RebootRequestedBy.OTHER);

    final ReplicaSetHardware replicaSetHardware2 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware2 = replicaSetHardware2.getHardware().get(0);

    assertTrue(hardware2.getRebootRequestedDate().isPresent());
    assertEquals(hardware.getRebootRequestedDate().get(), hardware2.getRebootRequestedDate().get());

    // New date is set once the previous request is processed
    _replicaSetHardwareDao.setInstanceForceServerRestart(
        replicaSetHardwareId, hardware.getInstanceId(), false);
    final Date date3 = new Date();
    _replicaSetHardwareDao.setInstanceRequestServerReboot(
        replicaSetHardwareId, instanceId, false, date3, false, null, RebootRequestedBy.OTHER);

    final ReplicaSetHardware replicaSetHardware3 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware3 = replicaSetHardware3.getHardware().get(0);
    assertEquals(date3, hardware3.getRebootRequestedDate().get());

    // New date is set if request is critical
    final Date date4 = new Date();
    _replicaSetHardwareDao.setInstanceRequestServerReboot(
        replicaSetHardwareId, instanceId, false, date4, true, null, RebootRequestedBy.OTHER);
    final ReplicaSetHardware replicaSetHardware4 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware4 = replicaSetHardware4.getHardware().get(0);
    assertEquals(date4, hardware4.getRebootRequestedDate().get());
    assertTrue(hardware4.needsCriticalReboot());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndUnsetExternalRequestedRebootDate() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final DBObject id = ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    final Date rebootDate = new Date();
    _replicaSetHardwareDao.setExternalRequestedRebootDate(id, instanceId, false, rebootDate);

    final ReplicaSetHardware replicaSetHardware1 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware1 = replicaSetHardware1.getHardware().get(0);

    assertTrue(hardware1.getExternalRebootRequestedDate().isPresent());
    assertEquals(rebootDate, hardware1.getExternalRebootRequestedDate().get());

    _replicaSetHardwareDao.unsetExternalRequestedRebootDate(id, instanceId, false);

    final ReplicaSetHardware replicaSetHardware2 =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware2 = replicaSetHardware2.getHardware().get(0);

    assertFalse(hardware2.getExternalRebootRequestedDate().isPresent());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetInstanceRequestServerRestart() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    {
      // a restart has been requested but should not bypass maintenance windows
      _replicaSetHardwareDao.setInstanceRequestServerRestart(
          replicaSetHardwareId, instanceId, false, new Date(), false);

      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
      final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);
      assertTrue(hardware.getRestartRequestedDate().isPresent());
      assertFalse(hardware.needsCriticalReboot());
    }

    {
      // a restart has been requested and should happen immediately
      _replicaSetHardwareDao.setInstanceRequestServerRestart(
          replicaSetHardwareId, instanceId, false, new Date(), true);

      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
      final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);
      assertTrue(hardware.getRestartRequestedDate().isPresent());
      assertTrue(hardware.needsCriticalReboot());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndUnsetForceStopStartVM() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    final Supplier<InstanceHardware> getHardware =
        () -> _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware().get(0);

    {
      final InstanceHardware hardware = getHardware.get();
      assertFalse(hardware.needsForceStopStartVM());
      assertEquals(List.of(), hardware.getForceStopStartVMSources());
    }

    {
      _replicaSetHardwareDao.setInstanceForceStopStartVM(
          replicaSetHardwareId, instanceId, false, ForceStopStartVMSource.QUEUED_ADMIN_JOB);
      final InstanceHardware hardware = getHardware.get();
      assertTrue(hardware.needsForceStopStartVM());
      assertEquals(
          List.of(ForceStopStartVMSource.QUEUED_ADMIN_JOB), hardware.getForceStopStartVMSources());
    }

    {
      // NOTE: Testing that the (de)serialization of the sources field handles value properly.
      final InstanceHardware hardware = getHardware.get().copy().build();
      assertTrue(hardware.needsForceStopStartVM());
      assertEquals(
          List.of(ForceStopStartVMSource.QUEUED_ADMIN_JOB), hardware.getForceStopStartVMSources());
    }

    {
      _replicaSetHardwareDao.setInstanceForceStopStartVM(
          replicaSetHardwareId, instanceId, false, ForceStopStartVMSource.SYSTEM_HEALTH);
      final InstanceHardware hardware = getHardware.get();
      assertTrue(hardware.needsForceStopStartVM());
      assertEquals(
          List.of(ForceStopStartVMSource.QUEUED_ADMIN_JOB, ForceStopStartVMSource.SYSTEM_HEALTH),
          hardware.getForceStopStartVMSources());
    }

    {
      _replicaSetHardwareDao.unsetInstanceForceStopStartVM(replicaSetHardwareId, instanceId, false);
      final InstanceHardware hardware = getHardware.get();
      assertFalse(hardware.needsForceStopStartVM());
      assertEquals(List.of(), hardware.getForceStopStartVMSources());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndUnsetForceResync() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    final Supplier<InstanceHardware> getHardware =
        () -> _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware().get(0);
    final Runnable setAll =
        () -> {
          _replicaSetHardwareDao.setInstanceForceServerResync(
              replicaSetHardwareId, instanceId, false);
        };

    final Consumer<Boolean> unset =
        (final Boolean pResync) ->
            _replicaSetHardwareDao.unsetInstanceForceServerResync(
                replicaSetHardwareId, instanceId, false);
    {
      setAll.run();
      final InstanceHardware hardware = getHardware.get();
      assertTrue(hardware.needsForceServerResync());
    }

    {
      setAll.run();
      unset.accept(true);
      final InstanceHardware hardware = getHardware.get();
      assertFalse(hardware.needsForceServerResync());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testUnsetInstanceRebootOrRestart() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final Runnable setAll =
        () -> {
          _replicaSetHardwareDao.setInstanceRequestServerRestart(
              replicaSetHardwareId, instanceId, false, new Date(), false);
          _replicaSetHardwareDao.setInstanceRequestServerReboot(
              replicaSetHardwareId,
              instanceId,
              false,
              new Date(),
              false,
              null,
              RebootRequestedBy.OTHER);
          _replicaSetHardwareDao.setInstanceForceServerRestart(
              replicaSetHardwareId, instanceId, false);
        };
    final Supplier<InstanceHardware> getHardware =
        () -> _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware().get(0);
    final Consumer<Boolean> unset =
        (final Boolean pReboot) ->
            _replicaSetHardwareDao.unsetInstanceRebootAndRestart(
                replicaSetHardwareId, instanceId, false, pReboot);

    {
      // Server was rebooted, so mongod was always restarted
      setAll.run();
      unset.accept(true);
      final InstanceHardware hardware = getHardware.get();
      assertFalse(hardware.needsCriticalReboot());
      // Reboot requested date is not unset by this function
      assertTrue(hardware.getRebootRequestedDate().isPresent());
      assertTrue(hardware.getRestartRequestedDate().isEmpty());
    }
    {
      // Unset only restart since server was not rebooted
      setAll.run();
      unset.accept(false);
      final InstanceHardware hardware = getHardware.get();
      assertFalse(hardware.needsCriticalReboot());
      assertTrue(hardware.getRebootRequestedDate().isPresent());
      assertTrue(hardware.getRestartRequestedDate().isEmpty());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testUnsetInstanceRebootRequestedDate() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final Date requestedRebootDate = new Date();

    final Runnable setAll =
        () ->
            _replicaSetHardwareDao.setInstanceRequestServerReboot(
                replicaSetHardwareId,
                instanceId,
                false,
                requestedRebootDate,
                false,
                null,
                RebootRequestedBy.OTHER);
    final Supplier<InstanceHardware> getHardware =
        () -> _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware().get(0);

    {
      setAll.run();
      assertTrue(
          _replicaSetHardwareDao.unsetInstanceRebootRequestProperties(
              replicaSetHardwareId, instanceId, false, requestedRebootDate));
      final InstanceHardware hardware = getHardware.get();
      assertFalse(hardware.getRebootRequestedDate().isPresent());
    }
    {
      final Date mismatchedDate = new Date();
      setAll.run();
      assertFalse(
          _replicaSetHardwareDao.unsetInstanceRebootRequestProperties(
              replicaSetHardwareId, instanceId, false, mismatchedDate));
      final InstanceHardware hardware = getHardware.get();
      assertTrue(hardware.getRebootRequestedDate().isPresent());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndUnsetNeedsReloadSslOnProcesses() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    // Should always be unset on new hardware
    {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
      assertFalse(replicaSetHardware.getHardware().get(0).needsReloadSslOnProcesses());
    }

    {
      _replicaSetHardwareDao.setNeedsReloadSslOnProcesses(replicaSetHardwareId, instanceId, false);

      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
      assertTrue(replicaSetHardware.getHardware().get(0).needsReloadSslOnProcesses());
    }

    {
      _replicaSetHardwareDao.unsetNeedsReloadSslOnProcesses(
          replicaSetHardwareId, instanceId, false);

      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
      assertFalse(replicaSetHardware.getHardware().get(0).needsReloadSslOnProcesses());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndClearReloadSslOnProcessesRequestedDate() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    // Set reload SSL on processes requested date
    final Date reloadSslDate = new Date();
    _replicaSetHardwareDao.setReloadSslOnProcessesRequestedDate(
        replicaSetHardwareId, instanceId, false, reloadSslDate);

    // Verify the date is set
    assertTrue(
        _replicaSetHardwareDao
            .findById(replicaSetHardwareId)
            .map(ReplicaSetHardware::getHardware)
            .map(h -> h.get(0))
            .flatMap(InstanceHardware::getReloadSslOnProcessesRequestedDate)
            .filter(reloadSslDate::equals)
            .isPresent());

    // Clear reload SSL on processes requested date
    _replicaSetHardwareDao.clearReloadSslOnProcessesRequestedDate(
        replicaSetHardwareId, instanceId, false);

    // Verify the date is cleared
    assertFalse(
        _replicaSetHardwareDao
            .findById(replicaSetHardwareId)
            .map(ReplicaSetHardware::getHardware)
            .map(h -> h.get(0))
            .flatMap(InstanceHardware::getReloadSslOnProcessesRequestedDate)
            .isPresent());
  }

  @Test
  public void testModifyInstanceIsPaused_Pause() {
    testModifyIsPaused(true);
  }

  @Test
  public void testModifyInstanceIsPaused_Resume() {
    testModifyIsPaused(false);
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  private void testModifyIsPaused(final boolean pIsPaused) {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    _replicaSetHardwareDao.modifyInstancePauseState(
        replicaSetHardwareId, instanceId, false, pIsPaused);

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);

    assertEquals(pIsPaused, hardware.isPaused());
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindByHostname() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "foo";
    final ReplicaSetType replicaSetType = ReplicaSetType.SHARD;
    final int rsIndex = 0;
    final String hostname =
        NDSHostModelTestFactory.getHostname(clusterName, replicaSetType, rsIndex, 0, false);

    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, rsIndex);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, rsIndex),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    final Hostnames hostnames = new Hostnames(hostname);

    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        hostnames.toDBList());

    assertTrue(_replicaSetHardwareDao.findByHostname(hostname, null).isPresent());
    assertTrue(_replicaSetHardwareDao.findByHostname(hostname, groupId).isPresent());
    assertFalse(_replicaSetHardwareDao.findByHostname("iam.afakehostfor.us", groupId).isPresent());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testDeleteUnProvisionedHardware() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    final String clusterName = "foo";
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0),
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        specId);

    final BasicDBObject query =
        new BasicDBObject(
            "_id",
            new BasicDBObject()
                .append("clusterName", clusterName)
                .append("groupId", groupId)
                .append("type", ReplicaSetType.SHARD.name())
                .append("index", 0));

    assertNotNull(_replicaSetHardwareDao.getDbCollection().findOne(query));

    _replicaSetHardwareDao.deleteHardware(
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 1));

    assertNotNull(_replicaSetHardwareDao.getDbCollection().findOne(query));

    _replicaSetHardwareDao.deleteHardware(
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0));

    assertNull(_replicaSetHardwareDao.getDbCollection().findOne(query));
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetCloudProviderHardware() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    final String clusterName = "foo";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        specId);

    final ObjectId instance1Id = new ObjectId();
    final ObjectId instance2Id = new ObjectId();
    final ObjectId instance3Id = new ObjectId();

    final BasicDBObject instanceHardware1 =
        InstanceHardware.getEmptyHardware(CloudProvider.AWS, instance1Id, new Date(), 0);
    final BasicDBObject instanceHardware2 =
        InstanceHardware.getEmptyHardware(CloudProvider.AWS, instance2Id, new Date(), 1);
    final BasicDBObject instanceHardware3 =
        InstanceHardware.getEmptyHardware(CloudProvider.AWS, instance3Id, new Date(), 2);

    final List<InstanceHardware> savedHardwareList =
        List.of(
            new AWSInstanceHardware(instanceHardware1),
            new AWSInstanceHardware(instanceHardware2),
            new AWSInstanceHardware(instanceHardware3));

    // update succeeds for empty existing hardware when passing in empty existing hardware
    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), savedHardwareList, false));

    final List<InstanceHardware> hardwareList =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware();

    assertEquals(3, hardwareList.size());
    assertEquals(instance1Id, hardwareList.get(0).getInstanceId());
    assertEquals(instance2Id, hardwareList.get(1).getInstanceId());
    assertEquals(instance3Id, hardwareList.get(2).getInstanceId());

    final List<InstanceHardware> reorderedHardwareList =
        List.of(
            new AWSInstanceHardware(instanceHardware2),
            new AWSInstanceHardware(instanceHardware3),
            new AWSInstanceHardware(instanceHardware1));

    // update fails when passing in non updated hardware list
    assertFalse(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), reorderedHardwareList, false));

    final List<InstanceHardware> nonUpdatedHardwareList =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware();

    assertEquals(3, hardwareList.size());
    assertEquals(instance1Id, nonUpdatedHardwareList.get(0).getInstanceId());
    assertEquals(instance2Id, nonUpdatedHardwareList.get(1).getInstanceId());
    assertEquals(instance3Id, nonUpdatedHardwareList.get(2).getInstanceId());

    // update succeeds when passing in up to date hardware
    final BasicDBList savedHardwareListObject =
        (BasicDBList)
            _replicaSetHardwareDao
                .findByClusterAsDBObject(groupId, clusterName)
                .get(0)
                .get(ReplicaSetHardware.FieldDefs.CLOUD_PROVIDER_HARDWARE);

    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, savedHardwareListObject, reorderedHardwareList, false));

    final List<InstanceHardware> updatedHardwareList =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware();

    assertEquals(3, hardwareList.size());
    assertEquals(instance2Id, updatedHardwareList.get(0).getInstanceId());
    assertEquals(instance3Id, updatedHardwareList.get(1).getInstanceId());
    assertEquals(instance1Id, updatedHardwareList.get(2).getInstanceId());

    // update fails when passing in non-empty hardware list to match empty hardware list
    assertFalse(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, savedHardwareListObject, reorderedHardwareList, true));

    final List<InstanceHardware> internalHardwareListNonUpdate =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getInternalHardware();

    assertEquals(0, internalHardwareListNonUpdate.size());
  }

  @Test
  @SuppressWarnings("deprecation")
  public void testFindByPublicIPAddress() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    final String clusterName = "foo";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        specId);

    final InstanceHardware instanceHardware0 =
        InstanceHardware.getHardware(
            InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                "host0.com", ObjectId.get(), 0));
    final InstanceHardware instanceHardware1 =
        InstanceHardware.getHardware(
            InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                "host1.com", ObjectId.get(), 0));

    // update succeeds for empty existing hardware when passing in empty existing hardware
    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), List.of(instanceHardware0), false));
    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), List.of(instanceHardware1), true));

    final Optional<ReplicaSetHardware> hardwareByCloudProviderHardwarePublicIP =
        _replicaSetHardwareDao.findByPublicIPAddress(instanceHardware0.getPublicIP().get());
    assertTrue(hardwareByCloudProviderHardwarePublicIP.isPresent());
    assertEquals(specId, hardwareByCloudProviderHardwarePublicIP.get().getReplicationSpecId());

    final Optional<ReplicaSetHardware> hardwareByCloudProviderInternalHardwarePublicIP =
        _replicaSetHardwareDao.findByPublicIPAddress(instanceHardware1.getPublicIP().get());
    assertTrue(hardwareByCloudProviderInternalHardwarePublicIP.isPresent());
    assertEquals(
        specId, hardwareByCloudProviderInternalHardwarePublicIP.get().getReplicationSpecId());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testFindDedicatedByPublicIPAddress() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    final String clusterName = "foo";
    final String freeClusterName = "bar";
    final BasicDBObject fooReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    final BasicDBObject freeReplicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(freeClusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        fooReplicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        specId);
    _replicaSetHardwareDao.create(
        freeReplicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(freeClusterName, 0),
        true,
        false,
        specId);

    final InstanceHardware instanceHardware0 =
        InstanceHardware.getHardware(
            InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                "host0.com", ObjectId.get(), 0));
    final InstanceHardware instanceHardware1 =
        InstanceHardware.getHardware(
            InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                "host1.com", ObjectId.get(), 0));
    final InstanceHardware freeInstanceHardware =
        InstanceHardware.getHardware(
            InstanceHardwareModelTestFactory.getFreeInstanceHardwareFull(
                "host2.com", "host2.com", ObjectId.get(), 0));

    // update succeeds for empty existing hardware when passing in empty existing hardware
    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            fooReplicaSetHardwareId, new BasicDBList(), List.of(instanceHardware0), false));
    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            fooReplicaSetHardwareId, new BasicDBList(), List.of(instanceHardware1), true));
    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            freeReplicaSetHardwareId, new BasicDBList(), List.of(freeInstanceHardware), false));

    final Optional<ReplicaSetHardware> hardwareByCloudProviderHardwarePublicIP =
        _replicaSetHardwareDao.findDedicatedByPublicIPAddress(
            instanceHardware0.getPublicIP().get(), null);
    assertTrue(hardwareByCloudProviderHardwarePublicIP.isPresent());
    assertEquals(specId, hardwareByCloudProviderHardwarePublicIP.get().getReplicationSpecId());

    final Optional<ReplicaSetHardware> hardwareByCloudProviderInternalHardwarePublicIP =
        _replicaSetHardwareDao.findDedicatedByPublicIPAddress(
            instanceHardware1.getPublicIP().get(), groupId);
    assertTrue(hardwareByCloudProviderInternalHardwarePublicIP.isPresent());
    assertEquals(
        specId, hardwareByCloudProviderInternalHardwarePublicIP.get().getReplicationSpecId());

    final Optional<ReplicaSetHardware> freeCloudProviderHardwareByPublicIP =
        _replicaSetHardwareDao.findDedicatedByPublicIPAddress(
            freeInstanceHardware.getPublicIP().get(), null);
    assertTrue(freeCloudProviderHardwareByPublicIP.isEmpty());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetInstanceUseCertificateManagementFields() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final ReplicaSetHardware replicaSetHardwarePre =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware instanceHardwarePre = replicaSetHardwarePre.getHardware().get(0);

    assertFalse(instanceHardwarePre.getConfigNeedsUpdateAfter().isPresent());
    assertFalse(instanceHardwarePre.getRotateSslAfter().isPresent());

    final Date configLastUpdatedDate = new Date(42L);
    final Date rotateSSLAfterDate = new Date(90_000_000L);
    final Date rotateSSLCriticalDate = new Date(80_000_00L);
    _replicaSetHardwareDao.setInstanceUseCertificateManagementFields(
        replicaSetHardwareId,
        instanceId,
        false,
        configLastUpdatedDate,
        rotateSSLAfterDate,
        rotateSSLCriticalDate);

    final ReplicaSetHardware replicaSetHardwarePost =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware instanceHardwarePost = replicaSetHardwarePost.getHardware().get(0);

    assertEquals(configLastUpdatedDate, instanceHardwarePost.getConfigNeedsUpdateAfter().get());
    assertEquals(rotateSSLAfterDate, instanceHardwarePost.getRotateSslAfter().get());
    assertEquals(rotateSSLCriticalDate, instanceHardwarePost.getRotateSslCritical().get());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSetAndUnsetProcessRestartRequestedDate() {
    final int index = 0;
    final ObjectId groupId = ObjectId.get();
    final String clusterName = "UnsetMePlz";

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        ObjectId.get());

    for (int i = 0; i < 3; i++) {
      _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, i);
    }

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();

    assertTrue(replicaSetHardware.getAllHardware().findFirst().isPresent());

    // Verify that we can serialize/deserialize each instance hardware.
    replicaSetHardware.getAllHardware().forEach(h -> new AWSInstanceHardware(h.toDBObject()));

    NDSProcessType.valuesAsList()
        .forEach(
            pt -> {
              final Date requestedDate = new Date();

              _replicaSetHardwareDao.setProcessRestartRequestedDateAndTaskToRun(
                  replicaSetHardware.getId(),
                  replicaSetHardware.getHardware().get(0).getInstanceId(),
                  false,
                  pt,
                  NDSProcessType.ProcessRestartTaskToRun.NONE,
                  requestedDate);

              // Verify the date gets set for the correct process type
              final ReplicaSetHardware replicaSetHardwareAfterSet =
                  _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();
              final Map<NDSProcessType, Date> processRestartRequestedDates =
                  replicaSetHardwareAfterSet.getHardware().get(0).getProcessRestartRequestedDates();
              assertTrue(processRestartRequestedDates.containsKey(pt));
              assertEquals(requestedDate, processRestartRequestedDates.get(pt));

              final Map<NDSProcessType, NDSProcessType.ProcessRestartTaskToRun>
                  processRestartTasksToRun =
                      replicaSetHardwareAfterSet.getHardware().get(0).getProcessRestartTasksToRun();
              assertTrue(processRestartTasksToRun.containsKey(pt));
              assertEquals(
                  NDSProcessType.ProcessRestartTaskToRun.NONE, processRestartTasksToRun.get(pt));

              // Verify other hardware wasn't affected by set above
              IntStream.range(1, replicaSetHardwareAfterSet.getHardware().size())
                  .mapToObj(replicaSetHardware.getHardware()::get)
                  .forEach(
                      instanceHardwareAfterSet -> {
                        assertEquals(
                            Map.of(), instanceHardwareAfterSet.getProcessRestartRequestedDates());
                        assertEquals(
                            Map.of(), instanceHardwareAfterSet.getProcessRestartTasksToRun());
                      });

              // Verify that we can serialize/deserialize each instance hardware.
              replicaSetHardwareAfterSet
                  .getAllHardware()
                  .forEach(h -> new AWSInstanceHardware(h.toDBObject()));

              // Verify the date gets unset for the correct process type - fields are null after
              // unset
              _replicaSetHardwareDao.unsetProcessRestartRequestedDate(
                  replicaSetHardwareId,
                  replicaSetHardware.getHardware().get(0).getInstanceId(),
                  false,
                  pt);
              final ReplicaSetHardware replicaSetHardwareAfterUnset =
                  _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();

              final InstanceHardware instanceHardwareUnset =
                  replicaSetHardwareAfterUnset.getHardware().get(0);
              final Map<NDSProcessType, Date> processRestartRequestedDatesAfterUnset =
                  instanceHardwareUnset.getProcessRestartRequestedDates();
              assertTrue(processRestartRequestedDatesAfterUnset.containsKey(pt));
              assertNull(processRestartRequestedDatesAfterUnset.get(pt));

              final Map<NDSProcessType, NDSProcessType.ProcessRestartTaskToRun>
                  processRestartTasksToRunAfterUnset =
                      instanceHardwareUnset.getProcessRestartTasksToRun();
              assertTrue(processRestartTasksToRunAfterUnset.containsKey(pt));
              assertNull(processRestartTasksToRunAfterUnset.get(pt));

              // Verify other hardware wasn't affected by unset
              IntStream.range(1, replicaSetHardwareAfterUnset.getHardware().size())
                  .mapToObj(replicaSetHardware.getHardware()::get)
                  .forEach(
                      instanceHardwareAfterUnset -> {
                        assertEquals(
                            Map.of(), instanceHardwareAfterUnset.getProcessRestartRequestedDates());
                        assertEquals(
                            Map.of(), instanceHardwareAfterUnset.getProcessRestartTasksToRun());
                      });

              // Verify that we can serialize/deserialize each instance hardware.
              replicaSetHardwareAfterUnset
                  .getAllHardware()
                  .forEach(h -> new AWSInstanceHardware(h.toDBObject()));
            });
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testCancelNeedsUngracefulDisconnectDate() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup(groupId);
    final String clusterName = "foo";
    final int index = 0;
    final ObjectId replicationSpecId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, index);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, index),
        true,
        false,
        replicationSpecId);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final ReplicaSetHardware replicaSetHardwarePre =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get();
    final InstanceHardware instanceHardwarePre = replicaSetHardwarePre.getHardware().get(0);

    assertEquals(Optional.empty(), instanceHardwarePre.getNeedsUngracefulDisconnectDate());
    final Date dateToSet = new Date();
    _replicaSetHardwareDao.setNeedsUngracefulDisconnectDate(
        replicaSetHardwarePre.getId(), instanceId, false, dateToSet);

    final InstanceHardware instanceHardwarePostSet =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware().get(0);

    assertEquals(
        Optional.of(dateToSet), instanceHardwarePostSet.getNeedsUngracefulDisconnectDate());

    final Optional<Date> date =
        _replicaSetHardwareDao.cancelNeedsUngracefulDisconnectDate(
            replicaSetHardwarePre.getId(), instanceId, false);

    assertEquals(Optional.of(dateToSet), date);

    final InstanceHardware instanceHardwarePostCancel =
        _replicaSetHardwareDao.findById(replicaSetHardwareId).get().getHardware().get(0);

    assertEquals(Optional.empty(), instanceHardwarePostCancel.getNeedsUngracefulDisconnectDate());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testFindReplicaSetHardwareByShardIndex() {
    final ObjectId groupId = ObjectId.get();
    final String clusterName = "cluster";
    final ObjectId rsId0 = ObjectId.get();
    final ObjectId rsConfigId = ObjectId.get();

    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0),
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        rsId0);
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createConfigReplicaSetId(clusterName, groupId),
        String.format("%s-config-0", clusterName),
        false,
        true,
        rsConfigId);

    assertEquals(
        rsId0,
        _replicaSetHardwareDao
            .findReplicaSetHardwareByShardIndex(groupId, clusterName, 0, false)
            .orElseThrow()
            .getReplicationSpecId());
    assertEquals(
        rsConfigId,
        _replicaSetHardwareDao
            .findReplicaSetHardwareByShardIndex(groupId, clusterName, 3, true)
            .orElseThrow()
            .getReplicationSpecId());
    assertEquals(
        Optional.empty(),
        _replicaSetHardwareDao.findReplicaSetHardwareByShardIndex(
            groupId, "nonExistentCluster", 3, true));
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void test_setMidDestroyingFields() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    _replicaSetHardwareDao.create(
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0),
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        specId);
    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findByCluster(groupId, "foo").get(0);
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardware.getId(), CloudProvider.AWS, false, 0);

    _replicaSetHardwareDao.setInstanceFields(
        replicaSetHardware.getId(),
        instanceId,
        false,
        Arrays.asList(Pair.of(InstanceHardware.FieldDefs.PUBLIC_IP, "*******")));

    final InstanceHardware instanceHardwareWithPublicIP =
        _replicaSetHardwareDao
            .findByCluster(groupId, "foo")
            .get(0)
            .getAllHardware()
            .findFirst()
            .orElseThrow();
    assertEquals("*******", instanceHardwareWithPublicIP.getPublicIP().get());

    _replicaSetHardwareDao.setMidDestroyingFields(replicaSetHardware.getId(), instanceId, false);

    final InstanceHardware instanceHardwareWithoutPublicIP =
        _replicaSetHardwareDao
            .findByCluster(groupId, "foo")
            .get(0)
            .getAllHardware()
            .findFirst()
            .orElseThrow();
    assertFalse(instanceHardwareWithoutPublicIP.getPublicIP().isPresent());
  }

  @Test
  public void testSetInstanceFieldsUsingATransaction() throws InterruptedException {
    final ObjectId groupId = new ObjectId();
    final ReplicaSetHardware.ReplicaSetHardwareIds rsIds =
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(groupId, "foo", "foo", List.of())
            .next();
    _replicaSetHardwareDao.create(rsIds.id(), rsIds.rsId(), true, false, new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(rsIds.id(), CloudProvider.AZURE, false, 0);
    _replicaSetHardwareDao.setInstanceFields(
        rsIds.id(),
        instanceId,
        false,
        List.of(Pair.of(FieldDefs.DISK_SIZE_GB, 0), Pair.of(FieldDefs.PUBLIC_IP, "")));

    { // check pre-conditions
      final ReplicaSetHardware hardwareBefore =
          _replicaSetHardwareDao.findById(rsIds.id()).orElseThrow();
      final AzureInstanceHardware instanceBefore =
          (AzureInstanceHardware) hardwareBefore.getHardware().get(0);
      Assertions.assertEquals(Optional.of(0), instanceBefore.getDiskSizeGB());
      Assertions.assertEquals(Optional.of(""), instanceBefore.getPublicIP());
    }

    final CountDownLatch latch = new CountDownLatch(1);

    LOG.info("try to weave two transactions together.  Transaction logic should prevent it");
    final Thread thread1 =
        new Thread(
            () -> {
              LOG.info("transaction 1 starts first");
              _replicaSetHardwareDao.setInstanceFieldsUsingATransaction(
                  rsIds.id(),
                  instanceId,
                  FunctionsEx.unchecked(
                      (rsh) -> {
                        LOG.info("pause mid-transaction 1 and let second transaction try to start");
                        final InstanceHardware instanceHardware = rsh.getHardware().get(0);
                        latch.countDown();
                        Thread.sleep(100);
                        LOG.info("finish transaction 1");
                        return List.of(
                            Pair.of(
                                FieldDefs.DISK_SIZE_GB,
                                instanceHardware.getDiskSizeGB().orElseThrow() + 1),
                            Pair.of(
                                FieldDefs.PUBLIC_IP,
                                instanceHardware.getPublicIP().orElseThrow() + "yo"));
                      }));
            });

    thread1.start();
    LOG.info("wait to ensure that the second transaction starts mid-way through the first");
    latch.await();

    LOG.info("transaction 2 should start mid transaction 1");
    _replicaSetHardwareDao.setInstanceFieldsUsingATransaction(
        rsIds.id(),
        instanceId,
        (rsh) -> {
          final InstanceHardware instanceHardware = rsh.getHardware().get(0);
          LOG.info("finish transaction 2");
          return List.of(
              Pair.of(FieldDefs.DISK_SIZE_GB, instanceHardware.getDiskSizeGB().orElseThrow() + 1),
              Pair.of(FieldDefs.PUBLIC_IP, instanceHardware.getPublicIP().orElseThrow() + "yo"));
        });

    thread1.join();

    { // check post-conditions
      final ReplicaSetHardware hardwareAfter =
          _replicaSetHardwareDao.findById(rsIds.id()).orElseThrow();
      final AzureInstanceHardware instanceAfter =
          (AzureInstanceHardware) hardwareAfter.getHardware().get(0);
      Assertions.assertEquals(Optional.of(2), instanceAfter.getDiskSizeGB());
      Assertions.assertEquals(Optional.of("yoyo"), instanceAfter.getPublicIP());
    }
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testSchedulingAndUnschedulingDiskCompactionResyncs() {
    final ObjectId groupId = new ObjectId();
    final ObjectId specId = new ObjectId();
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId("foo", groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName("foo", 0),
        true,
        false,
        specId);
    _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 1);
    _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, true, 2);
    _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, true, 3);

    { // test pre-conditions
      final ReplicaSetHardware updatedReplicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();
      assertTrue(
          updatedReplicaSetHardware
              .getAllHardware()
              .map(InstanceHardware::getNeedsDiskCompactionResyncDate)
              .allMatch(Optional::isEmpty));
    }

    _replicaSetHardwareDao.scheduleDiskCompactionResyncOfReplicaSet(replicaSetHardwareId);

    { // all instances should need re-sync now
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();
      assertTrue(
          replicaSetHardware
              .getAllHardware()
              .map(InstanceHardware::getNeedsDiskCompactionResyncDate)
              .allMatch(Optional::isPresent));
    }

    { // unset needs disk compaction for one internal instance
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();
      _replicaSetHardwareDao.unsetNeedsDiskCompactionResync(
          replicaSetHardwareId,
          replicaSetHardware.getInternalHardware().get(0).getInstanceId(),
          true);
    }

    { // all need re-sync except for one internal hardware instance
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();
      assertTrue(
          replicaSetHardware.getHardware().stream()
              .map(InstanceHardware::getNeedsDiskCompactionResyncDate)
              .allMatch(Optional::isPresent));
      assertEquals(
          Optional.empty(),
          replicaSetHardware.getInternalHardware().get(0).getNeedsDiskCompactionResyncDate());
      assertNotEquals(
          Optional.empty(),
          replicaSetHardware.getInternalHardware().get(1).getNeedsDiskCompactionResyncDate());
    }
  }
}
