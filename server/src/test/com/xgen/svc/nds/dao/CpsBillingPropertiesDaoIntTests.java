package com.xgen.svc.nds.dao;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.cps.billing._private.dao.CpsBillingPropertiesDao;
import com.xgen.cloud.cps.billing._public.model.CpsBillingProperty;
import com.xgen.cloud.cps.billing._public.model.CpsBillingProperty.FieldDefs;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CpsBillingPropertiesDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private CpsBillingPropertiesDao _cpsBillingPropertiesDao;
  private static final CpsBillingProperty bp1 =
      new CpsBillingProperty(
          CpsBillingPropertiesDao.OPLOG_USAGE_COLLECTION, LocalDate.parse("2023-03-20"));
  private static final CpsBillingProperty bp2 =
      new CpsBillingProperty(
          CpsBillingPropertiesDao.AWS_SNAPSHOT_USAGE_SUBMISSION, LocalDate.parse("2023-03-20"));

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    // clear if it exists
    _cpsBillingPropertiesDao.getDbCollection().drop();
    // ensure indexes exist after drop
    _cpsBillingPropertiesDao.ensureIndexes();

    BasicDBObject obj1 =
        new BasicDBObject(CpsBillingProperty.FieldDefs.ID, bp1.getId())
            .append(FieldDefs.LAST_SUCCESSFUL_DATE, bp1.getLastSuccessfulDate());
    _cpsBillingPropertiesDao.insertMajority(obj1);

    BasicDBObject obj2 =
        new BasicDBObject(CpsBillingProperty.FieldDefs.ID, bp2.getId())
            .append(FieldDefs.LAST_SUCCESSFUL_DATE, bp2.getLastSuccessfulDate());
    _cpsBillingPropertiesDao.insertMajority(obj2);
  }

  @Test
  public void testFindOneById_success() {
    Optional<CpsBillingProperty> result =
        _cpsBillingPropertiesDao.findOneById(CpsBillingPropertiesDao.AWS_SNAPSHOT_USAGE_SUBMISSION);
    assertTrue(result.isPresent());
    assertEquals(bp2.getLastSuccessfulDate(), result.get().getLastSuccessfulDate());
  }

  @Test
  public void testFindOneById_fail() {
    Optional<CpsBillingProperty> result =
        _cpsBillingPropertiesDao.findOneById(
            CpsBillingPropertiesDao.AZURE_SNAPSHOT_USAGE_SUBMISSION);
    assertTrue(result.isEmpty());
  }

  @Test
  public void testFindAllCpsBillingProperties() {
    List<CpsBillingProperty> result = _cpsBillingPropertiesDao.findAllCpsBillingProperties();
    assertEquals(2, result.size());
    assertThat(
        List.of(bp1.getId(), bp2.getId()),
        containsInAnyOrder(result.get(0).getId(), result.get(1).getId()));
    assertThat(
        List.of(bp1.getLastSuccessfulDate(), bp2.getLastSuccessfulDate()),
        containsInAnyOrder(
            result.get(0).getLastSuccessfulDate(), result.get(1).getLastSuccessfulDate()));
  }

  @Test
  public void testUpdateOne() {
    Optional<CpsBillingProperty> previousResult =
        _cpsBillingPropertiesDao.findOneById(CpsBillingPropertiesDao.AWS_SNAPSHOT_USAGE_SUBMISSION);
    assertTrue(previousResult.isPresent());
    LocalDate newDate = previousResult.get().getLastSuccessfulDate().plusDays(10);
    _cpsBillingPropertiesDao.upsertOneMajority(
        CpsBillingPropertiesDao.AWS_SNAPSHOT_USAGE_SUBMISSION, newDate);

    Optional<CpsBillingProperty> updatedResult =
        _cpsBillingPropertiesDao.findOneById(CpsBillingPropertiesDao.AWS_SNAPSHOT_USAGE_SUBMISSION);
    assertTrue(updatedResult.isPresent());
    assertEquals(newDate, updatedResult.get().getLastSuccessfulDate());
  }
}
