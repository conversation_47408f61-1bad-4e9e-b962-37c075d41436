package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.AttachStats;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.RestoreTargetCluster;
import com.xgen.cloud.group._public.model.GroupVisibility;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class BackupRestoreJobDaoAttachStatsIntTests extends JUnit5BaseSvcTest {
  @Inject private BackupRestoreJobDao _backupRestoreJobDao;

  @Test
  public void testUpdateDirectAttachRestoreAttachStats() {
    // create direct attach restore job
    final String hostname = "test-hostname";
    final ObjectId restoreJobId = saveDirectAttachRestoreJob(hostname);

    // update attach stats of restore job
    AttachStats attachStats = getTestAttachStats();
    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname, attachStats);

    // check that attach stats have been updated
    final DirectAttachReplicaSetBackupRestoreJob restoreJob =
        _backupRestoreJobDao.findDirectAttachReplSetJob(restoreJobId);
    attachStats = restoreJob.getAttachStatuses().get(0).getAttachStats();

    assertEquals(1L, attachStats.detachVolumeDurationMilliseconds());
    assertEquals(2L, attachStats.createVolumeFromSnapshotDurationSeconds());
    assertEquals(3L, attachStats.attachVolumeDurationMilliseconds());
    assertEquals(4L, attachStats.mountVolumeWithSnapshotDataDurationMilliseconds());
    assertEquals("gp3", attachStats.diskType());
    assertEquals(5L, attachStats.bounceStopIfUpWithForceKillDurationSeconds());
    assertEquals(6L, attachStats.getDirectAttachFileListDurationSeconds());
    assertEquals(7L, attachStats.diskPreWarmDurationSeconds());
    assertEquals(8.0, attachStats.diskPreWarmThroughputMbPerSecond());

    assertNotNull(attachStats.mongoStartupStats());
    assertNotNull(attachStats.mongoShutdownStats());
    assertEquals(
        1L,
        attachStats
            .mongoStartupStats()
            .get("prepareRsForApplyOps_startEphemeralMongoAsStandaloneForRestore")
            .get("oplogReplayDurationMilliseconds"));
    assertEquals(
        2L,
        attachStats
            .mongoStartupStats()
            .get("prepareRsForApplyOps_startEphemeralMongoAsStandaloneForRestore")
            .get("rollBackToStableDurationMilliseconds"));
    assertEquals(
        3L,
        attachStats
            .mongoStartupStats()
            .get("applyOpsToDesiredTime_startMongoOnEphemeralPortAsStandaloneForRestore")
            .get("oplogReplayDurationMilliseconds"));
    assertEquals(
        4L,
        attachStats
            .mongoStartupStats()
            .get("applyOpsToDesiredTime_startMongoOnEphemeralPortAsStandaloneForRestore")
            .get("rollBackToStableDurationMilliseconds"));
  }

  private static AttachStats getTestAttachStats() {
    final Map<String, Map<String, Long>> startupStats =
        Map.of(
            "prepareRsForApplyOps_startEphemeralMongoAsStandaloneForRestore",
            Map.of(
                "oplogReplayDurationMilliseconds", 1L, "rollBackToStableDurationMilliseconds", 2L),
            "applyOpsToDesiredTime_startMongoOnEphemeralPortAsStandaloneForRestore",
            Map.of(
                "oplogReplayDurationMilliseconds", 3L, "rollBackToStableDurationMilliseconds", 4L));

    final Map<String, Map<String, Long>> shutdownStats =
        Map.of(
            "prepareRsForApplyOps_startEphemeralMongoAsStandaloneForRestoreShutDown",
            Map.of(
                "oplogReplayDurationMilliseconds", 1L, "rollBackToStableDurationMilliseconds", 3L),
            "applyOpsToDesiredTime_startMongoOnEphemeralPortAsStandaloneForRestoreShutDown",
            Map.of(
                "oplogReplayDurationMilliseconds", 1L, "rollBackToStableDurationMilliseconds", 4L));
    // create complete AttachStats object
    return new AttachStats(startupStats, shutdownStats, 1L, 2L, 3L, 4L, "gp3", 5L, 6L, 7L, 8.0);
  }

  @Test
  public void testUpdateDirectAttachRestoreStatsWithNullValues() {
    // create direct attach restore job
    String hostname = "test-hostname";
    final ObjectId restoreJobId = saveDirectAttachRestoreJob(hostname);
    final Map<String, Map<String, Long>> startupStats =
        Map.of(
            "prepareRsForApplyOps_startEphemeralMongoAsStandaloneForRestore",
            Map.of("oplogReplayDurationMilliseconds", 1L));
    AttachStats attachStats =
        new AttachStats(startupStats, null, 1L, null, null, null, null, null, null, null, null);

    // update stats
    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname, attachStats);

    // check that attach stats have been updated
    final DirectAttachReplicaSetBackupRestoreJob restoreJob =
        _backupRestoreJobDao.findDirectAttachReplSetJob(restoreJobId);
    attachStats = restoreJob.getAttachStatuses().get(0).getAttachStats();

    assertEquals(1L, attachStats.detachVolumeDurationMilliseconds());
    assertNotNull(attachStats.mongoStartupStats());
    assertEquals(
        1L,
        attachStats
            .mongoStartupStats()
            .get("prepareRsForApplyOps_startEphemeralMongoAsStandaloneForRestore")
            .get("oplogReplayDurationMilliseconds"));
  }

  @Test
  public void testUpdateDirectAttachRestoreAttachStatsMetrics() {
    // create direct attach restore job with initial attach stats
    final String hostname = "test-hostname";
    final ObjectId restoreJobId = saveDirectAttachRestoreJob(hostname);
    // Set initial attach stats
    final Map<String, Map<String, Long>> initialStartupStats =
        Map.of(
            "seedRestore_restartWithOplogReplayParams",
            Map.of("oplogReplayDurationMs", 1000L, "rollBackToStableDurationMilliseconds", 2000L));
    final AttachStats initialAttachStats =
        new AttachStats(
            initialStartupStats, Map.of(), 100L, 200L, 300L, 400L, "gp3", 500L, 600L, 700L, 8.0);

    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname, initialAttachStats);

    // Update with new metrics - should merge with existing stats
    final AttachStats updateAttachStats =
        new AttachStats(
            null, // mongoStartupStats - not updating
            null, // mongoShutdownStats - not updating
            150L, // detachVolumeDurationMilliseconds - override existing value
            null, // createVolumeFromSnapshotDurationSeconds - not updating
            null, // attachVolumeDurationMilliseconds - not updating
            null, // mountVolumeWithSnapshotDataDurationMilliseconds - not updating
            null, // diskType - not updating
            null, // bounceStopIfUpWithForceKillDurationSeconds - not updating
            null, // getDirectAttachFileListDurationSeconds - not updating
            800L, // diskPreWarmDurationSeconds - override existing value
            null // diskPreWarmThroughputMbPerSecond - not updating
            );

    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname, updateAttachStats);

    // Verify the stats were updated correctly
    final DirectAttachReplicaSetBackupRestoreJob restoreJob =
        _backupRestoreJobDao.findDirectAttachReplSetJob(restoreJobId);
    final AttachStats updatedAttachStats = restoreJob.getAttachStatuses().get(0).getAttachStats();

    // Check that updated metrics have new values
    assertEquals(150L, updatedAttachStats.detachVolumeDurationMilliseconds());
    assertEquals(800L, updatedAttachStats.diskPreWarmDurationSeconds());

    // Check that non-updated metrics retain original values
    assertEquals(200L, updatedAttachStats.createVolumeFromSnapshotDurationSeconds());
    assertEquals(300L, updatedAttachStats.attachVolumeDurationMilliseconds());
    assertEquals(400L, updatedAttachStats.mountVolumeWithSnapshotDataDurationMilliseconds());
    assertEquals("gp3", updatedAttachStats.diskType());
    assertEquals(500L, updatedAttachStats.bounceStopIfUpWithForceKillDurationSeconds());
    assertEquals(600L, updatedAttachStats.getDirectAttachFileListDurationSeconds());
    assertEquals(8.0, updatedAttachStats.diskPreWarmThroughputMbPerSecond());

    // Check that complex nested stats (mongoStartupStats) are preserved
    assertNotNull(updatedAttachStats.mongoStartupStats());
    assertEquals(
        1000L,
        updatedAttachStats
            .mongoStartupStats()
            .get("seedRestore_restartWithOplogReplayParams")
            .get("oplogReplayDurationMs"));
    assertEquals(
        2000L,
        updatedAttachStats
            .mongoStartupStats()
            .get("seedRestore_restartWithOplogReplayParams")
            .get("rollBackToStableDurationMilliseconds"));
  }

  @Test
  public void testUpdateDirectAttachRestoreAttachStatsMetricsByHostname() {
    // create direct attach restore job with multiple attach statuses
    final String hostname1 = "test-hostname-1";
    final String hostname2 = "test-hostname-2";
    final ObjectId restoreJobId = saveDirectAttachRestoreJobWithMultipleHosts(hostname1, hostname2);

    // Set initial attach stats for both hosts
    final AttachStats initialAttachStats1 =
        new AttachStats(Map.of(), Map.of(), 100L, 200L, 300L, 400L, "gp3", 500L, 600L, 700L, 8.0);
    final AttachStats initialAttachStats2 =
        new AttachStats(
            Map.of(), Map.of(), 1000L, 2000L, 3000L, 4000L, "io2", 5000L, 6000L, 7000L, 80.0);

    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname1, initialAttachStats1);
    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname2, initialAttachStats2);

    // Update metrics for only the first host by hostname
    final AttachStats updateAttachStats =
        new AttachStats(
            null, // mongoStartupStats - not updating
            null, // mongoShutdownStats - not updating
            150L, // detachVolumeDurationMilliseconds - override existing value
            null, // createVolumeFromSnapshotDurationSeconds - not updating
            null, // attachVolumeDurationMilliseconds - not updating
            null, // mountVolumeWithSnapshotDataDurationMilliseconds - not updating
            null, // diskType - not updating
            null, // bounceStopIfUpWithForceKillDurationSeconds - not updating
            null, // getDirectAttachFileListDurationSeconds - not updating
            800L, // diskPreWarmDurationSeconds - override existing value
            null // diskPreWarmThroughputMbPerSecond - not updating
            );
    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname1, updateAttachStats);

    // Verify only the first host's stats were updated
    final DirectAttachReplicaSetBackupRestoreJob restoreJob =
        _backupRestoreJobDao.findDirectAttachReplSetJob(restoreJobId);

    // Find the attach status for hostname1
    final AttachStats updatedAttachStats1 =
        restoreJob.getAttachStatuses().stream()
            .filter(status -> hostname1.equals(status.getHostname()))
            .findFirst()
            .orElseThrow()
            .getAttachStats();

    // Find the attach status for hostname2
    final AttachStats unchangedAttachStats2 =
        restoreJob.getAttachStatuses().stream()
            .filter(status -> hostname2.equals(status.getHostname()))
            .findFirst()
            .orElseThrow()
            .getAttachStats();

    // Check that first host's metrics were updated
    assertEquals(150L, updatedAttachStats1.detachVolumeDurationMilliseconds());
    assertEquals(800L, updatedAttachStats1.diskPreWarmDurationSeconds());
    assertEquals(200L, updatedAttachStats1.createVolumeFromSnapshotDurationSeconds()); // unchanged

    // Check that second host's metrics were NOT updated
    assertEquals(1000L, unchangedAttachStats2.detachVolumeDurationMilliseconds());
    assertEquals(7000L, unchangedAttachStats2.diskPreWarmDurationSeconds());
    assertEquals(2000L, unchangedAttachStats2.createVolumeFromSnapshotDurationSeconds());
  }

  @Test
  public void testUpdateDirectAttachRestoreAttachStatsMetricsWithEmptyMap() {
    // create direct attach restore job
    final String hostname = "test-hostname";
    final ObjectId restoreJobId = saveDirectAttachRestoreJob(hostname);

    // Set initial attach stats
    final AttachStats initialAttachStats =
        new AttachStats(Map.of(), Map.of(), 100L, 200L, 300L, 400L, "gp3", 500L, 600L, 700L, 8.0);

    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, "test-hostname", initialAttachStats);

    // Update with empty AttachStats (all null values) - should not change anything
    final AttachStats emptyAttachStats =
        new AttachStats(null, null, null, null, null, null, null, null, null, null, null);

    _backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname, emptyAttachStats);

    // Verify the stats remain unchanged
    final DirectAttachReplicaSetBackupRestoreJob restoreJob =
        _backupRestoreJobDao.findDirectAttachReplSetJob(restoreJobId);
    final AttachStats unchangedAttachStats = restoreJob.getAttachStatuses().get(0).getAttachStats();

    // All values should remain the same
    assertEquals(100L, unchangedAttachStats.detachVolumeDurationMilliseconds());
    assertEquals(200L, unchangedAttachStats.createVolumeFromSnapshotDurationSeconds());
    assertEquals(300L, unchangedAttachStats.attachVolumeDurationMilliseconds());
    assertEquals(400L, unchangedAttachStats.mountVolumeWithSnapshotDataDurationMilliseconds());
    assertEquals("gp3", unchangedAttachStats.diskType());
    assertEquals(500L, unchangedAttachStats.bounceStopIfUpWithForceKillDurationSeconds());
    assertEquals(600L, unchangedAttachStats.getDirectAttachFileListDurationSeconds());
    assertEquals(700L, unchangedAttachStats.diskPreWarmDurationSeconds());
    assertEquals(8.0, unchangedAttachStats.diskPreWarmThroughputMbPerSecond());
  }

  private ObjectId saveDirectAttachRestoreJob(final String hostname) {
    final ObjectId restoreJobId = new ObjectId();

    final DirectAttachReplicaSetBackupRestoreJob.Builder jobBuilder =
        DirectAttachReplicaSetBackupRestoreJob.Builder.aDirectAttachReplicaSetRestoreJob()
            .withParentBuilder(
                ReplicaSetBackupRestoreJob.Builder.aReplicaSetBackupRestoreJob()
                    .withRegionName(AWSRegionName.US_EAST_1)
                    .withMetadataBuilder(
                        CpsRestoreMetadata.Builder.aCpsRestoreMetadata()
                            .withSnapshotId(ObjectId.get())
                            .withTarget(
                                RestoreTargetCluster.createRestoreTargetCluster(
                                    new ObjectId(),
                                    GroupVisibility.DEFAULT_VISIBLE,
                                    "cluster1",
                                    "cluster1",
                                    "",
                                    "UUID",
                                    "UUID"))
                            .withId(restoreJobId)
                            .withProjectId(new ObjectId())
                            .withClusterName("cluster0")
                            .withRequestingUser(mock(CpsRestoreMetadata.RequestingUser.class))
                            .withStrategy(CpsRestoreMetadata.StrategyName.DIRECT_ATTACH)))
            .withAttachStatuses(
                List.of(
                    new DirectAttachReplicaSetBackupRestoreJob.AttachStatus(
                        new BasicDBObject()
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .INSTANCE_ID,
                                new ObjectId())
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .HOSTNAME,
                                hostname)),
                    new DirectAttachReplicaSetBackupRestoreJob.AttachStatus(
                        new BasicDBObject()
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .INSTANCE_ID,
                                new ObjectId()))))
            .withCloudProvider(CloudProvider.AWS);
    _backupRestoreJobDao.save(jobBuilder.toDBObject(), WriteConcern.ACKNOWLEDGED);

    return restoreJobId;
  }

  private ObjectId saveDirectAttachRestoreJobWithMultipleHosts(
      final String hostname1, final String hostname2) {
    final ObjectId restoreJobId = new ObjectId();

    final DirectAttachReplicaSetBackupRestoreJob.Builder jobBuilder =
        DirectAttachReplicaSetBackupRestoreJob.Builder.aDirectAttachReplicaSetRestoreJob()
            .withParentBuilder(
                ReplicaSetBackupRestoreJob.Builder.aReplicaSetBackupRestoreJob()
                    .withRegionName(AWSRegionName.US_EAST_1)
                    .withMetadataBuilder(
                        CpsRestoreMetadata.Builder.aCpsRestoreMetadata()
                            .withSnapshotId(ObjectId.get())
                            .withTarget(
                                RestoreTargetCluster.createRestoreTargetCluster(
                                    new ObjectId(),
                                    GroupVisibility.DEFAULT_VISIBLE,
                                    "cluster1",
                                    "cluster1",
                                    "",
                                    "UUID",
                                    "UUID"))
                            .withId(restoreJobId)
                            .withProjectId(new ObjectId())
                            .withClusterName("cluster0")
                            .withRequestingUser(mock(CpsRestoreMetadata.RequestingUser.class))
                            .withStrategy(CpsRestoreMetadata.StrategyName.DIRECT_ATTACH)))
            .withAttachStatuses(
                List.of(
                    new DirectAttachReplicaSetBackupRestoreJob.AttachStatus(
                        new BasicDBObject()
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .INSTANCE_ID,
                                new ObjectId())
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .HOSTNAME,
                                hostname1)),
                    new DirectAttachReplicaSetBackupRestoreJob.AttachStatus(
                        new BasicDBObject()
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .INSTANCE_ID,
                                new ObjectId())
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .HOSTNAME,
                                hostname2))))
            .withCloudProvider(CloudProvider.AWS);
    _backupRestoreJobDao.save(jobBuilder.toDBObject(), WriteConcern.ACKNOWLEDGED);

    return restoreJobId;
  }
}
