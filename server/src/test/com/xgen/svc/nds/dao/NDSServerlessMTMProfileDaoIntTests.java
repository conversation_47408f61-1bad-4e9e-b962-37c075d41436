package com.xgen.svc.nds.dao;

import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.DUPLICATE_KEY_ERROR_CODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoWriteException;
import com.mongodb.client.result.DeleteResult;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSMTMProfile;
import com.xgen.cloud.nds.serverless._private.dao.NDSServerlessMTMProfileDao;
import com.xgen.cloud.nds.serverless._public.model.NDSServerlessMTMProfile;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.serverless.model.ServerlessTestFactory;
import jakarta.inject.Inject;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSServerlessMTMProfileDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private NDSServerlessMTMProfileDao _ndsServerlessMTMProfileDao;
  @Inject private AppSettings _appSettings;

  private final List<NDSServerlessMTMProfile> _serverlessMTMProfiles =
      NDSModelTestFactory.getNDSServerlessMTMProfilesForTest();

  @BeforeEach
  public void setup() throws Exception {
    _appSettings.setProp(
        "nds.instances.shared.version",
        NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
        AppSettings.SettingType.MEMORY);
    _ndsServerlessMTMProfileDao.insertManyReplicaSafe(_serverlessMTMProfiles);
  }

  @Test
  public void testSaveServerlessMTMProfile_poolIdUniqueness() {

    // reset to empty collection
    _ndsServerlessMTMProfileDao.getCollection().deleteMany(new BasicDBObject());

    final ObjectId poolId0 = oid(0);

    final NDSServerlessMTMProfile defaultProfile =
        new NDSServerlessMTMProfile(
            AWSRegionName.US_EAST_1,
            ServerlessInstanceSize.SERVERLESS_V2,
            AWSNDSInstanceSize.M30,
            1500,
            50,
            AWSNDSInstanceSize.M30,
            ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
            poolId0);
    saveServerlessMTMProfile(defaultProfile);
    assertEquals(1, _ndsServerlessMTMProfileDao.findAll().size());

    // differing region name with dup poolId
    try {
      final NDSServerlessMTMProfile updated =
          defaultProfile.toBuilder().setRegionName(AWSRegionName.US_EAST_2).build();
      saveServerlessMTMProfile(updated);
      fail("Dup key error not encountered");
    } catch (final MongoWriteException mwe) {
      assertEquals(DUPLICATE_KEY_ERROR_CODE.intValue(), mwe.getCode());
      assertEquals(1, _ndsServerlessMTMProfileDao.findAll().size());
    }

    // different region, different poolId
    final NDSServerlessMTMProfile updated =
        defaultProfile.toBuilder()
            .setRegionName(AWSRegionName.US_EAST_2)
            .setPoolId(new ObjectId())
            .build();
    saveServerlessMTMProfile(updated);
    assertEquals(2, _ndsServerlessMTMProfileDao.findAll().size());
  }

  @Test
  public void testSaveServerlessMTMProfile() {
    // set state
    final List<NDSServerlessMTMProfile> originalServerlessMTMProfiles =
        _ndsServerlessMTMProfileDao.findMTMProfiles();

    assertEquals(_serverlessMTMProfiles.size(), originalServerlessMTMProfiles.size());

    _ndsServerlessMTMProfileDao.getCollection().deleteMany(new BasicDBObject());

    assertEquals(0, _ndsServerlessMTMProfileDao.findMTMProfiles().size());

    // test profile save
    final ObjectId poolId = new ObjectId();
    final NDSServerlessMTMProfile profile =
        originalServerlessMTMProfiles.get(0).toBuilder().setPoolId(poolId).build();
    saveServerlessMTMProfile(profile);

    final List<NDSServerlessMTMProfile> profiles = _ndsServerlessMTMProfileDao.findMTMProfiles();
    assertEquals(1, profiles.size());

    final BasicDBObject profileDBObject = profile.toDBObject();
    final BasicDBObject savedProfileDBObject = profiles.get(0).toDBObject();
    profileDBObject.remove(NDSMTMProfile.FieldDefs.ID);
    savedProfileDBObject.remove(NDSMTMProfile.FieldDefs.ID);
    assertEquals(profileDBObject, savedProfileDBObject);
    assertEquals(poolId, profileDBObject.getObjectId(NDSMTMProfile.FieldDefs.POOL_ID));

    // test uniqueness constraints
    try {
      saveServerlessMTMProfile(profile);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof MongoWriteException);
    }

    assertEquals(1, _ndsServerlessMTMProfileDao.findMTMProfiles().size());
  }

  @Test
  public void testRemoveServerlessMTMProfile() {
    final List<NDSServerlessMTMProfile> originalServerlessMTMProfiles =
        _ndsServerlessMTMProfileDao.findMTMProfiles();
    assertFalse(originalServerlessMTMProfiles.isEmpty());

    originalServerlessMTMProfiles.forEach(
        profile -> {
          final DeleteResult deleteResult = _ndsServerlessMTMProfileDao.removeMTMProfile(profile);
          assertTrue(deleteResult.wasAcknowledged() && deleteResult.getDeletedCount() == 1L);
        });

    final List<NDSServerlessMTMProfile> newServerlessMTMProfiles =
        _ndsServerlessMTMProfileDao.findMTMProfiles();
    assertTrue(newServerlessMTMProfiles.isEmpty());
  }

  @Test
  public void testFindServerlessMTMProfiles() {
    assertEquals(
        _serverlessMTMProfiles.size(), _ndsServerlessMTMProfileDao.findMTMProfiles().size());
  }

  @Test
  public void testFindServerlessMTMProfile() {

    // reset to empty collection
    _ndsServerlessMTMProfileDao.getCollection().deleteMany(new BasicDBObject());

    final ObjectId poolId = new ObjectId();

    _ndsServerlessMTMProfileDao.insertManyReplicaSafe(
        List.of(
            new NDSServerlessMTMProfile(
                AWSRegionName.US_EAST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                poolId),
            new NDSServerlessMTMProfile(
                AWSRegionName.US_EAST_2,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                new ObjectId()),
            new NDSServerlessMTMProfile(
                AWSRegionName.US_WEST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                null)));

    // look up dne
    assertEquals(
        Optional.empty(),
        _ndsServerlessMTMProfileDao.findMTMProfile(
            ServerlessInstanceSize.SERVERLESS_V2,
            AzureRegionName.CHINA_NORTH,
            ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
            null));

    // look up null poolId - should exist
    {
      _ndsServerlessMTMProfileDao
          .findMTMProfile(
              ServerlessInstanceSize.SERVERLESS_V2,
              AWSRegionName.US_WEST_1,
              ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
              null)
          .ifPresentOrElse(
              profile -> {
                assertEquals(ServerlessInstanceSize.SERVERLESS_V2, profile.getTenantInstanceSize());
                assertEquals(AWSRegionName.US_WEST_1, profile.getRegionName());
                assertEquals(1500, profile.getDefaultCapacity());
                assertEquals(50, profile.getLowCapacityBuffer());
                assertEquals(AWSNDSInstanceSize.M50, profile.getBackingInstanceSize());
                assertNull(profile.getPoolId());
              },
              Assertions::fail);
    }

    // look up by poolId
    {
      _ndsServerlessMTMProfileDao
          .findMTMProfile(
              ServerlessInstanceSize.SERVERLESS_V2,
              AWSRegionName.US_EAST_1,
              ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
              poolId)
          .ifPresentOrElse(
              profile -> {
                assertEquals(ServerlessInstanceSize.SERVERLESS_V2, profile.getTenantInstanceSize());
                assertEquals(AWSRegionName.US_EAST_1, profile.getRegionName());
                assertEquals(1500, profile.getDefaultCapacity());
                assertEquals(50, profile.getLowCapacityBuffer());
                assertEquals(AWSNDSInstanceSize.M50, profile.getBackingInstanceSize());
                assertEquals(poolId, profile.getPoolId());
              },
              Assertions::fail);
    }
  }

  @Test
  public void testFindServerlessMTMProfilesByPoolId() {
    // reset to empty collection
    _ndsServerlessMTMProfileDao.getCollection().deleteMany(new BasicDBObject());

    final ObjectId poolId0 = new ObjectId();
    final ObjectId poolId1 = new ObjectId();

    final List<NDSServerlessMTMProfile> profiles =
        List.of(
            new NDSServerlessMTMProfile(
                AWSRegionName.US_EAST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                poolId0),
            new NDSServerlessMTMProfile(
                AWSRegionName.US_EAST_2,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                poolId1),
            new NDSServerlessMTMProfile(
                AWSRegionName.US_WEST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                null),
            new NDSServerlessMTMProfile(
                AWSRegionName.AP_EAST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                null));

    _ndsServerlessMTMProfileDao.insertManyReplicaSafe(profiles);

    // look up dne
    assertEquals(List.of(), _ndsServerlessMTMProfileDao.findMTMProfilesByPoolId(new ObjectId()));

    // look up profiles outside of pool
    assertEquals(
        Set.of(profiles.get(2), profiles.get(3)),
        new HashSet<>(_ndsServerlessMTMProfileDao.findMTMProfilesByPoolId(null)));

    // look up poolId
    assertEquals(
        List.of(profiles.get(0)), _ndsServerlessMTMProfileDao.findMTMProfilesByPoolId(poolId0));
    assertEquals(
        List.of(profiles.get(1)), _ndsServerlessMTMProfileDao.findMTMProfilesByPoolId(poolId1));
  }

  @Test
  public void testFindServerlessMTMProfileById() {
    // reset to empty collection
    _ndsServerlessMTMProfileDao.getCollection().deleteMany(new BasicDBObject());

    final ObjectId poolId0 = new ObjectId();
    final ObjectId poolId1 = new ObjectId();
    final ObjectId id0 = new ObjectId();
    final ObjectId id1 = new ObjectId();
    final ObjectId id2 = new ObjectId();

    final List<NDSServerlessMTMProfile> profiles =
        List.of(
            new NDSServerlessMTMProfile(
                id0,
                AWSRegionName.US_EAST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                poolId0),
            new NDSServerlessMTMProfile(
                id1,
                AWSRegionName.US_EAST_2,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                poolId1),
            new NDSServerlessMTMProfile(
                id2,
                AWSRegionName.US_WEST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                null),
            new NDSServerlessMTMProfile(
                AWSRegionName.AP_EAST_1,
                ServerlessInstanceSize.SERVERLESS_V2,
                AWSNDSInstanceSize.M50,
                1500,
                50,
                AWSNDSInstanceSize.M30,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION,
                null));

    _ndsServerlessMTMProfileDao.insertManyReplicaSafe(profiles);
    assertEquals(Optional.of(profiles.get(0)), _ndsServerlessMTMProfileDao.findMTMProfileById(id0));
    assertEquals(Optional.of(profiles.get(1)), _ndsServerlessMTMProfileDao.findMTMProfileById(id1));
    assertEquals(Optional.of(profiles.get(2)), _ndsServerlessMTMProfileDao.findMTMProfileById(id2));
    assertEquals(Optional.empty(), _ndsServerlessMTMProfileDao.findMTMProfileById(new ObjectId()));
  }

  private void saveServerlessMTMProfile(final NDSMTMProfile pProfile) {
    final NDSServerlessMTMProfile profile = (NDSServerlessMTMProfile) pProfile;
    _ndsServerlessMTMProfileDao.saveMTMProfile(
        new NDSServerlessMTMProfile(
            profile.getRegionName(),
            profile.getTenantInstanceSize(),
            profile.getBackingInstanceSize(),
            profile.getDefaultCapacity(),
            profile.getLowCapacityBuffer(),
            profile.getAutoScaleMinSize(),
            profile.getMongoDBMajorVersion(),
            profile.getPoolId()));
  }
}
