package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.cps.backupjob._private.dao.DataProtectionSettingsDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings.DataProtectionSettingsBuilder;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings.State;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class DataProtectionSettingsDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private DataProtectionSettingsDao _dataProtectionSettingsDao;

  @Test
  public void testSaveAndFind() {
    final ObjectId projectId1 = new ObjectId();
    final ObjectId projectId2 = new ObjectId();
    final ObjectId projectId3 = new ObjectId();
    final String updateUser = "testUser";
    final Date updateDate = new Date();
    final String authorizedEmail = "<EMAIL>";

    final List<PolicyItem> scheduledPolicyItemList =
        CpsPolicySvc.createDefaultPolicyItemList(true, true);
    final PolicyItem onDemandPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(3), BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(projectId1)
            .setState(State.ACTIVE)
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(true)
            .setPitEnabled(true)
            .setCopyProtectionEnabled(true)
            .setOnDemandPolicyItem(onDemandPolicyItem)
            .setScheduledPolicyItems(scheduledPolicyItemList)
            .setAuthorizedEmail(authorizedEmail)
            .setUpdatedDate(updateDate)
            .setUpdatedUser(updateUser);

    _dataProtectionSettingsDao.save(builder.build());
    _dataProtectionSettingsDao.save(builder.setProjectId(projectId2).build());

    final Optional<DataProtectionSettings> found1 =
        _dataProtectionSettingsDao.findByProjectId(projectId1);
    final Optional<DataProtectionSettings> found2 =
        _dataProtectionSettingsDao.findByProjectId(projectId2);
    final Optional<DataProtectionSettings> found3 =
        _dataProtectionSettingsDao.findByProjectId(projectId3);

    assertTrue(found1.isPresent());
    final DataProtectionSettings foundSettings1 = found1.get();

    assertEquals(projectId1, foundSettings1.getProjectId());
    assertEquals(State.ACTIVE, foundSettings1.getState());
    assertEquals(5d, foundSettings1.getRestoreWindowDays(), 1e-6);
    assertTrue(foundSettings1.isPitEnabled());
    assertTrue(foundSettings1.isEncryptionAtRestEnabled());
    assertTrue(foundSettings1.isCopyProtectionEnabled());

    assertEquals(scheduledPolicyItemList, foundSettings1.getScheduledPolicyItems());
    assertEquals(onDemandPolicyItem, foundSettings1.getOnDemandPolicyItem());
    assertEquals(updateDate, foundSettings1.getUpdatedDate());
    assertEquals(updateUser, foundSettings1.getUpdatedUser());
    assertEquals(authorizedEmail, foundSettings1.getAuthorizedEmail());

    assertNull(foundSettings1.getMarkedForDisablementDate());

    assertTrue(found2.isPresent());
    assertEquals(projectId2, found2.get().getProjectId());

    assertFalse(found3.isPresent());

    // now update the props for project 1

    final List<PolicyItem> scheduledPolicyItemListUpdated =
        List.of(
            new PolicyItem(
                null,
                BackupFrequencyType.WEEKLY,
                4,
                Duration.ofDays(8),
                BackupRetentionUnit.MONTHS));

    final PolicyItem onDemandPolicyItemUpdated =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(9), BackupRetentionUnit.WEEKS);

    builder
        .setProjectId(projectId1)
        .setState(State.ENABLING)
        .setRestoreWindowDays(6)
        .setEncryptionAtRestEnabled(false)
        .setPitEnabled(false)
        .setCopyProtectionEnabled(false)
        .setOnDemandPolicyItem(onDemandPolicyItemUpdated)
        .setScheduledPolicyItems(scheduledPolicyItemListUpdated);

    _dataProtectionSettingsDao.save(builder.build());

    final Optional<DataProtectionSettings> found1Updated =
        _dataProtectionSettingsDao.findByProjectId(projectId1);

    assertTrue(found1Updated.isPresent());
    final DataProtectionSettings foundSettings1Updated = found1Updated.get();

    assertEquals(projectId1, foundSettings1Updated.getProjectId());
    assertEquals(State.ENABLING, foundSettings1Updated.getState());
    assertEquals(6d, foundSettings1Updated.getRestoreWindowDays(), 1e-6);
    assertFalse(foundSettings1Updated.isPitEnabled());
    assertFalse(foundSettings1Updated.isEncryptionAtRestEnabled());
    assertFalse(foundSettings1Updated.isCopyProtectionEnabled());

    assertEquals(scheduledPolicyItemListUpdated, foundSettings1Updated.getScheduledPolicyItems());
    assertEquals(onDemandPolicyItemUpdated, foundSettings1Updated.getOnDemandPolicyItem());
  }

  @Test
  public void testRemove() {
    final ObjectId projectId1 = new ObjectId();
    final ObjectId projectId2 = new ObjectId();
    final ObjectId projectId3 = new ObjectId();

    final List<PolicyItem> scheduledPolicyItemList =
        CpsPolicySvc.createDefaultPolicyItemList(true, true);
    final PolicyItem onDemandPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(3), BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(projectId1)
            .setState(State.UPDATING)
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(true)
            .setPitEnabled(true)
            .setCopyProtectionEnabled(true)
            .setOnDemandPolicyItem(onDemandPolicyItem)
            .setScheduledPolicyItems(scheduledPolicyItemList);

    _dataProtectionSettingsDao.save(builder.build());
    _dataProtectionSettingsDao.save(builder.setProjectId(projectId2).build());

    // remove project 3
    _dataProtectionSettingsDao.removeByProjectId(projectId3);

    Optional<DataProtectionSettings> found1 =
        _dataProtectionSettingsDao.findByProjectId(projectId1);
    Optional<DataProtectionSettings> found2 =
        _dataProtectionSettingsDao.findByProjectId(projectId2);

    assertTrue(found1.isPresent());
    assertTrue(found2.isPresent());

    // remove project 1
    _dataProtectionSettingsDao.removeByProjectId(projectId1);

    found1 = _dataProtectionSettingsDao.findByProjectId(projectId1);
    found2 = _dataProtectionSettingsDao.findByProjectId(projectId2);

    assertFalse(found1.isPresent());
    assertTrue(found2.isPresent());
    assertEquals(State.UPDATING, found2.get().getState());

    // remove project 2
    _dataProtectionSettingsDao.removeByProjectId(projectId2);

    found2 = _dataProtectionSettingsDao.findByProjectId(projectId2);

    assertFalse(found2.isPresent());
  }

  @Test
  public void findAuthorizedUserInfoByProjectId_noUsername() {
    final ObjectId projectId1 = new ObjectId();
    final ObjectId projectId2 = new ObjectId();

    final List<PolicyItem> scheduledPolicyItemList =
        CpsPolicySvc.createDefaultPolicyItemList(true, true);
    final PolicyItem onDemandPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(3), BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(projectId1)
            .setState(State.UPDATING)
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(true)
            .setPitEnabled(true)
            .setCopyProtectionEnabled(true)
            .setOnDemandPolicyItem(onDemandPolicyItem)
            .setScheduledPolicyItems(scheduledPolicyItemList)
            .setAuthorizedEmail("<EMAIL>")
            .setAuthorizedUserFirstName(null)
            .setAuthorizedUserLastName(null);
    _dataProtectionSettingsDao.save(builder.build());

    final Optional<DataProtectionSettings> dataProtectionSettingsOptional =
        _dataProtectionSettingsDao.findByProjectId(projectId1);
    assertNull(dataProtectionSettingsOptional.get().getAuthorizedUserFirstName());
    assertNull(dataProtectionSettingsOptional.get().getAuthorizedUserLastName());

    final Optional<DataProtectionSettings.AuthorizedUserInfo> authorizedUserInfoOptional =
        _dataProtectionSettingsDao.findAuthorizedUserInfoByProjectId(projectId1);
    assertTrue(authorizedUserInfoOptional.isEmpty());
  }

  @Test
  public void findAuthorizedUserInfoByProjectId_hasUsername() {
    final ObjectId projectId1 = new ObjectId();

    final List<PolicyItem> scheduledPolicyItemList =
        CpsPolicySvc.createDefaultPolicyItemList(true, true);
    final PolicyItem onDemandPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(3), BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(projectId1)
            .setState(State.UPDATING)
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(true)
            .setPitEnabled(true)
            .setCopyProtectionEnabled(true)
            .setOnDemandPolicyItem(onDemandPolicyItem)
            .setScheduledPolicyItems(scheduledPolicyItemList)
            .setAuthorizedEmail("<EMAIL>")
            .setAuthorizedUserFirstName("first")
            .setAuthorizedUserLastName("last");
    _dataProtectionSettingsDao.save(builder.build());

    final Optional<DataProtectionSettings> dataProtectionSettingsOptional1 =
        _dataProtectionSettingsDao.findByProjectId(projectId1);
    assertNotNull(dataProtectionSettingsOptional1.get().getAuthorizedUserFirstName());
    assertNotNull(dataProtectionSettingsOptional1.get().getAuthorizedUserLastName());
    final Optional<DataProtectionSettings.AuthorizedUserInfo> authorizedUserInfoOptional1 =
        _dataProtectionSettingsDao.findAuthorizedUserInfoByProjectId(projectId1);
    assertFalse(authorizedUserInfoOptional1.isEmpty());
  }
}
