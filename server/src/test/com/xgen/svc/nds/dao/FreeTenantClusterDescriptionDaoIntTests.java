package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.free._private.dao.FreeTenantClusterDescriptionDao;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class FreeTenantClusterDescriptionDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private FreeTenantClusterDescriptionDao _clusterDescriptionDao;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _clusterDescriptionDao.ensureIndexes();
  }

  @Test
  public void testUpdateTenantClusterExcludeFromAutomaticPause() {
    final Date now = new Date();
    final Date userNotifiedAboutPauseDate = DateUtils.addDays(now, -8);
    final Date ndsAccessRevokedDate = DateUtils.addDays(now, -1);
    final ObjectId groupId = new ObjectId();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setClusterName("m0Cluster")
                    .setExcludeFromPause(false)
                    .setUserNotifiedAboutPauseDate(userNotifiedAboutPauseDate)
                    .setNDSAccessRevokedDate(ndsAccessRevokedDate)));
    // test assumptions
    assertEquals(
        userNotifiedAboutPauseDate,
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .getUserNotifiedAboutPauseDate());
    assertEquals(
        ndsAccessRevokedDate,
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .getNdsAccessRevokedDate());
    assertFalse(
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .shouldExcludeFromPause());

    _clusterDescriptionDao.insertReplicaSafe(clusterDescription.toDBObject());

    final Optional<ClusterDescription> savedClusterDescOptional =
        _clusterDescriptionDao.findByUniqueId(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId());

    assertTrue(savedClusterDescOptional.isPresent());
    final ClusterDescription savedClusterDesc = savedClusterDescOptional.get();

    assertEquals(
        userNotifiedAboutPauseDate,
        ((FreeTenantProviderOptions) savedClusterDesc.getFreeTenantProviderOptions())
            .getUserNotifiedAboutPauseDate());
    assertEquals(
        ndsAccessRevokedDate,
        ((FreeTenantProviderOptions) savedClusterDesc.getFreeTenantProviderOptions())
            .getNdsAccessRevokedDate());
    assertFalse(
        ((FreeTenantProviderOptions) savedClusterDesc.getFreeTenantProviderOptions())
            .shouldExcludeFromPause());

    // exclude from pause - should reset pause metadata
    _clusterDescriptionDao.updateFreeTenantClusterExcludeFromAutomaticPause(
        clusterDescription.getGroupId(), clusterDescription.getUniqueId());

    final Optional<ClusterDescription> savedClusterDescExcludedFromPauseOptional =
        _clusterDescriptionDao.findByUniqueId(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId());

    assertTrue(savedClusterDescExcludedFromPauseOptional.isPresent());
    final ClusterDescription savedClusterDescExcludedFromPause =
        savedClusterDescExcludedFromPauseOptional.get();

    assertNull(
        ((FreeTenantProviderOptions)
                savedClusterDescExcludedFromPause.getFreeTenantProviderOptions())
            .getUserNotifiedAboutPauseDate());
    assertNull(
        ((FreeTenantProviderOptions)
                savedClusterDescExcludedFromPause.getFreeTenantProviderOptions())
            .getNdsAccessRevokedDate());
    assertTrue(
        ((FreeTenantProviderOptions)
                savedClusterDescExcludedFromPause.getFreeTenantProviderOptions())
            .shouldExcludeFromPause());
  }

  @Test
  public void testUpdateUserNotifiedAboutPauseDateField() {
    final Date userNotifiedAboutPauseDate = new Date();
    final ObjectId groupId = new ObjectId();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setClusterName("m0Cluster")));
    assertNull(
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .getUserNotifiedAboutPauseDate());

    _clusterDescriptionDao.insertReplicaSafe(clusterDescription.toDBObject());
    _clusterDescriptionDao.updateFreeTenantClusterUserNotifiedAboutPauseDate(
        clusterDescription.getGroupId(),
        clusterDescription.getUniqueId(),
        userNotifiedAboutPauseDate);

    // Can set to non-null
    {
      final Optional<ClusterDescription> savedClusterDesc =
          _clusterDescriptionDao.findByUniqueId(
              clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      assertTrue(savedClusterDesc.isPresent());
      assertEquals(
          userNotifiedAboutPauseDate,
          ((FreeTenantProviderOptions) savedClusterDesc.get().getFreeTenantProviderOptions())
              .getUserNotifiedAboutPauseDate());
    }

    _clusterDescriptionDao.updateFreeTenantClusterUserNotifiedAboutPauseDate(
        clusterDescription.getGroupId(), clusterDescription.getUniqueId(), null);

    // Can set to null
    {
      final Optional<ClusterDescription> savedClusterDesc =
          _clusterDescriptionDao.findByUniqueId(
              clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      assertTrue(savedClusterDesc.isPresent());
      assertNull(
          ((FreeTenantProviderOptions) savedClusterDesc.get().getFreeTenantProviderOptions())
              .getUserNotifiedAboutPauseDate());
    }
  }

  @Test
  public void testUpdateTenantClusterNdsAccessRevokedDate() {
    final Date ndsAccessRevokedDate = new Date();
    final ObjectId groupId = new ObjectId();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setClusterName("m0Cluster")));
    assertNull(
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .getNdsAccessRevokedDate());

    _clusterDescriptionDao.insertReplicaSafe(clusterDescription.toDBObject());
    _clusterDescriptionDao.updateFreeTenantClusterNdsAccessRevokedDate(
        clusterDescription.getGroupId(), clusterDescription.getUniqueId(), ndsAccessRevokedDate);

    // Can set to non-null
    {
      final Optional<ClusterDescription> savedClusterDesc =
          _clusterDescriptionDao.findByUniqueId(
              clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      assertTrue(savedClusterDesc.isPresent());
      assertEquals(
          ndsAccessRevokedDate,
          ((FreeTenantProviderOptions) savedClusterDesc.get().getFreeTenantProviderOptions())
              .getNdsAccessRevokedDate());
    }

    _clusterDescriptionDao.updateFreeTenantClusterNdsAccessRevokedDate(
        clusterDescription.getGroupId(), clusterDescription.getUniqueId(), null);

    // Can set to null
    {
      final Optional<ClusterDescription> savedClusterDesc =
          _clusterDescriptionDao.findByUniqueId(
              clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      assertTrue(savedClusterDesc.isPresent());
      assertNull(
          ((FreeTenantProviderOptions) savedClusterDesc.get().getFreeTenantProviderOptions())
              .getNdsAccessRevokedDate());
    }
  }

  @Test
  public void testSetPausedAndRequestResume() {
    final ObjectId groupId = new ObjectId();

    final ClusterDescription wipClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setClusterName("m0Cluster")
                    .setProvisionType(ClusterDescription.ClusterProvisionType.FAST)));
    final ClusterDescription clusterDescription =
        wipClusterDescription
            .copy()
            .setFreeTenantProviderOptions(
                ((FreeTenantProviderOptions) wipClusterDescription.getFreeTenantProviderOptions())
                    .copy()
                    .setNeedsUnpauseTenantRestore(false)
                    .build())
            .setIsPaused(true)
            .setPausedDate(null)
            .build();

    assertFalse(
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .isNeedsUnpauseTenantRestore());

    _clusterDescriptionDao.insertReplicaSafe(clusterDescription.toDBObject());
    _clusterDescriptionDao.resetPausedAndRequestResumeForCompaction(
        groupId, clusterDescription.getName());

    final Optional<ClusterDescription> savedClusterDesc =
        _clusterDescriptionDao.findByUniqueId(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId());

    assertFalse(savedClusterDesc.get().isPaused());
    assertEquals(
        ClusterDescription.ClusterProvisionType.REGULAR,
        savedClusterDesc.get().getClusterProvisionType());
    assertTrue(
        ((FreeTenantProviderOptions) savedClusterDesc.get().getFreeTenantProviderOptions())
            .isNeedsUnpauseTenantRestore());
  }

  @Test
  public void testUpdateTenantToRepause() {
    final Date originalPauseDate = new Date();
    final ObjectId groupId = new ObjectId();

    final ClusterDescription wipClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setClusterName("m0Cluster")));

    final ClusterDescription clusterDescription =
        wipClusterDescription
            .copy()
            .setFreeTenantProviderOptions(
                ((FreeTenantProviderOptions) wipClusterDescription.getFreeTenantProviderOptions())
                    .copy()
                    .setNeedsUnpauseTenantRestore(true)
                    .build())
            .setIsPaused(false)
            .setPausedDate(originalPauseDate)
            .build();
    assertTrue(
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .isNeedsUnpauseTenantRestore());

    _clusterDescriptionDao.insertReplicaSafe(clusterDescription.toDBObject());
    _clusterDescriptionDao.updateTenantToRepause(
        clusterDescription.getGroupId(), clusterDescription.getUniqueId());

    {
      final Optional<ClusterDescription> savedClusterDesc =
          _clusterDescriptionDao.findByUniqueId(
              clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      assertTrue(savedClusterDesc.get().isPaused());
      assertFalse(
          ((FreeTenantProviderOptions) savedClusterDesc.get().getFreeTenantProviderOptions())
              .isNeedsUnpauseTenantRestore());
      assertEquals(originalPauseDate, savedClusterDesc.get().getPausedDate().get());
    }
  }

  @Test
  public void testUpdateFreeTenantClusterExcludeFromAutomaticPause() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription stuckM0 =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setExcludeFromPause(true)
                    .setClusterName("m0Cluster")));
    _clusterDescriptionDao.insertReplicaSafe(stuckM0.toDBObject());
    _clusterDescriptionDao.updateFreeTenantClusterExcludeFromPause(
        stuckM0.getGroupId(), stuckM0.getUniqueId(), false);
    final FreeTenantProviderOptions freeTenantProviderOptions =
        (FreeTenantProviderOptions)
            _clusterDescriptionDao
                .findByUniqueId(stuckM0.getGroupId(), stuckM0.getUniqueId())
                .get()
                .getFreeTenantProviderOptions();

    assertEquals(false, freeTenantProviderOptions.shouldExcludeFromPause());
  }
}
