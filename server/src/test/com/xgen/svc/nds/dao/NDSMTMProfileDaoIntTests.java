package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.flex._private.dao.NDSFlexMTMProfileDao;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.NDSFlexMTMProfile;
import com.xgen.cloud.nds.free._private.dao.NDSSharedMTMProfileDao;
import com.xgen.cloud.nds.free._public.model.NDSSharedMTMProfile;
import com.xgen.cloud.nds.serverless._private.dao.NDSServerlessMTMProfileDao;
import com.xgen.cloud.nds.serverless._public.model.NDSServerlessMTMProfile;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Objects;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSMTMProfileDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private NDSSharedMTMProfileDao _ndsSharedMTMProfileDao;
  @Inject private NDSServerlessMTMProfileDao _ndsServerlessMTMProfileDao;
  @Inject private NDSFlexMTMProfileDao _ndsFlexMTMProfileDao;
  @Inject private AppSettings _appSettings;

  private final List<NDSSharedMTMProfile> _sharedMTMProfiles =
      NDSModelTestFactory.getNDSMTMProfilesForTest();
  private final List<NDSServerlessMTMProfile> _serverlessMTMProfiles =
      NDSModelTestFactory.getNDSServerlessMTMProfilesForTest();

  @BeforeEach
  public void setup() throws Exception {
    _appSettings.setProp(
        "nds.instances.shared.version",
        NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
        AppSettings.SettingType.MEMORY);
    _ndsSharedMTMProfileDao.insertManyReplicaSafe(_sharedMTMProfiles);
    _ndsServerlessMTMProfileDao.insertManyReplicaSafe(_serverlessMTMProfiles);
  }

  @Test
  public void testSaveProfiles() {
    final List<NDSSharedMTMProfile> originalSharedMTMProfiles =
        _ndsSharedMTMProfileDao.findMTMProfiles();
    final List<NDSServerlessMTMProfile> originalServerlessMTMProfiles =
        _ndsServerlessMTMProfileDao.findMTMProfiles();
    final List<NDSFlexMTMProfile> flexMTMProfiles = List.of(getNDSFlexMTMProfile());

    assertEquals(_sharedMTMProfiles.size(), originalSharedMTMProfiles.size());
    assertEquals(_serverlessMTMProfiles.size(), originalServerlessMTMProfiles.size());

    _ndsSharedMTMProfileDao.getCollection().deleteMany(new BasicDBObject());

    assertEquals(0, _ndsSharedMTMProfileDao.findMTMProfiles().size());
    assertEquals(0, _ndsServerlessMTMProfileDao.findMTMProfiles().size());
    assertEquals(0, _ndsFlexMTMProfileDao.findMTMProfiles().size());

    _ndsSharedMTMProfileDao.saveProfiles(originalSharedMTMProfiles);
    _ndsServerlessMTMProfileDao.saveProfiles(originalServerlessMTMProfiles);
    _ndsFlexMTMProfileDao.saveProfiles(flexMTMProfiles);

    assertEquals(originalSharedMTMProfiles, _ndsSharedMTMProfileDao.findMTMProfiles());
    assertEquals(originalServerlessMTMProfiles, _ndsServerlessMTMProfileDao.findMTMProfiles());
    assertEquals(flexMTMProfiles, _ndsFlexMTMProfileDao.findMTMProfiles());
  }

  @Test
  public void testUpdateMongoDBMajorVersionForAllFlexMTMProfiles() {
    final String newVersion = "9.0";
    // Needs update
    final NDSFlexMTMProfile profile1 = getNDSFlexMTMProfile();
    // Up-to-date
    final NDSFlexMTMProfile profile2 =
        new NDSFlexMTMProfile(
            new ObjectId(),
            AWSRegionName.US_EAST_2,
            FlexInstanceSize.FLEX,
            AWSNDSInstanceSize.M30,
            100,
            10,
            AWSNDSInstanceSize.M30,
            newVersion);

    final List<NDSFlexMTMProfile> profiles = List.of(profile1, profile2);
    _ndsFlexMTMProfileDao.saveProfiles(profiles);
    assertEquals(2, _ndsFlexMTMProfileDao.findMTMProfiles().size());

    // Test only update version of eligible FLEX MTM Profiles, i.e. no Serverless or Shared tier MTM
    // profiles are updated
    assertEquals(
        1L,
        _ndsFlexMTMProfileDao.updateMongoDBMajorVersionForAllFlexMTMProfiles(
            VersionUtils.Version.v(newVersion)));
    // Verify all FLEX MTM profiles up-to-date
    assertEquals(
        2L,
        _ndsFlexMTMProfileDao.findMTMProfiles().stream()
            .filter(profile -> Objects.equals(profile.getMongoDBMajorVersion(), newVersion))
            .count());
  }

  @Test
  public void testUpdateLowCapacityBuffer() {
    _sharedMTMProfiles.forEach(
        profile -> {
          assertEquals(
              1,
              _ndsSharedMTMProfileDao
                  .updateLowCapacityBuffer(profile.getId(), profile.getLowCapacityBuffer())
                  .getMatchedCount());
          assertEquals(
              0,
              _ndsSharedMTMProfileDao
                  .updateLowCapacityBuffer(profile.getId(), profile.getLowCapacityBuffer())
                  .getModifiedCount());
          assertEquals(
              1,
              _ndsSharedMTMProfileDao
                  .updateLowCapacityBuffer(profile.getId(), 0)
                  .getModifiedCount());
        });
  }

  public NDSFlexMTMProfile getNDSFlexMTMProfile() {
    return new NDSFlexMTMProfile(
        new ObjectId(),
        AWSRegionName.US_EAST_1,
        FlexInstanceSize.FLEX,
        AWSNDSInstanceSize.M30,
        100,
        10,
        AWSNDSInstanceSize.M30,
        "8.0");
  }
}
