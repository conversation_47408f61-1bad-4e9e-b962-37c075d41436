package com.xgen.svc.nds.dao;

import static com.xgen.svc.nds.model.NDSModelTestFactory.getAWSAdminBackupSnapshot;
import static com.xgen.svc.nds.model.NDSModelTestFactory.getAzureAdminBackupSnapshot;
import static com.xgen.svc.nds.model.NDSModelTestFactory.getGCPAdminBackupSnapshot;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.aws._private.dao.AWSAdminBackupSnapshotDao;
import com.xgen.cloud.nds.aws._public.model.AWSAdminBackupSnapshot;
import com.xgen.cloud.nds.azure._private.dao.AzureAdminBackupSnapshotDao;
import com.xgen.cloud.nds.azure._public.model.AzureAdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.gcp._public.model.GCPAdminBackupSnapshot;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AdminBackupSnapshotDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private NonCloudProviderSpecificAdminBackupSnapshotDao _adminBackupSnapshotDao;
  @Inject private AWSAdminBackupSnapshotDao _awsAdminBackupSnapshotDao;
  @Inject private AzureAdminBackupSnapshotDao _azureAdminBackupSnapshotDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _adminBackupSnapshotDao.ensureIndexes();
  }

  @Test
  public void testCreateAndFindSnapshot() {
    final GCPAdminBackupSnapshot gcpSnapshot =
        getGCPAdminBackupSnapshot(new ObjectId(), "clusterGCP", "instanceGCP", Status.PENDING);
    final AWSAdminBackupSnapshot awsSnapshot =
        getAWSAdminBackupSnapshot(new ObjectId(), "clusterAWS", "instanceAWS", Status.PENDING);
    final AzureAdminBackupSnapshot azureSnapshot =
        getAzureAdminBackupSnapshot(
            new ObjectId(), "clusterAzure", "instanceAzure", Status.PENDING);
    final AzureAdminBackupSnapshot activeSnapshot =
        getAzureAdminBackupSnapshot(
            new ObjectId(), "clusterActive", "instanceActive", Status.ACTIVE);
    _adminBackupSnapshotDao.create(gcpSnapshot);
    _adminBackupSnapshotDao.create(awsSnapshot);
    _adminBackupSnapshotDao.create(azureSnapshot);
    _adminBackupSnapshotDao.create(activeSnapshot);

    assertEquals(4, _adminBackupSnapshotDao.findAll().size());

    final Optional<AdminBackupSnapshot> gcpResult =
        _adminBackupSnapshotDao.findPendingSnapshot(
            gcpSnapshot.getProjectId(), gcpSnapshot.getClusterName(), gcpSnapshot.getInstanceId());
    assertTrue(gcpResult.isPresent());
    assertEquals(gcpSnapshot, gcpResult.get());

    final Optional<AdminBackupSnapshot> awsResult =
        _adminBackupSnapshotDao.findPendingSnapshot(
            awsSnapshot.getProjectId(), awsSnapshot.getClusterName(), awsSnapshot.getInstanceId());
    assertTrue(awsResult.isPresent());
    assertEquals(awsSnapshot, awsResult.get());

    final Optional<AdminBackupSnapshot> azureResult =
        _adminBackupSnapshotDao.findPendingSnapshot(
            azureSnapshot.getProjectId(),
            azureSnapshot.getClusterName(),
            azureSnapshot.getInstanceId());
    assertTrue(azureResult.isPresent());
    assertEquals(azureSnapshot, azureResult.get());

    final Optional<AdminBackupSnapshot> nonDeletedResult =
        _adminBackupSnapshotDao.findNonDeletedSnapshot(
            activeSnapshot.getProjectId(),
            activeSnapshot.getClusterName(),
            activeSnapshot.getInstanceId());
    assertTrue(nonDeletedResult.isPresent());
    assertEquals(activeSnapshot, nonDeletedResult.get());
  }

  @Test
  public void testUpdateAndDeleteAWSAdminBackupSnapshot() {
    final AWSAdminBackupSnapshot awsSnapshot =
        getAWSAdminBackupSnapshot(new ObjectId(), "clusterAWS", "instanceAWS", Status.PENDING);
    _adminBackupSnapshotDao.create(awsSnapshot);
    Optional<AdminBackupSnapshot> result = _adminBackupSnapshotDao.find(awsSnapshot.getId());
    assertTrue(result.isPresent());
    AWSAdminBackupSnapshot awsResult = (AWSAdminBackupSnapshot) result.get();
    assertNull(awsResult.getAccountId());
    assertNull(awsResult.getEbsSnapshotId());
    assertNull(awsResult.getSourceEbsVolumeId());

    final ObjectId accountId = new ObjectId();
    final String snapshotId = "snapshotAWS";
    final String sourceEbsVolumeId = "sourceEbsVolumeId";
    _awsAdminBackupSnapshotDao.updateAWSAdminBackupSnapshot(
        awsSnapshot.getId(), accountId, snapshotId, sourceEbsVolumeId);
    result = _adminBackupSnapshotDao.find(awsSnapshot.getId());
    assertTrue(result.isPresent());
    awsResult = (AWSAdminBackupSnapshot) result.get();
    assertEquals(accountId, awsResult.getAccountId());
    assertEquals(snapshotId, awsResult.getEbsSnapshotId());
    assertEquals(sourceEbsVolumeId, awsResult.getSourceEbsVolumeId());
    assertEquals(Status.PENDING, awsResult.getStatus());

    _adminBackupSnapshotDao.markSnapshotDeleted(CloudProvider.AWS, snapshotId);
    awsResult = (AWSAdminBackupSnapshot) _adminBackupSnapshotDao.find(awsSnapshot.getId()).get();
    assertEquals(Status.DELETED, awsResult.getStatus());
  }

  @Test
  public void testUpdateAzureAdminBackupSnapshot() {
    final String clusterName = "clusterAzure";
    final String instanceName = "instanceAzure";
    final AzureAdminBackupSnapshot azureSnapshot =
        getAzureAdminBackupSnapshot(new ObjectId(), clusterName, instanceName, Status.PENDING);
    _adminBackupSnapshotDao.create(azureSnapshot);
    Optional<AdminBackupSnapshot> result = _adminBackupSnapshotDao.find(azureSnapshot.getId());
    assertTrue(result.isPresent());
    AzureAdminBackupSnapshot azureResult = (AzureAdminBackupSnapshot) result.get();
    assertNull(azureResult.getSubscriptionId());
    assertNull(azureResult.getDiskSnapshotName());
    assertNull(azureResult.getResourceGroupName());
    assertNull(azureResult.getSourceDiskName());

    final ObjectId subscriptionId = new ObjectId();
    final String diskSnapshotName = "diskSnapshotName";
    final String resourceGroupName = "resourceGroupName";
    final String sourceDiskName = "sourceDiskName";

    _azureAdminBackupSnapshotDao.updateAzureAdminBackupSnapshot(
        azureSnapshot.getId(), subscriptionId, resourceGroupName, sourceDiskName, diskSnapshotName);
    result = _adminBackupSnapshotDao.find(azureSnapshot.getId());
    assertTrue(result.isPresent());
    azureResult = (AzureAdminBackupSnapshot) result.get();
    assertEquals(diskSnapshotName, azureResult.getDiskSnapshotName());
    assertEquals(resourceGroupName, azureResult.getResourceGroupName());
    assertEquals(sourceDiskName, azureResult.getSourceDiskName());
  }

  @Test
  public void testUpdateSnapshotStatus() {
    final AWSAdminBackupSnapshot awsSnapshot =
        getAWSAdminBackupSnapshot(new ObjectId(), "clusterAWS", "instanceAWS", Status.PENDING);
    _adminBackupSnapshotDao.create(awsSnapshot);
    Optional<AdminBackupSnapshot> result = _adminBackupSnapshotDao.find(awsSnapshot.getId());
    assertTrue(result.isPresent());
    assertEquals(Status.PENDING, result.get().getStatus());

    _adminBackupSnapshotDao.updateSnapshotStatus(awsSnapshot.getId(), Status.ACTIVE);
    result = _adminBackupSnapshotDao.find(awsSnapshot.getId());
    assertTrue(result.isPresent());
    assertEquals(Status.ACTIVE, result.get().getStatus());
  }

  @Test
  public void testUpdateSnapshotExpirationDate() {
    final AWSAdminBackupSnapshot awsSnapshot =
        getAWSAdminBackupSnapshot(new ObjectId(), "clusterAWS", "instanceAWS", Status.PENDING);
    _adminBackupSnapshotDao.create(awsSnapshot);
    Optional<AdminBackupSnapshot> result = _adminBackupSnapshotDao.find(awsSnapshot.getId());
    assertTrue(result.isPresent());
    assertEquals(awsSnapshot.getExpirationDate(), result.get().getExpirationDate());

    final Date newDate = new Date(awsSnapshot.getExpirationDate().getTime() + 1000);
    _adminBackupSnapshotDao.updateSnapshotExpirationDate(awsSnapshot.getId(), newDate);
    result = _adminBackupSnapshotDao.find(awsSnapshot.getId());
    assertTrue(result.isPresent());
    assertEquals(newDate, result.get().getExpirationDate());
  }

  @Test
  public void testFindNonDeletedSnapshotForCluster() {
    final ObjectId projectId = new ObjectId();
    final String clusterName = "cluster0";
    final GCPAdminBackupSnapshot gcpSnapshot =
        getGCPAdminBackupSnapshot(projectId, clusterName, "instanceGCP", Status.PENDING);
    final AWSAdminBackupSnapshot awsSnapshot =
        getAWSAdminBackupSnapshot(projectId, clusterName, "instanceAWS", Status.PENDING);
    final AzureAdminBackupSnapshot azureSnapshot =
        getAzureAdminBackupSnapshot(projectId, clusterName, "instanceAzure", Status.DELETED);
    _adminBackupSnapshotDao.create(gcpSnapshot);
    _adminBackupSnapshotDao.create(awsSnapshot);
    _adminBackupSnapshotDao.create(azureSnapshot);

    assertEquals(3, _adminBackupSnapshotDao.findAll().size());

    List<AdminBackupSnapshot> snapshots =
        _adminBackupSnapshotDao.findNonDeletedSnapshotForCluster(projectId, clusterName);
    assertEquals(2, snapshots.size());
    final AWSAdminBackupSnapshot awsResult =
        (AWSAdminBackupSnapshot)
            snapshots.stream()
                .filter(e -> e.getCloudProvider() == CloudProvider.AWS)
                .findFirst()
                .get();
    final GCPAdminBackupSnapshot gcpResult =
        (GCPAdminBackupSnapshot)
            snapshots.stream()
                .filter(e -> e.getCloudProvider() == CloudProvider.GCP)
                .findFirst()
                .get();
    assertEquals(awsSnapshot, awsResult);
    assertEquals(gcpSnapshot, gcpResult);
  }
}
