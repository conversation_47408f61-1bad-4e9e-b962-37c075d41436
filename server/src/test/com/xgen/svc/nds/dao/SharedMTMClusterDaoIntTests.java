package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.flex._private.dao.FlexMTMClusterDao;
import com.xgen.cloud.nds.flex._public.model.FlexMTMCluster;
import com.xgen.cloud.nds.free._private.dao.SharedMTMClusterDao;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.free._public.model.SharedTenantClusterConfiguration;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SharedMTMClusterDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private SharedMTMClusterDao _sharedMTMClusterDao;
  @Inject private ServerlessMTMClusterDao _serverlessMTMClusterDao;
  @Inject private FlexMTMClusterDao _flexMTMClusterDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _sharedMTMClusterDao.ensureIndexes();
  }

  @Test
  public void testFindByCloudProvider() {
    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());

    _sharedMTMClusterDao.insert(sharedCluster);
    final SharedMTMCluster mtmClusterId =
        _sharedMTMClusterDao
            .findByCloudProviderRegionNameAndTenantInstanceSize(CloudProvider.AWS, null, null)
            .get(0);
    assertEquals(sharedCluster.getGroupId(), mtmClusterId.getGroupId());
    assertEquals(sharedCluster.getName(), mtmClusterId.getName());
  }

  @Test
  public void testFindByCloudProvider_IncorrectCloudProvider() {
    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());

    _sharedMTMClusterDao.insert(sharedCluster);
    final List<SharedMTMCluster> mtmClustersAzure =
        _sharedMTMClusterDao.findByCloudProviderRegionNameAndTenantInstanceSize(
            CloudProvider.AZURE, null, null);
    assertTrue(mtmClustersAzure.isEmpty());
  }

  @Test
  public void testFindByCloudProviderAndRegionName() {
    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());

    _sharedMTMClusterDao.insert(sharedCluster);
    final SharedMTMCluster mtmClusterId =
        _sharedMTMClusterDao
            .findByCloudProviderRegionNameAndTenantInstanceSize(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, null)
            .get(0);
    assertEquals(sharedCluster.getGroupId(), mtmClusterId.getGroupId());
    assertEquals(sharedCluster.getName(), mtmClusterId.getName());
  }

  @Test
  public void testFindByCloudProviderAndTenantInstanceSize() {
    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());

    _sharedMTMClusterDao.insert(sharedCluster);
    final SharedMTMCluster mtmClusterId =
        _sharedMTMClusterDao
            .findByCloudProviderRegionNameAndTenantInstanceSize(
                CloudProvider.AWS, null, FreeInstanceSize.M0)
            .get(0);
    assertEquals(sharedCluster.getGroupId(), mtmClusterId.getGroupId());
    assertEquals(sharedCluster.getName(), mtmClusterId.getName());
  }

  @Test
  public void testFindByCloudProviderRegionNameAndTenantInstanceSize() {
    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster());

    _sharedMTMClusterDao.insert(sharedCluster);
    final SharedMTMCluster mtmClusterId =
        _sharedMTMClusterDao
            .findByCloudProviderRegionNameAndTenantInstanceSize(
                sharedCluster.getProviderName(),
                sharedCluster.getRegion(),
                sharedCluster.getTenantInstanceSize())
            .get(0);
    assertEquals(sharedCluster.getGroupId(), mtmClusterId.getGroupId());
    assertEquals(sharedCluster.getName(), mtmClusterId.getName());
  }

  @Test
  public void testFindAllSharedClustersQueryFilter_withSkipAndLimit() {
    for (int i = 0; i < 10; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(
              NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-" + i));
      _sharedMTMClusterDao.insert(mtm);
    }
    final Map<String, String> filters = new HashMap<>();

    assertEquals(10, _sharedMTMClusterDao.findAllSharedClustersQueryFilter(filters, 10, 0).size());
    assertEquals(10, _sharedMTMClusterDao.findAllSharedClustersQueryFilter(filters, 20, 0).size());
    assertEquals(5, _sharedMTMClusterDao.findAllSharedClustersQueryFilter(filters, 5, 0).size());
    assertEquals(5, _sharedMTMClusterDao.findAllSharedClustersQueryFilter(filters, 10, 5).size());
    assertEquals(5, _sharedMTMClusterDao.findAllSharedClustersQueryFilter(filters, 20, 5).size());
  }

  @Test
  public void testFindAllSharedClustersQueryFilter_withFilter() {
    final SharedMTMCluster mtmAws =
        new SharedMTMCluster(
            NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-aws"));
    _sharedMTMClusterDao.insert(mtmAws);
    final SharedMTMCluster mtmAzure =
        new SharedMTMCluster(
                NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-azure"))
            .copy()
            .setProviderName(CloudProvider.AZURE)
            .setRegion(AzureRegionName.US_EAST_2)
            .build();
    _sharedMTMClusterDao.insert(mtmAzure);
    final SharedMTMCluster mtmAwsM2 =
        new SharedMTMCluster(
                NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-aws-1"))
            .copy()
            .setTenantInstanceSize(FreeInstanceSize.M2)
            .build();
    _sharedMTMClusterDao.insert(mtmAwsM2);
    final SharedMTMCluster mtmAzureM2 =
        new SharedMTMCluster(
                NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-azure-1"))
            .copy()
            .setProviderName(CloudProvider.AZURE)
            .setRegion(AzureRegionName.US_EAST_2)
            .setTenantInstanceSize(FreeInstanceSize.M2)
            .build();
    _sharedMTMClusterDao.insert(mtmAzureM2);

    assertEquals(
        2,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("cloudProvider", "AWS"), 5, 0)
            .size());
    assertTrue(
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("cloudProvider", "AWS"), 5, 0)
            .containsAll(List.of(mtmAws, mtmAwsM2)));
    assertEquals(
        2,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("regionName", "US_EAST_2"), 5, 0)
            .size());
    assertTrue(
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("regionName", "US_EAST_2"), 5, 0)
            .containsAll(List.of(mtmAzure, mtmAzureM2)));
    assertEquals(
        2,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("instanceSize", "M2"), 5, 0)
            .size());
    assertTrue(
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("instanceSize", "M2"), 5, 0)
            .containsAll(List.of(mtmAzureM2, mtmAwsM2)));
    assertEquals(
        1,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(
                Map.of("cloudProvider", "AWS", "instanceSize", "M2"), 5, 0)
            .size());
    assertTrue(
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(
                Map.of("cloudProvider", "AWS", "instanceSize", "M2"), 5, 0)
            .contains(mtmAwsM2));
    assertEquals(
        1,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("searchValue", "shared-mtm-aws"), 5, 0)
            .size());
    assertEquals(
        mtmAws,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(Map.of("searchValue", "shared-mtm-aws"), 5, 0)
            .get(0));
    assertEquals(
        1,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(
                Map.of("searchValue", mtmAzure.getGroupId().toString()), 5, 0)
            .size());
    assertEquals(
        mtmAzure,
        _sharedMTMClusterDao
            .findAllSharedClustersQueryFilter(
                Map.of("searchValue", mtmAzure.getGroupId().toString()), 5, 0)
            .get(0));
  }

  @Test
  public void testFindSharedClustersByGroupId_withSkipAndLimit() {
    final ObjectId groupId = new ObjectId();

    for (int i = 0; i < 10; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster(groupId, "shared-mtm" + i));
      _sharedMTMClusterDao.insert(mtm);
    }

    for (int i = 0; i < 10; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(
              NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm" + i));
      _sharedMTMClusterDao.insert(mtm);
    }

    assertEquals(10, _sharedMTMClusterDao.findSharedClustersByGroupId(groupId).size());
    assertEquals(10, _sharedMTMClusterDao.findSharedClustersByGroupId(groupId, 10, 0).size());
    assertEquals(10, _sharedMTMClusterDao.findSharedClustersByGroupId(groupId, 20, 0).size());
    assertEquals(5, _sharedMTMClusterDao.findSharedClustersByGroupId(groupId, 5, 0).size());
    assertEquals(5, _sharedMTMClusterDao.findSharedClustersByGroupId(groupId, 10, 5).size());
    assertEquals(5, _sharedMTMClusterDao.findSharedClustersByGroupId(groupId, 20, 5).size());
  }

  @Test
  public void testCountAllSharedClusters() {
    for (int i = 0; i < 10; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(
              NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-" + i));
      _sharedMTMClusterDao.insert(mtm);
    }

    assertEquals(10L, _sharedMTMClusterDao.countAllSharedClusters(Map.of()));

    for (int i = 10; i < 15; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(
              NDSModelTestFactory.getSharedMTMCluster(new ObjectId(), "shared-mtm-" + i));
      _sharedMTMClusterDao.insert(mtm);
    }

    assertEquals(15L, _sharedMTMClusterDao.countAllSharedClusters(Map.of()));
  }

  @Test
  public void testCountSharedClustersByGroupId() {
    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();

    for (int i = 0; i < 10; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster(groupId1, "shared-mtm" + i));
      _sharedMTMClusterDao.insert(mtm);
    }

    for (int i = 0; i < 5; i++) {
      final SharedMTMCluster mtm =
          new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster(groupId2, "shared-mtm" + i));
      _sharedMTMClusterDao.insert(mtm);
    }

    assertEquals(10L, _sharedMTMClusterDao.countSharedClustersByGroupId(groupId1));
    assertEquals(5L, _sharedMTMClusterDao.countSharedClustersByGroupId(groupId2));
  }

  @Test
  public void testFindFlexPackingExperimentCandidateClusterWithAvailableCapacity() {
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final FreeInstanceSize instanceSize = FreeInstanceSize.M5;
    final SharedMTMCluster mtm_notCandidate =
        new SharedMTMCluster(
            new ObjectId(),
            "mtm_notCandidate",
            true,
            1,
            cloudProvider,
            regionName,
            instanceSize,
            "7.0",
            new ObjectId());
    _sharedMTMClusterDao.insert(mtm_notCandidate);

    assertTrue(
        _sharedMTMClusterDao
            .findFlexPackingExperimentCandidateClusterWithAvailableCapacity(
                cloudProvider, regionName, instanceSize, Optional.empty(), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_candidateButAssignmentDisabled =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_candidateButAssignmentDisabled",
                false,
                1,
                cloudProvider,
                regionName,
                instanceSize,
                "7.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_candidateButAssignmentDisabled);

    assertTrue(
        _sharedMTMClusterDao
            .findFlexPackingExperimentCandidateClusterWithAvailableCapacity(
                cloudProvider, regionName, instanceSize, Optional.empty(), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_candidateButNoCapacity =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_candidateButNoCapacity",
                true,
                0,
                cloudProvider,
                regionName,
                instanceSize,
                "7.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_candidateButNoCapacity);

    assertTrue(
        _sharedMTMClusterDao
            .findFlexPackingExperimentCandidateClusterWithAvailableCapacity(
                cloudProvider, regionName, instanceSize, Optional.empty(), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_wrongVersion =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_wrongVersion",
                true,
                0,
                cloudProvider,
                regionName,
                instanceSize,
                "8.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_wrongVersion);

    assertTrue(
        _sharedMTMClusterDao
            .findFlexPackingExperimentCandidateClusterWithAvailableCapacity(
                cloudProvider, regionName, instanceSize, Optional.of("7.0"), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_candidate =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_candidate",
                true,
                1,
                cloudProvider,
                regionName,
                instanceSize,
                "7.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_candidate);

    final Optional<SharedMTMCluster> result =
        _sharedMTMClusterDao.findFlexPackingExperimentCandidateClusterWithAvailableCapacity(
            cloudProvider, regionName, instanceSize, Optional.empty(), null, false);
    assertTrue(result.isPresent());
    assertEquals(mtm_candidate.getName(), result.get().getName());
  }

  @Test
  public void testFindUSSPackingExperimentCandidateClusterWithAssignmentEnabled() {
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final FreeInstanceSize instanceSize = FreeInstanceSize.M5;
    final SharedMTMCluster mtm_notCandidate =
        new SharedMTMCluster(
            new ObjectId(),
            "mtm_notCandidate",
            true,
            1,
            cloudProvider,
            regionName,
            instanceSize,
            "7.0",
            new ObjectId());
    _sharedMTMClusterDao.insert(mtm_notCandidate);

    assertTrue(
        _sharedMTMClusterDao
            .findUSSPackingExperimentCandidateClusterWithAssignmentEnabled(
                cloudProvider, regionName, instanceSize, Optional.of("7.0"), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_candidateButAssignmentDisabled =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_candidateButAssignmentDisabled",
                false,
                1,
                cloudProvider,
                regionName,
                instanceSize,
                "7.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_candidateButAssignmentDisabled);

    assertTrue(
        _sharedMTMClusterDao
            .findUSSPackingExperimentCandidateClusterWithAssignmentEnabled(
                cloudProvider, regionName, instanceSize, Optional.of("7.0"), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_wrongVersion =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_wrongVersion",
                true,
                0,
                cloudProvider,
                regionName,
                instanceSize,
                "8.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_wrongVersion);

    assertTrue(
        _sharedMTMClusterDao
            .findUSSPackingExperimentCandidateClusterWithAssignmentEnabled(
                cloudProvider, regionName, instanceSize, Optional.of("7.0"), null, false)
            .isEmpty());

    final SharedMTMCluster mtm_candidate =
        new SharedMTMCluster(
                new ObjectId(),
                "mtm_candidate",
                true,
                1,
                cloudProvider,
                regionName,
                instanceSize,
                "7.0",
                new ObjectId())
            .copy()
            .setIsUSSPackingExperimentCandidate(true)
            .build();
    _sharedMTMClusterDao.insert(mtm_candidate);

    final Optional<SharedMTMCluster> result =
        _sharedMTMClusterDao.findUSSPackingExperimentCandidateClusterWithAssignmentEnabled(
            cloudProvider, regionName, instanceSize, Optional.of("7.0"), null, false);
    assertTrue(result.isPresent());
    assertEquals(mtm_candidate.getName(), result.get().getName());
  }

  @Test
  public void testFindDistinctSharedClusterConfigurations() {
    final ServerlessMTMCluster serverlessCluster =
        new ServerlessMTMCluster(NDSModelTestFactory.getServerlessMTMCluster("serverless-mtm"));
    _sharedMTMClusterDao.insert(serverlessCluster);

    // test no shared mtms
    {
      final Set<SharedTenantClusterConfiguration> tenantClusterConfigurations =
          _sharedMTMClusterDao.findDistinctSharedClusterConfigurations();
      assertTrue(tenantClusterConfigurations.isEmpty());
    }

    final SharedMTMCluster sharedCluster =
        new SharedMTMCluster(NDSModelTestFactory.getSharedMTMCluster("shared-mtm"));
    _sharedMTMClusterDao.insert(sharedCluster);

    // test shared mtm present
    {
      final Set<SharedTenantClusterConfiguration> tenantClusterConfigurations =
          _sharedMTMClusterDao.findDistinctSharedClusterConfigurations();
      assertEquals(1, tenantClusterConfigurations.size());
      final SharedTenantClusterConfiguration tenantClusterConfiguration =
          tenantClusterConfigurations.stream().findFirst().get();
      assertEquals(sharedCluster.getRegion(), tenantClusterConfiguration.getRegionName());
      assertEquals(
          sharedCluster.getTenantInstanceSize(),
          tenantClusterConfiguration.getTenantInstanceSize());
      assertEquals(
          sharedCluster.getMongoDBMajorVersion(),
          tenantClusterConfiguration.getMongoDBMajorVersion());
    }

    _sharedMTMClusterDao.deleteCluster(
        serverlessCluster.getGroupId(),
        serverlessCluster.getName(),
        serverlessCluster.getMTMClusterType());
    _sharedMTMClusterDao.deleteCluster(
        sharedCluster.getGroupId(), sharedCluster.getName(), sharedCluster.getMTMClusterType());

    // test no mtms
    {
      final Set<SharedTenantClusterConfiguration> tenantClusterConfigurations =
          _sharedMTMClusterDao.findDistinctSharedClusterConfigurations();
      assertTrue(tenantClusterConfigurations.isEmpty());
    }
  }

  @Test
  public void testFindSharedMTMs() {
    final ClusterDescriptionId sharedClusterDescriptionId1 =
        new ClusterDescriptionId("shared-mtm-1", oid(1));
    final ClusterDescriptionId sharedClusterDescriptionId2 =
        new ClusterDescriptionId("shared-mtm-2", oid(1));
    final ClusterDescriptionId sharedClusterDescriptionId3 =
        new ClusterDescriptionId("shared-mtm-3", oid(2));

    final ClusterDescriptionId serverlessClusterDescriptionId1 =
        new ClusterDescriptionId("serverless-mtm-1", oid(3));
    final ClusterDescriptionId serverlessClusterDescriptionId2 =
        new ClusterDescriptionId("serverless-mtm-2", oid(3));
    final ClusterDescriptionId serverlessClusterDescriptionId3 =
        new ClusterDescriptionId("serverless-mtm-3", oid(4));

    final ClusterDescriptionId flexClusterDescriptionId1 =
        new ClusterDescriptionId("flex-mtm-1", oid(5));
    final ClusterDescriptionId flexClusterDescriptionId2 =
        new ClusterDescriptionId("flex-mtm-2", oid(5));
    final ClusterDescriptionId flexClusterDescriptionId3 =
        new ClusterDescriptionId("flex-mtm-3", oid(6));

    final SharedMTMCluster sharedMTMCluster1 =
        new SharedMTMCluster(
            NDSModelTestFactory.getSharedMTMCluster(
                sharedClusterDescriptionId1.getGroupId(),
                sharedClusterDescriptionId1.getClusterName()));
    final SharedMTMCluster sharedMTMCluster2 =
        new SharedMTMCluster(
            NDSModelTestFactory.getSharedMTMCluster(
                sharedClusterDescriptionId2.getGroupId(),
                sharedClusterDescriptionId2.getClusterName()));
    final SharedMTMCluster sharedMTMCluster3 =
        new SharedMTMCluster(
            NDSModelTestFactory.getSharedMTMCluster(
                sharedClusterDescriptionId3.getGroupId(),
                sharedClusterDescriptionId3.getClusterName()));
    Stream.of(sharedMTMCluster1, sharedMTMCluster2, sharedMTMCluster3)
        .forEach(mtmCluster -> _sharedMTMClusterDao.insert(mtmCluster));

    Stream.of(
            serverlessClusterDescriptionId1,
            serverlessClusterDescriptionId2,
            serverlessClusterDescriptionId3)
        .map(
            cid ->
                new ServerlessMTMCluster(
                    NDSModelTestFactory.getServerlessMTMCluster(
                        cid.getGroupId(), cid.getClusterName())))
        .forEach(serverlessMTMCluster -> _serverlessMTMClusterDao.insert(serverlessMTMCluster));

    Stream.of(flexClusterDescriptionId1, flexClusterDescriptionId2, flexClusterDescriptionId3)
        .map(
            cid ->
                new FlexMTMCluster(
                    NDSModelTestFactory.getFlexMTMCluster(cid.getClusterName(), cid.getGroupId())))
        .forEach(flexMTMCluster -> _flexMTMClusterDao.insert(flexMTMCluster));

    // Test empty list of cluster description IDs
    assertEquals(
        Collections.emptyList(), _sharedMTMClusterDao.findSharedMTMs(Collections.emptyList()));

    // Test lists of shared-only cluster description IDs
    assertEquals(
        List.of(sharedMTMCluster1),
        _sharedMTMClusterDao.findSharedMTMs(List.of(sharedClusterDescriptionId1)));
    assertEquals(
        List.of(sharedMTMCluster2),
        _sharedMTMClusterDao.findSharedMTMs(List.of(sharedClusterDescriptionId2)));
    assertEquals(
        List.of(sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(List.of(sharedClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster1, sharedMTMCluster2),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(sharedClusterDescriptionId1, sharedClusterDescriptionId2)));
    assertEquals(
        List.of(sharedMTMCluster1, sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(sharedClusterDescriptionId1, sharedClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster2, sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(sharedClusterDescriptionId2, sharedClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster1, sharedMTMCluster2, sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3)));

    // Test lists of shared, serverless, and flex cluster description IDs
    assertEquals(
        List.of(sharedMTMCluster1),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId1,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster2),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId2,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster1, sharedMTMCluster2),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster1, sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster2, sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
    assertEquals(
        List.of(sharedMTMCluster1, sharedMTMCluster2, sharedMTMCluster3),
        _sharedMTMClusterDao.findSharedMTMs(
            List.of(
                sharedClusterDescriptionId1,
                sharedClusterDescriptionId2,
                sharedClusterDescriptionId3,
                serverlessClusterDescriptionId1,
                serverlessClusterDescriptionId2,
                serverlessClusterDescriptionId3,
                flexClusterDescriptionId1,
                flexClusterDescriptionId2,
                flexClusterDescriptionId3)));
  }
}
