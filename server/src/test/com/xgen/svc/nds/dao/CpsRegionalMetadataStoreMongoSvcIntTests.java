package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.cps.pit._private.dao.CpsRegionalMetadataStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsRegionalMetadataStoreConfig;
import com.xgen.cloud.cps.pit._public.model.CpsRegionalMetadataStoreConfig.FieldDefs;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CpsRegionalMetadataStoreMongoSvcIntTests extends JUnit5BaseSvcTest {

  private static final String STORE_ID_1 = "s3oplog_storeid_1";
  private static final String STORE_ID_2 = "s3oplog_storeid_2";
  private static final String STORE_ID_3 = "s3oplog_storeid_3";
  private static final String BRS_STORE_ID_1 = "brs_s3oplog_storeid_1";
  private static final String BRS_STORE_ID_2 = "brs_s3oplog_storeid_2";
  private static final String BRS_STORE_ID_3 = "brs_s3oplog_storeid_3";
  private static final int MAX_CONNECTION_IDLE_TIME = 1000;

  @Inject private CpsRegionalMetadataStoreConfigDao _cpsRegionalMetadataStoreConfigDao;

  @Inject private CpsRegionalMetadataStoreMongoSvc _cpsRegionalMetadataStoreMongoSvc;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    // Clean start
    _cpsRegionalMetadataStoreMongoSvc.close();

    // Inserting two metadata store configs
    _cpsRegionalMetadataStoreConfigDao.insertForTests(
        new CpsRegionalMetadataStoreConfig(
            STORE_ID_1,
            BRS_STORE_ID_1,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            true,
            false,
            false,
            null,
            "fake_deployment_id",
            MAX_CONNECTION_IDLE_TIME));

    _cpsRegionalMetadataStoreConfigDao.insertForTests(
        new CpsRegionalMetadataStoreConfig(
            STORE_ID_2,
            BRS_STORE_ID_2,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            false,
            null,
            "fake_deployment_id",
            MAX_CONNECTION_IDLE_TIME));
  }

  @AfterEach
  @Override
  public void tearDown() throws Exception {
    super.tearDown();
    // Prevent test leaking MongoClients
    _cpsRegionalMetadataStoreMongoSvc.close();
  }

  @Test
  public void allMongosIncludesUnassignable() {
    final List<MongoClient> allMongos = _cpsRegionalMetadataStoreMongoSvc.getAllMongos();
    assertEquals(2, allMongos.size(), "Unexpected mongos:" + allMongos);

    assertEquals(
        allMongos.get(0).getMongoClientOptions().getMaxConnectionIdleTime(),
        MAX_CONNECTION_IDLE_TIME);
  }

  @Test
  public void allEntriesIncludesUnassignable() {
    final Set<Entry<String, MongoClient>> allEntries =
        _cpsRegionalMetadataStoreMongoSvc.getAllEntries();
    assertEquals(2, allEntries.size(), "Unexpected entries:" + allEntries);
  }

  @Test
  public void getConfigurationReturnsConfigWhenMissingMaxConnectionIdleTime() {
    final BasicDBObject obj =
        new CpsRegionalMetadataStoreConfig(
                STORE_ID_3,
                BRS_STORE_ID_3,
                "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
                false,
                false,
                "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
                false,
                false,
                false,
                null,
                "fake_deployment_id",
                10)
            .toDBObject();
    obj.removeField(FieldDefs.MAX_CONNECTION_IDLE_TIME);

    _cpsRegionalMetadataStoreConfigDao.insertForTests(
        CpsRegionalMetadataStoreConfig.fromDBObject(obj));

    final List<MongoClient> allMongos = _cpsRegionalMetadataStoreMongoSvc.getAllMongos();

    assertEquals(3, allMongos.size(), "Unexpected entries:" + allMongos);
    assertEquals(
        allMongos.stream()
            .filter(m -> m.getMongoClientOptions().getMaxConnectionIdleTime() == 0)
            .count(),
        1);
  }

  @Test
  public void getConfigurationReturnsConfigForAnyAssignableStatus_FoundInCache() {
    // try case when found in connection cache
    assertNotNull(
        _cpsRegionalMetadataStoreMongoSvc.getConfiguration(STORE_ID_1),
        "Failed to find cached assignable store: " + STORE_ID_1);

    assertNotNull(
        _cpsRegionalMetadataStoreMongoSvc.getConfiguration(STORE_ID_2),
        "Failed to find cached unassignable store: " + STORE_ID_2);
  }

  @Test
  public void getConfigurationReturnsConfigForAnyAssignableStatus_Uncached() {
    // try case when not found in cache and created on demand
    _cpsRegionalMetadataStoreMongoSvc.close();

    // load cache
    _cpsRegionalMetadataStoreMongoSvc.getAllEntries();

    // add new entry which will be uncached.
    _cpsRegionalMetadataStoreConfigDao.insertForTests(
        new CpsRegionalMetadataStoreConfig(
            STORE_ID_3,
            BRS_STORE_ID_3,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            false,
            null,
            "fake_deployment_id",
            0));

    assertNotNull(
        _cpsRegionalMetadataStoreMongoSvc.getConfiguration(STORE_ID_1),
        "Failed to find cached assignable store: " + STORE_ID_1);

    assertNotNull(
        _cpsRegionalMetadataStoreMongoSvc.getConfiguration(STORE_ID_3),
        "Failed to find uncached assignable store: " + STORE_ID_3);
  }
}
