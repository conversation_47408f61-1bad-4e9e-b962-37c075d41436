package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.MongoWriteException;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.nds.cloudprovider._private.dao.BaseMultiTargetConnectionRuleDao;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule.NdsProcessPortTyped;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public abstract class BaseMultiTargetConnectionRuleDaoIntBaseTests<
        V extends BaseMultiTargetConnectionRule, T extends BaseMultiTargetConnectionRuleDao<V>>
    extends JUnit5BaseSvcTest {

  // Due to no reification of non wildcard generics, have injection in subclass
  private T _dao;

  public abstract V createCloudProviderConnectionRule(
      final ObjectId pRuleId,
      final ObjectId pGroupId,
      final String pClusterName,
      final List<ObjectId> pInstanceIds,
      final NdsProcessPortTyped pPortType,
      final int pFrontentForwardedPort,
      final Date pDeletedDate,
      final ObjectId pContainerId,
      final ObjectId pPrivateEndpointConnectionId);

  private final ObjectId _groupId = new ObjectId();
  private final ObjectId _containerId = new ObjectId();
  private final ObjectId _privateEndpointConnectionId = new ObjectId();
  private final String _existingClusterName = "Existing1";

  public abstract T getDao();

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _dao = getDao();
    _dao.ensureIndexes();
  }

  private V createConnectionRuleOnPort(
      final int pForwardingPort, final ObjectId pGroupId, final String pClusterName) {
    return createCloudProviderConnectionRule(
        new ObjectId(),
        pGroupId,
        pClusterName,
        List.of(),
        BaseMultiTargetConnectionRule.NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
        pForwardingPort,
        null,
        _containerId,
        _privateEndpointConnectionId);
  }

  private V createConnectionRuleOnPort(
      final int pForwardingPort,
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pContainerId,
      final ObjectId pPrivateEndpointConnectionId) {
    return createCloudProviderConnectionRule(
        new ObjectId(),
        pGroupId,
        pClusterName,
        List.of(),
        BaseMultiTargetConnectionRule.NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
        pForwardingPort,
        null,
        pContainerId,
        pPrivateEndpointConnectionId);
  }

  V copyConnectionRuleWithDeletedDate(final V pOriginalRule, final Date pDeletedDate) {
    return createCloudProviderConnectionRule(
        pOriginalRule.getId(),
        pOriginalRule.getGroupId(),
        pOriginalRule.getClusterName(),
        pOriginalRule.getInstanceIds(),
        pOriginalRule.getNdsProcessPortTyped(),
        pOriginalRule.getReservedPortNumber(),
        pDeletedDate,
        pOriginalRule.getContainerIdForLB(),
        pOriginalRule.getPrivateLinkConnectionId());
  }

  @Test
  public void testCreate() {
    final V newRule = createConnectionRuleOnPort(2000, _groupId, _existingClusterName);
    _dao.reserveMultiTargetConnectionRule(newRule);
    final Optional<V> findResult = findDoc(newRule);
    assertTrue(findResult.isPresent());
    assertEquals(findResult.get(), newRule);
    try {
      _dao.reserveMultiTargetConnectionRule(newRule);
      fail();
    } catch (final MongoWriteException pE) {
      assertEquals(DbUtils.DUPLICATE_KEY_ERROR_CODE, Integer.valueOf(pE.getCode()));
    }
  }

  @Test
  public void testUpdate() throws SvcException {

    final ObjectId newInstanceOne = new ObjectId();
    final ObjectId newInstanceTwo = new ObjectId();
    final ObjectId newInstanceThree = new ObjectId();

    // Initial setup - create a new rule
    final V initialRule = createConnectionRuleOnPort(2000, _groupId, _existingClusterName);
    _dao.reserveMultiTargetConnectionRule(initialRule);

    // and add an instance to it
    _dao.addInstancesToExistingMultiTargetRule(initialRule, List.of(newInstanceOne));

    // We expect the new instance to be present
    assertEquals(findDoc(initialRule).get().getInstanceIds(), List.of(newInstanceOne));
    // Adding two more instances sees us with three total
    _dao.addInstancesToExistingMultiTargetRule(
        initialRule, List.of(newInstanceTwo, newInstanceThree));
    assertEquals(
        findDoc(initialRule).get().getInstanceIds(),
        List.of(newInstanceOne, newInstanceTwo, newInstanceThree));

    // Removing the 2nd instance leaves us with 1 and 3
    _dao.removeInstancesFromMultiTargetRule(initialRule, List.of(newInstanceTwo));
    assertEquals(
        findDoc(initialRule).get().getInstanceIds(), List.of(newInstanceOne, newInstanceThree));

    // Attempting to delete an instance that is in use raises an exception - shouldn't happen
    // but needs higher level concurrency control for prevention / not blocked by DAO
    assertEquals(
        Optional.empty(),
        _dao.markMultiTargetRuleAsInactive(
            initialRule.getGroupId(),
            initialRule.getClusterName(),
            initialRule.getNdsProcessPortTyped(),
            initialRule.getContainerIdForLB(),
            new Date()));

    // And removing all instances results in an empty list
    _dao.removeInstancesFromMultiTargetRule(initialRule, List.of(newInstanceOne, newInstanceThree));
    assertEquals(findDoc(initialRule).get().getInstanceIds(), List.of());

    // And with no instances using the rule marking it as inactive should proceed
    final Date deletedDate = new Date();
    final V initalRuleWithDeletedDate = copyConnectionRuleWithDeletedDate(initialRule, deletedDate);
    final Optional<V> oy =
        _dao.markMultiTargetRuleAsInactive(
            initialRule.getGroupId(),
            initialRule.getClusterName(),
            initialRule.getNdsProcessPortTyped(),
            initialRule.getContainerIdForLB(),
            deletedDate);
    assertEquals(Optional.of(initalRuleWithDeletedDate), oy);
  }

  @Test
  public void testGetProvisionedConnectionRulesForGroup() {
    final AtomicInteger portNum = new AtomicInteger(2000);
    final Function<ObjectId, List<V>> createRulesForGroup =
        (groupId) -> {
          final List<V> groupRules =
              List.of(
                  createConnectionRuleOnPort(portNum.incrementAndGet(), groupId, "1"),
                  createConnectionRuleOnPort(portNum.incrementAndGet(), groupId, "2"),
                  createConnectionRuleOnPort(portNum.incrementAndGet(), groupId, "3"));
          groupRules.forEach(rule -> _dao.reserveMultiTargetConnectionRule(rule));
          return groupRules;
        };
    final ObjectId groupOne = new ObjectId();
    final List<V> groupOneRules = createRulesForGroup.apply(groupOne);
    // The three target rules for the group are found
    assertEquals(groupOneRules, _dao.getProvisionedMultiTargetConnectionRulesForGroup(groupOne));

    final ObjectId groupTwo = new ObjectId();
    final List<V> groupTwoRules = createRulesForGroup.apply(groupTwo);
    // Still find group specific targets
    assertEquals(groupOneRules, _dao.getProvisionedMultiTargetConnectionRulesForGroup(groupOne));
    // And new separate group targets also found
    assertEquals(groupTwoRules, _dao.getProvisionedMultiTargetConnectionRulesForGroup(groupTwo));

    // Group w no rules finds no rules
    final ObjectId emptyGroup = new ObjectId();
    assertEquals(List.of(), _dao.getProvisionedMultiTargetConnectionRulesForGroup(emptyGroup));

    // inactive rules aren't returned
    final V ruleToInactivate = groupOneRules.get(0);
    assertNotEquals(
        Optional.empty(),
        _dao.markMultiTargetRuleAsInactive(
            ruleToInactivate.getGroupId(),
            ruleToInactivate.getClusterName(),
            ruleToInactivate.getNdsProcessPortTyped(),
            ruleToInactivate.getContainerIdForLB(),
            new Date()));
    assertEquals(
        groupOneRules.stream().skip(1).collect(Collectors.toList()),
        _dao.getProvisionedMultiTargetConnectionRulesForGroup(groupOne));
  }

  @Test
  public void testGetProvisionedConnectionRulesForCluster() {
    final List<ObjectId> containerIds = List.of(oid(1), oid(2), oid(3));
    final List<ObjectId> privateEndpointConnectionIds = List.of(oid(4), oid(5), oid(6));
    final AtomicInteger portNum = new AtomicInteger(2000);
    final Function<Pair<ObjectId, String>, List<V>> createRulesForCluster =
        (groupIdAndClusterNamePair) -> {
          final List<V> groupRules =
              List.of(
                  createConnectionRuleOnPort(
                      portNum.incrementAndGet(),
                      groupIdAndClusterNamePair.getLeft(),
                      groupIdAndClusterNamePair.getRight(),
                      containerIds.get(0),
                      privateEndpointConnectionIds.get(0)),
                  createConnectionRuleOnPort(
                      portNum.incrementAndGet(),
                      groupIdAndClusterNamePair.getLeft(),
                      groupIdAndClusterNamePair.getRight(),
                      containerIds.get(1),
                      privateEndpointConnectionIds.get(1)),
                  createConnectionRuleOnPort(
                      portNum.incrementAndGet(),
                      groupIdAndClusterNamePair.getLeft(),
                      groupIdAndClusterNamePair.getRight(),
                      containerIds.get(2),
                      privateEndpointConnectionIds.get(2)));
          groupRules.forEach(rule -> _dao.reserveMultiTargetConnectionRule(rule));
          return groupRules;
        };
    final ObjectId groupOne = new ObjectId();
    final String clusterOne = "cluster1";
    final List<V> groupOneClusterOneRules =
        createRulesForCluster.apply(Pair.of(groupOne, clusterOne));
    // The three target rules for the cluster are found
    assertEquals(
        groupOneClusterOneRules,
        _dao.getProvisionedConnectionRulesForCluster(groupOne, clusterOne));

    final ObjectId groupTwo = new ObjectId();
    final String clusterTwo = "cluster2";
    final List<V> groupTwoClusterTwoRules =
        createRulesForCluster.apply(Pair.of(groupTwo, clusterTwo));
    // Still find other cluster-specific rules
    assertEquals(
        groupOneClusterOneRules,
        _dao.getProvisionedConnectionRulesForCluster(groupOne, clusterOne));
    // And new separate targets also found
    assertEquals(
        groupTwoClusterTwoRules,
        _dao.getProvisionedConnectionRulesForCluster(groupTwo, clusterTwo));

    final List<V> groupOneClusterTwoRules =
        createRulesForCluster.apply(Pair.of(groupOne, clusterTwo));
    // Still find other cluster-specific rules
    assertEquals(
        groupOneClusterOneRules,
        _dao.getProvisionedConnectionRulesForCluster(groupOne, clusterOne));
    assertEquals(
        groupTwoClusterTwoRules,
        _dao.getProvisionedConnectionRulesForCluster(groupTwo, clusterTwo));
    // And new separate targets also found
    assertEquals(
        groupOneClusterTwoRules,
        _dao.getProvisionedConnectionRulesForCluster(groupOne, clusterTwo));

    // Group w no rules finds no rules
    final ObjectId emptyGroup = new ObjectId();
    assertEquals(List.of(), _dao.getProvisionedConnectionRulesForCluster(emptyGroup, clusterTwo));

    // inactive rules aren't returned
    final V ruleToInactivate = groupOneClusterOneRules.get(0);
    assertNotEquals(
        Optional.empty(),
        _dao.markMultiTargetRuleAsInactive(
            ruleToInactivate.getGroupId(),
            ruleToInactivate.getClusterName(),
            ruleToInactivate.getNdsProcessPortTyped(),
            ruleToInactivate.getContainerIdForLB(),
            new Date()));
    assertEquals(
        groupOneClusterOneRules.stream().skip(1).collect(Collectors.toList()),
        _dao.getProvisionedConnectionRulesForCluster(groupOne, clusterOne));
  }

  private Optional<V> findDoc(final V pDoc) {
    return _dao.findOneMultiTargetConnectionRule(
        pDoc.getGroupId(),
        pDoc.getClusterName(),
        pDoc.getNdsProcessPortTyped(),
        pDoc.getContainerIdForLB());
  }

  @Test
  public void testGetEarliestDeletedForGroup() {
    final ObjectId groupId = new ObjectId();
    final V ruleToDelete = createConnectionRuleOnPort(2000, groupId, "toDelete");
    final V ruleToKeep = createConnectionRuleOnPort(2001, groupId, "groupToKeep");
    final V ruleToDeleteLater =
        createConnectionRuleOnPort(2002, groupId, "groupToDeleteLaterInTime");
    // Setup - create two rules, assert none are inactive for this group, and mark one as inactive
    _dao.reserveMultiTargetConnectionRule(ruleToDelete);
    _dao.reserveMultiTargetConnectionRule(ruleToKeep);
    _dao.reserveMultiTargetConnectionRule(ruleToDeleteLater);

    // marking a rule as inactive causes us to find it when querying the oldest inactive
    final Date laterDeletionDate = new Date(2345);
    assertEquals(Optional.empty(), _dao.getOldestInactiveMultiTargetConnectionRule(groupId));
    assertEquals(
        Optional.of(copyConnectionRuleWithDeletedDate(ruleToDeleteLater, laterDeletionDate)),
        _dao.markMultiTargetRuleAsInactive(
            groupId,
            ruleToDeleteLater.getClusterName(),
            ruleToDeleteLater.getNdsProcessPortTyped(),
            ruleToDeleteLater.getContainerIdForLB(),
            laterDeletionDate));
    // Keep in sync w expected DB state - set deleted date on rule
    final V ruleToDeleteLaterWithDeletionDate =
        copyConnectionRuleWithDeletedDate(ruleToDeleteLater, laterDeletionDate);
    assertEquals(
        Optional.of(ruleToDeleteLaterWithDeletionDate),
        _dao.getOldestInactiveMultiTargetConnectionRule(groupId));

    // marking a rule inactive prior to an already inactive rule sees the earlier rule returned
    final Date deletionDate = new Date(1234);
    assertEquals(
        Optional.of(copyConnectionRuleWithDeletedDate(ruleToDelete, deletionDate)),
        _dao.markMultiTargetRuleAsInactive(
            groupId,
            ruleToDelete.getClusterName(),
            ruleToDelete.getNdsProcessPortTyped(),
            ruleToDelete.getContainerIdForLB(),
            deletionDate));
    // Keep in sync w expected DB state - set deleted date on rule
    final V ruleToDeleteWithDeletionDate =
        copyConnectionRuleWithDeletedDate(ruleToDelete, deletionDate);
    // Expect that the rule we asked to have marked inactive is present and marked as inactive
    assertEquals(
        Optional.of(ruleToDeleteWithDeletionDate),
        _dao.getOldestInactiveMultiTargetConnectionRule(groupId));
    assertEquals(Optional.of(ruleToDeleteWithDeletionDate), findDoc(ruleToDeleteWithDeletionDate));

    // For interop w 1:1 rules cases will arise where we need to delete a document so its ingress
    // port may be used for a 1:1 rule - test that doing so here fully removes the rule
    _dao.deleteOldestInactiveMultiTargetConnectionRule(groupId);
    assertEquals(Optional.empty(), findDoc(ruleToDelete));

    assertEquals(
        Optional.of(ruleToDeleteLaterWithDeletionDate), findDoc(ruleToDeleteLaterWithDeletionDate));
  }
}
