package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.cps.restore._private.dao.CpsBackupCursorFileListsDao;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class CpsBackupCursorFileListsDaoIntTests extends JUnit5BaseSvcTest {

  private static final ObjectId PROJECT_ID = new ObjectId();
  private static final String DEFAULT_CLUSTER_NAME = "clusterName";
  private static final String DEFAULT_RS_ID = "rsId";
  private static final ObjectId CLUSTER_UNIQUE_ID = ObjectId.get();
  private static final long CURSOR_ID = 123456789L;
  private static final long EXTENDED_CURSOR_ID = 987654321L;
  private static final ObjectId SNAPSHOT_ID = new ObjectId();
  private static final String SOURCE_HOSTNAME = "sourcehostname";
  private static final String BACKUP_ID = "b7b7b912-2602-4e48-b351-ab982ba5c4a4";

  @Inject private CpsBackupCursorFileListsDao _cpsBackupCursorFileListsDao;

  @Test
  public void testAddNewFileList() {
    final int numBatches = 6;
    final boolean isExtended = false;
    final List<CpsBackupCursorFileList> listOfCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numBatches, isExtended, CURSOR_ID);

    for (int i = 0; i < numBatches; i++) {
      _cpsBackupCursorFileListsDao.addNewFileList(listOfCpsBackupCursorFileList.get(i));
    }
    assertEquals(numBatches, _cpsBackupCursorFileListsDao.findAll().count());
  }

  @Test
  public void testGetCursorFiles() {
    final int numBatches = 3;
    final boolean extend = false;
    final List<CpsBackupCursorFileList> listOfCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numBatches, extend, CURSOR_ID);

    for (final CpsBackupCursorFileList list : listOfCpsBackupCursorFileList) {
      _cpsBackupCursorFileListsDao.addNewFileList(list);
    }

    final boolean extendIt = true;
    final List<CpsBackupCursorFileList> listOfExtendedCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numBatches, extendIt, EXTENDED_CURSOR_ID);
    for (int i = 0; i < numBatches; i++) {
      _cpsBackupCursorFileListsDao.addNewFileList(listOfExtendedCpsBackupCursorFileList.get(i));
    }

    final ModelCursor<CpsBackupCursorFileList> cursor =
        _cpsBackupCursorFileListsDao.getCursorFiles(SNAPSHOT_ID);

    assertEquals(6, cursor.count());
    for (int i = 1; i <= numBatches; i++) {
      final CpsBackupCursorFileList cpsBackupCursorFileList = cursor.next();
      assertFalse(cpsBackupCursorFileList.isExtended());
      assertEquals(i, cpsBackupCursorFileList.getBatchIndex());
    }
    for (int i = 1; i < numBatches; i++) {
      final CpsBackupCursorFileList cpsBackupCursorFileList = cursor.next();
      assertTrue(cpsBackupCursorFileList.isExtended());
      assertEquals(i, cpsBackupCursorFileList.getBatchIndex());
    }
  }

  @Test
  public void testRemoval() {
    final int numBatches = 2;
    final boolean extend = false;
    final List<CpsBackupCursorFileList> listOfCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numBatches, extend, CURSOR_ID);
    for (final CpsBackupCursorFileList list : listOfCpsBackupCursorFileList) {
      _cpsBackupCursorFileListsDao.addNewFileList(list);
    }

    assertEquals(numBatches, _cpsBackupCursorFileListsDao.findAll().count());
    assertEquals(numBatches, _cpsBackupCursorFileListsDao.removeBySnapshotId(SNAPSHOT_ID));
    assertEquals(0, _cpsBackupCursorFileListsDao.findAll().count());
  }

  @Test
  public void testFindFirstBatch() {
    final int numBatches = 2;
    final boolean extend = false;
    final List<CpsBackupCursorFileList> listOfCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numBatches, extend, CURSOR_ID);
    for (final CpsBackupCursorFileList list : listOfCpsBackupCursorFileList) {
      _cpsBackupCursorFileListsDao.addNewFileList(list);
    }

    final Optional<CpsBackupCursorFileList> firstBatch =
        _cpsBackupCursorFileListsDao.findFirstBatch(SNAPSHOT_ID);
    assertTrue(firstBatch.isPresent());
    assertEquals(1, firstBatch.get().getBatchIndex());

    final Optional<CpsBackupCursorFileList> firstBatch2 =
        _cpsBackupCursorFileListsDao.findFirstBatch(new ObjectId());
    assertFalse(firstBatch2.isPresent());
  }

  @Test
  public void testFindLastBatch() {
    final int numUnextendedBatches = 5;
    final int numExtendedBatches = 5;
    final List<CpsBackupCursorFileList> listOfUnextendedCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numUnextendedBatches, false, CURSOR_ID);
    final List<CpsBackupCursorFileList> listOfExtendedCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numExtendedBatches, true, CURSOR_ID + 1);
    for (int i = 0; i < 4; i++) {
      _cpsBackupCursorFileListsDao.addNewFileList(listOfUnextendedCpsBackupCursorFileList.get(i));
    }
    final Optional<CpsBackupCursorFileList> lastBatch =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, true);
    assertFalse(lastBatch.isPresent());

    final Optional<CpsBackupCursorFileList> lastBatch1 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, false);
    assertTrue(lastBatch1.isPresent());
    assertEquals(4, lastBatch1.get().getBatchIndex());

    final Optional<CpsBackupCursorFileList> lastBatch2 =
        _cpsBackupCursorFileListsDao.findLastBatch(new ObjectId(), false);
    assertFalse(lastBatch2.isPresent());

    for (int i = 0; i < 2; i++) {
      _cpsBackupCursorFileListsDao.addNewFileList(listOfExtendedCpsBackupCursorFileList.get(i));
    }

    final Optional<CpsBackupCursorFileList> lastBatch3 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, true);
    assertTrue(lastBatch3.isPresent());
    assertEquals(2, lastBatch3.get().getBatchIndex());
    assertTrue(lastBatch3.get().isExtended());

    final Optional<CpsBackupCursorFileList> lastBatch4 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, false);
    assertTrue(lastBatch4.isPresent());
    assertEquals(4, lastBatch4.get().getBatchIndex());
    assertFalse(lastBatch4.get().isExtended());

    _cpsBackupCursorFileListsDao.addNewFileList(listOfUnextendedCpsBackupCursorFileList.get(4));

    final Optional<CpsBackupCursorFileList> lastBatch5 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, true);
    assertTrue(lastBatch5.isPresent());
    assertEquals(2, lastBatch5.get().getBatchIndex());
    assertTrue(lastBatch5.get().isExtended());

    final Optional<CpsBackupCursorFileList> lastBatch6 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, false);
    assertTrue(lastBatch6.isPresent());
    assertEquals(5, lastBatch6.get().getBatchIndex());
    assertFalse(lastBatch6.get().isExtended());

    for (int i = 2; i < numExtendedBatches; i++) {
      _cpsBackupCursorFileListsDao.addNewFileList(listOfExtendedCpsBackupCursorFileList.get(i));
    }

    final Optional<CpsBackupCursorFileList> lastBatch7 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, true);
    assertTrue(lastBatch7.isPresent());
    assertEquals(5, lastBatch7.get().getBatchIndex());
    assertTrue(lastBatch7.get().isExtended());

    final Optional<CpsBackupCursorFileList> lastBatch8 =
        _cpsBackupCursorFileListsDao.findLastBatch(SNAPSHOT_ID, false);
    assertTrue(lastBatch8.isPresent());
    assertEquals(5, lastBatch8.get().getBatchIndex());
    assertFalse(lastBatch8.get().isExtended());
  }

  @Test
  public void testUnsetNamespacesAndFileSizes() {
    final int numBatches = 2;
    final boolean extend = false;
    final List<CpsBackupCursorFileList> listOfCpsBackupCursorFileList =
        makeListOfCpsBackupCursorFileList(numBatches, extend, CURSOR_ID);
    for (final CpsBackupCursorFileList list : listOfCpsBackupCursorFileList) {
      _cpsBackupCursorFileListsDao.addNewFileList(list);
    }

    final ModelCursor<CpsBackupCursorFileList> cursor =
        _cpsBackupCursorFileListsDao.getCursorFiles(SNAPSHOT_ID);
    final CpsBackupCursorFileList firstFileList = cursor.next();
    final CpsBackupCursorFileList.CursorFile firstFile = firstFileList.getFiles().get(0);

    assertEquals("db.col", firstFile.namespace());
    assertEquals(1024, firstFile.filesize());
    assertEquals("file1false", firstFile.filename());

    _cpsBackupCursorFileListsDao.removeNamespacesAndFileSizesBySnapshotId(SNAPSHOT_ID);

    final ModelCursor<CpsBackupCursorFileList> cursorAfter =
        _cpsBackupCursorFileListsDao.getCursorFiles(SNAPSHOT_ID);
    final CpsBackupCursorFileList firstFileListAfter = cursorAfter.next();
    final CpsBackupCursorFileList.CursorFile firstFileAfter = firstFileListAfter.getFiles().get(0);

    // confirm removed fields for first entry
    assertNull(firstFileAfter.namespace());
    assertEquals(0, firstFileAfter.filesize());
    assertEquals("file1false", firstFile.filename());
  }

  private List<CpsBackupCursorFileList> makeListOfCpsBackupCursorFileList(
      final int numBatches, final boolean isExtended, final long cursorId) {
    final List<CpsBackupCursorFileList> listOfCpsBackupCursorFileList = new ArrayList<>();
    for (int i = 1; i <= numBatches; i++) {

      final List<CpsBackupCursorFileList.CursorFile> files = new ArrayList<>();
      if (i < numBatches) {
        files.add(new CpsBackupCursorFileList.CursorFile("file" + i + isExtended, "db.col", 1024));
      }
      final CpsBackupCursorFileList.Builder builder =
          new CpsBackupCursorFileList.Builder()
              .withId(new ObjectId())
              .withBackupId(BACKUP_ID)
              .withProjectId(PROJECT_ID)
              .withClusterName(DEFAULT_CLUSTER_NAME)
              .withRsId(DEFAULT_RS_ID)
              .withClusterUniqueId(CLUSTER_UNIQUE_ID)
              .withCursorId(cursorId)
              .withBatchIndex(i)
              .withSnapshotId(SNAPSHOT_ID)
              .withSourceHostname(SOURCE_HOSTNAME)
              .withFiles(files)
              .withIsExtended(isExtended);
      final CpsBackupCursorFileList cpsBackupCursorFileList = new CpsBackupCursorFileList(builder);
      listOfCpsBackupCursorFileList.add(cpsBackupCursorFileList);
    }
    return listOfCpsBackupCursorFileList;
  }
}
