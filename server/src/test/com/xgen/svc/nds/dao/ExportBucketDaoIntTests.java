package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.cps.restore._private.dao.ExportBucketDao;
import com.xgen.cloud.cps.restore._public.model.AWSExportBucket;
import com.xgen.cloud.cps.restore._public.model.AWSExportBucket.FieldDefs;
import com.xgen.cloud.cps.restore._public.model.AzureExportBucket;
import com.xgen.cloud.cps.restore._public.model.ExportBucket;
import com.xgen.cloud.cps.restore._public.model.ExportBucket.BucketType;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.bson.BsonDocument;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class ExportBucketDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private ExportBucketDao _exportBucketDao;

  @Test
  public void testSaveAndFindAWS() throws Exception {

    final AWSExportBucket awsBucket1 =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AWSExportBucket awsBucket2 =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket2")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AWSExportBucket awsBucket3 =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket3")
            .withBucketType(BucketType.DLZ)
            .build();

    _exportBucketDao.insertManyReplicaSafe(List.of(awsBucket1, awsBucket2, awsBucket3));

    assertEquals(3, _exportBucketDao.countAll());

    final ArrayList<BsonDocument> bsonDocuments =
        _exportBucketDao.getCollection().find(BsonDocument.class).into(new ArrayList<>());

    assertEquals(
        Set.of(
            FieldDefs.ID,
            FieldDefs.CLOUD_PROVIDER,
            FieldDefs.BUCKET_NAME,
            FieldDefs.BUCKET_TYPE,
            FieldDefs.PROJECT_ID,
            AWSExportBucket.FieldDefs.IAM_ROLE_ID,
            FieldDefs.REGION,
            FieldDefs.REQUIRE_PRIVATE_NETWORKING),
        bsonDocuments.get(0).keySet());

    final List<ExportBucket> all = _exportBucketDao.findAll();
    assertEquals(3, all.size());

    assertTrue(all.stream().allMatch(b -> b.getCloudProvider() == CloudProvider.AWS));
    assertTrue(all.stream().allMatch(b -> b instanceof AWSExportBucket));

    final List<AWSExportBucket> customerBuckets =
        all.stream()
            .filter(b -> b.getBucketType() == BucketType.CUSTOMER)
            .map(AWSExportBucket.class::cast)
            .toList();
    customerBuckets.forEach(b -> assertNotNull(b.getIamRoleId()));

    final List<AWSExportBucket> dlzBuckets =
        all.stream()
            .filter(b -> b.getBucketType() == BucketType.DLZ)
            .map(AWSExportBucket.class::cast)
            .toList();
    dlzBuckets.forEach(b -> assertNull(b.getIamRoleId()));

    assertContains(all, awsBucket1);
    assertContains(all, awsBucket2);
    assertContains(all, awsBucket3);

    assertTrue(all.stream().allMatch(b -> b.getId() != null));
    assertNotEquals(all.get(0).getId(), all.get(1).getId());

    assertTrue(
        all.stream()
            .allMatch(
                b ->
                    matches(
                        (AWSExportBucket) b,
                        _exportBucketDao.find(b.getId()).map(AWSExportBucket.class::cast).get())));
    assertTrue(_exportBucketDao.find(ObjectId.get()).isEmpty());
  }

  @Test
  public void testSaveAndFindAzure() throws Exception {

    final AzureExportBucket azureBucket1 =
        AzureExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("AzureBucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withRoleId(ObjectId.get())
            .withTenantId("tenantId")
            .withServiceUrl("https://examplestorageaccount.blob.core.windows.net/examplecontainer")
            .build();
    final AzureExportBucket azureBucket2 =
        AzureExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("AzureBucket2")
            .withBucketType(BucketType.CUSTOMER)
            .withRoleId(ObjectId.get())
            .withTenantId("tenantId")
            .withServiceUrl("https://examplestorageaccount.blob.core.windows.net/examplecontainer")
            .build();
    final AzureExportBucket azureBucket3 =
        AzureExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("AzureBucket3")
            .withBucketType(BucketType.DLZ)
            .build();

    _exportBucketDao.insertManyReplicaSafe(List.of(azureBucket1, azureBucket2, azureBucket3));

    assertEquals(3, _exportBucketDao.countAll());

    final ArrayList<BsonDocument> bsonDocuments =
        _exportBucketDao.getCollection().find(BsonDocument.class).into(new ArrayList<>());

    assertEquals(
        Set.of(
            FieldDefs.ID,
            FieldDefs.CLOUD_PROVIDER,
            FieldDefs.BUCKET_NAME,
            FieldDefs.BUCKET_TYPE,
            FieldDefs.PROJECT_ID,
            AzureExportBucket.FieldDefs.ROLE_ID,
            AzureExportBucket.FieldDefs.TENANT_ID,
            AzureExportBucket.FieldDefs.SERVICE_URL),
        bsonDocuments.get(0).keySet());

    final List<ExportBucket> all = _exportBucketDao.findAll();
    assertEquals(3, all.size());

    assertTrue(all.stream().allMatch(b -> b.getCloudProvider() == CloudProvider.AZURE));
    assertTrue(all.stream().allMatch(b -> b instanceof AzureExportBucket));

    final List<AzureExportBucket> customerBuckets =
        all.stream()
            .filter(b -> b.getBucketType() == BucketType.CUSTOMER)
            .map(AzureExportBucket.class::cast)
            .toList();
    customerBuckets.forEach(b -> assertNotNull(b.getRoleId()));
    customerBuckets.forEach(b -> assertNotNull(b.getTenantId()));
    customerBuckets.forEach(b -> assertNotNull(b.getServiceUrl()));

    final List<AzureExportBucket> dlzBuckets =
        all.stream()
            .filter(b -> b.getBucketType() == BucketType.DLZ)
            .map(AzureExportBucket.class::cast)
            .toList();
    dlzBuckets.forEach(b -> assertNull(b.getRoleId()));
    dlzBuckets.forEach(b -> assertNull(b.getTenantId()));
    dlzBuckets.forEach(b -> assertNull(b.getServiceUrl()));

    assertContains(all, azureBucket1);
    assertContains(all, azureBucket2);
    assertContains(all, azureBucket3);

    assertTrue(all.stream().allMatch(b -> b.getId() != null));
    assertNotEquals(all.get(0).getId(), all.get(1).getId());

    assertTrue(
        all.stream()
            .allMatch(
                b ->
                    matches(
                        (AzureExportBucket) b,
                        _exportBucketDao
                            .find(b.getId())
                            .map(AzureExportBucket.class::cast)
                            .get())));
    assertTrue(_exportBucketDao.find(ObjectId.get()).isEmpty());
  }

  @Test
  public void testFindByProjectId() throws Exception {
    final ObjectId projectId1 = ObjectId.get();
    final ObjectId projectId2 = ObjectId.get();

    final AWSExportBucket awsBucket1 =
        AWSExportBucket.builder()
            .withProjectId(projectId1)
            .withBucketName("bucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AWSExportBucket awsBucket2 =
        AWSExportBucket.builder()
            .withProjectId(projectId2)
            .withBucketName("bucket2")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AWSExportBucket awsBucket3 =
        AWSExportBucket.builder()
            .withProjectId(projectId1)
            .withBucketName("bucket3")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AzureExportBucket azureBucket1 =
        AzureExportBucket.builder()
            .withProjectId(projectId1)
            .withBucketName("AzureBucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withRoleId(ObjectId.get())
            .withTenantId("tenantId")
            .withServiceUrl("https://examplestorageaccount.blob.core.windows.net/examplecontainer")
            .build();
    final AzureExportBucket azureBucket2 =
        AzureExportBucket.builder()
            .withProjectId(projectId2)
            .withBucketName("AzureBucket2")
            .withBucketType(BucketType.CUSTOMER)
            .withRoleId(ObjectId.get())
            .withTenantId("tenantId")
            .withServiceUrl("https://examplestorageaccount.blob.core.windows.net/examplecontainer")
            .build();

    _exportBucketDao.insertManyReplicaSafe(
        List.of(awsBucket1, awsBucket2, awsBucket3, azureBucket1, azureBucket2));

    final List<ExportBucket> bucketsForProj1 = _exportBucketDao.findByProjectId(projectId1);
    final List<ExportBucket> bucketsForProj2 = _exportBucketDao.findByProjectId(projectId2);
    final List<ExportBucket> bucketsForNonExistentProj =
        _exportBucketDao.findByProjectId(ObjectId.get());

    assertEquals(3, bucketsForProj1.size());
    assertEquals(2, bucketsForProj2.size());
    assertTrue(bucketsForNonExistentProj.isEmpty());
  }

  @Test
  public void testDelete() throws Exception {
    final AWSExportBucket awsBucket1 =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AWSExportBucket awsBucket2 =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket2")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();
    final AWSExportBucket awsBucket3 =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket2")
            .withBucketType(BucketType.DLZ)
            .build();
    final AzureExportBucket azureBucket1 =
        AzureExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("AzureBucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withRoleId(ObjectId.get())
            .withTenantId("tenantId")
            .withServiceUrl("https://examplestorageaccount.blob.core.windows.net/examplecontaine")
            .build();
    final AzureExportBucket azureBucket2 =
        AzureExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("AzureBucket2")
            .withBucketType(BucketType.DLZ)
            .build();

    _exportBucketDao.insertManyReplicaSafe(
        List.of(awsBucket1, awsBucket2, awsBucket3, azureBucket1, azureBucket2));
    assertEquals(5, _exportBucketDao.countAll());

    final List<ExportBucket> all = _exportBucketDao.findAll();

    _exportBucketDao.deleteById(all.get(0).getId());
    assertEquals(4, _exportBucketDao.countAll());

    // already deleted, no-op
    _exportBucketDao.deleteById(all.get(0).getId());
    assertEquals(4, _exportBucketDao.countAll());

    _exportBucketDao.deleteById(all.get(1).getId());
    assertEquals(3, _exportBucketDao.countAll());

    _exportBucketDao.deleteById(all.get(3).getId());
    assertEquals(2, _exportBucketDao.countAll());

    _exportBucketDao.deleteById(all.get(4).getId());
    assertEquals(1, _exportBucketDao.countAll());
  }

  @Test
  public void testUpdateRegion() throws Exception {
    // Create an AWS bucket without a region
    final AWSExportBucket originalBucket =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket-for-region-update")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();

    _exportBucketDao.create(originalBucket);

    assertNull(originalBucket.getRegion(), "Region should initially be null");

    // Create an updated bucket with a region
    final String expectedRegion = "us-east-1";

    _exportBucketDao.updateRegion(originalBucket.getId(), expectedRegion);

    final AWSExportBucket updatedFoundBucket =
        _exportBucketDao
            .find(originalBucket.getId())
            .map(AWSExportBucket.class::cast)
            .orElseThrow(() -> new AssertionError("Bucket should still exist"));

    assertNotNull(updatedFoundBucket.getRegion(), "Region should not be null after update");
    assertEquals(
        expectedRegion, updatedFoundBucket.getRegion(), "Region should match expected value");
  }

  private void assertContains(final List<ExportBucket> all, final AWSExportBucket bucket) {
    assertTrue(
        all.stream()
            .filter(b -> b instanceof AWSExportBucket)
            .map(AWSExportBucket.class::cast)
            .anyMatch(b -> matches(bucket, b)));
  }

  private void assertContains(final List<ExportBucket> all, final AzureExportBucket bucket) {
    assertTrue(
        all.stream()
            .filter(b -> b instanceof AzureExportBucket)
            .map(AzureExportBucket.class::cast)
            .anyMatch(b -> matches(bucket, b)));
  }

  private boolean matches(final AWSExportBucket bucket, final AWSExportBucket b) {
    return b.getProjectId().equals(bucket.getProjectId())
        && Objects.equals(b.getIamRoleId(), bucket.getIamRoleId())
        && b.getBucketName().equals(bucket.getBucketName());
  }

  private boolean matches(final AzureExportBucket bucket, final AzureExportBucket b) {
    return b.getProjectId().equals(bucket.getProjectId())
        && Objects.equals(b.getRoleId(), bucket.getRoleId())
        && Objects.equals(b.getTenantId(), bucket.getTenantId())
        && Objects.equals(b.getServiceUrl(), bucket.getServiceUrl())
        && b.getBucketName().equals(bucket.getBucketName());
  }

  @Test
  public void testUpdateRequirePrivateNetworking() throws Exception {
    final AWSExportBucket originalBucket =
        AWSExportBucket.builder()
            .withProjectId(ObjectId.get())
            .withBucketName("bucket-for-private-networking-update")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(ObjectId.get())
            .build();

    _exportBucketDao.create(originalBucket);

    assertNotNull(
        originalBucket.getRequirePrivateNetworking(),
        "RequirePrivateNetworking should not be null");
    assertFalse(
        originalBucket.getRequirePrivateNetworking(),
        "RequirePrivateNetworking should initially be false");

    _exportBucketDao.updateRequirePrivateNetworking(originalBucket.getId(), true);

    final AWSExportBucket updatedBucket =
        _exportBucketDao
            .find(originalBucket.getId())
            .map(AWSExportBucket.class::cast)
            .orElseThrow(() -> new AssertionError("Bucket should still exist"));

    assertTrue(
        updatedBucket.getRequirePrivateNetworking(), "RequirePrivateNetworking should be true");

    _exportBucketDao.updateRequirePrivateNetworking(originalBucket.getId(), false);

    final AWSExportBucket updatedBucketFalse =
        _exportBucketDao
            .find(originalBucket.getId())
            .map(AWSExportBucket.class::cast)
            .orElseThrow(() -> new AssertionError("Bucket should still exist"));

    assertFalse(
        updatedBucketFalse.getRequirePrivateNetworking(),
        "RequirePrivateNetworking should be false");
  }
}
