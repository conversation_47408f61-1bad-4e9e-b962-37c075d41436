package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.cps.restore._private.dao.BackupSearchIndexDefinitionDao;
import com.xgen.cloud.cps.restore._public.model.BackupSearchIndexDefinition;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.search.external._public.model.BackupFTSIndexConfig;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class BackupSearchIndexDefinitionDaoIntTests extends JUnit5BaseSvcTest {
  @Inject BackupSearchIndexDefinitionDao _backupSearchIndexDefinitionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  private static final ObjectId ID = new ObjectId();
  private static final ObjectId PROJECT_ID = new ObjectId();
  private static final ObjectId SNAPSHOT_ID = new ObjectId();
  private static final ObjectId CLUSTER_UNIQUE_ID = new ObjectId();
  private static final String CLUSTER_NAME = "testCluster";

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _ndsGroupSvc.create(PROJECT_ID, new NDSManagedX509(), false);
    createCluster(PROJECT_ID, CLUSTER_NAME);
  }

  @Test
  public void testCreateAndDelete() {
    assertTrue(_backupSearchIndexDefinitionDao.findById(ID).isEmpty());

    final BackupSearchIndexDefinition backupSearchIndexDefinition =
        new BackupSearchIndexDefinition(
            ID,
            SNAPSHOT_ID,
            CLUSTER_UNIQUE_ID,
            new BackupFTSIndexConfig(PROJECT_ID, CLUSTER_NAME, null, null, null));
    _backupSearchIndexDefinitionDao.addNewIndexDefinition(backupSearchIndexDefinition);

    final Optional<BackupSearchIndexDefinition> result =
        _backupSearchIndexDefinitionDao.findById(ID);
    assertTrue(result.isPresent());
    assertEquals(SNAPSHOT_ID, result.get().getSnapshotId());
    assertEquals(CLUSTER_UNIQUE_ID, result.get().getClusterUniqueId());
    assertEquals(PROJECT_ID, result.get().getFtsIndexConfig().getGroupId());
    assertEquals(CLUSTER_NAME, result.get().getFtsIndexConfig().getClusterName());

    _backupSearchIndexDefinitionDao.removeById(ID);
    assertTrue(_backupSearchIndexDefinitionDao.findById(ID).isEmpty());
  }

  private void createCluster(final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription(pGroupId, pClusterName)
            .append("dnsPin", pGroupId.toHexString());
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
  }
}
