package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.CollectionMetadataConfig;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.AzureBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AzureBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.RequestingUser;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.RequestingUser.FieldDefs;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.ServerlessTenant;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorMetadata;
import com.xgen.cloud.cps.restore._public.model.GCPBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.SLSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ShardedClusterBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.AwsSnapshotFieldBuilder;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.AzureSnapshotFieldBuilder;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.GcpSnapshotFieldBuilder;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.SLSSnapshotFieldBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.DisaggregatedStorageConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class BackupSnapshotDaoIntTests extends JUnit5BaseSvcTest {

  private static final long USED_SPACE = 1 << 30; // 1GB

  private static final Date SCHEDULED_CREATION_DATE =
      new Date(System.currentTimeMillis() - Duration.ofMinutes(1).toMillis());
  private static final Date SNAPSHOT_INITIATION_DATE =
      new Date(System.currentTimeMillis() - Duration.ofMinutes(1).toMillis());
  private static final Date SCHEDULED_DELETION_DATE =
      new Date(System.currentTimeMillis() + Duration.ofDays(30).toMillis());
  private static final Date PROTECTION_END_DATE =
      new Date(System.currentTimeMillis() + Duration.ofDays(15).toMillis());
  private static final Date CURRENT_TIME = new Date(System.currentTimeMillis());
  private static final Date PAST_TIME =
      new Date(System.currentTimeMillis() - Duration.ofDays(2).toMillis());

  private static final ObjectId PROJECT_ID = new ObjectId();
  private static final ObjectId CLUSTER_UNIQUE_ID = new ObjectId();
  private static final ObjectId CLUSTER_UNIQUE_ID_2 = new ObjectId();
  private static final ObjectId SUBSCRIPTION_ID = new ObjectId();
  private static final ObjectId SLS_BACKUP_CLUSTERSHOT_ID = new ObjectId();
  private static final ObjectId SLS_BACKUP_SNAPSHOT_ID = new ObjectId();
  private static final String SLS_BACKUP_CELL_NAME = new ObjectId().toString();
  private static final Long SLS_BACKUP_LOG_ID = 15L;

  private static final String CLUSTER_NAME = "clusterName";
  private static final String SNAPSHOT_NAME = "snapshotName";
  private static final String RESOURCE_GROUP = "resourceGroup";
  private static final String RS_ID = "clusterName-shard-01";
  private static final String AWS_CMK_ID = "123-123";
  private static final String AZURE_CMK_ID = "999-999";
  private static final AWSBackupSnapshotEncryptionCredentials AWS_ENCRYPT_CREDENTIALS =
      new AWSBackupSnapshotEncryptionCredentials(
          "accesskeyid", "secret", ObjectId.get(), AWS_CMK_ID, "US_EAST_1", false);
  private static final AzureBackupSnapshotEncryptionCredentials AZURE_ENCRYPT_CREDENTIALS =
      new AzureBackupSnapshotEncryptionCredentials(
          "clientId",
          "tenantId",
          "secret",
          "AZURE_CHINA",
          "subscriptId-1",
          "resourceName",
          "keyvault1",
          AZURE_CMK_ID);

  @Inject private BackupSnapshotDao _backupSnapshotDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
  }

  @Test
  public void testUpdateSnapshotWithWtcMetadata() {
    final ObjectId snapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    updateSnapshotWithWtcMetadata(snapshotId);
  }

  @Test
  public void testUpdateWtcStatus() {
    final ObjectId snapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    updateSnapshotWithWtcMetadata(snapshotId);
    _backupSnapshotDao.updateWtcStatus(
        snapshotId, ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.LAST_BATCH_RECEIVED);
    final ReplicaSetBackupSnapshot snap =
        (ReplicaSetBackupSnapshot) _backupSnapshotDao.findById(snapshotId).get();
    assertEquals(
        ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.LAST_BATCH_RECEIVED,
        snap.getCpsWtCheckpoint().getState());
  }

  @Test
  public void testAddAzureBackupSnapshot() {
    final ObjectId snapshotId =
        addAzureSnapshot(
            new AzureSnapshotFieldBuilder()
                .withSnapshotName(SNAPSHOT_NAME)
                .withRegionName(AzureRegionName.US_EAST.name()));
    final Optional<BackupSnapshot> savedBackupSnapshot = _backupSnapshotDao.findById(snapshotId);
    assertTrue(savedBackupSnapshot.isPresent());
    assertTrue(savedBackupSnapshot.get() instanceof AzureBackupSnapshot);
    final AzureBackupSnapshot azureBackupSnapshot =
        savedBackupSnapshot.map(AzureBackupSnapshot.class::cast).get();
    assertEquals(SNAPSHOT_NAME, azureBackupSnapshot.getName());
    assertEquals(SUBSCRIPTION_ID, azureBackupSnapshot.getSubscriptionId());
    assertEquals(RESOURCE_GROUP, azureBackupSnapshot.getResourceGroup());
    assertEquals(CLUSTER_NAME, azureBackupSnapshot.getClusterName());
    assertEquals(CLUSTER_NAME, azureBackupSnapshot.getDeploymentClusterName());
    assertEquals(AzureRegionName.US_EAST, azureBackupSnapshot.getRegion());
    assertEquals(AzureDiskType.P10, azureBackupSnapshot.getDiskType());
    assertEquals(USED_SPACE, azureBackupSnapshot.getUsedDiskSpace());
    assertFalse(azureBackupSnapshot.getDeleted());
    assertEquals(BackupSnapshot.Status.COMPLETED, azureBackupSnapshot.getSnapshotStatus());

    final String snapshotName2 = "snapshot2";
    final ObjectId snapshotId2 =
        addAzureSnapshot(
            new AzureSnapshotFieldBuilder()
                .withSnapshotName(snapshotName2)
                .withRegionName(AzureRegionName.US_EAST.name())
                .withDiskType(AzureDiskType.V2.name())
                .withDiskSizeGB(1234L));

    final Set<ObjectId> idSet = Stream.of(snapshotId, snapshotId2).collect(Collectors.toSet());
    final Set<String> nameSet = Stream.of(SNAPSHOT_NAME, snapshotName2).collect(Collectors.toSet());
    boolean deleted = false;
    final List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted);
    assertEquals(2, snapshots.size());
    for (final BackupSnapshot snapshot : snapshots) {
      final AzureBackupSnapshot abs = (AzureBackupSnapshot) snapshot;
      assertTrue(nameSet.contains(abs.getName()));
      nameSet.remove(abs.getName());
      assertTrue(idSet.contains(abs.getId()));
      idSet.remove(abs.getId());
      assertEquals(RESOURCE_GROUP, abs.getResourceGroup());
      assertEquals(CLUSTER_NAME, abs.getClusterName());
      assertEquals(CLUSTER_NAME, abs.getDeploymentClusterName());
      assertFalse(abs.getDeleted());

      if (((AzureBackupSnapshot) snapshot).getDiskType() == AzureDiskType.V2) {
        assertEquals(1234L, ((AzureBackupSnapshot) snapshot).getSnapshotDiskSize());
      } else {
        assertEquals(
            ((AzureBackupSnapshot) snapshot).getDiskType().getSizeGB(),
            ((AzureBackupSnapshot) snapshot).getSnapshotDiskSize());
      }
    }
    deleted = true;
    final List<BackupSnapshot> deletedSnapshots =
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted);
    assertTrue(deletedSnapshots.isEmpty());
  }

  @Test
  public void testAzureBillingQueryWithHint() {
    addAzureSnapshot(
        new AzureSnapshotFieldBuilder()
            .withSnapshotName(SNAPSHOT_NAME)
            .withRegionName(AzureRegionName.US_EAST.name()));

    final List<BackupSnapshot> billableSnapshots =
        _backupSnapshotDao.findSnapshotsUsingStorage(
            PROJECT_ID,
            CloudProvider.AZURE,
            Date.from(Instant.now().minus(Duration.ofHours(1))),
            Date.from(Instant.now().plus(Duration.ofHours(1))));
    assertEquals(
        1, billableSnapshots.size(), "Unexpected snapshot count, was: " + billableSnapshots);
  }

  @Test
  public void testAddGcpBackupSnapshot() {
    final String gcpRegionName = GCPRegionName.US_WEST_2.getName();
    final ObjectId snapshotId = addGcpSnapshot(gcpRegionName);
    final Optional<BackupSnapshot> savedBackupSnapshot = _backupSnapshotDao.findById(snapshotId);
    assertTrue(savedBackupSnapshot.isPresent());
    assertTrue(savedBackupSnapshot.get() instanceof GCPBackupSnapshot);
  }

  @Test
  public void testAddAwsBackupSnapshot() {
    final String awsRegionName = AWSRegionName.US_EAST_1.getName();
    final ObjectId awsAccountId = new ObjectId();
    final ObjectId awsContainerId = new ObjectId();
    final String awsSubnetId = "awsSubnetId-1";
    final String ebsVolumeId = "ebsVolumeId-1";
    final String ebsSnapshotId = "ebsSnapshotId-1";
    final String ebsSnapshotDesc = AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId);
    final String ebsVolumeType = VolumeType.Io1.name();
    final Boolean ebsVolumeEncrypted = true;
    final Integer ebsVolumeSize = AWSNDSInstanceSize.M30.getDefaultDiskSizeGB();
    final Integer ebsDiskIOPS = AWSNDSInstanceSize.M30.getMaxEBSStandardIOPS();

    final ObjectId snapshotId =
        _backupSnapshotDao.addBackupSnapshot(
            new SnapshotUpdate()
                .setId(new ObjectId())
                .setProjectId(PROJECT_ID)
                .setClusterName(CLUSTER_NAME)
                .setDeploymentClusterName(CLUSTER_NAME)
                .setRsId(RS_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
                .setSnapshotInitiationDate(SNAPSHOT_INITIATION_DATE)
                .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
                .setProtectionEndDate(PROTECTION_END_DATE)
                .setDeleted(false)
                .setPurged(false)
                .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
                .setUsedDiskSpace(USED_SPACE)
                .setCloudProviders(List.of(CloudProvider.AWS))
                .setAwsSnapshotField(
                    new AwsSnapshotFieldBuilder()
                        .withEbsSnapshotId(ebsSnapshotId)
                        .withEbsSnapshotDescription(ebsSnapshotDesc)
                        .withAWSAccountId(awsAccountId)
                        .withAWSContainerId(awsContainerId)
                        .withAWSSubnetId(awsSubnetId)
                        .withEbsVolumeId(ebsVolumeId)
                        .withEbsVolumeType(ebsVolumeType)
                        .withIsEbsVolumeEncrypted(ebsVolumeEncrypted)
                        .withEbsVolumeSize(ebsVolumeSize)
                        .withEbsDiskIOPS(ebsDiskIOPS)
                        .withRegionName(awsRegionName))
                .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setFrequencyType(BackupFrequencyType.DAILY)
                .setOverrideRetentionPolicy(false)
                .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
                .setSnapshotCompletionDate(new Date()));

    final Optional<BackupSnapshot> savedBackupSnapshot = _backupSnapshotDao.findById(snapshotId);
    assertTrue(savedBackupSnapshot.isPresent());
    assertTrue(savedBackupSnapshot.get() instanceof AWSBackupSnapshot);
    final AWSBackupSnapshot awsBackupSnapshot =
        savedBackupSnapshot.map(AWSBackupSnapshot.class::cast).get();

    assertEquals(ebsSnapshotId, awsBackupSnapshot.getEbsSnapshotId());
    assertEquals(awsAccountId, awsBackupSnapshot.getAwsAccountId());
    assertEquals(awsContainerId, awsBackupSnapshot.getAwsContainerId());
    assertEquals(AWSRegionName.valueOf(awsRegionName), awsBackupSnapshot.getRegion());
    assertEquals(awsSubnetId, awsBackupSnapshot.getAwsSubnetId());
    assertEquals(CLUSTER_NAME, awsBackupSnapshot.getClusterName());
    assertEquals(CLUSTER_NAME, awsBackupSnapshot.getDeploymentClusterName());
    assertEquals(CLUSTER_UNIQUE_ID, awsBackupSnapshot.getClusterUniqueId());
    assertEquals(ebsSnapshotDesc, awsBackupSnapshot.getEbsSnapshotDescription());
    assertEquals(ebsVolumeId, awsBackupSnapshot.getEbsVolumeId());
    assertEquals(ebsVolumeType, awsBackupSnapshot.getEbsVolumeType());
    assertEquals(ebsVolumeEncrypted, awsBackupSnapshot.getEbsVolumeEncrypted());
    assertEquals(ebsVolumeSize, awsBackupSnapshot.getEbsVolumeSize());
    assertEquals(ebsDiskIOPS, awsBackupSnapshot.getEbsDiskIOPS());
    assertEquals(PROTECTION_END_DATE, awsBackupSnapshot.getProtectionEndDate());
    assertEquals(USED_SPACE, awsBackupSnapshot.getUsedDiskSpace());
    assertFalse(awsBackupSnapshot.getDeleted());
    assertEquals(
        ClusterDescription.EncryptionAtRestProvider.AWS,
        awsBackupSnapshot.getSnapshotEncryptionDetails().getEncryptionAtRestProvider());
    assertEquals(BackupSnapshot.Status.COMPLETED, awsBackupSnapshot.getSnapshotStatus());
    final BackupSnapshotEncryptionCredentials encCreds =
        awsBackupSnapshot.getSnapshotEncryptionDetails().getEncryptionCredentials().get();
    assertEquals(AWS_CMK_ID, encCreds.getKeyId());
    assertEquals(AWS_ENCRYPT_CREDENTIALS, encCreds);

    final String anotherEbsSnapshotId = "ebsSnapshotId-2";

    final ObjectId snapshotId2 =
        _backupSnapshotDao.addBackupSnapshot(
            new SnapshotUpdate()
                .setId(new ObjectId())
                .setProjectId(PROJECT_ID)
                .setClusterName(CLUSTER_NAME)
                .setDeploymentClusterName(CLUSTER_NAME)
                .setRsId(RS_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
                .setSnapshotInitiationDate(SNAPSHOT_INITIATION_DATE)
                .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
                .setDeleted(false)
                .setPurged(false)
                .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
                .setUsedDiskSpace(USED_SPACE)
                .setCloudProviders(List.of(CloudProvider.AWS))
                .setAwsSnapshotField(
                    new AwsSnapshotFieldBuilder()
                        .withEbsSnapshotId(anotherEbsSnapshotId)
                        .withEbsSnapshotDescription(ebsSnapshotDesc)
                        .withAWSAccountId(awsAccountId)
                        .withAWSContainerId(awsContainerId)
                        .withAWSSubnetId(awsSubnetId)
                        .withEbsVolumeId(ebsVolumeId)
                        .withEbsVolumeType(ebsVolumeType)
                        .withIsEbsVolumeEncrypted(ebsVolumeEncrypted)
                        .withEbsVolumeSize(ebsVolumeSize)
                        .withEbsDiskIOPS(ebsDiskIOPS)
                        .withRegionName(awsRegionName))
                .setEncryptionDetails(AZURE_ENCRYPT_CREDENTIALS)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setFrequencyType(BackupFrequencyType.DAILY)
                .setOverrideRetentionPolicy(false)
                .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
                .setSnapshotCompletionDate(new Date()));

    final Optional<BackupSnapshot> savedBackupSnapshot2 = _backupSnapshotDao.findById(snapshotId2);
    assertTrue(savedBackupSnapshot2.isPresent());
    assertTrue(savedBackupSnapshot2.get() instanceof AWSBackupSnapshot);
    final AWSBackupSnapshot awsBackupSnapshot2 =
        savedBackupSnapshot2.map(AWSBackupSnapshot.class::cast).get();
    assertEquals(
        ClusterDescription.EncryptionAtRestProvider.AZURE,
        awsBackupSnapshot2.getSnapshotEncryptionDetails().getEncryptionAtRestProvider());
    final BackupSnapshotEncryptionCredentials encCreds2 =
        awsBackupSnapshot2.getSnapshotEncryptionDetails().getEncryptionCredentials().get();
    assertEquals(AZURE_CMK_ID, encCreds2.getKeyId());

    final Set<ObjectId> idSet = Stream.of(snapshotId, snapshotId2).collect(Collectors.toSet());
    final Set<String> ebsSnapshotIds =
        Stream.of(ebsSnapshotId, anotherEbsSnapshotId).collect(Collectors.toSet());
    boolean deleted = false;
    final List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted);
    assertEquals(2, snapshots.size());
    for (final BackupSnapshot snapshot : snapshots) {
      final AWSBackupSnapshot abs = (AWSBackupSnapshot) snapshot;
      assertTrue(ebsSnapshotIds.contains(abs.getEbsSnapshotId()));
      ebsSnapshotIds.remove(abs.getEbsSnapshotId());
      assertTrue(idSet.contains(abs.getId()));
      idSet.remove(abs.getId());
      assertEquals(awsContainerId, abs.getAwsContainerId());
      assertEquals(CLUSTER_NAME, abs.getClusterName());
      assertEquals(CLUSTER_NAME, abs.getDeploymentClusterName());
      assertFalse(abs.getDeleted());
    }
    deleted = true;
    final List<BackupSnapshot> deletedSnapshots =
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted);
    assertTrue(deletedSnapshots.isEmpty());
  }

  @Test
  public void testMarkProtectedForActiveSnapshots() {
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPurged(false)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet-failed")
                .setShard(false)
                .setStatus(Status.FAILED)
                .setPurged(false)
                .setDeleted(true)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId shard0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPurged(false)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId shard1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPurged(false)
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId config0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-config-0")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.ON_DEMAND)
                .setPurged(false)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId shardedClusterSnapshotId = new ObjectId();
    final Map<ObjectId, String> members = new HashMap<>();
    members.put(shard0, CLUSTER_NAME + "-shard-0");
    members.put(shard1, CLUSTER_NAME + "-shard-1");
    members.put(config0, CLUSTER_NAME + "-config-0");
    addShardedClusterSnapshot(
        shardedClusterSnapshotId, members, BackupSnapshot.Status.IN_PROGRESS, Type.SCHEDULED);

    // All snapshots are not Protected without protected filed set
    BackupSnapshot snapshot1 = _backupSnapshotDao.findById(snap1).get();
    BackupSnapshot snapshot2 = _backupSnapshotDao.findById(config0).get();
    BackupSnapshot snapshot3 = _backupSnapshotDao.findById(shard0).get();
    BackupSnapshot snapshot4 = _backupSnapshotDao.findById(shardedClusterSnapshotId).get();
    BackupSnapshot snapshot5 = _backupSnapshotDao.findById(snap2).get();
    BackupSnapshot snapshot6 = _backupSnapshotDao.findById(shard1).get();
    assertFalse(snapshot1.isProtected());
    assertFalse(snapshot2.isProtected());
    assertFalse(snapshot3.isProtected());
    assertFalse(snapshot4.isProtected());
    assertFalse(snapshot5.isProtected());
    assertFalse(snapshot6.isProtected());

    _backupSnapshotDao.updateProtectedForActiveSnapshots(PROJECT_ID, true);
    // Active snapshots become protected
    snapshot1 = _backupSnapshotDao.findById(snap1).get();
    snapshot2 = _backupSnapshotDao.findById(config0).get();
    snapshot3 = _backupSnapshotDao.findById(shard0).get();
    snapshot4 = _backupSnapshotDao.findById(shardedClusterSnapshotId).get();

    // Deleted/Purged snapshots remain unProtected
    snapshot5 = _backupSnapshotDao.findById(snap2).get();
    snapshot6 = _backupSnapshotDao.findById(shard1).get();

    assertTrue(snapshot1.isProtected());
    assertTrue(snapshot2.isProtected());
    assertTrue(snapshot3.isProtected());
    assertTrue(snapshot4.isProtected());
    assertFalse(snapshot5.isProtected());
    assertFalse(snapshot6.isProtected());

    _backupSnapshotDao.updateProtectedForActiveSnapshots(PROJECT_ID, false);
    snapshot1 = _backupSnapshotDao.findById(snap1).get();
    snapshot2 = _backupSnapshotDao.findById(config0).get();
    snapshot3 = _backupSnapshotDao.findById(shard0).get();
    snapshot4 = _backupSnapshotDao.findById(shardedClusterSnapshotId).get();
    snapshot5 = _backupSnapshotDao.findById(snap2).get();
    snapshot6 = _backupSnapshotDao.findById(shard1).get();
    assertFalse(snapshot1.isProtected());
    assertFalse(snapshot2.isProtected());
    assertFalse(snapshot3.isProtected());
    assertFalse(snapshot4.isProtected());
    assertFalse(snapshot5.isProtected());
    assertFalse(snapshot6.isProtected());

    // Test SnapshotUpdate
    final SnapshotUpdate snapshotUpdate = new SnapshotUpdate().setProtected(true);
    _backupSnapshotDao.updateBackupSnapshot(snap1, snapshotUpdate);
    snapshot1 = _backupSnapshotDao.findById(snap1).get();
    assertTrue(snapshot1.isProtected());
  }

  @Test
  public void testDisableProtectedAndProtectionEndDateForActiveSnapshots() {
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setProtected(true)
                .setProtectionEndDate(PROTECTION_END_DATE)
                .setPurged(false)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet-deleted")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setPurged(false)
                .setDeleted(true)
                .setProtected(true)
                .setProtectionEndDate(PROTECTION_END_DATE)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName2"));

    final ObjectId snap3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet-purged")
                .setShard(false)
                .setStatus(Status.FAILED)
                .setType(Type.ON_DEMAND)
                .setPurged(true)
                .setDeleted(false)
                .setProtected(true)
                .setProtectionEndDate(PROTECTION_END_DATE),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName3"));

    final ObjectId snap4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet-not-protected")
                .setType(Type.ON_DEMAND)
                .setShard(false)
                .setStatus(Status.FAILED)
                .setPurged(false)
                .setDeleted(false)
                .setProtected(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName4"));

    _backupSnapshotDao.disableProtectedAndProtectionEndDateForActiveSnapshots(PROJECT_ID);

    BackupSnapshot snapshot1 = _backupSnapshotDao.findById(snap1).get();
    BackupSnapshot snapshot2 = _backupSnapshotDao.findById(snap2).get();
    BackupSnapshot snapshot3 = _backupSnapshotDao.findById(snap3).get();
    BackupSnapshot snapshot4 = _backupSnapshotDao.findById(snap4).get();

    assertFalse(snapshot1.isProtected());
    assertTrue(snapshot2.isProtected());
    assertTrue(snapshot3.isProtected());
    assertFalse(snapshot4.isProtected());

    assertNull(snapshot1.getProtectionEndDate());
    assertEquals(PROTECTION_END_DATE, snapshot2.getProtectionEndDate());
    assertEquals(PROTECTION_END_DATE, snapshot3.getProtectionEndDate());
    assertNull(snapshot4.getProtectionEndDate());
  }

  @Test
  public void testFindActiveSnapshotsContainsScheduledDeletionDate() {
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(CLUSTER_NAME + "replSet")
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setPurged(false)
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // deleted
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(CLUSTER_NAME + "replSet-deleted")
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setPurged(false)
            .setDeleted(true)
            .setType(Type.COPY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName2"));

    // purged
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(CLUSTER_NAME + "replSet-purged")
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.ON_DEMAND)
            .setPurged(true)
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName3"));

    // no scheduled deletion date
    final ObjectId snapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet-not-protected")
                .setType(Type.ON_DEMAND)
                .setScheduledDeletionDate(null)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setPurged(false)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName4"));
    _backupSnapshotDao.extendSnapshotsExpirationDate(List.of(snapshotId), null);

    assertEquals(
        1, _backupSnapshotDao.findActiveSnapshotsContainsScheduledDeletionDate(PROJECT_ID).size());
  }

  @Test
  public void testSetProtectionEndDate() {
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setProtected(true)
                .setPurged(false)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    BackupSnapshot snapshot1 = _backupSnapshotDao.findById(snap1).get();
    assertNull(snapshot1.getProtectionEndDate());
    _backupSnapshotDao.setProtectionEndDate(snap1, PROTECTION_END_DATE);
    snapshot1 = _backupSnapshotDao.findById(snap1).get();
    assertEquals(PROTECTION_END_DATE, snapshot1.getProtectionEndDate());
  }

  @Test
  public void testPurgeAzureSnapshots() {
    addAzureSnapshot(
        new AzureSnapshotFieldBuilder()
            .withSnapshotName(SNAPSHOT_NAME)
            .withRegionName(AzureRegionName.US_EAST.name()));

    final Date lastMillisLive = SCHEDULED_DELETION_DATE;
    assertEquals(
        0,
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AZURE, lastMillisLive).size(),
        "The snapshot should not be expired yet with the given date " + lastMillisLive);

    final Date firstMillisDead = new Date(lastMillisLive.getTime() + 1L);
    assertEquals(
        1,
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AZURE, firstMillisDead).size(),
        "The snapshot should be expired by the given date " + firstMillisDead);

    final boolean deleted = false;
    assertEquals(
        1,
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted).size());

    _backupSnapshotDao.markDeletedForCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertEquals(
        0,
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted).size());

    final List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AZURE, new Date());
    assertEquals(1, snapshots.size(), "Should have 1 deleted=true snapshot to purge.");
    final BackupSnapshot backupSnapshot = snapshots.get(0);
    assertTrue(backupSnapshot.getDeleted());
    assertFalse(backupSnapshot.getPurged());
    assertNull(backupSnapshot.getPurgedDate());

    // purge
    final Date purgedDate = new Date();
    _backupSnapshotDao.markPurged(backupSnapshot.getId(), purgedDate);
    assertEquals(
        0,
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted).size());

    final BackupSnapshot purgedSnapshot = _backupSnapshotDao.findById(backupSnapshot.getId()).get();
    assertTrue(purgedSnapshot.getPurged());
    assertEquals(purgedDate, purgedSnapshot.getPurgedDate());
  }

  @Test
  public void testPurgeAWSSnapshots() {

    // non-completed snapshots should not be purged
    final ObjectId inProgressSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0")
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final ObjectId completedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    updateSnapshotWithStatus(completedSnapshotId, Status.COMPLETED);

    final Date lastMillisLive = SCHEDULED_DELETION_DATE;
    assertEquals(
        0,
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, lastMillisLive).size(),
        "The completed snapshot should not be expired yet with the given date " + lastMillisLive);

    final Date firstMillisDead = new Date(lastMillisLive.getTime() + 1L);

    assertEquals(
        1,
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, firstMillisDead).size(),
        "The completed snapshot should be expired by the given date " + firstMillisDead);

    final boolean deleted = false;
    assertEquals(
        1,
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted).size());

    _backupSnapshotDao.markDeletedForCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertEquals(
        0,
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted).size());

    final List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, new Date());
    assertEquals(1, snapshots.size(), "Should have 1 deleted=true snapshot to purge.");
    final BackupSnapshot backupSnapshot = snapshots.get(0);
    assertTrue(backupSnapshot.getDeleted());
    assertFalse(backupSnapshot.getPurged());
    assertNull(backupSnapshot.getPurgedDate());

    // purge
    final Date purgedDate = new Date();
    _backupSnapshotDao.markPurged(backupSnapshot.getId(), purgedDate);
    assertEquals(
        0,
        _backupSnapshotDao.findCompletedByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, deleted).size());

    final BackupSnapshot purgedSnapshot = _backupSnapshotDao.findById(backupSnapshot.getId()).get();
    assertTrue(purgedSnapshot.getPurged());
    assertEquals(purgedDate, purgedSnapshot.getPurgedDate());
  }

  @Test
  public void testFindSnapshotsToPurgeWithMultipleSimpleQueries() {
    // Adding snapshots with permuted deleted/type/shard/scheduledDeletionDate
    //    snap1 - deleted: true, copy: true, nonShard: true, expired: true
    //    snap2 - deleted: true, copy: true, nonShard: true, expired: false
    //    snap3 - deleted: true, copy: true, nonShard: false, expired: true
    //    snap4 - deleted: true, copy: true, nonShard: false, expired: false
    //    snap5 - deleted: true, copy: false, nonShard: true, expired: true
    //    snap6 - deleted: true, copy: false, nonShard: true, expired: false
    //    snap7 - deleted: true, copy: false, nonShard: false, expired: true
    //    snap8 - deleted: true, copy: false, nonShard: false, expired: false
    //    snap9 - deleted: false, copy: true, nonShard: true, expired: true
    //    snap10 - deleted: false, copy: true, nonShard: true, expired: false
    //    snap11 - deleted: false, copy: true, nonShard: false, expired: true
    //    snap12 - deleted: false, copy: true, nonShard: false, expired: false
    //    snap13 - deleted: false, copy: false, nonShard: true, expired: true
    //    snap14 - deleted: false, copy: false, nonShard: true, expired: false
    //    snap15 - deleted: false, copy: false, nonShard: false, expired: true
    //    snap16 - deleted: false, copy: false, nonShard: false, expired: false
    final ObjectId snap1 = addSnapshotsToPurge(1, true, true, true, true);
    final ObjectId snap2 = addSnapshotsToPurge(2, true, true, true, false);
    final ObjectId snap3 = addSnapshotsToPurge(3, true, true, false, true);
    final ObjectId snap4 = addSnapshotsToPurge(4, true, true, false, false);
    final ObjectId snap5 = addSnapshotsToPurge(5, true, false, true, true);
    final ObjectId snap6 = addSnapshotsToPurge(6, true, false, true, false);
    final ObjectId snap7 = addSnapshotsToPurge(7, true, false, false, true);
    final ObjectId snap8 = addSnapshotsToPurge(8, true, false, false, false);
    final ObjectId snap9 = addSnapshotsToPurge(9, false, true, true, true);
    final ObjectId snap10 = addSnapshotsToPurge(10, false, true, true, false);
    final ObjectId snap11 = addSnapshotsToPurge(11, false, true, false, true);
    final ObjectId snap12 = addSnapshotsToPurge(12, false, true, false, false);
    final ObjectId snap13 = addSnapshotsToPurge(13, false, false, true, true);
    final ObjectId snap14 = addSnapshotsToPurge(14, false, false, true, false);
    final ObjectId snap15 = addSnapshotsToPurge(15, false, false, false, true);
    final ObjectId snap16 = addSnapshotsToPurge(16, false, false, false, false);

    final List<BackupSnapshot> snapshotsToPurgeForDeletedCopy =
        _backupSnapshotDao.findSnapshotsToPurgeForDeletedCopy(CloudProvider.AWS);
    assertEquals(4, snapshotsToPurgeForDeletedCopy.size());
    final List<ObjectId> snapshotsToPurgeForDeletedCopyIds =
        snapshotsToPurgeForDeletedCopy.stream().map(BackupSnapshot::getId).toList();
    assertTrue(snapshotsToPurgeForDeletedCopyIds.contains(snap1));
    assertTrue(snapshotsToPurgeForDeletedCopyIds.contains(snap2));
    assertTrue(snapshotsToPurgeForDeletedCopyIds.contains(snap3));
    assertTrue(snapshotsToPurgeForDeletedCopyIds.contains(snap4));

    final List<BackupSnapshot> snapshotsToPurgeForDeletedNonShard =
        _backupSnapshotDao.findSnapshotsToPurgeForDeletedNonShard(CloudProvider.AWS);
    assertEquals(4, snapshotsToPurgeForDeletedNonShard.size());
    final List<ObjectId> snapshotsToPurgeForDeletedNonShardIds =
        snapshotsToPurgeForDeletedNonShard.stream().map(BackupSnapshot::getId).toList();

    assertTrue(snapshotsToPurgeForDeletedNonShardIds.contains(snap1));
    assertTrue(snapshotsToPurgeForDeletedNonShardIds.contains(snap2));
    assertTrue(snapshotsToPurgeForDeletedNonShardIds.contains(snap5));
    assertTrue(snapshotsToPurgeForDeletedNonShardIds.contains(snap6));

    final List<BackupSnapshot> snapshotsToPurgeForExpiredCopy =
        _backupSnapshotDao.findSnapshotsToPurgeForExpiredCopy(CloudProvider.AWS, CURRENT_TIME);
    final List<ObjectId> snapshotsToPurgeForExpiredCopyIds =
        snapshotsToPurgeForExpiredCopy.stream().map(BackupSnapshot::getId).toList();
    assertEquals(4, snapshotsToPurgeForExpiredCopy.size());
    assertTrue(snapshotsToPurgeForExpiredCopyIds.contains(snap1));
    assertTrue(snapshotsToPurgeForExpiredCopyIds.contains(snap3));
    assertTrue(snapshotsToPurgeForExpiredCopyIds.contains(snap9));
    assertTrue(snapshotsToPurgeForExpiredCopyIds.contains(snap11));

    final List<BackupSnapshot> snapshotsToPurgeForExpiredNonShard =
        _backupSnapshotDao.findSnapshotsToPurgeForExpiredNonShard(CloudProvider.AWS, CURRENT_TIME);
    final List<ObjectId> snapshotsToPurgeForExpiredNonShardIds =
        snapshotsToPurgeForExpiredNonShard.stream().map(BackupSnapshot::getId).toList();
    assertEquals(4, snapshotsToPurgeForExpiredNonShard.size());
    assertTrue(snapshotsToPurgeForExpiredNonShardIds.contains(snap1));
    assertTrue(snapshotsToPurgeForExpiredNonShardIds.contains(snap5));
    assertTrue(snapshotsToPurgeForExpiredNonShardIds.contains(snap9));
    assertTrue(snapshotsToPurgeForExpiredNonShardIds.contains(snap13));

    final List<BackupSnapshot> snapshotsToPurge =
        _backupSnapshotDao.findSnapshotsToPurgeWithMultipleSimpleQueries(
            CloudProvider.AWS, CURRENT_TIME);
    final List<ObjectId> snapshotsToPurgeIds =
        snapshotsToPurge.stream().map(BackupSnapshot::getId).toList();
    assertEquals(9, snapshotsToPurge.size());
    assertTrue(snapshotsToPurgeIds.contains(snap1));
    assertTrue(snapshotsToPurgeIds.contains(snap2));
    assertTrue(snapshotsToPurgeIds.contains(snap3));
    assertTrue(snapshotsToPurgeIds.contains(snap4));
    assertTrue(snapshotsToPurgeIds.contains(snap5));
    assertTrue(snapshotsToPurgeIds.contains(snap6));
    assertTrue(snapshotsToPurgeIds.contains(snap9));
    assertTrue(snapshotsToPurgeIds.contains(snap11));
    assertTrue(snapshotsToPurgeIds.contains(snap13));
  }

  private ObjectId addSnapshotsToPurge(
      final int index,
      final boolean deleted,
      final boolean copy,
      final boolean nonShard,
      final boolean expired) {
    return addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(CLUSTER_NAME + "replSet")
            .setShard(!nonShard)
            .setStatus(index % 2 == 0 ? Status.COMPLETED : Status.FAILED)
            .setPurged(false)
            .setDeleted(deleted)
            .setType(copy ? Type.COPY : index % 3 == 0 ? Type.SCHEDULED : Type.ON_DEMAND)
            .setScheduledDeletionDate(expired ? PAST_TIME : SCHEDULED_DELETION_DATE),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName" + index));
  }

  @Test
  public void testMarkCompleted() {
    final Date scheduleDeletionDate = new Date();
    final Date completeDate = new Date();
    final Date protectionEndDate = new Date();

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setProtected(true)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap1"));
    _backupSnapshotDao.markCompleted(
        snapshotId1, scheduleDeletionDate, completeDate, BackupRetentionUnit.DAYS);
    final BackupSnapshot snapshot1 = _backupSnapshotDao.findById(snapshotId1).get();
    assertEquals(completeDate, snapshot1.getSnapshotCompletionDate());
    assertEquals(scheduleDeletionDate, snapshot1.getScheduledDeletionDate());
    assertNull(snapshot1.getProtectionEndDate());

    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-2")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.COPY)
                .setProtected(true)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap2"));

    _backupSnapshotDao.markCompletedWithExtraRetention(
        snapshotId2,
        protectionEndDate,
        scheduleDeletionDate,
        completeDate,
        BackupRetentionUnit.DAYS);
    final BackupSnapshot snapshot2 = _backupSnapshotDao.findById(snapshotId2).get();
    assertEquals(completeDate, snapshot2.getSnapshotCompletionDate());
    assertEquals(scheduleDeletionDate, snapshot2.getScheduledDeletionDate());
    assertEquals(protectionEndDate, snapshot2.getProtectionEndDate());
  }

  @Test
  public void testFindCompletedRootSnapshotsByFrequencyTypes() {
    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setShard(false)
                .setProtected(true)
                .setFrequencyType(BackupFrequencyType.HOURLY)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap1"));

    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-2")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setFrequencyType(BackupFrequencyType.HOURLY)
                .setShard(true)
                .setProtected(true)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap2"));

    final ObjectId snapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-3")
                .setShard(false)
                .setType(Type.SCHEDULED)
                .setStatus(Status.COMPLETED)
                .setFrequencyType(BackupFrequencyType.DAILY)
                .setShard(false)
                .setProtected(true)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap3"));

    final ObjectId snapshotId4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-4")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setFrequencyType(BackupFrequencyType.DAILY)
                .setShard(false)
                .setProtected(true)
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap4"));

    List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findCompletedRootSnapshotsByFrequencyTypes(
            PROJECT_ID,
            CLUSTER_UNIQUE_ID,
            false,
            List.of(BackupFrequencyType.DAILY, BackupFrequencyType.HOURLY));

    assertEquals(2, snapshots.size());
    assertEquals(
        Set.of(snapshotId1, snapshotId3),
        snapshots.stream().map(BackupSnapshot::getId).collect(Collectors.toSet()));
  }

  @Test
  public void testMarkDeletedForProject() {
    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap1"));

    final ObjectId snapshotId2 =
        addAzureSnapshot(
            new AzureSnapshotFieldBuilder()
                .withSnapshotName("snap2")
                .withRegionName(AzureRegionName.US_EAST.name()));

    final ObjectId projectId2 = new ObjectId();

    final ObjectId snapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap1"),
            new Date(),
            Optional.of(projectId2),
            Optional.empty(),
            Optional.empty());

    final ObjectId snapshotId4 =
        addAzureSnapshot(
            new SnapshotUpdate(),
            new AzureSnapshotFieldBuilder()
                .withSnapshotName("snap2")
                .withRegionName(AzureRegionName.US_EAST.name()),
            projectId2);

    assertFalse(_backupSnapshotDao.findById(snapshotId1).get().getDeleted());
    assertFalse(_backupSnapshotDao.findById(snapshotId2).get().getDeleted());
    assertFalse(_backupSnapshotDao.findById(snapshotId3).get().getDeleted());
    assertFalse(_backupSnapshotDao.findById(snapshotId4).get().getDeleted());

    _backupSnapshotDao.markDeletedForProject(PROJECT_ID);

    assertTrue(_backupSnapshotDao.findById(snapshotId1).get().getDeleted());
    assertTrue(_backupSnapshotDao.findById(snapshotId2).get().getDeleted());
    assertFalse(_backupSnapshotDao.findById(snapshotId3).get().getDeleted());
    assertFalse(_backupSnapshotDao.findById(snapshotId4).get().getDeleted());
  }

  @Test
  public void testFindSnapshotsToPurge() {
    // add a replica set snapshot
    final ObjectId replSetSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add a failed replica set snapshot
    final ObjectId replSetSnapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0-failed")
                .setShard(false)
                .setStatus(Status.FAILED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add shard snapshots
    final ObjectId shard0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0")
                .setShard(true)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId shard1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(true)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId config0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-config-0")
                .setShard(true)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add a sharded cluster snapshot
    final ObjectId shardedClusterSnapshotId = new ObjectId();
    final Map<ObjectId, String> members = new HashMap<>();
    members.put(shard0, CLUSTER_NAME + "-shard-0");
    members.put(shard1, CLUSTER_NAME + "-shard-1");
    members.put(config0, CLUSTER_NAME + "-config-0");
    addShardedClusterSnapshot(
        shardedClusterSnapshotId, members, BackupSnapshot.Status.IN_PROGRESS, Type.SCHEDULED);

    final Date dateToPurge = new Date(SCHEDULED_DELETION_DATE.getTime() + 1L);
    final List<BackupSnapshot> snapshotToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, dateToPurge);
    // expect to find 1 completed snapshot and 1 failed snapshot
    assertEquals(2, snapshotToPurge.size());

    updateSnapshotWithStatus(shardedClusterSnapshotId, Status.COMPLETED);
    // expect to find 2 completed snapshot and 1 failed snapshot
    final List<BackupSnapshot> snapshotsToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, dateToPurge);
    assertEquals(3, snapshotsToPurge.size());
  }

  @Test
  public void testFindCopySnapshotsToPurge() {
    // add a completed replica set snapshot copy
    final ObjectId replSetSnapshotIdCopy =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add a failed replica set snapshot copy
    final ObjectId replSetSnapshotId2Copy =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "replSet-failed")
                .setShard(false)
                .setStatus(Status.FAILED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add shard copy snapshots, test that copy snapshots of sharded clusters will get purged.
    // Note: copy snapshots have no such concept as parent/root snapshot
    final ObjectId shard0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0")
                .setShard(true)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId shard1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(true)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId config0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-config-0")
                .setShard(true)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setType(Type.COPY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final Date dateToPurge = new Date(SCHEDULED_DELETION_DATE.getTime() + 1L);
    final List<BackupSnapshot> snapshotToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, dateToPurge);
    // expect to find 1 completed replSet snapshot copy, 1 failed replSet snapshot copy
    assertEquals(2, snapshotToPurge.size());

    updateSnapshotWithStatus(shard0, Status.COMPLETED);
    updateSnapshotWithStatus(shard1, Status.COMPLETED);
    updateSnapshotWithStatus(config0, Status.FAILED);
    final List<BackupSnapshot> snapshotsToPurge =
        _backupSnapshotDao.findSnapshotsToPurge(CloudProvider.AWS, dateToPurge);
    // expect to find 1 completed replSet snapshot copy, 1 failed replSet snapshot copy, 2 completed
    // sharded snapshot copy, 1 failed sharded snapshot copy,
    assertEquals(5, snapshotsToPurge.size());
  }

  @Test
  public void testExtendSnapshotsExpirationDate() {
    final List<BackupSnapshot> backupSnapshots = new ArrayList<>();
    for (int i = 0; i < 2; i++) {

      final ObjectId snapshotId =
          _backupSnapshotDao.addBackupSnapshot(
              new SnapshotUpdate()
                  .setId(new ObjectId())
                  .setProjectId(PROJECT_ID)
                  .setClusterName(CLUSTER_NAME)
                  .setDeploymentClusterName(CLUSTER_NAME)
                  .setRsId(RS_ID)
                  .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                  .setScheduledCreationDate(new Date())
                  .setSnapshotInitiationDate(new Date())
                  .setScheduledDeletionDate(new Date())
                  .setDeleted(false)
                  .setPurged(false)
                  .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
                  .setUsedDiskSpace(USED_SPACE)
                  .setCloudProviders(List.of(CloudProvider.AZURE))
                  .setAzureSnapshotField(
                      new AzureSnapshotFieldBuilder()
                          .withSnapshotName(SNAPSHOT_NAME)
                          .withDiskType(AzureDiskType.P10.name())
                          .withSubscriptionId(SUBSCRIPTION_ID)
                          .withResourceGroup(RESOURCE_GROUP)
                          .withRegionName(AzureRegionName.US_EAST.name()))
                  .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
                  .setStatus(Status.COMPLETED)
                  .setType(Type.SCHEDULED)
                  .setFrequencyType(BackupFrequencyType.DAILY)
                  .setOverrideRetentionPolicy(false)
                  .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
                  .setSnapshotCompletionDate(new Date()));
      backupSnapshots.add(_backupSnapshotDao.findById(snapshotId).get());
    }

    final Date extendDate =
        new Date(System.currentTimeMillis() + Duration.ofMinutes(240).toMillis());
    _backupSnapshotDao.extendSnapshotsExpirationDate(
        backupSnapshots.stream().map(BackupSnapshot::getId).collect(Collectors.toList()),
        extendDate);

    final List<BackupSnapshot> extendedSnapshots =
        _backupSnapshotDao.findByIds(
            backupSnapshots.stream().map(BackupSnapshot::getId).collect(Collectors.toList()));
    extendedSnapshots.forEach(
        sn -> assertEquals(extendDate.getTime(), sn.getScheduledDeletionDate().getTime()));
  }

  @Test
  public void testAddShardedClusterSnapshot() {
    final ObjectId clusterSnapshotId1 = new ObjectId();

    final Map<ObjectId, String> expectedSnapshotIds = new HashMap<>();
    for (int i = 0; i < 3; i++) {
      expectedSnapshotIds.put(new ObjectId(), "shard-" + i);
    }

    addShardedClusterSnapshot(
        clusterSnapshotId1, expectedSnapshotIds, BackupSnapshot.Status.IN_PROGRESS, Type.SCHEDULED);

    final Optional<BackupSnapshot> savedBackupSnapshot =
        _backupSnapshotDao.findById(clusterSnapshotId1);
    assertTrue(savedBackupSnapshot.isPresent());
    assertTrue(savedBackupSnapshot.get() instanceof ShardedClusterBackupSnapshot);
    assertEquals(BackupSnapshot.Status.IN_PROGRESS, savedBackupSnapshot.get().getSnapshotStatus());

    final String rsId = "RsId";

    // add another snapshot member and mark shardedClusterSnapshot as completed
    updateShardedClusterSnapshotCompleted(clusterSnapshotId1, rsId);

    final Optional<BackupSnapshot> completedBackupSnapshot =
        _backupSnapshotDao.findById(clusterSnapshotId1);
    assertEquals(
        BackupSnapshot.Status.COMPLETED, completedBackupSnapshot.get().getSnapshotStatus());

    final ShardedClusterBackupSnapshot shardedSnapshot =
        completedBackupSnapshot.map(ShardedClusterBackupSnapshot.class::cast).get();

    assertEquals(CLUSTER_NAME, shardedSnapshot.getClusterName());
    assertEquals(CLUSTER_NAME, shardedSnapshot.getDeploymentClusterName());
    assertEquals(CLUSTER_UNIQUE_ID, shardedSnapshot.getClusterUniqueId());
    assertEquals(0L, shardedSnapshot.getUsedDiskSpace());
    assertFalse(shardedSnapshot.getDeleted());

    final List<ShardedClusterBackupSnapshot.SnapshotMember> snapshotMembers =
        shardedSnapshot.getSnapshotMembers();

    assertEquals(expectedSnapshotIds.size() + 1, snapshotMembers.size());
  }

  @Test
  public void testFindLastActiveByClusterUniqueIdAndRsId() {
    final String snapshotName1 = "snapshotName1";
    final String rsId1 = RS_ID + "_1";

    final String snapshotName2 = "snapshotName2";
    final String rsId2 = RS_ID + "_2";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId1)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1));
    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId2)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName2));

    final Optional<BackupSnapshot> backupSnapshot =
        _backupSnapshotDao.findLastCompletedActiveNonCopyByClusterUniqueIdAndRsId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, rsId1);
    assertTrue(backupSnapshot.isPresent());
    final ReplicaSetBackupSnapshot snapshot = (ReplicaSetBackupSnapshot) backupSnapshot.get();
    assertEquals(rsId1, snapshot.getRsId());
    assertEquals(snapshotId1, snapshot.getId());

    final Optional<BackupSnapshot> backupSnapshot2 =
        _backupSnapshotDao.findLastCompletedActiveNonCopyByClusterUniqueIdAndRsId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, rsId2);
    assertTrue(backupSnapshot2.isPresent());
    final ReplicaSetBackupSnapshot snapshot2 = (ReplicaSetBackupSnapshot) backupSnapshot2.get();
    assertEquals(rsId2, snapshot2.getRsId());
    assertEquals(snapshotId2, snapshot2.getId());
  }

  @Test
  public void testFindLastCompletedActiveNonCopyByClusterUniqueIdAndRsIdAndAwsVolumeId() {
    final String snapshotName1 = "snapshotName1";
    final String rsId1 = RS_ID + "_1";
    final String ebsVolumeId1 = "ebsVolumeId-1";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId1)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1),
            new Date(),
            Optional.empty(),
            Optional.empty(),
            Optional.empty(),
            Optional.of(ebsVolumeId1));

    // Don't find the snapshot when querying by a different ebsVolumeId
    Optional<BackupSnapshot> backupSnapshot =
        _backupSnapshotDao.findLastCompletedActiveNonCopyByClusterUniqueIdAndRsIdAndAwsVolumeId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, rsId1, "DifferentEbsVolumeId");
    assertFalse(backupSnapshot.isPresent());

    // Find the snapshot when querying by the right ebsVolumeId
    backupSnapshot =
        _backupSnapshotDao.findLastCompletedActiveNonCopyByClusterUniqueIdAndRsIdAndAwsVolumeId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, rsId1, ebsVolumeId1);
    assertTrue(backupSnapshot.isPresent());
    ReplicaSetBackupSnapshot snapshot = (ReplicaSetBackupSnapshot) backupSnapshot.get();
    assertEquals(rsId1, snapshot.getRsId());
    assertEquals(snapshotId1, snapshot.getId());

    // Insert a new IN_PROGRESS Snapshot
    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId1)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1),
            new Date(),
            Optional.empty(),
            Optional.empty(),
            Optional.empty(),
            Optional.of(ebsVolumeId1));

    // Returns the previous snapshot since the new one is still IN_PROGRESS
    backupSnapshot =
        _backupSnapshotDao.findLastCompletedActiveNonCopyByClusterUniqueIdAndRsIdAndAwsVolumeId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, rsId1, ebsVolumeId1);
    assertTrue(backupSnapshot.isPresent());
    snapshot = (ReplicaSetBackupSnapshot) backupSnapshot.get();
    assertEquals(rsId1, snapshot.getRsId());
    assertEquals(snapshotId1, snapshot.getId());
    assertNotEquals(snapshotId2, snapshot.getId());
  }

  @Test
  public void testUpdateShardedClusterSnapshotWithMemberData() {
    final ObjectId shardedClusterSnapshotId = new ObjectId();
    final Map<ObjectId, String> members = Collections.emptyMap();
    addShardedClusterSnapshot(
        shardedClusterSnapshotId, members, BackupSnapshot.Status.IN_PROGRESS, Type.SCHEDULED);

    final Optional<BackupSnapshot> snapshotOpt =
        _backupSnapshotDao.findById(shardedClusterSnapshotId);
    assertTrue(snapshotOpt.isPresent());
    final ShardedClusterBackupSnapshot backupSnapshot =
        (ShardedClusterBackupSnapshot) snapshotOpt.get();
    assertEquals(0, backupSnapshot.getSnapshotMembers().size());

    final ObjectId snapshotId1 = new ObjectId();
    final String rsId1 = RS_ID + "_1";
    _backupSnapshotDao.updateShardedClusterSnapshotWithMemberData(
        shardedClusterSnapshotId, snapshotId1, rsId1, null);

    final Optional<BackupSnapshot> snapshotOpt2 =
        _backupSnapshotDao.findById(shardedClusterSnapshotId);
    assertTrue(snapshotOpt2.isPresent());
    assertEquals(BackupSnapshot.Status.IN_PROGRESS, snapshotOpt2.get().getSnapshotStatus());
    final ShardedClusterBackupSnapshot backupSnapshot2 =
        (ShardedClusterBackupSnapshot) snapshotOpt2.get();
    assertEquals(1, backupSnapshot2.getSnapshotMembers().size());

    final ObjectId snapshotId2 = new ObjectId();
    final String rsId2 = RS_ID + "_2";
    _backupSnapshotDao.updateShardedClusterSnapshotWithMemberData(
        shardedClusterSnapshotId, snapshotId2, rsId2, null);

    _backupSnapshotDao.markCompleted(
        shardedClusterSnapshotId, SCHEDULED_DELETION_DATE, new Date(), BackupRetentionUnit.DAYS);
    final Optional<BackupSnapshot> snapshotOpt3 =
        _backupSnapshotDao.findById(shardedClusterSnapshotId);
    assertTrue(snapshotOpt3.isPresent());
    assertEquals(BackupSnapshot.Status.COMPLETED, snapshotOpt3.get().getSnapshotStatus());
    final ShardedClusterBackupSnapshot backupSnapshot3 =
        (ShardedClusterBackupSnapshot) snapshotOpt3.get();
    assertEquals(2, backupSnapshot3.getSnapshotMembers().size());
  }

  @Test
  public void testGetReplicaSetBackupSnapshot() {
    final ObjectId rsSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName"));
    final Optional<ReplicaSetBackupSnapshot> replicaSetSnapshot =
        _backupSnapshotDao.findReplicaSetSnapshotById(
            rsSnapshotId, BackupSnapshot.Status.COMPLETED);
    assertTrue(replicaSetSnapshot.isPresent());
    assertEquals(rsSnapshotId, replicaSetSnapshot.get().getId());
    final ObjectId shardedClusterSnapshotId = new ObjectId();
    final Map<ObjectId, String> members = Collections.emptyMap();
    addShardedClusterSnapshot(
        shardedClusterSnapshotId, members, BackupSnapshot.Status.IN_PROGRESS, Type.SCHEDULED);
    final Optional<ReplicaSetBackupSnapshot> shardedClusterSnapshot =
        _backupSnapshotDao.findReplicaSetSnapshotById(
            shardedClusterSnapshotId, BackupSnapshot.Status.COMPLETED);
    assertFalse(shardedClusterSnapshot.isPresent());
  }

  @Test
  public void testFindModelCursorByClusterNoShard() {
    // add a replica set snapshot
    final String rsId = "rsId1";
    final ObjectId replSetSnapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add another replica set snapshot without the isShard field
    final ObjectId replSetSnapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add a shard snapshot
    final ObjectId shardSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId)
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));
    final ObjectId shardedClusterSnapshotId = new ObjectId();
    final Map<ObjectId, String> members = Collections.emptyMap();
    addShardedClusterSnapshot(
        shardedClusterSnapshotId, members, BackupSnapshot.Status.IN_PROGRESS, Type.SCHEDULED);
    updateShardedClusterSnapshotCompleted(shardedClusterSnapshotId, rsId);
    final ModelCursor<BackupSnapshot> snapshots =
        _backupSnapshotDao.findModelCursorByClusterNotShard(PROJECT_ID, CLUSTER_UNIQUE_ID, false);
    final List<ObjectId> snapshotIds =
        snapshots.toList().stream().map(snapshot -> snapshot.getId()).collect(Collectors.toList());
    assertEquals(3, snapshotIds.size());
    assertTrue(snapshotIds.contains(replSetSnapshotId1));
    assertTrue(snapshotIds.contains(replSetSnapshotId2));
    assertTrue(snapshotIds.contains(shardedClusterSnapshotId));
    assertFalse(snapshotIds.contains(shardSnapshotId));

    final ModelCursor<BackupSnapshot> snapshots2 =
        _backupSnapshotDao.findModelCursorByClusterNotShard(PROJECT_ID, CLUSTER_UNIQUE_ID, false);
    snapshots2.skip(2);
    final List<ObjectId> snapshotIds2 =
        snapshots2.toList().stream().map(snapshot -> snapshot.getId()).collect(Collectors.toList());
    assertEquals(1, snapshotIds2.size());
    assertTrue(
        snapshotIds2.contains(replSetSnapshotId1)
            || snapshotIds2.contains(replSetSnapshotId2)
            || snapshotIds2.contains(shardedClusterSnapshotId));
    assertFalse(snapshotIds2.contains(shardSnapshotId));
  }

  @Test
  public void testFindByIdAndStatus() {
    final ObjectId queuedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(true)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));
    final ObjectId completedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName2"));
    final Optional<BackupSnapshot> completedSnapshotNotFound =
        _backupSnapshotDao.findByIdAndStatus(queuedSnapshotId, BackupSnapshot.Status.COMPLETED);
    assertEquals(Optional.empty(), completedSnapshotNotFound);

    final Optional<BackupSnapshot> queuedSnapshot =
        _backupSnapshotDao.findByIdAndStatus(queuedSnapshotId, BackupSnapshot.Status.QUEUED);
    assertEquals(queuedSnapshot.get().getId(), queuedSnapshotId);

    final Optional<BackupSnapshot> queuedSnapshotNotFound =
        _backupSnapshotDao.findByIdAndStatus(completedSnapshotId, BackupSnapshot.Status.QUEUED);
    assertEquals(Optional.empty(), queuedSnapshotNotFound);

    final Optional<BackupSnapshot> completedSnapshot =
        _backupSnapshotDao.findByIdAndStatus(completedSnapshotId, BackupSnapshot.Status.COMPLETED);
    assertEquals(completedSnapshot.get().getId(), completedSnapshotId);
  }

  @Test
  public void testFindByClusterAndStatusList() {

    final ObjectId queuedSnapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));
    final ObjectId queuedSnapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName2"));
    final ObjectId inProgressSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName3"));
    final ObjectId completedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName4"));
    final ObjectId failedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.FAILED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName5"));
    final ObjectId deletedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName6"));

    final List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findActionableNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertEquals(3, snapshots.size());
    final List<ObjectId> snapshotIds =
        snapshots.stream().map(snapshot -> snapshot.getId()).collect(Collectors.toList());
    assertTrue(snapshotIds.contains(queuedSnapshotId1));
    assertTrue(snapshotIds.contains(queuedSnapshotId2));
    assertTrue(snapshotIds.contains(inProgressSnapshotId));
    assertFalse(snapshotIds.contains(completedSnapshotId));
    assertFalse(snapshotIds.contains(failedSnapshotId));
    assertFalse(snapshotIds.contains(deletedSnapshotId));
  }

  @Test
  public void testUpdateSnapshotInProgress() {
    final ObjectId queuedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId completedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName2"));

    final ObjectId failedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.FAILED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName3"));
    final ObjectId inProgressSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName4"));

    final ObjectId planId = new ObjectId();

    _backupSnapshotDao.setQueuedSnapshotToInProgress(queuedSnapshotId, planId);
    _backupSnapshotDao.setQueuedSnapshotToInProgress(completedSnapshotId, planId);
    _backupSnapshotDao.setQueuedSnapshotToInProgress(failedSnapshotId, planId);
    _backupSnapshotDao.setQueuedSnapshotToInProgress(inProgressSnapshotId, planId);

    final Optional<BackupSnapshot> queuedSnapshotResult =
        _backupSnapshotDao.findById(queuedSnapshotId);
    final Optional<BackupSnapshot> completedSnapshotResult =
        _backupSnapshotDao.findById(completedSnapshotId);
    final Optional<BackupSnapshot> failedSnapshotResult =
        _backupSnapshotDao.findById(failedSnapshotId);
    final Optional<BackupSnapshot> inProgressSnapshotResult =
        _backupSnapshotDao.findById(inProgressSnapshotId);

    assertTrue(queuedSnapshotResult.isPresent());
    assertTrue(completedSnapshotResult.isPresent());
    assertTrue(failedSnapshotResult.isPresent());
    assertTrue(inProgressSnapshotResult.isPresent());

    assertEquals(BackupSnapshot.Status.IN_PROGRESS, queuedSnapshotResult.get().getSnapshotStatus());
    assertEquals(planId, queuedSnapshotResult.get().getPlanId());
    assertEquals(
        BackupSnapshot.Status.COMPLETED, completedSnapshotResult.get().getSnapshotStatus());
    assertEquals(BackupSnapshot.Status.FAILED, failedSnapshotResult.get().getSnapshotStatus());
    assertEquals(
        BackupSnapshot.Status.IN_PROGRESS, inProgressSnapshotResult.get().getSnapshotStatus());
  }

  @Test
  public void testUpdateSnapshotTenants() {
    final ObjectId inProgressSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName4"));
    _backupSnapshotDao.setQueuedSnapshotToInProgress(inProgressSnapshotId, new ObjectId());

    List<ServerlessTenant> tenants =
        List.of(ObjectId.get(), ObjectId.get(), ObjectId.get()).stream()
            .map((id) -> new ServerlessTenant(id, -1))
            .collect(Collectors.toList());
    _backupSnapshotDao.setMtmTenants(inProgressSnapshotId, tenants);

    final Optional<BackupSnapshot> inProgressSnapshotResult =
        _backupSnapshotDao.findById(inProgressSnapshotId);
    assertTrue(inProgressSnapshotResult.isPresent());
    assertEquals(
        BackupSnapshot.Status.IN_PROGRESS, inProgressSnapshotResult.get().getSnapshotStatus());
    assertTrue(
        CollectionUtils.isEqualCollection(
            tenants.stream().map(tenant -> tenant.getTenantId()).collect(Collectors.toList()),
            inProgressSnapshotResult.get().getTenants().stream()
                .map(tenant -> tenant.getTenantId())
                .collect(Collectors.toList())));
  }

  @Test
  public void testFindCopySnapshotsByStatus() {
    final ObjectId queuedOnDemandSnasphotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.ON_DEMAND)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final List<ReplicaSetBackupSnapshot> snapshots1 =
        _backupSnapshotDao.findCopySnapshotsByStatus(PROJECT_ID, CLUSTER_UNIQUE_ID, Status.QUEUED);

    assertEquals(0, snapshots1.size());

    final ObjectId queuedCopySnasphotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.COPY)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final List<ReplicaSetBackupSnapshot> snapshots2 =
        _backupSnapshotDao.findCopySnapshotsByStatus(PROJECT_ID, CLUSTER_UNIQUE_ID, Status.QUEUED);

    assertEquals(1, snapshots2.size());

    final ObjectId queuedCopySnasphotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.COPY)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final List<ReplicaSetBackupSnapshot> snapshots3 =
        _backupSnapshotDao.findCopySnapshotsByStatus(PROJECT_ID, CLUSTER_UNIQUE_ID, Status.QUEUED);

    assertEquals(2, snapshots3.size());

    final ObjectId inProgressCopySnasphotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.COPY)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final List<ReplicaSetBackupSnapshot> snapshots4 =
        _backupSnapshotDao.findCopySnapshotsByStatus(
            PROJECT_ID, CLUSTER_UNIQUE_ID, Status.IN_PROGRESS);

    assertEquals(1, snapshots4.size());

    final ObjectId completedCopySnasphotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.COPY)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final List<ReplicaSetBackupSnapshot> snapshots5 =
        _backupSnapshotDao.findCopySnapshotsByStatus(
            PROJECT_ID, CLUSTER_UNIQUE_ID, Status.COMPLETED);

    assertEquals(1, snapshots5.size());

    final List<ReplicaSetBackupSnapshot> snapshots6 =
        _backupSnapshotDao.findCopySnapshotsByStatus(PROJECT_ID, CLUSTER_UNIQUE_ID, null);

    assertEquals(4, snapshots6.size());
  }

  @Test
  public void testFindInProgressOrQueuedOnDemandSnapshots() {

    final ObjectId queuedSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.ON_DEMAND)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    final ObjectId queuedSnapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(RS_ID)
            .setShard(true)
            .setStatus(Status.QUEUED)
            .setType(Type.ON_DEMAND)
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(RS_ID)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.ON_DEMAND)
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(RS_ID)
            .setShard(false)
            .setStatus(Status.FAILED)
            .setType(Type.ON_DEMAND)
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(RS_ID)
            .setShard(false)
            .setStatus(Status.IN_PROGRESS)
            .setType(Type.ON_DEMAND)
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final ObjectId shardedInProgressSnapshotId = new ObjectId();
    final ObjectId shardedQueuedSnapshotId = new ObjectId();
    final ObjectId shardedCompletedSnapshotId = new ObjectId();
    final ObjectId shardedFailedSnapshotId = new ObjectId();
    addShardedClusterSnapshot(
        shardedInProgressSnapshotId,
        Collections.emptyMap(),
        BackupSnapshot.Status.IN_PROGRESS,
        BackupSnapshot.Type.ON_DEMAND);
    addShardedClusterSnapshot(
        shardedQueuedSnapshotId,
        Collections.emptyMap(),
        BackupSnapshot.Status.QUEUED,
        BackupSnapshot.Type.ON_DEMAND);
    addShardedClusterSnapshot(
        shardedCompletedSnapshotId,
        Collections.emptyMap(),
        BackupSnapshot.Status.COMPLETED,
        BackupSnapshot.Type.ON_DEMAND);
    addShardedClusterSnapshot(
        shardedFailedSnapshotId,
        Collections.emptyMap(),
        BackupSnapshot.Status.FAILED,
        BackupSnapshot.Type.ON_DEMAND);

    final List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findActiveQueuedOrInProgressOnDemand(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertEquals(4, snapshots.size());

    // delete one snapshot
    _backupSnapshotDao.markSnapshotsDeleted(Collections.singletonList(queuedSnapshotId));
    final List<BackupSnapshot> snapshots1 =
        _backupSnapshotDao.findActiveQueuedOrInProgressOnDemand(PROJECT_ID, CLUSTER_UNIQUE_ID);

    assertEquals(3, snapshots1.size());
  }

  @Test
  public void testFindQueuedResilientForCluster() {
    final ObjectId shard1SnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(PROJECT_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setRsId(RS_ID)
                .setShard(true)
                .setStatus(Status.QUEUED)
                .setType(Type.FALLBACK)
                .setDeleted(false)
                .setPurged(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final Optional<BackupSnapshot> queuedSnapshot =
        _backupSnapshotDao.findQueuedNonCopySnapshotForPlanning(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertTrue(queuedSnapshot.isEmpty());

    final ObjectId replSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(PROJECT_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setRsId(RS_ID)
                .setStatus(Status.QUEUED)
                .setType(Type.FALLBACK)
                .setDeleted(false)
                .setPurged(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final Optional<BackupSnapshot> queuedReplSnapshot =
        _backupSnapshotDao.findQueuedNonCopySnapshotForPlanning(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertEquals(replSnapshotId, queuedReplSnapshot.get().getId());

    _backupSnapshotDao.setQueuedSnapshotToInProgress(replSnapshotId, new ObjectId());

    final Optional<BackupSnapshot> queuedReplSnapshot1 =
        _backupSnapshotDao.findQueuedNonCopySnapshotForPlanning(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertTrue(queuedReplSnapshot1.isEmpty());
  }

  @Test
  public void test_findLastCompletedByCluster() {
    final ObjectId s1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(PROJECT_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setStatus(Status.QUEUED)
                .setDeleted(false)
                .setType(Type.ON_DEMAND),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, true)
            .isEmpty());
    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, false)
            .isEmpty());

    final ObjectId s2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(PROJECT_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setStatus(Status.IN_PROGRESS)
                .setDeleted(false)
                .setType(Type.ON_DEMAND),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, true)
            .isEmpty());
    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, false)
            .isEmpty());

    final ObjectId s3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(PROJECT_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setStatus(Status.COMPLETED)
                .setDeleted(true)
                .setSnapshotInitiationDate(new Date())
                .setType(Type.ON_DEMAND),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, true)
            .isEmpty());

    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, false)
            .isPresent());

    assertEquals(
        s3,
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, false)
            .get()
            .getId());

    final ObjectId s4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(PROJECT_ID)
                .setClusterUniqueId(CLUSTER_UNIQUE_ID)
                .setStatus(Status.COMPLETED)
                .setDeleted(false)
                .setSnapshotInitiationDate(new Date())
                .setType(Type.ON_DEMAND),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, true)
            .isPresent());

    assertEquals(
        s4,
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, true)
            .get()
            .getId());

    assertTrue(
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, false)
            .isPresent());

    assertEquals(
        s4,
        _backupSnapshotDao
            .findLastCompletedNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID, false)
            .get()
            .getId());
  }

  @Test
  public void test_findCompletedScheduledByCluster() {
    final ObjectId shardSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final ObjectId shardedCompletedSnapshotId = new ObjectId();
    addShardedClusterSnapshot(
        shardedCompletedSnapshotId,
        Collections.emptyMap(),
        BackupSnapshot.Status.COMPLETED,
        BackupSnapshot.Type.SCHEDULED);

    // the single shard snapshot and the sharded snapshot are not necessarily
    // of the same cluster, but that's not what this test cares about. We
    // Only care about root vs non-root here

    final List<BackupSnapshot> allUnpurged =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            PROJECT_ID, CLUSTER_UNIQUE_ID, false, false);
    assertEquals(2, allUnpurged.size());

    final List<BackupSnapshot> rootOnlyUnpurged =
        _backupSnapshotDao.findCompletedScheduledByCluster(
            PROJECT_ID, CLUSTER_UNIQUE_ID, false, true);
    assertEquals(1, rootOnlyUnpurged.size());
  }

  @Test
  public void testFindSnapshotsAffectedByPolicy() {
    final ObjectId policyItemId1 = new ObjectId();
    final ObjectId policyItemId2 = new ObjectId();
    final ObjectId policyItemId3 = new ObjectId();
    final PolicyItem policyItem1 =
        new PolicyItem(
            policyItemId1,
            BackupFrequencyType.HOURLY,
            1,
            new Date(),
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS);
    final PolicyItem policyItem2 =
        new PolicyItem(
            policyItemId2,
            BackupFrequencyType.HOURLY,
            1,
            new Date(),
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS);
    final PolicyItem policyItem3 =
        new PolicyItem(
            policyItemId3,
            BackupFrequencyType.HOURLY,
            1,
            new Date(),
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS);

    final ObjectId completedSnapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPolicyItemIds(
                    List.of(policyItem1).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId1),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    final ObjectId completedSnapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPolicyItemIds(
                    Arrays.asList(policyItem1, policyItem2).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId2),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    final ObjectId completedSnapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPolicyItemIds(
                    Arrays.asList(policyItem2, policyItem3).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId3),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    // this will be ignored because it's on demand
    final ObjectId queuedOnDemandSnapshotId4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.ON_DEMAND)
                .setDeleted(false)
                .setPolicyItemIds(
                    List.of(policyItem1).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId1),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    // this will be ignored because override retention policy is true
    final ObjectId overrideRetentionSnapshotId5 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPolicyItemIds(
                    Arrays.asList(policyItem1, policyItem2).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId2),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    _backupSnapshotDao.overrideRetention(
        overrideRetentionSnapshotId5, new Date(), BackupRetentionUnit.DAYS);

    // this will be ignored because it's deleted
    final ObjectId deletedSnapshotId6 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(true)
                .setPolicyItemIds(
                    List.of(policyItem1).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId1),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    // this will be ignored because it's not completed yet
    final ObjectId queuedSnapshotId7 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.QUEUED)
                .setType(Type.SCHEDULED)
                .setDeleted(false)
                .setPolicyItemIds(
                    List.of(policyItem1).stream()
                        .map(PolicyItem::getId)
                        .collect(Collectors.toList()))
                .setMainPolicyItemId(policyItemId1),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    List<BackupSnapshot> snapshotsAffectedByPolicy =
        _backupSnapshotDao.findSnapshotsAffectedByPolicy(
            PROJECT_ID, CLUSTER_UNIQUE_ID, List.of(policyItemId1));
    assertEquals(
        Collections.singletonList(completedSnapshotId1),
        snapshotsAffectedByPolicy.stream().map(BackupSnapshot::getId).collect(Collectors.toList()));
    snapshotsAffectedByPolicy =
        _backupSnapshotDao.findSnapshotsAffectedByPolicy(
            PROJECT_ID, CLUSTER_UNIQUE_ID, Arrays.asList(policyItemId1, policyItemId2));
    assertEquals(
        Arrays.asList(completedSnapshotId1, completedSnapshotId2),
        snapshotsAffectedByPolicy.stream().map(BackupSnapshot::getId).collect(Collectors.toList()));
    snapshotsAffectedByPolicy =
        _backupSnapshotDao.findSnapshotsAffectedByPolicy(
            PROJECT_ID, CLUSTER_UNIQUE_ID, List.of(policyItemId3));
    assertEquals(
        Collections.singletonList(completedSnapshotId3),
        snapshotsAffectedByPolicy.stream().map(BackupSnapshot::getId).collect(Collectors.toList()));
  }

  @Test
  public void testGeoShardedSnapshot_geoShardedRegions() {

    final ObjectId snapshotId =
        addGeoShardedAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));

    final Optional<BackupSnapshot> savedSnapshot = _backupSnapshotDao.findById(snapshotId);

    assertTrue(savedSnapshot.isPresent());

    final AWSBackupSnapshot backupSnapshot = (AWSBackupSnapshot) savedSnapshot.get();

    assertTrue(backupSnapshot.isGeoSharded());

    assertEquals(AWSRegionName.US_EAST_1, backupSnapshot.getRegion());
  }

  @Test
  public void testFindSnapshots() {
    final String snapshotName1 = "snapshotName1";
    final String snapshotName2 = "snapshotName2";
    final String snapshotName3 = "snapshotName3";
    final String snapshotName4 = "snapshotName4";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1));
    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.FALLBACK)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName2));

    final ObjectId snapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.FALLBACK)
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName3));

    final ObjectId snapshotId4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.FALLBACK)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName4));

    final List<BackupSnapshot> backupSnapshots =
        _backupSnapshotDao.findSnapshots(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertEquals(4, backupSnapshots.size());
    final List<ObjectId> snapshotIds =
        backupSnapshots.stream()
            .map(ReplicaSetBackupSnapshot.class::cast)
            .map(ReplicaSetBackupSnapshot::getId)
            .collect(Collectors.toList());
    assertTrue(snapshotIds.contains(snapshotId1));
    assertTrue(snapshotIds.contains(snapshotId2));
    assertTrue(snapshotIds.contains(snapshotId3));
    assertTrue(snapshotIds.contains(snapshotId4));
  }

  @Test
  public void testFindServerlessSnapshots_FilteringOutManualSnapshots() {
    testFindServerlessSnapshots(true);
  }

  @Test
  public void testFindServerlessSnapshots_NotFilteringOutManualSnapshots() {
    testFindServerlessSnapshots(false);
  }

  private void testFindServerlessSnapshots(final boolean pFilterOutManualSnapshots) {
    final ObjectId tenantId = new ObjectId();
    final String firstSnapshotName = "firstSnapshotName";
    final String secondSnapshotName = "secondSnapshotName";
    final String thirdSnapshotName = "thirdSnapshotName";
    final SnapshotUpdate snapshotUpdate =
        new SnapshotUpdate()
            .setRsId(RS_ID)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setDeleted(false)
            .setRequestingUser(
                new RequestingUser(
                    new BasicDBObject(FieldDefs.USERNAME, "Test User")
                        .append(FieldDefs.EMAIL, "<EMAIL>")));

    final Instant now = Instant.now();
    final ObjectId firstSnapshotId =
        addAwsSnapshot(
            snapshotUpdate,
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(firstSnapshotName),
            Date.from(now.minus(Duration.ofDays(3))));
    final ObjectId secondSnapshotId =
        addAwsSnapshot(
            snapshotUpdate,
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(secondSnapshotName),
            Date.from(now.minus(Duration.ofDays(2))));
    final ObjectId thirdSnapshotId =
        addAwsSnapshot(
            snapshotUpdate,
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(thirdSnapshotName),
            Date.from(now.minus(Duration.ofDays(1))));
    _backupSnapshotDao.setMtmTenants(firstSnapshotId, List.of(new ServerlessTenant(tenantId, -1L)));
    _backupSnapshotDao.setMtmTenants(
        secondSnapshotId, List.of(new ServerlessTenant(tenantId, -1L)));
    _backupSnapshotDao.setMtmTenants(thirdSnapshotId, List.of(new ServerlessTenant(tenantId, -1L)));

    final List<BackupSnapshot> backupSnapshots =
        _backupSnapshotDao.get2MostRecentByMtmTenantId(tenantId, pFilterOutManualSnapshots);

    if (pFilterOutManualSnapshots) {
      assertTrue(backupSnapshots.isEmpty());
    } else {
      assertEquals(2, backupSnapshots.size());
      final List<ObjectId> snapshotIds =
          backupSnapshots.stream()
              .map(ReplicaSetBackupSnapshot.class::cast)
              .map(ReplicaSetBackupSnapshot::getId)
              .collect(Collectors.toList());

      // we want the second and third snapshots here because they are the most recent 2
      assertEquals(secondSnapshotId, snapshotIds.get(1));
      assertEquals(thirdSnapshotId, snapshotIds.get(0));

      final Optional<BackupSnapshot> latestSnapshot =
          _backupSnapshotDao.getLatestSnapshotByMtmTenantId(tenantId);
      assertTrue(latestSnapshot.isPresent());
      assertEquals(thirdSnapshotId, latestSnapshot.get().getId());
    }
  }

  @Test
  public void testFindSnapshotsForProjectAndProvider() {
    final String awsSnapshotName = "awsSnapshotName";
    final String azureSnapshotName = "azureSnapshotName";
    final String awsSnapshotName2 = "awsSnapshotName2";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));
    final ObjectId snapshotId2 =
        addAzureSnapshot(new AzureSnapshotFieldBuilder().withSnapshotName(azureSnapshotName));

    final ObjectId snapshotId3 = addGcpSnapshot(GCPRegionName.EASTERN_US.getName());

    final ObjectId snapshotId4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.FALLBACK)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName2));

    final List<BackupSnapshot> awsBackupSnapshots =
        _backupSnapshotDao.findSnapshotsForProjectAndCloudProvider(PROJECT_ID, CloudProvider.AWS);
    assertEquals(2, awsBackupSnapshots.size());
    final List<ObjectId> awsSnapshotIds =
        awsBackupSnapshots.stream()
            .map(ReplicaSetBackupSnapshot.class::cast)
            .map(ReplicaSetBackupSnapshot::getId)
            .collect(Collectors.toList());
    assertTrue(awsSnapshotIds.contains(snapshotId1));
    assertFalse(awsSnapshotIds.contains(snapshotId2));
    assertFalse(awsSnapshotIds.contains(snapshotId3));
    assertTrue(awsSnapshotIds.contains(snapshotId4));

    final List<BackupSnapshot> azureBackupSnapshots =
        _backupSnapshotDao.findSnapshotsForProjectAndCloudProvider(PROJECT_ID, CloudProvider.AZURE);
    assertEquals(1, azureBackupSnapshots.size());
    final List<ObjectId> azureSnapshotIds =
        azureBackupSnapshots.stream()
            .map(ReplicaSetBackupSnapshot.class::cast)
            .map(ReplicaSetBackupSnapshot::getId)
            .collect(Collectors.toList());
    assertFalse(azureSnapshotIds.contains(snapshotId1));
    assertTrue(azureSnapshotIds.contains(snapshotId2));
    assertFalse(azureSnapshotIds.contains(snapshotId3));
    assertFalse(azureSnapshotIds.contains(snapshotId4));

    final List<BackupSnapshot> gcpBackupSnapshots =
        _backupSnapshotDao.findSnapshotsForProjectAndCloudProvider(PROJECT_ID, CloudProvider.GCP);
    assertEquals(1, gcpBackupSnapshots.size());
    final List<ObjectId> gcpSnapshotIds =
        gcpBackupSnapshots.stream()
            .map(ReplicaSetBackupSnapshot.class::cast)
            .map(ReplicaSetBackupSnapshot::getId)
            .collect(Collectors.toList());
    assertFalse(gcpSnapshotIds.contains(snapshotId1));
    assertFalse(gcpSnapshotIds.contains(snapshotId2));
    assertTrue(gcpSnapshotIds.contains(snapshotId3));
    assertFalse(gcpSnapshotIds.contains(snapshotId4));

    assertTrue(
        _backupSnapshotDao
            .findSnapshotsForProjectAndCloudProvider(new ObjectId(), CloudProvider.AWS)
            .isEmpty());
  }

  @Test
  public void testFindCompletedNonResilientNonCopyByCluster() {
    final String awsSnapshotName = "aws";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPitSentinelOptime(new BSONTimestamp(4, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));

    final ObjectId copySnapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.COPY)
                .setPitSentinelOptime(new BSONTimestamp(4, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));

    List<BackupSnapshot> snapshots =
        _backupSnapshotDao.findCompletedNonResilientNonCopyByCluster(
            PROJECT_ID, CLUSTER_UNIQUE_ID, false, new BSONTimestamp(2, 1));
    assertEquals(1, snapshots.size());
    assertEquals(snapshotId1, snapshots.get(0).getId());

    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPitSentinelOptime(new BSONTimestamp(7, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));

    final ObjectId copySnapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.COPY)
                .setPitSentinelOptime(new BSONTimestamp(7, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));

    snapshots =
        _backupSnapshotDao.findCompletedNonResilientNonCopyByCluster(
            PROJECT_ID, CLUSTER_UNIQUE_ID, false, new BSONTimestamp(2, 1));
    assertEquals(2, snapshots.size());
    Set<ObjectId> snapshotIds = snapshots.stream().map(s -> s.getId()).collect(Collectors.toSet());
    assertTrue(snapshotIds.contains(snapshotId1));
    assertTrue(snapshotIds.contains(snapshotId2));

    final ObjectId snapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.FALLBACK)
                .setPitSentinelOptime(new BSONTimestamp(7, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));

    final ObjectId copySnapshotId3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.COPY)
                .setPitSentinelOptime(new BSONTimestamp(7, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName));

    snapshots =
        _backupSnapshotDao.findCompletedNonResilientNonCopyByCluster(
            PROJECT_ID, CLUSTER_UNIQUE_ID, false, new BSONTimestamp(2, 1));
    assertEquals(2, snapshots.size());
    snapshotIds = snapshots.stream().map(s -> s.getId()).collect(Collectors.toSet());
    assertTrue(snapshotIds.contains(snapshotId1));
    assertTrue(snapshotIds.contains(snapshotId2));

    snapshots =
        _backupSnapshotDao.findCompletedNonResilientNonCopyByCluster(
            PROJECT_ID, CLUSTER_UNIQUE_ID, false, new BSONTimestamp(6, 1));
    assertEquals(1, snapshots.size());
    assertEquals(snapshotId2, snapshots.get(0).getId());
  }

  @Test
  public void testFindOldestCompletedActiveClusterEmpty() {
    { // no snapshot
      final Optional<BackupSnapshot> backupSnapshot =
          _backupSnapshotDao.findOldestCompletedActiveByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
      assertTrue(backupSnapshot.isEmpty());
    }

    { // only snapshot has been deleted
      final String snapshotName1 = "snapshotName1";
      final String rsId1 = RS_ID + "_1";

      final ObjectId snapshotId1 =
          addAwsSnapshot(
              new SnapshotUpdate()
                  .setRsId(rsId1)
                  .setShard(false)
                  .setStatus(Status.COMPLETED)
                  .setType(Type.SCHEDULED)
                  .setDeleted(true),
              new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1));

      final Optional<BackupSnapshot> backupSnapshot =
          _backupSnapshotDao.findOldestCompletedActiveByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
      assertTrue(backupSnapshot.isEmpty());
    }
  }

  @Test
  public void testFindOldestCompletedActiveCluster() {
    final String snapshotName1 = "snapshotName1";
    final String rsId1 = RS_ID + "_1";

    final String snapshotName2 = "snapshotName2";
    final String rsId2 = RS_ID + "_2";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId1)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1));

    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId2)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName2));

    final Optional<BackupSnapshot> backupSnapshot =
        _backupSnapshotDao.findOldestCompletedActiveByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
    assertTrue(backupSnapshot.isPresent());
    final ReplicaSetBackupSnapshot snapshot = (ReplicaSetBackupSnapshot) backupSnapshot.get();
    assertEquals(rsId1, snapshot.getRsId());
    assertEquals(snapshotId1, snapshot.getId());
  }

  @Test
  public void testFindAnyActiveSnapshotByProjectFound() {
    final String snapshotName1 = "snapshotName1";
    final String rsId1 = RS_ID + "_1";

    final String snapshotName2 = "snapshotName2";
    final String rsId2 = RS_ID + "_2";

    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId1)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1));

    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(rsId2)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName2));

    final Optional<BackupSnapshot> backupSnapshot =
        _backupSnapshotDao.findAnyActiveSnapshotByProject(PROJECT_ID);
    assertTrue(backupSnapshot.isPresent());
    assertEquals(snapshotId1, backupSnapshot.get().getId());
  }

  @Test
  public void testFindAnyActiveSnapshotByProjectNotFound() {
    final String snapshotName1 = "snapshotName1";
    final String rsId1 = RS_ID + "_1";

    final String snapshotName2 = "snapshotName2";
    final String rsId2 = RS_ID + "_2";

    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(rsId1)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setPurged(true),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName1));

    addAwsSnapshot(
        new SnapshotUpdate()
            .setRsId(rsId2)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setDeleted(true),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId(snapshotName2));

    final Optional<BackupSnapshot> backupSnapshot =
        _backupSnapshotDao.findAnyActiveSnapshotByProject(PROJECT_ID);
    assertTrue(backupSnapshot.isEmpty());
  }

  @Test
  public void testFindAllActiveSnapshotByProjectAndRegionFound() {
    final RegionName region = AWSRegionName.US_EAST_1;

    final String snapshotName1 = "snapshotName1";
    final String snapshotName2 = "snapshotName2";

    // both snapshots with snapshotId1 and snapshotId2 should be found by query
    final ObjectId snapshotId1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setPurged(false)
                .setType(Type.SCHEDULED)
                .setStatus(Status.COMPLETED),
            new AwsSnapshotFieldBuilder()
                .withEbsSnapshotId(snapshotName1)
                .withRegionName(region.getName()));
    final ObjectId snapshotId2 =
        addAwsSnapshot(
            new SnapshotUpdate().setPurged(false).setType(Type.SCHEDULED).setStatus(Status.FAILED),
            new AwsSnapshotFieldBuilder()
                .withEbsSnapshotId(snapshotName2)
                .withRegionName(region.getName()));

    final List<BackupSnapshot> backupSnapshots =
        _backupSnapshotDao.findAllActiveSnapshotsByProjectAndRegion(PROJECT_ID, region);
    assertEquals(2, backupSnapshots.size());
    assertEquals(
        backupSnapshots.stream()
            .filter(snapshot -> snapshot.getId().equals(snapshotId1))
            .toList()
            .size(),
        1);
    assertEquals(
        backupSnapshots.stream()
            .filter(snapshot -> snapshot.getId().equals(snapshotId2))
            .toList()
            .size(),
        1);
  }

  @Test
  public void testFindAllActiveSnapshotByProjectAndRegionNotFound() {
    final RegionName awsRegion = AWSRegionName.US_EAST_1;
    final RegionName otherAWSRegion = AWSRegionName.US_EAST_2;

    final String snapshotName1 = "snapshotName1";
    final String snapshotName2 = "snapshotName2";
    final String snapshotName3 = "snapshotName3";
    final String snapshotName4 = "snapshotName4";
    final String snapshotName5 = "snapshotName5";

    // first snapshot should not be found because it has a different cloud provider
    addGcpSnapshot(
        new SnapshotUpdate().setPurged(false).setType(Type.SCHEDULED).setStatus(Status.COMPLETED),
        new GcpSnapshotFieldBuilder()
            .withSnapshotName(snapshotName1)
            .withRegionName(awsRegion.getName()));

    // second snapshot should not be found because it has a different project ID
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(new ObjectId())
            .setPurged(false)
            .setType(Type.SCHEDULED)
            .setStatus(Status.COMPLETED),
        new AwsSnapshotFieldBuilder()
            .withEbsSnapshotId(snapshotName2)
            .withRegionName(awsRegion.getName()));

    // snapshot with snapshotId3 should not be found because it has a different region name
    addAwsSnapshot(
        new SnapshotUpdate().setPurged(false).setType(Type.SCHEDULED).setStatus(Status.COMPLETED),
        new AwsSnapshotFieldBuilder()
            .withEbsSnapshotId(snapshotName3)
            .withRegionName(otherAWSRegion.getName()));

    // snapshot with snapshotId4 should not be found because it has been purged
    addAwsSnapshot(
        new SnapshotUpdate().setPurged(true).setType(Type.SCHEDULED).setStatus(Status.COMPLETED),
        new AwsSnapshotFieldBuilder()
            .withEbsSnapshotId(snapshotName4)
            .withRegionName(awsRegion.getName()));

    // snapshot with snapshotId5 should not be found because its status is not completed
    addAwsSnapshot(
        new SnapshotUpdate().setPurged(false).setType(Type.SCHEDULED).setStatus(Status.IN_PROGRESS),
        new AwsSnapshotFieldBuilder()
            .withEbsSnapshotId(snapshotName5)
            .withRegionName(awsRegion.getName()));

    final List<BackupSnapshot> backupSnapshots =
        _backupSnapshotDao.findAllActiveSnapshotsByProjectAndRegion(PROJECT_ID, awsRegion);
    assertTrue(backupSnapshots.isEmpty());
  }

  @Test
  public void testGetSnapshotRegionsForProject() {
    final String awsSnapshotName = "aws";

    final ObjectId snapshotDeleted1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPitSentinelOptime(new BSONTimestamp(4, 1))
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName),
            Optional.empty(),
            Optional.of(AWSRegionName.US_EAST_1.getName()),
            Optional.empty());

    final ObjectId snapshotLive2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPitSentinelOptime(new BSONTimestamp(4, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName),
            Optional.empty(),
            Optional.of(AWSRegionName.US_EAST_2.getName()),
            Optional.empty());

    final ObjectId snapshotCopy3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.COPY)
                .setPitSentinelOptime(new BSONTimestamp(4, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName),
            Optional.empty(),
            Optional.of(AWSRegionName.US_WEST_1.getName()),
            Optional.empty());

    final ObjectId snapshotFallback4 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.FALLBACK)
                .setPitSentinelOptime(new BSONTimestamp(7, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName),
            Optional.empty(),
            Optional.of(AWSRegionName.CA_CENTRAL_1.getName()),
            Optional.empty());

    final ObjectId snapshotCluster2Live5 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setPitSentinelOptime(new BSONTimestamp(4, 1))
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(awsSnapshotName),
            Optional.empty(),
            Optional.of(AWSRegionName.EU_CENTRAL_1.getName()),
            Optional.of(CLUSTER_UNIQUE_ID_2));

    { // exclude deleted with cluster id
      Set<RegionName> actualRegions =
          _backupSnapshotDao.getUnpurgedSnapshotRegions(
              PROJECT_ID, Optional.of(CLUSTER_UNIQUE_ID), true);

      assertEquals(3, actualRegions.size());

      Set<RegionName> expectedRegions = new HashSet<>();
      expectedRegions.add(AWSRegionName.US_EAST_2); // snapshotLive2
      expectedRegions.add(AWSRegionName.US_WEST_1); // snapshotCopy3
      expectedRegions.add(AWSRegionName.CA_CENTRAL_1); // snapshotFallback4

      assertEquals(expectedRegions, actualRegions);
    }

    { // include deleted with cluster id
      Set<RegionName> actualRegions =
          _backupSnapshotDao.getUnpurgedSnapshotRegions(
              PROJECT_ID, Optional.of(CLUSTER_UNIQUE_ID), false);

      assertEquals(4, actualRegions.size());

      Set<RegionName> expectedRegions = new HashSet<>();

      expectedRegions.add(AWSRegionName.US_EAST_1); // snapshotDeleted1
      expectedRegions.add(AWSRegionName.US_EAST_2); // snapshotLive2
      expectedRegions.add(AWSRegionName.US_WEST_1); // snapshotCopy3
      expectedRegions.add(AWSRegionName.CA_CENTRAL_1); // snapshotFallback4

      assertEquals(expectedRegions, actualRegions);
    }

    { // without specifying cluster id and excludeDeleted
      Set<RegionName> actualRegions = _backupSnapshotDao.getUnpurgedSnapshotRegions(PROJECT_ID);

      assertEquals(5, actualRegions.size());

      Set<RegionName> expectedRegions = new HashSet<>();
      expectedRegions.add(AWSRegionName.US_EAST_1); // snapshotDeleted1
      expectedRegions.add(AWSRegionName.US_EAST_2); // snapshotLive2
      expectedRegions.add(AWSRegionName.US_WEST_1); // snapshotCopy3
      expectedRegions.add(AWSRegionName.CA_CENTRAL_1); // snapshotFallback4
      expectedRegions.add(AWSRegionName.EU_CENTRAL_1); // snapshotCluster2Live5

      assertEquals(expectedRegions, actualRegions);
    }
  }

  @Test
  public void testSLSBackupSnapshots() {
    final ObjectId snapshotId =
        addSLSSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setCloudProviders(List.of(CloudProvider.AWS))
                .setDeleted(false),
            new SLSSnapshotFieldBuilder());

    final Optional<BackupSnapshot> savedSnapshot = _backupSnapshotDao.findById(snapshotId);
    assertTrue(savedSnapshot.isPresent());

    final SLSBackupSnapshot backupSnapshot = (SLSBackupSnapshot) savedSnapshot.get();
    assertFalse(backupSnapshot.isGeoSharded());

    assertEquals(SLS_BACKUP_CLUSTERSHOT_ID, backupSnapshot.getSlsClustershotId());
    assertEquals(SLS_BACKUP_SNAPSHOT_ID, backupSnapshot.getSlsSnapshotId());
    assertEquals(
        SLS_BACKUP_LOG_ID, (Long) backupSnapshot.getSlsDisaggregatedStorageConfig().getSlsLogId());
  }

  private ObjectId addGeoShardedAwsSnapshot(
      final SnapshotUpdate update, final AwsSnapshotFieldBuilder awsBuilder) {
    final String awsRegionName = AWSRegionName.US_EAST_1.getName();
    final ObjectId awsAccountId = new ObjectId();
    final ObjectId awsContainerId = new ObjectId();
    final String awsSubnetId = "awsSubnetId-1";
    final String ebsVolumeId = "ebsVolumeId-1";
    final String ebsSnapshotDesc = AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId);
    final String ebsVolumeType = VolumeType.Io1.name();
    final boolean ebsVolumeEncrypted = true;
    final Integer ebsVolumeSize = AWSNDSInstanceSize.M30.getDefaultDiskSizeGB();
    final Integer ebsDiskIOPS = AWSNDSInstanceSize.M30.getMaxEBSStandardIOPS();

    final Date completionDate = new Date();
    return _backupSnapshotDao.addBackupSnapshot(
        update
            .setId(new ObjectId())
            .setProjectId(PROJECT_ID)
            .setClusterName(CLUSTER_NAME)
            .setDeploymentClusterName(CLUSTER_NAME)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
            .setSnapshotInitiationDate(SNAPSHOT_INITIATION_DATE)
            .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
            .setPurged(false)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(USED_SPACE)
            .setCloudProviders(List.of(CloudProvider.AWS))
            .setAwsSnapshotField(
                awsBuilder
                    .withEbsSnapshotDescription(ebsSnapshotDesc)
                    .withAWSAccountId(awsAccountId)
                    .withAWSContainerId(awsContainerId)
                    .withAWSSubnetId(awsSubnetId)
                    .withEbsVolumeId(ebsVolumeId)
                    .withEbsVolumeType(ebsVolumeType)
                    .withIsEbsVolumeEncrypted(ebsVolumeEncrypted)
                    .withEbsVolumeSize(ebsVolumeSize)
                    .withEbsDiskIOPS(ebsDiskIOPS)
                    .withRegionName(awsRegionName))
            .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(completionDate)
            .setIsGeoSharded(true));
  }

  private ObjectId addAwsSnapshot(
      final SnapshotUpdate update,
      final AwsSnapshotFieldBuilder awsBuilder,
      final Date initiationDate,
      final Optional<ObjectId> projectId,
      final Optional<String> regionName,
      final Optional<ObjectId> clusterId) {
    return addAwsSnapshot(
        update, awsBuilder, initiationDate, projectId, regionName, clusterId, Optional.empty());
  }

  private ObjectId addAwsSnapshot(
      final SnapshotUpdate update,
      final AwsSnapshotFieldBuilder awsBuilder,
      final Date initiationDate,
      final Optional<ObjectId> projectId,
      final Optional<String> regionName,
      final Optional<ObjectId> clusterId,
      final Optional<String> pEbsVolumeId) {
    final String awsRegionName = regionName.orElse(AWSRegionName.US_EAST_1.getName());
    final ObjectId awsAccountId = new ObjectId();
    final ObjectId awsContainerId = new ObjectId();
    final String awsSubnetId = "awsSubnetId-1";
    final String ebsVolumeId = pEbsVolumeId.orElse("ebsVolumeId-1");
    final String ebsSnapshotDesc = AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId);
    final String ebsVolumeType = VolumeType.Io1.name();
    final boolean ebsVolumeEncrypted = true;
    final Integer ebsVolumeSize = AWSNDSInstanceSize.M30.getDefaultDiskSizeGB();
    final Integer ebsDiskIOPS = AWSNDSInstanceSize.M30.getMaxEBSStandardIOPS();

    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(new ObjectId())
            .setProjectId(projectId.orElse(PROJECT_ID))
            .setClusterName(CLUSTER_NAME)
            .setDeploymentClusterName(CLUSTER_NAME)
            .setClusterUniqueId(clusterId.orElse(CLUSTER_UNIQUE_ID))
            .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
            .setSnapshotInitiationDate(initiationDate)
            .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
            .setPurged(false)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(USED_SPACE)
            .setCloudProviders(List.of(CloudProvider.AWS))
            .setAwsSnapshotField(
                new AwsSnapshotFieldBuilder()
                    .withEbsSnapshotDescription(ebsSnapshotDesc)
                    .withAWSAccountId(awsAccountId)
                    .withAWSContainerId(awsContainerId)
                    .withAWSSubnetId(awsSubnetId)
                    .withEbsVolumeId(ebsVolumeId)
                    .withEbsVolumeType(ebsVolumeType)
                    .withIsEbsVolumeEncrypted(ebsVolumeEncrypted)
                    .withEbsVolumeSize(ebsVolumeSize)
                    .withEbsDiskIOPS(ebsDiskIOPS)
                    .withRegionName(awsRegionName)
                    .withBuilder(awsBuilder))
            .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(new Date())
            .withSnapshotUpdate(update));
  }

  private ObjectId addAwsSnapshot(
      SnapshotUpdate update, AwsSnapshotFieldBuilder awsBuilder, Date initiationDate) {
    return addAwsSnapshot(
        update, awsBuilder, initiationDate, Optional.empty(), Optional.empty(), Optional.empty());
  }

  private ObjectId addAwsSnapshot(
      SnapshotUpdate update,
      AwsSnapshotFieldBuilder awsBuilder,
      Optional<ObjectId> projectId,
      Optional<String> regionName,
      Optional<ObjectId> clusterId) {
    return addAwsSnapshot(update, awsBuilder, new Date(), projectId, regionName, clusterId);
  }

  private ObjectId addAwsSnapshot(SnapshotUpdate update, AwsSnapshotFieldBuilder awsBuilder) {
    return addAwsSnapshot(update, awsBuilder, new Date());
  }

  private ObjectId addAzureSnapshot(AzureSnapshotFieldBuilder azureBuilder) {
    return addAzureSnapshot(new SnapshotUpdate(), azureBuilder, PROJECT_ID);
  }

  private ObjectId addAzureSnapshot(
      AzureSnapshotFieldBuilder azureBuilder, final ObjectId projectId) {
    return addAzureSnapshot(new SnapshotUpdate(), azureBuilder, projectId);
  }

  private ObjectId addAzureSnapshot(
      final SnapshotUpdate pSnapshotUpdate, final AzureSnapshotFieldBuilder pAzureBuilder) {
    return addAzureSnapshot(pSnapshotUpdate, pAzureBuilder, PROJECT_ID);
  }

  private ObjectId addAzureSnapshot(
      final SnapshotUpdate pSnapshotUpdate,
      final AzureSnapshotFieldBuilder pAzureBuilder,
      final ObjectId pProjectId) {
    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(new ObjectId())
            .setProjectId(pProjectId)
            .setClusterName(CLUSTER_NAME)
            .setDeploymentClusterName(CLUSTER_NAME)
            .setRsId(RS_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
            .setSnapshotInitiationDate(SNAPSHOT_INITIATION_DATE)
            .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(USED_SPACE)
            .setCloudProviders(List.of(CloudProvider.AZURE))
            .setAzureSnapshotField(
                new AzureSnapshotFieldBuilder()
                    .withDiskType(AzureDiskType.P10.name())
                    .withSubscriptionId(SUBSCRIPTION_ID)
                    .withResourceGroup(RESOURCE_GROUP)
                    .withBuilder(pAzureBuilder))
            .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(new Date())
            .withSnapshotUpdate(pSnapshotUpdate));
  }

  private ObjectId addGcpSnapshot(final String regionName) {
    return addGcpSnapshot(
        new SnapshotUpdate(), new GcpSnapshotFieldBuilder().withRegionName(regionName));
  }

  private ObjectId addGcpSnapshot(
      final SnapshotUpdate pSnapshotUpdate, final GcpSnapshotFieldBuilder pGcpBuilder) {
    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(new ObjectId())
            .setProjectId(PROJECT_ID)
            .setClusterName(CLUSTER_NAME)
            .setDeploymentClusterName(CLUSTER_NAME)
            .setRsId(RS_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
            .setSnapshotInitiationDate(SNAPSHOT_INITIATION_DATE)
            .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(USED_SPACE)
            .setCloudProviders(List.of(CloudProvider.GCP))
            .setGcpSnapshotField(
                new GcpSnapshotFieldBuilder()
                    .withSourceDiskName("sourceDisk")
                    .withSnapshotDiskSizeGb(100L)
                    .withBuilder(pGcpBuilder))
            .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .withSnapshotUpdate(pSnapshotUpdate));
  }

  private ObjectId addSLSSnapshot(
      final SnapshotUpdate pSnapshotUpdate, final SLSSnapshotFieldBuilder builder) {
    return _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(new ObjectId())
            .setProjectId(PROJECT_ID)
            .setClusterName(CLUSTER_NAME)
            .setDeploymentClusterName(CLUSTER_NAME)
            .setRsId(RS_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setScheduledCreationDate(SCHEDULED_CREATION_DATE)
            .setSnapshotInitiationDate(SNAPSHOT_INITIATION_DATE)
            .setScheduledDeletionDate(SCHEDULED_DELETION_DATE)
            .setDeleted(false)
            .setPurged(false)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setCloudProviders(List.of(CloudProvider.AWS))
            .setDisaggregatedStorageSystem(true)
            .setSLSSnapshotField(
                new SLSSnapshotFieldBuilder()
                    .withClustershotId(SLS_BACKUP_CLUSTERSHOT_ID)
                    .withSnapshotId(SLS_BACKUP_SNAPSHOT_ID)
                    .withRegionName(AWSRegionName.US_EAST_1.getName())
                    .withDisaggregatedStorageConfig(
                        new DisaggregatedStorageConfig(
                            new BasicDBObject()
                                .append(
                                    DisaggregatedStorageConfig.FieldDefs.SLS_LOG_ID,
                                    SLS_BACKUP_LOG_ID)
                                .append(
                                    DisaggregatedStorageConfig.FieldDefs.SLS_CELLS,
                                    new BasicDBList())
                                .append(
                                    DisaggregatedStorageConfig.FieldDefs.SLS_LOG_SERVERS,
                                    new BasicDBList())))
                    .withBuilder(builder))
            .setEncryptionDetails(AWS_ENCRYPT_CREDENTIALS)
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setOverrideRetentionPolicy(false)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .withSnapshotUpdate(pSnapshotUpdate));
  }

  private void addShardedClusterSnapshot(
      final ObjectId clusterSnapshotId,
      final Map<ObjectId, String> members,
      final BackupSnapshot.Status pStatus,
      Type scheduled) {
    _backupSnapshotDao.addShardedClusterBackupSnapshot(
        clusterSnapshotId,
        PROJECT_ID,
        CLUSTER_NAME,
        CLUSTER_NAME,
        CLUSTER_UNIQUE_ID,
        SCHEDULED_CREATION_DATE,
        SNAPSHOT_INITIATION_DATE,
        SCHEDULED_DELETION_DATE,
        NDSModelTestFactory.TEST_MONGODB_VERSION,
        NDSModelTestFactory.TEST_MONGODB_VERSION,
        0L,
        List.of(CloudProvider.AWS),
        AWS_ENCRYPT_CREDENTIALS,
        members,
        scheduled,
        null,
        pStatus,
        null,
        BackupFrequencyType.DAILY,
        Collections.emptyList(),
        null,
        BackupRetentionUnit.DAYS,
        ConfigServerType.DEDICATED,
        BackupSnapshot.PlanningType.BLOCKING,
        null,
        null,
        false);
  }

  private void updateSnapshotWithStatus(final ObjectId snapshotId, BackupSnapshot.Status status) {
    final SnapshotUpdate updatedSnapshot =
        new SnapshotUpdate().setSnapshotCompletionDate(new Date()).setStatus(status);
    _backupSnapshotDao.updateBackupSnapshot(snapshotId, updatedSnapshot);
  }

  private void updateShardedClusterSnapshotCompleted(
      final ObjectId shardedClusterId, final String rsId) {
    _backupSnapshotDao.updateShardedClusterSnapshotWithMemberData(
        shardedClusterId, new ObjectId(), rsId, null);

    _backupSnapshotDao.updateShardedClusterSnapshotWithEncryptionDetails(
        shardedClusterId, AWS_ENCRYPT_CREDENTIALS);

    _backupSnapshotDao.markCompleted(
        shardedClusterId, SCHEDULED_DELETION_DATE, new Date(), BackupRetentionUnit.DAYS);
  }

  private void updateSnapshotWithWtcMetadata(final ObjectId pSnapshotId) {
    final Long cursorId = 123456789412412L;
    final String uuid = "uuid";
    final String dbPath = "dbPath";
    final BSONTimestamp oplogStart = new BSONTimestamp(100, 1);
    final BSONTimestamp oplogEnd = new BSONTimestamp(200, 1);
    final BSONTimestamp checkpointTimeStamp = new BSONTimestamp(300, 1);
    _backupSnapshotDao.updateSnapshotWithWtcMetadata(
        pSnapshotId,
        new CpsBackupCursorMetadata(
            cursorId, uuid, dbPath, oplogStart, oplogEnd, checkpointTimeStamp));
    final ReplicaSetBackupSnapshot snap =
        (ReplicaSetBackupSnapshot) _backupSnapshotDao.findById(pSnapshotId).get();

    assertEquals(cursorId, snap.getCpsWtCheckpoint().getCursorId());
    assertEquals(uuid, snap.getCpsWtCheckpoint().getBackupId());
    assertEquals(dbPath, snap.getCpsWtCheckpoint().getDbPath());
    assertEquals(oplogStart, snap.getCpsWtCheckpoint().getOplogStart());
    assertEquals(oplogEnd, snap.getCpsWtCheckpoint().getOplogEnd());
    assertEquals(checkpointTimeStamp, snap.getCpsWtCheckpoint().getCheckpointTimestamp());
  }

  @Test
  public void testFindMostRecentSnapshotsByClusterShardedTypeAndStatus_ReplicaSet() {
    // setup snapshots - replica sets
    addAwsSnapshot(
        new SnapshotUpdate()
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setPitSentinelOptime(new BSONTimestamp(7, 1))
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("test"));

    addAwsSnapshot(
        new SnapshotUpdate()
            .setShard(false)
            .setStatus(Status.IN_PROGRESS)
            .setType(Type.SCHEDULED)
            .setPitSentinelOptime(new BSONTimestamp(7, 1))
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("test"));

    addAwsSnapshot(
        new SnapshotUpdate()
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setPitSentinelOptime(new BSONTimestamp(7, 1))
            .setDeleted(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("test"));

    addAwsSnapshot(
        new SnapshotUpdate()
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setPitSentinelOptime(new BSONTimestamp(7, 1))
            .setDeleted(true),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("test"));

    addAwsSnapshot(
        new SnapshotUpdate()
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setPitSentinelOptime(new BSONTimestamp(7, 1))
            .setDeleted(false)
            .setPurged(true),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("test"));

    addAwsSnapshot(
        new SnapshotUpdate()
            .setShard(false)
            .setStatus(Status.COMPLETED)
            .setType(Type.ON_DEMAND)
            .setPitSentinelOptime(new BSONTimestamp(7, 1))
            .setDeleted(false)
            .setPurged(true),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("test"));

    // Invalid projectId - no snapshots found
    assertTrue(
        _backupSnapshotDao
            .findMostRecentSnapshotsByClusterShardedTypeAndStatus(
                ObjectId.get(), CLUSTER_UNIQUE_ID, false, Type.SCHEDULED, Status.COMPLETED, 10)
            .isEmpty());

    // Invalid clusterUniqueId - no snapshots found
    assertTrue(
        _backupSnapshotDao
            .findMostRecentSnapshotsByClusterShardedTypeAndStatus(
                PROJECT_ID, ObjectId.get(), false, Type.SCHEDULED, Status.COMPLETED, 10)
            .isEmpty());

    // Test correct status and type found - limit > results
    {
      final List<BackupSnapshot> snapshots =
          _backupSnapshotDao.findMostRecentSnapshotsByClusterShardedTypeAndStatus(
              PROJECT_ID, CLUSTER_UNIQUE_ID, false, Type.SCHEDULED, Status.COMPLETED, 10);
      assertEquals(2, snapshots.size());

      snapshots.forEach(
          s -> {
            assertFalse(s.getDeleted());
            assertFalse(s.getPurged());
            assertEquals(Type.SCHEDULED, s.getSnapshotType());
            assertEquals(Status.COMPLETED, s.getSnapshotStatus());
            assertFalse(s instanceof ShardedClusterBackupSnapshot);
          });
    }

    // Test correct status and type found - limit < results
    {
      final List<BackupSnapshot> snapshots =
          _backupSnapshotDao.findMostRecentSnapshotsByClusterShardedTypeAndStatus(
              PROJECT_ID, CLUSTER_UNIQUE_ID, false, Type.SCHEDULED, Status.COMPLETED, 1);
      assertEquals(1, snapshots.size());

      snapshots.forEach(
          s -> {
            assertFalse(s.getDeleted());
            assertFalse(s.getPurged());
            assertEquals(Type.SCHEDULED, s.getSnapshotType());
            assertEquals(Status.COMPLETED, s.getSnapshotStatus());
            assertFalse(s instanceof ShardedClusterBackupSnapshot);
          });
    }
  }

  @Test
  public void testFindMostRecentSnapshotsByClusterShardedTypeAndStatus_ShardedCluster() {
    // This test covers the basic scenario of ensuring that only the sharded cluster snapshot is
    // returned for a sharded cluster.  All other query scenarios are covered in
    // testFindMostRecentSnapshotsByClusterShardedTypeAndStatus_ReplicaSet

    // add shard snapshots
    final ObjectId shard0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-0")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId shard1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-shard-1")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    final ObjectId config0 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(CLUSTER_NAME + "-config-0")
                .setShard(true)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName1"));

    // add a sharded cluster snapshot
    final ObjectId shardedClusterSnapshotId = new ObjectId();
    final Map<ObjectId, String> members = new HashMap<>();
    members.put(shard0, CLUSTER_NAME + "-shard-0");
    members.put(shard1, CLUSTER_NAME + "-shard-1");
    members.put(config0, CLUSTER_NAME + "-config-0");
    addShardedClusterSnapshot(shardedClusterSnapshotId, members, Status.COMPLETED, Type.SCHEDULED);

    // Test correct status and type found
    {
      final List<BackupSnapshot> snapshots =
          _backupSnapshotDao.findMostRecentSnapshotsByClusterShardedTypeAndStatus(
              PROJECT_ID, CLUSTER_UNIQUE_ID, true, Type.SCHEDULED, Status.COMPLETED, 10);
      assertEquals(1, snapshots.size());

      snapshots.forEach(
          s -> {
            assertFalse(s.getDeleted());
            assertFalse(s.getPurged());
            assertEquals(Type.SCHEDULED, s.getSnapshotType());
            assertEquals(Status.COMPLETED, s.getSnapshotStatus());
            assertTrue(s instanceof ShardedClusterBackupSnapshot);
          });
    }
  }

  private void setupLeakedItemProviderSnapshots() {
    addAwsSnapshot(
        new SnapshotUpdate().setId(oid(0)).setShard(false).setPurged(false),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("awsReplicaSet"));
    addAwsSnapshot(
        new SnapshotUpdate().setId(oid(1)).setShard(false).setPurged(true),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("awsReplicaSetPurged"));
    // Sharded snapshots do not have ebsSnapshotId
    addAwsSnapshot(
        new SnapshotUpdate()
            .setId(oid(2))
            .setShard(true)
            .setSnapshotMembers(List.of())
            .setPurged(false),
        new AwsSnapshotFieldBuilder());
    addAwsSnapshot(
        new SnapshotUpdate()
            .setId(oid(3))
            .setShard(true)
            .setSnapshotMembers(List.of())
            .setPurged(true),
        new AwsSnapshotFieldBuilder());

    addAzureSnapshot(
        new SnapshotUpdate().setId(oid(10)).setShard(false).setPurged(false),
        new AzureSnapshotFieldBuilder().withSnapshotName("azureReplicaSet"));
    addAzureSnapshot(
        new SnapshotUpdate().setId(oid(11)).setShard(false).setPurged(true),
        new AzureSnapshotFieldBuilder().withSnapshotName("azureReplicaSetPurged"));
    // Sharded snapshots do not have snapshotName
    addAzureSnapshot(
        new SnapshotUpdate()
            .setId(oid(12))
            .setShard(true)
            .setSnapshotMembers(List.of())
            .setPurged(false),
        new AzureSnapshotFieldBuilder());
    addAzureSnapshot(
        new SnapshotUpdate()
            .setId(oid(13))
            .setShard(true)
            .setSnapshotMembers(List.of())
            .setPurged(true),
        new AzureSnapshotFieldBuilder());

    addGcpSnapshot(
        new SnapshotUpdate().setId(oid(20)).setShard(false).setPurged(false),
        new GcpSnapshotFieldBuilder().withSnapshotName("gcpReplicaSet"));
    addGcpSnapshot(
        new SnapshotUpdate().setId(oid(21)).setShard(false).setPurged(true),
        new GcpSnapshotFieldBuilder().withSnapshotName("gcpReplicaSetPurged"));
    // Sharded snapshots do not have snapshotName
    addGcpSnapshot(
        new SnapshotUpdate()
            .setId(oid(22))
            .setShard(true)
            .setSnapshotMembers(List.of())
            .setPurged(false),
        new GcpSnapshotFieldBuilder());
    addGcpSnapshot(
        new SnapshotUpdate()
            .setId(oid(23))
            .setShard(true)
            .setSnapshotMembers(List.of())
            .setPurged(true),
        new GcpSnapshotFieldBuilder());
  }

  @Test
  public void findByIdUnshardedProviderSnapshotId() {
    setupLeakedItemProviderSnapshots();

    assertEquals(
        Optional.of("awsReplicaSet"),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(0), AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(1), AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(2), AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(3), AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID));

    assertEquals(
        Optional.of("azureReplicaSet"),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(10), AzureBackupSnapshot.FieldDefs.NAME));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(11), AzureBackupSnapshot.FieldDefs.NAME));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(12), AzureBackupSnapshot.FieldDefs.NAME));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(13), AzureBackupSnapshot.FieldDefs.NAME));

    assertEquals(
        Optional.of("gcpReplicaSet"),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(20), GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(21), GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(22), GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME));
    assertEquals(
        Optional.empty(),
        _backupSnapshotDao.findByIdUnshardedProviderSnapshotId(
            oid(23), GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME));
  }

  @Test
  public void testFindByClusterUniqueIdUnshardedProviderSnapshotId() {
    setupLeakedItemProviderSnapshots();

    assertEquals(
        Set.of("awsReplicaSet"),
        _backupSnapshotDao.findByClusterUniqueIdUnshardedProviderSnapshotId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID));

    assertEquals(
        Set.of("azureReplicaSet"),
        _backupSnapshotDao.findByClusterUniqueIdUnshardedProviderSnapshotId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, AzureBackupSnapshot.FieldDefs.NAME));

    assertEquals(
        Set.of("gcpReplicaSet"),
        _backupSnapshotDao.findByClusterUniqueIdUnshardedProviderSnapshotId(
            PROJECT_ID, CLUSTER_UNIQUE_ID, GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME));
  }

  @Test
  public void testFindUnshardedProviderSnapshotIdsForProjectAndCloudProvider() {
    setupLeakedItemProviderSnapshots();

    assertEquals(
        Set.of("awsReplicaSet"),
        _backupSnapshotDao.findUnshardedProviderSnapshotIdsForProjectAndCloudProvider(
            PROJECT_ID, CloudProvider.AWS, AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID));

    assertEquals(
        Set.of("azureReplicaSet"),
        _backupSnapshotDao.findUnshardedProviderSnapshotIdsForProjectAndCloudProvider(
            PROJECT_ID, CloudProvider.AZURE, AzureBackupSnapshot.FieldDefs.NAME));

    assertEquals(
        Set.of("gcpReplicaSet"),
        _backupSnapshotDao.findUnshardedProviderSnapshotIdsForProjectAndCloudProvider(
            PROJECT_ID, CloudProvider.GCP, GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME));
  }

  @Test
  public void testSaveCustomerCollectionMetadataConfig() {
    final ObjectId snapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final String bucketName = "testBucket";
    final String key = "key";
    final CollectionMetadataConfig collectionMetadataConfig =
        new CollectionMetadataConfig.CollectionMetadataConfigBuilder()
            .setCloudProvider(cloudProvider)
            .setRegionName(regionName)
            .setBucketName(bucketName)
            .setKey(key)
            .setStatus(CollectionMetadataConfig.Status.NOT_STARTED)
            .build();
    _backupSnapshotDao.saveCustomerCollectionMetadataConfig(snapshotId, collectionMetadataConfig);
    final ReplicaSetBackupSnapshot snap =
        (ReplicaSetBackupSnapshot) _backupSnapshotDao.findById(snapshotId).get();
    assertEquals(collectionMetadataConfig, snap.getCollectionMetadataConfig());
  }

  @Test
  public void testMarkCustomerCollectionMetadataConfigUploaded() {
    final ObjectId snapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final String bucketName = "testBucket";
    final String key = "key";
    final CollectionMetadataConfig collectionMetadataConfig =
        new CollectionMetadataConfig.CollectionMetadataConfigBuilder()
            .setCloudProvider(cloudProvider)
            .setRegionName(regionName)
            .setBucketName(bucketName)
            .setKey(key)
            .setStatus(CollectionMetadataConfig.Status.COMPLETED)
            .build();

    _backupSnapshotDao.saveCustomerCollectionMetadataConfig(snapshotId, collectionMetadataConfig);

    final Instant uploadedDate = Instant.now();
    _backupSnapshotDao.markCustomerCollectionMetadataConfigUploaded(snapshotId, uploadedDate);

    final CollectionMetadataConfig collectionMetadataConfigUploaded =
        new CollectionMetadataConfig.CollectionMetadataConfigBuilder()
            .setCloudProvider(cloudProvider)
            .setRegionName(regionName)
            .setBucketName(bucketName)
            .setKey(key)
            .setStatus(CollectionMetadataConfig.Status.COMPLETED)
            .setUploadedDate(uploadedDate)
            .build();

    final ReplicaSetBackupSnapshot snap =
        (ReplicaSetBackupSnapshot) _backupSnapshotDao.findById(snapshotId).get();
    assertEquals(collectionMetadataConfigUploaded, snap.getCollectionMetadataConfig());
  }

  @Test
  public void testMarkCustomerCollectionMetadataConfigPurged() {
    final ObjectId snapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId(RS_ID)
                .setShard(false)
                .setStatus(Status.COMPLETED)
                .setType(Type.SCHEDULED)
                .setDeleted(true),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId(SNAPSHOT_NAME));
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final String bucketName = "testBucket";
    final String key = "key";
    final Instant uploadedDate = Instant.now();
    final CollectionMetadataConfig collectionMetadataConfig =
        new CollectionMetadataConfig.CollectionMetadataConfigBuilder()
            .setCloudProvider(cloudProvider)
            .setRegionName(regionName)
            .setBucketName(bucketName)
            .setKey(key)
            .setStatus(CollectionMetadataConfig.Status.PURGED)
            .setUploadedDate(uploadedDate)
            .build();

    _backupSnapshotDao.saveCustomerCollectionMetadataConfig(snapshotId, collectionMetadataConfig);

    final Instant purgedDate = Instant.now();
    _backupSnapshotDao.markCustomerCollectionMetadataConfigPurged(snapshotId, purgedDate);

    final CollectionMetadataConfig purgedCollectionMetadataConfig =
        new CollectionMetadataConfig.CollectionMetadataConfigBuilder()
            .setCloudProvider(cloudProvider)
            .setRegionName(regionName)
            .setBucketName(bucketName)
            .setKey(key)
            .setUploadedDate(uploadedDate)
            .setPurgedDate(purgedDate)
            .setStatus(CollectionMetadataConfig.Status.PURGED)
            .build();

    final ReplicaSetBackupSnapshot snap =
        (ReplicaSetBackupSnapshot) _backupSnapshotDao.findById(snapshotId).get();
    assertEquals(purgedCollectionMetadataConfig, snap.getCollectionMetadataConfig());
  }

  @Test
  public void testGetIncompleteSnapshotsByTenantId() {
    final ObjectId inProgressSnapshotId =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setRsId("rsId1")
                .setShard(false)
                .setStatus(Status.IN_PROGRESS)
                .setType(Type.SCHEDULED)
                .setDeleted(false),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snapshotName4"));

    _backupSnapshotDao.setQueuedSnapshotToInProgress(inProgressSnapshotId, new ObjectId());

    final ObjectId tenantUniqueId1 = new ObjectId();
    final ObjectId tenantUniqueId2 = new ObjectId();

    // return nothing before tenants are added to snapshot
    assertTrue(_backupSnapshotDao.getIncompleteSnapshotsByTenantId(tenantUniqueId1).isEmpty());
    assertTrue(_backupSnapshotDao.getIncompleteSnapshotsByTenantId(tenantUniqueId2).isEmpty());
    assertTrue(_backupSnapshotDao.getIncompleteSnapshotsByTenantId(new ObjectId()).isEmpty());

    List<ServerlessTenant> tenants =
        List.of(tenantUniqueId1, tenantUniqueId2).stream()
            .map((id) -> new ServerlessTenant(id, -1))
            .collect(Collectors.toList());
    _backupSnapshotDao.setMtmTenants(inProgressSnapshotId, tenants);

    // return in progress backup snapshot for linked tenants
    assertFalse(_backupSnapshotDao.getIncompleteSnapshotsByTenantId(tenantUniqueId1).isEmpty());
    assertFalse(_backupSnapshotDao.getIncompleteSnapshotsByTenantId(tenantUniqueId2).isEmpty());
    // still nothing gets returned for random objectId
    assertTrue(_backupSnapshotDao.getIncompleteSnapshotsByTenantId(new ObjectId()).isEmpty());
  }

  @Test
  public void testFindByGroupId() {
    final ObjectId testGroupId = new ObjectId();
    final ObjectId otherGroupId = new ObjectId();
    final ObjectId snap1 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(testGroupId)
                .setDeleted(false)
                .setShard(false)
                .setStatus(BackupSnapshot.Status.COMPLETED)
                .setPurged(false)
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setClusterName("test-cluster")
                .setClusterUniqueId(new ObjectId())
                .setFrequencyType(BackupFrequencyType.DAILY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap1"));
    final ObjectId snap2 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(testGroupId)
                .setDeleted(false)
                .setShard(false)
                .setStatus(BackupSnapshot.Status.COMPLETED)
                .setPurged(false)
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setClusterName("test-cluster")
                .setClusterUniqueId(new ObjectId())
                .setFrequencyType(BackupFrequencyType.DAILY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap2"));
    final ObjectId snap3 =
        addAwsSnapshot(
            new SnapshotUpdate()
                .setProjectId(testGroupId)
                .setDeleted(false)
                .setShard(false)
                .setStatus(BackupSnapshot.Status.COMPLETED)
                .setPurged(false)
                .setType(BackupSnapshot.Type.SCHEDULED)
                .setClusterName("test-cluster")
                .setClusterUniqueId(new ObjectId())
                .setFrequencyType(BackupFrequencyType.DAILY),
            new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap3"));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(otherGroupId)
            .setDeleted(false)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setPurged(false)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setClusterName("other-cluster")
            .setClusterUniqueId(new ObjectId())
            .setFrequencyType(BackupFrequencyType.DAILY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("other-group"));
    final List<BackupSnapshot> found = _backupSnapshotDao.findByGroupId(testGroupId, 2);
    assertEquals(2, found.size());
    assertTrue(found.stream().allMatch(s -> testGroupId.equals(s.getProjectId())));
  }

  @Test
  public void testFindByCluster() {
    final ObjectId testGroupId = new ObjectId();
    final ObjectId testClusterUniqueId = new ObjectId();
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(testGroupId)
            .setDeleted(false)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setPurged(false)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setClusterName("test-cluster")
            .setClusterUniqueId(testClusterUniqueId)
            .setFrequencyType(BackupFrequencyType.DAILY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap1"));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(testGroupId)
            .setDeleted(false)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setPurged(false)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setClusterName("test-cluster")
            .setClusterUniqueId(testClusterUniqueId)
            .setFrequencyType(BackupFrequencyType.DAILY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap2"));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(testGroupId)
            .setDeleted(false)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setPurged(false)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setClusterName("test-cluster")
            .setClusterUniqueId(testClusterUniqueId)
            .setFrequencyType(BackupFrequencyType.DAILY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("snap3"));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(testGroupId)
            .setDeleted(false)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setPurged(false)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setClusterName("other-cluster-in-group")
            .setClusterUniqueId(new ObjectId())
            .setFrequencyType(BackupFrequencyType.DAILY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("other-cluster"));
    addAwsSnapshot(
        new SnapshotUpdate()
            .setProjectId(new ObjectId())
            .setDeleted(false)
            .setShard(false)
            .setStatus(BackupSnapshot.Status.COMPLETED)
            .setPurged(false)
            .setType(BackupSnapshot.Type.SCHEDULED)
            .setClusterName("other-cluster")
            .setClusterUniqueId(new ObjectId())
            .setFrequencyType(BackupFrequencyType.DAILY),
        new AwsSnapshotFieldBuilder().withEbsSnapshotId("other-group"));
    final List<BackupSnapshot> found =
        _backupSnapshotDao.findByCluster(testGroupId, testClusterUniqueId, 10);
    assertEquals(3, found.size());
    assertTrue(found.stream().allMatch(s -> testGroupId.equals(s.getProjectId())));
    assertTrue(found.stream().allMatch(s -> testClusterUniqueId.equals(s.getClusterUniqueId())));
    final List<BackupSnapshot> foundRestricted =
        _backupSnapshotDao.findByCluster(testGroupId, testClusterUniqueId, 2);
    assertEquals(2, foundRestricted.size());
  }
}
