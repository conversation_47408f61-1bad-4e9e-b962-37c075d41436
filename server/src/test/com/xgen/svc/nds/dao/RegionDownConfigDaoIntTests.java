package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.cps.backupjob._private.dao.RegionDownConfigDao;
import com.xgen.cloud.cps.backupjob._public.model.RegionDownConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.Optional;
import org.junit.jupiter.api.Test;

public class RegionDownConfigDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private RegionDownConfigDao _regionDownConfigDao;

  private CloudProvider _cloudProvider1 = CloudProvider.AWS;
  private CloudProvider _cloudProvider2 = CloudProvider.AZURE;

  private String _regionName1 = "us-east-1";
  private String _regionName2 = "us-east-2";

  private Date _markedDate = new Date();

  @Test
  public void testCreateAndFind() {

    final RegionDownConfig config1 = getRegionDownConfig1();
    assertTrue(
        _regionDownConfigDao
            .findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName1)
            .isEmpty());

    _regionDownConfigDao.create(config1);
    Optional<RegionDownConfig> result1 =
        _regionDownConfigDao.findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName1);
    assertNotNull(result1);
    assertEquals(result1.get().getCloudProvider(), _cloudProvider1);
    assertEquals(result1.get().getRegionName(), _regionName1);

    final RegionDownConfig config2 = getRegionDownConfig2();
    assertTrue(
        _regionDownConfigDao
            .findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName2)
            .isEmpty());

    _regionDownConfigDao.create(config2);
    Optional<RegionDownConfig> result2 =
        _regionDownConfigDao.findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName2);
    assertNotNull(result2);
    assertEquals(result2.get().getCloudProvider(), _cloudProvider1);
    assertNotEquals(result2.get().getRegionName(), _regionName1);
    assertEquals(result2.get().getRegionName(), _regionName2);
  }

  @Test
  public void testDelete() {
    final RegionDownConfig config1 = getRegionDownConfig1();
    _regionDownConfigDao.create(config1);

    final RegionDownConfig config2 = getRegionDownConfig2();
    _regionDownConfigDao.create(config2);

    final RegionDownConfig config3 = getRegionDownConfig3();
    _regionDownConfigDao.create(config3);

    Optional<RegionDownConfig> result1 =
        _regionDownConfigDao.findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName1);
    assertNotNull(result1);

    _regionDownConfigDao.delete(_cloudProvider1, _regionName1);

    Optional<RegionDownConfig> result2 =
        _regionDownConfigDao.findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName1);
    assertTrue(result2.isEmpty());

    Optional<RegionDownConfig> result3 =
        _regionDownConfigDao.findConfigByCloudProviderAndRegionName(_cloudProvider1, _regionName2);
    assertFalse(result3.isEmpty());

    Optional<RegionDownConfig> result4 =
        _regionDownConfigDao.findConfigByCloudProviderAndRegionName(_cloudProvider2, _regionName1);
    assertFalse(result4.isEmpty());
  }

  private RegionDownConfig getRegionDownConfig1() {
    return RegionDownConfig.Builder.aRegionDownConfig()
        .cloudProvider(_cloudProvider1)
        .regionName(_regionName1)
        .markedDate(_markedDate)
        .author("John.Doe")
        .reason("HELP-222")
        .build();
  }

  private RegionDownConfig getRegionDownConfig2() {
    return RegionDownConfig.Builder.aRegionDownConfig()
        .cloudProvider(_cloudProvider1)
        .regionName(_regionName2)
        .markedDate(_markedDate)
        .author("John.Doe")
        .reason("HELP-222")
        .build();
  }

  private RegionDownConfig getRegionDownConfig3() {
    return RegionDownConfig.Builder.aRegionDownConfig()
        .cloudProvider(_cloudProvider2)
        .regionName(_regionName1)
        .markedDate(_markedDate)
        .author("John.Doe")
        .reason("HELP-222")
        .build();
  }
}
