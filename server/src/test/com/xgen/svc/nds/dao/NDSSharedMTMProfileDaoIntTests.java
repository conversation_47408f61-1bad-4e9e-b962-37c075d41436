package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoWriteException;
import com.mongodb.client.result.DeleteResult;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSMTMProfile;
import com.xgen.cloud.nds.free._private.dao.NDSSharedMTMProfileDao;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.NDSSharedMTMProfile;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSSharedMTMProfileDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private AppSettings _appSettings;
  @Inject private NDSSharedMTMProfileDao _ndsSharedMTMProfileDao;

  private final List<NDSSharedMTMProfile> _sharedMTMProfiles =
      NDSModelTestFactory.getNDSMTMProfilesForTest();

  @BeforeEach
  public void setup() throws Exception {
    _appSettings.setProp(
        "nds.instances.shared.version",
        NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString(),
        AppSettings.SettingType.MEMORY);
    _ndsSharedMTMProfileDao.insertManyReplicaSafe(_sharedMTMProfiles);
  }

  @Test
  public void testFindByIdForAutoScalingUpdate() {
    final NDSSharedMTMProfile awsUsEast1M0MtmProfile =
        _sharedMTMProfiles.stream()
            .filter(
                ndsSharedMTMProfile ->
                    ndsSharedMTMProfile.getCloudProvider().equals(CloudProvider.AWS)
                        && ndsSharedMTMProfile.getRegionName().equals(AWSRegionName.US_EAST_1)
                        && ndsSharedMTMProfile.getTenantInstanceSize().equals(FreeInstanceSize.M0))
            .findFirst()
            .get();

    final Optional<NDSSharedMTMProfile> profile =
        _ndsSharedMTMProfileDao.findByIdForAutoScalingUpdate(
            awsUsEast1M0MtmProfile.getId(), AWSNDSInstanceSize.M20.name());

    assertEquals(AWSNDSInstanceSize.M30.name(), profile.get().getAutoScaleMinSize().name());
  }

  @Test
  public void testFindByIdForAutoScalingUpdate_notFound() {
    final NDSSharedMTMProfile awsUsEast1M0MtmProfile =
        _sharedMTMProfiles.stream()
            .filter(
                ndsSharedMTMProfile ->
                    ndsSharedMTMProfile.getCloudProvider().equals(CloudProvider.AWS)
                        && ndsSharedMTMProfile.getRegionName().equals(AWSRegionName.US_EAST_1)
                        && ndsSharedMTMProfile.getTenantInstanceSize().equals(FreeInstanceSize.M0))
            .findFirst()
            .get();

    final Optional<NDSSharedMTMProfile> profile =
        _ndsSharedMTMProfileDao.findByIdForAutoScalingUpdate(
            awsUsEast1M0MtmProfile.getId(), AWSNDSInstanceSize.M30.name());

    assertTrue(profile.isEmpty());
  }

  @Test
  public void testUpdateAutoscaleMinimumInstanceSizeForId() {
    // Before the update
    Optional<NDSSharedMTMProfile> profile =
        _ndsSharedMTMProfileDao.findMTMProfileById(_sharedMTMProfiles.get(0).getId());
    assertEquals(AWSNDSInstanceSize.M30.name(), profile.get().getAutoScaleMinSize().name());

    _ndsSharedMTMProfileDao.updateAutoscaleMinimumInstanceSizeForId(
        _sharedMTMProfiles.get(0).getId(), AWSNDSInstanceSize.M20.name());
    profile = _ndsSharedMTMProfileDao.findMTMProfileById(_sharedMTMProfiles.get(0).getId());

    assertEquals(AWSNDSInstanceSize.M20.name(), profile.get().getAutoScaleMinSize().name());
  }

  @Test
  public void testFindByCloudProviderRegionNameAndTenantInstanceSize() {
    final List<NDSSharedMTMProfile> awsUsEast1M0MtmProfiles =
        _sharedMTMProfiles.stream()
            .filter(
                ndsSharedMTMProfile ->
                    ndsSharedMTMProfile.getCloudProvider().equals(CloudProvider.AWS)
                        && ndsSharedMTMProfile.getRegionName().equals(AWSRegionName.US_EAST_1)
                        && ndsSharedMTMProfile.getTenantInstanceSize().equals(FreeInstanceSize.M0))
            .collect(Collectors.toList());

    final List<NDSSharedMTMProfile> mtmProfiles =
        _ndsSharedMTMProfileDao.findByCloudProviderRegionNameAndTenantInstanceSize(
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            FreeInstanceSize.M0,
            AWSNDSInstanceSize.M20.name(),
            null);

    assertEquals(awsUsEast1M0MtmProfiles.size(), mtmProfiles.size());
    assertEquals(1, mtmProfiles.size());
  }

  @Test
  public void testSaveSharedMTMProfile() {
    // set state
    final List<NDSSharedMTMProfile> originalSharedMTMProfiles =
        _ndsSharedMTMProfileDao.findMTMProfiles();

    assertEquals(_sharedMTMProfiles.size(), originalSharedMTMProfiles.size());

    _ndsSharedMTMProfileDao.getCollection().deleteMany(new BasicDBObject());
    assertEquals(0, _ndsSharedMTMProfileDao.findMTMProfiles().size());

    // test profile save
    final NDSSharedMTMProfile profile = originalSharedMTMProfiles.get(0);
    saveSharedMTMProfile(profile);

    final List<NDSSharedMTMProfile> profiles = _ndsSharedMTMProfileDao.findMTMProfiles();
    assertEquals(1, profiles.size());

    final BasicDBObject profileDBObject = profile.toDBObject();
    final BasicDBObject savedProfileDBObject = profiles.get(0).toDBObject();
    profileDBObject.remove(NDSMTMProfile.FieldDefs.ID);
    savedProfileDBObject.remove(NDSMTMProfile.FieldDefs.ID);
    assertEquals(profileDBObject, savedProfileDBObject);
    assertFalse(profileDBObject.containsField(NDSMTMProfile.FieldDefs.POOL_ID));

    // test uniqueness constraints
    try {
      saveSharedMTMProfile(profile);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof MongoWriteException);
    }

    assertEquals(1, _ndsSharedMTMProfileDao.findMTMProfiles().size());
  }

  @Test
  public void testRemoveSharedMTMProfile() {
    final List<NDSSharedMTMProfile> originalSharedMTMProfiles =
        _ndsSharedMTMProfileDao.findMTMProfiles();
    assertFalse(originalSharedMTMProfiles.isEmpty());

    originalSharedMTMProfiles.forEach(
        profile -> {
          final DeleteResult deleteResult = _ndsSharedMTMProfileDao.removeMTMProfile(profile);
          assertTrue(deleteResult.wasAcknowledged() && deleteResult.getDeletedCount() == 1L);
        });

    final List<NDSSharedMTMProfile> newSharedMTMProfiles =
        _ndsSharedMTMProfileDao.findMTMProfiles();
    assertTrue(newSharedMTMProfiles.isEmpty());
  }

  @Test
  public void testFindSharedMTMProfiles() {
    assertEquals(_sharedMTMProfiles.size(), _ndsSharedMTMProfileDao.findMTMProfiles().size());
  }

  @Test
  public void testFindSharedMTMProfile() {
    assertFalse(
        _ndsSharedMTMProfileDao
            .findMTMProfile(
                FreeInstanceSize.M0,
                AzureRegionName.CHINA_NORTH,
                NDSSettings.getNewSharedInstanceVersion(_appSettings))
            .isPresent());
    assertTrue(
        _ndsSharedMTMProfileDao
            .findMTMProfile(
                FreeInstanceSize.M0,
                AWSRegionName.US_EAST_1,
                NDSSettings.getNewSharedInstanceVersion(_appSettings))
            .isPresent());

    final NDSSharedMTMProfile profile =
        _ndsSharedMTMProfileDao
            .findMTMProfile(
                FreeInstanceSize.M0,
                AWSRegionName.US_EAST_1,
                NDSSettings.getNewSharedInstanceVersion(_appSettings))
            .get();

    assertEquals(FreeInstanceSize.M0, profile.getTenantInstanceSize());
    assertEquals(AWSRegionName.US_EAST_1, profile.getRegionName());
    assertEquals(1500, profile.getDefaultCapacity());
    assertEquals(50, profile.getLowCapacityBuffer());
    assertEquals(AWSNDSInstanceSize.M50, profile.getBackingInstanceSize());
  }

  @Test
  public void testFindAllNDSMTMProfileByTenantInstanceSize() {
    final List<NDSSharedMTMProfile> m0Profiles =
        _ndsSharedMTMProfileDao.findAllNDSMTMProfileByTenantInstanceSize(FreeInstanceSize.M0);
    final List<NDSSharedMTMProfile> m2Profiles =
        _ndsSharedMTMProfileDao.findAllNDSMTMProfileByTenantInstanceSize(FreeInstanceSize.M2);
    final List<NDSSharedMTMProfile> m5Profiles =
        _ndsSharedMTMProfileDao.findAllNDSMTMProfileByTenantInstanceSize(FreeInstanceSize.M5);

    final List<ObjectId> sharedProfileIds =
        _sharedMTMProfiles.stream().map(NDSSharedMTMProfile::getId).toList();

    assertEquals(
        _sharedMTMProfiles.stream()
            .filter(profile -> profile.getTenantInstanceSize().equals(FreeInstanceSize.M0))
            .toList()
            .size(),
        m0Profiles.size());
    m0Profiles.forEach(profile -> sharedProfileIds.contains(profile.getId()));

    assertEquals(
        _sharedMTMProfiles.stream()
            .filter(profile -> profile.getTenantInstanceSize().equals(FreeInstanceSize.M2))
            .toList()
            .size(),
        m2Profiles.size());
    m2Profiles.forEach(profile -> sharedProfileIds.contains(profile.getId()));

    assertEquals(
        _sharedMTMProfiles.stream()
            .filter(profile -> profile.getTenantInstanceSize().equals(FreeInstanceSize.M5))
            .toList()
            .size(),
        m5Profiles.size());
    m5Profiles.forEach(profile -> sharedProfileIds.contains(profile.getId()));
  }

  @Test
  public void testUpdateDefaultCapacityForId() {
    final NDSSharedMTMProfile sharedMTMProfile = _sharedMTMProfiles.get(0);
    final int newDefaultCapacity = sharedMTMProfile.getDefaultCapacity() + 100;

    _ndsSharedMTMProfileDao.updateDefaultCapacityForId(
        sharedMTMProfile.getId(), newDefaultCapacity);

    final Optional<NDSSharedMTMProfile> profileOpt =
        _ndsSharedMTMProfileDao.find(sharedMTMProfile.getId());

    assertTrue(profileOpt.isPresent());
    assertEquals(newDefaultCapacity, profileOpt.get().getDefaultCapacity());
  }

  private void saveSharedMTMProfile(final NDSMTMProfile pProfile) {
    _ndsSharedMTMProfileDao.saveMTMProfile(
        new NDSSharedMTMProfile(
            pProfile.getRegionName(),
            (FreeInstanceSize) pProfile.getTenantInstanceSize(),
            pProfile.getBackingInstanceSize(),
            pProfile.getDefaultCapacity(),
            pProfile.getLowCapacityBuffer(),
            pProfile.getAutoScaleMinSize(),
            pProfile.getMongoDBMajorVersion()));
  }
}
