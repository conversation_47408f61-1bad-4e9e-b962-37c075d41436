package com.xgen.svc.nds.dao;

import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.DUPLICATE_KEY_ERROR_CODE;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.MongoWriteException;
import com.xgen.cloud.nds.project._private.dao.DisaggregatedStorageServiceLogDao;
import com.xgen.cloud.nds.project._public.model.DisaggregatedServiceLog;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class DisaggregatedStorageServiceLogDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private DisaggregatedStorageServiceLogDao disaggregatedStorageServiceLogDao;

  @Test
  public void testCreateAndFind() {
    final var log = new DisaggregatedServiceLog(1, new ObjectId(), null, null);
    assertFalse(disaggregatedStorageServiceLogDao.findByLogId(log.getLogId()).isPresent());

    disaggregatedStorageServiceLogDao.create(log);
    assertTrue(disaggregatedStorageServiceLogDao.findByLogId(log.getLogId()).isPresent());

    final var ex =
        assertThrows(
            MongoWriteException.class, () -> disaggregatedStorageServiceLogDao.create(log));
    assertEquals(DUPLICATE_KEY_ERROR_CODE, ex.getCode());
    assertTrue(disaggregatedStorageServiceLogDao.findByLogId(log.getLogId()).isPresent());
  }
}
