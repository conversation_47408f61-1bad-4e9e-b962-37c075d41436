package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._private.dao.AWSPeerVpcDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSPeerVpc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.project._private.dao.NDSContainerPeerDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSContainerPeerDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private NDSContainerPeerDao _peerVpcDao;

  @Inject private AWSPeerVpcDao _awsVpcDao;

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private GroupDao _groupDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _ndsGroupDao.ensureIndexes();
    _groupDao.ensureIndexes();
  }

  private Pair<ObjectId, ObjectId> setupGroupWithSingleContainer() {
    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(
            groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
    return Pair.of(groupId, containerId);
  }

  @Test
  public void testSetAwsPeerVpcDeleteRequested() {
    final ObjectId groupId = new ObjectId();

    // Add a group
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    // Add a container
    final ObjectId containerId =
        _ndsGroupDao.addCloudContainer(
            groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSPeerVpc vpc = NDSModelTestFactory.getAWSPeerVpc(containerId);
    _awsVpcDao.addAwsPeerVpc(groupId, vpc);

    final List<AWSPeerVpc> peers = _awsVpcDao.getAwsPeerVpcs(groupId);
    assertFalse(peers.get(0).isDeleteRequested());
    _peerVpcDao.setPeerDeleteRequested(groupId, containerId, peers.get(0).getId());
    assertTrue(_awsVpcDao.getAwsPeerVpcs(groupId).get(0).isDeleteRequested());
  }

  @Test
  public void testSetPeerVpcFieldsForContainer() {
    final Pair<ObjectId, ObjectId> ids = setupGroupWithSingleContainer();
    final ObjectId groupId = ids.getLeft();
    final ObjectId containerId = ids.getRight();

    _ndsGroupDao.addCloudContainer(
        groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSPeerVpc vpc = NDSModelTestFactory.getAWSPeerVpc(containerId);
    _awsVpcDao.addAwsPeerVpc(groupId, vpc);

    final List<AWSPeerVpc> peers = _awsVpcDao.getAwsPeerVpcs(groupId);
    assertEquals(1, peers.size());
    peers.forEach(p -> assertEquals(vpc, p));

    _awsVpcDao.addAwsPeerVpc(groupId, vpc);

    final List<AWSPeerVpc> peers2 = _awsVpcDao.getAwsPeerVpcs(groupId);
    assertEquals(2, peers2.size());
    peers2.forEach(p -> assertEquals(vpc, p));

    final NDSGroup group = _ndsGroupDao.find(groupId).get();

    final CloudProviderContainer container = group.getCloudProviderContainer(containerId).get();

    final String connectionId = "pcx-1234";
    final AWSPeerVpc.Status status = AWSPeerVpc.Status.PENDING_ACCEPTANCE;
    _peerVpcDao.setPeerFieldsForContainer(
        groupId,
        containerId,
        vpc.getId(),
        Arrays.asList(
            Pair.of(AWSPeerVpc.FieldDefs.CONNECTION_ID, connectionId),
            Pair.of(AWSPeerVpc.FieldDefs.STATUS, status.name())));

    final List<AWSPeerVpc> updatedPeers =
        _awsVpcDao.getAwsPeerVpcs(groupId).stream()
            .filter(p -> !p.equals(vpc))
            .collect(Collectors.toList());
    assertEquals(1, updatedPeers.size());

    final AWSPeerVpc updated = updatedPeers.get(0);

    assertNotEquals(vpc, updated);
    // Old fields are the same
    assertEquals(vpc.getAwsAccountId(), updated.getAwsAccountId());
    assertEquals(vpc.getId(), updated.getId());
    assertEquals(vpc.getRouteTableCidrBlock(), updated.getRouteTableCidrBlock());
    assertEquals(vpc.getVpcId(), updated.getVpcId());
    // Modified fields are different
    assertEquals(connectionId, updated.getConnectionId().get());
    assertEquals(status, updated.getStatus());

    final CloudProviderContainer updatedContainer =
        _ndsGroupDao.find(groupId).get().getCloudProviderContainer(containerId).get();
    assertNotEquals(container.getLastUpdate(), updatedContainer.getLastUpdate());
  }
}
