package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.util._public.logging.LogLevel;
import com.xgen.cloud.common.util._public.util.MapUtils;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.nds.project._private.dao.BaseClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsV1Dao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsV2Dao;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs.ClusterDescriptionProcessArgsId;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs.Type;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgsUpdatable;
import com.xgen.cloud.nds.project._public.model.CustomDefaultRWConcern;
import com.xgen.cloud.nds.project._public.model.MongosqldProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public final class ClusterDescriptionProcessArgsDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private ClusterDescriptionProcessArgsV1Dao _clusterDescriptionProcessArgsV1Dao;
  @Inject private ClusterDescriptionProcessArgsV2Dao _clusterDescriptionProcessArgsV2Dao;
  @Inject private ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _clusterDescriptionProcessArgsV1Dao.ensureIndexes();
    _clusterDescriptionProcessArgsV2Dao.ensureIndexes();
  }

  private List<BaseClusterDescriptionProcessArgsDao> getDaos() {
    return List.of(_clusterDescriptionProcessArgsV1Dao, _clusterDescriptionProcessArgsV2Dao);
  }

  @Test
  public void testSaveAndFindMerged() {
    final ObjectId pGroupId = new ObjectId("6365624e09363e9fed16926c");
    final String pClusterName = "Group1";

    // ClusterDescriptionProcessArgsV1Dao
    {
      final ProcessArguments2_6 shardArgsForStandard = new ProcessArguments2_6();
      shardArgsForStandard.setPort(3000);
      final ClusterDescriptionProcessArgsUpdatable standard =
          new ClusterDescriptionProcessArgsUpdatable(
              shardArgsForStandard,
              createProcessArgumentsWithManyFields(),
              createProcessArgumentsWithOplogSize1MB(),
              createProcessArgumentsWithCustomBI(),
              createMongotProcessArguments(),
              createProcessArgumentsWithManyFields(),
              createMongotuneProcessArguments(),
              createCustomDefaultRWConcern(),
              createSetClusterParameterMap(),
              pClusterName,
              pGroupId,
              Type.STANDARD);

      final ProcessArguments2_6 shardArgsForAdmin = new ProcessArguments2_6();
      shardArgsForAdmin.setPort(4000);
      final ClusterDescriptionProcessArgsUpdatable admin =
          new ClusterDescriptionProcessArgsUpdatable(
              shardArgsForAdmin,
              standard.getMongosArg().get(),
              standard.getConfigArg().get(),
              standard.getMongosqldArg().get(),
              standard.getMongotArg().get(),
              standard.getPhasedVersionShardArg().get(),
              standard.getMongotuneArg().get(),
              standard.getCustomDefaultRWConcern().get(),
              standard.getSetClusterParameter().get(),
              pClusterName,
              pGroupId,
              Type.ADMIN);

      _clusterDescriptionProcessArgsV1Dao.save(standard);
      Optional<ClusterDescriptionProcessArgs> res1 =
          _clusterDescriptionProcessArgsV1Dao.findMerged(pClusterName, pGroupId);
      assertEquals(standard, res1.orElseThrow());

      _clusterDescriptionProcessArgsV1Dao.save(admin);
      Optional<ClusterDescriptionProcessArgs> res2 =
          _clusterDescriptionProcessArgsV1Dao.findMerged(pClusterName, pGroupId);
      assertEquals(admin, res2.orElseThrow());

      // check response is of type MERGED via reflection
      Class<?> clazz = ClusterDescriptionProcessArgs.class;
      try {
        Field internalTypeField = clazz.getDeclaredField("_internalType");
        internalTypeField.setAccessible(true);
        List.of(internalTypeField.get(res1.get()), internalTypeField.get(res2.get()))
            .forEach(
                internalType -> {
                  assertEquals("MERGED", internalType.toString());
                });
      } catch (Exception pE) {
        fail();
      }
    }

    // ClusterDescriptionProcessArgsV2Dao
    {
      final ProcessArguments2_6 shardArgsForStandard = new ProcessArguments2_6();
      shardArgsForStandard.setPort(3000);
      shardArgsForStandard.setRouterPort(3100);
      final Map<String, Object> setClusterParameterMapForStandard =
          Map.of(
              "level1-argA",
              Map.of("level2-argA", "std", "level2-argB", "std"),
              "level1-argB",
              "std",
              "level1-argC",
              "std");
      final ClusterDescriptionProcessArgsUpdatable standard =
          new ClusterDescriptionProcessArgsUpdatable(
              shardArgsForStandard,
              null,
              createProcessArgumentsWithOplogSize1MB(),
              createProcessArgumentsWithCustomBI(),
              createMongotProcessArguments(),
              createProcessArgumentsWithManyFields(),
              createMongotuneProcessArguments(),
              createCustomDefaultRWConcern(),
              setClusterParameterMapForStandard,
              pClusterName,
              pGroupId,
              Type.STANDARD);

      final ProcessArguments2_6 shardArgsForAdmin = new ProcessArguments2_6();
      shardArgsForAdmin.setRouterPort(4100);
      shardArgsForAdmin.setWiredTigerEngineCacheSizeGB(2.0);
      final Map<String, Object> setClusterParameterMapForAdmin =
          Map.of(
              "level1-argA",
              Map.of("level2-argA", "adm", "level2-argC", "adm"),
              "level1-argB",
              "adm",
              "level1-argD",
              "adm");
      final ClusterDescriptionProcessArgsUpdatable admin =
          new ClusterDescriptionProcessArgsUpdatable(
              shardArgsForAdmin,
              createProcessArgumentsWithManyFields(),
              null,
              null,
              null,
              null,
              null,
              null,
              setClusterParameterMapForAdmin,
              pClusterName,
              pGroupId,
              Type.ADMIN);

      _clusterDescriptionProcessArgsV2Dao.save(standard);
      _clusterDescriptionProcessArgsV2Dao.save(admin);
      ClusterDescriptionProcessArgs merged =
          _clusterDescriptionProcessArgsV2Dao.findMerged(pClusterName, pGroupId).orElseThrow();

      // test non-conflicting (set only in standard or admin)
      assertEquals(admin.getMongosArg().orElseThrow(), merged.getMongosArg().orElseThrow());
      assertEquals(standard.getConfigArg().orElseThrow(), merged.getConfigArg().orElseThrow());
      assertEquals(
          standard.getMongosqldArg().orElseThrow(), merged.getMongosqldArg().orElseThrow());
      assertEquals(standard.getMongotArg().orElseThrow(), merged.getMongotArg().orElseThrow());
      assertEquals(
          standard.getPhasedVersionShardArg().orElseThrow(),
          merged.getPhasedVersionShardArg().orElseThrow());
      assertEquals(
          standard.getMongotuneArg().orElseThrow(), merged.getMongotuneArg().orElseThrow());
      assertEquals(
          standard.getCustomDefaultRWConcern().orElseThrow(),
          merged.getCustomDefaultRWConcern().orElseThrow());

      // test conflicting
      final ProcessArguments2_6 shardArgsForMerged = merged.getShardArg().orElseThrow();
      final Map<String, Object> setClusterParameterMapForMerged =
          merged.getSetClusterParameter().orElseThrow();
      assertEquals(shardArgsForStandard.getPort(), shardArgsForMerged.getPort());
      assertEquals(shardArgsForAdmin.getRouterPort(), shardArgsForMerged.getRouterPort());
      assertEquals(
          shardArgsForAdmin.getWiredTigerEngineCacheSizeGB(),
          shardArgsForMerged.getWiredTigerEngineCacheSizeGB());

      List.of(
              Pair.of("std", List.of("level1-argA/level2-argB", "level1-argC")),
              Pair.of(
                  "adm",
                  List.of(
                      "level1-argA/level2-argA",
                      "level1-argA/level2-argC",
                      "level1-argB",
                      "level1-argD")))
          .forEach(
              typeToArgListPair -> {
                typeToArgListPair
                    .getRight()
                    .forEach(
                        argPath -> {
                          assertEquals(
                              typeToArgListPair.getLeft(),
                              getValueFromMap(setClusterParameterMapForMerged, argPath));
                        });
              });
      // check response is of type MERGED via reflection
      Class<?> clazz = ClusterDescriptionProcessArgs.class;
      try {
        Field internalTypeField = clazz.getDeclaredField("_internalType");
        internalTypeField.setAccessible(true);
        assertEquals("MERGED", internalTypeField.get(merged).toString());
      } catch (Exception pE) {
        fail();
      }
    }
  }

  @Test
  public void testFindNonExisting() {
    final ProcessArguments2_6 pMongosArg = createProcessArgumentsWithManyFields();
    final ProcessArguments2_6 pShardArg = createProcessArgumentsWithPort3000();
    final ProcessArguments2_6 pConfigArg = createProcessArgumentsWithOplogSize1MB();
    final MongosqldProcessArgs pMongosqldArg = createProcessArgumentsWithCustomBI();
    final MongotProcessArgs pMongotArg = createMongotProcessArguments();
    final ProcessArguments2_6 pPhasedVersionShardArg = createProcessArgumentsWithManyFields();
    final MongotuneProcessArgs pMongotuneArg = createMongotuneProcessArguments();
    final CustomDefaultRWConcern pRWConcern = createCustomDefaultRWConcern();
    final Map<String, Object> pSetClusterParameter = createSetClusterParameterMap();
    final ObjectId pGroupId = new ObjectId("6365624e09363e9fed16926c");
    final String pClusterName = "Group0";
    getDaos()
        .forEach(
            dao -> {
              final ClusterDescriptionProcessArgsUpdatable cdpa =
                  new ClusterDescriptionProcessArgsUpdatable(
                      pShardArg,
                      pMongosArg,
                      pConfigArg,
                      pMongosqldArg,
                      pMongotArg,
                      pPhasedVersionShardArg,
                      pMongotuneArg,
                      pRWConcern,
                      pSetClusterParameter,
                      pClusterName,
                      pGroupId,
                      Type.STANDARD);
              dao.save(cdpa);
              final Optional<ClusterDescriptionProcessArgs> res =
                  dao.findMerged("Doesn'tExist", new ObjectId("121212121212121212121212"));
              assertEquals(Optional.empty(), res);
            });
  }

  @Test
  public void testUpdate() {
    final ProcessArguments2_6 pMongosArg = createProcessArgumentsWithManyFields();
    final ProcessArguments2_6 pShardArg = createProcessArgumentsWithPort3000();
    final ProcessArguments2_6 pConfigArg = createProcessArgumentsWithOplogSize1MB();
    final MongosqldProcessArgs pMongosqldArg = createProcessArgumentsWithCustomBI();
    final MongotProcessArgs pMongotArg = createMongotProcessArguments();
    final ProcessArguments2_6 pPhasedVersionShardArg = createProcessArgumentsWithManyFields();
    final MongotuneProcessArgs pMongotuneArg = createMongotuneProcessArguments();
    final Map<String, Object> pSetClusterParameter = createSetClusterParameterMap();
    final CustomDefaultRWConcern pRWConcern = createCustomDefaultRWConcern();
    final ObjectId pGroupId = new ObjectId("6365624e09363e9fed16926c");
    final String pClusterName = "Group1";
    getDaos()
        .forEach(
            dao -> {
              final ClusterDescriptionProcessArgsUpdatable cdpa =
                  new ClusterDescriptionProcessArgsUpdatable(
                      pShardArg,
                      pMongosArg,
                      pConfigArg,
                      pMongosqldArg,
                      pMongotArg,
                      pPhasedVersionShardArg,
                      pMongotuneArg,
                      pRWConcern,
                      pSetClusterParameter,
                      pClusterName,
                      pGroupId,
                      Type.STANDARD);

              // All arguments are pMongos but the clusterName and groupId are the same. Thus it'll
              // update the
              // pre-existing
              // Arguments to match this document.
              final ClusterDescriptionProcessArgsUpdatable updatedCdpa =
                  new ClusterDescriptionProcessArgsUpdatable(
                      pMongosArg,
                      pMongosArg,
                      pMongosArg,
                      pMongosqldArg,
                      pMongotArg,
                      pPhasedVersionShardArg,
                      pMongotuneArg,
                      pRWConcern,
                      pSetClusterParameter,
                      pClusterName,
                      pGroupId,
                      Type.STANDARD);
              dao.save(cdpa);
              dao.save(updatedCdpa);
              final Optional<ClusterDescriptionProcessArgs> res =
                  dao.findMerged(pClusterName, pGroupId);
              assertNotEquals(cdpa, res.get());
              assertEquals(updatedCdpa, res.get());
            });
  }

  @Test
  public void testFindForUpdate() {
    final ObjectId pGroupId = new ObjectId("6365624e09363e9fed16926c");
    final String pClusterName = "Group1";

    // ClusterDescriptionProcessArgsV1Dao
    {
      final ClusterDescriptionProcessArgsUpdatable processArgs =
          new ClusterDescriptionProcessArgsUpdatable.Builder(pClusterName, pGroupId)
              .setOplogSizeMB(10)
              .setMongotProcessArgs(createMongotProcessArguments())
              .build();
      _clusterDescriptionProcessArgsV1Dao.save(processArgs);
      ClusterDescriptionProcessArgs resSystem =
          _clusterDescriptionProcessArgsV1Dao
              .findForUpdate(pClusterName, pGroupId, Type.STANDARD)
              .get();
      ClusterDescriptionProcessArgs resCustom =
          _clusterDescriptionProcessArgsV1Dao
              .findForUpdate(pClusterName, pGroupId, Type.ADMIN)
              .get();
      Class<ClusterDescriptionProcessArgs> clazz = ClusterDescriptionProcessArgs.class;
      Stream.of(Pair.of(Type.STANDARD, resSystem), Pair.of(Type.ADMIN, resCustom))
          .forEach(
              typeToProcessArgsFromDBPair -> {
                final String expectedType = typeToProcessArgsFromDBPair.getLeft().toString();
                final ClusterDescriptionProcessArgs processArgsFromDB =
                    typeToProcessArgsFromDBPair.getRight();
                assertEquals(processArgs, processArgsFromDB);
                // check response has expected internal type via reflection
                try {
                  Field internalTypeField = clazz.getDeclaredField("_internalType");
                  internalTypeField.setAccessible(true);
                  Object internalType = internalTypeField.get(processArgsFromDB);
                  assertEquals(expectedType, internalType.toString());
                } catch (Exception pE) {
                  fail();
                }
              });
    }

    // ClusterDescriptionProcessArgsV2Dao
    {
      final ProcessArguments2_6 shardArgsForSystem = new ProcessArguments2_6();
      shardArgsForSystem.setPort(27017);
      shardArgsForSystem.setOplogSizeMB(10);
      final ProcessArguments2_6 shardArgsForCustom = new ProcessArguments2_6();
      shardArgsForCustom.setPort(26000);
      final ClusterDescriptionProcessArgsUpdatable system =
          new ClusterDescriptionProcessArgsUpdatable(
              shardArgsForSystem,
              null,
              null,
              null,
              null,
              null,
              null,
              null,
              null,
              pClusterName,
              pGroupId,
              Type.STANDARD);
      final ClusterDescriptionProcessArgsUpdatable custom =
          new ClusterDescriptionProcessArgsUpdatable(
              shardArgsForCustom,
              null,
              null,
              null,
              null,
              null,
              null,
              null,
              null,
              pClusterName,
              pGroupId,
              Type.ADMIN);
      _clusterDescriptionProcessArgsV2Dao.save(system);
      _clusterDescriptionProcessArgsV2Dao.save(custom);
      ClusterDescriptionProcessArgs resSystem =
          _clusterDescriptionProcessArgsV2Dao
              .findForUpdate(pClusterName, pGroupId, Type.STANDARD)
              .get();
      ClusterDescriptionProcessArgs resCustom =
          _clusterDescriptionProcessArgsV2Dao
              .findForUpdate(pClusterName, pGroupId, Type.ADMIN)
              .get();
      Class<ClusterDescriptionProcessArgs> clazz = ClusterDescriptionProcessArgs.class;
      assertNotEquals(resSystem, resCustom);
      Stream.of(List.of(Type.STANDARD, system, resSystem), List.of(Type.ADMIN, custom, resCustom))
          .forEach(
              list -> {
                final String expectedInternalType = list.get(0).toString();
                final ClusterDescriptionProcessArgs expectedProcessArgs =
                    (ClusterDescriptionProcessArgs) list.get(1);
                final ClusterDescriptionProcessArgs processArgsFromDB =
                    (ClusterDescriptionProcessArgs) list.get(2);
                assertEquals(expectedProcessArgs, processArgsFromDB);
                // check response has expected internal type via reflection
                try {
                  Field internalTypeField = clazz.getDeclaredField("_internalType");
                  internalTypeField.setAccessible(true);
                  Object internalType = internalTypeField.get(processArgsFromDB);
                  assertEquals(expectedInternalType, internalType.toString());
                } catch (Exception pE) {
                  fail();
                }
              });
    }
  }

  @Test
  public void testClearAdminOverrides() {
    final String clusterName = "ClusterAdminClearTest";
    final ObjectId groupId = new ObjectId(new Date(), 101);
    final String standardField = "standardKey";
    final String adminField = "adminkey";

    final ClusterDescriptionProcessArgsUpdatable standardForAdminClearTest =
        new ClusterDescriptionProcessArgsUpdatable.Builder(clusterName, groupId, Type.STANDARD)
            .setShardArgSetParameterMap(Map.of(standardField, "standardValue"))
            .build();
    final ClusterDescriptionProcessArgsUpdatable adminForAdminClearTest =
        new ClusterDescriptionProcessArgsUpdatable.Builder(clusterName, groupId, Type.ADMIN)
            .setShardArgSetParameterMap(Map.of(adminField, "adminValue"))
            .build();
    _clusterDescriptionProcessArgsV2Dao.save(standardForAdminClearTest);
    _clusterDescriptionProcessArgsV2Dao.save(adminForAdminClearTest);

    // admin override originally exists
    assertTrue(
        _clusterDescriptionProcessArgsV2Dao
            .findForUpdate(clusterName, groupId, Type.ADMIN)
            .isPresent());

    // clear admin field
    _clusterDescriptionProcessArgsV2Dao.clearAdminOverrides(clusterName, groupId);

    // Not just an admin object with empty fields. Admin object no longer exists.
    assertFalse(
        _clusterDescriptionProcessArgsV2Dao
            .findForUpdate(clusterName, groupId, Type.ADMIN)
            .isPresent());
  }

  @Test
  public void testRemove() {
    final ProcessArguments2_6 pMongosArg = createProcessArgumentsWithManyFields();
    final ProcessArguments2_6 pShardArg = createProcessArgumentsWithPort3000();
    final ProcessArguments2_6 pConfigArg = createProcessArgumentsWithOplogSize1MB();
    final MongosqldProcessArgs pMongosqldArg = createProcessArgumentsWithCustomBI();
    final MongotProcessArgs pMongotArg = createMongotProcessArguments();
    final ProcessArguments2_6 pPhasedVersionShardArg = createProcessArgumentsWithManyFields();
    final MongotuneProcessArgs pMongotuneArg = createMongotuneProcessArguments();
    final CustomDefaultRWConcern pRWConcern = createCustomDefaultRWConcern();
    final Map<String, Object> pSetClusterParameter = createSetClusterParameterMap();
    final ObjectId pGroupId = new ObjectId("6365624e09363e9fed16926c");
    final String pClusterName = "Cluster1";
    // test each V1 and V2 individually
    getDaos()
        .forEach(
            dao -> {
              final ClusterDescriptionProcessArgsUpdatable cdpa =
                  new ClusterDescriptionProcessArgsUpdatable(
                      pShardArg,
                      pMongosArg,
                      pConfigArg,
                      pMongosqldArg,
                      pMongotArg,
                      pPhasedVersionShardArg,
                      pMongotuneArg,
                      pRWConcern,
                      pSetClusterParameter,
                      pClusterName,
                      pGroupId,
                      Type.STANDARD);
              dao.save(cdpa);
              // Remove wrong one and doc should still be present
              dao.remove("someName", pGroupId);
              assertTrue(dao.findMerged(pClusterName, pGroupId).isPresent());
              // Remove correct one and it should be gone
              dao.remove(pClusterName, pGroupId);
              assertTrue(dao.findMerged(pClusterName, pGroupId).isEmpty());
            });
    // test wrapped remove call
    final ClusterDescriptionProcessArgsUpdatable cdpa =
        new ClusterDescriptionProcessArgsUpdatable(
            pShardArg,
            pMongosArg,
            pConfigArg,
            pMongosqldArg,
            pMongotArg,
            pPhasedVersionShardArg,
            pMongotuneArg,
            pRWConcern,
            pSetClusterParameter,
            pClusterName,
            pGroupId,
            Type.STANDARD);
    _clusterDescriptionProcessArgsDao.save(cdpa);
    assertTrue(_clusterDescriptionProcessArgsV1Dao.findMerged(pClusterName, pGroupId).isPresent());
    assertTrue(_clusterDescriptionProcessArgsV2Dao.findMerged(pClusterName, pGroupId).isPresent());
    _clusterDescriptionProcessArgsDao.remove(pClusterName, pGroupId);
    assertTrue(_clusterDescriptionProcessArgsV1Dao.findMerged(pClusterName, pGroupId).isEmpty());
    assertTrue(_clusterDescriptionProcessArgsV2Dao.findMerged(pClusterName, pGroupId).isEmpty());
  }

  @Test
  void testFindMergedByIds() {
    final ObjectId groupId = new ObjectId();
    final int count = 3;

    final ProcessArguments2_6 mongosArgs = createProcessArgumentsWithManyFields();
    final ProcessArguments2_6 shardArg = createProcessArgumentsWithPort3000();
    final ProcessArguments2_6 configArg = createProcessArgumentsWithOplogSize1MB();
    final MongosqldProcessArgs mongosqldArg = createProcessArgumentsWithCustomBI();
    final MongotProcessArgs mongotArg = createMongotProcessArguments();
    final ProcessArguments2_6 phasedVersionShardArg = createProcessArgumentsWithManyFields();
    final MongotuneProcessArgs mongotuneArg = createMongotuneProcessArguments();
    final CustomDefaultRWConcern rwConcern = createCustomDefaultRWConcern();
    final Map<String, Object> setClusterParameter = createSetClusterParameterMap();

    // add 3 documents in group 1
    List<ClusterDescriptionProcessArgsId> idList = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      final String clusterName = "Cluster" + i;
      idList.add(new ClusterDescriptionProcessArgsId(clusterName, groupId));
      getDaos()
          .forEach(
              dao -> {
                final ClusterDescriptionProcessArgsUpdatable cdpa =
                    new ClusterDescriptionProcessArgsUpdatable(
                        shardArg,
                        mongosArgs,
                        configArg,
                        mongosqldArg,
                        mongotArg,
                        phasedVersionShardArg,
                        mongotuneArg,
                        rwConcern,
                        setClusterParameter,
                        clusterName,
                        groupId,
                        Type.STANDARD);
                dao.save(cdpa);
              });
    }

    getDaos()
        .forEach(
            dao -> {
              List<ClusterDescriptionProcessArgs> res = dao.findMergedByIds(idList);
              assertEquals(count, res.size());
              res.forEach(cdpa -> assertTrue(idList.contains(cdpa.getClusterConfId())));
            });
  }

  @Test
  public void testNumberOfProcessArgsInGroup() {
    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();

    getDaos()
        .forEach(
            dao -> {
              // should be 0 without any process args docs
              assertEquals(0, dao.numberOfProcessArgsInGroup(groupId1));
              assertEquals(0, dao.numberOfProcessArgsInGroup(groupId2));
            });

    final ProcessArguments2_6 mongosArgs = createProcessArgumentsWithManyFields();
    final ProcessArguments2_6 shardArg = createProcessArgumentsWithPort3000();
    final ProcessArguments2_6 configArg = createProcessArgumentsWithOplogSize1MB();
    final MongosqldProcessArgs mongosqldArg = createProcessArgumentsWithCustomBI();
    final MongotProcessArgs mongotArg = createMongotProcessArguments();
    final ProcessArguments2_6 phasedVersionShardArg = createProcessArgumentsWithManyFields();
    final MongotuneProcessArgs mongotuneArg = createMongotuneProcessArguments();
    final CustomDefaultRWConcern rwConcern = createCustomDefaultRWConcern();
    final Map<String, Object> setClusterParameter = createSetClusterParameterMap();

    // add 3 documents in group 1
    for (int i = 0; i < 3; i++) {
      final String clusterName = "Cluster" + i;
      getDaos()
          .forEach(
              dao -> {
                final ClusterDescriptionProcessArgsUpdatable cdpa =
                    new ClusterDescriptionProcessArgsUpdatable(
                        shardArg,
                        mongosArgs,
                        configArg,
                        mongosqldArg,
                        mongotArg,
                        phasedVersionShardArg,
                        mongotuneArg,
                        rwConcern,
                        setClusterParameter,
                        clusterName,
                        groupId1,
                        Type.STANDARD);
                dao.save(cdpa);
              });
    }

    // add 2 documents in group 2
    for (int i = 0; i < 2; i++) {
      final String clusterName = "Cluster" + i;
      getDaos()
          .forEach(
              dao -> {
                final ClusterDescriptionProcessArgsUpdatable cdpa =
                    new ClusterDescriptionProcessArgsUpdatable(
                        shardArg,
                        mongosArgs,
                        configArg,
                        mongosqldArg,
                        mongotArg,
                        phasedVersionShardArg,
                        mongotuneArg,
                        rwConcern,
                        setClusterParameter,
                        clusterName,
                        groupId2,
                        Type.STANDARD);
                dao.save(cdpa);
              });
    }

    getDaos()
        .forEach(
            dao -> {
              assertEquals(3, dao.numberOfProcessArgsInGroup(groupId1));
              assertEquals(2, dao.numberOfProcessArgsInGroup(groupId2));
            });
  }

  @Test
  public void testSave_dualWrites() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "Cluster1";

    // test update of type STANDARD
    final ClusterDescriptionProcessArgsUpdatable standard =
        new ClusterDescriptionProcessArgsUpdatable(
            createProcessArgumentsWithManyFields(),
            createProcessArgumentsWithManyFields(),
            createProcessArgumentsWithOplogSize1MB(),
            createProcessArgumentsWithCustomBI(),
            createMongotProcessArguments(),
            createProcessArgumentsWithManyFields(),
            createMongotuneProcessArguments(),
            createCustomDefaultRWConcern(),
            createSetClusterParameterMap(),
            clusterName,
            groupId,
            Type.STANDARD);
    _clusterDescriptionProcessArgsDao.save(standard);
    assertTrue(
        _clusterDescriptionProcessArgsDao.areDaosInSync(
            _clusterDescriptionProcessArgsV1Dao.findMerged(clusterName, groupId).orElseThrow(),
            _clusterDescriptionProcessArgsV2Dao.findMerged(clusterName, groupId).orElseThrow()));

    // test update of type ADMIN
    {
      final ClusterDescriptionProcessArgsUpdatable admin =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(clusterName, groupId, Type.ADMIN)
              .orElseThrow();
      admin.getShardArg().orElseThrow().setPort(4000);
      _clusterDescriptionProcessArgsDao.save(admin);
      _clusterDescriptionProcessArgsDao.areDaosInSync(
          _clusterDescriptionProcessArgsV1Dao.findMerged(clusterName, groupId).orElseThrow(),
          _clusterDescriptionProcessArgsV2Dao.findMerged(clusterName, groupId).orElseThrow());
    }

    // test standard and admin values for the same parameter
    {
      final int standardValue = 300;
      final int adminValue = 310;
      final ClusterDescriptionProcessArgsUpdatable standardUpdate =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(clusterName, groupId, Type.STANDARD)
              .orElseThrow();
      standardUpdate
          .getShardArg()
          .orElseThrow()
          .setMinSnapshotHistoryWindowInSeconds(standardValue);
      _clusterDescriptionProcessArgsDao.save(standardUpdate);
      final ClusterDescriptionProcessArgsUpdatable adminUpdate =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(clusterName, groupId, Type.ADMIN)
              .orElseThrow();
      adminUpdate.getShardArg().orElseThrow().setMinSnapshotHistoryWindowInSeconds(adminValue);
      _clusterDescriptionProcessArgsDao.save(adminUpdate);
      assertEquals(
          standardValue,
          _clusterDescriptionProcessArgsV2Dao
              .findForUpdate(clusterName, groupId, Type.STANDARD)
              .orElseThrow()
              .getShardArg()
              .orElseThrow()
              .getMinSnapshotHistoryWindowInSeconds());
      assertEquals(
          adminValue,
          _clusterDescriptionProcessArgsV1Dao
              .findForUpdate(clusterName, groupId, Type.STANDARD)
              .orElseThrow()
              .getShardArg()
              .orElseThrow()
              .getMinSnapshotHistoryWindowInSeconds());
      assertTrue(
          _clusterDescriptionProcessArgsDao.areDaosInSync(
              _clusterDescriptionProcessArgsV1Dao.findMerged(clusterName, groupId).orElseThrow(),
              _clusterDescriptionProcessArgsV2Dao.findMerged(clusterName, groupId).orElseThrow()));
    }

    // test removing parameters
    {
      final ClusterDescriptionProcessArgsUpdatable admin =
          _clusterDescriptionProcessArgsV1Dao
              .findForUpdate(clusterName, groupId, Type.ADMIN)
              .orElseThrow();
      admin.getShardArg().orElseThrow().unsetNet();
      _clusterDescriptionProcessArgsDao.save(admin);
      assertTrue(
          _clusterDescriptionProcessArgsDao.areDaosInSync(
              _clusterDescriptionProcessArgsV1Dao.findMerged(clusterName, groupId).orElseThrow(),
              _clusterDescriptionProcessArgsV2Dao.findMerged(clusterName, groupId).orElseThrow()));
    }

    // test mixed updates
    {
      final ClusterDescriptionProcessArgsUpdatable standardUpdate =
          _clusterDescriptionProcessArgsV1Dao
              .findForUpdate(clusterName, groupId, Type.STANDARD)
              .orElseThrow()
              .copy()
              .setOplogSizeMB(30)
              .build();
      _clusterDescriptionProcessArgsDao.save(standardUpdate);

      final ClusterDescriptionProcessArgsUpdatable adminUpdate =
          _clusterDescriptionProcessArgsV1Dao
              .findForUpdate(clusterName, groupId, Type.ADMIN)
              .orElseThrow()
              .copy()
              .setOplogSizeMB(null)
              .build();
      _clusterDescriptionProcessArgsDao.save(adminUpdate);

      final ClusterDescriptionProcessArgs v1 =
          _clusterDescriptionProcessArgsV1Dao.findMerged(clusterName, groupId).orElseThrow();
      final ClusterDescriptionProcessArgs v2 =
          _clusterDescriptionProcessArgsV2Dao.findMerged(clusterName, groupId).orElseThrow();
      assertTrue(_clusterDescriptionProcessArgsDao.areDaosInSync(v1, v2));
      assertNull(v2.getShardArg().orElseThrow().getOplogSizeMB());
    }
  }

  @Test
  public void testHasAdminOverrides() {
    final String clusterName = "TestCluster";
    final ObjectId groupId = new ObjectId();

    // Test case 1: No process args exist - should return false
    assertFalse(_clusterDescriptionProcessArgsV2Dao.hasAdminOverrides(clusterName, groupId));

    // Test case 2: Only standard process args exist - should return false
    final ClusterDescriptionProcessArgsUpdatable standardOnly =
        new ClusterDescriptionProcessArgsUpdatable.Builder(clusterName, groupId, Type.STANDARD)
            .setShardArgSetParameterMap(Map.of("standardKey", "standardValue"))
            .build();
    _clusterDescriptionProcessArgsV2Dao.save(standardOnly);
    assertFalse(_clusterDescriptionProcessArgsV2Dao.hasAdminOverrides(clusterName, groupId));

    // Test case 3: Admin process args exist - should return true
    final ClusterDescriptionProcessArgsUpdatable admin =
        new ClusterDescriptionProcessArgsUpdatable.Builder(clusterName, groupId, Type.ADMIN)
            .setShardArgSetParameterMap(Map.of("adminKey", "adminValue"))
            .build();
    _clusterDescriptionProcessArgsV2Dao.save(admin);
    assertTrue(_clusterDescriptionProcessArgsV2Dao.hasAdminOverrides(clusterName, groupId));

    // Test case 4: After clearing admin overrides - should return false
    _clusterDescriptionProcessArgsV2Dao.clearAdminOverrides(clusterName, groupId);
    assertFalse(_clusterDescriptionProcessArgsV2Dao.hasAdminOverrides(clusterName, groupId));

    // Test case 5: Different cluster should not affect result
    final String differentClusterName = "DifferentCluster";
    final ClusterDescriptionProcessArgsUpdatable differentClusterAdmin =
        new ClusterDescriptionProcessArgsUpdatable.Builder(
                differentClusterName, groupId, Type.ADMIN)
            .setShardArgSetParameterMap(Map.of("adminKey", "adminValue"))
            .build();
    _clusterDescriptionProcessArgsV2Dao.save(differentClusterAdmin);
    assertFalse(_clusterDescriptionProcessArgsV2Dao.hasAdminOverrides(clusterName, groupId));
    assertTrue(
        _clusterDescriptionProcessArgsV2Dao.hasAdminOverrides(differentClusterName, groupId));
  }

  @Test
  public void testGetProcessArgsAdminOverrides() {
    // setup
    final String clusterNamePrefix = "Cluster";
    final Date now = new Date();
    final String standardField = "standardKey";
    final String adminField = "adminkey";
    final int countClusterWithOverrides = 5;
    final List<ClusterDescriptionProcessArgs.ClusterDescriptionProcessArgsId> clusterIdList =
        new ArrayList<>();

    for (int i = countClusterWithOverrides; i > 0; i--) {
      final String clusterName = clusterNamePrefix + i;
      final ObjectId groupId = new ObjectId(now, i);
      final ClusterDescriptionProcessArgsUpdatable standard =
          new ClusterDescriptionProcessArgsUpdatable.Builder(clusterName, groupId, Type.STANDARD)
              .setShardArgSetParameterMap(Map.of(standardField, "standardValue"))
              .build();
      final ClusterDescriptionProcessArgsUpdatable admin =
          new ClusterDescriptionProcessArgsUpdatable.Builder(clusterName, groupId, Type.ADMIN)
              .setShardArgSetParameterMap(Map.of(adminField, "adminValue"))
              .build();
      _clusterDescriptionProcessArgsV2Dao.save(standard);
      _clusterDescriptionProcessArgsV2Dao.save(admin);
      clusterIdList.add(0, new ClusterDescriptionProcessArgsId(clusterName, groupId));
    }

    // control cluster without overrides
    final String controlClusterName = clusterNamePrefix + 0;
    final ObjectId controlClusterGroupId = new ObjectId(now, 0);
    final ClusterDescriptionProcessArgsUpdatable standard =
        new ClusterDescriptionProcessArgsUpdatable.Builder(
                controlClusterName, controlClusterGroupId, Type.STANDARD)
            .setShardArgSetParameterMap(Map.of(standardField, "standardValue"))
            .build();
    _clusterDescriptionProcessArgsV2Dao.save(standard);
    clusterIdList.add(
        0, new ClusterDescriptionProcessArgsId(controlClusterName, controlClusterGroupId));

    // test overall functionality, no optional filter
    {
      final List<BasicDBObject> result =
          _clusterDescriptionProcessArgsV2Dao.getProcessArgsAdminOverrides(
              0, 0, Collections.emptyList(), null);
      assertEquals(countClusterWithOverrides, result.size());
      for (int i = 1; i <= countClusterWithOverrides; i++) {
        assertEquals(
            clusterIdList.get(i).getClusterName(),
            Optional.of(result.get(i - 1))
                .map(adminArgs -> adminArgs.get(FieldDefs.ID))
                .map(BasicDBObject.class::cast)
                .map(id -> id.getString(ClusterDescriptionProcessArgsId.FieldDefs.CLUSTER_NAME))
                .orElseThrow());
        assertEquals(
            clusterIdList.get(i).getGroupId(),
            Optional.of(result.get(i - 1))
                .map(adminArgs -> adminArgs.get(FieldDefs.ID))
                .map(BasicDBObject.class::cast)
                .map(id -> id.getObjectId(ClusterDescriptionProcessArgsId.FieldDefs.GROUP_ID))
                .orElseThrow());

        // doesn't contain standard args
        assertFalse(
            Optional.of(result.get(i - 1))
                .map(adminArgs -> adminArgs.get(FieldDefs.SHARD_ARG_FIELD))
                .map(BasicDBObject.class::cast)
                .map(shardArgs -> shardArgs.get(ProcessArguments2_6.FieldDefs.SET_PARAMETER))
                .map(Map.class::cast)
                .map(setParameterMap -> setParameterMap.containsKey(standardField))
                .orElseThrow());
        // contains admin args
        assertTrue(
            Optional.of(result.get(i - 1))
                .map(adminArgs -> adminArgs.get(FieldDefs.SHARD_ARG_FIELD))
                .map(BasicDBObject.class::cast)
                .map(shardArgs -> shardArgs.get(ProcessArguments2_6.FieldDefs.SET_PARAMETER))
                .map(Map.class::cast)
                .map(setParameterMap -> setParameterMap.containsKey(adminField))
                .orElseThrow());
      }
    }

    // test cluster name filter
    {
      final ClusterDescriptionProcessArgsId cluster3Id = clusterIdList.get(3);
      final List<BasicDBObject> result =
          _clusterDescriptionProcessArgsV2Dao.getProcessArgsAdminOverrides(
              0, 0, List.of(cluster3Id.getGroupId()), cluster3Id.getClusterName());
      assertEquals(1, result.size());
      final BasicDBObject resultClusterId =
          Optional.of(result.get(0))
              .map(adminArgs -> adminArgs.get(FieldDefs.ID))
              .map(BasicDBObject.class::cast)
              .orElseThrow();
      assertEquals(
          cluster3Id.getClusterName(),
          resultClusterId.getString(ClusterDescriptionProcessArgsId.FieldDefs.CLUSTER_NAME));
      assertEquals(
          cluster3Id.getGroupId(),
          resultClusterId.getObjectId(ClusterDescriptionProcessArgsId.FieldDefs.GROUP_ID));
    }

    // test group list filter
    {
      final List<ObjectId> groupIdList =
          List.of(clusterIdList.get(4).getGroupId(), clusterIdList.get(5).getGroupId());
      final List<BasicDBObject> result =
          _clusterDescriptionProcessArgsV2Dao.getProcessArgsAdminOverrides(0, 0, groupIdList, null);
      assertEquals(groupIdList.size(), result.size());
      for (int i = 0; i < groupIdList.size(); i++) {
        assertEquals(
            groupIdList.get(i),
            Optional.of(result.get(i))
                .map(adminArgs -> adminArgs.get(FieldDefs.ID))
                .map(BasicDBObject.class::cast)
                .map(id -> id.getObjectId(ClusterDescriptionProcessArgsId.FieldDefs.GROUP_ID))
                .orElseThrow());
      }
    }

    // test page number and page size
    {
      final List<BasicDBObject> result =
          _clusterDescriptionProcessArgsV2Dao.getProcessArgsAdminOverrides(
              2, 2, Collections.emptyList(), null);
      final List<ClusterDescriptionProcessArgsId> expectedClusterList =
          List.of(clusterIdList.get(3), clusterIdList.get(4));
      assertEquals(2, result.size());
      for (int i = 0; i < result.size(); i++) {
        assertEquals(
            expectedClusterList.get(i).getClusterName(),
            Optional.of(result.get(i))
                .map(adminArgs -> adminArgs.get(FieldDefs.ID))
                .map(BasicDBObject.class::cast)
                .map(id -> id.getString(ClusterDescriptionProcessArgsId.FieldDefs.CLUSTER_NAME))
                .orElseThrow());
        assertEquals(
            expectedClusterList.get(i).getGroupId(),
            Optional.of(result.get(i))
                .map(adminArgs -> adminArgs.get(FieldDefs.ID))
                .map(BasicDBObject.class::cast)
                .map(id -> id.getObjectId(ClusterDescriptionProcessArgsId.FieldDefs.GROUP_ID))
                .orElseThrow());
      }
    }
  }

  private static ProcessArguments2_6 createProcessArgumentsWithManyFields() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setPort(27017);
    args.setBindIp("********");
    args.setDbPath("/tmp/data/db");
    return args;
  }

  private static ProcessArguments2_6 createProcessArgumentsWithOplogSize1MB() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setOplogSizeMB(1);
    return args;
  }

  private static ProcessArguments2_6 createProcessArgumentsWithPort3000() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setPort(3000);
    return args;
  }

  private static MongosqldProcessArgs createProcessArgumentsWithCustomBI() {
    final MongosqldProcessArgs args = new MongosqldProcessArgs();
    args.setSampleSize(1000);
    args.setSampleRefreshIntervalSecs(110);
    return args;
  }

  private static MongotProcessArgs createMongotProcessArguments() {
    final MongotProcessArgs args = new MongotProcessArgs();
    args.setFTSDisabled(false);
    return args;
  }

  private static MongotuneProcessArgs createMongotuneProcessArguments() {
    return MongotuneProcessArgs.builder().logLevel(LogLevel.INFO).build();
  }

  private static CustomDefaultRWConcern createCustomDefaultRWConcern() {
    final CustomDefaultRWConcern rwConcern = new CustomDefaultRWConcern();
    rwConcern.setDefaultReadConcern("local");
    rwConcern.setDefaultWriteConcern("majority");
    return rwConcern;
  }

  private static Map<String, Object> createSetClusterParameterMap() {
    final Map<String, Object> setClusterParameterMap = new HashMap<>();
    MapUtils.setDeep(
        setClusterParameterMap,
        List.of("changeStreamOptions", "preAndPostImages", "expireAfterSeconds"),
        "10");
    return setClusterParameterMap;
  }

  @SuppressWarnings("unchecked")
  private static Object getValueFromMap(final Map<String, Object> map, final String path) {
    List<String> names = List.of(path.split("/"));
    int currentLevel = 1;
    Map<String, Object> currentMap = map;
    while (currentLevel < names.size()) {
      currentMap = (Map<String, Object>) currentMap.get(names.get(currentLevel - 1));
      currentLevel++;
    }
    return currentMap.get(names.get(currentLevel - 1));
  }
}
