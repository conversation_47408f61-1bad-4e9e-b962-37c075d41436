package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.cps.restore._private.dao.SystemClusterJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CollectionRestore;
import com.xgen.cloud.cps.restore._public.model.CollectionRestore.OverallState;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreJob;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest;
import com.xgen.cloud.cps.restore._public.model.Export;
import com.xgen.cloud.cps.restore._public.model.Export.ExportType;
import com.xgen.cloud.cps.restore._public.model.ExportStatus;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterExportJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob.ExecutionOption;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob.SystemClusterJobStatus;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class SystemClusterJobDaoIntTests extends JUnit5BaseSvcTest {

  // source related fields
  public static final ObjectId SOURCE_PROJECT_ID = ObjectId.get();
  public static final String SOURCE_CLUSTER_NAME = "sourceClusterName";
  public static final ObjectId SOURCE_CLUSTER_UNIQUE_ID = ObjectId.get();
  public static final ObjectId TARGET_PROJECT_ID = ObjectId.get();
  public static final String TARGET_CLUSTER_NAME = "targetClusterName";
  public static final ObjectId TARGET_CLUSTER_UNIQUE_ID = ObjectId.get();
  public static final String TARGET_MDB_VERSION = "8.0.0";
  public static final String TARGET_FCV = "7.3";
  public static final ObjectId ORG_ID = ObjectId.get();
  public static final ObjectId SOURCE_SNAPSHOT_ID = ObjectId.get();
  public static final BSONTimestamp SOURCE_PIT_TIME = new BSONTimestamp(4, 1);
  public static final SystemClusterJob.RequestingUser REQUESTING_USER =
      new SystemClusterJob.RequestingUser(ObjectId.get(), "Test User", "<EMAIL>");
  public static final List<String> EXPORT_AGENT_ERROR_MESSAGES =
      List.of("failed to export snapshot");

  // system project related
  public static final ObjectId SYSTEM_PROJECT_ID = ObjectId.get();
  public static final SystemClusterJobStatus SYSTEM_PROJECT_STATUS =
      SystemClusterJobStatus.SUCCESSFUL;
  public static final Date SYSTEM_PROJECT_CREATED_DATE = TimeUtils.fromISOString("2024-05-01");
  public static final Date SYSTEM_PROJECT_DELETED_DATE = TimeUtils.fromISOString("2024-05-07");

  // system cluster related
  public static final String SYSTEM_CLUSTER_NAME = "systemClusterName";
  public static final ObjectId SYSTEM_CLUSTER_UNIQUE_ID = ObjectId.get();
  public static final SystemClusterJobStatus SYSTEM_CLUSTER_STATUS =
      SystemClusterJobStatus.SUCCESSFUL;
  public static final Date SYSTEM_CLUSTER_CREATED_DATE = TimeUtils.fromISOString("2024-05-01");
  public static final Date SYSTEM_CLUSTER_DELETED_DATE = TimeUtils.fromISOString("2024-05-07");

  public static final Date SNAPSHOT_INITIATION_DATE = TimeUtils.fromISOString("2024-04-30");

  // restore job related
  public static final ObjectId RESTORE_JOB_ID = ObjectId.get();
  public static final SystemClusterJobStatus RESTORE_STATUS = SystemClusterJobStatus.NOT_STARTED;
  public static final Date RESTORE_START_DATE = TimeUtils.fromISOString("2024-05-02");
  public static final Date RESTORE_END_DATE = TimeUtils.fromISOString("2024-05-06");

  // execution status
  public static final SystemClusterJobStatus EXECUTION_STATUS = SystemClusterJobStatus.NOT_STARTED;
  public static final Date EXECUTION_START_DATE = TimeUtils.fromISOString("2024-05-03");
  public static final Date EXECUTION_END_DATE = TimeUtils.fromISOString("2024-05-05");
  public static final Instant TEST_TIMESTAMP = Instant.parse("2024-05-04T00:00:00Z");

  @Inject private SystemClusterJobDao _systemClusterJobDao;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSSystemClusterJobDao/systemClusterJobs.json.ftl",
        null,
        "nds",
        "config.nds.backup.systemClusterJobs");
  }

  @Test
  public void testCreateAndFindSystemClusterJob() {
    final SystemClusterJob job1 = getSystemClusterExportJob();
    _systemClusterJobDao.save(job1);

    final SystemClusterJob job2 = getSystemClusterExportJob().setSystemProjectDeletedDate(null);
    _systemClusterJobDao.save(job2);

    final List<SystemClusterJob> jobs = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID);
    assertEquals(2, jobs.size(), jobs.toString());

    final List<SystemClusterJob> notCompletedJobs =
        _systemClusterJobDao.findActiveJobsForProject(SOURCE_PROJECT_ID);
    assertEquals(1, notCompletedJobs.size());

    BackupSnapshot backupSnapshot = mock(BackupSnapshot.class);
    when(backupSnapshot.getId()).thenReturn(SOURCE_SNAPSHOT_ID);
    when(backupSnapshot.getProjectId()).thenReturn(SOURCE_PROJECT_ID);

    final List<SystemClusterExportJob> notCompletedJobsForSnapshot =
        _systemClusterJobDao.findActiveExportJobsForSnapshot(backupSnapshot);
    assertEquals(1, notCompletedJobsForSnapshot.size());
  }

  @Test
  public void testCreateAndFindSystemClusterExportJob() {
    final SystemClusterExportJob exportJobSourceInput = getSystemClusterExportJob();

    _systemClusterJobDao.save(exportJobSourceInput);
    final List<SystemClusterJob> jobs = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID);
    assertEquals(1, jobs.size());

    final Optional<SystemClusterJob> exportJobOutput =
        _systemClusterJobDao.findById(exportJobSourceInput.getId());
    assertTrue(exportJobOutput.isPresent());
    final SystemClusterExportJob systemClusterExportJob =
        (SystemClusterExportJob) exportJobOutput.get();
    assertEquals(EXPORT_AGENT_ERROR_MESSAGES, systemClusterExportJob.getExportAgentErrorMessages());
  }

  @Test
  public void testCreateAndFindCollectionRestoreJob() {
    final CollectionRestoreJob jobSourceInput = getCollectionRestoreJob();

    _systemClusterJobDao.save(jobSourceInput);
    final List<SystemClusterJob> jobs = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID);
    assertEquals(1, jobs.size());

    final Optional<SystemClusterJob> jobOutput =
        _systemClusterJobDao.findById(jobSourceInput.getId());
    assertTrue(jobOutput.isPresent());
    final CollectionRestoreJob restoreJob = (CollectionRestoreJob) jobOutput.get();
    assertEquals(
        getCollectionRestoreRequest(),
        restoreJob.getCollectionRestore().getCollectionRestoreRequest());
  }

  @Test
  public void testCreateAndFindShadowClusterJob() {
    final ShadowClusterJob jobSourceInput = getShadowClusterJob();

    _systemClusterJobDao.save(jobSourceInput);
    final List<SystemClusterJob> jobs = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID);
    assertEquals(1, jobs.size());

    final Optional<SystemClusterJob> jobOutput =
        _systemClusterJobDao.findById(jobSourceInput.getId());
    assertTrue(jobOutput.isPresent());
    final ShadowClusterJob shadowClusterJob = (ShadowClusterJob) jobOutput.get();

    // Spot check a few fields
    assertEquals(jobSourceInput.getExecutionEndDate(), shadowClusterJob.getExecutionEndDate());
    assertEquals(jobSourceInput.getExposureId(), shadowClusterJob.getExposureId());
  }

  @Test
  public void testCreateAndFindSystemClusterExportJobBySourceCluster() {
    final SystemClusterExportJob exportJobSourceInput1 = getSystemClusterExportJob();
    SystemClusterExportJob exportJobSourceInput2 = getSystemClusterExportJob();
    exportJobSourceInput2.setSystemClusterName(SYSTEM_CLUSTER_NAME + 1);
    SystemClusterExportJob exportJobSourceInput3 = getSystemClusterExportJob();
    final ObjectId differentId = ObjectId.get();
    exportJobSourceInput3.setSourceClusterUniqueId(differentId);

    _systemClusterJobDao.save(exportJobSourceInput1);
    _systemClusterJobDao.save(exportJobSourceInput2);
    _systemClusterJobDao.save(exportJobSourceInput3);
    final List<SystemClusterJob> jobs = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID);
    assertEquals(3, jobs.size());

    final List<SystemClusterJob> exportJobsOutput =
        _systemClusterJobDao.findAllJobsBySourceCluster(
            SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID);
    assertEquals(2, exportJobsOutput.size());

    // Assert descending order
    assertEquals(SYSTEM_CLUSTER_NAME + 1, exportJobsOutput.get(0).getSystemClusterName());
    assertEquals(SYSTEM_CLUSTER_NAME, exportJobsOutput.get(1).getSystemClusterName());
  }

  @Test
  public void testUpdateExportStatus() {
    final SystemClusterExportJob exportJobSourceInput = getSystemClusterExportJob();
    _systemClusterJobDao.save(exportJobSourceInput);

    final ExportStatus.Builder exportStatusBuilder = getExportStatusBuilder();
    exportStatusBuilder.exportedCollections(123);
    exportStatusBuilder.uncompressedRawBytesExported(456);

    // update its ExportStatus.
    _systemClusterJobDao.updateExportStatus(
        exportJobSourceInput.getId(), exportStatusBuilder.build());

    // Check that it was updated.
    final Optional<SystemClusterJob> exportJobOutput =
        _systemClusterJobDao.findById(exportJobSourceInput.getId());
    assertTrue(exportJobOutput.isPresent());
    final SystemClusterExportJob systemClusterExportJob =
        (SystemClusterExportJob) exportJobOutput.get();
    assertEquals(
        123, systemClusterExportJob.getExport().getExportStatus().getExportedCollections());
    assertEquals(
        456,
        systemClusterExportJob.getExport().getExportStatus().getUncompressedRawBytesExported());
  }

  @Test
  public void getSystemClusterJobsOverRange() {
    Date startDate = TimeUtils2.fromISOString("2024-07-03T00:00:00Z");
    Date endDate = TimeUtils2.fromISOString("2024-07-04T00:00:00Z");
    List<SystemClusterExportJob> completedSystemClusterJobs =
        _systemClusterJobDao.findAllSuccessfullyCompletedExportJobsOverRange(startDate, endDate);
    assertEquals(1, completedSystemClusterJobs.size());
    SystemClusterJob job1 = completedSystemClusterJobs.get(0);
    assertEquals(oid(3), job1.getId());
    assertEquals(oid(3), job1.getSystemProjectId());
    assertEquals(oid(345), job1.getSourceProjectId());
    assertEquals("Cluster0-aws-sharded", job1.getSourceClusterName());
    assertEquals("backupExport-3", job1.getSystemClusterName());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job1.getOverallStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job1.getSystemClusterCreationStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job1.getRestoreStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job1.getExecutionStatus());

    startDate = TimeUtils2.fromISOString("2024-07-04T00:00:00Z");
    endDate = TimeUtils2.fromISOString("2024-07-05T00:00:00Z");
    completedSystemClusterJobs =
        _systemClusterJobDao.findAllSuccessfullyCompletedExportJobsOverRange(startDate, endDate);
    assertEquals(2, completedSystemClusterJobs.size());

    SystemClusterJob job2 =
        completedSystemClusterJobs.stream()
            .filter(systemClusterExportJob -> systemClusterExportJob.getId().equals(oid(1)))
            .findFirst()
            .get();
    assertEquals(oid(1), job2.getSystemProjectId());
    assertEquals(oid(123), job2.getSourceProjectId());
    assertEquals("Cluster0-aws", job2.getSourceClusterName());
    assertEquals("backupExport-1", job2.getSystemClusterName());
    assertEquals(ExecutionOption.BACKUP_EXPORT, job2.getExecutionOption());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job2.getOverallStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job2.getSystemClusterCreationStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job2.getRestoreStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job2.getExecutionStatus());

    SystemClusterJob job3 =
        completedSystemClusterJobs.stream()
            .filter(systemClusterExportJob -> systemClusterExportJob.getId().equals(oid(2)))
            .findFirst()
            .get();
    assertEquals(oid(2), job3.getSystemProjectId());
    assertEquals(oid(234), job3.getSourceProjectId());
    assertEquals("Cluster0-azure", job3.getSourceClusterName());
    assertEquals("backupExport-2", job3.getSystemClusterName());
    assertEquals(ExecutionOption.BACKUP_EXPORT, job3.getExecutionOption());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job3.getOverallStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job3.getSystemClusterCreationStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job3.getRestoreStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, job3.getExecutionStatus());
  }

  @Test
  public void updateCollectionRestoreOverallStateWithoutReason() {
    final CollectionRestoreJob jobSourceInput = getCollectionRestoreJob();
    _systemClusterJobDao.save(jobSourceInput);
    _systemClusterJobDao.updateCollectionRestoreOverallState(
        jobSourceInput.getId(), OverallState.IN_PROGRESS, null, Date.from(TEST_TIMESTAMP));

    final Optional<SystemClusterJob> jobOutput =
        _systemClusterJobDao.findById(jobSourceInput.getId());
    assertTrue(jobOutput.isPresent());
    final CollectionRestoreJob restoreJob = (CollectionRestoreJob) jobOutput.get();
    final CollectionRestore restore = restoreJob.getCollectionRestore();

    assertEquals(OverallState.IN_PROGRESS, restore.getOverallRestoreState());
    assertNull(restoreJob.getStateReason());
    assertEquals(Date.from(TEST_TIMESTAMP), restore.getLastUpdatedDate());
  }

  @Test
  public void updateCollectionRestoreOverallStateWithReason() {
    final CollectionRestoreJob jobSourceInput = getCollectionRestoreJob();
    _systemClusterJobDao.save(jobSourceInput);
    _systemClusterJobDao.updateCollectionRestoreOverallState(
        jobSourceInput.getId(),
        OverallState.FAILED,
        new SystemClusterJob.StateReason(CommonErrorCode.NOT_FOUND, "testDB.testColl"),
        Date.from(TEST_TIMESTAMP));

    final Optional<SystemClusterJob> jobOutput =
        _systemClusterJobDao.findById(jobSourceInput.getId());
    assertTrue(jobOutput.isPresent());
    final CollectionRestoreJob restoreJob = (CollectionRestoreJob) jobOutput.get();
    final CollectionRestore restore = restoreJob.getCollectionRestore();

    assertEquals(OverallState.FAILED, restore.getOverallRestoreState());
    assertEquals(
        new SystemClusterJob.StateReason(CommonErrorCode.NOT_FOUND, "testDB.testColl"),
        restoreJob.getStateReason());
    assertEquals(Date.from(TEST_TIMESTAMP), restore.getLastUpdatedDate());
  }

  private CollectionRestoreJob getCollectionRestoreJob() {
    return (CollectionRestoreJob)
        new CollectionRestoreJob(
                SOURCE_PROJECT_ID,
                SOURCE_CLUSTER_NAME,
                SOURCE_CLUSTER_UNIQUE_ID,
                ORG_ID,
                SOURCE_SNAPSHOT_ID,
                SOURCE_PIT_TIME,
                REQUESTING_USER,
                null,
                null,
                getCollectionRestore())
            .setSystemProjectId(SYSTEM_PROJECT_ID)
            .setSystemProjectStatus(SYSTEM_PROJECT_STATUS)
            .setSystemProjectCreatedDate(SYSTEM_PROJECT_CREATED_DATE)
            .setSystemProjectDeletedDate(SYSTEM_PROJECT_DELETED_DATE)
            .setSystemClusterName(SYSTEM_CLUSTER_NAME)
            .setSystemClusterUniqueId(SYSTEM_CLUSTER_UNIQUE_ID)
            .setSystemClusterStatus(SYSTEM_CLUSTER_STATUS)
            .setSystemClusterCreatedDate(SYSTEM_CLUSTER_CREATED_DATE)
            .setSystemClusterDeletedDate(SYSTEM_CLUSTER_DELETED_DATE)
            .setRestoreJobId(RESTORE_JOB_ID)
            .setRestoreStatus(RESTORE_STATUS)
            .setRestoreStartDate(RESTORE_START_DATE)
            .setRestoreEndDate(RESTORE_END_DATE)
            .setExecutionStatus(EXECUTION_STATUS)
            .setExecutionStartDate(EXECUTION_START_DATE)
            .setExecutionEndDate(EXECUTION_END_DATE);
  }

  private CollectionRestore getCollectionRestore() {
    return new CollectionRestore(getCollectionRestoreRequest())
        .setTotalDocNum(1234567890123456789L)
        .setRestoredDocNum(123456789L)
        .setTotalValidCollectionNum(100)
        .setRestoredCollectionNum(10)
        .setOverallRestoreState(CollectionRestore.OverallState.FINALIZING);
  }

  private CollectionRestoreRequest getCollectionRestoreRequest() {
    return new CollectionRestoreRequest(
        TARGET_PROJECT_ID,
        TARGET_CLUSTER_NAME,
        TARGET_CLUSTER_UNIQUE_ID,
        TARGET_MDB_VERSION,
        TARGET_FCV,
        CollectionRestoreRequest.IndexRestoreOption.RESTORE_ALL_INDEXES,
        CollectionRestoreRequest.RollbackStrategy.ROLLBACK_ALL_COLLECTIONS,
        CollectionRestoreRequest.WriteStrategy.OVERWRITE_IF_EXISTS,
        List.of(new CollectionRestoreRequest.RestoreName("sourceDb1", "targetDb1")),
        List.of(new CollectionRestoreRequest.RestoreName("db3.coll1", "db3.new-coll1")),
        "");
  }

  private SystemClusterExportJob getSystemClusterExportJob() {
    return (SystemClusterExportJob)
        new SystemClusterExportJob(
                SOURCE_PROJECT_ID,
                SOURCE_CLUSTER_NAME,
                SOURCE_CLUSTER_UNIQUE_ID,
                ORG_ID,
                SOURCE_SNAPSHOT_ID,
                SOURCE_PIT_TIME,
                REQUESTING_USER,
                getExport(),
                EXPORT_AGENT_ERROR_MESSAGES)
            .setSystemProjectId(SYSTEM_PROJECT_ID)
            .setSystemProjectStatus(SYSTEM_PROJECT_STATUS)
            .setSystemProjectCreatedDate(SYSTEM_PROJECT_CREATED_DATE)
            .setSystemProjectDeletedDate(SYSTEM_PROJECT_DELETED_DATE)
            .setSystemClusterName(SYSTEM_CLUSTER_NAME)
            .setSystemClusterUniqueId(SYSTEM_CLUSTER_UNIQUE_ID)
            .setSystemClusterStatus(SYSTEM_CLUSTER_STATUS)
            .setSystemClusterCreatedDate(SYSTEM_CLUSTER_CREATED_DATE)
            .setSystemClusterDeletedDate(SYSTEM_CLUSTER_DELETED_DATE)
            .setRestoreJobId(RESTORE_JOB_ID)
            .setRestoreStatus(RESTORE_STATUS)
            .setRestoreStartDate(RESTORE_START_DATE)
            .setRestoreEndDate(RESTORE_END_DATE)
            .setExecutionStatus(EXECUTION_STATUS)
            .setExecutionStartDate(EXECUTION_START_DATE)
            .setExecutionEndDate(EXECUTION_END_DATE);
  }

  private ShadowClusterJob getShadowClusterJob() {
    return (ShadowClusterJob)
        new ShadowClusterJob(
                SOURCE_PROJECT_ID,
                SOURCE_CLUSTER_NAME,
                SOURCE_CLUSTER_UNIQUE_ID,
                ORG_ID,
                SOURCE_SNAPSHOT_ID,
                SOURCE_PIT_TIME,
                REQUESTING_USER,
                new ObjectId(),
                new ObjectId(),
                ShadowCluster.Status.PROVISIONING,
                "requested",
                new Date())
            .setSystemProjectId(SYSTEM_PROJECT_ID)
            .setSystemProjectStatus(SYSTEM_PROJECT_STATUS)
            .setSystemProjectCreatedDate(SYSTEM_PROJECT_CREATED_DATE)
            .setSystemProjectDeletedDate(SYSTEM_PROJECT_DELETED_DATE)
            .setSystemClusterName(SYSTEM_CLUSTER_NAME)
            .setSystemClusterUniqueId(SYSTEM_CLUSTER_UNIQUE_ID)
            .setSystemClusterStatus(SYSTEM_CLUSTER_STATUS)
            .setSystemClusterCreatedDate(SYSTEM_CLUSTER_CREATED_DATE)
            .setSystemClusterDeletedDate(SYSTEM_CLUSTER_DELETED_DATE)
            .setRestoreJobId(RESTORE_JOB_ID)
            .setRestoreStatus(RESTORE_STATUS)
            .setRestoreStartDate(RESTORE_START_DATE)
            .setRestoreEndDate(RESTORE_END_DATE)
            .setExecutionStatus(EXECUTION_STATUS)
            .setExecutionStartDate(EXECUTION_START_DATE)
            .setExecutionEndDate(EXECUTION_END_DATE);
  }

  private Export getExport() {
    return Export.Builder.aExport()
        .region("us-east-1")
        .prefix("imAPrefix")
        .snapshotDiskSizeInBytes(2048)
        .exportBucketId(ObjectId.get())
        .exportBucketCloudProvider(CloudProvider.AWS)
        .bucket("some-bucket")
        .type(ExportType.MANUAL)
        .exportStatusBuilder(getExportStatusBuilder())
        .exportConfig(new Export.ExportConfig(Export.ExportFormat.JSON))
        .build();
  }

  private ExportStatus.Builder getExportStatusBuilder() {
    return ExportStatus.Builder.anExportStatus()
        .exportedCollections(0)
        .totalCollections(100)
        .uncompressedBytesExported(0)
        .uncompressedRawBytesExported(0)
        .numDocs(1000)
        .storageSize(15000)
        .dataSize(10000)
        .compressedBytesExported(0)
        .numFilesUploaded(0L);
  }

  @Test
  public void testBulkSave_EmptyList() {
    _systemClusterJobDao.bulkSave(List.of());

    // Verify no changes were made
    final int finalCount = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID).size();
    assertEquals(0, finalCount);
  }

  @Test
  public void testBulkSave_SingleJob() {
    final SystemClusterExportJob exportJob = getSystemClusterExportJob();

    _systemClusterJobDao.bulkSave(List.of(exportJob));

    // Verify job was saved
    final Optional<SystemClusterJob> savedJob = _systemClusterJobDao.findById(exportJob.getId());
    assertTrue(savedJob.isPresent());
    assertEquals(exportJob.getId(), savedJob.get().getId());
    assertEquals(exportJob.getSourceProjectId(), savedJob.get().getSourceProjectId());
    assertEquals(exportJob.getSystemClusterName(), savedJob.get().getSystemClusterName());
  }

  @Test
  public void testBulkSave_MultipleJobs() {
    final SystemClusterExportJob exportJob1 = getSystemClusterExportJob();
    final SystemClusterExportJob exportJob2 = getSystemClusterExportJob();
    exportJob2.setSystemClusterName("different-export-job");
    final CollectionRestoreJob restoreJob = getCollectionRestoreJob();

    final List<SystemClusterJob> jobs = List.of(exportJob1, exportJob2, restoreJob);
    _systemClusterJobDao.bulkSave(jobs);

    // Verify all jobs were saved
    for (SystemClusterJob job : jobs) {
      final Optional<SystemClusterJob> savedJob = _systemClusterJobDao.findById(job.getId());
      assertTrue(savedJob.isPresent(), "Job with ID " + job.getId() + " should be saved");
      assertEquals(job.getId(), savedJob.get().getId());
      assertEquals(job.getExecutionOption(), savedJob.get().getExecutionOption());
    }

    // Verify jobs can be found by org
    final List<SystemClusterJob> jobsByOrg = _systemClusterJobDao.findAllJobsByOrgId(ORG_ID);
    assertTrue(jobsByOrg.size() >= 3, "Should have at least 3 jobs for the org");
  }

  @Test
  public void testBulkSave_UpsertBehavior() {
    // Create and save initial jobs
    final SystemClusterExportJob exportJob = getSystemClusterExportJob();
    final CollectionRestoreJob restoreJob = getCollectionRestoreJob();

    _systemClusterJobDao.bulkSave(List.of(exportJob, restoreJob));

    // Verify initial save
    Optional<SystemClusterJob> savedExportJob = _systemClusterJobDao.findById(exportJob.getId());
    Optional<SystemClusterJob> savedRestoreJob = _systemClusterJobDao.findById(restoreJob.getId());
    assertTrue(savedExportJob.isPresent());
    assertTrue(savedRestoreJob.isPresent());
    assertEquals(SystemClusterJobStatus.NOT_STARTED, savedExportJob.get().getExecutionStatus());
    assertEquals(SystemClusterJobStatus.NOT_STARTED, savedRestoreJob.get().getExecutionStatus());

    // Modify jobs and bulk save again (upsert)
    exportJob.setExecutionStatus(SystemClusterJobStatus.IN_PROGRESS);
    restoreJob.setExecutionStatus(SystemClusterJobStatus.SUCCESSFUL);

    _systemClusterJobDao.bulkSave(List.of(exportJob, restoreJob));

    // Verify updates were applied
    savedExportJob = _systemClusterJobDao.findById(exportJob.getId());
    savedRestoreJob = _systemClusterJobDao.findById(restoreJob.getId());
    assertTrue(savedExportJob.isPresent());
    assertTrue(savedRestoreJob.isPresent());
    assertEquals(SystemClusterJobStatus.IN_PROGRESS, savedExportJob.get().getExecutionStatus());
    assertEquals(SystemClusterJobStatus.SUCCESSFUL, savedRestoreJob.get().getExecutionStatus());
  }

  @Test
  public void testBulkSave_MixedJobTypes() {
    final SystemClusterExportJob exportJob = getSystemClusterExportJob();
    final CollectionRestoreJob restoreJob = getCollectionRestoreJob();
    final ShadowClusterJob shadowJob = getShadowClusterJob();

    final List<SystemClusterJob> mixedJobs = List.of(exportJob, restoreJob, shadowJob);
    _systemClusterJobDao.bulkSave(mixedJobs);

    // Verify all different job types were saved correctly
    final Optional<SystemClusterJob> savedExportJob =
        _systemClusterJobDao.findById(exportJob.getId());
    final Optional<SystemClusterJob> savedRestoreJob =
        _systemClusterJobDao.findById(restoreJob.getId());
    final Optional<SystemClusterJob> savedShadowJob =
        _systemClusterJobDao.findById(shadowJob.getId());

    assertTrue(savedExportJob.isPresent());
    assertTrue(savedRestoreJob.isPresent());
    assertTrue(savedShadowJob.isPresent());

    // Verify job types are preserved
    assertEquals(ExecutionOption.BACKUP_EXPORT, savedExportJob.get().getExecutionOption());
    assertEquals(ExecutionOption.DB_COLLECTION_RESTORE, savedRestoreJob.get().getExecutionOption());
    assertEquals(ExecutionOption.SHADOW_CLUSTER, savedShadowJob.get().getExecutionOption());

    // Verify specific job type casting works
    assertTrue(savedExportJob.get() instanceof SystemClusterExportJob);
    assertTrue(savedRestoreJob.get() instanceof CollectionRestoreJob);
    assertTrue(savedShadowJob.get() instanceof ShadowClusterJob);
  }

  @Test
  public void testBulkSave_RetrievalAfterSave() {
    final ObjectId testOrgId = ObjectId.get();
    final ObjectId testProjectId = ObjectId.get();
    final ObjectId testClusterUniqueId = ObjectId.get();

    // Create jobs with specific test IDs for isolation using constructor parameters
    final SystemClusterExportJob exportJob1 =
        (SystemClusterExportJob)
            new SystemClusterExportJob(
                    testProjectId,
                    "test-cluster-1",
                    testClusterUniqueId,
                    testOrgId,
                    SOURCE_SNAPSHOT_ID,
                    SOURCE_PIT_TIME,
                    REQUESTING_USER,
                    getExport(),
                    EXPORT_AGENT_ERROR_MESSAGES)
                .setSystemClusterName("bulk-test-export-1");

    final SystemClusterExportJob exportJob2 =
        (SystemClusterExportJob)
            new SystemClusterExportJob(
                    testProjectId,
                    "test-cluster-2",
                    testClusterUniqueId,
                    testOrgId,
                    SOURCE_SNAPSHOT_ID,
                    SOURCE_PIT_TIME,
                    REQUESTING_USER,
                    getExport(),
                    EXPORT_AGENT_ERROR_MESSAGES)
                .setSystemClusterName("bulk-test-export-2");

    final List<SystemClusterJob> testJobs = List.of(exportJob1, exportJob2);
    _systemClusterJobDao.bulkSave(testJobs);

    // Test retrieval by org ID
    final List<SystemClusterJob> jobsByOrg = _systemClusterJobDao.findAllJobsByOrgId(testOrgId);
    assertEquals(2, jobsByOrg.size());
    assertTrue(jobsByOrg.stream().anyMatch(job -> job.getId().equals(exportJob1.getId())));
    assertTrue(jobsByOrg.stream().anyMatch(job -> job.getId().equals(exportJob2.getId())));

    // Test retrieval by source cluster
    final List<SystemClusterJob> jobsByCluster =
        _systemClusterJobDao.findAllJobsBySourceCluster(testProjectId, testClusterUniqueId);
    assertEquals(2, jobsByCluster.size());
    assertTrue(jobsByCluster.stream().anyMatch(job -> job.getId().equals(exportJob1.getId())));
    assertTrue(jobsByCluster.stream().anyMatch(job -> job.getId().equals(exportJob2.getId())));

    // Test individual retrieval by ID
    final Optional<SystemClusterJob> foundJob1 = _systemClusterJobDao.findById(exportJob1.getId());
    final Optional<SystemClusterJob> foundJob2 = _systemClusterJobDao.findById(exportJob2.getId());

    assertTrue(foundJob1.isPresent());
    assertTrue(foundJob2.isPresent());
    assertEquals("bulk-test-export-1", foundJob1.get().getSystemClusterName());
    assertEquals("bulk-test-export-2", foundJob2.get().getSystemClusterName());
  }
}
