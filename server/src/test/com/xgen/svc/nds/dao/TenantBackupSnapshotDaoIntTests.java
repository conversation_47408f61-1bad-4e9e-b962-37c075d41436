package com.xgen.svc.nds.dao;

import static com.xgen.svc.nds.svc.NDSTenantSnapshotRestoreSvc.CUSTOM_TASK_TIMEOUT_OVERRIDE_IN_SECONDS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask.State;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.TenantBackupSnapshot;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class TenantBackupSnapshotDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private TenantBackupSnapshotDao _dao;

  @Test
  public void testCreateRetrieve() {
    final TenantBackupSnapshot snapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, ObjectId.get(), ObjectId.get(), new Date());
    final ObjectId id = snapshot.getId();

    _dao.insertReplicaSafe(snapshot);
    final Optional<TenantBackupSnapshot> findResult = _dao.find(id);
    assertEquals(findResult.get(), snapshot);
  }

  @Test
  public void testFindByCluster() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId0 = ObjectId.get();
    final ObjectId uniqueId1 = ObjectId.get();
    final String clusterName1 = "testCluster1";
    final String clusterName2 = "testCluster2";
    final TenantBackupSnapshot snapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, groupId, uniqueId0, clusterName1, new Date());
    final TenantBackupSnapshot snapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.PENDING,
            groupId,
            uniqueId0,
            clusterName1,
            DateUtils.addHours(new Date(), 1));
    final TenantBackupSnapshot snapshot2 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.PENDING,
            groupId,
            uniqueId1,
            clusterName2,
            DateUtils.addHours(new Date(), 2));

    _dao.insertReplicaSafe(snapshot0);
    _dao.insertReplicaSafe(snapshot1);
    _dao.insertReplicaSafe(snapshot2);

    final List<TenantBackupSnapshot> snapshots0 =
        _dao.findByCluster(groupId, clusterName1, TenantBackupTask.SnapshotType.BACKUP);
    assertEquals(2, snapshots0.size());
    assertEquals(snapshot1, snapshots0.get(0));
    assertEquals(snapshot0, snapshots0.get(1));

    final List<TenantBackupSnapshot> snapshots1 =
        _dao.findByCluster(groupId, clusterName2, TenantBackupTask.SnapshotType.BACKUP);
    assertEquals(1, snapshots1.size());
    assertEquals(snapshot2, snapshots1.get(0));

    final List<TenantBackupSnapshot> snapshotsNoMatching =
        _dao.findByCluster(groupId, "foobar", TenantBackupTask.SnapshotType.BACKUP);
    assertEquals(0, snapshotsNoMatching.size());
  }

  @Test
  public void testFindMostRecentSnapshotsByCluster() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId = ObjectId.get();
    final Date now = new Date();
    final TenantBackupSnapshot snapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud", State.PENDING, groupId, uniqueId, now)
            .copy()
            .setScheduledDeletionDate(null)
            .build();
    final TenantBackupSnapshot snapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud",
                State.PENDING,
                groupId,
                uniqueId,
                DateUtils.addDays(now, 1))
            .copy()
            .setScheduledDeletionDate(null)
            .build();
    assertNull(snapshot0.getScheduledDeletionDate());
    assertNull(snapshot1.getScheduledDeletionDate());

    assertTrue(
        _dao.findMostRecentSnapshotsByCluster(
                groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP, 1)
            .isEmpty());

    _dao.insertReplicaSafe(snapshot0);
    _dao.insertReplicaSafe(snapshot1);

    // limit is more than db object count
    {
      final List<TenantBackupSnapshot> mostRecentSnapshots =
          _dao.findMostRecentSnapshotsByCluster(
              groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP, 5);
      assertEquals(Set.of(snapshot0, snapshot1), Set.copyOf(mostRecentSnapshots));
    }

    // limit is less than db object count
    {
      final List<TenantBackupSnapshot> mostRecentSnapshots =
          _dao.findMostRecentSnapshotsByCluster(
              groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP, 1);
      assertEquals(Set.of(snapshot1), Set.copyOf(mostRecentSnapshots));
    }

    // should not return anything if the wrong snapshot type is specified
    assertTrue(
        _dao.findMostRecentSnapshotsByCluster(
                groupId, uniqueId, TenantBackupTask.SnapshotType.M0PAUSE, 5)
            .isEmpty());

    // if scheduled deletion date is present, will still be considered as a recent snapshot
    final TenantBackupSnapshot snapshot2 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, groupId, uniqueId, DateUtils.addDays(now, 2));
    assertNotNull(snapshot2.getScheduledDeletionDate());
    _dao.insertReplicaSafe(snapshot2);

    {
      final List<TenantBackupSnapshot> mostRecentSnapshots =
          _dao.findMostRecentSnapshotsByCluster(
              groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP, 5);
      assertEquals(Set.of(snapshot0, snapshot1, snapshot2), Set.copyOf(mostRecentSnapshots));
    }
  }

  @Test
  public void testFindMostRecentSnapshotByClusterNotDeleted() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId = ObjectId.get();
    final TenantBackupSnapshot snapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud", State.PENDING, groupId, uniqueId, new Date())
            .copy()
            .setScheduledDeletionDate(null)
            .build();
    final TenantBackupSnapshot snapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud",
                State.PENDING,
                groupId,
                uniqueId,
                DateUtils.addDays(new Date(), 1))
            .copy()
            .setScheduledDeletionDate(null)
            .build();
    assertNull(snapshot0.getScheduledDeletionDate());
    assertNull(snapshot1.getScheduledDeletionDate());

    assertFalse(
        _dao.findMostRecentSnapshotByClusterNotDeleted(
                groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP)
            .isPresent());

    _dao.insertReplicaSafe(snapshot0);
    _dao.insertReplicaSafe(snapshot1);

    final Optional<TenantBackupSnapshot> mostRecentSnapshot0 =
        _dao.findMostRecentSnapshotByClusterNotDeleted(
            groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP);
    assertTrue(mostRecentSnapshot0.isPresent());
    assertEquals(snapshot1, mostRecentSnapshot0.get());
    // Should not be present for the wrong snapshot type
    assertFalse(
        _dao.findMostRecentSnapshotByClusterNotDeleted(
                groupId, uniqueId, TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());

    // If deletion date is present, will not be considered the most recent snapshot
    final TenantBackupSnapshot snapshot2 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.PENDING,
            groupId,
            uniqueId,
            DateUtils.addDays(new Date(), 2));
    _dao.insertReplicaSafe(snapshot2.copy().setScheduledDeletionDate(new Date()).build());

    final Optional<TenantBackupSnapshot> mostRecentSnapshot1 =
        _dao.findMostRecentSnapshotByClusterNotDeleted(
            groupId, uniqueId, TenantBackupTask.SnapshotType.BACKUP);
    assertTrue(mostRecentSnapshot1.isPresent());
    assertEquals(snapshot1, mostRecentSnapshot1.get());
    assertFalse(
        _dao.findMostRecentSnapshotByClusterNotDeleted(
                groupId, uniqueId, TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());
  }

  @Test
  public void testFindMostRecentCompletedByCluster() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId = ObjectId.get();
    final TenantBackupSnapshot snapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.COMPLETED, groupId, uniqueId, new Date());
    final TenantBackupSnapshot snapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.PENDING,
            groupId,
            uniqueId,
            DateUtils.addDays(new Date(), 1));

    assertFalse(_dao.findMostRecentCompletedByCluster(groupId, uniqueId).isPresent());

    _dao.insertReplicaSafe(snapshot1);
    assertFalse(_dao.findMostRecentCompletedByCluster(groupId, uniqueId).isPresent());

    _dao.insertReplicaSafe(snapshot0);

    final Optional<TenantBackupSnapshot> mostRecentSnapshot =
        _dao.findMostRecentCompletedByCluster(groupId, uniqueId);
    assertTrue(mostRecentSnapshot.isPresent());
    assertEquals(snapshot0, mostRecentSnapshot.get());
  }

  @Test
  public void testFindOldestCompletedActiveByCluster() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId = ObjectId.get();
    final Date now = new Date();

    final TenantBackupSnapshot snapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.COMPLETED, groupId, uniqueId, now);
    final TenantBackupSnapshot snapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, groupId, uniqueId, DateUtils.addDays(now, 1));
    final TenantBackupSnapshot olderActiveSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.COMPLETED,
            groupId,
            uniqueId,
            Date.from(now.toInstant().minus(Duration.ofDays(1))));
    final TenantBackupSnapshot evenOlderSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.COMPLETED,
            groupId,
            uniqueId,
            Date.from(now.toInstant().minus(Duration.ofDays(2))));
    final TenantBackupSnapshot expiredOlderSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud",
                State.COMPLETED,
                groupId,
                uniqueId,
                Date.from(now.toInstant().minus(Duration.ofDays(3))))
            .copy()
            .setDeleted(true)
            .setPurged(true)
            .setDeletionDate(Date.from(now.toInstant().minus(Duration.ofHours(1))))
            .build();

    assertFalse(_dao.findOldestCompletedActiveByCluster(groupId, uniqueId).isPresent());

    _dao.insertReplicaSafe(snapshot1);
    _dao.insertReplicaSafe(snapshot0);
    _dao.insertReplicaSafe(olderActiveSnapshot);
    _dao.insertReplicaSafe(evenOlderSnapshot);
    _dao.insertReplicaSafe(expiredOlderSnapshot);

    final Optional<TenantBackupSnapshot> oldestSnapshot =
        _dao.findOldestCompletedActiveByCluster(groupId, uniqueId);
    assertTrue(oldestSnapshot.isPresent());
    assertEquals(evenOlderSnapshot, oldestSnapshot.get());
  }

  @Test
  public void testFindIncompleteByHandlingProxyHost() {
    final String fooHost = "proxy.foo.mongodbcloud";
    final String barHost = "proxy.bar.mongodbcloud";
    Arrays.stream(State.values())
        .forEach(
            s -> {
              _dao.insertReplicaSafe(
                  NDSModelTestFactory.getTenantBackupSnapshot(
                      fooHost, s, ObjectId.get(), ObjectId.get(), new Date()));
              _dao.insertReplicaSafe(
                  NDSModelTestFactory.getTenantBackupSnapshot(
                      barHost, s, ObjectId.get(), ObjectId.get(), new Date()));
            });
    final List<TenantBackupSnapshot> incompleteByFooHost =
        _dao.findIncompleteByHandlingProxyHost(fooHost);
    final List<TenantBackupSnapshot> incompleteByBarHost =
        _dao.findIncompleteByHandlingProxyHost(barHost);
    assertEquals(3, incompleteByFooHost.size());
    assertEquals(State.PENDING, incompleteByFooHost.get(0).getState());
    assertEquals(State.QUEUED, incompleteByFooHost.get(1).getState());
    assertEquals(State.RUNNING, incompleteByFooHost.get(2).getState());

    assertEquals(3, incompleteByBarHost.size());
    assertEquals(State.PENDING, incompleteByBarHost.get(0).getState());
    assertEquals(State.QUEUED, incompleteByBarHost.get(1).getState());
    assertEquals(State.RUNNING, incompleteByBarHost.get(2).getState());

    assertTrue(incompleteByFooHost.stream().noneMatch(incompleteByBarHost::contains));
    assertTrue(incompleteByBarHost.stream().noneMatch(incompleteByFooHost::contains));
  }

  @Test
  public void testFindPendingForCluster() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId clusterUniqueId = ObjectId.get();
    final TenantBackupSnapshot completeSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.COMPLETED, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot failedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.FAILED, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.RUNNING, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.QUEUED, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, groupId, clusterUniqueId, new Date());

    // Complete snapshot.
    _dao.insertReplicaSafe(completeSnapshot);

    assertTrue(_dao.findIncompleteByCluster(groupId, "testCluster").isEmpty());

    // Failed snapshot.
    _dao.insertReplicaSafe(failedSnapshot);

    assertTrue(_dao.findIncompleteByCluster(groupId, "testCluster").isEmpty());

    // Running snapshot.
    _dao.insertReplicaSafe(runningSnapshot);

    Optional<TenantBackupSnapshot> snapshot = _dao.findIncompleteByCluster(groupId, "testCluster");
    assertTrue(snapshot.isPresent());
    assertEquals(runningSnapshot, snapshot.get());

    _dao.updateTaskState(
        runningSnapshot.getId(),
        Optional.of(State.COMPLETED),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty());

    // Queued snapshot.
    _dao.insertReplicaSafe(queuedSnapshot);

    snapshot = _dao.findIncompleteByCluster(groupId, "testCluster");
    assertTrue(snapshot.isPresent());
    assertEquals(queuedSnapshot, snapshot.get());

    _dao.updateTaskState(
        queuedSnapshot.getId(),
        Optional.of(State.COMPLETED),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty());

    // Pending snapshot.
    _dao.insertReplicaSafe(pendingSnapshot);

    snapshot = _dao.findIncompleteByCluster(groupId, "testCluster");
    assertTrue(snapshot.isPresent());
    assertEquals(pendingSnapshot, snapshot.get());
  }

  @Test
  public void updateTaskState() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId = ObjectId.get();
    final Date startedDate = new Date();
    final Date finishedDate = Date.from(startedDate.toInstant().plus(Duration.ofMinutes(1)));
    final Long usedDiskSpace = 808L;
    final BSONTimestamp lastOplogEntryTimestamp = new BSONTimestamp(333, 3);
    final TenantBackupSnapshot snapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, groupId, uniqueId, new Date());

    _dao.insertMajority(snapshot0);

    _dao.updateTaskState(
        snapshot0.getId(),
        Optional.of(State.QUEUED),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty());

    final Optional<TenantBackupSnapshot> snapshot1 = _dao.findById(snapshot0.getId());

    // Only the state should have changed since the last call
    assertTrue(snapshot1.isPresent());
    assertEquals(snapshot0.copy().setState(State.QUEUED).build(), snapshot1.get());

    _dao.updateTaskState(
        snapshot0.getId(),
        Optional.empty(),
        Optional.of(startedDate),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty());

    final Optional<TenantBackupSnapshot> snapshot2 = _dao.findById(snapshot0.getId());

    // Only startedDate should have changed since the last call
    assertTrue(snapshot2.isPresent());
    assertEquals(snapshot1.get().copy().setStartedDate(startedDate).build(), snapshot2.get());

    _dao.updateTaskState(
        snapshot0.getId(),
        Optional.empty(),
        Optional.empty(),
        Optional.of(finishedDate),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty());

    final Optional<TenantBackupSnapshot> snapshot3 = _dao.findById(snapshot0.getId());

    // Only finishedDate should have changed since the last call
    assertTrue(snapshot3.isPresent());
    assertEquals(snapshot2.get().copy().setFinishedDate(finishedDate).build(), snapshot3.get());

    _dao.updateTaskState(
        snapshot0.getId(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.of(usedDiskSpace),
        Optional.empty(),
        Optional.empty());

    final Optional<TenantBackupSnapshot> snapshot4 = _dao.findById(snapshot0.getId());

    // Only usedDiskSpace should have changed since the last call
    assertTrue(snapshot4.isPresent());
    assertEquals(snapshot3.get().copy().setUsedDiskSpace(usedDiskSpace).build(), snapshot4.get());

    _dao.updateTaskState(
        snapshot0.getId(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.of(lastOplogEntryTimestamp),
        Optional.empty());

    final Optional<TenantBackupSnapshot> snapshot5 = _dao.findById(snapshot0.getId());

    // Only lastOplogEntryTimestamp should have changed since the last call
    assertTrue(snapshot5.isPresent());
    assertEquals(
        snapshot4.get().copy().setLastOplogEntryTimestamp(lastOplogEntryTimestamp).build(),
        snapshot5.get());

    // change error
    final String error = "timed out running snapshot";
    _dao.updateTaskState(
        snapshot0.getId(),
        Optional.empty(),
        Optional.empty(),
        Optional.empty(),
        Optional.of(error),
        Optional.empty(),
        Optional.empty(),
        Optional.of(CUSTOM_TASK_TIMEOUT_OVERRIDE_IN_SECONDS));

    final Optional<TenantBackupSnapshot> snapshot6 = _dao.findById(snapshot0.getId());

    // Error and task_timeout should have changed since last call
    assertTrue(snapshot6.isPresent());
    assertEquals(
        snapshot5
            .get()
            .copy()
            .setError(error)
            .setTaskTimeout(CUSTOM_TASK_TIMEOUT_OVERRIDE_IN_SECONDS)
            .build(),
        snapshot6.get());
  }

  @Test
  public void testGetVisibleSnapshotsForCluster() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId clusterUniqueId = ObjectId.get();

    // Visible
    final TenantBackupSnapshot completeSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.COMPLETED, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot failedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.FAILED, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.RUNNING, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.QUEUED, groupId, clusterUniqueId, new Date());
    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud", State.PENDING, groupId, clusterUniqueId, new Date());

    // Not visible
    final TenantBackupSnapshot completeDeletedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud", State.COMPLETED, groupId, clusterUniqueId, new Date())
            .copy()
            .setDeleted(true)
            .build();
    final String clusterName = completeSnapshot.getClusterName();
    final TenantBackupSnapshot m0PauseSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.RUNNING,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date(),
            TenantBackupTask.SnapshotType.M0PAUSE,
            null,
            null);

    _dao.insertMajority(completeSnapshot);
    _dao.insertMajority(completeDeletedSnapshot);
    _dao.insertMajority(failedSnapshot);
    _dao.insertMajority(runningSnapshot);
    _dao.insertMajority(queuedSnapshot);
    _dao.insertMajority(pendingSnapshot);
    _dao.insertMajority(m0PauseSnapshot);

    List<TenantBackupSnapshot> result =
        _dao.getVisibleSnapshotsForCluster(
            groupId, clusterName, Set.of(TenantBackupTask.SnapshotType.BACKUP));

    assertEquals(
        Set.of(completeSnapshot, failedSnapshot, runningSnapshot, queuedSnapshot, pendingSnapshot),
        Set.copyOf(result));
  }

  @Test
  public void testFindByClusterAndId() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId clusterUniqueId = ObjectId.get();
    final String clusterName = "testCluster";

    final TenantBackupSnapshot completeBackupSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.COMPLETED,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date());
    final TenantBackupSnapshot completeM0PauseSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.COMPLETED,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date(),
            TenantBackupTask.SnapshotType.M0PAUSE,
            null,
            null);
    final TenantBackupSnapshot completeDeletedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.foo.mongodbcloud",
                State.COMPLETED,
                groupId,
                clusterUniqueId,
                clusterName,
                new Date())
            .copy()
            .setDeleted(true)
            .build();

    final TenantBackupSnapshot failedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.FAILED,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date());
    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.RUNNING,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date());
    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.QUEUED,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date());
    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.foo.mongodbcloud",
            State.PENDING,
            groupId,
            clusterUniqueId,
            clusterName,
            new Date());

    _dao.insertMajority(completeBackupSnapshot);
    _dao.insertMajority(completeM0PauseSnapshot);
    _dao.insertMajority(completeDeletedSnapshot);
    _dao.insertMajority(failedSnapshot);
    _dao.insertMajority(runningSnapshot);
    _dao.insertMajority(queuedSnapshot);
    _dao.insertMajority(pendingSnapshot);

    assertEquals(
        Optional.of(completeBackupSnapshot),
        _dao.findByClusterAndId(
            groupId,
            clusterName,
            completeBackupSnapshot.getId(),
            TenantBackupTask.SnapshotType.BACKUP));

    assertEquals(
        Optional.of(completeM0PauseSnapshot),
        _dao.findByClusterAndId(
            groupId,
            clusterName,
            completeM0PauseSnapshot.getId(),
            TenantBackupTask.SnapshotType.M0PAUSE));

    assertEquals(
        Optional.of(failedSnapshot),
        _dao.findByClusterAndId(
            groupId, clusterName, failedSnapshot.getId(), TenantBackupTask.SnapshotType.BACKUP));

    assertEquals(
        Optional.of(runningSnapshot),
        _dao.findByClusterAndId(
            groupId, clusterName, runningSnapshot.getId(), TenantBackupTask.SnapshotType.BACKUP));

    assertEquals(
        Optional.of(queuedSnapshot),
        _dao.findByClusterAndId(
            groupId, clusterName, queuedSnapshot.getId(), TenantBackupTask.SnapshotType.BACKUP));

    assertEquals(
        Optional.of(pendingSnapshot),
        _dao.findByClusterAndId(
            groupId, clusterName, pendingSnapshot.getId(), TenantBackupTask.SnapshotType.BACKUP));

    assertEquals(
        Optional.of(completeDeletedSnapshot),
        _dao.findByClusterAndId(
            groupId,
            clusterName,
            completeDeletedSnapshot.getId(),
            TenantBackupTask.SnapshotType.BACKUP));

    // Incorrect Snapshot Type
    assertFalse(
        _dao.findByClusterAndId(
                groupId,
                clusterName,
                completeBackupSnapshot.getId(),
                TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                groupId,
                clusterName,
                completeM0PauseSnapshot.getId(),
                TenantBackupTask.SnapshotType.BACKUP)
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                groupId, clusterName, failedSnapshot.getId(), TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                groupId,
                clusterName,
                runningSnapshot.getId(),
                TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                groupId, clusterName, queuedSnapshot.getId(), TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                groupId,
                clusterName,
                pendingSnapshot.getId(),
                TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                groupId,
                clusterName,
                completeDeletedSnapshot.getId(),
                TenantBackupTask.SnapshotType.M0PAUSE)
            .isPresent());

    // Incorrect groupId
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                completeBackupSnapshot.getId(),
                completeBackupSnapshot.getSnapshotType())
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                completeM0PauseSnapshot.getId(),
                completeM0PauseSnapshot.getSnapshotType())
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                completeDeletedSnapshot.getId(),
                completeDeletedSnapshot.getSnapshotType())
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                failedSnapshot.getId(),
                failedSnapshot.getSnapshotType())
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                runningSnapshot.getId(),
                runningSnapshot.getSnapshotType())
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                queuedSnapshot.getId(),
                queuedSnapshot.getSnapshotType())
            .isPresent());
    assertFalse(
        _dao.findByClusterAndId(
                ObjectId.get(),
                clusterName,
                pendingSnapshot.getId(),
                pendingSnapshot.getSnapshotType())
            .isPresent());
  }

  @Test
  public void testScheduleBackupDeleted() {
    final ObjectId groupId = new ObjectId();
    final ObjectId clusterUniqueId = new ObjectId();

    final TenantBackupSnapshot snapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, new Date())
            .copy()
            .setScheduledDeletionDate(null)
            .build();

    _dao.insertMajority(snapshot);

    final TenantBackupSnapshot preRunSnapshot =
        _dao.getCollection().find().into(new ArrayList<>()).get(0);
    assertNull(preRunSnapshot.getScheduledDeletionDate());

    final Date scheduledDeletionDate = new Date();
    _dao.scheduleBackupDeletion(snapshot.getId(), scheduledDeletionDate);

    final TenantBackupSnapshot postRunSnapshot =
        _dao.getCollection().find().into(new ArrayList<>()).get(0);
    assertNotNull(postRunSnapshot.getScheduledDeletionDate());
    assertEquals(
        snapshot.copy().setScheduledDeletionDate(scheduledDeletionDate).build(), postRunSnapshot);
  }

  @Test
  public void testMarkAsDeleted() {
    final ObjectId groupId = new ObjectId();
    final ObjectId clusterUniqueId = new ObjectId();

    final Date testOffsetDate = new Date();
    final Date fiveMinAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofMinutes(5)));
    final Date oneDayAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofDays(1)));
    final Date inOneDay = Date.from(testOffsetDate.toInstant().plus(Duration.ofHours(1)));

    final TenantBackupSnapshot overdueDeletion =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot completedDeletion =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setDeleted(true)
            .setDeletionDate(testOffsetDate)
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot notYetDueDeletion =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot failedOverdueSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot failedDeletedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setDeleted(true)
            .setDeletionDate(testOffsetDate)
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot failedNotYetDueSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.PENDING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.QUEUED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.RUNNING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    _dao.insertMajority(overdueDeletion);
    _dao.insertMajority(completedDeletion);
    _dao.insertMajority(notYetDueDeletion);
    _dao.insertMajority(failedOverdueSnapshot);
    _dao.insertMajority(failedDeletedSnapshot);
    _dao.insertMajority(failedNotYetDueSnapshot);
    _dao.insertMajority(pendingSnapshot);
    _dao.insertMajority(queuedSnapshot);
    _dao.insertMajority(runningSnapshot);

    // Deleting an overdue completed snapshot should set the deletion flag & timestamp

    _dao.markAsDeleted(overdueDeletion.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedOverdueDeletion =
        overdueDeletion.copy().setDeleted(true).setDeletionDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            notYetDueDeletion,
            failedOverdueSnapshot,
            failedDeletedSnapshot,
            failedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Trying to delete an already-deleted snapshot should have no effect

    _dao.markAsDeleted(completedDeletion.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            notYetDueDeletion,
            failedOverdueSnapshot,
            failedDeletedSnapshot,
            failedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Trying to delete a backup that is not yet deleted or due for deletion _SHOULD_ delete it (so
    // this method can be used when the cluster is deleted)

    _dao.markAsDeleted(notYetDueDeletion.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedNotYetDueDeletion =
        notYetDueDeletion.copy().setDeleted(true).setDeletionDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            updatedNotYetDueDeletion,
            failedOverdueSnapshot,
            failedDeletedSnapshot,
            failedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Deleting an overdue failed snapshot should set the flag and deletion timestamp

    _dao.markAsDeleted(failedOverdueSnapshot.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedFailedOverdueSnapshot =
        failedOverdueSnapshot.copy().setDeleted(true).setDeletionDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            updatedNotYetDueDeletion,
            updatedFailedOverdueSnapshot,
            failedDeletedSnapshot,
            failedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Deleting a failed snapshot that is already deleted should do nothing

    _dao.markAsDeleted(failedDeletedSnapshot.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            updatedNotYetDueDeletion,
            updatedFailedOverdueSnapshot,
            failedDeletedSnapshot,
            failedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Deleting a failed snapshot not yet due, should delete it (for cluster deletion use)

    _dao.markAsDeleted(failedNotYetDueSnapshot.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedFailedNotYetDueSnapshot =
        failedNotYetDueSnapshot.copy().setDeleted(true).setDeletionDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            updatedNotYetDueDeletion,
            updatedFailedOverdueSnapshot,
            failedDeletedSnapshot,
            updatedFailedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Deleting pending, queued, or running snapshots, should do nothing:

    _dao.markAsDeleted(pendingSnapshot.getId(), fiveMinAgo);
    _dao.markAsDeleted(queuedSnapshot.getId(), fiveMinAgo);
    _dao.markAsDeleted(runningSnapshot.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverdueDeletion,
            completedDeletion,
            updatedNotYetDueDeletion,
            updatedFailedOverdueSnapshot,
            failedDeletedSnapshot,
            updatedFailedNotYetDueSnapshot,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));
  }

  @Test
  public void testMarkBackupsDeleted() {
    final ObjectId groupId = new ObjectId();
    final ObjectId clusterUniqueId = new ObjectId();

    final TenantBackupSnapshot pausedSnapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.host",
            State.PENDING,
            groupId,
            clusterUniqueId,
            "BAR",
            new Date(),
            TenantBackupTask.SnapshotType.M0PAUSE,
            null,
            null);
    final TenantBackupSnapshot pausedSnapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.host",
            State.RUNNING,
            groupId,
            clusterUniqueId,
            "FOO",
            new Date(),
            TenantBackupTask.SnapshotType.M0PAUSE,
            null,
            null);
    final TenantBackupSnapshot backupSnapshot0 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.host", State.COMPLETED, groupId, clusterUniqueId, "ZIG", new Date());
    final TenantBackupSnapshot backupSnapshot1 =
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.host", State.FAILED, groupId, clusterUniqueId, "ZAG", new Date());

    _dao.insertMajority(pausedSnapshot0);
    _dao.insertMajority(pausedSnapshot1);
    _dao.insertMajority(backupSnapshot0);
    _dao.insertMajority(backupSnapshot1);

    assertEquals(
        Set.of(pausedSnapshot0, pausedSnapshot1, backupSnapshot0, backupSnapshot1),
        _dao.getCollection().find().into(new HashSet<>()));

    final Date deletionDate0 = DateUtils.addDays(new Date(), 10);
    final Date deletionDate1 = DateUtils.addDays(new Date(), 14);

    _dao.markAllBackupsDeleted(
        clusterUniqueId, deletionDate0, TenantBackupTask.SnapshotType.M0PAUSE);

    final TenantBackupSnapshot pausedSnapshot0_scheduledForDeletion =
        pausedSnapshot0.copy().setScheduledDeletionDate(deletionDate0).build();
    final TenantBackupSnapshot pausedSnapshot1_scheduledForDeletion =
        pausedSnapshot1.copy().setScheduledDeletionDate(deletionDate0).build();

    assertEquals(
        Set.of(
            pausedSnapshot0_scheduledForDeletion,
            pausedSnapshot1_scheduledForDeletion,
            backupSnapshot0,
            backupSnapshot1),
        _dao.getCollection().find().into(new HashSet<>()));

    _dao.markAllBackupsDeleted(
        clusterUniqueId, deletionDate1, TenantBackupTask.SnapshotType.BACKUP);

    final TenantBackupSnapshot backupSnapshot0_scheduledForDeletion =
        backupSnapshot0.copy().setScheduledDeletionDate(deletionDate1).build();
    final TenantBackupSnapshot backupSnapshot1_scheduledForDeletion =
        backupSnapshot1.copy().setScheduledDeletionDate(deletionDate1).build();

    assertEquals(
        Set.of(
            pausedSnapshot0_scheduledForDeletion,
            pausedSnapshot1_scheduledForDeletion,
            backupSnapshot0_scheduledForDeletion,
            backupSnapshot1_scheduledForDeletion),
        _dao.getCollection().find().into(new HashSet<>()));
  }

  @Test
  public void markAsPurged() {
    final ObjectId groupId = new ObjectId();
    final ObjectId clusterUniqueId = new ObjectId();

    final Date testOffsetDate = new Date();
    final Date fiveMinAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofMinutes(5)));
    final Date oneDayAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofDays(1)));
    final Date twoDaysAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofDays(2)));
    final Date inOneDay = Date.from(testOffsetDate.toInstant().plus(Duration.ofHours(1)));

    final TenantBackupSnapshot overduePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot completedPurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .setPurged(true)
            .setPurgedDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot notYetDuePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .setDeleted(true)
            .setDeletionDate(testOffsetDate)
            .build();

    final TenantBackupSnapshot failedOverduePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot failedPurgedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .setPurged(true)
            .setPurgedDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot failedNotYetDuePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .setDeleted(true)
            .setDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot completedButNotDeleted =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.PENDING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.QUEUED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.RUNNING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    _dao.insertMajority(overduePurge);
    _dao.insertMajority(completedPurge);
    _dao.insertMajority(notYetDuePurge);
    _dao.insertMajority(failedOverduePurge);
    _dao.insertMajority(failedPurgedSnapshot);
    _dao.insertMajority(failedNotYetDuePurge);
    _dao.insertMajority(completedButNotDeleted);
    _dao.insertMajority(pendingSnapshot);
    _dao.insertMajority(queuedSnapshot);
    _dao.insertMajority(runningSnapshot);

    // Purging an overdue completed deleted snapshot should set the purge flag & timestamp

    _dao.markAsPurged(overduePurge.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedOverduePurge =
        overduePurge.copy().setPurged(true).setPurgedDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            notYetDuePurge,
            failedOverduePurge,
            failedPurgedSnapshot,
            failedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Trying to purge an already-purged snapshot should have no effect

    _dao.markAsPurged(completedPurge.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            notYetDuePurge,
            failedOverduePurge,
            failedPurgedSnapshot,
            failedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Trying to purge a backup that is not yet purged or due for purging _SHOULD_ purge it, for use
    // in cluster deletion

    _dao.markAsPurged(notYetDuePurge.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedNotYetDuePurge =
        notYetDuePurge.copy().setPurged(true).setPurgedDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            updatedNotYetDuePurge,
            failedOverduePurge,
            failedPurgedSnapshot,
            failedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Purging an overdue failed snapshot should set the flag and timestamp

    _dao.markAsPurged(failedOverduePurge.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedFailedOverduePurge =
        failedOverduePurge.copy().setPurged(true).setPurgedDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            updatedNotYetDuePurge,
            updatedFailedOverduePurge,
            failedPurgedSnapshot,
            failedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Purging a failed snapshot that is already purged should do nothing

    _dao.markAsPurged(failedPurgedSnapshot.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            updatedNotYetDuePurge,
            updatedFailedOverduePurge,
            failedPurgedSnapshot,
            failedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Deleting a failed snapshot not yet due, should delete it (for cluster deletion use)

    _dao.markAsPurged(failedNotYetDuePurge.getId(), fiveMinAgo);

    final TenantBackupSnapshot updatedFailedNotYetDuePurge =
        failedNotYetDuePurge.copy().setPurged(true).setPurgedDate(fiveMinAgo).build();

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            updatedNotYetDuePurge,
            updatedFailedOverduePurge,
            failedPurgedSnapshot,
            updatedFailedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Purging a snapshot that is not deleted should do nothing

    _dao.markAsPurged(completedButNotDeleted.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            updatedNotYetDuePurge,
            updatedFailedOverduePurge,
            failedPurgedSnapshot,
            updatedFailedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));

    // Deleting pending, queued, or running snapshots, should do nothing:

    _dao.markAsPurged(pendingSnapshot.getId(), fiveMinAgo);
    _dao.markAsPurged(queuedSnapshot.getId(), fiveMinAgo);
    _dao.markAsPurged(runningSnapshot.getId(), fiveMinAgo);

    assertEquals(
        Set.of(
            updatedOverduePurge,
            completedPurge,
            updatedNotYetDuePurge,
            updatedFailedOverduePurge,
            failedPurgedSnapshot,
            updatedFailedNotYetDuePurge,
            completedButNotDeleted,
            pendingSnapshot,
            queuedSnapshot,
            runningSnapshot),
        _dao.getCollection().find().into(new HashSet<>()));
  }

  @Test
  public void testFindBackupsWithExpiryBefore() {
    final ObjectId groupId = new ObjectId();
    final ObjectId clusterUniqueId = new ObjectId();

    final Date testOffsetDate = new Date();
    final Date oneDayAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofDays(1)));
    final Date inOneDay = Date.from(testOffsetDate.toInstant().plus(Duration.ofHours(1)));

    final TenantBackupSnapshot overdueDeletion =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot completedDeletion =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setDeleted(true)
            .setDeletionDate(testOffsetDate)
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot notYetDueDeletion =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot failedOverdueSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot failedDeletedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setDeleted(true)
            .setDeletionDate(testOffsetDate)
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot failedNotYetDueSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.PENDING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.QUEUED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.RUNNING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    _dao.insertMajority(overdueDeletion);
    _dao.insertMajority(completedDeletion);
    _dao.insertMajority(notYetDueDeletion);
    _dao.insertMajority(failedOverdueSnapshot);
    _dao.insertMajority(failedDeletedSnapshot);
    _dao.insertMajority(failedNotYetDueSnapshot);
    _dao.insertMajority(pendingSnapshot);
    _dao.insertMajority(queuedSnapshot);
    _dao.insertMajority(runningSnapshot);

    final HashSet<TenantBackupSnapshot> resultSet = new HashSet<>();
    _dao.findBackupsWithExpiryBefore(testOffsetDate).forEachRemaining(resultSet::add);

    assertEquals(Set.of(overdueDeletion, failedOverdueSnapshot), resultSet);
  }

  @Test
  public void findBackupsWithDeleteDateBefore() {
    final ObjectId groupId = new ObjectId();
    final ObjectId clusterUniqueId = new ObjectId();

    final Date testOffsetDate = new Date();
    final Date oneDayAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofDays(1)));
    final Date twoDaysAgo = Date.from(testOffsetDate.toInstant().minus(Duration.ofDays(2)));
    final Date inOneDay = Date.from(testOffsetDate.toInstant().plus(Duration.ofHours(1)));

    final TenantBackupSnapshot overduePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot completedPurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .setPurged(true)
            .setPurgedDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot notYetDuePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .setDeleted(true)
            .setDeletionDate(testOffsetDate)
            .build();

    final TenantBackupSnapshot failedOverduePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot failedPurgedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, twoDaysAgo)
            .copy()
            .setScheduledDeletionDate(twoDaysAgo)
            .setDeleted(true)
            .setDeletionDate(twoDaysAgo)
            .setPurged(true)
            .setPurgedDate(twoDaysAgo)
            .build();

    final TenantBackupSnapshot failedNotYetDuePurge =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.FAILED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .setDeleted(true)
            .setDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot completedButNotDeleted =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.COMPLETED, groupId, clusterUniqueId, inOneDay)
            .copy()
            .setScheduledDeletionDate(inOneDay)
            .build();

    final TenantBackupSnapshot pendingSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.PENDING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot queuedSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.QUEUED, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    final TenantBackupSnapshot runningSnapshot =
        NDSModelTestFactory.getTenantBackupSnapshot(
                "proxy.host", State.RUNNING, groupId, clusterUniqueId, oneDayAgo)
            .copy()
            .setScheduledDeletionDate(oneDayAgo)
            .build();

    _dao.insertMajority(overduePurge);
    _dao.insertMajority(completedPurge);
    _dao.insertMajority(notYetDuePurge);
    _dao.insertMajority(failedOverduePurge);
    _dao.insertMajority(failedPurgedSnapshot);
    _dao.insertMajority(failedNotYetDuePurge);
    _dao.insertMajority(completedButNotDeleted);
    _dao.insertMajority(pendingSnapshot);
    _dao.insertMajority(queuedSnapshot);
    _dao.insertMajority(runningSnapshot);

    final HashSet<TenantBackupSnapshot> resultSet = new HashSet<>();
    _dao.findBackupsWithDeleteDateBefore(testOffsetDate).forEachRemaining(resultSet::add);

    assertEquals(Set.of(overduePurge, failedOverduePurge), resultSet);
  }

  @Test
  public void testResetForHandlingProxyHost() {
    final ObjectId groupId = ObjectId.get();
    final ObjectId uniqueId = ObjectId.get();
    final String clusterName = "testCluster1";
    _dao.insertReplicaSafe(
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.host", State.RUNNING, groupId, uniqueId, clusterName, new Date()));

    _dao.insertReplicaSafe(
        NDSModelTestFactory.getTenantBackupSnapshot(
            "proxy.host", State.QUEUED, groupId, uniqueId, clusterName, new Date()));

    final List<TenantBackupSnapshot> snapshots = _dao.findToResetForHandlingProxyHost("proxy.host");
    assertEquals(2, snapshots.size());

    _dao.resetForHandlingProxyHost("proxy.host");

    final List<TenantBackupSnapshot> snapshots2 =
        _dao.findByCluster(groupId, clusterName, TenantBackupTask.SnapshotType.BACKUP);

    assertEquals(2, snapshots2.size());
    assertEquals(State.PENDING, snapshots2.get(0).getState());
    assertEquals(State.PENDING, snapshots2.get(1).getState());
  }
}
