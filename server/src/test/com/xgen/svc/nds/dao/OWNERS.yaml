version: 1.0.0
filters:
  - "*":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "BUILD.bazel":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "ClusterDescriptionDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "ClusterDescriptionProcessArgsDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform-i
  - "ShadowClusterJobDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-acad2
  - "/Cps*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/Backup*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/AdminBackup*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/EolVersionUpgradeHistoryDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/NDSGroupMaintenanceDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/ExportBucketDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "CloudChefConfDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "/LeakedItemDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "CollectionRestoreStateDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "SystemClusterJobDaoIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
