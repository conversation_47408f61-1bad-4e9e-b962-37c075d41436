package com.xgen.svc.nds.res;

import static org.apache.http.HttpStatus.SC_ACCEPTED;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeToServerlessStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus.ErrorClass;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus.State;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeToServerlessStatus;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.nds.tenantUpgrade.model.ui.TenantUpgradeFeatureUsageView;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class TenantUpgradeToServerlessResourceIntTests
    extends BaseTenantUpgradeResourceTest<TenantUpgradeToServerlessStatusDao> {

  @Inject private TenantUpgradeToServerlessStatusDao _tenantUpgradeToServerlessStatusDao;
  @Inject private FeatureFlagSvc _featureFlagSvc;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _featureFlagSvc.enableNonConfigServiceFeatureFlag(
        getGroup(), null, FeatureFlag.SERVERLESS_SHARED_UI_OPTION_ENABLED);
  }

  @Test
  public void testCreateUpgrade() {
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jsonObj = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    jsonObj.put("name", CLUSTER_NAME);
    jsonObj.put("groupId", getGroup().getId().toHexString());

    final String endpoint = String.format("/nds/clusterUpgradeToServerless/%s", getGroup().getId());
    doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_ACCEPTED);

    Optional<ClusterDescription> clusterDescription =
        getTenantClusterDescriptionUpgradeDao()
            .findByName(getNDSGroup().getGroupId(), CLUSTER_NAME);

    assertTrue(clusterDescription.isPresent());

    final List<TenantUpgradeToServerlessStatus> tenantUpgradeToServerlessStatusList =
        getTenantUpgradeStatusDao().getTenantUpgradesForGroup(getGroup().getId());

    assertEquals(tenantUpgradeToServerlessStatusList.size(), 1);
    assertEquals(tenantUpgradeToServerlessStatusList.get(0).getClusterName(), CLUSTER_NAME);

    // test serverless instance as source
    jsonObj.put("name", SERVERLESS_INSTANCE_NAME);

    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_BAD_REQUEST);
    assertEquals(
        CommonErrorCode.NOT_FOUND.name(), serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testCreateUpgrade_NonServerlessInstance() {
    // Create cluster upgrade request fails for non-serverless instances as target
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject nonServerlessJSONObj =
        doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    nonServerlessJSONObj.put("groupId", getGroup().getId().toHexString());

    final String endpoint = String.format("/nds/clusterUpgradeToServerless/%s", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, nonServerlessJSONObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetTenantUpgradeFeatureUsage_NonTenantCluster() {
    // Check cluster eligibility request fails for non-tenant cluster as target
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject dedicatedJSONObj =
        doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    dedicatedJSONObj.put("groupId", getGroup().getId().toHexString());

    final String endpoint =
        String.format(
            "/nds/clusterUpgradeToServerless/%s/getTenantUpgradeFeatureUsage", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, dedicatedJSONObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetTenantUpgradeChartsFeatureUsage_NotInUse() {
    final String endpoint =
        String.format(
            "/nds/clusterUpgradeToServerless/%s/%s/getTenantUpgradeChartsUsage",
            getGroup().getId(), SERVERLESS_INSTANCE_NAME);
    final JSONObject response = doAuthedJsonGet(getAppUser(), endpoint, SC_OK);
    final TenantUpgradeFeatureUsageView tenantUpgradeFeatureUsageView =
        new TenantUpgradeFeatureUsageView(response);
    assertFalse(tenantUpgradeFeatureUsageView.isUsingCharts());
  }

  @Test
  public void testGetTenantUpgradeToServerlessStatusIsComplete_IsNotComplete() {
    final String clusterName = "my-cluster-name";
    final TenantUpgradeToServerlessStatus workingStatus =
        new TenantUpgradeToServerlessStatus(
            new ObjectId(),
            getGroup().getId(),
            clusterName,
            new ObjectId(),
            "source-mtm",
            new ObjectId(),
            new Date(),
            null,
            "username",
            "password",
            3,
            State.WORKING,
            ErrorClass.NONE,
            false,
            null,
            null,
            null);
    _tenantUpgradeToServerlessStatusDao.insertMajority(workingStatus.toDBObject());

    final String endpoint =
        String.format(
            "/nds/clusterUpgradeToServerless/%s/%s/isComplete", getGroup().getId(), clusterName);
    final JSONObject response = doAuthedJsonGet(getAppUser(), endpoint, SC_OK);
    assertFalse(response.getBoolean("tenantUpgradeToServerlessIsComplete"));
  }

  @Test
  public void testUpgradeServerlessSharedUIFeatureOff() {
    _featureFlagSvc.disableNonConfigServiceFeatureFlag(
        getGroup(), null, FeatureFlag.SERVERLESS_SHARED_UI_OPTION_ENABLED);
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jsonObj = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    jsonObj.put("name", CLUSTER_NAME);
    jsonObj.put("groupId", getGroup().getId().toHexString());

    final String endpoint = String.format("/nds/clusterUpgradeToServerless/%s", getGroup().getId());
    final JSONObject response = doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_BAD_REQUEST);
    assertEquals(NDSErrorCode.UNSUPPORTED.name(), response.get(ApiError.ERROR_CODE_FIELD));
    assertEquals("Tenant upgrade to serverless is no longer supported", response.get("message"));
  }

  @Test
  public void testGetTenantUpgradeToServerlessStatusIsComplete_IsComplete() {
    final String clusterName = "my-cluster-name";
    final TenantUpgradeToServerlessStatus completeStatus =
        new TenantUpgradeToServerlessStatus(
            new ObjectId(),
            getGroup().getId(),
            clusterName,
            new ObjectId(),
            "source-mtm",
            new ObjectId(),
            new Date(),
            new Date(),
            "username",
            "password",
            3,
            State.COMPLETE,
            ErrorClass.NONE,
            false,
            new ObjectId(),
            "target-mtm",
            new ObjectId());
    _tenantUpgradeToServerlessStatusDao.insertMajority(completeStatus.toDBObject());

    final String endpoint =
        String.format(
            "/nds/clusterUpgradeToServerless/%s/%s/isComplete", getGroup().getId(), clusterName);
    final JSONObject response = doAuthedJsonGet(getAppUser(), endpoint, SC_OK);
    assertTrue(response.getBoolean("tenantUpgradeToServerlessIsComplete"));
    assertEquals(
        completeStatus.getTargetTenantUniqueId().toString(), response.getString("tenantUniqueId"));
  }
}
