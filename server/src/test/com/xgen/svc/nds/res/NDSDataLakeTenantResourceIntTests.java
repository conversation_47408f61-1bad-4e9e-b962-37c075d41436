package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.DATA_FEDERATION_QUERY_LIMIT_CONFIGURED;
import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.DATA_FEDERATION_QUERY_LIMIT_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.FEDERATED_DATABASE_QUERY_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.datalake._public.model.NDSDataLakeModelTestFactory.getDataLakeStorageConfigDocument;
import static com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount.ServiceAccountProvisionStatus;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.instanceOf;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.oneOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.ArgumentMatchers.notNull;
import static org.mockito.ArgumentMatchers.startsWith;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.AccessToken;
import com.mongodb.BasicDBList;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity.AuditSvcIntTestUtils;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.util._public.compression.GZipCompressionUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.aws._public.util.AWSProviderFactory;
import com.xgen.cloud.nds.azure._public.util.AzureProviderFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.LimitTestFactory;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeModelTestFactory;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeS3Bucket;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeState;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStoreProvider;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStoreProvider.ProviderValues;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.FieldDefs;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.LimitSpan;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.OverrunPolicy;
import com.xgen.cloud.nds.datalake._public.model.ui.NDSDataLakeDataProcessRegionView;
import com.xgen.cloud.nds.datalake._public.util.DataLakeTenantUtil;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.gcp._public.util.GCPProviderFactory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveDataLakeConfig;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.featureid.NDSCloudProviderAccessFeatureUsageDataLakeFeatureId;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.mhouse.services.billinglimits.v1.Models;
import com.xgen.mhouse.services.billinglimits.v1.Models.DataScanningLimitStatus;
import com.xgen.mhouse.services.billinglimits.v1.Models.UsageLimit;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessFeatureUsageView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessGCPServiceAccountView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAWSCloudProviderConfigView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeClientConfigSourceView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCloudProviderConfigView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeMetricsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeS3BucketView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageValidationErrorsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantView;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Collection;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.DataSource;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Database;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Store;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.View;
import com.xgen.svc.nds.svc.adl.MockRegionsService;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;

@ExtendWith(GuiceTestExtension.class)
public class NDSDataLakeTenantResourceIntTests extends JUnit5BaseResourceTest {
  private static final String URL_PREFIX = "/nds/dataLakes/";

  @Inject private AuditSvc _auditSvc;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSDataLakeTenantDao _tenantDao;
  @Inject private AppSettings _appSettings;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private OnlineArchiveSvc _onlineArchiveSvc;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;

  private Organization _organization;
  private NDSGroup _ndsGroup;
  private Group _group;
  private AppUser _user;
  private AppUser _userReadOnly;
  private AppUser _groupDataAccessReadOnly;
  private AWSAccount _awsAccount;
  private AWSApiSvc _awsApiSvc;
  private GCPApiSvc _gcpApiSvc;
  private final String _clusterName = "testCluster0";
  private final ObjectId _clusterUniqueId = ObjectId.get();

  private static final String DATA_LAKE_ASSUME_ROLE_IAM_USER = "some:user:arn/who.are.you";

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _dataLakeTestUtils.setUp();
    _organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(_organization, "cus_0010");
    _ndsGroup = _ndsGroupSvc.ensureGroup(_group.getId());
    _ndsGroupSvc.ensureGroup(_group.getId());
    _user = MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));
    _userReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group, String.format("<EMAIL>", getUniquifier()), Role.GROUP_READ_ONLY);
    _groupDataAccessReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group,
            String.format("dataAccessReadOnlyUser%s", getUniquifier()),
            Role.GROUP_DATA_ACCESS_READ_ONLY);
    _appSettings.setProp(
        NDSDataLakeTenantSvc.DATA_LAKE_ASSUME_ROLE_IAM_USER_ARN_PROP,
        DATA_LAKE_ASSUME_ROLE_IAM_USER,
        AppSettings.SettingType.MEMORY);

    _awsAccount = new AWSAccount(AWSModelTestFactory.getAWSAccount());

    _awsAccountDao.save(_awsAccount);
    // Need to mock AWSApiSvc for valid request since the svc will attempt to run validations.
    // NDSDataLakeTenantUISvcIntTests covers the cloud provider config tests in more detail,
    // including mocking the AWSApiSvc behavior extensively.
    _awsApiSvc = mockAWSApiSvc();
    _gcpApiSvc = mockGCPApiSvc();

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), _clusterName)
            .append(ClusterDescription.FieldDefs.UNIQUE_ID, _clusterUniqueId));

    AWSProviderFactory.registerProvider();
    AzureProviderFactory.registerProvider();
    GCPProviderFactory.registerProvider();
  }

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testGetDataLakes() throws SvcException, JsonProcessingException {
    // test unauthorized
    doHtmlGet(getDataLakeV1Url(), HttpStatus.SC_UNAUTHORIZED);

    // initial empty tenants
    final JSONArray emptyResponse =
        doAuthedJsonArrayGet(_userReadOnly, getDataLakeV1Url(), HttpStatus.SC_OK);
    assertNotNull(emptyResponse);
    assertEquals(emptyResponse.length(), 0);

    // save tenant
    for (final String name : List.of("foo0", "bar1")) {
      final NDSDataLakeTenant tenant0 =
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, name);
      _dataLakeTestUtils.saveTenant(tenant0);
      // add storage to just the Admin API
      _dataLakeTestUtils.setAdminApiStorage(
          tenant0.getTenantId(), new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument()));
    }

    // save a tenant for charts to test it is filtered out in the response
    final NDSDataLakeTenant chartsTenant =
        NDSDataLakeModelTestFactory.getDataLakeTenantBuilder(_ndsGroup, "charts")
            .dataLakeType(NDSDataLakeTenant.DataLakeType.CHARTS)
            .build();
    _dataLakeTestUtils.saveTenant(chartsTenant);

    // get tenants without storage
    {
      // assert correct number of tenant
      final JSONArray multipleResponse =
          doAuthedJsonArrayGet(_userReadOnly, getDataLakeV1Url(), HttpStatus.SC_OK);
      assertNotNull(multipleResponse);
      assertEquals(multipleResponse.length(), 2);

      // assert tenant value
      final List<JSONObject> responseList = convertJSONArrayToJSONObjectList(multipleResponse);
      final Set<String> tenantNames =
          responseList.stream().map(t -> t.getString("name")).collect(Collectors.toSet());
      final List<JSONObject> tenantStorageConfigs =
          responseList.stream().map(t -> t.getJSONObject("storage")).toList();
      assertEquals(tenantNames, Set.of("foo0", "bar1"));
      for (final JSONObject storage : tenantStorageConfigs) {
        assertEquals(storage.optJSONObject("config"), null);
      }
      responseList.forEach(this::assertExternalIdAndIAMUserARNPresent);
    }

    // get tenants with storage
    {
      // assert correct number of tenant
      final JSONArray multipleResponse =
          doAuthedJsonArrayGet(
              _userReadOnly, getDataLakeV1Url() + "?includeStorage=true", HttpStatus.SC_OK);
      assertNotNull(multipleResponse);
      assertEquals(multipleResponse.length(), 2);

      // assert tenant value
      final List<JSONObject> responseList = convertJSONArrayToJSONObjectList(multipleResponse);
      final Set<String> tenantNames =
          responseList.stream().map(t -> t.getString("name")).collect(Collectors.toSet());
      assertEquals(tenantNames, Set.of("foo0", "bar1"));
      final NDSDataLakeStorageV1View expectedStorage =
          new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument());
      final ObjectMapper mapper = CustomJacksonJsonProvider.createObjectMapper();
      for (final JSONObject tenantJson : responseList) {
        final NDSDataLakeStorageView storage =
            mapper.readValue(tenantJson.toString(), NDSDataLakeTenantView.class).getStorage();
        assertThat(storage, instanceOf(NDSDataLakeStorageView.class));
        assertEquals(expectedStorage, storage);
        assertExternalIdAndIAMUserARNPresent(tenantJson);
      }
    }
  }

  @Test
  public void testGetDataLakesWithMissingRegion() throws SvcException, JsonProcessingException {
    // save tenant
    for (final String name : List.of("foo0", "bar1")) {
      final NDSDataLakeTenant tenant0 =
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, name);
      _dataLakeTestUtils.saveTenant(tenant0);
      // add storage to just the Admin API
      final Document storageConfig =
          NDSDataLakeModelTestFactory.getDataLakeStorageConfigDocumentWithMissingRegion();
      _dataLakeTestUtils.setAdminApiStorage(
          tenant0.getTenantId(), new NDSDataLakeStorageV1View(storageConfig));
    }

    // get tenants with storage
    {
      // assert correct number of tenant
      final JSONArray multipleResponse =
          doAuthedJsonArrayGet(
              _userReadOnly, getDataLakeV1Url() + "?includeStorage=true", HttpStatus.SC_OK);
      assertNotNull(multipleResponse);
      assertEquals(multipleResponse.length(), 2);

      // assert tenant value
      final List<JSONObject> responseList = convertJSONArrayToJSONObjectList(multipleResponse);
      final Set<String> tenantNames =
          responseList.stream().map(t -> t.getString("name")).collect(Collectors.toSet());
      assertEquals(tenantNames, Set.of("foo0", "bar1"));
      final NDSDataLakeStorageV1View expectedStorage =
          new NDSDataLakeStorageV1View(
              NDSDataLakeModelTestFactory.getDataLakeStorageConfigDocumentWithMissingRegion());
      final ObjectMapper mapper = new ObjectMapper();
      for (final JSONObject tenantJson : responseList) {
        final NDSDataLakeStorageView storage =
            mapper.readValue(tenantJson.toString(), NDSDataLakeTenantView.class).getStorage();
        assertThat(storage, instanceOf(NDSDataLakeStorageV1View.class));
        assertEquals(expectedStorage, storage);
        assertExternalIdAndIAMUserARNPresent(tenantJson);
      }
    }
  }

  @Test
  public void testGetDataLake() throws SvcException, JsonProcessingException {
    // test unauthorized
    doHtmlGet(getDataLakeV1Url() + "/somedatalake", HttpStatus.SC_UNAUTHORIZED);

    // not found
    final JSONObject notFound =
        doAuthedJsonGet(
            _userReadOnly, getDataLakeV1Url() + "/doesnotexist", HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME.name(), notFound.getString("errorCode"));

    // charts tenant
    final NDSDataLakeTenant chartsTenant =
        NDSDataLakeModelTestFactory.getDataLakeTenantBuilder(_ndsGroup, "charts")
            .dataLakeType(NDSDataLakeTenant.DataLakeType.CHARTS)
            .build();
    _dataLakeTestUtils.saveTenant(chartsTenant);
    final JSONObject chartsDataLakeResponse =
        doAuthedJsonGet(
            _userReadOnly,
            getDataLakeV1Url() + "/" + chartsTenant.getName(),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.CHARTS_DATA_LAKE_TENANT.name(), chartsDataLakeResponse.getString("errorCode"));

    final List<String> names = List.of("foo0", "bar1");

    // save tenants
    for (final String name : names) {
      final NDSDataLakeTenant tenant0 =
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, name);
      _dataLakeTestUtils.saveTenant(tenant0);
      // add storage to just the Admin API
      _dataLakeTestUtils.setAdminApiStorage(
          tenant0.getTenantId(), new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument()));
    }

    for (final String name : names) {
      // assert correct number of tenant
      final JSONObject response =
          doAuthedJsonGet(_userReadOnly, getDataLakeV1Url() + "/" + name, HttpStatus.SC_OK);
      assertNotNull(response);

      // assert tenant value
      assertEquals(name, response.getString("name"));
      final NDSDataLakeStorageV1View expectedStorage =
          new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument());
      final ObjectMapper mapper = new ObjectMapper();
      final NDSDataLakeStorageView storage =
          mapper.readValue(response.toString(), NDSDataLakeTenantView.class).getStorage();
      assertThat(storage, instanceOf(NDSDataLakeStorageV1View.class));
      assertEquals(expectedStorage, storage);
      assertExternalIdAndIAMUserARNPresent(response);
    }
  }

  @Test
  public void testGetAtlasSQLDataLake() {
    // unauthorized
    {
      doJsonGet(getAtlasSQLDataLakeUrl(_clusterName), HttpStatus.SC_UNAUTHORIZED);
    }
    // invalid cluster
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user, getAtlasSQLDataLakeUrl("invalidCluster"), HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.CLUSTER_NOT_FOUND.name(), response.getString("errorCode"));
    }
    // not found
    {
      final JSONObject response =
          doAuthedJsonGet(_user, getAtlasSQLDataLakeUrl(_clusterName), HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME.name(), response.getString("errorCode"));
    }
    // create atlas sql tenant
    doAuthedJsonPost(_user, getAtlasSQLDataLakeUrl(_clusterName), null);
    // success
    {
      final JSONObject resTenant =
          doAuthedJsonGet(_userReadOnly, getAtlasSQLDataLakeUrl(_clusterName), HttpStatus.SC_OK);
      assertAtlasSQLTenantResponse(resTenant);
    }
  }

  @Test
  public void testRequestIdForwarding() throws DataLakeAdminApiException {
    final String tenantName = "DataLake0";
    _dataLakeTestUtils.saveTenant(
        NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName));

    doAuthedJsonGet(_userReadOnly, getDataLakeV1Url() + "/" + tenantName, HttpStatus.SC_OK);
    // When no x-request-id has been set, it should be a null value
    assertNull(_dataLakeTestUtils.getLastRequestId());

    final String requestId = UUID.randomUUID().toString();
    final String path = appendAuthToPath(_userReadOnly, getDataLakeV1Url() + "/" + tenantName);
    HttpUtils.getInstance()
        .doAuthGetJson(getResourceUrl(path), HttpStatus.SC_OK, Map.of("x-request-id", requestId));
    assertEquals(requestId, _dataLakeTestUtils.getLastRequestId());
  }

  @Test
  public void testCreateTenant_aws() {
    // Test POST and response
    final String endpoint = getDataLakeV1Url();

    final JSONArray stores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, ProviderValues.S3)
                    .put(Store.BUCKET, "bucket")
                    .put(Store.DELIMITER, JSONObject.NULL)
                    .put(
                        Store.ADDITIONAL_STORAGE_CLASSES,
                        new JSONArray().put("INTELLIGENT_TIERING"))
                    .put(Store.INCLUDE_TAGS, false)
                    .put(Store.NAME, "storeName0")
                    .put(Store.PREFIX, "./patch-prefix-1/")
                    .put(Store.REGION, AWSRegionName.US_EAST_1.getValue()));

    // Test read only user unauthorized
    {
      doAuthedJsonPost(_userReadOnly, endpoint, new JSONObject(), HttpStatus.SC_FORBIDDEN);
    }

    // Test invalid tenant view - aws cloud provider config contains iamAssumedRoleARN
    {
      final JSONObject awsCloudProviderConfig =
          new JSONObject()
              .put("iamAssumedRoleARN", "arn:aws:iam::************:role/role-over-beethoven;");

      final JSONObject cloudProviderConfig = new JSONObject().put("aws", awsCloudProviderConfig);

      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));

      final JSONObject tenant =
          new JSONObject()
              .put("name", "testTenant")
              .put("cloudProviderConfig", cloudProviderConfig)
              .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);

      final JSONObject tenantResponse =
          doAuthedJsonPost(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.DATA_LAKE_IAM_ROLE_REQUIRED.name(), tenantResponse.getString("errorCode"));

      // Check that data lake is not in the database
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), "testTenant");
      assertTrue(savedTenant.isEmpty());
    }

    // Setup the Cloud Provider Access role
    final ObjectId roleId = new ObjectId();
    final String roleExternalId = UUID.randomUUID().toString();
    final String assumedRoleArn = "arn:aws:iam::************:role/role-name";
    final List<NDSCloudProviderAccessFeatureUsage> featureUsages = List.of();
    final NDSCloudProviderAccessAWSIAMRole role =
        new NDSCloudProviderAccessAWSIAMRole(
            roleId,
            assumedRoleArn,
            featureUsages,
            new ObjectId(),
            _awsAccount.getRootARN(),
            roleExternalId,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(_ndsGroup, role, null);

    // Test invalid tenant view - aws cloud provider config does not contain a test S3 bucket
    {
      final JSONObject awsCloudProviderConfig = new JSONObject().put("roleId", roleId.toString());

      final JSONObject cloudProviderConfig = new JSONObject().put("aws", awsCloudProviderConfig);

      final JSONObject tenant =
          new JSONObject()
              .put("name", "testTenant")
              .put("cloudProviderConfig", cloudProviderConfig);

      final JSONObject tenantResponse =
          doAuthedJsonPost(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_ARGUMENT.name(), tenantResponse.getString("errorCode"));
      assertEquals(tenantResponse.get("message"), "Invalid testS3Bucket");

      // Check that data lake is not in the database
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), "testTenant");
      assertTrue(savedTenant.isEmpty());
    }

    // Test valid tenant view with roleId - creates in ACTIVE state
    {
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = new JSONObject();
      tenant
          .put("name", "testTenant")
          .put(
              "cloudProviderConfig",
              new JSONObject()
                  .put(
                      "aws",
                      new JSONObject()
                          .put("roleId", roleId.toString())
                          .put("testS3Bucket", new JSONObject().put("name", "testbucket"))))
          .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);

      final JSONObject tenantResponse = doAuthedJsonPost(_user, endpoint, tenant);
      assertEquals("testTenant", tenantResponse.getString("name"));
      assertExternalIdAndIAMUserARNPresent(tenantResponse);
      assertEquals("USER", tenantResponse.getString("dataLakeType"));

      // Check that data lake is in the database
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), "testTenant");
      assertTrue(savedTenant.isPresent());
      assertEquals("testTenant", savedTenant.get().getName());
      assertEquals(NDSDataLakeState.ACTIVE, savedTenant.get().getState());

      // Check that the default monthly limit has been created
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(
                  _group.getId().toHexString(),
                  savedTenant.get().getTenantId().toHexString(),
                  "LIMIT_SPAN_MONTHLY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getTenantId().getValue(),
          savedTenant.get().getTenantId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(),
          (long) Units.convert(100.0, Units.TERABYTES, Units.BYTES));
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(),
          Models.LimitSpan.LIMIT_SPAN_MONTHLY);
    }
  }

  @Test
  public void testCreateTenant_gcp() {
    // Test POST and response
    final String endpoint = getDataLakeV1Url();

    final JSONArray stores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, ProviderValues.GCS)
                    .put(Store.BUCKET, "bucketName")
                    .put(Store.DELIMITER, JSONObject.NULL)
                    .put(Store.NAME, "storeName0")
                    .put(Store.PREFIX, "./patch-prefix-1/")
                    .put(Store.REGION, GCPRegionName.CENTRAL_US.getValue()));

    // Test read only user unauthorized
    {
      doAuthedJsonPost(_userReadOnly, endpoint, new JSONObject(), HttpStatus.SC_FORBIDDEN);
    }

    // Test invalid tenant view - cloud provider config contains read-only field.
    {
      final JSONObject gcpCloudProviderConfig =
          new JSONObject()
              .put(
                  "gcpServiceAccount",
                  "<EMAIL>");

      final JSONObject cloudProviderConfig = new JSONObject().put("gcp", gcpCloudProviderConfig);

      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));

      final JSONObject tenant =
          new JSONObject()
              .put("name", "testTenant")
              .put("cloudProviderConfig", cloudProviderConfig)
              .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);

      final JSONObject tenantResponse =
          doAuthedJsonPost(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_ARGUMENT.name(), tenantResponse.getString("errorCode"));

      // Check that data lake is not in the database
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), "testTenant");
      assertTrue(savedTenant.isEmpty());
    }

    // Setup the Cloud Provider Access role
    final ObjectId roleId = new ObjectId();
    final String gcpProjectID = "p-************************";
    final String serviceAccount =
        "mongodb-atlas-****************@" + gcpProjectID + ".iam.gserviceaccount.com";

    final List<NDSCloudProviderAccessFeatureUsageView> featureUsages = List.of();
    final NDSCloudProviderAccessGCPServiceAccountView role =
        new NDSCloudProviderAccessGCPServiceAccountView(
            roleId,
            serviceAccount,
            featureUsages,
            new Date(),
            ServiceAccountProvisionStatus.COMPLETE);

    // At first, it will fail without a cloud container provisioned.
    try {
      _ndsCloudProviderAccessSvc.addGCPServiceAccountToCloudProviderAccess(_group, role, null);
      fail("expected exception");
    } catch (SvcException pE) {
      assertEquals(
          pE.toString(),
          new SvcException(NDSErrorCode.CLOUD_PROVIDER_ACCESS_NO_GCP_CONTAINER_PROVISIONED)
              .toString());
    }

    // Try again with a cloud container provisioned.
    final GCPCloudProviderContainer gcpContainer =
        new GCPCloudProviderContainer(
            new GCPCloudProviderContainer(new ObjectId(), NDSDefaults.ATLAS_CIDR)
                .toDBObject()
                .append("id", new ObjectId())
                .append("projectId", gcpProjectID)
                .append("networkName", "bar")
                .append("peers", new BasicDBList()));
    final NDSGroup ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final ObjectId containerId = _ndsGroupSvc.addCloudContainer(ndsGroup, gcpContainer);

    try {
      _ndsCloudProviderAccessSvc.addGCPServiceAccountToCloudProviderAccess(_group, role, null);
    } catch (SvcException pE) {
      fail("expected no exceptions {}", pE);
    }

    { // Test gracefully handling a bucket where we can't access the GCS bucket.
      var token = new AccessToken("token", new Date());
      doReturn(token).when(_gcpApiSvc).getAccessTokenForCloudStorageAccess(any(), any());
      // Don't mock the call to listBlobs to let the GCP SDK throw an exception.
      doCallRealMethod().when(_gcpApiSvc).listBlobsAtPrefix((AccessToken) any(), any(), any());
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = new JSONObject();
      tenant
          .put("name", "testTenant")
          .put(
              NDSDataLakeTenantView.FieldDefs.CLOUD_PROVIDER_CONFIG,
              new JSONObject().put("gcp", new JSONObject().put("roleId", roleId.toString())))
          .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);

      final JSONObject tenantResponse =
          doAuthedJsonPost(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);

      assertEquals(
          NDSErrorCode.DATA_LAKE_CANNOT_ACCESS_GOOGLE_CLOUD_STORAGE_DURING_SETUP.name(),
          tenantResponse.getString("errorCode"));
      assertEquals(
          "Data Federation is unable to access the specified Google Cloud Storage Bucket"
              + " (bucketName) via the provided Service Account"
              + " (<EMAIL>)."
              + " If access was recently configured, it may take a few minutes to register in GCP."
              + " Please try again.",
          tenantResponse.getString("message"));

      // Check that data lake is not in the database
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), "testTenant");
      assertTrue(savedTenant.isEmpty());
    }

    { // Test valid tenant view with roleId - creates in ACTIVE state
      var token = new AccessToken("token", new Date());
      doReturn(token).when(_gcpApiSvc).getAccessTokenForCloudStorageAccess(any(), any());
      doReturn(null).when(_gcpApiSvc).listBlobsAtPrefix((AccessToken) any(), any(), any());

      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = new JSONObject();
      tenant
          .put("name", "testTenant")
          .put(
              NDSDataLakeTenantView.FieldDefs.CLOUD_PROVIDER_CONFIG,
              new JSONObject().put("gcp", new JSONObject().put("roleId", roleId.toString())))
          .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage)
          .put(
              NDSDataLakeTenantView.FieldDefs.DATA_PROCESS_REGION,
              new JSONObject()
                  .put(NDSDataLakeDataProcessRegionView.FieldDefs.CLOUD_PROVIDER, "GCP"));

      // First attempt should fail without GCP feature flag.
      final JSONObject errorResponse =
          doAuthedJsonPost(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER.name(),
          errorResponse.getString("errorCode"));

      // Now, it should be successful with the feature flag enabled.
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_DATA_FEDERATION_ON_GCP);
      final JSONObject tenantResponse = doAuthedJsonPost(_user, endpoint, tenant);

      assertEquals("testTenant", tenantResponse.getString("name"));
      final JSONObject cloudProviderConfigJSON =
          tenantResponse
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.CLOUD_PROVIDER_CONFIG)
              .getJSONObject(NDSDataLakeCloudProviderConfigView.FieldDefs.GCP);
      assertEquals(roleId.toString(), cloudProviderConfigJSON.getString("roleId"));
      assertEquals(serviceAccount, cloudProviderConfigJSON.getString("gcpServiceAccount"));
      assertEquals("USER", tenantResponse.getString("dataLakeType"));

      // Check that data lake is in the database
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), "testTenant");
      assertTrue(savedTenant.isPresent());
      assertEquals("testTenant", savedTenant.get().getName());
      assertEquals(NDSDataLakeState.ACTIVE, savedTenant.get().getState());

      // Check that the default monthly limit has been created
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getGcpUsageLimit(
                  _group.getId().toHexString(),
                  savedTenant.get().getTenantId().toHexString(),
                  "LIMIT_SPAN_MONTHLY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getTenantId().getValue(),
          savedTenant.get().getTenantId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(),
          (long) Units.convert(100.0, Units.TERABYTES, Units.BYTES));
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(),
          Models.LimitSpan.LIMIT_SPAN_MONTHLY);
    }
  }

  @Test
  public void testCreateTenant_skipValidation() {
    // make listObjects fail
    when(_awsApiSvc.listObjects(any(), any(), any(), any(), anyBoolean(), any()))
        .thenThrow(new AWSApiException());
    final ObjectId roleId = new ObjectId();
    final JSONArray stores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, ProviderValues.S3)
                    .put(Store.BUCKET, "bucket")
                    .put(Store.DELIMITER, JSONObject.NULL)
                    .put(
                        Store.ADDITIONAL_STORAGE_CLASSES,
                        new JSONArray().put("INTELLIGENT_TIERING"))
                    .put(Store.INCLUDE_TAGS, false)
                    .put(Store.NAME, "storeName0")
                    .put(Store.PREFIX, "./patch-prefix-1/")
                    .put(Store.REGION, AWSRegionName.US_EAST_1.getValue()));

    final JSONObject storage =
        new JSONObject()
            .put(
                NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                new JSONObject()
                    .put(StorageConfigFieldDefs.STORES, stores)
                    .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
    final JSONObject tenant = new JSONObject();
    tenant
        .put("name", "testTenant")
        .put(
            "cloudProviderConfig",
            new JSONObject()
                .put(
                    "aws",
                    new JSONObject()
                        .put("roleId", roleId.toString())
                        .put("testS3Bucket", new JSONObject().put("name", "testbucket"))))
        .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
    final String roleExternalId = UUID.randomUUID().toString();
    final String assumedRoleArn = "arn:aws:iam::************:role/role-name";
    final List<NDSCloudProviderAccessFeatureUsage> featureUsages = List.of();
    final NDSCloudProviderAccessAWSIAMRole role =
        new NDSCloudProviderAccessAWSIAMRole(
            roleId,
            assumedRoleArn,
            featureUsages,
            new ObjectId(),
            _awsAccount.getRootARN(),
            roleExternalId,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(_ndsGroup, role, null);

    // should fail
    final JSONObject errorResponse =
        doAuthedJsonPost(_user, getDataLakeV1Url(), tenant, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.DATA_LAKE_CANNOT_LIST_S3_BUCKET.name(), errorResponse.getString("errorCode"));

    // should succeed with skipRoleValidation
    doAuthedJsonPost(
        _user, getDataLakeV1Url() + "?skipRoleValidation=true", tenant, HttpStatus.SC_OK);
  }

  @Test
  public void testCreateAtlasSQLTenant() {
    // data access read only user unauthorized
    {
      doAuthedJsonPost(
          _groupDataAccessReadOnly,
          getAtlasSQLDataLakeUrl(_clusterName),
          null,
          HttpStatus.SC_FORBIDDEN);
    }
    // invalid cluster
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user, getAtlasSQLDataLakeUrl("non-exist"), null, HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.CLUSTER_NOT_FOUND.name(), response.getString("errorCode"));
    }
    // success
    {
      final JSONObject resTenant =
          doAuthedJsonPost(_user, getAtlasSQLDataLakeUrl(_clusterName), null);
      assertAtlasSQLTenantResponse(resTenant);
      // Check that data lake is in the database
      final String tenantName = String.format("atlas-sql-%s", _clusterUniqueId);
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), tenantName);
      assertTrue(savedTenant.isPresent());
      assertEquals(NDSDataLakeState.ACTIVE, savedTenant.get().getState());
    }
    // conflict
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user, getAtlasSQLDataLakeUrl(_clusterName), null, HttpStatus.SC_CONFLICT);
      assertEquals(
          NDSErrorCode.DATA_LAKE_TENANT_NAME_ALREADY_EXISTS.name(),
          response.getString("errorCode"));
    }
  }

  private JSONObject createTenant(final String pTenantName, final String pBucketName)
      throws DataLakeAdminApiException {
    return createTenant(pTenantName, pBucketName, null);
  }

  private JSONObject createTenant(
      final String pTenantName, final String pBucketName, final ObjectId pRoleId)
      throws DataLakeAdminApiException {
    final NDSDataLakeTenant initTenant =
        NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, pTenantName).toBuilder()
            .cloudProviderConfig(
                NDSDataLakeModelTestFactory.getDataLakeCloudProviderConfig(
                    null,
                    "2cd98b50-072e-4704-b0e6-5d4787d5c54f",
                    NDSDataLakeS3Bucket.builder()
                        .name(pBucketName)
                        .region(AWSRegionName.US_EAST_1)
                        .build(),
                    pRoleId))
            .createdDate(new Date())
            .privateEndpointHostnameMap(Map.of("endpoint", "hostname"))
            .lastOIDCJWKSRevokedTime(new Date())
            .build();

    _dataLakeTestUtils.saveTenant(initTenant);

    return doAuthedJsonGet(_user, getDataLakeV1Url() + "/" + pTenantName, HttpStatus.SC_OK);
  }

  @Test
  public void testUpdateTenantStorage() throws DataLakeAdminApiException {
    final String tenantName = "updateStorageTenant0";
    final String bucketName = "test-bucket";
    final String clusterName = "test-cluster";
    final JSONObject initialTenant = createTenant(tenantName, bucketName);
    int expectedClientConfigCount =
        initialTenant.getInt(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_COUNT);
    assertThat(expectedClientConfigCount, equalTo(0));

    final String endpoint = String.format(getDataLakeV1Url() + "/%s", tenantName);

    // null store, null databases
    {
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, JSONObject.NULL)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject resTenant = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);

      expectedClientConfigCount += 1;
      assertThat(
          resTenant.getInt(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_COUNT),
          equalTo(expectedClientConfigCount));
    }

    final String httpStoreProvider = NDSDataLakeStoreProvider.ProviderValues.HTTP;
    final String httpStoreName = "storeName0";
    final JSONArray httpStores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, httpStoreProvider)
                    .put(Store.NAME, httpStoreName)
                    .put(Store.URLS, List.of("https://foo")));

    final JSONArray s3Stores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, NDSDataLakeStoreProvider.ProviderValues.S3)
                    .put(Store.BUCKET, bucketName)
                    .put(Store.DELIMITER, JSONObject.NULL)
                    .put(
                        Store.ADDITIONAL_STORAGE_CLASSES,
                        new JSONArray().put(Store.STORAGE_CLASS_INTELLIGENT_TIERING))
                    .put(Store.INCLUDE_TAGS, false)
                    .put(Store.NAME, "storeName0")
                    .put(Store.PREFIX, "./patch-prefix-1/")
                    .put(Store.REGION, AWSRegionName.US_EAST_1.getValue()));

    final JSONArray atlasStores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, NDSDataLakeStoreProvider.ProviderValues.ATLAS)
                    .put(Store.NAME, "storeName0")
                    .put(Store.CLUSTER_NAME, clusterName)
                    .put(Store.PROJECT_ID, _group.getId().toString())
                    .put(
                        Store.READ_PREFERENCE,
                        new JSONObject()
                            .put(Store.READ_PREFERENCE_MODE, "secondary")
                            .put(Store.READ_PREFERENCE_MAX_STALENESS_SECONDS, 100)
                            .put(
                                Store.READ_PREFERENCE_TAG_SETS,
                                new JSONArray()
                                    .put(
                                        new JSONArray()
                                            .put(
                                                new JSONObject()
                                                    .put("name", "provider")
                                                    .put("value", "AWS"))))));

    // valid S3 store, no IAM role
    {
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, s3Stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject resError =
          doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertThat(
          resError.getString("errorCode"),
          equalTo(NDSErrorCode.DATA_LAKE_IAM_ROLE_REQUIRED.name()));
    }

    // invalid storage config - ADL validation fails
    {
      final ObjectId tenantId =
          new ObjectId(initialTenant.getString(NDSDataLakeTenant.FieldDefs.TENANT_ID));
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.AWS, tenantId, List.of("error1", "error2"));

      final JSONArray dataSources =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(DataSource.STORE_NAME, httpStoreName)
                      .put(DataSource.PROVIDER, httpStoreProvider)
                      .put(DataSource.PROVENANCE_FIELD_NAME, "provName0"));
      final JSONArray collections =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Collection.NAME, "col0")
                      .put(Collection.DATA_SOURCES, dataSources));
      final JSONArray databases =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Database.NAME, "db0")
                      .put(Database.COLLECTIONS, collections));

      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, httpStores)
                      .put(StorageConfigFieldDefs.DATABASES, databases));

      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);

      final JSONObject resError =
          doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertThat(
          resError.getString("errorCode"),
          equalTo(NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_INVALID.name()));
      assertThat(resError.getString("message"), containsString("error1, error2"));

      _dataLakeTestUtils.setAdminApiStorageValidationErrors(CloudProvider.AWS, tenantId, List.of());
    }

    // valid HTTP store, null databases
    {
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, httpStores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject resTenant = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
      final JSONObject resStorage =
          resTenant
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.STORAGE)
              .getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG);
      final JSONArray resStores = resStorage.getJSONArray(StorageConfigFieldDefs.STORES);

      assertStoreUpdate(httpStores, resStores);

      // assert empty databases
      assertTrue(resStorage.isNull(StorageConfigFieldDefs.DATABASES));

      // store change increment count
      expectedClientConfigCount += 1;
      assertThat(
          resTenant.getInt(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_COUNT),
          equalTo(expectedClientConfigCount));

      assertThat(
          resTenant.get(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_SOURCE),
          equalTo(NDSDataLakeClientConfigSourceView.ATLAS.name()));
    }
    // valid Atlas store, null databases
    {
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, atlasStores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject resTenant = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
      final JSONObject resStorage =
          resTenant
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.STORAGE)
              .getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG);
      final JSONArray resStores = resStorage.getJSONArray(StorageConfigFieldDefs.STORES);

      assertStoreUpdate(atlasStores, resStores);

      // assert empty databases
      assertTrue(resStorage.isNull(StorageConfigFieldDefs.DATABASES));

      // store change increment count
      expectedClientConfigCount += 1;
      assertThat(
          resTenant.getInt(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_COUNT),
          equalTo(expectedClientConfigCount));

      assertThat(
          resTenant.get(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_SOURCE),
          equalTo(NDSDataLakeClientConfigSourceView.ATLAS.name()));
    }

    // valid stores, valid databases
    {
      final JSONArray dataSources =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(DataSource.PROVIDER, NDSDataLakeStoreProvider.ProviderValues.HTTP)
                      .put(DataSource.STORE_NAME, "storeName0")
                      .put(DataSource.PROVENANCE_FIELD_NAME, "provName0"));

      final JSONArray collections =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Collection.NAME, "col0")
                      .put(Collection.DATA_SOURCES, dataSources));

      final JSONArray views =
          new JSONArray()
              .put(
                  0,
                  new JSONObject()
                      .put(View.NAME, "view0")
                      .put(View.PIPELINE, "pipe0")
                      .put(View.SOURCE, "source0"));

      final JSONArray databases =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Database.NAME, "db0")
                      .put(Database.COLLECTIONS, collections)
                      .put(Database.VIEWS, views)
                      .put(Database.MAX_WILDCARD_COLLECTIONS, JSONObject.NULL));

      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, httpStores)
                      .put(StorageConfigFieldDefs.DATABASES, databases));

      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject resTenant = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
      final JSONObject resStorage =
          resTenant
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.STORAGE)
              .getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG);

      assertStoreUpdate(httpStores, resStorage.getJSONArray(StorageConfigFieldDefs.STORES));

      final JSONArray databasesView =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Database.NAME, "db0")
                      .put(Database.COLLECTIONS, collections)
                      .put(Database.VIEWS, views)
                      .put(Database.MAX_WILDCARD_COLLECTIONS, JSONObject.NULL));
      assertDatabasesUpdate(
          databasesView, resStorage.getJSONArray(StorageConfigFieldDefs.DATABASES));

      // store and databases updated increment
      expectedClientConfigCount += 1;
      assertThat(
          resTenant.getInt(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_COUNT),
          equalTo(expectedClientConfigCount));

      // check that source has been updated by atlas
      assertThat(
          resTenant.get(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_SOURCE),
          equalTo(NDSDataLakeClientConfigSourceView.ATLAS.name()));

      // check that private endpoint hostname map is unchanged
      assertThat(
          resTenant
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.PRIVATE_ENDPOINT_HOSTNAME_MAP)
              .has("endpoint"),
          equalTo(true));

      // check that lastOIDCJWKSRevokedTime is unchanged
      assertThat(
          resTenant.has(NDSDataLakeTenantView.FieldDefs.LAST_OIDC_JWKS_REVOKED_TIME),
          equalTo(true));
    }

    // valid storage, valid databases, update max wildcard collections
    {
      final JSONArray dataSources =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(DataSource.PROVIDER, NDSDataLakeStoreProvider.ProviderValues.HTTP)
                      .put(DataSource.STORE_NAME, "storeName0"));

      final JSONArray collections =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Collection.NAME, "col0")
                      .put(Collection.DATA_SOURCES, dataSources));

      final JSONArray views =
          new JSONArray()
              .put(
                  0,
                  new JSONObject()
                      .put(View.NAME, "view0")
                      .put(View.PIPELINE, "pipe0")
                      .put(View.SOURCE, "source0"));

      final JSONArray databases =
          new JSONArray()
              .put(
                  new JSONObject()
                      .put(Database.NAME, "db0")
                      .put(Database.COLLECTIONS, collections)
                      .put(Database.VIEWS, views)
                      .put(Database.MAX_WILDCARD_COLLECTIONS, 100));

      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, httpStores)
                      .put(StorageConfigFieldDefs.DATABASES, databases));

      final JSONObject tenant = initialTenant.put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject resTenant = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
      final JSONObject resStorage =
          resTenant
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.STORAGE)
              .getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG);

      assertStoreUpdate(httpStores, resStorage.getJSONArray(StorageConfigFieldDefs.STORES));

      assertDatabasesUpdate(databases, resStorage.getJSONArray(StorageConfigFieldDefs.DATABASES));

      expectedClientConfigCount += 1;
      assertThat(
          resTenant.getInt(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_COUNT),
          equalTo(expectedClientConfigCount));

      // check that source has been updated by atlas
      assertThat(
          resTenant.get(NDSDataLakeTenantView.FieldDefs.CLIENT_CONFIG_SOURCE),
          equalTo(NDSDataLakeClientConfigSourceView.ATLAS.name()));
    }
  }

  @Test
  public void testUpdateTenantStorage_RegionHandling() throws DataLakeAdminApiException {
    final String tenantName = "messWithTheRegionsTenant";
    final String bucketName = "dont-kick-the-bucket";

    final JSONObject initialTenant = createTenant(tenantName, bucketName, new ObjectId());
    final String endpoint = String.format(getDataLakeV1Url() + "/%s", tenantName);

    final String storeName = "storeName0";
    final String provider = NDSDataLakeStoreProvider.ProviderValues.S3;

    final JSONArray stores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, provider)
                    .put(Store.BUCKET, bucketName)
                    .put(Store.DELIMITER, JSONObject.NULL)
                    .put(
                        Store.ADDITIONAL_STORAGE_CLASSES,
                        new JSONArray().put(Store.STORAGE_CLASS_INTELLIGENT_TIERING))
                    .put(Store.INCLUDE_TAGS, false)
                    .put(Store.NAME, storeName)
                    .put(Store.PREFIX, "./patch-prefix-1/")
                    .put(Store.REGION, AWSRegionName.US_WEST_2.getValue()));

    final JSONArray dataSources =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(DataSource.STORE_NAME, storeName)
                    .put(DataSource.PROVIDER, provider)
                    .put(DataSource.PROVENANCE_FIELD_NAME, "provName0")
                    .put(DataSource.PATH, "/patch-definition/1")
                    .put(DataSource.DEFAULT_FORMAT, ".json"));

    final JSONArray collections =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Collection.NAME, "col0")
                    .put(Collection.DATA_SOURCES, dataSources));

    final JSONArray views =
        new JSONArray()
            .put(
                0,
                new JSONObject()
                    .put(View.NAME, "view0")
                    .put(View.PIPELINE, "pipe0")
                    .put(View.SOURCE, "source0"));

    final JSONArray databases =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Database.NAME, "db0")
                    .put(Database.COLLECTIONS, collections)
                    .put(Database.VIEWS, views));

    final JSONObject storage =
        new JSONObject()
            .put(
                NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                new JSONObject()
                    .put(StorageConfigFieldDefs.STORES, stores)
                    .put(StorageConfigFieldDefs.DATABASES, databases));

    // validate user can patch tenant by providing role ID
    final ObjectId specifiedRoleId = new ObjectId();
    final String roleExternalId = UUID.randomUUID().toString();
    final String assumedRoleArn = "arn:aws:iam::************:role/role-name";
    final List<NDSCloudProviderAccessFeatureUsage> featureUsages = List.of();
    final NDSCloudProviderAccessAWSIAMRole role =
        new NDSCloudProviderAccessAWSIAMRole(
            specifiedRoleId,
            assumedRoleArn,
            featureUsages,
            new ObjectId(),
            _awsAccount.getRootARN(),
            roleExternalId,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(_ndsGroup, role, null);

    final JSONObject aws =
        new JSONObject()
            .put("roleId", specifiedRoleId)
            .put("testS3Bucket", new JSONObject().put("name", "testbucc"));
    final JSONObject cloudProviderConfig = new JSONObject().put("aws", aws);

    final JSONObject tenant =
        initialTenant
            .put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage)
            .put(NDSDataLakeTenantView.FieldDefs.CLOUD_PROVIDER_CONFIG, cloudProviderConfig);
    final JSONObject resTenant = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
    assertTrue(resTenant.has(NDSDataLakeTenantView.FieldDefs.STORAGE));
    final JSONObject storageResponse =
        resTenant
            .getJSONObject(NDSDataLakeTenantView.FieldDefs.STORAGE)
            .getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG);
    assertTrue(resTenant.has(NDSDataLakeTenantView.FieldDefs.STORAGE));
    final JSONArray resStores = storageResponse.getJSONArray(StorageConfigFieldDefs.STORES);
    assertStoreUpdate(stores, resStores);
    assertTrue(resStores.length() > 0);
    final JSONObject resStore = resStores.getJSONObject(0);
    assertTrue(resStore.has(Store.REGION));
    assertEquals(AWSRegionName.US_WEST_2.getValue(), resStore.getString(Store.REGION));

    // Test invalid region results in fetching the region for the bucket via aws
    // GetBucketLocationRequest
    {
      stores.getJSONObject(0).put(Store.REGION, "a-galaxyfaraway-1");
      final JSONObject res = doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
      final String region =
          (String)
              res.getJSONObject("storage")
                  .getJSONObject("config")
                  .getJSONArray("stores")
                  .getJSONObject(0)
                  .get(Store.REGION);
      // This is a UI interaction where regions are in the value (us-east-1) format.
      final Optional<AWSRegionName> regionOpt = AWSRegionName.findByValue(region);
      assertTrue(regionOpt.isPresent());
      assertEquals(AWSRegionName.US_EAST_1, regionOpt.get());
    }
  }

  @Test
  public void testValidateStorage_aws() {
    // set-up
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeStorageV1View storageView =
        new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument());
    final String urlFormat = getStorageValidationUrl() + "?tenantId=%s&cloudProvider=AWS";
    // set up error in Azure to make sure it never gets called
    _dataLakeTestUtils.setAdminApiStorageValidationErrors(
        CloudProvider.AZURE, tenantId, List.of("error1", "error2"));

    // null tenantId and no error
    {
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, ""), storageView, HttpStatus.SC_OK);
      assertEquals(
          0, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      assertEquals(
          0,
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS).length());
    }

    // tenantId and no error
    {
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, tenantId), storageView, HttpStatus.SC_OK);
      assertEquals(
          0, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      assertEquals(
          0,
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS).length());
    }

    // null tenantId and error
    {
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.AWS,
          DataLakeTenantUtil.ZERO_TENANT_ID_FOR_STORAGE_VALIDATION,
          List.of("error1", "error2"));
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, ""), storageView, HttpStatus.SC_OK);
      assertEquals(
          2, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      final JSONArray jsonErrorArray =
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS);
      assertEquals("error1", jsonErrorArray.get(0));
      assertEquals("error2", jsonErrorArray.get(1));
    }

    // tenantId with error
    {
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.AWS, tenantId, List.of("error3", "error4"));
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, tenantId), storageView, HttpStatus.SC_OK);
      assertEquals(
          2, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      final JSONArray jsonErrorArray =
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS);
      assertEquals("error3", jsonErrorArray.get(0));
      assertEquals("error4", jsonErrorArray.get(1));
    }
  }

  @Test
  public void testValidateStorage_azure() {
    // set-up
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeStorageV1View storageView =
        new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument());
    final String urlFormat = getStorageValidationUrl() + "?tenantId=%s&cloudProvider=AZURE";
    // set up error in AWS to make sure it never gets called
    _dataLakeTestUtils.setAdminApiStorageValidationErrors(
        CloudProvider.AWS, tenantId, List.of("error1", "error2"));

    // null tenantId and no error
    {
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, ""), storageView, HttpStatus.SC_OK);
      assertEquals(
          0, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      assertEquals(
          0,
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS).length());
    }

    // tenantId and no error
    {
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, tenantId), storageView, HttpStatus.SC_OK);
      assertEquals(
          0, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      assertEquals(
          0,
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS).length());
    }

    // null tenantId and error
    {
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.AZURE,
          DataLakeTenantUtil.ZERO_TENANT_ID_FOR_STORAGE_VALIDATION,
          List.of("error1", "error2"));
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, ""), storageView, HttpStatus.SC_OK);
      assertEquals(
          2, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      final JSONArray jsonErrorArray =
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS);
      assertEquals("error1", jsonErrorArray.get(0));
      assertEquals("error2", jsonErrorArray.get(1));
    }

    // tenantId with error
    {
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.AZURE, tenantId, List.of("error3", "error4"));
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, tenantId), storageView, HttpStatus.SC_OK);
      assertEquals(
          2, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      final JSONArray jsonErrorArray =
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS);
      assertEquals("error3", jsonErrorArray.get(0));
      assertEquals("error4", jsonErrorArray.get(1));
    }
  }

  @Test
  public void testValidateStorage_gcp() {
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeStorageV1View storageView =
        new NDSDataLakeStorageV1View(getDataLakeStorageConfigDocument());
    final String urlFormat = getStorageValidationUrl() + "?tenantId=%s&cloudProvider=GCP";
    // set up error in AWS to make sure it never gets called
    _dataLakeTestUtils.setAdminApiStorageValidationErrors(
        CloudProvider.AWS, tenantId, List.of("error1", "error2"));

    // null tenantId and no error
    {
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, ""), storageView, HttpStatus.SC_OK);
      assertEquals(
          0, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      assertEquals(
          0,
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS).length());
    }

    // tenantId and no error
    {
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, tenantId), storageView, HttpStatus.SC_OK);
      assertEquals(
          0, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      assertEquals(
          0,
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS).length());
    }

    // null tenantId and error
    {
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.GCP,
          DataLakeTenantUtil.ZERO_TENANT_ID_FOR_STORAGE_VALIDATION,
          List.of("error1", "error2"));
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, ""), storageView, HttpStatus.SC_OK);
      assertEquals(
          2, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      final JSONArray jsonErrorArray =
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS);
      assertEquals("error1", jsonErrorArray.get(0));
      assertEquals("error2", jsonErrorArray.get(1));
    }

    // tenantId with error
    {
      _dataLakeTestUtils.setAdminApiStorageValidationErrors(
          CloudProvider.GCP, tenantId, List.of("error3", "error4"));
      final JSONObject response =
          doAuthedJsonPost(
              _userReadOnly, String.format(urlFormat, tenantId), storageView, HttpStatus.SC_OK);
      assertEquals(
          2, response.getInt(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS_COUNT));
      final JSONArray jsonErrorArray =
          response.getJSONArray(NDSDataLakeStorageValidationErrorsView.FieldDefs.ERRORS);
      assertEquals("error3", jsonErrorArray.get(0));
      assertEquals("error4", jsonErrorArray.get(1));
    }
  }

  @Test
  public void testValidateCloudProviderConfig()
      throws NoSuchFieldException, IllegalAccessException {
    final String testS3Bucket = "s3Bucket";

    // setup sample role id
    final String roleExternalId = UUID.randomUUID().toString();
    final ObjectId roleId = ObjectId.get();
    final String assumedRoleArn = "arn:aws:iam::************:role/role-name";
    final List<NDSCloudProviderAccessFeatureUsage> featureUsages = List.of();
    final NDSCloudProviderAccessAWSIAMRole role =
        new NDSCloudProviderAccessAWSIAMRole(
            roleId,
            assumedRoleArn,
            featureUsages,
            new ObjectId(),
            _awsAccount.getRootARN(),
            roleExternalId,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(_ndsGroup, role, null);

    // aws role does not exist - fail
    {
      _awsApiSvc = mockAWSApiSvc();
      final String endpoint = getCloudProviderConfigValidationUrl(false);
      final NDSDataLakeTenantView tenant =
          NDSDataLakeTenantView.builder()
              .cloudProviderConfig(
                  NDSDataLakeCloudProviderConfigView.builder()
                      .aws(
                          NDSDataLakeAWSCloudProviderConfigView.builder()
                              .roleId(ObjectId.get())
                              .testS3Bucket(
                                  NDSDataLakeS3BucketView.builder().name(testS3Bucket).build())
                              .build())
                      .build())
              .build();
      final JSONObject response =
          doAuthedJsonPost(_userReadOnly, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND.name(),
          response.getString("errorCode"));
    }

    // aws role exists no bucket validation - success
    {
      _awsApiSvc = mockAWSApiSvc();
      final String endpoint = getCloudProviderConfigValidationUrl(true);
      final NDSDataLakeTenantView tenant =
          NDSDataLakeTenantView.builder()
              .cloudProviderConfig(
                  NDSDataLakeCloudProviderConfigView.builder()
                      .aws(
                          NDSDataLakeAWSCloudProviderConfigView.builder()
                              .roleId(roleId)
                              // TODO: if skip validation is true we should not require check for s3
                              // test bucket currently this is throwing a missing attribute for
                              // testBucket
                              .testS3Bucket(
                                  NDSDataLakeS3BucketView.builder().name(testS3Bucket).build())
                              .build())
                      .build())
              .build();
      doAuthedJsonPost(_userReadOnly, endpoint, tenant, HttpStatus.SC_OK);

      verify(_awsApiSvc, times(1))
          .assumeRole(
              any(ObjectId.class),
              any(),
              eq(assumedRoleArn),
              eq(roleExternalId),
              anyString(),
              any(),
              any(),
              any(),
              any());
    }

    // aws role exists with test s3 bucket - success
    {
      _awsApiSvc = mockAWSApiSvc();
      final AWSRegionName bucketRegion = AWSRegionName.US_WEST_1;
      doReturn(Optional.of(bucketRegion))
          .when(_awsApiSvc)
          .getS3BucketRegion(any(), eq(testS3Bucket), anyBoolean(), any());
      final String endpoint = getCloudProviderConfigValidationUrl(false);
      final NDSDataLakeTenantView tenant =
          NDSDataLakeTenantView.builder()
              .cloudProviderConfig(
                  NDSDataLakeCloudProviderConfigView.builder()
                      .aws(
                          NDSDataLakeAWSCloudProviderConfigView.builder()
                              .roleId(roleId)
                              .testS3Bucket(
                                  NDSDataLakeS3BucketView.builder().name(testS3Bucket).build())
                              .build())
                      .build())
              .build();
      final JSONObject response =
          doAuthedJsonPost(_userReadOnly, endpoint, tenant, HttpStatus.SC_OK);

      // verify region population
      final JSONObject actualBucket =
          response
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.CLOUD_PROVIDER_CONFIG)
              .getJSONObject(NDSDataLakeCloudProviderConfigView.FieldDefs.AWS)
              .getJSONObject(NDSDataLakeAWSCloudProviderConfigView.FieldDefs.TEST_S3_BUCKET);
      final JSONObject expectedBucket =
          new JSONObject()
              .put(NDSDataLakeS3BucketView.FieldDefs.NAME, testS3Bucket)
              .put(NDSDataLakeS3BucketView.FieldDefs.REGION, bucketRegion.name());
      assertEquals(expectedBucket.toString(), actualBucket.toString());

      // verify aws api interactions
      verify(_awsApiSvc, times(1))
          .assumeRole(
              any(ObjectId.class),
              any(),
              eq(assumedRoleArn),
              eq(roleExternalId),
              anyString(),
              any(),
              any(),
              any(),
              any());
      verify(_awsApiSvc, times(1)).getS3BucketRegion(any(), eq(testS3Bucket), anyBoolean(), any());
      verify(_awsApiSvc, times(1))
          .listObjects(any(), eq(testS3Bucket), any(), anyInt(), anyBoolean(), any(Logger.class));
    }

    // aws role exists with s3 data stores - success
    {
      _awsApiSvc = mockAWSApiSvc();
      final String bucketPrefix = "storeBucket";
      final Map<String, AWSRegionName> bucketMapping =
          Map.of(
              bucketPrefix + 0, AWSRegionName.US_EAST_2, bucketPrefix + 1, AWSRegionName.EU_WEST_1);
      bucketMapping.forEach(
          (key, value) ->
              doReturn(Optional.of(value))
                  .when(_awsApiSvc)
                  .getS3BucketRegion(any(), eq(key), anyBoolean(), any()));

      final String endpoint = getCloudProviderConfigValidationUrl(false);

      final NDSDataLakeTenantView tenant =
          NDSDataLakeTenantView.builder()
              .cloudProviderConfig(
                  NDSDataLakeCloudProviderConfigView.builder()
                      .aws(
                          NDSDataLakeAWSCloudProviderConfigView.builder()
                              .testS3Bucket(
                                  NDSDataLakeS3BucketView.builder().name("storeBucket0").build())
                              .roleId(roleId)
                              .build())
                      .build())
              .storage(
                  NDSDataLakeStorageV1View.builder()
                      .config(
                          new Document()
                              .append(
                                  StorageConfigFieldDefs.STORES,
                                  bucketMapping.keySet().stream()
                                      .map(
                                          bucket ->
                                              new Document()
                                                  .append(Store.PROVIDER, ProviderValues.S3)
                                                  .append(Store.BUCKET, bucket))
                                      .collect(Collectors.toList())))
                      .build())
              .build();

      final JSONObject response =
          doAuthedJsonPost(_userReadOnly, endpoint, tenant, HttpStatus.SC_OK);

      // verify region population
      final Map<String, String> responseBucketMapping =
          response
              .getJSONObject(NDSDataLakeTenantView.FieldDefs.STORAGE)
              .getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG)
              .getJSONArray(StorageConfigFieldDefs.STORES)
              .toList()
              .stream()
              .map(HashMap.class::cast)
              .collect(
                  Collectors.toMap(
                      o -> (String) o.get(Store.BUCKET), o -> (String) o.get(Store.REGION)));
      // This tests a UI interaction where value-form regions (us-east-1) are expected.
      final Map<String, String> expectedBucketMapping =
          bucketMapping.entrySet().stream()
              .collect(Collectors.toMap(Entry::getKey, o -> o.getValue().getValue()));
      assertEquals(expectedBucketMapping, responseBucketMapping);

      // verify aws api interactions
      // called multiple times by populateRegions and validateS3Buckets
      verify(_awsApiSvc, times(2))
          .assumeRole(
              any(ObjectId.class),
              any(),
              eq(assumedRoleArn),
              eq(roleExternalId),
              anyString(),
              any(),
              any(),
              any(),
              any());
      bucketMapping.forEach(
          (key, value) ->
              verify(_awsApiSvc, atLeast(1))
                  .getS3BucketRegion(any(), eq(key), anyBoolean(), any()));
      verify(_awsApiSvc, atLeast(1))
          .listObjects(
              any(), startsWith(bucketPrefix), any(), anyInt(), anyBoolean(), any(Logger.class));
    }
  }

  private void assertDatabasesUpdate(
      final JSONArray pDatabasesUpdate, final JSONArray pDatabasesResult) {
    assertThat(pDatabasesResult.length(), equalTo(pDatabasesUpdate.length()));
    for (int i = 0; i < pDatabasesUpdate.length(); i++) {
      assertThat(
          pDatabasesResult.getJSONObject(i).toString(),
          equalTo(pDatabasesUpdate.get(i).toString()));
    }
  }

  private void assertStoreUpdate(final JSONArray pStoresUpdate, final JSONArray pStoreResult) {
    assertThat(pStoreResult.length(), equalTo(pStoresUpdate.length()));
    for (int i = 0; i < pStoresUpdate.length(); i++) {
      // assert that store update is a subset of store result
      final Map<String, Object> resStoreMap = pStoreResult.getJSONObject(i).toMap();
      pStoresUpdate
          .getJSONObject(i)
          .toMap()
          .forEach(
              (key, value) -> {
                if (value instanceof Boolean && !((Boolean) value)) {
                  // if expectedValue is Boolean.FALSE we're ok with it being null
                  assertThat(resStoreMap, hasEntry(equalTo(key), is(oneOf(value, null))));
                } else {
                  assertThat(resStoreMap, hasEntry(equalTo(key), equalTo(value)));
                }
              });
    }
  }

  @Test
  public void testUpdateTenant() throws DataLakeAdminApiException {
    final JSONArray stores =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(Store.PROVIDER, NDSDataLakeStoreProvider.ProviderValues.S3)
                    .put(Store.BUCKET, "bucket")
                    .put(Store.DELIMITER, JSONObject.NULL)
                    .put(
                        Store.ADDITIONAL_STORAGE_CLASSES,
                        new JSONArray().put(Store.STORAGE_CLASS_INTELLIGENT_TIERING))
                    .put(Store.INCLUDE_TAGS, false)
                    .put(Store.NAME, "storeName0")
                    .put(Store.PREFIX, "./patch-prefix-1/")
                    .put(Store.REGION, AWSRegionName.US_EAST_1.getValue()));

    final String tenantName = "uberTenant";
    createTenant(tenantName, "bucket");

    // Test PATCH and response
    final String endpoint = String.format(getDataLakeV1Url() + "/%s", tenantName);

    // Test read only user unauthorized
    {
      doAuthedJsonPatch(_userReadOnly, endpoint, new JSONObject(), HttpStatus.SC_FORBIDDEN);
    }

    // validate user can't patch tenant with non-existent role ID
    {
      final JSONObject aws =
          new JSONObject()
              .put("roleId", new ObjectId())
              .put("testS3Bucket", new JSONObject().put("name", "testbucc"));
      final JSONObject cloudProviderConfig = new JSONObject().put("aws", aws);
      final JSONObject tenant = new JSONObject().put("cloudProviderConfig", cloudProviderConfig);

      final JSONObject response =
          doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND.name(),
          response.getString("errorCode"));
    }

    // validate user can't add S3 stores without role ID
    {
      final JSONObject storage =
          new JSONObject()
              .put(
                  NDSDataLakeStorageV1View.FieldDefs.CONFIG,
                  new JSONObject()
                      .put(StorageConfigFieldDefs.STORES, stores)
                      .put(StorageConfigFieldDefs.DATABASES, JSONObject.NULL));
      final JSONObject tenant =
          new JSONObject().put(NDSDataLakeTenantView.FieldDefs.STORAGE, storage);
      final JSONObject response =
          doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.DATA_LAKE_IAM_ROLE_REQUIRED.name(), response.getString("errorCode"));
    }

    // validate user can patch tenant by providing role ID
    final ObjectId specifiedRoleId = new ObjectId();
    final String roleExternalId = UUID.randomUUID().toString();
    final String assumedRoleArn = "arn:aws:iam::************:role/role-name";
    final List<NDSCloudProviderAccessFeatureUsage> featureUsages = List.of();
    final NDSCloudProviderAccessAWSIAMRole role =
        new NDSCloudProviderAccessAWSIAMRole(
            specifiedRoleId,
            assumedRoleArn,
            featureUsages,
            new ObjectId(),
            _awsAccount.getRootARN(),
            roleExternalId,
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(_ndsGroup, role, null);

    {
      final JSONObject aws =
          new JSONObject()
              .put("roleId", specifiedRoleId)
              .put("testS3Bucket", new JSONObject().put("name", "testbucc"));
      final JSONObject cloudProviderConfig = new JSONObject().put("aws", aws);
      final JSONObject tenant =
          new JSONObject().put("name", tenantName).put("cloudProviderConfig", cloudProviderConfig);

      final JSONObject tenantResponse =
          doAuthedJsonPatch(_user, endpoint, tenant, HttpStatus.SC_OK);
      assertExternalIdAndIAMUserARNPresent(tenantResponse);
      final Optional<NDSDataLakeTenant> savedTenant =
          _tenantDao.findByGroupIdAndName(_group.getId(), tenantName);
      assertEquals(
          assumedRoleArn,
          savedTenant.get().getCloudProviderConfig().getAws().getIamAssumedRoleARN());
      assertEquals(
          roleExternalId, savedTenant.get().getCloudProviderConfig().getAws().getExternalId());
      assertEquals(
          specifiedRoleId, savedTenant.get().getCloudProviderConfig().getAws().getRoleId());

      final NDSCloudProviderAccessFeatureUsageDataLakeFeatureId dataLakeFeatureUsageId =
          _ndsGroupSvc
              .find(_ndsGroup.getGroupId())
              .map(NDSGroup::getCloudProviderAccess)
              .flatMap(cpa -> cpa.getNDSCloudProviderAccessAWSIAMRoleById(specifiedRoleId))
              .flatMap(r -> r.getFeatureUsages().stream().findFirst())
              .flatMap(NDSCloudProviderAccessFeatureUsage::getFeatureId)
              .map(NDSCloudProviderAccessFeatureUsageDataLakeFeatureId.class::cast)
              .get();
      assertEquals(_ndsGroup.getGroupId(), dataLakeFeatureUsageId.getGroupId());
      assertEquals(tenantName, dataLakeFeatureUsageId.getDataLakeName());
    }
  }

  @Test
  public void testGetAvailableRegions() {
    final String endpoint = URL_PREFIX + _group.getId() + "/availableRegions";
    final Set<String> expectedRegions =
        MockRegionsService.getMockDataLakeClientRegions().stream()
            .map(RegionName::getName)
            .collect(Collectors.toUnmodifiableSet());

    // assert correct number of regions
    final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, endpoint, HttpStatus.SC_OK);

    final Set<String> regions =
        convertJSONArrayToJSONObjectList(response).stream()
            .map(r -> r.getString("key"))
            .collect(Collectors.toSet());
    assertEquals(expectedRegions, regions);
  }

  @Test
  public void testDeleteTenant() throws SvcException {
    final String tenantName = "tenant";
    final String endpoint = String.format(getDataLakeUrl() + "/%s", tenantName);

    // Test read only user unauthorized
    {
      doAuthedJsonDelete(_userReadOnly, endpoint, HttpStatus.SC_FORBIDDEN);
    }

    // Try to delete a tenant that does not exist
    {
      final JSONObject response = doAuthedJsonDelete(_user, endpoint, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME.name(), response.getString("errorCode"));
    }

    // Try to delete a charts tenant
    {
      final NDSDataLakeTenant chartsTenant =
          NDSDataLakeModelTestFactory.getDataLakeTenantBuilder(_ndsGroup, "charts")
              .dataLakeType(NDSDataLakeTenant.DataLakeType.CHARTS)
              .build();
      _dataLakeTestUtils.saveTenant(chartsTenant);

      final JSONObject chartsDataLakeResponse =
          doAuthedJsonGet(
              _userReadOnly,
              getDataLakeV1Url() + "/" + chartsTenant.getName(),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CHARTS_DATA_LAKE_TENANT.name(),
          chartsDataLakeResponse.getString("errorCode"));
    }

    // Delete a tenant that does exist
    {
      final NDSDataLakeTenant tenant =
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName);
      _dataLakeTestUtils.saveTenant(tenant);
      doAuthedJsonDelete(_user, endpoint, HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGetMetrics() throws DataLakeAdminApiException {
    final String tenantName = "tenant";
    final String endpoint = String.format(getDataLakeUrl() + "/%s/metrics", tenantName);

    {
      // Test does not exist
      doAuthedJsonGet(
          _user,
          String.format(getDataLakeUrl() + "/%s/metrics", "dneTenant"),
          HttpStatus.SC_BAD_REQUEST);
    }
    {
      // Test charts tenant
      final NDSDataLakeTenant chartsTenant =
          NDSDataLakeModelTestFactory.getDataLakeTenantBuilder(_ndsGroup, "charts")
              .dataLakeType(NDSDataLakeTenant.DataLakeType.CHARTS)
              .build();
      _dataLakeTestUtils.saveTenant(chartsTenant);

      final JSONObject chartsDataLakeResponse =
          doAuthedJsonGet(
              _userReadOnly,
              getDataLakeV1Url() + "/" + chartsTenant.getName(),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CHARTS_DATA_LAKE_TENANT.name(),
          chartsDataLakeResponse.getString("errorCode"));
    }
    {
      final NDSDataLakeTenant tenant =
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName);
      _dataLakeTestUtils.saveTenant(tenant);

      // add dummy metrics
      _dataLakeTestUtils.setTenantMetrics(
          tenant.getTenantId().toHexString(),
          NDSDataLakeMetricsView.builder()
              .avgExecutionTime(123.0)
              .totalDataReturned(10L)
              .totalDataScanned(20L)
              .totalFailedQueries(30L)
              .totalSuccessfulQueries(40L)
              .build());

      final JSONObject metricsResponse = doAuthedJsonGet(_userReadOnly, endpoint, HttpStatus.SC_OK);
      assertEquals(
          40L, metricsResponse.getLong(NDSDataLakeMetricsView.FieldDefs.TOTAL_SUCCESSFUL_QUERIES));
      assertEquals(
          30L, metricsResponse.getLong(NDSDataLakeMetricsView.FieldDefs.TOTAL_FAILED_QUERIES));
      assertEquals(
          20L, metricsResponse.getLong(NDSDataLakeMetricsView.FieldDefs.TOTAL_DATA_SCANNED));
      assertEquals(
          10L, metricsResponse.getLong(NDSDataLakeMetricsView.FieldDefs.TOTAL_DATA_RETURNED));
      assertEquals(
          0.0,
          metricsResponse.getDouble(NDSDataLakeMetricsView.FieldDefs.AVG_EXECUTION_TIME),
          123.0);
    }
  }

  @Test
  public void testGetQueryLogs() throws DataLakeAdminApiException, IOException {
    final String tenantName = "tenant";
    final LocalDateTime end = LocalDateTime.now().withNano(0);
    final LocalDateTime start = end.minusDays(1);
    final long endEpoch = end.toEpochSecond(ZoneOffset.UTC);
    final long startEpoch = start.toEpochSecond(ZoneOffset.UTC);
    final String endpoint =
        String.format(
            getDataLakeUrl() + "/%s/queryLogs.gz?startDate=%s&endDate=%s",
            tenantName,
            startEpoch,
            endEpoch);
    {
      // tenant does not exist
      doAuthedGetBytes(
          _groupDataAccessReadOnly,
          String.format(
              getDataLakeUrl() + "/%s/queryLogs.gz?startDate=%s&endDate=%s",
              "dneTenant",
              startEpoch,
              endEpoch),
          HttpStatus.SC_BAD_REQUEST);
    }
    {
      // start/end time not included
      _dataLakeTestUtils.saveTenant(
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName));
      doAuthedGetBytes(
          _groupDataAccessReadOnly,
          String.format(getDataLakeUrl() + "/%s/queryLogs.gz", tenantName),
          HttpStatus.SC_BAD_REQUEST);
    }
    {
      // user does not have sufficient permissions
      doAuthedGetBytes(_userReadOnly, endpoint, HttpStatus.SC_FORBIDDEN);
    }
    {
      // charts tenant
      final NDSDataLakeTenant chartsTenant =
          NDSDataLakeModelTestFactory.getDataLakeTenantBuilder(_ndsGroup, "charts")
              .dataLakeType(NDSDataLakeTenant.DataLakeType.CHARTS)
              .build();
      _dataLakeTestUtils.saveTenant(chartsTenant);

      final JSONObject chartsDataLakeResponse =
          doAuthedJsonGet(
              _userReadOnly,
              getDataLakeV1Url() + "/" + chartsTenant.getName(),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CHARTS_DATA_LAKE_TENANT.name(),
          chartsDataLakeResponse.getString("errorCode"));
    }

    // ok
    {
      _dataLakeTestUtils.saveTenant(
          NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName));
      final NDSDataLakeTenant tenant =
          _tenantDao.findByGroupIdAndName(_ndsGroup.getGroupId(), tenantName).orElseThrow();
      final byte[] logsResponse =
          doAuthedGetBytes(_groupDataAccessReadOnly, endpoint, HttpStatus.SC_OK);

      // check audit
      final List<Event> pEvents =
          _auditSvc.findByDate(FEDERATED_DATABASE_QUERY_LOGS_DOWNLOADED, Timestamp.valueOf(end));
      assertNotNull(pEvents);
      assertEquals(1, pEvents.size());
      final NDSAudit audit = (NDSAudit) pEvents.get(0);
      assertEquals(_groupDataAccessReadOnly.getId(), audit.getUserId());
      assertEquals(_group.getId(), audit.getGroupId());
      assertEquals(FEDERATED_DATABASE_QUERY_LOGS_DOWNLOADED, audit.getEventType());
      assertEquals(tenant.getName(), audit.getDataLakeTenantName());

      // check response content and dates
      final String response = new String(GZipCompressionUtils.ungzipBytes(logsResponse));
      assertTrue(response.contains(tenant.getTenantId().toString()));
      assertTrue(response.contains(start.toString()));
      assertTrue(response.contains(end.toString()));
    }
  }

  @Test
  public void testGetQueryLimits() throws SvcException {
    final String limitsUrl = getQueryLimitsUrl();

    // success - empty
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }

    // add project usage limit
    final UsageLimit limit1 = LimitTestFactory.getProjectUsageLimit(_group.getId());
    _dataLakeTestUtils.setUsageLimit(limit1);
    assertTrue(_dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());

    // success - project
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(1, response.length());
      final List<JSONObject> responseJSONObjects = convertJSONArrayToJSONObjectList(response);
      final List<JSONObject> projectJSONObjects =
          responseJSONObjects.stream()
              .filter(o -> !o.has(FieldDefs.TENANT_NAME))
              .collect(Collectors.toList());
      assertEquals(1, projectJSONObjects.size());
      final JSONObject responseJSONObject = projectJSONObjects.get(0);
      assertEquals(_group.getId().toHexString(), responseJSONObject.getString(FieldDefs.GROUP_ID));
      assertFalse(responseJSONObject.has(FieldDefs.TENANT_NAME));
      assertEquals(LimitSpan.QUERY.name(), responseJSONObject.getString(FieldDefs.LIMIT_SPAN));
      assertEquals(
          (long) Units.convert(117.0, Units.GIGABYTES, Units.BYTES),
          responseJSONObject.getLong(FieldDefs.LIMIT_IN_BYTES));
      assertTrue(
          _dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());
    }

    // delete project usage limit
    _dataLakeTestUtils.deleteUsageLimit(limit1);
    assertFalse(
        _dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());

    // add tenant
    final String tenantName = "limited";
    _dataLakeTestUtils.saveTenant(
        NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName));
    final NDSDataLakeTenant tenant =
        _tenantDao.findByGroupIdAndName(_group.getId(), tenantName).orElseThrow();
    final ObjectId tenantId = tenant.getTenantId();

    // add tenant usage limit
    final UsageLimit limit2 = LimitTestFactory.getTenantUsageLimit(_group.getId(), tenantId);
    _dataLakeTestUtils.setUsageLimit(limit2);
    assertTrue(
        _dataLakeTestUtils
            .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
            .isPresent());

    // success - tenant
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(1, response.length());
      final List<JSONObject> responseJSONObjects = convertJSONArrayToJSONObjectList(response);
      final List<JSONObject> tenantJSONObjects =
          responseJSONObjects.stream()
              .filter(o -> o.has(FieldDefs.TENANT_NAME))
              .collect(Collectors.toList());
      assertEquals(1, tenantJSONObjects.size());
      final JSONObject tenantJSONObject = tenantJSONObjects.get(0);
      assertNotNull(tenantJSONObject);
      assertEquals(_group.getId().toHexString(), tenantJSONObject.getString(FieldDefs.GROUP_ID));
      assertEquals(tenantName, tenantJSONObject.getString(FieldDefs.TENANT_NAME));
      assertEquals(LimitSpan.QUERY.name(), tenantJSONObject.getString(FieldDefs.LIMIT_SPAN));
      assertEquals(
          (long) Units.convert(117.0, Units.GIGABYTES, Units.BYTES),
          tenantJSONObject.getLong(FieldDefs.LIMIT_IN_BYTES));
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .isPresent());
    }

    // add 10 tenants in DELETED state
    final List<NDSDataLakeTenant> deletedTenants =
        IntStream.range(0, 10)
            .mapToObj(
                i ->
                    NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, "limitedDeleted" + i)
                        .toBuilder()
                        .state(NDSDataLakeState.DELETED)
                        .build())
            .collect(Collectors.toList());
    deletedTenants.forEach(
        t -> {
          try {
            _dataLakeTestUtils.saveTenant(t);
            final UsageLimit limit =
                LimitTestFactory.getTenantUsageLimit(_group.getId(), t.getTenantId());
            _dataLakeTestUtils.setUsageLimit(limit);
          } catch (SvcException pE) {
            pE.printStackTrace();
          }
        });
    deletedTenants.forEach(
        t -> {
          assertTrue(
              _dataLakeTestUtils
                  .getAwsUsageLimit(
                      _group.getId().toHexString(), t.getTenantId().toHexString(), "QUERY")
                  .isPresent());
        });

    // success - only 1 limit
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(1, response.length());
      final List<JSONObject> responseJSONObjects = convertJSONArrayToJSONObjectList(response);
      final List<JSONObject> tenantJSONObjects =
          responseJSONObjects.stream()
              .filter(o -> o.has(FieldDefs.TENANT_NAME))
              .collect(Collectors.toList());
      assertEquals(1, tenantJSONObjects.size());
      final JSONObject tenantJSONObject = tenantJSONObjects.get(0);
      assertNotNull(tenantJSONObject);
      assertEquals(_group.getId().toHexString(), tenantJSONObject.getString(FieldDefs.GROUP_ID));
      assertEquals(tenantName, tenantJSONObject.getString(FieldDefs.TENANT_NAME));
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .isPresent());
    }

    // add project usage limit
    final UsageLimit limit4 = LimitTestFactory.getProjectUsageLimit(_group.getId());
    _dataLakeTestUtils.setUsageLimit(limit4);
    assertTrue(_dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());

    // success - project + tenant
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(2, response.length());
      final List<JSONObject> responseJSONObjects = convertJSONArrayToJSONObjectList(response);
      final List<JSONObject> projectJSONObjects =
          responseJSONObjects.stream()
              .filter(o -> !o.has(FieldDefs.TENANT_NAME))
              .collect(Collectors.toList());
      assertEquals(1, projectJSONObjects.size());
      final List<JSONObject> tenantJSONObjects =
          responseJSONObjects.stream()
              .filter(o -> o.has(FieldDefs.TENANT_NAME))
              .collect(Collectors.toList());
      assertEquals(1, tenantJSONObjects.size());
      assertTrue(
          _dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .isPresent());
    }

    // add atlas_sql, charts and streams tenants and limits
    final List<NDSDataLakeTenant> notUserTypeTenants =
        Stream.of(
                NDSDataLakeTenant.DataLakeType.ATLAS_SQL,
                NDSDataLakeTenant.DataLakeType.CHARTS,
                NDSDataLakeTenant.DataLakeType.STREAM)
            .map(
                type -> {
                  final NDSDataLakeTenant t =
                      NDSDataLakeModelTestFactory.getDataLakeTenantBuilder(
                              _ndsGroup, String.format("%sTenant", type))
                          .dataLakeType(type)
                          .build();
                  try {
                    _dataLakeTestUtils.saveTenant(t);
                    _dataLakeTestUtils.setUsageLimit(
                        LimitTestFactory.getTenantUsageLimit(_group.getId(), t.getTenantId()));
                  } catch (SvcException pE) {
                    pE.printStackTrace();
                  }
                  return t;
                })
            .collect(Collectors.toList());
    notUserTypeTenants.forEach(
        t -> {
          assertTrue(
              _dataLakeTestUtils
                  .getAwsUsageLimit(
                      _group.getId().toHexString(), t.getTenantId().toHexString(), "QUERY")
                  .isPresent());
        });
    // success - also have atlas sql tenant limits
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(3, response.length());
      final List<JSONObject> responseJSONObjects = convertJSONArrayToJSONObjectList(response);
      final List<JSONObject> tenantJSONObjects =
          responseJSONObjects.stream()
              .filter(o -> o.has(FieldDefs.TENANT_NAME))
              .collect(Collectors.toList());
      assertEquals(2, tenantJSONObjects.size());
      final Set<String> tenantNames =
          tenantJSONObjects.stream()
              .map(tenantJSONObject -> tenantJSONObject.getString(FieldDefs.TENANT_NAME))
              .collect(Collectors.toSet());
      assertTrue(tenantNames.contains(tenantName));
      assertTrue(tenantNames.contains(notUserTypeTenants.get(0).getName())); // Atlas SQL
    }

    // orphaned limits should be deleted asynchronously
    deletedTenants.forEach(
        t -> {
          assertTrue(
              _dataLakeTestUtils
                  .getAwsUsageLimit(
                      _group.getId().toHexString(), t.getTenantId().toHexString(), "QUERY")
                  .isEmpty());
        });
  }

  @Test
  public void testGetQueryLimits_deletedOnlineArchive() throws SvcException {
    final String limitsUrl = getQueryLimitsUrl();

    // add online archive
    final OnlineArchive oa = getOnlineArchive();
    _onlineArchiveSvc.create(oa, AuditInfoHelpers.fromSystem());

    // verify default oa tenant limits exist
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(2, response.length());
    }

    // add oa tenant usage limits
    final OnlineArchiveDataLakeConfig config =
        _onlineArchiveSvc.getValidateOnlineArchiveDataLakeConfig(_group.getId(), _clusterName);
    final NDSDataLakeTenant oaTenant =
        _tenantDao.findById(config.getDataLakeTenantId()).orElseThrow();
    final NDSDataLakeTenant archiveTenant =
        _tenantDao.findById(config.getArchiveOnlyDataLakeTenantId()).orElseThrow();
    _dataLakeTestUtils.setUsageLimit(
        LimitTestFactory.getTenantUsageLimit(_group.getId(), oaTenant.getTenantId()));
    _dataLakeTestUtils.setUsageLimit(
        LimitTestFactory.getTenantUsageLimit(_group.getId(), archiveTenant.getTenantId()));

    // verify oa tenant limits exist
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(4, response.length());
    }

    // delete OA
    _onlineArchiveSvc.markForDeletion(oa.getId(), AuditInfoHelpers.fromSystem(), _group.getId());

    // verify data federations still exist
    assertTrue(
        _tenantDao
            .findById(config.getDataLakeTenantId())
            .map(NDSDataLakeTenant::getState)
            .filter(NDSDataLakeState.ACTIVE::equals)
            .isPresent());
    assertTrue(
        _tenantDao
            .findById(config.getArchiveOnlyDataLakeTenantId())
            .map(NDSDataLakeTenant::getState)
            .filter(NDSDataLakeState.ACTIVE::equals)
            .isPresent());

    // verify oa tenant limits still exist
    assertTrue(
        _dataLakeTestUtils
            .getAwsUsageLimit(
                _group.getId().toHexString(), archiveTenant.getTenantId().toHexString(), "QUERY")
            .isPresent());
    assertTrue(
        _dataLakeTestUtils
            .getAwsUsageLimit(
                _group.getId().toHexString(), oaTenant.getTenantId().toHexString(), "QUERY")
            .isPresent());

    // verify oa tenant limits are not returned
    {
      final JSONArray response = doAuthedJsonArrayGet(_userReadOnly, limitsUrl, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }
  }

  @Test
  public void testSetProjectQueryLimit() throws SvcException {
    final String limitsUrl = getQueryLimitsUrl();

    final long limitInBytes = (long) Units.convert(118.0, Units.GIGABYTES, Units.BYTES);
    final JSONObject usageLimitViewJSON = getUsageLimitViewJSON(limitInBytes, "BLOCK");

    // unauthorized
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () ->
                  doAuthedJsonPatch(
                      _userReadOnly,
                      limitsUrl + "/query",
                      usageLimitViewJSON,
                      HttpStatus.SC_FORBIDDEN));
    }

    // bad limit span
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () -> {
                final JSONObject response =
                    doAuthedJsonPatch(
                        _user,
                        limitsUrl + "/badSpan",
                        usageLimitViewJSON,
                        HttpStatus.SC_BAD_REQUEST);
                assertEquals(
                    NDSErrorCode.DATA_FEDERATION_INVALID_USAGE_LIMIT_SPAN.name(),
                    response.getString("errorCode"));
              });
    }

    // invalid overrun policy
    {
      final JSONObject usageLimitViewJSON2 =
          getUsageLimitViewJSON(limitInBytes, "badOverrunPolicy");
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () ->
                  doAuthedJsonPatch(
                      _user,
                      limitsUrl + "/monthly",
                      usageLimitViewJSON2,
                      HttpStatus.SC_BAD_REQUEST));
    }

    // success - per query
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertNull(event.getDataLakeTenantName());
                assertEquals(LimitSpan.QUERY, event.getDataFederationQueryLimitSpan());
                assertEquals(OverrunPolicy.BLOCK, event.getDataFederationQueryLimitOverrunPolicy());
                assertEquals(limitInBytes, (long) event.getLimitValue());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/query", usageLimitViewJSON, HttpStatus.SC_OK),
                      _group.getId(),
                      null,
                      LimitSpan.QUERY,
                      null,
                      limitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), null, "QUERY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(actualUsageLimit.getPerQueryScanningLimit().getLimitBytes(), limitInBytes);
    }

    // success - time span
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertNull(event.getDataLakeTenantName());
                assertEquals(LimitSpan.WEEKLY, event.getDataFederationQueryLimitSpan());
                assertEquals(limitInBytes, (long) event.getLimitValue());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/weekly", usageLimitViewJSON, HttpStatus.SC_OK),
                      _group.getId(),
                      null,
                      LimitSpan.WEEKLY,
                      OverrunPolicy.BLOCK,
                      limitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), null, "LIMIT_SPAN_WEEKLY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(), limitInBytes);
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(),
          Models.LimitSpan.LIMIT_SPAN_WEEKLY);
    }

    // success - updating a limit - limit value
    {
      // add usage limit
      final UsageLimit limit = LimitTestFactory.getProjectUsageLimit(_group.getId());
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());

      final long updatedLimitInBytes = (long) Units.convert(118.0, Units.GIGABYTES, Units.BYTES);
      final JSONObject usageLimitViewJSON3 =
          getUsageLimitViewJSON(updatedLimitInBytes, OverrunPolicy.BLOCK.name());

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertNull(event.getDataLakeTenantName());
                assertEquals(LimitSpan.QUERY, event.getDataFederationQueryLimitSpan());
                assertEquals(updatedLimitInBytes, (long) event.getLimitValue());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/query", usageLimitViewJSON3, HttpStatus.SC_OK),
                      _group.getId(),
                      null,
                      LimitSpan.QUERY,
                      null,
                      updatedLimitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), null, "QUERY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getLimitBytes(), updatedLimitInBytes);
    }

    // success - updating a limit - overrun policy
    {
      // add usage limit
      final UsageLimit limit =
          UsageLimit.newBuilder()
              .setTimeSpanScanningLimit(
                  LimitTestFactory.getProjectTimeSpanScanningLimit(_group.getId()))
              .build();
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils
              .getUsageLimit(_group.getId().toHexString(), "LIMIT_SPAN_DAILY")
              .isPresent());

      final OverrunPolicy updatedOverrunPolicy = OverrunPolicy.BLOCK_AND_KILL;
      final JSONObject usageLimitViewJSON4 = getUsageLimitViewJSON(updatedOverrunPolicy.name());

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertNull(event.getDataLakeTenantName());
                assertEquals(LimitSpan.DAILY, event.getDataFederationQueryLimitSpan());
                assertEquals(
                    updatedOverrunPolicy, event.getDataFederationQueryLimitOverrunPolicy());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/daily", usageLimitViewJSON4, HttpStatus.SC_OK),
                      _group.getId(),
                      null,
                      LimitSpan.DAILY,
                      updatedOverrunPolicy,
                      limit.getTimeSpanScanningLimit().getLimitBytes()));

      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), null, "LIMIT_SPAN_DAILY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getOverrunPolicy(),
          Models.OverrunPolicy.OVERRUN_POLICY_BLOCK_AND_KILL);
    }

    // success - updating a limit - limit value + overrun policy
    {
      // add usage limit
      final UsageLimit limit =
          UsageLimit.newBuilder()
              .setTimeSpanScanningLimit(
                  LimitTestFactory.getProjectTimeSpanScanningLimit(_group.getId()))
              .build();
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils
              .getUsageLimit(_group.getId().toHexString(), "LIMIT_SPAN_DAILY")
              .isPresent());

      final long updatedLimitInBytes = (long) Units.convert(120.0, Units.GIGABYTES, Units.BYTES);
      final OverrunPolicy updatedOverrunPolicy = OverrunPolicy.BLOCK_AND_KILL;
      final JSONObject usageLimitViewJSON4 =
          getUsageLimitViewJSON(updatedLimitInBytes, updatedOverrunPolicy.name());

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertNull(event.getDataLakeTenantName());
                assertEquals(LimitSpan.DAILY, event.getDataFederationQueryLimitSpan());
                assertEquals(updatedLimitInBytes, (long) event.getLimitValue());
                assertEquals(
                    updatedOverrunPolicy, event.getDataFederationQueryLimitOverrunPolicy());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/daily", usageLimitViewJSON4, HttpStatus.SC_OK),
                      _group.getId(),
                      null,
                      LimitSpan.DAILY,
                      updatedOverrunPolicy,
                      updatedLimitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), null, "LIMIT_SPAN_DAILY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(), updatedLimitInBytes);
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getOverrunPolicy(),
          Models.OverrunPolicy.OVERRUN_POLICY_BLOCK_AND_KILL);
    }

    // does not configure limit according to groupId specified in the request view
    {
      // add usage limit
      final UsageLimit limit = LimitTestFactory.getProjectUsageLimit(_group.getId());
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());

      final long updatedLimitInBytes = (long) Units.convert(118.0, Units.GIGABYTES, Units.BYTES);
      final JSONObject usageLimitViewJSON5 =
          new JSONObject()
              .put(FieldDefs.GROUP_ID, ObjectId.get())
              .put(DataFederationUsageLimitView.FieldDefs.LIMIT_IN_BYTES, updatedLimitInBytes);

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () ->
                  doAuthedJsonPatch(
                      _user,
                      limitsUrl + "/query",
                      usageLimitViewJSON5,
                      HttpStatus.SC_UNAUTHORIZED));
    }
  }

  @Test
  public void testSetTenantQueryLimit() throws SvcException {
    final String tenantName = "tenantName";
    _dataLakeTestUtils.saveTenant(
        NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName));
    final NDSDataLakeTenant tenant =
        _tenantDao.findByGroupIdAndName(_group.getId(), tenantName).orElseThrow();
    final ObjectId tenantId = tenant.getTenantId();

    final long limitInBytes = (long) Units.convert(118.0, Units.GIGABYTES, Units.BYTES);
    final JSONObject usageLimitViewJSON =
        getUsageLimitViewJSON(limitInBytes, OverrunPolicy.BLOCK.name());

    final String limitsUrl = getQueryLimitsUrl(tenantName);

    // unauthorized
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () ->
                  doAuthedJsonPatch(
                      _userReadOnly,
                      limitsUrl + "/query",
                      usageLimitViewJSON,
                      HttpStatus.SC_FORBIDDEN));
    }

    // bad limit span
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () -> {
                final JSONObject response =
                    doAuthedJsonPatch(
                        _user,
                        limitsUrl + "/badSpan",
                        usageLimitViewJSON,
                        HttpStatus.SC_BAD_REQUEST);
                assertEquals(
                    NDSErrorCode.DATA_FEDERATION_INVALID_USAGE_LIMIT_SPAN.name(),
                    response.getString("errorCode"));
              });
    }

    // invalid overrun policy
    {
      final JSONObject usageLimitViewJSON2 =
          getUsageLimitViewJSON(limitInBytes, "badOverrunPolicy");
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () ->
                  doAuthedJsonPatch(
                      _user,
                      limitsUrl + "/monthly",
                      usageLimitViewJSON2,
                      HttpStatus.SC_BAD_REQUEST));
    }

    // success - per query
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertEquals(tenantName, event.getDataLakeTenantName());
                assertEquals(LimitSpan.QUERY, event.getDataFederationQueryLimitSpan());
                assertEquals(OverrunPolicy.BLOCK, event.getDataFederationQueryLimitOverrunPolicy());
                assertEquals(limitInBytes, (long) event.getLimitValue());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/query", usageLimitViewJSON, HttpStatus.SC_OK),
                      _group.getId(),
                      tenantName,
                      LimitSpan.QUERY,
                      null,
                      limitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getTenantId().getValue(),
          tenantId.toHexString());
      assertEquals(actualUsageLimit.getPerQueryScanningLimit().getLimitBytes(), limitInBytes);
    }

    // success - time span
    {
      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertEquals(tenantName, event.getDataLakeTenantName());
                assertEquals(LimitSpan.WEEKLY, event.getDataFederationQueryLimitSpan());
                assertEquals(OverrunPolicy.BLOCK, event.getDataFederationQueryLimitOverrunPolicy());
                assertEquals(limitInBytes, (long) event.getLimitValue());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/weekly", usageLimitViewJSON, HttpStatus.SC_OK),
                      _group.getId(),
                      tenantName,
                      LimitSpan.WEEKLY,
                      OverrunPolicy.BLOCK,
                      limitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(
                  _group.getId().toHexString(), tenantId.toHexString(), "LIMIT_SPAN_WEEKLY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getTenantId().getValue(),
          tenantId.toHexString());
      assertEquals(actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(), limitInBytes);
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(),
          Models.LimitSpan.LIMIT_SPAN_WEEKLY);
    }

    // success - updating a limit - limit value
    {
      // add usage limit
      final UsageLimit limit = LimitTestFactory.getTenantUsageLimit(_group.getId(), tenantId);
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .isPresent());

      final long updatedLimitInBytes = (long) Units.convert(119.0, Units.GIGABYTES, Units.BYTES);
      final JSONObject usageLimitViewJSON3 =
          getUsageLimitViewJSON(updatedLimitInBytes, OverrunPolicy.BLOCK.name());

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertEquals(tenantName, event.getDataLakeTenantName());
                assertEquals(updatedLimitInBytes, (long) event.getLimitValue());
                assertEquals(OverrunPolicy.BLOCK, event.getDataFederationQueryLimitOverrunPolicy());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/query", usageLimitViewJSON3, HttpStatus.SC_OK),
                      _group.getId(),
                      tenantName,
                      LimitSpan.QUERY,
                      null,
                      updatedLimitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getPerQueryScanningLimit().getLimitBytes(), updatedLimitInBytes);
    }

    // success - updating a limit - overrun policy
    {
      // add usage limit
      final UsageLimit limit =
          UsageLimit.newBuilder()
              .setTimeSpanScanningLimit(
                  LimitTestFactory.getTenantTimeSpanScanningLimit(_group.getId(), tenantId))
              .build();
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(
                  _group.getId().toHexString(), tenantId.toHexString(), "LIMIT_SPAN_DAILY")
              .isPresent());

      final OverrunPolicy updatedOverrunPolicy = OverrunPolicy.BLOCK_AND_KILL;
      final JSONObject usageLimitViewJSON4 = getUsageLimitViewJSON(updatedOverrunPolicy.name());

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertEquals(tenantName, event.getDataLakeTenantName());
                assertEquals(LimitSpan.DAILY, event.getDataFederationQueryLimitSpan());
                assertEquals(
                    updatedOverrunPolicy, event.getDataFederationQueryLimitOverrunPolicy());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/daily", usageLimitViewJSON4, HttpStatus.SC_OK),
                      _group.getId(),
                      tenantName,
                      LimitSpan.DAILY,
                      updatedOverrunPolicy,
                      limit.getTimeSpanScanningLimitOrBuilder().getLimitBytes()));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(
                  _group.getId().toHexString(), tenantId.toHexString(), "LIMIT_SPAN_DAILY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getTenantId().getValue(),
          tenantId.toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getOverrunPolicy(),
          Models.OverrunPolicy.OVERRUN_POLICY_BLOCK_AND_KILL);
    }

    // success - updating a limit - limit value + overrun policy
    {
      // add usage limit
      final UsageLimit limit =
          UsageLimit.newBuilder()
              .setTimeSpanScanningLimit(
                  LimitTestFactory.getTenantTimeSpanScanningLimit(_group.getId(), tenantId))
              .build();
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(
                  _group.getId().toHexString(), tenantId.toHexString(), "LIMIT_SPAN_DAILY")
              .isPresent());

      final long updatedLimitInBytes = (long) Units.convert(120.0, Units.GIGABYTES, Units.BYTES);
      final OverrunPolicy updatedOverrunPolicy = OverrunPolicy.BLOCK_AND_KILL;
      final JSONObject usageLimitViewJSON4 =
          getUsageLimitViewJSON(updatedLimitInBytes, updatedOverrunPolicy.name());

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willBeAudited(
              audits -> {
                assertEquals(1, audits.size());
                final NDSAudit event = (NDSAudit) audits.get(0);
                assertEquals(_group.getId(), event.getGroupId());
                assertEquals(tenantName, event.getDataLakeTenantName());
                assertEquals(LimitSpan.DAILY, event.getDataFederationQueryLimitSpan());
                assertEquals(updatedLimitInBytes, (long) event.getLimitValue());
                assertEquals(
                    updatedOverrunPolicy, event.getDataFederationQueryLimitOverrunPolicy());
              })
          .during(
              () ->
                  assertUsageLimitResponse(
                      doAuthedJsonPatch(
                          _user, limitsUrl + "/daily", usageLimitViewJSON4, HttpStatus.SC_OK),
                      _group.getId(),
                      tenantName,
                      LimitSpan.DAILY,
                      updatedOverrunPolicy,
                      updatedLimitInBytes));
      final UsageLimit actualUsageLimit =
          _dataLakeTestUtils
              .getAwsUsageLimit(
                  _group.getId().toHexString(), tenantId.toHexString(), "LIMIT_SPAN_DAILY")
              .map(DataScanningLimitStatus::getUsageLimit)
              .orElseThrow();
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
          _group.getId().toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getTenantId().getValue(),
          tenantId.toHexString());
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(), updatedLimitInBytes);
      assertEquals(
          actualUsageLimit.getTimeSpanScanningLimit().getOverrunPolicy(),
          Models.OverrunPolicy.OVERRUN_POLICY_BLOCK_AND_KILL);
    }

    // does not configure limit according to tenantName specified in the request view
    {
      // add usage limit
      final UsageLimit limit = LimitTestFactory.getTenantUsageLimit(_group.getId(), tenantId);
      _dataLakeTestUtils.setUsageLimit(limit);
      assertTrue(
          _dataLakeTestUtils
              .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
              .isPresent());

      final long updatedLimitInBytes = (long) Units.convert(119.0, Units.GIGABYTES, Units.BYTES);
      final JSONObject usageLimitViewJSON4 =
          new JSONObject()
              .put(FieldDefs.TENANT_NAME, "anotherTenant")
              .put(DataFederationUsageLimitView.FieldDefs.LIMIT_IN_BYTES, updatedLimitInBytes);

      AuditSvcIntTestUtils.verifyEvents(_auditSvc)
          .ofType(DATA_FEDERATION_QUERY_LIMIT_CONFIGURED)
          .willNotBeAudited()
          .during(
              () ->
                  doAuthedJsonPatch(
                      _user,
                      limitsUrl + "/query",
                      usageLimitViewJSON4,
                      HttpStatus.SC_UNAUTHORIZED));
    }
  }

  @Test
  public void testDeleteProjectQueryLimit() throws SvcException {
    // add usage limit
    final UsageLimit limit = LimitTestFactory.getProjectUsageLimit(_group.getId());
    _dataLakeTestUtils.setUsageLimit(limit);
    assertTrue(_dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isPresent());

    final String limitsUrl = getQueryLimitsUrl();

    // bad limit span
    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(DATA_FEDERATION_QUERY_LIMIT_DELETED)
        .willNotBeAudited()
        .during(
            () -> {
              final JSONObject response =
                  doAuthedJsonDelete(_user, limitsUrl + "/badSpan", HttpStatus.SC_BAD_REQUEST);
              assertEquals(
                  NDSErrorCode.DATA_FEDERATION_INVALID_USAGE_LIMIT_SPAN.name(),
                  response.getString("errorCode"));
            });

    // not authorized
    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(DATA_FEDERATION_QUERY_LIMIT_DELETED)
        .willNotBeAudited()
        .during(
            () -> doAuthedJsonDelete(_userReadOnly, limitsUrl + "/query", HttpStatus.SC_FORBIDDEN));

    // success
    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(DATA_FEDERATION_QUERY_LIMIT_DELETED)
        .willBeAudited(
            audits -> {
              assertEquals(1, audits.size());
              final NDSAudit event = (NDSAudit) audits.get(0);
              assertEquals(_group.getId(), event.getGroupId());
              assertNull(event.getDataLakeTenantName());
              assertEquals(LimitSpan.QUERY, event.getDataFederationQueryLimitSpan());
            })
        .during(() -> doAuthedJsonDelete(_user, limitsUrl + "/query", HttpStatus.SC_NO_CONTENT));
    assertTrue(_dataLakeTestUtils.getUsageLimit(_group.getId().toHexString(), "QUERY").isEmpty());
  }

  @Test
  public void testDeleteTenantQueryLimit() throws SvcException {
    final String tenantName = "limited";
    _dataLakeTestUtils.saveTenant(
        NDSDataLakeModelTestFactory.getDataLakeTenant(_ndsGroup, tenantName));
    final NDSDataLakeTenant tenant =
        _tenantDao.findByGroupIdAndName(_group.getId(), tenantName).orElseThrow();
    final ObjectId tenantId = tenant.getTenantId();
    // add usage limit
    final UsageLimit limit = LimitTestFactory.getTenantUsageLimit(_group.getId(), tenantId);
    _dataLakeTestUtils.setUsageLimit(limit);
    assertTrue(
        _dataLakeTestUtils
            .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
            .isPresent());

    final String limitsUrl = getQueryLimitsUrl(tenantName);

    // bad limit span
    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(DATA_FEDERATION_QUERY_LIMIT_DELETED)
        .willNotBeAudited()
        .during(
            () -> {
              final JSONObject response =
                  doAuthedJsonDelete(_user, limitsUrl + "/badSpan", HttpStatus.SC_BAD_REQUEST);
              assertEquals(
                  NDSErrorCode.DATA_FEDERATION_INVALID_USAGE_LIMIT_SPAN.name(),
                  response.getString("errorCode"));
            });

    // not authorized
    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(DATA_FEDERATION_QUERY_LIMIT_DELETED)
        .willNotBeAudited()
        .during(
            () -> doAuthedJsonDelete(_userReadOnly, limitsUrl + "/query", HttpStatus.SC_FORBIDDEN));

    // success
    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(DATA_FEDERATION_QUERY_LIMIT_DELETED)
        .willBeAudited(
            audits -> {
              assertEquals(1, audits.size());
              final NDSAudit event = (NDSAudit) audits.get(0);
              assertEquals(_group.getId(), event.getGroupId());
              assertEquals(tenantName, event.getDataLakeTenantName());
              assertEquals(LimitSpan.QUERY, event.getDataFederationQueryLimitSpan());
            })
        .during(() -> doAuthedJsonDelete(_user, limitsUrl + "/query", HttpStatus.SC_NO_CONTENT));
    assertTrue(
        _dataLakeTestUtils
            .getAwsUsageLimit(_group.getId().toHexString(), tenantId.toHexString(), "QUERY")
            .isEmpty());
  }

  private String getDataLakeUrl() {
    return URL_PREFIX + _group.getId() + "/tenants";
  }

  private String getDataLakeV1Url() {
    return URL_PREFIX + _group.getId() + "/v1/tenants";
  }

  private String getQueryLimitsUrl() {
    return getQueryLimitsUrl(null);
  }

  private String getQueryLimitsUrl(final String pTenantName) {
    return String.format(
        URL_PREFIX + _group.getId() + "/queryLimits%s",
        pTenantName == null ? "" : "/tenants/" + pTenantName);
  }

  private String getAtlasSQLDataLakeUrl(final String pClusterName) {
    return URL_PREFIX + _group.getId() + "/atlasSQL/" + pClusterName;
  }

  private String getStorageValidationUrl() {
    return URL_PREFIX + _group.getId() + "/v1/validateStorage";
  }

  private String getCloudProviderConfigValidationUrl(final boolean pSkipRoleValidation) {
    return URL_PREFIX
        + _group.getId()
        + "/v1/validateCloudProviderConfig"
        + (pSkipRoleValidation ? "?skipRoleValidation=true" : "");
  }

  private void assertExternalIdAndIAMUserARNPresent(final JSONObject pDataLakeTenantJSON) {
    assertExternalIdAndIAMUserARNFieldsShouldExist(pDataLakeTenantJSON, true);
  }

  private void assertExternalIdAndIAMUserARNFieldsShouldExist(
      final JSONObject pDataLakeTenantJSON, final boolean pShouldExist) {
    final JSONObject awsCloudProviderConfigJSON =
        pDataLakeTenantJSON
            .getJSONObject(NDSDataLakeTenantView.FieldDefs.CLOUD_PROVIDER_CONFIG)
            .getJSONObject(NDSDataLakeCloudProviderConfigView.FieldDefs.AWS);
    assertTrue(
        awsCloudProviderConfigJSON.has(
            NDSDataLakeAWSCloudProviderConfigView.FieldDefs.IAM_USER_ARN));
    assertTrue(
        awsCloudProviderConfigJSON.has(
            NDSDataLakeAWSCloudProviderConfigView.FieldDefs.EXTERNAL_ID));
    assertEquals(
        pShouldExist,
        !awsCloudProviderConfigJSON.isNull(
            NDSDataLakeAWSCloudProviderConfigView.FieldDefs.IAM_USER_ARN));
    assertEquals(
        pShouldExist,
        !awsCloudProviderConfigJSON.isNull(
            NDSDataLakeAWSCloudProviderConfigView.FieldDefs.EXTERNAL_ID));
  }

  private GCPApiSvc mockGCPApiSvc() throws IllegalAccessException, NoSuchFieldException {
    final GCPApiSvc apiSvc = mock(GCPApiSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePublicSvc.class), "_gcpApiSvc", apiSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_gcpApiSvc", apiSvc);
    return apiSvc;
  }

  private AWSApiSvc mockAWSApiSvc() throws IllegalAccessException, NoSuchFieldException {
    final AWSApiSvc apiSvc = mock(AWSApiSvc.class);
    final Credentials tempCredentials = mock(Credentials.class);
    doReturn("THEK3Y2SUCCEZZIZZZM333:)").when(tempCredentials).getAccessKeyId();
    doReturn("SHhIAmAS3CR3tDONTT3LLANY1;-)").when(tempCredentials).getSecretAccessKey();
    doReturn("SEzz10nT0kK3n!!").when(tempCredentials).getSessionToken();
    doReturn(tempCredentials)
        .when(apiSvc)
        .assumeRole(
            any(AWSCredentialsProvider.class), any(), notNull(), any(), any(), any(), any());
    doReturn(tempCredentials)
        .when(apiSvc)
        .assumeRole(
            any(ObjectId.class), any(), any(), any(), notNull(), any(), any(), any(), any());

    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(apiSvc)
        .assumeRole(any(AWSCredentialsProvider.class), any(), isNull(), any(), any(), any(), any());
    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(apiSvc)
        .assumeRole(any(ObjectId.class), any(), any(), isNull(), any(), any(), any(), any(), any());

    doReturn(List.of(mock(S3ObjectSummary.class)))
        .when(apiSvc)
        .listObjects(any(), any(), any(), any(), anyBoolean(), any());
    doReturn(mock(ObjectMetadata.class))
        .when(apiSvc)
        .getObjectMetadata((AWSCredentials) any(), any(), any(), anyBoolean(), any());
    doReturn(Optional.of(AWSRegionName.US_EAST_1))
        .when(apiSvc)
        .getS3BucketRegion(any(), anyString(), anyBoolean(), any());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePublicSvc.class), "_awsApiSvc", apiSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_awsApiSvc", apiSvc);

    return apiSvc;
  }

  private JSONObject getUsageLimitViewJSON(final long pLimitInBytes, final String pOverrunPolicy) {
    return new JSONObject()
        .put(DataFederationUsageLimitView.FieldDefs.LIMIT_IN_BYTES, pLimitInBytes)
        .put(DataFederationUsageLimitView.FieldDefs.OVERRUN_POLICY, pOverrunPolicy);
  }

  private JSONObject getUsageLimitViewJSON(final String pOverrunPolicy) {
    return new JSONObject().put(FieldDefs.OVERRUN_POLICY, pOverrunPolicy);
  }

  private void assertUsageLimitResponse(
      final JSONObject pResponse,
      final ObjectId pGroupId,
      final String pTenantName,
      final LimitSpan pLimitSpan,
      final OverrunPolicy pOverrunPolicy,
      final Long pLimitValue) {
    assertEquals(pGroupId.toHexString(), pResponse.getString(FieldDefs.GROUP_ID));
    if (pTenantName == null) {
      assertFalse(pResponse.has(FieldDefs.TENANT_NAME));
    } else {
      assertEquals(pTenantName, pResponse.getString(FieldDefs.TENANT_NAME));
    }
    assertEquals(pLimitSpan.name(), pResponse.getString(FieldDefs.LIMIT_SPAN));
    if (pLimitSpan != LimitSpan.QUERY) {
      assertEquals(pOverrunPolicy.name(), pResponse.getString(FieldDefs.OVERRUN_POLICY));
    } else {
      assertFalse(pResponse.has(FieldDefs.OVERRUN_POLICY));
    }
    assertEquals(pLimitValue.longValue(), pResponse.getLong(FieldDefs.LIMIT_IN_BYTES));
  }

  private void assertAtlasSQLTenantResponse(final JSONObject pResponse) {
    assertEquals("ATLAS_SQL", pResponse.getString("dataLakeType"));
    final String tenantName = String.format("atlas-sql-%s", _clusterUniqueId);
    assertEquals(tenantName, pResponse.getString("name"));
    final JSONObject resStorage = pResponse.getJSONObject(StorageConfigFieldDefs.STORAGE);
    final JSONObject resConfig =
        resStorage.getJSONObject(NDSDataLakeStorageV1View.FieldDefs.CONFIG);
    final JSONObject resStore =
        resConfig.getJSONArray(DataLakeTestUtils.StorageConfigFieldDefs.STORES).getJSONObject(0);
    assertEquals("atlas-sql-store", resStore.get(Store.NAME));
    assertEquals("atlas", resStore.get(Store.PROVIDER));
    assertEquals(_group.getId().toHexString(), resStore.get(Store.PROJECT_ID));
    assertEquals(_clusterName, resStore.get(Store.CLUSTER_NAME));
    assertEquals(
        "secondaryPreferred",
        ((JSONObject) resStore.get(Store.READ_PREFERENCE)).getString(Store.READ_PREFERENCE_MODE));
    final JSONObject resDatabase =
        resConfig.getJSONArray(StorageConfigFieldDefs.DATABASES).getJSONObject(0);
    assertEquals("*", resDatabase.getString(Database.NAME));
    final JSONObject resCollection =
        resDatabase.getJSONArray(Database.COLLECTIONS).getJSONObject(0);
    assertEquals("*", resCollection.getString(Collection.NAME));
    final JSONObject resDataSource =
        resCollection.getJSONArray(Collection.DATA_SOURCES).getJSONObject(0);
    assertEquals("atlas-sql-store", resDataSource.getString(DataSource.STORE_NAME));
  }

  private OnlineArchive getOnlineArchive() {
    final String dbName = "mydb";
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("field1", 0),
            new PartitionField("field2", 1),
            new PartitionField("dateField", 2));

    return new OnlineArchive.Builder()
        .setArchiveId(ObjectId.get())
        .setClusterId(_group.getId(), _clusterName)
        .setDbName(dbName)
        .setCollName("mycoll")
        .setPartitionFields(partitionFields)
        .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
        .setState(OnlineArchive.State.ACTIVE)
        .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
        .build();
  }
}
