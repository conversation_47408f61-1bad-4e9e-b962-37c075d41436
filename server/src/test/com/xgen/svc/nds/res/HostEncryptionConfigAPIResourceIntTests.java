package com.xgen.svc.nds.res;

import static com.xgen.cloud.deployment._public.model.EncryptionProviders.AWS_KMS_FIELD;
import static com.xgen.cloud.deployment._public.model.EncryptionProviders.AZURE_KEY_VAULT_FIELD;
import static com.xgen.cloud.deployment._public.model.EncryptionProviders.GOOGLE_CLOUD_KMS_FIELD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.gcp._public.model.NDSGoogleCloudKMS;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.search.decoupled.cloudprovider._private.dao.PartitionGroupDao;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchPhysicalModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.SearchConfigModelTestFactory;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class HostEncryptionConfigAPIResourceIntTests extends JUnit5BaseResourceTest {
  private Group _group;
  private AgentApiKey _agentApiKey;
  private AppUser _user;

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;
  @Inject private PartitionGroupDao _partitionGroupDao;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createUser(_group);
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), _user.getId());
  }

  @Test
  public void testAWS() throws Exception {
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(_group.getId());
    final ObjectId awsRoleId = new ObjectId();
    final AWSAccount awsAccount = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    final NDSCloudProviderAccessAWSIAMRole ndsCloudProviderAccessAWSIAMRole =
        new NDSCloudProviderAccessAWSIAMRole(
            awsRoleId,
            "arn:aws:iam::************:role/data-lake-role",
            List.of(),
            awsAccount.getId(),
            awsAccount.getRootARN(),
            UUID.randomUUID().toString(),
            new Date(),
            new Date());
    _ndsCloudProviderAccessSvc.addAwsIamRoleToCloudProviderAccess(
        ndsGroup, ndsCloudProviderAccessAWSIAMRole, AuditInfoHelpers.fromSystem());
    final NDSEncryptionAtRest enabledAWSEncryption =
        new NDSEncryptionAtRest()
            .copy()
            .awsKms(
                new NDSAWSKMS.Builder()
                    .setEnabled(true)
                    .setRoleId(awsRoleId)
                    .setCustomerMasterKeyID("cmki")
                    .setRegion(AWSNDSDefaults.REGION_NAME)
                    .build())
            .build();
    _ndsGroupSvc.saveEncryptionAtRest(
        _group.getId(), enabledAWSEncryption, AuditInfoHelpers.fromSystem());
    final PartitionGroup partitionGroup =
        SearchPhysicalModelTestFactory.getPartitionGroupWithInstances(
            SearchConfigModelTestFactory.getDeploymentDescriptionAWS(2),
            2,
            SearchInstance.EncryptionAtRest.of(ClusterDescription.EncryptionAtRestProvider.AWS));
    _partitionGroupDao.create(partitionGroup);
    final String hostname =
        partitionGroup
            .getInstances()
            .get(0)
            .getInstanceHardware()
            .getHostnameForAgents()
            .orElseThrow();
    final String path = "/conf/nds/hostEncryptionConf/" + _group.getId() + "/" + hostname;
    final JSONObject response = new JSONObject(doAgentApiCallGet(_group, _agentApiKey, path));
    assertEquals(hostname, response.getString("hostname"));
    assertEquals(
        awsRoleId.toString(),
        response
            .getJSONObject("encryptionProviders")
            .getJSONObject(AWS_KMS_FIELD)
            .getString("roleId"));
    assertFalse(response.getJSONObject("encryptionProviders").has(AZURE_KEY_VAULT_FIELD));
    assertFalse(response.getJSONObject("encryptionProviders").has(GOOGLE_CLOUD_KMS_FIELD));
    assertEquals("aws", response.getString("encryptionProviderType"));
  }

  @Test
  public void testAzure() throws Exception {
    _ndsGroupSvc.ensureGroup(_group.getId());
    final String azureClientId = "cid";
    final NDSEncryptionAtRest enabledAzureEncryption =
        new NDSEncryptionAtRest()
            .copy()
            .azureKeyVault(
                new NDSAzureKeyVault(
                    true,
                    azureClientId,
                    "tid",
                    "s",
                    SupportedAzureEnvironment.AZURE,
                    "sid",
                    "rgn",
                    "6ix-416-647",
                    "https://6ix-416-647.vault.azure.net/keys/6ix-416-647-789/t0r0nt0"))
            .build();
    _ndsGroupSvc.saveEncryptionAtRest(
        _group.getId(), enabledAzureEncryption, AuditInfoHelpers.fromSystem());
    final PartitionGroup partitionGroup =
        SearchPhysicalModelTestFactory.getPartitionGroupWithInstances(
            SearchConfigModelTestFactory.getDeploymentDescriptionAzure(2),
            2,
            SearchInstance.EncryptionAtRest.of(ClusterDescription.EncryptionAtRestProvider.AZURE));
    _partitionGroupDao.create(partitionGroup);
    final String hostname =
        partitionGroup
            .getInstances()
            .get(0)
            .getInstanceHardware()
            .getHostnameForAgents()
            .orElseThrow();
    final String path = "/conf/nds/hostEncryptionConf/" + _group.getId() + "/" + hostname;
    final JSONObject response = new JSONObject(doAgentApiCallGet(_group, _agentApiKey, path));
    assertEquals(hostname, response.getString("hostname"));
    assertEquals(
        azureClientId,
        response
            .getJSONObject("encryptionProviders")
            .getJSONObject(AZURE_KEY_VAULT_FIELD)
            .getString("clientId"));
    assertFalse(response.getJSONObject("encryptionProviders").has(AWS_KMS_FIELD));
    assertFalse(response.getJSONObject("encryptionProviders").has(GOOGLE_CLOUD_KMS_FIELD));
    assertEquals("azure", response.getString("encryptionProviderType"));
  }

  @Test
  public void testGCP() throws Exception {
    _ndsGroupSvc.ensureGroup(_group.getId());
    final String gcpServiceAccountKey = "{ \"secret\": \"stuff\" }";
    final NDSEncryptionAtRest enabledGCPEncryption =
        new NDSEncryptionAtRest()
            .copy()
            .googleCloudKMS(
                new NDSGoogleCloudKMS.Builder()
                    .setEnabled(true)
                    .setServiceAccountKey(gcpServiceAccountKey)
                    .setKeyVersionResourceID(
                        "projects/p/locations/us/keyRings/kr/cryptoKeys/k/cryptoKeyVersions/1")
                    .build())
            .build();
    _ndsGroupSvc.saveEncryptionAtRest(
        _group.getId(), enabledGCPEncryption, AuditInfoHelpers.fromSystem());
    final PartitionGroup partitionGroup =
        SearchPhysicalModelTestFactory.getPartitionGroupWithInstances(
            SearchConfigModelTestFactory.getDeploymentDescriptionGCP(2),
            2,
            SearchInstance.EncryptionAtRest.of(ClusterDescription.EncryptionAtRestProvider.GCP));
    _partitionGroupDao.create(partitionGroup);
    final String hostname =
        partitionGroup
            .getInstances()
            .get(0)
            .getInstanceHardware()
            .getHostnameForAgents()
            .orElseThrow();
    final String path = "/conf/nds/hostEncryptionConf/" + _group.getId() + "/" + hostname;
    JSONObject response = new JSONObject(doAgentApiCallGet(_group, _agentApiKey, path));
    assertEquals(hostname, response.getString("hostname"));
    assertEquals(
        gcpServiceAccountKey,
        response
            .getJSONObject("encryptionProviders")
            .getJSONObject(GOOGLE_CLOUD_KMS_FIELD)
            .getString("serviceAccountKey"));
    assertFalse(response.getJSONObject("encryptionProviders").has(AWS_KMS_FIELD));
    assertFalse(response.getJSONObject("encryptionProviders").has(AZURE_KEY_VAULT_FIELD));
    assertEquals("gcp", response.getString("encryptionProviderType"));
  }
}
