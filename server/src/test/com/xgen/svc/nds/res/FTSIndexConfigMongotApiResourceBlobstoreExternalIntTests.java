package com.xgen.svc.nds.res;

import static com.xgen.cloud.search.decoupled.blobstore._public.model.BlobstoreType.AWS_S3;
import static com.xgen.cloud.search.decoupled.blobstore._public.model.BlobstoreType.AZURE_BLOB_STORAGE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.DeleteObjectsResult.DeletedObject;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.securitytoken.model.GetCallerIdentityRequest;
import com.azure.core.util.BinaryData;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobContainerClientBuilder;
import com.azure.storage.blob.models.BlobItem;
import com.azure.storage.blob.models.ListBlobsOptions;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.AWSAccountBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.search.decoupled.blobstore._private.dao.BlobstoreDao;
import com.xgen.cloud.search.decoupled.blobstore._public.model.Blobstore;
import com.xgen.cloud.search.decoupled.blobstore._public.model.Blobstore.Id;
import com.xgen.cloud.search.decoupled.blobstore._public.model.Blobstore.ProviderInfo;
import com.xgen.cloud.search.decoupled.blobstore._public.model.Blobstore.ProviderInfo.Aws;
import com.xgen.cloud.search.decoupled.blobstore._public.model.Blobstore.ProviderInfo.Azure;
import com.xgen.cloud.search.decoupled.blobstore._public.svc.BlobstoreAzureParamsSvc;
import com.xgen.cloud.search.decoupled.blobstore._public.svc.BlobstoreParamsUtils;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroupSyncSource;
import com.xgen.svc.nds.model.mongotview.BlobstoreAwsCredentialsView;
import com.xgen.svc.nds.model.mongotview.BlobstoreAzureCredentialsView;
import com.xgen.svc.nds.model.mongotview.BlobstoreParamsView;
import com.xgen.svc.nds.model.mongotview.FTSIndexConfigMongotView;
import com.xgen.svc.nds.model.mongotview.FTSIndexConfigRequestView;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

@ExtendWith(GuiceTestExtension.class)
public class FTSIndexConfigMongotApiResourceBlobstoreExternalIntTests
    extends BaseFTSIndexConfigMongotApiResourceIntTest {
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private BlobstoreDao _blobstoreDao;
  @Inject private AzureSubscriptionDao _azureSubscriptionDao;

  /** Common methods for tests to interact with blobstore. */
  interface BlobstoreTestHelper {
    // Saves an entry to the blobstore collection.
    void createBlobstoreDocument();

    // Gets the base prefix for each file uploaded/downloaded from blobstore.
    String getBasePrefix();

    // Uploads a blob.
    String uploadBlob(String fileName, String content);

    // Lists blobs that match a prefix.
    List<String> listBlobs(String basePrefix);

    // Gets a specific blob.
    String getBlob(String objectKey);

    // Deletes a specific blob.
    boolean deleteBlob(String objectKey);

    // Sets the blobstore client based on the return value of the conf call.
    void initializeBlobstoreClient(JSONObject confCallResponseData);
  }

  class AwsS3TestHelper implements BlobstoreTestHelper {
    private static final AWSRegionName DEFAULT_REGION = AWSRegionName.US_EAST_1;
    // Should be consistent with the default region.
    private static final String DEFAULT_SNAPSHOT_BUCKET = "mongot-snapshot-testing-us-east-1";

    private static final String AWS_ACCESS_KEY = "local.aws.accessKey";
    private static final String AWS_SECRET_KEY = "local.aws.secretKey";

    private final AWSAccountDao awsAccountDao;
    private final AWSApiSvc awsApiSvc;
    private final AppSettings appSettings;
    private final BlobstoreDao blobstoreDao;
    private final Group group;
    private AmazonS3 s3Client;
    private Instant createdTime;

    public AwsS3TestHelper(
        AWSAccountDao awsAccountDao,
        AWSApiSvc awsApiSvc,
        AppSettings appSettings,
        BlobstoreDao blobstoreDao,
        Group group) {
      this.awsAccountDao = awsAccountDao;
      this.awsApiSvc = awsApiSvc;
      this.blobstoreDao = blobstoreDao;
      this.appSettings = appSettings;
      this.group = group;
      this.createdTime = Instant.now();
    }

    @Override
    public void createBlobstoreDocument() {
      var awsAccount =
          new AWSAccountBuilder()
              .setAccessKey(this.appSettings.getStrProp(AWS_ACCESS_KEY))
              .setSecretKey(this.appSettings.getStrProp(AWS_SECRET_KEY))
              .setName("local-account")
              .build();
      this.awsAccountDao.save(awsAccount);
      var stsResp =
          this.awsApiSvc
              .getSTSClient(awsAccount.getId(), DEFAULT_REGION)
              .getCallerIdentity(new GetCallerIdentityRequest());

      var blobstore =
          new Blobstore(
              Id.FromRegionNameAndBlobstoreType(DEFAULT_REGION, AWS_S3),
              true,
              ProviderInfo.create(
                  new Aws(
                      awsAccount.getId(),
                      awsAccount.getName(),
                      stsResp.getAccount(),
                      DEFAULT_SNAPSHOT_BUCKET)));
      this.blobstoreDao.create(blobstore);
    }

    @Override
    public String getBasePrefix() {
      var syncSource =
          new PartitionGroupSyncSource(
              _cluster.getUniqueId(), Date.from(this.createdTime), DEFAULT_REGION, "rsId");
      return BlobstoreParamsUtils.getBasePrefix(this.group.getId(), syncSource);
    }

    @Override
    public void initializeBlobstoreClient(JSONObject confCallResponseData) {
      var blobstoreParams =
          confCallResponseData
              .getJSONObject(FTSIndexConfigMongotView.FieldDefs.BLOBSTORE_PARAMS)
              .getJSONObject(BlobstoreParamsView.FieldDefs.CREDENTIALS);

      s3Client =
          AmazonS3ClientBuilder.standard()
              .withRegion(DEFAULT_REGION.getValue())
              .withCredentials(
                  new AWSStaticCredentialsProvider(
                      new BasicSessionCredentials(
                          blobstoreParams.getString(
                              BlobstoreAwsCredentialsView.FieldDefs.ACCESS_KEY),
                          blobstoreParams.getString(
                              BlobstoreAwsCredentialsView.FieldDefs.SECRET_KEY),
                          blobstoreParams.getString(
                              BlobstoreAwsCredentialsView.FieldDefs.SESSION_TOKEN))))
              .build();
    }

    @Override
    public String uploadBlob(String fileName, String content) {
      var bucketPrefix = getBasePrefix();
      var objectKey =
          String.join(
              "/",
              bucketPrefix,
              // Add a random ObjectId to the filename to avoid test collisions.
              new ObjectId().toHexString(),
              fileName);
      getS3Client().putObject(DEFAULT_SNAPSHOT_BUCKET, objectKey, content);
      return objectKey;
    }

    @Override
    public List<String> listBlobs(String basePrefix) {
      var listReq =
          new ListObjectsV2Request()
              .withBucketName(DEFAULT_SNAPSHOT_BUCKET)
              .withPrefix(basePrefix + '/');
      List<String> keys =
          getS3Client().listObjectsV2(listReq).getObjectSummaries().stream()
              .map(S3ObjectSummary::getKey)
              .collect(Collectors.toList());
      return keys;
    }

    @Override
    public String getBlob(String objectKey) {
      return getS3Client().getObjectAsString(DEFAULT_SNAPSHOT_BUCKET, objectKey);
    }

    @Override
    public boolean deleteBlob(String objectKey) {
      var delReq = new DeleteObjectsRequest(DEFAULT_SNAPSHOT_BUCKET).withKeys(objectKey);
      return getS3Client().deleteObjects(delReq).getDeletedObjects().stream()
          .map(DeletedObject::getKey)
          .anyMatch(objectKey::equals);
    }

    private AmazonS3 getS3Client() {
      if (this.s3Client == null) {
        throw new IllegalStateException("S3 client has not been initialized.");
      }
      return this.s3Client;
    }
  }

  public class AzureBlobStorageHelper implements BlobstoreTestHelper {
    private static final AzureRegionName DEFAULT_REGION = AzureRegionName.US_EAST;
    // This should match the default region.
    private static final String DEFAULT_STORAGE_ACCOUNT = "lclasieastus";
    private static final String CLIENT_ID_PROP = "test.azure.clientId";
    private static final String TENANT_ID_PROP = "test.azure.tenantId";
    private static final String SUBSCRIPTION_ID_PROP = "test.azure.subscriptionId";
    private static final String SECRET_PROP = "test.azure.secret";
    private static final String MULTI_TENANT_APP_ID_PROP = "test.azure.multiTenantAppId";
    private static final String MULTI_TENANT_APP_SECRET_PROP = "test.azure.multiTenantAppSecret";

    private final AzureSubscriptionDao azureSubscriptionDao;
    private final BlobstoreDao blobstoreDao;
    private final AppSettings appSettings;
    private final Group group;
    private BlobContainerClient blobContainerClient;
    private final Instant createdTime;

    public AzureBlobStorageHelper(
        AzureSubscriptionDao azureSubscriptionDao,
        BlobstoreDao blobstoreDao,
        AppSettings appSettings,
        Group group) {
      this.azureSubscriptionDao = azureSubscriptionDao;
      this.blobstoreDao = blobstoreDao;
      this.group = group;
      this.appSettings = appSettings;
      // The base prefix depends on a PartitionGroupSyncSource which requires a creation timestamp.
      // We save this for a consistent creation timestamp.
      this.createdTime = Instant.now();
    }

    @Override
    public void createBlobstoreDocument() {
      final AzureSubscription subscription =
          new AzureSubscription(
              new ObjectId(),
              "cloud-provider-access-local",
              new Date(),
              new Date(),
              this.appSettings.getStrProp(CLIENT_ID_PROP),
              this.appSettings.getStrProp(TENANT_ID_PROP),
              this.appSettings.getStrProp(SUBSCRIPTION_ID_PROP),
              this.appSettings.getStrProp(SECRET_PROP),
              null,
              this.appSettings.getStrProp(MULTI_TENANT_APP_ID_PROP),
              this.appSettings.getStrProp(MULTI_TENANT_APP_SECRET_PROP),
              SupportedAzureEnvironment.AZURE,
              Collections.emptyList(),
              Collections.emptyList(),
              false,
              false,
              false,
              false,
              // We expect this to be the CPA subscription.
              true);
      this.azureSubscriptionDao.insertReplicaSafe(subscription.toDBObject());

      var blobstore =
          new Blobstore(
              Id.FromRegionNameAndBlobstoreType(DEFAULT_REGION, AZURE_BLOB_STORAGE),
              true,
              ProviderInfo.create(
                  new Azure(
                      subscription.getId(),
                      DEFAULT_STORAGE_ACCOUNT,
                      subscription.getTenantId(),
                      subscription.getMultiTenantAppId().orElseThrow())));
      this.blobstoreDao.create(blobstore);
    }

    @Override
    public String getBasePrefix() {
      var syncSource =
          new PartitionGroupSyncSource(
              _cluster.getUniqueId(), Date.from(this.createdTime), DEFAULT_REGION, "rsId");
      return BlobstoreParamsUtils.getBasePrefix(this.group.getId(), syncSource);
    }

    @Override
    public String uploadBlob(String fileName, String content) {
      BlobContainerClient blobContainerClient = getBlobContainerClient();
      var blobName = String.join("/", getBasePrefix(), new ObjectId().toHexString(), fileName);
      BlobClient blobClient = blobContainerClient.getBlobClient(blobName);

      blobClient.upload(BinaryData.fromString(content), true);
      return blobName;
    }

    @Override
    public String getBlob(String blobName) {
      BlobContainerClient blobContainerClient = getBlobContainerClient();
      BlobClient blobClient = blobContainerClient.getBlobClient(blobName);

      try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
        blobClient.getBlockBlobClient().downloadStream(stream);
        return stream.toString(StandardCharsets.UTF_8);
      } catch (IOException e) {
        throw new RuntimeException("Failed to download blob: " + blobName, e);
      }
    }

    @Override
    public List<String> listBlobs(String basePrefix) {
      BlobContainerClient blobContainerClient = getBlobContainerClient();
      ListBlobsOptions options = new ListBlobsOptions().setPrefix(basePrefix);
      return blobContainerClient.listBlobs(options, null).stream()
          .map(BlobItem::getName)
          .collect(Collectors.toList());
    }

    @Override
    public void initializeBlobstoreClient(JSONObject confCallResponseData) {
      JSONObject blobstoreParams =
          confCallResponseData
              .getJSONObject(FTSIndexConfigMongotView.FieldDefs.BLOBSTORE_PARAMS)
              .getJSONObject(BlobstoreParamsView.FieldDefs.CREDENTIALS);

      this.blobContainerClient =
          new BlobContainerClientBuilder()
              .endpoint(BlobstoreAzureParamsSvc.getStorageAccountEndpoint(DEFAULT_STORAGE_ACCOUNT))
              .containerName(BlobstoreAzureParamsSvc.getAzureContainerName(this.group.getId()))
              .sasToken(
                  blobstoreParams.getString(BlobstoreAzureCredentialsView.FieldDefs.SAS_TOKEN))
              .buildClient();
    }

    private BlobContainerClient getBlobContainerClient() {
      if (this.blobContainerClient == null) {
        throw new IllegalStateException("BlobContainer client not initialized");
      }
      return this.blobContainerClient;
    }

    @Override
    public boolean deleteBlob(String blobName) {
      BlobContainerClient blobContainerClient = getBlobContainerClient();
      BlobClient blobClient = blobContainerClient.getBlobClient(blobName);
      return blobClient.deleteIfExists();
    }
  }

  // Sets up the cluster, decoupled search nodes, and test helper for a specific provider.
  private BlobstoreTestHelper setupBlobstoreTest(CloudProvider provider) {
    switch (provider) {
      case AWS:
        setupAwsSingleRegionCluster();
        setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));
        return new AwsS3TestHelper(
            this._awsAccountDao,
            this._awsApiSvc,
            this._appSettings,
            this._blobstoreDao,
            this._group);
      case AZURE:
        setupAzureSingleRegionCluster();
        setUpAzureDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));
        return new AzureBlobStorageHelper(
            this._azureSubscriptionDao, this._blobstoreDao, this._appSettings, _group);
      default:
        throw new IllegalArgumentException("Unsupported BlobstoreProvider: " + provider);
    }
  }

  private static Stream<CloudProvider> supportedBlobstoreProviders() {
    return Stream.of(CloudProvider.AWS, CloudProvider.AZURE);
  }

  @ParameterizedTest
  @MethodSource("supportedBlobstoreProviders")
  public void testBlobstoreCredentials(CloudProvider provider) throws Exception {
    var blobstoreTestHelper = setupBlobstoreTest(provider);
    blobstoreTestHelper.createBlobstoreDocument();

    // Get the mongot hostname and request blobstore credentials.
    var path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    var hostname = getDecoupledMongotHostnames(_mongotPartitionGroups.get(0)).get(0);
    var mongotRequest =
        Map.of(
            FTSIndexConfigRequestView.FieldDefs.HOSTNAME,
            hostname,
            FTSIndexConfigRequestView.FieldDefs.BLOBSTORE_CREDENTIALS_REQUESTED,
            true);

    // Note: there are more exhaustive tests of these credentials in
    // BlobstoreParamsSvc{CloudProvider}ExternalIntTests. This test is meant to verify that we
    // can construct and use credentials from the raw JSON.
    var response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    // Initialize the client with credentials from the conf call.
    blobstoreTestHelper.initializeBlobstoreClient(response);

    // Ensure the blob is uploaded.
    var fileName = "resource-test.txt";
    var fileContent = "test";
    var blobName = blobstoreTestHelper.uploadBlob(fileName, fileContent);

    // Test list, get, and delete.
    var basePrefix = blobstoreTestHelper.getBasePrefix();
    assertTrue(
        blobstoreTestHelper.listBlobs(basePrefix).stream().anyMatch(key -> key.endsWith(fileName)));
    assertEquals(fileContent, blobstoreTestHelper.getBlob(blobName));
    assertTrue(blobstoreTestHelper.deleteBlob(blobName));
  }
}
