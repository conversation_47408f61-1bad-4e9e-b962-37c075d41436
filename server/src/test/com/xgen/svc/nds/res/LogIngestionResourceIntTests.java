package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory.mockNonConfigShardFunctions;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.logingestion._private.dao.LogIngestionLogsDao;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry.FieldDefs;
import com.xgen.cloud.nds.logingestion._public.model.LogIngestionLogEntry.SourceMetadata;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.LogIngestionRule;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.svc.LogIngestionSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class LogIngestionResourceIntTests extends JUnit5BaseResourceTest {

  private static final String BASE_URL = "/admin/nds/logIngestion";

  private static final ObjectId RULE_ID = ObjectId.get();

  @Inject LogIngestionSvc _logIngestionSvc;
  @Inject LogIngestionLogsDao _logIngestionLogsDao;

  private AppUser _user;
  private AppUser _requester;
  private AppUser _viewer;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _user = MmsFactory.createGlobalAtlasAdminUser(MmsFactory.createGroupWithStandardPlan());
    _requester = MmsFactory.createUser("first.last");
    _viewer = MmsFactory.createGlobalAdminReadOnlyUser();
  }

  @Test
  public void testCreateRule() {
    final JSONObject ruleView = getLogIngestionRuleJSON("Rule1", _requester.getUsername());

    doAuthedJsonPost(_user, BASE_URL, ruleView, HttpStatus.SC_OK);
    assertTrue(_logIngestionSvc.getRuleByName("Rule1").isPresent());

    final JSONObject duplicateRuleResponse =
        doAuthedJsonPost(_user, BASE_URL, ruleView, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.RULE_ALREADY_EXISTS.name(), duplicateRuleResponse.getString("errorCode"));

    final JSONObject ruleViewBadRequester = getLogIngestionRuleJSON("Rule2", "bad.requester");
    final JSONObject badRequesterResponse =
        doAuthedJsonPost(_user, BASE_URL, ruleViewBadRequester, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.USERNAME_NOT_FOUND.name(), badRequesterResponse.getString("errorCode"));
  }

  @Test
  public void testGetRules() throws SvcException {
    final LogIngestionRule logIngestionRule1 =
        getLogIngestionRule("Rule1", _requester.getUsername());
    final LogIngestionRule logIngestionRule2 =
        getLogIngestionRule("Rule2", _requester.getUsername());

    final JSONArray arrayPreCreate = doAuthedJsonArrayGet(_viewer, BASE_URL, HttpStatus.SC_OK);
    assertEquals(0, arrayPreCreate.length());

    _logIngestionSvc.createRule(logIngestionRule1);
    _logIngestionSvc.createRule(logIngestionRule2);

    final JSONArray arrayPostCreate = doAuthedJsonArrayGet(_viewer, BASE_URL, HttpStatus.SC_OK);
    assertEquals(2, arrayPostCreate.length());
  }

  @Test
  public void testDeleteRule() throws SvcException {
    final LogIngestionRule logIngestionRule = getLogIngestionRule("Rule", _requester.getUsername());
    final String URL = String.format("%s/%s", BASE_URL, logIngestionRule.getId());

    _logIngestionSvc.createRule(logIngestionRule);

    doAuthedJsonDelete(_user, URL, HttpStatus.SC_OK);
    assertFalse(_logIngestionSvc.getRuleByName(logIngestionRule.getName()).isPresent());

    final JSONObject response = doAuthedJsonDelete(_user, URL, HttpStatus.SC_BAD_REQUEST);
    assertEquals(NDSErrorCode.RULE_DOES_NOT_EXIST.name(), response.getString("errorCode"));
  }

  @Test
  public void testUpdateRule() throws SvcException {
    final LogIngestionRule logIngestionRule = getLogIngestionRule("Rule", _requester.getUsername());
    final String URL = String.format("%s/%s", BASE_URL, logIngestionRule.getId());

    _logIngestionSvc.createRule(logIngestionRule);

    final JSONObject newRule =
        getLogIngestionRuleJSON("NewRule", _requester.getUsername(), logIngestionRule.getId());

    doAuthedJsonPut(_user, URL, newRule, HttpStatus.SC_OK);
    assertTrue(_logIngestionSvc.getRuleByName("NewRule").isPresent());
    assertFalse(_logIngestionSvc.getRuleByName("Rule").isPresent());

    final ObjectId badID = ObjectId.get();
    final JSONObject badIdResponse =
        doAuthedJsonPut(
            _user, String.format("%s/%s", BASE_URL, badID), newRule, HttpStatus.SC_BAD_REQUEST);

    assertEquals(
        String.format(
            "Invalid rule ID - provided rule ID '%s' does not match the expected rule" + " ID '%s'",
            badID, logIngestionRule.getId().toString()),
        badIdResponse.getString("message"));
  }

  @Test
  public void testGetRuleStats() {
    final ObjectId ruleId = ObjectId.get();
    final ObjectId clusterUniqueId = ObjectId.get();

    final String URL = String.format("%s/%s/stats", BASE_URL, ruleId);
    final JSONObject statsNoEntries = doAuthedJsonGet(_user, URL, HttpStatus.SC_OK);

    assertEquals(4, statsNoEntries.keySet().size());
    assertTrue(statsNoEntries.has("instanceSizes"));
    assertTrue(statsNoEntries.has("mongoDBVersions"));
    assertTrue(statsNoEntries.has("regions"));
    assertTrue(statsNoEntries.has("logsOverTime"));

    assertEquals(0, statsNoEntries.getJSONObject("instanceSizes").keySet().size());
    assertEquals(0, statsNoEntries.getJSONObject("mongoDBVersions").keySet().size());
    assertEquals(0, statsNoEntries.getJSONObject("regions").keySet().size());
    assertEquals(0, statsNoEntries.getJSONObject("logsOverTime").keySet().size());
    assertFalse(statsNoEntries.has("elapsedTime"));

    createLogEntries(ruleId);

    final JSONObject stats = doAuthedJsonGet(_user, URL, HttpStatus.SC_OK);

    // Instance sizes
    final JSONObject instanceSizes = stats.getJSONObject("instanceSizes");
    assertEquals(3, instanceSizes.keySet().size());
    assertTrue(instanceSizes.has(CloudProvider.AWS.name()));
    assertTrue(instanceSizes.has(CloudProvider.GCP.name()));
    assertTrue(instanceSizes.has(CloudProvider.AZURE.name()));

    final JSONObject awsInstanceSizes = instanceSizes.getJSONObject(CloudProvider.AWS.name());
    assertEquals(2, awsInstanceSizes.keySet().size());
    assertEquals(2, awsInstanceSizes.get(AWSNDSInstanceSize.M10.name()));
    assertEquals(1, awsInstanceSizes.get(AWSNDSInstanceSize.M20.name()));

    final JSONObject gcpInstanceSizes = instanceSizes.getJSONObject(CloudProvider.GCP.name());
    assertEquals(1, gcpInstanceSizes.keySet().size());
    assertEquals(1, gcpInstanceSizes.get(GCPNDSInstanceSize.M30.name()));

    final JSONObject azureInstanceSizes = instanceSizes.getJSONObject(CloudProvider.AZURE.name());
    assertEquals(1, azureInstanceSizes.keySet().size());
    assertEquals(1, azureInstanceSizes.get(GCPNDSInstanceSize.M40.name()));

    // MongoDB versions
    final JSONObject mongoDBVersions = stats.getJSONObject("mongoDBVersions");
    assertEquals(3, mongoDBVersions.keySet().size());
    assertTrue(mongoDBVersions.has(CloudProvider.AWS.name()));
    assertTrue(mongoDBVersions.has(CloudProvider.GCP.name()));
    assertTrue(mongoDBVersions.has(CloudProvider.AZURE.name()));

    final JSONObject awsMongoDBVersions = mongoDBVersions.getJSONObject(CloudProvider.AWS.name());
    assertEquals(2, awsMongoDBVersions.keySet().size());
    assertEquals(2, awsMongoDBVersions.get("3.6.0"));
    assertEquals(1, awsMongoDBVersions.get("4.2.0"));

    final JSONObject gcpMongoDBVersions = mongoDBVersions.getJSONObject(CloudProvider.GCP.name());
    assertEquals(1, gcpMongoDBVersions.keySet().size());
    assertEquals(1, gcpMongoDBVersions.get("4.4.0"));

    final JSONObject azureMongoDBVersions =
        mongoDBVersions.getJSONObject(CloudProvider.AZURE.name());
    assertEquals(1, azureMongoDBVersions.keySet().size());
    assertEquals(1, azureMongoDBVersions.get("4.4.0"));

    // Regions
    final JSONObject regions = stats.getJSONObject("regions");
    assertEquals(3, regions.keySet().size());
    assertTrue(regions.has(CloudProvider.AWS.name()));
    assertTrue(regions.has(CloudProvider.GCP.name()));
    assertTrue(regions.has(CloudProvider.AZURE.name()));

    final JSONObject awsRegions = regions.getJSONObject(CloudProvider.AWS.name());
    assertEquals(2, awsRegions.keySet().size());
    assertEquals(2, awsRegions.get(AWSRegionName.US_EAST_1.name()));
    assertEquals(1, awsRegions.get(AWSRegionName.US_EAST_2.name()));

    final JSONObject gcpRegions = regions.getJSONObject(CloudProvider.GCP.name());
    assertEquals(1, gcpRegions.keySet().size());
    assertEquals(1, gcpRegions.get(GCPRegionName.US_EAST_4.name()));

    final JSONObject azureRegions = regions.getJSONObject(CloudProvider.AZURE.name());
    assertEquals(1, azureRegions.keySet().size());
    assertEquals(1, azureRegions.get(AzureRegionName.US_EAST.name()));

    // Logs over time
    final JSONObject logsOverTime = stats.getJSONObject("logsOverTime");
    assertEquals(1, logsOverTime.keySet().size());
    assertEquals(6, logsOverTime.get(logsOverTime.keySet().iterator().next()));

    // Elapsed time
    assertTrue(stats.has("elapsedTime"));
    final JSONObject elapsedTime = stats.getJSONObject("elapsedTime");
    assertNotNull(elapsedTime.get("average"));
    assertNotNull(elapsedTime.get("max"));
  }

  private LogIngestionRule getLogIngestionRule(final String pName, final String pRequester) {
    return new LogIngestionRule.Builder()
        .setId(ObjectId.get())
        .setName(pName)
        .setRequester(pRequester)
        .setLogType(LogIngestionRule.LogType.MONGOD)
        .setSourceClusters(
            new LogIngestionRule.SourceClusters.Builder()
                .setClusterTiers(List.of("M10"))
                .setMongoDBVersions(List.of("4.4.0"))
                .build())
        .setFormat(LogIngestionRule.Format.REGEX)
        .setQuery("^(regex)$")
        .setProjection("$1")
        .setResultsPerHour(10)
        .build();
  }

  private JSONObject getLogIngestionRuleJSON(final String pName, final String pRequester) {
    return getLogIngestionRuleJSON(pName, pRequester, null);
  }

  private JSONObject getLogIngestionRuleJSON(
      final String pName, final String pRequester, final ObjectId id) {
    return new JSONObject()
        .put("_id", id)
        .put("name", pName)
        .put("requester", pRequester)
        .put("logType", LogIngestionRule.LogType.MONGOD.getValue())
        .put(
            "sourceClusters",
            new JSONObject()
                .put("clusterTiers", new JSONArray().put("M10"))
                .put("mongoDBVersions", new JSONArray().put("4.4.0")))
        .put("format", LogIngestionRule.Format.REGEX.name())
        .put("query", "^(regex)$")
        .put("projection", "$1")
        .put("resultsPerHour", 10);
  }

  private void createLogEntries(final ObjectId pRuleId) {
    final ObjectId clusterUniqueId = ObjectId.get();

    // Two entries from an AWS M10 cluster in US_EAST_1 running 3.6.0
    // One entry from a separate AWS M10 cluster in US_EAST_1 running 3.6.0
    // One entry from an AWS M20 cluster in US_EAST_2 running 4.2.0
    // One entry from a GCP M30 cluster in US_EAST_4 running 4.4.0
    // One entry from an Azure M40 cluster in US_EAST running 4.4.0
    final ObjectId groupId = new ObjectId();
    final ReplicaSetHardware dedicatedShardRsh = mock(ReplicaSetHardware.class);
    mockNonConfigShardFunctions(dedicatedShardRsh);
    _logIngestionLogsDao.insertManyMajority(
        List.of(
            new LogIngestionLogEntry(
                ObjectId.get(),
                pRuleId,
                new BasicDBObject(),
                new LogIngestionLogEntry.SourceMetadata(
                    clusterUniqueId,
                    dedicatedShardRsh,
                    List.of(LogIngestionLogEntry.SourceMetadata.Process.MONGOD),
                    "host1",
                    CloudProvider.AWS,
                    AWSNDSInstanceSize.M10,
                    AWSRegionName.US_EAST_1,
                    new VersionUtils.Version("3.6.0"),
                    groupId,
                    "test-cluster",
                    ClusterType.REPLICASET),
                new Date(),
                75800L),
            new LogIngestionLogEntry(
                ObjectId.get(),
                pRuleId,
                new BasicDBObject(),
                new LogIngestionLogEntry.SourceMetadata(
                    clusterUniqueId,
                    dedicatedShardRsh,
                    List.of(LogIngestionLogEntry.SourceMetadata.Process.MONGOD),
                    "host2",
                    CloudProvider.AWS,
                    AWSNDSInstanceSize.M10,
                    AWSRegionName.US_EAST_1,
                    new VersionUtils.Version("3.6.0"),
                    groupId,
                    "test-cluster",
                    ClusterType.REPLICASET),
                new Date(),
                75800L),
            new LogIngestionLogEntry(
                ObjectId.get(),
                pRuleId,
                new BasicDBObject(),
                new LogIngestionLogEntry.SourceMetadata(
                    ObjectId.get(),
                    dedicatedShardRsh,
                    List.of(LogIngestionLogEntry.SourceMetadata.Process.MONGOD),
                    "host3",
                    CloudProvider.AWS,
                    AWSNDSInstanceSize.M10,
                    AWSRegionName.US_EAST_1,
                    new VersionUtils.Version("3.6.0"),
                    groupId,
                    "test-cluster",
                    ClusterType.REPLICASET),
                new Date(),
                75800L),
            new LogIngestionLogEntry(
                ObjectId.get(),
                pRuleId,
                new BasicDBObject(),
                new LogIngestionLogEntry.SourceMetadata(
                    ObjectId.get(),
                    dedicatedShardRsh,
                    List.of(LogIngestionLogEntry.SourceMetadata.Process.MONGOD),
                    "hostA",
                    CloudProvider.AWS,
                    AWSNDSInstanceSize.M20,
                    AWSRegionName.US_EAST_2,
                    new VersionUtils.Version("4.2.0"),
                    groupId,
                    "test-cluster",
                    ClusterType.REPLICASET),
                new Date(),
                75800L),
            new LogIngestionLogEntry(
                ObjectId.get(),
                pRuleId,
                new BasicDBObject(),
                new LogIngestionLogEntry.SourceMetadata(
                    ObjectId.get(),
                    dedicatedShardRsh,
                    List.of(LogIngestionLogEntry.SourceMetadata.Process.MONGOD),
                    "hostB",
                    CloudProvider.GCP,
                    GCPNDSInstanceSize.M30,
                    GCPRegionName.US_EAST_4,
                    new VersionUtils.Version("4.4.0"),
                    groupId,
                    "test-cluster",
                    ClusterType.REPLICASET),
                new Date(),
                75800L),
            new LogIngestionLogEntry(
                ObjectId.get(),
                pRuleId,
                new BasicDBObject(),
                new LogIngestionLogEntry.SourceMetadata(
                    ObjectId.get(),
                    dedicatedShardRsh,
                    List.of(LogIngestionLogEntry.SourceMetadata.Process.MONGOD),
                    "hostC",
                    CloudProvider.AZURE,
                    AzureNDSInstanceSize.M40,
                    AzureRegionName.US_EAST,
                    new VersionUtils.Version("4.4.0"),
                    groupId,
                    "test-cluster",
                    ClusterType.REPLICASET),
                new Date(),
                75800L)));
  }

  @Test
  public void testEnableAndDisable() throws SvcException {
    final JSONObject ruleView = getLogIngestionRuleJSON("RuleToToggle", _requester.getUsername());

    doAuthedJsonPost(_user, BASE_URL, ruleView, HttpStatus.SC_OK);
    final var rule = _logIngestionSvc.getRuleByName("RuleToToggle").get();
    assertNull(rule.getDisabled());
    _logIngestionSvc.disableRule(rule.getId());
    assertTrue(_logIngestionSvc.getRuleByName("RuleToToggle").get().getDisabled());
    _logIngestionSvc.enableRule(rule.getId());
    assertNull(_logIngestionSvc.getRuleByName("RuleToToggle").get().getDisabled());
  }

  @Test
  void testBackwardsCompatibility_legacyDocumentFromDatabase_retrievedSuccessfully() {
    // Create a legacy LogIngestionLogEntry document that lacks the clusterName field
    // and persist it directly to the database, then retrieve it through the DAO layer
    // to validate end-to-end backwards compatibility

    final ObjectId entryId = ObjectId.get();
    final ObjectId ruleId = ObjectId.get();
    final ObjectId clusterUniqueId = ObjectId.get();
    final ObjectId groupId = ObjectId.get();
    final ReplicaSetHardware dedicatedShardRsh = mock(ReplicaSetHardware.class);
    mockNonConfigShardFunctions(dedicatedShardRsh);

    // Create a legacy SourceMetadata BasicDBObject WITHOUT the clusterName field
    // This simulates a document that was created before the clusterName field was added
    final BasicDBObject legacySourceMetadataDBObject =
        new BasicDBObject()
            .append(SourceMetadata.FieldDefs.CLUSTER_UNIQUE_ID, clusterUniqueId)
            .append(SourceMetadata.FieldDefs.REPLICA_SET_TYPE, "SHARD")
            .append(
                SourceMetadata.FieldDefs.NODE_PROCESSES,
                List.of("MONGOD").stream()
                    .collect(com.xgen.cloud.common.mongo._public.mongo.DbUtils.toBasicDBList()))
            .append(SourceMetadata.FieldDefs.NODE_HOSTNAME, "legacy-host")
            .append(SourceMetadata.FieldDefs.CLOUD_PROVIDER, "AWS")
            .append(SourceMetadata.FieldDefs.INSTANCE_SIZE, "M10")
            .append(SourceMetadata.FieldDefs.REGION, "US_EAST_1")
            .append(SourceMetadata.FieldDefs.MONGODB_VERSION, "4.4.0")
            .append(SourceMetadata.FieldDefs.GROUP_ID, groupId);
    // Intentionally omitting CLUSTER_NAME field to simulate legacy document

    // Create a complete legacy LogIngestionLogEntry BasicDBObject
    final BasicDBObject legacyLogEntryDBObject =
        new BasicDBObject()
            .append(FieldDefs.ID, entryId)
            .append(FieldDefs.RULE_ID, ruleId)
            .append(FieldDefs.PROJECTION, new BasicDBObject("field", "value"))
            .append(FieldDefs.SOURCE_METADATA, legacySourceMetadataDBObject)
            .append(FieldDefs.LOG_TIMESTAMP, new Date())
            .append(FieldDefs.ELAPSED_TIME, 12345L);

    // Insert the legacy document directly into the database using the DAO
    // This tests the actual database serialization/deserialization path
    final LogIngestionLogEntry legacyEntry = new LogIngestionLogEntry(legacyLogEntryDBObject);
    _logIngestionLogsDao.insertMajority(legacyEntry);

    // Retrieve the document from the database through the DAO layer
    // This validates that the entire stack (database → DAO → model) handles legacy documents
    final var retrievedEntry = _logIngestionLogsDao.find(entryId);
    assertTrue(retrievedEntry.isPresent());

    final LogIngestionLogEntry entry = retrievedEntry.get();

    // Verify that the entry was retrieved correctly
    assertNotNull(entry);
    assertEquals(entryId, entry.getId());
    assertEquals(ruleId, entry.getRuleId());
    assertNotNull(entry.getSourceMetadata());

    // Verify that clusterName is null for legacy documents (backwards compatibility)
    final SourceMetadata sourceMetadata = entry.getSourceMetadata();
    assertNull(sourceMetadata.getClusterName());

    // Verify other fields are correctly retrieved from the database
    assertEquals(clusterUniqueId, sourceMetadata.getClusterUniqueId());
    assertEquals("legacy-host", sourceMetadata.getNodeHostname());
    assertEquals(CloudProvider.AWS, sourceMetadata.getCloudProvider());
    assertEquals(groupId, sourceMetadata.getGroupId());

    // Test that the retrieved entry can be serialized back to database format
    final BasicDBObject serializedDBObject = entry.toDBObject();
    assertNotNull(serializedDBObject);
    assertEquals(entryId, serializedDBObject.getObjectId(FieldDefs.ID));
    assertEquals(ruleId, serializedDBObject.getObjectId(FieldDefs.RULE_ID));

    // Verify that the serialized SourceMetadata includes clusterName field (even if null)
    // This ensures consistency in the database schema going forward
    final BasicDBObject serializedSourceMetadata =
        (BasicDBObject) serializedDBObject.get(FieldDefs.SOURCE_METADATA);
    assertNotNull(serializedSourceMetadata);
    assertTrue(serializedSourceMetadata.containsField(SourceMetadata.FieldDefs.CLUSTER_NAME));
    assertNull(serializedSourceMetadata.get(SourceMetadata.FieldDefs.CLUSTER_NAME));
  }
}
