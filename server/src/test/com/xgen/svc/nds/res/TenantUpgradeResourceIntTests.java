package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED;
import static org.apache.http.HttpStatus.SC_ACCEPTED;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._private.dao.FlexTenantMigrationDao;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration.TenantStartingState;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.resourcepolicy._public.model.Policy;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.nds.resourcepolicy.util.AtlasResourcePolicyTestUtil;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionViewUtils;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class TenantUpgradeResourceIntTests
    extends BaseTenantUpgradeResourceTest<TenantUpgradeStatusDao> {
  private static final String FAILED_UPGRADE_CLUSTER_NAME = "Cluster1";

  @Inject private AppSettings _appSettings;
  @Inject private AtlasResourcePolicySvc _atlasResourcePolicySvc;
  @Inject private FlexTenantMigrationDao _flexTenantMigrationDao;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testCreateUpgrade() {
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jObject = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());

    // Overwrite the mongodb version. The UI does this in the function:
    // determinePostUpgradeClusterDescriptionForSharedTierCluster
    // This is needed to prevent downgrading the version if the shared tier is ahead of the default
    // dedicated tier
    jObject.put(FieldDefs.MONGODB_VERSION, NDSModelTestFactory.TEST_FREE_MONGODB_VERSION);
    jObject.put(
        FieldDefs.MONGODB_MAJOR_VERSION, NDSModelTestFactory.TEST_FREE_MONGODB_MAJOR_VERSION);

    final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
    doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_ACCEPTED);

    Optional<ClusterDescription> clusterDescription =
        getTenantClusterDescriptionUpgradeDao()
            .findByName(getNDSGroup().getGroupId(), CLUSTER_NAME);

    assertTrue(clusterDescription.isPresent());
    assertEquals(
        clusterDescription.get().getMongoDBMajorVersion(),
        NDSModelTestFactory.TEST_FREE_MONGODB_MAJOR_VERSION);

    final List<TenantUpgradeStatus> tenantUpgradeStatusList =
        getTenantUpgradeStatusDao().getTenantUpgradesForGroup(getGroup().getId());

    assertEquals(tenantUpgradeStatusList.size(), 1);
    assertEquals(tenantUpgradeStatusList.get(0).getClusterName(), CLUSTER_NAME);

    // test serverless instance as source
    jObject.put("name", SERVERLESS_INSTANCE_NAME);

    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.UNSUPPORTED.name(), serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testCreateUpgrade_FlexTenantMigration() {
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jObject = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());
    final ClusterDescription sharedCluster =
        getClusterDescriptionDao().findByName(getGroup().getId(), CLUSTER_NAME).orElseThrow();

    // Tenant Migration exists where migration is running -- returns error
    final FlexTenantMigration flexTenantMigration =
        new FlexTenantMigration(
            sharedCluster.getName(),
            sharedCluster.getGroupId(),
            sharedCluster.getUniqueId(),
            new FlexTenantMigration.MTM("mtmName", new ObjectId(), new ObjectId()),
            CloudProvider.FREE,
            FreeInstanceSize.M2,
            MigrationStatus.MIGRATION_RUNNING,
            Date.from(Instant.EPOCH),
            null,
            null,
            null,
            null,
            null,
            null,
            Collections.emptyList(),
            new TenantStartingState(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M2), null));
    _flexTenantMigrationDao.saveMigration(flexTenantMigration);
    {
      final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
      final JSONObject response = doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.name(),
          response.get(ApiError.ERROR_CODE_FIELD));
    }

    // a migration has rolled back for this tenant -- upgrade works
    {
      _flexTenantMigrationDao.updateMigration(
          flexTenantMigration.toBuilder().setStatus(MigrationStatus.ROLLBACK_SUCCESS).build());
      final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
      doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_ACCEPTED);

      final Optional<ClusterDescription> clusterDescription =
          getTenantClusterDescriptionUpgradeDao()
              .findByName(getNDSGroup().getGroupId(), CLUSTER_NAME);
      assertTrue(clusterDescription.isPresent());
    }
  }

  @Test
  public void testCreateUpgrade_missingMDBVersion() {
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jObject = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());

    // Remove the MongoDB versions to ensure that the endpoint works when these are missing
    jObject.remove(FieldDefs.MONGODB_VERSION);
    jObject.remove(FieldDefs.MONGODB_MAJOR_VERSION);

    final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
    doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_ACCEPTED);

    Optional<ClusterDescription> clusterDescription =
        getTenantClusterDescriptionUpgradeDao()
            .findByName(getNDSGroup().getGroupId(), CLUSTER_NAME);

    assertTrue(clusterDescription.isPresent());
    assertEquals(
        NDSModelTestFactory.TEST_FREE_MONGODB_MAJOR_VERSION,
        clusterDescription.get().getMongoDBMajorVersion());

    final List<TenantUpgradeStatus> tenantUpgradeStatusList =
        getTenantUpgradeStatusDao().getTenantUpgradesForGroup(getGroup().getId());

    assertEquals(tenantUpgradeStatusList.size(), 1);
    assertEquals(tenantUpgradeStatusList.get(0).getClusterName(), CLUSTER_NAME);
  }

  @Test
  public void testCreateUpgrade_ServerlessInstance() {
    // Create cluster upgrade request fails for serverless instances as target
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject serverlessJSONObj =
        doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    serverlessJSONObj.put("groupId", getGroup().getId().toHexString());

    final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, serverlessJSONObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testCreateUpgrade_EditM2M5() {
    // Create cluster upgrade request fails for m5 instances as target
    final ClusterDescription m5Cluster =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig()
                        .setGroupId(getGroup().getId())
                        .setClusterName(CLUSTER_NAME)
                        .setInstanceSize(FreeInstanceSize.M5)))
            .copy()
            .build();

    final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
    final JSONObject resp =
        doAuthedJsonPost(
            getAppUser(),
            endpoint,
            ClusterDescriptionViewUtils.mergeReplicationSpecs(
                new ClusterDescriptionView(m5Cluster)),
            SC_BAD_REQUEST);
    assertEquals(NDSErrorCode.UNSUPPORTED.name(), resp.get(ApiError.ERROR_CODE_FIELD));

    final ClusterDescription m2ClusterRegionChange =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig()
                        .setGroupId(getGroup().getId())
                        .setClusterName(CLUSTER_NAME)
                        .setInstanceSize(FreeInstanceSize.M2)
                        .setBackingProvider(CloudProvider.AZURE)
                        .setRegionName(AzureRegionName.US_EAST_2)))
            .copy()
            .build();

    final JSONObject resp2 =
        doAuthedJsonPost(
            getAppUser(),
            endpoint,
            ClusterDescriptionViewUtils.mergeReplicationSpecs(
                new ClusterDescriptionView(m2ClusterRegionChange)),
            SC_BAD_REQUEST);
    assertEquals(NDSErrorCode.UNSUPPORTED.name(), resp2.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testHasUnacknowledgedTenantUpgradeFailure() {
    final ClusterDescription failedUpgradeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(FAILED_UPGRADE_CLUSTER_NAME)
            .setGroupId(getGroup().getId())
            .build();
    getClusterDescriptionDao().save(failedUpgradeCluster);
    getTenantUpgradeStatusDao()
        .save(new TenantUpgradeStatus(getFailedTenantUpgradeDoc(getGroup().getId())));

    final JSONObject result =
        HttpUtils.getInstance()
            .get()
            .path(
                "/nds/clusterUpgrade/%s/%s/unacknowledgedFailures",
                getGroup().getId(), FAILED_UPGRADE_CLUSTER_NAME)
            .uiAuth(getAppUser())
            .expectedReturnStatus(SC_OK)
            .returnType(JSONObject.class)
            .send();
    assertTrue(result.getBoolean("hasUnacknowledgedFailure"));
    assertEquals(
        TenantUpgradeStatus.ErrorClass.RETRIABLE_FAILURE.toString(),
        result.getString("errorClass"));
  }

  @Test
  public void testMarkTenantUpgradeFailuresAcknowledged() {
    final ClusterDescription failedUpgradeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(FAILED_UPGRADE_CLUSTER_NAME)
            .setGroupId(getGroup().getId())
            .build();
    final Date clusterCreateDate = new DateTime().minusHours(1).toDate();
    getClusterDescriptionDao().save(failedUpgradeCluster);
    getTenantUpgradeStatusDao()
        .save(new TenantUpgradeStatus(getFailedTenantUpgradeDoc(getGroup().getId())));
    assertTrue(
        getTenantUpgradeStatusDao()
            .findUnacknowledgedFailureForCluster(
                getGroup().getId(), FAILED_UPGRADE_CLUSTER_NAME, clusterCreateDate)
            .isPresent());
    HttpUtils.getInstance()
        .patch()
        .path(
            "/nds/clusterUpgrade/%s/%s/unacknowledgedFailures",
            getGroup().getId(), FAILED_UPGRADE_CLUSTER_NAME)
        .uiAuth(getAppUser())
        .data(new JSONObject())
        .send();
    assertFalse(
        getTenantUpgradeStatusDao()
            .findUnacknowledgedFailureForCluster(
                getGroup().getId(), FAILED_UPGRADE_CLUSTER_NAME, clusterCreateDate)
            .isPresent());
  }

  @Test
  public void testStartClusterUpgrade_resourcePolicies() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, getOrganization(), FeatureFlag.ATLAS_RESOURCE_POLICIES);

    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    final JSONObject jObject =
        doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == cloud::Action::"cluster.createEdit",
                resource
            ) when {
                context.cluster.regions.containsAny([cloud::region::"aws:us-east-1",
                  cloud::region::"aws:us-east-2"])
            };
            """);
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), getOrganization().getId())
            .toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final String endpoint = String.format("/nds/clusterUpgrade/%s", getGroup().getId());
    final JSONObject response = doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_FORBIDDEN);

    assertEquals(
        response.getString("errorCode"), ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name());
    assertEquals(
        response.getString("message"),
        ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage());
  }

  private BasicDBObject getFailedTenantUpgradeDoc(final ObjectId pGroupId) {
    return new BasicDBObject()
        .append(TenantUpgradeStatus.FieldDefs.ID, new ObjectId())
        .append(
            TenantUpgradeStatus.FieldDefs.NAME,
            TenantUpgradeResourceIntTests.FAILED_UPGRADE_CLUSTER_NAME)
        .append(TenantUpgradeStatus.FieldDefs.GROUP_ID, pGroupId)
        .append(TenantUpgradeStatus.FieldDefs.CREATE_DATE, new Date())
        .append(TenantUpgradeStatus.FieldDefs.COMPLETE_DATE, new Date())
        .append(TenantUpgradeStatus.FieldDefs.USERNAME, null)
        .append(TenantUpgradeStatus.FieldDefs.STATE, TenantUpgradeStatus.State.FAILED)
        .append(
            TenantUpgradeStatus.FieldDefs.ERROR_CLASS,
            TenantUpgradeStatus.ErrorClass.RETRIABLE_FAILURE)
        .append(TenantUpgradeStatus.FieldDefs.HAS_UNACKNOWLEDGED_FAILURE, true);
  }
}
