package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.search.decoupled.api._public.model.SearchInstanceSizeAPI.API_INSTANCE_SIZE;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentRequestView;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentResponseView;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentResponseView.State;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentSpecView;
import com.xgen.cloud.search.decoupled.test.DecoupledAPITestUtils;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class FTSDeploymentResourceIntTests extends JUnit5BaseResourceTest {
  private static final String DEPLOYMENT_URL = "/nds/clusters/%s/%s/search/deployment";
  @Inject private DecoupledAPITestUtils _decoupledUtils;
  private Group _group;
  private AppUser _groupOwner;
  private final ObjectMapper _objectMapper = CustomJacksonJsonProvider.createObjectMapper();

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan("groupName");
    _groupOwner = MmsFactory.createGroupOwnerUser(_group);
  }

  @Test
  public void testCreateSearchDeployment() {
    _decoupledUtils.enableDecoupledFlags(_group);
    _decoupledUtils.createAWSCluster(_group.getId(), "clusterName");

    // send create deployment request
    final ApiSearchDeploymentRequestView request =
        new ApiSearchDeploymentRequestView(
            new ArrayList<>(
                List.of(new ApiSearchDeploymentSpecView(2, API_INSTANCE_SIZE.S20_HIGHCPU_NVME))));
    final JSONObject response =
        sendCreateDeploymentRequest("clusterName", request, HttpStatus.SC_OK);

    // assert response
    assertEquals(
        response.getString(ApiSearchDeploymentResponseView.Fields.GROUP_ID_FIELD),
        _group.getId().toString());
    assertEquals(
        response.getString(ApiSearchDeploymentResponseView.Fields.STATE_NAME_FIELD),
        State.UPDATING.toString());

    final JSONObject expectedSpec =
        new JSONObject()
            .put(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD, 2)
            .put(
                ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD,
                request.getSpec(0).getInstanceSize().toString());
    assertResponseSpec(
        response.getJSONArray(ApiSearchDeploymentResponseView.Fields.SPECS_FIELD).getJSONObject(0),
        expectedSpec);
  }

  @Test
  public void testCreateSearchDeployment_whenUserWithInsufficientPermissions_fail()
      throws Exception {
    _decoupledUtils.enableDecoupledFlags(_group);

    final AppUser groupMemberReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_DATA_ACCESS_READ_ONLY);
    sendCreateDeploymentRequest(
        "clusterName",
        DecoupledAPITestUtils.getValidDeploymentView(),
        HttpStatus.SC_FORBIDDEN,
        groupMemberReadOnly);
  }

  @Test
  public void testCreateSearchDeployment_whenFeatureFlagDisabled_fail() {
    sendCreateDeploymentRequest(
        "clusterName", DecoupledAPITestUtils.getValidDeploymentView(), HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testUpdateSearchDeployment() throws Exception {
    _decoupledUtils.enableDecoupledFlags(_group);
    _decoupledUtils.createAWSCluster(_group.getId(), "clusterName");

    // send create cluster request
    final ApiSearchDeploymentRequestView createRequest =
        new ApiSearchDeploymentRequestView(
            new ArrayList<>(
                List.of(new ApiSearchDeploymentSpecView(2, API_INSTANCE_SIZE.S20_HIGHCPU_NVME))));
    sendCreateDeploymentRequest("clusterName", createRequest, HttpStatus.SC_OK);

    // send update cluster request
    final ApiSearchDeploymentRequestView updateRequest =
        new ApiSearchDeploymentRequestView(
            new ArrayList<>(
                List.of(new ApiSearchDeploymentSpecView(4, API_INSTANCE_SIZE.S30_HIGHCPU_NVME))));
    final JSONObject response =
        sendUpdateDeploymentRequest("clusterName", updateRequest, HttpStatus.SC_OK);

    // assert response
    assertEquals(
        response.getString(ApiSearchDeploymentResponseView.Fields.GROUP_ID_FIELD),
        _group.getId().toString());
    assertEquals(
        response.getString(ApiSearchDeploymentResponseView.Fields.STATE_NAME_FIELD),
        State.UPDATING.toString());

    final JSONObject expectedSpec =
        new JSONObject()
            .put(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD, 4)
            .put(
                ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD,
                updateRequest.getSpec(0).getInstanceSize().toString());
    assertResponseSpec(
        response.getJSONArray(ApiSearchDeploymentResponseView.Fields.SPECS_FIELD).getJSONObject(0),
        expectedSpec);
  }

  @Test
  public void testUpdateSearchDeployment_whenUserWithInsufficientPermissions_fail()
      throws Exception {
    _decoupledUtils.enableDecoupledFlags(_group);

    final AppUser groupMemberReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_DATA_ACCESS_READ_ONLY);
    sendUpdateDeploymentRequest(
        "clusterName",
        DecoupledAPITestUtils.getValidDeploymentView(),
        HttpStatus.SC_FORBIDDEN,
        groupMemberReadOnly);
  }

  @Test
  public void testUpdateSearchDeployment_whenFeatureFlagDisabled_fail() throws Exception {
    sendUpdateDeploymentRequest(
        "clusterName", DecoupledAPITestUtils.getValidDeploymentView(), HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testGetSearchDeployment() {
    _decoupledUtils.enableDecoupledFlags(_group);
    _decoupledUtils.createAWSCluster(_group.getId(), "clusterName");

    // send Get deployment request
    final JSONObject emptyResponse = sendGetDeploymentRequest("clusterName", HttpStatus.SC_OK);

    // assert response
    assertTrue(emptyResponse.isEmpty());

    // send create deployment request
    final ApiSearchDeploymentRequestView request =
        new ApiSearchDeploymentRequestView(
            new ArrayList<>(
                List.of(new ApiSearchDeploymentSpecView(2, API_INSTANCE_SIZE.S20_HIGHCPU_NVME))));
    sendCreateDeploymentRequest("clusterName", request, HttpStatus.SC_OK);

    // send Get deployment request
    final JSONObject response = sendGetDeploymentRequest("clusterName", HttpStatus.SC_OK);

    // assert response
    assertEquals(
        response.getString(ApiSearchDeploymentResponseView.Fields.GROUP_ID_FIELD),
        _group.getId().toString());
    assertEquals(
        response.getString(ApiSearchDeploymentResponseView.Fields.STATE_NAME_FIELD),
        State.UPDATING.toString());

    final JSONObject expectedSpec =
        new JSONObject()
            .put(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD, 2)
            .put(
                ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD,
                request.getSpec(0).getInstanceSize().toString());
    assertResponseSpec(
        response.getJSONArray(ApiSearchDeploymentResponseView.Fields.SPECS_FIELD).getJSONObject(0),
        expectedSpec);
  }

  @Test
  public void testGetSearchDeployment_whenFeatureFlagDisabled_fail() {
    sendGetDeploymentRequest("clusterName", HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testDeleteSearchDeployment() {
    _decoupledUtils.enableDecoupledFlags(_group);
    _decoupledUtils.createAWSCluster(_group.getId(), "clusterName");

    // send create deployment request
    final ApiSearchDeploymentRequestView request =
        new ApiSearchDeploymentRequestView(
            new ArrayList<>(
                List.of(new ApiSearchDeploymentSpecView(2, API_INSTANCE_SIZE.S20_HIGHCPU_NVME))));
    sendCreateDeploymentRequest("clusterName", request, HttpStatus.SC_OK);

    // send delete cluster request
    sendDeleteDeploymentRequest("clusterName", HttpStatus.SC_OK);
  }

  private JSONObject sendCreateDeploymentRequest(
      final String pClusterName,
      final ApiSearchDeploymentRequestView pBody,
      final int pExpectedStatus) {
    return sendCreateDeploymentRequest(pClusterName, pBody, pExpectedStatus, _groupOwner);
  }

  private JSONObject sendCreateDeploymentRequest(
      final String pClusterName,
      final ApiSearchDeploymentRequestView pBody,
      final int pExpectedStatus,
      final AppUser pUser) {
    final String url = String.format(DEPLOYMENT_URL, _group.getId(), pClusterName);
    return doAuthedJsonPost(pUser, url, pBody, pExpectedStatus);
  }

  private JSONObject sendGetDeploymentRequest(
      final String pClusterName, final int pExpectedStatus) {
    return sendGetDeploymentRequest(pClusterName, pExpectedStatus, _groupOwner);
  }

  private JSONObject sendGetDeploymentRequest(
      final String pClusterName, final int pExpectedStatus, final AppUser pUser) {
    final String url = String.format(DEPLOYMENT_URL, _group.getId(), pClusterName);
    return doAuthedJsonGet(pUser, url, pExpectedStatus);
  }

  private JSONObject sendUpdateDeploymentRequest(
      final String pClusterName,
      final ApiSearchDeploymentRequestView pBody,
      final int pExpectedStatus)
      throws Exception {
    return sendUpdateDeploymentRequest(pClusterName, pBody, pExpectedStatus, _groupOwner);
  }

  private JSONObject sendUpdateDeploymentRequest(
      final String pClusterName,
      final ApiSearchDeploymentRequestView pBody,
      final int pExpectedStatus,
      final AppUser pUser)
      throws Exception {
    final String url = String.format(DEPLOYMENT_URL, _group.getId(), pClusterName);
    return doAuthedJsonPatch(
        pUser, url, new JSONObject(_objectMapper.writeValueAsString(pBody)), pExpectedStatus);
  }

  private void sendDeleteDeploymentRequest(final String pClusterName, final int pExpectedStatus) {
    final String url = String.format(DEPLOYMENT_URL, _group.getId(), pClusterName);
    doAuthedDelete(_groupOwner, url, pExpectedStatus);
  }

  private void assertResponseSpec(final JSONObject pActualSpec, final JSONObject pExpectedSpec) {
    assertEquals(
        pActualSpec.getInt(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD),
        pExpectedSpec.getInt(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD));
    assertEquals(
        pActualSpec.getString(ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD),
        pExpectedSpec.getString(ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD));
  }
}
