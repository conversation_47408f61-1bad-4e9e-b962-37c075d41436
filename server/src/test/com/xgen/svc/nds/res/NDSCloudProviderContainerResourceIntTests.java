package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.List;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSCloudProviderContainerResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private NDSGroupDao _groupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private AWSAccountDao _awsAccountDao;

  @Inject private GCPOrganizationDao _gcpOrganizationDao;

  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private AppSettings _appSettings;
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private GroupDao _mmsGroupDao;

  private Group _group;
  private AppUser _user;
  private String _urlPrefix;

  private final String CIDR = "**********/21";
  private final String VALID_GCP_CIDR = "**********/18";
  private final String AWS_REGION = "us-east-1";
  private final String AZURE_REGION = "US_EAST_2";
  private final String CIDR_FIELD = "atlasCidrBlock";
  private final String REGION_FIELD = "region";
  private final String PROVIDER_FIELD = "@provider";
  private final String IS_PROVISIONED_FIELD = "isProvisioned";

  private final String AWS = "AWS";
  private final String GCP = "GCP";
  private final String AZURE = "AZURE";

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createUser(_group);
    _urlPrefix = String.format("/nds/%s/containers", _group.getId());
  }

  private void createAWSContainer() throws JSONException {
    final AWSAccount account = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(account);

    final String endpoint = _urlPrefix;
    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, AWS);
    params.put(CIDR_FIELD, CIDR);
    params.put(REGION_FIELD, AWS_REGION);

    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_ACCEPTED);
  }

  private JSONObject createContainerWithIsProvisioned() throws JSONException {

    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, AWS);
    params.put(CIDR_FIELD, CIDR);
    params.put(REGION_FIELD, AWS_REGION);
    params.put(IS_PROVISIONED_FIELD, true);

    return params;
  }

  @Test
  public void testCreateAWSContainer_Valid() throws JSONException {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    createAWSContainer();

    final List<CloudProviderContainer> containers =
        _groupDao.find(_group.getId()).get().getCloudProviderContainers();
    assertEquals(containers.size(), 1);
    assertEquals(containers.get(0).getCloudProvider(), CloudProvider.AWS);

    final AWSCloudProviderContainer container = (AWSCloudProviderContainer) containers.get(0);
    assertEquals(container.getAtlasCidr(), CIDR);
    assertEquals(container.getRegion(), AWSRegionName.US_EAST_1);
  }

  @Test
  public void testCreateAWSContainer_Invalid() throws JSONException {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    final AWSAccount account = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(account);

    final String endpoint = _urlPrefix;
    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, AWS);
    params.put(REGION_FIELD, AWS_REGION);
    params.put(CIDR_FIELD, "1.0.0.1/32");

    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);

    params.put(CIDR_FIELD, "*******");
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);

    params.put(CIDR_FIELD, "some random string");
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateGCPContainer_Valid() throws JSONException {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    _gcpOrganizationDao.save(
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization()));

    final String endpoint = _urlPrefix;
    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, GCP);
    params.put(CIDR_FIELD, VALID_GCP_CIDR);

    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_ACCEPTED);

    final List<CloudProviderContainer> containers =
        _groupDao.find(_group.getId()).get().getCloudProviderContainers();
    assertEquals(1, containers.size());
    assertEquals(CloudProvider.GCP, containers.get(0).getCloudProvider());

    final GCPCloudProviderContainer container = (GCPCloudProviderContainer) containers.get(0);
    assertTrue(container.getCustomAtlasCidr().isPresent());
    assertEquals(container.getCustomAtlasCidr().get(), VALID_GCP_CIDR);
  }

  @Test
  public void testCreateGCPContainer_Invalid() throws JSONException {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    _gcpOrganizationDao.save(
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization()));

    final String endpoint = _urlPrefix;
    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, GCP);
    params.put(CIDR_FIELD, CIDR);

    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);

    params.put(CIDR_FIELD, "*******");
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);

    params.put(CIDR_FIELD, "**********/32");
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);

    params.put(CIDR_FIELD, "some random string");
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateGCPContainer_GovEnabled() throws SvcException {
    _appSettings.setProp(
        AppSettings.Fields.NDS_GOV_US_ENABLED.value, "true", AppSettings.SettingType.MEMORY);

    _gcpOrganizationDao.save(
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization()));

    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, GCP);
    params.put(CIDR_FIELD, VALID_GCP_CIDR);

    // commercial fedramp regions - invalid
    {
      final Group group = MmsFactory.createGroupWithNDSPlan("govGroup1", new ObjectId());
      _ndsGroupSvc.ensureGroup(
          group.getId(), false, RegionUsageRestrictions.COMMERCIAL_FEDRAMP_REGIONS_ONLY, false);
      _mmsGroupDao.save(group);
      final AppUser user = MmsFactory.createUser(group);
      final String endpoint = String.format("/nds/%s/containers", group.getId());

      final JSONObject response =
          doAuthedJsonPut(user, endpoint, params, HttpStatus.SC_BAD_REQUEST);
    }
    // gov regions only  - invalid
    {
      final Group group = MmsFactory.createGroupWithNDSPlan("govGroup2", new ObjectId());
      _ndsGroupSvc.ensureGroup(
          group.getId(), false, RegionUsageRestrictions.GOV_REGIONS_ONLY, false);
      _mmsGroupDao.save(group);
      final AppUser user = MmsFactory.createUser(group);
      final String endpoint = String.format("/nds/%s/containers", group.getId());

      doAuthedJsonPut(user, endpoint, params, HttpStatus.SC_BAD_REQUEST);
    }

    // gov regions only with feature flag enabled - valid
    _gcpOrganizationDao.save(
        NDSModelTestFactory.getFullyAvailableGovGCPOrganizationWithCredentials());
    {
      final Group group = MmsFactory.createGroupWithNDSPlan("govGroup3", new ObjectId());
      _ndsGroupSvc.ensureGroup(
          group.getId(), false, RegionUsageRestrictions.GOV_REGIONS_ONLY, false);
      _mmsGroupDao.save(group);
      final AppUser user = MmsFactory.createUser(group);
      final String endpoint = String.format("/nds/%s/containers", group.getId());

      doAuthedJsonPut(user, endpoint, params, HttpStatus.SC_ACCEPTED);

      final List<CloudProviderContainer> containers =
          _groupDao.find(group.getId()).get().getCloudProviderContainers();
      assertEquals(1, containers.size());
      assertEquals(CloudProvider.GCP, containers.get(0).getCloudProvider());

      final GCPCloudProviderContainer container = (GCPCloudProviderContainer) containers.get(0);
      assertTrue(container.getCustomAtlasCidr().isPresent());
      assertEquals(container.getCustomAtlasCidr().get(), VALID_GCP_CIDR);
    }
  }

  @Test
  public void testCreateAzureContainer_Valid() {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    _azureSubscriptionDao.save(
        new AzureSubscription(NDSModelTestFactory.getFullyAvailableAzureSubscription()));

    final String endpoint = _urlPrefix;
    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, AZURE);
    params.put(CIDR_FIELD, CIDR);
    params.put(REGION_FIELD, AZURE_REGION);

    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_ACCEPTED);

    final List<CloudProviderContainer> containers =
        _groupDao.find(_group.getId()).get().getCloudProviderContainers();
    assertEquals(containers.size(), 1);
    assertEquals(containers.get(0).getCloudProvider(), CloudProvider.AZURE);

    final AzureCloudProviderContainer container = (AzureCloudProviderContainer) containers.get(0);
    assertEquals(container.getAtlasCidr(), CIDR);
  }

  @Test
  public void testCreateAzureContainer_Invalid() {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    _azureSubscriptionDao.save(
        new AzureSubscription(NDSModelTestFactory.getFullyAvailableAzureSubscription()));

    final String endpoint = _urlPrefix;
    final JSONObject params = new JSONObject();
    params.put(PROVIDER_FIELD, AZURE);

    params.put(CIDR_FIELD, "*******");
    params.put(REGION_FIELD, AZURE_REGION);
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);

    params.put(CIDR_FIELD, CIDR);
    params.put(REGION_FIELD, AWS_REGION);
    doAuthedJsonPut(_user, endpoint, params, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testIsProvisionedField() {
    final AWSAccount account = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(account);

    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    final AppUser user = MmsFactory.createGlobalOwnerAdminUser(_group);
    final JSONObject requestBody = createContainerWithIsProvisioned();

    doAuthedJsonPut(user, _urlPrefix, requestBody, HttpStatus.SC_ACCEPTED);

    final List<CloudProviderContainer> containers =
        _groupDao.find(_group.getId()).get().getCloudProviderContainers();
    assertEquals(containers.size(), 1);
    assertEquals(containers.get(0).getCloudProvider(), CloudProvider.AWS);
    final CloudProviderContainer container = containers.get(0);

    // isProvisioned is false even though we specified true
    final boolean currentIsProvisionedValue = container.isProvisioned();
    assertFalse(currentIsProvisionedValue);

    // Make sure ignorable property isProvisioned is ignored if we attempt to update it
    final String endpoint = _urlPrefix + String.format("/%s", container.getId());
    doAuthedJsonPatch(user, endpoint, requestBody, HttpStatus.SC_ACCEPTED);

    final JSONArray arr = doAuthedJsonArrayGet(user, _urlPrefix, HttpStatus.SC_OK);
    assertFalse((Boolean) arr.getJSONObject(0).get(IS_PROVISIONED_FIELD));
  }

  @Test
  public void testGetContainers() throws JSONException {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    // Create an AWS container
    createAWSContainer();

    // Create an Azure subscription
    final AzureSubscription azureSubscription =
        new AzureSubscription(NDSModelTestFactory.getFullyAvailableAzureSubscription());
    _azureSubscriptionDao.save(azureSubscription);

    // Create an Azure container
    _groupDao.addCloudContainer(
        _group.getId(),
        new AzureCloudProviderContainer(azureSubscription.getId(), AzureRegionName.US_EAST));

    final String endpoint = _urlPrefix;
    final JSONArray arr = doAuthedJsonArrayGet(_user, endpoint, HttpStatus.SC_OK);

    // Make sure both are returned
    assertEquals(arr.length(), 2);
  }

  @Test
  public void testGetContainerDefaults() throws JSONException {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    String endpoint = _urlPrefix + "/aws/defaults";
    JSONObject json = doAuthedJsonGet(_user, endpoint);

    assertEquals(NDSDefaults.ATLAS_CIDR, json.getString("atlasCidrBlock"));

    JSONArray regions = json.getJSONArray("regions");
    for (int i = 0; i < regions.length(); i++) {
      final JSONObject region = regions.getJSONObject(i);
      assertTrue(
          AWSNDSDefaults.ALL_AVAILABLE_REGIONS.contains(
              AWSRegionName.findByValue(region.getString("name")).get()));
    }

    endpoint = _urlPrefix + "/gcp/defaults";
    json = doAuthedJsonGet(_user, endpoint);
    regions = json.getJSONArray("regions");
    for (int i = 0; i < regions.length(); i++) {
      final JSONObject region = regions.getJSONObject(i);
      assertTrue(
          GCPNDSDefaults.AVAILABLE_REGIONS.contains(
              GCPRegionName.findByValue(region.getString("name")).get()));
      assertTrue(region.has("gcpRegionCode"));
    }
  }

  @Test
  public void testGetContainerById() {
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    createAWSContainer();

    final ObjectId containerId =
        _groupDao.find(_group.getId()).get().getCloudProviderContainers().get(0).getId();
    final String endpoint = _urlPrefix + String.format("/%s", containerId);

    doAuthedJsonGet(_user, endpoint, HttpStatus.SC_OK);
  }
}
