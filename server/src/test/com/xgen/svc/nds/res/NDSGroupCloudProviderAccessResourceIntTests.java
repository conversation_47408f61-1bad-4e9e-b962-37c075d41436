package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isNotNull;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

import com.amazonaws.services.securitytoken.model.Credentials;
import com.google.iam.admin.v1.ServiceAccount;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._public.model.error.AzureApiException;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._private.dao.GCPCloudProviderContainerDao;
import com.xgen.cloud.nds.gcp._public.model.GCPErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.gcp._public.svc.GCPOrganizationSvc;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccess;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAzureServicePrincipal;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessDataLakeFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessEncryptionAtRestFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessFeatureUsageType;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.featureid.NDSCloudProviderAccessFeatureUsageDataLakeFeatureId;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.gcp.model.ui.GCPCloudProviderContainerView;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAWSIAMRoleView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAzureServicePrincipalView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessFeatureUsageView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessGCPServiceAccountView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessRoleView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.featureId.NDSCloudProviderAccessFeatureUsageDataLakeFeatureIdView;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSGroupCloudProviderAccessResourceIntTests extends JUnit5BaseResourceTest {

  private static final String DATA_LAKE_NAME = "DataLakeExample";

  @Inject private GCPOrganizationSvc _gcpOrganizationSvc;
  @Inject private GCPCloudProviderContainerDao _gcpContainerDao;
  @Inject private NDSGroupDao _groupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private NDSCloudProviderContainerSvc _ndsContainerSvc;
  @Inject private AppSettings _appSettings;

  private Group _group;
  private Group _cnRegionsOnlyGroup;
  private AppUser _user;
  private AppUser _readOnlyAdminUser;
  private AppUser _cnRegionsOnlyUser;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createUser(_group);
    _readOnlyAdminUser = MmsFactory.createReadOnlyAdminUser(_group);
    _gcpOrganizationSvc.save(
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization()));

    _cnRegionsOnlyGroup =
        MmsFactory.createGroupWithNDSPlan("cnRegionsOnlyGroup", ObjectId.get(), true);
    _cnRegionsOnlyUser = MmsFactory.createUser(_cnRegionsOnlyGroup, "<EMAIL>");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/AWSAccountDao/awsAccounts.json.ftl", null, "nds", "config.nds.awsAccounts");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/AzureSubscriptionDao/azureSubscriptions.json.ftl",
        null,
        "nds",
        "config.nds.azureSubscriptions");
  }

  @Test
  public void testGetCloudProviderAccess_AWS() {
    final Set<String> awsIamRoleArns =
        IntStream.range(0, 3)
            .mapToObj(i -> "arn:aws:iam::************:role/buttered-role-arn-" + i)
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(awsIamRoleArns, Set.of(), Set.of());

    // SUCCESS - check num roles and role attributes
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              HttpStatus.SC_OK);
      final JSONArray awsIamRolesResponse = response.getJSONArray("awsIamRoles");

      assertEquals(awsIamRoleArns.size(), awsIamRolesResponse.length());

      for (int i = 0; i < awsIamRolesResponse.length(); i++) {

        final JSONObject awsIamRole = awsIamRolesResponse.getJSONObject(i);

        // assumed role arn
        assertTrue(
            awsIamRoleArns.contains(
                awsIamRole.getString(
                    NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.IAM_ASSUMED_ROLE_ARN)));

        // feature usages
        testFeatureUsages(
            awsIamRole.getJSONArray(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.FEATURE_USAGES));
      }
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonGet(
          _readOnlyAdminUser,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGetCloudProviderAccess_Azure() {
    final Set<String> azureServicePrincipalIds =
        IntStream.range(0, 3)
            .mapToObj(i -> "azure-service-principal-" + i)
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(Set.of(), azureServicePrincipalIds, Set.of());

    // SUCCESS - check num ids and attributes
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              HttpStatus.SC_OK);
      final JSONArray azureServicePrincipalResponse =
          response.getJSONArray("azureServicePrincipals");

      assertEquals(azureServicePrincipalIds.size(), azureServicePrincipalResponse.length());

      for (int i = 0; i < azureServicePrincipalResponse.length(); i++) {

        final JSONObject azureServicePrincipal = azureServicePrincipalResponse.getJSONObject(i);

        // service principal id
        assertTrue(
            azureServicePrincipalIds.contains(
                azureServicePrincipal.getString(
                    NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs
                        .SERVICE_PRINCIPAL_ID)));

        // feature usages
        testFeatureUsages(
            azureServicePrincipal.getJSONArray(
                NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.FEATURE_USAGES));
      }
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonGet(
          _readOnlyAdminUser,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGetCloudProviderAccess_GCP() {
    final Set<String> gcpServiceAccountsForAtlas =
        IntStream.range(0, 3)
            .mapToObj(i -> "gcp-service-account-" + i + "@project-id.iam.gserviceaccount.com")
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(Set.of(), Set.of(), gcpServiceAccountsForAtlas);

    // SUCCESS - check num ids and attributes
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              HttpStatus.SC_OK);
      final JSONArray gcpServiceAccountsResponse = response.getJSONArray("gcpServiceAccounts");

      assertEquals(gcpServiceAccountsForAtlas.size(), gcpServiceAccountsResponse.length());

      for (int i = 0; i < gcpServiceAccountsResponse.length(); i++) {

        final JSONObject gcpServiceAccount = gcpServiceAccountsResponse.getJSONObject(i);

        // check service account email
        assertTrue(
            gcpServiceAccountsForAtlas.contains(
                gcpServiceAccount.getString(
                    NDSCloudProviderAccessGCPServiceAccount.FieldDefs
                        .GCP_SERVICE_ACCOUNT_FOR_ATLAS)));

        // check feature usages
        testFeatureUsages(
            gcpServiceAccount.getJSONArray(
                NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.FEATURE_USAGES));
      }
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonGet(
          _readOnlyAdminUser,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGetCloudProviderAccess_AllCloudProviders() {
    final Set<String> awsIamRoleArns =
        IntStream.range(0, 3)
            .mapToObj(i -> "arn:aws:iam::************:role/buttered-role-arn-" + i)
            .collect(Collectors.toSet());
    final Set<String> azureServicePrincipalIds =
        IntStream.range(0, 4)
            .mapToObj(i -> "azure-service-principal-" + i)
            .collect(Collectors.toSet());
    final Set<String> gcpServiceAccountsForAtlas =
        IntStream.range(0, 5)
            .mapToObj(i -> "gcp-service-account-" + i + "@project-id.iam.gserviceaccount.com")
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(
        awsIamRoleArns, azureServicePrincipalIds, gcpServiceAccountsForAtlas);

    // SUCCESS
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              HttpStatus.SC_OK);
      final JSONArray awsIamRolesResponse = response.getJSONArray("awsIamRoles");
      final JSONArray azureServicePrincipalResponse =
          response.getJSONArray("azureServicePrincipals");
      final JSONArray gcpServiceAccountResponse = response.getJSONArray("gcpServiceAccounts");

      assertEquals(awsIamRoleArns.size(), awsIamRolesResponse.length());
      assertEquals(azureServicePrincipalIds.size(), azureServicePrincipalResponse.length());
      assertEquals(gcpServiceAccountsForAtlas.size(), gcpServiceAccountResponse.length());
    }
  }

  @Test
  public void testGetIdsFromCloudProviderAccess_InvalidCloudProvider() {
    // FAIL - unsupported cloud provider
    {
      final JSONObject error =
          doAuthedJsonGet(
              _user,
              String.format("/nds/%s/cloudProviderAccess/%s", _group.getId(), "invalid"),
              HttpStatus.SC_BAD_REQUEST);

      assertEquals(error.getString("errorCode"), NDSErrorCode.INVALID_CLOUD_PROVIDER.name());
      assertEquals(error.getString("message"), NDSErrorCode.INVALID_CLOUD_PROVIDER.getMessage());
    }
  }

  @Test
  public void testGetRolesFromCloudProviderAccess_AWS() {
    final Set<String> awsIamRoleArns =
        IntStream.range(0, 3)
            .mapToObj(i -> "arn:aws:iam::************:role/buttered-role-arn-" + i)
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(awsIamRoleArns, Set.of(), Set.of());

    // SUCCESS - check num roles and role attributes
    {
      final JSONArray awsIamRolesResponse =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(awsIamRoleArns.size(), awsIamRolesResponse.length());

      for (int i = 0; i < awsIamRolesResponse.length(); i++) {

        final JSONObject awsIamRole = awsIamRolesResponse.getJSONObject(i);

        // assumed role arn
        assertTrue(
            awsIamRoleArns.contains(
                awsIamRole.getString(
                    NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.IAM_ASSUMED_ROLE_ARN)));

        // feature usages
        testFeatureUsages(
            awsIamRole.getJSONArray(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.FEATURE_USAGES));
      }
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonArrayGet(
          _readOnlyAdminUser,
          String.format("/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGetIdsFromCloudProviderAccess_Azure() {
    final Set<String> azureServicePrincipalIds =
        IntStream.range(0, 3)
            .mapToObj(i -> "azure-service-principal-" + i)
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(Set.of(), azureServicePrincipalIds, Set.of());

    // SUCCESS - check num roles and role attributes
    {
      final JSONArray azureServicePrincipalResponse =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AZURE.name()),
              HttpStatus.SC_OK);

      assertEquals(azureServicePrincipalIds.size(), azureServicePrincipalResponse.length());

      for (int i = 0; i < azureServicePrincipalResponse.length(); i++) {

        final JSONObject azureServicePrincipal = azureServicePrincipalResponse.getJSONObject(i);

        // service principal id
        assertTrue(
            azureServicePrincipalIds.contains(
                azureServicePrincipal.getString(
                    NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs
                        .SERVICE_PRINCIPAL_ID)));

        // feature usages
        testFeatureUsages(
            azureServicePrincipal.getJSONArray(
                NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.FEATURE_USAGES));
      }
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonArrayGet(
          _readOnlyAdminUser,
          String.format(
              "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AZURE.name()),
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGetServiceAccountsFromCloudProviderAccess_GCP() {
    final Set<String> gcpServiceAccountsForAtlas =
        IntStream.range(0, 3)
            .mapToObj(i -> "gcp-service-account-" + i + "@project-id.iam.gserviceaccount.com")
            .collect(Collectors.toSet());

    createGroupWithCloudProviderAccess(Set.of(), Set.of(), gcpServiceAccountsForAtlas);

    // SUCCESS - check num roles and role attributes
    {
      final JSONArray gcpServiceAccountResponse =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.GCP.name()),
              HttpStatus.SC_OK);

      assertEquals(gcpServiceAccountsForAtlas.size(), gcpServiceAccountResponse.length());

      for (int i = 0; i < gcpServiceAccountResponse.length(); i++) {

        final JSONObject gcpServiceAccount = gcpServiceAccountResponse.getJSONObject(i);

        // check service account email
        assertTrue(
            gcpServiceAccountsForAtlas.contains(
                gcpServiceAccount.getString(
                    NDSCloudProviderAccessGCPServiceAccount.FieldDefs
                        .GCP_SERVICE_ACCOUNT_FOR_ATLAS)));

        // check feature usages
        testFeatureUsages(
            gcpServiceAccount.getJSONArray(
                NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.FEATURE_USAGES));
      }
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonArrayGet(
          _readOnlyAdminUser,
          String.format("/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.GCP.name()),
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testAddCloudProviderAccessRole_UnsupportedCloudProvider() throws Exception {
    setUpGroup(_group.getId(), false);

    // FAIL - unsupported cloud provider
    {
      doAuthedJsonPostReturnHttp(
          _user,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          Map.of("providerName", "invalid"),
          HttpStatus.SC_BAD_REQUEST);
    }
  }

  @Test
  public void testAddCloudProviderAccessRole_AWS() {
    setUpGroup(_group.getId(), false);

    // SUCCESS - assert initially empty
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(0, response.length());
    }

    // SUCCESS - post new aws iam role
    {
      doAuthedJsonPost(
          _user,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          Map.of("providerName", "AWS"),
          HttpStatus.SC_OK);
    }

    // SUCCESS - assert new aws iam role has been created
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(1, response.length());
    }

    // SUCCESS - post new aws iam role with IAM Assumed Role ARN / External ID
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              Map.of(
                  "providerName",
                  "AWS",
                  "iamAssumedRoleArn",
                  "arn:aws:iam::111111111111:role/big-spice",
                  "atlasAssumedRoleExternalId",
                  "20403496-f534-48bf-9121-e4692a5e9ff9"),
              HttpStatus.SC_OK);
    }

    // SUCCESS - assert new aws iam role has been created
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(2, response.length());
      final JSONObject awsIAMRole1 = response.getJSONObject(1);
      assertEquals(
          "arn:aws:iam::111111111111:role/big-spice", awsIAMRole1.getString("iamAssumedRoleArn"));
      assertEquals(
          "20403496-f534-48bf-9121-e4692a5e9ff9",
          awsIAMRole1.getString("atlasAssumedRoleExternalId"));
    }
  }

  @Test
  public void testAddCloudProviderAccessRole_Azure()
      throws NoSuchFieldException, IllegalAccessException {
    setUpGroup(_group.getId(), false);

    // SUCCESS - assert initially empty
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AZURE.name()),
              HttpStatus.SC_OK);

      assertEquals(0, response.length());
    }

    // FAIL - validation failed
    mockAzureApiSvc(false);
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              Map.of(
                  "providerName",
                  "AZURE",
                  "servicePrincipalId",
                  "servicePrincipalId",
                  "tenantId",
                  "tenantId",
                  "atlasAzureAppId",
                  "azure-multi-tenant-app-id"),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CANNOT_AUTHENTICATE_SERVICE_PRINCIPAL.name(),
          response.getString("errorCode"));
    }

    mockAzureApiSvc(true);
    // SUCCESS - post new Azure service principal
    {
      doAuthedJsonPost(
          _user,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          Map.of(
              "providerName",
              "AZURE",
              "servicePrincipalId",
              "servicePrincipalId",
              "tenantId",
              "tenantId",
              "atlasAzureAppId",
              "azure-multi-tenant-app-id"),
          HttpStatus.SC_OK);
    }

    // SUCCESS - assert new Azure service principal has been created
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AZURE.name()),
              HttpStatus.SC_OK);

      assertEquals(1, response.length());
      final JSONObject servicePrincipal = response.getJSONObject(0);
      assertEquals(
          "servicePrincipalId",
          servicePrincipal.getString(
              NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.SERVICE_PRINCIPAL_ID));
      assertEquals(
          "tenantId",
          servicePrincipal.getString(
              NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.TENANT_ID));
      assertEquals(
          "fd63a0b5-5835-423d-9903-d935a44ae1bb",
          servicePrincipal.getString(
              NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.ATLAS_AZURE_APP_ID));
    }
  }

  @Test
  public void testAddCloudProviderAccessRole_GCP()
      throws SvcException, NoSuchFieldException, IllegalAccessException {
    setUpGroup(_group.getId(), false);

    // SUCCESS - assert initially empty
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.GCP.name()),
              HttpStatus.SC_OK);

      assertEquals(0, response.length());
    }

    // FAIL - no GCP container
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              Map.of("providerName", "GCP"),
              HttpStatus.SC_OK);
    }

    // Create GCP container
    final ObjectId gcpContainerId =
        _ndsContainerSvc.upsertCloudContainer(
            _group.getId(), new GCPCloudProviderContainerView(NDSDefaults.ATLAS_CIDR, null));
    _gcpContainerDao.setProvisionedFields(
        _group.getId(), gcpContainerId, "project-id", "", List.of());

    // FAIL - post new GCP service account
    mockGCPApiSvc(false);
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              Map.of("providerName", "GCP"),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.GCP_SERVICE_ACCOUNT_CREATION_ERROR.name(), response.getString("errorCode"));
    }

    // SUCCESS - post new GCP service account
    mockGCPApiSvc(true);
    {
      doAuthedJsonPost(
          _user,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          Map.of("providerName", "GCP"),
          HttpStatus.SC_OK);
    }

    // SUCCESS - assert new GCP service account has been created
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.GCP.name()),
              HttpStatus.SC_OK);

      assertEquals(2, response.length());
      final JSONObject serviceAccount = response.getJSONObject(1);
      assertNotNull(
          serviceAccount.getString(
              NDSCloudProviderAccessGCPServiceAccountView.FieldDefs.GCP_SERVICE_ACCOUNT_FOR_ATLAS));
    }

    // SUCCESS - post new GCP service account with service account email
    {
      doAuthedJsonPost(
          _user,
          String.format("/nds/%s/cloudProviderAccess", _group.getId()),
          Map.of(
              "providerName",
              "GCP",
              "roleId",
              new ObjectId(),
              "gcpServiceAccountForAtlas",
              "<EMAIL>"),
          HttpStatus.SC_OK);
    }

    // FAILED - post the same service account as previous, service account already exists
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              String.format("/nds/%s/cloudProviderAccess", _group.getId()),
              Map.of(
                  "providerName",
                  "GCP",
                  "roleId",
                  new ObjectId(),
                  "gcpServiceAccountForAtlas",
                  "<EMAIL>"),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.DUPLICATE_GCP_SERVICE_ACCOUNT.name(), response.getString("errorCode"));
    }

    // SUCCESS - assert new GCP service account has been created
    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.GCP.name()),
              HttpStatus.SC_OK);

      assertEquals(3, response.length());
      final JSONObject gcpServiceAccount1 = response.getJSONObject(2);
      assertEquals(
          "<EMAIL>",
          gcpServiceAccount1.getString(
              NDSCloudProviderAccessGCPServiceAccountView.FieldDefs.GCP_SERVICE_ACCOUNT_FOR_ATLAS));
    }
  }

  @Test
  public void testPatchCloudProviderAccessRole()
      throws IllegalAccessException, NoSuchFieldException {
    setUpGroup(_group.getId(), false);

    final String path = String.format("/nds/%s/cloudProviderAccess", _group.getId());

    // Create empty aws iam role
    final JSONObject emptyAWSIamRole =
        doAuthedJsonPost(_user, path, Map.of("providerName", "AWS"), HttpStatus.SC_OK);
    assertCloudProviderAWSIAMRoleCreation(emptyAWSIamRole);

    final String awsIamAssumedRoleArn = "arn:aws:iam::************:role/buttered-role-arn";

    // setup mock for temp credentials
    mockAWSApiSvc();

    final JSONObject patch =
        emptyAWSIamRole
            .put(
                NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.IAM_ASSUMED_ROLE_ARN,
                awsIamAssumedRoleArn)
            .put(NDSCloudProviderAccessRoleView.FieldDefs.CLOUD_PROVIDER, CloudProvider.AWS.name());

    // SUCCESS - aws iam role should exist
    {
      final ObjectId roleId =
          new ObjectId(
              emptyAWSIamRole.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ROLE_ID));
      final JSONObject updateResponse =
          doAuthedJsonPatch(_user, String.format("%s/%s", path, roleId), patch, HttpStatus.SC_OK);
      assertCloudProviderAWSIAMUpdate(awsIamAssumedRoleArn, updateResponse);
    }
  }

  @Test
  public void testPatchCloudProviderAccessRole_Azure()
      throws NoSuchFieldException, IllegalAccessException {
    setUpGroup(_group.getId(), false);
    mockAzureApiSvc(true);

    final String path = String.format("/nds/%s/cloudProviderAccess", _group.getId());

    // set up new Azure principal
    final JSONObject initialAzurePrincipal =
        doAuthedJsonPost(
            _user,
            String.format("/nds/%s/cloudProviderAccess", _group.getId()),
            Map.of(
                "providerName",
                "AZURE",
                "servicePrincipalId",
                "servicePrincipalId",
                "tenantId",
                "tenantId",
                "atlasAzureAppId",
                "azure-multi-tenant-app-id"),
            HttpStatus.SC_OK);

    final String updatedServicePrincipalId = "updatedServicePrincipalId";
    final JSONObject patch =
        initialAzurePrincipal
            .put(
                NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.SERVICE_PRINCIPAL_ID,
                updatedServicePrincipalId)
            .put(
                NDSCloudProviderAccessRoleView.FieldDefs.CLOUD_PROVIDER,
                CloudProvider.AZURE.name());

    // FAIL - validation failed
    mockAzureApiSvc(false);
    {
      final ObjectId id =
          new ObjectId(
              initialAzurePrincipal.getString(
                  NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.ID));
      final JSONObject updateResponse =
          doAuthedJsonPatch(
              _user, String.format("%s/%s", path, id), patch, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CANNOT_AUTHENTICATE_SERVICE_PRINCIPAL.name(),
          updateResponse.getString("errorCode"));
    }

    mockAzureApiSvc(true);
    // SUCCESS
    {
      final ObjectId id =
          new ObjectId(
              initialAzurePrincipal.getString(
                  NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.ID));
      final JSONObject updateResponse =
          doAuthedJsonPatch(_user, String.format("%s/%s", path, id), patch, HttpStatus.SC_OK);
      assertEquals(
          updatedServicePrincipalId,
          updateResponse.getString(
              NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.SERVICE_PRINCIPAL_ID));
    }

    // FAIL - invalid service principal id
    {
      final ObjectId id = ObjectId.get();
      final JSONObject updateResponse =
          doAuthedJsonPatch(
              _user, String.format("%s/%s", path, id), patch, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_NOT_FOUND.name(),
          updateResponse.getString("errorCode"));
    }
  }

  @Test
  public void testDeleteRoleFromCloudProviderAccess() {
    setUpGroup(_group.getId(), false);

    final JSONObject createdAWSIAMRole =
        doAuthedJsonPost(
            _user,
            String.format("/nds/%s/cloudProviderAccess", _group.getId()),
            Map.of("providerName", "AWS"));

    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(1, response.length());
    }

    // FAIL - role does not exist
    {
      final ObjectId roleId = ObjectId.get();
      final JSONObject error =
          doAuthedJsonDelete(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s/%s",
                  _group.getId(), CloudProvider.AWS.name(), roleId),
              HttpStatus.SC_BAD_REQUEST);

      assertEquals(
          error.getString("errorCode"), NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND.name());
      assertTrue(error.getString("message").contains(roleId.toString()));

      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(1, response.length());
    }

    final String roleId =
        createdAWSIAMRole.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ROLE_ID);

    // SUCCESS
    {
      doAuthedJsonDelete(
          _user,
          String.format(
              "/nds/%s/cloudProviderAccess/%s/%s",
              _group.getId(), CloudProvider.AWS.name(), roleId),
          HttpStatus.SC_NO_CONTENT);

      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      assertEquals(0, response.length());
    }
  }

  @Test
  public void testDeleteRoleFromCloudProviderAccess_Azure() {
    setUpGroup(_group.getId(), false);

    final String path = String.format("/nds/%s/cloudProviderAccess", _group.getId());

    // set up new Azure principal
    final JSONObject initialAzurePrincipal =
        doAuthedJsonPost(
            _user,
            String.format("/nds/%s/cloudProviderAccess", _group.getId()),
            Map.of(
                "providerName",
                "AZURE",
                "servicePrincipalId",
                "servicePrincipalId",
                "tenantId",
                "tenantId",
                "atlasAzureAppId",
                "azure-multi-tenant-app-id"),
            HttpStatus.SC_OK);

    {
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AZURE.name()),
              HttpStatus.SC_OK);
      assertEquals(1, response.length());
    }

    // FAIL - invalid service principal id
    {
      final ObjectId id = ObjectId.get();
      final JSONObject errorResponse =
          doAuthedJsonDelete(
              _user,
              String.format("%s/%s/%s", path, CloudProvider.AZURE.name(), id),
              HttpStatus.SC_BAD_REQUEST);

      assertEquals(
          errorResponse.getString("errorCode"),
          NDSErrorCode.CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_NOT_FOUND.name());
      assertTrue(errorResponse.getString("message").contains(id.toHexString()));
    }

    // SUCCESS
    {
      final ObjectId id =
          new ObjectId(
              initialAzurePrincipal.getString(
                  NDSCloudProviderAccessAzureServicePrincipalView.FieldDefs.ID));
      doAuthedJsonDelete(
          _user,
          String.format("%s/%s/%s", path, CloudProvider.AZURE.name(), id),
          HttpStatus.SC_NO_CONTENT);

      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.AZURE.name()),
              HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }
  }

  @Test
  public void testDeleteRoleFromCloudProviderAccess_GCP()
      throws SvcException, IllegalAccessException, NoSuchFieldException {
    setUpGroup(_group.getId(), false);

    final String path = String.format("/nds/%s/cloudProviderAccess", _group.getId());

    // Create GCP container
    final ObjectId gcpContainerId =
        _ndsContainerSvc.upsertCloudContainer(
            _group.getId(), new GCPCloudProviderContainerView(NDSDefaults.ATLAS_CIDR, null));
    _gcpContainerDao.setProvisionedFields(
        _group.getId(), gcpContainerId, "project-id", "", List.of());

    // Create GCP service account
    mockGCPApiSvc(true);
    final JSONObject initialServiceAccount =
        doAuthedJsonPost(_user, path, Map.of("providerName", "GCP"), HttpStatus.SC_OK);

    // FAIL - invalid service principal
    {
      final ObjectId id = ObjectId.get();
      final JSONObject errorResponse =
          doAuthedJsonDelete(
              _user,
              String.format("%s/%s/%s", path, CloudProvider.GCP.name(), id),
              HttpStatus.SC_BAD_REQUEST);

      assertEquals(
          errorResponse.getString("errorCode"),
          NDSErrorCode.CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_NOT_FOUND.name());
      assertTrue(errorResponse.getString("message").contains(id.toHexString()));
    }

    final ObjectId id =
        new ObjectId(
            initialServiceAccount.getString(
                NDSCloudProviderAccessGCPServiceAccountView.FieldDefs.ROLE_ID));

    // FAIL - GCP exception
    mockGCPApiSvc(true, false);
    {
      final JSONObject response =
          doAuthedJsonDelete(
              _user,
              String.format("%s/%s/%s", path, CloudProvider.GCP.name(), id),
              HttpStatus.SC_INTERNAL_SERVER_ERROR);
      assertEquals(CommonErrorCode.SERVER_ERROR.name(), response.getString("errorCode"));
    }

    // SUCCESS
    mockGCPApiSvc(true, true);
    {
      doAuthedJsonDelete(
          _user,
          String.format("%s/%s/%s", path, CloudProvider.GCP.name(), id),
          HttpStatus.SC_NO_CONTENT);

      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s", _group.getId(), CloudProvider.GCP.name()),
              HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }
  }

  @Test
  public void testDeleteRoleFromCloudProviderAccess_InvalidCloudProvider() {
    setUpGroup(_group.getId(), false);
    final String path = String.format("/nds/%s/cloudProviderAccess", _group.getId());

    // FAIL - invalid cloud provider
    {
      final ObjectId id = ObjectId.get();
      final JSONObject errorResponse =
          doAuthedJsonDelete(
              _user,
              String.format("%s/%s/%s", path, "invalid_provider", id),
              HttpStatus.SC_BAD_REQUEST);

      assertEquals(
          errorResponse.getString("errorCode"), NDSErrorCode.INVALID_CLOUD_PROVIDER.name());
    }
  }

  @Test
  public void testGetAtlasAccountDetails() {
    setUpGroup(_group.getId(), false);
    setUpGroup(_cnRegionsOnlyGroup.getId(), true);

    // FAILURE - unsupported cloud provider (GCP)
    {
      doAuthedJsonGet(
          _user,
          String.format(
              "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
              _group.getId(), CloudProvider.GCP.name()),
          HttpStatus.SC_BAD_REQUEST);
    }

    // FAILURE - unsupported cloud provider (FREE)
    {
      doAuthedJsonGet(
          _user,
          String.format(
              "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
              _group.getId(), CloudProvider.FREE.name()),
          HttpStatus.SC_BAD_REQUEST);
    }

    // SUCCESS (Azure)
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
                  _group.getId(), CloudProvider.AZURE.name()),
              HttpStatus.SC_OK);

      final Pattern atlasAppIdPattern =
          Pattern.compile("^\\p{XDigit}{8}(-\\p{XDigit}{4}){3}-\\p{XDigit}{12}$");
      final String atlasAzureAppId = response.getString("atlasAzureAppId");
      assertTrue(atlasAppIdPattern.matcher(atlasAzureAppId).matches());
    }

    // SUCCESS
    {
      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
                  _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      final Pattern externalIDPattern =
          Pattern.compile("^\\p{XDigit}{8}(-\\p{XDigit}{4}){3}-\\p{XDigit}{12}$");
      final Pattern atlasAwsIamRootAccountArnPattern = ValidationUtils.VALID_AWS_IAM_ROOT;

      assertTrue(
          externalIDPattern.matcher(response.getString("atlasAssumedRoleExternalId")).matches());
      assertTrue(
          atlasAwsIamRootAccountArnPattern
              .matcher(response.getString("atlasAWSAccountArn"))
              .matches());
      assertEquals("arn:aws:iam::************:root", response.getString("atlasAWSAccountArn"));
    }

    // SUCCESS - accessed through read-only admin user
    {
      doAuthedJsonGet(
          _readOnlyAdminUser,
          String.format(
              "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
              _group.getId(), CloudProvider.AWS.name()),
          HttpStatus.SC_OK);
    }

    // SUCCESS - CN regions-only group
    {
      final JSONObject response =
          doAuthedJsonGet(
              _cnRegionsOnlyUser,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
                  _cnRegionsOnlyGroup.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      final Pattern externalIDPattern =
          Pattern.compile("^\\p{XDigit}{8}(-\\p{XDigit}{4}){3}-\\p{XDigit}{12}$");
      final Pattern atlasAwsIamRootAccountArnPattern = ValidationUtils.VALID_AWS_IAM_ROOT;

      assertTrue(
          externalIDPattern.matcher(response.getString("atlasAssumedRoleExternalId")).matches());
      assertTrue(
          atlasAwsIamRootAccountArnPattern
              .matcher(response.getString("atlasAWSAccountArn"))
              .matches());
      assertEquals("arn:aws-cn:iam::************:root", response.getString("atlasAWSAccountArn"));
    }

    // TODO<CLOUDP-83539>: Uncomment the lines of code below when AWSAccountDao::
    // findCloudProviderAccessAccount has been updated to take Gov regions-only groups into account
    // SUCCESS - NDS Gov enabled
    /*{
      _appSettings.setProp(AppSettings.Fields.NDS_GOV_US_ENABLED.value, "true", SettingType.MEMORY);

      final JSONObject response =
          doAuthedJsonGet(
              _user,
              String.format(
                  "/nds/%s/cloudProviderAccess/%s/atlasAccountDetails",
                  _group.getId(), CloudProvider.AWS.name()),
              HttpStatus.SC_OK);

      final Pattern externalIDPattern =
          Pattern.compile("^\\p{XDigit}{8}(-\\p{XDigit}{4}){3}-\\p{XDigit}{12}$");
      final Pattern atlasAwsIamRootAccountArnPattern = ValidationUtils.VALID_AWS_IAM_ROOT;

      assertTrue(
          externalIDPattern.matcher(response.getString("atlasAssumedRoleExternalId")).matches());
      assertTrue(
          atlasAwsIamRootAccountArnPattern
              .matcher(response.getString("atlasAWSAccountArn"))
              .matches());
      assertEquals(
          "arn:aws-us-gov:iam::************:root", response.getString("atlasAWSAccountArn"));

      _appSettings.setProp(
          AppSettings.Fields.NDS_GOV_US_ENABLED.value, "false", SettingType.MEMORY);
    }*/
  }

  private void assertCloudProviderAWSIAMRoleCreation(final JSONObject pResult) {
    assertEquals(
        "arn:aws:iam::************:root",
        pResult.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ATLAS_AWS_ACCOUNT_ARN));
    assertNotEquals(
        JSONObject.NULL,
        pResult.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.CREATED_DATE));
    assertNotEquals(
        JSONObject.NULL, pResult.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ROLE_ID));
    assertNotEquals(
        JSONObject.NULL,
        pResult.getString(
            NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ATLAS_ASSUMED_ROLE_EXTERNAL_ID));

    // not yet validated should be null
    assertEquals(
        JSONObject.NULL,
        pResult.get(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.AUTHORIZED_DATE));
    assertEquals(
        JSONObject.NULL,
        pResult.get(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.IAM_ASSUMED_ROLE_ARN));
  }

  private void assertCloudProviderAWSIAMUpdate(
      final String pExpectedARN, final JSONObject pResult) {
    assertEquals(
        "arn:aws:iam::************:root",
        pResult.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ATLAS_AWS_ACCOUNT_ARN));
    assertNotEquals(
        JSONObject.NULL,
        pResult.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.CREATED_DATE));
    assertNotEquals(
        JSONObject.NULL, pResult.getString(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ROLE_ID));
    assertNotEquals(
        JSONObject.NULL,
        pResult.getString(
            NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.ATLAS_ASSUMED_ROLE_EXTERNAL_ID));

    //  validated should not be null
    assertNotEquals(
        JSONObject.NULL,
        pResult.get(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.AUTHORIZED_DATE));
    assertEquals(
        pExpectedARN,
        pResult.get(NDSCloudProviderAccessAWSIAMRoleView.FieldDefs.IAM_ASSUMED_ROLE_ARN));
  }

  private void setUpGroup(final ObjectId pGroupId, final boolean pUseCNRegionsOnly) {
    _ndsGroupSvc.create(pGroupId, new NDSManagedX509(), pUseCNRegionsOnly);
  }

  private void testFeatureUsages(final JSONArray pFeatureUsagesResponse) {
    assertEquals(2, pFeatureUsagesResponse.length());

    // encryption at rest
    {
      final JSONObject featureUsage = pFeatureUsagesResponse.getJSONObject(0);
      assertEquals(
          NDSCloudProviderAccessFeatureUsageType.ENCRYPTION_AT_REST.name(),
          featureUsage.getString(NDSCloudProviderAccessFeatureUsageView.FieldDefs.FEATURE_TYPE));

      assertEquals(
          JSONObject.NULL,
          featureUsage.get(NDSCloudProviderAccessFeatureUsageView.FieldDefs.FEATURE_ID));
    }

    // data lake
    {
      final JSONObject featureUsage = pFeatureUsagesResponse.getJSONObject(1);
      assertEquals(
          NDSCloudProviderAccessFeatureUsageType.ATLAS_DATA_LAKE.name(),
          featureUsage.getString(NDSCloudProviderAccessFeatureUsageView.FieldDefs.FEATURE_TYPE));

      final JSONObject featureId =
          featureUsage.getJSONObject(NDSCloudProviderAccessFeatureUsageView.FieldDefs.FEATURE_ID);

      assertEquals(
          _group.getId().toHexString(),
          featureId.getString(
              NDSCloudProviderAccessFeatureUsageDataLakeFeatureIdView.FieldDefs.GROUP_ID));

      assertEquals(
          DATA_LAKE_NAME,
          featureId.get(NDSCloudProviderAccessFeatureUsageDataLakeFeatureIdView.FieldDefs.NAME));
    }
  }

  private void createGroupWithCloudProviderAccess(
      final Set<String> pIamAssumedRoleArns,
      final Set<String> pServicePrincipalIds,
      final Set<String> pServiceAccounts) {
    final ObjectId groupId = _group.getId();
    final List<NDSCloudProviderAccessAWSIAMRole> awsIamRoles =
        pIamAssumedRoleArns.stream()
            .map(
                arn ->
                    NDSCloudProviderAccessAWSIAMRole.builder()
                        .roleId(ObjectId.get())
                        .iamAssumedRoleArn(arn)
                        .featureUsages(
                            List.of(
                                new NDSCloudProviderAccessEncryptionAtRestFeatureUsage(),
                                new NDSCloudProviderAccessDataLakeFeatureUsage(
                                    new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                                        groupId, DATA_LAKE_NAME))))
                        .atlasAWSAccountId(ObjectId.get())
                        .atlasAWSAccountArn("arn:aws:iam::************:role/atlas-arn")
                        .atlasAssumedRoleExternalId(UUID.randomUUID().toString())
                        .createdDate(Date.from(Instant.EPOCH))
                        .authorizedDate(Date.from(Instant.EPOCH))
                        .build())
            .collect(Collectors.toList());

    final List<NDSCloudProviderAccessAzureServicePrincipal> azureServicePrincipals =
        pServicePrincipalIds.stream()
            .map(
                servicePrincipalId ->
                    NDSCloudProviderAccessAzureServicePrincipal.builder()
                        .servicePrincipalId(servicePrincipalId)
                        .azureSubscriptionId(ObjectId.get())
                        .tenantId("tenant-id")
                        .atlasAzureAppId("azure-app-id")
                        .featureUsages(
                            List.of(
                                new NDSCloudProviderAccessEncryptionAtRestFeatureUsage(),
                                new NDSCloudProviderAccessDataLakeFeatureUsage(
                                    new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                                        groupId, DATA_LAKE_NAME))))
                        .createdDate(Date.from(Instant.EPOCH))
                        .build())
            .collect(Collectors.toList());

    final List<NDSCloudProviderAccessGCPServiceAccount> gcpServiceAccounts =
        pServiceAccounts.stream()
            .map(
                serviceAccount ->
                    NDSCloudProviderAccessGCPServiceAccount.builder()
                        .roleId(ObjectId.get())
                        .gcpServiceAccountForAtlas(serviceAccount)
                        .gcpContainerId(ObjectId.get())
                        .featureUsages(
                            List.of(
                                new NDSCloudProviderAccessEncryptionAtRestFeatureUsage(),
                                new NDSCloudProviderAccessDataLakeFeatureUsage(
                                    new NDSCloudProviderAccessFeatureUsageDataLakeFeatureId(
                                        groupId, DATA_LAKE_NAME))))
                        .createdDate(Date.from(Instant.EPOCH))
                        .build())
            .collect(Collectors.toList());

    setUpGroup(groupId, false);
    _groupDao.updateCloudProviderAccess(
        groupId,
        new NDSCloudProviderAccess(awsIamRoles, azureServicePrincipals, gcpServiceAccounts));
  }

  private void mockAWSApiSvc() throws IllegalAccessException, NoSuchFieldException {
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    doReturn(mock(Credentials.class))
        .when(awsApiSvc)
        .assumeRole(
            any(ObjectId.class), any(), any(), isNotNull(), any(), any(), any(), any(), any());
    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(awsApiSvc)
        .assumeRole(any(ObjectId.class), any(), any(), isNull(), any(), any(), any(), any(), any());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_awsApiSvc", awsApiSvc);
  }

  private void mockAzureApiSvc(final boolean pSuccess)
      throws IllegalAccessException, NoSuchFieldException {
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);
    if (!pSuccess) {
      doThrow(new AzureApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(azureApiSvc)
          .authenticateServicePrincipalForCloudProviderAccess(any(), any(), any());
    }
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_azureApiSvc", azureApiSvc);
  }

  private void mockGCPApiSvc(final boolean pServiceAccountCreationSuccess)
      throws IllegalAccessException, NoSuchFieldException {
    mockGCPApiSvc(pServiceAccountCreationSuccess, true);
  }

  private void mockGCPApiSvc(
      final boolean pServiceAccountCreationSuccess, final boolean pServiceAccountDeletionSuccess)
      throws IllegalAccessException, NoSuchFieldException {
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);
    final ServiceAccount serviceAccount =
        ServiceAccount.newBuilder()
            .setEmail("<EMAIL>")
            .build();
    if (!pServiceAccountCreationSuccess) {
      doThrow(new GCPApiException(GCPErrorCode.BACKEND_ERROR))
          .when(gcpApiSvc)
          .createServiceAccountInProject(any(), any(), any());
    } else {
      doReturn(serviceAccount).when(gcpApiSvc).createServiceAccountInProject(any(), any(), any());
    }
    if (!pServiceAccountDeletionSuccess) {
      doThrow(new GCPApiException(GCPErrorCode.BACKEND_ERROR))
          .when(gcpApiSvc)
          .deleteServiceAccountFromProject(any(), any());
    }
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_gcpApiSvc", gcpApiSvc);
  }
}
