package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.google.api.gax.paging.Page;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.Storage.BlobListOption;
import com.google.cloud.storage.StorageException;
import com.google.cloud.storage.StorageOptions;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._public.model.DataLandingZoneConfig.GCPDataLandingZoneConfig;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.CollectionType;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.NDSLocalSecretsManager;
import com.xgen.svc.nds.gcp.GCPTestConstants;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.dataLake.OnlineArchiveGCPCredentialsView.FieldDefs;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.util.OnlineArchiveBucketsUtil;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(GuiceTestExtension.class)
public class OnlineArchiveConfResourceGcpExternalIntTests extends JUnit5BaseResourceTest {
  private static final Logger LOG =
      LoggerFactory.getLogger(OnlineArchiveConfResourceGcpExternalIntTests.class);

  private static final String BASE_URL = "/conf/onlinearchive";
  private static final String CONFIGURE_URL = BASE_URL + "/%s/cluster/%s?ah=%s&sk=%s&av=%s";
  private static final String GET_DLZ_BUCKET_CREDENTIALS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/dataLandingZoneBucketCredentials?ah=%s&sk=%s";

  private static final String GROUP_NAME = "testGroup";
  private static final String GCP_CLUSTER_NAME = "gcpTestCluster";
  private static final String AGENT_VERSION = "1.0";
  private static final String GCP_SESSION_KEY = "sk1";
  private static final String GCP_HOSTNAME = "hostname1";

  private static final String GCP_TEST_DLZ_BUCKET_NAME = "online-archive-dlz-int-tests";
  private static final String GCP_TEST_DLZ_BUCKET_NAME_2 = "online-archive-dlz-int-tests-2";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private OnlineArchiveSvc _onlineArchiveSvc;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private OrganizationSvc _orgSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private AppSettings _appSettings;
  @Inject private GCPOrganizationDao _gcpOrganizationDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private OnlineArchiveBucketsUtil _onlineArchiveBucketsUtil;
  @Inject private NDSLocalSecretsManager _ndsLocalSecretsManager;

  private Group _group;
  private AgentApiKey _gcpAgentApiKey;
  private ClusterDescription _gcpClusterDescription;
  private GCPRegionName _gcpRegion;
  private String _gcpClientCertificateFile;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();

    _group = MmsFactory.createGroupWithNDSPlan(GROUP_NAME);
    final Organization org = _orgSvc.findById(_group.getOrgId());
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    final AppUser appUser = MmsFactory.createGroupOwnerUser(_group);

    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, org, FeatureFlag.ONLINE_ARCHIVE_GCP);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findDraftOrEmpty(_group.getId(), appUser.getId());
    final Auth auth = automationConfig.getDeployment().getAuth();
    auth.setAutoUser("username");
    auth.setAutoPwd("password");
    _automationConfigSvc.saveDraft(automationConfig, appUser, org, _group);
    _automationConfigSvc.publish(org, _group, appUser);
    _dataLakeTestUtils.setUp();

    _gcpRegion = GCPRegionName.CENTRAL_US;

    _gcpAgentApiKey = MmsFactory.generateApiKey(_group.getId(), new ObjectId());
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getGCPClusterDescription(_group.getId(), GCP_CLUSTER_NAME);
    clusterDescription.append(ClusterDescription.FieldDefs.UNIQUE_ID, ObjectId.get());
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);
    _gcpClusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), GCP_CLUSTER_NAME).orElseThrow();

    // Create instance hardware. This is required for AgentApiCall authorization testing.
    final GCPCloudProviderContainer gcpContainer =
        new GCPCloudProviderContainer(NDSModelTestFactory.getGCPContainer());
    final NDSGroup ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final ObjectId containerId = _ndsGroupSvc.addCloudContainer(ndsGroup, gcpContainer);

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(GCP_CLUSTER_NAME, _group.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
                ndsGroup.getGroupId(), GCP_CLUSTER_NAME, GCP_CLUSTER_NAME, List.of())
            .next()
            .rsId(),
        true,
        false,
        ObjectId.get());
    final GCPInstanceHardware gcpInstanceHardware =
        new GCPInstanceHardware(
            InstanceHardware.getEmptyHardware(CloudProvider.GCP, ObjectId.get(), new Date(), 0)
                .append(InstanceHardware.FieldDefs.CLOUD_PROVIDER_CONTAINER_ID, containerId)
                .append(
                    InstanceHardware.FieldDefs.HOSTNAMES,
                    new Hostnames(GCP_HOSTNAME, GCP_HOSTNAME).toDBList())
                .append(InstanceHardware.FieldDefs.PROVISIONED, true)
                .append(
                    InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                    InstanceHostname.HostnameScheme.PUBLIC.name())
                .append(InstanceHardware.FieldDefs.PUBLIC_IP, getGCPExternalIpAddress()));

    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), List.of(gcpInstanceHardware), false));

    // Only AWS Secrets are required. Secrets for using an authorized GCP Service Key are fetched
    // from AWS SecretsManager using AWS Credentials.
    final JSONObject secrets =
        _ndsLocalSecretsManager.getAWSSecret(GCPTestConstants.GCP_COMMON_SECRETS);

    try {
      _gcpClientCertificateFile = secrets.getString("clientCertificateFile");
    } catch (final Exception e) {
      LOG.warn(
          "Populate AWS Secrets in server/conf/conf-test-secure.properties to fetch GCP Secrets",
          e);
      throw e;
    }

    final GCPOrganization gcpOrganization =
        GCPOrganization.builder()
            .credentials(_gcpClientCertificateFile)
            .forOnlineArchiveDataLandingZone(true)
            .build();
    _gcpOrganizationDao.save(gcpOrganization);
  }

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testGetArchiveDataLandingZoneBucketGCPCredentials() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(GCP_CLUSTER_NAME);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    final OnlineArchive createdOnlineArchive =
        _onlineArchiveDao.find(onlineArchive.getId()).orElseThrow();

    final String pathPrefix =
        _group.getId().toHexString() + "/" + onlineArchive.getId().toHexString();

    // Manually override DLZ config to point to the test bucket rather than the real DLZ for this
    // region. The path prefix and region should stay the same.
    assertEquals(
        pathPrefix,
        createdOnlineArchive.getDataLandingZoneConfig().get().getPathPrefix(),
        "DLZ path prefix does not match");
    _onlineArchiveDao.updateDataLandingZoneConfig(
        onlineArchive.getId(),
        new GCPDataLandingZoneConfig(_gcpRegion, pathPrefix, GCP_TEST_DLZ_BUCKET_NAME));

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _gcpAgentApiKey,
        String.format(
            CONFIGURE_URL,
            _group.getId(),
            GCP_CLUSTER_NAME,
            GCP_HOSTNAME,
            GCP_SESSION_KEY,
            AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // Request the credentials.
    final JSONObject response =
        new JSONObject(
            doAgentApiCallGet(
                _group,
                _gcpAgentApiKey,
                String.format(
                    GET_DLZ_BUCKET_CREDENTIALS_URL,
                    _group.getId(),
                    GCP_CLUSTER_NAME,
                    onlineArchive.getId(),
                    GCP_HOSTNAME,
                    GCP_SESSION_KEY)));

    String tokenValue = response.getString(FieldDefs.TOKEN_VALUE);
    assertNotNull((tokenValue));
    assertNotNull(response.getString(FieldDefs.EXPIRATION_DATE));

    // Only paths starting with the projectID + archiveID should be authorized.
    String authorizedPath = pathPrefix + "/testGetArchiveDataLandingZoneBucketGCPCredentials.txt";
    String blobContent = "some-tremendous-dlz-content";

    AccessToken token = AccessToken.newBuilder().setTokenValue(tokenValue).build();
    GoogleCredentials credentials = GoogleCredentials.create(token);

    // NOTE: GCP_TEST_DLZ_BUCKET_NAME is configured with a lifecycle policy to delete all these
    // test files after 3 days.
    GCPApiSvc.uploadBlob(
        credentials, GCP_TEST_DLZ_BUCKET_NAME, authorizedPath, blobContent.getBytes());
    byte[] readBackContent =
        GCPApiSvc.readBlob(credentials, GCP_TEST_DLZ_BUCKET_NAME, authorizedPath);
    assertEquals(blobContent, new String(readBackContent));

    List<String> listedBlobs = listBlobNames(credentials, GCP_TEST_DLZ_BUCKET_NAME, pathPrefix);
    assertEquals(1, listedBlobs.size());
    assertEquals(authorizedPath, listedBlobs.get(0));

    // Cleanup.
    GCPApiSvc.deleteBlob(credentials, GCP_TEST_DLZ_BUCKET_NAME, authorizedPath);

    // Test an unauthorized path in an authorized bucket (random object IDs).
    String unauthorizedPath = "16685e18cb976405645c10f5/16685ed032bba22123667d79/unauthorized.txt";
    try {
      GCPApiSvc.uploadBlob(
          credentials, GCP_TEST_DLZ_BUCKET_NAME, unauthorizedPath, blobContent.getBytes());
      fail();
    } catch (final StorageException pE) {
      assertEquals(403, pE.getCode());
    }

    // Test an authorized path in an unauthorized bucket that exists.
    try {
      GCPApiSvc.uploadBlob(
          credentials, GCP_TEST_DLZ_BUCKET_NAME_2, authorizedPath, blobContent.getBytes());
      fail();
    } catch (final StorageException pE) {
      assertEquals(403, pE.getCode());
    }
  }

  private OnlineArchive getOnlineArchive(final String clusterName) {
    final String dbName = "mydb";
    final UUID collectionUUID = new UUID(1L, 1L);
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("string", "field1", 0),
            new PartitionField("string", "field2", 1),
            new PartitionField("date", "dateField", 2));

    return new OnlineArchive.Builder()
        .setArchiveId(ObjectId.get())
        .setClusterId(_group.getId(), clusterName)
        .setDbName(dbName)
        .setCollName("mycoll")
        .setCollectionUUID(collectionUUID)
        .setPartitionFields(partitionFields)
        .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
        .setState(OnlineArchive.State.ACTIVE)
        .setCollectionType(CollectionType.STANDARD)
        .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
        .setDataProcessRegion(
            new OnlineArchive.DataProcessRegion(CloudProvider.PROVIDER_GCP, _gcpRegion.name()))
        .build();
  }

  private String getGCPExternalIpAddress() {
    try {
      return HttpUtils.getInstance().doGetStr("http://checkip.amazonaws.com/").trim();
    } catch (Exception e) {
      return "Unknown";
    }
  }

  private List<String> listBlobNames(
      GoogleCredentials credentials, String bucketName, String pathPrefix) {
    Storage storage = StorageOptions.newBuilder().setCredentials(credentials).build().getService();
    Page<Blob> blobs = storage.list(bucketName, BlobListOption.prefix(pathPrefix));

    List<String> blobNames = new ArrayList<>();
    for (Blob blob : blobs.iterateAll()) {
      blobNames.add(blob.getName());
    }
    return blobNames;
  }
}
