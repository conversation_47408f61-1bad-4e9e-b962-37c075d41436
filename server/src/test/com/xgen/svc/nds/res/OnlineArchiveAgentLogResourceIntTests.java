package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import java.util.Date;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;

public class OnlineArchiveAgentLogResourceIntTests extends JUnit5BaseResourceTest {

  private Group _group;
  private AppUser _user;
  private AgentApiKey _agentApiKey;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(organization, "cus_0001");
    _user = MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), _user.getId());
  }

  @Test
  public void postBatch() throws Exception {
    final long secondTimestamp = System.currentTimeMillis();
    final long firstTimestamp = secondTimestamp - 1000L;

    final JSONObject withoutThrowable =
        new JSONObject()
            .put("timestamp", firstTimestamp)
            .put("level", "info")
            .put("thread", "file.go:123")
            .put("logger", "onlinearchivemodule")
            .put("message", "Making conf call")
            .put("throwable", "");
    final JSONObject withThrowable =
        new JSONObject()
            .put("timestamp", secondTimestamp)
            .put("level", "error")
            .put("thread", "util.go:456")
            .put("logger", "onlinearchivemodule")
            .put("message", "Error doing restore")
            .put("throwable", "at onlinearchive/stack1.go\n  at onlinearchive/stack2.go");

    final JSONArray jsonArray = new JSONArray().put(withoutThrowable).put(withThrowable);

    final String postEndpoint =
        String.format("/agents/onlinearchive/logs/%s/batch", _group.getId());
    assertEquals("{}", doAgentApiCallPost(_group, _agentApiKey, postEndpoint, jsonArray));

    final String uiGetEndpoint = String.format("/agents/onlinearchive/logs/%s", _group.getId());
    final JSONObject response = doAuthedJsonGet(_user, uiGetEndpoint);
    final String debugStr = response.toString();

    final JSONArray actualEntries = response.getJSONArray("entries");
    assertEquals(
        2, actualEntries.length(), "Unexpected online archive module logs response: " + debugStr);

    // results in descending timestamp order
    final JSONObject firstEntry = actualEntries.getJSONObject(0);
    // response includes groupId and _id
    assertNotNull(firstEntry.remove("_id"));
    assertNotNull(firstEntry.remove("groupId"));

    // response has timestamp string, and does "threw" instead of "throwable"
    withThrowable.put("timestamp", new Date(withThrowable.getLong("timestamp")).toString());
    withThrowable.put("threw", withThrowable.remove("throwable"));
    JSONAssert.assertEquals(withThrowable, firstEntry, true);

    final JSONObject secondEntry = actualEntries.getJSONObject(1);
    // response includes groupId and _id
    assertNotNull(secondEntry.remove("_id"));
    assertNotNull(secondEntry.remove("groupId"));

    // response has timestamp string, and does "threw" instead of "throwable"
    withoutThrowable.put("timestamp", new Date(withoutThrowable.getLong("timestamp")).toString());
    withoutThrowable.put("threw", withoutThrowable.remove("throwable"));
    JSONAssert.assertEquals(withoutThrowable, secondEntry, true);
  }
}
