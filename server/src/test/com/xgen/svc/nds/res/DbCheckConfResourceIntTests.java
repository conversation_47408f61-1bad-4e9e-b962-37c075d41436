package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.dao.codec._public.encrypted.string.EncryptedString;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.dbcheck._private.dao.DbCheckDao;
import com.xgen.cloud.nds.dbcheck._public.model.BatchWriteConcernOptions;
import com.xgen.cloud.nds.dbcheck._public.model.ClusterInfo;
import com.xgen.cloud.nds.dbcheck._public.model.DbCheck;
import com.xgen.cloud.nds.dbcheck._public.model.FailureView;
import com.xgen.cloud.nds.dbcheck._public.model.IncompleteValidationView;
import com.xgen.cloud.nds.dbcheck._public.model.InconsistencyType;
import com.xgen.cloud.nds.dbcheck._public.model.InconsistencyView;
import com.xgen.cloud.nds.dbcheck._public.model.OperationStatus;
import com.xgen.cloud.nds.dbcheck._public.model.Options;
import com.xgen.cloud.nds.dbcheck._public.model.Options.OptionLimitDefinitions;
import com.xgen.cloud.nds.dbcheck._public.model.RunStatus;
import com.xgen.cloud.nds.dbcheck._public.model.ShardStatus;
import com.xgen.cloud.nds.dbcheck._public.model.ValidationMode;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatus;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatus.HostLevelStatusId;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatusAgentGetView;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatusLastObservedView;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatusPostView;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.MongoDBTimestamp;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.ShardLevelStatusAgentGetView;
import com.xgen.cloud.nds.dbcheck._public.svc.DbCheckSvc;
import com.xgen.cloud.nds.dbcheck._public.view.ShardStatusView;
import com.xgen.cloud.nds.dbcheck._public.view.agent.DbCheckConfView.FieldDefs;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionRunResult;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class DbCheckConfResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private DbCheckDao _dbCheckDao;
  @Inject private DbCheckSvc _dbCheckSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  private AppUser _user;
  private Group _group;
  private AgentApiKey _agentApiKey;
  private ObjectMapper _customObjectMapper;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createGroupOwnerUser(_group);
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), _user.getId());
    _customObjectMapper = CustomJacksonJsonProvider.createObjectMapper();
  }

  @Test
  public void testGetDbCheckConfig_initialCall() throws Exception {
    final String rsName = "shard-0";
    final DbCheck dbCheck = getTestDbCheck(null, getNewShardStatusesForRsNames(List.of("shard-0")));
    _dbCheckDao.addNewDbCheck(dbCheck);

    final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);
    assertEquals(dbCheck.getId(), new ObjectId(confResponse.getString(FieldDefs.ID_FIELD)));
    assertEquals(
        CorruptionDetectionRunResult.NOT_DONE,
        CorruptionDetectionRunResult.valueOf(confResponse.getString(FieldDefs.RESULT_FIELD)));
    assertEquals(JSONObject.NULL, confResponse.get(FieldDefs.LAST_STARTED_NAMESPACE_UUID_FIELD));
    assertEquals(JSONObject.NULL, confResponse.get(FieldDefs.LAST_COMPLETED_NAMESPACE_UUID_FIELD));
    assertEquals(JSONObject.NULL, confResponse.get(FieldDefs.LAST_STARTED_SERVER_DB_CHECK_TIME));

    // validate that default options are returned
    final JSONObject optionsResponse = confResponse.getJSONObject(FieldDefs.OPTIONS_FIELD);
    assertEquals(
        (int) Options.DEFAULT_MAX_BATCH_TIME_MS,
        optionsResponse.getInt(Options.FieldDefs.MAX_BATCH_TIME_MS));
    assertEquals(
        (int) Options.DEFAULT_MAX_DOCS_PER_BATCH,
        optionsResponse.getInt(Options.FieldDefs.MAX_DOCS_PER_BATCH));
    final JSONObject batchWriteConcernOption =
        optionsResponse.getJSONObject(Options.FieldDefs.BATCH_WRITE_CONCERN);
    assertEquals(
        Options.DEFAULT_BATCH_WRITE_CONCERN.getW(),
        batchWriteConcernOption.getString(BatchWriteConcernOptions.FieldDefs.W_FIELD));
    assertTrue(
        batchWriteConcernOption.getBoolean(BatchWriteConcernOptions.FieldDefs.JOURNAL_FIELD));
    assertEquals(
        (int) Options.DEFAULT_BATCH_WRITE_CONCERN.getWTimeout(),
        batchWriteConcernOption.getInt(BatchWriteConcernOptions.FieldDefs.W_TIMEOUT_FIELD));
    assertEquals(JSONObject.NULL, optionsResponse.get(Options.FieldDefs.MIN_KEY));
    assertEquals(JSONObject.NULL, optionsResponse.get(Options.FieldDefs.MAX_KEY));
    assertEquals(JSONObject.NULL, optionsResponse.get(Options.FieldDefs.MAX_COUNT));
    assertEquals(JSONObject.NULL, optionsResponse.get(Options.FieldDefs.MAX_SIZE));
    assertFalse(optionsResponse.has(DEPRECATED_FIELD_DEFS.MAX_COUNT_PER_SECOND));
    assertFalse(optionsResponse.has(DEPRECATED_FIELD_DEFS.MAX_BYTES_PER_BATCH));
    assertEquals(JSONObject.NULL, optionsResponse.get(Options.FieldDefs.SNAPSHOT_READ));
  }

  @Test
  public void testGetDbCheckConfig_secondaryCallWithLastStartTime() throws Exception {
    // Round to the nearest second to prevent the date from having more precision than the API
    // response
    final Date serverDbCheckStartDate = DateUtils.round(new Date(), Calendar.SECOND);
    final String rsName = "rs-0";
    final ShardStatus shardStatus =
        new ShardStatus(
            rsName,
            new Date(),
            new Date(),
            OperationStatus.WORKING,
            UUID.randomUUID(),
            UUID.randomUUID(),
            10,
            0,
            "",
            serverDbCheckStartDate,
            null,
            null);
    final DbCheck dbCheck = getTestDbCheck(null, List.of(shardStatus));
    _dbCheckDao.addNewDbCheck(dbCheck);

    final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);

    assertEquals(dbCheck.getId(), new ObjectId(confResponse.getString(FieldDefs.ID_FIELD)));
    assertEquals(
        CorruptionDetectionRunResult.NOT_DONE,
        CorruptionDetectionRunResult.valueOf(confResponse.getString(FieldDefs.RESULT_FIELD)));
    assertEquals(
        shardStatus.getLastStartedNamespaceUUID().toString(),
        confResponse.get(FieldDefs.LAST_STARTED_NAMESPACE_UUID_FIELD));
    assertEquals(
        shardStatus.getLastCompletedNamespaceUUID().toString(),
        confResponse.get(FieldDefs.LAST_COMPLETED_NAMESPACE_UUID_FIELD));
    assertEquals(
        shardStatus.getLastStartedServerDbCheckTime().toInstant().toString(),
        confResponse.get(FieldDefs.LAST_STARTED_SERVER_DB_CHECK_TIME));
    assertEquals(
        shardStatus.getOperationStatus().toString(),
        confResponse.get(FieldDefs.OPERATION_STATUS_FIELD));
  }

  @Test
  public void testGetDbCheckConfig_optionsSpecified() throws Exception {
    final String rsName = "shard-0";
    final Options optionsSpecified =
        new Options(
            "minKey",
            "maxKey",
            10,
            11,
            OptionLimitDefinitions.MAX_DOCS_PER_BATCH.getMinimumLimit(),
            OptionLimitDefinitions.MAX_BATCH_TIME_MS.getMinimumLimit(),
            false,
            new BatchWriteConcernOptions("majority", false, 16),
            null,
            null,
            null,
            null);
    final DbCheck dbCheck =
        getTestDbCheck(optionsSpecified, getNewShardStatusesForRsNames(List.of("shard-0")));
    _dbCheckDao.addNewDbCheck(dbCheck);

    final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);

    assertEquals(dbCheck.getId(), new ObjectId(confResponse.getString(FieldDefs.ID_FIELD)));
    assertEquals(
        CorruptionDetectionRunResult.NOT_DONE,
        CorruptionDetectionRunResult.valueOf(confResponse.getString(FieldDefs.RESULT_FIELD)));
    assertEquals(JSONObject.NULL, confResponse.get(FieldDefs.LAST_STARTED_NAMESPACE_UUID_FIELD));
    assertEquals(JSONObject.NULL, confResponse.get(FieldDefs.LAST_COMPLETED_NAMESPACE_UUID_FIELD));

    // validate that specified options are returned
    final JSONObject optionsResponse = confResponse.getJSONObject(FieldDefs.OPTIONS_FIELD);
    assertEquals(
        (int) optionsSpecified.getMaxBatchTimeMillis(),
        optionsResponse.getInt(Options.FieldDefs.MAX_BATCH_TIME_MS));
    assertEquals(
        (int) optionsSpecified.getMaxDocsPerBatch(),
        optionsResponse.getInt(Options.FieldDefs.MAX_DOCS_PER_BATCH));
    assertEquals(
        optionsSpecified.getMinKey(), optionsResponse.getString(Options.FieldDefs.MIN_KEY));
    assertEquals(
        optionsSpecified.getMaxKey(), optionsResponse.getString(Options.FieldDefs.MAX_KEY));
    assertEquals(
        (int) optionsSpecified.getMaxCount(), optionsResponse.getInt(Options.FieldDefs.MAX_COUNT));
    assertEquals(
        (int) optionsSpecified.getMaxSize(), optionsResponse.getInt(Options.FieldDefs.MAX_SIZE));
    assertEquals(
        optionsSpecified.getSnapshotRead(),
        optionsResponse.getBoolean(Options.FieldDefs.SNAPSHOT_READ));
    assertFalse(optionsResponse.has(DEPRECATED_FIELD_DEFS.MAX_COUNT_PER_SECOND));
    assertFalse(optionsResponse.has(DEPRECATED_FIELD_DEFS.MAX_BYTES_PER_BATCH));

    final JSONObject batchWriteConcernOption =
        optionsResponse.getJSONObject(Options.FieldDefs.BATCH_WRITE_CONCERN);
    assertEquals(
        optionsSpecified.getBatchWriteConcern().getW(),
        batchWriteConcernOption.getString(BatchWriteConcernOptions.FieldDefs.W_FIELD));
    assertEquals(
        optionsSpecified.getBatchWriteConcern().getJournal(),
        batchWriteConcernOption.get(BatchWriteConcernOptions.FieldDefs.JOURNAL_FIELD));
    assertEquals(
        (int) optionsSpecified.getBatchWriteConcern().getWTimeout(),
        batchWriteConcernOption.getInt(BatchWriteConcernOptions.FieldDefs.W_TIMEOUT_FIELD));
  }

  @Test
  public void testGetDbCheckConfig_agentChecks() throws Exception {
    final String rsName = "shard-0";

    // Null opts should be translated to their default when generating the view.
    // skip agent checks -> false
    // replication lag -> 30s
    {
      final DbCheck dbCheck =
          getTestDbCheck(null, getNewShardStatusesForRsNames(List.of("shard-0")), null, null);
      _dbCheckDao.addNewDbCheck(dbCheck);

      final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);
      assertFalse((Boolean) confResponse.get(FieldDefs.SKIP_AGENT_CHECKS));
      assertEquals(
          DbCheck.DEFAULT_REPLICATION_LAG_THRESHOLD,
          confResponse.get(FieldDefs.REPLICATION_LAG_THRESHOLD));
    }

    // Options set are maintained
    {
      final DbCheck dbCheck =
          getTestDbCheck(null, getNewShardStatusesForRsNames(List.of("shard-0")), true, 55);
      _dbCheckDao.addNewDbCheck(dbCheck);

      final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);
      assertTrue((Boolean) confResponse.get(FieldDefs.SKIP_AGENT_CHECKS));
      assertEquals(55, confResponse.get(FieldDefs.REPLICATION_LAG_THRESHOLD));
    }
  }

  private JSONObject getDbCheckAgentViewJson(final String rsName, final DbCheck dbCheck)
      throws IOException {
    return new JSONObject(
        doAgentApiCallGet(
            _group,
            _agentApiKey,
            String.format(
                "/conf/dbcheck/%s/%s/%s?ah=%s&sk=%s&av=%s",
                _group.getId(), rsName, dbCheck.getId(), "hostname", "sk1", "1.0")));
  }

  @Test
  public void testGetDbCheckConfig_withImplicitValidationModes() throws IOException {
    final String rsName = "shard-0";
    final DbCheck dbCheck = getTestDbCheck(null, getNewShardStatusesForRsNames(List.of(rsName)));
    _dbCheckDao.addNewDbCheck(dbCheck);
    final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);
    final JSONArray validationModeJson = confResponse.getJSONArray(FieldDefs.VALIDATION_MODES);
    final List<ValidationMode> responseModes =
        validationModeJson.toList().stream()
            .map(x -> (String) x)
            .map(x -> ValidationMode.valueOf(x))
            .collect(Collectors.toUnmodifiableList());
    assertEquals(List.of(ValidationMode.DATA_CONSISTENCY), responseModes);
  }

  @Test
  public void testGetDbCheckConfig_withExplicitValidationModes() throws IOException {
    final String rsName = "shard-0";
    final DbCheck dbCheck =
        getTestDbCheck(
            null,
            getNewShardStatusesForRsNames(List.of(rsName)),
            List.of(
                ValidationMode.CATALOG_CONSISTENCY_CHECK,
                ValidationMode.EXTRA_INDEX_KEYS_CHECK,
                ValidationMode.DATA_CONSISTENCY,
                ValidationMode.DATA_CONSISTENCY_AND_MISSING_INDEX_KEYS_CHECK),
            null,
            null);
    _dbCheckDao.addNewDbCheck(dbCheck);
    final JSONObject confResponse = getDbCheckAgentViewJson(rsName, dbCheck);
    final JSONArray validationModeJson = confResponse.getJSONArray(FieldDefs.VALIDATION_MODES);
    final List<ValidationMode> responseModes =
        validationModeJson.toList().stream()
            .map(x -> (String) x)
            .map(x -> ValidationMode.valueOf(x))
            .collect(Collectors.toUnmodifiableList());
    final var expectedModes =
        List.of(
            ValidationMode.DATA_CONSISTENCY_AND_MISSING_INDEX_KEYS_CHECK,
            ValidationMode.CATALOG_CONSISTENCY_CHECK,
            ValidationMode.EXTRA_INDEX_KEYS_CHECK);
    assertEquals(expectedModes, responseModes);
  }

  @Test
  public void testGetDbCheckConfig_withValidationModesAndIndexNames() throws IOException {
    final String rsName0 = "shard-0";
    final String rsName1 = "shard-1";
    final int rs0_current_op_idx = 2;
    final int rs1_current_op_idx = 1;
    final String rs0_last_validated_index_name = "field_otherfield_1";
    final String rs1_last_validated_index_name = "otherfield_somenumber_1";
    final ShardStatus shard0Status =
        new ShardStatus(
            rsName0,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            rs0_current_op_idx,
            rs0_last_validated_index_name);
    final ShardStatus shard1Status =
        new ShardStatus(
            rsName1,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            rs1_current_op_idx,
            rs1_last_validated_index_name);
    final DbCheck dbCheck = getTestDbCheck(null, List.of(shard0Status, shard1Status));
    _dbCheckDao.addNewDbCheck(dbCheck);
    final JSONObject confResponse_shard0 = getDbCheckAgentViewJson(rsName0, dbCheck);
    assertEquals(
        rs0_current_op_idx, confResponse_shard0.getInt(FieldDefs.CURRENT_VALIDATION_MODE_INDEX));
    assertEquals(
        rs0_last_validated_index_name,
        confResponse_shard0.getString(FieldDefs.LAST_INDEX_VALIDATED));
    final JSONObject confResponse_shard1 = getDbCheckAgentViewJson(rsName1, dbCheck);
    assertEquals(
        rs1_current_op_idx, confResponse_shard1.getInt(FieldDefs.CURRENT_VALIDATION_MODE_INDEX));
    assertEquals(
        rs1_last_validated_index_name,
        confResponse_shard1.getString(FieldDefs.LAST_INDEX_VALIDATED));
  }

  private List<ShardStatus> getNewShardStatusesForRsNames(final List<String> pRsNames) {
    return pRsNames.stream()
        .map(
            n ->
                new ShardStatus(
                    n,
                    null,
                    null,
                    OperationStatus.NOT_STARTED,
                    null,
                    null,
                    null,
                    null,
                    (EncryptedString) null,
                    null,
                    null,
                    null))
        .collect(Collectors.toList());
  }

  @Test
  public void testPostOperationStats() throws Exception {
    // scenario: a new dbCheck with two shards.
    final String shard0_rsName = "shard-0";
    final String shard1_rsName = "shard-1";

    final DbCheck dbCheck =
        getTestDbCheck(null, getNewShardStatusesForRsNames(List.of(shard0_rsName, shard1_rsName)));
    _dbCheckDao.addNewDbCheck(dbCheck);
    assertEquals(2, dbCheck.getShardStatuses().size());
    assertEquals(RunStatus.NEW, dbCheck.getStatus());

    // first shard reports to mms as WORKING
    final UUID shard0_lastStarted_0 = UUID.randomUUID();
    final UUID shard0_lastCompleted_0 = UUID.randomUUID();
    final Integer shard0_evaluationSetSize_0 = 10;
    final Integer shard0_currentEvaluationIndex_0 = 5;
    final String shard0_currentOpMsg_0 =
        "Scanning namespace sample_weatherdata.data Scanning namespace sample_weatherdata.data:"
            + " 5000/10000 50%";
    final Date shard0_startDate = new Date();
    final JSONObject shard0_status0 =
        getShardStatus(
            shard0_rsName,
            OperationStatus.WORKING,
            shard0_lastStarted_0,
            shard0_lastCompleted_0,
            shard0_evaluationSetSize_0,
            shard0_currentEvaluationIndex_0,
            shard0_currentOpMsg_0,
            shard0_startDate);

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/opstats?ah=%s&sk=%s&av=%s",
            _group.getId(), shard0_rsName, dbCheck.getId(), "hostname", "sk1", "1.0"),
        shard0_status0);

    final DbCheck updatedCheck0 = _dbCheckDao.find(dbCheck.getId()).get();
    assertEquals(2, updatedCheck0.getShardStatuses().size());
    final Optional<ShardStatus> updatedShardStatus0 =
        updatedCheck0.getShardStatuses().stream()
            .filter(ss -> ss.getRsName().equals(shard0_rsName))
            .findFirst();
    final Optional<ShardStatus> updatedShardStatus1 =
        updatedCheck0.getShardStatuses().stream()
            .filter(ss -> ss.getRsName().equals(shard1_rsName))
            .findFirst();

    // assert top-level run status + all expected shard status values
    assertEquals(RunStatus.WORKING, updatedCheck0.getStatus());

    // shard-0
    assertTrue(updatedShardStatus0.isPresent());
    assertEquals(OperationStatus.WORKING, updatedShardStatus0.get().getOperationStatus());
    assertEquals(updatedShardStatus0.get().getLastStartedNamespaceUUID(), shard0_lastStarted_0);
    assertEquals(updatedShardStatus0.get().getLastCompletedNamespaceUUID(), shard0_lastCompleted_0);
    assertEquals(shard0_evaluationSetSize_0, updatedShardStatus0.get().getEvaluationSetSize());
    assertEquals(
        shard0_currentEvaluationIndex_0, updatedShardStatus0.get().getCurrentEvaluationIndex());
    assertEquals(
        shard0_currentOpMsg_0, updatedShardStatus0.get().getEncryptedCurrentOpMsg().getValue());
    assertEquals(shard0_startDate, updatedShardStatus0.get().getLastStartedServerDbCheckTime());

    // shard-1
    assertEquals(OperationStatus.NOT_STARTED, updatedShardStatus1.get().getOperationStatus());
    assertNull(updatedShardStatus1.get().getLastStartedNamespaceUUID());
    assertNull(updatedShardStatus1.get().getLastCompletedNamespaceUUID());
    assertNull(updatedShardStatus1.get().getEvaluationSetSize());
    assertNull(updatedShardStatus1.get().getCurrentEvaluationIndex());
    assertNull(updatedShardStatus1.get().getEncryptedCurrentOpMsg());
    assertNull(updatedShardStatus1.get().getLastStartedServerDbCheckTime());

    // second shard reports to mms as WORKING
    final UUID shard1_lastStarted_0 = UUID.randomUUID();
    final UUID shard1_lastCompleted_0 = UUID.randomUUID();
    final JSONObject shard1_status0 =
        getShardStatus(
            shard1_rsName,
            OperationStatus.WORKING,
            shard1_lastStarted_0,
            shard1_lastCompleted_0,
            10,
            9,
            "",
            null);

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/opstats?ah=%s&sk=%s&av=%s",
            _group.getId(), shard0_rsName, dbCheck.getId(), "hostname", "sk1", "1.0"),
        shard1_status0);

    final DbCheck updatedCheck1 = _dbCheckDao.find(dbCheck.getId()).get();
    assertEquals(2, updatedCheck1.getShardStatuses().size());
    final Optional<ShardStatus> updatedShardStatus1_1 =
        updatedCheck1.getShardStatuses().stream()
            .filter(ss -> ss.getRsName().equals(shard1_rsName))
            .findFirst();

    // just concerned with status transitions from here out
    assertTrue(updatedShardStatus1_1.isPresent());
    assertEquals(OperationStatus.WORKING, updatedShardStatus1_1.get().getOperationStatus());
    assertEquals(RunStatus.WORKING, updatedCheck1.getStatus());

    // first shard reports to mms as COMPLETE; reuse old UUIDs for brevity
    final JSONObject shard0_status1 =
        getShardStatus(
            shard0_rsName,
            OperationStatus.COMPLETE,
            shard0_lastStarted_0,
            shard0_lastCompleted_0,
            10,
            9,
            "",
            null);

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/opstats?ah=%s&sk=%s&av=%s",
            _group.getId(), shard0_rsName, dbCheck.getId(), "hostname", "sk1", "1.0"),
        shard0_status1);

    final DbCheck updatedCheck2 = _dbCheckDao.find(dbCheck.getId()).get();
    assertEquals(2, updatedCheck2.getShardStatuses().size());
    final Optional<ShardStatus> updatedShardStatus2 =
        updatedCheck2.getShardStatuses().stream()
            .filter(ss -> ss.getRsName().equals(shard0_rsName))
            .findFirst();

    assertTrue(updatedShardStatus2.isPresent());
    assertEquals(OperationStatus.COMPLETE, updatedShardStatus2.get().getOperationStatus());
    assertEquals(RunStatus.WORKING, updatedCheck2.getStatus());

    // second shard reports to mms as COMPLETE
    final JSONObject shard1_status1 =
        getShardStatus(
            shard1_rsName,
            OperationStatus.COMPLETE,
            shard0_lastStarted_0,
            shard0_lastCompleted_0,
            10,
            9,
            "",
            null);

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/opstats?ah=%s&sk=%s&av=%s",
            _group.getId(), shard0_rsName, dbCheck.getId(), "hostname", "sk1", "1.0"),
        shard1_status1);

    final DbCheck updatedCheck3 = _dbCheckDao.find(dbCheck.getId()).get();
    assertEquals(2, updatedCheck3.getShardStatuses().size());
    final Optional<ShardStatus> updatedShardStatus3 =
        updatedCheck2.getShardStatuses().stream()
            .filter(ss -> ss.getRsName().equals(shard0_rsName))
            .findFirst();

    assertTrue(updatedShardStatus3.isPresent());
    assertEquals(OperationStatus.COMPLETE, updatedShardStatus3.get().getOperationStatus());
  }

  private void callPostStartStopDates(
      final ObjectId pDbCheckId,
      final String pHostname,
      final HostLevelStatusLastObservedView pStatusView)
      throws IOException {

    final JSONObject viewAsJson =
        new JSONObject(_customObjectMapper.writeValueAsString(pStatusView));
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/hostObservedDbCheckStartStops?sk=%s&av=%s",
            _group.getId(), pDbCheckId.toHexString(), pHostname, "sk1", "1.0"),
        viewAsJson);
  }

  private HostLevelStatusLastObservedView getStartStopDates(
      final ObjectId pDbCheckId, final Set<String> pHostnames) throws IOException {
    return _customObjectMapper
        .readerFor(HostLevelStatusLastObservedView.class)
        .readValue(callGetStartStopDates(pDbCheckId, pHostnames));
  }

  private String callGetStartStopDates(final ObjectId pDbCheckId, final Set<String> pHostnames)
      throws IOException {
    final JSONObject viewAsJson =
        new JSONObject(
            _customObjectMapper.writeValueAsString(
                new ShardLevelStatusAgentGetView(pHostnames.stream().toList())));
    return doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/getCrossShardObservations?sk=%s&av=%s",
            _group.getId(), pDbCheckId.toHexString(), "sk1", "1.0"),
        viewAsJson);
  }

  @Test
  public void testPostHostObservedDbCheckStartStopsEndpoint() throws Exception {

    final DbCheck dbCheck = getTestDbCheck(null, getNewShardStatusesForRsNames(List.of("shard-0")));
    _dbCheckDao.addNewDbCheck(dbCheck);
    final ObjectId dbcheckId = dbCheck.getId();
    final String hostnameOne = "abcd";

    Calendar calendar = new GregorianCalendar();
    calendar.set(2024, Calendar.JANUARY, 20, 11, 30, 2);
    final Date lastObservedDbCheckStopDate = calendar.getTime();
    calendar.set(2024, Calendar.JANUARY, 20, 8, 20, 1);
    final Date lastObservedDbCheckStartDate = calendar.getTime();
    calendar.set(2024, Calendar.JANUARY, 20, 9, 32, 2);
    final Date lastObservedRestartableFailureDate = calendar.getTime();
    final MongoDBTimestamp lastObservedOptime = new MongoDBTimestamp(1234, 12345);

    final HostLevelStatusLastObservedView hostOneView =
        new HostLevelStatusLastObservedView(
            lastObservedDbCheckStopDate,
            lastObservedDbCheckStartDate,
            lastObservedOptime,
            lastObservedRestartableFailureDate);

    callPostStartStopDates(dbcheckId, hostnameOne, hostOneView);
    final List<HostLevelStatus> hostLevelStatus = _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);

    // fields are set in the db as expected (with seconds precision)
    assertEquals(
        lastObservedDbCheckStopDate.getTime(),
        hostLevelStatus.get(0).getLastObservedDbCheckStopDate().getTime(),
        1000);
    assertEquals(
        lastObservedDbCheckStartDate.getTime(),
        hostLevelStatus.get(0).getLastObservedDbCheckStartDate().getTime(),
        1000);
    assertEquals(lastObservedOptime, hostLevelStatus.get(0).getLastObservedOptime());
    assertEquals(
        lastObservedRestartableFailureDate.getTime(),
        hostLevelStatus.get(0).getLastObservedRestartableFailureDate().getTime(),
        1000);

    // Only three arguments are passed to endpoint.
    // Tests that the unchanged value retains its previous value
    calendar.set(2025, Calendar.JANUARY, 20, 8, 20, 1);
    final Date lastObservedDbCheckStartDateNew = calendar.getTime();
    calendar.set(2025, Calendar.JANUARY, 20, 9, 32, 2);
    final Date lastObservedRestartableFailureDateNew = calendar.getTime();

    final MongoDBTimestamp lastObservedOptimeNew = new MongoDBTimestamp(12345, 12);

    final HostLevelStatusLastObservedView hostOneView2 =
        new HostLevelStatusLastObservedView(
            null,
            lastObservedDbCheckStartDateNew,
            lastObservedOptimeNew,
            lastObservedRestartableFailureDateNew);

    callPostStartStopDates(dbcheckId, hostnameOne, hostOneView2);
    final List<HostLevelStatus> hostLevelStatus2 =
        _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);

    assertEquals(
        lastObservedDbCheckStopDate.getTime(),
        hostLevelStatus2.get(0).getLastObservedDbCheckStopDate().getTime(),
        1000);
    assertEquals(
        lastObservedDbCheckStartDateNew.getTime(),
        hostLevelStatus2.get(0).getLastObservedDbCheckStartDate().getTime(),
        1000);
    assertEquals(lastObservedOptimeNew, hostLevelStatus2.get(0).getLastObservedOptime());
    assertEquals(
        lastObservedRestartableFailureDateNew.getTime(),
        hostLevelStatus2.get(0).getLastObservedRestartableFailureDate().getTime(),
        1000);

    calendar.set(2025, Calendar.JANUARY, 20, 9, 32, 2);
    final Date now = calendar.getTime();

    // tests that calling the failure endpoint does not change any other fields in db other than
    // Failures and lastReportedDate
    callFailureEndpoint(dbcheckId, hostnameOne, new HostLevelStatusPostView(now, null, null, null));
    final List<HostLevelStatus> hostLevelStatus3 =
        _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);

    assertEquals(now.getTime(), hostLevelStatus3.get(0).getLastExported().getTime(), 1000);
    assertEquals(
        lastObservedDbCheckStopDate.getTime(),
        hostLevelStatus3.get(0).getLastObservedDbCheckStopDate().getTime(),
        1000);
    assertEquals(
        lastObservedDbCheckStartDateNew.getTime(),
        hostLevelStatus3.get(0).getLastObservedDbCheckStartDate().getTime(),
        1000);
    assertEquals(lastObservedOptimeNew, hostLevelStatus3.get(0).getLastObservedOptime());
    assertEquals(
        lastObservedRestartableFailureDateNew.getTime(),
        hostLevelStatus3.get(0).getLastObservedRestartableFailureDate().getTime(),
        1000);

    final HostLevelStatusLastObservedView hostOneViewNull =
        new HostLevelStatusLastObservedView(null, null, null, null);

    callPostStartStopDates(dbcheckId, hostnameOne, hostOneViewNull);
    final List<HostLevelStatus> hostLevelStatus4 =
        _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);
    assertEquals(now.getTime(), hostLevelStatus4.get(0).getLastExported().getTime(), 1000);
    assertEquals(
        lastObservedDbCheckStopDate.getTime(),
        hostLevelStatus4.get(0).getLastObservedDbCheckStopDate().getTime(),
        1000);
    assertEquals(
        lastObservedDbCheckStartDateNew.getTime(),
        hostLevelStatus4.get(0).getLastObservedDbCheckStartDate().getTime(),
        1000);
    assertEquals(lastObservedOptimeNew, hostLevelStatus4.get(0).getLastObservedOptime());
    assertEquals(
        lastObservedRestartableFailureDateNew.getTime(),
        hostLevelStatus4.get(0).getLastObservedRestartableFailureDate().getTime(),
        1000);
  }

  private void callFailureEndpoint(
      final ObjectId pDbCheckId, final String pHostname, final HostLevelStatusPostView pStatusView)
      throws IOException {

    final JSONObject viewAsJson =
        new JSONObject(_customObjectMapper.writeValueAsString(pStatusView));
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/hoststats?sk=%s&av=%s",
            _group.getId(), pDbCheckId.toHexString(), pHostname, "sk1", "1.0"),
        viewAsJson);
  }

  @Test
  public void testPostFailuresAndLastExportTimestamp() throws Exception {
    final DbCheck dbCheck = getTestDbCheck(null, getNewShardStatusesForRsNames(List.of("shard-0")));
    _dbCheckDao.addNewDbCheck(dbCheck);
    final ObjectId dbcheckId = dbCheck.getId();

    final String fakeHostnameOne = "abcd";

    final String namespace1 = "namespace1";
    final FailureView hostOneFailureOne = new FailureView(namespace1, "failure message here");
    final FailureView hostOneFailureTwo =
        new FailureView(namespace1, "this is not a great collection");
    final InconsistencyView hostOneInconsistency =
        new InconsistencyView(
            namespace1, "inconsistency message here", InconsistencyType.REPLICA_SET_INCONSISTENCY);
    final IncompleteValidationView hostOneIncompleteValidationOne =
        new IncompleteValidationView(namespace1, "incomplete validation one message here");
    final IncompleteValidationView hostOneIncompleteValidationTwo =
        new IncompleteValidationView(namespace1, "incomplete validation two message here");

    final Date firstHostFirstReportTime =
        Date.from(LocalDateTime.of(2023, 1, 1, 0, 0).toInstant(ZoneOffset.UTC));
    final HostLevelStatusPostView hostOneFirstView =
        new HostLevelStatusPostView(
            firstHostFirstReportTime,
            List.of(hostOneFailureOne, hostOneFailureTwo),
            List.of(hostOneInconsistency),
            List.of(hostOneIncompleteValidationOne, hostOneIncompleteValidationTwo));

    final HostLevelStatusId firstHostId = new HostLevelStatusId(dbcheckId, fakeHostnameOne);
    callFailureEndpoint(dbcheckId, fakeHostnameOne, hostOneFirstView);
    {
      final List<HostLevelStatus> firstReportFailures =
          _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);
      assertEquals(1, firstReportFailures.size());
      final HostLevelStatus outputOne = firstReportFailures.get(0);
      final HostLevelStatusId outputOneId = outputOne.getId();
      assertEquals(fakeHostnameOne, outputOneId.getHostname());
      assertEquals(dbcheckId, outputOneId.getDbCheckId());
      assertEquals(2, outputOne.getFailures().size());
      assertEquals(
          Set.of(hostOneFailureOne.toFailure(), hostOneFailureTwo.toFailure()),
          outputOne.getFailures().stream().collect(Collectors.toUnmodifiableSet()));
      assertEquals(1, outputOne.getInconsistencies().size());
      assertEquals(
          Set.of(hostOneInconsistency.toInconsistency()),
          outputOne.getInconsistencies().stream().collect(Collectors.toUnmodifiableSet()));
      assertEquals(2, outputOne.getIncompleteValidations().size());
      assertEquals(
          Set.of(
              hostOneIncompleteValidationOne.toIncompleteValidation(),
              hostOneIncompleteValidationTwo.toIncompleteValidation()),
          outputOne.getIncompleteValidations().stream().collect(Collectors.toUnmodifiableSet()));
      assertEquals(firstHostFirstReportTime, outputOne.getLastExported());
    }

    // existing host w failure has new failure, inconsistency, and incomplete validation - gets
    // updated!
    final String hostOneNewFailureNS = "namespace2";
    final FailureView hostOneNewFailure =
        new FailureView(hostOneNewFailureNS, "uh oh looking not great");
    final InconsistencyView hostOneNewInconsistency =
        new InconsistencyView(
            hostOneNewFailureNS,
            "uh oh inconsistency",
            InconsistencyType.SINGLE_MEMBER_INCONSISTENCY);
    final IncompleteValidationView hostOneNewIncompleteValidation =
        new IncompleteValidationView(hostOneNewFailureNS, "uh oh incomplete validation");
    final Date firstHostSecondReportTime =
        Date.from(firstHostFirstReportTime.toInstant().plus(5, ChronoUnit.MINUTES));
    final HostLevelStatusPostView secondReport =
        new HostLevelStatusPostView(
            firstHostSecondReportTime,
            List.of(hostOneNewFailure),
            List.of(hostOneNewInconsistency),
            List.of(hostOneNewIncompleteValidation));

    final Consumer<HostLevelStatus> assertHostStatusMatchesFirstHost =
        (hostStatus) -> {
          assertEquals(hostStatus.getId(), firstHostId);
          assertEquals(3, hostStatus.getFailures().size());
          assertEquals(
              Set.of(
                  hostOneFailureOne.toFailure(),
                  hostOneFailureTwo.toFailure(),
                  hostOneNewFailure.toFailure()),
              hostStatus.getFailures().stream().collect(Collectors.toUnmodifiableSet()));
          assertEquals(
              Set.of(
                  hostOneInconsistency.toInconsistency(),
                  hostOneNewInconsistency.toInconsistency()),
              hostStatus.getInconsistencies().stream().collect(Collectors.toUnmodifiableSet()));
          assertEquals(
              Set.of(
                  hostOneIncompleteValidationOne.toIncompleteValidation(),
                  hostOneIncompleteValidationTwo.toIncompleteValidation(),
                  hostOneNewIncompleteValidation.toIncompleteValidation()),
              hostStatus.getIncompleteValidations().stream()
                  .collect(Collectors.toUnmodifiableSet()));
          assertEquals(firstHostSecondReportTime, hostStatus.getLastExported());
        };

    callFailureEndpoint(dbcheckId, fakeHostnameOne, secondReport);
    {
      final List<HostLevelStatus> secondReportFailures =
          _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);
      assertEquals(1, secondReportFailures.size());
      final HostLevelStatus hostStatus = secondReportFailures.get(0);
      assertHostStatusMatchesFirstHost.accept(hostStatus);
    }

    // we have a new host failing (failure and inconsistency) - and a corresponding new record

    final String fakeHostnameTwo = "defg";
    final FailureView hostTwoFailureOne = new FailureView(namespace1, "bad node");
    final InconsistencyView hostTwoInconsistency =
        new InconsistencyView(
            namespace1, "bad node inconsistency", InconsistencyType.INDETERMINATE_INCONSISTENCY);
    final Date secondHostLastReportTime =
        Date.from(firstHostSecondReportTime.toInstant().plus(10, ChronoUnit.MINUTES));
    final HostLevelStatusId secondHostId = new HostLevelStatusId(dbcheckId, fakeHostnameTwo);
    final HostLevelStatusPostView secondHostReport =
        new HostLevelStatusPostView(
            secondHostLastReportTime,
            List.of(hostTwoFailureOne),
            List.of(hostTwoInconsistency),
            null);
    final Consumer<HostLevelStatus> assertHostStatusMatchesSecondHost =
        (hostStatus) -> {
          assertEquals(secondHostId, hostStatus.getId());
          assertEquals(1, hostStatus.getFailures().size());
          assertEquals(
              Set.of(hostTwoFailureOne.toFailure()),
              hostStatus.getFailures().stream().collect(Collectors.toUnmodifiableSet()));
          assertEquals(1, hostStatus.getInconsistencies().size());
          assertEquals(
              Set.of(hostTwoInconsistency.toInconsistency()),
              hostStatus.getInconsistencies().stream().collect(Collectors.toUnmodifiableSet()));
          assertNull(hostStatus.getIncompleteValidations());
          assertEquals(secondHostLastReportTime, hostStatus.getLastExported());
        };
    callFailureEndpoint(dbcheckId, fakeHostnameTwo, secondHostReport);
    {
      final List<HostLevelStatus> thirdReportFailures =
          _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);
      assertEquals(2, thirdReportFailures.size());
      for (final HostLevelStatus hostStatus : thirdReportFailures) {
        // 1st host should be unchanged
        if (hostStatus.getId().equals(firstHostId)) {
          assertHostStatusMatchesFirstHost.accept(hostStatus);
        } // else expect to be new host
        else {
          assertHostStatusMatchesSecondHost.accept(hostStatus);
        }
      }
    }

    // we have a 3rd host reporting with no errors
    final String fakeHostnameThree = "three";
    final Date thirdHostReportDate =
        Date.from(firstHostFirstReportTime.toInstant().plus(1, ChronoUnit.HOURS));
    final HostLevelStatusPostView thirdHostView =
        new HostLevelStatusPostView(thirdHostReportDate, null, null, null);
    final HostLevelStatusId thirdHostId = new HostLevelStatusId(dbcheckId, fakeHostnameThree);
    callFailureEndpoint(dbcheckId, fakeHostnameThree, thirdHostView);
    {
      final List<HostLevelStatus> fourthReportStatus =
          _dbCheckSvc.getHostLevelDbCheckResults(dbcheckId);
      assertEquals(3, fourthReportStatus.size());
      for (final HostLevelStatus hostStatus : fourthReportStatus) {
        final HostLevelStatusId hostStatusId = hostStatus.getId();
        // 1st host should be unchanged
        if (hostStatusId.equals(firstHostId)) {
          assertHostStatusMatchesFirstHost.accept(hostStatus);
        } // else expect to be new host
        else if (hostStatusId.equals(secondHostId)) {
          assertHostStatusMatchesSecondHost.accept(hostStatus);
        } else {
          assertEquals(thirdHostId, hostStatusId);
          assertNull(hostStatus.getFailures());
          assertNull(hostStatus.getInconsistencies());
          assertNull(hostStatus.getIncompleteValidations());
          assertEquals(thirdHostReportDate, hostStatus.getLastExported());
        }
      }
    }
  }

  @Test
  public void testGetLastReportTime() throws IOException {
    final ObjectId fakeDbCheckId = new ObjectId();
    final String fakeHostnameOne = "abcd";

    final String namespace1 = "namespace1";

    final Date firstHostFirstReportTime =
        Date.from(LocalDateTime.of(2023, 1, 1, 0, 0).toInstant(ZoneOffset.UTC));
    final HostLevelStatusPostView hostOneFirstView =
        new HostLevelStatusPostView(firstHostFirstReportTime, null, null, null);

    // Should never happen, but with no DBCheck expect exception
    doAgentApiCallGetWithStatus(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/%s/timestamp?sk=%s&av=%s",
            _group.getId(), fakeDbCheckId.toHexString(), fakeHostnameOne, "sk1", "1.0"),
        HttpStatus.SC_INTERNAL_SERVER_ERROR);

    final DbCheck dbCheck = getTestDbCheck(null, getNewShardStatusesForRsNames(List.of("shard-0")));
    _dbCheckDao.addNewDbCheck(dbCheck);

    callFailureEndpoint(dbCheck.getId(), fakeHostnameOne, hostOneFirstView);
    final String resp =
        doAgentApiCallGet(
            _group,
            _agentApiKey,
            String.format(
                "/conf/dbcheck/%s/%s/%s/timestamp?sk=%s&av=%s",
                _group.getId(), dbCheck.getId().toHexString(), fakeHostnameOne, "sk1", "1.0"));
    final HostLevelStatusAgentGetView hostHealthlogTimestampView =
        _customObjectMapper.readerFor(HostLevelStatusAgentGetView.class).readValue(resp);
    assertEquals(firstHostFirstReportTime, hostHealthlogTimestampView.getLastExported());
  }

  @Test
  public void testGetMostRecentNodeObservations() throws IOException {

    final ObjectId fakeDbCheckId = new ObjectId();
    final String fakeHostnameNoData = "abc";
    final String fakeHostnameWithData = "def";

    // note - we seem to lose precision in serialization. IE 1000 and 1001 dates serialize same
    // which is fine for our purposes here
    final Date fakeOneStop = new Date(1000);
    final Date fakeOneStart = new Date(2000);
    final MongoDBTimestamp fakeOneLastBatchOptime = new MongoDBTimestamp(12345, 123);
    final Date fakeOneLastRestartable = new Date(3000);

    callPostStartStopDates(
        fakeDbCheckId,
        fakeHostnameWithData,
        new HostLevelStatusLastObservedView(
            fakeOneStop, fakeOneStart, fakeOneLastBatchOptime, fakeOneLastRestartable));

    final HostLevelStatusLastObservedView expected =
        new HostLevelStatusLastObservedView(
            fakeOneStop, fakeOneStart, fakeOneLastBatchOptime, fakeOneLastRestartable);

    final Date fakeStartMostlyEmptyShard = new Date(5000);
    callPostStartStopDates(
        fakeDbCheckId,
        fakeHostnameNoData,
        new HostLevelStatusLastObservedView(null, fakeStartMostlyEmptyShard, null, null));

    final HostLevelStatusLastObservedView expectedEmptyView =
        getStartStopDates(fakeDbCheckId, Set.of(fakeHostnameNoData));

    final HostLevelStatusLastObservedView viewWithNullFields =
        new HostLevelStatusLastObservedView(null, fakeStartMostlyEmptyShard, null, null);
    assertNull(expectedEmptyView.getLastObservedRestartableFailureDate());
    assertEquals(fakeStartMostlyEmptyShard, expectedEmptyView.getLastObservedDbCheckStartDate());
    assertNull(expectedEmptyView.getLastObservedDbCheckStopDate());
    assertNull(expectedEmptyView.getLastObservedOptime());
    assertEquals(viewWithNullFields, expectedEmptyView);

    final HostLevelStatusLastObservedView expectedFullView =
        getStartStopDates(fakeDbCheckId, Set.of(fakeHostnameWithData));
    assertEquals(expected, expectedFullView);

    final HostLevelStatusLastObservedView expectedEmptySaveForRestartable =
        getStartStopDates(fakeDbCheckId, Set.of(fakeHostnameNoData, fakeHostnameWithData));

    assertEquals(
        fakeOneLastRestartable,
        expectedEmptySaveForRestartable.getLastObservedRestartableFailureDate());
    assertEquals(fakeOneStart, expectedEmptySaveForRestartable.getLastObservedDbCheckStartDate());
    assertNull(expectedEmptySaveForRestartable.getLastObservedDbCheckStopDate());
    assertNull(expectedEmptySaveForRestartable.getLastObservedOptime());

    // Post a more recent observation for 3rd node
    final String fakeHostnameWithMoreRecentData = "ghi";
    final Date fakeNewerStart = new Date(100_000);
    final Date fakeNewerStop = new Date(200_000);
    final MongoDBTimestamp fakeNewerOptime = new MongoDBTimestamp(12346, 123);
    final Date fakeNewerRestartable = new Date(300_000);
    callPostStartStopDates(
        fakeDbCheckId,
        fakeHostnameWithMoreRecentData,
        new HostLevelStatusLastObservedView(
            fakeNewerStop, fakeNewerStart, fakeNewerOptime, fakeNewerRestartable));

    final HostLevelStatusLastObservedView earlierExpectedSaveForRestartable =
        getStartStopDates(
            fakeDbCheckId, Set.of(fakeHostnameWithMoreRecentData, fakeHostnameWithData));
    final HostLevelStatusLastObservedView expectedForNewerData =
        new HostLevelStatusLastObservedView(
            fakeOneStop, fakeOneStart, fakeOneLastBatchOptime, fakeNewerRestartable);
    assertEquals(expectedForNewerData, earlierExpectedSaveForRestartable);
  }

  private JSONObject getShardStatus(
      final String pShardName,
      final OperationStatus pOpStatus,
      final UUID pLsUUID,
      final UUID pLcUUID,
      final Integer pEvaluationSetSize,
      final Integer pCurrentEvaluationIndex,
      final String pCurrentOpMsg,
      final Date pLastStartedServerDbCheckTime) {
    return new JSONObject()
        .put(ShardStatusView.FieldDefs.RS_NAME_FIELD, pShardName)
        .put(ShardStatusView.FieldDefs.OPERATION_STATUS_FIELD, pOpStatus)
        .put(ShardStatusView.FieldDefs.LAST_UPDATED_FIELD, Instant.now().toString())
        .put(ShardStatusView.FieldDefs.LAST_STARTED_NAMESPACE_UUID_FIELD, pLsUUID)
        .put(ShardStatusView.FieldDefs.LAST_COMPLETED_NAMESPACE_UUID_FIELD, pLcUUID)
        .put(ShardStatusView.FieldDefs.EVALUATION_SET_SIZE_FIELD, pEvaluationSetSize)
        .put(ShardStatusView.FieldDefs.CURRENT_EVALUATION_INDEX_FIELD, pCurrentEvaluationIndex)
        .put(ShardStatusView.FieldDefs.CURRENT_OP_MSG_FIELD, pCurrentOpMsg)
        .putOpt(
            ShardStatusView.FieldDefs.LAST_STARTED_SERVER_DB_CHECK_TIME,
            Optional.ofNullable(pLastStartedServerDbCheckTime)
                .map(Date::toInstant)
                .map(Instant::toString)
                .orElse(null));
  }

  private DbCheck getTestDbCheck(final Options pOptions, final List<ShardStatus> pShardStatuses) {
    return getTestDbCheck(pOptions, pShardStatuses, null, null, null);
  }

  private DbCheck getTestDbCheck(
      final Options pOptions,
      final List<ShardStatus> pShardStatuses,
      final Boolean pSkipAgentChecks,
      final Integer pReplicationLagThreshold) {
    return getTestDbCheck(
        pOptions, pShardStatuses, null, pSkipAgentChecks, pReplicationLagThreshold);
  }

  private DbCheck getTestDbCheck(
      final Options pOptions,
      final List<ShardStatus> pShardStatuses,
      final List<ValidationMode> pValidationModes,
      final Boolean pSkipAgentChecks,
      final Integer pReplicationLagThreshold) {
    final ClusterDescription baseCluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), "myCluster"));
    final ReplicationSpec dummySpec =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), new ObjectId(), "zone", 1, 3, List.of(AWSRegionName.AP_EAST_1));
    final ClusterDescription cluster =
        baseCluster
            .copy()
            .setReplicationSpecList(List.of(dummySpec))
            .setMongoDBMajorVersion("6.0")
            .setDiskSizeGB(200)
            .build();

    final ClusterInfo clusterInfo = new ClusterInfo(cluster);
    _clusterDescriptionDao.save(cluster);

    return new DbCheck(
        cluster.getUniqueId(),
        cluster.getName(),
        _group.getId(),
        new Date(),
        null,
        clusterInfo,
        pOptions,
        pShardStatuses,
        null,
        null,
        CorruptionDetectionOperationOrigin.MANUAL,
        pValidationModes,
        pSkipAgentChecks,
        pReplicationLagThreshold,
        null);
  }

  @Test
  public void testAgentCancellation() throws Exception {
    final String shard0_rsName = "shard-0";
    final String shard1_rsName = "shard-1";

    final DbCheck dbCheck =
        getTestDbCheck(null, getNewShardStatusesForRsNames(List.of(shard0_rsName, shard1_rsName)));
    _dbCheckDao.addNewDbCheck(dbCheck);
    assertEquals(2, dbCheck.getShardStatuses().size());
    assertEquals(RunStatus.NEW, dbCheck.getStatus());

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            "/conf/dbcheck/%s/%s/cancel?ah=%s&sk=%s&av=%s",
            _group.getId(), dbCheck.getId(), "hostname", "sk1", "1.0"),
        new JSONObject());

    final DbCheck updatedCheck0 = _dbCheckDao.find(dbCheck.getId()).get();
    assertEquals(RunStatus.CANCELED, updatedCheck0.getStatus());
  }

  static class DEPRECATED_FIELD_DEFS {
    public static final String MAX_BYTES_PER_BATCH = "maxBytesPerBatch";
    public static final String MAX_COUNT_PER_SECOND = "maxCountPerSecond";
  }
}
