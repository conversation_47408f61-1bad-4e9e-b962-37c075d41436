package com.xgen.svc.nds.res;

import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType.MEMORY;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.joinFields;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.apiuser._public.svc.ApiUserSvc;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.accesstransparency._public.svc.AccessTransparencyEventSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSServerAccessDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSServerAccess;
import com.xgen.cloud.nds.project._public.model.NDSServerAccess.AccessLevel;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.svc.NDSDBRoleSvc;
import com.xgen.cloud.nds.project._public.util.NDSX509Util;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.ApiUser;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.mms.api.view._private.ApiPrivateNDSResourceSSHKeyRequest;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.ui.AtlasSshApiKeyAppServicesDebugRequestView;
import com.xgen.svc.nds.model.ui.AtlasSshApiKeySupportAccessRequestView;
import com.xgen.svc.nds.svc.NDSServerAccessSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.core.Response;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

@ExtendWith(GuiceTestExtension.class)
public class AtlasSshApiKeyAuthenticatedResourceIntTests extends JUnit5BaseResourceTest {

  // Dao
  @Inject private UserDao _userDao;
  @Inject private GroupDao _groupDao;
  @Inject private NDSServerAccessDao _ndsServerAccessDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  // Svc
  @Inject private UserSvc _userSvc;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private AuthzSvc _authzSvc;
  @Inject @Spy private AuditSvc _auditSvc;
  @Inject private NDSServerAccessSvc _ndsServerAccessSvc;
  @Inject private ApiUserSvc _apiUserSvc;
  @Inject private ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  @Inject private AccessTransparencyEventSvc _accessTransparencyEventSvc;
  @Inject private HostSvc _hostSvc;
  @Inject @Spy private NDSGroupSvc _ndsGroupSvc;
  @Inject @Spy private AppSettings _appSettings;
  @Inject private NDSDBRoleSvc _ndsDbRoleSvc;
  @Inject @Spy private AuditInfoSvc _auditInfoSvc;

  // Other
  private Group _group;
  private AuditInfo _auditInfo;
  private AgentApiKey _agentApiKey;
  private final ObjectId _groupId = new ObjectId("000000000000000000000118");
  private final String _username = "<EMAIL>";
  private final String _clusterName = "rs1";
  private Cluster _cluster;
  private InstanceHardware _hardware;
  private ApiUser _apiUser;
  private AppUser _appUser;
  private final Date _now = Date.from(Instant.now());
  private final String SSH_KEY_FILEPATH = "server/scripts/nds/nds.ssh.private.pem";
  private final String EXPECTED_REMOTE_IP_ADDRESS = "127.0.0.1";

  private AutoCloseable mocks;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();

    mocks = MockitoAnnotations.openMocks(this);
    populateBackingDb();

    _group = _groupDao.findById(_groupId);
    _auditInfo = MmsFactory.createAuditInfoWithAppUser();
    _agentApiKey = MmsFactory.generateApiKey(_groupId, new ObjectId());
    _appUser =
        _userSvc.ensureLocalUser(
            _username,
            "password1",
            "First Name",
            "Last Name",
            List.of(_groupId),
            null,
            Set.of(Role.GLOBAL_APP_SERVICES_CLUSTER_DEBUG_DATA_ACCESS),
            mock(AuditInfo.class),
            null);

    final ClusterDescription clusterDescription =
        _clusterDescriptionDao.findByName(_groupId, _clusterName).orElseThrow();
    final List<ReplicaSetHardware> replicaSetHardware =
        _replicaSetHardwareDao.findByCluster(_groupId, _clusterName);
    assertEquals(
        1, replicaSetHardware.size(), "Expected to find a single replica set for the cluster");
    _cluster = new Cluster.ReplicaSet(clusterDescription, replicaSetHardware.get(0));
    _hardware = replicaSetHardware.get(0).getHardware().get(0);

    // Remove unneeded replica set hardware to allow proper lookup by hostname
    _replicaSetHardwareDao.remove(
        new BasicDBObject()
            .append(
                joinFields(
                    ReplicaSetHardware.FieldDefs.ID, ReplicaSetHardware.FieldDefs.CLUSTER_NAME),
                new BasicDBObject(BaseDao.NE, _clusterName)));
    _replicaSetHardwareDao.remove(
        new BasicDBObject()
            .append(
                joinFields(ReplicaSetHardware.FieldDefs.ID, ReplicaSetHardware.FieldDefs.GROUP_ID),
                new BasicDBObject(BaseDao.NE, _groupId)));

    // Set all hardware to be provisioned
    _replicaSetHardwareDao.updateAll(
        new BasicDBObject(),
        new BasicDBObject(
            BaseDao.SET,
            new BasicDBObject(
                joinFields(
                    ReplicaSetHardware.FieldDefs.CLOUD_PROVIDER_HARDWARE,
                    BaseDao.POSITIONAL_ALL,
                    InstanceHardware.FieldDefs.PROVISIONED),
                true)));

    // Valid secret
    when(_appSettings.getDeviceSyncDebugAccessSharedSecretJson())
        .thenReturn("{\"secret\": \"password\"}");
  }

  @AfterEach
  public void tearDown() throws Exception {
    mocks.close();
  }

  private AtlasSshApiKeyAuthenticatedResource getResource() {
    return UnitTestUtils.create(AtlasSshApiKeyAuthenticatedResource.class)
        .withArgs(
            _appSettings,
            _authzSvc,
            _auditSvc,
            _accessTransparencyEventSvc,
            _hostSvc,
            _replicaSetHardwareSvc,
            _ndsClusterSvc,
            _ndsGroupSvc,
            _ndsServerAccessSvc,
            _userSvc,
            _ndsDbRoleSvc,
            _auditInfoSvc);
  }

  private void populateBackingDb() throws Exception {
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ReplicaSetHardwareDao/replicaSetHardware.json.ftl",
        null,
        "nds",
        "config.nds.replicaSetHardware");
  }

  private ApiUser getApiUser() {
    if (_apiUser == null) {
      try {
        final Date expiration = Date.from(_now.toInstant().plus(Duration.ofDays(1L)));
        _apiUser = _apiUserSvc.createTemporaryGlobalApiUser("", expiration, mock(AuditInfo.class));
      } catch (SvcException pE) {
        throw new RuntimeException(pE);
      }
    }
    return _apiUser;
  }

  private NDSServerAccess getTestNDSServerAccess(final Date validUntil) {
    final String hostName = "hostname0";

    final ApiUser apiUser = getApiUser();

    final NDSServerAccess ndsServerAccess =
        new NDSServerAccess.Builder()
            .id(new ObjectId())
            .groupId(_groupId)
            .userId(_appUser.getId())
            .userName(_username)
            .hostname(_hardware.getHostnameForAgents().orElseThrow())
            .apiUserUsername(apiUser.getUsername())
            .apiKey(apiUser.getPrivateKey())
            .validUntil(validUntil)
            .accessLevel(AccessLevel.ELEVATED)
            .build();
    _ndsServerAccessDao.save(ndsServerAccess);

    return ndsServerAccess;
  }

  private JSONObject getSyncAccessTestDefaultView(final NDSServerAccess pNDSServerAccess) {
    return getSyncAccessTestDefaultView(pNDSServerAccess, List.of());
  }

  private JSONObject getSyncAccessTestDefaultView(
      final NDSServerAccess pNDSServerAccess, final List<ObjectId> pAppIds) {
    final NDSServerAccess ndsServerAccess = pNDSServerAccess.showApiKey();
    final String sshRequestKey =
        String.format(
            "%s:%s:%s",
            ndsServerAccess.getApiUserUsername(),
            ndsServerAccess.getApiKey(),
            ndsServerAccess.getId());

    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView = new JSONObject();
    apiPrivateAtlasSshApiKeyAppServicesDebugRequestView.put(
        AtlasSshApiKeyAppServicesDebugRequestView.SSH_REQUEST_KEY, sshRequestKey);
    apiPrivateAtlasSshApiKeyAppServicesDebugRequestView.put(
        AtlasSshApiKeyAppServicesDebugRequestView.SYNC_APP_IDS, pAppIds);
    return apiPrivateAtlasSshApiKeyAppServicesDebugRequestView;
  }

  private JSONObject getSupportAccessTestDefaultView(final NDSServerAccess pNDSServerAccess) {
    return getSupportAccessTestDefaultView(pNDSServerAccess, Map.of(), Map.of());
  }

  private JSONObject getSupportAccessTestDefaultView(
      final NDSServerAccess pNDSServerAccess,
      final Map<String, String> pSupportAccessRequestViewOverrides,
      final Map<String, String> pSSHKeyRequestViewOverrides) {
    final NDSServerAccess ndsServerAccess = pNDSServerAccess.showApiKey();

    final String requestIdPlusUserSignature;
    try {
      requestIdPlusUserSignature =
          TLSUtil.rsaSignWithPrivateKey(
              ndsServerAccess.getId().toHexString() + ndsServerAccess.getApiUserUsername(),
              SSH_KEY_FILEPATH);
    } catch (final Exception checkedException) {
      throw new RuntimeException(checkedException);
    }

    final JSONObject apiPrivateNDSResourceSSHKeyRequestView = new JSONObject();
    apiPrivateNDSResourceSSHKeyRequestView.put("requestId", ndsServerAccess.getId().toHexString());
    apiPrivateNDSResourceSSHKeyRequestView.put("keyType", "toor");
    apiPrivateNDSResourceSSHKeyRequestView.put("user", ndsServerAccess.getApiUserUsername());
    apiPrivateNDSResourceSSHKeyRequestView.put(
        "requestIdPlusUserSignature", requestIdPlusUserSignature);
    apiPrivateNDSResourceSSHKeyRequestView.put(
        "accessLevel", ndsServerAccess.getAccessLevel().name());
    for (final Map.Entry<String, String> entry : pSSHKeyRequestViewOverrides.entrySet()) {
      apiPrivateNDSResourceSSHKeyRequestView.put(entry.getKey(), entry.getValue());
    }

    final JSONObject atlasSshApiKeySupportAccessRequestView = new JSONObject();
    atlasSshApiKeySupportAccessRequestView.put(
        "sshKeyRequest", apiPrivateNDSResourceSSHKeyRequestView);
    atlasSshApiKeySupportAccessRequestView.put("x509PEMEncryptionKey", "encryptionKey");
    atlasSshApiKeySupportAccessRequestView.put(
        "requestingUserEmail", ndsServerAccess.getUserName());
    for (final Map.Entry<String, String> entry : pSupportAccessRequestViewOverrides.entrySet()) {
      atlasSshApiKeySupportAccessRequestView.put(entry.getKey(), entry.getValue());
    }

    return atlasSshApiKeySupportAccessRequestView;
  }

  private AtlasSshApiKeyAppServicesDebugRequestView generateX509ReadyOnlyAccessViewObject(
      final NDSServerAccess pNDSServerAccess, final List<ObjectId> pAppIds) {
    final NDSServerAccess ndsServerAccess = pNDSServerAccess.showApiKey();
    final String sshRequestKey =
        String.format(
            "%s:%s:%s",
            ndsServerAccess.getApiUserUsername(),
            ndsServerAccess.getApiKey(),
            ndsServerAccess.getId());

    return new AtlasSshApiKeyAppServicesDebugRequestView(sshRequestKey, pAppIds);
  }

  private String makeX509ReadOnlyAccessRequest(
      final JSONObject requestBody, final Integer expectedStatus) {
    final String requestPath =
        String.format(
            "/nds/sshkeyauth/%s/clusters/%s/appservices/x509readonlyaccess",
            _groupId, _clusterName);
    try {
      return doAgentApiCallPostWithStatus(
          _group, _agentApiKey, requestPath, requestBody, expectedStatus);
    } catch (final IOException pE) {
      throw new RuntimeException("Encountered IOException making agent api call.");
    }
  }

  private String makeX509SupportAccessRequest(
      final JSONObject requestBody, final Integer expectedStatus) {
    final String requestPath = String.format("/nds/sshkeyauth/%s/x509supportaccess", _groupId);
    try {
      return doAgentApiCallPostWithStatus(
          _group, _agentApiKey, requestPath, requestBody, expectedStatus);
    } catch (final IOException pE) {
      throw new RuntimeException("Encountered IOException making agent api call.");
    }
  }

  private void grantEmployeeAccessToCluster(final EmployeeAccessGrantType pGrantType) {
    final ClusterDescription cluster = _cluster.getClusterDescription();

    if (pGrantType != null) {
      _ndsClusterSvc.grantEmployeeAccess(
          cluster,
          pGrantType,
          Date.from(Instant.now().plus(Duration.ofDays(1))),
          _group,
          _auditInfo,
          null);
    }
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_worksCorrectly() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    final String response =
        makeX509ReadOnlyAccessRequest(
            apiPrivateAtlasSshApiKeyAppServicesDebugRequestView, HttpStatus.SC_OK);
    assertTrue(response.contains("BEGIN CERTIFICATE"));
    assertTrue(response.contains("END CERTIFICATE"));
    assertTrue(response.contains("BEGIN RSA PRIVATE KEY"));
    assertTrue(response.contains("END RSA PRIVATE KEY"));
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_requestIdDNE() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    makeX509ReadOnlyAccessRequest(
        apiPrivateAtlasSshApiKeyAppServicesDebugRequestView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_expiredRequestCredentials() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().minus(Duration.ofDays(1L))));
    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    makeX509ReadOnlyAccessRequest(
        apiPrivateAtlasSshApiKeyAppServicesDebugRequestView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_clusterDNE() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));

    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    try {
      doAgentApiCallPostWithStatus(
          _group,
          _agentApiKey,
          "/nds/sshkeyauth/"
              + _groupId
              + "/clusters/INCORRECT_CLUSTER_NAME/appservices/x509readonlyaccess",
          apiPrivateAtlasSshApiKeyAppServicesDebugRequestView,
          HttpStatus.SC_NOT_FOUND);
    } catch (final IOException pE) {
      fail();
    }
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_doesNotHaveRole() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));

    _userDao.removeRoleAssignments(
        _username,
        Collections.singleton(
            RoleAssignment.forGlobal(Role.GLOBAL_APP_SERVICES_CLUSTER_DEBUG_DATA_ACCESS)));

    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    makeX509ReadOnlyAccessRequest(
        apiPrivateAtlasSshApiKeyAppServicesDebugRequestView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_accessAddedThenRevoked() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));

    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);
    final ClusterDescription basicCluster =
        _clusterDescriptionDao.findByName(_groupId, _clusterName).get();

    _ndsClusterSvc.revokeGrantedEmployeeAccess(basicCluster, _group, _auditInfo, null);

    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    makeX509ReadOnlyAccessRequest(
        apiPrivateAtlasSshApiKeyAppServicesDebugRequestView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_doesNotHaveAccess() {
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));

    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);

    final JSONObject apiPrivateAtlasSshApiKeyAppServicesDebugRequestView =
        getSyncAccessTestDefaultView(ndsServerAccess);

    makeX509ReadOnlyAccessRequest(
        apiPrivateAtlasSshApiKeyAppServicesDebugRequestView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_possibleAppSettingSecretValues() throws SvcException {
    doNothing().when(_auditSvc).saveAuditEvent(any());
    final AtlasSshApiKeyAuthenticatedResource atlasSshApiKeyAuthenticatedResource = getResource();

    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final AtlasSshApiKeyAppServicesDebugRequestView atlasSshApiKeyAppServicesDebugRequestView =
        generateX509ReadyOnlyAccessViewObject(
            ndsServerAccess, Arrays.asList(new ObjectId(), new ObjectId()));

    final AppUser appUser = new AppUser();
    appUser.setUsername(ndsServerAccess.getApiUserUsername());

    // Secret is not set for the environment
    {
      when(_appSettings.getDeviceSyncDebugAccessSharedSecretJson()).thenReturn("");
      assertThrows(
          SvcException.class,
          () ->
              atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
                  mock(HttpServletRequest.class),
                  _clusterName,
                  _groupId.toString(),
                  atlasSshApiKeyAppServicesDebugRequestView));
    }

    // Secret is not valid JSON
    {
      when(_appSettings.getDeviceSyncDebugAccessSharedSecretJson()).thenReturn("foo");
      assertThrows(
          SvcException.class,
          () ->
              atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
                  mock(HttpServletRequest.class),
                  _clusterName,
                  _groupId.toString(),
                  atlasSshApiKeyAppServicesDebugRequestView));
    }

    // Secret is valid JSON but is missing proper payload
    {
      when(_appSettings.getDeviceSyncDebugAccessSharedSecretJson())
          .thenReturn("{\"foo\": \"bar\"}");
      assertThrows(
          SvcException.class,
          () ->
              atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
                  mock(HttpServletRequest.class),
                  _clusterName,
                  _groupId.toString(),
                  atlasSshApiKeyAppServicesDebugRequestView));
    }

    // Secret is valid
    {
      when(_appSettings.getDeviceSyncDebugAccessSharedSecretJson())
          .thenReturn("{\"secret\": \"bar\"}");
      final Response response =
          atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
              mock(HttpServletRequest.class),
              _clusterName,
              _groupId.toString(),
              atlasSshApiKeyAppServicesDebugRequestView);
      assertEquals(response.getStatus(), HttpStatus.SC_OK);
    }
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_x509CorrectParameters() throws SvcException {
    final AtlasSshApiKeyAuthenticatedResource atlasSshApiKeyAuthenticatedResource = getResource();

    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final AtlasSshApiKeyAppServicesDebugRequestView atlasSshApiKeyAppServicesDebugRequestView =
        generateX509ReadyOnlyAccessViewObject(ndsServerAccess, new ArrayList<>());

    final AppUser appUser = new AppUser();
    appUser.setUsername(ndsServerAccess.getApiUserUsername());

    final Response response =
        atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
            mock(HttpServletRequest.class),
            _clusterName,
            _groupId.toString(),
            atlasSshApiKeyAppServicesDebugRequestView);

    final List<Pair<String, String>> roles =
        Stream.of(
                _ndsGroupSvc
                    .getNDSDBRoleSvc()
                    .factoryCreate("__realm_sync", "read")
                    .getRoleAndDbPair())
            .map(p -> Pair.of(p.getLeft(), p.getRight()))
            .collect(Collectors.toList());

    try {
      final ArgumentCaptor<Long> captor = ArgumentCaptor.forClass(Long.class);
      verify(_ndsGroupSvc, times(1))
          .generateX509CertKeyPair(
              eq(_groupId),
              captor.capture(),
              eq(NDSX509Util.getDNForManagedX509User(appUser.getUsername())),
              eq(List.of(TLSUtil.toDbRolesCertificateExtension(roles))),
              eq("password"));
      assertTrue(Math.abs(captor.getValue() - Duration.ofHours(6).toSeconds()) < 50);
    } catch (SvcException pE) {
      pE.printStackTrace();
      fail();
    }

    assertTrue(response.getEntity().toString().contains("BEGIN "));
    assertTrue(response.getEntity().toString().contains("END "));
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_x509UnderSixHours() throws SvcException {
    final AtlasSshApiKeyAuthenticatedResource atlasSshApiKeyAuthenticatedResource = getResource();

    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final Date twoHoursFromNow = Date.from(Instant.now().plus(Duration.ofHours(2)));
    final ClusterDescription basicCluster =
        _clusterDescriptionDao.findByName(_groupId, _clusterName).get();

    final ObjectId pGroupId = basicCluster.getGroupId();
    final String pClusterName = basicCluster.getName();
    _clusterDescriptionDao.setEmployeeAccessGrant(
        pGroupId,
        pClusterName,
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA,
        twoHoursFromNow);

    final AtlasSshApiKeyAppServicesDebugRequestView atlasSshApiKeyAppServicesDebugRequestView =
        generateX509ReadyOnlyAccessViewObject(ndsServerAccess, new ArrayList<>());

    final AppUser appUser = new AppUser();
    appUser.setUsername(ndsServerAccess.getApiUserUsername());

    atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
        mock(HttpServletRequest.class),
        _clusterName,
        _groupId.toString(),
        atlasSshApiKeyAppServicesDebugRequestView);

    final List<Pair<String, String>> roles =
        Stream.of(
                _ndsGroupSvc
                    .getNDSDBRoleSvc()
                    .factoryCreate("__realm_sync", "read")
                    .getRoleAndDbPair())
            .map(p -> Pair.of(p.getLeft(), p.getRight()))
            .collect(Collectors.toList());

    try {
      final ArgumentCaptor<Long> captor = ArgumentCaptor.forClass(Long.class);
      verify(_ndsGroupSvc, times(1))
          .generateX509CertKeyPair(
              eq(_groupId),
              captor.capture(),
              eq(NDSX509Util.getDNForManagedX509User(appUser.getUsername())),
              eq(List.of(TLSUtil.toDbRolesCertificateExtension(roles))),
              eq("password"));
      assertTrue(Math.abs(captor.getValue() - Duration.ofHours(2).toSeconds()) < 50);
    } catch (SvcException pE) {
      pE.printStackTrace();
      fail();
    }
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_x509MultipleAppIds() throws SvcException {
    final AtlasSshApiKeyAuthenticatedResource atlasSshApiKeyAuthenticatedResource = getResource();

    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    ObjectId oidOne = new ObjectId();
    ObjectId oidTwo = new ObjectId();

    final AtlasSshApiKeyAppServicesDebugRequestView atlasSshApiKeyAppServicesDebugRequestView =
        generateX509ReadyOnlyAccessViewObject(ndsServerAccess, Arrays.asList(oidOne, oidTwo));

    final AppUser appUser = new AppUser();
    appUser.setUsername(ndsServerAccess.getApiUserUsername());

    final Response response =
        atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
            mock(HttpServletRequest.class),
            _clusterName,
            _groupId.toString(),
            atlasSshApiKeyAppServicesDebugRequestView);

    final List<Pair<String, String>> roles =
        Stream.of(
                _ndsGroupSvc
                    .getNDSDBRoleSvc()
                    .factoryCreate("__realm_sync", "read")
                    .getRoleAndDbPair())
            .map(p -> Pair.of(p.getLeft(), p.getRight()))
            .collect(Collectors.toList());

    Arrays.asList(oidOne, oidTwo)
        .forEach(
            appId ->
                Stream.of(
                        _ndsGroupSvc
                            .getNDSDBRoleSvc()
                            .factoryCreate("__realm_sync_" + appId, "read")
                            .getRoleAndDbPair())
                    .map(p -> Pair.of(p.getLeft(), p.getRight()))
                    .forEach(roles::add));

    try {
      final ArgumentCaptor<Long> captor = ArgumentCaptor.forClass(Long.class);
      verify(_ndsGroupSvc, times(1))
          .generateX509CertKeyPair(
              eq(_groupId),
              captor.capture(),
              eq(NDSX509Util.getDNForManagedX509User(appUser.getUsername())),
              eq(List.of(TLSUtil.toDbRolesCertificateExtension(roles))),
              eq("password"));
      assertTrue(Math.abs(captor.getValue() - Duration.ofHours(6).toSeconds()) < 50);
    } catch (SvcException pE) {
      pE.printStackTrace();
      fail();
    }

    assertTrue(response.getEntity().toString().contains("BEGIN "));
    assertTrue(response.getEntity().toString().contains("END "));
  }

  @Test
  public void testGenerateReadOnlyDebugAccess_ensureAuditHappens() throws SvcException {
    doNothing().when(_auditSvc).saveAuditEvent(any());

    final AtlasSshApiKeyAuthenticatedResource atlasSshApiKeyAuthenticatedResource = getResource();

    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    grantEmployeeAccessToCluster(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA);

    final AtlasSshApiKeyAppServicesDebugRequestView atlasSshApiKeyAppServicesDebugRequestView =
        generateX509ReadyOnlyAccessViewObject(
            ndsServerAccess, Arrays.asList(new ObjectId(), new ObjectId()));

    final AppUser appUser = new AppUser();
    appUser.setUsername(ndsServerAccess.getApiUserUsername());

    atlasSshApiKeyAuthenticatedResource.generateReadOnlyDebugAccess(
        mock(HttpServletRequest.class),
        _clusterName,
        _groupId.toString(),
        atlasSshApiKeyAppServicesDebugRequestView);

    verify(_auditSvc, times(1))
        .saveAuditEvent(
            argThat(
                event ->
                    event.isHidden()
                        && event.getGroupId().equals(_groupId)
                        && event.getEventType() == Type.DEVICE_SYNC_DEBUG_X509_CERT_CREATED));
  }

  private void setSupportX509CertificatesEnabled() {
    _appSettings.setProp(
        AppSettings.Fields.MMS_ATLAS_ACCESS_TRANSPARENCY_X509_CERTS_ENABLED.value, "true", MEMORY);
  }

  private void setSupportX509CertificatesDisabled() {
    _appSettings.setProp(
        AppSettings.Fields.MMS_ATLAS_ACCESS_TRANSPARENCY_X509_CERTS_ENABLED.value, "false", MEMORY);
  }

  private void setPublicIpForHardware(final String pPublicIp) {
    final BasicDBObject filter =
        new BasicDBObject()
            .append(
                joinFields(ReplicaSetHardware.FieldDefs.ID, ReplicaSetHardware.FieldDefs.GROUP_ID),
                _groupId.toHexString())
            .append(
                joinFields(
                    ReplicaSetHardware.FieldDefs.ID, ReplicaSetHardware.FieldDefs.CLUSTER_NAME),
                _clusterName);
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                BaseDao.SET,
                new BasicDBObject(
                    joinFields(
                        ReplicaSetHardware.FieldDefs.CLOUD_PROVIDER_HARDWARE,
                        BaseDao.POSITIONAL_ALL,
                        InstanceHardware.FieldDefs.PUBLIC_IP),
                    pPublicIp));

    _replicaSetHardwareDao.updateAll(new BasicDBObject(), update);
  }

  @Test
  public void testGenerateX509AccessForSupport_appSettingDisabled() {
    setSupportX509CertificatesDisabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);

    makeX509SupportAccessRequest(testView, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testGenerateX509AccessForSupport_requestIdSignatureSpecified() {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject testView =
        getSupportAccessTestDefaultView(
            ndsServerAccess, Map.of(), Map.of("requestIdSignature", "anything"));

    makeX509SupportAccessRequest(testView, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testGenerateX509AccessForSupport_accessRequestMissing() {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);
    _ndsServerAccessDao.removeAccess(
        ndsServerAccess.getGroupId(), ndsServerAccess.getHostname(), ndsServerAccess.getUserId());

    makeX509SupportAccessRequest(testView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateX509AccessForSupport_accessRequestExpired() {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(-1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);

    makeX509SupportAccessRequest(testView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateX509AccessForSupport_accessNotGranted() {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(null);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(-1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);

    makeX509SupportAccessRequest(testView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateX509AccessForSupport_invalidBastionSignature() throws Exception {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final String invalidSignature = TLSUtil.rsaSignWithPrivateKey("anything", SSH_KEY_FILEPATH);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(-1L))));
    final JSONObject testView =
        getSupportAccessTestDefaultView(
            ndsServerAccess, Map.of(), Map.of("requestIdPlusUserSignature", invalidSignature));

    makeX509SupportAccessRequest(testView, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testGenerateX509AccessForSupport_requestPublicIpInvalid() {
    setSupportX509CertificatesEnabled();
    final String UNEXPECTED_REMOTE_IP_ADDRESS = "***********";
    setPublicIpForHardware(UNEXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);

    makeX509SupportAccessRequest(testView, HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  public void testGenerateX509AccessForSupport_validRequest() {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);
    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);

    final String response = makeX509SupportAccessRequest(testView, HttpStatus.SC_OK);
    assertTrue(response.contains("BEGIN CERTIFICATE"));
    assertTrue(response.contains("END CERTIFICATE"));
    assertTrue(response.contains("BEGIN RSA PRIVATE KEY"));
    assertTrue(response.contains("END RSA PRIVATE KEY"));
  }

  @Test
  public void testGenerateX509AccessForSupport_auditEvent() throws SvcException {
    setSupportX509CertificatesEnabled();
    setPublicIpForHardware(EXPECTED_REMOTE_IP_ADDRESS);
    grantEmployeeAccessToCluster(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE);

    final NDSServerAccess ndsServerAccess =
        getTestNDSServerAccess(Date.from(_now.toInstant().plus(Duration.ofDays(1L))));
    final JSONObject testView = getSupportAccessTestDefaultView(ndsServerAccess);
    final AtlasSshApiKeySupportAccessRequestView requestView =
        new AtlasSshApiKeySupportAccessRequestView(
            new ApiPrivateNDSResourceSSHKeyRequest(
                testView.getJSONObject("sshKeyRequest").getString("requestId"),
                testView.getJSONObject("sshKeyRequest").getString("keyType"),
                testView.getJSONObject("sshKeyRequest").getString("user"),
                "",
                testView.getJSONObject("sshKeyRequest").getString("requestIdPlusUserSignature"),
                AccessLevel.valueOf(
                    testView.getJSONObject("sshKeyRequest").getString("accessLevel"))),
            testView.getString("x509PEMEncryptionKey"),
            testView.getString("requestingUserEmail"));

    final AtlasSshApiKeyAuthenticatedResource atlasSshApiKeyAuthenticatedResource = getResource();

    final HttpServletRequest request = mock(HttpServletRequest.class);
    when(request.getRemoteAddr()).thenReturn(EXPECTED_REMOTE_IP_ADDRESS);
    try (final Response response =
        atlasSshApiKeyAuthenticatedResource.generateX509AccessForSupport(
            request, _groupId.toString(), requestView)) {

      assertEquals(200, response.getStatus());
      verify(_auditSvc, times(1))
          .saveAndLogAuditEvent(
              argThat(
                  arg ->
                      arg.getEventType()
                          .equals(
                              HostEvent.Type
                                  .HOST_X509_CERTIFICATE_CERTIFICATE_GENERATED_FOR_SUPPORT_ACCESS)));
    }
  }
}
