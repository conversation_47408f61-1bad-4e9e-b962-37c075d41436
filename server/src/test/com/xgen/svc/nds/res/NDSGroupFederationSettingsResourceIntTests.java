package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.federation._public.model.IdentityProvider;
import com.xgen.cloud.federation._public.model.OidcIdentityProvider;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._public.util.NDSTestOIDCIdp;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.federation.dao.FederationSettingsDao;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.NDSFederationTestingUtils;
import com.xgen.svc.nds.model.ui.NDSGroupFederationSettingsView;
import com.xgen.svc.nds.model.ui.NDSOIDCIdentityProviderView;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;

@ExtendWith(GuiceTestExtension.class)
public class NDSGroupFederationSettingsResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private FederationSettingsDao _federationSettingsDao;
  private ObjectId _groupId;
  private AppUser _user;
  private Group _group;
  private AutoCloseable _closable;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _closable = MockitoAnnotations.openMocks(this);
    _group = MmsFactory.createGroupWithNDSPlan();
    _groupId = _group.getId();
    _ndsGroupSvc.ensureGroup(_groupId);
    _user = MmsFactory.createUser(_group);
  }

  @AfterEach
  public void tearDown() throws Exception {
    _closable.close();
  }

  private String getRequestPath() {
    return getRequestPath(_groupId);
  }

  private String getRequestPath(final ObjectId pGroupId) {
    return String.format("/nds/%s/federationSettings", pGroupId);
  }

  @Test
  public void testNDSGroupFederationSettingsResource() {
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION);

    final OidcIdentityProvider workforceIdp =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    final OidcIdentityProvider workforceIdpUserAuthType =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);
    final OidcIdentityProvider workloadIdp =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);
    final OidcIdentityProvider workloadIdentityProviderGroupAuthType =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);

    final Set<IdentityProvider> idps =
        Set.of(
            workforceIdp,
            workforceIdpUserAuthType,
            workloadIdp,
            workloadIdentityProviderGroupAuthType);
    NDSFederationTestingUtils.linkIdentityProvidersToOrg(idps, _group, _federationSettingsDao);

    final JSONObject response = doAuthedJsonGet(_user, getRequestPath());
    final JSONArray responseArray =
        response.getJSONArray(NDSGroupFederationSettingsView.OIDC_IDENTITY_PROVIDERS);
    assertEquals(responseArray.length(), idps.size());
    assertIdpsMatchJsonArray(idps, responseArray);
  }

  @Test
  public void testNDSGroupFederationSettingsResource_noIdpConfigured() {
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION);

    final JSONObject response = doAuthedJsonGet(_user, getRequestPath());
    final JSONArray responseArray =
        response.getJSONArray(NDSGroupFederationSettingsView.OIDC_IDENTITY_PROVIDERS);
    assertEquals(responseArray.length(), 0);
  }

  @Test
  public void testNDSGroupFederationSettingsResource_useHardcodedIdpInformation() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION);

    final JSONObject response = doAuthedJsonGet(_user, getRequestPath());
    final JSONArray responseArray =
        response.getJSONArray(NDSGroupFederationSettingsView.OIDC_IDENTITY_PROVIDERS);
    assertEquals(responseArray.length(), 2);
    assertIdpsMatchJsonArray(
        List.of(NDSTestOIDCIdp.getTestWorkforceOIDCIdp(), NDSTestOIDCIdp.getTestWorkloadOIDCIdp()),
        responseArray);
  }

  @Test
  public void testNDSGroupFederationSettingsResource_userNotInGroup() {
    doAuthedGetString(_user, getRequestPath(new ObjectId()), 404);
  }

  /*
   Helper method for asserting a collection of Idps matches a serialized response. Does not assume any ordering.
  */
  private void assertIdpsMatchJsonArray(
      final Collection<IdentityProvider> pIdps, final JSONArray pSerializedArray) {
    // Cast everything to OidcIdentityProvider
    final List<OidcIdentityProvider> idps =
        pIdps.stream().map(idp -> (OidcIdentityProvider) idp).toList();

    // Ensure collections have the same size
    assertEquals(idps.size(), pSerializedArray.length());

    // Ensure uniqueness of IdP Ids
    assertEquals(idps.stream().map(OidcIdentityProvider::getId).count(), idps.size());

    // Map each IdP to the JSON object
    final Map<OidcIdentityProvider, JSONObject> idpMap = new HashMap<>();
    for (final OidcIdentityProvider idp : idps) {
      for (int i = 0; i < pSerializedArray.length(); i++) {
        if (pSerializedArray
            .getJSONObject(i)
            .getString(NDSOIDCIdentityProviderView.AUTH_NAME_PREFIX)
            .equals(idp.getId().toString())) {
          idpMap.put(idp, pSerializedArray.getJSONObject(i));
        }
      }
    }

    assertEquals(idpMap.size(), idps.size());
    idps.forEach(idp -> assertIdpMatchesJsonObject(idp, idpMap.get(idp)));
  }

  private void assertIdpMatchesJsonObject(
      final OidcIdentityProvider pIdp, final JSONObject pSerializedIdp) {
    assertEquals(
        pIdp.getId().toString(), pSerializedIdp.get(NDSOIDCIdentityProviderView.AUTH_NAME_PREFIX));
    assertEquals(
        Optional.ofNullable(pIdp.getAuthorizationType())
            .orElse(OidcIdentityProvider.AuthorizationType.GROUP)
            .toString(),
        pSerializedIdp.get(NDSOIDCIdentityProviderView.AUTHORIZATION_TYPE));
    assertEquals(
        Optional.ofNullable(pIdp.getIdpType())
            .orElse(OidcIdentityProvider.IdpType.WORKFORCE)
            .toString(),
        pSerializedIdp.get(NDSOIDCIdentityProviderView.IDP_TYPE));
    assertEquals(
        pIdp.getDisplayName(), pSerializedIdp.get(NDSOIDCIdentityProviderView.DISPLAY_NAME));
    assertEquals(
        pIdp.getDescription(), pSerializedIdp.get(NDSOIDCIdentityProviderView.DESCRIPTION));
  }
}
