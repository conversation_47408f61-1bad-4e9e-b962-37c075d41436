package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.FieldDefs;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.State;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessPushBasedLogExportFeatureUsage;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.featureid.NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSPushBasedLogExportResourceIntTests extends JUnit5BaseResourceTest {
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  private Group _group;
  private AppUser _user;
  private AppUser _userWithoutPerms;
  private AWSApiSvc _awsApiSvc;
  @Inject private AWSAccountDao _awsAccountDao;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createGlobalAtlasAdminUser(_group);
    _userWithoutPerms = MmsFactory.createGlobalReadOnlyUser();
    _ndsGroupSvc.ensureGroup(_group.getId());
    _awsApiSvc = mock(AWSApiSvc.class);

    mockCredentials();

    final AWSAccount account = new AWSAccount(NDSModelTestFactory.getFullyAvailableAWSAccount());
    _awsAccountDao.save(account);

    final NDSCloudProviderAccessAWSIAMRole role1 =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(new ObjectId(0, 0))
            .iamAssumedRoleArn("arn:aws:iam::************:role/buttered-role-arn-1")
            .atlasAssumedRoleExternalId(UUID.randomUUID().toString())
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(oid(1))
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    final NDSCloudProviderAccessAWSIAMRole role2 =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(new ObjectId(1, 1))
            .iamAssumedRoleArn("arn:aws:iam::************:role/buttered-role-arn-2")
            .atlasAssumedRoleExternalId(UUID.randomUUID().toString())
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(oid(1))
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    _ndsGroupDao.addAwsIamRoleToCloudProviderAccess(_group.getId(), role1);
    _ndsGroupDao.addAwsIamRoleToCloudProviderAccess(_group.getId(), role2);
  }

  @Test
  public void testGetPushBasedLogExport() throws SvcException {
    final String endpoint = String.format("/nds/%s/pushBasedLogExport", _group.getId());
    final PushBasedLogExport config =
        new PushBasedLogExport(
            State.ACTIVE,
            "bucket_test",
            "us-east-1",
            "prefix_test",
            new ObjectId(0, 0),
            PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS,
            new Date(0),
            new Date(0),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());
    _ndsGroupSvc.setPushBasedLogExportFields(_group.getId(), config, AuditInfoHelpers.fromSystem());
    assertEquals(config, _ndsGroupSvc.find(_group.getId()).get().getPushBasedLogExport());

    // on project without feature flag
    doAuthedJsonGet(_user, endpoint, HttpStatus.SC_FORBIDDEN);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT);

    // get the settings
    final JSONObject response = doAuthedJsonGet(_user, endpoint, HttpStatus.SC_OK);
    assertEquals(config.getState().toString(), response.getString("state"));
    assertEquals(config.getBucketName(), response.getString("bucketName"));
    assertEquals(config.getPrefixPath(), response.getString("prefixPath"));
    assertEquals(config.getIamRoleId().toString(), response.getString("iamRoleId"));
    assertNotNull(response.getString("createDate"));
  }

  @Test
  public void testDeletePushBasedLogExport() throws SvcException {
    final String endpoint = String.format("/nds/%s/pushBasedLogExport", _group.getId());
    final PushBasedLogExport config =
        new PushBasedLogExport(
            State.ACTIVE,
            "bucket_test",
            "us-east-1",
            "prefix_test",
            new ObjectId(0, 0),
            PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS,
            new Date(0),
            new Date(0),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());
    _ndsGroupSvc.setPushBasedLogExportFields(_group.getId(), config, AuditInfoHelpers.fromSystem());
    assertEquals(config, _ndsGroupSvc.find(_group.getId()).get().getPushBasedLogExport());

    // on project without feature flag
    doAuthedDelete(_user, endpoint, HttpStatus.SC_FORBIDDEN);

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT);

    // from user without needed perms
    doAuthedDelete(_userWithoutPerms, endpoint, HttpStatus.SC_FORBIDDEN);

    // delete the settings
    doAuthedDelete(_user, endpoint, HttpStatus.SC_OK);

    final PushBasedLogExport pbleAfter =
        _ndsGroupSvc.find(_group.getId()).get().getPushBasedLogExport();

    assertEquals(PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS, pbleAfter.getExportInterval());
    assertEquals(State.UNCONFIGURED, pbleAfter.getState());
    assertNull(pbleAfter.getBucketName());
    assertNull(pbleAfter.getBucketRegion());
    assertNull(pbleAfter.getPrefixPath());
    assertNull(pbleAfter.getIamRoleId());
    assertNull(pbleAfter.getCreateDate());
    assertTrue(config.getLastUpdateDate().before(pbleAfter.getLastUpdateDate()));

    assertEquals(
        Collections.emptyList(),
        _ndsGroupSvc.find(_group.getId()).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(new ObjectId(0, 0)))
            .findFirst()
            .get()
            .getFeatureUsages());
  }

  @Test
  public void testCreatePushBasedLogExport() {
    final String endpoint = String.format("/nds/%s/pushBasedLogExport", _group.getId());

    assertEquals(
        new PushBasedLogExport(), _ndsGroupSvc.find(_group.getId()).get().getPushBasedLogExport());

    final PushBasedLogExport config =
        new PushBasedLogExport(
            State.ACTIVE,
            "bucket_test",
            "us-east-1",
            "prefix/path",
            new ObjectId(0, 0),
            PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS,
            new Date(),
            new Date(),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());

    final JSONObject emptyBody = new JSONObject();
    final JSONObject partialBody =
        new JSONObject().put(FieldDefs.BUCKET_NAME, config.getBucketName());
    final JSONObject requestBody =
        new JSONObject()
            .put(FieldDefs.BUCKET_NAME, config.getBucketName())
            .put(FieldDefs.PREFIX_PATH, config.getPrefixPath())
            .put(FieldDefs.IAM_ROLE_ID, config.getIamRoleId());

    // on project without feature flag
    doAuthedJsonPost(_user, endpoint, requestBody, HttpStatus.SC_FORBIDDEN);

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT);

    // from user without needed perms
    doAuthedJsonPost(_userWithoutPerms, endpoint, requestBody, HttpStatus.SC_FORBIDDEN);

    // create with bad requests
    doAuthedJsonPost(_user, endpoint, emptyBody, HttpStatus.SC_BAD_REQUEST);
    doAuthedJsonPost(_user, endpoint, partialBody, HttpStatus.SC_BAD_REQUEST);

    // create the configuration
    doAuthedJsonPost(_user, endpoint, requestBody, HttpStatus.SC_OK);

    PushBasedLogExport pushBasedLogExport =
        _ndsGroupSvc.find(_group.getId()).get().getPushBasedLogExport();
    assertEquals(State.ACTIVE.name(), pushBasedLogExport.getState().name());
    assertEquals(config.getBucketName(), pushBasedLogExport.getBucketName());
    assertEquals(config.getPrefixPath(), pushBasedLogExport.getPrefixPath());
    assertEquals(config.getIamRoleId(), pushBasedLogExport.getIamRoleId());
    assertNotNull(pushBasedLogExport.getCreateDate());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _group.getId(), config.getBucketName()))),
        _ndsGroupSvc.find(_group.getId()).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(config.getIamRoleId()))
            .findFirst()
            .get()
            .getFeatureUsages());
  }

  @Test
  public void testUpdatePushBasedLogExport() throws SvcException {
    final String endpoint = String.format("/nds/%s/pushBasedLogExport", _group.getId());
    final PushBasedLogExport config =
        new PushBasedLogExport(
            State.ACTIVE,
            "bucket_test",
            "us-east-1",
            "prefix/path",
            new ObjectId(0, 0),
            PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS,
            new Date(0),
            new Date(0),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());
    final PushBasedLogExport oneChangeConfig =
        new PushBasedLogExport(
            config.getState(),
            "new_bucket_test",
            config.getBucketRegion(),
            config.getPrefixPath(),
            config.getIamRoleId(),
            config.getExportInterval(),
            config.getCreateDate(),
            config.getLastUpdateDate(),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());
    final PushBasedLogExport newConfig =
        new PushBasedLogExport(
            State.ACTIVE,
            "newer_bucket_test",
            "some-other-region",
            "new/prefix/path",
            new ObjectId(1, 1),
            PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS,
            new Date(1),
            new Date(1),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());

    final JSONObject partialBody =
        new JSONObject().put(FieldDefs.BUCKET_NAME, oneChangeConfig.getBucketName());
    final JSONObject requestBody =
        new JSONObject()
            .put(FieldDefs.BUCKET_NAME, newConfig.getBucketName())
            .put(FieldDefs.PREFIX_PATH, newConfig.getPrefixPath())
            .put(FieldDefs.IAM_ROLE_ID, newConfig.getIamRoleId());

    _ndsGroupSvc.setPushBasedLogExportFields(_group.getId(), config, AuditInfoHelpers.fromSystem());
    assertEquals(config, _ndsGroupSvc.getPushBasedLogExport(_group.getId()));

    // on project without feature flag
    doAuthedJsonPatch(_user, endpoint, requestBody, HttpStatus.SC_FORBIDDEN);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT);

    // from user without needed perms
    doAuthedJsonPatch(_userWithoutPerms, endpoint, requestBody, HttpStatus.SC_FORBIDDEN);

    // creating configuration with partial change
    doAuthedJsonPatch(_user, endpoint, partialBody, HttpStatus.SC_OK);
    final PushBasedLogExport afterPartialPatch = _ndsGroupSvc.getPushBasedLogExport(_group.getId());
    assertEquals(oneChangeConfig.getState(), afterPartialPatch.getState());
    assertEquals(oneChangeConfig.getBucketName(), afterPartialPatch.getBucketName());
    assertEquals(oneChangeConfig.getPrefixPath(), afterPartialPatch.getPrefixPath());
    assertEquals(oneChangeConfig.getIamRoleId(), afterPartialPatch.getIamRoleId());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _group.getId(), oneChangeConfig.getBucketName()))),
        _ndsGroupSvc.find(_group.getId()).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(oneChangeConfig.getIamRoleId()))
            .findFirst()
            .get()
            .getFeatureUsages());

    // creating configuration with full change
    doAuthedJsonPatch(_user, endpoint, requestBody, HttpStatus.SC_OK);
    final PushBasedLogExport fullPatch = _ndsGroupSvc.getPushBasedLogExport(_group.getId());
    assertEquals(newConfig.getState(), fullPatch.getState());
    assertEquals(newConfig.getBucketName(), fullPatch.getBucketName());
    assertEquals(newConfig.getPrefixPath(), fullPatch.getPrefixPath());
    assertEquals(newConfig.getIamRoleId(), fullPatch.getIamRoleId());
    assertEquals(
        List.of(
            new NDSCloudProviderAccessPushBasedLogExportFeatureUsage(
                new NDSCloudProviderAccessFeatureUsagePushBasedLogExportFeatureId(
                    _group.getId(), newConfig.getBucketName()))),
        _ndsGroupSvc.find(_group.getId()).get().getCloudProviderAccess().getAwsIamRoles().stream()
            .filter(role -> role.getRoleId().equals(newConfig.getIamRoleId()))
            .findFirst()
            .get()
            .getFeatureUsages());
  }

  @Test
  public void testValidatePushBasedLogExport() {
    final String endpoint = String.format("/nds/%s/pushBasedLogExport/validate", _group.getId());
    final PushBasedLogExport config =
        new PushBasedLogExport(
            State.ACTIVE,
            "bucket_test",
            "us-east-1",
            "prefix/path",
            new ObjectId(0, 0),
            PushBasedLogExport.DEFAULT_EXPORT_INTERVAL_SECONDS,
            new Date(0),
            new Date(0),
            NDSModelTestFactory.getPBLETempCredentialsForTest(),
            false,
            new Date());
    final JSONObject requestBody =
        new JSONObject()
            .put(FieldDefs.BUCKET_NAME, config.getBucketName())
            .put(FieldDefs.PREFIX_PATH, config.getPrefixPath())
            .put(FieldDefs.IAM_ROLE_ID, config.getIamRoleId());

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT);

    // Validation should succeed
    doAuthedJsonPost(_user, endpoint, requestBody, HttpStatus.SC_OK);

    // Validation should fail
    doThrow(IllegalArgumentException.class)
        .when(_awsApiSvc)
        .uploadObjectToS3(
            any(AWSCredentials.class),
            anyString(),
            anyString(),
            any(),
            any(),
            any(),
            anyBoolean(),
            any());
    doAuthedJsonPost(_user, endpoint, requestBody, HttpStatus.SC_BAD_REQUEST);
  }

  private void mockCredentials() throws NoSuchFieldException, IllegalAccessException {
    doReturn(List.of())
        .when(_awsApiSvc)
        .listObjects(any(), anyString(), anyString(), anyInt(), anyBoolean(), any());
    doNothing()
        .when(_awsApiSvc)
        .uploadObjectToS3(
            any(AWSCredentials.class),
            anyString(),
            anyString(),
            any(),
            any(),
            any(),
            anyBoolean(),
            any());
    doNothing().when(_awsApiSvc).deleteObject(any(), any(), any(), any());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_awsApiSvc", _awsApiSvc);

    final Credentials credentials = mock(Credentials.class);
    doReturn("accessKeyId").when(credentials).getAccessKeyId();
    doReturn("secretAccessKey").when(credentials).getSecretAccessKey();
    doReturn("sessionToken").when(credentials).getSessionToken();
    doReturn(new Date()).when(credentials).getExpiration();

    doReturn(credentials)
        .when(_awsApiSvc)
        .assumeRole(any(ObjectId.class), any(), any(), any(), any(), any(), any(), any(), any());

    doReturn(Optional.of(AWSRegionName.US_EAST_1))
        .when(_awsApiSvc)
        .getS3BucketRegion(any(), anyString(), anyBoolean(), any());
  }
}
