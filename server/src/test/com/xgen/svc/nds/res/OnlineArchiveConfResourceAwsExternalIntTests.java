package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.aws._public.clients.AWSClientsFactory;
import com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.AWSAccountBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.CollectionType;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.util.OnlineArchiveBucketsUtil;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class OnlineArchiveConfResourceAwsExternalIntTests extends JUnit5BaseResourceTest {
  private static final String BASE_URL = "/conf/onlinearchive";
  private static final String CONFIGURE_URL = BASE_URL + "/%s/cluster/%s?ah=%s&sk=%s&av=%s";
  private static final String GET_CREDENTIALS_URL =
      BASE_URL + "/%s/cluster/%s/tempCredentials?ah=%s&sk=%s";
  private static final String GET_DLZ_BUCKET_CREDENTIALS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/dataLandingZoneBucketCredentials?ah=%s&sk=%s";

  private static final String GROUP_NAME = "testGroup";
  private static final String AWS_CLUSTER_NAME = "awsTestCluster";
  private static final String AGENT_VERSION = "1.0";
  private static final String AWS_SESSION_KEY = "sk1";
  private static final String AWS_HOSTNAME = "hostname1";

  private static final String AWS_ACCESS_KEY = "local.aws.accessKey";
  private static final String AWS_SECRET_KEY = "local.aws.secretKey";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private OnlineArchiveSvc _onlineArchiveSvc;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private OrganizationSvc _orgSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private AppSettings _appSettings;
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private OnlineArchiveBucketsUtil _onlineArchiveBucketsUtil;

  private Group _group;
  private AgentApiKey _awsAgentApiKey;
  private AWSClientsFactory _clientsFactory;
  private ClusterDescription _awsClusterDescription;
  private AWSRegionName _awsRegion;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();

    _group = MmsFactory.createGroupWithNDSPlan(GROUP_NAME);
    final Organization org = _orgSvc.findById(_group.getOrgId());
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    final AppUser appUser = MmsFactory.createGroupOwnerUser(_group);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findDraftOrEmpty(_group.getId(), appUser.getId());
    final Auth auth = automationConfig.getDeployment().getAuth();
    auth.setAutoUser("username");
    auth.setAutoPwd("password");
    _automationConfigSvc.saveDraft(automationConfig, appUser, org, _group);
    _automationConfigSvc.publish(org, _group, appUser);
    _dataLakeTestUtils.setUp();

    _awsRegion = AWSRegionName.US_EAST_1;

    _awsAgentApiKey = MmsFactory.generateApiKey(_group.getId(), new ObjectId());
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), AWS_CLUSTER_NAME);
    clusterDescription.append(ClusterDescription.FieldDefs.UNIQUE_ID, ObjectId.get());
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);
    _awsClusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), AWS_CLUSTER_NAME).orElseThrow();

    // create instance hardware
    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final NDSGroup ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final ObjectId awsContainerId = _ndsGroupSvc.addCloudContainer(ndsGroup, awsContainer);

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(AWS_CLUSTER_NAME, _group.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
                ndsGroup.getGroupId(), AWS_CLUSTER_NAME, AWS_CLUSTER_NAME, List.of())
            .next()
            .rsId(),
        true,
        false,
        ObjectId.get());
    final AWSInstanceHardware awsInstanceHardware =
        new AWSInstanceHardware(
            InstanceHardware.getEmptyHardware(CloudProvider.AWS, ObjectId.get(), new Date(), 0)
                .append(InstanceHardware.FieldDefs.CLOUD_PROVIDER_CONTAINER_ID, awsContainerId)
                .append(
                    InstanceHardware.FieldDefs.HOSTNAMES,
                    new Hostnames(AWS_HOSTNAME, AWS_HOSTNAME).toDBList())
                .append(InstanceHardware.FieldDefs.PROVISIONED, true)
                .append(
                    InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                    InstanceHostname.HostnameScheme.PUBLIC.name())
                .append(InstanceHardware.FieldDefs.PUBLIC_IP, getAWSExternalIpAddress()));

    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), List.of(awsInstanceHardware), false));

    // setup aws accounts
    final String awsAccessKey = _appSettings.getStrProp(AWS_ACCESS_KEY);
    final String awsSecretKey = _appSettings.getStrProp(AWS_SECRET_KEY);

    final AWSAccount awsAccount =
        new AWSAccountBuilder()
            .setAccessKey(awsAccessKey)
            .setSecretKey(awsSecretKey)
            .setForOnlineArchive(true)
            .setForOnlineArchiveDataLandingZone(true)
            .build();

    _awsAccountDao.save(awsAccount);
    _clientsFactory = new AWSClientsFactory(_appSettings.getNDSGovUSEnabled());
  }

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testGetCredentials() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(AWS_CLUSTER_NAME);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    final String bucket = _onlineArchiveBucketsUtil.getOnlineArchiveBucketName(_awsRegion);

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _awsAgentApiKey,
        String.format(
            CONFIGURE_URL,
            _group.getId(),
            AWS_CLUSTER_NAME,
            AWS_HOSTNAME,
            AWS_SESSION_KEY,
            AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final JSONObject response =
        new JSONObject(
            doAgentApiCallGet(
                _group,
                _awsAgentApiKey,
                String.format(
                    GET_CREDENTIALS_URL,
                    _group.getId(),
                    AWS_CLUSTER_NAME,
                    AWS_HOSTNAME,
                    AWS_SESSION_KEY)));

    assertNotNull(response.getString("accessKey"));
    assertNotNull(response.getString("secretKey"));
    assertNotNull(response.getString("sessionToken"));
    assertNotNull(response.getString("expirationDate"));

    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(
            response.getString("accessKey"),
            response.getString("secretKey"),
            response.getString("sessionToken"),
            null);

    final AmazonS3 s3Client = _clientsFactory.getS3Client(provider, _awsRegion.getValue());

    // check for 403
    try {
      InputStream stream = new ByteArrayInputStream("hi".getBytes());
      PutObjectRequest request =
          new PutObjectRequest(bucket, "bad_key.json", stream, new ObjectMetadata());
      s3Client.putObject(request);
      fail();
    } catch (final AmazonS3Exception pE) {
      assertEquals(HttpStatus.SC_FORBIDDEN, pE.getStatusCode());
    }

    final String allowedKey =
        String.format("%s/%s/hi.json", _group.getId(), _awsClusterDescription.getUniqueId());
    try {

      InputStream stream = new ByteArrayInputStream("hi".getBytes());
      PutObjectRequest request =
          new PutObjectRequest(bucket, allowedKey, stream, new ObjectMetadata());
      s3Client.putObject(request);
    } catch (final AmazonS3Exception pE) {
      assertEquals(HttpStatus.SC_NOT_FOUND, pE.getStatusCode());
    } finally {
      s3Client.deleteObject(bucket, allowedKey);
    }

    // pass
  }

  @Test
  public void testGetArchiveDataLandingZoneBucketAWSCredentials() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(AWS_CLUSTER_NAME);

    final String archiveS3Path = "foo/bar";
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveDao.updateArchiveS3Path(onlineArchive.getId(), archiveS3Path);
    _onlineArchiveDao.updateOnlineArchiveVersion(onlineArchive.getId(), OnlineArchiveVersion.V3);

    final String bucket = _onlineArchiveBucketsUtil.getOnlineArchiveBucketName(_awsRegion);

    _onlineArchiveDao.updateDataLandingZoneBucketName(onlineArchive.getId(), bucket);
    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _awsAgentApiKey,
        String.format(
            CONFIGURE_URL,
            _group.getId(),
            AWS_CLUSTER_NAME,
            AWS_HOSTNAME,
            AWS_SESSION_KEY,
            AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final JSONObject response =
        new JSONObject(
            doAgentApiCallGet(
                _group,
                _awsAgentApiKey,
                String.format(
                    GET_DLZ_BUCKET_CREDENTIALS_URL,
                    _group.getId(),
                    AWS_CLUSTER_NAME,
                    onlineArchive.getId(),
                    AWS_HOSTNAME,
                    AWS_SESSION_KEY)));

    assertNotNull(response.getString("accessKey"));
    assertNotNull(response.getString("secretKey"));
    assertNotNull(response.getString("sessionToken"));
    assertNotNull(response.getString("expirationDate"));

    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(
            response.getString("accessKey"),
            response.getString("secretKey"),
            response.getString("sessionToken"),
            null);

    final AmazonS3 s3Client = _clientsFactory.getS3Client(provider, _awsRegion.getValue());

    // check for 403
    try {
      InputStream stream = new ByteArrayInputStream("hi".getBytes());
      PutObjectRequest request =
          new PutObjectRequest(bucket, "bad_key.json", stream, new ObjectMetadata());
      s3Client.putObject(request);
      fail();
    } catch (final AmazonS3Exception pE) {
      assertEquals(HttpStatus.SC_FORBIDDEN, pE.getStatusCode());
    }

    final String allowedKey = String.format("%s/hi.json", archiveS3Path);
    try {

      InputStream stream = new ByteArrayInputStream("hi".getBytes());
      PutObjectRequest request =
          new PutObjectRequest(bucket, allowedKey, stream, new ObjectMetadata());
      s3Client.putObject(request);
    } catch (final AmazonS3Exception pE) {
      assertEquals(HttpStatus.SC_NOT_FOUND, pE.getStatusCode());
    } finally {
      s3Client.deleteObject(bucket, allowedKey);
    }
  }

  private OnlineArchive getOnlineArchive(final String clusterName) {
    return getOnlineArchive(OnlineArchiveVersion.V1, clusterName);
  }

  private OnlineArchive getOnlineArchive(
      final OnlineArchiveVersion pVersion, final String clusterName) {
    final String dbName = "mydb";
    final UUID collectionUUID = new UUID(1L, 1L);
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("string", "field1", 0),
            new PartitionField("string", "field2", 1),
            new PartitionField("date", "dateField", 2));

    return new OnlineArchive.Builder()
        .setArchiveId(ObjectId.get())
        .setClusterId(_group.getId(), clusterName)
        .setDbName(dbName)
        .setCollName("mycoll")
        .setCollectionUUID(collectionUUID)
        .setPartitionFields(partitionFields)
        .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
        .setState(OnlineArchive.State.ACTIVE)
        .setCollectionType(CollectionType.STANDARD)
        .setOnlineArchiveVersion(pVersion)
        .build();
  }

  private String getAWSExternalIpAddress() {
    try {
      return HttpUtils.getInstance().doGetStr("http://checkip.amazonaws.com/").trim();
    } catch (Exception e) {
      return "Unknown";
    }
  }
}
