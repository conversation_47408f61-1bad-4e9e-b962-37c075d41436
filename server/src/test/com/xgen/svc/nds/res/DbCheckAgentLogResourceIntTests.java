package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.agent._public.model.AgentLog;
import com.xgen.cloud.common.agent._public.model.AgentLogView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;

public class DbCheckAgentLogResourceIntTests extends JUnit5BaseResourceTest {
  private Group _group;
  private AppUser _user;
  private AgentApiKey _agentApiKey;
  private AppUser _lowPermissionUser;
  private AppUser _groupAdminUser;
  private AppUser _tseUser;
  private AppUser _monitoringUser;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(organization, "cus_0001");
    _user = MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), _user.getId());

    _lowPermissionUser =
        MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_READ_ONLY);
    _groupAdminUser = MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_OWNER);
    _tseUser = MmsFactory.createGlobalAtlasTSEUser(_group);
    _monitoringUser = MmsFactory.createMonitoringAdminUser(_group);
  }

  @Test
  public void postBatch() throws Exception {
    final long secondTimestamp = System.currentTimeMillis();
    final long firstTimestamp = secondTimestamp - 1000L;

    final var withoutThrowable =
        new AgentLogView(
            1682618282766L,
            "error",
            "dbcheckmodule/module.go:62",
            "dbcheck-module",
            "message one",
            "");
    final var withThrowable =
        new AgentLogView(
            1682618282763L,
            "error",
            "dbcheckmodule/module.go:62",
            "dbcheck-module",
            "message two",
            "throw me");
    final ObjectMapper mapper = new ObjectMapper();

    final JSONArray jsonArray = new JSONArray();
    {
      final JSONObject objOne = new JSONObject(mapper.writeValueAsString(withoutThrowable));
      final JSONObject objTwo = new JSONObject(mapper.writeValueAsString(withThrowable));
      jsonArray.put(objOne).put(objTwo);
    }

    final String fakeHostname = "fakeHost";
    final String fakeSessionKey = "fakeSessionKey";
    final String postEndpoint =
        String.format(
            "/agents/dbcheck/logs/%s/?ah=" + fakeHostname + "&sk=" + fakeSessionKey,
            _group.getId());
    assertEquals("{}", doAgentApiCallPost(_group, _agentApiKey, postEndpoint, jsonArray));

    final String uiGetEndpoint = String.format("/agents/dbcheck/logs/%s", _group.getId());
    doAuthedJsonGet(_lowPermissionUser, uiGetEndpoint, HttpStatus.SC_FORBIDDEN);
    doAuthedJsonGet(_groupAdminUser, uiGetEndpoint, HttpStatus.SC_FORBIDDEN);
    final JSONObject response = doAuthedJsonGet(_tseUser, uiGetEndpoint, HttpStatus.SC_OK);
    doAuthedJsonGet(_monitoringUser, uiGetEndpoint, HttpStatus.SC_OK);
    final String debugStr = response.toString();

    final JSONArray actualEntries = response.getJSONArray("entries");
    assertEquals(2, actualEntries.length(), "Unexpected dbcheck module logs response: " + debugStr);

    // results in descending timestamp order
    final JSONObject firstEntry = actualEntries.getJSONObject(0);
    // response includes groupId and _id
    assertNotNull(firstEntry.remove("_id"));
    assertNotNull(firstEntry.remove("groupId"));

    // disconnect from online archive - response (and db entry) keeps long timestamp. Doesn't
    // rename throwable.
    final AgentLog expectedFirst =
        withoutThrowable.toAgentLog(_group.getId(), fakeHostname, fakeSessionKey);
    final JSONObject stringyExpectation = new JSONObject(mapper.writeValueAsString(expectedFirst));
    stringyExpectation.remove("groupId");

    JSONAssert.assertEquals(stringyExpectation, firstEntry, true);

    final JSONObject secondEntry = actualEntries.getJSONObject(1);
    final AgentLog expectedSecond =
        withThrowable.toAgentLog(_group.getId(), fakeHostname, fakeSessionKey);
    final JSONObject stringySecond = new JSONObject(mapper.writeValueAsString(expectedSecond));
    // response includes groupId and _id
    assertNotNull(secondEntry.remove("_id"));
    assertNotNull(secondEntry.remove("groupId"));
    stringySecond.remove("groupId");

    JSONAssert.assertEquals(stringySecond, secondEntry, true);
  }
}
