package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig.RolloutType;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.ifr._private.dao.IFREventDao;
import com.xgen.cloud.nds.ifr._private.dao.WaveDao;
import com.xgen.cloud.nds.ifr._public.model.IFREvent;
import com.xgen.cloud.nds.ifr._public.model.IFREvent.IFREventType;
import com.xgen.cloud.nds.ifr._public.model.WaveAllocationInternal;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.versions.PhasedVersionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.versions.IFRState;
import com.xgen.cloud.nds.project._public.model.versions.IFRState.WaveStatus;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.IFREventView;
import com.xgen.svc.nds.model.ui.IFRInfoView.FieldDefs;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSAdminIFRResourceIntTests extends JUnit5BaseResourceTest {

  private static final String GET_IFR_INFO_URL = "/admin/nds/groups/%s/clusters/%s/ifrInfo";
  private static final String GET_IFR_EVENTS_URL =
      "/admin/nds/groups/%s/clusters/%s/ifrRolloutEvents";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private WaveDao _waveDao;
  @Inject private IFREventDao _ifrEventDao;
  @Inject private PhasedVersionDao _phasedVersionDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private PhasedVersionFactory _phasedVersionFactory;

  private AppUser _globalAdminUser;
  private Group _group;
  private ClusterDescription _clusterDescription;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _globalAdminUser = MmsFactory.createGlobalOwnerAdminUser(_group);
    _ndsGroupSvc.ensureGroup(_group.getId());
    _clusterDescription =
        NDSModelTestFactory.getClusterDescription(_group.getId(), "cluster0", CloudProvider.AWS);
    _clusterDescriptionDao.save(_clusterDescription);
    final BasicDBObject replicasetHardware =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), _clusterDescription);
    _replicaSetHardwareDao.saveReplicaSafe(replicasetHardware);
    CloudProviderRegistryUtil.registerAllProvider();
  }

  @Test
  void testGetIfrInfo() {
    {
      // Cluster not found
      doAuthedGetBytes(
          _globalAdminUser,
          String.format(GET_IFR_INFO_URL, _group.getId().toString(), "cluster1"),
          HttpStatus.SC_NOT_FOUND);
    }

    {
      // No IFR experiment found
      final JSONObject response =
          doAuthedJsonGet(
              _globalAdminUser,
              String.format(GET_IFR_INFO_URL, _group.getId().toString(), "cluster0"),
              HttpStatus.SC_OK);
      assertNull(response.opt(FieldDefs.EXPERIMENT_ID));
    }

    {
      // IFR experiment found and no ifr events found
      final ObjectId experimentId = prepareIfrExperiment();
      final JSONObject response =
          doAuthedJsonGet(
              _globalAdminUser,
              String.format(GET_IFR_INFO_URL, _group.getId().toString(), "cluster0"),
              HttpStatus.SC_OK);
      assertEquals(response.getString(FieldDefs.EXPERIMENT_ID), experimentId.toString());
      assertEquals(response.getString(FieldDefs.EXPERIMENT_STATUS), "LIVE");
      assertEquals(response.getString(FieldDefs.ROLLOUT_TYPE), "BINARY");
      assertFalse(response.getBoolean(FieldDefs.TREAT_APPLIED_TO_CLUSTER));
      assertNotNull(response.getString(FieldDefs.PHOLIOTA_LINK));

      // IFR experiment found and ifr events found
      _ifrEventDao.saveEvent(
          IFREvent.validated(
              new ObjectId(),
              experimentId,
              _clusterDescription.getUniqueId(),
              IFREventType.END_OF_CLUSTER_UPDATE,
              1,
              false,
              new Date()));

      final JSONObject response1 =
          doAuthedJsonGet(
              _globalAdminUser,
              String.format(GET_IFR_INFO_URL, _group.getId().toString(), "cluster0"),
              HttpStatus.SC_OK);
      assertTrue(response1.getBoolean(FieldDefs.TREAT_APPLIED_TO_CLUSTER));
    }
  }

  @Test
  void testGetIfrEvents() {
    {
      // Cluster not found
      doAuthedGetBytes(
          _globalAdminUser,
          String.format(GET_IFR_EVENTS_URL, _group.getId().toString(), "cluster1"),
          HttpStatus.SC_NOT_FOUND);
    }

    {
      // No IFR experiment found
      final JSONArray response =
          doAuthedJsonArrayGet(
              _globalAdminUser,
              String.format(GET_IFR_EVENTS_URL, _group.getId().toString(), "cluster0"),
              HttpStatus.SC_OK);
      assertTrue(response.isEmpty());
    }

    {
      // IFR experiment found and ifr events found
      final ObjectId experimentId = prepareIfrExperiment();
      _ifrEventDao.saveEvent(
          IFREvent.validated(
              new ObjectId(),
              experimentId,
              _clusterDescription.getUniqueId(),
              IFREventType.END_OF_CLUSTER_UPDATE,
              1,
              false,
              new Date()));
      final JSONArray response =
          doAuthedJsonArrayGet(
              _globalAdminUser,
              String.format(GET_IFR_EVENTS_URL, _group.getId().toString(), "cluster0"),
              HttpStatus.SC_OK);
      assertEquals(1, response.length());

      final JSONObject event = response.getJSONObject(0);
      assertEquals(event.getString(IFREventView.FieldDefs.EXPERIMENT_ID), experimentId.toString());
      assertEquals(
          event.getString(IFREventView.FieldDefs.CLUSTER_UNIQUE_ID),
          _clusterDescription.getUniqueId().toString());
      assertEquals(event.getString(IFREventView.FieldDefs.TYPE), "END_OF_CLUSTER_UPDATE");
      assertEquals(event.getInt(IFREventView.FieldDefs.WAVE), 1);
      assertFalse(event.getBoolean(IFREventView.FieldDefs.HAS_OVERRIDE));
      assertNotNull(event.getString(IFREventView.FieldDefs.CREATION_DATE));
    }
  }

  private ObjectId prepareIfrExperiment() {
    ObjectId experimentId = new ObjectId();
    _waveDao.insertMajority(
        new WaveAllocationInternal(
            experimentId, _clusterDescription.getUniqueId(), 1, new java.util.Date(), null));
    final PhasedVersion continuousDeliveryVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                    SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "8.1.0-rc0")
                .setReleaseMode(ReleaseMode.IFR)
                .setIfrState(
                    new IFRState(
                        experimentId,
                        ExperimentStatus.LIVE,
                        RolloutType.BINARY,
                        1,
                        WaveStatus.FINALIZED)));
    _phasedVersionDao.save(continuousDeliveryVersion);
    return experimentId;
  }
}
