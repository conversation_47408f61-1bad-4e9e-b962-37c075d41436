package com.xgen.svc.nds.res;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.xgen.cloud.access.authn._public.model.AutomationAgentCredentials;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.view.AutomationAgentPrincipalConfirmationView;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ApiPrivateAutomationAgentAuthResourceIntTests extends JUnit5BaseResourceTest {

  private static final String AGENT_API_AUTH_ENDPOINT = "/api/private/unauth/agents/auth";
  private static final String AGENT_IP = "*********";

  private Group group;
  private AgentApiKey agentApiKey;
  private AgentApiKey otherAgentApiKey;
  private AgentApiKey agentApiKeyUnboundedIp;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("TestOrg");
    group = MmsFactory.createGroup(organization, "group1");
    AppUser user =
        MmsFactory.createUser(group, String.format("<EMAIL>", getUniquifier()));
    agentApiKey =
        MmsFactory.generateApiKeyWithIPBinding(group.getId(), user.getId(), true, AGENT_IP);

    Group otherGroup = MmsFactory.createGroup(organization, "group2");
    otherAgentApiKey = MmsFactory.generateApiKey(otherGroup.getId(), user.getId());

    agentApiKeyUnboundedIp =
        MmsFactory.generateApiKeyWithIPBinding(group.getId(), user.getId(), false, AGENT_IP);
  }

  @Test
  public void auth_succeedsAndReturnsPrincipal() {
    final AutomationAgentPrincipalConfirmationView principalConfirmationView =
        getHttpUtils()
            .post()
            .path(AGENT_API_AUTH_ENDPOINT)
            .data(new AutomationAgentCredentials(group.getId(), agentApiKey.getKey(), AGENT_IP))
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(AutomationAgentPrincipalConfirmationView.class)
            .send();

    assertThat(principalConfirmationView, notNullValue());
    assertThat(principalConfirmationView.username(), is(agentApiKey.getId().toHexString()));
    assertThat(principalConfirmationView.id(), is(agentApiKey.getId().toHexString()));
    assertThat(principalConfirmationView.agentKeySource(), is(AgentApiKey.KeySource.USER));
  }

  @Test
  public void auth_failsAndReturnsUnauthorizedResponse_invalidKey() {
    getHttpUtils()
        .post()
        .path(AGENT_API_AUTH_ENDPOINT)
        .data(new AutomationAgentCredentials(group.getId(), "invalid", AGENT_IP))
        .expectedReturnStatus(HttpStatus.SC_UNAUTHORIZED)
        .returnType(AutomationAgentPrincipalConfirmationView.class)
        .send();
  }

  @Test
  public void auth_failsAndReturnsUnauthorizedResponse_invalidGroup() {
    getHttpUtils()
        .post()
        .path(AGENT_API_AUTH_ENDPOINT)
        .data(new AutomationAgentCredentials(ObjectId.get(), "invalid", AGENT_IP))
        .expectedReturnStatus(HttpStatus.SC_UNAUTHORIZED)
        .returnType(AutomationAgentPrincipalConfirmationView.class)
        .send();
  }

  @Test
  public void auth_failsAndReturnsUnauthorizedResponse_otherGroupAgentApiKey() {
    getHttpUtils()
        .post()
        .path(AGENT_API_AUTH_ENDPOINT)
        .data(new AutomationAgentCredentials(group.getId(), otherAgentApiKey.getKey(), AGENT_IP))
        .expectedReturnStatus(HttpStatus.SC_UNAUTHORIZED)
        .returnType(AutomationAgentPrincipalConfirmationView.class)
        .send();
  }

  @Test
  public void auth_succeedsAndReturnsPrincipal_unboundedIp() {
    final AutomationAgentPrincipalConfirmationView principalConfirmationView =
        getHttpUtils()
            .post()
            .path(AGENT_API_AUTH_ENDPOINT)
            .data(
                new AutomationAgentCredentials(
                    group.getId(), agentApiKeyUnboundedIp.getKey(), "*********"))
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(AutomationAgentPrincipalConfirmationView.class)
            .send();

    assertThat(principalConfirmationView, notNullValue());
    assertThat(
        principalConfirmationView.username(), is(agentApiKeyUnboundedIp.getId().toHexString()));
    assertThat(principalConfirmationView.id(), is(agentApiKeyUnboundedIp.getId().toHexString()));
    assertThat(principalConfirmationView.agentKeySource(), is(AgentApiKey.KeySource.USER));
  }

  @Test
  public void auth_failsAndReturnsUnauthorizedResponse_incorrectBoundedIp() {
    getHttpUtils()
        .post()
        .path(AGENT_API_AUTH_ENDPOINT)
        .data(new AutomationAgentCredentials(group.getId(), agentApiKey.getKey(), "*********"))
        .expectedReturnStatus(HttpStatus.SC_UNAUTHORIZED)
        .returnType(AutomationAgentPrincipalConfirmationView.class)
        .send();
  }
}
