package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.serverless._private.dao.AtlasUISKeysDao;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMPoolDao;
import com.xgen.cloud.nds.serverless._public.model.AtlasUISKey;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.strategy.ServerlessAutoScalingStrategyType;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.serverless._public.svc.ServerlessEnvoySSHKeysSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.serverless.model.ServerlessTestFactory;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class EnvoyInstanceResourceIntTests extends JUnit5BaseResourceTest {
  private static final String BASE_URL = "/admin/nds/envoyInstances";

  private AppUser _viewer;
  private AppUser _operator;
  private AppUser _admin;

  @Inject ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  @Inject ServerlessEnvoySSHKeysSvc _serverlessEnvoySSHKeysSvc;
  @Inject private ServerlessMTMPoolDao _serverlessMTMPoolDao;
  @Inject private AtlasUISKeysDao _atlasUISKeysDao;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _viewer = MmsFactory.createGlobalAdminReadOnlyUser();
    _operator = MmsFactory.createGlobalAtlasOperatorUser(MmsFactory.createGroupWithStandardPlan());
    _admin = MmsFactory.createGlobalAtlasAdminUser(MmsFactory.createGroupWithStandardPlan());
  }

  @Test
  public void testGetEnvoyInstances() {
    final JSONArray arrayPreCreate =
        HttpUtils.getInstance()
            .get()
            .path(BASE_URL)
            .uiAuth(_viewer)
            .returnType(JSONArray.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();
    assertEquals(0, arrayPreCreate.length());

    final EnvoyInstance envoyInstance =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(ObjectId.get(), 0);
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        new ServerlessLoadBalancingDeployment.Builder()
            .setEnvoyInstances(List.of(envoyInstance))
            .setCloudProvider(CloudProvider.AWS)
            .setRegionName(AWSNDSDefaults.REGION_NAME)
            .setDesiredInstanceSize(AWSNDSInstanceSize.M10)
            .setDesiredInstanceFamily(AWSInstanceFamily.T3)
            .build();
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);

    final JSONArray arrayPostCreate =
        HttpUtils.getInstance()
            .get()
            .path(BASE_URL)
            .uiAuth(_viewer)
            .returnType(JSONArray.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();
    assertEquals(1, arrayPostCreate.length());
  }

  @Test
  public void testReplaceEnvoyInstance() {
    final ObjectId instanceId = ObjectId.get();
    final EnvoyInstance envoyInstance =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(instanceId, 0);
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        new ServerlessLoadBalancingDeployment.Builder()
            .setGroupId(new ObjectId())
            .setEnvoyInstances(List.of(envoyInstance))
            .setCloudProvider(CloudProvider.AWS)
            .setRegionName(AWSNDSDefaults.REGION_NAME)
            .setDesiredInstanceSize(AWSNDSInstanceSize.M10)
            .setDesiredInstanceFamily(AWSInstanceFamily.T3)
            .build();
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);

    final JSONObject params = new JSONObject();
    HttpUtils.getInstance()
        .post()
        .path(BASE_URL + "/%s", instanceId.toString())
        .uiAuth(_operator)
        .data(params)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeploymentAfterPost =
        _serverlessLoadBalancingDeploymentDao.findAll().get(0);
    assertNotNull(
        serverlessLoadBalancingDeploymentAfterPost
            .getEnvoyInstances()
            .get(0)
            .getReplacementRequestedDate());
  }

  @Test
  public void testRotateUISKeysForEnvoyInstance() {
    final ObjectId instanceId = ObjectId.get();
    final EnvoyInstance envoyInstance =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(instanceId, 0);
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        new ServerlessLoadBalancingDeployment.Builder()
            .setGroupId(new ObjectId())
            .setEnvoyInstances(List.of(envoyInstance))
            .setCloudProvider(CloudProvider.AWS)
            .setRegionName(AWSNDSDefaults.REGION_NAME)
            .setDesiredInstanceSize(AWSNDSInstanceSize.M10)
            .setDesiredInstanceFamily(AWSInstanceFamily.T3)
            .build();
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);
    final ServerlessMTMPool pool =
        ServerlessTestFactory.getServerlessMTMPool(
            new ObjectId(),
            "pool0",
            serverlessLoadBalancingDeployment.getGroupId(),
            CloudProvider.AWS,
            ServerlessAutoScalingStrategyType.V2,
            new ArrayList<>(),
            new ArrayList<>(),
            true,
            true,
            new ArrayList<>());
    _serverlessMTMPoolDao.createPool(pool);
    final ObjectId newKeyId = new ObjectId();
    _atlasUISKeysDao.createKey(
        new AtlasUISKey(
            newKeyId,
            pool.getId(),
            serverlessLoadBalancingDeployment.getEnvoyInstances().get(0).getId(),
            Date.from(Instant.now()),
            "TestPEM",
            null,
            null));

    final JSONObject params = new JSONObject();

    // Unauthorized
    HttpUtils.getInstance()
        .post()
        .path(BASE_URL + "/rotateUISKeys/%s", instanceId)
        .uiAuth(_viewer)
        .data(params)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();

    // Authorized
    HttpUtils.getInstance()
        .post()
        .path(BASE_URL + "/rotateUISKeys/%s", instanceId)
        .uiAuth(_admin)
        .data(params)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    final AtlasUISKey key = _atlasUISKeysDao.findKeyById(newKeyId).get();
    assertNotNull(key.getRotateKeysAfter());
    assertTrue(key.getRotateKeysAfter().before(Date.from(Instant.now())));
  }

  @Test
  public void testReplaceEnvoyInstance_UnauthorizedUser() {
    final ObjectId instanceId = ObjectId.get();
    final EnvoyInstance envoyInstance =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(instanceId, 0);
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        new ServerlessLoadBalancingDeployment.Builder()
            .setEnvoyInstances(List.of(envoyInstance))
            .setCloudProvider(CloudProvider.AWS)
            .setRegionName(AWSNDSDefaults.REGION_NAME)
            .setDesiredInstanceSize(AWSNDSInstanceSize.M10)
            .setDesiredInstanceFamily(AWSInstanceFamily.T3)
            .build();
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);
    final JSONObject params = new JSONObject();

    HttpUtils.getInstance()
        .post()
        .path(BASE_URL + "/%s", instanceId.toString())
        .uiAuth(_viewer)
        .data(params)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testGetEnvoySshKeyPassphrase() {
    final ObjectId groupId = ObjectId.get();
    _serverlessEnvoySSHKeysSvc.createEnvoySshKeys(groupId);

    final JSONObject passphraseObject =
        HttpUtils.getInstance()
            .get()
            .path(BASE_URL + "/%s/sshPassphrase", groupId.toString())
            .uiAuth(_admin)
            .returnType(JSONObject.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    assertNotNull(passphraseObject);
    assertTrue(passphraseObject.has("passphrase"));
  }
}
