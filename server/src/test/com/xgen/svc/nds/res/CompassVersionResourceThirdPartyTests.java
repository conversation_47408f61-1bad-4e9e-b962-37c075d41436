package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertFalse;

import com.xgen.svc.core.JUnit5BaseResourceTest;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;

public class CompassVersionResourceThirdPartyTests extends JUnit5BaseResourceTest {

  @Test
  public void testGetLatestStableVersion() throws JSONException {
    JSONObject latestStableVersion = doJsonGet("/nds/compass/versions/latestStable");

    assertFalse(latestStableVersion.isNull("_id"));
    assertFalse(latestStableVersion.isNull("version"));
    assertFalse(latestStableVersion.isNull("platform"));
  }
}
