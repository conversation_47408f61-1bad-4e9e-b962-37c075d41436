package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSCloudProviderContainerDao;
import com.xgen.cloud.nds.aws._private.dao.AWSTenantEndpointServiceDeploymentDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.azure._private.dao.AzureCloudProviderContainerDao;
import com.xgen.cloud.nds.azure._private.dao.AzureTenantEndpointServiceDeploymentDao;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.api.view.atlas.serverless.privatenetworking.ApiAtlasServerlessAzureTenantEndpointView;
import com.xgen.svc.mms.api.view.atlas.serverless.privatenetworking.ApiAtlasServerlessTenantEndpointUpdateView;
import com.xgen.svc.mms.api.view.atlas.serverless.privatenetworking.ApiAtlasServerlessTenantEndpointView;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.azure.model.ui.ServerlessAzureTenantEndpointView;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory;
import com.xgen.svc.nds.model.ui.ServerlessTenantEndpointView.FieldDefs;
import com.xgen.svc.nds.model.ui.ServerlessTenantEndpointView.Status;
import com.xgen.svc.nds.res.util.ServerlessTenantEndpointUtil;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Set;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.JSONAssert;

@ExtendWith(GuiceTestExtension.class)
public class NDSServerlessTenantEndpointResourceIntTests extends JUnit5BaseResourceTest {
  private static final String BASE_URL = "/nds/%s/privateEndpoint/serverless/instance/%s/endpoint";

  private static final String ERROR_CODE_FIELD = "errorCode";
  private static final String UPDATED_COMMENT = "This is a comment about this endpoint.";
  private static final String TOO_LONG_COMMENT =
      "I am a comment that is way too long and should "
          + "throw an error because I'm longer than the 80 character maximum.";
  private static final String UNSAFE_COMMENT = "{not xss safe}";

  private AppUser _user;
  private ObjectId _groupId;

  @Inject private AppSettings _appSettings;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private NDSTenantEndpointSvc _ndsTenantEndpointSvc;
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private AWSCloudProviderContainerDao _awsCloudProviderContainerDao;
  @Inject private AzureCloudProviderContainerDao _azureCloudProviderContainerDao;
  @Inject private AWSTenantEndpointServiceDeploymentDao _awsTenantEndpointServiceDeploymentDao;
  @Inject private AzureTenantEndpointServiceDeploymentDao _azureTenantEndpointServiceDeploymentDao;
  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private GroupSvc _groupSvc;
  private ServerlessTenantEndpointUtil _serverlessTenantEndpointUtil;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _serverlessTenantEndpointUtil =
        new ServerlessTenantEndpointUtil(
            _featureFlagSvc,
            _awsCloudProviderContainerDao,
            _azureCloudProviderContainerDao,
            _awsTenantEndpointServiceDeploymentDao,
            _azureTenantEndpointServiceDeploymentDao,
            _mtmClusterDao,
            _clusterDescriptionDao,
            _ndsGroupDao,
            _ndsGroupSvc,
            _groupSvc,
            _appSettings);

    createTenantResources();
  }

  private void createTenantResources() throws SvcException {
    Organization organization = MmsFactory.createOrganizationWithNDSPlan(false);
    paymentMethodStubber.stubPaymentMethod(organization.getId());
    Group group = MmsFactory.createGroup(organization, "testGroup", ObjectId.get(), false);
    _user = MmsFactory.createUser(group);

    NDSGroup ndsGroup =
        _ndsGroupSvc.ensureGroup(group.getId(), false, RegionUsageRestrictions.NONE, false);
    _groupId = ndsGroup.getGroupId();

    // set up accounts
    _awsAccountDao.save(new AWSAccount(NDSModelTestFactory.getFullyAvailableAWSAccount()));
  }

  @Test
  public void testCreateEndpoint_AWS() throws SvcException {
    assertCreateEndpoint(CloudProvider.AWS);
  }

  @Test
  public void testCreateEndpoint_AZURE() throws SvcException {
    assertCreateEndpoint(CloudProvider.AZURE);
  }

  private void assertCreateEndpoint(final CloudProvider pCloudProvider) throws SvcException {
    final ClusterDescription tenantInstance =
        _serverlessTenantEndpointUtil.createServerlessInstanceForBackingCloudProvider(
            pCloudProvider, _groupId);
    paymentMethodStubber.stubPaymentMethod(_groupSvc.findById(_groupId).getOrgId());
    final String tenantInstanceName = tenantInstance.getName();

    final JSONObject response = createDefaultEndpoint(tenantInstanceName);

    assertEquals(response.getString(FieldDefs.COMMENT), "");
    assertEquals(Status.RESERVATION_REQUESTED.name(), response.getString(FieldDefs.STATUS));
    assertEquals(pCloudProvider.name(), response.getString(FieldDefs.PROVIDER_NAME));
    assertTrue(response.isNull(FieldDefs.ERROR_MESSAGE));
    assertTrue(response.isNull(FieldDefs.ENDPOINT_SERVICE_NAME));
    assertTrue(response.isNull(FieldDefs.CLOUD_PROVIDER_ENDPOINT_ID));

    if (pCloudProvider.equals(CloudProvider.AZURE)) {
      assertTrue(
          response.isNull(
              ServerlessAzureTenantEndpointView.FieldDefs.PRIVATE_LINK_SERVICE_RESOURCE_ID));
      assertTrue(
          response.isNull(ServerlessAzureTenantEndpointView.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS));
    }

    // Test max endpoints exceeded per instance

    // make second endpoint
    createDefaultEndpoint(tenantInstanceName);

    // make third endpoint that exceeds the max allowed per instance
    final JSONObject errorResponse =
        createEndpoint(HttpStatus.SC_BAD_REQUEST, _groupId, tenantInstanceName, _user);
    assertEquals(
        NDSErrorCode.TENANT_PRIVATE_ENDPOINT_MAX_PER_INSTANCE_EXCEEDED.toString(),
        errorResponse.getString(ERROR_CODE_FIELD));

    // Test unsupported cloud provider

    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject invalidCloudProviderResponse =
        createEndpoint(HttpStatus.SC_BAD_REQUEST, _groupId, tenantInstanceName, _user);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.toString(),
        invalidCloudProviderResponse.getString(ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateEndpoint_AWS() throws SvcException {
    assertUpdateEndpoint(CloudProvider.AWS);
  }

  @Test
  public void testUpdateEndpoint_AZURE() throws SvcException {
    assertUpdateEndpoint(CloudProvider.AZURE);
  }

  private void assertUpdateEndpoint(final CloudProvider pCloudProvider) throws SvcException {
    final ClusterDescription tenantInstance =
        _serverlessTenantEndpointUtil.createServerlessInstanceForBackingCloudProvider(
            pCloudProvider, _groupId);
    final String tenantInstanceName = tenantInstance.getName();

    final JSONObject tenantEndpointJson = createDefaultEndpoint(tenantInstanceName);

    final JSONObject updates = new JSONObject();
    updates.put("providerName", pCloudProvider.name());
    updates.put(FieldDefs.COMMENT, UPDATED_COMMENT);

    if (pCloudProvider.equals(CloudProvider.AWS)) {
      updates.put(
          FieldDefs.CLOUD_PROVIDER_ENDPOINT_ID,
          TenantPrivateNetworkingModelTestFactory.AWS_ENDPOINT_ID);
    } else if (pCloudProvider.equals(CloudProvider.AZURE)) {
      updates.put(
          FieldDefs.CLOUD_PROVIDER_ENDPOINT_ID,
          TenantPrivateNetworkingModelTestFactory.AZURE_ENDPOINT_ID);
      updates.put(
          ApiAtlasServerlessAzureTenantEndpointView.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS,
          TenantPrivateNetworkingModelTestFactory.AZURE_PRIVATE_ENDPOINT_IP_ADDRESS);
    }

    // Test endpoint not found
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName, new ObjectId().toString(), HttpStatus.SC_BAD_REQUEST, updates);
      assertEquals(
          NDSErrorCode.TENANT_PRIVATE_ENDPOINT_NOT_FOUND.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    // Test update invalid
    // (doesn't have Failed or Reserved status, still RESERVATION_REQUESTED)
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName,
              tenantEndpointJson.getString(FieldDefs.ID),
              HttpStatus.SC_BAD_REQUEST,
              updates);
      assertEquals(
          NDSErrorCode.TENANT_PRIVATE_ENDPOINT_PROVIDER_UPDATE_INVALID.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    // Transition to reserved status
    _ndsTenantEndpointSvc.setEndpointServiceReserved(
        tenantInstance.getGroupId(),
        tenantInstanceName,
        new ObjectId(
            tenantEndpointJson.getString(ApiAtlasServerlessTenantEndpointView.FieldDefs.ID)),
        TenantPrivateNetworkingModelTestFactory.getActiveTenantEndpointServiceForCloudProvider(
            pCloudProvider));

    updates.put(ApiAtlasServerlessTenantEndpointUpdateView.FieldDefs.COMMENT, TOO_LONG_COMMENT);

    // Test comment too long
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName,
              tenantEndpointJson.getString(FieldDefs.ID),
              HttpStatus.SC_BAD_REQUEST,
              updates);
      assertEquals(
          NDSErrorCode.INVALID_PRIVATE_NETWORK_ENDPOINT_COMMENT.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    updates.put(ApiAtlasServerlessTenantEndpointUpdateView.FieldDefs.COMMENT, UNSAFE_COMMENT);

    // Test unsafe comment
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName,
              tenantEndpointJson.getString(FieldDefs.ID),
              HttpStatus.SC_BAD_REQUEST,
              updates);
      assertEquals(
          NDSErrorCode.INVALID_PRIVATE_NETWORK_ENDPOINT_COMMENT.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    updates.put(ApiAtlasServerlessTenantEndpointUpdateView.FieldDefs.COMMENT, UPDATED_COMMENT);

    // Test success
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName,
              tenantEndpointJson.getString(FieldDefs.ID),
              HttpStatus.SC_OK,
              updates);
      assertEquals(UPDATED_COMMENT, ret.getString(FieldDefs.COMMENT));
      assertEquals(
          updates.get(FieldDefs.CLOUD_PROVIDER_ENDPOINT_ID),
          ret.getString(FieldDefs.CLOUD_PROVIDER_ENDPOINT_ID));

      if (pCloudProvider.equals(CloudProvider.AZURE)) {
        assertEquals(
            updates.get(
                ApiAtlasServerlessAzureTenantEndpointView.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS),
            ret.getString(
                ApiAtlasServerlessAzureTenantEndpointView.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS));
      }
    }

    final JSONObject tenantEndpointJson2 = createDefaultEndpoint(tenantInstanceName);

    // Transition to reserved status
    _ndsTenantEndpointSvc.setEndpointServiceReserved(
        tenantInstance.getGroupId(),
        tenantInstanceName,
        new ObjectId(
            tenantEndpointJson2.getString(ApiAtlasServerlessTenantEndpointView.FieldDefs.ID)),
        TenantPrivateNetworkingModelTestFactory.getActiveTenantEndpointServiceForCloudProvider(
            pCloudProvider));

    // Test endpoint already exists
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName,
              tenantEndpointJson2.getString(FieldDefs.ID),
              HttpStatus.SC_BAD_REQUEST,
              updates);
      assertEquals(
          NDSErrorCode.TENANT_PRIVATE_ENDPOINT_ALREADY_EXISTS.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    // Test unsupported cloud provider
    {
      final JSONObject ret =
          updateEndpoint(
              tenantInstanceName,
              tenantEndpointJson.getString(FieldDefs.ID),
              HttpStatus.SC_BAD_REQUEST,
              updates);
      assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER.toString(), ret.getString(ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testGetEndpoint_AWS() throws SvcException {
    assertGetEndpoint(CloudProvider.AWS);
  }

  @Test
  public void testGetEndpoint_AZURE() throws SvcException {
    assertGetEndpoint(CloudProvider.AZURE);
  }

  private void assertGetEndpoint(final CloudProvider pCloudProvider) throws SvcException {
    final ClusterDescription tenantInstance =
        _serverlessTenantEndpointUtil.createServerlessInstanceForBackingCloudProvider(
            pCloudProvider, _groupId);
    final String tenantInstanceName = tenantInstance.getName();

    final JSONObject tenantEndpointJson = createDefaultEndpoint(tenantInstanceName);

    // Test not found
    {
      final JSONObject ret =
          getEndpointForInstance(
              new ObjectId().toString(), tenantInstanceName, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.TENANT_PRIVATE_ENDPOINT_NOT_FOUND.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    // Test success
    {
      final JSONObject ret =
          getEndpointForInstance(
              tenantEndpointJson.getString(FieldDefs.ID), tenantInstanceName, HttpStatus.SC_OK);
      JSONAssert.assertEquals(tenantEndpointJson, ret, false);
    }

    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    // Test unsupported cloud provider
    {
      final JSONObject ret =
          getEndpointForInstance(
              tenantEndpointJson.getString(FieldDefs.ID),
              tenantInstanceName,
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER.toString(), ret.getString(ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testGetEndpoints_AWS() throws SvcException {
    assertGetEndpoints(CloudProvider.AWS);
  }

  @Test
  public void testGetEndpoints_AZURE() throws SvcException {
    assertGetEndpoints(CloudProvider.AZURE);
  }

  private void assertGetEndpoints(final CloudProvider pCloudProvider) throws SvcException {
    final ClusterDescription tenantInstance =
        _serverlessTenantEndpointUtil.createServerlessInstanceForBackingCloudProvider(
            pCloudProvider, _groupId);
    final String tenantInstanceName = tenantInstance.getName();

    // Test Empty case - success
    {
      final JSONArray ret = getEndpointsForInstance(tenantInstanceName, HttpStatus.SC_OK);
      assertEquals(0, ret.length());
    }

    final JSONObject tenantEndpointJson1 = createDefaultEndpoint(tenantInstanceName);
    final JSONObject tenantEndpointJson2 = createDefaultEndpoint(tenantInstanceName);

    // Test with endpoints - success
    {
      final JSONArray ret = getEndpointsForInstance(tenantInstanceName, HttpStatus.SC_OK);
      for (int i = 0; i < ret.length(); i++) {
        assertTrue(
            Set.of(
                    tenantEndpointJson1.getString(FieldDefs.ID),
                    tenantEndpointJson2.getString(FieldDefs.ID))
                .contains(ret.getJSONObject(i).getString(FieldDefs.ID)));
      }
    }

    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    // Test unsupported cloud provider
    {
      final JSONArray ret = getEndpointsForInstance(tenantInstanceName, HttpStatus.SC_BAD_REQUEST);
      assertEquals(0, ret.length());
    }
  }

  @Test
  public void testDeleteEndpoint_AWS() throws SvcException {
    assertDeleteEndpoint(CloudProvider.AWS);
  }

  @Test
  public void testDeleteEndpoint_AZURE() throws SvcException {
    assertDeleteEndpoint(CloudProvider.AZURE);
  }

  private void assertDeleteEndpoint(final CloudProvider pCloudProvider) throws SvcException {
    final ClusterDescription tenantInstance =
        _serverlessTenantEndpointUtil.createServerlessInstanceForBackingCloudProvider(
            pCloudProvider, _groupId);
    final String tenantInstanceName = tenantInstance.getName();

    final JSONObject tenantEndpointJson = createDefaultEndpoint(tenantInstanceName);

    // Test not found
    {
      final JSONObject ret =
          deleteEndpoint(tenantInstanceName, new ObjectId().toString(), HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.TENANT_PRIVATE_ENDPOINT_NOT_FOUND.toString(),
          ret.getString(ERROR_CODE_FIELD));
    }

    // Test success
    {
      final JSONObject ret =
          deleteEndpoint(
              tenantInstanceName, tenantEndpointJson.getString(FieldDefs.ID), HttpStatus.SC_OK);
      assertEquals(Status.DELETING.toString(), ret.getString(FieldDefs.STATUS));
    }

    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    // Test unsupported cloud provider
    {
      final JSONObject ret =
          deleteEndpoint(
              tenantInstanceName,
              tenantEndpointJson.getString(FieldDefs.ID),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER.toString(), ret.getString(ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testGetEndpointsForTenantGroupByCloudProvider_AWS() throws SvcException {
    assertGetEndpointsForTenantGroupByCloudProvider(CloudProvider.AWS);
  }

  @Test
  public void testGetEndpointsForTenantGroupByCloudProvider_AZURE() throws SvcException {
    assertGetEndpointsForTenantGroupByCloudProvider(CloudProvider.AZURE);
  }

  private void assertGetEndpointsForTenantGroupByCloudProvider(final CloudProvider pCloudProvider)
      throws SvcException {
    final ClusterDescription tenantInstance =
        _serverlessTenantEndpointUtil.createServerlessInstanceForBackingCloudProvider(
            pCloudProvider, _groupId);
    final String tenantInstanceName = tenantInstance.getName();

    final JSONArray endpointsForGroup =
        HttpUtils.getInstance()
            .get()
            .path("/nds/%s/privateEndpoint/serverless/%s", _groupId, pCloudProvider.name())
            .uiAuth(_user)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONArray.class)
            .send();
    assertNotNull(endpointsForGroup);
    assertEquals(0, endpointsForGroup.length());

    createEndpoint(HttpStatus.SC_OK, _groupId, tenantInstanceName, _user);

    // Test add an endpoint.
    final JSONArray reloadedEndpointsForGroup =
        HttpUtils.getInstance()
            .get()
            .path("/nds/%s/privateEndpoint/serverless/%s", _groupId, pCloudProvider.name())
            .uiAuth(_user)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONArray.class)
            .send();
    assertNotNull(reloadedEndpointsForGroup);
    assertEquals(1, reloadedEndpointsForGroup.length());
  }

  // request helper methods

  private JSONObject createDefaultEndpoint(final String pTenantInstanceName) {
    return createEndpoint(HttpStatus.SC_OK, _groupId, pTenantInstanceName, _user);
  }

  private JSONObject createEndpoint(
      final int pExpectedHttpStatusCode,
      final ObjectId pGroupId,
      final String pTenantInstanceName,
      final AppUser pAppUser) {
    return HttpUtils.getInstance()
        .post()
        .path(BASE_URL, pGroupId, pTenantInstanceName)
        .uiAuth(pAppUser)
        .data(new JSONObject())
        .expectedReturnStatus(pExpectedHttpStatusCode)
        .returnType(JSONObject.class)
        .send();
  }

  private JSONObject updateEndpoint(
      final String pTenantInstanceName,
      final String pEndpointId,
      final int pExpectedHttpStatusCode,
      final JSONObject pUpdates) {

    return HttpUtils.getInstance()
        .patch()
        .path(BASE_URL + "/%s", _groupId, pTenantInstanceName, pEndpointId)
        .uiAuth(_user)
        .data(pUpdates)
        .expectedReturnStatus(pExpectedHttpStatusCode)
        .returnType(JSONObject.class)
        .send();
  }

  private JSONObject getEndpointForInstance(
      final String pEndpointId,
      final String pTenantInstanceName,
      final int pExpectedHttpStatusCode) {
    return HttpUtils.getInstance()
        .get()
        .path(BASE_URL + "/%s", _groupId, pTenantInstanceName, pEndpointId)
        .uiAuth(_user)
        .expectedReturnStatus(pExpectedHttpStatusCode)
        .returnType(JSONObject.class)
        .send();
  }

  private JSONArray getEndpointsForInstance(
      final String pTenantInstanceName, final int pExpectedHttpStatusCode) {

    return HttpUtils.getInstance()
        .get()
        .path(BASE_URL, _groupId, pTenantInstanceName)
        .uiAuth(_user)
        .expectedReturnStatus(pExpectedHttpStatusCode)
        .returnType(JSONArray.class)
        .send();
  }

  private JSONObject deleteEndpoint(
      final String pTenantInstanceName,
      final String pEndpointId,
      final int pExpectedHttpStatusCode) {
    return HttpUtils.getInstance()
        .delete()
        .path(BASE_URL + "/%s", _groupId, pTenantInstanceName, pEndpointId)
        .uiAuth(_user)
        .expectedReturnStatus(pExpectedHttpStatusCode)
        .returnType(JSONObject.class)
        .send();
  }
}
