package com.xgen.svc.nds.res;

import static org.apache.http.HttpStatus.SC_ACCEPTED;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.liveimport.common.LiveImportErrorCode;
import com.xgen.module.liveimport.dao.LiveImportDao;
import com.xgen.module.liveimport.dao.LiveImportLocationDao;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.module.liveimport.model.LiveImport.State;
import com.xgen.module.liveimport.model.LiveImportLocation;
import com.xgen.module.liveimport.model.LiveImportLocation.OS;
import com.xgen.module.liveimport.model.MongomirrorPullLiveImport;
import com.xgen.module.liveimport.model.MongosyncPullLiveImport;
import com.xgen.module.liveimport.model.MongosyncStatus;
import com.xgen.module.liveimport.model.MongosyncStatus.MongosyncStage;
import com.xgen.module.liveimport.model.PullLiveImport;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.liveimport.svc.LiveImportSvc;
import com.xgen.svc.nds.liveimport.view.LiveImportJobView;
import com.xgen.svc.nds.liveimport.view.LiveImportView;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class LiveImportResourceIntTests extends JUnit5BaseResourceTest {
  @Inject LiveImportDao _liveImportDao;

  @Inject ClusterDescriptionDao _clusterDescriptionDao;

  @Inject LiveImportLocationDao _liveImportLocationDao;

  @Inject NDSGroupSvc _groupSvc;

  @Inject LiveImportSvc _liveImportSvc;

  private Organization _organization;
  private Group _group;
  private AppUser _user;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "ndsCustomer1");
    _groupSvc.ensureGroup(_group.getId());
    _user = MmsFactory.createUser(_group);
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), "cluster"));
    _liveImportLocationDao.saveReplicaSafe(
        new BasicDBObject()
            .append(
                LiveImportLocation.FieldDefs.IP_ADDRESSES, Collections.singletonList("*******/32"))
            .append(LiveImportLocation.FieldDefs.JOB_QUEUE_TAG, "LIVE_IMPORT_US_EAST_1_HELIX")
            .append(LiveImportLocation.FieldDefs.REGION, AWSRegionName.US_EAST_1.getName())
            .append(LiveImportLocation.FieldDefs.OS, OS.HELIX.getName())
            .append(LiveImportLocation.FieldDefs.ENABLED, true));
  }

  @Test
  public void testGetLiveImportsForGroup() throws Exception {
    final String endpoint = String.format("/%s/liveImport", _group.getId());

    final JSONArray response2 = doAuthedJsonArrayGet(_user, endpoint, SC_OK);
    assertEquals(0, response2.length());

    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    _liveImportDao.insertLiveImport(liveImport);

    final JSONArray response3 = doAuthedJsonArrayGet(_user, endpoint, SC_OK);
    assertEquals(1, response3.length());
  }

  @Test
  public void testGetLiveImportHistoryForGroup() throws Exception {
    final String endpoint = String.format("/%s/liveImport/history", _group.getId());

    final JSONArray response1 = doAuthedJsonArrayGet(_user, endpoint, SC_OK);
    assertEquals(0, response1.length());

    final PullLiveImport import1 =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017", State.COMPLETE);
    final PullLiveImport import2 =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017", State.EXPIRED);
    final PullLiveImport import3 =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017", State.FAILED);
    _liveImportDao.insertLiveImport(import1);
    _liveImportDao.insertLiveImport(import2);
    _liveImportDao.insertLiveImport(import3);

    final JSONArray response2 = doAuthedJsonArrayGet(_user, endpoint, SC_OK);
    assertEquals(3, response2.length());

    final String endpointWithStateParams =
        String.format(
            "/%s/liveImport/history?state=%s&state=%s",
            _group.getId(), State.COMPLETE, State.FAILED);
    final JSONArray filteredResponse = doAuthedJsonArrayGet(_user, endpointWithStateParams, SC_OK);
    assertEquals(2, filteredResponse.length());
  }

  @Test
  public void testGetLiveImport_mongomirror() throws Exception {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final String endpoint = String.format("/%s/liveImport/%s", _group.getId(), liveImport.getId());

    doAuthedJsonGet(_user, endpoint, SC_BAD_REQUEST);

    _liveImportDao.insertLiveImport(liveImport);

    final JSONObject response = doAuthedJsonGet(_user, endpoint, SC_OK);
    assertEquals(
        liveImport.getDestination().getClusterName(),
        response.getString(LiveImportView.FieldDefs.CLUSTER_NAME));
    assertEquals(
        LiveImportView.Status.RUNNING.name(), response.getString(LiveImportView.FieldDefs.STATUS));
  }

  @Test
  public void testGetLiveImport_mongosync() throws Exception {
    final PullLiveImport liveImport =
        createMongosyncImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017", State.WORKING);
    final String endpoint = String.format("/%s/liveImport/%s", _group.getId(), liveImport.getId());

    doAuthedJsonGet(_user, endpoint, SC_BAD_REQUEST);

    _liveImportDao.insertLiveImport(liveImport);

    final JSONObject response = doAuthedJsonGet(_user, endpoint, SC_OK);
    assertEquals(
        liveImport.getDestination().getClusterName(),
        response.getString(LiveImportView.FieldDefs.CLUSTER_NAME));
    assertEquals(
        LiveImportView.Status.RUNNING.name(), response.getString(LiveImportView.FieldDefs.STATUS));
  }

  @Test
  public void testCreateLiveImport() throws Exception {
    // Note we don't use doAuthedJsonPatch due to needing to mock LiveImportSvc.isSourceSharded
    final LiveImportSvc liveImportSvcSpy = spy(_liveImportSvc);
    final LiveImportResource liveImportResource = new LiveImportResource(liveImportSvcSpy);
    final AuditInfo auditInfo = spy(AuditInfo.class);

    liveImportResource.createLiveImport(
        _group, auditInfo, new LiveImportJobView("cluster", "localhost:27017", null));
    assertEquals(1, _liveImportDao.getLiveImportsForGroup(_group.getId()).size());

    final SvcException svcException =
        assertThrows(
            SvcException.class,
            () -> {
              liveImportResource.createLiveImport(
                  _group, auditInfo, new LiveImportJobView("cluster", "localhost:27017", null));
            });
    assertEquals(
        "This cluster is being used as the target for a live import", svcException.getMessage());
  }

  @Test
  public void testExtendLiveImport() throws Exception {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final String endpoint = String.format("/%s/liveImport/%s", _group.getId(), liveImport.getId());
    final JSONObject body =
        new JSONObject()
            .put("_id", liveImport.getId())
            .put("groupId", liveImport.getGroupId())
            .put("clusterName", "cluster");

    final JSONObject response1 = doAuthedJsonPatch(_user, endpoint, body, SC_BAD_REQUEST);
    assertEquals(
        LiveImportErrorCode.NO_LIVE_IMPORT_IN_PROGRESS.name(), response1.getString("errorCode"));

    final Date currentExpireDate = new Date();
    _liveImportDao.insertLiveImport(liveImport);
    _liveImportDao.setExpireDate(liveImport.getGroupId(), liveImport.getId(), currentExpireDate);

    // invalid expire date
    final Date requestedExpireDate2 =
        new Date(currentExpireDate.getTime() + Duration.ofDays(2).toMillis());
    final JSONObject body2 =
        new JSONObject()
            .put("_id", liveImport.getId())
            .put("groupId", liveImport.getGroupId())
            .put("clusterName", "cluster")
            .put("expireDate", TimeUtils.toISOString(requestedExpireDate2));

    final JSONObject response2 = doAuthedJsonPatch(_user, endpoint, body2, SC_BAD_REQUEST);
    assertEquals(LiveImportErrorCode.INVALID_EXPIRATION.name(), response2.getString("errorCode"));

    // valid expire date
    final Date requestedExpireDate3 =
        new Date(currentExpireDate.getTime() + Duration.ofDays(1).toMillis());
    final JSONObject body3 =
        new JSONObject()
            .put("_id", liveImport.getId())
            .put("groupId", liveImport.getGroupId())
            .put("clusterName", "cluster")
            .put("expireDate", TimeUtils.toISOStringMillis(requestedExpireDate3));

    doAuthedJsonPatch(_user, endpoint, body3, SC_ACCEPTED);
    final LiveImport result =
        _liveImportDao.find(liveImport.getGroupId(), liveImport.getId()).get();
    assertEquals(requestedExpireDate3, result.getExpireDate());
  }

  @Test
  public void testStopLiveImport() throws Exception {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final String endpoint = String.format("/%s/liveImport/%s", _group.getId(), liveImport.getId());

    final JSONObject response2 = doAuthedJsonDelete(_user, endpoint, SC_BAD_REQUEST);
    assertEquals(
        LiveImportErrorCode.NO_LIVE_IMPORT_IN_PROGRESS.name(), response2.getString("errorCode"));

    _liveImportDao.insertLiveImport(liveImport);

    doAuthedJsonDelete(_user, endpoint, SC_ACCEPTED);
    final LiveImport liveImport2 =
        _liveImportDao.find(liveImport.getGroupId(), liveImport.getId()).get();
    assertTrue(liveImport2.isCancelled());
  }

  @Test
  public void testCutOverLiveImport() throws Exception {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final String endpoint = String.format("/%s/liveImport/%s", _group.getId(), liveImport.getId());
    final JSONObject body =
        new JSONObject()
            .put("_id", liveImport.getId())
            .put("groupId", liveImport.getGroupId())
            .put("clusterName", "cluster")
            .put("isCutoverComplete", true);

    final JSONObject response0 = doAuthedJsonPatch(_user, endpoint, body, SC_BAD_REQUEST);
    assertEquals(
        LiveImportErrorCode.NO_LIVE_IMPORT_IN_PROGRESS.name(), response0.getString("errorCode"));

    _liveImportDao.insertLiveImport(liveImport);

    doAuthedJsonPatch(_user, endpoint, body, SC_ACCEPTED);
    final LiveImport liveImport2 =
        _liveImportDao.find(liveImport.getGroupId(), liveImport.getId()).get();
    assertTrue(liveImport2.isCutoverComplete());
  }

  @Test
  public void testAcknowledgeLiveImport() throws Exception {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final String endpoint = String.format("/%s/liveImport/%s", _group.getId(), liveImport.getId());
    final JSONObject body =
        new JSONObject()
            .put("_id", liveImport.getId())
            .put("groupId", liveImport.getGroupId())
            .put("clusterName", "cluster")
            .put("isAcknowledged", true);

    final JSONObject response0 = doAuthedJsonPatch(_user, endpoint, body, SC_BAD_REQUEST);
    assertEquals(
        LiveImportErrorCode.NO_LIVE_IMPORT_IN_PROGRESS.name(), response0.getString("errorCode"));

    _liveImportDao.insertLiveImport(liveImport);

    doAuthedJsonPatch(_user, endpoint, body, SC_ACCEPTED);
    final LiveImport liveImport2 =
        _liveImportDao.find(liveImport.getGroupId(), liveImport.getId()).get();
    assertTrue(liveImport2.isAcknowledged());
  }

  @Test
  public void testRestartLiveImportForShard() throws Exception {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final int shardIndex = 0;
    final String endpoint =
        String.format("/%s/liveImport/%s/%s", _group.getId(), liveImport.getId(), shardIndex);
    final JSONObject body = new JSONObject();

    final JSONObject response0 = doAuthedJsonPatch(_user, endpoint, body, SC_BAD_REQUEST);
    assertEquals(
        LiveImportErrorCode.NO_LIVE_IMPORT_IN_PROGRESS.name(), response0.getString("errorCode"));

    _liveImportDao.insertLiveImport(liveImport);

    doAuthedJsonPatch(_user, endpoint, body, SC_OK);
    final LiveImport liveImport2 =
        _liveImportDao.find(liveImport.getGroupId(), liveImport.getId()).get();
    assertTrue(liveImport2.getShardIndexesNeedingResync().contains(shardIndex));
  }

  @Test
  public void testNegativeShardIndex() {
    final PullLiveImport liveImport =
        createMongomirrorPullLiveImport(
            _group.getId(), "cluster", ObjectId.get(), "localhost:27017");
    final int shardIndex = -1;
    final String endpoint =
        String.format("/%s/liveImport/%s/%s", _group.getId(), liveImport.getId(), shardIndex);
    final JSONObject body = new JSONObject();
    final JSONObject response0 = doAuthedJsonPatch(_user, endpoint, body, SC_BAD_REQUEST);

    assertEquals(LiveImportErrorCode.INVALID_ARGUMENT.name(), response0.getString("errorCode"));
  }

  private MongomirrorPullLiveImport createMongomirrorPullLiveImport(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pClusterUniqueId,
      final String pHostname) {
    return createMongomirrorPullLiveImport(
        pGroupId, pClusterName, pClusterUniqueId, pHostname, State.WORKING);
  }

  private MongomirrorPullLiveImport createMongomirrorPullLiveImport(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pClusterUniqueId,
      final String pHostname,
      final LiveImport.State pState) {
    return new MongomirrorPullLiveImport.Builder()
        .groupId(pGroupId)
        .destination(new LiveImport.Destination(pClusterName, pClusterUniqueId, false))
        .src(new LiveImport.Source(pHostname, "", "", "", false, "", ""))
        .state(pState)
        .build();
  }

  private MongosyncPullLiveImport createMongosyncImport(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pClusterUniqueId,
      final String pHostname,
      final LiveImport.State pState) {
    return new MongosyncPullLiveImport.Builder()
        .groupId(pGroupId)
        .destination(new LiveImport.Destination(pClusterName, pClusterUniqueId, false))
        .src(new LiveImport.Source(pHostname, "", "", "", false, "", ""))
        .state(pState)
        .mongosyncStatuses(
            List.of(MongosyncStatus.builder().stage(MongosyncStage.INITIALIZING).build()))
        .build();
  }

  private static HttpServletRequest createTestRequest() {
    final HttpServletRequest request = spy(HttpServletRequest.class);
    when(request.getRemoteAddr()).thenReturn("host");

    return request;
  }
}
