package com.xgen.svc.nds.res;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.cps.restore._private.dao.ShadowClusterJobDao;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.shadowcluster._private.dao.ExposureDao;
import com.xgen.cloud.nds.shadowcluster._private.dao.ShadowClusterDao;
import com.xgen.cloud.nds.shadowcluster._private.dao.ShadowClusterExposureJobDao;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ConfigOverride;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure.Status;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ExposureStatusInfo;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation.PermutationType;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Treatment;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import server.src.main.com.xgen.cloud.nds.shadowcluster.view._public.view.ExposureView;

@ExtendWith(GuiceTestExtension.class)
public class NDSAdminShadowClusterResourceIntTests extends JUnit5BaseResourceTest {

  public static final ExposureStatusInfo EMPTY_STATUS = new ExposureStatusInfo(null, null, null);

  private Group group;
  private AppUser globalAtlasOperatorUser;

  @Inject private ExposureDao exposureDao;
  @Inject private ShadowClusterDao shadowClusterDao;
  @Inject private ShadowClusterJobDao shadowClusterJobDao;
  @Inject private ShadowClusterExposureJobDao shadowclusterExposureJobDao;
  @Inject private ShadowClusterSvc shadowClusterSvc;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    group = MmsFactory.createGroupWithNDSPlan();
    globalAtlasOperatorUser = MmsFactory.createGlobalAtlasOperatorUser(group);
    AppConfig.getAppSettings()
        .setProp("nds.shadowClusters.enabled", Boolean.TRUE.toString(), SettingType.MEMORY);
  }

  @Test
  public void testGetAllExposures() {
    final String url = "/admin/nds/shadowClusterExposure";

    // setup
    final ObjectId sourceCluster1Id = ObjectId.get();
    final String sourceCluster1Name = "cluster1";
    final ObjectId sourceCluster2Id = ObjectId.get();
    final String sourceCluster2Name = "cluster2";

    final ObjectId sourceGroupId = ObjectId.get();
    final String sourceGroupName = "group";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";
    final Date captureStartDate = new Date();
    final int captureDurationMinutes = 10;
    final String triggerReasonJira = "CLOUDP-1234";
    final List<Permutation> permutations =
        List.of(
            Permutation.builder()
                .id(ObjectId.get())
                .shadowClusters(new ArrayList<>())
                .type(PermutationType.BASELINE)
                .treatment(Treatment.builder().id(ObjectId.get()).build())
                .build());
    final ConfigOverride configOverride = ConfigOverride.builder().build();

    shadowClusterSvc.createExposure(
        sourceCluster1Id,
        sourceCluster1Name,
        sourceGroupId,
        sourceGroupName,
        sourceOrgId,
        sourceOrgName,
        triggerReasonJira,
        captureStartDate,
        captureDurationMinutes,
        permutations,
        configOverride);

    shadowClusterSvc.createExposure(
        sourceCluster2Id,
        sourceCluster2Name,
        sourceGroupId,
        sourceGroupName,
        sourceOrgId,
        sourceOrgName,
        triggerReasonJira,
        captureStartDate,
        captureDurationMinutes,
        permutations,
        configOverride);

    final JSONArray requestedExposureResult =
        doAuthedJsonArrayGet(globalAtlasOperatorUser, url, HttpStatus.SC_OK);
    assertEquals(2, requestedExposureResult.length());
  }

  @Test
  public void testCreateExposure() {
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "cluster1";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";
    final String triggerReasonJira = "CLOUDP-1234";
    final List<Permutation> permutations =
        List.of(
            Permutation.builder()
                .id(ObjectId.get())
                .shadowClusters(new ArrayList<>())
                .type(PermutationType.BASELINE)
                .treatment(Treatment.builder().id(ObjectId.get()).build())
                .build());
    final ConfigOverride configOverride = ConfigOverride.builder().build();

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceClusterName(sourceClusterName)
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgId(sourceOrgId)
            .sourceOrgName(sourceOrgName)
            .triggerReason(triggerReasonJira)
            .permutations(permutations)
            .configOverride(configOverride)
            .build();

    final ExposureView response =
        doAuthedJsonPost(
            globalAtlasOperatorUser,
            "/admin/nds/shadowClusterExposure",
            new ExposureView(exposure, EMPTY_STATUS),
            HttpStatus.SC_OK,
            ExposureView.class);

    assertThat(new ExposureView(exposure, EMPTY_STATUS))
        .usingRecursiveComparison()
        .ignoringFields("creationDate", "id", "permutationViews", "status", "statusReason")
        .isEqualTo(response);

    assertThat(response.getStatus()).isEqualTo(Exposure.Status.REQUESTED);
    assertThat(response.getStatusReason()).isEqualTo("Exposure requested");
    assertThat(response.getCreationDate()).isNotNull();
    assertThat(response.getId()).isNotNull();
  }

  @Test
  public void testCreateExposureWithNullDataReturns400HTTPCode() {
    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        null,
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresSourceClusterId() {
    final String sourceClusterName = "cluster1";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";
    final String triggerReasonJira = "CLOUDP-1234";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterName(sourceClusterName)
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgId(sourceOrgId)
            .sourceOrgName(sourceOrgName)
            .triggerReason(triggerReasonJira)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresSourceClusterName() {
    final ObjectId sourceClusterId = ObjectId.get();
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";
    final String triggerReasonJira = "CLOUDP-1234";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgId(sourceOrgId)
            .sourceOrgName(sourceOrgName)
            .triggerReason(triggerReasonJira)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresSourceOrgId() {
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "cluster1";
    final String sourceOrgName = "org";
    final String triggerReasonJira = "CLOUDP-1234";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceClusterName(sourceClusterName)
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgName(sourceOrgName)
            .triggerReason(triggerReasonJira)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresSourceOrgName() {
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "cluster1";
    final ObjectId sourceOrgId = ObjectId.get();
    final String triggerReasonJira = "CLOUDP-1234";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceClusterName(sourceClusterName)
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgId(sourceOrgId)
            .triggerReason(triggerReasonJira)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresSourceGroupId() {
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "cluster1";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";
    final String triggerReasonJira = "CLOUDP-1234";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceClusterName(sourceClusterName)
            .sourceGroupName(group.getName())
            .sourceOrgId(sourceOrgId)
            .sourceOrgName(sourceOrgName)
            .triggerReason(triggerReasonJira)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresSourceGroupName() {
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "cluster1";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";
    final String triggerReasonJira = "CLOUDP-1234";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceClusterName(sourceClusterName)
            .sourceGroupId(group.getId())
            .sourceOrgId(sourceOrgId)
            .sourceOrgName(sourceOrgName)
            .triggerReason(triggerReasonJira)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateExposureRequiresJira() {
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "cluster1";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "org";

    final Exposure exposure =
        Exposure.builder()
            .sourceClusterId(sourceClusterId)
            .sourceClusterName(sourceClusterName)
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgId(sourceOrgId)
            .sourceOrgName(sourceOrgName)
            .build();

    doAuthedJsonPost(
        globalAtlasOperatorUser,
        "/admin/nds/shadowClusterExposure",
        new ExposureView(exposure, EMPTY_STATUS),
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testGetByExposureId_existingExposure_returnsExposureView() {
    // Setup - create an exposure using the parameter-based create method
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "test-cluster";
    final ObjectId sourceGroupId = group.getId();
    final String sourceGroupName = group.getName();
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "test-org";
    final String triggerReasonJira = "CLOUDP-1234";
    final Date captureStartDate = new Date();
    final int captureDurationMinutes = 10;
    final List<Permutation> permutations =
        List.of(
            Permutation.builder()
                .id(ObjectId.get())
                .shadowClusters(new ArrayList<>())
                .type(PermutationType.BASELINE)
                .treatment(Treatment.builder().id(ObjectId.get()).build())
                .build());
    final ConfigOverride configOverride = ConfigOverride.builder().build();

    // Use the service method to create exposure with associated job
    final ObjectId exposureId =
        shadowClusterSvc.createExposure(
            sourceClusterId,
            sourceClusterName,
            sourceGroupId,
            sourceGroupName,
            sourceOrgId,
            sourceOrgName,
            triggerReasonJira,
            captureStartDate,
            captureDurationMinutes,
            permutations,
            configOverride);

    assertThat(exposureId).isNotNull();

    // Execute
    final String endpoint =
        String.format("/admin/nds/shadowClusterExposure/%s", exposureId.toHexString());
    final JSONObject response =
        doAuthedJsonGet(globalAtlasOperatorUser, endpoint, HttpStatus.SC_OK);

    // Verify
    assertThat(response).isNotNull();
    assertThat(response.getString("id")).isEqualTo(exposureId.toHexString());
    assertThat(response.getString("sourceClusterName")).isEqualTo(sourceClusterName);
    assertThat(response.getString("sourceClusterId")).isEqualTo(sourceClusterId.toHexString());
    assertThat(response.getString("sourceGroupId")).isEqualTo(sourceGroupId.toHexString());
    assertThat(response.getString("sourceGroupName")).isEqualTo(sourceGroupName);
    assertThat(response.getString("sourceOrgId")).isEqualTo(sourceOrgId.toHexString());
    assertThat(response.getString("sourceOrgName")).isEqualTo(sourceOrgName);
    assertThat(response.getString("triggerReasonJira")).isEqualTo(triggerReasonJira);
    assertThat(response.getString("status")).isEqualTo(Status.REQUESTED.name());
  }

  @Test
  public void testGetByExposureId_withEnvelope_returnsWrappedResponse() {
    // Setup - create an exposure using parameter-based create method
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "test-cluster-envelope";
    final ObjectId sourceOrgId = ObjectId.get();
    final String sourceOrgName = "test-org";
    final String triggerReasonJira = "CLOUDP-5678";
    final Date captureStartDate = new Date();
    final int captureDurationMinutes = 10;
    final List<Permutation> permutations =
        List.of(
            Permutation.builder()
                .id(ObjectId.get())
                .shadowClusters(new ArrayList<>())
                .type(PermutationType.BASELINE)
                .treatment(Treatment.builder().id(ObjectId.get()).build())
                .build());
    final ConfigOverride configOverride = ConfigOverride.builder().build();

    // Use the service method to create exposure with associated job
    final ObjectId exposureId =
        shadowClusterSvc.createExposure(
            sourceClusterId,
            sourceClusterName,
            group.getId(),
            group.getName(),
            sourceOrgId,
            sourceOrgName,
            triggerReasonJira,
            captureStartDate,
            captureDurationMinutes,
            permutations,
            configOverride);

    assertThat(exposureId).isNotNull();

    // Execute with envelope=true
    final String endpoint =
        String.format(
            "/admin/nds/shadowClusterExposure/%s?envelope=true", exposureId.toHexString());
    doAuthedJsonGet(globalAtlasOperatorUser, endpoint, HttpStatus.SC_OK);
  }

  @Test
  public void testDeleteByExposureId() {
    // Setup – create an exposure that we can delete
    final Date start = new Date();
    final ObjectId sourceProjectId = new ObjectId();
    final ObjectId exposureId = new ObjectId();
    final Status exposureStatus = Status.REQUESTED;
    final Date targetDeletionDate = new Date();
    final String sourceClusterName = "DummyName";
    final String exposureStatusReason = "Dummy status";
    final List<ObjectId> permutationIds = List.of(new ObjectId(), new ObjectId());
    final ShadowClusterExposureJob job =
        new ShadowClusterExposureJob(
            sourceProjectId,
            exposureId,
            permutationIds,
            exposureStatus,
            exposureStatusReason,
            targetDeletionDate,
            sourceClusterName);
    shadowclusterExposureJobDao.save(job);

    final Exposure exposureToDelete =
        Exposure.builder()
            .id(exposureId)
            .sourceClusterId(ObjectId.get())
            .sourceClusterName("cluster-to-delete")
            .sourceGroupId(group.getId())
            .sourceGroupName(group.getName())
            .sourceOrgId(ObjectId.get())
            .sourceOrgName("org")
            .triggerReason("CLOUDP-DELETE")
            .creationDate(new Date())
            .lastUpdated(new Date())
            .permutations(List.of())
            .jobIds(List.of(job.getId()))
            .build();

    exposureDao.insertMajority(exposureToDelete);

    final String endpoint =
        String.format("/admin/nds/shadowClusterExposure/%s", exposureToDelete.getId());

    // Execute – delete the exposure
    doAuthedJsonDelete(globalAtlasOperatorUser, endpoint, HttpStatus.SC_OK);

    final Optional<ShadowClusterExposureJob> jobAfterDelete =
        shadowclusterExposureJobDao.find(job.getId());
    assertThat(jobAfterDelete.isPresent()).isTrue();
    assertThat(jobAfterDelete.get().getTargetDeletionDate()).isAfterOrEqualTo(start);
  }

  @Test
  public void testGetShadowClusterById_Success() {
    // Setup - create a shadow cluster in the database
    final ObjectId shadowClusterId = ObjectId.get();
    final ObjectId exposureId = ObjectId.get();
    final ObjectId permutationId = ObjectId.get();
    final ObjectId sourceClusterId = ObjectId.get();
    final String sourceClusterName = "test-source-cluster";
    final ObjectId shadowClusterJobId = ObjectId.get();

    final ShadowCluster shadowCluster =
        ShadowCluster.builder()
            .id(shadowClusterId)
            .exposureId(exposureId)
            .permutationId(permutationId)
            .sourceGroupId(group.getId())
            .sourceClusterName(sourceClusterName)
            .sourceClusterId(sourceClusterId)
            .type(PermutationType.BASELINE)
            .jobIds(List.of(shadowClusterJobId))
            .build();

    shadowClusterDao.insertMajority(shadowCluster);

    // Insert a job with a PROVISIONED status so the resource can derive status.
    // Ensure the job's _id matches the shadow cluster's current jobId and executionOption is
    // SHADOW_CLUSTER
    final BasicDBObject jobDbObject =
        new BasicDBObject()
            .append(ShadowClusterJob.FieldDefs.EXPOSURE_ID, exposureId)
            .append(ShadowClusterJob.FieldDefs.PERMUTATION_ID, permutationId)
            .append(ShadowClusterJob.FieldDefs.JOB_STATUS, ShadowCluster.Status.PROVISIONED.name())
            .append(ShadowClusterJob.FieldDefs.JOB_STATUS_REASON, "ok")
            .append(ShadowClusterJob.FieldDefs.TARGET_DELETION_DATE, null)
            .append(ShadowClusterJob.FieldDefs.CONTAINER_ID, null)
            .append(SystemClusterJob.FieldDefs.ID, shadowClusterJobId)
            .append(
                SystemClusterJob.FieldDefs.EXECUTION_OPTION,
                SystemClusterJob.ExecutionOption.SHADOW_CLUSTER.name());

    final ShadowClusterJob job = new ShadowClusterJob(jobDbObject);
    shadowClusterJobDao.save(job);

    // Execute - call the GET endpoint
    final String url =
        "/admin/nds/shadowClusterExposure/shadowCluster/" + shadowClusterId.toHexString();
    final JSONObject response = doAuthedJsonGet(globalAtlasOperatorUser, url, HttpStatus.SC_OK);

    // Verify response
    assertThat(response.getString("id")).isEqualTo(shadowClusterId.toHexString());
    assertThat(response.getString("exposureId")).isEqualTo(exposureId.toHexString());
    assertThat(response.getString("permutationId")).isEqualTo(permutationId.toHexString());
    assertThat(response.getString("sourceGroupId")).isEqualTo(group.getId().toHexString());
    assertThat(response.getString("sourceClusterName")).isEqualTo(sourceClusterName);
    assertThat(response.getString("sourceClusterId")).isEqualTo(sourceClusterId.toHexString());
    assertThat(response.getString("type")).isEqualTo("BASELINE");
    assertThat(response.getString("status")).isEqualTo("PROVISIONED");
  }

  @Test
  public void testGetShadowClusterById_NotFound() {
    // Setup - use a non-existent shadow cluster ID
    final ObjectId nonExistentId = ObjectId.get();

    // Execute - call the GET endpoint
    final String url =
        "/admin/nds/shadowClusterExposure/shadowCluster/" + nonExistentId.toHexString();
    doAuthedJsonGet(globalAtlasOperatorUser, url, HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void testGetShadowClusterById_InvalidObjectId() {
    // Execute - call the GET endpoint with invalid ObjectId
    final String url = "/admin/nds/shadowClusterExposure/shadowCluster/invalid-object-id";
    doAuthedJsonGet(globalAtlasOperatorUser, url, HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void testGetShadowClusterById_RequiresAuthentication() {
    // Setup - create a shadow cluster in the database
    final ObjectId shadowClusterId = ObjectId.get();
    final ShadowCluster shadowCluster =
        ShadowCluster.builder()
            .id(shadowClusterId)
            .exposureId(ObjectId.get())
            .permutationId(ObjectId.get())
            .sourceGroupId(group.getId())
            .sourceClusterName("auth-test-cluster")
            .sourceClusterId(ObjectId.get())
            .type(PermutationType.BASELINE)
            .jobIds(List.of(ObjectId.get()))
            .build();

    shadowClusterDao.insertMajority(shadowCluster);

    // Execute - call the GET endpoint without authentication
    final String url =
        "/admin/nds/shadowClusterExposure/shadowCluster/" + shadowClusterId.toHexString();
    doJsonGet(url, HttpStatus.SC_UNAUTHORIZED);
  }
}
