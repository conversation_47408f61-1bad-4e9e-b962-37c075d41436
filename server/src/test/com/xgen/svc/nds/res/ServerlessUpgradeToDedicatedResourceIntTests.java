package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED;
import static org.apache.http.HttpStatus.SC_ACCEPTED;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.resourcepolicy._public.model.Policy;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.nds.resourcepolicy.util.AtlasResourcePolicyTestUtil;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessUpgradeToDedicatedStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus.ErrorClass;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus.State;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeLocation;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ServerlessUpgradeToDedicatedResourceIntTests
    extends BaseTenantUpgradeResourceTest<ServerlessUpgradeToDedicatedStatusDao> {

  @Inject private AtlasResourcePolicySvc _atlasResourcePolicySvc;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testCreateUpgrade() {
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jObject = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());

    // Overwrite the mongodb version. The UI does this already.
    // This is needed to prevent downgrading the version if the serverless is ahead of the default
    // dedicated tier
    jObject.put(FieldDefs.NAME, SERVERLESS_INSTANCE_NAME);
    jObject.put(FieldDefs.MONGODB_VERSION, NDSModelTestFactory.TEST_SERVERLESS_MONGODB_VERSION);
    jObject.put(
        FieldDefs.MONGODB_MAJOR_VERSION, NDSModelTestFactory.TEST_SERVERLESS_MONGODB_MAJOR_VERSION);

    final String endpoint =
        String.format("/nds/serverlessUpgradeToDedicated/%s", getGroup().getId());
    doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_ACCEPTED);

    Optional<ClusterDescription> clusterDescription =
        getTenantClusterDescriptionUpgradeDao()
            .findByName(getNDSGroup().getGroupId(), SERVERLESS_INSTANCE_NAME);

    assertTrue(clusterDescription.isPresent());
    assertEquals(
        clusterDescription.get().getMongoDBMajorVersion(),
        NDSModelTestFactory.TEST_SERVERLESS_MONGODB_MAJOR_VERSION);

    final List<ServerlessUpgradeToDedicatedStatus> serverlessUpgradeToDedicatedStatuses =
        getTenantUpgradeStatusDao().getTenantUpgradesForGroup(getGroup().getId());

    assertEquals(serverlessUpgradeToDedicatedStatuses.size(), 1);
    assertEquals(
        serverlessUpgradeToDedicatedStatuses.get(0).getClusterName(), SERVERLESS_INSTANCE_NAME);

    // test non-serverless instance as source
    jObject.put("name", CLUSTER_NAME);

    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.UNSUPPORTED.name(), serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testCreateUpgrade_missingMDBVersion() {
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jObject = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());

    // Remove the MongoDB versions to ensure that the endpoint works when these are missing
    jObject.remove(FieldDefs.MONGODB_VERSION);
    jObject.remove(FieldDefs.MONGODB_MAJOR_VERSION);
    jObject.put(FieldDefs.NAME, SERVERLESS_INSTANCE_NAME);

    final String endpoint =
        String.format("/nds/serverlessUpgradeToDedicated/%s", getGroup().getId());
    doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_ACCEPTED);

    Optional<ClusterDescription> clusterDescription =
        getTenantClusterDescriptionUpgradeDao()
            .findByName(getNDSGroup().getGroupId(), SERVERLESS_INSTANCE_NAME);

    assertTrue(clusterDescription.isPresent());
    assertEquals(
        NDSModelTestFactory.TEST_SERVERLESS_MONGODB_MAJOR_VERSION,
        clusterDescription.get().getMongoDBMajorVersion());

    final List<ServerlessUpgradeToDedicatedStatus> serverlessUpgradeToDedicatedStatuses =
        getTenantUpgradeStatusDao().getTenantUpgradesForGroup(getGroup().getId());

    assertEquals(serverlessUpgradeToDedicatedStatuses.size(), 1);
    assertEquals(
        serverlessUpgradeToDedicatedStatuses.get(0).getClusterName(), SERVERLESS_INSTANCE_NAME);
  }

  @Test
  public void testStartServerlessUpgrade_resourcePolicies() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, getOrganization(), FeatureFlag.ATLAS_RESOURCE_POLICIES);

    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    final JSONObject jObject =
        doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    jObject.put("groupId", getGroup().getId().toHexString());

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == cloud::Action::"cluster.createEdit",
                resource
            ) when {
                context.cluster.regions.containsAny([cloud::region::"aws:us-east-1",
                  cloud::region::"aws:us-east-2"])
            };
            """);
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), getOrganization().getId())
            .toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final String endpoint =
        String.format("/nds/serverlessUpgradeToDedicated/%s", getGroup().getId());
    final JSONObject response = doAuthedJsonPost(getAppUser(), endpoint, jObject, SC_FORBIDDEN);

    assertEquals(
        response.getString("errorCode"), ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name());
    assertEquals(
        response.getString("message"),
        ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage());
  }

  @Test
  public void testHasUnacknowledgedTenantUpgradeFailure() {
    final ClusterDescription failedUpgradeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(SERVERLESS_INSTANCE_NAME)
            .setGroupId(getGroup().getId())
            .build();
    getClusterDescriptionDao().save(failedUpgradeCluster);

    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        new ServerlessUpgradeToDedicatedStatus(
            SERVERLESS_INSTANCE_NAME,
            getGroup().getId(),
            "mtm",
            new ObjectId(),
            new ObjectId(),
            new Date(),
            AWSRegionName.US_EAST_1,
            null,
            null);
    final ServerlessUpgradeToDedicatedStatus failedUpgrade =
        new ServerlessUpgradeToDedicatedStatus(
            serverlessUpgradeToDedicatedStatus
                .toDBObject()
                .append(
                    BaseTenantUpgradeStatus.FieldDefs.STATE, BaseTenantUpgradeStatus.State.FAILED)
                .append(BaseTenantUpgradeStatus.FieldDefs.HAS_UNACKNOWLEDGED_FAILURE, true)
                .append(BaseTenantUpgradeStatus.FieldDefs.ERROR_CLASS, ErrorClass.RETRIABLE_FAILURE)
                .append(BaseTenantUpgradeStatus.FieldDefs.COMPLETE_DATE, new Date()));
    getTenantUpgradeStatusDao().save(failedUpgrade);

    final JSONObject result =
        HttpUtils.getInstance()
            .get()
            .path(
                "/nds/clusterUpgrade/%s/%s/unacknowledgedFailures",
                getGroup().getId(), SERVERLESS_INSTANCE_NAME)
            .uiAuth(getAppUser())
            .expectedReturnStatus(SC_OK)
            .returnType(JSONObject.class)
            .send();
    assertTrue(result.getBoolean("hasUnacknowledgedFailure"));
    assertEquals(
        TenantUpgradeStatus.ErrorClass.RETRIABLE_FAILURE.toString(),
        result.getString("errorClass"));
  }

  @Test
  public void testMarkTenantUpgradeFailuresAcknowledged() {
    final ClusterDescription failedUpgradeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(SERVERLESS_INSTANCE_NAME)
            .setGroupId(getGroup().getId())
            .build();
    getClusterDescriptionDao().save(failedUpgradeCluster);

    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        new ServerlessUpgradeToDedicatedStatus(
            SERVERLESS_INSTANCE_NAME,
            getGroup().getId(),
            "mtm",
            new ObjectId(),
            new ObjectId(),
            new Date(),
            AWSRegionName.US_EAST_1,
            null,
            null);
    final ServerlessUpgradeToDedicatedStatus failedUpgrade =
        new ServerlessUpgradeToDedicatedStatus(
            serverlessUpgradeToDedicatedStatus
                .toDBObject()
                .append(
                    BaseTenantUpgradeStatus.FieldDefs.STATE, BaseTenantUpgradeStatus.State.FAILED)
                .append(BaseTenantUpgradeStatus.FieldDefs.HAS_UNACKNOWLEDGED_FAILURE, true)
                .append(BaseTenantUpgradeStatus.FieldDefs.COMPLETE_DATE, new Date()));
    getTenantUpgradeStatusDao().save(failedUpgrade);

    final Date clusterCreateDate = new DateTime().minusHours(1).toDate();
    assertTrue(
        getTenantUpgradeStatusDao()
            .findUnacknowledgedFailureForCluster(
                getGroup().getId(), SERVERLESS_INSTANCE_NAME, clusterCreateDate)
            .isPresent());
    HttpUtils.getInstance()
        .patch()
        .path(
            "/nds/clusterUpgrade/%s/%s/unacknowledgedFailures",
            getGroup().getId(), SERVERLESS_INSTANCE_NAME)
        .uiAuth(getAppUser())
        .data(new JSONObject())
        .send();
    assertFalse(
        getTenantUpgradeStatusDao()
            .findUnacknowledgedFailureForCluster(
                getGroup().getId(), SERVERLESS_INSTANCE_NAME, clusterCreateDate)
            .isPresent());
  }

  @Test
  public void testHasUnacknowledgedForcedUpgrade() {
    final ClusterDescription forcedUpgradeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(SERVERLESS_INSTANCE_NAME)
            .setGroupId(getGroup().getId())
            .build();
    getClusterDescriptionDao().save(forcedUpgradeCluster);

    final ServerlessUpgradeToDedicatedStatus forcedUpgradeStatus =
        new ServerlessUpgradeToDedicatedStatus(
            new ObjectId(),
            getGroup().getId(),
            SERVERLESS_INSTANCE_NAME,
            new ObjectId(),
            "mtm",
            new ObjectId(),
            new Date(),
            new Date(),
            "username",
            0,
            State.COMPLETE,
            ErrorClass.NONE,
            false,
            new ServerlessUpgradeLocation(null, AWSRegionName.US_EAST_1, null, null),
            forcedUpgradeCluster,
            new ObjectId(),
            true,
            true,
            false,
            null);
    getTenantUpgradeStatusDao().save(forcedUpgradeStatus);

    final JSONObject result =
        HttpUtils.getInstance()
            .get()
            .path(
                "/nds/serverlessUpgradeToDedicated/%s/%s/unacknowledgedForcedUpgrades",
                getGroup().getId(), SERVERLESS_INSTANCE_NAME)
            .uiAuth(getAppUser())
            .expectedReturnStatus(SC_OK)
            .returnType(JSONObject.class)
            .send();
    assertTrue(result.getBoolean("hasUnacknowledgedForcedUpgrade"));
  }

  @Test
  public void testMarkForcedUpgradesAcknowledged() {
    final ClusterDescription forcedUpgradeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(SERVERLESS_INSTANCE_NAME)
            .setGroupId(getGroup().getId())
            .build();
    getClusterDescriptionDao().save(forcedUpgradeCluster);

    final ServerlessUpgradeToDedicatedStatus forcedUpgradeStatus =
        new ServerlessUpgradeToDedicatedStatus(
            new ObjectId(),
            getGroup().getId(),
            SERVERLESS_INSTANCE_NAME,
            new ObjectId(),
            "mtm",
            new ObjectId(),
            new Date(),
            new Date(),
            "username",
            0,
            State.COMPLETE,
            ErrorClass.NONE,
            false,
            new ServerlessUpgradeLocation(null, AWSRegionName.US_EAST_1, null, null),
            forcedUpgradeCluster,
            new ObjectId(),
            true,
            true,
            false,
            null);
    getTenantUpgradeStatusDao().save(forcedUpgradeStatus);

    final Date clusterCreateDate = new DateTime().minusHours(1).toDate();
    assertTrue(
        getTenantUpgradeStatusDao()
            .findCompletedForcedUpgradeForCluster(
                getGroup().getId(), SERVERLESS_INSTANCE_NAME, clusterCreateDate)
            .orElseThrow()
            .hasUnacknowledgedForcedUpgrade());
    HttpUtils.getInstance()
        .patch()
        .path(
            "/nds/serverlessUpgradeToDedicated/%s/%s/unacknowledgedForcedUpgrades",
            getGroup().getId(), SERVERLESS_INSTANCE_NAME)
        .uiAuth(getAppUser())
        .data(new JSONObject())
        .send();
    assertFalse(
        getTenantUpgradeStatusDao()
            .findCompletedForcedUpgradeForCluster(
                getGroup().getId(), SERVERLESS_INSTANCE_NAME, clusterCreateDate)
            .orElseThrow()
            .hasUnacknowledgedForcedUpgrade());
  }
}
