package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.admin._public.model.DataPlaneAccessRequest.FieldDefs;
import com.xgen.cloud.nds.admin._public.svc.DataPlaneAccessRequestSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSAdminDataPlaneAccessRequestResourceIntTests extends JUnit5BaseResourceTest {
  @Inject private DataPlaneAccessRequestSvc dataPlaneAccessRequestSvc;
  private Group group;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    group = MmsFactory.createGroupWithNDSPlan();
  }

  @Test
  public void testGetDataPlaneAccessRequests() {
    final AppUser globalMonitoringAdmin = MmsFactory.createMonitoringAdminUser(group);
    final AppUser globalCrashLogAnalyst = MmsFactory.createCrashLogAnalystUser(group);

    // Insert two requests for HELP-1234.
    dataPlaneAccessRequestSvc.submitLogDownloadAccessRequest(
        new ObjectId(),
        List.of("mongod"),
        List.of("random-host"),
        new Date(),
        "user",
        "HELP-1234",
        false);
    dataPlaneAccessRequestSvc.submitSSHAccessRequest(
        new ObjectId(), List.of("random-host"), new Date(), "user2", "HELP-1234", true);
    // Insert one request for SFDC 12345678
    dataPlaneAccessRequestSvc.submitSSHAccessRequest(
        new ObjectId(), List.of("random-host"), new Date(), "user2", "12345678", true);

    // Test retrieving multiple HELP tickets.
    final JSONArray helpResponse =
        doAuthedJsonArrayGet(globalMonitoringAdmin, "/admin/nds/dataPlaneAccessRequests/HELP-1234");
    assertEquals(2, helpResponse.length());
    for (int i = 0; i < helpResponse.length(); i++) {
      final JSONObject helpResult = helpResponse.getJSONObject(i);

      assertEquals("HELP-1234", helpResult.getString(FieldDefs.TICKET));
      assertEquals(1, helpResult.getJSONArray(FieldDefs.HOSTS).length());
      assertEquals("random-host", helpResult.getJSONArray(FieldDefs.HOSTS).get(0));
      if (helpResult.getString(FieldDefs.USERNAME).equals("user2")) {
        assertTrue(helpResult.isNull(FieldDefs.LOG_TYPES));
        assertTrue(helpResult.getBoolean(FieldDefs.REQUEST_VALIDATED));
      } else {
        assertEquals(1, helpResult.getJSONArray(FieldDefs.LOG_TYPES).length());
        assertEquals("mongod", helpResult.getJSONArray(FieldDefs.LOG_TYPES).get(0));
        assertFalse(helpResult.getBoolean(FieldDefs.REQUEST_VALIDATED));
      }
    }

    // Test SFDC with the other permissible user type.
    final JSONArray sfdcResponse =
        doAuthedJsonArrayGet(globalCrashLogAnalyst, "/admin/nds/dataPlaneAccessRequests/12345678");
    assertEquals(1, sfdcResponse.length());
    final JSONObject sfdcResult = sfdcResponse.getJSONObject(0);
    assertEquals("12345678", sfdcResult.getString(FieldDefs.TICKET));
    assertEquals(1, sfdcResult.getJSONArray(FieldDefs.HOSTS).length());
    assertEquals("random-host", sfdcResult.getJSONArray(FieldDefs.HOSTS).get(0));
    assertTrue(sfdcResult.isNull(FieldDefs.LOG_TYPES));
    assertTrue(sfdcResult.getBoolean(FieldDefs.REQUEST_VALIDATED));
  }

  @Test
  public void testGetDataPlaneAccessRequests_BlocksNonAdminRoles() {
    final AppUser groupOwner = MmsFactory.createGroupOwnerUser(group);
    doAuthedJsonGet(groupOwner, "/admin/nds/dataPlaneAccessRequests/12345678", 403);
  }
}
