package com.xgen.svc.nds.res;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.set;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.simulateregionoutage._private.dao.ClusterOutageSimulationDao;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation.OutageFilter;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation.OutageFilter.Type;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation.State;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ui.ClusterOutageSimulationView;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ui.ClusterOutageSimulationView.FieldDefs;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ui.OutageFilterView;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ClusterOutageSimulationResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ClusterOutageSimulationDao _clusterOutageSimulationDao;
  @Inject private FeatureFlagSvc _featureFlagSvc;

  private final String BASE_PATH = "/nds/clusters/%s/%s/outage-simulation";
  private Group _group;
  private AppUser _clusterManagerUser;
  private AppUser _readOnlyUser;
  private AppUser _noAccessUser;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("testOrg");
    _group = MmsFactory.createGroup(organization, "testGroup");

    MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));

    _clusterManagerUser =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_CLUSTER_MANAGER);
    _readOnlyUser =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_READ_ONLY);
    _noAccessUser = MmsFactory.createUser();
  }

  @Test
  public void testRequestStartSimulation() {
    final String clusterName = NDSModelTestFactory.DEFAULT_CLUSTER_NAME;
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getClusterDescription(_group.getId(), CloudProvider.AWS));
    final String path = String.format(BASE_PATH, _group.getId(), clusterName);

    final ClusterOutageSimulationView clusterOutageSimulationView =
        ClusterOutageSimulationView.builder()
            .setOutageFilters(
                List.of(
                    new OutageFilterView(
                        Type.REGION, CloudProvider.AWS, AWSRegionName.US_EAST_1.getName())))
            .build();

    // When request is made by the user without GROUP_CLUSTER_MANAGER role, response is 403
    // Forbidden.
    doAuthedJsonPost(_readOnlyUser, path, clusterOutageSimulationView, SC_FORBIDDEN);

    // When request is made by the user without any access, response is 404 not found
    doAuthedJsonPost(_noAccessUser, path, clusterOutageSimulationView, SC_NOT_FOUND);

    final Date dateBeforeRequest = new Date(System.currentTimeMillis() - 1000);
    doAuthedJsonPost(_clusterManagerUser, path, clusterOutageSimulationView, SC_OK);

    Optional<ClusterOutageSimulation> clusterOutageSimulations =
        _clusterOutageSimulationDao.findActiveByGroupIdAndClusterName(_group.getId(), clusterName);

    assertNotEquals(Optional.empty(), clusterOutageSimulations);

    ClusterOutageSimulation clusterOutageSimulation = clusterOutageSimulations.get();

    assertEquals(clusterName, clusterOutageSimulation.getClusterName());
    assertEquals(_group.getId(), clusterOutageSimulation.getGroupId());
    assertTrue(clusterOutageSimulation.getStartRequestDate().after(dateBeforeRequest));
    assertEquals(State.START_REQUESTED, clusterOutageSimulation.getState());
    assertEquals(1, clusterOutageSimulation.getOutageFilters().size());

    final OutageFilter outageFilter = clusterOutageSimulation.getOutageFilters().get(0);

    assertEquals(Type.REGION, outageFilter.getType());
    assertEquals(CloudProvider.AWS, outageFilter.getCloudProvider());
    assertEquals(AWSRegionName.US_EAST_1, outageFilter.getRegionName());
  }

  @Test
  public void testFindSimulation() {
    final ObjectId id = new ObjectId();
    final String clusterName = "testCluster";
    final String path = String.format(BASE_PATH, _group.getId(), clusterName);

    // When request is made by the user without any access, response is 404 not found.
    doAuthedGetBytes(_noAccessUser, path, SC_NOT_FOUND);

    // When request is made by the user without GROUP_CLUSTER_MANAGER role, response is 404 Not
    // Found.
    doAuthedGetBytes(_readOnlyUser, path, SC_NOT_FOUND);

    // When cluster outage simulation does not exist, response is 404 Not Found
    doAuthedGetBytes(_clusterManagerUser, path, SC_NOT_FOUND);

    // When cluster outage simulation state is not SIMULATING, response is 400 Bad Request
    final ClusterOutageSimulation simulation =
        ClusterOutageSimulation.builder()
            .setId(id)
            .setClusterName(clusterName)
            .setGroupId(_group.getId())
            .setState(State.STARTING)
            .setOutageFilters(
                List.of(new OutageFilter(Type.REGION, CloudProvider.AWS, AWSRegionName.US_EAST_1)))
            .build();
    _clusterOutageSimulationDao.insertMajority(simulation);

    // When request is made by the user without any access, response is 404 not found.
    doAuthedGetBytes(_noAccessUser, path, SC_NOT_FOUND);

    // When request is made by the user without GROUP_CLUSTER_MANAGER role, response is OK.
    doAuthedJsonGet(_readOnlyUser, path, SC_OK);

    JSONObject startingResponse = doAuthedJsonGet(_clusterManagerUser, path, SC_OK);

    assertEquals(id.toHexString(), startingResponse.getString(FieldDefs.ID));
    assertEquals(clusterName, startingResponse.getString(FieldDefs.CLUSTER_NAME));
    assertEquals(_group.getId().toHexString(), startingResponse.getString(FieldDefs.GROUP_ID));
    assertEquals(State.STARTING.name(), startingResponse.getString(FieldDefs.STATE));

    _clusterOutageSimulationDao.updateOneMajority(
        eq(ClusterOutageSimulation.FieldDefs.ID, id),
        set(ClusterOutageSimulation.FieldDefs.COMPLETION_DATE, new Date()));

    // When cluster outage simulation does not exist, response is 404 Not Found
    doAuthedGetBytes(_clusterManagerUser, path, SC_NOT_FOUND);
  }

  @Test
  public void testRequestEndSimulation() {
    final String clusterName = NDSModelTestFactory.DEFAULT_CLUSTER_NAME;
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getClusterDescription(_group.getId(), CloudProvider.AWS));
    final String path = String.format(BASE_PATH, _group.getId(), clusterName);

    // When request is made by the user without GROUP_CLUSTER_MANAGER role, response is 403
    // Forbidden.
    doAuthedJsonDelete(_readOnlyUser, path, SC_FORBIDDEN);

    // When request is made by the user without any access, response is 404
    // not found.
    doAuthedJsonDelete(_noAccessUser, path, SC_NOT_FOUND);

    // When cluster outage simulation does not exist, response is 404 not found
    doAuthedDelete(_clusterManagerUser, path, SC_NOT_FOUND);

    // When cluster outage simulation state is not SIMULATING, response is 400 Bad Request
    final ClusterOutageSimulation simulation =
        ClusterOutageSimulation.builder()
            .setClusterName(clusterName)
            .setGroupId(_group.getId())
            .setState(State.STARTING)
            .setStartRequestDate(new Date())
            .setOutageFilters(
                List.of(new OutageFilter(Type.REGION, CloudProvider.AWS, AWSRegionName.US_EAST_1)))
            .build();
    _clusterOutageSimulationDao.insertMajority(simulation);

    JSONObject invalidStateResponse = doAuthedJsonDelete(_clusterManagerUser, path, SC_BAD_REQUEST);

    assertEquals(
        NDSErrorCode.INVALID_CLUSTER_OUTAGE_SIMULATION_STATE.name(),
        invalidStateResponse.getString("errorCode"));
    assertEquals(
        NDSErrorCode.INVALID_CLUSTER_OUTAGE_SIMULATION_STATE.formatMessage(
            State.STARTING, State.SIMULATING),
        invalidStateResponse.getString("message"));

    // When cluster outage simulation state is SIMULATING, response is 200 OK and cluster outage
    // simulation fields get set correctly
    final Date dateBeforeRequest = new Date(System.currentTimeMillis() - 1000);
    _clusterOutageSimulationDao.updateSimulationState(simulation.getId(), State.SIMULATING);

    // When request is made by the user without GROUP_CLUSTER_MANAGER role, response is 403
    // Forbidden.
    doAuthedJsonDelete(_readOnlyUser, path, SC_FORBIDDEN);

    // When request is made by the user without any access, response is 404
    // not found.
    doAuthedJsonDelete(_noAccessUser, path, SC_NOT_FOUND);

    // When the request is made by the manager, it should succeed
    doAuthedJsonDelete(_clusterManagerUser, path, SC_OK);

    final Optional<ClusterOutageSimulation> clusterOutageSimulationOpt =
        _clusterOutageSimulationDao.findById(simulation.getId());

    assertTrue(clusterOutageSimulationOpt.isPresent());

    final ClusterOutageSimulation clusterOutageSimulation = clusterOutageSimulationOpt.get();

    assertTrue(clusterOutageSimulation.getEndRequestDate().isPresent());
    assertTrue(clusterOutageSimulation.getEndRequestDate().get().after(dateBeforeRequest));
    assertEquals(State.RECOVERY_REQUESTED, clusterOutageSimulation.getState());
  }
}
