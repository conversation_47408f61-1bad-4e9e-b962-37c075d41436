package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity.AuditSvcIntTestUtils;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.exmaintenance._private.dao.NDSExternalMaintenanceTaskDao;
import com.xgen.cloud.nds.exmaintenance._public.model.NDSExternalMaintenanceTask;
import com.xgen.cloud.nds.exmaintenance._public.model.NDSExternalMaintenanceTask.FieldDefs;
import com.xgen.cloud.nds.exmaintenance._public.model.NDSExternalMaintenanceTask.Phasing;
import com.xgen.cloud.nds.exmaintenance._public.model.NDSExternalMaintenanceTask.State;
import com.xgen.cloud.nds.exmaintenance._public.model.NDSExternalMaintenanceTask.Status;
import com.xgen.cloud.nds.project._public.model.ElevatedHealthMonitoringModelTestFactory;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ElevatedHealthMonitoring;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringThresholds;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringThresholds.ResourceThreshold;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc;
import com.xgen.cloud.nds.project._public.view.elevatedhealthmonitoring.MonitoringThresholdsView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSExternalMaintenanceResourceIntTests extends JUnit5BaseResourceTest {
  private static final String BASE_URL = "/admin/nds/externalMaintenance";

  @Inject NDSExternalMaintenanceTaskDao _ndsExternalMaintenanceTaskDao;
  @Inject ElevatedHealthMonitoringSvc _elevatedHealthMonitoringSvc;
  @Inject AuditSvc _auditSvc;

  private AppUser _admin;
  private AppUser _operator;
  private AppUser _viewer;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _admin = MmsFactory.createGlobalAtlasAdminUser(MmsFactory.createGroupWithStandardPlan());
    _operator =
        MmsFactory.createGlobalAtlasEngineeringOperatorUser(
            MmsFactory.createGroupWithStandardPlan());
    _viewer = MmsFactory.createGlobalAdminReadOnlyUser();
  }

  @Test
  public void testCreateTask_withAdminPermissionsShouldSucceed() {
    final String maintenanceName = "MyMaintenance";
    final JSONObject taskView = getMaintenanceTaskJSON(maintenanceName);

    doAuthedJsonPost(_admin, BASE_URL, taskView, HttpStatus.SC_ACCEPTED);
  }

  @Test
  public void testCreateTask_withEngineeringOperatorPermissionsShouldSucceed() {
    final String maintenanceName = "MyMaintenance";
    final JSONObject taskView = getMaintenanceTaskJSON(maintenanceName);

    doAuthedJsonPost(_operator, BASE_URL, taskView, HttpStatus.SC_ACCEPTED);
  }

  @Test
  public void testCreateTask_withViewerPermissionsShouldFail() {
    final String maintenanceName = "MyMaintenance";
    final JSONObject taskView = getMaintenanceTaskJSON(maintenanceName);

    doAuthedJsonPost(_viewer, BASE_URL, taskView, HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testCreateTask_withNormalizedCompletionPercentage() {
    final String maintenanceName = "MyMaintenance";
    final JSONObject taskView = getMaintenanceTaskJSON(maintenanceName);

    assertTrue(
        _ndsExternalMaintenanceTaskDao
            .find(new BasicDBObject().append("maintenanceName", maintenanceName))
            .isEmpty());

    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(NDSAudit.Type.EXTERNAL_MAINTENANCE_CREATED)
        .willBeAudited()
        .during(() -> doAuthedJsonPost(_operator, BASE_URL, taskView, HttpStatus.SC_ACCEPTED));

    assertFalse(
        _ndsExternalMaintenanceTaskDao
            .find(new BasicDBObject().append("maintenanceName", maintenanceName))
            .isEmpty());
  }

  @Test
  public void testCreateTask_withoutNormalizedCompletionPercentage() {
    final String maintenanceName = "MyMaintenance";
    final JSONObject taskView = getMaintenanceTaskJSON(maintenanceName, false);

    assertTrue(
        _ndsExternalMaintenanceTaskDao
            .find(new BasicDBObject().append("maintenanceName", maintenanceName))
            .isEmpty());

    doAuthedJsonPost(_operator, BASE_URL, taskView, HttpStatus.SC_ACCEPTED);

    assertFalse(
        _ndsExternalMaintenanceTaskDao
            .find(new BasicDBObject().append("maintenanceName", maintenanceName))
            .isEmpty());
  }

  @Test
  public void testCreateTask_withInvalidMaintenanceJavaClassNameShouldFail() {
    final String maintenanceName = "MyMaintenance";
    final JSONObject taskView =
        getMaintenanceTaskJSON(maintenanceName)
            .put("maintenanceJavaClassName", "ClassDoesNotExist");

    doAuthedJsonPost(_operator, BASE_URL, taskView, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testGetTasks() {
    final NDSExternalMaintenanceTask task1 = getMaintenanceTask("MyMaintenance1");
    final NDSExternalMaintenanceTask task2 = getMaintenanceTask("MyMaintenance2");

    final JSONArray arrayPreCreate = doAuthedJsonArrayGet(_viewer, BASE_URL, HttpStatus.SC_OK);
    assertEquals(0, arrayPreCreate.length());

    _ndsExternalMaintenanceTaskDao.createTask(task1);
    _ndsExternalMaintenanceTaskDao.createTask(task2);

    final ObjectId taskId1 = task1.getId();
    final ObjectId taskId2 = task2.getId();

    // Create monitoring thresholds with different values to distinguish them
    final MonitoringThresholds oldestThresholds =
        new MonitoringThresholds.Builder()
            .setMaxMonitoringIterations(3)
            .setMaxFleetClustersToMonitorCount(5)
            .setMaxFleetClustersToMonitorPercent(10)
            .setCPUThreshold(new MonitoringThresholds.ResourceThreshold(10, 10, 5, 15))
            .setMemoryThreshold(new MonitoringThresholds.ResourceThreshold(30, 30, 5, 35))
            .setConnectionCountThreshold(
                new MonitoringThresholds.ResourceThreshold(50, 50, null, null))
            .setIterationIntervalInMinutes(5)
            .setLookbackDurationInMinutes(5)
            .setPlanFailureThresholdWarning(1)
            .setPlanFailureThresholdCritical(2)
            .setUnhealthyClustersThresholdWarning(1)
            .setUnhealthyClustersThresholdCritical(2)
            .build();

    final MonitoringThresholds middleThresholds =
        new MonitoringThresholds.Builder()
            .setMaxMonitoringIterations(4)
            .setMaxFleetClustersToMonitorCount(10)
            .setMaxFleetClustersToMonitorPercent(20)
            .setCPUThreshold(new MonitoringThresholds.ResourceThreshold(30, 30, 5, 35))
            .setMemoryThreshold(new MonitoringThresholds.ResourceThreshold(30, 30, 5, 35))
            .setConnectionCountThreshold(
                new MonitoringThresholds.ResourceThreshold(0, 50, null, null))
            .setIterationIntervalInMinutes(5)
            .setLookbackDurationInMinutes(5)
            .setPlanFailureThresholdWarning(1)
            .setPlanFailureThresholdCritical(2)
            .setUnhealthyClustersThresholdWarning(1)
            .setUnhealthyClustersThresholdCritical(2)
            .build();

    final MonitoringThresholds newestThresholds =
        new MonitoringThresholds.Builder()
            .setMaxMonitoringIterations(6)
            .setMaxFleetClustersToMonitorCount(30)
            .setMaxFleetClustersToMonitorPercent(40)
            .setCPUThreshold(new MonitoringThresholds.ResourceThreshold(10, 10, 5, 15))
            .setMemoryThreshold(new MonitoringThresholds.ResourceThreshold(30, 30, 5, 35))
            .setConnectionCountThreshold(
                new MonitoringThresholds.ResourceThreshold(50, 50, null, null))
            .setIterationIntervalInMinutes(5)
            .setLookbackDurationInMinutes(5)
            .setPlanFailureThresholdWarning(1)
            .setPlanFailureThresholdCritical(2)
            .setUnhealthyClustersThresholdWarning(1)
            .setUnhealthyClustersThresholdCritical(2)
            .build();

    // Create multiple monitoring records with different timestamps for taskId1
    final Date oldestDate = new Date(System.currentTimeMillis() - 600000); // 10 minutes ago
    final Date middleDate = new Date(System.currentTimeMillis() - 300000); // 5 minutes ago
    final Date newestDate = new Date(); // now

    // Add oldest monitoring for task1
    final ElevatedHealthMonitoring oldestMonitoring =
        new ElevatedHealthMonitoring.Builder()
            .setActionId(taskId1)
            .setCreatedDate(oldestDate)
            .setSchedulingStatus(ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_STARTED)
            .setMonitoringThresholds(oldestThresholds)
            .build();
    _elevatedHealthMonitoringSvc.save(oldestMonitoring);

    // Add middle monitoring for task1
    final ElevatedHealthMonitoring middleMonitoring =
        new ElevatedHealthMonitoring.Builder()
            .setActionId(taskId1)
            .setCreatedDate(middleDate)
            .setSchedulingStatus(ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_STARTED)
            .setMonitoringThresholds(middleThresholds)
            .build();
    _elevatedHealthMonitoringSvc.save(middleMonitoring);

    // Add newest monitoring for task1
    final ElevatedHealthMonitoring newestMonitoring =
        new ElevatedHealthMonitoring.Builder()
            .setActionId(taskId1)
            .setCreatedDate(newestDate)
            .setSchedulingStatus(ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_STARTED)
            .setMonitoringThresholds(newestThresholds)
            .build();
    _elevatedHealthMonitoringSvc.save(newestMonitoring);

    // Add only one monitoring for task2
    final ElevatedHealthMonitoring monitoring2 =
        new ElevatedHealthMonitoring.Builder()
            .setActionId(taskId2)
            .setCreatedDate(middleDate)
            .setSchedulingStatus(ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_HALTED)
            .setMonitoringThresholds(middleThresholds)
            .build();
    _elevatedHealthMonitoringSvc.save(monitoring2);

    final JSONArray arrayPostCreate = doAuthedJsonArrayGet(_viewer, BASE_URL, HttpStatus.SC_OK);
    assertEquals(2, arrayPostCreate.length());

    // Verify task status and monitoring fields
    boolean foundTask1 = false;
    boolean foundTask2 = false;

    for (int i = 0; i < arrayPostCreate.length(); i++) {
      final JSONObject task = arrayPostCreate.getJSONObject(i);

      // Common verification for all tasks
      assertEquals(
          JSONObject.NULL, task.getJSONObject("status").get("normalizedCompletionPercentage"));

      // Identify tasks and verify monitoring fields
      String maintenanceName = task.getString("maintenanceName");

      if (maintenanceName.equals("MyMaintenance1")) {
        foundTask1 = true;
        assertTrue(task.getBoolean("monitoringEnabled"), "Task1 monitoring should be enabled");

        // Should have the newest thresholds (MaxMonitoringIterations = 6)
        JSONObject thresholds = task.getJSONObject("monitoringThresholds");
        assertEquals(
            6,
            thresholds.getInt("maxMonitoringIterations"),
            "Task1 should have the newest monitoring thresholds");
        assertEquals(
            30,
            thresholds.getInt("maxFleetClustersToMonitorCount"),
            "Task1 should have the newest maxFleetClustersToMonitorCount");
        assertEquals(
            40,
            thresholds.getInt("maxFleetClustersToMonitorPercent"),
            "Task1 should have the newest maxFleetClustersToMonitorPercent");

      } else if (maintenanceName.equals("MyMaintenance2")) {
        foundTask2 = true;
        assertFalse(task.getBoolean("monitoringEnabled"), "Task2 monitoring should be disabled");

        // Should have the middle thresholds (MaxMonitoringIterations = 4)
        JSONObject thresholds = task.getJSONObject("monitoringThresholds");
        assertEquals(
            4,
            thresholds.getInt("maxMonitoringIterations"),
            "Task2 should have the only monitoring thresholds");
        assertEquals(
            10,
            thresholds.getInt("maxFleetClustersToMonitorCount"),
            "Task2 should have the correct maxFleetClustersToMonitorCount");
        assertEquals(
            20,
            thresholds.getInt("maxFleetClustersToMonitorPercent"),
            "Task2 should have the correct maxFleetClustersToMonitorPercent");
      }
    }

    assertTrue(foundTask1, "Response should contain task1");
    assertTrue(foundTask2, "Response should contain task2");
  }

  @Test
  public void testGetMaintenanceMetadata() {
    // test maintenance that restarts only mongot
    final JSONObject response1 =
        doAuthedJsonGet(
            _operator,
            String.format("%s/metadata/%s", BASE_URL, "RotateInstanceChefConfigMaintenance"));
    assertFalse(response1.getBoolean("isNeverRestartingMongot"));
    assertTrue(response1.getBoolean("isNeverRestartingMongod"));

    // test maintenance that restarts both mongod and mongot
    final JSONObject response2 =
        doAuthedJsonGet(
            _operator,
            String.format("%s/metadata/%s", BASE_URL, "UpdateServerlessMTMCapacityMaintenance"));
    assertFalse(response2.getBoolean("isNeverRestartingMongot"));
    assertFalse(response2.getBoolean("isNeverRestartingMongod"));

    // test invalid maintenance name
    try {
      doAuthedJsonGet(_operator, String.format("%s/metadata/%s", BASE_URL, "test"));
      fail();
    } catch (Exception e) {
      assertTrue(e.getMessage().contains("Expected '200' but got '400'"));
    }

    // viewer should not have permissions
    try {
      doAuthedJsonGet(
          _viewer,
          String.format("%s/metadata/%s", BASE_URL, "UpdateServerlessMTMCapacityMaintenance"));
      fail();
    } catch (Exception e) {
      assertTrue(e.getMessage().contains("Expected '200' but got '403'"));
    }
  }

  @Test
  public void testUpdateTask_withViewerPermissionsShouldFail() {
    final String maintenanceName = "MyMaintenance";
    final var jsonBlob = getMaintenanceTaskJSON(maintenanceName);

    doAuthedJsonPost(_operator, BASE_URL, jsonBlob, HttpStatus.SC_ACCEPTED);
    final var taskId =
        _ndsExternalMaintenanceTaskDao
            .findOne(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .orElseThrow()
            .getId();

    final var newRolloutPercentage = 50.0f;
    jsonBlob.put(FieldDefs.ID, taskId);
    jsonBlob.put(FieldDefs.ROLLOUT_PERCENTAGE, newRolloutPercentage);

    doAuthedJsonPatch(_viewer, BASE_URL, jsonBlob, HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testUpdateTask() {
    final String maintenanceName = "MyMaintenance";
    final var jsonBlob = getMaintenanceTaskJSON(maintenanceName);

    doAuthedJsonPost(_operator, BASE_URL, jsonBlob, HttpStatus.SC_ACCEPTED);
    final var taskId =
        _ndsExternalMaintenanceTaskDao
            .findOne(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .orElseThrow()
            .getId();

    final var newRolloutPercentage = 50.0f;
    jsonBlob.put(FieldDefs.ID, taskId);
    jsonBlob.put(FieldDefs.ROLLOUT_PERCENTAGE, newRolloutPercentage);

    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(NDSAudit.Type.EXTERNAL_MAINTENANCE_UPDATED)
        .willBeAudited()
        .during(() -> doAuthedJsonPatch(_operator, BASE_URL, jsonBlob, HttpStatus.SC_ACCEPTED));

    final NDSExternalMaintenanceTask taskAfterUpdate =
        _ndsExternalMaintenanceTaskDao
            .find(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .get(0);
    assertEquals(State.QUEUED, taskAfterUpdate.getStatus().getState());
    assertEquals(newRolloutPercentage, taskAfterUpdate.getRolloutPercentage(), 0);
  }

  @Test
  public void testUpdateElevatedHealthMonitoring() throws JsonProcessingException {
    final String maintenanceName = "MyMaintenance";
    final MonitoringThresholds monitoringThresholds =
        ElevatedHealthMonitoringModelTestFactory.getMonitoringThresholdsBuilder()
            .setMaxMonitoringIterations(4)
            .setCPUThreshold(
                new ResourceThreshold(
                    10, // averageUsagePercentDecrease
                    10, // averageUsagePercentIncrease
                    5, // averageUsagePercentLowerLimit
                    15 // averageUsagePercentUpperLimit
                    ))
            .setIterationIntervalInMinutes(5)
            .setLookbackDurationInMinutes(5)
            .build();
    final var jsonBlob = getMaintenanceTaskWithEHMJSON(monitoringThresholds);

    doAuthedJsonPost(_operator, BASE_URL, jsonBlob, HttpStatus.SC_ACCEPTED);
    final var taskId =
        _ndsExternalMaintenanceTaskDao
            .findOne(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .orElseThrow()
            .getId();
    final var ehm = _elevatedHealthMonitoringSvc.findMostRecentByActionId(taskId).orElseThrow();
    assertEquals(
        ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_STARTED, ehm.getSchedulingStatus());
    // Verify initial EHM settings match what we configured
    assertEquals(4, ehm.getMonitoringThresholds().maxMonitoringIterations());
    assertEquals(10, ehm.getMonitoringThresholds().cpuThreshold().averageUsagePercentDecrease());
    assertEquals(10, ehm.getMonitoringThresholds().cpuThreshold().averageUsagePercentIncrease());
    assertEquals(
        (Integer) 5, ehm.getMonitoringThresholds().cpuThreshold().averageUsagePercentLowerLimit());
    assertEquals(
        (Integer) 15, ehm.getMonitoringThresholds().cpuThreshold().averageUsagePercentUpperLimit());
    assertEquals(5, ehm.getMonitoringThresholds().iterationIntervalInMinutes());
    assertEquals(5, ehm.getMonitoringThresholds().lookbackDurationInMinutes());

    // Update monitoring thresholds
    final JSONObject existingThresholds =
        jsonBlob.has("monitoringThresholds")
            ? jsonBlob.getJSONObject("monitoringThresholds")
            : new JSONObject();

    // Create updated thresholds with specific changes
    final MonitoringThresholds updatedMonitoringThresholds =
        monitoringThresholds
            .copy()
            .setMaxMonitoringIterations(5)
            .setCPUThreshold(
                new ResourceThreshold(
                    15, // averageUsagePercentDecrease
                    15, // averageUsagePercentIncrease
                    10, // averageUsagePercentLowerLimit
                    20 // averageUsagePercentUpperLimit
                    ))
            .setIterationIntervalInMinutes(10)
            .setLookbackDurationInMinutes(10)
            .build();
    final JSONObject updatedThresholds =
        viewToJsonObject(new MonitoringThresholdsView(updatedMonitoringThresholds));

    jsonBlob.put("_id", taskId);
    jsonBlob.put("monitoringEnabled", true);
    jsonBlob.put("monitoringThresholds", updatedThresholds);

    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(NDSAudit.Type.EXTERNAL_MAINTENANCE_UPDATED)
        .willBeAudited()
        .during(() -> doAuthedJsonPatch(_operator, BASE_URL, jsonBlob, HttpStatus.SC_ACCEPTED));

    _ndsExternalMaintenanceTaskDao
        .findOne(new BasicDBObject().append(FieldDefs.ID, taskId))
        .orElseThrow();
    // Get the updated EHM settings
    final var updatedEhm =
        _elevatedHealthMonitoringSvc.findMostRecentByActionId(taskId).orElseThrow();
    assertEquals(
        ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_STARTED,
        updatedEhm.getSchedulingStatus());
    assertEquals(5, updatedEhm.getMonitoringThresholds().maxMonitoringIterations());
    assertEquals(
        15, updatedEhm.getMonitoringThresholds().cpuThreshold().averageUsagePercentDecrease());
    assertEquals(
        15, updatedEhm.getMonitoringThresholds().cpuThreshold().averageUsagePercentIncrease());
    assertEquals(
        (Integer) 10,
        updatedEhm.getMonitoringThresholds().cpuThreshold().averageUsagePercentLowerLimit());
    assertEquals(
        (Integer) 20,
        updatedEhm.getMonitoringThresholds().cpuThreshold().averageUsagePercentUpperLimit());
    assertEquals(10, updatedEhm.getMonitoringThresholds().iterationIntervalInMinutes());
    assertEquals(10, updatedEhm.getMonitoringThresholds().lookbackDurationInMinutes());

    jsonBlob.put("_id", taskId);
    jsonBlob.put("monitoringEnabled", false);

    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(NDSAudit.Type.EXTERNAL_MAINTENANCE_UPDATED)
        .willBeAudited()
        .during(() -> doAuthedJsonPatch(_operator, BASE_URL, jsonBlob, HttpStatus.SC_ACCEPTED));
    final var pausedEHM =
        _elevatedHealthMonitoringSvc.findMostRecentByActionId(taskId).orElseThrow();
    assertEquals(
        ElevatedHealthMonitoring.SchedulingStatus.SCHEDULING_HALTED,
        pausedEHM.getSchedulingStatus());
  }

  @Test
  public void testCancelTask_withViewerPermissionsShouldFail() {
    final String maintenanceName = "MyMaintenance";

    doAuthedJsonPost(
        _operator, BASE_URL, getMaintenanceTaskJSON(maintenanceName), HttpStatus.SC_ACCEPTED);
    final var taskId =
        _ndsExternalMaintenanceTaskDao
            .findOne(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .orElseThrow()
            .getId();

    doAuthedJsonPost(
        _viewer,
        String.format("%s/cancel/%s", BASE_URL, taskId),
        new JSONObject(),
        HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testCancelTask() {
    final String maintenanceName = "MyMaintenance";

    doAuthedJsonPost(
        _operator, BASE_URL, getMaintenanceTaskJSON(maintenanceName), HttpStatus.SC_ACCEPTED);
    final var taskId =
        _ndsExternalMaintenanceTaskDao
            .findOne(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .orElseThrow()
            .getId();

    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(Type.EXTERNAL_MAINTENANCE_CANCELED)
        .willBeAudited()
        .during(
            () ->
                doAuthedJsonPost(
                    _operator,
                    String.format("%s/cancel/%s", BASE_URL, taskId),
                    new JSONObject(),
                    HttpStatus.SC_ACCEPTED));

    final NDSExternalMaintenanceTask taskAfterCancel =
        _ndsExternalMaintenanceTaskDao
            .find(new BasicDBObject().append(FieldDefs.MAINTENANCE_NAME, maintenanceName))
            .get(0);
    assertEquals(State.CANCEL, taskAfterCancel.getStatus().getState());
  }

  private NDSExternalMaintenanceTask getMaintenanceTask(final String pMaintenanceName) {
    return new NDSExternalMaintenanceTask.Builder()
        .setId(ObjectId.get())
        .setMaintenanceName(pMaintenanceName)
        .setMaintenanceJavaClassName(pMaintenanceName)
        .setMaintenanceArgs(List.of("--arg1", "value1", "--arg2", "value2"))
        .setRolloutPercentage(10.0f)
        .setBypassMaintenanceWindow(true)
        .setNumWorkers(1)
        .setStatus(
            new Status.Builder()
                .setState(State.IN_PROGRESS)
                .setNumItems(1)
                .setNormalizedCompletionPercentage(0.0d)
                .build())
        .setPhasing(
            new Phasing.Builder()
                .setStartingPercentage(5.0f)
                .setMaxRounds(1000)
                .setSleepSeconds(15)
                .build())
        .build();
  }

  private JSONObject getMaintenanceTaskJSON(final String pMaintenanceName) {
    return getMaintenanceTaskJSON(pMaintenanceName, true);
  }

  private JSONObject getMaintenanceTaskJSON(
      final String pMaintenanceName, final boolean pIncludeNormalizedCompletionPercentage) {
    JSONObject status = new JSONObject().put("state", "IN_PROGRESS").put("numItems", 1);
    if (pIncludeNormalizedCompletionPercentage) {
      status = status.put("normalizedCompletionPercentage", 0.0d);
    }
    return new JSONObject()
        .put("maintenanceName", pMaintenanceName)
        .put("maintenanceJavaClassName", "MongoDBMajorVersionUpgradeMaintenance")
        .put(
            "maintenanceArgs",
            new JSONArray().put("--currentVersion").put("5.0").put("--desiredVersion").put("6.0"))
        .put("rolloutPercentage", 10.0f)
        .put("bypassMaintenanceWindow", true)
        .put("numWorkers", 1)
        .put("status", status)
        .put(
            "phasing",
            new JSONObject()
                .put("startingPercentage", 5.0f)
                .put("maxRounds", 1000)
                .put("sleepSeconds", 15));
  }

  private JSONObject getMaintenanceTaskWithEHMJSON(final MonitoringThresholds pThresholds)
      throws JsonProcessingException {
    final var task = getMaintenanceTaskJSON("MyMaintenance", true);

    // Convert model to view
    final MonitoringThresholdsView thresholdsView = new MonitoringThresholdsView(pThresholds);
    final JSONObject thresholdsViewJSON = viewToJsonObject(thresholdsView);

    // Add monitoring thresholds to task
    task.put("monitoringThresholds", thresholdsViewJSON);
    return task;
  }

  // Convert view to JSONObject using ObjectMapper -> throws Exception if conversion fails, which
  // would be unexpected behavior, and thus should cause test failure
  private JSONObject viewToJsonObject(final MonitoringThresholdsView pView) {
    final ObjectMapper mapper = new ObjectMapper();
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

    return new JSONObject(mapper.convertValue(pView, new TypeReference<Map<String, Object>>() {}));
  }
}
