package com.xgen.svc.nds.res;

import static com.xgen.svc.nds.model.ui.CloudChefPackagePolicyView.FieldDefs.MAINTENANCE_WINDOW;
import static com.xgen.svc.nds.model.ui.CloudChefPackagePolicyView.FieldDefs.PACKAGE_POLICY_ETAG;
import static com.xgen.svc.nds.model.ui.CloudChefPackagePolicyView.FieldDefs.PACKAGE_POLICY_VERSION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBObject;
import com.mongodb.ReadPreference;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.agent._public.svc.AgentApiKeySvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.RebootRequestedBy;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf.Builder;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.view.ChefDataView;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.AWSEnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.vmimage._public.model.AtlasRemoteImagesConfig;
import com.xgen.cloud.nds.vmimage._public.util.AtlasRemoteImagesConfigFactory;
import com.xgen.cloud.search.decoupled.cloudprovider._private.dao.PartitionGroupDao;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchPhysicalModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.aws.AWSSearchInstanceSize;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.svc.CloudChefConfSvc;
import com.xgen.svc.nds.svc.NDSInstanceAgentApiKeysSvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.net.InetAddress;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class CloudChefConfResourceIntTests extends JUnit5BaseResourceTest {
  private static final ObjectId INSTANCE_ID = new ObjectId();
  private static final String FQDN_VAL = "localhost";

  private static final String HOST_IP = "127.0.0.1";
  private static final String CLUSTER_NAME_VAL = "clusterName";
  private static final String DATA_DEVICE_VAL = "dataDevice";
  private static final String CHEF_CALLBACK_URL_VAL = "chefcallbackurl";
  private static final String NODE_NAME_VAL = "nodeName";
  private static final String LOG_RETENTION_VAL = "logRetention";
  private static final String BACKUP_DEVICE_VAL = "backupDevice";

  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private NDSGroupDao _groupDao;

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  @Inject private ReplicaSetHardwareDao _hardwareDao;

  @Inject private CloudChefConfSvc _cloudChefConfSvc;

  @Inject private AgentApiKeySvc _agentApiKeySvc;

  @Inject private ReplicaSetHardwareSvc _replicaSetHardwareSvc;

  @Inject private ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;

  @Inject private NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;

  @Inject private NDSInstanceAgentApiKeysSvc _ndsInstanceAgentApiKeysSvc;

  @Inject private PartitionGroupDao _partitionGroupDao;

  @Inject private AuditSvc _auditSvc;

  private Group _mmsGroup;
  private AgentApiKey _agentApiKey;

  private CloudChefConf _cloudChefConf;

  private ClusterDescription _clusterDescription;

  private String _cloudChefConfId;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    setupClusterAndGroup();

    final Builder<?> _cloudChefConfBuilder = CloudChefConf.builder();
    _cloudChefConfBuilder.fqdn(FQDN_VAL);
    _cloudChefConfBuilder.ip(HOST_IP);
    _cloudChefConfBuilder.groupId(_mmsGroup.getId().toString());
    _cloudChefConfBuilder.clusterName(_clusterDescription.getName());
    _cloudChefConfBuilder.dataDevice(DATA_DEVICE_VAL);
    _cloudChefConfBuilder.validDatetime(CloudChefConf.generateValidDatetime());
    _cloudChefConfBuilder.nodeName(NODE_NAME_VAL);
    _cloudChefConfBuilder.region(AWSRegionName.US_EAST_1.getValue());
    _cloudChefConfBuilder.cloudProvider(CloudProvider.AWS.getChefProvider());
    _cloudChefConfBuilder.hostnameSubdomainLevel(InstanceHostname.SubdomainLevel.MONGODB.name());
    _cloudChefConfBuilder.chefCallbackUrl(CHEF_CALLBACK_URL_VAL);
    _cloudChefConfBuilder.createdAt(new Date());
    _cloudChefConfBuilder.instanceId(INSTANCE_ID);
    _cloudChefConfBuilder.logRetention(LOG_RETENTION_VAL);
    _cloudChefConfBuilder.backupDevice(BACKUP_DEVICE_VAL);
    _cloudChefConfBuilder.partitions(Collections.emptyList());
    _cloudChefConfBuilder.pushBasedLogExportConfig(
        CloudChefConfSvc.getDefaultDisabledLogExportConfig());
    _cloudChefConf = _cloudChefConfBuilder.build();
    _cloudChefConfId = _cloudChefConfSvc.saveCloudChefConf(_cloudChefConf);
    _agentApiKey =
        _ndsInstanceAgentApiKeysSvc.generateNewApiKey(
            INSTANCE_ID, _mmsGroup.getId(), HOST_IP, _clusterDescription.getName(), false);
  }

  private void setupClusterAndGroup() throws Exception {

    _mmsGroup = MmsFactory.createGroupWithNDSPlan();

    _ndsGroupSvc.ensureGroup(_mmsGroup.getId());

    _groupDao.addCloudContainer(
        _mmsGroup.getId(), new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    _clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_mmsGroup.getId(), CLUSTER_NAME_VAL));

    _clusterDescriptionDao.save(_clusterDescription);
  }

  @Test
  public void testGet() throws Exception {
    final String endpoint = String.format("/nds/cloudchefconf/%s", _cloudChefConfId);
    final JSONObject result = doJsonGet(endpoint);

    assertEquals(result.getJSONObject("xgen").getString("group_id"), _mmsGroup.getId().toString());

    final String key = result.getJSONObject("xgen").getString("api_key");

    assertNotNull(
        _agentApiKeySvc.authenticate(
            _mmsGroup.getId(),
            key,
            InetAddress.getLoopbackAddress().getHostAddress(),
            ReadPreference.secondaryPreferred()));

    // IP address is bound to loopback, so auth should fail from other addresses
    assertNull(
        _agentApiKeySvc.authenticate(
            _mmsGroup.getId(), key, "*******", ReadPreference.secondaryPreferred()));
  }

  @Disabled
  @Test
  public void testInvalidGet() {
    final String nonExistedId = "knowhere";
    final String endpoint = String.format("/nds/cloudchefconf/%s", nonExistedId);

    try {
      doJsonGet(endpoint);
      fail("Expected a non-existing Cloud Chef Conf");
    } catch (Exception e) {
    }
  }

  @Test
  public void testRequestServerReboot() throws Exception {
    final NDSGroup ndsGroup = _groupDao.find(_mmsGroup.getId()).get();

    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, ndsGroup.getCloudProviderContainers().get(0).getId(), _clusterDescription);
    ((BasicDBObject) hardwareDoc.get("_id")).put("groupId", _mmsGroup.getId());
    final ReplicaSetHardware hardware = new ReplicaSetHardware(hardwareDoc);
    _hardwareDao.saveReplicaSafe(hardwareDoc);

    final String hostname = hardware.getHardware().get(0).getHostnameForAgents().get();
    assertFalse(
        _replicaSetHardwareSvc
            .getInstanceHardwareByHostname(hostname, _mmsGroup.getId())
            .get()
            .getRebootRequestedDate()
            .isPresent());

    // Non-critical reboot
    final String endpoint =
        String.format("/nds/cloudchefconf/%s/%s/reboot", _mmsGroup.getId(), hostname);
    doAgentApiCallPost(
        _mmsGroup,
        _agentApiKey,
        endpoint,
        String.format("{\"%s\":\"hash1\"}", ChefDataView.CHEF_PAYLOAD_KEY).getBytes());

    final InstanceHardware updatedHardware =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();

    assertTrue(updatedHardware.getRebootRequestedDate().isPresent());
    assertFalse(updatedHardware.needsCriticalReboot());
    assertEquals("hash1", updatedHardware.getRebootRequestedChefCommitHash().get());
    assertEquals(RebootRequestedBy.CHEF, updatedHardware.getRebootRequestedBy().get());

    // Critical reboot
    final String criticalEndpoint =
        String.format(
            "/nds/cloudchefconf/%s/%s/reboot?isCritical=true", _mmsGroup.getId(), hostname);
    doAgentApiCallPost(_mmsGroup, _agentApiKey, criticalEndpoint, "".getBytes());

    InstanceHardware criticalUpdatedHardware =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();

    assertTrue(criticalUpdatedHardware.getRebootRequestedDate().isPresent());
    assertTrue(criticalUpdatedHardware.needsCriticalReboot());

    // In above requests we were sending ContentType.DEFAULT_BINARY as content type. When passing
    // JSONObject, we make application/json request.
    doAgentApiCallPost(
        _mmsGroup,
        _agentApiKey,
        criticalEndpoint,
        new JSONObject().put(ChefDataView.CHEF_PAYLOAD_KEY, "hash2"));
    criticalUpdatedHardware =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();

    assertTrue(criticalUpdatedHardware.getRebootRequestedDate().isPresent());
    assertTrue(criticalUpdatedHardware.needsCriticalReboot());
    assertEquals("hash2", criticalUpdatedHardware.getRebootRequestedChefCommitHash().get());

    // Test for sending incorrect json returns a 200 as well
    doAgentApiCallPost(_mmsGroup, _agentApiKey, criticalEndpoint, new String("not-a-json-object"));
    criticalUpdatedHardware =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();

    assertTrue(criticalUpdatedHardware.getRebootRequestedDate().isPresent());
    assertTrue(criticalUpdatedHardware.needsCriticalReboot());
    assertEquals("hash2", criticalUpdatedHardware.getRebootRequestedChefCommitHash().get());
  }

  @Test
  public void testRequestServerRestartAndRebootEnvoyInstance() throws Exception {
    final String envoyHostname0 = "instance-00.sniproxy.hello.mmscloudtest.com";
    final Hostnames hostnames0 = new Hostnames(envoyHostname0, "hostname");

    final AWSEnvoyInstance instance0 =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(
            new ObjectId(), 0, false, null, hostnames0);

    final AWSEnvoyInstance instance1 =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 1);

    final AWSEnvoyInstance instance2 =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 2);

    final ObjectId containerId = new ObjectId();
    final ServerlessLoadBalancingDeployment deployment =
        ServerlessDeploymentModelTestFactory.getAWSServerlessLoadBalancingDeployment(
                AWSRegionName.US_EAST_1)
            .copy()
            .setGroupId(_mmsGroup.getId())
            .setContainerId(containerId)
            .setEnvoyInstances(List.of(instance0, instance1, instance2))
            .build();

    _serverlessLoadBalancingDeploymentDao.save(deployment);

    final ServerlessLoadBalancingDeployment originalDeployment =
        _serverlessLoadBalancingDeploymentDao.find(deployment.getId()).get();
    originalDeployment
        .getEnvoyInstances()
        .forEach(envoyInstance -> assertTrue(envoyInstance.getRebootRequestedDate().isEmpty()));

    final String rebootEndpoint =
        String.format("/nds/cloudchefconf/%s/%s/%s", _mmsGroup.getId(), envoyHostname0, "reboot");

    doAgentApiCallPost(_mmsGroup, _agentApiKey, rebootEndpoint, new JSONObject());

    final ServerlessLoadBalancingDeployment updatedDeploymentAfterReboot =
        _serverlessLoadBalancingDeploymentDao.find(deployment.getId()).get();

    assertTrue(
        updatedDeploymentAfterReboot
            .getEnvoyInstanceById(instance0.getId())
            .get()
            .getRebootRequestedDate()
            .isPresent());
    assertTrue(
        updatedDeploymentAfterReboot
            .getEnvoyInstanceById(instance1.getId())
            .get()
            .getRebootRequestedDate()
            .isEmpty());
    assertTrue(
        updatedDeploymentAfterReboot
            .getEnvoyInstanceById(instance2.getId())
            .get()
            .getRebootRequestedDate()
            .isEmpty());
  }

  @Test
  public void testRequestServerRestartAndRebootSearchInstance() throws Exception {
    final String hostname = "search-abc123-partition-00-00.mmscloudtest.com";
    final Hostnames hostnames =
        new Hostnames(new InstanceHostname(InstanceHostname.HostnameScheme.INTERNAL, hostname));

    final SearchInstance instance0 =
        SearchPhysicalModelTestFactory.getProvisionedSearchInstance(
            new ObjectId(),
            0,
            new Date(),
            AWSSearchInstanceSize.S20_HIGHCPU_NVME,
            Optional.of(hostnames),
            Optional.empty());
    final SearchInstance instance1 = SearchPhysicalModelTestFactory.getProvisionedSearchInstance(1);

    final ObjectId partitionGroupId = new ObjectId();
    _partitionGroupDao.create(
        SearchPhysicalModelTestFactory.getPartitionGroup(
            partitionGroupId,
            _mmsGroup.getId(),
            new ObjectId(),
            new ObjectId(),
            List.of(instance0, instance1)));

    final PartitionGroup originalPartitionGroup =
        _partitionGroupDao.findByPartitionGroupId(partitionGroupId).orElseThrow();
    originalPartitionGroup
        .getInstances()
        .forEach(si -> assertTrue(si.getInstanceHardware().getRebootRequestedDate().isEmpty()));

    final String rebootEndpoint =
        String.format("/nds/cloudchefconf/%s/%s/%s", _mmsGroup.getId(), hostname, "reboot");

    doAgentApiCallPost(_mmsGroup, _agentApiKey, rebootEndpoint, new JSONObject());

    final PartitionGroup rebootedPartitionGroup =
        _partitionGroupDao.findByPartitionGroupId(partitionGroupId).orElseThrow();
    assertEquals(
        rebootedPartitionGroup.getInstances().stream()
            .map(si -> si.getInstanceHardware().getRebootRequestedDate().isPresent())
            .collect(Collectors.toList()),
        List.of(true, false));
  }

  @Test
  public void testGetPackagePolicy() throws Exception {
    final AtlasRemoteImagesConfig imagesConfig =
        AtlasRemoteImagesConfigFactory.getDefaultRemoteImageConfig();
    final String policyVersion = imagesConfig.getPolicyVersion();
    final String url =
        String.format(
            "/nds/cloudchefconf/%s/policy/al2/%s", _mmsGroup.getId().toString(), policyVersion);

    final JSONObject response =
        new JSONObject(doAgentApiCallGetWithStatus(_mmsGroup, _agentApiKey, url, 200));

    assertNotNull(response);
    assertTrue(response.getString("packagePolicy").contains("package"));

    // creds not supplied
    final JSONObject responseNoCreds = doJsonGet(url, 400);
    assertTrue(responseNoCreds.getString("message").contains("Credentials missing"));

    // invalid group id
    final String urlWithInvalidGroupId =
        String.format("/nds/cloudchefconf/%s/policy/al2/%s", "123", policyVersion);
    doAgentApiCallGetWithStatus(_mmsGroup, _agentApiKey, urlWithInvalidGroupId, 400);

    // invalid os
    final String invalidUrl =
        String.format(
            "/nds/cloudchefconf/%s/policy/al3/%s", _mmsGroup.getId().toString(), policyVersion);
    final JSONObject response404 =
        new JSONObject(doAgentApiCallGetWithStatus(_mmsGroup, _agentApiKey, invalidUrl, 404));
    assertTrue(response404.getString("detail").contains("does not exist"));
  }

  @Test
  public void testGetPackagePolicyMetadata() throws Exception {
    final Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("America/New_York"));
    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(
            calendar.get(Calendar.DAY_OF_WEEK), calendar.get(Calendar.HOUR_OF_DAY));
    _ndsGroupMaintenanceSvc.setUserDefinedMaintenanceWindow(
        _mmsGroup.getId(), maintenanceWindow, null);

    final String url =
        String.format("/nds/cloudchefconf/%s/policyMetadata/al2", _mmsGroup.getId().toString());
    final JSONObject response =
        new JSONObject(doAgentApiCallGetWithStatus(_mmsGroup, _agentApiKey, url, 200));
    final JSONObject maintenanceWindowResult = response.getJSONObject(MAINTENANCE_WINDOW);
    assertEquals(
        maintenanceWindow.getHourOfDay().intValue(), maintenanceWindowResult.getInt("hourOfDay"));
    assertEquals(
        maintenanceWindow.getDayOfWeek().intValue(), maintenanceWindowResult.getInt("dayOfWeek"));
    assertTrue(maintenanceWindowResult.getBoolean("isUserDefined"));
    assertFalse(maintenanceWindowResult.getBoolean("startASAP"));
    assertFalse(response.getString(PACKAGE_POLICY_ETAG).isEmpty());
    assertFalse(response.getString(PACKAGE_POLICY_VERSION).isEmpty());

    // creds not supplied
    final JSONObject responseNoCreds = doJsonGet(url, 400);
    assertTrue(responseNoCreds.getString("message").contains("Credentials missing"));

    // invalid group id
    final String urlWithInvalidGroupId =
        String.format("/nds/cloudchefconf/%s/policyMetadata/al2", "123");
    doAgentApiCallGetWithStatus(_mmsGroup, _agentApiKey, urlWithInvalidGroupId, 400);

    // invalid os
    final String urlWithInvalidOs =
        String.format("/nds/cloudchefconf/%s/policyMetadata/al3", _mmsGroup.getId().toString());
    final JSONObject response404 =
        new JSONObject(doAgentApiCallGetWithStatus(_mmsGroup, _agentApiKey, urlWithInvalidOs, 404));
    assertTrue(response404.getString("detail").contains("No object found for s3 bucket"));
  }

  @Test
  public void testRequestServerReplacement() throws Exception {
    _clusterDescription
        .getReplicationSpecsWithShardData()
        .get(0)
        .getRegionConfigs()
        .replaceAll(
            r ->
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_WEST_1, AWSNDSInstanceSize.M20, 0, 0, 0, 3, 0));

    final NDSGroup ndsGroup = _groupDao.find(_mmsGroup.getId()).get();
    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, ndsGroup.getCloudProviderContainers().get(0).getId(), _clusterDescription);
    ((BasicDBObject) hardwareDoc.get("_id")).put("groupId", _mmsGroup.getId());
    final ReplicaSetHardware hardware = new ReplicaSetHardware(hardwareDoc);
    _hardwareDao.saveReplicaSafe(hardwareDoc);

    final String hostname = hardware.getHardware().get(0).getHostnameForAgents().get();
    final String endpoint =
        String.format("/nds/cloudchefconf/%s/%s/replace", _mmsGroup.getId().toString(), hostname);

    // Dry-run request, noop.
    doAgentApiCallPost(
        _mmsGroup,
        _agentApiKey,
        endpoint,
        new JSONObject(Map.of("dryRun", true, "reason", "dryRun true")));
    final InstanceHardware ihw0 =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();
    assertFalse(ihw0.needsForceReplacement());

    // Request with no data posted means dryRun to make double sure we mean the replacement.
    doAgentApiCallPost(
        _mmsGroup, _agentApiKey, endpoint, new JSONObject(Map.of("reason", "no dryRun")));
    final InstanceHardware ihw1 =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();
    assertFalse(ihw1.needsForceReplacement());

    // Request with dryRun==false.
    final String replacementReason = "dryRun false";
    doAgentApiCallPost(
        _mmsGroup,
        _agentApiKey,
        endpoint,
        new JSONObject(Map.of("dryRun", false, "reason", replacementReason)));
    final InstanceHardware ihw2 =
        _replicaSetHardwareSvc.getInstanceHardwareByHostname(hostname, _mmsGroup.getId()).get();
    assertTrue(ihw2.needsForceReplacement());

    // Verify that the audit event was created
    final List<Event> auditEvents =
        _auditSvc.findByEventTypeForGroup(
            _mmsGroup.getId(), NDSAudit.Type.NRA_REPLACEMENT_REQUESTED);
    assertFalse(
        auditEvents.isEmpty(), "Expected NRA_REPLACEMENT_REQUESTED audit event to be created");

    final NDSAudit auditEvent = (NDSAudit) auditEvents.get(0);
    assertEquals(NDSAudit.Type.NRA_REPLACEMENT_REQUESTED, auditEvent.getEventType());
    assertEquals(_mmsGroup.getId(), auditEvent.getGroupId());
    assertEquals(hostname, auditEvent.getHostname());
    assertEquals(replacementReason, auditEvent.getReason());
    assertTrue(auditEvent.isHidden(), "Audit event should be hidden");
  }
}
