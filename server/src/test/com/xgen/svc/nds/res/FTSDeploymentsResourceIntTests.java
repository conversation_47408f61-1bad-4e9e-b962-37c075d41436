package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.search.decoupled.api._public.model.SearchInstanceSizeAPI;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentResponseView;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentSpecView;
import com.xgen.cloud.search.decoupled.config._public.model.aws.AWSSearchInstanceSize;
import com.xgen.cloud.search.decoupled.test.DecoupledAPITestUtils;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class FTSDeploymentsResourceIntTests extends JUnit5BaseResourceTest {
  private static final String DEPLOYMENTS_URL = "/nds/clusters/%s/search/deployments";
  @Inject private DecoupledAPITestUtils _decoupledUtils;
  private Group _group;
  private AppUser _groupOwner;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan("groupName");
    _groupOwner = MmsFactory.createGroupOwnerUser(_group);
  }

  @Test
  public void testGetAllSearchDeployments() {
    _decoupledUtils.enableDecoupledFlags(_group);

    // create clusters
    final ClusterDescription cluster1 =
        _decoupledUtils.createAWSCluster(_group.getId(), "clusterName1");
    final ClusterDescription cluster2 =
        _decoupledUtils.createAWSCluster(_group.getId(), "clusterName2");

    // create deployments
    _decoupledUtils.createActiveSearchDescription(
        _group.getId(), cluster1.getUniqueId(), 2, AWSSearchInstanceSize.S20_HIGHCPU_NVME);
    _decoupledUtils.createActiveSearchDescription(
        _group.getId(), cluster2.getUniqueId(), 4, AWSSearchInstanceSize.S40_HIGHCPU_NVME);

    // assert GET response
    final JSONArray response = sendGetDeploymentsRequest(HttpStatus.SC_OK);
    assertEquals(response.length(), 2);

    // assert first search deployment
    final JSONObject expectedSpec1 =
        new JSONObject()
            .put(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD, 2)
            .put(
                ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD,
                SearchInstanceSizeAPI.API_INSTANCE_SIZE.S20_HIGHCPU_NVME.toString());
    assertResponseSpec(
        response
            .getJSONObject(0)
            .getJSONArray(ApiSearchDeploymentResponseView.Fields.SPECS_FIELD)
            .getJSONObject(0),
        expectedSpec1);

    // assert second search deployment
    final JSONObject expectedSpec2 =
        new JSONObject()
            .put(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD, 4)
            .put(
                ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD,
                SearchInstanceSizeAPI.API_INSTANCE_SIZE.S40_HIGHCPU_NVME.toString());
    assertResponseSpec(
        response
            .getJSONObject(1)
            .getJSONArray(ApiSearchDeploymentResponseView.Fields.SPECS_FIELD)
            .getJSONObject(0),
        expectedSpec2);
  }

  private JSONArray sendGetDeploymentsRequest(final int pExpectedStatus) {
    return sendGetDeploymentsRequest(pExpectedStatus, _groupOwner);
  }

  private JSONArray sendGetDeploymentsRequest(final int pExpectedStatus, final AppUser pUser) {
    final String url = String.format(DEPLOYMENTS_URL, _group.getId());
    return doAuthedJsonArrayGet(pUser, url, pExpectedStatus);
  }

  private void assertResponseSpec(final JSONObject pActualSpec, final JSONObject pExpectedSpec) {
    assertEquals(
        pActualSpec.getInt(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD),
        pExpectedSpec.getInt(ApiSearchDeploymentSpecView.Fields.NODE_COUNT_FIELD));
    assertEquals(
        pActualSpec.getString(ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD),
        pExpectedSpec.getString(ApiSearchDeploymentSpecView.Fields.INSTANCE_SIZE_FIELD));
  }
}
