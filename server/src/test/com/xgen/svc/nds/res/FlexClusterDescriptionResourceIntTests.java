package com.xgen.svc.nds.res;

import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.AutoIndexingView;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.AutoScalingView;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.ComputeAutoScalingView;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.DiskGBAutoScalingView;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._private.dao.FlexTenantMigrationDao;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexMTMCluster;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration.TenantStartingState;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.versions.PhasedVersionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription.FieldDefs;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionViewUtils;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.model.ui.HardwareSpecView;
import com.xgen.svc.nds.model.ui.RegionConfigView;
import com.xgen.svc.nds.model.ui.RegionSpecView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import com.xgen.svc.nds.svc.TenantClusterConfigurationSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class FlexClusterDescriptionResourceIntTests extends JUnit5BaseResourceTest {

  @Inject AutomationMongoDbVersionSvc _versionSvc;
  @Inject NDSClusterSvc _clusterSvc;
  @Inject NDSGroupSvc _groupSvc;
  @Inject MTMClusterDao _mtmClusterDao;
  @Inject TenantClusterConfigurationSvc _tenantClusterConfigurationSvc;
  @Inject AWSAccountDao _awsAccountDao;
  @Inject NDSUISvc _ndsUIsvc;
  @Inject ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private PhasedVersionDao _phasedVersionDao;
  @Inject FlexTenantMigrationDao _flexTenantMigrationDao;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  private Organization _organization;
  private Group _group;
  private AppUser _user;
  private NDSGroup _ndsGroup;
  private AppSettings _settings;
  private VersionUtils.Version _cdMongoDBVersion;

  private FlexMTMCluster _flexMtmCluster;

  private static final String BASE_URL = "/nds/flex";

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _clusterSvc.start();

    _organization = MmsFactory.createOrganizationWithNDSPlan("TestOrg");
    _group = MmsFactory.createGroup(_organization, "TestGroup");
    _user = MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));
    _ndsGroup = _groupSvc.ensureGroup(_group.getId());
    paymentMethodStubber.stubPaymentMethod(_organization.getId(), true);

    _settings = AppConfig.getAppSettings();

    addUser(
        _user.getUsername(),
        MmsFactory.DEFAULT_PASSWORD,
        _user.getFirstName(),
        _user.getLastName());

    _cdMongoDBVersion =
        VersionUtils.parse(
            _phasedVersionDao
                .findBySoftwareType(SoftwareType.MONGODB_8_0)
                .get()
                .getTargetVersion());

    _flexMtmCluster =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                "FlexMtm",
                FlexInstanceSize.FLEX,
                AWSRegionName.US_EAST_1,
                NDSSettings.getNewFlexInstanceVersion(_settings)));

    _mtmClusterDao.insert(_flexMtmCluster);

    // get the TenantClusterConfigurationSvc instance used by the app so that we clear the cache in
    // the proper scope
    AppConfig.getInstance(TenantClusterConfigurationSvc.class).clearCache();
    // also clear the cache in this scope since we're making calls to the service layer directly
    _tenantClusterConfigurationSvc.clearCache();

    // enable continuous delivery feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_CONTINUOUS_DELIVERY);
  }

  @Test
  public void testSingleFlexCluster() throws Exception {
    final String endpoint = String.format("%s/%s/%s", BASE_URL, _group.getId(), "flex1");

    // Invalid flex cluster name
    final String invalidEndpoint = String.format("%s/%s/%s", BASE_URL, _group.getId(), "@invalid");
    doHtmlGet(invalidEndpoint, HttpStatus.SC_UNAUTHORIZED);
    final JSONObject retVal1 = doAuthedJsonGet(_user, invalidEndpoint, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        CommonErrorCode.INVALID_PARAMETER.name(), retVal1.getString(ApiError.ERROR_CODE_FIELD));

    createAWSCluster("foo");
    createFlexCluster("flex1");

    // Invalid cloud provider
    final String awsEndpoint = String.format("%s/%s/%s", BASE_URL, _group.getId(), "foo");
    doHtmlGet(awsEndpoint, HttpStatus.SC_UNAUTHORIZED);
    final JSONObject retVal2 = doAuthedJsonGet(_user, awsEndpoint, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(), retVal2.getString(ApiError.ERROR_CODE_FIELD));

    doHtmlGet(endpoint, HttpStatus.SC_UNAUTHORIZED);
    final JSONObject retVal4 = doAuthedJsonGet(_user, endpoint, HttpStatus.SC_OK);

    assertEquals("flex1", retVal4.getString("name"));
    assertEquals(_group.getId().toString(), retVal4.getString("groupId"));
    assertFalse(retVal4.getBoolean("isMonitoringPaused"));
  }

  @Test
  public void testCreateFlexInstance() {
    final String endpoint = String.format("%s/%s", BASE_URL, _group.getId());

    // Invalid cloud provider
    final JSONObject awsClusterDescription = new JSONObject();

    final JSONObject retVal1 =
        doAuthedJsonPost(_user, endpoint, awsClusterDescription, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(), retVal1.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject flexClusterDescription = getFlexClusterDescription();
    doAuthedJsonPost(_user, endpoint, flexClusterDescription, HttpStatus.SC_OK);

    final JSONObject retVal2 = getExistingFlexInstance(_group.getId(), "foo");
    assertEquals("foo", retVal2.getString("name"));
    assertEquals(_group.getId().toString(), retVal2.getString("groupId"));
    assertEquals(
        _cdMongoDBVersion.getMajorVersionString(), retVal2.getString("mongoDBMajorVersion"));
  }

  @Test
  public void testUpdateFlexCluster() throws Exception {
    final String endpoint =
        String.format("%s/%s/%s", BASE_URL, _group.getId(), "flexClusterToUpdate");

    createAWSCluster("foo");
    createFlexCluster("flexClusterToUpdate");

    final JSONObject existingFlexClusterDescription = doAuthedJsonGet(_user, endpoint);
    assertFalse(
        existingFlexClusterDescription.getBoolean(FieldDefs.TERMINATION_PROTECTION_ENABLED));

    final JSONObject updatedFlexClusterDescription =
        existingFlexClusterDescription.put(FieldDefs.TERMINATION_PROTECTION_ENABLED, true);

    doAuthedJsonPatch(_user, endpoint, updatedFlexClusterDescription, HttpStatus.SC_OK);

    final JSONObject refreshedFlexClusterDescription = doAuthedJsonGet(_user, endpoint);
    assertTrue(
        refreshedFlexClusterDescription.getBoolean(FieldDefs.TERMINATION_PROTECTION_ENABLED));
  }

  @Test
  public void testUpdateFlexCluster_FlexTenantMigration() throws Exception {
    final String endpoint =
        String.format("%s/%s/%s", BASE_URL, _group.getId(), "flexClusterToUpdate");
    createFlexCluster("flexClusterToUpdate");
    final ClusterDescription flexCluster =
        _clusterDescriptionDao.findByName(_group.getId(), "flexClusterToUpdate").orElseThrow();

    // Flex Tenant Migration with rollback pending
    final FlexTenantMigration flexTenantMigration =
        new FlexTenantMigration(
            flexCluster.getName(),
            flexCluster.getGroupId(),
            flexCluster.getUniqueId(),
            new FlexTenantMigration.MTM("mtmName", new ObjectId(), new ObjectId()),
            CloudProvider.FREE,
            FreeInstanceSize.M2,
            MigrationStatus.ROLLBACK_PENDING,
            Date.from(Instant.EPOCH),
            null,
            null,
            null,
            null,
            null,
            null,
            Collections.emptyList(),
            new TenantStartingState(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M2), null));
    _flexTenantMigrationDao.saveMigration(flexTenantMigration);
    // Update should fail
    {
      final JSONObject response =
          doAuthedJsonPatch(_user, endpoint, new JSONObject(), HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Tenant Migration with migration successful -- update works
    {
      _flexTenantMigrationDao.updateMigration(
          flexTenantMigration.toBuilder().setStatus(MigrationStatus.MIGRATION_SUCCESS).build());
      final JSONObject existingFlexClusterDescription = doAuthedJsonGet(_user, endpoint);
      assertFalse(
          existingFlexClusterDescription.getBoolean(FieldDefs.TERMINATION_PROTECTION_ENABLED));

      final JSONObject response =
          doAuthedJsonPatch(
              _user,
              endpoint,
              existingFlexClusterDescription.put(FieldDefs.TERMINATION_PROTECTION_ENABLED, true),
              HttpStatus.SC_OK);
      assertTrue(response.getBoolean(FieldDefs.TERMINATION_PROTECTION_ENABLED));
    }
  }

  @Test
  public void testDeleteFlexCluster() throws Exception {
    final String endpoint =
        String.format("%s/%s/%s", BASE_URL, _group.getId(), "flexClusterToDelete");

    createAWSCluster("foo");
    createFlexCluster("flexClusterToDelete");

    // Invalid cloud provider
    final String awsEndpoint = String.format("%s/%s/%s", BASE_URL, _group.getId(), "foo");
    final JSONObject retVal1 = doAuthedJsonDelete(_user, awsEndpoint, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(), retVal1.getString(ApiError.ERROR_CODE_FIELD));

    doAuthedJsonDelete(_user, endpoint, HttpStatus.SC_ACCEPTED);

    final JSONObject retVal2 = getExistingFlexInstance(_group.getId(), "flexClusterToDelete");
    assertEquals("flexClusterToDelete", retVal2.getString("name"));
    assertEquals("DELETING", retVal2.getString("state"));

    // Fail: delete with Termination Protection enabled
    createFlexClusterWithTerminationProtectionEnabled("flexClusterCannotDelete");

    // force flex cluster to finish provisioning
    final String[] hosts = new String[] {"a", "b", "c"};
    final String[] privateHosts = new String[] {"a-pri", "b-pri", "c-pri"};
    final NDSClusterSvc clusterSvc = spy(_clusterSvc);
    doReturn(hosts).when(clusterSvc).generateMongoUriHosts(any(), any(), any());
    doReturn(privateHosts).when(clusterSvc).generatePrivateMongoUriHosts(any(), any());
    clusterSvc.clusterChangesCompleted(
        _ndsGroup, "flexClusterCannotDelete", null, null, null, null);

    final JSONObject result =
        doAuthedJsonDelete(
            _user,
            String.format("%s/%s/%s", BASE_URL, _group.getId(), "flexClusterCannotDelete"),
            SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.CANNOT_TERMINATE_CLUSTER_WHEN_TERMINATION_PROTECTION_ENABLED.name(),
        result.getString("errorCode"));
  }

  @Test
  public void testDeleteFlexCluster_FlexTenantMigration() throws Exception {
    final String endpoint =
        String.format("%s/%s/%s", BASE_URL, _group.getId(), "flexClusterToDelete");
    createFlexCluster("flexClusterToDelete");
    final ClusterDescription flexCluster =
        _clusterDescriptionDao.findByName(_group.getId(), "flexClusterToDelete").orElseThrow();

    // Flex Tenant Migration with rollback pending
    final FlexTenantMigration flexTenantMigration =
        new FlexTenantMigration(
            flexCluster.getName(),
            flexCluster.getGroupId(),
            flexCluster.getUniqueId(),
            new FlexTenantMigration.MTM("mtmName", new ObjectId(), new ObjectId()),
            CloudProvider.FREE,
            FreeInstanceSize.M2,
            MigrationStatus.ROLLBACK_PENDING,
            Date.from(Instant.EPOCH),
            null,
            null,
            null,
            null,
            null,
            null,
            Collections.emptyList(),
            new TenantStartingState(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M2), null));
    _flexTenantMigrationDao.saveMigration(flexTenantMigration);

    // delete should fail
    {
      final JSONObject response = doAuthedJsonDelete(_user, endpoint, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Tenant Migration with migration successful -- delete works
    {
      _flexTenantMigrationDao.updateMigration(
          flexTenantMigration.toBuilder().setStatus(MigrationStatus.MIGRATION_SUCCESS).build());
      doAuthedJsonDelete(_user, endpoint, HttpStatus.SC_ACCEPTED);
      final JSONObject response = getExistingFlexInstance(_group.getId(), "flexClusterToDelete");
      assertEquals("flexClusterToDelete", response.getString("name"));
      assertEquals("DELETING", response.getString("state"));
    }
  }

  @Test
  public void testGetOptions() {
    final String endpoint = String.format("%s/%s/options", BASE_URL, _group.getId());

    // Just 1 AWS options
    final JSONObject returnedOptions1 = doAuthedJsonGet(_user, endpoint);

    final JSONArray regionOptions1 = returnedOptions1.getJSONArray("regions");
    assertEquals(1L, regionOptions1.length());
    assertEquals(
        _flexMtmCluster.getRegion().getProvider().name(),
        ((JSONObject) regionOptions1.get(0)).get("provider"));
    assertEquals(
        _flexMtmCluster.getRegion().getName(), ((JSONObject) regionOptions1.get(0)).get("key"));

    // Add in another cloud provider
    final FlexMTMCluster azureMTM =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                "azureFlexMtm",
                FlexInstanceSize.FLEX,
                AzureRegionName.US_EAST_2,
                NDSSettings.getNewFlexInstanceVersion(_settings)));
    _mtmClusterDao.insert(azureMTM);
    AppConfig.getInstance(TenantClusterConfigurationSvc.class).clearCache();
    final JSONObject returnedOptions2 = doAuthedJsonGet(_user, endpoint);

    final JSONArray regionOptions2 = returnedOptions2.getJSONArray("regions");
    assertEquals(2L, regionOptions2.length());

    final List<JSONObject> allRegions2 =
        List.of(regionOptions2.getJSONObject(0), regionOptions2.getJSONObject(1));

    assertTrue(
        allRegions2.stream()
            .anyMatch(
                region ->
                    region.get("provider").equals(_flexMtmCluster.getRegion().getProvider().name())
                        && region.get("key").equals(_flexMtmCluster.getRegion().getName())));

    assertTrue(
        allRegions2.stream()
            .anyMatch(
                region ->
                    region.get("provider").equals(azureMTM.getRegion().getProvider().name())
                        && region.get("key").equals(azureMTM.getRegion().getName())));

    // Add in another region in an existing cloud provider
    final FlexMTMCluster awsFlexMtm =
        new FlexMTMCluster(
            NDSModelTestFactory.getFlexMTMCluster(
                "awsFlexMtm",
                FlexInstanceSize.FLEX,
                AWSRegionName.US_EAST_2,
                NDSSettings.getNewFlexInstanceVersion(_settings)));
    _mtmClusterDao.insert(awsFlexMtm);

    AppConfig.getInstance(TenantClusterConfigurationSvc.class).clearCache();
    final JSONObject returnedOptions3 = doAuthedJsonGet(_user, endpoint);

    final JSONArray regionOptions3 = returnedOptions3.getJSONArray("regions");
    assertEquals(3L, regionOptions3.length());

    final List<JSONObject> allRegions3 =
        List.of(
            regionOptions3.getJSONObject(0),
            regionOptions3.getJSONObject(1),
            regionOptions3.getJSONObject(2));

    assertTrue(
        allRegions3.stream()
            .anyMatch(
                region ->
                    region.get("provider").equals(_flexMtmCluster.getRegion().getProvider().name())
                        && region.get("key").equals(_flexMtmCluster.getRegion().getName())));

    assertTrue(
        allRegions3.stream()
            .anyMatch(
                region ->
                    region.get("provider").equals(awsFlexMtm.getRegion().getProvider().name())
                        && region.get("key").equals(awsFlexMtm.getRegion().getName())));

    assertTrue(
        allRegions3.stream()
            .anyMatch(
                region ->
                    region.get("provider").equals(azureMTM.getRegion().getProvider().name())
                        && region.get("key").equals(azureMTM.getRegion().getName())));
  }

  @Test
  public void testCostEstimate() {
    final String endpoint = String.format("%s/%s/costEstimate", BASE_URL, _group.getId());

    // Invalid cloud provider
    final JSONObject awsClusterDescription = new JSONObject();
    awsClusterDescription.put("@provider", "AWS");
    final JSONObject retVal1 =
        doAuthedJsonPost(_user, endpoint, awsClusterDescription, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(), retVal1.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject flexClusterDescription = getFlexClusterDescription();
    final JSONObject retVal2 =
        doAuthedJsonPost(_user, endpoint, flexClusterDescription, HttpStatus.SC_OK);

    assertEquals(3000L, retVal2.getLong("flexMonthlyMaxEstimateCents"));
    assertEquals(802L, retVal2.getLong("flexMonthlyMinEstimateCents"));
    assertEquals(802L, retVal2.getLong("monthlyEstimateCents"));
    assertEquals(802L, retVal2.getLong("monthlyBaseEstimateCents"));
    assertEquals(26L, retVal2.getLong("dailyEstimateCents"));
    assertEquals(0L, retVal2.getLong("biConnectorSustainedDailyCents"));
    assertEquals(0L, retVal2.getLong("biConnectorMaxDailyCents"));
    assertEquals(0L, retVal2.getLong("biConnectorMaxMonthlyCents"));
    assertEquals(0L, retVal2.getLong("advancedSecurityDailyCents"));
    assertEquals(0L, retVal2.getLong("advancedSecurityMonthlyCents"));
    assertEquals(0L, retVal2.getLong("enterpriseAuditingDailyCents"));
    assertEquals(0L, retVal2.getLong("enterpriseAuditingMonthlyCents"));
  }

  private JSONObject getExistingFlexInstance(
      final ObjectId pGroupId, final String pFlexClusterName) {
    final String endpoint = String.format("%s/%s/%s", BASE_URL, pGroupId, pFlexClusterName);
    return doAuthedJsonGet(_user, endpoint, HttpStatus.SC_OK);
  }

  private void createAWSCluster(final String pClusterName) throws Exception {
    final ClusterDescriptionView view =
        spy(
            ClusterDescriptionViewUtils.getDefaultAWSReplicaSetView(
                NDSModelTestFactory.TEST_MONGODB_VERSION.getMajorVersionString(),
                _ndsGroup,
                new HashMap<>()));

    doReturn(pClusterName).when(view).getName();
    doReturn(_group.getId()).when(view).getGroupId();

    final AWSAccount account = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(account);
    _ndsUIsvc.createCluster(
        _organization,
        _group.getId(),
        view,
        ClusterCreateContext.forIntegrationTest(),
        _user,
        AuditInfoHelpers.fromSystem(),
        null,
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
  }

  private void createFlexCluster(final String pFlexClusterName) throws Exception {
    final ClusterDescriptionView view =
        spy(
            ClusterDescriptionViewUtils.getDefaultFlexClusterDescriptionView(
                _settings, FlexInstanceSize.FLEX));
    doReturn(pFlexClusterName).when(view).getName();
    doReturn(_group.getId()).when(view).getGroupId();
    doReturn((double) FlexInstanceSize.FLEX.getMaxAllowedDiskSizeGB(false))
        .when(view)
        .getDiskSizeGB();

    _ndsUIsvc.createCluster(
        _organization,
        _group.getId(),
        view,
        ClusterCreateContext.forIntegrationTest(),
        _user,
        AuditInfoHelpers.fromSystem(),
        null,
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
  }

  private void createFlexClusterWithTerminationProtectionEnabled(final String pFlexClusterName)
      throws Exception {
    final ClusterDescriptionView view =
        spy(
            ClusterDescriptionViewUtils.getDefaultFlexClusterDescriptionView(
                _settings, FlexInstanceSize.FLEX));
    doReturn(pFlexClusterName).when(view).getName();
    doReturn(_group.getId()).when(view).getGroupId();
    doReturn((double) FlexInstanceSize.FLEX.getMaxAllowedDiskSizeGB(false))
        .when(view)
        .getDiskSizeGB();
    doReturn(true).when(view).isTerminationProtectionEnabled();

    _ndsUIsvc.createCluster(
        _organization,
        _group.getId(),
        view,
        ClusterCreateContext.forIntegrationTest(),
        _user,
        AuditInfoHelpers.fromSystem(),
        null,
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
  }

  private JSONObject getFlexClusterDescription() {
    final JSONObject flexReplicationSpecs = new JSONObject();
    flexReplicationSpecs.put(ReplicationSpecView.FieldDefs.ID, oid(1));
    flexReplicationSpecs.put(ReplicationSpecView.FieldDefs.ZONE_NAME, "zone1");
    flexReplicationSpecs.put(ReplicationSpecView.FieldDefs.NUM_SHARDS, 1);

    final JSONArray regionConfigs = new JSONArray();
    final JSONObject regionConfig = new JSONObject();
    regionConfigs.put(regionConfig);

    flexReplicationSpecs.put(ReplicationSpecView.FieldDefs.REGION_CONFIGS, regionConfigs);

    regionConfig.put(RegionConfigView.FieldDefs.PRIORITY, RegionConfig.MAX_PRIORITY);
    regionConfig.put(RegionConfigView.FieldDefs.REGION_NAME, "US_EAST_1");
    regionConfig.put(RegionConfigView.FieldDefs.CLOUD_PROVIDER, CloudProvider.FLEX.name());

    regionConfig.put(
        RegionConfigView.FieldDefs.AUTOSCALING,
        new JSONObject()
            .put(
                AutoScalingView.FieldDefs.COMPUTE,
                new JSONObject().put(ComputeAutoScalingView.FieldDefs.ENABLED, false))
            .put(
                AutoScalingView.FieldDefs.DISK_GB,
                new JSONObject().put(DiskGBAutoScalingView.FieldDefs.ENABLED, false))
            .put(
                AutoScalingView.FieldDefs.AUTO_INDEXING,
                new JSONObject().put(AutoIndexingView.FieldDefs.ENABLED, false)));

    regionConfig.put(
        RegionConfigView.FieldDefs.ELECTABLE_SPECS,
        new JSONObject()
            .put(HardwareSpecView.FieldDefs.BACKING_PROVIDER, CloudProvider.AWS.name())
            .put(HardwareSpecView.FieldDefs.INSTANCE_SIZE, FlexInstanceSize.FLEX.name())
            .put(HardwareSpecView.FieldDefs.NODE_COUNT, RegionSpecView.DEFAULT_ELECTABLE_NODES));
    regionConfig.put(
        RegionConfigView.FieldDefs.ANALYTICS_SPECS,
        new JSONObject()
            .put(HardwareSpecView.FieldDefs.BACKING_PROVIDER, CloudProvider.AWS.name())
            .put(HardwareSpecView.FieldDefs.INSTANCE_SIZE, FlexInstanceSize.FLEX.name())
            .put(HardwareSpecView.FieldDefs.NODE_COUNT, 0));
    regionConfig.put(
        RegionConfigView.FieldDefs.READ_ONLY_SPECS,
        new JSONObject()
            .put(HardwareSpecView.FieldDefs.BACKING_PROVIDER, CloudProvider.AWS.name())
            .put(HardwareSpecView.FieldDefs.INSTANCE_SIZE, FlexInstanceSize.FLEX.name())
            .put(HardwareSpecView.FieldDefs.NODE_COUNT, 0));

    final JSONObject flexClusterDescription = new JSONObject();
    flexClusterDescription.put(ClusterDescriptionView.FieldDefs.DISK_SIZE_GB_LIMIT, "1024");
    flexClusterDescription.put(ClusterDescriptionView.FieldDefs.TENANT_BACKUP_ENABLED, "false");
    flexClusterDescription.put("name", "foo");
    flexClusterDescription.put("groupId", _group.getId());
    flexClusterDescription.put("mongoDBVersion", VersionUtils.FOUR_ZERO_ZERO.toString());
    flexClusterDescription.put(
        "mongoDBMajorVersion", VersionUtils.FOUR_ZERO_ZERO.getMajorVersionString());
    flexClusterDescription.put("clusterType", "REPLICASET");
    flexClusterDescription.put(FieldDefs.INTERNAL_CLUSTER_ROLE, InternalClusterRole.NONE.name());
    flexClusterDescription.put("replicationSpecList", new JSONArray().put(flexReplicationSpecs));
    flexClusterDescription.put("diskSizeGB", "3");
    flexClusterDescription.put("backupEnabled", "false");
    flexClusterDescription.put("diskBackupEnabled", "false");
    flexClusterDescription.put(
        "geoSharding",
        new JSONObject()
            .put(GeoShardingView.FieldDefs.CUSTOM_ZONE_MAPPING, new JSONObject())
            .put(GeoShardingView.FieldDefs.MANAGED_NAMESPACES, new JSONArray()));
    flexClusterDescription.put("encryptionAtRestProvider", "NONE");
    flexClusterDescription.put(
        FieldDefs.VERSION_RELEASE_SYSTEM, VersionReleaseSystem.CONTINUOUS.name());

    return flexClusterDescription;
  }
}
