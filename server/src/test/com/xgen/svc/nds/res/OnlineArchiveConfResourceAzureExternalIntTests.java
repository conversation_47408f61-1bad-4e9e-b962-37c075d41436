package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.error.AzureApiException;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._public.model.DataLandingZoneConfig.AzureDataLandingZoneConfig;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.CollectionType;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveTestUtils;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(GuiceTestExtension.class)
public class OnlineArchiveConfResourceAzureExternalIntTests extends JUnit5BaseResourceTest {
  private static final Logger LOG =
      LoggerFactory.getLogger(OnlineArchiveConfResourceAzureExternalIntTests.class);
  private static final String BASE_URL = "/conf/onlinearchive";
  private static final String CONFIGURE_URL = BASE_URL + "/%s/cluster/%s?ah=%s&sk=%s&av=%s";

  private static final String GET_DLZ_BUCKET_CREDENTIALS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/dataLandingZoneBucketCredentials?ah=%s&sk=%s";

  private static final String GROUP_NAME = "testGroup";
  private static final String AZURE_CLUSTER_NAME = "azureTestCluster";
  private static final String AGENT_VERSION = "1.0";
  private static final String AZURE_SESSION_KEY = "sk1";
  private static final String AZURE_HOSTNAME = "hostname1";

  private static final String AZURE_STORAGE_ACCOUNT_NAME = "mdbdlzoaeaus2l";
  private static final String AZURE_CONTAINER_NAME = "testcontainer";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private OnlineArchiveSvc _onlineArchiveSvc;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private OrganizationSvc _orgSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private OnlineArchiveTestUtils _onlineArchiveTestUtils;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private AzureApiSvc _azureApiSvc;

  private Group _group;
  private AgentApiKey _azureAgentApiKey;
  private AzureRegionName _azureRegion;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();

    _group = MmsFactory.createGroupWithNDSPlan(GROUP_NAME);
    final Organization org = _orgSvc.findById(_group.getOrgId());
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    final AppUser appUser = MmsFactory.createGroupOwnerUser(_group);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findDraftOrEmpty(_group.getId(), appUser.getId());
    final Auth auth = automationConfig.getDeployment().getAuth();
    auth.setAutoUser("username");
    auth.setAutoPwd("password");
    _automationConfigSvc.saveDraft(automationConfig, appUser, org, _group);
    _automationConfigSvc.publish(org, _group, appUser);
    _dataLakeTestUtils.setUp();

    _azureRegion = AzureRegionName.US_EAST_2;

    _azureAgentApiKey = MmsFactory.generateApiKey(_group.getId(), new ObjectId());
    final BasicDBObject azureClusterDescription =
        NDSModelTestFactory.getAzureClusterDescription(_group.getId(), AZURE_CLUSTER_NAME);
    azureClusterDescription.append(ClusterDescription.FieldDefs.UNIQUE_ID, ObjectId.get());
    _clusterDescriptionDao.saveReplicaSafe(azureClusterDescription);

    // create instance hardware
    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(new ObjectId(), _azureRegion);
    final NDSGroup ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final ObjectId azureContainerId = _ndsGroupSvc.addCloudContainer(ndsGroup, azureContainer);

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(AZURE_CLUSTER_NAME, _group.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
                ndsGroup.getGroupId(), AZURE_CLUSTER_NAME, AZURE_CLUSTER_NAME, List.of())
            .next()
            .rsId(),
        true,
        false,
        ObjectId.get());
    final AzureInstanceHardware azureInstanceHardware =
        new AzureInstanceHardware(
            InstanceHardware.getEmptyHardware(CloudProvider.AZURE, ObjectId.get(), new Date(), 0)
                .append(InstanceHardware.FieldDefs.CLOUD_PROVIDER_CONTAINER_ID, azureContainerId)
                .append(
                    InstanceHardware.FieldDefs.HOSTNAMES,
                    new Hostnames(AZURE_HOSTNAME, AZURE_HOSTNAME).toDBList())
                .append(InstanceHardware.FieldDefs.PROVISIONED, true)
                .append(
                    InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                    InstanceHostname.HostnameScheme.PUBLIC.name())
                .append(InstanceHardware.FieldDefs.PUBLIC_IP, getAzureExternalIpAddress()));

    assertTrue(
        _replicaSetHardwareDao.setCloudProviderHardware(
            replicaSetHardwareId, new BasicDBList(), List.of(azureInstanceHardware), false));

    _onlineArchiveTestUtils.setUpAzureTestStorageAccount(_group);
  }

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testGetArchiveDataLandingZoneBucketAzureCredentials() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(AZURE_CLUSTER_NAME);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    // manually override DLZ config to use the test container rather than actual DLZ container.
    _onlineArchiveDao.updateDataLandingZoneConfig(
        onlineArchive.getId(),
        new AzureDataLandingZoneConfig(
            _azureRegion, AZURE_CONTAINER_NAME, AZURE_STORAGE_ACCOUNT_NAME));

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _azureAgentApiKey,
        String.format(
            CONFIGURE_URL,
            _group.getId(),
            AZURE_CLUSTER_NAME,
            AZURE_HOSTNAME,
            AZURE_SESSION_KEY,
            AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final JSONObject response =
        new JSONObject(
            doAgentApiCallGet(
                _group,
                _azureAgentApiKey,
                String.format(
                    GET_DLZ_BUCKET_CREDENTIALS_URL,
                    _group.getId(),
                    AZURE_CLUSTER_NAME,
                    onlineArchive.getId(),
                    AZURE_HOSTNAME,
                    AZURE_SESSION_KEY)));

    assertNotNull(response.getString("sasUri"));
    assertNotNull(response.getString("sasToken"));
    assertNotNull(response.getString("expirationDate"));

    final String blobPath = "testBlob" + new ObjectId();

    // check for 403
    try {
      final InputStream stream = new ByteArrayInputStream("hi".getBytes());
      _azureApiSvc.uploadBlob(
          "wrongtestcontainer",
          response.getString("sasUri"),
          response.getString("sasToken"),
          blobPath,
          stream,
          LOG);
      fail();
    } catch (final AzureApiException pE) {
      assertEquals(CommonErrorCode.NO_AUTHORIZATION, pE.getErrorCode());
    }

    try {
      final InputStream stream = new ByteArrayInputStream("hi".getBytes());
      _azureApiSvc.uploadBlob(
          AZURE_CONTAINER_NAME,
          response.getString("sasUri"),
          response.getString("sasToken"),
          blobPath,
          stream,
          LOG);
    } catch (final AzureApiException pE) {
      assertEquals(CommonErrorCode.NOT_FOUND, pE.getErrorCode());
    } finally {
      _azureApiSvc.deleteBlob(
          AZURE_CONTAINER_NAME,
          response.getString("sasUri"),
          response.getString("sasToken"),
          blobPath,
          LOG);
    }
  }

  private OnlineArchive getOnlineArchive(final String clusterName) {
    final String dbName = "mydb";
    final UUID collectionUUID = new UUID(1L, 1L);
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("string", "field1", 0),
            new PartitionField("string", "field2", 1),
            new PartitionField("date", "dateField", 2));

    return new OnlineArchive.Builder()
        .setArchiveId(ObjectId.get())
        .setClusterId(_group.getId(), clusterName)
        .setDbName(dbName)
        .setCollName("mycoll")
        .setCollectionUUID(collectionUUID)
        .setPartitionFields(partitionFields)
        .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
        .setState(OnlineArchive.State.ACTIVE)
        .setCollectionType(CollectionType.STANDARD)
        .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
        .setDataProcessRegion(
            new OnlineArchive.DataProcessRegion(CloudProvider.PROVIDER_AZURE, _azureRegion.name()))
        .build();
  }

  private String getAzureExternalIpAddress() {
    try {
      return HttpUtils.getInstance()
          .doGetStr("http://checkip.azurewebsites.net/")
          .replace("Current IP Address: ", "")
          .trim();
    } catch (Exception e) {
      return "Unknown";
    }
  }
}
