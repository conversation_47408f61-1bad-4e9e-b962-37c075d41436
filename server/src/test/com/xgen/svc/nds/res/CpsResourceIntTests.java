package com.xgen.svc.nds.res;

import static com.xgen.svc.nds.serverless.model.ServerlessTestFactory.getServerlessMockedMetric;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.amazonaws.auth.AWSCredentials;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._private.dao.DataProtectionSettingsDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.ClusterType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.CpsPolicyUtils;
import com.xgen.cloud.cps.backupjob._public.model.CustomerCollectionMetadata;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings.State;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.backupjob._public.ui.DataProtectionSettingsView;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._private.dao.CpsBackupCursorFileListsDao;
import com.xgen.cloud.cps.restore._private.dao.RollingReplacementJobDao;
import com.xgen.cloud.cps.restore._public.model.AWSExportBucket;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.ServerlessTenant;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest.IndexRestoreOption;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest.RollbackStrategy;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest.WriteStrategy;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList.CursorFile;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.StrategyName;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.UserRequestType;
import com.xgen.cloud.cps.restore._public.model.ExportBucket;
import com.xgen.cloud.cps.restore._public.model.ExportBucket.BucketType;
import com.xgen.cloud.cps.restore._public.model.NodeToBeReplaced;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.RollingReplacementJob;
import com.xgen.cloud.cps.restore._public.model.TopologyHandler;
import com.xgen.cloud.cps.restore._public.ui.CollectionRestoreRequestView;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.activity._public.event.audit.ExportBucketAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.FieldDefs;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._private.dao.FlexTenantMigrationDao;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration.TenantStartingState;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.metrics._public.svc.NDSComputeClusterMetricsSvc;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.monitoring._public.model.ServerlessMockedMetricType;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware.ReplicaSetHardwareIds;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccess;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAzureServicePrincipal;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessBackupOptions;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantProviderOptions;
import com.xgen.cloud.nds.serverless._public.model.metrics.ServerlessMetricName;
import com.xgen.cloud.nds.tenant._private.dao.backup.TenantRestoreDao;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask.SnapshotType;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserAllowList;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseResourceTestCommon;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.dao.TenantBackupSnapshotDao;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.TenantBackupSnapshot;
import com.xgen.svc.nds.serverless.dao.NDSServerlessMockedMetricsDao;
import com.xgen.svc.nds.serverless.model.ServerlessTestFactory;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.cps.CpsCollectionMetadataBackupSvc;
import com.xgen.svc.nds.svc.cps.CpsCollectionRestoreJobSvc;
import com.xgen.svc.nds.svc.cps.CpsExportSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.ws.rs.core.Response;
import java.io.IOException;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.Consts;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.slf4j.Logger;

public class CpsResourceIntTests extends BaseResourceTestCommon {

  private static final ObjectId GROUP_ID = oid(148);
  private static final ObjectId GROUP_ID_2 = oid(151);

  private static final ObjectId SERVERLESS_GROUP_ID = oid(118);
  private static final ObjectId SERVERLESS_CLUSTER_UNIQUE_ID = oid(100);
  private static final ObjectId SERVERLESS_CLUSTER_3_UNIQUE_ID = oid(103);
  private static final ObjectId SERVERLESS_MTM_CLUSTER_DESCRIPTION_UNIQUE_ID = oid(101);
  private static final ObjectId SERVERLESS_CLUSTER_CLOUD_CONTAINER_ID = oid(1000);
  private static final ObjectId SERVERLESS_BACKUP_SNAPSHOT_ID = oid(15);

  private static final ObjectId RESTORE_JOB_ID = oid(1);
  private static final ObjectId COLLECTION_RESTORE_JOB_ID = oid(501);
  private static final ObjectId BACKUP_SNAPSHOT_ID_1 = oid(10);

  private static final String CLUSTER_NAME = "Azure5";
  private static final String CLUSTER_NAME_2 = "Azure6";
  private static final ObjectId CLUSTER_UNIQUE_ID = oid(17);
  private static final ObjectId CLUSTER_UNIQUE_ID_2 = oid(1111);

  private static final String SHARDED_CLUSTER_NAME = "ShardedCluster155";
  private static final String SERVERLESS_INSTANCE_NAME = "serverless1";
  private static final String SERVERLESS_INSTANCE_3_NAME = "serverless3";
  private static final String SERVERLESS_MTM_NAME = "serverlessMTM";

  private static final String URL_PREFIX = "/nds/backup/";
  private static final Date LAST_TENANT_SNAPSHOT_DATE = Date.from(Instant.EPOCH);
  private static final Date TENANT_RESTORE_DATE = Date.from(Instant.EPOCH.plus(Duration.ofDays(1)));

  private static final List<CursorFile> WTC_FILE_LIST_1 =
      Arrays.asList(
          new CursorFile("/tmp/var/log/somefile_1.wt", "admin.system.roles", 1024),
          new CursorFile("/tmp/var/log/somefile_2.bson", "config.system.roles", 1024),
          new CursorFile("/tmp/var/log/index-3--4776843778073959199.wt", "db.col1", 1024),
          new CursorFile(
              "/tmp/var/log/0_realllyloooongfile" + genStr('e', 5000), "db2.col2", 1024));

  private static final List<CursorFile> WTC_FILE_LIST_2 =
      Arrays.asList(
          new CursorFile("/tmp/var/log/deep/blob/mongod.lock", "local.repl.coll", 1024),
          new CursorFile("/tmp/var/log/deep/blob/README", "test.system.data", 1024));
  private static final List<String> EXPECTED_COMPLETE_FILE_LIST =
      List.of(
          "somefile_1.wt",
          "somefile_2.bson",
          "index-3--4776843778073959199.wt",
          "0_realllyloooongfile" + genStr('e', 5000),
          "deep/blob/mongod.lock",
          "deep/blob/README");

  private static final List<String> EXPECTED_COMPLETE_FILE_LIST_EAR =
      List.of(
          "somefile_1.wt",
          "somefile_2.bson",
          "index-3--4776843778073959199.wt",
          "0_realllyloooongfile" + genStr('e', 5000),
          "deep/blob/mongod.lock",
          "deep/blob/README",
          "cloudProviderKeys/",
          "cloudProviderKeys");

  // see ndsGroups.json.ftl networkPermissionList
  private static final List<String> EXPECTED_MANUAL_DOWNLOAD_IP_ALLOWED_LIST =
      List.of("***********/32", "***********/24", "***********/32");

  private static final List<List<String>> SERVERLESS_TO_DEDICATED_TARGET_IPS =
      List.of(List.of("*********", "*********"), List.of("**********", "**********"));

  private static final List<String> EXPECTED_SERVERLESS_TO_DEDICATED_IP_ALLOWED_LIST =
      Stream.concat(
              SERVERLESS_TO_DEDICATED_TARGET_IPS.stream().flatMap(List::stream).toList().stream()
                  .map(NetUtils::toIpv4CidrNotation),
              Arrays.stream(NetUtils.PRIVATE_RANGES))
          .collect(Collectors.toList());

  private static final List<String> SERVERLESS_TO_SERVERLESS_TARGET_IPS =
      List.of("************", "************");

  private static final List<String> EXPECTED_SERVERLESS_TO_SERVERLESS_IP_ALLOWED_LIST =
      Stream.concat(
              SERVERLESS_TO_SERVERLESS_TARGET_IPS.stream().map(NetUtils::toIpv4CidrNotation),
              Arrays.stream(NetUtils.PRIVATE_RANGES))
          .collect(Collectors.toList());

  private static final ObjectId DEFAULT_POLICY_ID = oid(123);

  private static final ObjectId DEFAULT_POLICY_ITEM_ID = oid(234);

  private AppUser _user;
  private AppUser _adminUser;
  private AppUser _serverlessUser;

  private AuditInfo _auditInfo;

  private AppSettings _appSettings;
  private GroupDao _groupDao;
  private NDSGroupDao _ndsGroupDao;
  private NDSGroupSvc _ndsGroupSvc;
  private BackupRestoreJobDao _restoreJobDao;
  private TenantBackupSnapshotDao _tenantBackupSnapshotDao;
  private TenantRestoreDao _tenantRestoreDao;
  private CpsBackupCursorFileListsDao _cpsBackupCursorFileListsDao;
  private AppSettings _settings;
  private CpsExportSvc _cpsExportSvc;
  private AuditSvc _auditSvc;
  private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;
  private MTMClusterDao _mtmClusterDao;
  private ServerlessMTMClusterDao _serverlessMTMClusterDao;
  private ClusterDescriptionDao _clusterDescriptionDao;
  private BackupJobDao _backupJobDao;
  private AWSApiSvc _awsApiSvc;
  private ReplicaSetHardwareDao _replicaSetHardwareDao;
  private NDSServerlessMockedMetricsDao _serverlessMockedMetricsDao;
  private BackupSnapshotDao _backupSnapshotDao;
  private Group _serverlessGroup;
  private DataProtectionSettingsDao _dataProtectionSettingsDao;
  private RollingReplacementJobDao _rollingReplacementJobDao;
  private AWSInstanceHardwareDao _instanceHardwareDao;
  private FlexTenantMigrationDao _flexTenantMigrationDao;
  private NDSComputeClusterMetricsSvc _computeClusterMetricsSvc;
  private CpsCollectionMetadataBackupSvc _cpsCollectionMetadataBackupSvc;

  @BeforeClass
  public static void prepareEnv() {
    BaseResourceTestCommon.prepareEnv();
    CloudProviderRegistryUtil.registerAllProvider();
  }

  @AfterClass
  public static void deregisterProviders() {
    CloudProviderRegistryUtil.deregisterAllProvider();
  }

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/host/HostClusterDao/hostClusters.json.ftl",
        null,
        HostCluster.DB_NAME,
        HostCluster.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/host/HostDao/hosts.json.ftl", null, Host.DB_NAME, Host.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, NDSGroupDao.DB, NDSGroupDao.COLLECTION);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/user/UserAllowListDao/userAllowList.json.ftl",
        null,
        UserAllowList.DB_NAME,
        UserAllowList.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "brs/dao/RestoreJobDao/restoreJobs.json.ftl", null, "backupjobs", "restorejobs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupSnapshotDao/backupSnapshots.json.ftl",
        null,
        "nds",
        "config.nds.backup.snapshots");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupRestoreJobDao/backupRestoreJobs.json.ftl",
        null,
        "nds",
        "config.nds.backup.restorejobs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupJobDao/jobs.json.ftl", null, "nds", "config.nds.backup.jobs");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSSystemClusterJobDao/collectionRestoreJobs.json.ftl",
        null,
        "nds",
        "config.nds.backup.systemClusterJobs");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSSystemClusterJobDao/collectionRestoreStates.json.ftl",
        null,
        "nds",
        "config.nds.backup.collectionRestoreStates");

    final Map<String, Object> configDataParams =
        Map.of(
            "groupId",
            oid(148),
            "rsId",
            "Azure5-shard-0",
            "provisionedHostname0",
            "provisionedHostname0",
            "provisionedHostname1",
            "provisionedHostname1",
            "provisionedHostname2",
            "provisionedHostname2");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/atm/AutomationConfigDao/config-basic-rs.json.ftl",
        configDataParams,
        AutomationConfig.DB_NAME,
        AutomationConfig.COLLECTION_NAME);

    _appSettings = AppConfig.getAppSettings();
    _groupDao = AppConfig.getInstance(GroupDao.class);

    final Group group = _groupDao.findById(GROUP_ID);

    _user = MmsFactory.createUser(group);
    _adminUser =
        MmsFactory.createUser(group, "<EMAIL>", Set.of(Role.GLOBAL_ATLAS_ADMIN));
    _serverlessGroup = _groupDao.findById(SERVERLESS_GROUP_ID);
    _serverlessUser = MmsFactory.createUser(_serverlessGroup, "<EMAIL>");

    _auditInfo = MmsFactory.createAuditInfoFromUiCall(_user, false, "localhost");

    _ndsGroupDao = AppConfig.getInstance(NDSGroupDao.class);
    _ndsGroupSvc = AppConfig.getInstance(NDSGroupSvc.class);
    _ndsGroupSvc.create(GROUP_ID, new NDSManagedX509(), false);
    _restoreJobDao = AppConfig.getInstance(BackupRestoreJobDao.class);
    _tenantBackupSnapshotDao = AppConfig.getInstance(TenantBackupSnapshotDao.class);
    _tenantRestoreDao = AppConfig.getInstance(TenantRestoreDao.class);
    _cpsBackupCursorFileListsDao = AppConfig.getInstance(CpsBackupCursorFileListsDao.class);
    _settings = AppConfig.getInstance(AppSettings.class);
    _cpsExportSvc = AppConfig.getInstance(CpsExportSvc.class);
    _mtmClusterDao = AppConfig.getInstance(MTMClusterDao.class);
    _clusterDescriptionDao = AppConfig.getInstance(ClusterDescriptionDao.class);
    _backupJobDao = AppConfig.getInstance(BackupJobDao.class);
    _replicaSetHardwareDao = AppConfig.getInstance(ReplicaSetHardwareDao.class);
    _serverlessMockedMetricsDao = AppConfig.getInstance(NDSServerlessMockedMetricsDao.class);
    _backupSnapshotDao = AppConfig.getInstance(BackupSnapshotDao.class);
    _serverlessMTMClusterDao = AppConfig.getInstance(ServerlessMTMClusterDao.class);
    _dataProtectionSettingsDao = AppConfig.getInstance(DataProtectionSettingsDao.class);
    _rollingReplacementJobDao = AppConfig.getInstance(RollingReplacementJobDao.class);
    _instanceHardwareDao = AppConfig.getInstance(AWSInstanceHardwareDao.class);
    _flexTenantMigrationDao = AppConfig.getInstance(FlexTenantMigrationDao.class);

    _settings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    _auditSvc = AppConfig.getInstance(AuditSvc.class);

    _ndsCloudProviderAccessSvc = mock(NDSCloudProviderAccessSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsExportSvc.class),
        "ndsCloudProviderAccessSvc",
        _ndsCloudProviderAccessSvc);

    NDSAWSTempCredentials creds = new NDSAWSTempCredentials("key", "secret", "token", new Date());

    doReturn(creds)
        .when(_ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(ObjectId.class), any(ObjectId.class));

    _awsApiSvc = mock(AWSApiSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsExportSvc.class), "awsApiSvc", _awsApiSvc);
    doReturn(Optional.of(AWSRegionName.US_EAST_1))
        .when(_awsApiSvc)
        .getS3BucketRegion(any(AWSCredentials.class), anyString(), anyBoolean(), any(Logger.class));
    _computeClusterMetricsSvc = mock(NDSComputeClusterMetricsSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsCollectionRestoreJobSvc.class),
        "ndsComputeClusterMetricsSvc",
        _computeClusterMetricsSvc);
    _cpsCollectionMetadataBackupSvc = mock(CpsCollectionMetadataBackupSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsCollectionRestoreJobSvc.class),
        "cpsCollectionMetadataBackupSvc",
        _cpsCollectionMetadataBackupSvc);
    final NDSCloudProviderAccess access = mock(NDSCloudProviderAccess.class);
    doReturn(Optional.of(mock(NDSCloudProviderAccessAWSIAMRole.class)))
        .when(access)
        .getNDSCloudProviderAccessAWSIAMRoleById(any());
    doReturn(Optional.of(mock(NDSCloudProviderAccessAzureServicePrincipal.class)))
        .when(access)
        .getNDSCloudProviderAccessAzureServicePrincipalById(any());

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(access).when(ndsGroup).getCloudProviderAccess();

    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    when(ndsGroupSvc.find(any())).thenReturn(Optional.of(ndsGroup));

    final NDSCloudProviderAccessSvc ndsCloudProviderAccessSvc =
        mock(NDSCloudProviderAccessSvc.class);
    doNothing()
        .when(ndsCloudProviderAccessSvc)
        .removeFeatureUsageFromAwsIamRoleInCloudProviderAccess(any(), any(), any(), any());
    doNothing()
        .when(ndsCloudProviderAccessSvc)
        .addFeatureUsageToAwsIamRoleInCloudProviderAccess(any(), any(), any(), any());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsExportSvc.class), "ndsGroupSvc", ndsGroupSvc);
  }

  private void setupWtcData(final ObjectId snapshotId) {
    CpsBackupCursorFileList.Builder builder =
        new CpsBackupCursorFileList.Builder()
            .withSnapshotId(snapshotId)
            .withBatchIndex(1)
            .withCursorId(440L)
            .withFiles(
                WTC_FILE_LIST_1.stream()
                    .map(
                        file ->
                            new CpsBackupCursorFileList.CursorFile(
                                file.filename(), file.namespace(), file.filesize()))
                    .collect(Collectors.toList()));
    _cpsBackupCursorFileListsDao.addNewFileList(builder.build());

    builder =
        new CpsBackupCursorFileList.Builder()
            .withSnapshotId(snapshotId)
            .withBatchIndex(2)
            .withCursorId(440L)
            .withFiles(
                WTC_FILE_LIST_2.stream()
                    .map(
                        file ->
                            new CpsBackupCursorFileList.CursorFile(
                                file.filename(), file.namespace(), file.filesize()))
                    .collect(Collectors.toList()));
    _cpsBackupCursorFileListsDao.addNewFileList(builder.build());

    // empty file to mark end
    builder =
        new CpsBackupCursorFileList.Builder()
            .withSnapshotId(snapshotId)
            .withBatchIndex(3)
            .withCursorId(440L);
    _cpsBackupCursorFileListsDao.addNewFileList(builder.build());
  }

  private static String genStr(final char character, final int times) {
    final StringBuilder sb = new StringBuilder();
    for (int i = 0; i < times; i++) {
      sb.append(character);
    }
    return sb.toString();
  }

  @Test
  public void getAzureSnapshot() {
    final JSONObject response = doAuthedJsonGet(_user, getSnapshotUrl(), HttpStatus.SC_OK);
    assertNotNull(response);
    assertEquals(BACKUP_SNAPSHOT_ID_1.toHexString(), response.get("id"));
    assertEquals(CLUSTER_NAME, response.get("clusterName"));
    assertFalse(response.getBoolean("deleted"));
  }

  @Test
  public void testGetBaseSnapshotForPIT() {
    final String url =
        URL_PREFIX
            + GROUP_ID
            + "/"
            + CLUSTER_UNIQUE_ID.toHexString()
            + "/baseSnapshotForPit?pitUTCSeconds=1800000000&pitOplogTs=10&pitOplogInc=10";
    final JSONObject response = doAuthedJsonGet(_user, url, HttpStatus.SC_OK);
    assertNotNull(response);
    assertEquals(BACKUP_SNAPSHOT_ID_1.toHexString(), response.get("id"));
  }

  @Test
  public void deleteAzureSnapshot() {
    final JSONObject response = doAuthedJsonDelete(_user, getSnapshotUrl(), HttpStatus.SC_OK);
    assertNotNull(response);

    final JSONObject response2 = doAuthedJsonGet(_user, getSnapshotUrl(), HttpStatus.SC_OK);
    assertNotNull(response2);
    assertEquals(BACKUP_SNAPSHOT_ID_1.toHexString(), response2.get("id"));
    assertEquals(CLUSTER_NAME, response2.get("clusterName"));
    assertTrue(response2.getBoolean("deleted"));
  }

  @Test
  public void downloadAzureRestoreJobReplSet() {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("deliveryMethod", "download"),
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()));

    final JSONObject response =
        doAuthedJsonPostForm(_user, getRestorePostUrl(GROUP_ID), formValues, HttpStatus.SC_OK);
    assertNotNull(response);

    final JSONArray restoreJobIds = response.getJSONArray("restoreJobIds");
    assertNotNull(restoreJobIds);

    final String restoreId = restoreJobIds.getString(0);
    final ObjectId restoreJobId = new ObjectId(restoreId);
    findRestoreJob(_user, GROUP_ID, restoreJobId, false);
  }

  @Test
  public void downloadAzureSnapshotInvalidId() {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("deliveryMethod", "download"),
            new BasicNameValuePair("snapshotId", oid(1235).toHexString()));

    doAuthedJsonPostForm(_user, getRestorePostUrl(GROUP_ID), formValues, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void downloadAzureSnapshotInvalidDeliveryMethod() {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("deliveryMethod", "invalidMethod"),
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()));

    doAuthedJsonPostForm(_user, getRestorePostUrl(GROUP_ID), formValues, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCreateTenantRestoreJob_AWSChinaRegionsOnly() {
    // Target is AWS China Regions only group, but not source
    final JSONObject targetAWSChinaRegionsOnly =
        requestRestoreForTenantBackup(
            _user,
            "doesntMatter",
            GROUP_ID,
            "alsoDoesntMatter",
            oid(188),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.BACKUP_RESTORE_TO_AWS_CN_ONLY_GROUP_INVALID.name(),
        targetAWSChinaRegionsOnly.getString("errorCode"));

    // Source is AWS China Regions only group, but not target
    final AppUser awsChinaRegionsOnlyUser =
        MmsFactory.createUser(_groupDao.findById(oid(188)), "<EMAIL>");
    final JSONObject sourceAWSChinaRegionsOnly =
        requestRestoreForTenantBackup(
            awsChinaRegionsOnlyUser,
            "doesntMatter",
            oid(188),
            "alsoDoesntMatter",
            GROUP_ID,
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.BACKUP_RESTORE_TO_AWS_CN_ONLY_GROUP_INVALID.name(),
        sourceAWSChinaRegionsOnly.getString("errorCode"));
  }

  @Test
  public void testCreateRestoreJob_AWSChinaRegionsOnly() {
    // Target is AWS China Regions only group, but not source
    final JSONObject targetAWSChinaRegionsOnly = requestRestoreJob(_user, GROUP_ID, oid(188));
    assertEquals(
        NDSErrorCode.BACKUP_RESTORE_TO_AWS_CN_ONLY_GROUP_INVALID.name(),
        targetAWSChinaRegionsOnly.getString("errorCode"));

    // Source is AWS China Regions only group, but not target
    final AppUser awsChinaRegionsOnlyUser =
        MmsFactory.createUser(_groupDao.findById(oid(188)), "<EMAIL>");
    final JSONObject sourceAWSChinaRegionsOnly =
        requestRestoreJob(awsChinaRegionsOnlyUser, oid(188), GROUP_ID);
    assertEquals(
        NDSErrorCode.BACKUP_RESTORE_TO_AWS_CN_ONLY_GROUP_INVALID.name(),
        sourceAWSChinaRegionsOnly.getString("errorCode"));
  }

  @Test
  public void testCreateRestoreJob_GovCloudRegionsOnly() {
    // Target is GovCloud Regions only group, but not source
    final JSONObject targetGovCloudRegionsOnly = requestRestoreJob(_user, GROUP_ID, oid(189));
    assertEquals(
        NDSErrorCode.BACKUP_RESTORE_TO_GROUP_WITH_DIFFERENT_REGION_RESTRICTIONS_INVALID.name(),
        targetGovCloudRegionsOnly.getString("errorCode"));

    // Source is GovCloud Regions only group, but not target
    final AppUser govCloudRegionsOnlyUser =
        MmsFactory.createUser(_groupDao.findById(oid(189)), "<EMAIL>");
    final JSONObject sourceGovCloudRegionsOnly =
        requestRestoreJob(govCloudRegionsOnlyUser, oid(189), GROUP_ID);
    assertEquals(
        NDSErrorCode.BACKUP_RESTORE_TO_GROUP_WITH_DIFFERENT_REGION_RESTRICTIONS_INVALID.name(),
        sourceGovCloudRegionsOnly.getString("errorCode"));
  }

  @Test
  public void createRestoreJobToServerlessInstanceFails() {
    // Restore from dedicated cluster to serverless cluster is not allowed.\
    final AppUser serverlessInstanceUser =
        MmsFactory.createUser(_groupDao.findById(oid(118)), "<EMAIL>");
    final AppUser shardedClusterUser =
        MmsFactory.createUser(_groupDao.findById(oid(155)), "<EMAIL>");

    // Target cluster is a serverless instance
    requestPointInTimeRestoreForServerlessInstance(shardedClusterUser, oid(155), oid(118));

    // Automation Pull Restore
    requestAutomatedRestoreForServerlessInstance(
        _user, CLUSTER_NAME, GROUP_ID, SERVERLESS_INSTANCE_NAME, oid(118));
    requestAutomatedRestoreForServerlessInstance(
        shardedClusterUser, SHARDED_CLUSTER_NAME, oid(155), SERVERLESS_INSTANCE_NAME, oid(118));
  }

  @Test
  public void testCreateCollectionRestoreJob_pit_without_baseSnapshot() {
    final JSONObject collRestoreData = getCollectionRestoreJobViewAndMock();
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.PIT_UTC_SECONDS, 10);
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.OPLOG_Ts, 10);
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.OPLOG_INC, 10);

    final JSONObject response =
        doAuthedJsonPost(
            _user,
            getCollectionRestorePostUrl(GROUP_ID, CLUSTER_UNIQUE_ID),
            collRestoreData,
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.UNABLE_TO_FIND_SNAPSHOT_PRIOR_TO_PIT_RESTORE_OPTIME.name(),
        response.getString("errorCode"));
  }

  @Test
  public void testCreateCollectionRestoreJob_validPit()
      throws SecurityException,
          NoSuchFieldException,
          IllegalArgumentException,
          IllegalAccessException {
    final JSONObject collRestoreData = getCollectionRestoreJobViewAndMock();
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.PIT_UTC_SECONDS, 1800000000);
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.OPLOG_Ts, 10);
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.OPLOG_INC, 10);

    final JSONObject response =
        doAuthedJsonPost(
            _user,
            getCollectionRestorePostUrl(GROUP_ID, CLUSTER_UNIQUE_ID),
            collRestoreData,
            HttpStatus.SC_OK);
    assertNotNull(response);
  }

  @Test
  public void testCreateCollectionRestoreJob() {
    final JSONObject collRestoreData = getCollectionRestoreJobViewAndMock();

    final JSONObject response =
        doAuthedJsonPost(
            _user,
            getCollectionRestorePostUrl(GROUP_ID, CLUSTER_UNIQUE_ID),
            collRestoreData,
            HttpStatus.SC_OK);
    assertNotNull(response);
  }

  @Test
  public void testCreateCollectionRestoreJobWithConcurrentSnapshotRestore() {
    final JSONObject collRestoreData = getCollectionRestoreJobViewAndMock();

    // Override collection restore request to have the same target cluster as an in progress restore
    collRestoreData.put(
        CollectionRestoreRequest.FieldDefs.TARGET_CLUSTER_UNIQUE_ID,
        CLUSTER_UNIQUE_ID.toHexString());
    collRestoreData.put(
        CollectionRestoreRequest.FieldDefs.TARGET_PROJECT_ID, GROUP_ID.toHexString());
    collRestoreData.put(CollectionRestoreRequest.FieldDefs.TARGET_CLUSTER_NAME, CLUSTER_NAME);

    final JSONObject response =
        doAuthedJsonPost(
            _user,
            getCollectionRestorePostUrl(GROUP_ID, CLUSTER_UNIQUE_ID),
            collRestoreData,
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(NDSErrorCode.CLUSTER_RESTORE_IN_PROGRESS.name(), response.getString("errorCode"));
  }

  @Test
  public void testCreateCollectionRestoreJob_nonexisting_snapshot() {
    final JSONObject collRestoreData = getCollectionRestoreJobViewAndMock();
    collRestoreData.put(CollectionRestoreRequestView.FieldDefs.SNAPSHOT_ID, new ObjectId());

    final JSONObject response =
        doAuthedJsonPost(
            _user,
            getCollectionRestorePostUrl(GROUP_ID, CLUSTER_UNIQUE_ID),
            collRestoreData,
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(NDSErrorCode.SNAPSHOT_NOT_FOUND.name(), response.getString("errorCode"));
  }

  @Test
  public void createTenantRestoreForServerlessInstanceFails() {
    setupTenantBackupCollections();
    final AppUser user = getTenantBackupTestUser();

    requestTenantRestoreForServerlessInstance(user, oid(118), oid(118));
  }

  @Test
  public void createTenantRestore_FlexTenantMigration() {
    final AppUser user = getTenantBackupTestUser();
    final ClusterDescription sourceClusterInfo =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription("sourceCluster", oid(118)));
    _clusterDescriptionDao.save(sourceClusterInfo);
    final ClusterDescription targetClusterInfo =
        sourceClusterInfo.copy().setName("free2").setUniqueId(new ObjectId()).build();
    _clusterDescriptionDao.save(targetClusterInfo);

    final TenantBackupSnapshot.TenantSnapshotInfo tenantSnapshotInfo =
        new TenantBackupSnapshot.TenantSnapshotInfo(
            AWSRegionName.US_EAST_1,
            "atlas-tenant-backups-us-east-1-test",
            "mtm-mtmHolder/1234.tgz");
    final TenantBackupSnapshot tenantBackupSnapshot =
        new TenantBackupSnapshot(
            oid(408),
            sourceClusterInfo.getGroupId(),
            sourceClusterInfo.getUniqueId(),
            sourceClusterInfo.getName(),
            "proxy.foo.mongodb.org",
            TenantBackupTask.State.COMPLETED,
            LAST_TENANT_SNAPSHOT_DATE,
            new Date(222),
            new Date(333),
            null,
            TenantBackupTask.SnapshotType.BACKUP,
            null,
            sourceClusterInfo.getMongoDBVersion().getVersion(),
            CloudProvider.GCP,
            "source.tenant.mongodb.org",
            new Date(888),
            null,
            false,
            null,
            false,
            444L,
            tenantSnapshotInfo,
            new BSONTimestamp(),
            oid(9),
            "mtmClusterName");
    _tenantBackupSnapshotDao.insertReplicaSafe(tenantBackupSnapshot);

    final TenantBackupSnapshot visibleSnapshot =
        _tenantBackupSnapshotDao.findById(oid(408)).orElseThrow();
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("deliveryMethod", TenantRestore.DeliveryType.RESTORE.name()),
            new BasicNameValuePair("snapshotId", visibleSnapshot.getId().toHexString()),
            new BasicNameValuePair("sourceClusterName", sourceClusterInfo.getName()),
            new BasicNameValuePair("targetGroupId", targetClusterInfo.getGroupId().toHexString()),
            new BasicNameValuePair("targetClusterName", targetClusterInfo.getName()));

    // Flex Tenant Migration exists with migration running on target cluster--> returns error
    final FlexTenantMigration flexTenantMigration2 =
        new FlexTenantMigration(
            targetClusterInfo.getName(),
            targetClusterInfo.getGroupId(),
            targetClusterInfo.getUniqueId(),
            new FlexTenantMigration.MTM("mtmName", new ObjectId(), new ObjectId()),
            CloudProvider.FREE,
            FreeInstanceSize.M2,
            MigrationStatus.MIGRATION_RUNNING,
            Date.from(Instant.EPOCH),
            null,
            null,
            null,
            null,
            null,
            null,
            Collections.emptyList(),
            new TenantStartingState(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M2), null));
    _flexTenantMigrationDao.saveMigration(flexTenantMigration2);
    {
      final JSONObject response =
          doAuthedJsonPostForm(
              user,
              String.format("/nds/backup/tenant/%s/restore", sourceClusterInfo.getGroupId()),
              formValues,
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          ApiErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
      assertTrue(
          _tenantRestoreDao
              .findByCluster(
                  sourceClusterInfo.getGroupId(), sourceClusterInfo.getName(), SnapshotType.BACKUP)
              .isEmpty());
    }
  }

  @Test
  public void getRestoreJobChecksGroupOwnership() throws Exception {
    final Group otherGroup = _groupDao.findById(oid(151));
    final AppUser otherUser = MmsFactory.createUser(otherGroup, "<EMAIL>");

    // User from Group 151 making request Group 148 with restore ID from Group 148,
    // expect redirect
    assertNotNull(
        "Expected a redirect URL for request with mismatched Group and User",
        doAuthedGetRedirectUrl(otherUser, getRestoreJobUrl(GROUP_ID, RESTORE_JOB_ID)));

    // User from Group 151 making request Group 151 with restore ID from Group 148,
    // expect 400
    final JSONObject response =
        doAuthedJsonGet(
            otherUser,
            getRestoreJobUrl(otherGroup.getId(), RESTORE_JOB_ID),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        "Expected valid JSON response with errorCode, was: " + response,
        "NOT_FOUND",
        response.optString("errorCode"));
  }

  @Test
  public void getSnapshotChecksGroupOwnership() throws Exception {
    final Group otherGroup = _groupDao.findById(oid(151));
    final AppUser otherUser = MmsFactory.createUser(otherGroup, "<EMAIL>");

    // User from Group 151 requesting restore by ID to Group 151, expect 400.
    final String snapshotUrl =
        String.format("/nds/backup/%s/snapshot/%s", otherGroup.getId(), oid(2));
    final JSONObject response = doAuthedJsonGet(otherUser, snapshotUrl, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        "Expected valid JSON response with errorCode, was: " + response,
        "SNAPSHOT_NOT_FOUND",
        response.optString("errorCode"));
  }

  @Test
  public void cancelAzureRestoreJob() throws Exception {
    findRestoreJob(_user, GROUP_ID, RESTORE_JOB_ID, false);

    final JSONObject response =
        doAuthedJsonDelete(_user, getRestoreJobUrl(RESTORE_JOB_ID), HttpStatus.SC_OK);
    assertNotNull(response);

    findRestoreJob(_user, GROUP_ID, RESTORE_JOB_ID, true);
  }

  @Test
  public void markRestoreJobsAsSeen() {
    final List<NameValuePair> formValues =
        List.of(new BasicNameValuePair("restoreJobIds[]", RESTORE_JOB_ID.toHexString()));

    doAuthedFormPatch(_user, getMarkRestoresAsSeenUrl(), formValues, HttpStatus.SC_OK);
    final TopologyHandler restoreJob = _restoreJobDao.findTopologyAgnosticById(RESTORE_JOB_ID);
    assertFalse(restoreJob.getMetadata().isShowRestoreReady());
  }

  @Test
  public void markRestoreJobsAsSeenNoIds() {
    final List<NameValuePair> formValues = List.of(new BasicNameValuePair("restoreJobIds[]", ""));
    doAuthedFormPatch(_user, getMarkRestoresAsSeenUrl(), formValues, HttpStatus.SC_OK);

    final TopologyHandler restoreJob = _restoreJobDao.findTopologyAgnosticById(RESTORE_JOB_ID);
    assertTrue(restoreJob.getMetadata().isShowRestoreReady());
  }

  @Test
  public void getWtcFileList() {
    setupWtcData(oid(41));
    final ReplicaSetBackupRestoreJob restoreJob = _restoreJobDao.findReplicaSetById(oid(5));
    final String verificationKey = restoreJob.getVerificationKey();
    final String restoreJobId = restoreJob.getId().toHexString();

    final String url = String.format("/nds/backup/files/%s/%s", verificationKey, restoreJobId);
    final JSONObject responseJson = doJsonGet(url, HttpStatus.SC_OK);
    final JSONArray filesJsonArray = responseJson.getJSONArray("files");
    final List<String> parsedFilesFromResponse = new ArrayList<>(filesJsonArray.length());
    filesJsonArray.forEach(file -> parsedFilesFromResponse.add((String) file));
    assertEquals(EXPECTED_COMPLETE_FILE_LIST, parsedFilesFromResponse);
  }

  @Test
  public void getWtcFileListWithEar() {
    setupWtcData(oid(2));
    final ReplicaSetBackupRestoreJob restoreJob = _restoreJobDao.findReplicaSetById(oid(8));
    final String verificationKey = restoreJob.getVerificationKey();
    final String restoreJobId = restoreJob.getId().toHexString();

    final String url = String.format("/nds/backup/files/%s/%s", verificationKey, restoreJobId);
    final JSONObject responseJson = doJsonGet(url, HttpStatus.SC_OK);
    final JSONArray filesJsonArray = responseJson.getJSONArray("files");
    final List<String> parsedFilesFromResponse = new ArrayList<>(filesJsonArray.length());
    filesJsonArray.forEach(file -> parsedFilesFromResponse.add((String) file));
    assertEquals(EXPECTED_COMPLETE_FILE_LIST_EAR, parsedFilesFromResponse);
  }

  @Test
  public void testSetRestoreServerRunning() {
    final ReplicaSetBackupRestoreJob restoreJob = _restoreJobDao.findReplicaSetById(oid(5));
    final String verificationKey = restoreJob.getVerificationKey();
    final String restoreJobId = restoreJob.getId().toHexString();

    final String url =
        String.format("/nds/backup/serverRunning/%s/%s", verificationKey, restoreJobId);
    doGetBytes(url, HttpStatus.SC_OK);
  }

  @Test
  public void testIpAllowedAccessListBadVerificationKey() throws Exception {
    JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessDownloadRestoreWorkload(SERVERLESS_BACKUP_SNAPSHOT_ID));
    final ReplicaSetBackupRestoreJob manualDownloadRestoreJob =
        _restoreJobDao.findReplicaSetById(new ObjectId((String) restoreJob.get("id")));

    final String verificationKey = "badKey";
    final String restoreJobId = manualDownloadRestoreJob.getId().toHexString();
    final String url =
        String.format("/nds/backup/ipAllowedAccessList/%s/%s", verificationKey, restoreJobId);
    doGetBytes(url, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testIpAllowedAccessListManualDownload() {
    final JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessDownloadRestoreWorkload(SERVERLESS_BACKUP_SNAPSHOT_ID));
    final ReplicaSetBackupRestoreJob manualDownloadRestoreJob =
        _restoreJobDao.findReplicaSetById(new ObjectId((String) restoreJob.get("id")));

    final String verificationKey = manualDownloadRestoreJob.getVerificationKey();
    final String restoreJobId = manualDownloadRestoreJob.getId().toHexString();
    final String url =
        String.format("/nds/backup/ipAllowedAccessList/%s/%s", verificationKey, restoreJobId);
    final JSONObject responseJson = doJsonGet(url, HttpStatus.SC_OK);
    final JSONArray allowedIpsJsonArray = responseJson.getJSONArray("ips");
    final List<String> parsedIpsFromResponse = new ArrayList<>(allowedIpsJsonArray.length());
    allowedIpsJsonArray.forEach(ip -> parsedIpsFromResponse.add((String) ip));
    assertEquals(EXPECTED_MANUAL_DOWNLOAD_IP_ALLOWED_LIST, parsedIpsFromResponse);
  }

  @Test
  public void testIpAllowedAccessListServerlessToDedicated() throws Exception {
    final ObjectId targetGroupId = oid(147);
    final String targetClusterName = "gcp1";
    final ClusterDescription mtmClusterDescription =
        createMTMClusterResource(
            SERVERLESS_GROUP_ID, SERVERLESS_MTM_NAME, List.of(SERVERLESS_CLUSTER_UNIQUE_ID));
    final ObjectId snapshotId =
        createMTMSnapshot(
            SERVERLESS_GROUP_ID,
            SERVERLESS_MTM_NAME,
            mtmClusterDescription.getUniqueId(),
            SERVERLESS_CLUSTER_UNIQUE_ID);
    createDummyReplicaSetHardwareWithPublicIps(
        targetClusterName, targetGroupId, SERVERLESS_TO_DEDICATED_TARGET_IPS);
    setupMockedDiskStats(targetGroupId, targetClusterName);
    createAutomationConfig(targetGroupId, targetClusterName, true);
    final JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessAutomatedRestoreWorkload(targetGroupId, targetClusterName, snapshotId));

    final ReplicaSetBackupRestoreJob serverlessToDedicatedRestoreJob =
        _restoreJobDao.findReplicaSetById(new ObjectId((String) restoreJob.get("id")));
    final String verificationKey = serverlessToDedicatedRestoreJob.getVerificationKey();
    final String restoreJobId = serverlessToDedicatedRestoreJob.getId().toHexString();
    final String url =
        String.format("/nds/backup/ipAllowedAccessList/%s/%s", verificationKey, restoreJobId);
    final JSONObject responseJson = doJsonGet(url, HttpStatus.SC_OK);
    final JSONArray allowedIpsJsonArray = responseJson.getJSONArray("ips");
    final List<String> parsedIpsFromResponse = new ArrayList<>(allowedIpsJsonArray.length());
    allowedIpsJsonArray.forEach(ip -> parsedIpsFromResponse.add((String) ip));
    assertEquals(EXPECTED_SERVERLESS_TO_DEDICATED_IP_ALLOWED_LIST, parsedIpsFromResponse);
  }

  @Test
  public void testIpAllowedAccessListServerlessToServerless() throws Exception {
    ClusterDescription mtmClusterDescription =
        createServerlessMTMClusterResourcesAndTenantContainers();
    ObjectId snapshotId =
        createMTMSnapshot(
            SERVERLESS_GROUP_ID,
            SERVERLESS_MTM_NAME,
            mtmClusterDescription.getUniqueId(),
            SERVERLESS_CLUSTER_UNIQUE_ID);

    final BasicDBObject replicaSetHardareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(SERVERLESS_MTM_NAME, SERVERLESS_GROUP_ID, 0);
    setupReplicaSetWithPublicIps(replicaSetHardareId, SERVERLESS_TO_SERVERLESS_TARGET_IPS);
    setupMockedDiskStats(SERVERLESS_GROUP_ID, SERVERLESS_MTM_NAME);
    createAutomationConfig(SERVERLESS_GROUP_ID, SERVERLESS_MTM_NAME, false);
    final JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessAutomatedRestoreWorkload(
                SERVERLESS_GROUP_ID, SERVERLESS_INSTANCE_NAME, snapshotId));
    assertEquals(StrategyName.SERVERLESS_STREAMING.name(), restoreJob.get("strategy"));
    final ReplicaSetBackupRestoreJob serverlessToServerlessRestoreJob =
        _restoreJobDao.findReplicaSetById(new ObjectId((String) restoreJob.get("id")));

    final String verificationKey = serverlessToServerlessRestoreJob.getVerificationKey();
    final String restoreJobId = serverlessToServerlessRestoreJob.getId().toHexString();
    final String url =
        String.format("/nds/backup/ipAllowedAccessList/%s/%s", verificationKey, restoreJobId);
    final JSONObject responseJson = doJsonGet(url, HttpStatus.SC_OK);
    final JSONArray allowedIpsJsonArray = responseJson.getJSONArray("ips");
    final List<String> parsedIpsFromResponse = new ArrayList<>(allowedIpsJsonArray.length());
    allowedIpsJsonArray.forEach(ip -> parsedIpsFromResponse.add((String) ip));
    assertEquals(EXPECTED_SERVERLESS_TO_SERVERLESS_IP_ALLOWED_LIST, parsedIpsFromResponse);
  }

  private void createDummyReplicaSetHardwareWithPublicIps(
      final String clusterName, final ObjectId groupId, final List<List<String>> publicIpLists) {
    final BasicDBObject replicaSetHardware0Id =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 0);
    final Iterator<ReplicaSetHardwareIds> replicaSetNames =
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
            groupId, clusterName, clusterName, List.of());
    _replicaSetHardwareDao.create(
        replicaSetHardware0Id, replicaSetNames.next().rsId(), true, false, new ObjectId());
    setupReplicaSetWithPublicIps(replicaSetHardware0Id, publicIpLists.get(0));
    final BasicDBObject replicaSetHardware1Id =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, groupId, 1);
    _replicaSetHardwareDao.create(
        replicaSetHardware1Id, replicaSetNames.next().rsId(), true, false, new ObjectId());
    setupReplicaSetWithPublicIps(replicaSetHardware1Id, publicIpLists.get(1));
  }

  private void setupReplicaSetWithPublicIps(
      final BasicDBObject pReplicationSpecId, List<String> ips) {
    List<ObjectId> hardwareInstanceIds = new ArrayList<>();
    for (int i = 0; i < ips.size(); i++) {
      final ObjectId hardwareInstanceId =
          _replicaSetHardwareDao.addInstance(pReplicationSpecId, CloudProvider.AWS, false, i);
      hardwareInstanceIds.add(hardwareInstanceId);
    }

    for (int i = 0; i < ips.size(); i++) {
      _replicaSetHardwareDao.setInstanceField(
          pReplicationSpecId, hardwareInstanceIds.get(i), false, FieldDefs.PROVISIONED, true);
      _replicaSetHardwareDao.setInstanceField(
          pReplicationSpecId, hardwareInstanceIds.get(i), false, FieldDefs.PUBLIC_IP, ips.get(i));
    }
  }

  @Test
  public void getSnapshots_replicaSet() {
    final Group group = _groupDao.findById(oid(151));
    final AppUser user = MmsFactory.createUser(group);

    final JSONArray response =
        doAuthedJsonArrayGet(
            user, "/nds/backup/" + oid(151) + "/" + oid(1111) + "/snapshots/", HttpStatus.SC_OK);
    assertNotNull(response);
    assertEquals(1, response.length());
    final JSONObject replsetSnapshot = (JSONObject) response.get(0);
    assertNotNull(replsetSnapshot.get("encryptionAtRestProvider"));
    assertEquals(JSONObject.NULL, replsetSnapshot.get("encryptionAtRestId"));
    assertEquals("NONE", replsetSnapshot.get("encryptionAtRestProvider"));
    final JSONArray replsetCopyRegionArray = replsetSnapshot.getJSONArray("copyRegions");
    final List<String> replsetCopyRegions =
        replsetCopyRegionArray.toList().stream()
            .map(obj -> (String) obj)
            .collect(Collectors.toList());
    assertEquals(List.of("eastus2", "northcentralus"), replsetCopyRegions);
    assertFalse(replsetSnapshot.getBoolean("protected"));
  }

  @Test
  public void getSnapshots_sharded() {
    final Group group = _groupDao.findById(oid(151));
    final AppUser user = MmsFactory.createUser(group);

    final JSONArray response =
        doAuthedJsonArrayGet(
            user, "/nds/backup/" + oid(151) + "/" + oid(1112) + "/snapshots/", HttpStatus.SC_OK);
    assertNotNull(response);
    assertEquals(1, response.length());
    final JSONObject shardedSnapshot = (JSONObject) response.get(0);
    final JSONArray members = shardedSnapshot.getJSONArray("members");
    final Map<String, String> rsIdToZoneName = new HashMap<>();
    final Map<String, List<String>> rsIdToCopyRegions = new HashMap<>();
    members.forEach(
        (member) -> {
          final JSONObject memberObj = (JSONObject) member;
          rsIdToZoneName.put(memberObj.getString("rsId"), memberObj.getString("zoneName"));
          final JSONArray copyRegionArray = memberObj.getJSONArray("copyRegions");
          final List<String> copyRegions =
              copyRegionArray.toList().stream()
                  .map(obj -> (String) obj)
                  .collect(Collectors.toList());
          rsIdToCopyRegions.put(memberObj.getString("rsId"), copyRegions);
        });
    assertTrue(shardedSnapshot.getBoolean("protected"));
    assertEquals(
        Map.of("rs1-config-0", "zone 0", "rs1-shard-0", "zone 0", "rs1-shard-1", "zone 1"),
        rsIdToZoneName);
    assertEquals(
        Map.of(
            "rs1-config-0",
            List.of("us-east-2"),
            "rs1-shard-0",
            List.of("us-east-2"),
            "rs1-shard-1",
            List.of("us-west-1")),
        rsIdToCopyRegions);
  }

  @Test
  public void getSnapshotWithEncryption() throws Exception {
    final JSONObject cluster =
        doAuthedJsonGet(_user, URL_PREFIX + GROUP_ID + "/snapshot/" + oid(2), HttpStatus.SC_OK);
    assertNotNull(cluster.get("encryptionAtRestProvider"));
    assertNotNull(cluster.get("encryptionAtRestId"));
    assertEquals("AWS", cluster.get("encryptionAtRestProvider"));
    assertEquals("masterKey", cluster.get("encryptionAtRestId"));
  }

  @Test
  public void getSingleShardedClusterRestoreJob() {
    final Group group = _groupDao.findById(oid(151));
    final AppUser user = MmsFactory.createUser(group);
    final JSONObject response =
        doAuthedJsonGet(
            user, "/nds/backup/" + oid(151) + "/restoreJob/" + oid(4), HttpStatus.SC_OK);

    assertNotNull(response);
    assertTrue(response.has("rsIdToDeliveryUrl"));

    JSONArray rsIdToDeliveryUrls = (JSONArray) response.get("rsIdToDeliveryUrl");
    rsIdToDeliveryUrls.forEach(
        item -> {
          assertTrue(((JSONObject) item).has("rsId"));
          assertTrue(((JSONObject) item).has("deliveryUrl"));
        });
  }

  @Test
  public void testGetTenantBackupSnapshots() {
    setupTenantBackupCollections();
    final AppUser appUser = getTenantBackupTestUser();

    final JSONArray tenantSnapshots =
        doAuthedJsonArrayGet(
            appUser,
            String.format("/nds/backup/tenant/%s/%s/snapshots", oid(118), oid(9)),
            HttpStatus.SC_OK);
    assertEquals(1, tenantSnapshots.length());
    final JSONObject tenantSnapshotJSON = tenantSnapshots.getJSONObject(0);
    assertEquals(oid(408).toString(), tenantSnapshotJSON.getString("id"));
    assertEquals("free1", tenantSnapshotJSON.get("clusterName"));
    assertEquals(TimeUtils.toISOString(new Date(222)), tenantSnapshotJSON.get("creationDate"));
    assertEquals(TimeUtils.toISOString(new Date(333)), tenantSnapshotJSON.get("completedDate"));
    assertEquals("4.0.0", tenantSnapshotJSON.get("mongoDbVersion"));
    assertEquals(TimeUtils.toISOString(new Date(888)), tenantSnapshotJSON.get("expirationDate"));
    assertEquals(444L, tenantSnapshotJSON.getLong("usedDiskSpaceInBytes"));
    assertEquals("COMPLETED", tenantSnapshotJSON.getString("status"));
  }

  @Test
  public void testGetTenantRestores() {
    final AppUser appUser = getTenantBackupTestUser();
    setupTenantBackupCollections();

    final JSONObject restoreJSON =
        doAuthedJsonGet(
            appUser,
            String.format("/nds/backup/tenant/%s/restore/%s", oid(118), oid(1)),
            HttpStatus.SC_OK);
    assertNotNull(restoreJSON);
    assertEquals(oid(1).toString(), restoreJSON.getString("id"));
    assertEquals(oid(118).toString(), restoreJSON.getString("projectId"));
    assertEquals("free1", restoreJSON.getString("clusterName"));
    assertEquals(TimeUtils.toISOString(new Date(3)), restoreJSON.get("timestamp"));
    assertEquals(TimeUtils.toISOString(TENANT_RESTORE_DATE), restoreJSON.get("submittedAt"));
    assertEquals(TimeUtils.toISOString(new Date(4)), restoreJSON.get("expirationDate"));
    assertEquals(TimeUtils.toISOString(new Date(2)), restoreJSON.get("finishedDate"));
    assertEquals("RESTORE", restoreJSON.getString("deliveryType"));
    assertEquals(oid(0).toString(), restoreJSON.getString("targetProjectId"));
    assertEquals("target-m2", restoreJSON.getString("targetClusterName"));
    assertEquals(oid(4).toString(), restoreJSON.getString("snapshotId"));
    assertEquals("PENDING", restoreJSON.getString("status"));
  }

  @Test
  public void testGetTenantRestore() {
    final AppUser appUser = getTenantBackupTestUser();
    setupTenantBackupCollections();

    final JSONArray restores =
        doAuthedJsonArrayGet(
            appUser,
            String.format("/nds/backup/tenant/%s/%s/restores", oid(118), oid(9)),
            HttpStatus.SC_OK);
    assertEquals(1, restores.length());
    final JSONObject restoreJSON = restores.getJSONObject(0);
    assertEquals(oid(1).toString(), restoreJSON.getString("id"));
    assertEquals(oid(118).toString(), restoreJSON.getString("projectId"));
    assertEquals("free1", restoreJSON.getString("clusterName"));
    assertEquals(TimeUtils.toISOString(new Date(3)), restoreJSON.get("timestamp"));
    assertEquals(TimeUtils.toISOString(TENANT_RESTORE_DATE), restoreJSON.get("submittedAt"));
    assertEquals(TimeUtils.toISOString(new Date(4)), restoreJSON.get("expirationDate"));
    assertEquals(TimeUtils.toISOString(new Date(2)), restoreJSON.get("finishedDate"));
    assertEquals("RESTORE", restoreJSON.getString("deliveryType"));
    assertEquals(oid(0).toString(), restoreJSON.getString("targetProjectId"));
    assertEquals("target-m2", restoreJSON.getString("targetClusterName"));
    assertEquals(oid(4).toString(), restoreJSON.getString("snapshotId"));
    assertEquals("PENDING", restoreJSON.getString("status"));
  }

  @Test
  public void testGetTenantBackupMetadata() {
    final AppUser appUser = getTenantBackupTestUser();

    final JSONObject metadata0 =
        doAuthedJsonGet(
            appUser,
            String.format("/nds/backup/tenant/%s/%s/backup", oid(118), oid(9)),
            HttpStatus.SC_OK);
    assertEquals(JSONObject.NULL, metadata0.get("lastSnapshotDate"));
    assertEquals(JSONObject.NULL, metadata0.get("nextSnapshotDate"));

    setupTenantBackupCollections();

    final JSONObject metadata1 =
        doAuthedJsonGet(
            appUser,
            String.format("/nds/backup/tenant/%s/%s/backup", oid(118), oid(9)),
            HttpStatus.SC_OK);
    assertEquals(
        LAST_TENANT_SNAPSHOT_DATE,
        Date.from(Instant.parse(metadata1.getString("lastSnapshotDate"))));
    assertEquals(JSONObject.NULL, metadata1.get("nextSnapshotDate"));
  }

  @Test
  public void testGetExportBucket() throws SvcException {
    // getAllExportBucketsByGroup endpoint returns empty list when no export buckets
    // have been added
    final ObjectId id1 = ObjectId.get();
    final ObjectId id2 = ObjectId.get();
    final JSONArray emptyGroupResponse =
        doAuthedJsonArrayGet(_user, getExportBucketUrl(GROUP_ID), HttpStatus.SC_OK);
    assertEquals(emptyGroupResponse.length(), 0);

    // getExportBucketById endpoint returns error if bucket not found
    final JSONObject bucketNotFoundResponse =
        doAuthedJsonGet(_user, getExportBucketUrl(GROUP_ID) + oid(117), HttpStatus.SC_BAD_REQUEST);
    assertEquals(bucketNotFoundResponse.get("errorCode"), "EXPORT_BUCKET_NOT_FOUND");

    // adding export bucket through CpsExportSvc
    final AWSExportBucket exportBucket1 =
        AWSExportBucket.builder()
            .withId(id1)
            .withProjectId(GROUP_ID)
            .withBucketName("bucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(oid(118))
            .build();

    _cpsExportSvc.createExportBucket(exportBucket1, _auditInfo);

    final List<Event> audits1 =
        _auditSvc.findByDate(ExportBucketAudit.Type.BUCKET_CREATED_AUDIT, Date.from(Instant.EPOCH));
    assertEquals(1, audits1.size());

    // getExportBucketById endpoint returns correct export bucket
    final JSONObject bucket1Res =
        doAuthedJsonGet(
            _user, getExportBucketUrl(GROUP_ID) + exportBucket1.getId(), HttpStatus.SC_OK);
    assertEquals(exportBucket1.getBucketName(), bucket1Res.get("bucketName"));
    assertEquals(exportBucket1.getProjectId().toString(), bucket1Res.get("projectId"));
    assertEquals(exportBucket1.getIamRoleId().toString(), bucket1Res.get("iamRoleId"));

    // adding second export bucket through cpsExportSvc
    final AWSExportBucket exportBucket2 =
        AWSExportBucket.builder()
            .withId(id2)
            .withProjectId(GROUP_ID)
            .withBucketName("bucket2")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(oid(119))
            .build();
    _cpsExportSvc.createExportBucket(exportBucket2, _auditInfo);

    final List<Event> audits2 =
        _auditSvc.findByDate(ExportBucketAudit.Type.BUCKET_CREATED_AUDIT, Date.from(Instant.EPOCH));
    assertEquals(2, audits2.size());

    // getAllExportBucketsByGroup endpoint returns list of all export buckets for
    // group
    final JSONArray listResponse =
        doAuthedJsonArrayGet(_user, getExportBucketUrl(GROUP_ID), HttpStatus.SC_OK);
    assertEquals(listResponse.length(), 2);

    JSONObject listResponse1 = listResponse.getJSONObject(0);
    assertEquals(exportBucket1.getBucketName(), listResponse1.get("bucketName"));
    assertEquals(exportBucket1.getProjectId().toString(), listResponse1.get("projectId"));
    assertEquals(exportBucket1.getIamRoleId().toString(), listResponse1.get("iamRoleId"));

    JSONObject listResponse2 = listResponse.getJSONObject(1);
    assertEquals(exportBucket2.getBucketName(), listResponse2.get("bucketName"));
    assertEquals(exportBucket2.getProjectId().toString(), listResponse2.get("projectId"));
    assertEquals(exportBucket2.getIamRoleId().toString(), listResponse2.get("iamRoleId"));
  }

  @Test
  public void testAddDeleteExportBucket() {
    // adding export bucket through endpoint
    final ObjectId iamRoleId = oid(118);
    final String bucketName = "bucket1";

    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("iamRoleId", iamRoleId.toHexString()),
            new BasicNameValuePair("bucketName", bucketName));

    final JSONObject addResponse =
        doAuthedJsonPostForm(_user, getExportBucketUrl(GROUP_ID), formValues, HttpStatus.SC_OK);
    assertNotNull(addResponse);
    ObjectId id = new ObjectId(addResponse.get("_id").toString());

    // can get added export bucket by id through CpsExportSvc
    Optional<ExportBucket> addExportBucketOpt = _cpsExportSvc.findExportBucket(id);
    assertTrue(addExportBucketOpt.isPresent());

    ExportBucket exportBucket = addExportBucketOpt.get();
    assertEquals(bucketName, exportBucket.getBucketName());
    assertEquals(GROUP_ID, exportBucket.getProjectId());

    // deleting export bucket through endpoint
    final JSONObject deleteResponse =
        doAuthedJsonDelete(_user, getExportBucketUrl(GROUP_ID) + id, HttpStatus.SC_OK);
    assertNotNull(deleteResponse);

    // cannot find deleted export bucket anymore through CpsExportSvc
    Optional<ExportBucket> deleteExportBucketOpt = _cpsExportSvc.findExportBucket(id);
    assertFalse(deleteExportBucketOpt.isPresent());

    // deleting an export bucket that is not found returns an error.
    final JSONObject deleteResponse2 =
        doAuthedJsonDelete(_user, getExportBucketUrl(GROUP_ID) + id, HttpStatus.SC_BAD_REQUEST);
    assertEquals(deleteResponse2.get("errorCode"), "EXPORT_BUCKET_NOT_FOUND");

    final List<Event> createAudits =
        _auditSvc.findByDate(ExportBucketAudit.Type.BUCKET_CREATED_AUDIT, Date.from(Instant.EPOCH));
    assertEquals(1, createAudits.size());

    final List<Event> deleteAudits =
        _auditSvc.findByDate(ExportBucketAudit.Type.BUCKET_DELETED_AUDIT, Date.from(Instant.EPOCH));
    assertEquals(1, deleteAudits.size());
  }

  @Test
  public void testAddExportBucket_inCompatibleGovRegion() {
    final ObjectId iamRoleId = oid(118);
    final String bucketName = "bucket1";

    doReturn(Optional.of(AWSRegionName.US_GOV_EAST_1))
        .when(_awsApiSvc)
        .getS3BucketRegion(any(), eq(bucketName), anyBoolean(), any());

    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("iamRoleId", iamRoleId.toHexString()),
            new BasicNameValuePair("bucketName", bucketName));

    HttpUtils.getInstance()
        .post()
        .path("/nds/backup/groups/%s/backup/exportBuckets/", GROUP_ID)
        .data(new UrlEncodedFormEntity(formValues, Consts.UTF_8))
        .uiAuth(_user)
        .expectedErrorCode(
            HttpStatus.SC_BAD_REQUEST, NDSErrorCode.EXPORT_BUCKET_INCOMPATIBLE_REGION)
        .send();
  }

  @Test
  public void testGetDeleteExportBucketWithWrongGroupId() throws SvcException {
    // adding export bucket through CpsExportSvc
    final ObjectId id1 = ObjectId.get();
    final AWSExportBucket exportBucket1 =
        AWSExportBucket.builder()
            .withId(id1)
            .withProjectId(GROUP_ID)
            .withBucketName("bucket1")
            .withBucketType(BucketType.CUSTOMER)
            .withIamRoleId(oid(118))
            .build();
    _cpsExportSvc.createExportBucket(exportBucket1, _auditInfo);

    final Group otherGroup = _groupDao.findById(oid(147));
    final AppUser otherUser = MmsFactory.createUser(otherGroup, "<EMAIL>");

    // getExportBucketById endpoint returns error if group id does not match
    final JSONObject getBucketResponse =
        doAuthedJsonGet(
            otherUser,
            getExportBucketUrl(oid(147)) + exportBucket1.getId(),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(getBucketResponse.get("errorCode"), "EXPORT_BUCKET_NOT_FOUND");

    // deleting export bucket through endpoint returns error if group id does not
    // match
    final JSONObject deleteResponse =
        doAuthedJsonDelete(
            otherUser,
            getExportBucketUrl(oid(147)) + exportBucket1.getId(),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(deleteResponse.get("errorCode"), "EXPORT_BUCKET_NOT_FOUND");

    final List<Event> createAudits =
        _auditSvc.findByDate(ExportBucketAudit.Type.BUCKET_CREATED_AUDIT, Date.from(Instant.EPOCH));
    assertEquals(1, createAudits.size());

    final List<Event> deleteAudits =
        _auditSvc.findByDate(ExportBucketAudit.Type.BUCKET_DELETED_AUDIT, Date.from(Instant.EPOCH));
    assertEquals(0, deleteAudits.size());
  }

  @Test
  public void testGetDataProtectionClusterList() {
    createServerlessMTMClusterResourcesAndTenantContainers();

    setupTenantBackupCollections();

    final AppUser appUser = getTenantBackupTestUser();

    final JSONArray clusters =
        doAuthedJsonArrayGet(
            appUser,
            String.format("/nds/backup/%s/dataProtectionClusterList", oid(118)),
            HttpStatus.SC_OK);

    final Map<String, JSONObject> clusterMap = new HashMap<>();

    for (Object item : clusters) {
      JSONObject cluster = (JSONObject) item;
      clusterMap.put(cluster.getString("clusterName"), cluster);
    }

    {
      final JSONObject rs1Cluster = clusterMap.get("rs1");
      assertFalse(rs1Cluster.getBoolean("backupEnabled"));
      assertFalse(rs1Cluster.getBoolean("pitEnabled"));
      assertEquals("ACTIVE", rs1Cluster.getString("clusterState"));
      assertTrue(rs1Cluster.getJSONArray("snapshotRegions").isEmpty());
      assertTrue(rs1Cluster.isNull("lastSnapshotTime"));
      assertNotNull(rs1Cluster.opt("nextSnapshotTime"));
      assertTrue(rs1Cluster.isNull("oldestSnapshotTime"));
    }

    {
      final JSONObject rs2Cluster = clusterMap.get("rs2");
      assertTrue(rs2Cluster.getBoolean("backupEnabled"));
      assertFalse(rs2Cluster.getBoolean("pitEnabled"));
      assertEquals("ACTIVE", rs2Cluster.getString("clusterState"));
      assertTrue(rs2Cluster.getJSONArray("snapshotRegions").isEmpty());
      assertTrue(rs2Cluster.isNull("lastSnapshotTime"));
      assertNotNull(rs2Cluster.opt("nextSnapshotTime"));
      assertTrue(rs2Cluster.isNull("oldestSnapshotTime"));
    }

    { // deleted cluster without backup retaining
      final JSONObject rs3DeletedBackupDisabled = clusterMap.get("rs3DeletedBackupDisabled");
      assertNull(rs3DeletedBackupDisabled);
    }

    { // deleted cluster with diskBackupState set to "retaining" in config.nds.backup.jobs
      // collection
      final JSONObject rs4DeletedBackupEnabled = clusterMap.get("rs4DeletedBackupEnabled");
      assertNotNull(rs4DeletedBackupEnabled);
      assertTrue(rs4DeletedBackupEnabled.getBoolean("backupEnabled"));
      assertTrue(rs4DeletedBackupEnabled.getBoolean("pitEnabled"));
      assertEquals("DELETED", rs4DeletedBackupEnabled.getString("clusterState"));
      assertNotNull(rs4DeletedBackupEnabled.opt("nextSnapshotTime"));
    }

    { // live cluster with snapshot
      final JSONObject rs5liveDataProtection = clusterMap.get("rs5liveDataProtection");
      assertNotNull(rs5liveDataProtection);
      assertTrue(rs5liveDataProtection.getBoolean("backupEnabled"));
      assertTrue(rs5liveDataProtection.getBoolean("pitEnabled"));
      assertEquals("ACTIVE", rs5liveDataProtection.getString("clusterState"));
      assertFalse(rs5liveDataProtection.getJSONArray("snapshotRegions").isEmpty());
      assertEquals(
          "AWS us-east-2", rs5liveDataProtection.getJSONArray("snapshotRegions").getString(0));

      ZonedDateTime now = ZonedDateTime.now().truncatedTo(ChronoUnit.DAYS);
      ZonedDateTime now_minus_30 = now.minusDays(30);
      ZonedDateTime now_minus_60 = now.minusDays(60);
      ZonedDateTime now_plus_2 = now.plusDays(2);
      String expectedOldestSnapshotTime = now_minus_60.format(DateTimeFormatter.ISO_INSTANT);
      String expectedLastSnapshotTime = now_minus_30.format(DateTimeFormatter.ISO_INSTANT);
      String expectedNextSnapshotTime = now_plus_2.format(DateTimeFormatter.ISO_INSTANT);

      assertEquals(expectedLastSnapshotTime, rs5liveDataProtection.getString("lastSnapshotTime"));
      assertEquals(
          expectedOldestSnapshotTime, rs5liveDataProtection.getString("oldestSnapshotTime"));
      assertEquals(expectedNextSnapshotTime, rs5liveDataProtection.getString("nextSnapshotTime"));
    }

    { // delete requested cluster with dataProtection
      final JSONObject rs6DeletedRequestedDataProtection =
          clusterMap.get("rs6DeletedRequestedDataProtection");
      assertNotNull(rs6DeletedRequestedDataProtection);
      assertTrue(rs6DeletedRequestedDataProtection.isNull("lastSnapshotTime"));
      assertTrue(rs6DeletedRequestedDataProtection.getBoolean("backupEnabled"));
      assertTrue(rs6DeletedRequestedDataProtection.getBoolean("pitEnabled"));
      assertEquals("DELETED", rs6DeletedRequestedDataProtection.getString("clusterState"));
    }

    { // includes serverless & shared-tier clusters
      assertTrue(clusterMap.containsKey("serverless1"));
      assertTrue(clusterMap.containsKey("serverless3"));
      assertTrue(clusterMap.containsKey("free1")); // this is actually an M5
    }
  }

  @Test
  public void testGetCollectionRestoreJob() {
    final String uri = URL_PREFIX + GROUP_ID + "/collectionRestoreJob/" + COLLECTION_RESTORE_JOB_ID;

    JSONObject response = doAuthedJsonGet(_user, uri, HttpStatus.SC_OK);

    assertNotNull(response);
    assertEquals(COLLECTION_RESTORE_JOB_ID.toHexString(), response.getString("_id"));
  }

  private JSONObject findRestoreJob(
      AppUser user, final ObjectId groupId, final ObjectId restoreJobId, final boolean isCanceled)
      throws JSONException {
    final JSONObject response =
        doAuthedJsonGet(user, getRestoreJobUrl(groupId, restoreJobId), HttpStatus.SC_OK);
    assertNotNull(response);
    assertEquals(restoreJobId.toHexString(), response.getString("id"));
    assertEquals(isCanceled, response.getBoolean("canceled"));
    return response;
  }

  private String getSnapshotUrl() {
    return URL_PREFIX + GROUP_ID + "/snapshot/" + BACKUP_SNAPSHOT_ID_1;
  }

  private String getRestoreJobUrl(final ObjectId restoreJobId) {
    return getRestoreJobUrl(GROUP_ID, restoreJobId);
  }

  private String getRestoreJobUrl(final ObjectId pGroupId, final ObjectId restoreJobId) {
    if (restoreJobId != null) {
      return String.format("/nds/backup/%s/restoreJob/%s", pGroupId, restoreJobId);
    } else {
      return String.format("/nds/backup/%s/restoreJob", pGroupId);
    }
  }

  private String getRestorePostUrl(ObjectId groupId) {
    return URL_PREFIX + groupId + "/restoreJob/";
  }

  private String getCollectionRestorePostUrl(ObjectId groupId, ObjectId clusterId) {
    return URL_PREFIX + groupId + "/" + clusterId + "/collectionRestoreJob";
  }

  private String getMarkRestoresAsSeenUrl() {
    return URL_PREFIX + GROUP_ID + "/restoreJob/seen";
  }

  private String getExportBucketUrl(ObjectId groupId) {
    return "/nds/backup/groups/" + groupId + "/backup/exportBuckets/";
  }

  private void setupTenantBackupCollections() {
    final TenantBackupSnapshot.TenantSnapshotInfo tenantSnapshotInfo =
        new TenantBackupSnapshot.TenantSnapshotInfo(
            AWSRegionName.US_EAST_1,
            "atlas-tenant-backups-us-east-1-test",
            "mtm-mtmHolder/1234.tgz");
    final TenantBackupSnapshot tenantBackupSnapshot =
        new TenantBackupSnapshot(
            oid(408),
            oid(118),
            oid(9),
            "free1",
            "proxy.foo.mongodb.org",
            TenantBackupTask.State.COMPLETED,
            LAST_TENANT_SNAPSHOT_DATE,
            new Date(222),
            new Date(333),
            null,
            TenantBackupTask.SnapshotType.BACKUP,
            null,
            "4.0.0",
            CloudProvider.GCP,
            "source.tenant.mongodb.org",
            new Date(888),
            null,
            false,
            null,
            false,
            444L,
            tenantSnapshotInfo,
            new BSONTimestamp(),
            oid(9),
            "mtmClusterName");
    _tenantBackupSnapshotDao.insertReplicaSafe(tenantBackupSnapshot);

    final TenantRestore.RequestingUser requestingUser =
        new TenantRestore.RequestingUser("username", "<EMAIL>");
    final TenantRestore.RestoreDelivery restoreDelivery =
        new TenantRestore.RestoreDelivery(TenantRestore.DeliveryType.RESTORE, oid(0), "target-m2");
    final TenantRestore tenantRestore =
        new TenantRestore(
            oid(1),
            oid(118),
            oid(9),
            "free1",
            "handling.proxy.host",
            TenantBackupTask.State.PENDING,
            TENANT_RESTORE_DATE,
            new Date(1),
            new Date(2),
            null,
            TenantBackupTask.SnapshotType.BACKUP,
            null,
            oid(4),
            "snapshot.url",
            new BSONTimestamp(333, 3),
            new Date(3),
            new Date(4),
            "target-m2-00-00.mms.com",
            requestingUser,
            restoreDelivery,
            oid(10),
            "mtm-us-east-1-0");
    _tenantRestoreDao.insertReplicaSafe(tenantRestore);
  }

  private AppUser getTenantBackupTestUser() {
    return MmsFactory.createUser(_groupDao.findById(oid(118)));
  }

  private JSONObject requestRestoreJob(
      final AppUser pUser, final ObjectId pSourceGroupId, final ObjectId pTargetGroupId) {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair(
                "deliveryMethod", UserRequestType.POINT_IN_TIME.getRequestName()),
            new BasicNameValuePair("pointInTimeUTCSeconds", "1"),
            new BasicNameValuePair("oplogTs", "1"),
            new BasicNameValuePair("oplogInc", "1"),
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()),
            new BasicNameValuePair("sourceClusterName", "doesntMatter"),
            new BasicNameValuePair("targetGroupId", pTargetGroupId.toHexString()),
            new BasicNameValuePair("targetClusterName", "alsoDoesntMatter"));

    return doAuthedJsonPostForm(
        pUser, getRestoreJobUrl(pSourceGroupId, null), formValues, HttpStatus.SC_BAD_REQUEST);
  }

  private JSONObject requestRestoreForTenantBackup(
      final AppUser pUser,
      final String pSourceClusterName,
      final ObjectId pSourceGroupId,
      final String pTargetClusterName,
      final ObjectId pTargetGroupId,
      final int pExpectedCode) {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("deliveryMethod", TenantRestore.DeliveryType.RESTORE.name()),
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()),
            new BasicNameValuePair("sourceClusterName", pSourceClusterName),
            new BasicNameValuePair("targetGroupId", pTargetGroupId.toHexString()),
            new BasicNameValuePair("targetClusterName", pTargetClusterName));

    return doAuthedJsonPostForm(
        pUser,
        String.format("/nds/backup/tenant/%s/restore", pSourceGroupId),
        formValues,
        pExpectedCode);
  }

  private void requestPointInTimeRestoreForServerlessInstance(
      final AppUser pUser, final ObjectId pSourceGroupId, final ObjectId pTargetGroupId) {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair(
                "deliveryMethod",
                CpsRestoreMetadata.UserRequestType.POINT_IN_TIME.getRequestName()),
            new BasicNameValuePair("pointInTimeUTCSeconds", "1"),
            new BasicNameValuePair("oplogTs", "1"),
            new BasicNameValuePair("oplogInc", "1"),
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()),
            new BasicNameValuePair("sourceClusterName", CpsResourceIntTests.SHARDED_CLUSTER_NAME),
            new BasicNameValuePair("targetGroupId", pTargetGroupId.toHexString()),
            new BasicNameValuePair(
                "targetClusterName", CpsResourceIntTests.SERVERLESS_INSTANCE_NAME));

    createRestoreJobForServerlessInstance(
        pUser, String.format("/nds/backup/%s/restoreJob", pSourceGroupId), formValues);
  }

  private void requestAutomatedRestoreForServerlessInstance(
      final AppUser pUser,
      final String pSourceClusterName,
      final ObjectId pSourceGroupId,
      final String pTargetClusterName,
      final ObjectId pTargetGroupId) {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair(
                "deliveryMethod",
                CpsRestoreMetadata.UserRequestType.AUTOMATION_PULL.getRequestName()),
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()),
            new BasicNameValuePair("sourceClusterName", pSourceClusterName),
            new BasicNameValuePair("targetGroupId", pTargetGroupId.toHexString()),
            new BasicNameValuePair("targetClusterName", pTargetClusterName));

    createRestoreJobForServerlessInstance(
        pUser, String.format("/nds/backup/%s/restoreJob", pSourceGroupId), formValues);
  }

  private void requestTenantRestoreForServerlessInstance(
      final AppUser pUser, final ObjectId pSourceGroupId, final ObjectId pTargetGroupId) {
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("snapshotId", BACKUP_SNAPSHOT_ID_1.toHexString()),
            new BasicNameValuePair("deliveryMethod", TenantRestore.DeliveryType.RESTORE.name()),
            new BasicNameValuePair("sourceClusterName", "free1"),
            new BasicNameValuePair("targetGroupId", pTargetGroupId.toHexString()),
            new BasicNameValuePair(
                "targetClusterName", CpsResourceIntTests.SERVERLESS_INSTANCE_NAME));

    createRestoreJobForServerlessInstance(
        pUser, String.format("/nds/backup/tenant/%s/restore", pSourceGroupId), formValues);
  }

  private void createRestoreJobForServerlessInstance(
      final AppUser pUser, final String pPath, final List<NameValuePair> pFormValues) {
    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject createRestoreJobs1 =
        doAuthedJsonPostForm(pUser, pPath, pFormValues, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        createRestoreJobs1.get(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _settings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject createRestoreJobs2 =
        doAuthedJsonPostForm(pUser, pPath, pFormValues, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        createRestoreJobs2.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void createServerlessDownloadRestoreJob() {
    JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessDownloadRestoreWorkload(SERVERLESS_BACKUP_SNAPSHOT_ID));
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.getString("projectId"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.getString("clusterName"));
  }

  @Test
  public void createServerlessDownloadRestoreJob_FlexTenantMigration() {
    final String targetClusterName = "serverless2";
    final ObjectId targetGroupId = oid(130);
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("deliveryMethod", "download"),
            new BasicNameValuePair("snapshotId", SERVERLESS_BACKUP_SNAPSHOT_ID.toHexString()),
            new BasicNameValuePair("sourceClusterName", SERVERLESS_INSTANCE_NAME),
            new BasicNameValuePair("targetClusterName", targetClusterName),
            new BasicNameValuePair("targetGroupId", targetGroupId.toHexString()));

    // Flex Tenant Migration exists with migration running on source cluster--> returns error
    final FlexTenantMigration flexTenantMigration =
        new FlexTenantMigration(
            SERVERLESS_INSTANCE_NAME,
            SERVERLESS_GROUP_ID,
            new ObjectId(),
            new FlexTenantMigration.MTM("mtmName", new ObjectId(), new ObjectId()),
            CloudProvider.SERVERLESS,
            ServerlessInstanceSize.SERVERLESS_V2,
            MigrationStatus.MIGRATION_RUNNING,
            Date.from(Instant.EPOCH),
            null,
            null,
            null,
            null,
            null,
            null,
            Collections.emptyList(),
            new TenantStartingState(
                new ServerlessTenantProviderOptions(
                    new BasicDBObject(), ServerlessInstanceSize.SERVERLESS_V2),
                new ServerlessBackupOptions(false, Optional.empty())));
    _flexTenantMigrationDao.saveMigration(flexTenantMigration);
    {
      final JSONObject response =
          doAuthedJsonPostForm(
              _serverlessUser,
              "/nds/backup/serverless/" + SERVERLESS_GROUP_ID + "/restore/",
              formValues,
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          ApiErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }
    _flexTenantMigrationDao.updateMigration(
        flexTenantMigration.toBuilder().setStatus(MigrationStatus.MIGRATION_SUCCESS).build());

    // Flex Tenant Migration exists with rollback running on target cluster--> returns error
    final FlexTenantMigration flexTenantMigration2 =
        new FlexTenantMigration(
            targetClusterName,
            targetGroupId,
            new ObjectId(),
            new FlexTenantMigration.MTM("mtmName", new ObjectId(), new ObjectId()),
            CloudProvider.SERVERLESS,
            ServerlessInstanceSize.SERVERLESS_V2,
            MigrationStatus.ROLLBACK_RUNNING,
            Date.from(Instant.EPOCH),
            null,
            null,
            null,
            null,
            null,
            null,
            Collections.emptyList(),
            new TenantStartingState(
                new ServerlessTenantProviderOptions(
                    new BasicDBObject(), ServerlessInstanceSize.SERVERLESS_V2),
                new ServerlessBackupOptions(false, Optional.empty())));
    _flexTenantMigrationDao.saveMigration(flexTenantMigration2);

    {
      final JSONObject response =
          doAuthedJsonPostForm(
              _serverlessUser,
              "/nds/backup/serverless/" + SERVERLESS_GROUP_ID + "/restore/",
              formValues,
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          ApiErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }
    _flexTenantMigrationDao.updateMigration(
        flexTenantMigration2.toBuilder().setStatus(MigrationStatus.ROLLBACK_SUCCESS).build());

    // all migrations have completed, restore successful
    {
      doAuthedJsonPostForm(
          _serverlessUser,
          "/nds/backup/serverless/" + SERVERLESS_GROUP_ID + "/restore/",
          formValues,
          HttpStatus.SC_OK);
    }
  }

  @Test
  public void testCreateAutomatedServerlessRestore_ServerlessToServerlessInTheSameGroupStreaming()
      throws Exception {
    ClusterDescription mtmClusterDescription =
        createServerlessMTMClusterResourcesAndTenantContainers();
    ObjectId snapshotId =
        createMTMSnapshot(
            SERVERLESS_GROUP_ID,
            SERVERLESS_MTM_NAME,
            mtmClusterDescription.getUniqueId(),
            SERVERLESS_CLUSTER_UNIQUE_ID);
    setupMockedDiskStats(SERVERLESS_GROUP_ID, SERVERLESS_MTM_NAME);
    createAutomationConfig(SERVERLESS_GROUP_ID, SERVERLESS_MTM_NAME, false);

    final JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessAutomatedRestoreWorkload(
                SERVERLESS_GROUP_ID, SERVERLESS_INSTANCE_NAME, snapshotId));
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.get("projectId"));
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.get("targetProjectId"));
    assertEquals(
        CpsRestoreMetadata.UserRequestType.AUTOMATION_PULL.toString(),
        restoreJob.getString("deliveryType"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.get("targetClusterName"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.get("clusterName"));
    assertEquals(StrategyName.SERVERLESS_STREAMING.name(), restoreJob.get("strategy"));
  }

  @Test
  public void testCreateAutomatedServerlessRestore_ServerlessToDedicated() throws Exception {
    final ObjectId targetGroupId = oid(147);
    final String targetClusterName = "gcp1";

    ClusterDescription mtmClusterDescription =
        createMTMClusterResource(
            SERVERLESS_GROUP_ID, SERVERLESS_MTM_NAME, List.of(SERVERLESS_CLUSTER_UNIQUE_ID));
    ObjectId snapshotId =
        createMTMSnapshot(
            SERVERLESS_GROUP_ID,
            SERVERLESS_MTM_NAME,
            mtmClusterDescription.getUniqueId(),
            SERVERLESS_CLUSTER_UNIQUE_ID);
    createDummyReplicaSetHardware(targetClusterName, targetGroupId, null);
    setupMockedDiskStats(targetGroupId, targetClusterName);
    createAutomationConfig(targetGroupId, targetClusterName, true);

    final JSONObject restoreJob =
        createServerlessRestoreJob(
            getServerlessAutomatedRestoreWorkload(targetGroupId, targetClusterName, snapshotId));
    assertEquals(StrategyName.SERVERLESS_DEDICATED.name(), restoreJob.get("strategy"));
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.get("projectId"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.get("clusterName"));
    assertEquals(targetGroupId.toString(), restoreJob.get("targetProjectId"));
    assertEquals(UserRequestType.AUTOMATION_PULL.toString(), restoreJob.getString("deliveryType"));
    assertEquals(targetClusterName, restoreJob.get("targetClusterName"));
    assertEquals(StrategyName.SERVERLESS_DEDICATED.name(), restoreJob.get("strategy"));
  }

  @Test
  public void getServerlessBackupJob() {
    final Date loadCheckDate = Date.from(Instant.now().minus(Duration.ofMinutes(30)));
    final ServerlessMTMCluster serverlessMTMCluster =
        new ServerlessMTMCluster(
            SERVERLESS_GROUP_ID,
            SERVERLESS_MTM_NAME,
            true,
            1000,
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            ServerlessInstanceSize.SERVERLESS_V2,
            "4.4",
            null,
            loadCheckDate,
            new Date(loadCheckDate.getTime() - Duration.ofMinutes(7).toMillis()),
            new Date(loadCheckDate.getTime() - Duration.ofMinutes(7).toMillis()),
            null,
            null,
            SERVERLESS_MTM_CLUSTER_DESCRIPTION_UNIQUE_ID);
    _mtmClusterDao.insert(serverlessMTMCluster);
    _mtmClusterDao.decrementCapacityForCluster(
        SERVERLESS_GROUP_ID,
        SERVERLESS_MTM_NAME,
        MTMClusterType.SERVERLESS,
        SERVERLESS_CLUSTER_CLOUD_CONTAINER_ID);
    _serverlessMTMClusterDao.incrementTenantTargetProxyVersion(
        SERVERLESS_GROUP_ID,
        SERVERLESS_MTM_NAME,
        MTMClusterType.SERVERLESS,
        SERVERLESS_CLUSTER_UNIQUE_ID,
        SERVERLESS_CLUSTER_CLOUD_CONTAINER_ID);
    final ClusterDescription serverlessMTMClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                SERVERLESS_GROUP_ID,
                SERVERLESS_MTM_NAME,
                List.of(
                    NDSModelTestFactory.getShardRegionConfigForRegion(
                        AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 1))));
    _clusterDescriptionDao.save(serverlessMTMClusterDescription);
    _backupJobDao.create(
        SERVERLESS_GROUP_ID,
        SERVERLESS_MTM_NAME,
        serverlessMTMClusterDescription.getUniqueId(),
        ClusterType.REPLICA_SET,
        null,
        5,
        List.of(
            new PolicyItem(
                new ObjectId(),
                BackupFrequencyType.MONTHLY,
                CpsPolicyUtils.FREQUENCY_INTERVAL_LAST_DAY_OF_MONTH,
                Duration.ofDays(31 * 12),
                BackupRetentionUnit.MONTHS)),
        false,
        new Date());

    String endpoint =
        String.format("/nds/backup/serverless/%s/%s/job", SERVERLESS_GROUP_ID, oid(1));
    JSONObject getError = doAuthedJsonGet(_serverlessUser, endpoint, HttpStatus.SC_BAD_REQUEST);
    assertEquals("NOT_SERVERLESS_TENANT_CLUSTER", getError.getString("errorCode"));
  }

  @Test
  public void editRetentionWithInvalidRetentionUnitFails() {
    _user =
        MmsFactory.createUser(
            _groupDao.findById(GROUP_ID),
            "<EMAIL>",
            Set.of(Role.GLOBAL_ATLAS_ADMIN));
    final JSONObject snapshotResponse = doAuthedJsonGet(_user, getSnapshotUrl(), HttpStatus.SC_OK);
    final String snapshotId = snapshotResponse.get("id").toString();
    final List<NameValuePair> formValues =
        List.of(
            new BasicNameValuePair("snapshotId", snapshotId),
            new BasicNameValuePair("retentionValue", "1"),
            new BasicNameValuePair("retentionUnit", "hours"));
    final JSONObject response =
        doAuthedFormPatch(
            _user,
            "/nds/backup/" + GROUP_ID + "/snapshot/" + snapshotId + "/editRetention",
            formValues,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals(
        NDSErrorCode.BACKUP_RETENTION_UNIT_INVALID.name(), response.getString("errorCode"));
  }

  @Test
  public void getServerlessBackupSnapshots() {
    final Date loadCheckDate = Date.from(Instant.now().minus(Duration.ofMinutes(30)));
    final ServerlessMTMCluster serverlessMTMCluster =
        new ServerlessMTMCluster(
            SERVERLESS_GROUP_ID,
            SERVERLESS_MTM_NAME,
            true,
            1000,
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            ServerlessInstanceSize.SERVERLESS_V2,
            "5.0",
            oid(1000),
            loadCheckDate,
            new Date(loadCheckDate.getTime() - Duration.ofMinutes(7).toMillis()),
            new Date(loadCheckDate.getTime() - Duration.ofMinutes(7).toMillis()),
            null,
            null,
            SERVERLESS_MTM_CLUSTER_DESCRIPTION_UNIQUE_ID);
    _mtmClusterDao.insert(serverlessMTMCluster);
    _mtmClusterDao.decrementCapacityForCluster(
        SERVERLESS_GROUP_ID,
        SERVERLESS_MTM_NAME,
        MTMClusterType.SERVERLESS,
        SERVERLESS_CLUSTER_CLOUD_CONTAINER_ID);
    _serverlessMTMClusterDao.incrementTenantTargetProxyVersion(
        SERVERLESS_GROUP_ID,
        SERVERLESS_MTM_NAME,
        MTMClusterType.SERVERLESS,
        SERVERLESS_CLUSTER_UNIQUE_ID,
        SERVERLESS_CLUSTER_CLOUD_CONTAINER_ID);

    String endpoint =
        String.format(
            "/nds/backup/serverless/%s/%s/snapshots", SERVERLESS_GROUP_ID, new ObjectId());

    JSONObject getError = doAuthedJsonGet(_serverlessUser, endpoint, HttpStatus.SC_BAD_REQUEST);
    assertEquals("CLUSTER_NOT_FOUND", getError.getString("errorCode"));

    final ObjectId snapshot1Id = oid(15);
    final ObjectId snapshot2Id = oid(16);

    endpoint =
        String.format(
            "/nds/backup/serverless/%s/%s/snapshots",
            SERVERLESS_GROUP_ID, SERVERLESS_CLUSTER_UNIQUE_ID);

    JSONArray getResult = doAuthedJsonArrayGet(_serverlessUser, endpoint);
    assertNotNull(getResult);
    assertEquals(2, getResult.length());

    final JSONObject cluster1 = (JSONObject) getResult.get(0);
    final JSONObject cluster2 = (JSONObject) getResult.get(1);

    assertEquals(snapshot1Id, oid(Integer.parseInt((String) cluster1.get("id"))));
    assertEquals(snapshot2Id, oid(Integer.parseInt((String) cluster2.get("id"))));

    assertEquals("3.4", cluster1.getString("mongoDbFcv"));
    assertEquals("3.4", cluster2.getString("mongoDbFcv"));

    assertEquals(-1L, cluster1.getLong("usedDiskSpaceInBytes"));
  }

  @Test
  public void getServerlessRestoreJobs() {
    createServerlessRestoreJob(getServerlessDownloadRestoreWorkload(SERVERLESS_BACKUP_SNAPSHOT_ID));
    String endpoint =
        String.format(
            "/nds/backup/serverless/%s/%s/restores",
            SERVERLESS_GROUP_ID, SERVERLESS_CLUSTER_UNIQUE_ID);

    JSONArray getResult = doAuthedJsonArrayGet(_serverlessUser, endpoint);
    assertNotNull(getResult);
    assertEquals(1, getResult.length());
    final JSONObject restoreJob = (JSONObject) getResult.get(0);
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.getString("projectId"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.getString("clusterName"));
  }

  @Test
  public void testCreateRollingReplacementJobs() {
    final ObjectId groupId = GROUP_ID;
    final String clusterName = "cluster0";
    final ClusterDescription cd =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                groupId,
                clusterName,
                List.of(
                    NDSModelTestFactory.getShardRegionConfigForRegion(
                        AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 1))));
    _clusterDescriptionDao.save(cd);

    final ClusterDescription cd1 = _clusterDescriptionDao.findByName(groupId, clusterName).get();

    createDummyReplicaSetHardware(clusterName, groupId, null);

    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findByCluster(groupId, clusterName).get(0);

    final InstanceHardware instanceHardware =
        replicaSetHardware.getAllHardware().findFirst().orElseThrow();

    _instanceHardwareDao.setInstanceFields(
        replicaSetHardware.getId(),
        instanceHardware.getInstanceId(),
        false,
        Arrays.asList(
            Pair.of(FieldDefs.PROVISIONED, true),
            Pair.of(AWSInstanceHardware.FieldDefs.EBS_VOLUME_ENCRYPTION_KEY, null)));

    final String url =
        String.format("/nds/backup/%s/%s/rollingNodeReplacement", groupId, clusterName);
    doAuthedJsonPost(_user, url, new JSONObject(), HttpStatus.SC_OK);
    final ObjectId instanceId = instanceHardware.getInstanceId();

    final List<RollingReplacementJob> jobs =
        _rollingReplacementJobDao.findByProjectIdAndClusterUniqueId(
            cd1.getGroupId(), cd1.getUniqueId());

    assertEquals(1, jobs.size());
    final RollingReplacementJob job = jobs.get(0);
    assertEquals(cd.getName(), job.getClusterName());
    assertEquals(cd.getUniqueId(), job.getClusterUniqueId());
    assertEquals(cd.getGroupId(), job.getProjectId());
    assertEquals(_user.getPrimaryEmail(), job.getUserEmail());
    assertEquals(RollingReplacementJob.Status.READY, job.getStatus());

    assertEquals(1, job.getNodesTobeReplaced().size());

    assertEquals(
        NodeToBeReplaced.aBuilder()
            .failedCount(0)
            .status(NodeToBeReplaced.Status.READY)
            .instanceId(instanceId)
            .build(),
        job.getNodesTobeReplaced().get(0));
  }

  @Test
  public void getServerlessRestoreJob() {
    JSONObject createResult =
        createServerlessRestoreJob(
            getServerlessDownloadRestoreWorkload(SERVERLESS_BACKUP_SNAPSHOT_ID));
    String endpoint =
        String.format(
            "/nds/backup/serverless/%s/restore/%s",
            SERVERLESS_GROUP_ID, createResult.getString("id"));
    JSONObject restoreJob = doAuthedJsonGet(_serverlessUser, endpoint);
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.getString("projectId"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.getString("clusterName"));
  }

  @Test
  public void cancelServerlessRestoreJob() {
    JSONObject createResult =
        createServerlessRestoreJob(
            getServerlessDownloadRestoreWorkload(SERVERLESS_BACKUP_SNAPSHOT_ID));
    String endpoint =
        String.format(
            "/nds/backup/serverless/%s/restore/%s",
            SERVERLESS_GROUP_ID, createResult.getString("id"));
    doAuthedJsonDelete(_serverlessUser, endpoint);
    JSONObject restoreJob =
        findRestoreJob(
            _serverlessUser, SERVERLESS_GROUP_ID, new ObjectId(createResult.getString("id")), true);
    assertEquals(SERVERLESS_GROUP_ID.toString(), restoreJob.getString("projectId"));
    assertEquals(SERVERLESS_INSTANCE_NAME, restoreJob.getString("clusterName"));
  }

  @Test
  public void getDataProtectionSettings_empty() throws IOException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = oid(97);
    final AppUser user = MmsFactory.createUser(_groupDao.findById(projectId));
    final String url = String.format("/nds/backup/%s/dataProtection", projectId);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);

    assertTrue(responseJson.isEmpty());
  }

  @Test
  public void getDataProtectionSettings_nonEmpty() throws IOException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = GROUP_ID;
    final AppUser user = MmsFactory.createUser(_groupDao.findById(projectId));
    final String url = String.format("/nds/backup/%s/dataProtection", projectId);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);

    assertFalse(responseJson.isEmpty());
    assertEquals(projectId.toString(), responseJson.getString("projectId"));
    assertEquals("ACTIVE", responseJson.getString("state"));
    assertTrue(responseJson.getBoolean("pitEnabled"));
    assertTrue(responseJson.getBoolean("encryptionAtRestEnabled"));
    assertTrue(responseJson.getBoolean("copyProtectionEnabled"));
    assertEquals(5, responseJson.getInt("restoreWindowDays"));

    final JSONObject onDemandPolicyItem = responseJson.getJSONObject("onDemandPolicyItem");
    final JSONArray scheduledPolicyItems = responseJson.getJSONArray("scheduledPolicyItems");

    assertEquals(0, onDemandPolicyItem.getInt("frequencyInterval"));
    assertEquals(3, onDemandPolicyItem.getInt("retention"));
    assertEquals("DAYS", onDemandPolicyItem.getString("retentionUnit"));
    assertEquals("ON_DEMAND", onDemandPolicyItem.getString("frequencyType"));
    assertTrue(onDemandPolicyItem.isNull("id"));

    assertEquals(2, scheduledPolicyItems.length());

    assertEquals(12, scheduledPolicyItems.getJSONObject(0).getInt("frequencyInterval"));
    assertEquals(7, scheduledPolicyItems.getJSONObject(0).getInt("retention"));
    assertEquals("DAYS", scheduledPolicyItems.getJSONObject(0).getString("retentionUnit"));
    assertEquals("HOURLY", scheduledPolicyItems.getJSONObject(0).getString("frequencyType"));
    assertEquals(oid(201).toString(), scheduledPolicyItems.getJSONObject(0).getString("id"));

    assertEquals(1, scheduledPolicyItems.getJSONObject(1).getInt("frequencyInterval"));
    assertEquals(7, scheduledPolicyItems.getJSONObject(1).getInt("retention"));
    assertEquals("DAYS", scheduledPolicyItems.getJSONObject(1).getString("retentionUnit"));
    assertEquals("DAILY", scheduledPolicyItems.getJSONObject(1).getString("frequencyType"));
    assertTrue(scheduledPolicyItems.getJSONObject(1).isNull("id"));
  }

  @Test
  public void getDataProtectionSettings_null_on_demand() throws IOException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = oid(192);
    final AppUser user = MmsFactory.createUser(_groupDao.findById(projectId));
    final String url = String.format("/nds/backup/%s/dataProtection", projectId);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);

    assertFalse(responseJson.isEmpty());
    assertEquals(projectId.toString(), responseJson.getString("projectId"));

    assertTrue(responseJson.isNull("onDemandPolicyItem"));
  }

  @Test
  public void getDataProtectionSettings_missing_on_demand() throws IOException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = oid(517);
    final AppUser user = MmsFactory.createUser(_groupDao.findById(projectId));
    final String url = String.format("/nds/backup/%s/dataProtection", projectId);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);

    assertFalse(responseJson.isEmpty());
    assertEquals(projectId.toString(), responseJson.getString("projectId"));

    assertTrue(responseJson.isNull("onDemandPolicyItem"));
  }

  @Test
  public void saveDataProtectionSettings() {
    final Group group = _groupDao.findById(GROUP_ID);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.CPS_BACKUP_COMPLIANCE_POLICY_POST_GA);

    try {
      // case #1: successful creation
      final JSONObject onDemandPolicy = getPolicyItem(oid(101), "ON_DEMAND", 0, 14);
      final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 14);
      final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 14);

      final JSONArray scheduledPolicies = new JSONArray();
      scheduledPolicies.put(scheduledPolicy1);
      scheduledPolicies.put(scheduledPolicy2);
      final String authorizedEmail = "<EMAIL>";
      final JSONObject dpSettings = new JSONObject();
      dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
      dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
      dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
      dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
      dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
      dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
      dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
      dpSettings.put(DataProtectionSettingsView.ON_DEMAND_POLICY_ITEM, onDemandPolicy);
      dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, authorizedEmail);
      dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_FIRST_NAME, "foo");
      dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_LAST_NAME, "bar");

      final String url = String.format("/nds/backup/%s/dataProtection", GROUP_ID);
      doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_OK);

      // case #2: unsuccessful update due to pending state
      dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 7);
      final JSONObject responseUpdate =
          doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CANNOT_UPDATE_BACKUP_COMPLIANCE_POLICY_SETTINGS_WITH_PENDING_ACTION.name(),
          responseUpdate.getString("errorCode"));

      // case #3: restore Window should be the same as originally created
      final JSONObject responseGet = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
      assertEquals(10, responseGet.getInt("restoreWindowDays"));

      // case #4: authorizedEmail should be updated
      assertEquals(authorizedEmail, responseGet.getString("authorizedEmail"));
      assertEquals("foo", responseGet.getString("authorizedUserFirstName"));
      assertEquals("bar", responseGet.getString("authorizedUserLastName"));

      // case #4: authorized last name updated to be null
      dpSettings.remove(DataProtectionSettingsView.AUTHORIZED_USER_LAST_NAME);
      final JSONObject responseUpdate2 =
          doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CANNOT_UPDATE_BACKUP_COMPLIANCE_POLICY_SETTINGS_WITH_PENDING_ACTION.name(),
          responseUpdate2.getString("errorCode"));
    } finally {
      FeatureFlagIntTestUtil.disableFeatureForEntity(
          group, null, FeatureFlag.CPS_BACKUP_COMPLIANCE_POLICY_POST_GA);
    }
  }

  @Test
  public void saveDataProtectionSettings_null_on_demand() {
    final Group group = _groupDao.findById(GROUP_ID);

    final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 14);
    final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 14);

    final JSONArray scheduledPolicies = new JSONArray();
    scheduledPolicies.put(scheduledPolicy1);
    scheduledPolicies.put(scheduledPolicy2);

    final JSONObject dpSettings = new JSONObject();
    dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
    dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
    dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
    dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
    dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
    dpSettings.put(DataProtectionSettingsView.ON_DEMAND_POLICY_ITEM, (JSONObject) null);
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, "<EMAIL>");
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_FIRST_NAME, "bac");
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_LAST_NAME, "user");

    final String url = String.format("/nds/backup/%s/dataProtection", GROUP_ID);
    doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_OK);

    final Optional<DataProtectionSettings> settings =
        _dataProtectionSettingsDao.findByProjectId(GROUP_ID);
    assertTrue(settings.isPresent());
    assertFalse(settings.get().hasOnDemandPolicyItem());
  }

  @Test
  public void saveDataProtectionSettings_missing_on_demand() {
    final Group group = _groupDao.findById(GROUP_ID);

    final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 14);
    final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 14);

    final JSONArray scheduledPolicies = new JSONArray();
    scheduledPolicies.put(scheduledPolicy1);
    scheduledPolicies.put(scheduledPolicy2);

    final JSONObject dpSettings = new JSONObject();
    dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
    dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
    dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
    dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
    dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, "<EMAIL>");
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_FIRST_NAME, "bac");
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_LAST_NAME, "user");

    final String url = String.format("/nds/backup/%s/dataProtection", GROUP_ID);
    doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_OK);

    final Optional<DataProtectionSettings> settings =
        _dataProtectionSettingsDao.findByProjectId(GROUP_ID);
    assertTrue(settings.isPresent());
    assertFalse(settings.get().hasOnDemandPolicyItem());
  }

  @Test
  public void saveDataProtectionSettings_missing_authorized_email() {
    final Group group = _groupDao.findById(GROUP_ID);

    final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 14);
    final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 14);

    final JSONArray scheduledPolicies = new JSONArray();
    scheduledPolicies.put(scheduledPolicy1);
    scheduledPolicies.put(scheduledPolicy2);

    final JSONObject dpSettings = new JSONObject();
    dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
    dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
    dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
    dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
    dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_FIRST_NAME, "bac");
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_USER_LAST_NAME, "user");

    final String url = String.format("/nds/backup/%s/dataProtection", GROUP_ID);
    final JSONObject response =
        doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.BACKUP_COMPLIANCE_POLICY_SETTINGS_INVALID.name(),
        response.getString("errorCode"));

    // after adding authorized email, the post action should pass
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, "<EMAIL>");
    doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_OK);
  }

  @Test
  public void saveDataProtectionSettings_validation_error() {
    final Group group = _groupDao.findById(GROUP_ID);

    final JSONObject onDemandPolicy = getPolicyItem(oid(101), "ON_DEMAND", 0, 3);
    final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 7);
    final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 7);

    final JSONArray scheduledPolicies = new JSONArray();
    scheduledPolicies.put(scheduledPolicy1);
    scheduledPolicies.put(scheduledPolicy2);

    final JSONObject dpSettings = new JSONObject();
    dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
    dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
    dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
    dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
    dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
    dpSettings.put(DataProtectionSettingsView.ON_DEMAND_POLICY_ITEM, onDemandPolicy);
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, "<EMAIL>");

    final String url = String.format("/nds/backup/%s/dataProtection", GROUP_ID);
    final JSONObject response =
        doAuthedJsonPatch(_adminUser, url, dpSettings, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.BACKUP_COMPLIANCE_POLICY_SETTINGS_INVALID.name(),
        response.getString("errorCode"));
  }

  @Test
  public void disableDataProtectionSettingsAuthorizedGlobalAtlasAdmin() throws IOException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = GROUP_ID;
    final Group group = _groupDao.findById(projectId);

    AppUser authorizedUser = MmsFactory.createGlobalAtlasAdminUser(group);

    final String url = String.format("/nds/backup/%s/dataProtection", projectId);

    final JSONObject responseJson = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
    assertEquals("ACTIVE", responseJson.getString("state"));

    final String approveDisablementUrl =
        String.format("/nds/backup/%s/dataProtection:markForDisablement", projectId);
    final List<NameValuePair> formValues =
        List.of(new BasicNameValuePair("markForDisablement", "true"));

    doAuthedJsonPostForm(authorizedUser, approveDisablementUrl, formValues, HttpStatus.SC_OK);

    doAuthedJsonDelete(authorizedUser, url, HttpStatus.SC_OK);

    final JSONObject responseJsonAfterDelete = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
    assertEquals("DISABLING", responseJsonAfterDelete.getString("state"));
  }

  @Test
  public void disableDataProtectionSettingsAuthorizedGlobalBCPAdmin() throws IOException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = GROUP_ID;
    final Group group = _groupDao.findById(projectId);

    AppUser authorizedUser = MmsFactory.createGlobalBackupCompliancePolicyAdminUser(group);

    final String url = String.format("/nds/backup/%s/dataProtection", projectId);

    final JSONObject responseJson = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
    assertEquals("ACTIVE", responseJson.getString("state"));

    final String approveDisablementUrl =
        String.format("/nds/backup/%s/dataProtection:markForDisablement", projectId);
    final List<NameValuePair> formValues =
        List.of(new BasicNameValuePair("markForDisablement", "true"));

    doAuthedJsonPostForm(authorizedUser, approveDisablementUrl, formValues, HttpStatus.SC_OK);

    doAuthedJsonDelete(authorizedUser, url, HttpStatus.SC_OK);

    final JSONObject responseJsonAfterDelete = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
    assertEquals("DISABLING", responseJsonAfterDelete.getString("state"));
  }

  @Test
  public void disableDataProtectionSettingsUnauthorized() throws IOException, SvcException {
    populateBackupCompliancePolicySettings();

    final ObjectId projectId = GROUP_ID;
    final Group group = _groupDao.findById(projectId);

    AppUser unAuthorizedUser =
        MmsFactory.createUserWithRoleInGroup(group, "<EMAIL>", Role.GROUP_CLUSTER_MANAGER);

    final String url = String.format("/nds/backup/%s/dataProtection", projectId);

    final JSONObject responseJson = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
    assertEquals("ACTIVE", responseJson.getString("state"));

    doAuthedJsonDelete(unAuthorizedUser, url, Response.Status.FORBIDDEN.getStatusCode());

    final JSONObject responseJsonAfterDelete = doAuthedJsonGet(_adminUser, url, HttpStatus.SC_OK);
    assertEquals("ACTIVE", responseJsonAfterDelete.getString("state"));
  }

  @Test
  public void validateDataProtectionSettings_noError() throws IOException {
    populateBackupCompliancePolicySettings();

    final Group group = _groupDao.findById(GROUP_ID);

    // case #1: successful creation
    final JSONObject onDemandPolicy = getPolicyItem(oid(101), "ON_DEMAND", 0, 14);
    final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 14);
    final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 14);

    final JSONArray scheduledPolicies = new JSONArray();
    scheduledPolicies.put(scheduledPolicy1);
    scheduledPolicies.put(scheduledPolicy2);

    final JSONObject dpSettings = new JSONObject();
    dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
    dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
    dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
    dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
    dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
    dpSettings.put(DataProtectionSettingsView.ON_DEMAND_POLICY_ITEM, onDemandPolicy);
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, "<EMAIL>");

    final String url = String.format("/nds/backup/%s/dataProtection/validate", GROUP_ID);
    doAuthedJsonPost(_adminUser, url, dpSettings, HttpStatus.SC_OK);
  }

  @Test
  public void validateDataProtectionSettings_withError() throws IOException {
    populateBackupCompliancePolicySettings();

    final Group group = _groupDao.findById(GROUP_ID);

    final JSONObject onDemandPolicy = getPolicyItem(oid(101), "ON_DEMAND", 0, 3);
    final JSONObject scheduledPolicy1 = getPolicyItem(oid(201), "HOURLY", 12, 7);
    final JSONObject scheduledPolicy2 = getPolicyItem(oid(202), "DAILY", 1, 7);

    final JSONArray scheduledPolicies = new JSONArray();
    scheduledPolicies.put(scheduledPolicy1);
    scheduledPolicies.put(scheduledPolicy2);

    final JSONObject dpSettings = new JSONObject();
    dpSettings.put(DataProtectionSettingsView.PROJECT_ID, GROUP_ID);
    dpSettings.put(DataProtectionSettingsView.STATE, State.ENABLING);
    dpSettings.put(DataProtectionSettingsView.RESTORE_WINDOW_DAYS, 10);
    dpSettings.put(DataProtectionSettingsView.PIT_ENABLED, true);
    dpSettings.put(DataProtectionSettingsView.ENCRYPTION_AT_REST_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.COPY_PROTECTION_ENABLED, false);
    dpSettings.put(DataProtectionSettingsView.SCHEDULED_POLICY_ITEMS, scheduledPolicies);
    dpSettings.put(DataProtectionSettingsView.ON_DEMAND_POLICY_ITEM, onDemandPolicy);
    dpSettings.put(DataProtectionSettingsView.AUTHORIZED_EMAIL, "<EMAIL>");

    final String url = String.format("/nds/backup/%s/dataProtection/validate", GROUP_ID);
    final JSONObject response =
        doAuthedJsonPost(_adminUser, url, dpSettings, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.BACKUP_COMPLIANCE_POLICY_SETTINGS_INVALID.name(),
        response.getString("errorCode"));
  }

  @Test
  public void testUpdateBackupPolicy_withFailedDataProtectionValidation() throws IOException {
    populateBackupCompliancePolicySettings();

    final JSONArray policies = getPolicyWithNewFrequency();
    final JSONObject request = new JSONObject();

    request.put("policies", policies);
    request.put("restoreWindowDays", 2);

    final ObjectId clusterUniqueId = oid(17);

    final Group group = _groupDao.findById(GROUP_ID);

    // the Backup Compliance Policy check should fail
    final JSONObject response2 =
        doAuthedJsonPost(
            _user,
            "/nds/backup/" + GROUP_ID + "/" + clusterUniqueId + "/backupPolicy",
            request,
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS.name(),
        response2.getString("errorCode"));
  }

  @Test
  public void testGetOptInForAwsCrossProjectRestore() {
    // Given
    final ObjectId groupId = GROUP_ID;
    final String clusterName = "cluster0";
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());
    final AWSAccountDao awsAccountDao = AppConfig.getInstance(AWSAccountDao.class);
    awsAccountDao.save(
        AWSAccount.builder()
            .setId(container.getAWSAccountId())
            .setAssumeRoleARN("arn:aws:iam::" + container.getAWSAccountId() + ":role/rootArn")
            .build());
    final BasicDBObject awsClusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(
            groupId,
            clusterName,
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 1)));
    awsClusterDescription.append("diskBackupEnabled", true);
    final ClusterDescription cd = new ClusterDescription(awsClusterDescription);
    _clusterDescriptionDao.save(cd);
    final ObjectId containerId = _ndsGroupDao.addCloudContainer(groupId, container);
    _appSettings.setProp(
        "nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate",
        "dummy new %s %s",
        AppSettings.SettingType.MEMORY);
    final ClusterDescription cd1 = _clusterDescriptionDao.findByName(groupId, clusterName).get();
    final String path =
        String.format(
            "/nds/backup/%s/%s/canOptInForAwsCrossProjectRestore", GROUP_ID, cd1.getUniqueId());
    final Group group = _groupDao.findById(GROUP_ID);
    createDummyReplicaSetHardware(
        clusterName, groupId, container.getCloudProviderAccountId(), containerId);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.CPS_RESTORE_CROSS_PROJECT_AWS_NEW_CMK);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.CPS_RESTORE_CROSS_PROJECT_AWS_MIGRATION);

    try {
      // When all the conditions are satisfied(Happy Path)
      final JSONObject response = doAuthedJsonGet(_user, path, HttpStatus.SC_OK);
      // Then should return true
      assertTrue(response.getBoolean("canOptIn"));

      // When CMK is equal to the new CMK
      _appSettings.setProp(
          "nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate",
          "dummy old %s %s",
          AppSettings.SettingType.MEMORY);
      final JSONObject response2 = doAuthedJsonGet(_user, path, HttpStatus.SC_OK);

      // Then should return false
      assertFalse(response2.getBoolean("canOptIn"));
    } finally {
      FeatureFlagIntTestUtil.disableFeatureForEntity(
          group, null, FeatureFlag.CPS_RESTORE_CROSS_PROJECT_AWS_NEW_CMK);
      FeatureFlagIntTestUtil.disableFeatureForEntity(
          group, null, FeatureFlag.CPS_RESTORE_CROSS_PROJECT_AWS_MIGRATION);
    }

    // When feature flag got disabled
    final JSONObject response3 = doAuthedJsonGet(_user, path, HttpStatus.SC_OK);

    // Then should return false
    assertFalse(response3.getBoolean("canOptIn"));
  }

  private ObjectId createMTMSnapshot(
      ObjectId serverlessGroupId,
      String serverlessMtmName,
      ObjectId serverlessMtmUniqueId,
      ObjectId serverlessClusterUniqueId) {
    ObjectId snapshotId = ObjectId.get();
    BasicDBObject basicDBObject = NDSModelTestFactory.getDefaultAwsBackupSnapshot();
    basicDBObject.put(BackupSnapshot.FieldDefs.ID, snapshotId);
    basicDBObject.put(BackupSnapshot.FieldDefs.PROJECT_ID, serverlessGroupId);
    basicDBObject.put(BackupSnapshot.FieldDefs.CLUSTER_NAME, serverlessMtmName);
    basicDBObject.put(BackupSnapshot.FieldDefs.CLUSTER_UNIQUE_ID, serverlessMtmUniqueId);
    _backupSnapshotDao.saveReplicaSafe(basicDBObject);
    _backupSnapshotDao.setMtmTenants(
        snapshotId, List.of(new ServerlessTenant(serverlessClusterUniqueId, 500L)));
    return snapshotId;
  }

  private ClusterDescription createMTMClusterResource(
      final ObjectId mtmGroupId,
      final String mtmClusterName,
      final List<ObjectId> serverlessInstanceUniqueIds) {
    final ServerlessMTMCluster serverlessMtmCluster =
        new ServerlessMTMCluster(
            NDSModelTestFactory.getServerlessMTMCluster(
                mtmClusterName,
                mtmGroupId,
                ServerlessNDSDefaults.INSTANCE_SIZE,
                AWSRegionName.US_EAST_1,
                ServerlessTestFactory.TEST_MONGODB_MAJOR_VERSION));
    _mtmClusterDao.insert(serverlessMtmCluster);
    serverlessInstanceUniqueIds.forEach(
        serverlessInstanceUniqueId -> {
          _mtmClusterDao.incrementMtmTargetProxyVersion(
              mtmGroupId,
              mtmClusterName,
              serverlessMtmCluster.getMTMClusterType(),
              serverlessInstanceUniqueId.toString());
        });
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(mtmGroupId, mtmClusterName));
    createDummyReplicaSetHardware(mtmClusterName, mtmGroupId, null);
    return _clusterDescriptionDao.findByName(mtmGroupId, mtmClusterName).orElse(null);
  }

  private JSONArray getPolicyWithNewFrequency() {
    final JSONArray policyItems = new JSONArray();
    policyItems.put(getPolicyItem(DEFAULT_POLICY_ITEM_ID, "WEEKLY", 3, 8));
    return getPolicyArray(policyItems);
  }

  private JSONObject getPolicyItem(
      final ObjectId pPolicyItemId,
      final String pFrequencyType,
      final int pFrequencyInterval,
      final int pRetentionValue) {
    final JSONObject policyItem = new JSONObject();
    policyItem.put("id", pPolicyItemId);
    policyItem.put("frequencyType", pFrequencyType);
    policyItem.put("frequencyInterval", pFrequencyInterval);
    policyItem.put("retention", pRetentionValue);
    policyItem.put("retentionUnit", "DAYS");
    return policyItem;
  }

  private JSONArray getPolicyArray(final JSONArray pPolicyItems) {
    return getPolicyArray(DEFAULT_POLICY_ID, pPolicyItems);
  }

  private JSONArray getPolicyArray(final ObjectId pPolicyId, final JSONArray pPolicyItems) {
    final JSONObject policy = getDefaultPolicy(pPolicyId, pPolicyItems);
    final JSONArray policies = new JSONArray();
    policies.put(policy);
    return policies;
  }

  private JSONObject getDefaultPolicy(final ObjectId pPolicyId, final JSONArray pPolicyItems) {
    final JSONObject policy = new JSONObject();
    policy.put("id", pPolicyId);
    if (pPolicyItems != null) {
      policy.put("policyItems", pPolicyItems);
    }
    return policy;
  }

  private ClusterDescription createServerlessMTMClusterResourcesAndTenantContainers() {
    final ClusterDescription mtmClusterDescription =
        createMTMClusterResource(
            CpsResourceIntTests.SERVERLESS_GROUP_ID,
            CpsResourceIntTests.SERVERLESS_MTM_NAME,
            List.of(
                CpsResourceIntTests.SERVERLESS_CLUSTER_UNIQUE_ID,
                CpsResourceIntTests.SERVERLESS_CLUSTER_3_UNIQUE_ID));
    final TenantCloudProviderContainer container1 =
        new ServerlessCloudProviderContainer(
            NDSModelTestFactory.getServerlessContainer(
                mtmClusterDescription.getGroupId(),
                mtmClusterDescription.getName(),
                CpsResourceIntTests.SERVERLESS_INSTANCE_NAME));
    _ndsGroupDao.addCloudContainer(CpsResourceIntTests.SERVERLESS_GROUP_ID, container1);
    final TenantCloudProviderContainer container3 =
        new ServerlessCloudProviderContainer(
            NDSModelTestFactory.getServerlessContainer(
                mtmClusterDescription.getGroupId(),
                mtmClusterDescription.getName(),
                CpsResourceIntTests.SERVERLESS_INSTANCE_3_NAME));
    _ndsGroupDao.addCloudContainer(CpsResourceIntTests.SERVERLESS_GROUP_ID, container3);
    return mtmClusterDescription;
  }

  private void createDummyReplicaSetHardware(
      final String MTMClusterName,
      final ObjectId groupId,
      final ObjectId cloudProviderAccountId,
      final ObjectId... cloudContainerId) {
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(MTMClusterName, groupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
                groupId, MTMClusterName, MTMClusterName, List.of())
            .next()
            .rsId(),
        true,
        false,
        new ObjectId());
    final ObjectId hardwareInstanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    _replicaSetHardwareDao.setCloudProviderHardware(
        replicaSetHardwareId,
        new BasicDBList(),
        List.of(
            new AWSInstanceHardware(
                InstanceHardware.getEmptyHardware(
                    CloudProvider.AWS, new ObjectId(), new Date(), 0))),
        false);
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        hardwareInstanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        new Hostnames("hostname1").toDBList());
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        hardwareInstanceId,
        false,
        FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
        "LEGACY");
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        hardwareInstanceId,
        false,
        AWSInstanceHardware.FieldDefs.EBS_VOLUME_ENCRYPTION_KEY,
        "dummy old us-east-1 " + cloudProviderAccountId);
    if (cloudContainerId.length > 0) {
      _replicaSetHardwareDao.setInstanceField(
          replicaSetHardwareId,
          hardwareInstanceId,
          false,
          FieldDefs.CLOUD_PROVIDER_CONTAINER_ID,
          cloudContainerId[0]);
    }
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId, hardwareInstanceId, false, FieldDefs.PROVISIONED, true);
  }

  private void setupMockedDiskStats(
      final ObjectId pTargetGroupId, final String pTargetMTMClusterName) {
    _appSettings.setProp(
        "nds.serverless.metrics.mocks.enabled", "true", AppSettings.SettingType.MEMORY);

    final Instant now =
        Instant.now(Clock.systemUTC()).truncatedTo(ChronoUnit.MINUTES).plus(Duration.ofMinutes(1));

    for (int i = 0; i < 5; i++) {
      _serverlessMockedMetricsDao.insertMockedMetric(
          getServerlessMockedMetric(
              ServerlessMockedMetricType.MTM_SERIES,
              pTargetGroupId,
              pTargetMTMClusterName,
              ServerlessMetricName.DISK_MB,
              1e10,
              Map.of(
                  "provisionedHostname0",
                  5.0,
                  "provisionedHostname1",
                  5.0,
                  "provisionedHostname2",
                  5.0),
              Date.from(now.minus(Duration.ofMinutes(i))),
              Date.from(now.plus(Duration.ofDays(1)).plus(Duration.ofMinutes(i)))));
    }
  }

  private void createAutomationConfig(
      final ObjectId pTargetGroupId, final String pTargetMTMClusterName, final boolean pUpdateUser)
      throws Exception {
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/atm/AutomationConfigDao/config-basic-serverless-rs.json.ftl",
        Map.of(
            "groupId", pTargetGroupId,
            "rsId", pTargetMTMClusterName + "-shard-0",
            "provisionedHostname0", "provisionedHostname0",
            "provisionedHostname1", "provisionedHostname1",
            "provisionedHostname2", "provisionedHostname2"),
        AutomationConfig.DB_NAME,
        AutomationConfig.COLLECTION_NAME);
    if (pUpdateUser) {
      _serverlessUser =
          MmsFactory.createUser(
              _groupDao.findById(pTargetGroupId),
              "<EMAIL>",
              Set.of(Role.GROUP_BACKUP_ADMIN));
    }
  }

  private List<NameValuePair> getServerlessAutomatedRestoreWorkload(
      final ObjectId targetGroupId, final String targetInstanceName, ObjectId snapshotId) {
    return List.of(
        new BasicNameValuePair("deliveryMethod", "automated"),
        new BasicNameValuePair("targetGroupId", targetGroupId.toString()),
        new BasicNameValuePair("snapshotId", snapshotId.toHexString()),
        new BasicNameValuePair("sourceClusterName", SERVERLESS_INSTANCE_NAME),
        new BasicNameValuePair("targetClusterName", targetInstanceName));
  }

  private List<NameValuePair> getServerlessDownloadRestoreWorkload(ObjectId snapshotId) {
    return List.of(
        new BasicNameValuePair("deliveryMethod", "download"),
        new BasicNameValuePair("snapshotId", snapshotId.toHexString()),
        new BasicNameValuePair("sourceClusterName", SERVERLESS_INSTANCE_NAME));
  }

  private JSONObject createServerlessRestoreJob(final List<NameValuePair> formValues) {
    final JSONObject response =
        doAuthedJsonPostForm(
            _serverlessUser,
            "/nds/backup/serverless/" + SERVERLESS_GROUP_ID + "/restore/",
            formValues,
            HttpStatus.SC_OK);
    assertNotNull(response);

    final JSONArray restoreJobIds = response.getJSONArray("restoreJobIds");
    assertNotNull(restoreJobIds);

    final String restoreId = restoreJobIds.getString(0);
    final ObjectId restoreJobId = new ObjectId(restoreId);
    return findRestoreJob(_serverlessUser, SERVERLESS_GROUP_ID, restoreJobId, false);
  }

  private JSONObject getCollectionRestoreJobViewAndMock() {
    final JSONObject job = new JSONObject(); //
    job.put(CollectionRestoreRequestView.FieldDefs.SNAPSHOT_ID, BACKUP_SNAPSHOT_ID_1.toHexString());
    job.put(CollectionRestoreRequest.FieldDefs.TARGET_PROJECT_ID, GROUP_ID_2.toHexString());
    job.put(CollectionRestoreRequest.FieldDefs.TARGET_CLUSTER_NAME, CLUSTER_NAME_2);
    job.put(
        CollectionRestoreRequest.FieldDefs.TARGET_CLUSTER_UNIQUE_ID,
        CLUSTER_UNIQUE_ID_2.toHexString());
    job.put(
            CollectionRestoreRequest.FieldDefs.INDEX_RESTORE_OPTION,
            IndexRestoreOption.RESTORE_ALL_INDEXES.name())
        .names();
    job.put(
        CollectionRestoreRequest.FieldDefs.ROLLBACK_STRATEGY,
        RollbackStrategy.ROLLBACK_ALL_COLLECTIONS.name());
    job.put(
        CollectionRestoreRequest.FieldDefs.WRITE_STRATEGY,
        WriteStrategy.OVERWRITE_IF_EXISTS.name());
    job.put(CollectionRestoreRequest.FieldDefs.DBS_TO_RESTORE, new JSONArray());

    final JSONArray collectionsToRestore = new JSONArray();
    collectionsToRestore.put(createRestoreName("db1.collA", "db1.collA"));
    collectionsToRestore.put(createRestoreName("db1.collB", "db1.collectionB"));
    job.put(CollectionRestoreRequest.FieldDefs.COLLECTIONS_TO_RESTORE, collectionsToRestore);

    final Map<String, CustomerCollectionMetadata.ShardSizes> shardSizes =
        Map.of("shard-0", new CustomerCollectionMetadata.ShardSizes(1024, 2048));
    final Map<String, Map<String, CustomerCollectionMetadata.ShardSizes>> namespaceStats =
        Map.of("db1.collA", shardSizes, "db1.collB", shardSizes);
    final CustomerCollectionMetadata metadata =
        new CustomerCollectionMetadata(BACKUP_SNAPSHOT_ID_1.toHexString(), namespaceStats);
    doReturn(Optional.of(metadata))
        .when(_cpsCollectionMetadataBackupSvc)
        .getCustomerCollectionMetadata(any());
    doReturn(Optional.of(Double.valueOf(Units.GIGABYTES.convertTo(1, Units.BYTES))))
        .when(_computeClusterMetricsSvc)
        .getMinFreeDiskSpaceForCluster(any());
    return job;
  }

  private JSONObject createRestoreName(final String sourceName, final String targetName) {
    final JSONObject restoreName = new JSONObject();
    restoreName.put(CollectionRestoreRequest.FieldDefs.SOURCE_NAME, sourceName);
    restoreName.put(CollectionRestoreRequest.FieldDefs.TARGET_NAME, targetName);
    return restoreName;
  }

  protected static void populateBackupCompliancePolicySettings() throws IOException {
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupDataProtectionSettingsDao/dataProtectionSettings.json.ftl",
        null,
        "nds",
        "config.nds.backup.dataProtection");
  }
}
