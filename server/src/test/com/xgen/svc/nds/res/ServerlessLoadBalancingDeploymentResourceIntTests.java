package com.xgen.svc.nds.res;

import static java.lang.String.format;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.atm.core._private.dao.AutomationConfigDao;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.MaintainedEnvoyTemplate;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.AutomationTestUtils;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.serverless.dao.EnvoyConfigurationDao;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.serverless.model.EnvoyConfiguration;
import com.xgen.svc.nds.serverless.view.ServerlessLoadBalancingDeploymentView.FieldDefs;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ServerlessLoadBalancingDeploymentResourceIntTests extends JUnit5BaseResourceTest {
  private static final String BASE_URL = "/admin/nds/serverlessLoadBalancingDeployments";

  private AppUser _globalAtlasOperatorUser;
  private AppUser _globalAdminReadOnlyUser;

  @Inject ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  @Inject EnvoyConfigurationDao _envoyConfigurationDao;
  @Inject AutomationConfigDao _automationConfigDao;
  @Inject AppSettings _appSettings;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _globalAtlasOperatorUser =
        MmsFactory.createGlobalAtlasOperatorUser(MmsFactory.createGroupWithStandardPlan());
    _globalAdminReadOnlyUser = MmsFactory.createGlobalAdminReadOnlyUser();
  }

  @Test
  public void testGetServerlessLoadBalancingDeployments() throws Exception {
    final JSONArray arrayPreCreate =
        HttpUtils.getInstance()
            .get()
            .path(BASE_URL)
            .uiAuth(_globalAdminReadOnlyUser)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(JSONArray.class)
            .send();
    assertEquals(0, arrayPreCreate.length());

    final String testVersion = "1.21.3-14-gddda769";

    final List<ServerlessLoadBalancingDeployment> deploymentsAndVersionsMap = new ArrayList<>();
    deploymentsAndVersionsMap.add(
        ServerlessDeploymentModelTestFactory.getAWSServerlessLoadBalancingDeployment(
            AWSRegionName.US_EAST_1));
    deploymentsAndVersionsMap.add(
        ServerlessDeploymentModelTestFactory.getAzureServerlessLoadBalancingDeployment(
            AzureRegionName.US_EAST_2));
    deploymentsAndVersionsMap.add(
        ServerlessDeploymentModelTestFactory.getGCPServerlessLoadBalancingDeployment(
            GCPRegionName.CENTRAL_US));
    deploymentsAndVersionsMap.add(
        ServerlessDeploymentModelTestFactory.getUnProvisionedAWSServerlessLoadBalancingDeployment(
            AWSRegionName.US_EAST_1));
    deploymentsAndVersionsMap.add(
        ServerlessDeploymentModelTestFactory.getUnProvisionedAzureServerlessLoadBalancingDeployment(
            AzureRegionName.ASIA_EAST));
    deploymentsAndVersionsMap.add(
        ServerlessDeploymentModelTestFactory.getUnProvisionedGCPServerlessLoadBalancingDeployment(
            GCPRegionName.CENTRAL_US));

    deploymentsAndVersionsMap.forEach(
        deployment -> {
          _serverlessLoadBalancingDeploymentDao.save(deployment);
          AutomationConfig config = null;
          try {
            config =
                AutomationTestUtils.getAutomationConfigWithReplicaSetForTests(
                    deployment.getGroupId());
          } catch (final Exception e) {
            fail();
          }
          final MaintainedEnvoyTemplate maintainedEnvoyTemplate =
              new MaintainedEnvoyTemplate(_appSettings);
          maintainedEnvoyTemplate.updateVersion(testVersion);
          config.setMaintainedEnvoyTemplate(maintainedEnvoyTemplate);
          _automationConfigDao.save(config);
        });

    final JSONArray arrayPostCreate =
        HttpUtils.getInstance()
            .get()
            .path(BASE_URL)
            .uiAuth(_globalAdminReadOnlyUser)
            .returnType(JSONArray.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();
    assertEquals(6, arrayPostCreate.length());

    arrayPostCreate.forEach(
        deployment -> {
          final JSONObject serverlessLoadBalancingDeployment = (JSONObject) deployment;
          assertEquals(
              testVersion,
              serverlessLoadBalancingDeployment.getString(FieldDefs.SERVERLESS_PROXY_VERSION));
        });
  }

  @Test
  public void testUpdateLoadBalancingDeployment() {
    final String newDesiredInstanceSize = AWSNDSInstanceSize.M20.name();
    final String expectedDesiredInstanceFamily = AWSInstanceFamily.T3.name();
    final Integer newNumDesiredNodes = 4;
    final Integer newNumDesiredPreallocatedRecords = 5;
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);

    final JSONObject params = new JSONObject();
    params.put("desiredInstanceSize", newDesiredInstanceSize);
    params.put("numDesiredNodes", newNumDesiredNodes);
    params.put("numDesiredPreallocatedRecords", newNumDesiredPreallocatedRecords);

    HttpUtils.getInstance()
        .patch()
        .path(BASE_URL + "/%s", serverlessLoadBalancingDeployment.getId())
        .uiAuth(_globalAtlasOperatorUser)
        .data(params)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeploymentAfterPost =
        _serverlessLoadBalancingDeploymentDao.findAll().get(0);

    assertEquals(
        serverlessLoadBalancingDeploymentAfterPost.getDesiredInstanceSize(),
        newDesiredInstanceSize);
    assertEquals(
        serverlessLoadBalancingDeploymentAfterPost.getDesiredInstanceFamily(),
        expectedDesiredInstanceFamily);
    assertEquals(
        serverlessLoadBalancingDeploymentAfterPost.getNumDesiredNodes(), newNumDesiredNodes);
    assertEquals(
        serverlessLoadBalancingDeploymentAfterPost.getNumDesiredPreallocatedRecords(),
        newNumDesiredPreallocatedRecords);
  }

  @Test
  public void testUpdateLoadBalancingDeployment_DeploymentDoesNotExist() {
    final String newDesiredInstanceSize = AWSNDSInstanceSize.M20.name();
    final JSONObject params = new JSONObject();
    params.put("desiredInstanceSize", newDesiredInstanceSize);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .path(BASE_URL + "/%s", new ObjectId())
            .uiAuth(_globalAtlasOperatorUser)
            .data(params)
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertEquals(
        NDSErrorCode.INVALID_SERVERLESS_LOAD_BALANCING_DEPLOYMENT.name(),
        response.getString("errorCode"));
    assertEquals("Invalid Serverless Load Balancing Deployment", response.getString("message"));
  }

  @Test
  public void testUpdateLoadBalancingDeployment_InvalidInstanceSizeProvided() {
    final String newDesiredInstanceSize = "ABC";
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);

    final JSONObject params = new JSONObject();
    params.put("desiredInstanceSize", newDesiredInstanceSize);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .path(BASE_URL + "/%s", serverlessLoadBalancingDeployment.getId())
            .uiAuth(_globalAtlasOperatorUser)
            .data(params)
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertEquals(NDSErrorCode.INVALID_INSTANCE_SIZE.name(), response.getString("errorCode"));
    assertEquals(
        format(
            "No instance size %s for provider %s.",
            newDesiredInstanceSize, serverlessLoadBalancingDeployment.getCloudProvider()),
        response.getString("message"));
  }

  @Test
  public void testUpdateLoadBalancingDeployment_UnauthorizedUser() {
    final JSONObject params = new JSONObject();
    final ObjectId objectId = ObjectId.get();

    HttpUtils.getInstance()
        .patch()
        .path(BASE_URL + "/%s", objectId)
        .uiAuth(_globalAdminReadOnlyUser)
        .data(params)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testUpdateLoadBalancingDeployment_InvalidNumDesiredNodes() {
    final Integer newNumDesiredNodes = 0;
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);

    final JSONObject params = new JSONObject();
    params.put("desiredInstanceSize", AWSNDSInstanceSize.M20.name());
    params.put("numDesiredNodes", newNumDesiredNodes);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .path(BASE_URL + "/%s", serverlessLoadBalancingDeployment.getId())
            .uiAuth(_globalAtlasOperatorUser)
            .data(params)
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertEquals(
        NDSErrorCode.INVALID_ENVOY_NUM_DESIRED_NODES.name(), response.getString("errorCode"));
    assertEquals(
        format("Invalid number of desired nodes provided %s.", newNumDesiredNodes),
        response.getString("message"));
  }

  @Test
  public void testUpdateLoadBalancingDeployment_InvalidNumDesiredPreallocatedRecords() {
    final Integer newNumDesiredPreallocatedRecords = -1;
    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);

    final JSONObject params = new JSONObject();
    params.put("numDesiredPreallocatedRecords", newNumDesiredPreallocatedRecords);

    final JSONObject response =
        HttpUtils.getInstance()
            .patch()
            .path(BASE_URL + "/%s", serverlessLoadBalancingDeployment.getId())
            .uiAuth(_globalAtlasOperatorUser)
            .data(params)
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertEquals(
        NDSErrorCode.INVALID_ENVOY_NUM_DESIRED_PREALLOCATED_RECORDS.name(),
        response.getString("errorCode"));
    assertEquals(
        format(
            "Invalid number of desired preallocated records provided %s.",
            newNumDesiredPreallocatedRecords),
        response.getString("message"));
  }

  @Test
  public void testGetEnvoyConfiguration() {
    final Group group = MmsFactory.createGroupWithStandardPlan();

    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getAWSServerlessLoadBalancingDeployment(
            AWSRegionName.US_EAST_1, group.getId(), new ObjectId());
    _serverlessLoadBalancingDeploymentDao.save(serverlessLoadBalancingDeployment);
    final JSONObject responsePreCreate =
        HttpUtils.getInstance()
            .get()
            .path(
                BASE_URL + "/%s/%s",
                serverlessLoadBalancingDeployment.getId(),
                "envoyConfiguration")
            .uiAuth(_globalAdminReadOnlyUser)
            .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
            .returnType(JSONObject.class)
            .send();

    assertNotNull(responsePreCreate);
    assertEquals(
        NDSErrorCode.NO_ENVOY_CONFIGURATION_FOUND_FOR_DEPLOYMENT.name(),
        responsePreCreate.getString("errorCode"));
    assertEquals(
        format(
            "No envoy configuration found for deployment %s",
            serverlessLoadBalancingDeployment.getId()),
        responsePreCreate.getString("message"));

    final EnvoyConfiguration envoyConfiguration =
        ServerlessDeploymentModelTestFactory.getEnvoyConfiguration(
            serverlessLoadBalancingDeployment.getGroupId(),
            serverlessLoadBalancingDeployment.getId(),
            0L,
            Map.of(),
            Map.of());
    _envoyConfigurationDao.createConfiguration(envoyConfiguration);

    final JSONObject responsePostCreate =
        HttpUtils.getInstance()
            .get()
            .path(
                BASE_URL + "/%s/%s",
                serverlessLoadBalancingDeployment.getId(),
                "envoyConfiguration")
            .uiAuth(_globalAdminReadOnlyUser)
            .returnType(JSONObject.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();
    assertNotNull(responsePostCreate);
    assertTrue(responsePostCreate.has("clusters"));
    assertTrue(responsePostCreate.has("listeners"));
  }
}
