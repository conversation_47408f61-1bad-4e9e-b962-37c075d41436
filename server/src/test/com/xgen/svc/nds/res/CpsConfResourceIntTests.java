package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.amazonaws.services.securitytoken.model.Credentials;
import com.google.auth.oauth2.AccessToken;
import com.google.inject.Injector;
import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.brs.core._public.svc.BackupDeploymentSvc;
import com.xgen.cloud.brs.daemon._public.grid.BackupInjector;
import com.xgen.cloud.brs.daemon._public.grid.SeedBlockstoreConfigs;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._private.dao.CpsBlobStoreConfigDao;
import com.xgen.cloud.cps.pit._private.dao.CpsRegionalMetadataStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsBlobStoreConfig;
import com.xgen.cloud.cps.pit._public.model.OplogMigration;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureStorageAccountDao;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureStorageAccount;
import com.xgen.cloud.nds.azure._public.model.error.AzureApiException;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.CpsTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.cps.CpsConfSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class CpsConfResourceIntTests extends JUnit5BaseResourceTest {

  private static final String AGENT_SESSION_KEY = ObjectId.get().toHexString();
  private static final String AGENT_ADDRESS = "127.0.0.1";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private BackupJobDao _backupJobDao;

  @Inject private AWSAccountDao _awsAccountDao;

  @Inject private GCPOrganizationDao _gcpOrganizationDao;

  @Inject private AzureStorageAccountDao _azureStorageAccountDao;

  @Inject private CpsBlobStoreConfigDao _blobStoreConfigDao;

  @Inject private CpsConfSvc _cpsConfSvc;

  @Inject private BackupDeploymentSvc _deploymentSvc;

  @Inject private CpsRegionalMetadataStoreConfigDao _cpsRegionalMetadataStoreConfigDao;

  @Inject private AppSettings _appSettings;

  @Inject private Injector _injector;

  private SeedBlockstoreConfigs _seedBlockstoreConfigs;
  private Group _groupAWS;
  private Group _groupGCP;
  private Group _groupAzure;
  private AppUser _userAWS;
  private AppUser _userGCP;
  private AppUser _userAzure;
  private AgentApiKey _agentApiKeyAWS;
  private AgentApiKey _agentApiKeyGCP;
  private AgentApiKey _agentApiKeyAzure;
  private ClusterDescription _clusterDescriptionAWS;
  private ClusterDescription _clusterDescriptionGCP;
  private ClusterDescription _clusterDescriptionAzure;
  private String _rsIdAWS;
  private String _rsIdGCP;
  private String _rsIdAzure;
  private String _confEndpointAWS;
  private String _confEndpointGCP;
  private String _confEndpointAzure;
  private AWSAccount _awsAccount;
  private GCPOrganization _gcpOrganization;
  private AzureStorageAccount _azureStorageAccount;
  private AWSRegionName _blobStoreRegionName1;
  private CpsBlobStoreConfig _blobStoreConfig1;
  private String _blobStoreBucket1;
  private AWSRegionName _blobStoreRegionName2;
  private String _blobStoreBucket2;
  private CpsBlobStoreConfig _blobStoreConfig2;
  private GCPRegionName _blobStoreRegionName3;
  private String _blobStoreBucket3;
  private CpsBlobStoreConfig _blobStoreConfig3;
  private GCPRegionName _blobStoreRegionName4;
  private String _blobStoreBucket4;
  private CpsBlobStoreConfig _blobStoreConfig4;
  private AzureRegionName _blobStoreRegionName5;
  private String _blobStoreBucket5;
  private CpsBlobStoreConfig _blobStoreConfig5;
  private AzureRegionName _blobStoreRegionName6;
  private String _blobStoreBucket6;
  private CpsBlobStoreConfig _blobStoreConfig6;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _seedBlockstoreConfigs = new SeedBlockstoreConfigs(new BackupInjector(_injector, _appSettings));
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");

    _groupAWS = MmsFactory.createGroup(organization, "cus_0001");
    _userAWS =
        MmsFactory.createUser(_groupAWS, String.format("<EMAIL>", getUniquifier()));
    _agentApiKeyAWS = MmsFactory.generateApiKey(_groupAWS.getId(), _userAWS.getId());

    _groupGCP = MmsFactory.createGroup(organization, "cus_0002");
    _userGCP =
        MmsFactory.createUser(_groupGCP, String.format("<EMAIL>", getUniquifier()));
    _agentApiKeyGCP = MmsFactory.generateApiKey(_groupGCP.getId(), _userGCP.getId());

    _groupAzure = MmsFactory.createGroup(organization, "cus_0003");
    _userAzure =
        MmsFactory.createUser(_groupAzure, String.format("<EMAIL>", getUniquifier()));
    _agentApiKeyAzure = MmsFactory.generateApiKey(_groupAzure.getId(), _userAzure.getId());

    // Setup ClusterDescription
    final BasicDBObject clusterBson =
        NDSModelTestFactory.getAWSClusterDescription(_groupAWS.getId(), "foo")
            .append(FieldDefs.DISK_BACKUP_ENABLED, true)
            .append(FieldDefs.PIT_ENABLED, true);
    _clusterDescriptionAWS = new ClusterDescription(clusterBson);
    _clusterDescriptionDao.save(_clusterDescriptionAWS);

    final BasicDBObject clusterBsonGCP =
        NDSModelTestFactory.getGCPClusterDescription(_groupGCP.getId(), "bar")
            .append(FieldDefs.DISK_BACKUP_ENABLED, true)
            .append(FieldDefs.PIT_ENABLED, true);
    _clusterDescriptionGCP = new ClusterDescription(clusterBsonGCP);
    _clusterDescriptionDao.save(_clusterDescriptionGCP);

    final BasicDBObject clusterBsonAzure =
        NDSModelTestFactory.getAzureClusterDescription(_groupAzure.getId(), "foobar")
            .append(FieldDefs.DISK_BACKUP_ENABLED, true)
            .append(FieldDefs.PIT_ENABLED, true);
    _clusterDescriptionAzure = new ClusterDescription(clusterBsonAzure);
    _clusterDescriptionDao.save(_clusterDescriptionAzure);

    // Replica set hardware
    final BasicDBObject hardwareDocAWS =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), _clusterDescriptionAWS);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(hardwareDocAWS);
    _rsIdAWS = replicaSetHardware.getRsId();
    _replicaSetHardwareDao.save(hardwareDocAWS, WriteConcern.ACKNOWLEDGED);

    final BasicDBObject hardwareDocGCP =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), _clusterDescriptionGCP);
    final ReplicaSetHardware replicaSetHardwareGCP = new ReplicaSetHardware(hardwareDocGCP);
    _rsIdGCP = replicaSetHardwareGCP.getRsId();
    _replicaSetHardwareDao.save(hardwareDocGCP, WriteConcern.ACKNOWLEDGED);

    final BasicDBObject hardwareDocAzure =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), _clusterDescriptionAzure);
    final ReplicaSetHardware replicaSetHardwareAzure = new ReplicaSetHardware(hardwareDocAzure);
    _rsIdAzure = replicaSetHardwareAzure.getRsId();
    _replicaSetHardwareDao.save(hardwareDocAzure, WriteConcern.ACKNOWLEDGED);

    // Setup Backup Job
    final BackupJob backupJobAWS =
        NDSModelTestFactory.getBackupJobBuilder()
            .projectId(_groupAWS.getId())
            .clusterName(_clusterDescriptionAWS.getName())
            .build();
    _backupJobDao.insert(BackupJobDao.toDbObj(backupJobAWS), WriteConcern.ACKNOWLEDGED);

    final BackupJob backupJobGCP =
        NDSModelTestFactory.getBackupJobBuilder()
            .projectId(_groupGCP.getId())
            .clusterName(_clusterDescriptionGCP.getName())
            .build();
    _backupJobDao.insert(BackupJobDao.toDbObj(backupJobGCP), WriteConcern.ACKNOWLEDGED);

    final BackupJob backupJobAzure =
        NDSModelTestFactory.getBackupJobBuilder()
            .projectId(_groupAzure.getId())
            .clusterName(_clusterDescriptionAzure.getName())
            .build();
    _backupJobDao.insert(BackupJobDao.toDbObj(backupJobAzure), WriteConcern.ACKNOWLEDGED);

    // populate with some region config, this dao is not supposed to do any inserts,
    // but for the sake of testing we do it only here
    _cpsRegionalMetadataStoreConfigDao
        .getDbCollection()
        .insert(CpsTestUtils.getDefaultCpsRegionalMetadataStoreConfig(), WriteConcern.ACKNOWLEDGED);

    _blobStoreRegionName1 = AWSRegionName.EU_WEST_3;
    final String blobStoreRegion1 = _blobStoreRegionName1.getValue();
    final String blobStoreId1 = String.format("s3blobstore-%s", blobStoreRegion1);
    _blobStoreBucket1 = String.format("atlas-backup-oplogs-%s-local", blobStoreRegion1);
    _blobStoreConfig1 =
        new CpsBlobStoreConfig(
            blobStoreId1,
            true,
            CloudProvider.PROVIDER_AWS,
            blobStoreRegion1,
            _blobStoreBucket1,
            "s3.amazonaws.com");
    _blobStoreConfigDao.insertReplicaSafe(CpsBlobStoreConfigDao.toDBObject(_blobStoreConfig1));

    _blobStoreRegionName2 = AWSRegionName.US_EAST_2;
    final String blobStoreRegion2 = _blobStoreRegionName2.getValue();
    final String blobStoreId2 = String.format("s3blobstore-%s", blobStoreRegion2);
    _blobStoreBucket2 = String.format("atlas-backup-oplogs-%s-local", blobStoreRegion2);
    _blobStoreConfig2 =
        new CpsBlobStoreConfig(
            blobStoreId2,
            true,
            CloudProvider.PROVIDER_AWS,
            blobStoreRegion2,
            _blobStoreBucket2,
            "s3.amazonaws.com");
    _blobStoreConfigDao.insertReplicaSafe(CpsBlobStoreConfigDao.toDBObject(_blobStoreConfig2));

    _blobStoreRegionName3 = GCPRegionName.EASTERN_US;
    final String blobStoreRegion3 = _blobStoreRegionName3.getValue();
    final String blobStoreId3 = String.format("gcpblobstore-%s", blobStoreRegion3);
    _blobStoreBucket3 = String.format("atlas-backup-oplogs-%s-local", blobStoreRegion3);
    _blobStoreConfig3 =
        new CpsBlobStoreConfig(
            blobStoreId3,
            true,
            CloudProvider.PROVIDER_GCP,
            blobStoreRegion3,
            _blobStoreBucket3,
            "storage.googleapis.com");
    _blobStoreConfigDao.insertReplicaSafe(CpsBlobStoreConfigDao.toDBObject(_blobStoreConfig3));

    _blobStoreRegionName4 = GCPRegionName.WESTERN_US;
    final String blobStoreRegion4 = _blobStoreRegionName4.getValue();
    final String blobStoreId4 = String.format("gcpblobstore-%s", blobStoreRegion4);
    _blobStoreBucket4 = String.format("atlas-backup-oplogs-%s-local", blobStoreRegion4);
    _blobStoreConfig4 =
        new CpsBlobStoreConfig(
            blobStoreId4,
            true,
            CloudProvider.PROVIDER_GCP,
            blobStoreRegion4,
            _blobStoreBucket4,
            "storage.googleapis.com");
    _blobStoreConfigDao.insertReplicaSafe(CpsBlobStoreConfigDao.toDBObject(_blobStoreConfig4));

    _blobStoreRegionName5 = AzureRegionName.US_EAST;
    final String blobStoreRegion5 = _blobStoreRegionName5.getValue();
    final String blobStoreId5 = String.format("azureblobstore-%s", blobStoreRegion5);
    _blobStoreBucket5 = String.format("lclabo%s", blobStoreRegion5);
    _blobStoreConfig5 =
        new CpsBlobStoreConfig(
            blobStoreId5,
            true,
            CloudProvider.PROVIDER_AZURE,
            blobStoreRegion5,
            _blobStoreBucket5,
            "blob.core.windows.net");
    _blobStoreConfigDao.insertReplicaSafe(CpsBlobStoreConfigDao.toDBObject(_blobStoreConfig5));

    _blobStoreRegionName6 = AzureRegionName.US_WEST;
    final String blobStoreRegion6 = _blobStoreRegionName6.getValue();
    final String blobStoreId6 = String.format("azureblobstore-%s", blobStoreRegion6);
    _blobStoreBucket6 = String.format("lclabo%s", blobStoreRegion6);
    _blobStoreConfig6 =
        new CpsBlobStoreConfig(
            blobStoreId6,
            true,
            CloudProvider.PROVIDER_AZURE,
            blobStoreRegion6,
            _blobStoreBucket6,
            "blob.core.windows.net");
    _blobStoreConfigDao.insertReplicaSafe(CpsBlobStoreConfigDao.toDBObject(_blobStoreConfig6));

    // Enable PIT
    final PitSetting pitSettingAWS =
        PitSetting.builder()
            .blobStoreConfigId(blobStoreId1)
            .regionalMetadataStoreConfigId("store1")
            .regionName(blobStoreRegion1)
            .lastContiguousCheckEndTimestamp(new BSONTimestamp(0, 0))
            .oplogMigration(
                OplogMigration.Builder.aOplogMigration()
                    .startDate(new Date())
                    .storage(
                        List.of(
                            PitStorage.Builder.aPitStorage()
                                .blobStoreConfigId(blobStoreId2)
                                .blobStoreRegionName(blobStoreRegion2)
                                .metadataStoreConfigId("metaStore2")
                                .build()))
                    .build())
            .build();

    final PitSetting pitSettingGCP =
        PitSetting.builder()
            .blobStoreConfigId(blobStoreId3)
            .regionalMetadataStoreConfigId("store3")
            .regionName(blobStoreRegion3)
            .lastContiguousCheckEndTimestamp(new BSONTimestamp(0, 0))
            .oplogMigration(
                OplogMigration.Builder.aOplogMigration()
                    .startDate(new Date())
                    .storage(
                        List.of(
                            PitStorage.Builder.aPitStorage()
                                .blobStoreConfigId(blobStoreId4)
                                .blobStoreRegionName(blobStoreRegion4)
                                .metadataStoreConfigId("metaStore4")
                                .build()))
                    .build())
            .build();

    final PitSetting pitSettingAzure =
        PitSetting.builder()
            .blobStoreConfigId(blobStoreId5)
            .regionalMetadataStoreConfigId("store5")
            .regionName(blobStoreRegion5)
            .lastContiguousCheckEndTimestamp(new BSONTimestamp(0, 0))
            .oplogMigration(
                OplogMigration.Builder.aOplogMigration()
                    .startDate(new Date())
                    .storage(
                        List.of(
                            PitStorage.Builder.aPitStorage()
                                .blobStoreConfigId(blobStoreId6)
                                .blobStoreRegionName(blobStoreRegion6)
                                .metadataStoreConfigId("metaStore6")
                                .build()))
                    .build())
            .build();

    final Map<String, PitSetting> rsIdToPitSettingsAWS = new HashMap<>();
    rsIdToPitSettingsAWS.put(_rsIdAWS, pitSettingAWS);

    final Map<String, PitSetting> rsIdToPitSettingsGCP = new HashMap<>();
    rsIdToPitSettingsGCP.put(_rsIdGCP, pitSettingGCP);

    final Map<String, PitSetting> rsIdToPitSettingsAzure = new HashMap<>();
    rsIdToPitSettingsAzure.put(_rsIdAzure, pitSettingAzure);

    _backupJobDao.enablePitIfDisabled(backupJobAWS.getId());
    _backupJobDao.updatePitSettings(
        backupJobAWS.getId(), rsIdToPitSettingsAWS, backupJobAWS.getVersion() + 1);

    _backupJobDao.enablePitIfDisabled(backupJobGCP.getId());
    _backupJobDao.updatePitSettings(
        backupJobGCP.getId(), rsIdToPitSettingsGCP, backupJobGCP.getVersion() + 1);

    _backupJobDao.enablePitIfDisabled(backupJobAzure.getId());
    _backupJobDao.updatePitSettings(
        backupJobAzure.getId(), rsIdToPitSettingsAzure, backupJobAzure.getVersion() + 1);

    final String agentHostNameAWS =
        replicaSetHardware.getHardware().get(0).getHostnameForAgents().get();

    final String agentHostNameGCP =
        replicaSetHardwareGCP.getHardware().get(0).getHostnameForAgents().get();

    final String agentHostNameAzure =
        replicaSetHardwareAzure.getHardware().get(0).getHostnameForAgents().get();

    // Setup primary agent session for replica set
    _cpsConfSvc.isSessionWinner(
        _groupAWS.getId(),
        _rsIdAWS,
        AGENT_SESSION_KEY,
        AGENT_ADDRESS,
        agentHostNameAWS,
        System.currentTimeMillis());
    _confEndpointAWS =
        String.format(
            "/conf/cps/%s/cluster/%s?ah=%s&sk=%s&av=dummyVersion",
            _groupAWS.getId(),
            _clusterDescriptionAWS.getName(),
            agentHostNameAWS,
            AGENT_SESSION_KEY);

    _cpsConfSvc.isSessionWinner(
        _groupGCP.getId(),
        _rsIdGCP,
        AGENT_SESSION_KEY,
        AGENT_ADDRESS,
        agentHostNameGCP,
        System.currentTimeMillis());
    _confEndpointGCP =
        String.format(
            "/conf/cps/%s/cluster/%s?ah=%s&sk=%s&av=dummyVersion",
            _groupGCP.getId(),
            _clusterDescriptionGCP.getName(),
            agentHostNameGCP,
            AGENT_SESSION_KEY);

    _cpsConfSvc.isSessionWinner(
        _groupAzure.getId(),
        _rsIdAzure,
        AGENT_SESSION_KEY,
        AGENT_ADDRESS,
        agentHostNameAzure,
        System.currentTimeMillis());
    _confEndpointAzure =
        String.format(
            "/conf/cps/%s/cluster/%s?ah=%s&sk=%s&av=dummyVersion",
            _groupAzure.getId(),
            _clusterDescriptionAzure.getName(),
            agentHostNameAzure,
            AGENT_SESSION_KEY);

    final BasicDBObject awsAccountDoc = AWSModelTestFactory.getAWSAccount();
    awsAccountDoc.append(AWSAccount.FieldDefs.FOR_CPS_OPLOG_STORE, true);
    _awsAccountDao.saveReplicaSafe(awsAccountDoc);
    _awsAccount = new AWSAccount(awsAccountDoc);

    _gcpOrganization =
        new GCPOrganization("local", 2, "localProject", "credentialFile", "billing-account-id");
    _gcpOrganizationDao.save(_gcpOrganization);

    _azureStorageAccount =
        new AzureStorageAccount(
            ObjectId.get(),
            ObjectId.get(),
            "",
            "storage-account-name",
            "storage-account-key",
            new Date(),
            new Date());
    _azureStorageAccountDao.save(_azureStorageAccount);

    _seedBlockstoreConfigs.ensureDefaultConfigs(_deploymentSvc.getDefaultBackupRegion());
  }

  @Test
  public void testHostConfigure_providerSettingsAWS() throws Exception {
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final CpsSvc cpsSvc = mock(CpsSvc.class);
    final AWSApiException awsApiException = mock(AWSApiException.class);
    doReturn(CommonErrorCode.NO_AUTHORIZATION).when(awsApiException).getErrorCode();

    final Credentials awsCredentials =
        new Credentials("accessKeyId", "secretKey", "sessionToken", new Date());

    doReturn(awsCredentials)
        .when(awsApiSvc)
        .getTemporarySessionCredentials(
            eq(_awsAccount), any(), eq(CpsConfSvc.CPS_AGENT_FEDERATED_USER), any(), any(), any());

    doReturn(true).when(cpsSvc).isCopySettingEnabledForCloudProvider(any(), any());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsConfSvc.class), "_awsApiSvc", awsApiSvc);
    ClassModifier.modifyInstanceValue(AppConfig.getInstance(CpsConfSvc.class), "_cpsSvc", cpsSvc);

    final JSONObject response =
        new JSONObject(doAgentApiCallGet(_groupAWS, _agentApiKeyAWS, _confEndpointAWS));

    final JSONArray awsListArray =
        response.getJSONObject("providerSettings").getJSONArray("awsList");
    assertEquals(2, awsListArray.length());

    // Convert JSONArray to List<JSONObject>, awsListArray is not sorted,
    List<JSONObject> jsonList = new ArrayList<>();
    for (int i = 0; i < awsListArray.length(); i++) {
      jsonList.add(awsListArray.getJSONObject(i));
    }
    Collections.sort(
        jsonList, (a, b) -> a.optString("bucket", "").compareTo(b.optString("bucket", "")));

    final JSONObject awsObj1 = jsonList.get(0);
    assertTrue(awsObj1.isNull("accessKeyId"));
    assertTrue(awsObj1.isNull("secretAccessKey"));
    assertTrue(awsObj1.isNull("sessionToken"));

    // Expected value: atlas-backup-oplogs-eu-west-3-local
    assertEquals(_blobStoreBucket1, awsObj1.getString("bucket"));
    // Expected value: ue-west-3
    assertEquals(_blobStoreRegionName1.getValue(), awsObj1.getString("region"));

    final JSONObject awsObj2 = jsonList.get(1);
    assertTrue(awsObj2.isNull("accessKeyId"));
    assertTrue(awsObj2.isNull("secretAccessKey"));
    assertTrue(awsObj2.isNull("sessionToken"));

    // Expected value: atlas-backup-oplogs-us-east-2-local
    assertEquals(_blobStoreBucket2, awsObj2.getString("bucket"));
    // Expected value: us-east-2
    assertEquals(_blobStoreRegionName2.getValue(), awsObj2.getString("region"));
  }

  @Test
  public void testHostConfigure_providerSettingsGCP() throws Exception {
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);
    final CpsSvc cpsSvc = mock(CpsSvc.class);
    final GCPApiException gcpApiException = mock(GCPApiException.class);
    doReturn(CommonErrorCode.NO_AUTHORIZATION).when(gcpApiException).getErrorCode();

    final AccessToken accessToken = new AccessToken("accessToken", new Date());
    doReturn(accessToken)
        .when(gcpApiSvc)
        .refreshGcpAccessToken(
            eq(List.of("https://www.googleapis.com/auth/devstorage.read_write")));

    doReturn(true).when(cpsSvc).isCopySettingEnabledForCloudProvider(any(), any());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsConfSvc.class), "_gcpApiSvc", gcpApiSvc);
    ClassModifier.modifyInstanceValue(AppConfig.getInstance(CpsConfSvc.class), "_cpsSvc", cpsSvc);

    final JSONObject response =
        new JSONObject(doAgentApiCallGet(_groupGCP, _agentApiKeyGCP, _confEndpointGCP));

    final JSONArray gcpListArray =
        response.getJSONObject("providerSettings").getJSONArray("gcpList");
    assertEquals(2, gcpListArray.length());

    // Convert JSONArray to List<JSONObject>, gcpListArray is not sorted,
    List<JSONObject> jsonList = new ArrayList<>();
    for (int i = 0; i < gcpListArray.length(); i++) {
      jsonList.add(gcpListArray.getJSONObject(i));
    }
    Collections.sort(
        jsonList, (a, b) -> a.optString("bucket", "").compareTo(b.optString("bucket", "")));

    final JSONObject gcpObj1 = jsonList.get(0);
    assertTrue(gcpObj1.isNull("sessionToken"));

    // Expected value: atlas-backup-oplogs-us-east1-local
    assertEquals(_blobStoreBucket3, gcpObj1.getString("bucket"));
    // Expected value: us-east1
    assertEquals(_blobStoreRegionName3.getValue(), gcpObj1.getString("region"));

    final JSONObject gcpObj2 = jsonList.get(1);
    assertTrue(gcpObj2.isNull("sessionToken"));

    // Expected value: atlas-backup-oplogs-us-west1-local
    assertEquals(_blobStoreBucket4, gcpObj2.getString("bucket"));
    // Expected value: us-west1
    assertEquals(_blobStoreRegionName4.getValue(), gcpObj2.getString("region"));
  }

  @Test
  public void testHostConfigure_providerSettingsAzure() throws Exception {
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);
    final CpsSvc cpsSvc = mock(CpsSvc.class);
    final AzureApiException azureApiException = mock(AzureApiException.class);
    doReturn(CommonErrorCode.NO_AUTHORIZATION).when(azureApiException).getErrorCode();

    final NDSAzureTempCredentialsView creds =
        new NDSAzureTempCredentialsView("sasUri", "sasToken", new Date());
    doReturn(creds)
        .when(azureApiSvc)
        .generateStorageContainerSasToken(
            eq(_azureStorageAccount.getStorageAccountName()),
            eq(_azureStorageAccount.getStorageAccountKey()),
            anyString(),
            anyString());

    doReturn(true).when(cpsSvc).isCopySettingEnabledForCloudProvider(any(), any());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(CpsConfSvc.class), "_azureApiSvc", azureApiSvc);
    ClassModifier.modifyInstanceValue(AppConfig.getInstance(CpsConfSvc.class), "_cpsSvc", cpsSvc);

    final JSONObject response =
        new JSONObject(doAgentApiCallGet(_groupAzure, _agentApiKeyAzure, _confEndpointAzure));

    final JSONArray azureListArray =
        response.getJSONObject("providerSettings").getJSONArray("azureList");
    assertEquals(2, azureListArray.length());

    // Convert JSONArray to List<JSONObject>, azureListArray is not sorted,
    List<JSONObject> jsonList = new ArrayList<>();
    for (int i = 0; i < azureListArray.length(); i++) {
      jsonList.add(azureListArray.getJSONObject(i));
    }
    Collections.sort(
        jsonList, (a, b) -> a.optString("bucket", "").compareTo(b.optString("bucket", "")));

    final JSONObject azureObj1 = jsonList.get(0);
    assertTrue(azureObj1.isNull("sasToken"));
    assertTrue(azureObj1.isNull("sasUri"));

    // Expected value: lclaboeastus
    assertEquals(_blobStoreBucket5, azureObj1.getString("bucket"));
    // Expected value: eastus
    assertEquals(_blobStoreRegionName5.getValue(), azureObj1.getString("region"));

    final JSONObject azureObj2 = jsonList.get(1);
    assertTrue(azureObj2.isNull("sasToken"));
    assertTrue(azureObj2.isNull("sasUri"));

    // Expected value: lclabowestus
    assertEquals(_blobStoreBucket6, azureObj2.getString("bucket"));
    // Expected value: westus
    assertEquals(_blobStoreRegionName6.getValue(), azureObj2.getString("region"));
  }
}
