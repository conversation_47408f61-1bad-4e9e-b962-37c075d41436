package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.mms.api.res.ApiJUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Set;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

@ExtendWith(GuiceTestExtension.class)
public class AdminCpsSnapshotUsageResourceIntTests extends ApiJUnit5BaseResourceTest {

  private static final ObjectId GROUP_ID = oid(118);
  private static final String CLUSTER_NAME = "rs1";
  @Inject private GroupDao _groupDao;

  private AppUser _user;

  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, NDSGroupDao.DB, NDSGroupDao.COLLECTION);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        NDSGroupDao.DB,
        ClusterDescriptionDao.COLLECTION_NAME);

    final Group group = _groupDao.findById(GROUP_ID);
    _user =
        MmsFactory.createUser(group, "<EMAIL>", Set.of(Role.GLOBAL_MONITORING_ADMIN));
  }

  @ParameterizedTest
  @CsvSource({
    "2024-01-01, 2024-04-01, SNAPSHOT_USAGE_DATE_RANGE_INVALID, AZURE",
    "2023-10-02, 2024-01-01, SNAPSHOT_USAGE_DATE_RANGE_INVALID, GCP",
    "2023-01-01, invalidFormatDate, INVALID_ARGUMENT, AZURE",
  })
  public void testGetSnapshotUsageSummary_invalidDateParams(
      String startDate, String endDate, String expectedError, String cloudProvider) {
    final JSONObject response1 =
        doAuthedJsonGet(
            _user,
            String.format(
                "/nds/backup/%s/%s/snapshotUsages/%s?usageStartDate=%s&&usageEndDate=%s",
                GROUP_ID, CLUSTER_NAME, cloudProvider, startDate, endDate),
            HttpStatus.SC_BAD_REQUEST);

    assertEquals(expectedError, response1.getString("errorCode"));
  }

  @Test
  public void testGetSnapshotUsageSummaryAzure_successful() {
    doAuthedJsonArrayGet(
        _user,
        String.format(
            "/nds/backup/%s/%s/snapshotUsages/AZURE?usageStartDate=%s&&usageEndDate=%s",
            GROUP_ID, CLUSTER_NAME, "2024-01-01", "2024-03-31"),
        HttpStatus.SC_OK);
  }

  @Test
  public void testGetSnapshotUsageSummaryAws_successful() {
    doAuthedJsonArrayGet(
        _user,
        String.format("/nds/backup/%s/%s/snapshotUsages/AWS", GROUP_ID, CLUSTER_NAME),
        HttpStatus.SC_OK);
  }
}
