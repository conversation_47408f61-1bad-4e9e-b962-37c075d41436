package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._private.dao.HostClusterDao;
import com.xgen.cloud.monitoring.topology._public.model.ClusterType;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class GoToResourceIntTests extends JUnit5BaseResourceTest {

  @Inject ClusterDescriptionDao _clusterDescriptionDao;
  @Inject NDSGroupSvc _groupSvc;
  @Inject HostClusterDao _hostClusterDao;

  private Organization _organization;
  private Group _group;
  private AppUser _tseUser;
  private AppUser _adminUser;
  private AppUser _orgOwner;
  private AppUser _groupMember;
  private AppUser _groupOwner;
  private ClusterDescription _cluster;
  private ClusterDescription _shardedCluster;
  private ClusterDescription _serverlessCluster;
  private HostCluster _hostCluster1;
  private HostCluster _hostCluster2;
  private ClusterDescription _freeCluster;

  /**
   * Helper method that normalizes URLs with variable placeholders in the format {name:value} with
   * just the value.
   */
  private String normalizeUrl(String url) {
    if (url == null) {
      return null;
    }
    // Use regex to match {name:value} pattern and replace with just value
    Pattern pattern = Pattern.compile("\\{([^:]+):([^}]+)\\}");
    Matcher matcher = pattern.matcher(url);
    StringBuffer sb = new StringBuffer();
    while (matcher.find()) {
      matcher.appendReplacement(sb, matcher.group(2));
    }
    matcher.appendTail(sb);
    return sb.toString();
  }

  /** Helper method to assert URL equality, normalizing any variable placeholders. */
  private void assertUrlEquals(String expected, String actual) {
    assertEquals(expected, normalizeUrl(actual));
  }

  private static final String GOTO_BASE = "/goto";

  private String getPathsUrl(final ObjectId pOrgId, final ObjectId pProjectId) {
    return getPathsUrl(pOrgId, pProjectId, null);
  }

  private String getPathsUrl(
      final ObjectId pOrgId, final ObjectId pProjectId, final String pClusterName) {
    return getUrl(GOTO_BASE, pOrgId, pProjectId, pClusterName);
  }

  private String getCommandUrl(final String pCommand) {
    return getCommandUrl(pCommand, null, null);
  }

  private String getCommandUrl(
      final String pCommand, final ObjectId pOrgId, final ObjectId pProjectId) {
    return getUrl(GOTO_BASE + pCommand, pOrgId, pProjectId);
  }

  private String getCommandUrl(
      final String pCommand,
      final ObjectId pOrgId,
      final ObjectId pProjectId,
      final String pClusterName) {
    return getUrl(GOTO_BASE + pCommand, pOrgId, pProjectId, pClusterName);
  }

  private String getUrl(final String pBase, final ObjectId pOrgId, final ObjectId pProjectId) {
    return getUrl(pBase, pOrgId, pProjectId, null);
  }

  private String getUrl(
      final String pBase,
      final ObjectId pOrgId,
      final ObjectId pProjectId,
      final String pClusterName) {
    final StringBuilder sb = new StringBuilder(pBase);
    if (pOrgId != null) {
      sb.append("?orgId=").append(pOrgId);
      if (pProjectId != null) {
        sb.append("&groupId=").append(pProjectId);
        if (pClusterName != null) {
          sb.append("&clusterName=").append(pClusterName);
        }
      }
    }
    return sb.toString();
  }

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "projectName");
    _cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), "clusterName"));
    _clusterDescriptionDao.save(_cluster);
    _hostCluster1 = new HostCluster();
    _hostCluster1.setName(_cluster.getName());
    _hostCluster1.setClusterId(new ObjectId());
    _hostCluster1.setGroupId(_cluster.getGroupId());
    _hostCluster1.setShardingZone("US");
    _hostCluster1.setReplicaSetIds(
        Set.of(NDSDefaults.getReplicaSetNameForUnshardedCluster(_cluster)));
    _hostCluster1.setTypeCode(ClusterType.REPLICA_SET.getCode());
    _hostCluster1.setHostIds(Set.of("host1"));
    _hostCluster1.setActive(true);
    _hostClusterDao.save(_hostCluster1);

    _shardedCluster =
        new ShardedClusterDescription(
            NDSModelTestFactory.getShardedAWSClusterDescription(
                _group.getId(), "shardedClusterName", 2));
    _clusterDescriptionDao.save(_shardedCluster);
    _hostCluster2 = new HostCluster();
    _hostCluster2.setName(_shardedCluster.getName());
    _hostCluster2.setClusterId(new ObjectId());
    _hostCluster2.setGroupId(_shardedCluster.getGroupId());
    _hostCluster2.setShardingZone("US");
    _hostCluster2.setTypeCode(ClusterType.SHARDED_REPLICA_SET.getCode());
    _hostCluster2.setHostIds(Set.of("host2"));
    _hostCluster2.setActive(true);
    _hostClusterDao.save(_hostCluster2);

    _serverlessCluster =
        new ClusterDescription(
            NDSModelTestFactory.getDefaultServerlessClusterDescription(
                _group.getId(), "serverlessClusterName"));
    _clusterDescriptionDao.save(_serverlessCluster);

    _freeCluster =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new NDSModelTestFactory.TestFreeClusterDescriptionConfig()
                    .setGroupId(_group.getId())));
    _clusterDescriptionDao.save(_freeCluster);
    _groupSvc.ensureGroup(_group.getId());
    _groupOwner = MmsFactory.createGroupOwnerUser(_group);
    _groupMember =
        MmsFactory.createUserWithRoleInGroup(
            _group, getUniquifier() + "@example.com", Role.GROUP_READ_ONLY);
    _orgOwner = MmsFactory.createUserInOrg(_organization, getUniquifier() + "@example.com");
    _adminUser = MmsFactory.createReadOnlyAdminUser(_group);
    _tseUser = MmsFactory.createGlobalAtlasTSEUser(_group);
  }

  @Test
  public void testGetPaths() {
    final List<String> globalCommands =
        List.of(
            "/admin",
            "/admin/backup",
            "/admin/billing",
            "/admin/fleetAttributes",
            "/admin/internalMaintenance",
            "/admin/maintenance",
            "/admin/plans",
            "/admin/search",
            "/find",
            "/git",
            "/git/branch",
            "/git/commit",
            "/git/commits",
            "/help",
            "/orgs",
            "/orgs/testOrg/access",
            "/orgs/testOrg/activity",
            "/orgs/testOrg/alerts",
            "/orgs/testOrg/flags",
            "/orgs/testOrg/projects",
            "/orgs/testOrg/settings",
            "/orgs/testOrg/support",
            "/preferences");
    final List<String> orgCommands =
        List.of(
            "/flags",
            "/settings",
            "/activity",
            "/alerts",
            "/access",
            "/billing",
            "/admin/config",
            "/support",
            "/admin/config/org",
            "/orgs/testOrg/projects",
            "/orgs/testOrg/support",
            "/orgs/testOrg/settings",
            "/orgs/testOrg/flags",
            "/orgs/testOrg/activity",
            "/orgs/testOrg/alerts",
            "/orgs/testOrg/access",
            "/project",
            "/charts/projectName");

    final List<String> groupCommands =
        List.of(
            "/flags",
            "/admin/config/automation",
            "/admin/config/project",
            "/org",
            "/org/settings",
            "/org/flags",
            "/org/activity",
            "/org/alerts",
            "/org/access",
            "/splunk",
            "/admin/logs",
            "/support",
            "/settings",
            "/activity",
            "/alerts",
            "/access",
            "/admin/config",
            "/project",
            "/charts");

    final List<String> clusterCommands =
        List.of(
            "/backup",
            "/admin/config/cluster",
            "/admin/config/hardware",
            "/cluster",
            "/metrics",
            "/rtp",
            "/collections",
            "/profiler",
            "/advisor",
            "/connect",
            "/search");

    // no context
    {
      final JSONArray paths = doAuthedJsonArrayGet(_adminUser, GOTO_BASE, HttpStatus.SC_OK);
      assertNotNull(paths);
      var sortedPaths = paths.toList().stream().sorted().toList();
      for (int i = 0; i < globalCommands.size(); i++) {
        assertUrlEquals(globalCommands.get(i), (String) sortedPaths.get(i));
      }
    }

    // with org
    {
      final List<Object> pathsWithVariables =
          doAuthedJsonArrayGet(
                  _adminUser, getPathsUrl(_organization.getId(), null), HttpStatus.SC_OK)
              .toList();
      assertNotNull(pathsWithVariables);
      var paths =
          pathsWithVariables.stream().map((path) -> this.normalizeUrl(path.toString())).toList();
      // paths should include global commands
      assertTrue(paths.containsAll(globalCommands));
      // and org commands
      assertTrue(paths.containsAll(orgCommands));
      assertTrue(paths.contains("/project/projectName/overview"));
    }

    // with project
    {
      final List<Object> pathsWithVariables =
          doAuthedJsonArrayGet(
                  _adminUser, getPathsUrl(_organization.getId(), _group.getId()), HttpStatus.SC_OK)
              .toList();
      var paths =
          pathsWithVariables.stream().map((path) -> this.normalizeUrl(path.toString())).toList();
      assertNotNull(paths);
      // paths should include global commands
      assertTrue(paths.containsAll(globalCommands));
      // and org commands
      assertTrue(paths.containsAll(orgCommands));
      assertTrue(paths.contains("/project/projectName/overview"));
      // and group commands
      assertTrue(paths.containsAll(groupCommands));
      assertTrue(paths.contains("/cluster/clusterName/overview"));
      assertTrue(paths.contains("/cluster/clusterName/metrics"));
      assertTrue(paths.contains("/cluster/clusterName/collections"));
      assertTrue(paths.contains("/cluster/clusterName/profiler"));
      assertTrue(paths.contains("/cluster/clusterName/advisor"));
      assertTrue(paths.contains("/cluster/clusterName/backup"));
      assertTrue(paths.contains("/cluster/clusterName/connect"));
      // but not cluster commands
      assertFalse(CollectionUtils.containsAny(paths, clusterCommands));
    }

    // with cluster
    {
      final List<Object> pathsWithVariables =
          doAuthedJsonArrayGet(
                  _adminUser,
                  getPathsUrl(_organization.getId(), _group.getId(), _cluster.getName()),
                  HttpStatus.SC_OK)
              .toList();
      var paths =
          pathsWithVariables.stream().map((path) -> this.normalizeUrl(path.toString())).toList();
      assertNotNull(paths);
      // paths should include global commands
      assertTrue(paths.containsAll(globalCommands));
      // and org commands
      assertTrue(paths.containsAll(orgCommands));
      assertTrue(paths.contains("/project/projectName/overview"));
      // and group commands
      assertTrue(paths.containsAll(groupCommands));
      assertTrue(paths.contains("/cluster/clusterName/overview"));
      assertTrue(paths.contains("/cluster/clusterName/metrics"));
      assertTrue(paths.contains("/cluster/clusterName/rtp"));
      assertTrue(paths.contains("/cluster/clusterName/collections"));
      assertTrue(paths.contains("/cluster/clusterName/profiler"));
      assertTrue(paths.contains("/cluster/clusterName/advisor"));
      assertTrue(paths.contains("/cluster/clusterName/backup"));
      assertTrue(paths.contains("/cluster/clusterName/connect"));
      // and cluster commands
      assertTrue(paths.containsAll(clusterCommands));
    }

    // with tenant cluster
    {
      final List<Object> pathsWithVariables =
          doAuthedJsonArrayGet(
                  _adminUser,
                  getPathsUrl(_organization.getId(), _group.getId(), _freeCluster.getName()),
                  HttpStatus.SC_OK)
              .toList();
      var paths =
          pathsWithVariables.stream().map((path) -> this.normalizeUrl(path.toString())).toList();
      assertNotNull(paths);
      // paths should include global commands
      assertTrue(paths.containsAll(globalCommands));
      // and org commands
      assertTrue(paths.containsAll(orgCommands));
      assertTrue(paths.contains("/project/projectName/overview"));
      // and group commands
      assertTrue(paths.containsAll(groupCommands));
      assertTrue(paths.contains("/cluster/clusterName/overview"));
      assertTrue(paths.contains("/cluster/clusterName/metrics"));
      assertTrue(paths.contains("/cluster/clusterName/rtp"));
      assertTrue(paths.contains("/cluster/clusterName/collections"));
      assertTrue(paths.contains("/cluster/clusterName/profiler"));
      assertTrue(paths.contains("/cluster/clusterName/advisor"));
      assertTrue(paths.contains("/cluster/clusterName/backup"));
      assertTrue(paths.contains("/cluster/clusterName/connect"));
      // and cluster commands
      assertTrue(paths.containsAll(clusterCommands));
      // and mtm command
      assertTrue(paths.contains("/mtm"));
    }

    // with sharded cluster
    {
      final List<Object> pathsWithVariables =
          doAuthedJsonArrayGet(
                  _adminUser,
                  getPathsUrl(_organization.getId(), _group.getId(), _shardedCluster.getName()),
                  HttpStatus.SC_OK)
              .toList();
      var paths =
          pathsWithVariables.stream().map((path) -> this.normalizeUrl(path.toString())).toList();

      // Should not contain /rtp for current sharded cluster
      assertFalse(paths.contains("/rtp"));
      assertFalse(paths.contains("/cluster/shardedClusterName/rtp"));

      // Should not contain /advisor for sharded cluster
      assertFalse(paths.contains("/advisor"));
      assertFalse(paths.contains("/cluster/shardedClusterName/advisor"));

      // Should not contain /advisor for sharded cluster
      assertFalse(paths.contains("/profiler"));
      assertFalse(paths.contains("/cluster/shardedClusterName/profiler"));
    }
  }

  private void testOrgOrProjectLinkCommand(
      final String pCommand,
      final String pExpectedOrgUrlTemplate,
      final String pExpectedProjectUrlTemplate)
      throws Exception {
    // with no context
    {
      doAuthedJsonPostReturnHttp(
          _adminUser,
          getCommandUrl(pCommand, null, null),
          new BasicDBObject(),
          HttpStatus.SC_NOT_FOUND);
    }

    // with org
    {
      if (pExpectedOrgUrlTemplate == null) {
        doAuthedJsonPostReturnHttp(
            _adminUser,
            getCommandUrl(pCommand, _organization.getId(), null),
            new BasicDBObject(),
            HttpStatus.SC_NOT_FOUND);
      } else {
        final JSONObject response =
            doAuthedJsonPost(
                _adminUser,
                getCommandUrl(pCommand, _organization.getId(), null),
                new BasicDBObject());
        assertUrlEquals(
            String.format(pExpectedOrgUrlTemplate, _organization.getId()),
            response.getString("url"));
      }
    }

    // with project
    if (pExpectedProjectUrlTemplate != null) {
      final JSONObject response =
          doAuthedJsonPost(
              _adminUser,
              getCommandUrl(pCommand, _organization.getId(), _group.getId()),
              new BasicDBObject());
      assertUrlEquals(
          String.format(pExpectedProjectUrlTemplate, _group.getId()), response.getString("url"));
    }
  }

  private void testProjectLinkCommand(
      final String pCommand, final String pExpectedProjectUrlTemplate) throws Exception {
    testOrgOrProjectLinkCommand(pCommand, null, pExpectedProjectUrlTemplate);
  }

  private void testOrgLinkCommand(final String pCommand, final String pUrlTemplate)
      throws Exception {
    testOrgOrProjectLinkCommand(pCommand, pUrlTemplate, null);
  }

  private void testClusterLinkCommand(
      final String pCommand, final String pExpectedClusterUrlTemplate) throws Exception {
    // with no context
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand, null, null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with org only
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand, _organization.getId(), null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with project only
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand, _organization.getId(), _group.getId()),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with bad cluster name
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand, _organization.getId(), _group.getId(), "BAD_CLUSTER"),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // success
    {
      final JSONObject response =
          doAuthedJsonPost(
              _adminUser,
              getCommandUrl(pCommand, _organization.getId(), _group.getId(), _cluster.getName()),
              new BasicDBObject());
      assertUrlEquals(
          String.format(pExpectedClusterUrlTemplate, _group.getId(), _cluster.getName()),
          response.getString("url"));
    }
  }

  private void testSupportCommand(final String pCommand, final String pExpectedSupportUrlTemplate) {
    if (pCommand.contains("project")) {
      final JSONObject response =
          doAuthedJsonPost(
              _adminUser,
              getCommandUrl(pCommand, _organization.getId(), null),
              new BasicDBObject());
      assertUrlEquals(
          String.format(pExpectedSupportUrlTemplate, _group.getId()), response.getString("url"));
    } else {
      final JSONObject response =
          doAuthedJsonPost(_adminUser, getCommandUrl(pCommand), new BasicDBObject());
      assertUrlEquals(
          String.format(pExpectedSupportUrlTemplate, _organization.getId()),
          response.getString("url"));
    }
  }

  private void testOrgCommandWithPathParameter(
      final String pCommand, final String pExpectedOrgUrlTemplate) {
    final JSONObject response =
        doAuthedJsonPost(_adminUser, getCommandUrl(pCommand), new BasicDBObject());

    assertUrlEquals(
        String.format(pExpectedOrgUrlTemplate, _organization.getId()), response.getString("url"));
  }

  private void testOrgCommandWithGroupContext(
      final String pCommand, final String pExpectedOrgUrlTemplate) {
    final JSONObject response =
        doAuthedJsonPost(
            _adminUser,
            getCommandUrl(pCommand, _organization.getId(), _group.getId()),
            new BasicDBObject());
    assertUrlEquals(
        String.format(pExpectedOrgUrlTemplate, _organization.getId()), response.getString("url"));
  }

  @Test
  public void testGoToSettings() throws Exception {
    testOrgOrProjectLinkCommand(
        "/settings", "/v2#/org/%s/settings/general", "/v2/%s#/settings/groupSettings");
  }

  @Test
  public void testGoToFlags() throws Exception {
    testOrgOrProjectLinkCommand("/flags", "/v2#/org/%s/settings/betaFeatures", null);
  }

  @Test
  public void testGoToActivity() throws Exception {
    testOrgOrProjectLinkCommand("/activity", "/v2#/org/%s/activity/list", "/v2/%s#/activity");
  }

  @Test
  public void testGoToAlerts() throws Exception {
    testOrgOrProjectLinkCommand("/alerts", "/v2#/org/%s/alerts/list", "/v2/%s#/alerts");
  }

  @Test
  public void testGoToAccess() throws Exception {
    testOrgOrProjectLinkCommand("/access", "/v2#/org/%s/access/users", "/v2/%s#/access");
  }

  @Test
  public void testGoToOrg() throws Exception {
    testOrgLinkCommand("/org", "/v2#/org/%s");
  }

  @Test
  public void testGoToBilling() throws Exception {
    testOrgLinkCommand("/billing", "/v2#/org/%s/billing/overview");
  }

  @Test
  public void testGoToOrgSettings() {
    testOrgCommandWithPathParameter(
        String.format("/orgs/%s/settings", _organization.getName()),
        "/v2#/org/%s/settings/general");
  }

  @Test
  public void testGoToOrgFlags() {
    final String expectedUrlTemplate = "/v2#/org/%s/settings/betaFeatures";
    testOrgCommandWithPathParameter(
        String.format("/orgs/%s/flags", _organization.getName()), expectedUrlTemplate);
    testOrgCommandWithGroupContext("/org/flags", expectedUrlTemplate);
  }

  @Test
  public void testGoToOrgActivity() {
    final String expectedUrlTemplate = "/v2#/org/%s/activity/list";
    testOrgCommandWithPathParameter(
        String.format("/orgs/%s/activity", _organization.getName()), expectedUrlTemplate);
    testOrgCommandWithGroupContext("/org/activity", expectedUrlTemplate);
  }

  @Test
  public void testGoToOrgAlerts() {
    final String expectedUrlTemplate = "/v2#/org/%s/alerts/list";
    testOrgCommandWithPathParameter(
        String.format("/orgs/%s/alerts", _organization.getName()), expectedUrlTemplate);
    testOrgCommandWithGroupContext("/org/alerts", expectedUrlTemplate);
  }

  @Test
  public void testGoToOrgAccess() {
    final String expectedUrlTemplate = "/v2#/org/%s/access/users";
    testOrgCommandWithPathParameter(
        String.format("/orgs/%s/access", _organization.getName()), "/v2#/org/%s/access/users");
    testOrgCommandWithGroupContext("/org/access", expectedUrlTemplate);
  }

  @Test
  public void testGoToCurrentProject() throws Exception {
    testOrgOrProjectLinkCommand("/project", String.format("/v2/%s", _group.getId()), "/v2/%s");
  }

  @Test
  public void testGoToCluster() throws Exception {
    final String pCommand = "/cluster";
    // with no context
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand + "/something/overview", null, null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with only org
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand + "/something", _organization.getId(), null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with project but bad cluster name
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand + "/doesnotexist/overview", _organization.getId(), _group.getId()),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // ok
    final JSONObject response =
        doAuthedJsonPost(
            _adminUser,
            getCommandUrl(
                pCommand + "/" + _cluster.getName() + "/overview",
                _organization.getId(),
                _group.getId()),
            new BasicDBObject());
    assertUrlEquals(
        String.format("/v2/%s#/clusters/detail/%s", _group.getId(), _cluster.getName()),
        response.getString("url"));
  }

  @Test
  public void testGoToProjectName() throws Exception {
    // with no context
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl("/project/something/overview", null, null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with org but bad project name
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl("/project/notaproject/overview", _organization.getId(), null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // ok
    final JSONObject response =
        doAuthedJsonPost(
            _adminUser,
            getCommandUrl(
                "/project/" + _group.getName() + "/overview", _organization.getId(), null),
            new BasicDBObject());
    assertUrlEquals("/v2/" + _group.getId(), response.getString("url"));
  }

  private void testGoToUrl(final String pUrl, final String pExpectedUrl) {
    final JSONObject response = doAuthedJsonPost(_adminUser, pUrl, new BasicDBObject());
    assertUrlEquals(pExpectedUrl, response.getString("url"));
  }

  private void testGoToSomething(final Object pSomething, final String pExpectedUrl) {
    testGoToUrl(
        getCommandUrl("/" + pSomething.toString(), _organization.getId(), _group.getId()),
        pExpectedUrl);
  }

  @Test
  public void testGoToSomething() throws Exception {
    // invalid
    doAuthedJsonPostReturnHttp(
        _adminUser, getCommandUrl("/nonsense"), new BasicDBObject(), HttpStatus.SC_NOT_FOUND);

    // group id
    testGoToSomething(_group.getId(), "/v2/" + _group.getId());

    // org id
    testGoToSomething(_organization.getId(), "/v2#/org/" + _organization.getId());

    // cluster name
    testGoToSomething(
        _cluster.getName(),
        String.format("/v2/%s#/clusters/detail/%s", _group.getId(), _cluster.getName()));

    // project name
    testGoToSomething(_group.getName(), "/v2/" + _group.getId());

    // cluster id
    testGoToSomething(
        _cluster.getUniqueId(),
        String.format("/v2/%s#/clusters/detail/%s", _group.getId(), _cluster.getName()));
  }

  @Test
  public void testGoToOrgs() {
    testGoToUrl(getCommandUrl("/orgs"), "/v2#/preferences/organizations");
  }

  @Test
  public void testGoToAdmin() {
    testGoToUrl(getCommandUrl("/admin"), "/v2/admin");
  }

  @Test
  public void testGoToAdminSearch() {
    // no context
    testGoToUrl(getCommandUrl("/admin/search"), "/v2/admin#/atlas/search");

    // with group
    testGoToUrl(
        getCommandUrl("/admin/search", _organization.getId(), _group.getId()),
        "/v2/admin#/atlas/search?search=" + _group.getId());

    // with argument
    testGoToUrl(getCommandUrl("/admin/search/query"), "/v2/admin#/atlas/search?search=query");
  }

  @Test
  public void testGoToPlans() {
    // no context
    testGoToUrl(getCommandUrl("/admin/plans"), "/v2/admin#/atlas/planSearch");

    // with group
    testGoToUrl(
        getCommandUrl("/admin/plans", _organization.getId(), _group.getId()),
        String.format("/v2/admin#/atlas/planSearch?groupId=%s&limit=25", _group.getId()));
  }

  @Test
  public void testGoToFind() {
    // no context
    testGoToUrl(getCommandUrl("/find"), "https://search.corp.mongodb.com");

    // with group
    testGoToUrl(
        getCommandUrl("/find", _organization.getId(), _group.getId()),
        String.format("https://search.corp.mongodb.com/#q=%s&sort=relevancy", _group.getId()));
  }

  @Test
  public void testGoToLogs() throws Exception {
    // no context
    doAuthedJsonPostReturnHttp(
        _adminUser, getCommandUrl("/admin/logs"), new BasicDBObject(), HttpStatus.SC_NOT_FOUND);

    // with group
    testGoToUrl(
        getCommandUrl("/admin/logs", _organization.getId(), _group.getId()),
        String.format("/v2/admin#/atlas/logs?group=%s", _group.getId()));
  }

  @Test
  public void testGoToAdminBackup() {
    // no context
    testGoToUrl(getCommandUrl("/admin/backup"), "/v2/admin#/backupv2/jobs");
  }

  @Test
  public void testGoToAdminPlan() {
    testGoToUrl(getCommandUrl("/admin/plans/PLAN_ID"), "/v2/admin#/atlas/planGraph?planId=PLAN_ID");
  }

  @Test
  public void testGoToHelp() {
    testGoToUrl(getCommandUrl("/help"), "https://wiki.corp.mongodb.com/display/MMS/Atlas+goto");
  }

  @Test
  public void testGoToMaintenance() throws Exception {
    // no context
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl("/admin/maintenance"),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with group only
    testGoToUrl(
        getCommandUrl("/admin/maintenance", _organization.getId(), _group.getId()),
        String.format("/admin/nds/groups/%s/maintenanceHistories", _group.getId()));

    // with group and cluster
    testGoToUrl(
        getCommandUrl(
            "/admin/maintenance", _organization.getId(), _group.getId(), _cluster.getName()),
        String.format(
            "/admin/nds/groups/%s/clusters/%s/maintenanceHistories",
            _group.getId(), _cluster.getName()));
  }

  @Test
  public void testGoToPreferences() {
    testGoToUrl(getCommandUrl("/preferences"), "/v2#/preferences/personalization");
  }

  @Test
  public void testGoToAutomationConfig() throws Exception {
    testProjectLinkCommand("/admin/config/automation", "/v2/%s#/deploymentAdmin/raw");
  }

  @Test
  public void testGoToOrgConfig() throws Exception {
    testOrgLinkCommand("/admin/config/org", "/admin/nds/orgs/%s");
  }

  @Test
  public void testGoToProjectConfig() throws Exception {
    testProjectLinkCommand("/admin/config/project", "/admin/nds/groups/%s");
  }

  @Test
  public void testGoToClusterConfig() throws Exception {
    testClusterLinkCommand(
        "/admin/config/cluster", "/admin/nds/groups/%s/clusterDescriptions/%s?pretty=true");
  }

  @Test
  public void testGoToHardwareConfig() throws Exception {
    testClusterLinkCommand(
        "/admin/config/hardware",
        "/admin/nds/groups/%s/clusterDescriptions/%s/replicaSetHardware?pretty=true");
  }

  @Test
  public void testGoToConfig() throws Exception {
    // no context
    doAuthedJsonPostReturnHttp(
        _adminUser, getCommandUrl("/admin/config"), new BasicDBObject(), HttpStatus.SC_NOT_FOUND);

    // with org only
    testGoToUrl(
        getCommandUrl("/admin/config", _organization.getId(), null),
        String.format("/admin/nds/orgs/%s", _organization.getId()));

    // with group only
    testGoToUrl(
        getCommandUrl("/admin/config", _organization.getId(), _group.getId()),
        String.format("/admin/nds/groups/%s", _group.getId()));

    // with cluster
    testGoToUrl(
        getCommandUrl("/admin/config", _organization.getId(), _group.getId(), _cluster.getName()),
        String.format(
            "/admin/nds/groups/%s/clusterDescriptions/%s?pretty=true",
            _group.getId(), _cluster.getName()));
  }

  @Test
  public void testGoToCharts() throws Exception {
    final String pCommand = "/charts";
    // with no context
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(pCommand, null, null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // ok
    final JSONObject response =
        doAuthedJsonPost(
            _adminUser,
            getCommandUrl(pCommand, _organization.getId(), _group.getId()),
            new BasicDBObject());
    assertUrlEquals(String.format("/v2/%s#/charts", _group.getId()), response.getString("url"));
  }

  @Test
  public void testGoToSupport() {
    // project
    testSupportCommand("/project/projectName/support", "/v2/%s#/info/support");

    // org
    testSupportCommand(
        String.format("/orgs/%s/support", _organization.getName()), "/v2#/org/%s/support");

    // project with context
    testGoToUrl(
        getCommandUrl("/support", _organization.getId(), _group.getId()),
        String.format("/v2/%s#/info/support", _group.getId()));

    // org with context
    testGoToUrl(
        getCommandUrl("/support", _organization.getId(), null),
        String.format("/v2#/org/%s/support", _organization.getId()));
  }

  private void testHostClusterLinkCommand(final String pCommand, final String pExpectedUrlTemplate)
      throws Exception {
    // cluster in context
    testClusterLinkCommand(pCommand, pExpectedUrlTemplate);
    // cluster in URL
    testProjectLinkCommand(
        String.format("/cluster/%s%s", _cluster.getName(), pCommand), pExpectedUrlTemplate);
  }

  @Test
  public void testGoToClusterMetrics() throws Exception {
    // dedicated cluster
    String command = String.format("/cluster/%s/metrics", _cluster.getName());
    testProjectLinkCommand(command, "/v2/%s#/host/replicaSet/" + _hostCluster1.getClusterId());

    // sharded cluster
    command = String.format("/cluster/%s/metrics", _shardedCluster.getName());
    testProjectLinkCommand(command, "/v2/%s#/host/cluster/" + _hostCluster2.getClusterId());

    // serverless cluster
    command = String.format("/cluster/%s/metrics", _serverlessCluster.getName());
    testProjectLinkCommand(
        command, "/v2/%s#/serverless/monitoring/" + _serverlessCluster.getName());
  }

  @Test
  public void testGoToClusterRealTimePanel() throws Exception {
    testHostClusterLinkCommand(
        "/rtp", "/v2/%s#/metrics/replicaSet/" + _hostCluster1.getClusterId() + "/realtime/panel");
  }

  @Test
  public void testGoToClusterCollections() throws Exception {
    testHostClusterLinkCommand(
        "/collections", "/v2/%s#/metrics/replicaSet/" + _hostCluster1.getClusterId() + "/explorer");
  }

  @Test
  public void testGoToClusterProfiler() throws Exception {
    testHostClusterLinkCommand(
        "/profiler", "/v2/%s#/metrics/replicaSet/" + _hostCluster1.getClusterId() + "/profiler");
  }

  @Test
  public void testGoToClusterAdvisor() throws Exception {
    testHostClusterLinkCommand(
        "/advisor", "/v2/%s#/metrics/replicaSet/" + _hostCluster1.getClusterId() + "/advisor");
  }

  @Test
  public void testGoToClusterConnect() throws Exception {
    testHostClusterLinkCommand(
        "/connect", "/v2/%s#/clusters/connect?clusterId=" + _hostCluster1.getName());
  }

  @Test
  public void testGoToClusterBackup() throws Exception {
    // replica set
    testHostClusterLinkCommand("/backup", "/v2/%s#/clusters/backup/" + _hostCluster1.getName());

    // serverless
    final String command = String.format("/cluster/%s/backup", _serverlessCluster.getName());
    testProjectLinkCommand(command, "/v2/%s#/serverless/backup/" + _serverlessCluster.getName());
  }

  @Test
  public void testGoToClusterSearch() throws Exception {
    final String expectedUrlTemplate = "/v2/%s#/clusters/atlasSearch/";
    // replica set
    testHostClusterLinkCommand("/search", expectedUrlTemplate + _hostCluster1.getName());

    // sharded
    final String command = String.format("/cluster/%s/search", _shardedCluster.getName());
    testProjectLinkCommand(command, expectedUrlTemplate + _shardedCluster.getName());
  }

  @Test
  public void testGoToSsh() throws Exception {
    final String command = "/admin/ssh";
    // with no context
    doAuthedJsonPostReturnHttp(
        _tseUser, getCommandUrl(command, null, null), new BasicDBObject(), HttpStatus.SC_NOT_FOUND);

    // with org only
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl(command, _organization.getId(), null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with project only
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl(command, _organization.getId(), _group.getId()),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with bad cluster name
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl(command, _organization.getId(), _group.getId(), "BAD_CLUSTER"),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // shared
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl(command, _organization.getId(), _group.getId(), _freeCluster.getName()),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // requires TSE access or greater
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(command, _organization.getId(), _group.getId(), _cluster.getName()),
        new BasicDBObject(),
        HttpStatus.SC_FORBIDDEN);

    // success
    {
      final String hostname =
          URLEncoder.encode(
              HostUtils.extractHostname(_cluster.getFirstMongoDBUriHost()), StandardCharsets.UTF_8);
      final String expectedUrl = "/v2/admin#/atlas/serverAccess?hostname=" + hostname + "&all=true";
      final JSONObject response =
          doAuthedJsonPost(
              _tseUser,
              getCommandUrl(command, _organization.getId(), _group.getId(), _cluster.getName()),
              new BasicDBObject());
      assertUrlEquals(expectedUrl, response.getString("url"));
    }
  }

  @Test
  public void testGoToSsh_singleClusterProject() throws SvcException {
    // set up group with only one cluster
    final Group group = MmsFactory.createGroup(_organization, "onlyOneCluster");
    _groupSvc.ensureGroup(group.getId());
    final AppUser tseUser = MmsFactory.createGlobalAtlasTSEUser(group);
    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), "theCluster"));
    _clusterDescriptionDao.save(cluster);

    final String command = "/admin/ssh";

    final String hostname =
        URLEncoder.encode(
            HostUtils.extractHostname(cluster.getFirstMongoDBUriHost()), StandardCharsets.UTF_8);
    final String expectedUrl = "/v2/admin#/atlas/serverAccess?hostname=" + hostname + "&all=true";
    // works at group level
    final JSONObject response =
        doAuthedJsonPost(
            tseUser,
            getCommandUrl(command, _organization.getId(), group.getId()),
            new BasicDBObject());
    assertUrlEquals(expectedUrl, response.getString("url"));
  }

  @Test
  public void testGoToSshCluster() throws Exception {
    final String command = "/admin/ssh/" + _cluster.getName();
    // with no context
    doAuthedJsonPostReturnHttp(
        _tseUser, getCommandUrl(command, null, null), new BasicDBObject(), HttpStatus.SC_NOT_FOUND);

    // with org only
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl(command, _organization.getId(), null),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // with bad cluster name
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl("/admin/ssh/badCluster", _organization.getId(), _group.getId()),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // shared
    doAuthedJsonPostReturnHttp(
        _tseUser,
        getCommandUrl(
            "/admin/ssh/" + _freeCluster.getName(), _organization.getId(), _group.getId()),
        new BasicDBObject(),
        HttpStatus.SC_NOT_FOUND);

    // requires TSE access or greater
    doAuthedJsonPostReturnHttp(
        _adminUser,
        getCommandUrl(command, _organization.getId(), _group.getId()),
        new BasicDBObject(),
        HttpStatus.SC_FORBIDDEN);

    // success
    {
      final String hostname =
          URLEncoder.encode(
              HostUtils.extractHostname(_cluster.getFirstMongoDBUriHost()), StandardCharsets.UTF_8);
      final String expectedUrl = "/v2/admin#/atlas/serverAccess?hostname=" + hostname + "&all=true";
      final JSONObject response =
          doAuthedJsonPost(
              _tseUser,
              getCommandUrl(command, _organization.getId(), _group.getId()),
              new BasicDBObject());
      assertUrlEquals(expectedUrl, response.getString("url"));
    }
  }
}
