package com.xgen.svc.nds.res;

import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus.FieldDefs;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus.SampleDataset;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.List;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSSampleDatasetLoadResourceIntTests extends JUnit5BaseResourceTest {
  private static final String BASE_URL = "/nds/sampleDatasetLoad/%s";

  private final String _clusterName = "clusterName";

  @Inject private NDSGroupSvc _groupSvc;

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  private Group _group;
  private AppUser _user;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    _group = MmsFactory.createGroupWithNDSPlan();
    _groupSvc.ensureGroup(_group.getId());
    _user = MmsFactory.createUser(_group);

    MmsFactory.createCluster(
        _clusterName,
        asList(MmsFactory.createReplicaSet_V_4_2(_group.getId(), "hostCluster", false)));

    _clusterDescriptionDao.insertReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), _clusterName));
  }

  @Test
  public void testLoadSampleDatasetAndGetLoadStatus() throws Exception {
    // Test load and successful retrieval of load status
    final JSONObject sampleDatasetLoadStatusObject = new JSONObject();
    sampleDatasetLoadStatusObject.put(FieldDefs.CLUSTER_NAME, _clusterName);
    sampleDatasetLoadStatusObject.put(FieldDefs.DATASET, SampleDataset.ANALYTICS.getName());

    final JSONObject doc = verifySuccessPostAndBasicStatusDocFields(sampleDatasetLoadStatusObject);
    assertEquals(SampleDataset.ANALYTICS.getName(), doc.getString(FieldDefs.DATASET));
  }

  @Test
  public void testLoadSelectedSampleDatasetsAndGetLoadStatus() throws Exception {
    // Test load and successful retrieval of load status
    List<String> selectedDatasets =
        List.of(SampleDataset.ANALYTICS.getName(), SampleDataset.MOVIES.getName());
    final JSONObject sampleDatasetLoadStatusObject = new JSONObject();
    sampleDatasetLoadStatusObject.put(FieldDefs.CLUSTER_NAME, _clusterName);
    sampleDatasetLoadStatusObject.put(FieldDefs.SELECTED_DATASETS, selectedDatasets);

    final JSONObject doc = verifySuccessPostAndBasicStatusDocFields(sampleDatasetLoadStatusObject);
    assertEquals(selectedDatasets, doc.getJSONArray(FieldDefs.SELECTED_DATASETS).toList());
  }

  /**
   * Helper method to make a post request to load sample dataset. It also verifies a {@link
   * SampleDatasetLoadStatus} db doc is successfully inserted.
   *
   * @param sampleDatasetLoadStatusObject request body
   * @return newly created {@link SampleDatasetLoadStatus} db object
   */
  private JSONObject verifySuccessPostAndBasicStatusDocFields(
      JSONObject sampleDatasetLoadStatusObject) {
    doAuthedJsonPost(
        _user,
        String.format(BASE_URL, _group.getId()),
        sampleDatasetLoadStatusObject,
        HttpStatus.SC_OK);

    final JSONArray result =
        doAuthedJsonArrayGet(_user, String.format(BASE_URL, _group.getId()), HttpStatus.SC_OK);

    assertEquals(1, result.length());

    final JSONObject doc = result.getJSONObject(0);

    assertFalse(doc.isNull(SampleDatasetLoadStatus.FieldDefs.ID));
    assertEquals(
        _group.getId().toString(), doc.getString(SampleDatasetLoadStatus.FieldDefs.GROUP_ID));
    assertEquals(_clusterName, doc.getString(SampleDatasetLoadStatus.FieldDefs.CLUSTER_NAME));
    assertFalse(doc.isNull(SampleDatasetLoadStatus.FieldDefs.CREATE_DATE));
    assertTrue(doc.isNull(SampleDatasetLoadStatus.FieldDefs.COMPLETE_DATE));
    assertEquals(
        SampleDatasetLoadStatus.State.WORKING.name(),
        doc.getString(SampleDatasetLoadStatus.FieldDefs.STATE));
    assertTrue(doc.isNull(SampleDatasetLoadStatus.FieldDefs.ERROR_MESSAGE));
    return doc;
  }
}
