load("//server/src/test:rules.bzl", "library_package", "test_package")

# Non-test classes
library_package(
    name = "nonTestLibrary",
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/activity/_public/svc",
        "//server/src/main/com/xgen/cloud/atm/core/_public/svc",
        "//server/src/main/com/xgen/cloud/atm/publish/_public/svc",
        "//server/src/main/com/xgen/cloud/common/agent/_public/model",
        "//server/src/main/com/xgen/cloud/common/appsettings/_public/svc",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/deployment/_public/model",
        "//server/src/main/com/xgen/cloud/group/_public/model",
        "//server/src/main/com/xgen/cloud/group/_public/model/activity",
        "//server/src/main/com/xgen/cloud/monitoring/common",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/metrics/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_public/model",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/fts/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/fts/_public/model",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade/_private/dao",
        "//server/src/main/com/xgen/cloud/organization/_public/model",
        "//server/src/main/com/xgen/cloud/search/common/_public/util",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/util",
        "//server/src/main/com/xgen/cloud/search/decoupled/external/_public/svc",
        "//server/src/main/com/xgen/cloud/user/_public/model",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/test/com/xgen/cloud/services/payments/modules/paymentMethod/common",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/testlib/junit5/extensions/guicetest",
        "//server/src/unit/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/unit/com/xgen/svc/atm",
        "//server/src/unit/com/xgen/svc/mms/svc/metrics",
        "//server/src/unit/com/xgen/svc/mms/util/serverless",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)

ADMIN_TEST_LIBRARY = [
    "NDSAdminResourceIntTests.java",
    "NDSAdminDashboardResourceIntTests.java",
]

CLUSTERS_TEST_LIBRARY = [
    "ChefCallbackResourceIntTests.java",
    "CloudChefConfResourceIntTests.java",
    "ClusterDescriptionResourceIntTests.java",
    "ClusterOutageSimulationResourceIntTests.java",
    "DataValidationResourceIntTests.java",
    "FlexClusterDescriptionResourceIntTests.java",
    "GoToResourceIntTests.java",
    "LogIngestionResourceIntTests.java",
    "NDSAdminDataPlaneAccessRequestResourceIntTests.java",
    "NDSAdminIFRResourceIntTests.java",
    "NDSExternalMaintenanceResourceIntTests.java",
    "NDSGroupResourceIntTests.java",
    "NDSPeerResourceIntTests.java",
    "NDSProxyResourceIntTests.java",
    "RegionalOutageResourceIntTests.java",
    "RollingResyncResourceIntTests.java",
    "TenantUpgradeResourceIntTests.java",
    "TenantUpgradeToServerlessResourceIntTests.java",
]

test_package(
    name = "TestLibrary",
    srcs = glob(
        ["*IntTests.java"],
        exclude = ["*ExternalIntTests.java"] + ADMIN_TEST_LIBRARY + CLUSTERS_TEST_LIBRARY,
    ),
    data = ["//server/scripts/nds:ssh_keys"],
    deny_warnings = True,
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/mms/res/admin",
        "//server/src/main/com/xgen/svc/mms/res/common",
    ],
    deps = [
        ":nonTestLibrary",
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/access/authn",
        "//server/src/main/com/xgen/cloud/access/authz/_public/svc",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/agent",
        "//server/src/main/com/xgen/cloud/apiuser",
        "//server/src/main/com/xgen/cloud/appconfig/_public/config",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/core/_private/dao",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/auditinfosvc",
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/brs/core/_private/dao",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/dao/codec",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/entity",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/http/url",
        "//server/src/main/com/xgen/cloud/common/jackson",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/logging",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/system",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/cps/agent/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/pit",
        "//server/src/main/com/xgen/cloud/cps/pit/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/externalanalyticsjobhandlers",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/federation",
        "//server/src/main/com/xgen/cloud/fts/activity",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/common/_public/model",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/metrics/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/accesstransparency",
        "//server/src/main/com/xgen/cloud/nds/accesstransparency/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/admin",
        "//server/src/main/com/xgen/cloud/nds/admin/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/agentApiKeys",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/common",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/context",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/context/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/predictive",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/checkmetadataconsistency",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datalake/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/datavalidation",
        "//server/src/main/com/xgen/cloud/nds/datavalidation/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dbcheck",
        "//server/src/main/com/xgen/cloud/nds/dbcheck/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/exmaintenance",
        "//server/src/main/com/xgen/cloud/nds/exmaintenance/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/ftdc",
        "//server/src/main/com/xgen/cloud/nds/fts",
        "//server/src/main/com/xgen/cloud/nds/fts/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/leakeditem/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/logingestion",
        "//server/src/main/com/xgen/cloud/nds/logingestion/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/metrics/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/module",
        "//server/src/main/com/xgen/cloud/nds/monitoring",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/settings",
        "//server/src/main/com/xgen/cloud/nds/planning/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao/admin",
        "//server/src/main/com/xgen/cloud/nds/replicasethardware",
        "//server/src/main/com/xgen/cloud/nds/resourcepolicy",
        "//server/src/main/com/xgen/cloud/nds/rollingresync",
        "//server/src/main/com/xgen/cloud/nds/rollingresync/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/sampledataset",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/serverless/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/model",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/svc",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/view",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/streams/_public/model",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/nds/tenant/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/vmimage",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/search/api",
        "//server/src/main/com/xgen/cloud/search/common",
        "//server/src/main/com/xgen/cloud/search/decoupled/api",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/config",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model/aws",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/external/_public/svc",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/main/com/xgen/module/federation/dao",
        "//server/src/main/com/xgen/module/liveimport/common",
        "//server/src/main/com/xgen/module/liveimport/dao",
        "//server/src/main/com/xgen/module/liveimport/model",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/model/auth",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/svc/pausefreetiermonitoring",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/test/com/xgen/cloud/activity:utils",
        "//server/src/test/com/xgen/cloud/billingplatform/util",
        "//server/src/test/com/xgen/cloud/nds/project/_public/svc/testutil",
        "//server/src/test/com/xgen/cloud/nds/resourcepolicy/util",
        "//server/src/test/com/xgen/cloud/nds/spothealthcheck/util",
        "//server/src/test/com/xgen/cloud/search/decoupled/test",
        "//server/src/test/com/xgen/cloud/services/payments/modules/paymentMethod/common",
        "//server/src/test/com/xgen/module/common/planner/inttestimpls",
        "//server/src/test/com/xgen/svc/brs",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/mms/api/res",
        "//server/src/test/com/xgen/svc/mms/util",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/free/util",
        "//server/src/test/com/xgen/svc/nds/res/util",
        "//server/src/test/com/xgen/svc/nds/serverless/util",
        "//server/src/test/com/xgen/svc/nds/svc/adl:utils",
        "//server/src/test/com/xgen/svc/nds/svc/onlinearchive:utils",
        "//server/src/test/com/xgen/svc/nds/tenant/planner",
        "//server/src/test/com/xgen/svc/nds/tenant/util",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model:cloudProviderTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/datalake/_public/model:datalakeTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/unit/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/unit/com/xgen/svc/atm",
        "//server/src/unit/com/xgen/svc/core",
        "//server/src/unit/com/xgen/svc/mms/svc/metrics",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/mms/util/serverless",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//server/src/unit/com/xgen/svc/nds/serverless/model:serverlessTestFactory",
        "//third_party:driverwrappers",
        "@com_xgen_mdb_idl//:mhouse_definitions",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:com_amazonaws_aws_java_sdk_kms",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_amazonaws_aws_java_sdk_sts",
        "@maven//:com_google_api_grpc_proto_google_iam_admin_v1",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:com_google_inject_guice",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:junit_junit",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_bouncycastle_bc_fips",
        "@maven//:org_bouncycastle_bcpkix_fips",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_skyscreamer_jsonassert",
    ],
)

test_package(
    name = "AdminTestLibrary",
    srcs = ADMIN_TEST_LIBRARY,
    data = ["//server/scripts/nds:ssh_keys"],
    deny_warnings = True,
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/mms/res/admin",
        "//server/src/main/com/xgen/svc/mms/res/common",
    ],
    deps = [
        ":nonTestLibrary",
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/access/authz/_public/svc",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/agent",
        "//server/src/main/com/xgen/cloud/apiuser",
        "//server/src/main/com/xgen/cloud/appconfig/_public/config",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/core/_private/dao",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/brs/core/_private/dao",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/dao/codec",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/entity",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/http/url",
        "//server/src/main/com/xgen/cloud/common/jackson",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/logging",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/system",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/cps/agent/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/pit",
        "//server/src/main/com/xgen/cloud/cps/pit/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/federation",
        "//server/src/main/com/xgen/cloud/fts/activity",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/common/_public/model",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/metrics/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/accesstransparency",
        "//server/src/main/com/xgen/cloud/nds/accesstransparency/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/admin",
        "//server/src/main/com/xgen/cloud/nds/admin/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/common",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/context",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/context/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/checkmetadataconsistency",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/cron/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datalake/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/datavalidation",
        "//server/src/main/com/xgen/cloud/nds/datavalidation/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dbcheck",
        "//server/src/main/com/xgen/cloud/nds/dbcheck/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/exmaintenance",
        "//server/src/main/com/xgen/cloud/nds/exmaintenance/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/ftdc",
        "//server/src/main/com/xgen/cloud/nds/fts",
        "//server/src/main/com/xgen/cloud/nds/fts/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/leakeditem/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/logingestion",
        "//server/src/main/com/xgen/cloud/nds/logingestion/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/maintenance",
        "//server/src/main/com/xgen/cloud/nds/module",
        "//server/src/main/com/xgen/cloud/nds/monitoring",
        "//server/src/main/com/xgen/cloud/nds/mpa",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/planning/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao/admin",
        "//server/src/main/com/xgen/cloud/nds/replicasethardware",
        "//server/src/main/com/xgen/cloud/nds/resourcepolicy",
        "//server/src/main/com/xgen/cloud/nds/rollingresync",
        "//server/src/main/com/xgen/cloud/nds/rollingresync/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/sampledataset",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/serverless/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/model",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/streams/_public/model",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/nds/tenant/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/vmimage",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/search/api",
        "//server/src/main/com/xgen/cloud/search/common",
        "//server/src/main/com/xgen/cloud/search/decoupled/api",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/config",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model/aws",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/external/_public/svc",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/main/com/xgen/module/federation/dao",
        "//server/src/main/com/xgen/module/liveimport/common",
        "//server/src/main/com/xgen/module/liveimport/dao",
        "//server/src/main/com/xgen/module/liveimport/model",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/model/auth",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/svc/pausefreetiermonitoring",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/test/com/xgen/cloud/activity:utils",
        "//server/src/test/com/xgen/cloud/billingplatform/util",
        "//server/src/test/com/xgen/cloud/nds/project/_public/svc/testutil",
        "//server/src/test/com/xgen/cloud/nds/resourcepolicy/util",
        "//server/src/test/com/xgen/cloud/nds/spothealthcheck/util",
        "//server/src/test/com/xgen/cloud/search/decoupled/test",
        "//server/src/test/com/xgen/cloud/services/payments/modules/paymentMethod/common",
        "//server/src/test/com/xgen/module/common/planner/inttestimpls",
        "//server/src/test/com/xgen/svc/brs",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/mms/api/res",
        "//server/src/test/com/xgen/svc/mms/util",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/free/util",
        "//server/src/test/com/xgen/svc/nds/res/util",
        "//server/src/test/com/xgen/svc/nds/serverless/util",
        "//server/src/test/com/xgen/svc/nds/svc/adl:utils",
        "//server/src/test/com/xgen/svc/nds/svc/onlinearchive:utils",
        "//server/src/test/com/xgen/svc/nds/tenant/planner",
        "//server/src/test/com/xgen/svc/nds/tenant/util",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model:cloudProviderTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/datalake/_public/model:datalakeTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/unit/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/unit/com/xgen/svc/atm",
        "//server/src/unit/com/xgen/svc/core",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/mms/util/serverless",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//server/src/unit/com/xgen/svc/nds/serverless/model:serverlessTestFactory",
        "//third_party:driverwrappers",
        "@com_xgen_mdb_idl//:mhouse_definitions",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:com_amazonaws_aws_java_sdk_kms",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_amazonaws_aws_java_sdk_sts",
        "@maven//:com_google_api_grpc_proto_google_iam_admin_v1",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:com_google_inject_guice",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:junit_junit",
        "@maven//:org_bouncycastle_bc_fips",
        "@maven//:org_bouncycastle_bcpkix_fips",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_skyscreamer_jsonassert",
    ],
)

test_package(
    name = "ClustersTestLibrary",
    srcs = CLUSTERS_TEST_LIBRARY,
    data = ["//server/scripts/nds:ssh_keys"],
    deny_warnings = True,
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/mms/res/admin",
        "//server/src/main/com/xgen/svc/mms/res/common",
    ],
    deps = [
        ":nonTestLibrary",
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/access/authz/_public/svc",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/agent",
        "//server/src/main/com/xgen/cloud/apiuser",
        "//server/src/main/com/xgen/cloud/appconfig/_public/config",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/core/_private/dao",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/brs/core/_private/dao",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/chef/_private/dao",
        "//server/src/main/com/xgen/cloud/chef/_public/model",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/dao/codec",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/entity",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/http/url",
        "//server/src/main/com/xgen/cloud/common/jackson",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/logging",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/system",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/cps/agent/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/pit",
        "//server/src/main/com/xgen/cloud/cps/pit/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/federation",
        "//server/src/main/com/xgen/cloud/fts/activity",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/ifrabtest",
        "//server/src/main/com/xgen/cloud/monitoring/common/_public/model",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/metrics/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/accesstransparency",
        "//server/src/main/com/xgen/cloud/nds/accesstransparency/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/admin",
        "//server/src/main/com/xgen/cloud/nds/admin/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/agentApiKeys",
        "//server/src/main/com/xgen/cloud/nds/agentApiKeys/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/common",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/context",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/context/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/capacity/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/checkmetadataconsistency",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datalake/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/datavalidation",
        "//server/src/main/com/xgen/cloud/nds/datavalidation/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dbcheck",
        "//server/src/main/com/xgen/cloud/nds/dbcheck/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/exmaintenance",
        "//server/src/main/com/xgen/cloud/nds/exmaintenance/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/ftdc",
        "//server/src/main/com/xgen/cloud/nds/fts",
        "//server/src/main/com/xgen/cloud/nds/fts/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/ifr",
        "//server/src/main/com/xgen/cloud/nds/ifr/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/leakeditem/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/logingestion",
        "//server/src/main/com/xgen/cloud/nds/logingestion/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/module",
        "//server/src/main/com/xgen/cloud/nds/monitoring",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/planning/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao/admin",
        "//server/src/main/com/xgen/cloud/nds/replicasethardware",
        "//server/src/main/com/xgen/cloud/nds/resourcepolicy",
        "//server/src/main/com/xgen/cloud/nds/rollingresync",
        "//server/src/main/com/xgen/cloud/nds/rollingresync/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/sampledataset",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/serverless/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/streams/_public/model",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/nds/tenant/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/vmimage",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/payments/common",
        "//server/src/main/com/xgen/cloud/payments/grpc",
        "//server/src/main/com/xgen/cloud/payments/standalone/common",
        "//server/src/main/com/xgen/cloud/search/api",
        "//server/src/main/com/xgen/cloud/search/common",
        "//server/src/main/com/xgen/cloud/search/decoupled/api",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/config",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/model/aws",
        "//server/src/main/com/xgen/cloud/search/decoupled/config/_public/svc",
        "//server/src/main/com/xgen/cloud/search/decoupled/external/_public/svc",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/dao",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/main/com/xgen/module/federation/dao",
        "//server/src/main/com/xgen/module/liveimport/common",
        "//server/src/main/com/xgen/module/liveimport/dao",
        "//server/src/main/com/xgen/module/liveimport/model",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/model/auth",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/svc/pausefreetiermonitoring",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/test/com/xgen/cloud/activity:utils",
        "//server/src/test/com/xgen/cloud/billingplatform/util",
        "//server/src/test/com/xgen/cloud/nds/project/_public/svc/testutil",
        "//server/src/test/com/xgen/cloud/nds/resourcepolicy/util",
        "//server/src/test/com/xgen/cloud/nds/spothealthcheck/util",
        "//server/src/test/com/xgen/cloud/search/decoupled/test",
        "//server/src/test/com/xgen/cloud/services/payments/modules/paymentMethod/common",
        "//server/src/test/com/xgen/module/common/planner/inttestimpls",
        "//server/src/test/com/xgen/svc/brs",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/mms/api/res",
        "//server/src/test/com/xgen/svc/mms/util",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/free/util",
        "//server/src/test/com/xgen/svc/nds/res/util",
        "//server/src/test/com/xgen/svc/nds/serverless/util",
        "//server/src/test/com/xgen/svc/nds/svc/adl:utils",
        "//server/src/test/com/xgen/svc/nds/svc/onlinearchive:utils",
        "//server/src/test/com/xgen/svc/nds/tenant/planner",
        "//server/src/test/com/xgen/svc/nds/tenant/util",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model:cloudProviderTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/datalake/_public/model:datalakeTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/unit/com/xgen/cloud/search/decoupled/config/_public/model",
        "//server/src/unit/com/xgen/svc/atm",
        "//server/src/unit/com/xgen/svc/core",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/mms/util/serverless",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//server/src/unit/com/xgen/svc/nds/serverless/model:serverlessTestFactory",
        "//third_party:driverwrappers",
        "@com_xgen_mdb_idl//:mhouse_definitions",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:com_amazonaws_aws_java_sdk_kms",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_amazonaws_aws_java_sdk_sts",
        "@maven//:com_google_api_grpc_proto_google_iam_admin_v1",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:com_google_inject_guice",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:junit_junit",
        "@maven//:org_bouncycastle_bc_fips",
        "@maven//:org_bouncycastle_bcpkix_fips",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_skyscreamer_jsonassert",
    ],
)

# NDS Azure Integration Tests
test_package(
    name = "externalTestLibrary",
    srcs = ["NDSAzurePeerResourceExternalIntTests.java"],
    deny_warnings = True,
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = [
        "external",
        "no-sandbox",
    ],
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/atm/res/api",
        "//server/src/main/com/xgen/svc/mms/res",
        "//server/src/main/com/xgen/svc/mms/res/admin",
        "//server/src/main/com/xgen/svc/mms/res/common",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/main/com/xgen/svc/nds/security/res",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/nds/azure",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)

# onlineArchiveConfResourceExternalTestLibrary
test_package(
    name = "onlineArchiveConfResourceExternalTestLibrary",
    srcs = glob([
        "OnlineArchiveConfResourceAwsExternalIntTests.java*",
        "OnlineArchiveConfResourceAzureExternalIntTests.java*",
        "OnlineArchiveConfResourceGcpExternalIntTests.java*",
    ]),
    deny_warnings = True,
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = ["external"],
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/atm/res/api",
        "//server/src/main/com/xgen/svc/mms/res",
        "//server/src/main/com/xgen/svc/mms/res/admin",
        "//server/src/main/com/xgen/svc/mms/res/common",
        "//server/src/main/com/xgen/svc/nds/res",
        "//server/src/main/com/xgen/svc/nds/security/res",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings/_public/svc",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper/_public/helper",
        "//server/src/main/com/xgen/cloud/common/aws",
        "//server/src/main/com/xgen/cloud/common/featureFlag/_public/model",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/deployment/_public/model",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp/_public/model",
        "//server/src/main/com/xgen/cloud/nds/gcp/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/organization/_public/model",
        "//server/src/main/com/xgen/cloud/organization/_public/svc",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/gcp",
        "//server/src/test/com/xgen/svc/nds/svc/adl:utils",
        "//server/src/test/com/xgen/svc/nds/svc/onlinearchive:utils",
        "//server/src/test/com/xgen/testlib/junit5/extensions/guicetest",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_google_api_gax",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:com_google_cloud_google_cloud_storage",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)

test_package(
    name = "atlasSearchResourceExternalTestLibrary",
    srcs = [
        "FTSIndexConfigMongotApiResourceBlobstoreExternalIntTests.java",
    ],
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = ["external"],
    runtime_deps = [
        "//server/src/main/com/xgen/svc/nds/res",
    ],
    deps = [
        ":nonTestLibrary",
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/featureFlag/_public/model",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/project/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/blobstore",
        "//server/src/main/com/xgen/cloud/search/decoupled/blobstore/_private/dao",
        "//server/src/main/com/xgen/cloud/search/decoupled/blobstore/_public/model",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider/_public/model",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/testlib/junit5/extensions/guicetest",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_amazonaws_aws_java_sdk_sts",
        "@maven//:com_azure_azure_core",
        "@maven//:com_azure_azure_storage_blob",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
    ],
)

test_package(
    name = "thirdpartyTestLibrary",
    srcs = glob(["*ThirdPartyTests.java"]),
    deny_warnings = True,
    tags = ["external"],
    runtime_deps = [
        "//server/src/main/com/xgen/svc/nds/res",
    ],
    deps = [
        "//server/src/test/com/xgen/svc/core",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
