package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._private.dao.CpsOplogSliceMetadataDao;
import com.xgen.cloud.cps.pit._private.dao.CpsRegionalMetadataStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsOplogId;
import com.xgen.cloud.cps.pit._public.model.CpsOplogSlice;
import com.xgen.cloud.cps.pit._public.model.OplogMigration;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.pit._public.ui.CpsOplogMetadataBatch;
import com.xgen.cloud.cps.pit._public.ui.CpsOplogSliceView;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob.Builder;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.CpsTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.cps.CpsConfSvc;
import com.xgen.svc.nds.svc.cps.CpsOplogIngestionSvc;
import com.xgen.svc.nds.svc.cps.CpsOplogSliceMetadataDaoProxy;
import com.xgen.svc.nds.svc.cps.CpsOplogStoreFactory;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.http.HttpStatus;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class CpsSliceResourceIntTests extends JUnit5BaseResourceTest {

  private static final String AGENT_SESSION_KEY = ObjectId.get().toHexString();
  private static final String AGENT_ADDRESS = "127.0.0.1";
  private static final String AGENT_HOSTNAME = "hostname";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private BackupJobDao _backupJobDao;

  @Inject private BackupRestoreJobDao _backupRestoreJobDao;

  @Inject private CpsConfSvc _cpsConfSvc;

  @Inject private CpsOplogStoreFactory _cpsOplogStoreFactory;

  @Inject private CpsRegionalMetadataStoreConfigDao _cpsRegionalMetadataStoreConfigDao;

  private Group _group;
  private AppUser _user;
  private AgentApiKey _agentApiKey;
  private ClusterDescription _clusterDescription;
  private BackupJob _backupJob;
  private String _rsId;
  private String _sliceBatchPostEndpointV2;
  private CpsOplogIngestionSvc _cpsOplogIngestionSvc;
  private CpsSliceResource _cpsSliceResource;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(organization, "cus_0001");
    _user = MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), _user.getId());

    // Setup ClusterDescription
    final BasicDBObject clusterBson =
        NDSModelTestFactory.getAWSClusterDescription(_group.getId())
            .append(FieldDefs.DISK_BACKUP_ENABLED, true)
            .append(FieldDefs.PIT_ENABLED, true);
    _clusterDescription = new ClusterDescription(clusterBson);
    _clusterDescriptionDao.save(_clusterDescription);

    // Replica set hardware
    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), _clusterDescription);
    _rsId = new ReplicaSetHardware(hardwareDoc).getRsId();
    _replicaSetHardwareDao.save(hardwareDoc, WriteConcern.ACKNOWLEDGED);

    // Setup Backup Job
    final BackupJob backupJob =
        NDSModelTestFactory.getBackupJobBuilder()
            .projectId(_group.getId())
            .clusterName(_clusterDescription.getName())
            .build();
    _backupJobDao.insert(BackupJobDao.toDbObj(backupJob), WriteConcern.ACKNOWLEDGED);

    // populate with some region config, this dao is not supposed to do any inserts,
    // but for the sake of testing we do it only here
    _cpsRegionalMetadataStoreConfigDao
        .getDbCollection()
        .insert(
            List.of(
                CpsTestUtils.getDefaultCpsRegionalMetadataStoreConfig(),
                CpsTestUtils.getSecondDefaultCpsRegionalMetadataStoreConfig(),
                CpsTestUtils.getThirdDefaultCpsRegionalMetadataStoreConfig()));

    // Enable PIT
    final PitSetting pitSetting =
        PitSetting.builder()
            .blobStoreConfigId("blobstore1")
            .regionalMetadataStoreConfigId("store1")
            .regionName("us-east-1")
            .lastContiguousCheckEndTimestamp(new BSONTimestamp(0, 0))
            .build();
    final Map<String, PitSetting> rsIdToPitSettings = new HashMap<>();
    rsIdToPitSettings.put(_rsId, pitSetting);
    _backupJobDao.enablePitIfDisabled(backupJob.getId());
    _backupJobDao.updatePitSettings(
        backupJob.getId(), rsIdToPitSettings, backupJob.getVersion() + 1);

    _backupJob = _backupJobDao.find(backupJob.getId()).orElseThrow();

    // Setup primary agent session for replica set
    _cpsConfSvc.isSessionWinner(
        _group.getId(),
        _rsId,
        AGENT_SESSION_KEY,
        AGENT_ADDRESS,
        AGENT_HOSTNAME,
        System.currentTimeMillis());

    _sliceBatchPostEndpointV2 =
        String.format(
            "/agents/cps/oplog/slice/batch/v2/%s/%s/%s?ah=%s&sk=%s",
            _group.getId(),
            _clusterDescription.getName(),
            _rsId,
            AGENT_HOSTNAME,
            AGENT_SESSION_KEY);

    _cpsSliceResource = AppConfig.getInstance(CpsSliceResource.class);
    _cpsOplogIngestionSvc = spy(AppConfig.getInstance(CpsOplogIngestionSvc.class));
    ClassModifier.modifyInstanceValue(
        _cpsSliceResource, "_cpsOplogIngestionSvc", _cpsOplogIngestionSvc);

    final CpsSvc cpsSvc = mock(CpsSvc.class);
    doReturn(true).when(cpsSvc).isCopySettingEnabledForCloudProvider(any(), any());
    ClassModifier.modifyInstanceValue(_cpsSliceResource, "_cpsSvc", cpsSvc);
  }

  @Test
  public void postOplogSliceMetadataBatchV2_ShardRemoved() throws Exception {
    // Lower numShards from 1 to 0 to emulate a removed shard.
    final ReplicationSpec replicationSpec =
        _clusterDescription.getReplicationSpecsWithShardData().get(0);
    final ReplicationSpec spec =
        new ReplicationSpec.Builder(replicationSpec).setNumShards(0).build();
    final ClusterDescription updated =
        _clusterDescription.copy().setReplicationSpecList(Collections.singletonList(spec)).build();
    _clusterDescriptionDao.save(updated);

    final CpsOplogMetadataBatch cpsOplogMetadataBatch =
        NDSModelTestFactory.createCpsOplogMetadataBatch(List.of("us-east-1"));
    final JSONObject json =
        new JSONObject(
            CustomJacksonJsonProvider.createObjectMapper()
                .writeValueAsString(cpsOplogMetadataBatch));

    final String response =
        doAgentApiCallPost(_group, _agentApiKey, _sliceBatchPostEndpointV2, json);
    assertEquals(
        new JSONObject(response).optString("status"),
        "OK",
        "Unexpected response format: " + response);

    // Slice should still be inserted while shard is draining.
    final CpsOplogId oplogStatus =
        _backupJobDao.find(_backupJob.getId()).orElseThrow().getOplogStatusByRsId(_rsId);

    assertNotNull(oplogStatus);
    assertEquals(5L, oplogStatus.getHash().longValue());
    assertEquals(6L, oplogStatus.getTerm().longValue());
    assertEquals(new BSONTimestamp(2, 1), oplogStatus.getTs());

    final CpsOplogSliceMetadataDaoProxy cpsSliceDao =
        _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(_backupJob, _rsId);
    assertNotNull(
        cpsSliceDao.findEarliestSlice(
            _group.getId(), _clusterDescription.getUniqueId(), _rsId, "us-east-1"),
        "Expected to find inserted slice while shard is draining");
  }

  @Test
  public void postOplogSliceMetadata_postOplogSliceMetadataBatchV2_missingCpsOplogMetadataBatch()
      throws Exception {
    // we should be passing in CpsOplogMetadataBatch to the v2 endpoint
    final CpsOplogSliceView view = NDSModelTestFactory.getCpsOplogSliceView();
    final JSONObject json =
        new JSONObject(CustomJacksonJsonProvider.createObjectMapper().writeValueAsString(view));

    doAgentApiCallPostWithStatus(
        _group, _agentApiKey, _sliceBatchPostEndpointV2, json, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void postOplogSliceMetadata_postOplogSliceMetadataBatchV2_regionMismatch()
      throws Exception {
    final CpsOplogMetadataBatch cpsOplogMetadataBatch =
        NDSModelTestFactory.createCpsOplogMetadataBatch(List.of("us-west-1"));
    final JSONObject json1 =
        new JSONObject(
            CustomJacksonJsonProvider.createObjectMapper()
                .writeValueAsString(cpsOplogMetadataBatch));

    doAgentApiCallPostWithStatus(
        _group, _agentApiKey, _sliceBatchPostEndpointV2, json1, HttpStatus.SC_BAD_REQUEST);

    final CpsOplogMetadataBatch cpsOplogMetadataBatch2 =
        NDSModelTestFactory.createCpsOplogMetadataBatch(Arrays.asList("us-east-1", "us-west-1"));

    final JSONObject json2 =
        new JSONObject(
            CustomJacksonJsonProvider.createObjectMapper()
                .writeValueAsString(cpsOplogMetadataBatch2));

    doAgentApiCallPostWithStatus(
        _group, _agentApiKey, _sliceBatchPostEndpointV2, json2, HttpStatus.SC_BAD_REQUEST);

    addMigration();
    doAgentApiCallPostWithStatus(
        _group, _agentApiKey, _sliceBatchPostEndpointV2, json1, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void postOplogSliceMetadata_postOplogSliceMetadataBatchV2_successNoOngoingMigration()
      throws Exception {
    final CpsOplogMetadataBatch cpsOplogMetadataBatch =
        NDSModelTestFactory.createCpsOplogMetadataBatch(List.of("us-east-1"));
    final JSONObject json =
        new JSONObject(
            CustomJacksonJsonProvider.createObjectMapper()
                .writeValueAsString(cpsOplogMetadataBatch));

    doAgentApiCallPostWithStatus(
        _group, _agentApiKey, _sliceBatchPostEndpointV2, json, HttpStatus.SC_OK);

    final CpsOplogSliceMetadataDaoProxy cpsSliceDao =
        _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(_backupJob, _rsId);
    assertNotNull(
        cpsSliceDao.findEarliestSlice(
            _group.getId(), _clusterDescription.getUniqueId(), _rsId, "us-east-1"),
        "Expected to find inserted slice");

    final List<CpsOplogSlice> slices =
        cpsSliceDao.getSlices(
            _rsId,
            "us-east-1",
            new BSONTimestamp(0, 0),
            new BSONTimestamp(Integer.MAX_VALUE, Integer.MAX_VALUE),
            true);

    assertEquals(5, slices.size());

    final Set<ObjectId> batchSliceIds =
        cpsOplogMetadataBatch.getOplogSliceViews().stream()
            .map(v -> v.getId())
            .collect(Collectors.toSet());

    final Set<ObjectId> storedIds = slices.stream().map(s -> s.getId()).collect(Collectors.toSet());

    final Set<ObjectId> storedSliceIds =
        slices.stream().map(s -> s.getSliceId()).collect(Collectors.toSet());

    assertEquals(batchSliceIds, storedSliceIds);
    assertNotEquals(storedIds, storedSliceIds);
  }

  @Test
  public void postOplogSliceMetadata_postOplogSliceMetadataBatchV2_successWithOngoingMigration()
      throws Exception {
    addMigration();
    final CpsOplogMetadataBatch cpsOplogMetadataBatch =
        NDSModelTestFactory.createCpsOplogMetadataBatch(Arrays.asList("us-east-1", "us-west-1"));
    final JSONObject json =
        new JSONObject(
            CustomJacksonJsonProvider.createObjectMapper()
                .writeValueAsString(cpsOplogMetadataBatch));

    doAgentApiCallPostWithStatus(
        _group, _agentApiKey, _sliceBatchPostEndpointV2, json, HttpStatus.SC_OK);

    final CpsOplogSliceMetadataDaoProxy primaryCpsSliceDao =
        _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(_backupJob, _rsId);
    assertNotNull(
        primaryCpsSliceDao.findEarliestSlice(
            _group.getId(), _clusterDescription.getUniqueId(), _rsId, "us-east-1"),
        "Expected to find inserted slice");

    final DBCursor cursor1 = primaryCpsSliceDao.findAll();
    assertEquals(5, cursor1.count());

    while (cursor1.hasNext()) {
      final BasicDBObject object = (BasicDBObject) cursor1.next();
      assertFalse(object.getBoolean(CpsOplogSliceMetadataDao.FieldDefs.SKIP_BILLING));
      assertEquals(
          "us-east-1-bucket", object.getString(CpsOplogSliceMetadataDao.FieldDefs.STORAGE_BUCKET));
      assertEquals(
          "us-east-1", object.getString(CpsOplogSliceMetadataDao.FieldDefs.STORAGE_REGION_NAME));
    }

    final CpsOplogSliceMetadataDaoProxy migrationCpsSliceDao =
        _cpsOplogStoreFactory.getCpsOplogMigrationStorageRegionSliceDaoProxy(_backupJob, _rsId);

    assertNotNull(
        migrationCpsSliceDao.findEarliestSlice(
            _group.getId(), _clusterDescription.getUniqueId(), _rsId, "us-west-1"),
        "Expected to find inserted slice");

    final DBCursor cursor2 = migrationCpsSliceDao.findAll();
    assertEquals(5, cursor2.count());

    while (cursor2.hasNext()) {
      final BasicDBObject object = (BasicDBObject) cursor2.next();
      assertFalse(object.getBoolean(CpsOplogSliceMetadataDao.FieldDefs.SKIP_BILLING));
      assertEquals(
          "us-west-1-bucket", object.getString(CpsOplogSliceMetadataDao.FieldDefs.STORAGE_BUCKET));
      assertEquals(
          "us-west-1", object.getString(CpsOplogSliceMetadataDao.FieldDefs.STORAGE_REGION_NAME));
    }
  }

  @Test
  @Disabled
  public void postOplogSliceMetadata_postOplogSliceMetadataBatchV2_rollback() {
    doThrow(new RuntimeException("uhoh!"))
        .when(_cpsOplogIngestionSvc)
        .ingestOplogMetadataRequestBatch(any(), any(), any(), eq("store3"), anyBoolean());

    addMigration();

    final CpsOplogMetadataBatch cpsOplogMetadataBatch =
        NDSModelTestFactory.createCpsOplogMetadataBatch(Arrays.asList("us-east-1", "us-west-1"));

    final List<ObjectId> sliceIds =
        cpsOplogMetadataBatch.getOplogSliceViews().stream()
            .map(CpsOplogSliceView::getId)
            .collect(Collectors.toList());

    _cpsSliceResource.ingestOplogMetadataRequestBatch(
        _backupJob,
        _rsId,
        _backupJob.getPitSettingByRsId(_rsId),
        AWSRegionName.US_EAST_1,
        cpsOplogMetadataBatch.getOplogSliceViews(),
        cpsOplogMetadataBatch.getStorageLocations());

    verify(_cpsOplogIngestionSvc)
        .ingestOplogMetadataRequestBatch(any(), any(), any(), eq("store1"), anyBoolean());
    verify(_cpsOplogIngestionSvc)
        .ingestOplogMetadataRequestBatch(any(), any(), any(), eq("store3"), anyBoolean());

    verify(_cpsOplogIngestionSvc).rollbackBatchInserts(_backupJob, _rsId, "store1", sliceIds);
    verify(_cpsOplogIngestionSvc).rollbackBatchInserts(_backupJob, _rsId, "store3", sliceIds);

    final CpsOplogSliceMetadataDaoProxy primaryCpsSliceDao =
        _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(_backupJob, _rsId);
    final DBCursor allInPrimary = primaryCpsSliceDao.findAll();
    assertFalse(allInPrimary.hasNext());

    final CpsOplogSliceMetadataDaoProxy migrationDao =
        _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(_backupJob, _rsId);
    final DBCursor allInMigration = migrationDao.findAll();
    assertFalse(allInMigration.hasNext());
  }

  @Test
  public void testGetOplogSliceMetadata() throws IOException {
    final CpsOplogSlice slice1 =
        NDSModelTestFactory.getCpsOplogSliceBuilder()
            .setReplicaSetId(_rsId)
            .setGroupId(_group.getId())
            .setCloudProvider(CloudProvider.AWS)
            .setStorageRegionName(AWSRegionName.US_EAST_1)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(100, 0))
            .setEndTimestamp(new BSONTimestamp(110, 0)) // out of range
            .setValid(true)
            .build();

    final CpsOplogSlice slice2 =
        NDSModelTestFactory.getCpsOplogSliceBuilder()
            .setReplicaSetId(_rsId)
            .setGroupId(_group.getId())
            .setCloudProvider(CloudProvider.AWS)
            .setStorageRegionName(AWSRegionName.US_EAST_1)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(110, 0))
            .setEndTimestamp(new BSONTimestamp(140, 10))
            .setValid(true)
            .build();

    final CpsOplogSlice slice3 =
        NDSModelTestFactory.getCpsOplogSliceBuilder()
            .setReplicaSetId("wrongRs") // wrong rs
            .setGroupId(_group.getId())
            .setCloudProvider(CloudProvider.AWS)
            .setStorageRegionName(AWSRegionName.US_EAST_1)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(120, 0))
            .setEndTimestamp(new BSONTimestamp(130, 0))
            .setValid(true)
            .build();

    final CpsOplogSlice slice4 =
        NDSModelTestFactory.getCpsOplogSliceBuilder()
            .setReplicaSetId(_rsId)
            .setGroupId(_group.getId())
            .setCloudProvider(CloudProvider.AWS)
            .setStorageRegionName(AWSRegionName.US_EAST_2) // wrong region
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(130, 0))
            .setEndTimestamp(new BSONTimestamp(140, 0))
            .setValid(true)
            .build();

    final CpsOplogSlice slice5 =
        NDSModelTestFactory.getCpsOplogSliceBuilder()
            .setSliceId(null)
            .setReplicaSetId(_rsId)
            .setGroupId(_group.getId())
            .setCloudProvider(CloudProvider.AWS)
            .setStorageRegionName(AWSRegionName.US_EAST_1)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(140, 5))
            .setEndTimestamp(new BSONTimestamp(150, 0))
            .setValid(true)
            .build();

    final CpsOplogSlice slice6 =
        NDSModelTestFactory.getCpsOplogSliceBuilder()
            .setReplicaSetId(_rsId)
            .setGroupId(_group.getId())
            .setCloudProvider(CloudProvider.AWS)
            .setStorageRegionName(AWSRegionName.US_EAST_1)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(150, 0)) // out of range
            .setEndTimestamp(new BSONTimestamp(160, 0))
            .setValid(true)
            .build();

    final CpsOplogSliceMetadataDaoProxy sliceDao =
        _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(_backupJob, _rsId);

    sliceDao.addMetadatas(List.of(slice1, slice2, slice3, slice4, slice5, slice6));

    final ObjectId restoreJobId = ObjectId.get();
    final String restoreVerificationKey = "verificationKey";

    final Builder restoreJobBuilder =
        NDSModelTestFactory.getDirectAttachRestoreJobBuilder(restoreJobId);

    restoreJobBuilder
        .getParentBuilder()
        .withRsId(_rsId)
        .withVerificationKey(EncryptionUtils.genEncryptStr(restoreVerificationKey))
        .getMetadataBuilder()
        .withProjectId(_group.getId())
        .withClusterName(_clusterDescription.getName());

    _backupRestoreJobDao.insertReplicaSafe(restoreJobBuilder.toDBObject());

    final String endpoint =
        String.format(
            "/agents/cps/oplog/slice/%s/%s:%s/%s:%s/%s/%s",
            _group.getId(), // target group ID
            110, // start time
            10, // start inc
            140, // end time
            10, // end inc
            restoreJobId,
            restoreVerificationKey);

    final String result =
        doAgentApiCallGetWithStatus(_group, _agentApiKey, endpoint, HttpStatus.SC_OK);

    final JSONArray resultJson = new JSONArray(result);
    assertEquals(2, resultJson.length()); // slice 2 and 5
    assertEquals(slice2.getId().toString(), resultJson.getJSONObject(0).getString("id"));
    assertEquals(slice5.getId().toString(), resultJson.getJSONObject(1).getString("id"));
  }

  public void addMigration() {
    final Map<String, PitSetting> pitsettings = _backupJob.getPitSettings();
    final PitSetting pitSetting = pitsettings.get(_rsId);

    final PitStorage pitStorage = new PitStorage("store3", "blobstore3", "us-west-1");
    final OplogMigration oplogMigration = new OplogMigration(List.of(pitStorage), new Date());
    final PitSetting updatedSetting = pitSetting.toBuilder().oplogMigration(oplogMigration).build();

    pitsettings.put(_rsId, updatedSetting);
    _backupJobDao.updatePitSettings(_backupJob.getId(), pitsettings, _backupJob.getVersion());
  }
}
