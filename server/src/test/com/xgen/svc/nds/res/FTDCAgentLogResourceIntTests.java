package com.xgen.svc.nds.res;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.agent._public.model.AgentLog;
import com.xgen.cloud.common.agent._public.model.AgentLogView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.ftdc._public.svc.FTDCAgentLogSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.skyscreamer.jsonassert.JSONAssert;

@ExtendWith(GuiceTestExtension.class)
public class FTDCAgentLogResourceIntTests extends JUnit5BaseResourceTest {
  private Group _group;
  private AppUser _lowPermissionUser;
  private AppUser _groupAdminUser;
  private AppUser _tseUser;
  private AppUser _monitoringUser;

  @Inject private FTDCAgentLogSvc _agentLogSvc;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(organization, "cus_0001");

    _lowPermissionUser =
        MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_READ_ONLY);
    _groupAdminUser = MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_OWNER);
    _tseUser = MmsFactory.createGlobalAtlasTSEUser(_group);
    _monitoringUser = MmsFactory.createMonitoringAdminUser(_group);
  }

  @Test
  public void TestGetLogs() throws Exception {
    final var withoutThrowable =
        new AgentLogView(
            1682618282766L, "error", "ftdcexport/module.go:62", "ftdc-module", "message one", "");
    final var withThrowable =
        new AgentLogView(
            1682618282763L,
            "error",
            "ftdcexport/module.go:62",
            "ftdc-module",
            "message two",
            "throw me");
    final ObjectMapper mapper = new ObjectMapper();

    final JSONArray jsonArray = new JSONArray();
    {
      final JSONObject objOne = new JSONObject(mapper.writeValueAsString(withoutThrowable));
      final JSONObject objTwo = new JSONObject(mapper.writeValueAsString(withThrowable));
      jsonArray.put(objOne).put(objTwo);
    }

    final String fakeHostname = "hostname";
    final String fakeSessionKey = "sessionKey";

    _agentLogSvc.saveAgentLogs(
        List.of(withoutThrowable, withThrowable), _group.getId(), fakeHostname, fakeSessionKey);

    final String uiGetEndpoint = String.format("/agents/ftdc/logs/%s", _group.getId());
    final JSONObject response = doAuthedJsonGet(_tseUser, uiGetEndpoint, HttpStatus.SC_OK);
    doAuthedJsonGet(_monitoringUser, uiGetEndpoint, HttpStatus.SC_OK);
    final String debugStr = response.toString();

    final JSONArray actualEntries = response.getJSONArray("entries");
    Assertions.assertEquals(
        2, actualEntries.length(), "Unexpected ftdc module logs response: " + debugStr);

    // results in descending timestamp order
    final JSONObject firstEntry = actualEntries.getJSONObject(0);
    // response includes groupId and _id
    Assertions.assertNotNull(firstEntry.remove("_id"));
    Assertions.assertNotNull(firstEntry.remove("groupId"));

    final AgentLog expectedFirst =
        withoutThrowable.toAgentLog(_group.getId(), fakeHostname, fakeSessionKey);
    final JSONObject stringyExpectation = new JSONObject(mapper.writeValueAsString(expectedFirst));
    stringyExpectation.remove("groupId");

    JSONAssert.assertEquals(stringyExpectation, firstEntry, true);

    final JSONObject secondEntry = actualEntries.getJSONObject(1);
    final AgentLog expectedSecond =
        withThrowable.toAgentLog(_group.getId(), fakeHostname, fakeSessionKey);
    final JSONObject stringySecond = new JSONObject(mapper.writeValueAsString(expectedSecond));
    // response includes groupId and _id
    Assertions.assertNotNull(secondEntry.remove("_id"));
    Assertions.assertNotNull(secondEntry.remove("groupId"));
    stringySecond.remove("groupId");

    JSONAssert.assertEquals(stringySecond, secondEntry, true);
  }

  @Test
  public void GetsGetLogsCSV() throws JsonProcessingException {
    final var withoutThrowable =
        new AgentLogView(
            1682618282766L, "error", "ftdcexport/module.go:62", "ftdc-module", "message one", "");
    final var withThrowable =
        new AgentLogView(
            1682618282763L,
            "error",
            "ftdcexport/module.go:62",
            "ftdc-module",
            "message two",
            "throw me");
    final ObjectMapper mapper = new ObjectMapper();

    final JSONArray jsonArray = new JSONArray();
    {
      final JSONObject objOne = new JSONObject(mapper.writeValueAsString(withoutThrowable));
      final JSONObject objTwo = new JSONObject(mapper.writeValueAsString(withThrowable));
      jsonArray.put(objOne).put(objTwo);
    }

    final String fakeHostname = "hostname";
    final String fakeSessionKey = "sessionKey";

    _agentLogSvc.saveAgentLogs(
        List.of(withoutThrowable, withThrowable), _group.getId(), fakeHostname, fakeSessionKey);

    // Define the endpoint with a fake group ID and optional start timestamp
    final String downloadEndpoint = String.format("/agents/ftdc/logs/csv/%s", _group.getId());
    final long startTimestamp = System.currentTimeMillis();

    final byte[] response =
        doAuthedGetBytes(
            _tseUser, downloadEndpoint + "?startTimestamp=" + startTimestamp, HttpStatus.SC_OK);

    // Convert the byte array to a string
    final String csvContent = new String(response, StandardCharsets.UTF_8);
    Assertions.assertNotNull(csvContent);
    Assertions.assertTrue(
        csvContent.contains(
            "Timestamp")); // Check that the header is present as a sanity check that the csv isn't
    // corrupted
  }

  @Test
  public void TestInvalidRequestFails() throws IOException {
    final String getEndpoint = String.format("/agents/ftdc/logs/%s", _group.getId());
    doAuthedJsonGet(_lowPermissionUser, getEndpoint, HttpStatus.SC_FORBIDDEN);
    doAuthedJsonGet(_groupAdminUser, getEndpoint, HttpStatus.SC_FORBIDDEN);

    final String csvEndpoint = String.format("/agents/ftdc/logs/csv/%s", _group.getId());
    doAuthedGetBytes(_lowPermissionUser, csvEndpoint, HttpStatus.SC_FORBIDDEN);
    doAuthedGetBytes(_groupAdminUser, csvEndpoint, HttpStatus.SC_FORBIDDEN);
  }
}
