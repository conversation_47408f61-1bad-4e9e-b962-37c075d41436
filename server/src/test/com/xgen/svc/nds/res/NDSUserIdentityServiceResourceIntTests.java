package com.xgen.svc.nds.res;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.BaseHostUtils;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster;
import com.xgen.cloud.nds.common._public.model.AWSIAMType;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.X509Type;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ProxyConfig.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBUser;
import com.xgen.cloud.nds.serverless._private.dao.AtlasUISKeysDao;
import com.xgen.cloud.nds.serverless._public.model.AtlasUISKey;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.ui.UISKeyView;
import com.xgen.cloud.nds.serverless._public.model.ui.UISPromMetricView;
import com.xgen.cloud.nds.serverless._public.model.ui.UISPromMetricsLabelView;
import com.xgen.cloud.nds.serverless._public.model.ui.UISPromMetricsView;
import com.xgen.cloud.nds.serverless._public.svc.AtlasUISKeysSvc;
import com.xgen.cloud.nds.serverless._public.svc.AtlasUISPromHandlerSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.model.ui.MTMClusterView;
import com.xgen.svc.nds.model.ui.UISAccountsView;
import com.xgen.svc.nds.model.ui.UISKeysView;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.tenant.planner.TenantIntTest;
import com.xgen.svc.nds.tenant.util.TenantTestUtil;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.skyscreamer.jsonassert.JSONAssert;

public class NDSUserIdentityServiceResourceIntTests extends TenantIntTest {

  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private OrganizationSvc _orgSvc;
  @Inject private AuditSvc _auditSvc;
  @Inject private UserDao _userDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AppSettings _appSettings;
  @Inject private AtlasUISKeysSvc _atlasUISKeysSvc;
  @Inject private AtlasUISKeysDao _atlasUISKeysDao;

  private List<ServerlessMTMPool> _serverlessMTMPools;
  private List<EnvoyInstance> _envoyInstances;
  private List<AtlasUISKey> _keys;

  @Before
  public void setUp() throws Exception {
    clearData();
    ensureApp();

    makeAWSAccount("test", "test");
    makeAWSDNSAccount("test", "test");

    setupServerlessMTMGroup();
    setupServerlessGroup(true);
    getServerlessContainerDao()
        .setProvisionedFields(
            getServerlessGroup().getId(),
            getServerlessContainerId(),
            getServerlessMtmGroup().getId(),
            getServerlessMtmCluster().getName());

    final AppUser user = _userDao.findByUsername("<EMAIL>");
    user.setLastAuthAddr("127.0.0.1");
    _userDao.update(user);

    // Create Atlas UIS keys for testing
    _keys = new ArrayList<>();
    _envoyInstances =
        List.of(
            ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 0),
            ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 1),
            ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 2));
    _envoyInstances.forEach(
        envoyInstance ->
            _keys.addAll(
                List.of(
                    new AtlasUISKey(
                        new ObjectId(),
                        getServerlessMtmCluster().getPoolId(),
                        envoyInstance.getId(),
                        Date.from(Instant.now()),
                        "TestPEM" + envoyInstance.getId(),
                        null,
                        null),
                    new AtlasUISKey(
                        new ObjectId(),
                        getServerlessMtmCluster().getPoolId(),
                        envoyInstance.getId(),
                        Date.from(
                            Instant.now().minus(Duration.ofMinutes(30))), // Created 30 minutes ago
                        "TestPEM" + envoyInstance.getId(),
                        null,
                        null),
                    new AtlasUISKey(
                        new ObjectId(),
                        getServerlessMtmCluster().getPoolId(),
                        envoyInstance.getId(),
                        Date.from(Instant.now().minus(Duration.ofHours(2))), // Created 2 hours ago
                        "TestPEM" + envoyInstance.getId(),
                        Date.from(Instant.now().minus(Duration.ofHours(1))), // Expired 1 hour ago
                        null))));
    _keys.forEach(key -> _atlasUISKeysDao.createKey(key));
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    // Just to override the default teardown in the super class. That step is not necessary
    // as this test does not create any DNS record
  }

  private NDSDBUser addAWSUser() {
    return NDSDBUser.builder()
        .username("AWS:arn:user")
        .database("test")
        .roles(
            Collections.singletonList(_ndsGroupSvc.getNDSDBRoleSvc().factoryCreate("db", "read")))
        .scopes(Collections.emptyList())
        .labels(Collections.emptyList())
        .isEditable(true)
        .awsIAMType(AWSIAMType.USER)
        .build();
  }

  private NDSDBUser addX509User() {
    return NDSDBUser.builder()
        .username("cn=test")
        .database("test")
        .roles(
            Collections.singletonList(_ndsGroupSvc.getNDSDBRoleSvc().factoryCreate("db", "read")))
        .scopes(Collections.emptyList())
        .labels(Collections.emptyList())
        .isEditable(true)
        .x509Type(X509Type.MANAGED)
        .build();
  }

  @Test
  public void testNonexistent() throws Exception {
    final String path =
        "/nds/uis/conf/"
            + getServerlessMtmGroup().getId()
            + "/"
            + getServerlessMtmCluster().getPoolId().toString()
            + "/"
            + (new ObjectId()).toHexString();

    final String mtmClusterName = getClusterSvc().getServerlessMTMClusterViews().get(0).getName();
    final JSONObject body =
        new JSONObject()
            .put(
                FieldDefs.PROXY_HOSTNAME,
                TenantTestUtil.makeLegacyHostname(
                    mtmClusterName, 0, getServerlessClusterDescription().getDnsPin()))
            .put(FieldDefs.PROXY_VERSION, "v0.0.1")
            .put(FieldDefs.UPTIME_SECONDS, 1000)
            .put(FieldDefs.ACCOUNTS, Collections.emptyMap())
            .put("ThisFieldDoesNotExist", "AtAllItsTotallyMadeUp");
    final String responseString =
        doAgentApiCallPost(getServerlessMtmGroup(), getServerlessMtmAgentApiKey(), path, body);
    final JSONObject response = new JSONObject(responseString);
    assertNotNull(response);
    // Only the MTM hostname account should be present
    assertEquals(1, ((JSONArray) response.get(FieldDefs.ACCOUNTS)).length());
    assertEquals(
        getServerlessMtmClusterDescription().getUniqueId().toString() + "_",
        ((JSONArray) response.get(FieldDefs.ACCOUNTS)).getJSONObject(0).getString("dbPrefix"));

    assertTrue(response.isNull(UISAccountsView.FieldDefs.PUBLIC_KEY));
  }

  private void testEmpty(
      final Group pMTMGroup,
      final ObjectId pServerlessPoolID,
      final ClusterDescription pMTMCd,
      final MTMClusterView pMTMClusterView,
      final AgentApiKey pAgentApiKey)
      throws Exception {
    final String path =
        "/nds/uis/conf/"
            + pMTMGroup.getId()
            + "/"
            + pServerlessPoolID.toString()
            + "/"
            + _envoyInstances.get(0).getId().toHexString();

    final String mtmClusterName = pMTMClusterView.getName();
    final JSONObject body =
        new JSONObject()
            .put(
                FieldDefs.PROXY_HOSTNAME,
                TenantTestUtil.makeLegacyHostname(mtmClusterName, 0, pMTMCd.getDnsPin()))
            .put(FieldDefs.PROXY_VERSION, "v0.0.1")
            .put(FieldDefs.UPTIME_SECONDS, 1000)
            .put(FieldDefs.ACCOUNTS, Collections.emptyMap())
            .put(FieldDefs.INTERNAL_CREDENTIALS_PASS, "password");
    final String responseString = doAgentApiCallPost(pMTMGroup, pAgentApiKey, path, body);
    final JSONObject response = new JSONObject(responseString);
    assertNotNull(response);

    assertEquals(1, ((JSONArray) response.get(FieldDefs.ACCOUNTS)).length());

    final JSONArray mtmHostnames =
        (JSONArray) response.get(UISAccountsView.FieldDefs.MTM_HOSTNAME_ACCOUNTS);
    assertEquals(1, mtmHostnames.length());

    assertEquals(
        getServerlessMtmClusterDescription().getUniqueId().toHexString() + "_",
        mtmHostnames.getString(0));

    final JSONObject uisPublicKey = response.getJSONObject(UISAccountsView.FieldDefs.PUBLIC_KEY);
    assertNotNull(uisPublicKey);
    assertTrue(uisPublicKey.has(UISKeyView.FieldDefs.KEY_ID));
    assertTrue(uisPublicKey.has(UISKeyView.FieldDefs.CREATED_DATE));
    assertTrue(uisPublicKey.has(UISKeyView.FieldDefs.PUBLIC_KEY_PEM));
    assertEquals(
        uisPublicKey.getString(UISKeyView.FieldDefs.PUBLIC_KEY_PEM),
        "TestPEM" + _envoyInstances.get(0).getId());
  }

  @Test
  public void testEmpty_Serverless() throws Exception {
    testEmpty(
        getServerlessMtmGroup(),
        getServerlessMtmCluster().getPoolId(),
        getServerlessMtmClusterDescription(),
        getClusterSvc().getServerlessMTMClusterViews().get(0),
        getServerlessMtmAgentApiKey());
  }

  public void testSingleNew(
      final Group pGroup,
      final Group pMTMGroup,
      final ObjectId pServerlessPoolId,
      final MTMClusterView pMTMClusterView,
      final MTMCluster pMTMCluster,
      final ClusterDescription pTenantClusterDescription,
      final AgentApiKey pMTMAgentApiKey,
      final ClusterDescription pMTMClusterDescription)
      throws Exception {
    final NDSGroup ndsGroup = getNDSGroup(pGroup.getId());
    final NDSGroup mtmNdsGroup = getNDSGroup(pMTMGroup.getId());
    final Organization mtmOrg = _orgSvc.findById(pMTMGroup.getOrgId());
    final AppUser mtmUser = MmsFactory.createGroupOwnerUser(pMTMGroup);
    final String path =
        "/nds/uis/conf/"
            + pMTMGroup.getId()
            + "/"
            + pServerlessPoolId.toString()
            + "/"
            + _envoyInstances.get(0).getId().toHexString();
    final String mtmClusterName = pMTMClusterView.getName();
    final String mtmClusterHostname =
        TenantTestUtil.makeLegacyHostname(mtmClusterName, 0, mtmNdsGroup.getDNSPin());
    final JSONObject body =
        new JSONObject()
            .put(FieldDefs.PROXY_HOSTNAME, mtmClusterHostname)
            .put(FieldDefs.PROXY_VERSION, "v0.0.1")
            .put(FieldDefs.UPTIME_SECONDS, 1000)
            .put(FieldDefs.ACCOUNTS, Collections.emptyMap());

    _ndsGroupDao.addDatabaseUser(ndsGroup.getGroupId(), addAWSUser());
    _ndsGroupDao.addDatabaseUser(ndsGroup.getGroupId(), addX509User());

    final ObjectId tenantContainerId = ObjectId.get();

    getMtmClusterDao()
        .decrementCapacityForCluster(
            pMTMCluster.getGroupId(),
            pMTMCluster.getName(),
            pMTMCluster.getMTMClusterType(),
            tenantContainerId);

    getServerlessMTMClusterDao()
        .incrementTenantTargetProxyVersion(
            pMTMCluster.getGroupId(),
            pMTMCluster.getName(),
            pMTMCluster.getMTMClusterType(),
            pTenantClusterDescription.getUniqueId(),
            tenantContainerId);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findDraftOrEmpty(pMTMGroup.getId(), mtmUser.getId());
    final Auth auth = automationConfig.getDeployment().getAuth();
    auth.setAutoUser("username");
    auth.setAutoPwd("password");
    _automationConfigSvc.saveDraft(automationConfig, mtmUser, mtmOrg, pMTMGroup);
    _automationConfigSvc.publish(mtmOrg, pMTMGroup, mtmUser);

    // Send no accounts and get back the single account
    final String responseString = doAgentApiCallPost(pMTMGroup, pMTMAgentApiKey, path, body);
    final JSONObject response = new JSONObject(responseString);
    assertNotNull(response);
    assertTrue(response.has(FieldDefs.ACCOUNTS));
    final JSONArray accounts = (JSONArray) response.get(FieldDefs.ACCOUNTS);
    assertEquals(2, accounts.length());

    final JSONObject newAccountJson;
    final JSONObject mtmAccountJson;
    // We want to examine the contents of the tenant and mtm hostname account, so lets make sure
    // we're checking the right one,
    // as the array order may not always be consistent
    if (accounts
        .getJSONObject(0)
        .getString("dbPrefix")
        .equals(pMTMClusterDescription.getUniqueId().toHexString() + "_")) {
      mtmAccountJson = accounts.getJSONObject(0);
      newAccountJson = accounts.getJSONObject(1);
    } else {
      newAccountJson = accounts.getJSONObject(0);
      mtmAccountJson = accounts.getJSONObject(1);
    }
    assertEquals(
        pTenantClusterDescription.getUniqueId().toString() + "_",
        newAccountJson.getString("dbPrefix"));
    assertEquals(1, newAccountJson.getLong("version"));
    final JSONObject users = newAccountJson.getJSONObject("users");
    assertTrue(users.has("AWS:arn:user"));
    assertTrue(users.has("cn=test"));

    final JSONArray mtmHostnames =
        (JSONArray) response.get(UISAccountsView.FieldDefs.MTM_HOSTNAME_ACCOUNTS);
    assertEquals(1, mtmHostnames.length());

    assertEquals(
        pMTMClusterDescription.getUniqueId().toHexString() + "_", mtmHostnames.getString(0));

    assertEquals(
        pMTMClusterDescription.getUniqueId().toString() + "_",
        mtmAccountJson.getString("dbPrefix"));

    assertEquals(1, mtmAccountJson.getLong("version"));

    final List<String> mtmHostnamesInDB = new ArrayList<>();
    getHardwareDao().findByCluster(pMTMGroup.getId(), pMTMClusterDescription.getName()).stream()
        .flatMap(replicaSetHardware -> replicaSetHardware.getHardware().stream())
        .forEach(
            instanceHardware -> {
              // SNI should always match tenant hostname with MTM hostname that matches the MTM
              // cluster's hostname scheme for agents, regardless of tenant hostname scheme.
              IntStream.range(0, 3)
                  .forEach(
                      i ->
                          mtmHostnamesInDB.add(
                              instanceHardware
                                  .getHostnames()
                                  .getHostnameOfScheme(
                                      pMTMClusterDescription.getHostnameSchemeForAgents().get())
                                  .get()));
            });
    final List<String> sniHostnamesInDB = new ArrayList<>();
    getHardwareDao()
        .findByCluster(ndsGroup.getGroupId(), pTenantClusterDescription.getName())
        .stream()
        .flatMap(replicaSetHardware -> replicaSetHardware.getHardware().stream())
        .forEach(
            instanceHardware -> {
              sniHostnamesInDB.add(
                  instanceHardware
                      .getHostnames()
                      .getHostnameOfScheme(InstanceHostname.HostnameScheme.INTERNAL)
                      .get());
              sniHostnamesInDB.add(
                  instanceHardware
                      .getHostnames()
                      .getHostnameOfScheme(InstanceHostname.HostnameScheme.LEGACY)
                      .get());
              sniHostnamesInDB.add(
                  instanceHardware
                      .getHostnames()
                      .getHostnameOfScheme(InstanceHostname.HostnameScheme.PUBLIC)
                      .get());
            });
    final JSONArray correctHosts = new JSONArray();
    IntStream.range(0, mtmHostnamesInDB.size())
        .forEach(
            i -> {
              final JSONObject correctHost = new JSONObject();
              correctHost.put(
                  "mtm",
                  BaseHostUtils.assembleHostnameAndPort(
                      mtmHostnamesInDB.get(i), NDSDefaults.MONGOD_MTM_PUBLIC_PORT));
              correctHost.put(
                  "sni",
                  BaseHostUtils.assembleHostnameAndPort(
                      sniHostnamesInDB.get(i), NDSDefaults.MONGOD_PUBLIC_PORT));
              correctHosts.put(correctHost);
            });

    if (mtmNdsGroup.isServerlessMTMHolder()) {
      final JSONObject serverlessLBHost = new JSONObject();
      final String loadBalancedHostname =
          DNSRecordUtil.getLoadBalancedHostnameForServerlessInstance(
              pTenantClusterDescription,
              NDSSettings.getInstanceDomainName(_appSettings, SubdomainLevel.MONGODB));
      serverlessLBHost.put(
          "mtm",
          BaseHostUtils.assembleHostnameAndPort(
              mtmHostnamesInDB.get(0), NDSDefaults.MONGOD_MTM_PUBLIC_PORT));
      serverlessLBHost.put(
          "sni",
          BaseHostUtils.assembleHostnameAndPort(
              loadBalancedHostname, NDSDefaults.MONGOD_PUBLIC_PORT));
      correctHosts.put(serverlessLBHost);

      final JSONObject serverlessMeshLBHost = new JSONObject();
      final String loadBalancedMeshHostname =
          DNSRecordUtil.getLoadBalancedMeshHostnameForServerlessInstance(
              pTenantClusterDescription,
              NDSSettings.getInstanceDomainName(_appSettings, SubdomainLevel.MONGODB));
      serverlessMeshLBHost.put(
          "mtm",
          BaseHostUtils.assembleHostnameAndPort(
              mtmHostnamesInDB.get(0), NDSDefaults.MONGOD_MTM_PUBLIC_PORT));
      serverlessMeshLBHost.put(
          "sni",
          BaseHostUtils.assembleHostnameAndPort(
              loadBalancedMeshHostname, NDSDefaults.MONGOD_PUBLIC_PORT));
      correctHosts.put(serverlessMeshLBHost);
    }

    JSONAssert.assertEquals(correctHosts, newAccountJson.getJSONArray("hosts"), false);

    final JSONObject uisPublicKey = response.getJSONObject(UISAccountsView.FieldDefs.PUBLIC_KEY);
    assertNotNull(uisPublicKey);
    assertTrue(uisPublicKey.has(UISKeyView.FieldDefs.KEY_ID));
    assertTrue(uisPublicKey.has(UISKeyView.FieldDefs.CREATED_DATE));
    assertTrue(uisPublicKey.has(UISKeyView.FieldDefs.PUBLIC_KEY_PEM));
    assertEquals(
        uisPublicKey.getString(UISKeyView.FieldDefs.PUBLIC_KEY_PEM),
        "TestPEM" + _envoyInstances.get(0).getId());
  }

  @Test
  public void testSingleNew_Serverless() throws Exception {
    testSingleNew(
        getServerlessGroup(),
        getServerlessMtmGroup(),
        getServerlessMtmCluster().getPoolId(),
        getClusterSvc().getServerlessMTMClusterViews().get(0),
        getServerlessMtmCluster(),
        getServerlessClusterDescription(),
        getServerlessMtmAgentApiKey(),
        getServerlessMtmClusterDescription());
  }

  private void testUISPanic(
      final Group pMTMGroup, final ObjectId pServerlessPoolId, final AgentApiKey pMTMAgentApiKey) {
    final String handlingUISHost = "testUIShost.com";
    setHandlingUISHostnameInInstanceHardware(handlingUISHost);

    // trigger UIS panic
    HttpUtils.getInstance()
        .post()
        .basicAuth(pMTMGroup.getId().toString(), pMTMAgentApiKey.getKey())
        .path(
            "/nds/uis/errors/%s/%s/%s",
            pMTMGroup.getId().toString(), pServerlessPoolId.toString(), handlingUISHost)
        .data(
            new JSONObject()
                .put("uisHostName", handlingUISHost)
                .put("uisVersion", "v20200421")
                .put("uisGitCommit", "bb06b57996df2fb991626886dd4dbdc0bdf6d8b1")
                .put("goVersion", "1.14.2")
                .put("panicData", "oh no!")
                .put("panicTrace", "trace1\ntrace2\ntrace3"))
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // ensure that global alert was sent
    assertEquals(1, _auditSvc.findByDate(Type.UIS_PANICKED, Date.from(Instant.EPOCH)).size());
  }

  @Test
  public void testUISPanic_Serverless() {
    testUISPanic(
        getServerlessMtmGroup(),
        getServerlessMtmCluster().getPoolId(),
        getServerlessMtmAgentApiKey());
  }

  private void setHandlingUISHostnameInInstanceHardware(final String pHandlingUISHostname) {
    final ClusterDescription mtmClusterDescription = getServerlessMtmClusterDescription();
    final ReplicaSetHardware mtmReplicaSetHardware =
        getHardwareDao()
            .findByCluster(mtmClusterDescription.getGroupId(), mtmClusterDescription.getName())
            .get(0);
    final InstanceHardware instanceHardware = mtmReplicaSetHardware.getHardware().get(0);
    final Hostnames hostnames = new Hostnames(pHandlingUISHostname);

    getHardwareDao()
        .setInstanceField(
            mtmReplicaSetHardware.getId(),
            instanceHardware.getInstanceId(),
            false,
            InstanceHardware.FieldDefs.HOSTNAMES,
            hostnames.toDBList());
  }

  @Test
  public void testUploadUISKey() {
    _envoyInstances.forEach(
        instance -> {

          // Test basic upload behavior. More stringent tests can be found in
          // AtlasUISKeysSvcIntTests
          final AtlasUISKey newKey =
              new AtlasUISKey(
                  new ObjectId(),
                  getServerlessMtmCluster().getPoolId(),
                  instance.getId(),
                  Date.from(Instant.now().plusSeconds(1)),
                  "TestPEM" + instance.getId(),
                  null,
                  null);
          HttpUtils.getInstance()
              .post()
              .basicAuth(
                  getServerlessMtmGroup().getId().toString(),
                  getServerlessMtmAgentApiKey().getKey())
              .path(
                  "/nds/uis/keys/%s/%s/%s",
                  getServerlessMtmGroup().getId().toString(),
                  getServerlessMtmCluster().getPoolId().toString(),
                  instance.getId().toString())
              .data(
                  new JSONObject()
                      .put(UISKeyView.FieldDefs.PUBLIC_KEY_PEM, newKey.getPublicKeyPEM())
                      .put(UISKeyView.FieldDefs.KEY_ID, newKey.getId())
                      .put(
                          UISKeyView.FieldDefs.CREATED_DATE,
                          TimeUtils.toISOStringMillis(newKey.getCreatedDate())))
              .expectedReturnStatus(HttpStatus.SC_OK)
              .send();
          assertTrue(_atlasUISKeysDao.findKeyById(newKey.getId()).isPresent());
          // Get new list of keys for instance
          final List<AtlasUISKey> newKeysPostUpload =
              _atlasUISKeysSvc.findLatestKeysByEnvoyInstanceID(
                  getServerlessMtmCluster().getPoolId(), instance.getId());
          // Ensure only 2 keys exist, the newly uploaded key and the previous unexpired key
          assertEquals(newKeysPostUpload.size(), 2);
          assertEquals(newKeysPostUpload.get(0), newKey);
          assertTrue(
              newKeysPostUpload.get(1).getKeyExpiration() != null
                  && newKeysPostUpload
                      .get(1)
                      .getKeyExpiration()
                      .after(Date.from(Instant.now())) // set for one hour from now
                  && newKeysPostUpload
                      .get(1)
                      .getKeyExpiration()
                      .before(
                          Date.from(
                              Instant.now()
                                  .plus(Duration.ofHours(2))))); // will expire within 2 hours

          // Check with the underlying Dao that only 2 keys exist for the instance + pool
          List<AtlasUISKey> keysFromDao =
              _atlasUISKeysDao
                  .findKeysByInstanceID(getServerlessMtmCluster().getPoolId(), instance.getId())
                  .stream()
                  .sorted(Comparator.comparing(AtlasUISKey::getCreatedDate).reversed())
                  .collect(Collectors.toList());
          assertEquals(keysFromDao.size(), 2);
          assertEquals(keysFromDao.get(0), newKey);
        });
  }

  @Test
  public void testFindLatestKeysByEnvoyInstanceID() {
    // Get keys that exist for instance+pool, ensure they have not expired
    _envoyInstances.forEach(
        instance -> {
          final UISKeysView result =
              HttpUtils.getInstance()
                  .get()
                  .basicAuth(
                      getServerlessMtmGroup().getId().toString(),
                      getServerlessMtmAgentApiKey().getKey())
                  .path(
                      "/nds/uis/keys/%s/%s/%s",
                      getServerlessMtmGroup().getId().toString(),
                      getServerlessMtmCluster().getPoolId().toString(),
                      instance.getId().toString())
                  .expectedReturnStatus(HttpStatus.SC_OK)
                  .returnType(UISKeysView.class)
                  .send();

          assertEquals(result.getKeys().size(), 2);
          result
              .getKeys()
              .forEach(
                  key -> {
                    assertTrue(
                        key.getKeyExpiration() == null
                            || key.getKeyExpiration().after(Date.from(Instant.now())));
                  });
        });
    // Get key from pool+instance that doesn't exist
    final UISKeysView result =
        HttpUtils.getInstance()
            .get()
            .basicAuth(
                getServerlessMtmGroup().getId().toString(), getServerlessMtmAgentApiKey().getKey())
            .path(
                "/nds/uis/keys/%s/%s/%s",
                getServerlessMtmGroup().getId().toString(),
                MongoSvcUtils.oid(9999),
                MongoSvcUtils.oid(9999))
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(UISKeysView.class)
            .send();
    assertTrue(result.getKeys().isEmpty());
  }

  @Test
  public void testFindKeysByPoolID() {
    // Get keys that exist for pool, ensure they have not expired
    final UISKeysView result =
        HttpUtils.getInstance()
            .get()
            .basicAuth(
                getServerlessMtmGroup().getId().toString(), getServerlessMtmAgentApiKey().getKey())
            .path(
                "/nds/uis/keys/%s/%s",
                getServerlessMtmGroup().getId().toString(),
                getServerlessMtmCluster().getPoolId().toString())
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(UISKeysView.class)
            .send();

    assertEquals(result.getKeys().size(), 6);
    result
        .getKeys()
        .forEach(
            key -> {
              assertTrue(
                  key.getKeyExpiration() == null
                      || key.getKeyExpiration()
                          .after(
                              Date.from(
                                  Instant.now()))); // Ensure keys have either not expired nor have
              // no expiration set
            });

    // Get key from pool that doesn't exist
    List<AtlasUISKey> keys = _atlasUISKeysSvc.findKeysByPoolID(MongoSvcUtils.oid(9999));
    assertTrue(keys.isEmpty());
    final UISKeysView newResult =
        HttpUtils.getInstance()
            .get()
            .basicAuth(
                getServerlessMtmGroup().getId().toString(), getServerlessMtmAgentApiKey().getKey())
            .path(
                "/nds/uis/keys/%s/%s",
                getServerlessMtmGroup().getId().toString(), MongoSvcUtils.oid(9999))
            .expectedReturnStatus(HttpStatus.SC_OK)
            .returnType(UISKeysView.class)
            .send();
    assertTrue(newResult.getKeys().isEmpty());
  }

  @Test
  public void testRotateKeyForInstance() {
    _envoyInstances.forEach(
        instance -> {
          try {

            HttpUtils.getInstance()
                .post()
                .basicAuth(
                    getServerlessMtmGroup().getId().toString(),
                    getServerlessMtmAgentApiKey().getKey())
                .path(
                    "/nds/uis/rotateKeys/%s/%s/%s",
                    getServerlessMtmGroup().getId().toString(),
                    getServerlessMtmCluster().getPoolId().toString(),
                    instance.getId().toString())
                .data(new JSONObject())
                .expectedReturnStatus(HttpStatus.SC_OK)
                .send();

            Thread.sleep(200);
            final List<AtlasUISKey> keysInDb =
                _atlasUISKeysSvc.findLatestKeysByEnvoyInstanceID(
                    getServerlessMtmCluster().getPoolId(), instance.getId());
            assertEquals(keysInDb.size(), 2);
            // RotateKeysAfter should now be set and should be before the current time
            assertTrue(
                keysInDb.get(0).getRotateKeysAfter() != null
                    && keysInDb.get(0).getRotateKeysAfter().before(Date.from(Instant.now())));
          } catch (final InterruptedException pE) {
            fail("Did not expect InterruptedException");
          }
        });

    // Rotate for instance that doesn't exist - exception thrown
    String result =
        HttpUtils.getInstance()
            .post()
            .basicAuth(
                getServerlessMtmGroup().getId().toString(), getServerlessMtmAgentApiKey().getKey())
            .path(
                "/nds/uis/rotateKeys/%s/%s/%s",
                getServerlessMtmGroup().getId().toString(),
                MongoSvcUtils.oid(9999),
                MongoSvcUtils.oid(9999))
            .data(new JSONObject())
            .expectedReturnStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR)
            .returnType(String.class)
            .send();

    assertNotNull(result);
    assertTrue(
        result.contains("No UIS public keys found for Instance ID: 000000000000000000009999"));
  }

  @Test
  public void testRemoveKeysForEnvoyInstanceID() {
    _envoyInstances.forEach(
        instance -> {
          HttpUtils.getInstance()
              .post()
              .basicAuth(
                  getServerlessMtmGroup().getId().toString(),
                  getServerlessMtmAgentApiKey().getKey())
              .path(
                  "/nds/uis/removeKeys/%s/%s/%s",
                  getServerlessMtmGroup().getId().toString(),
                  getServerlessMtmCluster().getPoolId().toString(),
                  instance.getId().toString())
              .data(new JSONObject())
              .expectedReturnStatus(HttpStatus.SC_OK)
              .send();

          assertTrue(
              _atlasUISKeysDao
                  .findKeysByInstanceID(getServerlessMtmCluster().getPoolId(), instance.getId())
                  .isEmpty());
        });

    // Remove for instance that doesn't exist
    String result =
        HttpUtils.getInstance()
            .post()
            .basicAuth(
                getServerlessMtmGroup().getId().toString(), getServerlessMtmAgentApiKey().getKey())
            .path(
                "/nds/uis/removeKeys/%s/%s/%s",
                getServerlessMtmGroup().getId().toString(),
                MongoSvcUtils.oid(9999),
                MongoSvcUtils.oid(9999))
            .data(new JSONObject())
            .expectedReturnStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR)
            .returnType(String.class)
            .send();

    assertNotNull(result);
    assertTrue(
        result.contains(
            "Encountered a server error during a DB operation: Problem deleting UIS Key for"
                + " Serverless Pool ID 000000000000000000009999 and Envoy Instance ID"
                + " 000000000000000000009999"));
  }

  @Test
  public void testUploadUISMetrics() {
    // Just a sanity test, ensure no errors/panics as a result of uploading a correct set of metrics
    _envoyInstances.forEach(
        instance -> {
          HttpUtils.getInstance()
              .post()
              .basicAuth(
                  getServerlessMtmGroup().getId().toString(),
                  getServerlessMtmAgentApiKey().getKey())
              .path(
                  "/nds/uis/metrics/%s/%s/%s",
                  getServerlessMtmGroup().getId().toString(),
                  getServerlessMtmCluster().getPoolId().toString(),
                  instance.getId().toString())
              .data(
                  new JSONObject()
                      .put( // 2 counter metrics, 1 with label and 1 without
                          UISPromMetricsView.FieldDefs.COUNTER_METRICS,
                          new JSONArray()
                              .put(
                                  new JSONObject()
                                      .put(
                                          UISPromMetricView.FieldDefs.METRIC_NAME,
                                          AtlasUISPromHandlerSvc.FieldDefs
                                              .UIS_COUNTER_CONFIG_RELOADS_TOTAL)
                                      .put(UISPromMetricView.FieldDefs.METRIC_COUNT, 10))
                              .put(
                                  new JSONObject()
                                      .put(
                                          UISPromMetricView.FieldDefs.METRIC_NAME,
                                          AtlasUISPromHandlerSvc.FieldDefs
                                              .UIS_COUNTER_AUTH_AUTHENTICATE_TOTAL)
                                      .put(UISPromMetricView.FieldDefs.METRIC_COUNT, 10)
                                      .put(
                                          UISPromMetricView.FieldDefs.METRIC_LABELS,
                                          new JSONArray()
                                              .put(
                                                  new JSONObject()
                                                      .put(
                                                          UISPromMetricsLabelView.FieldDefs
                                                              .LABEL_NAME,
                                                          AtlasUISPromHandlerSvc.FieldDefs
                                                              .MMS_UIS_AUTH_MECHANISM_LABEL)
                                                      .put(
                                                          UISPromMetricsLabelView.FieldDefs
                                                              .LABEL_VALUE,
                                                          "X509")))))
                      .put( // 1 summary metric (with both count and metric filled)
                          UISPromMetricsView.FieldDefs.SUMMARY_METRICS,
                          new JSONArray()
                              .put(
                                  new JSONObject()
                                      .put(
                                          UISPromMetricView.FieldDefs.METRIC_NAME,
                                          AtlasUISPromHandlerSvc.FieldDefs
                                              .UIS_SUMMARY_CONFIG_MMS_UIS_POST_DURATION_SECONDS)
                                      .put(UISPromMetricView.FieldDefs.METRIC_COUNT, 10)
                                      .put(UISPromMetricView.FieldDefs.METRIC_SUM, 100)))
                      .put( // 1 guage metric
                          UISPromMetricsView.FieldDefs.GAUGE_METRICS,
                          new JSONArray()
                              .put(
                                  new JSONObject()
                                      .put(
                                          UISPromMetricView.FieldDefs.METRIC_NAME,
                                          AtlasUISPromHandlerSvc.FieldDefs
                                              .UIS_GAUGE_CONFIG_ACCOUNTS_ACTIVE)
                                      .put(UISPromMetricView.FieldDefs.METRIC_COUNT, 10))))
              .expectedReturnStatus(HttpStatus.SC_OK)
              .send();
        });
  }
}
