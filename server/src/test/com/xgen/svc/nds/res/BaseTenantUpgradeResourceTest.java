package com.xgen.svc.nds.res;

import static com.xgen.svc.mms.util.serverless.ServerlessMetricTestHelpers.generateIntervalOfMetrics;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.metrics._private.dao.serverless.ServerlessClusterMeasurementDao;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessClusterMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessHost.ROLE;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.flex._public.model.FlexCloudProviderContainer;
import com.xgen.cloud.nds.free._public.model.FreeCloudProviderContainer;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.tenantupgrade._private.dao.BaseTenantUpgradeStatusDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantClusterDescriptionUpgradeDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.serverless.ServerlessMetricTestHelpers.HostParams;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;

public abstract class BaseTenantUpgradeResourceTest<T extends BaseTenantUpgradeStatusDao<?>>
    extends JUnit5BaseResourceTest {

  @Inject private TenantClusterDescriptionUpgradeDao _tenantClusterDescriptionUpgradeDao;
  @Inject private T _baseTenantUpgradeStatusDao;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private NDSGroupSvc _groupSvc;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private AutomationMongoDbVersionSvc _versionSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private ServerlessClusterMeasurementDao _serverlessClusterMeasurementDao;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  protected static final String CLUSTER_NAME = "Cluster0";
  protected static final String SERVERLESS_INSTANCE_NAME = "Serverless0";
  protected static final String FLEX_INSTANCE_NAME = "Flex0";

  private Group _group;
  private NDSGroup _ndsGroup;
  private Organization _organization;
  private AppUser _appUser;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _ndsClusterSvc.start();
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "group-001");
    _appUser = MmsFactory.createUser(_group);
    _ndsGroup = _groupSvc.ensureGroup(_group.getId());
    paymentMethodStubber.stubPaymentMethod(_organization.getId(), true);

    final ClusterDescription freeCluster =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig()
                        .setGroupId(_group.getId())
                        .setClusterName(CLUSTER_NAME)))
            .copy()
            .build();
    _clusterDescriptionDao.save(freeCluster);
    createMtmContainerForFreeCluster(freeCluster);

    final ClusterDescription serverlessCluster =
        new ClusterDescription(
            NDSModelTestFactory.getDefaultServerlessClusterDescription(
                _group.getId(), SERVERLESS_INSTANCE_NAME));
    _clusterDescriptionDao.save(serverlessCluster);
    createMtmContainerForServerlessCluster(serverlessCluster);

    // Insert metrics for Serverless instance.
    final List<ServerlessClusterMeasurement> metrics =
        generateIntervalOfMetrics(
            0,
            60,
            Instant.now(),
            Instant.now(),
            serverlessCluster.getUniqueId(),
            List.of(
                Pair.of("h0", new HostParams(4L, 4L, ROLE.PRIMARY)),
                Pair.of("h1", new HostParams(1L, 1L, ROLE.SECONDARY)),
                Pair.of("h2", new HostParams(2L, 2L, ROLE.SECONDARY))));

    _serverlessClusterMeasurementDao.insert(metrics, Retention.MINUTE_DATA_RETENTION);

    final ClusterDescription flexCluster =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription(FLEX_INSTANCE_NAME, _group.getId()));
    _clusterDescriptionDao.save(flexCluster);
    createMtmContainerForFlexCluster(flexCluster);

    final AWSAccount account = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(account);
  }

  protected TenantClusterDescriptionUpgradeDao getTenantClusterDescriptionUpgradeDao() {
    return _tenantClusterDescriptionUpgradeDao;
  }

  protected T getTenantUpgradeStatusDao() {
    return _baseTenantUpgradeStatusDao;
  }

  protected ClusterDescriptionDao getClusterDescriptionDao() {
    return _clusterDescriptionDao;
  }

  protected Group getGroup() {
    return _group;
  }

  protected NDSGroup getNDSGroup() {
    return _ndsGroup;
  }

  protected Organization getOrganization() {
    return _organization;
  }

  protected AppUser getAppUser() {
    return _appUser;
  }

  protected MTMClusterDao getMTMClusterDao() {
    return _mtmClusterDao;
  }

  private void createMtmContainerForFreeCluster(final ClusterDescription freeCluster) {
    final String mtmClusterName = String.format("mtm_%s", freeCluster.getName());
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription(_ndsGroup.getGroupId(), mtmClusterName)
            .append(
                ClusterDescription.FieldDefs.MONGODB_VERSION,
                freeCluster.getMongoDBVersion().getVersion());
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    final FreeCloudProviderContainer freeClusterContainer =
        new FreeCloudProviderContainer(
            NDSModelTestFactory.getFreeContainer()
                .append(
                    "clusterId",
                    new BasicDBObject()
                        .append("clusterName", mtmClusterName)
                        .append("groupId", _ndsGroup.getGroupId()))
                .append("tenantClusterName", freeCluster.getName()));
    _groupSvc.addCloudContainer(_ndsGroup, freeClusterContainer);
  }

  private void createMtmContainerForFlexCluster(final ClusterDescription pFlexCluster) {
    final String mtmClusterName = String.format("mtm_%s", pFlexCluster.getName());
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription(_ndsGroup.getGroupId(), mtmClusterName)
            .append(
                ClusterDescription.FieldDefs.MONGODB_VERSION,
                pFlexCluster.getMongoDBVersion().getVersion());
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    final FlexCloudProviderContainer flexClusterContainer =
        new FlexCloudProviderContainer(
            NDSModelTestFactory.getFlexContainer()
                .append(
                    "clusterId",
                    new BasicDBObject()
                        .append("clusterName", mtmClusterName)
                        .append("groupId", _ndsGroup.getGroupId()))
                .append("tenantClusterName", pFlexCluster.getName()));
    _groupSvc.addCloudContainer(_ndsGroup, flexClusterContainer);
  }

  private void createMtmContainerForServerlessCluster(final ClusterDescription serverlessCluster) {
    final String mtmClusterName = String.format("mtm_%s", serverlessCluster.getName());
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription(_ndsGroup.getGroupId(), mtmClusterName)
            .append(
                ClusterDescription.FieldDefs.MONGODB_VERSION,
                serverlessCluster.getMongoDBVersion().getVersion());
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    getMTMClusterDao()
        .insert(
            new ServerlessMTMCluster(
                new ObjectId(),
                mtmClusterName,
                true,
                1000,
                clusterDescription.getRegionNames().stream().findFirst().get().getProvider(),
                clusterDescription.getRegionNames().stream().findFirst().get(),
                ServerlessInstanceSize.SERVERLESS_V2,
                NDSModelTestFactory.TEST_SERVERLESS_MONGODB_VERSION,
                new ObjectId(),
                new Date(),
                new Date(),
                new Date(),
                new Date(),
                new Date(),
                new ObjectId()));

    final ServerlessCloudProviderContainer serverlessClusterContainer =
        new ServerlessCloudProviderContainer(
            NDSModelTestFactory.getServerlessContainer()
                .append(
                    "clusterId",
                    new BasicDBObject()
                        .append("clusterName", mtmClusterName)
                        .append("groupId", _ndsGroup.getGroupId()))
                .append("tenantClusterName", serverlessCluster.getName()));
    _groupSvc.addCloudContainer(_ndsGroup, serverlessClusterContainer);
  }
}
