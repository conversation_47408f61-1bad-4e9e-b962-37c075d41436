package com.xgen.svc.nds.res;

import static com.xgen.svc.mms.util.serverless.ServerlessMetricTestHelpers.generateIntervalOfMetrics;
import static org.apache.http.HttpStatus.SC_ACCEPTED;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.metrics._private.dao.serverless.ServerlessClusterMeasurementDao;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessClusterMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessHost.ROLE;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessFreeMigrationStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus.ServerlessFreeMigrationState;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.util.serverless.ServerlessMetricTestHelpers.HostParams;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestServerlessClusterDescriptionConfig;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ServerlessFreeMigrationResourceIntTests
    extends BaseTenantUpgradeResourceTest<ServerlessFreeMigrationStatusDao> {

  @Inject private ServerlessFreeMigrationStatusDao _serverlessFreeMigrationStatusDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ServerlessClusterMeasurementDao _serverlessClusterMeasurementDao;
  @Inject private PlanDao _planDao;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testStartServerlessFreeMigration_NonServerlessInstance() {
    // Create cluster upgrade request fails for non-serverless instances as target
    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject nonServerlessJSONObj =
        doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("replicaSet");
    nonServerlessJSONObj.put("groupId", getGroup().getId().toHexString());

    final String endpoint = String.format("/nds/serverlessFreeMigration/%s", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, nonServerlessJSONObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testStartServerlessFreeMigration_noServerlessFreeMigrationStatus() {
    final ObjectId groupId = getNDSGroup().getGroupId();
    final ObjectId uniqueId = new ObjectId();

    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jsonObj = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    jsonObj.put("name", CLUSTER_NAME);
    jsonObj.put("groupId", groupId.toHexString());
    jsonObj.put("uniqueId", uniqueId.toHexString());

    final ClusterDescription cd =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                new TestServerlessClusterDescriptionConfig()
                    .setGroupId(groupId)
                    .setClusterName(CLUSTER_NAME)
                    .setClusterUniqueId(uniqueId)));
    _clusterDescriptionDao.save(cd);

    assertTrue(
        _serverlessFreeMigrationStatusDao.findByName(getGroup().getId(), CLUSTER_NAME).isEmpty());

    final String endpoint = String.format("/nds/serverlessFreeMigration/%s", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.SERVERLESS_INSTANCE_INELIGIBLE_FOR_MIGRATION_TO_FREE.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));

    assertTrue(
        _serverlessFreeMigrationStatusDao.findByName(getGroup().getId(), CLUSTER_NAME).isEmpty());
  }

  @Test
  public void testStartServerlessFreeMigration_notEligible_stateIsActionRequired() {
    final ObjectId groupId = getNDSGroup().getGroupId();
    final ObjectId uniqueId = new ObjectId();

    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jsonObj = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    jsonObj.put("name", CLUSTER_NAME);
    jsonObj.put("groupId", groupId.toHexString());
    jsonObj.put("uniqueId", uniqueId.toHexString());

    final Date createdDate = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();

    final ClusterDescription cd =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                    new TestServerlessClusterDescriptionConfig()
                        .setGroupId(groupId)
                        .setClusterName(CLUSTER_NAME)
                        .setClusterUniqueId(uniqueId))
                .append(FieldDefs.CREATE_DATE, createdDate));

    _clusterDescriptionDao.save(cd);

    final ServerlessFreeMigrationStatus status =
        new ServerlessFreeMigrationStatus(
            cd.getGroupId(),
            cd.getName(),
            cd.getUniqueId(),
            new ObjectId(),
            "mtm1",
            ServerlessFreeMigrationState.ACTION_REQUIRED);
    _serverlessFreeMigrationStatusDao.save(status);

    final String endpoint = String.format("/nds/serverlessFreeMigration/%s", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.SERVERLESS_INSTANCE_INELIGIBLE_FOR_MIGRATION_TO_FREE.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));

    // state should be updated to not eligible
    assertEquals(
        ServerlessFreeMigrationState.NOT_ELIGIBLE,
        _serverlessFreeMigrationStatusDao
            .findByName(cd.getGroupId(), cd.getName())
            .get()
            .getMigrationState());
  }

  @Test
  public void testStartServerlessFreeMigration_notEligible_stateIsNotNeeded() {
    final ObjectId groupId = getNDSGroup().getGroupId();
    final ObjectId uniqueId = new ObjectId();

    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jsonObj = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    jsonObj.put("name", CLUSTER_NAME);
    jsonObj.put("groupId", groupId.toHexString());
    jsonObj.put("uniqueId", uniqueId.toHexString());

    final Date createdDate = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();

    final ClusterDescription cd =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                    new TestServerlessClusterDescriptionConfig()
                        .setGroupId(groupId)
                        .setClusterName(CLUSTER_NAME)
                        .setClusterUniqueId(uniqueId))
                .append(FieldDefs.CREATE_DATE, createdDate));

    _clusterDescriptionDao.save(cd);

    final ServerlessFreeMigrationStatus status =
        new ServerlessFreeMigrationStatus(
            cd.getGroupId(),
            cd.getName(),
            cd.getUniqueId(),
            new ObjectId(),
            "mtm1",
            ServerlessFreeMigrationState.NOT_NEEDED);
    _serverlessFreeMigrationStatusDao.save(status);

    final String endpoint = String.format("/nds/serverlessFreeMigration/%s", getGroup().getId());
    final JSONObject serverlessResponse =
        doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.SERVERLESS_INSTANCE_INELIGIBLE_FOR_MIGRATION_TO_FREE.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));

    // state should be updated to not eligible
    assertEquals(
        ServerlessFreeMigrationState.NOT_ELIGIBLE,
        _serverlessFreeMigrationStatusDao
            .findByName(cd.getGroupId(), cd.getName())
            .get()
            .getMigrationState());
  }

  @Test
  public void testStartServerlessFreeMigration_successful() {
    final ObjectId groupId = getNDSGroup().getGroupId();
    final ObjectId uniqueId = new ObjectId();

    final String defaultEndpoint = "/nds/clusters/" + getNDSGroup().getGroupId() + "/aws/default";
    JSONObject jsonObj = doAuthedJsonGet(getAppUser(), defaultEndpoint).getJSONObject("serverless");
    jsonObj.put("name", CLUSTER_NAME);
    jsonObj.put("groupId", groupId.toHexString());
    jsonObj.put("uniqueId", uniqueId.toHexString());

    final Date createdDate = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();

    final ClusterDescription cd =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                    new TestServerlessClusterDescriptionConfig()
                        .setGroupId(groupId)
                        .setClusterName(CLUSTER_NAME)
                        .setClusterUniqueId(uniqueId))
                .append(FieldDefs.CREATE_DATE, createdDate));

    _clusterDescriptionDao.save(cd);

    final ServerlessFreeMigrationStatus status =
        new ServerlessFreeMigrationStatus(
            cd.getGroupId(),
            cd.getName(),
            cd.getUniqueId(),
            new ObjectId(),
            "mtm1",
            ServerlessFreeMigrationState.ACTION_REQUIRED);
    _serverlessFreeMigrationStatusDao.save(status);

    // fulfills eligibleForFree requirements by having dataSize within limits
    final List<ServerlessClusterMeasurement> tenantMetrics =
        generateIntervalOfMetrics(
            0,
            9,
            Instant.now().minus(10, ChronoUnit.MINUTES),
            Instant.now(),
            cd.getUniqueId(),
            List.of(
                Pair.of("h0", new HostParams(4L, 4L, ROLE.PRIMARY)),
                Pair.of("h1", new HostParams(1L, 1L, ROLE.SECONDARY)),
                Pair.of("h2", new HostParams(2L, 2L, ROLE.SECONDARY))));

    _serverlessClusterMeasurementDao.insert(tenantMetrics, Retention.MINUTE_DATA_RETENTION);

    assertTrue(_planDao.findByGroupId(cd.getGroupId()).isEmpty());

    final String endpoint = String.format("/nds/serverlessFreeMigration/%s", getGroup().getId());

    doAuthedJsonPost(getAppUser(), endpoint, jsonObj, SC_ACCEPTED);

    // a plan was added
    assertEquals(1, _planDao.findByGroupId(cd.getGroupId()).size());

    // state should be updated to pending
    assertEquals(
        ServerlessFreeMigrationState.PENDING,
        _serverlessFreeMigrationStatusDao
            .findByName(cd.getGroupId(), cd.getName())
            .get()
            .getMigrationState());
  }
}
