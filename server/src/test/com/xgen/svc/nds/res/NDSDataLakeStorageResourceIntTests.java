package com.xgen.svc.nds.res;

import static com.amazonaws.util.DateUtils.parseISO8601Date;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Updates.set;
import static com.xgen.svc.nds.res.NDSDataLakeStorageResource.DEFAULT_PIPELINE_RUN_RESPONSE_LIMIT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.jobqueue._public.model.BaseJob;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.JobHandler;
import com.xgen.cloud.common.jobqueue._public.model.JobHandlerEnum;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.ClusterType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.backupjob._public.ui.PolicyItemView;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.externalanalyticsjobhandlers._public.svc.SegmentEventJobHandler;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.util.AWSProviderFactory;
import com.xgen.cloud.nds.common._public.model.NDSSegmentEvent;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.IngestionPipelineDao;
import com.xgen.cloud.nds.datalake._private.dao.IngestionPipelineRunDao;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.datalake._public.model.dls.DLSIngestionSink;
import com.xgen.cloud.nds.datalake._public.model.dls.DatasetRetentionPolicy;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipeline;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipeline.State;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipelineRun;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionSource;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.PeriodicCpsSnapshotSource;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.view.atlas.ingestionPipelines.ApiAtlasIngestionPipelineView;
import com.xgen.svc.mms.api.view.atlas.ingestionPipelines.ApiAtlasIngestionSourceView;
import com.xgen.svc.mms.api.view.atlas.ingestionPipelines.ApiAtlasOnDemandCpsSnapshotSourceView;
import com.xgen.svc.mms.svc.IngestionPipelineAndDatasetCleanupSvc;
import com.xgen.svc.mms.svc.event.DeprecatedSegmentEventJobHandler;
import com.xgen.svc.nds.model.NDSDataLakeStorageTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.dls.CpsSnapshotSourceView;
import com.xgen.svc.nds.model.ui.dls.DatasetRetentionPolicyView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineRunView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineStatsView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineView;
import com.xgen.svc.nds.model.ui.dls.IngestionSourceView;
import com.xgen.svc.nds.model.ui.dls.PeriodicCpsSnapshotSourceView;
import com.xgen.svc.nds.model.ui.dls.TriggerIngestionRequestView;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient.DataLakeAdminApiOperationOutcome;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSDataLakeStorageResourceIntTests extends JUnit5BaseResourceTest {

  private static final ObjectMapper MAPPER = new ObjectMapper();

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private IngestionPipelineDao _ingestionPipelineDao;
  @Inject private IngestionPipelineRunDao _ingestionPipelineRunDao;
  @Inject private BackupJobDao _backupJobDao;
  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private NDSDataSetDao _ndsDataSetDao;
  @Inject private JobsProcessorSvc _jobsProcessorSvc;

  private ObjectId _groupId;
  private Group _group;
  private AppUser _user;
  private AppUser _userReadOnly;
  private ClusterDescription _clusterDescription;
  private final String _clusterName = "testCluster0";
  private ObjectId _policyItemId;
  private List<PolicyItem> _policyItems;
  private final ObjectId _clusterUniqueId = ObjectId.get();

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("TestOrg");
    _group = MmsFactory.createGroup(organization, "Group0");
    _user = MmsFactory.createUser(_group, String.format("<EMAIL>", getUniquifier()));
    _userReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group, String.format("<EMAIL>", getUniquifier()), Role.GROUP_READ_ONLY);
    _groupId = _group.getId();
    _ndsGroupSvc.ensureGroup(_groupId);

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_groupId, _clusterName)
            .append(FieldDefs.DISK_BACKUP_ENABLED, true)
            .append(FieldDefs.UNIQUE_ID, _clusterUniqueId));
    _clusterDescription = _clusterDescriptionDao.findByName(_groupId, _clusterName).orElseThrow();

    // policy item id gets overridden in the create method
    final List<PolicyItem> policyItemList =
        List.of(
            new PolicyItem(
                null, BackupFrequencyType.HOURLY, 6, Duration.ofDays(2), BackupRetentionUnit.DAYS),
            new PolicyItem(
                null, BackupFrequencyType.DAILY, 1, Duration.ofDays(7), BackupRetentionUnit.DAYS));

    _policyItems = insertBackupJob(_clusterDescription, policyItemList);
    _policyItemId = _policyItems.get(0).getId();
    _ingestionPipelineDao.ensureIndexes();

    AWSProviderFactory.registerProvider();
  }

  private String getSnapshotsForClusterUrl(final ObjectId pGroupId, final String pClusterName) {
    return String.format("nds/dls/%s/snapshots/%s", pGroupId, pClusterName);
  }

  private String getCreateOnDemandPipelineRunUrl(
      final ObjectId pGroupId, final String pPipelineName) {
    return String.format("%s/trigger", getIngestionPipelineUrl(pGroupId, pPipelineName));
  }

  private String getIngestionPipelineUrl(final ObjectId pGroupId, final String pName) {
    return String.format(
        "%s/%s",
        getIngestionPipelineUrl(pGroupId), URLEncoder.encode(pName, StandardCharsets.UTF_8));
  }

  private String getIngestionPipelineUrl(final ObjectId pGroupId) {
    return String.format("/nds/dls/%s/pipelines", pGroupId);
  }

  private String getIngestionPipelineRunUrl(
      final ObjectId pGroupId, final String pName, final ObjectId runId) {
    return String.format("%s/pipelineRuns/%s", getIngestionPipelineUrl(pGroupId, pName), runId);
  }

  private JSONObject getIngestionPipelineJson() {
    return getIngestionPipelineJson("dbName", "collectionName");
  }

  private JSONObject getIngestionPipelineJson(final String dbName, final String collectionName) {
    final String name = "pipelineName";
    final ObjectId id = ObjectId.get();
    final JSONObject ingestionSource =
        NDSDataLakeStorageTestFactory.getPeriodicSnapshotSourceJSON(
            _clusterDescription.getGroupId(),
            _clusterDescription.getName(),
            dbName,
            collectionName,
            _policyItemId);

    final JSONArray partitionFields = NDSDataLakeStorageTestFactory.getPartitionFieldsJSON();

    final JSONObject ingestionSink =
        NDSDataLakeStorageTestFactory.getDLSIngestionSinkJSON(partitionFields);

    final JSONArray fieldTransformations =
        NDSDataLakeStorageTestFactory.getFieldTransformationsJSON();

    return NDSDataLakeStorageTestFactory.getIngestionPipelineJSON(
        id, name, _groupId, ingestionSource, ingestionSink, fieldTransformations);
  }

  @Test
  public void testGetPolicyItemsForGroup() {
    // generate expected json string
    final JSONArray policyItemsJSON = getBackupPolicyArray(_policyItems);
    final JSONObject expectedPolicyItemMap = new JSONObject().put(_clusterName, policyItemsJSON);

    // retrieve actual mapping
    final String policyItemUri = String.format("/nds/dls/%s/backupPolicyItems", _groupId);
    final JSONObject policyItemMap = doAuthedJsonGet(_user, policyItemUri);

    assertEquals(expectedPolicyItemMap.toString(), policyItemMap.toString());
  }

  @Test
  public void testGetPolicyItemsForCluster() {

    // hourly policy not allowed
    {
      final List<PolicyItem> expectedBackupPolicy =
          _policyItems.stream()
              .filter(
                  policyItem -> !policyItem.getFrequencyType().equals(BackupFrequencyType.HOURLY))
              .collect(Collectors.toList());
      final JSONArray expectedPolicyItems = getBackupPolicyArray(expectedBackupPolicy);

      // retrieve actual mapping
      final String policyItemUri =
          String.format("/nds/dls/%s/backupPolicyItems/%s", _groupId, _clusterName);
      final JSONArray policyItemMap = doAuthedJsonArrayGet(_user, policyItemUri);

      assertEquals(expectedPolicyItems.toString(), policyItemMap.toString());
    }

    // hourly policy allowed
    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_DATA_LAKE_STORAGE_ALLOW_HOURLY_INGESTION);
      final JSONArray expectedPolicyItems = getBackupPolicyArray(_policyItems);

      // retrieve actual mapping
      final String policyItemUri =
          String.format("/nds/dls/%s/backupPolicyItems/%s", _groupId, _clusterName);
      final JSONArray policyItemMap = doAuthedJsonArrayGet(_user, policyItemUri);

      assertEquals(expectedPolicyItems.toString(), policyItemMap.toString());
    }
  }

  private JSONArray getBackupPolicyArray(final List<PolicyItem> pBackupPolicyItems) {
    final ObjectMapper mapper = CustomJacksonJsonProvider.createObjectMapper();
    final JSONArray policyItemsJSON = new JSONArray();
    pBackupPolicyItems.stream()
        .map(PolicyItemView::new)
        .map(
            v -> {
              try {
                return mapper.writeValueAsString(v);
              } catch (final JsonProcessingException e) {
                e.printStackTrace();
                return "{}";
              }
            })
        .map(JSONObject::new)
        .forEach(policyItemsJSON::put);
    return policyItemsJSON;
  }

  @Test
  public void testGetSnapshotsForCluster() {

    // SUCCESS - no pipelines
    {
      assertTrue(
          doAuthedJsonArrayGet(_userReadOnly, getSnapshotsForClusterUrl(_groupId, _clusterName))
              .isEmpty());
    }

    // cluster does not exist
    {
      String DNEClusterName = "DoesNotExist";
      final JSONObject responseJSON =
          doAuthedJsonGet(
              _userReadOnly,
              getSnapshotsForClusterUrl(_groupId, DNEClusterName),
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          NDSErrorCode.CLUSTER_NOT_FOUND.name(), responseJSON.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          String.format("No cluster named %s in group %s", DNEClusterName, _groupId),
          responseJSON.getString("message"));
    }

    // seed some pipelines
    ObjectId[] allSnapshots = new ObjectId[10];
    for (int i = 0; i < 10; i++) {
      allSnapshots[i] = new ObjectId();
      _backupSnapshotDao.addBackupSnapshot(
          NDSModelTestFactory.generateSnapshotUpdates(
              allSnapshots[i],
              _groupId,
              _clusterName,
              String.format("201%s-07-02T00:00:00Z", i),
              _clusterUniqueId,
              false));
    }

    final JSONArray response =
        doAuthedJsonArrayGet(_userReadOnly, getSnapshotsForClusterUrl(_groupId, _clusterName));
    assertEquals(allSnapshots.length, response.length());

    final List<String> responseViews =
        response.toList().stream()
            .map(Map.class::cast)
            .map(JSONObject::new)
            .map(pJson -> pJson.getString("id"))
            .collect(Collectors.toList());
    // verifies the response is in reverse chronological order according to snapshotInitiationDate
    int i = 9;
    for (String id : responseViews) {
      assertEquals(allSnapshots[i].toString(), id);
      i--;
    }
  }

  @Test
  public void testGetSnapshotsForCluster_sharded() {
    // create cluster description
    final String clusterName = "shardedCluster";
    final ShardedClusterDescription clusterDescription =
        new ShardedClusterDescription.Builder(
                NDSModelTestFactory.getShardedAWSClusterDescription(_groupId, clusterName, 4))
            .setDiskBackupEnabled(true)
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    final JSONObject response =
        doAuthedJsonGet(
            _userReadOnly,
            getSnapshotsForClusterUrl(_groupId, clusterName),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INGESTION_NOT_SUPPORTED_FOR_SHARDED_CLUSTERS.name(),
        response.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testCreateOnDemandPipelineRun() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_DATA_LAKE_STORAGE_DATASET_RETENTION_POLICY);

    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));

    assertEquals(
        Optional.of(ingestionPipeline), _ingestionPipelineDao.find(ingestionPipeline.getId()));

    ObjectId backupSnapshotId = ObjectId.get();
    _backupSnapshotDao.addBackupSnapshot(
        NDSModelTestFactory.generateSnapshotUpdates(
            backupSnapshotId,
            _groupId,
            _clusterName,
            "2020-07-02T00:00:00Z",
            new ObjectId(),
            false));

    final JSONObject datasetRetentionPolicy =
        new JSONObject()
            .put(DatasetRetentionPolicyView.FieldDefs.VALUE, 1)
            .put(
                DatasetRetentionPolicyView.FieldDefs.UNITS,
                DatasetRetentionPolicy.RetentionUnit.DAYS);
    final JSONObject triggerIngestionRequestView =
        new JSONObject()
            .put(TriggerIngestionRequestView.FieldDefs.SNAPSHOT_ID, backupSnapshotId.toHexString())
            .put(
                TriggerIngestionRequestView.FieldDefs.DATASET_RETENTION_POLICY,
                datasetRetentionPolicy);

    // FAIL - read only permission
    doAuthedJsonPost(
        _userReadOnly,
        getCreateOnDemandPipelineRunUrl(_groupId, ingestionPipeline.getName()),
        triggerIngestionRequestView,
        HttpStatus.SC_FORBIDDEN);

    // SUCCESS
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              getCreateOnDemandPipelineRunUrl(_groupId, ingestionPipeline.getName()),
              triggerIngestionRequestView);

      final IngestionPipelineRunView result = safeParsePipelineRun(response);
      final IngestionPipelineRun createdRun =
          _ingestionPipelineRunDao
              .findByPipelineId(ingestionPipeline.getId())
              .collect(Collectors.toList())
              .get(0);
      assertEquals(backupSnapshotId, createdRun.getSnapshotId());
      final IngestionPipelineRunView created = new IngestionPipelineRunView(createdRun);
      assertEquals(created.toString(), result.toString());
      assertNotNull(result.getDatasetRetentionPolicy().getLastModifiedDate());
      assertEquals(
          datasetRetentionPolicy.get(DatasetRetentionPolicyView.FieldDefs.UNITS),
          result.getDatasetRetentionPolicy().getUnits());
      assertEquals(
          datasetRetentionPolicy.getInt(DatasetRetentionPolicyView.FieldDefs.VALUE),
          result.getDatasetRetentionPolicy().getValue());

      assertSegmentEvent("On Demand Ingestion Triggered", _groupId, new BasicDBObject(), 1);
    }

    // FAIL - On-demand pipeline run already exists
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              getCreateOnDemandPipelineRunUrl(_groupId, ingestionPipeline.getName()),
              triggerIngestionRequestView,
              HttpStatus.SC_BAD_REQUEST);
      final String errorCode = response.getString("errorCode");
      assertNotNull(errorCode);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_RUN_IN_PROGRESS.name(), response.getString("errorCode"));

      final String message = response.getString("message");
      assertNotNull(message);
      assertTrue(message.contains(ingestionPipeline.getName()));
    }
  }

  @Test
  public void testCreateIngestionPipeline() throws Exception {

    final JSONObject ingestionPipelineJSON = getIngestionPipelineJson();

    // FAIL - read only permission
    doAuthedJsonPost(
        _userReadOnly,
        getIngestionPipelineUrl(_groupId),
        ingestionPipelineJSON,
        HttpStatus.SC_FORBIDDEN);

    // FAIL - invalid groupId
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              getIngestionPipelineUrl(_groupId),
              getIngestionPipelineJson().put("groupId", ObjectId.get()),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.INVALID_GROUP_ID.name(), response.getString(ApiError.ERROR_CODE_FIELD));
    }

    int successfulCreateCount = 0;
    final ObjectId pipelineId;
    // SUCCESS - group owner
    {
      final JSONObject response =
          doAuthedJsonPost(_user, getIngestionPipelineUrl(_groupId), ingestionPipelineJSON);
      successfulCreateCount += 1;

      final IngestionPipeline result = safeParsePipeline(response).toIngestionPipeline();

      // assert model db insertion
      pipelineId = result.getId();
      final IngestionPipeline created = _ingestionPipelineDao.findById(pipelineId).orElseThrow();
      assertEquals(ceilingDatesForEqualityCheck(result), ceilingDatesForEqualityCheck(created));

      // assert segment create events
      {
        final ObjectId responsePipelineId =
            new ObjectId(response.getString(ApiAtlasIngestionPipelineView.FieldDefs.ID));
        final IngestionSource.IngestionSourceType responseSourceType = result.getSource().getType();
        assertSegmentEvent(
            "Data Lake Pipeline Created",
            _groupId,
            new BasicDBObject("pipeline_id", responsePipelineId)
                .append("pipeline_source_type", responseSourceType.name()),
            1);
      }
    }

    // FAIL - duplicate ingestion pipeline name
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              getIngestionPipelineUrl(_groupId),
              // specifying a different source so if indexes are reordered during creation we don't
              // match INGESTION_PIPELINE_SOURCE_ALREADY_IN_USE first
              getIngestionPipelineJson("otherDB", "otherSource"),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_ALREADY_EXISTS.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // FAIL - duplicate ingestion pipeline source
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              getIngestionPipelineUrl(_groupId),
              getIngestionPipelineJson().put(IngestionPipeline.FieldDefs.NAME, "newPipelineName"),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_SOURCE_ALREADY_IN_USE.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // FAIL - duplicate source groupId/cluster/db/collection
    {
      final JSONObject response =
          doAuthedJsonPost(
              _user,
              getIngestionPipelineUrl(_groupId),
              getIngestionPipelineJson()
                  .put(IngestionPipeline.FieldDefs.NAME, "newPipelineName2")
                  .put(
                      IngestionPipeline.FieldDefs.SOURCE,
                      new JSONObject()
                          .put(
                              ApiAtlasIngestionSourceView.FieldDefs.TYPE,
                              ApiAtlasOnDemandCpsSnapshotSourceView.ON_DEMAND_CPS)
                          .put(ApiAtlasOnDemandCpsSnapshotSourceView.FieldDefs.GROUP_ID, _groupId)
                          .put(
                              ApiAtlasOnDemandCpsSnapshotSourceView.FieldDefs.CLUSTER_NAME,
                              _clusterName)
                          .put(
                              ApiAtlasOnDemandCpsSnapshotSourceView.FieldDefs.DATABASE_NAME,
                              "dbName")
                          .put(
                              ApiAtlasOnDemandCpsSnapshotSourceView.FieldDefs.COLLECTION_NAME,
                              "collectionName")),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_SOURCE_ALREADY_IN_USE.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // delete pipeline
    {
      doAuthedJsonDelete(
          _user,
          getIngestionPipelineUrl(_groupId, ingestionPipelineJSON.getString("name")),
          HttpStatus.SC_OK);

      // assert ingestion document is in deleting state
      final IngestionPipeline deleting = _ingestionPipelineDao.findById(pipelineId).orElseThrow();
      assertTrue(deleting.isDeleted());
      assertEquals(State.DELETING, deleting.getState());
    }

    // SUCCESS - create pipeline with name from previously deleted pipeline
    {
      // create pipeline with same name
      final JSONObject response =
          doAuthedJsonPost(_user, getIngestionPipelineUrl(_groupId), getIngestionPipelineJson());
      successfulCreateCount += 1;
      final IngestionPipeline result = safeParsePipeline(response).toIngestionPipeline();

      // assert segment create events
      {
        final ObjectId responsePipelineId =
            new ObjectId(response.getString(ApiAtlasIngestionPipelineView.FieldDefs.ID));
        final IngestionSource.IngestionSourceType responseSourceType = result.getSource().getType();
        assertSegmentEvent(
            "Data Lake Pipeline Created",
            _groupId,
            new BasicDBObject("pipeline_id", responsePipelineId)
                .append("pipeline_source_type", responseSourceType.name()),
            1);
      }

      // assert model db insertion
      final IngestionPipeline created =
          _ingestionPipelineDao.findById(result.getId()).orElseThrow();
      assertEquals(ceilingDatesForEqualityCheck(result), ceilingDatesForEqualityCheck(created));

      // delete pipeline (again)
      doAuthedJsonDelete(
          _user,
          getIngestionPipelineUrl(_groupId, getIngestionPipelineJson().getString("name")),
          HttpStatus.SC_OK);

      assertTrue(
          _ingestionPipelineDao
              .findActiveOrPausedByGroupIdAndName(_groupId, created.getName())
              .isEmpty());
    }
  }

  private IngestionPipeline ceilingDatesForEqualityCheck(
      final IngestionPipeline pIngestionPipeline) {
    return pIngestionPipeline.toBuilder()
        .createdDate(
            Optional.ofNullable(pIngestionPipeline.getCreatedDate())
                .map(d -> DateUtils.ceiling(d, Calendar.SECOND))
                .orElse(null))
        .lastUpdatedDate(
            Optional.ofNullable(pIngestionPipeline.getLastUpdatedDate())
                .map(d -> DateUtils.ceiling(d, Calendar.SECOND))
                .orElse(null))
        .build();
  }

  @Test
  public void testGetIngestionPipelines() {
    // FAIL - invalid groupId
    {
      doAuthedGetBytes(
          _userReadOnly, getIngestionPipelineUrl(ObjectId.get()), HttpStatus.SC_NOT_FOUND);
    }

    // SUCCESS - no pipelines
    {
      assertTrue(doAuthedJsonArrayGet(_userReadOnly, getIngestionPipelineUrl(_groupId)).isEmpty());
    }

    // seed some pipelines
    final Set<IngestionPipeline> pipelines =
        IntStream.range(0, 10)
            .mapToObj(
                i ->
                    getTestIngestionPipeline(_groupId, _clusterName, _policyItemId).toBuilder()
                        .groupId(_groupId)
                        .name("pipeline" + i)
                        .source(
                            new PeriodicCpsSnapshotSource(
                                _groupId, _clusterName, "db", "col" + i, _policyItemId))
                        .build())
            .collect(Collectors.toSet());
    pipelines.forEach(_ingestionPipelineDao::saveIngestionPipeline);

    // SUCCESS
    {
      final JSONArray response =
          doAuthedJsonArrayGet(_userReadOnly, getIngestionPipelineUrl(_groupId));

      assertEquals(pipelines.size(), response.length());

      final Set<IngestionPipeline> responsePipelines =
          response.toList().stream()
              .map(Map.class::cast)
              .map(JSONObject::new)
              .map(
                  pJson -> {
                    try {
                      return safeParsePipeline(pJson);
                    } catch (final JsonProcessingException pE) {
                      throw new RuntimeException(pE);
                    }
                  })
              .map(IngestionPipelineView::toIngestionPipeline)
              .collect(Collectors.toSet());
      assertEquals(pipelines, responsePipelines);
    }
  }

  @Test
  public void testGetSingleIngestionPipeline() {
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));

    // FAIL - no pipeline found
    {
      final JSONObject response =
          doAuthedJsonGet(
              _userReadOnly,
              getIngestionPipelineUrl(_groupId, "doesnotexist"),
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_NOT_FOUND.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // SUCCESS
    {
      final JSONObject response =
          doAuthedJsonGet(
              _userReadOnly,
              getIngestionPipelineUrl(_groupId, ingestionPipeline.getName()),
              HttpStatus.SC_OK);

      assertEquals(ingestionPipeline.getId().toString(), response.getString("_id"));
    }
  }

  @Test
  public void testDeleteIngestionPipeline() throws Exception {
    // create ingestion pipeline
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    assertEquals(
        Optional.of(ingestionPipeline), _ingestionPipelineDao.find(ingestionPipeline.getId()));

    // deleting with invalid user should fail
    {
      doAuthedJsonDelete(
          _userReadOnly,
          getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
          HttpStatus.SC_FORBIDDEN);
    }

    // deleting with invalid group should fail
    {
      doAuthedJsonDelete(
          _user,
          getIngestionPipelineUrl(ObjectId.get(), ingestionPipeline.getName()),
          HttpStatus.SC_NOT_FOUND);
    }

    // deleting with invalid pipeline name should fail
    {
      doAuthedJsonDelete(
          _user,
          getIngestionPipelineUrl(ingestionPipeline.getGroupId(), "IfYouDontKnowMeByNow"),
          HttpStatus.SC_NOT_FOUND);
    }

    // success
    {
      doAuthedJsonDelete(
          _user,
          getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
          HttpStatus.SC_OK);

      final IngestionPipeline deletedPipeline =
          _ingestionPipelineDao.find(ingestionPipeline.getId()).orElseThrow();
      assertEquals(State.DELETING, deletedPipeline.getState());

      assertSegmentEvent(
          "Data Lake Pipeline Deleted",
          _groupId,
          new BasicDBObject("pipeline_id", deletedPipeline.getId()),
          1);
    }
  }

  @Test
  public void testUpdateIngestionPipeline() throws Exception {

    // create ingestion pipeline
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    final ObjectId groupId = ingestionPipeline.getGroupId();
    final String pipelineName = ingestionPipeline.getName();
    assertEquals(
        Optional.of(ingestionPipeline), _ingestionPipelineDao.find(ingestionPipeline.getId()));

    final JSONObject ingestionPipelineSourceJSON =
        new JSONObject()
            .put(IngestionSourceView.FieldDefs.TYPE, PeriodicCpsSnapshotSourceView.PERIODIC_CPS)
            .put(
                PeriodicCpsSnapshotSourceView.FieldDefs.POLICY_ITEM_ID,
                _policyItemId.toHexString());

    final JSONObject ingestionPipelineUpdateRequestJSON =
        new JSONObject().put(IngestionPipelineView.FieldDefs.SOURCE, ingestionPipelineSourceJSON);

    // updating with an invalid user should fail
    {
      doAuthedJsonPatch(
          _userReadOnly,
          getIngestionPipelineUrl(groupId, pipelineName),
          ingestionPipelineUpdateRequestJSON,
          HttpStatus.SC_FORBIDDEN);
      assertSegmentEvent("Data Lake Pipeline Updated", _groupId, new BasicDBObject(), 0);
    }

    // updating with an invalid group should fail
    {
      doAuthedJsonPatch(
          _user,
          getIngestionPipelineUrl(ObjectId.get(), pipelineName),
          ingestionPipelineUpdateRequestJSON,
          HttpStatus.SC_NOT_FOUND);
      assertSegmentEvent("Data Lake Pipeline Updated", _groupId, new BasicDBObject(), 0);
    }

    // updating with an invalid pipeline name should fail
    {
      final String nameDNE = "IfYouDontKnowMeByNow";
      final JSONObject responseJSON =
          doAuthedJsonPatch(
              _user,
              getIngestionPipelineUrl(groupId, nameDNE),
              ingestionPipelineUpdateRequestJSON,
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          responseJSON.getString(ApiError.ERROR_CODE_FIELD),
          NDSErrorCode.INGESTION_PIPELINE_NOT_FOUND.name());
      assertEquals(
          String.format("No Data Lake Pipeline named %s in group %s.", nameDNE, _groupId),
          responseJSON.getString("message"));
      assertSegmentEvent("Data Lake Pipeline Updated", _groupId, new BasicDBObject(), 0);
    }

    // remove cluster description to test cluster existence check
    _clusterDescriptionDao
        .getDbCollection()
        .remove(
            new BasicDBObject()
                .append(ClusterDescription.FieldDefs.UNIQUE_ID, _clusterDescription.getUniqueId()));

    // cluster does not exist
    {
      final JSONObject responseJSON =
          doAuthedJsonPatch(
              _user,
              getIngestionPipelineUrl(groupId, pipelineName),
              ingestionPipelineUpdateRequestJSON,
              HttpStatus.SC_NOT_FOUND);

      assertEquals(
          NDSErrorCode.CLUSTER_NOT_FOUND.name(), responseJSON.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          String.format("No cluster named %s in group %s", _clusterDescription.getName(), _groupId),
          responseJSON.getString("message"));
      assertSegmentEvent("Data Lake Pipeline Updated", _groupId, new BasicDBObject(), 0);
    }

    // re-insert back
    _clusterDescriptionDao.saveReplicaSafe(_clusterDescription.toDBObject());

    // update a pipeline with an unsupported field - no op
    {
      final JSONObject ingestionPipelineUpdateUnsupportedFieldRequestJSON =
          new JSONObject().put(IngestionPipelineView.FieldDefs.NAME, "NewName");

      final JSONObject responseJSON =
          doAuthedJsonPatch(
              _user,
              getIngestionPipelineUrl(groupId, pipelineName),
              ingestionPipelineUpdateUnsupportedFieldRequestJSON,
              HttpStatus.SC_OK);

      final IngestionPipelineView updatedIngestionPipelineView = safeParsePipeline(responseJSON);

      assertEquals(pipelineName, updatedIngestionPipelineView.getName());

      assertNotEquals(
          ingestionPipeline.getLastUpdatedDate(),
          updatedIngestionPipelineView.getLastUpdatedDate());
      assertSegmentEvent("Data Lake Pipeline Updated", _groupId, new BasicDBObject(), 1);
    }

    // success
    {
      final JSONObject successPipeline =
          new JSONObject()
              .put(
                  IngestionPipelineView.FieldDefs.SOURCE,
                  ingestionPipelineSourceJSON.put(
                      CpsSnapshotSourceView.FieldDefs.CLUSTER_NAME, _clusterName));
      final JSONObject responseJSON =
          doAuthedJsonPatch(
              _user,
              getIngestionPipelineUrl(groupId, pipelineName),
              successPipeline,
              HttpStatus.SC_OK);

      final IngestionPipelineView updatedIngestionPipelineView = safeParsePipeline(responseJSON);

      assertEquals(
          _policyItemId,
          updatedIngestionPipelineView
              .getSource()
              .map(PeriodicCpsSnapshotSourceView.class::cast)
              .flatMap(PeriodicCpsSnapshotSourceView::getPolicyItemId)
              .orElseThrow());

      assertNotEquals(
          ingestionPipeline.getLastUpdatedDate(),
          updatedIngestionPipelineView.getLastUpdatedDate());
      assertSegmentEvent("Data Lake Pipeline Updated", _groupId, new BasicDBObject(), 2);
    }
  }

  @Test
  public void testGetIngestionPipelineRuns() {
    // create ingestion pipeline
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    final String url =
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName())
            + "/pipelineRuns";

    // no runs
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }

    final int defaultLimit = DEFAULT_PIPELINE_RUN_RESPONSE_LIMIT;
    final int totalPipelineRuns = defaultLimit + 10;
    final Date now = DateUtils.setMilliseconds(new Date(), 0);
    createIngestionPipelineRuns(ingestionPipeline, totalPipelineRuns, now);

    // returns default
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          defaultLimit,
          now,
          DateUtils.addHours(now, -(defaultLimit - 1)));
    }

    // limit to 10
    {
      final int limit = 10;
      final JSONArray response =
          doAuthedJsonArrayGet(_user, url + "?limit=" + limit, HttpStatus.SC_OK);
      assertEquals(10, response.length());
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          limit,
          DateUtils.addHours(now, 0),
          DateUtils.addHours(now, -(limit - 1)));
    }

    // limit to 5, skip 15
    {
      final int limit = 5;
      final int skip = 15;
      final JSONArray response =
          doAuthedJsonArrayGet(_user, url + "?limit=" + limit + "&skip=" + skip, HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          limit,
          DateUtils.addHours(now, -skip),
          DateUtils.addHours(now, -(skip + limit - 1)));
    }

    // limit to 0, no limit
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url + "?limit=0", HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          totalPipelineRuns,
          DateUtils.addHours(now, 0),
          DateUtils.addHours(now, -(totalPipelineRuns - 1)));
    }

    // skip all
    {
      final JSONArray response =
          doAuthedJsonArrayGet(_user, url + "?skip=" + totalPipelineRuns, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }

    // created before
    {
      final Date createdBefore = DateUtils.addHours(now, 0);
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              url + "?createdBefore=" + createdBefore.toInstant().toString(),
              HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          defaultLimit,
          DateUtils.addHours(now, -1),
          DateUtils.addHours(now, -defaultLimit));
    }

    // created before + limit
    {
      final int limit = 10;
      final Date createdBefore = DateUtils.addHours(now, 0);
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              url + "?limit=" + limit + "&createdBefore=" + createdBefore.toInstant().toString(),
              HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          10,
          DateUtils.addHours(createdBefore, -1),
          DateUtils.addHours(createdBefore, -limit));
    }

    // created before, limit, and skip
    {
      final int limit = 10;
      final int skip = 5;
      // skip all but the last 10 add one to include boundary
      final Date createdBefore = DateUtils.addHours(now, -totalPipelineRuns + 10 + 1);
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              url
                  + "?limit="
                  + limit
                  + "&skip="
                  + skip
                  + "&createdBefore="
                  + createdBefore.toInstant().toString(),
              HttpStatus.SC_OK);
      // expectedReturn = createdDate(10) - skip(5) => Min(5, limit(10)) => 5
      final int expectedRunCount = 5;
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          expectedRunCount,
          DateUtils.addHours(createdBefore, -(skip + 1)),
          DateUtils.addHours(createdBefore, -(skip + expectedRunCount)));
    }
  }

  @Test
  public void testGetIngestionPipelineRunsWithDatasets() {
    // create ingestion pipeline
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    final String url =
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName())
            + "/pipelineRuns?withDatasets=true";

    // no runs
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }

    final int defaultLimit = DEFAULT_PIPELINE_RUN_RESPONSE_LIMIT;

    // allow large enough runs to test querying on withdatasets
    final int totalPipelineRuns = defaultLimit + 100;
    final Date now = DateUtils.setMilliseconds(new Date(), 0);
    createIngestionPipelineRuns(ingestionPipeline, totalPipelineRuns, now);

    // still no runs
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }

    final int numDatasetIds = defaultLimit + 10;
    final Date datasetIdDateBoundary =
        DateUtils.addHours(now, -(totalPipelineRuns - numDatasetIds));
    final long updatedDocs =
        _ingestionPipelineRunDao.updateManyMajority(
            lte(IngestionPipelineRun.FieldDefs.CREATED_DATE, datasetIdDateBoundary),
            set(IngestionPipelineRun.FieldDefs.DATASET_ID, ObjectId.get()));
    assertEquals(numDatasetIds, updatedDocs);

    // returns default limit
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          defaultLimit,
          DateUtils.addHours(datasetIdDateBoundary, 0),
          DateUtils.addHours(datasetIdDateBoundary, -(defaultLimit - 1)));
    }

    // limit to 10
    {
      final int limit = 10;
      final JSONArray response =
          doAuthedJsonArrayGet(_user, url + "&limit=" + limit, HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          limit,
          DateUtils.addHours(datasetIdDateBoundary, 0),
          DateUtils.addHours(datasetIdDateBoundary, -(limit - 1)));
    }

    // limit to 5, skip 15
    {
      final int limit = 5;
      final int skip = 15;
      final JSONArray response =
          doAuthedJsonArrayGet(_user, url + "&limit=" + limit + "&skip=" + skip, HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          limit,
          DateUtils.addHours(datasetIdDateBoundary, -skip),
          DateUtils.addHours(datasetIdDateBoundary, -(skip + limit - 1)));
    }

    // limit to 0, no limit
    {
      final JSONArray response = doAuthedJsonArrayGet(_user, url + "&limit=0", HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          numDatasetIds,
          DateUtils.addHours(datasetIdDateBoundary, 0),
          DateUtils.addHours(datasetIdDateBoundary, -(numDatasetIds - 1)));
    }

    // skip all
    {
      final JSONArray response =
          doAuthedJsonArrayGet(_user, url + "&skip=" + totalPipelineRuns, HttpStatus.SC_OK);
      assertEquals(0, response.length());
    }

    // created before
    {
      final Date createdBefore = DateUtils.addHours(datasetIdDateBoundary, 0);
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              url + "&createdBefore=" + createdBefore.toInstant().toString(),
              HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          defaultLimit,
          // exclusive beginning
          DateUtils.addHours(datasetIdDateBoundary, -1),
          DateUtils.addHours(datasetIdDateBoundary, -defaultLimit));
    }

    // created before + limit
    {
      final int limit = 10;
      final Date createdBefore = DateUtils.addHours(datasetIdDateBoundary, 0);
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              url + "&limit=" + limit + "&createdBefore=" + createdBefore.toInstant().toString(),
              HttpStatus.SC_OK);
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          limit,
          // exclusive beginning
          DateUtils.addHours(createdBefore, -1),
          DateUtils.addHours(createdBefore, -limit));
    }

    // created before, limit, and skip
    {
      final int limit = 10;
      final int skip = 5;
      // skip all but the last 10 add one to include boundary
      final Date createdBefore = DateUtils.addHours(datasetIdDateBoundary, -numDatasetIds + 10 + 1);
      final JSONArray response =
          doAuthedJsonArrayGet(
              _user,
              url
                  + "&limit="
                  + limit
                  + "&skip="
                  + skip
                  + "&createdBefore="
                  + createdBefore.toInstant().toString(),
              HttpStatus.SC_OK);
      // expectedReturn = createdDate(10) - skip(5) => Min(5, limit(10)) => 5
      final int expectedRunCount = 5;
      assertExpectedPipelineRunsResponse(
          response,
          ingestionPipeline,
          expectedRunCount,
          DateUtils.addHours(createdBefore, -(skip + 1)),
          DateUtils.addHours(createdBefore, -(skip + expectedRunCount)));
    }
  }

  @Test
  public void testGetIngestionPipelineStats() throws Exception {
    // create ingestion pipeline
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    final String url =
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName())
            + "/stats";

    // still no runs
    {
      final JSONObject response = doAuthedJsonGet(_userReadOnly, url, HttpStatus.SC_OK);
      final IngestionPipelineStatsView result = safeParsePipelineStats(response);
      assertEquals(result.getLastUpdatedDate(), ingestionPipeline.getLastUpdatedDate());
      assertEquals(result.getTotalDatasets(), 0);
    }

    // create 50 runs with different create dates and no data sets
    final Date now = DateUtils.setMilliseconds(new Date(), 0);
    IntStream.range(0, 50)
        .mapToObj(
            i ->
                NDSDataLakeStorageTestFactory.getIngestionPipelineRun(ingestionPipeline).toBuilder()
                    .createdDate(DateUtils.addHours(now, -i))
                    .datasetName(null)
                    .dataSetId(null)
                    .build())
        .forEach(_ingestionPipelineRunDao::saveIngestionPipelineRun);

    // still no runs
    {
      final JSONObject response = doAuthedJsonGet(_userReadOnly, url, HttpStatus.SC_OK);
      final IngestionPipelineStatsView result = safeParsePipelineStats(response);
      assertEquals(result.getTotalDatasets(), 0);
    }

    // add datasetId to first 30 runs
    _ingestionPipelineRunDao.updateManyMajority(
        lte(IngestionPipelineRun.FieldDefs.CREATED_DATE, DateUtils.addHours(now, -20)),
        set(IngestionPipelineRun.FieldDefs.DATASET_ID, ObjectId.get()));

    {
      final JSONObject response = doAuthedJsonGet(_userReadOnly, url, HttpStatus.SC_OK);
      final IngestionPipelineStatsView result = safeParsePipelineStats(response);
      assertEquals(result.getTotalDatasets(), 30);
    }
  }

  private void assertExpectedPipelineRunsResponse(
      final JSONArray response,
      final IngestionPipeline ingestionPipeline,
      final int pExpectedNumber,
      final Date pFirstCreatedDate,
      final Date pLastCreatedDate) {
    assertEquals(pExpectedNumber, response.length());
    final List<JSONObject> runs = convertJSONArrayToJSONObjectList(response);
    assertTrue(
        runs.stream()
            .map(run -> run.getString("groupId"))
            .map(ObjectId::new)
            .allMatch(_groupId::equals));
    assertTrue(
        runs.stream()
            .map(run -> run.getString("pipelineId"))
            .map(ObjectId::new)
            .allMatch(ingestionPipeline.getId()::equals));
    // sorted by createdDate descending
    assertEquals(
        runs,
        runs.stream()
            .sorted(
                (o1, o2) ->
                    parseISO8601Date(o2.getString("createdDate"))
                        .compareTo(parseISO8601Date(o1.getString("createdDate"))))
            .collect(Collectors.toList()));
    // verify range of created dates
    final JSONObject firstRun = runs.get(0);
    assertEquals(pFirstCreatedDate, parseISO8601Date(firstRun.getString("createdDate")));
    final JSONObject lastRun = runs.get(runs.size() - 1);
    assertEquals(pLastCreatedDate, parseISO8601Date(lastRun.getString("createdDate")));
  }

  @Test
  public void testUpdateIngestionPipeline_stateTransition() throws Exception {

    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));

    // active -> active - success
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.ACTIVE.name()),
        HttpStatus.SC_OK);
    assertSegmentEvent(
        "Data Lake Pipeline Resumed",
        _groupId,
        new BasicDBObject("pipeline_id", ingestionPipeline.getId()),
        0);

    // active -> paused - success
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.PAUSED.name()),
        HttpStatus.SC_OK);
    assertSegmentEvent(
        "Data Lake Pipeline Paused",
        _groupId,
        new BasicDBObject("pipeline_id", ingestionPipeline.getId()),
        1);

    // paused -> paused - success
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.PAUSED.name()),
        HttpStatus.SC_OK);
    assertSegmentEvent(
        "Data Lake Pipeline Paused",
        _groupId,
        new BasicDBObject("pipeline_id", ingestionPipeline.getId()),
        1);

    // paused -> active - success
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.ACTIVE.name()),
        HttpStatus.SC_OK);
    assertSegmentEvent(
        "Data Lake Pipeline Resumed",
        _groupId,
        new BasicDBObject("pipeline_id", ingestionPipeline.getId()),
        1);

    // active -> deleting - success
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.DELETING.name()),
        HttpStatus.SC_OK);

    // deleted -> paused - error
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.PAUSED.name()),
        HttpStatus.SC_NOT_FOUND);

    // deleted -> active - error
    doAuthedJsonPatch(
        _user,
        getIngestionPipelineUrl(ingestionPipeline.getGroupId(), ingestionPipeline.getName()),
        new JSONObject().put(IngestionPipelineView.FieldDefs.STATE, State.ACTIVE.name()),
        HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void testDeleteIngestionPipelineRunDataSet() throws Exception {
    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    final DataLakeAdminApiOperationOutcome outcome = mock(DataLakeAdminApiOperationOutcome.class);
    doReturn(outcome).when(dataLakeAdminApiClient).disableDataSets(any(), any());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(IngestionPipelineAndDatasetCleanupSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    final NDSDataSet dataSet = NDSDataLakeStorageTestFactory.getDataSetDLS();
    _ndsDataSetDao.createDataSet(dataSet);
    final IngestionPipelineRun run =
        NDSDataLakeStorageTestFactory.getIngestionPipelineRun(ingestionPipeline).toBuilder()
            .dataSetId(dataSet.getId())
            .datasetName(dataSet.getName())
            .state(IngestionPipelineRun.State.IN_PROGRESS)
            .build();
    _ingestionPipelineRunDao.saveIngestionPipelineRun(run);

    // group not found
    {
      doAuthedJsonDelete(
          _user,
          getIngestionPipelineRunUrl(ObjectId.get(), ingestionPipeline.getName(), run.getId()),
          HttpStatus.SC_NOT_FOUND);
    }
    // pipeline name not found
    {
      final JSONObject response =
          doAuthedJsonDelete(
              _user,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), "doesnotexist", run.getId()),
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_NOT_FOUND.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // run not found
    {
      final JSONObject response =
          doAuthedJsonDelete(
              _user,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), ingestionPipeline.getName(), ObjectId.get()),
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_RUN_NOT_FOUND.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // invalid state
    {
      final JSONObject response =
          doAuthedJsonDelete(
              _user,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), ingestionPipeline.getName(), run.getId()),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_RUN_INVALID_STATE_UPDATE.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }

    // set state to DONE
    _ingestionPipelineRunDao.setState(run, IngestionPipelineRun.State.DONE);

    // success
    {
      final JSONObject response =
          doAuthedJsonDelete(
              _user,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), ingestionPipeline.getName(), run.getId()),
              HttpStatus.SC_OK);
      assertEquals(IngestionPipelineRun.State.DATASET_DELETED.name(), response.getString("state"));

      // verify data set state
      assertEquals(
          NDSDataSet.State.DELETING,
          _ndsDataSetDao.findById(dataSet.getId()).map(NDSDataSet::getState).orElseThrow());

      // assert segment
      assertSegmentEvent("Data Lake Pipeline Dataset Deleted", _groupId, new BasicDBObject(), 1);
    }
  }

  @Test
  public void testGetIngestionPipelineRun() {
    final IngestionPipeline ingestionPipeline =
        createIngestionPipeline(getTestIngestionPipeline(_groupId, _clusterName, _policyItemId));
    final NDSDataSet dataSet = NDSDataLakeStorageTestFactory.getDataSetDLS();
    final IngestionPipelineRun run =
        NDSDataLakeStorageTestFactory.getIngestionPipelineRun(ingestionPipeline).toBuilder()
            .dataSetId(dataSet.getId())
            .datasetName(dataSet.getName())
            .state(IngestionPipelineRun.State.IN_PROGRESS)
            .build();
    _ingestionPipelineRunDao.saveIngestionPipelineRun(run);

    // pipeline name not found
    {
      final JSONObject response =
          doAuthedJsonGet(
              _userReadOnly,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), "doesnotexist", run.getId()),
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_NOT_FOUND.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }
    // run not found
    {
      final JSONObject response =
          doAuthedJsonGet(
              _userReadOnly,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), ingestionPipeline.getName(), ObjectId.get()),
              HttpStatus.SC_NOT_FOUND);
      assertEquals(
          NDSErrorCode.INGESTION_PIPELINE_RUN_NOT_FOUND.name(),
          response.getString(ApiError.ERROR_CODE_FIELD));
    }
    // success
    {
      final JSONObject response =
          doAuthedJsonGet(
              _userReadOnly,
              getIngestionPipelineRunUrl(
                  ingestionPipeline.getGroupId(), ingestionPipeline.getName(), run.getId()),
              HttpStatus.SC_OK);

      assertEquals(run.getId().toString(), response.getString("_id"));
    }
  }

  private void assertDeprecatedCommonSegmentFields(
      final NDSSegmentEvent pEvent, final BasicDBObject pParams) {
    assertEquals(pEvent.name(), pParams.getString(NDSSegmentEvent.FieldDefs.EVENT_TYPE));
    assertEquals(
        EventSource.USER.getDisplayText(),
        pParams.getString(NDSSegmentEvent.FieldDefs.EVENT_SOURCE));
  }

  private void assertDeprecatedSegmentEvent(
      final NDSSegmentEvent pEvent, final int pExpectedEventCount) {
    assertDeprecatedSegmentEvent(pEvent, pExpectedEventCount, (p) -> {});
  }

  private void assertDeprecatedSegmentEvent(
      final NDSSegmentEvent pEvent,
      final int pExpectedEventCount,
      final Consumer<List<BasicDBObject>> pAssertAllFunction) {
    final List<Job> segmentEventJobs =
        _jobsProcessorSvc.findAllJobsForHandler(DeprecatedSegmentEventJobHandler.class).stream()
            .filter(
                job ->
                    job.getParameters()
                        .get(NDSSegmentEvent.FieldDefs.EVENT_TYPE)
                        .equals(pEvent.name()))
            .collect(Collectors.toList());

    assertEquals(pExpectedEventCount, segmentEventJobs.size());

    final List<BasicDBObject> allParams =
        segmentEventJobs.stream()
            .map(BaseJob::getParameters)
            .peek(params -> assertDeprecatedCommonSegmentFields(pEvent, params))
            .collect(Collectors.toList());

    pAssertAllFunction.accept(allParams);
  }

  private void assertSegmentEvent(
      final String pEventType,
      final ObjectId pGroupId,
      final BasicDBObject pProperties,
      final int pExpectedEventCount)
      throws Exception {
    final Class<? extends JobHandler> jobHandlerClass =
        Class.forName(JobHandlerEnum.SEGMENT_EVENT_JOB_HANDLER.getValue())
            .asSubclass(SegmentEventJobHandler.class);
    final BasicDBObject allProperties =
        new BasicDBObject(pProperties)
            .append("event_type", pEventType)
            .append("project_id", pGroupId);
    final List<Job> segmentEventJobs =
        _jobsProcessorSvc.findAllJobsForHandler(jobHandlerClass).stream()
            .filter(
                job -> {
                  final BasicDBObject jobProps =
                      (BasicDBObject) job.getParameters().get("properties");
                  final boolean allPropsMatch =
                      allProperties.keySet().stream()
                          .allMatch(
                              key ->
                                  jobProps.get(key) != null
                                      && jobProps.get(key).equals(allProperties.get(key)));
                  return allPropsMatch;
                })
            .toList();

    assertEquals(pExpectedEventCount, segmentEventJobs.size());
  }

  private IngestionPipeline getTestIngestionPipeline(
      final ObjectId pGroupId, final String pClusterName, final ObjectId pPolicyItemId) {
    final IngestionPipeline ingestionPipeline =
        NDSDataLakeStorageTestFactory.getIngestionPipeline(pGroupId, "PipeDream-?!&+*/^}[%$#");

    final PeriodicCpsSnapshotSource source =
        new PeriodicCpsSnapshotSource(pGroupId, pClusterName, "db", "col", pPolicyItemId);

    // Override the partition fields  to remove the type as it is read-only and will not appear in
    // the view during deserialization
    final DLSIngestionSink sourceSink = (DLSIngestionSink) ingestionPipeline.getSink();
    final DLSIngestionSink updatedSink =
        new DLSIngestionSink(
            sourceSink.getMetadataProvider(),
            sourceSink.getMetadataRegion(),
            List.of(new PartitionField("testField", 0)));

    return ceilingDatesForEqualityCheck(
        ingestionPipeline.toBuilder().sink(updatedSink).source(source).build());
  }

  private List<PolicyItem> insertBackupJob(
      final ClusterDescription pClusterDescription, final List<PolicyItem> pPolicyItems) {

    // create matching backup job
    final ObjectId backupJobId =
        _backupJobDao.create(
            pClusterDescription.getGroupId(),
            pClusterDescription.getName(),
            pClusterDescription.getUniqueId(),
            ClusterType.REPLICA_SET,
            null,
            2,
            pPolicyItems,
            true,
            new Date());

    return _backupJobDao.find(backupJobId).stream()
        .flatMap(j -> j.getPolicies().stream())
        .flatMap(p -> p.getPolicyItems().stream())
        .collect(Collectors.toList());
  }

  private IngestionPipeline createIngestionPipeline(final IngestionPipeline pPipeline) {
    return _ingestionPipelineDao.saveIngestionPipeline(pPipeline);
  }

  private IngestionPipelineView safeParsePipeline(final JSONObject pJson)
      throws JsonProcessingException {
    return MAPPER.readValue(pJson.toString(), IngestionPipelineView.class);
  }

  private IngestionPipelineRunView safeParsePipelineRun(final JSONObject pJson)
      throws JsonProcessingException {
    return MAPPER.readValue(pJson.toString(), IngestionPipelineRunView.class);
  }

  private IngestionPipelineStatsView safeParsePipelineStats(final JSONObject pJson)
      throws JsonProcessingException {
    return MAPPER.readValue(pJson.toString(), IngestionPipelineStatsView.class);
  }

  private void createIngestionPipelineRuns(
      final IngestionPipeline pIngestionPipeline, final int pNumberRuns, final Date pNow) {
    IntStream.range(0, pNumberRuns)
        .mapToObj(
            i ->
                NDSDataLakeStorageTestFactory.getIngestionPipelineRun(pIngestionPipeline)
                    .toBuilder()
                    .createdDate(DateUtils.addHours(pNow, -i))
                    .datasetName(null)
                    .dataSetId(null)
                    .build())
        .forEach(_ingestionPipelineRunDao::saveIngestionPipelineRun);
  }
}
