package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.US_EAST_1;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.US_WEST_1;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.BaseHostUtils;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._private.dao.HostClusterDao;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZoneName;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.fts._private.dao.FTSIndexConfigDao;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat.StatusCode;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatusMap;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostStat;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSHostModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.common._public.util.HostnameUtil;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.cloudprovider._public.svc.PartitionGroupSvc;
import com.xgen.cloud.search.decoupled.config._public.model.SearchConfigModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription.State;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentSpec;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentSpecTestFactory;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.decoupled.config._public.util.SearchHostnameManager;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.mongotview.FTSReplicationStatusView;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.GenerateAutomationConfigBaseStep;
import com.xgen.svc.nds.planner.GenerateAutomationConfigForGroupStep;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.serverless.svc.ServerlessLoadBalancingDeploymentSvc;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.bson.types.BasicBSONList;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public abstract class BaseFTSIndexConfigMongotApiResourceIntTest extends JUnit5BaseResourceTest {

  @Inject protected AutomationMongoDbVersionSvc _versionSvc;
  @Inject private NDSClusterSvc _clusterSvc;
  @Inject protected PartitionGroupSvc _partitionGroupSvc;
  @Inject protected SearchDeploymentDescriptionSvc _searchDeploymentDescriptionSvc;
  @Inject protected HostClusterDao _hostClusterDao;
  @Inject protected AppSettings _appSettings;
  @Inject protected NDSGroupSvc _ndsGroupSvc;
  @Inject protected NDSGroupDao _ndsGroupDao;
  @Inject protected ClusterDescriptionDao _clusterDescriptionDao;
  @Inject protected ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject protected FTSIndexConfigSvc _indexSvc;
  @Inject protected FTSIndexConfigDao _indexDao;
  @Inject protected AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject protected PlanDao _planDao;
  @Inject protected HostDao _hostDao;
  @Inject protected NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;
  @Inject protected NDSPlanContext.Factory _ndsPlanContextFactory;
  @Inject protected AuditSvc _auditSvc;
  @Inject protected ServerlessLoadBalancingDeploymentSvc _serverlessLoadBalancingDeploymentSvc;
  @Inject protected SearchInstanceSvc _searchInstanceSvc;

  protected Group _group;
  protected NDSGroup _ndsGroup;
  protected ClusterDescription _cluster;
  protected ClusterDescription _serverlessInstance;
  protected ReplicaSetHardware _hardware;
  protected List<PartitionGroup> _mongotPartitionGroups;
  protected ReplicaSetHardware _serverlessHardware;
  protected AgentApiKey _apiKey;
  protected String _primaryHostname;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _clusterSvc.start();

    Organization _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "cus_0001");
    AppUser _appUser = MmsFactory.createAutomationAdminUser(_group);
    _apiKey = MmsFactory.generateApiKey(_group.getId(), _appUser.getId());
    _ndsGroup = _ndsGroupSvc.ensureGroup(_group.getId());

    publishDefaultAutomationConfig(_organization, _group);
  }

  protected void setupServerlessInstance() {
    final String serverlessInstanceName = "serverless1";
    _serverlessInstance =
        new ClusterDescription(
            NDSModelTestFactory.getDefaultServerlessClusterDescription(
                    _group.getId(), serverlessInstanceName)
                .append(ClusterDescription.FieldDefs.MONGODB_VERSION, "4.2.0"));
    _clusterDescriptionDao.save(_serverlessInstance);
    final BasicDBObject serverlessHardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, new ObjectId(), _serverlessInstance);
    _serverlessHardware = new ReplicaSetHardware(serverlessHardwareDoc);
    _replicaSetHardwareDao.saveReplicaSafe(serverlessHardwareDoc);
  }

  protected void setupAzureSingleRegionCluster() {
    final String clusterName = "cluster1";
    _cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(_group.getId(), clusterName)
                .append(ClusterDescription.FieldDefs.MONGODB_VERSION, "4.2.0"));
    _clusterDescriptionDao.save(_cluster);

    final AzureCloudProviderContainer azureContainer =
        new AzureCloudProviderContainer(ObjectId.get(), AzureRegionName.US_EAST);
    final ObjectId cloudContainerId = _ndsGroupSvc.addCloudContainer(_ndsGroup, azureContainer);

    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, cloudContainerId, _cluster);
    _hardware = new ReplicaSetHardware(hardwareDoc);
    _replicaSetHardwareDao.saveReplicaSafe(hardwareDoc);
    setHostTypeForTests();
  }

  protected void setupAwsSingleRegionCluster() {
    final String clusterName = "cluster1";
    _cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName)
                .append(ClusterDescription.FieldDefs.MONGODB_VERSION, "4.2.0"));
    _clusterDescriptionDao.save(_cluster);

    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(ObjectId.get(), US_EAST_1);
    final ObjectId cloudContainerId = _ndsGroupSvc.addCloudContainer(_ndsGroup, awsContainer);

    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, cloudContainerId, _cluster);
    _hardware = new ReplicaSetHardware(hardwareDoc);
    _replicaSetHardwareDao.saveReplicaSafe(hardwareDoc);
    setHostTypeForTests();
  }

  protected void setHostTypeForTests() {
    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareDao.findById(_hardware.getId()).get();
    replicaSetHardware
        .getHardware()
        .forEach(
            instanceHardware -> {
              _hostDao.create(
                  instanceHardware.getHostnameForAgents().get(),
                  NDSDefaults.MONGOD_PUBLIC_PORT,
                  _group.getId(),
                  null);
            });
    _primaryHostname = replicaSetHardware.getHardware().get(0).getHostnameForAgents().get();
    _hostDao.setHostTypeForTests(
        _group.getId(),
        _hostDao.getHostId(_group.getId(), _primaryHostname, 27017),
        HostType.REPLICA_PRIMARY,
        BaseHostUtils.assembleHostnameAndPort(_primaryHostname, 27017));
    _hostDao.setHostTypeForTests(
        _group.getId(),
        _hostDao.getHostId(
            _group.getId(),
            replicaSetHardware.getHardware().get(1).getHostnameForAgents().get(),
            27017),
        HostType.REPLICA_SECONDARY,
        BaseHostUtils.assembleHostnameAndPort(
            replicaSetHardware.getHardware().get(1).getHostnameForAgents().get(), 27017));
    _hostDao.setHostTypeForTests(
        _group.getId(),
        _hostDao.getHostId(
            _group.getId(),
            replicaSetHardware.getHardware().get(2).getHostnameForAgents().get(),
            27017),
        HostType.REPLICA_SECONDARY,
        BaseHostUtils.assembleHostnameAndPort(
            replicaSetHardware.getHardware().get(2).getHostnameForAgents().get(), 27017));
  }

  protected void setUpAwsDecoupledSearchNodes(Cluster cluster) {
    // Add the mongod cluster host info to the db.
    NDSHostModelTestFactory.getHostClusters(_group.getId(), cluster, true)
        .forEach(_hostClusterDao::save);

    SearchDeploymentDescription searchDeploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            _group.getId(),
            cluster.getClusterDescription().getUniqueId(),
            1,
            cluster.getClusterDescription().getRegionNames().stream().toList(),
            null);
    _searchDeploymentDescriptionSvc.create(searchDeploymentDescription);
    SearchDeploymentSpec searchDeploymentSpec =
        SearchDeploymentSpecTestFactory.getSearchDeploymentSpec(
            searchDeploymentDescription, cluster);
    searchDeploymentSpec
        .getPartitionGroupSpecs()
        .forEach(
            partitionGroupSpec -> {
              var regionName = partitionGroupSpec.getNodeHardwareDescription().getRegionName();
              PartitionGroup partitionGroup =
                  _partitionGroupSvc.create(searchDeploymentSpec, partitionGroupSpec);
              List<SearchInstance> searchInstances =
                  _partitionGroupSvc.addInstancesToPartitionGroup(
                      partitionGroup.getId(),
                      SearchConfigModelTestFactory.DEFAULT_AWS_INSTANCE_SIZE,
                      regionName,
                      Set.of(0, 1),
                      AWSInstanceFamily.C6G,
                      OS.AL2,
                      SearchInstance.EncryptionAtRest.DISABLED);
              searchInstances.forEach(
                  searchInstance -> {
                    InstanceHardware instanceHardware = searchInstance.getInstanceHardware();
                    _partitionGroupSvc.setInstanceHardwareAWSProvisionedFields(
                        partitionGroup.getId(),
                        instanceHardware.getInstanceId(),
                        instanceHardware.getCloudContainerId(),
                        instanceHardware.getDiskSizeGB().orElse(0),
                        instanceHardware.getInstanceSize().orElse(null),
                        "M4",
                        CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                            .getInstanceHardwareProvider()
                            .getDefaultOs(_appSettings, AWSInstanceFamily.M4),
                        "eipalloc-1234",
                        "i-2134",
                        "subnet-1234",
                        new Hostnames(
                            SearchHostnameManager.buildHostnamesForPartitionGroupMember(
                                cluster.getClusterDescription().getClusterNamePrefix(),
                                partitionGroupSpec.getReplicaSetHardwareSpec().getRsId(),
                                searchInstance.getDnsId(),
                                "grp123",
                                "mongodb.net")),
                        null,
                        false,
                        "/dev/localdisk0n1",
                        "test-image-version");
                  });
            });
    _mongotPartitionGroups =
        _partitionGroupSvc.getAllPartitionGroupsForDeployment(searchDeploymentDescription.getId());
  }

  protected void setUpAzureDecoupledSearchNodes(Cluster cluster) {
    // Add the mongod cluster host info to the db.
    NDSHostModelTestFactory.getHostClusters(_group.getId(), cluster, true)
        .forEach(_hostClusterDao::save);
    SearchDeploymentDescription searchDeploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAzure(
            _group.getId(),
            cluster.getClusterDescription().getUniqueId(),
            State.IDLE,
            1,
            SearchConfigModelTestFactory.DEFAULT_AZURE_INSTANCE_SIZE);
    _searchDeploymentDescriptionSvc.create(searchDeploymentDescription);
    SearchDeploymentSpec searchDeploymentSpec =
        SearchDeploymentSpecTestFactory.getSearchDeploymentSpec(
            searchDeploymentDescription, cluster);
    searchDeploymentSpec
        .getPartitionGroupSpecs()
        .forEach(
            partitionGroupSpec -> {
              var regionName = partitionGroupSpec.getNodeHardwareDescription().getRegionName();
              PartitionGroup partitionGroup =
                  _partitionGroupSvc.create(searchDeploymentSpec, partitionGroupSpec);
              List<SearchInstance> searchInstances =
                  _partitionGroupSvc.addInstancesToPartitionGroup(
                      partitionGroup.getId(),
                      SearchConfigModelTestFactory.DEFAULT_AZURE_INSTANCE_SIZE,
                      regionName,
                      Set.of(0, 1),
                      AzureInstanceFamily.STANDARD_DLDSV5,
                      OS.AL2,
                      SearchInstance.EncryptionAtRest.DISABLED);
              searchInstances.forEach(
                  searchInstance -> {
                    InstanceHardware instanceHardware = searchInstance.getInstanceHardware();
                    _partitionGroupSvc.setInstanceHardwareAzureProvisionedFields(
                        partitionGroup.getId(),
                        instanceHardware.getInstanceId(),
                        instanceHardware.getCloudContainerId(),
                        instanceHardware.getDiskSizeGB().orElse(0),
                        instanceHardware.getInstanceSize().orElse(null),
                        AzureInstanceFamily.STANDARD_DLDSV5.getName(),
                        "azure-instance-name",
                        "azure-nic-name",
                        "azure-public-ip-name",
                        CloudProviderRegistry.getByCloudProvider(CloudProvider.AZURE)
                            .getInstanceHardwareProvider()
                            .getDefaultOs(_appSettings, AzureInstanceFamily.STANDARD_DLDSV5),
                        "os-disk-name",
                        new Hostnames(
                            SearchHostnameManager.buildHostnamesForPartitionGroupMember(
                                cluster.getClusterDescription().getClusterNamePrefix(),
                                partitionGroupSpec.getReplicaSetHardwareSpec().getRsId(),
                                searchInstance.getDnsId(),
                                "grp123",
                                "mongodb.net")),
                        "123.456.789.0",
                        AzureNDSDefaults.SEARCH_DATA_DEVICE,
                        AzureAvailabilityZoneName.ZONE_1.toString(),
                        null,
                        "test-os-version");
                  });
            });
    _mongotPartitionGroups =
        _partitionGroupSvc.getAllPartitionGroupsForDeployment(searchDeploymentDescription.getId());
  }

  // Helper to get the mongot hostname from the PartitionGroup collection.
  protected String getDecoupledMongotHostname() {
    return getDecoupledMongotHostnames(_mongotPartitionGroups.get(0)).get(0);
  }

  protected List<String> getDecoupledMongotHostnames(PartitionGroup pPartitionGroup) {
    return pPartitionGroup.getInstances().stream()
        .map(searchInstance -> searchInstance.getInstanceHardware().getHostnameForAgents())
        .flatMap(Optional::stream)
        .collect(Collectors.toList());
  }

  protected String targetMongodHostname(List<String> mongodHostnames, String mongotHostname) {
    // Hardcoding the host count based on temporary logic used to get the mongod host.
    int mongodHostCount = 3;
    return mongodHostnames.get(Math.floorMod(mongotHostname.hashCode(), mongodHostCount));
  }

  protected void publishDefaultAutomationConfig(final Organization pOrg, final Group pGroup)
      throws SvcException {
    final Plan plan = new Plan(pGroup.getId(), _ndsPlanContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);
    final GenerateAutomationConfigForGroupStep step =
        new GenerateAutomationConfigForGroupStep(
            _ndsPlanContextFactory.create(pGroup.getId(), plan.getId()),
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                0,
                plan.getPlanContext().getPlanDao()),
            _ndsGroupMaintenanceSvc,
            _ndsGroupDao,
            _ndsGroup,
            _group,
            _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId()),
            _clusterSvc,
            _serverlessLoadBalancingDeploymentSvc,
            _searchInstanceSvc);

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();
    final AppUser user = MmsFactory.createUser(pGroup);
    _automationConfigSvc.saveDraft(resultConfig, user, pOrg, pGroup);
    _automationConfigSvc.publish(pOrg, pGroup, user);
  }

  protected BasicBSONList generateIndexes(final String pHostName, final int pNumIndexes)
      throws SvcException {
    final BasicBSONList indexesFromMongot = new BasicBSONList();
    final List<FTSIndex> indexes = new ArrayList<>();
    for (int i = 0; i < pNumIndexes; i++) {
      final ObjectId indexId = new ObjectId();
      final UUID uuid = UUID.randomUUID();
      final String collectionName = "coll" + i;
      final String indexName = "index" + HostnameUtil.getHostnameWithoutDomain(pHostName) + i;
      final FTSIndexStatusMap<FTSIndexHostStat> stats = new FTSIndexStatusMap<>();
      stats.put(
          HostnameUtil.getHostnameWithoutDomain(pHostName),
          FTSSearchIndexHostStat.builder()
              .statusCode(FTSIndexHostStat.StatusCode.NOT_STARTED)
              .build());
      final FTSSearchIndex ftsIndex =
          new FTSSearchIndex.Builder()
              .setIndexId(indexId)
              .setName(indexName)
              .setDatabase("test")
              .setLastObservedCollectionName(collectionName)
              .setMappings(new BasicDBObject("dynamic", true))
              .setCollectionUUID(uuid)
              .setStats(stats)
              .build();
      indexes.add(ftsIndex);
      final FTSIndexHostStat updatedHostStat =
          FTSSearchIndexHostStat.builder()
              .optime(1L)
              .statusCode(FTSIndexHostStat.StatusCode.INITIAL_SYNC)
              .dataSize(10.0)
              .numOfDocs(10)
              .build();
      final JSONObject indexStatsMongotView =
          new JSONObject(Map.of(pHostName, new JSONObject(updatedHostStat.toDBObject().toMap())));
      final JSONObject indexDefinitionView =
          new JSONObject(
              Map.of(
                  "indexID",
                  indexId.toString(),
                  "lastObservedCollectionName",
                  collectionName,
                  "collectionUUID",
                  uuid.toString()));
      indexesFromMongot.add(
          new JSONObject(Map.of("definition", indexDefinitionView, "stats", indexStatsMongotView)));
    }
    _indexSvc.createSearchIndexManagementCommand(_group.getId(), _cluster.getName(), indexes, null);
    _indexSvc.ensureFTSIndexConfig(_group.getId(), _cluster.getName());
    return indexesFromMongot;
  }

  protected Map<String, Object> generateMongotRequest(
      String hostname, FTSIndexHostStat.StatusCode statusCode, FTSIndex index) {

    final FTSIndexHostStat updatedHostStat =
        FTSSearchIndexHostStat.builder()
            .optime(1L)
            .statusCode(statusCode)
            .statusDetail(
                FTSSearchIndexHostStat.StatusDetail.builder()
                    .index(statusCode)
                    .synonyms(
                        List.of(
                            FTSSearchIndexHostStat.SynonymStatus.builder()
                                .name("synonym1")
                                .status(
                                    FTSSearchIndexHostStat.SynonymStatus.StatusCode.SYNC_ENQUEUED)
                                .build()))
                    .build())
            .dataSize(10.0)
            .numOfDocs(10)
            .build();
    final JSONObject indexStatsMongotView =
        new JSONObject(Map.of(hostname, new JSONObject(updatedHostStat.toDBObject().toMap())));
    final JSONObject indexDefinitionView =
        new JSONObject(
            Map.of(
                "indexID",
                index.getIndexId().toString(),
                "type",
                index.getActualType().getStringValue(),
                "lastObservedCollectionName",
                "foo",
                "collectionUUID",
                index.getCollectionUUID().orElseThrow().toString()));
    final JSONObject indexDetailedStatusesMongotView =
        new JSONObject(
            Map.of(hostname, getHostDetailedStatusesJsonObject(indexDefinitionView, statusCode)));
    final List<JSONObject> mongotView =
        List.of(
            new JSONObject(
                Map.of(
                    "definition", indexDefinitionView,
                    "stats", indexStatsMongotView,
                    "detailedStatuses", indexDetailedStatusesMongotView)));
    if (index.getActualType() == FTSIndex.Type.SEARCH) {
      return Map.of("hostname", hostname, "indexes", new JSONArray(mongotView));
    } else {
      return Map.of("hostname", hostname, "vectorIndexes", new JSONArray(mongotView));
    }
  }

  protected Map<String, Object> generateMongotRequestWithReplicationStatus(
      String hostname,
      FTSIndexHostStat.StatusCode statusCode,
      FTSIndex index,
      FTSReplicationStatusView ftsReplicationStatusView) {
    Map<String, Object> mongotRequest =
        new HashMap<>(generateMongotRequest(hostname, statusCode, index));
    mongotRequest.put(
        "replicationStatus",
        new JSONObject(
            Map.of(
                "startTime", ftsReplicationStatusView.getStartTime(),
                "lastUpdatedTime", ftsReplicationStatusView.getLastUpdatedTime(),
                "isReplicationStopped", ftsReplicationStatusView.getIsReplicationStopped())));
    return mongotRequest;
  }

  protected Cluster setupAwsSingleRegionShardedCluster() {
    final ShardedClusterDescription shardedCluster =
        new ShardedClusterDescription(
            NDSModelTestFactory.getShardedAWSClusterDescription(
                    _group.getId(), "sharded-cluster", 2)
                .append(ClusterDescription.FieldDefs.MONGODB_VERSION, "4.2.0"));
    _clusterDescriptionDao.save(shardedCluster);

    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(ObjectId.get(), US_EAST_1);
    final ObjectId cloudContainerId = _ndsGroupSvc.addCloudContainer(_ndsGroup, awsContainer);

    List<ReplicaSetHardware> hardwares =
        IntStream.range(0, shardedCluster.getNumShards())
            .mapToObj(
                shardIndex -> {
                  final BasicDBObject hardwareDoc =
                      ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                          shardIndex, cloudContainerId, shardedCluster);
                  final ReplicaSetHardware hardware = new ReplicaSetHardware(hardwareDoc);
                  _replicaSetHardwareDao.saveReplicaSafe(hardwareDoc);
                  hardware
                      .getHardware()
                      .forEach(
                          instanceHardware -> {
                            _hostDao.create(
                                instanceHardware.getHostnameForAgents().get(),
                                NDSDefaults.MONGOD_PUBLIC_PORT,
                                _group.getId(),
                                null);
                            _hostDao.setHostTypeForTests(
                                _group.getId(),
                                _hostDao.getHostId(
                                    _group.getId(),
                                    instanceHardware.getHostnameForAgents().get(),
                                    27017),
                                HostType.REPLICA_SECONDARY,
                                BaseHostUtils.assembleHostnameAndPort(
                                    instanceHardware.getHostnameForAgents().get(), 27017));
                          });
                  return hardware;
                })
            .collect(Collectors.toList());
    final BasicDBObject configHardwareDoc =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(shardedCluster);
    final ReplicaSetHardware configHardware = new ReplicaSetHardware(configHardwareDoc);
    _replicaSetHardwareDao.saveReplicaSafe(configHardwareDoc);
    hardwares.add(configHardware);
    return Cluster.getCluster(shardedCluster, hardwares);
  }

  protected Cluster setupAwsMultiRegionCluster() {
    final ClusterDescription multiRegionCluster =
        new ClusterDescription(
            NDSModelTestFactory.getMultiRegionAWSClusterDescription(
                    _group.getId(), "multi-region-cluster")
                .append(ClusterDescription.FieldDefs.MONGODB_VERSION, "4.2.0"));

    _clusterDescriptionDao.save(multiRegionCluster);

    final AWSCloudProviderContainer awsContainer1 =
        new AWSCloudProviderContainer(ObjectId.get(), US_EAST_1);
    final ObjectId cloudContainerId1 = _ndsGroupSvc.addCloudContainer(_ndsGroup, awsContainer1);

    final AWSCloudProviderContainer awsContainer2 =
        new AWSCloudProviderContainer(ObjectId.get(), US_WEST_1);
    final ObjectId cloudContainerId2 = _ndsGroupSvc.addCloudContainer(_ndsGroup, awsContainer2);

    final BasicDBObject hardwareDoc =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0,
            List.of(cloudContainerId1, cloudContainerId2, cloudContainerId2),
            multiRegionCluster,
            List.of());
    final ReplicaSetHardware hardware = new ReplicaSetHardware(hardwareDoc);
    _replicaSetHardwareDao.saveReplicaSafe(hardwareDoc);

    hardware
        .getHardware()
        .forEach(
            instanceHardware -> {
              _hostDao.create(
                  instanceHardware.getHostnameForAgents().get(),
                  NDSDefaults.MONGOD_PUBLIC_PORT,
                  _group.getId(),
                  null);
            });
    return Cluster.getCluster(multiRegionCluster, List.of(hardware));
  }

  protected static JSONObject getHostDetailedStatusesJsonObject(
      JSONObject indexDefinitionView, StatusCode statusCode) {
    return new JSONObject(
        Map.of(
            "mainIndex",
            new JSONObject(
                Map.of("status", statusCode.toString(), "definition", indexDefinitionView)),
            "stagedIndex",
            new JSONObject(
                Map.of("status", statusCode.toString(), "definition", indexDefinitionView))));
  }
}
