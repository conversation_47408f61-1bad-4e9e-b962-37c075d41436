package com.xgen.svc.nds.res;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzurePeerNetwork;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.azure.AzureMultiTenantExternalIntTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

/**
 * The validate call in NDSPeerResource relies on calling an external API (the Azure HTTP API) to
 * ensure that Atlas can access a customer's Azure account. Because this test relies on the
 * existence of certain resources outside of the Atlas DB, the validate test is included in this
 * separate set of external integration tests off of NDSPeerResourceIntTests.
 *
 * <p>TODO (CLOUDP-309317): To be fixed in CLOUDP-309317. These tests have been broken for a long
 * time, as our task generation script has not picked it up to run until recently.
 */
@Ignore
public class NDSAzurePeerResourceExternalIntTests extends AzureMultiTenantExternalIntTest {

  private static final String AZURE_PROVIDER = "AZURE";

  @Inject private NDSGroupDao _groupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  private Group _group;
  private AppUser _user;
  private String _urlPrefix;
  private CloudProviderContainer _container;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createUser(_group);
    _urlPrefix = String.format("/nds/%s/peers", _group.getId());

    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
  }

  @Test(timeout = 10 * 60 * 1000L)
  public void testValidate() throws Exception {
    setupAzure();

    final String endpoint = _urlPrefix + "/validate";

    // Validate with an account that has not been configured for multi-tenant access
    final JSONObject message1 =
        new JSONObject()
            .put("azureSubscriptionId", "subscription1234")
            .put("azureDirectoryId", "tenant1234")
            .put("resourceGroupName", "rg1234")
            .put("vnetName", "vnet1234")
            .put("containerId", _container.getId())
            .put("@provider", AZURE_PROVIDER);
    try {
      doAuthedJsonPost(_user, endpoint, message1);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE.getMessage().contains(NDSErrorCode.AZURE_CUSTOMER_NETWORK_UNREACHABLE.name()));
    }

    // Validate with an account that has been configured for multi-tenant access
    final AzurePeerNetwork customerNetwork = getCustomerNetwork();

    final JSONObject message2 =
        new JSONObject()
            .put("azureSubscriptionId", customerNetwork.getAzureSubscriptionId())
            .put("azureDirectoryId", customerNetwork.getAzureDirectoryId())
            .put("resourceGroupName", customerNetwork.getResourceGroupName())
            .put("vnetName", customerNetwork.getVnetName())
            .put("containerId", _container.getId())
            .put("@provider", AZURE_PROVIDER);
    doAuthedJsonPost(_user, endpoint, message2);
  }

  private void setupAzure() {
    final AzureCloudProviderContainer azureCloudProviderContainer =
        new AzureCloudProviderContainer(
            NDSModelTestFactory.getAzureContainer()
                .append("azureSubscriptionId", getAzureSubscription().getId()));
    _groupDao.addCloudContainer(_group.getId(), azureCloudProviderContainer);

    // Private IP mode needs to be enabled for peers to be added
    _groupDao.setPrivateIpMode(_group.getId(), true);

    // Refresh the container from the database because addCloudContainer randomizes the id
    _container = _groupDao.find(_group.getId()).get().getCloudProviderContainers().get(0);
  }
}
