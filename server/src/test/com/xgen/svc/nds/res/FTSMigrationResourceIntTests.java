package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndex.Type;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat.StatusCode;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatusMap;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexGenerationDetail;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndexGenerationDetail;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndexHostDetailedStatuses;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.search.decoupled.config._public.model.Migration;
import com.xgen.cloud.search.decoupled.config._public.model.SearchConfigModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.decoupled.test.DecoupledAPITestUtils;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.view.ApiAtlasFtsIndexHostStatsView;
import com.xgen.svc.mms.api.view.ApiAtlasFtsIndexMigrationTargetView;
import com.xgen.svc.mms.api.view.ApiAtlasFtsIndexMigrationTargetView.State;
import com.xgen.svc.mms.api.view.ApiAtlasFtsIndexStatsView;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.function.BiFunction;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class FTSMigrationResourceIntTests extends JUnit5BaseResourceTest {

  private static final String CLUSTER_NAME = "ftsCluster";
  private static final String DEPLOYMENT_URL = "/nds/clusters/%s/%s/search/migration";
  private static final String INDEX_MIGRATION_STATUS_URL = DEPLOYMENT_URL + "/indexes/%s";
  private static final String CLUSTER_MIGRATION_TARGET_URL = DEPLOYMENT_URL + "/target";
  private static final String DEDICATED_HOST_A = "a1-shard-121-search-abc1";
  private static final String DEDICATED_HOST_B = "b2-shard-834-search-abc2";
  private static final String DEDICATED_HOST_C = "c3-shard-624-search-abc3";

  @Inject private SearchDeploymentDescriptionSvc _searchDeploymentDescriptionSvc;
  @Inject private DecoupledAPITestUtils _decoupledAPITestUtils;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  private Group _group;
  private AppUser _groupSearchIndexEditor;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan("groupName");
    _groupSearchIndexEditor =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_SEARCH_INDEX_EDITOR);
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
  }

  @Test
  public void testFindIndexMigrationStatus() throws JsonProcessingException {
    _clusterDescriptionDao.save(
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), CLUSTER_NAME)));

    {
      // Verify cluster name not found
      final var errorResponse =
          doAuthedJsonGet(
              _groupSearchIndexEditor,
              String.format(
                  INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME + "XYZ", ObjectId.get()),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals("CLUSTER_NOT_FOUND", errorResponse.getString(ApiError.ERROR_CODE_FIELD));
    }

    final var clusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), CLUSTER_NAME).orElseThrow();

    {
      // Verify search deployment not found
      final var response =
          doAuthedJsonGet(
              _groupSearchIndexEditor,
              String.format(
                  INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME, ObjectId.get()),
              HttpStatus.SC_OK);
      assertTrue(response.isEmpty());
    }

    final var indexStatuses = createIndexStatuses();
    final var indexDetailedStatuses = createIndexDetailedStatuses(Type.SEARCH);
    final var vectorSearchIndexStatuses = createIndexStatuses();
    final var vectorSearchIndexDetailedStatuses = createIndexDetailedStatuses(Type.VECTOR_SEARCH);
    final SearchDeploymentDescription searchDeployment =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            _group.getId(),
            clusterDescription.getUniqueId(),
            2,
            new Migration(
                Migration.Target.DEDICATED_NODES,
                Migration.State.TARGET_STARTUP,
                Instant.now(),
                Migration.serializeIndexStatuses(indexStatuses),
                Migration.serializeIndexStatuses(indexDetailedStatuses),
                Migration.serializeIndexStatuses(vectorSearchIndexStatuses),
                Migration.serializeIndexStatuses(vectorSearchIndexDetailedStatuses)));
    _searchDeploymentDescriptionSvc.create(searchDeployment);

    {
      // Sanity check
      var persistedSearchDeployment =
          _searchDeploymentDescriptionSvc.findDeploymentById(searchDeployment.getId());
      assertTrue(persistedSearchDeployment.isPresent());
      assertEquals(_group.getId(), persistedSearchDeployment.get().getGroupId());
      var migrationOptional = persistedSearchDeployment.get().getMigrationOptional();
      assertTrue(migrationOptional.isPresent());
      var indexStatsOptional = migrationOptional.get().getIndexStatsOptional();
      assertTrue(indexStatsOptional.isPresent());
      assertEquals(2, indexStatsOptional.get().size());
      var indexDetailedStatusesOptional =
          migrationOptional.get().getIndexDetailedStatusesOptional();
      assertTrue(indexDetailedStatusesOptional.isPresent());
      assertEquals(2, indexDetailedStatusesOptional.get().size());
      var vectorSearchIndexStatsOptional =
          migrationOptional.get().getVectorSearchIndexStatsOptional();
      assertTrue(vectorSearchIndexStatsOptional.isPresent());
      assertEquals(2, vectorSearchIndexStatsOptional.get().size());
      var vectorSearchIndexDetailedStatusesOptional =
          migrationOptional.get().getVectorSearchIndexDetailedStatusesOptional();
      assertTrue(vectorSearchIndexDetailedStatusesOptional.isPresent());
      assertEquals(2, vectorSearchIndexDetailedStatusesOptional.get().size());
    }

    {
      // Index not found
      final var response =
          doAuthedJsonGet(
              _groupSearchIndexEditor,
              String.format(
                  INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME, ObjectId.get()),
              HttpStatus.SC_OK);
      assertTrue(response.isEmpty());
    }

    {
      // Fetch valid index stats
      var jsonMapper = new ObjectMapper();
      for (var entry : indexStatuses.entrySet()) {
        final var response =
            doAuthedJsonGet(
                _groupSearchIndexEditor,
                String.format(
                    INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME, entry.getKey()),
                HttpStatus.SC_OK);

        assertFalse(response.isEmpty());

        ApiAtlasFtsIndexStatsView responseStats =
            jsonMapper.readValue(response.toString(), ApiAtlasFtsIndexStatsView.class);

        assertEquals(entry.getValue().size(), responseStats.size());
        entry
            .getValue()
            .forEach(
                (hostName, stats) -> {
                  assertTrue(responseStats.containsKey(hostName));
                  ApiAtlasFtsIndexHostStatsView actual = responseStats.get(hostName);
                  assertEquals(
                      stats.getStatusCode().map(StatusCode::name).orElse(null),
                      actual.getStatusCode());
                  assertEquals(stats.getDataSize(), actual.getDataSize(), 0);
                  assertEquals(stats.getNumOfDocs(), actual.getNumOfDocs());
                });
      }
    }
    {
      // Fetch valid index stats
      var jsonMapper = new ObjectMapper();
      for (var entry : vectorSearchIndexStatuses.entrySet()) {
        final var response =
            doAuthedJsonGet(
                _groupSearchIndexEditor,
                String.format(
                    INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME, entry.getKey()),
                HttpStatus.SC_OK);

        assertFalse(response.isEmpty());

        ApiAtlasFtsIndexStatsView responseStats =
            jsonMapper.readValue(response.toString(), ApiAtlasFtsIndexStatsView.class);

        assertEquals(entry.getValue().size(), responseStats.size());
        entry
            .getValue()
            .forEach(
                (hostName, stats) -> {
                  assertTrue(responseStats.containsKey(hostName));
                  ApiAtlasFtsIndexHostStatsView actual = responseStats.get(hostName);
                  assertEquals(
                      stats.getStatusCode().map(StatusCode::name).orElse(null),
                      actual.getStatusCode());
                  assertEquals(stats.getDataSize(), actual.getDataSize(), 0);
                  assertEquals(stats.getNumOfDocs(), actual.getNumOfDocs());
                });
      }
    }

    _searchDeploymentDescriptionSvc.deleteDeploymentMigration(searchDeployment.getId());
    {
      // No migration found
      final var response =
          doAuthedJsonGet(
              _groupSearchIndexEditor,
              String.format(
                  INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME, ObjectId.get()),
              HttpStatus.SC_OK);
      assertTrue(response.isEmpty());
    }
  }

  @Test
  public void testFindClusterMigrationTarget() throws JsonProcessingException {
    _clusterDescriptionDao.save(
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), CLUSTER_NAME)));

    {
      // Verify cluster name not found
      final var errorResponse =
          doAuthedJsonGet(
              _groupSearchIndexEditor,
              String.format(CLUSTER_MIGRATION_TARGET_URL, _group.getId(), CLUSTER_NAME + "XYZ"),
              HttpStatus.SC_BAD_REQUEST);
      assertEquals("CLUSTER_NOT_FOUND", errorResponse.getString(ApiError.ERROR_CODE_FIELD));
    }

    final var clusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), CLUSTER_NAME).orElseThrow();

    {
      // Verify search deployment not found
      assertMigrationState(State.None);
    }

    final SearchDeploymentDescription searchDeployment =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            _group.getId(),
            clusterDescription.getUniqueId(),
            2,
            new Migration(
                Migration.Target.DEDICATED_NODES,
                Migration.State.TARGET_STARTUP,
                Instant.now(),
                null,
                null,
                null,
                null));
    _searchDeploymentDescriptionSvc.create(searchDeployment);

    {
      // Sanity check
      var persistedSearchDeployment =
          _searchDeploymentDescriptionSvc.findDeploymentById(searchDeployment.getId());
      assertTrue(persistedSearchDeployment.isPresent());
      assertEquals(_group.getId(), persistedSearchDeployment.get().getGroupId());
      var migrationOptional = persistedSearchDeployment.get().getMigrationOptional();
      assertTrue(migrationOptional.isPresent());
    }

    {
      // Fetch valid migration target
      assertMigrationState(State.InProgress_ToDedicatedNodes);
    }

    {
      // mark as failed
      _searchDeploymentDescriptionSvc.updateDeploymentMigrationState(
          searchDeployment, Migration.State.FAILED);

      assertMigrationState(State.Failed_ToDedicatedNodes);
    }

    _searchDeploymentDescriptionSvc.deleteDeploymentMigration(searchDeployment.getId());
    {
      // No migration found
      final var response =
          doAuthedJsonGet(
              _groupSearchIndexEditor,
              String.format(
                  INDEX_MIGRATION_STATUS_URL, _group.getId(), CLUSTER_NAME, ObjectId.get()),
              HttpStatus.SC_OK);
      assertTrue(response.isEmpty());
    }
  }

  private void assertMigrationState(State state) throws JsonProcessingException {
    var jsonMapper = new ObjectMapper();
    final var response =
        doAuthedJsonGet(
            _groupSearchIndexEditor,
            String.format(CLUSTER_MIGRATION_TARGET_URL, _group.getId(), CLUSTER_NAME),
            HttpStatus.SC_OK);

    assertFalse(response.isEmpty());

    ApiAtlasFtsIndexMigrationTargetView responseTarget =
        jsonMapper.readValue(response.toString(), ApiAtlasFtsIndexMigrationTargetView.class);

    assertEquals(responseTarget.getTarget(), state.name());
  }

  private Map<ObjectId, FTSIndexStatusMap<FTSIndexHostStat>> createIndexStatuses() {
    final Random rnd = new Random();
    final BiFunction<Long, Double, FTSIndexHostStat> generateHostStat =
        (docs, size) ->
            FTSSearchIndexHostStat.builder()
                .statusCode(StatusCode.STEADY)
                .numOfDocs(docs)
                .dataSize(size)
                .build();

    var base = rnd.nextDouble();
    var index1StatsMap = new FTSIndexStatusMap<FTSIndexHostStat>();
    index1StatsMap.put(DEDICATED_HOST_A, generateHostStat.apply(1000L, base + 10));
    index1StatsMap.put(DEDICATED_HOST_B, generateHostStat.apply(1008L, base + 20));
    index1StatsMap.put(DEDICATED_HOST_C, generateHostStat.apply(1102L, base + 50));

    var index2StatsMap = new FTSIndexStatusMap<FTSIndexHostStat>();
    index2StatsMap.put(DEDICATED_HOST_A, generateHostStat.apply(516L, base + 110));
    index2StatsMap.put(DEDICATED_HOST_B, generateHostStat.apply(600L, base + 220));
    index2StatsMap.put(DEDICATED_HOST_C, generateHostStat.apply(814L, base + 510));

    Map<ObjectId, FTSIndexStatusMap<FTSIndexHostStat>> statsPerIndex = new HashMap<>();
    statsPerIndex.put(ObjectId.get(), index1StatsMap);
    statsPerIndex.put(ObjectId.get(), index2StatsMap);
    return statsPerIndex;
  }

  public Map<ObjectId, FTSIndexStatusMap<FTSIndexHostDetailedStatuses>> createIndexDetailedStatuses(
      FTSIndex.Type pType) {
    FTSIndexStatusMap<FTSIndexHostDetailedStatuses> index1StatusMap = new FTSIndexStatusMap<>();
    index1StatusMap.put(DEDICATED_HOST_A, createDetailedStatus(StatusCode.STEADY, pType));
    index1StatusMap.put(DEDICATED_HOST_B, createDetailedStatus(StatusCode.STEADY, pType));
    index1StatusMap.put(DEDICATED_HOST_C, createDetailedStatus(StatusCode.STEADY, pType));

    FTSIndexStatusMap<FTSIndexHostDetailedStatuses> index2StatusMap = new FTSIndexStatusMap<>();
    index2StatusMap.put(DEDICATED_HOST_A, createDetailedStatus(StatusCode.STEADY, pType));
    index2StatusMap.put(DEDICATED_HOST_B, createDetailedStatus(StatusCode.STEADY, pType));
    index2StatusMap.put(DEDICATED_HOST_C, createDetailedStatus(StatusCode.STEADY, pType));

    Map<ObjectId, FTSIndexStatusMap<FTSIndexHostDetailedStatuses>> statusPerIndex = new HashMap<>();
    statusPerIndex.put(ObjectId.get(), index1StatusMap);
    statusPerIndex.put(ObjectId.get(), index2StatusMap);
    return statusPerIndex;
  }

  private FTSIndexHostDetailedStatuses createDetailedStatus(StatusCode code, FTSIndex.Type pType) {
    switch (pType) {
      case SEARCH:
        return FTSSearchIndexHostDetailedStatuses.builder()
            .mainIndex(
                FTSSearchIndexGenerationDetail.builder()
                    .status(code)
                    .definition(new FTSSearchIndex(new BasicDBObject()))
                    .build())
            .build();
      case VECTOR_SEARCH:
        return FTSVectorSearchIndexHostDetailedStatuses.builder()
            .mainIndex(
                FTSVectorSearchIndexGenerationDetail.builder()
                    .status(code)
                    .definition(new FTSVectorSearchIndex(new BasicDBObject()))
                    .build())
            .build();
      default:
        throw new IllegalStateException("unsupported type " + pType.getStringValue());
    }
  }
}
