package com.xgen.svc.nds.res;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xgen.cloud.atm.core._private.dao.LDAPVerifyConnectivityJobRequestDao;
import com.xgen.cloud.atm.core._public.model.LDAPVerifyConnectivityJobRequest;
import com.xgen.cloud.atm.core._public.model.LDAPVerifyConnectivityJobRequestValidation;
import com.xgen.cloud.atm.core._public.model.LDAPVerifyConnectivityJobResponse;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.deployment._public.model.AutomationErrorCode;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionUpdatesDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionFactory;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSLDAP;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserToDNMapping;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseResourceTestCommon;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasNDSLDAPVerifyConnectivityJobRequestParamsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasNDSLDAPVerifyConnectivityJobRequestValidationView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasNDSLDAPVerifyConnectivityJobRequestView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasNDSLDAPView;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.NDSCustomerX509View;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.io.IOException;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.bouncycastle.asn1.x509.Time;
import org.bouncycastle.cert.X509CRLHolder;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RunWith(GuiceTestRunner.class)
public class NDSUserSecurityResourceIntTests extends BaseResourceTestCommon {

  private static final Logger LOG = LoggerFactory.getLogger(NDSUserSecurityResourceIntTests.class);
  private static final String BASE_URL = "/nds/%s/userSecurity";
  private static final String TEST_CLUSTER_NAME = "I_TEST_USER_SEC";
  @Inject private NDSGroupDao _groupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AutomationMongoDbVersionSvc _automationMongoDbVersionSvc;
  @Inject private LDAPVerifyConnectivityJobRequestDao _ldapVerifyConnectivityJobRequestDao;
  @Inject private PlanDao _planDao;
  @Inject private NDSGroupSvc _groupSvc;
  @Inject private ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ClusterDescriptionUpdatesDao _clusterDescriptionUpdateDao;
  private Group _group;
  private NDSGroup _ndsGroup;
  private AppUser _user;

  @BeforeClass
  public static void prepareEnv() {
    BaseResourceTestCommon.prepareEnv();
    CloudProviderRegistryUtil.registerAllProvider();
  }

  @AfterClass
  public static void deregisterProviders() {
    CloudProviderRegistryUtil.deregisterAllProvider();
  }

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _automationMongoDbVersionSvc.invalidateVersionManifestCache();
    _automationMongoDbVersionSvc.autoUpdateDefaultVersions();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createUser(_group);
    _ndsGroup = _groupSvc.ensureGroup(_group.getId());

    _groupSvc.addCloudContainer(
        _ndsGroup, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final ClusterDescription cd0 =
        ClusterDescriptionFactory.get(
                NDSModelTestFactory.getAWSClusterDescription(_group.getId(), TEST_CLUSTER_NAME))
            .copy()
            .setState(ClusterDescription.State.IDLE)
            .setMongoDBVersion(NDSModelTestFactory.TEST_DEDICATED_MONGODB_VERSION)
            .setMongoDBMajorVersion(NDSModelTestFactory.TEST_DEDICATED_MONGODB_MAJOR_VERSION)
            .setMongoUriHosts(
                new String[] {
                  "host1.myfavoritecluster.com",
                  "host2.myfavoritecluster.com",
                  "host3.myfavoritecluster.com"
                })
            .build();

    _clusterDescriptionUpdateDao.save(cd0);

    _ndsClusterSvc.mergePendingClusterChanges(_ndsGroup);

    final List<ReplicaSetHardware> rsList =
        _replicaSetHardwareSvc.ensureHardware(
            _ndsGroup, Cluster.getCluster(cd0, Collections.emptyList()), LOG, _group);

    rsList.stream()
        .forEach(
            rsh -> {
              IntStream.range(0, rsh.getHardware().size())
                  .forEach(
                      instanceIndex -> {
                        final InstanceHardware instanceHardware =
                            rsh.getHardware().get(instanceIndex);

                        final Hostnames hostnames =
                            new Hostnames(
                                String.format("host%s.myfavoritecluster.com", instanceIndex));

                        _replicaSetHardwareDao.setInstanceFields(
                            rsh.getId(),
                            instanceHardware.getInstanceId(),
                            false,
                            Arrays.asList(
                                Pair.of(InstanceHardware.FieldDefs.HOSTNAMES, hostnames.toDBList()),
                                Pair.of(
                                    InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                                    InstanceHostname.HostnameScheme.LEGACY.name()),
                                Pair.of(InstanceHardware.FieldDefs.PROVISIONED, true),
                                Pair.of(
                                    InstanceHardware.FieldDefs.ACTION,
                                    InstanceHardware.Action.NONE.name())));
                      });
            });
  }

  @Test
  public void testGetUserSecurity() throws JSONException {
    final JSONObject userSecurity =
        doAuthedJsonGet(_user, getBaseUrl(_group.getId()), HttpStatus.SC_OK);
    final JSONObject ldap = userSecurity.getJSONObject("ldap");
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");

    assertFalse(ldap.getBoolean("authenticationEnabled"));
    assertFalse(ldap.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldap.get("hostname"));
    assertEquals(JSONObject.NULL, ldap.get("port"));
    assertEquals(JSONObject.NULL, ldap.get("bindUsername"));
    assertFalse(ldap.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldap.get("caCertificate"));

    assertEquals(JSONObject.NULL, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(JSONObject.NULL, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        0,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());
  }

  @Test
  public void testUpdateUserSecurityLDAP() throws JSONException {
    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", true)
                    .put("authorizationEnabled", true)
                    .put("hostname", "a.example.com")
                    .put("port", "636")
                    .put("bindUsername", "username")
                    .put("bindPassword", "hunter2")
                    .put("authzQueryTemplate", "CN=Fred,CN=Flintstones?memberOf?base"))
            .put(
                "customerX509",
                new JSONObject()
                    .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, new JSONArray()));

    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject ldap = userSecurity.getJSONObject("ldap");
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");

    assertTrue(ldap.getBoolean("authenticationEnabled"));
    assertTrue(ldap.getBoolean("authorizationEnabled"));
    assertEquals("a.example.com", ldap.getString("hostname"));
    assertEquals(636, ldap.getInt("port"));
    assertEquals("username", ldap.getString("bindUsername"));
    assertFalse(ldap.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldap.get("userToDNMapping"));
    assertEquals(JSONObject.NULL, ldap.get("caCertificate"));
    assertEquals("CN=Fred,CN=Flintstones?memberOf?base", ldap.getString("authzQueryTemplate"));

    assertEquals(JSONObject.NULL, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(JSONObject.NULL, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        0,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());
  }

  private String generatePEMforCA(final TLSUtil.PEMKeyFile pCA) throws IOException {
    return TLSUtil.convertDERToPEM("CERTIFICATE", pCA.getKeyCertificate().getEncoded());
  }

  private String generateCRLforCA(final TLSUtil.PEMKeyFile pCA) throws IOException {
    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());

    final X509CRLHolder crl =
        TLSUtil.generateCRL(
            pCA,
            Collections.singletonList(Pair.of(serial, new Date())),
            NDSDefaults.X509_SIG_ALGORITHM);

    return TLSUtil.convertDERToPEM("X509 CRL", crl.getEncoded());
  }

  @Test
  public void testUpdateUserSecurityCustomerX509() throws JSONException, IOException {
    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial);

    final String pemForCA = generatePEMforCA(newCA);
    final String crlForCA = generateCRLforCA(newCA);
    JSONArray crlDistributionPoints = new JSONArray();
    crlDistributionPoints.put("testPoint");
    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", false)
                    .put("authorizationEnabled", false))
            .put(
                "customerX509",
                new JSONObject()
                    .put(NDSCustomerX509View.CAS_FIELD, pemForCA)
                    .put(NDSCustomerX509View.CRLS_FIELD, crlForCA)
                    .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, crlDistributionPoints));

    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject ldap = userSecurity.getJSONObject("ldap");
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());

    assertFalse(ldap.getBoolean("authenticationEnabled"));
    assertFalse(ldap.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldap.get("hostname"));
    assertEquals(JSONObject.NULL, ldap.get("port"));
    assertEquals(JSONObject.NULL, ldap.get("bindUsername"));
    assertFalse(ldap.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldap.get("caCertificate"));
  }

  @Test
  public void testPatchUserSecurity() throws JSONException, IOException {

    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial);

    final String pemForCA = generatePEMforCA(newCA);
    final String crlForCA = generateCRLforCA(newCA);

    JSONArray crlDistributionPoints = new JSONArray();
    crlDistributionPoints.put("testPoint");
    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", false)
                    .put("authorizationEnabled", false))
            .put(
                "customerX509",
                new JSONObject()
                    .put(NDSCustomerX509View.CAS_FIELD, pemForCA)
                    .put(NDSCustomerX509View.CRLS_FIELD, crlForCA)
                    .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, crlDistributionPoints));

    // Patch with both LDAP and CustomerX509
    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject ldap = userSecurity.getJSONObject("ldap");
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());

    assertFalse(ldap.getBoolean("authenticationEnabled"));
    assertFalse(ldap.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldap.get("hostname"));
    assertEquals(JSONObject.NULL, ldap.get("port"));
    assertEquals(JSONObject.NULL, ldap.get("bindUsername"));
    assertFalse(ldap.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldap.get("caCertificate"));

    // Patch LDAP only
    final JSONObject userSecurityPatchLDAP =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", false)
                    .put("authorizationEnabled", false));
    final JSONObject patchedLDAP =
        doAuthedJsonPatch(
            _user, getBaseUrl(_group.getId()), userSecurityPatchLDAP, HttpStatus.SC_OK);
    final JSONObject ldapPatched1 = patchedLDAP.getJSONObject("ldap");
    final JSONObject customerX509Patched1 = patchedLDAP.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509Patched1.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509Patched1.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509Patched1.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD))
            .length());

    assertFalse(ldapPatched1.getBoolean("authenticationEnabled"));
    assertFalse(ldapPatched1.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("hostname"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("port"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("bindUsername"));
    assertFalse(ldapPatched1.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("caCertificate"));

    // Patch CustomerX509 only
    final JSONObject customerX509Patch =
        new JSONObject()
            .put(NDSCustomerX509View.CAS_FIELD, JSONObject.NULL)
            .put(NDSCustomerX509View.CRLS_FIELD, JSONObject.NULL)
            .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, Collections.emptyList());
    final JSONObject userSecurityPatchCustomerX509 =
        new JSONObject().put("customerX509", customerX509Patch);

    final JSONObject userSecurityPatchedCustomerX509 =
        doAuthedJsonPatch(
            _user, getBaseUrl(_group.getId()), userSecurityPatchCustomerX509, HttpStatus.SC_OK);
    final JSONObject ldapPatched2 = userSecurityPatchedCustomerX509.getJSONObject("ldap");
    final JSONObject customerX509Patched2 =
        userSecurityPatchedCustomerX509.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509Patched2.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(JSONObject.NULL, customerX509Patched2.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        0,
        ((JSONArray) customerX509Patched2.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD))
            .length());

    assertFalse(ldapPatched2.getBoolean("authenticationEnabled"));
    assertFalse(ldapPatched2.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldapPatched2.get("hostname"));
    assertEquals(JSONObject.NULL, ldapPatched2.get("port"));
    assertEquals(JSONObject.NULL, ldapPatched2.get("bindUsername"));
    assertFalse(ldapPatched2.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldapPatched2.get("caCertificate"));
  }

  @Test
  public void testPatchUserSecurity_LDAP() throws JSONException, IOException {
    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial);
    final String pemForCA = generatePEMforCA(newCA);

    final JSONObject userToDNMapping =
        new JSONObject()
            .put("match", "(.*)")
            .put("substitution", "cn={0},ou=engineering,dc=example,dc=com");

    final String authzQueryTemplate = "CN=Fred,CN=Flintstones?memberOf?base";

    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", false)
                    .put("authorizationEnabled", false)
                    .put("caCertificate", pemForCA)
                    .put("userToDNMapping", List.of(userToDNMapping))
                    .put("authzQueryTemplate", authzQueryTemplate));

    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject ldap = userSecurity.getJSONObject("ldap");

    assertFalse(ldap.getBoolean("authenticationEnabled"));
    assertFalse(ldap.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldap.get("hostname"));
    assertEquals(JSONObject.NULL, ldap.get("port"));
    assertEquals(JSONObject.NULL, ldap.get("bindUsername"));
    assertFalse(ldap.has("bindPassword"));
    assertEquals(1, ldap.getJSONArray("userToDNMapping").length());
    assertEquals("(.*)", ldap.getJSONArray("userToDNMapping").getJSONObject(0).getString("match"));
    assertEquals(pemForCA, ldap.get("caCertificate"));
    assertEquals(authzQueryTemplate, ldap.get("authzQueryTemplate"));

    // Patch LDAP - unset userToDNMapping and unset CA Certs
    final JSONObject userSecurityPatchLDAP =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", false)
                    .put("authorizationEnabled", false)
                    .put("caCertificate", "")
                    .put("authzQueryTemplate", ""));

    final JSONObject patchedLDAP =
        doAuthedJsonPatch(
            _user, getBaseUrl(_group.getId()), userSecurityPatchLDAP, HttpStatus.SC_OK);
    final JSONObject ldapPatched1 = patchedLDAP.getJSONObject("ldap");

    assertFalse(ldapPatched1.getBoolean("authenticationEnabled"));
    assertFalse(ldapPatched1.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("hostname"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("port"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("bindUsername"));
    assertFalse(ldapPatched1.has("bindPassword"));
    assertEquals(
        "(.*)", ldapPatched1.getJSONArray("userToDNMapping").getJSONObject(0).getString("match"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("caCertificate"));
    assertEquals(JSONObject.NULL, ldapPatched1.get("authzQueryTemplate"));
  }

  @Test
  public void testPatchUserSecurity_CustomerX509() throws JSONException, IOException {
    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial);

    final String pemForCA = generatePEMforCA(newCA);
    final String crlForCA = generateCRLforCA(newCA);

    JSONArray crlDistributionPoints = new JSONArray();
    crlDistributionPoints.put("testPoint");
    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "customerX509",
                new JSONObject()
                    .put(NDSCustomerX509View.CAS_FIELD, pemForCA)
                    .put(NDSCustomerX509View.CRLS_FIELD, crlForCA)
                    .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, crlDistributionPoints));

    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());

    // Patch CustomerX509 - case: "crls" is null and "crlDistributionPoints" is empty
    final JSONObject customerX509Patch =
        new JSONObject()
            .put(NDSCustomerX509View.CAS_FIELD, JSONObject.NULL)
            .put(NDSCustomerX509View.CRLS_FIELD, JSONObject.NULL)
            .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, Collections.emptyList());
    final JSONObject userSecurityPatchCustomerX509 =
        new JSONObject().put("customerX509", customerX509Patch);

    final JSONObject userSecurityPatchedCustomerX509 =
        doAuthedJsonPatch(
            _user, getBaseUrl(_group.getId()), userSecurityPatchCustomerX509, HttpStatus.SC_OK);
    final JSONObject customerX509Patched2 =
        userSecurityPatchedCustomerX509.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509Patched2.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(JSONObject.NULL, customerX509Patched2.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        0,
        ((JSONArray) customerX509Patched2.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD))
            .length());

    // Patch CustomerX509 - case: update "cas", "crls" and "crlDistributionPoints"
    final BigInteger serial2 = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA2 =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial2);

    final String pemForCA2 = generatePEMforCA(newCA2);
    final String crlForCA2 = generateCRLforCA(newCA2);
    customerX509Patch
        .put(NDSCustomerX509View.CAS_FIELD, pemForCA2)
        .put(NDSCustomerX509View.CRLS_FIELD, crlForCA2)
        .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, List.of("localhost"));
    userSecurityPatchCustomerX509.put("customerX509", customerX509Patch);
    final JSONObject userSecurityPatchedCustomerX5092 =
        doAuthedJsonPatch(
            _user, getBaseUrl(_group.getId()), userSecurityPatchCustomerX509, HttpStatus.SC_OK);
    final JSONObject customerX509Patched3 =
        userSecurityPatchedCustomerX5092.getJSONObject("customerX509");

    assertEquals(pemForCA2, customerX509Patched3.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA2, customerX509Patched3.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509Patched3.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD))
            .length());
  }

  @Test
  public void deleteUserSecurityLdapUserToDNMapping() throws JSONException, IOException {
    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial);

    final String pemForCA = generatePEMforCA(newCA);
    final String crlForCA = generateCRLforCA(newCA);

    final JSONObject userToDNMapping =
        new JSONObject()
            .put("match", "(.*)")
            .put("substitution", "cn={0},ou=engineering,dc=example,dc=com");

    JSONArray crlDistributionPoints = new JSONArray();
    crlDistributionPoints.put("testPoint");
    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "ldap",
                new JSONObject()
                    .put("authenticationEnabled", false)
                    .put("authorizationEnabled", false)
                    .put("userToDNMapping", List.of(userToDNMapping)))
            .put(
                "customerX509",
                new JSONObject()
                    .put(NDSCustomerX509View.CAS_FIELD, pemForCA)
                    .put(NDSCustomerX509View.CRLS_FIELD, crlForCA)
                    .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, crlDistributionPoints));

    // Create LDAP and CustomerX509
    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject ldap = userSecurity.getJSONObject("ldap");
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());

    assertFalse(ldap.getBoolean("authenticationEnabled"));
    assertFalse(ldap.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldap.get("hostname"));
    assertEquals(JSONObject.NULL, ldap.get("port"));
    assertEquals(JSONObject.NULL, ldap.get("bindUsername"));
    assertFalse(ldap.has("bindPassword"));
    assertEquals("(.*)", ldap.getJSONArray("userToDNMapping").getJSONObject(0).getString("match"));
    assertEquals(JSONObject.NULL, ldap.get("caCertificate"));

    // delete userToDNMapping from ldap
    doAuthedDelete(
        _user, getBaseUrl(_group.getId()) + "/ldap/userToDNMapping", HttpStatus.SC_ACCEPTED);

    // verify the deletion
    final JSONObject userSecurityWithoutUserToDNMapping =
        doAuthedJsonGet(_user, getBaseUrl(_group.getId()), HttpStatus.SC_OK);
    final JSONObject ldapUpdated = userSecurityWithoutUserToDNMapping.getJSONObject("ldap");
    final JSONObject customerX509Updated =
        userSecurityWithoutUserToDNMapping.getJSONObject("customerX509");

    assertEquals(pemForCA, customerX509Updated.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509Updated.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509Updated.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD))
            .length());

    assertFalse(ldapUpdated.getBoolean("authenticationEnabled"));
    assertFalse(ldapUpdated.getBoolean("authorizationEnabled"));
    assertEquals(JSONObject.NULL, ldapUpdated.get("hostname"));
    assertEquals(JSONObject.NULL, ldapUpdated.get("port"));
    assertEquals(JSONObject.NULL, ldapUpdated.get("bindUsername"));
    assertFalse(ldapUpdated.has("bindPassword"));
    assertEquals(JSONObject.NULL, ldapUpdated.get("userToDNMapping"));
    assertEquals(JSONObject.NULL, ldap.get("caCertificate"));
  }

  @Test
  public void testDeleteCustomerX509() throws JSONException, IOException {
    final BigInteger serial = new BigInteger(Long.SIZE - 1, new SecureRandom());
    final TLSUtil.PEMKeyFile newCA =
        TLSUtil.createNewSelfSignedCA(
            "CN=certificate.authority",
            NDSDefaults.X509_SIG_ALGORITHM,
            NDSDefaults.X509_KEYLENGTH,
            new Time(new Date(System.currentTimeMillis() - NDSDefaults.X509_BACKDATE.toMillis())),
            NDSDefaults.X509_NOT_AFTER,
            serial);

    final String pemForCA = generatePEMforCA(newCA);
    final String crlForCA = generateCRLforCA(newCA);

    JSONArray crlDistributionPoints = new JSONArray();
    crlDistributionPoints.put("testPoint");
    final JSONObject userSecurityUpdate =
        new JSONObject()
            .put(
                "customerX509",
                new JSONObject()
                    .put(NDSCustomerX509View.CAS_FIELD, pemForCA)
                    .put(NDSCustomerX509View.CRLS_FIELD, crlForCA)
                    .put(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD, crlDistributionPoints));

    // create customerX509 for test
    final JSONObject userSecurity =
        doAuthedJsonPatch(_user, getBaseUrl(_group.getId()), userSecurityUpdate, HttpStatus.SC_OK);
    final JSONObject customerX509 = userSecurity.getJSONObject("customerX509");
    assertEquals(pemForCA, customerX509.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(crlForCA, customerX509.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        1,
        ((JSONArray) customerX509.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD)).length());

    // delete customerX509
    doAuthedDelete(_user, getBaseUrl(_group.getId()) + "/customerX509", HttpStatus.SC_ACCEPTED);

    // verify the deletion
    final JSONObject userSecurityWithoutCustomerX509 =
        doAuthedJsonGet(_user, getBaseUrl(_group.getId()), HttpStatus.SC_OK);
    final JSONObject customerX509Deleted =
        userSecurityWithoutCustomerX509.getJSONObject("customerX509");
    assertEquals(JSONObject.NULL, customerX509Deleted.get(NDSCustomerX509View.CAS_FIELD));
    assertEquals(JSONObject.NULL, customerX509Deleted.get(NDSCustomerX509View.CRLS_FIELD));
    assertEquals(
        0,
        ((JSONArray) customerX509Deleted.get(NDSCustomerX509View.CRL_DISTRIBUTION_POINTS_FIELD))
            .length());
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_NoRequestExists() throws Exception {
    _ldapVerifyConnectivityJobRequestDao.deleteByGroup(_group.getId());

    final JSONObject requestJSON = createLDAPVerifyRequestJSON();
    assertLDAPVerifyResultsJSON(
        requestJSON,
        submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK),
        LDAPVerifyConnectivityJobRequest.Status.PENDING,
        Collections.emptyList());
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_InvalidUserToDNMapping() throws Exception {
    final JSONObject requestJSON = createLDAPVerifyRequestJSON();

    // invalid userToDNMapping JSON
    requestJSON.put(NDSLDAP.FieldDefs.USER_TO_DN_MAPPING, "I am not JSON.  My name is Ralph.");
    assertLDAPVerifyInvalidUserToDNMappingResultsJSON(requestJSON, "Invalid JSON.");

    // missing match field
    requestJSON.put(
        NDSLDAP.FieldDefs.USER_TO_DN_MAPPING,
        getUserToDNMapping(null, "cn={0},ou=engineering,dc=example,dc=com", null));
    assertLDAPVerifyInvalidUserToDNMappingResultsJSON(
        requestJSON, "The match field must be specified.");

    // invalid match field
    requestJSON.put(
        NDSLDAP.FieldDefs.USER_TO_DN_MAPPING,
        getUserToDNMapping("*", "cn={0},ou=engineering,dc=example,dc=com", null));
    assertLDAPVerifyInvalidUserToDNMappingResultsJSON(
        requestJSON, "The match field is not a valid regular expression.");

    // invalid ldap query
    requestJSON.put(
        NDSLDAP.FieldDefs.USER_TO_DN_MAPPING,
        getUserToDNMapping(
            "(.*)",
            null,
            "I am not an LDAP query.  I just ask deep philispohical questions.  What is the"
                + " meaning of life?"));
    assertLDAPVerifyInvalidUserToDNMappingResultsJSON(
        requestJSON, "Unable to parse the LDAP query.");

    // no substitution or ldap query
    requestJSON.put(NDSLDAP.FieldDefs.USER_TO_DN_MAPPING, getUserToDNMapping("(.*)", null, null));
    assertLDAPVerifyInvalidUserToDNMappingResultsJSON(
        requestJSON, "Substitution or an LDAP query must be specified.");

    // substitution and ldap query specified together
    requestJSON.put(
        NDSLDAP.FieldDefs.USER_TO_DN_MAPPING,
        getUserToDNMapping(
            "(.*)",
            "cn={0},ou=engineering,dc=example,dc=com",
            "ou=engineering,dc=example,dc=com??one?(user={0})"));
    assertLDAPVerifyInvalidUserToDNMappingResultsJSON(
        requestJSON,
        "Substitution or an LDAP query must be specified. Both values cannot be set together.");

    // valid mapping with substitution
    requestJSON.put(
        NDSLDAP.FieldDefs.USER_TO_DN_MAPPING,
        getUserToDNMapping("(.*)", "cn={0},ou=engineering,dc=example,dc=com", null));
    submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK);

    // valid mapping with ldap query
    requestJSON.put(
        NDSLDAP.FieldDefs.USER_TO_DN_MAPPING,
        getUserToDNMapping("(.*)", null, "ou=engineering,dc=example,dc=com??one?(user={0})"));
    submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK);
  }

  private String getUserToDNMapping(
      final String pMatch, final String pSubstitution, final String pLDAPQuery) {
    final JSONObject userTDNMapping = new JSONObject();
    if (pMatch != null) {
      userTDNMapping.put(NDSUserToDNMapping.MATCH_FIELD, pMatch);
    }
    if (pSubstitution != null) {
      userTDNMapping.put(NDSUserToDNMapping.SUBSTITUTION_FIELD, pSubstitution);
    }
    if (pLDAPQuery != null) {
      userTDNMapping.put(NDSUserToDNMapping.LDAP_QUERY_FIELD, pLDAPQuery);
    }

    return new JSONArray(Collections.singletonList(userTDNMapping)).toString();
  }

  private void assertLDAPVerifyInvalidUserToDNMappingResultsJSON(
      final JSONObject pRequestJSON, final String pErrorMessageContains) throws Exception {
    final JSONObject resultsJSON = submitLDAPVerifyRequest(pRequestJSON, HttpStatus.SC_BAD_REQUEST);

    assertEquals(
        NDSErrorCode.LDAP_USER_TO_DN_MAPPING_INVALID.name(), resultsJSON.getString("errorCode"));
    assertTrue(resultsJSON.getString("message").contains(pErrorMessageContains));
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_PendingRequestExists() throws Exception {
    _ldapVerifyConnectivityJobRequestDao.deleteByGroup(_group.getId());

    final JSONObject requestJSON = createLDAPVerifyRequestJSON();
    assertLDAPVerifyResultsJSON(
        requestJSON,
        submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK),
        LDAPVerifyConnectivityJobRequest.Status.PENDING,
        Collections.emptyList());
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_SuccessRequestExists() throws Exception {
    testCreateLDAPVerifyConnectivityRequest_TerminatingStateRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS);
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_FailedRequestExists() throws Exception {
    testCreateLDAPVerifyConnectivityRequest_TerminatingStateRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.FAILED);
  }

  // Test that a new job is created (overwriting the existing one) when an existing job exists with
  // a finite status (i.e. SUCCESS for FAILED)
  private void testCreateLDAPVerifyConnectivityRequest_TerminatingStateRequestExists(
      final LDAPVerifyConnectivityJobRequest.Status pStatus) throws Exception {
    _ldapVerifyConnectivityJobRequestDao.deleteByGroup(_group.getId());
    final JSONObject tsResultsJSON =
        submitLDAPVerifyRequest(createLDAPVerifyRequestJSON(), HttpStatus.SC_OK);

    final ObjectId requestId =
        new ObjectId(
            tsResultsJSON.getString(
                ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.REQUEST_ID_FIELD));

    if (LDAPVerifyConnectivityJobRequest.Status.SUCCESS.equals(pStatus)) {
      _ldapVerifyConnectivityJobRequestDao.updateRequestWithResults(
          requestId, new LDAPVerifyConnectivityJobResponse());
    } else {
      _ldapVerifyConnectivityJobRequestDao.updateRequestWithError(
          requestId,
          "Strange things are afoot at the Circle K",
          AutomationErrorCode.JOB_FAILED,
          new LDAPVerifyConnectivityJobResponse());
    }

    final JSONObject requestJSON = createLDAPVerifyRequestJSON();
    final JSONObject resultsJSON = submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK);

    assertLDAPVerifyResultsJSON(
        requestJSON,
        resultsJSON,
        LDAPVerifyConnectivityJobRequest.Status.PENDING,
        Collections.emptyList());
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_MissingFields() throws Exception {
    // Test with missing fields - Failed
    testDropField(ApiAtlasNDSLDAPView.HOSTNAME_FIELD);
    testDropField(ApiAtlasNDSLDAPView.PORT_FIELD);
    testDropField(ApiAtlasNDSLDAPView.BIND_USERNAME_FIELD);
    testDropField(ApiAtlasNDSLDAPView.BIND_PASSWORD_FIELD);
  }

  private void testDropField(final String pFieldName) throws Exception {
    _ldapVerifyConnectivityJobRequestDao.deleteByGroup(_group.getId());

    final JSONObject requestJSON = createLDAPVerifyRequestJSON();
    requestJSON.remove(pFieldName);

    testForError(requestJSON, HttpStatus.SC_BAD_REQUEST, NDSErrorCode.INVALID_ARGUMENT, pFieldName);
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_NoAvailableClusters() throws Exception {
    _clusterDescriptionDao.markDeleted(_group.getId(), TEST_CLUSTER_NAME, new Date());

    testForError(
        createLDAPVerifyRequestJSON(),
        HttpStatus.SC_BAD_REQUEST,
        NDSErrorCode.LDAP_VERIFY_CONNECTIVITY_NO_AVAILABLE_CLUSTERS,
        null);
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_NoAvailableInstances() throws Exception {
    _replicaSetHardwareDao.deleteAllHardwareOnCluster(_group.getId(), TEST_CLUSTER_NAME);

    testForError(
        createLDAPVerifyRequestJSON(),
        HttpStatus.SC_BAD_REQUEST,
        NDSErrorCode.LDAP_VERIFY_CONNECTIVITY_NO_AVAILABLE_INSTANCES,
        null);
  }

  @Test
  public void testCreateLDAPVerifyConnectivityRequest_NoAvailableMongoDBPackage() throws Exception {
    final ClusterDescription cd0 =
        _clusterDescriptionDao
            .findByName(_group.getId(), TEST_CLUSTER_NAME)
            .get()
            .copy()
            .setMongoDBVersion("3.6.999")
            .setMongoDBMajorVersion("3.4.999")
            .build();
    _clusterDescriptionDao.save(cd0);

    testForError(
        createLDAPVerifyRequestJSON(),
        HttpStatus.SC_BAD_REQUEST,
        NDSErrorCode.LDAP_VERIFY_CONNECTIVITY_NO_AVAILABLE_MONGODB_PACKAGE,
        null);
  }

  @Test
  public void testCheckLDAPVerifyConnectivityRequestStatus_NoRequestExist() throws Exception {
    getLDAPVerifyRequest(new ObjectId().toString(), HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testCheckLDAPVerifyConnectivityRequestStatus_PendingRequestExists() throws Exception {
    final JSONObject requestJSON = createLDAPVerifyRequestJSON();
    final JSONObject resultsJSON = submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK);

    final JSONObject retrievedRequestJSON =
        getLDAPVerifyRequest(
            resultsJSON.getString(ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.REQUEST_ID_FIELD),
            HttpStatus.SC_OK);

    assertLDAPVerifyResultsJSON(
        requestJSON,
        retrievedRequestJSON,
        LDAPVerifyConnectivityJobRequest.Status.PENDING,
        Collections.emptyList());
  }

  @Test
  public void testCheckLDAPVerifyConnectivityRequestStatus_SuccessRequestExistsNoAuthZ()
      throws Exception {
    final String stdOut =
        "Running MongoDB LDAP authorization validation checks...\n"
            + "Version: 4.4.24\n"
            + "\n"
            + "Checking that an LDAP server has been specified...\n"
            + "[OK] LDAP server found\n"
            + "\n"
            + "Connecting to LDAP server...\n"
            + "[OK] Connected to LDAP server\n"
            + "\n"
            + "Attempting to authenticate against the LDAP server...\n"
            + "[OK] Successful authentication performed\n"
            + "\n"
            + "Checking if LDAP authorization has been enabled by configuration...\n"
            + "[FAIL] LDAP authorization is not enabled, the configuration will require internal"
            + " users to be maintained\n"
            + "\t* Make sure you have 'security.ldap.authz.queryTemplate' in your configuration";

    final List<
            Pair<
                LDAPVerifyConnectivityJobRequestValidation.ValidationType,
                LDAPVerifyConnectivityJobRequestValidation.Status>>
        expectedValidations =
            Arrays.asList(
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.SERVER_SPECIFIED,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.CONNECT,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.AUTHENTICATE,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK));
    testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        stdOut,
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        expectedValidations,
        false,
        true);
    testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        stdOut,
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        expectedValidations,
        false,
        false);
  }

  @Test
  public void testCheckLDAPVerifyConnectivityRequestStatus_SuccessRequestExistsWithAuthZ()
      throws Exception {
    final String stdOut =
        "Running MongoDB LDAP authorization validation checks...\n"
            + "Version: 4.4.24\n"
            + "\n"
            + "Checking that an LDAP server has been specified...\n"
            + "[OK] LDAP server found\n"
            + "\n"
            + "Connecting to LDAP server...\n"
            + "[OK] Connected to LDAP server\n"
            + "\n"
            + "Attempting to authenticate against the LDAP server...\n"
            + "[OK] Successful authentication performed\n"
            + "\n"
            + "Checking if LDAP authorization has been enabled by configuration...\n"
            + "[OK] LDAP authorization enabled\n"
            + "\n"
            + "Parsing LDAP query template...\n"
            + "[OK] LDAP query configuration template appears valid\n"
            + "\n"
            + "Executing query against LDAP server...\n"
            + "[OK] Successfully acquired the following roles on the 'admin' database:\n"
            + "\n"
            + "\t* CN=Administrators,CN=Builtin,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com\n"
            + "\t* CN=Group Policy Creator"
            + " Owners,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com\n"
            + "\t* CN=Schema Admins,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com\n"
            + "\t* CN=Domain Admins,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com";

    final List<
            Pair<
                LDAPVerifyConnectivityJobRequestValidation.ValidationType,
                LDAPVerifyConnectivityJobRequestValidation.Status>>
        expectedValidations =
            Arrays.asList(
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.SERVER_SPECIFIED,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.CONNECT,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.AUTHENTICATE,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.AUTHORIZATION_ENABLED,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType
                        .PARSE_AUTHZ_QUERY_TEMPLATE,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.QUERY_SERVER,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK));
    testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        stdOut,
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        expectedValidations,
        true,
        true);
    testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        stdOut,
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        expectedValidations,
        true,
        false);
  }

  @Test
  public void testCheckLDAPVerifyConnectivityRequestStatus_FailedRequestExistsWithAuthZ()
      throws Exception {
    final String stdOut =
        "Running MongoDB LDAP authorization validation checks...\n"
            + "Version: 4.4.24\n"
            + "\n"
            + "Checking that an LDAP server has been specified...\n"
            + "[OK] LDAP server found\n"
            + "\n"
            + "Connecting to LDAP server...\n"
            + "[OK] Connected to LDAP server\n"
            + "\n"
            + "Attempting to authenticate against the LDAP server...\n"
            + "[OK] Successful authentication performed\n"
            + "\n"
            + "Checking if LDAP authorization has been enabled by configuration...\n"
            + "[OK] LDAP authorization enabled\n"
            + "\n"
            + "Parsing LDAP query template...\n"
            + "[OK] LDAP query configuration template appears valid\n"
            + "\n"
            + "Executing query against LDAP server...\n"
            + "[FAIL] Unable to acquire roles\n"
            + "\t* Error: OperationFailed: Failed to obtain LDAP entities for query 'BaseDN:"
            + " \"CN=Administrator,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=comsdsdds\","
            + " Scope: \"base\", Filter: \"\", Attributes: \"memberOf\", ': LDAP operation"
            + " <ldap_search_ext_s>, Failed to perform query: No such object' Query was: 'BaseDN:"
            + " \"CN=Administrator,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=comsdsdds\","
            + " Scope: \"base\", Filter: \"\", Attributes: \"memberOf\", '\". (32/No such object):"
            + " acl_read: Error retrieving instanceType for base. at"
            + " ../source4/dsdb/samdb/ldb_modules/acl_read.c:362";

    final List<
            Pair<
                LDAPVerifyConnectivityJobRequestValidation.ValidationType,
                LDAPVerifyConnectivityJobRequestValidation.Status>>
        expectedValidations =
            Arrays.asList(
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.SERVER_SPECIFIED,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.CONNECT,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.AUTHENTICATE,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.AUTHORIZATION_ENABLED,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType
                        .PARSE_AUTHZ_QUERY_TEMPLATE,
                    LDAPVerifyConnectivityJobRequestValidation.Status.OK),
                Pair.of(
                    LDAPVerifyConnectivityJobRequestValidation.ValidationType.QUERY_SERVER,
                    LDAPVerifyConnectivityJobRequestValidation.Status.FAIL));
    testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        stdOut,
        LDAPVerifyConnectivityJobRequest.Status.FAILED,
        expectedValidations,
        true,
        true);
    testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
        LDAPVerifyConnectivityJobRequest.Status.SUCCESS,
        stdOut,
        LDAPVerifyConnectivityJobRequest.Status.FAILED,
        expectedValidations,
        true,
        false);
  }

  private void testCheckLDAPVerifyConnectivityRequestStatus_TerminatingStatusRequestExists(
      final LDAPVerifyConnectivityJobRequest.Status pRequestStatus,
      final String pStdOut,
      final LDAPVerifyConnectivityJobRequest.Status pExpectedStatus,
      final List<
              Pair<
                  LDAPVerifyConnectivityJobRequestValidation.ValidationType,
                  LDAPVerifyConnectivityJobRequestValidation.Status>>
          pExpectedValidations,
      final boolean pUseAuthZ,
      final boolean pEnvelope)
      throws Exception {
    final JSONObject requestJSON = createLDAPVerifyRequestJSON();

    if (pUseAuthZ) {
      requestJSON.put(
          ApiAtlasNDSLDAPVerifyConnectivityJobRequestParamsView.AUTHZ_QUERY_TEMPLATE,
          "{USER}?memberof?base");
    }

    final JSONObject resultsJSON = submitLDAPVerifyRequest(requestJSON, HttpStatus.SC_OK);

    final ObjectId requestId =
        new ObjectId(
            resultsJSON.getString(
                ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.REQUEST_ID_FIELD));

    if (LDAPVerifyConnectivityJobRequest.Status.SUCCESS.equals(pRequestStatus)) {
      _ldapVerifyConnectivityJobRequestDao.updateRequestWithResults(
          requestId, new LDAPVerifyConnectivityJobResponse(0, pStdOut, null));
    } else {
      _ldapVerifyConnectivityJobRequestDao.updateRequestWithError(
          requestId,
          "Strange things are afoot at the Circle K",
          AutomationErrorCode.JOB_FAILED,
          new LDAPVerifyConnectivityJobResponse(0, pStdOut, null));
    }

    final JSONObject retrievedRequestJSON =
        getLDAPVerifyRequest(requestId.toString(), HttpStatus.SC_OK);

    assertLDAPVerifyResultsJSON(
        requestJSON, retrievedRequestJSON, pExpectedStatus, pExpectedValidations);
  }

  private void testForError(
      final JSONObject pRequestJSON,
      final int pExpectedHttpStatusCoe,
      final NDSErrorCode pNDSErrorCode,
      final String pMessageContains)
      throws JsonProcessingException, JSONException {
    final JSONObject resultsJSON = submitLDAPVerifyRequest(pRequestJSON, pExpectedHttpStatusCoe);

    assertEquals("ERROR", resultsJSON.getString("status"));
    assertEquals(pNDSErrorCode.name(), resultsJSON.getString("errorCode"));

    if (pMessageContains != null) {
      assertTrue(resultsJSON.getString("message").contains(pMessageContains));
    }
  }

  private JSONObject createLDAPVerifyRequestJSON() throws JSONException {
    final JSONObject contentJSON = new JSONObject();
    contentJSON.put(ApiAtlasNDSLDAPView.HOSTNAME_FIELD, "tedtheldapserver.iamnotscramauth.org");
    contentJSON.put(ApiAtlasNDSLDAPView.PORT_FIELD, 636);
    contentJSON.put(ApiAtlasNDSLDAPView.BIND_USERNAME_FIELD, "ldapTed");
    contentJSON.put(ApiAtlasNDSLDAPView.BIND_PASSWORD_FIELD, "WhoN@m3zLd@pS3rv3rs@fterP30ple?;)");

    return contentJSON;
  }

  private JSONObject submitLDAPVerifyRequest(
      final JSONObject request, final int pExpectedHttpStatusCode) throws JsonProcessingException {
    return doAuthedJsonPost(
        _user, getBaseUrl(_group.getId()) + "/ldap/verify", request, pExpectedHttpStatusCode);
  }

  private JSONObject getLDAPVerifyRequest(final String requestId, final int pExpectedHttpStatusCode)
      throws Exception {
    return doAuthedJsonGet(
        _user,
        getBaseUrl(_group.getId()) + String.format("/ldap/verify/%s", requestId),
        pExpectedHttpStatusCode);
  }

  private void assertLDAPVerifyResultsJSON(
      final JSONObject pRequestJSON,
      final JSONObject pResultsJSON,
      final LDAPVerifyConnectivityJobRequest.Status pExpectedStatus,
      final List<
              Pair<
                  LDAPVerifyConnectivityJobRequestValidation.ValidationType,
                  LDAPVerifyConnectivityJobRequestValidation.Status>>
          pExpectedValidations)
      throws JSONException {
    assertFalse(
        pResultsJSON.isNull(ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.REQUEST_ID_FIELD));
    assertEquals(
        _group.getId().toString(),
        pResultsJSON.get(ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.GROUP_ID_FIELD));
    assertFalse(pResultsJSON.isNull(ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.REQUEST_FIELD));
    assertEquals(
        pExpectedStatus.name(),
        pResultsJSON.getString(ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.STATUS_FIELD));

    final JSONObject resultRequest = pResultsJSON.getJSONObject("request");
    assertEquals(
        pRequestJSON.getString(ApiAtlasNDSLDAPView.HOSTNAME_FIELD),
        resultRequest.getString(ApiAtlasNDSLDAPView.HOSTNAME_FIELD));
    assertEquals(
        pRequestJSON.getInt(ApiAtlasNDSLDAPView.PORT_FIELD),
        resultRequest.getInt(ApiAtlasNDSLDAPView.PORT_FIELD));
    assertEquals(
        pRequestJSON.getString(ApiAtlasNDSLDAPView.BIND_USERNAME_FIELD),
        resultRequest.getString(ApiAtlasNDSLDAPView.BIND_USERNAME_FIELD));
    assertTrue(resultRequest.isNull(ApiAtlasNDSLDAPView.BIND_PASSWORD_FIELD));

    final JSONArray validationsJSON =
        pResultsJSON.getJSONArray(
            ApiAtlasNDSLDAPVerifyConnectivityJobRequestView.VALIDATIONS_FIELD);
    assertEquals(pExpectedValidations.size(), validationsJSON.length());
    for (int i = 0; i < pExpectedValidations.size(); i++) {
      final JSONObject validationJSON = validationsJSON.getJSONObject(i);
      assertEquals(
          validationJSON.getString(
              ApiAtlasNDSLDAPVerifyConnectivityJobRequestValidationView.VALIDATION_TYPE_FIELD),
          pExpectedValidations.get(i).getLeft().name());

      assertEquals(
          validationJSON.getString(
              ApiAtlasNDSLDAPVerifyConnectivityJobRequestValidationView.STATUS_FIELD),
          pExpectedValidations.get(i).getRight().name());
    }
  }

  private String getBaseUrl(final ObjectId pGroupId) {
    return String.format(BASE_URL, pGroupId);
  }
}
