package com.xgen.svc.nds.aws.planner;

import static com.xgen.cloud.nds.common._public.model.NodeReadPreferenceTag.READY_DISK_STATE;
import static com.xgen.cloud.nds.common._public.model.NodeReadPreferenceTag.WARMING_DISK_STATE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.ReplicaSetMember;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.diskwarming._private.dao.DiskWarmingInstanceDao;
import com.xgen.cloud.nds.diskwarming._public.model.DiskWarmingInstance;
import com.xgen.cloud.nds.diskwarming._public.model.DiskWarmingInstance.State;
import com.xgen.cloud.nds.diskwarming._public.svc.DiskWarmingSvc;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.ClusterMaintenanceDecisions;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.DiskWarmingMode;
import com.xgen.cloud.nds.project._public.model.RegionSpec;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.planner.EnsureConnectivityForTopologyChangeMove;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import com.xgen.svc.nds.planner.WaitForMachineHealthyMove;
import com.xgen.svc.nds.svc.planning.NDSPlanningSvc.AutomationConfigContext;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Test;
import org.slf4j.LoggerFactory;

public class AWSAutomationSetDiskWarmingTagIntTests extends AWSExternalIntTest {

  @Inject private NDSClusterSvc _clusterSvc;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private HostSvc _hostSvc;
  @Inject private DiskWarmingSvc _diskWarmingSvc;
  @Inject private DiskWarmingInstanceDao _diskWarmingInstanceDao;
  @Inject private AppSettings _appSettings;

  private Plan _setupPlan;
  private ClusterDescription _clusterDescription;

  @Override
  @After
  public void tearDown() throws InterruptedException {
    if (_setupPlan != null) {
      waitForPlanRollbackSuccess(_setupPlan);
    }
    super.tearDown();
  }

  @Test(timeout = 60 * 60 * 1000L)
  public void testAddNode_visible_earlier() {
    _setupPlan = setupPlanWithContainerAndSingleInstance(AWSNDSInstanceSize.M10);
    waitForPlanPerformSuccess(_setupPlan);
    testAddNodeInternal(true);
  }

  @Test(timeout = 60 * 60 * 1000L)
  public void testAddNode_fully_warmed() {
    _setupPlan = setupPlanWithContainerAndSingleInstance(AWSNDSInstanceSize.M10);
    waitForPlanPerformSuccess(_setupPlan);
    testAddNodeInternal(false);
  }

  private void testAddNodeInternal(final boolean isVisibleEarlierMode) {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        getGroup(), null, FeatureFlag.ATLAS_IGNORE_DISK_WARMING_STATE);
    final ClusterDescription setupClusterDescription = getClusterDescription();

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final ObjectId instanceId = getInstanceIds().get(1);

    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            setupClusterDescription.getName(),
            instanceId,
            true,
            _testInjector);
    plan.addMove(provisionMachineMove);

    final List<RegionConfig> updateRegionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_WEST_1,
                RegionSpec.MAX_PRIORITY,
                setupClusterDescription.getReplicationSpecsWithShardData().get(0).getTotalNodes()
                    + 1,
                0,
                0,
                0,
                CpuArchitecture.X86_64));
    final ReplicationSpec updatedReplicationSpec =
        new ReplicationSpec(
            setupClusterDescription.getReplicationSpecsWithShardData().get(0).getId(),
            setupClusterDescription.getReplicationSpecsWithShardData().get(0).getExternalId(),
            setupClusterDescription.getReplicationSpecsWithShardData().get(0).getZoneId(),
            setupClusterDescription.getReplicationSpecsWithShardData().get(0).getZoneName(),
            1,
            updateRegionConfigs);

    final ClusterDescription updatedClusterDescription =
        setupClusterDescription
            .copy()
            .setReplicationSpecList(Collections.singletonList(updatedReplicationSpec))
            // Simulate cluster created correctly in order to set mongoDBUriHosts for
            // assumptions in the logic of
            // ProcessAutomationConfigPerClusterMove::existingMongodHostnamesMatchDesiredVotingTopology
            .setMongoUriHosts(
                new String[] {
                  getReplicaSetHardwareForInstance(
                          setupClusterDescription.getName(), getInstanceIds().get(0))
                      .getHardware()
                      .get(0)
                      .getHostnameForAgents()
                      .get()
                })
            .setDiskWarmingMode(
                Optional.of(
                    isVisibleEarlierMode
                        ? DiskWarmingMode.VISIBLE_EARLIER
                        : DiskWarmingMode.FULLY_WARMED))
            .build();
    getClusterDescriptionDao().save(updatedClusterDescription);

    final ProcessAutomationConfigPerClusterMove configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);

    configMove.addPredecessor(provisionMachineMove);

    plan.addMove(configMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionMachineMove);

      final ReplicaSetHardware replicaSetHardware =
          getReplicaSetHardwareForInstance(getClusterDescription().getName(), instanceId);
      final String hostname =
          replicaSetHardware.getById(instanceId).get().getHostnameForAgents().get();

      final Optional<DiskWarmingInstance> warmingInstance =
          _diskWarmingSvc.findInstanceByInstanceId(instanceId);
      assertTrue(warmingInstance.isPresent());
      assertEquals(hostname, warmingInstance.get().getHostname());

      // mock disk warming in progress
      final boolean updateDocResult =
          _diskWarmingSvc.setWarmingDocToInProgress(
              warmingInstance.get().getHostname(),
              warmingInstance.get().getCloudProviderInstanceId());
      assertTrue(updateDocResult);

      // phase 1 completes
      Result<NoData> result = configMove.perform();
      assertTrue(result.getStatus().isInProgress());

      final AutomationConfig initialConfig =
          getAutomationConfigSvc().findPublishedOrEmpty(getGroupId());
      final Process process =
          initialConfig.getDeployment().getProcessesByHostname(hostname).stream()
              .filter(p -> p.getProcessType() == ProcessType.MONGOD)
              .findFirst()
              .get();
      final ReplicaSetMember member = initialConfig.getDeployment().getReplicaSetMember(process);

      assertEquals(0, (int) member.getPriority());
      assertTrue(member.isHidden());
      assertTrue(member.getTags().containsValue(WARMING_DISK_STATE));

      if (isVisibleEarlierMode) {
        waitForMovePerformMidWithUnhiddenNode(configMove, getGroupId(), hostname);

        // node is unhidden and warming tag is still set
        final AutomationConfig updatedConfig =
            getAutomationConfigSvc().findPublishedOrEmpty(getGroupId());
        final ReplicaSetMember unhiddenMember =
            updatedConfig.getDeployment().getReplicaSetMember(process);
        assertTrue(unhiddenMember.getTags().containsValue(WARMING_DISK_STATE));
        assertFalse(unhiddenMember.isHidden());
      }

      // mock warming completes
      _diskWarmingInstanceDao.updateStateByHostname(hostname, State.COMPLETED, new Date());
      waitForMovePerformDone(configMove);

      final AutomationConfig finalConfig =
          getAutomationConfigSvc().findPublishedOrEmpty(getGroupId());
      assertEquals(
          RegionSpec.MAX_PRIORITY,
          (int) finalConfig.getDeployment().getReplicaSetMember(process).getPriority());
      finalConfig.getDeployment().getReplicaSets().stream()
          .flatMap(rs -> rs.getMembers().stream())
          .forEach(
              rsMember -> {
                assertTrue(rsMember.getTags().containsValue(READY_DISK_STATE));
                assertFalse(rsMember.isHidden());
              });
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }

  @Test(timeout = 60 * 60 * 1000L)
  public void testWaitForMachineHealthyMove_visible_earlier() {
    final ClusterDescription setupClusterDescription = getClusterDescription();
    _clusterDescription =
        setupClusterDescription
            .copy()
            .setDiskWarmingMode(Optional.of(DiskWarmingMode.VISIBLE_EARLIER))
            .build();
    getClusterDescriptionDao().save(_clusterDescription);

    _setupPlan = setupPlanWithContainerAndInstances();
    waitForPlanPerformSuccess(_setupPlan);
    testWaitForMachineHealthyMoveInternal(true);
  }

  @Test(timeout = 60 * 60 * 1000L)
  public void testWaitForMachineHealthyMove_fully_warmed() {
    final ClusterDescription setupClusterDescription = getClusterDescription();
    _clusterDescription =
        setupClusterDescription
            .copy()
            .setDiskWarmingMode(Optional.of(DiskWarmingMode.FULLY_WARMED))
            .build();
    getClusterDescriptionDao().save(_clusterDescription);

    _setupPlan = setupPlanWithContainerAndInstances();
    waitForPlanPerformSuccess(_setupPlan);
    testWaitForMachineHealthyMoveInternal(false);
  }

  private void testWaitForMachineHealthyMoveInternal(final boolean pIsVisibleEarlierMode) {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        getGroup(), null, FeatureFlag.ATLAS_IGNORE_DISK_WARMING_STATE);
    forceReplaceMachine();
    final Plan plan = setupPlanForMachineReplacement();
    assertEquals(
        1, plan.getMoves().stream().filter(AWSDestroyMachineMove.class::isInstance).count());
    final Move destroyMachineMove =
        plan.getMoves().stream().filter(AWSDestroyMachineMove.class::isInstance).findFirst().get();
    assertEquals(
        1, plan.getMoves().stream().filter(AWSProvisionMachineMove.class::isInstance).count());
    final Move provisionMachineMove =
        plan.getMoves().stream()
            .filter(AWSProvisionMachineMove.class::isInstance)
            .findFirst()
            .get();
    assertEquals(
        1, plan.getMoves().stream().filter(WaitForMachineHealthyMove.class::isInstance).count());
    final Move waitForMachineHealthyMove =
        plan.getMoves().stream()
            .filter(WaitForMachineHealthyMove.class::isInstance)
            .findFirst()
            .get();

    // initial config
    final ObjectId instanceId = getInstanceIds().get(0);
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                plan.getGroupId(), getClusterDescription().getName(), instanceId);
    final InstanceHardware instanceHardware = replicaSetHardware.getById(instanceId).get();
    final String hostname = instanceHardware.getHostnameForAgents().get();
    final AutomationConfig configBeforeReplacement =
        getAutomationConfigSvc().findPublishedOrEmpty(plan.getGroupId());
    final Process tobeWarmedProcess =
        configBeforeReplacement.getDeployment().getProcessesByHostname(hostname).stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .findFirst()
            .get();
    final ReplicaSetMember tobeWarmedMember =
        configBeforeReplacement.getDeployment().getReplicaSetMember(tobeWarmedProcess);

    // plan start
    waitForMovePerformDone(destroyMachineMove);

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    final AutomationConfig config =
        getAutomationConfigSvc().findPublishedOrEmpty(plan.getGroupId());
    final long initialVersion = config.getVersion();

    // confirm warming member has diskState: WARMING tag
    final Process warmingProcess =
        config.getDeployment().getProcessesByHostname(hostname).stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .findFirst()
            .get();
    final ReplicaSetMember member = config.getDeployment().getReplicaSetMember(warmingProcess);
    assertTrue(member.isHidden());
    assertTrue(member.getTags().containsValue(WARMING_DISK_STATE));
    final Optional<DiskWarmingInstance> warmingInstance =
        _diskWarmingSvc.findInstanceByInstanceId(instanceId);
    assertTrue(warmingInstance.isPresent());
    assertEquals(tobeWarmedMember.getPriority(), warmingInstance.get().getNodePriority(), 0);

    // mock disk warming in progress
    final boolean updateDocResult =
        _diskWarmingSvc.setWarmingDocToInProgress(
            warmingInstance.get().getHostname(),
            warmingInstance.get().getCloudProviderInstanceId());
    assertTrue(updateDocResult);
    final Date t0 = new Date();

    try {
      Result<NoData> result = waitForMachineHealthyMove.perform();
      assertTrue(result.getStatus().isInProgress());

      final AutomationConfig initialConfig =
          getAutomationConfigSvc().findPublishedOrEmpty(getGroupId());
      final Process process =
          initialConfig.getDeployment().getProcessesByHostname(hostname).stream()
              .filter(p -> p.getProcessType() == ProcessType.MONGOD)
              .findFirst()
              .get();

      if (pIsVisibleEarlierMode) {
        waitForMovePerformMidWithUnhiddenNode(waitForMachineHealthyMove, getGroupId(), hostname);

        // node is unhidden and warming tag is still set
        final AutomationConfig updatedConfig =
            getAutomationConfigSvc().findPublishedOrEmpty(getGroupId());
        final ReplicaSetMember unhiddenMember =
            updatedConfig.getDeployment().getReplicaSetMember(process);
        assertTrue(unhiddenMember.getTags().containsValue(WARMING_DISK_STATE));
        assertFalse(unhiddenMember.isHidden());
      }
      // mock warming completes
      final Optional<DiskWarmingInstance> completedInstance =
          _diskWarmingInstanceDao.updateStateByHostname(hostname, State.COMPLETED, new Date());
      assertTrue(completedInstance.isPresent());

      waitForMovePerformDone(waitForMachineHealthyMove);

      // verify that WaitForMachineHealthyMove took at least 1 minute
      final Date t1 = new Date();
      assertTrue(Duration.between(t0.toInstant(), t1.toInstant()).toMinutes() >= 1);

      assertTrue(hostHasPrimaries(hostname));
      assertTrue(isHostInGoalState(hostname, initialVersion));
      assertTrue(isHostHealthy(hostname));

      final AutomationConfig finalConfig =
          getAutomationConfigSvc().findPublishedOrEmpty(getGroupId());
      final ReplicaSetMember finalMember = finalConfig.getDeployment().getReplicaSetMember(process);
      assertTrue(finalMember.getTags().containsValue(READY_DISK_STATE));
      assertFalse(finalMember.isHidden());
      assertEquals(tobeWarmedMember.getPriority(), finalMember.getPriority(), 0);
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }

  private void forceReplaceMachine() {
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName())
            .get(0);

    final InstanceHardware instanceHardware = replicaSetHardware.getHardware().get(0);
    // Wait for agents to ping back
    final String replSetName = replicaSetHardware.getRsId();
    waitForAgentPing(replSetName, getNDSGroup().getGroupId());
    final long lastPingTime = new Date().getTime();

    // Make one hardware unhealthy
    getHardwareDao()
        .setInstanceForceReplacement(
            replicaSetHardware.getId(), instanceHardware.getInstanceId(), false);

    getPlanningSvc()
        .refreshClusterHardwareActions(
            getNDSGroup(),
            getGroup(),
            getHostClusterLifecycleSvc()
                .findHostClustersByGroupId(getNDSGroup().getGroupId(), true),
            lastPingTime,
            LoggerFactory.getLogger("foo"),
            Cluster.getCluster(
                getClusterDescription(),
                getHardwareDao()
                    .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName())),
            List.of(),
            new AutomationConfigContext(getAutomationConfigSvc(), getGroupId()),
            new ClusterMaintenanceDecisions.Builder());
  }

  private void waitForAgentPing(final String pReplSetName, final ObjectId pGroupId) {
    waitForCondition(
        Duration.ofMinutes(2),
        () -> {
          final List<Host> hosts =
              getHostSvc()
                  .getHostDao()
                  .findByReplicaSetGroupIdAndType(
                      pReplSetName, pGroupId, Arrays.asList(HostType.values()));

          final Date now = new Date();

          final boolean hasRecentPrimaryOrSecondary =
              hosts.stream()
                  .filter(
                      h ->
                          h.getLastPing() != null
                              && h.getLastPing()
                                  .after(
                                      new Date(now.getTime() - Duration.ofMinutes(2).toMillis())))
                  .anyMatch(h -> h.getIsPrimary() || h.getIsSecondary());
          return hasRecentPrimaryOrSecondary;
        });
  }

  private Plan setupPlanForMachineReplacement() {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final ObjectId instanceId = getInstanceIds().get(0);

    final Move destroyMachineMove =
        AWSDestroyMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            instanceId,
            false,
            _testInjector);

    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            instanceId,
            true,
            _testInjector);
    provisionMachineMove.addPredecessor(destroyMachineMove);

    final WaitForMachineHealthyMove wfmhm =
        WaitForMachineHealthyMove.factoryCreate(
            plan.getPlanContext(), getClusterDescription().getName(), getInstanceIds().get(0));
    wfmhm.addPredecessor(provisionMachineMove);

    plan.addMove(destroyMachineMove);
    plan.addMove(provisionMachineMove);
    plan.addMove(wfmhm);
    final EnsureConnectivityForTopologyChangeMove ecftcm =
        EnsureConnectivityForTopologyChangeMove.factoryCreate(
            plan.getPlanContext(), getClusterDescription().getName());
    plan.addMove(ecftcm);
    ecftcm.addPredecessor(wfmhm);

    getPlanDao().save(plan);

    return plan;
  }
}
