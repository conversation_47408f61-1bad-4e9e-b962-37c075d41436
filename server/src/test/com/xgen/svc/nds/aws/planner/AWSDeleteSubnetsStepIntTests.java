package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertTrue;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.Test;

public class AWSDeleteSubnetsStepIntTests extends AWSExternalIntTest {

  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private GroupDao _groupDao;

  private void testAWSDeleteSubnetsStepInternal(final Plan pPlan, final List<String> pSubnetIds)
      throws InterruptedException {

    final AWSDeleteSubnetsStep step =
        new AWSDeleteSubnetsStep(
            (NDSPlanContext) pPlan.getPlanContext(),
            new Step.State(
                pPlan.getId(),
                pPlan.getMoves().get(0).getId(),
                2,
                pPlan.getPlanContext().getPlanDao()),
            getContainer(),
            pSubnetIds,
            _awsApiSvc,
            getOrphanedItemSvc());

    waitForStepPerformDone(step);
    verifySubnetsDeleted(pSubnetIds);
    assertTrue(step.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSDeleteSubnetsStep() throws InterruptedException {

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final AWSCreateVpcStep createVpcStep =
        new AWSCreateVpcStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                0,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            _awsApiSvc,
            getOrphanedItemSvc(),
            getOrphanedItemDao(),
            _groupDao);

    try {
      waitForStepPerformDone(createVpcStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createVpcStep);
      throw t;
    }

    final NDSPlanContext context = (NDSPlanContext) plan.getPlanContext();

    final AWSCreateSubnetsStep createSubnetsStep =
        new AWSCreateSubnetsStep(
            context,
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                1,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            createVpcStep.getVpcId().get(),
            _awsApiSvc,
            _awsAccountDao,
            getOrphanedItemSvc(),
            _groupDao);

    try {
      waitForStepPerformDone(createSubnetsStep);
    } catch (final Throwable t) {
      cleanupSubnets(createSubnetsStep.getSubnetIds());
      throw t;
    }

    final List<String> subnetIds =
        createSubnetsStep.perform().getData().getSubnets().stream()
            .map(AWSSubnet::getSubnetId)
            .collect(Collectors.toList());

    try {
      testAWSDeleteSubnetsStepInternal(plan, subnetIds);
    } catch (final Throwable t) {
      cleanupSubnets(subnetIds);
      throw t;
    } finally {
      waitForStepRollbackDone(createVpcStep);
    }
  }
}
