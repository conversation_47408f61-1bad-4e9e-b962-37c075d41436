package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.Test;

public class AWSDestroyContainerMoveIntTests extends AWSExternalIntTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;

  public void testAWSDestroyContainerMoveInternal(final Move pMove) throws InterruptedException {
    NDSGroup group = _ndsGroupDao.find(getGroup().getId()).get();
    AWSCloudProviderContainer container =
        (AWSCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

    final AWSCloudProviderContainer originalContainer = container;

    Result<NoData> result = pMove.perform();
    while (!result.getStatus().isDone()) {

      assertNotFailed(pMove.getClass().getSimpleName(), result);
      assertTrue(container.getVpcId().isPresent());
      assertTrue(container.getIgwId().isPresent());
      assertNotEquals(0, container.getSubnets().length);

      Thread.sleep(Duration.ofSeconds(5).toMillis());

      result = pMove.perform();
      group = _ndsGroupDao.find(getGroup().getId()).get();
      container =
          (AWSCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();
    }

    group = _ndsGroupDao.find(getGroup().getId()).get();
    container =
        (AWSCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

    assertFalse(container.getVpcId().isPresent());
    assertFalse(container.getIgwId().isPresent());
    assertEquals(0, container.getSubnets().length);

    verifyVpcDeleted(originalContainer.getVpcId().get());
    verifyInternetGatewayDeleted(originalContainer.getIgwId().get());
    verifySubnetsDeleted(
        Stream.of(originalContainer.getSubnets())
            .map(AWSSubnet::getSubnetId)
            .collect(Collectors.toList()));

    verifyCIDRNotInAuthRestrictions(
        _automationConfigSvc.findPublishedOrEmpty(getGroup().getId()), container.getAtlasCidr());

    assertTrue(pMove.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSDestroyContainerMove() throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    final Move dummyDestroyContainerMove =
        getMoveFromPlanner(
            AWSDestroyContainerMove.class, plan.getPlanContext(), getContainer().getId());
    plan.addMove(provisionContainerMove);
    plan.addMove(dummyDestroyContainerMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      testAWSDestroyContainerMoveInternal(dummyDestroyContainerMove);
    } catch (final Throwable t) {
      cleanupSubnets(
          Stream.of(getContainer().getSubnets())
              .map(AWSSubnet::getSubnetId)
              .collect(Collectors.toList()));
      cleanupInternetGateway(getContainer().getIgwId().get());
      cleanupVpc(getContainer().getVpcId().get());
      throw t;
    }
  }
}
