package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;

import com.xgen.cloud.atm.core._public.svc.AutomationAgentAuditSvc;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Collections;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AWSSyncPauseStateMoveIntTests extends AWSExternalIntTest {

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AWSInstanceHardwareDao _instanceHardwareDao;
  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private AutomationAgentAuditSvc _automationAgentAuditSvc;

  private void testAWSSyncPauseStateInternal(final ObjectId pInstanceId, final boolean pIsPaused)
      throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final AWSSyncPauseStateMove syncPauseStateMove =
        AWSSyncPauseStateMove.factoryCreate(
            (NDSPlanContext) plan.getPlanContext(), getClusterDescription().getName(), pInstanceId);

    plan.addMove(syncPauseStateMove);
    getPlanDao().save(plan);

    final AWSInstanceHardware oldHardware =
        (AWSInstanceHardware)
            getInstanceHardware(plan.getGroupId(), getClusterDescription().getName(), pInstanceId);

    assertEquals(oldHardware.getAction(), syncPauseStateMove.getMoveTriggeringAction());

    waitForMovePerformDone(syncPauseStateMove);

    final ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(getClusterDescription().getName(), pInstanceId);
    final InstanceHardware instanceHardware = getInstanceHardware(replicaSetHardware, pInstanceId);

    assertEquals(pIsPaused, instanceHardware.isPaused());
  }

  @Test(timeout = 25 * 60 * 1000L)
  public void testAWSSyncPauseStateMove() throws Exception {

    // In order to properly pause/resume machine, we need a automation config to have been
    // published.
    // However, we don't need to launch 3 nodes in order to run this test successfully.
    final ReplicationSpec currentSpec =
        getClusterDescription().getReplicationSpecsWithShardData().get(0);
    final int numHiddenNodes = 0;
    getClusterDescriptionDao()
        .save(
            getClusterDescription()
                .copy()
                .setReplicationSpecList(
                    getReplicationSpecs(currentSpec.getId(), 1, 1, numHiddenNodes))
                .build());

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final ObjectId instanceId = getInstanceIds().get(0);
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    final Move configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);
    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            instanceId,
            true,
            _testInjector);

    plan.addMove(provisionContainerMove);
    plan.addMove(configMove);
    plan.addMove(provisionMachineMove);
    provisionMachineMove.addPredecessor(provisionContainerMove);
    configMove.addPredecessor(provisionMachineMove);

    getPlanDao().save(plan);

    try {
      // Wait for the cluster creation
      waitForPlanPerformSuccess(plan);

      // Wait for agent ping so that we have a primary
      Thread.sleep(Duration.ofMinutes(1).toMillis());

      // Save 'true' to the isPaused property
      getClusterDescriptionDao().save(getClusterDescription().copy().setIsPaused(true).build());

      // Test the pausing of an instance
      testAWSSyncPauseStateInternal(instanceId, true);

      // Save 'false' to the isPaused property
      getClusterDescriptionDao().save(getClusterDescription().copy().setIsPaused(false).build());

      // Test the resuming of an instance
      testAWSSyncPauseStateInternal(instanceId, false);
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }
}
