package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.time.Duration;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AWSSwapMachineMoveIntTests extends AWSExternalIntTest {
  private ObjectId _instanceId;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  private void testAWSSwapMachineMoveInternal(final AWSSwapMachineMove pDummySwapMachineMove)
      throws InterruptedException {
    final ClusterDescription clusterDescription = getClusterDescription();

    final AWSInstanceHardware originalHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);

    _replicaSetHardwareDao.setInstanceOSSwap(
        getReplicaSetHardwareForInstance(clusterDescription.getName(), _instanceId).getId(),
        _instanceId,
        originalHardware.isNVMe(),
        true);

    Result<Result.NoData> result;
    do {
      result = pDummySwapMachineMove.perform();
      assertNotFailed(pDummySwapMachineMove.getClass().getSimpleName(), result);
      final AWSInstanceHardware instanceHardware =
          (AWSInstanceHardware)
              getInstanceHardware(
                  pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                  clusterDescription.getName(),
                  _instanceId);
      assertTrue(instanceHardware.getNVMePrioritizedEBSId().isPresent());
      assertTrue(instanceHardware.getNVMePrioritizedDiskIOPS().isPresent());
      assertTrue(instanceHardware.getNVMePrioritizedEBSVolumeType().isPresent());
      assertTrue(instanceHardware.getEIPId().isPresent());
      assertTrue(instanceHardware.getEC2InstanceId().isPresent());
      assertTrue(instanceHardware.getInstanceSize().isPresent());
      assertTrue(instanceHardware.getHostnames().getLegacyHostname().isPresent());
      assertFalse(instanceHardware.needsForceReplacement());
      assertEquals(originalHardware.getMemberIndex(), instanceHardware.getMemberIndex());
      assertEquals(originalHardware.getEBSId(), instanceHardware.getEBSId());
      assertEquals(originalHardware.getEIPId(), instanceHardware.getEIPId());
      assertEquals(originalHardware.getHostnames(), instanceHardware.getHostnames());

      if (result.getStatus().isDone()) {
        assertNotEquals(originalHardware.getEC2InstanceId(), instanceHardware.getEC2InstanceId());
        assertFalse(instanceHardware.needsOSSwap());
      } else {
        if (pDummySwapMachineMove.hardwareMetadataUpdated()) {
          assertFalse(instanceHardware.needsOSSwap());
        } else {
          assertTrue(instanceHardware.needsOSSwap());
        }
      }
      Thread.sleep(Duration.ofSeconds(5).toMillis());
    } while (!result.getStatus().isDone());

    waitForMoveRollbackDone(pDummySwapMachineMove);

    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);
    assertTrue(instanceHardware.needsForceReplacement());
    assertFalse(instanceHardware.needsOSSwap());
    assertTrue(instanceHardware.getEC2InstanceId().isPresent());
  }

  private void testAWSSwapMachineMoveRollbackInternal_ForceReplace(
      final AWSSwapMachineMove pDummySwapMachineMove) throws InterruptedException {
    final Plan plan1 = new Plan(getGroup().getId(), getPlanContextFactory());
    final ClusterDescription clusterDescription = getClusterDescription();

    AWSInstanceHardware originalHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);
    _replicaSetHardwareDao.setInstanceOSSwap(
        getReplicaSetHardwareForInstance(clusterDescription.getName(), _instanceId).getId(),
        _instanceId,
        originalHardware.isNVMe(),
        true);

    // Fail on wait for automation agent
    final AWSSwapMachineMove spyMove1 = spy(pDummySwapMachineMove);
    plan1.addMove(spyMove1);
    getPlanDao().save(plan1);

    doReturn(Result.failed()).when(spyMove1).waitForAutomationAgentStart(any(), any());

    originalHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);
    Result<Result.NoData> result;
    do {
      result = spyMove1.perform();
      Thread.sleep(Duration.ofSeconds(5).toMillis());
    } while (!result.getStatus().isFailed());

    waitForMoveRollbackDone(spyMove1);
    AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);

    // Verify instance has new hardware with need force replacement set
    assertNotEquals(originalHardware.getEC2InstanceId(), instanceHardware.getEC2InstanceId());
    assertEquals(originalHardware.getEIPId(), instanceHardware.getEIPId());
    assertEquals(originalHardware.getEBSId(), instanceHardware.getEBSId());
    assertTrue(instanceHardware.needsForceReplacement());
  }

  private void testAWSSwapMachineMoveRollbackInternal_RevertToOldHardware(
      final AWSSwapMachineMove pDummySwapMachineMove) throws InterruptedException {
    final Plan plan2 = new Plan(getGroup().getId(), getPlanContextFactory());
    final ClusterDescription clusterDescription = getClusterDescription();

    AWSInstanceHardware originalHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);
    _replicaSetHardwareDao.setInstanceOSSwap(
        getReplicaSetHardwareForInstance(clusterDescription.getName(), _instanceId).getId(),
        _instanceId,
        originalHardware.isNVMe(),
        true);

    // Fail on attach elastic IP
    final AWSSwapMachineMove spyMove2 = spy(pDummySwapMachineMove);
    plan2.addMove(spyMove2);
    getPlanDao().save(plan2);
    doReturn(Result.failed()).when(spyMove2).attachElasticIp(any(), any(), any());

    Result<NoData> result;
    do {
      result = spyMove2.perform();
      Thread.sleep(Duration.ofSeconds(5).toMillis());
    } while (!result.getStatus().isFailed());

    waitForMoveRollbackDone(spyMove2);
    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);

    // verify instance is reverted to same hardware
    assertEquals(originalHardware.getEC2InstanceId(), instanceHardware.getEC2InstanceId());
    assertEquals(originalHardware.getEIPId(), instanceHardware.getEIPId());
    assertEquals(originalHardware.getEBSId(), instanceHardware.getEBSId());
  }

  private void testAWSSwapMachineMoveRollbackInternal_BeforeNewInstanceProvisioned(
      final AWSSwapMachineMove pDummySwapMachineMove) throws InterruptedException {
    final Plan plan3 = new Plan(getGroup().getId(), getPlanContextFactory());
    final ClusterDescription clusterDescription = getClusterDescription();

    AWSInstanceHardware originalHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);
    _replicaSetHardwareDao.setInstanceOSSwap(
        getReplicaSetHardwareForInstance(clusterDescription.getName(), _instanceId).getId(),
        _instanceId,
        originalHardware.isNVMe(),
        true);

    // Fail on create new instance
    final AWSSwapMachineMove spyMove3 = spy(pDummySwapMachineMove);
    plan3.addMove(spyMove3);
    getPlanDao().save(plan3);
    doReturn(Result.failed())
        .when(spyMove3)
        .createOSSwapInstance(any(), any(), any(), any(), any(), any(), any(), any());

    Result<NoData> result;
    do {
      result = spyMove3.perform();
      Thread.sleep(Duration.ofSeconds(5).toMillis());
    } while (!result.getStatus().isFailed());

    waitForMoveRollbackDone(spyMove3);
    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            getInstanceHardware(
                pDummySwapMachineMove.getNDSPlanContext().getGroupId(),
                clusterDescription.getName(),
                _instanceId);

    // verify instance is reverted to same hardware
    assertEquals(originalHardware.getEC2InstanceId(), instanceHardware.getEC2InstanceId());
    assertEquals(originalHardware.getEIPId(), instanceHardware.getEIPId());
    assertEquals(originalHardware.getEBSId(), instanceHardware.getEBSId());
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testAWSSwapMachineMoveRollback() throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    final ClusterDescription clusterDescription = getClusterDescription();

    _instanceId = findInstanceIdWithNonZeroMemberIndex();

    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            _instanceId,
            true,
            _testInjector);
    final NDSPlanContext context = (NDSPlanContext) plan.getPlanContext();
    final AWSSwapMachineMove dummySwapMachineMove1 =
        AWSSwapMachineMove.factoryCreate(context, clusterDescription.getName(), _instanceId);
    final AWSSwapMachineMove dummySwapMachineMove2 =
        AWSSwapMachineMove.factoryCreate(context, clusterDescription.getName(), _instanceId);
    final AWSSwapMachineMove dummySwapMachineMove3 =
        AWSSwapMachineMove.factoryCreate(context, clusterDescription.getName(), _instanceId);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    plan.addMove(dummySwapMachineMove1);
    plan.addMove(dummySwapMachineMove2);
    plan.addMove(dummySwapMachineMove3);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    try {
      testAWSSwapMachineMoveRollbackInternal_ForceReplace(dummySwapMachineMove1);
      testAWSSwapMachineMoveRollbackInternal_RevertToOldHardware(dummySwapMachineMove2);
      testAWSSwapMachineMoveRollbackInternal_BeforeNewInstanceProvisioned(dummySwapMachineMove3);
    } catch (final Throwable t) {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroup().getId(), clusterDescription.getName(), _instanceId);
      final AWSInstanceHardware instanceHardware =
          (AWSInstanceHardware) getInstanceHardware(replicaSetHardware, _instanceId);
      if (instanceHardware.isProvisioned()) {
        cleanupEC2Instance(instanceHardware.getEC2InstanceId().get());
        cleanupEBSVolume(instanceHardware.getNVMePrioritizedEBSId().get());
        cleanupElasticIp(instanceHardware.getEIPId().get());
        cleanupDNSRecord(
            DNSRecordUtil.getLegacyDNSRecordForInstance(
                getNDSGroup(),
                replicaSetHardware,
                clusterDescription,
                instanceHardware.getInstanceId(),
                NDSSettings.getCrossCloudInstanceDomainName(getAppSettings())));
      }
      throw t;
    }
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testAWSSwapMachineMove() throws Exception {

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    _instanceId = findInstanceIdWithNonZeroMemberIndex();

    final ClusterDescription clusterDescription = getClusterDescription();

    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            _instanceId,
            true,
            _testInjector);
    final AWSSwapMachineMove dummySwapMachineMove =
        AWSSwapMachineMove.factoryCreate(
            (NDSPlanContext) plan.getPlanContext(), clusterDescription.getName(), _instanceId);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    plan.addMove(dummySwapMachineMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    try {
      testAWSSwapMachineMoveInternal(dummySwapMachineMove);
    } catch (final Throwable t) {
      final ReplicaSetHardware replicaSetHardware =
          _replicaSetHardwareDao.findReplicaSetHardwareForInstance(
              getGroup().getId(), clusterDescription.getName(), _instanceId);
      final AWSInstanceHardware instanceHardware =
          (AWSInstanceHardware) getInstanceHardware(replicaSetHardware, _instanceId);
      if (instanceHardware.isProvisioned()) {
        cleanupEC2Instance(instanceHardware.getEC2InstanceId().get());
        cleanupEBSVolume(instanceHardware.getNVMePrioritizedEBSId().get());
        cleanupElasticIp(instanceHardware.getEIPId().get());
        cleanupDNSRecord(
            DNSRecordUtil.getLegacyDNSRecordForInstance(
                getNDSGroup(),
                replicaSetHardware,
                clusterDescription,
                instanceHardware.getInstanceId(),
                NDSSettings.getCrossCloudInstanceDomainName(getAppSettings())));
      }
      throw t;
    }
  }

  private ObjectId findInstanceIdWithNonZeroMemberIndex() {
    // Find a hardware instance with memberIndex > 0
    return getInstanceIds().stream()
        .filter(
            instanceId ->
                getInstanceHardware(
                            getGroup().getId(), getClusterDescription().getName(), instanceId)
                        .getMemberIndex()
                    > 0)
        .findFirst()
        .get();
  }
}
