package com.xgen.svc.nds.aws.svc.admincapacity;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSCheckCapacityRequestDao;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSPhysicalZoneId;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry.Status;
import com.xgen.cloud.nds.capacity._public.svc.AWSCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AWSAZCapacityCheckerSvcIntTests extends JUnit5BaseSvcTest {
  @Inject private AWSAZCapacityCheckerSvc awsAZCapacityCheckerSvc;
  @Inject private AWSCapacityDenylistSvc awsCapacityDenylistSvc;
  @Inject private AWSCheckCapacityRequestDao awsCheckCapacityRequestDao;
  @Inject private NDSGroupSvc ndsGroupSvc;
  @Inject private AWSAccountDao awsAccountDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
  }

  @Test
  public void testCheckAWSAZCapacity() throws SvcException {
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org);
    final ObjectId groupId = group.getId();
    awsAccountDao.save(
        NDSModelTestFactory.getFullyAvailableAWSAccount4Regions(new ObjectId(), "temp", false));
    ndsGroupSvc.ensureGroup(groupId);
    final AppSettings settings = AppConfig.getInstance(AppSettings.class);
    settings.setProp("mms.azCapacity.internalProjectId", groupId.toString(), SettingType.MEMORY);

    { // register mock capacity events
      // this will make US_EAST_2 have CAPACITY_UNAVAILABLE status
      for (int i = 0; i < 3; i++) {
        awsCapacityDenylistSvc.registerCapacityFailure(
            AWSInstanceFamily.T4G,
            AWSNDSInstanceSize.M10,
            AWSRegionName.US_EAST_2,
            Optional.of(new AWSPhysicalZoneId("use2-az1")));
      }
      // this sets AP_NORTHEAST_1 to be CAPACITY_UNAVAILABLE_OVERRIDE
      awsCapacityDenylistSvc.registerCapacityOverride(
          AWSInstanceFamily.T4G,
          AWSNDSInstanceSize.M10,
          AWSRegionName.AP_NORTHEAST_1,
          new AWSPhysicalZoneId("apne1-az2"),
          Status.CAPACITY_UNAVAILABLE_OVERRIDE,
          Optional.empty());

      // this places AP_NORTHEAST_2 into the tracked zones, but it is CAPACITY_AVAILABLE
      awsCapacityDenylistSvc.registerCapacityFailure(
          AWSInstanceFamily.T4G,
          AWSNDSInstanceSize.M10,
          AWSRegionName.AP_NORTHEAST_2,
          Optional.of(new AWSPhysicalZoneId("apne2-az1")));
    }

    // this sets EU_WEST_1 to be CAPACITY_AVAILABLE_OVERRIDE
    awsCapacityDenylistSvc.registerCapacityOverride(
        AWSInstanceFamily.T4G,
        AWSNDSInstanceSize.M10,
        AWSRegionName.EU_WEST_1,
        new AWSPhysicalZoneId("euw1-az3"),
        Status.CAPACITY_AVAILABLE_OVERRIDE,
        Optional.empty());

    assertEquals(4, awsCapacityDenylistSvc.getAllCapacityDenylistEntries().size());

    awsAZCapacityCheckerSvc.checkAZCapacity();
    final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>>
        checkCapacityRequests = awsCheckCapacityRequestDao.findAllRequests();

    // AP_NORTHEAST_2 and US_EAST_2 should've generated capacity check requests
    assertEquals(2, checkCapacityRequests.size());
    assertEquals(
        checkCapacityRequests.get(0).getInstanceSpecs().get(0).getRegionName(),
        AWSRegionName.US_EAST_2);
    assertEquals(
        checkCapacityRequests.get(1).getInstanceSpecs().get(0).getRegionName(),
        AWSRegionName.AP_NORTHEAST_2);
  }
}
