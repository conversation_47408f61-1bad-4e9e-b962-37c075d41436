package com.xgen.svc.nds.aws.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.Volume;
import com.amazonaws.services.ec2.model.VolumeType;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.AwsSnapshotFieldBuilder;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanStrategy;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.aws.planner.AWSCreateVolumeStep;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class AWSCreateVolumeFromSnapshotStepIntTests extends AWSExternalIntTest {

  private Plan _plan;

  @Inject AWSApiSvc _awsApiSvc;
  @Inject NDSOrphanedItemDao _orphanedItemDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private GroupDao _groupDao;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _plan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccess(_plan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(_plan);
      throw t;
    }
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    waitForPlanRollbackSuccess(_plan);
    super.tearDown();
  }

  public void testAWSCreateVolumeFromSnapshotStepInternal(final AWSCreateVolumeStep pStep) {

    // Now create volume
    waitForStepPerformDone(pStep);

    final Result<AWSCreateVolumeStep.Data> result = pStep.perform();

    final Volume volume =
        getAWSApiSvc()
            .findEBSVolume(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pStep.getLogger(),
                result.getData().getEBSVolumeId());

    assertEquals(result.getData().getEBSVolumeId(), volume.getVolumeId());
    assertEquals(result.getData().getEBSVolumeType(), volume.getVolumeType());
    assertEquals(result.getData().getEBSVolumeEncrypted(), volume.getEncrypted());
    assertEquals(result.getData().getSizeGB(), volume.getSize());
    assertEquals(result.getData().getIOPS(), volume.getIops());

    verifyTags(volume.getTags());

    waitForStepRollbackDone(pStep);

    // Make sure the volume is orphaned and still present in AWS
    verifyEBSVolumeOrphaned(result.getData().getEBSVolumeId(), false);
    assertNotNull(
        getAWSApiSvc()
            .findEBSVolume(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pStep.getLogger(),
                result.getData().getEBSVolumeId()));

    // Make sure rolling back does nothing
    assertTrue(pStep.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSCreateVolumeFromSnapshotStep() {
    final NDSPlanContext planContext = (NDSPlanContext) _plan.getPlanContext();

    final ObjectId planId = _plan.getId();
    final PlanDao planDao = getPlanDao();
    final ObjectId moveId = _plan.getMoves().get(1).getId();
    final String subnetId = getContainer().getSubnets()[0].getSubnetId();
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));

    final InstanceHardware instanceHardware =
        getInstanceHardware(replicaSetHardware, getInstanceIds().get(0));

    final AWSCreateVolumeStep step =
        new AWSCreateVolumeStep(
            planContext,
            new Step.State(planId, moveId, 0, planDao),
            _awsApiSvc,
            _orphanedItemDao,
            getContainer(),
            getClusterDescription(),
            replicaSetHardware,
            instanceHardware,
            subnetId,
            _groupDao,
            ResourceOperationTagType.NONE,
            false);

    try {
      waitForStepPerformDone(step);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForStepRollbackDone(step));
      throw t;
    }

    final Result<AWSCreateVolumeStep.Data> result = step.perform();

    final String ebsVolumeId = result.getData().getEBSVolumeId();

    final AWSCreateEbsSnapshotStep createEbsSnapshotStep =
        new AWSCreateEbsSnapshotStep(
            (NDSPlanContext) _plan.getPlanContext(),
            new State(planId, moveId, 1, planDao),
            getContainer(),
            getClusterDescription(),
            ebsVolumeId,
            AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId),
            false,
            null,
            new ObjectId(),
            AWSCreateEbsSnapshotStep.SnapshotType.BACKUP,
            _automationConfigSvc,
            _ndsClusterSvc,
            _awsApiSvc,
            _groupDao,
            _orphanedItemDao,
            _backupSnapshotDao,
            ResourceOperationTagType.NONE);

    try {
      waitForStepPerformDone(createEbsSnapshotStep);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForStepRollbackDone(createEbsSnapshotStep));
      throw t;
    }

    final String ebsSnapshotId = createEbsSnapshotStep.getEbsSnapshotId().get();
    final ObjectId backupSnapshotId = new ObjectId();
    final BackupSnapshotEncryptionCredentials pEncryptionCredentials =
        new AWSBackupSnapshotEncryptionCredentials(null, null, null, null, null, false);

    final ObjectId pProjectId = new ObjectId();
    final ObjectId pClusterUniqueId = new ObjectId();
    final Date pScheduledCreationDate = new Date();
    final Date pSnapshotInitiationDate = new Date();
    final Date pScheduledDeletionDate = new Date(System.currentTimeMillis() + 3600 * 1000);
    final Date completionDate = new Date();
    _backupSnapshotDao.addBackupSnapshot(
        new SnapshotUpdate()
            .setId(backupSnapshotId)
            .setProjectId(pProjectId)
            .setClusterName("some cluster")
            .setDeploymentClusterName("some cluster")
            .setRsId("rsId")
            .setClusterUniqueId(pClusterUniqueId)
            .setScheduledCreationDate(pScheduledCreationDate)
            .setSnapshotInitiationDate(pSnapshotInitiationDate)
            .setScheduledDeletionDate(pScheduledDeletionDate)
            .setMongoDbVersion(NDSModelTestFactory.TEST_MONGODB_VERSION)
            .setUsedDiskSpace(20L)
            .setCloudProviders(Arrays.asList(CloudProvider.AWS))
            .setAwsSnapshotField(
                new AwsSnapshotFieldBuilder()
                    .withEbsSnapshotId(ebsSnapshotId)
                    .withEbsSnapshotDescription(
                        AWSBackupSnapshot.getDescriptionFromEbsVolumeId(ebsVolumeId))
                    .withAWSAccountId(getContainer().getAWSAccountId())
                    .withAWSContainerId(getContainer().getId())
                    .withAWSSubnetId(subnetId)
                    .withEbsVolumeId(ebsVolumeId)
                    .withEbsVolumeType(result.getData().getEBSVolumeType())
                    .withIsEbsVolumeEncrypted(result.getData().getEBSVolumeEncrypted())
                    .withEbsVolumeSize(result.getData().getSizeGB())
                    .withEbsDiskIOPS(result.getData().getIOPS())
                    .withRegionName(getContainer().getRegion().getName()))
            .setEncryptionDetails(pEncryptionCredentials)
            .setStatus(Status.COMPLETED)
            .setType(Type.SCHEDULED)
            .setFrequencyType(BackupFrequencyType.DAILY)
            .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
            .setSnapshotCompletionDate(completionDate));

    final AWSBackupSnapshot backupSnapshot =
        (AWSBackupSnapshot) _backupSnapshotDao.findById(backupSnapshotId).get();

    final AWSCreateVolumeFromSnapshotStep createVolumeFromSnapshotStep =
        new AWSCreateVolumeFromSnapshotStep(
            planContext,
            new Step.State(planId, moveId, 2, planDao),
            getContainer(),
            instanceHardware,
            getClusterDescription(),
            backupSnapshot.getAwsSubnetId(),
            backupSnapshot.getEbsSnapshotId(),
            backupSnapshot.getEbsVolumeEncrypted(),
            backupSnapshot.getEbsVolumeSize(),
            300,
            0,
            VolumeType.Io1,
            _awsApiSvc,
            _orphanedItemDao,
            _groupDao,
            OrphanStrategy.UseClusterCreationDate,
            ResourceOperationTagType.NONE,
            false);

    try {
      testAWSCreateVolumeFromSnapshotStepInternal(createVolumeFromSnapshotStep);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForStepRollbackDone(createVolumeFromSnapshotStep));
      throw t;
    }
  }
}
