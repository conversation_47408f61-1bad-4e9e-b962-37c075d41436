package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.ResyncBaseMove;
import com.xgen.svc.nds.planner.ResyncMove;
import java.time.Duration;
import java.util.Optional;
import org.bson.types.ObjectId;

public abstract class BaseResyncIntTest extends AWSExternalIntTest {
  protected abstract ResyncBaseMove getResyncMove(
      final PlanContext pPlanContext, final String pClusterName, final ObjectId pInstanceId);

  protected void testResyncMoveInternal() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            true,
            _testInjector);
    final ResyncBaseMove resyncMove =
        getResyncMove(
            plan.getPlanContext(), getClusterDescription().getName(), getInstanceIds().get(0));

    getHardwareDao()
        .scheduleDiskCompactionResyncOfReplicaSet(
            getReplicaSetHardwareForInstance(
                    getClusterDescription().getName(), getInstanceIds().get(0))
                .getId());

    final InstanceHardware oldHardware =
        getInstanceHardware(
            getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));
    assertNotNull(oldHardware.getNeedsDiskCompactionResyncDate());

    if (resyncMove instanceof ResyncMove) {
      assertEquals(oldHardware.getAction(), resyncMove.getMoveTriggeringAction());
    }

    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    plan.addMove(resyncMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      provisionMachineInternal(provisionMachineMove);
    } catch (final Throwable t) {
      // Best attempt to cleanup
      performUnconditionally(() -> waitForMoveRollbackDone(provisionMachineMove));
      throw t;
    }

    try {
      assertTrue(resyncMove.perform().getStatus().isDone());
      assertTrue(resyncMove.rollback().getStatus().isDone());

      final InstanceHardware updatedHardware =
          getInstanceHardware(
              getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));
      assertEquals(Optional.empty(), updatedHardware.getNeedsDiskCompactionResyncDate());
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForMoveRollbackDone(resyncMove));

      final ReplicaSetHardware replicaSetHardware =
          getReplicaSetHardwareForInstance(
              getClusterDescription().getName(), getInstanceIds().get(0));
      final AWSInstanceHardware instanceHardware =
          (AWSInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).get();

      cleanupEC2Instance(instanceHardware.getEC2InstanceId().get());
      cleanupEBSVolume(instanceHardware.getNVMePrioritizedEBSId().get());
      cleanupElasticIp(instanceHardware.getEIPId().get());

      throw t;
    } finally {
      waitForMoveRollbackDone(provisionMachineMove);
      waitForMoveRollbackDone(provisionContainerMove);
    }
  }

  private void provisionMachineInternal(final Move pProvisionMachineMove)
      throws InterruptedException {
    Result<Result.NoData> result = pProvisionMachineMove.perform();
    while (!result.getStatus().isDone()) {
      assertFalse(result.getStatus().isFailed());
      Thread.sleep(Duration.ofSeconds(5).toMillis());
      result = pProvisionMachineMove.perform();
    }
  }
}
