package com.xgen.svc.nds.aws.svc;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.set;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.Job.Status;
import com.xgen.cloud.common.jobqueue._public.model.JobHandler;
import com.xgen.cloud.common.util._public.util.SysProp;
import com.xgen.cloud.email._public.model.EmailMsg;
import com.xgen.cloud.nds.aws._private.dao.AWSCloudProviderContainerDao;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSLeakedItem;
import com.xgen.cloud.nds.aws._public.model.AWSLeakedItem.Type;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem;
import com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem.Builder;
import com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem.Expirations;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.LeakedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.LeakedItem.FieldDefs;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanedItem;
import com.xgen.cloud.nds.leakeditem._private.dao.LeakedItemDao;
import com.xgen.cloud.nds.leakeditem._public.model.LeakedItemCleanupState;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.svc.NDSOrphanedItemSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AWSCloudProviderItemProcessorSvcIntTests extends AWSExternalIntTest {

  @Inject private AWSCloudProviderItemProcessorSvc _svc;

  @Inject private AWSCloudProviderContainerDao _awsCloudProviderContainerDao;

  @Inject private AWSInstanceHardwareDao _instanceHardwareDao;

  @Inject private LeakedItemDao _leakedItemDao;

  @Inject private NDSOrphanedItemDao _orphanedItemDao;

  @Inject private NDSOrphanedItemSvc _orphanedItemSvc;

  @Inject ClusterDescriptionDao _clusterDescriptionDao;

  private AWSOrphanedItem _ec2Instance;
  private AWSOrphanedItem _vpc;
  private AWSOrphanedItem _ebsVolume;

  private AWSCloudProviderContainer _containerWithMetadata;

  private static final Logger LOG =
      LoggerFactory.getLogger(AWSCloudProviderItemProcessorSvcIntTests.class);

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();

    SysProp.set(SysProp.Property.JOB_PROCESSOR_ENABLED, "true");

    getAppSettings()
        .setProp(
            Fields.NDS_LEAKED_LEAKED_ITEMS_PROCESSOR_MINIMUM_ITEM_LIFE_SECONDS.value,
            "0",
            AppSettings.SettingType.MEMORY);
    getAppSettings()
        .setProp(
            Fields.EMAIL_DAO_CLASS.value,
            "com.xgen.cloud.email._private.dao.AwsEmailDao",
            SettingType.MEMORY);
    getAppSettings()
        .setProp(Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value, "PAUSED", SettingType.MEMORY);
    getAppSettings()
        .setProp(Fields.NDS_AWS_LEAKED_ITEM_DETECTION_MODE.value, "PAUSED", SettingType.MEMORY);
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    if (_ec2Instance != null) {
      _orphanedItemDao.add(_ec2Instance);
    }
    if (_vpc != null) {
      _orphanedItemDao.add(_vpc);
    }
    if (_ebsVolume != null) {
      _orphanedItemDao.add(_ebsVolume);
    }

    // manually add orphaned items for igw and subnets so that they will be cleared in tearDown
    // process. Can be deleted after CLOUDP-67536 is done
    if (_containerWithMetadata != null) {
      Arrays.stream(_containerWithMetadata.getSubnets())
          .forEach(
              s ->
                  _orphanedItemDao.add(
                      new AWSOrphanedItem.Builder()
                          .setAWSAccountId(getContainer().getAWSAccountId())
                          .setDurationToKeep(Duration.ofMinutes(0))
                          .setDurationUntilExpire(Expirations.SUBNET)
                          .setId(s.getSubnetId())
                          .setType(AWSOrphanedItem.Type.SUBNET)
                          .setRegionName(getContainer().getRegion())
                          .build()));
      _containerWithMetadata
          .getIgwId()
          .ifPresent(
              id ->
                  _orphanedItemDao.add(
                      new Builder()
                          .setAWSAccountId(getContainer().getAWSAccountId())
                          .setAWSVpcId(_vpc.getId())
                          .setDurationToKeep(Duration.ofMinutes(0))
                          .setDurationUntilExpire(Expirations.INTERNET_GATEWAY)
                          .setId(_containerWithMetadata.getIgwId().get())
                          .setType(AWSOrphanedItem.Type.INTERNET_GATEWAY)
                          .setRegionName(getContainer().getRegion())
                          .build()));
    }
    super.tearDown();
  }

  @Test(timeout = 150 * 60 * 1000L)
  public void testFullLifeCycleWithCron() throws InterruptedException {
    getAppSettings().setProp(Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value, "CRON", SettingType.MEMORY);
    getAppSettings()
        .setProp(Fields.NDS_AWS_LEAKED_ITEM_DETECTION_MODE.value, "CRON", SettingType.MEMORY);

    final Plan plan = setupPlanWithContainerAndSingleInstance(AWSNDSInstanceSize.M10);

    try {
      waitForPlanPerformSuccess(plan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(plan);
      // Test cannot run because plan not performed successfully
      fail();
    }

    final NDSGroup group = getNDSGroup();
    final ClusterDescription cluster = getClusterDescription();
    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            getInstanceHardware(group.getGroupId(), cluster.getName(), getInstanceIds().get(0));
    final ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(cluster.getName(), instanceHardware.getInstanceId());
    final AWSCloudProviderContainer container = getContainer();

    // remember container with metadata so that we can clean up igw and subnets in tearDown.
    _containerWithMetadata = container;

    _ec2Instance =
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationToKeep(Duration.ofMinutes(1))
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.EC2_INSTANCE)
            .setId(instanceHardware.getEC2InstanceId().get())
            .setType(AWSOrphanedItem.Type.EC2_INSTANCE)
            .setRegionName(container.getRegion())
            .build();

    _vpc =
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationToKeep(Duration.ofMinutes(1))
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.VPC)
            .setId(container.getVpcId().get())
            .setType(AWSOrphanedItem.Type.VPC)
            .setRegionName(container.getRegion())
            .build();

    _ebsVolume =
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationToKeep(Duration.ofMinutes(1))
            .setDurationUntilExpire(Expirations.EBS_VOLUME)
            .setId(instanceHardware.getNVMePrioritizedEBSId().get())
            .setType(AWSOrphanedItem.Type.EBS_VOLUME)
            .setRegionName(container.getRegion())
            .build();

    final String rootVolumeId =
        getAWSApiSvc()
            .findInstanceRootVolume(
                container.getAWSAccountId(),
                container.getRegion(),
                getLogger(),
                _ec2Instance.getId())
            .getVolumeId();

    final Set<String> createdIds = Set.of(_ec2Instance.getId(), _vpc.getId(), _ebsVolume.getId());

    _svc.findNewLeakedItems();

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Since metadata still exists for the created resources, they should not be considered leaked
      assertTrue(leakedItems.isEmpty());
    }

    _orphanedItemDao.add(_ec2Instance);
    _orphanedItemDao.add(_vpc);
    _orphanedItemDao.add(_ebsVolume);

    // Remove metadata for vpc and instance
    _instanceHardwareDao.unsetProvisionedFields(
        replicaSetHardware.getId(), instanceHardware.getInstanceId(), false, CloudProvider.AWS);

    _awsCloudProviderContainerDao.unsetProvisionedFields(group.getGroupId(), container.getId());

    _svc.findNewLeakedItems();

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exists but items are in the orphan queue and have not expired
      assertTrue(leakedItems.isEmpty());
    }

    // Items expired in the orphan queue
    _orphanedItemDao.expireItem(_vpc.getId(), CloudProvider.AWS, AWSOrphanedItem.Type.VPC);
    _orphanedItemDao.expireItem(
        _ec2Instance.getId(), CloudProvider.AWS, AWSOrphanedItem.Type.EC2_INSTANCE);
    _orphanedItemDao.expireItem(
        _ebsVolume.getId(), CloudProvider.AWS, AWSOrphanedItem.Type.EBS_VOLUME);

    _svc.findNewLeakedItems();

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exist and the items in the orphan queue have expired, but the cluster
      // is not idle, so cluster level resources will be marked as in use
      assertEquals(1, leakedItems.size());
      final Set<AWSLeakedItem.Type> clusterLevelResources =
          Set.of(Type.EC2_INSTANCE, Type.EBS_VOLUME);
      leakedItems.forEach(item -> assertTrue(!clusterLevelResources.contains(item.getType())));
    }

    _clusterDescriptionDao.setState(group.getGroupId(), cluster.getName(), State.IDLE);

    _svc.findNewLeakedItems();

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exists, items in the orphan queue are expired, and the cluster is idle.
      assertEquals(3, leakedItems.size());
      assertEquals(
          createdIds, leakedItems.stream().map(i -> i.getId()).collect(Collectors.toSet()));
      // Make sure the root volume is not marked as a leaked item
      assertTrue(
          _leakedItemDao
              .findByCloudProviderAndId(CloudProvider.AWS, rootVolumeId, Type.EBS_VOLUME)
              .isEmpty());
    }

    _svc.deleteApprovedItems();

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Items would still exist because they have not been marked for deletion
      assertEquals(3, leakedItems.size());
      assertEquals(
          createdIds, leakedItems.stream().map(i -> i.getId()).collect(Collectors.toSet()));
      assertTrue(leakedItems.stream().noneMatch(i -> i.getDeletedDate().isPresent()));
    }

    _leakedItemDao.bulkApproveForDeletion(
        List.of(_ec2Instance.getId()), CloudProvider.AWS, Type.EC2_INSTANCE);
    _leakedItemDao.bulkApproveForDeletion(List.of(_vpc.getId()), CloudProvider.AWS, Type.VPC);
    _leakedItemDao.bulkApproveForDeletion(
        List.of(_ebsVolume.getId()), CloudProvider.AWS, Type.EBS_VOLUME);

    // Instance is deleted first
    _svc.deleteApprovedItems();
    verifyEC2InstanceDeleted(_ec2Instance.getId());
    final LeakedItem leakedInstance =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ec2Instance.getId(), Type.EC2_INSTANCE)
            .get();
    assertTrue(leakedInstance.getDeletedDate().isPresent());

    // Volume is deleted second
    _svc.deleteApprovedItems();
    verifyEBSVolumeDeleted(_ebsVolume.getId());
    final LeakedItem leakedVolume =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ebsVolume.getId(), Type.EBS_VOLUME)
            .get();
    assertTrue(leakedVolume.getDeletedDate().isPresent());

    final List<String> subnetIds =
        Arrays.stream(container.getSubnets())
            .map(AWSSubnet::getSubnetId)
            .collect(Collectors.toList());

    _orphanedItemDao.add(
        new Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(Expirations.ELASTIC_IP)
            .setId(instanceHardware.getEIPId().get())
            .setType(AWSOrphanedItem.Type.ELASTIC_IP)
            .setRegionName(container.getRegion())
            .build());

    // Remove the vpc from the orphan queue to make sure that it is the service we are testing that
    // is performing the deletion request
    _orphanedItemDao.remove(new BasicDBObject("id", _vpc.getId()));
    while (_orphanedItemDao.findAll().size() > 0) {
      // Delete all other dependent resources before VPC can be deleted
      _orphanedItemSvc.cleanItems();
      Thread.sleep(Duration.ofSeconds(10).toMillis());
    }

    _svc.deleteApprovedItems();
    verifySubnetsDeleted(subnetIds);
    verifyInternetGatewayDeleted(container.getIgwId().get());
    verifyVpcDeleted(_vpc.getId());
    final LeakedItem leakedVpc =
        _leakedItemDao.findByCloudProviderAndId(CloudProvider.AWS, _vpc.getId(), Type.VPC).get();
    assertTrue(leakedVpc.getDeletedDate().isPresent());

    // Unset all the dates on the leaked items to test that they get removed on pre-processing
    _leakedItemDao.unsetDeleteDataForTest(_vpc.getId(), CloudProvider.AWS, Type.VPC);
    _leakedItemDao.unsetDeleteDataForTest(_ebsVolume.getId(), CloudProvider.AWS, Type.EBS_VOLUME);
    _leakedItemDao.unsetDeleteDataForTest(
        _ec2Instance.getId(), CloudProvider.AWS, Type.EC2_INSTANCE);

    _svc.removeItemsIfNotNeeded();
    assertTrue(
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _vpc.getId(), Type.VPC)
            .isEmpty());
    assertTrue(
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ec2Instance.getId(), Type.EC2_INSTANCE)
            .isEmpty());
    assertTrue(
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ebsVolume.getId(), Type.EBS_VOLUME)
            .isEmpty());
  }

  @Test
  public void testCorrectNumberOfCleanupJobsCreated() {
    getAppSettings()
        .setProp(Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value, "JOB_HANDLER", SettingType.MEMORY);
    _svc.runCleanupOnly();

    final List<Job> jobsCreated =
        _svc.getJobsProcessorSvc().findAllJobsForHandler(AWSLeakedItemCleanupJobHandler.class);
    assertEquals(1, jobsCreated.size());

    final Job job = jobsCreated.get(0);

    final BasicDBObject params = job.getParameters();
    final ObjectId id = params.getObjectId(LeakedItemCleanupState.FieldDefs.ID);
    final LeakedItemCleanupState state = _svc.getLeakedItemCleanupStateSvc().findById(id);

    assertEquals(CloudProvider.AWS, state.getCloudProvider());
  }

  @Test
  public void testCorrectNumberOfDetectionJobsCreated() {
    getAppSettings()
        .setProp(
            Fields.NDS_AWS_LEAKED_ITEM_DETECTION_MODE.value, "JOB_HANDLER", SettingType.MEMORY);
    _svc.runDetection();

    final List<Job> jobsCreated =
        _svc.getJobsProcessorSvc().findAllJobsForHandler(AWSLeakedItemDetectionJobHandler.class);
    assertEquals(1, jobsCreated.size());
  }

  private void waitForJobToComplete(Class<? extends JobHandler> pJobHandlerClass)
      throws InterruptedException {
    List<Job> jobs;
    do {
      jobs = _svc.getJobsProcessorSvc().findAllJobsForHandler(pJobHandlerClass);

      // if any jobs have failed, test has failed
      if (jobs.stream()
          .anyMatch(
              j ->
                  j.getStatus().equals(Status.FAILED)
                      || j.getStatus().equals(Status.RETRY_EXHAUSTED_FAIL)
                      || j.getStatus().equals(Status.FINALIZED_EXHAUSTED_FAIL))) {
        fail();
      }

      LogFirstIncompletedJobs(jobs);

      // brief sleep to avoid busy waiting
      Thread.sleep(1000);

      // if there are any incomplete jobs, continue
    } while (jobs.stream().anyMatch(j -> !j.getStatus().equals(Status.COMPLETED)));
  }

  private void LogFirstIncompletedJobs(final List<Job> pJobs) {
    final Optional<Job> inCompleteJob =
        pJobs.stream().filter(j -> !j.getStatus().equals(Status.COMPLETED)).findFirst();

    inCompleteJob.ifPresent(
        pJob -> LOG.info("Job for worker {} is in {} status.", pJob.getWorker(), pJob.getStatus()));
  }

  @Test(timeout = 150 * 60 * 1000L)
  public void testFullLifeCycleWithJobHandler() throws InterruptedException {

    final Plan plan = setupPlanWithContainerAndSingleInstance(AWSNDSInstanceSize.M10);

    try {
      waitForPlanPerformSuccess(plan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(plan);
      // Test cannot run because plan not performed successfully
      fail();
    }

    final NDSGroup group = getNDSGroup();
    final ClusterDescription cluster = getClusterDescription();
    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            getInstanceHardware(group.getGroupId(), cluster.getName(), getInstanceIds().get(0));
    final ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(cluster.getName(), instanceHardware.getInstanceId());
    final AWSCloudProviderContainer container = getContainer();

    // remember container with metadata so that we can clean up igw and subnets in tearDown.
    _containerWithMetadata = container;

    _ec2Instance =
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationToKeep(Duration.ofMinutes(1))
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.EC2_INSTANCE)
            .setId(instanceHardware.getEC2InstanceId().get())
            .setType(AWSOrphanedItem.Type.EC2_INSTANCE)
            .setRegionName(container.getRegion())
            .build();

    _vpc =
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationToKeep(Duration.ofMinutes(1))
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.VPC)
            .setId(container.getVpcId().get())
            .setType(AWSOrphanedItem.Type.VPC)
            .setRegionName(container.getRegion())
            .build();

    _ebsVolume =
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationToKeep(Duration.ofMinutes(1))
            .setDurationUntilExpire(Expirations.EBS_VOLUME)
            .setId(instanceHardware.getNVMePrioritizedEBSId().get())
            .setType(AWSOrphanedItem.Type.EBS_VOLUME)
            .setRegionName(container.getRegion())
            .build();

    final String rootVolumeId =
        getAWSApiSvc()
            .findInstanceRootVolume(
                container.getAWSAccountId(),
                container.getRegion(),
                getLogger(),
                _ec2Instance.getId())
            .getVolumeId();

    final Set<String> createdIds = Set.of(_ec2Instance.getId(), _vpc.getId(), _ebsVolume.getId());

    getAppSettings()
        .setProp(
            Fields.NDS_AWS_LEAKED_ITEM_DETECTION_MODE.value, "JOB_HANDLER", SettingType.MEMORY);
    _svc.runCleanupOnly();
    _svc.runDetection();
    waitForJobToComplete(AWSLeakedItemDetectionJobHandler.class);

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Since metadata still exists for the created resources, they should not be considered leaked
      assertTrue(leakedItems.isEmpty());
    }

    _orphanedItemDao.add(_ec2Instance);
    _orphanedItemDao.add(_vpc);
    _orphanedItemDao.add(_ebsVolume);

    // Remove metadata for vpc and instance
    _instanceHardwareDao.unsetProvisionedFields(
        replicaSetHardware.getId(), instanceHardware.getInstanceId(), false, CloudProvider.AWS);
    _awsCloudProviderContainerDao.unsetProvisionedFields(group.getGroupId(), container.getId());

    _svc.runDetection();
    waitForJobToComplete(AWSLeakedItemDetectionJobHandler.class);

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exist but items are in the orphan queue and have not expired
      assertTrue(leakedItems.isEmpty());
    }

    // Items expired in the orphan queue
    _orphanedItemDao.expireItem(_vpc.getId(), CloudProvider.AWS, AWSOrphanedItem.Type.VPC);
    _orphanedItemDao.expireItem(
        _ec2Instance.getId(), CloudProvider.AWS, AWSOrphanedItem.Type.EC2_INSTANCE);
    _orphanedItemDao.expireItem(
        _ebsVolume.getId(), CloudProvider.AWS, AWSOrphanedItem.Type.EBS_VOLUME);

    _svc.runDetection();
    waitForJobToComplete(AWSLeakedItemDetectionJobHandler.class);

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exist and the items in the orphan queue have expired, but the cluster
      // is not idle, so cluster level resources will be marked as in use
      assertEquals(1, leakedItems.size());
      final Set<AWSLeakedItem.Type> clusterLevelResources =
          Set.of(Type.EC2_INSTANCE, Type.EBS_VOLUME);
      leakedItems.forEach(item -> assertTrue(!clusterLevelResources.contains(item.getType())));
    }

    _clusterDescriptionDao.setState(group.getGroupId(), cluster.getName(), State.IDLE);

    _svc.runDetection();
    waitForJobToComplete(AWSLeakedItemDetectionJobHandler.class);

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Metadata does not exists, items in the orphan queue are expired, and the cluster is idle.
      assertEquals(3, leakedItems.size());
      assertEquals(
          createdIds, leakedItems.stream().map(i -> i.getId()).collect(Collectors.toSet()));
      // Make sure the root volume is not marked as a leaked item
      assertTrue(
          _leakedItemDao
              .findByCloudProviderAndId(CloudProvider.AWS, rootVolumeId, Type.EBS_VOLUME)
              .isEmpty());

      // Update the discover date so the items will be detectable by findUnacknowledgedItems()
      leakedItems.forEach(
          item ->
              _leakedItemDao.updateOneMajority(
                  eq(OrphanedItem.FieldDefs.ID, item.getId()),
                  set(FieldDefs.DISCOVER_DATE, DateUtils.addDays(item.getDiscoverDate(), -1))));
    }

    getAppSettings()
        .setProp(Fields.NDS_AWS_LEAKED_ITEM_DETECTION_MODE.value, "PAUSED", SettingType.MEMORY);
    getAppSettings()
        .setProp(Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value, "JOB_HANDLER", SettingType.MEMORY);

    _svc.runCleanupOnly();
    waitForJobToComplete(AWSLeakedItemCleanupJobHandler.class);

    {
      final List<AWSLeakedItem> leakedItems =
          _leakedItemDao.findAll().stream()
              .filter(i -> i.getCloudProvider() == CloudProvider.AWS)
              .map(AWSLeakedItem.class::cast)
              .filter(item -> createdIds.contains(item.getId()))
              .collect(Collectors.toList());

      // Items would still exist because they have not been marked for deletion
      assertEquals(3, leakedItems.size());
      assertEquals(
          createdIds, leakedItems.stream().map(i -> i.getId()).collect(Collectors.toSet()));
      assertTrue(leakedItems.stream().noneMatch(i -> i.getDeletedDate().isPresent()));

      // Check that three emails were sent as all items are unacknowledged
      final List<EmailMsg> emails =
          _svc.getEmailSvc()
              .findEmailsByRecipient(getAppSettings().getAtlasAdminEmailAddress(), 0, 1);

      assertEquals(1, emails.size());
    }

    _leakedItemDao.bulkApproveForDeletion(
        List.of(_ec2Instance.getId()), CloudProvider.AWS, Type.EC2_INSTANCE);
    _leakedItemDao.bulkApproveForDeletion(List.of(_vpc.getId()), CloudProvider.AWS, Type.VPC);
    _leakedItemDao.bulkApproveForDeletion(
        List.of(_ebsVolume.getId()), CloudProvider.AWS, Type.EBS_VOLUME);

    // Instance is deleted first
    _svc.runCleanupOnly();
    waitForJobToComplete(AWSLeakedItemCleanupJobHandler.class);

    // Check that no new emails were sent as all items are now acknowledged (should still only be 3)
    final List<EmailMsg> emails =
        _svc.getEmailSvc()
            .findEmailsByRecipient(getAppSettings().getAtlasAdminEmailAddress(), 0, 1);

    assertEquals(1, emails.size());

    verifyEC2InstanceDeleted(_ec2Instance.getId());
    final LeakedItem leakedInstance =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ec2Instance.getId(), Type.EC2_INSTANCE)
            .get();
    assertTrue(leakedInstance.getDeletedDate().isPresent());

    // Volume is deleted second
    _svc.runCleanupOnly();
    waitForJobToComplete(AWSLeakedItemCleanupJobHandler.class);

    verifyEBSVolumeDeleted(_ebsVolume.getId());
    final LeakedItem leakedVolume =
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ebsVolume.getId(), Type.EBS_VOLUME)
            .get();
    assertTrue(leakedVolume.getDeletedDate().isPresent());

    final List<String> subnetIds =
        Arrays.stream(container.getSubnets())
            .map(AWSSubnet::getSubnetId)
            .collect(Collectors.toList());

    _orphanedItemDao.add(
        new Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(Expirations.ELASTIC_IP)
            .setId(instanceHardware.getEIPId().get())
            .setType(AWSOrphanedItem.Type.ELASTIC_IP)
            .setRegionName(container.getRegion())
            .build());

    // Remove the vpc from the orphan queue to make sure that it is the service we are testing
    // that is performing the deletion request
    _orphanedItemDao.remove(new BasicDBObject("id", _vpc.getId()));
    while (_orphanedItemDao.findAll().size() > 0) {
      // Delete all other dependent resources before VPC can be deleted
      _orphanedItemSvc.cleanItems();
      Thread.sleep(Duration.ofSeconds(10).toMillis());
    }

    _svc.runCleanupOnly();
    waitForJobToComplete(AWSLeakedItemCleanupJobHandler.class);

    verifySubnetsDeleted(subnetIds);
    verifyInternetGatewayDeleted(container.getIgwId().get());
    verifyVpcDeleted(_vpc.getId());
    final LeakedItem leakedVpc =
        _leakedItemDao.findByCloudProviderAndId(CloudProvider.AWS, _vpc.getId(), Type.VPC).get();
    assertTrue(leakedVpc.getDeletedDate().isPresent());

    // Unset all the dates on the leaked items to test that they get removed on pre-processing
    _leakedItemDao.unsetDeleteDataForTest(_vpc.getId(), CloudProvider.AWS, Type.VPC);
    _leakedItemDao.unsetDeleteDataForTest(_ebsVolume.getId(), CloudProvider.AWS, Type.EBS_VOLUME);
    _leakedItemDao.unsetDeleteDataForTest(
        _ec2Instance.getId(), CloudProvider.AWS, Type.EC2_INSTANCE);

    // Supposed to ensure that the Remove Unneeded Items stage is successful
    _svc.runCleanupOnly();
    waitForJobToComplete(AWSLeakedItemCleanupJobHandler.class);

    assertTrue(
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _vpc.getId(), Type.VPC)
            .isEmpty());
    assertTrue(
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ec2Instance.getId(), Type.EC2_INSTANCE)
            .isEmpty());
    assertTrue(
        _leakedItemDao
            .findByCloudProviderAndId(CloudProvider.AWS, _ebsVolume.getId(), Type.EBS_VOLUME)
            .isEmpty());
  }
}
