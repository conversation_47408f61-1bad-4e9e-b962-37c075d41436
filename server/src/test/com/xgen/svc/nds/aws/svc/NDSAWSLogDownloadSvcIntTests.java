package com.xgen.svc.nds.aws.svc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.core._private.dao.AutomationConfigDao;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.MongosqldConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.BiConnector.FieldDefs;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.mms.model.agent.constants.FTDCProcessType;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.aws.svc.NDSAWSLogDownloadSvc.SupportedLogs;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.io.IOUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class NDSAWSLogDownloadSvcIntTests extends AWSExternalIntTest {

  @Inject private NDSAWSLogDownloadSvc _svc;
  @Inject private AutomationConfigDao _automationConfigDao;

  private LocalDateTime _now;

  private String _hostname = "testHostname";

  // Note: This is explicitly duplicated from NDSAWSLogDownloadSvc for redundancy purposes.
  private Set<SupportedLogs> supportedLogsForValidation =
      Set.of(SupportedLogs.MONGODB_AUDIT_LOG, SupportedLogs.MONGODB);

  @Before
  public void seedLogChunks() throws Exception {
    final RegionName region = getClusterDescription().getHighestPriorityRegion();
    final AmazonS3 s3Client =
        _svc.getS3Client(NDSAWSLogDownloadSvc.getAWSRegionForInstanceRegion(region).getValue());
    final String bucket = _svc.getBucket(getClusterDescription().getHighestPriorityRegion());

    _now = LocalDateTime.now();

    for (final NDSAWSLogDownloadSvc.SupportedLogs logType :
        NDSAWSLogDownloadSvc.SupportedLogs.values()) {
      LocalDateTime timeStamp = _now.minus(Duration.ofMinutes(10));

      if (logType == SupportedLogs.FTDC) {
        for (final FTDCProcessType processType : FTDCProcessType.values()) {
          final List<LogStreamContainer> logChunkStreams = getLogChunkStreams(logType, true);
          for (final LogStreamContainer logChunkStream : logChunkStreams) {
            final PutObjectRequest putObjectRequest =
                new PutObjectRequest(
                    bucket,
                    String.format(
                        "%s/%s/%s/%s/%s/%d",
                        getNDSGroup().getGroupId(),
                        getClusterDescription().getDeploymentClusterName(),
                        _hostname,
                        logType.getType(),
                        processType.getValue(),
                        timeStamp.toEpochSecond(ZoneOffset.UTC)),
                    logChunkStream.stream,
                    logChunkStream.objectMetadata);
            s3Client.putObject(putObjectRequest);
            timeStamp = timeStamp.plus(Duration.ofMinutes(5));
          }
        }
        continue;
      }

      final List<LogStreamContainer> logChunkStreams = getLogChunkStreams(logType, true);
      for (final LogStreamContainer logChunkStream : logChunkStreams) {
        final PutObjectRequest putObjectRequest =
            new PutObjectRequest(
                bucket,
                String.format(
                    "%s/%s/%s/%s/%d",
                    getNDSGroup().getGroupId(),
                    getClusterDescription().getDeploymentClusterName(),
                    _hostname,
                    logType.getType(),
                    timeStamp.toEpochSecond(ZoneOffset.UTC)),
                logChunkStream.stream,
                logChunkStream.objectMetadata);
        s3Client.putObject(putObjectRequest);
        timeStamp = timeStamp.plus(Duration.ofMinutes(5));
      }
    }

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(
            getClusterDescription().getName(), getNDSGroup().getGroupId(), 0);
    getHardwareDao().addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final ObjectId instanceId =
        getHardwareDao()
            .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName())
            .get(0)
            .getHardware()
            .get(0)
            .getInstanceId();

    getHardwareDao()
        .setInstanceField(
            replicaSetHardwareId,
            instanceId,
            false,
            InstanceHardware.FieldDefs.HOSTNAMES,
            new Hostnames(_hostname).toDBList());

    getHardwareDao()
        .setInstanceField(
            replicaSetHardwareId,
            instanceId,
            false,
            InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
            InstanceHostname.HostnameScheme.LEGACY.name());

    getHardwareDao()
        .setInstanceField(
            replicaSetHardwareId,
            instanceId,
            false,
            "cloudProviderContainerId",
            getContainer().getId());

    getClusterDescriptionDao()
        .updateBiConnector(
            getNDSGroup().getGroupId(),
            getClusterDescription().getName(),
            new ClusterDescription.BiConnector(
                NDSModelTestFactory.getBiConnector(true)
                    .append(FieldDefs.HOSTNAMES, new Hostnames(_hostname).toDBList())));

    final String deploymentItem =
        _svc.getMongosqldConfigDeploymentItemForCluster(getNDSGroup(), getClusterDescription());

    final MongosqldConfig config = new MongosqldConfig();
    config.setId(1234567);
    config.setDeploymentItem(deploymentItem);
    config.setHostname(_hostname);

    final AutomationConfig publishedConfig =
        getAutomationConfigSvc().findPublished(getNDSGroup().getGroupId());

    publishedConfig.getDeployment().addMongosqld(config);
    _automationConfigDao.republish(getNDSGroup().getGroupId(), publishedConfig);
  }

  @After
  public void cleanupLogChunks() throws Exception {
    final RegionName region = getClusterDescription().getHighestPriorityRegion();
    final AmazonS3 s3Client =
        _svc.getS3Client(NDSAWSLogDownloadSvc.getAWSRegionForInstanceRegion(region).getValue());
    final NDSGroup groupId = getNDSGroup();
    final ClusterDescription clusterDescription = getClusterDescription();
    final String bucket = _svc.getBucket(getClusterDescription().getHighestPriorityRegion());
    final ListObjectsRequest listObjectsRequest =
        new ListObjectsRequest()
            .withBucketName(bucket)
            .withPrefix(
                String.format("%s/%s/", groupId, clusterDescription.getDeploymentClusterName()));
    final ObjectListing objectListing = s3Client.listObjects(listObjectsRequest);
    objectListing
        .getObjectSummaries()
        .forEach(
            os -> {
              s3Client.deleteObject(bucket, os.getKey());
            });
  }

  private List<LogStreamContainer> getLogChunkStreams(
      final NDSAWSLogDownloadSvc.SupportedLogs pLogType, final boolean addCorruptedChunk)
      throws IOException {
    final ArrayList<LogStreamContainer> logStreamContainers = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      final byte[] bytes =
          String.format("%s %s contents%s", pLogType, i, i < 4 ? ", " : "")
              .repeat(10)
              .getBytes(); // Repeat 10 times to yield logs > 20 bytes, based on CLOUDP-276130

      final byte[] zippedBytes = getZippedByteContents(bytes);

      final ObjectMetadata objectMetadata = new ObjectMetadata();
      objectMetadata.setContentLength(zippedBytes.length);

      logStreamContainers.add(
          new LogStreamContainer(new ByteArrayInputStream(zippedBytes), objectMetadata));
    }
    if (supportedLogsForValidation.contains(pLogType) && addCorruptedChunk) {
      final byte[] bytes = new byte[50];
      for (int i = 0; i < bytes.length; i++) {
        bytes[i] = (byte) i;
      }

      final ObjectMetadata objectMetadata = new ObjectMetadata();
      objectMetadata.setContentLength(bytes.length);
      logStreamContainers.add(
          new LogStreamContainer(new ByteArrayInputStream(bytes), objectMetadata));
    }
    return logStreamContainers;
  }

  private byte[] getZippedByteContents(final byte[] pBytes) throws IOException {
    final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    final GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream);
    gzipOutputStream.write(pBytes);
    gzipOutputStream.close();
    final byte[] zippedBytes = byteArrayOutputStream.toByteArray();
    byteArrayOutputStream.close();
    return zippedBytes;
  }

  private class LogStreamContainer {
    public final InputStream stream;
    public final ObjectMetadata objectMetadata;

    public LogStreamContainer(final InputStream pStream, final ObjectMetadata pObjectMetadata) {
      stream = pStream;
      objectMetadata = pObjectMetadata;
    }
  }

  @Test
  public void testGetLogsForDates() throws Exception {
    for (final NDSAWSLogDownloadSvc.SupportedLogs logType :
        NDSAWSLogDownloadSvc.SupportedLogs.values()) {
      // FTDC special cased in test below
      if (logType.equals(SupportedLogs.FTDC)) {
        continue;
      }

      // Expect there to be 4 total logs - 3 within the given time range, and 1 log that's added
      // after the time range
      final int expectedStartIdx = 1;
      final int expectedEndIdx = 5;

      final StreamingOutput logsForDates =
          _svc.getLogsForDates(
              _now.minus(Duration.ofMinutes(7)).toEpochSecond(ZoneOffset.UTC) * 1000,
              _now.plus(Duration.ofMinutes(7)).toEpochSecond(ZoneOffset.UTC) * 1000,
              getNDSGroup().getGroupId(),
              _hostname,
              logType);

      final ByteArrayOutputStream expected = new ByteArrayOutputStream();
      final ByteArrayOutputStream actual = new ByteArrayOutputStream();

      final List<LogStreamContainer> logChunkStreams = getLogChunkStreams(logType, false);
      for (int i = expectedStartIdx; i < expectedEndIdx; i++) {
        try (final InputStream stream = logChunkStreams.get(i).stream) {
          while (stream.available() > 0) {
            expected.write(stream.read());
          }
        }
      }

      logsForDates.write(actual);
      assertEquals(expected.toString(), actual.toString());
    }
  }

  // Test edge case from https://jira.mongodb.org/browse/CLOUDP-49339
  @Test
  public void testGetLogsForDatesLastLogMatchesEndDate() throws Exception {
    for (final NDSAWSLogDownloadSvc.SupportedLogs logType :
        NDSAWSLogDownloadSvc.SupportedLogs.values()) {
      // FTDC special cased in test below
      if (logType.equals(SupportedLogs.FTDC)) {
        continue;
      }

      final StreamingOutput logsForDates =
          _svc.getLogsForDates(
              _now.minus(Duration.ofMinutes(11)).toEpochSecond(ZoneOffset.UTC) * 1000,
              _now.plus(Duration.ofMinutes(10)).toEpochSecond(ZoneOffset.UTC) * 1000,
              getNDSGroup().getGroupId(),
              _hostname,
              logType);

      final ByteArrayOutputStream expected = new ByteArrayOutputStream();
      final ByteArrayOutputStream actual = new ByteArrayOutputStream();

      final List<LogStreamContainer> logChunkStreams = getLogChunkStreams(logType, false);
      for (int i = 0; i < 5; i++) {
        try (final InputStream stream = logChunkStreams.get(i).stream) {
          while (stream.available() > 0) {
            expected.write(stream.read());
          }
        }
      }
      logsForDates.write(actual);
      assertEquals(expected.toString(), actual.toString());
    }
  }

  @Test
  public void testGetLogsForDates_FTDC() throws Exception {
    // For FTDC, we get the first log before the window, and don't return the log after the
    // window. (normally this range would be idx 1-5). See getChunkSummariesForFTDC for more info
    final int expectedStartIdx = 0;
    final int expectedEndIdx = 4;

    // Obtain the streaming output which produces a tar.gz with nested directories.
    final StreamingOutput logsForDates =
        _svc.getLogsForDates(
            _now.minus(Duration.ofMinutes(7)).toEpochSecond(ZoneOffset.UTC) * 1000,
            _now.plus(Duration.ofMinutes(7)).toEpochSecond(ZoneOffset.UTC) * 1000,
            getNDSGroup().getGroupId(),
            _hostname,
            SupportedLogs.FTDC);

    // Build the expected content by decompressing each LogStreamContainer's gzipped content.
    final ByteArrayOutputStream expectedExtractedContents = new ByteArrayOutputStream();
    final List<LogStreamContainer> logChunkStreams = getLogChunkStreams(SupportedLogs.FTDC, false);
    for (int i = expectedStartIdx; i < expectedEndIdx; i++) {
      try (final InputStream gzippedStream = logChunkStreams.get(i).stream;
          final GZIPInputStream fileGzipIn = new GZIPInputStream(gzippedStream)) {
        IOUtils.copy(fileGzipIn, expectedExtractedContents);
      }
    }

    final ByteArrayOutputStream actualExtractedContents = writeZipOfDirToOutput(logsForDates);
    assertEquals(expectedExtractedContents.toString(), actualExtractedContents.toString());

    // Test edge case when log matches end date
    final StreamingOutput logsForDatesWhenLogMatchesEndDate =
        _svc.getLogsForDates(
            _now.minus(Duration.ofMinutes(11)).toEpochSecond(ZoneOffset.UTC) * 1000,
            _now.plus(Duration.ofMinutes(10)).toEpochSecond(ZoneOffset.UTC) * 1000,
            getNDSGroup().getGroupId(),
            _hostname,
            SupportedLogs.FTDC);

    final ByteArrayOutputStream expectedWhenLogMatchesEndDate = new ByteArrayOutputStream();

    final List<LogStreamContainer> logChunkStreams2 = getLogChunkStreams(SupportedLogs.FTDC, false);
    for (int i = 0; i < 5; i++) {
      try (final InputStream gzippedStream = logChunkStreams2.get(i).stream;
          final GZIPInputStream fileGzipIn = new GZIPInputStream(gzippedStream)) {
        IOUtils.copy(fileGzipIn, expectedWhenLogMatchesEndDate);
      }
    }
    final ByteArrayOutputStream actualWhenLogMatchesEndDate =
        writeZipOfDirToOutput(logsForDatesWhenLogMatchesEndDate);
    assertEquals(expectedWhenLogMatchesEndDate.toString(), actualWhenLogMatchesEndDate.toString());
  }

  @Test
  public void testGetLogsForDates_ValidateS3Chunks() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        getGroup(), null, FeatureFlag.VALIDATE_S3_CHUNKS_ON_DOWNLOAD);

    Stream.of(SupportedLogs.MONGODB, SupportedLogs.MONGODB_AUDIT_LOG)
        .forEach(
            logType -> {
              try {
                final StreamingOutput logsForDates =
                    _svc.getLogsForDates(
                        _now.minus(Duration.ofMinutes(11)).toEpochSecond(ZoneOffset.UTC) * 1000,
                        _now.plus(Duration.ofMinutes(17)).toEpochSecond(ZoneOffset.UTC) * 1000,
                        getNDSGroup().getGroupId(),
                        _hostname,
                        logType);

                final ByteArrayOutputStream expected = new ByteArrayOutputStream();
                final ByteArrayOutputStream actual = new ByteArrayOutputStream();

                final List<LogStreamContainer> logChunkStreams = getLogChunkStreams(logType, false);
                for (int i = 0; i < 5; i++) {
                  try (final InputStream stream = logChunkStreams.get(i).stream) {
                    while (stream.available() > 0) {
                      expected.write(stream.read());
                    }
                  }
                }

                logsForDates.write(actual);
                assertEquals(expected.toString(), actual.toString());
              } catch (final Exception pE) {
                fail(pE.getMessage());
              }
            });
  }

  private ByteArrayOutputStream writeZipOfDirToOutput(final StreamingOutput actualOutput)
      throws Exception {
    // Write logsForDates to a ByteArrayOutputStream.
    final ByteArrayOutputStream actualOutputStream = new ByteArrayOutputStream();
    actualOutput.write(actualOutputStream);
    actualOutputStream.flush(); // Ensure all data is flushed.
    final byte[] tarGzBytes = actualOutputStream.toByteArray();

    // Extract the tar.gz archive.
    final ByteArrayOutputStream actualExtractedContents = new ByteArrayOutputStream();
    try (final GZIPInputStream gzipIn = new GZIPInputStream(new ByteArrayInputStream(tarGzBytes));
        final TarArchiveInputStream tarIn = new TarArchiveInputStream(gzipIn)) {

      TarArchiveEntry entry;
      while ((entry = tarIn.getNextEntry()) != null) {
        if (entry.isDirectory()) {
          continue;
        }
        // Process only file entries that are individually gzipped.
        if (!entry.getName().endsWith(".gz")) {
          continue;
        }
        // Read the content of this tar entry.
        final ByteArrayOutputStream entryOutputStream = new ByteArrayOutputStream();
        IOUtils.copy(tarIn, entryOutputStream);
        final byte[] gzippedFileBytes = entryOutputStream.toByteArray();

        // Decompress the gzipped file.
        try (final GZIPInputStream fileGzipIn =
                new GZIPInputStream(new ByteArrayInputStream(gzippedFileBytes));
            final ByteArrayOutputStream decompressedEntry = new ByteArrayOutputStream()) {
          IOUtils.copy(fileGzipIn, decompressedEntry);
          actualExtractedContents.write(decompressedEntry.toByteArray());
        }
      }
    }

    return actualExtractedContents;
  }
}
