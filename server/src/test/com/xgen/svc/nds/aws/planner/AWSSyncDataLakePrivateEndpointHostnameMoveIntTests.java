package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;

import com.amazonaws.services.route53.model.RRType;
import com.xgen.cloud.nds.aws._public.model.AWSNDSSettings;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeState;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeDataProcessRegion;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkEndpointIdEntry;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkEndpointIdEntry.Type;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkSettings;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AWSSyncDataLakePrivateEndpointHostnameMoveIntTests extends AWSExternalIntTest {

  private static String ADF_HOSTNAME_FORMAT = "adf-%s";
  @Inject NDSDataLakeTenantDao _ndsDataLakeTenantDao;

  private NDSDataLakeTenant buildSimpleTenant(
      final ObjectId pTenantId,
      final ObjectId pGroupId,
      final String pTenantName,
      final Map<String, String> pHostnameDNSMap,
      final String pRegion) {
    return new NDSDataLakeTenant(
        new NDSDataLakeTenantId(pGroupId, pTenantName),
        pTenantId,
        Date.from(Instant.EPOCH),
        Date.from(Instant.EPOCH),
        NDSDataLakeState.UNVERIFIED,
        new NDSDataLakeDataProcessRegion(CloudProvider.AWS, pRegion),
        false,
        false,
        0,
        null,
        false,
        null,
        List.of("foo-aa001.virginia-usa.a.query.mongodb.net"),
        "foo-aa001.a.query.mongodb.net",
        Date.from(Instant.EPOCH),
        pHostnameDNSMap);
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testAWSSyncDataLakePrivateEndpointHostnameMoveIntTests() throws Exception {
    final NDSGroup ndsGroup = getNDSGroup();

    final String endpoint1 = "vpce-00000000000000001";
    final String dns1 =
        "vpce-0cf3679edfa5e08b1-s8a8orgw.vpce-svc-0a7247db33497082e.us-east-1.vpce.amazonaws.com";
    final String endpoint2 = "vpce-00000000000000002";
    final String dns2 =
        "vpce-020e0d69aed9c906e-xbwpiiso.vpce-svc-0a0587f774cf7983a.us-east-1.vpce.amazonaws.com";

    // only endpoint
    final Set<NDSPrivateNetworkEndpointIdEntry> entries =
        Set.of(
            new NDSPrivateNetworkEndpointIdEntry.Builder()
                .endpointId(endpoint1)
                .type(Type.DATA_LAKE)
                .provider(CloudProvider.AWS)
                .comment("comment1")
                .customerEndpointDNSName(dns1)
                .region(AWSRegionName.US_EAST_1.getName())
                .build(),
            new NDSPrivateNetworkEndpointIdEntry.Builder()
                .endpointId(endpoint2)
                .type(Type.DATA_LAKE)
                .provider(CloudProvider.AWS)
                .comment("comment2")
                .customerEndpointDNSName(dns2)
                .region(AWSRegionName.US_WEST_1.getName())
                .build());

    final NDSPrivateNetworkSettings networkSettings =
        new NDSPrivateNetworkSettings(entries, new Date());

    getNDSGroupDao()
        .updatePrivateNetworkSettings(
            ndsGroup.getGroupId(),
            networkSettings,
            ndsGroup.getPrivateNetworkSettings().getLastUpdated());
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final String tenant1Name = "tenant1";
    final ObjectId tenant1Id = ObjectId.get();
    // this endpoint will be deleted.
    final String endpointOnlyOnTenant1 = "vpce-00000000000000007";
    final Map<String, String> tenant1PrivateEndpointHostnameMap = new HashMap<>();
    tenant1PrivateEndpointHostnameMap.put(
        endpoint1,
        AWSSyncDataLakePrivateEndpointHostnameMove.getHostname(
            String.format(ADF_HOSTNAME_FORMAT, tenant1Id),
            "abc123",
            endpoint1,
            NDSSettings.getCrossCloudInstanceDomainName(plan.getPlanContext().getAppSettings())));
    tenant1PrivateEndpointHostnameMap.put(
        endpointOnlyOnTenant1,
        AWSSyncDataLakePrivateEndpointHostnameMove.getHostname(
            String.format(ADF_HOSTNAME_FORMAT, tenant1Id),
            "abc123",
            endpointOnlyOnTenant1,
            NDSSettings.getCrossCloudInstanceDomainName(plan.getPlanContext().getAppSettings())));
    final NDSDataLakeTenant tenant1 =
        buildSimpleTenant(
            tenant1Id,
            ndsGroup.getGroupId(),
            tenant1Name,
            tenant1PrivateEndpointHostnameMap,
            AWSRegionName.US_EAST_1.getName());
    _ndsDataLakeTenantDao.saveTenant(tenant1);

    final Move syncPrivateEndpointHostnameMove =
        getMoveFromPlanner(AWSSyncDataLakePrivateEndpointHostnameMove.class, plan.getPlanContext());

    plan.addMove(syncPrivateEndpointHostnameMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(syncPrivateEndpointHostnameMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(syncPrivateEndpointHostnameMove);
      throw t;
    }

    final List<NDSDataLakeTenant> tenants =
        _ndsDataLakeTenantDao.findTenantsByGroupId(ndsGroup.getGroupId());
    final Map<String, String> map = tenants.get(0).getPrivateEndpointHostnameMap();

    assertEquals(1, map.size());
    assertEquals(
        "adf-"
            + tenants.get(0).getTenantId().toString()
            + "-"
            + ndsGroup.getDNSPin()
            + "-vpce-00000000000000001.a-pl.query."
            + NDSSettings.getCrossCloudInstanceDomainName(plan.getPlanContext().getAppSettings()),
        map.get("vpce-00000000000000001"));

    // cleanup.
    cleanupDNSRecord(
        map.get("vpce-00000000000000001"),
        RRType.CNAME,
        AWSNDSSettings.getRoute53HostedZoneIdForDataLake(plan.getPlanContext().getAppSettings()));
  }

  private NDSDataLakeTenant buildTenant(
      final ObjectId pGroupId,
      final String pTenantName,
      final ObjectId pTenantId,
      final CloudProvider pProvider) {
    return new NDSDataLakeTenant(
        new NDSDataLakeTenantId(pGroupId, pTenantName),
        pTenantId,
        Date.from(Instant.EPOCH),
        Date.from(Instant.EPOCH),
        NDSDataLakeState.UNVERIFIED,
        new NDSDataLakeDataProcessRegion(pProvider, null),
        false,
        false,
        0,
        null,
        false,
        null,
        List.of(String.format("foo-%s.virginia-usa.z.query.mongodb.net", pTenantName)),
        String.format("foo-%s.z-pl.query.mongodb.net", pTenantName),
        Date.from(Instant.EPOCH),
        Collections.emptyMap());
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testCrossProviderAWSSyncDataLakePrivateEndpointHostnameMoveIntTests() {
    final NDSGroup ndsGroup = getNDSGroup();

    // Create multiple tenants and insert them into the DB.
    final String tenant1Name = "tenant1";
    final ObjectId tenant1Id = ObjectId.get();
    final String tenant2Name = "tenant2";
    final ObjectId tenant2Id = ObjectId.get();
    final String tenant3Name = "tenant3";
    final ObjectId tenant3Id = ObjectId.get();

    NDSDataLakeTenant tenant1 =
        buildTenant(ndsGroup.getGroupId(), tenant1Name, tenant1Id, CloudProvider.AWS);
    NDSDataLakeTenant tenant2 =
        buildTenant(ndsGroup.getGroupId(), tenant2Name, tenant2Id, CloudProvider.AWS);
    NDSDataLakeTenant tenant3 =
        buildTenant(ndsGroup.getGroupId(), tenant3Name, tenant3Id, CloudProvider.AZURE);
    _ndsDataLakeTenantDao.saveTenant(tenant1);
    _ndsDataLakeTenantDao.saveTenant(tenant2);
    _ndsDataLakeTenantDao.saveTenant(tenant3);

    // Create multiple endpoints and save them in network settings.
    final String connectionName1 = "/foo/bar/1";
    final String dnsName1 = "cat";
    final String connectionName2 = "/foo/bar/2";
    final String dnsName2 = "dog";
    final String connectionName3 = "/foo/bar/3";
    final String dnsName3 = "parrot";

    final Set<NDSPrivateNetworkEndpointIdEntry> entries =
        Set.of(
            new NDSPrivateNetworkEndpointIdEntry.Builder()
                .endpointId(connectionName1)
                .type(Type.DATA_LAKE)
                .provider(CloudProvider.AWS)
                .comment("comment1")
                .customerEndpointDNSName(dnsName1)
                .region(AWSRegionName.US_WEST_1.getName())
                .build(),
            new NDSPrivateNetworkEndpointIdEntry.Builder()
                .endpointId(connectionName2)
                .type(Type.DATA_LAKE)
                .provider(CloudProvider.AWS)
                .comment("comment2")
                .customerEndpointDNSName(dnsName2)
                .region(AWSRegionName.US_EAST_1.getName())
                .build(),
            new NDSPrivateNetworkEndpointIdEntry.Builder()
                .endpointId(connectionName3)
                .type(Type.DATA_LAKE)
                .provider(CloudProvider.AZURE)
                .comment("comment3")
                .customerEndpointIPAddress(dnsName3)
                .region("eastus2")
                .build());

    final NDSPrivateNetworkSettings networkSettings =
        new NDSPrivateNetworkSettings(entries, new Date());

    getNDSGroupDao()
        .updatePrivateNetworkSettings(
            ndsGroup.getGroupId(),
            networkSettings,
            ndsGroup.getPrivateNetworkSettings().getLastUpdated());

    // Sync private endpoint hostnames and add the move to the plan.
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move syncPrivateEndpointHostnameMove =
        getMoveFromPlanner(AWSSyncDataLakePrivateEndpointHostnameMove.class, plan.getPlanContext());

    plan.addMove(syncPrivateEndpointHostnameMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(syncPrivateEndpointHostnameMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(syncPrivateEndpointHostnameMove);
      throw t;
    }

    // Ensure tenants' private hostnames are updated.
    final List<NDSDataLakeTenant> tenants =
        _ndsDataLakeTenantDao.findTenantsByGroupId(ndsGroup.getGroupId());
    assertEquals(3, tenants.size());

    for (NDSDataLakeTenant tenant : tenants) {
      final Map<String, String> map = tenant.getPrivateEndpointHostnameMap();
      // Non-AWS tenants shouldn't have any entries here.
      if (tenant.getCloudProvider() != CloudProvider.AWS) {
        assertEquals(0, map.size());
        continue;
      }

      assertEquals(2, map.size());
      assertEquals(
          "adf-"
              + tenant.getTenantId().toString()
              + "-"
              + ndsGroup.getDNSPin()
              + "-"
              + connectionName1
              + ".a-pl.query."
              + NDSSettings.getCrossCloudInstanceDomainName(plan.getPlanContext().getAppSettings()),
          map.get(connectionName1));
      assertEquals(
          "adf-"
              + tenant.getTenantId().toString()
              + "-"
              + ndsGroup.getDNSPin()
              + "-"
              + connectionName2
              + ".a-pl.query."
              + NDSSettings.getCrossCloudInstanceDomainName(plan.getPlanContext().getAppSettings()),
          map.get(connectionName2));
    }
  }
}
