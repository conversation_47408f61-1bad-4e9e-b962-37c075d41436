package com.xgen.svc.nds.aws.svc.admincapacity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.CapacityReservation;
import com.amazonaws.services.ec2.model.Tag;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.aws._public.view.admincapacity.ScheduledAWSInstanceCapacitySpecView;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureCheckResult;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest.CapacityHold;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest.CapacityHold.HoldState;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest.CheckState;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckResult;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckResult.CheckStatus;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.InstanceCapacitySpec;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.admincapacity.InstanceUnboundCapacitySpecView;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.admincapacity.ODCRInfoView;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.admincapacity.TargetedODCRRequestView;
import com.xgen.cloud.nds.cloudprovider._public.svc.admincapacity.CloudProviderCheckCapacityRequestSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.azure.svc.admincapacity.AzureCheckCapacityRequestSvc;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AWSCheckCapacityRequestSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private AWSCheckCapacityRequestSvc _svc;

  @Inject private AWSAccountDao _accountDao;

  @Inject private AzureCheckCapacityRequestSvc _azureCheckCapacityRequestSvc;

  @Inject private AzureSubscriptionDao _subscriptionDao;

  private AWSAccount _account;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final AzureSubscription _subscription =
        new AzureSubscription(NDSModelTestFactory.getFullyAvailableAzureSubscription());
    _subscriptionDao.save(_subscription);

    _account = new AWSAccount(NDSModelTestFactory.getFullyAvailableAWSAccount());
    _accountDao.save(_account);
  }

  @Test
  public void testCreateNewRequest_failValidate() {
    try {
      _svc.createNewRequest(new ObjectId(), null, 0);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testCreateNewRequest_failValidate_InvalidSpecs() {
    try {
      _svc.createNewRequest(new ObjectId(), new TargetedODCRRequestView(null), 0);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testCreateNewRequest_failValidate_tooManyNodes() {
    final InstanceUnboundCapacitySpecView spec =
        new InstanceUnboundCapacitySpecView(
            AWSRegionName.US_EAST_1.getName(),
            0,
            "t4g.small",
            CheckCapacityRequest.MAX_INSTANCES_PER_REQUEST + 1);

    try {
      _svc.createNewRequest(new ObjectId(), new TargetedODCRRequestView(List.of(spec)), 0);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testCreateNewRequest_success() {
    final InstanceUnboundCapacitySpecView spec =
        new InstanceUnboundCapacitySpecView(
            AWSRegionName.US_EAST_1.getName(),
            0,
            "t4g.small",
            CheckCapacityRequest.MAX_INSTANCES_PER_REQUEST);

    try {
      _svc.createNewRequest(new ObjectId(), new TargetedODCRRequestView(List.of(spec)), 0);
    } catch (SvcException e) {
      fail();
    }
  }

  @Test
  public void testAcceptAssistedReservationsAndCreateRequest_success() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();
    final List<ScheduledAWSInstanceCapacitySpecView> specs =
        List.of(
            getAssistedCapacitySpec(DateUtils.addDays(now, 5), "reservation1"),
            getAssistedCapacitySpec(DateUtils.addDays(now, 0), "reservation2"),
            getAssistedCapacitySpec(DateUtils.addDays(now, -5), "reservation3"));

    try {
      final ObjectId requestId = _svc.acceptAssistedReservationsAndCreateRequest(groupId, specs);
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> savedRequest =
          _svc.findRequest(requestId);
      assertEquals(CheckState.PENDING_ACCEPT, savedRequest.getState());
      assertEquals(3, savedRequest.getInstanceSpecs().size());
      savedRequest
          .getInstanceSpecs()
          .forEach(
              spec -> {
                assertEquals(3, spec.getNumInstances());
                assertEquals(_account.getId(), spec.getAccountId());
                assertEquals(AWSRegionName.US_EAST_1, spec.getRegionName());
                assertEquals("us-east-1a", spec.getAZName());
                assertEquals("t4g.small", spec.getInstanceSize());
              });
      assertEquals(3, savedRequest.getCheckResults().size());
      for (var i = 0; i < 3; i++) {
        final AWSCheckResult checkResult = savedRequest.getCheckResults().get(i);
        assertEquals(String.format("reservation%d", i + 1), checkResult.getReservationId().get());
        assertEquals(CheckStatus.SUCCESS, checkResult.getCheckStatus());
        assertTrue(checkResult.getErrorMessage().isEmpty());
      }
      assertNotNull(savedRequest.getCapacityHold());
      final CapacityHold hold = savedRequest.getCapacityHold().get();
      assertEquals(DateUtils.addDays(now, 10), hold.getHoldUtil());
      assertEquals(HoldState.HELD, hold.getHoldState());
      assertFalse(hold.getRequestRelease());
    } catch (SvcException e) {
      fail();
    }

    final List<ScheduledAWSInstanceCapacitySpecView> specsAlreadyReserved =
        List.of(getAssistedCapacitySpec(DateUtils.addDays(now, 5), "reservation1"));
    try {
      _svc.acceptAssistedReservationsAndCreateRequest(groupId, specsAlreadyReserved);
      fail("should fail to accept already held reservation");
    } catch (SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
    }
  }

  @Test
  public void testFindRequest_notFound() throws SvcException {
    _svc.createNewRequest(
        new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    try {
      _svc.findRequest(new ObjectId());
      fail();
    } catch (SvcException e) {
      assertEquals(CommonErrorCode.NOT_FOUND, e.getErrorCode());
    }
  }

  @Test
  public void testFindRequest_found() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    try {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(requestId, request.getId());
    } catch (SvcException e) {
      fail();
    }
  }

  @Test
  public void testSetState_invalidTransition() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.NEW, request.getState());
    }

    try {
      // NEW->COMPLETE not allowed
      _svc.setState(requestId, CheckState.COMPLETE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }

    // NEW->STARTED allowed
    _svc.setState(requestId, CheckState.STARTED);

    try {
      // STARTED->STARTED not allowed
      _svc.setState(requestId, CheckState.STARTED);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }

    // STARTED->COMPLETE allowed
    _svc.setState(requestId, CheckState.COMPLETE);

    try {
      // COMPLETE->STARTED not allowed
      _svc.setState(requestId, CheckState.STARTED);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }

    try {
      // COMPLETE->NEW not allowed
      _svc.setState(requestId, CheckState.NEW);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testSetState_COMPLETE() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.NEW, request.getState());
    }

    _svc.setState(requestId, CheckState.STARTED);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.STARTED, request.getState());
    }

    _svc.setState(requestId, CheckState.COMPLETE);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.COMPLETE, request.getState());
    }
  }

  @Test
  public void testSetState_FAILED() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.NEW, request.getState());
    }

    _svc.setState(requestId, CheckState.STARTED);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.STARTED, request.getState());
    }

    _svc.setState(requestId, CheckState.FAILED);

    {
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          _svc.findRequest(requestId);
      assertEquals(CheckState.FAILED, request.getState());
    }
  }

  @Test
  public void testSetCheckResults_incorrectSize() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    try {
      _svc.setCheckResults(
          requestId,
          List.of(
              new AWSCheckResult(CheckStatus.SUCCESS, "", null),
              new AWSCheckResult(CheckStatus.SUCCESS, "", null)));
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testSetCheckResults_correctSize() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    _svc.setCheckResults(
        requestId, List.of(new AWSCheckResult(CheckStatus.SUCCESS, "reservationId", null)));

    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestId);
    assertEquals(1, request.getCheckResults().size());
    assertEquals(CheckStatus.SUCCESS, request.getCheckResults().get(0).getCheckStatus());
    assertEquals("reservationId", request.getCheckResults().get(0).getReservationId().get());
  }

  @Test
  public void testSetCapacityHeld() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 3);

    _svc.setCapacityHeld(requestId);

    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestId);
    assertEquals(HoldState.HELD, request.getCapacityHold().get().getHoldState());
  }

  @Test
  public void testSetCapacityHeld_alreadyComplete() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 3);

    // Need to first set to STARTED, so the transition to COMPLETED is allowed
    _svc.setState(requestId, CheckState.STARTED);
    _svc.setState(requestId, CheckState.COMPLETE);

    try {
      _svc.setCapacityHeld(requestId);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testSetCapacityHeld_notRequested() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    try {
      _svc.setCapacityHeld(requestId);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testRequestReleaseHeldCapacity_invalidRequest() {
    final ObjectId groupId = new ObjectId();

    try {
      _svc.requestReleaseHeldCapacity(groupId, new ObjectId());
      fail();
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.NOT_FOUND, e.getErrorCode());
    }
  }

  @Test
  public void testRequestReleaseHeldCapacity_notHeld() throws SvcException {
    final ObjectId groupId = new ObjectId();
    final ObjectId requestId =
        _svc.createNewRequest(groupId, new TargetedODCRRequestView(List.of(getDefaultSpec())), 0);

    _svc.setState(requestId, CheckState.STARTED);

    try {
      _svc.requestReleaseHeldCapacity(groupId, requestId);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testRequestReleaseHeldCapacity_notComplete() throws SvcException {
    final ObjectId groupId = new ObjectId();
    final ObjectId requestId =
        _svc.createNewRequest(groupId, new TargetedODCRRequestView(List.of(getDefaultSpec())), 1);

    try {
      _svc.requestReleaseHeldCapacity(groupId, requestId);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testRequestReleaseHeldCapacity() throws SvcException {
    final ObjectId groupId = new ObjectId();
    final ObjectId requestId =
        _svc.createNewRequest(groupId, new TargetedODCRRequestView(List.of(getDefaultSpec())), 1);

    // Need to first set to STARTED, so the transition to COMPLETED is allowed
    _svc.setState(requestId, CheckState.STARTED);
    _svc.setCapacityHeld(requestId);
    _svc.setState(requestId, CheckState.COMPLETE);

    _svc.requestReleaseHeldCapacity(groupId, requestId);

    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestId);
    assertTrue(request.getCapacityHold().get().getRequestRelease());
  }

  @Test
  public void testSetCapacityReleased_invalidRequest() throws SvcException {
    try {
      _svc.setCapacityReleased(new ObjectId());
      fail();
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.NOT_FOUND, e.getErrorCode());
    }
  }

  @Test
  public void testSetCapacityReleased_notComplete() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 1);

    try {
      _svc.setCapacityReleased(requestId);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testSetCapacityReleased() throws SvcException {
    final ObjectId requestId =
        _svc.createNewRequest(
            new ObjectId(), new TargetedODCRRequestView(List.of(getDefaultSpec())), 1);

    // Need to first set to STARTED, so the transition to COMPLETED is allowed
    _svc.setState(requestId, CheckState.STARTED);
    _svc.setState(requestId, CheckState.COMPLETE);

    _svc.setCapacityReleased(requestId);

    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestId);
    assertEquals(HoldState.RELEASED, request.getCapacityHold().get().getHoldState());
  }

  @Test
  public void testFindActiveMatchingHeldReservationId_notMatch() throws SvcException {
    final ObjectId groupId = new ObjectId();

    final ObjectId requestId =
        createNewHeldAWSRequest(
            groupId,
            getDefaultSpec(),
            3,
            new AWSCheckResult(CheckStatus.SUCCESS, "reservationId", null));

    final NDSGroup group = _ndsGroupSvc.ensureGroup(groupId);
    final ObjectId awsAccountId =
        ((AWSCloudProviderContainer)
                group
                    .getCloudProviderContainer(CloudProvider.AWS, AWSRegionName.US_EAST_1, null)
                    .get())
            .getAWSAccountId();

    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestId);

    assertEquals(
        0,
        _svc.findActiveMatchingHeldReservationId(
                groupId,
                awsAccountId,
                AWSRegionName.AP_SOUTH_2, // region doesn't match
                "ap-south-2a",
                request.getInstanceSpecs().get(0).getInstanceSize())
            .size());
    assertEquals(
        0,
        _svc.findActiveMatchingHeldReservationId(
                groupId,
                awsAccountId,
                (AWSRegionName) request.getInstanceSpecs().get(0).getRegionName(),
                "foo", // az doesn't match
                request.getInstanceSpecs().get(0).getInstanceSize())
            .size());
    assertEquals(
        0,
        _svc.findActiveMatchingHeldReservationId(
                groupId,
                awsAccountId,
                (AWSRegionName) request.getInstanceSpecs().get(0).getRegionName(),
                request.getInstanceSpecs().get(0).getAZName(),
                "foo") // instance size doesn't match
            .size());
  }

  @Test
  public void testFindActiveMatchingHeldReservationId_match() throws SvcException {
    final ObjectId groupId = new ObjectId();

    final ObjectId requestId =
        createNewHeldAWSRequest(
            groupId,
            getDefaultSpec(),
            3,
            new AWSCheckResult(CheckStatus.SUCCESS, "reservationId", null));

    final NDSGroup group = _ndsGroupSvc.ensureGroup(groupId);
    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestId);

    assertEquals(
        1,
        _svc.findActiveMatchingHeldReservationId(
                groupId,
                ((AWSCloudProviderContainer)
                        group
                            .getCloudProviderContainer(
                                CloudProvider.AWS, AWSRegionName.US_EAST_1, null)
                            .get())
                    .getAWSAccountId(),
                (AWSRegionName) request.getInstanceSpecs().get(0).getRegionName(),
                request.getInstanceSpecs().get(0).getAZName(),
                request.getInstanceSpecs().get(0).getInstanceSize())
            .size());
  }

  @Test
  public void testFindActiveMatchingHeldReservationId_matchMultiple() throws SvcException {
    final ObjectId groupId = new ObjectId();

    final ObjectId requestIdForLookup =
        createNewHeldAWSRequest(
            groupId,
            getDefaultSpec(),
            3,
            new AWSCheckResult(CheckStatus.SUCCESS, "reservationId0", null));

    createNewHeldAWSRequest(
        groupId,
        getDefaultSpec(),
        3,
        new AWSCheckResult(CheckStatus.SUCCESS, "reservationId1", null));

    final NDSGroup group = _ndsGroupSvc.ensureGroup(groupId);
    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
        _svc.findRequest(requestIdForLookup);

    final List<String> result =
        _svc.findActiveMatchingHeldReservationId(
            groupId,
            ((AWSCloudProviderContainer)
                    group
                        .getCloudProviderContainer(CloudProvider.AWS, AWSRegionName.US_EAST_1, null)
                        .get())
                .getAWSAccountId(),
            (AWSRegionName) request.getInstanceSpecs().get(0).getRegionName(),
            request.getInstanceSpecs().get(0).getAZName(),
            request.getInstanceSpecs().get(0).getInstanceSize());

    assertEquals(2, result.size());
    assertNotEquals(result.get(0), result.get(1));
    assertTrue(result.get(0).equals("reservationId0") || result.get(0).equals("reservationId1"));
    assertTrue(result.get(1).equals("reservationId0") || result.get(1).equals("reservationId1"));
  }

  @Test
  public void testFindRequestsFiltering() throws SvcException {
    final ObjectId groupId = new ObjectId();

    final ObjectId requestIdToCheck =
        createNewHeldAWSRequest(
            groupId,
            getDefaultSpec(),
            3,
            new AWSCheckResult(CheckStatus.SUCCESS, "capacityReservationId", null));

    createNewHeldAzureRequest(
        groupId,
        new InstanceUnboundCapacitySpecView(
            AzureRegionName.US_EAST.getName(), 0, "Standard_B1ms", 1),
        3,
        new AzureCheckResult(CheckStatus.SUCCESS, "rsg", "crg", "cr", null));

    List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>> requests =
        _svc.findCloudProviderHeldRequestsByGroupId(groupId);
    assertEquals(1, requests.size());
    assertEquals(requestIdToCheck, requests.get(0).getId());

    List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>> nonReleasedRequests =
        _svc.findAllCloudProviderNonReleasedRequests();
    assertEquals(1, nonReleasedRequests.size());
    assertEquals(requestIdToCheck, nonReleasedRequests.get(0).getId());
  }

  @Test
  public void testGetODCRInfo_throwsSvcExceptionIfResourceNamesMissing() {
    final AWSInstanceCapacitySpec spec =
        new AWSInstanceCapacitySpec(
            new ObjectId(), AWSRegionName.US_EAST_1, "us-east-1a", "t4g.small", 1);

    final AWSCheckResult checkResult = new AWSCheckResult(CheckStatus.SUCCESS, null, null);

    try {
      _svc.getODCRInfo(spec, checkResult);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.RESOURCE_NOT_FOUND, e.getErrorCode());
    }
  }

  @Test
  public void testGetODCRInfo_returnsODCRInfoView()
      throws NoSuchFieldException, IllegalAccessException, SvcException {
    final AWSInstanceCapacitySpec spec =
        new AWSInstanceCapacitySpec(
            new ObjectId(), AWSRegionName.US_EAST_1, "us-east-1a", "t4g.small", 1);

    final AWSCheckResult checkResult =
        new AWSCheckResult(CheckStatus.SUCCESS, "reservationId", null);

    final AWSApiSvc apiSvc = mock(AWSApiSvc.class);
    final Date testDate = new Date();
    final String testState = "foo";
    final CapacityReservation capacityReservation = mock(CapacityReservation.class);
    doReturn(3).when(capacityReservation).getAvailableInstanceCount();
    doReturn(5).when(capacityReservation).getTotalInstanceCount();
    doReturn(testDate).when(capacityReservation).getStartDate();
    doReturn(testState).when(capacityReservation).getState();
    doReturn(List.of()).when(capacityReservation).getTags();

    doReturn(capacityReservation).when(apiSvc).getCapacityReservation(any(), any(), any(), any());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(AWSCheckCapacityRequestSvc.class), "_awsApiSvc", apiSvc);

    final ODCRInfoView view = _svc.getODCRInfo(spec, checkResult);
    assertEquals(3, view.getAvailableInstanceCount());
    assertEquals(5, view.getTotalInstanceCount());
    assertEquals(testDate, view.getStartDate());
    assertEquals(testState, view.getState());
  }

  private InstanceUnboundCapacitySpecView getDefaultSpec() {
    return new InstanceUnboundCapacitySpecView(
        AWSRegionName.US_EAST_1.getName(), 0, "t4g.small", 1);
  }

  private ScheduledAWSInstanceCapacitySpecView getAssistedCapacitySpec(
      final Date pStartDate, final String pReservationId) {
    final CapacityReservation assistedReservation = mock(CapacityReservation.class);
    when(assistedReservation.getStartDate()).thenReturn(pStartDate);
    when(assistedReservation.getCapacityReservationId()).thenReturn(pReservationId);
    when(assistedReservation.getAvailabilityZone()).thenReturn("us-east-1a");
    when(assistedReservation.getInstanceType()).thenReturn("t4g.small");
    when(assistedReservation.getTags())
        .thenReturn(
            List.of(
                new Tag("aws:ec2capacityreservation:createdBy", "AWS Assisted"),
                new Tag("aws:ec2capacityreservation:incrementalRequestedQuantity", "3")));
    return new ScheduledAWSInstanceCapacitySpecView(
        _account.getId(), AWSRegionName.US_EAST_1, assistedReservation);
  }

  private ObjectId createNewHeldAWSRequest(
      ObjectId groupId, InstanceUnboundCapacitySpecView spec, int holdDays, AWSCheckResult result)
      throws SvcException {
    return createNewHeldRequest(_svc, groupId, spec, holdDays, result);
  }

  private void createNewHeldAzureRequest(
      ObjectId groupId, InstanceUnboundCapacitySpecView spec, int holdDays, AzureCheckResult result)
      throws SvcException {
    createNewHeldRequest(_azureCheckCapacityRequestSvc, groupId, spec, holdDays, result);
  }

  private <I extends InstanceCapacitySpec, T extends CheckResult> ObjectId createNewHeldRequest(
      CloudProviderCheckCapacityRequestSvc<I, T> svc,
      ObjectId groupId,
      InstanceUnboundCapacitySpecView spec,
      int holdDays,
      T result)
      throws SvcException {
    final ObjectId requestId =
        svc.createNewRequest(groupId, new TargetedODCRRequestView(List.of(spec)), holdDays);
    svc.setCheckResults(requestId, List.of(result));

    svc.setState(requestId, CheckState.STARTED);
    svc.setCapacityHeld(requestId);
    svc.setState(requestId, CheckState.COMPLETE);

    return requestId;
  }
}
