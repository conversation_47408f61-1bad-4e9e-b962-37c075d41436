package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.Subnet;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAvailabilityZone;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSPhysicalZoneId;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.svc.AWSCapacityDenylistSvc;
import com.xgen.cloud.nds.capacity._public.svc.CloudProviderAvailabilityFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.svc.AZSelectionSvc;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.junit.After;
import org.junit.Test;

public class AWSSelectSubnetStepIntTests extends AWSExternalIntTest {
  @Inject AWSApiSvc _awsApiSvc;
  @Inject AWSAccountDao _awsAccountDao;
  @Inject CloudProviderAvailabilityFactory _cloudProviderAvailabilityFactory;
  @Inject NDSGroupDao _ndsGroupDao;
  @Inject AZSelectionSvc _azSelectionSvc;
  @Inject AWSInstanceHardwareDao _instanceHardwareDao;
  @Inject AWSCapacityDenylistSvc _awsCapacityDenylistSvc;

  private Plan _setupPlan;
  private Plan _testPlan;

  private AWSPhysicalZoneId _expectedZoneId;

  @Override
  @After
  public void tearDown() throws InterruptedException {
    super.tearDown();
    if (_testPlan != null) {
      try {
        waitForPlanRollbackSuccess(_testPlan);
      } catch (Exception ignored) {
      }
    }
    if (_setupPlan != null) {
      try {
        waitForPlanRollbackSuccess(_setupPlan);
      } catch (Exception ignored) {
      }
    }
  }

  private void registerCapacityDenylistOverrides(List<AWSPhysicalZoneId> pZonesToConstrain) {
    final RegionConfig regionConfig =
        getClusterDescription()
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigForProvider(CloudProvider.AWS);
    final AWSHardwareSpec hardwareSpec = (AWSHardwareSpec) regionConfig.getElectableSpecs();
    for (final AWSPhysicalZoneId zoneId : pZonesToConstrain) {
      _awsCapacityDenylistSvc.registerCapacityOverride(
          hardwareSpec.getInstanceFamily(),
          hardwareSpec.getInstanceSize(),
          (AWSRegionName) regionConfig.getRegionName(),
          zoneId,
          CapacityDenyListEntry.Status.CAPACITY_UNAVAILABLE_OVERRIDE,
          Optional.empty());
    }
  }

  public void setUpTest(final boolean pCapacityAware) {
    _setupPlan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            _setupPlan.getPlanContext(),
            getTags(),
            getContainer().getId());
    _setupPlan.addMove(provisionContainerMove);

    // Need to save plan so we can get state prepared
    getPlanDao().save(_setupPlan);

    waitForPlanPerformSuccess(_setupPlan);

    final List<AWSPhysicalZoneId> zoneIds =
        Stream.of(getContainer().getSubnets()).map(AWSSubnet::getZoneId).toList();

    assertEquals(2, zoneIds.size());
    if (pCapacityAware) {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          getGroup(), null, FeatureFlag.AWS_CAPACITY_AWARE_AZ_SELECTION);
      registerCapacityDenylistOverrides(zoneIds.subList(0, 1));
      _expectedZoneId = zoneIds.get(1);
    } else {
      _expectedZoneId = zoneIds.get(0);
    }
  }

  private AWSSelectSubnetStep createAWSSelectSubnetStep(final boolean pCapacityAware) {
    _testPlan = new Plan(getGroup().getId(), getPlanContextFactory());
    _testPlan.addMove(new DummyMove());
    getPlanDao().save(_testPlan);

    final NDSGroup ndsGroup =
        _ndsGroupDao.find(_testPlan.getPlanContext().getGroupId()).orElseThrow();
    final NDSPlanContext context = (NDSPlanContext) _testPlan.getPlanContext();

    final ReplicaSetHardware hardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                context.getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware)
            hardware
                .getAllHardware()
                .filter(h -> h.getInstanceId().equals(getInstanceIds().get(0)))
                .findFirst()
                .orElseThrow();
    final AWSCloudProviderContainer container =
        ndsGroup
            .getCloudProviderContainer(getContainer().getId())
            .map(AWSCloudProviderContainer.class::cast)
            .orElseThrow();
    final int regionHardwareIndex =
        hardware.getRegionHardwareIndex(
            instanceHardware.getInstanceId(),
            hardware.getInstanceIdToRegionNameMap(
                getClusterDescription()
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()));

    return new AWSSelectSubnetStep(
        context,
        new Step.State(
            _testPlan.getId(),
            _testPlan.getMoves().get(0).getId(),
            0,
            _testPlan.getPlanContext().getPlanDao()),
        container,
        instanceHardware.getInstanceId(),
        regionHardwareIndex,
        getNDSGroup(),
        getClusterDescription(),
        hardware.getId(),
        pCapacityAware,
        _cloudProviderAvailabilityFactory,
        _awsApiSvc,
        _azSelectionSvc,
        _instanceHardwareDao);
  }

  public void testAWSSelectSubnetStepInternal(final AWSSelectSubnetStep pStep) {

    // Now select subnet
    waitForStepPerformDone(pStep);

    final Result<AWSSelectSubnetStep.Data> result = pStep.perform();

    assertFalse(result.getData().getSubnetId().isEmpty());

    final AWSAccount awsAccount =
        _awsAccountDao
            .find(getContainer().getAWSAccountId())
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "Failed to find AWS Account by id (%s)",
                            getContainer().getAWSAccountId())));

    final Subnet subnet =
        _awsApiSvc
            .findSubnets(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pStep.getContext().getLogger(),
                Collections.singletonList(result.getData().getSubnetId()))
            .get(0);

    assertTrue(
        awsAccount.getRegions().stream()
            .filter(r -> r.getName().equals(getContainer().getRegion()))
            .findFirst()
            .orElseThrow()
            .getAvailabilityZones()
            .stream()
            .anyMatch(
                az ->
                    az.getName().equals(subnet.getAvailabilityZone())
                        && az.getStatus() == AWSAvailabilityZone.Status.AVAILABLE));
    assertEquals(_expectedZoneId, new AWSPhysicalZoneId(subnet.getAvailabilityZoneId()));
    waitForStepRollbackDone(pStep);

    // Make sure rolling back does nothing
    assertTrue(pStep.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSSelectSubnetStep_CapacityUnaware() {
    setUpTest(false);
    final AWSSelectSubnetStep step = createAWSSelectSubnetStep(false);
    testAWSSelectSubnetStepInternal(step);
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSSelectSubnetStep_CapacityAware() {
    setUpTest(true);
    final AWSSelectSubnetStep step = createAWSSelectSubnetStep(true);
    testAWSSelectSubnetStepInternal(step);
  }
}
