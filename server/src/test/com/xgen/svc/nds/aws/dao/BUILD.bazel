load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/svc/nds/dao:AbstractBaseTests",
        "//server/src/unit/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:junit_junit",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)
