package com.xgen.svc.nds.aws.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.Snapshot;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.CpsTestUtils;
import com.xgen.svc.nds.dao.BackupSnapshotDaoForTestExt;
import com.xgen.svc.nds.planner.BackupSnapshotUtils;
import com.xgen.svc.nds.planner.snapshot.CpsReplSetSnapshotMove;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AWSCreateEbsSnapshotMoveBaseIntTest extends AWSCreateSnapshotIntTest {

  private static final Logger LOG =
      LoggerFactory.getLogger(AWSCreateEbsSnapshotMoveBaseIntTest.class);

  @Inject private BackupSnapshotDaoForTestExt _backupSnapshotDao;
  private static int _createdSnapshotsExpected = 0;
  private static int _purgedSnapshotsExpected = 0;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _createdSnapshotsExpected = 0;
    _purgedSnapshotsExpected = 0;
  }

  protected CpsReplSetSnapshotMove prepareMoveForSnapshotCreate() {
    final ClusterDescription cluster = getClusterDescription();
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final CpsReplSetSnapshotMove snapshotMove =
        CpsReplSetSnapshotMove.factoryCreate(plan.getPlanContext(), cluster.getName());

    plan.addMove(snapshotMove);
    getPlanDao().save(plan);
    return snapshotMove;
  }

  public void testAWSCreateEbsSnapshot(
      final CpsReplSetSnapshotMove pMove, final ObjectId pBackupJobId) {
    final ObjectId groupId = getGroup().getId();
    final ClusterDescription cluster = getClusterDescription();

    // add an AWS Snapshot to be picked up
    final BackupJob backupJob = _backupJobDao.find(pBackupJobId).get();
    BackupSnapshotUtils.queueScheduledSnapshot(
        new Date(), getClusterDescription(), backupJob, _backupSnapshotDao, false, false);

    pMove.perform();

    waitForMovePerformDone(pMove);
    _createdSnapshotsExpected++;

    final List<BackupSnapshot> backupSnapshotList =
        _backupSnapshotDao.findCompletedByCluster(
            cluster.getGroupId(), cluster.getUniqueId(), false);
    assertEquals(_createdSnapshotsExpected, backupSnapshotList.size());

    final AWSBackupSnapshot backupSnapshot = (AWSBackupSnapshot) backupSnapshotList.get(0);
    final String ebsSnapshotId = backupSnapshot.getEbsSnapshotId();

    final Snapshot snapshot =
        getAWSApiSvc()
            .findEbsSnapshot(
                getContainer().getAWSAccountId(), getContainer().getRegion(), LOG, ebsSnapshotId);

    assertNotNull(snapshot);
    assertEquals(snapshot.getDescription(), backupSnapshot.getEbsSnapshotDescription());
    assertEquals(snapshot.isEncrypted(), backupSnapshot.getEbsVolumeEncrypted());
    assertEquals(snapshot.getVolumeSize(), backupSnapshot.getEbsVolumeSize());

    CpsTestUtils.verifyCompletedBackupSnapshotMetadata(backupJob, backupSnapshot);

    // make sure snapshot is tagged correctly

    final Map<String, String> snapshotTags = new HashMap<>();
    snapshot.getTags().forEach(t -> snapshotTags.put(t.getKey(), t.getValue()));
    assertEquals(cluster.getName(), snapshotTags.get(NDSDefaults.CLUSTER_NAME_TAG_NAME));
    assertEquals(groupId.toString(), snapshotTags.get(NDSDefaults.GROUP_ID_TAG_NAME));
    assertEquals(
        cluster.getUniqueId().toString(), snapshotTags.get(NDSDefaults.CLUSTER_UNIQUE_ID_TAG_NAME));
    assertEquals(
        getContainer().getId().toString(), snapshotTags.get(NDSDefaults.CONTAINER_ID_TAG_NAME));
  }

  protected void testMoveRollback(final CpsReplSetSnapshotMove pMove) {
    waitForMoveRollbackDone(pMove);
    _purgedSnapshotsExpected++;

    final List<BackupSnapshot> snapshotsPurged =
        _backupSnapshotDao.findPurgedByCluster(
            pMove.getContext().getGroupId(), getClusterDescription().getName());

    assertEquals(
        "There should be a purged snapshot in db because of the move rollback",
        _purgedSnapshotsExpected,
        snapshotsPurged.size());

    final AWSBackupSnapshot awsBackupSnapshot = (AWSBackupSnapshot) snapshotsPurged.get(0);
    // Cps Snapshots marked as orphaned items when rolledBack
    getOrphanedItemSvc().cleanItems();

    final boolean isBackupSnapshotDeleted =
        waitForCondition(
            Duration.ofMinutes(5),
            () ->
                findSnapshot(awsBackupSnapshot.getEbsSnapshotId(), pMove.getContext().getLogger())
                    == null);

    assertTrue(
        "There should be no snapshot in AWS because of the move rollback", isBackupSnapshotDeleted);
  }

  protected Snapshot findSnapshot(final String pSnapshotId, final Logger pLogger) {
    try {
      return getAWSApiSvc()
          .findEbsSnapshot(
              getContainer().getAWSAccountId(), getContainer().getRegion(), pLogger, pSnapshotId);
    } catch (final AWSApiException e) {
      if (e.getErrorCode() != CommonErrorCode.NOT_FOUND) {
        throw e;
      }
      return null;
    }
  }
}
