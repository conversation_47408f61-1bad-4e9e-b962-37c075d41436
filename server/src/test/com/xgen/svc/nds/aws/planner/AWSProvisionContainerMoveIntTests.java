package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.InternetGateway;
import com.amazonaws.services.ec2.model.Subnet;
import com.amazonaws.services.ec2.model.Vpc;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import java.time.Duration;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.Before;
import org.junit.Test;

public class AWSProvisionContainerMoveIntTests extends AWSExternalIntTest {

  @Override
  @Before
  public void setUp() throws Exception {
    // Set region to us east 1 to test that subnets are created for all 6 AZs
    super.setUp(AWSRegionName.US_EAST_1);
  }

  public void testAWSProvisionContainerMoveInternal(final Move pMove) throws InterruptedException {
    Result<NoData> result = pMove.perform();

    // The move should not fail but it may be done or in progress
    assertNotFailed(pMove.getClass().getSimpleName(), result);

    NDSGroup group = getNDSGroupDao().find(getGroup().getId()).get();
    AWSCloudProviderContainer container =
        (AWSCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

    while (!result.getStatus().isDone()) {

      assertNotFailed(pMove.getClass().getSimpleName(), result);

      assertFalse(container.getVpcId().isPresent());
      assertFalse(container.getIgwId().isPresent());
      assertEquals(0, container.getSubnets().length);

      result = pMove.perform();
      group = getNDSGroupDao().find(getGroup().getId()).get();
      container =
          (AWSCloudProviderContainer) group.getCloudProviderContainer(getContainer().getId()).get();

      Thread.sleep(Duration.ofSeconds(5).toMillis());
    }

    assertTrue(container.getVpcId().isPresent());
    assertTrue(container.getIgwId().isPresent());
    assertTrue(container.getSubnets().length > 0);

    final Vpc vpc =
        getAWSApiSvc()
            .findVpc(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pMove.getContext().getLogger(),
                container.getVpcId().get());
    assertEquals(vpc.getVpcId(), container.getVpcId().get());

    final InternetGateway igw =
        getAWSApiSvc()
            .findInternetGateway(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pMove.getContext().getLogger(),
                container.getIgwId().get());
    assertEquals(igw.getInternetGatewayId(), container.getIgwId().get());
    assertTrue(igw.getAttachments().stream().anyMatch(a -> a.getVpcId().equals(vpc.getVpcId())));

    final int azCount =
        getAwsAccountDao().find(getContainer().getAWSAccountId()).get().getRegions().stream()
            .filter(r -> r.getName().equals(AWSRegionName.US_EAST_1))
            .findFirst()
            .get()
            .getAvailabilityZones()
            .size();

    assertEquals(azCount, container.getSubnets().length);

    for (final AWSSubnet subnet : container.getSubnets()) {
      final Subnet awsSubnet =
          getAWSApiSvc()
              .findSubnets(
                  getContainer().getAWSAccountId(),
                  getContainer().getRegion(),
                  pMove.getContext().getLogger(),
                  Collections.singletonList(subnet.getSubnetId()))
              .get(0);

      assertEquals(subnet.getSubnetId(), awsSubnet.getSubnetId());
      assertEquals(subnet.getAvailabilityZone(), awsSubnet.getAvailabilityZone());
    }

    waitForMoveRollbackDone(pMove);

    verifyVpcDeleted(container.getVpcId().get());
    verifyInternetGatewayDeleted(container.getIgwId().get());
    verifySubnetsDeleted(
        Stream.of(container.getSubnets()).map(AWSSubnet::getSubnetId).collect(Collectors.toList()));
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSProvisionContainerMove() throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move move =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    plan.addMove(move);

    // Need to save plan so we can get state prepared
    getPlanDao().save(plan);

    // Verify that cloud provider and region are added into move tags
    final Optional<Plan> savedPlan = getPlanDao().find(plan.getId());
    assertTrue(savedPlan.isPresent());
    assertEquals(getTags(), savedPlan.get().getMoveById(move.getId()).get().getTags());

    try {
      testAWSProvisionContainerMoveInternal(move);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForMoveRollbackDone(move));
      throw t;
    }
  }
}
