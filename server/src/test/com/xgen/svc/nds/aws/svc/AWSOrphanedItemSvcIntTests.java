package com.xgen.svc.nds.aws.svc;

import static com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem.Type.DATA_SNAPSHOT;
import static com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem.Type.EBS_VOLUME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.amazonaws.services.ec2.model.NetworkInterface;
import com.amazonaws.services.ec2.model.Snapshot;
import com.amazonaws.services.ec2.model.SnapshotState;
import com.amazonaws.services.ec2.model.Subnet;
import com.amazonaws.services.ec2.model.Vpc;
import com.amazonaws.services.ec2.model.VpcState;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.model.AWSAdminBackupSnapshot;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.svc.AWSIpamPoolSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.aws.planner.AWSCreateElasticIpStep;
import com.xgen.svc.nds.aws.planner.AWSCreateElasticIpStep.Data;
import com.xgen.svc.nds.aws.planner.AWSCreateElasticIpStep.ElasticIpState;
import com.xgen.svc.nds.aws.planner.AWSProvisionContainerMove;
import com.xgen.svc.nds.aws.planner.AWSProvisionMachineMove;
import com.xgen.svc.nds.aws.planner.AWSSnapshotAndOrphanVolumeStep;
import com.xgen.svc.nds.aws.planner.snapshot.AWSAdminBackupSnapshotMove;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.svc.NDSOrphanedItemSvc;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

/**
 * The name of this test is a little misleading. We are actually testing using NDSOrphanedItemSvc
 * but specifically for the case where it is using AWSOrphanedItemSvc to perform the clean up
 */
public class AWSOrphanedItemSvcIntTests extends AWSExternalIntTest {

  @Inject private NDSOrphanedItemSvc _svc;

  @Inject private NDSOrphanedItemDao _dao;

  @Inject private AWSIpamPoolSvc _awsIpamPoolSvc;

  protected void waitEBSSnapshotCompleted(
      final AWSCloudProviderContainer pContainer, final String pEBSSnapshotId)
      throws InterruptedException {
    while (true) {
      final Snapshot ebsSnapshot =
          getAWSApiSvc()
              .findEbsSnapshot(
                  pContainer.getAWSAccountId(),
                  pContainer.getRegion(),
                  getLogger(),
                  pEBSSnapshotId);
      final boolean snapshotError =
          ebsSnapshot == null
              || Objects.equals(SnapshotState.Error.toString(), ebsSnapshot.getState());
      if (snapshotError) {
        fail(String.format("Snapshot %s failed to complete.", pEBSSnapshotId));
      }

      if (Objects.equals(SnapshotState.Completed.toString(), ebsSnapshot.getState())) {
        getLogger().info("Snapshot {} has completed.", pEBSSnapshotId);
        break;
      } else {
        getLogger()
            .info(
                "Snapshot {} is still in progress. Progress: {}",
                pEBSSnapshotId,
                ebsSnapshot.getProgress());
        Thread.sleep(Duration.ofSeconds(30).toMillis());
      }
    }
  }

  @Test(timeout = 20 * 60 * 1000L)
  public void testCleanAwsOrphans() throws Exception {

    final AppUser globalAtlasOperatorUser =
        MmsFactory.createApiUserWithGlobalRole(
            "globalAtlasOperatorUser", Role.GLOBAL_ATLAS_OPERATOR);

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final Move provisionContainerMove =
        AWSProvisionContainerMove.factoryCreate(
            plan.getPlanContext(), Map.of(), getContainer().getId());
    plan.addMove(provisionContainerMove);

    final Move provisionMachineMove1 =
        AWSProvisionMachineMove.factoryCreate(
            plan.getPlanContext(),
            Map.of(),
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            false);
    provisionMachineMove1.addPredecessor(provisionContainerMove);
    plan.addMove(provisionMachineMove1);

    getPlanDao().save(plan);

    try {
      waitForPlanPerformSuccess(plan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(plan);
      throw t;
    }

    ReplicaSetHardware replicaSetHardware =
        getReplicaSetHardwareForInstance(
            getClusterDescription().getName(), getInstanceIds().get(0));
    AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).orElseThrow();
    final AWSCloudProviderContainer container = getContainer();

    // Set the past/future Elastic IP for the instance to test orphaning the past/future IP
    final Plan dummyPlanForPastElasticIpStep =
        new Plan(getGroup().getId(), getPlanContextFactory());
    dummyPlanForPastElasticIpStep.addMove(new DummyMove());
    final AWSCreateElasticIpStep pastElasticIpStep =
        getCreateElasticIpStepForPlan(dummyPlanForPastElasticIpStep, ElasticIpState.PAST);
    Data createPastElasticIpData;
    getPlanDao().save(dummyPlanForPastElasticIpStep);
    try {
      waitForStepPerformDone(pastElasticIpStep);
      createPastElasticIpData = pastElasticIpStep.perform().getData();
    } catch (final Throwable t) {
      waitForStepRollbackDone(pastElasticIpStep);
      throw t;
    }

    final Plan dummyPlanForFutureElasticIpStep =
        new Plan(getGroup().getId(), getPlanContextFactory());
    dummyPlanForFutureElasticIpStep.addMove(new DummyMove());
    final AWSCreateElasticIpStep futureElasticIpStep =
        getCreateElasticIpStepForPlan(dummyPlanForFutureElasticIpStep, ElasticIpState.FUTURE);
    Data createFutureElasticIpData;
    getPlanDao().save(dummyPlanForFutureElasticIpStep);
    try {
      waitForStepPerformDone(futureElasticIpStep);
      createFutureElasticIpData = futureElasticIpStep.perform().getData();
    } catch (final Throwable t) {
      waitForStepRollbackDone(futureElasticIpStep);
      throw t;
    }

    getHardwareDao()
        .setInstanceFields(
            replicaSetHardware.getId(),
            instanceHardware.getInstanceId(),
            false,
            Arrays.asList(
                Pair.of(
                    AWSInstanceHardware.FieldDefs.PAST_EIP_ID,
                    createPastElasticIpData.getElasticIpId()),
                Pair.of(
                    AWSInstanceHardware.FieldDefs.PAST_PUBLIC_IP,
                    createPastElasticIpData.getElasticIp()),
                Pair.of(
                    AWSInstanceHardware.FieldDefs.FUTURE_EIP_ID,
                    createFutureElasticIpData.getElasticIpId()),
                Pair.of(
                    AWSInstanceHardware.FieldDefs.FUTURE_PUBLIC_IP,
                    createFutureElasticIpData.getElasticIp())));

    // Create test ENI for orphaned item testing
    // Use existing SLS ENI if available, otherwise create a new test ENI with separate VPC/subnet
    // This future proofs against the instance hardware becoming a disagg instance
    final TestIpv6Resources ipv6Resources;

    if (instanceHardware.getSlsEniId().isPresent()) {
      // Use existing SLS ENI
      ipv6Resources =
          new TestIpv6Resources(instanceHardware.getSlsEniId().orElseThrow(), null, null);
    } else {
      // Create new test ENI with VPC/subnet
      ipv6Resources = createTestEni(container);
    }
    final String testEniId = ipv6Resources.eniId();

    // Refresh the instance hardware so it has past/future ip on it
    replicaSetHardware =
        getReplicaSetHardwareForInstance(
            getClusterDescription().getName(), getInstanceIds().get(0));
    instanceHardware =
        (AWSInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).orElseThrow();

    // AWSAdminBackupSnapshotMove need to be run after provisionContainerMove because we need
    // cluster, instanceHardware to be present.
    final Plan snapshotPlan = new Plan(getGroup().getId(), getPlanContextFactory());
    final NDSAdminJob ndsAdminJob =
        getNDSAdminJobDao()
            .create(
                snapshotPlan.getId(),
                getGroupId(),
                getClusterDescription().getName(),
                instanceHardware.getHostnameForAgents().orElseThrow(),
                NDSAdminJob.Type.TAKE_ADMIN_BACKUP_SNAPSHOT);

    getNDSAdminBackupSnapshotSvc()
        .insertAdminBackupSnapshot(
            getGroupId(),
            getClusterDescription().getName(),
            getInstanceHardware(
                getGroupId(), getClusterDescription().getName(), getInstanceIds().get(0)),
            getHostname(getClusterDescription(), getInstanceIds()),
            globalAtlasOperatorUser,
            "comment");
    AWSAdminBackupSnapshotMove adminBackupSnapshotMove =
        AWSAdminBackupSnapshotMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            ndsAdminJob.getId());
    snapshotPlan.addMove(adminBackupSnapshotMove);
    getPlanDao().save(snapshotPlan);

    try {
      waitForPlanPerformSuccess(snapshotPlan);
    } catch (final Throwable t) {
      waitForPlanRollbackSuccess(snapshotPlan);
      throw t;
    }

    // As per AWS docs: "It's a best practice to wait 15 seconds between create-snapshot API
    // requests for the same volume."
    // Otherwise, we see the following error message: "The maximum per volume CreateSnapshot request
    // rate has been exceeded. Use an increasing or variable sleep interval between requests."
    Thread.sleep(Duration.ofSeconds(15).toMillis());

    final Plan dummyPlanForSnapshotAndOrphanVolumeStep =
        new Plan(getGroup().getId(), getPlanContextFactory());
    dummyPlanForSnapshotAndOrphanVolumeStep.addMove(new DummyMove());
    final Date orphanCreationDate = new Date();
    final AWSSnapshotAndOrphanVolumeStep snapshotAndOrphanVolumeStep =
        getSnapshotAndOrphanVolumeStepForPlan(
            dummyPlanForSnapshotAndOrphanVolumeStep, orphanCreationDate, getGroupDao());
    // for AWS, if we attempt to delete the volume while a snapshot is still creating, the volume
    // can be stuck in Deleting for a long time. thus to reduce test noise, let's wait for the
    // snapshot to complete before attempting.
    try {
      waitForStepPerformDone(snapshotAndOrphanVolumeStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(snapshotAndOrphanVolumeStep);
      throw t;
    }

    final List<NDSOrphanedItem> orphanedDataSnapshots =
        _dao.findByCloudProviderAndType(CloudProvider.AWS, DATA_SNAPSHOT);
    assertEquals(1, orphanedDataSnapshots.size());
    final AWSOrphanedItem orphanedDataSnapshot = (AWSOrphanedItem) orphanedDataSnapshots.get(0);
    waitEBSSnapshotCompleted(container, orphanedDataSnapshot.getId());

    // set data snapshot to expire in an hour - the step will likely to choose to add an immediate
    // TTL, which this test shouldn't rely on
    _dao.extendItem(orphanedDataSnapshot, DateUtils.addHours(new Date(), 1));

    final List<NDSOrphanedItem> orphanedEbsVolumes =
        _dao.findByCloudProviderAndType(CloudProvider.AWS, EBS_VOLUME);
    assertEquals(1, orphanedEbsVolumes.size());
    final AWSOrphanedItem orphanedEbsVolume = (AWSOrphanedItem) orphanedEbsVolumes.get(0);

    final Optional<AWSOrphanedItem> correspondingOrphanedSnapshotOpt =
        _dao.getOrphanedItemByTypeForCorrespondingHost(orphanedEbsVolume, DATA_SNAPSHOT)
            .map(AWSOrphanedItem.class::cast);
    if (correspondingOrphanedSnapshotOpt.isEmpty()) {
      fail("Expected volume to have corresponding snapshot.");
    }

    // as of now, an assumption is corresponding volumes and snapshot must have the same hostname
    // and created date
    // https://wiki.corp.mongodb.com/display/MMS/Orphaned+Item+Handling#OrphanedItemHandling-Expectedbehavioroforphaningitemseligibleforresurrection
    assertEquals(instanceHardware.getHostnameForAgents(), orphanedEbsVolume.getHostname());
    assertEquals(container.getRegion(), orphanedEbsVolume.getRegionName().orElseThrow());
    assertEquals(orphanCreationDate, orphanedEbsVolume.getCreatedDate());
    assertEquals(container.getAWSAccountId(), orphanedEbsVolume.getAWSAccountId());
    assertEquals(container.getVpcId(), orphanedEbsVolume.getAWSVpcId());

    assertEquals(orphanedDataSnapshot.getHostname(), orphanedEbsVolume.getHostname());
    assertEquals(orphanedDataSnapshot.getCreatedDate(), orphanedEbsVolume.getCreatedDate());
    assertEquals(orphanedDataSnapshot.getRegionName(), orphanedEbsVolume.getRegionName());
    assertEquals(orphanedDataSnapshot.getAWSVpcId(), orphanedEbsVolume.getAWSVpcId());
    assertEquals(orphanedDataSnapshot.getAWSAccountId(), orphanedEbsVolume.getAWSAccountId());

    final List<String> subnetIds =
        Arrays.stream(container.getSubnets())
            .map(AWSSubnet::getSubnetId)
            .collect(Collectors.toList());

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.EC2_INSTANCE)
            .setId(instanceHardware.getEC2InstanceId().orElseThrow())
            .setType(AWSOrphanedItem.Type.EC2_INSTANCE)
            .setRegionName(container.getRegion())
            .build());

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.ELASTIC_IP)
            .setId(instanceHardware.getEIPId().orElseThrow())
            .setType(AWSOrphanedItem.Type.ELASTIC_IP)
            .setRegionName(container.getRegion())
            .build());

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.ELASTIC_IP)
            .setId(instanceHardware.getPastEipId().orElseThrow())
            .setType(AWSOrphanedItem.Type.PAST_ELASTIC_IP)
            .setRegionName(container.getRegion())
            .build());

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.ELASTIC_IP)
            .setId(instanceHardware.getFutureEipId().orElseThrow())
            .setType(AWSOrphanedItem.Type.FUTURE_ELASTIC_IP)
            .setRegionName(container.getRegion())
            .build());

    // Add ENI orphaned item for testing - using the ENI created earlier in the test
    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.ENI)
            .setId(testEniId)
            .setType(AWSOrphanedItem.Type.ENI)
            .setRegionName(container.getRegion())
            .build());

    subnetIds.forEach(
        id ->
            _dao.add(
                new AWSOrphanedItem.Builder()
                    .setAWSAccountId(container.getAWSAccountId())
                    .setDurationUntilExpire(AWSOrphanedItem.Expirations.SUBNET)
                    .setId(id)
                    .setType(AWSOrphanedItem.Type.SUBNET)
                    .setRegionName(container.getRegion())
                    .build()));

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSVpcId(container.getVpcId().orElseThrow())
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.INTERNET_GATEWAY)
            .setId(container.getIgwId().orElseThrow())
            .setType(AWSOrphanedItem.Type.INTERNET_GATEWAY)
            .setRegionName(container.getRegion())
            .build());

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(AWSOrphanedItem.Expirations.VPC)
            .setId(container.getVpcId().orElseThrow())
            .setType(AWSOrphanedItem.Type.VPC)
            .setRegionName(container.getRegion())
            .build());

    List<? extends AdminBackupSnapshot> adminBackupSnapshots =
        getAdminBackupSnapshotDao().findAll();

    assertEquals(1, adminBackupSnapshots.size());
    AWSAdminBackupSnapshot awsAdminBackupSnapshot =
        (AWSAdminBackupSnapshot) adminBackupSnapshots.get(0);

    _dao.add(
        new AWSOrphanedItem.Builder()
            .setAWSAccountId(container.getAWSAccountId())
            .setDurationUntilExpire(Duration.ofDays(5))
            .setId(awsAdminBackupSnapshot.getCloudProviderSnapshotId())
            .setType(AWSOrphanedItem.Type.EMERGENCY_SNAPSHOT)
            .setRegionName(container.getRegion())
            .build());

    try {
      // Call clean items to delete the instance first and anything not dependent on the instance
      _svc.cleanItems();
      verifyEC2InstanceDeleted(instanceHardware.getEC2InstanceId().orElseThrow());
      assertTrue(
          _dao.findByIdAndCloudProviderAndType(
                  orphanedDataSnapshot.getId(),
                  CloudProvider.AWS,
                  AWSOrphanedItem.Type.DATA_SNAPSHOT)
              .isPresent());
      assertTrue(
          _dao.findByIdAndCloudProviderAndType(
                  instanceHardware.getNVMePrioritizedEBSId().orElseThrow(),
                  CloudProvider.AWS,
                  AWSOrphanedItem.Type.EBS_VOLUME)
              .isPresent());

      // Call clean items again - EBS volume deleted because data_snapshot is complete
      _svc.cleanItems();
      verifyEBSVolumeDeleted(instanceHardware.getNVMePrioritizedEBSId().orElseThrow());
      assertTrue(
          _dao.findByIdAndCloudProviderAndType(
                  orphanedDataSnapshot.getId(),
                  CloudProvider.AWS,
                  AWSOrphanedItem.Type.DATA_SNAPSHOT)
              .isPresent());
      assertFalse(
          _dao.findByIdAndCloudProviderAndType(
                  instanceHardware.getNVMePrioritizedEBSId().orElseThrow(),
                  CloudProvider.AWS,
                  AWSOrphanedItem.Type.EBS_VOLUME)
              .isPresent());

      // Orphaned Data Snapshot is expired
      _dao.expireItem(orphanedDataSnapshot);

      // Call clean items again - snapshot is deleted
      _svc.cleanItems();
      verifySnapshotDeleted(orphanedDataSnapshot.getId());
      assertFalse(
          _dao.findByIdAndCloudProviderAndType(
                  orphanedDataSnapshot.getId(),
                  CloudProvider.AWS,
                  AWSOrphanedItem.Type.DATA_SNAPSHOT)
              .isPresent());

      // Then everything else
      _svc.cleanItems();
      verifyElasticIpDeleted(instanceHardware.getEIPId().orElseThrow());
      verifyElasticIpDeleted(instanceHardware.getPastEipId().orElseThrow());
      verifyElasticIpDeleted(instanceHardware.getFutureEipId().orElseThrow());
      verifyEniDeleted(testEniId);
      verifySubnetsDeleted(subnetIds);
      verifyInternetGatewayDeleted(container.getIgwId().orElseThrow());
      verifyVpcDeleted(container.getVpcId().orElseThrow());
      verifySnapshotDeleted(awsAdminBackupSnapshot.getEbsSnapshotId());
      verifyAdminSnapshotMarkAsDeleted(awsAdminBackupSnapshot.getId());

      // Clean up the test IPv6 resources (VPC/subnet) that were created specifically for this test
      cleanupIpv6Resources(ipv6Resources);
    } catch (final Throwable t) {
      cleanupEC2Instance(instanceHardware.getEC2InstanceId().orElseThrow());
      cleanupEBSVolume(instanceHardware.getNVMePrioritizedEBSId().orElseThrow());
      cleanupElasticIp(instanceHardware.getEIPId().orElseThrow());
      cleanupElasticIp(instanceHardware.getPastEipId().orElseThrow());
      cleanupElasticIp(instanceHardware.getFutureEipId().orElseThrow());
      cleanupIpv6Resources(ipv6Resources);
      cleanupSubnets(subnetIds);
      cleanupInternetGateway(container.getIgwId().orElseThrow());
      cleanupVpc(container.getVpcId().orElseThrow());
      cleanUpSnapshot(awsAdminBackupSnapshot.getEbsSnapshotId());
      cleanUpSnapshot(orphanedDataSnapshot.getId());
      throw t;
    } finally {
      cleanupDNSRecord(instanceHardware.getHostnameForAgents().orElseThrow());
    }
  }

  private AWSSnapshotAndOrphanVolumeStep getSnapshotAndOrphanVolumeStepForPlan(
      final Plan pPlan, final Date pOrphanedItemCreatedDate, final GroupDao pGroupDao) {
    final NDSPlanContext planContext = (NDSPlanContext) pPlan.getPlanContext();
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));

    final AWSInstanceHardware instanceHardware =
        (AWSInstanceHardware) replicaSetHardware.getById(getInstanceIds().get(0)).orElseThrow();
    return new AWSSnapshotAndOrphanVolumeStep(
        planContext,
        new Step.State(pPlan.getId(), pPlan.getMoves().get(0).getId(), 0, getPlanDao()),
        getAWSApiSvc(),
        _svc,
        getContainer(),
        instanceHardware.getNVMePrioritizedEBSId().orElseThrow(),
        getClusterDescription(),
        instanceHardware,
        pOrphanedItemCreatedDate,
        pGroupDao,
        replicaSetHardware);
  }

  private AWSCreateElasticIpStep getCreateElasticIpStepForPlan(
      final Plan pPlan, final ElasticIpState pElasticIpState) {
    final NDSPlanContext planContext = (NDSPlanContext) pPlan.getPlanContext();

    return new AWSCreateElasticIpStep(
        planContext,
        new Step.State(pPlan.getId(), pPlan.getMoves().get(0).getId(), 0, getPlanDao()),
        getContainer(),
        getOrphanedItemDao(),
        getAWSApiSvc(),
        getAwsApiSvcV2(),
        _svc,
        _awsIpamPoolSvc,
        getGroupDao(),
        getClusterDescription().getCreateDate(),
        ResourceOperationTagType.NONE,
        pElasticIpState,
        false,
        false);
  }

  /**
   * Creates a test ENI for integration testing purposes by creating a separate IPv6 VPC and subnet
   *
   * @return TestIpv6Resources containing the ENI, VPC, and subnet IDs
   */
  private TestIpv6Resources createTestEni(final AWSCloudProviderContainer container) {
    try {
      // Create a separate IPv6 VPC for ENI testing
      final Vpc vpc =
          getAWSApiSvc()
              .createVpcWithIpv6(
                  container.getAWSAccountId(), container.getRegion(), getLogger(), "10.0.0.0/16");

      // Wait for VPC to be available with IPv6 CIDR block
      final boolean vpcReady =
          waitForCondition(
              java.time.Duration.ofMinutes(3),
              () -> {
                final Vpc refreshedVpc =
                    getAWSApiSvc()
                        .findVpc(
                            container.getAWSAccountId(),
                            container.getRegion(),
                            getLogger(),
                            vpc.getVpcId());
                return refreshedVpc.getState().equals(VpcState.Available.toString())
                    && !refreshedVpc.getIpv6CidrBlockAssociationSet().isEmpty();
              });

      if (!vpcReady) {
        throw new RuntimeException("VPC failed to become available with IPv6 CIDR block");
      }

      final Vpc refreshedVpc =
          getAWSApiSvc()
              .findVpc(
                  container.getAWSAccountId(), container.getRegion(), getLogger(), vpc.getVpcId());

      // Get first availability zone
      final String availabilityZone =
          getAWSApiSvc()
              .findAvailabilityZones(
                  container.getAWSAccountId(), container.getRegion(), getLogger())
              .get(0)
              .getZoneName();

      // Create IPv6 subnet
      final Subnet subnet =
          getAWSApiSvc()
              .createIpv6Subnet(
                  container.getAWSAccountId(),
                  container.getRegion(),
                  getLogger(),
                  refreshedVpc.getVpcId(),
                  availabilityZone,
                  refreshedVpc.getIpv6CidrBlockAssociationSet().get(0).getIpv6CidrBlock());

      // Find default security group for the VPC
      final String securityGroupId =
          getAWSApiSvc()
              .findDefaultSecurityGroupForVpc(
                  container.getAWSAccountId(), container.getRegion(), getLogger(), vpc.getVpcId())
              .orElseThrow()
              .getGroupId();

      // Create IPv6 ENI
      final NetworkInterface eni =
          getAWSApiSvc()
              .createIpv6Eni(
                  container.getAWSAccountId(),
                  container.getRegion(),
                  getLogger(),
                  subnet.getSubnetId(),
                  securityGroupId,
                  "test-eni-for-orphaned-item-integration-test",
                  List.of());

      return new TestIpv6Resources(
          eni.getNetworkInterfaceId(), vpc.getVpcId(), subnet.getSubnetId());
    } catch (Exception e) {
      throw new RuntimeException("Failed to create test ENI", e);
    }
  }

  /**
   * Helper record to hold test IPv6 resources (ENI, VPC, and subnet).
   *
   * @param eniId ENI ID (required, never null)
   * @param vpcId VPC ID (nullable - null when using existing SLS ENI)
   * @param subnetId Subnet ID (nullable - null when using existing SLS ENI)
   */
  private record TestIpv6Resources(
      String eniId, @Nullable String vpcId, @Nullable String subnetId) {}

  /** Verifies that the ENI has been deleted */
  private void verifyEniDeleted(final String eniId) {
    try {
      final var interfaces =
          getAWSApiSvc()
              .findNetworkInterfaces(
                  getContainer().getAWSAccountId(),
                  getContainer().getRegion(),
                  getLogger(),
                  List.of(eniId));
      if (!interfaces.isEmpty()) {
        fail("Expected ENI " + eniId + " to be deleted, but it still exists");
      }
    } catch (final Exception e) {
      // Expected - ENI should not be found
      getLogger().info("ENI {} successfully deleted", eniId);
    }
  }

  /** Cleanup ENI and associated test resources if they still exist (for test failure cases) */
  private void cleanupIpv6Resources(final TestIpv6Resources ipv6Resources) {
    if (ipv6Resources == null || ipv6Resources.eniId() == null) {
      return;
    }

    try {
      // Cleanup ENI first
      getAWSApiSvc()
          .deleteEni(
              getContainer().getAWSAccountId(),
              getContainer().getRegion(),
              getLogger(),
              ipv6Resources.eniId());
    } catch (final Exception e) {
      // Ignore cleanup failures
      getLogger().warn("Failed to cleanup ENI {}: {}", ipv6Resources.eniId(), e.getMessage());
    }

    if (ipv6Resources.subnetId() != null) {
      try {
        // Cleanup subnet
        getAWSApiSvc()
            .deleteSubnet(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                ipv6Resources.subnetId());
      } catch (final Exception e) {
        // Ignore cleanup failures
        getLogger()
            .warn("Failed to cleanup subnet {}: {}", ipv6Resources.subnetId(), e.getMessage());
      }
    }

    if (ipv6Resources.vpcId() != null) {
      try {
        // Cleanup VPC
        getAWSApiSvc()
            .deleteVpc(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                ipv6Resources.vpcId());
      } catch (final Exception e) {
        // Ignore cleanup failures
        getLogger().warn("Failed to cleanup VPC {}: {}", ipv6Resources.vpcId(), e.getMessage());
      }
    }
  }
}
