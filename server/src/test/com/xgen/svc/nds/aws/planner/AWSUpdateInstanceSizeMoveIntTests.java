package com.xgen.svc.nds.aws.planner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.CapacityReservation;
import com.amazonaws.services.ec2.model.Filter;
import com.amazonaws.services.ec2.model.Instance;
import com.amazonaws.services.ec2.model.Volume;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.planner.NDSPlanContext;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.junit.Test;

public class AWSUpdateInstanceSizeMoveIntTests extends AWSExternalIntTest {

  public void testAWSUpdateInstanceSizeInternal(final AWSUpdateInstanceSizeMove pMove)
      throws InterruptedException {
    final ReplicaSetHardware initialReplicaSetHardware =
        getReplicaSetHardwareForInstance(
            getClusterDescription().getName(), getInstanceIds().get(0));
    final AWSInstanceHardware initialHardware =
        (AWSInstanceHardware) initialReplicaSetHardware.getById(getInstanceIds().get(0)).get();

    Result<?> result = pMove.perform();

    boolean capacityReservationUsedChecked = false;
    while (!result.getStatus().isDone()) {
      assertNotFailed(pMove.getClass().getSimpleName(), result);
      Thread.sleep(Duration.ofSeconds(5).toMillis());
      result = pMove.perform();
      if (pMove.instanceSizeUpdated()
          && !result.getStatus().isDone()
          && !capacityReservationUsedChecked) {
        // Make sure the updated instance is using the capacity reservation
        final List<CapacityReservation> crs =
            getAWSApiSvc()
                .getCapacityReservations(
                    getContainer().getAWSAccountId(),
                    getContainer().getRegion(),
                    List.of(
                        new Filter(
                            "tag:instanceHostname",
                            List.of(initialHardware.getHostnameForAgents().get()))),
                    pMove.getContext().getLogger());

        assertEquals(1, crs.size());
        assertEquals("active", crs.get(0).getState());
        assertEquals(0, crs.get(0).getAvailableInstanceCount().intValue());
        capacityReservationUsedChecked = true;
      }
    }

    final ReplicaSetHardware updatedReplicaSetHardware =
        getReplicaSetHardwareForInstance(
            getClusterDescription().getName(), getInstanceIds().get(0));
    final AWSInstanceHardware updatedHardware =
        (AWSInstanceHardware) updatedReplicaSetHardware.getById(getInstanceIds().get(0)).get();

    assertThat(
        updatedHardware.getLastInstanceSizeModifyDate().get(),
        greaterThan(initialHardware.getLastInstanceSizeModifyDate().get()));

    final Volume ebsVolume =
        getAWSApiSvc()
            .findEBSVolume(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pMove.getContext().getLogger(),
                updatedHardware.getNVMePrioritizedEBSId().get());

    final Instance ec2Instance =
        getAWSApiSvc()
            .findEC2Instance(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pMove.getContext().getLogger(),
                updatedHardware.getEC2InstanceId().get());

    assertEquals(ebsVolume.getVolumeId(), updatedHardware.getNVMePrioritizedEBSId().get());
    assertEquals(ec2Instance.getInstanceId(), updatedHardware.getEC2InstanceId().get());

    final InstanceSize instanceSize = updatedHardware.getNDSInstanceSize().orElseThrow();
    final InstanceFamily instanceFamily =
        getClusterDescription().getInstanceFamily(NodeType.ELECTABLE);

    assertEquals(
        ec2Instance.getInstanceType(), instanceSize.getInstanceFamilies().get(instanceFamily));

    final List<CapacityReservation> crs =
        getAWSApiSvc()
            .getCapacityReservations(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                List.of(
                    new Filter(
                        "tag:instanceHostname",
                        List.of(initialHardware.getHostnameForAgents().get()))),
                pMove.getContext().getLogger());

    assertEquals(1, crs.size());
    assertEquals("cancelled", crs.get(0).getState());
  }

  @Test(timeout = 25 * 60 * 1000L)
  public void testAWSUpdateInstanceSizeMove() throws InterruptedException {
    final Plan plan = setupPlanWithContainerAndSingleInstance(AWSNDSInstanceSize.M10);

    // Setup the clusterDescription
    final ClusterDescription original = getClusterDescription();
    final ClusterDescription clusterDescription =
        original
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M20))
            .build();

    final Plan plan2 = new Plan(getGroup().getId(), getPlanContextFactory());

    final AWSUpdateInstanceSizeMove updateInstanceSizeMove =
        AWSUpdateInstanceSizeMove.factoryCreate(
            (NDSPlanContext) plan2.getPlanContext(),
            getTags(),
            clusterDescription.getName(),
            getInstanceIds().get(0),
            false);

    plan2.addMove(updateInstanceSizeMove);
    getPlanDao().save(plan2);

    // Verify that cloud provider and region are added into move tags
    final Optional<Plan> savedPlan = getPlanDao().find(plan2.getId());
    assertTrue(savedPlan.isPresent());
    assertEquals(
        getTags(), savedPlan.get().getMoveById(updateInstanceSizeMove.getId()).get().getTags());

    try {
      waitForPlanPerformSuccess(plan);
      getClusterDescriptionDao().save(clusterDescription);
      testAWSUpdateInstanceSizeInternal(updateInstanceSizeMove);
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }
}
