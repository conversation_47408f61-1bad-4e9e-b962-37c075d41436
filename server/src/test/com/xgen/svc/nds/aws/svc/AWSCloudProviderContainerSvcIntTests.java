package com.xgen.svc.nds.aws.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSCloudProviderContainerDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSPhysicalZoneId;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AWSCloudProviderContainerSvcIntTests extends JUnit5BaseSvcTest {

  @Inject AWSCloudProviderContainerSvc _svc;

  @Inject AWSCloudProviderContainerDao _containerDao;

  @Inject NDSClusterSvc _clusterSvc;

  @Inject NDSGroupSvc _groupSvc;

  @Inject NDSGroupDao _groupDao;
  @Inject NDSGroupSvc _ndsGroupSvc;

  @Inject AWSAccountDao _accountDao;

  @Inject AppSettings _appSettings;

  @Inject AuditSvc _auditSvc;

  private AWSAccount _account;

  @BeforeEach
  public void setup() {
    _account = new AWSAccount(NDSModelTestFactory.getFullyAvailableAWSAccount());
    _accountDao.save(_account);
  }

  @Test
  public void testUpdateRegion() throws SvcException {
    final ObjectId groupId = new ObjectId();
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);

    _svc.getCloudContainerId(
        _groupDao.find(groupId).get(), AWSRegionName.US_EAST_1, NDSDefaults.ATLAS_CIDR);

    final AWSCloudProviderContainer existing =
        (AWSCloudProviderContainer)
            _groupDao.find(groupId).get().getCloudProviderContainers().get(0);

    // Update should work because container is unprovisioned
    _svc.updateContainer(_groupDao.find(groupId).get(), existing, AWSRegionName.US_WEST_1, null);

    final AWSCloudProviderContainer updatedContainer =
        (AWSCloudProviderContainer)
            _groupDao.find(groupId).get().getCloudProviderContainers().get(0);

    // Region is updated
    assertEquals(AWSRegionName.US_WEST_1, updatedContainer.getRegion());

    // Add another container to US_EAST_1
    final ObjectId containerId2 =
        _svc.getCloudContainerId(_groupDao.find(groupId).get(), AWSRegionName.US_EAST_1, null);

    // Force container2 to be provisioned
    _containerDao.setProvisionedFields(
        groupId, containerId2, "", "", Collections.singletonList(AWSSubnet.empty()));

    // Try updating container1 to the same region as container2 should fail because container2 is
    // provisioned
    try {
      _svc.updateContainer(
          _groupDao.find(groupId).get(), updatedContainer, AWSRegionName.US_EAST_1, null);
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.EXISTING_CONTAINER_IN_REGION, e.getErrorCode());
    }

    // Container2 is now unprovisioned
    _containerDao.unsetProvisionedFields(groupId, containerId2);

    // Updating container1 to the same region as container2 should now succeed
    _svc.updateContainer(
        _groupDao.find(groupId).get(), updatedContainer, AWSRegionName.US_EAST_1, null);

    // container2 should now be deleted
    assertTrue(_groupDao.find(groupId).get().getCloudProviderContainer(containerId2).isEmpty());

    // container should be updated to US_EAST_1
    assertEquals(
        AWSRegionName.US_EAST_1,
        ((AWSCloudProviderContainer)
                _groupDao.find(groupId).get().getCloudProviderContainers().get(0))
            .getRegion());

    // The group is only assigned to US_EAST_1
    final AWSAccount account = _accountDao.findByGroupId(groupId).get(0);
    assertTrue(
        account.getRegions().stream()
            .filter(r -> r.getGroupIds().contains(groupId))
            .allMatch(r -> r.getName().equals(AWSRegionName.US_EAST_1)));
    assertEquals(
        1, account.getRegions().stream().filter(r -> r.getGroupIds().contains(groupId)).count());
  }

  @Test
  public void testUpdateRegion_MixedCNStandardRegions() throws SvcException {
    final List<AWSRegionName> excludedRegions =
        List.of(AWSRegionName.AP_NORTHEAST_3, AWSRegionName.AP_SOUTHEAST_3, AWSRegionName.GLOBAL);

    final NDSGroup standardGroup = _groupSvc.ensureGroup(new ObjectId());
    assertFalse(standardGroup.useCNRegionsOnly());

    _svc.getCloudContainerId(standardGroup, AWSRegionName.US_EAST_1, NDSDefaults.ATLAS_CIDR);

    AWSNDSDefaults.CN_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    standardGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(standardGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China containers.", pE.getMessage());
              }
            });
    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    standardGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(standardGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });

    final NDSGroup cnRegionsOnlyGroup =
        _groupSvc.ensureGroup(new ObjectId(), true, RegionUsageRestrictions.NONE, false);
    assertTrue(cnRegionsOnlyGroup.useCNRegionsOnly());

    _svc.getCloudContainerId(cnRegionsOnlyGroup, AWSRegionName.CN_NORTH_1, NDSDefaults.ATLAS_CIDR);

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    cnRegionsOnlyGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(cnRegionsOnlyGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China containers.", pE.getMessage());
              }
            });
    AWSNDSDefaults.CN_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    cnRegionsOnlyGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(cnRegionsOnlyGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });
  }

  @Test
  public void testUpdateRegion_MixedGovCommercialRegions() throws SvcException {
    _appSettings.setProp(Fields.NDS_GOV_US_ENABLED.value, "true", SettingType.MEMORY);

    final List<AWSRegionName> excludedRegions =
        List.of(AWSRegionName.AP_NORTHEAST_3, AWSRegionName.AP_SOUTHEAST_3, AWSRegionName.GLOBAL);

    final NDSGroup commercialGroup = _groupSvc.ensureGroup(new ObjectId());
    assertFalse(commercialGroup.getRegionUsageRestrictions().isGovRegionsOnly());

    _svc.getCloudContainerId(commercialGroup, AWSRegionName.US_EAST_1, NDSDefaults.ATLAS_CIDR);

    AWSNDSDefaults.GOV_CLOUD_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    commercialGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(commercialGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial containers.",
                    pE.getMessage());
              }
            });
    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    commercialGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(commercialGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });

    final NDSGroup govRegionsOnlyGroup =
        _groupSvc.ensureGroup(
            new ObjectId(), false, RegionUsageRestrictions.GOV_REGIONS_ONLY, false);
    assertTrue(govRegionsOnlyGroup.getRegionUsageRestrictions().isGovRegionsOnly());

    _svc.getCloudContainerId(
        govRegionsOnlyGroup, AWSRegionName.US_GOV_WEST_1, NDSDefaults.ATLAS_CIDR);

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    govRegionsOnlyGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(govRegionsOnlyGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial containers.",
                    pE.getMessage());
              }
            });
    AWSNDSDefaults.GOV_CLOUD_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.updateContainer(
                    govRegionsOnlyGroup,
                    (AWSCloudProviderContainer)
                        _groupDao
                            .find(govRegionsOnlyGroup.getGroupId())
                            .get()
                            .getCloudProviderContainers()
                            .get(0),
                    r,
                    null);
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });
  }

  @Test
  public void testGetCloudContainerId_NoAWSContainers() throws Exception {
    final ObjectId groupId = new ObjectId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    // Create a new container in empty account
    final ObjectId containerId1 =
        _svc.getCloudContainerId(group, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertNotNull(containerId1);

    final NDSGroup group2 = _groupSvc.ensureGroup(groupId);

    // Get the same container
    final ObjectId containerId2 =
        _svc.getCloudContainerId(group2, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertNotNull(containerId2);

    assertEquals(containerId1, containerId2);

    final NDSGroup groupAfter = _groupSvc.ensureGroup(groupId);
    assertEquals(
        AWSRegionName.US_EAST_1,
        ((AWSCloudProviderContainer) groupAfter.getCloudProviderContainer(containerId1).get())
            .getRegion());
    assertEquals(
        "10.0.0.0/21",
        ((AWSCloudProviderContainer) groupAfter.getCloudProviderContainer(containerId1).get())
            .getAtlasCidr());
    assertNotNull(
        ((AWSCloudProviderContainer) groupAfter.getCloudProviderContainer(containerId1).get())
            .getAWSAccountId());
  }

  @Test
  public void testGetCloudContainerId_NoAWSContainers_InvalidCIDR() throws Exception {
    final ObjectId groupId = new ObjectId();

    // Creating outside of /21 not allowed
    try {
      _svc.getCloudContainerId(
          _groupSvc.ensureGroup(groupId), AWSRegionName.US_EAST_1, "10.0.0.0/16");
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ATLAS_CIDR_BLOCK, e.getErrorCode());
    }

    final ObjectId awsAccountId =
        _accountDao.assignRegionToGroup(AWSRegionName.US_EAST_1, groupId).get();
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(awsAccountId, AWSRegionName.US_EAST_1, "10.0.0.0/16");
    _groupDao.addCloudContainer(groupId, container);

    // If there is already an existing /16 getting that again is allowed
    _svc.getCloudContainerId(
        _groupSvc.ensureGroup(groupId), AWSRegionName.US_EAST_1, "10.0.0.0/16");

    // Creating a different container with a bad CIDR is not allowed
    try {
      _svc.getCloudContainerId(
          _groupSvc.ensureGroup(groupId), AWSRegionName.EU_WEST_1, "10.0.0.0/18");
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ATLAS_CIDR_BLOCK, e.getErrorCode());
    }
  }

  @Test
  public void testGetCloudContainerId_UpdateExisting() throws Exception {
    final ObjectId groupId = new ObjectId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    // Create a new container in empty account
    final ObjectId containerId1 =
        _svc.getCloudContainerId(group, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertNotNull(containerId1);

    final NDSGroup group2 = _groupSvc.ensureGroup(groupId);

    // Get a container for different region/cidr, since the first container is unused
    final ObjectId containerId2 =
        _svc.getCloudContainerId(group2, AWSRegionName.EU_WEST_1, "********/21");
    assertNotNull(containerId2);

    assertNotEquals(containerId1, containerId2);

    final NDSGroup groupAfter = _groupSvc.ensureGroup(groupId);

    final AWSCloudProviderContainer container1 =
        (AWSCloudProviderContainer) groupAfter.getCloudProviderContainer(containerId1).get();
    final AWSCloudProviderContainer container2 =
        (AWSCloudProviderContainer) groupAfter.getCloudProviderContainer(containerId2).get();

    assertEquals(AWSRegionName.US_EAST_1, container1.getRegion());
    assertEquals("10.0.0.0/21", container1.getAtlasCidr());
    assertEquals(AWSRegionName.EU_WEST_1, container2.getRegion());
    assertEquals("********/21", container2.getAtlasCidr());
    assertNotNull(container1.getAWSAccountId());
    assertNotNull(container2.getAWSAccountId());
    assertEquals(container1.getAWSAccountId(), container2.getAWSAccountId());

    // Update existing container
    final ObjectId containerId3 =
        _svc.getCloudContainerId(groupAfter, AWSRegionName.US_EAST_1, "10.2.0.0/21");
    assertEquals(containerId1, containerId3);

    final NDSGroup groupAfter2 = _groupSvc.ensureGroup(groupId);
    final AWSCloudProviderContainer container3 =
        (AWSCloudProviderContainer) groupAfter2.getCloudProviderContainer(containerId3).get();

    assertEquals(AWSRegionName.US_EAST_1, container3.getRegion());
    assertEquals("10.2.0.0/21", container3.getAtlasCidr());
    assertNotNull(container3.getAWSAccountId());
    assertEquals(container1.getAWSAccountId(), container3.getAWSAccountId());
  }

  @Test
  public void testGetCloudContainerId_ExistingUpdateDateChanged() throws Exception {
    final ObjectId groupId = new ObjectId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    // Create a new container in empty account
    final ObjectId containerId1 =
        _svc.getCloudContainerId(group, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertNotNull(containerId1);

    final NDSGroup group2 = _groupSvc.ensureGroup(groupId);
    _groupDao.touchCloudContainer(groupId, containerId1, new Date(10));

    // Try to change the cidr for the container
    try {
      _svc.getCloudContainerId(group2, AWSRegionName.US_EAST_1, "********/21");
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INTERNAL, e.getErrorCode());
    }
  }

  @Test
  public void testGetCloudContainerId_ExistingInUse() throws Exception {
    final ObjectId groupId = new ObjectId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    // Create a new container in empty account
    final ObjectId containerId1 =
        _svc.getCloudContainerId(group, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertNotNull(containerId1);
    _containerDao.setProvisionedFields(
        groupId,
        containerId1,
        "vpc-1",
        "igw-1",
        Collections.singletonList(
            AWSSubnet.builder()
                .subnetId("subnet-1")
                .availabilityZone("a")
                .zoneId(new AWSPhysicalZoneId("zone1"))
                .build()));

    final NDSGroup group2 = _groupSvc.ensureGroup(groupId);

    // Create a container for different region/cidr, even though the first container is in use
    final ObjectId containerId2 =
        _svc.getCloudContainerId(group2, AWSRegionName.EU_WEST_1, "********/21");
    assertNotNull(containerId2);
    assertNotEquals(containerId1, containerId2);

    final NDSGroup group3 = _groupSvc.ensureGroup(groupId);

    try {
      _svc.getCloudContainerId(group3, AWSRegionName.US_EAST_1, "********/21");
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.OVERLAPPING_EXISTING_ATLAS_CIDR_BLOCK, e.getErrorCode());
    }

    try {
      _svc.getCloudContainerId(group3, AWSRegionName.US_EAST_1, "192.168.1.1/21");
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.CONTAINER_IN_USE, e.getErrorCode());
    }

    // The region matches and we don't care about the CIDR, so return the original container
    final ObjectId containerId3 = _svc.getCloudContainerId(group3, AWSRegionName.US_EAST_1, null);
    assertEquals(containerId3, containerId1);
  }

  @Test
  public void testGetCloudContainerId_ExistingClusters() throws Exception {
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    final Group mmsGroup = MmsFactory.createGroup(organization);
    MmsFactory.createUser(mmsGroup);

    final ObjectId groupId = mmsGroup.getId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    // Create a new container in empty account
    final ObjectId containerId1 =
        _svc.getCloudContainerId(group, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertNotNull(containerId1);

    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setGroupId(groupId)
            .build();

    _clusterSvc.createDefaultProcessArgs(
        mmsGroup, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);

    final NDSGroup group2 = _groupSvc.ensureGroup(groupId);

    // Create a container for different region/cidr, even though the first container has existing
    // clusters.
    final ObjectId containerId2 =
        _svc.getCloudContainerId(group2, AWSRegionName.EU_WEST_1, "********/21");
    assertNotNull(containerId2);
    assertNotEquals(containerId1, containerId2);

    // The region matches and we don't care about the CIDR, so return the original container
    final ObjectId containerId3 = _svc.getCloudContainerId(group2, AWSRegionName.US_EAST_1, null);
    assertEquals(containerId1, containerId3);
  }

  @Test
  public void testGetCloudContainerId_MixedCNStandardRegions() throws SvcException {
    final List<AWSRegionName> excludedRegions =
        List.of(AWSRegionName.AP_NORTHEAST_3, AWSRegionName.AP_SOUTHEAST_3, AWSRegionName.GLOBAL);

    final NDSGroup standardGroup = _groupSvc.ensureGroup(new ObjectId());
    assertFalse(standardGroup.useCNRegionsOnly());

    AWSNDSDefaults.CN_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(standardGroup, r, "10.0.0.0/21");
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China containers.", pE.getMessage());
              }
            });
    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(standardGroup, r, "10.0.0.0/21");
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });

    final NDSGroup cnRegionsOnlyGroup =
        _groupSvc.ensureGroup(new ObjectId(), true, RegionUsageRestrictions.NONE, false);
    assertTrue(cnRegionsOnlyGroup.useCNRegionsOnly());

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(cnRegionsOnlyGroup, r, "10.0.0.0/21");
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China containers.", pE.getMessage());
              }
            });
    AWSNDSDefaults.CN_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(cnRegionsOnlyGroup, r, "10.0.0.0/21");
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });
  }

  @Test
  public void testGetCloudContainerId_MixedGovCommercialRegions() throws SvcException {
    _appSettings.setProp(Fields.NDS_GOV_US_ENABLED.value, "true", SettingType.MEMORY);

    final List<AWSRegionName> excludedRegions =
        List.of(AWSRegionName.AP_NORTHEAST_3, AWSRegionName.AP_SOUTHEAST_3, AWSRegionName.GLOBAL);

    final NDSGroup commercialGroup = _groupSvc.ensureGroup(new ObjectId());
    assertFalse(commercialGroup.getRegionUsageRestrictions().isGovRegionsOnly());

    AWSNDSDefaults.GOV_CLOUD_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(commercialGroup, r, "10.0.0.0/21");
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial containers.",
                    pE.getMessage());
              }
            });
    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(commercialGroup, r, "10.0.0.0/21");
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });

    final NDSGroup govRegionsOnlyGroup =
        _groupSvc.ensureGroup(
            new ObjectId(), false, RegionUsageRestrictions.GOV_REGIONS_ONLY, false);
    assertTrue(govRegionsOnlyGroup.getRegionUsageRestrictions().isGovRegionsOnly());

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion() && !excludedRegions.contains(r))
        .filter(r -> !r.isUSGovRegion())
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(govRegionsOnlyGroup, r, "10.0.0.0/21");
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial containers.",
                    pE.getMessage());
              }
            });
    AWSNDSDefaults.GOV_CLOUD_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _svc.getCloudContainerId(govRegionsOnlyGroup, r, "10.0.0.0/21");
              } catch (final SvcException pE) {
                fail("Did not expect SvcException");
              }
            });
  }

  @Test
  public void testAddAWSCloudContainer_ExistingContainer_MultipleAWSAccounts() throws Exception {
    // Create second available AWS account
    _accountDao.save(new AWSAccount(NDSModelTestFactory.getFullyAvailableAWSAccount()));

    final ObjectId groupId = new ObjectId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    // Add cloud container twice for same group/region; make sure same container id is returned
    final ObjectId containerId =
        _svc.addAWSCloudContainer(group, AWSRegionName.US_EAST_1, "10.0.0.0/21");
    assertEquals(
        containerId, _svc.addAWSCloudContainer(group, AWSRegionName.US_EAST_1, "10.0.0.0/21"));

    // Make sure only one container has been created
    final NDSGroup updatedGroup = _groupSvc.find(groupId).get();
    assertEquals(1, updatedGroup.getCloudProviderContainers().size());

    // Make sure there is only one AWS account entry for the group
    assertEquals(1, _accountDao.findByGroupId(groupId).size());
  }

  @Test
  public void testDeleteUnusedContainer() throws Exception {
    final ObjectId groupId = new ObjectId();
    _groupDao.create(
        groupId,
        new NDSManagedX509(),
        false,
        RegionUsageRestrictions.NONE,
        false,
        new Limits(),
        NDSDefaults.generateDnsPinPerGroup());
    _accountDao.assignRegionToGroup(AWSRegionName.US_EAST_1, groupId);

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getUnprovisionedAWSContainer(
                AWSRegionName.US_EAST_1, _account.getId()));
    _groupDao.addCloudContainerWithId(groupId, container, container.getId());

    _svc.deleteUnusedContainer(groupId, container);

    final NDSGroup group = _groupDao.find(groupId).get();
    assertFalse(group.getCloudProviderContainers().contains(container));
    assertEquals(0, group.getCloudProviderContainers().size());

    final List<Event> e = _auditSvc.findByEventTypeForOrganization(null, Type.CONTAINER_DELETED);
    assertEquals(1, e.size());
    final NDSAudit n = (NDSAudit) e.get(0);
    assertEquals(Type.CONTAINER_DELETED, n.getEventType());
    assertEquals(groupId, n.getGroupId());
    assertEquals(CloudProvider.AWS.name(), n.getCloudProvider());
  }

  @Test
  public void testDeleteUnusedContainer_ExistingCluster() throws Exception {
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan();
    final Group mmsGroup = MmsFactory.createGroup(organization);
    MmsFactory.createUser(mmsGroup);

    final ObjectId groupId = mmsGroup.getId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    final ObjectId containerId =
        _svc.getCloudContainerId(group, AWSRegionName.US_EAST_1, NDSDefaults.ATLAS_CIDR);
    final AWSCloudProviderContainer container = _svc.getContainerById(groupId, containerId).get();

    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setGroupId(groupId)
            .build();
    _clusterSvc.createDefaultProcessArgs(
        mmsGroup, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);

    SvcException e =
        assertThrows(SvcException.class, () -> _svc.deleteUnusedContainer(groupId, container));
    assertEquals(NDSErrorCode.CONTAINER_IN_USE, e.getErrorCode());

    // No container has been deleted, therefore no audit event should have
    // been emitted.
    assertEquals(0, _auditSvc.findByEventTypeForOrganization(null, Type.CONTAINER_DELETED).size());
  }

  @Test
  public void testDeleteUnusedContainer_ProvisionedContainer() throws SvcException {
    final ObjectId groupId = new ObjectId();
    _groupSvc.ensureGroup(groupId);

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getAWSContainer(AWSRegionName.US_EAST_1, _account.getId()));
    _accountDao.assignRegionToGroup(AWSRegionName.US_EAST_1, groupId);
    _groupDao.addCloudContainerWithId(groupId, container, container.getId());

    SvcException e =
        assertThrows(SvcException.class, () -> _svc.deleteUnusedContainer(groupId, container));
    assertEquals(NDSErrorCode.CONTAINER_IN_USE, e.getErrorCode());

    // No container has been deleted, therefore no audit event should have
    // been emitted.
    assertEquals(0, _auditSvc.findByEventTypeForOrganization(null, Type.CONTAINER_DELETED).size());
  }
}
