package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertTrue;

import com.amazonaws.services.route53.model.RRType;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.aws._public.svc.AWSIpamPoolSvc;
import com.xgen.cloud.nds.aws._public.svc.AwsApiSvcV2;
import com.xgen.cloud.nds.capacity._public.svc.AWSCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.aws.dns.DNSChangeQueueDao;
import com.xgen.svc.nds.aws.planner.AWSCreateElasticIpStep.ElasticIpState;
import com.xgen.svc.nds.aws.planner.admincapacity.AWSCheckCapacityStepHelper;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.SyncDNSRecordStep;
import com.xgen.svc.nds.svc.CloudChefConfSvc;
import com.xgen.svc.nds.svc.NDSRemoteImageSvc;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.junit.Test;

public class AWSDeleteInstanceStepIntTests extends AWSExternalIntTest {

  @Inject AWSApiSvc _awsApiSvc;
  @Inject NDSOrphanedItemDao _orphanedItemDao;
  @Inject private DNSChangeQueueDao _dnsChangeQueueDao;
  @Inject private NDSRemoteImageSvc _ndsRemoteImageSvc;
  @Inject private CloudChefConfSvc _cloudChefConfSvc;
  @Inject private GroupDao _groupDao;
  @Inject private AWSCheckCapacityStepHelper _awsCheckCapacityStepHelper;
  @Inject private AWSIpamPoolSvc _awsIpamPoolSvc;
  @Inject private AwsApiSvcV2 _awsApiSvcV2;
  @Inject private AWSCapacityDenylistSvc _awsCapacityDenylistSvc;

  private void testAWSDeleteInstanceStepInternal(final Plan pPlan, final String pEC2InstanceId) {

    final AWSDeleteInstanceStep step =
        new AWSDeleteInstanceStep(
            (NDSPlanContext) pPlan.getPlanContext(),
            new Step.State(pPlan.getId(), pPlan.getMoves().get(1).getId(), 2, getPlanDao()),
            getContainer(),
            pEC2InstanceId,
            false,
            _awsApiSvc,
            _orphanedItemDao);

    waitForStepPerformDone(step);
    verifyEC2InstanceDeleted(pEC2InstanceId);
    assertTrue(step.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSDeleteInstanceStep() {

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    plan.addMove(provisionContainerMove);
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    final NDSPlanContext planContext = (NDSPlanContext) plan.getPlanContext();

    final AWSCreateElasticIpStep createElasticIpStep =
        new AWSCreateElasticIpStep(
            planContext,
            new Step.State(plan.getId(), plan.getMoves().get(1).getId(), 0, getPlanDao()),
            getContainer(),
            _orphanedItemDao,
            _awsApiSvc,
            _awsApiSvcV2,
            getOrphanedItemSvc(),
            _awsIpamPoolSvc,
            _groupDao,
            new Date(),
            ResourceOperationTagType.NONE,
            ElasticIpState.CURRENT,
            false,
            false);

    try {
      waitForStepPerformDone(createElasticIpStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createElasticIpStep);
      throw t;
    }

    final List<SyncDNSRecordStep> createDNSRecordStep =
        performAndWaitForDNSSync(
            planContext,
            new Step.State(plan.getId(), plan.getMoves().get(1).getId(), 1, getPlanDao()),
            NDSSettings.getRoute53PublicHostedZonesForClusterHost(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            RRType.CNAME,
            DNSRecordUtil.getInternalDNSRecordForInstance(
                getNDSGroup(),
                getHardwareDao()
                    .findReplicaSetHardwareForInstance(
                        getGroup().getId(),
                        getClusterDescription().getName(),
                        getInstanceIds().get(0)),
                getClusterDescription(),
                getInstanceIds().get(0),
                NDSSettings.getCrossCloudInstanceDomainName(getAppSettings())),
            AWSNDSDefaults.getAWSPublicDNSRecordForIp(
                createElasticIpStep.getElasticIpId().get(), getContainer()));

    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final AWSCreateVolumeStep createVolumeStep =
        new AWSCreateVolumeStep(
            planContext,
            new Step.State(plan.getId(), plan.getMoves().get(1).getId(), 1, getPlanDao()),
            _awsApiSvc,
            _orphanedItemDao,
            getContainer(),
            getClusterDescription(),
            replicaSetHardware,
            getInstanceHardware(replicaSetHardware, getInstanceIds().get(0)),
            getContainer().getSubnets()[0].getSubnetId(),
            _groupDao,
            ResourceOperationTagType.NONE,
            false);

    try {
      waitForStepPerformDone(createVolumeStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createVolumeStep);
      throw t;
    }

    final AWSCreateInstanceAbstractStep createInstanceStep =
        new AWSCreateInstanceStep(
            planContext,
            new Step.State(plan.getId(), plan.getMoves().get(1).getId(), 2, getPlanDao()),
            getContainer(),
            getClusterDescription(),
            getInstanceIds().get(0),
            getHardwareDao()
                .findReplicaSetHardwareForInstance(
                    getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0)),
            getContainer().getSubnets()[0].getSubnetId(),
            createElasticIpStep.perform().getData().getElasticIpId(),
            createElasticIpStep.perform().getData().getElasticIp(),
            AWSNDSDefaults.getAWSPublicDNSRecordForIp(
                createElasticIpStep.getElasticIpId().get(), getContainer()),
            "publicHostname",
            false,
            _awsApiSvc,
            _orphanedItemDao,
            _ndsRemoteImageSvc,
            _cloudChefConfSvc,
            _awsCheckCapacityStepHelper,
            _groupDao,
            _awsCapacityDenylistSvc);

    try {
      waitForStepPerformDone(createInstanceStep);
    } catch (final Throwable t) {
      if (createInstanceStep.getEC2InstanceId().isPresent()) {
        cleanupEC2Instance(createInstanceStep.getEC2InstanceId().get());
      }
      throw t;
    }

    try {
      testAWSDeleteInstanceStepInternal(
          plan, createInstanceStep.perform().getData().getEC2InstanceId());
    } catch (final Throwable t) {
      cleanupEC2Instance(createInstanceStep.perform().getData().getEC2InstanceId());
      throw t;
    } finally {
      waitForStepRollbackDone(createVolumeStep);
      createDNSRecordStep.forEach(this::waitForStepRollbackDone);
      waitForStepRollbackDone(createElasticIpStep);
      waitForMoveRollbackDone(provisionContainerMove);
    }
  }
}
