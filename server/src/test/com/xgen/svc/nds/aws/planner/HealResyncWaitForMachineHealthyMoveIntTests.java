package com.xgen.svc.nds.aws.planner;

import static com.xgen.cloud.common.dao.base._public.util.BsonUtils.Static.oid;

import com.google.inject.Injector;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.project._private.dao.NDSLogDao;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Move.Observable;
import com.xgen.module.common.planner.model.Move.State;
import com.xgen.svc.nds.planner.HealResyncWaitForMachineHealthyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.svc.planning.NDSPlanExecutorCallbackSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class HealResyncWaitForMachineHealthyMoveIntTests {

  @Inject private AppSettings appSettings;
  @Inject private PlanDao planDao;
  @Inject private NDSLogDao logDao;
  @Inject private NDSPlanExecutorCallbackSvc executorCallbackSvc;
  @Inject private Observable planExecutorObservable;
  @Inject private Injector injector;

  @Test
  public void testCreateSerializeLoad() {
    final ObjectId groupId = oid(0);
    final ObjectId planId = oid(1);
    final HealResyncWaitForMachineHealthyMove move =
        HealResyncWaitForMachineHealthyMove.factoryCreate(
            new NDSPlanContext(
                groupId,
                planId,
                appSettings,
                planDao,
                logDao,
                executorCallbackSvc,
                planExecutorObservable),
            "Cluster0",
            oid(2));

    HealResyncWaitForMachineHealthyMove.fromDBObject(
        injector, move.getContext(), new State(planId, move.getId(), planDao), move.toDBObject());
  }
}
