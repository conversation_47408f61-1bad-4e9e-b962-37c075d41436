package com.xgen.svc.nds.aws.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.AWSPeerVpcAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSPeerVpcDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSPeerVpc;
import com.xgen.cloud.nds.aws._public.model.AWSPeerVpc.ErrorState;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.ui.AWSPeerVpcView;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class AWSPeerVpcSvcIntTests extends JUnit5BaseSvcTest {
  private static final String VPC_ID = "vpc-1234";
  private static final String AWS_ACCOUNT_ID = "AAAAAAAAA";
  private static final String ROUTE_TABLE_CIDR_BLOCK = "172.16.0.0/21";

  private ObjectId _awsContainerId;

  private static AWSPeerVpcView getDefaultView(final ObjectId pContainerId) {
    return new AWSPeerVpcView(VPC_ID, ROUTE_TABLE_CIDR_BLOCK, AWS_ACCOUNT_ID, pContainerId);
  }

  @Inject private AWSPeerVpcSvc _awsPeerVpcSvc;

  @Inject private AuditSvc _auditSvc;

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private AWSPeerVpcDao _awsPeerVpcDao;
  @Inject private GroupSvc _groupSvc;

  @Test
  public void testAddPeer() throws SvcException {
    final AuditInfo auditInfo = createTestAuditInfo();
    final ObjectId groupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId containerId =
        _ndsGroupDao.find(groupId).get().getCloudProviderContainers().get(0).getId();

    // try to add peer with non-existent AWS account ID
    try {
      _awsPeerVpcSvc.addPeer(
          groupId,
          new AWSPeerVpcView(VPC_ID, ROUTE_TABLE_CIDR_BLOCK, null, containerId),
          auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
    }

    // try to add peer with CIDR BLOCK of invalid format
    try {
      _awsPeerVpcSvc.addPeer(
          groupId, new AWSPeerVpcView(VPC_ID, "", AWS_ACCOUNT_ID, containerId), auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_IP_ADDRESS_OR_CIDR_NOTATION, pE.getErrorCode());
    }

    // try to add peer with VPC ID of invalid format
    try {
      _awsPeerVpcSvc.addPeer(
          groupId,
          new AWSPeerVpcView("", ROUTE_TABLE_CIDR_BLOCK, AWS_ACCOUNT_ID, containerId),
          auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());
    }

    // try to add peer to non-existent group
    try {
      _awsPeerVpcSvc.addPeer(new ObjectId(), getDefaultView(null), auditInfo);
      fail();
    } catch (final Exception pE) {
      assertNotNull(pE);
    }

    // successfully add peer to group
    final AWSPeerVpcView defaultView = getDefaultView(containerId);
    _awsPeerVpcSvc.addPeer(groupId, defaultView, auditInfo);
    final List<Event> audits =
        _auditSvc.findByDate(AWSPeerVpcAudit.Type.PEER_CREATED, Date.from(Instant.EPOCH));
    assertEquals(1, audits.size());
    final AWSPeerVpcAudit audit = (AWSPeerVpcAudit) audits.get(0);
    assertEquals(defaultView.getVpcId(), audit.getVpcId());
    assertEquals(defaultView.getAwsAccountId(), audit.getAwsAccountId());

    // add peer with non-canonical cidr block
    final String cidr = "10.0.1.0/16";
    final AWSPeerVpcView withWithNonCanonical =
        new AWSPeerVpcView("vpc-4567", cidr, AWS_ACCOUNT_ID, containerId);
    final ObjectId peerId = _awsPeerVpcSvc.addPeer(groupId, withWithNonCanonical, auditInfo);

    final AWSPeerVpc storedPeer = _awsPeerVpcDao.getAwsPeerVpc(groupId, peerId).get();
    assertEquals("10.0.0.0/16", storedPeer.getRouteTableCidrBlock());
  }

  @Test
  public void testAddPeer_MixedCNStandardRegions() throws SvcException {
    final AuditInfo auditInfo = createTestAuditInfo();

    final ObjectId standardGroupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId standardContainerId =
        _ndsGroupDao.find(standardGroupId).get().getCloudProviderContainers().get(0).getId();

    AWSNDSDefaults.CN_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.addPeer(
                    standardGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        standardContainerId,
                        r.getValue()),
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.addPeer(
        standardGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            standardContainerId,
            AWSRegionName.US_EAST_1.getValue()),
        auditInfo);

    final ObjectId cnRegionsOnlyGroupId = createTestGroup(true, true, RegionUsageRestrictions.NONE);
    final ObjectId cnRegionsOnlyContainerId =
        _ndsGroupDao.find(cnRegionsOnlyGroupId).get().getCloudProviderContainers().get(0).getId();

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion())
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.addPeer(
                    cnRegionsOnlyGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        cnRegionsOnlyContainerId,
                        r.getValue()),
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.addPeer(
        cnRegionsOnlyGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            cnRegionsOnlyContainerId,
            AWSRegionName.CN_NORTH_1.getValue()),
        auditInfo);
  }

  @Test
  public void testAddPeer_MixedGovCommercialRegions() throws SvcException {
    final AuditInfo auditInfo = createTestAuditInfo();

    final ObjectId commercialGroupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId commercialContainerId =
        _ndsGroupDao.find(commercialGroupId).get().getCloudProviderContainers().get(0).getId();

    AWSNDSDefaults.GOV_CLOUD_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.addPeer(
                    commercialGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        commercialContainerId,
                        r.getValue()),
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.addPeer(
        commercialGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            commercialContainerId,
            AWSRegionName.US_EAST_1.getValue()),
        auditInfo);

    final ObjectId govRegionsOnlyGroupId =
        createTestGroup(true, false, RegionUsageRestrictions.GOV_REGIONS_ONLY);
    final ObjectId govRegionsOnlyContainerId =
        _ndsGroupDao.find(govRegionsOnlyGroupId).get().getCloudProviderContainers().get(0).getId();

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isUSGovRegion())
        .filter(r -> !r.isCNRegion())
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.addPeer(
                    govRegionsOnlyGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        govRegionsOnlyContainerId,
                        r.getValue()),
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.addPeer(
        govRegionsOnlyGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            govRegionsOnlyContainerId,
            AWSRegionName.US_GOV_WEST_1.getValue()),
        auditInfo);
  }

  @Test
  public void testGetPeers() throws SvcException {
    ObjectId groupId;

    // try to get peers from non-existent group
    try {
      _awsPeerVpcSvc.getPeers(new ObjectId());
      fail();
    } catch (final IllegalStateException pE) {
      assertTrue(pE.getMessage().contains("does not exist"));
    }

    // try to get peers from group with no containers
    groupId = createTestGroup(false, false, RegionUsageRestrictions.NONE);
    assertEquals(0, _awsPeerVpcSvc.getPeers(groupId).size());

    // get list from group with no peers
    groupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId containerId =
        _ndsGroupDao.find(groupId).get().getCloudProviderContainers().get(0).getId();
    assertEquals(0, _awsPeerVpcSvc.getPeers(groupId).size());

    // successfully get peers from group
    final AWSPeerVpcView defaultView = getDefaultView(containerId);
    _awsPeerVpcSvc.addPeer(groupId, defaultView, createTestAuditInfo());
    final List<AWSPeerVpcView> views = _awsPeerVpcSvc.getPeers(groupId);
    assertEquals(1, views.size());
    final AWSPeerVpcView view = views.get(0);
    assertEquals(defaultView.getVpcId(), view.getVpcId());
    assertEquals(defaultView.getRouteTableCidrBlock(), view.getRouteTableCidrBlock());
    assertEquals(defaultView.getAwsAccountId(), view.getAwsAccountId());
  }

  @Test
  public void testUpdateAwsPeerRouteTableCidrBlock() throws SvcException {
    ObjectId groupId;
    final AuditInfo auditInfo = createTestAuditInfo();

    // try to update peer with CIDR block of invalid format
    try {
      _awsPeerVpcSvc.updateAwsPeerRouteTableCidrBlock(
          new ObjectId(), new ObjectId(), "", auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_IP_ADDRESS_OR_CIDR_NOTATION, pE.getErrorCode());
    }

    // try to update peer from non-existent group
    try {
      _awsPeerVpcSvc.updateAwsPeerRouteTableCidrBlock(
          new ObjectId(), new ObjectId(), ROUTE_TABLE_CIDR_BLOCK, auditInfo);
      fail();
    } catch (final IllegalStateException pE) {
      assertTrue(pE.getMessage().contains("does not exist"));
    }

    // try to update non-existent peer from group
    groupId = createTestGroup(false, false, RegionUsageRestrictions.NONE);
    try {
      _awsPeerVpcSvc.updateAwsPeerRouteTableCidrBlock(
          groupId, new ObjectId(), ROUTE_TABLE_CIDR_BLOCK, auditInfo);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.PEER_NOT_FOUND, pE.getErrorCode());
    }

    // successfully update peer from group
    groupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId containerId =
        _ndsGroupDao.find(groupId).get().getCloudProviderContainers().get(0).getId();
    final AWSPeerVpcView defaultView = getDefaultView(containerId);
    _awsPeerVpcSvc.addPeer(groupId, defaultView, auditInfo);
    final ObjectId peerId = _awsPeerVpcSvc.getPeers(groupId).get(0).getId();
    _awsPeerVpcSvc.updateAwsPeerRouteTableCidrBlock(groupId, peerId, "192.168.0.0/21", auditInfo);
    final AWSPeerVpcView view = _awsPeerVpcSvc.getPeers(groupId).get(0);
    assertEquals("192.168.0.0/21", view.getRouteTableCidrBlock());
    final List<Event> audits =
        _auditSvc.findByDate(AWSPeerVpcAudit.Type.PEER_UPDATED, Date.from(Instant.EPOCH));
    assertEquals(1, audits.size());
    final AWSPeerVpcAudit audit = (AWSPeerVpcAudit) audits.get(0);
    assertEquals(view.getId(), audit.getPeerVpcId());
    assertEquals(view.getRouteTableCidrBlock(), audit.getCidrBlock());

    // update peer with non-canonical cidr
    final String cidr = "10.0.1.0/16";
    _awsPeerVpcSvc.updateAwsPeerRouteTableCidrBlock(groupId, peerId, cidr, auditInfo);

    final AWSPeerVpc storedPeer = _awsPeerVpcDao.getAwsPeerVpc(groupId, peerId).get();
    assertEquals("10.0.0.0/16", storedPeer.getRouteTableCidrBlock());
  }

  @Test
  public void testUpdatePeer_MixedCNStandardRegions() throws SvcException {
    final AuditInfo auditInfo = createTestAuditInfo();

    final ObjectId standardGroupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId standardContainerId =
        _ndsGroupDao.find(standardGroupId).get().getCloudProviderContainers().get(0).getId();
    final ObjectId standardPeerId =
        _awsPeerVpcSvc.addPeer(
            standardGroupId,
            new AWSPeerVpcView(
                VPC_ID,
                ROUTE_TABLE_CIDR_BLOCK,
                AWS_ACCOUNT_ID,
                standardContainerId,
                AWSRegionName.US_EAST_1.getValue()),
            auditInfo);
    // One can only update a peer VPC when its status is FAILED
    _awsPeerVpcDao.setAwsPeerVpcFailed(
        standardGroupId, standardContainerId, standardPeerId, ErrorState.EXPIRED);

    AWSNDSDefaults.CN_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.updatePeerVpc(
                    standardGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        standardContainerId,
                        r.getValue()),
                    standardPeerId,
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.updatePeerVpc(
        standardGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            standardContainerId,
            AWSRegionName.US_EAST_2.getValue()),
        standardPeerId,
        auditInfo);

    final ObjectId cnRegionsOnlyGroupId = createTestGroup(true, true, RegionUsageRestrictions.NONE);
    final ObjectId cnRegionsOnlyContainerId =
        _ndsGroupDao.find(cnRegionsOnlyGroupId).get().getCloudProviderContainers().get(0).getId();
    final ObjectId cnRegionsOnlyPeerId =
        _awsPeerVpcSvc.addPeer(
            cnRegionsOnlyGroupId,
            new AWSPeerVpcView(
                VPC_ID,
                ROUTE_TABLE_CIDR_BLOCK,
                AWS_ACCOUNT_ID,
                cnRegionsOnlyContainerId,
                AWSRegionName.CN_NORTH_1.getValue()),
            auditInfo);
    // One can only update a peer VPC when its status is FAILED
    _awsPeerVpcDao.setAwsPeerVpcFailed(
        cnRegionsOnlyGroupId, cnRegionsOnlyContainerId, cnRegionsOnlyPeerId, ErrorState.EXPIRED);

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isCNRegion())
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.updatePeerVpc(
                    cnRegionsOnlyGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        cnRegionsOnlyContainerId,
                        r.getValue()),
                    cnRegionsOnlyPeerId,
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both China and non-China regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.updatePeerVpc(
        cnRegionsOnlyGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            cnRegionsOnlyContainerId,
            AWSRegionName.CN_NORTHWEST_1.getValue()),
        cnRegionsOnlyPeerId,
        auditInfo);
  }

  @Test
  public void testUpdatePeer_MixedGovCommercialRegions() throws SvcException {
    final AuditInfo auditInfo = createTestAuditInfo();

    final ObjectId commercialGroupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId commercialContainerId =
        _ndsGroupDao.find(commercialGroupId).get().getCloudProviderContainers().get(0).getId();
    final ObjectId standardPeerId =
        _awsPeerVpcSvc.addPeer(
            commercialGroupId,
            new AWSPeerVpcView(
                VPC_ID,
                ROUTE_TABLE_CIDR_BLOCK,
                AWS_ACCOUNT_ID,
                commercialContainerId,
                AWSRegionName.US_EAST_1.getValue()),
            auditInfo);
    // One can only update a peer VPC when its status is FAILED
    _awsPeerVpcDao.setAwsPeerVpcFailed(
        commercialGroupId, commercialContainerId, standardPeerId, ErrorState.EXPIRED);

    AWSNDSDefaults.GOV_CLOUD_REGIONS.stream()
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.updatePeerVpc(
                    commercialGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        commercialContainerId,
                        r.getValue()),
                    standardPeerId,
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.updatePeerVpc(
        commercialGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            commercialContainerId,
            AWSRegionName.US_EAST_2.getValue()),
        standardPeerId,
        auditInfo);

    final ObjectId govRegionsOnlyGroupId =
        createTestGroup(true, false, RegionUsageRestrictions.GOV_REGIONS_ONLY);
    final ObjectId govRegionsOnlyContainerId =
        _ndsGroupDao.find(govRegionsOnlyGroupId).get().getCloudProviderContainers().get(0).getId();
    final ObjectId govRegionsOnlyPeerId =
        _awsPeerVpcSvc.addPeer(
            govRegionsOnlyGroupId,
            new AWSPeerVpcView(
                VPC_ID,
                ROUTE_TABLE_CIDR_BLOCK,
                AWS_ACCOUNT_ID,
                govRegionsOnlyContainerId,
                AWSRegionName.US_GOV_WEST_1.getValue()),
            auditInfo);
    // One can only update a peer VPC when its status is FAILED
    _awsPeerVpcDao.setAwsPeerVpcFailed(
        govRegionsOnlyGroupId, govRegionsOnlyContainerId, govRegionsOnlyPeerId, ErrorState.EXPIRED);

    Arrays.stream(AWSRegionName.values())
        .filter(r -> !r.isUSGovRegion())
        .filter(r -> !r.isCNRegion())
        .forEach(
            r -> {
              try {
                _awsPeerVpcSvc.updatePeerVpc(
                    govRegionsOnlyGroupId,
                    new AWSPeerVpcView(
                        VPC_ID,
                        ROUTE_TABLE_CIDR_BLOCK,
                        AWS_ACCOUNT_ID,
                        govRegionsOnlyContainerId,
                        r.getValue()),
                    govRegionsOnlyPeerId,
                    auditInfo);
                fail("Expected SvcException");
              } catch (final SvcException pE) {
                assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
                assertEquals(
                    "A project cannot have both GovCloud and Commercial regions.", pE.getMessage());
              }
            });
    _awsPeerVpcSvc.updatePeerVpc(
        govRegionsOnlyGroupId,
        new AWSPeerVpcView(
            VPC_ID,
            ROUTE_TABLE_CIDR_BLOCK,
            AWS_ACCOUNT_ID,
            govRegionsOnlyContainerId,
            AWSRegionName.US_GOV_WEST_1.getValue()),
        govRegionsOnlyPeerId,
        auditInfo);
  }

  @Test
  public void testRequestDeleteAWSPeer() throws SvcException {
    ObjectId groupId;
    final AuditInfo auditInfo = createTestAuditInfo();

    // try to delete peer from non-existent group
    try {
      _awsPeerVpcSvc.requestDeleteContainerPeer(
          new ObjectId(),
          getDefaultView(new ObjectId()).toAWSPeerVpc(AWSRegionName.AP_SOUTH_1),
          auditInfo);
      fail();
    } catch (final IllegalStateException pE) {
      assertTrue(pE.getMessage().contains("does not exist"));
    }

    // successfully delete aws peer from group
    groupId = createTestGroup(true, false, RegionUsageRestrictions.NONE);
    final ObjectId peerId =
        _awsPeerVpcSvc.addPeer(groupId, getDefaultView(_awsContainerId), auditInfo);
    AWSPeerVpcView awsView = _awsPeerVpcSvc.getPeers(groupId).get(0);
    assertNotEquals(AWSPeerVpcView.Status.TERMINATING, awsView.getStatus().get());

    _awsPeerVpcSvc.requestDeleteContainerPeer(
        groupId, _awsPeerVpcSvc.getPeerById(groupId, peerId).get(), auditInfo);
    assertEquals(
        1,
        _auditSvc.findByDate(AWSPeerVpcAudit.Type.PEER_DELETED, Date.from(Instant.EPOCH)).size());
    awsView = _awsPeerVpcSvc.getPeers(groupId).get(0);
    assertEquals(AWSPeerVpcView.Status.TERMINATING, awsView.getStatus().get());

    // try to delete non-existent peer from group
    _awsPeerVpcSvc.requestDeleteContainerPeer(
        groupId, getDefaultView(new ObjectId()).toAWSPeerVpc(AWSRegionName.AP_SOUTH_1), auditInfo);
  }

  private ObjectId createTestGroup(
      final boolean pAddContainers,
      boolean pUseCNRegionsOnly,
      final RegionUsageRestrictions pRegionUsageRestrictions) {
    final ObjectId groupId = new ObjectId();
    final Organization org = MmsFactory.createOrganizationWithNDSPlan();
    final Group group = MmsFactory.createGroup(org, "testGroup", groupId, pUseCNRegionsOnly);
    _groupSvc.save(group);
    _ndsGroupSvc.create(groupId, new NDSManagedX509(), pUseCNRegionsOnly, pRegionUsageRestrictions);
    if (pAddContainers) {
      _awsContainerId =
          _ndsGroupDao.addCloudContainer(
              groupId,
              new AWSCloudProviderContainer(
                  pUseCNRegionsOnly
                      ? NDSModelTestFactory.getAWSContainer(AWSRegionName.CN_NORTH_1)
                      : NDSModelTestFactory.getAWSContainer()));
    }
    return groupId;
  }

  private AuditInfo createTestAuditInfo() {
    final AppUser user = new AppUser();
    user.setId(new ObjectId());
    user.setUsername("tempUser");
    return MmsFactory.createAuditInfoFromPublicApi(user, false, "127.0.0.1");
  }
}
