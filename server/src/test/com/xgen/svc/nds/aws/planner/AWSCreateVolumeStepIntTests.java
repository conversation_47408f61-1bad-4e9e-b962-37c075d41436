package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.Volume;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.Map;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class AWSCreateVolumeStepIntTests extends AWSExternalIntTest {

  private Plan _plan;
  private Move _provisionContainerMove;

  @Inject AWSApiSvc _awsApiSvc;
  @Inject NDSOrphanedItemDao _orphanedItemDao;
  @Inject private GroupDao _groupDao;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _plan = new Plan(getGroup().getId(), getPlanContextFactory());
    _provisionContainerMove =
        AWSProvisionContainerMove.factoryCreate(
            (NDSPlanContext) _plan.getPlanContext(), Map.of(), getContainer().getId());
    _plan.addMove(_provisionContainerMove);
    _plan.addMove(new DummyMove());

    // Need to save plan so we can get state prepared
    getPlanDao().save(_plan);

    try {
      waitForMovePerformDone(_provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(_provisionContainerMove);
      throw t;
    }
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    waitForMoveRollbackDone(_provisionContainerMove);
    super.tearDown();
  }

  @SuppressWarnings("deprecation") // TODO CLOUDP-296703: Address deprecated getLogger methods
  public void testAWSCreateVolumeStepInternal(final AWSCreateVolumeStep pStep) {

    // Now create volume
    waitForStepPerformDone(pStep);

    final Result<AWSCreateVolumeStep.Data> result = pStep.perform();

    final Volume volume =
        getAWSApiSvc()
            .findEBSVolume(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pStep.getLogger(),
                result.getData().getEBSVolumeId());

    assertEquals(result.getData().getEBSVolumeId(), volume.getVolumeId());
    assertEquals(result.getData().getEBSVolumeType(), volume.getVolumeType());
    assertEquals(result.getData().getEBSVolumeEncrypted(), volume.getEncrypted());
    assertEquals(result.getData().getSizeGB(), volume.getSize());
    assertEquals(result.getData().getIOPS(), volume.getIops());

    verifyTags(volume.getTags());

    waitForStepRollbackDone(pStep);

    // Make sure the volume is orphaned and still present in AWS
    verifyEBSVolumeOrphaned(result.getData().getEBSVolumeId(), false);
    assertNotNull(
        getAWSApiSvc()
            .findEBSVolume(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                pStep.getLogger(),
                result.getData().getEBSVolumeId()));

    // Make sure rolling back does nothing
    assertTrue(pStep.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSCreateVolumeStep_Encrypted() {
    final NDSPlanContext planContext = (NDSPlanContext) _plan.getPlanContext();
    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setEncryptEBSVolume(true))
            .build();
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));

    final AWSCreateVolumeStep step =
        new AWSCreateVolumeStep(
            planContext,
            new Step.State(_plan.getId(), _plan.getMoves().get(1).getId(), 0, getPlanDao()),
            _awsApiSvc,
            _orphanedItemDao,
            getContainer(),
            clusterDescription,
            replicaSetHardware,
            getInstanceHardware(replicaSetHardware, getInstanceIds().get(0)),
            getContainer().getSubnets()[0].getSubnetId(),
            _groupDao,
            ResourceOperationTagType.NONE,
            false);

    try {
      testAWSCreateVolumeStepInternal(step);
    } catch (final Throwable t) {
      performUnconditionally(() -> waitForStepRollbackDone(step));
      throw t;
    }
  }
}
