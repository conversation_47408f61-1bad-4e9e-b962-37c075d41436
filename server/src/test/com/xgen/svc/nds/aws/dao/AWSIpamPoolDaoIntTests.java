package com.xgen.svc.nds.aws.dao;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.mongodb.DuplicateKeyException;
import com.xgen.cloud.nds.aws._private.dao.AWSIpamPoolDao;
import com.xgen.cloud.nds.aws._public.model.AWSIpamPool;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class AWSIpamPoolDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private AWSIpamPoolDao awsIpamPoolDao;

  @Test
  void testCreateIpamPool() {
    final String poolId = ObjectId.get().toHexString();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final boolean enabled = true;

    assertEquals(0, awsIpamPoolDao.count());

    awsIpamPoolDao.create(poolId, regionName, enabled);

    assertEquals(1, awsIpamPoolDao.count());
    final AWSIpamPool ipamPool = awsIpamPoolDao.find(regionName).orElseThrow();
    assertEquals(poolId, ipamPool.getPoolId());
    assertEquals(regionName, ipamPool.getRegionName());
    assertEquals(enabled, ipamPool.getEnabled());
  }

  @Test
  void testCreateIpamPool_indexViolation() {
    final String poolId1 = ObjectId.get().toHexString(),
        poolId2 = ObjectId.get().toHexString(),
        poolId3 = ObjectId.get().toHexString();
    final AWSRegionName sameRegionName = AWSRegionName.US_EAST_1;
    final AWSRegionName diffRegionName = AWSRegionName.US_EAST_2;
    final boolean enabled = true;

    assertEquals(0, awsIpamPoolDao.count());
    awsIpamPoolDao.create(poolId1, sameRegionName, enabled);
    assertEquals(1, awsIpamPoolDao.count());

    // Create enabled pool in different region that does not violate index
    awsIpamPoolDao.create(poolId2, diffRegionName, enabled);

    // Create enabled pool in same region that does violate index
    assertThrows(
        DuplicateKeyException.class, () -> awsIpamPoolDao.create(poolId3, sameRegionName, enabled));
  }

  @Test
  void testSetEnabledIpamPool() {
    final String poolId = ObjectId.get().toHexString();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    awsIpamPoolDao.create(poolId, regionName, true);

    assertTrue(awsIpamPoolDao.setEnabled(poolId, false));

    final AWSIpamPool disabledPool = new AWSIpamPool(awsIpamPoolDao.findOne(new BasicDBObject()));
    assertEquals(poolId, disabledPool.getPoolId());
    assertEquals(regionName, disabledPool.getRegionName());
    assertFalse(disabledPool.getEnabled());

    assertTrue(awsIpamPoolDao.setEnabled(poolId, true));

    final AWSIpamPool enabledPool = awsIpamPoolDao.find(regionName).orElseThrow();
    assertEquals(poolId, enabledPool.getPoolId());
    assertEquals(regionName, enabledPool.getRegionName());
    assertTrue(enabledPool.getEnabled());
  }

  @Test
  void testSetEnabledIpamPool_indexViolation() {
    final String poolId1 = ObjectId.get().toHexString(), poolId2 = ObjectId.get().toHexString();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    awsIpamPoolDao.create(poolId1, regionName, true);
    awsIpamPoolDao.create(poolId2, regionName, false);

    // `enabled` index violation
    assertThrows(DuplicateKeyException.class, () -> awsIpamPoolDao.setEnabled(poolId2, true));
  }
}
