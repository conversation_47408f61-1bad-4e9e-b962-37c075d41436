package com.xgen.svc.nds.aws.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.aws._public.model.AWSAdminBackupSnapshot;
import com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob.Type;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;

public class AWSAdminBackupSnapshotMoveIntTests extends AWSExternalIntTest {
  @Before
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test(timeout = 45 * 60 * 1000L)
  public void testAwsAdminBackupSnapshotMove() throws InterruptedException, SvcException {
    final Plan setupPlan = setupPlanWithContainerAndSingleInstance();

    try {
      waitForPlanPerformSuccess(setupPlan);
      testPerform();
    } finally {
      waitForPlanRollbackSuccess(setupPlan);
    }
  }

  private void testPerform() throws InterruptedException, SvcException {
    final Plan createAdminSnapshotPlan = new Plan(getGroupId(), getPlanContextFactory());
    final ClusterDescription clusterDescription = getClusterDescription();
    final ObjectId instanceId = getInstanceIds().get(0);
    final String hostname = getHostname(clusterDescription, getInstanceIds());

    final AppUser globalAtlasOperatorUser =
        MmsFactory.createApiUserWithGlobalRole(
            "globalAtlasOperatorUser", Role.GLOBAL_ATLAS_OPERATOR);

    try {
      final NDSAdminJob ndsAdminJob =
          getNDSAdminJobDao()
              .create(
                  createAdminSnapshotPlan.getId(),
                  getGroupId(),
                  clusterDescription.getName(),
                  hostname,
                  Type.TAKE_ADMIN_BACKUP_SNAPSHOT);

      getNDSAdminBackupSnapshotSvc()
          .insertAdminBackupSnapshot(
              getGroupId(),
              getClusterDescription().getName(),
              getInstanceHardware(getGroupId(), clusterDescription.getName(), instanceId),
              hostname,
              globalAtlasOperatorUser,
              "comment");

      final Optional<AdminBackupSnapshot> adminBackupSnapshotOpt =
          getNDSAdminBackupSnapshotSvc()
              .findPendingSnapshot(getGroupId(), clusterDescription.getName(), instanceId);
      assertTrue(adminBackupSnapshotOpt.isPresent());
      final AWSAdminBackupSnapshot awsAdminBackupSnapshot =
          (AWSAdminBackupSnapshot) adminBackupSnapshotOpt.get();

      final AWSAdminBackupSnapshotMove adminBackupSnapshotMove =
          spy(
              AWSAdminBackupSnapshotMove.factoryCreate(
                  createAdminSnapshotPlan.getPlanContext(),
                  getClusterDescription().getName(),
                  instanceId,
                  ndsAdminJob.getId()));
      createAdminSnapshotPlan.addMove(adminBackupSnapshotMove);
      getPlanDao().save(createAdminSnapshotPlan);

      Result<?> result = adminBackupSnapshotMove.perform();
      final Optional<NDSAdminJob> ndsAdminJobJustAfterPerform =
          getNDSAdminJobDao().find(ndsAdminJob.getId());
      assertTrue(ndsAdminJobJustAfterPerform.isPresent());
      assertEquals(NDSAdminJob.Status.WORKING, ndsAdminJobJustAfterPerform.get().getStatus());

      while (!result.getStatus().isDone()) {
        assertFalse(result.getStatus().isFailed());
        Thread.sleep(Duration.ofSeconds(5).toMillis());
        result = adminBackupSnapshotMove.perform();
      }

      final Optional<NDSAdminJob> ndsAdminJobAfterPerform =
          getNDSAdminJobDao().find(ndsAdminJob.getId());
      assertTrue(ndsAdminJobAfterPerform.isPresent());
      assertEquals(NDSAdminJob.Status.COMPLETE, ndsAdminJobAfterPerform.get().getStatus());

      final List<AdminBackupSnapshot> snapshots =
          getNDSAdminBackupSnapshotSvc()
              .findNonDeletedSnapshotsForCluster(getGroupId(), clusterDescription.getName());
      assertEquals(1, snapshots.size());

      final AWSAdminBackupSnapshot awsResult = (AWSAdminBackupSnapshot) snapshots.get(0);
      assertEquals(awsAdminBackupSnapshot.getId(), awsResult.getId());
      assertEquals(Status.ACTIVE, awsResult.getStatus());

      final Optional<NDSOrphanedItem> orphanedItemOpt =
          getOrphanedItemDao()
              .findByIdAndCloudProviderAndType(
                  awsResult.getCloudProviderSnapshotId(),
                  awsResult.getCloudProvider(),
                  AWSOrphanedItem.Type.EMERGENCY_SNAPSHOT);
      assertTrue(orphanedItemOpt.isPresent());
      assertEquals(awsResult.getCloudProviderSnapshotId(), orphanedItemOpt.get().getId());
    } finally {
      waitForPlanRollbackSuccess(createAdminSnapshotPlan);
      final Optional<AdminBackupSnapshot> snapshotAfterRollback =
          getNDSAdminBackupSnapshotSvc()
              .findNonDeletedSnapshot(getGroupId(), clusterDescription.getName(), instanceId);
      assertTrue(snapshotAfterRollback.isPresent());
      assertEquals(Status.FAILED, snapshotAfterRollback.get().getStatus());
    }
  }
}
