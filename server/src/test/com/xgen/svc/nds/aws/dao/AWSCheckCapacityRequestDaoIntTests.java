package com.xgen.svc.nds.aws.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.nds.aws._private.dao.AWSCheckCapacityRequestDao;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest.CapacityHold;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest.CheckState;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckResult.CheckStatus;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AWSCheckCapacityRequestDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private AWSCheckCapacityRequestDao awsCheckCapacityRequestDao;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
  }

  @Test
  public void testFindByCheckResultCapacityReservationId() {
    final ObjectId groupId = new ObjectId();
    { // no CheckResult
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          new CheckCapacityRequest<>(
              CloudProvider.AWS,
              new ObjectId(),
              List.of(getDefaultAWSInstanceCapacitySpec()),
              new CapacityHold(DateUtils.addDays(new Date(), 3)));
      awsCheckCapacityRequestDao.save(request);
      awsCheckCapacityRequestDao.setState(request.getId(), CheckState.COMPLETE);
    }
    { // with CheckResult
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          new CheckCapacityRequest<>(
              CloudProvider.AWS,
              groupId,
              List.of(getDefaultAWSInstanceCapacitySpec()),
              new CapacityHold(DateUtils.addDays(new Date(), 3)));
      awsCheckCapacityRequestDao.save(request);
      awsCheckCapacityRequestDao.setCheckResults(
          request.getId(),
          List.of(new AWSCheckResult(CheckStatus.SUCCESS, "reservationId0", null)));
      awsCheckCapacityRequestDao.setState(request.getId(), CheckState.COMPLETE);
    }
    { // with 'wrong' CheckResult
      final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> request =
          new CheckCapacityRequest<>(
              CloudProvider.AWS,
              groupId,
              List.of(getDefaultAWSInstanceCapacitySpec()),
              new CapacityHold(DateUtils.addDays(new Date(), 3)));
      awsCheckCapacityRequestDao.save(request);
      awsCheckCapacityRequestDao.setCheckResults(
          request.getId(),
          List.of(new AWSCheckResult(CheckStatus.SUCCESS, "reservationId1", null)));
      awsCheckCapacityRequestDao.setState(request.getId(), CheckState.COMPLETE);
    }
    final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>> awsResults =
        awsCheckCapacityRequestDao.findByCheckResultCapacityReservationId("reservationId0");
    assertEquals(1, awsResults.size());
  }

  public static AWSInstanceCapacitySpec getDefaultAWSInstanceCapacitySpec() {
    return new AWSInstanceCapacitySpec(
        new ObjectId(), AWSRegionName.US_EAST_1, "us-east-1a", "t4g.small", 1);
  }
}
