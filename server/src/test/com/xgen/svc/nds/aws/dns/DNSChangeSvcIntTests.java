package com.xgen.svc.nds.aws.dns;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.amazonaws.services.route53.model.Change;
import com.amazonaws.services.route53.model.ChangeAction;
import com.amazonaws.services.route53.model.RRType;
import com.amazonaws.services.route53.model.ResourceRecord;
import com.amazonaws.services.route53.model.ResourceRecordSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.dns._public.model.DNSChangeItem;
import com.xgen.cloud.nds.dns._public.model.DNSChangeItem.Record;
import com.xgen.cloud.nds.dns._public.model.DNSChangeItem.State;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.RandomStringUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DNSChangeSvcIntTests extends AWSExternalIntTest {

  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private DNSChangeQueueDao _queueDao;
  @Inject private DNSBulkChangeDao _dnsBulkChangeDao;
  @Inject private AppSettings _appSettings;
  @Inject private AWSAccountDao _awsAccountDao;

  private AWSApiSvc _awsApiSvcSpy;
  private DNSChangeSvc _changeSvc;

  private List<String> _hostnamesToClean;
  private ObjectId _awsAccountId;

  private static final String VALUE = "127.0.0.1";
  private static final Logger LOG = LoggerFactory.getLogger(DNSChangeSvcIntTests.class);

  @Override
  @Before
  public void setUp() throws Exception {
    _awsApiSvcSpy = spy(_awsApiSvc);
    _changeSvc =
        new DNSChangeSvc(_appSettings, _awsAccountDao, _awsApiSvcSpy, _queueDao, _dnsBulkChangeDao);

    // Don't run DNSChangeSvc background thread for this test as having it run concurrently with
    // the usage of DNSChangeSvc in this test will cause test failures due to the semaphore in
    // DNSChangeSvc. The background thread is needed for other external int tests to clean up
    // DNS records.
    super.setUp(DEFAULT_TEST_REGION, false);
    _hostnamesToClean = new ArrayList<>();
    _awsAccountId =
        getAwsAccountDao()
            .findDNSAccount()
            .orElseThrow(() -> new IllegalStateException("Expected to find an AWSAccount for DNS"))
            .getId();
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    final List<ResourceRecordSet> toClean = new ArrayList<>();
    for (String hostname : _hostnamesToClean) {
      final Optional<ResourceRecordSet> record =
          _awsApiSvc.findDNSRecord(
              _awsAccountId,
              LOG,
              NDSSettings.getRoute53PublicHostedZoneForInternet(
                  getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
              hostname,
              RRType.A);

      record.ifPresent(toClean::add);
      Thread.sleep(500);
    }

    final int batchSize = 5;
    for (int n = 0; n < toClean.size(); n += batchSize) {
      _awsApiSvc.changeDNSRecords(
          _awsAccountId,
          LOG,
          NDSSettings.getRoute53PublicHostedZoneForInternet(
              getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
          toClean.subList(n, Math.min(n + batchSize, toClean.size())).stream()
              .map(r -> new Change(ChangeAction.DELETE, r))
              .collect(Collectors.toList()));
    }

    super.tearDown();
  }

  @Test
  public void testSingleDeleteNoValues() {
    final String hostname = getRandomHostname();

    final ResourceRecordSet resourceRecordSet =
        new ResourceRecordSet()
            .withName(hostname)
            .withType(RRType.A)
            .withResourceRecords(new ResourceRecord(VALUE))
            .withTTL(AWSNDSDefaults.INSTANCE_DNS_TTL);
    final Change change =
        new Change().withAction(ChangeAction.CREATE).withResourceRecordSet(resourceRecordSet);

    _hostnamesToClean.add(hostname);
    _awsApiSvc.changeDNSRecords(
        _awsAccountId,
        LOG,
        NDSSettings.getRoute53PublicHostedZoneForInternet(
            getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
        List.of(change));

    final DNSChangeItem item =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            Record.forDelete(RRType.A, hostname));
    _queueDao.saveNewItem(item);

    _changeSvc.run();

    final DNSChangeItem pendingItem = _queueDao.find(item.getId()).get();
    assertEquals(State.PENDING, pendingItem.getState());
    assertEquals(List.of(VALUE), pendingItem.getRecord().getValues());

    final boolean result =
        waitForCondition(
            Duration.ofMinutes(15),
            () -> {
              _changeSvc.run();
              return _queueDao.find(item.getId()).get().getState() == DNSChangeItem.State.INSYNC;
            });

    assertTrue(result);
  }

  @Test
  public void testSingleDeleteNotNeeded_WithValues() {
    final String hostname = getRandomHostname();

    final DNSChangeItem item =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            new Record(ChangeAction.DELETE, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(item);

    _changeSvc.run();

    assertEquals(DNSChangeItem.State.INSYNC, _queueDao.find(item.getId()).get().getState());
  }

  @Test
  public void testSingleDeleteNotNeeded_WithoutValues() {
    final String hostname = getRandomHostname();

    final DNSChangeItem item =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            Record.forDelete(RRType.A, hostname));
    _queueDao.saveNewItem(item);

    _changeSvc.run();

    assertEquals(DNSChangeItem.State.INSYNC, _queueDao.find(item.getId()).get().getState());
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testMultipleDeleteNotNeeded() {
    final List<ObjectId> itemIds = new ArrayList<>();
    final int numChangeItems = 6;

    for (int n = 0; n < numChangeItems; n++) {
      final String hostname = getRandomHostname();
      final DNSChangeItem item =
          new DNSChangeItem(
              NDSSettings.getRoute53PublicHostedZoneForInternet(
                  getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
              new Record(ChangeAction.DELETE, RRType.A, hostname, List.of(VALUE)));
      _queueDao.saveNewItem(item);
      itemIds.add(item.getId());
    }

    _changeSvc.run();

    // Assert a bulk request was submitted by verifying changeDNSRecords was only called once
    final ArgumentCaptor<List<Change>> changesArgumentCaptor = ArgumentCaptor.forClass(List.class);
    verify(_awsApiSvcSpy, times(1))
        .changeDNSRecords(any(), any(), anyString(), changesArgumentCaptor.capture());
    assertEquals(numChangeItems, changesArgumentCaptor.getValue().size());

    itemIds.forEach(
        itemId ->
            assertEquals(DNSChangeItem.State.INSYNC, _queueDao.find(itemId).get().getState()));
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testMultipleDeleteNotNeededAndCreate_Bulk_Request_Is_Retried() {
    final int numChangeItemsDeleted = 2;
    final List<ObjectId> deletedItemIds =
        IntStream.range(0, numChangeItemsDeleted)
            .mapToObj(i -> createDNSChangeItem(ChangeAction.DELETE, List.of(VALUE)))
            .peek(item -> _queueDao.saveNewItem(item))
            .map(DNSChangeItem::getId)
            .toList();

    final int numChangeItemsCreated = 4;
    final List<ObjectId> createdItemIds =
        IntStream.range(0, numChangeItemsCreated)
            .mapToObj(i -> createDNSChangeItem(ChangeAction.CREATE, List.of("127.0.0." + i)))
            .peek(item -> _queueDao.saveNewItem(item))
            .map(DNSChangeItem::getId)
            .toList();

    _changeSvc.run();

    // Assert a bulk request was submitted by verifying changeDNSRecords was only called twice
    final ArgumentCaptor<List<Change>> changesArgumentCaptor = ArgumentCaptor.forClass(List.class);
    verify(_awsApiSvcSpy, times(2))
        .changeDNSRecords(any(), any(), anyString(), changesArgumentCaptor.capture());

    // First Bulk Request will have all change items
    assertEquals(
        numChangeItemsDeleted + numChangeItemsCreated,
        changesArgumentCaptor.getAllValues().get(0).size());
    // Second Bulk Request only has created change items
    assertEquals(numChangeItemsCreated, changesArgumentCaptor.getAllValues().get(1).size());

    deletedItemIds.forEach(
        itemId ->
            assertEquals(DNSChangeItem.State.INSYNC, _queueDao.find(itemId).get().getState()));

    createdItemIds.forEach(
        itemId -> {
          assertNotEquals(DNSChangeItem.State.NEW, _queueDao.find(itemId).get().getState());
          assertNotEquals(DNSChangeItem.State.ERROR, _queueDao.find(itemId).get().getState());
        });

    final boolean result =
        waitForCondition(
            Duration.ofMinutes(15),
            () -> {
              _changeSvc.run();

              return createdItemIds.stream()
                  .allMatch(
                      itemId ->
                          _queueDao.find(itemId).get().getState() == DNSChangeItem.State.INSYNC);
            });

    assertTrue(result);
  }

  @Test
  public void testUpsertNotNeeded() {
    final String hostname = getRandomHostname();

    final ResourceRecordSet resourceRecordSet =
        new ResourceRecordSet()
            .withName(hostname)
            .withType(RRType.A)
            .withResourceRecords(new ResourceRecord(VALUE))
            .withTTL(AWSNDSDefaults.INSTANCE_DNS_TTL);
    final Change change =
        new Change().withAction(ChangeAction.UPSERT).withResourceRecordSet(resourceRecordSet);

    _hostnamesToClean.add(hostname);
    _awsApiSvc.changeDNSRecords(
        _awsAccountId,
        LOG,
        NDSSettings.getRoute53PublicHostedZoneForInternet(
            getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
        List.of(change));

    final DNSChangeItem item =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            new Record(ChangeAction.UPSERT, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(item);

    _changeSvc.run();

    assertEquals(DNSChangeItem.State.INSYNC, _queueDao.find(item.getId()).get().getState());
  }

  @Test
  public void testSingleBatch() throws InterruptedException {
    testBatch(5, 1);
  }

  @Test
  public void testMultipleBatch() throws InterruptedException {
    testBatch(15, 100);
  }

  @Test
  public void testBatchContainingError() {
    String hostname = getRandomHostname();
    final DNSChangeItem itemGood1 =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            new Record(ChangeAction.CREATE, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(itemGood1);
    _hostnamesToClean.add(hostname);

    hostname = getRandomHostname();
    final DNSChangeItem itemBad =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            new Record(ChangeAction.DELETE, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(itemBad);
    _hostnamesToClean.add(hostname);

    hostname = getRandomHostname();
    final DNSChangeItem itemGood2 =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            new Record(ChangeAction.CREATE, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(itemGood2);
    _hostnamesToClean.add(hostname);

    _changeSvc.run();

    assertEquals(DNSChangeItem.State.PENDING, _queueDao.find(itemGood1.getId()).get().getState());
    assertEquals(DNSChangeItem.State.INSYNC, _queueDao.find(itemBad.getId()).get().getState());
    assertEquals(DNSChangeItem.State.PENDING, _queueDao.find(itemGood2.getId()).get().getState());

    final boolean result =
        waitForCondition(
            Duration.ofMinutes(15),
            () -> {
              _changeSvc.run();

              return _queueDao.find(itemGood1.getId()).get().getState()
                      == DNSChangeItem.State.INSYNC
                  && _queueDao.find(itemGood2.getId()).get().getState()
                      == DNSChangeItem.State.INSYNC;
            });

    assertTrue(result);
  }

  @Test
  public void testBatchContainingInvalidZoneID() {
    String hostname = getRandomHostname();
    final DNSChangeItem itemBad =
        new DNSChangeItem(
            "invalidZoneId", new Record(ChangeAction.UPSERT, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(itemBad);
    _hostnamesToClean.add(hostname);

    hostname = getRandomHostname();
    final DNSChangeItem itemGood =
        new DNSChangeItem(
            NDSSettings.getRoute53PublicHostedZoneForInternet(
                getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
            new Record(ChangeAction.CREATE, RRType.A, hostname, List.of(VALUE)));
    _queueDao.saveNewItem(itemGood);
    _hostnamesToClean.add(hostname);

    _changeSvc.run();

    assertEquals(DNSChangeItem.State.ERROR, _queueDao.find(itemBad.getId()).get().getState());
    assertEquals(DNSChangeItem.State.PENDING, _queueDao.find(itemGood.getId()).get().getState());

    final boolean result =
        waitForCondition(
            Duration.ofMinutes(15),
            () -> {
              _changeSvc.run();

              return _queueDao.find(itemGood.getId()).get().getState()
                  == DNSChangeItem.State.INSYNC;
            });

    assertTrue(result);
  }

  private void testBatch(final int pCount, final int pValueCount) throws InterruptedException {
    final List<ObjectId> itemIds = new ArrayList<>();

    // Create large values so it is easier to hit the batch size
    final List<String> values = new ArrayList<>();
    for (int n = 0; n < pValueCount; n++) {
      values.add("127.0.0." + n);
    }

    for (int n = 0; n < pCount; n++) {
      final String hostname = getRandomHostname();
      final DNSChangeItem item =
          new DNSChangeItem(
              NDSSettings.getRoute53PublicHostedZoneForInternet(
                  getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
              new Record(ChangeAction.CREATE, RRType.A, hostname, values));
      _queueDao.saveNewItem(item);
      itemIds.add(item.getId());
      _hostnamesToClean.add(hostname);
    }

    _changeSvc.run();

    itemIds.forEach(
        itemId -> {
          assertNotEquals(DNSChangeItem.State.NEW, _queueDao.find(itemId).get().getState());
          assertNotEquals(DNSChangeItem.State.ERROR, _queueDao.find(itemId).get().getState());
        });

    final boolean result =
        waitForCondition(
            Duration.ofMinutes(15),
            () -> {
              _changeSvc.run();

              return itemIds.stream()
                  .allMatch(
                      itemId ->
                          _queueDao.find(itemId).get().getState() == DNSChangeItem.State.INSYNC);
            });

    assertTrue(result);
  }

  private String getRandomHostname() {
    return RandomStringUtils.randomAlphabetic(50).toLowerCase()
        + "."
        + NDSSettings.getCrossCloudInstanceDomainName(getAppSettings());
  }

  private DNSChangeItem createDNSChangeItem(
      final ChangeAction pChangeAction, final List<String> pValues) {
    return new DNSChangeItem(
        NDSSettings.getRoute53PublicHostedZoneForInternet(
            getAppSettings(), CloudProvider.AWS, InstanceHostname.SubdomainLevel.MONGODB),
        new Record(pChangeAction, RRType.A, getRandomHostname(), pValues));
  }
}
