package com.xgen.svc.nds.aws.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.nds.aws._private.dao.AWSTenantEndpointServiceDeploymentDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSTenantEndpointService;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSTenantEndpointServiceDeployment;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService.Status;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.CloudProviderContainerTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AWSTenantEndpointServiceDeploymentDaoIntTests extends JUnit5BaseSvcTest {
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AWSTenantEndpointServiceDeploymentDao _tenantEndpointServiceDeploymentDao;

  private ObjectId _groupId;
  private ObjectId _containerId;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _ndsGroupDao.ensureIndexes();
    final Pair<ObjectId, ObjectId> pair =
        CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
            _ndsGroupDao, _ndsGroupSvc);
    _groupId = pair.getLeft();
    _containerId = pair.getRight();
  }

  @Test
  public void testFindContainerWithDeployment() {
    // Add extra container to verify correct container matched.
    _ndsGroupDao.addCloudContainer(
        _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    // Verify no containers with deployments.
    assertTrue(_tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isEmpty());

    // Test find container with deployment.
    {
      final AWSTenantEndpointServiceDeployment deployment =
          TenantPrivateNetworkingModelTestFactory.getAWSTenantEndpointServiceDeployment();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));

      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());
    }

    // Test > 1 container with deployment throws exception.
    {
      final ObjectId containerId =
          _ndsGroupDao.addCloudContainer(
              _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
      final AWSTenantEndpointServiceDeployment deployment =
          TenantPrivateNetworkingModelTestFactory.getAWSTenantEndpointServiceDeployment();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, containerId, deployment));

      try {
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId);
        fail();
      } catch (Exception pE) {
        assertTrue(pE instanceof IllegalStateException);
      }
    }
  }

  @Test
  public void testRequestDeploymentDelete() throws InterruptedException {
    // Test wrong container id doesn't request deletion on deployment.
    {
      final Pair<ObjectId, ObjectId> pair =
          CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
              _ndsGroupDao, _ndsGroupSvc);
      final ObjectId groupId = pair.getLeft();
      final ObjectId containerId = pair.getRight();

      // Add extra container to verify correct container matched.
      final ObjectId otherContainerId =
          _ndsGroupDao.addCloudContainer(
              groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

      final AWSTenantEndpointServiceDeployment deployment =
          new AWSTenantEndpointServiceDeployment();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(groupId, containerId, deployment));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(groupId).isPresent());

      assertFalse(
          _tenantEndpointServiceDeploymentDao.requestDeploymentDelete(groupId, otherContainerId));
      assertNull(
          _ndsGroupDao
              .find(groupId)
              .flatMap(ndsGroup -> ndsGroup.getCloudProviderContainer(containerId))
              .flatMap(CloudProviderContainer::getTenantEndpointServiceDeployment)
              .get()
              .getDeleteRequestedDate());
    }

    // Test successfully requested deletion on deployment on desired container.
    {
      final Pair<ObjectId, ObjectId> pair =
          CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
              _ndsGroupDao, _ndsGroupSvc);
      final ObjectId groupId = pair.getLeft();
      final ObjectId containerId = pair.getRight();

      // Add extra container to verify correct container matched.
      _ndsGroupDao.addCloudContainer(
          groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

      final AWSTenantEndpointServiceDeployment deployment =
          new AWSTenantEndpointServiceDeployment();
      assertTrue(deployment.getEndpointServices().isEmpty());
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(groupId, containerId, deployment));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(groupId).isPresent());

      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(groupId)
              .get()
              .getCloudProviderContainer(containerId)
              .get()
              .getLastUpdate();

      // sleep for 1 ms to ensure that the last update date is updated with a new timestamp
      Thread.sleep(1);

      assertTrue(_tenantEndpointServiceDeploymentDao.requestDeploymentDelete(groupId, containerId));

      final CloudProviderContainer container =
          _ndsGroupDao
              .find(groupId)
              .flatMap(ndsGroup -> ndsGroup.getCloudProviderContainer(containerId))
              .get();
      assertNotNull(container.getTenantEndpointServiceDeployment().get().getDeleteRequestedDate());
      // Verify last updated date changed.
      assertTrue(
          container.getLastUpdate().after(lastUpdatedDate),
          () ->
              String.format(
                  "last update date has not been updated: prev=%s, after=%s",
                  lastUpdatedDate, container.getLastUpdate()));
    }
  }

  @Test
  public void testDeleteDeployment() throws InterruptedException {
    // Test wrong container id doesn't delete deployment.
    {
      final Pair<ObjectId, ObjectId> pair =
          CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
              _ndsGroupDao, _ndsGroupSvc);
      final ObjectId groupId = pair.getLeft();
      final ObjectId containerId = pair.getRight();

      final AWSTenantEndpointServiceDeployment deployment =
          new AWSTenantEndpointServiceDeployment();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(groupId, containerId, deployment));

      assertFalse(_tenantEndpointServiceDeploymentDao.deleteDeployment(groupId, oid(999)));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(groupId).isPresent());
    }

    // Test fail to remove container with deployment with endpoint services.
    {
      final Pair<ObjectId, ObjectId> pair =
          CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
              _ndsGroupDao, _ndsGroupSvc);
      final ObjectId groupId = pair.getLeft();
      final ObjectId containerId = pair.getRight();

      final AWSTenantEndpointServiceDeployment deployment =
          TenantPrivateNetworkingModelTestFactory.getAWSTenantEndpointServiceDeployment();
      assertFalse(deployment.getEndpointServices().isEmpty());
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(groupId, containerId, deployment));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(groupId).isPresent());
      assertFalse(_tenantEndpointServiceDeploymentDao.deleteDeployment(groupId, containerId));
    }

    // Test successful deployment deletion.
    {
      // Add extra container to verify correct container matched.
      final ObjectId otherContainerId =
          _ndsGroupDao.addCloudContainer(
              _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

      final AWSTenantEndpointServiceDeployment deployment =
          new AWSTenantEndpointServiceDeployment();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));

      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();

      // Sleep so our date comparison is always after
      Thread.sleep(1000);

      assertTrue(_tenantEndpointServiceDeploymentDao.deleteDeployment(_groupId, _containerId));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isEmpty());

      // Verify last updated date changed.
      assertTrue(
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate()
              .after(lastUpdatedDate));

      // Ensure other container not deleted.
      assertTrue(
          _ndsGroupDao
              .find(_groupId)
              .flatMap(ndsGroup -> ndsGroup.getCloudProviderContainer(otherContainerId))
              .isPresent());
    }
  }

  @Test
  public void testSetNumDesiredEndpointServices() {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    assertTrue(_tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isEmpty());

    // Add deployment to desired container.
    final AWSTenantEndpointServiceDeployment deployment =
        new AWSTenantEndpointServiceDeployment(1, List.of());
    _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment);
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Test wrong container id fails.
    assertFalse(
        _tenantEndpointServiceDeploymentDao.setNumDesiredEndpointServices(
            _groupId, otherContainerId, 1));

    // Test failed if val < current num desired endpoint services.
    assertFalse(
        _tenantEndpointServiceDeploymentDao.setNumDesiredEndpointServices(
            _groupId, _containerId, 0));
    // Test failed if val = current num desired endpoint services.
    assertFalse(
        _tenantEndpointServiceDeploymentDao.setNumDesiredEndpointServices(
            _groupId, _containerId, 1));

    // Test success if val > current num desired endpoint services.
    final Date lastUpdatedDate =
        _ndsGroupDao
            .find(_groupId)
            .get()
            .getCloudProviderContainer(_containerId)
            .get()
            .getLastUpdate();

    assertTrue(
        _tenantEndpointServiceDeploymentDao.setNumDesiredEndpointServices(
            _groupId, _containerId, 2));
    // Verify last updated date changed.
    assertTrue(
        _ndsGroupDao
            .find(_groupId)
            .get()
            .getCloudProviderContainer(_containerId)
            .get()
            .getLastUpdate()
            .after(lastUpdatedDate));
  }

  @Test
  public void testAddEndpointService() {
    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    // Test add endpoint service fails if deployment is requested to be deleted.
    {
      final Pair<ObjectId, ObjectId> pair =
          CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
              _ndsGroupDao, _ndsGroupSvc);
      final ObjectId groupId = pair.getLeft();
      final ObjectId containerId = pair.getRight();

      // Add extra container to verify correct container matched.
      _ndsGroupDao.addCloudContainer(
          groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

      final AWSTenantEndpointServiceDeployment deployment =
          AWSTenantEndpointServiceDeployment.builder()
              .deleteRequestedDate(new Date())
              .numDesiredEndpointServices(1)
              .build();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(groupId, containerId, deployment));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(groupId).isPresent());

      assertFalse(
          _tenantEndpointServiceDeploymentDao.addEndpointService(
              groupId, containerId, endpointService));
      assertTrue(
          _ndsGroupDao
              .find(groupId)
              .get()
              .getCloudProviderContainer(containerId)
              .get()
              .getTenantEndpointServices()
              .isEmpty());
    }

    // Test no remaining capacity in num desired endpoint services.
    {
      final Pair<ObjectId, ObjectId> pair =
          CloudProviderContainerTestUtils.setupGroupWithSingleAWSContainer(
              _ndsGroupDao, _ndsGroupSvc);
      final ObjectId groupId = pair.getLeft();
      final ObjectId containerId = pair.getRight();

      // Add extra container to verify correct container matched.
      _ndsGroupDao.addCloudContainer(
          groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

      final AWSTenantEndpointServiceDeployment deployment =
          TenantPrivateNetworkingModelTestFactory.getAWSTenantEndpointServiceDeployment();
      assertEquals(1, deployment.getNumDesiredEndpointServices());
      assertEquals(
          deployment.getNumDesiredEndpointServices(), deployment.getEndpointServices().size());
      assertTrue(
          _tenantEndpointServiceDeploymentDao.createDeployment(groupId, containerId, deployment));
      assertTrue(
          _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(groupId).isPresent());

      assertFalse(
          _tenantEndpointServiceDeploymentDao.addEndpointService(
              groupId, containerId, endpointService));

      // Assert endpoint services unchanged.
      assertEquals(
          deployment.getEndpointServices(),
          _ndsGroupDao
              .find(groupId)
              .get()
              .getCloudProviderContainer(containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointServiceDeployment deployment =
        new AWSTenantEndpointServiceDeployment(1, List.of());
    assertEquals(1, deployment.getNumDesiredEndpointServices());
    assertEquals(0, deployment.getEndpointServices().size());
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Test fail to add endpoint service to container w/o deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.addEndpointService(
              _groupId, otherContainerId, endpointService));
      assertTrue(
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices()
              .isEmpty());
    }

    // successful add
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.addEndpointService(
              _groupId, _containerId, endpointService));

      final CloudProviderContainer container =
          _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(List.of(endpointService), container.getTenantEndpointServices());
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testDeleteEndpointService() {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    // Add additional endpoint service to verify only desired endpoint service is deleted.
    final AWSTenantEndpointService otherEndpointService = new AWSTenantEndpointService();

    final AWSTenantEndpointServiceDeployment deployment =
        AWSTenantEndpointServiceDeployment.builder().numDesiredEndpointServices(2).build();
    assertTrue(deployment.getEndpointServices().isEmpty());
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Add endpoint services to deployment.
    _tenantEndpointServiceDeploymentDao.addEndpointService(_groupId, _containerId, endpointService);
    _tenantEndpointServiceDeploymentDao.addEndpointService(
        _groupId, _containerId, otherEndpointService);

    assertEquals(
        List.of(endpointService, otherEndpointService),
        _ndsGroupDao
            .find(_groupId)
            .get()
            .getCloudProviderContainer(_containerId)
            .get()
            .getTenantEndpointServices());

    // Test fail to delete endpoint service on container w/o deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.deleteEndpointService(
              _groupId, otherContainerId, endpointService.getId()));

      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.deleteEndpointService(
              _groupId, _containerId, oid(999)));

      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Test successful deletion.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();

      assertTrue(
          _tenantEndpointServiceDeploymentDao.deleteEndpointService(
              _groupId, _containerId, endpointService.getId()));

      final CloudProviderContainer container =
          _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(List.of(otherEndpointService), container.getTenantEndpointServices());
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testRequestEndpointServiceDelete() throws Exception {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    assertFalse(endpointService.isDeleteRequested());
    // Additional endpoint service to verify only desired endpoint service is updated.
    final AWSTenantEndpointService otherEndpointService = new AWSTenantEndpointService();
    assertFalse(otherEndpointService.isDeleteRequested());

    final AWSTenantEndpointServiceDeployment deployment =
        AWSTenantEndpointServiceDeployment.builder()
            .numDesiredEndpointServices(2)
            .endpointServices(List.of(endpointService, otherEndpointService))
            .build();
    assertEquals(2, deployment.getEndpointServices().size());
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Test update on container with no deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.requestEndpointServiceDelete(
              _groupId, otherContainerId, endpointService.getId()));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      // Verify delete requested is unchanged.
      assertEquals(
          endpointService.isDeleteRequested(), reloadedEndpointService.isDeleteRequested());
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.requestEndpointServiceDelete(
              _groupId, _containerId, oid(999)));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Verify desired field on endpoint service & last update date on container is changed.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.requestEndpointServiceDelete(
              _groupId, _containerId, endpointService.getId()));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(2, container.getTenantEndpointServices().size());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertTrue(reloadedEndpointService.isDeleteRequested());

      // Verify other endpoint service unchanged.
      final AWSTenantEndpointService reloadedOtherEndpointService =
          container.getTenantEndpointServices().get(1);
      assertEquals(
          otherEndpointService.isDeleteRequested(),
          reloadedOtherEndpointService.isDeleteRequested());

      // Verify last updated date changed.
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testUnsetEndpointServiceNeedsUpdateAfter() throws Exception {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final Date lastNeedsUpdateAfter = new Date();
    final AWSTenantEndpointService endpointService =
        new AWSTenantEndpointService.Builder().needsUpdateAfter(lastNeedsUpdateAfter).build();
    // Additional endpoint service to verify only desired endpoint service is updated.
    final AWSTenantEndpointService otherEndpointService =
        new AWSTenantEndpointService.Builder().needsUpdateAfter(lastNeedsUpdateAfter).build();

    final AWSTenantEndpointServiceDeployment deployment =
        new AWSTenantEndpointServiceDeployment(2, List.of(endpointService, otherEndpointService));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Test update on container with no deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.unsetEndpointServiceNeedsUpdateAfter(
              _groupId, otherContainerId, endpointService.getId(), lastNeedsUpdateAfter));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(
          List.of(endpointService, otherEndpointService), container.getTenantEndpointServices());
      // Verify get needs updated unchanged.
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertEquals(
          endpointService.getNeedsUpdateAfter(), reloadedEndpointService.getNeedsUpdateAfter());
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.unsetEndpointServiceNeedsUpdateAfter(
              _groupId, _containerId, oid(999), lastNeedsUpdateAfter));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Test last needs update after is different
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.unsetEndpointServiceNeedsUpdateAfter(
              _groupId, _containerId, endpointService.getId(), new Date(0)));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Verify needs update after field on endpoint service & last update date on container is
    // changed.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.unsetEndpointServiceNeedsUpdateAfter(
              _groupId, _containerId, endpointService.getId(), lastNeedsUpdateAfter));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(2, container.getTenantEndpointServices().size());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertEquals(Optional.empty(), reloadedEndpointService.getNeedsUpdateAfter());

      // Verify other endpoint service unchanged.
      final AWSTenantEndpointService reloadedOtherEndpointService =
          container.getTenantEndpointServices().get(1);
      assertEquals(
          otherEndpointService.getNeedsUpdateAfter(),
          reloadedOtherEndpointService.getNeedsUpdateAfter());

      // Verify last updated date changed.
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testSetEndpointServiceStatus() throws Exception {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    assertEquals(Status.INITIATING, endpointService.getStatus());
    // Additional endpoint service to verify only desired endpoint service is updated.
    final AWSTenantEndpointService otherEndpointService = new AWSTenantEndpointService();
    assertEquals(Status.INITIATING, otherEndpointService.getStatus());

    final AWSTenantEndpointServiceDeployment deployment =
        AWSTenantEndpointServiceDeployment.builder()
            .numDesiredEndpointServices(2)
            .endpointServices(List.of(endpointService, otherEndpointService))
            .build();
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Test update endpoint service status on container with no deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.setEndpointServiceStatus(
              _groupId, otherContainerId, endpointService.getId(), Status.AVAILABLE));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(
          List.of(endpointService, otherEndpointService), container.getTenantEndpointServices());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);

      // Verify status is unchanged.
      assertEquals(endpointService.getStatus(), reloadedEndpointService.getStatus());
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.setEndpointServiceStatus(
              _groupId, _containerId, oid(999), Status.AVAILABLE));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Test successful update.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.setEndpointServiceStatus(
              _groupId, _containerId, endpointService.getId(), Status.AVAILABLE));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(2, container.getTenantEndpointServices().size());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertEquals(Status.AVAILABLE, reloadedEndpointService.getStatus());

      // Verify other endpoint service unchanged.
      final AWSTenantEndpointService reloadedOtherEndpointService =
          container.getTenantEndpointServices().get(1);
      assertEquals(Status.INITIATING, reloadedOtherEndpointService.getStatus());

      // Verify last updated date changed.
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testSetLoadBalancingDeployment() {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    // Additional endpoint service to verify only desired endpoint service is updated.
    final AWSTenantEndpointService otherEndpointService = new AWSTenantEndpointService();

    final AWSTenantEndpointServiceDeployment deployment =
        AWSTenantEndpointServiceDeployment.builder()
            .numDesiredEndpointServices(2)
            .endpointServices(List.of(endpointService, otherEndpointService))
            .build();
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    final ObjectId loadBalancingDeploymentId = new ObjectId();
    final String loadBalancerArn = "load-balancer-arn";

    // Test set LB deployment on container with no deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.setLoadBalancingDeployment(
              _groupId,
              otherContainerId,
              endpointService.getId(),
              loadBalancingDeploymentId,
              loadBalancerArn));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(
          List.of(endpointService, otherEndpointService), container.getTenantEndpointServices());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);

      // Verify LB deployment fields are unchanged.
      assertEquals(
          endpointService.getLoadBalancingDeploymentId(),
          reloadedEndpointService.getLoadBalancingDeploymentId());
      assertEquals(
          endpointService.getLoadBalancerArn(), reloadedEndpointService.getLoadBalancerArn());
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.setLoadBalancingDeployment(
              _groupId, _containerId, oid(999), loadBalancingDeploymentId, loadBalancerArn));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Test successful update.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.setLoadBalancingDeployment(
              _groupId,
              _containerId,
              endpointService.getId(),
              loadBalancingDeploymentId,
              loadBalancerArn));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(2, container.getTenantEndpointServices().size());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertEquals(
          Optional.of(loadBalancingDeploymentId),
          reloadedEndpointService.getLoadBalancingDeploymentId());
      assertEquals(Optional.of(loadBalancerArn), reloadedEndpointService.getLoadBalancerArn());

      // Verify other endpoint service unchanged.
      final AWSTenantEndpointService reloadedOtherEndpointService =
          container.getTenantEndpointServices().get(1);
      // Verify LB deployment fields are unchanged.
      assertEquals(
          otherEndpointService.getLoadBalancingDeploymentId(),
          reloadedOtherEndpointService.getLoadBalancingDeploymentId());
      assertEquals(
          otherEndpointService.getLoadBalancerArn(),
          reloadedOtherEndpointService.getLoadBalancerArn());

      // Verify last updated date changed.
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testSetProvisionedFields() {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    // Additional endpoint service to verify only desired endpoint service is updated.
    final AWSTenantEndpointService otherEndpointService = new AWSTenantEndpointService();

    final AWSTenantEndpointServiceDeployment deployment =
        AWSTenantEndpointServiceDeployment.builder()
            .numDesiredEndpointServices(2)
            .endpointServices(List.of(endpointService, otherEndpointService))
            .build();
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    final String endpointServiceName = "endpoint-service-name";
    final String endpointServiceId = "endpoint-service-id";

    // Test set provisioned fields on container with no deployment.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.setProvisionedFields(
              _groupId,
              otherContainerId,
              endpointService.getId(),
              endpointServiceName,
              endpointServiceId,
              null));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(
          List.of(endpointService, otherEndpointService), container.getTenantEndpointServices());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);

      // Verify provisioned fields are unchanged.
      assertEquals(
          endpointService.getEndpointServiceName(),
          reloadedEndpointService.getEndpointServiceName());
      assertEquals(
          endpointService.getEndpointServiceId(), reloadedEndpointService.getEndpointServiceId());
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          _tenantEndpointServiceDeploymentDao.setProvisionedFields(
              _groupId, _containerId, oid(999), endpointServiceName, endpointServiceId, null));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Test successful update.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          _tenantEndpointServiceDeploymentDao.setProvisionedFields(
              _groupId,
              _containerId,
              endpointService.getId(),
              endpointServiceName,
              endpointServiceId,
              null));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(2, container.getTenantEndpointServices().size());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertEquals(
          Optional.of(endpointServiceName), reloadedEndpointService.getEndpointServiceName());
      assertEquals(Optional.of(endpointServiceId), reloadedEndpointService.getEndpointServiceId());
      assertEquals(Status.AVAILABLE, reloadedEndpointService.getStatus());

      // Verify other endpoint service unchanged.
      final AWSTenantEndpointService reloadedOtherEndpointService =
          container.getTenantEndpointServices().get(1);
      // Verify provisioned fields are unchanged.
      assertEquals(
          otherEndpointService.getEndpointServiceName(),
          reloadedOtherEndpointService.getEndpointServiceName());
      assertEquals(
          otherEndpointService.getEndpointServiceId(),
          reloadedOtherEndpointService.getEndpointServiceId());
      assertEquals(otherEndpointService.getStatus(), reloadedOtherEndpointService.getStatus());

      // Verify last updated date changed.
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  @Test
  public void testSetEndpointServiceNeedsUpdateAfter() throws Exception {
    testUpdateEndpointServiceField(
        getDaoMethod("setEndpointServiceNeedsUpdateAfter"),
        getEndpointServiceMethod("getNeedsUpdateAfter"),
        Optional.empty(),
        new Date());
  }

  @Test
  public void testSetEndpointServiceErrorMessage() throws Exception {
    testUpdateEndpointServiceField(
        getDaoMethod("setEndpointServiceErrorMessage"),
        getEndpointServiceMethod("getErrorMessage"),
        Optional.empty(),
        "errorMessage");
  }

  public <T> void testUpdateEndpointServiceField(
      final Method pMethod,
      final Method pEndpointServiceMethod,
      final T pDefaultValue,
      final T pExpectedValue)
      throws Exception {
    // Add extra container to verify correct container matched.
    final ObjectId otherContainerId =
        _ndsGroupDao.addCloudContainer(
            _groupId, new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final AWSTenantEndpointService endpointService = new AWSTenantEndpointService();
    assertEquals(pDefaultValue, pEndpointServiceMethod.invoke(endpointService));
    // Additional endpoint service to verify only desired endpoint service is updated.
    final AWSTenantEndpointService otherEndpointService = new AWSTenantEndpointService();
    assertEquals(pDefaultValue, pEndpointServiceMethod.invoke(otherEndpointService));

    final AWSTenantEndpointServiceDeployment deployment =
        AWSTenantEndpointServiceDeployment.builder()
            .numDesiredEndpointServices(2)
            .endpointServices(List.of(endpointService, otherEndpointService))
            .build();
    assertTrue(
        _tenantEndpointServiceDeploymentDao.createDeployment(_groupId, _containerId, deployment));
    assertTrue(
        _tenantEndpointServiceDeploymentDao.findContainerWithDeployment(_groupId).isPresent());

    // Test update on container with no deployment.
    {
      assertFalse(
          invokeUpdateEndpointServiceFieldDaoMethod(
              pMethod, _groupId, otherContainerId, endpointService.getId(), pExpectedValue));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(
          List.of(endpointService, otherEndpointService), container.getTenantEndpointServices());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      // Verify field value is unchanged.
      assertEquals(pDefaultValue, pEndpointServiceMethod.invoke(reloadedEndpointService));
    }

    // Test wrong endpoint service id.
    {
      assertFalse(
          invokeUpdateEndpointServiceFieldDaoMethod(
              pMethod, _groupId, _containerId, oid(999), pExpectedValue));
      assertEquals(
          List.of(endpointService, otherEndpointService),
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getTenantEndpointServices());
    }

    // Verify desired field on endpoint service & last update date on container is changed.
    {
      final Date lastUpdatedDate =
          _ndsGroupDao
              .find(_groupId)
              .get()
              .getCloudProviderContainer(_containerId)
              .get()
              .getLastUpdate();
      assertTrue(
          invokeUpdateEndpointServiceFieldDaoMethod(
              pMethod, _groupId, _containerId, endpointService.getId(), pExpectedValue));

      final AWSCloudProviderContainer container =
          (AWSCloudProviderContainer)
              _ndsGroupDao.find(_groupId).get().getCloudProviderContainer(_containerId).get();

      assertEquals(2, container.getTenantEndpointServices().size());
      final AWSTenantEndpointService reloadedEndpointService =
          container.getTenantEndpointServices().get(0);
      assertEquals(
          Optional.of(pExpectedValue), pEndpointServiceMethod.invoke(reloadedEndpointService));

      // Assert other endpoint service unchanged.
      final AWSTenantEndpointService reloadedOtherEndpointService =
          container.getTenantEndpointServices().get(1);
      assertEquals(pDefaultValue, pEndpointServiceMethod.invoke(reloadedOtherEndpointService));

      // Verify last updated date changed.
      assertTrue(container.getLastUpdate().after(lastUpdatedDate));
    }
  }

  private <T> boolean invokeUpdateEndpointServiceFieldDaoMethod(
      final Method pMethod,
      final ObjectId pGroupId,
      final ObjectId pContainerId,
      final ObjectId pEndpointServiceId,
      final T pValue)
      throws Exception {
    return (Boolean)
        pMethod.invoke(
            _tenantEndpointServiceDeploymentDao,
            pGroupId,
            pContainerId,
            pEndpointServiceId,
            pValue);
  }

  private Method getDaoMethod(final String pName) {
    return Arrays.stream(_tenantEndpointServiceDeploymentDao.getClass().getMethods())
        .filter(m -> m.getName().equals(pName))
        .findFirst()
        .get();
  }

  private Method getEndpointServiceMethod(final String pName) {
    return Arrays.stream(AWSTenantEndpointService.class.getMethods())
        .filter(m -> m.getName().equals(pName))
        .findFirst()
        .get();
  }
}
