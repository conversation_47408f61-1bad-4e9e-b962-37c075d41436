package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertTrue;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import org.junit.Test;

public class AWSDeleteInternetGatewayStepIntTests extends AWSExternalIntTest {

  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private GroupDao _groupDao;

  private void testAWSDeleteInternetGatewayStepInternal(
      final Plan pPlan, final String pVpcId, final String pIgwId) throws InterruptedException {

    final AWSDeleteInternetGatewayStep step =
        new AWSDeleteInternetGatewayStep(
            (NDSPlanContext) pPlan.getPlanContext(),
            new Step.State(
                pPlan.getId(),
                pPlan.getMoves().get(0).getId(),
                2,
                pPlan.getPlanContext().getPlanDao()),
            getContainer(),
            pVpcId,
            pIgwId,
            _awsApiSvc,
            getOrphanedItemSvc());

    waitForStepPerformDone(step);
    verifyInternetGatewayDeleted(pIgwId);
    assertTrue(step.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSDeleteInternetGatewayStep() throws InterruptedException {

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final AWSCreateVpcStep createVpcStep =
        new AWSCreateVpcStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                0,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            _awsApiSvc,
            getOrphanedItemSvc(),
            getOrphanedItemDao(),
            _groupDao);

    try {
      waitForStepPerformDone(createVpcStep);
    } catch (final Throwable t) {
      waitForStepRollbackDone(createVpcStep);
      throw t;
    }

    final AWSCreateInternetGatewayStep createIgwStep =
        new AWSCreateInternetGatewayStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                1,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            createVpcStep.getVpcId().get(),
            _awsApiSvc,
            getOrphanedItemSvc(),
            _groupDao);

    try {
      waitForStepPerformDone(createIgwStep);
    } catch (final Throwable t) {
      if (createIgwStep.getIgwId().isPresent()) {
        cleanupInternetGateway(createIgwStep.getIgwId().get());
      }
      throw t;
    }

    try {
      testAWSDeleteInternetGatewayStepInternal(
          plan,
          createVpcStep.perform().getData().getVpcId(),
          createIgwStep.perform().getData().getIgwId());
    } catch (final Throwable t) {
      cleanupInternetGateway(createIgwStep.perform().getData().getIgwId());
      throw t;
    } finally {
      waitForStepRollbackDone(createVpcStep);
    }
  }
}
