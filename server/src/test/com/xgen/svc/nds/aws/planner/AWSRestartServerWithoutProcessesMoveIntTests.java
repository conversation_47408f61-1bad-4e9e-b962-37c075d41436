package com.xgen.svc.nds.aws.planner;

import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import java.time.Duration;
import java.util.Collections;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AWSRestartServerWithoutProcessesMoveIntTests extends AWSExternalIntTest {
  @Test(timeout = 35 * 60 * 1000L)
  public void testAWSRestartServerWithoutProcessesMove_processesDisabled() throws Exception {
    testAWSRestartServerWithoutProcessesMove(true);
  }

  @Test(timeout = 35 * 60 * 1000L)
  public void testAWSRestartServerWithoutProcessesMove_processesEnabled() throws Exception {
    testAWSRestartServerWithoutProcessesMove(false);
  }

  private void testAWSRestartServerWithoutProcessesMove(final boolean pProcessesDisabled)
      throws Exception {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    plan.addMove(provisionContainerMove);

    final ProcessAutomationConfigPerClusterMove configMove =
        ProcessAutomationConfigPerClusterMove.factoryCreate(
            plan.getPlanContext(),
            getClusterDescription().getName(),
            Collections.emptyList(),
            false);
    plan.addMove(configMove);

    final ObjectId instanceId = getInstanceIds().get(0);

    getInstanceIds().stream()
        .map(
            id ->
                AWSProvisionMachineMove.testFactoryCreate(
                    plan.getPlanContext(),
                    getTags(),
                    getClusterDescription().getName(),
                    id,
                    true,
                    _testInjector))
        .peek(plan::addMove)
        .peek(
            m -> {
              m.addPredecessor(provisionContainerMove);
              configMove.addPredecessor(m);
            })
        .forEach(configMove::addPredecessor);

    getPlanDao().save(plan);

    try {
      // Wait for the cluster creation
      waitForPlanPerformSuccessWithWhitelistSync(plan);

      // Wait for agent ping
      Thread.sleep(Duration.ofMinutes(1).toMillis());

      // Then restart an instance
      final Plan restartMongoServerWithoutProcessesPlan =
          new Plan(getGroup().getId(), getPlanContextFactory());
      final AWSRestartServerWithoutProcessesMove restartMongoServerWithoutProcessesMove =
          AWSRestartServerWithoutProcessesMove.factoryCreate(
              (NDSPlanContext) restartMongoServerWithoutProcessesPlan.getPlanContext(),
              getClusterDescription().getName(),
              instanceId);
      restartMongoServerWithoutProcessesPlan.addMove(restartMongoServerWithoutProcessesMove);
      getPlanDao().save(restartMongoServerWithoutProcessesPlan);

      testRestartServerWithoutProcessesInternal(
          getNDSGroup(),
          getClusterDescription().getName(),
          instanceId,
          restartMongoServerWithoutProcessesMove,
          pProcessesDisabled);
    } finally {
      waitForPlanRollbackSuccess(plan);
    }
  }
}
