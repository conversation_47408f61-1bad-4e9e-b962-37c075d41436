package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.Tag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.nds.aws._public.model.AWSErrorCode;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class AWSCreateSlsEniStepIntTests extends BaseAWSIpv6NetworkingIntTest {
  private final List<String> createdEniIds = new ArrayList<>();
  private final List<String> createdSecurityGroupIds = new ArrayList<>();

  private static final String TEST_RS_NAME = "cluster0-shard-0";
  private static final int TEST_MEMBER_INDEX = 1;
  private static final String TEST_HOSTNAME = "test-hostname.mongodb.net";

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    setupVpcAndSubnet();
  }

  @Override
  @After
  public void tearDown() throws InterruptedException {
    // Clean up all created ENIs
    createdEniIds.forEach(
        id ->
            performUnconditionally(
                () ->
                    getAWSApiSvc()
                        .deleteEni(
                            getContainer().getAWSAccountId(),
                            getContainer().getRegion(),
                            getLogger(),
                            id)));

    // Clean up all created Security Groups
    createdSecurityGroupIds.forEach(
        id ->
            performUnconditionally(
                () ->
                    getAWSApiSvc()
                        .deleteSecurityGroup(
                            getContainer().getAWSAccountId(),
                            getContainer().getRegion(),
                            getLogger(),
                            id)));

    super.tearDown();
  }

  @Test
  public void testAWSCreateSlsEniStep() {
    final var plan = new Plan(getGroup().getId(), getPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final var slsVpcId = getVpcId().orElseThrow();
    final var subnetId = getSubnetId().orElseThrow();

    // Test 1: Create first ENI + Security Group with initial CIDR blocks
    var ipv6CidrBlocks = List.of("2600:1f18:3245:e902::/64", "2600:1f18:3245:e903::/64");
    final var firstStep =
        new AWSCreateSlsEniStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(plan.getId(), plan.getMoves().get(0).getId(), 0, getPlanDao()),
            getAWSApiSvc(),
            getContainer(),
            TEST_RS_NAME,
            TEST_MEMBER_INDEX,
            ipv6CidrBlocks,
            slsVpcId,
            subnetId,
            getOrphanedItemDao(),
            getClusterDescription(),
            TEST_HOSTNAME,
            getGroupDao());

    String firstEniId = null;
    String firstSecurityGroupId = null;
    for (int i = 0; i < 2; i++) {
      // Run twice to ensure idempotency within the same step
      final var res = waitForStepPerformDone(firstStep);
      if (firstEniId == null) {
        firstEniId = res.getData().eniId();
        firstSecurityGroupId = res.getData().securityGroupId();
        createdEniIds.add(firstEniId);
        createdSecurityGroupIds.add(firstSecurityGroupId);
      } else {
        // Verify same resources are returned on subsequent calls
        assertEquals(
            "ENI ID should be the same on idempotent calls", firstEniId, res.getData().eniId());
        assertEquals(
            "Security Group ID should be the same on idempotent calls",
            firstSecurityGroupId,
            res.getData().securityGroupId());
      }
      assertNetworkInterfaceConfiguration(
          res.getData().eniId(), res.getData().securityGroupId(), ipv6CidrBlocks);
      assertResourceTags(res.getData().eniId(), res.getData().securityGroupId());
    }

    // Test 2: Create second ENI + Security Group with different step state (should create new
    // resources despite arguments indicating the same node).
    ipv6CidrBlocks = List.of("2600:1f18:3245:e902::/64", "2600:1f18:3245:e904::/64");
    final var secondStep =
        new AWSCreateSlsEniStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(plan.getId(), plan.getMoves().get(0).getId(), 1, getPlanDao()),
            getAWSApiSvc(),
            getContainer(),
            TEST_RS_NAME,
            TEST_MEMBER_INDEX,
            ipv6CidrBlocks,
            slsVpcId,
            subnetId,
            getOrphanedItemDao(),
            getClusterDescription(),
            TEST_HOSTNAME,
            getGroupDao());

    String secondEniId = null;
    String secondSecurityGroupId = null;
    for (int i = 0; i < 2; i++) {
      // Run twice to ensure idempotency within the same step
      final var res = waitForStepPerformDone(secondStep);
      if (secondEniId == null) {
        secondEniId = res.getData().eniId();
        secondSecurityGroupId = res.getData().securityGroupId();
        createdEniIds.add(secondEniId);
        createdSecurityGroupIds.add(secondSecurityGroupId);
      } else {
        // Verify same resources are returned on subsequent calls
        assertEquals(
            "ENI ID should be the same on idempotent calls", secondEniId, res.getData().eniId());
        assertEquals(
            "Security Group ID should be the same on idempotent calls",
            secondSecurityGroupId,
            res.getData().securityGroupId());
      }
      assertNetworkInterfaceConfiguration(
          res.getData().eniId(), res.getData().securityGroupId(), ipv6CidrBlocks);
      assertResourceTags(res.getData().eniId(), res.getData().securityGroupId());
    }

    // Test 3: Verify that different steps create different resources
    assertNotEquals("Different steps should create different ENIs", firstEniId, secondEniId);
    assertNotEquals(
        "Different steps should create different Security Groups",
        firstSecurityGroupId,
        secondSecurityGroupId);

    // Test 4: Verify both resources exist by ID
    assertResourceExistsById(firstEniId, firstSecurityGroupId);
    assertResourceExistsById(secondEniId, secondSecurityGroupId);

    // Test 5: Test rollback of first step
    for (int i = 0; i < 2; i++) {
      // Run rollback twice to ensure idempotency
      waitForStepRollbackDone(firstStep);
      assertResourceDeletedById(firstEniId, firstSecurityGroupId);
    }

    // Test 6: Verify second step resources still exist after first step rollback
    assertResourceExistsById(secondEniId, secondSecurityGroupId);
  }

  private void assertNetworkInterfaceConfiguration(
      String eniId, String sgId, List<String> expectedIpv6Cidrs) {
    final var networkInterface =
        getAWSApiSvc()
            .findNetworkInterfaces(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                List.of(eniId))
            .stream()
            .findFirst()
            .orElseThrow(() -> new AssertionError("ENI not found: " + eniId));

    assertEquals(
        "ENI should have exactly one security group", 1, networkInterface.getGroups().size());
    assertEquals(
        "ENI should be associated with expected security group",
        sgId,
        networkInterface.getGroups().get(0).getGroupId());

    final var securityGroup =
        getAWSApiSvc()
            .findSecurityGroup(
                getContainer().getAWSAccountId(), getContainer().getRegion(), getLogger(), sgId)
            .orElseThrow(() -> new AssertionError("Security Group not found: " + sgId));

    assertTrue(
        "Security group should have no ingress rules", securityGroup.getIpPermissions().isEmpty());
    assertFalse(
        "Security group should have egress rules",
        securityGroup.getIpPermissionsEgress().isEmpty());

    for (final var egressRule : securityGroup.getIpPermissionsEgress()) {
      assertTrue("Egress rules should not have IPv4 ranges", egressRule.getIpv4Ranges().isEmpty());
      assertEquals(
          "Egress rule should have expected number of IPv6 ranges",
          expectedIpv6Cidrs.size(),
          egressRule.getIpv6Ranges().size());

      for (final var ipv6Cidr : egressRule.getIpv6Ranges()) {
        assertTrue(
            "IPv6 CIDR should be in expected list: " + ipv6Cidr.getCidrIpv6(),
            expectedIpv6Cidrs.contains(ipv6Cidr.getCidrIpv6()));
      }
    }
  }

  private void assertResourceExistsById(String eniId, String securityGroupId) {
    // Verify ENI exists by ID
    final var enis =
        getAWSApiSvc()
            .findNetworkInterfaces(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                List.of(eniId));
    assertEquals("ENI should exist", 1, enis.size());

    // Verify Security Group exists by ID
    final var securityGroup =
        getAWSApiSvc()
            .findSecurityGroup(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                securityGroupId);
    assertTrue("Security Group should exist", securityGroup.isPresent());
  }

  private void assertResourceDeletedById(String eniId, String securityGroupId) {
    // Verify ENI is deleted by ID - should throw NOT_FOUND exception
    final var notFoundError =
        assertThrows(
            AWSApiException.class,
            () ->
                getAWSApiSvc()
                    .findNetworkInterfaces(
                        getContainer().getAWSAccountId(),
                        getContainer().getRegion(),
                        getLogger(),
                        List.of(eniId)));
    assertEquals(
        "ENI should be deleted (NOT_FOUND)",
        CommonErrorCode.NOT_FOUND,
        notFoundError.getErrorCode());

    // Verify Security Group is deleted by ID - should throw NO_SECURITY_GROUP_FOUND exception
    final var sgNotFoundError =
        assertThrows(
            AWSApiException.class,
            () ->
                getAWSApiSvc()
                    .findSecurityGroup(
                        getContainer().getAWSAccountId(),
                        getContainer().getRegion(),
                        getLogger(),
                        securityGroupId));
    assertEquals(
        "Security Group should be deleted (NO_SECURITY_GROUP_FOUND)",
        AWSErrorCode.NO_SECURITY_GROUP_FOUND,
        sgNotFoundError.getErrorCode());
  }

  private void assertResourceTags(String eniId, String securityGroupId) {
    // Verify ENI tags
    final var enis =
        getAWSApiSvc()
            .findNetworkInterfaces(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                List.of(eniId));
    assertEquals("ENI should exist", 1, enis.size());
    final var eni = enis.stream().toList().get(0);

    // Verify Security Group tags
    final var securityGroup =
        getAWSApiSvc()
            .findSecurityGroup(
                getContainer().getAWSAccountId(),
                getContainer().getRegion(),
                getLogger(),
                securityGroupId)
            .orElseThrow(() -> new AssertionError("Security Group not found: " + securityGroupId));

    // Verify both resources have the expected tags
    assertExpectedTags(eni.getTagSet(), "ENI");
    assertExpectedTags(securityGroup.getTags(), "Security Group");
  }

  private void assertExpectedTags(List<Tag> tags, String resourceType) {
    // Convert AWS tags to a map for easier verification
    final var tagMap =
        tags.stream()
            .collect(
                Collectors.toMap(
                    com.amazonaws.services.ec2.model.Tag::getKey,
                    com.amazonaws.services.ec2.model.Tag::getValue));

    // Verify standard NDS tags that should be present
    assertNotNull(
        resourceType + " should have planId tag", tagMap.get(NDSDefaults.PLAN_ID_TAG_NAME));
    assertNotNull(
        resourceType + " should have groupId tag", tagMap.get(NDSDefaults.GROUP_ID_TAG_NAME));
    assertNotNull(
        resourceType + " should have containerId tag",
        tagMap.get(NDSDefaults.CONTAINER_ID_TAG_NAME));
    assertNotNull(
        resourceType + " should have environment tag",
        tagMap.get(NDSDefaults.ENVIRONMENT_TAG_NAME));
    assertNotNull(
        resourceType + " should have originHostnameId tag",
        tagMap.get(NDSDefaults.ORIGIN_HOSTNAME_ID_TAG_NAME));
    assertNotNull(
        resourceType + " should have createDate tag", tagMap.get(NDSDefaults.CREATE_DATE_TAG_NAME));
    assertNotNull(
        resourceType + " should have clusterName tag",
        tagMap.get(NDSDefaults.CLUSTER_NAME_TAG_NAME));
    assertNotNull(
        resourceType + " should have clusterUniqueId tag",
        tagMap.get(NDSDefaults.CLUSTER_UNIQUE_ID_TAG_NAME));
    assertNotNull(
        resourceType + " should have instanceHostname tag",
        tagMap.get(NDSDefaults.INSTANCE_HOSTNAME_TAG_NAME));

    // Verify SLS-specific tags
    assertEquals(
        resourceType + " should have Purpose tag", "SLS-Communication", tagMap.get("Purpose"));
    assertEquals(
        resourceType + " should have ResourceType tag", "SLS-Network", tagMap.get("ResourceType"));
    assertEquals(
        resourceType + " should have ShardName tag", TEST_RS_NAME, tagMap.get("ShardName"));
    assertEquals(
        resourceType + " should have MemberIndex tag",
        String.valueOf(TEST_MEMBER_INDEX),
        tagMap.get("MemberIndex"));

    // Verify specific tag values
    assertEquals(
        resourceType + " should have correct hostname tag",
        TEST_HOSTNAME,
        tagMap.get(NDSDefaults.INSTANCE_HOSTNAME_TAG_NAME));
    assertEquals(
        resourceType + " should have correct cluster name",
        getClusterDescription().getName(),
        tagMap.get(NDSDefaults.CLUSTER_NAME_TAG_NAME));
    assertEquals(
        resourceType + " should have correct cluster unique ID",
        getClusterDescription().getUniqueId().toString(),
        tagMap.get(NDSDefaults.CLUSTER_UNIQUE_ID_TAG_NAME));
    assertEquals(
        resourceType + " should have correct group ID",
        getGroup().getId().toString(),
        tagMap.get(NDSDefaults.GROUP_ID_TAG_NAME));
    assertEquals(
        resourceType + " should have correct container ID",
        getContainer().getId().toString(),
        tagMap.get(NDSDefaults.CONTAINER_ID_TAG_NAME));
  }
}
