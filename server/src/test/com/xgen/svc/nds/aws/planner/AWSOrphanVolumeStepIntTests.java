package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.model.AWSOrphanedItem;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanStrategy;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class AWSOrphanVolumeStepIntTests extends AWSExternalIntTest {

  @Inject private AppSettings _appSettings;
  @Inject private NDSOrphanedItemDao _orphanedItemDao;
  @Inject AWSApiSvc _awsApiSvc;
  @Inject private GroupDao _groupDao;

  @Before
  public void init() throws Exception {
    _appSettings.setProp(
        NDSSettings.Properties.KEEP_ORPHANED_AWS_DISK_HOURS, "120", AppSettings.SettingType.MEMORY);
  }

  @After
  public void cleanup() throws Exception {
    _appSettings.clearMemory();
  }

  private void executeProvisionAndPlanMoves(final Plan pPlan, final Move pProvisionContainerMove) {
    pPlan.addMove(pProvisionContainerMove);
    pPlan.addMove(new DummyMove());
    getPlanDao().save(pPlan);

    try {
      waitForMovePerformDone(pProvisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(pProvisionContainerMove);
      throw t;
    }
  }

  private AWSCreateVolumeStep executeCreateVolumeStep(
      final Plan pPlan, final ClusterDescription pClusterDescription) throws InterruptedException {
    final NDSPlanContext planContext = (NDSPlanContext) pPlan.getPlanContext();
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final AWSCreateVolumeStep createVolumeStep =
        new AWSCreateVolumeStep(
            planContext,
            new Step.State(pPlan.getId(), pPlan.getMoves().get(1).getId(), 0, getPlanDao()),
            _awsApiSvc,
            _orphanedItemDao,
            getContainer(),
            pClusterDescription,
            replicaSetHardware,
            getInstanceHardware(replicaSetHardware, getInstanceIds().get(0)),
            getContainer().getSubnets()[0].getSubnetId(),
            _groupDao,
            ResourceOperationTagType.NONE,
            false);

    try {
      waitForStepPerformDone(createVolumeStep);
    } catch (final Throwable t) {
      if (createVolumeStep.getEBSVolumeId().isPresent()) {
        cleanupEBSVolume(createVolumeStep.getEBSVolumeId().get());
      }
      throw t;
    }

    return createVolumeStep;
  }

  private String testOrphanVolumeStep(
      final Plan pPlan,
      final AWSCreateVolumeStep pCreateVolumeStep,
      final List<Move> pMovesToRollBackInOrder,
      final OrphanStrategy pOrphanStrategy,
      final boolean pInstanceIsProvisioned) {
    final String ebsVolumeId = pCreateVolumeStep.perform().getData().getEBSVolumeId();
    try {
      testAWSOrphanVolumeStepInternal(pPlan, ebsVolumeId, pOrphanStrategy, pInstanceIsProvisioned);
      return ebsVolumeId;
    } catch (final Throwable t) {
      cleanupEBSVolume(ebsVolumeId);
      throw t;
    } finally {
      pMovesToRollBackInOrder.forEach(this::waitForMoveRollbackDone);
    }
  }

  private void testAWSOrphanVolumeStepInternal(
      final Plan pPlan,
      final String pEBSVolumeId,
      final OrphanStrategy pOrphanStrategy,
      final boolean pInstanceIsProvisioned) {
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName())
            .get(0);
    final InstanceHardware instanceHardware =
        getInstanceHardware(replicaSetHardware, getInstanceIds().get(0));
    if (pInstanceIsProvisioned) {
      assertTrue(instanceHardware.getHostnameForAgents().isPresent());
    } else {
      assertTrue(instanceHardware.getHostnameForAgents().isEmpty());
    }

    final AWSOrphanVolumeStep step =
        new AWSOrphanVolumeStep(
            (NDSPlanContext) pPlan.getPlanContext(),
            new Step.State(pPlan.getId(), pPlan.getMoves().get(1).getId(), 1, getPlanDao()),
            _orphanedItemDao,
            getContainer(),
            pEBSVolumeId,
            getHardwareDao()
                .findByCluster(getNDSGroup().getGroupId(), getClusterDescription().getName())
                .get(0),
            getClusterDescription(),
            pOrphanStrategy,
            _awsApiSvc,
            instanceHardware,
            new Date());

    waitForStepPerformDone(step);
    verifyEBSVolumeOrphaned(pEBSVolumeId, true);
    assertTrue(step.rollback().getStatus().isDone());
  }

  private AWSOrphanedItem retrieveOrphanedItem(
      final AWSCreateVolumeStep pCreateVolumeStep,
      final Plan pPlan,
      final List<Move> pMovesToRollBackInOrder,
      final boolean pInstanceIsProvisioned) {
    final String ebsVolumeId =
        testOrphanVolumeStep(
            pPlan,
            pCreateVolumeStep,
            pMovesToRollBackInOrder,
            OrphanStrategy.UseClusterCreationDate,
            pInstanceIsProvisioned);
    return findOrphanedItemById(ebsVolumeId);
  }

  private AWSOrphanedItem retrieveOrphanedItemWithDiskCreateTimeAsTtl(
      final AWSCreateVolumeStep pCreateVolumeStep,
      final Plan pPlan,
      final Move pProvisionContainerMove) {
    final String ebsVolumeId =
        testOrphanVolumeStep(
            pPlan,
            pCreateVolumeStep,
            Collections.singletonList(pProvisionContainerMove),
            OrphanStrategy.UseVolumeCreationDateFirst,
            false);
    return findOrphanedItemById(ebsVolumeId);
  }

  private void cleanupOrphanedItems(final String pEBSVolumeId) {
    cleanOrphanedItems();
    verifyEBSVolumeDeleted(pEBSVolumeId);
  }

  private void tryToCleanupOrphanedItems(final String pEBSVolumeId) {
    cleanOrphanedItems();
    try {
      verifyEBSVolumeDeleted(pEBSVolumeId);
      fail();
    } catch (final AssertionError e) {
      assertNotNull(e);
    }
    cleanupEBSVolume(pEBSVolumeId);
    verifyEBSVolumeDeleted(pEBSVolumeId);
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final AWSCreateVolumeStep createVolumeStep =
        executeCreateVolumeStep(plan, getClusterDescription());

    final String ebsVolumeId =
        testOrphanVolumeStep(
            plan,
            createVolumeStep,
            Collections.singletonList(provisionContainerMove),
            OrphanStrategy.UseClusterCreationDate,
            false);
    cleanupOrphanedItems(ebsVolumeId);
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanImmediately() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final AWSCreateVolumeStep createVolumeStep =
        executeCreateVolumeStep(plan, getClusterDescription());
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStep, plan, Collections.singletonList(provisionContainerMove), false);
    assertEquals(orphanedItem.getCreatedDate(), orphanedItem.getKeepUntilDate());
    cleanupOrphanedItems(orphanedItem.getId());
  }

  @Test(timeout = 40 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanImmediately_HasClusterMetadata() {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    final Move provisionMachineMove =
        AWSProvisionMachineMove.testFactoryCreate(
            plan.getPlanContext(),
            getTags(),
            getClusterDescription().getName(),
            getInstanceIds().get(0),
            true,
            _testInjector);
    plan.addMove(provisionContainerMove);
    plan.addMove(provisionMachineMove);
    getPlanDao().save(plan);

    try {
      waitForMovePerformDone(provisionContainerMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionContainerMove);
      throw t;
    }

    try {
      waitForMovePerformDone(provisionMachineMove);
    } catch (final Throwable t) {
      waitForMoveRollbackDone(provisionMachineMove);
      throw t;
    }

    final NDSPlanContext planContext = (NDSPlanContext) plan.getPlanContext();
    final ReplicaSetHardware replicaSetHardware =
        getHardwareDao()
            .findReplicaSetHardwareForInstance(
                getGroup().getId(), getClusterDescription().getName(), getInstanceIds().get(0));
    final AWSCreateVolumeStep createVolumeStepFromPlan =
        new AWSCreateVolumeStep(
            planContext,
            new Step.State(
                plan.getId(),
                provisionMachineMove.getId(),
                AWSProvisionMachineMove.StepNumber.CREATE_VOLUME,
                getPlanDao()),
            _awsApiSvc,
            _orphanedItemDao,
            getContainer(),
            getClusterDescription(),
            replicaSetHardware,
            getInstanceHardware(replicaSetHardware, getInstanceIds().get(0)),
            getContainer().getSubnets()[0].getSubnetId(),
            _groupDao,
            ResourceOperationTagType.NONE,
            false);

    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStepFromPlan,
            plan,
            List.of(provisionMachineMove, provisionContainerMove),
            true);
    assertEquals(orphanedItem.getCreatedDate(), orphanedItem.getKeepUntilDate());
    cleanupOrphanedItems(orphanedItem.getId());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanUnder12Hours() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .setCreateDate(new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(11)))
            .build();
    getClusterDescriptionDao().save(clusterDescription);
    final AWSCreateVolumeStep createVolumeStep = executeCreateVolumeStep(plan, clusterDescription);
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStep, plan, Collections.singletonList(provisionContainerMove), false);
    assertEquals(orphanedItem.getCreatedDate(), orphanedItem.getKeepUntilDate());
    cleanupOrphanedItems(orphanedItem.getId());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanExactly12Hours() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .setCreateDate(new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(12)))
            .build();
    getClusterDescriptionDao().save(clusterDescription);
    final AWSCreateVolumeStep createVolumeStep = executeCreateVolumeStep(plan, clusterDescription);
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStep, plan, Collections.singletonList(provisionContainerMove), false);
    final long ebsVolumeOrphanedTime =
        orphanedItem.getKeepUntilDate().getTime() - orphanedItem.getCreatedDate().getTime();
    assertTrue(ebsVolumeOrphanedTime > (long) (0.2 * TimeUnit.HOURS.toMillis(12)));
    assertTrue(ebsVolumeOrphanedTime < TimeUnit.DAYS.toMillis(5));
    tryToCleanupOrphanedItems(createVolumeStep.perform().getData().getEBSVolumeId());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanUnder25Days() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .setCreateDate(new Date(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(24)))
            .build();
    getClusterDescriptionDao().save(clusterDescription);
    final AWSCreateVolumeStep createVolumeStep = executeCreateVolumeStep(plan, clusterDescription);
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStep, plan, Collections.singletonList(provisionContainerMove), false);
    final long ebsVolumeOrphanedTime =
        orphanedItem.getKeepUntilDate().getTime() - orphanedItem.getCreatedDate().getTime();
    assertTrue(ebsVolumeOrphanedTime > (long) (0.2 * TimeUnit.DAYS.toMillis(24)));
    assertTrue(ebsVolumeOrphanedTime < TimeUnit.DAYS.toMillis(5));
    tryToCleanupOrphanedItems(createVolumeStep.perform().getData().getEBSVolumeId());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanExactly25Days() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .setCreateDate(new Date(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(25)))
            .build();
    getClusterDescriptionDao().save(clusterDescription);
    final AWSCreateVolumeStep createVolumeStep = executeCreateVolumeStep(plan, clusterDescription);
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStep, plan, Collections.singletonList(provisionContainerMove), false);
    final long ebsVolumeOrphanedTime =
        orphanedItem.getKeepUntilDate().getTime() - orphanedItem.getCreatedDate().getTime();
    assertEquals(ebsVolumeOrphanedTime, TimeUnit.DAYS.toMillis(5));
    tryToCleanupOrphanedItems(createVolumeStep.perform().getData().getEBSVolumeId());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanOver25Days() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .setCreateDate(new Date(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(30)))
            .build();
    getClusterDescriptionDao().save(clusterDescription);
    final AWSCreateVolumeStep createVolumeStep = executeCreateVolumeStep(plan, clusterDescription);
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItem(
            createVolumeStep, plan, Collections.singletonList(provisionContainerMove), false);
    final long ebsVolumeOrphanedTime =
        orphanedItem.getKeepUntilDate().getTime() - orphanedItem.getCreatedDate().getTime();
    assertEquals(ebsVolumeOrphanedTime, TimeUnit.DAYS.toMillis(5));
    tryToCleanupOrphanedItems(createVolumeStep.perform().getData().getEBSVolumeId());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSOrphanVolumeStep_OrphanUsingDiskCreateDate() throws InterruptedException {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    final Move provisionContainerMove =
        getMoveFromPlanner(
            AWSProvisionContainerMove.class,
            plan.getPlanContext(),
            getTags(),
            getContainer().getId());
    executeProvisionAndPlanMoves(plan, provisionContainerMove);

    final ClusterDescription clusterDescription =
        getClusterDescription()
            .copy()
            .setCreateDate(new Date(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(30)))
            .build();
    getClusterDescriptionDao().save(clusterDescription);
    final AWSCreateVolumeStep createVolumeStep = executeCreateVolumeStep(plan, clusterDescription);
    final AWSOrphanedItem orphanedItem =
        retrieveOrphanedItemWithDiskCreateTimeAsTtl(createVolumeStep, plan, provisionContainerMove);
    final long ebsVolumeOrphanedTime =
        orphanedItem.getKeepUntilDate().getTime() - orphanedItem.getCreatedDate().getTime();

    // since the cluster created date was more than 30 days ago, if the cluster create date was used
    // in calculating keepUntilTime for orphan item, then it will default to 5 days. But using disk
    // create time would be much shorter, since we just created it just now. 15 minutes chosen is
    // based on test timeout
    assertTrue(ebsVolumeOrphanedTime < TimeUnit.MINUTES.toMillis(15));
    tryToCleanupOrphanedItems(createVolumeStep.perform().getData().getEBSVolumeId());
  }
}
