package com.xgen.svc.nds.aws.planner;

import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.nds.planner.ResyncBaseMove;
import com.xgen.svc.nds.planner.ResyncMove;
import org.bson.types.ObjectId;
import org.junit.Test;

public class ResyncMoveIntTests extends BaseResyncIntTest {

  @Override
  protected ResyncBaseMove getResyncMove(
      final PlanContext pPlanContext, final String pClusterName, final ObjectId pInstanceId) {
    return ResyncMove.factoryCreate(pPlanContext, pClusterName, pInstanceId);
  }

  @Test(timeout = 30 * 60 * 1000L)
  public void testResyncMove() throws InterruptedException {
    testResyncMoveInternal();
  }
}
