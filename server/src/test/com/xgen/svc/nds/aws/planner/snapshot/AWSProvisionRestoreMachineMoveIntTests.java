package com.xgen.svc.nds.aws.planner.snapshot;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.StreamingReplicaSetRestoreJob;
import com.xgen.cloud.cps.restore._public.model.VMBasedReplSetRestoreJob;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.BackupSnapshotUtils;
import com.xgen.svc.nds.planner.snapshot.CpsReplSetSnapshotMove;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import jakarta.inject.Inject;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.Test;

public class AWSProvisionRestoreMachineMoveIntTests extends AWSExternalIntTest {

  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private BackupJobDao _backupJobDao;
  @Inject private CpsSvc _cpsSvc;
  @Inject private BackupRestoreJobDao _restoreJobDao;
  @Inject private CpsPolicySvc _cpsPolicySvc;

  @Test(timeout = 60 * 60 * 1000L)
  public void testAWSProvisionRestoreMachineMove() throws Exception {
    final Plan provisionContainerAndInstancesPlan = setupPlanWithContainerAndSingleInstance();
    final Plan createSnapshotPlan = setupPlanWithSnapshotMove();

    try {
      waitForPlanPerformSuccess(provisionContainerAndInstancesPlan);

      waitforMonitoringData();

      waitForPlanPerformSuccess(createSnapshotPlan);
      testAWSProvisionRestoreMachineMoveInternal();
    } finally {
      waitForPlanRollbackSuccess(createSnapshotPlan);
      waitForPlanRollbackSuccess(provisionContainerAndInstancesPlan);
    }
  }

  private void testAWSProvisionRestoreMachineMoveInternal() throws SvcException {
    final ObjectId jobId = createRestoreJob();
    final Plan restoreMachinePlan = setupPlanWithRestoreMachineMove(jobId);

    try {
      waitForPlanPerformSuccess(restoreMachinePlan);

      final StreamingReplicaSetRestoreJob restoreJob =
          _restoreJobDao.findStreamingReplSetJob(jobId);

      assertNotNull(restoreJob.buildPublicDeliveryUrl());
      assertFalse(restoreJob.getExpired());
      assertFalse(restoreJob.getCanceled());
      assertTrue(restoreJob.isShowRestoreReady());

      final InstanceHardware restoreInstance = restoreJob.getInstanceHardware();
      assertEquals(
          DNSRecordUtil.getPublicDNSRecordForRestoreInstance(
              restoreJob.getMetadata(),
              getNDSGroup(),
              NDSSettings.getCrossCloudInstanceDomainName(getAppSettings())),
          restoreInstance.getHostnameForAgents().get());

    } finally {
      waitForPlanRollbackSuccess(restoreMachinePlan);
    }

    final VMBasedReplSetRestoreJob terminatedRestoreJob =
        _restoreJobDao.findStreamingReplSetJob(jobId);
    assertNotNull(terminatedRestoreJob.getTerminatedDate());
  }

  private Plan setupPlanWithSnapshotMove() {
    final ClusterDescription cluster = getClusterDescription();

    // create a backup job in db for AWSCreateEbsSnapshotMove
    final ObjectId backupJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            cluster.getGroupId(),
            cluster.getName(),
            new ObjectId(),
            BackupJob.ClusterType.REPLICA_SET,
            2,
            false,
            false);

    final BackupJob backupJob = _backupJobDao.find(backupJobId).get();
    assertNotNull(backupJob);

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    scheduleSnapshot(cluster, backupJob);
    final CpsReplSetSnapshotMove snapshotMove =
        CpsReplSetSnapshotMove.factoryCreate(plan.getPlanContext(), cluster.getName());

    plan.addMove(snapshotMove);
    getPlanDao().save(plan);
    return plan;
  }

  private Plan setupPlanWithRestoreMachineMove(ObjectId restoreJobId) {
    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());

    final AWSProvisionRestoreMachineMove move =
        AWSProvisionRestoreMachineMove.factoryCreate(plan.getPlanContext(), restoreJobId);
    plan.addMove(move);
    getPlanDao().save(plan);
    return plan;
  }

  private ObjectId scheduleSnapshot(
      final ClusterDescription pClusterDesc, final BackupJob pBackupJob) {

    return BackupSnapshotUtils.queueScheduledSnapshot(
        new Date(), pClusterDesc, pBackupJob, _backupSnapshotDao, false, false);
  }

  private ObjectId createRestoreJob() throws SvcException {
    final ClusterDescription cluster = getClusterDescription();
    final boolean deleted = false;
    final BackupSnapshot snapshot =
        _backupSnapshotDao
            .findCompletedByCluster(cluster.getGroupId(), cluster.getUniqueId(), deleted)
            .get(0);
    return _cpsSvc.createDownloadRestoreJob(
        getGroup(), cluster.getName(), getUser(), snapshot.getId());
  }
}
