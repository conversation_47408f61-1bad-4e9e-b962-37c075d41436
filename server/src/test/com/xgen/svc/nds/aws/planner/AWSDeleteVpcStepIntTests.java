package com.xgen.svc.nds.aws.planner;

import static org.junit.Assert.assertTrue;

import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.aws.AWSExternalIntTest;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.NDSPlanContext;
import jakarta.inject.Inject;
import org.junit.Test;

public class AWSDeleteVpcStepIntTests extends AWSExternalIntTest {

  @Inject private AWSApiSvc _awsApiSvc;
  @Inject private GroupDao _groupDao;

  private void testAWSDeleteVPCStepInternal(final Plan pPlan, final String pVpcId)
      throws InterruptedException {

    final AWSDeleteVpcStep step =
        new AWSDeleteVpcStep(
            (NDSPlanContext) pPlan.getPlanContext(),
            new Step.State(
                pPlan.getId(),
                pPlan.getMoves().get(0).getId(),
                1,
                pPlan.getPlanContext().getPlanDao()),
            getContainer(),
            pVpcId,
            _awsApiSvc,
            getOrphanedItemSvc(),
            getOrphanedItemDao());

    waitForStepPerformDone(step);
    verifyVpcDeleted(pVpcId);
    assertTrue(step.rollback().getStatus().isDone());
  }

  @Test(timeout = 15 * 60 * 1000L)
  public void testAWSDeleteVPCStep() throws InterruptedException {

    final Plan plan = new Plan(getGroup().getId(), getPlanContextFactory());
    plan.addMove(new DummyMove());
    getPlanDao().save(plan);

    final AWSCreateVpcStep createVpcStep =
        new AWSCreateVpcStep(
            (NDSPlanContext) plan.getPlanContext(),
            new Step.State(
                plan.getId(),
                plan.getMoves().get(0).getId(),
                0,
                plan.getPlanContext().getPlanDao()),
            getContainer(),
            _awsApiSvc,
            getOrphanedItemSvc(),
            getOrphanedItemDao(),
            _groupDao);

    try {
      waitForStepPerformDone(createVpcStep);
    } catch (final Throwable t) {
      if (createVpcStep.getVpcId().isPresent()) {
        cleanupVpc(createVpcStep.getVpcId().get());
      }
      throw t;
    }

    try {
      testAWSDeleteVPCStepInternal(plan, createVpcStep.perform().getData().getVpcId());
    } catch (final Throwable t) {
      cleanupVpc(createVpcStep.perform().getData().getVpcId());
      throw t;
    }
  }
}
