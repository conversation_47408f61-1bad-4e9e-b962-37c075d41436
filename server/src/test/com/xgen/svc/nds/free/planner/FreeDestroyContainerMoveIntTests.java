package com.xgen.svc.nds.free.planner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

import com.xgen.cloud.nds.free._public.model.FreeCloudProviderContainer;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.free.FreeNDSDefaults;
import com.xgen.svc.nds.tenant.planner.TenantIntTest;
import org.junit.Test;

public class FreeDestroyContainerMoveIntTests extends TenantIntTest {
  public void testFreeDestroyContainerMoveInternal(final FreeDestroyContainerMove pMove) {

    waitForMovePerformDone(pMove);

    final FreeCloudProviderContainer container = getFreeContainer();
    final ClusterDescription mtmCluster = getFreeMtmClusterDescription();
    assertNotEquals(mtmCluster.getName(), container.getClusterName());
    assertNotEquals(mtmCluster.getGroupId(), container.getGroupId());
    assertEquals(FreeNDSDefaults.MTM_MAX_CAPACITY, getSharedMtmCluster().getCapacityRemaining());
  }

  @Test(timeout = 5 * 60 * 1000L)
  public void testFreeDestroyContainerMove() throws InterruptedException {
    final Plan plan = new Plan(getFreeGroup().getId(), getNdsPlanContextFactory());
    final FreeProvisionContainerMove provisionMove =
        FreeProvisionContainerMove.factoryCreate(plan.getPlanContext(), getFreeContainerId());
    final FreeDestroyContainerMove destroyMove =
        FreeDestroyContainerMove.factoryCreate(plan.getPlanContext(), getFreeContainerId());

    plan.addMove(provisionMove);
    plan.addMove(destroyMove);

    getPlanDao().save(plan);

    waitForMovePerformDone(provisionMove);

    testFreeDestroyContainerMoveInternal(destroyMove);
  }
}
