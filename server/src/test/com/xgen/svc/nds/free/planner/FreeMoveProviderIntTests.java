package com.xgen.svc.nds.free.planner;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.module.common.planner.model.Move;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.nds.planner.NDSPlanContext;
import java.util.Collections;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class FreeMoveProviderIntTests {
  private static final ObjectId CONTAINER_ID = oid(0);
  private static final ObjectId INSTANCE_ID = oid(0);
  private static final String CLUSTER_NAME = "freeCluster";

  private NDSPlanContext _context;
  private FreeMoveProvider _moveProvider;

  @Before
  public void setUp() {
    _context =
        AppConfig.getInstance(NDSPlanContext.Factory.class).create(new ObjectId(), new ObjectId());
    _moveProvider = new FreeMoveProvider(_context);
  }

  @Test
  public void testGetProvisionContainerMove() {
    final Move provisionContainerMove =
        _moveProvider.getProvisionContainerMove(Map.of(), CONTAINER_ID, Collections.emptyList());
    assertNotNull(provisionContainerMove);
    assertTrue(provisionContainerMove instanceof FreeProvisionContainerMove);
    assertArrayEquals(
        new Object[] {CONTAINER_ID},
        ((FreeProvisionContainerMove) provisionContainerMove).getArguments());
  }

  @Test
  public void testGetProcessMongoDBConfigMove() {
    final Move processMongoDBConfigMove =
        _moveProvider.getProcessMongoDBConfigMove(CLUSTER_NAME, Collections.emptyList(), false);
    assertNotNull(processMongoDBConfigMove);
    assertTrue(processMongoDBConfigMove instanceof FreeProcessProxyConfigPerClusterMove);
    assertArrayEquals(
        new Object[] {CLUSTER_NAME},
        ((FreeProcessProxyConfigPerClusterMove) processMongoDBConfigMove).getArguments());
  }

  @Test
  public void testGetDestroyContainerMove() {
    final Move destroyContainerMove = _moveProvider.getDestroyContainerMove(CONTAINER_ID);
    assertNotNull(destroyContainerMove);
    assertTrue(destroyContainerMove instanceof FreeDestroyContainerMove);
    assertArrayEquals(
        new Object[] {CONTAINER_ID},
        ((FreeDestroyContainerMove) destroyContainerMove).getArguments());
  }

  @Test
  public void testGetDestroyMachineMove() {
    final Move destroyMachineMove =
        _moveProvider.getDestroyMachineMove(Map.of(), CLUSTER_NAME, INSTANCE_ID, false);
    assertNotNull(destroyMachineMove);
    assertTrue(destroyMachineMove instanceof FreeDestroyMachineMove);
    assertArrayEquals(
        new Object[] {CLUSTER_NAME, INSTANCE_ID},
        ((FreeDestroyMachineMove) destroyMachineMove).getArguments());
  }
}
