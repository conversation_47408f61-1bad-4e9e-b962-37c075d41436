load("//server/src/test:rules.bzl", "library_package", "test_package")

library_package(
    name = "planner",
    visibility = [
        "//server/src/test/com/xgen/svc/nds:__subpackages__",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/topology/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/replicasethardware",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/serverless/_private/dao",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/test/com/xgen/svc/nds/flex/util",
        "//server/src/test/com/xgen/svc/nds/free/util",
        "//server/src/test/com/xgen/svc/nds/planner",
        "//server/src/test/com/xgen/svc/nds/serverless/util",
        "//server/src/test/com/xgen/svc/nds/tenant/util",
        "@maven//:junit_junit",
    ],
)

test_package(
    name = "externalTestLibrary",
    srcs = glob(
        ["*IntTests.java"],
    ),
    deny_warnings = True,
    extra_jvmargs = ["-Djob.processor.enabled=true"],
    tags = ["external"],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/free/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/test/com/xgen/svc/nds/tenant/planner",
        "//server/src/test/com/xgen/svc/nds/tenant/util",
        "@maven//:com_amazonaws_aws_java_sdk_route53",
        "@maven//:junit_junit",
    ],
)
