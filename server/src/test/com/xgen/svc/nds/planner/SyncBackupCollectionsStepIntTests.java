package com.xgen.svc.nds.planner;

import static com.xgen.cloud.nds.project._public.model.ClusterDescription.State.IDLE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.mongodb.WriteConcern;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._private.dao.CpsOplogSliceMetadataDao;
import com.xgen.cloud.cps.pit._private.dao.CpsRegionalMetadataStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsOplogSlice;
import com.xgen.cloud.cps.pit._public.model.CpsRegionalMetadataStoreConfig;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.CanceledReason;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.dao.CpsRegionalMetadataStoreMongoSvc;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.BackupJobQueueSvc;
import com.xgen.svc.nds.svc.cps.CpsOplogSliceMetadataDaoProxy;
import com.xgen.svc.nds.svc.cps.CpsOplogStoreFactory;
import com.xgen.svc.nds.svc.cps.CpsResurrectSvc;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SyncBackupCollectionsStepIntTests extends JUnit5BaseSvcTest {

  @Inject private CpsRegionalMetadataStoreMongoSvc cpsRegionalMetadataStoreMongoSvc;
  @Inject private CpsRegionalMetadataStoreConfigDao cpsRegionalMetadataStoreConfigDao;

  private BackupJobQueueSvc backupJobQueueSvc;
  private BackupSnapshotDao backupSnapshotDao;
  private BackupRestoreJobDao backupRestoreJobDao;
  private BackupJobDao backupJobDao;
  private CpsOplogStoreFactory cpsOplogStoreFactory;
  private Group group;
  private CpsResurrectSvc cpsResurrectSvc;
  private SyncBackupCollectionsStep step;
  private ClusterDescriptionDao clusterDescriptionDao;
  private Plan plan;
  private static final ObjectId DELETED_CLUSTER_UNIQUE_ID = oid(97);
  private static final ObjectId RESURRECTED_CLUSTER_UNIQUE_ID_NO_PIT = oid(98);
  private static final ObjectId RESURRECTED_CLUSTER_UNIQUE_ID = oid(5);
  private static final ObjectId GROUP_ID = oid(148);
  private static final String CLUSTER_NAME = "RetainedCluster";
  private static final String CLUSTER_NAME_NO_PIT = "RetainedClusterNoPit";
  private static final ObjectId RETAINED_SNAPSHOT_ID = oid(22);
  private static final ObjectId ONGOING_RESTORE_JOB_ID = oid(14);
  private static final ObjectId FINISHED_RESTORE_JOB_ID = oid(15);
  private static final String OPLOG_STORE_ID = "regional-store-1";

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupSnapshotDao/backupSnapshots.json.ftl",
        null,
        "nds",
        "config.nds.backup.snapshots");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupRestoreJobDao/backupRestoreJobs.json.ftl",
        null,
        "nds",
        "config.nds.backup.restorejobs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupJobDao/jobs.json.ftl", null, "nds", "config.nds.backup.jobs");

    final GroupDao groupDao = AppConfig.getInstance(GroupDao.class);
    backupJobQueueSvc = AppConfig.getInstance(BackupJobQueueSvc.class);
    backupJobDao = AppConfig.getInstance(BackupJobDao.class);
    backupSnapshotDao = AppConfig.getInstance(BackupSnapshotDao.class);
    backupRestoreJobDao = AppConfig.getInstance(BackupRestoreJobDao.class);
    cpsOplogStoreFactory = AppConfig.getInstance(CpsOplogStoreFactory.class);
    cpsResurrectSvc = spy(AppConfig.getInstance(CpsResurrectSvc.class));
    group = groupDao.findById(GROUP_ID);
    clusterDescriptionDao = AppConfig.getInstance(ClusterDescriptionDao.class);
    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), CLUSTER_NAME)
                .append(ClusterDescription.FieldDefs.UNIQUE_ID, RESURRECTED_CLUSTER_UNIQUE_ID)
                .append(ClusterDescription.FieldDefs.BACKUP_ENABLED, false)
                .append(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED, true)
                .append(ClusterDescription.FieldDefs.PIT_ENABLED, true)
                .append(ClusterDescription.FieldDefs.STATE, IDLE));
    clusterDescriptionDao.insert(clusterDescription.toDBObject(), WriteConcern.ACKNOWLEDGED);

    cpsRegionalMetadataStoreMongoSvc.close();
    cpsRegionalMetadataStoreConfigDao.insertForTests(
        new CpsRegionalMetadataStoreConfig(
            OPLOG_STORE_ID,
            OPLOG_STORE_ID,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            false,
            false,
            "mongodb://localhost:" + TestDataUtils.TEST_MONGO_PORT,
            true,
            false,
            false,
            null,
            "fake_deployment_id",
            0));

    final NDSPlanContext.Factory planFactory = AppConfig.getInstance(NDSPlanContext.Factory.class);
    plan = new Plan(group.getId(), planFactory);
    step =
        spy(
            new SyncBackupCollectionsStep(
                plan.getPlanContext(),
                mock(Step.State.class),
                backupJobDao,
                backupRestoreJobDao,
                clusterDescription,
                cpsResurrectSvc));
  }

  @AfterEach
  public void tearDown() {
    // drop all oplog db collections if they exist
    try {
      cpsOplogStoreFactory
          .getCpsSliceDaoProxy(
              // build a backupJob to find cpsSliceDao with resurrected clusterUniqueId
              BackupJob.builder()
                  .clusterUniqueId(RESURRECTED_CLUSTER_UNIQUE_ID)
                  .clusterName(CLUSTER_NAME)
                  .build(),
              GROUP_ID,
              OPLOG_STORE_ID)
          .getDbCollection()
          .drop();

      cpsOplogStoreFactory.getCpsSliceDaoProxy(
          // build a backupJob to find cpsSliceDao with resurrected clusterUniqueId
          BackupJob.builder()
              .clusterUniqueId(DELETED_CLUSTER_UNIQUE_ID)
              .clusterName(CLUSTER_NAME)
              .build(),
          GROUP_ID,
          OPLOG_STORE_ID);
    } catch (Exception e) {
      // do nothing
    }
  }

  @Test
  public void testPerformInternal_doesNotTryToRenameOplogsNoPit() {
    doNothing().when(cpsResurrectSvc).updateSnapshotTags(any(), any(), any(), any(), any());
    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), CLUSTER_NAME_NO_PIT)
                .append(
                    ClusterDescription.FieldDefs.UNIQUE_ID, RESURRECTED_CLUSTER_UNIQUE_ID_NO_PIT)
                .append(ClusterDescription.FieldDefs.BACKUP_ENABLED, false)
                .append(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED, true)
                .append(ClusterDescription.FieldDefs.PIT_ENABLED, false)
                .append(ClusterDescription.FieldDefs.STATE, IDLE));
    clusterDescriptionDao.insert(clusterDescription.toDBObject(), WriteConcern.ACKNOWLEDGED);
    step =
        spy(
            new SyncBackupCollectionsStep(
                plan.getPlanContext(),
                mock(Step.State.class),
                backupJobDao,
                backupRestoreJobDao,
                clusterDescription,
                cpsResurrectSvc));

    final Result<Result.NoData> result = step.performInternal();
    assertEquals(Result.Status.DONE, result.getStatus());

    // no attempt to update oplogs
    verify(cpsResurrectSvc, times(0)).updateOplogMetadataCollections(any(), any());

    // update snapshot tags
    verify(cpsResurrectSvc, times(1)).updateSnapshotTags(any(), any(), any(), any(), any());
  }

  @Test
  public void testPerformInternal_renamesBackupCollections() {
    // insert oplogs
    final String collectionName = DELETED_CLUSTER_UNIQUE_ID.toHexString() + "_" + CLUSTER_NAME;
    final CpsOplogSliceMetadataDao cpsOplogSliceMetadataDao =
        CpsOplogSliceMetadataDao.get(
            cpsRegionalMetadataStoreMongoSvc,
            OPLOG_STORE_ID,
            group.getId().toHexString() + "_cps",
            WriteConcern.JOURNALED,
            collectionName);
    cpsOplogSliceMetadataDao.getDbCollection().drop();
    final CpsOplogSlice cpsOplogSliceValid =
        buildTestSlice(true, new BSONTimestamp(1, 0), new BSONTimestamp(2, 0));
    final CpsOplogSlice cpsOplogSliceValid2 =
        buildTestSlice(true, new BSONTimestamp(3, 0), new BSONTimestamp(4, 0));
    final CpsOplogSlice cpsOplogSliceInvalid =
        buildTestSlice(false, new BSONTimestamp(5, 0), new BSONTimestamp(6, 0));
    cpsOplogSliceMetadataDao.addMetadata(cpsOplogSliceValid);
    cpsOplogSliceMetadataDao.addMetadata(cpsOplogSliceValid2);
    cpsOplogSliceMetadataDao.addMetadata(cpsOplogSliceInvalid);

    doNothing().when(cpsResurrectSvc).updateSnapshotTags(any(), any(), any(), any(), any());

    final Result<Result.NoData> result = step.performInternal();
    assertEquals(Result.Status.DONE, result.getStatus());

    // snapshots, restoreJobs clusterUniqueId updated
    final BackupSnapshot backupSnapshot = backupSnapshotDao.findById(RETAINED_SNAPSHOT_ID).get();
    final ReplicaSetBackupRestoreJob ongoingRestoreJob =
        backupRestoreJobDao.findDirectAttachReplSetJob(ONGOING_RESTORE_JOB_ID);
    final ReplicaSetBackupRestoreJob finishedRestoreJob =
        backupRestoreJobDao.findDirectAttachReplSetJob(FINISHED_RESTORE_JOB_ID);

    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID, backupSnapshot.getClusterUniqueId());
    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID, ongoingRestoreJob.getClusterUniqueId());
    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID, finishedRestoreJob.getClusterUniqueId());

    // ongoing restore job was cancelled due to the resurrection
    assertEquals(
        Optional.of(CanceledReason.CLUSTER_RESURRECTING), ongoingRestoreJob.getCanceledReason());
    // finished restore job was not cancelled
    assertFalse(finishedRestoreJob.getCanceled());

    // backupJob removed and put in queue with new clusterUniqueId
    final BackupJob queuedBackupJob = backupJobQueueSvc.findAllJobs(group.getId()).get(0);
    assertTrue(backupJobDao.find(group.getId(), CLUSTER_NAME).isEmpty());
    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID, queuedBackupJob.getClusterUniqueId());

    final CpsOplogSliceMetadataDaoProxy renamedDao =
        cpsOplogStoreFactory.getCpsSliceDaoProxy(
            // build a backupJob to find cpsSliceDao with resurrected clusterUniqueId
            BackupJob.builder()
                .clusterUniqueId(RESURRECTED_CLUSTER_UNIQUE_ID)
                .clusterName(CLUSTER_NAME)
                .build(),
            GROUP_ID,
            OPLOG_STORE_ID);

    // oplog slice metadata dao collections are renamed
    final String newCollectionName = renamedDao.getDbCollection().getName();
    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID + "_" + CLUSTER_NAME, newCollectionName);
    // there are 2 valid slices and 1 invalid slice, only the valid slice should be renamed
    final List<CpsOplogSlice> slices =
        renamedDao.getSlices(
            "test-rs-id", "us-east-1", new BSONTimestamp(0, 0), new BSONTimestamp(5, 0), true);
    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID, slices.get(0).getClusterUniqueId());
    assertEquals(RESURRECTED_CLUSTER_UNIQUE_ID, slices.get(1).getClusterUniqueId());

    final CpsOplogSlice invalidSlice =
        renamedDao.getInvalidUnpurgedSlices("test-rs-id", CloudProvider.AWS, "us-east-1").get(0);
    assertEquals(DELETED_CLUSTER_UNIQUE_ID, invalidSlice.getClusterUniqueId());

    verify(cpsResurrectSvc, times(1)).updateSnapshotTags(any(), any(), any(), any(), any());
  }

  private CpsOplogSlice buildTestSlice(
      boolean isValid, BSONTimestamp startTimeStamp, BSONTimestamp endTimeStamp) {
    return new CpsOplogSlice.Builder()
        .setId(ObjectId.get())
        .setGroupId(group.getId())
        .setClusterUniqueId(DELETED_CLUSTER_UNIQUE_ID)
        .setClusterName(CLUSTER_NAME)
        .setReplicaSetId("test-rs-id")
        .setRegionName(AWSRegionName.US_EAST_1)
        .setNumDocs(1)
        .setProcessedSize(100)
        .setCloudProvider(CloudProvider.AWS)
        .setRawSize(200)
        .setMongodVersion("3.4.0")
        .setCompressor("snappy")
        .setStartTimestamp(startTimeStamp)
        .setEndTimestamp(endTimeStamp)
        .setPurged(false)
        .setValid(isValid)
        .build();
  }
}
