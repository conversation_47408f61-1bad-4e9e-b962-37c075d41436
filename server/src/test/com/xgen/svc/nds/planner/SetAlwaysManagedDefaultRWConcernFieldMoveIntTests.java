package com.xgen.svc.nds.planner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionFactory;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.Status;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import jakarta.inject.Inject;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SetAlwaysManagedDefaultRWConcernFieldMoveIntTests extends JUnit5BaseSvcTest {

  private Group _group;
  private Organization _organization;

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSPlanContext.Factory _planFactory;
  @Inject private PlanDao _planDao;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();

    _organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(_organization, "cus_0001");
  }

  @Test
  public void performInternal() {
    final SetAlwaysManagedDefaultRWConcernFieldMove mockedMove = getMockedMove();

    final Result<?> failedNoClusterName = mockedMove.performInternal();
    assertEquals(Status.FAILED, failedNoClusterName.getStatus());
    assertEquals("Cluster not found: Cluster0", failedNoClusterName.getMessage());

    _clusterDescriptionDao.insertMajority(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), "Cluster0"));

    final Result<?> result = mockedMove.performInternal();
    assertEquals(Status.DONE, result.getStatus());

    assertNotEquals(
        Optional.empty(),
        ClusterDescriptionFactory.get(_clusterDescriptionDao.findOne(new BasicDBObject()))
            .getAlwaysManagedDefaultRWConcernSince());
  }

  private SetAlwaysManagedDefaultRWConcernFieldMove getMockedMove() {
    final Plan plan = new Plan(_group.getId(), _planFactory);
    final SetAlwaysManagedDefaultRWConcernFieldMove move =
        new SetAlwaysManagedDefaultRWConcernFieldMove(
            plan.getPlanContext(), "Cluster0", _clusterDescriptionDao);
    plan.addMove(move);
    _planDao.save(plan);
    return move;
  }
}
