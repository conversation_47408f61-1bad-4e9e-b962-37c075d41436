package com.xgen.svc.nds.planner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.atm.agentjobs._public.view.AgentJobView;
import com.xgen.cloud.atm.core._private.dao.AutomationConfigDao;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.jobqueue._public.model.AgentJobResponse;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.atm.AutomationTestUtils;
import com.xgen.svc.atm.jobs.RotateTLSCertificatesJobHandler;
import com.xgen.svc.atm.jobs.view.RotateTLSCertificatesJobResponseView;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ReloadSslOnProcessesMoveIntTests extends JUnit5NDSBaseTest {

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private PlanDao _planDao;
  @Inject private AutomationConfigDao _automationConfigDao;
  @Inject private AgentJobSvc _agentJobSvc;
  @Inject private ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private NDSPlanContext.Factory _planFactory;
  @Inject private AuditSvc _auditSvc;
  @Inject private NDSClusterSvc _ndsClusterSvc;

  private Group _group;
  private Organization _organization;
  private NDSGroup _ndsGroup;
  private ClusterDescription _clusterDescription;
  private ReplicaSetHardware _replicaSetHardware;
  private ReloadSslOnProcessesMove _move;
  private InstanceHardware _firstInstance;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();

    _organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(_organization, "cus_0001");
    final ObjectId groupId = _group.getId();

    _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);
    _ndsGroup = _ndsGroupDao.find(groupId).get();

    final BasicDBObject containerObj = NDSModelTestFactory.getAWSContainer(AWSRegionName.US_EAST_1);

    final AWSCloudProviderContainer awsContainer = new AWSCloudProviderContainer(containerObj);
    _ndsGroupDao.addCloudContainer(groupId, awsContainer);

    final AutomationConfig config =
        AutomationTestUtils.getAutomationConfigWithReplicaSetForTests(
            groupId, VersionUtils.FIVE_ZERO_ZERO.getVersion());
    _automationConfigDao.save(config);

    final List<String> hostnames =
        config.getDeployment().getProcesses().stream()
            .map(Process::getHostname)
            .collect(Collectors.toList());

    _clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId));
    forceHardwareToProvisioned(_ndsGroupDao.find(groupId).get(), _clusterDescription, hostnames);

    _replicaSetHardware =
        _replicaSetHardwareSvc
            .getReplicaSetHardware(_ndsGroup.getGroupId(), _clusterDescription.getName())
            .get(0);

    _firstInstance = _replicaSetHardware.getAllHardware().findFirst().orElseThrow();

    final Plan plan = new Plan(_group.getId(), _planFactory);
    _move =
        new ReloadSslOnProcessesMove(
            plan.getPlanContext(),
            _clusterDescription.getName(),
            _firstInstance.getInstanceId(),
            _agentJobSvc,
            _replicaSetHardwareSvc,
            _automationConfigSvc,
            _auditSvc,
            _ndsClusterSvc);
    plan.addMove(_move);
    _planDao.save(plan);
  }

  @Test
  public void testPerform() throws SvcException {
    _replicaSetHardwareSvc.setNeedsReloadSslOnProcesses(
        _replicaSetHardware.getId(), _firstInstance.getInstanceId(), false);
    doTestPerform();
  }

  @Test
  public void testPerform_reloadSslOnProcessesRequestedDate() throws SvcException {
    _replicaSetHardwareSvc.setReloadSslOnProcessesRequestedDate(
        _replicaSetHardware.getId(), _firstInstance.getInstanceId(), false);

    doTestPerform();

    assertFalse(
        _replicaSetHardwareSvc
            .getReplicaSetHardware(_ndsGroup.getGroupId(), _clusterDescription.getName())
            .get(0)
            .getAllHardware()
            .findFirst()
            .orElseThrow()
            .getReloadSslOnProcessesRequestedDate()
            .isPresent(),
        "reloadSslOnProcessesRequestedDate should be cleared after move is done");
  }

  private void doTestPerform() throws SvcException {
    {
      // First run should create an Agent Job
      final Result<NoData> result = _move.perform();
      assertTrue(result.getStatus().isInProgress());

      final List<AgentJobView> jobs =
          _agentJobSvc.getNewJobs(
              _ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get());
      assertEquals(1, jobs.size());
      assertEquals(RotateTLSCertificatesJobHandler.COMMAND.toString(), jobs.get(0).getCommand());
    }

    {
      // Second run should not create a new Agent Job
      final Result<NoData> result = _move.perform();
      assertTrue(result.getStatus().isInProgress());
      final List<AgentJobView> jobs =
          _agentJobSvc.getNewJobs(
              _ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get());
      assertEquals(1, jobs.size());
    }

    // Simulate the agent picking  up and completing the Agent Job
    final AgentJobView reloadJob =
        _agentJobSvc
            .getNewJobs(_ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get())
            .get(0);
    _agentJobSvc.completeJob(
        new RotateTLSCertificatesJobResponseView(
            reloadJob.getId(), reloadJob.getCommand(), AgentJobResponse.STATUS_SUCCESS, null));

    {
      // Move should now be done since Agent Job is done
      final Result<NoData> result = _move.perform();
      assertTrue(result.getStatus().isDone());

      // verify audit event
      List<Event> events =
          _auditSvc.findByEventTypeMostRecentFirst(NDSAudit.Type.RELOAD_SSL_ON_PROCESSES);
      assertEquals(events.size(), 1);

      final InstanceHardware firstInstance =
          _replicaSetHardwareSvc
              .getReplicaSetHardware(_ndsGroup.getGroupId(), _clusterDescription.getName())
              .get(0)
              .getAllHardware()
              .findFirst()
              .orElseThrow();
      assertFalse(firstInstance.needsReloadSslOnProcesses());
    }
  }

  @Test
  public void testRollback_ExceedJobFailures() throws SvcException {
    {
      // First run should create an Agent Job
      final Result<NoData> result = _move.perform();
      assertTrue(result.getStatus().isInProgress());

      final List<AgentJobView> jobs =
          _agentJobSvc.getNewJobs(
              _ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get());
      assertEquals(1, jobs.size());
      assertEquals(RotateTLSCertificatesJobHandler.COMMAND.toString(), jobs.get(0).getCommand());
    }

    // Simulate the Agent Job failing 3 times, which should trigger the Move to fail
    for (int n = 0; n < 4; n++) {
      final AgentJobView reloadJob =
          _agentJobSvc
              .getNewJobs(_ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get())
              .get(0);
      _agentJobSvc.markAsFailed(reloadJob.getId(), "failed");

      if (n < 3) {
        // The first run recognizes that Agent Job has failed and clears state, so the second
        // run actually makes the new job
        assertTrue(_move.perform().getStatus().isInProgress());
        assertTrue(_move.perform().getStatus().isInProgress());
      } else {
        assertTrue(_move.perform().getStatus().isFailed());
      }
    }

    assertTrue(_move.rollback().getStatus().isDone());
  }

  @Test
  public void testRollback_RollbackJob() {
    {
      // First run should create an Agent Job
      final Result<NoData> result = _move.perform();
      assertTrue(result.getStatus().isInProgress());

      final List<AgentJobView> jobs =
          _agentJobSvc.getNewJobs(
              _ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get());
      assertEquals(1, jobs.size());
      assertEquals(RotateTLSCertificatesJobHandler.COMMAND.toString(), jobs.get(0).getCommand());
    }

    assertTrue(_move.rollback().getStatus().isDone());

    // No agent jobs should be left running after the Move is rolled back.
    final List<AgentJobView> jobs =
        _agentJobSvc.getNewJobs(
            _ndsGroup.getGroupId(), _firstInstance.getHostnameForAgents().get());
    assertEquals(0, jobs.size());
  }
}
