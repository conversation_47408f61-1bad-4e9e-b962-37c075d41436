package com.xgen.module.account.okta.model.tokenhook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenHookRequest {
  @JsonProperty(FieldDefs.DATA)
  private TokenHookRequestData tokenHookRequestData;

  public TokenHookRequest() {}

  public TokenHookRequest(TokenHookRequestData pHookRequestData) {
    tokenHookRequestData = pHookRequestData;
  }

  public TokenHookRequestData getTokenHookRequestData() {
    return tokenHookRequestData;
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TokenHookRequestData {
    @JsonProperty(FieldDefs.CONTEXT)
    private TokenHookRequestContext tokenHookRequestContext;

    public TokenHookRequestData() {}

    public TokenHookRequestData(TokenHookRequestContext pTokenHookRequestContext) {
      tokenHookRequestContext = pTokenHookRequestContext;
    }

    public TokenHookRequestContext getTokenHookRequestContext() {
      return tokenHookRequestContext;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TokenHookRequestContext {
      @JsonProperty(FieldDefs.SESSION)
      private OktaSessionData oktaSessionData;

      public TokenHookRequestContext() {}

      public TokenHookRequestContext(OktaSessionData pOktaSessionData) {
        oktaSessionData = pOktaSessionData;
      }

      public OktaSessionData getOktaSessionData() {
        return oktaSessionData;
      }
    }
  }

  private static final class FieldDefs {
    private static final String DATA = "data";
    private static final String CONTEXT = "context";
    private static final String SESSION = "session";
  }
}
