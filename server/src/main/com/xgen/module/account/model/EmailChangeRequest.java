package com.xgen.module.account.model;

import static com.xgen.module.account.model.EmailChangeRequest.FieldDefs.CREATED_FIELD;
import static com.xgen.module.account.model.EmailChangeRequest.FieldDefs.CURRENT_EMAIL_ADDRESS_FIELD;
import static com.xgen.module.account.model.EmailChangeRequest.FieldDefs.NEW_EMAIL_ADDRESS_FIELD;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import org.bson.codecs.pojo.annotations.BsonProperty;

public class EmailChangeRequest {
  public static final String DB_NAME = "account";
  public static final String COLLECTION_NAME = "emailChangeRequest";

  private String id;

  private String currentEmailAddress;

  @JsonProperty(NEW_EMAIL_ADDRESS_FIELD)
  private String newEmailAddress;

  @JsonProperty(CREATED_FIELD)
  private Date created;

  public EmailChangeRequest(
      String pId, String pCurrentEmail<PERSON>ddress, String pNewEmailAddress, Date pCreated) {
    id = pId;
    currentEmailAddress = pCurrentEmailAddress;
    newEmailAddress = pNewEmailAddress;
    created = pCreated;
  }

  public EmailChangeRequest() {}

  public String getId() {
    return id;
  }

  public void setId(String pId) {
    id = pId;
  }

  @BsonProperty(CURRENT_EMAIL_ADDRESS_FIELD)
  public String getCurrentEmailAddress() {
    return currentEmailAddress;
  }

  public void setCurrentEmailAddress(String pCurrentEmailAddress) {
    currentEmailAddress = pCurrentEmailAddress;
  }

  @BsonProperty(NEW_EMAIL_ADDRESS_FIELD)
  public String getNewEmailAddress() {
    return newEmailAddress;
  }

  public void setNewEmailAddress(String pNewEmailAddress) {
    newEmailAddress = pNewEmailAddress;
  }

  @BsonProperty(CREATED_FIELD)
  public Date getCreated() {
    return created;
  }

  public void setCreated(Date pDate) {
    created = pDate;
  }

  public static class FieldDefs {
    public static final String CURRENT_EMAIL_ADDRESS_FIELD = "currentEmailAddress";
    public static final String NEW_EMAIL_ADDRESS_FIELD = "newEmailAddress";
    public static final String CREATED_FIELD = "created";
  }
}
