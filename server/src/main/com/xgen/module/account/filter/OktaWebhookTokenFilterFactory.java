package com.xgen.module.account.filter;

import com.xgen.cloud.access.authn._public.authenticator.OktaWebhookTokenAuthenticator;
import com.xgen.cloud.common.filter._public.ResourceFilter;
import com.xgen.module.account.annotation.OktaWebhookTokenRequired;
import com.xgen.module.iam.config.IamAppSettings;
import com.xgen.svc.mms.res.filter.MethodOverClass;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.ResourceInfo;
import java.util.List;

public class OktaWebhookTokenFilterFactory extends MethodOverClass<OktaWebhookTokenRequired> {

  private final IamAppSettings iamAppSettings;
  private final OktaWebhookTokenAuthenticator oktaWebhookTokenAuthenticator;

  @Inject
  public OktaWebhookTokenFilterFactory(
      IamAppSettings iamAppSettings, OktaWebhookTokenAuthenticator oktaWebhookTokenAuthenticator) {
    super(OktaWebhookTokenRequired.class);
    this.iamAppSettings = iamAppSettings;
    this.oktaWebhookTokenAuthenticator = oktaWebhookTokenAuthenticator;
  }

  @Override
  protected List<ResourceFilter> createAnnotationFilters(
      ResourceInfo resourceInfo, OktaWebhookTokenRequired annotation) {
    return List.of(
        new OktaWebhookTokenFilter(
            iamAppSettings,
            oktaWebhookTokenAuthenticator,
            annotation.issuer(),
            annotation.audience()));
  }
}
