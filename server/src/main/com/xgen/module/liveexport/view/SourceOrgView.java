package com.xgen.module.liveexport.view;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.module.liveexport.model.SourceOrg;
import io.swagger.v3.oas.annotations.Hidden;
import java.beans.ConstructorProperties;
import java.util.Date;
import org.bson.types.ObjectId;

@Hidden
public class SourceOrgView {

  @JsonProperty(FieldDefs.SOURCE_ORG_ID)
  private final ObjectId _sourceOrgId;

  @JsonProperty(FieldDefs.CREATED_AT)
  private final Date _createdAt;

  @JsonProperty(FieldDefs.LAST_SYNCED_AT)
  private final Date _lastSyncedAt;

  @JsonProperty(FieldDefs.ERROR_MESSAGE)
  private final String _errorMessage;

  @JsonProperty(FieldDefs.TARGET_ORG)
  private final TargetOrgView _targetOrg;

  @ConstructorProperties({
    FieldDefs.SOURCE_ORG_ID,
    FieldDefs.CREATED_AT,
    FieldDefs.LAST_SYNCED_AT,
    FieldDefs.TARGET_ORG,
    FieldDefs.ERROR_MESSAGE,
  })
  @JsonCreator
  public SourceOrgView(
      final ObjectId pSourceOrgId,
      final Date pCreatedAt,
      final Date pUpdatedAt,
      final TargetOrgView pTargetOrg,
      final String pErrorMessage) {
    _sourceOrgId = pSourceOrgId;
    _createdAt = pCreatedAt;
    _lastSyncedAt = pUpdatedAt;
    _targetOrg = pTargetOrg;
    _errorMessage = pErrorMessage;
  }

  public SourceOrgView(final SourceOrg sourceOrg) {
    this(
        sourceOrg.getSourceOrgId(),
        sourceOrg.getCreatedAt(),
        sourceOrg.getLastSyncedAt(),
        new TargetOrgView(sourceOrg.getTargetOrg()),
        sourceOrg.getErrorMessage());
  }

  public ObjectId getSourceOrgId() {
    return _sourceOrgId;
  }

  public Date getCreatedAt() {
    return _createdAt;
  }

  public Date getUpdatedAt() {
    return _lastSyncedAt;
  }

  public String getErrorMessage() {
    return _errorMessage;
  }

  public TargetOrgView getTargetOrg() {
    return _targetOrg;
  }

  public static final class FieldDefs {
    public static final String SOURCE_ORG_ID = "sourceOrgId";
    public static final String CREATED_AT = "createdAt";
    public static final String LAST_SYNCED_AT = "lastSyncedAt";
    public static final String TARGET_ORG = "targetOrg";
    public static final String ERROR_MESSAGE = "errorMessage";
  }
}
