package com.xgen.module.liveexport.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.liveexport.model.OrganizationLinkStatus;
import com.xgen.module.liveexport.model.SourceOrg;
import com.xgen.module.liveexport.svc.link.SourceOrgSvc;
import com.xgen.module.liveexport.view.MigrationLinkStatusView;
import com.xgen.module.liveexport.view.SourceOrgView;
import com.xgen.module.liveimport.view.TargetOrgView;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;

@Path("/orgs/{orgId}/liveExport/migrationLink")
@Singleton
public class OrganizationLiveExportMigrationLinkResource extends BaseResource {
  private final SourceOrgSvc _sourceOrgSvc;
  private final AppSettings _appSettings;

  @Inject
  public OrganizationLiveExportMigrationLinkResource(
      final SourceOrgSvc pSourceOrgSvc, final AppSettings pAppSettings) {
    _sourceOrgSvc = pSourceOrgSvc;
    _appSettings = pAppSettings;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveExportMigrationLinkResource.getMigrationLink.GET")
  public Response getMigrationLink(@Context final Organization pOrganization) {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final Optional<SourceOrg> sourceOrg = _sourceOrgSvc.getOrgLink(pOrganization);
    if (sourceOrg.isPresent()) {
      return Response.ok().entity(new SourceOrgView(sourceOrg.get())).build();
    }
    return SimpleApiResponse.notFound().build();
  }

  @POST
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveExportMigrationLinkResource.createMigrationLink.POST")
  public Response createMigrationLink(
      @Context final Organization pOrganization, final TargetOrgView pRequest) throws SvcException {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final SourceOrgView result = _sourceOrgSvc.migrationLinkFromApi(pRequest, pOrganization);
    return Response.accepted(result).build();
  }

  @DELETE
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveExportMigrationLinkResource.deleteMigrationLink.DELETE")
  public Response deleteMigrationLink(@Context final Organization pOrganization)
      throws SvcException {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final Optional<SourceOrg> sourceOrg = _sourceOrgSvc.getOrgLink(pOrganization);
    if (sourceOrg.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    _sourceOrgSvc.deleteOrgLink(pOrganization);
    return Response.noContent().build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/status")
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveExportMigrationLinkResource.migrationLinkStatus.GET")
  public Response migrationLinkStatus(@Context final Organization pOrganization) {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final Optional<SourceOrg> sourceOrg = _sourceOrgSvc.getOrgLink(pOrganization);
    if (sourceOrg.isEmpty()) {
      return SimpleApiResponse.notFound().build();
    }

    MigrationLinkStatusView view = new MigrationLinkStatusView(OrganizationLinkStatus.NOT_SYNCED);
    if (sourceOrg.get().getLastSyncedAt() != null) {
      view = new MigrationLinkStatusView(OrganizationLinkStatus.SYNCED);
    }

    return Response.ok().entity(view).build();
  }
}
