package com.xgen.module.liveexport.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.liveexport.model.SourceOrg;
import com.xgen.module.liveexport.svc.link.SourceOrgSvc;
import com.xgen.module.liveexport.svc.sync.AtlasOrganizationSyncSvc;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;

@Path("/orgs/{orgId}/liveExport/availableProjects")
@Singleton
public class OrganizationLiveExportResource extends BaseResource {

  private final SourceOrgSvc _sourceOrgSvc;
  private final AtlasOrganizationSyncSvc _atlasOrganizationSyncSvc;
  private final AppSettings _appSettings;

  @Inject
  public OrganizationLiveExportResource(
      final SourceOrgSvc pSourceOrgSvc,
      final AtlasOrganizationSyncSvc pAtlasOrganizationSyncSvc,
      final AppSettings pAppSettings) {
    _sourceOrgSvc = pSourceOrgSvc;
    _atlasOrganizationSyncSvc = pAtlasOrganizationSyncSvc;
    _appSettings = pAppSettings;
  }

  @POST
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveExportResource.syncAvailableProjects.POST")
  public Response syncAvailableProjects(@Context final Organization pOrganization)
      throws SvcException {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final Optional<SourceOrg> sourceOrg = _sourceOrgSvc.getOrgLink(pOrganization);
    if (sourceOrg.isEmpty()) {
      return SimpleApiResponse.notFound().build();
    }

    _atlasOrganizationSyncSvc.syncOrgToAtlas(pOrganization);
    return SimpleApiResponse.ok().resource(EMPTY_JSON_OBJECT).build();
  }
}
