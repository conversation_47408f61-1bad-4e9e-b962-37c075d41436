package com.xgen.module.common.planner.model;

import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.COMMA_DELIMITER;

import com.google.common.annotations.VisibleForTesting;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.util._public.logging.LogLevel;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Move.Phase;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.module.common.planner.model.Result.Status;
import com.xgen.module.mdc.MDCUtil;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import jakarta.annotation.Nullable;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * A Step is a concrete action that is contained within a Move in order to accomplish some larger
 * goal state of a Plan.
 */
public abstract class Step<DataType> {
  private static final String NDS_PLAN_STATE_IN_MEMORY_OBJECT_DISABLED_STEPS =
      "nds.planState.inMemoryObject.disabled.steps";
  protected final PlanContext _planContext;
  protected final State _state;

  @VisibleForTesting
  static final Histogram PLANNER_STEP_DURATION_SECONDS =
      PromMetricsSvc.registerHistogram(
          "mms_planner_step_duration_seconds",
          "Run time for single invocation of executing step",
          PromMetricsSvc.getHistogramExpBucket(0.5, 2, 10),
          "step_name",
          "phase",
          "status");

  @VisibleForTesting
  static final Histogram PLANNER_STEP_OVERALL_DURATION_SECONDS =
      PromMetricsSvc.registerHistogram(
          "mms_planner_step_overall_duration_seconds",
          "Run time for step from first to nominally last invocation. Nonsensical for steps that do"
              + " not reach a terminal status",
          PromMetricsSvc.getHistogramExpBucket(1, 2, 19),
          "step_name",
          "phase",
          "status");

  public Step(final PlanContext pContext, final State pState) {
    _planContext = pContext;
    _state =
        pState != null
                && pState.isStepStateCacheEnabled()
                && pContext
                    .getAppSettings()
                    .getListProperty(
                        NDS_PLAN_STATE_IN_MEMORY_OBJECT_DISABLED_STEPS, COMMA_DELIMITER)
                    .contains(getClass().getSimpleName())
            ? new Step.State(pState._planId, pState._moveId, pState._stepIdx, pContext.getPlanDao())
            : pState;
  }

  protected abstract PlanContext getContext();

  protected abstract Result<DataType> performInternal();

  /**
   * The Step performs the specific actions related to the Step itself potentially modifies its own
   * state.
   */
  public final Result<DataType> perform() {
    final Date performInvocationStartDate = new Date();

    if (hasRollbackStarted()) {
      final String errMsg =
          String.format("%s is rolling back. Cannot perform.", getClass().getSimpleName());
      getContext().getLogger().atError().setMessage(errMsg).log();
      throw new IllegalStateException(errMsg);
    }

    if (!hasPerformStarted()) {
      setPerformStartedDate();
    }

    /*
     * Although the step may have already been performed, the step is responsible for providing its
     * own result such that no caching mechanism is enforced. Steps that do not have {@link
     * #shouldTrackTimeToDoneForPerform()} false are expected to stop doing meaningful work after
     * reaching a state of done
     */
    final Result<DataType> performResult;
    try {
      MDC.put(MDCUtil.STEP_NAME, getClass().getSimpleName());
      performResult = performInternal();
    } finally {
      MDC.remove(MDCUtil.STEP_NAME);
    }
    if (performResult.getStatus().isDone() && !isPerformed()) {
      setPerformed();
      if (shouldTrackTimeToDoneForPerform()) {
        setPerformEndedDate();
        // The first time a step reaches a state of performed, publish overall duration
        // to reach that state to prom unless the step opts out
        publishHistogramMetrics(
            PLANNER_STEP_OVERALL_DURATION_SECONDS,
            TimeUnit.SECONDS,
            getPerformStartedDate(),
            Phase.PERFORM,
            performResult.getStatus());
      }
    }

    if (performResult.getStatus().isFailed()) {
      getContext()
          .getLogger()
          .atWarn()
          .setMessage("Step failed")
          .addKeyValue("name", getClass().getSimpleName())
          .addKeyValue("result", performResult.toString())
          .log();
    }

    publishHistogramMetrics(
        PLANNER_STEP_DURATION_SECONDS,
        TimeUnit.SECONDS,
        performInvocationStartDate,
        Phase.PERFORM,
        performResult.getStatus());
    return performResult;
  }

  protected Result<NoData> rollbackInternal() {
    getContext().getLogger().debug("{} doesn't rollback", getClass().getSimpleName());
    return Result.done();
  }

  /** The Step attempts to rollback any changes it has made based on its state. */
  public final Result<NoData> rollback() {
    final Date rollbackInvocationStartDate = new Date();

    if (isRolledBack()) {
      getContext().getLogger().debug("{} already rolled back", getClass().getSimpleName());
      return Result.done();
    } else if (!hasPerformStarted()) {
      getContext()
          .getLogger()
          .debug(
              "Nothing to rollback as this step ({}) has never started performing",
              getClass().getSimpleName());
      return Result.done();
    }

    if (!hasRollbackStarted()) {
      setRollbackStarted();
    }

    final Result<NoData> result = rollbackInternal();
    if (result.getStatus().isDone() && !isRolledBack()) {
      setRollbackEndedDate();
      publishHistogramMetrics(
          PLANNER_STEP_OVERALL_DURATION_SECONDS,
          TimeUnit.SECONDS,
          getPerformStartedDate(),
          Phase.ROLLBACK,
          result.getStatus());
    }

    publishHistogramMetrics(
        PLANNER_STEP_DURATION_SECONDS,
        TimeUnit.SECONDS,
        rollbackInvocationStartDate,
        Phase.ROLLBACK,
        result.getStatus());
    return result;
  }

  public static class State implements IState {
    private static final Delimiter DELIMITER = Delimiter.COLON;

    private final ObjectId _planId;
    private final ObjectId _moveId;
    private final int _stepIdx;
    protected final PlanDao _planDao;
    @Nullable protected final AppSettings _appSettings;
    @Nullable protected final BasicDBObject _stepStateCache;

    private static final Logger LOG = LoggerFactory.getLogger(Step.State.class);
    private static final Counter STEP_STATE_CACHE_READS_TOTAL =
        NDSPromMetricsSvc.registerCounter(
            "mms_nds_step_state_cache_reads_total",
            "Count of reads on step state cache",
            "is_dry_run");

    public State(
        final ObjectId pPlanId, final ObjectId pMoveId, final int pIdx, final PlanDao pPlanDao) {
      this(pPlanId, pMoveId, pIdx, pPlanDao, null, null);
    }

    public State(
        final ObjectId pPlanId,
        final ObjectId pMoveId,
        final int pIdx,
        final PlanDao pPlanDao,
        final AppSettings pAppSettings,
        final BasicDBObject pStepStateCache) {
      _planId = pPlanId;
      _moveId = pMoveId;
      _stepIdx = pIdx;
      _planDao = pPlanDao;
      _appSettings = pAppSettings;
      _stepStateCache = pStepStateCache;
    }

    @VisibleForTesting
    public void setValue(final String pField, final Object pObject) {
      if (isStepStateCacheEnabled()) {
        final Object value =
            _planDao.updateAndGetStepValue(_planId, _moveId, _stepIdx, pField, pObject);
        synchronized (_stepStateCache) {
          _stepStateCache.put(getStateFieldWithIndex(pField), value);
        }
      } else {
        _planDao.updateStepValue(_planId, _moveId, _stepIdx, pField, pObject);
      }
    }

    public ObjectId getPlanId() {
      return _planId;
    }

    public ObjectId getMoveId() {
      return _moveId;
    }

    public int getStepIdx() {
      return _stepIdx;
    }

    public Optional<Object> getValue(final String pField) {
      final String field = getStateFieldWithIndex(pField);
      if (isStepStateCacheEnabled()) {
        final boolean isDryRun =
            !(_appSettings != null && _appSettings.getPlanStateInMemoryObjectReadsEnabled());
        final Optional<Object> valueFromCache = Optional.ofNullable(_stepStateCache.get(field));

        NDSPromMetricsSvc.incrementCounter(
            STEP_STATE_CACHE_READS_TOTAL, Boolean.toString(isDryRun));
        if (isDryRun) {
          Optional<Object> valueFromDB = _planDao.findStepValue(_planId, _moveId, _stepIdx, pField);
          try {
            if ((valueFromDB.isPresent() ? valueFromCache.isEmpty() : valueFromCache.isPresent())
                || (valueFromDB.isPresent()
                    && !Objects.deepEquals(valueFromCache.get(), valueFromDB.get()))) {
              LOG.warn(
                  String.format(
                      "mismatch encountered between step state in memory cache: [%s] and db: [%s]"
                          + " for field %s in plan %s move %s",
                      valueFromCache.map(Object::toString).orElse(null),
                      valueFromDB.map(Object::toString).orElse(null),
                      pField,
                      _planId,
                      _moveId));
            }
          } catch (Exception e) {
            LOG.warn(
                String.format(
                    "error found while comparing step state in memory cache: [%s] and db: [%s] for"
                        + " field %s in plan %s move %s",
                    valueFromCache.map(Object::toString).orElse(null),
                    valueFromDB.map(Object::toString).orElse(null),
                    pField,
                    _planId,
                    _moveId),
                e);
          }

          return valueFromDB;
        } else {
          return valueFromCache;
        }
      }

      return _planDao.findStepValue(_planId, _moveId, _stepIdx, pField);
    }

    public boolean isStepStateCacheEnabled() {
      return _stepStateCache != null;
    }

    public State toMultiState(final int pMultiIdx) {
      return new MultiState(
          _planId,
          _moveId,
          _stepIdx,
          String.format("%s", pMultiIdx),
          _planDao,
          _appSettings,
          _stepStateCache);
    }

    private String getStateFieldWithIndex(final String pStateField) {
      return Delimiter.bindWithDelimiter(DELIMITER, Integer.toString(_stepIdx), pStateField);
    }
  }

  public static class MultiState extends State {
    private static final Logger LOG = LoggerFactory.getLogger(MultiState.class);
    private static final Delimiter DELIMITER = Delimiter.UNDERSCORE;

    private final String _multiIdx;

    private MultiState(
        final ObjectId pPlanId,
        final ObjectId pMoveId,
        final int pIdx,
        final String pMultiIdx,
        final PlanDao pPlanDao,
        final AppSettings pAppSettings,
        final BasicDBObject pStepStateCache) {
      super(pPlanId, pMoveId, pIdx, pPlanDao, pAppSettings, pStepStateCache);
      _multiIdx = pMultiIdx;
    }

    @Override
    @VisibleForTesting
    public void setValue(final String pField, final Object pObject) {
      super.setValue(getStateFieldWithMultiStateIndex(pField), pObject);
    }

    @Override
    public Optional<Object> getValue(final String pField) {
      return super.getValue(getStateFieldWithMultiStateIndex(pField));
    }

    private String getStateFieldWithMultiStateIndex(final String pStateField) {
      return Delimiter.bindWithDelimiter(DELIMITER, _multiIdx, pStateField);
    }

    @Override
    public State toMultiState(int pMultiIdx) {
      LOG.warn(
          "toMultiState called on MultiState, this is now safe but there used to be dragons here",
          new Throwable("Stack trace for identifying callers"));
      return new MultiState(
          getPlanId(),
          getMoveId(),
          getStepIdx(),
          String.format("%s_%s", _multiIdx, pMultiIdx),
          _planDao,
          _appSettings,
          _stepStateCache);
    }
  }

  private String getStateField(final String pField) {
    return String.format("%s:%s", getClass().getSimpleName(), pField);
  }

  public final void setStateValue(final String pField, final Object pObject) {
    _state.setValue(getStateField(pField), pObject);
  }

  public final Optional<Object> getStateValue(final String pField) {
    return _state.getValue(getStateField(pField));
  }

  public final Optional<String> getStateStringValue(final String pField) {
    return _state.getStringValue(getStateField(pField));
  }

  public final Optional<ObjectId> getStateObjectIdValue(final String pField) {
    return _state.getObjectIdValue(getStateField(pField));
  }

  public final Optional<Integer> getStateIntegerValue(final String pField) {
    return _state.getIntegerValue(getStateField(pField));
  }

  public final Optional<Long> getStateLongValue(final String pField) {
    return _state.getLongValue(getStateField(pField));
  }

  public final Optional<Double> getStateDoubleValue(final String pField) {
    return _state.getDoubleValue(getStateField(pField));
  }

  public final Optional<Date> getStateDateValue(final String pField) {
    return _state.getDateValue(getStateField(pField));
  }

  public final Optional<Boolean> getStateBooleanValue(final String pField) {
    return _state.getBooleanValue(getStateField(pField));
  }

  public final Optional<BasicDBObject> getStateBasicDBObjectValue(final String pField) {
    return _state.getBasicDBObjectValue(getStateField(pField));
  }

  public final <T> Optional<List<T>> getStateListValue(final String pField, Class<T> elemClass) {
    return _state.getListValue(getStateField(pField), elemClass);
  }

  protected State getState() {
    return _state;
  }

  public final Result<NoData> evaluateAttempt(
      final boolean pConditionPassed,
      final String pName,
      final Duration pLimit,
      final String pDescription) {
    return evaluateAttempt(
        pConditionPassed,
        pName,
        pLimit,
        pDescription,
        Result.DEFAULT_IN_PROGRESS_WAIT_TIME_SECONDS);
  }

  public final Result<NoData> evaluateAttempt(
      final boolean pConditionPassed,
      final String pName,
      final Duration pLimit,
      final String pDescription,
      final long pYieldPeriodSeconds) {
    return new EvaluateAttempts(getClass(), getContext(), getState(), this::getStateField)
        .evaluateAttempt(pConditionPassed, pName, pLimit, pDescription, pYieldPeriodSeconds);
  }

  public final Result<NoData> evaluateAttempt(
      final boolean pConditionPassed,
      final String pName,
      final int pLimit,
      final String pDescription) {
    return evaluateAttempt(
        pConditionPassed,
        pName,
        pLimit,
        pDescription,
        Result.DEFAULT_IN_PROGRESS_WAIT_TIME_SECONDS);
  }

  public final Result<NoData> evaluateAttempt(
      final boolean pConditionPassed,
      final String pName,
      final int pLimit,
      final String pDescription,
      final long pYieldPeriodSeconds) {
    return new EvaluateAttempts(getClass(), getContext(), getState(), this::getStateField)
        .evaluateAttempt(pConditionPassed, pName, pLimit, pDescription, pYieldPeriodSeconds);
  }

  public final Result<NoData> evaluateAttempt(
      final boolean pConditionPassed,
      final String pName,
      final int pLimit,
      final String pDescription,
      final LogLevel pLevel) {
    return evaluateAttempt(
        pConditionPassed,
        pName,
        pLimit,
        pDescription,
        pLevel,
        Result.DEFAULT_IN_PROGRESS_WAIT_TIME_SECONDS);
  }

  public final Result<NoData> evaluateAttempt(
      final boolean pConditionPassed,
      final String pName,
      final int pLimit,
      final String pDescription,
      final LogLevel pLevel,
      final long pYieldPeriodSeconds) {
    return new EvaluateAttempts(getClass(), getContext(), getState(), this::getStateField)
        .evaluateAttempt(
            pConditionPassed, pName, pLimit, pDescription, pLevel, pYieldPeriodSeconds);
  }

  public final boolean hasAttemptTimePassed(final String pName, final Duration pLimit) {
    return new EvaluateAttempts(getClass(), getContext(), getState(), this::getStateField)
        .hasAttemptTimePassed(pName, pLimit);
  }

  public final boolean isPerformed() {
    return getStateValue(StateFields_DoNotIntroduceNewReferences.PERFORMED).isPresent();
  }

  @VisibleForTesting
  public void setPerformed() {
    setStateValue(StateFields_DoNotIntroduceNewReferences.PERFORMED, true);
  }

  @VisibleForTesting
  protected void setPerformEndedDate() {
    setStateValue(StateFields_DoNotIntroduceNewReferences.PERFORM_ENDED_DATE, new Date());
  }

  protected final Date getPerformEndedDate() {
    return getStateDateValue(StateFields_DoNotIntroduceNewReferences.PERFORM_ENDED_DATE)
        .orElse(null);
  }

  public boolean shouldTrackTimeToDoneForPerform() {
    return true;
  }

  public final boolean isRolledBack() {
    return getStateValue(StateFields_DoNotIntroduceNewReferences.ROLLBACK_ENDED_DATE).isPresent()
        || getStateValue(StateFields_DoNotIntroduceNewReferences.ROLLED_BACK)
            .isPresent(); // CLOUDP-192898 - delete once TTL expires
  }

  @VisibleForTesting
  protected void setRollbackEndedDate() {
    setStateValue(StateFields_DoNotIntroduceNewReferences.ROLLBACK_ENDED_DATE, new Date());
  }

  public final boolean hasPerformStarted() {
    return getStateValue(StateFields_DoNotIntroduceNewReferences.PERFORM_STARTED_DATE).isPresent();
  }

  @VisibleForTesting
  public final Date getPerformStartedDate() {
    return getStateDateValue(StateFields_DoNotIntroduceNewReferences.PERFORM_STARTED_DATE)
        .orElse(new Date());
  }

  @VisibleForTesting
  protected void setPerformStartedDate() {
    setStateValue(StateFields_DoNotIntroduceNewReferences.PERFORM_STARTED_DATE, new Date());
  }

  @VisibleForTesting
  public boolean hasRollbackStarted() {
    return getStateValue(StateFields_DoNotIntroduceNewReferences.ROLLBACK_STARTED_DATE).isPresent();
  }

  @VisibleForTesting
  protected void setRollbackStarted() {
    setStateValue(StateFields_DoNotIntroduceNewReferences.ROLLBACK_STARTED_DATE, new Date());
  }

  @VisibleForTesting
  protected void publishHistogramMetrics(
      final Histogram pHistogram,
      final TimeUnit pTimeUnit,
      final Date pStartTime,
      final Phase pPhase,
      final Status pStatus) {
    PromMetricsSvc.recordTimer(
        pHistogram,
        pStartTime,
        pTimeUnit,
        this.getClass().getSimpleName(),
        pPhase.name(),
        pStatus.name());
  }

  @VisibleForTesting
  // Also visible for https://jira.mongodb.org/browse/CLOUDP-193241 - avoid adding new references to
  // this class outside step
  public static class StateFields_DoNotIntroduceNewReferences {
    public static final String PERFORMED = "performed";
    static final String ROLLED_BACK = "rolledBack";
    public static final String PERFORM_STARTED_DATE = "performStartedDate";
    static final String ROLLBACK_STARTED_DATE = "rollbackStartedDate";
    public static final String PERFORM_ENDED_DATE = "performEndedDate";
    static final String ROLLBACK_ENDED_DATE = "rollbackEndedDate";
  }

  public enum Delimiter {
    COLON(":"),
    UNDERSCORE("_");

    private final String delimiter;

    Delimiter(final String pDelimiter) {
      this.delimiter = pDelimiter;
    }

    public String getDelimiter() {
      return delimiter;
    }

    public static String bindWithDelimiter(
        final Delimiter pDelimiter, final String pFirst, final String pSecond) {
      return String.join(pDelimiter.getDelimiter(), pFirst, pSecond);
    }

    public static String getStepNameFromKey(final String pStepKey) {
      String step = pStepKey;
      for (Delimiter delimiter : Step.Delimiter.values()) {
        if (step.contains(delimiter.getDelimiter())) {
          step = step.substring(step.lastIndexOf(delimiter.getDelimiter()) + 1);
        }
      }
      return step;
    }
  }
}
