package com.xgen.module.metering.client.svc.grpc;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.module.metering.client.svc.IMeterReportSvc;
import com.xgen.module.metering.client.svc.MeteringServiceClient;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterUsage;
import jakarta.inject.Inject;
import java.util.List;

public class GrpcMigrationMeterReportService implements IMeterReportSvc {

  private static final String METERING_GRPC_CLIENT_ENABLED = "metering.grpc.client.%s.enabled";
  private final MeteringServiceClient meteringServiceClient;
  private final MeteringSubmissionClient meteringSubmissionClient;
  private final AppSettings appSettings;

  @Inject
  public GrpcMigrationMeterReportService(
      MeteringServiceClient meteringServiceClient,
      MeteringSubmissionClient meteringSubmissionClient,
      AppSettings appSettings) {
    this.meteringServiceClient = meteringServiceClient;
    this.meteringSubmissionClient = meteringSubmissionClient;
    this.appSettings = appSettings;
  }

  @Override
  public void submitMeterUsage(List<MeterUsage> meterUsages, String ingestionJobName)
      throws MeterSvcException {

    if (isGrpcClientEnabled(ingestionJobName)) {
      meteringSubmissionClient.submitMeterUsageV2(meterUsages, ingestionJobName);
    } else {
      meteringServiceClient.submitMeterUsageV2(meterUsages, ingestionJobName);
    }
  }

  private boolean isGrpcClientEnabled(String ingestionJobName) {
    return appSettings.getBoolProp(getJobNameKey(ingestionJobName), false);
  }

  String getJobNameKey(String ingestionJobName) {
    return METERING_GRPC_CLIENT_ENABLED.formatted(ingestionJobName);
  }
}
