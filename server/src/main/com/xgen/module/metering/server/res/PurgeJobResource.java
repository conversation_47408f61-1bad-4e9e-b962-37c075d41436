package com.xgen.module.metering.server.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.metering.common.model.PurgeJobRuntimeWindow;
import com.xgen.module.metering.common.view.PurgeJobRuntimeWindowView;
import com.xgen.module.metering.server.dao.PurgeJobRuntimeWindowDao.PurgeJobRuntimeWindowSaveError;
import com.xgen.module.metering.server.svc.PurgeJobRuntimeWindowSvc;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;

@Path("/admin/metering/purgejob")
@Singleton
public class PurgeJobResource extends ApiBaseResource {

  private final PurgeJobRuntimeWindowSvc purgeJobRuntimeWindowSvc;

  @Inject
  public PurgeJobResource(
      AppSettings appSettings, PurgeJobRuntimeWindowSvc purgeJobRuntimeWindowSvc) {
    super(appSettings);
    this.purgeJobRuntimeWindowSvc = purgeJobRuntimeWindowSvc;
  }

  @GET
  @Path("/runtimewindow")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PurgeJobResource.getAllPurgeJobRuntimeWindows.GET")
  public Response getAllPurgeJobRuntimeWindows() {
    List<PurgeJobRuntimeWindow> windows = purgeJobRuntimeWindowSvc.getAllPurgeJobRuntimeWindows();
    return new ApiResponseBuilder(false)
        .ok()
        .content(new PurgeJobRuntimeWindowView(windows))
        .build();
  }

  @POST
  @Path("/runtimewindow")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PurgeJobResource.createPurgeJobRuntimeWindow.POST")
  public Response createPurgeJobRuntimeWindow(
      @Context AppUser appUser, @RequestBody PurgeJobRuntimeWindow body) {
    Optional<PurgeJobRuntimeWindowSaveError> optionalError =
        purgeJobRuntimeWindowSvc.createPurgeJobRuntimeWindow(
            new PurgeJobRuntimeWindow.Builder()
                .setCreatedBy(appUser.getUsername())
                .setCreatedDate(new Date())
                .setWindowStartHourUtc(body.getWindowStartHourUtc())
                .setWindowStartMinuteUtc(body.getWindowStartMinuteUtc())
                .setWindowEndHourUtc(body.getWindowEndHourUtc())
                .setWindowEndMinuteUtc(body.getWindowEndMinuteUtc())
                .build());
    if (optionalError.isPresent()
        && optionalError.get() != PurgeJobRuntimeWindowSaveError.UNKNOWN) {
      return new ApiResponseBuilder(false).badRequest(optionalError.get().getMessage()).build();
    } else if (optionalError.isPresent()) {
      return new ApiResponseBuilder(false)
          .internalServerError(optionalError.get().getMessage())
          .build();
    }
    return new ApiResponseBuilder(false).ok().build();
  }

  @DELETE
  @Path("/runtimewindow/{id}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PurgeJobResource.deletePurgeJobRuntimeWindow.DELETE")
  public Response deletePurgeJobRuntimeWindow(
      @Context AppUser appUser, @PathParam("id") ObjectId id) {
    boolean deletionOk = purgeJobRuntimeWindowSvc.deletePurgeJobRuntimeWindow(appUser, id);
    if (!deletionOk) {
      return new ApiResponseBuilder(false)
          .internalServerError("Error occurred attempting to delete window.")
          .build();
    }
    return new ApiResponseBuilder(false).ok().build();
  }
}
