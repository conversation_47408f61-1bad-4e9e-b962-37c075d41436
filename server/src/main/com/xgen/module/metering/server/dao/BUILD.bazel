load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "dao",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/module/metering/server/res:__subpackages__",
        "//server/src/main/com/xgen/module/metering/server/svc:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/model/billing:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/svc/billing/regressiontests:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/util/billing/scenarios:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/util/billing/testFactories:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/common",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/dao/base",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/db/mongo",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/module/metering/common/exception",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/module/metering/common/utils",
        "//server/src/main/com/xgen/module/metering/common/view",
        "//server/src/main/com/xgen/module/metering/server/model",
        "//server/src/main/com/xgen/svc/core/codec",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//third_party:guava",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_core",
        "@maven//:org_mongodb_mongodb_driver_sync",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
