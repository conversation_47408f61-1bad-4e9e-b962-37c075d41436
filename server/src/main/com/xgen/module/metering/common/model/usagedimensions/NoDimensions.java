package com.xgen.module.metering.common.model.usagedimensions;

import org.bson.codecs.pojo.annotations.BsonDiscriminator;

@BsonDiscriminator(value = NoDimensions.CLASS_NAME)
public class NoDimensions extends UsageDimensions {
  public static final String CLASS_NAME = "NoDimensions";

  public NoDimensions() {}

  @Override
  public void accept(final UsageDimensionsVisitor visitor) {
    visitor.visit(this);
  }

  @Override
  public String toString() {
    return "NoDimensions{} " + super.toString();
  }
}
