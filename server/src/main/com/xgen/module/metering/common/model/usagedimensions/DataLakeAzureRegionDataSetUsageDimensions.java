package com.xgen.module.metering.common.model.usagedimensions;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.module.metering.common.model.getter.AzureRegionNameGetter;
import com.xgen.module.metering.common.model.jackson.AzureRegionNameDeserializer;
import com.xgen.module.metering.common.model.jackson.RegionNameSerializer;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonProperty;

@BsonDiscriminator(value = DataLakeAzureRegionDataSetUsageDimensions.CLASS_NAME)
public class DataLakeAzureRegionDataSetUsageDimensions extends UsageDimensions
    implements AzureRegionNameGetter {

  public static final String CLASS_NAME = "DataLakeAzureRegionDataSetUsageDimensions";

  private static final String DATA_REGION_FIELD = "dataRegion";
  private static final String DATA_SET_FIELD = "dataSetBillingName";

  @NotNull
  @BsonProperty(DATA_REGION_FIELD)
  @JsonProperty(DATA_REGION_FIELD)
  @JsonDeserialize(using = AzureRegionNameDeserializer.class)
  @JsonSerialize(using = RegionNameSerializer.class)
  private AzureRegionName dataRegion;

  @NotNull
  @BsonProperty(DATA_SET_FIELD)
  @JsonProperty(DATA_SET_FIELD)
  private String dataSetBillingName;

  public AzureRegionName getAzureRegionName() {
    return dataRegion;
  }

  public AzureRegionName getDataRegion() {
    return dataRegion;
  }

  public String getDataSetBillingName() {
    return dataSetBillingName;
  }

  public DataLakeAzureRegionDataSetUsageDimensions() {}

  private DataLakeAzureRegionDataSetUsageDimensions(final Builder builder) {
    dataRegion = builder.dataRegion;
    dataSetBillingName = builder.dataSetBillingName;
  }

  @Override
  public void accept(final UsageDimensionsVisitor visitor) {
    visitor.visit(this);
  }

  public static final class Builder {

    private AzureRegionName dataRegion;
    private String dataSetBillingName;

    public Builder dataRegion(final AzureRegionName dataRegion) {
      this.dataRegion = dataRegion;
      return this;
    }

    public Builder dataSetBillingName(final String dataSetBillingName) {
      this.dataSetBillingName = dataSetBillingName;
      return this;
    }

    public DataLakeAzureRegionDataSetUsageDimensions build() {
      return new DataLakeAzureRegionDataSetUsageDimensions(this);
    }
  }

  @Override
  public String toString() {
    return "DataLakeAzureRegionDataSetUsageDimensions{"
        + "dataRegion='"
        + dataRegion
        + '\''
        + ", dataSetBillingName='"
        + dataSetBillingName
        + '\''
        + '}';
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    if (!super.equals(o)) {
      return false;
    }
    final DataLakeAzureRegionDataSetUsageDimensions that =
        (DataLakeAzureRegionDataSetUsageDimensions) o;
    return (dataRegion == that.dataRegion && dataSetBillingName.equals(that.dataSetBillingName));
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), dataRegion, dataSetBillingName);
  }
}
