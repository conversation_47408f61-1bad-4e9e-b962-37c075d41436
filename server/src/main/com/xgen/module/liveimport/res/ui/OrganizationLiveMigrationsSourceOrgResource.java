package com.xgen.module.liveimport.res.ui;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.liveimport.dao.TargetOrgDao;
import com.xgen.module.liveimport.model.TargetOrg;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;
import org.apache.http.HttpStatus;

@Path("/orgs/{orgId}/liveMigrations/sourceOrg")
@Singleton
public class OrganizationLiveMigrationsSourceOrgResource extends BaseResource {

  private final TargetOrgDao targetOrgDao;
  private final AppSettings appSettings;

  @Inject
  public OrganizationLiveMigrationsSourceOrgResource(
      final TargetOrgDao targetOrgDao, final AppSettings appSettings) {
    this.targetOrgDao = targetOrgDao;
    this.appSettings = appSettings;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, plan = PlanTypeSet.NDS, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveMigrationsSourceOrgResource.getSourceOrg.GET")
  public Response getSourceOrg(@Context final Organization pOrg) {
    if (!appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final Optional<TargetOrg> targetOrgMaybe = targetOrgDao.findByOrgId(pOrg.getId());
    if (targetOrgMaybe.isEmpty()) {
      return SimpleApiResponse.notFound().build();
    }
    final TargetOrg targetOrg = targetOrgMaybe.get();
    if (targetOrg.getSourceOrg() == null) {
      return SimpleApiResponse.notFound().build();
    }
    return Response.status(HttpStatus.SC_OK).entity(targetOrg.getSourceOrg()).build();
  }
}
