package com.xgen.module.liveimport.res.ui;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.liveimport.model.TargetOrg;
import com.xgen.module.liveimport.svc.TargetOrgSvc;
import com.xgen.module.liveimport.view.TargetOrgRequestView;
import com.xgen.module.liveimport.view.TargetOrgView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.Optional;

@Path("/orgs/{orgId}/liveMigrations/linkTokens")
@Singleton
public class OrganizationLiveMigrationsLinkTokensResource extends BaseResource {

  private final TargetOrgSvc _targetOrgSvc;
  private final AppSettings _appSettings;

  @Inject
  public OrganizationLiveMigrationsLinkTokensResource(
      final TargetOrgSvc pTargetOrgSvc, final AppSettings pAppSettings) {
    _targetOrgSvc = pTargetOrgSvc;
    _appSettings = pAppSettings;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/status")
  @UiCall(roles = RoleSet.ORG_OWNER, plan = PlanTypeSet.NDS, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveMigrationsLinkTokensResource.getLinkStatus.GET")
  public Response getLinkStatus(@Context final Organization pOrg) {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    try {
      return Response.ok(_targetOrgSvc.getOrgLinkStatus(pOrg)).build();
    } catch (final SvcException e) {
      if (e.getErrorCode() == CommonErrorCode.NOT_FOUND) {
        return createNotFoundResponse();
      } else {
        return Response.status(Status.BAD_REQUEST).entity(e.getMessage()).build();
      }
    }
  }

  @POST
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, plan = PlanTypeSet.NDS, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveMigrationsLinkTokensResource.createMigrationLink.POST")
  public Response createMigrationLink(
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      final TargetOrgRequestView pRequest)
      throws SvcException {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final TargetOrgView result =
        _targetOrgSvc.createLinkTokenFromApi(pRequest, pOrganization, pUser, pAuditInfo);
    return Response.accepted(result).build();
  }

  @DELETE
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, plan = PlanTypeSet.NDS, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveMigrationsLinkTokensResource.deleteMigrationLink.DELETE")
  public Response deleteMigrationLink(
      @Context final Organization pOrganization, @Context final AuditInfo pAuditInfo)
      throws SvcException {
    if (!_appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final Optional<TargetOrg> targetOrg = _targetOrgSvc.getOrgLink(pOrganization);
    if (targetOrg.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    _targetOrgSvc.deleteOrgLink(pOrganization, pAuditInfo);
    return Response.noContent().build();
  }
}
