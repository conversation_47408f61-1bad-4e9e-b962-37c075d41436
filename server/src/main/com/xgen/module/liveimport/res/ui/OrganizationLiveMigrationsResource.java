package com.xgen.module.liveimport.res.ui;

import static jakarta.ws.rs.core.MediaType.APPLICATION_JSON;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.liveimport.model.LiveImport.State;
import com.xgen.module.liveimport.svc.LiveMigrationSvc;
import com.xgen.module.liveimport.view.LiveMigrationResponseView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;

@Singleton
@Path("/orgs/{orgId}/liveMigrations")
public class OrganizationLiveMigrationsResource extends BaseResource {

  private final LiveMigrationSvc liveMigrationSvc;
  private final AppSettings appSettings;

  @Inject
  public OrganizationLiveMigrationsResource(
      final LiveMigrationSvc liveMigrationSvc, final AppSettings appSettings) {
    this.liveMigrationSvc = liveMigrationSvc;
    this.appSettings = appSettings;
  }

  @GET
  @Produces({APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, plan = PlanTypeSet.NDS, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationLiveMigrationsResource.getActivePushMigrations.GET")
  public Response getActivePushMigrations(
      @Context final Organization org, @QueryParam("status") final List<State> status) {
    if (!appSettings.isPushLiveMigrationsEnabled()) {
      return Response.status(HttpServletResponse.SC_FORBIDDEN)
          .type(MediaType.TEXT_PLAIN_TYPE)
          .entity("Feature not supported")
          .build();
    }

    final List<LiveMigrationResponseView> result =
        liveMigrationSvc.getLiveMigrations(org.getId(), status);
    return Response.ok(result).build();
  }
}
