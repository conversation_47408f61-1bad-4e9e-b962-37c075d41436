package com.xgen.module.liveimport.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.core._public.model.AgentAudit;
import jakarta.annotation.Nullable;
import java.beans.ConstructorProperties;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonProperty;

public class MigrationHost {

  @JsonProperty(FieldDefs.HOSTNAME)
  private final String hostname;

  @JsonProperty(FieldDefs.AGENT_VERSION)
  @Nullable
  private final String agentVersion;

  @ConstructorProperties({FieldDefs.HOSTNAME, FieldDefs.AGENT_VERSION})
  @JsonCreator
  @BsonCreator
  public MigrationHost(
      @BsonProperty(FieldDefs.HOSTNAME) final String pHostname,
      @Nullable @BsonProperty(FieldDefs.AGENT_VERSION) final String pAgentVersion) {
    hostname = pHostname;
    agentVersion = pAgentVersion;
  }

  public MigrationHost(final String pHostname) {
    hostname = pHostname;
    agentVersion = null;
  }

  public static MigrationHost fromDBObject(final BasicDBObject agent) {
    return new MigrationHost(
        agent.getString(AgentAudit.AGENT_HOSTNAME_FIELD),
        agent.getString(AgentAudit.AGENT_VERSION_FIELD));
  }

  public BasicDBObject toDBObject() {
    if (StringUtils.isNotBlank(agentVersion)) {
      assert agentVersion != null;
      return new BasicDBObject(FieldDefs.HOSTNAME, hostname)
          .append(FieldDefs.AGENT_VERSION, agentVersion);
    }

    return new BasicDBObject(FieldDefs.HOSTNAME, hostname).append(FieldDefs.AGENT_VERSION, "");
  }

  public static class FieldDefs {
    public static final String HOSTNAME = "hostname";
    public static final String AGENT_VERSION = "agentVersion";
  }

  public String getHostname() {
    return hostname;
  }

  @Nullable
  public String getAgentVersion() {
    return agentVersion;
  }

  @Override
  public String toString() {
    return "MigrationHost{"
        + "hostname='"
        + hostname
        + '\''
        + ", agentVersion='"
        + agentVersion
        + '\''
        + '}';
  }

  @Override
  public boolean equals(final Object pO) {
    if (this == pO) {
      return true;
    }
    if (pO == null || getClass() != pO.getClass()) {
      return false;
    }
    final MigrationHost that = (MigrationHost) pO;
    return Objects.equals(hostname, that.hostname)
        && Objects.equals(agentVersion, that.agentVersion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(this.getHostname(), this.getAgentVersion());
  }
}
