package com.xgen.module.liveimport.dao;

import static com.xgen.module.liveimport.model.LiveImportLocation.FieldDefs;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.module.liveimport.model.LiveImportLocation;
import com.xgen.module.liveimport.model.LiveImportLocation.OS;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.core.dao.base.MongoIndex;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

@Singleton
public class LiveImportLocationDao extends BaseDao {
  @Inject
  public LiveImportLocationDao(final MongoSvc pMongoSvc) {
    super(pMongoSvc, "nds", "config.nds.liveImportLocations");
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    list.add(new MongoIndex(this).key(FieldDefs.REGION).key(FieldDefs.OS).unique());

    return list;
  }

  public Optional<LiveImportLocation> find(final ObjectId pId) {
    final BasicDBObject query = new BasicDBObject(FieldDefs.ID, pId);
    final BasicDBObject document = (BasicDBObject) getDbCollection().findOne(query);
    if (document == null) {
      return Optional.empty();
    }

    return Optional.of(new LiveImportLocation(document));
  }

  @Deprecated
  public List<LiveImportLocation> findAllLocations() {
    try (final DBCursor cursor = getDbCollection().find()) {
      return cursor.toArray().stream()
          .map(dbObject -> new LiveImportLocation((BasicDBObject) dbObject))
          .collect(Collectors.toList());
    }
  }

  public List<LiveImportLocation> findAllLocationsForOsList(
      final List<LiveImportLocation.OS> pOsList) {
    if (pOsList.isEmpty()) {
      return List.of();
    }

    final BasicDBObject query =
        new BasicDBObject(
            FieldDefs.OS,
            new BasicDBObject(
                IN, pOsList.stream().map(OS::getName).collect(DbUtils.toBasicDBList())));
    try (final DBCursor cursor = getDbCollection().find(query)) {
      return cursor.toArray().stream()
          .map(dbObject -> new LiveImportLocation((BasicDBObject) dbObject))
          .collect(Collectors.toList());
    }
  }

  public List<LiveImportLocation> findAllHelixLocationsEnabledForLiveMigrate() {
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.OS, OS.HELIX.getName())
            .append(FieldDefs.ENABLED, true);
    try (final DBCursor cursor = getDbCollection().find(query)) {
      return cursor.toArray().stream()
          .map(dbObject -> new LiveImportLocation((BasicDBObject) dbObject))
          .collect(Collectors.toList());
    }
  }
}
