package com.xgen.module.usagedata.svc;

import static com.xgen.module.usagedata.model.UsageDataProp.MMS_USAGE_DATA_INGESTION_ENABLED;
import static com.xgen.module.usagedata.model.UsageDataProp.MMS_USAGE_DATA_PGP_PUBLIC_KEY;
import static com.xgen.module.usagedata.model.UsageDataProp.MMS_USAGE_DATA_PGP_SECRET_KEY;
import static com.xgen.module.usagedata.model.UsageDataProp.MMS_USAGE_DATA_PGP_SECRET_PASSPHRASE;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.module.usagedata.dao.UsageDataPayloadConsumer;
import com.xgen.module.usagedata.dao.UsageDataPayloadConsumer.UsageDataPayloadException;
import com.xgen.module.usagedata.dao.UsageDataPayloadS3Store;
import com.xgen.module.usagedata.model.UsageDataPayload;
import com.xgen.module.usagedata.model.UsageDataProp;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Service class used by the Cloud ingestion endpoint to store {@link UsageDataPayload}s. */
@Singleton
public class UsageDataIngestionSvc {

  private static final Logger LOG = LoggerFactory.getLogger(UsageDataIngestionSvc.class);
  private final Set<UsageDataPayloadConsumer> _usageDataPayloadConsumers;
  private final AppSettings _appSettings;

  // initialize an object mapper that will be used for de/serialization operations
  private static final ObjectMapper MAPPER = CustomJacksonJsonProvider.createObjectMapper();

  @Inject
  public UsageDataIngestionSvc(
      final Set<UsageDataPayloadConsumer> pUsageDataPayloadConsumers,
      final AppSettings pAppSettings) {
    this._usageDataPayloadConsumers = pUsageDataPayloadConsumers;
    this._appSettings = pAppSettings;
  }

  /* ------------------------------------------------------------------------- */
  /*                              Ingestion logic                              */
  /* ------------------------------------------------------------------------- */

  /**
   * Accepts a {@link UsageDataPayload} object, decrypts it, and stores it into S3, via {@link
   * UsageDataPayloadS3Store}
   *
   * @throws PGPEncryptionSvc.EncryptionSvcException if the data cannot be decrypted using the
   *     registered keys
   * @throws UsageDataPayloadConsumer.UsageDataPayloadException if the data fails to be accepted by
   *     all consumers
   * @throws SecurityException if usage data ingestion is not allowed in the running environment
   */
  public void storeUsageDataPayload(final UsageDataPayload pUsageDataPayload, final String remoteIp)
      throws PGPEncryptionSvc.EncryptionSvcException {
    if (!isIngestionEnabled()) {
      throw new SecurityException("Method not allowed in current environment");
    }

    // decrypt the data
    final String decryptedUsageData =
        provideEncryptionService().decrypt(pUsageDataPayload.payloadData);

    // add information about that data's origin, and re-wrap into a payload object, ready for
    // consumption
    final UsageDataPayload augmentedDecryptedPayload =
        augmentDataWithOriginInformation(pUsageDataPayload, remoteIp, decryptedUsageData);

    final List<UsageDataPayloadException> exceptions =
        storeData(remoteIp, augmentedDecryptedPayload);

    // if any exceptions were encountered, rethrow for further handling
    if (!exceptions.isEmpty()) {
      throw new UsageDataPayloadConsumer.UsageDataPayloadException(
          "Processing Usage Data failed, see suppressed exceptions", exceptions);
    }
  }

  public List<UsageDataPayloadException> storeData(
      String remoteIp, UsageDataPayload augmentedDecryptedPayload) {
    // and store it via the registered consumers
    final List<UsageDataPayloadException> exceptions =
        _usageDataPayloadConsumers.stream()
            .map(
                payloadConsumer -> {
                  try {
                    payloadConsumer.accept(augmentedDecryptedPayload);
                    LOG.info(
                        "Successfully processed payload {}, processor={}, remoteIP={}, op=PROCESS,"
                            + " success=1, feature=UD",
                        augmentedDecryptedPayload.toString(),
                        payloadConsumer.getClass().getSimpleName(),
                        remoteIp);
                    return null;

                  } catch (final UsageDataPayloadException e) {
                    // ensure any exceptions appear in the logs
                    LOG.warn(
                        "Unexpected exception while processing payload {}, processor={},"
                            + " remoteIP={}, op=PROCESS, error=1, err={}, feature=UD",
                        augmentedDecryptedPayload.toString(),
                        payloadConsumer.getClass().getSimpleName(),
                        remoteIp,
                        e.getMessage(),
                        e);
                    return e;
                  }
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    return exceptions;
  }

  /**
   * Augments the serialized input JSON data with information about the data's origin (time, IP,
   * etc.)
   */
  private UsageDataPayload augmentDataWithOriginInformation(
      final UsageDataPayload pPayload, final String remoteIp, final String pDecryptedUsageData) {
    try {
      // read data as a (key, value) map
      final Map<String, Object> data =
          MAPPER.readValue(pDecryptedUsageData, new TypeReference<>() {});

      // augment it
      data.put(UsageDataInfoField.omVersion.name(), pPayload.omVersion);
      data.put(UsageDataInfoField.protocolVersion.name(), pPayload.protocolVersion);
      data.put(UsageDataInfoField.receivedAt.name(), computeCurrentDate());
      data.put(UsageDataInfoField.remoteIp.name(), remoteIp);

      // and return a new payload, constructed from the augmented dataset
      return pPayload.cloneWithData(MAPPER.writeValueAsString(data));

    } catch (final IOException e) {
      LOG.error(
          "Unexpected exception while augmenting payload {}, remoteIP={}, op=AUGMENT, error=1,"
              + " err={}, feature=UD",
          pPayload.toString(),
          remoteIp,
          e.getMessage(),
          e);

      // stop if any de/serializations exceptions are encountered; most likely to occur if someone
      // spoofs the endpoint with dummy data (non-JSON)
      throw new UsageDataPayloadConsumer.UsageDataPayloadException(e);
    }
  }

  /** Delegates to {@link AppSettings} to determine if Usage Data ingestion is allowed */
  public boolean isIngestionEnabled() {
    return _appSettings.getBoolProp(MMS_USAGE_DATA_INGESTION_ENABLED.name, false);
  }

  /** Wrapper that simplifies the loading of optional string {@link AppSettings} */
  private Optional<String> loadOptionalStringProp(final UsageDataProp pPropKey) {
    return Optional.ofNullable(_appSettings.getStrProp(pPropKey.name, null))
        // only return a value if the endpoint is defined
        .map(Strings::emptyToNull);
  }

  private PGPEncryptionSvc provideEncryptionService() {
    final Optional<String> publicKey = retrievePGPPublicKey();
    final Optional<String> secretKey = retrievePGPSecretKey();
    final Optional<char[]> passPhrase = retrievePGPSecretPassPhrase();
    return new PGPEncryptionSvc(
        publicKey.orElse(null), secretKey.orElse(null), passPhrase.orElse(null));
  }

  /**
   * Delegates to {@link AppSettings} to retrieve the PGP public key used for encrypting Usage Data
   * payloads
   */
  private Optional<String> retrievePGPPublicKey() {
    return loadOptionalStringProp(MMS_USAGE_DATA_PGP_PUBLIC_KEY);
  }

  /**
   * Delegates to {@link AppSettings} to retrieve the PGP secret key used for decrypting Usage Data
   * payloads
   */
  private Optional<String> retrievePGPSecretKey() {
    return loadOptionalStringProp(MMS_USAGE_DATA_PGP_SECRET_KEY);
  }

  /**
   * Delegates to {@link AppSettings} to retrieve the passphrase which can decrypt the secret key
   * loaded from {@link #retrievePGPSecretKey()}
   */
  private Optional<char[]> retrievePGPSecretPassPhrase() {
    return loadOptionalStringProp(MMS_USAGE_DATA_PGP_SECRET_PASSPHRASE).map(String::toCharArray);
  }

  /**
   * Convert Java8 to old-Java date types; unfortunately, LocalDateTime is not fully supported in
   * our implementation of mongo-java-driver
   */
  private static Date convertLocalDateTimeToDate(final LocalDateTime value) {
    final Instant instant = value.toInstant(ZoneOffset.UTC);
    return Date.from(instant);
  }

  /**
   * Date/time supplier
   *
   * @return {@link Date} based on the definition of time provided by {@link #computeCurrentTime()}
   */
  private static Date computeCurrentDate() {
    return convertLocalDateTimeToDate(computeCurrentTime());
  }

  /** Date/time supplier; standardizes the definition of 'now' in this context */
  private static LocalDateTime computeCurrentTime() {
    return LocalDateTime.now(ZoneOffset.UTC);
  }

  /** Holder for custom fields which are appended to any ingested payloads */
  public enum UsageDataInfoField {
    createdAt,
    omVersion,
    protocolVersion,
    receivedAt,
    remoteIp,
    uuid
  }
}
