package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

@Path("/recovery")
@Singleton
public class PasswordRecoveryResource extends AutomationBaseResource {

  private final AutomationConfigPublishingSvc _automationConfigSvc;

  @Inject
  public PasswordRecoveryResource(final AutomationConfigPublishingSvc pAutomationConfigSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
  }

  @POST // handle as post to avoid browser caching
  @Path("/automationAgent/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED) @Auth(endpointAction = "epa.project.PasswordRecoveryResource.getAgentPassword.POST")
  public Response getAgentPassword(@Context final AppUser pUser, @Context final Group pGroup)
      throws SvcException {
    final Auth auth =
        _automationConfigSvc
            .findCurrentOrEmpty(pGroup.getId(), pUser.getId())
            .getDeployment()
            .getAuth();

    final Map<String, String> result = new HashMap<>(1);
    result.put("agentPassword", auth.getAutoPwd());
    return Response.ok(result).build();
  }
}
