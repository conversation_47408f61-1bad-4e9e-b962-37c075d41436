package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.svc.SecurityChecklistSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.SecurityChecklistItem;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/automation/securityChecklist/{groupId}")
@Singleton
public class SecurityChecklistResource extends AutomationBaseResource {
  private AutomationConfigPublishingSvc _automationConfigSvc;
  private SecurityChecklistSvc _securityChecklistSvc;

  @Inject
  public SecurityChecklistResource(
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final SecurityChecklistSvc pSecurityChecklistSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
    _securityChecklistSvc = pSecurityChecklistSvc;
  }

  @GET
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.SecurityChecklistResource.getSecurityChecklist.GET")
  public Response getSecurityChecklist(@Context final Group pGroup, @Context final AppUser pUser) {
    if (!_securityChecklistSvc.canUserAndGroupSeeChecklist(pUser, pGroup)) {
      return Response.status(Response.Status.UNAUTHORIZED).build();
    }
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    return Response.ok(_securityChecklistSvc.getChecklist(config)).build();
  }

  @PUT
  @Path("/dismiss/{checklistItemName}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.SecurityChecklistResource.dismissSecurityChecklistItem.PUT")
  public Response dismissSecurityChecklistItem(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("checklistItemName") final String pChecklistItemName)
      throws SvcException {
    if (!_securityChecklistSvc.canUserAndGroupSeeChecklist(pUser, pGroup)) {
      return Response.status(Response.Status.UNAUTHORIZED).build();
    }
    final SecurityChecklistItem item;
    try {
      item = SecurityChecklistItem.fromString(pChecklistItemName);
    } catch (Exception e) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, e.getMessage());
    }
    _securityChecklistSvc.dismissChecklistItemForGroup(pGroup.getId(), item);
    return Response.ok().build();
  }
}
