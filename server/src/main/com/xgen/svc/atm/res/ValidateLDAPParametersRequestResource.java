package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.view.LDAPVerifyConnectivityJobRequestView;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.LDAP;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.ui.auth.ValidateLDAPParametersResponseView;
import com.xgen.svc.atm.svc.ValidateLDAPParametersRequestSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/** Resource to validate LDAP parameters */
@Singleton
@Path("/automation/validateLDAPParameters")
public class ValidateLDAPParametersRequestResource extends AutomationBaseResource {

  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final ValidateLDAPParametersRequestSvc _validateLDAPParametersRequestSvc;

  @Inject
  public ValidateLDAPParametersRequestResource(
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final ValidateLDAPParametersRequestSvc pValidateLDAPParametersRequestSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
    _validateLDAPParametersRequestSvc = pValidateLDAPParametersRequestSvc;
  }

  @POST
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.ValidateLDAPParametersRequestResource.submitValidateLdapParametersRequest.POST")
  public Response submitValidateLdapParametersRequest(
      @Context final Organization pOrg,
      @Context Group pGroup,
      @Context AppUser pUser,
      final LDAP pLDAPParameters)
      throws SvcException {

    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());

    final LDAPVerifyConnectivityJobRequestView request =
        _validateLDAPParametersRequestSvc.createLDAPVerifyConnectivityJob(
            pGroup, config, pLDAPParameters);

    return Response.ok().entity(request).build();
  }

  @GET
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.ValidateLDAPParametersRequestResource.getValidateLdapParametersStatus.GET")
  public Response getValidateLdapParametersStatus(
      @Context final Organization pOrg, @Context final Group pGroup) throws SvcException {

    // STATUS_FIELD will be set based on the result of the validation
    final ValidateLDAPParametersResponseView ldapRequestResponse =
        _validateLDAPParametersRequestSvc.getLDAPVerifyConnectivityJobRequest(pGroup);

    return Response.ok().entity(ldapRequestResponse).build();
  }
}
