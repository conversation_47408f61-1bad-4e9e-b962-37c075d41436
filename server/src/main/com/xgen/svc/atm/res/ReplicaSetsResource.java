package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.svc.BiConnectorSvc;
import com.xgen.cloud.atm.core._public.svc.DeploymentBuilderSvc;
import com.xgen.cloud.atm.core._public.svc.ReplicaSetsSvc;
import com.xgen.cloud.atm.core._public.view.ConnectionInfoView;
import com.xgen.cloud.atm.core._public.view.ConvertProcessToRSRequestView;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.view.ReplicaSetView;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.metrics._public.svc.collstatslatency.CollStatsLatencyConfigSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.svc.MonitoredReplicaSetsSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/** CRUD resource for replica sets within an automation config */
@Singleton
@Path("/automation/replicaSets")
public class ReplicaSetsResource extends AutomationBaseResource {

  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final ReplicaSetsSvc _replicaSetsSvc;
  private final DeploymentBuilderSvc _deploymentBuilderSvc;
  private final MonitoredReplicaSetsSvc _monitoredReplicaSetsSvc;
  private final BiConnectorSvc _biConnectorSvc;
  private final CollStatsLatencyConfigSvc _collStatsLatencyConfigSvc;

  private final FeatureFlagSvc _featureFlagSvc;

  @Inject
  public ReplicaSetsResource(
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final ReplicaSetsSvc pReplicaSetsSvc,
      final DeploymentBuilderSvc pDeploymentBuilderSvc,
      final MonitoredReplicaSetsSvc pMonitoredReplicaSetsSvc,
      final BiConnectorSvc pBiConnectorSvc,
      final CollStatsLatencyConfigSvc pCollStatsLatencyConfigSvc,
      final FeatureFlagSvc pFeatureFlagSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
    _replicaSetsSvc = pReplicaSetsSvc;
    _deploymentBuilderSvc = pDeploymentBuilderSvc;
    _monitoredReplicaSetsSvc = pMonitoredReplicaSetsSvc;
    _biConnectorSvc = pBiConnectorSvc;
    _collStatsLatencyConfigSvc = pCollStatsLatencyConfigSvc;
    _featureFlagSvc = pFeatureFlagSvc;
  }

  @GET
  @Path("/{groupId}/{rsId}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.getReplicaSet.GET")
  public Response getReplicaSet(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ReplicaSetView replicaSet = _replicaSetsSvc.getReplicaSet(pGroup, pUser, config, pRsId);
    return Response.ok(replicaSet).build();
  }

  @GET
  @Path("/{groupId}/{rsId}/processReplicaStates")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.getReplicaSetProcessReplicaStates.GET")
  public Response getReplicaSetProcessReplicaStates(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _monitoredReplicaSetsSvc.fetchAndAddProcessMonitoringStates(config, pRsId, true);
    return Response.ok(_monitoredReplicaSetsSvc.getProcessReplicaStates(config, pRsId)).build();
  }

  @POST
  @Path("/{groupId}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.addReplicaSet.POST")
  public Response addReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      final ReplicaSetView pReplicaSet)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ReplicaSetView replicaSet = _replicaSetsSvc.addReplicaSet(config, pReplicaSet);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok(replicaSet).build();
  }

  @PUT
  @Path("/{groupId}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.updateReplicaSet.PUT")
  public Response updateReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pReplicaSetId,
      final ReplicaSetView pReplicaSet)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ReplicaSetView replicaSet = _replicaSetsSvc.updateReplicaSet(config, pReplicaSet);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok(replicaSet).build();
  }

  @DELETE
  @Path("/{groupId}/{rsId}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.deleteReplicaSet.DELETE")
  public Response deleteReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.deleteReplicaSet(config, pRsId);
    _biConnectorSvc.deleteBiConnectorForDeploymentItemName(config.getDeployment(), pRsId);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @DELETE
  @Path("/{groupId}/{rsId}/withMonitoringState")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.deleteReplicaSetAndMonitoringState.DELETE")
  public Response deleteReplicaSetAndMonitoringState(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final AuditInfo auditInfo,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());

    // Create unified deployment view by merging monitoring data with automation configuration
    // Also, this makes a copy so that when we delete the replica set from the config, we still have
    // this available for monitoredReplicaSetsSvc to use to look up the replica set.
    final Deployment deployment =
        _deploymentBuilderSvc.buildDeployment(pGroup, config.getDeployment());

    if (_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.NAMESPACE_QUERY_LATENCY_METRICS, pOrg, pGroup)) {
      _collStatsLatencyConfigSvc.deleteNamespacesForReplicaSet(pGroup, pRsId, deployment);
    }

    _monitoredReplicaSetsSvc.failIfCannotDeleteReplicaSetMonitoringState(pGroup, deployment, pRsId);

    _replicaSetsSvc.deleteReplicaSet(config, pRsId);
    _biConnectorSvc.deleteBiConnectorForDeploymentItemName(config.getDeployment(), pRsId);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);

    _monitoredReplicaSetsSvc.deleteReplicaSetMonitoringState(pGroup, deployment, pRsId, auditInfo);

    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{rsId}/startup")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.startupReplicaSet.POST")
  public Response startupReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.setReplicaSetDisabled(config, pRsId, false);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{rsId}/shutdown")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.shutdownReplicaSet.POST")
  public Response shutdownReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.setReplicaSetDisabled(config, pRsId, true);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{rsId}/suspend")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.suspendReplicaSet.POST")
  public Response suspendReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.setReplicaSetManualMode(config, pRsId, true);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{rsId}/resume")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.resumeReplicaSet.POST")
  public Response resumeReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.setReplicaSetManualMode(config, pRsId, false);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{rsId}/restart")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.restartReplicaSet.POST")
  public Response restartReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.restartReplicaSet(config, pRsId);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{rsId}/rotateKmipKey")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.updateConfigForKmipKeyRotation.POST")
  public Response updateConfigForKmipKeyRotation(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.updateConfigForKmipKeyRotation(config, pRsId);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @GET
  @Path("/{groupId}/{rsId}/connectionInfo")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.getReplicaSetConnectionInfo.GET")
  public Response getReplicaSetConnectionInfo(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ConnectionInfoView connectionInfoView =
        _monitoredReplicaSetsSvc.getConnectionInfoView(config, pGroup, pRsId);
    return Response.ok(connectionInfoView).build();
  }

  @POST
  @Path("/{groupId}/convertProcess")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ReplicaSetsResource.convertProcessToReplicaSet.POST")
  public Response convertProcessToReplicaSet(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      final ConvertProcessToRSRequestView pConvertProcessRequest)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _replicaSetsSvc.convertProcessToReplicaSet(config, pConvertProcessRequest);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }
}
