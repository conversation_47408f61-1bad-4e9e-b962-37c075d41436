package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.svc.BiConnectorSvc;
import com.xgen.cloud.atm.core._public.svc.DeploymentBuilderSvc;
import com.xgen.cloud.atm.core._public.svc.ProcessesSvc;
import com.xgen.cloud.atm.core._public.view.ConnectionInfoView;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.view.ProcessView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.svc.MonitoredProcessesSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/** CRUD resource for processes within an automation config */
@Singleton
@Path("/automation/processes")
public class ProcessesResource extends AutomationBaseResource {

  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final ProcessesSvc _processesSvc;
  private final MonitoredProcessesSvc _monitoredProcessesSvc;
  private final DeploymentBuilderSvc _deploymentBuilderSvc;
  private final BiConnectorSvc _biConnectorSvc;

  @Inject
  public ProcessesResource(
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final ProcessesSvc pProcessesSvc,
      final MonitoredProcessesSvc pMonitoredProcessesSvc,
      final DeploymentBuilderSvc pDeploymentBuilderSvc,
      final BiConnectorSvc pBiConnectorSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
    _processesSvc = pProcessesSvc;
    _monitoredProcessesSvc = pMonitoredProcessesSvc;
    _deploymentBuilderSvc = pDeploymentBuilderSvc;
    _biConnectorSvc = pBiConnectorSvc;
  }

  @GET
  @Path("/{groupId}/{processName}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.getProcess.GET")
  public Response getProcess(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ProcessView process = _processesSvc.getProcess(pGroup, pUser, config, pProcessName);
    return Response.ok(process).build();
  }

  @POST
  @Path("/{groupId}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.addStandaloneProcess.POST")
  public Response addStandaloneProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      final ProcessView pProcess)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ProcessView process = _processesSvc.addStandalone(config, pProcess);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok(pProcess).build();
  }

  @PUT
  @Path("/{groupId}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.updateProcess.PUT")
  public Response updateProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      final ProcessView pProcess)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ProcessView process = _processesSvc.updateProcess(config, pProcess);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok(pProcess).build();
  }

  @DELETE
  @Path("/{groupId}/{processName}")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.deleteProcess.DELETE")
  public Response deleteProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.deleteProcess(config, pProcessName);
    _biConnectorSvc.deleteBiConnectorForDeploymentItemName(config.getDeployment(), pProcessName);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @DELETE
  @Path("/{groupId}/{processName}/withMonitoringState")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.deleteProcessAndMonitoringState.DELETE")
  public Response deleteProcessAndMonitoringState(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final AuditInfo auditInfo,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());

    // Create unified deployment view by merging monitoring data with automation configuration
    // Also, this makes a copy so that when we delete the process from the config, we still have
    // this available for monitoredProcessSvc to use to look up the process.
    final Deployment deployment =
        _deploymentBuilderSvc.buildDeployment(pGroup, config.getDeployment());

    _monitoredProcessesSvc.failIfCannotDeleteProcessMonitoringState(
        pGroup, deployment, pProcessName);

    _processesSvc.deleteProcess(config, pProcessName);
    _biConnectorSvc.deleteBiConnectorForDeploymentItemName(config.getDeployment(), pProcessName);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);

    _monitoredProcessesSvc.deleteProcessMonitoringState(
        pGroup, deployment, pProcessName, auditInfo);

    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/startup")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.startupProcess.POST")
  public Response startupProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.setProcessDisabled(config, pProcessName, false);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/shutdown")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.shutdownProcess.POST")
  public Response shutdownProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.setProcessDisabled(config, pProcessName, true);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/suspend")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.suspendProcess.POST")
  public Response suspendProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.setProcessManualMode(config, pProcessName, true);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/resume")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.resumeProcess.POST")
  public Response resumeProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.setProcessManualMode(config, pProcessName, false);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/restart")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.restartProcess.POST")
  public Response restartProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.restartProcess(config, pProcessName);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/resync")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.resyncProcess.POST")
  public Response resyncProcess(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.resyncProcess(config, pProcessName);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/{processName}/rotateKmipKey")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.updateConfigForKmipKeyRotation.POST")
  public Response updateConfigForKmipKeyRotation(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.updateConfigForKmipKeyRotation(config, pProcessName);
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }

  @GET
  @Path("/{groupId}/{processName}/connectionInfo")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ProcessesResource.getProcessConnectionInfo.GET")
  public Response getProcessConnectionInfo(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String pProcessName)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    final ConnectionInfoView connectionInfoView =
        _monitoredProcessesSvc.getConnectionInfo(config, pProcessName);
    return Response.ok(connectionInfoView).build();
  }
}
