package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.ui.auth.ValidateTLSCertificateResponseView;
import com.xgen.svc.atm.model.ui.auth.VerifyTLSCertificateJobRequestParamsView;
import com.xgen.svc.atm.model.ui.auth.VerifyTLSCertificateJobRequestView;
import com.xgen.svc.atm.svc.VerifyTLSCertificateSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/** Resource to verify TLS Certificate */
@Singleton
@Path("/automation/verifyTLSCertificate")
public class VerifyTLSCertificateResource extends AutomationBaseResource {

  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final VerifyTLSCertificateSvc _verifyTLSCertificateRequestSvc;

  @Inject
  public VerifyTLSCertificateResource(
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final VerifyTLSCertificateSvc pVerifyTLSCertificateRequestSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
    _verifyTLSCertificateRequestSvc = pVerifyTLSCertificateRequestSvc;
  }

  @POST
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.VerifyTLSCertificateResource.submitVerifyTLSCertificateRequest.POST")
  public Response submitVerifyTLSCertificateRequest(
      @Context final Organization pOrg,
      @Context Group pGroup,
      @Context AppUser pUser,
      final VerifyTLSCertificateJobRequestParamsView pTlSCertificateJobRequestParams)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());

    final VerifyTLSCertificateJobRequestView request =
        _verifyTLSCertificateRequestSvc.createVerifyTLSCertificateJob(
            pGroup, config, pTlSCertificateJobRequestParams);

    return Response.ok().entity(request).build();
  }

  @GET
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.VerifyTLSCertificateResource.getVerifyTLSCertificateStatus.GET")
  public Response getVerifyTLSCertificateStatus(
      @Context final Organization pOrg, @Context final Group pGroup) throws SvcException {

    // STATUS_FIELD will be set based on the result of the validation
    final ValidateTLSCertificateResponseView tlsCertRequestResponse =
        _verifyTLSCertificateRequestSvc.getVerifyTLSCertificateJobRequest(pGroup);

    return Response.ok().entity(tlsCertRequestResponse).build();
  }
}
