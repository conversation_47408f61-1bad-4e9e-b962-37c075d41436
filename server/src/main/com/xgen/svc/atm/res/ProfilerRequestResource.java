package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.ProfilerRequest;
import com.xgen.svc.atm.model.ui.ProfilerRequestView;
import com.xgen.svc.atm.svc.ProfilerRequestSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import java.util.List;
import org.bson.types.ObjectId;

@Path("/automation/profiler/{groupId}")
@Singleton
public class ProfilerRequestResource extends AutomationBaseResource {
  private final ProfilerRequestSvc _profilerRequestSvc;

  @Inject
  public ProfilerRequestResource(final ProfilerRequestSvc pProfilerRequestSvc) {
    _profilerRequestSvc = pProfilerRequestSvc;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_READ_ONLY, plan = PlanTypeSet.ALL)
  @Auth(endpointAction = "epa.global.ProfilerRequestResource.getProfileRequests.GET")
  public Response getProfileRequests(@Context Group pGroup) throws SvcException {
    final List<ProfilerRequestView> requests = _profilerRequestSvc.getProfilerRequests(pGroup);
    return Response.ok().entity(requests).build();
  }

  @POST
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, plan = PlanTypeSet.ALL)
  @Auth(endpointAction = "epa.global.ProfilerRequestResource.createProfilerRequest.POST")
  public Response createProfilerRequest(
      @Context Group pGroup, @Context AppUser pUser, final ProfilerRequestView pRequest)
      throws SvcException {
    final ProfilerRequestView request =
        _profilerRequestSvc.createProfilerRequest(pGroup, pUser, pRequest);
    return Response.ok().entity(request).build();
  }

  @DELETE
  @Path("{id}")
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, plan = PlanTypeSet.ALL)
  @Auth(endpointAction = "epa.global.ProfilerRequestResource.deleteProfilerRequest.DELETE")
  public Response deleteProfilerRequest(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("id") final ObjectId pId)
      throws SvcException {
    _profilerRequestSvc.deleteProfilerRequest(pGroup, pId);
    return Response.noContent().build();
  }

  @GET
  @Path("{id}/download")
  @Produces({"application/gzip"})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, plan = PlanTypeSet.ALL)
  @Auth(endpointAction = "epa.global.ProfilerRequestResource.downloadProfile.GET")
  public Response downloadProfile(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("id") final ObjectId pId)
      throws Exception {
    final ProfilerRequest request = _profilerRequestSvc.findProfilerRequest(pGroup, pId);
    final String downloadFilename = request.getDownloadFilename();

    final StreamingOutput streamingOutput = _profilerRequestSvc.createStreamingForDownload(request);

    return Response.ok()
        .header(
            HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + downloadFilename + "\"")
        .entity(streamingOutput)
        .build();
  }
}
