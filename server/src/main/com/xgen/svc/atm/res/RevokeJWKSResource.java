package com.xgen.svc.atm.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.ui.auth.RevokeJWKSResponseView;
import com.xgen.svc.atm.svc.RevokeJWKSSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/** Resource to revoke/refresh OIDC JWKS tokens */
@Singleton
@Path("/automation/revokeJWKS")
public class RevokeJWKSResource extends AutomationBaseResource {
  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final RevokeJWKSSvc _revokeJWKSSvc;

  @Inject
  public RevokeJWKSResource(
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final RevokeJWKSSvc pRevokeJWKSSvc) {
    _automationConfigSvc = pAutomationConfigSvc;
    _revokeJWKSSvc = pRevokeJWKSSvc;
  }

  @POST
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.RevokeJWKSResource.submitRevokeJWKSRequest.POST")
  public Response submitRevokeJWKSRequest(
      @Context final Organization pOrg, @Context Group pGroup, @Context AppUser pUser)
      throws SvcException {
    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());

    _revokeJWKSSvc.createRevokeJWKSJob(pGroup, config);

    return Response.ok().build();
  }

  @GET
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.RevokeJWKSResource.getRevokeJWKSStatus.GET")
  public Response getRevokeJWKSStatus(@Context final Organization pOrg, @Context final Group pGroup)
      throws SvcException {

    // STATUS_FIELD will be set based on the result of the validation
    final RevokeJWKSResponseView revokeJWKSResponseView =
        _revokeJWKSSvc.getGetRevokeJWKSJobRequest(pGroup);

    return Response.ok().entity(revokeJWKSResponseView).build();
  }
}
