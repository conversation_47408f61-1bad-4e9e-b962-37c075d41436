package com.xgen.svc.atm.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.model.DeploymentItemType;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.UserPinnedCards;
import com.xgen.svc.atm.svc.UserPinnedCardsSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.stream.Collectors;

@Path("/automation/userPinnedCards/{groupId}")
@Singleton
public class UserPinnedCardsResource extends AutomationBaseResource {
  private UserPinnedCardsSvc _svc;

  @Inject
  public UserPinnedCardsResource(final UserPinnedCardsSvc pSvc) {
    _svc = pSvc;
  }

  @GET
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.getPins.GET")
  public Response getPins(@Context final Group pGroup, @Context final AppUser pUser) {
    return responseFromUserPinnedCards(_svc.getPins(pUser.getId(), pGroup.getId()));
  }

  @PUT
  @Path("/cluster/{clusterName}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.pinCluster.PUT")
  public Response pinCluster(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName) {
    final UserPinnedCards pinned = _svc.pinCluster(pUser.getId(), pGroup.getId(), pClusterName);
    _svc.savePins(pinned);
    return responseFromUserPinnedCards(pinned);
  }

  @DELETE
  @Path("/cluster/{clusterName}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.unpinCluster.DELETE")
  public Response unpinCluster(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName) {
    final UserPinnedCards pinned = _svc.unpinCluster(pUser.getId(), pGroup.getId(), pClusterName);
    _svc.savePins(pinned);
    return responseFromUserPinnedCards(pinned);
  }

  @PUT
  @Path("/replicaSet/{rsId}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.pinReplicaSet.PUT")
  public Response pinReplicaSet(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId) {
    final UserPinnedCards pinned = _svc.pinReplicaSet(pUser.getId(), pGroup.getId(), pRsId);
    _svc.savePins(pinned);
    return responseFromUserPinnedCards(pinned);
  }

  @DELETE
  @Path("/replicaSet/{rsId}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.unpinReplicaSet.DELETE")
  public Response unpinReplicaSet(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("rsId") final String pRsId) {
    final UserPinnedCards pinned = _svc.unpinReplicaSet(pUser.getId(), pGroup.getId(), pRsId);
    _svc.savePins(pinned);
    return responseFromUserPinnedCards(pinned);
  }

  @PUT
  @Path("/process/{processName}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.pinStandalone.PUT")
  public Response pinStandalone(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String processName) {
    final UserPinnedCards pinned = _svc.pinStandalone(pUser.getId(), pGroup.getId(), processName);
    _svc.savePins(pinned);
    return responseFromUserPinnedCards(pinned);
  }

  @DELETE
  @Path("/process/{processName}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.UserPinnedCardsResource.unpinStandalone.DELETE")
  public Response unpinStandalone(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("processName") final String processName) {
    final UserPinnedCards pinned = _svc.unpinStandalone(pUser.getId(), pGroup.getId(), processName);
    _svc.savePins(pinned);
    return responseFromUserPinnedCards(pinned);
  }

  private Response responseFromUserPinnedCards(final UserPinnedCards pinned) {
    return Response.ok(
            pinned.getPinnedDeploymentItems().stream()
                .map(PinView::new)
                .collect(Collectors.toList()))
        .build();
  }

  private static class PinView {
    @JsonProperty("type")
    private DeploymentItemType _type;

    @JsonProperty("automationId")
    private String _automationId;

    private PinView(final UserPinnedCards.Pin pPin) {
      _type = pPin.getType();
      _automationId = pPin.getAutomationId();
    }
  }
}
