package com.xgen.svc.mms.api.res;

import static com.xgen.cloud.access.role._public.model.RoleSet.NAME.GLOBAL_READ_ONLY;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.nds.security._private.dao.NDSACMEOrderMetadataDao;
import com.xgen.cloud.nds.security._public.model.NDSACMEOrderMetadata;
import com.xgen.cloud.nds.security._public.model.NDSACMEOrderMetadata.State;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.res.filter.TestUtility;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/***
 * This webhook tracks metadata of ACME orders from local environment and stores the data in a cluster in
 * dev.
 */
@Path("/api/private/acme/")
@Singleton
public class WebhookAcmeResource extends ApiBaseResource {

  private final NDSACMEOrderMetadataDao _acmeOrderMetadataDao;
  private final ObjectMapper _objectMapper =
      CustomJacksonJsonProvider.createObjectMapper()
          .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

  @Inject
  public WebhookAcmeResource(
      final NDSACMEOrderMetadataDao pAcmeOrderMetadataDao, final AppSettings pAppSettings) {
    super(pAppSettings);
    _acmeOrderMetadataDao = pAcmeOrderMetadataDao;
  }

  @POST
  @Path("tracking")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({GLOBAL_READ_ONLY})
  @TestUtility
  @Auth(endpointAction = "epa.global.WebhookAcmeResource.tracking.POST")
  public Response tracking(
      @Context final HttpServletRequest pRequest, final NDSACMEOrderMetadata pOrderMetadata)
      throws Exception {
    var env = AppEnv.findByCode(pOrderMetadata.getEnvironment());
    if (!(env.isProd() || env.isProdGov() || env.isInternal())) {
      if (State.DONE.equals(pOrderMetadata.getState())) {
        _acmeOrderMetadataDao.markAcmeOrderAsComplete(pOrderMetadata.getId(), pOrderMetadata);
      } else {
        _acmeOrderMetadataDao.insertAcmeOrder(pOrderMetadata);
      }
      return new ApiResponseBuilder(false).ok().build();
    } else {
      return new ApiResponseBuilder(false).badRequest("Non-production environments only").build();
    }
  }
}
