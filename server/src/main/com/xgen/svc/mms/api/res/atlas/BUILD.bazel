load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "atlas",
    srcs = glob(["**/*.java"]),
    allow_lib_to_depend_on_service = True,
    deny_warnings = True,
    visibility = [
        "//server/src/main/com/xgen/cloud/group/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/backup/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/datalake/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/flex/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/fts/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/intel/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/project/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/sampledataset/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/cloud/nds/serverless/runtime/res:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/api/res/_private:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/api/res/atlas:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep: gazelle cannot resolve this dep in all cases, since it uses filegroups.
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/access/rolecheck",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/apiusagedata",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/auditinfosvc",
        "//server/src/main/com/xgen/cloud/authz/resource",
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingplatform/model/plan",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/brs/web",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/brs",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/explorer",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/groupcreation",
        "//server/src/main/com/xgen/cloud/common/jackson",
        "//server/src/main/com/xgen/cloud/common/logging",
        "//server/src/main/com/xgen/cloud/common/metrics",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/requestparams",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/user",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/common/versioning",
        "//server/src/main/com/xgen/cloud/common/view",
        "//server/src/main/com/xgen/cloud/configlimit",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/externalanalytics",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/federation",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/monitoredhost",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/flex",
        "//server/src/main/com/xgen/cloud/nds/flex/runtime/svc",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/fts",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/resourcepolicy",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/performanceadvisor",
        "//server/src/main/com/xgen/cloud/resourcetags",
        "//server/src/main/com/xgen/cloud/search/decoupled/external",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/iam/annotation",
        "//server/src/main/com/xgen/svc/common/logger",
        "//server/src/main/com/xgen/svc/core/model/api",
        "//server/src/main/com/xgen/svc/mms/api/res",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/api/res/monitoring",
        "//server/src/main/com/xgen/svc/mms/model/explorer",
        "//server/src/main/com/xgen/svc/mms/model/grouptype",
        "//server/src/main/com/xgen/svc/mms/model/performanceadvisor",
        "//server/src/main/com/xgen/svc/mms/res/filter/annotation",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "//server/src/main/com/xgen/svc/mms/svc/common",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_apache_directory_api_api_util",
        "@maven//:org_json_json",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_core",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
