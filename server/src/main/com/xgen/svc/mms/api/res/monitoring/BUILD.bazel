load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "monitoring",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/cloud:__subpackages__",
        "//server/src/main/com/xgen/module:__subpackages__",
        "//server/src/main/com/xgen/svc/brs/slurp/res:__subpackages__",
        "//server/src/main/com/xgen/svc/mms:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep: gazelle cannot resolve this dep in all cases, since it uses filegroups.
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/billingplatform/model/plan",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/db/mongo",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/querystats",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/monitoring/tsstrategy/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/regionalization",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/performanceadvisor",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/api/view/monitoring/querystats",
        "//server/src/main/com/xgen/svc/mms/model/performanceadvisor",
        "//server/src/main/com/xgen/svc/mms/res/filter/annotation",
        "@maven//:com_fasterxml_jackson_core_jackson_databind",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_core",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
