load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/cloud:__subpackages__",
        "//server/src/main/com/xgen/module:__subpackages__",
        "//server/src/main/com/xgen/svc/mms:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep: gazelle cannot resolve this dep in all cases, since it uses filegroups.
        "//server/src/main/com/xgen/cloud/abtesting",
        "//server/src/main/com/xgen/cloud/access/accesslist",
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/agent",
        "//server/src/main/com/xgen/cloud/agents",
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/alerts/integration",
        "//server/src/main/com/xgen/cloud/alerts/notify",
        "//server/src/main/com/xgen/cloud/apiuser",
        "//server/src/main/com/xgen/cloud/appconfig",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/billingplatform/invoice",
        "//server/src/main/com/xgen/cloud/billingplatform/invoiceexport",
        "//server/src/main/com/xgen/cloud/billingplatform/invoicetags",
        "//server/src/main/com/xgen/cloud/billingplatform/model/plan",
        "//server/src/main/com/xgen/cloud/billingshared/invoiceviews",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/brs/core/_private/dao",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/brs/publish",
        "//server/src/main/com/xgen/cloud/brs/thirdparty",
        "//server/src/main/com/xgen/cloud/brs/web",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/brs",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/jackson",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/okta",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/common/resource",
        "//server/src/main/com/xgen/cloud/common/retry",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/user",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/common/view",
        "//server/src/main/com/xgen/cloud/communication",
        "//server/src/main/com/xgen/cloud/configlimit",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/email",
        "//server/src/main/com/xgen/cloud/externalanalytics",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/federation",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/integration",
        "//server/src/main/com/xgen/cloud/integrations",
        "//server/src/main/com/xgen/cloud/invitation",
        "//server/src/main/com/xgen/cloud/legacyintegration",
        "//server/src/main/com/xgen/cloud/monitoring/agent",
        "//server/src/main/com/xgen/cloud/monitoring/common",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/monitoredhost",
        "//server/src/main/com/xgen/cloud/monitoring/ratelimit",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/naturallanguagequery",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/security",
        "//server/src/main/com/xgen/cloud/nds/security/_private/dao",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/payments/standalone/common",
        "//server/src/main/com/xgen/cloud/realm/activity",
        "//server/src/main/com/xgen/cloud/rolecheck",
        "//server/src/main/com/xgen/cloud/team",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/core/model/api",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/api/view/integrations/constants",
        "//server/src/main/com/xgen/svc/mms/api/view/versionManifest",
        "//server/src/main/com/xgen/svc/mms/dao/host",
        "//server/src/main/com/xgen/svc/mms/misc",
        "//server/src/main/com/xgen/svc/mms/model/agent/constants",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/model/grouptype",
        "//server/src/main/com/xgen/svc/mms/res/filter/annotation",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "//server/src/main/com/xgen/svc/mms/svc/common",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/main/com/xgen/svc/mms/util/namespace",
        "//third_party:guava",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:com_fasterxml_jackson_core_jackson_core",
        "@maven//:com_fasterxml_jackson_core_jackson_databind",
        "@maven//:com_google_code_gson_gson",
        "@maven//:com_twilio_sdk_twilio",
        "@maven//:commons_io_commons_io",
        "@maven//:commons_lang_commons_lang",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_apache_commons_commons_collections4",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_apache_httpcomponents_httpclient",
        "@maven//:org_apache_httpcomponents_httpcore",
        "@maven//:org_json_json",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_core",
        "@maven//:org_mongodb_mongodb_driver_sync",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
