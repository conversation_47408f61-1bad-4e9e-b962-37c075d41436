package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.model.billing.SelfServeProduct;
import com.xgen.svc.mms.model.billing.plans.selfServe.SelfServePlanView;
import com.xgen.svc.mms.res.view.billing.PlanReactivationDetailsView;
import com.xgen.svc.mms.svc.billing.NDSSupportBiller;
import com.xgen.svc.mms.svc.billing.OrgPlanSvc;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import com.xgen.svc.mms.svc.billing.PaymentMethodSvc;
import com.xgen.svc.mms.svc.billing.PlanSvc;
import com.xgen.svc.mms.svc.billing.plans.selfServe.SelfServePlanProvider;
import com.xgen.svc.mms.svc.billing.plans.selfServe.SelfServePlanSvc;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Date;
import java.util.List;

@Path("/selfServe")
@Singleton
public class SelfServeSupportResource extends BaseResource {

  private final PayingOrgSvc payingOrgSvc;
  private final PlanSvc planSvc;
  private final OrgPlanSvc orgPlanSvc;
  private final SelfServePlanProvider selfServePlanProvider;
  private final PaymentMethodSvc paymentMethodSvc;
  private final NDSSupportBiller ndsSupportBiller;

  @Inject
  public SelfServeSupportResource(
      final PlanSvc pPlanSvc,
      final PayingOrgSvc pPayingOrgSvc,
      final SelfServePlanProvider pSelfServePlanProvider,
      final OrgPlanSvc pOrgPlanSvc,
      final PaymentMethodSvc pPaymentMethodSvc,
      final NDSSupportBiller pNdsSupportBiller) {
    payingOrgSvc = pPayingOrgSvc;
    planSvc = pPlanSvc;
    selfServePlanProvider = pSelfServePlanProvider;
    orgPlanSvc = pOrgPlanSvc;
    paymentMethodSvc = pPaymentMethodSvc;
    ndsSupportBiller = pNdsSupportBiller;
  }

  /** Get miscellaneous plan-related details displayed in Support page. */
  @GET
  @Hidden
  @Path("/reactivationDetails/org/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SelfServeSupportResource.getPlanDetails.GET")
  public Response getPlanDetails(
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @QueryParam("productType") SelfServeProduct.Type productType)
      throws SvcException {
    final Date proposedActivatedDate = new Date();
    final OrgPlan currentPlan = getPlanSvc().getCurrentPlan(org.getId());

    final SelfServePlanSvc selfServePlanSvc = getSelfServePlanProvider().get(productType);
    final Date startDate =
        selfServePlanSvc.getStartDate(org.getId(), currentPlan, proposedActivatedDate);
    final PrepaidPlan proposedPrepaidPlan =
        selfServePlanSvc.getProposedPlan(productType, startDate, proposedActivatedDate);
    final OrgPlan proposedOrgPlan =
        OrgPlan.Builder.fromPrepaidPlan(org.getId(), proposedPrepaidPlan).build();

    final long backfillChargeCents =
        ndsSupportBiller.estimateReactivationSupportCharges(
            org, proposedOrgPlan, startDate, proposedActivatedDate);

    final PlanReactivationDetailsView details =
        new PlanReactivationDetailsView(backfillChargeCents, startDate);
    return Response.ok().entity(details).build();
  }

  @POST
  @Path("/activate/orgs/{orgId}/productType/{productType}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SelfServeSupportResource.activate.POST")
  public Response activate(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("productType") final SelfServeProduct.Type pSelfServeProductType)
      throws Exception {
    if (getPayingOrgSvc().isPayingOrg(pOrganization.getId())
        || getPayingOrgSvc().isLinkedToPayingOrg(pOrganization.getId())) {
      return SimpleApiResponse.badRequest(
              BillingErrorCode.SUPPORT_PLAN_NOT_AVAILABLE_FOR_PAYING_ORGS)
          .build();
    }

    final Date now = new Date();
    getSelfServePlanProvider()
        .get(pSelfServeProductType)
        .activate(pOrganization.getId(), now, pAuditInfo);
    getPayingOrgSvc().copyBillingInfoToLinkedOrgs(pOrganization.getId());

    final OrgPlan newPlan = getPlanSvc().getCurrentPlan(pOrganization.getId());
    return Response.ok().entity(newPlan).build();
  }

  @POST
  @Path("/cancel/orgs/{orgId}/productType/{productType}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SelfServeSupportResource.cancel.POST")
  public Response cancel(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("productType") final SelfServeProduct.Type pSelfServeProductType)
      throws Exception {
    getPayingOrgSvc().validatePayingOrStandardOrg(pOrganization.getId());
    final Date now = new Date();
    getSelfServePlanProvider()
        .get(pSelfServeProductType)
        .cancel(pOrganization, pAppUser, now, pAuditInfo);
    getPayingOrgSvc().copyBillingInfoToLinkedOrgs(pOrganization.getId());
    final OrgPlan newPlan = getPlanSvc().getCurrentPlan(pOrganization.getId());
    return Response.ok().entity(newPlan).build();
  }

  @GET
  @Path("/selfServePlans/orgs/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SelfServeSupportResource.getSelfServePlans.GET")
  public Response getSelfServePlans(
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo)
      throws Exception {
    final List<SelfServePlanView> selfServePlanViews =
        getOrgPlanSvc().getOrgSelfServePrepaidPlans(pOrganization.getId());
    return Response.ok().entity(selfServePlanViews).build();
  }

  protected PayingOrgSvc getPayingOrgSvc() {
    return payingOrgSvc;
  }

  protected PlanSvc getPlanSvc() {
    return planSvc;
  }

  protected SelfServePlanProvider getSelfServePlanProvider() {
    return selfServePlanProvider;
  }

  protected OrgPlanSvc getOrgPlanSvc() {
    return orgPlanSvc;
  }

  protected PaymentMethodSvc getPaymentMethodSvc() {
    return paymentMethodSvc;
  }
}
