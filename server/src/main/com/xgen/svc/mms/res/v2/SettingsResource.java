/* (C) Copyright 2015, MongoDB, Inc. */

package com.xgen.svc.mms.res.v2;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.GroupStorageConfig;
import com.xgen.svc.mms.model.agent.constants.AgentLogLevel;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.json.JSONObject;

@Path("/v2/settings")
@Singleton
public class SettingsResource extends BaseResource {

  @Inject
  public SettingsResource() {}

  @GET
  @Path("/groupAdminOptions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.project.SettingsResource.getMonitoringAgentLogLevels.GET")
  public Response getMonitoringAgentLogLevels(@Context final HttpServletRequest pRequest)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    final JSONObject responseObject = new JSONObject();
    responseObject.put("allMonitoringAgentLogLevels", AgentLogLevel.publicValues());
    responseObject.put("allAutomationAgentLogLevels", AgentLogLevel.automationAgentValues());
    responseObject.put("allGroupStorageConfigModes", GroupStorageConfig.Mode.values());
    return Response.ok(responseObject.toString()).build();
  }
}
