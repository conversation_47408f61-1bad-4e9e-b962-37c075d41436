package com.xgen.svc.mms.res.v2.alerts;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.communication._public.model.enums.ProviderType;
import com.xgen.cloud.integration._public.model.Integration;
import com.xgen.cloud.integration._public.svc.IntegrationSvc;
import com.xgen.cloud.organization._public.model.Organization;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/v2/organization/{orgId}/integrations")
@Singleton
public class OrganizationIntegrationResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(OrganizationIntegrationResource.class);

  private final IntegrationSvc integrationSvc;
  private final AppSettings appSettings;

  @Inject
  public OrganizationIntegrationResource(
      final AppSettings appSettings, final IntegrationSvc integrationSvc) {
    this.integrationSvc = integrationSvc;
    this.appSettings = appSettings;
  }

  @GET
  @Path("/integrationConfigurations")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationIntegrationResource.getIntegrationConfigurations.GET")
  public Response getIntegrationConfigurations(@Context final Organization organization) {
    String clientId = appSettings.getStrProp("slack.oauth2.clientId");
    String frontendOAuthUrl = "https://slack.com/oauth/authorize";
    String scope = "chat:write:bot";

    final boolean useNewSlackCredentials = appSettings.getBoolean("slack.oauth.v2.enabled", false);
    if (useNewSlackCredentials) {
      clientId = appSettings.getStrProp("slack.oauth.v2.clientId");
      frontendOAuthUrl = appSettings.getStrProp("slack.oauth.v2.url.frontend");
      scope = appSettings.getStrProp("slack.oauth.v2.scopes");
    }

    final String baseOidcUrl = appSettings.getSlackOAuthUrl();

    boolean requiresUpgrade = false;
    final List<Integration> defaultIntegrations =
        integrationSvc.getDefaultIntegrationsForResource(
            new ResourceId(ResourceId.ResourceType.ORGANIZATION, organization.getId()),
            ProviderType.SLACK);
    if (defaultIntegrations != null
        && defaultIntegrations.size() > 0
        && defaultIntegrations.get(0) != null) {
      final com.xgen.cloud.integration._public.model.SlackIntegration integration =
          (com.xgen.cloud.integration._public.model.SlackIntegration) defaultIntegrations.get(0);
      if (integration.getCredentials() != null
          && integration.getCredentials().getDecryptedApiToken() != null
          && integration.getCredentials().getDecryptedApiToken().startsWith("xoxp")) {
        // New slack integrations will all be xoxb (bot) tokens.
        // Legacy tokens were all xoxp tokens.
        requiresUpgrade = true;
      }
    }

    boolean requiresMicrosoftTeamsUpgrade = false;
    final List<Integration> defaultMicrosoftTeamsIntegrations =
        integrationSvc.getDefaultIntegrationsForResource(
            new ResourceId(ResourceId.ResourceType.ORGANIZATION, organization.getId()),
            ProviderType.MICROSOFT_TEAMS);
    if (defaultMicrosoftTeamsIntegrations != null
        && defaultMicrosoftTeamsIntegrations.size() > 0
        && defaultMicrosoftTeamsIntegrations.get(0) != null) {
      final com.xgen.cloud.integration._public.model.MicrosoftTeamsIntegration integration =
          (com.xgen.cloud.integration._public.model.MicrosoftTeamsIntegration)
              defaultMicrosoftTeamsIntegrations.get(0);
      if (integration.getCredentials() != null
          && integration.getCredentials().getDecryptedWebhookUrl() != null) {
        final List<String> substrings =
            Arrays.stream(
                    integration.getCredentials().getDecryptedWebhookUrl().split("/webhookb2/"))
                .toList();
        if (substrings.size() == 2 && substrings.get(1) != null) {
          // It's complicated, but the TLDR is that old urls take the format
          // <uuid (36)>@<uuid (36)>/IncomingWebhook/<uuid (32)>/<uuid (36)>
          // After the /webhookb2/. The new urls take the same format, with an additional
          // /<Hash (unknown, but the examples I've seen have been 46 char)>
          // So, the old ones should be 159 here, and the new ones should be 206.
          // Cutting it off at 180, just in case there's variance in the hash length.
          requiresMicrosoftTeamsUpgrade = substrings.get(1).length() > 180;
        }
      }
    }

    final Map<ProviderType, Map<String, Object>> result =
        Map.of(
            ProviderType.SLACK,
            Map.of(
                "oidcRedirectBaseUrl", baseOidcUrl,
                "clientId", clientId,
                "oidcBaseUrl", frontendOAuthUrl,
                "scopes", scope,
                "v2Enabled", useNewSlackCredentials,
                // We shouldn't be trying to upgrade the user if we haven't actually released the
                // updated flow.
                "requiresUpgrade", useNewSlackCredentials && requiresUpgrade),
            ProviderType.MICROSOFT_TEAMS,
            Map.of("requiresUpgrade", requiresMicrosoftTeamsUpgrade));

    return Response.ok().entity(result).build();
  }
}
