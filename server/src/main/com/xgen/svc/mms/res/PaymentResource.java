package com.xgen.svc.mms.res;

import static com.xgen.cloud.access.role._public.model.RoleSet.GLOBAL_BILLING_ADMIN;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billing._public.svc.ICodeActivationSvc;
import com.xgen.cloud.billing._public.svc.RefundSvc;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.group._public.model.BillingAddress;
import com.xgen.cloud.group._public.model.CompanyAddress;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.common._public.svc.PaymentsFeatureCheckSvc;
import com.xgen.cloud.payments.creation._public.svc.shared.PartialPaymentSvc;
import com.xgen.cloud.payments.creditView._public.svc.CreditDetailsViewSvc;
import com.xgen.cloud.payments.creditView._public.svc.CreditViewSvc;
import com.xgen.cloud.payments.dealactivations._public.svc.PendingDealActivationViewSvc;
import com.xgen.cloud.payments.exception._public.common.PaymentProcessorSvcException;
import com.xgen.cloud.payments.export._public.svc.PartnerMarketplaceUsageReportExportApiSvc;
import com.xgen.cloud.payments.grpc._public.client.PaymentProcessingClient;
import com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient;
import com.xgen.cloud.payments.paymentview._public.model.PaymentView;
import com.xgen.cloud.payments.paymentview._public.svc.CreditDrawdownPaymentViewsSvc;
import com.xgen.cloud.payments.paymentview._public.svc.PaymentViewSvc;
import com.xgen.cloud.payments.salestax.validation._public.svc.AddressValidationSvc;
import com.xgen.cloud.payments.salestax.validation._public.svc.TaxIdValidationSvc;
import com.xgen.cloud.payments.standalone.common._public.gateway.PaymentMethodGateway;
import com.xgen.cloud.payments.yaypay._public.svc.YayPaySvc;
import com.xgen.cloud.services.payments.modules.partners._public.client.PartnerPaymentServiceClient;
import com.xgen.cloud.services.payments.modules.partners._public.model.PartnerPaymentRedirect;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentErrorDetails;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentErrorCode;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentErrorDetails;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.BraintreeCustomer;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.DiscountView;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.PaymentMethodView;
import com.xgen.svc.mms.model.billing.PurchaseType;
import com.xgen.svc.mms.model.billing.Refund;
import com.xgen.svc.mms.model.billing.RevenueRefundReason;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.view.BraintreeClientTokenView;
import com.xgen.svc.mms.res.view.LinkBraintreeCustomerView;
import com.xgen.svc.mms.res.view.billing.FetchYayPayLinkResponseView;
import com.xgen.svc.mms.res.view.billing.FetchYayPayLinkResponseView.FetchYayPayLinkResponseViewBuilder;
import com.xgen.svc.mms.res.view.billing.PaymentAuthenticationRequestView;
import com.xgen.svc.mms.svc.AdminOrgSvc;
import com.xgen.svc.mms.svc.billing.AddPaymentMethodOrchestratorSvc;
import com.xgen.svc.mms.svc.billing.BraintreeCustomerSvc;
import com.xgen.svc.mms.svc.billing.BraintreeSvc;
import com.xgen.svc.mms.svc.billing.CompanyAddressSvc;
import com.xgen.svc.mms.svc.billing.CreditSvc;
import com.xgen.svc.mms.svc.billing.DelinquentOrganizationSvc;
import com.xgen.svc.mms.svc.billing.DiscountSvc;
import com.xgen.svc.mms.svc.billing.InvoiceSvc;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import com.xgen.svc.mms.svc.billing.PaymentMethodSvc;
import com.xgen.svc.mms.svc.billing.PaymentMethodUpdateResponse;
import com.xgen.svc.mms.svc.billing.PaymentSvc;
import com.xgen.svc.mms.svc.billing.PullForwardRequestUpdateResponse;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/billing")
@Singleton
public class PaymentResource extends BaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(PaymentResource.class);

  private final CreditSvc creditSvc;
  private final CreditViewSvc creditViewSvc;
  private final CreditDetailsViewSvc creditDetailsViewSvc;
  private final OrganizationSvc organizationSvc;
  private final PayingOrgSvc payingOrgSvc;
  private final PaymentMethodSvc paymentMethodSvc;
  private final ICodeActivationSvc codeActivationSvc;
  private final PartnerMarketplaceUsageReportExportApiSvc partnerMarketplaceUsageReportExportApiSvc;
  private final PaymentSvc paymentSvc;
  private final PaymentProcessingClient paymentProcessingClient;
  private final DelinquentOrganizationSvc delinquentOrganizationSvc;
  private final PartialPaymentSvc partialPaymentSvc;
  private final TaxIdValidationSvc taxIdValidationSvc;
  private final InvoiceSvc invoiceSvc;
  private final PaymentViewSvc paymentViewSvc;
  private final AuthzSvc authzSvc;
  private final CreditDrawdownPaymentViewsSvc creditDrawdownPaymentViewsSvc;
  private final YayPaySvc yayPaySvc;
  private final SalesSoldDealActivationSvc salesSoldDealActivationSvc;
  private final AuditSvc auditSvc;
  private final DiscountSvc discountSvc;
  private final RefundSvc refundSvc;
  private final RefundProcessingClient refundProcessingClient;
  private final AppSettings appSettings;
  private final BraintreeSvc braintreeSvc;
  private final BraintreeCustomerSvc braintreeCustomerSvc;
  private final CompanyAddressSvc companyAddressSvc;
  private final GroupSvc groupSvc;
  private final AdminOrgSvc adminOrgSvc;
  private final AddressValidationSvc addressValidationSvc;
  private final AddPaymentMethodOrchestratorSvc addPaymentMethodOrchestratorSvc;
  private final PaymentMethodGateway paymentMethodGateway;
  private final PendingDealActivationViewSvc pendingDealActivationViewSvc;
  private final PartnerPaymentServiceClient partnerPaymentServiceClient;
  private final PaymentsFeatureCheckSvc paymentsFeatureCheckSvc;

  @Inject
  public PaymentResource(
      CreditSvc creditSvc,
      CreditViewSvc creditViewSvc,
      CreditDetailsViewSvc creditDetailsViewSvc,
      OrganizationSvc organizationSvc,
      PayingOrgSvc payingOrgSvc,
      PaymentMethodSvc paymentMethodSvc,
      ICodeActivationSvc codeActivationSvc,
      PartnerMarketplaceUsageReportExportApiSvc partnerMarketplaceUsageReportExportApiSvc,
      PaymentSvc paymentSvc,
      PaymentProcessingClient paymentProcessingClient,
      DelinquentOrganizationSvc delinquentOrganizationSvc,
      PartialPaymentSvc partialPaymentSvc,
      TaxIdValidationSvc taxIdValidationSvc,
      InvoiceSvc invoiceSvc,
      PaymentViewSvc paymentViewSvc,
      AuthzSvc authzSvc,
      CreditDrawdownPaymentViewsSvc creditDrawdownPaymentViewsSvc,
      YayPaySvc yayPaySvc,
      SalesSoldDealActivationSvc salesSoldDealActivationSvc,
      AuditSvc auditSvc,
      DiscountSvc discountSvc,
      RefundSvc refundSvc,
      RefundProcessingClient refundProcessingClient,
      AppSettings appSettings,
      BraintreeSvc braintreeSvc,
      BraintreeCustomerSvc braintreeCustomerSvc,
      CompanyAddressSvc companyAddressSvc,
      GroupSvc groupSvc,
      AdminOrgSvc adminOrgSvc,
      AddressValidationSvc addressValidationSvc,
      AddPaymentMethodOrchestratorSvc addPaymentMethodOrchestratorSvc,
      PaymentMethodGateway paymentMethodGateway,
      PendingDealActivationViewSvc pendingDealActivationViewSvc,
      PartnerPaymentServiceClient partnerPaymentServiceClient,
      PaymentsFeatureCheckSvc paymentsFeatureCheckSvc) {
    this.creditSvc = creditSvc;
    this.creditViewSvc = creditViewSvc;
    this.creditDetailsViewSvc = creditDetailsViewSvc;
    this.organizationSvc = organizationSvc;
    this.payingOrgSvc = payingOrgSvc;
    this.paymentMethodSvc = paymentMethodSvc;
    this.codeActivationSvc = codeActivationSvc;
    this.partnerMarketplaceUsageReportExportApiSvc = partnerMarketplaceUsageReportExportApiSvc;
    this.paymentSvc = paymentSvc;
    this.paymentProcessingClient = paymentProcessingClient;
    this.delinquentOrganizationSvc = delinquentOrganizationSvc;
    this.partialPaymentSvc = partialPaymentSvc;
    this.taxIdValidationSvc = taxIdValidationSvc;
    this.invoiceSvc = invoiceSvc;
    this.paymentViewSvc = paymentViewSvc;
    this.authzSvc = authzSvc;
    this.creditDrawdownPaymentViewsSvc = creditDrawdownPaymentViewsSvc;
    this.yayPaySvc = yayPaySvc;
    this.salesSoldDealActivationSvc = salesSoldDealActivationSvc;
    this.auditSvc = auditSvc;
    this.discountSvc = discountSvc;
    this.refundSvc = refundSvc;
    this.refundProcessingClient = refundProcessingClient;
    this.appSettings = appSettings;
    this.braintreeSvc = braintreeSvc;
    this.braintreeCustomerSvc = braintreeCustomerSvc;
    this.companyAddressSvc = companyAddressSvc;
    this.groupSvc = groupSvc;
    this.adminOrgSvc = adminOrgSvc;
    this.addressValidationSvc = addressValidationSvc;
    this.addPaymentMethodOrchestratorSvc = addPaymentMethodOrchestratorSvc;
    this.paymentMethodGateway = paymentMethodGateway;
    this.pendingDealActivationViewSvc = pendingDealActivationViewSvc;
    this.partnerPaymentServiceClient = partnerPaymentServiceClient;
    this.paymentsFeatureCheckSvc = paymentsFeatureCheckSvc;
  }

  @GET
  @Path("/anyActivePrepaidCredits/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.anyActivePrepaidCredits.GET")
  public Response anyActivePrepaidCredits(@Context Organization organization) {
    Date now = new Date();
    return Response.ok()
        .entity(creditSvc.hasAvailablePrepaidCreditInRangeRemaining(organization.getId(), now, now))
        .build();
  }

  @GET
  @Path("/anyActivePaymentMethod/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.anyActivePaymentMethod.GET")
  public Response anyActivePaymentMethod(@Context Organization organization) {
    ObjectId orgId = payingOrgSvc.getEffectivePayingOrgIdEventuallyConsistent(organization.getId());
    return Response.ok()
        .entity(paymentMethodGateway.getActivePaymentMethod(orgId, true).isPresent())
        .build();
  }

  @GET
  @Path("{orgId}/credit/{creditId}/dailyUsageReport/csv")
  @Produces("text/csv")
  @UiCall(
      roles = {RoleSet.GLOBAL_BILLING_ADMIN, RoleSet.GLOBAL_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.getPartnerDailyCreditUsageReportCsv.GET")
  public Response getPartnerDailyCreditUsageReportCsv(@PathParam("creditId") ObjectId creditId) {
    return partnerMarketplaceUsageReportExportApiSvc.writeDailyUsageReportSummaryCsv(
        creditId, new Date());
  }

  @GET
  @Path("{orgId}/credit/{creditId}/monthlyUsageReport/csv")
  @Produces("text/csv")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getPartnerMonthlyCreditUsageReportCsv.GET")
  public Response getPartnerMonthlyCreditUsageReportCsv(
      @PathParam("orgId") ObjectId orgId, @PathParam("creditId") ObjectId creditId) {

    final Credit credit = creditSvc.findById(creditId);
    if (credit == null || !credit.getOrgId().equals(orgId)) {
      return createNotFoundResponse();
    }

    return partnerMarketplaceUsageReportExportApiSvc.writeMonthlyUsageReportSummaryCsv(
        creditId, new Date());
  }

  @POST
  @Path("/retryPayment/{orgId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Operation(
      summary = "Retry charging payment",
      description = "Retries charging a payment manually.",
      parameters = {
        @Parameter(
            name = "paymentId",
            description = "Payment ID.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = PaymentAuthenticationRequestView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  @Auth(endpointAction = "epa.organization.PaymentResource.retryPayment.POST")
  public Response retryPayment(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId)
      throws Exception {
    Payment payment = paymentSvc.getPayment(paymentId);
    if (payment == null || !payment.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }
    if (payment.getStatus() != Payment.Status.FAILED
        && payment.getStatus() != Payment.Status.FAILED_AUTHENTICATION) {
      return SimpleApiResponse.ok().build();
    }

    ChargePaymentResult result =
        paymentProcessingClient.chargePayment(paymentId, false, false, true);

    if (result.requiresAuth() && result.getAuthRequest().isPresent()) {
      PaymentAuthenticationRequestView view =
          PaymentAuthenticationRequestView.builder()
              .stripePaymentIntentClientSecret(
                  result.getAuthRequest().get().getStripePaymentIntentClientSecret())
              .payment(paymentSvc.getPayment(payment.getId()))
              .build();
      return Response.ok().entity(view).build();
    }
    if (result.isFailed()) {
      Exception cause =
          result
              .getErrorDetails()
              .map(ChargePaymentErrorDetails::reconstructException)
              .orElse(null);
      if (cause instanceof PaymentProcessorSvcException) {
        throw cause;
      }
      LOG.error(
          "Failed to retry payment: paymentId={}, errorDetails={}",
          paymentId,
          result.getErrorDetails(),
          cause);
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }

    delinquentOrganizationSvc.reactivateOrganizationIfPaid(organization, auditInfo);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/confirmPayment/{orgId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.confirmPayment.POST")
  public Response confirmPayment(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId)
      throws Exception {
    Payment payment = paymentSvc.getPayment(paymentId);
    if (payment == null || !payment.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }

    ChargePaymentResult result = paymentProcessingClient.confirmPayment(paymentId);

    if (result.isFailed()) {
      LOG.warn(
          "Failed to confirm payment: paymentId={}, errorDetails={}",
          paymentId,
          result.getErrorDetails());

      throw result
          .getErrorDetails()
          .map(ChargePaymentErrorDetails::reconstructException)
          .orElse(new SvcException(CommonErrorCode.SERVER_ERROR));
    }

    delinquentOrganizationSvc.reactivateOrganizationIfPaid(organization, auditInfo);
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/stripeSetupIntentSecret/{orgId}/{billingAccount}")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getStripeSetupIntentSecret.GET")
  public Response getStripeSetupIntentSecret(
      @Context Organization organization,
      @PathParam("billingAccount") BillingAccount billingAccount)
      throws Exception {
    try {
      if (billingAccount == null) {
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
      }

      String setupIntentSecret = paymentMethodGateway.createOffSessionSetupIntent(billingAccount);

      return Response.ok().entity(setupIntentSecret).build();
    } catch (Exception e) {
      LOG.error(
          "Failed to retrieve Stripe SetupIntent secret for orgId={}", organization.getId(), e);
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }
  }

  @POST
  @Path("/forgivePayment/{orgId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.forgivePayment.POST")
  public Response forgivePayment(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId,
      @FormParam("reason") String reason)
      throws Exception {
    Payment payment = paymentSvc.getPayment(paymentId);
    if (payment == null || !payment.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }

    paymentSvc.markPaymentAsForgiven(payment, new Date(), reason, auditInfo);
    delinquentOrganizationSvc.reactivateOrganizationIfPaid(organization, auditInfo);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/partialPayment/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.partialPayment.POST")
  public Response partialPayment(@Context Organization organization) {
    Optional<Payment> optionalPayment =
        partialPaymentSvc.tryAddPartialPayment(organization.getId(), new Date());
    optionalPayment.ifPresent(payment -> paymentSvc.submitChargePaymentJob(payment.getId()));
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/anyFailedPayments/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.hasAnyFailedPayments.GET")
  public Response hasAnyFailedPayments(@Context Organization organization) {
    boolean resp = paymentSvc.anyFailedByOrgId(organization.getId());
    return SimpleApiResponse.ok().customField("anyFailedPayments", resp).build();
  }

  @POST
  @Path("/validateVATID/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.validateVATID.POST")
  public Response validateVATID(
      @FormParam("country") String country, @FormParam("vatNumber") String vATNumber)
      throws SvcException {
    boolean isValid = taxIdValidationSvc.validateVATID(country, vATNumber);
    return Response.ok().entity(isValid).build();
  }

  @GET
  @Path("/orgs/{orgId}/invoices/{invoiceId}/creditDrawdownPayments")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getInvoiceCreditDrawdownPaymentViews.GET")
  public Response getInvoiceCreditDrawdownPaymentViews(
      @Context AppUser user,
      @Context Organization organization,
      @PathParam("invoiceId") ObjectId invoiceId) {
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);
    if (invoice == null || !invoice.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }
    boolean isInMcDiscrepanciesExperimentVariant =
        paymentsFeatureCheckSvc.isMcDiscrepanciesUiEnabled(user, organization.getId());

    List<PaymentView> payments;
    if (payingOrgSvc.isPayingOrg(organization.getId())) {
      payments =
          creditDrawdownPaymentViewsSvc.getCreditDrawdownPaymentViewsForPayingOrgsInvoice(
              invoiceId,
              organization.getId(),
              invoice.getStartDate(),
              invoice.getEndDate(),
              authzSvc.isOrgBillingAdmin(user, organization.getId()),
              authzSvc.isGlobalReadOnly(user),
              authzSvc.isGlobalReadOnly(user),
              false,
              isInMcDiscrepanciesExperimentVariant);
    } else {
      payments =
          creditDrawdownPaymentViewsSvc.getCreditDrawdownPaymentViewsForInvoice(
              invoiceId,
              authzSvc.isOrgBillingAdmin(user, organization.getId()),
              authzSvc.isGlobalReadOnly(user),
              authzSvc.isGlobalReadOnly(user),
              false,
              isInMcDiscrepanciesExperimentVariant);
    }
    return Response.ok().entity(payments).build();
  }

  @GET
  @Path("/orgs/{orgId}/invoices/{invoiceId}/nonMonthlyCommitmentPayments")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getInvoiceNonMonthlyCommitmentPaymentViews.GET")
  public Response getInvoiceNonMonthlyCommitmentPaymentViews(
      @Context AppUser user,
      @PathParam("orgId") ObjectId orgId,
      @PathParam("invoiceId") ObjectId invoiceId) {
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);
    if (invoice == null || !invoice.getOrgId().equals(orgId)) {
      return createNotFoundResponse();
    }
    boolean isInMcDiscrepanciesExperimentVariant =
        paymentsFeatureCheckSvc.isMcDiscrepanciesUiEnabled(user, orgId);

    List<PaymentView> payments =
        paymentViewSvc.getNonMonthlyCommitmentPayments(
            invoiceId,
            authzSvc.isGlobalReadOnly(user),
            authzSvc.isOrgBillingAdmin(user, orgId),
            authzSvc.isOrgBillingAdmin(user, orgId),
            authzSvc.isGlobalReadOnly(user),
            authzSvc.isGlobalReadOnly(user),
            authzSvc.isGlobalReadOnly(user),
            List.of(),
            isInMcDiscrepanciesExperimentVariant);
    return Response.ok().entity(payments).build();
  }

  @GET
  @Path("/orgs/{orgId}/invoices/{invoiceId}/monthlyCommitmentPayments")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getMonthlyCommitmentPaymentsForInvoiceSummary.GET")
  public Response getMonthlyCommitmentPaymentsForInvoiceSummary(
      @Context AppUser user,
      @PathParam("orgId") ObjectId orgId,
      @PathParam("invoiceId") ObjectId invoiceId) {
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);
    if (invoice == null || !invoice.getOrgId().equals(orgId)) {
      return createNotFoundResponse();
    }

    return Response.ok()
        .entity(
            paymentViewSvc.getInvoiceMonthlyCommitmentPayments(
                invoice,
                authzSvc.isOrgBillingAdmin(user, orgId),
                authzSvc.isGlobalReadOnly(user),
                authzSvc.isOrgBillingAdmin(user, orgId),
                authzSvc.isGlobalReadOnly(user),
                authzSvc.isGlobalReadOnly(user),
                List.of()))
        .build();
  }

  @POST
  @Path("/invoicePayments/payByCheck/{orgId}/{invoiceId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.payInvoiceByCheck.POST")
  public Response payInvoiceByCheck(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      @PathParam("invoiceId") ObjectId invoiceId,
      @FormParam("checkNumber") String checkNumber,
      @FormParam("externalNote") String externalNote,
      @FormParam("dateReceived") Long dateReceivedMilliseconds)
      throws Exception {
    if (dateReceivedMilliseconds != null && dateReceivedMilliseconds < 0) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER, "date in milli seconds must be >= 0");
    }
    if (!ValidationUtils.isLong(checkNumber)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "invalid check number");
    }
    Date dateReceived =
        dateReceivedMilliseconds != null ? new Date(dateReceivedMilliseconds) : null;
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);
    if (invoice == null || !invoice.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }
    if (invoice.getStatus() == Invoice.Status.PENDING
        || invoice.getStatus() == Invoice.Status.INVOICED) {
      throw new SvcException(BillingErrorCode.INVALID_INVOICE_FOR_OFFLINE_PAYMENT);
    }
    paymentSvc.payByCheck(invoice, checkNumber, dateReceived, externalNote, auditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/invoicePayments/{orgId}/{paymentId}/yayPayLink")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.fetchYayPayLink.GET")
  public Response fetchYayPayLink(
      @PathParam("orgId") ObjectId orgId, @PathParam("paymentId") ObjectId paymentId) {
    Payment payment = paymentSvc.findById(paymentId);

    if (payment == null
        || !payment.getOrgId().equals(orgId)
        || payment.getNetsuiteInvoiceDetails() == null) {
      return createNotFoundResponse();
    }

    String link =
        yayPaySvc.fetchCustomerPortalUrlByInternalId(
            payment.getNetsuiteInvoiceDetails().getCustomerInternalId());

    FetchYayPayLinkResponseView response =
        new FetchYayPayLinkResponseViewBuilder().link(link).build();

    return Response.ok().entity(response).build();
  }

  @POST
  @Path("/invoicePayments/payByWireTransfer/{orgId}/{invoiceId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.payInvoiceByWireTransfer.POST")
  public Response payInvoiceByWireTransfer(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      @PathParam("invoiceId") ObjectId invoiceId,
      @FormParam("wireTransferNumber") String wireTransferNumber,
      @FormParam("externalNote") String externalNote,
      @FormParam("dateReceived") Long dateReceivedMilliSeconds)
      throws Exception {
    if (dateReceivedMilliSeconds != null && dateReceivedMilliSeconds < 0) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER, "date in milli seconds must be >= 0");
    }
    if (!ValidationUtils.isValidWireTransferNumber(wireTransferNumber)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid wire transfer number");
    }
    Date dateReceived =
        dateReceivedMilliSeconds != null ? new Date(dateReceivedMilliSeconds) : null;
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);
    if (invoice == null || !invoice.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }
    if (invoice.getStatus() == Invoice.Status.PENDING
        || invoice.getStatus() == Invoice.Status.INVOICED) {
      throw new SvcException(BillingErrorCode.INVALID_INVOICE_FOR_OFFLINE_PAYMENT, invoiceId);
    }

    paymentSvc.payByWireTransfer(
        invoice, wireTransferNumber, dateReceived, externalNote, auditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/invoicePayments/{orgId}/{invoiceId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getInvoicePayment.GET")
  public Response getInvoicePayment(
      @Context Organization organization,
      @PathParam("invoiceId") ObjectId invoiceId,
      @PathParam("paymentId") ObjectId paymentId) {
    ObjectId orgId = organization.getId();
    Payment payment = paymentSvc.getPayment(paymentId);
    if (payment == null
        || !payment.getOrgId().equals(orgId)
        || !payment.getInvoiceId().equals(invoiceId)) {
      return createNotFoundResponse();
    }
    return Response.ok().entity(payment).build();
  }

  @GET
  @Path("/invoiceRefunds/{orgId}/{invoiceId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getInvoiceRefunds.GET")
  public Response getInvoiceRefunds(
      @Context Organization organization, @PathParam("invoiceId") ObjectId invoiceId) {
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);
    if (invoice == null || !invoice.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }
    List<Refund> refunds = invoiceSvc.getRefunds(invoiceId);
    return Response.ok().entity(refunds).build();
  }

  @GET
  @Path("/paymentMethods/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getPaymentMethods.GET")
  public Response getPaymentMethods(
      @Context Organization organization,
      @QueryParam("useEffectivePayingOrg") @DefaultValue("true") boolean useEffectivePayingOrg) {
    List<PaymentMethodView> paymentMethods =
        paymentMethodGateway
            .getAllPaymentMethodsForOrg(organization.getId(), useEffectivePayingOrg)
            .stream()
            .map(PaymentMethodView::new)
            .toList();
    return Response.ok().entity(paymentMethods).build();
  }

  @POST
  @Path("/updatePaymentMethod/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.updatePaymentMethod.POST")
  public Response updatePaymentMethod(
      @Context Organization organization,
      @Context AppUser appUser,
      @FormParam("vatNumber") String vATNumber,
      @FormParam("couponCode") String couponCode,
      @FormParam("stripePaymentMethodId") String stripePaymentMethodId,
      @FormParam("stripeSetupIntentId") String stripeSetupIntentId,
      @FormParam("billingAccount") BillingAccount billingAccount,
      @FormParam("linkBraintree") boolean linkBraintree,
      @FormParam("addrLine1") String addrLine1,
      @FormParam("addrLine2") String addrLine2,
      @FormParam("city") String city,
      @FormParam("state") String state,
      @FormParam("zip") String zip,
      @FormParam("country") String country,
      @Nullable @FormParam("purchaseType") PurchaseType purchaseType,
      @Context AuditInfo auditInfo)
      throws Exception {

    BillingAddress billingAddress =
        linkBraintree
            ? new BillingAddress.Builder()
                .addressLineOne(addrLine1)
                .addressLineTwo(addrLine2)
                .city(city)
                .state(state)
                .zip(zip)
                .country(country)
                .build()
            : new BillingAddress.Builder().country(country).build();
    try {
      PaymentMethodUpdateResponse response =
          addPaymentMethodOrchestratorSvc.upsertPaymentMethodAndReprocessOrg(
              billingAccount,
              organization,
              appUser,
              stripePaymentMethodId,
              stripeSetupIntentId,
              linkBraintree,
              vATNumber,
              couponCode,
              billingAddress,
              auditInfo,
              Clock.systemUTC(),
              purchaseType);
      return Response.ok().entity(response).build();
    } catch (SvcException e) {
      // obscure the reason for the failure for OFAC related failures.
      if (e.getErrorCode() == BillingErrorCode.OFAC_HIT
          || e.getErrorCode() == BillingErrorCode.OFAC_ERROR) {
        throw new SvcException(CommonErrorCode.SERVER_ERROR);
      }
      throw e;
    }
  }

  @DELETE
  @Path("/paymentMethods/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.removePaymentMethods.DELETE")
  public Response removePaymentMethods(
      @Context Organization organization, @Context AuditInfo auditInfo) {
    paymentMethodSvc.deleteOrgPaymentMethods(organization.getId(), new Date());
    BillingAudit.Builder builder =
        new BillingAudit.Builder(BillingEvent.Type.PAYMENT_METHODS_REMOVED, ObjectId.get());
    builder.auditInfo(auditInfo);
    builder.orgId(organization.getId());
    auditSvc.saveAuditEvent(builder.build());
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/validateActivationCodePullForwardEligible/{orgId}/{activationCode}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.PaymentResource.validateActivationCodePullForwardEligible.GET")
  public Response validateActivationCodePullForwardEligible(
      @Context AuditInfo auditInfo,
      @PathParam("activationCode") String activationCode,
      @PathParam("orgId") ObjectId orgId)
      throws Exception {
    Date now = new Date();

    PullForwardRequestUpdateResponse pullForwardEligible =
        salesSoldDealActivationSvc.validateActivationCodePullForwardEligible(
            orgId, activationCode, now, auditInfo);

    return Response.ok().entity(pullForwardEligible).build();
  }

  @POST
  @Path("/applyCouponOrActivationCode/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.PaymentResource.applyCouponOrActivationCode.POST")
  public Response applyCouponOrActivationCode(
      @Context Organization organization,
      @Context AppUser user,
      @Context AuditInfo auditInfo,
      @FormParam("couponCode") String code,
      @FormParam("allowPullforward") boolean allowPullforward)
      throws Exception {
    payingOrgSvc.validatePayingOrStandardOrg(organization.getId());
    codeActivationSvc.applyCode(
        organization, user, code, Clock.systemUTC(), auditInfo, allowPullforward);

    payingOrgSvc.copyBillingInfoToLinkedOrgs(organization.getId());

    Map<String, String> responseMap = new HashMap<>();

    List<Credit> marketplaceMonthlyCommitmentCredits =
        creditSvc.findByActivationCode(code).stream()
            .filter(Credit::isPartnerMarketplaceUsageBasedMonthlyCommitment)
            .toList();

    boolean showDealLateActivationMsg =
        marketplaceMonthlyCommitmentCredits.stream().anyMatch(Credit::isLateActivated);

    responseMap.put("showDealLateActivationMsg", showDealLateActivationMsg ? "true" : "false");

    return Response.ok(responseMap).build();
  }

  @GET
  @Path("/activationCode/{activationCode}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(auth = false)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response getAvailableActivationCode(@PathParam("activationCode") String activationCode) {
    boolean exists = salesSoldDealActivationSvc.isAvailableActivationCode(activationCode);
    if (exists) {
      return SimpleApiResponse.ok().build();
    }
    throw new NotFoundException();
  }

  @GET
  @Path("/credits/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getCredits.GET")
  public Response getCredits(
      @Context Organization organization,
      @Context AppUser user,
      @QueryParam("filter") @DefaultValue("noFilter") String filter)
      throws SvcException {
    try {
      return Response.ok().entity(creditViewSvc.getCreditViews(organization, user, filter)).build();
    } catch (IllegalArgumentException e) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid filter value: " + filter);
    }
  }

  @GET
  @Path("/orgs/{orgId}/creditDetails/{creditId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getCreditDetails.GET")
  public Response getCreditDetails(
      @Context Organization organization,
      @Context AppUser user,
      @PathParam("creditId") ObjectId creditId,
      @QueryParam("upToDate") LocalDate upToDate) {
    Credit credit = creditSvc.findById(creditId);
    upToDate = Objects.requireNonNullElse(upToDate, LocalDate.now());

    if (credit == null || !credit.getOrgId().equals(organization.getId())) {
      return createNotFoundResponse();
    }

    return Response.ok()
        .entity(creditDetailsViewSvc.getCreditDetailsView(credit, upToDate, user))
        .build();
  }

  @GET
  @Path("/pendingDealActivationErrors/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getPendingDealActivationErrors.GET")
  public Response getPendingDealActivationErrors(@Context Organization organization) {
    return Response.ok()
        .entity(pendingDealActivationViewSvc.getPendingDealActivationErrors(organization.getId()))
        .build();
  }

  @GET
  @Path("/monthlyCommitmentCredits/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getDirectMonthlyCommitmentCredits.GET")
  public Response getDirectMonthlyCommitmentCredits(
      @Context Organization organization, @Context AppUser user) {
    return Response.ok()
        .entity(creditViewSvc.getDirectMonthlyCommitmentCreditViews(organization, user))
        .build();
  }

  @GET
  @Path("/discounts/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getDiscounts.GET")
  public Response getDiscounts(
      @Context Organization organization,
      @Context AppUser user,
      @QueryParam("activeDiscounts") boolean getActiveDiscounts) {
    List<DiscountView> views;
    if (getActiveDiscounts) {
      views =
          discountSvc.getActiveDiscountViewsByOrganization(
              organization, new Date(), authzSvc.isGlobalBillingAdmin(user));
    } else {
      views =
          discountSvc.getExpiredDiscountViews(
              organization, new Date(), authzSvc.isGlobalBillingAdmin(user));
    }
    return Response.ok().entity(views).build();
  }

  public Response getBillingLimits(@Context Organization organization) {
    ObjectId payingOrgId =
        payingOrgSvc.getEffectivePayingOrgIdEventuallyConsistent(organization.getId());
    Organization payingOrganization = organizationSvc.findById(payingOrgId);
    Map<String, Long> map = new HashMap<>();
    if (payingOrganization.getGroupType() == GroupType.NDS) {
      map.put("hourlyBillingLimitCents", payingOrganization.getNDSHourlyBillingLimitCents());
      map.put("maxOutstandingBillCents", payingOrganization.getNDSMaxOutstandingBillCents());
      map.put("maxOutstandingBillCapCents", payingOrganization.getNDSMaxOutstandingBillCapCents());
    }
    return Response.ok().entity(map).build();
  }

  @PATCH
  @Path("/billingLimits/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.updateBillingLimits.PATCH")
  public Response updateBillingLimits(
      @Context Organization organization,
      @FormParam("hourlyBillingLimitCents") Long hourlyBillingLimitCents,
      @FormParam("maxOutstandingBillCents") Long maxOutstandingBillCents,
      @FormParam("maxOutstandingBillCapCents") Long maxOutstandingBillCapCents) {

    if (hourlyBillingLimitCents != null) {
      organizationSvc.setNDSHourlyBillingLimitCents(organization.getId(), hourlyBillingLimitCents);
    }
    if (maxOutstandingBillCents != null) {
      organizationSvc.setNDSMaxOutstandingBillCents(organization.getId(), maxOutstandingBillCents);
    }
    if (maxOutstandingBillCapCents != null) {
      organizationSvc.setNDSMaxOutstandingBillCapCents(
          organization.getId(), maxOutstandingBillCapCents);
    }
    return getBillingLimits(organization);
  }

  @POST
  @Path("/fullRefund/{orgId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.fullRefund.POST")
  public Response fullRefund(
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId,
      @FormParam("reason") String reason,
      @FormParam("revenueRefundReason") RevenueRefundReason revenueRefundReason)
      throws Exception {
    RefundPaymentResult result =
        refundProcessingClient.fullRefundPayment(paymentId, reason, revenueRefundReason, auditInfo);
    throwUserExceptionIfRefundFailed(paymentId, result);

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/partialRefund/{orgId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.partialRefund.POST")
  public Response partialRefund(
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId,
      @FormParam("amountCents") long amountCents,
      @FormParam("reason") String reason,
      @FormParam("revenueRefundReason") RevenueRefundReason revenueRefundReason)
      throws Exception {
    RefundPaymentResult result =
        refundProcessingClient.partialRefundPayment(
            paymentId, amountCents, reason, revenueRefundReason, auditInfo);
    throwUserExceptionIfRefundFailed(paymentId, result);

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/taxRefund/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.partialRefund.POST")
  public Response partialRefund(
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId,
      @FormParam("reason") String reason)
      throws Exception {
    RefundPaymentResult result =
        refundProcessingClient.fullTaxRefundPayment(paymentId, reason, auditInfo);
    throwUserExceptionIfRefundFailed(paymentId, result);

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/chargeThenRefund/{orgId}/{paymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.chargeThenRefund.POST")
  public Response chargeThenRefund(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      @PathParam("paymentId") ObjectId paymentId,
      @FormParam("reason") String reason,
      @FormParam("revenueRefundReason") RevenueRefundReason revenueRefundReason)
      throws Exception {
    if (StringUtils.isBlank(reason)) {
      throw new SvcException(RefundPaymentErrorCode.REASON_REQUIRED);
    }
    if (revenueRefundReason == null) {
      throw new SvcException(RefundPaymentErrorCode.REVENUE_REASON_REQUIRED);
    }

    try {
      boolean authNeeded =
          refundSvc.chargeThenRefund(
              paymentId, organization, reason, revenueRefundReason, auditInfo);
      return Response.ok().entity(authNeeded).build();
    } catch (SvcException e) {
      if (e.getErrorCode().equals(RefundPaymentErrorCode.PAYMENT_NOT_FOUND)) {
        return createNotFoundResponse();
      }
      throw e;
    } catch (Exception e) {
      LOG.error("Failed to charge-then-refund payment: paymentId={}", paymentId, e);
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }
  }

  @GET
  @Path("/stripeDashboard/{orgId}")
  @Produces(MediaType.TEXT_HTML)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.stripeDashboard.GET")
  public Response stripeDashboard(@Context Organization org) throws Exception {
    PaymentMethod paymentMethod =
        paymentMethodGateway.getActivePaymentMethod(org.getId(), true).orElse(null);
    if (paymentMethod == null) {
      return createNotFoundResponse();
    }
    StringBuilder sb = new StringBuilder("https://dashboard.stripe.com/merchant_redirect/");
    switch (paymentMethod.getBillingAccount()) {
      case MONGODB_INC:
      default:
        sb.append(appSettings.getStrProp("stripe.accountId"));
        break;
      case MONGODB_LTD:
        sb.append(appSettings.getStrProp("stripe.accountId.ltd"));
        break;
    }
    sb.append("?destination=/customers/");
    sb.append(paymentMethod.getStripeCustomerId());
    return tempRedirect(sb.toString());
  }

  @GET
  @Path("/braintreeDashboard/{orgId}")
  @Produces(MediaType.TEXT_HTML)
  @UiCall(roles = GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.PaymentResource.braintreeDashboard.GET")
  public Response braintreeDashboard(@Context Organization org) throws Exception {
    ObjectId orgId = payingOrgSvc.getEffectivePayingOrgIdEventuallyConsistent(org.getId());
    Optional<BraintreeCustomer> braintreeCustomer = braintreeCustomerSvc.findByOrgId(orgId);
    if (braintreeCustomer.isEmpty()) {
      return createNotFoundResponse();
    }
    return tempRedirect(braintreeSvc.getCustomerPage(orgId));
  }

  @GET
  @Path("/braintreeClientToken/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getBraintreeClientToken.GET")
  public Response getBraintreeClientToken(@Context Organization organization) {
    BraintreeClientTokenView response = braintreeSvc.getClientToken(organization.getId());
    return Response.ok().entity(response).build();
  }

  @POST
  @Path("/braintreeCustomer/{orgId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.linkBraintreeCustomer.POST")
  public Response linkBraintreeCustomer(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      LinkBraintreeCustomerView linkBraintreeCustomerView)
      throws SvcException {
    braintreeSvc.linkBraintreeCustomer(
        organization.getId(),
        linkBraintreeCustomerView.getPaymentMethodNonce(),
        linkBraintreeCustomerView.getType(),
        linkBraintreeCustomerView.getBillingAddress(),
        auditInfo);
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/braintreeCustomer/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getBraintreeCustomer.GET")
  public Response getBraintreeCustomer(@Context Organization organization) {

    Optional<BraintreeCustomer> customer = braintreeCustomerSvc.findByOrgId(organization.getId());
    if (customer.isPresent()) {
      return Response.ok().entity(customer.get()).build();
    } else {
      return Response.noContent().build();
    }
  }

  @POST
  @Path("/pullForwardCredit/{orgId}/{creditId}")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.pullForwardCredit.POST")
  public Response pullForwardCredit(
      @PathParam("orgId") ObjectId orgId,
      @PathParam("creditId") ObjectId creditId,
      @Context AuditInfo auditInfo)
      throws SvcException {

    final Credit credit = creditSvc.findById(creditId);
    if (credit == null || !credit.getOrgId().equals(orgId)) {
      return createNotFoundResponse();
    }

    Date now = new Date();

    creditSvc.pullForwardCredit(creditId, now, auditInfo);

    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/hasEffectivePaymentMethod/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.hasEffectivePaymentMethod.GET")
  public Response hasEffectivePaymentMethod(@Context Organization organization) {
    boolean hasEffectivePaymentMethod =
        paymentMethodGateway.hasEffectivePaymentMethod(organization.getId());
    return Response.ok(hasEffectivePaymentMethod).build();
  }

  @GET
  @Path("/paymentConfigRedirect/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getPartnerPaymentRedirect.GET")
  public Response getPartnerPaymentRedirect(@Context Organization organization) {
    PartnerPaymentRedirect partnerPaymentRedirect =
        partnerPaymentServiceClient.getPartnerPaymentRedirect(organization.getId());

    return Response.ok().entity(partnerPaymentRedirect).build();
  }

  @GET
  @Path("/hasCreditAvailableAsPaymentMethod/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.hasCreditAvailableAsPaymentMethod.GET")
  public Response hasCreditAvailableAsPaymentMethod(@Context Organization organization) {
    boolean hasCreditAvailableAsPaymentMethod =
        paymentMethodGateway.hasCreditAvailableAsPaymentMethod(
            organization.getId(), LocalDateTime.now());
    return Response.ok(hasCreditAvailableAsPaymentMethod).build();
  }

  @GET
  @Path("/companyAddress/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.getCompanyAddress.GET")
  public Response getCompanyAddress(@Context Organization organization) throws Exception {
    CompanyAddress address = companyAddressSvc.findActiveByOrgId(organization.getId(), true);
    return Response.ok().entity(Objects.requireNonNullElse(address, EMPTY_JSON_OBJECT)).build();
  }

  @PUT
  @Path("/companyAddress/{orgId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.updateCompanyAddress.PUT")
  public Response updateCompanyAddress(
      @Context Organization org,
      @Context AuditInfo auditInfo,
      @Context AppUser user,
      CompanyAddress companyAddress)
      throws Exception {
    try {
      companyAddressSvc.setCompanyAddress(org, companyAddress, new Date(), auditInfo, user);
    } catch (SvcException e) {
      // obscure the reason for the failure for OFAC related failures.
      if (e.getErrorCode() == BillingErrorCode.OFAC_HIT
          || e.getErrorCode() == BillingErrorCode.OFAC_ERROR) {
        throw new SvcException(CommonErrorCode.SERVER_ERROR);
      }
      throw e;
    }
    return Response.ok().entity(companyAddress).build();
  }

  @POST
  @Path("/validateAddress/{orgId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.validateAddress.POST")
  public Response validateAddress(final BillingAddress address) throws Exception {
    addressValidationSvc.validateAddress(address);
    return Response.ok().build();
  }

  @POST
  @Path("/terminateServicesRequestedByUser/{orgId}")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.ORG_BILLING_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PaymentResource.terminateServicesRequestedByUser.POST")
  public Response terminateServicesRequestedByUser(
      @Context final AppUser user,
      @Context final Organization organization,
      @Context final AuditInfo auditInfo)
      throws SvcException {
    // If user is not org owner, check if the user is the owner of all active projects
    final ObjectId orgId = organization.getId();
    if (!authzSvc.isOrgOwner(user, orgId)) {
      final List<Group> groups = groupSvc.findByOrgId(orgId, false);
      for (final Group group : groups) {
        if (!authzSvc.isProjectOwner(user, group)) {
          return Response.status(Status.FORBIDDEN).build();
        }
      }
    }

    adminOrgSvc.terminateServicesRequestedByUser(organization, auditInfo, user);
    return Response.ok(Collections.emptyMap()).build();
  }

  private void throwUserExceptionIfRefundFailed(ObjectId paymentId, RefundPaymentResult result)
      throws SvcException {
    if (result.isFailed()) {
      Exception cause =
          result
              .getErrorDetails()
              .map(RefundPaymentErrorDetails::reconstructException)
              .orElse(null);
      if (cause instanceof PaymentProcessorSvcException svcException) {
        throw svcException;
      }
      LOG.error(
          "Failed to refund a payment: paymentId={}, errorDetails={}",
          paymentId,
          result.getErrorDetails(),
          cause);
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }
  }
}
