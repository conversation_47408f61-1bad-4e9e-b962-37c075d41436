package com.xgen.svc.mms.res;

import static com.xgen.cloud.common.model._public.error.CommonErrorCode.INVALID_PARAMETER;
import static com.xgen.cloud.common.util._public.util.ValidationUtils.isValidObjectId;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.svc.mms.res.BaseMetricsResource.assertAccessToAnyCluster;

import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.constants.MonitoringConstants;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.logs._public.svc.QueryProfilerSvc;
import com.xgen.cloud.monitoring.logs._public.view.AbbrvSlowLogEntryView;
import com.xgen.cloud.monitoring.logs._public.view.ProfilingLevelChangeHistoryView;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.regionalization._public.RegionalizationValidator;
import com.xgen.cloud.nds.regionalization._public.RegionalizationValidator.RegionalEndpoint;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.model.performanceadvisor.SlowQueryLogEntry;
import com.xgen.svc.mms.model.performanceadvisor.profiler.AbbrvSlowQueryLogEntry;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.mms.svc.DbProfileSvc.ProfilerEntryView;
import com.xgen.svc.mms.svc.SlowQueryLogSvc;
import com.xgen.svc.mms.svc.host.LegacyHostSvc;
import io.prometheus.client.Counter;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/v3/profiler")
@Singleton
public class QueryProfilerResource extends BaseResource {
  private static final Counter GET_PROFILER_ENTRIES_REQUESTS_COUNTER =
      Counter.build()
          .name("query_profiler_resource_get_profiler_entries_atlas_requests_total")
          .help("Total get profiler entries requests from Atlas Home Page")
          .labelNames("from")
          .namespace(MonitoringConstants.ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final Logger LOG = LoggerFactory.getLogger(QueryProfilerResource.class);
  public static final String QUERY_LIST_TABLE_SAMPLE_LIMIT_KEY =
      "mms.monitoring.slowlogs.queryListTableSampleLimit";
  public static final Integer QUERY_LIST_TABLE_SAMPLE_LIMIT_DEFAULT = 10000;
  public static final String SLOW_QUERY_LOG_FROM_QUERY_SHAPE_HASH_LIMIT_KEY =
      "mms.monitoring.slowlogs.slowQueryLogFromQueryShapeHashLimit";
  public static final int DEFAULT_SLOW_QUERY_LOG_FROM_QUERY_SHAPE_HASH_LIMIT = 3;

  private final HostClusterLifecycleSvc _hostClusterLifecycleSvc;
  private final HostSvc _hostSvc;
  private final LegacyHostSvc _legacyHostSvc;
  private final QueryProfilerSvc _queryProfilerSvc;
  private final SlowQueryLogSvc _slowQueryLogSvc;
  private final AuthzSvc _authzSvc;
  private final AppSettings _appSettings;
  private final RegionalizationValidator _regionalizationValidator;

  @Inject
  public QueryProfilerResource(
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final HostSvc pHostSvc,
      final LegacyHostSvc pLegacyHostSvc,
      final QueryProfilerSvc pQueryProfilerSvc,
      final SlowQueryLogSvc pSlowQueryLogSvc,
      final AuthzSvc pAuthzSvc,
      final AppSettings pAppSettings,
      final RegionalizationValidator pRegionalizationValidator) {
    _hostClusterLifecycleSvc = pHostClusterLifecycleSvc;
    _hostSvc = pHostSvc;
    _legacyHostSvc = pLegacyHostSvc;
    _queryProfilerSvc = pQueryProfilerSvc;
    _slowQueryLogSvc = pSlowQueryLogSvc;
    _authzSvc = pAuthzSvc;
    _appSettings = pAppSettings;
    _regionalizationValidator = pRegionalizationValidator;
  }

  @GET
  @Hidden
  @Path("/entries/{groupId}/count")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getCountOfProfilerEntriesByHostId.GET")
  public Response getCountOfProfilerEntriesByHostId(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final String pGroupId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("analyticHostIds[]") final List<String> pAnalyticHostIds,
      @QueryParam("replicaStates[]") final List<String> pReplicaStates,
      @QueryParam("namespaces[]") final List<String> pNamespaces,
      @QueryParam("operations[]") final List<String> pOperations,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("from") @DefaultValue("emptyFrom") final String pFrom,
      @QueryParam("threshold") @DefaultValue("0") final Long pThreshold,
      @QueryParam("excludeSystemLogs") final boolean excludeSystemLogs)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrg, pGroup, false, RegionalEndpoint.V3_PROFILER_ENTRIES_COUNT);

    if (pHostIds.isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    if (pHostIds.stream().anyMatch(hostId -> !ValidationUtils.isValidHostName(hostId))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var hosts = _hostSvc.findHostsByIds(pGroup.getId(), pHostIds, false);
    if (!_hostClusterLifecycleSvc.userCanAccessHosts(pUser, pGroup, hosts)) {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final boolean isHistoricalReplicaStateEnabled =
        isFeatureFlagEnabled(FeatureFlag.HISTORICAL_REPLICA_STATE, _appSettings, pOrg, pGroup);
    if (isHistoricalReplicaStateEnabled && pReplicaStates != null) {
      if (pReplicaStates.stream()
          .anyMatch(replicaState -> !ValidationUtils.isValidReplicaState(replicaState))) {
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Host Type");
      }

      // if we filter by analytic, we need analytic host ids
      final boolean hasAnalyticHostIds = pAnalyticHostIds != null && !pAnalyticHostIds.isEmpty();
      if (pReplicaStates.contains("ANALYTIC") && !hasAnalyticHostIds) {
        throw new SvcException(
            INVALID_PARAMETER,
            "Analytic host ids are required when filtering by analytic and primary/secondary");
      }
    }

    // default to max 10k entries for QueryListTable
    final int sampleLimit =
        _appSettings.getIntProp(
            QUERY_LIST_TABLE_SAMPLE_LIMIT_KEY, QUERY_LIST_TABLE_SAMPLE_LIMIT_DEFAULT);

    GET_PROFILER_ENTRIES_REQUESTS_COUNTER.labels(pFrom).inc();
    final Map<String, Integer> countsByHostId =
        _queryProfilerSvc.getSlowLogEntriesByHostIds(
            pGroup.getId(),
            pHostIds,
            pAnalyticHostIds,
            pReplicaStates,
            pNamespaces,
            pOperations,
            pSince,
            pUntil,
            pThreshold,
            sampleLimit,
            isHistoricalReplicaStateEnabled,
            excludeSystemLogs);
    return Response.ok(countsByHostId).build();
  }

  @GET
  @Hidden
  @Path("/entries/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditEventType = AccessEvent.Type.VISUAL_PROFILER,
      hostSource = AccessAudit.HostSource.NONE)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getProfilerEntries.GET")
  public Response getProfilerEntries(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final String pGroupId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("analyticHostIds[]") final List<String> pAnalyticHostIds,
      @QueryParam("replicaStates[]") final List<String> pReplicaStates,
      @QueryParam("namespaces[]") final List<String> pNamespaces,
      @QueryParam("operations[]") final List<String> pOperations,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("from") @DefaultValue("emptyFrom") final String pFrom)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrg, pGroup, false, RegionalEndpoint.V3_PROFILER_ENTRIES);

    if (pHostIds.isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    if (pHostIds.stream().anyMatch(hostId -> !ValidationUtils.isValidHostName(hostId))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var hosts = _hostSvc.findHostsByIds(pGroup.getId(), pHostIds, false);
    if (!_hostClusterLifecycleSvc.userCanAccessHosts(pUser, pGroup, hosts)) {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final boolean isHistoricalReplicaStateEnabled =
        isFeatureFlagEnabled(FeatureFlag.HISTORICAL_REPLICA_STATE, _appSettings, pOrg, pGroup);
    if (isHistoricalReplicaStateEnabled && pReplicaStates != null) {
      if (pReplicaStates.stream()
          .anyMatch(replicaState -> !ValidationUtils.isValidReplicaState(replicaState))) {
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Host Type");
      }

      // if we filter by analytic, we need analytic host ids
      final boolean hasAnalyticHostIds = pAnalyticHostIds != null && !pAnalyticHostIds.isEmpty();
      if (pReplicaStates.contains("ANALYTIC") && !hasAnalyticHostIds) {
        throw new SvcException(
            INVALID_PARAMETER,
            "Analytic host ids are required when filtering by analytic and primary/secondary");
      }
    }

    final boolean shouldClassifyOp =
        isFeatureFlagEnabled(
            FeatureFlag.CLUSTER_CENTRIC_QUERY_PROFILER_FOLLOWUPS, _appSettings, pOrg, pGroup);

    // default to max 10k entries for QueryListTable
    final int sampleLimit =
        _appSettings.getIntProp(
            QUERY_LIST_TABLE_SAMPLE_LIMIT_KEY, QUERY_LIST_TABLE_SAMPLE_LIMIT_DEFAULT);
    GET_PROFILER_ENTRIES_REQUESTS_COUNTER.labels(pFrom).inc();
    final List<AbbrvSlowLogEntryView> entries =
        _queryProfilerSvc.getEntryViews(
            pGroup.getId(),
            pHostIds,
            pAnalyticHostIds,
            pReplicaStates,
            pNamespaces,
            pOperations,
            pSince,
            pUntil,
            sampleLimit,
            isHistoricalReplicaStateEnabled,
            shouldClassifyOp);
    return Response.ok(entries).build();
  }

  @GET
  @Hidden
  @Path("/entryBins/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditEventType = AccessEvent.Type.VISUAL_PROFILER,
      hostSource = AccessAudit.HostSource.NONE)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getBins.GET")
  public Response getBins(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final String pGroupId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("analyticHostIds[]") final List<String> pAnalyticHostIds,
      @QueryParam("namespaces[]") final List<String> pNamespaces,
      @QueryParam("operations[]") final List<String> pOperations,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("metric") final String pMetric,
      @QueryParam("replicaStates[]") final List<String> pReplicaStates)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrg, pGroup, false, RegionalEndpoint.V3_PROFILER_BINS);
    if (pHostIds.isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    if (pHostIds.stream().anyMatch(hostId -> !ValidationUtils.isValidHostName(hostId))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var hosts = _hostSvc.findHostsByIds(pGroup.getId(), pHostIds, false);
    if (!_hostClusterLifecycleSvc.userCanAccessHosts(pUser, pGroup, hosts)) {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final boolean isHistoricalReplicaStateEnabled =
        isFeatureFlagEnabled(FeatureFlag.HISTORICAL_REPLICA_STATE, _appSettings, pOrg, pGroup);
    if (isHistoricalReplicaStateEnabled && pReplicaStates != null) {
      if (pReplicaStates.stream()
          .anyMatch(replicaState -> !ValidationUtils.isValidReplicaState(replicaState))) {
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Host Type");
      }

      // if we filter by analytic, we need analytic host ids
      final boolean hasAnalyticHostIds = pAnalyticHostIds != null && !pAnalyticHostIds.isEmpty();
      if (pReplicaStates.contains("ANALYTIC") && !hasAnalyticHostIds) {
        throw new SvcException(
            INVALID_PARAMETER,
            "Analytic host ids are required when filtering by analytic and primary/secondary");
      }
    }

    final boolean shouldClassifyOp =
        isFeatureFlagEnabled(
            FeatureFlag.CLUSTER_CENTRIC_QUERY_PROFILER_FOLLOWUPS, _appSettings, pOrg, pGroup);

    final var response =
        _queryProfilerSvc.getResponseView(
            pGroup.getId(),
            pHostIds,
            pAnalyticHostIds,
            pReplicaStates,
            pNamespaces,
            pOperations,
            pSince,
            pUntil,
            pMetric,
            isHistoricalReplicaStateEnabled,
            shouldClassifyOp);
    return Response.ok(response).build();
  }

  @GET
  @Hidden
  @Path("/hourlyEntryBins/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getHourlyEntryBins.GET")
  public Response getHourlyEntryBins(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final String pGroupId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("operations[]") final List<String> pOperations,
      @QueryParam("replicaStates[]") final List<String> pReplicaStates,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("from") @DefaultValue("emptyFrom") final String pFrom,
      @QueryParam("metric") final String pMetric)
      throws JSONException, SvcException {
    GET_PROFILER_ENTRIES_REQUESTS_COUNTER.labels(pFrom).inc();

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrg, pGroup, false, RegionalEndpoint.V3_PROFILER_HOURLY_BINS);
    if (pHostIds.isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    if (pHostIds.stream().anyMatch(hostId -> !ValidationUtils.isValidHostName(hostId))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var hosts = _hostSvc.findHostsByIds(pGroup.getId(), pHostIds, false);
    if (!_hostClusterLifecycleSvc.userCanAccessHosts(pUser, pGroup, hosts)) {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final boolean isHistoricalReplicaStateEnabled =
        isFeatureFlagEnabled(FeatureFlag.HISTORICAL_REPLICA_STATE, _appSettings, pOrg, pGroup);
    if (isHistoricalReplicaStateEnabled && pReplicaStates != null) {
      if (pReplicaStates.stream()
          .anyMatch(replicaState -> !ValidationUtils.isValidReplicaState(replicaState))) {
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Host Type");
      }
    }
    final List<String> replicaStates =
        pReplicaStates != null ? pReplicaStates : Collections.emptyList();

    final boolean shouldClassifyOp =
        isFeatureFlagEnabled(
            FeatureFlag.CLUSTER_CENTRIC_QUERY_PROFILER_FOLLOWUPS, _appSettings, pOrg, pGroup);

    final var response =
        _queryProfilerSvc.getSlowLogHourlyBinView(
            pGroup.getId(),
            pHostIds,
            pOperations,
            pSince,
            pUntil,
            pMetric,
            isHistoricalReplicaStateEnabled,
            shouldClassifyOp,
            replicaStates);
    return Response.ok(response).build();
  }

  @GET
  @Hidden
  @Path("/changeHistory/{groupId}/{clusterId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AccessAudit(
      auditEventType = AccessEvent.Type.VISUAL_PROFILER,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getProfilingLevelChangeHistory.GET")
  public Response getProfilingLevelChangeHistory(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterId") final String pClusterId,
      @QueryParam("limit") @DefaultValue("10000") final int pLimit,
      @QueryParam("since") final Long pSince)
      throws JSONException, SvcException {
    final RequestParams params = getRequestParams(pRequest);
    final String methodPath = pRequest.getRequestURI();
    final List<ProfilingLevelChangeHistoryView> entries;

    if (!isValidObjectId(pClusterId)) {
      LOG.info(
          "clusterId is not a valid objectId, assuming it's a hostId, clusterId={}", pClusterId);

      final Host host = _hostSvc.findHostById(pClusterId, pGroup.getId());
      if (host == null) {
        LOG.warn("Couldn't find host with hostId={}", pClusterId);
        throw new SvcException(INVALID_PARAMETER, pClusterId);
      }

      params.setHost(host);
      _legacyHostSvc.assertUserCanAccessHost(pClusterId, params);
      entries =
          _queryProfilerSvc.getProfilingLevelChangeHistoryByHostIds(
              List.of(pClusterId), new Date(pSince), pLimit);
    } else {
      LOG.info(
          "clusterId is a valid objectId, assuming it's a hostClusterId, clusterId={}", pClusterId);

      final HostCluster cluster =
          _hostClusterLifecycleSvc.findHostClusterByClusterId(
              pGroup.getId(), new ObjectId(pClusterId), false);
      if (cluster == null || !cluster.hasHostIds()) {
        LOG.warn("Couldn't find hostCluster with hostClusterId={}", pClusterId);
        throw new SvcException(INVALID_PARAMETER, pClusterId);
      }

      assertAccessToAnyCluster(pGroup.getId(), new ObjectId(pClusterId), params, cluster);
      final List<String> hostIds = cluster.getHostIds().stream().collect(Collectors.toList());
      entries =
          _queryProfilerSvc.getProfilingLevelChangeHistoryByHostIds(
              hostIds, new Date(pSince), pLimit);
    }

    return Response.ok(entries).build();
  }

  @GET
  @Hidden
  @Path("/raw/{groupId}/{logId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = AccessEvent.Type.VISUAL_PROFILER)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getRawLog.GET")
  public Response getRawLog(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("logId") final ObjectId pLogId,
      @QueryParam("timestamp") final long pTimestampMillis)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrg, pGroup, false, RegionalEndpoint.V3_PROFILER_RAW_LOG);
    final boolean canViewPii = _authzSvc.hasPiiViewAccess(pUser, pGroup);
    final Instant timestamp = Instant.ofEpochMilli(pTimestampMillis);
    final SlowQueryLogEntry entry = _slowQueryLogSvc.findSlowQueryLogEntryById(pLogId, timestamp);
    if (!entry.getGroupId().equals(pGroup.getId())) {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final boolean shouldClassifyOp =
        isFeatureFlagEnabled(
            FeatureFlag.CLUSTER_CENTRIC_QUERY_PROFILER_FOLLOWUPS, _appSettings, pOrg, pGroup);

    return Response.ok(new ProfilerEntryView(entry, canViewPii, shouldClassifyOp)).build();
  }

  @GET
  @Hidden
  @Path("/rawByHash/{groupId}/{queryShapeHash}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Auth(endpointAction = "epa.project.QueryProfilerResource.getRawLogByQueryShapeHash.GET")
  public Response getRawLogByQueryShapeHash(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final String pGroupId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("analyticHostIds[]") final List<String> pAnalyticHostIds,
      @QueryParam("replicaStates[]") final List<String> pReplicaStates,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @PathParam("queryShapeHash") final String pQueryShapeHash)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrg, pGroup, false, RegionalEndpoint.V3_PROFILER_RAW_LOG_BY_QUERY_HASH);

    final boolean canViewPii = _authzSvc.hasPiiViewAccess(pUser, pGroup);

    if (pHostIds.isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    if (pQueryShapeHash == null) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    if (pHostIds.stream().anyMatch(hostId -> !ValidationUtils.isValidHostName(hostId))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var hosts = _hostSvc.findHostsByIds(pGroup.getId(), pHostIds, false);
    if (!_hostClusterLifecycleSvc.userCanAccessHosts(pUser, pGroup, hosts)) {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final boolean isHistoricalReplicaStateEnabled =
        isFeatureFlagEnabled(FeatureFlag.HISTORICAL_REPLICA_STATE, _appSettings, pOrg, pGroup);
    if (isHistoricalReplicaStateEnabled && pReplicaStates != null) {
      if (pReplicaStates.stream()
          .anyMatch(replicaState -> !ValidationUtils.isValidReplicaState(replicaState))) {
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Host Type");
      }

      // if we filter by analytic, we need analytic host ids
      final boolean hasAnalyticHostIds = pAnalyticHostIds != null && !pAnalyticHostIds.isEmpty();
      if (pReplicaStates.contains("ANALYTIC") && !hasAnalyticHostIds) {
        throw new SvcException(
            INVALID_PARAMETER,
            "Analytic host ids are required when filtering by analytic and primary/secondary");
      }
    }

    final boolean shouldClassifyOp =
        isFeatureFlagEnabled(
            FeatureFlag.CLUSTER_CENTRIC_QUERY_PROFILER_FOLLOWUPS, _appSettings, pOrg, pGroup);

    final int abbrvLogLimit =
        _appSettings.getIntProp(
            SLOW_QUERY_LOG_FROM_QUERY_SHAPE_HASH_LIMIT_KEY,
            DEFAULT_SLOW_QUERY_LOG_FROM_QUERY_SHAPE_HASH_LIMIT);
    final List<AbbrvSlowQueryLogEntry> abbrvLogs =
        _queryProfilerSvc
            .getEntriesByQueryShapeHash(
                pGroup.getId(),
                pHostIds,
                pAnalyticHostIds,
                pReplicaStates,
                List.of(pQueryShapeHash),
                pSince,
                pUntil,
                abbrvLogLimit,
                isHistoricalReplicaStateEnabled)
            .stream()
            .limit(abbrvLogLimit)
            .toList();

    final List<ProfilerEntryView> rawLogs = new ArrayList<>();
    abbrvLogs.forEach(
        log -> {
          try {
            final SlowQueryLogEntry slowQueryLog =
                _slowQueryLogSvc.findSlowQueryLogEntryById(
                    log.getRawLogId(), log.getTimestamp().toInstant());
            rawLogs.add(new ProfilerEntryView(slowQueryLog, canViewPii, shouldClassifyOp));
          } catch (SvcException e) {
            LOG.warn(
                "Failed to get SlowQueryLogEntry with id={} timestamp={}",
                log.getRawLogId(),
                log.getTimestamp(),
                e);
            // continue, try to get others
          }
        });

    return Response.ok(rawLogs).build();
  }
}
