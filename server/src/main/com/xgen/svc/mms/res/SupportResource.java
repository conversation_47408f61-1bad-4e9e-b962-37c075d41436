package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.email._public.view.SendEmailResult;
import com.xgen.cloud.email._public.view.SupportEmailParams;
import com.xgen.cloud.externalanalytics._public.model.CloudManagerOrganizationRequestSupportEvent;
import com.xgen.cloud.externalanalytics._public.model.SegmentEvent;
import com.xgen.cloud.externalanalytics._public.model.SupportPortalRedirectEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.sfsc._public.svc.SalesforceServiceCloudSyncTrackSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.PayingOrg;
import com.xgen.svc.mms.svc.billing.OrgPlanSvc;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import com.xgen.svc.mms.svc.support.emails.SupportEmailsSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.net.URI;
import java.util.Date;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@Path("/support")
public class SupportResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(SupportResource.class);
  static final String SYNC_IN_PROCESS_USER_MESSAGE =
      "Try again in 5 minutes.  Support Portal account initializing.";
  private static final String ORG_TRACKED_USER_MESSAGE = "Your org has Support Portal access.";

  static final String CAN_REDIRECT_TO_SUPPORT_PORTAL = "canRedirectToSupportPortal";
  static final String CAN_REDIRECT_TO_SUPPORT_PORTAL_MESSAGE = "canRedirectToSupportPortalMessage";

  private final AppSettings appSettings;
  private final SalesforceServiceCloudSyncTrackSvc salesforceServiceCloudSyncTrackSvc;
  private final AuditSvc auditSvc;
  private final PayingOrgSvc payingOrgSvc;
  private final OrgPlanSvc orgPlanSvc;
  private final SupportEmailsSvc supportEmailsSvc;
  private final SegmentEventSvc segmentEventSvc;

  private static final String REDIRECT_SUPPORT_URL_COMMERCIAL = "https://support.mongodb.com/help";
  private static final String REDIRECT_SUPPORT_URL_GOV = "https://support.mongodbgov.com/help";
  private static final String REFERER_QUERY_PARAM = "?referrer=";

  @Inject
  public SupportResource(
      final AppSettings appSettings,
      final SalesforceServiceCloudSyncTrackSvc salesforceServiceCloudSyncTrackSvc,
      final AuditSvc auditSvc,
      final PayingOrgSvc payingOrgSvc,
      final OrgPlanSvc orgPlanSvc,
      final SupportEmailsSvc supportEmailsSvc,
      final SegmentEventSvc segmentEventSvc) {
    this.appSettings = appSettings;
    this.salesforceServiceCloudSyncTrackSvc = salesforceServiceCloudSyncTrackSvc;
    this.auditSvc = auditSvc;
    this.payingOrgSvc = payingOrgSvc;
    this.orgPlanSvc = orgPlanSvc;
    this.supportEmailsSvc = supportEmailsSvc;
    this.segmentEventSvc = segmentEventSvc;
  }

  @GET
  @Path("/canRedirectToSupportPortal/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SupportResource.getCanRedirectToSupportPortal.GET")
  public Response getCanRedirectToSupportPortal(
      @Context final Organization org, @Context final AppUser appUser) {
    if (getSfscSyncTrackSvc().checkIsTracked(org.getId())) {
      if (getSfscSyncTrackSvc().isInitialSyncInProcess(org.getId())) {
        LOG.debug("Initial SFSC Sync still in process for orgId={}", org.getId());
        return SimpleApiResponse.ok()
            .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL, false)
            .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL_MESSAGE, SYNC_IN_PROCESS_USER_MESSAGE)
            .build();
      }
      return SimpleApiResponse.ok()
          .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL, true)
          .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL_MESSAGE, ORG_TRACKED_USER_MESSAGE)
          .build();
    } else {
      // if org is not tracked and CM then we want to start the sync immediately
      if (org.isCloudManager()) {
        triggerSalesforceSync(org, appUser, CloudManagerOrganizationRequestSupportEvent.EVENT_TYPE);
        return SimpleApiResponse.ok()
            .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL, false)
            .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL_MESSAGE, SYNC_IN_PROCESS_USER_MESSAGE)
            .build();
      }
      // if its an org is not CM, there is an edge case where a customer had a support plan before
      // we migrated to the current approach and they were not synced to SFSC, so we need to force
      // the sync here
      final OrgPlan plan;
      if (payingOrgSvc.isLinkedToPayingOrg(org.getId())) {
        final Optional<PayingOrg> payingOrgOptional =
            payingOrgSvc.findByLinkedOrgIdEventuallyConsistent(org.getId());
        plan =
            payingOrgOptional
                .map(
                    payingOrg ->
                        orgPlanSvc.getOrgPlanForDate(payingOrg.getPayingOrgId(), new Date()))
                .orElse(null);
      } else {
        plan = orgPlanSvc.getOrgPlanForDate(org.getId(), new Date());
      }
      // if plan is not plain atlas plan then we know they should have support
      if (plan != null && plan.hasSupport()) {
        triggerSalesforceSync(org, appUser, SupportPortalRedirectEvent.EVENT_TYPE);
        return SimpleApiResponse.ok()
            .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL, false)
            .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL_MESSAGE, SYNC_IN_PROCESS_USER_MESSAGE)
            .build();
      }
    }

    return SimpleApiResponse.ok()
        .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL, false)
        .customField(CAN_REDIRECT_TO_SUPPORT_PORTAL_MESSAGE, SYNC_IN_PROCESS_USER_MESSAGE)
        .build();
  }

  @GET
  @Path("/redirectToSupportPortal/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SupportResource.redirectToSupportPortal.GET")
  public Response redirectToSupportPortal(
      @Context AppUser pAppUser,
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse)
      throws Exception {
    RequestParams params = getRequestParams(pRequest);
    if (params.getAppUser() != null) {
      LOG.debug("Redirecting user={} to SFSC Support Portal", pAppUser.getId());
      final String referer =
          pRequest.getHeader("Referer") == null
              ? ""
              : REFERER_QUERY_PARAM + pRequest.getHeader("Referer");
      return getAppSettings().getNDSGovUSEnabled()
          ? tempRedirect(REDIRECT_SUPPORT_URL_GOV + referer)
          : tempRedirect(REDIRECT_SUPPORT_URL_COMMERCIAL + referer);
    }
    return SimpleApiResponse.ok()
        .customField("message", "An error occurred, please retry action in a few minutes")
        .build();
  }

  @GET
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false)
  public Response supportLoggedOut(
      @Context final HttpServletRequest pRequest, @Context final HttpServletResponse pResponse)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);

    if (!appSettings.getBoolProp("mms.helpAndSupportPage.enabled")) {
      return Response.seeOther(new URI(appSettings.getDocsUrl())).build();
    }

    if (params.getAppUser() != null && params.getAppUser().getCurrentGroupId() != null) {
      return tempRedirect("/v2/" + params.getAppUser().getCurrentGroupId() + "#/info/support");
    } else {
      return tempRedirect("/user/login?n=%2Fsupport");
    }
  }

  @POST
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_PROACTIVE_SUPPORT_ADMIN},
      groupSource = GroupSource.NONE)
  @Path("/emails")
  @Auth(endpointAction = "epa.global.SupportResource.sendSupportEmails.POST")
  public Response sendSupportEmails(final SupportEmailParams pSupportEmailParams) {
    final SendEmailResult result = supportEmailsSvc.sendSupportEmails(pSupportEmailParams);
    if (result.getValidationError() != null) {
      return ApiErrorCode.VALIDATION_ERROR.response(false, result.getValidationError());
    }
    return new ApiResponseBuilder(false).content(result).ok().build();
  }

  private void triggerSalesforceSync(
      final Organization org, final AppUser appUser, final String segmentEventType) {
    final SegmentEvent.Builder<?> segmentEventBuilder =
        switch (segmentEventType) {
          case CloudManagerOrganizationRequestSupportEvent.EVENT_TYPE ->
              CloudManagerOrganizationRequestSupportEvent.builder();
          case SupportPortalRedirectEvent.EVENT_TYPE -> SupportPortalRedirectEvent.builder();
          default -> throw ApiErrorCode.INVALID_ATTRIBUTE.exception(false, segmentEventType);
        };

    final SegmentEvent segmentEvent =
        segmentEventBuilder
            .organizationId(org.getId())
            .userId(appUser.getId())
            .groupId(appUser.getCurrentGroupId())
            .build();

    segmentEventSvc.submitEvent(segmentEvent);
    final BillingAudit.Builder syncEventBuilder =
        new BillingAudit.Builder(BillingEvent.Type.INITIATE_SALESFORCE_SERVICE_CLOUD_SYNC);
    syncEventBuilder.orgId(org.getId());
    getAuditSvc().saveAuditEvent(syncEventBuilder.build());
  }

  SalesforceServiceCloudSyncTrackSvc getSfscSyncTrackSvc() {
    return salesforceServiceCloudSyncTrackSvc;
  }

  AuditSvc getAuditSvc() {
    return auditSvc;
  }

  public AppSettings getAppSettings() {
    return appSettings;
  }
}
