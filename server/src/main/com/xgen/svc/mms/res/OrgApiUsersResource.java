package com.xgen.svc.mms.res;

import static java.util.stream.Collectors.toList;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiuser._public.svc.ApiUserSvc;
import com.xgen.cloud.authz.core._public.client.ActorClient;
import com.xgen.cloud.authz.core._public.utils.ConversionUtils;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authz._public.view.PolicyAssignmentView;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._private.view.ApiAccessListView;
import com.xgen.cloud.user._public.model.ApiUser;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserAllowList;
import com.xgen.cloud.user._public.svc.UserAllowListSvc;
import com.xgen.module.liveimport.svc.LiveMigrationInternalAccessListSvc;
import com.xgen.svc.mms.res.view.user.CreateApiUserView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.HashSet;
import java.util.List;
import org.bson.types.ObjectId;

/** Where Programmatic Access Keys (PAKs) are managed */
@Path("/orgs/{orgId}/apiUsers")
@Singleton
public class OrgApiUsersResource extends BaseResource {

  private final ApiUserSvc _apiUserSvc;
  private final ConfigLimitSvc _configLimitSvc;
  private final OrganizationSvc _organizationSvc;
  private final UserAllowListSvc _userAllowListSvc;
  private final LiveMigrationInternalAccessListSvc _liveMigrationInternalAccessListSvc;
  private final ActorClient _actorClient;
  private final FeatureFlagSvc _featureFlagSvc;
  private final AuthzSvc _authzSvc;

  @Inject
  public OrgApiUsersResource(
      final ApiUserSvc pApiUserSvc,
      final ConfigLimitSvc pConfigLimitSvc,
      final OrganizationSvc pOrganizationSvc,
      final UserAllowListSvc pUserAllowListSvc,
      final LiveMigrationInternalAccessListSvc pLiveMigrationInternalAccessListSvc,
      final ActorClient pActorClient,
      final FeatureFlagSvc pFeatureFlagSvc,
      final AuthzSvc pAuthzSvc) {
    _apiUserSvc = pApiUserSvc;
    _configLimitSvc = pConfigLimitSvc;
    _organizationSvc = pOrganizationSvc;
    _userAllowListSvc = pUserAllowListSvc;
    _liveMigrationInternalAccessListSvc = pLiveMigrationInternalAccessListSvc;
    _actorClient = pActorClient;
    _featureFlagSvc = pFeatureFlagSvc;
    _authzSvc = pAuthzSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.getOrgApiUsers.GET")
  public Response getOrgApiUsers(
      @Context final Organization pOrganization, @Context final AppUser pRequestUser) {
    final List<ApiUser> apiUsers =
        getApiUserSvc()
            .findApiUsersByOrgId(pOrganization.getId(), _authzSvc.hasAnyGlobalRole(pRequestUser))
            .stream()
            .map(ApiUser::redact)
            .collect(toList());

    return Response.ok().entity(apiUsers).build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.createApiUser.POST")
  public Response createApiUser(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      final CreateApiUserView pView)
      throws SvcException {

    if (!pView.getRoles().stream().allMatch(Role::isOrgSpecific)) {
      throw new SvcException(AppUserErrorCode.INVALID_ROLE_IN_ORG);
    }

    final String username = getApiUserSvc().generateApiUserUsername();

    getConfigLimitSvc().validateMaxAPIUsersPerOrg(1, pOrganization.getId());

    final ApiUser apiUser =
        getApiUserSvc()
            .createApiUser(
                username,
                pView.getDescription(),
                pOrganization,
                null,
                pView.getRoles(),
                pAuditInfo);

    if (_featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.FINE_GRAINED_AUTH, pOrganization, null)) {
      addPolicyAssignmentsToApiUser(apiUser.getUserId().toString(), pView.getPolicyAssignments());
    }

    return Response.ok().entity(apiUser).build();
  }

  private void addPolicyAssignmentsToApiUser(
      final String actorId, final List<PolicyAssignmentView> policyAssignmentViews)
      throws AuthzServiceClientException {
    _actorClient.editPolicyAssignments(
        actorId,
        policyAssignmentViews.stream()
            .map(ConversionUtils::mapPolicyAssignmentViewToMessage)
            .toList(),
        List.of());
  }

  @PATCH
  @Path("/{apiUserId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.updateApiUser.PATCH")
  public Response updateApiUser(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("apiUserId") final ObjectId pApiUserId,
      final CreateApiUserView pView)
      throws SvcException {

    final ApiUser apiUser = getOrgApiUser(pApiUserId, pOrganization.getId());

    if (_featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.FINE_GRAINED_AUTH, pOrganization, null)) {
      setPolicyAssignmentsToApiUser(apiUser.getUserId().toString(), pView.getPolicyAssignments());
    } else {
      getApiUserSvc()
          .setOrgRoles(apiUser, pOrganization, new HashSet<>(pView.getRoles()), pAuditInfo);
    }

    getApiUserSvc().updateDescription(apiUser, pView.getDescription(), pAuditInfo);

    final ApiUser updatedApiUser = getApiUserSvc().findByUserId(apiUser.getUserId());

    return Response.ok().entity(updatedApiUser.redact()).build();
  }

  private void setPolicyAssignmentsToApiUser(
      final String actorId, final List<PolicyAssignmentView> policyAssignmentViews)
      throws AuthzServiceClientException {
    _actorClient.setPolicyAssignments(
        actorId,
        policyAssignmentViews.stream()
            .map(ConversionUtils::mapPolicyAssignmentViewToMessage)
            .toList());
  }

  @DELETE
  @Path("/{apiUserId}")
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.deleteApiUser.DELETE")
  public Response deleteApiUser(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("apiUserId") final ObjectId pApiUserId)
      throws SvcException {
    final ApiUser apiUser = getOrgApiUser(pApiUserId, pOrganization.getId());
    getApiUserSvc().deleteApiUser(apiUser, pAuditInfo);
    getApiUserSvc().deleteAccessListEntries(apiUser, pAuditInfo);
    return Response.noContent().build();
  }

  @GET
  @Path("/{apiUserId}/publicApiAccessList")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.listPublicApiAccessList.GET")
  public Response listPublicApiAccessList(
      @Context final Organization pOrganization,
      @Context final AppUser pRequestUser,
      @PathParam("apiUserId") final ObjectId pApiUserId)
      throws SvcException {
    final ApiUser apiUser = getOrgApiUser(pApiUserId, pOrganization.getId());

    final List<UserAllowList> result = getUserAllowListSvc().findByUserId(apiUser.getUserId());
    final List<UserAllowList> filteredResult =
        _liveMigrationInternalAccessListSvc.getFilteredAccessList(pOrganization, apiUser, result);
    final List<ApiAccessListView> filteredView =
        filteredResult.stream().map(ApiAccessListView::new).collect(toList());
    return Response.ok(filteredView).build();
  }

  @POST
  @Path("/{apiUserId}/publicApiAccessList")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.addPublicApiAccessListEntry.POST")
  public Response addPublicApiAccessListEntry(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("apiUserId") final ObjectId pUserId,
      @FormParam("ipAddress") final String pIpAddress)
      throws SvcException {

    final ApiUser apiUser = getOrgApiUser(pUserId, pOrganization.getId());

    final UserAllowList userAllowList =
        getApiUserSvc().addAllowListEntry(apiUser, pIpAddress, pAuditInfo);
    final ApiAccessListView view = new ApiAccessListView(userAllowList);
    return Response.ok(view).build();
  }

  @DELETE
  @Path("/{apiUserId}/publicApiAccessList/{accessListEntryId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.deletePublicApiAccessListEntry.DELETE")
  public Response deletePublicApiAccessListEntry(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("apiUserId") final ObjectId pApiUserId,
      @PathParam("accessListEntryId") final ObjectId pAccessListEntryId)
      throws SvcException {

    final ApiUser apiUser = getOrgApiUser(pApiUserId, pOrganization.getId());

    getApiUserSvc().deleteAllowListEntry(apiUser, pAccessListEntryId, pAuditInfo);

    return Response.noContent().build();
  }

  @PATCH
  @Path("/{apiUserId}/orgUiAccessListInheritance")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgApiUsersResource.updateOrgUiAccessListInheritance.PATCH")
  public Response updateOrgUiAccessListInheritance(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("apiUserId") final ObjectId pUserId,
      @FormParam("shouldApplyOrgUiAccessListForApi")
          final boolean pShouldApplyOrgUiAccessListForApi)
      throws SvcException {
    final ApiUser apiUser = getOrgApiUser(pUserId, pOrganization.getId());
    _apiUserSvc.setShouldApplyOrgUiAccessListForApi(
        apiUser, pOrganization.getId(), pShouldApplyOrgUiAccessListForApi, pAuditInfo);
    return Response.ok().build();
  }

  private ApiUser getOrgApiUser(final ObjectId pApiUserId, final ObjectId pOrgId)
      throws SvcException {
    final ApiUser apiUser = getApiUserSvc().findByUserId(pApiUserId);

    if (apiUser == null) {
      throw new SvcException(AppUserErrorCode.API_KEY_NOT_FOUND);
    }

    if (!apiUser.getOrgId().equals(pOrgId)) {
      throw new SvcException(AppUserErrorCode.API_KEY_NOT_IN_ORG);
    }

    return apiUser;
  }

  private ApiUserSvc getApiUserSvc() {
    return _apiUserSvc;
  }

  private ConfigLimitSvc getConfigLimitSvc() {
    return _configLimitSvc;
  }

  private OrganizationSvc getOrganizationSvc() {
    return _organizationSvc;
  }

  private UserAllowListSvc getUserAllowListSvc() {
    return _userAllowListSvc;
  }
}
