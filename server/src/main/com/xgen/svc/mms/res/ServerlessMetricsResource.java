/* (C) Copyright 2015, MongoDB, Inc. */

package com.xgen.svc.mms.res;

import static com.xgen.cloud.monitoring.metrics._public.model.StatusBuilderOptions.Opcounters;
import static com.xgen.svc.mms.model.ServerlessChartSelect.DEFAULT_CHARTS;
import static com.xgen.svc.mms.util.ChartsUtils.createModelCharts;
import static java.util.Collections.singletonList;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentSupportSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.sharedpool._public.sharedpool.SharedWorkerPool;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.metrics._public.model.Chart;
import com.xgen.cloud.monitoring.metrics._public.model.ChartBuilder;
import com.xgen.cloud.monitoring.metrics._public.model.MetricDataSnapshot;
import com.xgen.cloud.monitoring.metrics._public.model.NumericAggregationFunction;
import com.xgen.cloud.monitoring.metrics._public.model.PartialClusterDescription;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessClusterMeasurement;
import com.xgen.cloud.monitoring.metrics._public.svc.MetricsSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.rrd.RetentionPolicySvc;
import com.xgen.cloud.monitoring.metrics._public.svc.serverless.ServerlessMetricsSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.dao.ServerlessChartSelectDao;
import com.xgen.svc.mms.res.view.metrics.AvailableChartsServerlessView;
import com.xgen.svc.mms.res.view.metrics.BucketableMetricResponse;
import com.xgen.svc.mms.res.view.metrics.ServerlessMetricsResponse;
import com.xgen.svc.mms.res.view.metrics.ZoomFilteredRetentionViewStore;
import com.xgen.svc.mms.svc.billing.PlanSvc;
import com.xgen.svc.mms.svc.host.LegacyHostSvc;
import com.xgen.svc.mms.svc.metrics.Request;
import com.xgen.svc.nds.serverless.util.ServerlessAccessUtil;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.json.JSONObject;

@Path("/metrics")
@Singleton
public class ServerlessMetricsResource extends BaseMetricsResource {

  private static final boolean IS_SERVERLESS_INSTANCE = true;

  private final ServerlessChartSelectDao _serverlessChartSelectDao;

  private final NDSClusterSvc _ndsClusterSvc;
  private final ServerlessAccessUtil _serverlessAccessUtil;
  private final ServerlessMetricsSvc _serverlessMetricsSvc;

  @Inject
  public ServerlessMetricsResource(
      final HostSvc pHostSvc,
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final MetricsSvc pMetricsSvc,
      final LegacyHostSvc pLegacyHostSvc,
      final ServerlessMetricsSvc pServerlessMetricsSvc,
      final PlanSvc pPlanSvc,
      final RetentionPolicySvc pRetentionPolicySvc,
      final AuthzSvc pAuthzSvc,
      final ServerlessChartSelectDao pServerlessChartSelectDao,
      final NDSClusterSvc pNdsClusterSvc,
      final ServerlessAccessUtil pServerlessAccessUtil,
      final AutomationAgentSupportSvc pAutomationAgentSupportSvc,
      final SharedWorkerPool pSharedWorkerPool,
      final FeatureFlagSvc pFeatureFlagSvc) {
    super(
        pMetricsSvc,
        pPlanSvc,
        pRetentionPolicySvc,
        pAuthzSvc,
        pHostSvc,
        pHostClusterLifecycleSvc,
        pLegacyHostSvc,
        pAutomationAgentSupportSvc,
        pSharedWorkerPool,
        pFeatureFlagSvc);
    _serverlessChartSelectDao = pServerlessChartSelectDao;
    _ndsClusterSvc = pNdsClusterSvc;
    _serverlessMetricsSvc = pServerlessMetricsSvc;
    _serverlessAccessUtil = pServerlessAccessUtil;
  }

  @GET
  @Path("/v1/groups/{groupId}/serverless/{uniqueId}/available")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces(MediaType.APPLICATION_JSON)
  @Auth(endpointAction = "epa.project.ServerlessMetricsResource.getAvailableServerlessCharts.GET")
  public Response getAvailableServerlessCharts(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("uniqueId") final String pUniqueId)
      throws JSONException {
    final RequestParams params = getRequestParams(pRequest);
    final AppUser appUser = params.getAppUser();

    final Opcounters effectiveOpcountersChartType =
        appUser.getSeparateOpcounterCharts() ? Opcounters.SEPARATE : Opcounters.COMBINED;

    // Serverless charts
    final List<ChartBuilder<ServerlessClusterMeasurement, PartialClusterDescription>> metrics =
        ServerlessClusterMeasurement.getAllServerlessChartBuilders(
            effectiveOpcountersChartType, appUser);
    return Response.ok(AvailableChartsServerlessView.from(metrics, appUser)).build();
  }

  @GET
  @Path("/serverless/defaultChartIds/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessMetricsResource.serverlessDefaultChartIds.GET")
  public Response serverlessDefaultChartIds(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @PathParam("groupId") final String pGroupId)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    final List<String> selectedCharts =
        fetchSavedServerlessChartIds(params.getAppUser(), new ObjectId(pGroupId));
    return Response.ok(new JSONObject().put("selectedCharts", selectedCharts).toString()).build();
  }

  @POST
  @Path("/serverlessChartsSelect/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessMetricsResource.serverlessChartsPage.POST")
  public String serverlessChartsPage(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @PathParam("groupId") final String pGroupId,
      final List<String> pSelectedCharts)
      throws Exception {
    if (pSelectedCharts == null) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }
    final RequestParams params = getRequestParams(pRequest);
    _serverlessChartSelectDao.upsert(
        params.getAppUser().getId(), new ObjectId(pGroupId), pSelectedCharts);
    return EMPTY_JSON_OBJECT;
  }

  @GET
  @Path("/v1/groups/{groupId}/serverless/{clusterUniqueId}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces(MediaType.APPLICATION_JSON)
  @Auth(endpointAction = "epa.project.ServerlessMetricsResource.getServerlessMetrics.GET")
  public Response getServerlessMetrics(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @QueryParam("granularity") final Long pSamplePeriodMillis,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("retention") final Long pRetention,
      @QueryParam("aggregator") final String aggregatorMethod,
      @QueryParam("bucketed") final boolean pBucketed)
      throws SvcException, JSONException {
    final RequestParams params = getRequestParams(pRequest);
    params.setClusterUniqueId(pClusterUniqueId);
    final Optional<ClusterDescription> optClusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterUniqueId);
    if (optClusterDescription.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.SERVERLESS_INSTANCE_NOT_FOUND_FOR_UNIQUE_ID, pClusterUniqueId);
    }
    final ClusterDescription clusterDescription = optClusterDescription.get();

    _serverlessAccessUtil.assertAccessToServerlessCluster(
        pGroup, pClusterUniqueId, clusterDescription, pUser);

    final PartialClusterDescription partialClusterDescription =
        new PartialClusterDescription(
            pGroup.getId(),
            clusterDescription.getDeploymentClusterName(),
            clusterDescription.getUniqueId(),
            clusterDescription.getMongoDBUriHosts(),
            clusterDescription.getMongoDBVersion(),
            clusterDescription.getLegacyProvider(),
            clusterDescription.getRegionName());

    final Request request =
        createAutoRequest(
            pOrganization,
            pGroup,
            pRetention,
            pSince,
            pUntil,
            pSamplePeriodMillis,
            pUser,
            IS_SERVERLESS_INSTANCE);

    final long samplePeriodMillis = request.getSamplePeriod().toMillis();
    final long until = request.getUntil().toEpochMilli();
    final long since = request.getSince().toEpochMilli();
    final long durationMillis = request.getDuration().toMillis();
    final ZoomFilteredRetentionViewStore retentionPassFailStore =
        request.getRetentionPassFailStore();

    // ServerlessClusterMeasurement tenantId is <reversed_uniqueId>
    final String tenantId = DriverUtils.reverseObjectId(pClusterUniqueId);

    final MetricDataSnapshot<ServerlessClusterMeasurement> snapshot =
        _serverlessMetricsSvc.findServerlessClusterMeasurements(
            pGroup, tenantId, samplePeriodMillis, until, durationMillis);

    // Build status charts
    final Opcounters effectiveOpcountersChartType =
        pUser.getSeparateOpcounterCharts() ? Opcounters.SEPARATE : Opcounters.COMBINED;
    final List<ChartBuilder<ServerlessClusterMeasurement, PartialClusterDescription>>
        statusChartBuilders =
            ServerlessClusterMeasurement.getAllServerlessChartBuilders(
                effectiveOpcountersChartType, pUser);

    final NumericAggregationFunction aggregator = findAggregatorFunction(aggregatorMethod);

    final List<Chart> statusCharts =
        createModelCharts(
            partialClusterDescription, pUser, snapshot, statusChartBuilders, aggregator);

    final BucketableMetricResponse<PartialClusterDescription> statusResponse =
        new BucketableMetricResponse<>(
            pGroup, partialClusterDescription, snapshot.getTimeline(), statusCharts, "metrics");

    final ServerlessMetricsResponse response =
        new ServerlessMetricsResponse(pGroup, partialClusterDescription, statusResponse);

    final MetricResponseWithMetadata entireResponse =
        new MetricResponseWithMetadata(
            retentionPassFailStore,
            durationMillis,
            since,
            until,
            samplePeriodMillis,
            pRetention == null,
            response);

    return Response.ok(
            (pBucketed
                    ? entireResponse.toBucketedJSON(samplePeriodMillis)
                    : entireResponse.toJSON())
                .toString())
        .build();
  }

  @GET
  @Path("/v1/groups/{groupId}/serverless/{clusterUniqueId}/metrics/{chartId}")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Produces(MediaType.APPLICATION_JSON)
  @Auth(endpointAction = "epa.project.ServerlessMetricsResource.getServerlessMetricsForChart.GET")
  public Response getServerlessMetricsForChart(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @PathParam("chartId") final String pChartId,
      @QueryParam("granularity") final Long pSamplePeriodMillis,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("retention") final Long pRetention,
      @QueryParam("aggregator") final String aggregatorMethod,
      @QueryParam("bucketed") final boolean pBucketed,
      @QueryParam("maxDataPoints") final int pMaxDataPoints)
      throws SvcException, JSONException {
    final RequestParams params = getRequestParams(pRequest);
    params.setClusterUniqueId(pClusterUniqueId);
    final Optional<ClusterDescription> optClusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterUniqueId);
    if (optClusterDescription.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.SERVERLESS_INSTANCE_NOT_FOUND_FOR_UNIQUE_ID, pClusterUniqueId);
    }
    final ClusterDescription clusterDescription = optClusterDescription.get();

    _serverlessAccessUtil.assertAccessToServerlessCluster(
        pGroup, pClusterUniqueId, clusterDescription, pUser);

    final PartialClusterDescription partialClusterDescription =
        new PartialClusterDescription(
            pGroup.getId(),
            clusterDescription.getDeploymentClusterName(),
            clusterDescription.getUniqueId(),
            clusterDescription.getMongoDBUriHosts(),
            clusterDescription.getMongoDBVersion(),
            clusterDescription.getLegacyProvider(),
            clusterDescription.getRegionName());

    final Request request =
        createAutoRequest(
            pOrganization,
            pGroup,
            pRetention,
            pSince,
            pUntil,
            pMaxDataPoints,
            pSamplePeriodMillis,
            pUser,
            IS_SERVERLESS_INSTANCE);

    final long samplePeriodMillis = request.getSamplePeriod().toMillis();
    final long until = request.getUntil().toEpochMilli();
    final long since = request.getSince().toEpochMilli();
    final long durationMillis = request.getDuration().toMillis();
    final ZoomFilteredRetentionViewStore retentionPassFailStore =
        request.getRetentionPassFailStore();

    // ServerlessClusterMeasurement tenantId is <reversed_uniqueId>
    final String tenantId = DriverUtils.reverseObjectId(pClusterUniqueId);

    final MetricDataSnapshot<ServerlessClusterMeasurement> snapshot =
        _serverlessMetricsSvc.findServerlessClusterMeasurements(
            pGroup, tenantId, samplePeriodMillis, until, durationMillis);

    // Build status charts
    final Optional<ChartBuilder<ServerlessClusterMeasurement, PartialClusterDescription>>
        metricsChartBuilder =
            ServerlessClusterMeasurement.getAllServerlessChartBuilders(Opcounters.ALL, pUser)
                .stream()
                .filter(b -> pChartId.equals(b.getChartId()))
                .findFirst();

    final NumericAggregationFunction aggregator = findAggregatorFunction(aggregatorMethod);

    if (!metricsChartBuilder.isPresent()) {
      throw new SvcException(CommonErrorCode.NOT_FOUND, pChartId);
    }

    final ChartBuilder<ServerlessClusterMeasurement, PartialClusterDescription> chartBuilder =
        metricsChartBuilder.get();

    final List<Chart> charts =
        createModelCharts(
            partialClusterDescription,
            pUser,
            snapshot,
            singletonList(chartBuilder),
            aggregator,
            true);

    final BucketableMetricResponse<PartialClusterDescription> statusResponse =
        new BucketableMetricResponse<>(
            pGroup, partialClusterDescription, snapshot.getTimeline(), charts, "status");

    final ServerlessMetricsResponse response =
        new ServerlessMetricsResponse(pGroup, partialClusterDescription, statusResponse);

    final MetricResponseWithMetadata entireResponse =
        new MetricResponseWithMetadata(
            retentionPassFailStore,
            durationMillis,
            since,
            until,
            samplePeriodMillis,
            pRetention == null,
            response);

    return Response.ok(
            (pBucketed
                    ? entireResponse.toBucketedJSON(samplePeriodMillis)
                    : entireResponse.toJSON())
                .toString())
        .build();
  }

  private List<String> fetchSavedServerlessChartIds(
      final AppUser pAppUser, final ObjectId pGroupId) {
    final List<String> defaultChartIds = new ArrayList<>();

    // Load the users selected chart ids.
    final List<String> selectedChartIds =
        _serverlessChartSelectDao.getSelectedChartIdsForUser(pAppUser.getId(), pGroupId);

    if (selectedChartIds != null) {
      defaultChartIds.addAll(selectedChartIds);
    } else {
      defaultChartIds.addAll(DEFAULT_CHARTS);
      _serverlessChartSelectDao.upsert(pAppUser.getId(), pGroupId, defaultChartIds);
    }
    return defaultChartIds;
  }
}
