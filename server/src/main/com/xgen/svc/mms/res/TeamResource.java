package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.externalanalytics._public.model.TeamCreatedEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.form.TeamRenameForm;
import com.xgen.svc.mms.form.TeamView;
import com.xgen.svc.mms.form.TeamView.UserData;
import com.xgen.svc.mms.res.view.user.UserView;
import com.xgen.svc.mms.svc.TeamSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collections;
import java.util.List;
import org.bson.types.ObjectId;

@Path("/orgs/{orgId}/teams")
@Singleton
public class TeamResource extends BaseResource {

  private final TeamSvc _teamSvc;
  private final UserSvc _userSvc;
  private final InvitationSvc _invitationSvc;
  private final ConfigLimitSvc _configLimitSvc;

  private final SegmentEventSvc _segmentEventSvc;

  @Inject
  public TeamResource(
      final TeamSvc pTeamSvc,
      final UserSvc pUserSvc,
      final InvitationSvc pInvitationSvc,
      final ConfigLimitSvc pConfigLimitSvc,
      final SegmentEventSvc segmentEventSvc) {
    _teamSvc = pTeamSvc;
    _userSvc = pUserSvc;
    _invitationSvc = pInvitationSvc;
    _configLimitSvc = pConfigLimitSvc;
    _segmentEventSvc = segmentEventSvc;
  }

  @GET
  @Path("/{teamId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.get.GET")
  public Response get(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId)
      throws SvcException {
    final Team team = getTeamSvc().getTeam(pOrganization, pTeamId, pUser);
    return Response.ok().entity(getTeamSvc().convertToView(team)).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.getAllForOrg.GET")
  public Response getAllForOrg(
      @Context final AppUser pUser, @Context final Organization pOrganization) throws SvcException {
    final List<TeamView> teamViews = getTeamSvc().getViewsForOrg(pOrganization, pUser);
    return Response.ok().entity(teamViews).build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.create.POST")
  public Response create(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      final TeamView pView)
      throws SvcException {
    // Validate addition of 1 new team to the organization relative to config limits
    getConfigLimitSvc().validateMaxTeamsPerOrg(1, pOrganization.getId());
    getConfigLimitSvc().validateDefaultMaxUsersPerTeam(pView.getUsernames().size());
    final Team team =
        getTeamSvc().create(pView.getName(), pView.getUsernames(), pOrganization, pAuditInfo);
    emitTeamCreatedSegmentEvent(
        team,
        pView.getUsers().stream().map(UserData::getId).toList(),
        pAuditInfo.getAppUserId(),
        pOrganization);
    return Response.ok().entity(team).build();
  }

  /**
   * Emits a Team Created segment event
   *
   * @param team team that was created
   * @param userId ID of the user that initiated the team creation
   * @param organization Organization the team falls under
   */
  private void emitTeamCreatedSegmentEvent(
      Team team, List<ObjectId> teamUserIds, ObjectId userId, Organization organization) {
    _segmentEventSvc.submitEvent(
        TeamCreatedEvent.builder()
            .team(team)
            .teamUserIds(teamUserIds)
            .userId(userId)
            .organizationId(organization.getId())
            .build());
  }

  @DELETE
  @Path("/{teamId}")
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.delete.DELETE")
  public Response delete(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId)
      throws SvcException {
    getTeamSvc().delete(pOrganization, pTeamId, pAuditInfo);
    return Response.ok().build();
  }

  @PATCH
  @Path("/{teamId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.rename.PATCH")
  public Response rename(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId,
      final TeamRenameForm pForm)
      throws SvcException {
    getTeamSvc().rename(pTeamId, pForm.getName(), pOrganization, pAuditInfo);
    return Response.ok().entity(getTeamSvc().findById(pTeamId)).build();
  }

  @POST
  @Path("/validateUser/{teamId}")
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.validateUserInTeam.POST")
  public Response validateUserInTeam(
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId,
      @FormParam("username") final String pUsername)
      throws SvcException {
    getTeamSvc().validateUserCanJoinTeam(pOrganization, pUsername, pTeamId);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/{teamId}/users")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.addUser.POST")
  public Response addUser(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId,
      final UserView pUserView)
      throws SvcException {
    // Validate addition of 1 user to the team relative to config limits
    getConfigLimitSvc().validateMaxUsersPerTeam(1, pTeamId);

    if (pUserView.getUsername() == null) {
      throw new SvcException(AppUserErrorCode.INVALID_USERNAME);
    }

    getTeamSvc()
        .addUsers(
            Collections.singletonList(pUserView.getUsername()), pTeamId, pOrganization, pAuditInfo);

    final UserView userView = fetchUserView(pUserView.getUsername(), pOrganization.getId());
    return Response.ok().entity(userView).build();
  }

  @POST
  @Path("/addMultipleUsers/{teamId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.addMultipleUsers.POST")
  public Response addMultipleUsers(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId,
      final List<UserView> pUsers)
      throws SvcException {

    getConfigLimitSvc().validateMaxUsersPerTeam(pUsers.size(), pTeamId);
    final List<String> usernames = pUsers.stream().map(UserView::getUsername).toList();
    getTeamSvc().addUsers(usernames, pTeamId, pOrganization, pAuditInfo);
    return SimpleApiResponse.ok().build();
  }

  @DELETE
  @Path("/{teamId}/users/{username}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.TeamResource.removeUser.DELETE")
  public Response removeUser(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @PathParam("teamId") final ObjectId pTeamId,
      @PathParam("username") final String pUsername)
      throws SvcException {
    getTeamSvc().removeUser(pUsername, pTeamId, pOrganization, pAuditInfo);
    final UserView userView = fetchUserView(pUsername, pOrganization.getId());
    return Response.ok().entity(userView).build();
  }

  @DELETE
  @Path("/{teamId}/user")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response removeSelf(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @PathParam("teamId") final ObjectId pTeamId)
      throws SvcException {
    getTeamSvc().removeUser(pUser.getUsername(), pTeamId, pOrganization, pAuditInfo);
    return Response.ok()
        .entity(
            getUserView(getUserSvc().findByUsername(pUser.getUsername()), pOrganization.getId()))
        .build();
  }

  private UserView fetchUserView(final String pUsername, final ObjectId pOrgId)
      throws SvcException {
    final AppUser appUser = getUserSvc().findByUsername(pUsername);
    if (appUser != null && appUser.hasOrgId(pOrgId)) {
      return getUserView(appUser, pOrgId);
    } else {
      final List<Invitation> invitations =
          getInvitationSvc().findByOrgIdAndUsername(pOrgId, pUsername);
      if (invitations.isEmpty()) {
        throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
      }
      return UserView.createFromInvitations(invitations);
    }
  }

  private UserView getUserView(final AppUser pUser, final ObjectId pOrgId) {
    final UserView.Builder builder = new UserView.Builder();
    builder.username(pUser.getUsername());
    builder.lastAuth(pUser.getLastAuth());
    builder.timeZoneId(pUser.getTimeZoneDisplay());
    builder.created(pUser.getCreated());
    builder.status(UserView.Status.CONFIRMED);
    builder.multiFactorAuth(pUser.getMultiFactorAuth());
    builder.hasAccountMultiFactorAuth(pUser.hasAccountMultiFactorAuth());
    for (final Role role : pUser.getOrgRoles(pOrgId)) {
      builder.role(role);
    }
    for (final ObjectId teamId : pUser.getTeamIds()) {
      builder.teamId(teamId);
    }
    return builder.build();
  }

  private TeamSvc getTeamSvc() {
    return _teamSvc;
  }

  private UserSvc getUserSvc() {
    return _userSvc;
  }

  private InvitationSvc getInvitationSvc() {
    return _invitationSvc;
  }

  private ConfigLimitSvc getConfigLimitSvc() {
    return _configLimitSvc;
  }
}
