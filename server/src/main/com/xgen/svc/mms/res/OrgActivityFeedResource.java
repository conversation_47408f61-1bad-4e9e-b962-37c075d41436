package com.xgen.svc.mms.res;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventsContainer;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.svc.alert.ActivityFeedSvc;
import com.xgen.svc.nds.model.ui.DownloadEventsView;
import com.xgen.svc.nds.model.ui.OrgEventRequestView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.json.JSONObject;

@Path("/orgs/{orgId}/activity")
@Singleton
public class OrgActivityFeedResource extends BaseResource {
  private static final int DEFAULT_PAGE_SIZE = 20;

  private final ActivityFeedSvc _activityFeedSvc;
  private final AuthzSvc _authzSvc;
  private final AppSettings _appSettings;

  @Inject
  public OrgActivityFeedResource(
      final AuthzSvc pAuthzSvc,
      final ActivityFeedSvc pActivityFeedSvc,
      final AppSettings pAppSettings) {
    _authzSvc = pAuthzSvc;
    _activityFeedSvc = pActivityFeedSvc;
    _appSettings = pAppSettings;
  }

  @POST
  @Path("/list")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgActivityFeedResource.filterEvents.POST")
  public Response filterEvents(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      final OrgEventRequestView pOrgEventRequestView) {
    Integer pLimit = pOrgEventRequestView.getLimit();
    if (pLimit == null) {
      pLimit = DEFAULT_PAGE_SIZE;
    }
    final ObjectId pOrgId = pOrganization.getId();
    final boolean excludeHidden = !getAuthzSvc().isGlobalOrgReadOnly(pUser);

    final List<Event> orgActivityFeedEvents =
        getActivityFeedSvc()
            .getOrgEvents(
                pOrgId,
                pLimit,
                pOrgEventRequestView.getEventTypes(),
                pOrgEventRequestView.getStartTimestamp(),
                pOrgEventRequestView.getEndTimestamp(),
                excludeHidden,
                pOrgEventRequestView.getBeforeId(),
                pOrgEventRequestView.getAfterId());
    if (pOrgEventRequestView.getBeforeId() != null) {
      orgActivityFeedEvents.sort(Comparator.comparing(Event::getCreatedAt).reversed());
    }

    final EventsContainer ret = new EventsContainer();

    if (orgActivityFeedEvents.size() > 0) {
      final List<Event> eventsForPrevious =
          getActivityFeedSvc()
              .getOrgEvents(
                  pOrgId,
                  2,
                  pOrgEventRequestView.getEventTypes(),
                  pOrgEventRequestView.getStartTimestamp(),
                  pOrgEventRequestView.getEndTimestamp(),
                  excludeHidden,
                  orgActivityFeedEvents.get(0).getId(),
                  null);
      final List<Event> eventsForNext =
          getActivityFeedSvc()
              .getOrgEvents(
                  pOrgId,
                  2,
                  pOrgEventRequestView.getEventTypes(),
                  pOrgEventRequestView.getStartTimestamp(),
                  pOrgEventRequestView.getEndTimestamp(),
                  excludeHidden,
                  null,
                  orgActivityFeedEvents.get(orgActivityFeedEvents.size() - 1).getId());

      // set the nextId and prevId on events container
      if (eventsForPrevious.size() > 1) {
        ret.setPrev(eventsForPrevious.get(1).getId());
      }
      if (eventsForNext.size() > 1) {
        ret.setNext(eventsForNext.get(1).getId());
      }
    }

    ret.setTotal(orgActivityFeedEvents.size());

    getActivityFeedSvc()
        .logEventsUsage(
            "ORG_UI",
            pOrgId,
            orgActivityFeedEvents,
            pOrgEventRequestView.getStartTimestamp() == null
                ? null
                : new Date(pOrgEventRequestView.getStartTimestamp()),
            getAuthzSvc().isGlobalOrgReadOnly(pUser));
    final List<Event> redactedEvents =
        ActivityFeedSvc.getRedactedEvents(
            getAuthzSvc().isGlobalOrgReadOnly(pUser), orgActivityFeedEvents);
    ret.setData(redactedEvents);
    return Response.ok().entity(ret).build();
  }

  @POST
  @Path("/downloadEvents")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ORG_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.OrgActivityFeedResource.getEventsJson.POST")
  public Response getEventsJson(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      final OrgEventRequestView pOrgEventRequestView) {
    final ObjectId pOrgId = pOrganization.getId();
    final boolean excludeHidden = !getAuthzSvc().isGlobalOrgReadOnly(pUser);
    // Page indexed from zero, so increment it by 1
    final int numDocuments =
        pOrgEventRequestView.getPage() != null
            ? (pOrgEventRequestView.getPage() + 1) * DEFAULT_PAGE_SIZE
            : DEFAULT_PAGE_SIZE;
    // Fetch all documents up to the page that was last fetched
    final List<Event> orgActivityFeedEvents =
        getActivityFeedSvc()
            .getOrgEvents(
                pOrgId,
                numDocuments,
                pOrgEventRequestView.getEventTypes(),
                pOrgEventRequestView.getStartTimestamp(),
                pOrgEventRequestView.getEndTimestamp(),
                excludeHidden,
                null,
                null);

    final List<Event> redactedEvents =
        ActivityFeedSvc.getRedactedEvents(
            getAuthzSvc().isGlobalOrgReadOnly(pUser), orgActivityFeedEvents);

    return Response.ok(new DownloadEventsView(redactedEvents)).build();
  }

  @POST
  @Path("/filters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgActivityFeedResource.saveFilter.POST")
  public Response saveFilter(
      @Context final AppUser pUser,
      @Context final Organization pOrg,
      final OrgEventRequestView pOrgEventRequestView)
      throws SvcException {
    final Long startTimestamp = pOrgEventRequestView.getStartTimestamp();
    final Long endTimestamp = pOrgEventRequestView.getEndTimestamp();
    final List<String> eventTypes = pOrgEventRequestView.getEventTypes();
    final String url =
        String.format(
            "%s/v2#/org/%s/activity/list", _appSettings.getCentralUrl(), pOrg.getId().toString());

    final String redirectId =
        _activityFeedSvc.saveFilterAndReturnRedirectId(
            startTimestamp,
            endTimestamp,
            eventTypes,
            url,
            RoleSet.ORG_MEMBER,
            pOrg.getId(),
            pUser.getId());

    final Map<String, String> jsonMap = new HashMap<>();
    jsonMap.put("redirectId", redirectId);
    final JSONObject redirectIdJson = new JSONObject(jsonMap);
    return Response.ok().entity(redirectIdJson.toString()).build();
  }

  private ActivityFeedSvc getActivityFeedSvc() {
    return _activityFeedSvc;
  }

  public AuthzSvc getAuthzSvc() {
    return _authzSvc;
  }
}
