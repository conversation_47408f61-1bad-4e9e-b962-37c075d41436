package com.xgen.svc.mms.res.v2.sms;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.communication._public.client.CommunicationClient;
import com.xgen.svc.mms.res.v2.sms.requestBody.RemoveRestrictionRequestBody;
import com.xgen.svc.mms.res.v2.sms.requestBody.RestrictPhoneNumberRequestBody;
import com.xgen.svc.mms.res.v2.sms.requestBody.SearchPhoneNumbersRequestBody;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/v2/admin/sms")
@Singleton
public class SmsBlockingResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(SmsBlockingResource.class);

  private final CommunicationClient communicationClient;

  @Inject
  public SmsBlockingResource(final CommunicationClient communicationClient) {
    this.communicationClient = communicationClient;
  }

  @POST
  @Path("/searchPhoneNumberRestrictions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SmsBlockingResource.searchPhoneNumberRestrictions.POST")
  public Response searchPhoneNumberRestrictions(final SearchPhoneNumbersRequestBody requestBody) {
    final String searchTerm = requestBody.getSearchTerm();
    final Map<String, Object> response = new HashMap<>();
    response.put("restrictions", communicationClient.searchRestrictedPhoneNumbers(searchTerm));
    return Response.ok(response).build();
  }

  @POST
  @Path("/restrictPhoneNumber")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SmsBlockingResource.restrictPhoneNumber.POST")
  public Response restrictPhoneNumber(final RestrictPhoneNumberRequestBody requestBody) {
    final String phoneNumber = requestBody.getPhoneNumber();
    communicationClient.createPhoneNumberRestriction(phoneNumber);
    return Response.ok().build();
  }

  @POST
  @Path("/removeRestriction")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SmsBlockingResource.removeRestriction.POST")
  public Response removeRestriction(final RemoveRestrictionRequestBody requestBody) {
    final String phoneNumber = requestBody.getPhoneNumber();
    communicationClient.removePhoneNumberRestriction(phoneNumber);
    return Response.ok().build();
  }
}
