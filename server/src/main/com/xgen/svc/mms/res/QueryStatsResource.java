package com.xgen.svc.mms.res;

import static com.xgen.cloud.common.model._public.error.CommonErrorCode.BAD_REQUEST;
import static com.xgen.cloud.common.model._public.error.CommonErrorCode.INVALID_PARAMETER;
import static com.xgen.cloud.common.model._public.error.CommonErrorCode.SERVER_ERROR;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.xgen.cloud.access.activity._public.event.AccessEvent.Type;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.querystats._public.model.BasicQueryStatsSummary;
import com.xgen.cloud.monitoring.querystats._public.model.QueryShapeStatistic;
import com.xgen.cloud.monitoring.querystats._public.model.QueryStatsChartData;
import com.xgen.cloud.monitoring.querystats._public.model.QueryStatsDetails;
import com.xgen.cloud.monitoring.querystats._public.model.QueryStatsSummary;
import com.xgen.cloud.monitoring.querystats._public.svc.QueryStatsSvc;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.regionalization._public.RegionalizationValidator;
import com.xgen.cloud.nds.regionalization._public.RegionalizationValidator.RegionalEndpoint;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/v1/queryShapeInsights")
@Singleton
public class QueryStatsResource extends BaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(QueryStatsResource.class);

  private final AppSettings _settings;
  private final QueryStatsSvc _queryStatsSvc;
  private final AuthzSvc _authzSvc;
  private final RegionalizationValidator _regionalizationValidator;
  private final NDSClusterSvc _ndsClusterSvc;

  public static final Counter QUERY_STATS_INVALID_GROUP_TOTAL =
      Counter.build()
          .name("query_stats_invalid_group_total")
          .help(
              "Total number of times Query Stats endpoints have been called with the incorrect"
                  + " groupId")
          .register();

  public static final Gauge REJECTED_QUERY_SHAPES_COUNT =
      Gauge.build()
          .name("rejected_query_shapes_count")
          .help("Number of rejected query shapes returned by getRejectedQueryShapes endpoint")
          .register();

  private static final String SUMMARY_TABLE_LIMIT_KEY = "mms.monitoring.queryStats.ui.summaryLimit";
  private static final Integer DEFAULT_SUMMARY_TABLE_LIMIT = 125;

  @Inject
  public QueryStatsResource(
      final AppSettings appSettings,
      final QueryStatsSvc queryStatsSvc,
      final AuthzSvc authzSvc,
      final RegionalizationValidator regionalizationValidator,
      final NDSClusterSvc ndsClusterSvc) {
    _settings = appSettings;
    _queryStatsSvc = queryStatsSvc;
    _authzSvc = authzSvc;
    _regionalizationValidator = regionalizationValidator;
    _ndsClusterSvc = ndsClusterSvc;
  }

  @GET
  @Hidden
  @Path("/summary/{groupId}/{clusterName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.getSummary.GET")
  public Response getSummary(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @QueryParam("since") final Long since,
      @QueryParam("until") final Long until,
      @QueryParam("hostnamePorts[]") final List<String> hostnamePorts,
      @QueryParam("namespaces[]") final List<String> namespaces,
      @QueryParam("commands[]") final List<String> commands,
      @QueryParam("replicaStates[]") final List<String> replicaStates,
      @QueryParam("analyticHostnamePorts[]") final List<String> analyticHostnamePorts)
      throws Exception {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_SUMMARY);

    try {
      if (!group.getId().toString().equals(groupId)) {
        QUERY_STATS_INVALID_GROUP_TOTAL.inc();
        return SimpleApiResponse.forbidden().build();
      }

      LOG.info(
          "/queryShapeInsights/summary request received for {} {} {} {} {}",
          kv("groupId", groupId),
          kv("clusterName", clusterName),
          kv("since", since),
          kv("until", until),
          kv("hostnamePorts", hostnamePorts));

      final Map<String, QueryStatsSummary> hashToSummary =
          _queryStatsSvc.getQueryStatsSummaries(
              groupId,
              clusterName,
              since,
              until,
              Collections.emptyList(),
              Collections.emptyList(),
              hostnamePorts,
              Collections.emptyList(),
              commands,
              analyticHostnamePorts,
              _authzSvc.hasPiiViewAccess(user, group),
              true,
              _settings.getIntProp(SUMMARY_TABLE_LIMIT_KEY, DEFAULT_SUMMARY_TABLE_LIMIT));
      return Response.ok().entity(hashToSummary.values()).build();
    } catch (final Exception e) {
      LOG.error(
          "Fetching summaries for QueryStats failed for groupId:{}, clusterName:{}.",
          groupId,
          clusterName,
          e);
      return SimpleApiResponse.badRequest(CommonErrorCode.SERVER_ERROR).build();
    }
  }

  @GET
  @Hidden
  @Path("/points/{groupId}/{clusterName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.getPoints.GET")
  public Response getPoints(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @QueryParam("since") final Long since,
      @QueryParam("until") final Long until,
      @QueryParam("hashes[]") final List<String> queryShapeHashes,
      @QueryParam("series[]") final List<QueryShapeStatistic> series,
      @QueryParam("hostnamePorts[]") final List<String> hostnamePorts,
      @QueryParam("analyticHostnamePorts[]") final List<String> analyticHostnamePorts)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_POINTS);

    if (!group.getId().toString().equals(groupId)) {
      QUERY_STATS_INVALID_GROUP_TOTAL.inc();
      return SimpleApiResponse.forbidden().build();
    }

    LOG.info(
        "/queryShapeInsights/points request received for {} {} {} {} {} {} {}",
        kv("groupId", groupId),
        kv("clusterName", clusterName),
        kv("since", since),
        kv("until", until),
        kv("hostnamePorts", hostnamePorts),
        kv("hashes", queryShapeHashes),
        kv("series", series));

    try {
      final Map<QueryShapeStatistic, Map<String, QueryStatsChartData>> chartData =
          _queryStatsSvc.getQueryStatsPoints(
              groupId,
              clusterName,
              since,
              until,
              series,
              queryShapeHashes,
              hostnamePorts,
              analyticHostnamePorts,
              _authzSvc.hasPiiViewAccess(user, group),
              true);
      return Response.ok().entity(chartData).build();
    } catch (final Exception e) {
      LOG.info(
          "Fetching points for {} {} failed with exception: ",
          kv("groupId", groupId),
          kv("clusterName", clusterName),
          e);
      return SimpleApiResponse.badRequest(CommonErrorCode.SERVER_ERROR).build();
    }
  }

  @GET
  @Hidden
  @Path("/details/{groupId}/{clusterName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.getDetails.GET")
  public Response getDetails(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @QueryParam("since") final Long since,
      @QueryParam("until") final Long until,
      @QueryParam("queryShapeHash") final String queryShapeHash,
      @QueryParam("hostnamePorts[]") final List<String> hostnamePorts,
      @QueryParam("analyticPorts[]") final List<String> analyticHostnamePorts)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_DETAILS);

    if (!group.getId().toString().equals(groupId)) {
      QUERY_STATS_INVALID_GROUP_TOTAL.inc();
      return SimpleApiResponse.forbidden().build();
    }

    if (queryShapeHash == null) {
      throw new SvcException(
          INVALID_PARAMETER, "Query shape hash cannot be null when fetching details");
    }

    LOG.info(
        "/queryShapeInsights/details request received for {} {} {} {} {} {}",
        kv("groupId", groupId),
        kv("clusterName", clusterName),
        kv("since", since),
        kv("until", until),
        kv("hostnamePorts", hostnamePorts),
        kv("hash", queryShapeHash));

    try {
      final List<QueryStatsDetails> details =
          _queryStatsSvc.getQueryStatsDetails(
              groupId,
              clusterName,
              since,
              until,
              List.of(queryShapeHash),
              hostnamePorts,
              analyticHostnamePorts,
              _authzSvc.hasPiiViewAccess(user, group),
              true);

      // There should be only one QueryStatsDetails object for this endpoint
      if (details.size() != 1) {
        LOG.info(
            "Found more than one details entry for {} {} {}. Found {} entries",
            kv("groupId", groupId),
            kv("clusterName", clusterName),
            kv("hash", queryShapeHash),
            details.size());
        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
      }

      return Response.ok().entity(details.get(0)).build();
    } catch (final Exception e) {
      LOG.error(
          "Fetching details for QueryStats failed for {} {} {}",
          kv("groupId", groupId),
          kv("clusterName", clusterName),
          kv("hash", queryShapeHash),
          e);
      return SimpleApiResponse.badRequest(CommonErrorCode.SERVER_ERROR).build();
    }
  }

  @POST
  @Hidden
  @Path("/rejectedQueryShapes/{groupId}/{clusterName}/blockQueryShape/{queryShapeHash}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.blockQueryShape.POST")
  public Response blockQueryShape(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @PathParam("queryShapeHash") final String queryShapeHash)
      throws SvcException {

    if (queryShapeHash == null || queryShapeHash.trim().isEmpty()) {
      throw new SvcException(
          INVALID_PARAMETER,
          "Query shape hash cannot be null or empty when blocking a query shape");
    }

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_DETAILS);

    if (!group.getId().toString().equals(groupId)) {
      QUERY_STATS_INVALID_GROUP_TOTAL.inc();
      return SimpleApiResponse.forbidden().build();
    }

    LOG.info(
        "/rejectedQueryShapes/{groupId}/{clusterName}/blockQueryShape/{queryShapeHash} for {} {}"
            + " {}",
        kv("groupId", groupId),
        kv("clusterName", clusterName),
        kv("queryShapeHash", queryShapeHash));

    try {
      final HostCluster cluster = getHostClusterFromClusterName(group.getId(), clusterName);
      _queryStatsSvc.rejectQueryShape(
          group.getId(), user.getId(), clusterName, cluster, queryShapeHash);
      return Response.ok().build();
    } catch (final SvcException e) {
      // for now we can just return a generic server error, but we can revisit if necessary
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    } catch (final Exception e) {
      LOG.error(
          "Blocking query shape failed for {} {} {}",
          kv("groupId", groupId),
          kv("clusterId", clusterName),
          kv("queryShapeHash", queryShapeHash),
          e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
  }

  @POST
  @Hidden
  @Path("/rejectedQueryShapes/{groupId}/{clusterName}/unblockQueryShape/{queryShapeHash}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.unblockQueryShape.POST")
  public Response unblockQueryShape(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @PathParam("queryShapeHash") final String queryShapeHash)
      throws SvcException {

    if (queryShapeHash == null || queryShapeHash.trim().isEmpty()) {
      throw new SvcException(
          INVALID_PARAMETER,
          "Query shape hash cannot be null or empty when unblocking a query shape");
    }

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_DETAILS);

    if (!group.getId().toString().equals(groupId)) {
      QUERY_STATS_INVALID_GROUP_TOTAL.inc();
      return SimpleApiResponse.forbidden().build();
    }

    LOG.info(
        "/rejectedQueryShapes/{groupId}/{clusterName}/unblockQueryShape/{queryShapeHash} for {} {}"
            + " {}",
        kv("groupId", groupId),
        kv("clusterName", clusterName),
        kv("queryShapeHash", queryShapeHash));

    try {
      final HostCluster cluster = getHostClusterFromClusterName(group.getId(), clusterName);
      _queryStatsSvc.unRejectQueryShape(
          group.getId(), user.getId(), clusterName, cluster, queryShapeHash);
      return Response.ok().build();
    } catch (final SvcException e) {
      // for now we can just return a generic server error, but we can revisit if necessary
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    } catch (final Exception e) {
      LOG.error(
          "Unblocking query shape failed for {} {} {}",
          kv("groupId", groupId),
          kv("clusterId", clusterName),
          kv("queryShapeHash", queryShapeHash),
          e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
  }

  @GET
  @Hidden
  @Path("/rejectedQueryShapes/{groupId}/{clusterName}/status/{queryShapeHash}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.isQueryShapeRejected.GET")
  public Response isQueryShapeRejected(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @PathParam("queryShapeHash") final String queryShapeHash)
      throws JSONException, SvcException {

    if (queryShapeHash == null || queryShapeHash.trim().isEmpty()) {
      throw new SvcException(
          INVALID_PARAMETER,
          "Query shape hash cannot be null or empty when checking rejection status");
    }

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_DETAILS);

    if (!group.getId().toString().equals(groupId)) {
      QUERY_STATS_INVALID_GROUP_TOTAL.inc();
      return SimpleApiResponse.forbidden().build();
    }

    LOG.info(
        "/rejectedQueryShapes/{groupId}/{clusterName}/status/{queryShapeHash} request received for"
            + " {} {} {}",
        kv("groupId", groupId),
        kv("clusterName", clusterName),
        kv("queryShapeHash", queryShapeHash));

    try {
      final HostCluster cluster = getHostClusterFromClusterName(group.getId(), clusterName);
      final boolean isRejected =
          _queryStatsSvc.isQueryShapeRejected(
              group.getId(), user.getId(), clusterName, cluster, queryShapeHash);

      final Map<String, Boolean> response = Map.of("isRejected", isRejected);
      return Response.ok().entity(response).build();
    } catch (final SvcException e) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    } catch (final Exception e) {
      LOG.error(
          "Checking rejection status for query shape failed for {} {} {}",
          kv("groupId", groupId),
          kv("clusterName", clusterName),
          kv("queryShapeHash", queryShapeHash),
          e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
  }

  @GET
  @Hidden
  @Path("/rejectedQueryShapes/{groupId}/{clusterName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
  @Auth(endpointAction = "epa.project.QueryStatsResource.getRejectedQueryShapes.GET")
  public Response getRejectedQueryShapes(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final Group group,
      @Context final AppUser user,
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName)
      throws JSONException, SvcException {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.V1_QUERY_STATS_DETAILS);

    if (!group.getId().toString().equals(groupId)) {
      QUERY_STATS_INVALID_GROUP_TOTAL.inc();
      return SimpleApiResponse.forbidden().build();
    }

    LOG.info(
        "/queryShapeInsights/rejectedQueryShapes request received for {} {}",
        kv("groupId", groupId),
        kv("clusterName", clusterName));

    try {
      final HostCluster cluster = getHostClusterFromClusterName(group.getId(), clusterName);
      final List<BasicQueryStatsSummary> hashes =
          _queryStatsSvc.getRejectedQueryShapes(
              group.getId(),
              user.getId(),
              clusterName,
              cluster,
              _authzSvc.hasPiiViewAccess(user, group));

      // Set gauge metric with the count of rejected query shapes
      REJECTED_QUERY_SHAPES_COUNT.set(hashes.size());

      LOG.debug(
          "Updated rejected query shapes count metric: {} for {} {}",
          kv("count", hashes.size()),
          kv("groupId", groupId),
          kv("clusterName", clusterName));

      return Response.ok().entity(hashes).build();
    } catch (final SvcException e) {
      // for now we can just return a generic server error, but we can revisit if necessary
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    } catch (final Exception e) {
      LOG.error(
          "Fetching details for QueryStats failed for {} {}",
          kv("groupId", groupId),
          kv("clusterId", clusterName),
          e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
  }

  private HostCluster getHostClusterFromClusterName(
      final ObjectId groupId, final String clusterName) throws SvcException {
    if (clusterName != null) {
      return _ndsClusterSvc
          .findActiveHostClusterByGroupIdAndAtlasClusterName(groupId, clusterName)
          .orElseThrow(
              () -> {
                LOG.warn("Unable to find cluster with clusterName: {}", clusterName);
                return new SvcException(SERVER_ERROR, "Cluster not found");
              }); // uses deployment cluster name to find HostCluster
    }
    throw new SvcException(BAD_REQUEST, "Cluster name must be provided");
  }
}
