package com.xgen.svc.mms.res.v2;

import static java.time.Instant.now;

import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.util._public.util.BsonUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.mms.svc.DbProfileStatsSvc;
import com.xgen.svc.mms.svc.DbProfileSvc;
import com.xgen.svc.mms.svc.DbProfileSvc.ProfilerEntryView;
import com.xgen.svc.nds.serverless.util.ServerlessAccessUtil;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Resource for Serverless Profile Data related endpoints */
@Path("/v2/profiler/serverless")
@Singleton
public class ServerlessProfilerResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(ServerlessProfilerResource.class);
  private final DbProfileStatsSvc _dbProfileStatsSvc;
  private final DbProfileSvc _dbProfileSvc;
  private final ServerlessAccessUtil _serverlessAccessUtil;

  @Inject
  public ServerlessProfilerResource(
      final DbProfileSvc pDbProfileSvc,
      final DbProfileStatsSvc pDbProfileStatsSvc,
      final ServerlessAccessUtil pServerlessAccessUtil) {
    _dbProfileSvc = pDbProfileSvc;
    _dbProfileStatsSvc = pDbProfileStatsSvc;
    _serverlessAccessUtil = pServerlessAccessUtil;
  }

  @GET
  @Path("/summaries/{groupId}/{clusterUniqueId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AccessAudit(auditEventType = AccessEvent.Type.VISUAL_PROFILER)
  @Auth(endpointAction = "epa.project.ServerlessProfilerResource.profilerSummaryStatsServerless.GET")
  public Response profilerSummaryStatsServerless(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @QueryParam("limit") @DefaultValue("10000") final int pLimit,
      @QueryParam("since") final Long pSince)
      throws JSONException, SvcException {

    _serverlessAccessUtil.assertAccessToServerlessCluster(pGroup, pUser, pClusterUniqueId);
    final JSONArray summaries =
        getDbProfileStatsSvc()
            .getNamespaceSummaries(pOrg, pGroup, pClusterUniqueId, pUser, pLimit, pSince);
    return Response.ok(summaries.toString()).build();
  }

  @GET
  @Path("/entries/{groupId}/{clusterUniqueId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AccessAudit(auditEventType = AccessEvent.Type.VISUAL_PROFILER)
  @Auth(endpointAction = "epa.project.ServerlessProfilerResource.getProfilerEntriesServerless.GET")
  public Response getProfilerEntriesServerless(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @QueryParam("limit") @DefaultValue("10000") final int pLimit,
      @QueryParam("since") final Long pSince)
      throws JSONException, SvcException {
    _serverlessAccessUtil.assertAccessToServerlessCluster(pGroup, pUser, pClusterUniqueId);

    Instant start = now().minus(Duration.ofHours(24));
    if (pSince != null) {
      start = Instant.ofEpochMilli(pSince);
    }
    final List<ProfilerEntryView> entries =
        getDbProfileSvc()
            .getEntriesFromSlowQueryLogsByClusterUniqueId(
                pOrg, pGroup, pClusterUniqueId, start, pLimit, pUser);

    // trim clusterUniqueId from the namespace field and the raw slow log entry before sending the
    // ProfilerEntryViews back to the UI
    final String clusterUniqueIdPrefix = pClusterUniqueId + "_";
    for (final ProfilerEntryView view : entries) {
      view.setNamespace(StringUtils.removeStart(view.getNamespace(), clusterUniqueIdPrefix));
      if (view.getRawEntry() != null) {
        view.getRawEntry().removeField("appName");
        BsonUtils.replaceStringValues(view.getRawEntry(), clusterUniqueIdPrefix, "");
      }
    }

    return Response.ok(entries).build();
  }

  public DbProfileStatsSvc getDbProfileStatsSvc() {
    return _dbProfileStatsSvc;
  }

  public DbProfileSvc getDbProfileSvc() {
    return _dbProfileSvc;
  }
}
