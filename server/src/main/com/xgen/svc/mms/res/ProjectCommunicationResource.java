/* (C) Copyright 2011, 10gen */

package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.communication._public.client.CommunicationClient;
import com.xgen.cloud.communication._public.model.admin.ValidityData;
import com.xgen.cloud.group._public.model.Group;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;
import org.bson.types.ObjectId;

@Path("/project/{groupId}/communication")
@Singleton
public class ProjectCommunicationResource extends BaseResource {
  private final CommunicationClient communicationClient;

  @Inject
  public ProjectCommunicationResource(final CommunicationClient communicationClient) {
    this.communicationClient = communicationClient;
  }

  /** Used to fetch the validity of the various communication methodologies. */
  @GET
  @Path("/validity")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_OWNER, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.ProjectCommunicationResource.getValidity.GET")
  public Response getValidity(@Context final Group project) {
    final ValidityData validityData =
        communicationClient.getIntegrationValidityForProject(project.getId());
    final Map<ObjectId, String> providerTypes = new HashMap<>();
    project.getAllIntegrations().entrySet().stream()
        .forEach(
            entry -> {
              String providerType = entry.getKey();
              if (providerType.equals("PAGER_DUTY")) {
                providerType = "PAGERDUTY";
              }
              if (entry.getValue().size() > 0) {
                final ObjectId integrationId = entry.getValue().get(0).getId();
                providerTypes.put(integrationId, providerType);
              }
            });
    final Map<String, Object> result =
        Map.of(
            "validity",
            validityData.validityBreakdown == null ? null : validityData.validityBreakdown,
            "providerTypes",
            providerTypes);

    return Response.ok().entity(result).build();
  }
}
