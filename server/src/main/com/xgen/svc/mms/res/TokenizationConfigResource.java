package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.ping.tokenization._public.model.TokenizationConfig;
import com.xgen.cloud.ping.tokenization._public.svc.TokenizationConfigSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;

@Path("nds/tokenizationConfig")
@Singleton
public class TokenizationConfigResource {

  private final TokenizationConfigSvc _tokenizationConfigSvc;

  @Inject
  public TokenizationConfigResource(final TokenizationConfigSvc pTokenizationConfigSvc) {
    _tokenizationConfigSvc = pTokenizationConfigSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_MONITORING_ADMIN, RoleSet.GLOBAL_APP_SETTING_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.TokenizationConfigResource.getAllConfigs.GET")
  public Response getAllConfigs(@Context final HttpServletRequest pRequest) {
    final List<TokenizationConfig> tokenizationConfigs = _tokenizationConfigSvc.getAllConfigs();
    return Response.ok(tokenizationConfigs).build();
  }

  @POST
  @Path("/name/{name}/type/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_APP_SETTING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.TokenizationConfigResource.addConfig.POST")
  public Response addConfig(
      @Context final HttpServletRequest pRequest,
      @PathParam("name") final String pName,
      @PathParam("type") final String pType) {
    _tokenizationConfigSvc.addConfig(pName, pType);
    return Response.ok().build();
  }

  @PUT
  @Path("/name/{name}/key/{key}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_APP_SETTING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.TokenizationConfigResource.addKey.PUT")
  public Response addKey(
      @Context final HttpServletRequest pRequest,
      @PathParam("name") final String pName,
      @PathParam("key") final String pKey) {
    _tokenizationConfigSvc.addKey(pName, pKey);
    return Response.ok().build();
  }

  @DELETE
  @Path("/name/{name}/key/{key}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_APP_SETTING_ADMIN, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.TokenizationConfigResource.removeKey.DELETE")
  public Response removeKey(
      @Context final HttpServletRequest pRequest,
      @PathParam("name") final String pName,
      @PathParam("key") final String pKey) {
    _tokenizationConfigSvc.removeKey(pName, pKey);
    return Response.ok().build();
  }
}
