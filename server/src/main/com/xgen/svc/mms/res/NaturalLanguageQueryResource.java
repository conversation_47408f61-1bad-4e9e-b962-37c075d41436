package com.xgen.svc.mms.res;

import com.fasterxml.jackson.annotation.JsonView;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.brs.core._public.view.ApiNaturalLanguageQueryAccessView;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.ratelimit._public.svc.RateLimitSvc;
import com.xgen.cloud.naturallanguagequery._public.devtools.FindOrAggregate;
import com.xgen.cloud.naturallanguagequery._public.devtools.NaturalLanguageQueryRequestOrigin;
import com.xgen.cloud.naturallanguagequery._public.model.ApiMockDataSchemaRequestView;
import com.xgen.cloud.naturallanguagequery._public.model.ApiMockDataSchemaResponseView;
import com.xgen.cloud.naturallanguagequery._public.svc.NaturalLanguageQuerySvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.view.ApiNaturalLanguageMqlQueryView;
import com.xgen.svc.mms.misc.NaturalLanguageQueryTool;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Singleton
@Path("/ai/v1")
public class NaturalLanguageQueryResource extends BaseResource {

  private final AppSettings _appSettings;
  private final NaturalLanguageQuerySvc _querySvc;
  private final RateLimitSvc _rateLimitSvc;
  private final SegmentEventSvc _segmentEventSvc;
  private static final String ENABLED_STATUS_FIELD = "enabled";
  private static final String GEN_AI_COMPASS_FEATURE_ID = "GEN_AI_COMPASS";

  @Inject
  public NaturalLanguageQueryResource(
      final AppSettings pAppSettings,
      final NaturalLanguageQuerySvc pQuerySvc,
      final RateLimitSvc pRateLimitSvc,
      final SegmentEventSvc pSegmentEventSvc) {
    _appSettings = pAppSettings;
    _querySvc = pQuerySvc;
    _rateLimitSvc = pRateLimitSvc;
    _segmentEventSvc = pSegmentEventSvc;
  }

  @POST
  @Path("/groups/{groupId}/mql-query")
  @UiCall(roles = {RoleSet.GROUP_READ_ONLY})
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.NaturalLanguageQueryResource.find.POST")
  public Response find(
      @Context HttpServletRequest request,
      @Context final AuditInfo pAuditInfo,
      @QueryParam("request_id") final String pRequestId,
      @PathParam("groupId") String pGroupId,
      @QueryParam("envelope") final boolean pEnvelope,
      @HeaderParam("User-Agent") final String pCompassVersion,
      final ApiNaturalLanguageMqlQueryView pRequest) {
    final RequestParams params = getRequestParams(request);
    final Organization org = params.getCurrentOrganization();
    final Group group = params.getCurrentGroup();
    if (FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ENABLE_DATA_EXPLORER_GEN_AI_FEATURES, _appSettings, org, group)) {
      if (FeatureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.DISABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING,
          _appSettings,
          org,
          group)) {
        pRequest.setSampleDocuments(null);
      }

      return NaturalLanguageQueryTool.doFindOrAggregate(
          pAuditInfo,
          FindOrAggregate.FIND,
          pRequest,
          pRequestId,
          NaturalLanguageQueryRequestOrigin.DATA_EXPLORER,
          pCompassVersion,
          pEnvelope,
          _appSettings,
          _querySvc,
          _segmentEventSvc,
          _rateLimitSvc);
    }
    return Response.status(Status.NOT_FOUND).build();
  }

  @POST
  @Path("/groups/{groupId}/mql-aggregation")
  @UiCall(roles = {RoleSet.GROUP_READ_ONLY})
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.NaturalLanguageQueryResource.aggregate.POST")
  public Response aggregate(
      @Context HttpServletRequest request,
      @Context final AuditInfo pAuditInfo,
      @QueryParam("request_id") final String pRequestId,
      @PathParam("groupId") String pGroupId,
      @QueryParam("envelope") final boolean pEnvelope,
      @HeaderParam("User-Agent") final String pCompassVersion,
      final ApiNaturalLanguageMqlQueryView pRequest) {
    final RequestParams params = getRequestParams(request);
    final Organization org = params.getCurrentOrganization();
    final Group group = params.getCurrentGroup();
    if (FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ENABLE_DATA_EXPLORER_GEN_AI_FEATURES, _appSettings, org, group)) {
      if (FeatureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.DISABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING,
          _appSettings,
          org,
          group)) {
        pRequest.setSampleDocuments(null);
      }
      return NaturalLanguageQueryTool.doFindOrAggregate(
          pAuditInfo,
          FindOrAggregate.AGGREGATE,
          pRequest,
          pRequestId,
          NaturalLanguageQueryRequestOrigin.DATA_EXPLORER,
          pCompassVersion,
          pEnvelope,
          _appSettings,
          _querySvc,
          _segmentEventSvc,
          _rateLimitSvc);
    }
    return Response.status(Status.NOT_FOUND).build();
  }

  @POST
  @Path("/groups/{groupId}/mock-data-schema")
  @UiCall(roles = {RoleSet.GROUP_READ_ONLY})
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @Operation(
      operationId = "generateMockDataSchema",
      summary = "Generates a mock data schema",
      description =
          "Generates a mapping of faker.js functions to fields for the given raw schema. This"
              + " schema will be used to generate mock data in another collection.")
  @Auth(endpointAction = "epa.project.NaturalLanguageQueryResource.mockDataSchema.POST")
  public Response mockDataSchema(
      @Context HttpServletRequest requestContext,
      @Context final AuditInfo auditInfo,
      @PathParam("groupId") final String groupId,
      @QueryParam("request_id") final String requestId,
      @QueryParam("envelope") final Boolean envelope,
      @HeaderParam("User-Agent") final String compassVersion,
      final ApiMockDataSchemaRequestView requestView) {

    final RequestParams params = getRequestParams(requestContext);
    final Organization org = params.getCurrentOrganization();
    final Group group = params.getCurrentGroup();

    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ENABLE_DATA_EXPLORER_GEN_AI_FEATURES, _appSettings, org, group)) {
      return Response.status(Status.NOT_FOUND).build();
    }

    // todo: need to determine if sample docs will be used in addition to the 'sample_values' of
    // each field
    final List<Object> sampleDocsPlaceholder = new ArrayList<>();

    final ApiMockDataSchemaResponseView respView =
        _querySvc.generateMockDataSchema(
            requestView.getDatabaseName(),
            requestView.getCollectionName(),
            requestView.getSchema(),
            sampleDocsPlaceholder,
            requestId,
            compassVersion,
            envelope);

    // todo: Changes within `NaturalLanguageQueryTool` to pass `_querySvc` through (CLOUDP-333835)

    return new ApiResponseBuilder(true).ok().content(respView).build();
  }

  @GET
  @Path("/hello/{userId}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  @JsonView(ApiNaturalLanguageQueryAccessView.class)
  public Response hello(
      @PathParam("userId") final String pUID, @QueryParam("envelope") final Boolean pEnvelope) {
    if (!_appSettings.isGenerativeAIForDataExplorerEnabled()) {
      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(new ApiNaturalLanguageQueryAccessView(null))
          .build();
    }

    return new ApiResponseBuilder(pEnvelope)
        .ok()
        .content(
            new ApiNaturalLanguageQueryAccessView(
                Map.of(GEN_AI_COMPASS_FEATURE_ID, Map.of(ENABLED_STATUS_FIELD, true))))
        .build();
  }
}
