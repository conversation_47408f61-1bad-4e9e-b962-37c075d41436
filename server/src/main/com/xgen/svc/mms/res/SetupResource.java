/* (C) Copyright 2014, MongoDB, Inc. */

package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.svc.DeploymentBuilderSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.brs.core._public.svc.BackupEncryptionUtil;
import com.xgen.cloud.brs.core._public.svc.BackupHostSvc;
import com.xgen.cloud.brs.core._public.svc.WTCNamespaceFilteringSvc;
import com.xgen.cloud.brs.web._public.svc.BackupAppSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessArguments;
import com.xgen.cloud.deployment._public.model.ProcessArgumentsFactory;
import com.xgen.cloud.deployment._public.model.ReplicaSet;
import com.xgen.cloud.deployment._public.model.ShardedCluster;
import com.xgen.cloud.deployment._public.model.monitoring.ProcessMonitoringState;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.monitoring.topology._public.svc.HostClusterSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.bson.BasicBSONObject;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** The contact resource. */
@Path("/setup")
@Singleton
public class SetupResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(SetupResource.class);

  // TODO DK to be replaced with Deployment
  private final HostSvc _hostSvc;
  private final HostClusterSvc _hostClusterSvc;
  private final BackupHostSvc _backupHostSvc;
  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final BackupAppSvc _backupAppSvc;
  private final DeploymentBuilderSvc _deploymentBuilderSvc;

  @Inject
  public SetupResource(
      final HostSvc pHostSvc,
      final HostClusterSvc pHostClusterSvc,
      final BackupHostSvc pBackupHostSvc,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final BackupAppSvc pBackupAppSvc,
      final DeploymentBuilderSvc pDeploymentBuilderSvc) {
    _hostSvc = pHostSvc;
    _hostClusterSvc = pHostClusterSvc;
    _backupHostSvc = pBackupHostSvc;
    _automationConfigSvc = pAutomationConfigSvc;
    _backupAppSvc = pBackupAppSvc;
    _deploymentBuilderSvc = pDeploymentBuilderSvc;
  }

  @GET
  @Path("/onboarding/{groupId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.SetupResource.onboarding.GET")
  public Response onboarding(@Context final Group pGroup) throws Exception {
    return tempRedirect(pGroup.getHomepageUri());
  }

  // TODO DK Replace with new deployment API
  @GET
  @Path("/deploymentData/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.SetupResource.getDeploymentData.GET")
  public Response getDeploymentData(@Context final Group pGroup) {
    final JSONObject result = new JSONObject();

    final List<Host> hosts =
        _hostSvc.findHostsByGroupIdAndTypes(
            pGroup.getId(), Arrays.asList(HostType.allButMongosValuesAsArray()), false);

    final List<HostCluster> clusters = _hostClusterSvc.getClustersForGroupId(pGroup.getId());
    final List<HostCluster> replicaSets =
        _hostClusterSvc.getReplicaSetHostClustersForGroupId(pGroup.getId());
    final Deployment deployment =
        _automationConfigSvc.findPublishedOrEmpty(pGroup.getId()).getDeployment();

    final Map<String, Process> managedStandalones = new HashMap<String, Process>();
    if (deployment != null && deployment.getStandaloneProcesses().size() != 0) {
      final Deployment deploymentWithMonitoringData =
          _deploymentBuilderSvc.buildDeployment(pGroup, deployment);
      for (final Process standalone : deploymentWithMonitoringData.getStandaloneProcesses()) {
        final ProcessMonitoringState monitoringState = standalone.getMonitoringState();
        if (monitoringState != null && monitoringState.getHostId() != null) {
          managedStandalones.put(monitoringState.getHostId(), standalone);
        }
      }
    }

    try {
      final JSONArray standaloneData = new JSONArray();
      for (final Host host : hosts) {
        if (!host.getIsStandalone()) {
          break;
        }
        final JSONObject data = new JSONObject();

        final Process process = managedStandalones.get(host.getId());

        data.put("hostId", host.getId());
        data.put("fullHostname", host.getFullHostname());
        if (process != null) {
          data.put("managed", process.isManaged());
          data.put("name", process.getName());
        } else {
          data.put("managed", false);
        }
        standaloneData.put(data);
      }
      result.put("standalones", standaloneData);

      final Map<String, ReplicaSet> rsMap =
          deployment == null ? Map.of() : deployment.buildReplicaSetMap();
      final HashMap<String, JSONObject> replicaSetLookup = new HashMap<>();
      final JSONArray replicaSetData = new JSONArray();
      for (final HostCluster cluster : replicaSets) {
        final JSONObject data = new JSONObject();
        data.put("clusterId", cluster.getClusterId());
        data.put("clusterName", cluster.getName());
        data.put("replicaSetId", cluster.getReplicaSetIds().iterator().next());
        data.put("replicaSetName", cluster.getReplicaSetName());
        data.put("isAutomationManaged", rsMap.containsKey(data.getString("replicaSetId")));
        data.put("supportsWTCbackup", isFCV42AndUp(cluster));
        data.put(
            "supportsWTCNamespaceFiltering",
            _hostSvc
                .findHostsByIds(cluster.getGroupId(), SetUtils.toList(cluster.getHostIds()), true)
                .stream()
                .filter(Host::isEnabled)
                .filter(h -> h.getIsPrimary() || h.getIsSecondary())
                .allMatch(WTCNamespaceFilteringSvc::supportsWTCNamespaceFiltering));

        final Optional<Host> primary =
            hosts.stream()
                .filter(
                    h ->
                        StringUtils.equals(
                                cluster.getReplicaSetIds().iterator().next(), h.getReplicaSetId())
                            && h.hasStorageEngineName())
                .findAny();
        if (primary.isPresent()) {
          data.put("primaryStorageEngine", primary.get().getStorageEngineName());
          data.put("primaryEncrypted", isEncryptionEnabled(primary.get()));
        }

        // Add the backup suggested storage engine for a replica set to the results.
        final String rsId = cluster.getReplicaSetIds().iterator().next();
        if (cluster.getConfigServerReplicaSetId() != null) {
          // For config servers, backup will automatically choose the storage engine for
          // the user, so the concept of a "backup suggested storage engine" isn't useful.
          // We use "unknown" here so that the UI will not consider the storage engine of
          // config servers when calculating the "backup suggested storage engine" for the
          // cluster the config server is in.
          data.put("backupSuggestedStorageEngine", BackupHostSvc.BACKUP_STORAGE_ENGINE_UNKNOWN);
        } else {
          data.put(
              "backupSuggestedStorageEngine",
              _backupHostSvc.getBackupSuggestedStorageEngineNameForRS(pGroup.getId(), rsId));
        }

        data.put(
            "hideEncryptionOption",
            primary.isEmpty()
                || !BackupEncryptionUtil.isPrimarySupportEncryption(primary.get(), null)
                || !BackupEncryptionUtil.isGroupSupportEncryption(
                    _backupAppSvc.getBackupAppDao().getBackupGroupConfigDao(), pGroup));

        replicaSetData.put(data);
        replicaSetLookup.put(rsId, data);
      }
      result.put("replicaSets", replicaSetData);

      final Map<String, ShardedCluster> clusterMap =
          deployment == null ? Collections.emptyMap() : deployment.buildShardingMap();
      final JSONArray clustersData = new JSONArray();
      for (final HostCluster cluster : clusters) {
        final JSONObject data = new JSONObject();
        data.put("clusterId", cluster.getClusterId());
        data.put("clusterName", cluster.getName());
        data.put("isAutomationManaged", clusterMap.containsKey(cluster.getName()));

        final JSONArray children = new JSONArray();
        cluster.getReplicaSetIds().stream()
            .filter(replicaSetLookup::containsKey)
            .forEach(rsId -> children.put(replicaSetLookup.get(rsId)));

        data.put("children", children);

        clustersData.put(data);
      }
      result.put("clusters", clustersData);
    } catch (final JSONException jex) {
    } // ignore
    return Response.ok(result.toString()).build();
  }

  private static ProcessArguments getProcessedArguments(final Host pHost) {
    final BasicBSONObject cmdLineOpts = pHost.getCmdLineOptsBson();
    if (cmdLineOpts == null) {
      return null;
    }
    return ProcessArgumentsFactory.fromCmdLineOpts(
        pHost.getVersion(), (BasicBSONObject) cmdLineOpts.get("parsed"));
  }

  private static boolean isEncryptionEnabled(final Host pHost) {
    final ProcessArguments args = getProcessedArguments(pHost);
    if (args == null) {
      return false;
    }

    final Boolean enableEncryption = args.getEnableEncryption();
    return enableEncryption != null && enableEncryption;
  }

  private boolean isFCV42AndUp(final HostCluster cluster) {
    return _hostSvc
        .findHostsByIds(cluster.getGroupId(), SetUtils.toList(cluster.getHostIds()), true)
        .stream()
        .anyMatch(
            host ->
                VersionUtils.checkFCVGreaterThanOrEqualTo42(
                    Optional.ofNullable(host)
                        .map(Host::getFeatureCompatibilityVersionString)
                        .orElse(null)));
  }
}
