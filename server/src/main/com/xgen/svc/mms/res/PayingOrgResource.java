package com.xgen.svc.mms.res;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billing._public.svc.exception.InvalidLinkedOrgException;
import com.xgen.cloud.billingplatform.crossorg._public.model.LinkableOrgView;
import com.xgen.cloud.billingplatform.crossorg._public.svc.CrossOrgValidationSvc;
import com.xgen.cloud.billingplatform.crossorg._public.svc.OrgLinkingSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.dao.billing.PayingOrgDao;
import com.xgen.svc.mms.model.billing.PayingOrg;
import com.xgen.svc.mms.svc.billing.LinkableOrgViewSvc;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/billing/payingOrg")
@Singleton
public class PayingOrgResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(PayingOrgResource.class);

  private final PayingOrgSvc payingOrgSvc;

  private final LinkableOrgViewSvc linkableOrgViewSvc;
  private final PayingOrgDao payingOrgDao;
  private final OrganizationDao organizationDao;
  private final CrossOrgValidationSvc crossOrgValidationSvc;
  private final AuthzSvc authzSvc;
  private final OrgLinkingSvc orgLinkingSvc;

  @Inject
  public PayingOrgResource(
      final PayingOrgSvc payingOrgSvc,
      LinkableOrgViewSvc linkableOrgViewSvc,
      final PayingOrgDao payingOrgDao,
      final OrganizationDao organizationDao,
      final CrossOrgValidationSvc crossOrgValidationSvc,
      final AuthzSvc authzSvc,
      final OrgLinkingSvc orgLinkingSvc) {
    this.payingOrgSvc = payingOrgSvc;
    this.linkableOrgViewSvc = linkableOrgViewSvc;
    this.payingOrgDao = payingOrgDao;
    this.organizationDao = organizationDao;
    this.crossOrgValidationSvc = crossOrgValidationSvc;
    this.authzSvc = authzSvc;
    this.orgLinkingSvc = orgLinkingSvc;
  }

  @GET
  @Path("/{orgId}/eligible")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PayingOrgResource.isEligibleToBecomePayingOrg.GET")
  public Response isEligibleToBecomePayingOrg(@Context final Organization pOrganization) {
    final boolean isEligible =
        crossOrgValidationSvc.isEligibleToBecomePayingOrg(pOrganization.getId());
    return Response.ok().entity(isEligible).build();
  }

  @POST
  @Path("/linkToPayingOrg/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PayingOrgResource.linkToPayingOrg.POST")
  public Response linkToPayingOrg(
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      final PayingOrg payingOrgFromUi)
      throws Exception {
    if (!payingOrgFromUi.getPayingOrgId().equals(pOrganization.getId())) {
      LOG.info(
          "Requesting orgId {} does not match payingOrgId {}",
          pOrganization.getId(),
          payingOrgFromUi.getPayingOrgId());
      return Response.status(Status.BAD_REQUEST).build();
    }

    for (final ObjectId linkedOrgId : payingOrgFromUi.getLinkedOrgIds()) {
      if (!authzSvc.isOrgBillingAdmin(pUser, linkedOrgId)) {
        throw new IllegalArgumentException(
            String.format("User is not Org Billing Admin for org %s", linkedOrgId));
      }
    }
    try {
      final PayingOrg payingOrg =
          orgLinkingSvc.linkToPayingOrg(
              payingOrgFromUi.getPayingOrgId(), payingOrgFromUi.getLinkedOrgIds(), pAuditInfo);
      return Response.ok(payingOrg).build();
    } catch (InvalidLinkedOrgException e) {
      return Response.status(Status.BAD_REQUEST).build();
    }
  }

  @GET
  @Path("/linkableOrgs/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PayingOrgResource.getLinkableOrgs.GET")
  public Response getLinkableOrgs(
      @Context final Organization pPayingOrganization, @Context final AppUser pUser) {
    final Set<ObjectId> potentialOrgIds = payingOrgSvc.getOrgIdsWithBillingAdminRole(pUser);
    final List<ObjectId> validLinkedOrgIds =
        crossOrgValidationSvc.filterNotEligibleOrganizations(
            pPayingOrganization.getId(), potentialOrgIds);
    final List<LinkableOrgView> result =
        linkableOrgViewSvc.getLinkableOrgViewFromOrgIds(validLinkedOrgIds, pUser);
    return Response.ok().entity(result).build();
  }

  @GET
  @Path("/linkableOrgView/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PayingOrgResource.getLinkableOrgView.GET")
  public Response getLinkableOrgView(
      @Context final Organization pPayingOrganization, @Context final AppUser pUser) {
    final LinkableOrgView result =
        linkableOrgViewSvc.getLinkableOrgView(pPayingOrganization, pUser);
    return Response.ok().entity(result).build();
  }

  @GET
  @Path("/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PayingOrgResource.getPayingOrganization.GET")
  public Response getPayingOrganization(@Context final Organization pOrganization) {
    final ObjectId orgId = pOrganization.getId();
    final Optional<PayingOrg> asPayingOrg = payingOrgDao.findByPayingOrgId(orgId);
    if (asPayingOrg.isPresent()) {
      return Response.ok().entity(asPayingOrg.get()).build();
    }
    final Optional<PayingOrg> asLinkedOrg =
        payingOrgDao.findByLinkedOrgId(orgId, DriverUtils.SECONDARY_PREFERRED_MINIMUM);
    if (asLinkedOrg.isPresent()) {
      final Organization organization =
          organizationDao.findById(asLinkedOrg.get().getPayingOrgId());
      final PayingOrg withPayingOrgName =
          asLinkedOrg.get().toBuilder().payingOrgName(organization.getName()).build();
      return Response.ok().entity(withPayingOrgName).build();
    }
    return Response.status(Response.Status.NOT_FOUND).build();
  }

  @GET
  @Path("/{orgId}/linked")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PayingOrgResource.getLinkedOrganizations.GET")
  public Response getLinkedOrganizations(
      @Context final Organization pOrganization, @Context final AppUser appUser) {
    final ObjectId orgId = pOrganization.getId();
    final Optional<PayingOrg> asPayingOrg = payingOrgDao.findByPayingOrgId(orgId);
    if (asPayingOrg.isPresent()) {
      final List<LinkableOrgView> linkedOrgsView =
          linkableOrgViewSvc.getLinkableOrgViewFromOrgIds(
              asPayingOrg.get().getLinkedOrgIds(), appUser);
      return Response.ok().entity(linkedOrgsView).build();
    }
    return Response.ok().entity(new ArrayList<>()).build();
  }
}
