package com.xgen.svc.mms.res;

import static com.xgen.cloud.common.util._public.util.MathUtils.isHashedIdWithinPercentage;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.svc.mms.svc.ServerlessAutoIndexingSvc.SERVERLESS_AUTO_INDEXING_PERCENTAGE_ALLOWED_KEY;
import static java.time.Duration.ofDays;
import static java.time.Instant.now;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.access.activity._public.event.AccessEvent.Type;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._public.svc.AgentJobsProcessorSvc;
import com.xgen.cloud.common.metrics._public.constants.MonitoringConstants;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.svc.HostClusterSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.monitoring.tsstrategy._private.dao.AbstractDaoStrategy.RollupType;
import com.xgen.cloud.nds.regionalization._public.RegionalizationValidator;
import com.xgen.cloud.nds.regionalization._public.RegionalizationValidator.RegionalEndpoint;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.performanceadvisor._private.dao.ShardingConfigDao;
import com.xgen.cloud.performanceadvisor._public.model.IndexSuggestionEngineType;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.AutoIndexingState;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.AutoIndexingStateForRollout;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.mms.res.view.performanceadvisor.CreateIndexesResponseView;
import com.xgen.svc.mms.res.view.performanceadvisor.DropIndexesResponseView;
import com.xgen.svc.mms.res.view.performanceadvisor.PerformanceAdvisorBuildingIndexResponseView;
import com.xgen.svc.mms.res.view.performanceadvisor.PerformanceAdvisorFeedbackResponseView;
import com.xgen.svc.mms.res.view.performanceadvisor.PerformanceAdvisorFeedbackView;
import com.xgen.svc.mms.res.view.performanceadvisor.PerformanceAdvisorIndexView;
import com.xgen.svc.mms.res.view.performanceadvisor.PerformanceAdvisorTextQueryUsageResponseView;
import com.xgen.svc.mms.res.view.performanceadvisor.SchemaSuggestionView;
import com.xgen.svc.mms.svc.ServerlessAutoIndexingSvc;
import com.xgen.svc.mms.svc.host.LegacyHostSvc;
import com.xgen.svc.mms.svc.performanceadvisor.CCPADropIndexSuggestionsSvc;
import com.xgen.svc.mms.svc.performanceadvisor.CCPAIndexSuggestionSvc;
import com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc;
import com.xgen.svc.mms.svc.performanceadvisor.DropIndexSuggestionsSvc;
import com.xgen.svc.mms.svc.performanceadvisor.PerformanceAdvisorFeedbackSvc;
import com.xgen.svc.mms.svc.performanceadvisor.PerformanceAdvisorSvc;
import com.xgen.svc.mms.svc.performanceadvisor.dataservices.DataServicesSuggestionsSvc;
import com.xgen.svc.mms.svc.performanceadvisor.dataservices.TextQueryMetricsSvc;
import com.xgen.svc.mms.svc.performanceadvisor.schema.CCPASchemaSuggestionsSvc;
import com.xgen.svc.mms.svc.performanceadvisor.schema.SchemaSuggestionsSvc;
import com.xgen.svc.nds.serverless.util.ServerlessAccessUtil;
import io.prometheus.client.Counter;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/performanceAdvisor")
@Singleton
public class PerformanceAdvisorResource extends BaseResource {
  public static final String GET_INDEX_CREATION_ADVICE_REQUESTS_COUNTER_NAME =
      "performance_advisor_resource_get_index_creation_advice_requests_total";
  private static final Counter GET_INDEX_CREATION_ADVICE_REQUESTS_COUNTER =
      Counter.build()
          .name(GET_INDEX_CREATION_ADVICE_REQUESTS_COUNTER_NAME)
          .help("Total get index creation advice requests")
          .labelNames("from")
          .namespace(MonitoringConstants.ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Logger LOG = LoggerFactory.getLogger(PerformanceAdvisorResource.class);

  private final AppSettings _settings;
  private final RegionalizationValidator _regionalizationValidator;
  private final HostSvc _hostSvc;
  private final LegacyHostSvc _legacyHostSvc;
  private final PerformanceAdvisorFeedbackSvc _paFeedbackSvc;
  private final SchemaSuggestionsSvc _schemaSuggestionsSvc;
  private final ServerlessAccessUtil _serverlessAccessUtil;
  private final DropIndexSuggestionsSvc _dropIndexSuggestionsSvc;
  private final CreateIndexSuggestionsSvc _createIndexSuggestionsSvc;
  private final TextQueryMetricsSvc _textQueryMetricsSvc;
  private final DataServicesSuggestionsSvc _dataServicesSuggestionsSvc;
  private final ServerlessAutoIndexingSvc _serverlessAutoIndexingSvc;
  private final PerformanceAdvisorSvc _performanceAdvisorSvc;
  private final HostClusterSvc _hostClusterSvc;
  private final GroupSvc _groupSvc;
  private final CCPAIndexSuggestionSvc _ccpaIndexSuggestionSvc;
  private final CCPADropIndexSuggestionsSvc _ccpaDropIndexSuggestionsSvc;
  private final CCPASchemaSuggestionsSvc _ccpaSchemaSuggestionSvc;
  private final ObjectMapper _objectMapper;
  private final AgentJobSvc _agentJobSvc;
  private final AgentJobsProcessorSvc _agentJobsProcessorSvc;
  private final ShardingConfigDao _shardingConfigDao;

  public enum HostType {
    PRIMARY,
    SECONDARY,
    ALL
  }

  @Inject
  public PerformanceAdvisorResource(
      final AppSettings pAppSettings,
      final RegionalizationValidator pRegionalizationValidator,
      final HostSvc pHostSvc,
      final LegacyHostSvc pLegacyHostSvc,
      final PerformanceAdvisorFeedbackSvc pPAFeedbackSvc,
      final SchemaSuggestionsSvc pSchemaSuggestionsSvc,
      final DropIndexSuggestionsSvc pDropIndexSuggestionsSvc,
      final ServerlessAccessUtil pServerlessAccessUtil,
      final CreateIndexSuggestionsSvc pCreateIndexSuggestionsSvc,
      final TextQueryMetricsSvc pTextQueryMetricsSvc,
      final DataServicesSuggestionsSvc pDataServicesSuggestionsSvc,
      final ServerlessAutoIndexingSvc pServerlessAutoIndexingSvc,
      final PerformanceAdvisorSvc pPerformanceAdvisorSvc,
      final HostClusterSvc pHostClusterSvc,
      final GroupSvc pGroupSvc,
      final CCPAIndexSuggestionSvc pCCPAIndexSuggestionSvc,
      final CCPADropIndexSuggestionsSvc pCCPADropIndexSuggestionsSvc,
      final CCPASchemaSuggestionsSvc pCCPASchemaSuggestionSvc,
      final ObjectMapper pObjectMapper,
      final AgentJobSvc pAgentJobSvc,
      final AgentJobsProcessorSvc pAgentJobsProcessorSvc,
      final ShardingConfigDao pShardingConfigDao) {
    _settings = pAppSettings;
    _regionalizationValidator = pRegionalizationValidator;
    _hostSvc = pHostSvc;
    _legacyHostSvc = pLegacyHostSvc;
    _paFeedbackSvc = pPAFeedbackSvc;
    _schemaSuggestionsSvc = pSchemaSuggestionsSvc;
    _serverlessAccessUtil = pServerlessAccessUtil;
    _dropIndexSuggestionsSvc = pDropIndexSuggestionsSvc;
    _createIndexSuggestionsSvc = pCreateIndexSuggestionsSvc;
    _textQueryMetricsSvc = pTextQueryMetricsSvc;
    _dataServicesSuggestionsSvc = pDataServicesSuggestionsSvc;
    _serverlessAutoIndexingSvc = pServerlessAutoIndexingSvc;
    _performanceAdvisorSvc = pPerformanceAdvisorSvc;
    _hostClusterSvc = pHostClusterSvc;
    _groupSvc = pGroupSvc;
    _ccpaIndexSuggestionSvc = pCCPAIndexSuggestionSvc;
    _ccpaDropIndexSuggestionsSvc = pCCPADropIndexSuggestionsSvc;
    _ccpaSchemaSuggestionSvc = pCCPASchemaSuggestionSvc;
    _objectMapper = pObjectMapper;
    _agentJobSvc = pAgentJobSvc;
    _agentJobsProcessorSvc = pAgentJobsProcessorSvc;
    _shardingConfigDao = pShardingConfigDao;
  }

  @GET
  @Path("/groups/{groupId}/hosts/{hostId}/indexCreationAdvice")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getIndexCreationAdvice.GET")
  public Response getIndexCreationAdvice(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId,
      @QueryParam("since") final Long pSince,
      @QueryParam("_fm") @DefaultValue("false") final boolean pFastMode,
      @QueryParam("_fi") @DefaultValue("") final String pFeedbackId,
      @QueryParam("_ri") @DefaultValue("") final String pRunId,
      @QueryParam("_et") @DefaultValue("") final String pEngineType,
      @QueryParam("from") @DefaultValue("emptyFrom") final String pFrom)
      throws Exception {

    GET_INDEX_CREATION_ADVICE_REQUESTS_COUNTER.labels(pFrom).inc();

    final RequestParams params = getRequestParams(pRequest);
    final AppUser user = params.getAppUser();
    final Group group = params.getCurrentGroup();
    final Organization org = params.getCurrentOrganization();
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.PA_CREATE_INDEX_SUGGESTION);

    final Host host = _hostSvc.findHostById(pHostId, pGroupId);
    params.setHost(host);
    _legacyHostSvc.assertUserCanAccessHost(pHostId, params);
    final Instant since = validate(pSince);

    final CreateIndexesResponseView view;
    if (!pFeedbackId.isEmpty()) {
      view = _paFeedbackSvc.getStoredIndexCreationAdvice(pFeedbackId);
    } else if (!pRunId.isEmpty()) {
      view = _createIndexSuggestionsSvc.getStoredPerformanceAdvisorRun(pRunId, pGroupId);
    } else {
      // Fast mode should not need a user in order to run. HostHasIndexSuggestions runs
      // PA in fast mode and also passes null for user, so keeping it as null for fast mode
      // here as well for consistency.
      view =
          _createIndexSuggestionsSvc.getIndexCreationAdvice(
              host, pGroup, since, pFastMode ? null : user, pOrganization, pFastMode, pEngineType);
    }

    return Response.ok(view).build();
  }

  // this endpoint is used for Atlas Home Page Prod tips
  @GET
  @Path("/groups/{groupId}/suggestedIndexes")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature({FeatureFlag.PERFORMANCE_ADVISOR, FeatureFlag.CLUSTER_CENTRIC_PERFORMANCE_ADVISOR})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getSuggestedIndexes.GET")
  public Response getSuggestedIndexes(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pCurrentUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterId") final ObjectId pClusterId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("hostType") final HostType pHostType,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("_fm") @DefaultValue("false") final boolean pFastMode,
      @QueryParam("_fi") @DefaultValue("") final String pFeedbackId,
      @QueryParam("_ri") @DefaultValue("") final String pRunId,
      @QueryParam("from") @DefaultValue("emptyFrom") final String pFrom)
      throws Exception {
    CreateIndexesResponseView view =
        getIndexCreationAdviceForClusterUtil(
            pRequest,
            pOrganization,
            pGroup,
            pCurrentUser,
            pGroupId,
            pClusterId,
            pHostIds,
            pHostType,
            pSince,
            pUntil,
            pFastMode,
            pFeedbackId,
            pRunId,
            pFrom,
            true,
            RegionalEndpoint.CCPA_CREATE_INDEX_SUGGESTION_ATLAS_HOME_PAGE,
            null);
    return Response.ok().entity(view.getSuggestedIndexes()).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterLevelIndexCreationAdvice")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature({FeatureFlag.PERFORMANCE_ADVISOR, FeatureFlag.CLUSTER_CENTRIC_PERFORMANCE_ADVISOR})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getIndexCreationAdviceForCluster.GET")
  public Response getIndexCreationAdviceForCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pCurrentUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterId") final ObjectId pClusterId,
      @QueryParam("hostIds[]") final List<String> pHostIds,
      @QueryParam("hostType") final HostType pHostType,
      @QueryParam("since") final Long pSince,
      @QueryParam("until") final Long pUntil,
      @QueryParam("_fm") @DefaultValue("false") final boolean pFastMode,
      @QueryParam("_fi") @DefaultValue("") final String pFeedbackId,
      @QueryParam("_ri") @DefaultValue("") final String pRunId,
      @QueryParam("from") @DefaultValue("emptyFrom") final String pFrom,
      @QueryParam("_qs") @DefaultValue("none") final String pForceQueryStats)
      throws Exception {
    CreateIndexesResponseView view =
        getIndexCreationAdviceForClusterUtil(
            pRequest,
            pOrganization,
            pGroup,
            pCurrentUser,
            pGroupId,
            pClusterId,
            pHostIds,
            pHostType,
            pSince,
            pUntil,
            pFastMode,
            pFeedbackId,
            pRunId,
            pFrom,
            false,
            RegionalEndpoint.CCPA_CREATE_INDEX_SUGGESTION_UI,
            pForceQueryStats);
    return Response.ok(view).build();
  }

  @GET
  @Path("/groups/{groupId}/serverless/{clusterUniqueId}/indexCreationAdvice")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getIndexCreationAdviceServerless.GET")
  public Response getIndexCreationAdviceServerless(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @QueryParam("since") final Long pSince,
      @QueryParam("_fm") @DefaultValue("false") final boolean pFastMode,
      @QueryParam("_fi") @DefaultValue("") final String pFeedbackId,
      @QueryParam("_ri") @DefaultValue("") final String pRunId)
      throws Exception {

    final RequestParams params = getRequestParams(pRequest);
    final AppUser user = params.getAppUser();
    final Instant since = validate(pSince);

    _serverlessAccessUtil.assertAccessToServerlessCluster(pGroup, user, pClusterUniqueId);

    final CreateIndexesResponseView view =
        pRunId.isEmpty()
            ? _createIndexSuggestionsSvc.getIndexCreationAdviceForServerless(
                pClusterUniqueId, pGroup, since, pFastMode ? null : user, pOrganization, pFastMode)
            : _createIndexSuggestionsSvc.getStoredPerformanceAdvisorRun(pRunId, pGroupId);

    return Response.ok(view).build();
  }

  @GET
  @Path("/groups/{groupId}/hosts/{hostId}/dropIndexes")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getIndexRemovalAdvice.GET")
  public Response getIndexRemovalAdvice(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    final Group group = params.getCurrentGroup();
    final Organization org = params.getCurrentOrganization();

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        org, group, false, RegionalEndpoint.PA_DROP_INDEX_SUGGESTION);
    final AppUser user = params.getAppUser();
    final Host host = _hostSvc.findHostById(pHostId, pGroupId);

    params.setHost(host);
    _legacyHostSvc.assertUserCanAccessHost(pHostId, params);

    final DropIndexesResponseView view =
        _dropIndexSuggestionsSvc.getIndexRemovalAdvice(host, pGroup, user, pOrganization);

    return Response.ok(view).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterLevelDropIndexes")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getIndexRemovalAdviceForCluster.GET")
  public Response getIndexRemovalAdviceForCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pCurrentUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterId") final ObjectId pClusterId,
      @QueryParam("hostId") final String pHostId)
      throws Exception {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrganization, pGroup, false, RegionalEndpoint.CCPA_DROP_INDEX_SUGGESTION);

    LOG.info(
        "getIndexRemovalAdviceForCluster: request received. GroupId={}, ClusterId={}, HostId={}",
        pGroupId,
        pClusterId,
        pHostId);

    if ((pHostId == null || pHostId.isEmpty()) && pClusterId == null) {
      LOG.warn("No clusterId or hostIds[] provided");
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var hostAndHostClusterPair =
        _ccpaSchemaSuggestionSvc.validateParams(pGroupId, pClusterId, pHostId);
    final Host standaloneHost = hostAndHostClusterPair.getRight();
    final HostCluster hostCluster = hostAndHostClusterPair.getLeft();

    _ccpaDropIndexSuggestionsSvc.checkAgentSupportsListIndexStatsWithMultipleNamespaces(pGroup);

    DropIndexesResponseView view =
        _ccpaDropIndexSuggestionsSvc.getClusterCentricIndexRemovalAdvice(
            standaloneHost,
            hostCluster,
            pGroup,
            pCurrentUser,
            pOrganization,
            IndexSuggestionEngineType.CCPA_UI);

    return Response.ok(view).build();
  }

  @GET
  @Path("/groups/{groupId}/hosts/{hostId}/schemaAdvice")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.SCHEMA_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getSchemaAdvice.GET")
  public Response getSchemaAdvice(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId)
      throws Exception {
    LOG.info("Schema Advisor: request first received at {}", System.currentTimeMillis());
    final RequestParams params = getRequestParams(pRequest);

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        params.getCurrentOrganization(),
        params.getCurrentGroup(),
        false,
        RegionalEndpoint.PA_SCHEMA_SUGGESTION);
    final AppUser user = params.getAppUser();
    final Host host = _hostSvc.findHostById(pHostId, pGroupId);
    params.setHost(host);
    _legacyHostSvc.assertUserCanAccessHost(pHostId, params);

    final SchemaSuggestionView view =
        _schemaSuggestionsSvc.getSchemaAdvice(host, pGroup, user, pOrganization);

    return Response.ok(view).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterLevelSchemaAdvice")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(auditableRoles = RoleSet.PII_AUDITABLE, auditEventType = Type.PERFORMANCE_ADVISOR)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getSchemaAdviceForCluster.GET")
  public Response getSchemaAdviceForCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterId") final ObjectId pClusterId,
      @QueryParam("hostId") final String pHostId)
      throws Exception {
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrganization, pGroup, false, RegionalEndpoint.CCPA_SCHEMA_SUGGESTION);

    LOG.info(
        "getSchemaAdviceForCluster: request received. GroupId={}, ClusterId={}",
        pGroupId.toString(),
        pClusterId);

    final var hostAndHostClusterPair =
        _ccpaSchemaSuggestionSvc.validateParams(pGroupId, pClusterId, pHostId);
    final Host host = hostAndHostClusterPair.getRight();
    final HostCluster cluster = hostAndHostClusterPair.getLeft();

    // check user has access to group
    if (!_groupSvc.canUserAccessGroup(pUser, pGroup)) {
      LOG.warn("current user={} can't access Group={}", pUser, pGroup);
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final SchemaSuggestionView view =
        _ccpaSchemaSuggestionSvc.getClusterCentricSchemaAdvice(
            host, cluster, pGroup, pUser, pOrganization, IndexSuggestionEngineType.CCPA_UI);

    return Response.ok(view).build();
  }

  private static Instant validate(final Long pSince) throws SvcException {
    if (pSince == null) {
      return now().minus(ofDays(1));
    }

    Instant since = Instant.ofEpochMilli(pSince);

    if (now().isBefore(since)) {
      LOG.warn("The user provided a future value {} for the 'since' parameter", pSince);
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    return since;
  }

  @POST
  @Path("/groups/{groupId}/saveCreatedIndex/{runId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PA_BUGS_AND_SUSTAINABILITY)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.saveCreatedIndex.POST")
  public Response saveCreatedIndex(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("runId") final ObjectId pRunId,
      final PerformanceAdvisorIndexView pIndexView)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        params.getCurrentOrganization(),
        params.getCurrentGroup(),
        false,
        RegionalEndpoint.PA_SAVE_CREATED_INDEX);
    _createIndexSuggestionsSvc.saveCreatedIndex(pGroup, pRunId, pIndexView);
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/hosts/{hostId}/feedback")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PA_FEEDBACK)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.saveFeedbackHostLevel.POST")
  public Response saveFeedbackHostLevel(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId,
      final PerformanceAdvisorFeedbackView pFeedbackView)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        params.getCurrentOrganization(),
        params.getCurrentGroup(),
        false,
        RegionalEndpoint.PA_SAVE_HOST_FEEDBACK);

    final AppUser appUser = params.getAppUser();
    final String user = appUser.getUsername();
    final PerformanceAdvisorFeedbackResponseView feedbackResponseView =
        _paFeedbackSvc.submitFeedback(pFeedbackView, pGroup, pHostId, null, user);
    return Response.ok(feedbackResponseView).build();
  }

  @POST
  @Path("/groups/{groupId}/clusters/{clusterId}/feedback")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PA_FEEDBACK)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.saveFeedback.POST")
  public Response saveFeedback(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId,
      final PerformanceAdvisorFeedbackView pFeedbackView)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        params.getCurrentOrganization(),
        params.getCurrentGroup(),
        false,
        RegionalEndpoint.PA_SAVE_CLUSTER_FEEDBACK);

    final AppUser appUser = params.getAppUser();
    final String user = appUser.getUsername();
    final PerformanceAdvisorFeedbackResponseView feedbackResponseView =
        _paFeedbackSvc.submitFeedback(pFeedbackView, pGroup, null, pClusterId, user);
    return Response.ok(feedbackResponseView).build();
  }

  @GET
  @Path("/groups/{groupId}/hosts/{hostId}/buildInProgress")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getBuildIndexStatus.GET")
  public Response getBuildIndexStatus(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId,
      @QueryParam("namespaces[]") final List<String> pNamespaces)
      throws Exception {

    final RequestParams params = getRequestParams(pRequest);
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        params.getCurrentOrganization(),
        params.getCurrentGroup(),
        false,
        RegionalEndpoint.PA_BUILD_INDEX_STATUS);

    final AppUser user = params.getAppUser();
    final Host host = _hostSvc.findHostById(pHostId, pGroupId);
    params.setHost(host);
    _legacyHostSvc.assertUserCanAccessHost(pHostId, params);

    final PerformanceAdvisorBuildingIndexResponseView resp =
        _createIndexSuggestionsSvc.checkIndexBuildInProgress(
            pOrganization, pGroup, user, host, new HashSet<>(pNamespaces));

    return Response.ok(resp).build();
  }

  @GET
  @Path("/groups/{groupId}/serverless/{clusterUniqueId}/buildInProgress")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getServerlessBuildIndexStatus.GET")
  public Response getServerlessBuildIndexStatus(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @QueryParam("namespaces[]") final List<String> pNamespaces)
      throws Exception {

    final RequestParams params = getRequestParams(pRequest);
    final AppUser user = params.getAppUser();
    _serverlessAccessUtil.assertAccessToServerlessCluster(pGroup, user, pClusterUniqueId);

    final PerformanceAdvisorBuildingIndexResponseView resp =
        _createIndexSuggestionsSvc.checkIndexBuildInProgress(
            pOrganization, pGroup, user, pClusterUniqueId, new HashSet<>(pNamespaces));

    return Response.ok(resp).build();
  }

  @POST
  @Path("/groups/{groupId}/hosts/textQueryUsage")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getTextQueryUsage.POST")
  public Response getTextQueryUsage(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      final Map<String, List<String>> rsIdToHostIds)
      throws Exception {

    final Map<String, Boolean> replicaSetsExceedingThreshold =
        _dataServicesSuggestionsSvc.getReplicaSetsExceedingThreshold(rsIdToHostIds, pGroup);

    return Response.ok(
            new PerformanceAdvisorTextQueryUsageResponseView(replicaSetsExceedingThreshold))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/hosts/{hostId}/dataServicesAdvice")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.PERFORMANCE_ADVISOR_RECOMMEND_SEARCH)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @AccessAudit(
      auditableRoles = RoleSet.PII_AUDITABLE,
      auditEventType = AccessEvent.Type.PERFORMANCE_ADVISOR,
      hostSource = AccessAudit.HostSource.PATH)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getDataServicesAdvice.GET")
  public Response getDataServicesAdvice(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId)
      throws Exception {
    LOG.info("Leverage Data Services: request first received at {}", System.currentTimeMillis());
    final RequestParams params = getRequestParams(pRequest);
    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        params.getCurrentOrganization(),
        params.getCurrentGroup(),
        false,
        RegionalEndpoint.PA_DATA_SERVICE_SUGGESTION);
    final AppUser user = params.getAppUser();
    final Host host = _hostSvc.findHostById(pHostId, pGroupId);
    params.setHost(host);
    _legacyHostSvc.assertUserCanAccessHost(pHostId, params);

    final SchemaSuggestionView view =
        _dataServicesSuggestionsSvc.getDataServiceAdvice(host, pGroup, user, pOrganization);

    return Response.ok(view).build();
  }

  @POST
  @Path("/groups/{groupId}/serverless/{clusterUniqueId}/autoIndexing")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.setServerlessAutoIndexing.POST")
  public Response setServerlessAutoIndexing(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      final Boolean pEnable)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    final AppUser user = params.getAppUser();
    _serverlessAccessUtil.assertAccessToServerlessCluster(pGroup, user, pClusterUniqueId);

    _serverlessAutoIndexingSvc.setServerlessAutoIndexingState(
        pOrganization.getId(), pGroupId, pClusterUniqueId, pEnable);

    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/serverless/{clusterUniqueId}/autoIndexing")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.PerformanceAdvisorResource.getServerlessAutoIndexing.GET")
  public Response getServerlessAutoIndexing(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    final AppUser user = params.getAppUser();
    _serverlessAccessUtil.assertAccessToServerlessCluster(pGroup, user, pClusterUniqueId);

    final AutoIndexingState state =
        _serverlessAutoIndexingSvc.getServerlessAutoIndexingState(pGroupId, pClusterUniqueId);
    if (state == null) {
      throw new SvcException(CommonErrorCode.NOT_FOUND);
    }

    // temporary, remove after serverless auto indexing rollout
    final double groupPercentageAllowed =
        _settings.getDoubleProp(SERVERLESS_AUTO_INDEXING_PERCENTAGE_ALLOWED_KEY, 0);

    final boolean showBanner =
        isFeatureFlagEnabled(FeatureFlag.SERVERLESS_AUTO_INDEXING, _settings, pOrganization, pGroup)
            || isHashedIdWithinPercentage(pGroup.getId(), groupPercentageAllowed);

    return Response.ok().entity(new AutoIndexingStateForRollout(state, showBanner)).build();
  }

  private CreateIndexesResponseView getIndexCreationAdviceForClusterUtil(
      @Nonnull HttpServletRequest pRequest,
      Organization pOrganization,
      Group pGroup,
      AppUser pCurrentUser,
      @Nonnull ObjectId pGroupId,
      ObjectId pClusterId,
      List<String> pHostIds,
      HostType pHostType,
      Long pSince,
      Long pUntil,
      boolean pFastMode,
      String pFeedbackId,
      String pRunId,
      String pFrom,
      boolean forceNoPII,
      RegionalEndpoint pRegionalEndpoint,
      String forceQueryStats)
      throws SvcException {
    GET_INDEX_CREATION_ADVICE_REQUESTS_COUNTER.labels(pFrom).inc();

    _regionalizationValidator.validateDedicatedRequestCanBeProcessed(
        pOrganization, pGroup, false, pRegionalEndpoint);
    LOG.info(
        "getIndexCreationAdviceForCluster: request received. GroupId={}, ClusterId={}, HostType="
            + "{}, Since={}, Until={}",
        pGroupId,
        pClusterId,
        pHostType,
        pSince,
        pUntil);

    final HostCluster hostCluster = _hostClusterSvc.findByClusterId(pGroupId, pClusterId);
    if ((pHostIds == null || pHostIds.isEmpty()) && hostCluster == null) { // to handle standalones
      LOG.warn("Unable to find cluster with clusterId: {}", pClusterId);
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var triple =
        _ccpaIndexSuggestionSvc.validateHostsAndCluster(pHostIds, pGroupId, hostCluster);
    final RollupType rollupType = triple.getRight();
    final List<Host> hosts = triple.getMiddle();
    final HostCluster cluster = triple.getLeft();

    // check user has access to group
    if (!_groupSvc.canUserAccessGroup(pCurrentUser, pGroup)) {
      LOG.warn("current user={} can't access Group={}", pCurrentUser, pGroup);
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final Pair<Instant, Instant> sinceUntilPair =
        _ccpaIndexSuggestionSvc.validateSinceAndUntil(pSince, pUntil);
    final Instant since = sinceUntilPair.getLeft();
    final Instant until = sinceUntilPair.getRight();

    final CreateIndexesResponseView view;
    if (!pFeedbackId.isEmpty()) {
      view = _paFeedbackSvc.getStoredIndexCreationAdvice(pFeedbackId);
    } else if (!pRunId.isEmpty()) {
      view = _createIndexSuggestionsSvc.getStoredPerformanceAdvisorRun(pRunId, pGroupId);
    } else {
      // Fast mode should not need a user in order to run. HostHasIndexSuggestions runs
      // PA in fast mode and also passes null for user, so keeping it as null for fast mode
      // here as well for consistency.
      view =
          _ccpaIndexSuggestionSvc.getIndexCreationAdviceForCCPA(
              hosts,
              cluster,
              pClusterId,
              pGroup,
              since,
              until,
              pFastMode ? null : pCurrentUser,
              pOrganization,
              pFastMode ? IndexSuggestionEngineType.CCPA_ALERTS : IndexSuggestionEngineType.CCPA_UI,
              rollupType,
              new ArrayList<>(),
              forceNoPII,
              forceQueryStats);
    }

    return view;
  }
}
