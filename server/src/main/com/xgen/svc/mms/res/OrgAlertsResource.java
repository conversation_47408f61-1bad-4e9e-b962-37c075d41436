/* (C) Copyright 2011, 10gen */

package com.xgen.svc.mms.res;

import static com.xgen.cloud.activity._public.model.alert.config.AlertConfigSource.UI;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.alert.Alert;
import com.xgen.cloud.activity._public.model.alert.AlertsContainer;
import com.xgen.cloud.activity._public.model.alert.TestAlert;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfig;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfigHistory;
import com.xgen.cloud.activity._public.model.event.EventScope;
import com.xgen.cloud.activity._public.svc.alert.AlertConfigHistorySvc;
import com.xgen.cloud.alerts.alert._public.model.AlertAcknowledgementForm;
import com.xgen.cloud.alerts.alert._public.model.AlertAcknowledgementResponse;
import com.xgen.cloud.alerts.alert._public.svc.AlertConfigSvc;
import com.xgen.cloud.alerts.notify._public.svc.NotificationSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.common.resource._public.model.ResourceId.ResourceType;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.integration._public.svc.IntegrationSvc;
import com.xgen.cloud.notification._public.model.Notification;
import com.xgen.cloud.notification._public.model.SlackNotification;
import com.xgen.cloud.notification._public.model.TeamNotification;
import com.xgen.cloud.notification._public.transformer.NotificationViewTransformer;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.OrgAlertAuthorizationSvc.AvailableAlertTarget;
import com.xgen.svc.mms.svc.TeamSvc;
import com.xgen.svc.mms.svc.alert.OrgAlertSvc;
import com.xgen.svc.mms.svc.common.OrgErrorCode;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/orgs/{orgId}/alerts")
@Singleton
public class OrgAlertsResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(HostResource.class);
  private final OrgAlertSvc _orgAlertSvc;
  private final AlertConfigSvc _alertConfigSvc;
  private final AlertConfigHistorySvc _alertConfigHistorySvc;
  private final NotificationSvc _notificationSvc;
  private final ConfigLimitSvc _configLimitSvc;
  private final OrgAlertAuthorizationSvc _alertAuthSvc;
  private final TeamSvc _teamSvc;
  private final IntegrationSvc integrationSvc;
  private final NotificationViewTransformer notificationViewTransformer;

  @Inject
  public OrgAlertsResource(
      final OrgAlertSvc pOrgAlertSvc,
      final AlertConfigSvc pAlertConfigSvc,
      final AlertConfigHistorySvc pAlertConfigHistorySvc,
      final NotificationSvc pNotificationSvc,
      final ConfigLimitSvc pConfigLimitSvc,
      final OrgAlertAuthorizationSvc pOrgAlertAuthorizationSvc,
      final TeamSvc pTeamSvc,
      final IntegrationSvc integrationSvc,
      final NotificationViewTransformer notificationViewTransformer) {
    _orgAlertSvc = pOrgAlertSvc;
    _alertConfigSvc = pAlertConfigSvc;
    _alertConfigHistorySvc = pAlertConfigHistorySvc;
    _notificationSvc = pNotificationSvc;
    _configLimitSvc = pConfigLimitSvc;
    _alertAuthSvc = pOrgAlertAuthorizationSvc;
    _teamSvc = pTeamSvc;
    this.integrationSvc = integrationSvc;
    this.notificationViewTransformer = notificationViewTransformer;
  }

  @GET
  @Path("/list")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getAllAlerts.GET")
  public Response getAllAlerts(
      @QueryParam("beforeId") final ObjectId pBeforeId,
      @QueryParam("afterId") final ObjectId pAfterId,
      @QueryParam("limit") @DefaultValue("20") int pLimit,
      @QueryParam("status") final List<Alert.Status> pStatus,
      @QueryParam("acked") final boolean pAcked,
      @PathParam("orgId") final ObjectId pOrgId) {
    final AlertsContainer ret =
        getOrgAlertSvc().getAllAlerts(pOrgId, pLimit, pStatus, pAcked, pBeforeId, pAfterId);

    return Response.ok().entity(ret).build();
  }

  @GET
  @Path("/{alertId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getAlert.GET")
  public Response getAlert(@PathParam("alertId") final ObjectId alertId) {
    final Alert ret = getOrgAlertSvc().findById(alertId);

    return Response.ok().entity(ret).build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/configs")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.createAlertConfig.POST")
  public Response createAlertConfig(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      final AlertConfig pAlertConfig)
      throws SvcException {

    _alertAuthSvc.authorizeAlertConfig(pUser, pOrganization, pAlertConfig);

    final List<Notification> notifications =
        (pAlertConfig != null && pAlertConfig.hasNotifications())
            ? notificationViewTransformer.fillRedactedCredentials(
                pAlertConfig.getNotifications(),
                List.of(new ResourceId(ResourceType.ORGANIZATION, pOrganization.getId())),
                new ArrayList<>(),
                pAlertConfig.isGlobalAlertConfig() || pAlertConfig.isSystemAlertConfig())
            : List.of();

    final AlertConfig alertConfigWithCredentials =
        pAlertConfig.copier().notifications(notifications).source(UI).build();
    final Map<String, Object> errors =
        getAlertConfigSvc().validateOrgAlertConfig(pOrganization, alertConfigWithCredentials);
    errors.putAll(
        validateTeamAlertConfigs(alertConfigWithCredentials.getNotifications(), pOrganization));

    if (!errors.isEmpty()) {
      throw new SvcException(
          OrgErrorCode.INVALID_ORG_ALERT_CONFIG, new JSONObject(errors).toString());
    }
    getConfigLimitSvc()
        .validateDefaultMaxNotificationsPerAlert(pAlertConfig.getNotifications().size());
    getAlertConfigSvc().saveOrgAlertConfig(alertConfigWithCredentials, pOrganization, pAuditInfo);
    // send the original alert config notifications back so it doesn't contain extra credentials
    return Response.ok()
        .entity(
            alertConfigWithCredentials
                .copier()
                .notifications(pAlertConfig.getNotifications())
                .build())
        .build();
  }

  @PUT
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/configs/{configId}")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.updateAlertConfig.PUT")
  public Response updateAlertConfig(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("configId") final ObjectId pAlertConfigId,
      final AlertConfig pAlertConfig)
      throws SvcException {

    _alertAuthSvc.authorizeAlertConfig(pUser, pOrganization, pAlertConfig);
    final AlertConfig oldAlertConfig =
        _alertConfigSvc.getOrgAlertConfig(pOrganization.getId(), pAlertConfigId);

    final List<Notification> notifications =
        (pAlertConfig != null && pAlertConfig.hasNotifications())
            ? notificationViewTransformer.fillRedactedCredentials(
                pAlertConfig.getNotifications(),
                List.of(new ResourceId(ResourceType.ORGANIZATION, pOrganization.getId())),
                oldAlertConfig.getNotifications(),
                pAlertConfig.isGlobalAlertConfig() || pAlertConfig.isSystemAlertConfig())
            : List.of();

    final AlertConfig alertConfigWithCredentials =
        pAlertConfig.copier().notifications(notifications).source(UI).build();

    final Map<String, Object> errors =
        getAlertConfigSvc().validateOrgAlertConfig(pOrganization, alertConfigWithCredentials);
    errors.putAll(
        validateTeamAlertConfigs(alertConfigWithCredentials.getNotifications(), pOrganization));

    if (!errors.isEmpty()) {
      throw new SvcException(
          OrgErrorCode.INVALID_ORG_ALERT_CONFIG, new JSONObject(errors).toString());
    }
    getConfigLimitSvc()
        .validateMaxNotificationsPerAlert(
            alertConfigWithCredentials.getNotifications().size(), pAlertConfigId);
    getAlertConfigSvc().updateOrgAlertConfig(pOrganization, alertConfigWithCredentials, pAuditInfo);

    _alertConfigSvc.removeUnusedIntegrations(oldAlertConfig, alertConfigWithCredentials);
    // send the original alert config notifications back so it doesn't contain extra credentials
    return Response.ok()
        .entity(
            alertConfigWithCredentials
                .copier()
                .notifications(pAlertConfig.getNotifications())
                .build())
        .build();
  }

  @DELETE
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/configs/{configId}")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.deleteAlertConfig.DELETE")
  public Response deleteAlertConfig(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("configId") final ObjectId pConfigId) {
    final AlertConfig alertConfig =
        getAlertConfigSvc().getOrgAlertConfig(pOrganization.getId(), pConfigId);

    if (alertConfig == null) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "No alert configuration with ID %s exists in organization %s.",
                  pConfigId, pOrganization.getId()))
          .build();
    }

    _alertAuthSvc.authorizeAlertConfig(pUser, pOrganization, alertConfig);

    getAlertConfigSvc().deleteOrgAlertConfig(pOrganization, alertConfig, pAuditInfo);
    _alertConfigSvc.removeUnusedIntegrations(alertConfig, null);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/ack")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.acknowledgeAlert.POST")
  public Response acknowledgeAlert(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      final AlertAcknowledgementForm pAlertAcknowledgementForm)
      throws SvcException {

    for (ObjectId id : pAlertAcknowledgementForm.getSelectedIds()) {
      _alertAuthSvc.authorizeAlert(pUser, pOrganization, id);
    }

    final AlertAcknowledgementResponse response =
        getOrgAlertSvc()
            .acknowledgeAlerts(
                pOrganization.getId(), pAlertAcknowledgementForm, pUser.getUsername(), pAuditInfo);
    return Response.ok().entity(response).build();
  }

  @POST
  @Path("/ackAll")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.acknowledgeAllAlerts.POST")
  public Response acknowledgeAllAlerts(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      final AlertAcknowledgementForm pAlertAcknowledgementForm)
      throws SvcException {

    Set<ObjectId> alertIds = _orgAlertSvc.getAllUnackAlertIds(pOrganization.getId());

    for (ObjectId id : alertIds) {
      _alertAuthSvc.authorizeAlert(pUser, pOrganization, id);
    }

    final AlertAcknowledgementResponse response =
        getOrgAlertSvc()
            .acknowledgeAllAlerts(
                pOrganization.getId(),
                alertIds,
                pAlertAcknowledgementForm,
                pUser.getUsername(),
                pAuditInfo);
    return Response.ok().entity(response).build();
  }

  @POST
  @Path("/unack")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.unacknowledgeAlert.POST")
  public Response unacknowledgeAlert(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      final AlertAcknowledgementForm pAlertAcknowledgementForm) {

    for (ObjectId id : pAlertAcknowledgementForm.getSelectedIds()) {
      _alertAuthSvc.authorizeAlert(pUser, pOrganization, id);
    }

    getOrgAlertSvc()
        .unacknowledgeAlerts(
            pOrganization.getId(), pAlertAcknowledgementForm.getSelectedIds(), pAuditInfo);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/unackAll")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.unacknowledgeAllAlerts.POST")
  public Response unacknowledgeAllAlerts(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser) {

    Set<ObjectId> alertIds = _orgAlertSvc.getAllAckAlertIds(pOrganization.getId());
    for (ObjectId id : alertIds) {
      _alertAuthSvc.authorizeAlert(pUser, pOrganization, id);
    }

    getOrgAlertSvc().unacknowledgeAlerts(pOrganization.getId(), alertIds, pAuditInfo);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/openAlertsCount")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getOpenAlertsCount.GET")
  public Response getOpenAlertsCount(@PathParam("orgId") final ObjectId pOrgId) {
    final long count = getOrgAlertSvc().getOpenAlertsCount(pOrgId);
    return Response.ok().entity(count).build();
  }

  @GET
  @Path("/configs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getAlertConfigs.GET")
  public Response getAlertConfigs(@PathParam("orgId") final ObjectId pOrgId) {

    final List<AlertConfig> alertConfigs =
        getAlertConfigSvc().findByOrgId(pOrgId).stream()
            .filter(AlertConfig::isOrgAlertConfig)
            .collect(Collectors.toList());
    return Response.ok().entity(alertConfigs).build();
  }

  @GET
  @Path("/configs/{configId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getAlertConfig.GET")
  public Response getAlertConfig(
      @PathParam("configId") final ObjectId pConfigId, @PathParam("orgId") final ObjectId pOrgId) {

    final AlertConfig alertConfig = getAlertConfigSvc().getOrgAlertConfig(pOrgId, pConfigId);

    return Response.ok().entity(alertConfig).build();
  }

  @PUT
  @Path("/configs/{configId}/disable")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.disableAlertConfig.PUT")
  public Response disableAlertConfig(
      @PathParam("configId") final ObjectId pConfigId,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo) {

    AlertConfig alertConfig = getAlertConfigSvc().findById(pConfigId);

    if (!pOrganization.getId().equals(alertConfig.getOrgId())) {
      return Response.status(Status.UNAUTHORIZED)
          .entity(
              String.format(
                  "Org ID %s not authorized to disable alert %s.",
                  pOrganization.getId(), alertConfig.getId()))
          .build();
    }

    getAlertConfigSvc().disableAlertConfig(pOrganization, null, pConfigId, pAuditInfo);

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @PUT
  @Path("/configs/{configId}/enable")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.enableAlertConfig.PUT")
  public Response enableAlertConfig(
      @PathParam("configId") final ObjectId pConfigId,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo) {

    AlertConfig alertConfig = getAlertConfigSvc().findById(pConfigId);
    if (!pOrganization.getId().equals(alertConfig.getOrgId())) {
      return Response.status(Status.UNAUTHORIZED)
          .entity(
              String.format(
                  "Org ID %s not authorized to enable alert %s.",
                  pOrganization.getId(), alertConfig.getId()))
          .build();
    }

    getAlertConfigSvc().enableAlertConfig(pOrganization, null, pConfigId, pAuditInfo);

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/configs/deleted")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ORG_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.OrgAlertsResource.getDeletedAlertConfigs.GET")
  public Response getDeletedAlertConfigs(@PathParam("orgId") final ObjectId pOrgId) {
    return Response.ok()
        .entity(getAlertConfigHistorySvc().getDeletedOrgAlertConfigs(pOrgId))
        .build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/configs/{configId}/history")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getAlertConfigHistory.GET")
  public Response getAlertConfigHistory(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("configId") final ObjectId pAlertConfigId) {

    final AlertConfigHistory alertConfigHistory =
        getAlertConfigHistorySvc().getOrgAlertConfigHistory(pOrganization.getId(), pAlertConfigId);

    // Check if alertConfigHistory is not null before accessing its methods
    if (alertConfigHistory != null) {
      if (!alertConfigHistory.getHistory().isEmpty()) {
        _alertAuthSvc.authorizeAlertConfig(
            pUser, pOrganization, alertConfigHistory.getHistory().get(0));
      } else {
        LOG.warn(
            "There are no elements in the history collection for alertConfigHistory document for"
                + " id: {}",
            pAlertConfigId);
      }
    } else {
      LOG.error("Could not retrieve alertConfigHistory document for id: {}", pAlertConfigId);
    }

    // Use Objects.requireNonNullElse to return alertConfigHistory if not null, else return
    // EMPTY_JSON_OBJECT
    return Response.ok()
        .entity(Objects.requireNonNullElse(alertConfigHistory, EMPTY_JSON_OBJECT))
        .build();
  }

  @POST
  @Path("/testNotification")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.testNotification.POST")
  public Response testNotification(
      @Context final Organization pOrg, final Notification pNotification) throws SvcException {
    test(pOrg, pNotification);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/testNotification/{alertConfigId}/{notificationId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.testExistingNotification.POST")
  public Response testExistingNotification(
      @Context final Organization pOrg,
      @PathParam("alertConfigId") final ObjectId pAlertConfigId,
      @PathParam("notificationId") final ObjectId pNotificationId,
      final Notification pNotification)
      throws SvcException {
    Notification notificationWithCredentials = pNotification;
    if (getNotificationSvc().isRedacted(pNotification)) {
      final AlertConfig alertConfig = _alertConfigSvc.findById(pAlertConfigId);
      notificationWithCredentials =
          notificationViewTransformer
              .fillRedactedCredentials(
                  List.of(pNotification),
                  new ArrayList<>(),
                  alertConfig == null ? (new ArrayList<>()) : alertConfig.getNotifications(),
                  true)
              .get(0);
    }
    test(pOrg, notificationWithCredentials);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  private void test(final Organization pOrg, final Notification pNotification) throws SvcException {
    final TestAlert.Builder alertBuilder = new TestAlert.Builder(new ObjectId());
    alertBuilder.orgId(pOrg.getId());
    alertBuilder.scope(EventScope.ORG);

    final Notification notificationWithCredentials =
        addNotificationCredentials(pOrg, pNotification);

    getNotificationSvc().test(alertBuilder.build(), notificationWithCredentials);
  }

  @GET
  @Path("/configs/availableTargets")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgAlertsResource.getAvailableTargets.GET")
  public Response getAvailableTargets(
      @Context final Organization pOrg,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser) {
    List<AvailableAlertTarget> availableAlertTargets =
        _alertAuthSvc.getAuthorizedTargets(pOrg, pUser);
    return Response.ok().entity(availableAlertTargets).build();
  }

  private Notification addNotificationCredentials(
      final Organization pOrg, final Notification pNotification) {
    if (pNotification instanceof SlackNotification) {
      final SlackNotification slackNotification = (SlackNotification) pNotification;
      return new SlackNotification(
          pOrg.getSlackApiKey(),
          slackNotification.getChannelName(),
          slackNotification.getSendIntervalInMinutes(),
          slackNotification.getSendDelayInMinutes());
    }
    return pNotification;
  }

  private Map<String, Object> validateTeamAlertConfigs(
      List<Notification> pNotifications, Organization pOrg) {
    return pNotifications.stream()
        .filter(notif -> notif instanceof TeamNotification)
        .map(notif -> getTeamSvc().findById(((TeamNotification) notif).getTeamId()))
        .filter(team -> team == null || !team.getOrgId().equals(pOrg.getId()))
        .limit(1)
        .collect(
            Collectors.toMap(
                team -> AlertConfig.NOTIFY_FIELD,
                team -> "attempts to notify a team not in the current organization"));
  }

  private OrgAlertSvc getOrgAlertSvc() {
    return _orgAlertSvc;
  }

  private AlertConfigSvc getAlertConfigSvc() {
    return _alertConfigSvc;
  }

  private AlertConfigHistorySvc getAlertConfigHistorySvc() {
    return _alertConfigHistorySvc;
  }

  private NotificationSvc getNotificationSvc() {
    return _notificationSvc;
  }

  private ConfigLimitSvc getConfigLimitSvc() {
    return _configLimitSvc;
  }

  private TeamSvc getTeamSvc() {
    return _teamSvc;
  }
}
