package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.atm.core._public.svc.ProcessesSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.ui.DeleteServerByHostnameView;
import com.xgen.svc.atm.svc.AgentsSvc;
import com.xgen.svc.atm.svc.MonitoredProcessesSvc;
import com.xgen.svc.mms.svc.ServerSvc;
import com.xgen.svc.mms.svc.ServerSvc.LegacyServerView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import org.bson.types.ObjectId;

@Path("/servers")
@Singleton
public class ServerResource extends BaseResource {

  private final ServerSvc _serverSvc;
  private final MonitoredProcessesSvc _monitoredProcessesSvc;
  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final ProcessesSvc _processesSvc;
  private final AgentsSvc _agentsSvc;

  @Inject
  public ServerResource(
      final ServerSvc pServerSvc,
      final MonitoredProcessesSvc pMonitoredProcessesSvc,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final ProcessesSvc pProcessesSvc,
      final AgentsSvc pAgentsSvc) {
    _serverSvc = pServerSvc;
    _monitoredProcessesSvc = pMonitoredProcessesSvc;
    _automationConfigSvc = pAutomationConfigSvc;
    _processesSvc = pProcessesSvc;
    _agentsSvc = pAgentsSvc;
  }

  @GET
  @Path("/list/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerResource.servers.GET")
  public Response servers(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @QueryParam("batchId") final String pBatchId,
      @QueryParam("mapProcesses") @DefaultValue("false") final boolean pMapProcesses,
      @QueryParam("mapAutomationAgents") @DefaultValue("false") final boolean pMapAutomationAgents,
      @QueryParam("mapBackupAgents") @DefaultValue("false") final boolean pMapBackupAgents,
      @QueryParam("mapMonitoringAgents") @DefaultValue("false") final boolean pMapMonitoringAgents)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);

    final ObjectId batchId;
    if (pBatchId == null || pBatchId.isEmpty()) {
      batchId = null;
    } else {
      batchId = new ObjectId(pBatchId);
    }

    final List<LegacyServerView> servers =
        _serverSvc.buildLegacyServersList(
            pGroup,
            pUser,
            batchId,
            pMapProcesses,
            pMapAutomationAgents,
            pMapBackupAgents,
            pMapMonitoringAgents);

    return Response.ok().entity(servers).build();
  }

  @GET
  @Path("/cards/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerResource.getServerCards.GET")
  public Response getServerCards(@Context final Group pGroup, @Context final AppUser pUser) {
    return Response.ok().entity(_serverSvc.buildServersList(pGroup, pUser)).build();
  }

  @POST
  @Path("/{groupId}/delete")
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Produces({MediaType.APPLICATION_JSON})
  @Auth(endpointAction = "epa.project.ServerResource.deleteServerByHostname.POST")
  public Response deleteServerByHostname(
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final AuditInfo auditInfo,
      final DeleteServerByHostnameView pView)
      throws SvcException {
    _monitoredProcessesSvc.deleteMonitoredProcessesByHostname(
        pGroup, pView.getHostname(), auditInfo);

    final AutomationConfig config =
        _automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
    _processesSvc.deleteProcessesByHostname(config, pView.getHostname());
    _agentsSvc.deleteBackupAgent(config, pView.getHostname());
    _agentsSvc.deleteMonitoringAgent(config, pView.getHostname());
    _automationConfigSvc.saveDraft(config, pUser, pOrg, pGroup);
    return Response.ok().build();
  }
}
