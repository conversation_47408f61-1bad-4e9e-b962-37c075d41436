/* (C) Copyright 2013, MongoDB, Inc. */

package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.email.TemplateMap;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.email._public.model.EmailMsg;
import com.xgen.cloud.email._public.svc.EmailSvc;
import com.xgen.cloud.email._public.svc.GroupEmailSvc;
import com.xgen.cloud.email._public.svc.template.HandlebarsTemplateSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.metrics._public.model.ChartDef;
import com.xgen.cloud.monitoring.metrics._public.model.ChartTemplate;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.dao.ChartBookmarkDao;
import com.xgen.svc.mms.dao.ChartDefDao;
import com.xgen.svc.mms.model.ChartBookmark;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.net.URI;
import java.util.Date;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/chart")
@Singleton
public class RrdChartResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(RrdChartResource.class);

  private final HandlebarsTemplateSvc _templateSvc;
  private final ChartDefDao _chartDefDao;
  private final EmailSvc _emailSvc;
  private final ChartBookmarkDao _chartBookmarkDao;
  private final HostSvc _hostSvc;
  private final GroupSvc _groupSvc;
  private final GroupEmailSvc _groupEmailSvc;
  private final AppSettings _appSettings;

  @Inject
  public RrdChartResource(
      final HandlebarsTemplateSvc pTemplateSvc,
      final ChartDefDao pChartDefDao,
      final EmailSvc pEmailSvc,
      final ChartBookmarkDao pChartBookmarkDao,
      final HostSvc pHostSvc,
      final GroupSvc pGroupSvc,
      final GroupEmailSvc pGroupEmailSvc,
      final AppSettings pAppSettings) {
    _templateSvc = pTemplateSvc;
    _chartDefDao = pChartDefDao;
    _emailSvc = pEmailSvc;
    _chartBookmarkDao = pChartBookmarkDao;
    _hostSvc = pHostSvc;
    _groupSvc = pGroupSvc;
    _groupEmailSvc = pGroupEmailSvc;
    _appSettings = pAppSettings;
  }

  @GET
  @Path("/bookmark/{groupId}/{bookmarkId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.RrdChartResource.bookmark.GET")
  public Response bookmark(
      @Context final Group pGroup, @PathParam("bookmarkId") final String pBookmarkId)
      throws Exception {
    final ChartBookmark bookmark = _chartBookmarkDao.findById(new ObjectId(pBookmarkId));
    if (bookmark == null) {
      return Response.temporaryRedirect(new URI("/")).build();
    }
    return Response.temporaryRedirect(
            new URI(
                String.format(
                    "/v2/%s#/metrics/host/%s/status/%s",
                    pGroup.getId(), bookmark.getHostId(), bookmark.getChartId())))
        .build();
  }

  /** Create the bookmark object and return the id. */
  private ObjectId createBookmark(final String pChartParams, final ObjectId pGroupId) {

    String chartParams = pChartParams;
    if (chartParams.startsWith("https://")) {
      chartParams = chartParams.replace("https://", "");
      chartParams = chartParams.substring(chartParams.indexOf("/"), chartParams.length());
    }

    // Parse the params, store in the database and redirect user.
    final String[] chartElements = chartParams.split("/");

    final ChartBookmark bookmark = new ChartBookmark();
    bookmark.setGroupId(pGroupId);
    bookmark.setHostId(chartElements[5]);
    bookmark.setChartId(chartElements[6]);
    bookmark.setEpochType(chartElements[8]);
    bookmark.setEpochCount(Integer.parseInt(chartElements[9]));
    bookmark.setDisplayType(chartElements[10]);
    bookmark.setStartTime(new Date());

    return _chartBookmarkDao.save(bookmark);
  }

  private GroupSvc getGroupSvc() {
    return _groupSvc;
  }

  private GroupEmailSvc getGroupEmailSvc() {
    return _groupEmailSvc;
  }

  /** Email the chart to someone. */
  @GET
  @Path("/emailChart/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.RrdChartResource.emailChart.GET")
  public Response emailChart(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @QueryParam("chartParams") final String pChartParams,
      @QueryParam("toAddr") final String pToAddr,
      @QueryParam("hostId") final String pHostId,
      @QueryParam("chartId") final String pChartId,
      @QueryParam("msg") final String pMsg)
      throws Exception {
    if (pToAddr == null) {
      return SimpleApiResponse.non500Error(CommonErrorCode.INVALID_EMAIL_ADDR).build();
    }

    final String email = pToAddr.toLowerCase().trim();

    if (!_appSettings.getEmailValidationMode().isValid(email)) {
      return SimpleApiResponse.non500Error(CommonErrorCode.INVALID_EMAIL_ADDR).build();
    }

    final Host host = _hostSvc.findHostById(pHostId, pGroup.getId());

    final TemplateMap params = getGroupEmailSvc().generateTemplateParameters(pGroup);
    params.put("sentBy", pUser.getUsername());

    // Create the boomark.
    final ObjectId bookmarkId = createBookmark(pChartParams, pGroup.getId());

    final ChartDef chartDef = _chartDefDao.findByChartId(pChartId);

    // Generate the subject.
    final StringBuilder subject = new StringBuilder();
    subject.append("Chart - ");
    subject.append(pGroup.getName()).append(" - ").append(host.getFullHostname());
    subject.append(" - ").append(chartDef.getDisplayName());

    params.put("chartName", chartDef.getDisplayName());
    params.put("hostId", host.getId());
    params.put("hostname", host.getFullHostname());
    params.put("isPrimary", host.getIsPrimary());
    params.put("isSecondary", host.getIsSecondary());
    params.put("isMongos", host.getIsMongos());
    params.put("isArbiter", host.getIsArbiter());
    params.put("isStandalone", host.getIsStandalone());
    params.put("isConfig", host.getIsSCCCConfig());
    params.put("isMaster", host.getIsMaster());
    params.put("isSlave", host.getIsSlave());
    params.put("isRecovering", host.getIsRecovering());

    if (pMsg != null) {
      params.put("message", pMsg.trim());
    } else {
      params.put("message", pMsg);
    }

    params.put(
        "bookmarkUrl",
        _appSettings.getCentralUrl()
            + "/chart/bookmark/"
            + pGroup.getId()
            + "/"
            + bookmarkId.toString());

    final EmailMsg.Builder builder =
        new EmailMsg.Builder(_appSettings)
            .recipient(email)
            .fromAddr(
                _appSettings
                    .getCloudSupportFromEmailAddress()
                    .orElse(_appSettings.getFromEmailAddress()))
            .replyAddrs(
                _appSettings
                    .getCloudSupportReplyToEmailAddress()
                    .orElse(_appSettings.getReplyToEmailAddress()))
            .subject(subject.toString())
            .htmlTemplate(ChartTemplate.EMAIL_CHART_HTML)
            .textTemplate(ChartTemplate.EMAIL_CHART_TEXT)
            .templateParams(params)
            .group(pGroup);
    _emailSvc.send(builder.build(getTemplateSvc()));

    return SimpleApiResponse.ok().newObjId(bookmarkId.toString()).build();
  }

  public static boolean isAnnotatedEvent(final HostEvent.Type pEventType) {
    switch (pEventType) {
      case HOST_RESTARTED:
      case HOST_NOW_PRIMARY:
      case HOST_NOW_SECONDARY:
        return true;
      default:
        return false;
    }
  }

  /**
   * The chart params come in as:
   * /chart/v1/timeLine/1499f25476f6e905ca13a6f6f9e97b32/fixed-opcounters-chart/GMT-4/minute/720/avg
   */
  @GET
  @Path("/createBookmark/{groupId}/{chartParams}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.RrdChartResource.createBookmark.GET")
  public Response createBookmark(
      @Context final Group pGroup, @PathParam("chartParams") final String pChartParams)
      throws Exception {
    final ObjectId bookmarkId = createBookmark(pChartParams, pGroup.getId());

    return Response.temporaryRedirect(
            new URI("/chart/bookmark/" + pGroup.getId() + "/" + bookmarkId.toString()))
        .build();
  }

  protected HandlebarsTemplateSvc getTemplateSvc() {
    return _templateSvc;
  }
}
