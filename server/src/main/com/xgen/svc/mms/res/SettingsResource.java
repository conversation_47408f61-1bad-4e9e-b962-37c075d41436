/* (C) Copyright 2013, MongoDB, Inc. */

package com.xgen.svc.mms.res;

import static com.xgen.cloud.common.util._public.util.LogUtils.entries;
import static com.xgen.cloud.integration._public.model.helpers.IntegrationUtils.isRedactedString;
import static java.util.stream.Collectors.toMap;

import com.mongodb.MongoException;
import com.mongodb.ReadPreference;
import com.xgen.cloud.abtesting._public.svc.ABTestSvc;
import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.agent._public.svc.AgentApiKeySvc;
import com.xgen.cloud.atm.changes._public.model.AutomationChange;
import com.xgen.cloud.atm.changes._public.svc.AutomationChangesSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.authz.core._public.wrapper.AuthorizationClientProvider;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit.UpdatedBillingProperty;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.constants._public.model.communication.DatadogRegion;
import com.xgen.cloud.common.constants._public.model.communication.OpsGenieRegion;
import com.xgen.cloud.common.constants._public.model.communication.PagerdutyRegion;
import com.xgen.cloud.common.constants._public.model.notification.DatadogNotificationConstants.Region;
import com.xgen.cloud.common.constants._public.model.notification.OpsGenieNotificationConstants;
import com.xgen.cloud.common.constants._public.model.notification.PagerDutyNotificationConstants;
import com.xgen.cloud.common.db.mongo._public.util.ExceptionUtils;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.date.DateFormat;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.time.TimeFormat;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.common.resource._public.model.ResourceId.ResourceType;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.json.JsonUtils;
import com.xgen.cloud.common.util._public.time.TZUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.PathUtils;
import com.xgen.cloud.communication._public.model.enums.ProviderType;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.AutomationErrorCode;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.federation._public.model.ConnectedOrgConfig;
import com.xgen.cloud.federation._public.svc.FederationSettingsSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStorageConfig;
import com.xgen.cloud.group._public.model.PrometheusConfig;
import com.xgen.cloud.group._public.model.RrdMigration;
import com.xgen.cloud.group._public.model.activity.GroupAudit;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.integration._public.model.DatadogIntegration;
import com.xgen.cloud.integration._public.model.HipChatIntegration;
import com.xgen.cloud.integration._public.model.Integration;
import com.xgen.cloud.integration._public.model.MicrosoftTeamsIntegration;
import com.xgen.cloud.integration._public.model.OpsGenieIntegration;
import com.xgen.cloud.integration._public.model.PagerdutyIntegration;
import com.xgen.cloud.integration._public.model.SlackIntegration;
import com.xgen.cloud.integration._public.model.VictorOpsIntegration;
import com.xgen.cloud.integration._public.model.WebhookIntegration;
import com.xgen.cloud.integration._public.model.credentials.DatadogCredentials;
import com.xgen.cloud.integration._public.model.credentials.HipChatCredentials;
import com.xgen.cloud.integration._public.model.credentials.MicrosoftTeamsCredentials;
import com.xgen.cloud.integration._public.model.credentials.OpsGenieCredentials;
import com.xgen.cloud.integration._public.model.credentials.PagerdutyCredentials;
import com.xgen.cloud.integration._public.model.credentials.SlackCredentials;
import com.xgen.cloud.integration._public.model.credentials.VictorOpsCredentials;
import com.xgen.cloud.integration._public.model.credentials.WebhookCredentials;
import com.xgen.cloud.integration._public.model.error.IntegrationErrorHelper;
import com.xgen.cloud.integration._public.svc.IntegrationSvc;
import com.xgen.cloud.integrations._public.svc.ProjectIntegrationSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.metrics._public.model.MetricsErrorCode;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.VercelLinkedCluster;
import com.xgen.cloud.organization._public.svc.OrgUiIpAccessListSvc;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.ui._public.util.SharedUiLayoutUtils;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.ProjectLandingPage;
import com.xgen.cloud.user._public.model.UserAllowList;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.cloud.user._public.svc.UserAllowListSvc;
import com.xgen.cloud.user._public.svc.UserApiKeySvc;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.cloud.user._public.view.UpdateShowCloudNavView;
import com.xgen.cloud.user._public.view.UpdateThemePreferenceView;
import com.xgen.module.iam.config.IamAppSettings;
import com.xgen.svc.core.model.api.FormResponse;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.api.view.integrations.constants.ApiIntegrationViewConstants.IntegrationType;
import com.xgen.svc.mms.model.agent.constants.AgentLogLevel;
import com.xgen.svc.mms.model.auth.UiAuthCode;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.mms.res.validation.OneOf;
import com.xgen.svc.mms.res.validation.ValidTimeZoneDisplayName;
import com.xgen.svc.mms.res.validation.ValidTimeZoneId;
import com.xgen.svc.mms.res.view.settings.AgentApiKeyView;
import com.xgen.svc.mms.res.view.user.UserView;
import com.xgen.svc.mms.svc.AdminSvc;
import com.xgen.svc.mms.svc.SlowQueryLogIngestionMetadataSvc;
import com.xgen.svc.mms.svc.billing.PlanSvc;
import com.xgen.svc.mms.svc.common.GroupErrorCode;
import com.xgen.svc.mms.svc.ping.NewRelicMessagingSvc;
import com.xgen.svc.mms.svc.prometheus.MetricsDisablePrometheusIntegrationSvc;
import com.xgen.svc.mms.svc.prometheus.MetricsPrometheusIntegrationSvc;
import com.xgen.svc.mms.svc.user.UserLoginSvc;
import com.xgen.svc.mms.util.PolicyAssignmentUtils;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/settings")
@Singleton
public class SettingsResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(SettingsResource.class);
  protected static final String TOGGLE_CONFIG_SERVICE_FEATURE_FLAG_ERROR_MESSAGE =
      "Tried to toggle a Feature Flag which is managed by the Config Service. Use the Admin UI to"
          + " perform this operation.";
  private final AdminSvc adminSvc;
  private final AutomationConfigPublishingSvc automationConfigSvc;
  private final FeatureFlagSvc featureFlagSvc;
  private final AppSettings appSettings;
  private final GroupDao groupDao;
  private final NDSGroupSvc ndsGroupSvc;
  private final UserSvc userSvc;
  private final UserApiKeySvc userApiKeySvc;
  private final UserAllowListSvc userAllowListSvc;
  private final GroupSvc groupSvc;
  private final ProjectIntegrationSvc projectIntegrationSvc;
  private final HostSvc hostSvc;
  private final HostClusterLifecycleSvc hostClusterLifecycleSvc;
  private final OrganizationSvc organizationSvc;
  private final PlanSvc planSvc;
  private final InvitationSvc invitationSvc;
  private final AgentApiKeySvc agentApiKeySvc;
  private final FederationSettingsSvc federationSettingsSvc;
  private final AutomationChangesSvc automationChangesSvc;
  private final AuthzSvc authzSvc;
  private final IamAppSettings iamAppSettings;
  private final UserLoginSvc userLoginSvc;
  private final MetricsPrometheusIntegrationSvc metricsPrometheusIntegrationSvc;
  private final MetricsDisablePrometheusIntegrationSvc metricsDisablePrometheusIntegrationSvc;
  private final AuditSvc auditSvc;
  private final OrgUiIpAccessListSvc orgUiIpAccessListSvc;
  private final SlowQueryLogIngestionMetadataSvc slowQueryLogIngestionMetadataSvc;
  private final AuthorizationClientProvider authorizationClientProvider;
  private final PolicyAssignmentUtils policyAssignmentUtils;
  private final IntegrationSvc integrationSvc;
  private final ABTestSvc abTestSvc;

  @Inject
  public SettingsResource(
      final IamAppSettings pIamAppSettings,
      final AdminSvc pAdminSvc,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final AppSettings pAppSettings,
      final GroupDao pGroupDao,
      final NDSGroupSvc pNdsGroupSvc,
      final UserApiKeySvc pUserApiKeySvc,
      final UserAllowListSvc pUserAllowListSvc,
      final GroupSvc pGroupSvc,
      final ProjectIntegrationSvc projectIntegrationSvc,
      final UserSvc pUserSvc,
      final HostSvc pHostSvc,
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final OrganizationSvc pOrganizationSvc,
      final PlanSvc pPlanSvc,
      final InvitationSvc pInvitationSvc,
      final AgentApiKeySvc pAgentApiKeySvc,
      final FederationSettingsSvc pFederationSettingsSvc,
      final AutomationChangesSvc pAutomationChangesSvc,
      final AuthzSvc pAuthzSvc,
      final UserLoginSvc pUserLoginSvc,
      final MetricsPrometheusIntegrationSvc pMetricsPrometheusIntegrationSvc,
      final MetricsDisablePrometheusIntegrationSvc pMetricsDisablePrometheusIntegrationSvc,
      final AuditSvc pAuditSvc,
      final OrgUiIpAccessListSvc pOrgUiIpAccessListSvc,
      final SlowQueryLogIngestionMetadataSvc pSlowQueryLogIngestionMetadataSvc,
      final AuthorizationClientProvider pAuthorizationClientProvider,
      final PolicyAssignmentUtils pPolicyAssignmentUtils,
      final IntegrationSvc integrationSvc,
      final ABTestSvc abTestSvc) {
    adminSvc = pAdminSvc;
    automationConfigSvc = pAutomationConfigSvc;
    featureFlagSvc = pFeatureFlagSvc;
    appSettings = pAppSettings;
    groupDao = pGroupDao;
    ndsGroupSvc = pNdsGroupSvc;
    groupSvc = pGroupSvc;
    userSvc = pUserSvc;
    userApiKeySvc = pUserApiKeySvc;
    userAllowListSvc = pUserAllowListSvc;
    hostSvc = pHostSvc;
    hostClusterLifecycleSvc = pHostClusterLifecycleSvc;
    organizationSvc = pOrganizationSvc;
    planSvc = pPlanSvc;
    invitationSvc = pInvitationSvc;
    agentApiKeySvc = pAgentApiKeySvc;
    federationSettingsSvc = pFederationSettingsSvc;
    automationChangesSvc = pAutomationChangesSvc;
    authzSvc = pAuthzSvc;
    iamAppSettings = pIamAppSettings;
    userLoginSvc = pUserLoginSvc;
    metricsPrometheusIntegrationSvc = pMetricsPrometheusIntegrationSvc;
    metricsDisablePrometheusIntegrationSvc = pMetricsDisablePrometheusIntegrationSvc;
    auditSvc = pAuditSvc;
    orgUiIpAccessListSvc = pOrgUiIpAccessListSvc;
    slowQueryLogIngestionMetadataSvc = pSlowQueryLogIngestionMetadataSvc;
    authorizationClientProvider = pAuthorizationClientProvider;
    policyAssignmentUtils = pPolicyAssignmentUtils;
    this.projectIntegrationSvc = projectIntegrationSvc;
    this.integrationSvc = integrationSvc;
    this.abTestSvc = abTestSvc;
  }

  @GET
  @Path("/groups")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String groups(@Context final AppUser pUser) {
    final JSONObject response = new JSONObject();

    final List<ObjectId> groupIds =
        pUser.getRoles().stream()
            .filter(assign -> assign.getRole().isGroupSpecific())
            .map(RoleAssignment::getGroupId)
            .collect(Collectors.toList());
    final List<Group> groups = groupSvc.findUnembargoedByIds(groupIds);

    final JSONArray groupsJson = new JSONArray();

    for (final Group group : groups) {

      final List<String> roleDisplayNames =
          pUser.getRoles().stream()
              .filter(assign -> Objects.equals(assign.getGroupId(), group.getId()))
              .map(assign -> assign.getRole().getDisplayName())
              .collect(Collectors.toList());

      final JSONObject groupJson = new JSONObject();
      JsonUtils.appendTo(groupJson, "roles", roleDisplayNames);
      JsonUtils.appendTo(groupJson, "id", group.getId().toString());
      JsonUtils.appendTo(groupJson, "name", group.getName());

      groupsJson.put(groupJson);
    }

    JsonUtils.appendTo(response, "groups", groupsJson);

    return response.toString();
  }

  @GET
  @Path("/orgs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public String orgs(@Context final AppUser pUser, @Context final HttpServletRequest pRequest) {
    final JSONObject response = new JSONObject();

    final List<ObjectId> orgIds =
        pUser.getRoles().stream()
            .filter(assign -> assign.getRole().isOrgSpecific())
            .map(RoleAssignment::getOrgId)
            .collect(Collectors.toList());

    final Map<ObjectId, Organization> unembargoedOrgsById =
        organizationSvc.findUnembargoedOrgs(orgIds).stream()
            .collect(toMap(Organization::getId, org -> org));

    final Map<ObjectId, ObjectId> federationSettingsIdByOrgId = new HashMap<>();
    federationSettingsSvc
        .findByOrgIds(unembargoedOrgsById.keySet())
        .forEach(
            federationSettings -> {
              for (ConnectedOrgConfig config : federationSettings.getConnectedOrgConfigs()) {
                if (unembargoedOrgsById.containsKey(config.getOrgId())) {
                  federationSettingsIdByOrgId.put(config.getOrgId(), federationSettings.getId());
                }
              }
            });

    final JSONArray orgsJson = new JSONArray();

    for (final Organization org : unembargoedOrgsById.values()) {
      final String remoteAddress = pRequest.getRemoteAddr();
      final JSONObject orgJson = createOrgViewFromOrg(org, pUser, remoteAddress);
      if (federationSettingsIdByOrgId.containsKey(org.getId())) {
        JsonUtils.appendTo(
            orgJson, "federationSettingsId", federationSettingsIdByOrgId.get(org.getId()));
      }

      orgsJson.put(orgJson);
    }

    JsonUtils.appendTo(response, "orgs", orgsJson);

    return response.toString();
  }

  @GET
  @Path("/orgsWithEmbargoedOrgs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String orgsWithEmbargoed(
      @Context final AppUser pUser, @Context final HttpServletRequest pRequest) {
    final List<Organization> orgs = organizationSvc.findOrgsByOrgIds(pUser.getOrgIds());
    final String remoteAddress = pRequest.getRemoteAddr();
    final JSONArray orgsJson = new JSONArray();
    for (final Organization org : orgs) {
      orgsJson.put(createOrgViewFromOrg(org, pUser, remoteAddress));
    }

    final JSONObject response = new JSONObject();
    JsonUtils.appendTo(response, "orgs", orgsJson);
    return response.toString();
  }

  @GET
  @Path("/api/{groupId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GROUP_OWNER, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.SettingsResource.showApiSettings.GET")
  public Response showApiSettings(@Context final Group pGroup) throws Exception {
    return tempRedirect("/settings/group/" + pGroup.getId());
  }

  @GET
  @Path("/group/users/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.SettingsResource.getGroupUsers.GET")
  public Response getGroupUsers(@Context final Group pGroup) {
    final List<UserView> users = new ArrayList<>();
    for (AppUser user : groupSvc.getModelCursorByGroup(pGroup, false, false)) {
      final List<Role> groupRoles = user.getGroupRoles(pGroup.getId());
      final UserView.Builder builder = new UserView.Builder();
      builder.username(user.getUsername());
      builder.firstName(user.getFirstName());
      builder.lastName(user.getLastName());
      builder.emailAddress(user.getPrimaryEmail());
      builder.lastAuth(user.getLastAuth());
      builder.timeZoneId(user.getTimeZoneDisplay());
      builder.created(user.getCreated());
      builder.userId("@" + user.getId().toString());
      for (final Role role : groupRoles) {
        builder.role(role);
      }
      builder.status(UserView.Status.CONFIRMED);
      builder.multiFactorAuth(user.getMultiFactorAuth());
      builder.hasAccountMultiFactorAuth(user.hasAccountMultiFactorAuth());
      builder.readOnly(authzSvc.isProjectChartsAdmin(user, pGroup));
      users.add(builder.build());
    }
    for (final Invitation invitation : invitationSvc.findByGroupId(pGroup.getId())) {
      final UserView.Builder builder = new UserView.Builder();
      builder.firstName("Pending");
      builder.lastName("User");
      builder.emailAddress(invitation.getUsername());
      builder.username(invitation.getUsername());
      builder.userId(invitation.getUsername());
      builder.created(invitation.getCreatedAt());
      for (final Role role : invitation.getRoles()) {
        builder.role(role);
      }
      builder.status(UserView.Status.INVITED);
      builder.isInvite();
      users.add(builder.build());
    }

    // Check org invitations for related groups that users will be added to
    for (final Invitation invitation : invitationSvc.findByOrgId(pGroup.getOrgId())) {
      if (invitation.getGroupRoleAssignments().size() > 0) {
        final List<Role> groupRoles = invitation.getGroupRolesByGroupId(pGroup.getId());

        if (groupRoles.size() > 0) {
          final UserView.Builder builder = new UserView.Builder();
          builder.firstName("Pending");
          builder.lastName("User");
          builder.created(invitation.getCreatedAt());
          builder.status(UserView.Status.INVITED);
          builder.isInvite();
          builder.emailAddress(invitation.getUsername());
          builder.username(invitation.getUsername());
          builder.userId(invitation.getUsername());
          builder.roles(groupRoles);
          users.add(builder.build());
        }
      }
    }

    return Response.ok(users).build();
  }

  @GET
  @Path("/{groupId}/agentApiKeys")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.SettingsResource.findAllAgentApiKeys.GET")
  public Response findAllAgentApiKeys(@Context final Group pGroup) throws SvcException {
    final List<AgentApiKey> keys =
        agentApiKeySvc.getKeysForGroup(pGroup.getId(), ReadPreference.primary());
    keys.sort(Comparator.comparing(AgentApiKey::getCreatedTime).reversed());
    final List<AgentApiKeyView> keyViews = new ArrayList<>();
    for (final AgentApiKey key : keys) {
      final AgentApiKeyView view = new AgentApiKeyView.AgentApiKeyViewBuilder(key).build();
      keyViews.add(view);
    }

    return Response.ok(keyViews).build();
  }

  @DELETE
  @Path("/{groupId}/agentApiKeys/{apiKeyId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.SettingsResource.deleteAgentApiKey.DELETE")
  public Response deleteAgentApiKey(
      @Context final Group pGroup,
      @PathParam("apiKeyId") final ObjectId pApiKeyId,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    agentApiKeySvc.deleteKey(pGroup.getId(), pApiKeyId, pAuditInfo);
    return Response.ok().build();
  }

  @POST
  @Path("/{groupId}/agentApiKeys")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.SettingsResource.createAgentApiKey.POST")
  public Response createAgentApiKey(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      @FormParam("desc") final String pDescription)
      throws SvcException {
    if (pDescription.length() > 1000) {
      LOG.warn(
          "Description passed in by user {} ({}) to the create agent api key endpoint is too long",
          pUser.getName(),
          pUser.getId());
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final AgentApiKey key =
        agentApiKeySvc.generateKey(
            pGroup.getId(),
            pUser.getId(),
            pAuditInfo.getRemoteAddr(),
            pDescription,
            AgentApiKey.KeySource.USER,
            pAuditInfo);
    final AgentApiKeyView view =
        new AgentApiKeyView.AgentApiKeyViewBuilder(key).unredacted().build();

    return Response.ok(view).build();
  }

  @POST
  @Path("/addTempApiKey")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response addTempApiKey(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @FormParam("desc") final String pDesc)
      throws SvcException {
    final String uiAuthCode = userLoginSvc.getUiAuthCode(pRequest);
    final UiAuthCode authCodeObj = pUser.findUiAuthCode(uiAuthCode);
    final Date expiresAt =
        DateUtils.addSeconds(
            authCodeObj.getCreated(), iamAppSettings.getMmsAuthCookieExpirationSeconds());
    final UserApiKey key = userApiKeySvc.addTemp(pUser.getId(), pDesc, expiresAt);
    return Response.ok(key).build();
  }

  // TODO [CLOUDP-91779]: Delete or migrate this to GPAK access list. Only being used in tests
  @POST
  @Path("/addPublicApiWhitelist")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response addPublicApiWhitelist(
      @Context final AppUser pUser, @FormParam("ipAddress") final String pIpAddress)
      throws SvcException {
    final UserAllowList result = userAllowListSvc.add(pUser.getId(), pUser.getType(), pIpAddress);
    return Response.ok(result).build();
  }

  @GET
  @Path("/billing/{groupId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.SettingsResource.redirectToV2Billing.GET")
  public Response redirectToV2Billing(@Context final Group pGroup) throws Exception {
    return permanentRedirect("/v2/" + pGroup.getId() + "#/settings/billing");
  }

  @GET
  @Path("/paymentHistory/{groupId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.SettingsResource.redirectToV2PaymentHistory.GET")
  public Response redirectToV2PaymentHistory(@Context final Group pGroup) throws Exception {
    return permanentRedirect("/v2/" + pGroup.getId() + "#/settings/paymentHistory");
  }

  @GET
  @Path("/featureFlags/groups/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {
        RoleSet.GLOBAL_OWNER,
        RoleSet.GLOBAL_ATLAS_ADMIN,
        RoleSet.GLOBAL_FEATURE_FLAG_ADMIN,
        RoleSet.GLOBAL_MONITORING_ADMIN
      })
  @Auth(endpointAction = "epa.global.SettingsResource.getAdminFeatureFlags.GET")
  public Response getAdminFeatureFlags(@Context final Organization pOrganization) {
    return Response.ok(getAdminGroupFeatureFlagsObj(pOrganization).toString()).build();
  }

  private JSONObject getAdminGroupFeatureFlagsObj(final Organization pOrganization) {
    final JSONObject responseObject = new JSONObject();
    final List<FeatureFlag> editableFlags = new ArrayList<>();
    for (FeatureFlag flag : FeatureFlag.values()) {
      if (flag == FeatureFlag.UNKNOWN) {
        continue;
      }
      if (flag.getScope() == FeatureFlag.Scope.GROUP
          && appSettings.isFeatureFlagInControlledState(flag)
          && (!flag.hasEnabledByDefaultForPlanTypes()
              || pOrganization.isEnabledForEffectivePlanType(flag))) {
        editableFlags.add(flag);
      }
    }

    final List<FeatureFlag> nonConfigServiceEditableFlags =
        editableFlags.stream()
            .filter(FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS::contains)
            .toList();
    final List<FeatureFlag> operatorEditableFlags =
        editableFlags.stream().filter(FeatureFlag.OPERATOR_FEATURE_FLAGS::contains).toList();

    responseObject.put("groupControlledFeatureFlags", editableFlags);
    responseObject.put(
        "nonConfigServiceGroupControlledFeatureFlags", nonConfigServiceEditableFlags);
    responseObject.put("operatorControlledFeatureFlags", operatorEditableFlags);
    return responseObject;
  }

  @GET
  @Path("/featureFlags/orgs/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {
        RoleSet.GLOBAL_OWNER,
        RoleSet.GLOBAL_ATLAS_ADMIN,
        RoleSet.GLOBAL_FEATURE_FLAG_ADMIN,
        RoleSet.GLOBAL_MONITORING_ADMIN
      },
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SettingsResource.getOrgFeatureFlags.GET")
  public Response getOrgFeatureFlags(@Context final Organization pOrganization) {
    final List<FeatureFlag> editableFlags =
        Stream.of(FeatureFlag.values())
            .filter(ff -> ff != FeatureFlag.UNKNOWN)
            .filter(
                flag ->
                    flag.getScope() == FeatureFlag.Scope.ORGANIZATION
                        && (!flag.hasEnabledByDefaultForPlanTypes()
                            || pOrganization.isEnabledForEffectivePlanType(flag))
                        && appSettings.isFeatureFlagInControlledState(flag))
            .collect(Collectors.toList());

    final List<FeatureFlag> nonConfigServiceEditableFlags =
        editableFlags.stream()
            .filter(FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS::contains)
            .toList();
    final List<FeatureFlag> enabledFlags =
        editableFlags.stream()
            .filter(
                flag ->
                    featureFlagSvc.isFeatureFlagEnabledForOrgId(flag, pOrganization.getId(), null))
            .collect(Collectors.toList());

    final JSONObject responseObject = new JSONObject();
    responseObject.put("orgControlledFeatureFlags", editableFlags);
    responseObject.put("orgEnabledFeatureFlags", enabledFlags);
    responseObject.put(
        "orgInheritableGroupFeatureFlags", pOrganization.getInheritableProjectFeatureFlags());
    responseObject.put("nonConfigServiceOrgControlledFeatureFlags", nonConfigServiceEditableFlags);
    responseObject.put("group", getAdminGroupFeatureFlagsObj(pOrganization));
    return Response.ok(responseObject.toString()).build();
  }

  @POST
  @Path("/updateTimeZoneId")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, bypassMfa = true, groupSource = GroupSource.NONE)
  public Response updateTimeZoneId(
      @Context final AppUser pUser,
      @FormParam("timeZoneId") final String pTimeZoneId,
      @FormParam("displayName") final String pTimeZoneDisplay)
      throws Exception {
    if (StringUtils.isNotBlank(pTimeZoneId) && !TZUtils.isValidTimeZone(pTimeZoneId)) {
      throw new SvcException(AppUserErrorCode.INVALID_TIMEZONE);
    }
    if (StringUtils.isNotBlank(pTimeZoneDisplay)
        && !TZUtils.isValidTimeZoneDisplayName(pTimeZoneDisplay)) {
      throw new SvcException(AppUserErrorCode.INVALID_TIMEZONE_DISPLAY_NAME);
    }

    userSvc.setDefaultTimeZoneId(pUser.getId(), pTimeZoneId, pTimeZoneDisplay);

    final JSONObject tzResult = new JSONObject();
    tzResult.put(AppUser.JSON_TIME_ZONE_ID_FIELD, pTimeZoneId);
    tzResult.put(AppUser.JSON_TIME_ZONE_DISPLAY_FIELD, pTimeZoneDisplay);
    return Response.ok(tzResult.toString()).build();
  }

  @POST
  @Path("/updateGroupTimeZoneId/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupTimeZoneId.POST")
  public Response updateGroupTimeZoneId(
      @Context final Group pGroup,
      @FormParam("timeZoneId") @ValidTimeZoneId String pTimeZoneId,
      @FormParam("timeZoneDisplay") Optional<@ValidTimeZoneDisplayName String> pTimeZoneDisplay)
      throws Exception {

    final String targetTimeZoneDisplay =
        pTimeZoneDisplay.orElse(TZUtils.getTimeZoneDisplayName(pTimeZoneId));

    LOG.info(
        "Changing group time zone settings {}",
        entries(
            "groupId",
            pGroup.getId(),
            "previous_timezone_id",
            pGroup.getDefaultTimeZoneId(),
            "previous_timezone_display",
            pGroup.getDefaultTimeZoneDisplay(),
            "new_timezone_id",
            pTimeZoneId,
            "new_timezone_display",
            targetTimeZoneDisplay));
    groupDao.setDefaultTimeZoneId(pGroup.getId(), pTimeZoneId, targetTimeZoneDisplay);

    final JSONObject tzResult = new JSONObject();
    tzResult.put(AppUser.JSON_TIME_ZONE_ID_FIELD, pTimeZoneId);
    tzResult.put(AppUser.JSON_TIME_ZONE_DISPLAY_FIELD, targetTimeZoneDisplay);
    tzResult.put(
        AppUser.JSON_TIME_ZONE_DISPLAY_SHORT_FIELD, TZUtils.getTimeZoneDisplayShort(pTimeZoneId));

    return Response.ok(tzResult.toString()).build();
  }

  @GET
  @Path("/groupTimeZone/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, groupSource = GroupSource.PATH, csrf = false)
  @AllowCORS({KnownCrossOrigin.CHARTS, KnownCrossOrigin.REALM, KnownCrossOrigin.BAAS})
  @Auth(endpointAction = "epa.project.SettingsResource.getGroupTimeZone.GET")
  public Response getGroupTimeZone(
      @Context final Group group,
      @Context final AppUser user,
      @QueryParam("considerUserPrefs") @DefaultValue("false") final Boolean considerUserTimeZone) {

    final String groupTimeZoneId = group.getDefaultTimeZoneId();
    final String userTimeZoneId = user.getTimeZoneId();

    String timeZoneId = groupTimeZoneId;
    // User TZ preference typically overrides an eventual project TZ setting for UI purposes
    if (considerUserTimeZone && userTimeZoneId != null) {
      timeZoneId = userTimeZoneId;
    }
    // In case both the project and user don't have a preferred TZ set, use UTC
    if (timeZoneId == null) {
      timeZoneId = "Etc/UTC";
    }

    final JSONObject tzResult = new JSONObject();
    tzResult.put(AppUser.JSON_TIME_ZONE_ID_FIELD, timeZoneId);
    tzResult.put(AppUser.JSON_TIME_ZONE_DISPLAY_FIELD, TZUtils.getTimeZoneDisplayName(timeZoneId));
    tzResult.put("timeZoneCurrentOffset", TZUtils.getOffset(timeZoneId));
    return Response.ok(tzResult.toString()).build();
  }

  @POST
  @Path("/updateDefaultChartGranularity/{granularity}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String updateDefaultChartGranularity(
      @Context final AppUser pUser, @PathParam("granularity") final long pGranularity) {
    userSvc.setDefaultChartGranularity(pUser.getId(), pGranularity);
    return EMPTY_JSON_OBJECT;
  }

  @POST
  @Path("/updateDefaultChartZoom/{zoom}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String updateDefaultChartZoom(
      @Context final AppUser pUser, @PathParam("zoom") final long pZoom) {
    userSvc.setDefaultChartZoom(pUser.getId(), pZoom);
    return EMPTY_JSON_OBJECT;
  }

  @POST
  @Path("/updateDateFormat")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateDateFormat(
      @Context final AppUser pUser, @FormParam("value") final String pCode) {
    if (!DateFormat.codeIsValidDateFormat(pCode)) {
      LOG.info("Invalid input date format code {}", entries("code", pCode));
      return SimpleApiResponse.badRequest(
              CommonErrorCode.INVALID_PARAMETER, "Invalid date format code value")
          .build();
    }

    userSvc.setDateFormatCode(pUser.getId(), pCode);

    JSONObject result = new JSONObject(Map.of("value", pCode));
    return Response.ok().entity(result.toString()).build();
  }

  @POST
  @Path("/updateTimeFormat")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateTimeFormat(
      @Context final AppUser pUser,
      @FormParam("value") @OneOf(allowedValues = {"24-hour", "am-pm"}) String code) {
    userSvc.setTimeFormatCode(pUser.getId(), TimeFormat.findByCode(code));

    final JSONObject result = new JSONObject(Map.of("value", code));
    return Response.ok().entity(result.toString()).build();
  }

  @PUT
  @Path("/projectLandingPage")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SettingsResource.updateProjectLandingPage.PUT")
  public Response updateProjectLandingPage(
      @Context final AppUser user,
      @FormParam("projectLandingPage") final String projectLandingPageString) {

    ProjectLandingPage projectLandingPage;
    try {
      if (projectLandingPageString != null) {
        projectLandingPage = ProjectLandingPage.valueOf(projectLandingPageString);
      } else {
        projectLandingPage = null;
      }
    } catch (IllegalArgumentException e) {
      return SimpleApiResponse.badRequest(
              CommonErrorCode.INVALID_PARAMETER, "Invalid projectLandingPage value")
          .build();
    }

    try {
      userSvc.setProjectLandingPage(user.getId(), projectLandingPage);
      final JSONObject result = new JSONObject();
      if (projectLandingPage != null) {
        result.put("projectLandingPage", projectLandingPage);
      } else {
        result.put("projectLandingPage", JSONObject.NULL);
      }

      return Response.ok().entity(result.toString()).build();
    } catch (Exception ex) {
      LOG.error("An error occurred while updating the project landing page", ex);
      return Response.serverError()
          .entity("An error occurred while updating the project landing page")
          .build();
    }
  }

  @POST
  @Path("/updateThemePreference")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateThemePreference(
      @Context final AppUser pUser, final UpdateThemePreferenceView pUpdateThemePreferenceView) {
    userSvc.setThemePreference(pUser.getId(), pUpdateThemePreferenceView.getValue());
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/canOptIntoCloudNav")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response getCanOptIntoCloudNav(@Context final AppUser pUser) {
    return Response.ok()
        .entity(
            Map.of(
                "canOptIntoCloudNav",
                SharedUiLayoutUtils.canOptIntoCloudNav(
                    pUser, appSettings, organizationSvc, abTestSvc)))
        .build();
  }

  @POST
  @Path("/showCloudNav")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response setShowCloudNav(
      @Context final AppUser pUser, final UpdateShowCloudNavView pView) {
    if (!SharedUiLayoutUtils.canOptIntoCloudNav(pUser, appSettings, organizationSvc, abTestSvc)) {
      return Response.status(Status.FORBIDDEN).build();
    }
    userSvc.setOptedIntoCloudNav(pUser.getId(), pView.isShowCloudNav());
    pUser.setOptedIntoCloudNav(pView.isShowCloudNav());
    return Response.ok()
        .entity(
            Map.of(
                "showCloudNav",
                SharedUiLayoutUtils.isUserInNewNav(
                    pUser, appSettings, organizationSvc, abTestSvc, null)))
        .build();
  }

  @POST
  @Path("/optInDataExplorerGenAIFeatures")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateOptInDataExplorerGenAIFeatures(
      @Context final AppUser pUser,
      @FormParam("value") final boolean pUpdateIsOptedIntoDataExplorerGenAI) {
    userSvc.setIsOptedIntoDataExplorerGenAI(pUser.getId(), pUpdateIsOptedIntoDataExplorerGenAI);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/updateSeparateOpcounterCharts/{value}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String updateChartDisplayType(
      @Context final AppUser pUser, @PathParam("value") final boolean pValue) {
    userSvc.setSeparateOpcounterCharts(pUser.getId(), pValue);
    return EMPTY_JSON_OBJECT;
  }

  @POST
  @Path("/updateEnableAllHostProfilers/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateEnableAllHostProfilers.POST")
  public Response updateEnableAllHostProfilers(
      @Context final Group pGroup, @FormParam("value") final boolean pValue) {
    hostSvc.setEnableProfilerForAllHosts(pGroup.getId(), pValue);
    groupDao.setEnableAllHostProfilers(pGroup.getId(), pValue);

    final String result = new JSONObject().put("value", pValue).toString();
    return Response.ok(result).build();
  }

  @POST
  @Path("/updateExtendedStorageSizesEnabled/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateExtendedStorageSizesEnabled.POST")
  public Response updateExtendedStorageSizesEnabled(
      @Context final Group pGroup,
      @FormParam("value") final boolean pValue,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    final boolean newValue =
        ndsGroupSvc.setExtendedStorageSizesEnabled(pGroup.getId(), pValue, pAuditInfo);

    final String result = new JSONObject().put("value", newValue).toString();
    return Response.ok(result).build();
  }

  @POST
  @Path("/updateSuppressMongosAutoDiscovery/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.SettingsResource.updateSuppressMongosAutoDiscovery.POST")
  public Response updateSuppressMongosAutoDiscovery(
      @Context final Group pGroup, @FormParam("value") final boolean pValue) {
    groupDao.setSuppressMongosAutoDiscovery(pGroup.getId(), pValue);

    final String result = new JSONObject().put("value", pValue).toString();
    return Response.ok(result).build();
  }

  @GET
  @Path("/updateChartRefreshRate/{code}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String updateChartRefreshRate(
      @Context final AppUser pUser, @PathParam("code") final int pCode) {
    userSvc.setChartRefreshRate(pUser.getId(), pCode);
    return EMPTY_JSON_OBJECT;
  }

  @GET
  @Path("/billingEmail/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SettingsResource.getBillingEmail.GET")
  public Response getBillingEmail(@Context final Organization pOrganization) {
    return SimpleApiResponse.ok()
        .customField("emailAddress", pOrganization.getBillingEmail())
        .build();
  }

  @POST
  @Path("/updateBillingEmail/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SettingsResource.updateBillingEmail.POST")
  public Response updateBillingEmail(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @FormParam("emailAddress") String emailAddress)
      throws SvcException {
    final JSONObject result = new JSONObject();

    emailAddress = emailAddress.trim();

    if (!StringUtils.isBlank(emailAddress)
        && !appSettings.getEmailValidationMode().isValid(emailAddress)) {
      throw new SvcException(CommonErrorCode.INVALID_EMAIL_ADDR);
    }
    setBillingEmailHelper(pOrganization, emailAddress, pAuditInfo);

    result.put("ok", 1);
    result.put("emailAddress", emailAddress);

    return Response.ok(result.toString()).build();
  }

  private void setBillingEmailHelper(
      final Organization pOrganization, final String newBillingEmail, final AuditInfo pAuditInfo) {
    final String oldBillingEmail = pOrganization.getBillingEmail();
    if (StringUtils.equals(oldBillingEmail, newBillingEmail)
        || oldBillingEmail == null && StringUtils.isBlank(newBillingEmail)) {
      return;
    }
    organizationSvc.setBillingEmail(pOrganization.getId(), newBillingEmail);
    final BillingAudit.Builder builder;
    if (StringUtils.isBlank(oldBillingEmail)) {
      builder = new BillingAudit.Builder(BillingEvent.Type.BILLING_EMAIL_ADDRESS_ADDED);
    } else if (StringUtils.isBlank(newBillingEmail)) {
      builder = new BillingAudit.Builder(BillingEvent.Type.BILLING_EMAIL_ADDRESS_REMOVED);
    } else {
      builder = new BillingAudit.Builder(BillingEvent.Type.BILLING_EMAIL_ADDRESS_CHANGED);
    }
    builder.auditInfo(pAuditInfo);
    builder.orgId(pOrganization.getId());
    builder.billingEmailAddress(new UpdatedBillingProperty(oldBillingEmail, newBillingEmail));
    auditSvc.saveAuditEvent(builder.build());
  }

  @POST
  @Path("/updateAutomationDownloadBase/{groupId}/{os}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_AUTOMATION_ADMIN, plan = PlanTypeSet.ALL_UNMANAGED)
  @Auth(endpointAction = "epa.project.SettingsResource.updateAutomationDownloadBase.POST")
  public Response updateAutomationDownloadBase(
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("os") final String pOS,
      @FormParam("automationDownloadBase") String pAutomationDownloadBase) {
    final boolean isWindows = pOS != null && pOS.equals("windows");
    try {
      final String cleanDownloadBase;
      final boolean valid;
      if (isWindows) {
        cleanDownloadBase = PathUtils.cleanWindowsPathToDirectory(pAutomationDownloadBase);
        valid =
            PathUtils.isValidWindowsDirName(cleanDownloadBase)
                && (PathUtils.isWindowsAbsolutePath(cleanDownloadBase)
                    || cleanDownloadBase.startsWith("%"));
      } else {
        cleanDownloadBase = PathUtils.cleanLinuxPathToDirectory(pAutomationDownloadBase);
        valid = !StringUtils.isEmpty(cleanDownloadBase);
      }
      if (!valid) {
        return SimpleApiResponse.non500Error(AutomationErrorCode.INVALID_DIRECTORY_NAME)
            .resource("Invalid download base.")
            .build();
      }
      AutomationConfig automationConfig =
          automationConfigSvc.findCurrentOrEmpty(pGroup.getId(), pUser.getId());
      if (isWindows) {
        automationConfig.getDeployment().setOption("downloadBaseWindows", cleanDownloadBase);
      } else {
        automationConfig.getDeployment().setOption("downloadBase", cleanDownloadBase);
      }
      automationConfigSvc.saveDraft(automationConfig, pUser, pOrganization, pGroup);

      final JSONObject result = new JSONObject();
      result.put("ok", 1);
      result.put("automationDownloadBase", cleanDownloadBase);
      return Response.ok(result.toString()).build();
    } catch (Exception exc) {
      LOG.error("Error updating Automation Download Base Directory", exc);
      return SimpleApiResponse.non500Error(CommonErrorCode.SERVER_ERROR)
          .resource("Error while updating Automation Download Base Directory.")
          .build();
    }
  }

  @POST
  @Path("/updatePagerDuty/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupPagerDuty.POST")
  public Response updateGroupPagerDuty(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("groupPagerDuty") String pPagerDutyServiceKey,
      @FormParam("region") PagerDutyNotificationConstants.Region pRegion)
      throws SvcException {
    // preserve functionality where all null values indicate a deletion
    if (StringUtils.isBlank(pPagerDutyServiceKey) && pRegion == null) {
      return removeGroupPagerDuty(pGroup, pAuditInfo);
    }

    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.PAGERDUTY);
    final PagerdutyIntegration existingIntegration =
        integrations.isEmpty() ? null : (PagerdutyIntegration) integrations.get(0);
    final PagerdutyIntegration integration =
        new PagerdutyIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new PagerdutyCredentials(
                pPagerDutyServiceKey,
                pRegion == null ? PagerdutyRegion.US : pRegion.toModernRegion()),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();
    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    try {
      groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);
    } catch (Exception exc) {
      return SimpleApiResponse.non500Error(CommonErrorCode.SERVER_ERROR)
          .resource("Error while updating PagerDuty service key.")
          .build();
    }

    return SimpleApiResponse.ok()
        .customField("groupPagerDuty", integration.getCredentials().getRedactedServiceKey())
        .customField("pagerDutyRegion", integration.getCredentials().getRegion())
        .build();
  }

  @DELETE
  @Path("/updatePagerDuty/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupPagerDuty.DELETE")
  public Response removeGroupPagerDuty(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.PAGER_DUTY, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/updateVictorOps/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupVictorOps.POST")
  public Response updateGroupVictorOps(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("groupVictorOpsApiKey") final String pVictorOpsApiKey,
      @FormParam("groupVictorOpsRoutingKey") final String pVictorOpsRoutingKey)
      throws SvcException {

    // allow unsetting of the integration if the fields are empty
    if (StringUtils.isBlank(pVictorOpsApiKey) && StringUtils.isBlank(pVictorOpsRoutingKey)) {
      return removeGroupVictorOps(pGroup, pAuditInfo);
    }

    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.VICTOR_OPS);
    final VictorOpsIntegration existingIntegration =
        integrations.isEmpty() ? null : (VictorOpsIntegration) integrations.get(0);
    final VictorOpsIntegration integration =
        new VictorOpsIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new VictorOpsCredentials(pVictorOpsApiKey, pVictorOpsRoutingKey),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();

    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    // Otherwise, set the new integration configuraiton
    try {
      groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);
    } catch (final Exception exception) {
      return SimpleApiResponse.non500Error(CommonErrorCode.SERVER_ERROR)
          .resource("Error while updating VictorOps API key.")
          .build();
    }

    return SimpleApiResponse.ok()
        .customField("groupVictorOpsApiKey", integration.getCredentials().getRedactedApiKey())
        .customField("groupVictorOpsRoutingKey", integration.getCredentials().getRoutingKey())
        .build();
  }

  @DELETE
  @Path("/updateVictorOps/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupVictorOps.DELETE")
  public Response removeGroupVictorOps(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.VICTOR_OPS, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/updateOpsGenie/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupOpsGenie.POST")
  public Response updateGroupOpsGenie(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("groupOpsGenie") final String pOpsGenieApiKey,
      @FormParam("groupOpsGenieRegion") final OpsGenieNotificationConstants.Region pOpsGenieRegion)
      throws SvcException {
    // preserve functionality where all null values indicate a deletion
    if (StringUtils.isBlank(pOpsGenieApiKey) && pOpsGenieRegion == null) {
      return removeGroupOpsGenie(pGroup, pAuditInfo);
    }

    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.OPS_GENIE);
    final OpsGenieIntegration existingIntegration =
        integrations.isEmpty() ? null : (OpsGenieIntegration) integrations.get(0);
    final OpsGenieIntegration integration =
        new OpsGenieIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new OpsGenieCredentials(
                pOpsGenieApiKey,
                pOpsGenieRegion == null ? OpsGenieRegion.US : pOpsGenieRegion.toModernRegion()),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();
    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);

    return SimpleApiResponse.ok()
        .customField("groupOpsGenie", integration.getCredentials().getRedactedApiKey())
        .customField("groupOpsGenieRegion", integration.getCredentials().getRegion())
        .build();
  }

  @DELETE
  @Path("/updateOpsGenie/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupOpsGenie.DELETE")
  public Response removeGroupOpsGenie(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.OPS_GENIE, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/updateHipChat/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupHipChat.POST")
  public Response updateGroupHipChat(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("groupHipChatRoomName") String pHipChatRoomName,
      @FormParam("groupHipChatNotificationToken") String pHipChatNotificationToken)
      throws SvcException {
    if (StringUtils.isBlank(pHipChatNotificationToken) && StringUtils.isBlank(pHipChatRoomName)) {
      return removeGroupHipChat(pGroup, pAuditInfo);
    }

    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.HIP_CHAT);
    final HipChatIntegration existingIntegration =
        integrations.isEmpty() ? null : (HipChatIntegration) integrations.get(0);
    final HipChatIntegration integration =
        new HipChatIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new HipChatCredentials(pHipChatNotificationToken, pHipChatRoomName),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();
    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    try {
      groupSvc.setDefaultIntegration(pGroup, integration);
    } catch (Exception exc) {
      return SimpleApiResponse.non500Error(CommonErrorCode.SERVER_ERROR)
          .resource("Error while updating HipChat settings.")
          .build();
    }

    return SimpleApiResponse.ok()
        .customField("groupHipChatRoomName", integration.getCredentials().getRoomName())
        .customField(
            "groupHipChatNotificationToken",
            integration.getCredentials().getRedactedNotificationToken())
        .build();
  }

  @DELETE
  @Path("/updateHipChat/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupHipChat.DELETE")
  public Response removeGroupHipChat(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.HIP_CHAT, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/updateSlack/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupSlack.POST")
  public Response updateGroupSlack(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("groupSlackChannelName") String pSlackChannelName,
      @FormParam("groupSlackTeamName") String pSlackTeamName,
      @FormParam("groupSlackApiToken") String pSlackApiToken)
      throws SvcException {
    // legacy support for removing configuration
    if (StringUtils.isBlank(pSlackApiToken)
        && StringUtils.isBlank(pSlackTeamName)
        && StringUtils.isBlank(pSlackChannelName)) {
      return removeGroupSlack(pGroup, pAuditInfo);
    }

    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.SLACK);
    final SlackIntegration existingIntegration =
        integrations.isEmpty() ? null : (SlackIntegration) integrations.get(0);

    if (!StringUtils.isBlank(pSlackApiToken)) {
      if (existingIntegration != null
          && pSlackApiToken.equals(existingIntegration.getCredentials().getRedactedApiToken())) {
        pSlackApiToken = existingIntegration.getCredentials().getDecryptedApiToken();
      }
    }

    // An API Token is required
    if (pSlackApiToken == null) {
      throw new SvcException(GroupErrorCode.SLACK_API_TOKEN_REQUIRED);
    }

    // A Team name is required for OAuth2
    final boolean hasOAuth2 = pGroup.hasSlackOAuth2();
    if (hasOAuth2 && pSlackTeamName == null) {
      throw new SvcException(GroupErrorCode.SLACK_TEAM_NAME_REQUIRED);
    }

    final SlackIntegration integration =
        new SlackIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new SlackCredentials(pSlackApiToken, pSlackChannelName, pSlackTeamName),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();
    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);

    return SimpleApiResponse.ok()
        .customField("groupSlackApiToken", integration.getCredentials().getRedactedApiToken())
        .customField("groupSlackTeamName", integration.getCredentials().getTeamName())
        .customField("groupSlackChannelName", integration.getCredentials().getChannelName())
        .build();
  }

  @DELETE
  @Path("/updateSlack/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupSlack.DELETE")
  public Response removeGroupSlack(@Context final Group pGroup, @Context final AuditInfo pAuditInfo)
      throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.SLACK, pAuditInfo);
    return Response.noContent().build();
  }

  @DELETE
  @Path("/prometheus/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removePrometheus.DELETE")
  public Response removePrometheus(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) {
    metricsPrometheusIntegrationSvc.deletePromConfigAndPlan(pGroup, pAuditInfo, false);
    return Response.noContent().build();
  }

  @POST
  @Path("/prometheus/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.createPromConfig.POST")
  public Response createPromConfig(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("enabled") final boolean pEnabled,
      @FormParam("username") final String pUsername,
      @FormParam("password") final String pPassword,
      @FormParam("scheme") final String pScheme,
      @FormParam("tlsPemPath") final String pTLsPemPath,
      @FormParam("tlsPemPassword") final String pTlsPemPassword,
      @FormParam("listenAddress") final String pListenAddress,
      @FormParam("serviceDiscovery") final String pServiceDiscovery,
      @FormParam("rateLimitIntervalSeconds") final Integer pRateLimitIntervalSeconds,
      @FormParam("sendUserProvidedResourceTagsEnabled")
          final Boolean pSendUserProvidedResourceTagsEnabled,
      @FormParam("isViaPrivateEndpoint") @DefaultValue("false") final Boolean pIsViaPrivateEndpoint,
      @FormParam("selectedPrivateEndpoints") final List<String> pSelectedPrivateEndpoints)
      throws SvcException {
    metricsPrometheusIntegrationSvc.createPromConfig(
        pGroup,
        pAuditInfo,
        pEnabled,
        pUsername,
        pPassword,
        pScheme,
        pTLsPemPath,
        pTlsPemPassword,
        pListenAddress,
        pServiceDiscovery,
        pRateLimitIntervalSeconds,
        pSendUserProvidedResourceTagsEnabled,
        pIsViaPrivateEndpoint,
        pSelectedPrivateEndpoints);
    return SimpleApiResponse.ok()
        .customField(PrometheusConfig.ENABLED_FIELD, pEnabled)
        .customField(PrometheusConfig.USERNAME_FIELD, pUsername)
        .customField(PrometheusConfig.TLS_PEM_PATH_FIELD, pTLsPemPath)
        .customField(PrometheusConfig.TLS_PEM_PASSWORD_FIELD, pTlsPemPassword)
        .customField(PrometheusConfig.LISTEN_ADDRESS_FIELD, pListenAddress)
        .customField(PrometheusConfig.SERVICE_DISCOVERY_FIELD, pServiceDiscovery)
        .customField(PrometheusConfig.SCHEME_FIELD, pScheme)
        .customField(PrometheusConfig.RATE_LIMIT_INTERVAL_SECONDS_FIELD, pRateLimitIntervalSeconds)
        .customField(
            PrometheusConfig.SEND_USER_PROVIDED_RESOURCE_TAGS_ENABLED_FIELD,
            pSendUserProvidedResourceTagsEnabled)
        .customField(PrometheusConfig.IS_VIA_PRIVATE_ENDPOINT_FIELD, pIsViaPrivateEndpoint)
        .customField(PrometheusConfig.SELECTED_PRIVATE_ENDPOINTS_FIELD, pSelectedPrivateEndpoints)
        .build();
  }

  @PUT
  @Path("/prometheus/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updatePromConfig.PUT")
  public Response updatePromConfig(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("enabled") final boolean pEnabled,
      @FormParam("username") final String pUsername,
      @FormParam("password") final String pPassword,
      @FormParam("scheme") final String pScheme,
      @FormParam("tlsPemPath") final String pTLsPemPath,
      @FormParam("tlsPemPassword") final String pTlsPemPassword,
      @FormParam("listenAddress") final String pListenAddress,
      @FormParam("serviceDiscovery") final String pServiceDiscovery,
      @FormParam("rateLimitIntervalSeconds") final Integer pRateLimitIntervalSeconds,
      @FormParam("sendUserProvidedResourceTagsEnabled")
          final Boolean pSendUserProvidedResourceTagsEnabled,
      @FormParam("isViaPrivateEndpoint") @DefaultValue("false") final Boolean pIsViaPrivateEndpoint,
      @FormParam("selectedPrivateEndpoints[]") final List<String> pSelectedPrivateEndpoints)
      throws SvcException {
    metricsPrometheusIntegrationSvc.upsertPromConfig(
        pGroup,
        pAuditInfo,
        pEnabled,
        pUsername,
        pPassword,
        pScheme,
        pTLsPemPath,
        pTlsPemPassword,
        pListenAddress,
        pServiceDiscovery,
        pRateLimitIntervalSeconds,
        pSendUserProvidedResourceTagsEnabled,
        pIsViaPrivateEndpoint,
        pSelectedPrivateEndpoints);
    return SimpleApiResponse.ok()
        .customField(PrometheusConfig.ENABLED_FIELD, pEnabled)
        .customField(PrometheusConfig.USERNAME_FIELD, pUsername)
        .customField(PrometheusConfig.TLS_PEM_PATH_FIELD, pTLsPemPath)
        .customField(PrometheusConfig.TLS_PEM_PASSWORD_FIELD, pTlsPemPassword)
        .customField(PrometheusConfig.LISTEN_ADDRESS_FIELD, pListenAddress)
        .customField(PrometheusConfig.SERVICE_DISCOVERY_FIELD, pServiceDiscovery)
        .customField(PrometheusConfig.SCHEME_FIELD, pScheme)
        .customField(PrometheusConfig.RATE_LIMIT_INTERVAL_SECONDS_FIELD, pRateLimitIntervalSeconds)
        .customField(
            PrometheusConfig.SEND_USER_PROVIDED_RESOURCE_TAGS_ENABLED_FIELD,
            pSendUserProvidedResourceTagsEnabled)
        .customField(PrometheusConfig.IS_VIA_PRIVATE_ENDPOINT_FIELD, pIsViaPrivateEndpoint)
        .customField(PrometheusConfig.SELECTED_PRIVATE_ENDPOINTS_FIELD, pSelectedPrivateEndpoints)
        .build();
  }

  @POST
  @Path("/updateNewRelic/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateNewRelic.POST")
  public Response updateNewRelic(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam(Group.NEW_RELIC_LICENSE_KEY) final String pNewRelicLicenseKey,
      @FormParam(Group.NEW_RELIC_INSIGHTS_ACCOUNT_ID) final String pNewRelicAccountId,
      @FormParam(Group.NEW_RELIC_INSIGHTS_READ_TOKEN) final String pNewRelicInsightsReadToken,
      @FormParam(Group.NEW_RELIC_INSIGHTS_WRITE_TOKEN) final String pNewRelicInsightsWriteToken)
      throws SvcException {
    final String newRelicLicenseKey = StringUtils.trimToNull(pNewRelicLicenseKey);
    final String newRelicAccountId = StringUtils.trimToNull(pNewRelicAccountId);
    final String newRelicInsightsReadToken = StringUtils.trimToNull(pNewRelicInsightsReadToken);
    final String newRelicInsightsWriteToken = StringUtils.trimToNull(pNewRelicInsightsWriteToken);

    final boolean isLicenseKeyBlank = StringUtils.isBlank(newRelicLicenseKey);
    final boolean isAccountIdBlank = StringUtils.isBlank(newRelicAccountId);
    final boolean isReadTokenBlank = StringUtils.isBlank(newRelicInsightsReadToken);
    final boolean isWriteTokenBlank = StringUtils.isBlank(newRelicInsightsWriteToken);

    final boolean allValuesPresent =
        (!isLicenseKeyBlank && !isAccountIdBlank && !isReadTokenBlank && !isWriteTokenBlank);
    final boolean noValuesPresent =
        (isLicenseKeyBlank && isAccountIdBlank && isReadTokenBlank && isWriteTokenBlank);

    if (!(allValuesPresent || noValuesPresent)) {
      throw new SvcException(GroupErrorCode.INVALID_NEW_RELIC_PARAMETERS);
    }

    if (allValuesPresent) {
      groupSvc.validateNewRelicAccountId(newRelicAccountId);
    }

    try {
      groupSvc.setDefaultNewRelicSettings(
          pGroup,
          pAuditInfo,
          newRelicLicenseKey,
          newRelicAccountId,
          newRelicInsightsReadToken,
          newRelicInsightsWriteToken);
    } catch (Exception exc) {
      LOG.error(exc.getMessage(), exc);
      throw new SvcException(
          CommonErrorCode.SERVER_ERROR, "Error while updating New Relic settings.");
    }

    return SimpleApiResponse.ok()
        .customField(Group.NEW_RELIC_LICENSE_KEY, newRelicLicenseKey)
        .customField(Group.NEW_RELIC_INSIGHTS_ACCOUNT_ID, newRelicAccountId)
        .customField(Group.NEW_RELIC_INSIGHTS_READ_TOKEN, newRelicInsightsReadToken)
        .customField(Group.NEW_RELIC_INSIGHTS_WRITE_TOKEN, newRelicInsightsWriteToken)
        .build();
  }

  @DELETE
  @Path("/updateNewRelic/{groupId}")
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.deleteNewRelicSettings.DELETE")
  public Response deleteNewRelicSettings(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) {
    groupSvc.deleteNewRelicSettings(pGroup, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/verifyNewRelicCredentials")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response verifyNewRelicCredentials(
      @FormParam(Group.NEW_RELIC_LICENSE_KEY) final String pLicenseKey,
      @FormParam(Group.NEW_RELIC_INSIGHTS_ACCOUNT_ID) final String pAccountId,
      @FormParam(Group.NEW_RELIC_INSIGHTS_READ_TOKEN) final String pInsightsQueryToken,
      @FormParam(Group.NEW_RELIC_INSIGHTS_WRITE_TOKEN) final String pInsightsInsertToken)
      throws Exception {

    groupSvc.validateNewRelicAccountId(pAccountId);

    final JSONObject res =
        NewRelicMessagingSvc.verifyNewRelicCredentials(
            pLicenseKey, pAccountId, pInsightsQueryToken, pInsightsInsertToken);

    final Iterator<String> keys = res.keys();
    while (keys.hasNext()) {
      final String field = keys.next();
      final String val = res.getString(field);
      if (NewRelicMessagingSvc.NewRelicCredentialStatus.INVALID.name().equals(val)) {
        throw new SvcException(MetricsErrorCode.INVALID_NEW_RELIC_CREDENTIAL, field);
      }
    }

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/updateWebhook/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupWebhook.POST")
  public Response updateGroupWebhook(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("webhookUrl") final String pUrl,
      @FormParam("webhookSecret") final String pSecret)
      throws SvcException {
    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.WEBHOOK);
    final WebhookIntegration existingIntegration =
        integrations.isEmpty() ? null : (WebhookIntegration) integrations.get(0);
    final WebhookIntegration integration =
        new WebhookIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new WebhookCredentials(pUrl, pSecret),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();

    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);
    return SimpleApiResponse.ok()
        .customField("webhookUrl", integration.getCredentials().getRedactedWebhookUrl())
        .customField("webhookSecret", integration.getCredentials().getFullRedactedWebhookSecret())
        .build();
  }

  @DELETE
  @Path("/updateWebhook/{groupId}")
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.deleteGroupWebhook.DELETE")
  public Response deleteGroupWebhook(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.WEBHOOK, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/updateDatadog/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupDatadog.POST")
  public Response updateGroupDatadog(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("datadogApiKey") final String pApiKey,
      @FormParam("datadogRegion") final Region pRegion,
      @FormParam("datadogCustomEndpoint") final String pCustomEndpoint,
      @FormParam("datadogDbStatsIntegrationEnabled") final boolean pDbStatsIntegrationEnabled,
      @FormParam("datadogCollStatsIntegrationEnabled") final boolean pCollStatsIntegrationEnabled,
      @FormParam("datadogQueryStatsIntegrationEnabled") final boolean pQueryStatsIntegrationEnabled,
      @FormParam("datadogSendUserProvidedResourceTagsEnabled")
          final boolean pSendUserProvidedResourceTagsEnabled)
      throws SvcException {
    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroupId);
    String trimmedApiKey = StringUtils.trimToNull(pApiKey);

    // preserve functionality where all null values indicate a deletion
    if (trimmedApiKey == null && pRegion == null && pCustomEndpoint == null) {
      return removeGroupDatadog(pGroup, pAuditInfo);
    }

    // in the case where a user wants to edit their DB & Coll Stats Integration without having to
    // re-input the API Key
    // we need to check if the encrypted API key matches the one from the current integration
    // if so this means that the new integration should have the same API Key with updated fields
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.DATADOG);
    final DatadogIntegration existingIntegration =
        integrations.isEmpty() ? null : (DatadogIntegration) integrations.get(0);
    if (existingIntegration != null) {
      if (isRedactedString(trimmedApiKey)
          && existingIntegration.getCredentials().getRedactedApiKey().equals(trimmedApiKey)) {
        trimmedApiKey = existingIntegration.getCredentials().getDecryptedApiKey();
      }
    }

    final DatadogRegion defaultDatadogRegion = appSettings.getDefaultDatadogRegion();

    final DatadogIntegration integration =
        new DatadogIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new DatadogCredentials(
                trimmedApiKey,
                pRegion == null ? defaultDatadogRegion : pRegion.toModernRegion(),
                pCustomEndpoint),
            pDbStatsIntegrationEnabled,
            pCollStatsIntegrationEnabled,
            pQueryStatsIntegrationEnabled,
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now(),
            pSendUserProvidedResourceTagsEnabled);
    final Map<String, Object> errors = integration.validate();
    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);
    return SimpleApiResponse.ok()
        .customField("datadogApiKey", integration.getCredentials().getRedactedApiKey())
        .customField("datadogRegion", integration.getCredentials().getRegion())
        .customField("datadogCustomEndpoint", integration.getCredentials().getCustomEndpoint())
        .customField("datadogDbStatsIntegrationEnabled", integration.getDatabaseStatsEnabled())
        .customField("datadogCollStatsIntegrationEnabled", integration.getCollectionStatsEnabled())
        .customField("datadogQueryStatsIntegrationEnabled", integration.getQueryStatsEnabled())
        .customField(
            "datadogSendUserProvidedResourceTagsEnabled",
            integration.getSendUserProvidedResourceTagsEnabled())
        .build();
  }

  @DELETE
  @Path("/updateDatadog/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupDatadog.DELETE")
  public Response removeGroupDatadog(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.DATADOG, pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/updateMicrosoftTeams/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.updateGroupMicrosoftTeams.POST")
  public Response updateGroupMicrosoftTeams(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("microsoftTeamsWebhookUrl") String pMicrosoftTeamsWebhookUrl)
      throws SvcException {
    final String trimmedUrl = StringUtils.trimToNull(pMicrosoftTeamsWebhookUrl);

    // preserve functionality where all null values indicate a deletion
    if (trimmedUrl == null) {
      return removeGroupMicrosoftTeams(pGroup, pAuditInfo);
    }

    final ResourceId resource = new ResourceId(ResourceType.PROJECT, pGroup.getId());
    final List<Integration> integrations =
        integrationSvc.getDefaultIntegrationsForResource(resource, ProviderType.MICROSOFT_TEAMS);
    final MicrosoftTeamsIntegration existingIntegration =
        integrations.isEmpty() ? null : (MicrosoftTeamsIntegration) integrations.get(0);
    final MicrosoftTeamsIntegration integration =
        new MicrosoftTeamsIntegration(
            existingIntegration == null ? new ObjectId() : existingIntegration.getId(),
            resource,
            true,
            new MicrosoftTeamsCredentials(pMicrosoftTeamsWebhookUrl),
            existingIntegration == null ? Instant.now() : existingIntegration.getCreatedAt(),
            Instant.now());

    final Map<String, Object> errors = integration.validate();

    if (!errors.isEmpty()) {
      return IntegrationErrorHelper.getIntegrationErrorResponse(integration, errors);
    }

    try {
      groupSvc.setDefaultIntegration(pGroup, integration, pAuditInfo);
    } catch (Exception exc) {
      return SimpleApiResponse.non500Error(CommonErrorCode.SERVER_ERROR)
          .resource("Error while updating Microsoft Teams webhook url.")
          .build();
    }

    return SimpleApiResponse.ok()
        .customField(
            "microsoftTeamsWebhookUrl", integration.getCredentials().getRedactedWebhookUrl())
        .build();
  }

  @DELETE
  @Path("/updateMicrosoftTeams/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.removeGroupMicrosoftTeams.DELETE")
  public Response removeGroupMicrosoftTeams(
      @Context final Group pGroup, @Context final AuditInfo pAuditInfo) throws SvcException {
    projectIntegrationSvc.deleteIntegration(pGroup, IntegrationType.MICROSOFT_TEAMS, pAuditInfo);
    return Response.noContent().build();
  }

  /** This method is very dangerous. Please use caution. */
  @POST
  @Path("/flushGroup/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_OWNER)
  @Auth(endpointAction = "epa.global.SettingsResource.flushGroup.POST")
  public Response flushGroup(@Context final Group pGroup, @Context final AuditInfo pAuditInfo)
      throws SvcException {
    if (!adminSvc.canFlushGroupConfiguration(pGroup)) {
      LOG.error("Not allowed to Flush group {}", pGroup.getId());
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }

    adminSvc.flushGroupConfiguration(pGroup.getId(), pGroup.getName(), pAuditInfo);

    return SimpleApiResponse.ok().build();
  }

  /** This method is very dangerous. Please use caution. */
  @POST
  @Path("/migrateGroup/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_OWNER)
  @Auth(endpointAction = "epa.global.SettingsResource.migrateGroup.POST")
  public Response migrateGroup(@Context final Group pGroup) throws SvcException {
    LOG.warn("Received request to enqueue group {} to new monitoring schema", pGroup.getId());

    if (pGroup.getGroupStorageConfig().getRrdMigration().getStatus()
        == RrdMigration.Status.RUNNING) {
      LOG.error("Group {} already has an active migration job running.", pGroup.getId());
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }

    if (pGroup.getGroupStorageConfig().getRrdMigration().getStatus()
        == RrdMigration.Status.SCHEDULE) {
      LOG.error("Group {} already is already scheduled for migration.", pGroup.getId());
      throw new SvcException(CommonErrorCode.SERVER_ERROR);
    }
    groupDao.setRrdMigration(pGroup.getId(), RrdMigration.Status.SCHEDULE, new Date(), null);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/changeDisplayChartAnnotations/{value}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String changeDisplayChartAnnotations(
      @Context final AppUser pUser, @PathParam("value") final boolean pValue) {
    userSvc.changeDisplayChartAnnotations(pUser.getId());

    return EMPTY_JSON_OBJECT;
  }

  @POST
  @Path("/changeDbstats/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.changeDbstats.POST")
  public Response changeDbstats(
      @Context final Group pGroup, @FormParam("value") final boolean pIsEnabled) {
    groupSvc.updateDbStats(pGroup.getId(), pIsEnabled);

    final String result = new JSONObject().put("value", pIsEnabled).toString();
    return Response.ok(result).build();
  }

  @GET
  @Path("/preferredHostnames/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_ADMIN)
  @Auth(endpointAction = "epa.project.SettingsResource.getPreferredHostname.GET")
  public Response getPreferredHostname(@Context final Group pGroup) {
    return Response.ok(pGroup.getPreferredHostnames()).build();
  }

  @POST
  @Path("/deletePreferredHostname/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.deletePreferredHostname.POST")
  public Response deletePreferredHostname(
      @Context final Group pGroup, @FormParam("id") final String pId) {
    groupDao.deletePreferredHostname(pGroup.getId(), new ObjectId(pId));

    return SimpleApiResponse.ok().newObjId("OK").build();
  }

  @POST
  @Path("/addPreferredHostname/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_MANAGER)
  @Auth(endpointAction = "epa.project.SettingsResource.addPreferredHostname.POST")
  public Response addPreferredHostname(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @FormParam("value") final String pValue,
      @FormParam("isRegexp") final boolean pIsRegexp,
      @FormParam("isEndsWith") final boolean pIsEndsWith) {
    final FormResponse response =
        hostClusterLifecycleSvc.setPreferredHostname(
            pGroup.getId(), pValue, true, pIsRegexp, pIsEndsWith, null);

    return SimpleApiResponse.formResponse(pRequest.getRequestURI(), response);
  }

  @GET
  @Path("/setPreferredHostname/{groupId}/{isInsert}/{isRegexp}/{isEndsWith}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_MONITORING_ADMIN)
  @Auth(endpointAction = "epa.project.SettingsResource.setPreferredHostname.GET")
  public Response setPreferredHostname(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @QueryParam("value") final String pValue,
      @PathParam("isInsert") final boolean pIsInsert,
      @PathParam("isRegexp") final boolean pIsRegexp,
      @PathParam("isEndsWith") final boolean pIsEndsWith,
      @QueryParam("oid") final String pObjectId) {
    final ObjectId objId = (StringUtils.isEmpty(pObjectId)) ? null : new ObjectId(pObjectId);

    final FormResponse response =
        hostClusterLifecycleSvc.setPreferredHostname(
            pGroup.getId(), pValue, pIsInsert, pIsRegexp, pIsEndsWith, objId);

    return SimpleApiResponse.formResponse(pRequest.getRequestURI(), response);
  }

  @POST
  @Path("/featureFlag/{groupId}/{featureFlag}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_OWNER, RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_FEATURE_FLAG_ADMIN})
  @AccessAudit(
      auditEventType = AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG,
      auditableRoles = RoleSet.ANY_AUTHENTICATED_USER)
  @Auth(endpointAction = "epa.global.SettingsResource.setFeatureFlag.POST")
  public Response setFeatureFlag(
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("featureFlag") final String pFeatureFlagName,
      @FormParam("value") final boolean enabled)
      throws SvcException {
    final FeatureFlag flag = FeatureFlag.valueOf(pFeatureFlagName);

    // if the Feature Flag requested to be toggled is part of the Config Service, refuse the request
    // to toggle it
    if (!FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS.contains(flag)) {
      return Response.status(Status.BAD_REQUEST)
          .entity(TOGGLE_CONFIG_SERVICE_FEATURE_FLAG_ERROR_MESSAGE)
          .build();
    }

    if (enabled) {
      featureFlagSvc.enableNonConfigServiceFeatureFlag(pGroup, pOrganization, flag);
    } else {
      featureFlagSvc.disableNonConfigServiceFeatureFlag(pGroup, pOrganization, flag);
    }

    automationChangesSvc.publish(
        new AutomationChange(AutomationChange.Type.SETTINGS_MODIFIED, pGroup.getId()));
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/inheritableProjectFeatureFlag/{orgId}/{featureFlag}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_OWNER, RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_FEATURE_FLAG_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SettingsResource.setInheritableProjectFeatureFlag.POST")
  public Response setInheritableProjectFeatureFlag(
      @Context final Organization pOrganization,
      @PathParam("featureFlag") final String pFeatureFlagName,
      @FormParam("updateAllGroups") final boolean updateAllGroups,
      @FormParam("value") final boolean enabled) {
    final FeatureFlag flag = FeatureFlag.valueOf(pFeatureFlagName);
    // if the Feature Flag requested to be toggled is part of the Config Service, refuse the request
    // to toggle it
    if (!FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS.contains(flag)) {
      return Response.status(Status.BAD_REQUEST)
          .entity(TOGGLE_CONFIG_SERVICE_FEATURE_FLAG_ERROR_MESSAGE)
          .build();
    }
    if (updateAllGroups) {
      groupSvc.toggleFeatureFlagForAllOrgGroups(pOrganization.getId(), flag, enabled);
    }
    organizationSvc.toggleInheritableFeatureFlag(pOrganization, flag, enabled);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/inheritableProjectFeatureFlag/{orgId}/{featureFlag}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_OWNER, RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_FEATURE_FLAG_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.SettingsResource.getInheritableProjectFeatureFlagInfo.GET")
  public Response getInheritableProjectFeatureFlagInfo(
      @Context final Organization pOrganization,
      @PathParam("featureFlag") final String pFeatureFlagName) {
    final FeatureFlag flag = FeatureFlag.valueOf(pFeatureFlagName);
    final List<Group> groups = groupSvc.findByOrgId(pOrganization.getId(), false);
    final JSONObject responseObject =
        new JSONObject()
            .put("flag", flag.name())
            .put(
                "defaultEnabled",
                pOrganization.getInheritableProjectFeatureFlags().contains(flag.name()))
            .put("numGroups", groups.size())
            .put(
                "numGroupsEnabled",
                groups.stream()
                    .filter(g -> featureFlagSvc.isFeatureFlagEnabled(flag, pOrganization, g))
                    .count());
    return Response.ok(responseObject.toString()).build();
  }

  @POST
  @Path("/updateAtlasGoToSettings")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateAtlasGoToSettings(
      @Context final AppUser pAppUser, @FormParam("isDisabled") final boolean pIsDisabled) {
    userSvc.setGoToDisabled(pAppUser.getId(), pIsDisabled);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/updateFlaggedForPersonalizationWizard")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateFlaggedForPersonalizationWizard(
      @Context final AppUser pAppUser, @FormParam("enabled") final boolean pEnabled) {
    userSvc.setFlaggedForPersonalizationWizard(pAppUser.getId(), pEnabled);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/appSettings/{groupId}/{field}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {RoleSet.GLOBAL_OWNER, RoleSet.GLOBAL_ATLAS_ADMIN})
  @Auth(endpointAction = "epa.global.SettingsResource.setAppSettingsField.POST")
  public Response setAppSettingsField(
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("field") final String pField,
      @FormParam("value") final String pValue)
      throws Exception {
    try {
      appSettings.setProp(pField, pValue, AppSettings.SettingType.DATABASE);
    } catch (RuntimeException e) {
      throw new Exception(e);
    }
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/editableFeatureFlag/{groupId}/{featureFlag}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER)
  @AccessAudit(
      auditEventType = AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG,
      auditableRoles = RoleSet.ANY_AUTHENTICATED_USER)
  @Auth(endpointAction = "epa.project.SettingsResource.setEditableFeatureFlag.POST")
  public Response setEditableFeatureFlag(
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("featureFlag") final String pFeatureFlagName,
      @FormParam("value") final boolean pEnabled)
      throws Exception {
    final FeatureFlag flag = FeatureFlag.valueOf(pFeatureFlagName);
    if (!FeatureFlag.EDITABLE_FEATURE_FLAGS.contains(flag)) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER, "The provided feature flag is not modifiable");
    }

    if (!pOrganization.isEnabledForEffectivePlanType(flag)) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          "The provided feature flag is not available for this group.");
    } else {
      if (pEnabled) {
        featureFlagSvc.enableNonConfigServiceFeatureFlag(pGroup, pOrganization, flag);
      } else {
        featureFlagSvc.disableNonConfigServiceFeatureFlag(pGroup, pOrganization, flag);
      }
      // use auditsvc to save audit event for data explorer toggle
      if (flag == FeatureFlag.DATA_EXPLORER) {
        // build audit if Data Explorer is enabled/disabled
        final GroupAudit.Builder builder;
        if (pEnabled) {
          builder = new GroupAudit.Builder(GroupAudit.Type.DATA_EXPLORER_ENABLED);
        } else {
          builder = new GroupAudit.Builder(GroupAudit.Type.DATA_EXPLORER_DISABLED);
        }
        builder.orgId(pOrganization.getId());
        builder.groupId(pGroup.getId());
        builder.auditInfo(pAuditInfo);
        builder.featureFlagIsEnabled(pEnabled);
        auditSvc.saveAuditEvent(builder.build());
      }

      // This logic matches what is specified in ApiManagedSlowMsResource
      if (flag == FeatureFlag.MANAGED_SLOW_MS) {
        if (pEnabled) {
          LOG.info(
              "Managed SlowMS feature enabled via settings page for group {} by username {}",
              pGroup.getId(),
              pAuditInfo.getUsername());
          try {
            slowQueryLogIngestionMetadataSvc.insertMetadatasForGroup(pGroup.getId(), true);
          } catch (final MongoException ex) {
            if (!ExceptionUtils.isDuplicateKeyException(ex)) {
              throw ex;
            }

            // Metadata docs might already exist because the user called this endpoint more than
            // once.
            LOG.debug("Metadata docs already exist for the group {}", pGroup.getId(), ex);
          }
        } else {
          LOG.info(
              "Managed SlowMS feature disabled via settings page for group {} by username {}",
              pGroup.getId(),
              pAuditInfo.getUsername());
          slowQueryLogIngestionMetadataSvc.deleteMetadatasForGroup(pGroup.getId());
        }
      }
    }

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/orgfeatureFlag/{orgId}/{featureFlag}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_OWNER, RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_FEATURE_FLAG_ADMIN},
      groupSource = GroupSource.NONE)
  @AccessAudit(
      auditEventType = AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG,
      auditableRoles = RoleSet.ANY_AUTHENTICATED_USER)
  @Auth(endpointAction = "epa.global.SettingsResource.setOrgFeatureFlag.POST")
  public Response setOrgFeatureFlag(
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("featureFlag") final FeatureFlag pFlag,
      @FormParam("value") final boolean pEnabled) {
    // if the Feature Flag requested to be toggled is part of the Config Service, refuse the request
    // to toggle it
    if (!FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS.contains(pFlag)) {
      return Response.status(Status.BAD_REQUEST)
          .entity(TOGGLE_CONFIG_SERVICE_FEATURE_FLAG_ERROR_MESSAGE)
          .build();
    }

    if (pEnabled) {
      featureFlagSvc.enableNonConfigServiceFeatureFlag(null, pOrganization, pFlag);
    } else {
      featureFlagSvc.disableNonConfigServiceFeatureFlag(null, pOrganization, pFlag);
    }

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/agentloglevel/{agent}/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {RoleSet.GLOBAL_OWNER, RoleSet.GLOBAL_AUTOMATION_ADMIN})
  @Auth(endpointAction = "epa.global.SettingsResource.setAgentLogLevel.POST")
  public String setAgentLogLevel(
      @Context final Group pGroup,
      @PathParam("agent") final String pAgentType,
      @FormParam("value") final String pLogLevel,
      @QueryParam("http") final Boolean pIsHttp) {
    final AgentType agentType = AgentType.valueOf(pAgentType.toUpperCase());
    final AgentLogLevel newLevel = AgentLogLevel.of(pLogLevel);
    final boolean isHttpLogLevelChange = pIsHttp != null ? pIsHttp : false;
    boolean publish = false;

    if (isHttpLogLevelChange && pGroup.getAgentHttpLogLevel(agentType) != newLevel) {
      // Passing in `agentType` here but method only supports the automation agent HTTP log level
      // for now.
      groupDao.setAgentHttpLogLevel(agentType, pGroup.getId(), newLevel);
      publish = true;
    } else if (!isHttpLogLevelChange && pGroup.getAgentLogLevel(agentType) != newLevel) {
      groupDao.setAgentLogLevel(agentType, pGroup.getId(), newLevel);
      publish = true;
    }

    if (publish) {
      automationChangesSvc.publish(
          new AutomationChange(AutomationChange.Type.SETTINGS_MODIFIED, pGroup.getId()));
    }

    return EMPTY_JSON_OBJECT;
  }

  @POST
  @Path("/groupstoragemode/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_OWNER)
  @Auth(endpointAction = "epa.global.SettingsResource.setGroupStorageConfigMode.POST")
  public String setGroupStorageConfigMode(
      @Context final Group pGroup, @FormParam("value") final GroupStorageConfig.Mode pMode) {
    if (pGroup.getGroupStorageConfig().getMode() != pMode) {
      groupDao.setGroupStorageConfigMode(pGroup.getId(), pMode);
    }

    return EMPTY_JSON_OBJECT;
  }

  @GET
  @Path("/separateOpcounterCharts")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response getChartDisplayType(@Context final AppUser pUser) throws SvcException {
    return Response.ok(
            new JSONObject()
                .put("separateOpcounterCharts", pUser.getSeparateOpcounterCharts())
                .toString())
        .build();
  }

  @POST
  @Path("/isProjectOverviewEnabled/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER)
  @Auth(endpointAction = "epa.project.SettingsResource.setIsProjectOverviewEnabled.POST")
  public Response setIsProjectOverviewEnabled(
      @PathParam("groupId") final ObjectId groupId,
      @FormParam("isEnabled") final Boolean isEnabled) {
    if (isEnabled == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(
              new JSONObject()
                  .put("message", "Parameter isEnabled not found in request")
                  .toString())
          .build();
    }

    try {
      groupSvc.setIsProjectOverviewEnabled(groupId, isEnabled);
    } catch (Exception exception) {
      LOG.error("Failed to set isProjectOverviewEnabled field on group", exception);
      return Response.serverError()
          .entity(new JSONObject().put("message", exception.getMessage()).toString())
          .build();
    }

    return SimpleApiResponse.accepted().build();
  }

  @POST
  @Path("/setEnableCurrentIpWarning/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_OWNER)
  @Auth(endpointAction = "epa.project.SettingsResource.setEnableCurrentIpWarning.POST")
  public Response setEnableCurrentIpWarning(
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("isEnabled") final Boolean pIsEnabled) {
    final boolean isMissingParameter = pIsEnabled == null;
    if (isMissingParameter) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(
              new JSONObject()
                  .put("message", "Parameter isEnabled not found in request")
                  .toString())
          .build();
    }

    try {
      groupSvc.setEnableCurrentIpWarning(pGroupId, pIsEnabled);
    } catch (Exception exception) {
      LOG.error("Failed to set enableCurrentIPWarning field on group", exception);
      return Response.serverError()
          .entity(new JSONObject().put("message", exception.getMessage()).toString())
          .build();
    }

    return SimpleApiResponse.accepted().build();
  }

  private JSONObject createOrgViewFromOrg(
      final Organization pOrg, final AppUser pUser, final String remoteAddress) {
    final List<String> roleDisplayNames =
        pUser.getRoles().stream()
            .filter(assign -> Objects.equals(assign.getOrgId(), pOrg.getId()))
            .map(assign -> assign.getRole().getDisplayName())
            .collect(Collectors.toList());

    final long numOwners = userSvc.countOrgOwners(pOrg.getId());
    final boolean isRestrictedByUiAccessList;
    if (pOrg.getUiIpAccessListEnabled() == null || !pOrg.getUiIpAccessListEnabled()) {
      isRestrictedByUiAccessList = false;
    } else {
      isRestrictedByUiAccessList =
          !orgUiIpAccessListSvc.isIpAddressAllowed(pOrg.getId(), remoteAddress);
    }

    final JSONObject orgJson = new JSONObject();
    JsonUtils.appendTo(orgJson, "roles", roleDisplayNames);
    JsonUtils.appendTo(orgJson, "id", pOrg.getId().toString());
    JsonUtils.appendTo(orgJson, "name", pOrg.getName());
    JsonUtils.appendTo(orgJson, "multiFactorAuthRequired", pOrg.getMultiFactorAuthRequired());
    JsonUtils.appendTo(orgJson, "hasVercelIntegration", pOrg.hasVercelIntegration());
    JsonUtils.appendTo(orgJson, "hasActiveVercelIntegration", pOrg.hasActiveVercelIntegration());
    JsonUtils.appendTo(
        orgJson,
        "hasDataApi",
        pOrg.getVercelInfo() != null
            && pOrg.getVercelInfo().getLinkedClusters().stream()
                .anyMatch(VercelLinkedCluster::isSetupInitialDataApi));
    JsonUtils.appendTo(orgJson, "isRestrictedByUiAccessList", isRestrictedByUiAccessList);
    JsonUtils.appendTo(
        orgJson,
        "isFineGrainedAuthEnabled",
        featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.FINE_GRAINED_AUTH, pOrg, null));
    JsonUtils.appendTo(
        orgJson,
        "maxServiceAccountSecretValidityInHours",
        pOrg.getMaxServiceAccountSecretValidityInHours());
    if (pOrg.isAtlas()) {
      final OrgPlan plan = planSvc.getCurrentPlan(pOrg.getId());
      JsonUtils.appendTo(orgJson, "planType", plan.getPlanType().getDescription());
    } else if (pOrg.isCloudManager()) {
      JsonUtils.appendTo(orgJson, "planType", "Cloud Manager");
    } else {
      JsonUtils.appendTo(orgJson, "planType", "");
    }

    JsonUtils.appendTo(orgJson, "numOwners", numOwners);
    return orgJson;
  }
}
