package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.svc.FlexMigrationSvc;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;

@Path("/nds/clusterUpgrade")
@Singleton
public class TenantUpgradeResource extends NDSBaseResource {

  private final TenantUpgradeSvc _tenantUpgradeSvc;
  private final AppSettings _appSettings;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;
  private final FlexMigrationSvc _flexMigrationSvc;

  @Inject
  public TenantUpgradeResource(
      final TenantUpgradeSvc pTenantUpgradeSvc,
      final AppSettings pAppSettings,
      final NDSResourcePolicySvc pNDSResourcePolicySvc,
      final FlexMigrationSvc pFlexMigrationSvc) {
    _tenantUpgradeSvc = pTenantUpgradeSvc;
    _appSettings = pAppSettings;
    _ndsResourcePolicySvc = pNDSResourcePolicySvc;
    _flexMigrationSvc = pFlexMigrationSvc;
  }

  @POST
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_CLUSTER_MANAGER, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.TenantUpgradeResource.startClusterUpgrade.POST")
  public Response startClusterUpgrade(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      final ClusterDescriptionView pClusterDescriptionView)
      throws SvcException {
    if (pClusterDescriptionView.isServerlessTenantCluster()) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
    _flexMigrationSvc.verifyFlexMigrationOrRollbackNotInProgress(
        pClusterDescriptionView.getName(), pGroup.getId(), "clusterUpgrade", false);
    final AtlasResourcePolicyAuthResponse authResult =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            pGroup,
            pClusterDescriptionView,
            ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(Optional.empty()),
            pAuditInfo);

    if (authResult.decision().isDeny()) {
      return SimpleApiResponse.forbidden(
              NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)
          .message(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage())
          .build();
    }

    _tenantUpgradeSvc.startClusterUpgrade(
        pOrganization, pClusterDescriptionView, pGroup.getId(), pUser, pAuditInfo, pRequest);

    return Response.accepted(pClusterDescriptionView).build();
  }
}
