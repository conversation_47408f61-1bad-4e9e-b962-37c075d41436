package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.NDSLDAPVerifyConnectivityJobRequestParamsView;
import com.xgen.svc.nds.model.ui.NDSUserSecurityView;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/userSecurity")
@AllowNDSCNRegionsOnlyGroups
@Singleton
public class NDSUserSecurityResource extends NDSBaseResource {
  private final NDSUISvc _ndsUISvc;

  @Inject
  public NDSUserSecurityResource(final NDSUISvc pNDSUISvc) {
    _ndsUISvc = pNDSUISvc;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSUserSecurityResource.getUserSecurity.GET")
  public Response getUserSecurity(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsUISvc.getUserSecurity(pGroup.getId())).build();
  }

  @PATCH
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSUserSecurityResource.patchUserSecurity.PATCH")
  public Response patchUserSecurity(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final NDSUserSecurityView pNDSUserSecurityView)
      throws Exception {
    _ndsUISvc.updateUserSecurity(pGroup.getId(), pNDSUserSecurityView, true, pAuditInfo);
    return Response.ok(_ndsUISvc.getUserSecurity(pGroup.getId())).build();
  }

  @POST
  @Path("/ldap/verify")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSUserSecurityResource.createLDAPVerifyConnectivityRequest.POST")
  public Response createLDAPVerifyConnectivityRequest(
      @Context final Group pGroup,
      final NDSLDAPVerifyConnectivityJobRequestParamsView pRequestParamsView)
      throws Exception {
    return Response.ok(
            _ndsUISvc.createLDAPVerificationJobRequest(pGroup.getId(), pRequestParamsView))
        .build();
  }

  @GET
  @Path("/ldap/verify/{requestId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSUserSecurityResource.checkLDAPVerifyConnectivityRequestStatus.GET")
  public Response checkLDAPVerifyConnectivityRequestStatus(
      @Context final Group pGroup,
      @PathParam("requestId") final ObjectId pRequestId,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws Exception {
    return Response.ok(_ndsUISvc.getLDAPVerificationJobRequest(pGroup.getId(), pRequestId)).build();
  }

  @DELETE
  @Path("/ldap/userToDNMapping")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSUserSecurityResource.deleteUserSecurityLdapUserToDNMapping.DELETE")
  public Response deleteUserSecurityLdapUserToDNMapping(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo)
      throws Exception {
    _ndsUISvc.deleteUserSecurityLdapUserToDNMapping(pGroup.getId(), pAuditInfo);
    return Response.accepted("{}").build();
  }

  @DELETE
  @Path("/customerX509")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSUserSecurityResource.deleteCustomerX509.DELETE")
  public Response deleteCustomerX509(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo)
      throws Exception {
    _ndsUISvc.deleteCustomerX509FromUserSecurity(pGroup.getId(), pAuditInfo);
    return Response.accepted("{}").build();
  }
}
