package com.xgen.svc.nds.res;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.DataLakeType;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView;
import com.xgen.cloud.nds.datalake._public.model.ui.DataFederationUsageLimitView.LimitSpan;
import com.xgen.cloud.nds.datalake._public.util.DataLakeTenantUtil;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.res.filter.TestUtility;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeClientConfigSourceView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageValidationErrorsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantView;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.ResponseBuilder;
import jakarta.ws.rs.core.Response.Status;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONObject;

@Singleton
@Path("/nds/dataLakes/{groupId}")
public class NDSDataLakeTenantResource extends NDSBaseResource {
  private final NDSGroupSvc _ndsGroupSvc;
  private final NDSDataLakePublicSvc _ndsDataLakePublicSvc;
  private final AppSettings _appSettings;

  @Inject
  public NDSDataLakeTenantResource(
      final NDSGroupSvc pNDSGroupSvc,
      final NDSDataLakePublicSvc pNDSDataLakePublicSvc,
      final AppSettings pAppSettings) {
    _ndsGroupSvc = pNDSGroupSvc;
    _ndsDataLakePublicSvc = pNDSDataLakePublicSvc;
    _appSettings = pAppSettings;
  }

  @GET
  @Path("/v1/tenants")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.getDataLakes.GET")
  public Response getDataLakes(
      @Context final Group pGroup, @QueryParam("includeStorage") final Boolean pIncludeStorage)
      throws Exception {
    final NDSGroup group = _ndsGroupSvc.ensureGroup(pGroup.getId());
    final List<NDSDataLakeTenantView> tenantViews =
        _ndsDataLakePublicSvc
            .findTenantsByGroup(group, Optional.ofNullable(pIncludeStorage).orElse(false))
            .stream()
            .filter(t -> !DataLakeType.CHARTS.equals(t.getDataLakeType()))
            .collect(Collectors.toList());
    return Response.ok(tenantViews).build();
  }

  @GET
  @Path("/v1/tenants/{tenantName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.getDataLake.GET")
  public Response getDataLake(
      @Context final Group pGroup, @PathParam("tenantName") final String pTenantName)
      throws Exception {
    if (isChartsDataLake(pGroup, pTenantName)) {
      throw new SvcException(NDSErrorCode.CHARTS_DATA_LAKE_TENANT);
    }
    return Response.ok(_ndsDataLakePublicSvc.findByGroupAndName(pGroup, pTenantName)).build();
  }

  @GET
  @Path("/atlasSQL/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.getAtlasSQLDataLake.GET")
  public Response getAtlasSQLDataLake(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws Exception {
    return Response.ok(_ndsDataLakePublicSvc.getAtlasSQLDataLakeTenant(pGroup, pClusterName))
        .build();
  }

  @GET
  @Path("/availableRegions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.getAvailableRegions.GET")
  public Response getAvailableRegions(@Context final Group pGroup) throws SvcException {
    return Response.ok(
            _ndsDataLakePublicSvc.getDataLakeClientRegions(pGroup).stream()
                .map(RegionView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Path("/regionMapping/{cloudProvider}")
  @Produces(MediaType.APPLICATION_JSON)
  @TestUtility
  public Response getRegionMapping(@PathParam("cloudProvider") final String pCloudProvider)
      throws SvcException {
    if (pCloudProvider == null || CloudProvider.findByNameIgnoreCase(pCloudProvider) == null) {
      return Response.status(Status.NOT_FOUND).build();
    }
    final JSONObject response =
        new JSONObject(
            _ndsDataLakePublicSvc.getSupportedRegions().entrySet().stream()
                .filter(e -> e.getKey().getProviderName().equalsIgnoreCase(pCloudProvider))
                .collect(Collectors.toMap(e -> e.getKey().getName(), e -> e.getValue().getName())));
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/tenants/{tenantName}/metrics")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.getMetricsForDataLakeTenant.GET")
  public Response getMetricsForDataLakeTenant(
      @Context final Group pGroup, @PathParam("tenantName") final String pDataLakeTenantName)
      throws Exception {
    if (!ValidationUtils.isValidTenantName(pDataLakeTenantName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Tenant name");
    }
    if (isChartsDataLake(pGroup, pDataLakeTenantName)) {
      throw new SvcException(NDSErrorCode.CHARTS_DATA_LAKE_TENANT);
    }
    return Response.ok(
            _ndsDataLakePublicSvc.getMetricsByGroupIdAndNameForCurrentBillingCycle(
                pGroup.getId(), pDataLakeTenantName))
        .build();
  }

  private Response handleDataLakeAdminApiException(final DataLakeAdminApiException pException)
      throws DataLakeAdminApiException {
    final ErrorCode errorCode = pException.getErrorCode();

    if (NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID.equals(errorCode)) {
      return SimpleApiResponse.notFound().message(pException.getMessage()).build();
    }
    if (NDSErrorCode.INVALID_ARGUMENT.equals(errorCode)
        || NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_INVALID.equals(errorCode)) {
      return SimpleApiResponse.badRequest(errorCode).message(pException.getMessage()).build();
    }
    if (NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_OUTDATED.equals(errorCode)) {
      return SimpleApiResponse.conflict(errorCode).message(pException.getMessage()).build();
    }

    throw pException;
  }

  @POST
  @Path("/v1/tenants")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.createDataLakeTenant.POST")
  public Response createDataLakeTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @QueryParam("skipRoleValidation") final Boolean pSkipRoleValidation,
      final NDSDataLakeTenantView pNDSDataLakeTenantView)
      throws Exception {
    try {
      _ndsDataLakePublicSvc.createTenant(
          pGroup,
          pNDSDataLakeTenantView,
          Optional.ofNullable(pSkipRoleValidation).orElse(false),
          pAuditInfo);
    } catch (final DataLakeAdminApiException e) {
      return handleDataLakeAdminApiException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
    final NDSDataLakeTenantView tenantView =
        _ndsDataLakePublicSvc.findByGroupAndName(pGroup, pNDSDataLakeTenantView.getName());
    return Response.ok(tenantView).build();
  }

  @POST
  @Path("/atlasSQL/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.createAtlasSQLDataLakeTenant.POST")
  public Response createAtlasSQLDataLakeTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    try {
      final NDSDataLakeTenantView tenantView =
          _ndsDataLakePublicSvc.createAtlasSQLDataLakeTenant(
              pGroup, pClusterName, pAuditInfo, pUser.getUsername());
      return Response.ok(tenantView).build();
    } catch (final DataLakeAdminApiException e) {
      return handleDataLakeAdminApiException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    } catch (final SvcException pException) {
      final ErrorCode errorCode = pException.getErrorCode();
      if (NDSErrorCode.DATA_LAKE_TENANT_NAME_ALREADY_EXISTS.equals(errorCode)) {
        return SimpleApiResponse.conflict(errorCode).message(pException.getMessage()).build();
      }
      throw pException;
    }
  }

  @DELETE
  @Path("/tenants/{tenantName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.deleteDataLakeTenant.DELETE")
  public Response deleteDataLakeTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pDataLakeTenantName)
      throws Exception {
    if (!ValidationUtils.isValidTenantName(pDataLakeTenantName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Tenant name");
    }
    if (isChartsDataLake(pGroup, pDataLakeTenantName)) {
      throw new SvcException(NDSErrorCode.CHARTS_DATA_LAKE_TENANT);
    }
    try {
      _ndsDataLakePublicSvc.deleteByGroupAndName(pGroup, pDataLakeTenantName, pAuditInfo);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
    return Response.ok().build();
  }

  @PATCH
  @Path("/v1/tenants/{tenantName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.updateDataLakeTenant.PATCH")
  public Response updateDataLakeTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pDataLakeTenantName,
      @QueryParam("skipRoleValidation") final Boolean pSkipRoleValidation,
      final NDSDataLakeTenantView pNDSDataLakeTenantView)
      throws Exception {
    if (!ValidationUtils.isValidTenantName(pDataLakeTenantName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Tenant name");
    }
    final NDSDataLakeTenantView tenantWithSource =
        pNDSDataLakeTenantView.toBuilder()
            .clientConfigSource(
                pNDSDataLakeTenantView.getStorage() == null
                    ? pNDSDataLakeTenantView.getClientConfigSource()
                    : NDSDataLakeClientConfigSourceView.ATLAS)
            .build();

    try {
      _ndsDataLakePublicSvc.updateTenant(
          pGroup,
          pDataLakeTenantName,
          tenantWithSource,
          Optional.ofNullable(pSkipRoleValidation).orElse(false),
          pAuditInfo);
    } catch (final DataLakeAdminApiException e) {
      return handleDataLakeAdminApiException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }

    final NDSDataLakeTenantView updatedTenantView =
        _ndsDataLakePublicSvc.findByGroupAndName(pGroup, pDataLakeTenantName);
    return Response.ok(updatedTenantView).build();
  }

  @GET
  @Path("/tenants/{tenantName}/queryLogs.gz")
  @Produces("application/gzip")
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, RoleSet.GROUP_DATA_ACCESS_ANY
        // If you update these, be sure to update the public api as well
      })
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.queryLogsForTenant.GET")
  public Response queryLogsForTenant(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("tenantName") final String pDataLakeTenantName,
      @QueryParam("startDate") final Long pStartTimestamp,
      @QueryParam("endDate") final Long pEndTimestamp)
      throws SvcException {
    if (pStartTimestamp == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "startDate");
    }
    if (pEndTimestamp == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "endDate");
    }

    if (!ValidationUtils.isValidTenantName(pDataLakeTenantName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Tenant name");
    }
    if (isChartsDataLake(pGroup, pDataLakeTenantName)) {
      throw new SvcException(NDSErrorCode.CHARTS_DATA_LAKE_TENANT);
    }
    Instant startInstant = Instant.ofEpochSecond(pStartTimestamp);
    Instant endInstant = Instant.ofEpochSecond(pEndTimestamp);
    return Response.ok(
            _ndsDataLakePublicSvc.getDataLakeTenantQueryLogsForDates(
                pResponse,
                pGroup,
                pDataLakeTenantName,
                startInstant,
                endInstant,
                auditInfo,
                pRequest))
        .build();
  }

  @PATCH
  @Path("/queryLimits/{limitSpan}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.setProjectQueryLimit.PATCH")
  public Response setProjectQueryLimit(
      @Context final Group pGroup,
      @PathParam("limitSpan") final String pLimitSpan,
      @Context final AuditInfo pAuditInfo,
      final DataFederationUsageLimitView pUsageLimitView)
      throws SvcException {
    final ObjectId groupId = pGroup.getId();
    if (pUsageLimitView.getGroupId() != null
        && !pUsageLimitView.getGroupId().equals(pGroup.getId())) {
      return Response.status(Response.Status.UNAUTHORIZED).build();
    }

    final LimitSpan limitSpan = DataFederationUsageLimitView.parseLimitSpan(pLimitSpan);
    final DataFederationUsageLimitView usageLimitView =
        pUsageLimitView.toBuilder().groupId(groupId).limitSpan(limitSpan).build();
    _ndsDataLakePublicSvc.setUsageLimit(usageLimitView, pGroup, pAuditInfo);
    return _ndsDataLakePublicSvc
        .getProjectUsageLimit(groupId, limitSpan)
        .map(Response::ok)
        .map(ResponseBuilder::build)
        .orElseGet(() -> Response.status(Response.Status.NOT_FOUND).build());
  }

  @PATCH
  @Path("/queryLimits/tenants/{tenantName}/{limitSpan}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.setTenantQueryLimit.PATCH")
  public Response setTenantQueryLimit(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("limitSpan") final String pLimitSpan,
      @Context final AuditInfo pAuditInfo,
      final DataFederationUsageLimitView pUsageLimitView)
      throws SvcException {
    final ObjectId groupId = pGroup.getId();
    if ((pUsageLimitView.getGroupId() != null
            && !pUsageLimitView.getGroupId().equals(pGroup.getId())
        || (pUsageLimitView.getTenantName().isPresent()
            && !pUsageLimitView.getTenantName().get().equals(pTenantName)))) {
      return Response.status(Response.Status.UNAUTHORIZED).build();
    }
    final LimitSpan limitSpan = DataFederationUsageLimitView.parseLimitSpan(pLimitSpan);
    final DataFederationUsageLimitView usageLimitView =
        pUsageLimitView.toBuilder()
            .groupId(groupId)
            .tenantName(pTenantName)
            .limitSpan(limitSpan)
            .build();
    _ndsDataLakePublicSvc.setUsageLimit(usageLimitView, pGroup, pAuditInfo);
    return _ndsDataLakePublicSvc
        .getTenantUsageLimit(groupId, pTenantName, limitSpan)
        .map(Response::ok)
        .map(ResponseBuilder::build)
        .orElseGet(() -> Response.status(Response.Status.NOT_FOUND).build());
  }

  @GET
  @Path("/queryLimits")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.getQueryLimits.GET")
  public Response getQueryLimits(@Context final Group pGroup) throws SvcException {
    return Response.ok(_ndsDataLakePublicSvc.getUsageLimits(pGroup.getId())).build();
  }

  @DELETE
  @Path("/queryLimits/{limitSpan}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.deleteProjectQueryLimit.DELETE")
  public Response deleteProjectQueryLimit(
      @Context final Group pGroup,
      @PathParam("limitSpan") final String pLimitSpan,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    _ndsDataLakePublicSvc.deleteProjectUsageLimit(
        pGroup, DataFederationUsageLimitView.parseLimitSpan(pLimitSpan), pAuditInfo);
    return Response.noContent().build();
  }

  @DELETE
  @Path("/queryLimits/tenants/{tenantName}/{limitSpan}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.deleteTenantQueryLimit.DELETE")
  public Response deleteTenantQueryLimit(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("limitSpan") final String pLimitSpan,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    _ndsDataLakePublicSvc.deleteTenantUsageLimit(
        pGroup.getId(),
        pTenantName,
        DataFederationUsageLimitView.parseLimitSpan(pLimitSpan),
        pAuditInfo);
    return Response.noContent().build();
  }

  @POST
  @Path("/v1/validateStorage")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.validateStorage.POST")
  public Response validateStorage(
      @QueryParam("tenantId") final String pTenantId,
      @QueryParam("cloudProvider") final String pCloudProvider,
      final String pNDSDataLakeStorageJSON)
      throws SvcException {
    // use zero tenant id to bypass validating projectId in atlas store for creating tenant
    // since projectId is optional, see https://jira.mongodb.org/browse/MHOUSE-2232 for details
    final ObjectId tenantId = DbUtils.parseObjectId(Optional.ofNullable(pTenantId).orElse(""));
    try {
      final NDSDataLakeStorageV1View storageView =
          new ObjectMapper().readValue(pNDSDataLakeStorageJSON, NDSDataLakeStorageV1View.class);
      final NDSDataLakeStorageValidationErrorsView validationErrors =
          _ndsDataLakePublicSvc.validateStorageConfig(
              Optional.ofNullable(tenantId)
                  .orElse(DataLakeTenantUtil.ZERO_TENANT_ID_FOR_STORAGE_VALIDATION),
              Optional.ofNullable(pCloudProvider)
                  .map(CloudProvider::findByNameIgnoreCase)
                  .orElse(CloudProvider.AWS),
              storageView);
      return Response.ok(validationErrors).build();
    } catch (final JsonProcessingException pE) {
      throw new SvcException(NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_INVALID, "invalid JSON");
    }
  }

  @POST
  @Path("/v1/validateCloudProviderConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeTenantResource.validateCloudProviderConfig.POST")
  public Response validateCloudProviderConfig(
      @Context final Group pGroup,
      final NDSDataLakeTenantView pTenantView,
      @QueryParam("skipRoleValidation") final Boolean pSkipRoleValidation)
      throws SvcException {
    final NDSDataLakeTenantView populatedTenantView;
    try {
      final NDSGroup ndsGroup =
          _ndsGroupSvc
              .find(pGroup.getId())
              .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
      populatedTenantView =
          _ndsDataLakePublicSvc.validateCloudProviderConfigNewTenant(
              ndsGroup, pTenantView, Optional.ofNullable(pSkipRoleValidation).orElse(false));

    } catch (final DataLakeAdminApiException e) {
      return handleDataLakeAdminApiException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }

    return Response.ok(populatedTenantView).build();
  }

  private boolean isChartsDataLake(final Group pGroup, final String pTenantName)
      throws SvcException {
    final DataLakeType dataLakeType = _ndsDataLakePublicSvc.getDataLakeType(pGroup, pTenantName);
    return DataLakeType.CHARTS.equals(dataLakeType);
  }

  private Response handleUnsupportedOperationException(final UnsupportedOperationException pE) {
    final String entity =
        new JSONObject()
            .put("errorCode", NDSErrorCode.INTERNAL.name())
            .put("message", pE.getMessage())
            .toString();
    return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(entity).build();
  }
}
