package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.voyage._public.svc.VoyageMetricsSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/voyage/metrics/orgs/{orgId}")
@Singleton
public class VoyageMetricsResource {

  private static final Logger LOG = LoggerFactory.getLogger(VoyageMetricsResource.class);

  private final AppSettings _appSettings;
  private final VoyageMetricsSvc _voyageMetricsSvc;

  @Inject
  public VoyageMetricsResource(
      final AppSettings pAppSettings, final VoyageMetricsSvc pVoyageMetricsSvc) {
    _appSettings = pAppSettings;
    _voyageMetricsSvc = pVoyageMetricsSvc;
  }

  @GET
  @Path("/models")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.VoyageMetricsResource.modelsWithUsage.GET")
  public Response modelsWithUsage(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @PathParam("orgId") final String pOrgId,
      @QueryParam("startTime") final String pStart,
      @QueryParam("endTime") final String pEnd,
      @QueryParam("projects") final List<String> pProjects) {
    LOG.info("Called VoyageMetricsResource::modelsWithUsage with orgId: {}", pOrgId);
    if (!_appSettings.isVoyageMetricsEnabled()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final JSONObject response = new JSONObject().put("orgId", pOrgId);
    return Response.ok(response.toString(), MediaType.APPLICATION_JSON_TYPE).build();
  }

  @GET
  @Path("/activity")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.VoyageMetricsResource.activityWithUsage.GET")
  public Response activityWithUsage(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @PathParam("orgId") final String pOrgId,
      @QueryParam("startTime") final String pStart,
      @QueryParam("endTime") final String pEnd,
      @QueryParam("projects") final List<String> pProjects,
      @QueryParam("models") final List<String> pModels) {
    LOG.info("Called VoyageMetricsResource::activityWithUsage with orgId: {}", pOrgId);
    if (!_appSettings.isVoyageMetricsEnabled()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final JSONObject response = new JSONObject().put("orgId", pOrgId);
    return Response.ok(response.toString(), MediaType.APPLICATION_JSON_TYPE).build();
  }

  @GET
  @Path("/top")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.VoyageMetricsResource.topUsageActivity.GET")
  public Response topUsageActivity(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @PathParam("orgId") final String pOrgId,
      @QueryParam("startTime") final String pStart,
      @QueryParam("endTime") final String pEnd,
      @QueryParam("projects") final List<String> pProjects,
      @QueryParam("models") final List<String> pModels) {
    LOG.info("Called VoyageMetricsResource::topUsageActivity with orgId: {}", pOrgId);
    if (!_appSettings.isVoyageMetricsEnabled()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final JSONObject response = new JSONObject().put("orgId", pOrgId);
    return Response.ok(response.toString(), MediaType.APPLICATION_JSON_TYPE).build();
  }

  @GET
  @Path("/free")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.VoyageMetricsResource.freeUsageActivity.GET")
  public Response freeUsageActivity(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @PathParam("orgId") final String pOrgId,
      @QueryParam("models") final List<String> pModels) {
    LOG.info("Called VoyageMetricsResource::freeUsageActivity with orgId: {}", pOrgId);
    if (!_appSettings.isVoyageMetricsEnabled()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final JSONObject response = new JSONObject().put("orgId", pOrgId);
    return Response.ok(response.toString(), MediaType.APPLICATION_JSON_TYPE).build();
  }
}
