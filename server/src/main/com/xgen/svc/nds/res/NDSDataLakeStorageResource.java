package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.cps.backupjob._public.ui.PolicyItemView;
import com.xgen.cloud.cps.restore._public.ui.BackupSnapshotView;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineDatasetDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelinePausedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineResumedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.OnDemandIngestionTriggeredEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventJobSubmissionSvc;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.dls.DatasetRetentionPolicy;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionSource;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionSource.IngestionSourceType;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.model.ui.dls.DatasetRetentionPolicyView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineRunView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineStatsView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineView;
import com.xgen.svc.nds.model.ui.dls.IngestionSourceView;
import com.xgen.svc.nds.model.ui.dls.TriggerIngestionRequestView;
import com.xgen.svc.nds.svc.IngestionPipelineUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.json.JSONObject;

@Singleton
@Path("/nds/dls/{groupId}")
public class NDSDataLakeStorageResource extends NDSBaseResource {
  public static final Integer DEFAULT_PIPELINE_RUN_RESPONSE_LIMIT = 90;
  public static final Integer DEFAULT_BACKUP_SNAPSHOTS_RESPONSE_LIMIT = 100;
  private final IngestionPipelineUISvc _ingestionPipelineUISvc;
  private final SegmentEventJobSubmissionSvc _deprecatedSegmentEventSvc;
  private final SegmentEventSvc _segmentEventSvc;

  @Inject
  public NDSDataLakeStorageResource(
      final IngestionPipelineUISvc pIngestionPipelineSvc,
      final SegmentEventJobSubmissionSvc pDeprecatedSegmentEventSvc,
      final SegmentEventSvc pSegmentEventSvc) {
    _ingestionPipelineUISvc = pIngestionPipelineSvc;
    _deprecatedSegmentEventSvc = pDeprecatedSegmentEventSvc;
    _segmentEventSvc = pSegmentEventSvc;
  }

  @GET
  @Path("/pipelines")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getIngestionPipelines.GET")
  public Response getIngestionPipelines(@Context final Group pGroup) throws Exception {
    return Response.ok()
        .entity(_ingestionPipelineUISvc.listIngestionPipelineForUI(pGroup.getId()))
        .build();
  }

  @GET
  @Path("/pipelines/{pipelineName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getSingleIngestionPipeline.GET")
  public Response getSingleIngestionPipeline(
      @Context final Group pGroup, @PathParam("pipelineName") final String pPipelineName)
      throws SvcException {
    try {
      final IngestionPipelineView pipeline =
          _ingestionPipelineUISvc.getIngestionPipelineView(pGroup.getId(), pPipelineName);
      return Response.ok().entity(pipeline).build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @POST
  @Path("/pipelines")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.createIngestionPipeline.POST")
  public Response createIngestionPipeline(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final IngestionPipelineView pIngestionPipelineView)
      throws Exception {
    try {
      // verify groupId
      if (!pGroup
          .getId()
          .equals(
              Optional.ofNullable(pIngestionPipelineView)
                  .map(IngestionPipelineView::getGroupId)
                  .orElse(null))) {
        throw new SvcException(NDSErrorCode.INVALID_GROUP_ID);
      }
      final IngestionPipelineView response =
          _ingestionPipelineUISvc.createIngestionPipeline(pIngestionPipelineView, pAuditInfo);

      final ObjectId pipelineId = response.getId();
      final IngestionSourceType sourceType =
          response
              .getSource()
              .map(IngestionSourceView::toIngestionSource)
              .map(IngestionSource::getType)
              .orElseThrow(
                  () ->
                      new SvcException(NDSErrorCode.DLS_UNEXPECTED_ERROR, "Source type not found"));
      _segmentEventSvc.submitEvent(
          DataLakePipelineCreatedEvent.builder()
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .pipelineId(pipelineId)
              .pipelineSourceType(sourceType.name())
              .projectId(pGroup.getId())
              .userId(pAuditInfo.getAppUserId())
              .eventSource(
                  pAuditInfo.getEventSource() != null
                      ? pAuditInfo.getEventSource().getDisplayText()
                      : null)
              .requestContext(pRequest)
              .build());

      return Response.ok().entity(response).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
  }

  @DELETE
  @Path("/pipelines/{pipelineName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.deleteIngestionPipeline.DELETE")
  public Response deleteIngestionPipeline(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("pipelineName") final String pPipelineName)
      throws SvcException {
    try {
      _ingestionPipelineUISvc
          .deleteIngestionPipeline(pGroup.getId(), pPipelineName, pAuditInfo)
          .ifPresent(
              pipelineId ->
                  _segmentEventSvc.submitEvent(
                      DataLakePipelineDeletedEvent.builder()
                          .groupId(pGroup.getId())
                          .organizationId(pGroup.getOrgId())
                          .pipelineId(pipelineId)
                          .projectId(pGroup.getId())
                          .userId(pAuditInfo.getAppUserId())
                          .eventSource(
                              pAuditInfo.getEventSource() != null
                                  ? pAuditInfo.getEventSource().getDisplayText()
                                  : null)
                          .requestContext(pRequest)
                          .build()));

      return Response.ok().build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
  }

  @PATCH
  @Path("/pipelines/{pipelineName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.updateIngestionPipeline.PATCH")
  public Response updateIngestionPipeline(
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @PathParam("pipelineName") final String pPipelineName,
      final IngestionPipelineView pIngestionPipelineView)
      throws SvcException {

    try {
      final IngestionPipelineView existing =
          _ingestionPipelineUISvc.getIngestionPipelineView(pGroup.getId(), pPipelineName);
      final IngestionPipelineView response =
          _ingestionPipelineUISvc.updateIngestionPipeline(
              pGroup.getId(), pPipelineName, pIngestionPipelineView, pAuditInfo);
      if (existing.getState() != response.getState()) {
        switch (response.getState()) {
          case ACTIVE:
            _segmentEventSvc.submitEvent(
                DataLakePipelineResumedEvent.builder()
                    .pipelineId(response.getId())
                    .groupId(pGroup.getId())
                    .organizationId(pGroup.getOrgId())
                    .userId(pAuditInfo.getAppUserId())
                    .eventSource(
                        pAuditInfo.getEventSource() != null
                            ? pAuditInfo.getEventSource().getDisplayText()
                            : null)
                    .projectId(pGroup.getId())
                    .requestContext(pRequest)
                    .build());
            break;
          case PAUSED:
            _segmentEventSvc.submitEvent(
                DataLakePipelinePausedEvent.builder()
                    .pipelineId(response.getId())
                    .groupId(pGroup.getId())
                    .organizationId(pGroup.getOrgId())
                    .userId(pAuditInfo.getAppUserId())
                    .eventSource(
                        pAuditInfo.getEventSource() != null
                            ? pAuditInfo.getEventSource().getDisplayText()
                            : null)
                    .projectId(pGroup.getId())
                    .requestContext(pRequest)
                    .build());
            break;
        }
      } else {
        _segmentEventSvc.submitEvent(
            DataLakePipelineUpdatedEvent.builder()
                .groupId(pGroup.getId())
                .organizationId(pGroup.getOrgId())
                .pipelineId(response.getId())
                .projectId(pGroup.getId())
                .userId(pAuditInfo.getAppUserId())
                .pipelineSourceType(
                    response
                        .getSource()
                        .map(ingestionSourceView -> ingestionSourceView.getType().name())
                        .orElseThrow(
                            () ->
                                new SvcException(
                                    NDSErrorCode.DLS_UNEXPECTED_ERROR, "Source type not found")))
                .eventSource(
                    pAuditInfo.getEventSource() != null
                        ? pAuditInfo.getEventSource().getDisplayText()
                        : null)
                .requestContext(pRequest)
                .build());
      }

      return Response.ok().entity(response).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
  }

  @GET
  @Path("/pipelines/{pipelineName}/stats")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getIngestionPipelineStats.GET")
  public Response getIngestionPipelineStats(
      @Context final Group pGroup, @PathParam("pipelineName") final String pPipelineName)
      throws SvcException {
    try {
      final IngestionPipelineStatsView response =
          _ingestionPipelineUISvc.getIngestionPipelineStats(pGroup.getId(), pPipelineName);

      return Response.ok().entity(response).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    }
  }

  @GET
  @Path("/pipelines/{pipelineName}/availableSnapshots")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getAvailableSnapshotsForPipeline.GET")
  public Response getAvailableSnapshotsForPipeline(
      @Context final Group pGroup,
      @PathParam("pipelineName") final String pPipelineName,
      @QueryParam("limit") final Integer pLimit)
      throws SvcException {
    try {
      final Integer limit =
          Optional.ofNullable(pLimit).orElse(DEFAULT_BACKUP_SNAPSHOTS_RESPONSE_LIMIT);
      final List<BackupSnapshotView> snapshotViews =
          _ingestionPipelineUISvc.getAvailableBackupSnapshotViews(
              pGroup.getId(), pPipelineName, limit);
      return Response.ok().entity(snapshotViews).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    }
  }

  private Response handleSvcException(final SvcException pSvcException) throws SvcException {
    if (pSvcException.getErrorCode() instanceof NDSErrorCode) {
      final NDSErrorCode errorCode = (NDSErrorCode) pSvcException.getErrorCode();
      final String entity =
          new JSONObject()
              .put("errorCode", errorCode.name())
              .put("message", pSvcException.getMessage())
              .toString();
      switch (errorCode) {
        case INGESTION_PIPELINE_ALREADY_EXISTS:
        case INGESTION_PIPELINE_INVALID_STATE_UPDATE:
        case INGESTION_TRANSFORMATION_FIELD_LIMIT_EXCEEDED:
        case INGESTION_SOURCE_GROUP_ID_DOES_NOT_MATCH_PIPELINE:
        case INVALID_GROUP_ID:
        case INGESTION_PIPELINE_RUN_INVALID_STATE_UPDATE:
        case DUPLICATE_PARTITION_FIELD_NAME:
        case PARTITION_FIELDS_LIMIT_EXCEEDED:
        case PARTITION_FIELDS_ORDER_INVALID:
        case NO_PARTITION_FIELDS:
        case INGESTION_NOT_SUPPORTED_FOR_PAUSED_CLUSTERS:
        case INGESTION_NOT_SUPPORTED_FOR_SHARDED_CLUSTERS:
        case INGESTION_PIPELINE_RUN_IN_PROGRESS:
          return Response.status(Response.Status.BAD_REQUEST).entity(entity).build();
        case CLUSTER_NOT_FOUND:
        case INGESTION_PIPELINE_NOT_FOUND:
        case INGESTION_PIPELINE_RUN_NOT_FOUND:
          return Response.status(Response.Status.NOT_FOUND).entity(entity).build();
      }
    }
    throw pSvcException;
  }

  @GET
  @Path("/pipelines/{pipelineName}/pipelineRuns")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getIngestionPipelineRuns.GET")
  public Response getIngestionPipelineRuns(
      @Context final Group pGroup,
      @PathParam("pipelineName") final String pPipelineName,
      @QueryParam("withDatasets") final boolean pWithDatasets,
      @QueryParam("limit") final Integer pLimit,
      @QueryParam("skip") final Integer pSkip,
      @QueryParam("createdBefore") final String pCreatedBeforeStr,
      @QueryParam("searchQuery") @DefaultValue("") final String pSearchQuery)
      throws Exception {

    final Integer limit = Optional.ofNullable(pLimit).orElse(DEFAULT_PIPELINE_RUN_RESPONSE_LIMIT);
    final Date createdBefore =
        Optional.ofNullable(pCreatedBeforeStr).map(TimeUtils2::fromISOString).orElseGet(Date::new);
    try {
      final List<IngestionPipelineRunView> runs =
          pWithDatasets
              ? _ingestionPipelineUISvc.listIngestionPipelineRunsWithDatasetsForUI(
                  pGroup.getId(), pPipelineName, limit, pSkip, createdBefore, pSearchQuery)
              : _ingestionPipelineUISvc.listIngestionPipelineRunsForUI(
                  pGroup.getId(), pPipelineName, limit, pSkip, createdBefore, pSearchQuery);
      return Response.ok().entity(runs).build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @GET
  @Path("/pipelines/{pipelineName}/pipelineRuns/{pipelineRunId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getIngestionPipelineRun.GET")
  public Response getIngestionPipelineRun(
      @Context final Group pGroup,
      @PathParam("pipelineName") final String pPipelineName,
      @PathParam("pipelineRunId") final ObjectId pPipelineRunId)
      throws SvcException {
    try {
      final IngestionPipelineRunView run =
          _ingestionPipelineUISvc.getIngestionPipelineRun(
              pGroup.getId(), pPipelineName, pPipelineRunId);
      return Response.ok().entity(run).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    }
  }

  @DELETE
  @Path("/pipelines/{pipelineName}/pipelineRuns/{pipelineRunId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.deleteIngestionPipelineRunDataSet.DELETE")
  public Response deleteIngestionPipelineRunDataSet(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("pipelineName") final String pPipelineName,
      @PathParam("pipelineRunId") final ObjectId pPipelineRunId)
      throws SvcException {
    try {
      final IngestionPipelineRunView deletedRun =
          _ingestionPipelineUISvc.deleteIngestionPipelineRunDataSet(
              pGroup.getId(), pPipelineName, pPipelineRunId);

      _segmentEventSvc.submitEvent(
          DataLakePipelineDatasetDeletedEvent.builder()
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .pipelineId(deletedRun.getPipelineId())
              .projectId(pGroup.getId())
              .userId(pAuditInfo.getAppUserId())
              .eventSource(
                  pAuditInfo.getEventSource() != null
                      ? pAuditInfo.getEventSource().getDisplayText()
                      : null)
              .requestContext(pRequest)
              .build());
      return Response.ok().entity(deletedRun).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
  }

  @GET
  @Path("/backupPolicyItems/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getPolicyItemsForCluster.GET")
  public Response getPolicyItemsForCluster(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final List<PolicyItemView> backupPolicyItems =
        _ingestionPipelineUISvc.getPolicyItemsViewsForCluster(pGroup, pClusterName);
    return Response.ok().entity(backupPolicyItems).build();
  }

  @GET
  @Path("/backupPolicyItems")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getPolicyItemMapForGroup.GET")
  public Response getPolicyItemMapForGroup(@Context final Group pGroup) throws SvcException {
    final Map<String, List<PolicyItemView>> backupPolicyItems =
        _ingestionPipelineUISvc.getPolicyItemsViewsPerClusterForGroup(pGroup.getId());
    return Response.ok().entity(backupPolicyItems).build();
  }

  @GET
  @Path("/defaultDataLakeRegionForCluster/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getDefaultDataLakeRegionForCluster.GET")
  public Response getDefaultDataLakeRegionForCluster(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    try {
      final RegionName regionName =
          _ingestionPipelineUISvc.getDefaultDataLakeRegionForCluster(pGroup.getId(), pClusterName);
      return Response.ok().entity(new RegionView(regionName)).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    }
  }

  @GET
  @Path("/snapshots")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getSnapshotsBySnapshotIds.GET")
  public Response getSnapshotsBySnapshotIds(
      @QueryParam("snapshotIds") final List<String> pSnapshotIds) {
    final List<BackupSnapshotView> snapshotViews =
        _ingestionPipelineUISvc.getBackupSnapshotViews(pSnapshotIds);
    return Response.ok().entity(snapshotViews).build();
  }

  @GET
  @Path("/snapshots/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.getSnapshotsForCluster.GET")
  public Response getSnapshotsForCluster(
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("limit") final Integer pLimit)
      throws SvcException {
    try {
      final Integer limit =
          Optional.ofNullable(pLimit).orElse(DEFAULT_BACKUP_SNAPSHOTS_RESPONSE_LIMIT);
      final List<BackupSnapshotView> snapshotViews =
          _ingestionPipelineUISvc.getBackupSnapshotViews(pGroup.getId(), pClusterName, limit);
      return Response.ok().entity(snapshotViews).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    }
  }

  @POST
  @Path("/pipelines/{pipelineName}/trigger")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeStorageResource.createOnDemandPipelineRun.POST")
  public Response createOnDemandPipelineRun(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("pipelineName") final String pPipelineName,
      final TriggerIngestionRequestView pTriggerIngestionRequestView)
      throws SvcException {
    try {
      final DatasetRetentionPolicy datasetRetentionPolicy =
          Optional.ofNullable(pTriggerIngestionRequestView.getDatasetRetentionPolicyView())
              .map(DatasetRetentionPolicyView::toDatasetRetentionPolicy)
              .orElse(null);
      final IngestionPipelineRunView pipelineRunView =
          _ingestionPipelineUISvc.createOnDemandPipelineRun(
              pGroup.getId(),
              pPipelineName,
              pTriggerIngestionRequestView.getSnapshotId(),
              datasetRetentionPolicy);

      _segmentEventSvc.submitEvent(
          OnDemandIngestionTriggeredEvent.builder()
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .pipelineId(pipelineRunView.getPipelineId())
              .projectId(pGroup.getId())
              .userId(pAuditInfo.getAppUserId())
              .eventSource(
                  pAuditInfo.getEventSource() != null
                      ? pAuditInfo.getEventSource().getDisplayText()
                      : null)
              .requestContext(pRequest)
              .build());

      return Response.ok().entity(pipelineRunView).build();
    } catch (final SvcException e) {
      return handleSvcException(e);
    } catch (final UnsupportedOperationException e) {
      return handleUnsupportedOperationException(e);
    }
  }

  private Response handleUnsupportedOperationException(final UnsupportedOperationException pE) {
    final String entity =
        new JSONObject()
            .put("errorCode", NDSErrorCode.INTERNAL.name())
            .put("message", pE.getMessage())
            .toString();
    return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(entity).build();
  }
}
