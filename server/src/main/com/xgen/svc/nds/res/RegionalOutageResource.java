package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.svc.nds.simulateregionoutage.svc.RegionalOutageSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.Map;
import java.util.Optional;

@Path("/nds/regionalOutage")
@Singleton
public class RegionalOutageResource extends NDSBaseResource {

  private final RegionalOutageSvc _regionalOutageSvc;
  private final NDSGroupSvc _ndsGroupSvc;

  @Inject
  public RegionalOutageResource(
      final RegionalOutageSvc pRegionalOutageSvc, final NDSGroupSvc pNDSGroupSvc) {
    _regionalOutageSvc = pRegionalOutageSvc;
    _ndsGroupSvc = pNDSGroupSvc;
  }

  @GET
  @Path("{groupId}/{clusterName}/likely-majority-regional-outage")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.RegionalOutageResource.isClusterInMajorityRegionalOutage.GET")
  public Response isClusterInMajorityRegionalOutage(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Optional<NDSGroup> ndsGroupOpt = _ndsGroupSvc.find(pGroup.getId());
    if (ndsGroupOpt.isEmpty()) {
      return Response.status(
              Status.NOT_FOUND.getStatusCode(),
              String.format("Group with ID %s not found", pGroup.getId()))
          .build();
    }
    final NDSGroup ndsGroup = ndsGroupOpt.get();
    return Response.ok()
        .entity(
            Map.of(
                "likelyRegionalOutage",
                _regionalOutageSvc
                    .getMajorityRegionalOutageLikelihood(ndsGroup, pClusterName)
                    .hasLikelyMajorityRegionalOutage()))
        .build();
  }
}
