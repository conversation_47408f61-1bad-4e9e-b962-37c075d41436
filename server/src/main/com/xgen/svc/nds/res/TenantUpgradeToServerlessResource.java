package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeToServerlessStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.tenantUpgrade.model.ui.TenantUpgradeFeatureUsageView;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeToServerlessSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.json.JSONObject;

@Path("/nds/clusterUpgradeToServerless")
@Singleton
public class TenantUpgradeToServerlessResource {
  private final TenantUpgradeToServerlessSvc _tenantUpgradeToServerlessSvc;
  private final FeatureFlagSvc _featureFlagSvc;

  @Inject
  public TenantUpgradeToServerlessResource(
      final TenantUpgradeToServerlessSvc pTenantUpgradeToServerlessSvc,
      final FeatureFlagSvc pFeatureFlagSvc) {
    _tenantUpgradeToServerlessSvc = pTenantUpgradeToServerlessSvc;
    _featureFlagSvc = pFeatureFlagSvc;
  }

  @POST
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_CLUSTER_MANAGER, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.TenantUpgradeToServerlessResource.startClusterUpgradeToServerless.POST")
  public Response startClusterUpgradeToServerless(
      @Context final AppUser pAppUser,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      final ClusterDescriptionView pClusterDescriptionView)
      throws SvcException {
    if (!pClusterDescriptionView.getCloudProviders().contains(CloudProvider.SERVERLESS)) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
    final TenantUpgradeToServerlessStatus tenantUpgradeToServerlessStatus =
        _tenantUpgradeToServerlessSvc.getUpgradeStatus(pGroup.getId(), pClusterDescriptionView);
    final TenantUpgradeFeatureUsageView tenantUpgradeFeatureUsageView =
        _tenantUpgradeToServerlessSvc.getTenantUpgradeFeatureUsage(
            tenantUpgradeToServerlessStatus, pAppUser);

    if (!_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.SERVERLESS_SHARED_UI_OPTION_ENABLED, null, pGroup)) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED, "Tenant upgrade to serverless is no longer supported");
    }

    // Note: In the UI, we perform checks for feature eligibility 2x.
    //    1st - when the user clicks on the Serverless door (endpoint below)
    //    2nd - when the user actually triggers the migration (this endpoint)
    // We do this to avoid missing any feature usage changes during the period
    // in which the user is editing their cluster.
    if (tenantUpgradeFeatureUsageView.isEligibleToUpgradeToServerlessByFeatureUsage()) {
      _tenantUpgradeToServerlessSvc.startClusterUpgrade(
          pOrganization, pClusterDescriptionView, pGroup.getId(), pAppUser, pAuditInfo, pRequest);
      return Response.accepted(pClusterDescriptionView).build();
    } else {
      final String urlString =
          "https://www.mongodb.com/docs/atlas/reference/serverless-instance-limitations/";
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED_FEATURES_IN_USE_DURING_TENANT_UPGRADE_TO_SERVERLESS,
          tenantUpgradeToServerlessStatus.getClusterName(),
          urlString);
    }
  }

  @POST
  @Path("/{groupId}/getTenantUpgradeFeatureUsage")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_CLUSTER_MANAGER, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.TenantUpgradeToServerlessResource.getTenantUpgradeFeatureUsage.POST")
  public Response getTenantUpgradeFeatureUsage(
      @Context final AppUser pAppUser,
      @Context final Group pGroup,
      final ClusterDescriptionView pClusterDescriptionView)
      throws SvcException {
    if (!pClusterDescriptionView.getCloudProviders().contains(CloudProvider.FREE)) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
    final TenantUpgradeToServerlessStatus tenantUpgradeToServerlessStatus =
        _tenantUpgradeToServerlessSvc.getUpgradeStatus(pGroup.getId(), pClusterDescriptionView);
    final TenantUpgradeFeatureUsageView tenantUpgradeFeatureUsageView =
        _tenantUpgradeToServerlessSvc.getTenantUpgradeFeatureUsage(
            tenantUpgradeToServerlessStatus, pAppUser);
    return Response.ok(tenantUpgradeFeatureUsageView).build();
  }

  // This endpoint is separate from the one above to allow us to check for Charts usage immediately
  // after an upgrade completes to notify users if they need to redirect the data source in their
  // existing chart to a collection in their new serverless instance. See <CLOUDP-136825> for more
  @GET
  @Path("/{groupId}/{deploymentName}/getTenantUpgradeChartsUsage")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.TenantUpgradeToServerlessResource.getTenantUpgradeChartsFeatureUsage.GET")
  public Response getTenantUpgradeChartsFeatureUsage(
      @Context final Group pGroup, @PathParam("deploymentName") final String pDeploymentName)
      throws SvcException {
    final boolean isUsingCharts =
        _tenantUpgradeToServerlessSvc.isClusterUsingCharts(pGroup.getId(), pDeploymentName);
    // This endpoint only cares about the Charts value
    final TenantUpgradeFeatureUsageView tenantUpgradeFeatureUsageView =
        new TenantUpgradeFeatureUsageView(isUsingCharts, false, false);
    return Response.ok(tenantUpgradeFeatureUsageView).build();
  }

  @GET
  @Path("/{groupId}/{deploymentName}/isComplete")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.TenantUpgradeToServerlessResource.getTenantUpgradeToServerlessStatusIsComplete.GET")
  public Response getTenantUpgradeToServerlessStatusIsComplete(
      @Context final Group pGroup, @PathParam("deploymentName") final String pDeploymentName) {
    final Optional<TenantUpgradeToServerlessStatus> tenantUpgradeToServerlessStatus =
        _tenantUpgradeToServerlessSvc.getTenantUpgradeToServerlessStatus(
            pDeploymentName, pGroup.getId());

    final JSONObject responseBody = new JSONObject();
    responseBody.put(
        "tenantUpgradeToServerlessIsComplete",
        tenantUpgradeToServerlessStatus
            .map(TenantUpgradeToServerlessStatus::getState)
            .map(state -> state == BaseTenantUpgradeStatus.State.COMPLETE)
            .orElse(false));
    responseBody.put(
        "tenantUniqueId",
        tenantUpgradeToServerlessStatus
            .map(TenantUpgradeToServerlessStatus::getTargetTenantUniqueId)
            .map(ObjectId::toString)
            .orElse(""));
    return Response.ok().entity(responseBody.toString()).build();
  }
}
