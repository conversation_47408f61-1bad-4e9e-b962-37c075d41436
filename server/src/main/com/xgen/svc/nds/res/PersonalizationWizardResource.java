package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.externalanalytics._public.model.PersonalizationWizardEvent;
import com.xgen.cloud.externalanalytics._public.model.PersonalizationWizardEvent.EventName;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.personalizationwizard._public.model.PersonalizationWizardResponse;
import com.xgen.cloud.personalizationwizard._public.svc.PersonalizationWizardSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.nds.model.ui.PersonalizationWizardView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/{groupId}/setup/personalization")
@Singleton
public class PersonalizationWizardResource extends NDSBaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(PersonalizationWizardResource.class);
  private final SegmentEventSvc _segmentEventSvc;
  private final UserSvc _userSvc;
  private final PersonalizationWizardSvc _personalizationWizardSvc;

  @Inject
  public PersonalizationWizardResource(
      final SegmentEventSvc pSegmentEventSvc,
      final UserSvc pUserSvc,
      final PersonalizationWizardSvc pPersonalizationWizardSvc) {
    _segmentEventSvc = pSegmentEventSvc;
    _userSvc = pUserSvc;
    _personalizationWizardSvc = pPersonalizationWizardSvc;
  }

  @PUT
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.PersonalizationWizardResource.submitPersonalizationWizard.PUT")
  public Response submitPersonalizationWizard(
      @Context final Group pGroup,
      @Context final Organization pOrg,
      @Context final AppUser pUser,
      final PersonalizationWizardView pForm) {

    final PersonalizationWizardResponse response;
    try {
      response = pForm.getPersonalizationWizardResponse(pUser.getId());
      final boolean isUpdate =
          _personalizationWizardSvc.upsertPersonalizationWizardResponse(response);
      _userSvc.setFlaggedForPersonalizationWizard(pUser.getId(), false);

      _segmentEventSvc.submitEvent(
          PersonalizationWizardEvent.builder(
                  isUpdate ? EventName.FORM_UPDATED : EventName.FORM_SUBMITTED)
              .organizationId(pOrg.getId())
              .groupId(pGroup.getId())
              .userId(pUser.getId())
              .selectedAtlasUsage(pForm.getSelectedAtlasUsage())
              .selectedAtlasUsageQuestion(pForm.getSelectedAtlasUsageQuestion())
              .selectedLanguage(pForm.getSelectedLanguage())
              .selectedLanguageQuestion(pForm.getSelectedLanguageQuestion())
              .levelExpertise(pForm.getLevelExpertise())
              .levelExpertiseQuestion(pForm.getLevelExpertiseQuestion())
              .projectDataTypes(pForm.getProjectDataTypes())
              .projectDataTypesQuestion(pForm.getProjectDataTypesQuestion())
              .projectDataTypesOther(pForm.getProjectDataTypesOther())
              .projectDataTypesOtherQuestion(pForm.getProjectDataTypesOtherQuestion())
              .appArchitecturalModels(pForm.getAppArchitecturalModels())
              .appArchitecturalModelsQuestion(pForm.getAppArchitecturalModelsQuestion())
              .build());

    } catch (final Exception ex) {
      LOG.error("Failed to save and set personalization wizard response.", ex);
      return Response.serverError().entity(ex.getMessage()).build();
    }
    return Response.ok().entity(response).build();
  }
}
