package com.xgen.svc.nds.res;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.QUEUED_ADMIN_ACTION_CANCELLED;
import static com.xgen.cloud.nds.planning.common._public.util.NDSMoveTags.BACKING_CLOUD_PROVIDER_TAG;
import static com.xgen.cloud.nds.planning.common._public.util.NDSMoveTags.REGION_TAG;
import static com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob.Type.PROCESS_AUTOMATION_CONFIG_PER_CLUSTER;
import static com.xgen.module.iam.annotation.RequireCustomerGrantForEmployeeAccess.ValuesAvailableWithGrantedLogAccess;
import static com.xgen.module.iam.annotation.RequireCustomerGrantForEmployeeAccess.ValuesExemptFromAccessGrantRequirement;
import static com.xgen.svc.nds.svc.project.NDSReactiveAutoScaleSvc.getClusterInstanceSizeBounds;
import static com.xgen.svc.nds.svc.project.NDSReactiveAutoScaleSvc.getShardInstanceSizeBounds;
import static java.util.Optional.ofNullable;

import com.amazonaws.services.ec2.model.AvailabilityZone;
import com.amazonaws.services.ec2.model.CapacityReservation;
import com.amazonaws.services.ec2.model.Filter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.Streams;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.access.activity._public.audit.AccessAuditEvent;
import com.xgen.cloud.access.activity._public.event.AccessEvent.Type;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.alerts.checks.system._public.model.AvailabilityZoneIssue;
import com.xgen.cloud.alerts.checks.system._public.svc.AvailabilityZoneIssueSvc;
import com.xgen.cloud.alerts.checks.system._public.view.AvailabilityZoneIssueView;
import com.xgen.cloud.atm.agentjobs._public.agentjobs.LDAPVerifyConnectivityJobRequestSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationConfigQuerySvc;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationValidationSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.chef._public.model.api.ApiPrivateChefServerStatusView;
import com.xgen.cloud.chef._public.svc.ChefServerStatusSvc;
import com.xgen.cloud.clusterdraft._public.svc.ClusterDraftSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.Scope;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.FeatureCompatibilityVersion;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.security._public.tls.CipherSuite;
import com.xgen.cloud.common.security._public.tls.TLSVersion;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.cps.backupjob._private.dao.RegionDownConfigDao;
import com.xgen.cloud.cps.backupjob._public.ui.RegionDownConfigView;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.StrategyName;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ServerlessStreamingBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.TenantUpgradeToServerlessBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.TenantUpgradeToServerlessBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.VMBasedReplSetRestoreJob;
import com.xgen.cloud.deployment._public.model.AgentConfig;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.AutomationErrorCode;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.MongoDbBuild;
import com.xgen.cloud.deployment._public.model.MongoDbVersion;
import com.xgen.cloud.deployment._public.model.ValidationException;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.serverless.ServerlessMetricsSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.Host.UserWriteBlockMode;
import com.xgen.cloud.monitoring.topology._public.model.Host.UserWriteBlockReason;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.cloud.monitoring.topology._public.model.ping.PingUtils;
import com.xgen.cloud.monitoring.topology._public.svc.HostLastPingSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Builder;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessInstanceAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerAudit;
import com.xgen.cloud.nds.admin._public.model.AdminClusterLock;
import com.xgen.cloud.nds.admin._public.model.AdminNote;
import com.xgen.cloud.nds.admin._public.model.ui.AdminNoteView;
import com.xgen.cloud.nds.admin._public.svc.AdminClusterLockSvc;
import com.xgen.cloud.nds.admin._public.svc.AdminNoteSvc;
import com.xgen.cloud.nds.admin._public.svc.NDSSupportTicketAuditorSvc;
import com.xgen.cloud.nds.autoscaling.common._public.model.UiViewConverter;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.CriterionAndMetricView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.CriterionAndMetricView.MetricView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.UpdateReactiveComputeAutoScalingView;
import com.xgen.cloud.nds.autoscaling.context._private.dao.AutoScalingContextDao;
import com.xgen.cloud.nds.autoscaling.context._public.model.AutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ClusterComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ShardComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.SingleScalingCriteria;
import com.xgen.cloud.nds.autoscaling.metrics._public.model.context.ComputeMetricsContext;
import com.xgen.cloud.nds.autoscaling.metrics._public.model.metrics.AutoScalingHostsMetrics;
import com.xgen.cloud.nds.autoscaling.metrics._public.svc.DebugComputeClusterMetricsCaluclatorSvc;
import com.xgen.cloud.nds.aws._private.dao.privatelink.AWSPrivateLinkTargetGroupDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSLeakedItem;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSMultiTargetConnectionRule;
import com.xgen.cloud.nds.aws._public.model.ui.AWSSubnetView;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.aws._public.view.admincapacity.ScheduledAWSInstanceCapacitySpecView;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._private.dao.privatelink.AzurePrivateLinkConnectionInboundNATRuleDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType.PreferredStorageType;
import com.xgen.cloud.nds.azure._public.model.AzureLeakedItem;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureCheckResult;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureInstanceCapacitySpec;
import com.xgen.cloud.nds.checkmetadataconsistency._public.svc.NDSCheckMetadataConsistencySvc;
import com.xgen.cloud.nds.checkmetadataconsistency._public.view.NDSCheckMetadataConsistencyRequestView;
import com.xgen.cloud.nds.cloudprovider._public.model.AZBalancingRequirement;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSMTMProfile;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSOrphanedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanedItem;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionalDedicatedCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.ZoneDistributionStatus;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckResult;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.InstanceCapacitySpec;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSizeBuilderFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.InstanceSizeAutoScaleBounds;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ScaleDirection;
import com.xgen.cloud.nds.cloudprovider._public.model.cloudproviderconsole.NDSCloudProviderConsoleInformationType;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.RegionNameView;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.admincapacity.CheckCapacityRequestView;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.admincapacity.ODCRInfoView;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.admincapacity.TargetedODCRRequestView;
import com.xgen.cloud.nds.cloudprovider._public.svc.admincapacity.CloudProviderCheckCapacityRequestSvc;
import com.xgen.cloud.nds.cloudprovider._public.svc.admincapacity.GenericCheckCapacityRequestSvc;
import com.xgen.cloud.nds.cloudprovider._public.util.ModelValidationUtils;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.NDSProcessType;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.ProxyProtocolForPrivateLinkMode;
import com.xgen.cloud.nds.common._public.model.ThrottleState;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.common._public.util.OsUtil;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.cloud.nds.datavalidation._public.model.DataValidationArguments;
import com.xgen.cloud.nds.datavalidation._public.model.DataValidationRecord;
import com.xgen.cloud.nds.datavalidation._public.model.DataValidationRun;
import com.xgen.cloud.nds.datavalidation._public.view.CancelDataValidationRequestView;
import com.xgen.cloud.nds.datavalidation._public.view.DataValidationAggregationResultsAdminView;
import com.xgen.cloud.nds.datavalidation._public.view.DataValidationArgumentsView;
import com.xgen.cloud.nds.datavalidation._public.view.DataValidationRecordAdminView;
import com.xgen.cloud.nds.datavalidation._public.view.SearchDataValidationRecordAdminView;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexNDSDefaults;
import com.xgen.cloud.nds.flex._public.model.NDSFlexMTMProfile;
import com.xgen.cloud.nds.flex._public.model.ui.FlexRecordAdminView;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.NDSSharedMTMProfile;
import com.xgen.cloud.nds.free._public.model.ui.FastSharedRecordAdminView;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectionDao;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectionRuleDao;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPLeakedItem;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPPrivateServiceConnection;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.leakeditem._public.model.OrphanedItemView;
import com.xgen.cloud.nds.leakeditem._public.svc.LeakedItemSvc;
import com.xgen.cloud.nds.maintenance._public.svc.NDSInternalMaintenanceRolloutSvc;
import com.xgen.cloud.nds.maintenance._public.view.NDSInternalMaintenanceRolloutView;
import com.xgen.cloud.nds.metrics._public.svc.NDSComputeClusterMetricsSvc;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.util._public.svc.MongotunePolicyArgsMergerUtil;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.ui.MongotuneStatusWithPolicyArgsAdminView;
import com.xgen.cloud.nds.mongotune.settings._public.svc.MongotuneAppSettings;
import com.xgen.cloud.nds.mtmcompaction._public.model.MTMCompaction;
import com.xgen.cloud.nds.mtmcompaction._public.svc.MTMCompactionSvc;
import com.xgen.cloud.nds.mtmcompaction._public.view.MTMCompactionView;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveDataLakeConfig;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveExpirationHistory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveHistory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun;
import com.xgen.cloud.nds.planning.common._public.svc.NDSPlanExecutorJobPrioritySvc;
import com.xgen.cloud.nds.planning.common._public.util.NDSMoveTags;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.FleetAttributesDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.NDSLogDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.admin.NDSAdminJobDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FixedVersionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.OsTunedFileOverrides;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ResurrectOptions;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgsUpdatable;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.FTDCExport;
import com.xgen.cloud.nds.project._public.model.FleetAttributes;
import com.xgen.cloud.nds.project._public.model.FleetAttributes.FleetAttribute;
import com.xgen.cloud.nds.project._public.model.MaintenanceType;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroup.MTMHolderType;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.NDSLog;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory;
import com.xgen.cloud.nds.project._public.model.QueuedAdminAction;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.SchedulingBehavior;
import com.xgen.cloud.nds.project._public.model.admin.CriticalMaintenanceRunChunkedJobState;
import com.xgen.cloud.nds.project._public.model.admin.NDSAdminJob;
import com.xgen.cloud.nds.project._public.model.api.NdsMaintenanceHistoryView;
import com.xgen.cloud.nds.project._public.model.privatelink.ClusterContainerGroup;
import com.xgen.cloud.nds.project._public.model.privatelink.ConnectionRuleWithHostname;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion.FixedBy;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion.UnFixedBy;
import com.xgen.cloud.nds.project._public.svc.NDSLogSvc;
import com.xgen.cloud.nds.project._public.svc.NdsMaintenanceHistorySvc;
import com.xgen.cloud.nds.project._public.svc.admin.CriticalMaintenanceRunChunkJobStateSvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc;
import com.xgen.cloud.nds.project._public.util.ClusterContainerGroupUtil;
import com.xgen.cloud.nds.project._public.util.InstanceSizeUtil;
import com.xgen.cloud.nds.project._public.util.NDSMaintenanceDateCalculationUtil;
import com.xgen.cloud.nds.project._public.view.NDSLogView;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.security._public.model.ui.AcmeCaStatusView;
import com.xgen.cloud.nds.security._public.svc.NDSACMEFailoverSvc;
import com.xgen.cloud.nds.serverless._public.model.NDSServerlessMTMProfile;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.ui.FastServerlessRecordAdminView;
import com.xgen.cloud.nds.serverless._public.model.ui.MTMTenantView;
import com.xgen.cloud.nds.serverless._public.svc.SentinelDBAccessInfoSvc;
import com.xgen.cloud.nds.tenant._public.model.FastTenantPreAllocatedRecord.State;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.nds.tenant._public.model.privatelink.MultiTenantEndpointService;
import com.xgen.cloud.nds.tenant._public.model.ui.FastTenantRecordContainer;
import com.xgen.cloud.nds.tenant._public.model.ui.MultiTenantEndpointServiceView;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeLogDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeLogDao.LogType;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeToServerlessStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ui.TenantUpgradeAdminView;
import com.xgen.cloud.nds.tenantupgrade._public.model.ui.TenantUpgradeToServerlessAdminView;
import com.xgen.cloud.organization._public.model.OrgLimits.FieldsDefs;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchAdminSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result.Status;
import com.xgen.module.common.planner.model.Step.Delimiter;
import com.xgen.module.common.planner.model.ui.PlanExecutorJobPriorityConfigValueView;
import com.xgen.module.common.planner.model.ui.PlanExecutorJobPrioritySettingView;
import com.xgen.module.common.planner.svc.PlanExecutorJobPriorityConfigValueSvc;
import com.xgen.module.iam.annotation.RequireCustomerGrantForEmployeeAccess;
import com.xgen.module.liveimport.common.LiveImportErrorCode;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.svc.atm.svc.ClientMetadataSvc;
import com.xgen.svc.core.dao.base.SearchOperator;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.svc.ApiCloudProviderSvc;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasMongoDbBuildView;
import com.xgen.svc.mms.api.view.mapper.admincapacity.CheckCapacityRequestViewMapper;
import com.xgen.svc.mms.dao.uniformfrontend.UniformFrontendEnvoyConfigurationDao;
import com.xgen.svc.mms.dao.uniformfrontend.UniformFrontendLoadBalancingDeploymentDao;
import com.xgen.svc.mms.model.uniformfrontend.UniformFrontendEnvoyConfiguration;
import com.xgen.svc.mms.model.uniformfrontend.UniformFrontendLoadBalancingDeployment;
import com.xgen.svc.mms.res.filter.TestUtility;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.mms.res.view.cluster.HostView;
import com.xgen.svc.mms.res.view.cluster.HostView.HostViewBuilder;
import com.xgen.svc.mms.svc.common.OrgErrorCode;
import com.xgen.svc.mms.svc.deployment.DeploymentSvc;
import com.xgen.svc.nds.aws.model.ui.AWSPrivateLinkTargetGroupAdminView;
import com.xgen.svc.nds.aws.planner.AWSSyncPauseStateMove;
import com.xgen.svc.nds.aws.svc.AWSCloudProviderContainerSvc;
import com.xgen.svc.nds.aws.svc.AWSConnectionRuleWithHostnameSvc;
import com.xgen.svc.nds.aws.svc.NDSAWSLogDownloadSvc;
import com.xgen.svc.nds.aws.svc.NDSAWSLogDownloadSvc.SupportedLogs;
import com.xgen.svc.nds.aws.svc.admincapacity.AWSCheckCapacityRequestSvc;
import com.xgen.svc.nds.azure.model.ui.AzurePrivateLinkConnectionInboundNATRuleAdminView;
import com.xgen.svc.nds.azure.planner.AzureSyncPauseStateMove;
import com.xgen.svc.nds.azure.svc.AzureConnectionRuleWithHostnameSvc;
import com.xgen.svc.nds.azure.svc.admincapacity.AzureCheckCapacityRequestSvc;
import com.xgen.svc.nds.datavalidation.svc.DataValidationExportCsvSvc;
import com.xgen.svc.nds.datavalidation.svc.DataValidationSvcImpl;
import com.xgen.svc.nds.flex.model.ui.FlexMTMClusterView;
import com.xgen.svc.nds.flex.svc.NDSFlexFastProvisioningSvc;
import com.xgen.svc.nds.free.FreeNDSDefaults;
import com.xgen.svc.nds.free.svc.NDSSharedFastProvisioningSvc;
import com.xgen.svc.nds.gcp.model.ui.GCPPSCConnectionRuleAdminView;
import com.xgen.svc.nds.gcp.model.ui.GCPPrivateServiceConnectionAdminView;
import com.xgen.svc.nds.gcp.planner.GCPSyncPauseStateMove;
import com.xgen.svc.nds.gcp.svc.GCPConnectionRuleWithHostnameSvc;
import com.xgen.svc.nds.gcp.svc.GCPPrivateServiceConnectSvc;
import com.xgen.svc.nds.gcp.svc.GCPQuotaUsageSvc;
import com.xgen.svc.nds.liveimport.svc.LiveImportOverridesSvc;
import com.xgen.svc.nds.liveimport.svc.LiveImportSvc;
import com.xgen.svc.nds.liveimport.view.LiveImportAdminView;
import com.xgen.svc.nds.liveimport.view.LiveImportOverridesView;
import com.xgen.svc.nds.model.ui.AZBalancingOverrideRequirementPatchView;
import com.xgen.svc.nds.model.ui.AZBalancingOverrideRequirementView;
import com.xgen.svc.nds.model.ui.AZBalancingRegionConfigOverrideRequirementPatchView;
import com.xgen.svc.nds.model.ui.AZBalancingRegionConfigOverrideRequirementView;
import com.xgen.svc.nds.model.ui.AZBalancingRequirementView;
import com.xgen.svc.nds.model.ui.AZCapacityImpactedClustersView;
import com.xgen.svc.nds.model.ui.CipherSuiteView;
import com.xgen.svc.nds.model.ui.CloudProviderSettingsView;
import com.xgen.svc.nds.model.ui.ClusterConnectionStringConfigurationRequestView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsAdminView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.DenyListEntryCreateView;
import com.xgen.svc.nds.model.ui.DenyListEntryDeleteView;
import com.xgen.svc.nds.model.ui.FTDCExportSettingsRequestView;
import com.xgen.svc.nds.model.ui.FTDCExportSettingsResponseView;
import com.xgen.svc.nds.model.ui.FleetAttributesView;
import com.xgen.svc.nds.model.ui.ForcePlanningReplicaSetView.ForcePlanningHostView;
import com.xgen.svc.nds.model.ui.GCPProjectQuotaView;
import com.xgen.svc.nds.model.ui.GroupStatusView;
import com.xgen.svc.nds.model.ui.HostClusterView;
import com.xgen.svc.nds.model.ui.LeakedItemView;
import com.xgen.svc.nds.model.ui.LeakedItemsContainer;
import com.xgen.svc.nds.model.ui.MTMClusterView;
import com.xgen.svc.nds.model.ui.MongotProcessArgsView;
import com.xgen.svc.nds.model.ui.NDSExtraMaintenanceDeferralGrantView;
import com.xgen.svc.nds.model.ui.NDSGroupMaintenanceWindowAdminView;
import com.xgen.svc.nds.model.ui.NDSPlanSearchContainer;
import com.xgen.svc.nds.model.ui.NDSThrottledCollectionView;
import com.xgen.svc.nds.model.ui.NDSVersionUpdateView;
import com.xgen.svc.nds.model.ui.NDSVersionView;
import com.xgen.svc.nds.model.ui.OAV3MigrationContainer;
import com.xgen.svc.nds.model.ui.OnlineArchiveExpirationHistoryAdminView;
import com.xgen.svc.nds.model.ui.OnlineArchiveHistoryEstimatedStatsView;
import com.xgen.svc.nds.model.ui.OnlineArchiveRegenerateConfigPreviewView;
import com.xgen.svc.nds.model.ui.OnlineArchiveV3MigrationJobsView;
import com.xgen.svc.nds.model.ui.OnlineArchiveV3MigrationView;
import com.xgen.svc.nds.model.ui.OsTunedFileOverridesView;
import com.xgen.svc.nds.model.ui.PinnedClusterView;
import com.xgen.svc.nds.model.ui.PlanExtraInformationView;
import com.xgen.svc.nds.model.ui.PlanView;
import com.xgen.svc.nds.model.ui.PrivateEndpointConnectionRuleClusterUsageAdminView;
import com.xgen.svc.nds.model.ui.PrivateEndpointConnectionRuleLoadBalancerUsageAdminView;
import com.xgen.svc.nds.model.ui.PrivateEndpointConnectionRuleUsageAdminView;
import com.xgen.svc.nds.model.ui.PrivateLinkConnectionRulesByProviderAdminView;
import com.xgen.svc.nds.model.ui.ProxyPrefixView;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.model.ui.ResurrectOptionsView;
import com.xgen.svc.nds.model.ui.SchedulingMetadataView;
import com.xgen.svc.nds.model.ui.SharedMTMClusterView;
import com.xgen.svc.nds.model.ui.TLSVersionView;
import com.xgen.svc.nds.model.ui.cloudProviderConsole.NDSCloudProviderConsoleInformationDefaultsView;
import com.xgen.svc.nds.model.ui.cloudProviderConsole.NDSCloudProviderConsoleInformationRequestView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineRunView;
import com.xgen.svc.nds.model.ui.dls.IngestionPipelineView;
import com.xgen.svc.nds.model.ui.search.MongotClusterVersionView;
import com.xgen.svc.nds.onlinearchive.v3migration.svc.OnlineArchiveV3MigrationSvc;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import com.xgen.svc.nds.planner.WaitForDiskWarmingCompleteStep;
import com.xgen.svc.nds.planner.WaitForMachineHealthyMove;
import com.xgen.svc.nds.serverless.model.ui.ServerlessMTMClusterView;
import com.xgen.svc.nds.serverless.model.ui.autoScaling.pool.ServerlessMTMPoolView;
import com.xgen.svc.nds.serverless.svc.NDSAutoScaleServerlessMTMCapacitySvc;
import com.xgen.svc.nds.serverless.svc.NDSServerlessFastProvisioningSvc;
import com.xgen.svc.nds.serverless.svc.NDSServerlessLoadSvc;
import com.xgen.svc.nds.serverless.svc.NDSServerlessUISvc;
import com.xgen.svc.nds.serverless.svc.ServerlessMTMPoolSvc;
import com.xgen.svc.nds.serverless.svc.ServerlessMTMProjectSvc;
import com.xgen.svc.nds.serverless.util.ServerlessSetupMTMBackupPolicyUtil;
import com.xgen.svc.nds.svc.AZSelectionSvc;
import com.xgen.svc.nds.svc.AdminActionSvc;
import com.xgen.svc.nds.svc.AdminActionSvc.HostAction;
import com.xgen.svc.nds.svc.CloudChefConfSvc;
import com.xgen.svc.nds.svc.CustomMongoDbBuildSvc;
import com.xgen.svc.nds.svc.FTDCExportCacheSvc;
import com.xgen.svc.nds.svc.IngestionPipelineUISvc;
import com.xgen.svc.nds.svc.NDSAdminBackupSnapshotSvc;
import com.xgen.svc.nds.svc.NDSAdminJobSvc;
import com.xgen.svc.nds.svc.NDSAdminSearchSvc;
import com.xgen.svc.nds.svc.NDSAdminSvc;
import com.xgen.svc.nds.svc.NDSAdminSvc.FileExtension;
import com.xgen.svc.nds.svc.NDSAutomationConfigSvc;
import com.xgen.svc.nds.svc.NDSCloudProviderConsoleInformationSvc;
import com.xgen.svc.nds.svc.NDSDataLakePrivateSvc;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import com.xgen.svc.nds.svc.NDSMTMProfileSvc;
import com.xgen.svc.nds.svc.NDSOrphanedItemSvc;
import com.xgen.svc.nds.svc.NDSPrivateLinkLimitsSvc;
import com.xgen.svc.nds.svc.NDSServerAccessSvc;
import com.xgen.svc.nds.svc.NDSServerAccessSvc.SessionRecordingsInfo;
import com.xgen.svc.nds.svc.NDSSetupFlexSvc;
import com.xgen.svc.nds.svc.NDSSetupFreeTierSvc;
import com.xgen.svc.nds.svc.NDSSetupServerlessSvc;
import com.xgen.svc.nds.svc.TenantClusterConfigurationSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveDataLakeConfigSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc.ClusterCollectionTypeResult;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSReactiveAutoScaleSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.MultiTenantEndpointServiceSvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import com.xgen.svc.nds.tenantUpgrade.TenantUpgradeErrorCode;
import com.xgen.svc.nds.tenantUpgrade.svc.ServerlessFreeMigrationStatusSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.ServerlessUpgradeToDedicatedSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeToServerlessSvc;
import com.xgen.svc.nds.util.ClusterValidationUtil;
import com.xgen.svc.nds.util.IndexConfigUtil;
import com.xgen.svc.security.acme.model.ACMEProvider;
import com.xgen.svc.streams.planner.StreamsUtil;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URI;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.bson.BasicBSONObject;
import org.bson.json.JsonMode;
import org.bson.json.JsonWriterSettings;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

@Path("/admin/nds")
@Singleton
public class NDSAdminResource extends NDSBaseResource {

  private static final Long DEFAULT_LOG_TIME_RANGE_MS = 24 * 60 * 60 * 1000L;
  protected static final int DEFAULT_NUM_SERVERLESS_TOP_TENANTS = 10;
  protected static final int DEFAULT_MTM_LOAD_LIMIT = 50;
  protected static final boolean DEFAULT_RETRIEVE_CACHED_LOAD = false;
  protected static final Duration COMPLETED_TENANT_UPGRADE_LOOK_BACK_TIME = Duration.ofHours(48);

  private static final Logger LOG = LoggerFactory.getLogger(NDSAdminResource.class);
  private static final int NON_ANCHORED_THROTTLING_MAX_DELAY_MILLIS =
      (int) Duration.ofMinutes(1).toMillis();
  private static final int MAX_REASON_LENGTH = 120;
  private static final int LAST_N_PIPELINE_RUNS = 50;
  private static final int LAST_N_ARCHIVE_HISTORIES = 10;
  private static final int LAST_N_ARCHIVE_EXPIRATION_HISTORIES = 10;
  private static final int LAST_N_ARCHIVE_RUNS = 50;
  private static final int MAINTENANCE_HISTORY_LIMIT = 1000;

  private static final Pattern JIRA_TICKET_PATTERN = Pattern.compile("[A-Z]+-\\d+");
  private static final String MAINTENANCE_DISPLAY_TIME_FORMAT_PATTERN =
      "yyyy-MM-dd EEEE 'at' hh:mm a z";

  private final NDSUISvc _ndsUISvc;
  private final NDSClusterSvc _clusterSvc;

  private final ConfigLimitSvc _configLimitSvc;
  private final NDSOrphanedItemSvc _orphanedItemSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final NDSLogSvc _ndsLogSvc;
  private final NDSMTMProfileSvc _ndsMTMProfileSvc;
  private final PlanDao _planDao;
  private final NDSGroupDao _ndsGroupDao;
  private final GroupDao _groupDao;
  private final GroupSvc _groupSvc;
  private final NDSAdminSearchSvc _ndsAdminSearchSvc;
  private final ClientMetadataSvc _clientMetadataSvc;
  private final ReplicaSetHardwareDao _hardwareDao;
  private final ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  private final NDSAWSLogDownloadSvc _ndsAWSLogDownloadSvc;
  private final LiveImportSvc _liveImportSvc;
  private final LiveImportOverridesSvc _liveImportOverridesSvc;
  private final TenantUpgradeSvc _tenantUpgradeSvc;
  private final TenantUpgradeToServerlessSvc _tenantUpgradeToServerlessSvc;
  private final ServerlessUpgradeToDedicatedSvc _serverlessUpgradeToDedicatedSvc;
  private final HostSvc _hostSvc;
  private final HostLastPingSvc _hostLastPingSvc;
  private final AuditSvc _auditSvc;
  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final NDSAdminJobSvc _ndsAdminJobSvc;
  private final NDSAdminJobDao _ndsAdminJobDao;
  private final AzureSubscriptionDao _azureSubscriptionDao;
  private final DataValidationSvcImpl _dataValidationSvc;
  private final DataValidationExportCsvSvc _dataValidationExportCsvSvc;
  private final NDSACMEFailoverSvc _ndsacmeFailoverSvc;
  private final AWSPrivateLinkTargetGroupDao _targetGroupDao;
  private final AzurePrivateLinkConnectionInboundNATRuleDao _inboundNATRuleDao;
  private final TenantClusterConfigurationSvc _tenantClusterConfigurationSvc;
  private final NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;
  private final NDSMaintenanceDateCalculationUtil _ndsMaintenanceDateCalculationUtil;
  private final LeakedItemSvc _leakedItemSvc;
  private final AutomationConfigPublishingSvc _automationConfigPublishingSvc;
  private final NDSAutomationConfigSvc _ndsAutomationConfigSvc;
  private final NDSDataLakePublicSvc _ndsDataLakePublicSvc;
  private final OnlineArchiveSvc _onlineArchiveSvc;
  private final OnlineArchiveDataLakeConfigSvc _onlineArchiveDataLakeSvc;
  private final ApiCloudProviderSvc _apiCloudProviderSvc;
  private final CpsSvc _cpsSvc;
  private final OrganizationSvc _organizationSvc;
  private final ServerlessMTMPoolSvc _serverlessMTMPoolSvc;
  private final ServerlessMTMProjectSvc _serverlessMTMProjectSvc;
  private final AuthzSvc _authzSvc;
  private final NDSServerlessUISvc _ndsServerlessUISvc;
  private final NDSCloudProviderConsoleInformationSvc _cloudProviderConsoleSvc;
  private final DeploymentSvc _deploymentSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final LDAPVerifyConnectivityJobRequestSvc _ldapVerifyConnectivityJobRequestSvc;
  private final AppSettings _appSettings;
  private final NDSComputeClusterMetricsSvc _ndsComputeClusterMetricsSvc;
  private final NDSAutoScaleServerlessMTMCapacitySvc _ndsAutoScaleServerlessMTMCapacitySvc;
  private final NDSServerlessLoadSvc _ndsServerlessLoadSvc;
  private final NDSAdminBackupSnapshotSvc _ndsAdminBackupSnapshotSvc;
  private final GCPPrivateServiceConnectionDao _gcpPrivateServiceConnectionDao;
  private final GCPPrivateServiceConnectionRuleDao _gcpPrivateServiceConnectionRuleDao;
  private final CloudChefConfSvc _cloudChefConfSvc;
  private final GCPQuotaUsageSvc _gcpQuotaUsageSvc;
  private final GCPPrivateServiceConnectSvc _gcpPrivateServiceConnectSvc;
  private final NDSSetupServerlessSvc _ndsSetupServerlessSvc;
  private final NDSSetupFreeTierSvc _ndsSetupFreeTierSvc;
  private final NDSSetupFlexSvc _ndsSetupFlexSvc;
  private final RegionDownConfigDao _regionDownConfigDao;
  private final GCPApiSvc _gcpApiSvc;
  private final NDSLookupSvc _ndsLookupSvc;
  private final ChefServerStatusSvc _chefServerStatusSvc;
  private final PlanExecutorJobPriorityConfigValueSvc _planExecutorJobPriorityConfigValueSvc;
  private final MultiTenantEndpointServiceSvc _multiTenantEndpointServiceSvc;
  private final NDSPlanExecutorJobPrioritySvc _ndsPlanExecutorJobPrioritySvc;
  private final NDSTenantEndpointSvc _ndsTenantEndpointSvc;
  private final AWSApiSvc _awsApiSvc;
  private final NdsMaintenanceHistorySvc _ndsMaintenanceHistorySvc;
  private final AvailabilityZoneIssueSvc _availabilityZoneIssueSvc;
  private final NDSSharedFastProvisioningSvc _ndsSharedFastProvisioningSvc;
  private final NDSFlexFastProvisioningSvc _ndsFlexFastProvisioningSvc;
  private final NDSServerlessFastProvisioningSvc _ndsServerlessFastProvisioningSvc;
  private final AdminActionSvc _adminActionSvc;
  private final AdminClusterLockSvc _adminClusterLockSvc;
  private final MTMCompactionSvc _mtmCompactionSvc;
  private final AdminNoteSvc _adminNoteSvc;
  private final CustomMongoDbBuildSvc _customMongoDbBuildSvc;
  private final NDSAdminSvc _ndsAdminSvc;
  private final OnlineArchiveV3MigrationSvc _onlineArchiveV3MigrationSvc;
  private final SearchAdminSvc _searchAdminSvc;
  private final SearchInstanceSvc _searchInstanceSvc;
  private final IngestionPipelineUISvc _ingestionPipelineUISvc;
  private final CriticalMaintenanceRunChunkJobStateSvc _criticalMaintenanceRunChunkJobStateSvc;
  private final ServerlessSetupMTMBackupPolicyUtil _serverlessSetupMTMBackupPolicyUtil;
  private final GenericCheckCapacityRequestSvc _genericCheckCapacityRequestSvc;
  private final AWSCheckCapacityRequestSvc _awsCheckCapacityRequestSvc;
  private final AzureCheckCapacityRequestSvc _azureCheckCapacityRequestSvc;
  private final AutomationValidationSvc _automationValidationSvc;
  private final AWSCloudProviderContainerSvc _awsCloudProviderContainerSvc;
  private final AutomationMongoDbVersionSvc _automationMongoDbVersionSvc;
  private final NDSReactiveAutoScaleSvc _ndsReactiveAutoScaleSvc;
  private final SentinelDBAccessInfoSvc _sentinelDBAccessInfoSvc;
  private final ClusterDraftSvc _clusterDraftSvc;
  private final DebugComputeClusterMetricsCaluclatorSvc _computeClusterMetricsCalculatorSvc;
  private final AutoScalingContextDao _autoScalingContextDao;
  private final NDSSupportTicketAuditorSvc _supportTicketAuditorSvc;
  private final UniformFrontendLoadBalancingDeploymentDao
      _uniformFrontendLoadBalancingDeploymentDao;
  private final UniformFrontendEnvoyConfigurationDao _uniformFrontendEnvoyConfigurationDao;
  private final NDSServerAccessSvc _ndsServerAccessSvc;
  private final NDSCheckMetadataConsistencySvc _ndsCheckMetadataConsistencySvc;
  private final NDSPrivateLinkLimitsSvc _ndsPrivateLinkLimitsSvc;
  private final AWSConnectionRuleWithHostnameSvc _awsConnectionRuleWithHostnameSvc;
  private final AzureConnectionRuleWithHostnameSvc _azureConnectionRuleWithHostnameSvc;
  private final GCPConnectionRuleWithHostnameSvc _gcpConnectionRuleWithHostnameSvc;
  private final ClusterValidationUtil _clusterValidationUtil;
  private final ServerlessFreeMigrationStatusSvc _serverlessFreeMigrationStatusSvc;
  private final SearchDeploymentDescriptionSvc _searchDeploymentDescriptionSvc;
  private final FleetAttributesDao _fleetAttributesDao;
  private final AZSelectionSvc _azSelectionSvc;
  private final NDSInternalMaintenanceRolloutSvc _ndsInternalMaintenanceRolloutSvc;
  private final ElevatedHealthMonitoringSvc _elevatedHealthMonitoringSvc;
  private final NDSDataLakePrivateSvc _ndsDataLakePrivateSvc;
  private final ServerlessMetricsSvc _serverlessMetricsSvc;
  private final AutomationConfigQuerySvc _automationConfigQuerySvc;
  private final MongotuneAppSettings _mongotuneAppSettings;

  @Inject
  public NDSAdminResource(
      final NDSClusterSvc pClusterSvc,
      final ConfigLimitSvc pConfigLimitSvc,
      final NDSOrphanedItemSvc pOrphanedItemSvc,
      final NDSUISvc pNDSUISvc,
      final NDSGroupSvc pNDSGroupSvc,
      final NDSLogSvc pNDSLogSvc,
      final NDSMTMProfileSvc pNDSMTMProfileSvc,
      final PlanDao pPlanDao,
      final NDSGroupDao pNdsGroupDao,
      final GroupDao pGroupDao,
      final NDSAdminSearchSvc pNDSAdminSearchSvc,
      final ReplicaSetHardwareDao pHardwareDao,
      final NDSAWSLogDownloadSvc pNDSAWSLogDownloadSvc,
      final ReplicaSetHardwareSvc pReplicaSetHardwareSvc,
      final LiveImportSvc pLiveImportSvc,
      final LiveImportOverridesSvc pLiveImportOverridesSvc,
      final TenantUpgradeSvc pTenantUpgradeSvc,
      final TenantUpgradeToServerlessSvc pTenantUpgradeToServerlessSvc,
      final ServerlessUpgradeToDedicatedSvc pServerlessUpgradeToDedicatedSvc,
      final HostSvc pHostSvc,
      final HostLastPingSvc pHostLastPingSvc,
      final AuditSvc pAuditSvc,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final ClientMetadataSvc pClientMetadataSvc,
      final NDSAdminJobSvc pNDSAdminJobSvc,
      final NDSAdminJobDao pNDSAdminJobDao,
      final NDSACMEFailoverSvc pNdsAcmeFailoverSvc,
      final AzureSubscriptionDao pAzureSubscriptionDao,
      final DataValidationSvcImpl pDataValidationSvc,
      final DataValidationExportCsvSvc pDataValidationExportCsvSvc,
      final AWSPrivateLinkTargetGroupDao pTargetGroupDao,
      final AzurePrivateLinkConnectionInboundNATRuleDao pInboundNatRuleDao,
      final TenantClusterConfigurationSvc pTenantClusterConfigurationSvc,
      final NDSGroupMaintenanceSvc pNDSGroupMaintenanceSvc,
      final NDSMaintenanceDateCalculationUtil pNdsMaintenanceDateCalculationUtil,
      final LeakedItemSvc pLeakedItemSvc,
      final AutomationConfigPublishingSvc pAutomationConfigPublishingSvc,
      final NDSAutomationConfigSvc pNDSAutomationConfigSvc,
      final NDSDataLakePublicSvc pNdsDataLakePublicSvc,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final OnlineArchiveDataLakeConfigSvc pOnlineArchiveDataLakeSvc,
      final ApiCloudProviderSvc pApiCloudProviderSvc,
      final CpsSvc pCpsSvc,
      final OrganizationSvc pOrganizationSvc,
      final ServerlessMTMPoolSvc pServerlessMTMPoolSvc,
      final ServerlessMTMProjectSvc pServerlessMTMProjectSvc,
      final AuthzSvc pAuthzSvc,
      final NDSServerlessUISvc pNDSServerlessUISvc,
      final NDSCloudProviderConsoleInformationSvc pCloudProviderConsoleSvc,
      final DeploymentSvc pDeploymentSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final LDAPVerifyConnectivityJobRequestSvc pLDAPVerifyConnectivityJobRequestSvc,
      final AppSettings pAppSettings,
      NDSComputeClusterMetricsSvc pNdsComputeClusterMetricsSvc,
      final NDSAutoScaleServerlessMTMCapacitySvc pNDSAutoScaleServerlessMTMCapacitySvc,
      final NDSServerlessLoadSvc pNDSServerlessLoadSvc,
      final NDSAdminBackupSnapshotSvc pNDSAdminBackupSnapshotSvc,
      final GCPPrivateServiceConnectionRuleDao pGcpPrivateServiceConnectionRuleDao,
      final GCPPrivateServiceConnectionDao pGcpPrivateServiceConnectionDao,
      final CloudChefConfSvc pCloudChefConfSvc,
      final GCPQuotaUsageSvc pGCPQuosaUsageSvc,
      final GCPPrivateServiceConnectSvc pGcpPrivateServiceConnectSvc,
      final NDSSetupServerlessSvc pNDSSetupServerlessSvc,
      final NDSSetupFreeTierSvc pNDSSetupFreeTierSvc,
      final RegionDownConfigDao pRegionDownConfigDao,
      final GroupSvc pGroupSvc,
      final NDSSetupFlexSvc pNdsSetupFlexSvc,
      final GCPApiSvc pGCPApiSvc,
      final NDSLookupSvc pNDSLookupSvc,
      final ChefServerStatusSvc pChefServerStatusSvc,
      final MultiTenantEndpointServiceSvc pMultiTenantEndpointServiceSvc,
      final PlanExecutorJobPriorityConfigValueSvc planExecutorJobPriorityConfigValueSvc,
      final NDSPlanExecutorJobPrioritySvc pPlanExecutorJobPrioritySvc,
      final NDSTenantEndpointSvc pNdsTenantEndpointSvc,
      final AWSApiSvc pAWSApiSvc,
      final NdsMaintenanceHistorySvc pNdsMaintenanceHistorySvc,
      final AvailabilityZoneIssueSvc pAvailabilityZoneIssueSvc,
      final NDSSharedFastProvisioningSvc pNDSSharedFastProvisioningSvc,
      final NDSFlexFastProvisioningSvc pNDSFlexFastProvisioningSvc,
      final NDSServerlessFastProvisioningSvc pNDSServerlessFastProvisioningSvc,
      final AdminActionSvc pAdminActionSvc,
      final AdminClusterLockSvc pAdminClusterLockSvc,
      final MTMCompactionSvc pMTMCompactionSvc,
      final AdminNoteSvc pAdminNoteSvc,
      final CustomMongoDbBuildSvc pCustomMongoDbBuildSvc,
      final NDSAdminSvc ndsAdminSvc,
      final OnlineArchiveV3MigrationSvc pOnlineArchiveV3MigrationSvc,
      final SearchInstanceSvc pSearchInstanceSvc,
      final SearchAdminSvc pSearchAdminSvc,
      final IngestionPipelineUISvc pIngestionPipelineUISvc,
      final CriticalMaintenanceRunChunkJobStateSvc pCriticalMaintenanceRunChunkJobStateSvc,
      final ServerlessSetupMTMBackupPolicyUtil pServerlessSetupMTMBackupPolicyUtil,
      final GenericCheckCapacityRequestSvc pGenericCheckCapacityRequestSvc,
      final AWSCheckCapacityRequestSvc pAWSCheckCapacityRequestSvc,
      final AzureCheckCapacityRequestSvc pAzureCheckCapacityRequestSvc,
      final AutomationValidationSvc pAutomationValidationSvc,
      final AWSCloudProviderContainerSvc pAwsCloudProviderContainerSvc,
      final AutomationMongoDbVersionSvc pAutomationMongoDbVersionSvc,
      final NDSReactiveAutoScaleSvc pNDSAutoScaleSvc,
      final SentinelDBAccessInfoSvc pSentinelDBAccessInfoSvc,
      final DebugComputeClusterMetricsCaluclatorSvc pComputeClusterMetricsCalculatorSvc,
      final AutoScalingContextDao pAutoScalingContextDao,
      final NDSSupportTicketAuditorSvc pSupportTicketAuditorSvc,
      final ClusterDraftSvc pClusterDraftSvc,
      final UniformFrontendLoadBalancingDeploymentDao pUniformFrontendLoadBalancingDeploymentDao,
      final UniformFrontendEnvoyConfigurationDao pUniformFrontendEnvoyConfigurationDao,
      final NDSServerAccessSvc pNDSServerAccessSvc,
      final NDSCheckMetadataConsistencySvc pNDSCheckMetadataConsistencySvc,
      final NDSPrivateLinkLimitsSvc pNDSPrivateLinkLimitsSvc,
      final AWSConnectionRuleWithHostnameSvc pAWSConnectionRuleWithHostnameSvc,
      final AzureConnectionRuleWithHostnameSvc pAzureConnectionRuleWithHostnameSvc,
      final GCPConnectionRuleWithHostnameSvc pGCPConnectionRuleWithHostnameSvc,
      final ClusterValidationUtil pClusterValidationUtil,
      final ServerlessFreeMigrationStatusSvc pServerlessFreeMigrationStatusSvc,
      final SearchDeploymentDescriptionSvc pSearchDeploymentDescriptionSvc,
      final FleetAttributesDao pFleetAttributesDao,
      final AZSelectionSvc pAZSelectionSvc,
      final NDSInternalMaintenanceRolloutSvc pNdsInternalMaintenanceRolloutSvc,
      final ElevatedHealthMonitoringSvc pElevatedHealthMonitoringSvc,
      final NDSDataLakePrivateSvc pNDSDataLakePrivateSvc,
      final ServerlessMetricsSvc pServerlessMetricsSvc,
      final AutomationConfigQuerySvc pAutomationConfigQuerySvc,
      final MongotuneAppSettings pMongotuneAppSettings) {

    _ndsUISvc = pNDSUISvc;
    _clusterSvc = pClusterSvc;
    _configLimitSvc = pConfigLimitSvc;
    _orphanedItemSvc = pOrphanedItemSvc;
    _ndsGroupSvc = pNDSGroupSvc;
    _ndsLogSvc = pNDSLogSvc;
    _planDao = pPlanDao;
    _ndsGroupDao = pNdsGroupDao;
    _groupDao = pGroupDao;
    _ndsAdminSearchSvc = pNDSAdminSearchSvc;
    _ndsAWSLogDownloadSvc = pNDSAWSLogDownloadSvc;
    _hardwareDao = pHardwareDao;
    _replicaSetHardwareSvc = pReplicaSetHardwareSvc;
    _serverlessUpgradeToDedicatedSvc = pServerlessUpgradeToDedicatedSvc;
    _hostSvc = pHostSvc;
    _hostLastPingSvc = pHostLastPingSvc;
    _auditSvc = pAuditSvc;
    _liveImportSvc = pLiveImportSvc;
    _liveImportOverridesSvc = pLiveImportOverridesSvc;
    _tenantUpgradeSvc = pTenantUpgradeSvc;
    _tenantUpgradeToServerlessSvc = pTenantUpgradeToServerlessSvc;
    _clusterDescriptionDao = pClusterDescriptionDao;
    _clientMetadataSvc = pClientMetadataSvc;
    _ndsAdminJobSvc = pNDSAdminJobSvc;
    _ndsAdminJobDao = pNDSAdminJobDao;
    _azureSubscriptionDao = pAzureSubscriptionDao;
    _dataValidationSvc = pDataValidationSvc;
    _dataValidationExportCsvSvc = pDataValidationExportCsvSvc;
    _ndsacmeFailoverSvc = pNdsAcmeFailoverSvc;
    _targetGroupDao = pTargetGroupDao;
    _inboundNATRuleDao = pInboundNatRuleDao;
    _tenantClusterConfigurationSvc = pTenantClusterConfigurationSvc;
    _ndsMTMProfileSvc = pNDSMTMProfileSvc;
    _ndsGroupMaintenanceSvc = pNDSGroupMaintenanceSvc;
    _ndsMaintenanceDateCalculationUtil = pNdsMaintenanceDateCalculationUtil;
    _leakedItemSvc = pLeakedItemSvc;
    _automationConfigPublishingSvc = pAutomationConfigPublishingSvc;
    _ndsAutomationConfigSvc = pNDSAutomationConfigSvc;
    _ndsDataLakePublicSvc = pNdsDataLakePublicSvc;
    _onlineArchiveSvc = pOnlineArchiveSvc;
    _onlineArchiveDataLakeSvc = pOnlineArchiveDataLakeSvc;
    _apiCloudProviderSvc = pApiCloudProviderSvc;
    _cpsSvc = pCpsSvc;
    _organizationSvc = pOrganizationSvc;
    _serverlessMTMPoolSvc = pServerlessMTMPoolSvc;
    _serverlessMTMProjectSvc = pServerlessMTMProjectSvc;
    _authzSvc = pAuthzSvc;
    _ndsServerlessUISvc = pNDSServerlessUISvc;
    _cloudProviderConsoleSvc = pCloudProviderConsoleSvc;
    _deploymentSvc = pDeploymentSvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _ldapVerifyConnectivityJobRequestSvc = pLDAPVerifyConnectivityJobRequestSvc;
    _appSettings = pAppSettings;
    _ndsComputeClusterMetricsSvc = pNdsComputeClusterMetricsSvc;
    _ndsAutoScaleServerlessMTMCapacitySvc = pNDSAutoScaleServerlessMTMCapacitySvc;
    _ndsServerlessLoadSvc = pNDSServerlessLoadSvc;
    _ndsAdminBackupSnapshotSvc = pNDSAdminBackupSnapshotSvc;
    _gcpPrivateServiceConnectionDao = pGcpPrivateServiceConnectionDao;
    _gcpPrivateServiceConnectionRuleDao = pGcpPrivateServiceConnectionRuleDao;
    _cloudChefConfSvc = pCloudChefConfSvc;
    _gcpQuotaUsageSvc = pGCPQuosaUsageSvc;
    _gcpPrivateServiceConnectSvc = pGcpPrivateServiceConnectSvc;
    _ndsSetupServerlessSvc = pNDSSetupServerlessSvc;
    _ndsSetupFreeTierSvc = pNDSSetupFreeTierSvc;
    _regionDownConfigDao = pRegionDownConfigDao;
    _ndsSetupFlexSvc = pNdsSetupFlexSvc;
    _ndsLookupSvc = pNDSLookupSvc;
    _chefServerStatusSvc = pChefServerStatusSvc;
    _groupSvc = pGroupSvc;
    _gcpApiSvc = pGCPApiSvc;
    _planExecutorJobPriorityConfigValueSvc = planExecutorJobPriorityConfigValueSvc;
    _multiTenantEndpointServiceSvc = pMultiTenantEndpointServiceSvc;
    _ndsPlanExecutorJobPrioritySvc = pPlanExecutorJobPrioritySvc;
    _ndsTenantEndpointSvc = pNdsTenantEndpointSvc;
    _awsApiSvc = pAWSApiSvc;
    _ndsMaintenanceHistorySvc = pNdsMaintenanceHistorySvc;
    _availabilityZoneIssueSvc = pAvailabilityZoneIssueSvc;
    _ndsSharedFastProvisioningSvc = pNDSSharedFastProvisioningSvc;
    _ndsFlexFastProvisioningSvc = pNDSFlexFastProvisioningSvc;
    _ndsServerlessFastProvisioningSvc = pNDSServerlessFastProvisioningSvc;
    _adminActionSvc = pAdminActionSvc;
    _adminClusterLockSvc = pAdminClusterLockSvc;
    _mtmCompactionSvc = pMTMCompactionSvc;
    _adminNoteSvc = pAdminNoteSvc;
    _customMongoDbBuildSvc = pCustomMongoDbBuildSvc;
    _ndsAdminSvc = ndsAdminSvc;
    _onlineArchiveV3MigrationSvc = pOnlineArchiveV3MigrationSvc;
    _searchAdminSvc = pSearchAdminSvc;
    _searchInstanceSvc = pSearchInstanceSvc;
    _ingestionPipelineUISvc = pIngestionPipelineUISvc;
    _criticalMaintenanceRunChunkJobStateSvc = pCriticalMaintenanceRunChunkJobStateSvc;
    _serverlessSetupMTMBackupPolicyUtil = pServerlessSetupMTMBackupPolicyUtil;
    _genericCheckCapacityRequestSvc = pGenericCheckCapacityRequestSvc;
    _awsCheckCapacityRequestSvc = pAWSCheckCapacityRequestSvc;
    _azureCheckCapacityRequestSvc = pAzureCheckCapacityRequestSvc;
    _automationValidationSvc = pAutomationValidationSvc;
    _awsCloudProviderContainerSvc = pAwsCloudProviderContainerSvc;
    _automationMongoDbVersionSvc = pAutomationMongoDbVersionSvc;
    _ndsReactiveAutoScaleSvc = pNDSAutoScaleSvc;
    _sentinelDBAccessInfoSvc = pSentinelDBAccessInfoSvc;
    _computeClusterMetricsCalculatorSvc = pComputeClusterMetricsCalculatorSvc;
    _autoScalingContextDao = pAutoScalingContextDao;
    _supportTicketAuditorSvc = pSupportTicketAuditorSvc;
    _clusterDraftSvc = pClusterDraftSvc;
    _uniformFrontendLoadBalancingDeploymentDao = pUniformFrontendLoadBalancingDeploymentDao;
    _uniformFrontendEnvoyConfigurationDao = pUniformFrontendEnvoyConfigurationDao;
    _ndsServerAccessSvc = pNDSServerAccessSvc;
    _ndsCheckMetadataConsistencySvc = pNDSCheckMetadataConsistencySvc;
    _ndsPrivateLinkLimitsSvc = pNDSPrivateLinkLimitsSvc;
    _awsConnectionRuleWithHostnameSvc = pAWSConnectionRuleWithHostnameSvc;
    _azureConnectionRuleWithHostnameSvc = pAzureConnectionRuleWithHostnameSvc;
    _gcpConnectionRuleWithHostnameSvc = pGCPConnectionRuleWithHostnameSvc;
    _clusterValidationUtil = pClusterValidationUtil;
    _serverlessFreeMigrationStatusSvc = pServerlessFreeMigrationStatusSvc;
    _searchDeploymentDescriptionSvc = pSearchDeploymentDescriptionSvc;
    _fleetAttributesDao = pFleetAttributesDao;
    _azSelectionSvc = pAZSelectionSvc;
    _ndsInternalMaintenanceRolloutSvc = pNdsInternalMaintenanceRolloutSvc;
    _elevatedHealthMonitoringSvc = pElevatedHealthMonitoringSvc;
    _ndsDataLakePrivateSvc = pNDSDataLakePrivateSvc;
    _serverlessMetricsSvc = pServerlessMetricsSvc;
    _automationConfigQuerySvc = pAutomationConfigQuerySvc;
    _mongotuneAppSettings = pMongotuneAppSettings;
  }

  static OrphanedItem.Type getCloudProviderLeakedItemType(
      final String pType, final CloudProvider pCloudProvider) {
    return switch (pCloudProvider) {
      case AWS -> AWSLeakedItem.Type.valueOf(pType);
      case AZURE -> AzureLeakedItem.Type.valueOf(pType);
      case GCP -> GCPLeakedItem.Type.valueOf(pType);
      default ->
          throw new IllegalArgumentException(
              "Unsupported Cloud Provider: " + pCloudProvider.name());
    };
  }

  private static CriterionAndMetricView entryToCriterionAndMetricView(
      final Entry<SingleScalingCriteria, AutoScalingHostsMetrics> pEntry) {
    return new CriterionAndMetricView(
        UiViewConverter.toSingleScalingCriterionViewWithoutIntervalThreshold(pEntry.getKey()),
        pEntry.getValue().thresholdsToHostMetrics().entrySet().stream()
            .map(entry2 -> new MetricView(entry2.getValue()))
            .toList());
  }

  @GET
  @Path("/dataProcessingRegions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataProcessingRegions.GET")
  public Response getDataProcessingRegions() throws Exception {
    final Map<String, List<String>> dataProcessingRegionsByProvider =
        Streams.concat(
                AWSNDSDefaults.COMMERCIAL_REGIONS.stream(),
                GCPNDSDefaults.AVAILABLE_REGIONS.stream(),
                AzureNDSDefaults.AVAILABLE_REGIONS.stream())
            .map(RegionName::getDataProcessingRegion)
            .distinct()
            .collect(
                Collectors.toMap(
                    r -> r.getProvider().name(),
                    r -> new ArrayList<>(List.of(r.getName())),
                    (existingList, latestList) -> {
                      existingList.addAll(latestList);
                      return existingList;
                    }));

    return Response.ok(dataProcessingRegionsByProvider).build();
  }

  @GET
  @Path("/search")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.adminSearch.GET")
  public Response adminSearch(
      @QueryParam("searchStrings") final List<String> pSearchList,
      @QueryParam("operator") final SearchOperator operator)
      throws Exception {
    return Response.ok(_ndsAdminSearchSvc.adminSearchWithStringsAndOperator(pSearchList, operator))
        .build();
  }

  @GET
  @Path("/search/groups/{groupId}/plans")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.adminPlanSearch.GET")
  public Response adminPlanSearch(
      @Context final NDSGroup pNDSGroup,
      @QueryParam("skip") @DefaultValue("0") final int pSkip,
      @QueryParam("limit") @DefaultValue("25") final int pLimit,
      @QueryParam("startDate") @DefaultValue("0") final Long pStartDate,
      @QueryParam("endDate") @DefaultValue("0") final Long pEndDate,
      @QueryParam("clusterName") @DefaultValue("") final String pClusterName) {

    final Date startDate = (pStartDate == 0) ? null : new Date(pStartDate);
    final Date endDate = (pEndDate == 0) ? null : new Date(pEndDate);

    final String clusterName = pClusterName.isEmpty() ? null : pClusterName;

    // Try an extra record to calculate if there's a next page
    final List<PlanView> plans =
        _ndsAdminSearchSvc.searchPlansForAdminUI(
            pNDSGroup, clusterName, startDate, endDate, pSkip, pLimit + 1);

    final List<PlanView> data = plans.size() < pLimit ? plans : plans.subList(0, pLimit);
    final boolean isLastPage = plans.size() == 0 || plans.size() <= pLimit;
    final NDSPlanSearchContainer result =
        NDSPlanSearchContainer.builder()
            .data(data)
            .skip(pSkip)
            .limit(pLimit)
            .firstPage(pSkip <= 0)
            .lastPage(isLastPage)
            .build();
    return Response.ok().entity(result).build();
  }

  @GET
  @Path("/search/plansForRestoreJob")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.adminPlansForRestore.GET")
  public Response adminPlansForRestore(@QueryParam("restoreId") final ObjectId pRestoreJobId) {
    final Optional<Set<String>> planIds = _ndsAdminSvc.getAdminPlansForRestore(pRestoreJobId);
    final StringBuilder url = new StringBuilder("/v2/admin#/atlas/search?operator=OR&search=");
    String str = String.join("%20", planIds.orElse(Collections.emptySet()));
    url.append(str);

    return Response.seeOther(URI.create(url.toString())).build();
  }

  @POST
  @Path("/groups/{groupId}/dataProcessingRegion")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setDataProcessingRegion.POST")
  public Response setDataProcessingRegion(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      final RegionNameView pRegionNameView)
      throws SvcException {
    _ndsGroupSvc.setDataProcessingRegion(pGroupId, pRegionNameView.toRegionName(), pAuditInfo);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/groups/{groupId}/gcpQuotas/{gcpOrgId}/{gcpProjectId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getGCPQuotaLimits.GET")
  public Response getGCPQuotaLimits(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("gcpOrgId") final ObjectId pGCPOrgId,
      @PathParam("gcpProjectId") final String pGCPProjectId)
      throws SvcException {
    try {
      final NDSGroup ndsGroup =
          _ndsGroupSvc
              .find(pGroupId)
              .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
      final boolean gcpProjectIdBelongsToGroup =
          ndsGroup.getCloudProviderContainerByType(CloudProvider.GCP).stream()
              .filter(CloudProviderContainer::isProvisioned)
              .map(c -> (GCPCloudProviderContainer) c)
              .anyMatch(c -> c.getGcpProjectId().get().equals(pGCPProjectId));
      if (!gcpProjectIdBelongsToGroup) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT);
      }
      return Response.ok(
              new GCPProjectQuotaView(
                  _gcpQuotaUsageSvc.getQuotaUsagesAndLimits(pGroupId, pGCPOrgId, pGCPProjectId)))
          .build();
    } catch (final GCPApiException e) {
      throw new SvcException(CommonErrorCode.OPERATION_ERROR, e);
    }
  }

  @GET
  @Path("/orgs/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOrganizationById.GET")
  public Response getOrganizationById(@PathParam("orgId") final ObjectId pOrgId) {
    final Organization org = _organizationSvc.findById(pOrgId);
    if (org == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    return Response.ok().entity(org).build();
  }

  @GET
  @Path("/gcp/{organizationId}/{projectId}/projectNumber")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getProjectNumber.GET")
  public Response getProjectNumber(
      @PathParam("organizationId") final ObjectId pOrganizationId,
      @PathParam("projectId") final String pProjectId) {
    final BasicDBObject projectInfoDB = new BasicDBObject();
    projectInfoDB.put(GCPCloudProviderContainer.FieldDefs.ORGANIZATION_ID, pOrganizationId);
    projectInfoDB.put(GCPCloudProviderContainer.FieldDefs.PROJECT_ID, pProjectId);
    projectInfoDB.put(
        "projectNumber",
        _gcpApiSvc.getProject(pOrganizationId, LOG, pProjectId).getProjectNumber());
    return Response.ok(projectInfoDB).build();
  }

  @GET
  @Path("/groups/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getNDSGroupById.GET")
  public Response getNDSGroupById(@Context final Group pGroup) {
    final BasicDBObject groupDoc = (BasicDBObject) _ndsGroupSvc.getNDSGroupById(pGroup.getId());
    if (groupDoc == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final NDSGroup ndsGroup = new NDSGroup(groupDoc);
    final List<DBObject> cloudProviderContainerDBObject = new ArrayList<>();
    final List<CloudProviderContainer> containers = ndsGroup.getCloudProviderContainers();
    containers.forEach(
        container -> {
          switch (container.getCloudProvider()) {
            case AWS:
              final boolean hasAllAvailabilityZones;
              final String awsAccountName;
              final AWSCloudProviderContainer awsContainer = (AWSCloudProviderContainer) container;
              final int numSubnets = awsContainer.getSubnets().length;
              final AWSRegionName awsRegionName = awsContainer.getRegion();
              try {
                final AWSAccount account =
                    _ndsUISvc
                        .getAWSAccountById(
                            ((AWSCloudProviderContainer) container).getAWSAccountId())
                        .orElseThrow();
                awsAccountName = account.getName();

                final int numAvailabilityZones =
                    account.getRegions().stream()
                        .filter(region -> region.getName() == awsRegionName)
                        .toList()
                        .get(0)
                        .getAvailabilityZones()
                        .size();

                hasAllAvailabilityZones = numSubnets >= numAvailabilityZones;
                cloudProviderContainerDBObject.add(
                    container
                        .toDBObject()
                        .append("accountName", awsAccountName)
                        .append("hasAllAvailabilityZones", hasAllAvailabilityZones)
                        .append("providerRegionName", awsRegionName.getValue()));

              } catch (final SvcException pSvcException) {
                cloudProviderContainerDBObject.add(container.toDBObject());
                LOG.error("An error occurred while trying to get AWS account by id.");
              }
              break;
            case AZURE:
              final String azureSubscriptionName;
              final AzureCloudProviderContainer azureContainer =
                  (AzureCloudProviderContainer) container;
              final AzureRegionName azureRegionName = azureContainer.getRegion();
              try {
                azureSubscriptionName =
                    _azureSubscriptionDao
                        .find(azureContainer.getAzureSubscriptionId())
                        .orElseThrow()
                        .getName();
                cloudProviderContainerDBObject.add(
                    container
                        .toDBObject()
                        .append("azureSubscriptionName", azureSubscriptionName)
                        .append("providerRegionName", azureRegionName.getValue()));
              } catch (final Exception pException) {
                cloudProviderContainerDBObject.add(container.toDBObject());
                LOG.error("An error occurred while trying to get Azure subscription by id.");
              }
              break;
            case FREE:
            case SERVERLESS:
            case FLEX:
              // Append the MTMs hardware info
              try {
                cloudProviderContainerDBObject.add(
                    container
                        .toDBObject()
                        .append(
                            "mtmReplicaSetHardwares",
                            _hardwareDao.findByClusterAsDBObject(
                                ((TenantCloudProviderContainer) container).getGroupId(),
                                ((TenantCloudProviderContainer) container).getClusterName())));
              } catch (final Exception pException) {
                cloudProviderContainerDBObject.add(container.toDBObject());
                LOG.error(
                    "An error occurred while trying to get MTM replica set hardware by group id &"
                        + " cluster name");
              }
              break;
            default:
              cloudProviderContainerDBObject.add(container.toDBObject());
              break;
          }
        });
    groupDoc.replace("cloudProviderContainers", cloudProviderContainerDBObject);
    groupDoc.put("allowAdvancedTesting", NDSSettings.getAllowAdvancedTesting(_appSettings));
    groupDoc.put("isSystemProject", groupDoc.getString("sourceGroupId", null) != null);

    final Organization organization = _organizationSvc.findById(pGroup.getOrgId());
    groupDoc.put("restrictEmployeeAccess", organization.getRestrictEmployeeAccess());
    groupDoc.put(
        "nonEditClusterDraftsCount",
        _clusterDraftSvc.countClusterDrafts(false, null, null, pGroup.getId(), false));
    return Response.ok(groupDoc).build();
  }

  @GET
  @Path("/groups/{groupId}/limits")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getNDSGroupLimits.GET")
  public Response getNDSGroupLimits(@Context final Group pGroup) throws Exception {
    return Response.ok(_ndsGroupSvc.getNDSGroupLimitsById(pGroup.getId())).build();
  }

  @GET
  @Path("/orgs/{orgId}/limits")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOrganizationLimits.GET")
  public Response getOrganizationLimits(@PathParam("orgId") final ObjectId pOrgId)
      throws Exception {
    return Response.ok(_organizationSvc.getOrgLimitsById(pOrgId)).build();
  }

  @PATCH
  @Path("/orgs/{orgId}/limits")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setCustomOrgLimit.PATCH")
  public Response setCustomOrgLimit(
      @Context final AuditInfo pAuditInfo,
      @PathParam("orgId") final ObjectId pOrgId,
      @FormParam("limit") final String pLimitName,
      @FormParam("value") final Long pValue)
      throws Exception {
    setLimitOnOrg(pOrgId, pLimitName, pValue, pAuditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/orgs/{orgId}/limits/serviceAccounts")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServiceAccountsLimits.GET")
  public Response getServiceAccountsLimits(@PathParam("orgId") final ObjectId pOrgId) {
    return Response.ok(_configLimitSvc.getMaxServiceAccountsPerOrg(pOrgId)).build();
  }

  @PATCH
  @Path("/orgs/{orgId}/limits/serviceAccounts")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setCustomServiceAccountsLimit.PATCH")
  public Response setCustomServiceAccountsLimit(
      @PathParam("orgId") final ObjectId pOrgId, @FormParam("value") final Long pValue)
      throws SvcException {
    try {
      _configLimitSvc.setMaxServiceAccountsPerOrg(pOrgId, pValue.intValue());
      return Response.ok().build();
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    }
  }

  @GET
  @Path("/orgs/{orgId}/limits/networkAddresses")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getNetworkAddressesLimits.GET")
  public Response getNetworkAddressesLimits(@PathParam("orgId") final ObjectId pOrgId) {
    return Response.ok(_configLimitSvc.getMaxNetworkAddressesPerServiceAccount(pOrgId)).build();
  }

  @PATCH
  @Path("/orgs/{orgId}/limits/networkAddresses")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setCustomNetworkAddressesLimit.PATCH")
  public Response setCustomNetworkAddressesLimit(
      @PathParam("orgId") final ObjectId pOrgId, @FormParam("value") final Long pValue)
      throws SvcException {
    try {
      _configLimitSvc.setMaxNetworkAddressesPerServiceAccount(pOrgId, pValue.intValue());
      return Response.ok().build();
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    }
  }

  @GET
  @Path("/groups/{groupId}/ldapVerifyConnectivityJob")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLDAPVerifyConnectivityJob.GET")
  public Response getLDAPVerifyConnectivityJob(@Context final Group pGroup) throws Exception {
    return Response.ok(
            _ldapVerifyConnectivityJobRequestSvc.getLDAPVerifyConnectivityJobRequestByGroupId(
                pGroup.getId()))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/operatorFeatureFlags")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY})
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOperatorFeatureFlags.GET")
  public Response getOperatorFeatureFlags(@Context final Group pGroup) {
    final JSONObject responseObject = new JSONObject();
    for (FeatureFlag flag : FeatureFlag.OPERATOR_FEATURE_FLAGS) {
      if (flag.getScope() == Scope.GROUP) {
        responseObject.put(flag.name(), isFeatureFlagEnabled(flag, _appSettings, null, pGroup));
      }
    }
    return Response.ok(responseObject.toString()).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterClientMetadata")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterClientMetadata.GET")
  public Response getClusterClientMetadata(
      @Context final Group pGroup,
      @QueryParam("hosts") final List<String> pHosts,
      @QueryParam("durationHours") @DefaultValue("24") final int pDurationHours)
      throws Exception {
    if (pDurationHours < 0) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hours must non-negative");
    }
    final List<String> hostsList;
    if (pHosts != null) {
      hostsList = pHosts;
      for (final String host : hostsList) {
        if (!ValidationUtils.isValidHostName(host)) {
          throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "invalid host");
        }
      }
    } else {
      hostsList = Collections.emptyList();
    }
    final List<DBObject> clientMetadata =
        _clientMetadataSvc.getRecentClientMetadata(pGroup.getId(), hostsList, pDurationHours);
    return Response.ok(clientMetadata).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterDescriptions.GET")
  public Response getClusterDescriptions(@Context final Group pGroup) {
    return Response.ok(_clusterSvc.getClusterDescriptionsForGroup(pGroup.getId())).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterDescription.GET")
  public Response getClusterDescription(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws Exception {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }
    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName);
    if (clusterDescription.isPresent()) {
      return Response.ok(clusterDescription.get().toDBObject()).build();
    } else {
      final Optional<ClusterDescription> deletedClusterDescription =
          _clusterSvc.getClusterDescriptionIncludingDeleted(pGroup.getId(), pClusterName);
      if (deletedClusterDescription.isPresent()) {
        return Response.ok(deletedClusterDescription.get().toDBObject()).build();
      }
    }
    return Response.status(Response.Status.NOT_FOUND).build();
  }

  @GET
  @Path("/clusterDescriptions/{uniqueId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterDescription.GET")
  public Response getClusterDescription(@PathParam("uniqueId") final ObjectId pUniqueId) {
    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getActiveClusterDescription(null, pUniqueId);
    if (clusterDescription.isPresent()) {
      return Response.ok(clusterDescription.get().toDBObject()).build();
    }
    return Response.status(Response.Status.NOT_FOUND).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptionUpdates")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterDescriptionUpdates.GET")
  public Response getClusterDescriptionUpdates(@Context final Group pGroup) {
    return Response.ok(_clusterSvc.getClusterDescriptionUpdatesForGroup(pGroup.getId())).build();
  }

  @GET
  @Path("/groups/{groupId}/mergedClusterDescription/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMergedClusterDescription.GET")
  public Response getMergedClusterDescription(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws Exception {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getMergedClusterDescription(pGroup.getId(), pClusterName);
    if (clusterDescription.isPresent()) {
      return Response.ok(clusterDescription.get().toDBObject()).build();
    } else {
      final Optional<ClusterDescription> deletedClusterDescription =
          _clusterSvc.getClusterDescriptionIncludingDeleted(pGroup.getId(), pClusterName);
      if (deletedClusterDescription.isPresent()) {
        return Response.ok(deletedClusterDescription.get().toDBObject()).build();
      }
    }

    return Response.status(Response.Status.NOT_FOUND).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/replicaSetHardware")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getReplicaSetHardware.GET")
  public Response getReplicaSetHardware(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws Exception {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }
    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName);
    if (clusterDescription.isPresent()) {
      return Response.ok(
              _ndsAdminSearchSvc.getReplicasSetHardwareWithHealthTimes(pGroup, pClusterName, LOG))
          .build();
    }
    return Response.ok(
            _ndsAdminSearchSvc.getReplicasSetHardwareForDeletedCluster(
                pGroup.getId(), pClusterName))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{planId}/planExtraInformation")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPlanExtraInformation.GET")
  public Response getPlanExtraInformation(
      @Context final Group pGroup, @PathParam("planId") final ObjectId pPlanId) {
    return Response.ok(
            new PlanExtraInformationView(
                pPlanId,
                _ndsAdminSearchSvc.getShardsDraining(pGroup, pPlanId).stream()
                    .map(String.class::cast)
                    .collect(Collectors.toList()),
                _ndsAdminSearchSvc.getPlanIsClusterOutageSimulationActive(pPlanId)))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/hostCluster")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getHostCluster.GET")
  public Response getHostCluster(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws Exception {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }
    final Optional<HostCluster> hostCluster =
        _clusterSvc.findActiveHostClusterByGroupIdAndAtlasClusterName(pGroup.getId(), pClusterName);
    if (hostCluster.isPresent()) {
      _hostSvc.loadHostClusterHosts(hostCluster.get());
      List<HostView> hostViews = null;
      if (hostCluster.get().hasHosts()) {
        final Map<String, List<InstanceHardware>> instanceHardwareByHostname =
            _deploymentSvc.getInstanceHardwareByHostname(pGroup, hostCluster.get().getHosts());

        hostViews =
            hostCluster.get().getHosts().stream()
                .map(
                    host -> {
                      final List<InstanceHostname> allHostnamesForHost =
                          _deploymentSvc.getAllHostnamesForHost(host, instanceHardwareByHostname);
                      return new HostViewBuilder(
                              host, _deploymentSvc.makeInstanceHostnameViews(allHostnamesForHost))
                          .build();
                    })
                .collect(Collectors.toList());
      }
      return Response.ok(new HostClusterView(hostCluster.get(), hostViews)).build();
    } else {
      return Response.status(Response.Status.OK).build();
    }
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/osTunedFileOverrides")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setOsTunedFileOverrides.POST")
  public Response setOsTunedFileOverrides(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final OsTunedFileOverridesView pOsTunedFileOverrides)
      throws Exception {
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroupId, pClusterName);
    final Optional<ClusterDescription> clusterOpt =
        _clusterDescriptionDao.findByName(pGroup.getId(), pClusterName);
    if (clusterOpt.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    final OsTunedFileOverrides oldOverrides = clusterOpt.get().getOsTunedFileOverrides();
    final ItemDiff osTunedFileOverridesDiff =
        pOsTunedFileOverrides.getUpdatedOsTunedFileOverridesDiff(pClusterName, oldOverrides);

    _cloudChefConfSvc.setOsTunedFileOverrides(
        pGroup, pClusterName, pOsTunedFileOverrides.toOsTunedFileOverrides());

    final Builder builder = new Builder(NDSAudit.Type.OS_TUNED_FILE_OVERRIDES);
    if (clusterOpt.isPresent()) {
      builder.clusterId(clusterOpt.get().getUniqueId());
    }
    builder.clusterName(pClusterName);
    builder.groupId(pGroup.getId());
    builder.auditInfo(pAuditInfo);
    builder.hidden(true);
    builder.reason(pOsTunedFileOverrides.getReason());
    builder.osTunedFileOverridesDiff(osTunedFileOverridesDiff);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/{nodeTypeFamily}/autoScalingContext")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.patchReactiveAutoScalingComputeContext.PATCH")
  public Response patchReactiveAutoScalingComputeContext(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("nodeTypeFamily") final String pNodeTypeFamily,
      final UpdateReactiveComputeAutoScalingView pUpdateReactiveComputeAutoScalingView)
      throws SvcException {

    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName);

    if (clusterDescription.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    final NodeTypeFamily nodeTypeToUpdate;
    try {
      nodeTypeToUpdate = NodeTypeFamily.valueOf(pNodeTypeFamily.toUpperCase());
    } catch (IllegalArgumentException e) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          String.format(
              "Invalid URL param for node type. Valid values are: BASE or ANALYTICS. %s provided",
              pNodeTypeFamily));
    }
    final AutoScaling autoScalingToUpdate =
        clusterDescription.get().getAutoScaling(nodeTypeToUpdate);

    final Optional<AutoScalingContext> context =
        _ndsAdminSearchSvc.getAutoScalingContext(pGroup, pClusterName);
    if (context.isEmpty()) {
      throw new IllegalStateException(
          String.format(
              "Auto-scaling is enabled but unable to find autoScalingContext for: groupId=%s,"
                  + " name=%s",
              pGroup.getId(), pClusterName));
    }

    // if downscaling is enabled, downscaling criteria can not be empty
    if (pUpdateReactiveComputeAutoScalingView.getDownScalingCriteria().isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "downScalingCriteria is required.");
    }

    if (pUpdateReactiveComputeAutoScalingView.getUpScalingCriteria().isEmpty()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "upScalingCriteria is required.");
    }

    final ComputeAutoScalingContext computeAutoScalingContextToUpdate =
        (nodeTypeToUpdate.equals(NodeTypeFamily.BASE)
            ? context.get().getBaseComputeContext()
            : context.get().getAnalyticsComputeContext());

    // we only support overwrite of both up and down scaling criteria
    final ComputeAutoScalingContext updatedComputeContext =
        computeAutoScalingContextToUpdate.toBuilder()
            .upScalingCriterias(
                pUpdateReactiveComputeAutoScalingView.getUpScalingCriteria().stream()
                    .map(UiViewConverter::toSingleScalingCriterionModel)
                    .collect(Collectors.toList()))
            .downScalingCriterias(
                (pUpdateReactiveComputeAutoScalingView.getDownScalingCriteria())
                    .stream()
                        .map(UiViewConverter::toSingleScalingCriterionModel)
                        .collect(Collectors.toList()))
            .build();

    _ndsReactiveAutoScaleSvc.updateTieredAutoScalingCriteria(
        pGroup.getId(), pClusterName, nodeTypeToUpdate, updatedComputeContext);

    // TODO[CLOUDP-231502]: add audit event
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/autoScalingContext")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAutoScalingContext.GET")
  public Response getAutoScalingContext(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException, JsonProcessingException {

    final AutoScalingContext autoScalingContext =
        _ndsAdminSearchSvc
            .getAutoScalingContext(pGroup, pClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND, "Autoscaling context"));

    final Map<String, Object> autoScaleContextAsMap =
        new ObjectMapper()
            .readValue(_autoScalingContextDao.toJson(autoScalingContext), new TypeReference<>() {});

    return Response.ok(autoScaleContextAsMap).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/autoScalingContext/metrics")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAutoScalingMetrics.GET")
  public Response getAutoScalingMetrics(
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("endDate") @Nullable final Long pEndTimestamp,
      @QueryParam("instanceSize") @Nullable final String pInstanceSize)
      throws SvcException {

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND, "Cluster"));

    if (pInstanceSize != null
        && !cluster.getClusterDescription().getCloudProviders().stream()
            .anyMatch(cp -> ModelValidationUtils.isValidInstanceSizeName(pInstanceSize, cp))) {
      throw new SvcException(NDSErrorCode.INVALID_INSTANCE_SIZE);
    }

    final AutoScalingContext autoScalingContext =
        _ndsAdminSearchSvc
            .getAutoScalingContext(pGroup, pClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND, "Autoscaling context"));

    final Instant windowEnd =
        (pEndTimestamp == null) ? Instant.now() : new Date(pEndTimestamp).toInstant();

    if (autoScalingContext.getBaseComputeContext() instanceof ClusterComputeAutoScalingContext
        && autoScalingContext.getAnalyticsComputeContext()
            instanceof ClusterComputeAutoScalingContext) {
      return Response.ok(
              getClusterCriterionAndMetricViews(
                  autoScalingContext, pGroup, cluster, windowEnd, pInstanceSize))
          .build();
    } else if (autoScalingContext.getBaseComputeContext() instanceof ShardComputeAutoScalingContext
        && autoScalingContext.getAnalyticsComputeContext()
            instanceof ShardComputeAutoScalingContext) {
      return Response.ok(
              getShardCriterionAndMetricViews(
                  autoScalingContext, pGroup, cluster, windowEnd, pInstanceSize))
          .build();
    } else {
      throw new IllegalStateException();
    }
  }

  private BasicDBObject getClusterCriterionAndMetricViews(
      final AutoScalingContext pAutoScalingContext,
      final Group pGroup,
      final Cluster pCluster,
      final Instant pWindowEnd,
      @Nullable final String pInstanceSize) {

    final AutoScaleInstanceSize autoScaleInstanceSize =
        (pInstanceSize == null)
            ? pCluster.getClusterDescription().getAutoScaleInstanceSize(NodeTypeFamily.BASE)
            : getAutoScaleInstanceSizeFromMatchingInstance(pCluster, pInstanceSize);

    final InstanceSizeAutoScaleBounds instanceSizeBounds =
        getClusterInstanceSizeBounds(pCluster, NodeTypeFamily.BASE);
    final AutoScaleInstanceSize lowerAutoScaleInstanceSize =
        autoScaleInstanceSize
            .getPreviousActiveInstanceSize(
                instanceSizeBounds.getMinInstanceSize().isPresent()
                    ? Set.of(instanceSizeBounds.getMinInstanceSize().get())
                    : Set.of(),
                pCluster.getClusterDescription().getCloudProviders())
            .orElse(null);

    final ComputeMetricsContext computeMetricsContext =
        new ComputeMetricsContext(
            pGroup,
            pCluster,
            _ndsReactiveAutoScaleSvc.getHostClustersForCluster(pCluster),
            NodeTypeFamily.BASE,
            ((ClusterComputeAutoScalingContext) pAutoScalingContext.getBaseComputeContext())
                .getLastScaleUpInitiated(),
            ((ClusterComputeAutoScalingContext) pAutoScalingContext.getBaseComputeContext())
                .getLastScaleDownInitiated(),
            pWindowEnd,
            autoScaleInstanceSize,
            lowerAutoScaleInstanceSize);

    final List<CriterionAndMetricView> upscalingCriterionAndMetricViews =
        _computeClusterMetricsCalculatorSvc
            .getMetricsForCriteriaWithAllHosts(
                Optional.empty(),
                pAutoScalingContext.getBaseComputeContext().getUpScalingCriterias(),
                computeMetricsContext,
                ScaleDirection.UP)
            .entrySet()
            .stream()
            .map(NDSAdminResource::entryToCriterionAndMetricView)
            .toList();

    final List<CriterionAndMetricView> downScalingCriterionAndMetricViews;
    if (lowerAutoScaleInstanceSize == null) {
      downScalingCriterionAndMetricViews = Collections.emptyList();
    } else {
      downScalingCriterionAndMetricViews =
          _computeClusterMetricsCalculatorSvc
              .getMetricsForCriteriaWithAllHosts(
                  Optional.empty(),
                  pAutoScalingContext.getBaseComputeContext().getDownScalingCriterias(),
                  computeMetricsContext,
                  ScaleDirection.DOWN)
              .entrySet()
              .stream()
              .map(NDSAdminResource::entryToCriterionAndMetricView)
              .toList();
    }

    final BasicDBObject basicDBObject = new BasicDBObject();
    basicDBObject.put("upscalingCriteriaAndCalculatedMetrics", upscalingCriterionAndMetricViews);
    basicDBObject.put(
        "downscalingCriteriaAndCalculatedMetrics", downScalingCriterionAndMetricViews);

    return basicDBObject;
  }

  private BasicDBObject getShardCriterionAndMetricViews(
      final AutoScalingContext pAutoScalingContext,
      final Group pGroup,
      final Cluster pCluster,
      final Instant pWindowEnd,
      @Nullable final String pInstanceSize) {
    final InstanceSizeAutoScaleBounds instanceSizeBounds =
        getShardInstanceSizeBounds(pCluster, NodeTypeFamily.BASE);
    final BasicDBObject upscalingCriterionAndMetricViewsDBObject = new BasicDBObject();
    final BasicDBObject downscalingCriterionAndMetricViewsDBObject = new BasicDBObject();

    for (ReplicaSetHardware replicaSetHardware : pCluster.getReplicaSets()) {
      final AutoScaleInstanceSize autoScaleInstanceSize =
          pInstanceSize == null
              ? AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(
                      pCluster.getClusterDescription().getCloudProviders())
                  .getAutoScaleInstanceSize(
                      pCluster
                          .getClusterDescription()
                          .getReplicationSpecById(replicaSetHardware.getReplicationSpecId())
                          .orElseThrow()
                          .getElectableInstanceSize())
              : getAutoScaleInstanceSizeFromMatchingInstance(pCluster, pInstanceSize);
      final AutoScaleInstanceSize lowerAutoScaleInstanceSize =
          autoScaleInstanceSize
              .getPreviousActiveInstanceSize(
                  instanceSizeBounds.getMinInstanceSize().isPresent()
                      ? Set.of(instanceSizeBounds.getMinInstanceSize().get())
                      : Set.of(),
                  pCluster.getClusterDescription().getCloudProviders())
              .orElse(null);

      final ComputeMetricsContext computeMetricsContext =
          new ComputeMetricsContext(
              pGroup,
              pCluster,
              _ndsReactiveAutoScaleSvc.getHostClustersForCluster(pCluster),
              NodeTypeFamily.BASE,
              ((ShardComputeAutoScalingContext) pAutoScalingContext.getBaseComputeContext())
                  .getShardContextFromRsId(replicaSetHardware.getRsId())
                  .orElseThrow()
                  .getLastScaleUpInitiated(),
              ((ShardComputeAutoScalingContext) pAutoScalingContext.getBaseComputeContext())
                  .getShardContextFromRsId(replicaSetHardware.getRsId())
                  .orElseThrow()
                  .getLastScaleDownInitiated(),
              pWindowEnd,
              autoScaleInstanceSize,
              lowerAutoScaleInstanceSize);

      final List<CriterionAndMetricView> upscalingCriterionAndMetricViews =
          _computeClusterMetricsCalculatorSvc
              .getMetricsForCriteriaWithAllHosts(
                  Optional.of(replicaSetHardware.getRsId()),
                  pAutoScalingContext.getBaseComputeContext().getUpScalingCriterias(),
                  computeMetricsContext,
                  ScaleDirection.UP)
              .entrySet()
              .stream()
              .map(NDSAdminResource::entryToCriterionAndMetricView)
              .toList();

      final List<CriterionAndMetricView> downScalingCriterionAndMetricViews;
      if (lowerAutoScaleInstanceSize == null) {
        downScalingCriterionAndMetricViews = Collections.emptyList();
      } else {
        downScalingCriterionAndMetricViews =
            _computeClusterMetricsCalculatorSvc
                .getMetricsForCriteriaWithAllHosts(
                    Optional.of(replicaSetHardware.getRsId()),
                    pAutoScalingContext.getBaseComputeContext().getDownScalingCriterias(),
                    computeMetricsContext,
                    ScaleDirection.DOWN)
                .entrySet()
                .stream()
                .map(NDSAdminResource::entryToCriterionAndMetricView)
                .toList();
      }

      upscalingCriterionAndMetricViewsDBObject.put(
          replicaSetHardware.getRsId(), upscalingCriterionAndMetricViews);
      downscalingCriterionAndMetricViewsDBObject.put(
          replicaSetHardware.getRsId(), downScalingCriterionAndMetricViews);
    }

    final BasicDBObject basicDBObject = new BasicDBObject();
    basicDBObject.put(
        "upscalingCriteriaAndCalculatedMetrics", upscalingCriterionAndMetricViewsDBObject);
    basicDBObject.put(
        "downscalingCriteriaAndCalculatedMetrics", downscalingCriterionAndMetricViewsDBObject);
    return basicDBObject;
  }

  private AutoScaleInstanceSize getAutoScaleInstanceSizeFromMatchingInstance(
      final Cluster pCluster, final String pInstanceSize) {
    final Optional<? extends NDSInstanceSize> matchingInstanceSize =
        pCluster.getClusterDescription().getCloudProviders().stream()
            .map(
                cloudProvider ->
                    CloudProviderRegistry.getByCloudProvider(cloudProvider)
                        .getInstanceHardwareProvider()
                        .findInstanceSizeByName(pInstanceSize))
            .filter(Objects::nonNull)
            .findFirst()
            .orElseThrow();
    return AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(
            pCluster.getClusterDescription().getCloudProviders())
        .getAutoScaleInstanceSize(matchingInstanceSize.get());
  }

  @GET
  @Path("/openAvailabilityZoneIssues")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllOpenAvailabilityZoneIssues.GET")
  public Response getAllOpenAvailabilityZoneIssues() {
    final List<AvailabilityZoneIssue> openIssues = _availabilityZoneIssueSvc.findAllOpenIssues();
    return Response.ok(
            openIssues.stream().map(AvailabilityZoneIssueView::new).collect(Collectors.toList()))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusters/{clusterName}/forcePlanningReplicaSets")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getReplicaSetsForForcePlanning.GET")
  public Response getReplicaSetsForForcePlanning(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName);

    if (clusterDescription.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    return Response.ok(
            _ndsUISvc.getForcePlanningReplicaSetViews(pGroup.getId(), clusterDescription.get()))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusters/{clusterName}/queuedActions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getQueuedActions.GET")
  public Response getQueuedActions(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    final Optional<NDSGroup> ndsGroupOpt = _ndsGroupSvc.find(pGroupId);
    if (ndsGroupOpt.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_GROUP_ID);
    }

    final ClusterDescription pClusterDescription =
        _clusterSvc
            .getActiveClusterDescription(pGroupId, pClusterName)
            .orElseThrow(
                () -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, pClusterName, pGroupId));

    return Response.ok(
            _ndsUISvc.getQueuedActionViewsForCluster(pClusterDescription, ndsGroupOpt.get()))
        .build();
  }

  @POST
  @Path("/groups/{groupId}/clusters/{clusterName}/forcePlanningReplicaSets")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setHostsForForcePlanning.POST")
  public Response setHostsForForcePlanning(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      final List<ForcePlanningHostView> pViews)
      throws SvcException {
    final List<String> hostnames = new ArrayList<>();
    for (final ForcePlanningHostView view : pViews) {
      final String hostname = view.getHostName();
      hostnames.add(hostname);

      final ReplicaSetHardware rsh =
          _replicaSetHardwareSvc
              .getReplicaSetHardwareForHostname(hostname, pGroup.getId())
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.NOT_FOUND,
                          String.format(
                              "ReplicaSetHardware not found for group: %s, clusterName: %s,"
                                  + " hostname: %s",
                              pGroup.getId(), pClusterName, hostname)));
      if (!rsh.getGroupId().equals(pGroup.getId()) || !rsh.getClusterName().equals(pClusterName)) {
        throw new SvcException(
            NDSErrorCode.INVALID_HOST_NAME,
            String.format(
                "Hostname provided: %s does not match group: %s or clusterName: %s",
                hostname, pGroup.getId(), pClusterName));
      }

      final InstanceHardware ih =
          rsh.getByHostname(view.getHostName())
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.NOT_FOUND,
                          String.format(
                              "InstanceHardware not found for group: %s, clusterName: %s, hostname:"
                                  + " %s",
                              pGroup.getId(), pClusterName, hostname)));
      _replicaSetHardwareSvc.setForcePrimaryForHost(rsh, ih.getInstanceId(), true);
    }
    _ndsGroupDao.setPlanASAP(pGroup.getId());

    final Builder builder = new Builder(NDSAudit.Type.CLUSTER_FORCE_PLANNED);
    builder.clusterId(getClusterId(pGroup.getId(), pClusterName));
    builder.clusterName(pClusterName);
    builder.groupId(pGroup.getId());
    builder.auditInfo(pAuditInfo);
    builder.hidden(true);
    builder.hostname(String.join(",", hostnames));
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/logs/{hostname}/{logName}/info")
  @Produces({"application/json"})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN)
  @RequireCustomerGrantForEmployeeAccess()
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLogInfoForHost.GET")
  public Response getLogInfoForHost(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("hostname") final String pHostname,
      @PathParam("logName")
          @ValuesExemptFromAccessGrantRequirement({
            "automation" /* SupportedLogs.AUTOMATION */,
            "messages" /* SupportedLogs.MESSAGES */,
            "secure" /* SupportedLogs.SECURE */,
            "chef-client" /* SupportedLogs.CHEF_CLIENT */,
            "ftdc" /* SupportedLogs.FTDC */,
          })
          @ValuesAvailableWithGrantedLogAccess({
            "mongodb", /* SupportedLogs.MONGODB */ "mongos", /* SupportedLogs.MONGOS */
            "mongot", /* SupportedLogs.MONGOT */
          })
          final String pLogName,
      @QueryParam("startDate") final Long pStartTimestampSeconds,
      @QueryParam("endDate") final Long pEndTimestampSeconds,
      @QueryParam("last") @DefaultValue("0") final String pLast)
      throws Exception {
    try {
      final Pair<Long, Long> range =
          getLogTimeRange(pLast, pStartTimestampSeconds, pEndTimestampSeconds);
      return _ndsAdminSvc.fetchLogInfoForHostAsAdmin(
          pAuditInfo, pGroup, pUser, pHostname, pLogName, range.getLeft(), range.getRight());
    } catch (final DateTimeParseException e) {
      return Response.serverError().build();
    }
  }

  private static Pair<Long, Long> getLogTimeRange(
      final String pLast, final Long pStartTimestampSeconds, final Long pEndTimestampSeconds)
      throws SvcException {
    final Long now = new Date().getTime();
    final Long startDate;
    final Long endDate;

    if (pLast.equals("0")) {
      final Optional<Long> endMs = ofNullable(pEndTimestampSeconds).map(s -> s * 1000);
      final Optional<Long> startMs = ofNullable(pStartTimestampSeconds).map(s -> s * 1000);
      // use end date if specified, else use 24 hours after start date, else use current date
      endDate = endMs.orElseGet(() -> startMs.map(s -> s + DEFAULT_LOG_TIME_RANGE_MS).orElse(now));
      // use start date if specified, else use 24 hours before calculated end date
      startDate = startMs.orElseGet(() -> endDate - DEFAULT_LOG_TIME_RANGE_MS);
    } else {
      startDate = now - Duration.parse(pLast).toMillis();
      endDate = now;
    }

    if (endDate < startDate) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid time stamps");
    }

    return Pair.of(startDate, endDate);
  }

  /**
   * This endpoint must be accessed with GLOBAL_MONITORING_ADMIN roles, UNLESS accessing FTDC logs.
   * In the FTDC case, only GLOBAL_READ_ONLY is required
   *
   * <p>So: we check for less restrictive criteria using @UiCall, and then do an additional check to
   * ensure that the more restrictive roles are present if the log isn't FTDC.
   */
  @GET
  @Path("/groups/{groupId}/logs/{hostname}/{logName}.gz")
  @Produces({MediaType.APPLICATION_JSON, "application/gzip"})
  @UiCall(roles = {RoleSet.GLOBAL_READ_ONLY, RoleSet.GLOBAL_MONITORING_ADMIN})
  @RequireCustomerGrantForEmployeeAccess()
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLogsForHost.GET")
  public Response getLogsForHost(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("hostname") final String pHostname,
      @PathParam("logName")
          @ValuesExemptFromAccessGrantRequirement({
            "automation" /* SupportedLogs.AUTOMATION */,
            "messages" /* SupportedLogs.MESSAGES */,
            "secure" /* SupportedLogs.SECURE */,
            "chef-client" /* SupportedLogs.CHEF_CLIENT */,
            "ftdc" /* SupportedLogs.FTDC */,
          })
          @ValuesAvailableWithGrantedLogAccess({
            "mongodb", /* SupportedLogs.MONGODB */ "mongos", /* SupportedLogs.MONGOS */
            "mongot", /* SupportedLogs.MONGOT */ "mongotune", /* SupportedLogs.MONGOTUNE */
          })
          final String pLogName,
      @QueryParam("startDate") final Long pStartTimestampSeconds,
      @QueryParam("endDate") final Long pEndTimestampSeconds,
      @QueryParam("last") @DefaultValue("0") final String pLast,
      @QueryParam("reason") final String pReason,
      @QueryParam("skipFetchingLogsForTesting") final boolean pSkipFetchingLogsForTesting)
      throws Exception {
    // If not FTDC, validate that the more restrictive role is present.
    final boolean ftdcPresent = pLogName.equals(SupportedLogs.FTDC.getType());
    if (!ftdcPresent && !_authzSvc.isGlobalMonitoringAdmin(pUser)) {
      throw ApiErrorCode.USER_UNAUTHORIZED.exception(false);
    }

    try {
      final Pair<Long, Long> range =
          getLogTimeRange(pLast, pStartTimestampSeconds, pEndTimestampSeconds);
      return _ndsAdminSvc.fetchLogsForHostAsAdmin(
          pAuditInfo,
          pGroup,
          pUser,
          pHostname,
          pLogName,
          range.getLeft(),
          range.getRight(),
          pReason,
          pSkipFetchingLogsForTesting);
    } catch (final DateTimeParseException e) {
      return Response.serverError().build();
    }
  }

  @GET
  @Path("/groups/{groupId}/ssh-session-recordings/{hostname}/{requestId}/{sessionId}")
  @Produces({"application/json"})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getSessionsRecordingURLsForSSHRequest.GET")
  public Response getSessionsRecordingURLsForSSHRequest(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("hostname") final String pHostname,
      @PathParam("requestId") final String pRequestId,
      @PathParam("sessionId") final String pSessionId,
      @QueryParam("continuationToken") final String pContinuationToken)
      throws Exception {
    final NDSGroup ndsGroup =
        _ndsGroupDao
            .find(pGroup.getId())
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID, pGroup.getId()));
    final SessionRecordingsInfo info =
        _ndsServerAccessSvc.getSessionRecordingsURLs(
            pRequest.getRemoteAddr(),
            ndsGroup,
            pHostname,
            pRequestId,
            pSessionId,
            pContinuationToken);
    final JSONObject response = new JSONObject().put("urls", info.urls());
    ofNullable(info.continuationToken())
        .ifPresent(continuationToken -> response.put("continuationToken", continuationToken));
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/groups/{groupId}/ssh-session-recordings/{hostname}/{sshRequestId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN)
  @Auth(endpointAction = "epa.global.NDSAdminResource.listSSHRequestSessions.GET")
  public Response listSSHRequestSessions(
      @Context final Group pGroup,
      @PathParam("hostname") final String pHostname,
      @PathParam("sshRequestId") String pSshRequestId)
      throws SvcException {
    final NDSGroup ndsGroup =
        _ndsGroupDao
            .find(pGroup.getId())
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID, pGroup.getId()));
    final List<String> sessionIds =
        _ndsServerAccessSvc.listSSHSessionIds(ndsGroup, pHostname, pSshRequestId);
    return Response.ok(sessionIds).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/version")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateFixedMongoDBVersion.PATCH")
  public Response updateFixedMongoDBVersion(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    return updateClusterFixedVersion(
        pGroup.getId(),
        pClusterName,
        FixedVersionType.MONGODB,
        pReason,
        pExpirationDate,
        pUser,
        pAuditInfo);
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/acmeProviders")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fixAcmeProvider.POST")
  public Response fixAcmeProvider(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("provider") final String pAcmeProvider,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    if (ACMEProvider.find(pAcmeProvider).isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "Invalid provider name: " + pAcmeProvider);
    }

    final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);
    final ACMEProvider provider =
        pAcmeProvider.equals(ACMEProvider.GTS.name())
            ? ACMEProvider.GTS
            : ACMEProvider.LETS_ENCRYPT_V2;
    _ndsUISvc.fixACMEProvider(
        pGroup.getId(), pClusterName, provider, pAuditInfo, pReason, expirationDate);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/acmeProviders")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unfixAcmeProvider.DELETE")
  public Response unfixAcmeProvider(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    _ndsUISvc.unfixClusterACMEProvider(pGroup.getId(), pClusterName, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/acmeProviders")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateAcmeProvider.PATCH")
  public Response updateAcmeProvider(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    return updateClusterFixedVersion(
        pGroup.getId(),
        pClusterName,
        FixedVersionType.ACME_PROVIDER,
        pReason,
        pExpirationDate,
        pUser,
        pAuditInfo);
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/version")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fixMongoDBVersion.POST")
  public Response fixMongoDBVersion(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @QueryParam("validateProcessFcv") final boolean pValidateProcessFcv,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("version") final String pVersion,
      @FormParam("reason") final String pReason,
      @FormParam("factorInVersionReleaseSystem") final Boolean pFactorInVersionReleaseSystem,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    validateMongoDBVersion(pVersion);
    validateReason(pReason);
    final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);

    final ClusterDescription clusterDescription =
        _clusterSvc
            .getMergedClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(
                () ->
                    new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, pClusterName, pGroup.getId()));

    final boolean fixedVersionSupportsAllOs =
        clusterDescription.getAllRegionConfigs().stream()
            .map(RegionConfig::getHardwareSpecs)
            .flatMap(List::stream)
            .map(HardwareSpec::getOS)
            .distinct()
            .allMatch(os -> OsUtil.versionSupportsOs(new Version(pVersion.strip()), os));

    if (!fixedVersionSupportsAllOs) {
      throw new SvcException(NDSErrorCode.FAILED_TO_FIX_MDB_VERSION_OS);
    }

    if (pValidateProcessFcv) {
      final AutomationConfig automationConfig =
          _automationConfigPublishingSvc.findCurrent(
              clusterDescription.getGroupId(), pUser.getId());

      _clusterValidationUtil.validateVersionUpgradeCompatibleWithProcessFcv(
          automationConfig, clusterDescription, VersionUtils.parse(pVersion).getMajorVersion());
    }

    _ndsUISvc.fixClusterVersion(
        pGroup.getId(),
        pClusterName,
        pVersion,
        pReason,
        pAuditInfo,
        ofNullable(pFactorInVersionReleaseSystem).orElse(true),
        expirationDate);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/version")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unfixMongoDBVersion.DELETE")
  public Response unfixMongoDBVersion(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    _ndsUISvc.unfixClusterVersion(pGroup.getId(), pClusterName, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  private Response updateClusterFixedVersion(
      final ObjectId pGroupId,
      final String pClusterName,
      final FixedVersionType pType,
      final String pReason,
      final long pExpirationDate,
      final AppUser pUser,
      final AuditInfo pAuditInfo)
      throws SvcException {

    final FixedVersion fixedVersion =
        _clusterSvc
            .getActiveClusterDescription(pGroupId, pClusterName)
            .orElseThrow(
                () -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, pClusterName, pGroupId))
            .getFixedVersionByType(pType)
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.BAD_REQUEST,
                        String.format("%s not set", pType.getFieldName())));

    final FixedVersion.Builder builder = fixedVersion.toBuilder();
    FixedVersion updatedFixedVersion =
        getUpdatedFixedVersion(builder, pUser, pReason, pExpirationDate);

    if (!fixedVersion.equals(updatedFixedVersion)) {
      _clusterSvc.updateFixedVersion(
          pGroupId,
          pClusterName,
          pType,
          updatedFixedVersion,
          fixedVersion.getUpdatesList(updatedFixedVersion),
          pAuditInfo);
    }

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  private FixedVersion getUpdatedFixedVersion(
      final FixedVersion.Builder builder,
      final AppUser pUser,
      final String pReason,
      final long pExpirationDate)
      throws SvcException {
    // pExpirationDate == 0 means expiration date is not being updated
    if (pExpirationDate != 0L) {
      final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);
      builder.expirationDate(expirationDate);
    }

    if (!Strings.isNullOrEmpty(pReason)) {
      validateReason(pReason);
      builder.reason(pReason);
    }

    return builder.build();
  }

  @GET
  @Path(
      "/groups/{groupId}/clusterDescriptions/{clusterName}/getDowngradableFeatureCompatibilityVersion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDowngradableFeatureCompatibilityVersion.GET")
  public Response getDowngradableFeatureCompatibilityVersion(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    if (!isFeatureFlagEnabled(
        FeatureFlag.ATLAS_FCV_DOWNGRADE_ADMIN_ACTION, _appSettings, null, pGroup)) {
      return Response.status(Response.Status.METHOD_NOT_ALLOWED).build();
    }

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    final String currentVersion =
        _clusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(
                () ->
                    new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, pClusterName, pGroup.getId()))
            .getMongoDBVersion()
            .getMajorVersionString();

    final String downgradableVersion =
        ofNullable(FeatureCompatibilityVersion.fromString(currentVersion))
            .map(FeatureCompatibilityVersion::getDowngradableFCV)
            .map(FeatureCompatibilityVersion::getParameterValue)
            .orElseThrow(
                () -> new SvcException(NDSErrorCode.CURRENT_FCV_CANNOT_DOWNGRADE, currentVersion));

    final BasicDBObject response =
        new BasicDBObject("currentVersion", currentVersion)
            .append("downgradableVersion", downgradableVersion);
    return Response.ok(response).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/featureCompatibilityVersion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateFixedFeatureCompatibilityVersion.PATCH")
  public Response updateFixedFeatureCompatibilityVersion(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    return updateClusterFixedVersion(
        pGroup.getId(),
        pClusterName,
        FixedVersionType.FEATURE_COMPATIBILITY,
        pReason,
        pExpirationDate,
        pUser,
        pAuditInfo);
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/featureCompatibilityVersion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fixFeatureCompatibilityVersion.POST")
  public Response fixFeatureCompatibilityVersion(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("featureCompatibilityVersion") final String pFCV,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    validateFeatureCompatibilityVersion(pFCV);
    validateReason(pReason);
    final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);
    final ObjectId pGroupId = pGroup.getId();
    _clusterSvc.fixClusterFeatureCompatibilityVersion(
        pGroupId,
        pClusterName,
        pFCV,
        pReason,
        pAuditInfo,
        expirationDate,
        new Date(),
        FixedBy.ADMIN);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/featureCompatibilityVersion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unfixFeatureCompatibilityVersion.DELETE")
  public Response unfixFeatureCompatibilityVersion(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    _clusterSvc.unfixClusterFeatureCompatibilityVersion(
        pGroup.getId(), pClusterName, pAuditInfo, UnFixedBy.ADMIN);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/limits")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateTenantClusterLimit.PATCH")
  public Response updateTenantClusterLimit(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("limit") final String pLimitName,
      @FormParam("value") final int pValue)
      throws SvcException {
    _clusterSvc.updateTenantClusterLimit(pAuditInfo, pGroupId, pClusterName, pLimitName, pValue);
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/publishConfigNow")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.publishClusterAutomationConfig.POST")
  public Response publishClusterAutomationConfig(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("isRestartAllowed") final Boolean pIsRestartAllowed,
      @FormParam("runAsAdminJob") final Boolean pRunAsAdminJob)
      throws SvcException {
    if (!_authzSvc.isGlobalAtlasAdmin(pUser) && Boolean.TRUE.equals(pRunAsAdminJob)) {
      return Response.status(Response.Status.FORBIDDEN).build();
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroupId, pClusterName);

    if (pIsRestartAllowed) {
      _clusterSvc.setNeedsPublishWithRestartForCluster(pGroupId, pClusterName);
    } else {
      _clusterSvc.setNeedsPublishDateForCluster(pGroupId, pClusterName);
    }

    // If running as an admin job it should run out of band from the planner otherwise we rely on
    // the planner
    if (Boolean.TRUE.equals(pRunAsAdminJob)) {
      LOG.info(
          "Publish config now invoked with runAsAdminJob on groupId={}, clusterName={}",
          pGroupId,
          pClusterName);
      final String hostname =
          _replicaSetHardwareSvc.getReplicaSetHardware(pGroupId, pClusterName).stream()
              .findFirst()
              .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND))
              .getHighestPriorityHardware()
              .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND))
              .getHostnameForAgents()
              .get();

      _ndsAdminJobSvc.submitAdminJob(
          pGroup.getId(),
          pClusterName,
          hostname,
          PROCESS_AUTOMATION_CONFIG_PER_CLUSTER,
          pAuditInfo,
          pUser,
          null);
    } else {
      _ndsGroupDao.setPlanASAP(pGroupId);
    }

    final Builder builder = new Builder(NDSAudit.Type.CLUSTER_AUTOMATION_CONFIG_PUBLISHED);
    builder.auditInfo(pAuditInfo);
    builder.groupId(pGroupId);
    builder.clusterId(getClusterId(pGroupId, pClusterName));
    builder.clusterName(pClusterName);
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());
    return Response.ok().build();
  }

  private ObjectId getClusterId(final ObjectId projectId, final String clusterName) {
    if (projectId != null && !StringUtils.isBlank(clusterName)) {
      final Optional<ClusterDescription> clusterDescription =
          _clusterSvc.getMergedClusterDescription(projectId, clusterName);
      if (clusterDescription.isPresent()) {
        return clusterDescription.get().getUniqueId();
      }
    }
    return null;
  }

  @POST
  @Path(
      "/groups/{groupId}/clusterDescriptions/{clusterName}/setEnsureClusterConnectivityAfterForCluster")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setEnsureClusterConnectivityAfterForCluster.POST")
  public Response setEnsureClusterConnectivityAfterForCluster(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroupId, pClusterName);

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }

    _clusterSvc.setEnsureClusterConnectivityAfterForCluster(pGroupId, pClusterName);

    _ndsGroupDao.setPlanASAP(pGroupId);

    final Builder builder =
        new Builder(NDSAudit.Type.SET_ENSURE_CLUSTER_CONNECTIVITY_AFTER_FOR_CLUSTER);
    builder.auditInfo(pAuditInfo);
    builder.groupId(pGroupId);
    builder.clusterId(getClusterId(pGroupId, pClusterName));
    builder.clusterName(pClusterName);
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @POST
  @Path(
      "/groups/{groupId}/clusterDescriptions/{clusterName}/allowPrivateEndpointLegacyConnectionStrings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.allowPrivateEndpointLegacyConnectionStrings.POST")
  public Response allowPrivateEndpointLegacyConnectionStrings(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    _clusterSvc.adminForceGeneratePrivateEndpointLegacyConnectionStrings(pGroupId, pClusterName);
    return Response.ok().build();
  }

  @POST
  @Path(
      "/groups/{groupId}/clusterDescriptions/{clusterName}/removePrivateEndpointLegacyConnectionStrings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.removePrivateEndpointLegacyConnectionStrings.POST")
  public Response removePrivateEndpointLegacyConnectionStrings(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    _clusterSvc.adminRemovePrivateEndpointLegacyConnectionStrings(pGroupId, pClusterName);
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/throttlingState")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setThrottlingState.POST")
  public Response setThrottlingState(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("state") final String pState)
      throws SvcException {
    if (pState == null) {
      _ndsUISvc.setThrottlingState(pGroup.getId(), ThrottleState.INHERIT.name(), pAuditInfo);
    } else {
      _ndsUISvc.setThrottlingState(pGroup.getId(), pState, pAuditInfo);
    }
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/{clusterName}/throttlingState")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setClusterThrottlingState.POST")
  public Response setClusterThrottlingState(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("state") final String pState)
      throws SvcException {
    if (pState == null) {
      _ndsUISvc.setClusterThrottlingState(
          pGroup.getId(), pClusterName, ThrottleState.INHERIT.name(), pAuditInfo);
    } else {
      _ndsUISvc.setClusterThrottlingState(pGroup.getId(), pClusterName, pState, pAuditInfo);
    }
    return Response.ok().build();
  }

  @POST
  @AccessAudit(
      auditEventType = Type.TOGGLEABLE_FEATURE_FLAG,
      auditableRoles = RoleSet.ANY_AUTHENTICATED_USER)
  @Path("/groups/{groupId}/operatorFeatureFlags/{featureFlag}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setFeatureFlag.POST")
  public Response setFeatureFlag(
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("featureFlag") final String pFeatureFlagName,
      @FormParam("value") final boolean pEnabled)
      throws SvcException {
    final FeatureFlag flag = FeatureFlag.valueOf(pFeatureFlagName);
    if (!FeatureFlag.OPERATOR_FEATURE_FLAGS.contains(flag)) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          "The provided feature flag is not modifiable by operator");
    }

    if (pEnabled) {
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(pGroup, pOrganization, flag);
    } else {
      _featureFlagSvc.disableNonConfigServiceFeatureFlag(pGroup, pOrganization, flag);
    }

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/hosts")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateHost.POST")
  public Response updateHost(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("oplogSizeMB") final Integer pNewOplogSizeMB,
      @FormParam("hostname") final String pHostname,
      @FormParam("hostAction") final HostAction pHostAction,
      @FormParam("isQueuedAction") final boolean pIsQueuedAction,
      @FormParam("scheduledFor") final Long pScheduledFor,
      @FormParam("comment") final String pComment,
      @Context final AppUser pUser)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    if ((pHostAction == HostAction.UPDATE_CONFIG
            || pHostAction == HostAction.REVOKE_SSL
            || pHostAction == HostAction.ROTATE_AGENT_API_KEY)
        && !_authzSvc.isGlobalAtlasAdmin(pUser)) {
      return Response.status(Response.Status.FORBIDDEN).build();
    }

    validateReason(pComment);

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    final Optional<ClusterDescription> clusterDescriptionOpt =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName);

    if (pNewOplogSizeMB != null) {
      final Optional<ClusterDescriptionProcessArgsUpdatable> clusterDescriptionProcessArgs =
          _clusterSvc.getProcessArgsForUpdate(
              pGroup.getId(), pClusterName, ClusterDescriptionProcessArgs.Type.ADMIN);

      final ClusterDescriptionProcessArgsUpdatable.Builder processArgsBuilder =
          (clusterDescriptionProcessArgs.isEmpty())
              ? new ClusterDescriptionProcessArgsUpdatable.Builder(
                  pClusterName, pGroup.getId(), ClusterDescriptionProcessArgs.Type.ADMIN)
              : clusterDescriptionProcessArgs.get().copy();

      processArgsBuilder.setOplogSizeMB(pNewOplogSizeMB);

      _clusterSvc.adminUpdateProcessArgs(
          pGroup,
          pClusterName,
          clusterDescriptionOpt.map(ClusterDescription::getMongoDBVersion).orElse(null),
          new ClusterDescriptionProcessArgsAdminView(processArgsBuilder.build()),
          pAuditInfo,
          clusterDescriptionOpt);

      _ndsGroupDao.setPlanASAP(pGroup.getId());

      final Builder builder = new Builder(NDSAudit.Type.CLUSTER_OPLOG_RESIZED);
      if (clusterDescriptionOpt.isPresent()) {
        builder.clusterId(clusterDescriptionOpt.get().getUniqueId());
      }
      builder.clusterName(pClusterName);
      builder.groupId(pGroup.getId());
      builder.hostname(pHostname);
      builder.auditInfo(pAuditInfo);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());
    }

    if (pHostAction != null && pHostname != null) {
      if (!ValidationUtils.isValidHostName(pHostname)) {
        throw new SvcException(NDSErrorCode.INVALID_HOST_NAME);
      }

      if (pIsQueuedAction) {
        if (!_appSettings.isQueuedAdminActionsEnabled()) {
          throw new SvcException(
              CommonErrorCode.BAD_REQUEST, "Queued Admin Actions Feature not enabled");
        }

        final ObjectId clusterUniqueId =
            clusterDescriptionOpt
                .map(clusterDescription -> clusterDescription.getUniqueId())
                .orElse(null);

        if (pScheduledFor != null) {
          // When pScheduledFor is provided, this means that we are bypassing the
          // maintenance window for queued admin actions and schedule it for
          // the pScheduledFor time outside the maintenance window
          final Date scheduledForDate = new Date(pScheduledFor);

          _adminActionSvc.createQueuedAdminAction(
              pGroup,
              pHostname,
              pHostAction,
              clusterUniqueId,
              pClusterName,
              pComment,
              pAuditInfo,
              scheduledForDate);
          _ndsGroupDao.setPlanASAP(pGroup.getId());
        } else {
          // If a queued admin action does not have a scheduleFor parameter, then we will slot
          // the action within the group's next maintenance window
          _adminActionSvc.createQueuedAdminAction(
              pGroup, pHostname, pHostAction, clusterUniqueId, pClusterName, pComment, pAuditInfo);
        }
      } else {
        _adminActionSvc.updateHost(
            pGroup, pAuditInfo, pOrganization, pClusterName, pHostname, pHostAction, pUser);
        _ndsGroupDao.setPlanASAP(pGroup.getId());
      }
    }

    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/configServerInstanceSize")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateCSRSInstanceSize.PATCH")
  public Response updateCSRSInstanceSize(
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("instanceSize") final String pNewInstanceSize,
      @Context final AppUser pUser)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    _clusterSvc.updateCSRSInstanceSize(
        pRequest, pAuditInfo, pGroup, pClusterName, pNewInstanceSize);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/configServerDiskSizeGB")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateConfigServerDiskSizeGB.PATCH")
  public Response updateConfigServerDiskSizeGB(
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("configDiskSizeGB") final double pConfigDiskSizeGB,
      @Context final AppUser pUser)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    _clusterSvc.updateDedicatedConfigServerDiskSizeGB(
        pRequest, pAuditInfo, pGroup, pClusterName, pConfigDiskSizeGB);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/restartProcess/{processType}/hostname/{hostname}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.requestProcessRestart.POST")
  public Response requestProcessRestart(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("processType") final NDSProcessType pProcessType,
      @PathParam("hostname") final String pHostname)
      throws SvcException {
    final ClusterDescriptionId clusterDescriptionId =
        _replicaSetHardwareSvc
            .getClusterDescriptionIdForHostname(pHostname, null)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_CLUSTER));
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, clusterDescriptionId);
    if (Stream.of(NDSProcessType.MONGOS, NDSProcessType.MONGOT, NDSProcessType.ATLAS_PROXY)
        .noneMatch(p -> p.equals(pProcessType))) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "processType");
    }
    _clusterSvc.requestProcessRestart(
        pHostname, pProcessType, pAuditInfo, clusterDescriptionId.getGroupId());
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/diskSizeGB")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateDiskSizeGB.POST")
  public Response updateDiskSizeGB(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("diskSizeGB") final double pDiskSizeGB)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    final Optional<NDSGroup> ndsGroupOpt = _ndsGroupSvc.find(pGroup.getId());

    final ClusterDescription existing =
        _clusterSvc
            .getMergedClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(
                () ->
                    new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, pClusterName, pGroup.getId()));

    _ndsGroupSvc.adminOverrideDiskSizeGB(existing, pDiskSizeGB, pAuditInfo, pRequest);

    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/host/{hostname}/lastPing")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLastPingForHostname.GET")
  public Response getLastPingForHostname(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostname") final String pHostname,
      @QueryParam("port") final Integer pPort)
      throws SvcException {

    final Host host =
        pPort != null
            ? _hostSvc.findAllHostsByName(pGroupId, pHostname).stream()
                .filter(h -> h.getPort().equals(pPort))
                .findFirst()
                .orElse(null)
            : _hostSvc.findHostByName(pGroupId, pHostname);

    if (host == null) {
      final String portError = pPort != null ? String.format(" on port %d", pPort) : "";
      throw new SvcException(
          CommonErrorCode.NOT_FOUND,
          String.format("Host not found with hostname %s%s", pHostname, portError));
    }

    final BasicBSONObject ping = _hostLastPingSvc.getLastPing(host.getId());

    if (ping == null) {
      throw new SvcException(CommonErrorCode.NOT_FOUND, "Last Ping");
    }
    return Response.ok(ping).build();
  }

  @GET
  @Path("/plans/{planId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPlan.GET")
  public Response getPlan(@PathParam("planId") final ObjectId pPlanId) throws Exception {
    return Response.ok(_ndsUISvc.getPlanAsDocument(pPlanId)).build();
  }

  @POST
  @Path("/plans/{planId}/move/{moveId}/step/{stepKey}/skip")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.skipStep.POST")
  public Response skipStep(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("planId") final ObjectId pPlanId,
      @PathParam("moveId") final ObjectId pMoveId,
      @PathParam("stepKey") final String pStepKey) {
    final List<String> skippableSteps =
        List.of(WaitForDiskWarmingCompleteStep.class.getSimpleName());

    final String step = Delimiter.getStepNameFromKey(pStepKey);
    if (_authzSvc.isGlobalAtlasAdmin(pUser) || skippableSteps.contains(step)) {
      _planDao.updateStepPerformed(pPlanId, pMoveId, pStepKey);
      final Builder builder = new Builder(NDSAudit.Type.STEP_SKIPPED);
      builder.auditInfo(pAuditInfo);
      builder.hidden(true);
      builder.planId(pPlanId);
      builder.skippedStep(pStepKey);
      _auditSvc.saveAuditEvent(builder.build());
      return Response.ok(EMPTY_JSON_OBJECT).build();
    } else {
      // Restrict skip step for other types of steps due to risk
      return Response.status(Response.Status.FORBIDDEN).build();
    }
  }

  @POST
  @Path("/plans/{planId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updatePlan.POST")
  public Response updatePlan(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("planId") final ObjectId pPlanId,
      @FormParam("rollback") final boolean pRollback,
      @FormParam("skipCurrentMove") final boolean pSkipCurrentMove,
      @FormParam("skipMoveId") final String pMoveId) {
    if (pRollback) {
      final Optional<Plan> plan = _planDao.find(pPlanId);
      if (plan.isEmpty()) {
        return Response.status(Response.Status.NOT_FOUND).build();
      }
      _planDao.abandonPlan(pPlanId);
      _ndsAdminJobDao
          .findByPlanId(pPlanId)
          .ifPresent(
              adminJob ->
                  _ndsAdminJobDao.updateJobStatus(
                      adminJob.getId(),
                      NDSAdminJob.Status.FAILED,
                      String.format(
                          "Plan (%s) abandoned in the admin job (%s) for host (%s) in cluster"
                              + " (%s).",
                          adminJob.getPlanId(),
                          adminJob.getId(),
                          adminJob.getHostname(),
                          adminJob.getClusterName())));

      final Builder builder = new Builder(NDSAudit.Type.PLAN_ABANDONED);
      builder.auditInfo(pAuditInfo);
      builder.groupId(plan.get().getGroupId());
      builder.hidden(true);
      builder.planId(pPlanId);
      _auditSvc.saveAuditEvent(builder.build());
    }

    if (pSkipCurrentMove || pMoveId != null) {
      final Optional<Plan> plan = _planDao.find(pPlanId);
      if (plan.isEmpty()) {
        return Response.status(Response.Status.NOT_FOUND).build();
      }
      final List<Move> inProgressMoves =
          plan.get().getMoves().stream()
              .filter(
                  move ->
                      move.getLastStepResult().isPresent()
                          && move.getLastStepResult().get().getRight() == Status.IN_PROGRESS
                          && (pMoveId == null || pMoveId.equals(move.getId().toString())))
              .toList();

      if (inProgressMoves.size() != 1) {
        return Response.status(Response.Status.BAD_REQUEST).build();
      }

      final Move toSkip = inProgressMoves.get(0);

      // Can skip WaitForMachineHealthyMove and *SyncPauseStateMove
      final List<Class<? extends Move>> operatorSkippableMoves =
          List.of(
              WaitForMachineHealthyMove.class,
              AWSSyncPauseStateMove.class,
              AzureSyncPauseStateMove.class,
              GCPSyncPauseStateMove.class);

      final List<Class<? extends Move>> engOperatorSkippableMoves =
          Stream.concat(
                  Stream.of(ProcessAutomationConfigPerClusterMove.class),
                  operatorSkippableMoves.stream())
              .toList();

      if (_authzSvc.isGlobalAtlasAdmin(pUser)
          || (_authzSvc.isGlobalAtlasOperator(pUser)
              && operatorSkippableMoves.contains(toSkip.getClass()))
          || (_authzSvc.isGlobalAtlasEngineeringOperator(pUser)
              && engOperatorSkippableMoves.contains(toSkip.getClass()))) {
        toSkip.markAsDone();

        final Builder builder = new Builder(NDSAudit.Type.MOVE_SKIPPED);
        builder.auditInfo(pAuditInfo);
        builder.groupId(plan.get().getGroupId());
        builder.hidden(true);
        builder.planId(pPlanId);
        builder.skippedMove(toSkip.getClass().getName());
        _auditSvc.saveAuditEvent(builder.build());
      } else {
        // Restrict skip move for other types of moves due to risk
        return Response.status(Response.Status.FORBIDDEN).build();
      }
    }
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/plans/executing")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPlan.GET")
  public Response getPlan() {
    return Response.ok(_ndsUISvc.getPlansExecuting()).build();
  }

  @GET
  @Path("/groups/{groupId}/plans")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPlansForGroup.GET")
  public Response getPlansForGroup(@Context final Group pGroup) {
    return Response.ok(_ndsUISvc.getPlansForGroup(pGroup.getId())).build();
  }

  @GET
  @Path("/groups/mtmGroups")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMGroups.GET")
  public Response getMTMGroups() throws Exception {
    final List<GroupStatusView> mtmGroups = _ndsUISvc.getMTMGroupStatuses();
    return Response.ok(mtmGroups).build();
  }

  @GET
  @Path("/groups/serverless/mtmGroups")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMGroups.GET")
  public Response getServerlessMTMGroups() throws Exception {
    verifyServerlessIsSupported();
    final List<GroupStatusView> serverlessMTMGroups = _ndsUISvc.getServerlessMTMGroupStatuses();
    return Response.ok(serverlessMTMGroups).build();
  }

  @GET
  @Path("/groups/flex/mtmGroups")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFlexMTMGroups.GET")
  public Response getFlexMTMGroups() throws Exception {
    verifyFlexIsSupported();
    final List<GroupStatusView> flexMTMGroups = _ndsUISvc.getFlexMTMGroupStatuses();
    return Response.ok(flexMTMGroups).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusters/{clusterName}/availabilitySet")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.forceMigrateFromAvailabilitySet.PATCH")
  public Response forceMigrateFromAvailabilitySet(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("availabilitySetNames[]") final Set<String> pAvailabilitySetNames)
      throws SvcException {
    final ClusterDescription clusterDescription =
        _clusterSvc
            .getMergedClusterDescription(pGroupId, pClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.INVALID_PARAMETER));
    _clusterSvc.setMigrateFromAvailabilitySets(pGroupId, pClusterName, pAvailabilitySetNames);

    final ItemDiff itemDiff =
        ItemDiff.forClusterDescription(pClusterName, ItemDiff.Status.MODIFIED);
    itemDiff.addItem(
        FieldDefs.MIGRATE_FROM_AVAILABILITY_SETS,
        "Migrate From Availability Sets",
        clusterDescription.getMigrateFromAvailabilitySets().toString(),
        pAvailabilitySetNames.toString());
    final Builder builder = new Builder(NDSAudit.Type.FORCE_MIGRATE_FROM_AVAILABILITY_SETS);
    builder.auditInfo(pAuditInfo);
    builder.groupId(pGroupId);
    builder.hidden(true);
    builder.clusterDescriptionDiff(itemDiff);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.accepted("{}").build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusters/{clusterName}/cpuSocketBinding")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setClusterCpuSocketBinding.PATCH")
  public Response setClusterCpuSocketBinding(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final List<Integer> pCpuSocketBinding)
      throws SvcException {
    final ClusterDescription clusterDescription =
        _clusterSvc
            .getMergedClusterDescription(pGroupId, pClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.INVALID_PARAMETER));
    _clusterSvc.adminSetCpuSocketBinding(pGroupId, pClusterName, pCpuSocketBinding);

    final ItemDiff itemDiff =
        ItemDiff.forClusterDescription(pClusterName, ItemDiff.Status.MODIFIED);
    itemDiff.addItem(
        FieldDefs.CPU_SOCKET_BINDING,
        "Set CPU Socket Binding",
        clusterDescription.getCpuSocketBinding().toString(),
        pCpuSocketBinding.toString());
    final Builder builder = new Builder(NDSAudit.Type.CLUSTER_SET_CPU_SOCKET_BINDING);
    builder.auditInfo(pAuditInfo);
    builder.groupId(pGroupId);
    builder.hidden(true);
    builder.clusterDescriptionDiff(itemDiff);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.accepted("{}").build();
  }

  @PATCH
  @Path("/groups/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setMTMHolder.PATCH")
  public Response setMTMHolder(
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("setMTMHolder") final String pSetMTMHolder,
      @FormParam("mtmHolderType") final String pMTMHolderType)
      throws Exception {
    final MTMHolderType type =
        MTMHolderType.valueOf(
            StringUtils.isEmpty(pMTMHolderType) ? MTMHolderType.SHARED.name() : pMTMHolderType);

    if (type == MTMHolderType.SERVERLESS) {
      verifyServerlessIsSupported();
    } else if (type == MTMHolderType.FLEX) {
      verifyFlexIsSupported();
    }

    if (pSetMTMHolder.equals("true")) {
      if (type == MTMHolderType.SHARED) {
        _ndsGroupSvc.setFreeMTMHolder(pGroupId);
      } else if (type == MTMHolderType.SERVERLESS) {
        _serverlessSetupMTMBackupPolicyUtil.enableDataProtectionForMTMGroup(pGroupId);
        _ndsGroupSvc.setServerlessMTMHolder(pGroupId);
      } else if (type == MTMHolderType.FLEX) {
        _ndsGroupSvc.setFlexMTMHolder(pGroupId);
      }
    } else if (pSetMTMHolder.equals("false")) {
      if (type == MTMHolderType.SHARED) {
        _ndsGroupSvc.unsetFreeMTMHolder(pGroupId);
      } else if (type == MTMHolderType.SERVERLESS) {
        _ndsGroupSvc.unsetServerlessMTMHolder(pGroupId);
      } else if (type == MTMHolderType.FLEX) {
        _ndsGroupSvc.unsetFlexMTMHolder(pGroupId);
      }
    }

    return Response.ok().build();
  }

  @GET
  @Path("/proxy/throttling/nonAnchoredRegex/delayMillis")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getProxyNaRegexDelayMillis.GET")
  public Response getProxyNaRegexDelayMillis() {
    final int delayMillis = _appSettings.getProxyNaRegexThrottlingMillis();

    return Response.ok(delayMillis).build();
  }

  @PATCH
  @Path("/proxy/throttling/nonAnchoredRegex/delayMillis")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateProxyNaRegexDelayMillis.PATCH")
  public Response updateProxyNaRegexDelayMillis(@FormParam("delay") final Integer pDelay)
      throws Exception {
    if (pDelay < 0 || pDelay > NON_ANCHORED_THROTTLING_MAX_DELAY_MILLIS) {
      throw new SvcException(NDSErrorCode.PROXY_THROTTLING_DELAY_ILLEGAL);
    }
    _appSettings.setProxyNaRegexThrottlingMillis(pDelay);

    return Response.ok().build();
  }

  @GET
  @Path("/proxy/throttling/nonAnchoredRegex/watchedPrefixes")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getProxyNaRegexWatchedPrefixes.GET")
  public Response getProxyNaRegexWatchedPrefixes() {
    final Set<String> prefixes = _appSettings.getProxyNaRegexThrottlingPrefixes();
    final List<ProxyPrefixView> views =
        prefixes.stream().map(ProxyPrefixView::new).collect(Collectors.toList());

    return Response.ok(views).build();
  }

  @PUT
  @Path("/proxy/throttling/nonAnchoredRegex/watchedPrefix/{prefix}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addProxyNaRegexWatchedPrefix.PUT")
  public Response addProxyNaRegexWatchedPrefix(@PathParam("prefix") final String pCollectionPrefix)
      throws Exception {

    // We store the collection as a comma separated values, so reject prefixes containing the comma
    if (!ValidationUtils.isValidCollectionName(pCollectionPrefix)
        || pCollectionPrefix.contains(",")) {
      throw new SvcException(NDSErrorCode.PROXY_THROTTLING_PREFIX_INVALID, pCollectionPrefix);
    }

    final Set<String> prefixes = new HashSet<>(_appSettings.getProxyNaRegexThrottlingPrefixes());
    if (prefixes.contains(pCollectionPrefix)) {
      throw new SvcException(NDSErrorCode.PROXY_THROTTLING_PREFIX_EXISTS, pCollectionPrefix);
    }
    prefixes.add(pCollectionPrefix);
    _appSettings.setProxyNaRegexThrottlingPrefixes(prefixes);

    return Response.ok().build();
  }

  @DELETE
  @Path("/proxy/throttling/nonAnchoredRegex/watchedPrefix/{prefix}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteProxyNaRegexWatchedPrefix.DELETE")
  public Response deleteProxyNaRegexWatchedPrefix(
      @PathParam("prefix") final String pCollectionPrefix) throws Exception {
    final Set<String> prefixes = new HashSet<>(_appSettings.getProxyNaRegexThrottlingPrefixes());

    if (!prefixes.contains(pCollectionPrefix)) {
      throw new SvcException(NDSErrorCode.PROXY_THROTTLING_PREFIX_UNKNOWN, pCollectionPrefix);
    }
    prefixes.remove(pCollectionPrefix);
    _appSettings.setProxyNaRegexThrottlingPrefixes(prefixes);

    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/limits")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setCustomLimit.PATCH")
  public Response setCustomLimit(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("limit") final String pLimitName,
      @FormParam("value") final Long pValue)
      throws Exception {
    _ndsGroupSvc.setLimit(pGroupId, pLimitName, pValue, pAuditInfo, pUser);
    return Response.ok().build();
  }

  @GET
  @Path("/mtm/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMCluster.GET")
  public Response getMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    return getMTMCluster(pGroupId, pClusterName, MTMClusterType.SHARED);
  }

  @GET
  @Path("/mtm/serverless/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMCluster.GET")
  public Response getServerlessMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    verifyServerlessIsSupported();
    return getMTMCluster(pGroupId, pClusterName, MTMClusterType.SERVERLESS);
  }

  @GET
  @Path("/mtm/flex/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFlexMTMCluster.GET")
  public Response getFlexMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    verifyFlexIsSupported();
    return getMTMCluster(pGroupId, pClusterName, MTMClusterType.FLEX);
  }

  @GET
  @Path("/mtm/clusters/{groupId}/{clusterName}/topTenants")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTopTenantsForMTM.GET")
  public Response getTopTenantsForMTM(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    return getTopTenantsForMTM(pGroupId, pClusterName, MTMClusterType.SHARED);
  }

  @GET
  @Path("/mtm/serverless/clusters/{groupId}/{clusterName}/tenants")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllTenantsForServerlessMTM.GET")
  public Response getAllTenantsForServerlessMTM(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    verifyServerlessIsSupported();
    return getTopTenantsForMTM(pGroupId, pClusterName, MTMClusterType.SERVERLESS);
  }

  @GET
  @Path("/mtm/clusters/{groupId}/{clusterName}/tenants")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllTenantsForSharedMTM.GET")
  public Response getAllTenantsForSharedMTM(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    return getTopTenantsForMTM(pGroupId, pClusterName, MTMClusterType.SHARED);
  }

  @GET
  @Path("/mtm/flex/clusters/{groupId}/{clusterName}/tenants")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllTenantsForFlexMTM.GET")
  public Response getAllTenantsForFlexMTM(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    verifyFlexIsSupported();
    return getTopTenantsForMTM(pGroupId, pClusterName, MTMClusterType.FLEX);
  }

  @GET
  @Path("/serverlessFreeMigration/{groupId}/{mtmClusterName}/eligibleServerlessInstances")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessInstancesEligibleForFreeMigration.GET")
  public Response getServerlessInstancesEligibleForFreeMigration(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("mtmClusterName") final String pMTMClusterName) {
    return Response.ok()
        .entity(
            _serverlessFreeMigrationStatusSvc.getServerlessInstancesEligibleForFreeMigration(
                pGroup.getId(), pMTMClusterName))
        .build();
  }

  @POST
  @Path(
      "/serverlessFreeMigration/group/{groupId}/uniqueId/{uniqueId}/name/{instanceName}/startMigrationToFree")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.global.NDSAdminResource.startMigrationToFree.POST")
  public Response startMigrationToFree(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("uniqueId") final ObjectId pUniqueId,
      @PathParam("instanceName") final String pInstanceName)
      throws SvcException {

    if (_serverlessFreeMigrationStatusSvc.isEligibleToStartServerlessFreeMigration(
        pGroupId, pUniqueId, false)) {
      _serverlessFreeMigrationStatusSvc.startServerlessFreeMigrationPlan(
          pAuditInfo,
          _serverlessFreeMigrationStatusSvc.findByName(pGroupId, pInstanceName).orElseThrow());
      return Response.accepted().build();
    }

    _serverlessFreeMigrationStatusSvc.setStatusIneligible(pGroupId, pInstanceName, false);
    throw new SvcException(
        NDSErrorCode.SERVERLESS_INSTANCE_INELIGIBLE_FOR_MIGRATION_TO_FREE, pInstanceName, pGroupId);
  }

  @PUT
  @Path("/mtm/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setMTMCluster.PUT")
  public Response setMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("maxCapacity") final Integer pMaxCapacity,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("isolationGroupIds") final String pIsolationGroupIds)
      throws Exception {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    if (!ModelValidationUtils.isValidInstanceSizeName(pTenantInstanceSize, CloudProvider.FREE)) {
      throw new SvcException(
          NDSErrorCode.INVALID_INSTANCE_SIZE, pTenantInstanceSize, CloudProvider.FREE);
    }
    _clusterSvc.makeSharedMTMCluster(
        pGroupId,
        pClusterName,
        pMaxCapacity == null ? FreeNDSDefaults.MTM_MAX_CAPACITY : pMaxCapacity,
        pTenantInstanceSize,
        convertIsolationGroupStringToSet(pIsolationGroupIds));
    return Response.ok().build();
  }

  @GET
  @Path("/serverlessFreeMigration/eligibleMtmHolderGroups")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getEligibleServerlessMTMHolderGroups.GET")
  public Response getEligibleServerlessMTMHolderGroups() {
    return Response.ok()
        .entity(
            _serverlessFreeMigrationStatusSvc
                .findServerlessMTMHolderGroupsWithTenantsEligibleToMigrate())
        .build();
  }

  @PUT
  @Path("/mtm/serverless/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setServerlessMTMCluster.PUT")
  public Response setServerlessMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("maxCapacity") final Integer pMaxCapacity,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("isolationGroupIds") final String pIsolationGroupIds,
      @FormParam("serverlessPoolId") final String pServerlessPoolId)
      throws Exception {
    verifyServerlessIsSupported();
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    if (!ModelValidationUtils.isValidInstanceSizeName(
        pTenantInstanceSize, CloudProvider.SERVERLESS)) {
      throw new SvcException(
          NDSErrorCode.INVALID_INSTANCE_SIZE, pTenantInstanceSize, CloudProvider.SERVERLESS);
    }
    _clusterSvc.makeServerlessMTMCluster(
        pGroupId,
        pClusterName,
        pMaxCapacity == null ? ServerlessNDSDefaults.MTM_MAX_CAPACITY : pMaxCapacity,
        pTenantInstanceSize,
        convertIsolationGroupStringToSet(pIsolationGroupIds),
        convertStringToObjectId(pServerlessPoolId, NDSErrorCode.SERVERLESS_MTM_POOL_ID_INVALID));
    return Response.ok().build();
  }

  @PUT
  @Path("/mtm/flex/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setFlexMTMCluster.PUT")
  public Response setFlexMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("maxCapacity") final Integer pMaxCapacity,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("isolationGroupIds") final String pIsolationGroupIds)
      throws Exception {
    verifyFlexIsSupported();
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    if (!ModelValidationUtils.isValidInstanceSizeName(pTenantInstanceSize, CloudProvider.FLEX)) {
      throw new SvcException(
          NDSErrorCode.INVALID_INSTANCE_SIZE, pTenantInstanceSize, CloudProvider.FLEX);
    }
    _clusterSvc.makeFlexMTMCluster(
        pGroupId,
        pClusterName,
        pMaxCapacity == null ? FlexNDSDefaults.MTM_MAX_CAPACITY : pMaxCapacity,
        pTenantInstanceSize,
        convertIsolationGroupStringToSet(pIsolationGroupIds));
    return Response.ok().build();
  }

  @PUT
  @Path("/mtm/profiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addMTMProfile.PUT")
  public Response addMTMProfile(
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("backingInstanceSize") final String pBackingInstanceSize,
      @FormParam("defaultCapacity") final Integer pDefaultCapacity,
      @FormParam("lowCapacityBuffer") final Integer pLowCapacityBuffer,
      @FormParam("autoScaleMinSize") final String pAutoScaleMinSize,
      @FormParam("mongoDBMajorVersion") final String pVersion)
      throws SvcException {
    _ndsMTMProfileSvc.addSharedMTMProfile(
        pCloudProvider,
        pRegionName,
        pTenantInstanceSize,
        pBackingInstanceSize,
        pDefaultCapacity,
        pLowCapacityBuffer,
        pAutoScaleMinSize,
        pVersion);
    return Response.ok().build();
  }

  @PUT
  @Path("/mtm/serverless/profiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addServerlessMTMProfile.PUT")
  public Response addServerlessMTMProfile(
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("backingInstanceSize") final String pBackingInstanceSize,
      @FormParam("defaultCapacity") final Integer pDefaultCapacity,
      @FormParam("lowCapacityBuffer") final Integer pLowCapacityBuffer,
      @FormParam("autoScaleMinSize") final String pAutoScaleMinSize,
      @FormParam("serverlessPoolId") final String pServerlessPoolId)
      throws SvcException {
    verifyServerlessIsSupported();
    _ndsMTMProfileSvc.addServerlessMTMProfile(
        pCloudProvider,
        pRegionName,
        pTenantInstanceSize,
        pBackingInstanceSize,
        pDefaultCapacity,
        pLowCapacityBuffer,
        pAutoScaleMinSize,
        pServerlessPoolId);
    return Response.ok().build();
  }

  @PUT
  @Path("/mtm/flex/profiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addFlexMTMProfile.PUT")
  public Response addFlexMTMProfile(
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("backingInstanceSize") final String pBackingInstanceSize,
      @FormParam("defaultCapacity") final Integer pDefaultCapacity,
      @FormParam("lowCapacityBuffer") final Integer pLowCapacityBuffer,
      @FormParam("autoScaleMinSize") final String pAutoScaleMinSize,
      @FormParam("mongoDBMajorVersion") final String pVersion)
      throws SvcException {
    verifyFlexIsSupported();
    _ndsMTMProfileSvc.addFlexMTMProfile(
        pCloudProvider,
        pRegionName,
        pTenantInstanceSize,
        pBackingInstanceSize,
        pDefaultCapacity,
        pLowCapacityBuffer,
        pAutoScaleMinSize,
        pVersion);
    return Response.ok().build();
  }

  @GET
  @Path("/mtm/profiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMProfiles.GET")
  public Response getMTMProfiles() {
    return Response.ok(
            _ndsMTMProfileSvc.getSharedProfiles().stream()
                .map(NDSMTMProfile::toDBObject)
                .collect(DbUtils.toBasicDBList()))
        .build();
  }

  @GET
  @Path("/mtm/serverless/profiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMProfiles.GET")
  public Response getServerlessMTMProfiles() {
    verifyServerlessIsSupported();
    return Response.ok(
            _ndsMTMProfileSvc.getServerlessProfiles().stream()
                .map(NDSMTMProfile::toDBObject)
                .collect(DbUtils.toBasicDBList()))
        .build();
  }

  @GET
  @Path("/mtm/flex/profiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFlexMTMProfiles.GET")
  public Response getFlexMTMProfiles() {
    verifyFlexIsSupported();
    return Response.ok(
            _ndsMTMProfileSvc.getAllFlexMTMProfiles().stream()
                .map(NDSMTMProfile::toDBObject)
                .collect(DbUtils.toBasicDBList()))
        .build();
  }

  @GET
  @Path("/mtm/serverless/profiles/pool/{poolId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMProfilesByPoolId.GET")
  public Response getServerlessMTMProfilesByPoolId(@PathParam("poolId") final ObjectId pPoolId) {
    verifyServerlessIsSupported();
    return Response.ok(
            _ndsMTMProfileSvc.getServerlessMTMProfilesForPool(pPoolId).stream()
                .map(NDSMTMProfile::toDBObject)
                .collect(DbUtils.toBasicDBList()))
        .build();
  }

  @GET
  @Path("/mtm/serverless/profiles/{id}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMProfileById.GET")
  public Response getServerlessMTMProfileById(@PathParam("id") final ObjectId pId)
      throws SvcException {
    verifyServerlessIsSupported();
    return Response.ok(_ndsMTMProfileSvc.getServerlessMTMProfile(pId).toDBObject()).build();
  }

  @GET
  @Path("/mtm/flex/profiles/{id}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFlexMTMProfileById.GET")
  public Response getFlexMTMProfileById(@PathParam("id") final ObjectId pId) throws SvcException {
    verifyFlexIsSupported();
    return Response.ok(_ndsMTMProfileSvc.getFlexMTMProfileById(pId).toDBObject()).build();
  }

  @DELETE
  @Path("/mtm/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteMTMCluster.DELETE")
  public Response deleteMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    _clusterSvc.deleteSharedMTMCluster(pGroupId, pClusterName);
    return Response.ok().build();
  }

  @DELETE
  @Path("/mtm/serverless/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteServerlessMTMCluster.DELETE")
  public Response deleteServerlessMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    verifyServerlessIsSupported();
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    _clusterSvc.deleteServerlessMTMCluster(pGroupId, pClusterName);
    return Response.ok().build();
  }

  @DELETE
  @Path("/mtm/flex/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteFlexMTMCluster.DELETE")
  public Response deleteFlexMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    verifyFlexIsSupported();
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    _clusterSvc.deleteFlexMTMCluster(pGroupId, pClusterName);
    return Response.ok().build();
  }

  @PATCH
  @Path("/mtm/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateMTMCluster.PATCH")
  public Response updateMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("assignmentEnabled") final Boolean pAssignmentEnabled,
      @FormParam("maxCapacity") final Integer pMaxCapacity,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("isolationGroupIds") final String pIsolationGroupIds)
      throws Exception {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    if (!ModelValidationUtils.isValidInstanceSizeName(pTenantInstanceSize, CloudProvider.FREE)) {
      throw new SvcException(
          NDSErrorCode.INVALID_INSTANCE_SIZE, pTenantInstanceSize, CloudProvider.FREE.name());
    }
    _clusterSvc.updateSharedMTMCluster(
        pGroupId,
        pClusterName,
        pAssignmentEnabled,
        pMaxCapacity,
        pTenantInstanceSize,
        convertIsolationGroupStringToSet(pIsolationGroupIds));
    return Response.ok().build();
  }

  @PATCH
  @Path("/mtm/serverless/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateServerlessMTMCluster.PATCH")
  public Response updateServerlessMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("assignmentEnabled") final Boolean pAssignmentEnabled,
      @FormParam("maxCapacity") final Integer pMaxCapacity,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("isolationGroupIds") final String pIsolationGroupIds)
      throws Exception {
    verifyServerlessIsSupported();
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    if (!ModelValidationUtils.isValidInstanceSizeName(
        pTenantInstanceSize, CloudProvider.SERVERLESS)) {
      throw new SvcException(
          NDSErrorCode.INVALID_INSTANCE_SIZE, pTenantInstanceSize, CloudProvider.SERVERLESS.name());
    }
    _clusterSvc.updateServerlessMTMCluster(
        pGroupId,
        pClusterName,
        pAssignmentEnabled,
        pMaxCapacity,
        pTenantInstanceSize,
        convertIsolationGroupStringToSet(pIsolationGroupIds));
    return Response.ok().build();
  }

  @PATCH
  @Path("/mtm/flex/clusters/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateFlexMTMCluster.PATCH")
  public Response updateFlexMTMCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("assignmentEnabled") final Boolean pAssignmentEnabled,
      @FormParam("maxCapacity") final Integer pMaxCapacity,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("isolationGroupIds") final String pIsolationGroupIds)
      throws Exception {
    verifyFlexIsSupported();
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    if (!ModelValidationUtils.isValidInstanceSizeName(pTenantInstanceSize, CloudProvider.FLEX)) {
      throw new SvcException(
          NDSErrorCode.INVALID_INSTANCE_SIZE, pTenantInstanceSize, CloudProvider.FLEX.name());
    }
    _clusterSvc.updateFlexMTMCluster(
        pGroupId,
        pClusterName,
        pAssignmentEnabled,
        pMaxCapacity,
        pTenantInstanceSize,
        convertIsolationGroupStringToSet(pIsolationGroupIds));
    return Response.ok().build();
  }

  @GET
  @Path("/mtm/clusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMClusters.GET")
  public Response getMTMClusters() {
    final List<SharedMTMClusterView> sharedMTMClusterViews = _clusterSvc.getSharedMTMClusterViews();
    return Response.ok(sharedMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/paginatedClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPaginatedSharedMTMClusters.GET")
  public Response getPaginatedSharedMTMClusters(
      @QueryParam("searchValue") final String pSearchValue,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName,
      @QueryParam("instanceSize") final String pInstanceSize,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip)
      throws SvcException {
    final List<SharedMTMClusterView> sharedMTMClusterViews =
        _clusterSvc.getSharedMTMClusterViewsByFilter(
            getPaginatedMTMClusterQuery(pSearchValue, pCloudProvider, pRegionName, pInstanceSize),
            pLimit,
            pSkip);
    return Response.ok(sharedMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/clusters/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getSharedMTMClustersByGroup.GET")
  public Response getSharedMTMClustersByGroup(
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip)
      throws SvcException {
    final List<SharedMTMClusterView> sharedMTMClusterViews =
        _clusterSvc.getSharedMTMClusterViewsByGroup(pGroupId, pLimit, pSkip);
    return Response.ok(sharedMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/clusters/count")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.countSharedMTMClusters.GET")
  public Response countSharedMTMClusters(
      @QueryParam("searchValue") final String pSearchValue,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName,
      @QueryParam("instanceSize") final String pInstanceSize) {
    verifyServerlessIsSupported();
    final int count =
        (int)
            _clusterSvc.countSharedMTMClusters(
                getPaginatedMTMClusterQuery(
                    pSearchValue, pCloudProvider, pRegionName, pInstanceSize));
    return Response.ok(new BasicDBObject("count", count)).build();
  }

  @GET
  @Path("/mtm/clusters/{groupId}/count")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.countSharedMTMClustersByGroup.GET")
  public Response countSharedMTMClustersByGroup(@PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    verifyServerlessIsSupported();
    final int count = (int) _clusterSvc.countSharedMTMClustersByGroup(pGroupId);
    return Response.ok(new BasicDBObject("count", count)).build();
  }

  @GET
  @Path("/mtm/serverless/clusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMClusters.GET")
  public Response getServerlessMTMClusters() {
    verifyServerlessIsSupported();
    final List<ServerlessMTMClusterView> serverlessMTMClusterViews =
        _clusterSvc.getServerlessMTMClusterViews();
    return Response.ok(serverlessMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/serverless/paginatedClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPaginatedServerlessMTMClusters.GET")
  public Response getPaginatedServerlessMTMClusters(
      @QueryParam("searchValue") final String pSearchValue,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName,
      @QueryParam("instanceSize") final String pInstanceSize,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip) {
    verifyServerlessIsSupported();

    final List<ServerlessMTMClusterView> serverlessMTMClusterViews =
        _clusterSvc.getServerlessMTMClusterViewsByFilter(
            getPaginatedMTMClusterQuery(pSearchValue, pCloudProvider, pRegionName, pInstanceSize),
            pLimit,
            pSkip);
    return Response.ok(serverlessMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/serverless/clusters/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMClustersByGroup.GET")
  public Response getServerlessMTMClustersByGroup(
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip)
      throws SvcException {
    verifyServerlessIsSupported();
    final List<ServerlessMTMClusterView> serverlessMTMClusterViews =
        _clusterSvc.getServerlessMTMClusterViewsByGroup(pGroupId, pLimit, pSkip);
    return Response.ok(serverlessMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/serverless/clusters/count")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.countServerlessMTMClusters.GET")
  public Response countServerlessMTMClusters(
      @QueryParam("searchValue") final String pSearchValue,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName,
      @QueryParam("instanceSize") final String pInstanceSize) {
    verifyServerlessIsSupported();
    final int count =
        (int)
            _clusterSvc.countServerlessMTMClusters(
                getPaginatedMTMClusterQuery(
                    pSearchValue, pCloudProvider, pRegionName, pInstanceSize));
    return Response.ok(new BasicDBObject("count", count)).build();
  }

  @GET
  @Path("/mtm/serverless/clusters/{groupId}/count")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.countServerlessMTMClustersByGroup.GET")
  public Response countServerlessMTMClustersByGroup(@PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    verifyServerlessIsSupported();
    final int count = (int) _clusterSvc.countServerlessMTMClustersByGroup(pGroupId);
    return Response.ok(new BasicDBObject("count", count)).build();
  }

  @GET
  @Path("/mtm/flex/clusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFlexMTMClusters.GET")
  public Response getFlexMTMClusters(
      @QueryParam("searchValue") final String pSearchValue,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName,
      @QueryParam("instanceSize") final String pInstanceSize,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip) {
    verifyFlexIsSupported();

    final List<FlexMTMClusterView> flexMTMClusterViews =
        _clusterSvc.getFlexMTMClusterViews(
            getPaginatedMTMClusterQuery(pSearchValue, pCloudProvider, pRegionName, pInstanceSize),
            pLimit,
            pSkip);
    return Response.ok(flexMTMClusterViews).build();
  }

  @GET
  @Path("/mtm/flex/clusters/count")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.countFlexMTMClusters.GET")
  public Response countFlexMTMClusters(
      @QueryParam("searchValue") final String pSearchValue,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName,
      @QueryParam("instanceSize") final String pInstanceSize) {
    verifyFlexIsSupported();
    final int count =
        (int)
            _clusterSvc.countFlexMTMClusters(
                getPaginatedMTMClusterQuery(
                    pSearchValue, pCloudProvider, pRegionName, pInstanceSize));
    return Response.ok(new BasicDBObject("count", count)).build();
  }

  @GET
  @Path("/dataValidations")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataValidationRecordsWithStatus.GET")
  public Response getDataValidationRecordsWithStatus(
      @Context HttpServletRequest pRequest,
      @QueryParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterName") final String pClusterName,
      @QueryParam("targetInstanceId") final String pTargetInstanceId,
      @QueryParam("status") final String pStatus,
      @QueryParam("validationStatus") final String pValidationStatus,
      @QueryParam("minCreateTimestamp") final Long pMinCreateTimestamp,
      @QueryParam("maxCreateTimestamp") final Long pMaxCreateTimestamp,
      @QueryParam("minCompletionTimestamp") final Long pMinCompletionTimestamp,
      @QueryParam("maxCompletionTimestamp") final Long pMaxCompletionTimestamp,
      @QueryParam("latest") @DefaultValue("true") final Boolean pLatest,
      @QueryParam("limit") @DefaultValue("200") final int pLimit,
      @QueryParam("offset") @DefaultValue("0") final int pOffset) {

    final ObjectId targetInstanceId =
        pTargetInstanceId != null ? new ObjectId(pTargetInstanceId) : null;
    final Optional<DataValidationRun.Status> status = DataValidationRun.Status.fromName(pStatus);
    final Optional<DataValidationRun.ValidationStatus> validationStatus =
        DataValidationRun.ValidationStatus.fromName(pValidationStatus);

    // Grab one more than the limit to determine if there are more records
    return Response.ok(
            new SearchDataValidationRecordAdminView(
                _dataValidationSvc
                    .findWithAdminFilter(
                        pGroupId,
                        pClusterName,
                        targetInstanceId,
                        status.orElse(null),
                        validationStatus.orElse(null),
                        pMinCreateTimestamp != null ? new Date(pMinCreateTimestamp) : null,
                        pMaxCreateTimestamp != null ? new Date(pMaxCreateTimestamp) : null,
                        pMinCompletionTimestamp != null ? new Date(pMinCompletionTimestamp) : null,
                        pMaxCompletionTimestamp != null ? new Date(pMaxCompletionTimestamp) : null,
                        pLatest,
                        pLimit + 1,
                        pOffset)
                    .stream()
                    .map(DataValidationRecordAdminView::new)
                    .collect(Collectors.toList()),
                pLimit,
                pOffset))
        .build();
  }

  @GET
  @Path("/dataValidations/aggregations")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataValidationRecordsAggregations.GET")
  public Response getDataValidationRecordsAggregations(
      @Context HttpServletRequest pRequest,
      @QueryParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterName") final String pClusterName,
      @QueryParam("targetInstanceId") final String pTargetInstanceId,
      @QueryParam("status") final String pStatus,
      @QueryParam("validationStatus") final String pValidationStatus,
      @QueryParam("minCreateTimestamp") final Long pMinCreateTimestamp,
      @QueryParam("maxCreateTimestamp") final Long pMaxCreateTimestamp,
      @QueryParam("minCompletionTimestamp") final Long pMinCompletionTimestamp,
      @QueryParam("maxCompletionTimestamp") final Long pMaxCompletionTimestamp,
      @QueryParam("latest") final Boolean pLatest) {

    final ObjectId targetInstanceId =
        pTargetInstanceId != null ? new ObjectId(pTargetInstanceId) : null;
    final Optional<DataValidationRun.Status> status = DataValidationRun.Status.fromName(pStatus);
    final Optional<DataValidationRun.ValidationStatus> validationStatus =
        DataValidationRun.ValidationStatus.fromName(pValidationStatus);

    return Response.ok(
            new DataValidationAggregationResultsAdminView(
                _dataValidationSvc.runAggregationSearchQuery(
                    pGroupId,
                    pClusterName,
                    targetInstanceId,
                    status.orElse(null),
                    validationStatus.orElse(null),
                    pMinCreateTimestamp != null ? new Date(pMinCreateTimestamp) : null,
                    pMaxCreateTimestamp != null ? new Date(pMaxCreateTimestamp) : null,
                    pMinCompletionTimestamp != null ? new Date(pMinCompletionTimestamp) : null,
                    pMaxCompletionTimestamp != null ? new Date(pMaxCompletionTimestamp) : null,
                    pLatest)))
        .build();
  }

  @GET
  @Path("/dataValidation/{validationId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataValidationRecordById.GET")
  public Response getDataValidationRecordById(
      @Context final HttpServletRequest pRequest,
      @PathParam("validationId") final String pValidationId) {

    final Optional<DataValidationRecord> record =
        _dataValidationSvc.findById(new ObjectId(pValidationId));
    return Response.ok()
        .entity(record.isPresent() ? new DataValidationRecordAdminView(record.get()) : "{}")
        .build();
  }

  @GET
  @Path("/dataValidation/{validationId}/logs/{logType}")
  @Produces("application/gzip")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataValidationLogsById.GET")
  public Response getDataValidationLogsById(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("validationId") final String pValidationId,
      @PathParam("logType") final String pLogType)
      throws SvcException {

    final Optional<DataValidationRecord> record =
        _dataValidationSvc.findById(new ObjectId(pValidationId));
    if (record.isEmpty() || record.get().getOptionalLogMetadata().isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final SupportedLogs logType = SupportedLogs.from(pLogType);
    if (logType == null) {
      return Response.status(HttpStatus.SC_NOT_FOUND).build();
    }

    _ndsAdminSvc.auditForLogAccess(
        record.get().getGroupId(), logType, null, false, pAuditInfo, pUser);

    final ServerAudit.Builder builder =
        new ServerAudit.Builder(
            HostEvent.Type.NDS_HOST_LOGS_DOWNLOADED, record.get().getGroupId(), null);
    builder.auditInfo(pAuditInfo);
    builder.createdAt(new Date());
    builder.hidden(true);
    builder.reason(logType.name());
    final ServerAudit serverAudit = builder.build();
    _auditSvc.saveAuditEvent(serverAudit);

    return Response.ok(_ndsAWSLogDownloadSvc.getLogsForValidationMachine(record.get(), logType))
        .header(
            "Content-Disposition",
            String.format(
                "attachment; filename=\"%s_%s.%s\"",
                record.get().getOptionalLogMetadata().get().getHostname(),
                logType.getType(),
                FileExtension.LOG_GZ))
        .build();
  }

  @POST
  @Path("/dataValidation/{targetInstanceId}/rerun")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.rerunValidation.POST")
  public Response rerunValidation(@PathParam("targetInstanceId") final ObjectId pTargetInstanceId)
      throws SvcException {
    _dataValidationSvc.rerunValidationForInstanceWithTargetInstanceId(pTargetInstanceId, LOG, null);
    return Response.ok().entity("{}").build();
  }

  @POST
  @Path("/dataValidation/cancel")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_ATLAS_DATA_VALIDATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.cancelDataValidations.POST")
  public Response cancelDataValidations(final CancelDataValidationRequestView pRequest)
      throws SvcException {
    final int cancelledCount =
        _dataValidationSvc.cancelDataValidations(pRequest.getValidationIds());
    return Response.ok().entity("{\"cancelledCount\": " + cancelledCount + "}").build();
  }

  @POST
  @Path("/dataValidation/{hostname}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.requestValidationForInstance.POST")
  public Response requestValidationForInstance(
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("hostname") final String pHostname,
      final DataValidationArgumentsView pArgumentsView)
      throws SvcException {
    final ClusterDescriptionId clusterDescriptionId =
        _replicaSetHardwareSvc
            .getClusterDescriptionIdForHostname(pHostname, null)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_CLUSTER));
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, clusterDescriptionId);
    _dataValidationSvc.requestValidationForHostname(
        pHostname,
        LOG,
        true,
        pArgumentsView.toArguments(),
        CorruptionDetectionOperationOrigin.MANUAL,
        clusterDescriptionId.getGroupId(),
        pArgumentsView.getReason());
    return Response.ok().entity("{}").build();
  }

  @POST
  @Path("/dataValidation/groupId/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.requestValidationForGroup.POST")
  public Response requestValidationForGroup(
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      final DataValidationArgumentsView pArgsView)
      throws SvcException {
    final DataValidationArguments args =
        ofNullable(pArgsView)
            .map(DataValidationArgumentsView::toArguments)
            .orElse(DataValidationArguments.noArgs());
    _dataValidationSvc.requestImmediateValidationForGroup(
        pGroupId, LOG, args, CorruptionDetectionOperationOrigin.MANUAL, pAuditInfo, null);
    return Response.ok().entity("{}").build();
  }

  @GET
  @Path("/dataValidations/csv")
  @Produces("text/csv")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.exportDataValidationRecordsAsCsv.GET")
  public Response exportDataValidationRecordsAsCsv(
      @Context HttpServletRequest pRequest,
      @QueryParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterName") final String pClusterName,
      @QueryParam("targetInstanceId") final ObjectId pTargetInstanceId,
      @QueryParam("status") final String pStatus,
      @QueryParam("validationStatus") final String pValidationStatus,
      @QueryParam("minCreateTimestamp") final Long pMinCreateTimestamp,
      @QueryParam("maxCreateTimestamp") final Long pMaxCreateTimestamp,
      @QueryParam("minCompletionTimestamp") final Long pMinCompletionTimestamp,
      @QueryParam("maxCompletionTimestamp") final Long pMaxCompletionTimestamp,
      @QueryParam("latest") final Boolean pLatest) {
    final Optional<DataValidationRun.Status> status = DataValidationRun.Status.fromName(pStatus);
    final Optional<DataValidationRun.ValidationStatus> validationStatus =
        DataValidationRun.ValidationStatus.fromName(pValidationStatus);

    final List<DataValidationRecord> dataValidationRecords =
        _dataValidationSvc
            .findWithAdminFilter(
                pGroupId,
                pClusterName,
                pTargetInstanceId,
                status.orElse(null),
                validationStatus.orElse(null),
                pMinCreateTimestamp != null ? new Date(pMinCreateTimestamp) : null,
                pMaxCreateTimestamp != null ? new Date(pMaxCreateTimestamp) : null,
                pMinCompletionTimestamp != null ? new Date(pMinCompletionTimestamp) : null,
                pMaxCompletionTimestamp != null ? new Date(pMaxCompletionTimestamp) : null,
                pLatest,
                0,
                0)
            .stream()
            .collect(Collectors.toList());

    StreamingOutput fileStream =
        output -> {
          try {
            _dataValidationExportCsvSvc.exportRecordsAsCsv(dataValidationRecords, output);
          } catch (final IOException e) {
            LOG.error("Exception exporting data validation records as CSV: ", e);
          }
        };

    final SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
    return Response.ok(fileStream)
        .header(
            "Content-Disposition",
            String.format(
                "attachment; filename=data-validation-records-%s.csv", df.format(new Date())))
        .build();
  }

  @GET
  @Path("/customBuilds")
  @Produces("application/json")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getCustomBuilds.GET")
  public Response getCustomBuilds(
      @Context final HttpServletRequest pRequest,
      @QueryParam("trueNameFilter") @Nullable final String pTrueNameFilter,
      @QueryParam("trueNameAfter") @Nullable final String pTrueNameAfter,
      @QueryParam("trueNameBefore") @Nullable final String pTrueNameBefore,
      @QueryParam("limit") @Nullable final Integer pLimit) {
    final int limit = Math.min(100, pLimit != null ? pLimit : 30);
    return Response.ok()
        .entity(
            _customMongoDbBuildSvc.findCustomBuilds(
                pTrueNameFilter, pTrueNameAfter, pTrueNameBefore, limit))
        .build();
  }

  @POST
  @Path("/customBuilds")
  @Produces("application/json")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addCustomMongoDbBuild.POST")
  public Response addCustomMongoDbBuild(
      @Context final HttpServletRequest pRequest,
      @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasMongoDbBuildView pApiAtlasMongoDbBuildView) {
    try {
      _customMongoDbBuildSvc.addCustomBuild(pApiAtlasMongoDbBuildView.toMongoDbBuild());
    } catch (SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_ARGUMENT)) {
        if (pApiAtlasMongoDbBuildView.getTrueName() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, MongoDbBuild.TRUE_NAME_FIELD);
        }
        if (pApiAtlasMongoDbBuildView.getPlatform() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, MongoDbBuild.PLATFORM_FIELD);
        }
        if (pApiAtlasMongoDbBuildView.getUrl() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, MongoDbBuild.URL_FIELD);
        }
        if (pApiAtlasMongoDbBuildView.getGitVersion() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, MongoDbBuild.GIT_VERSION_FIELD);
        }
      }
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_MONGODB_BUILD_NAME)) {
        throw ApiErrorCode.INVALID_MONGODB_BUILD_NAME.exception(
            pEnvelope, pApiAtlasMongoDbBuildView.getTrueName());
      }
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_MONGODB_CUSTOM_BUILD_NAME)) {
        throw ApiErrorCode.INVALID_MONGODB_CUSTOM_BUILD_NAME.exception(
            pEnvelope, pApiAtlasMongoDbBuildView.getTrueName());
      }

      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_MONGODB_VERSION_CONFIG)) {
        final MongoDbVersion mongoDbVersion =
            new MongoDbVersion.Builder(pApiAtlasMongoDbBuildView.getTrueName())
                .addBuild(pApiAtlasMongoDbBuildView.toMongoDbBuild())
                .customBuild()
                .build();

        // Re-running this validation here is only to get the error message without having to string
        // parse pE.getMessage() which technically can change
        try {
          _automationValidationSvc.validateMongoDbVersion(mongoDbVersion);
        } catch (final ValidationException pValidationException) {
          throw ApiErrorCode.INVALID_MONGODB_VERSION_CONFIG.exception(
              pEnvelope, pValidationException.getMessage().replaceAll("\\.$", ""));
        }
      }

      if (pE.getErrorCode().equals(NDSErrorCode.DUPLICATE_MONGODB_BUILD_NAME)) {
        throw ApiErrorCode.DUPLICATE_MONGODB_BUILD_NAME.exception(
            pEnvelope, pApiAtlasMongoDbBuildView.getTrueName());
      }

      LOG.warn(pE.getErrorCode().getMessage() + ": " + pE.getMessage());
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
    }

    return new ApiResponseBuilder(pEnvelope)
        .created()
        .content(
            new ApiAtlasMongoDbBuildView(
                _customMongoDbBuildSvc
                    .getCustomBuild(pApiAtlasMongoDbBuildView.getTrueName())
                    .stream()
                    .filter(build -> build.equals(pApiAtlasMongoDbBuildView.toMongoDbBuild()))
                    .findFirst()
                    .get()))
        .build();
  }

  @DELETE
  @Path("/customBuilds/{trueName}")
  @Produces("application/json")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteCustomMongoDBBuild.DELETE")
  public Response deleteCustomMongoDBBuild(
      @Context final HttpServletRequest pRequest, @PathParam("trueName") final String pTrueName)
      throws SvcException {
    _customMongoDbBuildSvc.deleteCustomBuilds(pTrueName);
    return Response.ok().build();
  }

  @GET
  @Path("/restore/{restoreJobId}/logs/{logName}.gz")
  @Produces("application/gzip")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getRestoreVMLogsById.GET")
  public Response getRestoreVMLogsById(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("restoreJobId") final String pRestoreJobId,
      @PathParam("logName") final String pLogName)
      throws SvcException {
    final ReplicaSetBackupRestoreJob restoreJob =
        _cpsSvc.getReplicaSetBackupRestoreJob(new ObjectId(pRestoreJobId));

    if (restoreJob == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    final ObjectId groupId = restoreJob.getProjectId();

    final RegionName regionName;
    final String deploymentClusterName;

    if (restoreJob
        instanceof
        TenantUpgradeToServerlessBackupRestoreJob tenantUpgradeToServerlessBackupRestoreJob) {
      final Optional<TenantUpgradeToServerlessStatus> status =
          _tenantUpgradeToServerlessSvc.getTenantUpgradeToServerlessStatus(
              tenantUpgradeToServerlessBackupRestoreJob
                  .getServerlessRestoreMetadata()
                  .getSnapshotClusterName(),
              groupId);
      if (status.isEmpty()) {
        return Response.status(Response.Status.NOT_FOUND).build();
      }

      regionName = tenantUpgradeToServerlessBackupRestoreJob.getRegionName();
      deploymentClusterName =
          status.flatMap(TenantUpgradeToServerlessStatus::getSourceTenantDeploymentName).orElse("");
      if (deploymentClusterName.isEmpty()) {
        return Response.status(Response.Status.NOT_FOUND).build();
      }
    } else {
      ObjectId snapshotGroupId = groupId;
      if (restoreJob instanceof ServerlessStreamingBackupRestoreJob serverlessRestoreJob) {
        snapshotGroupId = serverlessRestoreJob.getServerlessRestoreMetadata().getSnapshotGroupId();
      }

      final ReplicaSetBackupSnapshot replicaSetBackupSnapshot =
          _cpsSvc.getReplicaSetBackupSnapshot(snapshotGroupId, restoreJob.getSnapshotId());
      if (replicaSetBackupSnapshot == null) {
        return Response.status(Response.Status.NOT_FOUND).build();
      }

      regionName = replicaSetBackupSnapshot.getRegion();
      deploymentClusterName = replicaSetBackupSnapshot.getDeploymentClusterName();
    }

    final StrategyName strategy = restoreJob.getMetadata().getStrategy();
    final boolean isExport = strategy == StrategyName.EXPORT;
    final boolean isStreaming = strategy.isStreaming();
    final boolean isServerless = strategy.isServerlessStrategy();

    if (!isExport && !isStreaming && !isServerless) {
      LOG.info("Log download not applicable for this restore job type");
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }
    if (!ValidationUtils.isValidLogName(pLogName)) {
      LOG.info("Invalid log name parameter: {}", pLogName);
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }
    final SupportedLogs logName = SupportedLogs.from(pLogName);
    final List<SupportedLogs> restoreLogsSupported =
        new ArrayList<>(List.of(SupportedLogs.CHEF_CLIENT, SupportedLogs.MESSAGES));

    if (isExport) {
      restoreLogsSupported.addAll(Arrays.asList(SupportedLogs.MONGODB, SupportedLogs.AUTOMATION));
    }
    if (isServerless) {
      restoreLogsSupported.addAll(
          Arrays.asList(
              SupportedLogs.MONGODB,
              SupportedLogs.SERVERLESS_RESTORE_TARGET_MONGODB,
              SupportedLogs.AUTOMATION));
    }
    if (logName == null || !restoreLogsSupported.contains(logName)) {
      LOG.info("log name not supported by restore type: {}", pLogName);
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final Date restoreStartDate = restoreJob.getId().getDate();

    // restore VMs can persist for some extra time to allow Filebeat to finish uploading logs
    // otherwise we use the current time (if the job is not yet finished)
    final Date endDate =
        restoreJob
            .getFinishedDate()
            .map(d -> DateUtils.addMinutes(d, _appSettings.getBackupDestroyVmBufferMinutes()))
            .orElse(new Date());
    final LocalDateTime startDateLocal =
        LocalDateTime.ofInstant(restoreStartDate.toInstant(), ZoneOffset.UTC);
    final LocalDateTime endDateLocal = LocalDateTime.ofInstant(endDate.toInstant(), ZoneOffset.UTC);
    final String hostname = ((VMBasedReplSetRestoreJob) restoreJob).getHostname();

    final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    _ndsAdminSvc.auditForLogAccess(groupId, logName, null, false, pAuditInfo, pUser);

    final ServerAudit.Builder builder =
        new ServerAudit.Builder(HostEvent.Type.NDS_HOST_LOGS_DOWNLOADED, groupId, null);
    builder.auditInfo(pAuditInfo);
    builder.createdAt(new Date());
    builder.hidden(true);
    builder.reason(pLogName);
    final ServerAudit serverAudit = builder.build();
    _auditSvc.saveAuditEvent(serverAudit);

    return Response.ok(
            _ndsAWSLogDownloadSvc.getLogsForDatesForRestore(
                restoreStartDate.getTime(),
                endDate.getTime(),
                groupId,
                (VMBasedReplSetRestoreJob) restoreJob,
                logName,
                regionName,
                deploymentClusterName))
        .header(
            "Content-Disposition",
            String.format(
                "attachment; filename=\"%s_%s_%s_%s.%s\"",
                hostname,
                startDateLocal.format(formatter),
                endDateLocal.format(formatter),
                pLogName,
                FileExtension.LOG_GZ))
        .build();
  }

  @GET
  @Path("/liveImports")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLiveImports.GET")
  public Response getLiveImports(@QueryParam("status") final String pStatus) throws Exception {
    final List<LiveImport> liveImports;
    switch (pStatus) {
      case "running":
        liveImports = _liveImportSvc.getAllRunningImports();
        break;
      case "stopped":
        liveImports = _liveImportSvc.getStoppedImports(Duration.ofHours(48));
        break;
      default:
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }
    final List<LiveImportAdminView> imports =
        liveImports.stream()
            .map(
                i ->
                    new LiveImportAdminView(
                        i,
                        _groupDao.findNameById(i.getGroupId()),
                        _hardwareDao.findByCluster(
                            i.getGroupId(), i.getDestination().getClusterName())))
            .collect(Collectors.toList());
    return Response.ok(imports).build();
  }

  @GET
  @Path("/liveImports/recent")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getRecentLiveImports.GET")
  public Response getRecentLiveImports() {
    final List<LiveImport> liveImports =
        _liveImportSvc.getRunningAndStoppedImports(Duration.ofDays(3));
    final List<LiveImportAdminView> imports =
        liveImports.stream()
            .map(
                i ->
                    new LiveImportAdminView(
                        i,
                        _groupDao.findNameById(i.getGroupId()),
                        _hardwareDao.findByCluster(
                            i.getGroupId(), i.getDestination().getClusterName())))
            .collect(Collectors.toList());
    return Response.ok(imports).build();
  }

  @GET
  @Path("/liveImports/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLiveImports.GET")
  public Response getLiveImports(@PathParam("groupId") final ObjectId pGroupId) {
    final List<LiveImport> liveImports = _liveImportSvc.getAllImportsForGroup(pGroupId);
    final List<LiveImportAdminView> imports =
        liveImports.stream()
            .map(
                i ->
                    new LiveImportAdminView(
                        i,
                        _groupDao.findNameById(i.getGroupId()),
                        _hardwareDao.findByCluster(
                            i.getGroupId(), i.getDestination().getClusterName())))
            .collect(Collectors.toList());
    return Response.ok(imports).build();
  }

  @GET
  @Path("/liveImport/{liveImportId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLiveImport.GET")
  public Response getLiveImport(@PathParam("liveImportId") final ObjectId pLiveImportId) {
    return _liveImportSvc
        .getLiveImport(pLiveImportId)
        .map(
            liveImport ->
                Response.ok(
                        new LiveImportAdminView(
                            liveImport,
                            _groupDao.findNameById(liveImport.getGroupId()),
                            _hardwareDao.findByCluster(
                                liveImport.getGroupId(),
                                liveImport.getDestination().getClusterName())))
                    .build())
        .orElse(Response.status(HttpStatus.SC_NOT_FOUND).build());
  }

  @GET
  @Path("/liveImportOverrides")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLiveImportOverrides.GET")
  public Response getLiveImportOverrides() {
    final List<LiveImportOverridesView> liveImportOverrides =
        _liveImportOverridesSvc.findAllOverrides().stream()
            .map(LiveImportOverridesView::new)
            .collect(Collectors.toList());
    return Response.ok(liveImportOverrides).build();
  }

  @PUT
  @Path("/liveImportOverrides")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addLiveImportOverrides.PUT")
  public Response addLiveImportOverrides(
      @Context final AuditInfo pAuditInfo, final LiveImportOverridesView pLiveImportOverridesView)
      throws SvcException {
    _liveImportOverridesSvc.upsertLiveImportOverrides(
        pLiveImportOverridesView.toLiveImportOverrides(), pAuditInfo);

    LOG.info(
        "Added a live import override document for group {}, cluster name {}, by user {}, with"
            + " data: {}",
        pLiveImportOverridesView.getGroupId(),
        pLiveImportOverridesView.getClusterName(),
        pAuditInfo.getUsername(),
        pLiveImportOverridesView);
    return Response.ok().build();
  }

  @PATCH
  @Path("/liveImportOverrides/{groupId}/{clusterName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateLiveImportOverrides.PATCH")
  public Response updateLiveImportOverrides(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final LiveImportOverridesView pLiveImportOverridesView)
      throws SvcException {
    _liveImportOverridesSvc.upsertLiveImportOverrides(
        pLiveImportOverridesView.toLiveImportOverrides(), pAuditInfo);
    LOG.info(
        "Updated a live import override document for group {}, cluster name {}, by user {}, with"
            + " data: {},",
        pGroupId,
        pClusterName,
        pAuditInfo.getUsername(),
        pLiveImportOverridesView);
    return Response.ok().build();
  }

  @DELETE
  @Path("/liveImportOverrides/{groupId}/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteLiveImportOverrides.DELETE")
  public Response deleteLiveImportOverrides(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    _liveImportOverridesSvc.deleteLiveImportOverrides(pGroupId, pClusterName, pAuditInfo);
    LOG.info(
        "Deleted a live import override document for group {}, cluster name {}, by user {}",
        pGroupId,
        pClusterName,
        pAuditInfo.getUsername());
    return Response.ok().build();
  }

  @GET
  @Path("/tenantUpgrades")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantUpgrades.GET")
  public Response getTenantUpgrades(@QueryParam("status") final String pStatus) throws Exception {
    final List<TenantUpgradeStatus> tenantUpgrades;

    switch (pStatus) {
      case "running":
        tenantUpgrades = _tenantUpgradeSvc.getAllRunningTenantUpgrades();
        break;
      case "stopped":
        tenantUpgrades =
            _tenantUpgradeSvc.getCompletedUpgrades(COMPLETED_TENANT_UPGRADE_LOOK_BACK_TIME);
        break;
      default:
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final List<TenantUpgradeAdminView> upgrades =
        tenantUpgrades.stream()
            .map(i -> new TenantUpgradeAdminView(i, _groupDao.findNameById(i.getGroupId())))
            .collect(Collectors.toList());

    return Response.ok(upgrades).build();
  }

  @GET
  @Path("/tenantUpgradesToServerless")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantUpgradesToServerless.GET")
  public Response getTenantUpgradesToServerless(@QueryParam("status") final String pStatus)
      throws Exception {
    final List<TenantUpgradeToServerlessStatus> tenantUpgradesToServerless;

    switch (pStatus) {
      case "running":
        tenantUpgradesToServerless = _tenantUpgradeToServerlessSvc.getAllRunningTenantUpgrades();
        break;
      case "stopped":
        tenantUpgradesToServerless =
            _tenantUpgradeToServerlessSvc.getCompletedUpgrades(
                COMPLETED_TENANT_UPGRADE_LOOK_BACK_TIME);
        break;
      default:
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final List<TenantUpgradeToServerlessAdminView> upgrades =
        tenantUpgradesToServerless.stream()
            .map(
                i ->
                    new TenantUpgradeToServerlessAdminView(
                        i, _groupDao.findNameById(i.getGroupId())))
            .collect(Collectors.toList());

    return Response.ok(upgrades).build();
  }

  @GET
  @Path("/serverlessUpgradesToDedicated")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessUpgradesToDedicated.GET")
  public Response getServerlessUpgradesToDedicated(@QueryParam("status") final String pStatus)
      throws Exception {
    final List<ServerlessUpgradeToDedicatedStatus> serverlessUpgradeToDedicatedStatuses =
        switch (pStatus) {
          case "running" -> _serverlessUpgradeToDedicatedSvc.getAllRunningTenantUpgrades();
          case "stopped" ->
              _serverlessUpgradeToDedicatedSvc.getCompletedUpgrades(
                  COMPLETED_TENANT_UPGRADE_LOOK_BACK_TIME);
          default -> throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
        };

    final List<TenantUpgradeAdminView> upgrades =
        serverlessUpgradeToDedicatedStatuses.stream()
            .map(i -> new TenantUpgradeAdminView(i, _groupDao.findNameById(i.getGroupId())))
            .collect(Collectors.toList());

    return Response.ok(upgrades).build();
  }

  @GET
  @Path("/fastSharedPreallocatedRecords")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFastSharedRecordsByState.GET")
  public Response getFastSharedRecordsByState(
      @QueryParam("state") final String pState,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip)
      throws Exception {
    final List<FastSharedRecordAdminView> records;
    // Adding one to our limit to mimic getting an extra record to check if there's a next page
    if (pState.equals("ALL")) {
      records =
          _ndsSharedFastProvisioningSvc.getAllRecords(pLimit + 1, pSkip).stream()
              .map(FastSharedRecordAdminView::new)
              .collect(Collectors.toList());
    } else {
      try {
        State state = State.valueOf(pState);
        records =
            _ndsSharedFastProvisioningSvc.getRecordsByState(state, pLimit + 1, pSkip).stream()
                .map(FastSharedRecordAdminView::new)
                .collect(Collectors.toList());
      } catch (final Exception e) {
        throw new IllegalArgumentException("Illegal State Value");
      }
    }
    final List<FastSharedRecordAdminView> data =
        records.size() < pLimit ? records : records.subList(0, pLimit);
    final boolean isLastPage = records.size() == 0 || records.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final FastTenantRecordContainer<FastSharedRecordAdminView> container =
        new FastTenantRecordContainer<>(data, isFirstPage, isLastPage);
    return Response.ok(container).build();
  }

  @GET
  @Path("/fastFlexPreallocatedRecords")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFastFlexPreallocatedRecordsByState.GET")
  public Response getFastFlexPreallocatedRecordsByState(
      @QueryParam("state") final String pState,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip)
      throws SvcException {
    final List<FlexRecordAdminView> records;
    if (pState.equals("ALL")) {
      records =
          _ndsFlexFastProvisioningSvc.getAllRecords(pLimit + 1, pSkip).stream()
              .map(FlexRecordAdminView::new)
              .collect(Collectors.toList());
    } else {
      try {
        State state = State.valueOf(pState);
        records =
            _ndsFlexFastProvisioningSvc.getRecordsByState(state, pLimit + 1, pSkip).stream()
                .map(FlexRecordAdminView::new)
                .collect(Collectors.toList());
      } catch (final Exception e) {
        throw new IllegalArgumentException("Illegal State Value" + e);
      }
    }
    final List<FlexRecordAdminView> data =
        records.size() < pLimit ? records : records.subList(0, pLimit);
    final boolean isLastPage = records.size() == 0 || records.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final FastTenantRecordContainer<FlexRecordAdminView> container =
        new FastTenantRecordContainer<>(data, isFirstPage, isLastPage);
    return Response.ok(container).build();
  }

  @GET
  @Path("/fastServerlessPreallocatedRecords")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFastServerlessPreallocatedRecordsByState.GET")
  public Response getFastServerlessPreallocatedRecordsByState(
      @QueryParam("state") final String pState,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip)
      throws SvcException {
    final List<FastServerlessRecordAdminView> records;
    if (pState.equals("ALL")) {
      // We add one to our limit to check if there's a next page
      records =
          _ndsServerlessFastProvisioningSvc.getAllRecordsAndGroupId(pLimit + 1, pSkip).stream()
              .map(pair -> new FastServerlessRecordAdminView(pair.getLeft(), pair.getRight()))
              .collect(Collectors.toList());
    } else {
      try {
        State state = State.valueOf(pState);
        records =
            _ndsServerlessFastProvisioningSvc
                .getRecordsAndGroupIdByState(state, pLimit + 1, pSkip)
                .stream()
                .map(pair -> new FastServerlessRecordAdminView(pair.getLeft(), pair.getRight()))
                .collect(Collectors.toList());
      } catch (final Exception e) {
        throw new IllegalArgumentException("Illegal State Value");
      }
    }
    final List<FastServerlessRecordAdminView> data =
        records.size() < pLimit ? records : records.subList(0, pLimit);
    final boolean isLastPage = records.size() == 0 || records.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final FastTenantRecordContainer<FastServerlessRecordAdminView> container =
        new FastTenantRecordContainer<>(data, isFirstPage, isLastPage);
    return Response.ok(container).build();
  }

  @POST
  @Path("/setFastSharedPreallocatedRecordState")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setFastSharedRecordsStateByRecordId.POST")
  public Response setFastSharedRecordsStateByRecordId(
      final FastSharedRecordAdminView pRecordAdminView) throws Exception {
    State state = pRecordAdminView.getState();
    _ndsSharedFastProvisioningSvc.setRecordState(pRecordAdminView.getId(), state);
    if (pRecordAdminView.getState().equals(State.NEEDS_CLEANUP)) {
      _ndsGroupDao.setPlanASAP(pRecordAdminView.getGroupId());
    }
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/setFastFlexPreallocatedRecordState")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setFastFlexRecordsStateByRecordId.POST")
  public Response setFastFlexRecordsStateByRecordId(final FlexRecordAdminView pRecordAdminView) {
    State state = pRecordAdminView.getState();
    _ndsFlexFastProvisioningSvc.setRecordState(pRecordAdminView.getId(), state);
    if (pRecordAdminView.getState().equals(State.NEEDS_CLEANUP)) {
      _ndsGroupDao.setPlanASAP(pRecordAdminView.getGroupId());
    }
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/setFastServerlessPreallocatedRecordState")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setFastServerlessTenantRecordByRecordId.POST")
  public Response setFastServerlessTenantRecordByRecordId(
      final FastServerlessRecordAdminView pRecordAdminView) throws Exception {
    State state = pRecordAdminView.getState();
    _ndsServerlessFastProvisioningSvc.setRecordState(pRecordAdminView.getId(), state);
    if (pRecordAdminView.getState().equals(State.NEEDS_CLEANUP)) {
      _ndsGroupDao.setPlanASAP(pRecordAdminView.getGroupId());
    }
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}/clearTargetGroups")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.clearUnprovisionedTargetGroups.POST")
  public Response clearUnprovisionedTargetGroups(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId) {
    if (_targetGroupDao.deleteUnprovisionedTargetGroups(pGroupId)) {
      final Builder builder =
          new Builder(NDSAudit.Type.CLEAR_UNPROVISIONED_TARGET_GROUPS_REQUESTED);
      builder.auditInfo(pAuditInfo);
      builder.groupId(pGroupId);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());

      return Response.ok().build();
    } else {
      return Response.status(400).build();
    }
  }

  @GET
  @Path("/groups/{groupId}/privateLinkConnectionRules")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPrivateLinkConnectionRules.GET")
  public Response getPrivateLinkConnectionRules(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId) {
    final List<AWSPrivateLinkTargetGroupAdminView> awsTargetGroups =
        _targetGroupDao.getAllSingleTargetPrivateLinkConnectionRulesForGroup(pGroupId).stream()
            .map(AWSPrivateLinkTargetGroupAdminView::new)
            .collect(Collectors.toList());
    final List<AzurePrivateLinkConnectionInboundNATRuleAdminView> inboundNATRules =
        _inboundNATRuleDao.getAllSingleTargetPrivateLinkConnectionRulesForGroup(pGroupId).stream()
            .map(AzurePrivateLinkConnectionInboundNATRuleAdminView::new)
            .collect(Collectors.toList());
    final List<GCPPSCConnectionRuleAdminView> gcpConnectionRules =
        _gcpPrivateServiceConnectionRuleDao
            .getAllSingleTargetPrivateLinkConnectionRulesForGroup(pGroupId)
            .stream()
            .map(GCPPSCConnectionRuleAdminView::new)
            .collect(Collectors.toList());
    return Response.ok(
            new PrivateLinkConnectionRulesByProviderAdminView(
                awsTargetGroups, inboundNATRules, gcpConnectionRules))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/privateEndpointUsage")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getPrivateEndpointUsage.GET")
  public Response getPrivateEndpointUsage(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId) {
    final Group group = ofNullable(_groupSvc.findById(pGroupId)).orElseThrow();
    final NDSGroup ndsGroup = _ndsGroupSvc.find(pGroupId).orElseThrow();
    final List<Cluster> clusters =
        _clusterSvc.getMergedClusterDescriptions(pGroupId).stream()
            .map(
                clusterDescription ->
                    Cluster.getCluster(
                        clusterDescription,
                        _replicaSetHardwareSvc.getReplicaSetHardware(
                            pGroupId, clusterDescription.getName())))
            .toList();
    final boolean advancedRegionalizedModeFeatureEnabled =
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_ADVANCED_REGIONALIZED_PRIVATE_ENDPOINTS, null, group);

    // Single target groups
    final var singleTargetGroups =
        clusters.stream()
            .flatMap(
                c ->
                    Stream.of(
                            _awsConnectionRuleWithHostnameSvc
                                .getClusterPerInstanceConnectionRulesForClusterInstances(
                                    ndsGroup, c),
                            _azureConnectionRuleWithHostnameSvc
                                .getClusterPerInstanceConnectionRulesForClusterInstances(
                                    ndsGroup, c),
                            _gcpConnectionRuleWithHostnameSvc
                                .getClusterPerInstanceConnectionRulesForClusterInstances(
                                    ndsGroup, c))
                        .flatMap(List::stream))
            .map(ConnectionRuleWithHostname::getConnectionRule)
            .filter(rule -> !rule.isForHiddenSecondary())
            .toList();

    final var singleTargetLoadBalancerUsageView =
        new PrivateEndpointConnectionRuleLoadBalancerUsageAdminView(singleTargetGroups);

    // AWS multi target group
    final var awsMultiTargetListenerArns =
        clusters.stream()
            .flatMap(
                c ->
                    _awsConnectionRuleWithHostnameSvc
                        .getClusterMultiTargetConnectionRulesForClusterInstances(ndsGroup, c)
                        .stream())
            .map(ConnectionRuleWithHostname::getConnectionRule)
            .map(r -> (AWSMultiTargetConnectionRule) r)
            .map(AWSMultiTargetConnectionRule::getListenerArn)
            .toList();

    final Map<String, Integer> multiTargetListenerArnCount = new HashMap<>();
    awsMultiTargetListenerArns.forEach(
        arn -> {
          final String nlbName =
              PrivateEndpointConnectionRuleLoadBalancerUsageAdminView
                  .parseLoadBalancerNameFromAWSListenerArn(arn);
          multiTargetListenerArnCount.put(
              nlbName, multiTargetListenerArnCount.getOrDefault(nlbName, 0) + 1);
        });
    final var multiTargetGroupUsageView =
        new PrivateEndpointConnectionRuleLoadBalancerUsageAdminView(
            Map.of(CloudProvider.AWS.name(), multiTargetListenerArnCount));

    final var loadBalancerUsageView =
        PrivateEndpointConnectionRuleLoadBalancerUsageAdminView.combine(
            singleTargetLoadBalancerUsageView, multiTargetGroupUsageView);

    final var clusterViews =
        clusters.stream()
            .map(
                cluster -> {
                  final ClusterDescription clusterDescription = cluster.getClusterDescription();
                  final ClusterContainerGroup clusterContainerGroup =
                      ClusterContainerGroupUtil.getClusterContainerGroupByClusterDescription(
                          ndsGroup, clusterDescription);
                  final Map<RegionName, Integer> targetRulesPerRegion =
                      _ndsPrivateLinkLimitsSvc.getRegionNameToNumListenersUsed(
                          clusterDescription.getRegionNames(),
                          List.of(clusterDescription),
                          ndsGroup);
                  final Map<RegionName, Integer> visibleNodesPerRegion =
                      clusterDescription.getRegionNames().stream()
                          .collect(
                              Collectors.toMap(
                                  r -> r,
                                  clusterDescription
                                      ::getTotalVisibleNumNodesWithShardDataByRegion));

                  final boolean generateLegacyConnectionStrings =
                      clusterContainerGroup.clusterShouldHaveSingleTargetRules(
                          advancedRegionalizedModeFeatureEnabled);
                  final boolean generateLoadBalancedConnectionString =
                      clusterContainerGroup.clusterDescriptionSupportsLoadBalancedConnectionStrings(
                          advancedRegionalizedModeFeatureEnabled);

                  return new PrivateEndpointConnectionRuleClusterUsageAdminView(
                      clusterDescription.getClusterType(),
                      clusterDescription.getName(),
                      generateLegacyConnectionStrings,
                      generateLoadBalancedConnectionString,
                      targetRulesPerRegion,
                      visibleNodesPerRegion);
                })
            .toList();

    return Response.ok(
            new PrivateEndpointConnectionRuleUsageAdminView(loadBalancerUsageView, clusterViews))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/gcpPrivateServiceConnections")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getGCPPrivateServiceConnections.GET")
  public Response getGCPPrivateServiceConnections(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId) {
    final List<GCPPrivateServiceConnection> privateServiceConnections =
        _gcpPrivateServiceConnectionDao.findAllPrivateServiceConnectionsByProjectId(pGroupId);
    final Map<GCPRegionName, List<GCPPrivateServiceConnection>> regionNameToConnectionsMap =
        privateServiceConnections.stream()
            .collect(Collectors.groupingBy(GCPPrivateServiceConnection::getRegionName));

    final Map<String, List<GCPPrivateServiceConnectionAdminView>> map =
        regionNameToConnectionsMap.entrySet().stream()
            .collect(
                Collectors.toMap(
                    e -> e.getKey().getValue(),
                    e ->
                        e.getValue().stream()
                            .map(GCPPrivateServiceConnectionAdminView::new)
                            .collect(Collectors.toList())));
    return Response.ok(map).build();
  }

  @GET
  @Path("/groups/{groupId}/fetchHostnames")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fetchHostnames.GET")
  public Response fetchHostnames(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final List<String> hostnameList =
        _automationConfigQuerySvc.findPublishedManagedHostnames(pGroupId);

    if (hostnameList == null || hostnameList.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Project has no hostnames");
    }

    Map<String, List<String>> response = new HashMap<>();
    response.put("allHostnames", hostnameList);

    return Response.ok(response).build();
  }

  @GET
  @Path("/groups/{groupId}/fetchMonitoringAgents")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fetchMonitoringAgents.GET")
  public Response fetchMonitoringAgents(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final AutomationConfig automationConfig = _automationConfigQuerySvc.findPublished(pGroupId);

    if (automationConfig == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "AutomationConfig not found");
    }

    final List<String> monitoringAgentHosts =
        automationConfig.getDeployment().getMonitoringAgentVersions().stream()
            .map(AgentConfig::getHostname)
            .toList();

    if (monitoringAgentHosts == null || monitoringAgentHosts.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Project has no monitoring agents");
    }

    Map<String, List<String>> response = new HashMap<>();
    response.put("monitoringAgentHostnames", monitoringAgentHosts);

    return Response.ok(response).build();
  }

  @PATCH
  @Path("/groups/{groupId}/monitoringAgentHostnames")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateMonitoringAgentPlacement.PATCH")
  public Response updateMonitoringAgentPlacement(
      @Context final AuditInfo pAuditInfo,
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("hostnames") final List<String> pHostnames,
      @FormParam("ttlMinutes") final Integer pTTLMinutes)
      throws SvcException {

    if (pGroupId == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Group not found");
    }

    if (pHostnames == null || pHostnames.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Must enter hostnames");
    }

    boolean allUnique = pHostnames.size() == new HashSet<>(pHostnames).size();

    if (!allUnique) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Must enter unique hostnames");
    }

    for (String hostname : pHostnames) {
      if (hostname == null
          || hostname.equals("null")
          || !ValidationUtils.isValidHostName(hostname)) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Must enter valid hostnames");
      }
    }

    if (pTTLMinutes == null || pTTLMinutes <= 0) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Time must be greater than zero");
    }

    if (pTTLMinutes > Duration.ofHours(24).toMinutes()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Time is max 24 hours (1440 minutes)");
    }

    final LocalDateTime expire = LocalDateTime.now().plusMinutes(pTTLMinutes);
    final Instant instant = expire.atZone(ZoneOffset.UTC).toInstant();
    final Date dateExpire = Date.from(instant);

    _ndsGroupSvc.setOverrideHostnamesAndExpireAfter(pGroupId, pHostnames, dateExpire);

    final AutomationConfig automationConfig =
        _automationConfigPublishingSvc.findPublishedOrEmpty(pGroupId);

    final Optional<AgentConfig> existingAgentConfig =
        automationConfig.getDeployment().getMonitoringAgentVersions().stream().findFirst();

    if (existingAgentConfig.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "No monitoring agent found");
    }

    final List<AgentConfig> agentConfigs =
        pHostnames.stream()
            .map(
                h -> {
                  final AgentConfig config =
                      new AgentConfig(existingAgentConfig.get().getVersion(), h);
                  return config;
                })
            .toList();
    final Group group = _groupSvc.findById(pGroupId);
    automationConfig.getDeployment().setMonitoringAgentVersions(agentConfigs);
    _automationConfigPublishingSvc.saveDraft(automationConfig, pUser, pOrganization, group);
    _automationConfigPublishingSvc.publish(
        pOrganization, group, pUser, automationConfig, null, false);

    LOG.info("Updated monitoring agent hostnames for group: {}", pGroupId);
    final Builder builder = new Builder(NDSAudit.Type.MONITORING_AGENT_OVERRIDES);
    builder.auditInfo(pAuditInfo);
    builder.groupId(pGroupId);
    builder.hidden(true);
    final String reasonText =
        String.format(
            "Set monitoring agent hostnames: %s, TTL: %d minutes",
            String.join(", ", pHostnames), pTTLMinutes.intValue());
    builder.reason(reasonText);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}/gcpPSCRegionGroupCIDRRange")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateGCPPSCNatSubnetRange.POST")
  public Response updateGCPPSCNatSubnetRange(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("gcpPSCRegionGroupCIDRRange") final String pGCPPSCRegionGroupCIDRRange)
      throws SvcException {
    if (!NetUtils.isValidCidrNotation(pGCPPSCRegionGroupCIDRRange)) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "CIDR block");
    }

    _gcpPrivateServiceConnectSvc.setGCPPSCCIDRBlock(pGroupId, pGCPPSCRegionGroupCIDRRange);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateGroup.POST")
  public Response updateGroup(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("planASAP") final boolean pPlanASAP,
      @FormParam("healthCheckASAP") final boolean pHealthCheckASAP,
      @FormParam("publishConfigNow") final boolean pPublishConfigNow,
      @FormParam("clearFailedPlansCount") final boolean pClearFailedPlansCount) {
    if (pPlanASAP) {
      _ndsGroupDao.setPlanASAP(pGroupId);
      _searchAdminSvc.planASAP(pGroupId);
      final Builder builder = new Builder(NDSAudit.Type.PLAN_ASAP_REQUESTED);
      builder.auditInfo(pAuditInfo);
      builder.groupId(pGroupId);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());
    }
    if (pHealthCheckASAP) {
      _ndsGroupDao.setHealthCheckASAP(pGroupId);
      _searchAdminSvc.healthCheckASAP(pGroupId);
    }
    if (pPublishConfigNow) {
      _ndsGroupDao.setNeedsPublishForGroup(pGroupId, new Date());
      _ndsGroupDao.setPlanASAP(pGroupId);

      final Builder builder = new Builder(NDSAudit.Type.GROUP_AUTOMATION_CONFIG_PUBLISHED);
      builder.auditInfo(pAuditInfo);
      builder.groupId(pGroupId);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());
    }
    if (pClearFailedPlansCount) {
      try {
        _ndsGroupDao.clearFailedPlansCount(pGroupId);
      } catch (final SvcException pE) {
        return Response.status(400).build();
      }

      final Builder builder = new Builder(NDSAudit.Type.PLAN_FAILURE_COUNT_RESET);
      builder.auditInfo(pAuditInfo);
      builder.groupId(pGroupId);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());
    }
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/logs")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLogs.GET")
  public Response getLogs(
      @Context final Group pGroup,
      @Context final HttpServletRequest pRequest,
      @QueryParam("lookupWithinMillis") final Long pLookupWithinMillis,
      @QueryParam("lookupWithinMostRecentLines") final Long pLookupWithinMostRecentLines,
      @QueryParam("planId") final ObjectId pPlanId)
      throws Exception {
    final StreamingOutput result =
        out -> {
          // Default to 24 hours if not specified
          final long startTimestamp =
              System.currentTimeMillis()
                  - (pLookupWithinMillis == null
                      ? TimeUtils2.MILLISECONDS_PER_DAY
                      : pLookupWithinMillis);
          // Default to 500 lines if not specified
          final long defaultNumMostRecentLines = 500;
          final long numMostRecentLines =
              pLookupWithinMostRecentLines == null
                  ? defaultNumMostRecentLines
                  : pLookupWithinMostRecentLines;
          try (final DBCursor cursor =
              _ndsUISvc.getLogsForGroup(
                  pGroup.getId(), startTimestamp, numMostRecentLines, pPlanId)) {
            while (cursor.hasNext()) {
              final String entry = NDSLogDao.formatLog((BasicDBObject) cursor.next());
              out.write(entry.getBytes());
              out.write("\n".getBytes());
            }
          }
        };

    return Response.ok(result).build();
  }

  @GET
  @Path("/groups/{groupId}/appLogs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getProjectLogs.GET")
  public Response getProjectLogs(
      @Context final Group pGroup,
      @QueryParam("planId") final String pPlanId,
      @QueryParam("ts") final Long pTimestamp,
      @QueryParam("before") final Boolean pBefore,
      @QueryParam("bound") final Long pBound,
      @QueryParam("n") final Integer pLines) {
    return Response.ok()
        .entity(
            findLogs(
                    pGroup.getId(),
                    pPlanId,
                    ofNullable(pTimestamp).map(Date::new).orElse(null),
                    pBefore,
                    ofNullable(pBound).map(Date::new).orElse(null),
                    pLines)
                .stream()
                .map(NDSLogView::new)
                .collect(Collectors.toList()))
        .build();
  }

  private List<NDSLog> findLogs(
      final ObjectId pGroupId,
      final String pPlanId,
      final Date pTimestamp,
      final Boolean pBefore,
      final Date pBound,
      final Integer pLines) {
    final boolean before = ofNullable(pBefore).orElse(false);
    if (pPlanId == null || pPlanId.isEmpty()) {
      if (before) {
        return _ndsLogSvc.findLogsForGroupBefore(pGroupId, pTimestamp, pBound, pLines);
      } else {
        return _ndsLogSvc.findLogsForGroupAfter(pGroupId, pTimestamp, pBound, pLines);
      }
    } else if (ObjectId.isValid(pPlanId)) {
      final ObjectId planObjectId = new ObjectId(pPlanId);
      if (before) {
        return _ndsLogSvc.findLogsForPlanBefore(pGroupId, planObjectId, pTimestamp, pBound, pLines);
      } else {
        return _ndsLogSvc.findLogsForPlanAfter(pGroupId, planObjectId, pTimestamp, pBound, pLines);
      }
    } else {
      return Collections.emptyList();
    }
  }

  @GET
  @Path("/groups/{groupId}/containers/{containerId}/zoneMappings")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getZonesMappings.GET")
  public Response getZonesMappings(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("containerId") final ObjectId pContainerId)
      throws SvcException {

    final NDSGroup ndsGroup =
        _ndsGroupSvc.find(pGroupId).orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));

    final CloudProviderContainer container =
        ndsGroup
            .getCloudProviderContainer(pContainerId)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));

    final JSONObject response = new JSONObject();
    if (container.getCloudProvider().equals(CloudProvider.AWS)) {
      final AWSCloudProviderContainer awsContainer = (AWSCloudProviderContainer) container;

      final Map<String, String> zoneMap =
          _awsApiSvc
              .findAvailabilityZones(awsContainer.getAWSAccountId(), awsContainer.getRegion(), LOG)
              .stream()
              .collect(
                  Collectors.toMap(AvailabilityZone::getZoneName, AvailabilityZone::getZoneId));

      final List<JSONObject> zones =
          Arrays.stream(awsContainer.getSubnets())
              .map(
                  awsSubnet ->
                      new JSONObject()
                          .put("zoneName", awsSubnet.getAvailabilityZone())
                          .put(
                              "zoneId", zoneMap.getOrDefault(awsSubnet.getAvailabilityZone(), null))
                          .put("enabled", awsSubnet.isEnabled())
                          .put("subnetId", awsSubnet.getSubnetId()))
              .toList();
      response.put("zones", zones);

    } else {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    return Response.ok(response.toString()).build();
  }

  @POST
  @Path("/groups/{groupId}/containers/{containerId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateCloudContainer.POST")
  public Response updateCloudContainer(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("containerId") final ObjectId pContainerId,
      @FormParam("planASAP") final boolean pPlanASAP,
      @FormParam("requestSubnetsUpdate") final boolean pRequestSubnetsUpdate)
      throws SvcException {
    if (pRequestSubnetsUpdate) {
      final NDSGroup ndsGroup =
          _ndsGroupSvc
              .find(pGroupId)
              .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));
      final CloudProviderContainer container =
          ndsGroup
              .getCloudProviderContainer(pContainerId)
              .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));
      if (!CloudProvider.AWS.equals(container.getCloudProvider())) {
        throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER, container.getCloudProvider());
      }
      _ndsGroupDao.setCloudContainerFields(
          pGroupId,
          pContainerId,
          Collections.singletonList(
              Pair.of(
                  RegionalDedicatedCloudProviderContainer.FieldDefs.NEEDS_SUBNETS_UPDATE_AFTER,
                  new Date())));
      final Builder builder = new Builder(NDSAudit.Type.CONTAINER_SUBNETS_UPDATE_REQUESTED);
      builder.auditInfo(pAuditInfo);
      builder.groupId(pGroupId);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());
    }
    if (pPlanASAP) {
      _ndsGroupDao.setPlanASAP(pGroupId);
      final Builder builder = new Builder(NDSAudit.Type.PLAN_ASAP_REQUESTED);
      builder.auditInfo(pAuditInfo);
      builder.groupId(pGroupId);
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());
    }
    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/containers/{containerId}/subnets")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateContainerSubnets.PATCH")
  public Response updateContainerSubnets(
      @Context final NDSGroup pNDSGroup,
      @PathParam("containerId") final ObjectId pContainerId,
      final List<AWSSubnetView> pSubnets)
      throws SvcException {
    final CloudProviderContainer container =
        pNDSGroup
            .getCloudProviderContainer(pContainerId)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));

    if (!container.getCloudProvider().equals(CloudProvider.AWS)) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          String.format("%s is not an AWS container", pContainerId));
    }

    final AWSCloudProviderContainer awsContainer = (AWSCloudProviderContainer) container;
    final List<AWSSubnet> subnets =
        Arrays.stream(awsContainer.getSubnets())
            .map(AWSSubnetView::new)
            .map(
                s ->
                    s.copy()
                        // update enabled flag if specified
                        .enabled(
                            findSubnetViewById(pSubnets, s.getSubnetId())
                                .map(AWSSubnetView::isEnabled)
                                .orElseGet(s::isEnabled))
                        .build())
            .map(AWSSubnetView::toAWSSubnet)
            .collect(Collectors.toList());
    _awsCloudProviderContainerSvc.updateContainerSubnets(
        pNDSGroup.getGroupId(), pContainerId, subnets);
    return Response.noContent().build();
  }

  private Optional<AWSSubnetView> findSubnetViewById(
      final List<AWSSubnetView> pSubnets, final String pSubnetId) {
    return pSubnets.stream().filter(n -> pSubnetId.equals(n.getSubnetId())).findFirst();
  }

  @GET
  @Path("/groups/{groupId}/cloudProviderConsole/{hostname}/defaults")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getCloudProviderConsoleInformationDefaults.GET")
  public Response getCloudProviderConsoleInformationDefaults(
      @PathParam("groupId") final ObjectId pGroupId, @PathParam("hostname") final String pHostname)
      throws SvcException {
    final Optional<NDSGroup> ndsGroup = _ndsGroupDao.find(pGroupId);
    if (ndsGroup.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_GROUP_ID);
    }

    if (!ValidationUtils.isValidHostName(pHostname)) {
      throw new SvcException(NDSErrorCode.INVALID_HOST_NAME);
    }

    final CloudProvider provider = _cloudProviderConsoleSvc.findCloudProvider(pGroupId, pHostname);

    if (!_cloudProviderConsoleSvc.isSupportedCloudProvider(provider)) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }

    final NDSCloudProviderConsoleInformationDefaultsView output =
        new NDSCloudProviderConsoleInformationDefaultsView.Builder(pHostname, provider)
            .setAvailableTypes(_cloudProviderConsoleSvc.getAvailableTypes())
            .setAvailableMetrics(_cloudProviderConsoleSvc.getAvailableMetrics(provider))
            .build();

    return Response.ok(output).build();
  }

  @POST
  @Path("/groups/{groupId}/cloudProviderConsole/{hostname}.{extension:json|tar.gz}")
  @Produces({MediaType.APPLICATION_JSON, "application/gzip"})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getCloudProviderConsoleInformation.POST")
  public Response getCloudProviderConsoleInformation(
      @Context final HttpServletResponse pHttpServletResponse,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostname") final String pHostname,
      @PathParam("extension") final String pExtension,
      final NDSCloudProviderConsoleInformationRequestView
          pNDSCloudProviderConsoleInformationRequestView)
      throws SvcException, IOException {
    if (!ValidationUtils.isValidHostName(pHostname)) {
      throw new SvcException(NDSErrorCode.INVALID_HOST_NAME);
    }

    pNDSCloudProviderConsoleInformationRequestView.validate();

    final NDSGroup ndsGroup =
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final LocalDateTime startDate =
        LocalDateTime.ofEpochSecond(
            pNDSCloudProviderConsoleInformationRequestView.getStartTime(), 0, ZoneOffset.UTC);
    final LocalDateTime endDate =
        LocalDateTime.ofEpochSecond(
            pNDSCloudProviderConsoleInformationRequestView.getEndTime(), 0, ZoneOffset.UTC);

    // if extension == tar.gz, return tar.gz of:
    //   1 - METRICS - CSV files containing metrics for specified timeframe
    //   2 - EVENTS - JSON file containing activity feed log events for specified timeframe
    //   3 - ALL - 1 and 2 combined
    if (FileExtension.TAR_GZ.equals(pExtension)) {
      final String archiveName =
          String.format(
              "%s_%s_%s_cloud",
              pHostname,
              startDate.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME).replace(":", "_"),
              endDate.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME).replace(":", "_"));
      // The header must be written to the pHttpServletResponse instead of the Response object
      // created below since we actually write the cloud provider console information to the
      // pHttpServletResponse's outputStream in the underlying method.
      pHttpServletResponse.addHeader(
          "Content-Disposition",
          String.format("attachment; filename=\"%s.%s\"", archiveName, FileExtension.TAR_GZ));

      auditCloudProviderConsoleInformationAccess(
          pAuditInfo,
          pGroupId,
          pNDSCloudProviderConsoleInformationRequestView.getType(),
          pHostname);

      return Response.ok(
              _cloudProviderConsoleSvc.getCloudProviderConsoleInformationForDates(
                  archiveName,
                  startDate,
                  endDate,
                  ndsGroup,
                  pHostname,
                  pNDSCloudProviderConsoleInformationRequestView.getType(),
                  pNDSCloudProviderConsoleInformationRequestView.getMetrics(),
                  pHttpServletResponse.getOutputStream()))
          .build();
    }

    // if extension == json, return json of:
    //   1 - EVENTS - JSON file containing activity feed log events for specified timeframe
    //   error if METRICS or ALL is specified
    if (FileExtension.JSON.equals(pExtension)) {
      if (!NDSCloudProviderConsoleInformationType.EVENTS.equals(
          pNDSCloudProviderConsoleInformationRequestView.getType())) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "type");
      }

      auditCloudProviderConsoleInformationAccess(
          pAuditInfo,
          pGroupId,
          pNDSCloudProviderConsoleInformationRequestView.getType(),
          pHostname);

      return Response.ok(
              _cloudProviderConsoleSvc.getEventsForDates(startDate, endDate, ndsGroup, pHostname))
          .build();
    }

    throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "extension");
  }

  private void auditCloudProviderConsoleInformationAccess(
      final AuditInfo pAuditInfo,
      final ObjectId pGroupId,
      final NDSCloudProviderConsoleInformationType pNDSCloudProviderConsoleInformationType,
      final String pHostname) {
    final ServerAudit.Builder auditBuilder =
        new ServerAudit.Builder(
            HostEvent.Type.NDS_CLOUD_PROVIDER_CONSOLE_INFORMATION_DOWNLOADED, pGroupId, null);
    auditBuilder.auditInfo(pAuditInfo);
    auditBuilder.createdAt(new Date());
    auditBuilder.hidden(true);
    auditBuilder.reason(
        String.format("%s for %s", pNDSCloudProviderConsoleInformationType.name(), pHostname));
    final ServerAudit audit = auditBuilder.build();
    _auditSvc.saveAuditEvent(audit);
  }

  @GET
  @Path("/migrationToolLogs/{liveImportId}.zip")
  @Produces("application/zip")
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMigrationToolLogs.GET")
  public Response getMigrationToolLogs(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("liveImportId") final ObjectId pLiveImportId)
      throws SvcException {
    final LiveImport liveImport =
        _liveImportSvc
            .getLiveImport(pLiveImportId)
            .orElseThrow(() -> new SvcException(LiveImportErrorCode.NOT_FOUND, pLiveImportId));

    if (_authzSvc.isPiiAuditable(pUser)) {
      final AccessAuditEvent.Builder builder =
          new AccessAuditEvent.Builder(Type.ATLAS_MIGRATION_LOGS);
      builder.auditInfo(pAuditInfo);
      builder.roles(pUser.getRoles());
      builder.groupId(liveImport.getGroupId());
      final AccessAuditEvent piiAudit = builder.build();
      _auditSvc.saveAuditEvent(piiAudit);
    } else {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }

    final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH-mm-ss'Z'");
    final String liveImportCreateDateFormatted = dateFormat.format(liveImport.getCreateDate());
    final String downloadFilename =
        String.format(
            "liveimport_%s_%s_%s.%s",
            liveImport.getDestination().getClusterName(),
            liveImport.getId(),
            liveImportCreateDateFormatted,
            FileExtension.ZIP);

    final StreamingOutput output =
        out -> {
          try (final ZipArchiveOutputStream zipOut =
              new ZipArchiveOutputStream(new BufferedOutputStream(out))) {
            _liveImportSvc.getLogStreamForLiveImport(liveImport, zipOut);
          } catch (final Exception ex) {
            LOG.error("Error generating archive for live import {}", liveImport.getId(), ex);
          }
        };

    return Response.ok(output)
        .header(
            "Content-Disposition", String.format("attachment; filename=\"%s\"", downloadFilename))
        .build();
  }

  @GET
  @Path("/tenantUpgrades/{tenantUpgradeId}/downloadUrl")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantUpgradeSnapshotDownloadUrl.GET")
  public Response getTenantUpgradeSnapshotDownloadUrl(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("tenantUpgradeId") final ObjectId pTenantUpgradeId)
      throws SvcException {
    final String downloadUrl =
        _tenantUpgradeSvc.getTenantUpgradeSnapshotDownloadUrl(
            pTenantUpgradeId, null, pUser, pAuditInfo);

    return Response.ok(downloadUrl).build();
  }

  // Note: `clusterName` can be passed in as a query param for situations where the target cluster
  // for the snapshot is not the target cluster defined in the tenant upgrade status document.
  @GET
  @Path("/tenantUpgradesToServerless/{tenantUpgradeToServerlessId}/downloadUrl")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantUpgradeToServerlessSnapshotDownloadUrl.GET")
  public Response getTenantUpgradeToServerlessSnapshotDownloadUrl(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("tenantUpgradeToServerlessId") final ObjectId pTenantUpgradeToServerlessId,
      @QueryParam("clusterName") final String pClusterName)
      throws SvcException {
    final String downloadUrl =
        _tenantUpgradeToServerlessSvc.getTenantUpgradeSnapshotDownloadUrl(
            pTenantUpgradeToServerlessId, pClusterName, pUser, pAuditInfo);

    return Response.ok(downloadUrl).build();
  }

  @GET
  @Path("/serverlessUpgradesToDedicated/{serverlessUpgradeToDedicatedId}/downloadUrl")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessUpgradeToDedicatedSnapshotDownloadUrl.GET")
  public Response getServerlessUpgradeToDedicatedSnapshotDownloadUrl(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("serverlessUpgradeToDedicatedId") final ObjectId pServerlessUpgradeToDedicatedId,
      @QueryParam("clusterName") final String pClusterName)
      throws SvcException {
    final String downloadUrl =
        _serverlessUpgradeToDedicatedSvc.getTenantUpgradeSnapshotDownloadUrl(
            pServerlessUpgradeToDedicatedId, pClusterName, pUser, pAuditInfo);

    return Response.ok(downloadUrl).build();
  }

  @PATCH
  @Path(
      "/serverlessUpgradeToDedicated/{serverlessUpgradeToDedicatedId}/enableForcedUpgradeRetryOverride")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.enableForcedUpgradeRetryOverride.PATCH")
  public Response enableForcedUpgradeRetryOverride(
      @PathParam("serverlessUpgradeToDedicatedId") final ObjectId pServerlessUpgradeToDedicatedId) {
    if (!_serverlessUpgradeToDedicatedSvc.enableForcedUpgradeRetryOverrideForCluster(
        pServerlessUpgradeToDedicatedId)) {
      LOG.error(
          "Failed to enable forced upgrade retry override for serverless upgrade to dedicated"
              + " status with ID {}",
          pServerlessUpgradeToDedicatedId);
      return Response.serverError().build();
    }
    LOG.info(
        "Enabled forced upgrade retry override for serverless upgrade to dedicated status with ID"
            + " {}",
        pServerlessUpgradeToDedicatedId);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  // Note: This request from the Admin UI bypasses the eligibility criteria for automatic forced
  // upgrades. It is primarily used to submit an upgrade to tenants who were eligible due to
  // private endpoints which were deleted on the first upgrade attempt and are not eligible
  // based on data size.
  @POST
  @Path("/serverlessUpgradeToDedicated/{tenantUniqueId}/forcedUpgrade")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.scheduleForcedServerlessUpgradeToDedicated.POST")
  public Response scheduleForcedServerlessUpgradeToDedicated(
      @PathParam("tenantUniqueId") final ObjectId pTenantUniqueId) {
    final Optional<ClusterDescription> cdOpt =
        _clusterDescriptionDao.findByUniqueId(null, pTenantUniqueId);
    if (cdOpt.isEmpty()) {
      LOG.info(
          "Cluster description with unique id {} not found. Assuming"
              + " deleted or being upgraded.",
          pTenantUniqueId);
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    final ClusterDescription cd = cdOpt.get();

    if (!cd.isServerlessTenantCluster()) {
      LOG.info(
          "Cluster description with unique id {} is not a Serverless instance. Will not attempt to"
              + " force upgrade.",
          pTenantUniqueId);
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    if (cd.isDeletedOrDeleteRequested()) {
      LOG.warn(
          "Serverless instance with unique id {} is being requested for"
              + " deletion or deleted. Will not attempt to force upgrade.",
          pTenantUniqueId);
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    final Map<ObjectId, Long> serverlessTenantsDataSizes =
        _serverlessMetricsSvc.getDataSizeInBytesForTenants(List.of(pTenantUniqueId), Instant.now());

    if (!serverlessTenantsDataSizes.containsKey(pTenantUniqueId)) {
      LOG.warn(
          "Serverless instance with unique id {} has no data size information available. Will not"
              + " attempt to force upgrade.",
          pTenantUniqueId);
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    if (!_serverlessUpgradeToDedicatedSvc.attemptScheduleForcedUpgrade(
        cd, serverlessTenantsDataSizes.get(pTenantUniqueId))) {
      return Response.serverError().build();
    }
    return Response.ok().build();
  }

  @POST
  @Path("/tenantUpgradesToServerless/{tenantUpgradeToServerlessId}/retryRestoreJob")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.retryTenantUpgradeToServerlessRestore.POST")
  public Response retryTenantUpgradeToServerlessRestore(
      @PathParam("tenantUpgradeToServerlessId") final ObjectId pTenantUpgradeToServerlessId)
      throws SvcException {
    final TenantUpgradeToServerlessStatus status =
        _tenantUpgradeToServerlessSvc.setTenantUpgradeInProgressAndValidateFailure(
            pTenantUpgradeToServerlessId);
    final ClusterDescription serverlessInstanceClusterDescription =
        _clusterSvc
            .getMergedClusterDescription(status.getGroupId(), status.getClusterName())
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.CLUSTER_NOT_FOUND,
                        status.getClusterName(),
                        status.getGroupId()));

    final TenantUpgradeToServerlessBackupSnapshot backupSnapshot =
        _tenantUpgradeToServerlessSvc.constructBackupSnapshot(
            status,
            status.getClusterName(),
            status
                .getSourceTenantDeploymentName()
                .orElseThrow(
                    () ->
                        new SvcException(
                            TenantUpgradeErrorCode.SERVERLESS_UPGRADE_RETRY_INVALID,
                            status.getId(),
                            "Cannot retry due to missing source tenant deployment name")),
            status.getSourceTenantUniqueId(),
            serverlessInstanceClusterDescription.getRegionName(),
            serverlessInstanceClusterDescription.getBackingProvider(),
            Version.fromString(
                status
                    .getSourceMongoDbVersion()
                    .orElseThrow(
                        () ->
                            new SvcException(
                                TenantUpgradeErrorCode.SERVERLESS_UPGRADE_RETRY_INVALID,
                                status.getId(),
                                "Cannot retry due to missing source MongoDB Version"))),
            Version.fromString(
                status
                    .getSourceFcvMongoDbVersion()
                    .orElseThrow(
                        () ->
                            new SvcException(
                                TenantUpgradeErrorCode.SERVERLESS_UPGRADE_RETRY_INVALID,
                                status.getId(),
                                "Cannot retry due to missing source FCV"))));

    final Group group = _groupSvc.findById(status.getGroupId());
    try {
      final ObjectId restoreJobId =
          _cpsSvc.createTenantUpgradeToServerlessRestore(
              group, serverlessInstanceClusterDescription, backupSnapshot, AppUser.SYSTEM_USER);

      LOG.info("Created tenant upgrade to serverless restore job with ID {}", restoreJobId);
      _ndsGroupSvc.setPlanningNow(group.getId());
    } catch (final SvcException pE) {
      LOG.error(
          "Caught exception when trying to create a tenant upgrade to serverless restore job");
      throw new IllegalArgumentException(pE.getMessage(), pE);
    }
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  private void authenticateTenantUpgradeLogAccess(
      final AuditInfo pAuditInfo,
      final AppUser pUser,
      final BaseTenantUpgradeStatus pTenantUpgradeStatus,
      final LogType pType)
      throws SvcException {
    final Type eventType =
        pType == LogType.MONGODUMP ? Type.ATLAS_MONGODUMP_LOGS : Type.ATLAS_MONGORESTORE_LOGS;
    if (_authzSvc.isPiiAuditable(pUser)) {
      final AccessAuditEvent.Builder builder = new AccessAuditEvent.Builder(eventType);
      builder.auditInfo(pAuditInfo);
      builder.roles(pUser.getRoles());
      builder.groupId(pTenantUpgradeStatus.getGroupId());
      final AccessAuditEvent piiAudit = builder.build();
      _auditSvc.saveAuditEvent(piiAudit);
    } else {
      throw new SvcException(CommonErrorCode.NO_AUTHORIZATION);
    }
  }

  private StreamingOutput getLogFilesFromCursor(DBCursor pCursor, LogType pLogType) {
    return out -> {
      try {
        while (pCursor.hasNext()) {
          final String logChunk;
          if (pLogType.equals(LogType.RESTOREJOB)) {
            logChunk = TenantUpgradeLogDao.formatLogForRestoreJob((BasicDBObject) pCursor.next());
          } else {
            logChunk = TenantUpgradeLogDao.formatLog((BasicDBObject) pCursor.next());
          }
          out.write(logChunk.getBytes());
          out.write("\n".getBytes());
        }
      } catch (final Exception pE) {
        LOG.error("An error occurred while getting tenant upgrade log files", pE);
      }
    };
  }

  @GET
  @Path("/tenantUpgradesToServerless/{tenantUpgradeToServerlessId}/logs")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantUpgradeToServerlessLogs.GET")
  public Response getTenantUpgradeToServerlessLogs(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("tenantUpgradeToServerlessId") final ObjectId pTenantUpgradeId,
      @QueryParam("logType") final String pLogType)
      throws SvcException {
    final LogType type =
        LogType.findByValue(pLogType.toUpperCase())
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.INVALID_PARAMETER,
                        "Invalid log type: " + pLogType.toUpperCase()));

    if (!LogType.isValidServerlessLogType(type)) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          "Invalid log type for tenant upgrade to serverless: " + type.name());
    }

    final TenantUpgradeToServerlessStatus tenantUpgradeStatus =
        _tenantUpgradeToServerlessSvc
            .getTenantUpgrade(pTenantUpgradeId)
            .orElseThrow(
                () -> new SvcException(TenantUpgradeErrorCode.NOT_FOUND, pTenantUpgradeId));

    authenticateTenantUpgradeLogAccess(pAuditInfo, pUser, tenantUpgradeStatus, type);

    // Initialize the cursor outside of the streaming output in order to surface the SvcException.
    final DBCursor cursor;
    if (type.equals(LogType.MONGODUMP)) {
      cursor =
          _tenantUpgradeToServerlessSvc.getLogStreamForTenantUpgrade(tenantUpgradeStatus, type);
    } else {
      cursor =
          _tenantUpgradeToServerlessSvc.getRestoreJobForTenantUpgradeToServerless(
              tenantUpgradeStatus, type);
    }
    return Response.ok(getLogFilesFromCursor(cursor, type)).build();
  }

  @GET
  @Path("/tenantUpgrades/{tenantUpgradeId}/logs")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantUpgradeLogs.GET")
  public Response getTenantUpgradeLogs(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("tenantUpgradeId") final ObjectId pTenantUpgradeId,
      @QueryParam("logType") final String pLogType)
      throws SvcException {
    final LogType type =
        LogType.findByValue(pLogType.toUpperCase())
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.INVALID_PARAMETER,
                        "Invalid log type: " + pLogType.toUpperCase()));
    final TenantUpgradeStatus tenantUpgradeStatus =
        _tenantUpgradeSvc
            .getTenantUpgrade(pTenantUpgradeId)
            .orElseThrow(
                () -> new SvcException(TenantUpgradeErrorCode.NOT_FOUND, pTenantUpgradeId));

    authenticateTenantUpgradeLogAccess(pAuditInfo, pUser, tenantUpgradeStatus, type);

    // Initialize the cursor outside of the streaming output in order to surface the SvcException.
    final DBCursor cursor =
        _tenantUpgradeSvc.getLogStreamForTenantUpgrade(tenantUpgradeStatus, type);

    return Response.ok(getLogFilesFromCursor(cursor, type)).build();
  }

  @GET
  @Path("/serverlessUpgradesToDedicated/{serverlessUpgradeToDedicatedId}/logs")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessUpgradeToDedicatedLogs.GET")
  public Response getServerlessUpgradeToDedicatedLogs(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("serverlessUpgradeToDedicatedId") final ObjectId pServerlessUpgradeToDedicatedId,
      @QueryParam("logType") final String pLogType)
      throws SvcException {
    final LogType type =
        LogType.findByValue(pLogType.toUpperCase())
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.INVALID_PARAMETER,
                        "Invalid log type: " + pLogType.toUpperCase()));
    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        _serverlessUpgradeToDedicatedSvc
            .getTenantUpgrade(pServerlessUpgradeToDedicatedId)
            .orElseThrow(
                () ->
                    new SvcException(
                        TenantUpgradeErrorCode.NOT_FOUND, pServerlessUpgradeToDedicatedId));

    authenticateTenantUpgradeLogAccess(pAuditInfo, pUser, serverlessUpgradeToDedicatedStatus, type);

    final DBCursor cursor =
        _serverlessUpgradeToDedicatedSvc.getLogStreamForTenantUpgrade(
            serverlessUpgradeToDedicatedStatus, type);

    return Response.ok(getLogFilesFromCursor(cursor, type)).build();
  }

  @GET
  @Path("/serverlessDowngradeToFree/{serverlessDowngradeToFreeId}/logs")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessDowngradeToFreeLogs.GET")
  public Response getServerlessDowngradeToFreeLogs(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("serverlessDowngradeToFreeId") final ObjectId pServerlessDowngradeToFreeId,
      @QueryParam("logType") final String pLogType)
      throws SvcException {
    final LogType type =
        LogType.findByValue(pLogType.toUpperCase())
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.INVALID_PARAMETER,
                        "Invalid log type: " + pLogType.toUpperCase()));
    final ServerlessFreeMigrationStatus serverlessFreeMigrationStatus =
        _serverlessFreeMigrationStatusSvc
            .getTenantUpgrade(pServerlessDowngradeToFreeId)
            .orElseThrow(
                () ->
                    new SvcException(
                        TenantUpgradeErrorCode.NOT_FOUND, pServerlessDowngradeToFreeId));

    authenticateTenantUpgradeLogAccess(pAuditInfo, pUser, serverlessFreeMigrationStatus, type);

    final DBCursor cursor =
        _serverlessFreeMigrationStatusSvc.getLogStreamForTenantUpgrade(
            serverlessFreeMigrationStatus, type);

    return Response.ok(getLogFilesFromCursor(cursor, type)).build();
  }

  @POST
  @Path("/groups/{groupId}/agents/{agentType}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fixAgentVersion.POST")
  public Response fixAgentVersion(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("agentType") final String pAgentType,
      @FormParam("version") final String pVersion,
      @FormParam("baseUrl") final String pBaseUrl,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    if (!ModelValidationUtils.isValidAgentType(pAgentType)) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "Invalid agent type");
    }
    final AgentType agentType = AgentType.valueOf(pAgentType);
    if (!(agentType == AgentType.AUTOMATION
            || agentType == AgentType.MONGOT
            || agentType == AgentType.ATLAS_PROXY
            || agentType == AgentType.SERVERLESS_PROXY
            || agentType == AgentType.USER_IDENTITY_SERVICE
            || agentType == AgentType.SEARCH_ENVOY
            || agentType == AgentType.SERVERLESS
            || agentType == AgentType.MONGOTUNE)
        && StringUtils.isNotBlank(pBaseUrl)) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR,
          "Base URL only valid for MongoDB Agent, MongoT, Atlas Proxy, Serverless Proxy, User"
              + " Identity Service, Search Envoy, Serverless Agent, or Mongotune");
    }

    final String baseUrl = sanitizeBaseUrl(pBaseUrl);

    validateReason(pReason);
    final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);
    _ndsUISvc.fixAgentVersion(
        pGroupId, agentType, pVersion, baseUrl, pReason, expirationDate, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/groups/{groupId}/agents/{agentType}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unfixAgentVersion.DELETE")
  public Response unfixAgentVersion(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("agentType") final String pAgentType)
      throws SvcException {
    if (!ModelValidationUtils.isValidAgentType(pAgentType)) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "Invalid agent type");
    }

    final AgentType agentType = AgentType.valueOf(pAgentType);
    _ndsUISvc.unfixAgentVersion(pGroupId, agentType, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/agents/{agentType}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateFixedAgentVersion.PATCH")
  public Response updateFixedAgentVersion(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("agentType") final String pAgentType,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    if (!ModelValidationUtils.isValidAgentType(pAgentType)) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "Invalid agent type");
    }

    final AgentType agentType = AgentType.valueOf(pAgentType);

    final FixedAgentVersion fixedAgentVersion =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID))
            .getFixedAgentVersion(agentType)
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.BAD_REQUEST,
                        String.format(
                            "%s is not fixed for group with id %s",
                            agentType.getFullName(), pGroupId.toString())));

    final FixedAgentVersion.Builder builder = fixedAgentVersion.toBuilder();
    FixedAgentVersion updatedFixedAgentVersion =
        (FixedAgentVersion) getUpdatedFixedVersion(builder, pUser, pReason, pExpirationDate);

    if (!fixedAgentVersion.equals(updatedFixedAgentVersion)) {
      _ndsGroupSvc.updateFixedAgentVersion(
          pGroupId,
          updatedFixedAgentVersion,
          fixedAgentVersion.getUpdatesList(updatedFixedAgentVersion),
          pAuditInfo);
    }

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/groups/agents")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFixedAgentVersions.GET")
  public Response getFixedAgentVersions() {
    return Response.ok(_ndsUISvc.getFixedAgentVersions()).build();
  }

  @GET
  @Path("/groups/capacityFailures")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getGroupCapacityFailures.GET")
  public Response getGroupCapacityFailures() {
    return Response.ok(_ndsUISvc.getGroupCapacityFailures()).build();
  }

  @PUT
  @Path("/{provider}/denyListEntries")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setDenyListEntryOverride.PUT")
  public Response setDenyListEntryOverride(
      @PathParam("provider") final CloudProvider pCloudProvider,
      final DenyListEntryCreateView pDenyListEntryCreateView)
      throws Exception {
    try {
      _ndsUISvc.registerDenyListEntryOverride(
          pCloudProvider,
          pDenyListEntryCreateView.region(),
          pDenyListEntryCreateView.instanceFamily(),
          pDenyListEntryCreateView.instanceSize(),
          pDenyListEntryCreateView.override(),
          pDenyListEntryCreateView.zone(),
          pDenyListEntryCreateView.jira());
    } catch (final IllegalArgumentException e) {
      throw new SvcException(CommonErrorCode.BAD_REQUEST, e.getMessage());
    }

    return Response.ok().build();
  }

  @GET
  @Path("/{provider}/denyListEntries")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDenylistEntries.GET")
  public Response getDenylistEntries(@PathParam("provider") final CloudProvider pCloudProvider)
      throws Exception {
    return Response.ok(
            switch (pCloudProvider) {
              case AWS -> _ndsUISvc.getAWSDenyListEntries();
              case AZURE -> _ndsUISvc.getAzureDenyListEntries();
              case GCP -> _ndsUISvc.getCollapsedGCPDenyListEntries();
              default -> throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
            })
        .build();
  }

  @DELETE
  @Path("/{provider}/denyListEntries")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteDenyListEntry.DELETE")
  public Response deleteDenyListEntry(
      @PathParam("provider") final CloudProvider pCloudProvider,
      final DenyListEntryDeleteView pDenyListEntryDeleteView)
      throws Exception {
    _ndsUISvc.removeDenyListEntries(
        pCloudProvider,
        pDenyListEntryDeleteView.region(),
        pDenyListEntryDeleteView.instanceFamily(),
        pDenyListEntryDeleteView.instanceSize(),
        pDenyListEntryDeleteView.zone());
    return Response.ok().build();
  }

  @GET
  @Path("/versions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY, RoleSet.GLOBAL_VERSION_MANAGER_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getNDSVersions.GET")
  public Response getNDSVersions() {
    return Response.ok(_ndsUISvc.getAllNDSVersions()).build();
  }

  @PATCH
  @Path("/versions")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateNDSVersions.PATCH")
  public Response updateNDSVersions(
      @Context final AuditInfo pAuditInfo, final NDSVersionUpdateView pNDSVersionUpdateView)
      throws Exception {
    final NDSVersionView updatedNDSVersionView =
        _ndsUISvc.updateNDSVersion(pNDSVersionUpdateView, pAuditInfo);

    return Response.ok(updatedNDSVersionView).build();
  }

  @PATCH
  @Path("/versions/phased")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = RoleSet.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN,
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updatePhasedVersions.PATCH")
  public Response updatePhasedVersions(
      @Context final AuditInfo pAuditInfo, final NDSVersionUpdateView pNDSVersionUpdateView)
      throws Exception {
    _ndsUISvc.updateNDSVersion(pNDSVersionUpdateView, pAuditInfo);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/internalMaintenanceRollouts")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = RoleSet.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN,
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getInternalMaintenanceRolloutForToVersionAndMaintenanceType.GET")
  public Response getInternalMaintenanceRolloutForToVersionAndMaintenanceType(
      @QueryParam("toVersion") final String pToVersion,
      @QueryParam("maintenanceType") final MaintenanceType pMaintenanceType) {
    final NDSInternalMaintenanceRolloutView ndsInternalMaintenanceRolloutView =
        _ndsInternalMaintenanceRolloutSvc
            .findByToVersionAndMaintenanceType(pToVersion, pMaintenanceType)
            .map(NDSInternalMaintenanceRolloutView::new)
            .orElse(null);

    if (ndsInternalMaintenanceRolloutView == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    return Response.ok(ndsInternalMaintenanceRolloutView).build();
  }

  @PATCH
  @Path("/versions/cancel")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = RoleSet.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN,
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.cancelCriticalMaintenanceJob.PATCH")
  public Response cancelCriticalMaintenanceJob(final NDSVersionView pNDSVersionView)
      throws SvcException {
    // pNDSVersionView returned by front end only stores the ID of the job to be cancelled
    final CriticalMaintenanceRunChunkedJobState state =
        _criticalMaintenanceRunChunkJobStateSvc
            .findOne(pNDSVersionView.getCriticalMaintenanceRunChunkedJobState().getId())
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.CRITICAL_MAINTENANCE_STATE_NOT_FOUND,
                        pNDSVersionView.getCriticalMaintenanceRunChunkedJobState().getId()));

    _criticalMaintenanceRunChunkJobStateSvc.cancelCriticalMaintenanceJob(state);

    return Response.ok(_ndsUISvc.getNDSVersion(state.getParameters().getCriticalMaintenanceType()))
        .build();
  }

  @PATCH
  @Path("/versions/retry")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = RoleSet.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN,
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.retryCriticalMaintenanceJob.PATCH")
  public Response retryCriticalMaintenanceJob(final NDSVersionView pNDSVersionView)
      throws SvcException {
    // pNDSVersionView returned by front end only stores the ID of the job to be cancelled
    final CriticalMaintenanceRunChunkedJobState state =
        _criticalMaintenanceRunChunkJobStateSvc
            .findOne(pNDSVersionView.getCriticalMaintenanceRunChunkedJobState().getId())
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.CRITICAL_MAINTENANCE_STATE_NOT_FOUND,
                        pNDSVersionView.getCriticalMaintenanceRunChunkedJobState().getId()));

    _criticalMaintenanceRunChunkJobStateSvc.retryCriticalMaintenanceJob(state);

    return Response.ok(_ndsUISvc.getNDSVersion(state.getParameters().getCriticalMaintenanceType()))
        .build();
  }

  @GET
  @Path("/providerSettings")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getCloudProviderSettings.GET")
  public Response getCloudProviderSettings() {
    return Response.ok(_ndsUISvc.getAllCloudProviderSettings()).build();
  }

  @PATCH
  @Path("/providerSettings")
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateCloudProviderSettings.PATCH")
  public Response updateCloudProviderSettings(
      @Context final AuditInfo pAuditInfo,
      final CloudProviderSettingsView pCloudProviderSettingsView)
      throws SvcException {
    _ndsUISvc.updateCloudProviderSettings(pAuditInfo, pCloudProviderSettingsView);
    return Response.ok().build();
  }

  @PATCH
  @Path("/providerSettings/all")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateAllCloudProviderSettings.PATCH")
  public Response updateAllCloudProviderSettings(
      @Context final AuditInfo pAuditInfo, @FormParam("throttled") final Boolean pThrottled)
      throws SvcException {
    _ndsUISvc.updateAllCloudProviderSettings(pAuditInfo, pThrottled);
    return Response.ok().build();
  }

  @GET
  @Path("/{hostname}/clusterDescription")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterDescriptionForHostname.GET")
  public Response getClusterDescriptionForHostname(@PathParam("hostname") final String pHostname)
      throws SvcException {
    // Envoy, restore, and data validation hosts are not associated with a cluster
    if (NDSDefaults.isHostnameNotPartOfCluster(pHostname)
        || StreamsUtil.isVPCProxyHostname(pHostname)) {
      return Response.ok(EMPTY_JSON_OBJECT).build();
    }

    final ClusterDescription clusterDescription =
        _ndsAdminSvc.getClusterDescriptionForHostname(pHostname);

    final ClusterDescriptionView clusterDescriptionView =
        new ClusterDescriptionView(clusterDescription);

    return Response.ok(clusterDescriptionView).build();
  }

  @GET
  @Path("/{hostname}/rsId")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getRsIdFromHostname.GET")
  public Response getRsIdFromHostname(@PathParam("hostname") final String pHostname)
      throws SvcException {
    // Envoy, restore, search, and data validation hosts are not associated with a replica set
    if (NDSDefaults.isHostnameNotPartOfCluster(pHostname)
        || _searchInstanceSvc.isSearchInstanceHostname(pHostname)) {
      return Response.ok(EMPTY_JSON_OBJECT).build();
    }

    final JSONObject response = new JSONObject();
    final ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareSvc
            .getReplicaSetHardwareForHostname(pHostname, null)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_HOST_NAME));
    response.put("rsId", replicaSetHardware.getRsId());

    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/tenantRegions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantProviderRegions.GET")
  public Response getTenantProviderRegions() {
    return Response.ok(_tenantClusterConfigurationSvc.getAllAvailableSharedMTMRegionViewsForAdmin())
        .build();
  }

  @GET
  @Path("/serverless/tenantRegions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessTenantProviderRegions.GET")
  public Response getServerlessTenantProviderRegions() {
    verifyServerlessIsSupported();
    return Response.ok(
            _tenantClusterConfigurationSvc.getAllAvailableServerlessMTMRegionViewsForAdmin())
        .build();
  }

  @GET
  @Path("/fixedVersionClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFixedVersionClusters.GET")
  public Response getFixedVersionClusters() {
    final List<PinnedClusterView> fixedClusters = _ndsUISvc.getFixedVersionClusters();
    return Response.ok(fixedClusters).build();
  }

  @GET
  @Path("/fixedFeatureCompatibilityVersionClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFixedFCVClusters.GET")
  public Response getFixedFCVClusters() {
    final List<PinnedClusterView> fixedFCVClusters =
        _clusterSvc.getFixedFeatureCompatibilityVersionClusters();
    return Response.ok(fixedFCVClusters).build();
  }

  @GET
  @Path("/fixedCpuArchClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFixedCpuArchClusters.GET")
  public Response getFixedCpuArchClusters() {
    final List<PinnedClusterView> fixedClusters =
        _clusterSvc.getPinnedClusterViewsByFixedVersionType(FieldDefs.FIXED_CPU_ARCH);
    return Response.ok(fixedClusters).build();
  }

  @GET
  @Path("/fixedOsClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFixedOsClusters.GET")
  public Response getFixedOsClusters() {
    final List<PinnedClusterView> fixedClusters =
        _clusterSvc.getPinnedClusterViewsByFixedVersionType(FieldDefs.FIXED_OS);
    return Response.ok(fixedClusters).build();
  }

  @GET
  @Path("/fixedAcmeProviders")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFixedAcmeProviders.GET")
  public Response getFixedAcmeProviders() {
    final List<PinnedClusterView> fixedClusters =
        _clusterSvc.getPinnedClusterViewsByFixedVersionType(FieldDefs.FIXED_ACME_PROVIDER);
    return Response.ok(fixedClusters).build();
  }

  @POST
  @Path("/bulkPinAcmeProviders")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.bulkPinAcmeProviders.POST")
  public Response bulkPinAcmeProviders(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @FormParam("provider") final String pProvider,
      @FormParam("action") final String pAction,
      @FormParam("projectId") final List<String> pProjectIds,
      @FormParam("orgId") final List<String> pOrgIds)
      throws SvcException {
    if (Objects.equals(pAction, "pin")) {
      if (ACMEProvider.find(pProvider).isEmpty()) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT, "Invalid provider name: " + pProvider);
      }
      final ACMEProvider provider =
          pProvider.equals(ACMEProvider.GTS.name())
              ? ACMEProvider.GTS
              : ACMEProvider.LETS_ENCRYPT_V2;

      if (!pProjectIds.isEmpty()) {
        List<ObjectId> projectIds;
        try {
          projectIds = pProjectIds.stream().map(ObjectId::new).toList();
        } catch (final IllegalArgumentException e) {
          throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Invalid project ID format");
        }
        for (ObjectId id : projectIds) {
          fixAcmeForProject(id, provider, pAuditInfo);
        }
      } else if (!pOrgIds.isEmpty()) {
        List<ObjectId> orgIds;
        try {
          orgIds = pOrgIds.stream().map(ObjectId::new).toList();
        } catch (final IllegalArgumentException e) {
          throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Invalid org ID format");
        }
        for (ObjectId id : orgIds) {
          final List<Group> groupsForOrg = _groupDao.findByOrgId(id, false);
          for (Group g : groupsForOrg) {
            final ObjectId gid = g.getId();
            fixAcmeForProject(gid, provider, pAuditInfo);
          }
        }
      }
    } else if (Objects.equals(pAction, "unpin")) {
      if (!pProjectIds.isEmpty()) {
        List<ObjectId> projectIds;
        try {
          projectIds = pProjectIds.stream().map(ObjectId::new).toList();
        } catch (final IllegalArgumentException e) {
          throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Invalid project ID format");
        }
        for (ObjectId id : projectIds) {
          unfixAcmeForProject(id, pAuditInfo);
        }
      } else if (!pOrgIds.isEmpty()) {
        List<ObjectId> orgIds;
        try {
          orgIds = pOrgIds.stream().map(ObjectId::new).toList();
        } catch (final IllegalArgumentException e) {
          throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Invalid org ID format");
        }
        for (ObjectId id : orgIds) {
          final List<Group> groupsForOrg = _groupDao.findByOrgId(id, false);
          for (Group g : groupsForOrg) {
            final ObjectId gid = g.getId();
            unfixAcmeForProject(gid, pAuditInfo);
          }
        }
      }
    } else {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/preferredCpuArch")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateFixedCpuArchitecture.PATCH")
  public Response updateFixedCpuArchitecture(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    return updateClusterFixedVersion(
        pGroup.getId(),
        pClusterName,
        FixedVersionType.CPU_ARCHITECTURE,
        pReason,
        pExpirationDate,
        pUser,
        pAuditInfo);
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/preferredCpuArch")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fixCpuArchitecture.POST")
  public Response fixCpuArchitecture(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("preferredCpuArch") final String pPreferredCpuArch,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {

    validateReason(pReason);
    final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);
    _clusterSvc.fixCpuArchForCluster(
        pGroup.getId(),
        pClusterName,
        CpuArchitecture.valueOf(pPreferredCpuArch),
        pAuditInfo,
        pReason,
        expirationDate);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/preferredCpuArch")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unfixCpuArchitecture.DELETE")
  public Response unfixCpuArchitecture(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    _clusterSvc.unfixCpuArchForCluster(pGroup.getId(), pClusterName, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/os")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.fixOs.POST")
  public Response fixOs(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("os") final String pOs,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    validateReason(pReason);
    final Date expirationDate = getExpirationDateOrThrow(pExpirationDate, pUser);
    final ObjectId groupId = pGroup.getId();
    _clusterSvc.fixOsForCluster(
        groupId, pClusterName, OS.valueOf(pOs), pAuditInfo, pReason, expirationDate);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/os")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateFixedOs.PATCH")
  public Response updateFixedOs(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("reason") final String pReason,
      @FormParam("expirationDate") final long pExpirationDate)
      throws SvcException {
    return updateClusterFixedVersion(
        pGroup.getId(),
        pClusterName,
        FixedVersionType.OS,
        pReason,
        pExpirationDate,
        pUser,
        pAuditInfo);
  }

  @DELETE
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/os")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unfixOs.DELETE")
  public Response unfixOs(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "Invalid cluster name: " + pClusterName);
    }

    _clusterSvc.unfixOsForCluster(pGroup.getId(), pClusterName, pAuditInfo);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/azurePreferredStorageType")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.setAzureClusterPreferredStorageType.PATCH")
  public Response setAzureClusterPreferredStorageType(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("preferredStorageType") final String pPreferredStorageType)
      throws SvcException {
    final PreferredStorageType preferredStorageType;
    try {
      preferredStorageType =
          ofNullable(pPreferredStorageType)
              .filter(Predicate.not(String::isEmpty))
              .map(String::toUpperCase)
              .map(PreferredStorageType::valueOf)
              .orElseThrow(
                  () -> new SvcException(NDSErrorCode.INVALID_ARGUMENT, "preferredStorageType"));
    } catch (final IllegalArgumentException pE) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "preferredStorageType");
    }

    _clusterSvc.setAzurePreferredStorageTypeForCluster(
        pGroup.getId(), pClusterName, preferredStorageType, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/cancelShardDrain")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.cancelClusterShardDrain.PATCH")
  public Response cancelClusterShardDrain(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    final Optional<Date> cancelDate =
        _clusterSvc.cancelShardDrain(pGroup.getId(), pClusterName, pAuditInfo);
    if (cancelDate.isEmpty()) {
      return createNotFoundResponse();
    }

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/migrateBackToAwsManagedIp")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.migrateBackToAwsManagedIp.PATCH")
  public Response migrateBackToAwsManagedIp(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    _clusterSvc.migrateBackToAwsManagedIp(pGroup.getId(), pClusterName, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/whitelistThrottledGroups")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getWhitelistThrottledGroups.GET")
  public Response getWhitelistThrottledGroups() {
    final List<NDSThrottledCollectionView> networkPermissionThrottledGroups =
        _ndsUISvc.getNetworkPermissionThrottledGroups();
    return Response.ok(networkPermissionThrottledGroups).build();
  }

  @GET
  @Path("/blacklistThrottledGroups")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getBlacklistThrottledGroups.GET")
  public Response getBlacklistThrottledGroups() {
    final List<NDSThrottledCollectionView> blacklistedGroups =
        _ndsUISvc.getBlacklistThrottledGroups();
    return Response.ok(blacklistedGroups).build();
  }

  @GET
  @Path("/throttledClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getThrottledClusters.GET")
  public Response getThrottledClusters() {
    final List<NDSThrottledCollectionView> throttledClusters = _ndsUISvc.getAllThrottledClusters();
    return Response.ok(throttledClusters).build();
  }

  @GET
  @Path("/awsAccounts")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAWSAccounts.GET")
  public Response getAWSAccounts() {
    return Response.ok(_ndsUISvc.getAllAWSAccounts()).build();
  }

  @GET
  @Path("/gcpOrganizations")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getGCPOrganizations.GET")
  public Response getGCPOrganizations() {
    return Response.ok(_ndsUISvc.getAllGCPOrganizations()).build();
  }

  @GET
  @Path("/azureSubscriptions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAzureSubscriptions.GET")
  public Response getAzureSubscriptions() {
    return Response.ok(_ndsUISvc.getAllAzureSubscriptions()).build();
  }

  @GET
  @Path("/orphanedItems")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOrphanedItems.GET")
  public Response getOrphanedItems() {
    return Response.ok(_orphanedItemSvc.findAllOrphanedItems()).build();
  }

  @GET
  @Path("/orphanedItems/{orphanedItemId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOrphanedItemById.GET")
  public Response getOrphanedItemById(
      @PathParam("orphanedItemId") final ObjectId pOrphanedItemDocumentId) {
    final Optional<NDSOrphanedItem> orphanedItem =
        _orphanedItemSvc.findByDocumentId(pOrphanedItemDocumentId);

    if (orphanedItem.isEmpty()) {
      return createNotFoundResponse();
    }

    return Response.ok().entity(new OrphanedItemView(orphanedItem.get())).build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/adminJobs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitAdminJob.POST")
  public Response submitAdminJob(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("hostname") final String pHostname,
      @FormParam("jobType") final NDSAdminJob.Type pJobType,
      @FormParam("comment") final String comment)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    if (!ValidationUtils.isValidHostName(pHostname)) {
      throw new SvcException(NDSErrorCode.INVALID_HOST_NAME);
    }

    final Optional<NDSGroup> ndsGroup = _ndsGroupSvc.find(pGroup.getId());

    _ndsAdminJobSvc.submitAdminJob(
        pGroup.getId(), pClusterName, pHostname, pJobType, pAuditInfo, pUser, comment);
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/updateBumperFiles")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitUpdateBumperFilesAdminJob.POST")
  public Response submitUpdateBumperFilesAdminJob(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("hostname") final String pHostname,
      @FormParam("goalNumBumperFiles") final int pGoalNumBumperFiles)
      throws SvcException {
    if (_clusterSvc.getActiveCluster(pGroup.getId(), pClusterName).isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    if (pGoalNumBumperFiles < 0 || pGoalNumBumperFiles > 3) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "goalNumBumperFiles must be between 0 and 3 inclusive");
    }

    if (pGoalNumBumperFiles < 1 && !_authzSvc.isGlobalAtlasOperator(pUser)) {
      throw new SvcException(
          CommonErrorCode.NO_AUTHORIZATION,
          "You must have atlas operator privileges to remove the last bumper file");
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    _ndsAdminJobSvc.submitUpdateBumperFileAdminJob(
        pGroup.getId(), pClusterName, pHostname, pGoalNumBumperFiles, pAuditInfo);
    return Response.ok().build();
  }

  @POST
  @Path(
      "/groups/{groupId}/clusterDescriptions/{clusterName}/mongotuneAdminJob/unsetUserWriteBlockMode")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitUnsetUserWriteBlockModeAdminJob.POST")
  public Response submitUnsetUserWriteBlockModeAdminJob(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    if (!cluster.getClusterDescription().isDedicatedCluster()) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR,
          "unset user write block mode is only supported on dedicated clusters");
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    // Submit job and return the updated mongotune status
    _ndsAdminJobSvc.submitUnsetUserWriteBlockModelAgentJobAdminJobAndPlan(
        cluster.getClusterDescription(), pAuditInfo);

    return Response.ok(
            _clusterSvc
                .getActiveCluster(pGroup.getId(), pClusterName)
                .get()
                .getClusterDescription()
                .getMongotuneStatus()
                .get())
        .build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/mongotuneAdminJob/policy/{policyType}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitMongotunePolicyAdminJob.POST")
  public Response submitMongotunePolicyAdminJob(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("policyType") final PolicyType policyType,
      @FormParam("goalState") final Boolean policyEnabled)
      throws SvcException {
    final Optional<Cluster> cluster = _clusterSvc.getActiveCluster(pGroup.getId(), pClusterName);

    if (cluster.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    // Submit job and return the updated mongotune status
    _ndsAdminJobSvc.submitMongotunePolicyAdminJob(
        pGroup.getId(),
        cluster.get().getClusterDescription(),
        policyType,
        policyEnabled,
        pAuditInfo);

    return Response.ok(
            _clusterSvc
                .getActiveCluster(pGroup.getId(), pClusterName)
                .get()
                .getClusterDescription()
                .getMongotuneStatus()
                .get())
        .build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/mongotuneAdminJob/binary")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitMongotuneBinaryAdminJob.POST")
  public Response submitMongotuneBinaryAdminJob(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("goalState") final MongotuneStatus.State goalState)
      throws SvcException {
    final Optional<Cluster> cluster = _clusterSvc.getActiveCluster(pGroup.getId(), pClusterName);

    if (cluster.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    if (!isFeatureFlagEnabled(FeatureFlag.ENABLE_MONGOTUNE, _appSettings, null, pGroup)
        && goalState.isEnabled()) {
      return Response.status(Response.Status.METHOD_NOT_ALLOWED).build();
    }

    if (goalState == null) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR, "goalState must be provided for mongotune admin job");
    }
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroup.getId(), pClusterName);

    // Submit job and return the updated mongotune status
    _ndsAdminJobSvc.submitMongotuneBinaryAdminJob(
        pGroup.getId(), cluster.get().getClusterDescription(), goalState, pAuditInfo);
    return Response.ok(
            _clusterSvc
                .getActiveCluster(pGroup.getId(), pClusterName)
                .get()
                .getClusterDescription()
                .getMongotuneStatus()
                .get())
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/mongotune")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMongotuneStatus.GET")
  public Response getMongotuneStatus(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Optional<Cluster> cluster = _clusterSvc.getActiveCluster(pGroup.getId(), pClusterName);

    if (cluster.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    final ClusterDescription clusterDescription = cluster.get().getClusterDescription();

    final Optional<MongotuneStatus> clusterMongotuneStatus =
        clusterDescription.getMongotuneStatus();
    final boolean isTenantCluster = clusterDescription.isTenantCluster();
    if (isTenantCluster || clusterMongotuneStatus.isEmpty()) {
      throw new SvcException(
          CommonErrorCode.BAD_REQUEST,
          isTenantCluster
              ? "Tenant clusters do not support mongotune"
              : "Mongotune feature flag not enabled for cluster.");
    }

    final MongotuneStatus mongotuneStatus = clusterMongotuneStatus.get();
    final Map<PolicyType, PolicyStatus> policies = mongotuneStatus.policies();
    final Optional<MongotuneProcessArgs> processArgs =
        _clusterSvc
            .getProcessArgs(pGroup.getId(), pClusterName)
            .flatMap(ClusterDescriptionProcessArgs::getMongotuneArg);
    final Map<String, Map<String, Object>> mergedPolicyArgs =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs.orElse(null), policies, cluster.get(), null);

    // Response includes both the mongotune status from the cluster description and the merged
    // policy args
    return Response.ok(
            new MongotuneStatusWithPolicyArgsAdminView(
                mongotuneStatus.toDBObject(), mergedPolicyArgs))
        .build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/mongotune/updateWriteBlockStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateWriteBlockStatus.PATCH")
  public Response updateWriteBlockStatus(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    // Uses the last ping data for each host in the cluster to update whether writes are
    // blocked. This should happen automatically via log ingestion, but this endpoint can be used
    // to refresh the state on the cluster description in case state inconsistency arises without
    // requiring a DB update.
    final Optional<Cluster> cluster = _clusterSvc.getActiveCluster(pGroup.getId(), pClusterName);

    if (cluster.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    final ClusterDescription clusterDescription = cluster.get().getClusterDescription();

    final Optional<MongotuneStatus> clusterMongotuneStatus =
        clusterDescription.getMongotuneStatus();
    final boolean isTenantCluster = clusterDescription.isTenantCluster();
    if (isTenantCluster || clusterMongotuneStatus.isEmpty()) {
      throw new SvcException(
          CommonErrorCode.BAD_REQUEST,
          isTenantCluster
              ? "Tenant clusters do not support mongotune"
              : "Mongotune feature flag not enabled for cluster.");
    }

    // Get host IDs from the cluster description and check write block status from host last ping
    // data
    final Optional<HostCluster> hostCluster =
        _clusterSvc.findActiveHostClusterByGroupIdAndAtlasClusterName(pGroup.getId(), pClusterName);
    final Set<String> hostIds = hostCluster.orElseThrow().getHostIds();

    boolean anyWriteBlocked = false;

    for (final String hostId : hostIds) {
      final BasicBSONObject lastPingData = _hostLastPingSvc.getLastPing(hostId);
      if (lastPingData != null) {
        final BasicBSONObject serverStatus = PingUtils.extractServerStatus(lastPingData);
        if (serverStatus != null) {
          final BasicBSONObject repl = PingUtils.getBson(serverStatus, Ping.REPLICATION.field);
          final Integer userWriteBlockMode =
              PingUtils.getInteger(repl, Ping.USER_WRITE_BLOCK_MODE.field);
          final Integer userWriteBlockReason =
              PingUtils.getInteger(repl, Ping.USER_WRITE_BLOCK_REASON.field);
          if (userWriteBlockMode != null
              && userWriteBlockMode == UserWriteBlockMode.K_ENABLED.getCode()
              && userWriteBlockReason != null
              && userWriteBlockReason
                  == UserWriteBlockReason.K_DISK_USE_THRESHOLD_EXCEEDED.getCode()) {
            anyWriteBlocked = true;
            break;
          }
        }
      }
    }
    _clusterDescriptionDao.setDiskWriteBlockStatus(pGroup.getId(), pClusterName, anyWriteBlocked);

    final ClusterDescription updatedClusterDescription =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName).get();
    final MongotuneStatus mongotuneStatus = updatedClusterDescription.getMongotuneStatus().get();
    final Map<PolicyType, PolicyStatus> policies = mongotuneStatus.policies();
    final Optional<MongotuneProcessArgs> processArgs =
        _clusterSvc
            .getProcessArgs(pGroup.getId(), pClusterName)
            .flatMap(ClusterDescriptionProcessArgs::getMongotuneArg);
    final Map<String, Map<String, Object>> mergedPolicyArgs =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs.orElse(null), policies, cluster.get(), null);

    return Response.ok(
            new MongotuneStatusWithPolicyArgsAdminView(
                mongotuneStatus.toDBObject(), mergedPolicyArgs))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/mongotuneDashboard")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMongotuneGrafanaDashboard.GET")
  public Response getMongotuneGrafanaDashboard(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Optional<ClusterDescription> cluster =
        _clusterSvc.getActiveClusterDescription(pGroup.getId(), pClusterName);

    if (cluster.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }

    final String grafanaDashboardUrl =
        _mongotuneAppSettings.getMongotuneGrafanaDashboardUrl(pGroup.getId(), pClusterName);
    if (grafanaDashboardUrl == null) {
      throw new SvcException(
          CommonErrorCode.BAD_REQUEST,
          "Mongotune Grafana dashboard not configured for this environment.");
    }

    return Response.seeOther(URI.create(grafanaDashboardUrl)).build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/adminJobs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAdminJobs.GET")
  public Response getAdminJobs(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name");
    }
    final List<BasicDBObject> sortedJobs =
        _ndsAdminJobSvc.findByCluster(pGroupId, pClusterName).stream()
            .sorted(
                (a, b) ->
                    b.getDate(NDSAdminJob.FieldDefs.LAST_UPDATED)
                        .compareTo(a.getDate(NDSAdminJob.FieldDefs.LAST_UPDATED)))
            .collect(Collectors.toList());
    return Response.ok(sortedJobs).build();
  }

  @PUT
  @Path("/groups/{groupId}/imageJSON")
  @Consumes(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setImageJson.PUT")
  public Response setImageJson(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      final String pImageOverrides)
      throws SvcException {
    final String normalizedImageOverrides =
        StringUtils.isBlank(pImageOverrides) ? null : pImageOverrides;
    if (normalizedImageOverrides != null) {
      try {
        new JSONObject(normalizedImageOverrides);
      } catch (final JSONException e) {
        LOG.error("Bad json for image overrides: {}", normalizedImageOverrides);
        return Response.status(Response.Status.BAD_REQUEST).build();
      }
    }
    _ndsGroupSvc.setImageOverrides(pGroup.getId(), normalizedImageOverrides, pAuditInfo);
    return Response.ok().build();
  }

  @PUT
  @Path("/groups/{groupId}/chefTarballUri")
  @Consumes(MediaType.TEXT_PLAIN)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setChefTarballUri.PUT")
  public Response setChefTarballUri(
      @Context final AuditInfo pAuditInfo, @Context final Group pGroup, final String pChefTarball)
      throws SvcException {
    final String normalizedTarballUri = StringUtils.isBlank(pChefTarball) ? null : pChefTarball;
    _ndsGroupSvc.setChefTarballUri(pGroup.getId(), normalizedTarballUri, pAuditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/{clusterName}/processArgs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSAdminResource.getAllProcessArgs.GET")
  public Response getAllProcessArgs(
      @Context final Group pGroup, @PathParam("clusterName") final String pClusterName) {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    return Response.ok(_adminActionSvc.getProcessArgsAdminView(pGroup.getId(), pClusterName))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/{clusterName}/ftsEnabled")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSAdminResource.getFTSEnabled.GET")
  public Response getFTSEnabled(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final JSONObject response = new JSONObject();
    response.put("enabled", _ndsUISvc.getHasFTSEnabled(pGroup.getId(), pClusterName));

    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/groups/{groupId}/mongotVersion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSAdminResource.getMongotVersion.GET")
  public Response getMongotVersion(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup) {
    final AutomationConfig automationConfig =
        _automationConfigPublishingSvc.findPublished(pGroup.getId());
    if (automationConfig == null || automationConfig.getMongotTemplate() == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    return Response.ok(
            new JSONObject()
                .put("version", automationConfig.getMongotTemplate().getVersion())
                .toString())
        .build();
  }

  @GET
  @Path("/groups/{groupId}/{clusterName}/mongotVersion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSAdminResource.getClusterMongotVersion.GET")
  public Response getClusterMongotVersion(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    if (!ValidationUtils.isValidGroupId(pGroupId.toString())) {
      throw new SvcException(NDSErrorCode.INVALID_GROUP_ID);
    }
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    final String groupMongotVersion = _ndsAdminSvc.getGroupMongotVersion(pGroupId);

    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getActiveClusterDescription(pGroupId, pClusterName);

    if (clusterDescription.isEmpty()) {
      // check cluster with such name existed
      _clusterSvc
          .getClusterDescriptionIncludingDeleted(pGroupId, pClusterName)
          .orElseThrow(
              () -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, pClusterName, pGroupId));

      return Response.ok(new MongotClusterVersionView(groupMongotVersion)).build();
    }

    final Optional<SearchDeploymentDescription> searchDeploymentDescription =
        _searchDeploymentDescriptionSvc.findNonDeletedDeploymentBySourceMongoCluster(
            clusterDescription.get().getUniqueId());

    if (searchDeploymentDescription.isEmpty()) {
      return Response.ok(new MongotClusterVersionView(groupMongotVersion)).build();
    }

    final List<String> searchDeploymentMongotVersions =
        _ndsAdminSvc.getSearchDeploymentMongotVersions(
            pGroupId, clusterDescription.get().getUniqueId());

    return Response.ok(
            new MongotClusterVersionView(groupMongotVersion, searchDeploymentMongotVersions))
        .build();
  }

  @PATCH
  @Path("/groups/{groupId}/{clusterName}/processArgs")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateProcessArgs.PATCH")
  public Response updateProcessArgs(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final ClusterDescriptionProcessArgsAdminView pProcessArgsView)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroupId, pClusterName);

    final ClusterDescriptionProcessArgsAdminView result =
        _adminActionSvc.updateProcessArgs(
            pAuditInfo, pGroup, pGroupId, pClusterName, pProcessArgsView);

    return Response.ok(result).build();
  }

  @PATCH
  @Path("/groups/{groupId}/{clusterName}/mongotProcessArgs")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateMongotProcessArgs.PATCH")
  public Response updateMongotProcessArgs(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      final MongotProcessArgsView pMongotProcessArgsView)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }
    _ndsUISvc.updateMongotProcessArgs(
        pGroup.getId(),
        pClusterName,
        _clusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .map(ClusterDescription::getMongoDBVersion)
            .orElse(null),
        pMongotProcessArgsView,
        pAuditInfo);

    if (isFeatureFlagEnabled(
        FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES, _appSettings, null, pGroup)) {
      _searchAdminSvc.tryMarkingMongotArgsUpdatedForCluster(pGroup.getId(), pClusterName);
    }

    final Optional<MongotProcessArgsView> mongotProcessArgsViewOpt =
        _ndsUISvc.getMongotProcessArgs(pGroup.getId(), pClusterName);

    return Response.ok(
            mongotProcessArgsViewOpt.isPresent()
                ? mongotProcessArgsViewOpt.get()
                : EMPTY_JSON_OBJECT)
        .build();
  }

  @DELETE
  @Path("/groups/{groupId}/{clusterName}/pendingIndexes")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.cancelPendingIndexes.DELETE")
  public Response cancelPendingIndexes(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroupId, pClusterName);
    _clusterSvc.cancelAllPendingIndexes(pGroup.getId(), pClusterName, pAuditInfo);
    _ndsGroupDao.setPlanASAP(pGroup.getId());
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/groups/{groupId}/{clusterName}/clearIndexConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.clearIndexConfig.DELETE")
  public Response clearIndexConfig(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    _adminClusterLockSvc.validateClusterLockForCurrentUser(pUser, pGroupId, pClusterName);
    final Cluster targetCluster =
        _clusterSvc
            .getActiveCluster(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(AutomationErrorCode.CLUSTER_NOT_FOUND));
    final AutomationConfig automationConfig =
        _automationConfigPublishingSvc.findPublished(pGroup.getId());

    if (automationConfig == null) {
      throw new SvcException(AutomationErrorCode.PUBLISHED_CONFIG_NOT_FOUND);
    }

    final List<IndexConfig> indexConfigsWithoutTargetCluster =
        IndexConfigUtil.getIndexesWithoutTargetCluster(
            automationConfig.getDeployment().getIndexConfigs(), targetCluster);
    if (indexConfigsWithoutTargetCluster.size()
        != automationConfig.getDeployment().getIndexConfigs().size()) {
      _ndsAutomationConfigSvc.clearIndexConfig(
          pGroup, pOrganization, pUser, pAuditInfo, indexConfigsWithoutTargetCluster);
    }

    if (targetCluster.getClusterDescription().getPendingIndexes().size() != 0) {
      _clusterSvc.deleteAllPendingIndexes(pGroup.getId(), pClusterName, pAuditInfo);
      _ndsGroupDao.setPlanASAP(pGroup.getId());
    }

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/groups/{groupId}/{clusterName}/reboot")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.requestClusterCriticalReboot.POST")
  public Response requestClusterCriticalReboot(
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName) {
    LOG.info(
        "Critical Reboot Requested for cluster {} in group {} by addr {} user {}",
        pClusterName,
        pGroup.getId(),
        pRequest.getRemoteAddr(),
        pRequest.getRemoteUser());
    _clusterSvc.requestClusterCriticalReboot(pGroup.getId(), pClusterName, pAuditInfo);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/groups/{groupId}/{clusterName}/adminBackupSnapshots")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAdminBackupSnapshots.GET")
  public Response getAdminBackupSnapshots(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    return Response.ok(_ndsUISvc.getAdminBackupSnapshotCommonViews(pGroupId, pClusterName)).build();
  }

  @GET
  @Path("/groups/{groupId}/{clusterName}/adminBackupSnapshots/{snapshotId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAdminBackupSnapshot.GET")
  public Response getAdminBackupSnapshot(
      @PathParam("snapshotId") final ObjectId pAdminBackupSnapshotId) {
    Optional<AdminBackupSnapshot> snapshotOpt =
        _ndsAdminBackupSnapshotSvc.findById(pAdminBackupSnapshotId);
    if (snapshotOpt.isPresent()) {
      return Response.ok(_ndsUISvc.getAdminBackupSnapshotView(snapshotOpt.get())).build();
    }

    return Response.status(Response.Status.NOT_FOUND).build();
  }

  @DELETE
  @Path("/groups/{groupId}/{clusterName}/adminBackupSnapshots/{snapshotId}")
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteAdminBackupSnapshot.DELETE")
  public Response deleteAdminBackupSnapshot(@PathParam("snapshotId") final ObjectId pSnapshotId) {
    _ndsAdminBackupSnapshotSvc.markSnapshotForDeletion(pSnapshotId);
    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/{clusterName}/adminBackupSnapshots/{snapshotId}")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.extendAdminBackupSnapshot.PATCH")
  public Response extendAdminBackupSnapshot(
      @PathParam("snapshotId") final ObjectId pSnapshotId,
      @FormParam("expirationDate") final Long pExpirationDate) {
    _ndsAdminBackupSnapshotSvc.extendSnapshot(pSnapshotId, new Date(pExpirationDate));
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/resurrect")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.resurrectCluster.POST")
  public Response resurrectCluster(
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AppUser pAppUser,
      @PathParam("clusterName") final String pClusterName,
      final ResurrectOptionsView pResurrectOptions)
      throws SvcException {
    final Optional<ClusterDescription> clusterOpt =
        _clusterDescriptionDao.findByNameAnyState(pGroup.getId(), pClusterName);

    // validate cluster eligibility for resurrection
    if (clusterOpt.isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND);
    }
    if (!pGroup.isActive()) {
      // resurrect group
      _groupSvc.resurrectGroup(pGroup);
      _ndsGroupDao.setNeedsPublishForGroup(pGroup.getId(), new Date());
    }

    // ensure automation config is published
    final AutomationConfig published =
        _automationConfigPublishingSvc.findPublishedOrEmpty(pGroup.getId());
    if (published.getVersion() <= 0) {
      _automationConfigPublishingSvc.saveDraft(published, pAppUser, pOrganization, pGroup);
      _automationConfigPublishingSvc.publish(pOrganization, pGroup, pAppUser);
    }

    final ResurrectOptions resurrectOptions =
        ofNullable(pResurrectOptions)
            .map(ResurrectOptionsView::toResurrectOptions)
            .filter(
                o ->
                    o.getClusterUniqueId() != null
                        && !o.getClusterUniqueId().equals(clusterOpt.get().getUniqueId()))
            .orElse(null);

    // This is to guard against the condition where a customer might have recreated the cluster
    // with different specs accidentally. If we specify a unique id we can instead attempt to
    // serialize the archival doc of the original cluster instead of the accidental cluster.
    Optional<ClusterDescription> archivedDescription = Optional.empty();
    if (resurrectOptions != null
        && resurrectOptions.getClusterUniqueId() != null
        && clusterOpt.get().getUniqueId() != resurrectOptions.getClusterUniqueId()) {

      // We will try to serialize the document, but since this is an archived document we just give
      // a lazy effort here since we can't guarantee that this description is still valid in any
      // way.
      try {
        archivedDescription =
            _clusterSvc.getHistoricalClusterDescriptionByUniqueId(
                pGroup.getId(), resurrectOptions.getClusterUniqueId());
      } catch (final Exception pE) {
        LOG.atLevel(Level.WARN)
            .setCause(pE)
            .addKeyValue("groupId", pGroup.getId())
            .addKeyValue("clusterName", pClusterName)
            .addKeyValue("clusterUniqueId", resurrectOptions.getClusterUniqueId())
            .log(
                "Failed to serialize archived dao using unique id, falling back to most recent"
                    + " cluster description");
        archivedDescription = Optional.empty();
      }
    }

    final ClusterDescription newCluster =
        _clusterSvc.getResurrectedClusterDescription(
            archivedDescription.orElse(clusterOpt.get()), resurrectOptions);

    // throw error if BCP is enabled
    _clusterSvc.validateBCPResurrection(pGroup, newCluster);

    try {
      _ndsUISvc.createCluster(
          pOrganization,
          pGroup.getId(),
          NDSUISvc.getViewFromDescription(newCluster),
          ClusterCreateContext.forResurrection(),
          pAppUser,
          pAuditInfo,
          pRequest,
          null);
    } catch (final Exception e) {
      LOG.error("Error creating resurrected cluster {}/{}", pGroup.getId(), pClusterName, e);
      throw new SvcException(NDSErrorCode.RESURRECTION_FAILED, e.getMessage());
    }

    return Response.ok().build();
  }

  @GET
  @Path("/leakedItems")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLeakedItems.GET")
  public Response getLeakedItems(
      @QueryParam("skip") @DefaultValue("0") final int pSkip,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("provider") @DefaultValue("") final String pProvider,
      @QueryParam("type") @DefaultValue("") final String pType,
      @QueryParam("jiraTicket") @DefaultValue("") final String pJiraTicket,
      @QueryParam("jiraFilterEnabled") @DefaultValue("false") final Boolean pJiraFilterEnabled,
      @QueryParam("sortDirection") @DefaultValue("0") final int pSortDirection) {
    final Optional<CloudProvider> cloudProvider =
        pProvider.isEmpty() ? Optional.empty() : Optional.of(CloudProvider.valueOf(pProvider));

    final Optional<OrphanedItem.Type> type;
    final Optional<String> jiraTicket =
        pJiraTicket.isEmpty() ? Optional.empty() : Optional.of(pJiraTicket);

    if (!pType.isEmpty()) {
      if (cloudProvider.isPresent()) {
        type = Optional.of(getCloudProviderLeakedItemType(pType, cloudProvider.get()));
      } else {
        throw new IllegalArgumentException("Provider must be specified along with type.");
      }
    } else {
      type = Optional.empty();
    }

    // Try an extra record to calculate if there's a next page
    final List<LeakedItemView> leakedItems =
        _leakedItemSvc
            .getLeakedItemsNotDeleted(
                pSkip,
                pLimit + 1,
                cloudProvider,
                type,
                jiraTicket,
                pJiraFilterEnabled,
                pSortDirection)
            .stream()
            .map(LeakedItemView::new)
            .collect(Collectors.toList());
    final List<LeakedItemView> data =
        leakedItems.size() < pLimit ? leakedItems : leakedItems.subList(0, pLimit);
    final boolean isLastPage = leakedItems.size() == 0 || leakedItems.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final LeakedItemsContainer result =
        new LeakedItemsContainer(data, isFirstPage, isLastPage, pSkip, pLimit);
    return Response.ok().entity(result).build();
  }

  @PATCH
  @Path("/leakedItems/{cloudProvider}/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.approveLeakedItemForDeletion.PATCH")
  public Response approveLeakedItemForDeletion(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("type") final String pTypeStr,
      @FormParam("id") final String pId) {
    _leakedItemSvc.bulkApproveForDeletion(
        List.of(pId), pCloudProvider, getCloudProviderLeakedItemType(pTypeStr, pCloudProvider));
    return Response.ok().build();
  }

  @PATCH
  @Path("/leakedItems/bulk/{cloudProvider}/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.bulkApproveLeakedItemsForDeletion.PATCH")
  public Response bulkApproveLeakedItemsForDeletion(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("type") final String pTypeStr,
      @FormParam("ids") final List<String> pIds) {
    if (pIds == null || pIds.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity("IDs list cannot be null or empty")
          .build();
    }

    final long modifiedCount =
        _leakedItemSvc.bulkApproveForDeletion(
            pIds, pCloudProvider, getCloudProviderLeakedItemType(pTypeStr, pCloudProvider));

    return Response.ok().entity(Map.of("modifiedCount", modifiedCount)).build();
  }

  @DELETE
  @Path("/leakedItems/{cloudProvider}/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.revokeLeakedItemApprovalForDeletion.DELETE")
  public Response revokeLeakedItemApprovalForDeletion(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("type") final String pTypeStr,
      @FormParam("id") final String pId) {
    _leakedItemSvc.revokeApprovalForDeletion(
        pId, pCloudProvider, getCloudProviderLeakedItemType(pTypeStr, pCloudProvider));
    return Response.ok().build();
  }

  @PATCH
  @Path("/leakedItems/ignored/{cloudProvider}/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.ignoreLeakedItem.PATCH")
  public Response ignoreLeakedItem(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("type") final String pTypeStr,
      @FormParam("id") final String pId) {
    _leakedItemSvc.bulkIgnoreItems(
        List.of(pId), pCloudProvider, getCloudProviderLeakedItemType(pTypeStr, pCloudProvider));
    return Response.ok().build();
  }

  @PATCH
  @Path("/leakedItems/ignored/bulk/{cloudProvider}/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.bulkIgnoreLeakedItems.PATCH")
  public Response bulkIgnoreLeakedItems(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("type") final String pTypeStr,
      @FormParam("ids") final List<String> pIds) {
    if (pIds == null || pIds.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity("IDs list cannot be null or empty")
          .build();
    }

    final long modifiedCount =
        _leakedItemSvc.bulkIgnoreItems(
            pIds, pCloudProvider, getCloudProviderLeakedItemType(pTypeStr, pCloudProvider));

    return Response.ok().entity(Map.of("modifiedCount", modifiedCount)).build();
  }

  @PATCH
  @Path("/leakedItems/update/{cloudProvider}/{type}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateLeakedItem.PATCH")
  public Response updateLeakedItem(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("type") final String pTypeStr,
      @FormParam("id") final String pId,
      @FormParam("jiraTicket") final String pJiraTicket)
      throws SvcException {

    if (!pJiraTicket.isEmpty() && !ValidationUtils.isValidJiraTicket(pJiraTicket)) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "Jira ticket. The Jira ticket must be a valid Jira ticket number, e.g. CLOUDP-123456.");
    }

    _leakedItemSvc.updateJiraTicket(
        pId, pCloudProvider, pJiraTicket, getCloudProviderLeakedItemType(pTypeStr, pCloudProvider));
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/scheduleMaintenanceNow")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.scheduleMaintenanceNow.POST")
  public Response scheduleMaintenanceNow(@Context AuditInfo pAuditInfo, @Context final Group pGroup)
      throws SvcException {
    if (!pGroup.isActive()) {
      throw new SvcException(NDSErrorCode.GROUP_NOT_ACTIVE);
    }

    _ndsGroupMaintenanceSvc.startMaintenanceASAP(pGroup.getId(), pAuditInfo);

    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/scheduleMaintenanceNextWindow")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.scheduleMaintenanceNextWindow.POST")
  public Response scheduleMaintenanceNextWindow(
      @Context AuditInfo pAuditInfo, @Context final Group pGroup) throws SvcException {
    if (!pGroup.isActive()) {
      throw new SvcException(NDSErrorCode.GROUP_NOT_ACTIVE);
    }

    _ndsGroupMaintenanceSvc.scheduleMaintenanceForTheNextWindow(pGroup.getId(), pAuditInfo);

    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/grantExtraMaintenanceDeferral")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.grantExtraMaintenanceDeferral.PATCH")
  public Response grantExtraMaintenanceDeferral(
      @Context final AppUser pAppUser,
      @Context AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      final NDSExtraMaintenanceDeferralGrantView pExtraMaintenanceDeferralGrantView)
      throws SvcException {
    if (_appSettings.getNDSGovUSEnabled()) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Cannot grant additional deferred maintenance for projects in the MongoDB Government"
              + " environment.");
    }

    if (!ValidationUtils.isValidJiraLink(
        pExtraMaintenanceDeferralGrantView.getDeferralGrantJiraTicket())) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "Jira ticket. The extra deferral Jira ticket must be a valid Jira ticket.");
    }

    final boolean shouldSetAdminNote =
        pExtraMaintenanceDeferralGrantView.getAdminNote() != null
            && pExtraMaintenanceDeferralGrantView.getAdminNote().getMessage() != null;

    if (shouldSetAdminNote) {
      validateAdminNoteMessage(pExtraMaintenanceDeferralGrantView.getAdminNote().getMessage());
    }

    _ndsGroupMaintenanceSvc.grantExtraMaintenanceDeferral(
        pGroupId, pExtraMaintenanceDeferralGrantView.getDeferralGrantJiraTicket(), pAuditInfo);

    if (shouldSetAdminNote) {
      return setAdminNoteForGroup(
          pAuditInfo,
          pAppUser.getUsername(),
          pGroupId,
          pExtraMaintenanceDeferralGrantView.getAdminNote());
    }

    return Response.ok().build();
  }

  private void validateAdminNoteMessage(final String pMessage) throws SvcException {
    if (!ValidationUtils.isAlphanumericWithHyphensPeriodsColonsWhitespaceApostrophe(pMessage)) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "admin note. The admin note must only contain valid characters (alphanumeric with"
              + " colons, periods, hyphens, apostrophe and whitespaces).");
    }
    if (pMessage.length() > 250) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "admin note. The admin note must be less than or equal to 250 characters.");
    }
  }

  @GET
  @Path("/cloudProviders/{cloudProviderName}/options")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getProviderOptions(
      @PathParam("cloudProviderName") final String pCloudProviderName,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws Exception {

    if (StringUtils.isEmpty(pCloudProviderName)) {
      throw new IllegalArgumentException("cloudProviderName must be non-null");
    }

    final CloudProvider cloudProvider = CloudProvider.findByName(pCloudProviderName.toUpperCase());

    if (cloudProvider == CloudProvider.NONE) {
      throw ApiErrorCode.INVALID_PROVIDER.exception(pEnvelope, pCloudProviderName);
    }

    if (_appSettings.getNDSGovUSEnabled() && cloudProvider != CloudProvider.AWS) {
      throw ApiErrorCode.CLOUD_PROVIDER_UNSUPPORTED_FOR_GOV.exception(
          pEnvelope, cloudProvider, _appSettings.getGovSiteName());
    }

    return Response.ok(_apiCloudProviderSvc.getProviderOptions(pCloudProviderName)).build();
  }

  @GET
  @Path("/cipherSuiteOptions")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getCipherSuiteOptions() {
    final List<CipherSuiteView> cipherSuites =
        Arrays.stream(CipherSuite.values())
            // Remove filter when enabled in TODO: CLOUDP-287438
            .filter(c -> c.getTlsVersion() == TLSVersion.TLS1_2)
            .map(
                c ->
                    new CipherSuiteView(c.name(), TLSVersionView.valueOf(c.getTlsVersion().name())))
            .toList();

    return Response.ok(cipherSuites).build();
  }

  @GET
  @Path("/groups/{groupId}/cipherSuiteOptions")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getCipherSuiteOptions(@PathParam("groupId") final ObjectId pGroupId) {
    final Group group = _groupSvc.findById(pGroupId);
    final boolean tls13Enabled =
        isFeatureFlagEnabled(FeatureFlag.ATLAS_ALLOW_ENFORCE_MIN_TLS_13, _appSettings, null, group);
    final List<CipherSuiteView> cipherSuites =
        Arrays.stream(CipherSuite.values())
            .filter(
                c ->
                    c.getTlsVersion() == TLSVersion.TLS1_2
                        || (tls13Enabled && c.getTlsVersion() == TLSVersion.TLS1_3))
            .map(CipherSuiteView::fromCipherSuite)
            .toList();

    return Response.ok(cipherSuites).build();
  }

  @GET
  @Path("/groups/{groupId}/adminNote")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAdminNoteByGroupId.GET")
  public Response getAdminNoteByGroupId(@PathParam("groupId") final ObjectId pGroupId) {
    final Optional<AdminNote> adminNoteByGroupId = _adminNoteSvc.getAdminNoteByGroupId(pGroupId);
    if (adminNoteByGroupId.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
    }

    return Response.ok(new AdminNoteView(adminNoteByGroupId.get())).build();
  }

  @PATCH
  @Path("/groups/{groupId}/adminNote")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setAdminNote.PATCH")
  public Response setAdminNote(
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      final AdminNoteView pAdminNoteView)
      throws SvcException {
    _ndsGroupDao.find(pGroupId).orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    validateAdminNoteMessage(pAdminNoteView.getMessage());

    return setAdminNoteForGroup(pAuditInfo, pAppUser.getUsername(), pGroupId, pAdminNoteView);
  }

  private Response setAdminNoteForGroup(
      final AuditInfo pAuditInfo,
      final String pUsername,
      final ObjectId pGroupId,
      final AdminNoteView pAdminNoteView)
      throws SvcException {
    _adminNoteSvc.updateAdminNote(pAdminNoteView.toAdminNote(pUsername), pAuditInfo);
    return Response.ok(new AdminNoteView(_adminNoteSvc.getAdminNoteByGroupId(pGroupId).get()))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/capacityCriteria")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAZDistributionCriteriaForGroup.GET")
  public Response getAZDistributionCriteriaForGroup(@PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final NDSGroup group =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
    final AZBalancingRequirement azBalancingRequirement =
        group
            .getAZBalancingRequirementOverride()
            .orElse(AZBalancingRequirement.getRegionAgnosticDefault());
    final boolean isPinned = group.getAZBalancingRequirementOverride().isPresent();

    return Response.ok(
            new AZBalancingOverrideRequirementView(
                AZBalancingRequirementView.fromAZBalancingRequirement(azBalancingRequirement),
                isPinned))
        .build();
  }

  @PATCH
  @Path("/groups/{groupId}/capacityCriteria")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setAZDistributionCriteriaForGroup.PATCH")
  public Response setAZDistributionCriteriaForGroup(
      @PathParam("groupId") final ObjectId pGroupId,
      final AZBalancingOverrideRequirementPatchView pAZBalancingOverrideRequirementView,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    if (pAZBalancingOverrideRequirementView.azBalancingRequirement() == null) {
      _ndsGroupSvc.unsetAZBalancingRequirementOverride(pGroupId, pAuditInfo);
    } else {
      final AZBalancingRequirement azBalancingRequirement =
          new AZBalancingRequirement(
              pAZBalancingOverrideRequirementView.azBalancingRequirement().minNumAZs(),
              pAZBalancingOverrideRequirementView.azBalancingRequirement().goalNumAZs(),
              pAZBalancingOverrideRequirementView.azBalancingRequirement().maxNumAZs(),
              pAZBalancingOverrideRequirementView
                  .azBalancingRequirement()
                  .migrateCapacityConstrainedNodesOnUpgrade());
      _ndsGroupSvc.setAZBalancingRequirementOverride(pGroupId, azBalancingRequirement, pAuditInfo);
    }

    return getAZDistributionCriteriaForGroup(pGroupId);
  }

  // This method accepts a new AZ Balancing criteria and checks the impact the criteria
  // will have across all the hardware in the project. If any hardware is needs re-balancing,
  // depending on the severity, the cluster it belongs to is returned as invalid or suboptimal.
  @POST
  @Path("/groups/{groupId}/capacityCriteria/calculateImpactedClusters")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.calculateCapacityCriteriaImpactedClusters.POST")
  public Response calculateCapacityCriteriaImpactedClusters(
      @PathParam("groupId") final ObjectId pGroupId,
      final AZBalancingOverrideRequirementPatchView pNewAZBalancingRequirements)
      throws SvcException {
    final NDSGroup group =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    if (pNewAZBalancingRequirements.azBalancingRequirement() == null) {
      throw new SvcException(CommonErrorCode.BAD_REQUEST);
    }

    final AZBalancingRequirement azBalancingRequirement =
        new AZBalancingRequirement(
            pNewAZBalancingRequirements.azBalancingRequirement().minNumAZs(),
            pNewAZBalancingRequirements.azBalancingRequirement().goalNumAZs(),
            pNewAZBalancingRequirements.azBalancingRequirement().maxNumAZs(),
            pNewAZBalancingRequirements
                .azBalancingRequirement()
                .migrateCapacityConstrainedNodesOnUpgrade());

    final List<ClusterDescription> clusterDescriptions =
        _clusterSvc.getActiveClusterDescriptions(pGroupId);
    final List<Pair<String, ZoneDistributionStatus>> clusterStatuses =
        clusterDescriptions.stream()
            .map(
                (cd) -> {
                  final List<ReplicaSetHardware> replicaSetHardware =
                      _replicaSetHardwareSvc.getReplicaSetHardware(cd.getGroupId(), cd.getName());
                  final List<ZoneDistributionStatus> regionStatuses =
                      cd.getRegionNames().stream()
                          .map(
                              regionName ->
                                  _azSelectionSvc.calculateStatusFromNewBalancingRequirement(
                                      group,
                                      cd,
                                      replicaSetHardware,
                                      regionName,
                                      azBalancingRequirement))
                          .toList();

                  return Pair.of(
                      cd.getName(),
                      ZoneDistributionStatus.mergeStatuses(
                          regionStatuses.toArray(ZoneDistributionStatus[]::new)));
                })
            .toList();

    final List<String> invalidClusters =
        clusterStatuses.stream()
            .filter(
                statusPair ->
                    statusPair.getRight().status() == ZoneDistributionStatus.Status.INVALID)
            .map(Pair::getLeft)
            .toList();
    final List<String> suboptimalClusters =
        clusterStatuses.stream()
            .filter(
                statusPair ->
                    statusPair.getRight().status()
                        == ZoneDistributionStatus.Status.VALID_BUT_NOT_IDEAL)
            .map(Pair::getLeft)
            .toList();

    return Response.ok(new AZCapacityImpactedClustersView(invalidClusters, suboptimalClusters))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/clusters/{clusterName}/capacityCriteria")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAZDistributionCriteriaForCluster.GET")
  public Response getAZDistributionCriteriaForCluster(
      @Context final NDSGroup pNDSGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final ClusterDescription clusterDescription =
        _clusterSvc
            .getActiveClusterDescription(pGroupId, pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    final List<AZBalancingRegionConfigOverrideRequirementView> regionConfigOverrides =
        clusterDescription.getAllReplicationSpecsIncludingConfig().stream()
            .map(ReplicationSpec::getRegionConfigs)
            .flatMap(Collection::stream)
            .collect(
                Collectors.toMap(
                    rc -> Pair.of(rc.getCloudProvider(), rc.getRegionName()),
                    Function.identity(),
                    (existing, replacement) -> existing))
            .values()
            .stream()
            .map(
                regionConfig ->
                    AZBalancingRegionConfigOverrideRequirementView.create(pNDSGroup, regionConfig))
            .toList();

    return Response.ok(regionConfigOverrides).build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusters/{clusterName}/capacityCriteria")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setAZDistributionCriteriaForClusterRegion.PATCH")
  public Response setAZDistributionCriteriaForClusterRegion(
      @Context final AuditInfo pAuditInfo,
      @Context final NDSGroup pNDSGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final AZBalancingRegionConfigOverrideRequirementPatchView pRegionConfigOverride)
      throws SvcException {
    final RegionName regionName =
        switch (pRegionConfigOverride.cloudProvider()) {
          case AWS -> AWSRegionName.valueOf(pRegionConfigOverride.regionName());
          case AZURE -> AzureRegionName.valueOf(pRegionConfigOverride.regionName());
          case GCP -> GCPRegionName.valueOf(pRegionConfigOverride.regionName());
          default -> throw new IllegalArgumentException("Unexpected cloud provider");
        };

    if (pRegionConfigOverride.azBalancingRequirement() == null) {
      _clusterSvc.unsetAZBalancingCriteria(pGroupId, pClusterName, regionName, pAuditInfo);
    } else {
      final AZBalancingRequirement azBalancingRequirement =
          new AZBalancingRequirement(
              pRegionConfigOverride.azBalancingRequirement().minNumAZs(),
              pRegionConfigOverride.azBalancingRequirement().goalNumAZs(),
              pRegionConfigOverride.azBalancingRequirement().maxNumAZs(),
              pRegionConfigOverride
                  .azBalancingRequirement()
                  .migrateCapacityConstrainedNodesOnUpgrade());

      _clusterSvc.setAZBalancingCriteria(
          pGroupId, pClusterName, regionName, azBalancingRequirement, pAuditInfo);
    }

    return getAZDistributionCriteriaForCluster(pNDSGroup, pGroupId, pClusterName);
  }

  // This method accepts a new AZ Balancing criteria and checks the impact the criteria
  // will have across all the hardware within the same region of a cluster. If any hardware needs
  // re-balancing, depending on the severity, the cluster is returned as invalid or suboptimal.
  @POST
  @Path("/groups/{groupId}/clusters/{clusterName}/capacityCriteria/calculateImpactedRegion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.calculateCapacityCriteriaImpactedRegion.POST")
  public Response calculateCapacityCriteriaImpactedRegion(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final AZBalancingRegionConfigOverrideRequirementPatchView pRegionConfigOverride)
      throws SvcException {
    final NDSGroup group =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    if (pRegionConfigOverride.azBalancingRequirement() == null) {
      throw new SvcException(CommonErrorCode.BAD_REQUEST);
    }

    final AZBalancingRequirement azBalancingRequirement =
        new AZBalancingRequirement(
            pRegionConfigOverride.azBalancingRequirement().minNumAZs(),
            pRegionConfigOverride.azBalancingRequirement().goalNumAZs(),
            pRegionConfigOverride.azBalancingRequirement().maxNumAZs(),
            pRegionConfigOverride
                .azBalancingRequirement()
                .migrateCapacityConstrainedNodesOnUpgrade());

    final RegionName regionName =
        switch (pRegionConfigOverride.cloudProvider()) {
          case AWS -> AWSRegionName.valueOf(pRegionConfigOverride.regionName());
          case AZURE -> AzureRegionName.valueOf(pRegionConfigOverride.regionName());
          case GCP -> GCPRegionName.valueOf(pRegionConfigOverride.regionName());
          default -> throw new IllegalArgumentException("Unexpected cloud provider");
        };

    final ClusterDescription clusterDescription =
        _clusterSvc
            .getActiveClusterDescription(pGroupId, pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));
    final List<ReplicaSetHardware> replicaSetHardware =
        _replicaSetHardwareSvc.getReplicaSetHardware(
            clusterDescription.getGroupId(), clusterDescription.getName());

    final ZoneDistributionStatus regionStatus =
        _azSelectionSvc.calculateStatusFromNewBalancingRequirement(
            group, clusterDescription, replicaSetHardware, regionName, azBalancingRequirement);

    return Response.ok(
            switch (regionStatus.status()) {
              case VALID_BUT_NOT_IDEAL ->
                  new AZCapacityImpactedClustersView(List.of(), List.of(pClusterName));
              case INVALID -> new AZCapacityImpactedClustersView(List.of(pClusterName), List.of());
              default -> new AZCapacityImpactedClustersView(List.of(), List.of());
            })
        .build();
  }

  @POST
  @Path("/serverless/{tenantUniqueId}/blockTenant")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.blockServerlessTenant.POST")
  public Response blockServerlessTenant(
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantUniqueId") final ObjectId pTenantUniqueId)
      throws SvcException {
    verifyServerlessIsSupported();
    final ObjectId poolId = _serverlessMTMPoolSvc.getPoolInfoForTenant(pTenantUniqueId).getLeft();

    _serverlessMTMPoolSvc.blockServerlessTenant(poolId, pTenantUniqueId);

    final ClusterDescription clusterDescription =
        _clusterSvc
            .getActiveClusterDescription(null, pTenantUniqueId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "Expected to find a tenant with unique Id %s", pTenantUniqueId)));

    _clusterSvc.setNeedsPublishWithEnvoySyncForServerlessCluster(
        clusterDescription.getGroupId(), clusterDescription.getName());
    _ndsGroupSvc.setPlanningNow(clusterDescription.getGroupId());

    final NDSServerlessInstanceAudit.Builder builder =
        new NDSServerlessInstanceAudit.Builder(
            NDSServerlessInstanceAudit.Type.SERVERLESS_INSTANCE_BLOCKED);
    builder.groupId(clusterDescription.getGroupId());
    builder.serverlessInstanceName(clusterDescription.getName());
    builder.serverlessInstanceUniqueId(pTenantUniqueId);
    builder.auditInfo(pAuditInfo);
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @POST
  @Path("/serverless/{tenantUniqueId}/unblockTenant")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.unblockServerlessTenant.POST")
  public Response unblockServerlessTenant(
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantUniqueId") final ObjectId pTenantUniqueId)
      throws SvcException {
    verifyServerlessIsSupported();
    _serverlessMTMPoolSvc.unblockServerlessTenant(pTenantUniqueId);

    final ClusterDescription clusterDescription =
        _clusterSvc
            .getActiveClusterDescription(null, pTenantUniqueId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "Expected to find a tenant with unique Id %s", pTenantUniqueId)));

    _clusterSvc.setNeedsPublishWithEnvoySyncForServerlessCluster(
        clusterDescription.getGroupId(), clusterDescription.getName());
    _ndsGroupSvc.setPlanningNow(clusterDescription.getGroupId());

    final NDSServerlessInstanceAudit.Builder builder =
        new NDSServerlessInstanceAudit.Builder(
            NDSServerlessInstanceAudit.Type.SERVERLESS_INSTANCE_UNBLOCKED);
    builder.groupId(clusterDescription.getGroupId());
    builder.serverlessInstanceName(clusterDescription.getName());
    builder.serverlessInstanceUniqueId(pTenantUniqueId);
    builder.auditInfo(pAuditInfo);
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @POST
  @Path("/serverless/{tenantUniqueId}/createEligibleServerlessFreeMigrationStatus")
  @TestUtility
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.createEligibleServerlessFreeMigrationStatus.POST")
  public Response createEligibleServerlessFreeMigrationStatus(
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantUniqueId") final ObjectId pTenantUniqueId)
      throws SvcException {
    final boolean isLocalDevOrQA =
        _appSettings.getAppEnv().isLocal() || _appSettings.getAppEnv().isDevOrQA();
    if (!isLocalDevOrQA) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "environment");
    }

    verifyServerlessIsSupported();
    final ClusterDescription cd =
        _clusterSvc
            .getActiveClusterDescription(null, pTenantUniqueId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "Expected to find a tenant with unique Id %s", pTenantUniqueId)));

    final TenantCloudProviderContainer tenantCloudProviderContainer =
        _ndsGroupDao.find(cd.getGroupId()).stream()
            .flatMap(
                ndsGroup -> {
                  final List<CloudProviderContainer> containers =
                      ndsGroup.getCloudProviderContainersByType(CloudProvider.SERVERLESS);
                  return containers.stream();
                })
            .map(TenantCloudProviderContainer.class::cast)
            .filter(container -> Objects.nonNull(container.getTenantClusterName()))
            .filter(container -> container.getTenantClusterName().equals(cd.getName()))
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "Expected to find a tenant cloud provider container for tenant with"
                                + " unique Id %s",
                            pTenantUniqueId)));

    _serverlessFreeMigrationStatusSvc.saveNewServerlessFreeMigrationStatus(
        cd, tenantCloudProviderContainer);

    // update createdDate of cluster description so it's eligible
    final Date eligibleCreatedDate = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
    _clusterDescriptionDao.setCreatedDate(cd.getGroupId(), cd.getName(), eligibleCreatedDate);

    return Response.ok().build();
  }

  @GET
  @Path("/mtm/serverless/pools/{poolId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMPool.GET")
  public Response getServerlessMTMPool(@PathParam("poolId") final ObjectId pPoolId)
      throws SvcException {
    verifyServerlessIsSupported();
    return Response.ok(_ndsServerlessUISvc.getServerlessMTMPoolView(pPoolId)).build();
  }

  @GET
  @Path("/mtm/serverless/pools")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMPools.GET")
  public Response getServerlessMTMPools() {
    verifyServerlessIsSupported();
    return Response.ok(
            _serverlessMTMPoolSvc.getAllPools().stream()
                .map(ServerlessMTMPoolView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @POST
  @Path("/mtm/serverless/pools")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.createServerlessMTMPool.POST")
  public Response createServerlessMTMPool(
      @Context final AuditInfo auditInfo,
      @QueryParam("envoyLogLevel") @DefaultValue(ServerlessNDSDefaults.ENVOY_LOG_LEVEL)
          final String pEnvoyLogLevel,
      final ServerlessMTMPoolView pPoolView)
      throws SvcException {
    verifyServerlessIsSupported();
    pPoolView.validateViewForCreate();

    final ServerlessMTMPool newPool =
        _serverlessMTMProjectSvc.setupServerlessMTMPool(
            pPoolView.getName(),
            pPoolView.getUsageType(),
            pPoolView.getGroupConfigView().toServerlessMTMPoolGroupConfig(),
            pPoolView.getStrategyView().toServerlessAutoScalingStrategy(),
            pPoolView.getMaxResidentMTMs(),
            pPoolView.getMaxResidentTenants(),
            pPoolView.isAutoScalingEnabled(),
            pPoolView.getIsolationGroups(),
            pPoolView.getMergeStrategyView().toServerlessMergeStrategy(),
            auditInfo,
            pEnvoyLogLevel);
    return Response.ok(new ServerlessMTMPoolView(newPool)).build();
  }

  @PATCH
  @Path("/mtm/serverless/pools/{poolId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateServerlessMTMPool.PATCH")
  public Response updateServerlessMTMPool(
      @PathParam("poolId") final ObjectId pPoolId, final ServerlessMTMPoolView pPoolView)
      throws Exception {
    verifyServerlessIsSupported();
    pPoolView.validateViewForUpdate(pPoolId);
    final ServerlessMTMPool updatedPool =
        _serverlessMTMProjectSvc.updateServerlessMTMPool(
            pPoolId,
            pPoolView.getName(),
            pPoolView.getUsageType(),
            pPoolView.getGroupConfigView().toServerlessMTMPoolGroupConfig(),
            pPoolView.getStrategyView().toServerlessAutoScalingStrategy(),
            pPoolView.getMaxResidentMTMs(),
            pPoolView.getMaxResidentTenants(),
            pPoolView.isAssignmentEnabled(),
            pPoolView.isAutoScalingEnabled(),
            pPoolView.getIsolationGroups(),
            pPoolView.getMergeStrategyView().toServerlessMergeStrategy());
    return Response.ok(new ServerlessMTMPoolView(updatedPool)).build();
  }

  @DELETE
  @Path("/mtm/serverless/pools/{poolId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteServerlessMTMPool.DELETE")
  public Response deleteServerlessMTMPool(
      @Context final AuditInfo auditInfo, @PathParam("poolId") final ObjectId pPoolId)
      throws Exception {
    verifyServerlessIsSupported();
    _serverlessMTMProjectSvc.teardownServerlessMTMPool(pPoolId, auditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/mtm/serverless/pools/{poolId}/envoyBypassCompleteTenants")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getEnvoyBypassCompleteTenants.GET")
  public Response getEnvoyBypassCompleteTenants(@PathParam("poolId") final ObjectId pPoolId)
      throws SvcException {
    verifyServerlessIsSupported();
    return Response.ok(_serverlessMTMPoolSvc.getEnvoyBypassCompleteTenants(pPoolId)).build();
  }

  @GET
  @Path("/mtm/serverless/clusters/pool/{poolId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMClusterByPoolId.GET")
  public Response getServerlessMTMClusterByPoolId(@PathParam("poolId") final ObjectId pPoolId) {
    verifyServerlessIsSupported();
    return Response.ok(_clusterSvc.getServerlessMTMClusterViewsByPool(pPoolId)).build();
  }

  private Response getMTMCluster(
      final ObjectId pGroupId, final String pClusterName, final MTMClusterType pType)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    final MTMClusterView mtmClusterView;
    switch (pType) {
      case SHARED -> mtmClusterView = _clusterSvc.getSharedMTMClusterView(pGroupId, pClusterName);
      case SERVERLESS ->
          mtmClusterView = _clusterSvc.getServerlessMTMClusterView(pGroupId, pClusterName);
      case FLEX -> mtmClusterView = _clusterSvc.getFlexMTMClusterView(pGroupId, pClusterName);
      default -> throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, pType);
    }
    return Response.ok(mtmClusterView).build();
  }

  @GET
  @Path("/mtm/serverless/pools/{poolId}/mtmLoads")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMLoadsByPoolId.GET")
  public Response getServerlessMTMLoadsByPoolId(
      @PathParam("poolId") final ObjectId pPoolId,
      @QueryParam("limit") final Integer pLimit,
      @QueryParam("cached") final Boolean pCached)
      throws SvcException {
    verifyServerlessIsSupported();
    final int limit = ofNullable(pLimit).orElse(DEFAULT_MTM_LOAD_LIMIT);
    final boolean cached = ofNullable(pCached).orElse(DEFAULT_RETRIEVE_CACHED_LOAD);
    return Response.ok(
            _ndsServerlessUISvc.getServerlessMTMLoadViewsByPoolId(pPoolId, limit, cached))
        .build();
  }

  @GET
  @Path("/mtm/serverless/clusters/{groupId}/{clusterName}/load")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessMTMLoad.GET")
  public Response getServerlessMTMLoad(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("numTopTenants") final Integer pNumTopTenants,
      @QueryParam("cached") final Boolean pCached)
      throws SvcException {
    verifyServerlessIsSupported();
    return Response.ok(
            _ndsServerlessUISvc
                .getServerlessMTMLoadView(
                    pGroupId,
                    pClusterName,
                    ofNullable(pNumTopTenants).orElse(DEFAULT_NUM_SERVERLESS_TOP_TENANTS),
                    ofNullable(pCached).orElse(DEFAULT_RETRIEVE_CACHED_LOAD))
                .orElse(null))
        .build();
  }

  @GET
  @Path("/serverless/{groupId}/{tenantInstanceName}/load")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessTenantLoad.GET")
  public Response getServerlessTenantLoad(
      @Context final Group pGroup,
      @PathParam("tenantInstanceName") final String pClusterName,
      @QueryParam("cached") final Boolean pCached)
      throws SvcException {
    verifyServerlessIsSupported();
    return Response.ok(
            _ndsServerlessUISvc
                .getServerlessTenantLoadView(
                    pGroup.getId(),
                    pClusterName,
                    ofNullable(pCached).orElse(DEFAULT_RETRIEVE_CACHED_LOAD))
                .orElse(null))
        .build();
  }

  @GET
  @Path("/serverless/{groupId}/{clusterName}/endpoints")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getServerlessTenantEndpoints.GET")
  public Response getServerlessTenantEndpoints(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    return Response.ok(_ndsTenantEndpointSvc.getEndpointViewsForTenant(pGroupId, pClusterName))
        .build();
  }

  @POST
  @Path("/mtm/serverless/profiles/{profileId}/scaleMTMCapacity")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.scaleMTMCapacity.POST")
  public Response scaleMTMCapacity(
      @PathParam("profileId") final ObjectId pProfileId,
      @QueryParam("numMTMs") final Integer pNumberOfMTMs)
      throws SvcException {
    verifyServerlessIsSupported();

    if (_appSettings.getNDSServerlessMTMOrganizationId().isEmpty()) {
      LOG.error("Serverless MTM organization ID is not configured.");
      return Response.status(Response.Status.BAD_REQUEST).entity(EMPTY_JSON_OBJECT).build();
    }

    final NDSServerlessMTMProfile profile = _ndsMTMProfileSvc.getServerlessMTMProfile(pProfileId);
    final ServerlessMTMPool serverlessMTMPool = _serverlessMTMPoolSvc.getPool(profile.getPoolId());
    final int numberOfMTMs = pNumberOfMTMs == null ? 1 : pNumberOfMTMs;

    try {
      _ndsSetupServerlessSvc.scaleMTMCapacity(profile, serverlessMTMPool, numberOfMTMs);
    } catch (final SvcException pE) {
      return Response.status(Response.Status.BAD_REQUEST).entity(EMPTY_JSON_OBJECT).build();
    }

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path(
      "/mtm/serverless/{cloudProvider}/{regionName}/{serverlessInstanceSize}/{index}/groupAndPoolNames")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getGroupAndPoolNames.GET")
  public Response getGroupAndPoolNames(
      @PathParam("cloudProvider") final String pCloudProvider,
      @PathParam("regionName") final String pRegionName,
      @PathParam("serverlessInstanceSize") final String pServerlessInstanceSize,
      @PathParam("index") final int pIndex)
      throws SvcException {
    verifyServerlessIsSupported();

    final CloudProvider cloudProvider = CloudProvider.findByName(pCloudProvider.toUpperCase());
    final RegionName regionName =
        RegionNameHelper.findByValue(cloudProvider, pRegionName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.INVALID_PARAMETER));
    final ServerlessInstanceSize serverlessInstanceSize =
        ServerlessInstanceSize.findByName(pServerlessInstanceSize.toUpperCase())
            .orElseThrow(() -> new SvcException(CommonErrorCode.INVALID_PARAMETER));

    final String groupName =
        _ndsAutoScaleServerlessMTMCapacitySvc.getServerlessGroupName(regionName, pIndex);
    final String poolName =
        _ndsAutoScaleServerlessMTMCapacitySvc.getNextPoolName(
            groupName, regionName, serverlessInstanceSize);

    return Response.ok(
            new JSONObject().put("groupName", groupName).put("poolName", poolName).toString())
        .build();
  }

  @GET
  @Path("/groups/{groupId}/dataLakes/{name}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY})
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataLakeTenant.GET")
  public Response getDataLakeTenant(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("name") final String pName,
      @Context AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {
    if (!pGroup.isActive()) {
      throw new SvcException(NDSErrorCode.GROUP_NOT_ACTIVE);
    }

    try {
      final NDSDataLakeTenantView view =
          _ndsDataLakePublicSvc.findByGroupAndNameAnyState(pGroup, pName);
      return new ApiResponseBuilder(pEnvelope).ok().content(view).build();
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME)) {
        throw ApiErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME.exception(
            pEnvelope, pGroup.getId(), pName);
      }
      return new ApiResponseBuilder(pEnvelope).badRequest(pE.getMessage()).build();
    }
  }

  @GET
  @Path("/groups/{groupId}/dataLakes/{name}/queryLogs.gz")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY, RoleSet.GLOBAL_CHARTS_ADMIN})
  @RequireCustomerGrantForEmployeeAccess()
  @Auth(endpointAction = "epa.global.NDSAdminResource.getDataLakeQueryLogs.GET")
  public Response getDataLakeQueryLogs(
      @Context final HttpServletResponse pResponse,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("name") final String pName,
      @Context AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @QueryParam("envelope") final Boolean pEnvelope,
      @QueryParam("startDate") final Long pStartTimestamp,
      @QueryParam("endDate") final Long pEndTimestamp) {

    // Default to last 4 hours of logs
    final LocalDateTime now = LocalDateTime.now();
    final LocalDateTime startDate =
        pStartTimestamp == null
            ? now.minusHours(4)
            : LocalDateTime.ofEpochSecond(pStartTimestamp, 0, ZoneOffset.UTC);
    final LocalDateTime endDate =
        pEndTimestamp == null ? now : LocalDateTime.ofEpochSecond(pEndTimestamp, 0, ZoneOffset.UTC);

    if (startDate.isAfter(endDate)) {
      throw ApiErrorCode.START_DATE_AFTER_END_DATE.exception(pEnvelope);
    }

    try {
      if (!ValidationUtils.isValidTenantName(pName)) {
        throw ApiErrorCode.DATA_LAKE_TENANT_NAME_INVALID.exception(pEnvelope, pName);
      }

      _ndsAdminSvc.auditForLogAccess(
          pGroup.getId(), SupportedLogs.DATA_LAKE, null, true, pAuditInfo, pUser);
      final StreamingOutput queryLogResult =
          _ndsDataLakePublicSvc.getDataLakeTenantQueryLogsForDates(
              pResponse,
              pGroup,
              pName,
              startDate.toInstant(ZoneOffset.UTC),
              endDate.toInstant(ZoneOffset.UTC),
              pAuditInfo,
              pRequest);

      return Response.ok(queryLogResult).build();
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_ARGUMENT)) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, pE.getMessage());
      }
      if (pE.getErrorCode().equals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME)) {
        throw ApiErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME.exception(
            pEnvelope, pGroup.getId(), pName);
      }
      return new ApiResponseBuilder(pEnvelope).badRequest(pE.getMessage()).build();
    }
  }

  @GET
  @Path("/groups/{groupId}/pipelines/{pipelineName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY})
  @Auth(endpointAction = "epa.global.NDSAdminResource.getIngestionPipeline.GET")
  public Response getIngestionPipeline(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("pipelineName") final String pPipelineName,
      @Context AuditInfo pAuditInfo,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {
    final IngestionPipelineView ingestionPipelineView =
        _ingestionPipelineUISvc.getIngestionPipelineViewForAdminUI(pGroupId, pPipelineName);
    return Response.ok(ingestionPipelineView).build();
  }

  @GET
  @Path("/groups/{groupId}/pipelines/{pipelineName}/lastNPipelineRuns")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY})
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLastNPipelineRuns.GET")
  public Response getLastNPipelineRuns(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("pipelineName") final String pPipelineName,
      @Context AuditInfo pAuditInfo,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {
    final List<IngestionPipelineRunView> lastNPipelineRuns =
        _ingestionPipelineUISvc.listIngestionPipelineRunsForAdminUI(
            pGroupId, pPipelineName, LAST_N_PIPELINE_RUNS);
    return Response.ok(lastNPipelineRuns).build();
  }

  @GET
  @Path("/onlineArchives/{onlineArchiveId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOnlineArchive.GET")
  public Response getOnlineArchive(@PathParam("onlineArchiveId") final ObjectId pArchiveId)
      throws SvcException {

    final OnlineArchive onlineArchive = _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);

    return Response.ok(onlineArchive.toDBObject()).build();
  }

  @GET
  @Path("/onlineArchives/finishedV3Migrations")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFinishedOAV3Migrations.GET")
  public Response getFinishedOAV3Migrations(
      @QueryParam("skip") @DefaultValue("0") final int pSkip,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("archiveId") final List<ObjectId> pArchiveIds,
      @QueryParam("state") final List<String> pStates) {

    final List<OnlineArchiveV3MigrationView> migrations =
        _onlineArchiveV3MigrationSvc
            .searchMigrationsForAdminUI(pArchiveIds, pStates, pLimit + 1, pSkip, true)
            .stream()
            .map(OnlineArchiveV3MigrationView::new)
            .collect(Collectors.toList());

    final List<OnlineArchiveV3MigrationView> data =
        migrations.size() < pLimit ? migrations : migrations.subList(0, pLimit);
    final boolean isLastPage = migrations.size() == 0 || migrations.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final OAV3MigrationContainer result = new OAV3MigrationContainer(data, isFirstPage, isLastPage);

    return Response.ok(result).build();
  }

  @GET
  @Path("/onlineArchives/unfinishedV3Migrations")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getUnfinishedOAV3Migrations.GET")
  public Response getUnfinishedOAV3Migrations(
      @QueryParam("skip") @DefaultValue("0") final int pSkip,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("archiveId") final List<ObjectId> pArchiveIds,
      @QueryParam("state") final List<String> pStates) {

    final List<OnlineArchiveV3MigrationView> migrations =
        _onlineArchiveV3MigrationSvc
            .searchMigrationsForAdminUI(pArchiveIds, pStates, pLimit + 1, pSkip, false)
            .stream()
            .map(OnlineArchiveV3MigrationView::new)
            .collect(Collectors.toList());

    final List<OnlineArchiveV3MigrationView> data =
        migrations.size() < pLimit ? migrations : migrations.subList(0, pLimit);
    final boolean isLastPage = migrations.size() == 0 || migrations.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final OAV3MigrationContainer result = new OAV3MigrationContainer(data, isFirstPage, isLastPage);

    return Response.ok(result).build();
  }

  @POST
  @Path("/onlineArchives/{archiveId}/startV3Migration")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.createOAV3Migration.POST")
  public Response createOAV3Migration(
      @PathParam("archiveId") final ObjectId pId,
      @FormParam("targetStorageRegion") final String pTargetStorageRegion,
      @FormParam("overrideShardKeys") final String pOverrideShardKeys)
      throws SvcException {

    final List<String> overrideShardKeys =
        pOverrideShardKeys.length() > 0
            ? new ArrayList<String>(Arrays.asList(pOverrideShardKeys.split(",")))
            : null;

    final OnlineArchiveV3MigrationView migration =
        new OnlineArchiveV3MigrationView(
            _onlineArchiveV3MigrationSvc.createOnlineArchiveV3Migration(
                pId, pTargetStorageRegion, overrideShardKeys));

    return Response.ok(migration).build();
  }

  @POST
  @Path("/onlineArchives/{archiveId}/updateArchiveDeleteAfterDate")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateArchiveDeleteAfterDate.POST")
  public Response updateArchiveDeleteAfterDate(
      @PathParam("archiveId") final ObjectId pId,
      @FormParam("newDeleteAfterDate") final String newDeleteAfterDateStr,
      @Context AuditInfo pAuditInfo)
      throws SvcException {

    // Parse the date string in ISO 8601 format
    final Date newDeleteAfterDate;
    try {
      final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      newDeleteAfterDate = dateFormat.parse(newDeleteAfterDateStr);
    } catch (Exception e) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "Invalid date format. Please follow ISO 8601 format (yyyy-MM-dd'T'HH:mm:ss.SSS'Z').");
    }

    _onlineArchiveSvc.updateArchiveDeleteAfterDate(pId, newDeleteAfterDate, pAuditInfo);

    return Response.ok().build();
  }

  @POST
  @Path("/onlineArchives/{archiveId}/recoverDeletedOA")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.recoverDeletedOnlineArchive.POST")
  public Response recoverDeletedOnlineArchive(@PathParam("archiveId") final ObjectId pId)
      throws SvcException {

    _onlineArchiveSvc.recoverDeletedOnlineArchive(pId);

    return Response.ok().build();
  }

  @POST
  @Path("/onlineArchives/{archiveId}/forcePauseArchive")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_TSE},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.forcePauseArchive.POST")
  public Response forcePauseArchive(
      @PathParam("archiveId") final ObjectId pId,
      @FormParam("forcePauseTicketId") final String pForcePauseTicketId)
      throws SvcException {

    _onlineArchiveSvc.forcePauseArchive(pId, pForcePauseTicketId);

    return Response.ok().build();
  }

  @GET
  @Path("/onlineArchives/v3Migrations/{migrationId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOAV3Migration.GET")
  public Response getOAV3Migration(@PathParam("migrationId") final ObjectId pId)
      throws SvcException {

    final OnlineArchiveV3MigrationView migration =
        new OnlineArchiveV3MigrationView(
            _onlineArchiveV3MigrationSvc
                .getMigration(pId)
                .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND, pId)));

    return Response.ok(migration).build();
  }

  @GET
  @Path("/onlineArchives/v3Migrations/{migrationId}/jobs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOAV3MigrationJobs.GET")
  public Response getOAV3MigrationJobs(@PathParam("migrationId") final ObjectId pId) {

    final OnlineArchiveV3MigrationJobsView migrationJobsView =
        new OnlineArchiveV3MigrationJobsView(
            _onlineArchiveV3MigrationSvc.getMigrationUploadJobsStateSummary(pId));

    return Response.ok(migrationJobsView).build();
  }

  @GET
  @Path("/onlineArchives/{onlineArchiveId}/history")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOnlineArchiveHistory.GET")
  public Response getOnlineArchiveHistory(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @Context AuditInfo pAuditInfo,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {

    _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);
    final List<OnlineArchiveHistory> archiveHistories =
        _onlineArchiveSvc.findNLastArchiveHistoryDescending(pArchiveId, LAST_N_ARCHIVE_HISTORIES);

    final List<DBObject> archiveHistoryDBObjects =
        archiveHistories.stream()
            .map(OnlineArchiveHistory::toDBObject)
            .collect(Collectors.toList());

    return Response.ok(archiveHistoryDBObjects).build();
  }

  @GET
  @Path("/onlineArchives/{onlineArchiveId}/expirationHistory")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOnlineArchiveExpirationHistory.GET")
  public Response getOnlineArchiveExpirationHistory(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @Context AuditInfo pAuditInfo,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {

    _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);
    final List<OnlineArchiveExpirationHistory> archiveExpirationHistories =
        _onlineArchiveSvc.findNLastArchiveExpirationHistoryDescending(
            pArchiveId, LAST_N_ARCHIVE_EXPIRATION_HISTORIES);
    final List<OnlineArchiveExpirationHistoryAdminView> views =
        archiveExpirationHistories.stream()
            .map(OnlineArchiveExpirationHistoryAdminView::new)
            .collect(Collectors.toList());
    return new ApiResponseBuilder(pEnvelope).ok().content(views).build();
  }

  @GET
  @Path("/onlineArchives/{onlineArchiveId}/lastNArchiveRuns")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getLastNArchiveRuns.GET")
  public Response getLastNArchiveRuns(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @Context AuditInfo pAuditInfo,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {

    _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);
    final List<OnlineArchiveRun> lastNArchiveRuns =
        _onlineArchiveSvc.getLastNRunsForArchive(pArchiveId, LAST_N_ARCHIVE_RUNS);
    final List<DBObject> archiveRunDBObjects =
        lastNArchiveRuns.stream().map(OnlineArchiveRun::toDBObject).collect(Collectors.toList());
    return Response.ok(archiveRunDBObjects).build();
  }

  @POST
  @Path("/onlineArchives/{onlineArchiveId}/overrides/{fieldName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_TSE},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateArchiveSettingOverride.POST")
  public Response updateArchiveSettingOverride(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @PathParam("fieldName") final String pFieldName,
      @FormParam("value") final String pValue)
      throws SvcException {
    _onlineArchiveSvc.updateOrResetArchiveSettingOverride(pArchiveId, pFieldName, pValue);
    return Response.ok().build();
  }

  @DELETE
  @Path("/onlineArchives/{onlineArchiveId}/numDeleteDocumentWorkers")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_TSE},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.resetNumDeleteDocumentWorkers.DELETE")
  public Response resetNumDeleteDocumentWorkers(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId) throws SvcException {

    _onlineArchiveSvc.resetNumDeleteDocumentWorkers(pArchiveId);
    return Response.ok().build();
  }

  @DELETE
  @Path("/onlineArchives/{onlineArchiveId}/dataSizeThreshold")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.resetDataSizeThreshold.DELETE")
  public Response resetDataSizeThreshold(@PathParam("onlineArchiveId") final ObjectId pArchiveId)
      throws SvcException {
    _onlineArchiveSvc.resetDataSizeThreshold(pArchiveId);
    return Response.ok().build();
  }

  @POST
  @Path("/onlineArchives/{onlineArchiveId}/numDeleteDocumentWorkers")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_TSE},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateNumDeleteDocumentWorkers.POST")
  public Response updateNumDeleteDocumentWorkers(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @FormParam("numDeleteDocumentWorkers") final Integer pNumDeleteDocumentWorkers)
      throws SvcException {

    _onlineArchiveSvc.updateNumDeleteDocumentWorkers(pArchiveId, pNumDeleteDocumentWorkers);
    return Response.ok().build();
  }

  @POST
  @Path("/onlineArchives/{onlineArchiveId}/dataSizeThreshold")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateDataSizeThreshold.POST")
  public Response updateDataSizeThreshold(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @FormParam("dataSizeThreshold") final Long pDataSizeThreshold)
      throws SvcException {
    _onlineArchiveSvc.updateDataSizeThreshold(pArchiveId, pDataSizeThreshold);
    return Response.ok().build();
  }

  @POST
  @Path("/onlineArchives/{onlineArchiveId}/dataSizeThresholdGracePeriod")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setDataSizeThresholdGracePeriod.POST")
  public Response setDataSizeThresholdGracePeriod(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @FormParam("gracePeriod") final Integer pGracePeriod)
      throws SvcException {
    _onlineArchiveSvc.updateDataSizeThresholdStartDate(pArchiveId, pGracePeriod);
    return Response.ok().build();
  }

  @POST
  @Path("/onlineArchives/{onlineArchiveId}/regenerateStorageConfigPreview")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.generateOAStorageConfPreview.POST")
  public Response generateOAStorageConfPreview(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId) throws SvcException {

    final OnlineArchive archive = _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);
    final Optional<OnlineArchiveDataLakeConfig> config =
        _onlineArchiveDataLakeSvc.getOnlineArchiveDataLakeConfig(
            archive.getGroupId(), archive.getClusterName());

    final NDSDataLakeStorageV1View newFederatedStorage =
        _onlineArchiveSvc.regenerateDataLakeStoragePreview(
            archive.getGroupId(), archive.getClusterName(), false);

    final NDSDataLakeStorageV1View newArchiveOnlyStorage =
        _onlineArchiveSvc.regenerateDataLakeStoragePreview(
            archive.getGroupId(), archive.getClusterName(), true);

    if (config.isPresent()) {
      final NDSDataLakeTenantId federatedTenantId = config.get().getDataLakeTenantId();
      final NDSDataLakeTenantId archiveOnlyTenantId = config.get().getArchiveOnlyDataLakeTenantId();

      final Group group = _groupSvc.findById(federatedTenantId.getGroupId());
      final NDSDataLakeTenantView existingFederatedView =
          _ndsDataLakePublicSvc.findByGroupAndNameAnyState(group, federatedTenantId.getName());

      final NDSDataLakeTenantView existingArchiveOnlyView =
          _ndsDataLakePublicSvc.findByGroupAndNameAnyState(group, archiveOnlyTenantId.getName());

      return Response.ok(
              new OnlineArchiveRegenerateConfigPreviewView(
                  existingFederatedView.getStorage(),
                  newFederatedStorage,
                  existingArchiveOnlyView.getStorage(),
                  newArchiveOnlyStorage))
          .build();
    } else {
      return Response.ok(
              new OnlineArchiveRegenerateConfigPreviewView(
                  null, newFederatedStorage, null, newArchiveOnlyStorage))
          .build();
    }
  }

  @POST
  @Path("/onlineArchives/{onlineArchiveId}/regenerateStorageConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.regenerateOAStorageConf.POST")
  public Response regenerateOAStorageConf(@PathParam("onlineArchiveId") final ObjectId pArchiveId)
      throws SvcException {

    final OnlineArchive archive = _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);

    _onlineArchiveSvc.updateClusterDataLakeStorageConfigs(
        archive.getGroupId(), archive.getClusterName());
    return Response.ok().build();
  }

  /** Returns estimated history stats of the specified archive in JSON format */
  @GET
  @Path("/onlineArchives/{onlineArchiveId}/archiveHistoryEstimatedStats")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOnlineArchiveHistoryEstimatedStats.GET")
  public Response getOnlineArchiveHistoryEstimatedStats(
      @PathParam("onlineArchiveId") final ObjectId pArchiveId,
      @Context AuditInfo pAuditInfo,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {
    _onlineArchiveSvc.getValidateOnlineArchive(pArchiveId);
    final OnlineArchiveHistoryEstimatedStatsView view =
        _onlineArchiveSvc.getOnlineArchiveHistoryEstimatedStatsView(pArchiveId);
    return Response.ok(view).build();
  }

  @POST
  @Path("/mtm/serverless/{orgId}/{cloudProvider}/{regionName}/setupServerless")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setupServerless.POST")
  public Response setupServerless(
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("orgId") final ObjectId pOrgId,
      @PathParam("cloudProvider") final String pCloudProvider,
      @PathParam("regionName") final String pRegionName,
      @QueryParam("backingInstanceSize")
          @DefaultValue(ServerlessNDSDefaults.DEFAULT_BACKING_INSTANCE_SIZE)
          final String pBackingInstanceSize,
      @QueryParam("groupId") final ObjectId pGroupId,
      @QueryParam("createServerlessInstance") final Boolean pCreateServerlessInstance,
      @QueryParam("mtmClusterName") final String pMTMClusterName,
      @QueryParam("mtmClusterVersion") final String pMTMClusterVersion,
      @QueryParam("envoyLogLevel") @DefaultValue(ServerlessNDSDefaults.ENVOY_LOG_LEVEL)
          final String pEnvoyLogLevel)
      throws SvcException {

    final Optional<ObjectId> groupIdOpt = ofNullable(pGroupId);
    final Optional<String> mtmClusterNameOpt = ofNullable(pMTMClusterName);
    final Optional<String> mtmClusterVersionOpt = ofNullable(pMTMClusterVersion);
    final CloudProvider cloudProvider = CloudProvider.findByName(pCloudProvider.toUpperCase());
    final RegionName regionName =
        RegionNameHelper.findByName(cloudProvider, pRegionName)
            .orElseThrow(
                () ->
                    ApiErrorCode.INVALID_REGION.exception(
                        false, pRegionName, cloudProvider.name()));
    final Pair<NDSServerlessMTMProfile, ServerlessMTMPool> profileAndServerlessMTMPool;
    try {
      profileAndServerlessMTMPool =
          _ndsSetupServerlessSvc.setupServerless(
              pOrgId,
              groupIdOpt,
              cloudProvider,
              regionName,
              pBackingInstanceSize,
              pAuditInfo,
              mtmClusterNameOpt,
              mtmClusterVersionOpt,
              pEnvoyLogLevel);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_CLOUD_PROVIDER)) {
        throw ApiErrorCode.PROVIDER_UNSUPPORTED.exception(false, pCloudProvider);
      } else if (pE.getErrorCode().equals(NDSErrorCode.INVALID_ORG_ID)) {
        throw ApiErrorCode.INVALID_ORG_ID.exception(false, pOrgId);
      } else if (pE.getErrorCode().equals(NDSErrorCode.GROUP_ALREADY_EXISTS)) {
        throw ApiErrorCode.GROUP_ALREADY_EXISTS.exception(false, pE.getMessageParams().get(0));
      } else { // Includes NDSErrorCode::INTERNAL and any other unhandled SvcException
        throw ApiErrorCode.UNEXPECTED_ERROR.exception(false);
      }
    }
    if (pCreateServerlessInstance == null || !pCreateServerlessInstance) {
      return Response.ok(
              _ndsServerlessUISvc.getServerlessMTMPoolView(
                  profileAndServerlessMTMPool.getRight().getId()))
          .build();
    }

    try {
      _ndsSetupServerlessSvc.createTestServerlessInstance(pUser, pAuditInfo, regionName);
    } catch (final SvcException pE) {
      handleNDSAdminResourceExceptions(pE);
    } catch (final IOException pE) {
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(false);
    }
    return Response.ok(
            _ndsServerlessUISvc.getServerlessMTMPoolView(
                profileAndServerlessMTMPool.getRight().getId()))
        .build();
  }

  private void handleNDSAdminResourceExceptions(final SvcException pE) {
    if (pE.getErrorCode()
        .equals(NDSErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION)) {
      throw ApiErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION
          .exception(false, pE.getMessageParams());
    } else if (pE.getErrorCode().equals(NDSErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED)) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.exception(false);
    } else {
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(false);
    }
  }

  @POST
  @Path("/serverless/updateSentinelInfo")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateSentinelAccessInformation.POST")
  public Response updateSentinelAccessInformation(
      @FormParam("newPassword") final String pNewPassword,
      @FormParam("updatedIpAccess") final String pUpdatedIpAccess)
      throws SvcException {

    if (!pNewPassword.isEmpty()) {
      _sentinelDBAccessInfoSvc.rotatePassword(pNewPassword);
    }

    if (!pUpdatedIpAccess.isEmpty()) {
      final List<String> ipAddresses =
          Arrays.stream(pUpdatedIpAccess.trim().split(",")).map(String::trim).toList();
      _sentinelDBAccessInfoSvc.setLivenessCheckerIPRanges(ipAddresses);
    }

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/mtm/clusters/{orgId}/setupFreeTier")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setupFreeTier.POST")
  public Response setupFreeTier(
      @PathParam("orgId") final ObjectId pOrgId,
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("backingInstanceSize") final String pBackingInstanceSize,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize)
      throws SvcException {

    final CloudProvider cloudProvider = CloudProvider.findByName(pCloudProvider.toUpperCase());
    final RegionName regionName =
        RegionNameHelper.findByName(cloudProvider, pRegionName)
            .orElseThrow(
                () ->
                    ApiErrorCode.INVALID_REGION.exception(
                        false, pRegionName, cloudProvider.name()));
    final InstanceSize instanceSize =
        InstanceSizeUtil.findByName(cloudProvider, pBackingInstanceSize)
            .orElseThrow(
                () ->
                    ApiErrorCode.INVALID_INSTANCE_SIZE.exception(
                        false, pBackingInstanceSize, cloudProvider.name()));

    Pair<NDSSharedMTMProfile, ClusterDescription> createdCluster = null;
    try {
      createdCluster =
          _ndsSetupFreeTierSvc.setupFreeTier(
              pOrgId,
              cloudProvider,
              regionName,
              instanceSize,
              FreeInstanceSize.valueOf(pTenantInstanceSize));
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_CLOUD_PROVIDER)) {
        throw ApiErrorCode.PROVIDER_UNSUPPORTED.exception(false, pCloudProvider);
      } else if (pE.getErrorCode().equals(NDSErrorCode.INVALID_ORG_ID)) {
        throw ApiErrorCode.INVALID_ORG_ID.exception(false, pOrgId);
      } else if (pE.getErrorCode().equals(NDSErrorCode.GROUP_ALREADY_EXISTS)) {
        throw ApiErrorCode.GROUP_ALREADY_EXISTS.exception(false, pE.getMessageParams().get(0));
      } else { // Includes NDSErrorCode::INTERNAL and any other unhandled SvcException
        throw ApiErrorCode.UNEXPECTED_ERROR.exception(false);
      }
    }

    return Response.ok(createdCluster.getValue().getClusterDescriptionId()).build();
  }

  @POST
  @Path("/mtm/clusters/{orgId}/setupFlex")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setupFlex.POST")
  public Response setupFlex(
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("orgId") final ObjectId pOrgId,
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("backingInstanceSize") final String pBackingInstanceSize,
      @FormParam("tenantInstanceSize") final String pTenantInstanceSize,
      @FormParam("shouldCreateFlexInstance") final boolean pShouldCreateFlexInstance) {
    final CloudProvider cloudProvider = CloudProvider.findByName(pCloudProvider.toUpperCase());
    final RegionName regionName =
        RegionNameHelper.findByName(cloudProvider, pRegionName)
            .orElseThrow(
                () ->
                    ApiErrorCode.INVALID_REGION.exception(
                        false, pRegionName, cloudProvider.name()));
    final InstanceSize instanceSize =
        InstanceSizeUtil.findByName(cloudProvider, pBackingInstanceSize)
            .orElseThrow(
                () ->
                    ApiErrorCode.INVALID_INSTANCE_SIZE.exception(
                        false, pBackingInstanceSize, cloudProvider.name()));

    final Pair<NDSFlexMTMProfile, ClusterDescription> createdMTMClusterInfo;
    try {
      createdMTMClusterInfo =
          _ndsSetupFlexSvc.setupFlex(
              pOrgId,
              cloudProvider,
              regionName,
              instanceSize,
              FlexInstanceSize.valueOf(pTenantInstanceSize));
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.INVALID_CLOUD_PROVIDER)) {
        throw ApiErrorCode.PROVIDER_UNSUPPORTED.exception(false, pCloudProvider);
      } else if (pE.getErrorCode().equals(NDSErrorCode.INVALID_ORG_ID)) {
        throw ApiErrorCode.INVALID_ORG_ID.exception(false, pOrgId);
      } else if (pE.getErrorCode().equals(NDSErrorCode.GROUP_ALREADY_EXISTS)) {
        throw ApiErrorCode.GROUP_ALREADY_EXISTS.exception(false, pE.getMessageParams().get(0));
      } else { // Includes NDSErrorCode::INTERNAL and any other unhandled SvcException
        throw ApiErrorCode.UNEXPECTED_ERROR.exception(false);
      }
    }

    // Create Flex instance, if required
    if (pShouldCreateFlexInstance) {
      try {
        _ndsSetupFlexSvc.createTestFlexInstance(pUser, pAuditInfo, regionName);
      } catch (final SvcException pE) {
        handleNDSAdminResourceExceptions(pE);
      } catch (final IOException pE) {
        throw ApiErrorCode.UNEXPECTED_ERROR.exception(false);
      }
    }
    final JSONObject response =
        new JSONObject()
            .put("mtmClusterId", createdMTMClusterInfo.getRight().getClusterDescriptionId());
    response.put("mtmGroupId", createdMTMClusterInfo.getRight().getGroupId());
    response.put("mtmClusterName", createdMTMClusterInfo.getRight().getName());
    return Response.ok(response.toString()).build();
  }

  @PUT
  @Path("/cps/regionDown")
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addRegionDownConfig.PUT")
  public Response addRegionDownConfig(
      @Context final AuditInfo pAuditInfo,
      @FormParam("cloudProvider") final CloudProvider pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("reason") final String pReason) {
    _regionDownConfigDao.create(
        pCloudProvider, pRegionName, new Date(), pAuditInfo.getUsername(), pReason);
    LOG.info(
        "Added region down for region {}, cloud provider {}, by user {}, with reason {}",
        pRegionName,
        pCloudProvider.name(),
        pAuditInfo.getUsername(),
        pReason);
    return Response.ok().build();
  }

  @DELETE
  @Path("/cps/regionDown/{cloudProvider}/{regionName}")
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.removeRegionDownConfig.DELETE")
  public Response removeRegionDownConfig(
      @Context final AuditInfo pAuditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("regionName") final String pRegionName) {
    _regionDownConfigDao.delete(pCloudProvider, pRegionName);
    LOG.info(
        "Deleted region down for region {}, cloud provider {}, by user {}",
        pRegionName,
        pCloudProvider.name(),
        pAuditInfo.getUsername());
    return Response.ok().build();
  }

  @GET
  @Path("/cps/regionDown")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getRegionDownConfigs.GET")
  public Response getRegionDownConfigs() {
    final List<RegionDownConfigView> regionDownConfigs =
        _regionDownConfigDao.findAllConfigs().stream()
            .map(RegionDownConfigView::new)
            .collect(Collectors.toList());
    return Response.ok(regionDownConfigs).build();
  }

  @GET
  @Path("/cps/regionDown/getRegions/{cloudProvider}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getCloudProviderRegionsForRegionDown.GET")
  public Response getCloudProviderRegionsForRegionDown(
      @PathParam("cloudProvider") final CloudProvider pCloudProvider) {
    if (pCloudProvider.equals(CloudProvider.AWS)) {
      return Response.ok(
              Arrays.stream(AWSRegionName.values())
                  .map(RegionView::new)
                  .collect(Collectors.toList()))
          .build();
    } else if (pCloudProvider.equals(CloudProvider.GCP)) {
      return Response.ok(
              Arrays.stream(GCPRegionName.values())
                  .map(RegionView::new)
                  .collect(Collectors.toList()))
          .build();
    } else if (pCloudProvider.equals(CloudProvider.AZURE)) {
      return Response.ok(
              Arrays.stream(AzureRegionName.values())
                  .map(RegionView::new)
                  .collect(Collectors.toList()))
          .build();
    }
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/tenantEndpointServiceDeployment")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getTenantEndpointServiceDeployment.GET")
  public Response getTenantEndpointServiceDeployment(@Context final Group pGroup) throws Exception {
    final DBObject groupDoc = _ndsGroupSvc.getNDSGroupById(pGroup.getId());
    if (groupDoc == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final NDSGroup ndsGroup = new NDSGroup((BasicDBObject) groupDoc);
    Optional<CloudProviderContainer> cloudProviderContainer =
        ndsGroup.getCloudProviderContainerWithTenantEndpointServiceDeployment();
    if (cloudProviderContainer.isEmpty()) {
      return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
    }
    return Response.ok(
            cloudProviderContainer
                .get()
                .getTenantEndpointServiceDeployment()
                .orElseThrow()
                .toDBObject()
                .toJson(JsonWriterSettings.builder().outputMode(JsonMode.STRICT).build()))
        .build();
  }

  @GET
  @Path("/groups/{groupId}/multiTenantEndpointServices")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMultiTenantEndpointServicesForGroup.GET")
  public Response getMultiTenantEndpointServicesForGroup(
      @PathParam("groupId") final ObjectId pGroupId) throws Exception {
    final List<MultiTenantEndpointService> multiTenantEndpointServices =
        _multiTenantEndpointServiceSvc.findMultiTenantEndpointServicesByGroupId(pGroupId);

    return Response.ok(
            multiTenantEndpointServices.stream()
                .map(MultiTenantEndpointServiceView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Path("groups/{groupId}/hosts/{hostname}/chefServerStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY})
  @Auth(endpointAction = "epa.global.NDSAdminResource.getChefServerStatus.GET")
  public Response getChefServerStatus(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostname") final String pHostname,
      @QueryParam("envelope") final Boolean pEnvelope) {
    Optional<String> hostnameForAgents =
        _searchInstanceSvc.isSearchInstanceHostname(pHostname)
            ? _searchInstanceSvc.findHostnameForAgents(pHostname)
            : _ndsLookupSvc.getHostnameForAgents(pHostname, pGroupId);

    return hostnameForAgents
        .flatMap(_chefServerStatusSvc::findByHostname)
        .map(ApiPrivateChefServerStatusView::new)
        .map(view -> new ApiResponseBuilder(pEnvelope).ok().content(view).build())
        .orElseThrow(() -> ApiErrorCode.RESOURCE_NOT_FOUND.exception(pEnvelope, "status"));
  }

  // CRUD operations for PlanExecutorJobPriorityConfigValues in admin UI
  @POST
  @Path("ndsJobPriorityConfigs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addPlanExecutorJobPriorityConfigValue.POST")
  public Response addPlanExecutorJobPriorityConfigValue(
      @Context final AuditInfo pAuditInfo, final PlanExecutorJobPriorityConfigValueView pConfigView)
      throws SvcException {
    if (!pConfigView.isValid()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var configValue = pConfigView.toPlanExecutorJobPriorityConfigValue();
    final var configValueId = _planExecutorJobPriorityConfigValueSvc.createNewConfig(configValue);

    final Builder builder = new Builder(NDSAudit.Type.JOB_PRIORITY_CONFIG_CREATED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(
        List.of(
            new AuditDescription("id", configValueId.toHexString()),
            new AuditDescription("name", configValue.getName())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("ndsJobPriorityConfigs")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllPlanExecutorJobPriorityConfigValues.GET")
  public Response getAllPlanExecutorJobPriorityConfigValues() {
    final List<PlanExecutorJobPriorityConfigValueView> allConfigViews =
        _planExecutorJobPriorityConfigValueSvc.findAllPlanExecutorJobPriorityConfigValueViews();

    return Response.ok().entity(allConfigViews).build();
  }

  @PUT
  @Path("ndsJobPriorityConfigs/{configId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updatePlanExecutorJobPriorityConfigValue.PUT")
  public Response updatePlanExecutorJobPriorityConfigValue(
      @Context final AuditInfo pAuditInfo,
      @PathParam("configId") final ObjectId pConfigId,
      final PlanExecutorJobPriorityConfigValueView pConfigView)
      throws SvcException {
    if (!pConfigView.isValid()) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var configValue = pConfigView.toPlanExecutorJobPriorityConfigValue();
    _planExecutorJobPriorityConfigValueSvc.replace(pConfigId, configValue);

    final Builder builder = new Builder(NDSAudit.Type.JOB_PRIORITY_CONFIG_UPDATED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(
        List.of(
            new AuditDescription("id", pConfigId.toHexString()),
            new AuditDescription("name", configValue.getName())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @DELETE
  @Path("ndsJobPriorityConfigs/{configId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deletePlanExecutorJobPriorityConfigValue.DELETE")
  public Response deletePlanExecutorJobPriorityConfigValue(
      @Context final AuditInfo pAuditInfo, @PathParam("configId") final ObjectId pConfigId) {
    _planExecutorJobPriorityConfigValueSvc.delete(pConfigId);

    final Builder builder = new Builder(NDSAudit.Type.JOB_PRIORITY_CONFIG_DELETED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(List.of(new AuditDescription("id", pConfigId.toHexString())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  // CRUD operations for PlanExecutorJobPrioritySettings in admin UI
  @POST
  @Path("ndsJobPrioritySettings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.addPlanExecutorJobPrioritySetting.POST")
  public Response addPlanExecutorJobPrioritySetting(
      @Context final AuditInfo pAuditInfo, final PlanExecutorJobPrioritySettingView pSettingView)
      throws SvcException {
    if (!isValidSetting(pSettingView)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var setting = pSettingView.toPlanExecutorJobPrioritySetting();
    final var settingId = _ndsPlanExecutorJobPrioritySvc.createNewJobPrioritySetting(setting);

    final Builder builder = new Builder(NDSAudit.Type.JOB_PRIORITY_SETTING_CREATED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(
        List.of(
            new AuditDescription("id", settingId.toHexString()),
            new AuditDescription("name", setting.getName())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("ndsAcmeProviderStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getACMEProviderStatus.GET")
  public Response getACMEProviderStatus() {
    final List<AcmeCaStatusView> allSettingsViews =
        _ndsacmeFailoverSvc.findAllACMECAStatusSettingsViews();
    return Response.ok().entity(allSettingsViews).build();
  }

  @GET
  @Path("ndsJobPrioritySettings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllPlanExecutorJobPrioritySettings.GET")
  public Response getAllPlanExecutorJobPrioritySettings() {
    final List<PlanExecutorJobPrioritySettingView> allSettingsViews =
        _ndsPlanExecutorJobPrioritySvc.findAllPlanExecutorJobPrioritySettingsViews();

    return Response.ok().entity(allSettingsViews).build();
  }

  @PUT
  @Path("ndsJobPrioritySettings/{settingId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updatePlanExecutorJobPrioritySetting.PUT")
  public Response updatePlanExecutorJobPrioritySetting(
      @Context final AuditInfo pAuditInfo,
      @PathParam("settingId") final ObjectId pSettingId,
      final PlanExecutorJobPrioritySettingView pSettingView)
      throws SvcException {
    if (!isValidSetting(pSettingView)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    final var setting = pSettingView.toPlanExecutorJobPrioritySetting();
    _ndsPlanExecutorJobPrioritySvc.updateJobPrioritySetting(pSettingId, setting);

    final Builder builder = new Builder(NDSAudit.Type.JOB_PRIORITY_SETTING_UPDATED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(
        List.of(
            new AuditDescription("id", pSettingId.toHexString()),
            new AuditDescription("name", setting.getName())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @DELETE
  @Path("ndsJobPrioritySettings/{settingId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deletePlanExecutorJobPrioritySetting.DELETE")
  public Response deletePlanExecutorJobPrioritySetting(
      @Context final AuditInfo pAuditInfo, @PathParam("settingId") final ObjectId pSettingId) {
    _ndsPlanExecutorJobPrioritySvc.deletePlanExecutorJobPrioritySetting(pSettingId);

    final Builder builder = new Builder(NDSAudit.Type.JOB_PRIORITY_SETTING_DELETED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(List.of(new AuditDescription("id", pSettingId.toHexString())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/orphanedItems")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOrphanedItemsByGroupId.GET")
  public Response getOrphanedItemsByGroupId(@PathParam("groupId") final ObjectId pGroupId) {
    final List<OrphanedItemView> orphanedItemViews =
        _orphanedItemSvc.findByProjectId(pGroupId).stream().map(OrphanedItemView::new).toList();

    return Response.ok(orphanedItemViews).build();
  }

  @GET
  @Path("/clusters/{clusterUniqueId}/orphanedItems")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOrphanedItemsByClusterUniqueId.GET")
  public Response getOrphanedItemsByClusterUniqueId(
      @PathParam("clusterUniqueId") final ObjectId pClusterId) {
    final List<OrphanedItemView> orphanedItemViews =
        _orphanedItemSvc.findByClusterUniqueId(pClusterId).stream()
            .map(OrphanedItemView::new)
            .toList();

    return Response.ok(orphanedItemViews).build();
  }

  @PATCH
  @Path("/clusters/{clusterUniqueId}/orphanedItems/extend")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.extendOrphanedItemsExpirationForCluster.PATCH")
  public Response extendOrphanedItemsExpirationForCluster(
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @FormParam("extensionDays") final int pExtensionDays) {
    _orphanedItemSvc.extendOrphanedItemsExpirationForCluster(pClusterUniqueId, pExtensionDays);
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/maintenanceHistories")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMaintenanceHistoryByGroupId.GET")
  public Response getMaintenanceHistoryByGroupId(
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("clusterName") final String clusterName,
      @QueryParam("maintenanceType") final MaintenanceType pMaintenanceType,
      @QueryParam("startMaintenanceDateTime") final Long startMaintenanceDateTimeMillis,
      @QueryParam("endMaintenanceDateTime") final Long endMaintenanceDateTimeMillis,
      @QueryParam("state") final NdsMaintenanceHistory.State pState) {

    final Date startMaintenanceDateTruncated =
        ofNullable(startMaintenanceDateTimeMillis)
            .map(d -> DateUtils.truncate(new Date(d), Calendar.DAY_OF_MONTH))
            .orElse(null);

    final Date endMaintenanceDateTruncated =
        ofNullable(endMaintenanceDateTimeMillis)
            .map(d -> DateUtils.addDays(DateUtils.truncate(new Date(d), Calendar.DAY_OF_MONTH), 1))
            .orElse(null);

    final List<NdsMaintenanceHistoryView> maintenanceHistoryViews =
        _ndsMaintenanceHistorySvc
            .findMaintenancesByGroupIdClusterNameStateAndMaintenanceTypes(
                pGroupId,
                clusterName,
                pState,
                ofNullable(pMaintenanceType).map(List::of).orElse(null),
                startMaintenanceDateTruncated,
                endMaintenanceDateTruncated,
                MAINTENANCE_HISTORY_LIMIT)
            .stream()
            .map(NdsMaintenanceHistoryView::new)
            .collect(Collectors.toList());

    return Response.ok(maintenanceHistoryViews).build();
  }

  @POST
  @Path("/groups/{groupId}/maintenanceReset")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.resetMaintenance.POST")
  public Response resetMaintenance(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId) {
    final NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new UncheckedSvcException(NDSErrorCode.INVALID_GROUP_ID));
    _ndsGroupMaintenanceSvc.resetMaintenanceAfterCompletionForGroup(ndsGroup, LOG);

    final NDSAudit.Builder builder = new NDSAudit.Builder(NDSAudit.Type.MAINTENANCE_RESET_BY_ADMIN);
    builder.hidden(true);
    builder.groupId(pGroupId);
    builder.auditInfo(pAuditInfo);
    _auditSvc.saveAuditEvent(builder.build());
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/maintenanceWindowDetails")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMaintenanceWindowDetails.GET")
  public Response getMaintenanceWindowDetails(@PathParam("groupId") final ObjectId pGroupId) {
    final NDSGroupMaintenanceWindow ndsGroupMaintenanceWindow =
        _ndsGroupSvc
            .find(pGroupId)
            .map(NDSGroup::getMaintenanceWindow)
            .orElseThrow(() -> new UncheckedSvcException(NDSErrorCode.INVALID_GROUP_ID));
    final Group group = Objects.requireNonNull(_groupSvc.findById(pGroupId));
    final String groupTimeZoneId = Optional.ofNullable(group.getDefaultTimeZoneId()).orElse("UTC");

    final List<SchedulingMetadataView> schedulingMetadata =
        Stream.of(SchedulingBehavior.values())
            .map(
                schedulingBehavior -> {
                  final SimpleDateFormat dateFormatInGroupTimeZone =
                      new SimpleDateFormat(MAINTENANCE_DISPLAY_TIME_FORMAT_PATTERN);
                  dateFormatInGroupTimeZone.setTimeZone(TimeZone.getTimeZone(groupTimeZoneId));

                  final SimpleDateFormat dateFormatInUTC =
                      new SimpleDateFormat(MAINTENANCE_DISPLAY_TIME_FORMAT_PATTERN);
                  dateFormatInUTC.setTimeZone(TimeZone.getTimeZone("UTC"));

                  final Optional<Date> nextMaintenanceDate =
                      _ndsMaintenanceDateCalculationUtil
                          .getNextUserDefinedMaintenanceStartDateTime(
                              ndsGroupMaintenanceWindow, groupTimeZoneId)
                          .map(Calendar::getTime);

                  return new SchedulingMetadataView(
                      schedulingBehavior,
                      nextMaintenanceDate.map(dateFormatInGroupTimeZone::format).orElse(null),
                      nextMaintenanceDate.map(dateFormatInUTC::format).orElse(null));
                })
            .toList();

    return Response.ok(
            NDSGroupMaintenanceWindowAdminView.builder()
                .from(ndsGroupMaintenanceWindow)
                .setGroupId(group.getId())
                .setGroupName(group.getName())
                .setSchedulingMetadata(schedulingMetadata)
                .setGroupTimeZoneId(groupTimeZoneId)
                .build())
        .build();
  }

  @POST
  @Path("groups/{groupId}/clusters/{clusterName}/adminClusterLocks")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.acquireAdminClusterLock.POST")
  public Response acquireAdminClusterLock(
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("reason") final String pReason,
      @FormParam("disableAutoHealing") final boolean pDisableAutoHealing)
      throws SvcException {
    final AdminClusterLock lock =
        _adminClusterLockSvc.acquireLock(
            pAppUser, pGroupId, pClusterName, new Date(), pReason, pDisableAutoHealing, pAuditInfo);
    return Response.ok(lock).build();
  }

  @POST
  @Path("/hostnames/{hostname}/adminClusterLocks")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.acquireAdminClusterLock.POST")
  public Response acquireAdminClusterLock(
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("hostname") final String pHostname,
      @FormParam("reason") final String pReason)
      throws SvcException {
    final ClusterDescriptionId clusterDescriptionId =
        _replicaSetHardwareSvc
            .getClusterDescriptionIdForHostname(pHostname, null)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_CLUSTER));
    final AdminClusterLock lock =
        _adminClusterLockSvc.acquireLock(
            pAppUser,
            clusterDescriptionId.getGroupId(),
            clusterDescriptionId.getClusterName(),
            new Date(),
            pReason,
            pAuditInfo);
    return Response.ok(lock).build();
  }

  @GET
  @Path("hostnames/{hostname}/adminClusterLocks")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAdminClusterLockByHostname.GET")
  public Response getAdminClusterLockByHostname(@PathParam("hostname") final String pHostname)
      throws SvcException {
    ReplicaSetHardware replicaSetHardware =
        _replicaSetHardwareSvc
            .getReplicaSetHardwareForHostname(pHostname, null)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_CLUSTER));

    final ObjectId groupId = replicaSetHardware.getGroupId();
    final String clusterName = replicaSetHardware.getClusterName();
    final Optional<AdminClusterLock> lock =
        _adminClusterLockSvc.findByClusterDescriptionId(
            new ClusterDescriptionId(clusterName, groupId));
    if (lock.isPresent()) {
      return Response.ok(lock.get()).build();
    } else {
      return Response.ok(EMPTY_JSON_OBJECT).build();
    }
  }

  @DELETE
  @Path("groups/{groupId}/clusters/{clusterName}/adminClusterLocks")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.releaseAdminClusterLock.DELETE")
  public Response releaseAdminClusterLock(
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final AdminClusterLock lock =
        _adminClusterLockSvc.releaseLock(pAppUser, pGroupId, pClusterName, pAuditInfo);
    return Response.ok(lock).build();
  }

  @DELETE
  @Path("/groups/{groupId}/clusters/{clusterName}/queuedActions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.deleteQueuedAction.DELETE")
  public Response deleteQueuedAction(
      @Context AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("queuedActionId") final ObjectId pQueuedActionId)
      throws SvcException {

    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(NDSErrorCode.CLUSTER_NAME_INVALID);
    }

    _adminActionSvc.cancelQueuedAction(pQueuedActionId);

    final QueuedAdminAction action =
        _adminActionSvc
            .getQueuedAdminAction(pQueuedActionId)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));

    final Builder builder = new Builder(QUEUED_ADMIN_ACTION_CANCELLED);
    builder.auditInfo(pAuditInfo);
    builder.clusterName(pClusterName);
    builder.groupId(pGroupId);
    builder.hostname(action.getHostname());
    builder.queuedAdminActionId(action.getId());
    builder.queuedAdminActionType(action.getActionType());
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());
    return Response.ok().build();
  }

  @PATCH
  @Path("groups/{groupId}/clusters/{clusterName}/adminClusterLocks")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.renewAdminClusterLock.PATCH")
  public Response renewAdminClusterLock(
      @Context AppUser pAppUser,
      @Context AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final AdminClusterLock lock =
        _adminClusterLockSvc.renewLock(pAppUser, pGroupId, pClusterName, pAuditInfo);
    return Response.ok(lock).build();
  }

  @GET
  @Path("groups/{groupId}/clusters/{clusterName}/adminClusterLocks")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAdminClusterLockByClusterDescriptionId.GET")
  public Response getAdminClusterLockByClusterDescriptionId(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final ClusterDescriptionId clusterDescriptionId =
        new ClusterDescriptionId(pClusterName, pGroupId);
    final Optional<AdminClusterLock> result =
        _adminClusterLockSvc.findByClusterDescriptionId(clusterDescriptionId);
    if (result.isPresent()) {
      return Response.ok(result.get()).build();
    } else {
      return Response.ok(EMPTY_JSON_OBJECT).build();
    }
  }

  @GET
  @Path("mtm/clusters/compactions/recommendations")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMsForCompaction.GET")
  public Response getMTMsForCompaction(
      @QueryParam("cloudProvider") final CloudProvider pCloudProvider,
      @QueryParam("limit") final int pLimit,
      @QueryParam("regionName") final String pRegionName) {
    final RegionName region =
        RegionNameHelper.findByNameOrValue(pCloudProvider, pRegionName).orElse(null);
    final List<SharedMTMClusterView> clusters =
        _mtmCompactionSvc.findMTMClustersToCompact(pCloudProvider, pLimit, region).stream()
            .map(SharedMTMClusterView::new)
            .collect(Collectors.toList());
    return Response.ok(clusters).build();
  }

  @GET
  @Path("mtm/clusters/compactions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMCompactions.GET")
  public Response getMTMCompactions(@QueryParam("status") final List<String> pStatuses) {
    final MTMCompaction.Status[] statuses =
        pStatuses.stream()
            .map(status -> MTMCompaction.Status.valueOf(status))
            .toArray(MTMCompaction.Status[]::new);
    final List<MTMCompactionView> compactions =
        _mtmCompactionSvc.findCompactionsByStatuses(statuses).stream()
            .map(MTMCompactionView::new)
            .collect(Collectors.toList());
    return Response.ok(compactions).build();
  }

  @GET
  @Path("mtm/clusters/finishedCompactions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFinishedMTMCompactions.GET")
  public Response getFinishedMTMCompactions(@QueryParam("limit") final Integer pLimit) {
    final List<MTMCompactionView> compactions =
        _mtmCompactionSvc.findFinishedCompactions(ofNullable(pLimit)).stream()
            .map(MTMCompactionView::new)
            .collect(Collectors.toList());
    return Response.ok(compactions).build();
  }

  @POST
  @Path("mtm/clusters/compactions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.initiateMTMCompactions.POST")
  public Response initiateMTMCompactions(final List<ClusterDescriptionId> pClusterDescriptionIds)
      throws SvcException {
    if (!_appSettings.isMTMCompactionEnabled()) {
      throw new SvcException(NDSErrorCode.UNSUPPORTED, "MTM compaction is not enabled");
    }
    for (final ClusterDescriptionId id : pClusterDescriptionIds) {
      final Optional<MTMCompaction> compaction =
          _mtmCompactionSvc.findUnfinishedCompactionByMTMClusterDescriptionId(id);
      if (compaction.isPresent()) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT,
            "Provided MTMCluster is already undergoing compaction, please select other MTMs");
      }
      _mtmCompactionSvc.initiateCompaction(id);
      _ndsGroupDao.setPlanASAP(id.getGroupId());
    }
    return Response.ok().build();
  }

  @PATCH
  @Path("mtm/clusters/compactions/{mtmCompactionId}")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setMTMCompactionState.PATCH")
  public Response setMTMCompactionState(
      @PathParam("mtmCompactionId") final ObjectId pMTMCompactionId,
      @FormParam("status") final MTMCompaction.Status pStatus)
      throws SvcException {
    switch (pStatus) {
      case PROCESSING:
        _mtmCompactionSvc.resumeCompaction(pMTMCompactionId);
        break;
      case PAUSED:
        _mtmCompactionSvc.pauseCompaction(pMTMCompactionId);
        break;
      default:
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT);
    }
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("mtm/clusters/compactions/{mtmCompactionId}")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMCompaction.GET")
  public Response getMTMCompaction(@PathParam("mtmCompactionId") final ObjectId pMTMCompactionId)
      throws SvcException {
    final Optional<MTMCompaction> result =
        _mtmCompactionSvc.findMTMCompactionById(pMTMCompactionId);
    if (result.isPresent()) {
      return Response.ok(new MTMCompactionView(result.get())).build();
    }
    throw new SvcException(CommonErrorCode.NOT_FOUND);
  }

  @GET
  @Path("mtm/clusters/compactions/{groupId}/{clusterName}")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getMTMCompaction.GET")
  public Response getMTMCompaction(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Optional<MTMCompaction> result =
        _mtmCompactionSvc.findMTMCompactionByMTMClusterDescriptionId(
            new ClusterDescriptionId(pClusterName, pGroupId));
    if (result.isPresent()) {
      return Response.ok(new MTMCompactionView(result.get())).build();
    }
    throw new SvcException(CommonErrorCode.NOT_FOUND);
  }

  @PATCH
  @Path("mtm/clusters/compactions/{mtmCompactionId}/tenants/{tenantUniqueId}")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateTenantForCompaction.PATCH")
  public Response updateTenantForCompaction(
      @PathParam("mtmCompactionId") final ObjectId pMTMCompactionId,
      @PathParam("tenantUniqueId") final ObjectId pTenantUniqueId,
      @FormParam("action") final CompactTenantAction pAction)
      throws SvcException {
    switch (pAction) {
      case RETRY:
        _mtmCompactionSvc.setTenantRetry(pMTMCompactionId, pTenantUniqueId);
        return Response.ok(EMPTY_JSON_OBJECT).build();
      case MARK_COMPLETED:
        _mtmCompactionSvc.markFailedTenantAsCompacted(pMTMCompactionId, pTenantUniqueId);
        return Response.ok(EMPTY_JSON_OBJECT).build();
      default:
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT);
    }
  }

  private CloudProviderCheckCapacityRequestSvc<
          ? extends InstanceCapacitySpec, ? extends CheckResult>
      getCheckCapacityRequestSvc(String pCloudProvider) throws SvcException {
    // NB: cloud provider formerly was not a concept in the system prior to the introduction
    // of Azure capacity reservations, when only the AWS capacity reservations implementation was
    // available. To be backwards compatible, a missing cloud provider is equivalent to AWS
    final CloudProvider cloudProvider =
        ofNullable(pCloudProvider).map(CloudProvider::valueOf).orElse(CloudProvider.AWS);

    return switch (cloudProvider) {
      case AWS -> _awsCheckCapacityRequestSvc;
      case AZURE -> _azureCheckCapacityRequestSvc;
      default -> throw new SvcException(CommonErrorCode.BAD_REQUEST, "cloudProvider");
    };
  }

  @GET
  @Path("/capacity/requests")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAllNonReleasedRequests.GET")
  public Response getAllNonReleasedRequests() {
    final List<CheckCapacityRequestView> requests =
        _genericCheckCapacityRequestSvc.findAllNonReleasedRequests().stream()
            .map(CheckCapacityRequestViewMapper::viewFromRequest)
            .toList();

    return Response.ok(requests).build();
  }

  @POST
  @Path("/groups/{groupId}/capacity/check")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitCheckCapacityRequest.POST")
  public Response submitCheckCapacityRequest(
      @Context final Group pGroup, final TargetedODCRRequestView pTargetedODCRRequestView)
      throws SvcException {

    final ObjectId requestId =
        getCheckCapacityRequestSvc(pTargetedODCRRequestView.getCloudProvider())
            .createNewRequest(pGroup.getId(), pTargetedODCRRequestView, 0);

    return Response.ok(new BasicDBObject("requestId", requestId)).build();
  }

  @POST
  @Path("/groups/{groupId}/capacity/hold/{holdDays}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.submitHoldCapacityRequest.POST")
  public Response submitHoldCapacityRequest(
      @Context final Group pGroup,
      @PathParam("holdDays") final int holdDays,
      final TargetedODCRRequestView pTargetedODCRRequestView)
      throws SvcException {

    if (pTargetedODCRRequestView == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "Invalid targeted ODCR request");
    }

    if (holdDays < 1) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "Hold days, must be a positive non-zero value");
    }

    final ObjectId requestId =
        getCheckCapacityRequestSvc(pTargetedODCRRequestView.getCloudProvider())
            .createNewRequest(pGroup.getId(), pTargetedODCRRequestView, holdDays);

    return Response.ok(new BasicDBObject("requestId", requestId)).build();
  }

  @GET
  @Path("/groups/{groupId}/capacity/requests/{requestId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getCheckCapacityRequest.GET")
  public Response getCheckCapacityRequest(
      @Context final Group pGroup, @PathParam("requestId") final ObjectId pRequestId)
      throws SvcException {
    try {
      final CheckCapacityRequest<? extends InstanceCapacitySpec, ? extends CheckResult> request =
          _genericCheckCapacityRequestSvc.findRequest(pRequestId);
      if (request.getCapacityHold().isPresent() && !request.getGroupId().equals(pGroup.getId())) {
        return Response.status(Response.Status.FORBIDDEN).build();
      }
      return Response.ok(CheckCapacityRequestViewMapper.viewFromRequest(request)).build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @GET
  @Path("/groups/{groupId}/capacity/reservations")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getAssistedCapacityReservationsForGroup.GET")
  public Response getAssistedCapacityReservationsForGroup(@Context final Group pGroup)
      throws SvcException {
    try {
      final NDSGroup ndsGroup =
          _ndsGroupSvc
              .find(pGroup.getId())
              .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
      final List<ScheduledAWSInstanceCapacitySpecView> reservations =
          ndsGroup.getCloudProviderContainers().stream()
              .filter(AWSCloudProviderContainer.class::isInstance)
              .map(AWSCloudProviderContainer.class::cast)
              .flatMap(
                  c -> {
                    try {
                      final List<CapacityReservation> filteredReservations =
                          _awsApiSvc.getCapacityReservations(
                              c.getAWSAccountId(),
                              c.getRegion(),
                              List.of(
                                  new Filter(
                                      "state",
                                      ScheduledAWSInstanceCapacitySpecView.UI_FILTER_STATES)),
                              LOG);
                      LOG.debug(
                          "found {} reservations with state in {} for account {} and region {}",
                          filteredReservations.size(),
                          ScheduledAWSInstanceCapacitySpecView.UI_FILTER_STATES,
                          c.getAWSAccountId(),
                          c.getRegion());
                      return filteredReservations.stream()
                          .filter(
                              ScheduledAWSInstanceCapacitySpecView::isScheduledCapacityReservation)
                          .map(
                              r ->
                                  new ScheduledAWSInstanceCapacitySpecView(
                                      c.getAWSAccountId(), c.getRegion(), r));
                    } catch (final Exception pE) {
                      if (pE instanceof AWSApiException
                          && ((AWSApiException) pE)
                              .getErrorCode()
                              .equals(CommonErrorCode.NOT_FOUND)) {
                        LOG.debug(
                            "No capacity reservations found with state in {} for account {} and"
                                + " region {}",
                            ScheduledAWSInstanceCapacitySpecView.UI_FILTER_STATES,
                            c.getAWSAccountId(),
                            c.getRegion());
                      } else {
                        LOG.error(
                            "Exception getting capacity reservations for account {} and region {}",
                            c.getAWSAccountId(),
                            c.getRegion(),
                            pE);
                      }
                      return Stream.empty();
                    }
                  })
              .collect(Collectors.toList());
      return Response.ok(_awsCheckCapacityRequestSvc.filterOutHeldReservations(reservations))
          .build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @POST
  @Path("/groups/{groupId}/capacity/reservations/accept")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.acceptAssistedCapacityReservations.POST")
  public Response acceptAssistedCapacityReservations(
      @Context final Group pGroup,
      final List<ScheduledAWSInstanceCapacitySpecView> pScheduledAWSInstanceCapacitySpecViews)
      throws SvcException {
    try {
      final ObjectId requestId =
          _awsCheckCapacityRequestSvc.acceptAssistedReservationsAndCreateRequest(
              pGroup.getId(), pScheduledAWSInstanceCapacitySpecViews);
      return Response.ok(new BasicDBObject("requestId", requestId)).build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @PATCH
  @Path("/groups/{groupId}/capacity/requests/{requestId}/accept")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.retryAcceptAssistedCapacityRequest.PATCH")
  public Response retryAcceptAssistedCapacityRequest(
      @Context final Group pGroup, @PathParam("requestId") final ObjectId pRequestId)
      throws SvcException {
    try {
      _awsCheckCapacityRequestSvc.retryAcceptAssistedCapacityRequest(pGroup.getId(), pRequestId);
      return Response.ok().build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @GET
  @Path("/odcr/info")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.GLOBAL_ADMIN_READ_ONLY, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getOnDemandCapacityReservationInfo.GET")
  public Response getOnDemandCapacityReservationInfo(
      @QueryParam("requestId") final String pRequestId, @QueryParam("index") final int pIndex)
      throws SvcException {

    if (pRequestId == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "requestId");
    }

    if (pIndex < 0) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "index");
    }

    final CheckCapacityRequest<? extends InstanceCapacitySpec, ? extends CheckResult> request =
        _genericCheckCapacityRequestSvc.findRequest(new ObjectId(pRequestId));
    if (pIndex >= request.getInstanceSpecs().size() || pIndex >= request.getCheckResults().size()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "index");
    }

    final CloudProvider cloudProvider = request.getCloudProvider();
    final ODCRInfoView infoView =
        switch (cloudProvider) {
          case AWS ->
              _awsCheckCapacityRequestSvc.getODCRInfo(
                  (AWSInstanceCapacitySpec) request.getInstanceSpecs().get(pIndex),
                  (AWSCheckResult) request.getCheckResults().get(pIndex));
          case AZURE ->
              _azureCheckCapacityRequestSvc.getODCRInfo(
                  (AzureInstanceCapacitySpec) request.getInstanceSpecs().get(pIndex),
                  (AzureCheckResult) request.getCheckResults().get(pIndex));
          default -> throw new SvcException(CommonErrorCode.BAD_REQUEST, "cloudProvider");
        };

    return Response.ok(infoView).build();
  }

  @PATCH
  @Path("/groups/{groupId}/capacity/requests/{requestId}/release")
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN},
      groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.releaseCheckCapacityRequest.PATCH")
  public Response releaseCheckCapacityRequest(
      @Context final Group pGroup, @PathParam("requestId") final ObjectId pRequestId)
      throws SvcException {
    try {
      CheckCapacityRequest<? extends InstanceCapacitySpec, ? extends CheckResult> capacityRequest =
          _genericCheckCapacityRequestSvc.findRequest(pRequestId);

      getCheckCapacityRequestSvc(capacityRequest.getCloudProvider().name())
          .requestReleaseHeldCapacity(pGroup.getId(), pRequestId);
      return Response.ok().build();
    } catch (final SvcException pE) {
      return handleSvcException(pE);
    }
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/embeddedConfigServer")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateClusterToEmbeddedConfigServer.PATCH")
  public Response updateClusterToEmbeddedConfigServer(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("fixedConfigType") final boolean pFixedConfigType,
      @FormParam("updatedShardCount") final Integer pUpdatedShardCount)
      throws SvcException {
    _clusterSvc.adminUpdateConfigServerToEmbedded(
        pGroup,
        pClusterName,
        pFixedConfigType,
        ofNullable(pUpdatedShardCount),
        pAuditInfo,
        pRequest);
    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/dedicatedConfigServer")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateClusterToDedicatedConfigServer.PATCH")
  public Response updateClusterToDedicatedConfigServer(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("fixedConfigType") final boolean pFixedConfigType,
      @FormParam("updatedShardCount") final Integer pUpdatedShardCount)
      throws SvcException {
    _clusterSvc.adminUpdateConfigServerToDedicated(
        pGroup,
        pClusterName,
        pFixedConfigType,
        ofNullable(pUpdatedShardCount),
        pAuditInfo,
        pRequest);
    return Response.ok().build();
  }

  @PUT
  @Path("/uniformFrontend/loadBalancingDeployment")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.createUniformFrontendLoadBalancingDeployment.PUT")
  public Response createUniformFrontendLoadBalancingDeployment(
      @FormParam("deploymentId") final String pDeploymentId,
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName,
      @FormParam("loadBalancerHostname") final String pLoadBalancerCNAME)
      throws SvcException {
    // NOTE: Uniform Frontend can only be used on local or dev for the POC
    final boolean isLocalOrDev =
        _appSettings.getAppEnv().isLocal() || _appSettings.getAppEnv().isDev();
    if (!isLocalOrDev) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "environment");
    }

    if (!ObjectId.isValid(pDeploymentId)) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "uniform frontend load balancing deployment ID");
    }

    final ObjectId deploymentId = new ObjectId(pDeploymentId);
    final CloudProvider cloudProvider = CloudProvider.valueOf(pCloudProvider);
    final RegionName regionName =
        RegionNameHelper.findByNameOrElseThrow(cloudProvider, pRegionName);
    final Date createdDate = new Date();

    final UniformFrontendLoadBalancingDeployment deployment =
        UniformFrontendLoadBalancingDeployment.builder()
            .setId(deploymentId)
            .setCloudProvider(cloudProvider)
            .setRegionName(regionName)
            .setLoadBalancerCNAME(pLoadBalancerCNAME)
            .build();
    final UniformFrontendEnvoyConfiguration envoyConfig =
        UniformFrontendEnvoyConfiguration.builder()
            .setId(new ObjectId())
            .setDesiredVersion(0L)
            .setClusterVersionMap(new HashMap<>())
            .setListenerVersionMap(new HashMap<>())
            .setUniformFrontendLoadBalancingDeploymentId(deploymentId)
            .setCreatedDate(createdDate)
            .setUpdatedDate(createdDate)
            .setGroupIds(Set.of())
            .build();

    _uniformFrontendLoadBalancingDeploymentDao.save(deployment);
    _uniformFrontendEnvoyConfigurationDao.save(envoyConfig);
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/collectionTypes")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getClusterCollectionTypes.GET")
  public Response getClusterCollectionTypes(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final ClusterCollectionTypeResult collectionTypeResult =
        _clusterSvc.getClusterCollectionTypes(pGroupId, pClusterName);
    final Map<String, Object> response = new HashMap<>();
    response.put("containsTimeSeries", collectionTypeResult.containsTimeSeries());
    response.put("containsQueryableEncryption", collectionTypeResult.containsQueryableEncryption());
    return Response.ok(response).build();
  }

  @POST
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/metadataConsistencyChecks")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.requestCheckMetadataConsistencyForCluster.POST")
  public Response requestCheckMetadataConsistencyForCluster(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      final NDSCheckMetadataConsistencyRequestView pView)
      throws SvcException {
    _ndsCheckMetadataConsistencySvc.setNeedsCheckMetadataConsistencyAfter(
        pGroup.getId(),
        pClusterName,
        pView.bypassMaintenanceWindow(),
        pView.skipPreflightChecks(),
        CorruptionDetectionOperationOrigin.MANUAL);
    if (pView.bypassMaintenanceWindow()) {
      _ndsGroupDao.setPlanASAP(pGroup.getId());
    }
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/ftdcExportSettings")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFTDCExportSettings.GET")
  public Response getFTDCExportSettings(@PathParam("groupId") final ObjectId pGroupId)
      throws Exception {
    final Group group = _groupSvc.findById(pGroupId);
    final FTDCExport settings = _ndsGroupSvc.getFTDCExport(pGroupId);
    final FTDCExportSettingsResponseView view =
        new FTDCExportSettingsResponseView(
            settings.getDisabled(),
            settings.getLogCollectionIntervalMins(),
            settings.getMinInstanceSize(),
            settings.getDesiredForcePushDate().orElse(null),
            settings.getIncreasedFrequencyPushEndDate().orElse(null),
            settings.getIncreasedFrequencyPushIntervalMins().orElse(null));

    return Response.ok(view).build();
  }

  @POST
  @Path("/groups/{groupId}/{clusterName}/ftdcExportSettings")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.postFTDCExportSettings.POST")
  public Response postFTDCExportSettings(
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final FTDCExportSettingsRequestView pSettingsRequest)
      throws SvcException {
    final Group group = _groupSvc.findById(pGroupId);
    final FTDCExport settings = pSettingsRequest.toFTDCExport();

    if (pSettingsRequest.getForcePush()
        && !FTDCExportCacheSvc.doesClusterMeetFTDCMinInstanceSize(
            settings,
            _ndsLookupSvc.getClusterDescriptionForClusterName(pGroupId, pClusterName, false))) {
      throw new SvcException(NDSErrorCode.FTDC_TO_S3_MIN_INSTANCE_SIZE_NOT_MET_FOR_FORCE_PUSH);
    }

    _ndsGroupSvc.setFTDCExportFields(pGroupId, pClusterName, settings, pAuditInfo);
    return Response.ok().build();
  }

  private boolean isValidSetting(PlanExecutorJobPrioritySettingView pSettingView) {
    if (!pSettingView.isValid()) {
      return false;
    }

    Map<String, String> settingOptions = pSettingView.getSettingOptions();
    if (settingOptions == null) {
      return false;
    }

    if (settingOptions.isEmpty()) {
      return true;
    }

    final boolean isCloudProviderMissing =
        Strings.isNullOrEmpty(settingOptions.get(BACKING_CLOUD_PROVIDER_TAG));
    final boolean isRegionMissing = Strings.isNullOrEmpty(settingOptions.get(REGION_TAG));
    if (isCloudProviderMissing || isRegionMissing) {
      return false;
    }

    return NDSMoveTags.getRegionNameFromTags(settingOptions).isPresent();
  }

  private void setLimitOnOrg(
      final ObjectId pOrgId, final String pLimitName, final Long pValue, final AuditInfo pAuditInfo)
      throws SvcException {
    _organizationSvc.validateOrg(pOrgId);

    if (Arrays.stream(FieldsDefs.values()).noneMatch(l -> l.getValue().equals(pLimitName))) {
      throw new SvcException(OrgErrorCode.INVALID_LIMIT);
    }

    final Builder builder = new Builder(NDSAudit.Type.ORG_LIMIT_UPDATED);
    builder.auditInfo(pAuditInfo);
    builder.orgId(pOrgId);
    builder.limitName(pLimitName);
    builder.limitValue(pValue);
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());
    _organizationSvc.setLimit(pOrgId, pLimitName, pValue);
  }

  private Response getTopTenantsForMTM(
      final ObjectId pGroupId, final String pClusterName, final MTMClusterType pType)
      throws SvcException {
    if (!ValidationUtils.isValidClusterName(pClusterName)) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NAME_INVALID, "invalid cluster name, " + pClusterName);
    }
    final Collection<? extends MTMTenantView> topTenantsForMTM =
        switch (pType) {
          case SHARED -> _clusterSvc.getTenantViewsForMTM(pGroupId, pClusterName, pType);
          case SERVERLESS ->
              _ndsServerlessLoadSvc.getServerlessTenantViewsForMTM(pGroupId, pClusterName);
          case FLEX -> _ndsServerlessLoadSvc.getFlexTenantViewsForMTM(pGroupId, pClusterName);
        };
    return Response.ok(topTenantsForMTM).build();
  }

  private void verifyServerlessIsSupported() {
    if (!_appSettings.isServerlessEnabled()) {
      throw ApiErrorCode.PROVIDER_UNSUPPORTED.exception(
          false, CloudProvider.SERVERLESS.getDescription());
    }
  }

  protected void verifyFlexIsSupported() throws WebApplicationException {
    if (!_appSettings.isFlexEnabled()) {
      throw ApiErrorCode.PROVIDER_UNSUPPORTED.exception(false, CloudProvider.FLEX.getDescription());
    }
  }

  protected Set<ObjectId> convertIsolationGroupStringToSet(final String pIsolationGroupIds)
      throws SvcException {
    try {
      return ofNullable(pIsolationGroupIds).filter(StringUtils::isNotBlank).stream()
          .flatMap(ids -> Stream.of(ids.split(",")))
          .map(String::trim)
          .map(ObjectId::new)
          .collect(Collectors.toSet());
    } catch (final Exception e) {
      throw new SvcException(NDSErrorCode.ISOLATION_GROUP_IDS_INVALID, e);
    }
  }

  @VisibleForTesting
  protected void validateMongoDBVersion(final String pVersion) throws SvcException {
    if (!ValidationUtils.isValidMongodbVersion(pVersion)) {
      throw new SvcException(NDSErrorCode.INVALID_MONGODB_NAME, pVersion);
    }

    boolean validMongoDbDefaultVersion =
        _automationMongoDbVersionSvc.getAtlasVersionManifest().getDefaultVersions().stream()
            .anyMatch(version -> pVersion.equals(version.getName()));

    if (!validMongoDbDefaultVersion) {
      boolean validCustomMongoDbBuild = !_customMongoDbBuildSvc.getCustomBuild(pVersion).isEmpty();

      if (!validCustomMongoDbBuild) {
        throw new SvcException(NDSErrorCode.MONGODB_BUILD_DOES_NOT_EXIST, pVersion);
      }
    }
  }

  @VisibleForTesting
  protected void validateFeatureCompatibilityVersion(final String pFCV) throws SvcException {
    if (StringUtils.isBlank(pFCV)) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "Feature Compatibility Version cannot be null or empty");
    }

    final FeatureCompatibilityVersion fcv = FeatureCompatibilityVersion.fromString(pFCV.strip());
    if (fcv == null) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "Invalid Feature Compatibility Version: " + pFCV);
    }
  }

  private boolean isJiraTicket(final String pInput) {
    Matcher matcher = JIRA_TICKET_PATTERN.matcher(pInput);
    return matcher.matches();
  }

  @VisibleForTesting
  protected void validateReason(final String pReason) throws SvcException {
    // no reason validation for non-prod envs
    if (!_appSettings.getAppEnv().isProdOrProdGovOrInternal()) {
      return;
    }

    if (StringUtils.isEmpty(pReason)) {
      throw new SvcException(NDSErrorCode.INVALID_REASON, "reason cannot be empty");
    } else if (StringUtils.length(pReason) > MAX_REASON_LENGTH) {
      throw new SvcException(
          NDSErrorCode.INVALID_REASON, "reason. The reason must be less than 120 characters.");
    } else if (!isJiraTicket(pReason)) {
      throw new SvcException(
          NDSErrorCode.INVALID_REASON, "reason must be in the format of a Jira ticket. ABCD-1234");
    }
  }

  protected ObjectId convertStringToObjectId(final String pValue, final NDSErrorCode pErrorCode)
      throws SvcException {
    if (StringUtils.isBlank(pValue)) {
      return null;
    }

    if (!ObjectId.isValid(pValue)) {
      throw new SvcException(pErrorCode);
    }

    return new ObjectId(pValue);
  }

  private String sanitizeBaseUrl(final String baseUrl) {
    if (StringUtils.isEmpty(baseUrl)) {
      return null;
    } else if (baseUrl.endsWith("/")) {
      return baseUrl;
    } else {
      return baseUrl + "/";
    }
  }

  private Response handleSvcException(final SvcException pE) throws SvcException {
    final BasicDBObject error = new BasicDBObject("error", pE.getMessage());
    if (pE.getErrorCode().equals(CommonErrorCode.NOT_FOUND)) {
      return Response.status(Response.Status.NOT_FOUND).entity(error).build();
    }
    throw pE;
  }

  private Map<String, String> getPaginatedMTMClusterQuery(
      final String pSearchValue,
      final String pCloudProvider,
      final String pRegionName,
      final String pInstanceSize) {
    final Map<String, String> filters = new HashMap<>();
    if (pSearchValue != null) filters.put("searchValue", pSearchValue);
    if (pCloudProvider != null) filters.put("cloudProvider", pCloudProvider);
    if (pRegionName != null) filters.put("regionName", pRegionName);
    if (pInstanceSize != null) filters.put("instanceSize", pInstanceSize);
    return filters;
  }

  // null expiration date means indefinite expiration date
  @VisibleForTesting
  protected Date getExpirationDateOrThrow(final long pExpirationDate, final AppUser pAppUser)
      throws SvcException {
    Date expirationDate = null;
    // expiration being -1 means indefinite expiration
    // only GLOBAL_ATLAS_ADMIN can set indefinite expiration
    if (pExpirationDate < 0 && !pAppUser.getGlobalRoles().contains(Role.GLOBAL_ATLAS_ADMIN)) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR,
          "GLOBAL_ATLAS_OPERATOR cannot set expiration date to indefinite");
    }

    if (pExpirationDate >= 0) {
      expirationDate = new Date(pExpirationDate);
      if (expirationDate.before(new Date())) {
        throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "Invalid expiration date");
      }

      // add four more days after a quarter as a buffer
      Date oneQuarterFourDaysLater = DateUtils.addDays(new Date(), 94);

      // GLOBAL_ATLAS_OPERATOR cannot set expirationDate more than 1 quarter
      if (!pAppUser.getGlobalRoles().contains(Role.GLOBAL_ATLAS_ADMIN)
          && expirationDate.after(oneQuarterFourDaysLater)) {
        throw new SvcException(
            CommonErrorCode.VALIDATION_ERROR,
            "GLOBAL_ATLAS_OPERATOR cannot set expiration date more than one quarter");
      }
    }
    return expirationDate;
  }

  public enum CompactTenantAction {
    RETRY,
    MARK_COMPLETED
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/clusterConnectionStringSettings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR)
  @Auth(endpointAction = "epa.global.NDSAdminResource.updateClusterConnectionStringSettings.PATCH")
  public Response updateClusterConnectionStringSettings(
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final ClusterConnectionStringConfigurationRequestView pConfigurationView)
      throws SvcException {

    final Group group =
        ofNullable(_groupDao.findById(pGroupId))
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));
    final boolean featureFlagEnabled =
        isFeatureFlagEnabled(
            FeatureFlag.ATLAS_ADVANCED_REGIONALIZED_PRIVATE_ENDPOINTS, _appSettings, null, group);

    if (!featureFlagEnabled) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity("Feature flag is not enabled.")
          .build();
    }

    try {
      _clusterSvc.setClusterConnectionStringConfiguration(
          pGroupId, pClusterName, pConfigurationView.toClusterConnectionStringConfiguration());
      _ndsGroupSvc.setPlanningNow(pGroupId);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.CLUSTER_NOT_FOUND)
          || pE.getErrorCode()
              .equals(NDSErrorCode.CLUSTER_INCOMPATIBLE_WITH_EXISTING_PRIVATE_ENDPOINT_SERVICE)) {
        throw pE;
      }
      LOG.error("Unexpected exception updating cluster connection string settings.", pE);
      throw pE;
    }
    return Response.ok().build();
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/allowUnsafeRollingOperation")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSAdminResource.setAllowUnsafeRollingOperation.PATCH")
  public Response setAllowUnsafeRollingOperation(
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("allowUnsafeRollingOperation") final boolean pAllowUnsafeRollingOperation)
      throws SvcException {

    final AgentVersion curAgentVersion = _appSettings.getAutomationAgentVersion();
    // The unsafe rolling operation flag was implemented in 13.29.0, so error if the current Agent
    // version is less than tat
    if (curAgentVersion.isOlderThan(AgentVersion.MIN_UNSAFE_ROLLING_OPERATION_VERSION)) {
      throw new SvcException(
          NDSErrorCode.OUTDATED_AGENT_VERSION,
          curAgentVersion,
          AgentVersion.MIN_UNSAFE_ROLLING_OPERATION_VERSION);
    }

    _clusterSvc.setAllowUnsafeRollingOperationOnClusterDescriptionAndClusterDescriptionUpdate(
        pGroupId, pClusterName, pAllowUnsafeRollingOperation);

    return Response.ok().build();
  }

  @GET
  @Path("/fleetAttributes")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminResource.getFleetAttributes.GET")
  public Response getFleetAttributes() {
    final List<FleetAttributes> fleetAttributes = _fleetAttributesDao.findAll();
    final List<FleetAttributesView> fleetAttributesViews =
        fleetAttributes.stream().map(this::fleetAttributesToView).toList();
    return Response.ok(fleetAttributesViews).build();
  }

  public FleetAttributesView fleetAttributesToView(final FleetAttributes attrs) {
    final Map<String, Map<String, Integer>> attributesMap =
        attrs.getData().entrySet().stream()
            .map(
                entry -> {
                  final Map<String, Integer> fleetAttributesAsMap =
                      entry.getValue().stream()
                          .collect(
                              Collectors.toMap(FleetAttribute::getKey, FleetAttribute::getValue));

                  return Pair.of(entry.getKey(), fleetAttributesAsMap);
                })
            .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));

    final int maxAttributeCount =
        attrs.getData().values().stream()
            .mapToInt(f -> f.stream().mapToInt(FleetAttribute::getValue).sum())
            .max()
            .orElse(0);

    return new FleetAttributesView(
        attrs.getSource().toString(),
        attrs.getSource().getDescription(),
        attrs.getLastModified(),
        attrs.getCollectionTimeMS(),
        maxAttributeCount,
        attributesMap);
  }

  @PATCH
  @Path("/groups/{groupId}/clusterDescriptions/{clusterName}/proxyProtocolForPrivateLinkMode")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_OPERATOR, RoleSet.GLOBAL_OWNER})
  @Auth(endpointAction = "epa.global.NDSAdminResource.setAwsProxyProtocolForPrivateLinkMode.PATCH")
  public Response setAwsProxyProtocolForPrivateLinkMode(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("proxyProtocolForPrivateLinkMode") final String pProxyProtocolForPrivateLinkMode)
      throws SvcException {
    final ProxyProtocolForPrivateLinkMode proxyProtocolForPrivateLinkMode;
    try {
      proxyProtocolForPrivateLinkMode =
          ofNullable(pProxyProtocolForPrivateLinkMode)
              .filter(Predicate.not(String::isEmpty))
              .map(String::toUpperCase)
              .map(ProxyProtocolForPrivateLinkMode::valueOf)
              .orElseThrow(
                  () ->
                      new SvcException(
                          NDSErrorCode.INVALID_ARGUMENT, "proxyProtocolForPrivateLinkMode"));
    } catch (final IllegalArgumentException pE) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "proxyProtocolForPrivateLinkMode");
    }

    _clusterSvc.setProxyProtocolForPrivateLinkMode(
        pGroup.getId(), pClusterName, proxyProtocolForPrivateLinkMode, pAuditInfo);

    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  private void fixAcmeForProject(
      final ObjectId id, final ACMEProvider provider, final AuditInfo pAuditInfo)
      throws SvcException {
    // Temporary GTS x509 rollout tool.
    final List<ClusterDescription> clustersPerProject = _clusterDescriptionDao.findByGroup(id);
    for (ClusterDescription cd : clustersPerProject) {
      try {
        _ndsUISvc.fixACMEProvider(
            id, cd.getName(), provider, pAuditInfo, "GTS-ROLLOUT-BATCH-PIN", null);
      } catch (final SvcException pE) {
        LOG.warn(
            "Error bulk pinning ACME provider for GroupID "
                + id
                + " cluster: "
                + cd.getName()
                + " acme provider: "
                + provider
                + " exception: "
                + pE);
      }
    }
  }

  private void unfixAcmeForProject(final ObjectId id, final AuditInfo pAuditInfo)
      throws SvcException {
    // Temporary GTS x509 rollout tool.
    final List<ClusterDescription> clustersPerProject = _clusterDescriptionDao.findByGroup(id);
    for (ClusterDescription cd : clustersPerProject) {
      try {
        _ndsUISvc.unfixClusterACMEProvider(id, cd.getName(), pAuditInfo);
      } catch (final SvcException pE) {
        LOG.warn(
            "Error bulk unpinning ACME provider for GroupID "
                + id
                + " cluster: "
                + cd.getName()
                + " exception: "
                + pE);
      }
    }
  }
}
