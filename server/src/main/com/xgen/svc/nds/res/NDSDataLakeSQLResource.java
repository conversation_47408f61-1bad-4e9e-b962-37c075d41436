package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLAllSchemasView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLScheduledUpdateView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaWithMetadataView;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Singleton
@Path("/nds/atlasSQL/{groupId}")
public class NDSDataLakeSQLResource extends NDSBaseResource {

  private final NDSDataLakePublicSvc _ndsDataLakePublicSvc;

  @Inject
  public NDSDataLakeSQLResource(final NDSDataLakePublicSvc pNDSDataLakePublicSvc) {
    _ndsDataLakePublicSvc = pNDSDataLakePublicSvc;
  }

  @GET
  @Path("/{tenantName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.getAllSchemas.GET")
  public Response getAllSchemas(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName,
      @QueryParam("includeSchemaBody") @DefaultValue("false") final boolean pIncludeSchemaBody)
      throws SvcException {

    NDSDataLakeSQLAllSchemasView result =
        _ndsDataLakePublicSvc.getAllSchemas(pGroup, pTenantName, pAuditInfo, pIncludeSchemaBody);
    return Response.ok(result).build();
  }

  @POST
  @Path("/{tenantName}/generateAllSchemas")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.generateAllSchemas.POST")
  public Response generateAllSchemas(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName)
      throws SvcException {

    _ndsDataLakePublicSvc.generateAllSchemas(pGroup, pTenantName, pUser.getUsername(), pAuditInfo);
    return Response.ok().build();
  }

  @DELETE
  @Path("/{tenantName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.deleteAllSchemas.DELETE")
  public Response deleteAllSchemas(
      @Context final Group pGroup, @PathParam("tenantName") final String pTenantName)
      throws SvcException {

    _ndsDataLakePublicSvc.deleteAllSchemas(pGroup, pTenantName);
    return Response.ok().build();
  }

  @DELETE
  @Path("/{tenantName}/schema/db/{db}/collection/{collection}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.deleteSchema.DELETE")
  public Response deleteSchema(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("db") final String pDb,
      @PathParam("collection") final String pCollection)
      throws SvcException {

    _ndsDataLakePublicSvc.deleteSchema(pGroup, pTenantName, pDb, pCollection);
    return Response.ok().build();
  }

  @POST
  @Path("/{tenantName}/schema/db/{db}/collection/{collection}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.setSchema.POST")
  public Response setSchema(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("db") final String pDb,
      @PathParam("collection") final String pCollection,
      @QueryParam("isGenerated") final boolean pIsGenerated,
      NDSDataLakeSQLSchemaView pSchemaView)
      throws SvcException {

    _ndsDataLakePublicSvc.setSchema(
        pGroup, pTenantName, pUser.getUsername(), pDb, pCollection, pIsGenerated, pSchemaView);
    return Response.ok().build();
  }

  @GET
  @Path("/{tenantName}/schema/db/{db}/collection/{collection}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.getSchema.GET")
  public Response getSchema(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("db") final String pDb,
      @PathParam("collection") final String pCollection)
      throws SvcException {

    final NDSDataLakeSQLSchemaWithMetadataView result =
        _ndsDataLakePublicSvc.getSchema(pGroup, pTenantName, pDb, pCollection);
    return Response.ok(result).build();
  }

  @POST
  @Path("/{tenantName}/generateSchemas/db/{db}/collection/{collection}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.generateSchemas.POST")
  public Response generateSchemas(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("db") final String pDb,
      @PathParam("collection") final String pCollection)
      throws SvcException {

    final NDSDataLakeSQLSchemaView result =
        _ndsDataLakePublicSvc.generateSchemas(
            pGroup, pTenantName, pUser.getUsername(), pDb, pCollection, pAuditInfo);
    return Response.ok(result).build();
  }

  @GET
  @Path("/{tenantName}/schedule")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.getScheduledUpdate.GET")
  public Response getScheduledUpdate(
      @Context final Group pGroup, @PathParam("tenantName") final String pTenantName)
      throws SvcException {

    NDSDataLakeSQLScheduledUpdateView result =
        _ndsDataLakePublicSvc.getScheduledUpdate(pGroup, pTenantName);
    return Response.ok(result).build();
  }

  @POST
  @Path("/{tenantName}/schedule")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.createScheduledUpdate.POST")
  public Response createScheduledUpdate(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName,
      NDSDataLakeSQLScheduledUpdateView sqlScheduledUpdate)
      throws SvcException {

    NDSDataLakeSQLScheduledUpdateView result =
        _ndsDataLakePublicSvc.createScheduledUpdate(
            pGroup,
            pTenantName,
            sqlScheduledUpdate.getFrequency(),
            pAppUser.getUsername(),
            pAuditInfo);
    return Response.ok(result).build();
  }

  @PATCH
  @Path("/{tenantName}/schedule")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.updateScheduledUpdate.PATCH")
  public Response updateScheduledUpdate(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName,
      NDSDataLakeSQLScheduledUpdateView sqlScheduledUpdate)
      throws SvcException {

    NDSDataLakeSQLScheduledUpdateView result =
        _ndsDataLakePublicSvc.modifyScheduledUpdate(
            pGroup,
            pTenantName,
            sqlScheduledUpdate.getFrequency(),
            pAppUser.getUsername(),
            pAuditInfo);
    return Response.ok(result).build();
  }

  @DELETE
  @Path("/{tenantName}/schedule")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SQL_SCHEMA_MANAGEMENT)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSDataLakeSQLResource.deleteScheduledUpdate.DELETE")
  public Response deleteScheduledUpdate(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName)
      throws SvcException {

    NDSDataLakeSQLScheduledUpdateView result =
        _ndsDataLakePublicSvc.deleteScheduledUpdate(pGroup, pTenantName, pAuditInfo);
    return Response.ok(result).build();
  }
}
