package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.federation._public.model.OidcIdentityProvider;
import com.xgen.cloud.federation._public.svc.FederationAppIdentityProvidersSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.util.NDSTestOIDCIdp;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.svc.nds.model.ui.NDSGroupFederationSettingsView;
import com.xgen.svc.nds.model.ui.NDSOIDCIdentityProviderView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Path("/nds/{groupId}/federationSettings")
@Singleton
public class NDSGroupFederationSettingsResource extends NDSBaseResource {

  private final OrganizationSvc _orgSvc;
  private final FederationAppIdentityProvidersSvc _federationAppIdentityProvidersSvc;
  private final FeatureFlagSvc _featureFlagSvc;

  @Inject
  public NDSGroupFederationSettingsResource(
      final OrganizationSvc pOrgSvc,
      final FederationAppIdentityProvidersSvc pFederationAppIdentityProviderSvc,
      final FeatureFlagSvc pFeatureFlagSvc) {
    _orgSvc = pOrgSvc;
    _federationAppIdentityProvidersSvc = pFederationAppIdentityProviderSvc;
    _featureFlagSvc = pFeatureFlagSvc;
  }

  private List<OidcIdentityProvider> getLinkedOidcIdps(
      final Group pGroup, final Organization pOrganization) {
    if (_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION, pOrganization, pGroup)) {
      return List.of(
          NDSTestOIDCIdp.getTestWorkforceOIDCIdp(), NDSTestOIDCIdp.getTestWorkloadOIDCIdp());
    }
    return _federationAppIdentityProvidersSvc.getConnectedOidcIdpsByOrgId(pOrganization.getId());
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupFederationSettingsResource.getGroupFederationSettings.GET")
  public Response getGroupFederationSettings(@Context final Group pGroup) throws SvcException {
    final Organization org =
        Optional.ofNullable(_orgSvc.findById(pGroup.getOrgId()))
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final List<NDSOIDCIdentityProviderView> oidcIdentityProviderViews =
        getLinkedOidcIdps(pGroup, org).stream()
            .map(NDSOIDCIdentityProviderView::new)
            .collect(Collectors.toList());

    return Response.ok(new NDSGroupFederationSettingsView(oidcIdentityProviderViews)).build();
  }
}
