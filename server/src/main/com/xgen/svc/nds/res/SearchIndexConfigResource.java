package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.search.SearchIndexResponseView;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.util.SearchIndexResponseViewFactory;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

@Path("/nds/clusters/{groupId}/{clusterName}/search/indexes")
@AllowNDSCNRegionsOnlyGroups
@Singleton
public class SearchIndexConfigResource extends BaseResource {
  private final FTSIndexConfigSvc ftsIndexConfigSvc;

  @Inject
  public SearchIndexConfigResource(final FTSIndexConfigSvc pFtsIndexConfigSvc) {
    ftsIndexConfigSvc = pFtsIndexConfigSvc;
  }

  @GET
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_DATA_ACCESS_ANY,
        RoleSet.GLOBAL_READ_ONLY,
        RoleSet.GROUP_SEARCH_INDEX_EDITOR
      },
      plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.SearchIndexConfigResource.getSearchIndexes.GET")
  public Response getSearchIndexes(
      @Context final Group group, @PathParam("clusterName") final String clusterName)
      throws SvcException {
    this.ftsIndexConfigSvc.verifyGroupAndCluster(group.getId(), clusterName);
    boolean isDetailedStatusesReported =
        this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());

    if (this.ftsIndexConfigSvc.isSharedOrFlexTenantCluster(group.getId(), clusterName)) {
      final List<SearchIndexResponseView> ftsIndexes =
          this.ftsIndexConfigSvc.getTenantFTSIndexes(group.getId(), clusterName).stream()
              .map(
                  index ->
                      SearchIndexResponseViewFactory.createResponseView(
                          index, isDetailedStatusesReported))
              .collect(Collectors.toList());
      return Response.ok(ftsIndexes).build();
    } else {
      final List<SearchIndexResponseView> ftsIndexes =
          this.ftsIndexConfigSvc.getFTSIndexes(group.getId(), clusterName).stream()
              .map(
                  index ->
                      SearchIndexResponseViewFactory.createResponseView(
                          index, isDetailedStatusesReported))
              .collect(Collectors.toList());
      return Response.ok(ftsIndexes).build();
    }
  }

  @GET
  @Path("/{indexId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_DATA_ACCESS_ANY,
        RoleSet.GLOBAL_READ_ONLY,
        RoleSet.GROUP_SEARCH_INDEX_EDITOR
      },
      plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.SearchIndexConfigResource.getSearchIndex.GET")
  public Response getSearchIndex(
      @Context final Group group,
      @PathParam("clusterName") final String clusterName,
      @PathParam("indexId") final ObjectId indexId)
      throws SvcException {
    this.ftsIndexConfigSvc.verifyGroupAndCluster(group.getId(), clusterName);
    boolean isDetailedStatusesReported =
        this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());

    final Optional<FTSIndex> ftsIndexOpt;
    if (this.ftsIndexConfigSvc.isSharedOrFlexTenantCluster(group.getId(), clusterName)) {
      ftsIndexOpt = this.ftsIndexConfigSvc.getTenantFTSIndex(group.getId(), clusterName, indexId);
    } else {
      ftsIndexOpt = this.ftsIndexConfigSvc.getFTSIndex(group.getId(), clusterName, indexId);
    }

    if (ftsIndexOpt.isEmpty()) {
      throw new SvcException(CommonErrorCode.NOT_FOUND);
    }

    return Response.ok(
            SearchIndexResponseViewFactory.createResponseView(
                ftsIndexOpt.get(), isDetailedStatusesReported))
        .build();
  }
}
