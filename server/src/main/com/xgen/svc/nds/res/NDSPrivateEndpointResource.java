package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.ProjectSettingItemView;
import com.xgen.svc.nds.model.ui.privateLink.EndpointGroupModificationRequest;
import com.xgen.svc.nds.model.ui.privateLink.EndpointModificationRequest;
import com.xgen.svc.nds.model.ui.privateLink.EndpointServiceCreateRequest;
import com.xgen.svc.nds.svc.NDSPrivateLinkSvc;
import com.xgen.svc.nds.svc.NDSPrivateLinkSvc.InterfaceEndpointsByCloudProvider;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.LinkedHashMap;
import java.util.Map;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/privateEndpoint")
@Singleton
@AllowNDSCNRegionsOnlyGroups
public class NDSPrivateEndpointResource extends BaseResource {

  private final NDSPrivateLinkSvc _ndsPrivateLinkSvc;
  private final NDSUISvc _ndsUISvc;
  private final AppSettings _appSettings;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;

  @Inject
  public NDSPrivateEndpointResource(
      final NDSPrivateLinkSvc pNDSPrivateLinkSvc,
      final NDSGroupSvc pNDSGroupSvc,
      final NDSUISvc pNDSUISvc,
      final AppSettings pAppSettings,
      final NDSResourcePolicySvc pNDSResourcePolicySvc) {
    _ndsPrivateLinkSvc = pNDSPrivateLinkSvc;
    _ndsUISvc = pNDSUISvc;
    _appSettings = pAppSettings;
    _ndsResourcePolicySvc = pNDSResourcePolicySvc;
  }

  @GET
  @Path("/{cloudProvider}/endpointService")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.getEndpointServices.GET")
  public Response getEndpointServices(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider)
      throws SvcException {
    switch (pCloudProvider) {
      case AWS:
      case AZURE:
      case GCP:
        return Response.ok(
                _ndsPrivateLinkSvc.getEndpointServiceViewsForGroup(pNDSGroup, pCloudProvider))
            .build();
      default:
        return Response.status(Response.Status.NOT_FOUND).build();
    }
  }

  @PUT
  @Path("/{cloudProvider}/endpointService")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.addEndpointService.PUT")
  public Response addEndpointService(
      @Context final NDSGroup pNDSGroup,
      @Context final Organization pOrganization,
      @Context final AppUser pUser,
      @Context final AuditInfo auditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      final EndpointServiceCreateRequest pCreateRequest)
      throws SvcException {
    pCreateRequest.validate();
    final RegionName regionName =
        pCreateRequest.getRegion().orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_REGION));

    _ndsPrivateLinkSvc.addEndpointService(
        pUser, pOrganization, pNDSGroup, pCloudProvider, regionName, auditInfo);
    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/{cloudProvider}/endpointService/{endpointServiceId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.deleteEndpointService.DELETE")
  public Response deleteEndpointService(
      @Context final NDSGroup pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("endpointServiceId") final ObjectId pEndpointServiceId)
      throws SvcException {
    _ndsPrivateLinkSvc.deleteEndpointService(pGroup, pCloudProvider, pEndpointServiceId, auditInfo);
    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @PUT
  @Path("/{cloudProvider}/endpointService/{endpointServiceId}/endpoint")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.addEndpoint.PUT")
  public Response addEndpoint(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("endpointServiceId") final ObjectId pEndpointServiceId,
      final EndpointModificationRequest pAddRequest)
      throws SvcException {
    if (pCloudProvider == CloudProvider.AWS && pAddRequest.getPrivateEndpointIPAddress().isSet()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          EndpointModificationRequest.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS);
    }

    final InterfaceEndpointsByCloudProvider interfaceEndpointsByCloudProvider =
        _ndsPrivateLinkSvc.getInterfaceEndpointsByCloudProvider(pNDSGroup);
    interfaceEndpointsByCloudProvider.addInterfaceEndpoint(pCloudProvider, pAddRequest);

    final AtlasResourcePolicyAuthResponse authResp =
        _ndsResourcePolicySvc.isProjectPrivateEndpointRequestAuthorized(
            pGroup, interfaceEndpointsByCloudProvider.getAllInterfaceEndpoints(), pAuditInfo);

    if (authResp.decision().isDeny()) {
      return _ndsResourcePolicySvc.getDeniedResourcePolicyAuthResponse(authResp);
    }

    _ndsPrivateLinkSvc.addEndpoint(
        pNDSGroup,
        pCloudProvider,
        pEndpointServiceId,
        pAddRequest.getEndpointId(),
        pAddRequest.getPrivateEndpointIPAddress().orElse(null),
        pAuditInfo);
    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @PUT
  @Path("/{cloudProvider}/endpointService/{endpointServiceId}/endpointGroup")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.addEndpointGroup.PUT")
  public Response addEndpointGroup(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("endpointServiceId") final ObjectId pEndpointServiceId,
      final EndpointGroupModificationRequest pAddRequest)
      throws SvcException {
    if (pCloudProvider != CloudProvider.GCP) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }

    final InterfaceEndpointsByCloudProvider interfaceEndpointsByCloudProvider =
        _ndsPrivateLinkSvc.getInterfaceEndpointsByCloudProvider(pNDSGroup);
    interfaceEndpointsByCloudProvider.addInterfaceEndpointGroup(pAddRequest);

    final AtlasResourcePolicyAuthResponse authResp =
        _ndsResourcePolicySvc.isProjectPrivateEndpointRequestAuthorized(
            pGroup, interfaceEndpointsByCloudProvider.getAllInterfaceEndpoints(), pAuditInfo);

    if (authResp.decision().isDeny()) {
      return SimpleApiResponse.forbidden(
              NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)
          .message(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage())
          .build();
    }

    final Map<String, String> endpoints = new LinkedHashMap<>();
    pAddRequest
        .getEndpoints()
        .forEach(
            (view) -> {
              endpoints.put(view.getEndpointId(), view.getPrivateEndpointIPAddress().orElse(null));
            });

    _ndsPrivateLinkSvc.addEndpointGroup(
        pNDSGroup,
        pCloudProvider,
        pEndpointServiceId,
        pAddRequest.getEndpointGroupName(),
        pAddRequest.getCustomerGCPProjectId(),
        endpoints,
        pAuditInfo);
    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/{cloudProvider}/endpointService/{endpointServiceId}/endpointGroup/{endpointGroupId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.patchEndpointGroup.PATCH")
  public Response patchEndpointGroup(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("endpointServiceId") final ObjectId pEndpointServiceId,
      @PathParam("endpointGroupId") final ObjectId pEndpointGroupId,
      final EndpointGroupModificationRequest pAddRequest)
      throws SvcException {
    if (pCloudProvider != CloudProvider.GCP) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }

    final InterfaceEndpointsByCloudProvider interfaceEndpointsByCloudProvider =
        _ndsPrivateLinkSvc.getInterfaceEndpointsByCloudProvider(pNDSGroup);
    interfaceEndpointsByCloudProvider.removeInterfaceEndpoint(
        pCloudProvider, pEndpointGroupId.toString());
    interfaceEndpointsByCloudProvider.addInterfaceEndpointGroup(pAddRequest);

    final AtlasResourcePolicyAuthResponse authResp =
        _ndsResourcePolicySvc.isProjectPrivateEndpointRequestAuthorized(
            pGroup, interfaceEndpointsByCloudProvider.getAllInterfaceEndpoints(), pAuditInfo);

    if (authResp.decision().isDeny()) {
      return SimpleApiResponse.forbidden(
              NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)
          .message(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage())
          .build();
    }

    final Map<String, String> endpoints = new LinkedHashMap<>();
    pAddRequest
        .getEndpoints()
        .forEach(
            (view) -> {
              endpoints.put(view.getEndpointId(), view.getPrivateEndpointIPAddress().orElse(null));
            });

    _ndsPrivateLinkSvc.patchEndpointGroup(
        pNDSGroup,
        pCloudProvider,
        pEndpointServiceId,
        pEndpointGroupId,
        pAddRequest.getEndpointGroupName(),
        pAddRequest.getCustomerGCPProjectId(),
        endpoints,
        pAuditInfo);

    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/{cloudProvider}/endpointService/{endpointServiceId}/endpoint/{endpointId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.deleteEndpoint.DELETE")
  public Response deleteEndpoint(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("endpointServiceId") final ObjectId pEndpointServiceId,
      @PathParam("endpointId") final String pEndpointId)
      throws SvcException {

    final InterfaceEndpointsByCloudProvider interfaceEndpointsByCloudProvider =
        _ndsPrivateLinkSvc.getInterfaceEndpointsByCloudProvider(pNDSGroup);
    interfaceEndpointsByCloudProvider.removeInterfaceEndpoint(pCloudProvider, pEndpointId);

    final AtlasResourcePolicyAuthResponse authResp =
        _ndsResourcePolicySvc.isProjectPrivateEndpointRequestAuthorized(
            pGroup, interfaceEndpointsByCloudProvider.getAllInterfaceEndpoints(), pAuditInfo);

    if (authResp.decision().isDeny()) {
      return SimpleApiResponse.forbidden(
              NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)
          .message(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage())
          .build();
    }

    _ndsPrivateLinkSvc.deleteEndpoint(
        pNDSGroup, pCloudProvider, pEndpointServiceId, pEndpointId, pAuditInfo);
    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/{cloudProvider}/endpointService/{endpointServiceId}/endpoint/{endpointId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.patchEndpoint.PATCH")
  public Response patchEndpoint(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @Context final AuditInfo pAuditInfo,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("endpointServiceId") final ObjectId pEndpointServiceId,
      @PathParam("endpointId") final String pOldEndpointId,
      final EndpointModificationRequest pPatchRequest)
      throws SvcException {
    if (pCloudProvider == CloudProvider.AWS
        && pPatchRequest.getPrivateEndpointIPAddress().isSet()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          EndpointModificationRequest.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS);
    }

    final InterfaceEndpointsByCloudProvider interfaceEndpointsByCloudProvider =
        _ndsPrivateLinkSvc.getInterfaceEndpointsByCloudProvider(pNDSGroup);
    interfaceEndpointsByCloudProvider.removeInterfaceEndpoint(pCloudProvider, pOldEndpointId);
    interfaceEndpointsByCloudProvider.addInterfaceEndpoint(pCloudProvider, pPatchRequest);

    final AtlasResourcePolicyAuthResponse authResp =
        _ndsResourcePolicySvc.isProjectPrivateEndpointRequestAuthorized(
            pGroup, interfaceEndpointsByCloudProvider.getAllInterfaceEndpoints(), pAuditInfo);

    if (authResp.decision().isDeny()) {
      return SimpleApiResponse.forbidden(
              NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)
          .message(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.getMessage())
          .build();
    }

    _ndsPrivateLinkSvc.patchEndpoint(
        pNDSGroup,
        pCloudProvider,
        pEndpointServiceId,
        pOldEndpointId,
        pPatchRequest.getEndpointId(),
        pPatchRequest.getPrivateEndpointIPAddress().orElse(null),
        pAuditInfo);
    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/regionalMode")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.getRegionalizedPrivateLinkEnabled.GET")
  public Response getRegionalizedPrivateLinkEnabled(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws Exception {
    final boolean enabled = _ndsUISvc.getRegionalizedPrivateLinkEnabled(pGroup);
    return Response.ok(new ProjectSettingItemView(enabled)).build();
  }

  @PATCH
  @Path("/regionalMode")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPrivateEndpointResource.updateRegionalizedPrivateLinkEnabled.PATCH")
  public Response updateRegionalizedPrivateLinkEnabled(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final ProjectSettingItemView pProjectSettingItemView)
      throws SvcException {
    final boolean enabled = pProjectSettingItemView.isSettingEnabled();
    _ndsUISvc.setRegionalizedPrivateLinkEnabled(pGroup, enabled, pAuditInfo);
    return Response.accepted(new ProjectSettingItemView(enabled)).build();
  }
}
