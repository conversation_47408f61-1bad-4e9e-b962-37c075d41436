package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ExposureStatusInfo;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterStatusInfo;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import server.src.main.com.xgen.cloud.nds.shadowcluster.view._public.view.ConfigOverrideView;
import server.src.main.com.xgen.cloud.nds.shadowcluster.view._public.view.ExposureView;
import server.src.main.com.xgen.cloud.nds.shadowcluster.view._public.view.PermutationView;
import server.src.main.com.xgen.cloud.nds.shadowcluster.view._public.view.ShadowClusterView;

@Hidden
@Path("/admin/nds/shadowClusterExposure")
@Singleton
public class NDSAdminShadowClusterResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(NDSAdminShadowClusterResource.class);

  final ShadowClusterSvc shadowClusterSvc;

  @Inject
  public NDSAdminShadowClusterResource(final ShadowClusterSvc shadowClusterSvc) {
    this.shadowClusterSvc = shadowClusterSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminShadowClusterResource.getAllExposures.GET")
  public Response getAllExposures() throws SvcException {
    final List<Exposure> exposures = shadowClusterSvc.getAllExposures();

    // Extract exposure IDs for bulk operations
    final List<ObjectId> exposureIds = exposures.stream().map(Exposure::getId).toList();

    // Get derived status and status reasons in bulk
    final List<ExposureStatusInfo> statusInfoList;
    try {
      statusInfoList = shadowClusterSvc.getBulkExposureStatusById(exposureIds);
    } catch (final SvcException e) {
      // Log the error and return empty response for any exception
      LOG.warn("Failed to get exposure status information: {}", e.getMessage(), e);
      throw e;
    }

    // Create map for efficient lookup
    final Map<ObjectId, ExposureStatusInfo> statusInfoMap =
        statusInfoList.stream()
            .collect(Collectors.toMap(ExposureStatusInfo::exposureId, Function.identity()));

    // Create ExposureView objects with derived status and status reason
    final List<ExposureView> exposureViews =
        exposures.stream()
            .map(
                exposure -> {
                  final ExposureStatusInfo statusInfo = statusInfoMap.get(exposure.getId());
                  if (statusInfo == null) {
                    throw new RuntimeException(
                        "Failed to get status information for exposure " + exposure.getId());
                  }

                  return new ExposureView(exposure, statusInfo);
                })
            .collect(Collectors.toList());

    return Response.ok(exposureViews).build();
  }

  @POST
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminShadowClusterResource.createExposure.POST")
  public Response createExposure(
      @Context final AuditInfo pAuditInfo, @Valid @NotNull final ExposureView pExposureView) {

    try {
      final ObjectId exposureId =
          shadowClusterSvc.createExposure(
              pExposureView.getSourceClusterId(),
              pExposureView.getSourceClusterName(),
              pExposureView.getSourceGroupId(),
              pExposureView.getSourceGroupName(),
              pExposureView.getSourceOrgId(),
              pExposureView.getSourceOrgName(),
              pExposureView.getTriggerReasonJira(),
              pExposureView.getCaptureStartDate(),
              pExposureView.getCaptureDurationMinutes(),
              pExposureView.getPermutationViews().stream()
                  .map(PermutationView::createPermutation)
                  .toList(),
              Optional.ofNullable(pExposureView.getConfigOverrideView())
                  .map(ConfigOverrideView::toConfigOverride)
                  .orElse(null));

      final Exposure exposure = shadowClusterSvc.getExposure(exposureId).orElseThrow();

      // Get derived status and status reason for the newly created exposure
      final List<ExposureStatusInfo> statusInfoList =
          shadowClusterSvc.getBulkExposureStatusById(List.of(exposureId));

      if (statusInfoList.isEmpty()) {
        throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(
            false, "shadow cluster exposure " + exposureId);
      }

      return Response.ok(new ExposureView(exposure, statusInfoList.get(0))).build();
    } catch (final Exception e) {
      LOG.error("Failed to create exposure", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
  }

  @GET
  @Path("/{exposureId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminShadowClusterResource.getExposureById.GET")
  public Response getExposureById(
      @PathParam("exposureId") @NotNull final String exposureIdStr,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {

    // Validate ObjectId format
    if (!ObjectId.isValid(exposureIdStr)) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(pEnvelope, "exposureId");
    }

    final ObjectId exposureId = new ObjectId(exposureIdStr);

    // Retrieve exposure from service layer
    final Optional<Exposure> maybeExposure = shadowClusterSvc.getExposure(exposureId);

    if (maybeExposure.isEmpty()) {
      throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(
          pEnvelope, "shadow cluster exposure " + exposureIdStr);
    }

    final Exposure exposure = maybeExposure.get();

    // Get derived status and status reason for the exposure
    final List<ExposureStatusInfo> statusInfoList =
        shadowClusterSvc.getBulkExposureStatusById(List.of(exposureId));

    if (statusInfoList.isEmpty()) {
      throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(
          pEnvelope, "shadow cluster exposure " + exposureIdStr);
    }

    return new ApiResponseBuilder(pEnvelope)
        .ok()
        .content(new ExposureView(exposure, statusInfoList.get(0)))
        .build();
  }

  @DELETE
  @Path("/{exposureId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminShadowClusterResource.deleteExposureById.DELETE")
  public Response deleteExposureById(
      @Context final AuditInfo auditInfo, @PathParam("exposureId") final ObjectId exposureId) {

    // Check if exposure exists
    final Optional<Exposure> maybeExposure = shadowClusterSvc.getExposure(exposureId);
    if (maybeExposure.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    try {
      // Delete the exposure and associated jobs
      shadowClusterSvc.deleteExposure(exposureId);
      return Response.ok().build();
    } catch (final Exception e) {
      LOG.error("Failed to delete exposure with id {}", exposureId, e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
    }
  }

  @GET
  @Path("/shadowCluster/{shadowClusterId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSAdminShadowClusterResource.getShadowClusterById.GET")
  public Response getShadowClusterById(
      @PathParam("shadowClusterId") final ObjectId shadowClusterId,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {

    final Optional<ShadowCluster> maybeShadowCluster =
        shadowClusterSvc.getShadowCluster(shadowClusterId);

    if (maybeShadowCluster.isEmpty()) {
      throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(
          pEnvelope, "shadow cluster " + shadowClusterId);
    }

    final ShadowCluster shadowCluster = maybeShadowCluster.get();

    // Derive status and status reason from the current job
    final List<ShadowClusterStatusInfo> statusInfoList =
        shadowClusterSvc.getBulkShadowClusterStatusById(List.of(shadowCluster.getId()));

    final Map<ObjectId, ShadowClusterStatusInfo> statusById =
        statusInfoList.stream()
            .collect(
                Collectors.toMap(ShadowClusterStatusInfo::shadowClusterId, Function.identity()));

    final ShadowClusterStatusInfo statusInfo = statusById.get(shadowCluster.getId());
    if (statusInfo == null) {
      throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(
          pEnvelope, "shadow cluster status for " + shadowClusterId);
    }

    // Build view with derived status information
    final ShadowClusterView shadowClusterView = new ShadowClusterView(shadowCluster, statusInfo);

    return new ApiResponseBuilder(pEnvelope).ok().content(shadowClusterView).build();
  }
}
