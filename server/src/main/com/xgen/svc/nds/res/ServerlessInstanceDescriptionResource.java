package com.xgen.svc.nds.res;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billing._public.svc.IPlanSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._private.dao.DataRegionConfigDao;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.metrics._public.svc.serverless.ServerlessMetricsSvc;
import com.xgen.cloud.nds.billing._public.svc.EstimateSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.svc.FlexMigrationSvc;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus.ServerlessFreeMigrationState;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.decoupled.api._public.svc.SearchDeploymentDescriptionAPISvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.svc.allclusters.ServerlessToFlexInstanceSvc;
import com.xgen.svc.nds.aws.svc.NDSAWSLogDownloadSvc;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.svc.NDSClusterConversionSvc;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.ServerlessFreeMigrationStatusSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeToServerlessSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.json.JSONObject;

@Path("/nds/serverless")
@Singleton
public class ServerlessInstanceDescriptionResource extends BaseClusterDescriptionResource {

  private static final String MIGRATION_REASON = "migrationReason";
  private static final String MIGRATION_REASON_NONE = "NONE";

  private final ServerlessToFlexInstanceSvc _serverlessToFlexInstanceSvc;
  private final NDSTenantEndpointSvc _tenantEndpointSvc;
  private final ServerlessMetricsSvc _serverlessMetricsSvc;
  private final ServerlessFreeMigrationStatusSvc _serverlessFreeMigrationStatusSvc;

  @Inject
  public ServerlessInstanceDescriptionResource(
      final NDSUISvc pNDSUISvc,
      final NDSAWSLogDownloadSvc pNDSAWSLogDownloadSvc,
      final EstimateSvc pEstimateSvc,
      final IPlanSvc pPlanSvc,
      final AppSettings pAppSettings,
      final AuditSvc pAuditSvc,
      final SearchDeploymentDescriptionAPISvc pSearchDeploymentDescriptionAPISvc,
      final NDSGroupDao pNDSGroupDao,
      final DataRegionConfigDao pDataRegionConfigDao,
      final NDSClusterConversionSvc pNDSClusterConversionSvc,
      final NDSClusterSvc pNDSClusterSvc,
      final TenantUpgradeToServerlessSvc pTenantUpgradeSvc,
      final NDSResourcePolicySvc pNDSResourcePolicySvc,
      final ServerlessToFlexInstanceSvc pServerlessToFlexInstanceSvc,
      final NDSTenantEndpointSvc pTenantEndpointService,
      final ServerlessMetricsSvc pServerlessMetricsSvc,
      final ServerlessFreeMigrationStatusSvc pServerlessFreeMigrationStatusSvc,
      final FlexMigrationSvc pFlexMigrationSvc,
      final AuthzSvc authzSvc) {
    super(
        pNDSUISvc,
        pNDSGroupDao,
        pNDSAWSLogDownloadSvc,
        pEstimateSvc,
        pPlanSvc,
        pAppSettings,
        pAuditSvc,
        pSearchDeploymentDescriptionAPISvc,
        pDataRegionConfigDao,
        pNDSClusterConversionSvc,
        pNDSClusterSvc,
        pTenantUpgradeSvc,
        pNDSResourcePolicySvc,
        pFlexMigrationSvc,
        authzSvc);

    _serverlessToFlexInstanceSvc = pServerlessToFlexInstanceSvc;
    _tenantEndpointSvc = pTenantEndpointService;
    _serverlessMetricsSvc = pServerlessMetricsSvc;
    _serverlessFreeMigrationStatusSvc = pServerlessFreeMigrationStatusSvc;
  }

  @GET
  @Path("/{groupId}/options")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.getNDSOptions.GET")
  public Response getNDSOptions(@Context final Group pGroup) throws Exception {
    verifyServerlessIsSupported();
    return super.getNDSOptions(pGroup, CloudProvider.SERVERLESS.name());
  }

  @GET
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.allServerlessInstances.GET")
  public Response allServerlessInstances(@Context final AppUser pUser, @Context final Group pGroup)
      throws Exception {
    verifyServerlessIsSupported();
    return Response.ok(
            getNDSUISvc().getClusterDescriptions(pGroup.getId(), true, false).stream()
                .filter(ClusterDescriptionView::isServerlessTenantCluster)
                .collect(toClusterDescriptionViewList()))
        .build();
  }

  @GET
  @Path("/{groupId}/{serverlessInstanceName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.singleServerlessInstance.GET")
  public Response singleServerlessInstance(
      @Context final AppUser pUser,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName)
      throws Exception {
    verifyServerlessIsSupported();
    final ClusterDescriptionView view =
        super.getClusterDescriptionView(pOrg, pGroup, pServerlessInstanceName, true, false);
    verifyClusterServerlessInstance(view);
    return Response.ok(view).build();
  }

  @GET
  @Path("/{groupId}/{clusterName}/regional/clusterDescription")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.singleClusterWithDataProcessingRegion.GET")
  public Response singleClusterWithDataProcessingRegion(
      @Context final AppUser pUser,
      @Context final Organization pOrg,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    final ClusterDescriptionView view =
        super.getClusterDescriptionView(pOrg, pGroup, pClusterName, false, true);
    verifyClusterServerlessInstance(view);
    return Response.ok(view).build();
  }

  @GET
  @Path("/{groupId}/{serverlessInstanceName}/admin")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN)
  @Auth(endpointAction = "epa.global.ServerlessInstanceDescriptionResource.singleServerlessInstanceForGlobalMonitoringAdmin.GET")
  public Response singleServerlessInstanceForGlobalMonitoringAdmin(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName)
      throws Exception {
    verifyServerlessIsSupported();
    final ClusterDescriptionView view =
        super.getAdminClusterDescriptionView(pGroup, pServerlessInstanceName, true, pUser);
    verifyClusterServerlessInstance(view);
    return Response.ok(view).build();
  }

  @POST
  @Path("/{groupId}/costEstimate")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.costEstimate.POST")
  public Response costEstimate(
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      final ClusterDescriptionView pClusterDescriptionView)
      throws Exception {
    verifyServerlessIsSupported();
    verifyClusterServerlessInstance(pClusterDescriptionView);
    return super.costEstimate(pOrganization, pGroup, pNDSGroup, pClusterDescriptionView);
  }

  @POST
  @Path("/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.createServerlessInstance.POST")
  public Response createServerlessInstance(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @QueryParam("recaptchaToken") final String pRecaptchaToken,
      final ClusterDescriptionView pClusterDescriptionView)
      throws Exception {
    verifyServerlessIsSupported();
    verifyClusterServerlessInstance(pClusterDescriptionView);
    return super.createCluster(
        pRequest,
        pUser,
        pOrganization,
        pGroup,
        pClusterDescriptionView,
        pRecaptchaToken,
        pAuditInfo);
  }

  @PATCH
  @Path("/{groupId}/{serverlessInstanceName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_CLUSTER_MANAGER, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.updateServerlessInstance.PATCH")
  public Response updateServerlessInstance(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName,
      final ClusterDescriptionView pClusterDescriptionView)
      throws Exception {
    verifyServerlessIsSupported();
    // Must verify both the existing serverless instance and the proposed updates
    verifyClusterServerlessInstance(pGroup, pServerlessInstanceName);
    verifyClusterServerlessInstance(pClusterDescriptionView);
    return super.updateCluster(
        pRequest,
        pUser,
        pOrganization,
        pGroup,
        pServerlessInstanceName,
        pClusterDescriptionView,
        true,
        "updateServerlessInstance",
        pAuditInfo);
  }

  @DELETE
  @Path("/{groupId}/{serverlessInstanceName}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.requestDeleteServerlessInstance.DELETE")
  public Response requestDeleteServerlessInstance(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName)
      throws Exception {
    verifyServerlessIsSupported();
    verifyClusterServerlessInstance(pGroup, pServerlessInstanceName);
    return super.requestDeleteCluster(
        pRequest,
        pGroup,
        auditInfo,
        pServerlessInstanceName,
        true,
        false,
        "deleteServerlessInstance");
  }

  @GET
  @Path("/{groupId}/{serverlessInstanceName}/serverlessInstanceUsageStats")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.getServerlessInstanceUsageStats.GET")
  public Response getServerlessInstanceUsageStats(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName)
      throws Exception {
    verifyServerlessIsSupported();
    verifyClusterServerlessInstance(pGroup, pServerlessInstanceName);
    return super.getClusterUsageStats(pGroup, pServerlessInstanceName, true);
  }

  @GET
  @Path("/{groupId}/{serverlessInstanceName}/migrationStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.getServerlessMigrationStatus.GET")
  public Response getServerlessMigrationStatus(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName)
      throws Exception {
    final ClusterDescriptionView clusterDescriptionView =
        verifyClusterServerlessInstance(pGroup, pServerlessInstanceName);

    final JSONObject responseBody = new JSONObject();

    if (clusterDescriptionView != null) {
      responseBody.put(
          MIGRATION_REASON,
          verifyServerlessMigrationStatus(
              pGroup.getId(), pServerlessInstanceName, clusterDescriptionView.getUniqueId()));
    } else {
      responseBody.put(MIGRATION_REASON, MIGRATION_REASON_NONE);
    }

    return Response.ok().entity(responseBody.toString()).build();
  }

  @GET
  @Path("/{groupId}/{serverlessInstanceName}/freeMigrationOption")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.ServerlessInstanceDescriptionResource.getFreeMigrationOption.GET")
  public Response getFreeMigrationOption(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName) {
    final JSONObject responseBody = new JSONObject();

    try {
      final ServerlessFreeMigrationState currentState =
          _serverlessFreeMigrationStatusSvc.getFreeMigrationOption(
              pGroup.getId(), pServerlessInstanceName);
      responseBody.put("freeMigrationState", currentState.name());
    } catch (final Exception pE) {
      responseBody.put("freeMigrationState", ServerlessFreeMigrationState.NOT_ELIGIBLE.name());
    }

    return Response.ok().entity(responseBody.toString()).build();
  }

  // Protected for unit testing
  protected ClusterDescriptionView verifyClusterServerlessInstance(
      final Group pGroup, final String pServerlessInstanceName) throws Exception {
    final Optional<ClusterDescriptionView> clusterDescriptionView =
        Optional.of(Pair.of(pGroup, pServerlessInstanceName))
            .map(
                args -> {
                  try {
                    return getNDSUISvc()
                        .getServerlessClusterDescription(args.getLeft().getId(), args.getRight());
                  } catch (Exception pE) {
                    // Otherwise, ignore exceptions (i.e. CLUSTER_NOT_FOUND) to preserve
                    // pre-serverless functionality
                  }
                  return null;
                });
    if (clusterDescriptionView.map(cdv -> !cdv.isServerlessTenantCluster()).orElse(false)) {
      // Note that INVALID_CLOUD_PROVIDER is thrown regardless of whether the feature
      // NDS_SERVERLESS_FEATURE_ENABLED is enabled
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
    return clusterDescriptionView.orElse(null);
  }

  @VisibleForTesting
  protected void verifyClusterServerlessInstance(final ClusterDescriptionView pView)
      throws Exception {
    if (!pView.isServerlessTenantCluster()) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  @VisibleForTesting
  protected String verifyServerlessMigrationStatus(
      final ObjectId pGroupId, final String pServerlessInstanceName, final ObjectId pUniqueId)
      throws Exception {
    final Long tenantInstanceDataSize =
        _serverlessMetricsSvc
            .getDataSizeInBytesForTenants(List.of(pUniqueId), Instant.now())
            .getOrDefault(pUniqueId, 0L);

    // 5GB -> 5 * 1024^3
    final Long fiveGBInBytes = 5_368_709_120L;

    if (tenantInstanceDataSize > fiveGBInBytes) {
      return "dataSize";
    } else if (!_tenantEndpointSvc
        .getEndpointsForTenant(pGroupId, pServerlessInstanceName)
        .isEmpty()) {
      return "tenantEndpoint";
    } else if (_serverlessToFlexInstanceSvc
        .getServerlessInstancesAboveFlexOpsThreshold()
        .contains(pUniqueId)) {
      return "opsPerSec";
    }

    return MIGRATION_REASON_NONE;
  }

  private void verifyServerlessIsSupported() {
    if (!getAppSettings().isServerlessEnabled()) {
      throw ApiErrorCode.PROVIDER_UNSUPPORTED.exception(
          false, CloudProvider.SERVERLESS.getDescription());
    }
  }
}
