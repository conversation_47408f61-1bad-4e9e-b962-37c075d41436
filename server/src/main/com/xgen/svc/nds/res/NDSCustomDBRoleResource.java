package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.util.ModelValidationUtils;
import com.xgen.cloud.nds.common._public.model.NDSDBUserAction;
import com.xgen.cloud.nds.project._public.model.dbusers.AtlasAdminRole;
import com.xgen.cloud.nds.project._public.svc.NDSDBRoleSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.NDSDBUserActionView;
import com.xgen.svc.nds.model.ui.NDSDBUserBuiltInRoleView;
import com.xgen.svc.nds.model.ui.customRole.NDSCustomDBRoleView;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Arrays;
import java.util.stream.Collectors;

@Path("/nds/{groupId}/customDBRoles")
@Singleton
@AllowNDSCNRegionsOnlyGroups
public class NDSCustomDBRoleResource extends NDSBaseResource {
  private final NDSUISvc _ndsUISvc;
  private final NDSDBRoleSvc _ndsDbRoleSvc;
  private final FeatureFlagSvc _featureFlagSvc;

  @Inject
  public NDSCustomDBRoleResource(
      final NDSUISvc pNDSUISvc,
      final NDSDBRoleSvc pNdsDbRoleSvc,
      final FeatureFlagSvc pFeatureFlagSvc) {
    _ndsUISvc = pNDSUISvc;
    _ndsDbRoleSvc = pNdsDbRoleSvc;
    _featureFlagSvc = pFeatureFlagSvc;
  }

  @GET
  @Path("/roles")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCustomDBRoleResource.getCustomRoles.GET")
  public Response getCustomRoles(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup)
      throws Exception {
    return Response.ok(_ndsUISvc.getNDSCustomDBRoles(pGroup.getId())).build();
  }

  @GET
  @Path("/availableActions")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCustomDBRoleResource.getAvailableMongoDBUserActions.GET")
  public Response getAvailableMongoDBUserActions(@Context final Group pGroup) {
    return Response.ok(
            Arrays.stream(NDSDBUserAction.values())
                .map(NDSDBUserActionView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @POST
  @Path("/roles")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_ATLAS_ADMIN,
        RoleSet.GROUP_STREAM_PROCESSING_OWNER,
        RoleSet.GROUP_DATABASE_ACCESS_ADMIN
      },
      plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCustomDBRoleResource.addCustomRole.POST")
  public Response addCustomRole(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final NDSCustomDBRoleView pCustomDBRole)
      throws Exception {
    _ndsUISvc.createNDSCustomDBRole(pGroup, pCustomDBRole, pAuditInfo, pRequest);
    return Response.ok(_ndsUISvc.getNDSCustomDBRole(pGroup.getId(), pCustomDBRole.getRoleName()))
        .build();
  }

  @PATCH
  @Path("/roles/{roleName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_ATLAS_ADMIN,
        RoleSet.GROUP_STREAM_PROCESSING_OWNER,
        RoleSet.GROUP_DATABASE_ACCESS_ADMIN
      },
      plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCustomDBRoleResource.updateCustomRole.PATCH")
  public Response updateCustomRole(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("roleName") final String pRoleName,
      final NDSCustomDBRoleView pCustomRoleView)
      throws Exception {
    if (!ModelValidationUtils.isValidRoleName(pRoleName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid or null role name");
    }
    _ndsUISvc.updateNDSCustomDBRole(pGroup.getId(), pRoleName, pCustomRoleView, pAuditInfo);
    return Response.ok(_ndsUISvc.getNDSCustomDBRole(pGroup.getId(), pRoleName)).build();
  }

  @DELETE
  @Path("/roles/{roleName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {
        RoleSet.GROUP_ATLAS_ADMIN,
        RoleSet.GROUP_STREAM_PROCESSING_OWNER,
        RoleSet.GROUP_DATABASE_ACCESS_ADMIN
      },
      plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCustomDBRoleResource.deleteCustomRole.DELETE")
  public Response deleteCustomRole(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("roleName") final String pRoleName)
      throws Exception {
    if (!ModelValidationUtils.isValidRoleName(pRoleName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid or null role name");
    }
    _ndsUISvc.deleteNDSCustomDBRole(pGroup.getId(), pRoleName, pAuditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/builtIn")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCustomDBRoleResource.getBuiltInRolesList.GET")
  public Response getBuiltInRolesList() {
    return Response.ok(
            _ndsDbRoleSvc.getUserVisibleRoles().stream()
                // Not sure why this is needed, but it is in the original code
                .filter(r -> !r.getRole().equals(AtlasAdminRole.NAME))
                .map(NDSDBUserBuiltInRoleView::new)
                .toList())
        .build();
  }
}
