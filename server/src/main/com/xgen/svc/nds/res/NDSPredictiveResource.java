package com.xgen.svc.nds.res;

import static com.xgen.svc.mms.api.res.common.ApiErrorCode.PREDICTIVE_ERROR_INTERNAL;
import static com.xgen.svc.mms.api.res.common.ApiErrorCode.PREDICTIVE_ERROR_INVALID_TIME_RANGE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.client.MongoCursor;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.autoscaling.forecastmetrics._public.model.ui.ForecastShardSpecificCpuMetricsDataView;
import com.xgen.cloud.nds.autoscaling.forecastmetrics._public.predictionApi.PredictionApiClient;
import com.xgen.cloud.nds.autoscaling.predictive._private.dao.PredictiveAutoScalingTriggerDao;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.PredictiveAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.PredictiveAutoScalingTrigger;
import com.xgen.cloud.nds.autoscaling.predictive._public.svc.PredictiveAutoScalingContextSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.EnumUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/admin/nds/autoscaling/predictive")
@Singleton
public class NDSPredictiveResource extends ApiBaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(NDSPredictiveResource.class);
  private final NDSClusterSvc clusterSvc;
  private final PredictiveAutoScalingContextSvc predictiveAutoScalingContextSvc;
  private final PredictiveAutoScalingTriggerDao predictiveAutoScalingTriggerDao;
  private final PredictionApiClient predictionApiClient;

  @Inject
  public NDSPredictiveResource(
      final AppSettings appSettings,
      final NDSClusterSvc clusterSvc,
      final PredictiveAutoScalingContextSvc predictiveAutoScalingContextSvc,
      final PredictiveAutoScalingTriggerDao predictiveAutoScalingTriggerDao,
      final PredictionApiClient predictionApiClient) {
    super(appSettings);
    this.clusterSvc = clusterSvc;
    this.predictiveAutoScalingContextSvc = predictiveAutoScalingContextSvc;
    this.predictiveAutoScalingTriggerDao = predictiveAutoScalingTriggerDao;
    this.predictionApiClient = predictionApiClient;
  }

  @GET
  @Path("/groups/{groupId}/clusters/{clusterName}/predictions")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = UiCall.GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSPredictiveResource.getForecastMetrics.GET")
  public Response getForecastMetrics(
      @Context final Group group,
      @PathParam("clusterName") final String clusterName,
      @QueryParam("startTime") final Long startTimestamp,
      @QueryParam("endTime") final Long endTimestamp,
      @QueryParam("replicaSetId") @Nullable final String replicaSetId) {
    final Instant instantStart = Instant.ofEpochMilli(startTimestamp);
    final Instant instantEnd = Instant.ofEpochMilli(endTimestamp);
    if (PredictionApiClient.isPredictionTimeRangeInvalid(instantStart, instantEnd)) {
      throw PREDICTIVE_ERROR_INVALID_TIME_RANGE.exception(false);
    }
    final Cluster cluster =
        clusterSvc
            .getActiveCluster(group.getId(), clusterName)
            .orElseThrow(
                () -> ApiErrorCode.CLUSTER_NOT_FOUND.exception(false, clusterName, group.getId()));

    // If none provided, fetch metrics for all shards.
    final List<String> rsIds =
        Optional.ofNullable(replicaSetId)
            .map(Collections::singletonList)
            .orElse(cluster.getReplicaSets().stream().map(ReplicaSetHardware::getRsId).toList());

    Map<String, ForecastShardSpecificCpuMetricsDataView> resp;
    try {
      resp =
          predictionApiClient.getForecastMetricsForPrivateApi(
              instantStart,
              instantEnd,
              cluster.getClusterDescription().getUniqueId().toString(),
              rsIds);
    } catch (final Exception pE) {
      throw PREDICTIVE_ERROR_INTERNAL.exception(false, pE.toString());
    }

    return Response.ok(resp).build();
  }

  @POST
  @Path("/groups/{groupId}/clusters/{clusterName}/runCronASAP")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = UiCall.GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSPredictiveResource.requestCronCalculationForCluster.POST")
  public Response requestCronCalculationForCluster(
      @Context final Group group, @PathParam("clusterName") final String clusterName) {
    final PredictiveAutoScalingContext context = getPredictiveContext(group.getId(), clusterName);
    predictiveAutoScalingContextSvc.forceRecalculateTrigger(context, new Date());
    return Response.ok().build();
  }

  @GET
  @Path("/groups/{groupId}/clusters/{clusterName}/context")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = UiCall.GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSPredictiveResource.getPredictiveAutoScalingContext.GET")
  public Response getPredictiveAutoScalingContext(
      @Context final Group group, @PathParam("clusterName") final String clusterName)
      throws JsonProcessingException {
    final PredictiveAutoScalingContext context = getPredictiveContext(group.getId(), clusterName);
    String jsonResponse = new ObjectMapper().writeValueAsString(context);
    return Response.ok().entity(jsonResponse).build();
  }

  @GET
  @Path("/groups/{groupId}/triggers")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = UiCall.GroupSource.PATH)
  @Auth(endpointAction = "epa.global.NDSPredictiveResource.getAllPredictiveAutoScalingTriggers.GET")
  public Response getAllPredictiveAutoScalingTriggers(
      @Context final HttpServletRequest pRequest,
      @Context final Group group,
      @QueryParam("startDate") final Long startTimeStamp,
      @QueryParam("endDate") @Nullable final Long endTimestamp,
      @QueryParam("clusterName") @Nullable final String clusterName,
      @QueryParam("rsId") @Nullable final String rsId,
      @QueryParam("state") @Nullable final String state) {
    final Date startDate = new Date(startTimeStamp);
    final Date now = new Date();
    final Date endDate = endTimestamp == null ? now : new Date(endTimestamp);

    if (state != null
        && !EnumUtils.isValidEnumIgnoreCase(
            PredictiveAutoScalingTrigger.PredictiveTriggerState.class, state)) {
      throw ApiErrorCode.INVALID_PARAMETER.exception(
          false, PredictiveAutoScalingTrigger.FieldDefs.STATE);
    }

    final PredictiveAutoScalingTrigger.PredictiveTriggerState stateEnum =
        state == null
            ? null
            : PredictiveAutoScalingTrigger.PredictiveTriggerState.valueOf(state.toUpperCase());

    final MongoCursor<PredictiveAutoScalingTrigger> triggers =
        predictiveAutoScalingTriggerDao.getMongoCursorForAdminUIFilter(
            group.getId(), startDate, endDate, clusterName, rsId, stateEnum);

    return handlePagination(
        pRequest,
        triggers,
        trigger -> {
          try {
            // instead of creating view for trigger, we stringify the trigger and then using
            // ObjectMapper from Jackson to convert it into a map which is serializable as JSON
            return new ObjectMapper()
                .readValue(
                    predictiveAutoScalingTriggerDao.toJson(trigger), new TypeReference<>() {});
          } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
          }
        },
        false,
        200);
  }

  private PredictiveAutoScalingContext getPredictiveContext(
      final ObjectId groupId, final String clusterName) {
    final ClusterDescription clusterDescription =
        clusterSvc
            .getActiveClusterDescription(groupId, clusterName)
            .orElseThrow(
                () -> ApiErrorCode.CLUSTER_NOT_FOUND.exception(false, clusterName, groupId));
    if (!clusterDescription.isPredictiveAutoScalingEnabled(NodeTypeFamily.BASE)) {
      throw ApiErrorCode.PREDICTIVE_COMPUTE_AUTO_SCALING_NOT_ENABLED.exception(false);
    }

    return predictiveAutoScalingContextSvc
        .find(groupId, clusterDescription.getUniqueId())
        .orElseThrow(
            () ->
                ApiErrorCode.PREDICTIVE_COMPUTE_AUTO_SCALING_CONTEXT_NOT_FOUND.exception(
                    false, clusterDescription.getName(), groupId));
  }
}
