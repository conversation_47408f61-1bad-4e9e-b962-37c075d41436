package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.ServerlessTenantEndpointUpdateView;
import com.xgen.svc.nds.model.ui.ServerlessTenantEndpointView;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/privateEndpoint/serverless")
@Singleton
public class NDSServerlessTenantEndpointResource extends NDSBaseResource {

  private final NDSTenantEndpointSvc _ndsTenantEndpointSvc;

  @Inject
  public NDSServerlessTenantEndpointResource(final NDSTenantEndpointSvc pNDSTenantEndpointSvc) {
    _ndsTenantEndpointSvc = pNDSTenantEndpointSvc;
  }

  @POST
  @Path("/instance/{instanceName}/endpoint")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSServerlessTenantEndpointResource.createEndpoint.POST")
  public Response createEndpoint(
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("instanceName") final String pInstanceName)
      throws SvcException {
    _ndsTenantEndpointSvc.validateCloudProviderSupported(pNDSGroup.getGroupId(), pInstanceName);
    final ServerlessTenantEndpointView view =
        _ndsTenantEndpointSvc.createEndpoint(pNDSGroup.getGroupId(), pInstanceName, auditInfo);
    return Response.ok(view).build();
  }

  @DELETE
  @Path("/instance/{instanceName}/endpoint/{endpointId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSServerlessTenantEndpointResource.deleteEndpoint.DELETE")
  public Response deleteEndpoint(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo auditInfo,
      @PathParam("instanceName") final String pInstanceName,
      @PathParam("endpointId") final ObjectId pEndpointId)
      throws SvcException {
    _ndsTenantEndpointSvc.validateCloudProviderSupported(pNDSGroup.getGroupId(), pInstanceName);
    _ndsTenantEndpointSvc.requestDeleteEndpoint(
        pNDSGroup.getGroupId(), pInstanceName, pEndpointId, auditInfo);
    return Response.ok(
            _ndsTenantEndpointSvc.getEndpointView(
                pNDSGroup.getGroupId(), pInstanceName, pEndpointId))
        .build();
  }

  @PATCH
  @Path("/instance/{instanceName}/endpoint/{endpointId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSServerlessTenantEndpointResource.updateEndpoint.PATCH")
  public Response updateEndpoint(
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("instanceName") final String pInstanceName,
      @PathParam("endpointId") final ObjectId pEndpointId,
      final ServerlessTenantEndpointUpdateView pEndpointUpdateView)
      throws SvcException {
    _ndsTenantEndpointSvc.validateCloudProviderSupported(pNDSGroup.getGroupId(), pInstanceName);
    _ndsTenantEndpointSvc.updateEndpoint(
        pNDSGroup.getGroupId(), pInstanceName, pEndpointId, pEndpointUpdateView, auditInfo);
    return Response.ok(
            _ndsTenantEndpointSvc.getEndpointView(
                pNDSGroup.getGroupId(), pInstanceName, pEndpointId))
        .build();
  }

  @GET
  @Path("/instance/{instanceName}/endpoint/{endpointId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSServerlessTenantEndpointResource.getEndpointForInstance.GET")
  public Response getEndpointForInstance(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @PathParam("instanceName") final String pInstanceName,
      @PathParam("endpointId") final ObjectId pEndpointId)
      throws SvcException {
    _ndsTenantEndpointSvc.validateCloudProviderSupported(pNDSGroup.getGroupId(), pInstanceName);
    return Response.ok(
            _ndsTenantEndpointSvc.getEndpointView(
                pNDSGroup.getGroupId(), pInstanceName, pEndpointId))
        .build();
  }

  @GET
  @Path("/instance/{instanceName}/endpoint")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSServerlessTenantEndpointResource.getEndpointsForInstance.GET")
  public Response getEndpointsForInstance(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @PathParam("instanceName") final String pInstanceName)
      throws SvcException {
    _ndsTenantEndpointSvc.validateCloudProviderSupported(pNDSGroup.getGroupId(), pInstanceName);
    return Response.ok(
            _ndsTenantEndpointSvc.getEndpointViewsForTenant(pNDSGroup.getGroupId(), pInstanceName))
        .build();
  }

  @GET
  @Path("/{cloudProvider}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSServerlessTenantEndpointResource.getEndpointsForTenantProjectByCloudProvider.GET")
  public Response getEndpointsForTenantProjectByCloudProvider(
      @Context final HttpServletRequest pRequest,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @PathParam("cloudProvider") final String pCloudProvider) {
    return Response.ok(
            _ndsTenantEndpointSvc.getEndpointViewsForGroupByCloudProvider(
                pNDSGroup.getGroupId(), pCloudProvider))
        .build();
  }
}
