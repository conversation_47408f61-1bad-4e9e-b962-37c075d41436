package com.xgen.svc.nds.res;

import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_STREAM_PROCESSING_OWNER;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.svc.nds.svc.project.NDSGroupSvc.getRegionNames;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.deployment._public.util.PackageNamer.KMIPProxyPackageNamer;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.view.AuditLogView;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSKMSEARPrivateEndpoint;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzureKeyVaultEARPrivateEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkEndpointIdEntry;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.AWSCustomDNSEnabledView;
import com.xgen.svc.nds.model.ui.AWSKMSEARPrivateEndpointView;
import com.xgen.svc.nds.model.ui.AzureKeyVaultEARPrivateEndpointView;
import com.xgen.svc.nds.model.ui.EARPrivateEndpointView;
import com.xgen.svc.nds.model.ui.LimitsView;
import com.xgen.svc.nds.model.ui.NDSEncryptionAtRestView;
import com.xgen.svc.nds.model.ui.NDSPrivateNetworkEndpointIdEntryView;
import com.xgen.svc.nds.model.ui.NetworkPermissionEntryView;
import com.xgen.svc.nds.model.ui.PrivateIPModeView;
import com.xgen.svc.nds.model.ui.RegionUsageRestrictionsView;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.model.ui.SupportedAzureEnvironmentView;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.core.StreamingOutput;
import java.net.Inet4Address;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONException;
import org.json.JSONObject;

@Path("/nds")
@Singleton
public class NDSGroupResource extends NDSBaseResource {
  private final AppSettings _appSettings;
  private final NDSUISvc _ndsUISvc;
  private final AzureSubscriptionDao _azureSubscriptionDao;
  private final NDSGroupSvc _groupSvc;

  @Inject
  public NDSGroupResource(
      final AppSettings pAppSettings,
      final NDSUISvc pNDSUISvc,
      final AzureSubscriptionDao pAzureSubscriptionDao,
      final NDSGroupSvc pGroupSvc) {
    _appSettings = pAppSettings;
    _ndsUISvc = pNDSUISvc;
    _azureSubscriptionDao = pAzureSubscriptionDao;
    _groupSvc = pGroupSvc;
  }

  @GET
  @Path("/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getNDSGroupState.GET")
  public Response getNDSGroupState(@Context final Group pGroup) throws Exception {
    return Response.ok(_ndsUISvc.getGroupStatus(pGroup.getId())).build();
  }

  private Set<NetworkPermissionEntryView> getAllNetworkPermissionEntries(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    final Optional<Inet4Address> requestingAddress =
        NetUtils.getIPv4Address(pRequest.getRemoteAddr());

    if (requestingAddress.isPresent()) {
      final Inet4Address ipAddress = requestingAddress.get();
      return _ndsUISvc.getNetworkPermissionList(pGroup.getId(), ipAddress.getHostAddress(), true);
    } else {
      return _ndsUISvc.getNetworkPermissionList(pGroup.getId(), true);
    }
  }

  @GET
  @Path("/{groupId}/ipWhitelist")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getIpWhitelist.GET")
  public Response getIpWhitelist(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(getAllNetworkPermissionEntries(pGroup, pRequest)).build();
  }

  @PUT
  @Path("/{groupId}/ipWhitelist")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updateNetworkPermissions.PUT")
  public Response updateNetworkPermissions(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final Set<NetworkPermissionEntryView> pNetworkPermissions)
      throws Exception {
    final Set<NDSNetworkPermission> permissions = new HashSet<>();
    for (final NetworkPermissionEntryView view : pNetworkPermissions) {
      permissions.add(view.toNDSNetworkPermission());
    }

    try {
      _ndsUISvc.setNetworkPermissionList(pGroup.getId(), permissions, auditInfo, pRequest);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(pE.getMessageParams().get(0)).build();
      }
      throw pE;
    }

    return Response.ok(getAllNetworkPermissionEntries(pGroup, pRequest)).build();
  }

  @PUT
  @Path("/{groupId}/ipWhitelist/addPermissions")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.addIpWhitelistPermissions.PUT")
  public Response addIpWhitelistPermissions(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final Set<NetworkPermissionEntryView> pNetworkPermissions)
      throws Exception {
    final Set<NDSNetworkPermission> permissions = new HashSet<>();
    for (final NetworkPermissionEntryView view : pNetworkPermissions) {
      permissions.add(view.toNDSNetworkPermission());
    }

    try {
      _ndsUISvc.addNetworkPermissions(pGroup.getId(), permissions, auditInfo, pRequest);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(pE.getMessageParams().get(0)).build();
      }
      throw pE;
    }

    return Response.ok(getAllNetworkPermissionEntries(pGroup, pRequest)).build();
  }

  @GET
  @Path("/{groupId}/isWhitelistFreeOfZeroCidr")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.isWhitelistFreeOfZeroCidr.GET")
  public Response isWhitelistFreeOfZeroCidr(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(
            new JSONObject().put("isValid", _groupSvc.isWhitelistFreeOfZeroCidr(pGroup)).toString())
        .build();
  }

  private Set<NDSPrivateNetworkEndpointIdEntryView> getPrivateEndpointIdEntries(
      @Context final Group pGroup) throws Exception {
    return _ndsUISvc.getPrivateNetworkEndpointIds(pGroup.getId()).stream()
        .map(NDSPrivateNetworkEndpointIdEntryView::new)
        .collect(Collectors.toSet());
  }

  @GET
  @Path("/{groupId}/privateNetworkSettings/endpointServices/{cloudProvider}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getEndpointServices.GET")
  public Response getEndpointServices(
      @PathParam("cloudProvider") final String pCloudProvider,
      @Context final Group pGroup,
      @Context final HttpServletRequest pRequest)
      throws Exception {
    return Response.ok(
            new JSONObject(
                    _ndsUISvc.getDataLakeEndpointServices(CloudProvider.valueOf(pCloudProvider)))
                .toString())
        .build();
  }

  @GET
  @Path("/{groupId}/privateNetworkSettings/endpointIds")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getPrivateEndpointIds.GET")
  public Response getPrivateEndpointIds(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(getPrivateEndpointIdEntries(pGroup)).build();
  }

  @PUT
  @Path("/{groupId}/privateNetworkSettings/endpointIds")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updatePrivateNetworkSettings.PUT")
  public Response updatePrivateNetworkSettings(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final Set<NDSPrivateNetworkEndpointIdEntryView> pNDSPrivateNetworkEndpointIds)
      throws Exception {
    final Set<NDSPrivateNetworkEndpointIdEntry> entries =
        pNDSPrivateNetworkEndpointIds.stream()
            .map(NDSPrivateNetworkEndpointIdEntryView::toNDSPrivateNetworkEndpointIdEntry)
            .collect(Collectors.toSet());
    _ndsUISvc.updatePrivateNetworkSettings(pGroup.getId(), entries, auditInfo, true);

    return Response.ok(getPrivateEndpointIdEntries(pGroup)).build();
  }

  @GET
  @Path("/{groupId}/limits")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getLimits.GET")
  public Response getLimits(@Context final Group pGroup) throws SvcException {
    final Limits limits = _groupSvc.ensureGroup(pGroup.getId()).getLimits();
    return Response.ok(new LimitsView(limits)).build();
  }

  @GET
  @Path("/{groupId}/users")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getDatabaseUsers.GET")
  public Response getDatabaseUsers(@Context final Group pGroup) throws Exception {
    return Response.ok(_ndsUISvc.getDatabaseUsersWithCertExpiration(pGroup.getId())).build();
  }

  @GET
  @Path("/{groupId}/users/{database}/{username}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getDatabaseUser.GET")
  public Response getDatabaseUser(
      @Context final Group pGroup,
      @PathParam("database") final String pDatabase,
      @PathParam("username") final String pUsername)
      throws Exception {
    return Response.ok(_ndsUISvc.getDatabaseUserView(pGroup.getId(), pDatabase, pUsername)).build();
  }

  @GET
  @Path("/{groupId}/users/{username}/certs")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getDatabaseUserValidCerts.GET")
  public Response getDatabaseUserValidCerts(
      @Context final Group pGroup, @PathParam("username") final String pUsername) throws Exception {
    return Response.ok(_ndsUISvc.getValidCerts(pGroup.getId(), pUsername)).build();
  }

  @POST
  @Path("/{groupId}/users")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_ATLAS_ADMIN,
        GROUP_STREAM_PROCESSING_OWNER,
        RoleSet.GROUP_DATABASE_ACCESS_ADMIN
      },
      plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.addDatabaseUser.POST")
  public Response addDatabaseUser(
      @Context final Group pGroup, @Context final AuditInfo auditInfo, final NDSDBUserView pDBUser)
      throws Exception {
    _ndsUISvc.addDatabaseUser(pGroup, pDBUser, auditInfo);
    return Response.ok(
            _ndsUISvc.getDatabaseUserView(
                pGroup.getId(), pDBUser.getDatabase(), pDBUser.getUsername()))
        .build();
  }

  @PATCH
  @Path("/{groupId}/users/{database}/{username}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_ATLAS_ADMIN,
        GROUP_STREAM_PROCESSING_OWNER,
        RoleSet.GROUP_DATABASE_ACCESS_ADMIN
      },
      plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updateDatabaseUser.PATCH")
  public Response updateDatabaseUser(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("database") final String pDatabase,
      @PathParam("username") final String pUsername,
      final NDSDBUserView pDBUser)
      throws Exception {
    _ndsUISvc.updateDatabaseUser(pGroup, pDatabase, pUsername, pDBUser, false, auditInfo);
    return Response.ok(_ndsUISvc.getDatabaseUserView(pGroup.getId(), pDatabase, pUsername)).build();
  }

  @DELETE
  @Path("/{groupId}/users/{database}/{username}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GROUP_ATLAS_ADMIN,
        GROUP_STREAM_PROCESSING_OWNER,
        RoleSet.GROUP_DATABASE_ACCESS_ADMIN
      },
      plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.deleteDatabaseUser.DELETE")
  public Response deleteDatabaseUser(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("database") final String pDatabase,
      @PathParam("username") final String pUsername)
      throws Exception {
    _ndsUISvc.deleteDatabaseUser(pGroup.getId(), pDatabase, pUsername, false, auditInfo);
    return Response.accepted().build();
  }

  @DELETE
  @Path("/{groupId}/users/{username}/certs/{serialNumber}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, GROUP_STREAM_PROCESSING_OWNER},
      plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.revokeCert.DELETE")
  public Response revokeCert(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("username") final String pUsername,
      @PathParam("serialNumber") final long pSerialNumber)
      throws Exception {
    _ndsUISvc.revokeCert(pGroup.getId(), pUsername, pSerialNumber, auditInfo);
    return Response.accepted().build();
  }

  @GET
  @Path("/{groupId}/auditLog/auditEventActions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getAuditEventActions.GET")
  public Response getAuditEventActions(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsUISvc.getAuditEventActions()).build();
  }

  @GET
  @Path("/{groupId}/encryptionAtRest/awsKmsRegions")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Produces(MediaType.APPLICATION_JSON)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getAWSKMSRegions.GET")
  public Response getAWSKMSRegions(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(pGroup.getId());
    return Response.ok(
            Arrays.stream(AWSRegionName.values())
                .filter(
                    r ->
                        AWSNDSDefaults.getKMSRegionsAvailableForGroup(
                                _appSettings, pGroup, ndsGroup.getRegionUsageRestrictions())
                            .contains(r))
                .map(RegionView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Path("/{groupId}/encryptionAtRest/azureKeyVaultSupportedEnvironments")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Produces(MediaType.APPLICATION_JSON)
  @Auth(endpointAction = "epa.project.NDSGroupResource.getAzureKeyVaultSupportedEnvironments.GET")
  public Response getAzureKeyVaultSupportedEnvironments(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(
            pGroup.useCNRegionsOnly()
                ? List.of()
                : AzureNDSDefaults.AZURE_KEY_VAULT_SUPPORTED_ENVIRONMENTS.stream()
                    .map(SupportedAzureEnvironmentView::new)
                    .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Path("/{groupId}/encryptionAtRest")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Produces(MediaType.APPLICATION_JSON)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getEncryptionAtRest.GET")
  public Response getEncryptionAtRest(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsUISvc.getEncryptionAtRest(pGroup.getId())).build();
  }

  @PATCH
  @Path("/{groupId}/encryptionAtRest")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updateEncryptionAtRest.PATCH")
  public Response updateEncryptionAtRest(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final NDSEncryptionAtRestView pEncryptionAtRestView)
      throws Exception {
    _ndsUISvc.updateEncryptionAtRest(pGroup.getId(), pEncryptionAtRestView, true, auditInfo);
    return Response.ok(_ndsUISvc.getEncryptionAtRest(pGroup.getId())).build();
  }

  @GET
  @Path("/{groupId}/encryptionAtRest/{cloudProvider}/privateEndpoints")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getEncryptionAtRestPrivateEndpointsForCloudProvider.GET")
  public Response getEncryptionAtRestPrivateEndpointsForCloudProvider(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider)
      throws SvcException {
    final List<? extends CloudProviderPrivateEndpoint> privateEndpoints =
        _groupSvc.getEARPrivateEndpoints(pGroup.getId(), pCloudProvider);

    final List<? extends EARPrivateEndpointView> views =
        privateEndpoints.stream()
            .map(
                pEndpoint ->
                    switch (pEndpoint.getCloudProvider()) {
                      case AZURE ->
                          new AzureKeyVaultEARPrivateEndpointView(
                              (AzureKeyVaultEARPrivateEndpoint) pEndpoint);
                      case AWS ->
                          new AWSKMSEARPrivateEndpointView((AWSKMSEARPrivateEndpoint) pEndpoint);
                      default ->
                          throw new IllegalArgumentException(
                              String.format("Unsupported cloud provider: %s", pCloudProvider));
                    })
            .toList();

    return Response.ok(views).build();
  }

  @POST
  @Path("/{groupId}/encryptionAtRest/{cloudProvider}/privateEndpoints")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.requestEncryptionAtRestPrivateEndpointCreationForCloudProvider.POST")
  public Response requestEncryptionAtRestPrivateEndpointCreationForCloudProvider(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      final EARPrivateEndpointView pRequestView)
      throws SvcException {
    pRequestView.validateForCreation();
    final List<RegionName> regionNames =
        getRegionNames(pCloudProvider, List.of(pRequestView.getRegionName()));
    final List<EARPrivateEndpointView> privateEndpointViews =
        _ndsUISvc.requestEARPrivateEndpointCreation(pGroup.getId(), regionNames);

    return Response.ok(privateEndpointViews.get(0)).build();
  }

  @GET
  @Path("/{groupId}/encryptionAtRest/{cloudProvider}/privateEndpoints/{privateEndpointId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getEncryptionAtRestPrivateEndpointById.GET")
  public Response getEncryptionAtRestPrivateEndpointById(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("privateEndpointId") final ObjectId pEndpointId)
      throws SvcException {
    final CloudProviderPrivateEndpoint endpoint =
        _groupSvc.getEARPrivateEndpoint(pGroup.getId(), pCloudProvider, pEndpointId);

    final EARPrivateEndpointView view =
        switch (pCloudProvider) {
          case AZURE ->
              new AzureKeyVaultEARPrivateEndpointView((AzureKeyVaultEARPrivateEndpoint) endpoint);
          case AWS -> new AWSKMSEARPrivateEndpointView((AWSKMSEARPrivateEndpoint) endpoint);
          default ->
              throw new IllegalArgumentException(
                  String.format("Unsupported cloud provider: %s", pCloudProvider));
        };

    return Response.ok(view).build();
  }

  @DELETE
  @Path("/{groupId}/encryptionAtRest/{cloudProvider}/privateEndpoints/{privateEndpointId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.requestEncryptionAtRestPrivateEndpointDeletionById.DELETE")
  public Response requestEncryptionAtRestPrivateEndpointDeletionById(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider,
      @PathParam("privateEndpointId") final ObjectId pEndpointId)
      throws SvcException {
    _groupSvc.requestEARPrivateEndpointDeletion(pGroup.getId(), pCloudProvider, pEndpointId);
    return Response.ok().build();
  }

  @GET
  @Path("/{groupId}/encryptionAtRest/kmipTool/namer")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getKmipProxyNames.GET")
  public Response getKmipProxyNames(
      @Context final HttpServletRequest request, @Context final HttpServletResponse response)
      throws JSONException {
    final KMIPProxyPackageNamer namer =
        new KMIPProxyPackageNamer(_appSettings.getAutomationAgentVersion().toString(), true);
    return Response.ok(namer.toKMIPJSON().toString()).build();
  }

  @GET
  @Path("/{groupId}/auditLog")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getAuditLog.GET")
  public Response getAuditLog(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsUISvc.getAuditLog(pGroup.getId())).build();
  }

  @PATCH
  @Path("/{groupId}/auditLog")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updateAuditLog.PATCH")
  public Response updateAuditLog(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final AuditLogView pAuditLogView)
      throws Exception {
    _ndsUISvc.updateAuditLog(pGroup.getId(), pAuditLogView, false, pAuditInfo);
    return Response.ok(_ndsUISvc.getAuditLog(pGroup.getId())).build();
  }

  @GET
  @Path("/{groupId}/streamsAuditLog")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getStreamsAuditLog.GET")
  public Response getStreamsAuditLog(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsUISvc.getStreamsAuditLog(pGroup.getId())).build();
  }

  @PATCH
  @Path("/{groupId}/streamsAuditLog")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, RoleSet.GROUP_STREAM_PROCESSING_OWNER},
      plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updateStreamsAuditLog.PATCH")
  public Response updateStreamsAuditLog(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final AuditLogView pAuditLogView)
      throws Exception {
    var result = _ndsUISvc.updateStreamsAuditLog(pGroup.getId(), pAuditLogView, auditInfo);
    return Response.ok(result).build();
  }

  @GET
  @Path("/{groupId}/azureMultiTenantAppId")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupResource.getAzureMultiTenantAppIdForNetworkPeering.GET")
  public Response getAzureMultiTenantAppIdForNetworkPeering(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws Exception {
    final JSONObject azureMultiTenantAppId =
        new JSONObject()
            .put(
                "azureMultiTenantAppId",
                _azureSubscriptionDao.getMultiTenantAppIdForNetworkPeering().orElse(null));
    return Response.ok(azureMultiTenantAppId.toString()).build();
  }

  @GET
  @Path("/{groupId}/privateIpMode")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getPrivateIpMode.GET")
  public Response getPrivateIpMode(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws Exception {
    final boolean enabled = _ndsUISvc.getPrivateIpMode(pGroup.getId());
    return Response.ok(new PrivateIPModeView(enabled)).build();
  }

  @PATCH
  @Path("/{groupId}/privateIpMode")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updatePrivateIpMode.PATCH")
  public Response updatePrivateIpMode(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final PrivateIPModeView pPrivateIPModeView)
      throws Exception {
    final boolean enabled = pPrivateIPModeView.isEnabled();
    _ndsUISvc.setPrivateIpMode(
        pGroup.getId(),
        enabled,
        isFeatureFlagEnabled(
            FeatureFlag.ATLAS_ALLOW_DEPRECATED_VERSIONS, _appSettings, null, pGroup));
    return Response.ok(new PrivateIPModeView(enabled)).build();
  }

  @GET
  @Path("/{groupId}/regionUsageRestrictions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupResource.getRegionUsageRestrictions.GET")
  public Response getRegionUsageRestrictions(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws SvcException {
    final RegionUsageRestrictions regionUsageRestrictions =
        _groupSvc.ensureGroup(pGroup.getId()).getRegionUsageRestrictions();
    return Response.ok(new RegionUsageRestrictionsView(regionUsageRestrictions)).build();
  }

  @GET
  @Path("/{groupId}/awsCustomDNS")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.getAWSCustomDNSEnabled.GET")
  public Response getAWSCustomDNSEnabled(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws Exception {
    final NDSGroup group = _groupSvc.ensureGroup(pGroup.getId());
    final boolean enabled = group.isAWSCustomDNSEnabled();
    return Response.ok(new AWSCustomDNSEnabledView(enabled)).build();
  }

  @PATCH
  @Path("/{groupId}/awsCustomDNS")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.updateAWSCustomDNS.PATCH")
  public Response updateAWSCustomDNS(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final AWSCustomDNSEnabledView pAWSCustomDNSEnabledView) {
    final boolean enabled = pAWSCustomDNSEnabledView.isEnabled();
    _groupSvc.setAWSCustomDNSEnabled(pGroup.getId(), enabled);
    return Response.ok(new AWSCustomDNSEnabledView(enabled)).build();
  }

  @GET
  @Path("/{groupId}/users/{database}/{username}/certs")
  @Produces(MediaType.TEXT_PLAIN)
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, GROUP_STREAM_PROCESSING_OWNER},
      plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSGroupResource.generateCert.GET")
  public Response generateCert(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("username") final String pUsername,
      @QueryParam("monthsUntilExpiration") final Integer pMonthsUntilExpiration)
      throws Exception {
    // Default expiration of 3 months if no value is specified (minimum option we provide in the UI)
    final int monthsUntilExpiration = pMonthsUntilExpiration != null ? pMonthsUntilExpiration : 3;

    final TLSUtil.PEMKeyFile certPEM =
        _ndsUISvc.generateCertForDatabaseUser(
            pGroup.getId(), pUsername, monthsUntilExpiration, auditInfo);
    final String downloadFilename =
        String.format("X509-cert-%s.pem", certPEM.getKeyCertificate().getSerialNumber());

    final StreamingOutput result =
        out -> {
          out.write(
              TLSUtil.getPEMBytes(certPEM.getKeyCertificate().toASN1Structure(), certPEM.getKey()));
        };

    return Response.ok(result)
        .header(
            "Content-Disposition", String.format("attachment; filename=\"%s\"", downloadFilename))
        .build();
  }
}
