package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.SampleDatasetLoadStatusView;
import com.xgen.svc.nds.sampleDatasetLoad.svc.NDSSampleDatasetLoadSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.stream.Collectors;

@Path("/nds/sampleDatasetLoad")
@AllowNDSCNRegionsOnlyGroups
@Singleton
public class NDSSampleDatasetLoadResource extends NDSBaseResource {

  private final NDSSampleDatasetLoadSvc _ndsSampleDatasetLoadSvc;
  private final AppSettings _appSettings;

  @Inject
  public NDSSampleDatasetLoadResource(
      final NDSSampleDatasetLoadSvc pNDSSampleDatasetLoadSvc, final AppSettings pAppSettings) {
    _ndsSampleDatasetLoadSvc = pNDSSampleDatasetLoadSvc;
    _appSettings = pAppSettings;
  }

  @POST
  @Path("/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSSampleDatasetLoadResource.loadSampleDataset.POST")
  public Response loadSampleDataset(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final SampleDatasetLoadStatusView pSampleDatasetLoadStatusView)
      throws Exception {

    if (pSampleDatasetLoadStatusView.isSelectivelyLoadingSampleDatasetsEnabled()) {
      _ndsSampleDatasetLoadSvc.loadSelectedSampleDatasets(
          pGroup.getId(),
          pSampleDatasetLoadStatusView.getClusterName(),
          pSampleDatasetLoadStatusView.getSelectedDatasets(),
          auditInfo);
    } else {
      _ndsSampleDatasetLoadSvc.loadSampleDataset(
          pGroup.getId(),
          pSampleDatasetLoadStatusView.getClusterName(),
          auditInfo,
          pSampleDatasetLoadStatusView.getDataset(),
          false);
    }
    return Response.ok().build();
  }

  @GET
  @Path("/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSSampleDatasetLoadResource.getSampleDatasetLoadStatusForGroup.GET")
  public Response getSampleDatasetLoadStatusForGroup(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws Exception {
    final List<SampleDatasetLoadStatusView> views =
        _ndsSampleDatasetLoadSvc.getSampleDatasetLoadStatusForGroup(pGroup.getId()).stream()
            .map(status -> new SampleDatasetLoadStatusView(status))
            .collect(Collectors.toList());

    return Response.ok(views).build();
  }
}
