package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.OnlineArchiveView;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.util.OnlineArchiveBucketsUtil;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/onlineArchives/")
@Singleton
public class OnlineArchiveResource extends BaseResource {
  private final OnlineArchiveSvc _onlineArchiveSvc;
  private final NDSDataLakePublicSvc _ndsDataLakePublicSvc;
  private final OnlineArchiveBucketsUtil _onlineArchiveBucketsUtil;

  @Inject
  public OnlineArchiveResource(
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final NDSDataLakePublicSvc pNDSDataLakePublicSvc,
      final OnlineArchiveBucketsUtil pOnlineArchiveBucketsUtil) {
    _onlineArchiveSvc = pOnlineArchiveSvc;
    _ndsDataLakePublicSvc = pNDSDataLakePublicSvc;
    _onlineArchiveBucketsUtil = pOnlineArchiveBucketsUtil;
  }

  @GET
  @Path("/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.getOnlineArchiveForCluster.GET")
  public Response getOnlineArchiveForCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName) {
    final List<OnlineArchiveView> archiveViews =
        _onlineArchiveSvc.getOnlineArchiveViewsForCluster(pGroup.getId(), pClusterName);
    return Response.ok(archiveViews).build();
  }

  @POST
  @Path("/{clusterName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.createOnlineArchive.POST")
  public Response createOnlineArchive(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final OnlineArchiveView pOnlineArchiveView)
      throws SvcException {
    final OnlineArchiveVersion version =
        _onlineArchiveSvc.getOnlineArchiveDefaultVersion(
            pGroup, pOnlineArchiveView.getNonNullCollectionType());
    _onlineArchiveSvc.create(
        pOnlineArchiveView.toOnlineArchive(pGroupId, pClusterName, version), auditInfo);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.getOnlineArchivesForGroup.GET")
  public Response getOnlineArchivesForGroup(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) {
    final List<OnlineArchiveView> archiveViews =
        _onlineArchiveSvc.getOnlineArchiveViewsForGroup(pGroup.getId());
    return Response.ok(archiveViews).build();
  }

  @GET
  @Path("/{clusterName}/{archiveId}/metrics")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.getMetrics.GET")
  public Response getMetrics(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("archiveId") final ObjectId pArchiveId)
      throws SvcException {
    return Response.ok(_onlineArchiveSvc.getOnlineArchiveMetricsView(pGroupId, pArchiveId)).build();
  }

  @GET
  @Path("/{archiveId}/isArchivePausedBySystem")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.isArchivePausedBySystem.GET")
  public Response isArchivePausedBySystem(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @PathParam("archiveId") final ObjectId pArchiveId) {
    return Response.ok(_onlineArchiveSvc.isPausedBySystem(pArchiveId)).build();
  }

  @PATCH
  @Path("/{clusterName}/{archiveId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.updateOnlineArchive.PATCH")
  public Response updateOnlineArchive(
      @Context final AuditInfo auditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final OnlineArchiveView pOnlineArchiveView)
      throws SvcException {
    final OnlineArchive originalArchive =
        _onlineArchiveSvc
            .getOnlineArchive(pArchiveId)
            .orElseThrow(
                () -> new SvcException(NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST, pArchiveId));

    final OnlineArchive onlineArchive =
        pOnlineArchiveView.toOnlineArchive(
            pGroupId,
            pClusterName,
            pArchiveId,
            originalArchive.getState(),
            originalArchive.getOnlineArchiveVersion());

    _onlineArchiveSvc.updateOnlineArchive(pArchiveId, onlineArchive, auditInfo);
    final OnlineArchive updatedOnlineArchive = _onlineArchiveSvc.getOnlineArchive(pArchiveId).get();
    return Response.ok(new OnlineArchiveView(updatedOnlineArchive)).build();
  }

  @DELETE
  @Path("/{archiveId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.deleteOnlineArchive.DELETE")
  public Response deleteOnlineArchive(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("archiveId") final ObjectId pArchiveId)
      throws SvcException {
    _onlineArchiveSvc.markForDeletion(pArchiveId, auditInfo, pGroup.getId());
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{clusterName}/dlsSupportedRegions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.getDLZSupportedRegionsByProvider.GET")
  public Response getDLZSupportedRegionsByProvider(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    return Response.ok(_onlineArchiveSvc.getDLZSupportedRegionsByProvider(pGroupId, pClusterName))
        .build();
  }

  @GET
  @Path("/{clusterName}/defaultDLZSupportedRegion")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.getDefaultDLZSupportedRegionForCluster.GET")
  public Response getDefaultDLZSupportedRegionForCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    RegionName regionName =
        _onlineArchiveSvc.getDefaultDLZSupportedRegionForCluster(pGroup.getId(), pClusterName);
    RegionView regionView =
        new RegionView(
            regionName,
            regionName
                .getProvider()
                .equals(
                    _onlineArchiveSvc
                        .getHighestPriorityRegion(pGroup.getId(), pClusterName)
                        .getProvider()));
    return Response.ok(regionView).build();
  }

  @GET
  @Path("/{clusterName}/queryLogs.gz")
  @Produces("application/gzip")
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, RoleSet.GROUP_DATA_ACCESS_ANY
        // If you update these, be sure to update the public api as well
      },
      plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.OnlineArchiveResource.queryLogsForOnlineArchive.GET")
  public Response queryLogsForOnlineArchive(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("startDate") final Long pStartTimestamp,
      @QueryParam("endDate") final Long pEndTimestamp,
      @QueryParam("archiveOnly") final Boolean pArchiveOnly)
      throws SvcException {
    return Response.ok(
            _ndsDataLakePublicSvc.getOnlineArchiveQueryLogsForDates(
                pResponse,
                pGroup,
                pClusterName,
                pArchiveOnly,
                pStartTimestamp,
                pEndTimestamp,
                auditInfo,
                pRequest))
        .build();
  }
}
