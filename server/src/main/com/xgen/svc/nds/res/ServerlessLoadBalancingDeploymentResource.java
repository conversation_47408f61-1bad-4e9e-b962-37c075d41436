package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.serverless._public.view.EnvoyConfigurationSnapshotView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.serverless.svc.ServerlessLoadBalancingDeploymentSvc;
import com.xgen.svc.nds.serverless.util.EnvoyConfigUtil;
import com.xgen.svc.nds.serverless.view.ServerlessLoadBalancingDeploymentView;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import io.envoyproxy.controlplane.cache.v3.Snapshot;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

@Path("/admin/nds/serverlessLoadBalancingDeployments")
@Singleton
public class ServerlessLoadBalancingDeploymentResource extends BaseResource {
  final ServerlessLoadBalancingDeploymentSvc _serverlessLoadBalancingSvc;
  final NDSGroupSvc _ndsGroupSvc;
  final EnvoyConfigUtil _envoyConfigUtil;

  @Inject
  public ServerlessLoadBalancingDeploymentResource(
      final ServerlessLoadBalancingDeploymentSvc pServerlessLoadBalancingDeploymentSvc,
      final NDSGroupSvc pNDSGroupSvc,
      final EnvoyConfigUtil pEnvoyConfigUtil) {
    _serverlessLoadBalancingSvc = pServerlessLoadBalancingDeploymentSvc;
    _ndsGroupSvc = pNDSGroupSvc;
    _envoyConfigUtil = pEnvoyConfigUtil;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.ServerlessLoadBalancingDeploymentResource.getServerlessLoadBalancingDeployments.GET")
  public Response getServerlessLoadBalancingDeployments() {
    final List<ServerlessLoadBalancingDeploymentView> views =
        _serverlessLoadBalancingSvc.getAllDeployments().stream()
            .map(
                view ->
                    new ServerlessLoadBalancingDeploymentView(
                        view,
                        _serverlessLoadBalancingSvc.getServerlessProxyVersionForDeployment(
                            view.getGroupId())))
            .collect(Collectors.toList());
    return Response.ok(views).build();
  }

  @GET
  @Path("/{deploymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.ServerlessLoadBalancingDeploymentResource.getServerlessLoadBalancingDeploymentsById.GET")
  public Response getServerlessLoadBalancingDeploymentsById(
      @PathParam("deploymentId") final String pDeploymentId) throws SvcException {

    final ServerlessLoadBalancingDeployment deployment =
        _serverlessLoadBalancingSvc
            .getDeploymentById(new ObjectId(pDeploymentId))
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.INVALID_SERVERLESS_LOAD_BALANCING_DEPLOYMENT, pDeploymentId));
    final ServerlessLoadBalancingDeploymentView view =
        new ServerlessLoadBalancingDeploymentView(deployment);
    return Response.ok(view).build();
  }

  @PATCH
  @Path("/{deploymentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.ServerlessLoadBalancingDeploymentResource.updateLoadBalancingDeployment.PATCH")
  public Response updateLoadBalancingDeployment(
      @PathParam("deploymentId") final String pDeploymentId,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo auditInfo,
      final ServerlessLoadBalancingDeploymentView pServerlessLoadBalancingDeploymentView)
      throws SvcException {

    final Optional<ServerlessLoadBalancingDeployment> serverlessLoadBalancingDeployment =
        _serverlessLoadBalancingSvc.getDeploymentById(new ObjectId(pDeploymentId));
    if (serverlessLoadBalancingDeployment.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_SERVERLESS_LOAD_BALANCING_DEPLOYMENT, pDeploymentId);
    }

    final ServerlessLoadBalancingDeployment deployment = serverlessLoadBalancingDeployment.get();

    if (pServerlessLoadBalancingDeploymentView.getDesiredInstanceSize() != null
        && !pServerlessLoadBalancingDeploymentView
            .getDesiredInstanceSize()
            .equals(deployment.getDesiredInstanceSize())) {
      _serverlessLoadBalancingSvc.updateDesiredInstanceSize(
          deployment, pServerlessLoadBalancingDeploymentView.getDesiredInstanceSize(), auditInfo);
    }

    if (pServerlessLoadBalancingDeploymentView.getNumDesiredNodes() != null
        && !pServerlessLoadBalancingDeploymentView
            .getNumDesiredNodes()
            .equals(deployment.getNumDesiredNodes())) {
      _serverlessLoadBalancingSvc.updateNumDesiredNodes(
          deployment, pServerlessLoadBalancingDeploymentView.getNumDesiredNodes(), auditInfo);
    }

    if (pServerlessLoadBalancingDeploymentView.getNumDesiredPreallocatedRecords() != null
        && !pServerlessLoadBalancingDeploymentView
            .getNumDesiredPreallocatedRecords()
            .equals(deployment.getNumDesiredPreallocatedRecords())) {
      _serverlessLoadBalancingSvc.updateNumDesiredPreallocatedRecords(
          deployment,
          pServerlessLoadBalancingDeploymentView.getNumDesiredPreallocatedRecords(),
          auditInfo);
    }

    _ndsGroupSvc.setPlanningNow(deployment.getGroupId());
    return Response.ok().build();
  }

  @GET
  @Path("/{deploymentId}/envoyConfiguration")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.ServerlessLoadBalancingDeploymentResource.getEnvoyConfiguration.GET")
  public Response getEnvoyConfiguration(
      @PathParam("deploymentId") final ObjectId pDeploymentId,
      @Context final AppUser pUser,
      @Context final HttpServletRequest pRequest)
      throws SvcException {

    final Snapshot envoyConfigurationSnapshot =
        _serverlessLoadBalancingSvc
            .findEnvoyConfigurationForDeploymentId(pDeploymentId)
            .map(_envoyConfigUtil::createSnapshot)
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.NO_ENVOY_CONFIGURATION_FOUND_FOR_DEPLOYMENT, pDeploymentId))
            .getLeft();

    final EnvoyConfigurationSnapshotView view =
        new EnvoyConfigurationSnapshotView(envoyConfigurationSnapshot);

    return Response.ok(view).build();
  }
}
