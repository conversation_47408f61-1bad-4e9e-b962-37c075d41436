package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.util.ModelValidationUtils;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.CloudProviderContainerView;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/containers")
@AllowNDSCNRegionsOnlyGroups
@Singleton
public class NDSCloudProviderContainerResource extends NDSBaseResource {

  private final NDSCloudProviderContainerSvc _ndsContainerSvc;

  @Inject
  public NDSCloudProviderContainerResource(final NDSCloudProviderContainerSvc pContainerSvc) {
    _ndsContainerSvc = pContainerSvc;
  }

  @GET
  @Path("/{cloudProvider}/defaults")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCloudProviderContainerResource.getContainerDefaults.GET")
  public Response getContainerDefaults(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final String pCloudProviderName)
      throws SvcException {
    final String cloudProvider = pCloudProviderName.toUpperCase();
    if (!ModelValidationUtils.isValidCloudProviderName(cloudProvider)) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
    return Response.ok(_ndsContainerSvc.getContainerDefaults(pGroup.getId(), cloudProvider))
        .build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCloudProviderContainerResource.getContainers.GET")
  public Response getContainers(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws Exception {
    final List<CloudProviderContainerView> views =
        _ndsContainerSvc.getContainersForGroup(pGroup.getId());
    return Response.ok(views).build();
  }

  @PUT
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCloudProviderContainerResource.createContainer.PUT")
  public Response createContainer(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      final CloudProviderContainerView pView)
      throws SvcException {
    final ObjectId newContainerId = _ndsContainerSvc.upsertCloudContainer(pGroup.getId(), pView);

    return Response.accepted(Collections.singletonMap("containerId", newContainerId)).build();
  }

  @GET
  @Path("/{containerId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCloudProviderContainerResource.getContainer.GET")
  public Response getContainer(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("containerId") final ObjectId pContainerId)
      throws Exception {
    final Optional<CloudProviderContainerView> container =
        _ndsContainerSvc.getContainerViewById(pGroup.getId(), pContainerId);
    return Response.ok(
            container.orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.NOT_FOUND, String.format("container (%s)", pContainerId))))
        .build();
  }

  @PATCH
  @Path("/{containerId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSCloudProviderContainerResource.updateContainer.PATCH")
  public Response updateContainer(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      final CloudProviderContainerView pView)
      throws SvcException {
    _ndsContainerSvc.upsertCloudContainer(pGroup.getId(), pView);
    return Response.accepted("{}").build();
  }
}
