package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.tenantUpgrade.svc.ServerlessFreeMigrationStatusSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/nds/serverlessFreeMigration")
@Singleton
public class ServerlessFreeMigrationResource extends NDSBaseResource {

  private final ServerlessFreeMigrationStatusSvc _serverlessFreeMigrationStatusSvc;

  @Inject
  public ServerlessFreeMigrationResource(
      final ServerlessFreeMigrationStatusSvc pServerlessFreeMigrationStatusSvc) {
    _serverlessFreeMigrationStatusSvc = pServerlessFreeMigrationStatusSvc;
  }

  @POST
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_CLUSTER_MANAGER, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.ServerlessFreeMigrationResource.startServerlessFreeMigration.POST")
  public Response startServerlessFreeMigration(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      final ClusterDescriptionView pClusterDescriptionView)
      throws SvcException {
    if (!pClusterDescriptionView.isTenantCluster()
        || pClusterDescriptionView.isFlexTenantCluster()) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }

    if (_serverlessFreeMigrationStatusSvc.isEligibleToStartServerlessFreeMigration(
        pGroup.getId(), pClusterDescriptionView.getUniqueId(), false)) {
      _serverlessFreeMigrationStatusSvc.startServerlessFreeMigrationPlan(
          pAuditInfo, pClusterDescriptionView, pGroup);
      return Response.accepted(pClusterDescriptionView).build();
    } else {
      _serverlessFreeMigrationStatusSvc.setStatusIneligible(
          pGroup.getId(), pClusterDescriptionView.getName(), false);
      throw new SvcException(
          NDSErrorCode.SERVERLESS_INSTANCE_INELIGIBLE_FOR_MIGRATION_TO_FREE,
          pClusterDescriptionView.getName(),
          pGroup.getId());
    }
  }
}
