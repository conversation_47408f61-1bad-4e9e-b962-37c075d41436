package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.MaintenanceWindowView;
import com.xgen.svc.nds.model.ui.MongoDbEolPhasedVersionReleaseStatusView;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/maintenanceWindow")
@Singleton
@AllowNDSCNRegionsOnlyGroups
public class NDSGroupMaintenanceResource extends BaseResource {

  private final NDSUISvc _ndsUISvc;
  private final NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;

  @Inject
  public NDSGroupMaintenanceResource(
      final NDSUISvc pNDSUISvc,
      final NDSGroupMaintenanceSvc pNdsGroupMaintenanceSvc,
      final NDSResourcePolicySvc pNdsResourcePolicySvc) {
    _ndsUISvc = pNDSUISvc;
    _ndsGroupMaintenanceSvc = pNdsGroupMaintenanceSvc;
    _ndsResourcePolicySvc = pNdsResourcePolicySvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.getMaintenanceWindow.GET")
  public Response getMaintenanceWindow(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsUISvc.getMaintenanceWindow(pGroup)).build();
  }

  @GET
  @Path("/targetMongoDBVersions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.getTargetMongoDBVersionMap.GET")
  public Response getTargetMongoDBVersionMap(
      @Context final Group pGroup, @Context final HttpServletRequest pRequest) throws Exception {
    return Response.ok(_ndsGroupMaintenanceSvc.getMongoDBMajorVersionToFullVersionMapping())
        .build();
  }

  @GET
  @Path("/isInOngoingPhasedVersionRelease/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.getIsInOngoingPhasedVersionRelease.GET")
  public Response getIsInOngoingPhasedVersionRelease(
      @Context final Group pGroup,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("invalidateVersionCache") @DefaultValue("false")
          final boolean pInvalidateVersionCache)
      throws Exception {
    return Response.ok(
            _ndsGroupMaintenanceSvc.getIsInOngoingPhasedVersionRelease(
                pGroup.getId(), pClusterName, pInvalidateVersionCache))
        .build();
  }

  @GET
  @Path("/{clusterUniqueId}/mongoDbEolPhasedVersionReleaseStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.getMongoDbEolPhasedVersionReleaseStatus.GET")
  public Response getMongoDbEolPhasedVersionReleaseStatus(
      @Context final Group pGroup, @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId) {
    return Response.ok(
            new MongoDbEolPhasedVersionReleaseStatusView(
                _ndsGroupMaintenanceSvc.getMongoDbEolPhasedVersionReleaseStatus(
                    pGroup, pClusterUniqueId)))
        .build();
  }

  @PATCH
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.patchMaintenanceWindow.PATCH")
  public Response patchMaintenanceWindow(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final MaintenanceWindowView pMaintenanceWindowView)
      throws Exception {
    try {
      _ndsUISvc.updateMaintenanceWindow(pGroup, pMaintenanceWindowView, auditInfo);
    } catch (SvcException e) {
      if (e.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(e.getMessageParams().get(0)).build();
      }
      throw e;
    }
    return Response.ok(_ndsUISvc.getMaintenanceWindow(pGroup)).build();
  }

  @POST
  @Path("/defer")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.deferMaintenanceOneWeek.POST")
  public Response deferMaintenanceOneWeek(
      @Context final Group pGroup, @Context final AuditInfo auditInfo) throws Exception {
    _ndsUISvc.deferMaintenanceOneWeek(pGroup.getId(), auditInfo);
    return Response.ok(_ndsUISvc.getMaintenanceWindow(pGroup)).build();
  }

  @DELETE
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupMaintenanceResource.resetCustomMaintenanceWindow.DELETE")
  public Response resetCustomMaintenanceWindow(
      @Context final Group pGroup, @Context final AuditInfo auditInfo) throws Exception {
    try {
      _ndsUISvc.resetMaintenanceWindow(pGroup, auditInfo);
    } catch (SvcException e) {
      if (e.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(e.getMessageParams().get(0)).build();
      }
      throw e;
    }

    return Response.ok(_ndsUISvc.getMaintenanceWindow(pGroup)).build();
  }
}
