package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount.ServiceAccountProvisionStatus;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAWSIAMAccountDetailsView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAWSIAMRoleView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAzureAppDetailsView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAzureServicePrincipalView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessGCPServiceAccountView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessRoleView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSGCPServiceAccountSetupStatus;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;
import java.util.UUID;
import org.bson.types.ObjectId;

@AllowNDSCNRegionsOnlyGroups
@Path("/nds/{groupId}/cloudProviderAccess")
@Singleton
public class NDSGroupCloudProviderAccessResource extends BaseResource {
  private final NDSUISvc _ndsUISvc;
  private final NDSCloudProviderAccessSvc _cloudProviderAccessSvc;
  private final NDSGroupSvc _ndsGroupSvc;

  @Inject
  public NDSGroupCloudProviderAccessResource(
      final NDSUISvc pNDSUISvc,
      final NDSCloudProviderAccessSvc pCloudProviderAccessSvc,
      final NDSGroupSvc pNDSGroupSvc) {
    _ndsUISvc = pNDSUISvc;
    _cloudProviderAccessSvc = pCloudProviderAccessSvc;
    _ndsGroupSvc = pNDSGroupSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.getCloudProviderAccess.GET")
  public Response getCloudProviderAccess(@Context final Group pGroup) throws Exception {
    return Response.ok(_ndsUISvc.getCloudProviderAccess(pGroup.getId())).build();
  }

  @GET
  @Path("/{cloudProvider}/setupStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.getGCPServiceAccountSetupStatus.GET")
  public Response getGCPServiceAccountSetupStatus(
      @Context final Group pGroup, @PathParam("cloudProvider") final String pCloudProviderName)
      throws Exception {

    // only applicable for GCP
    Optional.ofNullable(CloudProvider.findByNameIgnoreCase(pCloudProviderName))
        .filter(c -> c.equals(CloudProvider.GCP))
        .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER));

    final ServiceAccountProvisionStatus status =
        _ndsUISvc.getGcpServiceAccountSetupStatus(pGroup.getId());

    return Response.ok(new NDSGCPServiceAccountSetupStatus(status, status.getDescription()))
        .build();
  }

  @GET
  @Path("/{cloudProvider}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.getRolesFromCloudProviderAccess.GET")
  public Response getRolesFromCloudProviderAccess(
      @Context final Group pGroup, @PathParam("cloudProvider") final String pCloudProviderName)
      throws Exception {
    final String cloudProvider = pCloudProviderName.toUpperCase();
    switch (CloudProvider.findByName(cloudProvider)) {
      case AWS:
        return Response.ok(
                _ndsUISvc
                    .getCloudProviderAccess(pGroup.getId())
                    .getNDSCloudProviderAccessAWSIAMRoleViews())
            .build();
      case AZURE:
        return Response.ok(
                _ndsUISvc
                    .getCloudProviderAccess(pGroup.getId())
                    .getNDSCloudProviderAccessAzureServicePrincipalViews())
            .build();
      case GCP:
        return Response.ok(
                _ndsUISvc
                    .getCloudProviderAccess(pGroup.getId())
                    .getNdsCloudProviderAccessGCPServiceAccountViews())
            .build();
      default:
        throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  @POST
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.addCloudProviderAccessRole.POST")
  public Response addCloudProviderAccessRole(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @QueryParam("asyncCreate") final boolean pAsyncCreate,
      final NDSCloudProviderAccessRoleView pNDSCloudProviderAccessRoleView)
      throws Exception {
    switch (pNDSCloudProviderAccessRoleView.getCloudProvider()) {
      case AWS -> {
        return Response.ok(
                _ndsUISvc.addAwsIamRoleToCloudProviderAccess(
                    pGroup,
                    (NDSCloudProviderAccessAWSIAMRoleView) pNDSCloudProviderAccessRoleView,
                    auditInfo))
            .build();
      }
      case AZURE -> {
        return Response.ok(
                _ndsUISvc.addAzureServicePrincipalToCloudProviderAccess(
                    pGroup,
                    (NDSCloudProviderAccessAzureServicePrincipalView)
                        pNDSCloudProviderAccessRoleView,
                    auditInfo))
            .build();
      }
      case GCP -> {
        final NDSCloudProviderAccessGCPServiceAccountView view =
            _ndsUISvc.setupGcpServiceAccountWithProjectProvisioning(
                pGroup,
                (NDSCloudProviderAccessGCPServiceAccountView) pNDSCloudProviderAccessRoleView,
                auditInfo);
        return Response.ok(view).build();
      }
      default -> throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  @PATCH
  @Path("/{roleId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.patchCloudProviderAccessRole.PATCH")
  public Response patchCloudProviderAccessRole(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("roleId") final ObjectId pRoleId,
      final NDSCloudProviderAccessRoleView pNDSCloudProviderAccessRoleView)
      throws Exception {
    switch (pNDSCloudProviderAccessRoleView.getCloudProvider()) {
      case AWS:
        final NDSCloudProviderAccessAWSIAMRoleView ndsCloudProviderAccessAWSIAMRoleView =
            (NDSCloudProviderAccessAWSIAMRoleView) pNDSCloudProviderAccessRoleView;
        return Response.ok(
                _ndsUISvc.updateAwsIamAssumedRoleARNInCloudProviderAccess(
                    pGroup, pRoleId, ndsCloudProviderAccessAWSIAMRoleView, auditInfo))
            .build();
      case AZURE:
        final NDSCloudProviderAccessAzureServicePrincipalView
            ndsCloudProviderAccessAzureServicePrincipalView =
                (NDSCloudProviderAccessAzureServicePrincipalView) pNDSCloudProviderAccessRoleView;
        return Response.ok(
                _ndsUISvc.updateAzureServicePrincipalInCloudProviderAccess(
                    pGroup, pRoleId, ndsCloudProviderAccessAzureServicePrincipalView, auditInfo))
            .build();
      default:
        throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  @DELETE
  @Path("/{cloudProvider}/{roleId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.deleteRoleFromCloudProviderAccess.DELETE")
  public Response deleteRoleFromCloudProviderAccess(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("cloudProvider") final String pCloudProviderName,
      @PathParam("roleId") final ObjectId pRoleId)
      throws Exception {
    final String cloudProvider = pCloudProviderName.toUpperCase();
    switch (CloudProvider.findByName(cloudProvider)) {
      case AWS:
        _ndsUISvc.removeAwsIamRoleFromCloudProviderAccess(pGroup.getId(), pRoleId, auditInfo);
        return Response.noContent().build();
      case AZURE:
        _ndsUISvc.removeAzureServicePrincipalInCloudProviderAccess(
            pGroup.getId(), pRoleId, auditInfo);
        return Response.noContent().build();
      case GCP:
        _ndsUISvc.removeGCPServiceAccountInCloudProviderAccess(pGroup.getId(), pRoleId, auditInfo);
        return Response.noContent().build();
      default:
        throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  @GET
  @Path("/{cloudProvider}/atlasAccountDetails")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSGroupCloudProviderAccessResource.getAtlasAccountDetails.GET")
  public Response getAtlasAccountDetails(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final String pCloudProviderName)
      throws SvcException {
    final String cloudProvider = pCloudProviderName.toUpperCase();
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(pGroup.getId());
    // For GCP, this endpoint should not be used because the service account must exist and be
    // created in the customer's GCP container in order for them to assign permissions to it.
    // Instead, use POST /nds/{groupId}/cloudProviderAccess.
    if (cloudProvider.equals(CloudProvider.AWS.name())) {
      final String awsIamRootAccountArn =
          _cloudProviderAccessSvc.getCloudProviderAccessRootARN(
              pGroup.useCNRegionsOnly(), ndsGroup.getRegionUsageRestrictions());
      final String externalId = UUID.randomUUID().toString();
      return Response.ok(
              new NDSCloudProviderAccessAWSIAMAccountDetailsView(awsIamRootAccountArn, externalId))
          .build();
    } else if (cloudProvider.equals(CloudProvider.AZURE.name())) {
      final String azureAppId = _cloudProviderAccessSvc.getCloudProviderAccessAzureAppId();
      return Response.ok(new NDSCloudProviderAccessAzureAppDetailsView(azureAppId)).build();
    } else {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }
}
