package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.nds.serverless._public.view.LivenessCheckerGroupConfigView;
import com.xgen.svc.nds.svc.MTMClusterSvc;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;

@Path("/api/private/serverless/livenessChecker")
@Singleton
public class ServerlessLivenessCheckerResource extends ApiBaseResource {
  private final MTMClusterSvc _mtmClusterSvc;

  @Inject
  public ServerlessLivenessCheckerResource(
      final AppSettings pSettings, final MTMClusterSvc pMTMClusterSvc) {
    super(pSettings);
    _mtmClusterSvc = pMTMClusterSvc;
  }

  @GET
  @Path("/config")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GLOBAL_SERVERLESS_LIVENESS_ADMIN})
  @Auth(endpointAction = "epa.global.ServerlessLivenessCheckerResource.getLivenessCheckerConfiguration.GET")
  public Response getLivenessCheckerConfiguration() {
    final List<LivenessCheckerGroupConfigView> configView =
        _mtmClusterSvc.buildServerlessLivenessCheckerConfiguration();

    return Response.ok(configView).build();
  }
}
