package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.azure._public.model.ui.AzurePeerNetworkView;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.ContainerPeerView;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.nds.svc.NDSContainerPeerSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/peers")
@Singleton
public class NDSPeerResource extends NDSBaseResource {

  private final NDSContainerPeerSvc _containerPeerSvc;

  @Inject
  public NDSPeerResource(final NDSContainerPeerSvc pPeerSvc) {
    _containerPeerSvc = pPeerSvc;
  }

  // Network Peering Routes
  @GET
  @Path("/{cloudProvider}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSPeerResource.getPeers.GET")
  public Response getPeers(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider)
      throws SvcException {
    return Response.ok(_containerPeerSvc.getPeersByProvider(pGroup.getId(), pCloudProvider))
        .build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSPeerResource.getPeers.GET")
  public Response getPeers(@Context final HttpServletResponse pRequest, @Context final Group pGroup)
      throws SvcException {
    return Response.ok(_containerPeerSvc.getPeerViewsForGroup(pGroup.getId())).build();
  }

  @PUT
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSPeerResource.addPeer.PUT")
  public Response addPeer(
      @Context final NDSGroup pNdsGroup,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final ContainerPeerView pView)
      throws SvcException {
    try {
      _containerPeerSvc.addPeer(pGroup, pNdsGroup, pView, auditInfo);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(pE.getMessageParams().get(0)).build();
      }
      throw pE;
    }
    return Response.accepted("{}").build();
  }

  @DELETE
  @Path("/{peerId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSPeerResource.requestDeletePeer.DELETE")
  public Response requestDeletePeer(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @PathParam("peerId") final ObjectId pPeerId)
      throws SvcException {
    try {
      _containerPeerSvc.requestDeleteContainerPeer(pGroup, pPeerId, auditInfo);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(pE.getMessageParams().get(0)).build();
      }
      throw pE;
    }
    return Response.accepted("{}").build();
  }

  @PATCH
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @AllowNDSCNRegionsOnlyGroups
  @Auth(endpointAction = "epa.project.NDSPeerResource.updatePeer.PATCH")
  public Response updatePeer(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      final ContainerPeerView pView)
      throws SvcException {
    try {
      _containerPeerSvc.updateContainerPeer(pGroup, pView, pView.getId(), auditInfo);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED)) {
        return Response.status(Status.FORBIDDEN).entity(pE.getMessageParams().get(0)).build();
      }
      throw pE;
    }

    return Response.accepted("{}").build();
  }

  // This is only used for Azure peering, as the process for Azure requires creating both peering
  // connections on our end
  // This endpoint validates that the customer has given us permission to create peering connections
  // in the customer Azure account
  @POST
  @Path("/validate")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSPeerResource.validatePeeringAccess.POST")
  public Response validatePeeringAccess(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final Group pGroup,
      final AzurePeerNetworkView pPeerNetworkView)
      throws SvcException {
    _containerPeerSvc.validatePeer(pPeerNetworkView);
    return Response.ok("{}").build();
  }
}
