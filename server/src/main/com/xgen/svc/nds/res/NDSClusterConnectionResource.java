package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.ratelimit._public.svc.RateLimitSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.view.AggregatedViewInfoView;
import com.xgen.cloud.nds.project._public.view.SearchAggregationRequestView;
import com.xgen.svc.nds.model.ui.NamespaceWithUUIDView;
import com.xgen.svc.nds.svc.NDSClusterConnectionSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.List;
import java.util.Optional;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/nds/clusters/{groupId}/{clusterName}/data")
@Singleton
public class NDSClusterConnectionResource {
  private static final Logger LOG = LoggerFactory.getLogger(NDSClusterConnectionResource.class);
  public static final int RATE_LIMIT_PERIOD_LENGTH_IN_MINUTES = 1;
  public static final int RATE_LIMIT_MAX_HITS_PER_PERIOD = 60;

  private final NDSClusterSvc _ndsClusterSvc;
  private final NDSClusterConnectionSvc _ndsClusterConnectionSvc;
  private final RateLimitSvc _ndsRateLimitSvc;

  @Inject
  public NDSClusterConnectionResource(
      final NDSClusterSvc pNdsClusterSvc,
      final NDSClusterConnectionSvc pNdsClusterConnectionSvc,
      final RateLimitSvc pNdsRateLimitSvc) {
    _ndsClusterSvc = pNdsClusterSvc;
    _ndsClusterConnectionSvc = pNdsClusterConnectionSvc;
    _ndsRateLimitSvc = pNdsRateLimitSvc;
  }

  private void throwIfRateLimited(final Group pGroup, final String pClusterName, final int pWeight)
      throws SvcException {
    final String rateLimitingId =
        String.format("clusterConnection-%s-%s", pGroup.getId(), pClusterName);
    if (!_ndsRateLimitSvc.isPermitted(
        rateLimitingId,
        RATE_LIMIT_PERIOD_LENGTH_IN_MINUTES,
        pWeight,
        RATE_LIMIT_MAX_HITS_PER_PERIOD)) {
      throw new SvcException(NDSErrorCode.RATE_LIMITED_CLUSTER_CONNECTION_API);
    }
  }

  @GET
  @Path("/databases")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.getDatabases.GET")
  public Response getDatabases(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      final List<String> databases =
          _ndsClusterConnectionSvc.listDatabaseNames(cluster, pAuditInfo);

      return Response.ok(databases).build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/databases/{databaseName}/collections")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.getCollections.GET")
  public Response getCollections(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("databaseName") final String pDatabaseName)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      final List<String> collections =
          _ndsClusterConnectionSvc.listCollectionNames(cluster, pDatabaseName, pAuditInfo);

      return Response.ok(collections).build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/namespaces")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.getNamespaces.GET")
  public Response getNamespaces(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      final List<String> namespaces = _ndsClusterConnectionSvc.listNamespaces(cluster, pAuditInfo);

      return Response.ok(namespaces).build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/hasNamespaces")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.getHasNamespaces.GET")
  public Response getHasNamespaces(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      final boolean hasNamespaces = _ndsClusterConnectionSvc.hasNamespaces(cluster, pAuditInfo);

      return Response.ok(hasNamespaces).build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/namespacesWithUUID")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.getNamespacesWithUUID.GET")
  public Response getNamespacesWithUUID(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      final List<NamespaceWithUUIDView> namespacesWithUUID =
          _ndsClusterConnectionSvc.listNamespacesWithUUID(cluster, pAuditInfo);
      return Response.ok(namespacesWithUUID).build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/aggregatedViewInfos")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.getAggregatedViewInfos.GET")
  public Response getAggregatedViewInfos(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      final List<AggregatedViewInfoView> listAggregatedViewInfo =
          _ndsClusterConnectionSvc.listAggregatedViewInfos(cluster, pAuditInfo);
      return Response.ok(listAggregatedViewInfo).build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @POST
  @Path("/databases/{databaseName}/collections/{collectionName}/runSearchAggregation")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.runSearchAggregation.POST")
  public Response runSearchAggregation(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("databaseName") final String pDatabaseName,
      @PathParam("collectionName") final String pCollectionName,
      final SearchAggregationRequestView pSearchAggregationRequest)
      throws SvcException {
    pSearchAggregationRequest.validate();
    throwIfRateLimited(pGroup, pClusterName, 2);
    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));
    try {
      return Response.ok(
              _ndsClusterConnectionSvc.aggregate(
                  cluster, pDatabaseName, pCollectionName, pSearchAggregationRequest, pAuditInfo))
          .build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/databases/{databaseName}/collections/{collectionName}/sampleCollectionFieldNames")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.sampleCollectionFiledNames.GET")
  public Response sampleCollectionFiledNames(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("databaseName") final String pDatabaseName,
      @PathParam("collectionName") final String pCollectionName,
      @QueryParam("sampleSize") final Integer pSampleSize,
      @QueryParam("returnSize") final Integer pReturnSize)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      return Response.ok(
              _ndsClusterConnectionSvc.sampleCollectionFieldNames(
                  cluster,
                  pDatabaseName,
                  pCollectionName,
                  Optional.ofNullable(pSampleSize).orElse(100),
                  Optional.ofNullable(pReturnSize).orElse(100),
                  pAuditInfo))
          .build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  @GET
  @Path("/databases/{databaseName}/collections/{collectionName}/sampleCollectionFieldNamesAndTypes")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_DATA_ACCESS_ANY, plan = PlanTypeSet.NDS)
  @Auth(endpointAction = "epa.project.NDSClusterConnectionResource.sampleCollectionFieldNamesAndTypes.GET")
  public Response sampleCollectionFieldNamesAndTypes(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("databaseName") final String pDatabaseName,
      @PathParam("collectionName") final String pCollectionName,
      @QueryParam("sampleSize") final Integer pSampleSize,
      @QueryParam("returnSize") final Integer pReturnSize)
      throws SvcException {
    throwIfRateLimited(pGroup, pClusterName, 1);

    final var cluster =
        _ndsClusterSvc
            .getActiveClusterDescription(pGroup.getId(), pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    try {
      return Response.ok(
              _ndsClusterConnectionSvc.sampleCollectionFieldNamesAndTypes(
                  cluster,
                  pDatabaseName,
                  pCollectionName,
                  Optional.ofNullable(pSampleSize).orElse(100),
                  Optional.ofNullable(pReturnSize).orElse(100),
                  pAuditInfo))
          .build();
    } catch (final SvcException e) {
      return handleClusterConnectionError(e);
    }
  }

  private Response handleClusterConnectionError(final SvcException pSvcException)
      throws SvcException {
    if (CommonErrorCode.TIMEOUT.equals(pSvcException.getErrorCode())) {
      return Response.status(Status.REQUEST_TIMEOUT).build();
    }
    if (CommonErrorCode.OPERATION_ERROR.equals(pSvcException.getErrorCode())) {
      LOG.info("Cluster Connection Operation Error", pSvcException);
      final JSONObject json = new JSONObject();
      json.put("message", pSvcException.getMessage());
      return Response.status(Response.Status.BAD_REQUEST).entity(json.toString()).build();
    }

    throw pSvcException;
  }
}
