package com.xgen.svc.nds.res;

import static net.logstash.logback.argument.StructuredArguments.kv;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.nds.rollingresync._public.model.CustomStepDownWindow;
import com.xgen.cloud.nds.rollingresync._public.model.RollingResync;
import com.xgen.cloud.nds.rollingresync._public.model.RollingResync.InitialSyncMethod;
import com.xgen.cloud.nds.rollingresync._public.model.RollingResync.Optimization;
import com.xgen.cloud.nds.rollingresync._public.model.RollingResync.Validation;
import com.xgen.cloud.nds.rollingresync._public.model.StepDownConfig;
import com.xgen.cloud.nds.rollingresync._public.model.StepDownConfig.StepDownTiming;
import com.xgen.cloud.nds.rollingresync._public.svc.RollingResyncSvc;
import com.xgen.svc.nds.model.ui.rollingResync.RollingResyncView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/admin/nds/rollingResync")
@Singleton
public class RollingResyncResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(RollingResyncResource.class);

  static final String ROLLING_RESYNC_NOT_FOUND_ERROR_MESSAGE =
      "Could not find a rolling resync with the id provided";
  static final String ROLLING_RESYNC_CANNOT_BE_CANCELLED_ERROR_MESSAGE =
      "The rolling resync cannot be cancelled because it does not have the status of "
          + "RESYNC_REQUESTED or RESYCNING";

  private final RollingResyncSvc _rollingResyncSvc;

  @Inject
  public RollingResyncResource(final RollingResyncSvc pRollingResyncSvc) {
    _rollingResyncSvc = pRollingResyncSvc;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/{groupId}")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY)
  @Auth(endpointAction = "epa.global.RollingResyncResource.getAllRollingResyncsByGroup.GET")
  public Response getAllRollingResyncsByGroup(@PathParam("groupId") final String pGroupId) {
    return Response.ok()
        .entity(
            _rollingResyncSvc.findAllByGroupId(new ObjectId(pGroupId)).stream()
                .map(RollingResyncView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/cluster")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.RollingResyncResource.requestResync.POST")
  public Response requestResync(
      @Context final AuditInfo pAuditInfo,
      @FormParam("groupId") final ObjectId pGroupId,
      @FormParam("clusterName") final String pClusterName,
      @FormParam("nodesRequestedToResync[]") final List<String> pNodesRequestedToResync,
      @FormParam("orderedNodesRequestedToResync") final String pOrderedNodesRequestedToResync,
      @FormParam("initialSyncMethod") final String pInitialSyncMethod,
      @FormParam("syncSourcesToExclude[]") final List<String> pSyncSourcesToExclude,
      @FormParam("bypassValidations[]") final List<String> pBypassValidations,
      @FormParam("stepDownTiming") final String pStepDownTiming,
      @FormParam("customStepDownStartDate") final Long pCustomStepDownStartDate,
      @FormParam("customStepDownEndDate") final Long pCustomStepDownEndDate,
      @FormParam("bypassOptimizations[]") final List<String> pBypassOptimizations,
      @FormParam("reason") final String pReason)
      throws SvcException {
    LOG.info(
        "Requesting a rolling reysnc for {}, {}, by {}, with {}, {}, {}, {}, {}, {}, {}, {}, {},"
            + " and {}",
        kv("groupId", pGroupId),
        kv("clusterName", pClusterName),
        kv("user", pAuditInfo.getUsername()),
        kv("nodesRequestedToResync", pNodesRequestedToResync),
        kv("orderedNodesRequestedToResync", pOrderedNodesRequestedToResync),
        kv("initialSyncMethod", pInitialSyncMethod),
        kv("syncSourcesToExclude", pSyncSourcesToExclude),
        kv("bypassValidations", pBypassValidations),
        kv("stepDownTiming", pStepDownTiming),
        kv("customStepDownStartDate", pCustomStepDownStartDate),
        kv("customStepDownEndDate", pCustomStepDownEndDate),
        kv("reason", pReason),
        kv("bypassOptimizations", pBypassOptimizations));

    final StepDownTiming stepDownTiming = StepDownTiming.valueOf(pStepDownTiming);
    final StepDownConfig stepDownConfig =
        getStepDownConfig(stepDownTiming, pCustomStepDownStartDate, pCustomStepDownEndDate);
    final boolean ordered = Boolean.parseBoolean(pOrderedNodesRequestedToResync);

    _rollingResyncSvc.requestRollingResync(
        pGroupId,
        pClusterName,
        pSyncSourcesToExclude,
        InitialSyncMethod.valueOf(pInitialSyncMethod),
        pBypassValidations.stream().map(v -> Validation.valueOf(v)).collect(Collectors.toSet()),
        stepDownConfig,
        pNodesRequestedToResync,
        pBypassOptimizations.stream().map(o -> Optimization.valueOf(o)).collect(Collectors.toSet()),
        ordered,
        pAuditInfo,
        pReason);
    return Response.ok().build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/cancel/{rollingResyncId}")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.RollingResyncResource.cancelRollingResync.POST")
  public Response cancelRollingResync(
      @Context final AuditInfo pAuditInfo,
      @PathParam("rollingResyncId") final ObjectId pRollingResyncId) {
    LOG.info(
        "Received a request to cancel a rolling resync. {} {}",
        kv("rollingResyncId", pRollingResyncId),
        kv("user", pAuditInfo.getUsername()));

    final Optional<RollingResync> rollingResync = _rollingResyncSvc.findById(pRollingResyncId);

    if (rollingResync.isEmpty()) {
      return Response.status(Status.NOT_FOUND)
          .entity(
              new JSONObject().put("message", ROLLING_RESYNC_NOT_FOUND_ERROR_MESSAGE).toString())
          .build();
    }

    if (!rollingResync.get().getStatus().isCancellable()) {
      return Response.status(Status.CONFLICT)
          .entity(
              new JSONObject()
                  .put("message", ROLLING_RESYNC_CANNOT_BE_CANCELLED_ERROR_MESSAGE)
                  .toString())
          .build();
    }

    _rollingResyncSvc.requestCancel(rollingResync.get());

    return Response.ok().build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/stepDownConfig/{rollingResyncId}")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_OPERATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.RollingResyncResource.modifyStepDownWindowRequest.POST")
  public Response modifyStepDownWindowRequest(
      @Context final AuditInfo pAuditInfo,
      @PathParam("rollingResyncId") final ObjectId pRollingResyncId,
      @FormParam("stepDownTiming") final String pStepDownTiming,
      @FormParam("customStepDownStartDate") final Long pCustomStepDownStartDate,
      @FormParam("customStepDownEndDate") final Long pCustomStepDownEndDate)
      throws SvcException {
    LOG.info(
        "Modifying step down config by {} for {} to new {}, {}, and {}",
        kv("user", pAuditInfo.getUsername()),
        kv("rollingResyncId", pRollingResyncId),
        kv("stepDownTiming", pStepDownTiming),
        kv("customStepDownStartDate", pCustomStepDownStartDate),
        kv("customStepDownEndDate", pCustomStepDownEndDate));

    final Optional<RollingResync> resyncOpt = _rollingResyncSvc.findById(pRollingResyncId);
    if (resyncOpt.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    final StepDownTiming stepDownTiming = StepDownTiming.valueOf(pStepDownTiming);
    final StepDownConfig stepDownConfig =
        getStepDownConfig(stepDownTiming, pCustomStepDownStartDate, pCustomStepDownEndDate);

    _rollingResyncSvc.setStepDownConfig(resyncOpt.get(), stepDownConfig).get();

    return Response.ok().build();
  }

  private static StepDownConfig getStepDownConfig(
      final StepDownTiming pStepDownTiming,
      final Long pCustomStepDownStartDate,
      final Long pCustomStepDownEndDate) {
    final StepDownConfig stepDownConfig;
    switch (pStepDownTiming) {
      case CUSTOM_PRIMARY_STEP_DOWN_WINDOW:
        stepDownConfig =
            new StepDownConfig(
                pStepDownTiming,
                new CustomStepDownWindow(
                    new Date(pCustomStepDownStartDate), new Date(pCustomStepDownEndDate)));
        break;
      case PROJECT_MAINTENANCE_WINDOW:
      case ANYTIME:
        stepDownConfig = new StepDownConfig(pStepDownTiming, new CustomStepDownWindow(null, null));
        break;
      default:
        throw new IllegalStateException("Unrecognized step down timing");
    }
    return stepDownConfig;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/allActive")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.RollingResyncResource.getAllActiveRollingResyncs.GET")
  public Response getAllActiveRollingResyncs() {
    return Response.ok()
        .entity(
            _rollingResyncSvc.findAllActiveRollingResyncs().stream()
                .map(RollingResyncView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/recent")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.RollingResyncResource.getRecentRollingResyncs.GET")
  public Response getRecentRollingResyncs(
      @QueryParam("term") final String pTerm,
      @QueryParam("limit") @DefaultValue("50") final int pLimit,
      @QueryParam("skip") @DefaultValue("0") final int pSkip) {
    return Response.ok()
        .entity(
            _rollingResyncSvc.findRecentRollingResyncs(pTerm, pLimit, pSkip).stream()
                .map(RollingResyncView::new)
                .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/recent/count")
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.RollingResyncResource.countRecentRollingResyncs.GET")
  public Response countRecentRollingResyncs(@QueryParam("term") final String pTerm) {
    final int count = (int) _rollingResyncSvc.countRecentRollingResyncs(pTerm);
    return Response.ok(new BasicDBObject("count", count)).build();
  }
}
