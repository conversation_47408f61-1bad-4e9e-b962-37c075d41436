package com.xgen.svc.nds.res;

import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_DATA_ACCESS_ADMIN;
import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_DATA_ACCESS_ANY;
import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_EXPLICIT_ACCESS;
import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_READ_ONLY;
import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_STREAM_PROCESSING_OWNER;
import static com.xgen.cloud.access.role._public.model.RoleSet.ORG_STREAM_PROCESSING_ADMIN;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AWS;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AZURE;
import static com.xgen.cloud.streams._public.model.StreamsMetric.OPERATOR_TARGET_INPUT_MESSAGE_COUNT;
import static com.xgen.cloud.streams._public.model.StreamsMetric.OPERATOR_TARGET_OUTPUT_MESSAGE_COUNT;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.billing._public.svc.EstimateSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.ui.NDSDataLakeDataProcessRegionView;
import com.xgen.cloud.nds.streams._public.model.StreamsConnectionType;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLink;
import com.xgen.cloud.nds.streams._public.model.ui.ClusterConnParamView;
import com.xgen.cloud.nds.streams._public.model.ui.KafkaConnParamView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamProcessorGetView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamProcessorView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsConnParamView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsSampleConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsTenantView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsTestBucketView;
import com.xgen.cloud.nds.streams._public.model.ui.UserSPIFeedbackView;
import com.xgen.cloud.nds.streams._public.svc.StreamsSPIFeedbackSvc;
import com.xgen.cloud.streams._public.model.VPCPeeringConnection.Status;
import com.xgen.cloud.streams._public.model.view.ApiStreamAWSAccountDetailsView;
import com.xgen.cloud.streams._public.model.view.ApiStreamAzureAccountDetailsView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsConnectionsAndPendingCountView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsPrivateLinkView;
import com.xgen.cloud.streams._public.model.view.vpc.ApiStreamsVPCPeeringChallengeView;
import com.xgen.cloud.streams._public.model.view.vpc.ApiStreamsVPCPeeringConnectionView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.svc.StreamsSvc;
import com.xgen.svc.nds.svc.streamsexportmetrics.CSVSvc;
import com.xgen.svc.nds.svc.streamsexportmetrics.PDFSvc;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/nds/streams/{groupId}")
@Singleton
public class NDSStreamsResource extends NDSBaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(NDSStreamsResource.class);

  private final StreamsSvc _streamsSvc;
  private final EstimateSvc _estimateSvc;
  private final StreamsSPIFeedbackSvc _streamsSPIFeedbackSvc;
  private final CSVSvc _streamsMetricsExportCSVSvc;
  private final PDFSvc _streamsMetricsExportPDFSvc;

  @Inject
  public NDSStreamsResource(
      final StreamsSvc streamsSvc,
      final EstimateSvc pEstimateSvc,
      final StreamsSPIFeedbackSvc pStreamsSPIFeedbackSvc,
      final CSVSvc pStreamsMetricsExportCSVSvc,
      final PDFSvc pStreamsMetricsExportPDFSvc) {
    _streamsSvc = streamsSvc;
    _estimateSvc = pEstimateSvc;
    _streamsSPIFeedbackSvc = pStreamsSPIFeedbackSvc;
    _streamsMetricsExportCSVSvc = pStreamsMetricsExportCSVSvc;
    _streamsMetricsExportPDFSvc = pStreamsMetricsExportPDFSvc;
  }

  private List<Document> convertToSupportExtendedJSON(List<Document> pipeline) {
    // The Java annotations are not parsing extended JSON into proper BSON types for
    // Document because we never added support for it in the deserializer. This converts
    // the pipeline to a JSON string and back into a Document using Document.parse,
    // which properly handles those types. MHOUSE-13190 to track the longer-term fix.
    return pipeline.stream().map(d -> d.toJson()).map(s -> Document.parse(s)).toList();
  }

  @GET
  @Path("/regions")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamRegions.GET")
  public Response getStreamRegions(@Context final Group pGroup) throws SvcException {
    return Response.ok(_streamsSvc.getAvailableRegions(pGroup)).build();
  }

  @GET
  @Path("/tenants")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamTenants.GET")
  public Response getStreamTenants(
      @Context final HttpServletRequest pRequest, @Context final Group pGroup) throws SvcException {
    return Response.ok(_streamsSvc.findByGroupId(pGroup.getId())).build();
  }

  @GET
  @Path("/tenants/{tenantName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {
        GROUP_STREAM_PROCESSING_OWNER,
        ORG_STREAM_PROCESSING_ADMIN,
        GROUP_DATA_ACCESS_ANY,
        GROUP_READ_ONLY
      })
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamTenant.GET")
  public Response getStreamTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("tenantName") final String pTenantName,
      @QueryParam("includeConnectionDetails") final boolean pIncludeConnectionDetails)
      throws Exception {
    StreamsTenantView sTenantView =
        _streamsSvc.findTenantWithConnections(
            pGroup, pTenantName, pIncludeConnectionDetails, pAuditInfo, pAppUser);
    _streamsSvc.unsetProxyInfoInList(sTenantView.getConnections());
    return Response.ok(sTenantView).build();
  }

  @PATCH
  @Path("/tenants/{tenantName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.updateStreamTenant.PATCH")
  public Response updateStreamTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName,
      final NDSDataLakeDataProcessRegionView pProcessRegionView)
      throws Exception {

    return Response.ok(
            _streamsSvc.updateTenant(pGroup, pTenantName, pProcessRegionView, pAuditInfo))
        .build();
  }

  @DELETE
  @Path("/tenants/{tenantName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.deleteStreamTenant.DELETE")
  public Response deleteStreamTenant(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("tenantName") final String pTenantName)
      throws Exception {

    _streamsSvc.deleteTenant(pGroup, pTenantName, pAuditInfo, true);
    return Response.ok().build();
  }

  @GET
  @Path("/tenants/{tenantName}/metrics")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamTenantMetrics.GET")
  public Response getStreamTenantMetrics(
      @Context final Group pGroup, @PathParam("tenantName") final String pTenantName)
      throws Exception {
    return Response.ok(_streamsSvc.getStreamTenantMetrics(pGroup.getId(), pTenantName)).build();
  }

  @GET
  @Path("/tenants/{tenantName}/streamProcessors")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {
        GROUP_STREAM_PROCESSING_OWNER,
        ORG_STREAM_PROCESSING_ADMIN,
        GROUP_DATA_ACCESS_ANY,
        GROUP_READ_ONLY
      })
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamTenantProcessors.GET")
  public Response getStreamTenantProcessors(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @PathParam("tenantName") final String pTenantName)
      throws Exception {
    List<StreamProcessorView> processors =
        _streamsSvc.getStreamTenantProcessors(pGroup.getId(), pTenantName, true, false, true);

    return Response.ok(
            _streamsSvc.filterPipelineInfo(pAppUser, pGroup.getOrgId(), pGroup.getId(), processors))
        .build();
  }

  @POST
  @Path("/tenants/{tenantName}/processor")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.createStreamProcessor.POST")
  public Response createStreamProcessor(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      final StreamProcessorView body)
      throws Exception {
    if (body == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "body");
    }

    final var name = body.getName() == null ? "" : body.getName().trim();
    if (name.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "name");
    }

    final var rawPipeline = body.getPipeline() == null ? List.<Document>of() : body.getPipeline();

    if (rawPipeline.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "pipeline");
    }

    final var pipeline = convertToSupportExtendedJSON(rawPipeline);
    final var processor =
        _streamsSvc.createStreamProcessor(
            auditInfo, pGroup.getId(), pTenantName, name, pipeline, null);
    return Response.ok(processor).build();
  }

  @GET
  @Path("/tenants/{tenantName}/processor/{processorName}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {
        GROUP_STREAM_PROCESSING_OWNER,
        ORG_STREAM_PROCESSING_ADMIN,
        GROUP_DATA_ACCESS_ANY,
        GROUP_READ_ONLY
      })
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamProcessor.GET")
  public Response getStreamProcessor(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("processorName") final String pProcessorName)
      throws Exception {
    StreamProcessorGetView processor =
        _streamsSvc.getStreamProcessor(pGroup.getId(), pTenantName, pProcessorName);
    return Response.ok(processor).build();
  }

  @POST
  @Path("/tenants/{tenantName}/processor/{processorName}:start")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.startStreamProcessor.POST")
  public Response startStreamProcessor(
      @Context final Group pGroup,
      @Context final AuditInfo auditInfo,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @PathParam("processorName") final String pProcessorName)
      throws Exception {
    _streamsSvc.startStreamProcessor(
        pGroup, pTenantName, pProcessorName, auditInfo.getUsername(), null);
    return Response.ok().build();
  }

  @POST
  @Path("/tenants/{tenantName}/processor/{processorName}:stop")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.stopStreamProcessor.POST")
  public Response stopStreamProcessor(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @PathParam("processorName") final String pProcessorName)
      throws Exception {
    _streamsSvc.stopStreamProcessor(pGroup, pTenantName, pProcessorName);
    return Response.ok().build();
  }

  @DELETE
  @Path("/tenants/{tenantName}/processor/{processorName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.deleteStreamProcessor.DELETE")
  public Response deleteStreamProcessor(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @PathParam("processorName") final String pProcessorName)
      throws Exception {
    _streamsSvc.dropStreamProcessor(pGroup, pTenantName, pProcessorName);
    return Response.ok().build();
  }

  @POST
  @Path("/tenants")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.createStreamTenant.POST")
  public Response createStreamTenant(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      final StreamsTenantView pTenantView)
      throws Exception {
    if (!ValidationUtils.isValidTenantName((pTenantView.getName()))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Tenant name");
    }
    StreamsTenantView created = _streamsSvc.createTenant(pTenantView, pGroup, pAuditInfo, true);
    return Response.ok(created).build();
  }

  @GET
  @Path("/tenants/{name}/connections")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ANY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamConnections.GET")
  public Response getStreamConnections(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("name") final String pTenantName)
      throws Exception {
    final StreamsTenantView tenant =
        _streamsSvc.findTenantWithConnections(pGroup, pTenantName, true, pAuditInfo, pAppUser);
    _streamsSvc.unsetProxyInfoInList(tenant.getConnections());
    return Response.ok(tenant.getConnections()).build();
  }

  @GET
  @Path("/tenants/{name}/connections/{connectionName}")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getStreamConnection.GET")
  public Response getStreamConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("name") final String pTenantName,
      @PathParam("connectionName") final String pConnectionName)
      throws Exception {
    StreamsConnectionView connection =
        _streamsSvc.findConnection(pGroup, pTenantName, pConnectionName, pAppUser);
    return Response.ok(_streamsSvc.unsetProxyInfo(connection)).build();
  }

  @PATCH
  @Path("/tenants/{name}/connections")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.editStreamConnection.PATCH")
  public Response editStreamConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("name") final String pTenantName,
      final StreamsConnectionView pStreamsConnectionView)
      throws Exception {

    String connectionName = pStreamsConnectionView.getName();

    final StreamsConnectionView updated =
        _streamsSvc.updateConnection(
            pStreamsConnectionView, connectionName, pTenantName, pGroup, pAuditInfo, pAppUser);

    return Response.ok(_streamsSvc.unsetProxyInfo(updated)).build();
  }

  @POST
  @Path("/tenants/{name}/createConnection")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.createStreamConnection.POST")
  public Response createStreamConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("name") final String pTenantName,
      final StreamsConnectionView pStreamsConnectionView)
      throws Exception {

    if ((pStreamsConnectionView.getType() == StreamsConnectionType.Sample
            && !StreamsSampleConnectionView.isSampleConnection(pStreamsConnectionView.getName()))
        || !ValidationUtils.ASCII_PRINTABLE_CHARACTERS
            .matcher(pStreamsConnectionView.getName())
            .matches()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "name");
    }

    final StreamsConnectionView updated =
        _streamsSvc.createConnection(
            pStreamsConnectionView, pTenantName, pGroup, pAuditInfo, pAppUser);

    return Response.ok(_streamsSvc.unsetProxyInfo(updated)).build();
  }

  @POST
  @Path("/{tenantName}/validateAWSBucket")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.validateAWSBucket.POST")
  public Response validateAWSBucket(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      StreamsTestBucketView pStreamsTestBucketView)
      throws Exception {
    _streamsSvc.validateAWSBucket(
        pStreamsTestBucketView.testBucket(),
        pGroup.getId(),
        pStreamsTestBucketView.roleArn(),
        pTenantName);

    return Response.ok().build();
  }

  @DELETE
  @Path("/tenants/{name}/connections/{connectionName}")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.deleteStreamConnection.DELETE")
  public Response deleteStreamConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("name") final String pTenantName,
      @PathParam("connectionName") final String pConnectionName)
      throws Exception {

    _streamsSvc.deleteConnection(pConnectionName, pTenantName, pGroup, pAuditInfo, pAppUser);

    return Response.ok().build();
  }

  @POST
  @Path("/tenants/{name}/connections/{connectionName}")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ANY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.viewStreamsConnection.POST")
  public Response viewStreamsConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("name") final String pTenantName,
      @PathParam("connectionName") final String pConnectionName)
      throws Exception {

    _streamsSvc.viewConnectionRegistryEvent(pConnectionName, pTenantName, pGroup, pAuditInfo);

    return Response.ok().build();
  }

  @POST
  @Path("/costEstimate")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSStreamsResource.costEstimate.POST")
  public Response costEstimate(@Context final Group pGroup, StreamsTenantView pStreamTenantView)
      throws Exception {
    final String pRegion = pStreamTenantView.getDataProcessRegion().getRegion();
    final CloudProvider pCloudProvider =
        pStreamTenantView.getDataProcessRegion().getCloudProvider();

    List<RegionView> regions = _streamsSvc.getAvailableRegions(pGroup);
    boolean exists =
        regions.stream()
            .anyMatch(
                r ->
                    r.getProvider().equalsIgnoreCase(pCloudProvider.getDescription())
                        && r.getKey().equalsIgnoreCase(pRegion));

    if (!exists) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Cloud Provider or Region");
    }

    if (pCloudProvider == AWS) {
      return Response.ok(
              _estimateSvc.getAWSStreamInstanceCostEstimate(AWSRegionName.valueOf(pRegion)))
          .build();
    } else if (pCloudProvider == AZURE) {
      return Response.ok(
              _estimateSvc.getAzureStreamInstanceCostEstimate(AzureRegionName.valueOf(pRegion)))
          .build();
    }

    throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Cloud Provider");
  }

  @POST
  @Path("/costEstimate/vpcPeering")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSStreamsResource.vpcPeeringCostEstimate.POST")
  public Response vpcPeeringCostEstimate(StreamsTenantView streamTenantView) throws Exception {
    final String region = streamTenantView.getDataProcessRegion().getRegion();
    final CloudProvider cloudProvider = streamTenantView.getDataProcessRegion().getCloudProvider();

    if (cloudProvider == AWS) {
      return Response.ok(
              _estimateSvc.getAWSStreamConnectionVPCPeeringCostEstimate(
                  AWSRegionName.valueOf(region)))
          .build();
    } else if (cloudProvider == AZURE) {
      return Response.ok(_estimateSvc.getAzureStreamConnectionVPCPeeringCostEstimate()).build();
    }

    throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Cloud Provider");
  }

  @GET
  @Path("/tenants/{name}/auditLogs.gz")
  @Produces("application/gzip")
  @UiCall(
      roles = {GROUP_DATA_ACCESS_ANY, GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN
        // If you update these, be sure to update the public api as well
      })
  @Auth(endpointAction = "epa.project.NDSStreamsResource.auditLogsForTenant.GET")
  public Response auditLogsForTenant(
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("name") final String pStreamsTenantName,
      @QueryParam("startDate") final Long pStartTimestamp,
      @QueryParam("endDate") final Long pEndTimestamp)
      throws SvcException {
    if (pStartTimestamp == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "startDate");
    }
    if (pEndTimestamp == null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "endDate");
    }

    if (!ValidationUtils.isValidTenantName(pStreamsTenantName)
        || _streamsSvc.isMissingTenant(pGroup.getId(), pStreamsTenantName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Streams Tenant");
    }

    Instant startInstant = Instant.ofEpochSecond(pStartTimestamp);
    Instant endInstant = Instant.ofEpochSecond(pEndTimestamp);

    StreamingOutput stream =
        _streamsSvc.getAuditLogsStream(
            pResponse, pAuditInfo, pGroup, pStreamsTenantName, startInstant, endInstant);
    return Response.ok(stream).build();
  }

  @DELETE
  @Path("/tenants/{name}/auditLogs")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.deleteAuditLogs.DELETE")
  public Response deleteAuditLogs(
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("name") final String pStreamsTenantName)
      throws SvcException {

    if (!ValidationUtils.isValidTenantName(pStreamsTenantName)
        || _streamsSvc.isMissingTenant(pGroup.getId(), pStreamsTenantName)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "Invalid Streams Tenant");
    }

    try {
      _streamsSvc.deleteAuditLogs(pAuditInfo, pGroup, pStreamsTenantName);
    } catch (SvcException ex) {
      return Response.serverError().build();
    }

    return Response.ok().build();
  }

  @POST
  @Path("/spiFeedback")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(roles = GROUP_EXPLICIT_ACCESS)
  @Auth(endpointAction = "epa.project.NDSStreamsResource.saveSPIFeedback.POST")
  public Response saveSPIFeedback(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      final UserSPIFeedbackView pFeedback)
      throws SvcException {

    _streamsSPIFeedbackSvc.registerFeedback(pGroup, pUser, pFeedback);

    return Response.ok().build();
  }

  @POST
  @Path("/privateLinkConnections")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.createPrivateLinkConnection.POST")
  public Response createPrivateLinkConnection(
      @Context final Group pGroup, final ApiStreamsPrivateLinkView pView) throws SvcException {
    return Response.ok(_streamsSvc.createPrivateLinkConnection(pView, pGroup, new ObjectId()))
        .build();
  }

  @DELETE
  @Path("/privateLinkConnections/{connectionId}")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.deletePrivateLinkConnection.DELETE")
  public Response deletePrivateLinkConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("connectionId") final String pConnectionId)
      throws SvcException {
    _streamsSvc.deletePrivateLinkConnection(pGroup.getId(), new ObjectId(pConnectionId));
    return Response.accepted().build();
  }

  @GET
  @Path("/privateLinkConnections")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ANY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getPrivateLinkConnections.GET")
  public Response getPrivateLinkConnections(@Context final Group pGroup) throws SvcException {
    return Response.ok(
            _streamsSvc.getPrivateLinkConnections(pGroup.getId()).stream()
                .map(ApiStreamsPrivateLinkView::getView)
                .collect(Collectors.toList()))
        .build();
  }

  @GET
  @Path("/privateLinkConnections/{connectionId}")
  @Feature(FeatureFlag.STREAMS_ENABLED)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getPrivateLinkConnection.GET")
  public Response getPrivateLinkConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("connectionId") final String pConnectionId)
      throws SvcException {
    if (!ObjectId.isValid(pConnectionId)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "connectionId");
    }
    ObjectId connectionId = new ObjectId(pConnectionId);
    final Optional<StreamsPrivateLink> connection =
        _streamsSvc.getPrivateLinkConnection(pGroup.getId(), connectionId);
    if (connection.isEmpty()) {
      throw new SvcException(CommonErrorCode.NOT_FOUND, "connectionId");
    }
    return Response.ok(ApiStreamsPrivateLinkView.getView(connection.get())).build();
  }

  @GET
  @Path("/vpcPeeringConnections")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getVPCPeeringConnections.GET")
  public Response getVPCPeeringConnections(
      @Context final Group pGroup,
      @QueryParam("requesterAccountIds") final String pRequesterAccountIds) {
    List<String> pRequesterAccountIdsList =
        pRequesterAccountIds != null
            ? Arrays.asList(pRequesterAccountIds.split(","))
            : Collections.emptyList();

    List<ApiStreamsVPCPeeringConnectionView> peeringConnections =
        _streamsSvc.getVPCPeeringConnections(pGroup.getId()).stream()
            .filter(
                conn ->
                    conn.getCloudStatus().equals("active")
                        || conn.getLocalStatus() == Status.ACTIVE
                        || conn.getCloudStatus().equals("pending-acceptance")
                            && conn.getLocalStatus() != Status.NONE
                        || pRequesterAccountIdsList.contains(conn.getRequesterAccountId()))
            .map(ApiStreamsVPCPeeringConnectionView::new)
            .toList();

    // filter for localStatus == NONE because that means it has not been approved by the user yet
    int pendingCount =
        _streamsSvc.getVPCPeeringConnections(pGroup.getId()).stream()
            .filter(
                conn ->
                    Objects.equals(conn.getCloudStatus(), "pending-acceptance")
                        && Objects.equals(conn.getLocalStatus(), Status.NONE))
            .toList()
            .size();

    ApiStreamsConnectionsAndPendingCountView response =
        new ApiStreamsConnectionsAndPendingCountView(peeringConnections, pendingCount);
    return Response.ok(response).build();
  }

  @POST
  @Path("/vpcPeeringConnections/{id}:accept")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.acceptVPCPeeringConnection.POST")
  public Response acceptVPCPeeringConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("id") final String pVPCConnectionId,
      final ApiStreamsVPCPeeringChallengeView pChallengeView) {

    _streamsSvc.requestAcceptVPCPeeringConnection(
        pGroup.getId(),
        pVPCConnectionId,
        pChallengeView.getRequesterVpcId(),
        pChallengeView.getRequesterAccountId());

    return Response.accepted().build();
  }

  @POST
  @Path("/vpcPeeringConnections/{id}:reject")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.rejectVPCPeeringConnection.POST")
  public Response rejectVPCPeeringConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("id") final String pVPCConnectionId) {

    _streamsSvc.requestRejectVPCPeeringConnection(pGroup.getId(), pVPCConnectionId);

    return Response.accepted().build();
  }

  @DELETE
  @Path("/vpcPeeringConnections/{id}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.deleteVPCPeeringConnection.DELETE")
  public Response deleteVPCPeeringConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("id") final String pVPCConnectionId) {

    _streamsSvc.requestDeleteVPCPeeringConnection(pGroup.getId(), pVPCConnectionId);

    return Response.accepted().build();
  }

  @GET
  @Path("/accountDetails")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getAccountDetails.GET")
  public Response getAccountDetails(
      @Context final Group pGroup,
      @QueryParam("cloudProvider") final String pCloudProvider,
      @QueryParam("regionName") final String pRegionName)
      throws SvcException {

    if (pCloudProvider == null
        || !List.of("aws", "azure", "gcp").contains(pCloudProvider.toLowerCase())) {
      return Response.status(
              Response.Status.BAD_REQUEST.getStatusCode(),
              "cloudProvider must be one of \"aws\", \"azure\", \"gcp\"")
          .build();
    }

    try {
      if (pCloudProvider.equalsIgnoreCase("aws")) {

        Optional<AWSRegionName> regionNameOpt = AWSRegionName.findByNameOrValue(pRegionName);
        if (regionNameOpt.isEmpty()) {
          return Response.status(
                  Response.Status.BAD_REQUEST.getStatusCode(), "Region name cannot be empty")
              .build();
        }

        AWSCloudProviderContainer container =
            _streamsSvc
                .getAwsCloudProviderContainer(pGroup.getId(), regionNameOpt.get().name())
                .orElseThrow();

        String nativeAwsAccountId = _streamsSvc.getNativeAwsAccountId(container);

        ApiStreamAWSAccountDetailsView details =
            new ApiStreamAWSAccountDetailsView(
                nativeAwsAccountId, container.getVpcId().orElseThrow(), container.getAtlasCidr());

        return Response.ok(details).build();
      } else if (pCloudProvider.equalsIgnoreCase("azure")) {

        Optional<AzureRegionName> regionNameOpt = AzureRegionName.findByNameOrValue(pRegionName);
        if (regionNameOpt.isEmpty()) {
          return Response.status(
                  Response.Status.BAD_REQUEST.getStatusCode(), "Region name cannot be empty")
              .build();
        }

        AzureCloudProviderContainer container =
            _streamsSvc
                .getAzureCloudProviderContainer(pGroup.getId(), regionNameOpt.get().name())
                .orElseThrow();

        String subscriptionId = _streamsSvc.getAzureSubscriptionId(container);

        ApiStreamAzureAccountDetailsView details =
            new ApiStreamAzureAccountDetailsView(
                subscriptionId,
                container.getVirtualNetworkName().orElseThrow(),
                container.getAtlasCidr());

        return Response.ok(details).build();
      }

      return Response.ok(
              String.format(
                  "{\"message\": \"Cloud provider: %s, not implemented.\"}", pCloudProvider))
          .build();
    } catch (SvcException e) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), e.getMessage())
          .build();
    }
  }

  @GET
  @Path("/metrics/query")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getMetricsInstantQuery.GET")
  public Response getMetricsInstantQuery(
      @Context final Group pGroup,
      @QueryParam("metricName") final String pMetricName,
      @QueryParam("processorIds[]") final List<String> pProcessorIds,
      @QueryParam("function") final String pFunction,
      @QueryParam("lookback") final String pLookback,
      @QueryParam("time") final String pTime) {
    try {
      String groupId = pGroup.getId().toHexString();
      PromQLResponseView.Data result =
          _streamsSvc.getMetricsInstantQuery(
              groupId, pMetricName, pProcessorIds, pFunction, pLookback, pTime);
      return Response.ok(result).build();
    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.INVALID_ARGUMENT) {
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode(), e.getMessage()).build();
      }
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), e.getMessage())
          .build();
    }
  }

  @GET
  @Path("/metrics/query_range")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_READ_ONLY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.getMetricsRangeQuery.GET")
  public Response getMetricsRangeQuery(
      @Context final Group pGroup,
      @QueryParam("metricName") final String pMetricName,
      @QueryParam("processorIds[]") final List<String> pProcessorIds,
      @QueryParam("function") final String pFunction,
      @QueryParam("lookback") final String pLookback,
      @QueryParam("start") final String pStart,
      @QueryParam("end") final String pEnd,
      @QueryParam("step") final String pStep) {
    try {
      String groupId = pGroup.getId().toHexString();
      PromQLResponseView.Data result =
          _streamsSvc.getMetricsRangeQuery(
              groupId, pMetricName, pProcessorIds, pFunction, pLookback, pStart, pEnd, pStep);
      return Response.ok(result).build();
    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.INVALID_ARGUMENT) {
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode(), e.getMessage()).build();
      }
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), e.getMessage())
          .build();
    }
  }

  @GET
  @Path("/tenants/{tenantName}/metrics/export/csv")
  @Produces("text/csv")
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.downloadMetricsCSV.GET")
  public Response downloadMetricsCSV(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @QueryParam("processorIds[]") final List<String> pProcessorIds,
      @QueryParam("function") final String pFunction,
      @QueryParam("lookback") final String pLookback,
      @QueryParam("start") final String pStart,
      @QueryParam("end") final String pEnd,
      @QueryParam("step") final String pStep,
      @QueryParam("timeZoneId") final String pTimeZoneId) {

    try {
      List<StreamProcessorView> streamProcessors =
          _streamsSvc.getStreamTenantProcessors(pGroup.getId(), pTenantName, false, false, false);

      List<String> processorNames = new ArrayList<>();
      for (StreamProcessorView spv : streamProcessors) {
        if (pProcessorIds.contains(spv.getId().toHexString())) {
          processorNames.add(spv.getName());
        }
      }

      String groupId = pGroup.getId().toHexString();
      String filename =
          String.join(
              "_", pTenantName, String.join("_", processorNames), pStart, pEnd, "Metrics.csv");

      PromQLResponseView.Data sourceResult =
          _streamsSvc.getMetricsRangeQuery(
              groupId,
              OPERATOR_TARGET_INPUT_MESSAGE_COUNT.getMetricName(),
              pProcessorIds,
              pFunction,
              pLookback,
              pStart,
              pEnd,
              pStep);

      PromQLResponseView.Data sinkResult =
          _streamsSvc.getMetricsRangeQuery(
              groupId,
              OPERATOR_TARGET_OUTPUT_MESSAGE_COUNT.getMetricName(),
              pProcessorIds,
              pFunction,
              pLookback,
              pStart,
              pEnd,
              pStep);

      return Response.ok(
              (StreamingOutput)
                  output -> {
                    try {
                      _streamsMetricsExportCSVSvc.writeMetricsToCSV(
                          sourceResult, sinkResult, pTimeZoneId, pStep, output);
                    } catch (final IOException e) {
                      LOG.error("Exception exporting streams metrics csv", e);
                    }
                  })
          .header("Content-Disposition", "attachment; filename=\"" + filename + "\"")
          .build();

    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.INVALID_ARGUMENT) {
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode(), e.getMessage()).build();
      }
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), e.getMessage())
          .build();
    }
  }

  @GET
  @Path("/tenants/{tenantName}/metrics/export/pdf")
  @Produces("application/pdf")
  @UiCall(roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.downloadMetricsPDF.GET")
  public Response downloadMetricsPDF(
      @Context final Group pGroup,
      @PathParam("tenantName") final String pTenantName,
      @QueryParam("processorIds[]") final List<String> pProcessorIds,
      @QueryParam("function") final String pFunction,
      @QueryParam("lookback") final String pLookback,
      @QueryParam("start") final String pStart,
      @QueryParam("end") final String pEnd,
      @QueryParam("step") final String pStep,
      @QueryParam("timeZoneId") final String pTimeZoneId) {

    try {
      List<StreamProcessorView> streamProcessors =
          _streamsSvc.getStreamTenantProcessors(pGroup.getId(), pTenantName, false, false, false);

      List<String> processorNames = new ArrayList<>();
      for (StreamProcessorView spv : streamProcessors) {
        if (pProcessorIds.contains(spv.getId().toHexString())) {
          processorNames.add(spv.getName());
        }
      }

      String groupId = pGroup.getId().toHexString();
      String filename =
          String.join(
              "_", pTenantName, String.join("_", processorNames), pStart, pEnd, "Metrics.pdf");

      PromQLResponseView.Data sourceResult =
          _streamsSvc.getMetricsRangeQuery(
              groupId,
              OPERATOR_TARGET_INPUT_MESSAGE_COUNT.getMetricName(),
              pProcessorIds,
              pFunction,
              pLookback,
              pStart,
              pEnd,
              pStep);

      PromQLResponseView.Data sinkResult =
          _streamsSvc.getMetricsRangeQuery(
              groupId,
              OPERATOR_TARGET_OUTPUT_MESSAGE_COUNT.getMetricName(),
              pProcessorIds,
              pFunction,
              pLookback,
              pStart,
              pEnd,
              pStep);

      final StreamingOutput result =
          out -> {
            try {
              _streamsMetricsExportPDFSvc.writeMetricsToPDF(
                  sourceResult, sinkResult, pTimeZoneId, pStep, out);
            } catch (final IOException e) {
              LOG.error("Exception exporting streams metrics pdf", e);
            }
          };

      return Response.ok(result)
          .header("Content-Disposition", "attachment; filename=\"" + filename + "\"")
          .build();

    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.INVALID_ARGUMENT) {
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode(), e.getMessage()).build();
      }
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), e.getMessage())
          .build();
    }
  }

  @POST
  @Path("/tenants/{tenantName}/connections/{connectionName}/test")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {GROUP_STREAM_PROCESSING_OWNER, ORG_STREAM_PROCESSING_ADMIN, GROUP_DATA_ACCESS_ANY})
  @Auth(endpointAction = "epa.project.NDSStreamsResource.testStreamConnection.POST")
  public Response testStreamConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @PathParam("tenantName") final String pTenantName,
      @PathParam("connectionName") final String pConnectionName,
      final StreamsConnParamView pConnParams)
      throws Exception {

    // Verify connection exists
    StreamsConnectionView connection =
        _streamsSvc.findConnection(pGroup, pTenantName, pConnectionName, pAppUser);

    // Validate that connection type matches the parameters
    if (pConnParams.connectionType() != connection.getType()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "Connection parameter type %s does not match connection type %s",
              pConnParams.connectionType(), connection.getType()));
    }

    String spName = pConnectionName + "_internal_conn_test";

    // Attempt to find existing stream processor, or create and start if not found
    handleStreamProcessor(
        pGroup, pTenantName, spName, pAuditInfo, pConnectionName, connection, pConnParams);

    // Monitor the processor and return the result
    boolean testSuccess =
        monitorTestProcessor(
            pGroup, pAuditInfo, pTenantName, pConnectionName, spName, connection, pConnParams);
    if (testSuccess) {
      return Response.ok(new JSONObject().put("message", "Connection Successful").toMap()).build();
    } else {
      throw new SvcException(NDSErrorCode.STREAM_CONNECTION_TEST_FAILED, "Connection test failed");
    }
  }

  private void handleStreamProcessor(
      Group pGroup,
      String pTenantName,
      String spName,
      AuditInfo pAuditInfo,
      String pConnectionName,
      StreamsConnectionView connection,
      StreamsConnParamView pConnParams)
      throws SvcException {
    try {
      _streamsSvc.getStreamProcessor(pGroup.getId(), pTenantName, spName);
    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.STREAM_PROCESSOR_NOT_FOUND
          || e.getErrorCode() == NDSErrorCode.STREAM_PROCESSOR_GENERIC_ERROR) {
        createAndStartTestProcessor(
            pGroup, pAuditInfo, pTenantName, pConnectionName, spName, connection, pConnParams);
      } else {
        throw e;
      }
    }
  }

  private void createAndStartTestProcessor(
      Group pGroup,
      AuditInfo pAuditInfo,
      String pTenantName,
      String pConnectionName,
      String spName,
      StreamsConnectionView connection,
      StreamsConnParamView pConnParams)
      throws SvcException {

    Document sourceDoc = new Document("connectionName", pConnectionName);

    // Add fields based on connection type
    if (pConnParams instanceof ClusterConnParamView clusterParams) {
      // Atlas DB connection - add db and coll fields
      sourceDoc.append("db", clusterParams.db());
      sourceDoc.append("coll", clusterParams.coll());
    } else if (pConnParams instanceof KafkaConnParamView kafkaParams) {
      // Kafka connection - add topic field
      sourceDoc.append("topic", kafkaParams.topic());
    }

    List<Document> pipeline =
        List.of(
            new Document("$source", sourceDoc),
            new Document("$emit", new Document("connectionName", "__testLog")));
    _streamsSvc.createStreamProcessor(
        pAuditInfo, pGroup.getId(), pTenantName, spName, pipeline, null);
    _streamsSvc.startStreamProcessorWithDataFlowOptions(
        pGroup, pTenantName, spName, pAuditInfo.getUsername(), null, false);
  }

  private boolean monitorTestProcessor(
      Group pGroup,
      AuditInfo pAuditInfo,
      String pTenantName,
      String pConnectionName,
      String spName,
      StreamsConnectionView connection,
      StreamsConnParamView pConnParams)
      throws SvcException {

    int maxRetries = 30; // 30 seconds timeout
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        StreamProcessorGetView sp =
            _streamsSvc.getStreamProcessor(pGroup.getId(), pTenantName, spName);
        String state = sp.state();
        switch (state) {
          case "STARTED", "STOPPED":
            _streamsSvc.dropStreamProcessor(pGroup, pTenantName, spName);
            return true;
          case "FAILED":
            _streamsSvc.dropStreamProcessor(pGroup, pTenantName, spName);
            return false;
          case "DROPPED":
            createAndStartTestProcessor(
                pGroup, pAuditInfo, pTenantName, pConnectionName, spName, connection, pConnParams);
            break;
          default:
            break;
        }
      } catch (SvcException e) {
        if (e.getErrorCode() == NDSErrorCode.STREAM_PROCESSOR_NOT_FOUND
            || e.getErrorCode() == NDSErrorCode.STREAM_PROCESSOR_GENERIC_ERROR) {
          createAndStartTestProcessor(
              pGroup, pAuditInfo, pTenantName, pConnectionName, spName, connection, pConnParams);
        } else {
          _streamsSvc.dropStreamProcessor(pGroup, pTenantName, spName);
          throw e;
        }
      }
      retryCount++;
      sleepWithInterruptHandling();
    }

    _streamsSvc.dropStreamProcessor(pGroup, pTenantName, spName);
    throw new SvcException(
        NDSErrorCode.STREAM_PROCESSOR_TIMEOUT_ERROR,
        "Timeout waiting for stream processor test to complete");
  }

  private void sleepWithInterruptHandling() throws SvcException {
    try {
      Thread.sleep(1000);
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      throw new SvcException(NDSErrorCode.STREAM_PROCESSOR_TIMEOUT_ERROR, "Monitoring interrupted");
    }
  }
}
