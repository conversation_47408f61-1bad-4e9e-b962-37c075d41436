package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._public.ui.PushBasedLogExportProjectView;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport.FieldDefs;
import com.xgen.svc.nds.svc.PushBasedLogExportSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;

@Path("/nds/{groupId}/pushBasedLogExport")
@Singleton
public class NDSPushBasedLogExportResource extends NDSBaseResource {
  private final PushBasedLogExportSvc _pushBasedLogExportSvc;

  @Inject
  public NDSPushBasedLogExportResource(final PushBasedLogExportSvc pPushBasedLogExportSvc) {
    _pushBasedLogExportSvc = pPushBasedLogExportSvc;
  }

  @GET
  @Feature(FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = "epa.project.NDSPushBasedLogExportResource.getPushBasedLogExport.GET")
  public Response getPushBasedLogExport(@PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final PushBasedLogExport config = _pushBasedLogExportSvc.getPushBasedLogConfiguration(pGroupId);
    return Response.ok(new PushBasedLogExportProjectView(config)).build();
  }

  @DELETE
  @Feature(FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.project.NDSPushBasedLogExportResource.deletePushBasedLogExport.DELETE")
  public Response deletePushBasedLogExport(
      @Context final AuditInfo pAuditInfo, @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    _pushBasedLogExportSvc.deletePushBasedLogConfiguration(pAuditInfo, pGroupId);
    return Response.ok().build();
  }

  @POST
  @Feature(FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT)
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.project.NDSPushBasedLogExportResource.createPushBasedLogExport.POST")
  public Response createPushBasedLogExport(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      final PushBasedLogExportProjectView pPushBasedLogExportProjectView)
      throws SvcException {
    validateCreateProjectConfigRequest(pPushBasedLogExportProjectView);
    _pushBasedLogExportSvc.createPushBasedLogConfiguration(
        pAuditInfo,
        pGroupId,
        pPushBasedLogExportProjectView.getBucketName(),
        pPushBasedLogExportProjectView.getPrefixPath(),
        pPushBasedLogExportProjectView.getIamRoleId());
    return Response.ok().build();
  }

  @POST
  @Path("/validate")
  @Feature(FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT)
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.project.NDSPushBasedLogExportResource.validateBucketAndIamRole.POST")
  public Response validateBucketAndIamRole(
      @PathParam("groupId") final ObjectId pGroupId,
      final PushBasedLogExportProjectView pPushBasedLogExportProjectView)
      throws SvcException {
    validateCreateProjectConfigRequest(pPushBasedLogExportProjectView);
    _pushBasedLogExportSvc.validateBucketAndIamRole(
        pGroupId,
        pPushBasedLogExportProjectView.getBucketName(),
        pPushBasedLogExportProjectView.getPrefixPath(),
        pPushBasedLogExportProjectView.getIamRoleId());
    return Response.ok().build();
  }

  @PATCH
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.ATLAS_PUSH_BASED_LOG_EXPORT)
  @UiCall(roles = RoleSet.GROUP_ATLAS_ADMIN)
  @Auth(endpointAction = "epa.project.NDSPushBasedLogExportResource.updatePushBasedLogExport.PATCH")
  public Response updatePushBasedLogExport(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      final PushBasedLogExportProjectView pPushBasedLogExportProjectView)
      throws SvcException {
    _pushBasedLogExportSvc.updatePushBasedLogConfiguration(
        pAuditInfo,
        pGroupId,
        pPushBasedLogExportProjectView.getBucketName(),
        pPushBasedLogExportProjectView.getPrefixPath(),
        pPushBasedLogExportProjectView.getIamRoleId());
    return Response.ok().build();
  }

  protected void validateCreateProjectConfigRequest(
      final PushBasedLogExportProjectView pPushBasedLogExportProjectView) throws SvcException {
    if (pPushBasedLogExportProjectView == null) {
      throw new SvcException(
          NDSErrorCode.MISSING_ATTRIBUTE,
          String.format(
              "%s, %s, and %s",
              FieldDefs.BUCKET_NAME, FieldDefs.PREFIX_PATH, FieldDefs.IAM_ROLE_ID));
    } else if (pPushBasedLogExportProjectView.getBucketName() == null) {
      throw new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, FieldDefs.BUCKET_NAME);
    } else if (pPushBasedLogExportProjectView.getPrefixPath() == null) {
      throw new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, FieldDefs.PREFIX_PATH);
    } else if (pPushBasedLogExportProjectView.getIamRoleId() == null) {
      throw new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, FieldDefs.IAM_ROLE_ID);
    }
  }
}
