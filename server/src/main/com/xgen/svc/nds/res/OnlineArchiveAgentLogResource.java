package com.xgen.svc.nds.res;

import static com.xgen.cloud.access.role._public.model.RoleSet.GLOBAL_MONITORING_ADMIN;
import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_EXPLICIT_ACCESS;
import static com.xgen.cloud.access.role._public.model.RoleSet.ORG_ANY_ADMIN;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.brs.core._public.svc.AgentLogSvc;
import com.xgen.cloud.common.access._public.annotation.AgentReadPolicy;
import com.xgen.cloud.common.access._public.annotation.ApiCall;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveAgentLogFilter;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.ApiVersion;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.nds.model.ui.OnlineArchiveAgentLogView;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveAgentLogSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/agents/onlinearchive/logs")
@Singleton
public class OnlineArchiveAgentLogResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(OnlineArchiveAgentLogResource.class);

  private final OnlineArchiveAgentLogSvc _agentLogSvc;

  @Inject
  public OnlineArchiveAgentLogResource(final OnlineArchiveAgentLogSvc pAgentLogSvc) {
    _agentLogSvc = pAgentLogSvc;
  }

  @POST
  @Path("/{groupId}/batch")
  @ApiCall(
      auth = true,
      version = ApiVersion.V1,
      groupReadPolicy = AgentReadPolicy.NONE,
      organizationReadPolicy = AgentReadPolicy.NONE)
  public Response saveAgentLogs(
      @PathParam("groupId") String pGroupId,
      @Context HttpServletRequest pRequest,
      @QueryParam("ah") String pAgentHostnameParam,
      @QueryParam("sk") String pSessionKeyParam,
      final List<OnlineArchiveAgentLogView> pAgentLogViews) {
    final String agentHostname = StringUtils.trimToNull(pAgentHostnameParam);
    final String sessionKey = StringUtils.trimToNull(pSessionKeyParam);

    _agentLogSvc.saveAgentLogs(pAgentLogViews, new ObjectId(pGroupId), agentHostname, sessionKey);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {GROUP_EXPLICIT_ACCESS, ORG_ANY_ADMIN, GLOBAL_MONITORING_ADMIN})
  @AccessAudit(auditEventType = AccessEvent.Type.ONLINE_ARCHIVE_AGENT_LOGS)
  @Auth(endpointAction = "epa.project.OnlineArchiveAgentLogResource.getAgentLogs.GET")
  public Response getAgentLogs(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @QueryParam("page") final Integer pPageNum,
      @QueryParam("sort") @DefaultValue("timestamp") final String pSortKey,
      @QueryParam("order") @DefaultValue("desc") final String pSortOrder,
      @QueryParam("startTimestamp") final Long pStartTimestamp)
      throws Exception {
    final int skip = getPaginationSkip(pPageNum);
    final int limit = getPaginationLimit(pPageNum);
    final boolean sortDescending = (pSortOrder.equals("desc"));
    final Date startTimestamp = (pStartTimestamp == null) ? new Date() : new Date(pStartTimestamp);

    final Map<String, String[]> parameterMap = pRequest.getParameterMap();
    final OnlineArchiveAgentLogFilter logFilter = new OnlineArchiveAgentLogFilter(parameterMap);

    final JSONObject agentLogs =
        _agentLogSvc.getLogsObject(
            pGroup.getId(), startTimestamp, skip, limit, pSortKey, sortDescending, logFilter);
    return Response.ok(agentLogs.toString(4)).build();
  }

  @GET
  @Path("/csv/{groupId}")
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  @UiCall(roles = {GROUP_EXPLICIT_ACCESS, ORG_ANY_ADMIN, GLOBAL_MONITORING_ADMIN})
  @AccessAudit(auditEventType = AccessEvent.Type.ONLINE_ARCHIVE_AGENT_LOGS_CSV_DOWNLOAD)
  @Auth(endpointAction = "epa.project.OnlineArchiveAgentLogResource.downloadAgentLogs.GET")
  public Response downloadAgentLogs(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @QueryParam("startTimestamp") final Long pStartTimestamp)
      throws Exception {
    final RequestParams params = getRequestParams(pRequest);
    final Date startTimestamp = (pStartTimestamp == null) ? new Date() : new Date(pStartTimestamp);
    final AppUser user = params.getAppUser();

    final Map<String, String[]> parameterMap = pRequest.getParameterMap();
    final OnlineArchiveAgentLogFilter logFilter = new OnlineArchiveAgentLogFilter(parameterMap);

    final List<BasicDBObject> logs =
        _agentLogSvc.getLogs(
            pGroup.getId(),
            startTimestamp,
            0,
            AgentLogSvc.DEFAULT_CSV_SIZE,
            AgentLogSvc.DEFAULT_SORT_FIELD,
            true,
            logFilter);

    return Response.ok(
            new StreamingOutput() {
              @Override
              public void write(OutputStream output) {
                try {
                  _agentLogSvc.writeAgentLogsToCsv(logs, output, user.getDateFormatPatternExcel());
                } catch (Throwable t) {
                  LOG.error(t.getMessage(), t);
                }
              }
            })
        .header("Content-Disposition", "attachment; filename=\"Online_Archive_Agent_Logs.csv\"")
        .build();
  }
}
