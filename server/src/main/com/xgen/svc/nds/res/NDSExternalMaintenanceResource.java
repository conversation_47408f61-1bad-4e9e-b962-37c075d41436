package com.xgen.svc.nds.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.exmaintenance._public.model.NDSExternalMaintenanceTask;
import com.xgen.cloud.nds.exmaintenance._public.svc.NDSExternalMaintenanceAdminSvc;
import com.xgen.cloud.nds.exmaintenance._public.view.NDSExternalMaintenanceTaskView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.List;
import org.bson.types.ObjectId;

@Path("/admin/nds/externalMaintenance")
@Singleton
public class NDSExternalMaintenanceResource extends BaseResource {

  private final NDSExternalMaintenanceAdminSvc _ndsExternalMaintenanceAdminSvc;
  private final AuditSvc _auditSvc;

  @Inject
  public NDSExternalMaintenanceResource(
      final NDSExternalMaintenanceAdminSvc pNDSExternalMaintenanceAdminSvc,
      final AuditSvc pAuditSvc) {
    _ndsExternalMaintenanceAdminSvc = pNDSExternalMaintenanceAdminSvc;
    _auditSvc = pAuditSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ADMIN_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.getTasks.GET")
  public Response getTasks() {
    final List<NDSExternalMaintenanceTaskView> views =
        _ndsExternalMaintenanceAdminSvc.getMaintenanceTasks();

    return Response.ok(views).build();
  }

  @POST
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.createTask.POST")
  public Response createTask(
      final NDSExternalMaintenanceTaskView pTaskView, @Context final AuditInfo pAuditInfo) {
    final NDSExternalMaintenanceTask task =
        pTaskView.toNDSExternalMaintenanceTaskWithUser(pAuditInfo.getUsername());
    try {
      _ndsExternalMaintenanceAdminSvc.createTask(
          task, pTaskView.getMonitoringThresholds().orElse(null), pAuditInfo);
    } catch (final IllegalStateException e) {
      return Response.status(Status.BAD_REQUEST).build();
    }

    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.EXTERNAL_MAINTENANCE_CREATED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(
        List.of(
            new AuditDescription("id", task.getId().toHexString()),
            new AuditDescription("name", task.getMaintenanceName())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.updateTask.PATCH")
  public Response updateTask(
      final NDSExternalMaintenanceTaskView pTaskView, @Context final AuditInfo pAuditInfo) {
    final NDSExternalMaintenanceTask task = pTaskView.toNDSExternalMaintenanceTask();

    try {
      _ndsExternalMaintenanceAdminSvc.updateTask(
          task, pTaskView.isMonitoringEnabled(), pTaskView.getMonitoringThresholds().orElse(null));
    } catch (final IllegalStateException e) {
      return Response.status(Status.BAD_REQUEST).build();
    }

    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.EXTERNAL_MAINTENANCE_UPDATED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(
        List.of(
            new AuditDescription("id", task.getId().toHexString()),
            new AuditDescription("name", task.getMaintenanceName())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/cancel/{taskId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.cancelTask.POST")
  public Response cancelTask(
      @PathParam("taskId") final ObjectId pTaskId, @Context final AuditInfo pAuditInfo) {
    _ndsExternalMaintenanceAdminSvc.cancelTask(pTaskId);

    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.EXTERNAL_MAINTENANCE_CANCELED);
    builder.auditInfo(pAuditInfo);
    builder.auditDescription(List.of(new AuditDescription("id", pTaskId.toHexString())));
    builder.hidden(true);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.accepted(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/metadata/{maintenanceJavaClassName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.getMaintenanceMetadata.GET")
  public Response getMaintenanceMetadata(
      @PathParam("maintenanceJavaClassName") final String maintenanceJavaClassName)
      throws SvcException {
    return Response.ok(
            _ndsExternalMaintenanceAdminSvc.getMaintenanceMetadata(maintenanceJavaClassName))
        .build();
  }

  @GET
  @Path("/{maintenanceName}/items/{itemId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.getMaintenanceState.GET")
  public Response getMaintenanceState(
      @PathParam("maintenanceName") final String maintenanceName,
      @PathParam("itemId") final ObjectId itemId)
      throws SvcException {
    return Response.ok(_ndsExternalMaintenanceAdminSvc.getMaintenanceState(maintenanceName, itemId))
        .build();
  }

  @GET
  @Path("/incomplete/{maintenanceJavaClassName}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.NDSExternalMaintenanceResource.getIncompleteMaintenanceTaskByJavaClassName.GET")
  public Response getIncompleteMaintenanceTaskByJavaClassName(
      @PathParam("maintenanceJavaClassName") final String pMaintenanceJavaClassName) {
    return Response.ok(
            _ndsExternalMaintenanceAdminSvc.getIncompleteMaintenanceTaskByJavaClassName(
                pMaintenanceJavaClassName))
        .build();
  }
}
