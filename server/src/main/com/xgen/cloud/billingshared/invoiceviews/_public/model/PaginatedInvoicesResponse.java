package com.xgen.cloud.billingshared.invoiceviews._public.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * A record representing a paginated response containing invoices and pagination metadata.
 *
 * @param invoices The list of invoice views for the current page
 * @param totalCount The total number of invoices available
 * @param page The current page number (0-indexed)
 * @param pageSize The maximum number of items per page
 */
public record PaginatedInvoicesResponse(
    @JsonProperty("invoices") List<InvoiceView> invoices,
    @JsonProperty("totalCount") long totalCount,
    @JsonProperty("page") int page,
    @JsonProperty("pageSize") int pageSize) {}
