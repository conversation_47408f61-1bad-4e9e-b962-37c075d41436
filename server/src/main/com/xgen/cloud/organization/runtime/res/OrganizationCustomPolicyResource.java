package com.xgen.cloud.organization.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.authz.core._public.client.AuthorizationClient;
import com.xgen.cloud.authz.core._public.client.PolicyClient;
import com.xgen.cloud.authz.core._public.view.ui.CustomPolicyView;
import com.xgen.cloud.authz.core._public.view.ui.ManagedPolicyView;
import com.xgen.cloud.authz.core._public.wrapper.AuthorizationClientProvider;
import com.xgen.cloud.authz.resource._public.client.ResourceClient;
import com.xgen.cloud.authz.resource._public.wrapper.ResourceClientProvider;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authz._public.model.AuthzRequestInfo;
import com.xgen.cloud.common.authz._public.view.ResourceIdView;
import com.xgen.cloud.common.constants._public.model.actions.ActionConstants;
import com.xgen.cloud.common.constants._public.model.resources.ResourceConstants;
import com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.activity.OrgAudit;
import com.xgen.cloud.organization._public.model.activity.OrgEvent;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.ListUtils;
import org.bson.types.ObjectId;

@Path("/orgs/{orgId}/customPolicy")
@Feature(FeatureFlag.FINE_GRAINED_AUTH)
@Singleton
public class OrganizationCustomPolicyResource extends BaseResource {
  protected static final int CUSTOM_POLICY_STRING_MAX_LENGTH = 250;

  private final AuthorizationClientProvider authorizationClientProvider;
  private final PolicyClient policyClient;
  private final ResourceClientProvider resourceClientProvider;
  private final InvitationSvc invitationSvc;
  private final AuditSvc auditSvc;

  @Inject
  public OrganizationCustomPolicyResource(
      AuthorizationClientProvider authorizationClientProvider,
      PolicyClient policyClient,
      ResourceClientProvider resourceClientProvider,
      InvitationSvc invitationSvc,
      AuditSvc auditSvc) {
    this.authorizationClientProvider = authorizationClientProvider;
    this.policyClient = policyClient;
    this.resourceClientProvider = resourceClientProvider;
    this.invitationSvc = invitationSvc;
    this.auditSvc = auditSvc;
  }

  @POST
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationCustomPolicyResource.createCustomPolicy.POST")
  public Response createCustomPolicy(
      CustomPolicyView customPolicy,
      @Context AppUser appUser,
      @Context Organization organization,
      @Context AuditInfo auditInfo)
      throws SvcException {
    validateCustomPolicy(appUser.getId().toString(), organization.getId(), customPolicy);
    CustomPolicyView response = policyClient.createCustomPolicy(customPolicy.serializeToMessage());
    auditCustomPolicyChange(
        organization.getId(),
        customPolicy.getName(),
        auditInfo,
        OrgEvent.Type.ORG_CUSTOM_POLICY_CREATED);
    return Response.ok().entity(response).build();
  }

  @PUT
  @Path("/{policyId}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationCustomPolicyResource.updateCustomPolicy.PUT")
  public Response updateCustomPolicy(
      CustomPolicyView customPolicy,
      @Context AppUser appUser,
      @Context Organization organization,
      @Context AuditInfo auditInfo)
      throws SvcException {
    validateIdExists(customPolicy);
    validateCustomPolicy(appUser.getId().toString(), organization.getId(), customPolicy);
    CustomPolicyView response = policyClient.updateCustomPolicy(customPolicy.serializeToMessage());
    auditCustomPolicyChange(
        organization.getId(),
        customPolicy.getName(),
        auditInfo,
        OrgEvent.Type.ORG_CUSTOM_POLICY_UPDATED);

    return Response.ok().entity(response).build();
  }

  @DELETE
  @Path("/{policyId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationCustomPolicyResource.deleteCustomPolicy.DELETE")
  public Response deleteCustomPolicy(
      @Context Organization organization,
      @PathParam("policyId") String policyId,
      @Context AuditInfo auditInfo)
      throws SvcException {
    try {
      CustomPolicyView policy = policyClient.getCustomPolicyById(policyId);

      if (!organizationHasResource(organization.getId().toString(), policy.getOwnerResource())) {
        throw new SvcException(CommonErrorCode.FORBIDDEN);
      }

      policyClient.deleteCustomPolicy(policyId);
      invitationSvc.removePolicyAssignmentFromInvitations(policyId, organization.getId());
      auditCustomPolicyChange(
          organization.getId(),
          policy.getName(),
          auditInfo,
          OrgEvent.Type.ORG_CUSTOM_POLICY_DELETED);
    } catch (Exception e) {
      throw new SvcException(CommonErrorCode.FORBIDDEN);
    }

    return Response.ok().build();
  }

  private void auditCustomPolicyChange(
      ObjectId orgId, String policyName, AuditInfo auditInfo, OrgEvent.Type eventType) {
    OrgAudit.Builder builder = new OrgAudit.Builder(eventType);
    builder.customPolicyName(policyName);
    builder.auditInfo(auditInfo);
    builder.orgId(orgId);
    auditSvc.saveAuditEvent(builder.build());
  }

  private void validateIdExists(CustomPolicyView customPolicy) throws SvcException {
    if (customPolicy.getId() == null || customPolicy.getId().trim().isEmpty()) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          String.format("Missing id in custom policy payload %s", customPolicy));
    }
  }

  private boolean isUserAuthorizedToCreateCustomPolicyWithResource(
      String actorId, String action, ResourceIdView resourceIdView) {
    return getAuthorizationClient()
        .isAuthorized(new AuthzRequestInfo(actorId, action, resourceIdView));
  }

  private void validateCustomPolicy(String actorId, ObjectId orgId, CustomPolicyView customPolicy)
      throws SvcException {
    validateCustomPolicyOwnerResourceNotGlobal(customPolicy.getOwnerResource());
    validatePolicyNameAndDescription(orgId, customPolicy);
    if (!isUserAuthorizedToCreateCustomPolicyWithResource(
        actorId, ActionConstants.ORG_USER_ADMIN.getAction(), customPolicy.getOwnerResource())) {
      throw new SvcException(CommonErrorCode.FORBIDDEN);
    }
  }

  private void validateCustomPolicyOwnerResourceNotGlobal(@Nullable ResourceIdView ownerResource)
      throws SvcException {
    if (ownerResource == null) {
      return;
    }
    if (ownerResource.getType().equals(ResourceTypeConstants.GLOBAL.getType())) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }
  }

  private void validatePolicyNameAndDescription(ObjectId orgId, CustomPolicyView customPolicy)
      throws SvcException {
    if (customPolicy.getName() == null
        || customPolicy.getName().trim().isEmpty()
        || customPolicy.getName().trim().length() > CUSTOM_POLICY_STRING_MAX_LENGTH
        || Optional.ofNullable(customPolicy.getDescription()).stream()
            .anyMatch(desc -> desc.trim().length() > CUSTOM_POLICY_STRING_MAX_LENGTH)) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }

    List<CustomPolicyView> customPolicies = policyClient.getCustomPolicies(orgId);
    List<ManagedPolicyView> managedPolicies = policyClient.getManagedPolicies();
    Set<String> otherPolicyNames =
        ListUtils.union(customPolicies, managedPolicies).stream()
            .filter(policy -> !policy.getId().equals(customPolicy.getId()))
            .map(policy -> trimAndLowerCase(policy.getName()))
            .collect(Collectors.toSet());

    if (otherPolicyNames.contains(trimAndLowerCase(customPolicy.getName()))) {
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER);
    }
  }

  private String trimAndLowerCase(String string) {
    return string.trim().toLowerCase();
  }

  private boolean organizationHasResource(String orgId, ResourceIdView resourceIdView)
      throws AuthzServiceClientException {
    if (orgId == null || resourceIdView == null) {
      return false;
    }

    ResourceIdView resourceIdViewToMatch =
        new ResourceIdView(
            ResourceConstants.CLOUD_SERVICE.getName(),
            ResourceTypeConstants.ORGANIZATION.getType(),
            orgId);

    if (resourceIdViewToMatch.equals(resourceIdView)) {
      return true;
    }

    // If the resource is a different organization, we can immediately return false
    if (ResourceConstants.CLOUD_SERVICE.getName().equals(resourceIdView.getService())
        && ResourceTypeConstants.ORGANIZATION.getType().equals(resourceIdView.getType())
        && !orgId.equals(resourceIdView.getId())) {
      return false;
    }

    List<ResourceIdView> ancestorResourceIdViews =
        getResourceClient().getAncestorResourceIds(resourceIdView);

    return ancestorResourceIdViews.stream().anyMatch(resourceIdViewToMatch::equals);
  }

  private AuthorizationClient getAuthorizationClient() {
    return authorizationClientProvider.get();
  }

  private ResourceClient getResourceClient() {
    return resourceClientProvider.get();
  }
}
