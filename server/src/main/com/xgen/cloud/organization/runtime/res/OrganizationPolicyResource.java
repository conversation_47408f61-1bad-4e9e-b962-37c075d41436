package com.xgen.cloud.organization.runtime.res;

import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toSet;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access_manager._public.view.ui.ActorsAndInvitationsView;
import com.xgen.cloud.access_manager._public.view.ui.InvitationView;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.authz.core._public.client.ActorClient;
import com.xgen.cloud.authz.core._public.client.PermissionClient;
import com.xgen.cloud.authz.core._public.client.PolicyClient;
import com.xgen.cloud.authz.core._public.utils.ConversionUtils;
import com.xgen.cloud.authz.core._public.view.ui.ActorView;
import com.xgen.cloud.authz.core._public.view.ui.CustomPolicyView;
import com.xgen.cloud.authz.core._public.view.ui.ManagedPolicyView;
import com.xgen.cloud.authz.core._public.view.ui.PermissionOptionView;
import com.xgen.cloud.authz.core._public.view.ui.PolicyView;
import com.xgen.cloud.authz.core._public.view.ui.ResourceTypeIdView;
import com.xgen.cloud.authz.resource._public.client.ResourceClient;
import com.xgen.cloud.authz.resource._public.wrapper.ResourceClientProvider;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authz._public.model.ResourceTypeId;
import com.xgen.cloud.common.authz._public.view.PolicyAssignmentView;
import com.xgen.cloud.common.authz._public.view.ResourceIdView;
import com.xgen.cloud.common.constants._public.model.actions.UsageContextConstants;
import com.xgen.cloud.common.constants._public.model.resources.ResourceConstants;
import com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.model.ResourceId;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.organization._private.view.PolicyAssignmentForm;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.organization._public.view.PolicyType;
import com.xgen.cloud.services.authzv2.proto.PermissionOptionMessage;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.activity.UserAudit;
import com.xgen.cloud.user._public.model.activity.UserEvent;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import net.logstash.logback.argument.StructuredArguments;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.EnumUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("NullAway") // suppressed to enable nullaway to address another issue
@Path("/orgs/{orgId}/policies")
@Feature(FeatureFlag.FINE_GRAINED_AUTH)
@Singleton
public class OrganizationPolicyResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(OrganizationPolicyResource.class);
  private final ActorClient actorClient;
  private final PermissionClient permissionClient;
  private final PolicyClient policyClient;
  private final ResourceClientProvider resourceClientProvider;
  private final GroupSvc groupSvc;
  private final OrganizationSvc organizationSvc;
  private final UserSvc userSvc;
  private final NDSLookupSvc ndsLookupSvc;
  private final InvitationSvc invitationSvc;
  private final AuditSvc auditSvc;

  @Inject
  public OrganizationPolicyResource(
      ActorClient actorClient,
      PermissionClient permissionClient,
      PolicyClient policyClient,
      ResourceClientProvider resourceClientProvider,
      GroupSvc groupSvc,
      OrganizationSvc organizationSvc,
      UserSvc userSvc,
      NDSLookupSvc ndsLookupSvc,
      InvitationSvc invitationSvc,
      AuditSvc auditSvc) {
    this.actorClient = actorClient;
    this.permissionClient = permissionClient;
    this.policyClient = policyClient;
    this.groupSvc = groupSvc;
    this.organizationSvc = organizationSvc;
    this.userSvc = userSvc;
    this.resourceClientProvider = resourceClientProvider;
    this.ndsLookupSvc = ndsLookupSvc;
    this.invitationSvc = invitationSvc;
    this.auditSvc = auditSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationPolicyResource.getOrgPolicies.GET")
  public Response getOrgPolicies(@Context Organization organization)
      throws AuthzServiceClientException {
    List<CustomPolicyView> customPolicies = policyClient.getCustomPolicies(organization.getId());
    List<ManagedPolicyView> managedPolicies = policyClient.getManagedPolicies();
    // Only return Atlas managed policies for now because FGA is not yet available for CM
    List<ManagedPolicyView> assignableManagedPolicies =
        managedPolicies.stream()
            .filter(policy -> policy.getUsageContexts().contains(UsageContextConstants.ATLAS))
            .toList();
    List<Object> policies = ListUtils.union(customPolicies, assignableManagedPolicies);
    return Response.ok().entity(policies).build();
  }

  @GET
  @Path("/{policyType}/{policyId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationPolicyResource.getOrgPolicyByTypeAndId.GET")
  public Response getOrgPolicyByTypeAndId(
      @PathParam("policyType") PolicyType policyType, @PathParam("policyId") String policyId)
      throws AuthzServiceClientException {
    if (policyType == PolicyType.CUSTOM) {
      return Response.ok().entity(policyClient.getCustomPolicyById(policyId)).build();
    } else {
      return Response.ok().entity(policyClient.getManagedPolicyById(policyId)).build();
    }
  }

  @GET
  @Path("/assignments")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationPolicyResource.getOrgPolicyAssignments.GET")
  public Response getOrgPolicyAssignments(@Context Organization organization)
      throws AuthzServiceClientException {
    try {
      List<Exception> exceptions = new ArrayList<>();
      List<AppUser> users = userSvc.findAllLocalUsersByOrgId(organization.getId());
      List<AppUser> apiUsers = userSvc.findApiUsersByOrgId(organization.getId());
      List<ActorView> usersAndApiUsers =
          ListUtils.union(users, apiUsers).stream()
              .map(
                  user -> {
                    try {
                      return actorClient.getActor(user.getId().toString());
                    } catch (Exception e) {
                      exceptions.add(e);
                      return null;
                    }
                  })
              .filter(Objects::nonNull)
              .map(this::removeUsersGlobalAssignments)
              .map(
                  actor -> {
                    try {
                      return filterAssignmentsNotBelongingToOrg(actor, organization.getId());
                    } catch (AuthzServiceClientException e) {
                      throw new RuntimeException(e);
                    }
                  })
              .toList();

      List<Invitation> invitations =
          invitationSvc.findByOrgAndHasPolicyAssignment(organization.getId());
      List<InvitationView> invitationViews =
          invitations.stream()
              .map(
                  invite ->
                      new InvitationView(
                          invite.getUsername(),
                          invite.getPolicyAssignments().stream()
                              .map(
                                  policyAssignment -> {
                                    ResourceId scope = policyAssignment.getScope();
                                    return new PolicyAssignmentView(
                                        policyAssignment.getPolicyId(),
                                        new ResourceIdView(
                                            scope.getService(),
                                            scope.getType(),
                                            scope.getId().toString()));
                                  })
                              .toList()))
              .toList();

      // Get all policy assignments from all users, apiUsers, and invitations
      List<PolicyAssignmentView> policyAssignmentViews =
          ListUtils.union(
              usersAndApiUsers.stream()
                  .flatMap(user -> user.getPolicyAssignments().stream())
                  .toList(),
              invitationViews.stream()
                  .flatMap(invitationView -> invitationView.getPolicyAssignments().stream())
                  .toList());

      // Map resource type to resource ids
      // TODO: CLOUDP-238160 To also leverage the Assignment.scope.service when constructing
      // resource
      // map below.
      Map<String, Set<ObjectId>> assignmentsResourceMap = new HashMap<>();
      for (PolicyAssignmentView policyAssignmentView : policyAssignmentViews) {
        String resourceType = policyAssignmentView.getScope().getType();
        String resourceId = policyAssignmentView.getScope().getId();
        assignmentsResourceMap.putIfAbsent(resourceType, new HashSet<>());
        assignmentsResourceMap.get(resourceType).add(new ObjectId(resourceId));
      }

      // Organization, Project, and Cluster information
      List<Organization> orgs =
          assignmentsResourceMap.containsKey(ResourceTypeConstants.ORGANIZATION.getType())
              ? organizationSvc.findOrgsByOrgIds(
                  assignmentsResourceMap.get(ResourceTypeConstants.ORGANIZATION.getType()))
              : new ArrayList<>();
      Map<String, Organization> orgsMap =
          orgs.stream().collect(Collectors.toMap(org -> org.getId().toString(), org -> org));
      List<Group> projects =
          assignmentsResourceMap.containsKey(ResourceTypeConstants.PROJECT.getType())
              ? groupSvc.findByIds(
                  assignmentsResourceMap.get(ResourceTypeConstants.PROJECT.getType()))
              : new ArrayList<>();
      Map<String, Group> projectsMap =
          projects.stream()
              .collect(Collectors.toMap(project -> project.getId().toString(), project -> project));
      Set<ObjectId> clusterIds =
          assignmentsResourceMap.getOrDefault(
              ResourceTypeConstants.CLUSTER.getType(), Collections.emptySet());
      Map<String, String> clusterIdToClusterNameMap = getProjectAndClusterNames(clusterIds);

      Map<String, String> policyIdToNameMap =
          getOrgAllNonGlobalPolicies(organization.getId()).stream()
              .collect(Collectors.toMap(PolicyView::getId, PolicyView::getName));

      // Augment resource data
      for (ActorView actorView : usersAndApiUsers) {
        augmentPolicyAssignmentsWithResourceData(
            actorView.getPolicyAssignments(),
            actorView.getId(),
            false,
            orgsMap,
            projectsMap,
            clusterIdToClusterNameMap,
            policyIdToNameMap);
      }

      for (InvitationView invitationView : invitationViews) {
        augmentPolicyAssignmentsWithResourceData(
            invitationView.getPolicyAssignments(),
            invitationView.getUsername(),
            true,
            orgsMap,
            projectsMap,
            clusterIdToClusterNameMap,
            policyIdToNameMap);
      }

      if (!exceptions.isEmpty()) {
        LOG.warn(
            "Error(s) occurred when fetching actors for organization {}, {}",
            StructuredArguments.entries(Map.of("orgId", organization.getId())),
            exceptions);
      }
      return Response.ok()
          .entity(new ActorsAndInvitationsView(usersAndApiUsers, invitationViews))
          .build();
    } catch (RuntimeException e) {
      // Rethrow unchecked exception from lambda
      if (e.getCause() instanceof AuthzServiceClientException) {
        throw (AuthzServiceClientException) e.getCause();
      }
      throw e;
    }
  }

  private void augmentPolicyAssignmentsWithResourceData(
      List<PolicyAssignmentView> policyAssignments,
      String id,
      boolean isInvite,
      Map<String, Organization> orgsMap,
      Map<String, Group> projectsMap,
      Map<String, String> clusterIdToClusterNameMap,
      Map<String, String> policyIdToNameMap) {
    for (PolicyAssignmentView policyAssignmentView : policyAssignments) {
      String resourceType = policyAssignmentView.getScope().getType();
      String resourceId = policyAssignmentView.getScope().getId();
      String policyName = policyIdToNameMap.get(policyAssignmentView.getPolicyId());
      if (policyName == null) {
        LOG.error(
            "Outdated policy assignment, the policy it based on no longer exists, {}",
            StructuredArguments.entries(Map.of("policyId", policyAssignmentView.getPolicyId())));
        continue;
      }
      policyAssignmentView.setPolicyDisplayName(policyName);

      if (resourceType.equals(ResourceTypeConstants.ORGANIZATION.getType())) {
        Organization matchingOrg = orgsMap.get(resourceId);
        if (matchingOrg != null) {
          policyAssignmentView.setResourceDisplayName(matchingOrg.getName());
        }

      } else if (resourceType.equals(ResourceTypeConstants.PROJECT.getType())) {
        Group matchingProject = projectsMap.get(resourceId);
        if (matchingProject != null) {
          policyAssignmentView.setResourceDisplayName(matchingProject.getName());
        }
      } else if (resourceType.equals(ResourceTypeConstants.CLUSTER.getType())) {
        if (clusterIdToClusterNameMap.containsKey(resourceId)) {
          policyAssignmentView.setResourceDisplayName(clusterIdToClusterNameMap.get(resourceId));
        }
      } else {
        LOG.error(
            "Found policy assignment with unrecognized resourceType {}.",
            StructuredArguments.entries(
                Map.of("resourceType", resourceType, "id", id, "isInvite", isInvite)));
      }
    }
  }

  @GET
  @Path("/permissions")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationPolicyResource.getAllPermissionsUnderOrgResourceType.GET")
  public Response getAllPermissionsUnderOrgResourceType() throws AuthzServiceClientException {
    List<PermissionOptionMessage> permissionOptionViews =
        permissionClient.getAllPermissionsUnderAncestorResourceType(
            new ResourceTypeId(
                ResourceConstants.CLOUD_SERVICE.getName(),
                ResourceTypeConstants.ORGANIZATION.getType()));

    // Remove actions that are RoleSets
    List<PermissionOptionView> response =
        permissionOptionViews.stream()
            .map(
                pv -> {
                  Set<String> actions =
                      pv.getActionsList().stream()
                          .filter(action -> !EnumUtils.isValidEnum(RoleSet.class, action))
                          .collect(Collectors.toSet());
                  return new PermissionOptionView(
                      ResourceTypeIdView.from(pv.getResourceTypeId()), actions);
                })
            .toList();

    return Response.ok().entity(response).build();
  }

  private ResourceClient getResourceClient() {
    return resourceClientProvider.get();
  }

  @PUT
  @Path("/assignments/user/{userId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationPolicyResource.updateUserPolicyAssignments.PUT")
  public Response updateUserPolicyAssignments(
      // userId will represent a username when updating an invitation
      @PathParam("userId") String userIdOrInviteUsername,
      @Context AuditInfo auditInfo,
      @Context Organization organization,
      @Context AppUser requestUser,
      List<PolicyAssignmentView> newAssignments)
      throws SvcException {
    boolean userHasInviteToOrg =
        invitationSvc.isUserInvitedToOrg(organization.getId(), userIdOrInviteUsername);
    if (userHasInviteToOrg) {
      validateAllNewAssignmentsBelongToOrg(newAssignments, organization.getId());
      invitationSvc.createOrUpdateInvitation(
          requestUser,
          StringUtils.trimToNull(userIdOrInviteUsername),
          organization,
          Set.of(),
          newAssignments,
          Set.of(),
          auditInfo);
      return Response.ok().entity(newAssignments).build();
    }

    ActorView actor = actorClient.getActor(userIdOrInviteUsername);
    if (actor == null) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    validateAllNewAssignmentsBelongToOrg(newAssignments, organization.getId());

    try {
      List<PolicyAssignmentView> currentAssignments =
          actor.getPolicyAssignments().stream()
              .filter(this::isNotGlobalPolicyAssignment)
              .filter(
                  assignment -> {
                    try {
                      return policyBelongsToOrg(assignment, organization.getId());
                    } catch (AuthzServiceClientException e) {
                      throw new RuntimeException(e);
                    }
                  })
              .toList();
      List<PolicyAssignmentView> assignmentsToRemove =
          findAssignmentsDiffByPolicyId(currentAssignments, newAssignments);
      List<PolicyAssignmentView> assignmentsToAdd =
          findAssignmentsDiffByPolicyId(newAssignments, currentAssignments);
      Map<String, List<PolicyAssignmentView>> policyAssignmentViewMap =
          updatePolicyAssignments(
              organization, List.of(actor), assignmentsToAdd, assignmentsToRemove, auditInfo);
      if (!policyAssignmentViewMap.containsKey(userIdOrInviteUsername)) {
        return Response.status(Response.Status.BAD_REQUEST).build();
      }
      return Response.ok().entity(policyAssignmentViewMap.get(userIdOrInviteUsername)).build();
    } catch (RuntimeException e) {
      if (e.getCause() instanceof AuthzServiceClientException) {
        throw (AuthzServiceClientException) e.getCause();
      }
      throw e;
    }
  }

  @PUT
  @Path("/assignments")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationPolicyResource.addOrgPolicyAssignments.PUT")
  public Response addOrgPolicyAssignments(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      PolicyAssignmentForm policyAssignmentForm)
      throws SvcException {
    List<String> userIds = policyAssignmentForm.getUserIds();
    List<ActorView> actors = new ArrayList<>(userIds.size());
    for (String userId : userIds) {
      try {
        // If any look up failed, we won't attempt to update.
        actors.add(actorClient.getActor(userId));
      } catch (AuthzServiceClientException ex) {
        return Response.status(Response.Status.BAD_REQUEST).build();
      }
    }

    Map<String, List<PolicyAssignmentView>> policyAssignmentViewMap =
        updatePolicyAssignments(
            organization,
            actors,
            policyAssignmentForm.getPolicyAssignments(),
            List.of(),
            auditInfo);

    if (!policyAssignmentViewMap.keySet().containsAll(userIds)) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    return Response.ok().entity(policyAssignmentViewMap).build();
  }

  // TODO: AI CLOUDP-237624 We're sending a request for each userId, but we'll want to update this
  // once AuthZ has a method that can accept multiple userIds
  private Map<String, List<PolicyAssignmentView>> updatePolicyAssignments(
      Organization organization,
      List<ActorView> actors,
      List<PolicyAssignmentView> assignmentsToAdd,
      List<PolicyAssignmentView> assignmentsToRemove,
      AuditInfo auditInfo)
      throws SvcException {
    policyClient.validatePoliciesExistInOrg(
        ListUtils.union(assignmentsToAdd, assignmentsToRemove), organization.getId());

    Map<String, List<PolicyAssignmentView>> results = new HashMap<>();

    if (actors == null || assignmentsToAdd == null || assignmentsToRemove == null) {
      return results;
    }

    List<AppUser> users =
        userSvc.findByIds(
            actors.stream().map(actor -> new ObjectId(actor.getId())).collect(Collectors.toSet()));

    Map<String, AppUser> userIdToUserMap = new HashMap<>();
    for (AppUser user : users) {
      // If any are invalid, we won't attempt to update
      if (user == null || !user.getOrgIds().contains(organization.getId())) {
        return results;
      }
      userIdToUserMap.put(user.getId().toString(), user);
    }

    // If we're removing assignments, validate that we're not leaving
    // and organization without at least one LOCAL (human) app user with an ORG_OWNER
    // policy
    var orgOwnerAssignments =
        assignmentsToRemove.stream()
            .filter(assignment -> assignment.getPolicyId().equals(Role.ORG_OWNER.name()))
            .toList();
    for (var orgOwnerAssigment : orgOwnerAssignments) {
      var toRemove = users.stream().filter(AppUser::isLocal).map(AppUser::getId).collect(toSet());
      // don't bother with a network request we know we won't need to make
      if (!toRemove.isEmpty()) {
        var nextOrgOwner =
            userSvc
                .findByIds(
                    actorClient.getAllActorsByPolicyAssignment(orgOwnerAssigment).stream()
                        .map(ObjectId::new)
                        .collect(toSet()))
                .stream()
                .filter(AppUser::isLocal)
                .map(AppUser::getId)
                .filter(not(toRemove::contains))
                .findFirst();
        if (nextOrgOwner.isEmpty()) {
          LOG.warn(
              "Attempt to change the last organization owner failed, group {}",
              orgOwnerAssigment.getScope().getId());
          throw new SvcException(AppUserErrorCode.CANNOT_DELETE_LAST_ADMIN);
        }
      }
    }

    for (ActorView actor : actors) {
      List<PolicyAssignmentView> assignmentsBefore = actor.getPolicyAssignments();
      actorClient.editPolicyAssignments(
          actor.getId(),
          assignmentsToAdd.stream().map(ConversionUtils::mapPolicyAssignmentViewToMessage).toList(),
          assignmentsToRemove.stream()
              .map(ConversionUtils::mapPolicyAssignmentViewToMessage)
              .toList());
      List<PolicyAssignmentView> updatedAssignments =
          actorClient.getActor(actor.getId().toString()).getPolicyAssignments();

      results.put(actor.getId(), updatedAssignments);

      // TODO: JL CLOUDP-276444: Update the activity feed event to include the resource name that a
      // policy is applied to
      auditUserPolicyAssignmentsChanged(
          userIdToUserMap.get(actor.getId()).getUsername(),
          organization.getId(),
          assignmentsBefore,
          updatedAssignments,
          auditInfo);
    }

    return results;
  }

  private List<PolicyAssignmentView> findAssignmentsDiffByPolicyId(
      List<PolicyAssignmentView> baseAssignments, List<PolicyAssignmentView> assignmentsToRemove) {
    Set<String> idsOfPoliciesToBeRemoved =
        assignmentsToRemove.stream()
            .map(PolicyAssignmentView::getPolicyId)
            .collect(Collectors.toSet());

    Map<String, PolicyAssignmentView> idToAssignmentMap =
        assignmentsToRemove.stream()
            .collect(Collectors.toMap(PolicyAssignmentView::getPolicyId, assignment -> assignment));

    return baseAssignments.stream()
        .filter(
            assignment ->
                !idsOfPoliciesToBeRemoved.contains(assignment.getPolicyId())
                    || !idToAssignmentMap
                        .get(assignment.getPolicyId())
                        .getScope()
                        .equals(assignment.getScope()))
        .toList();
  }

  private List<PolicyView> getOrgAllNonGlobalPolicies(ObjectId orgId)
      throws AuthzServiceClientException {
    List<CustomPolicyView> customPolicies = policyClient.getCustomPolicies(orgId);
    List<ManagedPolicyView> nonGlobalManagedPolicies =
        policyClient.getManagedPolicies().stream()
            .filter(
                policy ->
                    !policy.getId().startsWith(ResourceConstants.GLOBAL_POLICY_PREFIX.getName()))
            .toList();
    return ListUtils.union(customPolicies, nonGlobalManagedPolicies);
  }

  private ActorView removeUsersGlobalAssignments(ActorView actor) {
    List<PolicyAssignmentView> nonGlobalAssignments =
        actor.getPolicyAssignments().stream().filter(this::isNotGlobalPolicyAssignment).toList();
    return new ActorView(actor.getId(), nonGlobalAssignments, actor.getUserGroupIds());
  }

  private boolean isNotGlobalPolicyAssignment(PolicyAssignmentView policyAssignment) {
    return !policyAssignment
        .getPolicyId()
        .startsWith(ResourceConstants.GLOBAL_POLICY_PREFIX.getName());
  }

  private ActorView filterAssignmentsNotBelongingToOrg(ActorView actor, ObjectId orgId)
      throws AuthzServiceClientException {
    try {
      List<PolicyAssignmentView> filteredPolicies =
          actor.getPolicyAssignments().stream()
              .filter(
                  (assignment) -> {
                    try {
                      return policyBelongsToOrg(assignment, orgId);
                    } catch (AuthzServiceClientException e) {
                      throw new RuntimeException(e);
                    }
                  })
              .toList();
      return new ActorView(actor.getId(), filteredPolicies, actor.getUserGroupIds());
    } catch (RuntimeException e) {
      // Rethrow unchecked exception from lambda
      if (e.getCause() instanceof AuthzServiceClientException) {
        throw (AuthzServiceClientException) e.getCause();
      }
      throw e;
    }
  }

  private boolean policyBelongsToOrg(PolicyAssignmentView assignment, ObjectId orgId)
      throws AuthzServiceClientException {
    if (assignment.getScope().getType().equals(ResourceTypeConstants.ORGANIZATION.getType())) {
      return assignment.getScope().getId().equals(orgId.toString());
    }
    Set<String> ancestorIds =
        getResourceClient().getAncestorResourceIds(assignment.getScope()).stream()
            .map(ResourceIdView::getId)
            .collect(Collectors.toSet());
    return ancestorIds.contains(orgId.toString());
  }

  private void validateAllNewAssignmentsBelongToOrg(
      List<PolicyAssignmentView> newAssignments, ObjectId orgId) throws SvcException {
    for (PolicyAssignmentView assignment : newAssignments) {
      if (!policyBelongsToOrg(assignment, orgId)) {
        throw new SvcException(
            CommonErrorCode.INVALID_PARAMETER,
            String.format(
                "The requested policyAssignment %s doesn't belong to org %s", assignment, orgId));
      }
    }
  }

  private Map<String, String> getProjectAndClusterNames(Collection<ObjectId> clusterIds) {
    Map<String, String> clusterIdToClusterNameMap = new HashMap<>();
    Map<ObjectId, Optional<ClusterDescription>> clusterIdToDescriptionMap =
        ndsLookupSvc.findClusterDescriptionsByClusterUniqueIds(clusterIds);
    clusterIdToDescriptionMap.entrySet().stream()
        .filter(entry -> entry.getValue().isPresent())
        .forEach(
            entry -> {
              ClusterDescription description = entry.getValue().get();
              ObjectId groupId = description.getGroupId();
              Group group = groupSvc.findById(groupId);
              String groupAndClusterName =
                  String.format("%s/%s", group.getName(), description.getName());
              clusterIdToClusterNameMap.put(entry.getKey().toString(), groupAndClusterName);
            });
    return clusterIdToClusterNameMap;
  }

  private void auditUserPolicyAssignmentsChanged(
      String targetUsername,
      ObjectId orgId,
      List<PolicyAssignmentView> assignmentsBefore,
      List<PolicyAssignmentView> assignmentsAfter,
      AuditInfo auditInfo)
      throws AuthzServiceClientException {
    UserAudit.Builder builder =
        new UserAudit.Builder(UserEvent.Type.USER_POLICY_ASSIGNMENTS_CHANGED_AUDIT);
    List<String> beforeMessages = mapAssignmentsToAuditMessages(assignmentsBefore, orgId);
    List<String> afterMessages = mapAssignmentsToAuditMessages(assignmentsAfter, orgId);
    builder.targetUsername(targetUsername);
    builder.auditInfo(auditInfo);
    builder.orgId(orgId);
    builder.policyAssignmentsBeforeUpdate(beforeMessages);
    builder.policyAssignmentsAfterUpdate(afterMessages);
    auditSvc.saveAuditEvent(builder.build());
  }

  private String getPolicyNameById(String policyId) throws AuthzServiceClientException {
    if (ObjectId.isValid(policyId)) {
      return policyClient.getCustomPolicyById(policyId).getName();
    } else {
      return policyClient.getManagedPolicyById(policyId).getName();
    }
  }

  @VisibleForTesting
  protected List<String> mapAssignmentsToAuditMessages(
      List<PolicyAssignmentView> assignments, ObjectId orgId) throws AuthzServiceClientException {
    List<String> formattedMsgs = new ArrayList<>();
    for (PolicyAssignmentView assignment : assignments) {
      if (isNotGlobalPolicyAssignment(assignment) && policyBelongsToOrg(assignment, orgId)) {
        formattedMsgs.add(buildEventMessage(assignment));
      }
    }
    return formattedMsgs;
  }

  private String buildEventMessage(PolicyAssignmentView assignment)
      throws AuthzServiceClientException {
    String policyName = getPolicyNameById(assignment.getPolicyId());
    return String.format(
        "%s: {%s: %s}", policyName, assignment.getScope().getType(), assignment.getScope().getId());
  }
}
