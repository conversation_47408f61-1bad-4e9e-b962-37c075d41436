package com.xgen.cloud.organization.runtime.res;

import static net.logstash.logback.argument.StructuredArguments.entries;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.VercelClusterLinkingView;
import com.xgen.cloud.organization._public.model.VercelInfo;
import com.xgen.cloud.organization._public.model.VercelLinkedCluster;
import com.xgen.cloud.organization._public.model.VercelProject;
import com.xgen.cloud.organization._public.model.VercelProjectView;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.organization._public.svc.OrganizationVercelIntegrationSvc;
import com.xgen.cloud.organization._public.svc.VercelIntegrationErrorCode;
import com.xgen.cloud.organization._public.svc.VercelIntegrationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.svc.common.OrgErrorCode;
import com.xgen.svc.mms.svc.vercel.VercelEmailSvc;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import io.prometheus.client.Counter;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.logstash.logback.argument.StructuredArguments;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("NullAway") // suppressed to enable nullaway to address another issue
@Path("/orgs/{orgId}/integrations/vercel")
@Singleton
public class VercelIntegrationResource extends BaseResource {
  private final VercelIntegrationSvc vercelIntegrationSvc;
  private final OrganizationSvc organizationSvc;
  private final OrganizationVercelIntegrationSvc orgVercelIntegrationSvc;
  private final NDSUISvc ndsUISvc;
  private final UserSvc userSvc;
  private final GroupSvc groupSvc;
  private final VercelEmailSvc vercelEmailSvc;
  private final AuditSvc auditSvc;
  private final AuthzSvc authzSvc;

  private static final Logger LOG = LoggerFactory.getLogger(VercelIntegrationResource.class);

  private static final Counter CONFIRM_VERCEL_INTEGRATION_COUNTER =
      Counter.build()
          .name("mms_iam_vercel_integration_confirmed_total")
          .help("Total Vercel Integration Confirmed")
          .register();

  private static final Counter FAIL_TO_LINK_CLUSTER_TO_VERCEL_PROJECT_COUNTER =
      Counter.build()
          .name("mms_iam_vercel_integration_fail_to_link_cluster_to_vercel_project_total")
          .help("Total clusters that failed to link to Vercel project")
          .register();

  private static final Counter FAIL_TO_UNLINK_CLUSTER_FROM_VERCEL_PROJECT_COUNTER =
      Counter.build()
          .name("mms_iam_vercel_integration_fail_to_unlink_cluster_from_vercel_project_total")
          .help("Total clusters that failed to unlink from Vercel project")
          .register();

  private static final Counter FAIL_TO_SETUP_DATA_API_FOR_VERCEL_PROJECT_COUNTER =
      Counter.build()
          .name("mms_iam_vercel_integration_fail_to_setup_data_api_for_vercel_project_total")
          .help("Total failures for setting up Data API for Vercel project")
          .register();

  @Inject
  public VercelIntegrationResource(
      VercelIntegrationSvc vercelIntegrationSvc,
      OrganizationSvc organizationSvc,
      OrganizationVercelIntegrationSvc orgVercelIntegrationSvc,
      NDSUISvc ndsUISvc,
      UserSvc userSvc,
      GroupSvc groupSvc,
      VercelEmailSvc vercelEmailSvc,
      AuditSvc auditSvc,
      AuthzSvc authzSvc) {
    this.vercelIntegrationSvc = vercelIntegrationSvc;
    this.organizationSvc = organizationSvc;
    this.orgVercelIntegrationSvc = orgVercelIntegrationSvc;
    this.ndsUISvc = ndsUISvc;
    this.userSvc = userSvc;
    this.groupSvc = groupSvc;
    this.vercelEmailSvc = vercelEmailSvc;
    this.auditSvc = auditSvc;
    this.authzSvc = authzSvc;
  }

  protected void incrementLinkClustersToVercelProjectErrorCounter() {
    FAIL_TO_LINK_CLUSTER_TO_VERCEL_PROJECT_COUNTER.inc();
  }

  protected void incrementUnlinkClustersFromVercelProjectErrorCounter() {
    FAIL_TO_UNLINK_CLUSTER_FROM_VERCEL_PROJECT_COUNTER.inc();
  }

  protected void incrementSetupDataApiErrorCounter() {
    FAIL_TO_SETUP_DATA_API_FOR_VERCEL_PROJECT_COUNTER.inc();
  }

  @GET
  @Path("/projects")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.VercelIntegrationResource.getVercelProjectsForCurrentUser.GET")
  public Response getVercelProjectsForCurrentUser(
      @Parameter(hidden = true) @PathParam("orgId") ObjectId orgId) {
    List<VercelProject> projects;
    List<VercelProjectView> vercelProjectViews = new ArrayList<>();

    Organization organization = organizationSvc.findById(orgId);

    if (organization.hasVercelIntegration()) {
      projects =
          vercelIntegrationSvc.getAllProjectsForCurrentUserWithVercelInfo(
              organization.getVercelInfo());

      // filter out the projects that are currently linked to any of the Clusters as one project can
      // only be linked to one cluster at any given point
      List<VercelLinkedCluster> currentlyLinkedClusters =
          organization.getVercelInfo().getLinkedClusters();
      List<String> currentlyLinkedVercelProjectsIds = new ArrayList<>();

      for (VercelLinkedCluster linkedCluster : currentlyLinkedClusters) {
        List<String> vercelProjectIds =
            linkedCluster.getVercelProjects().stream().map(VercelProject::getId).toList();
        currentlyLinkedVercelProjectsIds.addAll(vercelProjectIds);
      }

      for (VercelProject project : projects) {
        String projectId = project.getId();
        VercelProjectView vercelProjectView;
        if (currentlyLinkedVercelProjectsIds.contains(projectId)) {
          vercelProjectView = new VercelProjectView(projectId, project.getName(), Boolean.TRUE);
        } else {
          vercelProjectView = new VercelProjectView(projectId, project.getName(), Boolean.FALSE);
        }
        vercelProjectViews.add(vercelProjectView);
      }

      return Response.ok().entity(new JSONArray(vercelProjectViews).toString()).build();
    }

    return Response.ok().entity(new JSONArray(vercelProjectViews).toString()).build();
  }

  @PATCH
  @Path("/{vercelUserId}/link/{groupId}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.PATH)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.VercelIntegrationResource.manageClusterLinkingToVercelProjects.PATCH")
  public Response manageClusterLinkingToVercelProjects(
      @Context HttpServletRequest request,
      @Context Organization organization,
      @Context Group group,
      @Context AppUser appUser,
      @Context AuditInfo auditInfo,
      VercelClusterLinkingView vercelClusterLinkingView,
      @PathParam("vercelUserId") String vercelUserId,
      @PathParam("orgId") ObjectId orgId,
      @PathParam("groupId") ObjectId groupId)
      throws SvcException {
    String clusterName = vercelClusterLinkingView.getClusterName();
    if (!authzSvc.isProjectOwner(appUser, group)) {
      throw new SvcException(
          OrgErrorCode.INSUFFICIENT_PERMISSIONS_TO_MANAGE_LINKING_FOR_CLUSTER_TO_VERCEL_PROJECTS);
    }

    // can't create/manage linking to Atlas cluster if no Vercel projects are specified
    if (vercelClusterLinkingView == null
        || vercelClusterLinkingView.getVercelProjects() == null
        || vercelClusterLinkingView.getVercelProjects().isEmpty()) {
      incrementLinkClustersToVercelProjectErrorCounter();
      throw new SvcException(VercelIntegrationErrorCode.NO_VERCEL_CLUSTERS_PROVIDED);
    }

    try {
      if (vercelClusterLinkingView.getIsEditingExistingLinking()) {
        Optional<VercelLinkedCluster> currentlyLinkedCluster =
            organization.getCurrentlyLinkedCluster(clusterName, groupId);

        if (currentlyLinkedCluster.isEmpty()) {
          throw new SvcException(OrgErrorCode.COULD_NOT_FIND_CLUSTER_LINKED_TO_VERCEL);
        }

        List<VercelProject> currentlyLinkedVercelProjects =
            currentlyLinkedCluster.get().getVercelProjects();
        List<VercelProject> ************************ = vercelClusterLinkingView.getVercelProjects();

        if (currentlyLinkedVercelProjects.isEmpty() || ************************.isEmpty()) {
          throw new SvcException(OrgErrorCode.COULD_NOT_FIND_VERCEL_PROJECTS_FOR_GIVEN_CLUSTER);
        }

        List<VercelProject> vercelProjectsToUnlink =
            ListUtils.subtract(currentlyLinkedVercelProjects, ************************);
        List<VercelProject> newVercelProjectsToLink =
            ListUtils.subtract(************************, currentlyLinkedVercelProjects);

        if (vercelProjectsToUnlink.size() > 0) {
          unlinkClusterFromVercelProjects(
              organization, groupId, vercelProjectsToUnlink, vercelClusterLinkingView, auditInfo);
        }

        Boolean setupInitialDataApi = vercelClusterLinkingView.getSetupInitialDataApi();
        boolean dataApiToggled =
            currentlyLinkedCluster.stream()
                .anyMatch(
                    linkedCluster -> linkedCluster.isSetupInitialDataApi() != setupInitialDataApi);

        // case when user toggles off Data API
        if (dataApiToggled && !setupInitialDataApi) {
          List<VercelProject> vercelProjectsToUpdate =
              ListUtils.subtract(currentlyLinkedVercelProjects, vercelProjectsToUnlink);
          vercelIntegrationSvc.removeVercelEnvironmentVariables(
              organization.getVercelInfo(), vercelProjectsToUpdate, true);
        }

        List<VercelProject> vercelProjectsToUpdate =
            dataApiToggled && setupInitialDataApi
                ? ************************
                : newVercelProjectsToLink;

        if (newVercelProjectsToLink.size() > 0 || dataApiToggled) {
          linkClusterToVercelProjects(
              organization, group, vercelProjectsToUpdate, vercelClusterLinkingView, auditInfo);
        }

        // update the linking locally
        orgVercelIntegrationSvc.updateExistingLinkingToVercelProjects(
            vercelUserId, organization, groupId, setupInitialDataApi, vercelClusterLinkingView);
      } else {
        linkClusterToVercelProjects(
            organization,
            group,
            vercelClusterLinkingView.getVercelProjects(),
            vercelClusterLinkingView,
            auditInfo);
      }

    } catch (SvcException e) {
      incrementLinkClustersToVercelProjectErrorCounter();
      LOG.error(
          "Could not link Cluster to Vercel projects due to: {}",
          entries(
              Map.of(
                  "orgId", String.valueOf(orgId),
                  "groupId", String.valueOf(group.getId()),
                  "clusterName", String.valueOf(clusterName),
                  "vercelUserId", String.valueOf(vercelUserId),
                  "setupInitialDataApi",
                      String.valueOf(vercelClusterLinkingView.getSetupInitialDataApi()),
                  "errorCode", String.valueOf(e.getErrorCode()))),
          e);
      throw new SvcException(
          VercelIntegrationErrorCode.COULD_NOT_LINK_CLUSTER_TO_VERCEL_PROJECTS,
          orgId,
          vercelUserId);
    }

    return Response.ok(organization.getVercelInfo()).build();
  }

  @DELETE
  @Path("/{vercelUserId}/link/{groupId}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.organization.VercelIntegrationResource.unlinkClusterFromVercelProjects.DELETE")
  public Response unlinkClusterFromVercelProjects(
      @Context Organization organization,
      @Context Group group,
      @Context AppUser appUser,
      @Context AuditInfo auditInfo,
      VercelClusterLinkingView vercelClusterLinkingView,
      @PathParam("vercelUserId") String vercelUserId,
      @PathParam("orgId") ObjectId orgId,
      @PathParam("groupId") ObjectId groupId)
      throws SvcException {
    String clusterName = vercelClusterLinkingView.getClusterName();

    if (!authzSvc.isProjectOwner(appUser, group)) {
      throw new SvcException(
          OrgErrorCode.INSUFFICIENT_PERMISSIONS_TO_MANAGE_LINKING_FOR_CLUSTER_TO_VERCEL_PROJECTS);
    }
    try {
      unlinkClusterFromVercelProjects(
          organization,
          groupId,
          vercelClusterLinkingView.getVercelProjects(),
          vercelClusterLinkingView,
          auditInfo);
    } catch (SvcException e) {
      incrementUnlinkClustersFromVercelProjectErrorCounter();
      LOG.warn(
          "Cluster {} for Project {} in Organization {} could not be unlinked from Vercel Projects"
              + " for Vercel User {} due to: {}",
          StructuredArguments.keyValue("clusterName", clusterName),
          StructuredArguments.keyValue("groupId", groupId),
          StructuredArguments.keyValue("orgId", orgId),
          StructuredArguments.keyValue("vercelUserId", vercelUserId),
          StructuredArguments.keyValue("error", e));
      throw new SvcException(
          VercelIntegrationErrorCode.COULD_NOT_UNLINK_CLUSTER_FROM_VERCEL_PROJECTS,
          clusterName,
          groupId,
          orgId,
          vercelUserId);
    }

    return Response.ok(organization.getVercelInfo()).build();
  }

  @POST
  @Path("/confirmVercel")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response ************************(
      @Context Organization pOrganization,
      @Context AppUser pAppUser,
      @Context AuditInfo pAuditInfo,
      @PathParam("orgId") ObjectId pOrgId,
      String pParams)
      throws SvcException {
    JSONObject params = new JSONObject(pParams);
    String vercelUserId = params.optString("vercelUserId", null);

    if (StringUtils.isBlank(vercelUserId)) {
      LOG.info(
          "Vercel User Id={} not provided when attempting to confirm the"
              + " integration for the Organization Id={} and User={} ",
          vercelUserId,
          pOrgId,
          pAppUser.getUsername());
      throw new SvcException(VercelIntegrationErrorCode.ORG_ID_VERCEL_USER_ID_ARE_REQUIRED);
    }

    VercelInfo vercelInfo =
        orgVercelIntegrationSvc.************************(pOrganization, vercelUserId, pAuditInfo);

    // send email to all org owners
    Set<String> orgOwnerUsernames =
        userSvc.findLocalOwnersByOrgId(pOrgId).stream()
            .map(AppUser::getPrimaryEmail)
            .collect(Collectors.toSet());

    CONFIRM_VERCEL_INTEGRATION_COUNTER.inc();
    vercelEmailSvc.sendIntegrationConfirmedEmail(vercelInfo, pOrganization, orgOwnerUsernames);
    return Response.ok().build();
  }

  @PATCH
  @Path("/disconnectVercel")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response disconnectVercelIntegration(
      @Context HttpServletRequest pRequest,
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      String pParams)
      throws SvcException {
    JSONObject paramsObj = new JSONObject(pParams);
    boolean deleteAccessRule = paramsObj.getBoolean("deleteAccessRule");
    boolean deleteUsers = paramsObj.getBoolean("deleteUsers");
    VercelInfo vercelInfo = pOrganization.getVercelInfo();

    if (vercelInfo == null) {
      throw new SvcException(OrgErrorCode.ORG_NOT_LINKED_TO_VERCEL);
    }

    List<VercelLinkedCluster> clusters = vercelInfo.getLinkedClusters();
    List<ObjectId> failedNetworkPermissionGroupIds = new ArrayList<>();
    List<ObjectId> failedDBUsersGroupIds = new ArrayList<>();

    orgVercelIntegrationSvc.disconnectVercelIntegration(pOrganization, pAuditInfo);
    LOG.info("Vercel integration for organization={} disconnected", pOrganization.getId());

    if (deleteAccessRule) {
      failedNetworkPermissionGroupIds =
          ndsUISvc.deleteVercelNetworkPermission(clusters, pRequest, pAuditInfo);
    }

    if (deleteUsers) {
      failedDBUsersGroupIds = ndsUISvc.deleteVercelDBUsers(clusters, pAuditInfo);
    }

    Set<String> orgOwnerEmails =
        userSvc.findLocalOwnersByOrgId(pOrganization.getId()).stream()
            .map(AppUser::getPrimaryEmail)
            .collect(Collectors.toSet());

    vercelEmailSvc.sendIntegrationDisconnectedEmail(
        pOrganization.getVercelInfo(),
        pOrganization,
        orgOwnerEmails,
        deleteAccessRule,
        deleteUsers);

    if (failedNetworkPermissionGroupIds.size() > 0 && failedDBUsersGroupIds.size() > 0) {
      throw new SvcException(OrgErrorCode.FAILED_TO_DELETE_VERCEL_DB_USERS_AND_NETWORK_PERMISSIONS);
    }

    if (failedNetworkPermissionGroupIds.size() > 0) {
      throw new SvcException(OrgErrorCode.FAILED_TO_DELETE_VERCEL_NETWORK_PERMISSIONS);
    }

    if (failedDBUsersGroupIds.size() > 0) {
      throw new SvcException(OrgErrorCode.FAILED_TO_DELETE_VERCEL_DB_USERS);
    }

    return Response.ok().build();
  }

  private void linkClusterToVercelProjects(
      Organization organization,
      Group group,
      List<VercelProject> vercelProjectsToLink,
      VercelClusterLinkingView vercelClusterLinkingView,
      AuditInfo auditInfo)
      throws SvcException {
    VercelInfo vercelInfo = organization.getVercelInfo();
    String vercelUserId = vercelInfo.getVercelUserId();
    String clusterName = vercelClusterLinkingView.getClusterName();
    Boolean setupInitialDataApi = vercelClusterLinkingView.getSetupInitialDataApi();
    String dataApiKeyName = vercelClusterLinkingView.getDataApiKeyName();

    Map<String, String> connectionData =
        ndsUISvc.configureClusterLinkForVercelProjects(group, clusterName, auditInfo);
    String connectionURI = connectionData.get("connectionURI");
    String dbUsername = connectionData.get("dbUsername");

    ObjectId groupId = group.getId();

    Map<String, String> vercelEnvVars = new HashMap<>();
    vercelEnvVars.put(VercelLinkedCluster.MONGO_URI_KEY_ENV_VAR_NAME, connectionURI);
    if (setupInitialDataApi) {
      vercelEnvVars.put(
          VercelLinkedCluster.DATA_API_KEY_ENV_VAR_NAME, vercelClusterLinkingView.getDataApiKey());
      vercelEnvVars.put(
          VercelLinkedCluster.DATA_API_URL_ENV_VAR_NAME, vercelClusterLinkingView.getDataApiUrl());
    }

    List<String> updatedProjectNames =
        vercelIntegrationSvc.setVercelEnvironmentVariables(
            vercelEnvVars, vercelInfo, vercelProjectsToLink);

    NDSAudit.Builder builder = new NDSAudit.Builder(NDSAudit.Type.CLUSTER_LINKED_TO_VERCEL);

    if (!vercelClusterLinkingView.getIsEditingExistingLinking()) {
      orgVercelIntegrationSvc.linkClusterToVercelProjects(
          vercelUserId, organization, groupId, setupInitialDataApi, vercelClusterLinkingView);

      // get the list of all org and project owners to send the cluster linked email to
      List<AppUser> orgOwners = userSvc.findLocalOwnersByOrgId(organization.getId());
      List<AppUser> groupOwners = groupSvc.findOwnersByGroup(group);
      Set<String> allOwnerEmails =
          Stream.of(orgOwners, groupOwners)
              .flatMap(List<AppUser>::stream)
              .map(AppUser::getUsername)
              .collect(Collectors.toSet());

      ClusterDescriptionView clusterDescriptionView =
          ndsUISvc.getClusterDescription(groupId, clusterName, true, false);

      if (clusterDescriptionView != null) {
        builder.clusterId(clusterDescriptionView.getUniqueId());
      }

      vercelEmailSvc.sendClusterLinkedEmail(
          vercelInfo,
          dbUsername,
          allOwnerEmails,
          organization,
          group.getName(),
          group.getId(),
          updatedProjectNames,
          clusterName,
          clusterDescriptionView.getRegionNameFromReplicationSpec(),
          VercelLinkedCluster.MONGO_URI_KEY_ENV_VAR_NAME,
          setupInitialDataApi,
          dataApiKeyName);
    }

    builder.auditInfo(auditInfo);
    builder.groupId(groupId);
    builder.clusterName(clusterName);
    builder.vercelEnvVarName(String.join(", ", vercelEnvVars.keySet()));
    builder.setupInitialDataApi(setupInitialDataApi);
    auditSvc.saveAuditEvent(builder.build());
  }

  private void unlinkClusterFromVercelProjects(
      Organization organization,
      ObjectId groupId,
      List<VercelProject> projectsToUnlink,
      VercelClusterLinkingView vercelClusterLinkingView,
      AuditInfo auditInfo)
      throws SvcException {
    String clusterName = vercelClusterLinkingView.getClusterName();
    String vercelUserId = organization.getVercelInfo().getVercelUserId();
    vercelIntegrationSvc.removeVercelEnvironmentVariables(
        organization.getVercelInfo(), projectsToUnlink, false);

    if (!vercelClusterLinkingView.getIsEditingExistingLinking()) {
      orgVercelIntegrationSvc.unlinkClusterFromVercelProjects(
          vercelUserId, organization, groupId, vercelClusterLinkingView);
    }

    NDSAudit.Builder builder = new NDSAudit.Builder(Type.CLUSTER_UNLINKED_FROM_VERCEL);
    builder.auditInfo(auditInfo);
    builder.groupId(groupId);

    if (groupId != null && !StringUtils.isBlank(clusterName)) {
      ClusterDescriptionView clusterDescriptionView =
          ndsUISvc.getClusterDescription(groupId, clusterName, true, false);
      if (clusterDescriptionView != null) {
        builder.clusterId(clusterDescriptionView.getUniqueId());
      }
    }

    builder.clusterName(clusterName);
    builder.vercelEnvVarName(VercelLinkedCluster.MONGO_URI_KEY_ENV_VAR_NAME);
    auditSvc.saveAuditEvent(builder.build());
  }
}
