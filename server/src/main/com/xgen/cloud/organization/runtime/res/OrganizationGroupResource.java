package com.xgen.cloud.organization.runtime.res;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.groupcreation._public.svc.GroupCreationSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupCreationForm;
import com.xgen.cloud.group._public.model.GroupCreationResponse;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.organization._private.view.UserGroupsView;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.resourcetags._private.svc.ResourceTagsManagementSvcImpl;
import com.xgen.cloud.resourcetags._public.model.ResourceId;
import com.xgen.cloud.resourcetags._public.model.ResourceService;
import com.xgen.cloud.resourcetags._public.model.ResourceType;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.view.GroupInListView;
import com.xgen.svc.mms.res.view.settings.GroupUsersView;
import com.xgen.svc.mms.res.view.user.UserView;
import com.xgen.svc.mms.svc.GroupViewSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("NullAway") // suppressed to enable nullaway to address another issue
@Path("/orgs")
@Produces(MediaType.APPLICATION_JSON)
@Singleton
public class OrganizationGroupResource extends BaseResource {

  private final OrganizationSvc organizationSvc;
  private final GroupSvc groupSvc;
  private final GroupCreationSvc groupCreationSvc;
  private final UserSvc userSvc;
  private final ConfigLimitSvc configLimitSvc;
  private final NDSGroupSvc ndsGroupSvc;
  private final AppSettings appSettings;
  private final GroupViewSvc groupViewSvc;
  private final ResourceTagsManagementSvcImpl resourceTagsManagementSvc;

  @Inject
  public OrganizationGroupResource(
      OrganizationSvc pOrganizationSvc,
      GroupSvc pGroupSvc,
      GroupCreationSvc pGroupCreationSvc,
      UserSvc pUserSvc,
      ConfigLimitSvc pConfigLimitSvc,
      NDSGroupSvc pNDSGroupSvc,
      GroupViewSvc pGroupViewSvc,
      AppSettings pAppSettings,
      ResourceTagsManagementSvcImpl resourceTagsManagementSvc) {
    organizationSvc = pOrganizationSvc;
    groupSvc = pGroupSvc;
    groupCreationSvc = pGroupCreationSvc;
    userSvc = pUserSvc;
    configLimitSvc = pConfigLimitSvc;
    ndsGroupSvc = pNDSGroupSvc;
    appSettings = pAppSettings;
    groupViewSvc = pGroupViewSvc;
    this.resourceTagsManagementSvc = resourceTagsManagementSvc;
  }

  @GET
  @Path("/{orgId}/groups")
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationGroupResource.getVisibleGroups.GET")
  public Response getVisibleGroups(@Context Organization pOrganization, @Context AppUser pUser) {
    return Response.ok().entity(getVisibleGroupsInListViews(pOrganization, pUser)).build();
  }

  @POST
  @Path("/{orgId}/groups")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = RoleSet.ORG_GROUP_CREATOR,
      groupSource = GroupSource.NONE,
      appSettingsSetupCall = true)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.OrganizationGroupResource.createGroup.POST")
  public Response createGroup(
      @Context HttpServletRequest pRequest,
      @Context AppUser pUser,
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      GroupCreationForm pGroupCreationForm)
      throws SvcException {
    // Validate addition of 1 group to organization relative to config limits
    getConfigLimitSvc().validateMaxGroupsPerOrg(1, pOrganization.getId());
    // Validate addition of 1 group to user, with null groupId given new group, if applicable
    getConfigLimitSvc().validateMaxGroupsPerUser(1, pUser.getUsername(), null);

    if (pGroupCreationForm.useCNRegionsOnly()
        && pOrganization.hasNDSPlan()
        && !canCreateCNRegionsOnlyGroup(pOrganization)) {
      throw new SvcException(NDSErrorCode.CANNOT_CREATE_CN_REGIONS_ONLY_GROUP);
    }

    if (pOrganization.isDeleted()) {
      throw new SvcException(NDSErrorCode.INVALID_ORG_ID);
    }

    Group createdGroup =
        groupCreationSvc.createFromForm(pGroupCreationForm, pOrganization, pUser, pAuditInfo);

    if (pOrganization.hasNDSPlan()) {
      ndsGroupSvc.ensureGroup(
          createdGroup.getId(),
          pGroupCreationForm.useCNRegionsOnly(),
          pGroupCreationForm.getRegionUsageRestrictions(),
          getAppSettings().isServerlessEnabled());
    }

    if (pGroupCreationForm.getTags() != null && !pGroupCreationForm.getTags().isEmpty()) {
      ResourceId parentResourceId =
          ResourceId.builder(ResourceService.CLOUD.toString(), ResourceType.ORGANIZATION.toString())
              .id(pOrganization.getId())
              .build();
      ResourceId resourceId =
          ResourceId.builder(ResourceService.CLOUD.toString(), ResourceType.PROJECT.toString())
              .id(createdGroup.getId())
              .build();
      resourceTagsManagementSvc.upsertResourceWithTags(
          pOrganization.getId(),
          resourceId,
          parentResourceId,
          pGroupCreationForm.getTags(),
          pAuditInfo);
    }

    return Response.ok()
        .entity(
            new GroupCreationResponse(
                createdGroup.getId(),
                pGroupCreationForm.getName(),
                pGroupCreationForm.getTags(),
                pGroupCreationForm.useCNRegionsOnly(),
                pGroupCreationForm.getRegionUsageRestrictions()))
        .build();
  }

  @GET
  @Path("/{orgId}/groups/users")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationGroupResource.getBulkGroupUsers.GET")
  public Response getBulkGroupUsers(@Context Organization pOrganization, @Context AppUser pUser) {

    List<Group> visibleGroups = getGroupSvc().getVisibleGroups(pOrganization.getId(), pUser);
    List<GroupUsersView> groupUsersViews =
        visibleGroups.stream()
            .map(
                group -> {
                  ModelCursor<AppUser> userCursor =
                      getGroupSvc().getModelCursorByGroup(group, false, false);

                  // users in each group are used to check eligibility
                  // of the logged-in user to leave a group
                  List<UserView> userViewsForGroup = new ArrayList<>();
                  for (AppUser user : userCursor) {
                    userViewsForGroup.add(
                        new UserView.Builder().userId(user.getId().toString()).build());
                  }
                  return new GroupUsersView.Builder()
                      .groupId(group.getId().toString())
                      .users(userViewsForGroup)
                      .build();
                })
            .collect(Collectors.toList());

    return Response.ok(groupUsersViews).build();
  }

  @GET
  @Path("/{orgId}/groups/user/roles")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.OrganizationGroupResource.getUserRolesInAllVisibleGroups.GET")
  public Response getUserRolesInAllVisibleGroups(
      @Context Organization pOrganization, @Context AppUser pUser) {

    List<Group> visibleGroups = getGroupSvc().getVisibleGroups(pOrganization.getId(), pUser);
    List<UserGroupsView> userGroupsViews =
        visibleGroups.stream()
            .map(
                group ->
                    new UserGroupsView(
                        group.getId().toString(),
                        group.getName(),
                        pUser.getGroupRoles(group.getId())))
            .collect(Collectors.toList());
    return Response.ok(userGroupsViews).build();
  }

  protected OrganizationSvc getOrganizationSvc() {
    return organizationSvc;
  }

  protected GroupSvc getGroupSvc() {
    return groupSvc;
  }

  protected UserSvc getUserSvc() {
    return userSvc;
  }

  protected ConfigLimitSvc getConfigLimitSvc() {
    return configLimitSvc;
  }

  private AppSettings getAppSettings() {
    return appSettings;
  }

  private boolean canCreateCNRegionsOnlyGroup(Organization pOrganization) {
    return isFeatureFlagEnabled(
            FeatureFlag.ATLAS_CN_REGIONS_ONLY, getAppSettings(), pOrganization, null)
        && pOrganization.hasAcceptedCNRegionsTermsOfService();
  }

  private List<GroupInListView> getVisibleGroupsInListViews(
      Organization pOrganization, AppUser pUser) {
    return getGroupSvc().getVisibleGroups(pOrganization.getId(), pUser).stream()
        .map(groupViewSvc::getGroupInListView)
        .collect(Collectors.toList());
  }
}
