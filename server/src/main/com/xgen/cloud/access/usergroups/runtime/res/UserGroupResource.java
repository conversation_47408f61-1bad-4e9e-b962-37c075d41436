package com.xgen.cloud.access.usergroups.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.usergroups._public.svc.MmsUserGroupSvc;
import com.xgen.cloud.authz.core._public.client.AuthorizationClient;
import com.xgen.cloud.authz.core._public.view.ui.UserGroupView;
import com.xgen.cloud.authz.core._public.wrapper.AuthorizationClientProvider;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.res._public.base.BaseResource;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/orgs/{orgId}/access/userGroups")
@Feature(FeatureFlag.FINE_GRAINED_AUTH_USER_GROUPS)
@Singleton
public class UserGroupResource extends BaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(UserGroupResource.class);
  private final AuthorizationClient authorizationClient;
  private final MmsUserGroupSvc userGroupSvc;

  @Inject
  public UserGroupResource(
      AuthorizationClientProvider authorizationClientProvider, MmsUserGroupSvc userGroupSvc) {
    this.authorizationClient = authorizationClientProvider.get();
    this.userGroupSvc = userGroupSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_MEMBER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.getUserGroups.GET")
  public Response getUserGroups(@PathParam("orgId") ObjectId orgId) {
    // TODO: BE CLOUDP-236791
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }

  @GET
  @Path("/detail/{userGroupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_MEMBER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.getUserGroupDetail.GET")
  public Response getUserGroupDetail(
      @PathParam("orgId") ObjectId ObjectId, @PathParam("userGroupId") ObjectId userGroupId) {
    // TODO: BE CLOUDP-236792
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_OWNER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.createUserGroup.POST")
  public Response createUserGroup(@PathParam("orgId") ObjectId orgId, UserGroupView userGroupView) {
    // TODO: BE CLOUDP-236793
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }

  @PATCH
  @Path("/{userGroupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_OWNER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.updateUserGroup.PATCH")
  public Response updateUserGroup(@PathParam("userGroupId") ObjectId userGroupId) {
    // TODO: BE CLOUDP-236794
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }

  @DELETE
  @Path("/{userGroupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_OWNER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.deleteUserGroup.DELETE")
  public Response deleteUserGroup(@PathParam("userGroupId") ObjectId userGroupId) {
    // TODO: BE CLOUDP-236795
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }

  @POST
  @Path("/{userGroupId}/members")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_OWNER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.addMember.POST")
  public Response addMember(@PathParam("userGroupId") ObjectId userGroupId) {
    // TODO: BE CLOUDP-236796
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }

  @DELETE
  @Path("/{userGroupId}/members")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ORG_OWNER)
  @Auth(endpointAction = "epa.organization.UserGroupResource.removeMember.DELETE")
  public Response removeMember(@PathParam("userGroupId") ObjectId userGroupId) {
    // TODO: BE CLOUDP-236797
    return Response.status(Status.NOT_IMPLEMENTED).build();
  }
}
