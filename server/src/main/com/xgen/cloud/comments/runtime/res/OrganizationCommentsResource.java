package com.xgen.cloud.comments.runtime.res;

import static com.xgen.cloud.comments._private.constants.CommentConstants.DEFAULT_SKIP;
import static com.xgen.cloud.common.http.authentication._public.HeaderName.INITIATOR_AUTH;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toList;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.authz.resource._public.client.ResourceClient;
import com.xgen.cloud.authz.resource._public.wrapper.ResourceClientProvider;
import com.xgen.cloud.authz.shared._public.utils.MessageUtils;
import com.xgen.cloud.comments._private.constants.CommentConstants;
import com.xgen.cloud.comments._public.client.CommentClient;
import com.xgen.cloud.comments._public.view.CommentView;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.authz.proto.DeleteResourceRequest;
import com.xgen.cloud.services.comment.proto.Comment;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;

@Path("/orgs/{orgId}/comments")
@Singleton
public class OrganizationCommentsResource {
  private final ResourceClientProvider resourceClientProvider;
  private final CommentClient commentClient;

  @Inject
  public OrganizationCommentsResource(
      final ResourceClientProvider resourceClientProvider, final CommentClient commentClient) {
    this.resourceClientProvider = resourceClientProvider;
    this.commentClient = commentClient;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationCommentsResource.listOrgComments.GET")
  public Response listOrgComments(
      @Context final AppUser requestingUser,
      @Context final Organization organization,
      @Context final HttpServletRequest request)
      throws SvcException {
    return Response.ok()
        .entity(
            commentClient
                .getOrgComments(
                    organization.getId(),
                    DEFAULT_SKIP,
                    CommentConstants.DEFAULT_PAGE_SIZE,
                    getInitiatorAuth(request))
                .stream()
                .map(CommentView::from)
                .sorted(comparing(CommentView::getId).reversed())
                .collect(toList()))
        .build();
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationCommentsResource.createOrgComment.POST")
  public Response createOrgComment(
      @Context final AppUser requestingUser,
      @Context final Organization organization,
      @Context final HttpServletRequest request,
      final CommentView commentView)
      throws SvcException {
    final Comment comment =
        commentClient.createOrgComment(
            commentView.getContent(), organization.getId(), getInitiatorAuth(request));
    getResourceClient().addCommentToOrgResourceRelationship(comment.getId(), organization.getId());
    return Response.ok().entity(CommentView.from(comment)).build();
  }

  @DELETE
  @Path("/{commentId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrganizationCommentsResource.deleteOrgComment.DELETE")
  public Response deleteOrgComment(
      @Context final AppUser requestingUser,
      @Context final Organization organization,
      @Context final AuditInfo auditInfo,
      @Context final HttpServletRequest request,
      @PathParam("commentId") final String commentId)
      throws SvcException {
    commentClient.deleteComment(new ObjectId(commentId), getInitiatorAuth(request));
    getResourceClient()
        .deleteResource(
            DeleteResourceRequest.newBuilder()
                .setResource(MessageUtils.convertToOrgCommentMessage(commentId))
                .build(),
            auditInfo);
    return Response.noContent().build();
  }

  @Nullable
  private String getInitiatorAuth(final HttpServletRequest request) {
    return request.getHeader(INITIATOR_AUTH.value());
  }

  private ResourceClient getResourceClient() {
    return resourceClientProvider.get();
  }
}
