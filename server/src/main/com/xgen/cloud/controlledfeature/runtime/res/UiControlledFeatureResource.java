package com.xgen.cloud.controlledfeature.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.controlledfeature._public.model.ControlledFeature;
import com.xgen.cloud.controlledfeature._public.model.Policy;
import com.xgen.cloud.controlledfeature._public.svc.ControlledFeatureSvc;
import com.xgen.cloud.controlledfeature._public.view.PolicyListView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Set;
import java.util.stream.Collectors;

@Path("/group/{groupId}/controlledFeature")
@Singleton
public class UiControlledFeatureResource extends BaseResource {
  private final ControlledFeatureSvc _controlledFeatureSvc;

  @Inject
  public UiControlledFeatureResource(final ControlledFeatureSvc pControlledFeatureSvc) {
    _controlledFeatureSvc = pControlledFeatureSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.UiControlledFeatureResource.getControlledFeaturesForGroup.GET")
  public Response getControlledFeaturesForGroup(@Context final Group pGroup) {
    final ControlledFeature res = _controlledFeatureSvc.findByGroupIdOrInitialize(pGroup.getId());
    return Response.ok().entity(res).build();
  }

  @GET
  @Path("/policy")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.UiControlledFeatureResource.getPolicies.GET")
  public Response getPolicies(@Context final Group pGroup) {
    final ControlledFeature features =
        _controlledFeatureSvc.findByGroupIdOrInitialize(pGroup.getId());
    final PolicyListView res =
        new PolicyListView(
            features.getPolicies().stream()
                .map(p -> p.getPolicy().toString())
                .collect(Collectors.toList()));

    return Response.ok().entity(res).build();
  }

  @PATCH
  @Path("/policy")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_OWNER, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.UiControlledFeatureResource.appendPolicies.PATCH")
  public Response appendPolicies(@Context final Group pGroup, final PolicyListView pPolicyList) {
    if (pPolicyList == null
        || pPolicyList.policyList() == null
        || pPolicyList.policyList().isEmpty()) {
      return SimpleApiResponse.badRequest(CommonErrorCode.VALUE_NOT_SET).build();
    }

    final Policy[] policies =
        pPolicyList.policyList().stream().map(Policy::valueOf).toArray(Policy[]::new);
    final ControlledFeature features = _controlledFeatureSvc.appendPolicies(pGroup, policies);
    final PolicyListView res =
        new PolicyListView(
            features.getPolicies().stream()
                .map(p -> p.getPolicy().toString())
                .collect(Collectors.toList()));

    return Response.ok().entity(res).build();
  }

  @DELETE
  @Path("/policy")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_OWNER, groupSource = GroupSource.PATH)
  @Auth(endpointAction = "epa.project.UiControlledFeatureResource.removePolicies.DELETE")
  public Response removePolicies(
      @Context final Group pGroup, @QueryParam("policy") final Set<String> pPolicyList) {
    if (pPolicyList == null || pPolicyList.isEmpty()) {
      return SimpleApiResponse.badRequest(CommonErrorCode.VALUE_NOT_SET).build();
    }

    final Policy[] policies = pPolicyList.stream().map(Policy::valueOf).toArray(Policy[]::new);
    final ControlledFeature features = _controlledFeatureSvc.removePolicies(pGroup, policies);
    final PolicyListView res =
        new PolicyListView(
            features.getPolicies().stream()
                .map(p -> p.getPolicy().toString())
                .collect(Collectors.toList()));

    return Response.ok().entity(res).build();
  }
}
