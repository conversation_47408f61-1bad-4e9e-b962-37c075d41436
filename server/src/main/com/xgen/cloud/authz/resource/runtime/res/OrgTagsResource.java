package com.xgen.cloud.authz.resource.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.authz.resource._public.view.ui.AssociateResourceView;
import com.xgen.cloud.authz.resource._public.view.ui.InternalTagView;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.resourcetags._public.model.TagVisibility;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/orgs/{orgId}/tags")
@Singleton
public class OrgTagsResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(OrgTagsResource.class);

  /**
   * Returns a list of all tags under the provided organization. Tags will also include related
   * resources. This will be used to populate most UI elements for this project
   */
  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.ORG_OWNER},
      groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgTagsResource.getOrganizationTags.GET")
  public Response getOrganizationTags(
      @Context final AppUser currentUser, @PathParam("orgId") final ObjectId orgId) {
    List<InternalTagView> temporaryTags = createTags();
    return Response.ok(temporaryTags).build();
  }

  @POST
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.ORG_OWNER},
      groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgTagsResource.createOrganizationTag.POST")
  public Response createOrganizationTag(
      @Context final AppUser currentUser, final String tagString) {

    LOG.info("Would have created tag: {}", tagString);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @PUT
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.ORG_OWNER},
      groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgTagsResource.editOrganizationTag.PUT")
  public Response editOrganizationTag(
      @Context final AppUser currentUser,
      @PathParam("orgId") final ObjectId orgId,
      final InternalTagView tag) {
    LOG.info(
        "Would have edited organization tag. orgId: {}, tagId: {}, tagKey: {}",
        orgId,
        tag.getId(),
        tag.getKey());
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = {RoleSet.ORG_OWNER},
      groupSource = UiCall.GroupSource.NONE)
  @Path("/{tagId}")
  @Auth(endpointAction = "epa.organization.OrgTagsResource.deleteOrganizationTag.DELETE")
  public Response deleteOrganizationTag(
      @Context final AppUser currentUser,
      @PathParam("orgId") final ObjectId orgId,
      @PathParam("tagId") final ObjectId tagId) {
    LOG.info("Would have deleted organization tag. orgId: {}, tagId: {}", orgId, tagId);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  private List<InternalTagView> createTags() {
    ArrayList<InternalTagView> tags = new ArrayList<>();
    for (int i = 0; i < 25; i++) {
      tags.add(
          new InternalTagView(
              new ObjectId(i, i),
              TagVisibility.PUBLIC_TYPE.toString(),
              "key" + i,
              createAssociatedResources(i % 10 + 1),
              "description-" + i));
    }
    return tags;
  }

  private List<AssociateResourceView> createAssociatedResources(int count) {
    List<AssociateResourceView> values = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      List<String> associatedResources = new ArrayList<>();
      for (int j = 0; j < i * 20; j++) {
        associatedResources.add("id-" + j);
      }
      values.add(new AssociateResourceView("value-" + i, associatedResources));
    }
    return values;
  }
}
