package com.xgen.cloud.salesforce.runtime.res;

import static com.xgen.cloud.common.constants._public.model.res.PrivateApiPathConstants.API_PRIVATE_BILLING;

import com.sforce.soap.enterprise.sobject.Opportunity;
import com.sforce.soap.enterprise.sobject.OpportunityLineItem;
import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.salesforce._public.model.RawSalesforceOpportunity;
import com.xgen.cloud.sfdc._public.svc.SalesforceApiSvc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;

@Path(API_PRIVATE_BILLING + "/salesforce")
@RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
@Singleton
public class SalesforceApiResource {

  private final SalesforceApiSvc salesforceApiSvc;

  @Inject
  public SalesforceApiResource(SalesforceApiSvc salesforceApiSvc) {
    this.salesforceApiSvc = salesforceApiSvc;
  }

  @GET
  @Path("/getOppWithCustomQuery")
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      summary = "Fetch Opp with custom SFDC field list",
      description =
          "fetch an opportunity from SFDC with a custom list of fields to query from the SFOLIs and"
              + " parent opportunity",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successful response",
            content = @Content(mediaType = MediaType.APPLICATION_JSON))
      })
  @RolesAllowed(NAME.GLOBAL_BILLING_ADMIN)
  @Auth(endpointAction = "epa.global.SalesforceApiResource.getOpportunityWithCustomQuery.GET")
  public Response getOpportunityWithCustomQuery(
      @Parameter(
              name = "sfoliFields",
              required = true,
              description =
                  "comma separated list of fields to query from the OpportunityLineItems SFDC"
                      + " datastore. e.g. 'Id, OpportunityId, ProductCode, Prod_start_date__c' ")
          @QueryParam("sfoliFields")
          final String sfoliFields,
      @Parameter(
              name = "oppFields",
              required = true,
              description =
                  "comma separated list of fields to query from the Opportunity SFDC datastore."
                      + " e.g. 'Id, AccountId, CloseDate, MMS_License_Key__c'")
          @QueryParam("oppFields")
          final String oppFields,
      @Parameter(name = "activationCode", required = true) @QueryParam("activationCode")
          final String activationCode)
      throws Exception {
    final Opportunity rawSalesforceOpportunity =
        salesforceApiSvc.getOpportunityByLicenseKey(activationCode, oppFields, false);
    if (rawSalesforceOpportunity == null) {
      return Response.noContent()
          .entity("failed find opportunity with given activtionCode")
          .build();
    }
    final List<OpportunityLineItem> rawLineItems =
        salesforceApiSvc.getOpportunityLineItems(rawSalesforceOpportunity.getId(), sfoliFields);
    return Response.ok()
        .entity(
            RawSalesforceOpportunity.builder()
                .opportunity(rawSalesforceOpportunity)
                .lineItems(rawLineItems)
                .build()
                .toString())
        .build();
  }
}
