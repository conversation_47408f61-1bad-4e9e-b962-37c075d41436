load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    deny_warnings = True,
    visibility = [
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/billingimport/common/_public/svc",
        "//server/src/main/com/xgen/cloud/billingimport/common/_public/view",
        "//server/src/main/com/xgen/cloud/common/access",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_validation_jakarta_validation_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
    ],
)
