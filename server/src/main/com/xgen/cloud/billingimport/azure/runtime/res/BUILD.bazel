load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    deny_warnings = True,
    visibility = [
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/billingimport/azure/_private/model",
        "//server/src/main/com/xgen/cloud/billingimport/azure/_private/svc",
        "//server/src/main/com/xgen/cloud/billingimport/azure/_private/view",
        "//server/src/main/com/xgen/cloud/billingimport/azure/_public/svc",
        "//server/src/main/com/xgen/cloud/billingimport/azure/_public/view",
        "//server/src/main/com/xgen/cloud/billingimport/common",
        "//server/src/main/com/xgen/cloud/billingplatform/common",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "//server/src/main/com/xgen/svc/mms/svc/billing",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_validation_jakarta_validation_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_json_json",
        "@maven//:org_mongodb_bson",
    ],
)
