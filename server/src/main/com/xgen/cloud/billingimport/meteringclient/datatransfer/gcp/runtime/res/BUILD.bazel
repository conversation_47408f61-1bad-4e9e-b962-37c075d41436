load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/billingimport/meteringclient/common",
        "//server/src/main/com/xgen/cloud/billingimport/meteringclient/datatransfer/gcp/_private/svc",
        "//server/src/main/com/xgen/cloud/billingimport/meteringclient/datatransfer/gcp/_public/svc",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
    ],
)
