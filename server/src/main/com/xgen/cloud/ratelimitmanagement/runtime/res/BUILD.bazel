load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    deny_warnings = True,
    enable_nullaway = True,
    visibility = [
        "//server/src/main/com/xgen:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/api/res:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/ratelimit/api",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/common/versioning",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/ratelimitmanagement/runtime/model",
        "//server/src/main/com/xgen/cloud/ratelimitmanagement/runtime/svc",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
