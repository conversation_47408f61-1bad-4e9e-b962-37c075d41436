package com.xgen.cloud.partners.vercel.billing.runtime.res;

import static com.xgen.cloud.common.constants._public.model.res.PrivateApiPathConstants.API_PRIVATE_BILLING;

import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoicePaymentSyncRequest;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoicePaymentSyncResult;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInvoiceSynchronizer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path(API_PRIVATE_BILLING)
@RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
@Singleton
public final class VercelPrivateBillingResource {
  private final VercelInvoiceSynchronizer vercelInvoiceSynchronizer;

  @Inject
  public VercelPrivateBillingResource(VercelInvoiceSynchronizer vercelInvoiceSynchronizer) {
    this.vercelInvoiceSynchronizer = vercelInvoiceSynchronizer;
  }

  @POST
  @Path("/partners/vercel/syncInvoiceAndPayment")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      summary = "Synchronize Vercel invoice and payment",
      description =
          "Synchronizes a Vercel invoice and payment by the payment ID."
              + "This endpoint is intended for support use to manually sync a payment and invoice.",
      requestBody =
          @RequestBody(
              description = "Request body for synchronizing a Vercel invoice and payment.",
              required = true,
              content =
                  @Content(
                      schema = @Schema(implementation = VercelInvoicePaymentSyncRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successful response",
            content =
                @Content(schema = @Schema(implementation = VercelInvoicePaymentSyncResult.class))),
        @ApiResponse(responseCode = "400", description = "Error: Bad request"),
        @ApiResponse(responseCode = "401", description = "Error: Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Error: Unauthorized")
      })
  @Auth(endpointAction = "epa.global.VercelPrivateBillingResource.synchronizePaymentAndInvoice.POST")
  public Response synchronizePaymentAndInvoice(
      @RequestBody @NotNull @Valid VercelInvoicePaymentSyncRequest request) {
    VercelInvoicePaymentSyncResult result = vercelInvoiceSynchronizer.synchronize(request).block();
    return Response.ok(result).build();
  }
}
