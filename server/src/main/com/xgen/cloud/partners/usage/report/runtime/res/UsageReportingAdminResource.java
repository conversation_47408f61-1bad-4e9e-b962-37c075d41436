package com.xgen.cloud.partners.usage.report.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.partners.usage.report._public.svc.AgreementSearchAdminSvc;
import com.xgen.cloud.partners.usage.report._public.svc.ReportingFailuresAdminSvc;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/admin/usagereporting")
public class UsageReportingAdminResource {

  private final ReportingFailuresAdminSvc reportingFailuresAdminSvc;
  private final AgreementSearchAdminSvc agreementSearchAdminSvc;

  @Inject
  public UsageReportingAdminResource(
      ReportingFailuresAdminSvc reportingFailuresAdminSvc,
      AgreementSearchAdminSvc agreementSearchAdminSvc) {
    this.reportingFailuresAdminSvc = reportingFailuresAdminSvc;
    this.agreementSearchAdminSvc = agreementSearchAdminSvc;
  }

  @GET
  @Path("/failures")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Operation(
      summary = "Gets Marketplace Usage Reporting Failures",
      description =
          "Gets Marketplace Usage Reporting Failures for a given Credit. This is intended to"
              + " support displaying details about failures in the Admin Page")
  @Auth(endpointAction = "epa.global.UsageReportingAdminResource.getUsageReportingFailures.GET")
  public Response getUsageReportingFailures() {
    return Response.ok().entity(reportingFailuresAdminSvc.getReportingFailures()).build();
  }

  @GET
  @Path("/agreement")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Operation(
      summary = "Gets AWS Marketplace Agreement",
      description =
          "Gets AWS Marketplace Agreement for a given Activation Code. This is intended to"
              + " support investigations about failures in the Admin Page")
  @Auth(endpointAction = "epa.global.UsageReportingAdminResource.getAgreementByActivationCode.GET")
  public Response getAgreementByActivationCode(@QueryParam("activationCode") String activationCode)
      throws SvcException {
    return Response.ok()
        .entity(agreementSearchAdminSvc.getAgreementByActivationCode(activationCode))
        .build();
  }
}
