package com.xgen.cloud.partners.common.runtime.res;

import static net.logstash.logback.argument.StructuredArguments.entries;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.constants._public.model.partners.PartnerType;
import com.xgen.cloud.partners.common._public.svc.PartnerSelfServeApiSvcProvider;
import com.xgen.cloud.partners.common._public.view.request.MarketplaceLinkAttemptRequestBody;
import com.xgen.cloud.partners.common._public.view.request.PartnerCustomerLinkOrgRequestBody;
import com.xgen.cloud.partners.common._public.view.response.ListMarketplaceLinkAttemptsResponse;
import com.xgen.cloud.partners.common._public.view.response.MarketplaceLinkAttemptResponse;
import com.xgen.cloud.partners.link._public.model.MarketplaceLinkAttempt;
import com.xgen.cloud.partners.link._public.svc.MarketplaceLinkAttemptSvc;
import com.xgen.cloud.partners.link._public.view.request.BatchDeleteMarketplaceLinkAttemptsRequest;
import com.xgen.cloud.partners.link._public.view.request.CreateMarketplaceLinkAttemptRequest;
import com.xgen.cloud.partners.link._public.view.request.ListMarketplaceLinkAttemptFilter;
import com.xgen.cloud.partners.link._public.view.request.ListMarketplaceLinkAttemptsRequest;
import com.xgen.cloud.partners.link._public.view.response.ListResult;
import com.xgen.cloud.payments.standalone.partners.common._public.constant.PartnerCustomerErrorCode;
import com.xgen.cloud.payments.standalone.partners.common._public.exception.UnsupportedMarketplaceException;
import com.xgen.cloud.payments.standalone.partners.common._public.svc.CustomerSubscriptionStatusSvc;
import com.xgen.cloud.payments.standalone.partners.common._public.svc.MarketplaceUrlSvcImpl;
import com.xgen.cloud.payments.standalone.partners.common._public.view.response.PartnerApiError;
import com.xgen.cloud.payments.standalone.partners.common._public.view.response.PartnerCustomerLinkOrgResult;
import com.xgen.cloud.payments.standalone.partners.common._public.view.response.PartnerCustomerLinkOrgStatusResult;
import com.xgen.cloud.payments.standalone.partners.common._public.view.response.PartnerCustomerSubscriptionStatusResult;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiError;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Partner Self-Serve Api Resources.
 *
 * <p>Currently only AWS is supported. A factory using the PartnerType is a logical next step for
 * supporting other partners.
 */
@Path("/billing/partners")
public final class PartnerSelfServeResource {

  private static final Logger LOG = LoggerFactory.getLogger(PartnerSelfServeResource.class);

  private final PartnerSelfServeApiSvcProvider partnerSelfServeApiSvcProvider;
  private final CustomerSubscriptionStatusSvc customerSubscriptionStatusSvc;
  private final MarketplaceLinkAttemptSvc marketplaceLinkAttemptSvc;
  private final MarketplaceUrlSvcImpl marketplaceUrlSvc;

  @Inject
  public PartnerSelfServeResource(
      PartnerSelfServeApiSvcProvider partnerSelfServeApiSvcProvider,
      CustomerSubscriptionStatusSvc customerSubscriptionStatusSvc,
      MarketplaceLinkAttemptSvc marketplaceLinkAttemptSvc,
      MarketplaceUrlSvcImpl marketplaceUrlSvc) {
    this.partnerSelfServeApiSvcProvider = partnerSelfServeApiSvcProvider;
    this.customerSubscriptionStatusSvc = customerSubscriptionStatusSvc;
    this.marketplaceLinkAttemptSvc = marketplaceLinkAttemptSvc;
    this.marketplaceUrlSvc = marketplaceUrlSvc;
  }

  /**
   * POST /billing/partners/{partnerType}/customer/linkOrg
   *
   * @param httpServletRequest (required)
   * @param appUser (required)
   * @param partnerType (required)
   * @param requestBody Request body for linking a Partner Customer and an Organization. (required)
   * @return Response body for a request to link an org to a customer.
   */
  @POST
  @Path("/{partnerType}/customer/linkOrg")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      summary = "Link org to customer",
      description =
          "Links a Partner Customer to an Atlas Organization.  Authenticated endpoint that also"
              + " validates the User's Roles.",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Response body for a request to link an org to a customer.",
            content =
                @Content(schema = @Schema(implementation = PartnerCustomerLinkOrgResult.class))),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "401",
            description = "401 Unauthorized error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "403",
            description = "403 Forbidden error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Internal Server error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "503",
            description = "503 Service Unavailable error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class)))
      })
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response customerLinkOrg(
      @Context HttpServletRequest httpServletRequest,
      @Context AppUser appUser,
      @Context AuditInfo auditInfo,
      @Parameter(description = "", required = true) @PathParam("partnerType") @NotNull @Valid
          PartnerType partnerType,
      @RequestBody(
              description =
                  "Request body for linking a Partner Customer and an Atlas Organization.",
              required = true)
          @NotNull
          @Valid
          PartnerCustomerLinkOrgRequestBody requestBody) {
    if (appUser.getMarketplaceRegistrationData() == null) {
      return missingRegistrationDataError();
    }

    return partnerSelfServeApiSvcProvider
        .get(partnerType)
        .customerLinkOrg(httpServletRequest, appUser, requestBody, auditInfo);
  }

  /**
   * GET /billing/partners/{partnerType}/customer/linkOrg/status
   *
   * @param httpServletRequest (required)
   * @param appUser (required)
   * @param partnerType (required)
   * @return Response body for a request to check org link status for a customer.
   */
  @GET
  @Path("/{partnerType}/customer/linkOrg/status")
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      summary = "Check link status of customer orgs",
      description =
          "Returns a list of the organizationIds and their linkable status for a Partner Customer."
              + "  Authenticated endpoint that also validates the User's Roles.",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Response body for a request to check org link status for a customer.",
            content =
                @Content(
                    schema = @Schema(implementation = PartnerCustomerLinkOrgStatusResult.class))),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "401",
            description = "401 Unauthorized error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "403",
            description = "403 Forbidden error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Internal Server error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "503",
            description = "503 Service Unavailable error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class)))
      })
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response customerLinkOrgStatus(
      @Context HttpServletRequest httpServletRequest,
      @Context AppUser appUser,
      @Parameter(description = "", required = true) @PathParam("partnerType") @NotNull @Valid
          PartnerType partnerType) {

    return partnerSelfServeApiSvcProvider
        .get(partnerType)
        .customerLinkOrgStatus(httpServletRequest, appUser);
  }

  /**
   * GET /billing/partners/customer/subscription/status
   *
   * @param httpServletRequest (required)
   * @param organizationId (required)
   * @return Response body for a request to check subscription status for a customer.
   */
  @GET
  @Path("/customer/subscription/status")
  @Produces({MediaType.APPLICATION_JSON})
  @Operation(
      summary = "Get subscription status",
      description = "Returns a summary detail of a customer's partner subscription status.",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description =
                "Response body for a request to check subscription status for a customer.",
            content =
                @Content(
                    schema =
                        @Schema(implementation = PartnerCustomerSubscriptionStatusResult.class))),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "401",
            description = "401 Unauthorized error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "403",
            description = "403 Forbidden error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "404",
            description =
                "404 Not Found error response body. Returned if provided invalid ObjectId.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Internal Server error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "503",
            description = "503 Service Unavailable error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class)))
      })
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response customerSubscriptionStatus(
      @Context HttpServletRequest httpServletRequest,
      @Parameter(description = "", required = true) @QueryParam("organizationId") @NotNull @Valid
          ObjectId organizationId) {
    return customerSubscriptionStatusSvc.customerSubscriptionStatus(
        httpServletRequest, organizationId);
  }

  /**
   * POST /billing/partners/{partnerType}/customer/marketplaceLinkAttempt:
   *
   * @param httpServletRequest (required)
   * @param partnerType (required)
   * @param requestBody Request body for creating a MarketplaceLinkAttempt. (required)
   * @return Response body for a request to create a MarketplaceLinkAttempt.
   */
  @POST
  @Path("/{partnerType}/customer/marketplaceLinkAttempt")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @Operation(
      summary = "Create a marketplace link attempt for an organization.",
      responses = {
        @ApiResponse(
            responseCode = "201",
            description = "201 Successfully created a marketplace link attempt",
            content =
                @Content(schema = @Schema(implementation = MarketplaceLinkAttemptResponse.class))),
        @ApiResponse(
            responseCode = "400",
            description =
                "400 Unable to process request i.e. organization is already linked, organization id"
                    + " is invalid, etc",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "401",
            description = "401 Not authenticated.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "403",
            description = "403 Not authorized.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "404",
            description = "404 Partner type not found.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "409",
            description = "409 Conflicting marketplace link attempt.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Server error.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
      })
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response createMarketplaceLinkAttempt(
      @Context HttpServletRequest httpServletRequest,
      @Context AppUser appUser,
      @Parameter(description = "", required = true) @PathParam("partnerType") @NotNull @Valid
          PartnerType partnerType,
      @RequestBody(
              description = "Request body for creating a MarketplaceLinkAttempt.",
              required = true)
          @NotNull
          @Valid
          MarketplaceLinkAttemptRequestBody requestBody) {
    return partnerSelfServeApiSvcProvider
        .get(partnerType)
        .createMarketplaceLinkAttempt(
            httpServletRequest,
            appUser,
            new CreateMarketplaceLinkAttemptRequest(
                appUser.getId(), requestBody.getOrgId(), partnerType, requestBody.getLinkSource()));
  }

  /**
   * GET /billing/partners/customer/marketplaceLinkAttempt
   *
   * @param httpServletRequest (required)
   * @return Response body for a request to get the user's current marketplace link attempt.
   */
  @GET
  @Path("/customer/marketplaceLinkAttempt")
  @Produces({MediaType.APPLICATION_JSON})
  @Operation(
      summary = "Gets the user's current marketplace link attempt",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "200 Active marketplace link attempt found.",
            content =
                @Content(schema = @Schema(implementation = MarketplaceLinkAttemptResponse.class))),
        @ApiResponse(
            responseCode = "401",
            description = "401 Not authenticated.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "403",
            description = "403 Not authorized.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "404",
            description = "404 No active link attempt found.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
      })
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.PartnerSelfServeResource.getMarketplaceLinkAttempt.GET")
  public Response getMarketplaceLinkAttempt(
      @Context HttpServletRequest httpServletRequest, @Context AppUser appUser) {
    Optional<MarketplaceLinkAttempt> marketplaceLinkAttempt =
        marketplaceLinkAttemptSvc.findByUserId(appUser.getId());

    if (marketplaceLinkAttempt.isEmpty()) {
      return missingMarketplaceLinkAttemptError();
    }

    String marketplaceUrl;
    MarketplaceLinkAttempt currMarketplaceLinkAttempt = marketplaceLinkAttempt.get();
    try {
      marketplaceUrl =
          marketplaceUrlSvc.getMarketplaceUrl(currMarketplaceLinkAttempt.getPartnerType());
      return Response.ok(
              MarketplaceLinkAttemptResponse.builder()
                  .partnerType(currMarketplaceLinkAttempt.getPartnerType())
                  .id(currMarketplaceLinkAttempt.getId())
                  .userId(currMarketplaceLinkAttempt.getUserId())
                  .orgId(currMarketplaceLinkAttempt.getOrgId())
                  .linkSource(currMarketplaceLinkAttempt.getLinkSource())
                  .marketplaceUrl(marketplaceUrl)
                  .createdAt(
                      currMarketplaceLinkAttempt
                          .getCreatedAt()
                          .atZone(ZoneId.systemDefault())
                          .withZoneSameInstant(ZoneId.of("UTC")))
                  .updatedAt(
                      currMarketplaceLinkAttempt
                          .getUpdatedAt()
                          .atZone(ZoneId.systemDefault())
                          .withZoneSameInstant(ZoneId.of("UTC")))
                  .build())
          .build();
    } catch (UnsupportedMarketplaceException e) {
      return unsupportedMarketplaceUrlError();
    }
  }

  /**
   * GET /billing/partners/marketplaceLinkAttempts
   *
   * @param httpServletRequest (required)
   * @param ids A list of marketplace link attempt IDs used to filter marketplace link attempts
   * @param userIds A list of user IDs used to filter marketplace link attempts
   * @param orgIds A list of org IDs used to filter marketplace link attempts
   * @param limit The maximum number of documents that can be returned in a page
   * @param cursor A pagination cursor for retrieving the current page of results.
   * @return Response body for a request to list marketplace link attempts
   */
  @GET
  @Path("/marketplaceLinkAttempts")
  @UiCall(roles = RoleSet.GLOBAL_READ_ONLY, groupSource = GroupSource.NONE)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      description = "List marketplace link attempts.",
      summary = "Retrieve a list of marketplace link attempts with optional filtering.",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "200 Successfully listed marketplace link attempts",
            content =
                @Content(
                    schema = @Schema(implementation = ListMarketplaceLinkAttemptsResponse.class))),
        @ApiResponse(
            responseCode = "400",
            description = "400 Invalid request.",
            content = @Content(schema = @Schema(implementation = ApiError.class))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Server error.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
      })
  @Auth(endpointAction = "epa.global.PartnerSelfServeResource.listMarketplaceLinkAttempts.GET")
  public Response listMarketplaceLinkAttempts(
      @Context HttpServletRequest httpServletRequest,
      @Parameter(
              description =
                  "A list of marketplace link attempt IDs used to filter marketplace link"
                      + " attempts.")
          @QueryParam("ids")
          @Nullable
          @Size(max = ListMarketplaceLinkAttemptFilter.MAX_FILTER_SIZE)
          @Valid
          List<ObjectId> ids,
      @Parameter(description = "A list of user IDs used to filter marketplace link attempts.")
          @QueryParam("userIds")
          @Nullable
          @Size(max = ListMarketplaceLinkAttemptFilter.MAX_FILTER_SIZE)
          @Valid
          List<ObjectId> userIds,
      @Parameter(
              description = "A list of organization IDs used to filter marketplace link attempts.")
          @QueryParam("orgIds")
          @Nullable
          @Size(max = ListMarketplaceLinkAttemptFilter.MAX_FILTER_SIZE)
          @Valid
          List<ObjectId> orgIds,
      @Parameter(
              description =
                  "Specifies the maximum number of documents to return in the response. This"
                      + " parameter is optional.")
          @QueryParam("limit")
          @Nullable
          @Positive
          @Max(ListMarketplaceLinkAttemptsRequest.MAX_PAGINATION_LIMIT)
          Integer limit,
      @Parameter(
              description =
                  "A pagination cursor for retrieving the current page of results. Optional.")
          @QueryParam("cursor")
          @Nullable
          String cursor) {
    try {
      ListResult<MarketplaceLinkAttempt> results =
          marketplaceLinkAttemptSvc.list(
              new ListMarketplaceLinkAttemptsRequest(
                  new ListMarketplaceLinkAttemptFilter(ids, userIds, orgIds), limit, cursor));

      return Response.ok()
          .entity(
              new ListMarketplaceLinkAttemptsResponse(
                  results.data().stream()
                      .map(
                          marketplaceLinkAttempt ->
                              MarketplaceLinkAttemptResponse.builder()
                                  .id(marketplaceLinkAttempt.getId())
                                  .orgId(marketplaceLinkAttempt.getOrgId())
                                  .userId(marketplaceLinkAttempt.getUserId())
                                  .partnerType(marketplaceLinkAttempt.getPartnerType())
                                  .linkSource(marketplaceLinkAttempt.getLinkSource())
                                  .marketplaceUrl(
                                      marketplaceUrlSvc.getMarketplaceUrl(
                                          marketplaceLinkAttempt.getPartnerType()))
                                  .createdAt(
                                      marketplaceLinkAttempt
                                          .getCreatedAt()
                                          .atZone(ZoneId.systemDefault())
                                          .withZoneSameInstant(ZoneId.of("UTC")))
                                  .updatedAt(
                                      marketplaceLinkAttempt
                                          .getCreatedAt()
                                          .atZone(ZoneId.systemDefault())
                                          .withZoneSameInstant(ZoneId.of("UTC")))
                                  .build())
                      .toList(),
                  results.nextCursor()))
          .build();
    } catch (ConstraintViolationException e) {
      // Allow ConstraintViolationExceptionMapper to handle these exceptions
      throw e;
    } catch (Exception e) {
      PartnerApiError partnerApiError = PartnerApiError.of(PartnerCustomerErrorCode.UNKNOWN_ERROR);

      return Response.status(partnerApiError.getStatusCode()).entity(partnerApiError).build();
    }
  }

  /**
   * @param httpServletRequest (required)
   * @param marketplaceLinkAttemptId (required) The unique identifier of the marketplace link
   *     attempt to be deleted.
   * @return
   */
  @DELETE
  @UiCall(
      roles = {RoleSet.GLOBAL_EXPERIMENT_ASSIGNMENT_ADMIN, RoleSet.GLOBAL_BILLING_ADMIN},
      groupSource = GroupSource.NONE)
  @Path("/marketplaceLinkAttempts/{marketplaceLinkAttemptId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      description = "Delete a marketplace link attempt.",
      summary = "Delete a marketplace link attempt by id.",
      responses = {
        @ApiResponse(responseCode = "204", description = "204 deleted marketplace link attempt"),
        @ApiResponse(
            responseCode = "500",
            description = "500 Server error.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
      })
  @Auth(endpointAction = "epa.global.PartnerSelfServeResource.deleteMarketplaceLinkAttempt.DELETE")
  public Response deleteMarketplaceLinkAttempt(
      @Context HttpServletRequest httpServletRequest,
      @Parameter(
              description = "The unique identifier of the MarketplaceLinkAttempt to be deleted.",
              required = true)
          @PathParam("marketplaceLinkAttemptId")
          @NotNull
          @Valid
          ObjectId marketplaceLinkAttemptId) {
    marketplaceLinkAttemptSvc.delete(marketplaceLinkAttemptId);
    return Response.status(Response.Status.NO_CONTENT).build();
  }

  /**
   * @param httpServletRequest (required)
   * @param request (required)
   * @return
   */
  @POST
  @UiCall(
      roles = {RoleSet.GLOBAL_EXPERIMENT_ASSIGNMENT_ADMIN, RoleSet.GLOBAL_BILLING_ADMIN},
      groupSource = GroupSource.NONE)
  @Path("/marketplaceLinkAttempts/batchDelete")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      summary = "Batch delete marketplace link attempts.",
      description =
          "This endpoint permits the batch deletion of marketplace link attempts. "
              + "Exactly one of the filters (`ids`, `orgIds`, or `userIds`) should be provided "
              + "to specify which attempts to delete. Providing none or more than one will result "
              + "in an error.",
      requestBody =
          @RequestBody(
              description =
                  "Request body for batch deleting marketplace link attempts. "
                      + "It must contain only one of `ids`, `orgIds`, or `userIds`.",
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation = BatchDeleteMarketplaceLinkAttemptsRequest.class))),
      responses = {
        @ApiResponse(responseCode = "204", description = "204 deleted marketplace link attempts."),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request.",
            content = @Content(schema = @Schema(oneOf = {PartnerApiError.class, ApiError.class}))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Server error.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
      })
  @Auth(endpointAction = "epa.global.PartnerSelfServeResource.batchDeleteMarketplaceLinkAttempts.POST")
  public Response batchDeleteMarketplaceLinkAttempts(
      @Context HttpServletRequest httpServletRequest,
      @Parameter(
              description = "Request body for batch delete of marketplace link attempts",
              required = true)
          @RequestBody(
              description = "Request body for batch deleting marketplace link attempts.",
              required = true)
          @NotNull
          @Valid
          BatchDeleteMarketplaceLinkAttemptsRequest request) {

    try {
      marketplaceLinkAttemptSvc.batchDelete(request);
    } catch (ConstraintViolationException e) {
      // Allow ConstraintViolationExceptionMapper to handle these exceptions
      throw e;
    } catch (Exception e) {
      PartnerApiError partnerApiError = PartnerApiError.of(PartnerCustomerErrorCode.UNKNOWN_ERROR);

      return Response.status(partnerApiError.getStatusCode()).entity(partnerApiError).build();
    }

    return Response.status(Response.Status.NO_CONTENT).build();
  }

  private static Response missingMarketplaceLinkAttemptError() {
    PartnerApiError apiError =
        PartnerApiError.of(PartnerCustomerErrorCode.MISSING_MARKETPLACE_LINK_ATTEMPT);
    return Response.status(apiError.getStatusCode()).entity(apiError).build();
  }

  private static Response unsupportedMarketplaceUrlError() {
    PartnerApiError apiError =
        PartnerApiError.of(PartnerCustomerErrorCode.UNSUPPORTED_MARKETPLACE_URL);
    Map<String, ?> errorDetails = Map.of("PartnerApiError", apiError);
    LOG.error("Failed to generate and validate marketplace URL. {}", entries(errorDetails));
    return Response.status(apiError.getStatusCode()).entity(apiError).build();
  }

  private static Response missingRegistrationDataError() {
    PartnerApiError apiError =
        PartnerApiError.of(PartnerCustomerErrorCode.MISSING_REGISTRATION_DATA);
    Map<String, ?> errorDetails = Map.of("PartnerApiError", apiError);
    LOG.error("Missing registration data to link org to customer. {}", entries(errorDetails));
    return Response.status(apiError.getStatusCode()).entity(apiError).build();
  }
}
