load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "mapper",
    srcs = glob(["*.java"]),
    deny_warnings = True,
    include_default_deps = False,
    plugins = [
        "//third_party:mapstruct_plugin",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/apiserviceaccount/_public/view",
        "//server/src/main/com/xgen/cloud/common/authn",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/user",
        "@maven//:com_google_errorprone_error_prone_annotations",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:org_mapstruct_mapstruct",
        "@maven//:org_mongodb_bson",
    ],
)
