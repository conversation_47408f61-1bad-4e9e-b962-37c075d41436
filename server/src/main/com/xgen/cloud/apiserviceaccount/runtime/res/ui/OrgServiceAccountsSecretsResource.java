package com.xgen.cloud.apiserviceaccount.runtime.res.ui;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiserviceaccount._private.utils.RequestBodyLoggingUtil;
import com.xgen.cloud.apiserviceaccount._public.svc.ServiceAccountSecretSvc;
import com.xgen.cloud.apiserviceaccount._public.view.secret.ServiceAccountSecretRequestView;
import com.xgen.cloud.apiserviceaccount.runtime.res.api_2024_08_05.ApiAtlasBaseServiceAccountSecretResource;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import java.lang.invoke.MethodHandles;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/orgs/{orgId}/serviceAccounts/{clientId}/secrets")
@Singleton
public class OrgServiceAccountsSecretsResource extends ApiAtlasBaseServiceAccountSecretResource {
  private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

  @Inject
  public OrgServiceAccountsSecretsResource(
      final AppSettings appSettings, final ServiceAccountSecretSvc serviceAccountSecretSvc) {
    super(appSettings, serviceAccountSecretSvc);
  }

  @POST
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsSecretsResource.createServiceAccountSecret.POST")
  public Response createServiceAccountSecret(
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      final ServiceAccountSecretRequestView request)
      throws SvcException {
    RequestBodyLoggingUtil.logServiceAccountSecretRequestView(LOG, request);
    return super.createServiceAccountSecret(org, auditInfo, clientId, false, request);
  }

  @DELETE
  @Path("/{secretId}")
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsSecretsResource.deleteServiceAccountSecret.DELETE")
  public Response deleteServiceAccountSecret(
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      @PathParam("secretId") final ObjectId secretId)
      throws SvcException {
    return super.deleteServiceAccountSecret(
        org.getId(), null, auditInfo, clientId, secretId, false);
  }
}
