package com.xgen.cloud.apiserviceaccount.runtime.res.ui;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiserviceaccount._private.utils.RequestBodyLoggingUtil;
import com.xgen.cloud.apiserviceaccount._private.utils.ServiceAccountSvcExceptionHandler;
import com.xgen.cloud.apiserviceaccount._public.svc.CreateServiceAccountSvc;
import com.xgen.cloud.apiserviceaccount._public.svc.ServiceAccountAuditSvc;
import com.xgen.cloud.apiserviceaccount._public.svc.ServiceAccountSvc;
import com.xgen.cloud.apiserviceaccount._public.svc.UpdateServiceAccountSvc;
import com.xgen.cloud.apiserviceaccount._public.view.org.OrgServiceAccountRequestView;
import com.xgen.cloud.apiserviceaccount._public.view.org.OrgServiceAccountUpdateRequestView;
import com.xgen.cloud.apiserviceaccount._public.view.org.OrgServiceAccountView;
import com.xgen.cloud.apiserviceaccount._public.view.org.ServiceAccountGroupView;
import com.xgen.cloud.apiserviceaccount._public.view.ui.ServiceAccountUiAccessListInheritanceView;
import com.xgen.cloud.apiserviceaccount.runtime.res.api_2024_08_05.ApiAtlasBaseServiceAccountsResource;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.Valid;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;
import java.lang.invoke.MethodHandles;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/orgs/{orgId}/serviceAccounts")
@Singleton
public class OrgServiceAccountsResource extends ApiAtlasBaseServiceAccountsResource {
  private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

  private final ConfigLimitSvc configLimitSvc;
  private final ServiceAccountSvc serviceAccountSvc;

  @Inject
  public OrgServiceAccountsResource(
      final AppSettings appSettings,
      final ConfigLimitSvc configLimitSvc,
      final ServiceAccountSvc serviceAccountSvc,
      final CreateServiceAccountSvc createServiceAccountSvc,
      final UpdateServiceAccountSvc updateServiceAccountSvc,
      final ServiceAccountAuditSvc auditSvc) {
    super(appSettings, createServiceAccountSvc, updateServiceAccountSvc);
    this.configLimitSvc = configLimitSvc;
    this.serviceAccountSvc = serviceAccountSvc;
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.listServiceAccounts.GET")
  public Response listServiceAccounts(@Context final Organization org) throws SvcException {
    try {
      List<OrgServiceAccountView> serviceAccounts =
          serviceAccountSvc.listServiceAccountsForOrg(org.getId());
      return Response.ok().entity(serviceAccounts).build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, null);
    }
  }

  @GET
  @Path("/{clientId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.getServiceAccount.GET")
  public Response getServiceAccount(
      @Context final Organization org,
      @PathParam("clientId") final String clientId,
      @QueryParam("envelope") final Boolean envelope)
      throws SvcException {
    try {
      OrgServiceAccountView response =
          serviceAccountSvc.getServiceAccountForOrg(org.getId(), clientId);
      return Response.ok().entity(response).build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, envelope, clientId);
    }
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.createServiceAccount.POST")
  public Response createServiceAccount(
      @Context UriInfo uriInfo,
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @Valid final OrgServiceAccountRequestView request)
      throws SvcException {
    RequestBodyLoggingUtil.logOrgServiceAccountRequestView(LOG, request);

    validateRolesAreUnique(false, request.roles());
    configLimitSvc.validateMaxServiceAccountsPerOrgForApi(1, org.getId(), false);
    final OrgServiceAccountView response = createOrgServiceAccount(org, false, request, auditInfo);
    return Response.created(uriInfo.getAbsolutePathBuilder().path(response.clientId()).build())
        .entity(response)
        .build();
  }

  @PATCH
  @Path("/{clientId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.updateServiceAccount.PATCH")
  public Response updateServiceAccount(
      @Context Organization org,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      @Valid final OrgServiceAccountUpdateRequestView serviceAccountUpdate)
      throws SvcException {
    RequestBodyLoggingUtil.logOrgServiceAccountUpdateRequestView(LOG, serviceAccountUpdate);

    validateUpdateServiceAccountRequest(serviceAccountUpdate, false);
    final OrgServiceAccountView response =
        updateOrgServiceAccount(org, clientId, false, serviceAccountUpdate, auditInfo);
    return Response.ok(response).build();
  }

  @GET
  @Path("/{clientId}/groups")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.listServiceAccountGroups.GET")
  public Response listServiceAccountGroups(
      @Context Organization org, @PathParam("clientId") final String clientId) throws SvcException {
    try {
      List<ServiceAccountGroupView> serviceAccountGroups =
          serviceAccountSvc.listGroupIdsForServiceAccount(org.getId(), clientId);
      return Response.ok().entity(serviceAccountGroups).build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, clientId);
    }
  }

  @DELETE
  @Path("/{clientId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.deleteServiceAccount.DELETE")
  public Response deleteServiceAccount(
      @Context Organization org,
      @Context final AppUser user,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId)
      throws SvcException {
    try {
      serviceAccountSvc.deleteServiceAccount(user, org, clientId, auditInfo);
      return Response.noContent().build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, clientId);
    }
  }

  @PUT
  @Path("/{clientId}/orgUiAccessListInheritance")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.updateOrgUiAccessListInheritance.PUT")
  public Response updateOrgUiAccessListInheritance(
      @Context final Organization organization,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      @FormParam("shouldApplyOrgUiAccessListForApi") final boolean shouldApplyOrgUiAccessListForApi)
      throws SvcException {
    try {
      serviceAccountSvc.updateOrgUiAccessListInheritance(
          clientId, shouldApplyOrgUiAccessListForApi, organization.getId(), null, auditInfo);
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, false, clientId);
    }
    return Response.ok().build();
  }

  @GET
  @Path("/{clientId}/shouldApplyOrgUiAccessListForApi")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsResource.getOrgUiAccessListInheritance.GET")
  public Response getOrgUiAccessListInheritance(
      @Context final Organization org, @PathParam("clientId") final String clientId)
      throws SvcException {
    boolean shouldApplyOrgUiAccessListForApi;
    try {
      shouldApplyOrgUiAccessListForApi =
          serviceAccountSvc.getOrgUiAccessListInheritance(org.getId(), null, clientId);
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, clientId);
    }
    return Response.ok()
        .entity(new ServiceAccountUiAccessListInheritanceView(shouldApplyOrgUiAccessListForApi))
        .build();
  }
}
