package com.xgen.cloud.apiserviceaccount.runtime.res.api_2024_08_05;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.VERB_OVERRIDE;

import com.mongodb.client.MongoCursor;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiserviceaccount._private.config.ServiceAccountOpenApiConst;
import com.xgen.cloud.apiserviceaccount._private.mapper.ServiceAccountIPAccessListEntryViewMapper;
import com.xgen.cloud.apiserviceaccount._private.utils.RequestBodyLoggingUtil;
import com.xgen.cloud.apiserviceaccount._private.utils.ServiceAccountSvcExceptionHandler;
import com.xgen.cloud.apiserviceaccount._public.svc.ServiceAccountAccessListSvc;
import com.xgen.cloud.apiserviceaccount._public.view.ipaccess.PaginatedServiceAccountIPAccessListView;
import com.xgen.cloud.apiserviceaccount._public.view.ipaccess.ServiceAccountIPAccessListEntryView;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.openapi._public.constant.OpenApiConst.ResponseDescriptions;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserAllowList;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.lang.invoke.MethodHandles;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@Path("/api/atlas/v2/orgs/{orgId}/serviceAccounts/{clientId}/accessList")
@Extension(
    name = IPA_EXCEPTION,
    properties = {
      @ExtensionProperty(
          name = "xgen-IPA-104-resource-has-GET",
          value = "API predates IPA validation."),
    })
public class ApiAtlasOrgServiceAccountIpAccessListResource extends ApiBaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
  private final ServiceAccountAccessListSvc serviceAccountAccessListSvc;
  public static final int MAX_IP_ACCESS_LIST_ENTRIES = 200;

  @Inject
  public ApiAtlasOrgServiceAccountIpAccessListResource(
      final AppSettings appSettings,
      final ServiceAccountAccessListSvc serviceAccountAccessListSvc) {
    super(appSettings);
    this.serviceAccountAccessListSvc = serviceAccountAccessListSvc;
  }

  @GET
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @RolesAllowed({RoleSet.NAME.ORG_READ_ONLY})
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @Operation(
      summary = "Return All Access List Entries for One Organization Service Account",
      operationId = "listOrgServiceAccountAccessList",
      description =
          "Returns all access list entries that you configured for the specified Service Account"
              + " for the organization.",
      tags = {"Service Accounts"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "orgId"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clientId",
            description = "The Client ID of the Service Account.",
            in = ParameterIn.PATH,
            schema =
                @Schema(
                    type = "string",
                    pattern = ServiceAccountOpenApiConst.CLIENT_ID_PATTERN,
                    example = "mdb_sa_id_1234567890abcdef12345678"),
            required = true),
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "IAM Workload Identity")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listOrgAccessList")
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = ResponseDescriptions.OK,
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema =
                        @Schema(implementation = PaginatedServiceAccountIPAccessListView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  @Auth(endpointAction = "epa.organization.ApiAtlasOrgServiceAccountIpAccessListResource_2024_08_05.getOrgServiceAccountAccessList.GET")
  public Response getOrgServiceAccountAccessList(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Parameter(hidden = true) @PathParam("clientId") final String clientId,
      @Parameter(hidden = true) @QueryParam("envelope") final boolean envelope)
      throws SvcException {
    try {
      try (final MongoCursor<UserAllowList> cursor =
          serviceAccountAccessListSvc.getServiceAccountAccessList(org.getId(), null, clientId)) {
        return handlePagination(
            request,
            cursor,
            ServiceAccountIPAccessListEntryViewMapper.INSTANCE::fromUserAllowList,
            envelope);
      }
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, envelope, clientId);
    }
  }

  @POST
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @RolesAllowed({RoleSet.NAME.ORG_USER_ADMIN})
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @Operation(
      summary = "Add Access List Entries for One Organization Service Account",
      operationId = "createOrgServiceAccountAccessList",
      description =
          "Add Access List Entries for the specified Service Account for the organization."
              + " Resources require all API requests to originate from IP addresses on the API"
              + " access list.",
      tags = {"Service Accounts"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "orgId"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clientId",
            description = "The Client ID of the Service Account.",
            in = ParameterIn.PATH,
            schema =
                @Schema(
                    type = "string",
                    pattern = ServiceAccountOpenApiConst.CLIENT_ID_PATTERN,
                    example = "mdb_sa_id_1234567890abcdef12345678"),
            required = true),
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "A list of access list entries to add to the access list of the specified"
                      + " Service Account for the organization.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2024_08_05_JSON,
                      extensions = {
                        @Extension(
                            properties = {
                              @ExtensionProperty(
                                  name = "x-xgen-version",
                                  value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                            }),
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                  value = "Content predates IPA validation.")
                            })
                      },
                      array =
                          @ArraySchema(
                              schema =
                                  @Schema(
                                      implementation = ServiceAccountIPAccessListEntryView.class),
                              maxItems = MAX_IP_ACCESS_LIST_ENTRIES,
                              extensions = {
                                @Extension(
                                    name = IPA_EXCEPTION,
                                    properties = {
                                      @ExtensionProperty(
                                          name = "xgen-IPA-124-array-max-items",
                                          value = "Schema predates IPA validation."),
                                    })
                              }))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = ResponseDescriptions.OK,
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema =
                        @Schema(implementation = PaginatedServiceAccountIPAccessListView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "IAM Workload Identity")
            }),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-106-create-method-should-not-have-query-parameters",
                  value = "API predates IPA validation."),
              @ExtensionProperty(
                  name = "xgen-IPA-106-create-method-response-code-is-201",
                  value = "API predates IPA validation.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createOrgAccessList")
            }),
      })
  @Auth(endpointAction = "epa.organization.ApiAtlasOrgServiceAccountIpAccessListResource_2024_08_05.addServiceAccountAccessList.POST")
  public Response addServiceAccountAccessList(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @Parameter(hidden = true) @PathParam("clientId") final String clientId,
      @Parameter(hidden = true) @QueryParam("envelope") final boolean envelope,
      final List<ServiceAccountIPAccessListEntryView> accessList)
      throws SvcException {
    RequestBodyLoggingUtil.logServiceAccountIPAccessListEntryViewList(LOG, accessList);
    try {
      if (accessList.isEmpty()) {
        // Throw error for creation of empty access list
        throw ApiErrorCode.INVALID_IP_ADDRESS_OR_CIDR_NOTATION.exception(envelope, accessList);
      }
      serviceAccountAccessListSvc.addServiceAccountAccessList(
          org.getId(), null, auditInfo, clientId, accessList, envelope);
      return getOrgServiceAccountAccessList(request, org, clientId, envelope);
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, envelope, clientId);
    }
  }

  @DELETE
  @Path("/{ipAddress}")
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @RolesAllowed({RoleSet.NAME.ORG_USER_ADMIN})
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @Operation(
      summary = "Remove One Access List Entry from One Organization Service Account",
      operationId = "deleteOrgServiceAccountAccessListEntry",
      description =
          "Removes the specified access list entry from the specified Service Account for the"
              + " organization. You can't remove the requesting IP address from the access list.",
      tags = {"Service Accounts"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "orgId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clientId",
            description = "The Client ID of the Service Account.",
            in = ParameterIn.PATH,
            schema =
                @Schema(
                    type = "string",
                    pattern = ServiceAccountOpenApiConst.CLIENT_ID_PATTERN,
                    example = "mdb_sa_id_1234567890abcdef12345678"),
            required = true),
        @Parameter(
            name = "ipAddress",
            description =
                "One IP address or multiple IP addresses represented as one CIDR block. When"
                    + " specifying a CIDR block with a subnet mask, such as *********/24, use the"
                    + " URL-encoded value %2F for the forward slash /.",
            in = ParameterIn.PATH,
            schema =
                @Schema(
                    type = "string",
                    pattern =
                        "^([0-9]{1,3}\\.){3}[0-9]{1,3}(%2[fF][0-9]{1,3})?|([0-9a-f]{1,4}\\:){7}[0-9a-f]{1,4}(%2[fF][0-9]{1,3})?|([0-9a-f]{1,4}\\:){1,6}\\:(%2[fF][0-9]{1,3})?$",
                    example = "*********%2F24"),
            required = true)
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "IAM Workload Identity")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "deleteEntry"),
              @ExtensionProperty(name = "customMethod", value = "true", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteOrgAccessEntry")
            }),
      },
      responses = {
        @ApiResponse(
            responseCode = "204",
            description = ResponseDescriptions.NO_BODY,
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  @Auth(endpointAction = "epa.organization.ApiAtlasOrgServiceAccountIpAccessListResource_2024_08_05.deleteUserAccessListEntry.DELETE")
  public Response deleteUserAccessListEntry(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final AppUser currentUser,
      @Context final AuditInfo auditInfo,
      @Parameter(hidden = true) @PathParam("clientId") final String clientId,
      @Parameter(hidden = true) @PathParam("ipAddress") final String ipAddress,
      @Parameter(hidden = true) @QueryParam("envelope") final boolean envelope)
      throws SvcException {
    try {
      serviceAccountAccessListSvc.deleteServiceAccountAccessListEntry(
          request.getRemoteAddr(), org.getId(), null, currentUser, auditInfo, clientId, ipAddress);
      return new ApiResponseBuilder(envelope).noContent().build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, envelope, clientId);
    }
  }
}
