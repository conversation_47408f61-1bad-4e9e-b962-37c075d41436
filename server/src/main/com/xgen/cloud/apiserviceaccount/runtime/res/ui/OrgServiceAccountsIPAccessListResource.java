package com.xgen.cloud.apiserviceaccount.runtime.res.ui;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiserviceaccount._private.utils.RequestBodyLoggingUtil;
import com.xgen.cloud.apiserviceaccount._private.utils.ServiceAccountSvcExceptionHandler;
import com.xgen.cloud.apiserviceaccount._public.svc.ServiceAccountAccessListSvc;
import com.xgen.cloud.apiserviceaccount._public.view.ipaccess.ServiceAccountIPAccessListEntryView;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;
import java.lang.invoke.MethodHandles;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/orgs/{orgId}/serviceAccounts/{clientId}/accessList")
@Singleton
public class OrgServiceAccountsIPAccessListResource extends ApiBaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

  private final ServiceAccountAccessListSvc serviceAccountAccessListSvc;

  @Inject
  public OrgServiceAccountsIPAccessListResource(
      final AppSettings appSettings,
      final ServiceAccountAccessListSvc serviceAccountAccessListSvc) {
    super(appSettings);
    this.serviceAccountAccessListSvc = serviceAccountAccessListSvc;
  }

  @GET
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsIPAccessListResource.getServiceAccountAccessList.GET")
  public Response getServiceAccountAccessList(
      @Context final Organization org, @PathParam("clientId") final String clientId)
      throws SvcException {
    try {
      return Response.ok()
          .entity(
              serviceAccountAccessListSvc.getServiceAccountAccessListForUI(
                  org.getId(), null, clientId))
          .build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, clientId);
    }
  }

  @POST
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsIPAccessListResource.addServiceAccountAccessList.POST")
  public Response addServiceAccountAccessList(
      @Context UriInfo uriInfo,
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      final List<ServiceAccountIPAccessListEntryView> accessList)
      throws SvcException {
    RequestBodyLoggingUtil.logServiceAccountIPAccessListEntryViewList(LOG, accessList);

    try {
      if (accessList.isEmpty()) {
        // Throw error for creation of empty access list
        throw ApiErrorCode.INVALID_IP_ADDRESS_OR_CIDR_NOTATION.exception(false, accessList);
      }
      serviceAccountAccessListSvc.addServiceAccountAccessList(
          org.getId(), null, auditInfo, clientId, accessList, false);
      return Response.created(uriInfo.getAbsolutePathBuilder().build())
          .entity(
              serviceAccountAccessListSvc.getServiceAccountAccessListForUI(
                  org.getId(), null, clientId))
          .build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, clientId);
    }
  }

  @DELETE
  @Path("/{ipAddress}")
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.OrgServiceAccountsIPAccessListResource.deleteUserAccessListEntry.DELETE")
  public Response deleteUserAccessListEntry(
      @Context final HttpServletRequest request,
      @Context final Organization org,
      @Context final AppUser currentUser,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      @PathParam("ipAddress") final String ipAddress)
      throws SvcException {
    try {
      serviceAccountAccessListSvc.deleteServiceAccountAccessListEntry(
          request.getRemoteAddr(), org.getId(), null, currentUser, auditInfo, clientId, ipAddress);
      return new ApiResponseBuilder(false).noContent().build();
    } catch (SvcException e) {
      throw ServiceAccountSvcExceptionHandler.handledSvcException(e, clientId);
    }
  }
}
