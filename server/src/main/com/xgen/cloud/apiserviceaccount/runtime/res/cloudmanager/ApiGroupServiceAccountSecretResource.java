package com.xgen.cloud.apiserviceaccount.runtime.res.cloudmanager;

import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.apiserviceaccount._public.view.secret.ServiceAccountSecretRequestView;
import com.xgen.cloud.apiserviceaccount.runtime.res.api_2024_08_05.ApiAtlasGroupServiceAccountSecretsResource;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;

@Singleton
@Path("/api/public/v1.0/groups/{groupId}/serviceAccounts/{clientId}/secrets")
public class ApiGroupServiceAccountSecretResource extends ApiBaseResource {

  private final ApiAtlasGroupServiceAccountSecretsResource atlasResource;

  @Inject
  public ApiGroupServiceAccountSecretResource(
      final AppSettings appSettings, ApiAtlasGroupServiceAccountSecretsResource atlasResource) {
    super(appSettings);
    this.atlasResource = atlasResource;
  }

  @POST
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @RolesAllowed(NAME.GROUP_USER_ADMIN)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @Auth(endpointAction = "epa.project.ApiGroupServiceAccountSecretResource.createServiceAccountSecret.POST")
  public Response createServiceAccountSecret(
      @Context final Organization org,
      @Context final Group group,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      @QueryParam("envelope") final boolean envelope,
      final ServiceAccountSecretRequestView request)
      throws SvcException {
    return atlasResource.createServiceAccountSecret(
        org, group, auditInfo, clientId, envelope, request);
  }

  @DELETE
  @Path("/{secretId}")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed(NAME.GROUP_USER_ADMIN)
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @Auth(endpointAction = "epa.project.ApiGroupServiceAccountSecretResource.deleteServiceAccountSecret.DELETE")
  public Response deleteServiceAccountSecret(
      @Context final Organization org,
      @Context final Group group,
      @Context final AuditInfo auditInfo,
      @PathParam("clientId") final String clientId,
      @PathParam("secretId") final ObjectId secretId,
      @QueryParam("envelope") final boolean envelope)
      throws SvcException {
    return atlasResource.deleteServiceAccountSecret(
        org, group, auditInfo, clientId, secretId, envelope);
  }
}
