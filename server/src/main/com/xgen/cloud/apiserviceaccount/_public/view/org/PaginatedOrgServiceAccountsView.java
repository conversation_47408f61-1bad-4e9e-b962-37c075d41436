package com.xgen.cloud.apiserviceaccount._public.view.org;

import com.xgen.cloud.common.view._public.base.ApiListView;
import io.swagger.v3.oas.annotations.media.Schema;

// Workaround to represent list in Swagger Core
@Schema(
    name = "PaginatedOrgServiceAccounts",
    description = "A list of Organization Service Accounts.")
public final class PaginatedOrgServiceAccountsView extends ApiListView<OrgServiceAccountView> {}
