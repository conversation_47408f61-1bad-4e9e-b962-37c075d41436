package com.xgen.cloud.performanceadvisor._public.util;

import static com.xgen.cloud.common.explorer._public.model.CollectedIndex.hasEqualIndexProperties;
import static com.xgen.cloud.common.model._public.error.CommonErrorCode.SERVER_ERROR;
import static java.util.stream.Collectors.toList;

import com.xgen.cloud.common.explorer._public.model.CollectedIndex;
import com.xgen.cloud.common.explorer._public.model.CollectedIndexes;
import com.xgen.cloud.common.explorer._public.model.RedundantIndexes;
import com.xgen.cloud.common.model._public.error.SvcException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.SortedMap;
import java.util.stream.Collectors;
import org.apache.commons.collections4.trie.PatriciaTrie;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MergeIndexUtils {

  private static final Logger LOG = LoggerFactory.getLogger(MergeIndexUtils.class);

  private static final long ONE_DAY_IN_MILLIS = Duration.ofDays(1).toMillis();

  public static Pair<List<CollectedIndexes>, List<CollectedIndexes>> getHiddenIndexes(
      final List<CollectedIndexes> pCollectedIndexes) {
    final List<CollectedIndexes> hiddenIndexes = new ArrayList<>();
    final List<CollectedIndexes> remainingIndexes = new ArrayList<>();
    pCollectedIndexes.stream()
        .map(MergeIndexUtils::filterHiddenIndexes)
        .filter(Objects::nonNull)
        .forEach(
            hiddenAndRemainingIndexes -> {
              if (hiddenAndRemainingIndexes.getLeft() != null) {
                hiddenIndexes.add(hiddenAndRemainingIndexes.getLeft());
              }
              if (hiddenAndRemainingIndexes.getRight() != null) {
                remainingIndexes.add(hiddenAndRemainingIndexes.getRight());
              }
            });
    return Pair.of(hiddenIndexes, remainingIndexes);
  }

  private static Pair<CollectedIndexes, CollectedIndexes> filterHiddenIndexes(
      final CollectedIndexes pCollectedIndexes) {
    if (pCollectedIndexes.getIndexes() == null) {
      return null;
    }

    final List<CollectedIndex> hiddenIndexes = new ArrayList<>();
    final List<CollectedIndex> remainingIndexes = new ArrayList<>();

    pCollectedIndexes
        .getIndexes()
        .forEach(
            index -> {
              if (index.getIndexProperties() != null
                  && index.getIndexProperties().containsKey("hidden")
                  && index.getIndexProperties().get("hidden").equals(true)) {
                hiddenIndexes.add(index);
              } else {
                remainingIndexes.add(index);
              }
            });

    final CollectedIndexes hiddenCollectedIndexes =
        hiddenIndexes.size() != 0
            ? new CollectedIndexes(
                pCollectedIndexes.getId(),
                pCollectedIndexes.getGroupId(),
                pCollectedIndexes.getHostId(),
                pCollectedIndexes.getNamespace(),
                pCollectedIndexes.getLastUpdated(),
                pCollectedIndexes.getVersion(),
                hiddenIndexes,
                pCollectedIndexes.getShards(),
                pCollectedIndexes.getCRC32())
            : null;

    final CollectedIndexes remainingCollectedIndexes =
        remainingIndexes.size() != 0
            ? new CollectedIndexes(
                pCollectedIndexes.getId(),
                pCollectedIndexes.getGroupId(),
                pCollectedIndexes.getHostId(),
                pCollectedIndexes.getNamespace(),
                pCollectedIndexes.getLastUpdated(),
                pCollectedIndexes.getVersion(),
                remainingIndexes,
                pCollectedIndexes.getShards(),
                pCollectedIndexes.getCRC32())
            : null;
    return Pair.of(hiddenCollectedIndexes, remainingCollectedIndexes);
  }

  public static List<RedundantIndexes> getRedundantIndexes(
      final List<CollectedIndexes> pCollectedIndexes) {
    return pCollectedIndexes.stream()
        .map(MergeIndexUtils::filterRedundantIndexes)
        .filter(Objects::nonNull)
        .collect(toList());
  }

  private static RedundantIndexes filterRedundantIndexes(final CollectedIndexes pCollectedIndexes) {
    if (pCollectedIndexes.getIndexes() == null) {
      return null;
    }

    final PatriciaTrie<CollectedIndex> indexFieldsTrie = new PatriciaTrie<>();

    pCollectedIndexes
        .getIndexes()
        .forEach(index -> indexFieldsTrie.put(index.toTrieString(), index));

    final List<Pair<CollectedIndex, List<CollectedIndex>>> redundantAndRelatedIndexes =
        new ArrayList<>();

    for (final CollectedIndex index : indexFieldsTrie.values()) {
      final SortedMap<String, CollectedIndex> matches =
          indexFieldsTrie.prefixMap(index.toTrieString());
      if (!matches.isEmpty()) {
        final List<CollectedIndex> matchIndexes =
            matches.values().stream()
                .filter(match -> !match.equals(index) && hasEqualIndexProperties(index, match))
                .collect(Collectors.toList());
        if (matchIndexes.size() > 0) {
          redundantAndRelatedIndexes.add(Pair.of(index, matchIndexes));
        }
      }
    }

    return redundantAndRelatedIndexes.size() != 0
        ? new RedundantIndexes(
            pCollectedIndexes.getId(),
            pCollectedIndexes.getGroupId(),
            pCollectedIndexes.getHostId(),
            pCollectedIndexes.getNamespace(),
            pCollectedIndexes.getShards(),
            redundantAndRelatedIndexes)
        : null;
  }

  public static List<CollectedIndexes> mergeIndexesForRemovalAdvice(
      final List<CollectedIndexes> pCollectedIndexes1,
      final List<CollectedIndexes> pCollectedIndexes2,
      final ObjectId pGroupId,
      final String pHostId)
      throws SvcException {
    if (pCollectedIndexes1 == null || pCollectedIndexes2 == null) {
      LOG.warn(
          "Error merging collected indexes because one or both lists are null."
              + " collectedIndexes1={} collectedIndexes2={} groupId={} pHostId={}",
          pCollectedIndexes1,
          pCollectedIndexes2,
          pGroupId,
          pHostId);
      throw new SvcException(SERVER_ERROR);
    }

    final List<CollectedIndexes> result = new ArrayList<>();
    final Map<String, CollectedIndexes> nsToIndexesMap =
        pCollectedIndexes2.stream()
            .collect(Collectors.toMap(CollectedIndexes::getNamespace, indexes -> indexes));

    for (final CollectedIndexes indexes : pCollectedIndexes1) {
      final String namespace = indexes.getNamespace();
      if (nsToIndexesMap.containsKey(namespace)) {
        final CollectedIndexes mergedIndexesForNamespace =
            mergeCollectedIndexesFromExpectedNodes(
                indexes, nsToIndexesMap.get(namespace), pGroupId, pHostId);
        result.add(mergedIndexesForNamespace);
        nsToIndexesMap.remove(namespace);
      } else {
        LOG.warn(
            "Inconsistency found while merging collected indexes. Namespace={} found on"
                + " collectedIndexes1={} that does not exist on collectedIndexes2={} for hostId={}",
            namespace,
            pCollectedIndexes1,
            pCollectedIndexes2,
            pHostId);
      }
    }
    // logging all ns that only exists in pCollectedIndexes2
    if (!nsToIndexesMap.isEmpty()) {
      LOG.warn(
          "Inconsistency found while merging collected indexes. Namespaces={} found on"
              + " collectedIndexes2={} that do not exist on collectedIndexes1={} for hostId={}",
          nsToIndexesMap.keySet(),
          pCollectedIndexes2,
          pCollectedIndexes1,
          pHostId);
    }
    return result;
  }

  public static CollectedIndexes mergeCollectedIndexesFromExpectedNodes(
      final CollectedIndexes pIndexes1,
      final CollectedIndexes pIndexes2,
      final ObjectId pGroupId,
      final String pHostId)
      throws SvcException {
    if (pIndexes1 == null || pIndexes2 == null) {
      LOG.warn(
          "Error merging collected indexes because one or both are null. collectedIndexes1={}"
              + " collectedIndexes2={} groupId={}",
          pIndexes1,
          pIndexes2,
          pGroupId);
      throw new SvcException(SERVER_ERROR);
    }

    final Map<String, CollectedIndex> indexNameToIndexMap =
        pIndexes2.getIndexes().stream()
            .collect(Collectors.toMap(CollectedIndex::getIndexName, index -> index));

    final List<CollectedIndex> collectedIndexes = new ArrayList<>();
    for (final CollectedIndex index1 : pIndexes1.getIndexes()) {
      final String currentIndexName = index1.getIndexName();
      if (indexNameToIndexMap.containsKey(currentIndexName)) {
        final CollectedIndex index2 = indexNameToIndexMap.get(currentIndexName);
        final Date mergedDate =
            index2.getAccessCountSince().after(index1.getAccessCountSince())
                ? index2.getAccessCountSince()
                : index1.getAccessCountSince();
        final CollectedIndex mergedIndex =
            new CollectedIndex(
                index1.getFields(),
                index1.isCaseInsensitive(),
                currentIndexName,
                Math.max(index1.getIndexSizeBytes(), index2.getIndexSizeBytes()),
                index1.getAccessCount() + index2.getAccessCount(),
                Math.min(index1.getAccessPeriod(), index2.getAccessPeriod()),
                mergedDate,
                index1.getIndexProperties());

        collectedIndexes.add(mergedIndex);
        indexNameToIndexMap.remove(currentIndexName);
      } else {
        LOG.warn(
            "Inconsistency found while merging collected indexes. CollectedIndex={} found on"
                + " collectedIndexes1={} that do not exist on collectedIndexes2={} for hostId={}",
            index1,
            pIndexes1,
            pIndexes2,
            pHostId);
      }
    }

    // logging all fields that only exists in pCollectedIndexes2
    if (!indexNameToIndexMap.isEmpty()) {
      LOG.warn(
          "Inconsistency found while merging collected indexes. Indexes={} found on"
              + " collectedIndexes2={} that do not exist on collectedIndexes1={} for hostId={}",
          indexNameToIndexMap.keySet(),
          pIndexes2,
          pIndexes1,
          pHostId);
    }

    return new CollectedIndexes(
        pIndexes2.getGroupId(),
        pIndexes2.getHostId(),
        pIndexes2.getNamespace(),
        collectedIndexes,
        pIndexes2.getShards());
  }

  public static List<CollectedIndexes> getUnusedIndexes(
      final List<CollectedIndexes> pCollectedIndexes, final int pMinUnusedAccessPeriodDays) {
    return pCollectedIndexes.stream()
        .map(index -> filterUnusedIndexes(index, pMinUnusedAccessPeriodDays))
        .filter(Objects::nonNull)
        .collect(toList());
  }

  private static CollectedIndexes filterUnusedIndexes(
      final CollectedIndexes pCollectedIndexes, final int pMinUnusedAccessPeriodDays) {
    if (pCollectedIndexes.getIndexes() == null) {
      return null;
    }

    final List<CollectedIndex> unusedCollectedIndexes =
        pCollectedIndexes.getIndexes().stream()
            .filter(
                index ->
                    index.getAccessCount() == 0
                        && index.getAccessPeriod() != null
                        && index.getAccessPeriod() > pMinUnusedAccessPeriodDays * ONE_DAY_IN_MILLIS)
            .collect(toList());

    if (unusedCollectedIndexes.size() != 0) {
      return new CollectedIndexes(
          pCollectedIndexes.getId(),
          pCollectedIndexes.getGroupId(),
          pCollectedIndexes.getHostId(),
          pCollectedIndexes.getNamespace(),
          pCollectedIndexes.getLastUpdated(),
          pCollectedIndexes.getVersion(),
          unusedCollectedIndexes,
          pCollectedIndexes.getShards(),
          pCollectedIndexes.getCRC32());
    }
    return null;
  }
}
