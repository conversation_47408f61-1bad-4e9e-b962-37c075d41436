package com.xgen.cloud.user.runtime.res;

import static com.xgen.cloud.common.http.url._public.UrlUtils.getQueryString;
import static com.xgen.cloud.common.http.url._public.UrlUtils.getQueryStringMapFromUriInfo;
import static com.xgen.cloud.common.util._public.auth.AuthUrlUtils.createRegistrationUrl;
import static com.xgen.cloud.common.util._public.json.JsonUtils.getJSONParam;
import static com.xgen.module.account.svc.OAuthSvc.generateErrorUrl;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.xgen.cloud.access.recaptcha._public.action.ReCaptchaAction;
import com.xgen.cloud.access.recaptcha._public.svc.RecaptchaSvc;
import com.xgen.cloud.access.role._public.model.GroupRole;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.driverwrappers._public.common.DbMaintenanceException;
import com.xgen.cloud.common.filter._public.FilterUtils;
import com.xgen.cloud.common.groupcreation._public.svc.GroupCreationSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.error.ServerError;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.passwordreset._public.model.AuthType;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.federation._public.model.FederationSettings;
import com.xgen.cloud.federation._public.svc.FederationSettingsSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited.Type;
import com.xgen.cloud.monitoring.ratelimit._public.svc.UnauthenticatedRateLimitSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._private.svc.UserSvcOkta;
import com.xgen.cloud.user._public.error.legacy2fa.MultiFactorAuthErrorCode;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserRegistrationForm;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.module.account.model.EmptyOAuthMethod;
import com.xgen.module.account.model.SessionTokenOAuthMethod;
import com.xgen.module.account.svc.AccountUserSvc;
import com.xgen.module.account.svc.OAuthSvc;
import com.xgen.module.iam.config.IamAppSettings;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.form.UserLoginForm;
import com.xgen.svc.mms.model.auth.UiAuthCode;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.view.settings.UserInvitationView;
import com.xgen.svc.mms.res.view.user.FederatedUserView;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.svc.mms.svc.user.UserLoginSvc;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriBuilder;
import jakarta.ws.rs.core.UriInfo;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/user")
@Singleton
public class UserResource extends BaseResource {
  protected static final String ACCOUNT_LOGIN_PATH = "/account/login";
  static final String RATE_LIMIT_POLICY_PREFIX = "mms.user";
  private static final Logger LOG = LoggerFactory.getLogger(UserResource.class);
  private static final String SIGNUP_SOURCE_PARAM = "signupSource";
  private static final String SIGNUP_METHOD_PARAM = "signupMethod";
  private static final int IP_BASED_RATE_LIMIT_PERIOD_MINUTES = 20;
  private static final int IP_BASED_RATE_LIMIT_PERIOD_LIMIT = 100;

  private static final Counter IAM_REFRESH_SESSION_COUNTER =
      Counter.build()
          .name("mms_iam_refresh_session_total")
          .help("Total Refresh Session Calls Made")
          .register();

  private final UserSvc userSvc;
  private final GroupSvc groupSvc;
  private final GroupCreationSvc groupCreationSvc;
  private final OrganizationSvc organizationSvc;
  private final InvitationSvc invitationSvc;
  private final ConfigLimitSvc configLimitSvc;
  private final UserLoginSvc userLoginSvc;
  private final OAuthSvc oAuthSvc;
  private final AppSettings appSettings;
  private final IamAppSettings iamAppSettings;
  private final FederationSettingsSvc federationSettingsSvc;
  private final SalesSoldDealActivationSvc salesSoldDealActivationSvc;
  private final UnauthenticatedRateLimitSvc unauthenticatedRateLimitSvc;
  private final AccountUserSvc accountUserSvc;
  private final RecaptchaSvc recaptchaSvc;
  private final AuditInfoSvc auditInfoSvc;

  @Inject
  public UserResource(
      AppSettings pAppSettings,
      IamAppSettings pIamAppSettings,
      UserSvc pUserSvc,
      GroupSvc pGroupSvc,
      GroupCreationSvc pGroupCreationSvc,
      OrganizationSvc pOrganizationSvc,
      InvitationSvc pInvitationSvc,
      ConfigLimitSvc pConfigLimitSvc,
      UserLoginSvc pUserLoginSvc,
      OAuthSvc pOAuthSvc,
      FederationSettingsSvc pFederationSettingsSvc,
      SalesSoldDealActivationSvc pSalesSoldDealActivationSvc,
      UnauthenticatedRateLimitSvc pUnauthenticatedRateLimitSvc,
      AccountUserSvc pAccountUserSvc,
      RecaptchaSvc recaptchaSvc,
      AuditInfoSvc auditInfoSvc) {
    appSettings = pAppSettings;
    iamAppSettings = pIamAppSettings;
    userSvc = pUserSvc;
    groupSvc = pGroupSvc;
    groupCreationSvc = pGroupCreationSvc;
    organizationSvc = pOrganizationSvc;
    invitationSvc = pInvitationSvc;
    configLimitSvc = pConfigLimitSvc;
    userLoginSvc = pUserLoginSvc;
    oAuthSvc = pOAuthSvc;
    federationSettingsSvc = pFederationSettingsSvc;
    salesSoldDealActivationSvc = pSalesSoldDealActivationSvc;
    unauthenticatedRateLimitSvc = pUnauthenticatedRateLimitSvc;
    accountUserSvc = pAccountUserSvc;
    this.recaptchaSvc = recaptchaSvc;
    this.auditInfoSvc = auditInfoSvc;
  }

  @GET
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, bypassMfa = true)
  public Response index(@Context RequestParams pRequestParams, @Context HttpServletRequest pRequest)
      throws Exception {
    if ((boolean) pRequestParams.get(RequestParams.IS_SAML)) {
      return seeOther("/");
    }

    return permanentRedirect("/account" + getQueryString(pRequest));
  }

  @GET
  @Path("/params")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      groupSource = UiCall.GroupSource.NONE,
      bypassMfa = true)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response params(@Context HttpServletRequest pRequest, @Context AppUser pAppUser) {
    RequestParams params = getRequestParams(pRequest);

    params.setAppUser(pAppUser);
    String csrfTime = String.valueOf(System.currentTimeMillis());
    String csrfToken = FilterUtils.generateCsrfToken(pAppUser.getOktaUserId(), csrfTime);
    params.setCsrfTime(csrfTime);
    params.setCsrfToken(csrfToken);

    return Response.ok(params).build();
  }

  @POST
  @Path("/removeUserFromGroup/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_USER_ADMIN)
  @Auth(endpointAction = "epa.project.UserResource.removeUserFromGroup.POST")
  public Response removeUserFromGroup(
      @Context HttpServletRequest pRequest,
      @Context Group pGroup,
      @Context AuditInfo pAuditInfo,
      @FormParam("username") String pUsername)
      throws SvcException {
    String trimmedUsername = StringUtils.trimToNull(pUsername);
    if (trimmedUsername == null) {
      throw new SvcException(AppUserErrorCode.INVALID_USERNAME);
    }
    AppUser user = userSvc.findByUsername(trimmedUsername);
    if (user == null) {
      throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
    }
    if (!user.hasGroupId(pGroup.getId())) {
      throw new SvcException(AppUserErrorCode.USER_NOT_IN_GROUP);
    }

    groupSvc.removeUserFromGroup(user, pGroup, pAuditInfo);
    return SimpleApiResponse.ok().resource(pRequest.getRequestURI()).build();
  }

  @GET
  @Path("/partnerIntegrationsData/{username}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "getUserPartnerIntegrationsData",
      types = {Type.IP, Type.PATH_PARAM})
  public Response getUserPartnerIntegrationsData(@PathParam("username") final String username) {
    final AppUser user = userSvc.findByUsername(username);
    if (user != null && user.getPartnerIntegrationsData() != null) {
      return Response.ok().entity(user.getPartnerIntegrationsData()).build();
    } else {
      // Since this is an unauthenticated endpoint, we want to reveal as little information as
      // possible - so return success even if user doesn't exist
      return Response.ok().build();
    }
  }

  @POST
  @Path("/updateUserRole/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_USER_ADMIN)
  @Auth(endpointAction = "epa.project.UserResource.updateUserRole.POST")
  public Response updateUserRole(
      @Context HttpServletRequest pRequest,
      @Context AppUser pUser,
      @Context Group pGroup,
      @Context AuditInfo pAuditInfo,
      @FormParam("username") String pUsername,
      @FormParam("roles[]") List<String> pRoles)
      throws Exception {
    String username = StringUtils.trimToNull(pUsername);
    if (username != null) {
      username = username.toLowerCase();
    }

    AppUser subjectUser = userSvc.findByUsername(username);

    if (subjectUser == null) {
      throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
    }

    if (invitationSvc.isUserInvitedToGroup(pGroup.getId(), username)
        || !subjectUser.hasGroupId(pGroup.getId())) {
      throw new SvcException(AppUserErrorCode.USER_NOT_IN_GROUP);
    }

    Set<Role> userRoles = new HashSet<>(subjectUser.getGroupRoles(pGroup.getId()));
    Set<Role> removedRoles = new HashSet<>();

    for (String roleStr : pRoles) {
      if (roleStr.startsWith("-")) {
        Role removedRole = Role.fromString(roleStr.substring(1));

        if (removedRole != null) {
          removedRoles.add(removedRole);
        } else {
          throw new SvcException(AppUserErrorCode.INVALID_ROLE_NAME);
        }
      } else {
        Role addedRole = Role.fromString(roleStr);

        if (addedRole != null) {
          userRoles.add(Role.fromString(roleStr));
        } else {
          throw new SvcException(AppUserErrorCode.INVALID_ROLE_NAME);
        }
      }
    }

    groupSvc.updateUserRole(subjectUser, pGroup, userRoles, removedRoles, pAuditInfo);
    return SimpleApiResponse.ok().resource(pRequest.getRequestURI()).build();
  }

  /**
   * This path is used by marketing for registration forms that are embedded inside landing pages
   * server by third party content providers. In the very rare event where a logged in user views a
   * landing page, they want to always show the registration page.
   */
  @GET
  @Path("/register/landing")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false)
  public Response registerPage(
      @Context RequestParams pRequestParams,
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse)
      throws Exception {
    if (pRequestParams.getAppUser() != null) {
      userLoginSvc.doSignOut(pRequest, pResponse);
    }
    return seeOther(createRegistrationUrl("register") + getQueryString(pRequest));
  }

  @GET
  @Path("/register")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  public Response registerPage(
      @Context RequestParams pRequestParams,
      @Context HttpServletRequest pRequest,
      @QueryParam("username") String pInvitedEmail,
      @QueryParam("groupname") String pInvitingGroupName,
      @QueryParam("token") String pInvitationToken,
      @QueryParam("c") String pOldStyleCampaignId)
      throws Exception {
    if (pRequestParams.getAppUser() != null
        && pRequestParams.getAppUser().getCurrentGroupId() != null) {
      return seeOther("/user/signout");
    }

    return seeOther(createRegistrationUrl("register") + getQueryString(pRequest));
  }

  /** This is used in the user/register page in the registration flow. */
  @POST
  @Path("/addAnotherGroup/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(
      roles = RoleSet.ORG_GROUP_CREATOR,
      groupSource = GroupSource.NONE,
      appSettingsSetupCall = true)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  @Auth(endpointAction = "epa.organization.UserResource.addAnotherGroup.POST")
  public Response addAnotherGroup(
      @Context HttpServletRequest pRequest,
      @Context AppUser pUser,
      @Context Organization pOrganization,
      @FormParam("group") String pNewGroupName,
      @FormParam("clientId") @DefaultValue("") String pClientId,
      @FormParam("anonymousId") String pAnonymousId,
      @FormParam(SIGNUP_SOURCE_PARAM) @DefaultValue("Account") String pSignupSource,
      @FormParam(SIGNUP_METHOD_PARAM) @DefaultValue("form") String pSignupMethod)
      throws SvcException {
    ObjectId newGroupId = ObjectId.get();
    // Validate addition of 1 group to organization relative to config limits
    configLimitSvc.validateMaxGroupsPerOrg(1, pOrganization.getId());
    // Validate addition of 1 group to user, with null groupId given new group, if
    // applicable
    configLimitSvc.validateMaxGroupsPerUser(1, pUser.getUsername(), null);
    groupCreationSvc.addAnotherGroup(
        newGroupId, // object Id of the new group intended to be created.
        pOrganization,
        pUser.getId(),
        pNewGroupName,
        auditInfoSvc.fromUiCall(
            pUser, pRequest.getRemoteAddr(), StringUtils.trimToNull(pClientId)));

    // AnonymousId is supplied in the post registration flow. Adding this condition
    // in case another part of the UI that isn't post registration uses this resource
    // endpoint. In that case they won't be supplying an anonymousId.
    if (pAnonymousId != null) {
      userSvc.createNewUserTrackingRequestWhenUserHaveOneOrg(
          pAnonymousId,
          pUser,
          pUser.getCurrentOrgId(),
          pSignupSource,
          pSignupMethod,
          "Project Creation",
          pRequest);
    }

    return SimpleApiResponse.ok()
        .resource(pRequest.getRequestURI())
        .newObjId(newGroupId.toString())
        .build();
  }

  /**
   * Endpoint used by Marketing embedded registration forms, such as <a
   * href="https://www.mongodb.com/try">www.mongodb.com/try</a>}) to register a new User
   *
   * @see #externalPostRegister
   */
  @POST
  @Path("/external/register")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.MARKETING)
  public Response externalRegister(UserRegistrationForm pForm) throws SvcException {
    // TODO: Migrate to new rate-limit annotation after WEBSITE-17162 makes clientIp more reliable
    unauthenticatedRateLimitSvc.rateLimitByIpAddress(
        "externalRegisterCall",
        pForm.getClientIp(),
        IP_BASED_RATE_LIMIT_PERIOD_MINUTES,
        IP_BASED_RATE_LIMIT_PERIOD_LIMIT);

    // For External Registrations, Request::getRemoteAddr() would return .com's
    // proxy server address, not the actual client IP address. To work around that,
    // a new field was added to the JSON payload, indicating the Client IP address
    // that we should use to tag the new user
    AuditInfo auditInfo = auditInfoSvc.fromUiCall(null, pForm.getClientIp());

    /*
     * newGroup flag is only used to determine whether registration validation has
     * to validate for the Job Responsibility. New group would be false only for
     * API and invite flows, while for registration it's always true. Since
     * this endpoint will not be used for invite, newGroup should be true.
     */
    pForm.setNewGroup(true);
    AppUser user = registerUser(pForm, true, auditInfo);
    auditInfoSvc.addAppUserToAuditInfo(auditInfo, user);

    return SimpleApiResponse.ok().build();
  }

  /**
   * Endpoint used by Marketing embedded registration forms, such as <a
   * href="https://www.mongodb.com/try">www.mongodb.com/try</a>}) to automatically log the new User
   * in and redirect them to the landing page within the app
   *
   * <p>This was originally an HTML endpoint that returned a <a
   * href="https://developer.mozilla.org/pt-BR/docs/Web/HTTP/Status/303">303 Response</a> with the
   * URL the browser should redirect to, but an issue was observed in Safari Mobile where it uses
   * the wrong http method to make that request, and it was causing CORS issues in this integration.
   *
   * @return a JSON object containing a <code>redirectUrl</code> field which the forms should
   *     manually redirect the browser to
   * @see #externalRegister
   */
  @POST
  @Path("/external/postRegister")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.MARKETING)
  public Response externalPostRegister(
      @Context HttpServletRequest pRequest,
      @Context AuditInfo pAuditInfo,
      @FormParam("username") String pUsername,
      @FormParam("password") String pPassword,
      @FormParam("planType") @DefaultValue("NDS") String pPlanType,
      @FormParam("couponCode") String pCouponCode,
      @FormParam("signup_source") String pSignupSource,
      @FormParam("anonymousId") String pAnonymousId)
      throws Exception {

    if (!userSvc.isUserRegistrationEnabled()) {
      throw new SvcException(AppUserErrorCode.REGISTRATION_DISABLED);
    }

    AppUser user = userSvc.findByUsername(pUsername);
    if (user == null) {
      throw new SvcException(AppUserErrorCode.INVALID_USERNAME);
    }

    userSvc.createNewUserTrackingRequestWhenUserHaveOneOrg(
        pAnonymousId,
        user,
        user.getCurrentOrgId(),
        pSignupSource,
        "form",
        "External Registration",
        pRequest);

    // We need to persist any query params that are given to us for marketing
    // purposes.
    UriBuilder queryParamBuilder = UriBuilder.fromPath("").replaceQuery(pRequest.getQueryString());

    queryParamBuilder.replaceQueryParam("userId", user.getId().toString());

    boolean planTypeIsNDS = Objects.equals(pPlanType, "NDS");
    queryParamBuilder.replaceQueryParam("nds", planTypeIsNDS);
    if (pCouponCode != null) {
      queryParamBuilder.replaceQueryParam("couponCode", pCouponCode);
    }
    LOG.info("Getting session token for {} user after successful external registration", pUsername);
    String oktaSessionToken =
        getUserSvcOkta().getUserSessionToken(pUsername, pPassword, pAuditInfo);

    queryParamBuilder.queryParam("sessionToken", oktaSessionToken);
    queryParamBuilder.queryParam("n", UserLoginSvc.REGISTRATION_SUCCESS_PAGE_PATH);

    if (StringUtils.isNotEmpty(pSignupSource)) {
      queryParamBuilder.queryParam(SIGNUP_SOURCE_PARAM, pSignupSource);
    }
    queryParamBuilder.queryParam(SIGNUP_METHOD_PARAM, "form");

    String registrationUrl = createRegistrationUrl("oauth") + getQueryString(queryParamBuilder);
    String fqdnRegistrationUrl = appSettings.getCentralUrl() + registrationUrl;

    JSONObject response = new JSONObject();

    /* This was a mistake :-( redirectUrl was meant to be a string value, not a string array.
    Will leave as is since it's harmless and changing this would require coordination with
    the .com team to consume a different field type */

    response.append("redirectUrl", fqdnRegistrationUrl);
    return Response.ok(response.toString()).type(MediaType.APPLICATION_JSON_TYPE).build();
  }

  // TODO CLOUDP-297277 move account related handlers to AccountAuthResource, there may be more in
  // this file
  /** This is the POST from the account/register page in the registration flow */
  @POST
  @Path("/registerCall")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "registerCall",
      types = {Type.SUSPENDABLE_IP})
  public Response registerCall(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      UserRegistrationForm pForm)
      throws SvcException {

    LOG.info("Initiating registration for username {}", pForm.getUsername());
    if (!pForm.getTosChecked()) {
      LOG.warn("User {} initiated registration without accepting TOS", pForm.getUsername());
      throw new SvcException(AppUserErrorCode.NEEDS_TOS_ACCEPTANCE);
    }

    // When a user tries to register with an username associated with an account that is currently
    // soft-deleted (in the process of being deleted via GDPR erasure), we should prevent a new user
    // from being created with the same email address. This specific error code is meant to prevent
    // confusion with the DUPLICATE_USERNAME error that would be thrown otherwise, since from the
    // user's POV the account doesn't exist anymore.
    if (accountUserSvc.isAccountBeingDeleted(pForm.getUsername())) {
      throw new SvcException(AppUserErrorCode.CANNOT_REGISTER_ACCOUNT_BEING_DELETED);
    }

    // We're finding the invitation before we register the user, because registerUser will process
    // the invitation and delete it.
    Optional<Invitation> invitationOpt =
        invitationSvc.findByUsernameAndToken(pForm.getUsername(), pForm.getInvitationToken());

    AppUser user = registerUser(pForm, false, pAuditInfo);

    String csrfTime = String.valueOf(System.currentTimeMillis());
    SimpleApiResponse.Builder responseBuilder =
        SimpleApiResponse.ok()
            .customField("csrfToken", FilterUtils.generateCsrfToken(user.getOktaUserId(), csrfTime))
            .customField("csrfTime", csrfTime);
    // adds tracking if invitation token is present
    if (pForm.getClientState() == null) {
      pForm.setClientState(new HashMap<>());
    }

    String signupSource =
        invitationOpt.isPresent()
            ? "invite"
            : pForm.getClientState().get("signupSource") != null
                ? pForm.getClientState().get("signupSource").toString()
                : "";
    String context = invitationOpt.isPresent() ? "Invitation" : "Account Registration";
    ObjectId orgId =
        invitationOpt.isPresent() ? invitationOpt.get().getOrgId() : user.getCurrentOrgId();
    userSvc.createNewUserTrackingRequestWhenUserHaveOneOrg(
        pForm.getAnonymousId(), user, orgId, signupSource, "form", context, pRequest);
    pForm.getClientState().putIfAbsent(SIGNUP_SOURCE_PARAM, signupSource);
    pForm.getClientState().putIfAbsent(SIGNUP_METHOD_PARAM, "form");

    LOG.info("Processing Okta registration for username {}", pForm.getUsername());
    String username = pForm.getUsername();
    String password = pForm.getPassword();

    LOG.info("Getting session token for {} user after successful internal registration", username);

    String oktaSessionToken = getUserSvcOkta().getUserSessionToken(username, password, pAuditInfo);

    String loginRedirect =
        oAuthSvc.getAccountLoginRedirect(
            username,
            pResponse,
            new SessionTokenOAuthMethod(oktaSessionToken),
            pForm.getClientState());

    LOG.info("Completed Okta registration for username {}, redirecting", pForm.getUsername());
    return responseBuilder.customField("loginRedirect", loginRedirect).build();
  }

  @GET
  @Path("/invitation/{username}/redirect")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "redirectInvite",
      types = {Type.IP, Type.PATH_PARAM})
  public Response redirectInvite(
      @PathParam("username") String pUsername, @QueryParam("inviteToken") String pInviteToken)
      throws Exception {
    Optional<Invitation> invitationOpt =
        invitationSvc.findByUsernameAndToken(pUsername, pInviteToken);
    if (invitationOpt.isEmpty()) {
      return seeOther(
          generateErrorUrl(ACCOUNT_LOGIN_PATH, AppUserErrorCode.INVALID_INVITATION_TOKEN.name()));
    }

    AppUser appUser = userSvc.findByUsername(pUsername);
    boolean needsRegistration = (appUser == null && !userSvc.usernameExists(pUsername));
    Optional<String> optionalIdpId = userSvc.getUserIdentityProviderId(pUsername);
    Invitation invitation = invitationOpt.get();
    Organization org = organizationSvc.findById(invitation.getOrgId());
    Group group = Optional.ofNullable(invitation.getGroupId()).map(groupSvc::findById).orElse(null);

    String route;
    if (optionalIdpId.isPresent()) {
      route = "oauth";
    } else if (needsRegistration) {
      route = "register";
    } else {
      route = "login";
    }
    return seeOther(
        getInviteRedirectURL(route, invitation, org, group, optionalIdpId.orElse(null)));
  }

  @GET
  @Path("/invitation/{username}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response getInvite(
      @PathParam("username") String pUsername, @QueryParam("inviteToken") String pInviteToken)
      throws Exception {

    Invitation invitation =
        invitationSvc
            .findByUsernameAndToken(pUsername, pInviteToken)
            .orElseThrow(() -> new SvcException(AppUserErrorCode.INVALID_INVITATION_TOKEN));

    JSONObject response = new JSONObject();
    AppUser inviterUser = userSvc.findByUsername(invitation.getInviterUsername());
    Group inviterGroup =
        invitation.getGroupId() != null ? groupSvc.findById(invitation.getGroupId()) : null;
    Organization inviterOrg =
        invitation.getOrgId() != null ? organizationSvc.findById(invitation.getOrgId()) : null;

    response.put("inviterUsername", inviterUser.getUsername());
    response.put("inviterName", inviterUser.getFullName());
    if (inviterGroup != null) {
      response.put("inviterGroupName", inviterGroup.getName());
    }
    if (inviterOrg != null) {
      response.put("inviterOrgName", inviterOrg.getName());
    }
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/invitations")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ANY_AUTHENTICATED_USER)
  public Response getInvites(@Context AppUser user) {
    List<Invitation> invitations = invitationSvc.findByUsername(user.getUsername());

    Set<ObjectId> orgIds = invitations.stream().map(Invitation::getOrgId).collect(toSet());
    Map<ObjectId, Organization> orgsById =
        organizationSvc.findByIds(orgIds).stream().collect(toMap(Organization::getId, org -> org));

    List<ObjectId> groupIds =
        invitations.stream().map(Invitation::getGroupId).distinct().collect(toList());
    Map<ObjectId, Group> groupsById =
        groupSvc.findByIds(groupIds).stream().collect(toMap(Group::getId, group -> group));

    List<UserInvitationView> userInvitations = new ArrayList<>();
    // generate a new Invite list including the expiry date
    for (Invitation invite : invitations) {
      Date thirtyDaysBefore = DateUtils.addDays(new Date(), -30);
      if (invite.getCreatedAt().after(thirtyDaysBefore)) {
        String orgName = orgsById.get(invite.getOrgId()).getName();
        String groupName =
            invite.getGroupId() == null ? "" : groupsById.get(invite.getGroupId()).getName();
        userInvitations.add(new UserInvitationView(invite, orgName, groupName));
      }
    }
    return Response.ok(Map.of("invitations", userInvitations)).build();
  }

  @POST
  @Path("/invitations/{invitationId}/accept")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ANY_AUTHENTICATED_USER)
  public Response acceptInvite(
      @Context AppUser pUser, @PathParam("invitationId") ObjectId pInvitationId)
      throws SvcException {
    Invitation invitation = invitationSvc.findByInvitationId(pInvitationId);
    if (invitation == null || !invitation.getUsername().equals(pUser.getUsername())) {
      throw new SvcException(AppUserErrorCode.INVALID_INVITATION, pUser.getUsername());
    }
    userSvc.processInvitation(pUser, invitation.getToken(), null);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/invitations/{invitationId}/decline")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(groupSource = GroupSource.NONE, roles = RoleSet.ANY_AUTHENTICATED_USER)
  public Response declineInvite(
      @Context AppUser pUser, @PathParam("invitationId") ObjectId pInvitationId)
      throws SvcException {
    Invitation invitation = invitationSvc.findByInvitationId(pInvitationId);
    if (invitation == null || !invitation.getUsername().equals(pUser.getUsername())) {
      throw new SvcException(AppUserErrorCode.INVALID_INVITATION, pUser.getUsername());
    }
    invitationSvc.removeByInvitationId(pInvitationId);
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/activationCode/{code}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false)
  public Response redirectActivationCode(@PathParam("code") String pActivationCode)
      throws URISyntaxException, SvcException {

    if (appSettings.isSecurityBackdoorEnabled()
        && pActivationCode.equals(SalesSoldDealActivationSvc.TEST_EMPLOYEE_ACTIVATION_CODE)) {
      String activationCode =
          salesSoldDealActivationSvc.generateTestEmployeeActivationCode(
              appSettings.getAppEnv().isGovCloud());
      return seeOther(getActivationCodeRedirectURL("register", activationCode, null));
    }

    var isActivationCodeAvailable =
        salesSoldDealActivationSvc.isAvailableActivationCode(pActivationCode);

    ErrorCode errorCode;
    String route;

    if (isActivationCodeAvailable) {
      route = "register";
      errorCode = null;
    } else {
      route = "login";
      errorCode = AppUserErrorCode.MISSING_VALID_INVITE_TOKEN_OR_ACTIVATION_CODE;
    }

    return seeOther(getActivationCodeRedirectURL(route, pActivationCode, errorCode));
  }

  @GET
  @Path("/login")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  public Response loginPage(@Context HttpServletRequest pRequest) throws Exception {
    // We cannot include the #/login in this redirect as it strips any
    // other # paths that may have been sent, such as next params.
    // Due to # being a client-side only value that the server can't see
    // we need to ensure that this persists so the client can save where
    // to next redirect the user.
    try {
      return seeOther("/account/login" + getQueryString(pRequest));
    } catch (URISyntaxException e) {
      LOG.warn("client provided an invalid query", e);
      return seeOther("/account/login");
    }
  }

  @GET
  @Path("/resetPassword")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  public Response resetPassword() throws Exception {
    return seeOther(createRegistrationUrl("reset/password"));
  }

  @GET
  @Path("/reset/password/{tempId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  public Response resetPassword(
      @Context HttpServletRequest pRequest, @PathParam("tempId") String pTempId) throws Exception {
    return seeOther(createRegistrationUrl("reset/password/" + pTempId) + getQueryString(pRequest));
  }

  // Note: This is currently used by support to reset a user's password when they invite new users
  @POST
  @Path("/resetRequest")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  public Response resetPasswordFormSend(@Context HttpServletRequest pRequest, String pParams) {
    JSONObject params = new JSONObject(pParams);

    String username = (params.has("username")) ? params.getString("username") : null;
    AuthType resetType = AuthType.valueOf(params.getString("resetType"));
    boolean isAtlas = params.optBoolean("nds", false);

    try {
      if (accountUserSvc.isAccountBeingDeleted(username)) {
        LOG.info(
            "User {} is soft deleted, and cannot reset their password", kv("username", username));
      } else {
        userSvc.sendResetEmail(
            resetType,
            StringUtils.trimToNull(username),
            auditInfoSvc.fromUiCall(null, pRequest.getRemoteAddr()),
            isAtlas);
      }
    } catch (SvcException e) {
      // for security purposes we must always return an OK response as to not reveal
      // valid/invalid
      // user emails
      LOG.info("Failed to reset password", e);
    }

    return SimpleApiResponse.ok().resource(pRequest.getRequestURI()).build();
  }

  @POST
  @Path("/resetComplete")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "resetSend",
      types = {Type.IP, Type.PAYLOAD})
  public Response resetSend(@Context HttpServletRequest pRequest, String pParams) throws Exception {
    JSONObject params = new JSONObject(pParams);
    String username = StringUtils.trimToNull(getJSONParam("username", params::getString));
    String password = StringUtils.trimToNull(getJSONParam("password", params::getString));
    String passwordConfirm =
        StringUtils.trimToNull(getJSONParam("passwordConfirm", params::getString));
    String apiKey = StringUtils.trimToNull(getJSONParam("apiKey", params::getString));
    String tempId = StringUtils.trimToNull(getJSONParam("tempId", params::getString));
    String dbUsername = StringUtils.trimToNull(getJSONParam("dbUsername", params::getString));
    String dbPassword = StringUtils.trimToNull(getJSONParam("dbPassword", params::getString));
    Boolean nds = getJSONParam("nds", params::getBoolean);
    String ipAddress = pRequest.getRemoteAddr();
    AuthType resetType = AuthType.valueOf(params.getString("resetType"));
    AppUser user = userSvc.findByUsername(username);

    if (resetType == AuthType.PASSWORD) {
      userSvc.resetPassword(
          username, password, passwordConfirm, tempId, auditInfoSvc.fromUiCall(user, ipAddress));
    } else {
      if (nds == Boolean.TRUE) {
        try {
          userSvc.resetMultiFactorAuthForAtlas(
              username,
              password,
              dbUsername,
              dbPassword,
              tempId,
              auditInfoSvc.fromUiCall(user, ipAddress));
        } catch (SvcException e) {
          if (e.getErrorCode() == NDSErrorCode.DATABASE_ACCOUNT_CANNOT_BE_FOUND) {
            if (CollectionUtils.isNotEmpty(user.getTeamIds())) {
              Set<ObjectId> userTeamGroupIds = groupSvc.findIdsByTeamIds(user.getTeamIds());
              boolean isValidCredentials =
                  groupSvc.hasGroupWithAtlasMongoDbAccount(
                      dbUsername,
                      NDSDBUserView.getPasswordHash(dbUsername, dbPassword),
                      new ArrayList<>(userTeamGroupIds));
              if (isValidCredentials) {
                // Reset isn't allowed if group membership is via a team, but we validate the
                // credentials to give a better error message
                SimpleApiResponse.Builder responseBuilder =
                    SimpleApiResponse.badRequest(
                        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_USER_NOT_IN_GROUP);
                return responseBuilder.build();
              }
            }
          }
          throw e;
        }
      } else {
        userSvc.resetMultiFactorAuth(
            username, password, apiKey, tempId, auditInfoSvc.fromUiCall(user, ipAddress));
      }
    }
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/signout")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response signOut(
      @Context HttpServletRequest pRequest, @Context HttpServletResponse pResponse)
      throws Exception {
    userLoginSvc.doSignOut(pRequest, pResponse);
    return seeOther("/account/login?signedOut=true");
  }

  @POST
  @Path("/logout")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(auth = false)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response logout(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AppUser pAppUser)
      throws Exception {
    if (pAppUser == null) {
      return Response.ok().build();
    }

    userLoginSvc.doSignOut(pRequest, pResponse);
    pResponse.addHeader("Clear-Site-Data", "\"cache\"");

    return Response.ok().build();
  }

  @GET
  @Path("/setLastDb")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public String setLastDb(@Context AppUser pUser, @QueryParam("n") String pDbName) {
    if (StringUtils.isEmpty(pDbName)) {
      userSvc.unsetLastDb(pUser.getId());
    } else {
      userSvc.setLastDb(pUser.getId(), pDbName);
    }

    return EMPTY_JSON_OBJECT;
  }

  @POST
  @Path("/profile/updateEmail")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateEmailAddress(
      @Context AppUser pUser,
      @Context AuditInfo pAuditInfo,
      @FormParam("emailAddress") String pEmailAddress)
      throws Exception {

    userSvc.validateEmailAddress(pEmailAddress);
    String currentEmailAddress = pUser.getPrimaryEmail();

    JSONObject result = new JSONObject();

    userSvc.updateEmailAddress(pUser.getUsername(), pEmailAddress, pAuditInfo);

    result.put("ok", 1);
    result.put("emailAddress", pEmailAddress);
    result.put("originalEmail", currentEmailAddress);

    return Response.ok(result.toString()).build();
  }

  @POST
  @Path("/profile/updateMobilePhoneNumber")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response updateMobilePhoneNumber(
      @Context AppUser pUser, @FormParam("mobilePhoneNumber") String pMobilePhoneNumber)
      throws SvcException {
    String mobilePhoneNumber = StringUtils.trimToNull(pMobilePhoneNumber);

    userSvc.validateMobilePhoneNumber(mobilePhoneNumber);
    userSvc.updatePhoneNumber(pUser, mobilePhoneNumber);

    return SimpleApiResponse.ok().customField("mobilePhoneNumber", mobilePhoneNumber).build();
  }

  @POST
  @Path("/v1/auth")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response authV1(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      UserLoginForm pUserLoginForm)
      throws Exception {
    String userAddr = pRequest.getRemoteAddr();

    AppUser user;

    try {
      boolean validReCaptchaResponse =
          recaptchaSvc.validateCaptchaResponse(
              Fields.RECAPTCHA_ENABLED_LOGIN.value, pUserLoginForm.getReCaptchaResponse());

      user =
          userSvc.authenticate(
              pUserLoginForm.getUsername(),
              pUserLoginForm.getPassword(),
              pUserLoginForm.getInviteToken(),
              validReCaptchaResponse,
              pAuditInfo);
    } catch (SvcException e) {
      LOG.info(
          "Login attempt from addr=\"{}\" username=\"{}\" result={}",
          userAddr,
          pUserLoginForm.getUsername(),
          e.getErrorCode());
      if (e.getErrorCode() == AppUserErrorCode.INVALID_CAPTCHA) {
        throw e;
      }
      return translateAuthErrorCode(
          pRequest, pUserLoginForm.getUsername(), userAddr, e.getErrorCode());
    }

    LOG.info(
        "Login attempt from addr=\"{}\" username=\"{}\" result={}",
        userAddr,
        pUserLoginForm.getUsername(),
        CommonErrorCode.SUCCESS);

    // at this point, the login is successful
    return userLoginSvc
        .processSuccessfulLogin(pRequest, pResponse, user)
        .getSuccessfulLoginResponse();
  }

  @POST
  @Path("/checkPassword")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response checkPassword(
      @Context HttpServletRequest pRequest,
      @Context AppUser pUser,
      @FormParam("password") String pPassword)
      throws SvcException {
    userSvc.checkPassword(pUser, pPassword, userLoginSvc.getUiAuthCode(pRequest));
    return SimpleApiResponse.ok().build();
  }

  /**
   * If the user has an existing Cloud session, just redirect them to the correct location,
   * otherwise log them in through Okta. This assumes the user already has an Okta session
   */
  @GET
  @Path("/detectSession")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false)
  public Response detectSession(
      @Context HttpServletResponse pResponse, @Context UriInfo pUriInfo, @Context AppUser pUser)
      throws Exception {
    if (pUser == null) {
      return Response.seeOther(
              new URI(
                  oAuthSvc.getCloudLoginRedirect(
                      pResponse, new EmptyOAuthMethod(), getQueryStringMapFromUriInfo(pUriInfo))))
          .build();
    } else {
      // Go to the splash page and keep query params to follow a normal cloud login redirect
      return seeOther("/");
    }
  }

  @GET
  @Path("/federationSettings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response getFederationSettings(@Context AppUser pAppUser) {
    Optional<String> maybeDomain = userSvc.getDomainFromEmailAddress(pAppUser.getUsername());

    if (maybeDomain.isEmpty()) {
      return SimpleApiResponse.notFound().build();
    }

    Optional<FederationSettings> maybeFederationSettings =
        federationSettingsSvc.findActiveByIdentityProviderAssociatedDomain(maybeDomain.get());

    if (maybeFederationSettings.isEmpty()) {
      return SimpleApiResponse.notFound().build();
    }

    FederationSettings federationSettings = maybeFederationSettings.get();

    return Response.ok(
            new FederatedUserView(
                pAppUser.getId(),
                federationSettings.getId(),
                pAppUser.getLastAuthMethod(),
                federationSettings.isRestrictOrgMembershipEnabled()))
        .build();
  }

  @POST
  @Path("/refreshSession")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(auth = false)
  public Response refreshSession(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AppUser appUser)
      throws SvcException {
    IAM_REFRESH_SESSION_COUNTER.inc();

    if (appUser == null) {
      throw new SvcException(AppUserErrorCode.USER_NOT_FOUND);
    }

    String code = FilterUtils.findUiAuthCode(appSettings, pRequest);
    if (code == null) {
      throw new SvcException(AppUserErrorCode.NO_ACTIVE_SESSION);
    }

    var uiAuthCode = appUser.findUiAuthCode(code);
    userLoginSvc.refreshIdleSession(pRequest, pResponse, uiAuthCode);

    return SimpleApiResponse.ok().build();
  }

  @PUT
  @Path("/deleteMarketplaceRegistrationData")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response deleteMarketplaceRegistrationData(@Context AppUser pUser) {
    userSvc.unsetMarketplaceRegistrationData(pUser.getId());
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/authCodeCreationTimestamp")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response getAuthCodeCreationTimestamp(
      @Context HttpServletRequest request, @Context AppUser user) throws SvcException {
    String requestUiAuthCode = FilterUtils.findUiAuthCode(appSettings, request);

    if (requestUiAuthCode == null) {
      throw new SvcException(AppUserErrorCode.NO_ACTIVE_SESSION);
    }

    try {
      UiAuthCode authCodeMetadata = user.findUiAuthCode(requestUiAuthCode);
      long authCodeCreationTimestamp = authCodeMetadata.getCreated().getTime();
      return SimpleApiResponse.ok()
          .customField("authCodeCreationTimestamp", authCodeCreationTimestamp)
          .build();
    } catch (Exception e) {
      throw new SvcException(AppUserErrorCode.NO_ACTIVE_SESSION);
    }
  }

  private AppUser registerUser(
      UserRegistrationForm pForm, boolean pExternalRegister, AuditInfo pAuditInfo)
      throws SvcException {
    if (pForm.getHoneypot() != null) {
      // should not be filled in, unless by bots
      throw new ServerError();
    }

    if (iamAppSettings.isDbMaintenanceErrorEnabled()) {
      throw new DbMaintenanceException();
    }

    boolean validReCaptchaEnterpriseAssessment = true;
    final String username = pForm.getUsername();
    if (pExternalRegister && iamAppSettings.getRecaptchaExternalRegistrationEnabled()) {
      validReCaptchaEnterpriseAssessment =
          recaptchaSvc.validateCaptchaEnterpriseAssessment(
              pForm.getReCaptchaToken(),
              username,
              ReCaptchaAction.EXTERNAL_REGISTER,
              iamAppSettings.getRecaptchaExternalRegistrationMinScore());
    } else if (!pExternalRegister && iamAppSettings.getRecaptchaRegistrationEnabled()) {
      validReCaptchaEnterpriseAssessment =
          recaptchaSvc.validateCaptchaEnterpriseAssessment(
              pForm.getReCaptchaToken(),
              username,
              ReCaptchaAction.REGISTER,
              iamAppSettings.getRecaptchaRegistrationMinScore());
    }

    if (!validReCaptchaEnterpriseAssessment) {
      throw new SvcException(AppUserErrorCode.INVALID_CAPTCHA);
    }

    if (isSelfServeForbiddenRegistrationAttempted(pForm, pExternalRegister)) {
      throw new SvcException(AppUserErrorCode.REGISTRATION_DISABLED);
    }

    if (StringUtils.isBlank(pAuditInfo.getRemoteAddr())) {
      LOG.warn(
          "{} registration received [username={}] without an associated IP address",
          pExternalRegister ? "External" : "Internal",
          pForm.getUsername());
    }

    return userSvc.register(pForm, pAuditInfo);
  }

  /**
   * Checks if self-serve registrations are disabled and there's no invitation token or activation
   * code present in the form, or if it's an external registration attempt
   */
  private boolean isSelfServeForbiddenRegistrationAttempted(
      UserRegistrationForm pForm, boolean pExternalRegister) {
    if (userSvc.isUserRegistrationEnabled()) {
      return false;
    }
    if (pExternalRegister) {
      return true;
    }
    return StringUtils.isEmpty(pForm.getInvitationToken())
        && StringUtils.isEmpty(pForm.getActivationCode());
  }

  private String getInviteRedirectURL(
      String pRoute,
      Invitation pInvitation,
      Organization pOrganization,
      Group pGroup,
      String pIdpId) {

    Map<String, String> queryParams =
        getInviteQueryParams(pInvitation, pOrganization, pGroup, pIdpId);

    return createRegistrationUrl(pRoute) + getQueryString(queryParams);
  }

  private Map<String, String> getInviteQueryParams(
      Invitation pInvitation, Organization pOrganization, Group pGroup, String pIdpId) {
    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("username", pInvitation.getUsername());
    queryParams.put("inviteToken", pInvitation.getToken());

    if (pInvitation.getInviterUsername() != null) {
      queryParams.put("inviterUsername", pInvitation.getInviterUsername());
    }

    if (!pInvitation.getGroupRoleAssignments().isEmpty()) {
      queryParams.put(
          "groupIdsFromGroupRoleAssignments[]",
          pInvitation.getGroupRoleAssignments().stream()
              .map(GroupRole::getGroupId)
              .distinct()
              .map(ObjectId::toString)
              .collect(Collectors.joining("|")));
    }

    if (pGroup != null) {
      queryParams.put("groupId", pGroup.getId().toString());
      queryParams.put("groupname", pGroup.getName());
    }

    if (pOrganization != null) {
      queryParams.put("orgId", pOrganization.getId().toString());
      queryParams.put("orgname", pOrganization.getName());
    }

    if (StringUtils.isNotEmpty(pIdpId)) {
      queryParams.put("idp", pIdpId);
    }

    return queryParams;
  }

  private String getActivationCodeRedirectURL(
      String pRoute, String pActivationCode, ErrorCode pErrorCode) {
    var queryParams = new HashMap<String, String>();
    if (pErrorCode == null) {
      queryParams.put("activationCode", pActivationCode);
    } else {
      queryParams.put("reason", pErrorCode.name());
    }
    return createRegistrationUrl(pRoute) + getQueryString(queryParams);
  }

  private Response translateAuthErrorCode(
      HttpServletRequest pRequest, String pUsername, String userAddr, ErrorCode authResponse) {
    if (authResponse == AppUserErrorCode.INVALID_PASSWORD_TOO_SHORT
        || authResponse == AppUserErrorCode.INVALID_PASSWORD_EXPIRED
        || authResponse == AppUserErrorCode.PASSWORD_IS_EMPTY) {
      return SimpleApiResponse.badRequest(authResponse)
          .resource(userLoginSvc.createPasswordResetTokenPath(pUsername, userAddr))
          .build();
    }

    return SimpleApiResponse.badRequest(authResponse).resource(pRequest.getRequestURI()).build();
  }

  private UserSvcOkta getUserSvcOkta() {
    // All methods calling this getter are really expecting Okta to be on. If it's not and this cast
    // fails, it's actually a good thing this throws an exception
    return (UserSvcOkta) userSvc.asRuntimeInstance();
  }
}
