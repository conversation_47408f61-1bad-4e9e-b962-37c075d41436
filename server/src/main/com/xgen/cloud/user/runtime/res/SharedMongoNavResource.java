package com.xgen.cloud.user.runtime.res;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.alert.Alert;
import com.xgen.cloud.alerts.alert._public.svc.AlertSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.group._public.view.GroupInfoView;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStatus;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.organization._public.view.OrgInfoView;
import com.xgen.cloud.organization._public.view.OrgPlanType;
import com.xgen.cloud.organization._public.view.SharedMongoNavView;
import com.xgen.cloud.sfdc._public.util.SalesforceUtils;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.module.account.model.AccountUser;
import com.xgen.module.account.svc.AccountUserSvc;
import com.xgen.svc.mms.model.ChartsConfig;
import com.xgen.svc.mms.model.UpdateSharedMongoNavView;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.svc.ChartsSvc;
import com.xgen.svc.mms.svc.NDSOrgSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/user/shared")
@Singleton
public class SharedMongoNavResource extends BaseResource {
  private static final int SEARCH_MAX_RETURN_SIZE = 50;
  private static final Logger log = LoggerFactory.getLogger(SharedMongoNavResource.class);

  private final GroupSvc groupSvc;
  private final UserSvc userSvc;
  private final AccountUserSvc accountUserSvc;
  private final OrganizationSvc organizationSvc;
  private final NDSOrgSvc ndsOrgSvc;
  private final InvitationSvc invitationSvc;
  private final ChartsSvc chartsSvc;
  private final AlertSvc alertSvc;
  private final AuthzSvc authzSvc;
  private final AppSettings appSettings;

  @Inject
  public SharedMongoNavResource(
      GroupSvc pGroupSvc,
      UserSvc pUserSvc,
      OrganizationSvc pOrganizationSvc,
      NDSOrgSvc pNDSOrgSvc,
      InvitationSvc pInvitationSvc,
      ChartsSvc pChartsSvc,
      AlertSvc pAlertSvc,
      AuthzSvc pAuthzSvc,
      AppSettings pAppSettings,
      AccountUserSvc pAccountUserSvc) {
    groupSvc = pGroupSvc;
    userSvc = pUserSvc;
    organizationSvc = pOrganizationSvc;
    ndsOrgSvc = pNDSOrgSvc;
    invitationSvc = pInvitationSvc;
    chartsSvc = pChartsSvc;
    alertSvc = pAlertSvc;
    authzSvc = pAuthzSvc;
    appSettings = pAppSettings;
    accountUserSvc = pAccountUserSvc;
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      groupSource = UiCall.GroupSource.NONE,
      csrf = false)
  @AllowCORS({KnownCrossOrigin.CHARTS, KnownCrossOrigin.REALM, KnownCrossOrigin.BAAS})
  public Response updateCurrentProjectOrOrg(
      @Context HttpServletRequest pRequest,
      @Context AppUser pAppUser,
      UpdateSharedMongoNavView pUpdateSharedMongoNavView) {
    ObjectId activeOrgId = pUpdateSharedMongoNavView.getActiveOrgId().orElse(null);
    ObjectId activeProjectId = pUpdateSharedMongoNavView.getActiveProjectId().orElse(null);

    // Post MongoNav deprecation, Charts will still require logic to update user project/org
    // context.
    // Share the logic in the meantime but logic has moved to ChartsSvc to avoid accidental
    // deletion.
    final AppUser updatedUser =
        getChartsSvc().updateCurrentProjectOrOrg(pAppUser, activeProjectId, activeOrgId);

    return Response.ok().entity(getSharedMongoNav(updatedUser)).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = UiCall.GroupSource.NONE)
  @AllowCORS({
    KnownCrossOrigin.CHARTS,
    KnownCrossOrigin.DEV_HUB,
    KnownCrossOrigin.MARKETING,
    KnownCrossOrigin.REALM,
    KnownCrossOrigin.BAAS,
    KnownCrossOrigin.UNIVERSITY,
  })
  public Response sharedMongoNav(@Context HttpServletRequest pRequest, @Context AppUser pAppUser) {
    return Response.ok().entity(getSharedMongoNav(pAppUser)).build();
  }

  private SharedMongoNavView getSharedMongoNav(AppUser pAppUser) {
    List<Invitation> invitations = invitationSvc.findByUsername(pAppUser.getUsername());
    Optional<AccountUser> accountUserOptional =
        accountUserSvc.findByOktaUserId(pAppUser.getOktaUserId());

    SharedMongoNavView.AccountView account =
        new SharedMongoNavView.AccountView(
            pAppUser.getFirstName(),
            pAppUser.getLastName(),
            pAppUser.getUsername(),
            pAppUser.getPrimaryEmail(),
            invitations.size(),
            getAuthzSvc().isGlobalAdminReadOnly(pAppUser),
            pAppUser.hasMultiFactorAuth(),
            accountUserOptional.isEmpty()
                ? getAppSettings().isAccountMultiFactorAuthEnabled()
                : getAppSettings().isAccountMultiFactorAuthEnabled()
                    && !accountUserOptional.get().isSocialUser()
                    && !accountUserOptional.get().isFederatedUser());

    var currentOrgId = pAppUser.getCurrentOrgId();
    var currentProjectId = pAppUser.getCurrentGroupId();

    Set<ObjectId> orgIds = new HashSet<>(pAppUser.getOrgIds());
    if (authzSvc.hasAnyGlobalRole(pAppUser) && currentOrgId != null) {
      orgIds.add(currentOrgId);
    }
    Set<Organization> organizations = new HashSet<>(getOrganizationSvc().findByIds(orgIds));

    Organization currentOrganization =
        organizations.stream()
            .filter(org -> org.getId().equals(currentOrgId))
            .findFirst()
            .orElse(null);

    LinkedHashSet<SharedMongoNavView.OrganizationView> organizationViews =
        organizations.stream()
            .distinct()
            .sorted(Comparator.comparing(Organization::getName, String.CASE_INSENSITIVE_ORDER))
            .map(
                org ->
                    new SharedMongoNavView.OrganizationView(
                        org.getId(),
                        org.getName(),
                        OrgPlanType.fromGroupType(org.getGroupType()),
                        org.getPaymentStatus().getStatus()))
            .collect(Collectors.toCollection(LinkedHashSet::new));

    SharedMongoNavView.OrganizationView currentOrganizationView =
        organizationViews.stream()
            .filter(org -> org.orgId.equals(currentOrgId))
            .map(
                org ->
                    new SharedMongoNavView.OrganizationView(
                        org.orgId, org.orgName, org.orgPlanType, org.paymentStatusType))
            .findFirst()
            .orElseGet(
                () -> {
                  if (authzSvc.hasAnyGlobalRole(pAppUser) && currentOrganization != null) {
                    return new SharedMongoNavView.OrganizationView(
                        currentOrganization.getId(),
                        currentOrganization.getName(),
                        OrgPlanType.fromGroupType(currentOrganization.getGroupType()),
                        currentOrganization.getPaymentStatus().getStatus());
                  }

                  return null;
                });

    List<Group> directProjects =
        pAppUser.getGroupIds().isEmpty()
            ? Collections.emptyList()
            : getGroupSvc().findByIds(pAppUser.getGroupIds());

    Set<ObjectId> teamIds = pAppUser.getTeamIds();
    List<Group> teamProjects =
        teamIds == null || teamIds.isEmpty()
            ? Collections.emptyList()
            : getGroupSvc().findByTeamIds(teamIds);

    List<Group> currentOrganizationGroups =
        currentOrganization == null
            ? Collections.emptyList()
            : getGroupSvc().getVisibleGroups(currentOrganization.getId(), pAppUser);

    Set<Group> allProjects =
        Stream.of(directProjects, teamProjects, currentOrganizationGroups)
            .flatMap(Collection::stream)
            .filter(group -> !group.isSystemProject() || authzSvc.isSystemProjectAdmin(pAppUser))
            .collect(Collectors.toSet());

    LinkedHashSet<SharedMongoNavView.ProjectView> projectViews =
        allProjects.stream()
            .distinct()
            .sorted(Comparator.comparing(Group::getName, String.CASE_INSENSITIVE_ORDER))
            .map(
                project ->
                    new SharedMongoNavView.ProjectView(
                        project.getId(),
                        project.getName(),
                        OrgPlanType.fromGroupType(project.getGroupType()),
                        project.getOrgId(),
                        project.getStatus() == null
                            ? GroupStatus.Type.ACTIVE
                            : project.getStatus().getStatus(),
                        project.useCNRegionsOnly()))
            .collect(Collectors.toCollection(LinkedHashSet::new));

    ChartsConfig chartsConfig = getChartsSvc().getChartsConfig(currentProjectId);
    var chartsActivated =
        chartsConfig != null
            && chartsConfig.getStatus() != ChartsConfig.Status.ACTIVATING
            && chartsConfig.getStatus() != ChartsConfig.Status.ACTIVATION_FAILED;
    long projectAlertsOpen = alertSvc.getUnacknowledgedOpenAlertCountForGroup(currentProjectId);

    Group currentProject =
        allProjects.stream()
            .filter(project -> project.getId().equals(currentProjectId))
            .findFirst()
            .orElseGet(
                () -> {
                  if (authzSvc.hasAnyGlobalRole(pAppUser) && currentProjectId != null) {
                    return getGroupSvc().findById(currentProjectId);
                  }

                  return null;
                });

    SharedMongoNavView.CurrentProjectView currentProjectView =
        projectViews.stream()
            .filter(project -> project.projectId.equals(currentProjectId))
            .map(
                project ->
                    new SharedMongoNavView.CurrentProjectView(
                        project.projectId,
                        project.projectName,
                        project.orgPlanType,
                        project.orgId,
                        project.statusType,
                        projectAlertsOpen,
                        chartsActivated,
                        project.useCNRegionsOnly))
            .findFirst()
            .orElseGet(
                () -> {
                  if (authzSvc.hasAnyGlobalRole(pAppUser) && currentProject != null) {
                    return new SharedMongoNavView.CurrentProjectView(
                        currentProject.getId(),
                        currentProject.getName(),
                        OrgPlanType.fromGroupType(currentProject.getGroupType()),
                        currentProject.getOrgId(),
                        currentProject.getStatus() != null
                            ? currentProject.getStatus().getStatus()
                            : GroupStatus.Type.ACTIVE,
                        projectAlertsOpen,
                        chartsActivated,
                        currentProject.useCNRegionsOnly());
                  }

                  return null;
                });

    return new SharedMongoNavView(
        account, organizationViews, projectViews, currentOrganizationView, currentProjectView);
  }

  @GET
  @Path("/alerts/project/{groupId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, groupSource = UiCall.GroupSource.PATH)
  @AllowCORS({KnownCrossOrigin.CHARTS, KnownCrossOrigin.REALM, KnownCrossOrigin.BAAS})
  @Auth(endpointAction = "epa.project.SharedMongoNavResource.sharedMongoNavProjectAlerts.GET")
  public Response sharedMongoNavProjectAlerts(
      @Context HttpServletRequest pRequest, @Context AppUser pAppUser, @Context Group pGroup) {
    List<Alert> openAlerts = alertSvc.getUnacknowledgedOpenAlertsForGroup(pGroup.getId());
    return Response.ok().entity(openAlerts.toArray(new Alert[] {})).build();
  }

  @GET
  @Path("/organizations/search")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @AllowCORS({KnownCrossOrigin.CHARTS, KnownCrossOrigin.REALM, KnownCrossOrigin.BAAS})
  @Auth(endpointAction = "epa.global.SharedMongoNavResource.searchOrganizations.GET")
  public Response searchOrganizations(@Context AppUser pUser, @QueryParam("term") String pTerm) {
    // don't search if a term is not provided
    if (StringUtils.isBlank(pTerm)) {
      return Response.ok().entity(Collections.emptyList()).build();
    }

    // first, check if the term is a valid activation code
    if (SalesforceUtils.isValidActivationCode(appSettings.getAppEnv(), pTerm)) {
      List<OrgInfoView> orgsByActivationCode =
          getNdsOrgSvc().searchOrgsByActivationCode(pTerm, pUser, SEARCH_MAX_RETURN_SIZE, false);
      if (!orgsByActivationCode.isEmpty()) {
        return Response.ok().entity(orgsByActivationCode).build();
      }
    }

    // next, check if the term is a valid orgId
    if (ObjectId.isValid(pTerm)) {
      OrgInfoView orgById = getNdsOrgSvc().searchOrgById(pTerm, pUser, false);
      if (orgById != null) {
        return Response.ok().entity(Collections.singletonList(orgById)).build();
      }
    }

    // next, check if there are results from a prefix search
    List<OrgInfoView> orgsByPrefix =
        getNdsOrgSvc().searchOrgsByPrefix(pTerm, pUser, SEARCH_MAX_RETURN_SIZE, false);
    orgsByPrefix.sort(Comparator.comparing(OrgInfoView::getOrgName));

    if (!orgsByPrefix.isEmpty()) {
      return Response.ok().entity(orgsByPrefix).build();
    }

    // finally, fall back to a full regex search
    List<OrgInfoView> orgsByScan =
        getNdsOrgSvc().searchOrgsByScan(pTerm, pUser, SEARCH_MAX_RETURN_SIZE, false);
    return Response.ok().entity(orgsByScan).build();
  }

  @GET
  @Path("/projects/search")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_READ_ONLY, groupSource = UiCall.GroupSource.NONE)
  @AllowCORS({KnownCrossOrigin.CHARTS, KnownCrossOrigin.REALM, KnownCrossOrigin.BAAS})
  @Auth(endpointAction = "epa.global.SharedMongoNavResource.searchProjects.GET")
  public Response searchProjects(@Context AppUser pUser, @QueryParam("term") String pTerm) {
    // don't search if a term is not provided
    if (StringUtils.isBlank(pTerm)) {
      return Response.ok().entity(Collections.emptyList()).build();
    }

    // first, check if the term is a valid groupId
    if (ObjectId.isValid(pTerm)) {
      GroupInfoView groupById = getGroupSvc().searchGroupById(pTerm, pUser, false);
      if (groupById != null) {
        return Response.ok().entity(Collections.singletonList(groupById)).build();
      }
    }

    // next, check if there are results from a prefix search
    List<GroupInfoView> groupsByPrefix =
        getGroupSvc().searchGroupsByPrefix(pTerm, pUser, SEARCH_MAX_RETURN_SIZE, false);
    if (!groupsByPrefix.isEmpty()) {
      return Response.ok().entity(groupsByPrefix).build();
    }

    // finally, fall back to a full regex search
    List<GroupInfoView> groupsByScan =
        getGroupSvc().searchGroupsByScan(pTerm, pUser, SEARCH_MAX_RETURN_SIZE, false);
    return Response.ok().entity(groupsByScan).build();
  }

  protected GroupSvc getGroupSvc() {
    return groupSvc;
  }

  public UserSvc getUserSvc() {
    return userSvc;
  }

  public OrganizationSvc getOrganizationSvc() {
    return organizationSvc;
  }

  public NDSOrgSvc getNdsOrgSvc() {
    return ndsOrgSvc;
  }

  public InvitationSvc getInvitationSvc() {
    return invitationSvc;
  }

  public ChartsSvc getChartsSvc() {
    return chartsSvc;
  }

  public AlertSvc getAlertSvc() {
    return alertSvc;
  }

  public AuthzSvc getAuthzSvc() {
    return authzSvc;
  }

  private AppSettings getAppSettings() {
    return appSettings;
  }
}
