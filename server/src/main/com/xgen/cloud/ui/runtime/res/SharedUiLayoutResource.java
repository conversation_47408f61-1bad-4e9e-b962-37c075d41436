package com.xgen.cloud.ui.runtime.res;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.xgen.cloud.abtesting._public.svc.ABTestSvc;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billing._public.svc.IPayingOrgSvc;
import com.xgen.cloud.billingplatform.crossorg._public.svc.CrossOrgValidationSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.federation._public.model.FederationSettings;
import com.xgen.cloud.federation._public.svc.FederationSettingsSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.organization._public.view.OrgPlanType;
import com.xgen.cloud.ui._public.view.AccountView;
import com.xgen.cloud.ui._public.view.AppIdsView;
import com.xgen.cloud.ui._public.view.HostnamesView;
import com.xgen.cloud.ui._public.view.ResourcesView;
import com.xgen.cloud.ui._public.view.SharedUiLayoutView;
import com.xgen.cloud.ui._public.view.resources.organization.OrganizationMetadataView;
import com.xgen.cloud.ui._public.view.resources.organization.OrganizationResourceView;
import com.xgen.cloud.ui._public.view.resources.project.ProjectMetadataView;
import com.xgen.cloud.ui._public.view.resources.project.ProjectResourceView;
import com.xgen.cloud.ui._public.view.visibility.AtlasOrganizationVisibilityView;
import com.xgen.cloud.ui._public.view.visibility.CloudManagerOrganizationVisibilityView;
import com.xgen.cloud.ui._public.view.visibility.CloudManagerProjectVisibilityView;
import com.xgen.cloud.ui._public.view.visibility.ElementVisibilityView;
import com.xgen.cloud.ui._public.view.visibility.OrganizationVisibilityView;
import com.xgen.cloud.ui._public.view.visibility.ProjectVisibilityView;
import com.xgen.cloud.ui._public.view.visibility.UtilitiesVisibilityView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.svc.ChartsSvc;
import com.xgen.svc.mms.svc.alert.OrgAlertSvc;
import com.xgen.svc.nds.svc.IngestionPipelineUISvc;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/ui/layout")
@Singleton
public class SharedUiLayoutResource extends BaseResource {
  private static final Logger log = LoggerFactory.getLogger(SharedUiLayoutResource.class);

  private final GroupSvc _groupSvc;
  private final OrganizationSvc _organizationSvc;
  private final AppSettings _appSettings;
  private final FederationSettingsSvc _federationSettingsSvc;
  private final AuthzSvc _authzSvc;
  private final ChartsSvc _chartsSvc;
  private final OrgAlertSvc _orgAlertsSvc;
  private final CrossOrgValidationSvc _crossOrgValidationSvc;
  private final IPayingOrgSvc _payingOrgSvc;
  private final ABTestSvc _abTestSvc;
  private final IngestionPipelineUISvc _ingestionPipelineUiSvc;

  @Inject
  public SharedUiLayoutResource(
      final GroupSvc pGroupSvc,
      final OrganizationSvc pOrganizationSvc,
      final AppSettings pAppSettings,
      final FederationSettingsSvc pFederationSettingsSvc,
      final AuthzSvc pAuthzSvc,
      final ChartsSvc pChartsSvc,
      final OrgAlertSvc pOrgAlertSvc,
      final CrossOrgValidationSvc crossOrgValidationSvc,
      final IPayingOrgSvc payingOrgSvc,
      final ABTestSvc abTestSvc,
      final IngestionPipelineUISvc ingestionPipelineUiSvc) {
    _groupSvc = pGroupSvc;
    _organizationSvc = pOrganizationSvc;
    _appSettings = pAppSettings;
    _federationSettingsSvc = pFederationSettingsSvc;
    _authzSvc = pAuthzSvc;
    _chartsSvc = pChartsSvc;
    _orgAlertsSvc = pOrgAlertSvc;
    _crossOrgValidationSvc = crossOrgValidationSvc;
    _payingOrgSvc = payingOrgSvc;
    _ingestionPipelineUiSvc = ingestionPipelineUiSvc;
    _abTestSvc = abTestSvc;
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      groupSource = UiCall.GroupSource.NONE,
      csrf = false)
  @AllowCORS({KnownCrossOrigin.CHARTS, KnownCrossOrigin.REALM, KnownCrossOrigin.BAAS})
  public Response updateCurrentProjectOrOrgByAppIds(
      @Context HttpServletRequest pRequest, @Context AppUser pAppUser, AppIdsView pAppIdsView) {
    // During extended navigation, a Charts user can become de-synced from the MMS
    // AppUser's currentProjectId. This causes the CloudNav to return incorrect data.
    // Similar to SharedMongoNavResource, does nothing if roles are invalid, group doesn't
    // exist, etc.
    final AppUser updatedUser = updateAppUserFromAppIds(pAppIdsView, pAppUser);
    return Response.ok().entity(getLayoutConfig(updatedUser)).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = UiCall.GroupSource.NONE)
  @AllowCORS({
    KnownCrossOrigin.ACCOUNT,
    KnownCrossOrigin.CHARTS,
    KnownCrossOrigin.DEV_HUB,
    KnownCrossOrigin.MARKETING,
    KnownCrossOrigin.REALM,
    KnownCrossOrigin.BAAS,
    KnownCrossOrigin.UNIVERSITY,
  })
  public Response layoutConfig(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pAppUser,
      @QueryParam("orgId") final ObjectId requestedOrganizationId,
      @QueryParam("projectId") final ObjectId requestedProjectId) {
    final Boolean hasRequestedResources =
        requestedOrganizationId != null || requestedProjectId != null;

    final SharedUiLayoutView layoutConfig =
        hasRequestedResources
            ? getLayoutConfig(pAppUser, requestedOrganizationId, requestedProjectId)
            : getLayoutConfig(pAppUser);

    return Response.ok().entity(layoutConfig).build();
  }

  private SharedUiLayoutView getLayoutConfig(final AppUser pAppUser) {
    return getLayoutConfig(pAppUser, pAppUser.getCurrentOrgId(), pAppUser.getCurrentGroupId());
  }

  private SharedUiLayoutView getLayoutConfig(
      final AppUser pAppUser,
      final ObjectId requestedOrganizationId,
      final ObjectId requestedProjectId) {
    final Set<Organization> organizations =
        getAvailableOrganizations(pAppUser, requestedOrganizationId);
    final Organization currentOrganization =
        getOrganization(pAppUser, organizations, requestedOrganizationId);

    final Set<Group> projects =
        getAvailableProjects(pAppUser, currentOrganization, requestedProjectId);
    final Group currentProject = getProject(pAppUser, projects, requestedProjectId);

    return new SharedUiLayoutView(
        getAccount(pAppUser),
        getEnv(),
        getHostnames(),
        getResources(pAppUser, organizations, currentOrganization, projects, currentProject),
        getExperimentAttributes(pAppUser, currentOrganization, currentProject));
  }

  private Map<String, String> getExperimentAttributes(
      final AppUser user, final Organization organization, final Group project) {
    return _abTestSvc.getActiveFeatureFlags(user, project, organization, null, null);
  }

  private AccountView getAccount(final AppUser pAppUser) {
    return new AccountView(
        pAppUser.getFirstName(),
        pAppUser.getLastName(),
        pAppUser.getUsername(),
        getAuthzSvc().isGlobalAdminReadOnly(pAppUser));
  }

  private ResourcesView getResources(
      final AppUser pAppUser,
      final Set<Organization> organizations,
      final Organization currentOrganization,
      final Set<Group> projects,
      final Group currentProject) {
    final ObjectId currentOrganizationId =
        currentOrganization != null ? currentOrganization.getId() : null;
    final ObjectId currentProjectId = currentProject != null ? currentProject.getId() : null;

    return new ResourcesView(
        currentOrganizationId,
        getOrganizationViews(pAppUser, organizations),
        currentProjectId,
        getProjectViews(pAppUser, currentOrganization, projects, currentProjectId),
        getCurrentFederationId(currentOrganizationId));
  }

  private Set<Organization> getAvailableOrganizations(
      final AppUser pAppUser, final ObjectId requestedOrganizationId) {
    final Set<ObjectId> orgIds = new HashSet<>(pAppUser.getOrgIds());

    if (_authzSvc.hasAnyGlobalRole(pAppUser) && requestedOrganizationId != null) {
      orgIds.add(requestedOrganizationId);
    }

    return new HashSet<>(getOrganizationSvc().findByIds(orgIds));
  }

  @GET
  @Path("/alerts/organization/{orgId}/count")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = UiCall.GroupSource.NONE)
  @AllowCORS({
    KnownCrossOrigin.ACCOUNT,
    KnownCrossOrigin.CHARTS,
    KnownCrossOrigin.DEV_HUB,
    KnownCrossOrigin.MARKETING,
    KnownCrossOrigin.REALM,
    KnownCrossOrigin.BAAS,
    KnownCrossOrigin.UNIVERSITY
  })
  @Auth(endpointAction = "epa.organization.SharedUiLayoutResource.sharedUiLayoutOrgAlertCount.GET")
  public Response sharedUiLayoutOrgAlertCount(
      @Context final AppUser pAppUser, @PathParam("orgId") ObjectId orgId) {
    final long openAlertsCount = _orgAlertsSvc.getOpenAlertsCount(orgId);
    return Response.ok().entity(openAlertsCount).build();
  }

  private Organization getOrganization(
      final AppUser pAppUser,
      final Set<Organization> organizations,
      final ObjectId organizationId) {
    return organizations.stream()
        .filter(org -> org.getId().equals(organizationId))
        .findFirst()
        .orElse(null);
  }

  private Group getProject(
      final AppUser pAppUser, final Set<Group> projects, final ObjectId projectId) {
    return projects.stream()
        .filter(project -> project.getId().equals(projectId))
        .findFirst()
        .orElse(null);
  }

  private List<OrganizationResourceView> getOrganizationViews(
      final AppUser user, final Set<Organization> organizations) {
    return organizations.stream()
        .distinct()
        .sorted(Comparator.comparing(Organization::getName, String.CASE_INSENSITIVE_ORDER))
        .map(
            org ->
                new OrganizationResourceView(
                    org.getId(),
                    org.getName(),
                    new OrganizationMetadataView(
                        getOrgElementVisibilityView(user, org),
                        org.getPaymentStatus().getStatus(),
                        OrgPlanType.fromGroupType(org.getGroupType()),
                        org.isDeleted())))
        .collect(Collectors.toCollection(ArrayList::new));
  }

  private ElementVisibilityView getOrgElementVisibilityView(
      final AppUser user, final Organization org) {
    final Boolean showLinkedOrgs = shouldShowLinkedOrgs(user, org);

    return org.isCloudManager()
        ? getCloudManagerOrgElementVisibilityView(user, org, showLinkedOrgs)
        : getAtlasOrgElementVisibilityView(user, org, showLinkedOrgs);
  }

  private ElementVisibilityView getAtlasOrgElementVisibilityView(
      final AppUser user, final Organization org, final Boolean showLinkedOrgs) {
    final AtlasOrganizationVisibilityView organizationVisibility =
        new AtlasOrganizationVisibilityView();

    UtilitiesVisibilityView utilitiesVisibility =
        getUtilitiesVisibilityView(user, org, showLinkedOrgs);

    final ElementVisibilityView visibility =
        ElementVisibilityView.atlasSideNavAndUtilities(
            organizationVisibility, null, utilitiesVisibility);

    setCommonOrganizationVisibility(visibility, organizationVisibility, user, org, showLinkedOrgs);

    final boolean showCostExplorer = true;
    final boolean showTagManager = getAppSettings().isOrgTagsEnabled();
    final boolean showAdmin = getAuthzSvc().isGlobalReadOnly(user);
    final boolean showResourcePolicies =
        isFeatureFlagEnabled(FeatureFlag.ATLAS_RESOURCE_POLICIES, getAppSettings(), org, null);

    visibility
        .sideNav
        .primary
        .setCostExplorer(showCostExplorer)
        .setTagManager(showTagManager)
        .setOrgAdmin(showAdmin)
        .setResourcePolicies(showResourcePolicies);

    organizationVisibility.setCostExplorer(showCostExplorer);
    organizationVisibility.setTagManager(showTagManager);
    organizationVisibility.setAdmin(showAdmin);
    organizationVisibility.setResourcePolicies(showResourcePolicies);

    return visibility;
  }

  private ElementVisibilityView getCloudManagerOrgElementVisibilityView(
      final AppUser user, final Organization org, final Boolean showLinkedOrgs) {
    final CloudManagerOrganizationVisibilityView organizationVisibility =
        new CloudManagerOrganizationVisibilityView();
    UtilitiesVisibilityView utilitiesVisibility =
        getUtilitiesVisibilityView(user, org, showLinkedOrgs);
    final ElementVisibilityView visibility =
        ElementVisibilityView.cloudManagerSideNavAndUtilities(
            organizationVisibility, null, utilitiesVisibility);

    setCommonOrganizationVisibility(visibility, organizationVisibility, user, org, showLinkedOrgs);

    final boolean showCostExplorer = false;
    final boolean showAdmin = getAuthzSvc().isGlobalReadOnly(user);

    visibility.sideNav.primary.setCostExplorer(showCostExplorer).setCloudManagerOrgAdmin(showAdmin);

    organizationVisibility.setCostExplorer(false);
    organizationVisibility.setAdmin(showAdmin);

    return visibility;
  }

  private void setCommonOrganizationVisibility(
      final ElementVisibilityView visibility,
      final OrganizationVisibilityView organizationVisibility,
      final AppUser user,
      final Organization org,
      final Boolean showLinkedOrgs) {
    final boolean showTeams =
        !isFeatureFlagEnabled(FeatureFlag.FINE_GRAINED_AUTH, getAppSettings(), org, null);
    final boolean showUserGroups =
        isFeatureFlagEnabled(
            FeatureFlag.FINE_GRAINED_AUTH_USER_GROUPS, getAppSettings(), org, null);
    final boolean showPolicies =
        isFeatureFlagEnabled(FeatureFlag.FINE_GRAINED_AUTH, getAppSettings(), org, null);

    visibility
        .sideNav
        .primary
        .setTeams(showTeams)
        .setUserGroups(showUserGroups)
        .setPolicies(showPolicies)
        .setLinkedOrganizations(showLinkedOrgs);

    organizationVisibility
        .setTeams(showTeams)
        .setUserGroups(showUserGroups)
        .setPolicies(showPolicies)
        .setLinkedOrganizations(showLinkedOrgs);
  }

  private Boolean shouldShowLinkedOrgs(final AppUser user, final Organization org) {
    // This attempts to recreate logic from
    // client/packages/organization/components/Billing/BillingPage.tsx
    return isFeatureFlagEnabled(FeatureFlag.CROSS_ORG_BILLING, getAppSettings(), org, null)
        && userHasLinkedOrgsPermission(user, org)
        && orgHasLinkedOrgsCapability(org);
  }

  private Boolean userHasLinkedOrgsPermission(final AppUser user, final Organization org) {
    return getAuthzSvc().isGlobalReadOnly(user)
        || getAuthzSvc().isOrgBillingAdmin(user, org.getId());
  }

  private Boolean orgHasLinkedOrgsCapability(final Organization org) {
    return _payingOrgSvc.isPayingOrg(org.getId())
        || _payingOrgSvc.isLinkedToPayingOrg(org.getId())
        || _crossOrgValidationSvc.isEligibleToBecomePayingOrg(org.getId());
  }

  private List<ProjectResourceView> getProjectViews(
      final AppUser user,
      Organization currentOrganization,
      final Set<Group> projects,
      final ObjectId currentProjectId) {
    return projects.stream()
        .distinct()
        .sorted(Comparator.comparing(Group::getName, String.CASE_INSENSITIVE_ORDER))
        .map(
            project ->
                new ProjectResourceView(
                    project.getId(),
                    project.getName(),
                    project.getOrgId(),
                    new ProjectMetadataView(
                        getProjectElementVisibilityView(
                            user,
                            currentOrganization,
                            project,
                            project.getId().equals(currentProjectId)),
                        project.getStatus().getStatus(),
                        project.getIsProjectOverviewEnabled(),
                        getChartsSvc().hasActivatedChartsApp(project.getId()))))
        .collect(Collectors.toCollection(ArrayList::new));
  }

  private ElementVisibilityView getProjectElementVisibilityView(
      final AppUser user,
      final Organization currentOrganization,
      final Group project,
      final boolean isCurrentProject) {
    return project.isCloudManager()
        ? getCloudManagerProjectElementVisibilityView(user, project)
        : getAtlasProjectElementVisibilityView(
            user, currentOrganization, project, isCurrentProject);
  }

  private ElementVisibilityView getAtlasProjectElementVisibilityView(
      final AppUser user,
      final Organization currentOrganization,
      final Group project,
      final boolean isCurrentProject) {
    final ProjectVisibilityView projectVisibility = new ProjectVisibilityView();
    ElementVisibilityView visibility =
        ElementVisibilityView.atlasSideNavAndUtilities(null, projectVisibility, null);

    final boolean isRegionalizationEnabled =
        Stream.of(
                FeatureFlag.ATLAS_DATA_REGIONALIZATION_ENABLED,
                FeatureFlag.ATLAS_DATA_REGIONALIZATION_ENABLED_GROUP)
            .anyMatch(
                flag -> isFeatureFlagEnabled(flag, getAppSettings(), currentOrganization, project));

    final boolean compassWebEnabled =
        !isFeatureFlagEnabled(
            FeatureFlag.DATA_EXPLORER_COMPASS_WEB_USER_CONTROLLED_DISABLE,
            getAppSettings(),
            currentOrganization,
            project);
    // Compass web is "enabled" if the user is NOT regionalized and the compass web feature flag is
    // enabled or the user can control it.
    // If the user can control it, they can disable it which will make "compassWebEnabled" false.
    final boolean isDataExplorerCompassWebEnabled = !isRegionalizationEnabled && compassWebEnabled;

    final boolean isGov = isGovEnvironment();

    final boolean showDataExplorer = isDataExplorerCompassWebEnabled;
    final boolean showQuickstart =
        !isFeatureFlagEnabled(
            FeatureFlag.ATLAS_HIDE_QUICK_ACCESS_PAGE, getAppSettings(), null, project);
    final boolean showStreamProcessing =
        isFeatureFlagEnabled(FeatureFlag.STREAMS_ENABLED, getAppSettings(), null, project);
    final boolean showVisualization = !isGov;
    final boolean showTriggers = !isGov;
    final boolean showDataApi = !isGov;
    final boolean showDataFederation = !isGov;
    final boolean showInternal = getAuthzSvc().isGlobalMonitoringAdmin(user);

    // Cloud Nav >=3.0.0
    visibility
        .sideNav
        .primary
        // Never show the Online Archive primary side nav item in new Cloud Nav versions,
        // since it's been superseded by the secondary side nav item.
        // LG Cloud Nav currently still includes it in the config, so we ensure it's hidden here.
        .setOnlineArchive(false)
        .setDataExplorer(showDataExplorer)
        .setQuickstart(showQuickstart)
        .setStreamProcessing(showStreamProcessing)
        .setVisualization(showVisualization)
        .setTriggers(showTriggers)
        .setDataApi(showDataApi)
        .setDataFederation(showDataFederation)
        .setProjectInternalSection(showInternal);

    if (isCurrentProject) {
      final boolean isDataLakeDeprecationEnabled =
          isFeatureFlagEnabled(
              FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_DEPRECATION,
              getAppSettings(),
              null,
              project);
      final boolean hasIngestionPipelines =
          !_ingestionPipelineUiSvc.listIngestionPipelineForUI(project.getId()).isEmpty();
      final boolean showDataLake = !isDataLakeDeprecationEnabled || hasIngestionPipelines;

      visibility.sideNav.primary.setDataLake(showDataLake);
    }

    // Cloud Nav <3.0.0
    projectVisibility
        .setOnlineArchive(!isGov)
        .setDataExplorer(showDataExplorer)
        .setQuickstart(showQuickstart)
        .setStreamProcessing(showStreamProcessing)
        .setVisualization(showVisualization)
        .setTriggers(showTriggers)
        .setDataApi(showDataApi)
        .setDataFederation(showDataFederation)
        .setInternal(showInternal);

    return visibility;
  }

  private ElementVisibilityView getCloudManagerProjectElementVisibilityView(
      final AppUser user, final Group project) {
    final CloudManagerProjectVisibilityView projectVisibility =
        new CloudManagerProjectVisibilityView();

    ElementVisibilityView visibility =
        ElementVisibilityView.cloudManagerSideNavAndUtilities(null, projectVisibility, null);

    final boolean showAdmin = getAuthzSvc().isGlobalReadOnly(user);

    visibility.sideNav.primary.setProjectAdminSection(showAdmin);
    projectVisibility.setAdmin(showAdmin);

    return visibility;
  }

  private UtilitiesVisibilityView getUtilitiesVisibilityView(
      final AppUser user, final Organization org, final Boolean showLinkedOrgs) {
    return new UtilitiesVisibilityView()
        .setLinkedOrganizations(showLinkedOrgs)
        .setCostExplorer(!org.isCloudManager());
  }

  private Set<Group> getAvailableProjects(
      final AppUser pAppUser,
      final Organization currentOrganization,
      final ObjectId requestedProjectId) {
    final Set<ObjectId> accessibleProjectIds = new HashSet<>(pAppUser.getGroupIds());

    if (_authzSvc.hasAnyGlobalRole(pAppUser) && requestedProjectId != null) {
      accessibleProjectIds.add(requestedProjectId);
    }

    final List<Group> directProjects =
        accessibleProjectIds.isEmpty()
            ? Collections.emptyList()
            : getGroupSvc().findByIds(accessibleProjectIds);

    final Set<ObjectId> teamIds = pAppUser.getTeamIds();
    final List<Group> teamProjects =
        teamIds == null || teamIds.isEmpty()
            ? Collections.emptyList()
            : getGroupSvc().findByTeamIds(teamIds);

    final List<Group> currentOrganizationGroups =
        currentOrganization == null
            ? Collections.emptyList()
            : getGroupSvc().getVisibleGroups(currentOrganization.getId(), pAppUser);

    return Stream.of(directProjects, teamProjects, currentOrganizationGroups)
        .flatMap(Collection::stream)
        .collect(Collectors.toSet());
  }

  @Nullable
  private ObjectId getCurrentFederationId(final ObjectId orgId) {
    if (orgId != null) {
      final Optional<FederationSettings> currentFederation =
          _federationSettingsSvc.findByConnectedOrgId(orgId);
      return currentFederation.map(FederationSettings::getId).orElse(null);
    }
    return null;
  }

  private AppUser updateAppUserFromAppIds(AppIdsView pAppIdsView, AppUser pContextAppUser) {
    final Optional<String> stitchClientAppId = pAppIdsView.getStitchClientAppId();

    if (stitchClientAppId.isPresent()) {
      final ObjectId desiredGroupId = getChartsSvc().findGroupId(stitchClientAppId.get());
      // Returns the AppUser with updated project / org details.
      return getChartsSvc().updateCurrentProjectOrOrg(pContextAppUser, desiredGroupId, null);
    }

    // Return the AppUser as is.
    return pContextAppUser;
  }

  private HostnamesView getHostnames() {
    return new HostnamesView()
        .setDataServices(getDataServicesUrl())
        .setCharts(getAppSettings().getChartsCentralUrl())
        .setBaas(getAppSettings().getBaasCentralUrl())
        .setAccount(getAppSettings().getAccountCentralUrl())
        .setUniversity(getAppSettings().getUniversityCentralUrl())
        .setSupport(
            isGovEnvironment() ? "https://support.mongodbgov.com" : "https://support.mongodb.com");
  }

  private String getDataServicesUrl() {
    return getAppSettings()
            .getAppEnv()
            .isLocal() // Locally, centralUrl is overwritten to an ngrok url. This is not where mms
        // is typically accessed
        ? getAppSettings().getCloudCentralUrlOverride()
        : getAppSettings().getCentralUrl();
  }

  private String getEnv() {
    return getAppSettings().getAppEnv().getCode();
  }

  private AppSettings getAppSettings() {
    return _appSettings;
  }

  private GroupSvc getGroupSvc() {
    return _groupSvc;
  }

  private ChartsSvc getChartsSvc() {
    return _chartsSvc;
  }

  private OrganizationSvc getOrganizationSvc() {
    return _organizationSvc;
  }

  public AuthzSvc getAuthzSvc() {
    return _authzSvc;
  }

  private Boolean isGovEnvironment() {
    return getAppSettings().getNDSGovUSEnabled();
  }
}
