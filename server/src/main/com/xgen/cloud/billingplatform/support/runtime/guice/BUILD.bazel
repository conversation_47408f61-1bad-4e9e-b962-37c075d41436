load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "guice",
    srcs = glob(["**/*.java"]),
    visibility = [
        # DO NOT EDIT THIS, THIS SHOULD ONLY BE VISIBLE TO THE MMS GUICE MODULE
        "//server/src/main/com/xgen/svc/mms/guice:__pkg__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/support/_private/svc",
        "//server/src/main/com/xgen/cloud/billingplatform/support/_public/svc",
        "@maven//:com_google_inject_guice",
    ],
)
