package com.xgen.cloud.billingplatform.common._public.svc;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.util.MathUtils;
import java.util.HashSet;
import java.util.Set;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Handles organization-level logging settings. */
public class BillingLogTimerSvc {

  static final String SUPPRESS_TIMING_LOG_IN_MILLIS_PROP = "mms.billing.suppressTimingLogInMillis";
  static final String ORG_IDS_TO_ALWAYS_LOG_PROP = "mms.billing.orgIdsToAlwaysLog";
  private static final String TIMING_LOG_SAMPLE_RATE_PROP = "mms.billing.timingLogSampleRate";
  private static final Logger LOG = LoggerFactory.getLogger(BillingLogTimerSvc.class);

  private final AppSettings appSettings;
  private final ThreadLocal<Boolean> logOrg = ThreadLocal.withInitial(() -> false);

  public BillingLogTimerSvc(AppSettings appSettings) {
    this.appSettings = appSettings;
  }

  public void initializeLoggingForOrg(ObjectId orgId) {
    Set<ObjectId> orgIdsToAlwaysLog = getOrgIdsToAlwaysLogFromProp();
    double timingLogSampleRate = getTimingLogSampleRate();
    // Sampling happens at the organization level.  If an org is sample, it prints out all timer
    // logs for the org unless logging is suppressed due to the threshold not met.
    logOrg.set(
        orgIdsToAlwaysLog.contains(orgId) || MathUtils.nextRandomDouble() < timingLogSampleRate);
  }

  public void removeLoggingForOrg() {
    logOrg.remove();
  }

  private Set<ObjectId> getOrgIdsToAlwaysLogFromProp() {
    Set<ObjectId> orgIds = new HashSet<>();
    if (appSettings.hasProp(ORG_IDS_TO_ALWAYS_LOG_PROP)) {
      String propValue = appSettings.getStrProp(ORG_IDS_TO_ALWAYS_LOG_PROP);
      for (String orgId : propValue.split(",")) {
        try {
          orgIds.add(new ObjectId(orgId));
        } catch (IllegalArgumentException exception) {
          // Prevent causing Splunk alerts
          LOG.warn("Parsing orgId={} failed", orgId);
        } catch (Exception exception) {
          LOG.error("Error occurred while parsing orgId={}", orgId, exception);
        }
      }
    }
    return orgIds;
  }

  private long getSuppressTimingLogInNanosecondsFromProp() {
    return appSettings.hasProp(SUPPRESS_TIMING_LOG_IN_MILLIS_PROP)
        ? (long) (appSettings.getDoubleProp(SUPPRESS_TIMING_LOG_IN_MILLIS_PROP) * 1000_000)
        : Long.MAX_VALUE;
  }

  private double getTimingLogSampleRate() {
    return appSettings.getDoubleProp(TIMING_LOG_SAMPLE_RATE_PROP, 1.0);
  }

  public NanoTimer createNanoTimer() {
    if (logOrg.get()) {
      return new LoggingNanoTimer(getSuppressTimingLogInNanosecondsFromProp());
    } else {
      return new IdleNanoTimer();
    }
  }

  public NanoTimer createLoggingNanoTimer() {
    return new LoggingNanoTimer(getSuppressTimingLogInNanosecondsFromProp());
  }

  public interface NanoTimer {

    void logElapsedTime(String format, ObjectId orgId, Object... arguments);
  }

  /**
   * This is a timer using System.nanoTime() for profiling billing processes. It is not thread-safe.
   * Only use it in one thread and in the same scope.
   */
  public static class IdleNanoTimer implements NanoTimer {

    @Override
    public void logElapsedTime(String format, ObjectId orgId, Object... arguments) {}
  }

  public static class LoggingNanoTimer implements NanoTimer {

    private long lastTime;
    private final long suppressTimingLogInNanoseconds;

    LoggingNanoTimer(long suppressTimingLogInNanoseconds) {
      this.lastTime = System.nanoTime();
      this.suppressTimingLogInNanoseconds = suppressTimingLogInNanoseconds;
    }

    @Override
    public void logElapsedTime(String format, ObjectId orgId, Object... arguments) {
      long elapsedTime = resetElapsedTime();
      if (elapsedTime >= suppressTimingLogInNanoseconds) {
        double timeInMillis = elapsedTime / 1000_000.;

        LOG.info(
            String.format("orgId=%s %s elapsedTime=%.6f ms", orgId, format, timeInMillis),
            arguments);
      }
    }

    private long resetElapsedTime() {
      long currentTime = System.nanoTime();
      long elapsedTime = currentTime - lastTime;
      this.lastTime = currentTime;
      return elapsedTime;
    }
  }
}
