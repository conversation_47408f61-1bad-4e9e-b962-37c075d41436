package com.xgen.cloud.billingplatform.monitoring.statuspage._private.model;

import com.xgen.cloud.billingplatform.monitoring.statuspage._public.model.ComponentStatus;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/** ComponentData is used to pass data needed to create incidents for degraded components. */
public class ComponentData {
  private final String title;
  private final String body;
  private final ComponentStatus componentStatus;

  public ComponentData(String title, String body, ComponentStatus componentStatus) {
    this.title = title;
    this.body = body;
    this.componentStatus = componentStatus;
  }

  public ComponentData(ComponentStatus componentStatus) {
    this.componentStatus = componentStatus;
    this.title = "";
    this.body = "";
  }

  public String getTitle() {
    return title;
  }

  public String getBody() {
    return body;
  }

  public ComponentStatus getComponentStatus() {
    return componentStatus;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }

    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    ComponentData that = (ComponentData) o;

    return new EqualsBuilder()
        .append(title, that.title)
        .append(body, that.body)
        .append(componentStatus, that.componentStatus)
        .isEquals();
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder(17, 37)
        .append(title)
        .append(body)
        .append(componentStatus)
        .toHashCode();
  }
}
