load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "statuspage",
    visibility = [
        "//server/src/main/com/xgen:__subpackages__",
    ],
    exports = [
        "//server/src/main/com/xgen/cloud/billingplatform/monitoring/statuspage/_public/exception",
        "//server/src/main/com/xgen/cloud/billingplatform/monitoring/statuspage/_public/model",
        "//server/src/main/com/xgen/cloud/billingplatform/monitoring/statuspage/_public/svc",
    ],
)
