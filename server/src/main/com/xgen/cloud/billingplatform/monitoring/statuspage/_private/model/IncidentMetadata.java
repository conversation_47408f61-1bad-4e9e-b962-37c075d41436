package com.xgen.cloud.billingplatform.monitoring.statuspage._private.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * IncidentMetadata is used to pass properties to the metadata field when creating incidents to make
 * incidents identifiable as either associated with an {@link
 * com.xgen.cloud.billingplatform.monitoring.statuspage._public.model.Alert Alert} or {@link
 * com.xgen.cloud.billingplatform.monitoring.statuspage._public.model.Component Component}
 */
public class IncidentMetadata {
  private static final String INCIDENT_TYPE_FIELD = "incidentType";

  @JsonProperty(INCIDENT_TYPE_FIELD)
  private final IncidentType incidentType;

  public IncidentType getIncidentType() {
    return incidentType;
  }

  @JsonCreator
  public IncidentMetadata(@JsonProperty(INCIDENT_TYPE_FIELD) IncidentType incidentType) {
    this.incidentType = incidentType;
  }

  public static class IncidentType {
    private static final String ENUM_TYPE_FIELD = "enumType";
    private static final String NAME_FIELD = "name";

    @JsonProperty(ENUM_TYPE_FIELD)
    private final String enumType;

    @JsonProperty(NAME_FIELD)
    private final String name;

    public String getEnumType() {
      return enumType;
    }

    public String getName() {
      return name;
    }

    @JsonCreator
    public IncidentType(
        @JsonProperty(ENUM_TYPE_FIELD) String enumType, @JsonProperty(NAME_FIELD) String name) {
      this.enumType = enumType;
      this.name = name;
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }

      if (o == null || getClass() != o.getClass()) {
        return false;
      }

      IncidentType that = (IncidentType) o;

      return new EqualsBuilder().append(enumType, that.enumType).append(name, that.name).isEquals();
    }

    @Override
    public int hashCode() {
      return new HashCodeBuilder(17, 37).append(enumType).append(name).toHashCode();
    }

    @Override
    public String toString() {
      return new ToStringBuilder(this).append("enumType", enumType).append("name", name).toString();
    }
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }

    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    IncidentMetadata that = (IncidentMetadata) o;

    return new EqualsBuilder().append(incidentType, that.incidentType).isEquals();
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder(17, 37).append(incidentType).toHashCode();
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this).append("incidentType", incidentType).toString();
  }
}
