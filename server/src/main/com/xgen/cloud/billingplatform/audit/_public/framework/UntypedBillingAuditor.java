package com.xgen.cloud.billingplatform.audit._public.framework;

import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.svc.IAuditorConfigSvc;
import java.util.Optional;

/** Audits data that is not easily defined by a single domain object */
public abstract class UntypedBillingAuditor implements BillingAuditor {
  private final IAuditorConfigSvc auditorConfigSvc;

  protected UntypedBillingAuditor(IAuditorConfigSvc auditorConfigSvc) {
    this.auditorConfigSvc = auditorConfigSvc;
  }

  public abstract Optional<AuditFailureDetail> audit();

  @Override
  public int getJobRetries() {
    return auditorConfigSvc.getJobRetries(getAuditorName());
  }

  @Override
  public boolean shouldSendSentryEventForFailure() {
    return auditorConfigSvc.shouldSendSentryEventForFailure(getAuditorName());
  }

  @Override
  public boolean isAuditingEnabled() {
    return auditorConfigSvc.isAuditingEnabled(getAuditorName());
  }
}
