package com.xgen.cloud.billingplatform.invoiceusagedetails.runtime.guice;

import com.google.inject.AbstractModule;
import com.xgen.cloud.billingplatform.invoiceusagedetails._private.dao.UsageDetailsLineItemDao;
import com.xgen.cloud.billingplatform.invoiceusagedetails._private.dao.UsageDetailsLineItemDaoImpl;
import com.xgen.cloud.billingplatform.invoiceusagedetails._private.svc.UsageDetailsFilterOptionSvcImpl;
import com.xgen.cloud.billingplatform.invoiceusagedetails._private.svc.UsageDetailsQuerySvcImpl;
import com.xgen.cloud.billingplatform.invoiceusagedetails._public.svc.UsageDetailsFilterOptionSvc;
import com.xgen.cloud.billingplatform.invoiceusagedetails._public.svc.UsageDetailsQuerySvc;

public class UsageDetailsModule extends AbstractModule {

  @Override
  protected void configure() {
    bind(UsageDetailsLineItemDao.class).to(UsageDetailsLineItemDaoImpl.class);
    bind(UsageDetailsQuerySvc.class).to(UsageDetailsQuerySvcImpl.class);
    bind(UsageDetailsFilterOptionSvc.class).to(UsageDetailsFilterOptionSvcImpl.class);
  }
}
