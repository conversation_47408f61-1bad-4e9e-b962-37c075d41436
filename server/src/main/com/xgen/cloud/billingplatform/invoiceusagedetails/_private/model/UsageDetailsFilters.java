package com.xgen.cloud.billingplatform.invoiceusagedetails._private.model;

import com.xgen.cloud.billingplatform.common._public.model.LocalDateRange;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuService;
import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

/**
 * Represents fields and their values used within the Invoice Usage Details tab to filter line
 * items. This will reflect the values that were actually selected by the user, explicitly or
 * implicitly. Since the user can select multiple values per field, this will account for sets of
 * values.
 *
 * <p>This includes certain implicit fields, such as SKUs to exclude, that were not explicitly
 * chosen by the user but are implicitly part of the criteria to use to filter results.
 *
 * <p>Note, invoice id is not included here and is provided separately. All filtering is done within
 * the context of an invoice.
 */
public class UsageDetailsFilters {
  private final Set<ObjectId> groupIds;
  private final Set<ObjectId> clusterIds;
  private final Set<SkuService> skuServices;
  private final LocalDateRange billDateRange;
  private final LocalDateRange usageDateRange;
  private final boolean includeZeroCentLineItems;
  private final Set<SKU> skusToExclude;

  private UsageDetailsFilters(Builder builder) {
    this.groupIds = initializeToEmpty(builder.groupIds);
    this.billDateRange = builder.billDateRange;
    this.clusterIds = initializeToEmpty(builder.clusterIds);
    this.usageDateRange = builder.usageDateRange;
    this.skuServices = initializeToEmpty(builder.skuServices);
    this.includeZeroCentLineItems = builder.includeZeroCentLineItems;
    this.skusToExclude = initializeToEmpty(builder.skusToExclude);
  }

  public Set<ObjectId> getGroupIds() {
    return groupIds;
  }

  public Set<ObjectId> getClusterIds() {
    return clusterIds;
  }

  /**
   * Represents categories of SKUs used to filter line items for the invoice. Each SKUService
   * implies a set of SKUs.
   */
  public Set<SkuService> getSkuServices() {
    return skuServices;
  }

  /** Convert set of SKU services to the full set of SKUs to which they correspond. */
  public Set<SKU> getSkus() {
    return skuServices.stream()
        .flatMap(
            service ->
                Arrays.stream(SKU.values())
                    .filter(sku -> sku.getInfo().getSkuService().equals(service)))
        .collect(Collectors.toSet());
  }

  /** Inclusive range of bill dates. */
  public LocalDateRange getBillDateRange() {
    return billDateRange;
  }

  /** Inclusive range of usage dates. */
  public LocalDateRange getUsageDateRange() {
    return usageDateRange;
  }

  /** True if line items with zero totalPriceCents are included, false if excluded. */
  public boolean isIncludeZeroCentLineItems() {
    return includeZeroCentLineItems;
  }

  /** Set of SKUs that should be excluded from line item usage details. */
  public Set<SKU> getSkusToExclude() {
    return skusToExclude;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UsageDetailsFilters that = (UsageDetailsFilters) o;
    return includeZeroCentLineItems == that.includeZeroCentLineItems
        && Objects.equals(groupIds, that.groupIds)
        && Objects.equals(clusterIds, that.clusterIds)
        && Objects.equals(skuServices, that.skuServices)
        && Objects.equals(billDateRange, that.billDateRange)
        && Objects.equals(usageDateRange, that.usageDateRange)
        && Objects.equals(skusToExclude, that.skusToExclude);
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        groupIds,
        clusterIds,
        skuServices,
        billDateRange,
        usageDateRange,
        includeZeroCentLineItems,
        skusToExclude);
  }

  @Override
  public String toString() {
    return "UsageDetailsFilters{"
        + "groupIds="
        + groupIds
        + ", clusterIds="
        + clusterIds
        + ", skuServices="
        + skuServices
        + ", billDateRange="
        + billDateRange
        + ", usageDateRange="
        + usageDateRange
        + ", includeZeroCentLineItems="
        + includeZeroCentLineItems
        + ", skusToExclude="
        + skusToExclude
        + '}';
  }

  public Builder toBuilder() {
    return new Builder(this);
  }

  private <T> Set<T> initializeToEmpty(Set<T> value) {
    return value != null ? value : Set.of();
  }

  public static final class Builder {

    private Set<ObjectId> groupIds;
    private Set<ObjectId> clusterIds;
    private Set<SkuService> skuServices;
    private LocalDateRange billDateRange;
    private LocalDateRange usageDateRange;
    private boolean includeZeroCentLineItems;
    private Set<SKU> skusToExclude;

    public Builder() {}

    private Builder(UsageDetailsFilters filters) {
      this.groupIds = filters.groupIds;
      this.billDateRange = filters.billDateRange;
      this.clusterIds = filters.clusterIds;
      this.usageDateRange = filters.usageDateRange;
      this.skusToExclude = filters.skusToExclude;
      this.skuServices = filters.skuServices;
      this.includeZeroCentLineItems = filters.includeZeroCentLineItems;
    }

    public Builder groupIds(Set<ObjectId> groupIds) {
      this.groupIds = groupIds;
      return this;
    }

    public Builder clusterIds(Set<ObjectId> clusterIds) {
      this.clusterIds = clusterIds;
      return this;
    }

    public Builder skuServices(Set<SkuService> skuServices) {
      this.skuServices = skuServices;
      return this;
    }

    public Builder billDateRange(LocalDateRange billDateRange) {
      this.billDateRange = billDateRange;
      return this;
    }

    public Builder usageDateRange(LocalDateRange usageDateRange) {
      this.usageDateRange = usageDateRange;
      return this;
    }

    public Builder includeZeroCentLineItems(boolean includeZeroCentLineItems) {
      this.includeZeroCentLineItems = includeZeroCentLineItems;
      return this;
    }

    public Builder skusToExclude(Set<SKU> skusToExclude) {
      this.skusToExclude = skusToExclude;
      return this;
    }

    public UsageDetailsFilters build() {
      return new UsageDetailsFilters(this);
    }
  }
}
