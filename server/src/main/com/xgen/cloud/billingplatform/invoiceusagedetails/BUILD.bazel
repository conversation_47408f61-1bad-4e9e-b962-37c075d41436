load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "invoiceusagedetails",
    visibility = [
        "//server/src/main/com/xgen/cloud/billingplatform:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/svc/billing:__subpackages__",
    ],
    exports = [
        "//server/src/main/com/xgen/cloud/billingplatform/invoiceusagedetails/_public/svc",
        "//server/src/main/com/xgen/cloud/billingplatform/invoiceusagedetails/_public/view",
    ],
)
