package com.xgen.cloud.billingplatform.costexplorer._private.svc.job;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.localDateOf;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.billingplatform.costexplorer._private.svc.CostExplorerAggregateSvc;
import com.xgen.cloud.billingplatform.costexplorer._private.svc.util.CostExplorerAggregateRangeUtilSvc;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.constants._public.model.metrics.MetricNamespaceConstants;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.common.util._public.util.DaemonThreadFactory;
import com.xgen.svc.mms.svc.billing.InvoiceSvc;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Creates cost explorer aggregates materialized view for last month. */
@Singleton
public class CostExplorerAggregateCreationJob implements Runnable {
  private static final Logger LOG = LoggerFactory.getLogger(CostExplorerAggregateCreationJob.class);
  static final String AGGREGATE_CREATION_TIMEFRAME_MONTH_PROP =
      "mms.costExplorer.costExplorerAggregateCreationJob.creationTimeframeInMonths";
  static final String NUM_CREATION_THREADS_PROP =
      "mms.costExplorer.costExplorerAggregateCreationJob.numThreads";
  private static final Integer DEFAULT_NUM_THREADS = 1;
  private static final String TIME_NAME = "cost_explorer_aggregate_creation_job_time";
  private static final String TIME_HELP =
      "Histogram of Cost Explorer Aggregate creation job runtime";
  private static final String CREATED_ELEMENTS_NAME =
      "cost_explorer_aggregate_creation_job_deleted_elements";
  private static final String CREATED_ELEMENTS_HELP =
      "Gauge of created elements by cost explorer aggregate creation job";

  private static final Histogram TIME_HISTOGRAM =
      Histogram.build()
          .namespace(MetricNamespaceConstants.BILLING_NAMESPACE)
          .name(TIME_NAME)
          .help(TIME_HELP)
          .exponentialBuckets(1, 2, 14)
          .register();
  private static final Gauge CREATED_ELEMENTS_GAUGE =
      new Gauge.Builder()
          .namespace(MetricNamespaceConstants.BILLING_NAMESPACE)
          .name(CREATED_ELEMENTS_NAME)
          .help(CREATED_ELEMENTS_HELP)
          .register();
  private final AppSettings appSettings;
  private final InvoiceSvc invoiceSvc;
  private final CostExplorerAggregateSvc costExplorerAggregateSvc;

  @Inject
  public CostExplorerAggregateCreationJob(
      AppSettings appSettings,
      InvoiceSvc invoiceSvc,
      CostExplorerAggregateSvc costExplorerAggregateSvc) {
    this.appSettings = appSettings;
    this.invoiceSvc = invoiceSvc;
    this.costExplorerAggregateSvc = costExplorerAggregateSvc;
  }

  @Override
  public void run() {
    TIME_HISTOGRAM.time(
        () -> {
          LocalDate currentDate =
              LocalDate.now(ZoneOffset.UTC).with(TemporalAdjusters.firstDayOfMonth());
          run(currentDate);
        });
  }

  /**
   * Generates and stores cost explorer aggregates for each closed non-free in invoice for a range
   * of months (past 17 months by default up to last month).
   */
  @VisibleForTesting
  public void run(LocalDate today) {
    LocalDate lastAggCreationMonth = today.with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1);

    int creationTimeframeOverride =
        appSettings.getIntProp(AGGREGATE_CREATION_TIMEFRAME_MONTH_PROP, -1);

    LocalDate firstAggCreationMonth;
    if (creationTimeframeOverride <= 0) {
      firstAggCreationMonth =
          CostExplorerAggregateRangeUtilSvc.getEarliestStartDateForAggregates(today);
    } else {
      firstAggCreationMonth = lastAggCreationMonth.minusMonths(creationTimeframeOverride - 1);
    }

    LOG.info(
        "Starting to create cost explorer aggregates from {} to {}.",
        firstAggCreationMonth,
        lastAggCreationMonth);

    // generate aggregates for each month up to the cutoff
    ExecutorService executor = getExecutor();
    LocalDate currentInvoiceStartDate = lastAggCreationMonth;
    List<Callable<Object>> tasks = new ArrayList<>();
    while (!currentInvoiceStartDate.isBefore(firstAggCreationMonth)) {
      LocalDate month = currentInvoiceStartDate;
      tasks.add(Executors.callable(() -> generateForMonth(month)));
      currentInvoiceStartDate = currentInvoiceStartDate.minusMonths(1);
    }

    try {
      executor.invokeAll(tasks);
      LOG.info(
          "Cost explorer aggregates creation for {} to {} completed.",
          firstAggCreationMonth,
          lastAggCreationMonth);
    } catch (InterruptedException e) {
      LOG.error(
          "Failed to created explorer aggregates for {} - {}.",
          firstAggCreationMonth,
          lastAggCreationMonth);
    } finally {
      executor.shutdown();
    }
  }

  private ExecutorService getExecutor() {
    int numThreads = appSettings.getIntProp(NUM_CREATION_THREADS_PROP, DEFAULT_NUM_THREADS);

    LOG.debug("Using fixed pool of {} threads to create aggregates", numThreads);

    return Executors.newFixedThreadPool(
        numThreads, new DaemonThreadFactory(this.getClass().getName()));
  }

  public void generateForMonth(LocalDate invoiceStartDate) {
    Set<Invoice> invoices = getInvoicesForAggGeneration(invoiceStartDate);
    generateForInvoices(invoiceStartDate, invoices);
  }

  public void generateForInvoice(ObjectId invoiceId) {
    Invoice invoice = invoiceSvc.getInvoice(invoiceId);

    Set<Invoice> filter =
        getInvoicesForAggGeneration(localDateOf(invoice.getStartDate())).stream()
            .filter(i -> i.getId().equals(invoiceId))
            .collect(Collectors.toSet());

    generateForInvoices(localDateOf(invoice.getStartDate()), filter);
  }

  private void generateForInvoices(LocalDate invoiceStartDate, Collection<Invoice> invoices) {
    if (invoices.isEmpty()) {
      LOG.info(
          "Already, created aggregates for all closed invoices for {}, skipping creation.",
          invoiceStartDate);
      return;
    }

    LOG.info(
        "Starting cost explorer aggregates creation for {} invoices for {}",
        invoices.size(),
        invoiceStartDate);
    invoices.forEach(
        invoice -> {
          LOG.debug(
              "Starting to store aggregation for invoice={}, startDate={}",
              invoice.getId(),
              invoice.getStartDate());
          costExplorerAggregateSvc.storeInvoiceAggregatesFor(invoice);
          LOG.debug(
              "Completed storing aggregation for invoice={}, startDate={}",
              invoice.getId(),
              invoice.getStartDate());
        });
    CREATED_ELEMENTS_GAUGE.set(invoices.size());

    LOG.info(
        "Finished cost explorer aggregates creation for {} invoices for {}.",
        invoices.size(),
        invoiceStartDate);
  }

  /**
   * Gets non-free closed invoices for a specific month that do not have a cost explorer aggregates
   * generated for yet.
   */
  @VisibleForTesting
  Set<Invoice> getInvoicesForAggGeneration(LocalDate invoiceStartDate) {
    Stream<Invoice> invoices =
        invoiceSvc.findMonthlyInvoicesWithUsageInDateRange(
            DateTimeUtils.dateOf(invoiceStartDate), DateTimeUtils.dateOf(invoiceStartDate));

    LocalDate endDate = invoiceStartDate.plusMonths(1);
    Set<ObjectId> alreadyCreated =
        costExplorerAggregateSvc.getDistinctInvoiceIdsForDateRange(invoiceStartDate, endDate);

    return invoices.filter(i -> !alreadyCreated.contains(i.getId())).collect(Collectors.toSet());
  }
}
