package com.xgen.cloud.billingplatform.costexplorer._private.model;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuService;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.bson.types.ObjectId;

/**
 * Model class that holds all the data of an aggregation result. This is a unified class that both
 * {@link com.xgen.cloud.billingplatform.costexplorer._private.dao.LineItemDao LineItemDao} and
 * {@link com.xgen.cloud.billingplatform.costexplorer._private.dao.CostExplorerAggregateDao
 * CostExplorerAggregateDao} use.
 */
public class CostExplorerAggregationResult {

  public static final Set<SKU> NEGATIVE_VALUE_SKUS =
      Arrays.stream(SKU.values())
          .filter(sku -> sku.getInfo().getSkuService() == SkuService.CREDITS)
          .collect(Collectors.toSet());

  private final ObjectId invoiceId;
  private final ObjectId projectId;
  private final ObjectId clusterId;
  private final SKU sku;
  private final LocalDate usageDate;
  private final long costCents;

  private CostExplorerAggregationResult(Builder builder) {
    Objects.requireNonNull(builder.invoiceId);

    this.invoiceId = builder.invoiceId;
    this.projectId = builder.projectId;
    this.clusterId = builder.clusterId;
    this.sku = builder.sku;
    this.usageDate = builder.usageDate;
    this.costCents = builder.costCents;
  }

  public ObjectId getInvoiceId() {
    return invoiceId;
  }

  public ObjectId getProjectId() {
    return projectId;
  }

  public ObjectId getClusterId() {
    return clusterId;
  }

  public SKU getSku() {
    return sku;
  }

  public LocalDate getUsageDate() {
    return usageDate;
  }

  public long getCostCents() {
    return costCents;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CostExplorerAggregationResult that = (CostExplorerAggregationResult) o;
    return Objects.equals(invoiceId, that.invoiceId)
        && Objects.equals(projectId, that.projectId)
        && Objects.equals(clusterId, that.clusterId)
        && Objects.equals(sku, that.sku)
        && Objects.equals(usageDate, that.usageDate)
        && Objects.equals(costCents, that.costCents);
  }

  @Override
  public int hashCode() {
    return Objects.hash(invoiceId, projectId, clusterId, sku, usageDate, costCents);
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE)
        .append("invoiceId", invoiceId)
        .append("projectId", projectId)
        .append("clusterId", clusterId)
        .append("sku", sku)
        .append("usageDate", usageDate)
        .append("costCents", costCents)
        .toString();
  }

  public static class Builder {
    private ObjectId invoiceId;
    private ObjectId projectId;
    private ObjectId clusterId;
    private SKU sku;
    private LocalDate usageDate;
    private long costCents;

    public Builder invoiceId(ObjectId invoiceId) {
      this.invoiceId = invoiceId;
      return this;
    }

    public Builder projectId(ObjectId projectId) {
      this.projectId = projectId;
      return this;
    }

    public Builder clusterId(ObjectId clusterId) {
      this.clusterId = clusterId;
      return this;
    }

    public Builder sku(SKU sku) {
      this.sku = sku;
      return this;
    }

    public Builder usageDate(LocalDate usageDate) {
      this.usageDate = usageDate;
      return this;
    }

    public Builder costCents(long costCents) {
      this.costCents = costCents;
      return this;
    }

    public CostExplorerAggregationResult build() {
      return new CostExplorerAggregationResult(this);
    }
  }
}
