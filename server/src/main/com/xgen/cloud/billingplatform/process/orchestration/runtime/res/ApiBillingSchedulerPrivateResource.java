package com.xgen.cloud.billingplatform.process.orchestration.runtime.res;

import static com.xgen.cloud.common.constants._public.model.res.PrivateApiPathConstants.API_PRIVATE_BILLING;

import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.billingplatform.process.orchestration._public.model.schedule.BillingJobSchedulingException;
import com.xgen.cloud.billingplatform.process.orchestration._public.model.schedule.BillingJobType;
import com.xgen.cloud.billingplatform.process.orchestration._public.svc.schedule.BillingScheduler;
import com.xgen.cloud.billingplatform.process.orchestration._public.util.BillingOrchestrationDateUtil;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.res.filter.TestUtility;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;

@Path(API_PRIVATE_BILLING + "/billingScheduler")
@RolesAllowed(NAME.GLOBAL_BILLING_ADMIN)
@Singleton
public class ApiBillingSchedulerPrivateResource extends ApiBaseResource {

  private final BillingScheduler billingScheduler;
  private final Clock clock;

  @Inject
  public ApiBillingSchedulerPrivateResource(
      AppSettings appSettings, BillingScheduler billingScheduler, Clock clock) {
    super(appSettings);
    this.billingScheduler = billingScheduler;
    this.clock = clock;
  }

  @POST
  @Path("/dailyBilling")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Auth(endpointAction = "epa.global.ApiBillingSchedulerPrivateResource.dailyBilling.POST")
  public Response dailyBilling(
      @QueryParam("jobType") @DefaultValue("ORGANIZATION") BillingJobType billingJobType,
      @QueryParam("envelope") boolean envelope,
      List<ObjectId> pOrgIds,
      @Context AuditInfo auditInfo) {

    LocalDateTime formattedDate =
        BillingOrchestrationDateUtil.formatRequestedBillingDate(LocalDate.now(clock));
    try {

      String jobId =
          billingScheduler.scheduleBillingJobForOrgsForDay(
              pOrgIds, billingJobType, DateTimeUtils.dateOf(formattedDate), auditInfo);

      return new ApiResponseBuilder(envelope).ok().content(Map.of("jobId", jobId)).build();
    } catch (BillingJobSchedulingException e) {
      return new ApiResponseBuilder(envelope).internalServerError(e.getMessage()).build();
    }
  }

  /**
   * Schedules a daily billing job for the specified organizations on the given billing date. This
   * is intended for testing purposes only and is not suitable for production use. The bill date
   * must be after or equal current date. Note that methods using new Date() will not use this date.
   *
   * @param billingJobType the type of billing job to schedule, defaults to "ORGANIZATION"
   * @param envelope whether to wrap the response in an envelope
   * @param billDateParam the billing date for which the job is to be scheduled
   * @param pOrgIds the list of organization IDs for which the billing job is to be scheduled
   * @param auditInfo the audit information for the request
   * @return a Response object containing the job ID if the scheduling is successful, or an error
   *     message if it fails
   */
  @POST
  @Path("/dailyBillingByDate")
  @TestUtility
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Auth(endpointAction = "epa.global.ApiBillingSchedulerPrivateResource.dailyBillingByDate.POST")
  public Response dailyBillingByDate(
      @QueryParam("jobType") @DefaultValue("ORGANIZATION") BillingJobType billingJobType,
      @QueryParam("envelope") boolean envelope,
      @QueryParam("billingDay") LocalDate billDateParam,
      List<ObjectId> pOrgIds,
      @Context AuditInfo auditInfo) {

    Date billDate = billDateParam == null ? new Date() : DateTimeUtils.dateOf(billDateParam);

    LocalDateTime formattedDate =
        BillingOrchestrationDateUtil.formatRequestedBillingDate(
            DateTimeUtils.localDateOf(billDate));

    if (formattedDate.isBefore(LocalDate.now(clock).atStartOfDay())) {
      return new ApiResponseBuilder(envelope)
          .badRequest("Bill date cannot be before current date")
          .build();
    }

    try {
      String jobId =
          billingScheduler.scheduleBillingJobForOrgsForDay(
              pOrgIds, billingJobType, DateTimeUtils.dateOf(formattedDate), auditInfo);

      return new ApiResponseBuilder(envelope).ok().content(Map.of("jobId", jobId)).build();
    } catch (BillingJobSchedulingException e) {
      return new ApiResponseBuilder(envelope).internalServerError(e.getMessage()).build();
    }
  }
}
