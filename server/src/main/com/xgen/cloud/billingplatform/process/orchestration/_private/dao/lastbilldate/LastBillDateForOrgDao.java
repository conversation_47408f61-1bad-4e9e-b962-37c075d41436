package com.xgen.cloud.billingplatform.process.orchestration._private.dao.lastbilldate;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Updates.min;
import static com.mongodb.client.model.Updates.set;

import com.mongodb.ReadConcern;
import com.mongodb.WriteConcern;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.UpdateOptions;
import com.xgen.cloud.billingplatform.process.orchestration._public.model.lastbilldate.LastBillDateForOrg;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

@Singleton
public class LastBillDateForOrgDao extends BaseDao<LastBillDateForOrg> {

  @Inject
  public LastBillDateForOrgDao(MongoClientContainer container, CodecRegistry codecRegistry) {
    super(container, LastBillDateForOrg.DB_NAME, LastBillDateForOrg.COLLECTION_NAME, codecRegistry);
  }

  @Override
  public List<MongoIndex> getIndexes() {
    List<MongoIndex> list = super.getIndexes();
    list.add(MongoIndex.builder().key(LastBillDateForOrg.ORG_ID_FIELD).unique().build());
    return list;
  }

  public MongoCollection<LastBillDateForOrg> getCollectionForRead() {
    return getCollection().withReadConcern(ReadConcern.MAJORITY);
  }

  public MongoCollection<LastBillDateForOrg> getCollectionForWrite() {
    return getCollection().withWriteConcern(WriteConcern.MAJORITY);
  }

  public LastBillDateForOrg findByOrgId(ObjectId pOrgId) {
    return getCollectionForRead()
        .find(LastBillDateForOrg.class)
        .filter(eq(LastBillDateForOrg.ORG_ID_FIELD, pOrgId))
        .first();
  }

  public List<LastBillDateForOrg> findByOrgIds(Collection<ObjectId> orgIds) {
    if (orgIds.isEmpty()) {
      return List.of();
    }

    return getCollectionForRead()
        .find(LastBillDateForOrg.class)
        .filter(in(LastBillDateForOrg.ORG_ID_FIELD, orgIds))
        .into(new ArrayList<>());
  }

  public void updateLastBillDate(ObjectId pOrgId, Date pLastBillDate) {
    Bson filter = eq(LastBillDateForOrg.ORG_ID_FIELD, pOrgId);
    Bson update = set(LastBillDateForOrg.LAST_BILL_DATE_FIELD, pLastBillDate);
    getCollectionForWrite().updateOne(filter, update, new UpdateOptions().upsert(true));
  }

  public void updateCreditLastBillDate(ObjectId orgId, Date lastBillDate) {
    Bson filter = eq(LastBillDateForOrg.ORG_ID_FIELD, orgId);
    Bson update = set(LastBillDateForOrg.LAST_BILL_DATE_FOR_CREDIT_FIELD, lastBillDate);
    getCollectionForWrite().updateOne(filter, update, new UpdateOptions().upsert(true));
  }

  public void updateMinimumCreditLastBillDate(ObjectId orgId, Date lastBillDate) {
    Bson filter = eq(LastBillDateForOrg.ORG_ID_FIELD, orgId);
    Bson update = min(LastBillDateForOrg.LAST_BILL_DATE_FOR_CREDIT_FIELD, lastBillDate);
    getCollectionForWrite().updateOne(filter, update, new UpdateOptions().upsert(true));
  }

  public Stream<LastBillDateForOrg> findOrgsByLastBillDateWindow(
      Date pLastBillDateStart, Date pLastBillDateEnd) {
    Bson filter =
        and(
            gte(LastBillDateForOrg.LAST_BILL_DATE_FIELD, pLastBillDateStart),
            lt(LastBillDateForOrg.LAST_BILL_DATE_FIELD, pLastBillDateEnd));
    MongoCursor<LastBillDateForOrg> cursor =
        getCollectionForRead().find(LastBillDateForOrg.class).filter(filter).cursor();
    return DbUtils.stream(cursor);
  }
}
