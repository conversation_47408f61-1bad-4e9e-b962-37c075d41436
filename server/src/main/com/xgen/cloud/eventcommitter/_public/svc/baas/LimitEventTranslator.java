package com.xgen.cloud.eventcommitter._public.svc.baas;

import static com.xgen.cloud.eventcommitter._public.svc.baas.BaasTranslatorConstants.BAAS_EVENT_TYPES_PREFIX;

import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslatorConfig;
import com.xgen.cloud.realm.activity._public.event.LimitEvent;
import com.xgen.events.schemas.baas.v1.BaasEventMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = BaseEventTranslatorConfig.class)
public abstract class LimitEventTranslator
    extends BaseEventTranslator<LimitEvent, BaasEventMessage> {

  public static final LimitEventTranslator INSTANCE = Mappers.getMapper(LimitEventTranslator.class);

  protected LimitEventTranslator() {
    super(BAAS_EVENT_TYPES_PREFIX, BaasEventMessage.class);
  }

  @Override
  protected EventType getMMSEventType(final String eventTypeString) {
    return LimitEvent.Type.valueOf(eventTypeString);
  }
}
