package com.xgen.cloud.eventcommitter._public.svc.billing;

import static com.xgen.cloud.eventcommitter._public.svc.billing.BillingTranslatorConstants.BILLING_EVENT_TYPES_PREFIX;

import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslatorConfig;
import com.xgen.events.schemas.billing.v1.BillingEventMessage;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = BaseEventTranslatorConfig.class)
@AnnotateWith(
    value = SuppressWarnings.class,
    elements = @AnnotateWith.Element(strings = "deprecation"))
public abstract class BillingEventTranslator
    extends BaseEventTranslator<BillingEvent, BillingEventMessage> {

  public static final BillingEventTranslator INSTANCE =
      Mappers.getMapper(BillingEventTranslator.class);

  protected BillingEventTranslator() {
    super(BILLING_EVENT_TYPES_PREFIX, BillingEventMessage.class);
  }

  @Override
  protected EventType getMMSEventType(final String eventTypeString) {
    return BillingEvent.Type.valueOf(eventTypeString);
  }
}
