package com.xgen.cloud.eventcommitter._public.svc.support;

import static com.xgen.cloud.eventcommitter._public.svc.support.SupportTranslatorConstants.SUPPORT_EVENT_TYPES_PREFIX;

import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.email._public.activity.event.SupportEvent;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslatorConfig;
import com.xgen.events.schemas.support.v1.SupportEventMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = BaseEventTranslatorConfig.class)
public abstract class SupportEventTranslator
    extends BaseEventTranslator<SupportEvent, SupportEventMessage> {

  public static final SupportEventTranslator INSTANCE =
      Mappers.getMapper(SupportEventTranslator.class);

  protected SupportEventTranslator() {
    super(SUPPORT_EVENT_TYPES_PREFIX, SupportEventMessage.class);
  }

  @Override
  protected EventType getMMSEventType(final String eventTypeString) {
    return SupportEvent.Type.valueOf(eventTypeString);
  }
}
