package com.xgen.cloud.eventcommitter._public.svc.monitoring;

import static com.xgen.cloud.eventcommitter._public.svc.monitoring.MonitoringTranslatorConstants.MONITORING_EVENT_TYPES_PREFIX;

import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.base.BaseEventTranslatorConfig;
import com.xgen.cloud.monitoring.metrics._public.model.activity.MongotEvent;
import com.xgen.events.schemas.monitoring.v1.MongotEventMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = BaseEventTranslatorConfig.class)
public abstract class MongotEventTranslator
    extends BaseEventTranslator<MongotEvent, MongotEventMessage> {

  public static final MongotEventTranslator INSTANCE =
      Mappers.getMapper(MongotEventTranslator.class);

  protected MongotEventTranslator() {
    super(MONITORING_EVENT_TYPES_PREFIX, MongotEventMessage.class);
  }

  @Override
  protected EventType getMMSEventType(final String eventTypeString) {
    return MongotEvent.Type.valueOf(eventTypeString);
  }
}
