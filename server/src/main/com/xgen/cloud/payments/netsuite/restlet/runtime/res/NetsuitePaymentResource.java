package com.xgen.cloud.payments.netsuite.restlet.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billing._public.svc.IPaymentSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.externalanalytics._public.model.BillingDocumentDownloadEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.payments.netsuite.restlet._public.constant.NetsuiteApiErrorCode;
import com.xgen.cloud.payments.netsuite.restlet._public.svc.NetsuiteRestletApiSvc;
import com.xgen.svc.mms.model.billing.Payment;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;

@Path("/org/{orgId}/payment")
@Singleton
public class NetsuitePaymentResource {

  private final IPaymentSvc paymentSvc;
  private final SegmentEventSvc segmentEventSvc;
  private final NetsuiteRestletApiSvc netsuiteRestletApiSvc;

  /** Segment Event {@link BillingDocumentDownloadEvent} */
  private static final String NETSUITE_INVOICE_PDF = "NetSuite Invoice PDF";

  @Inject
  public NetsuitePaymentResource(
      IPaymentSvc paymentSvc,
      SegmentEventSvc segmentEventSvc,
      NetsuiteRestletApiSvc netsuiteRestletApiSvc) {
    this.paymentSvc = paymentSvc;
    this.segmentEventSvc = segmentEventSvc;
    this.netsuiteRestletApiSvc = netsuiteRestletApiSvc;
  }

  @GET
  @Path("/{paymentId}/invoice/pdf")
  @Produces("application/pdf")
  @UiCall(roles = RoleSet.ORG_BILLING_READ_ONLY, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.NetsuitePaymentResource.exportNetsuiteInvoicePdf.GET")
  public Response exportNetsuiteInvoicePdf(
      @PathParam("orgId") ObjectId orgId,
      @PathParam("paymentId") ObjectId paymentId,
      @Nullable @QueryParam("source") String source)
      throws SvcException {
    Payment payment = paymentSvc.findById(paymentId);
    if (payment == null || !payment.getOrgId().equals(orgId)) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    if (StringUtils.isEmpty(payment.getNetsuiteInvoiceInternalId())) {
      throw new SvcException(NetsuiteApiErrorCode.CANNOT_GET_NETSUITE_INVOICE_PDF_NO_INVOICE_ID);
    }

    Response response =
        netsuiteRestletApiSvc.exportInvoicePdf(payment.getNetsuiteInvoiceInternalId());

    if (response.getStatus() == Response.Status.OK.getStatusCode()) {
      segmentEventSvc.submitEvent(
          BillingDocumentDownloadEvent.builder(BillingDocumentDownloadEvent.EVENT_TYPE)
              .organizationId(payment.getOrgId())
              .properties(
                  new BillingDocumentDownloadEvent.Properties.Builder()
                      .documentType(NETSUITE_INVOICE_PDF)
                      .invoiceId(payment.getInvoiceId())
                      .source(source)
                      .build())
              .build());
    }
    return response;
  }
}
