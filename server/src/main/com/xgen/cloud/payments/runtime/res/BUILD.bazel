load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep: gazelle cannot resolve this dep in all cases, since it uses filegroups.
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/billingplatform/invoice",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/common/versioning",
        "//server/src/main/com/xgen/cloud/common/view",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/api/res",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_mongodb_bson",
    ],
)
