package com.xgen.cloud.payments.oppchange.runtime.res;

import static com.xgen.cloud.common.constants._public.model.res.PrivateApiPathConstants.API_PRIVATE_BILLING;

import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.ChangeSubscriptionDatesChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.CreditToSupportPlanChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.DirectMonthlyCommitToDirectPrepaidChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.DirectPrepaidToDirectMonthlyCommitChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.DirectPrepaidToMarketplaceMonthlyCommitRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.ExcludeNetsuiteInvoicesFromDunningAlertsRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.GenerateNetsuiteInvoicesChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.MarketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.NameOrAddressChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.OtherNotSureChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.SetOrgFinancialProtectionStatusRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.SupportPlanToCreditChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.UpdateUsageReportingIdsChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.svc.scenario.UpgradeOrDowngradeSupportPlanChangeRequestProcessor;
import com.xgen.cloud.payments.oppchange._public.view.request.ChangeSubscriptionDatesChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.CreditToSupportPlanChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.DirectMonthlyCommitToDirectPrepaidRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.DirectPrepaidToDirectMonthlyCommitChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.DirectPrepaidToMarketplaceMonthlyCommitRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.ExcludeNetsuiteInvoicesFromDunningAlertsRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.GenerateNetsuiteInvoicesChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.MarketplacePrepaidToMarketplaceMonthlyCommitRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.NameOrAddressChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.OtherNotSureChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.SetOrgFinancialProtectionStatusRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.SupportPlanToCreditChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.UpdateUsageReportingIdsChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.request.UpgradeOrDowngradeSupportPlanChangeRequest;
import com.xgen.cloud.payments.oppchange._public.view.response.OpportunityChangeApiError;
import com.xgen.cloud.payments.oppchange._public.view.response.StdOpportunityChangeResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;

@Path(API_PRIVATE_BILLING)
@RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
public final class OpportunityChangeResource {

  private final NameOrAddressChangeRequestProcessor nameOrAddressChangeRequestProcessor;
  private final OtherNotSureChangeRequestProcessor otherNotSureChangeRequestProcessor;
  private final CreditToSupportPlanChangeRequestProcessor creditToSupportPlanChangeRequestProcessor;
  private final DirectPrepaidToMarketplaceMonthlyCommitRequestProcessor
      directPrepaidToMarketplaceMonthlyCommitRequestProcessor;
  private final SupportPlanToCreditChangeRequestProcessor supportPlanToCreditChangeRequestProcessor;
  private final DirectPrepaidToDirectMonthlyCommitChangeRequestProcessor
      directPrepaidToDirectMonthlyCommitChangeRequestProcessor;
  private final DirectMonthlyCommitToDirectPrepaidChangeRequestProcessor
      directMonthlyCommitToDirectPrepaidChangeRequestProcessor;
  private final GenerateNetsuiteInvoicesChangeRequestProcessor
      generateNetsuiteInvoicesChangeRequestProcessor;
  private final UpgradeOrDowngradeSupportPlanChangeRequestProcessor
      upgradeOrDowngradeSupportPlanChangeRequestProcessor;
  private final MarketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor
      marketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor;
  private final ChangeSubscriptionDatesChangeRequestProcessor
      changeSubscriptionDatesChangeRequestProcessor;
  private final UpdateUsageReportingIdsChangeRequestProcessor
      updateUsageReportingIdsChangeRequestProcessor;
  private final MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor
      marketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor;
  private final SetOrgFinancialProtectionStatusRequestProcessor
      setOrgFinancialProtectionStatusRequestProcessor;
  private final ExcludeNetsuiteInvoicesFromDunningAlertsRequestProcessor
      excludeNetsuiteInvoicesFromDunningAlertsRequestProcessor;

  @Inject
  public OpportunityChangeResource(
      NameOrAddressChangeRequestProcessor nameOrAddressChangeRequestProcessor,
      OtherNotSureChangeRequestProcessor otherNotSureChangeRequestProcessor,
      CreditToSupportPlanChangeRequestProcessor creditToSupportPlanChangeRequestProcessor,
      SupportPlanToCreditChangeRequestProcessor supportPlanToCreditChangeRequestProcessor,
      DirectPrepaidToDirectMonthlyCommitChangeRequestProcessor
          directPrepaidToDirectMonthlyCommitChangeRequestProcessor,
      DirectPrepaidToMarketplaceMonthlyCommitRequestProcessor
          directPrepaidToMarketplaceMonthlyCommitRequestProcessor,
      DirectMonthlyCommitToDirectPrepaidChangeRequestProcessor
          directMonthlyCommitToDirectPrepaidChangeRequestProcessor,
      GenerateNetsuiteInvoicesChangeRequestProcessor generateNetsuiteInvoicesChangeRequestProcessor,
      UpgradeOrDowngradeSupportPlanChangeRequestProcessor
          upgradeOrDowngradeSupportPlanChangeRequestProcessor,
      MarketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor
          marketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor,
      ChangeSubscriptionDatesChangeRequestProcessor changeSubscriptionDatesChangeRequestProcessor,
      UpdateUsageReportingIdsChangeRequestProcessor updateUsageReportingIdsChangeRequestProcessor,
      MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor
          marketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor,
      SetOrgFinancialProtectionStatusRequestProcessor
          setOrgFinancialProtectionStatusRequestProcessor,
      ExcludeNetsuiteInvoicesFromDunningAlertsRequestProcessor
          excludeNetsuiteInvoicesFromDunningAlertsRequestProcessor) {
    this.nameOrAddressChangeRequestProcessor = nameOrAddressChangeRequestProcessor;
    this.otherNotSureChangeRequestProcessor = otherNotSureChangeRequestProcessor;
    this.creditToSupportPlanChangeRequestProcessor = creditToSupportPlanChangeRequestProcessor;
    this.directPrepaidToMarketplaceMonthlyCommitRequestProcessor =
        directPrepaidToMarketplaceMonthlyCommitRequestProcessor;
    this.supportPlanToCreditChangeRequestProcessor = supportPlanToCreditChangeRequestProcessor;
    this.directPrepaidToDirectMonthlyCommitChangeRequestProcessor =
        directPrepaidToDirectMonthlyCommitChangeRequestProcessor;
    this.directMonthlyCommitToDirectPrepaidChangeRequestProcessor =
        directMonthlyCommitToDirectPrepaidChangeRequestProcessor;
    this.generateNetsuiteInvoicesChangeRequestProcessor =
        generateNetsuiteInvoicesChangeRequestProcessor;
    this.upgradeOrDowngradeSupportPlanChangeRequestProcessor =
        upgradeOrDowngradeSupportPlanChangeRequestProcessor;
    this.marketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor =
        marketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor;
    this.changeSubscriptionDatesChangeRequestProcessor =
        changeSubscriptionDatesChangeRequestProcessor;
    this.updateUsageReportingIdsChangeRequestProcessor =
        updateUsageReportingIdsChangeRequestProcessor;
    this.marketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor =
        marketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor;
    this.setOrgFinancialProtectionStatusRequestProcessor =
        setOrgFinancialProtectionStatusRequestProcessor;
    this.excludeNetsuiteInvoicesFromDunningAlertsRequestProcessor =
        excludeNetsuiteInvoicesFromDunningAlertsRequestProcessor;
  }

  private static Response getResponse(StdOpportunityChangeResult result) {
    if (result.isSuccess()) {
      return Response.ok(result).build();
    }
    Status errorStatus =
        !result.getValidationFailures().isEmpty()
            ? Status.BAD_REQUEST
            : Status.INTERNAL_SERVER_ERROR;
    return Response.status(errorStatus).entity(result).build();
  }

  /**
   * POST /api/private/billing/opportunityChange/nameOrAddressChange : Private endpoint to
   * auto-process a company name or address opportunity change support request in Cloud. This API
   * can be used to manually run an opportunity change automation.
   *
   * @param nameOrAddressChangeRequest Request body schema for a name or address opportunity change
   *     request. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process name or address change",
      description =
          "Private endpoint to auto-process a company name or address opportunity change support"
              + " request in Cloud."
              + " This API can be used to manually run an opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(schema = @Schema(implementation = NameOrAddressChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/nameOrAddressChange")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.nameOrAddressChange.POST")
  public Response nameOrAddressChange(
      @Parameter(
              name = "NameOrAddressChangeRequest",
              description = "Request body schema for a name or address opportunity change request.",
              required = true)
          @NotNull
          @Valid
          NameOrAddressChangeRequest nameOrAddressChangeRequest) {
    return getResponse(nameOrAddressChangeRequestProcessor.process(nameOrAddressChangeRequest));
  }

  /**
   * POST /api/private/billing/opportunityChange/generateNetsuiteInvoices : Private endpoint to
   * auto-process a generate NetSuite invoices support request in Cloud. This API can be used to
   * manually run an opportunity change automation.
   */
  @Operation(
      summary = "process generate netsuite invoices change",
      description =
          "Private endpoint to auto-process a generate NetSuite invoices support"
              + " request in Cloud."
              + " This API can be used to manually run an opportunity change automation.",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(implementation = GenerateNetsuiteInvoicesChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/generateNetsuiteInvoices")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.generateNetsuiteInvoices.POST")
  public Response generateNetsuiteInvoices(
      @Parameter(
              name = "GenerateNetsuiteInvoicesChangeRequest",
              description =
                  "Request body schema for a generate NetSuite invoices opportunity change"
                      + " request.",
              required = true)
          @NotNull
          @Valid
          GenerateNetsuiteInvoicesChangeRequest request) {
    return getResponse(generateNetsuiteInvoicesChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/directPrepaidToDirectMonthlyCommit : Private
   * endpoint to auto-process a Direct Prepaid to Direct Monthly Commit opportunity change support
   * request in Cloud. This API can be used to manually run an opportunity change automation.
   *
   * @param request Request body schema for a Direct Prepaid to Direct Monthly Commit opportunity
   *     change request. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process Direct Prepaid to Direct Monthly Commit change",
      description =
          "Private endpoint to auto-process a Direct Prepaid to Direct Monthly Commit opportunity"
              + " change support request in Cloud. This API can be used to manually run an"
              + " opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation =
                                  DirectPrepaidToDirectMonthlyCommitChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/directPrepaidToDirectMonthlyCommit")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.directPrepaidToDirectMonthlyCommit.POST")
  public Response directPrepaidToDirectMonthlyCommit(
      @Parameter(
              name = "DirectPrepaidToDirectMonthlyCommitChangeRequest",
              description =
                  "Request body schema for a Direct Prepaid to Direct Monthly Commit opportunity"
                      + " change request.",
              required = true)
          @NotNull
          @Valid
          DirectPrepaidToDirectMonthlyCommitChangeRequest request) {
    return getResponse(directPrepaidToDirectMonthlyCommitChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/otherNotSureChange : Private endpoint to
   * auto-process other/not sure change support requests in Cloud. This API can be used to manually
   * run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process other/not sure change",
      description =
          "Private endpoint to auto-process other/not sure opportunity change support"
              + " requests in Cloud."
              + " This API can be used to manually run an opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(schema = @Schema(implementation = OtherNotSureChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/otherNotSureChange")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.otherNotSureChange.POST")
  public Response otherNotSureChange(
      @Parameter(
              name = "OtherNotSureChangeRequest",
              description = "Request body schema for other/not sure change request.",
              required = true)
          @NotNull
          @Valid
          OtherNotSureChangeRequest request) {
    return getResponse(otherNotSureChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/creditToSupportPlanChange : Private endpoint to
   * auto-process a credit to support plan opportunity change request in Cloud. This API can be used
   * to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process credit to support plan change",
      description =
          "Private endpoint to auto-process credit to support plan opportunity change "
              + " requests in Cloud."
              + " This API can be used to manually run an opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema = @Schema(implementation = CreditToSupportPlanChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/creditToSupportPlan")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.creditToSupportPlanChange.POST")
  public Response creditToSupportPlanChange(
      @Parameter(
              name = "CreditToSupportPlanChangeRequest",
              description =
                  "Request body schema for the credit to support plan opp change request.",
              required = true)
          @NotNull
          @Valid
          CreditToSupportPlanChangeRequest request) {
    return getResponse(creditToSupportPlanChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/upgradeOrDowngradePlan : Private endpoint to
   * auto-process a upgrade or downgrade plan opportunity change request in Cloud. This API can be
   * used to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process upgrade or downgrade plan change",
      description =
          "Private endpoint to auto-process upgrade or downgrade plan opportunity change "
              + " requests in Cloud."
              + " This API can be used to manually run an opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation = UpgradeOrDowngradeSupportPlanChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/upgradeOrDowngradePlan")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.upgradeOrDowngradePlan.POST")
  public Response upgradeOrDowngradePlan(
      @Parameter(
              name = "UpgradeOrDowngradePlanChangeRequest",
              description = "Request body schema for upgrade or downbgrade plan change request.",
              required = true)
          @NotNull
          @Valid
          UpgradeOrDowngradeSupportPlanChangeRequest request) {
    return getResponse(upgradeOrDowngradeSupportPlanChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/supportPlanToCredit : Private endpoint to
   * auto-process a credit to support plan opportunity change request in Cloud. This API can be used
   * to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process support to credit plan change",
      description =
          "Private endpoint to auto-process support to credit plan opportunity change "
              + " requests in Cloud."
              + " This API can be used to manually run an opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema = @Schema(implementation = SupportPlanToCreditChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/supportPlanToCredit")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.supportToCreditPlanChange.POST")
  public Response supportToCreditPlanChange(
      @Parameter(
              name = "SupportPlanToCreditChangeRequest",
              description =
                  "Request body schema for the credit to support plan opp change request.",
              required = true)
          @NotNull
          @Valid
          SupportPlanToCreditChangeRequest request) {
    return getResponse(supportPlanToCreditChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/directPrepaidToMarketplaceMonthlyCommit : Private
   * endpoint to auto-process a direct prepaid to marketplace monthly commit opportunity change
   * request in Cloud. This API can be used to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process direct prepaid to marketplace monthly commit change",
      description =
          "Private endpoint to auto-process direct prepaid to marketplace monthly commit"
              + " opportunity change  requests in Cloud. This API can be used to manually run an"
              + " opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation =
                                  DirectPrepaidToMarketplaceMonthlyCommitRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/directPrepaidToMarketplaceMonthlyCommit")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.directPrepaidToMarketplaceMonthlyCommit.POST")
  public Response directPrepaidToMarketplaceMonthlyCommit(
      @Parameter(
              name = "DirectPrepaidToMarketplaceMonthlyCommitRequest",
              description =
                  "Request body schema for the direct prepaid to marketplace monthly commit change"
                      + " request.",
              required = true)
          @NotNull
          @Valid
          DirectPrepaidToMarketplaceMonthlyCommitRequest request) {
    return getResponse(directPrepaidToMarketplaceMonthlyCommitRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/directMonthlyCommitToDirectPrepaid : Private
   * endpoint to auto-process a direct monthly commit to direct prepaid opportunity change request
   * in Cloud. This API can be used to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process direct monthly commit to direct prepaid change",
      description =
          "Private endpoint to auto-process direct monthly commit to direct prepaid "
              + " opportunity change requests in Cloud. This API can be used to manually run an"
              + " opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation = DirectMonthlyCommitToDirectPrepaidRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/directMonthlyCommitToDirectPrepaid")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.directMonthlyCommitToDirectPrepaid.POST")
  public Response directMonthlyCommitToDirectPrepaid(
      @Parameter(
              name = "DirectMonthlyCommitToDirectPrepaidRequest",
              description =
                  "Request body schema for the direct monthly commit to direct prepaid change"
                      + " request.",
              required = true)
          @NotNull
          @Valid
          DirectMonthlyCommitToDirectPrepaidRequest request) {
    return getResponse(directMonthlyCommitToDirectPrepaidChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/marketplacePrepaidToMarketplaceMonthlyCommit :
   * Private endpoint to auto-process a marketplace prepaid to marketplace monthly commit
   * opportunity change request in Cloud. This API can be used to manually run an opportunity change
   * automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process marketplace prepaid to marketplace monthly commit change",
      description =
          "Private endpoint to auto-process marketplace prepaid to marketplace monthly commit"
              + " opportunity change requests in Cloud. This API can be used to manually run an"
              + " opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation =
                                  MarketplacePrepaidToMarketplaceMonthlyCommitRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/marketplacePrepaidToMarketplaceMonthlyCommit")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.marketplacePrepaidToMarketplaceMonthlyCommit.POST")
  public Response marketplacePrepaidToMarketplaceMonthlyCommit(
      @Parameter(
              name = "MarketplacePrepaidToMarketplaceMonthlyCommitRequest",
              description =
                  "Request body schema for the marketplace prepaid to marketplace monthly commit"
                      + " change request.",
              required = true)
          @NotNull
          @Valid
          MarketplacePrepaidToMarketplaceMonthlyCommitRequest request) {
    return getResponse(
        marketplacePrepaidToMarketplaceMonthlyCommitRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/changeSubscriptionDates : Private endpoint to
   * auto-process a change subscription dates opportunity change request in Cloud. This API can be
   * used to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process change subscription dates change",
      description =
          "Private endpoint to auto-process change subscription dates"
              + " opportunity change requests in Cloud. This API can be used to manually run an"
              + " opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(implementation = ChangeSubscriptionDatesChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/changeSubscriptionDates")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.changeSubscriptionDates.POST")
  public Response changeSubscriptionDates(
      @Parameter(
              name = "ChangeSubscriptionDatesChangeRequest",
              description =
                  "Request body schema for the change subscription dates" + " change request.",
              required = true)
          @NotNull
          @Valid
          ChangeSubscriptionDatesChangeRequest request) {
    return getResponse(changeSubscriptionDatesChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/updateUsageReportingIds : Private endpoint to
   * auto-process an update usage reporting ids opportunity change request in Cloud. This API can be
   * used to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process update usage reporting ids",
      description =
          "Private endpoint to auto-process update usage reporting ids"
              + " opportunity change requests in Cloud. This API can be used to manually run an"
              + " opportunity change automation. ",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(implementation = UpdateUsageReportingIdsChangeRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/updateUsageReportingIds")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.updateUsageReportingIds.POST")
  public Response updateUsageReportingIds(
      @Parameter(
              name = "UpdateUsageReportingIdsChangeRequest",
              description =
                  "Request body schema for the update usage reporting ids change request.",
              required = true)
          @NotNull
          @Valid
          UpdateUsageReportingIdsChangeRequest request) {
    return getResponse(updateUsageReportingIdsChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/marketplaceMonthlyCommitToMarketplacePrepaid :
   * Private endpoint to auto-process marketplace monthly commit to marketplace prepaid opportunity
   * change request in Cloud. This API can be used to manually run an opportunity change automation.
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "process marketplace monthly commit to marketplace prepaid opp change",
      description =
          "Private endpoint to auto-process marketplace monthly commit to marketplace prepaid"
              + " opportunity change requests in Cloud. This API can be used to manually run an"
              + " opportunity change automation.",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation =
                                  MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequest
                                      .class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/marketplaceMonthlyCommitToMarketplacePrepaid")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.marketplaceMonthlyCommitToMarketplacePrepaid.POST")
  public Response marketplaceMonthlyCommitToMarketplacePrepaid(
      @Parameter(
              name = "MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequest",
              description =
                  "Request body schema for marketplace monthly commit to marketplace prepaid opp"
                      + " change",
              required = true)
          @NotNull
          @Valid
          MarketplaceMonthlyCommitToMarketplacePrepaidChangeRequest request) {
    return getResponse(
        marketplaceMonthlyCommitToMarketplacePrepaidChangeRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/setOrgFinancialProtectionStatus : Private endpoint
   * to auto-process request to set Org Financial Protection Status
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "set Org Financial Protection Status",
      description =
          "Private endpoint to auto-process setting Org Financial Protection Status."
              + "This API can be used to manually run an opportunity change automation.",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(implementation = SetOrgFinancialProtectionStatusRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/setOrgFinancialProtectionStatus")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.setOrgFinancialProtectionStatus.POST")
  public Response setOrgFinancialProtectionStatus(
      @Parameter(
              name = "SetOrgFinancialProtectionStatusRequest",
              description = "Request body schema for setting Org Financial Protection Status",
              required = true)
          @NotNull
          @Valid
          SetOrgFinancialProtectionStatusRequest request) {
    return getResponse(setOrgFinancialProtectionStatusRequestProcessor.process(request));
  }

  /**
   * POST /api/private/billing/opportunityChange/excludeNetsuiteInvoicesFromDunningAlerts : Private
   * endpoint to auto-process requests to exclude NetSuite Invoices From Dunning Alerts
   *
   * @param request Request body schema for the opportunity change request type. (required)
   * @return successful response (status code 200) or 400 Bad Request error response body. (status
   *     code 400)
   */
  @Operation(
      summary = "exclude NetSuite Invoices From Dunning Alerts",
      description =
          "Private endpoint to auto-process requests to exclude NetSuite Invoices From Dunning"
              + " Alerts. This API can be used to manually run an opportunity change automation.",
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      schema =
                          @Schema(
                              implementation =
                                  ExcludeNetsuiteInvoicesFromDunningAlertsRequest.class))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {
              @Content(schema = @Schema(implementation = StdOpportunityChangeResult.class))
            }),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = {
              @Content(schema = @Schema(implementation = OpportunityChangeApiError.class))
            })
      })
  @POST
  @Path("/opportunityChange/excludeNetsuiteInvoicesFromDunningAlerts")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Auth(endpointAction = "epa.global.OpportunityChangeResource.excludeNetsuiteInvoicesFromDunningAlerts.POST")
  public Response excludeNetsuiteInvoicesFromDunningAlerts(
      @Parameter(
              name = "ExcludeNetsuiteInvoicesFromDunningAlertsRequest",
              description =
                  "Request body schema for excluding NetSuite Invoices From Dunning Alerts.",
              required = true)
          @NotNull
          @Valid
          ExcludeNetsuiteInvoicesFromDunningAlertsRequest request) {
    return getResponse(excludeNetsuiteInvoicesFromDunningAlertsRequestProcessor.process(request));
  }
}
