load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    allow_lib_to_depend_on_service = True,
    visibility = [
        "//server/src/main:__pkg__",
        "//server/src/main/com/xgen/svc/mms/api/generator:__pkg__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/payments/grpc",
        "//server/src/main/com/xgen/cloud/payments/processing/_public/model",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing",
        "//server/src/main/com/xgen/svc/core/model/api",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_mongodb_bson",
    ],
)
