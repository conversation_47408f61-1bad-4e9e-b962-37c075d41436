package com.xgen.cloud.payments.credit.runtime.res;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.billing._public.svc.ICreditSvc;
import com.xgen.cloud.billing._public.svc.IOrgPlanSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.credit._public.model.IssueGenericCreditRequest;
import com.xgen.cloud.payments.creditView._public.model.CreditView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.model.billing.Credit;
import com.xgen.svc.mms.model.billing.CreditType;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;

@Path("/api/private/billing/credits")
@Singleton
public final class PrivateCreditResource {

  private static final LocalDate NO_EXPIRY_END_DATE = LocalDate.parse("9999-12-31");

  private final ICreditSvc creditSvc;
  private final OrganizationSvc organizationSvc;
  private final IOrgPlanSvc orgPlanSvc;
  private final AuthzSvc authzSvc;

  @Inject
  public PrivateCreditResource(
      ICreditSvc creditSvc,
      OrganizationSvc organizationSvc,
      IOrgPlanSvc orgPlanSvc,
      AuthzSvc authzSvc) {
    this.creditSvc = creditSvc;
    this.organizationSvc = organizationSvc;
    this.orgPlanSvc = orgPlanSvc;
    this.authzSvc = authzSvc;
  }

  private static Credit buildServiceCredit(IssueGenericCreditRequest request, AppUser appUser) {
    LocalDate startDate =
        request.startDate() != null ? request.startDate() : YearMonth.now().atDay(1);
    return Credit.builder()
        .orgId(request.orgId())
        .reason(request.internalNote())
        .amountCents(request.amountCents())
        .amountRemainingCents(request.amountCents())
        .note(request.externalNote())
        .validPaymentMethod(Boolean.TRUE.equals(request.isValidPaymentMethod()))
        .issuer(appUser.getUsername())
        .revenueReason(request.revenueReason())
        .type(CreditType.GENERIC)
        .startDate(dateOf(startDate))
        .endDate(dateOf(NO_EXPIRY_END_DATE))
        .created(new Date())
        .build();
  }

  @POST
  @Path("/genericCredit")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GLOBAL_BILLING_ADMIN})
  @Operation(
      summary = "issue generic credit",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "successful response",
            content = {@Content(schema = @Schema(implementation = CreditView.class))})
      })
  @Auth(endpointAction = "epa.global.PrivateCreditResource.issueGenericCredit.POST")
  public Response issueGenericCredit(
      @Context AppUser appUser,
      @Context AuditInfo auditInfo,
      @Valid IssueGenericCreditRequest request)
      throws SvcException {
    Credit credit = buildServiceCredit(request, appUser);
    Credit savedCredit = creditSvc.issueServiceCreditRetroactively(credit, auditInfo);
    return Response.ok().entity(wrapCredit(savedCredit, appUser)).build();
  }

  private CreditView wrapCredit(Credit credit, AppUser appUser) {
    Organization org = organizationSvc.findById(credit.getOrgId());
    OrgPrepaidPlan prepaidPlan = orgPlanSvc.getOrgPrepaidPlan(credit.getOrgId());

    // we only use this resource for GENERIC credit so it's ok to assume isFxBillingModel is false
    return new CreditView.Builder(credit, org.getName(), prepaidPlan)
        .withBillingAdminView(authzSvc.isGlobalBillingAdmin(appUser))
        .build();
  }
}
