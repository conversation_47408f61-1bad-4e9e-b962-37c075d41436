load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/svc/atm/web/res:__pkg__",
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
        "//server/src/test/com/xgen/svc/atm:__subpackages__",
        "//server/src/unit/com/xgen/cloud/atm:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/deploymentimport",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_mongodb_bson",
    ],
)
