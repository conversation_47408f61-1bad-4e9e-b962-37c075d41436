package com.xgen.cloud.stacktrace.runtime.res;

import static com.xgen.cloud.stacktrace._public.model.StackTraceSnapshot.CREATED_AT_FIELD;
import static com.xgen.cloud.stacktrace._public.model.StackTraceSnapshot.ID_FIELD;
import static com.xgen.cloud.stacktrace._public.model.StackTraceSnapshot.SNAPSHOT_NUMBER_FIELD;
import static com.xgen.cloud.stacktrace.runtime.res.StackTraceResourceErrorCode.AVAILABLE_JOB_STATUS_ERROR;
import static com.xgen.cloud.stacktrace.runtime.res.StackTraceResourceErrorCode.INVALID_JOB_ID;
import static com.xgen.cloud.stacktrace.runtime.res.StackTraceResourceErrorCode.INVALID_MAX_TOP_N_CODE_PATHS;
import static com.xgen.cloud.stacktrace.runtime.res.StackTraceResourceErrorCode.INVALID_SNAPSHOT_ID;
import static com.xgen.cloud.stacktrace.runtime.res.StackTraceResourceErrorCode.MISSING_REQUIRED_INSTANCE_PARAMETERS;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.AppRuntimeEnvironment;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.sharedpool._public.sharedpool.SharedWorkerPool;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.stacktrace._public.model.JobListView;
import com.xgen.cloud.stacktrace._public.model.MetadataView;
import com.xgen.cloud.stacktrace._public.model.StackTraceJob;
import com.xgen.cloud.stacktrace._public.model.StackTraceSnapshot;
import com.xgen.cloud.stacktrace._public.svc.StackTraceJobSvc;
import com.xgen.cloud.stacktrace._public.svc.StackTraceSnapshotSvc;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/admin/stacktrace")
@Singleton
public class StackTraceResource extends BaseResource {

  private final StackTraceJobSvc _stackTraceJobSvc;

  private final StackTraceSnapshotSvc _stackTraceSnapshotSvc;

  private final SharedWorkerPool _sharedWorkerPool;

  private static final Logger LOG = LoggerFactory.getLogger(StackTraceResource.class);

  @Inject
  public StackTraceResource(
      final StackTraceJobSvc pStackTraceJobSvc,
      final StackTraceSnapshotSvc pStackTraceSnapshotSvc,
      final SharedWorkerPool pSharedWorkerPool) {
    this._stackTraceJobSvc = pStackTraceJobSvc;
    this._stackTraceSnapshotSvc = pStackTraceSnapshotSvc;
    this._sharedWorkerPool = pSharedWorkerPool;
  }

  @GET
  @Path("/jobs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.getJobs.GET")
  public Response getJobs(
      @QueryParam("hostname") final String pHostname,
      @QueryParam("hostIdentifier") final String pHostIdentifier,
      @QueryParam("runType") final AppRuntimeEnvironment pRunType,
      @QueryParam("runId") final String pRunId,
      @QueryParam("skip") @DefaultValue("0") final int pSkip,
      @QueryParam("limit") @DefaultValue("20") final int pLimit) {

    final boolean isAnySearchParameterPassed =
        StringUtils.isNotEmpty(pHostname)
            || StringUtils.isNotEmpty(pHostIdentifier)
            || pRunType != null
            || StringUtils.isNotEmpty(pRunId);

    final Future<List<StackTraceJob>> jobsFuture =
        _sharedWorkerPool.submit(
            () ->
                // Try an extra record to calculate if there's a next page
                isAnySearchParameterPassed
                    ? _stackTraceJobSvc.findLatestJobs(
                        pHostname, pHostIdentifier, pRunType, pRunId, pSkip, pLimit + 1)
                    : _stackTraceJobSvc.findAllLatestJobs(pSkip, pLimit + 1),
            null,
            LOG);

    final Future<Long> maxSizeFuture =
        _sharedWorkerPool.submit(
            () ->
                isAnySearchParameterPassed
                    ? _stackTraceJobSvc.findJobsDocumentCount(
                        pHostname, pHostIdentifier, pRunType, pRunId)
                    : _stackTraceJobSvc.findJobsDocumentCount(),
            null,
            LOG);

    final long maxSize = _sharedWorkerPool.getFutureValue(maxSizeFuture);

    final List<StackTraceJob> jobs = _sharedWorkerPool.getFutureValue(jobsFuture);

    final boolean isLastPage = jobs.size() == 0 || jobs.size() <= pLimit;

    final JobListView result =
        JobListView.builder()
            .data(jobs.size() <= pLimit ? jobs : jobs.subList(0, pLimit))
            .maxSize(maxSize)
            .lastPage(isLastPage)
            .firstPage(pSkip <= 0)
            .skip(pSkip)
            .limit(pLimit)
            .build();

    return Response.ok().entity(result).build();
  }

  @POST
  @Path("/jobs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.createJob.POST")
  public Response createJob(
      @Context final AppUser pAppUser,
      @FormParam("hostname") final String pHostname,
      @FormParam("hostIdentifier") final String pHostIdentifier,
      @FormParam("runType") final AppRuntimeEnvironment pRunType,
      @FormParam("runId") final String pRunId,
      @FormParam("mmsRole") final String pMmsRole,
      @FormParam("instanceId") final String pInstanceId)
      throws SvcException {
    // validate minimum required parameters are passed
    if (StringUtils.isBlank(pHostname)
        || StringUtils.isBlank(pHostIdentifier)
        || pRunType == null) {
      throw new SvcException(MISSING_REQUIRED_INSTANCE_PARAMETERS);
    }

    // validate only one job may exist in AVAILABLE status per JVM instance
    if (_stackTraceJobSvc.getAvailableJobsCount(pHostname, pHostIdentifier, pRunType, pRunId) > 0) {
      throw new SvcException(AVAILABLE_JOB_STATUS_ERROR);
    }

    final ObjectId jobId =
        _stackTraceJobSvc.createJob(
            pHostname,
            pHostIdentifier,
            pRunType,
            pRunId,
            pMmsRole,
            pInstanceId,
            pAppUser.getUsername());
    return Response.ok(new JSONObject().put("jobId", jobId.toString()).toString()).build();
  }

  @GET
  @Path("/jobs/{jobId}/metadata")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.getJobMetadata.GET")
  public Response getJobMetadata(
      @PathParam("jobId") final String pJobId,
      @QueryParam("threadNameRegex") final String pThreadNameRegex,
      @QueryParam("classFQNRegex") final String pClassFQNRegex,
      @QueryParam("threadStateRegex") final String pThreadStateRegex,
      @QueryParam("maxTopCodePaths") final String pMaxTopCodePaths)
      throws SvcException {
    final ObjectId jobId;
    try {
      jobId = new ObjectId(pJobId);
    } catch (final IllegalArgumentException pEx) {
      throw new SvcException(INVALID_JOB_ID);
    }

    int maxTopCodePaths = Integer.MAX_VALUE;
    if (StringUtils.isNotEmpty(pMaxTopCodePaths)) {
      if (ValidationUtils.isInteger(pMaxTopCodePaths)) {
        maxTopCodePaths = Integer.parseInt(pMaxTopCodePaths);
      } else {
        throw new SvcException(INVALID_MAX_TOP_N_CODE_PATHS);
      }
    }

    final MetadataView metadataView =
        _stackTraceSnapshotSvc.findMetadata(
            jobId, pThreadNameRegex, pClassFQNRegex, pThreadStateRegex, maxTopCodePaths);
    return Response.ok().entity(metadataView).build();
  }

  @GET
  @Path("/instances")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.getJvmInstances.GET")
  public Response getJvmInstances() {
    return Response.ok(_stackTraceJobSvc.getAllJVMInstancesUsedInJobs()).build();
  }

  @GET
  @Path("/snapshots/{jobId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.getSnapshots.GET")
  public Response getSnapshots(@PathParam("jobId") final String pJobId) throws SvcException {
    final ObjectId jobId;
    try {
      jobId = new ObjectId(pJobId);
    } catch (final IllegalArgumentException pEx) {
      throw new SvcException(INVALID_JOB_ID);
    }

    final List<StackTraceSnapshot> stackTraceSnapshots =
        _stackTraceSnapshotSvc.findThreadDumpsByJobId(
            jobId, List.of(ID_FIELD, SNAPSHOT_NUMBER_FIELD, CREATED_AT_FIELD));
    return Response.ok().entity(stackTraceSnapshots).build();
  }

  @GET
  @Path("/snapshot/{snapshotId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.getSnapshot.GET")
  public Response getSnapshot(@PathParam("snapshotId") final String pSnapshotId)
      throws SvcException {
    final ObjectId snapshotId;
    try {
      snapshotId = new ObjectId(pSnapshotId);
    } catch (final IllegalArgumentException pEx) {
      throw new SvcException(INVALID_SNAPSHOT_ID);
    }

    final String formattedStackTraces = _stackTraceSnapshotSvc.getFormattedStackTraces(snapshotId);
    return Response.ok().entity(formattedStackTraces).build();
  }

  @GET
  @Path("/threadStack/jobs/{jobId}")
  @Produces({"application/gzip"})
  @UiCall(roles = RoleSet.GLOBAL_MONITORING_ADMIN, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.global.StackTraceResource.downloadThreadStackTraces.GET")
  public Response downloadThreadStackTraces(@PathParam("jobId") final String pJobId)
      throws SvcException {
    final ObjectId jobId;
    try {
      jobId = new ObjectId(pJobId);
    } catch (final IllegalArgumentException pEx) {
      throw new SvcException(INVALID_JOB_ID);
    }

    final StackTraceJob job =
        _stackTraceJobSvc
            .findJobById(jobId)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));
    final String filename =
        "thread-dump-"
            + job.getHostname()
            + "-"
            + job.getHostIdentifier()
            + "-"
            + job.getId()
            + ".tar.gz";
    final StreamingOutput streamingOutput =
        _stackTraceSnapshotSvc.createStreamingForDownload(jobId);

    LOG.info(
        "Preparing thread dumps .tar.gz download stream for jobId={} and name={}", jobId, filename);
    return Response.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
        .entity(streamingOutput)
        .build();
  }
}
