package com.xgen.cloud.communication._public.model.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.communication._public.model.base.Message;
import com.xgen.cloud.communication._public.model.enums.ErrorCode;
import com.xgen.cloud.communication._public.model.enums.Status;
import java.util.Date;
import java.util.Objects;
import org.bson.types.ObjectId;

/** Representation of an interface for sending messages through a third party */
public class MessageValidity {
  public static final String INTEGRATION_ID_FIELD = "integrationId";
  public static final String STATUS_FIELD = "status";
  public static final String ERROR_CODE_FIELD = "errorCode";
  public static final String CREATED_AT_FIELD = "createdAt";

  @JsonProperty(INTEGRATION_ID_FIELD)
  protected ObjectId integrationId;

  @JsonProperty(STATUS_FIELD)
  protected Status status;

  @JsonProperty(ERROR_CODE_FIELD)
  protected ErrorCode errorCode;

  @JsonProperty(CREATED_AT_FIELD)
  protected Date createdAt;

  public MessageValidity(
      final ObjectId integrationId,
      final Status status,
      final ErrorCode errorCode,
      final Date createdAt) {
    this.integrationId = integrationId;
    this.status = status;
    this.errorCode = errorCode;
    this.createdAt = createdAt;
  }

  public static MessageValidity fromMessage(final Message oldMessage) {
    if (oldMessage == null) {
      return null;
    }

    final Status status = oldMessage.getStatus();
    final ErrorCode dbErrorCode = oldMessage.getErrorCode();
    final ObjectId dbIntegrationId =
        oldMessage.getResources() == null
            ? null
            : oldMessage.getResources().stream()
                .filter(Objects::nonNull)
                .filter(resourceId -> ResourceId.ResourceType.INTEGRATION == resourceId.getType())
                .findFirst()
                .map(ResourceId::getId)
                .orElse(null);
    final Date dbCreatedAt = Date.from(oldMessage.getCreatedAt());

    return new MessageValidity(dbIntegrationId, status, dbErrorCode, dbCreatedAt);
  }

  public ObjectId getIntegrationId() {
    return integrationId;
  }

  public Status getStatus() {
    return status;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }
}
