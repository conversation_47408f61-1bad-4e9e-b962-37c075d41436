/**
 * Admin resource for Vercel Native partner integration operations Provides endpoints for triggering
 * secrets rotation and other maintenance tasks
 */
package com.xgen.cloud.partnerintegrations.vercelnative.runtime.res;

import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.MAINTENANCE_TASK_USER_ALLOWLIST;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.rolecheck._public.svc.RoleSetSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeSecretRotationRequest;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeSecretRotationResult;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeSecretsRotationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Admin resource for Vercel Native partner integration operations. Provides endpoints for
 * triggering secrets rotation and other maintenance tasks.
 */
@Path("/admin/vercelNative")
@Singleton
public class VercelNativeAdminResource extends BaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(VercelNativeAdminResource.class);

  private final VercelNativeSecretsRotationSvc vercelNativeSecretsRotationSvc;
  private final RoleSetSvc roleSetSvc;

  @Inject
  public VercelNativeAdminResource(
      VercelNativeSecretsRotationSvc vercelNativeSecretsRotationSvc, RoleSetSvc roleSetSvc) {
    this.vercelNativeSecretsRotationSvc = vercelNativeSecretsRotationSvc;
    this.roleSetSvc = roleSetSvc;
  }

  /**
   * Triggers secrets rotation for all active Vercel Native installations. This endpoint rotates
   * database passwords and updates Vercel with new connection URIs.
   */
  @POST
  @Path("/rotateAllSecrets")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.VercelNativeAdminResource.rotateAllSecrets.POST")
  public Response rotateAllSecrets(
      @Context final AuditInfo auditInfo, @Context final AppUser user) {
    LOG.info(
        "Admin triggered rotation of all Vercel Native secrets: user={}", auditInfo.getUsername());
    if (!canUserRunVercelNativeIntegrationMaintenanceTasks(user)) {
      return Response.status(Status.FORBIDDEN)
          .entity(Map.of("message", "You do not have permission to run this migration."))
          .build();
    }
    try {
      VercelNativeSecretRotationResult result =
          vercelNativeSecretsRotationSvc.rotateAllVercelNativeSecrets();

      LOG.info(
          "Successfully completed rotation of all Vercel Native secrets: user={}, result={}",
          auditInfo.getUsername(),
          result);

      return Response.ok().entity(result).build();

    } catch (Exception e) {
      LOG.error("Failed to rotate all Vercel Native secrets: user={}", auditInfo.getUsername(), e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(
              Map.of("success", false, "message", "Failed to rotate secrets: " + e.getMessage()))
          .build();
    }
  }

  /**
   * Triggers secrets rotation for specific Vercel Native installation IDs. This endpoint rotates
   * database passwords and updates Vercel with new connection URIs for the specified installations
   * only.
   */
  @POST
  @Path("/rotateSpecificSecrets")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_TSE, groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "epa.global.VercelNativeAdminResource.rotateSpecificSecrets.POST")
  public Response rotateSpecificSecrets(
      @Valid @NotNull final VercelNativeSecretRotationRequest request,
      @Context final AuditInfo auditInfo) {
    LOG.info(
        "Admin triggered rotation of Vercel Native secrets for specific installations: user={},"
            + " installationIds={}",
        auditInfo.getUsername(),
        request.installationIds());

    try {
      VercelNativeSecretRotationResult result =
          vercelNativeSecretsRotationSvc.rotateAllVercelNativeSecrets(request.installationIds());

      LOG.info(
          "Successfully completed rotation of Vercel Native secrets for specific installations:"
              + " user={}, result={}",
          auditInfo.getUsername(),
          result);

      return Response.ok().entity(result).build();

    } catch (Exception e) {
      LOG.error(
          "Failed to rotate Vercel Native secrets for installations {}: user={}",
          request.installationIds(),
          auditInfo.getUsername(),
          e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(
              Map.of("success", false, "message", "Failed to rotate secrets: " + e.getMessage()))
          .build();
    }
  }

  // == helpers ==
  /**
   * Checks if a user has permission to run Vercel Native integration maintenance tasks, there are
   * two types of users who can run these tasks:
   * <li>Users with GLOBAL_ATLAS_ADMIN role, or
   * <li>Users with email addresses in allowlistedEmails, typically these are Atlas Growth leads and
   *     Atlas Growth Platforms engineers
   */
  private boolean canUserRunVercelNativeIntegrationMaintenanceTasks(AppUser user) {
    boolean isUserAtlasTse = userHasRole(user, List.of(Role.GLOBAL_ATLAS_ADMIN.name()));
    return isUserAtlasTse || MAINTENANCE_TASK_USER_ALLOWLIST.contains(user.getPrimaryEmail());
  }

  /** Checks if a user has any of the given roles */
  private boolean userHasRole(AppUser user, List<String> authRoles) {
    return authRoles.stream()
        .map(RoleSet::valueOf)
        .anyMatch((roleSet) -> roleSetSvc.doRoleCheck(roleSet, user, null, null));
  }
}
