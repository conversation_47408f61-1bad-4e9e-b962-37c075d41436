package com.xgen.cloud.partnerintegrations.vercelnative.runtime.res.v1;

import static com.xgen.cloud.common.metrics._public.constants.MonitoringConstants.ATLAS_GROWTH_PROM_NAMESPACE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_ERROR_LABEL_NAME;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_FAILURE_VALUE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_LABEL_NAME;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_SUCCESS_VALUE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.RATE_LIMIT_POLICY_PREFIX;
import static net.logstash.logback.argument.StructuredArguments.e;

import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.PartnerApiCall;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authn._public.model.PartnerIdentityType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited.Type;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.enums.VercelNativeIntegrationErrorCode;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelAuthSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeResourcesSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel400ErrorBody;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel400ErrorCode;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel400Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel403Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel409ErrorBody;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel409ErrorCode;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.Vercel409Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.error.VercelUserErrorDetail;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResource;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.delete.DeleteResourceResult;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.get.GetResourceResult;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.provision.ProvisionResource200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.provision.ProvisionResourceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.provision.ProvisionResourceResult;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.update.UpdateResourceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.update.UpdateResourceResult;
import com.xgen.cloud.user._public.model.AppUser;
import io.prometheus.client.Counter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.LinkedHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@Path("/api/private/vercelnative/v1")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class VercelNativeResourcesResourceImpl {

  private static final Logger LOG =
      LoggerFactory.getLogger(VercelNativeResourcesResourceImpl.class);

  private static final Counter VERCEL_NATIVE_PROVISION_RESOURCE_COUNTER =
      Counter.build()
          .name("vercel_native_provision_resources_total")
          .help("Total Vercel Native Integration Provision Resource calls")
          .labelNames(
              COUNTER_STATUS_LABEL_NAME, COUNTER_ERROR_LABEL_NAME, "clusterTier", "vercelRegion")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_GET_RESOURCE_COUNTER =
      Counter.build()
          .name("vercel_native_get_resources_total")
          .help("Total Vercel Native Integration Get Resource calls")
          .labelNames(COUNTER_STATUS_LABEL_NAME, COUNTER_ERROR_LABEL_NAME)
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_DELETE_RESOURCE_COUNTER =
      Counter.build()
          .name("vercel_native_delete_resource_total")
          .help("Total Vercel Native Integration Delete Resource calls")
          .labelNames(COUNTER_STATUS_LABEL_NAME, COUNTER_ERROR_LABEL_NAME)
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_UPDATE_RESOURCE_COUNTER =
      Counter.build()
          .name("vercel_native_update_resource_total")
          .help("Total Vercel Native Integration Update Resource calls")
          .labelNames(COUNTER_STATUS_LABEL_NAME, COUNTER_ERROR_LABEL_NAME)
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private final VercelNativeResourcesSvc vercelNativeResourcesSvc;
  private final VercelAuthSvc vercelAuthSvc;

  @Inject
  public VercelNativeResourcesResourceImpl(
      VercelNativeResourcesSvc vercelNativeResourcesSvc, VercelAuthSvc vercelAuthSvc) {
    this.vercelNativeResourcesSvc = vercelNativeResourcesSvc;
    this.vercelAuthSvc = vercelAuthSvc;
  }

  @Path("/installations/{installationId}/resources")
  @POST
  @Operation(
      summary = "Provision a new Vercel Native Integration resource",
      description =
          """
Creates a new resource (MongoDB cluster, project, DB user, etc.) for the specified installation.
This endpoint handles the provisioning of database resources with the specified configuration.
For new installations with no prior resources, this will also create an org and possibly an AppUser.""",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Resource provisioned successfully.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = ProvisionResource200Response.class))),
        @ApiResponse(
            responseCode = "400",
            description = "Input validation error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel400Response.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized."),
        @ApiResponse(
            responseCode = "403",
            description = "Authentication error.",
            content = @Content(schema = @Schema(implementation = Vercel403Response.class))),
        @ApiResponse(responseCode = "404", description = "Installation not found."),
        @ApiResponse(
            responseCode = "409",
            description = "Resource state conflict error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel409Response.class))),
        @ApiResponse(responseCode = "429", description = "Too many requests."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  @PartnerApiCall(identities = {PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER})
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "vercelNative",
      types = {Type.SUSPENDABLE_IP, Type.PATH_PARAM})
  public Response provisionResource(
      @Parameter(description = "Unique identifier for the installation", required = true)
          @PathParam("installationId")
          String installationId,
      @RequestBody(
              description = "Resource provisioning configuration",
              required = true,
              content =
                  @Content(
                      mediaType = MediaType.APPLICATION_JSON,
                      schema = @Schema(implementation = ProvisionResourceRequest.class)))
          @Valid
          ProvisionResourceRequest requestBody,
      @Parameter(description = "Idempotency key for request deduplication")
          @HeaderParam("Idempotency-Key")
          String idempotencyKey,
      @Context HttpServletRequest requestHeader,
      @Context AppUser appUser,
      @Context AuditInfo auditInfo)
      throws SvcException {
    ProvisionResourceResult result;
    final String requestedClusterTier = requestBody.metadata().clusterTier().name();
    final String requestedVercelRegion = requestBody.metadata().vercelRegion().name();

    LinkedHashMap<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("idempotencyKey", idempotencyKey);
    logContext.put("requestBody", requestBody);

    try {
      vercelAuthSvc.requireVercelAdmin(requestHeader, installationId);
    } catch (SvcException e) {
      LOG.warn("Failed to authorize Vercel Native Installation request {}", e(logContext), e);
      VERCEL_NATIVE_PROVISION_RESOURCE_COUNTER
          .labels(
              COUNTER_STATUS_FAILURE_VALUE,
              VercelNativeIntegrationErrorCode.VERCEL_NATIVE_AUTH_ERROR.name(),
              requestedClusterTier,
              requestedVercelRegion)
          .inc();
      return Response.status(403).build();
    }

    try {
      result =
          vercelNativeResourcesSvc.provisionResource(
              installationId, requestBody, idempotencyKey, requestHeader, appUser, auditInfo);
    } catch (Exception e) {
      LOG.error("Error provisioning Vercel Native resource: {}", e(logContext), e);

      VERCEL_NATIVE_PROVISION_RESOURCE_COUNTER
          .labels(
              COUNTER_STATUS_FAILURE_VALUE,
              VercelNativeIntegrationErrorCode.VERCEL_NATIVE_GENERAL_ERROR.name(),
              requestedClusterTier,
              requestedVercelRegion)
          .inc();

      return Response.status(500).build();
    }

    if (result.success()) {
      VERCEL_NATIVE_PROVISION_RESOURCE_COUNTER
          .labels(
              COUNTER_STATUS_SUCCESS_VALUE,
              COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE,
              requestedClusterTier,
              requestedVercelRegion)
          .inc();

      return Response.ok(result.provisionedResource()).build();
    }

    logContext.put("errorCode", result.errorCode());
    logContext.put("errorMessage", result.errorMessage());
    LOG.warn(
        "Failed to provision Vercel Native resource due to known error code: {}", e(logContext));

    VERCEL_NATIVE_PROVISION_RESOURCE_COUNTER
        .labels(
            COUNTER_STATUS_FAILURE_VALUE,
            result.errorCode().name(),
            requestedClusterTier,
            requestedVercelRegion)
        .inc();

    return switch (result.errorCode()) {
      case INSTALLATION_DELETED, RESOURCE_LIMIT_REACHED ->
          Response.status(409)
              .entity(
                  Vercel409Response.of(
                      Vercel409ErrorBody.builder()
                          .code(Vercel409ErrorCode.CONFLICT)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
      case INSTALLATION_NOT_FOUND -> Response.status(404).build();
      case RESOURCE_CREATION_DISABLED -> Response.status(500).build();
      case UNKNOWN -> Response.status(500).build();
      default ->
          Response.status(400)
              .entity(
                  Vercel400Response.of(
                      Vercel400ErrorBody.builder()
                          .code(Vercel400ErrorCode.VALIDATION_ERROR)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
    };
  }

  @GET
  @Path("/installations/{installationId}/resources/{resourceId}")
  @Operation(
      summary = "Get a Vercel Native Integration resource",
      description = "Retrieves the current configuration and status for a specific resource.",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Resource details retrieved successfully.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = VercelResource.class))),
        @ApiResponse(
            responseCode = "400",
            description = "Input validation error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel400Response.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized."),
        @ApiResponse(
            responseCode = "403",
            description = "Authentication error.",
            content = @Content(schema = @Schema(implementation = Vercel403Response.class))),
        @ApiResponse(responseCode = "404", description = "Installation or resource not found."),
        @ApiResponse(
            responseCode = "409",
            description = "Resource state conflict error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel409Response.class))),
        @ApiResponse(responseCode = "429", description = "Too many requests."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  @PartnerApiCall(identities = {PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER})
  @RolesAllowed(NAME.ORG_READ_ONLY)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "vercelNativeGet",
      types = {Type.SUSPENDABLE_IP, Type.PATH_PARAM})
  @Auth(endpointAction = "epa.organization.VercelNativeResourcesResourceImpl.getResource.GET")
  public Response getResource(
      @Parameter(description = "Unique identifier for the installation", required = true)
          @PathParam("installationId")
          String installationId,
      @Parameter(description = "Unique identifier for the resource", required = true)
          @PathParam("resourceId")
          String resourceId) {

    GetResourceResult result;
    LinkedHashMap<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("resourceId", resourceId);

    try {
      result = vercelNativeResourcesSvc.getResource(installationId, resourceId);
    } catch (Exception e) {
      LOG.error("Error getting Vercel Native Integration resource: {}", e(logContext), e);
      VERCEL_NATIVE_GET_RESOURCE_COUNTER
          .labels(
              COUNTER_STATUS_FAILURE_VALUE,
              VercelNativeIntegrationErrorCode.VERCEL_NATIVE_GENERAL_ERROR.name())
          .inc();
      return Response.status(500).build();
    }

    if (result.success()) {
      VERCEL_NATIVE_GET_RESOURCE_COUNTER
          .labels(COUNTER_STATUS_SUCCESS_VALUE, COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE)
          .inc();
      return Response.ok(result.resource()).build();
    }

    logContext.put("errorCode", result.errorCode());
    logContext.put("errorMessage", result.errorMessage());
    LOG.warn(
        "Failed to get Vercel Native Integration resource due to known error code: {}",
        e(logContext));

    VERCEL_NATIVE_GET_RESOURCE_COUNTER
        .labels(COUNTER_STATUS_FAILURE_VALUE, result.errorCode().name())
        .inc();

    return switch (result.errorCode()) {
      case INSTALLATION_DELETED ->
          Response.status(409)
              .entity(
                  Vercel409Response.of(
                      Vercel409ErrorBody.builder()
                          .code(Vercel409ErrorCode.CONFLICT)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
      case INSTALLATION_NOT_FOUND, RESOURCE_NOT_FOUND -> Response.status(404).build();
      default ->
          Response.status(400)
              .entity(
                  Vercel400Response.of(
                      Vercel400ErrorBody.builder()
                          .code(Vercel400ErrorCode.VALIDATION_ERROR)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
    };
  }

  @PATCH
  @Path("/installations/{installationId}/resources/{resourceId}")
  @Operation(
      summary = "Update a Vercel Native Integration resource",
      description =
          """
          Updates the configuration of an existing resource such as billing plan,
          user metadata, or other resource-specific settings.""",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Resource updated successfully.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = VercelResource.class))),
        @ApiResponse(
            responseCode = "400",
            description = "Input validation error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel400Response.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized."),
        @ApiResponse(
            responseCode = "403",
            description = "Authentication error.",
            content = @Content(schema = @Schema(implementation = Vercel403Response.class))),
        @ApiResponse(responseCode = "404", description = "Installation or resource not found."),
        @ApiResponse(
            responseCode = "409",
            description = "Resource state conflict error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel409Response.class))),
        @ApiResponse(responseCode = "429", description = "Too many requests."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  @PartnerApiCall(identities = {PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER})
  @RolesAllowed(NAME.ORG_OWNER)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "vercelNative",
      types = {Type.SUSPENDABLE_IP, Type.PATH_PARAM})
  @Auth(endpointAction = "epa.organization.VercelNativeResourcesResourceImpl.updateResource.PATCH")
  public Response updateResource(
      @Parameter(description = "Unique identifier for the installation", required = true)
          @PathParam("installationId")
          String installationId,
      @Parameter(description = "Unique identifier for the resource", required = true)
          @PathParam("resourceId")
          String resourceId,
      @RequestBody(
              description = "Resource update configuration",
              required = true,
              content =
                  @Content(
                      mediaType = MediaType.APPLICATION_JSON,
                      schema = @Schema(implementation = UpdateResourceRequest.class)))
          UpdateResourceRequest requestBody,
      @Context HttpServletRequest requestHeader,
      @Context AppUser appUser) {

    UpdateResourceResult result;

    LinkedHashMap<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("resourceId", resourceId);
    logContext.put("requestBody", requestBody);

    try {
      result =
          vercelNativeResourcesSvc.updateResource(
              installationId, resourceId, requestBody, requestHeader, appUser);
    } catch (Exception e) {
      LOG.error("Error updating Vercel Native resource: {}", e(logContext), e);
      VERCEL_NATIVE_UPDATE_RESOURCE_COUNTER
          .labels(
              COUNTER_STATUS_FAILURE_VALUE,
              VercelNativeIntegrationErrorCode.VERCEL_NATIVE_GENERAL_ERROR.name())
          .inc();
      return Response.status(500).build();
    }

    if (result.success()) {
      VERCEL_NATIVE_UPDATE_RESOURCE_COUNTER
          .labels(COUNTER_STATUS_SUCCESS_VALUE, COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE)
          .inc();
      return Response.ok(result.resource()).build();
    }

    logContext.put("errorCode", result.errorCode());
    logContext.put("errorMessage", result.errorMessage());
    LOG.warn(
        "Failed to update Vercel Native Integration resource due to known error code: {}",
        e(logContext));

    VERCEL_NATIVE_UPDATE_RESOURCE_COUNTER
        .labels(COUNTER_STATUS_FAILURE_VALUE, result.errorCode().name())
        .inc();

    return switch (result.errorCode()) {
      case INSTALLATION_DELETED, PAID_TO_FREE_DOWNGRADE_NOT_ALLOWED, RESOURCE_DELETED ->
          Response.status(409)
              .entity(
                  Vercel409Response.of(
                      Vercel409ErrorBody.builder()
                          .code(Vercel409ErrorCode.CONFLICT)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
      case INSTALLATION_NOT_FOUND, RESOURCE_NOT_FOUND -> Response.status(404).build();
      case UNKNOWN -> Response.status(500).build();
      default ->
          Response.status(400)
              .entity(
                  Vercel400Response.of(
                      Vercel400ErrorBody.builder()
                          .code(Vercel400ErrorCode.VALIDATION_ERROR)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
    };
  }

  @DELETE
  @Path("/installations/{installationId}/resources/{resourceId}")
  @Operation(
      summary = "Delete a Vercel Native Integration resource",
      description =
          """
          Deletes an existing resource and cleans up associated infrastructure.
          This operation will permanently remove the resource and its data.""",
      responses = {
        @ApiResponse(responseCode = "204", description = "Resource deleted successfully."),
        @ApiResponse(
            responseCode = "400",
            description = "Input validation error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel400Response.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized."),
        @ApiResponse(
            responseCode = "403",
            description = "Authentication error.",
            content = @Content(schema = @Schema(implementation = Vercel403Response.class))),
        @ApiResponse(responseCode = "404", description = "Installation or resource not found."),
        @ApiResponse(
            responseCode = "409",
            description = "Resource state conflict error.",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Vercel409Response.class))),
        @ApiResponse(responseCode = "429", description = "Too many requests."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  @PartnerApiCall(
      identities = {
        PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
        PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER
      })
  @RolesAllowed(NAME.ORG_OWNER)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "vercelNative",
      types = {Type.SUSPENDABLE_IP, Type.PATH_PARAM})
  @Auth(endpointAction = "epa.organization.VercelNativeResourcesResourceImpl.deleteResource.DELETE")
  public Response deleteResource(
      @Parameter(description = "Unique identifier for the installation", required = true)
          @PathParam("installationId")
          String installationId,
      @Parameter(description = "Unique identifier for the resource", required = true)
          @PathParam("resourceId")
          String resourceId,
      @Context HttpServletRequest requestHeader,
      @Context AppUser appUser) {

    LinkedHashMap<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("resourceId", resourceId);
    if (appUser != null) {
      logContext.put("appUserId", appUser.getId());
    }

    DeleteResourceResult result;
    try {
      result =
          vercelNativeResourcesSvc.deleteResource(
              installationId, resourceId, appUser, requestHeader, logContext);
    } catch (Exception e) {
      LOG.error("Unexpected error deleting Vercel Native resource: {}", e(logContext), e);
      VERCEL_NATIVE_DELETE_RESOURCE_COUNTER
          .labels(
              COUNTER_STATUS_FAILURE_VALUE,
              VercelNativeIntegrationErrorCode.VERCEL_NATIVE_GENERAL_ERROR.name())
          .inc();
      return Response.status(500).build();
    }

    if (result.success()) {
      VERCEL_NATIVE_DELETE_RESOURCE_COUNTER
          .labels(COUNTER_STATUS_SUCCESS_VALUE, COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE)
          .inc();
      return Response.noContent().build();
    }

    logContext.put("errorCode", result.errorCode());
    logContext.put("errorMessage", result.errorMessage());
    LOG.warn("Failed to delete Vercel Native resource due to known error code: {}", e(logContext));

    VERCEL_NATIVE_DELETE_RESOURCE_COUNTER
        .labels(COUNTER_STATUS_FAILURE_VALUE, result.errorCode().name())
        .inc();

    return switch (result.errorCode()) {
      case INSTALLATION_DELETED ->
          Response.status(409)
              .entity(
                  Vercel409Response.of(
                      Vercel409ErrorBody.builder()
                          .code(Vercel409ErrorCode.CONFLICT)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
      case INSTALLATION_NOT_FOUND, RESOURCE_NOT_FOUND -> Response.status(404).build();
      default ->
          Response.status(400)
              .entity(
                  Vercel400Response.of(
                      Vercel400ErrorBody.builder()
                          .code(Vercel400ErrorCode.VALIDATION_ERROR)
                          .message(result.errorMessage())
                          .user(new VercelUserErrorDetail(result.errorMessage(), null))
                          .build()))
              .build();
    };
  }
}
