package com.xgen.cloud.partnerintegrations.vercelnative._public.model;

import static java.util.Objects.requireNonNullElse;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

public record AtlasResources(
    @JsonProperty(ORGANIZATION_ID) @BsonProperty(ORGANIZATION_ID) @NotNull ObjectId organizationId,
    @JsonProperty(INSTALLED_PRODUCTS) @BsonProperty(INSTALLED_PRODUCTS) @NotNull
        List<InstalledProduct> installedProducts) {

  public static final String ORGANIZATION_ID = "organizationId";
  public static final String INSTALLED_PRODUCTS = "installedProducts";

  public AtlasResources {
    //noinspection DataFlowIssue
    installedProducts = requireNonNullElse(installedProducts, List.of());
  }

  public static Builder builder() {
    return new Builder();
  }

  public Builder toBuilder() {
    return new Builder(this);
  }

  public static final class Builder {
    private ObjectId organizationId;
    private List<InstalledProduct> installedProducts;

    private Builder() {}

    private Builder(AtlasResources other) {
      this.organizationId = other.organizationId;
      this.installedProducts = other.installedProducts;
    }

    public Builder organizationId(ObjectId organizationId) {
      this.organizationId = organizationId;
      return this;
    }

    public Builder installedProducts(List<InstalledProduct> installedProducts) {
      this.installedProducts = installedProducts;
      return this;
    }

    public AtlasResources build() {
      return new AtlasResources(organizationId, installedProducts);
    }
  }
}
