package com.xgen.cloud.sandbox.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.groupcreation._public.svc.GroupCreationSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;

@Path("/sandbox")
@Singleton
public class SandboxResource extends BaseResource {

  private final GroupCreationSvc groupCreationSvc;
  private final AuditInfoSvc auditInfoSvc;

  @Inject
  public SandboxResource(final GroupCreationSvc groupCreationSvc, final AuditInfoSvc auditInfoSvc) {
    this.groupCreationSvc = groupCreationSvc;
    this.auditInfoSvc = auditInfoSvc;
  }

  @POST
  @Path("/{orgId}/createSandboxProject")
  @UiCall(roles = RoleSet.ORG_GROUP_CREATOR, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SandboxResource.createSandboxProject.POST")
  public Response createSandboxProject(
      @Context HttpServletRequest request,
      @Context AppUser user,
      @Context Organization organization,
      @FormParam("groupName") String newGroupName)
      throws SvcException {

    ObjectId newGroupId = ObjectId.get();
    groupCreationSvc.addAnotherGroup(
        newGroupId,
        organization,
        user.getId(),
        newGroupName,
        auditInfoSvc.fromUiCall(user, request.getRemoteAddr(), request.getRemoteAddr()));
    return SimpleApiResponse.ok()
        .resource(request.getRequestURI())
        .newObjId(newGroupId.toString())
        .build();
  }
}
