load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/apiusagedata",
        "//server/src/main/com/xgen/cloud/authz/resource",
        "//server/src/main/com/xgen/cloud/billingplatform/model/plan",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/jackson",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/requestparams",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/cloud/common/versioning",
        "//server/src/main/com/xgen/cloud/common/view",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/flex/runtime/svc",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/project/_public/model",
        "//server/src/main/com/xgen/cloud/nds/resourcepolicy",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/sls",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/svc/mms/api/res/atlas",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "@maven//:com_fasterxml_jackson_core_jackson_core",
        "@maven//:com_fasterxml_jackson_core_jackson_databind",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_json_json",
        "@maven//:org_mongodb_bson",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
