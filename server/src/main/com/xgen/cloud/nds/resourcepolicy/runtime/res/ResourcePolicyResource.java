package com.xgen.cloud.nds.resourcepolicy.runtime.res;

import static com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type.RESOURCE_POLICY_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type.RESOURCE_POLICY_MODIFIED;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.nds.resourcepolicy._private.atlas.ui.view.mapper.ResourcePolicyCreateViewMapper;
import com.xgen.cloud.nds.resourcepolicy._private.atlas.ui.view.mapper.ResourcePolicyEditViewMapper;
import com.xgen.cloud.nds.resourcepolicy._public.atlas.ui.view.InvalidResourcePolicyErrorView;
import com.xgen.cloud.nds.resourcepolicy._public.atlas.ui.view.ResourcePolicyCreateView;
import com.xgen.cloud.nds.resourcepolicy._public.atlas.ui.view.ResourcePolicyEditView;
import com.xgen.cloud.nds.resourcepolicy._public.atlas.ui.view.ResourcePolicyView;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicy;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicyValidationError;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicyValidationError.ValidationErrorType;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.library.control.Result;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;

@Path("/orgs/{orgId}/resourcePolicies")
@Singleton
public class ResourcePolicyResource {

  private final AtlasResourcePolicySvc _atlasResourcePolicySvc;
  private final AuditSvc _auditSvc;

  @Inject
  public ResourcePolicyResource(
      final AtlasResourcePolicySvc pAtlasResourcePolicySvc, final AuditSvc pAuditSvc) {
    _atlasResourcePolicySvc = pAtlasResourcePolicySvc;
    _auditSvc = pAuditSvc;
  }

  @GET
  @Feature(FeatureFlag.ATLAS_RESOURCE_POLICIES)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.ResourcePolicyResource.getResourcePolicies.GET")
  public Response getResourcePolicies(@PathParam("orgId") final ObjectId pOrgId) {
    final List<ResourcePolicyView> resourcePolicyViewList =
        _atlasResourcePolicySvc.getResourcePolicies(pOrgId).stream()
            .map(ResourcePolicyView::new)
            .toList();

    return Response.ok(resourcePolicyViewList).build();
  }

  @GET
  @Path(("/{resourcePolicyId}"))
  @Feature(FeatureFlag.ATLAS_RESOURCE_POLICIES)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.ResourcePolicyResource.getResourcePolicy.GET")
  public Response getResourcePolicy(
      @PathParam("orgId") final ObjectId pOrgId,
      @PathParam("resourcePolicyId") final ObjectId pResourcePolicyId) {
    final Optional<ResourcePolicyView> resourcePolicyView =
        _atlasResourcePolicySvc
            .getResourcePolicy(pOrgId, pResourcePolicyId)
            .map(ResourcePolicyView::new);

    return resourcePolicyView.isPresent()
        ? Response.ok(resourcePolicyView.get()).build()
        : Response.status(Response.Status.NOT_FOUND).build();
  }

  @POST
  @Feature(FeatureFlag.ATLAS_RESOURCE_POLICIES)
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.ResourcePolicyResource.createResourcePolicy.POST")
  public Response createResourcePolicy(
      @PathParam("orgId") final ObjectId pOrgId,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      final ResourcePolicyCreateView pResourcePolicyCreateView) {
    final ResourcePolicy resourcePolicy =
        ResourcePolicyCreateViewMapper.toResourcePolicy(
            pAppUser, pOrgId, pResourcePolicyCreateView);
    final Result<ResourcePolicy, ResourcePolicyValidationError> policyValidationResult =
        _atlasResourcePolicySvc.saveResourcePolicy(
            resourcePolicy, RESOURCE_POLICY_CREATED, pAuditInfo, null);

    if (policyValidationResult.isOk()) {
      return Response.ok(new ResourcePolicyView(resourcePolicy)).build();
    }

    return validationErrorToResponse(policyValidationResult.unwrapErr());
  }

  @PATCH
  @Path("/{resourcePolicyId}")
  @Feature(FeatureFlag.ATLAS_RESOURCE_POLICIES)
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.ResourcePolicyResource.editResourcePolicy.PATCH")
  public Response editResourcePolicy(
      @PathParam("orgId") final ObjectId pOrgId,
      @PathParam("resourcePolicyId") final ObjectId pResourcePolicyId,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      final ResourcePolicyEditView pResourcePolicyEditView) {
    final Optional<ResourcePolicy> existingResourcePolicy =
        _atlasResourcePolicySvc.getResourcePolicy(pOrgId, pResourcePolicyId);

    // Prevent modification of Azure Native integration Resource Policy
    if (existingResourcePolicy.isPresent()
        && _atlasResourcePolicySvc.shouldPreventResourcePolicyModification(
            existingResourcePolicy.get())) {
      return Response.status(Response.Status.FORBIDDEN).build();
    }

    if (existingResourcePolicy.isEmpty()
        || !existingResourcePolicy.get().getOrgId().equals(pOrgId)) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    final ResourcePolicy newResourcePolicy =
        ResourcePolicyEditViewMapper.toUpdatedResourcePolicy(
            pAppUser, existingResourcePolicy.get(), pResourcePolicyEditView);
    final Result<ResourcePolicy, ResourcePolicyValidationError> policyValidationResult =
        _atlasResourcePolicySvc.saveResourcePolicy(
            newResourcePolicy, RESOURCE_POLICY_MODIFIED, pAuditInfo, existingResourcePolicy.get());

    if (policyValidationResult.isOk()) {
      return Response.ok(new ResourcePolicyView(newResourcePolicy)).build();
    }

    return validationErrorToResponse(policyValidationResult.unwrapErr());
  }

  @DELETE
  @Path("/{resourcePolicyId}")
  @Feature(FeatureFlag.ATLAS_RESOURCE_POLICIES)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.ResourcePolicyResource.deleteResourcePolicy.DELETE")
  public Response deleteResourcePolicy(
      @PathParam("orgId") final ObjectId pOrgId,
      @PathParam("resourcePolicyId") final ObjectId pResourcePolicyId,
      @Context final AuditInfo pAuditInfo) {
    final Optional<ResourcePolicy> existingResourcePolicy =
        _atlasResourcePolicySvc.getResourcePolicy(pOrgId, pResourcePolicyId);

    // Prevent deletion of Azure Native integration Resource Policy
    if (existingResourcePolicy.isPresent()
        && _atlasResourcePolicySvc.shouldPreventResourcePolicyModification(
            existingResourcePolicy.get())) {
      return Response.status(Response.Status.FORBIDDEN).build();
    }

    if (existingResourcePolicy.isEmpty()
        || !existingResourcePolicy.get().getOrgId().equals(pOrgId)
        || !_atlasResourcePolicySvc.deleteResourcePolicy(
            existingResourcePolicy.get(), pAuditInfo)) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    return Response.ok().build();
  }

  private static Response validationErrorToResponse(final ResourcePolicyValidationError pError) {
    final Response.Status errorCode;
    if (pError.errorType() == ValidationErrorType.CEDAR_INTERNAL_ERROR) {
      errorCode = Response.Status.INTERNAL_SERVER_ERROR;
    } else {
      errorCode = Response.Status.BAD_REQUEST;
    }
    return Response.status(errorCode).entity(new InvalidResourcePolicyErrorView(pError)).build();
  }
}
