load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "res",
    srcs = glob(["**/*.java"]),
    allow_common_to_depend_on_non_common = True,
    visibility = [
        "//server/src/main/com/xgen/svc/mms/api/generator:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep: gazelle cannot resolve this dep in all cases, since it uses filegroups.
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/common/access",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/messagebus/_private/model",
        "//server/src/main/com/xgen/cloud/common/messagebus/_private/svc",
        "//server/src/main/com/xgen/cloud/common/messagebus/_public/model",
        "//server/src/main/com/xgen/cloud/common/res",
        "//server/src/main/com/xgen/svc/mms/api/res/common",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
