package com.xgen.cloud.externalanalytics.runtime.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.externalanalytics._public.model.SegmentTraits;
import com.xgen.cloud.externalanalytics._public.svc.SegmentProfileSvc;
import com.xgen.cloud.organization._public.model.Organization;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;

@Path("/segmentTraits")
@Singleton
public class SegmentProfileResource extends BaseResource {
  private final AppSettings _appSettings;
  private final SegmentProfileSvc _segmentProfileSvc;

  @Inject
  public SegmentProfileResource(
      final AppSettings appSettings, final SegmentProfileSvc segmentProfileSvc) {
    _appSettings = appSettings;
    _segmentProfileSvc = segmentProfileSvc;
  }

  @GET
  @Path("/{orgId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  @Auth(endpointAction = "epa.organization.SegmentProfileResource.getOrganizationTraits.GET")
  public Response getOrganizationTraits(@Context final Organization organization) {
    if (_appSettings.isAnalyticsEnabled()) {
      final Optional<SegmentTraits> traits =
          _segmentProfileSvc.getOrganizationTraits(organization.getId());
      if (traits.isPresent()) {
        return Response.ok().entity(traits.get()).build();
      }
    }
    return Response.noContent().build();
  }
}
