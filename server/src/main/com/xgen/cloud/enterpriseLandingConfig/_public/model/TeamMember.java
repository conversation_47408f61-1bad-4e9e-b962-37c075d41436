package com.xgen.cloud.enterpriseLandingConfig._public.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nullable;
import java.util.Objects;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonProperty;

public class TeamMember {
  public static final String NAME = "name";
  public static final String JOB_TITLE = "jobTitle";
  public static final String DESCRIPTION = "description";
  public static final String EMAIL = "email";
  public static final String CHAT_LINK = "chatLink";

  @JsonProperty(NAME)
  @BsonProperty(NAME)
  private final String name;

  @JsonProperty(JOB_TITLE)
  @BsonProperty(JOB_TITLE)
  private final String jobTitle;

  @JsonProperty(DESCRIPTION)
  @BsonProperty(DESCRIPTION)
  private final String description;

  @Nullable
  @JsonProperty(EMAIL)
  @BsonProperty(EMAIL)
  private final String email;

  @Nullable
  @JsonProperty(CHAT_LINK)
  @BsonProperty(CHAT_LINK)
  private final String chatLink;

  @BsonCreator
  public TeamMember(
      @BsonProperty(NAME) String name,
      @BsonProperty(JOB_TITLE) String jobTitle,
      @BsonProperty(DESCRIPTION) String description,
      @BsonProperty(EMAIL) @Nullable String email,
      @BsonProperty(CHAT_LINK) @Nullable String chatLink) {
    this.name = name;
    this.jobTitle = jobTitle;
    this.description = description;
    this.email = email;
    this.chatLink = chatLink;
  }

  public String getName() {
    return name;
  }

  public String getJobTitle() {
    return jobTitle;
  }

  public String getDescription() {
    return description;
  }

  @Nullable
  public String getEmail() {
    return email;
  }

  @Nullable
  public String getChatLink() {
    return chatLink;
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .append("name", name)
        .append("jobTitle", jobTitle)
        .append("description", description)
        .append("email", email)
        .append("chatLink", chatLink)
        .toString();
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;

    if (!(obj instanceof TeamMember that)) return false;

    return Objects.equals(name, that.name)
        && Objects.equals(jobTitle, that.jobTitle)
        && Objects.equals(description, that.description)
        && Objects.equals(email, that.email)
        && Objects.equals(chatLink, that.chatLink);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, jobTitle, description, email, chatLink);
  }
}
