package com.xgen.devtools.bugchecker.planlogger;

import static com.google.errorprone.matchers.Matchers.staticMethod;
import static com.google.errorprone.predicates.TypePredicates.isDescendantOf;

import com.google.auto.service.AutoService;
import com.google.errorprone.BugPattern;
import com.google.errorprone.BugPattern.SeverityLevel;
import com.google.errorprone.VisitorState;
import com.google.errorprone.bugpatterns.BugChecker;
import com.google.errorprone.bugpatterns.BugChecker.MethodInvocationTreeMatcher;
import com.google.errorprone.fixes.SuggestedFix;
import com.google.errorprone.matchers.Description;
import com.google.errorprone.matchers.Matcher;
import com.google.errorprone.predicates.TypePredicate;
import com.google.errorprone.util.ASTHelpers;
import com.sun.source.tree.ExpressionTree;
import com.sun.source.tree.MethodInvocationTree;
import com.sun.tools.javac.code.Symbol;
import java.util.Optional;
import java.util.Set;

@AutoService(BugChecker.class)
@BugPattern(
    summary =
        "PlanLogger.resourceEntry() should not be passed values with known serialization issues."
            + " Serialize to String (or other simpler type) beforehand.",
    explanation =
        "PlanLogger structured logging uses slf4j which can attempt serialisation strategies that"
            + " internally deadlock. Serialize to String (or other simpler type) beforehand.",
    severity = SeverityLevel.ERROR)
public class ResourceEntryCloudProviderValues extends BugChecker
    implements MethodInvocationTreeMatcher {

  public static final Matcher<ExpressionTree> PLAN_LOG_RESOURCE_ENTRY =
      staticMethod()
          .onClass("com.xgen.module.common.planner.model.PlanLogResourceEntry")
          .named("resourceEntry");

  public static final TypePredicate ENUM = isDescendantOf("java.lang.Enum");

  public static final Set<String> BAD_SERIALIZATION_PACKAGES =
      Set.of("com.amazonaws", "com.azure.resourcemanager", "com.google.api");

  @Override
  public Description matchMethodInvocation(MethodInvocationTree tree, VisitorState state) {
    if (PLAN_LOG_RESOURCE_ENTRY.matches(tree, state) && tree.getArguments().size() > 1) {
      final ExpressionTree argument = tree.getArguments().get(1);

      Optional<Symbol> symbol = Optional.ofNullable(ASTHelpers.getSymbol(argument));

      final Optional<String> packageName =
          symbol.map(s -> s.type.tsym.getQualifiedName().toString());

      // When we can identify a package, and the value is from that package, and that value is not
      // an enum, error.
      if (packageName.isPresent()
          && BAD_SERIALIZATION_PACKAGES.stream().anyMatch(p -> packageName.get().startsWith(p))
          && !ENUM.apply(symbol.get().type, state)) {

        return buildDescription(tree)
            .setMessage(
                "Forcing the logger to serialize this value might result in a deadlock. Serialize"
                    + " it before logging.")
            .addFix(
                SuggestedFix.builder()
                    .addImport("java.util.Objects")
                    .prefixWith(argument, "Objects.toString(")
                    .postfixWith(argument, ")")
                    .build())
            .build();
      }
    }

    return Description.NO_MATCH;
  }
}
