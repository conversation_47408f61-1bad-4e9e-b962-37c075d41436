package com.xgen.devtools.configservicesdk;

import static com.xgen.devtools.configservicesdk.constants.ConfigServiceSdkConstants.CONFIG_KEY_DELIMITER;

import com.google.protobuf.Any;
import com.xgen.cloud.services.config.proto.ApplicationPropertyConfigData;
import com.xgen.cloud.services.config.proto.ApplicationPropertyConfigMetadata;
import com.xgen.cloud.services.config.proto.ApplicationPropertySource;
import com.xgen.cloud.services.config.proto.Config;
import com.xgen.cloud.services.config.proto.ConfigModule;
import com.xgen.devtools.configservicesdk.constants.ConfigServiceSdkErrors.NonHexadecimalStringHashAttemptException;
import com.xgen.devtools.configservicesdk.constants.ConfigServiceSdkErrors.RolloutPercentageOutOfBoundsException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.zip.CRC32;

public class ConfigServiceUtils {
  // These were brought over from MathUtils and CodecUtils to avoid directly depending on either
  private static final long MAX_HASH_VALUE = (long) (Math.pow(2, 32) - 1);
  private static final String HEXADECIMAL_STRING_REGEX_PATTERN = "^[0-9A-Fa-f]+$";

  private static final ThreadLocal<CRC32> _crc32Hasher =
      new ThreadLocal<>() {
        @Override
        public CRC32 initialValue() {
          try {
            return new CRC32();
          } catch (final Throwable t) {
            throw new IllegalStateException(t);
          }
        }

        @Override
        public CRC32 get() {
          final CRC32 d = super.get();
          d.reset();
          return d;
        }
      };

  private static long crc32(final String pV) {
    return crc32(pV.getBytes(StandardCharsets.UTF_8));
  }

  private static long crc32(final byte[] pV) {
    final CRC32 hasher = _crc32Hasher.get();

    try {
      hasher.update(pV);
    } catch (final Exception e) {
      throw new IllegalArgumentException(e);
    }

    return hasher.getValue();
  }

  /**
   * Method to determine if the crc32 hashed ObjectId+FeatureFlagId falls within a desired
   * percentage. Useful for sampling a percentage of ObjectIds for a particular feature flag. The
   * ObjectId is hashed with the FeatureFlagId to ensure that the rollout percentage evaluation is
   * scoped within each feature flag.
   */
  public static boolean isHashedIdWithinPercentageForFeatureFlag(
      String objectIdHexString,
      double percentageAllowed,
      String featureFlagName,
      String featureFlagId)
      throws RolloutPercentageOutOfBoundsException, NonHexadecimalStringHashAttemptException {
    if (percentageAllowed < 0 || percentageAllowed > 100) {
      throw new RolloutPercentageOutOfBoundsException(
          "The Rollout Percentage of Feature Flags must be 0 and 100. Feature Flag "
              + featureFlagName
              + " with Id "
              + featureFlagId
              + " has a Rollout Percentage of "
              + percentageAllowed);
    }
    if (!objectIdHexString.matches(HEXADECIMAL_STRING_REGEX_PATTERN)) {
      throw new NonHexadecimalStringHashAttemptException(
          "Only hexadecimal strings can be evaluated by the gradual rollout algorithm. Feature Flag"
              + featureFlagName
              + " with Id "
              + featureFlagId
              + "was attempted to be evaluated against the string "
              + objectIdHexString);
    }
    final long hashedValue = crc32(objectIdHexString + featureFlagId);
    return hashedValue <= (percentageAllowed / 100.0) * MAX_HASH_VALUE;
  }

  /**
   * Creates a config module key by combining the provided namespace and config item name. The
   * resulting key is formed by concatenating the namespace, a delimiter (":"), and the config item
   * name.
   *
   * @param namespace The namespace for the configuration.
   * @param configItemName The name of the configuration item.
   * @return A string representing the key.
   */
  public static String createConfigKey(String namespace, String configItemName) {
    return namespace + CONFIG_KEY_DELIMITER + configItemName;
  }

  public static Config createApplicationPropertyConfig(
      String value, ApplicationPropertySource source) {
    final ApplicationPropertyConfigData applicationPropertyConfigData =
        ApplicationPropertyConfigData.newBuilder().setValue(value).build();
    final ApplicationPropertyConfigMetadata applicationPropertyConfigMetadata =
        ApplicationPropertyConfigMetadata.newBuilder().setSource(source).build();

    return Config.newBuilder()
        .setModule(ConfigModule.CONFIG_MODULE_APPLICATION_PROPERTY)
        .setData(Any.pack(applicationPropertyConfigData))
        .setMetadata(Any.pack(applicationPropertyConfigMetadata))
        .build();
  }

  public static int getTimeElapsedSecFromStartTime(Instant startTime) {
    return (int) Math.ceil((double) (Duration.between(startTime, Instant.now()).toMillis()) / 1000);
  }
}
